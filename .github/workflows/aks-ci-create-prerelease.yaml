name: Create Pre-Release
permissions:
  id-token: write
  contents: write
on:
  schedule:
  - cron: '0 14 * * 1,2,3,4,5'
  workflow_dispatch:
jobs:
  vars: # Job to set variables
    runs-on: ["self-hosted", arc-dind-rootless-enterprise]
    steps:
      - name: Get Release Version
        id: get-latest-stage-version
        run: |
          sitewide_release_version=$(curl -k "https://sitewide-next.aks.stage.azeus.gaptech.com/healthz" | jq -r '.version')
          buy_release_version=$(curl -k "https://buy-next.aks.stage.azeus.gaptech.com/healthz" | jq -r '.version')
          category_release_version=$(curl -k "https://category-next.aks.stage.azeus.gaptech.com/healthz" | jq -r '.version')
          product_release_version=$(curl -k "https://product-next.aks.stage.azeus.gaptech.com/healthz" | jq -r '.version')
          if [[ "${sitewide_release_version}" == "${buy_release_version}" && "${sitewide_release_version}" == "${category_release_version}" && "${sitewide_release_version}" == "${product_release_version}" ]]
          then
            echo "Versions are in sync - Releasing Version: ${sitewide_release_version}"
            release_version="${sitewide_release_version}"
            echo "release_version=${release_version}">> $GITHUB_OUTPUT
          else
            echo "Versions are not in sync"
            echo "sitewide_release_version=${sitewide_release_version}"
            echo "buy_next_release_version=${buy_release_version}"
            echo "category_next_release_version=${category_release_version}"
            echo "product_next_release_version=${product_release_version}"
            exit 1
          fi
    outputs:
      release-version: ${{ steps.get-latest-stage-version.outputs.release_version }}
  create-prerelease:
    needs:
      - vars
    runs-on: [self-hosted]
    steps:
    - name: Create Release
      id: release
      uses: softprops/action-gh-release@9d7c94cfd0a1f3ed45544c887983e9fa900f0564
      env:
        GITHUB_TOKEN: ${{ secrets.BRODPS_GHP_TOKEN }}
      with:
        tag_name: ${{ needs.vars.outputs.release-version }}
        name: ${{ needs.vars.outputs.release-version }}
        prerelease: true
        generate_release_notes: true
    - uses: octokit/request-action@v2.3.1
      id: get_latest_release
      with:
        route: GET ${{ github.server_url }}/api/v3/repos/${{ github.repository }}/releases/tags/${{ needs.vars.outputs.release-version }}
      env:
        GITHUB_TOKEN: ${{ secrets.BRODPS_GHP_TOKEN }}
    - uses: devops-actions/json-to-file@v1.0.4
      with:
        json: "${{ fromJson(steps.get_latest_release.outputs.data).body }}"
        filename: RELEASE_NOTES_NOT_CONVERTED
    - name: Convert to Unix file format
      run: |
          perl -pe 's/\r$//' < RELEASE_NOTES_NOT_CONVERTED > RELEASE_NOTES
    - name: Release notes formatting - Strip html comment from the beginning of release notes
      run: sed -i -e 's/<\!--[^>]*-->//g' RELEASE_NOTES && cat RELEASE_NOTES
    - name: Release notes formatting - Strip "full changelog" link
      run: sed -i -e 's/\*\*Full Changelog.*//g' RELEASE_NOTES && cat RELEASE_NOTES
    - name: Release notes formatting - Strip new contributor section
      run: |
        sed -i -E -e 's/## New Contributors.*//' RELEASE_NOTES
        sed -i -E -e 's/\* .* made their first contribution .*//' RELEASE_NOTES
        cat RELEASE_NOTES
    - name: Release notes formatting - Replace markdown bullets with a more slack-compatible format
      run: sed -i -E -e 's/\* (.*) (by .*) in (http.*)/- \1/' RELEASE_NOTES && cat RELEASE_NOTES
    - name: Release notes formatting - Replace headers with bold text
      run: |
        sed -i -E -e 's/##* (.*)/*\1*/' RELEASE_NOTES && cat RELEASE_NOTES
    - name: Remove empty lines
      run: sed -i -E '/^$/d' RELEASE_NOTES && cat RELEASE_NOTES
    - name: Ensure release body is JSON-friendly
      run: |
        jq --null-input '{"body": $releaseNotes}' --rawfile releaseNotes RELEASE_NOTES > RELEASE_NOTES_JSON && cat RELEASE_NOTES_JSON
    - name: Assign release notes to step variable
      id: release-notes-formatting
      run: |
        RELEASE_NOTES=$(cat RELEASE_NOTES_JSON | jq '.body')
        echo ${RELEASE_NOTES}
        if [[ ${RELEASE_NOTES} == "\"\"" ]]
        then
          RELEASE_NOTES="\"none\""
        fi
        echo "RELEASE_NOTES=${RELEASE_NOTES}" >> $GITHUB_OUTPUT
    - name: Publish Change Log to Slack
      uses: slackapi/slack-github-action@v1.24.0
      id: release-message
      with:
        channel-id: C0727RHB73L
        payload: |
            {
              "text": "",
              "blocks": [
                {
                  "type": "section",
                  "text": {
                    "type": "mrkdwn",
                    "text": "<${{ github.server_url }}/${{ github.repository }}/releases/tag/${{ needs.vars.outputs.release-version }}|${{ needs.vars.outputs.release-version }} has been released.>"
                  }
                },
                {
                  "type": "section",
                  "text": {
                    "type": "mrkdwn",
                    "text": "Release notes - Look at the reply for more details."
                  }
                }
              ]
            }
      env:
        SLACK_BOT_TOKEN: ${{ secrets.SLACK_BOT_TOKEN }}
    - name: notify-slack
      uses: slackapi/slack-github-action@v1.24.0
      id: release-notes
      with:
        channel-id: C0727RHB73L
        payload: |
            {
              "thread_ts": "${{ steps.release-message.outputs.ts }}",
              "text": ${{ steps.release-notes-formatting.outputs.RELEASE_NOTES }}
            }
      env:
        SLACK_BOT_TOKEN: ${{ secrets.SLACK_BOT_TOKEN }}