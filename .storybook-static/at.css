/*
! tailwindcss v3.3.3 | MIT License | https://tailwindcss.com
*/

/*
1. Prevent padding and border from affecting element width. (https://github.com/mozdevs/cssremedy/issues/4)
2. Allow adding a border to an element by just adding a border-width. (https://github.com/tailwindcss/tailwindcss/pull/116)
*/

*,
::before,
::after {
  box-sizing: border-box;
  /* 1 */
  border-width: 0;
  /* 2 */
  border-style: solid;
  /* 2 */
  border-color: #e5e7eb;
  /* 2 */
}

::before,
::after {
  --tw-content: '';
}

/*
1. Use a consistent sensible line-height in all browsers.
2. Prevent adjustments of font size after orientation changes in iOS.
3. Use a more readable tab size.
4. Use the user's configured `sans` font-family by default.
5. Use the user's configured `sans` font-feature-settings by default.
6. Use the user's configured `sans` font-variation-settings by default.
*/

html {
  line-height: 1.5;
  /* 1 */
  -webkit-text-size-adjust: 100%;
  /* 2 */
  -moz-tab-size: 4;
  /* 3 */
  -o-tab-size: 4;
  tab-size: 4;
  /* 3 */
  font-family:
    ui-sans-serif,
    system-ui,
    -apple-system,
    BlinkMacSystemFont,
    'Segoe UI',
    Roboto,
    'Gap Sans',
    Arial,
    'Noto Sans',
    sans-serif,
    'Apple Color Emoji',
    'Segoe UI Emoji',
    'Segoe UI Symbol',
    'Noto Color Emoji';
  /* 4 */
  font-feature-settings: normal;
  /* 5 */
  font-variation-settings: normal;
  /* 6 */
}

/*
1. Remove the margin in all browsers.
2. Inherit line-height from `html` so users can set them as a class directly on the `html` element.
*/

body {
  margin: 0;
  /* 1 */
  line-height: inherit;
  /* 2 */
}

/*
1. Add the correct height in Firefox.
2. Correct the inheritance of border color in Firefox. (https://bugzilla.mozilla.org/show_bug.cgi?id=190655)
3. Ensure horizontal rules are visible by default.
*/

hr {
  height: 0;
  /* 1 */
  color: inherit;
  /* 2 */
  border-top-width: 1px;
  /* 3 */
}

/*
Add the correct text decoration in Chrome, Edge, and Safari.
*/

abbr:where([title]) {
  -webkit-text-decoration: underline dotted;
  text-decoration: underline dotted;
}

/*
Remove the default font size and weight for headings.
*/

h1,
h2,
h3,
h4,
h5,
h6 {
  font-size: inherit;
  font-weight: inherit;
}

/*
Reset links to optimize for opt-in styling instead of opt-out.
*/

a {
  color: inherit;
  text-decoration: inherit;
}

/*
Add the correct font weight in Edge and Safari.
*/

b,
strong {
  font-weight: bolder;
}

/*
1. Use the user's configured `mono` font family by default.
2. Correct the odd `em` font sizing in all browsers.
*/

code,
kbd,
samp,
pre {
  font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, 'Liberation Mono', 'Courier New', monospace;
  /* 1 */
  font-size: 1em;
  /* 2 */
}

/*
Add the correct font size in all browsers.
*/

small {
  font-size: 80%;
}

/*
Prevent `sub` and `sup` elements from affecting the line height in all browsers.
*/

sub,
sup {
  font-size: 75%;
  line-height: 0;
  position: relative;
  vertical-align: baseline;
}

sub {
  bottom: -0.25em;
}

sup {
  top: -0.5em;
}

/*
1. Remove text indentation from table contents in Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=999088, https://bugs.webkit.org/show_bug.cgi?id=201297)
2. Correct table border color inheritance in all Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=935729, https://bugs.webkit.org/show_bug.cgi?id=195016)
3. Remove gaps between table borders by default.
*/

table {
  text-indent: 0;
  /* 1 */
  border-color: inherit;
  /* 2 */
  border-collapse: collapse;
  /* 3 */
}

/*
1. Change the font styles in all browsers.
2. Remove the margin in Firefox and Safari.
3. Remove default padding in all browsers.
*/

button,
input,
optgroup,
select,
textarea {
  font-family: inherit;
  /* 1 */
  font-feature-settings: inherit;
  /* 1 */
  font-variation-settings: inherit;
  /* 1 */
  font-size: 100%;
  /* 1 */
  font-weight: inherit;
  /* 1 */
  line-height: inherit;
  /* 1 */
  color: inherit;
  /* 1 */
  margin: 0;
  /* 2 */
  padding: 0;
  /* 3 */
}

/*
Remove the inheritance of text transform in Edge and Firefox.
*/

button,
select {
  text-transform: none;
}

/*
1. Correct the inability to style clickable types in iOS and Safari.
2. Remove default button styles.
*/

button,
[type='button'],
[type='reset'],
[type='submit'] {
  -webkit-appearance: button;
  /* 1 */
  background-color: transparent;
  /* 2 */
  background-image: none;
  /* 2 */
}

/*
Use the modern Firefox focus style for all focusable elements.
*/

:-moz-focusring {
  outline: auto;
}

/*
Remove the additional `:invalid` styles in Firefox. (https://github.com/mozilla/gecko-dev/blob/2f9eacd9d3d995c937b4251a5557d95d494c9be1/layout/style/res/forms.css#L728-L737)
*/

:-moz-ui-invalid {
  box-shadow: none;
}

/*
Add the correct vertical alignment in Chrome and Firefox.
*/

progress {
  vertical-align: baseline;
}

/*
Correct the cursor style of increment and decrement buttons in Safari.
*/

::-webkit-inner-spin-button,
::-webkit-outer-spin-button {
  height: auto;
}

/*
1. Correct the odd appearance in Chrome and Safari.
2. Correct the outline style in Safari.
*/

[type='search'] {
  -webkit-appearance: textfield;
  /* 1 */
  outline-offset: -2px;
  /* 2 */
}

/*
Remove the inner padding in Chrome and Safari on macOS.
*/

::-webkit-search-decoration {
  -webkit-appearance: none;
}

/*
1. Correct the inability to style clickable types in iOS and Safari.
2. Change font properties to `inherit` in Safari.
*/

::-webkit-file-upload-button {
  -webkit-appearance: button;
  /* 1 */
  font: inherit;
  /* 2 */
}

/*
Add the correct display in Chrome and Safari.
*/

summary {
  display: list-item;
}

/*
Removes the default spacing and border for appropriate elements.
*/

blockquote,
dl,
dd,
h1,
h2,
h3,
h4,
h5,
h6,
hr,
figure,
p,
pre {
  margin: 0;
}

fieldset {
  margin: 0;
  padding: 0;
}

legend {
  padding: 0;
}

ol,
ul,
menu {
  list-style: none;
  margin: 0;
  padding: 0;
}

/*
Reset default styling for dialogs.
*/

dialog {
  padding: 0;
}

/*
Prevent resizing textareas horizontally by default.
*/

textarea {
  resize: vertical;
}

/*
1. Reset the default placeholder opacity in Firefox. (https://github.com/tailwindlabs/tailwindcss/issues/3300)
2. Set the default placeholder color to the user's configured gray 400 color.
*/

input::-moz-placeholder,
textarea::-moz-placeholder {
  opacity: 1;
  /* 1 */
  color: #9ca3af;
  /* 2 */
}

input::placeholder,
textarea::placeholder {
  opacity: 1;
  /* 1 */
  color: #9ca3af;
  /* 2 */
}

/*
Set the default cursor for buttons.
*/

button,
[role='button'] {
  cursor: pointer;
}

/*
Make sure disabled buttons don't get the pointer cursor.
*/

:disabled {
  cursor: default;
}

/*
1. Make replaced elements `display: block` by default. (https://github.com/mozdevs/cssremedy/issues/14)
2. Add `vertical-align: middle` to align replaced elements more sensibly by default. (https://github.com/jensimmons/cssremedy/issues/14#issuecomment-634934210)
   This can trigger a poorly considered lint error in some tools but is included by design.
*/

img,
svg,
video,
canvas,
audio,
iframe,
embed,
object {
  display: block;
  /* 1 */
  vertical-align: middle;
  /* 2 */
}

/*
Constrain images and videos to the parent width and preserve their intrinsic aspect ratio. (https://github.com/mozdevs/cssremedy/issues/14)
*/

img,
video {
  max-width: 100%;
  height: auto;
}

/* Make elements with the HTML hidden attribute stay hidden by default */

[hidden] {
  display: none;
}

.font-brand {
  font-size: 100%;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.font-alt {
  font-size: 100%;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

*,
::before,
::after {
  --tw-border-spacing-x: 0;
  --tw-border-spacing-y: 0;
  --tw-translate-x: 0;
  --tw-translate-y: 0;
  --tw-rotate: 0;
  --tw-skew-x: 0;
  --tw-skew-y: 0;
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  --tw-pan-x:  ;
  --tw-pan-y:  ;
  --tw-pinch-zoom:  ;
  --tw-scroll-snap-strictness: proximity;
  --tw-gradient-from-position:  ;
  --tw-gradient-via-position:  ;
  --tw-gradient-to-position:  ;
  --tw-ordinal:  ;
  --tw-slashed-zero:  ;
  --tw-numeric-figure:  ;
  --tw-numeric-spacing:  ;
  --tw-numeric-fraction:  ;
  --tw-ring-inset:  ;
  --tw-ring-offset-width: 0px;
  --tw-ring-offset-color: #fff;
  --tw-ring-color: rgb(59 130 246 / 0.5);
  --tw-ring-offset-shadow: 0 0 #0000;
  --tw-ring-shadow: 0 0 #0000;
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  --tw-blur:  ;
  --tw-brightness:  ;
  --tw-contrast:  ;
  --tw-grayscale:  ;
  --tw-hue-rotate:  ;
  --tw-invert:  ;
  --tw-saturate:  ;
  --tw-sepia:  ;
  --tw-drop-shadow:  ;
  --tw-backdrop-blur:  ;
  --tw-backdrop-brightness:  ;
  --tw-backdrop-contrast:  ;
  --tw-backdrop-grayscale:  ;
  --tw-backdrop-hue-rotate:  ;
  --tw-backdrop-invert:  ;
  --tw-backdrop-opacity:  ;
  --tw-backdrop-saturate:  ;
  --tw-backdrop-sepia:  ;
}

::backdrop {
  --tw-border-spacing-x: 0;
  --tw-border-spacing-y: 0;
  --tw-translate-x: 0;
  --tw-translate-y: 0;
  --tw-rotate: 0;
  --tw-skew-x: 0;
  --tw-skew-y: 0;
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  --tw-pan-x:  ;
  --tw-pan-y:  ;
  --tw-pinch-zoom:  ;
  --tw-scroll-snap-strictness: proximity;
  --tw-gradient-from-position:  ;
  --tw-gradient-via-position:  ;
  --tw-gradient-to-position:  ;
  --tw-ordinal:  ;
  --tw-slashed-zero:  ;
  --tw-numeric-figure:  ;
  --tw-numeric-spacing:  ;
  --tw-numeric-fraction:  ;
  --tw-ring-inset:  ;
  --tw-ring-offset-width: 0px;
  --tw-ring-offset-color: #fff;
  --tw-ring-color: rgb(59 130 246 / 0.5);
  --tw-ring-offset-shadow: 0 0 #0000;
  --tw-ring-shadow: 0 0 #0000;
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  --tw-blur:  ;
  --tw-brightness:  ;
  --tw-contrast:  ;
  --tw-grayscale:  ;
  --tw-hue-rotate:  ;
  --tw-invert:  ;
  --tw-saturate:  ;
  --tw-sepia:  ;
  --tw-drop-shadow:  ;
  --tw-backdrop-blur:  ;
  --tw-backdrop-brightness:  ;
  --tw-backdrop-contrast:  ;
  --tw-backdrop-grayscale:  ;
  --tw-backdrop-hue-rotate:  ;
  --tw-backdrop-invert:  ;
  --tw-backdrop-opacity:  ;
  --tw-backdrop-saturate:  ;
  --tw-backdrop-sepia:  ;
}

.container {
  width: 100%;
}

@media (min-width: 640px) {
  .container {
    max-width: 640px;
  }
}

@media (min-width: 768px) {
  .container {
    max-width: 768px;
  }
}

@media (min-width: 1024px) {
  .container {
    max-width: 1024px;
  }
}

@media (min-width: 1280px) {
  .container {
    max-width: 1280px;
  }
}

@media (min-width: 1536px) {
  .container {
    max-width: 1536px;
  }
}

.visible {
  visibility: visible;
}

.collapse {
  visibility: collapse;
}

.static {
  position: static;
}

.fixed {
  position: fixed;
}

.absolute {
  position: absolute;
}

.relative {
  position: relative;
}

.sticky {
  position: sticky;
}

.end-1 {
  inset-inline-end: 0.25rem;
}

.left-0 {
  left: 0px;
}

.right-\[-0\.75px\] {
  right: -0.75px;
}

.right-\[0\.8125rem\] {
  right: 0.8125rem;
}

.right-px {
  right: 1px;
}

.top-1 {
  top: 0.25rem;
}

.top-1\.5 {
  top: 0.375rem;
}

.top-1\/2 {
  top: 50%;
}

.top-10 {
  top: 2.5rem;
}

.float-right {
  float: right;
}

.float-left {
  float: left;
}

.m-auto {
  margin: auto;
}

.my-6 {
  margin-top: 1.5rem;
  margin-bottom: 1.5rem;
}

.-ml-\[0\.063rem\] {
  margin-left: -0.063rem;
}

.mb-0 {
  margin-bottom: 0px;
}

.mb-0\.5 {
  margin-bottom: 0.125rem;
}

.mt-4 {
  margin-top: 1rem;
}

.mt-\[5px\] {
  margin-top: 5px;
}

.box-border {
  box-sizing: border-box;
}

.block {
  display: block;
}

.inline-block {
  display: inline-block;
}

.inline {
  display: inline;
}

.flex {
  display: flex;
}

.inline-flex {
  display: inline-flex;
}

.table {
  display: table;
}

.table-caption {
  display: table-caption;
}

.table-cell {
  display: table-cell;
}

.table-column {
  display: table-column;
}

.table-column-group {
  display: table-column-group;
}

.table-footer-group {
  display: table-footer-group;
}

.table-header-group {
  display: table-header-group;
}

.table-row-group {
  display: table-row-group;
}

.table-row {
  display: table-row;
}

.grid {
  display: grid;
}

.contents {
  display: contents;
}

.list-item {
  display: list-item;
}

.hidden {
  display: none;
}

.h-10 {
  height: 2.5rem;
}

.h-6 {
  height: 1.5rem;
}

.h-\[2\.0625rem\] {
  height: 2.0625rem;
}

.h-\[40px\] {
  height: 40px;
}

.max-h-\[60px\] {
  max-height: 60px;
}

.min-h-fit {
  min-height: -moz-fit-content;
  min-height: fit-content;
}

.w-2 {
  width: 0.5rem;
}

.w-2\.5 {
  width: 0.625rem;
}

.w-32 {
  width: 8rem;
}

.w-\[10\.188rem\] {
  width: 10.188rem;
}

.w-\[100px\] {
  width: 100px;
}

.w-\[3\.125rem\] {
  width: 3.125rem;
}

.w-\[5\.125rem\] {
  width: 5.125rem;
}

.w-\[6\.563rem\] {
  width: 6.563rem;
}

.w-full {
  width: 100%;
}

.w-px {
  width: 1px;
}

.min-w-\[12rem\] {
  min-width: 12rem;
}

.min-w-\[6\.43rem\] {
  min-width: 6.43rem;
}

.max-w-\[1440px\] {
  max-width: 1440px;
}

.max-w-\[16rem\] {
  max-width: 16rem;
}

.shrink {
  flex-shrink: 1;
}

.grow {
  flex-grow: 1;
}

.transform {
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y))
    scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.resize {
  resize: both;
}

.flex-col {
  flex-direction: column;
}

.items-center {
  align-items: center;
}

.justify-center {
  justify-content: center;
}

.justify-between {
  justify-content: space-between;
}

.overflow-hidden {
  overflow: hidden;
}

.text-ellipsis {
  text-overflow: ellipsis;
}

.whitespace-nowrap {
  white-space: nowrap;
}

.border {
  border-width: 1px;
}

.border-x-0 {
  border-left-width: 0px;
  border-right-width: 0px;
}

.border-b-0 {
  border-bottom-width: 0px;
}

.border-r {
  border-right-width: 1px;
}

.border-t {
  border-top-width: 1px;
}

.border-none {
  border-style: none;
}

.border-\[\#cccccc\] {
  --tw-border-opacity: 1;
  border-color: rgb(204 204 204 / var(--tw-border-opacity));
}

.border-g1 {
  --tw-border-opacity: 1;
  border-color: rgb(51 51 51 / var(--tw-border-opacity));
}

.border-gray-20 {
  --tw-border-opacity: 1;
  border-color: rgb(204 204 204 / var(--tw-border-opacity));
}

.bg-alpha00 {
  background-color: transparent;
}

.bg-alpha00\/0 {
  background-color: rgb(0 0 0 / 0);
}

.bg-alpha00\/10 {
  background-color: rgb(0 0 0 / 0.1);
}

.bg-alpha00\/100 {
  background-color: rgb(0 0 0 / 1);
}

.bg-alpha00\/20 {
  background-color: rgb(0 0 0 / 0.2);
}

.bg-alpha00\/25 {
  background-color: rgb(0 0 0 / 0.25);
}

.bg-alpha00\/30 {
  background-color: rgb(0 0 0 / 0.3);
}

.bg-alpha00\/40 {
  background-color: rgb(0 0 0 / 0.4);
}

.bg-alpha00\/5 {
  background-color: rgb(0 0 0 / 0.05);
}

.bg-alpha00\/50 {
  background-color: rgb(0 0 0 / 0.5);
}

.bg-alpha00\/60 {
  background-color: rgb(0 0 0 / 0.6);
}

.bg-alpha00\/70 {
  background-color: rgb(0 0 0 / 0.7);
}

.bg-alpha00\/75 {
  background-color: rgb(0 0 0 / 0.75);
}

.bg-alpha00\/80 {
  background-color: rgb(0 0 0 / 0.8);
}

.bg-alpha00\/90 {
  background-color: rgb(0 0 0 / 0.9);
}

.bg-alpha00\/95 {
  background-color: rgb(0 0 0 / 0.95);
}

.bg-amber-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(254 243 199 / var(--tw-bg-opacity));
}

.bg-amber-100\/0 {
  background-color: rgb(254 243 199 / 0);
}

.bg-amber-100\/10 {
  background-color: rgb(254 243 199 / 0.1);
}

.bg-amber-100\/100 {
  background-color: rgb(254 243 199 / 1);
}

.bg-amber-100\/20 {
  background-color: rgb(254 243 199 / 0.2);
}

.bg-amber-100\/25 {
  background-color: rgb(254 243 199 / 0.25);
}

.bg-amber-100\/30 {
  background-color: rgb(254 243 199 / 0.3);
}

.bg-amber-100\/40 {
  background-color: rgb(254 243 199 / 0.4);
}

.bg-amber-100\/5 {
  background-color: rgb(254 243 199 / 0.05);
}

.bg-amber-100\/50 {
  background-color: rgb(254 243 199 / 0.5);
}

.bg-amber-100\/60 {
  background-color: rgb(254 243 199 / 0.6);
}

.bg-amber-100\/70 {
  background-color: rgb(254 243 199 / 0.7);
}

.bg-amber-100\/75 {
  background-color: rgb(254 243 199 / 0.75);
}

.bg-amber-100\/80 {
  background-color: rgb(254 243 199 / 0.8);
}

.bg-amber-100\/90 {
  background-color: rgb(254 243 199 / 0.9);
}

.bg-amber-100\/95 {
  background-color: rgb(254 243 199 / 0.95);
}

.bg-amber-200 {
  --tw-bg-opacity: 1;
  background-color: rgb(253 230 138 / var(--tw-bg-opacity));
}

.bg-amber-200\/0 {
  background-color: rgb(253 230 138 / 0);
}

.bg-amber-200\/10 {
  background-color: rgb(253 230 138 / 0.1);
}

.bg-amber-200\/100 {
  background-color: rgb(253 230 138 / 1);
}

.bg-amber-200\/20 {
  background-color: rgb(253 230 138 / 0.2);
}

.bg-amber-200\/25 {
  background-color: rgb(253 230 138 / 0.25);
}

.bg-amber-200\/30 {
  background-color: rgb(253 230 138 / 0.3);
}

.bg-amber-200\/40 {
  background-color: rgb(253 230 138 / 0.4);
}

.bg-amber-200\/5 {
  background-color: rgb(253 230 138 / 0.05);
}

.bg-amber-200\/50 {
  background-color: rgb(253 230 138 / 0.5);
}

.bg-amber-200\/60 {
  background-color: rgb(253 230 138 / 0.6);
}

.bg-amber-200\/70 {
  background-color: rgb(253 230 138 / 0.7);
}

.bg-amber-200\/75 {
  background-color: rgb(253 230 138 / 0.75);
}

.bg-amber-200\/80 {
  background-color: rgb(253 230 138 / 0.8);
}

.bg-amber-200\/90 {
  background-color: rgb(253 230 138 / 0.9);
}

.bg-amber-200\/95 {
  background-color: rgb(253 230 138 / 0.95);
}

.bg-amber-300 {
  --tw-bg-opacity: 1;
  background-color: rgb(252 211 77 / var(--tw-bg-opacity));
}

.bg-amber-300\/0 {
  background-color: rgb(252 211 77 / 0);
}

.bg-amber-300\/10 {
  background-color: rgb(252 211 77 / 0.1);
}

.bg-amber-300\/100 {
  background-color: rgb(252 211 77 / 1);
}

.bg-amber-300\/20 {
  background-color: rgb(252 211 77 / 0.2);
}

.bg-amber-300\/25 {
  background-color: rgb(252 211 77 / 0.25);
}

.bg-amber-300\/30 {
  background-color: rgb(252 211 77 / 0.3);
}

.bg-amber-300\/40 {
  background-color: rgb(252 211 77 / 0.4);
}

.bg-amber-300\/5 {
  background-color: rgb(252 211 77 / 0.05);
}

.bg-amber-300\/50 {
  background-color: rgb(252 211 77 / 0.5);
}

.bg-amber-300\/60 {
  background-color: rgb(252 211 77 / 0.6);
}

.bg-amber-300\/70 {
  background-color: rgb(252 211 77 / 0.7);
}

.bg-amber-300\/75 {
  background-color: rgb(252 211 77 / 0.75);
}

.bg-amber-300\/80 {
  background-color: rgb(252 211 77 / 0.8);
}

.bg-amber-300\/90 {
  background-color: rgb(252 211 77 / 0.9);
}

.bg-amber-300\/95 {
  background-color: rgb(252 211 77 / 0.95);
}

.bg-amber-400 {
  --tw-bg-opacity: 1;
  background-color: rgb(251 191 36 / var(--tw-bg-opacity));
}

.bg-amber-400\/0 {
  background-color: rgb(251 191 36 / 0);
}

.bg-amber-400\/10 {
  background-color: rgb(251 191 36 / 0.1);
}

.bg-amber-400\/100 {
  background-color: rgb(251 191 36 / 1);
}

.bg-amber-400\/20 {
  background-color: rgb(251 191 36 / 0.2);
}

.bg-amber-400\/25 {
  background-color: rgb(251 191 36 / 0.25);
}

.bg-amber-400\/30 {
  background-color: rgb(251 191 36 / 0.3);
}

.bg-amber-400\/40 {
  background-color: rgb(251 191 36 / 0.4);
}

.bg-amber-400\/5 {
  background-color: rgb(251 191 36 / 0.05);
}

.bg-amber-400\/50 {
  background-color: rgb(251 191 36 / 0.5);
}

.bg-amber-400\/60 {
  background-color: rgb(251 191 36 / 0.6);
}

.bg-amber-400\/70 {
  background-color: rgb(251 191 36 / 0.7);
}

.bg-amber-400\/75 {
  background-color: rgb(251 191 36 / 0.75);
}

.bg-amber-400\/80 {
  background-color: rgb(251 191 36 / 0.8);
}

.bg-amber-400\/90 {
  background-color: rgb(251 191 36 / 0.9);
}

.bg-amber-400\/95 {
  background-color: rgb(251 191 36 / 0.95);
}

.bg-amber-50 {
  --tw-bg-opacity: 1;
  background-color: rgb(255 251 235 / var(--tw-bg-opacity));
}

.bg-amber-50\/0 {
  background-color: rgb(255 251 235 / 0);
}

.bg-amber-50\/10 {
  background-color: rgb(255 251 235 / 0.1);
}

.bg-amber-50\/100 {
  background-color: rgb(255 251 235 / 1);
}

.bg-amber-50\/20 {
  background-color: rgb(255 251 235 / 0.2);
}

.bg-amber-50\/25 {
  background-color: rgb(255 251 235 / 0.25);
}

.bg-amber-50\/30 {
  background-color: rgb(255 251 235 / 0.3);
}

.bg-amber-50\/40 {
  background-color: rgb(255 251 235 / 0.4);
}

.bg-amber-50\/5 {
  background-color: rgb(255 251 235 / 0.05);
}

.bg-amber-50\/50 {
  background-color: rgb(255 251 235 / 0.5);
}

.bg-amber-50\/60 {
  background-color: rgb(255 251 235 / 0.6);
}

.bg-amber-50\/70 {
  background-color: rgb(255 251 235 / 0.7);
}

.bg-amber-50\/75 {
  background-color: rgb(255 251 235 / 0.75);
}

.bg-amber-50\/80 {
  background-color: rgb(255 251 235 / 0.8);
}

.bg-amber-50\/90 {
  background-color: rgb(255 251 235 / 0.9);
}

.bg-amber-50\/95 {
  background-color: rgb(255 251 235 / 0.95);
}

.bg-amber-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(245 158 11 / var(--tw-bg-opacity));
}

.bg-amber-500\/0 {
  background-color: rgb(245 158 11 / 0);
}

.bg-amber-500\/10 {
  background-color: rgb(245 158 11 / 0.1);
}

.bg-amber-500\/100 {
  background-color: rgb(245 158 11 / 1);
}

.bg-amber-500\/20 {
  background-color: rgb(245 158 11 / 0.2);
}

.bg-amber-500\/25 {
  background-color: rgb(245 158 11 / 0.25);
}

.bg-amber-500\/30 {
  background-color: rgb(245 158 11 / 0.3);
}

.bg-amber-500\/40 {
  background-color: rgb(245 158 11 / 0.4);
}

.bg-amber-500\/5 {
  background-color: rgb(245 158 11 / 0.05);
}

.bg-amber-500\/50 {
  background-color: rgb(245 158 11 / 0.5);
}

.bg-amber-500\/60 {
  background-color: rgb(245 158 11 / 0.6);
}

.bg-amber-500\/70 {
  background-color: rgb(245 158 11 / 0.7);
}

.bg-amber-500\/75 {
  background-color: rgb(245 158 11 / 0.75);
}

.bg-amber-500\/80 {
  background-color: rgb(245 158 11 / 0.8);
}

.bg-amber-500\/90 {
  background-color: rgb(245 158 11 / 0.9);
}

.bg-amber-500\/95 {
  background-color: rgb(245 158 11 / 0.95);
}

.bg-amber-600 {
  --tw-bg-opacity: 1;
  background-color: rgb(217 119 6 / var(--tw-bg-opacity));
}

.bg-amber-600\/0 {
  background-color: rgb(217 119 6 / 0);
}

.bg-amber-600\/10 {
  background-color: rgb(217 119 6 / 0.1);
}

.bg-amber-600\/100 {
  background-color: rgb(217 119 6 / 1);
}

.bg-amber-600\/20 {
  background-color: rgb(217 119 6 / 0.2);
}

.bg-amber-600\/25 {
  background-color: rgb(217 119 6 / 0.25);
}

.bg-amber-600\/30 {
  background-color: rgb(217 119 6 / 0.3);
}

.bg-amber-600\/40 {
  background-color: rgb(217 119 6 / 0.4);
}

.bg-amber-600\/5 {
  background-color: rgb(217 119 6 / 0.05);
}

.bg-amber-600\/50 {
  background-color: rgb(217 119 6 / 0.5);
}

.bg-amber-600\/60 {
  background-color: rgb(217 119 6 / 0.6);
}

.bg-amber-600\/70 {
  background-color: rgb(217 119 6 / 0.7);
}

.bg-amber-600\/75 {
  background-color: rgb(217 119 6 / 0.75);
}

.bg-amber-600\/80 {
  background-color: rgb(217 119 6 / 0.8);
}

.bg-amber-600\/90 {
  background-color: rgb(217 119 6 / 0.9);
}

.bg-amber-600\/95 {
  background-color: rgb(217 119 6 / 0.95);
}

.bg-amber-700 {
  --tw-bg-opacity: 1;
  background-color: rgb(180 83 9 / var(--tw-bg-opacity));
}

.bg-amber-700\/0 {
  background-color: rgb(180 83 9 / 0);
}

.bg-amber-700\/10 {
  background-color: rgb(180 83 9 / 0.1);
}

.bg-amber-700\/100 {
  background-color: rgb(180 83 9 / 1);
}

.bg-amber-700\/20 {
  background-color: rgb(180 83 9 / 0.2);
}

.bg-amber-700\/25 {
  background-color: rgb(180 83 9 / 0.25);
}

.bg-amber-700\/30 {
  background-color: rgb(180 83 9 / 0.3);
}

.bg-amber-700\/40 {
  background-color: rgb(180 83 9 / 0.4);
}

.bg-amber-700\/5 {
  background-color: rgb(180 83 9 / 0.05);
}

.bg-amber-700\/50 {
  background-color: rgb(180 83 9 / 0.5);
}

.bg-amber-700\/60 {
  background-color: rgb(180 83 9 / 0.6);
}

.bg-amber-700\/70 {
  background-color: rgb(180 83 9 / 0.7);
}

.bg-amber-700\/75 {
  background-color: rgb(180 83 9 / 0.75);
}

.bg-amber-700\/80 {
  background-color: rgb(180 83 9 / 0.8);
}

.bg-amber-700\/90 {
  background-color: rgb(180 83 9 / 0.9);
}

.bg-amber-700\/95 {
  background-color: rgb(180 83 9 / 0.95);
}

.bg-amber-800 {
  --tw-bg-opacity: 1;
  background-color: rgb(146 64 14 / var(--tw-bg-opacity));
}

.bg-amber-800\/0 {
  background-color: rgb(146 64 14 / 0);
}

.bg-amber-800\/10 {
  background-color: rgb(146 64 14 / 0.1);
}

.bg-amber-800\/100 {
  background-color: rgb(146 64 14 / 1);
}

.bg-amber-800\/20 {
  background-color: rgb(146 64 14 / 0.2);
}

.bg-amber-800\/25 {
  background-color: rgb(146 64 14 / 0.25);
}

.bg-amber-800\/30 {
  background-color: rgb(146 64 14 / 0.3);
}

.bg-amber-800\/40 {
  background-color: rgb(146 64 14 / 0.4);
}

.bg-amber-800\/5 {
  background-color: rgb(146 64 14 / 0.05);
}

.bg-amber-800\/50 {
  background-color: rgb(146 64 14 / 0.5);
}

.bg-amber-800\/60 {
  background-color: rgb(146 64 14 / 0.6);
}

.bg-amber-800\/70 {
  background-color: rgb(146 64 14 / 0.7);
}

.bg-amber-800\/75 {
  background-color: rgb(146 64 14 / 0.75);
}

.bg-amber-800\/80 {
  background-color: rgb(146 64 14 / 0.8);
}

.bg-amber-800\/90 {
  background-color: rgb(146 64 14 / 0.9);
}

.bg-amber-800\/95 {
  background-color: rgb(146 64 14 / 0.95);
}

.bg-amber-900 {
  --tw-bg-opacity: 1;
  background-color: rgb(120 53 15 / var(--tw-bg-opacity));
}

.bg-amber-900\/0 {
  background-color: rgb(120 53 15 / 0);
}

.bg-amber-900\/10 {
  background-color: rgb(120 53 15 / 0.1);
}

.bg-amber-900\/100 {
  background-color: rgb(120 53 15 / 1);
}

.bg-amber-900\/20 {
  background-color: rgb(120 53 15 / 0.2);
}

.bg-amber-900\/25 {
  background-color: rgb(120 53 15 / 0.25);
}

.bg-amber-900\/30 {
  background-color: rgb(120 53 15 / 0.3);
}

.bg-amber-900\/40 {
  background-color: rgb(120 53 15 / 0.4);
}

.bg-amber-900\/5 {
  background-color: rgb(120 53 15 / 0.05);
}

.bg-amber-900\/50 {
  background-color: rgb(120 53 15 / 0.5);
}

.bg-amber-900\/60 {
  background-color: rgb(120 53 15 / 0.6);
}

.bg-amber-900\/70 {
  background-color: rgb(120 53 15 / 0.7);
}

.bg-amber-900\/75 {
  background-color: rgb(120 53 15 / 0.75);
}

.bg-amber-900\/80 {
  background-color: rgb(120 53 15 / 0.8);
}

.bg-amber-900\/90 {
  background-color: rgb(120 53 15 / 0.9);
}

.bg-amber-900\/95 {
  background-color: rgb(120 53 15 / 0.95);
}

.bg-amber-950 {
  --tw-bg-opacity: 1;
  background-color: rgb(69 26 3 / var(--tw-bg-opacity));
}

.bg-amber-950\/0 {
  background-color: rgb(69 26 3 / 0);
}

.bg-amber-950\/10 {
  background-color: rgb(69 26 3 / 0.1);
}

.bg-amber-950\/100 {
  background-color: rgb(69 26 3 / 1);
}

.bg-amber-950\/20 {
  background-color: rgb(69 26 3 / 0.2);
}

.bg-amber-950\/25 {
  background-color: rgb(69 26 3 / 0.25);
}

.bg-amber-950\/30 {
  background-color: rgb(69 26 3 / 0.3);
}

.bg-amber-950\/40 {
  background-color: rgb(69 26 3 / 0.4);
}

.bg-amber-950\/5 {
  background-color: rgb(69 26 3 / 0.05);
}

.bg-amber-950\/50 {
  background-color: rgb(69 26 3 / 0.5);
}

.bg-amber-950\/60 {
  background-color: rgb(69 26 3 / 0.6);
}

.bg-amber-950\/70 {
  background-color: rgb(69 26 3 / 0.7);
}

.bg-amber-950\/75 {
  background-color: rgb(69 26 3 / 0.75);
}

.bg-amber-950\/80 {
  background-color: rgb(69 26 3 / 0.8);
}

.bg-amber-950\/90 {
  background-color: rgb(69 26 3 / 0.9);
}

.bg-amber-950\/95 {
  background-color: rgb(69 26 3 / 0.95);
}

.bg-b1 {
  --tw-bg-opacity: 1;
  background-color: rgb(51 51 51 / var(--tw-bg-opacity));
}

.bg-b1\/0 {
  background-color: rgb(51 51 51 / 0);
}

.bg-b1\/10 {
  background-color: rgb(51 51 51 / 0.1);
}

.bg-b1\/100 {
  background-color: rgb(51 51 51 / 1);
}

.bg-b1\/20 {
  background-color: rgb(51 51 51 / 0.2);
}

.bg-b1\/25 {
  background-color: rgb(51 51 51 / 0.25);
}

.bg-b1\/30 {
  background-color: rgb(51 51 51 / 0.3);
}

.bg-b1\/40 {
  background-color: rgb(51 51 51 / 0.4);
}

.bg-b1\/5 {
  background-color: rgb(51 51 51 / 0.05);
}

.bg-b1\/50 {
  background-color: rgb(51 51 51 / 0.5);
}

.bg-b1\/60 {
  background-color: rgb(51 51 51 / 0.6);
}

.bg-b1\/70 {
  background-color: rgb(51 51 51 / 0.7);
}

.bg-b1\/75 {
  background-color: rgb(51 51 51 / 0.75);
}

.bg-b1\/80 {
  background-color: rgb(51 51 51 / 0.8);
}

.bg-b1\/90 {
  background-color: rgb(51 51 51 / 0.9);
}

.bg-b1\/95 {
  background-color: rgb(51 51 51 / 0.95);
}

.bg-b2 {
  --tw-bg-opacity: 1;
  background-color: rgb(2 74 98 / var(--tw-bg-opacity));
}

.bg-b2\/0 {
  background-color: rgb(2 74 98 / 0);
}

.bg-b2\/10 {
  background-color: rgb(2 74 98 / 0.1);
}

.bg-b2\/100 {
  background-color: rgb(2 74 98 / 1);
}

.bg-b2\/20 {
  background-color: rgb(2 74 98 / 0.2);
}

.bg-b2\/25 {
  background-color: rgb(2 74 98 / 0.25);
}

.bg-b2\/30 {
  background-color: rgb(2 74 98 / 0.3);
}

.bg-b2\/40 {
  background-color: rgb(2 74 98 / 0.4);
}

.bg-b2\/5 {
  background-color: rgb(2 74 98 / 0.05);
}

.bg-b2\/50 {
  background-color: rgb(2 74 98 / 0.5);
}

.bg-b2\/60 {
  background-color: rgb(2 74 98 / 0.6);
}

.bg-b2\/70 {
  background-color: rgb(2 74 98 / 0.7);
}

.bg-b2\/75 {
  background-color: rgb(2 74 98 / 0.75);
}

.bg-b2\/80 {
  background-color: rgb(2 74 98 / 0.8);
}

.bg-b2\/90 {
  background-color: rgb(2 74 98 / 0.9);
}

.bg-b2\/95 {
  background-color: rgb(2 74 98 / 0.95);
}

.bg-b3 {
  --tw-bg-opacity: 1;
  background-color: rgb(0 174 188 / var(--tw-bg-opacity));
}

.bg-b3\/0 {
  background-color: rgb(0 174 188 / 0);
}

.bg-b3\/10 {
  background-color: rgb(0 174 188 / 0.1);
}

.bg-b3\/100 {
  background-color: rgb(0 174 188 / 1);
}

.bg-b3\/20 {
  background-color: rgb(0 174 188 / 0.2);
}

.bg-b3\/25 {
  background-color: rgb(0 174 188 / 0.25);
}

.bg-b3\/30 {
  background-color: rgb(0 174 188 / 0.3);
}

.bg-b3\/40 {
  background-color: rgb(0 174 188 / 0.4);
}

.bg-b3\/5 {
  background-color: rgb(0 174 188 / 0.05);
}

.bg-b3\/50 {
  background-color: rgb(0 174 188 / 0.5);
}

.bg-b3\/60 {
  background-color: rgb(0 174 188 / 0.6);
}

.bg-b3\/70 {
  background-color: rgb(0 174 188 / 0.7);
}

.bg-b3\/75 {
  background-color: rgb(0 174 188 / 0.75);
}

.bg-b3\/80 {
  background-color: rgb(0 174 188 / 0.8);
}

.bg-b3\/90 {
  background-color: rgb(0 174 188 / 0.9);
}

.bg-b3\/95 {
  background-color: rgb(0 174 188 / 0.95);
}

.bg-bk {
  --tw-bg-opacity: 1;
  background-color: rgb(0 0 0 / var(--tw-bg-opacity));
}

.bg-bk\/0 {
  background-color: rgb(0 0 0 / 0);
}

.bg-bk\/10 {
  background-color: rgb(0 0 0 / 0.1);
}

.bg-bk\/100 {
  background-color: rgb(0 0 0 / 1);
}

.bg-bk\/20 {
  background-color: rgb(0 0 0 / 0.2);
}

.bg-bk\/25 {
  background-color: rgb(0 0 0 / 0.25);
}

.bg-bk\/30 {
  background-color: rgb(0 0 0 / 0.3);
}

.bg-bk\/40 {
  background-color: rgb(0 0 0 / 0.4);
}

.bg-bk\/5 {
  background-color: rgb(0 0 0 / 0.05);
}

.bg-bk\/50 {
  background-color: rgb(0 0 0 / 0.5);
}

.bg-bk\/60 {
  background-color: rgb(0 0 0 / 0.6);
}

.bg-bk\/70 {
  background-color: rgb(0 0 0 / 0.7);
}

.bg-bk\/75 {
  background-color: rgb(0 0 0 / 0.75);
}

.bg-bk\/80 {
  background-color: rgb(0 0 0 / 0.8);
}

.bg-bk\/90 {
  background-color: rgb(0 0 0 / 0.9);
}

.bg-bk\/95 {
  background-color: rgb(0 0 0 / 0.95);
}

.bg-bkAlpha-25 {
  background-color: rgba(0, 0, 0, 0.25);
}

.bg-bkAlpha-25\/0 {
  background-color: rgba(0, 0, 0, 0);
}

.bg-bkAlpha-25\/10 {
  background-color: rgba(0, 0, 0, 0.1);
}

.bg-bkAlpha-25\/100 {
  background-color: rgba(0, 0, 0, 1);
}

.bg-bkAlpha-25\/20 {
  background-color: rgba(0, 0, 0, 0.2);
}

.bg-bkAlpha-25\/25 {
  background-color: rgba(0, 0, 0, 0.25);
}

.bg-bkAlpha-25\/30 {
  background-color: rgba(0, 0, 0, 0.3);
}

.bg-bkAlpha-25\/40 {
  background-color: rgba(0, 0, 0, 0.4);
}

.bg-bkAlpha-25\/5 {
  background-color: rgba(0, 0, 0, 0.05);
}

.bg-bkAlpha-25\/50 {
  background-color: rgba(0, 0, 0, 0.5);
}

.bg-bkAlpha-25\/60 {
  background-color: rgba(0, 0, 0, 0.6);
}

.bg-bkAlpha-25\/70 {
  background-color: rgba(0, 0, 0, 0.7);
}

.bg-bkAlpha-25\/75 {
  background-color: rgba(0, 0, 0, 0.75);
}

.bg-bkAlpha-25\/80 {
  background-color: rgba(0, 0, 0, 0.8);
}

.bg-bkAlpha-25\/90 {
  background-color: rgba(0, 0, 0, 0.9);
}

.bg-bkAlpha-25\/95 {
  background-color: rgba(0, 0, 0, 0.95);
}

.bg-bkAlpha-50 {
  background-color: rgba(0, 0, 0, 0.5);
}

.bg-bkAlpha-50\/0 {
  background-color: rgba(0, 0, 0, 0);
}

.bg-bkAlpha-50\/10 {
  background-color: rgba(0, 0, 0, 0.1);
}

.bg-bkAlpha-50\/100 {
  background-color: rgba(0, 0, 0, 1);
}

.bg-bkAlpha-50\/20 {
  background-color: rgba(0, 0, 0, 0.2);
}

.bg-bkAlpha-50\/25 {
  background-color: rgba(0, 0, 0, 0.25);
}

.bg-bkAlpha-50\/30 {
  background-color: rgba(0, 0, 0, 0.3);
}

.bg-bkAlpha-50\/40 {
  background-color: rgba(0, 0, 0, 0.4);
}

.bg-bkAlpha-50\/5 {
  background-color: rgba(0, 0, 0, 0.05);
}

.bg-bkAlpha-50\/50 {
  background-color: rgba(0, 0, 0, 0.5);
}

.bg-bkAlpha-50\/60 {
  background-color: rgba(0, 0, 0, 0.6);
}

.bg-bkAlpha-50\/70 {
  background-color: rgba(0, 0, 0, 0.7);
}

.bg-bkAlpha-50\/75 {
  background-color: rgba(0, 0, 0, 0.75);
}

.bg-bkAlpha-50\/80 {
  background-color: rgba(0, 0, 0, 0.8);
}

.bg-bkAlpha-50\/90 {
  background-color: rgba(0, 0, 0, 0.9);
}

.bg-bkAlpha-50\/95 {
  background-color: rgba(0, 0, 0, 0.95);
}

.bg-bkAlpha-75 {
  background-color: rgba(0, 0, 0, 0.75);
}

.bg-bkAlpha-75\/0 {
  background-color: rgba(0, 0, 0, 0);
}

.bg-bkAlpha-75\/10 {
  background-color: rgba(0, 0, 0, 0.1);
}

.bg-bkAlpha-75\/100 {
  background-color: rgba(0, 0, 0, 1);
}

.bg-bkAlpha-75\/20 {
  background-color: rgba(0, 0, 0, 0.2);
}

.bg-bkAlpha-75\/25 {
  background-color: rgba(0, 0, 0, 0.25);
}

.bg-bkAlpha-75\/30 {
  background-color: rgba(0, 0, 0, 0.3);
}

.bg-bkAlpha-75\/40 {
  background-color: rgba(0, 0, 0, 0.4);
}

.bg-bkAlpha-75\/5 {
  background-color: rgba(0, 0, 0, 0.05);
}

.bg-bkAlpha-75\/50 {
  background-color: rgba(0, 0, 0, 0.5);
}

.bg-bkAlpha-75\/60 {
  background-color: rgba(0, 0, 0, 0.6);
}

.bg-bkAlpha-75\/70 {
  background-color: rgba(0, 0, 0, 0.7);
}

.bg-bkAlpha-75\/75 {
  background-color: rgba(0, 0, 0, 0.75);
}

.bg-bkAlpha-75\/80 {
  background-color: rgba(0, 0, 0, 0.8);
}

.bg-bkAlpha-75\/90 {
  background-color: rgba(0, 0, 0, 0.9);
}

.bg-bkAlpha-75\/95 {
  background-color: rgba(0, 0, 0, 0.95);
}

.bg-black {
  --tw-bg-opacity: 1;
  background-color: rgb(0 0 0 / var(--tw-bg-opacity));
}

.bg-black\/0 {
  background-color: rgb(0 0 0 / 0);
}

.bg-black\/10 {
  background-color: rgb(0 0 0 / 0.1);
}

.bg-black\/100 {
  background-color: rgb(0 0 0 / 1);
}

.bg-black\/20 {
  background-color: rgb(0 0 0 / 0.2);
}

.bg-black\/25 {
  background-color: rgb(0 0 0 / 0.25);
}

.bg-black\/30 {
  background-color: rgb(0 0 0 / 0.3);
}

.bg-black\/40 {
  background-color: rgb(0 0 0 / 0.4);
}

.bg-black\/5 {
  background-color: rgb(0 0 0 / 0.05);
}

.bg-black\/50 {
  background-color: rgb(0 0 0 / 0.5);
}

.bg-black\/60 {
  background-color: rgb(0 0 0 / 0.6);
}

.bg-black\/70 {
  background-color: rgb(0 0 0 / 0.7);
}

.bg-black\/75 {
  background-color: rgb(0 0 0 / 0.75);
}

.bg-black\/80 {
  background-color: rgb(0 0 0 / 0.8);
}

.bg-black\/90 {
  background-color: rgb(0 0 0 / 0.9);
}

.bg-black\/95 {
  background-color: rgb(0 0 0 / 0.95);
}

.bg-blue-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(219 234 254 / var(--tw-bg-opacity));
}

.bg-blue-100\/0 {
  background-color: rgb(219 234 254 / 0);
}

.bg-blue-100\/10 {
  background-color: rgb(219 234 254 / 0.1);
}

.bg-blue-100\/100 {
  background-color: rgb(219 234 254 / 1);
}

.bg-blue-100\/20 {
  background-color: rgb(219 234 254 / 0.2);
}

.bg-blue-100\/25 {
  background-color: rgb(219 234 254 / 0.25);
}

.bg-blue-100\/30 {
  background-color: rgb(219 234 254 / 0.3);
}

.bg-blue-100\/40 {
  background-color: rgb(219 234 254 / 0.4);
}

.bg-blue-100\/5 {
  background-color: rgb(219 234 254 / 0.05);
}

.bg-blue-100\/50 {
  background-color: rgb(219 234 254 / 0.5);
}

.bg-blue-100\/60 {
  background-color: rgb(219 234 254 / 0.6);
}

.bg-blue-100\/70 {
  background-color: rgb(219 234 254 / 0.7);
}

.bg-blue-100\/75 {
  background-color: rgb(219 234 254 / 0.75);
}

.bg-blue-100\/80 {
  background-color: rgb(219 234 254 / 0.8);
}

.bg-blue-100\/90 {
  background-color: rgb(219 234 254 / 0.9);
}

.bg-blue-100\/95 {
  background-color: rgb(219 234 254 / 0.95);
}

.bg-blue-200 {
  --tw-bg-opacity: 1;
  background-color: rgb(191 219 254 / var(--tw-bg-opacity));
}

.bg-blue-200\/0 {
  background-color: rgb(191 219 254 / 0);
}

.bg-blue-200\/10 {
  background-color: rgb(191 219 254 / 0.1);
}

.bg-blue-200\/100 {
  background-color: rgb(191 219 254 / 1);
}

.bg-blue-200\/20 {
  background-color: rgb(191 219 254 / 0.2);
}

.bg-blue-200\/25 {
  background-color: rgb(191 219 254 / 0.25);
}

.bg-blue-200\/30 {
  background-color: rgb(191 219 254 / 0.3);
}

.bg-blue-200\/40 {
  background-color: rgb(191 219 254 / 0.4);
}

.bg-blue-200\/5 {
  background-color: rgb(191 219 254 / 0.05);
}

.bg-blue-200\/50 {
  background-color: rgb(191 219 254 / 0.5);
}

.bg-blue-200\/60 {
  background-color: rgb(191 219 254 / 0.6);
}

.bg-blue-200\/70 {
  background-color: rgb(191 219 254 / 0.7);
}

.bg-blue-200\/75 {
  background-color: rgb(191 219 254 / 0.75);
}

.bg-blue-200\/80 {
  background-color: rgb(191 219 254 / 0.8);
}

.bg-blue-200\/90 {
  background-color: rgb(191 219 254 / 0.9);
}

.bg-blue-200\/95 {
  background-color: rgb(191 219 254 / 0.95);
}

.bg-blue-300 {
  --tw-bg-opacity: 1;
  background-color: rgb(147 197 253 / var(--tw-bg-opacity));
}

.bg-blue-300\/0 {
  background-color: rgb(147 197 253 / 0);
}

.bg-blue-300\/10 {
  background-color: rgb(147 197 253 / 0.1);
}

.bg-blue-300\/100 {
  background-color: rgb(147 197 253 / 1);
}

.bg-blue-300\/20 {
  background-color: rgb(147 197 253 / 0.2);
}

.bg-blue-300\/25 {
  background-color: rgb(147 197 253 / 0.25);
}

.bg-blue-300\/30 {
  background-color: rgb(147 197 253 / 0.3);
}

.bg-blue-300\/40 {
  background-color: rgb(147 197 253 / 0.4);
}

.bg-blue-300\/5 {
  background-color: rgb(147 197 253 / 0.05);
}

.bg-blue-300\/50 {
  background-color: rgb(147 197 253 / 0.5);
}

.bg-blue-300\/60 {
  background-color: rgb(147 197 253 / 0.6);
}

.bg-blue-300\/70 {
  background-color: rgb(147 197 253 / 0.7);
}

.bg-blue-300\/75 {
  background-color: rgb(147 197 253 / 0.75);
}

.bg-blue-300\/80 {
  background-color: rgb(147 197 253 / 0.8);
}

.bg-blue-300\/90 {
  background-color: rgb(147 197 253 / 0.9);
}

.bg-blue-300\/95 {
  background-color: rgb(147 197 253 / 0.95);
}

.bg-blue-400 {
  --tw-bg-opacity: 1;
  background-color: rgb(96 165 250 / var(--tw-bg-opacity));
}

.bg-blue-400\/0 {
  background-color: rgb(96 165 250 / 0);
}

.bg-blue-400\/10 {
  background-color: rgb(96 165 250 / 0.1);
}

.bg-blue-400\/100 {
  background-color: rgb(96 165 250 / 1);
}

.bg-blue-400\/20 {
  background-color: rgb(96 165 250 / 0.2);
}

.bg-blue-400\/25 {
  background-color: rgb(96 165 250 / 0.25);
}

.bg-blue-400\/30 {
  background-color: rgb(96 165 250 / 0.3);
}

.bg-blue-400\/40 {
  background-color: rgb(96 165 250 / 0.4);
}

.bg-blue-400\/5 {
  background-color: rgb(96 165 250 / 0.05);
}

.bg-blue-400\/50 {
  background-color: rgb(96 165 250 / 0.5);
}

.bg-blue-400\/60 {
  background-color: rgb(96 165 250 / 0.6);
}

.bg-blue-400\/70 {
  background-color: rgb(96 165 250 / 0.7);
}

.bg-blue-400\/75 {
  background-color: rgb(96 165 250 / 0.75);
}

.bg-blue-400\/80 {
  background-color: rgb(96 165 250 / 0.8);
}

.bg-blue-400\/90 {
  background-color: rgb(96 165 250 / 0.9);
}

.bg-blue-400\/95 {
  background-color: rgb(96 165 250 / 0.95);
}

.bg-blue-50 {
  --tw-bg-opacity: 1;
  background-color: rgb(239 246 255 / var(--tw-bg-opacity));
}

.bg-blue-50\/0 {
  background-color: rgb(239 246 255 / 0);
}

.bg-blue-50\/10 {
  background-color: rgb(239 246 255 / 0.1);
}

.bg-blue-50\/100 {
  background-color: rgb(239 246 255 / 1);
}

.bg-blue-50\/20 {
  background-color: rgb(239 246 255 / 0.2);
}

.bg-blue-50\/25 {
  background-color: rgb(239 246 255 / 0.25);
}

.bg-blue-50\/30 {
  background-color: rgb(239 246 255 / 0.3);
}

.bg-blue-50\/40 {
  background-color: rgb(239 246 255 / 0.4);
}

.bg-blue-50\/5 {
  background-color: rgb(239 246 255 / 0.05);
}

.bg-blue-50\/50 {
  background-color: rgb(239 246 255 / 0.5);
}

.bg-blue-50\/60 {
  background-color: rgb(239 246 255 / 0.6);
}

.bg-blue-50\/70 {
  background-color: rgb(239 246 255 / 0.7);
}

.bg-blue-50\/75 {
  background-color: rgb(239 246 255 / 0.75);
}

.bg-blue-50\/80 {
  background-color: rgb(239 246 255 / 0.8);
}

.bg-blue-50\/90 {
  background-color: rgb(239 246 255 / 0.9);
}

.bg-blue-50\/95 {
  background-color: rgb(239 246 255 / 0.95);
}

.bg-blue-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(59 130 246 / var(--tw-bg-opacity));
}

.bg-blue-500\/0 {
  background-color: rgb(59 130 246 / 0);
}

.bg-blue-500\/10 {
  background-color: rgb(59 130 246 / 0.1);
}

.bg-blue-500\/100 {
  background-color: rgb(59 130 246 / 1);
}

.bg-blue-500\/20 {
  background-color: rgb(59 130 246 / 0.2);
}

.bg-blue-500\/25 {
  background-color: rgb(59 130 246 / 0.25);
}

.bg-blue-500\/30 {
  background-color: rgb(59 130 246 / 0.3);
}

.bg-blue-500\/40 {
  background-color: rgb(59 130 246 / 0.4);
}

.bg-blue-500\/5 {
  background-color: rgb(59 130 246 / 0.05);
}

.bg-blue-500\/50 {
  background-color: rgb(59 130 246 / 0.5);
}

.bg-blue-500\/60 {
  background-color: rgb(59 130 246 / 0.6);
}

.bg-blue-500\/70 {
  background-color: rgb(59 130 246 / 0.7);
}

.bg-blue-500\/75 {
  background-color: rgb(59 130 246 / 0.75);
}

.bg-blue-500\/80 {
  background-color: rgb(59 130 246 / 0.8);
}

.bg-blue-500\/90 {
  background-color: rgb(59 130 246 / 0.9);
}

.bg-blue-500\/95 {
  background-color: rgb(59 130 246 / 0.95);
}

.bg-blue-600 {
  --tw-bg-opacity: 1;
  background-color: rgb(37 99 235 / var(--tw-bg-opacity));
}

.bg-blue-600\/0 {
  background-color: rgb(37 99 235 / 0);
}

.bg-blue-600\/10 {
  background-color: rgb(37 99 235 / 0.1);
}

.bg-blue-600\/100 {
  background-color: rgb(37 99 235 / 1);
}

.bg-blue-600\/20 {
  background-color: rgb(37 99 235 / 0.2);
}

.bg-blue-600\/25 {
  background-color: rgb(37 99 235 / 0.25);
}

.bg-blue-600\/30 {
  background-color: rgb(37 99 235 / 0.3);
}

.bg-blue-600\/40 {
  background-color: rgb(37 99 235 / 0.4);
}

.bg-blue-600\/5 {
  background-color: rgb(37 99 235 / 0.05);
}

.bg-blue-600\/50 {
  background-color: rgb(37 99 235 / 0.5);
}

.bg-blue-600\/60 {
  background-color: rgb(37 99 235 / 0.6);
}

.bg-blue-600\/70 {
  background-color: rgb(37 99 235 / 0.7);
}

.bg-blue-600\/75 {
  background-color: rgb(37 99 235 / 0.75);
}

.bg-blue-600\/80 {
  background-color: rgb(37 99 235 / 0.8);
}

.bg-blue-600\/90 {
  background-color: rgb(37 99 235 / 0.9);
}

.bg-blue-600\/95 {
  background-color: rgb(37 99 235 / 0.95);
}

.bg-blue-700 {
  --tw-bg-opacity: 1;
  background-color: rgb(29 78 216 / var(--tw-bg-opacity));
}

.bg-blue-700\/0 {
  background-color: rgb(29 78 216 / 0);
}

.bg-blue-700\/10 {
  background-color: rgb(29 78 216 / 0.1);
}

.bg-blue-700\/100 {
  background-color: rgb(29 78 216 / 1);
}

.bg-blue-700\/20 {
  background-color: rgb(29 78 216 / 0.2);
}

.bg-blue-700\/25 {
  background-color: rgb(29 78 216 / 0.25);
}

.bg-blue-700\/30 {
  background-color: rgb(29 78 216 / 0.3);
}

.bg-blue-700\/40 {
  background-color: rgb(29 78 216 / 0.4);
}

.bg-blue-700\/5 {
  background-color: rgb(29 78 216 / 0.05);
}

.bg-blue-700\/50 {
  background-color: rgb(29 78 216 / 0.5);
}

.bg-blue-700\/60 {
  background-color: rgb(29 78 216 / 0.6);
}

.bg-blue-700\/70 {
  background-color: rgb(29 78 216 / 0.7);
}

.bg-blue-700\/75 {
  background-color: rgb(29 78 216 / 0.75);
}

.bg-blue-700\/80 {
  background-color: rgb(29 78 216 / 0.8);
}

.bg-blue-700\/90 {
  background-color: rgb(29 78 216 / 0.9);
}

.bg-blue-700\/95 {
  background-color: rgb(29 78 216 / 0.95);
}

.bg-blue-800 {
  --tw-bg-opacity: 1;
  background-color: rgb(30 64 175 / var(--tw-bg-opacity));
}

.bg-blue-800\/0 {
  background-color: rgb(30 64 175 / 0);
}

.bg-blue-800\/10 {
  background-color: rgb(30 64 175 / 0.1);
}

.bg-blue-800\/100 {
  background-color: rgb(30 64 175 / 1);
}

.bg-blue-800\/20 {
  background-color: rgb(30 64 175 / 0.2);
}

.bg-blue-800\/25 {
  background-color: rgb(30 64 175 / 0.25);
}

.bg-blue-800\/30 {
  background-color: rgb(30 64 175 / 0.3);
}

.bg-blue-800\/40 {
  background-color: rgb(30 64 175 / 0.4);
}

.bg-blue-800\/5 {
  background-color: rgb(30 64 175 / 0.05);
}

.bg-blue-800\/50 {
  background-color: rgb(30 64 175 / 0.5);
}

.bg-blue-800\/60 {
  background-color: rgb(30 64 175 / 0.6);
}

.bg-blue-800\/70 {
  background-color: rgb(30 64 175 / 0.7);
}

.bg-blue-800\/75 {
  background-color: rgb(30 64 175 / 0.75);
}

.bg-blue-800\/80 {
  background-color: rgb(30 64 175 / 0.8);
}

.bg-blue-800\/90 {
  background-color: rgb(30 64 175 / 0.9);
}

.bg-blue-800\/95 {
  background-color: rgb(30 64 175 / 0.95);
}

.bg-blue-900 {
  --tw-bg-opacity: 1;
  background-color: rgb(30 58 138 / var(--tw-bg-opacity));
}

.bg-blue-900\/0 {
  background-color: rgb(30 58 138 / 0);
}

.bg-blue-900\/10 {
  background-color: rgb(30 58 138 / 0.1);
}

.bg-blue-900\/100 {
  background-color: rgb(30 58 138 / 1);
}

.bg-blue-900\/20 {
  background-color: rgb(30 58 138 / 0.2);
}

.bg-blue-900\/25 {
  background-color: rgb(30 58 138 / 0.25);
}

.bg-blue-900\/30 {
  background-color: rgb(30 58 138 / 0.3);
}

.bg-blue-900\/40 {
  background-color: rgb(30 58 138 / 0.4);
}

.bg-blue-900\/5 {
  background-color: rgb(30 58 138 / 0.05);
}

.bg-blue-900\/50 {
  background-color: rgb(30 58 138 / 0.5);
}

.bg-blue-900\/60 {
  background-color: rgb(30 58 138 / 0.6);
}

.bg-blue-900\/70 {
  background-color: rgb(30 58 138 / 0.7);
}

.bg-blue-900\/75 {
  background-color: rgb(30 58 138 / 0.75);
}

.bg-blue-900\/80 {
  background-color: rgb(30 58 138 / 0.8);
}

.bg-blue-900\/90 {
  background-color: rgb(30 58 138 / 0.9);
}

.bg-blue-900\/95 {
  background-color: rgb(30 58 138 / 0.95);
}

.bg-blue-950 {
  --tw-bg-opacity: 1;
  background-color: rgb(23 37 84 / var(--tw-bg-opacity));
}

.bg-blue-950\/0 {
  background-color: rgb(23 37 84 / 0);
}

.bg-blue-950\/10 {
  background-color: rgb(23 37 84 / 0.1);
}

.bg-blue-950\/100 {
  background-color: rgb(23 37 84 / 1);
}

.bg-blue-950\/20 {
  background-color: rgb(23 37 84 / 0.2);
}

.bg-blue-950\/25 {
  background-color: rgb(23 37 84 / 0.25);
}

.bg-blue-950\/30 {
  background-color: rgb(23 37 84 / 0.3);
}

.bg-blue-950\/40 {
  background-color: rgb(23 37 84 / 0.4);
}

.bg-blue-950\/5 {
  background-color: rgb(23 37 84 / 0.05);
}

.bg-blue-950\/50 {
  background-color: rgb(23 37 84 / 0.5);
}

.bg-blue-950\/60 {
  background-color: rgb(23 37 84 / 0.6);
}

.bg-blue-950\/70 {
  background-color: rgb(23 37 84 / 0.7);
}

.bg-blue-950\/75 {
  background-color: rgb(23 37 84 / 0.75);
}

.bg-blue-950\/80 {
  background-color: rgb(23 37 84 / 0.8);
}

.bg-blue-950\/90 {
  background-color: rgb(23 37 84 / 0.9);
}

.bg-blue-950\/95 {
  background-color: rgb(23 37 84 / 0.95);
}

.bg-crossbrand-alpha00 {
  background-color: transparent;
}

.bg-crossbrand-alpha00\/0 {
  background-color: rgb(0 0 0 / 0);
}

.bg-crossbrand-alpha00\/10 {
  background-color: rgb(0 0 0 / 0.1);
}

.bg-crossbrand-alpha00\/100 {
  background-color: rgb(0 0 0 / 1);
}

.bg-crossbrand-alpha00\/20 {
  background-color: rgb(0 0 0 / 0.2);
}

.bg-crossbrand-alpha00\/25 {
  background-color: rgb(0 0 0 / 0.25);
}

.bg-crossbrand-alpha00\/30 {
  background-color: rgb(0 0 0 / 0.3);
}

.bg-crossbrand-alpha00\/40 {
  background-color: rgb(0 0 0 / 0.4);
}

.bg-crossbrand-alpha00\/5 {
  background-color: rgb(0 0 0 / 0.05);
}

.bg-crossbrand-alpha00\/50 {
  background-color: rgb(0 0 0 / 0.5);
}

.bg-crossbrand-alpha00\/60 {
  background-color: rgb(0 0 0 / 0.6);
}

.bg-crossbrand-alpha00\/70 {
  background-color: rgb(0 0 0 / 0.7);
}

.bg-crossbrand-alpha00\/75 {
  background-color: rgb(0 0 0 / 0.75);
}

.bg-crossbrand-alpha00\/80 {
  background-color: rgb(0 0 0 / 0.8);
}

.bg-crossbrand-alpha00\/90 {
  background-color: rgb(0 0 0 / 0.9);
}

.bg-crossbrand-alpha00\/95 {
  background-color: rgb(0 0 0 / 0.95);
}

.bg-crossbrand-b1 {
  --tw-bg-opacity: 1;
  background-color: rgb(4 102 202 / var(--tw-bg-opacity));
}

.bg-crossbrand-b1\/0 {
  background-color: rgb(4 102 202 / 0);
}

.bg-crossbrand-b1\/10 {
  background-color: rgb(4 102 202 / 0.1);
}

.bg-crossbrand-b1\/100 {
  background-color: rgb(4 102 202 / 1);
}

.bg-crossbrand-b1\/20 {
  background-color: rgb(4 102 202 / 0.2);
}

.bg-crossbrand-b1\/25 {
  background-color: rgb(4 102 202 / 0.25);
}

.bg-crossbrand-b1\/30 {
  background-color: rgb(4 102 202 / 0.3);
}

.bg-crossbrand-b1\/40 {
  background-color: rgb(4 102 202 / 0.4);
}

.bg-crossbrand-b1\/5 {
  background-color: rgb(4 102 202 / 0.05);
}

.bg-crossbrand-b1\/50 {
  background-color: rgb(4 102 202 / 0.5);
}

.bg-crossbrand-b1\/60 {
  background-color: rgb(4 102 202 / 0.6);
}

.bg-crossbrand-b1\/70 {
  background-color: rgb(4 102 202 / 0.7);
}

.bg-crossbrand-b1\/75 {
  background-color: rgb(4 102 202 / 0.75);
}

.bg-crossbrand-b1\/80 {
  background-color: rgb(4 102 202 / 0.8);
}

.bg-crossbrand-b1\/90 {
  background-color: rgb(4 102 202 / 0.9);
}

.bg-crossbrand-b1\/95 {
  background-color: rgb(4 102 202 / 0.95);
}

.bg-crossbrand-b2 {
  --tw-bg-opacity: 1;
  background-color: rgb(92 171 247 / var(--tw-bg-opacity));
}

.bg-crossbrand-b2\/0 {
  background-color: rgb(92 171 247 / 0);
}

.bg-crossbrand-b2\/10 {
  background-color: rgb(92 171 247 / 0.1);
}

.bg-crossbrand-b2\/100 {
  background-color: rgb(92 171 247 / 1);
}

.bg-crossbrand-b2\/20 {
  background-color: rgb(92 171 247 / 0.2);
}

.bg-crossbrand-b2\/25 {
  background-color: rgb(92 171 247 / 0.25);
}

.bg-crossbrand-b2\/30 {
  background-color: rgb(92 171 247 / 0.3);
}

.bg-crossbrand-b2\/40 {
  background-color: rgb(92 171 247 / 0.4);
}

.bg-crossbrand-b2\/5 {
  background-color: rgb(92 171 247 / 0.05);
}

.bg-crossbrand-b2\/50 {
  background-color: rgb(92 171 247 / 0.5);
}

.bg-crossbrand-b2\/60 {
  background-color: rgb(92 171 247 / 0.6);
}

.bg-crossbrand-b2\/70 {
  background-color: rgb(92 171 247 / 0.7);
}

.bg-crossbrand-b2\/75 {
  background-color: rgb(92 171 247 / 0.75);
}

.bg-crossbrand-b2\/80 {
  background-color: rgb(92 171 247 / 0.8);
}

.bg-crossbrand-b2\/90 {
  background-color: rgb(92 171 247 / 0.9);
}

.bg-crossbrand-b2\/95 {
  background-color: rgb(92 171 247 / 0.95);
}

.bg-crossbrand-bk {
  --tw-bg-opacity: 1;
  background-color: rgb(0 0 0 / var(--tw-bg-opacity));
}

.bg-crossbrand-bk\/0 {
  background-color: rgb(0 0 0 / 0);
}

.bg-crossbrand-bk\/10 {
  background-color: rgb(0 0 0 / 0.1);
}

.bg-crossbrand-bk\/100 {
  background-color: rgb(0 0 0 / 1);
}

.bg-crossbrand-bk\/20 {
  background-color: rgb(0 0 0 / 0.2);
}

.bg-crossbrand-bk\/25 {
  background-color: rgb(0 0 0 / 0.25);
}

.bg-crossbrand-bk\/30 {
  background-color: rgb(0 0 0 / 0.3);
}

.bg-crossbrand-bk\/40 {
  background-color: rgb(0 0 0 / 0.4);
}

.bg-crossbrand-bk\/5 {
  background-color: rgb(0 0 0 / 0.05);
}

.bg-crossbrand-bk\/50 {
  background-color: rgb(0 0 0 / 0.5);
}

.bg-crossbrand-bk\/60 {
  background-color: rgb(0 0 0 / 0.6);
}

.bg-crossbrand-bk\/70 {
  background-color: rgb(0 0 0 / 0.7);
}

.bg-crossbrand-bk\/75 {
  background-color: rgb(0 0 0 / 0.75);
}

.bg-crossbrand-bk\/80 {
  background-color: rgb(0 0 0 / 0.8);
}

.bg-crossbrand-bk\/90 {
  background-color: rgb(0 0 0 / 0.9);
}

.bg-crossbrand-bk\/95 {
  background-color: rgb(0 0 0 / 0.95);
}

.bg-crossbrand-err1 {
  --tw-bg-opacity: 1;
  background-color: rgb(208 0 0 / var(--tw-bg-opacity));
}

.bg-crossbrand-err1\/0 {
  background-color: rgb(208 0 0 / 0);
}

.bg-crossbrand-err1\/10 {
  background-color: rgb(208 0 0 / 0.1);
}

.bg-crossbrand-err1\/100 {
  background-color: rgb(208 0 0 / 1);
}

.bg-crossbrand-err1\/20 {
  background-color: rgb(208 0 0 / 0.2);
}

.bg-crossbrand-err1\/25 {
  background-color: rgb(208 0 0 / 0.25);
}

.bg-crossbrand-err1\/30 {
  background-color: rgb(208 0 0 / 0.3);
}

.bg-crossbrand-err1\/40 {
  background-color: rgb(208 0 0 / 0.4);
}

.bg-crossbrand-err1\/5 {
  background-color: rgb(208 0 0 / 0.05);
}

.bg-crossbrand-err1\/50 {
  background-color: rgb(208 0 0 / 0.5);
}

.bg-crossbrand-err1\/60 {
  background-color: rgb(208 0 0 / 0.6);
}

.bg-crossbrand-err1\/70 {
  background-color: rgb(208 0 0 / 0.7);
}

.bg-crossbrand-err1\/75 {
  background-color: rgb(208 0 0 / 0.75);
}

.bg-crossbrand-err1\/80 {
  background-color: rgb(208 0 0 / 0.8);
}

.bg-crossbrand-err1\/90 {
  background-color: rgb(208 0 0 / 0.9);
}

.bg-crossbrand-err1\/95 {
  background-color: rgb(208 0 0 / 0.95);
}

.bg-crossbrand-g1 {
  --tw-bg-opacity: 1;
  background-color: rgb(51 51 51 / var(--tw-bg-opacity));
}

.bg-crossbrand-g1\/0 {
  background-color: rgb(51 51 51 / 0);
}

.bg-crossbrand-g1\/10 {
  background-color: rgb(51 51 51 / 0.1);
}

.bg-crossbrand-g1\/100 {
  background-color: rgb(51 51 51 / 1);
}

.bg-crossbrand-g1\/20 {
  background-color: rgb(51 51 51 / 0.2);
}

.bg-crossbrand-g1\/25 {
  background-color: rgb(51 51 51 / 0.25);
}

.bg-crossbrand-g1\/30 {
  background-color: rgb(51 51 51 / 0.3);
}

.bg-crossbrand-g1\/40 {
  background-color: rgb(51 51 51 / 0.4);
}

.bg-crossbrand-g1\/5 {
  background-color: rgb(51 51 51 / 0.05);
}

.bg-crossbrand-g1\/50 {
  background-color: rgb(51 51 51 / 0.5);
}

.bg-crossbrand-g1\/60 {
  background-color: rgb(51 51 51 / 0.6);
}

.bg-crossbrand-g1\/70 {
  background-color: rgb(51 51 51 / 0.7);
}

.bg-crossbrand-g1\/75 {
  background-color: rgb(51 51 51 / 0.75);
}

.bg-crossbrand-g1\/80 {
  background-color: rgb(51 51 51 / 0.8);
}

.bg-crossbrand-g1\/90 {
  background-color: rgb(51 51 51 / 0.9);
}

.bg-crossbrand-g1\/95 {
  background-color: rgb(51 51 51 / 0.95);
}

.bg-crossbrand-g2 {
  --tw-bg-opacity: 1;
  background-color: rgb(102 102 102 / var(--tw-bg-opacity));
}

.bg-crossbrand-g2\/0 {
  background-color: rgb(102 102 102 / 0);
}

.bg-crossbrand-g2\/10 {
  background-color: rgb(102 102 102 / 0.1);
}

.bg-crossbrand-g2\/100 {
  background-color: rgb(102 102 102 / 1);
}

.bg-crossbrand-g2\/20 {
  background-color: rgb(102 102 102 / 0.2);
}

.bg-crossbrand-g2\/25 {
  background-color: rgb(102 102 102 / 0.25);
}

.bg-crossbrand-g2\/30 {
  background-color: rgb(102 102 102 / 0.3);
}

.bg-crossbrand-g2\/40 {
  background-color: rgb(102 102 102 / 0.4);
}

.bg-crossbrand-g2\/5 {
  background-color: rgb(102 102 102 / 0.05);
}

.bg-crossbrand-g2\/50 {
  background-color: rgb(102 102 102 / 0.5);
}

.bg-crossbrand-g2\/60 {
  background-color: rgb(102 102 102 / 0.6);
}

.bg-crossbrand-g2\/70 {
  background-color: rgb(102 102 102 / 0.7);
}

.bg-crossbrand-g2\/75 {
  background-color: rgb(102 102 102 / 0.75);
}

.bg-crossbrand-g2\/80 {
  background-color: rgb(102 102 102 / 0.8);
}

.bg-crossbrand-g2\/90 {
  background-color: rgb(102 102 102 / 0.9);
}

.bg-crossbrand-g2\/95 {
  background-color: rgb(102 102 102 / 0.95);
}

.bg-crossbrand-g3 {
  --tw-bg-opacity: 1;
  background-color: rgb(153 153 153 / var(--tw-bg-opacity));
}

.bg-crossbrand-g3\/0 {
  background-color: rgb(153 153 153 / 0);
}

.bg-crossbrand-g3\/10 {
  background-color: rgb(153 153 153 / 0.1);
}

.bg-crossbrand-g3\/100 {
  background-color: rgb(153 153 153 / 1);
}

.bg-crossbrand-g3\/20 {
  background-color: rgb(153 153 153 / 0.2);
}

.bg-crossbrand-g3\/25 {
  background-color: rgb(153 153 153 / 0.25);
}

.bg-crossbrand-g3\/30 {
  background-color: rgb(153 153 153 / 0.3);
}

.bg-crossbrand-g3\/40 {
  background-color: rgb(153 153 153 / 0.4);
}

.bg-crossbrand-g3\/5 {
  background-color: rgb(153 153 153 / 0.05);
}

.bg-crossbrand-g3\/50 {
  background-color: rgb(153 153 153 / 0.5);
}

.bg-crossbrand-g3\/60 {
  background-color: rgb(153 153 153 / 0.6);
}

.bg-crossbrand-g3\/70 {
  background-color: rgb(153 153 153 / 0.7);
}

.bg-crossbrand-g3\/75 {
  background-color: rgb(153 153 153 / 0.75);
}

.bg-crossbrand-g3\/80 {
  background-color: rgb(153 153 153 / 0.8);
}

.bg-crossbrand-g3\/90 {
  background-color: rgb(153 153 153 / 0.9);
}

.bg-crossbrand-g3\/95 {
  background-color: rgb(153 153 153 / 0.95);
}

.bg-crossbrand-g4 {
  --tw-bg-opacity: 1;
  background-color: rgb(204 204 204 / var(--tw-bg-opacity));
}

.bg-crossbrand-g4\/0 {
  background-color: rgb(204 204 204 / 0);
}

.bg-crossbrand-g4\/10 {
  background-color: rgb(204 204 204 / 0.1);
}

.bg-crossbrand-g4\/100 {
  background-color: rgb(204 204 204 / 1);
}

.bg-crossbrand-g4\/20 {
  background-color: rgb(204 204 204 / 0.2);
}

.bg-crossbrand-g4\/25 {
  background-color: rgb(204 204 204 / 0.25);
}

.bg-crossbrand-g4\/30 {
  background-color: rgb(204 204 204 / 0.3);
}

.bg-crossbrand-g4\/40 {
  background-color: rgb(204 204 204 / 0.4);
}

.bg-crossbrand-g4\/5 {
  background-color: rgb(204 204 204 / 0.05);
}

.bg-crossbrand-g4\/50 {
  background-color: rgb(204 204 204 / 0.5);
}

.bg-crossbrand-g4\/60 {
  background-color: rgb(204 204 204 / 0.6);
}

.bg-crossbrand-g4\/70 {
  background-color: rgb(204 204 204 / 0.7);
}

.bg-crossbrand-g4\/75 {
  background-color: rgb(204 204 204 / 0.75);
}

.bg-crossbrand-g4\/80 {
  background-color: rgb(204 204 204 / 0.8);
}

.bg-crossbrand-g4\/90 {
  background-color: rgb(204 204 204 / 0.9);
}

.bg-crossbrand-g4\/95 {
  background-color: rgb(204 204 204 / 0.95);
}

.bg-crossbrand-g5 {
  --tw-bg-opacity: 1;
  background-color: rgb(242 242 242 / var(--tw-bg-opacity));
}

.bg-crossbrand-g5\/0 {
  background-color: rgb(242 242 242 / 0);
}

.bg-crossbrand-g5\/10 {
  background-color: rgb(242 242 242 / 0.1);
}

.bg-crossbrand-g5\/100 {
  background-color: rgb(242 242 242 / 1);
}

.bg-crossbrand-g5\/20 {
  background-color: rgb(242 242 242 / 0.2);
}

.bg-crossbrand-g5\/25 {
  background-color: rgb(242 242 242 / 0.25);
}

.bg-crossbrand-g5\/30 {
  background-color: rgb(242 242 242 / 0.3);
}

.bg-crossbrand-g5\/40 {
  background-color: rgb(242 242 242 / 0.4);
}

.bg-crossbrand-g5\/5 {
  background-color: rgb(242 242 242 / 0.05);
}

.bg-crossbrand-g5\/50 {
  background-color: rgb(242 242 242 / 0.5);
}

.bg-crossbrand-g5\/60 {
  background-color: rgb(242 242 242 / 0.6);
}

.bg-crossbrand-g5\/70 {
  background-color: rgb(242 242 242 / 0.7);
}

.bg-crossbrand-g5\/75 {
  background-color: rgb(242 242 242 / 0.75);
}

.bg-crossbrand-g5\/80 {
  background-color: rgb(242 242 242 / 0.8);
}

.bg-crossbrand-g5\/90 {
  background-color: rgb(242 242 242 / 0.9);
}

.bg-crossbrand-g5\/95 {
  background-color: rgb(242 242 242 / 0.95);
}

.bg-crossbrand-g6 {
  --tw-bg-opacity: 1;
  background-color: rgb(238 238 238 / var(--tw-bg-opacity));
}

.bg-crossbrand-g6\/0 {
  background-color: rgb(238 238 238 / 0);
}

.bg-crossbrand-g6\/10 {
  background-color: rgb(238 238 238 / 0.1);
}

.bg-crossbrand-g6\/100 {
  background-color: rgb(238 238 238 / 1);
}

.bg-crossbrand-g6\/20 {
  background-color: rgb(238 238 238 / 0.2);
}

.bg-crossbrand-g6\/25 {
  background-color: rgb(238 238 238 / 0.25);
}

.bg-crossbrand-g6\/30 {
  background-color: rgb(238 238 238 / 0.3);
}

.bg-crossbrand-g6\/40 {
  background-color: rgb(238 238 238 / 0.4);
}

.bg-crossbrand-g6\/5 {
  background-color: rgb(238 238 238 / 0.05);
}

.bg-crossbrand-g6\/50 {
  background-color: rgb(238 238 238 / 0.5);
}

.bg-crossbrand-g6\/60 {
  background-color: rgb(238 238 238 / 0.6);
}

.bg-crossbrand-g6\/70 {
  background-color: rgb(238 238 238 / 0.7);
}

.bg-crossbrand-g6\/75 {
  background-color: rgb(238 238 238 / 0.75);
}

.bg-crossbrand-g6\/80 {
  background-color: rgb(238 238 238 / 0.8);
}

.bg-crossbrand-g6\/90 {
  background-color: rgb(238 238 238 / 0.9);
}

.bg-crossbrand-g6\/95 {
  background-color: rgb(238 238 238 / 0.95);
}

.bg-crossbrand-inf1 {
  --tw-bg-opacity: 1;
  background-color: rgb(92 171 247 / var(--tw-bg-opacity));
}

.bg-crossbrand-inf1\/0 {
  background-color: rgb(92 171 247 / 0);
}

.bg-crossbrand-inf1\/10 {
  background-color: rgb(92 171 247 / 0.1);
}

.bg-crossbrand-inf1\/100 {
  background-color: rgb(92 171 247 / 1);
}

.bg-crossbrand-inf1\/20 {
  background-color: rgb(92 171 247 / 0.2);
}

.bg-crossbrand-inf1\/25 {
  background-color: rgb(92 171 247 / 0.25);
}

.bg-crossbrand-inf1\/30 {
  background-color: rgb(92 171 247 / 0.3);
}

.bg-crossbrand-inf1\/40 {
  background-color: rgb(92 171 247 / 0.4);
}

.bg-crossbrand-inf1\/5 {
  background-color: rgb(92 171 247 / 0.05);
}

.bg-crossbrand-inf1\/50 {
  background-color: rgb(92 171 247 / 0.5);
}

.bg-crossbrand-inf1\/60 {
  background-color: rgb(92 171 247 / 0.6);
}

.bg-crossbrand-inf1\/70 {
  background-color: rgb(92 171 247 / 0.7);
}

.bg-crossbrand-inf1\/75 {
  background-color: rgb(92 171 247 / 0.75);
}

.bg-crossbrand-inf1\/80 {
  background-color: rgb(92 171 247 / 0.8);
}

.bg-crossbrand-inf1\/90 {
  background-color: rgb(92 171 247 / 0.9);
}

.bg-crossbrand-inf1\/95 {
  background-color: rgb(92 171 247 / 0.95);
}

.bg-crossbrand-inverse-b1 {
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity));
}

.bg-crossbrand-inverse-b1\/0 {
  background-color: rgb(255 255 255 / 0);
}

.bg-crossbrand-inverse-b1\/10 {
  background-color: rgb(255 255 255 / 0.1);
}

.bg-crossbrand-inverse-b1\/100 {
  background-color: rgb(255 255 255 / 1);
}

.bg-crossbrand-inverse-b1\/20 {
  background-color: rgb(255 255 255 / 0.2);
}

.bg-crossbrand-inverse-b1\/25 {
  background-color: rgb(255 255 255 / 0.25);
}

.bg-crossbrand-inverse-b1\/30 {
  background-color: rgb(255 255 255 / 0.3);
}

.bg-crossbrand-inverse-b1\/40 {
  background-color: rgb(255 255 255 / 0.4);
}

.bg-crossbrand-inverse-b1\/5 {
  background-color: rgb(255 255 255 / 0.05);
}

.bg-crossbrand-inverse-b1\/50 {
  background-color: rgb(255 255 255 / 0.5);
}

.bg-crossbrand-inverse-b1\/60 {
  background-color: rgb(255 255 255 / 0.6);
}

.bg-crossbrand-inverse-b1\/70 {
  background-color: rgb(255 255 255 / 0.7);
}

.bg-crossbrand-inverse-b1\/75 {
  background-color: rgb(255 255 255 / 0.75);
}

.bg-crossbrand-inverse-b1\/80 {
  background-color: rgb(255 255 255 / 0.8);
}

.bg-crossbrand-inverse-b1\/90 {
  background-color: rgb(255 255 255 / 0.9);
}

.bg-crossbrand-inverse-b1\/95 {
  background-color: rgb(255 255 255 / 0.95);
}

.bg-crossbrand-inverse-b2 {
  --tw-bg-opacity: 1;
  background-color: rgb(204 204 204 / var(--tw-bg-opacity));
}

.bg-crossbrand-inverse-b2\/0 {
  background-color: rgb(204 204 204 / 0);
}

.bg-crossbrand-inverse-b2\/10 {
  background-color: rgb(204 204 204 / 0.1);
}

.bg-crossbrand-inverse-b2\/100 {
  background-color: rgb(204 204 204 / 1);
}

.bg-crossbrand-inverse-b2\/20 {
  background-color: rgb(204 204 204 / 0.2);
}

.bg-crossbrand-inverse-b2\/25 {
  background-color: rgb(204 204 204 / 0.25);
}

.bg-crossbrand-inverse-b2\/30 {
  background-color: rgb(204 204 204 / 0.3);
}

.bg-crossbrand-inverse-b2\/40 {
  background-color: rgb(204 204 204 / 0.4);
}

.bg-crossbrand-inverse-b2\/5 {
  background-color: rgb(204 204 204 / 0.05);
}

.bg-crossbrand-inverse-b2\/50 {
  background-color: rgb(204 204 204 / 0.5);
}

.bg-crossbrand-inverse-b2\/60 {
  background-color: rgb(204 204 204 / 0.6);
}

.bg-crossbrand-inverse-b2\/70 {
  background-color: rgb(204 204 204 / 0.7);
}

.bg-crossbrand-inverse-b2\/75 {
  background-color: rgb(204 204 204 / 0.75);
}

.bg-crossbrand-inverse-b2\/80 {
  background-color: rgb(204 204 204 / 0.8);
}

.bg-crossbrand-inverse-b2\/90 {
  background-color: rgb(204 204 204 / 0.9);
}

.bg-crossbrand-inverse-b2\/95 {
  background-color: rgb(204 204 204 / 0.95);
}

.bg-crossbrand-inverse-err1 {
  --tw-bg-opacity: 1;
  background-color: rgb(253 101 101 / var(--tw-bg-opacity));
}

.bg-crossbrand-inverse-err1\/0 {
  background-color: rgb(253 101 101 / 0);
}

.bg-crossbrand-inverse-err1\/10 {
  background-color: rgb(253 101 101 / 0.1);
}

.bg-crossbrand-inverse-err1\/100 {
  background-color: rgb(253 101 101 / 1);
}

.bg-crossbrand-inverse-err1\/20 {
  background-color: rgb(253 101 101 / 0.2);
}

.bg-crossbrand-inverse-err1\/25 {
  background-color: rgb(253 101 101 / 0.25);
}

.bg-crossbrand-inverse-err1\/30 {
  background-color: rgb(253 101 101 / 0.3);
}

.bg-crossbrand-inverse-err1\/40 {
  background-color: rgb(253 101 101 / 0.4);
}

.bg-crossbrand-inverse-err1\/5 {
  background-color: rgb(253 101 101 / 0.05);
}

.bg-crossbrand-inverse-err1\/50 {
  background-color: rgb(253 101 101 / 0.5);
}

.bg-crossbrand-inverse-err1\/60 {
  background-color: rgb(253 101 101 / 0.6);
}

.bg-crossbrand-inverse-err1\/70 {
  background-color: rgb(253 101 101 / 0.7);
}

.bg-crossbrand-inverse-err1\/75 {
  background-color: rgb(253 101 101 / 0.75);
}

.bg-crossbrand-inverse-err1\/80 {
  background-color: rgb(253 101 101 / 0.8);
}

.bg-crossbrand-inverse-err1\/90 {
  background-color: rgb(253 101 101 / 0.9);
}

.bg-crossbrand-inverse-err1\/95 {
  background-color: rgb(253 101 101 / 0.95);
}

.bg-crossbrand-inverse-g1 {
  --tw-bg-opacity: 1;
  background-color: rgb(85 85 85 / var(--tw-bg-opacity));
}

.bg-crossbrand-inverse-g1\/0 {
  background-color: rgb(85 85 85 / 0);
}

.bg-crossbrand-inverse-g1\/10 {
  background-color: rgb(85 85 85 / 0.1);
}

.bg-crossbrand-inverse-g1\/100 {
  background-color: rgb(85 85 85 / 1);
}

.bg-crossbrand-inverse-g1\/20 {
  background-color: rgb(85 85 85 / 0.2);
}

.bg-crossbrand-inverse-g1\/25 {
  background-color: rgb(85 85 85 / 0.25);
}

.bg-crossbrand-inverse-g1\/30 {
  background-color: rgb(85 85 85 / 0.3);
}

.bg-crossbrand-inverse-g1\/40 {
  background-color: rgb(85 85 85 / 0.4);
}

.bg-crossbrand-inverse-g1\/5 {
  background-color: rgb(85 85 85 / 0.05);
}

.bg-crossbrand-inverse-g1\/50 {
  background-color: rgb(85 85 85 / 0.5);
}

.bg-crossbrand-inverse-g1\/60 {
  background-color: rgb(85 85 85 / 0.6);
}

.bg-crossbrand-inverse-g1\/70 {
  background-color: rgb(85 85 85 / 0.7);
}

.bg-crossbrand-inverse-g1\/75 {
  background-color: rgb(85 85 85 / 0.75);
}

.bg-crossbrand-inverse-g1\/80 {
  background-color: rgb(85 85 85 / 0.8);
}

.bg-crossbrand-inverse-g1\/90 {
  background-color: rgb(85 85 85 / 0.9);
}

.bg-crossbrand-inverse-g1\/95 {
  background-color: rgb(85 85 85 / 0.95);
}

.bg-crossbrand-inverse-g2 {
  --tw-bg-opacity: 1;
  background-color: rgb(153 153 153 / var(--tw-bg-opacity));
}

.bg-crossbrand-inverse-g2\/0 {
  background-color: rgb(153 153 153 / 0);
}

.bg-crossbrand-inverse-g2\/10 {
  background-color: rgb(153 153 153 / 0.1);
}

.bg-crossbrand-inverse-g2\/100 {
  background-color: rgb(153 153 153 / 1);
}

.bg-crossbrand-inverse-g2\/20 {
  background-color: rgb(153 153 153 / 0.2);
}

.bg-crossbrand-inverse-g2\/25 {
  background-color: rgb(153 153 153 / 0.25);
}

.bg-crossbrand-inverse-g2\/30 {
  background-color: rgb(153 153 153 / 0.3);
}

.bg-crossbrand-inverse-g2\/40 {
  background-color: rgb(153 153 153 / 0.4);
}

.bg-crossbrand-inverse-g2\/5 {
  background-color: rgb(153 153 153 / 0.05);
}

.bg-crossbrand-inverse-g2\/50 {
  background-color: rgb(153 153 153 / 0.5);
}

.bg-crossbrand-inverse-g2\/60 {
  background-color: rgb(153 153 153 / 0.6);
}

.bg-crossbrand-inverse-g2\/70 {
  background-color: rgb(153 153 153 / 0.7);
}

.bg-crossbrand-inverse-g2\/75 {
  background-color: rgb(153 153 153 / 0.75);
}

.bg-crossbrand-inverse-g2\/80 {
  background-color: rgb(153 153 153 / 0.8);
}

.bg-crossbrand-inverse-g2\/90 {
  background-color: rgb(153 153 153 / 0.9);
}

.bg-crossbrand-inverse-g2\/95 {
  background-color: rgb(153 153 153 / 0.95);
}

.bg-crossbrand-inverse-inf1 {
  --tw-bg-opacity: 1;
  background-color: rgb(93 171 245 / var(--tw-bg-opacity));
}

.bg-crossbrand-inverse-inf1\/0 {
  background-color: rgb(93 171 245 / 0);
}

.bg-crossbrand-inverse-inf1\/10 {
  background-color: rgb(93 171 245 / 0.1);
}

.bg-crossbrand-inverse-inf1\/100 {
  background-color: rgb(93 171 245 / 1);
}

.bg-crossbrand-inverse-inf1\/20 {
  background-color: rgb(93 171 245 / 0.2);
}

.bg-crossbrand-inverse-inf1\/25 {
  background-color: rgb(93 171 245 / 0.25);
}

.bg-crossbrand-inverse-inf1\/30 {
  background-color: rgb(93 171 245 / 0.3);
}

.bg-crossbrand-inverse-inf1\/40 {
  background-color: rgb(93 171 245 / 0.4);
}

.bg-crossbrand-inverse-inf1\/5 {
  background-color: rgb(93 171 245 / 0.05);
}

.bg-crossbrand-inverse-inf1\/50 {
  background-color: rgb(93 171 245 / 0.5);
}

.bg-crossbrand-inverse-inf1\/60 {
  background-color: rgb(93 171 245 / 0.6);
}

.bg-crossbrand-inverse-inf1\/70 {
  background-color: rgb(93 171 245 / 0.7);
}

.bg-crossbrand-inverse-inf1\/75 {
  background-color: rgb(93 171 245 / 0.75);
}

.bg-crossbrand-inverse-inf1\/80 {
  background-color: rgb(93 171 245 / 0.8);
}

.bg-crossbrand-inverse-inf1\/90 {
  background-color: rgb(93 171 245 / 0.9);
}

.bg-crossbrand-inverse-inf1\/95 {
  background-color: rgb(93 171 245 / 0.95);
}

.bg-crossbrand-inverse-s1 {
  --tw-bg-opacity: 1;
  background-color: rgb(130 176 93 / var(--tw-bg-opacity));
}

.bg-crossbrand-inverse-s1\/0 {
  background-color: rgb(130 176 93 / 0);
}

.bg-crossbrand-inverse-s1\/10 {
  background-color: rgb(130 176 93 / 0.1);
}

.bg-crossbrand-inverse-s1\/100 {
  background-color: rgb(130 176 93 / 1);
}

.bg-crossbrand-inverse-s1\/20 {
  background-color: rgb(130 176 93 / 0.2);
}

.bg-crossbrand-inverse-s1\/25 {
  background-color: rgb(130 176 93 / 0.25);
}

.bg-crossbrand-inverse-s1\/30 {
  background-color: rgb(130 176 93 / 0.3);
}

.bg-crossbrand-inverse-s1\/40 {
  background-color: rgb(130 176 93 / 0.4);
}

.bg-crossbrand-inverse-s1\/5 {
  background-color: rgb(130 176 93 / 0.05);
}

.bg-crossbrand-inverse-s1\/50 {
  background-color: rgb(130 176 93 / 0.5);
}

.bg-crossbrand-inverse-s1\/60 {
  background-color: rgb(130 176 93 / 0.6);
}

.bg-crossbrand-inverse-s1\/70 {
  background-color: rgb(130 176 93 / 0.7);
}

.bg-crossbrand-inverse-s1\/75 {
  background-color: rgb(130 176 93 / 0.75);
}

.bg-crossbrand-inverse-s1\/80 {
  background-color: rgb(130 176 93 / 0.8);
}

.bg-crossbrand-inverse-s1\/90 {
  background-color: rgb(130 176 93 / 0.9);
}

.bg-crossbrand-inverse-s1\/95 {
  background-color: rgb(130 176 93 / 0.95);
}

.bg-crossbrand-inverse-wrn1 {
  --tw-bg-opacity: 1;
  background-color: rgb(255 200 58 / var(--tw-bg-opacity));
}

.bg-crossbrand-inverse-wrn1\/0 {
  background-color: rgb(255 200 58 / 0);
}

.bg-crossbrand-inverse-wrn1\/10 {
  background-color: rgb(255 200 58 / 0.1);
}

.bg-crossbrand-inverse-wrn1\/100 {
  background-color: rgb(255 200 58 / 1);
}

.bg-crossbrand-inverse-wrn1\/20 {
  background-color: rgb(255 200 58 / 0.2);
}

.bg-crossbrand-inverse-wrn1\/25 {
  background-color: rgb(255 200 58 / 0.25);
}

.bg-crossbrand-inverse-wrn1\/30 {
  background-color: rgb(255 200 58 / 0.3);
}

.bg-crossbrand-inverse-wrn1\/40 {
  background-color: rgb(255 200 58 / 0.4);
}

.bg-crossbrand-inverse-wrn1\/5 {
  background-color: rgb(255 200 58 / 0.05);
}

.bg-crossbrand-inverse-wrn1\/50 {
  background-color: rgb(255 200 58 / 0.5);
}

.bg-crossbrand-inverse-wrn1\/60 {
  background-color: rgb(255 200 58 / 0.6);
}

.bg-crossbrand-inverse-wrn1\/70 {
  background-color: rgb(255 200 58 / 0.7);
}

.bg-crossbrand-inverse-wrn1\/75 {
  background-color: rgb(255 200 58 / 0.75);
}

.bg-crossbrand-inverse-wrn1\/80 {
  background-color: rgb(255 200 58 / 0.8);
}

.bg-crossbrand-inverse-wrn1\/90 {
  background-color: rgb(255 200 58 / 0.9);
}

.bg-crossbrand-inverse-wrn1\/95 {
  background-color: rgb(255 200 58 / 0.95);
}

.bg-crossbrand-r1 {
  --tw-bg-opacity: 1;
  background-color: rgb(208 0 0 / var(--tw-bg-opacity));
}

.bg-crossbrand-r1\/0 {
  background-color: rgb(208 0 0 / 0);
}

.bg-crossbrand-r1\/10 {
  background-color: rgb(208 0 0 / 0.1);
}

.bg-crossbrand-r1\/100 {
  background-color: rgb(208 0 0 / 1);
}

.bg-crossbrand-r1\/20 {
  background-color: rgb(208 0 0 / 0.2);
}

.bg-crossbrand-r1\/25 {
  background-color: rgb(208 0 0 / 0.25);
}

.bg-crossbrand-r1\/30 {
  background-color: rgb(208 0 0 / 0.3);
}

.bg-crossbrand-r1\/40 {
  background-color: rgb(208 0 0 / 0.4);
}

.bg-crossbrand-r1\/5 {
  background-color: rgb(208 0 0 / 0.05);
}

.bg-crossbrand-r1\/50 {
  background-color: rgb(208 0 0 / 0.5);
}

.bg-crossbrand-r1\/60 {
  background-color: rgb(208 0 0 / 0.6);
}

.bg-crossbrand-r1\/70 {
  background-color: rgb(208 0 0 / 0.7);
}

.bg-crossbrand-r1\/75 {
  background-color: rgb(208 0 0 / 0.75);
}

.bg-crossbrand-r1\/80 {
  background-color: rgb(208 0 0 / 0.8);
}

.bg-crossbrand-r1\/90 {
  background-color: rgb(208 0 0 / 0.9);
}

.bg-crossbrand-r1\/95 {
  background-color: rgb(208 0 0 / 0.95);
}

.bg-crossbrand-r2 {
  --tw-bg-opacity: 1;
  background-color: rgb(200 40 40 / var(--tw-bg-opacity));
}

.bg-crossbrand-r2\/0 {
  background-color: rgb(200 40 40 / 0);
}

.bg-crossbrand-r2\/10 {
  background-color: rgb(200 40 40 / 0.1);
}

.bg-crossbrand-r2\/100 {
  background-color: rgb(200 40 40 / 1);
}

.bg-crossbrand-r2\/20 {
  background-color: rgb(200 40 40 / 0.2);
}

.bg-crossbrand-r2\/25 {
  background-color: rgb(200 40 40 / 0.25);
}

.bg-crossbrand-r2\/30 {
  background-color: rgb(200 40 40 / 0.3);
}

.bg-crossbrand-r2\/40 {
  background-color: rgb(200 40 40 / 0.4);
}

.bg-crossbrand-r2\/5 {
  background-color: rgb(200 40 40 / 0.05);
}

.bg-crossbrand-r2\/50 {
  background-color: rgb(200 40 40 / 0.5);
}

.bg-crossbrand-r2\/60 {
  background-color: rgb(200 40 40 / 0.6);
}

.bg-crossbrand-r2\/70 {
  background-color: rgb(200 40 40 / 0.7);
}

.bg-crossbrand-r2\/75 {
  background-color: rgb(200 40 40 / 0.75);
}

.bg-crossbrand-r2\/80 {
  background-color: rgb(200 40 40 / 0.8);
}

.bg-crossbrand-r2\/90 {
  background-color: rgb(200 40 40 / 0.9);
}

.bg-crossbrand-r2\/95 {
  background-color: rgb(200 40 40 / 0.95);
}

.bg-crossbrand-r3 {
  --tw-bg-opacity: 1;
  background-color: rgb(244 61 0 / var(--tw-bg-opacity));
}

.bg-crossbrand-r3\/0 {
  background-color: rgb(244 61 0 / 0);
}

.bg-crossbrand-r3\/10 {
  background-color: rgb(244 61 0 / 0.1);
}

.bg-crossbrand-r3\/100 {
  background-color: rgb(244 61 0 / 1);
}

.bg-crossbrand-r3\/20 {
  background-color: rgb(244 61 0 / 0.2);
}

.bg-crossbrand-r3\/25 {
  background-color: rgb(244 61 0 / 0.25);
}

.bg-crossbrand-r3\/30 {
  background-color: rgb(244 61 0 / 0.3);
}

.bg-crossbrand-r3\/40 {
  background-color: rgb(244 61 0 / 0.4);
}

.bg-crossbrand-r3\/5 {
  background-color: rgb(244 61 0 / 0.05);
}

.bg-crossbrand-r3\/50 {
  background-color: rgb(244 61 0 / 0.5);
}

.bg-crossbrand-r3\/60 {
  background-color: rgb(244 61 0 / 0.6);
}

.bg-crossbrand-r3\/70 {
  background-color: rgb(244 61 0 / 0.7);
}

.bg-crossbrand-r3\/75 {
  background-color: rgb(244 61 0 / 0.75);
}

.bg-crossbrand-r3\/80 {
  background-color: rgb(244 61 0 / 0.8);
}

.bg-crossbrand-r3\/90 {
  background-color: rgb(244 61 0 / 0.9);
}

.bg-crossbrand-r3\/95 {
  background-color: rgb(244 61 0 / 0.95);
}

.bg-crossbrand-s1 {
  --tw-bg-opacity: 1;
  background-color: rgb(22 168 22 / var(--tw-bg-opacity));
}

.bg-crossbrand-s1\/0 {
  background-color: rgb(22 168 22 / 0);
}

.bg-crossbrand-s1\/10 {
  background-color: rgb(22 168 22 / 0.1);
}

.bg-crossbrand-s1\/100 {
  background-color: rgb(22 168 22 / 1);
}

.bg-crossbrand-s1\/20 {
  background-color: rgb(22 168 22 / 0.2);
}

.bg-crossbrand-s1\/25 {
  background-color: rgb(22 168 22 / 0.25);
}

.bg-crossbrand-s1\/30 {
  background-color: rgb(22 168 22 / 0.3);
}

.bg-crossbrand-s1\/40 {
  background-color: rgb(22 168 22 / 0.4);
}

.bg-crossbrand-s1\/5 {
  background-color: rgb(22 168 22 / 0.05);
}

.bg-crossbrand-s1\/50 {
  background-color: rgb(22 168 22 / 0.5);
}

.bg-crossbrand-s1\/60 {
  background-color: rgb(22 168 22 / 0.6);
}

.bg-crossbrand-s1\/70 {
  background-color: rgb(22 168 22 / 0.7);
}

.bg-crossbrand-s1\/75 {
  background-color: rgb(22 168 22 / 0.75);
}

.bg-crossbrand-s1\/80 {
  background-color: rgb(22 168 22 / 0.8);
}

.bg-crossbrand-s1\/90 {
  background-color: rgb(22 168 22 / 0.9);
}

.bg-crossbrand-s1\/95 {
  background-color: rgb(22 168 22 / 0.95);
}

.bg-crossbrand-s2 {
  --tw-bg-opacity: 1;
  background-color: rgb(240 176 11 / var(--tw-bg-opacity));
}

.bg-crossbrand-s2\/0 {
  background-color: rgb(240 176 11 / 0);
}

.bg-crossbrand-s2\/10 {
  background-color: rgb(240 176 11 / 0.1);
}

.bg-crossbrand-s2\/100 {
  background-color: rgb(240 176 11 / 1);
}

.bg-crossbrand-s2\/20 {
  background-color: rgb(240 176 11 / 0.2);
}

.bg-crossbrand-s2\/25 {
  background-color: rgb(240 176 11 / 0.25);
}

.bg-crossbrand-s2\/30 {
  background-color: rgb(240 176 11 / 0.3);
}

.bg-crossbrand-s2\/40 {
  background-color: rgb(240 176 11 / 0.4);
}

.bg-crossbrand-s2\/5 {
  background-color: rgb(240 176 11 / 0.05);
}

.bg-crossbrand-s2\/50 {
  background-color: rgb(240 176 11 / 0.5);
}

.bg-crossbrand-s2\/60 {
  background-color: rgb(240 176 11 / 0.6);
}

.bg-crossbrand-s2\/70 {
  background-color: rgb(240 176 11 / 0.7);
}

.bg-crossbrand-s2\/75 {
  background-color: rgb(240 176 11 / 0.75);
}

.bg-crossbrand-s2\/80 {
  background-color: rgb(240 176 11 / 0.8);
}

.bg-crossbrand-s2\/90 {
  background-color: rgb(240 176 11 / 0.9);
}

.bg-crossbrand-s2\/95 {
  background-color: rgb(240 176 11 / 0.95);
}

.bg-crossbrand-s3 {
  --tw-bg-opacity: 1;
  background-color: rgb(208 0 0 / var(--tw-bg-opacity));
}

.bg-crossbrand-s3\/0 {
  background-color: rgb(208 0 0 / 0);
}

.bg-crossbrand-s3\/10 {
  background-color: rgb(208 0 0 / 0.1);
}

.bg-crossbrand-s3\/100 {
  background-color: rgb(208 0 0 / 1);
}

.bg-crossbrand-s3\/20 {
  background-color: rgb(208 0 0 / 0.2);
}

.bg-crossbrand-s3\/25 {
  background-color: rgb(208 0 0 / 0.25);
}

.bg-crossbrand-s3\/30 {
  background-color: rgb(208 0 0 / 0.3);
}

.bg-crossbrand-s3\/40 {
  background-color: rgb(208 0 0 / 0.4);
}

.bg-crossbrand-s3\/5 {
  background-color: rgb(208 0 0 / 0.05);
}

.bg-crossbrand-s3\/50 {
  background-color: rgb(208 0 0 / 0.5);
}

.bg-crossbrand-s3\/60 {
  background-color: rgb(208 0 0 / 0.6);
}

.bg-crossbrand-s3\/70 {
  background-color: rgb(208 0 0 / 0.7);
}

.bg-crossbrand-s3\/75 {
  background-color: rgb(208 0 0 / 0.75);
}

.bg-crossbrand-s3\/80 {
  background-color: rgb(208 0 0 / 0.8);
}

.bg-crossbrand-s3\/90 {
  background-color: rgb(208 0 0 / 0.9);
}

.bg-crossbrand-s3\/95 {
  background-color: rgb(208 0 0 / 0.95);
}

.bg-crossbrand-valueDrawer-blu1 {
  --tw-bg-opacity: 1;
  background-color: rgb(0 55 100 / var(--tw-bg-opacity));
}

.bg-crossbrand-valueDrawer-blu1\/0 {
  background-color: rgb(0 55 100 / 0);
}

.bg-crossbrand-valueDrawer-blu1\/10 {
  background-color: rgb(0 55 100 / 0.1);
}

.bg-crossbrand-valueDrawer-blu1\/100 {
  background-color: rgb(0 55 100 / 1);
}

.bg-crossbrand-valueDrawer-blu1\/20 {
  background-color: rgb(0 55 100 / 0.2);
}

.bg-crossbrand-valueDrawer-blu1\/25 {
  background-color: rgb(0 55 100 / 0.25);
}

.bg-crossbrand-valueDrawer-blu1\/30 {
  background-color: rgb(0 55 100 / 0.3);
}

.bg-crossbrand-valueDrawer-blu1\/40 {
  background-color: rgb(0 55 100 / 0.4);
}

.bg-crossbrand-valueDrawer-blu1\/5 {
  background-color: rgb(0 55 100 / 0.05);
}

.bg-crossbrand-valueDrawer-blu1\/50 {
  background-color: rgb(0 55 100 / 0.5);
}

.bg-crossbrand-valueDrawer-blu1\/60 {
  background-color: rgb(0 55 100 / 0.6);
}

.bg-crossbrand-valueDrawer-blu1\/70 {
  background-color: rgb(0 55 100 / 0.7);
}

.bg-crossbrand-valueDrawer-blu1\/75 {
  background-color: rgb(0 55 100 / 0.75);
}

.bg-crossbrand-valueDrawer-blu1\/80 {
  background-color: rgb(0 55 100 / 0.8);
}

.bg-crossbrand-valueDrawer-blu1\/90 {
  background-color: rgb(0 55 100 / 0.9);
}

.bg-crossbrand-valueDrawer-blu1\/95 {
  background-color: rgb(0 55 100 / 0.95);
}

.bg-crossbrand-valueDrawer-grn1 {
  --tw-bg-opacity: 1;
  background-color: rgb(58 178 0 / var(--tw-bg-opacity));
}

.bg-crossbrand-valueDrawer-grn1\/0 {
  background-color: rgb(58 178 0 / 0);
}

.bg-crossbrand-valueDrawer-grn1\/10 {
  background-color: rgb(58 178 0 / 0.1);
}

.bg-crossbrand-valueDrawer-grn1\/100 {
  background-color: rgb(58 178 0 / 1);
}

.bg-crossbrand-valueDrawer-grn1\/20 {
  background-color: rgb(58 178 0 / 0.2);
}

.bg-crossbrand-valueDrawer-grn1\/25 {
  background-color: rgb(58 178 0 / 0.25);
}

.bg-crossbrand-valueDrawer-grn1\/30 {
  background-color: rgb(58 178 0 / 0.3);
}

.bg-crossbrand-valueDrawer-grn1\/40 {
  background-color: rgb(58 178 0 / 0.4);
}

.bg-crossbrand-valueDrawer-grn1\/5 {
  background-color: rgb(58 178 0 / 0.05);
}

.bg-crossbrand-valueDrawer-grn1\/50 {
  background-color: rgb(58 178 0 / 0.5);
}

.bg-crossbrand-valueDrawer-grn1\/60 {
  background-color: rgb(58 178 0 / 0.6);
}

.bg-crossbrand-valueDrawer-grn1\/70 {
  background-color: rgb(58 178 0 / 0.7);
}

.bg-crossbrand-valueDrawer-grn1\/75 {
  background-color: rgb(58 178 0 / 0.75);
}

.bg-crossbrand-valueDrawer-grn1\/80 {
  background-color: rgb(58 178 0 / 0.8);
}

.bg-crossbrand-valueDrawer-grn1\/90 {
  background-color: rgb(58 178 0 / 0.9);
}

.bg-crossbrand-valueDrawer-grn1\/95 {
  background-color: rgb(58 178 0 / 0.95);
}

.bg-crossbrand-valueDrawer-ylw1 {
  --tw-bg-opacity: 1;
  background-color: rgb(219 166 29 / var(--tw-bg-opacity));
}

.bg-crossbrand-valueDrawer-ylw1\/0 {
  background-color: rgb(219 166 29 / 0);
}

.bg-crossbrand-valueDrawer-ylw1\/10 {
  background-color: rgb(219 166 29 / 0.1);
}

.bg-crossbrand-valueDrawer-ylw1\/100 {
  background-color: rgb(219 166 29 / 1);
}

.bg-crossbrand-valueDrawer-ylw1\/20 {
  background-color: rgb(219 166 29 / 0.2);
}

.bg-crossbrand-valueDrawer-ylw1\/25 {
  background-color: rgb(219 166 29 / 0.25);
}

.bg-crossbrand-valueDrawer-ylw1\/30 {
  background-color: rgb(219 166 29 / 0.3);
}

.bg-crossbrand-valueDrawer-ylw1\/40 {
  background-color: rgb(219 166 29 / 0.4);
}

.bg-crossbrand-valueDrawer-ylw1\/5 {
  background-color: rgb(219 166 29 / 0.05);
}

.bg-crossbrand-valueDrawer-ylw1\/50 {
  background-color: rgb(219 166 29 / 0.5);
}

.bg-crossbrand-valueDrawer-ylw1\/60 {
  background-color: rgb(219 166 29 / 0.6);
}

.bg-crossbrand-valueDrawer-ylw1\/70 {
  background-color: rgb(219 166 29 / 0.7);
}

.bg-crossbrand-valueDrawer-ylw1\/75 {
  background-color: rgb(219 166 29 / 0.75);
}

.bg-crossbrand-valueDrawer-ylw1\/80 {
  background-color: rgb(219 166 29 / 0.8);
}

.bg-crossbrand-valueDrawer-ylw1\/90 {
  background-color: rgb(219 166 29 / 0.9);
}

.bg-crossbrand-valueDrawer-ylw1\/95 {
  background-color: rgb(219 166 29 / 0.95);
}

.bg-crossbrand-wh {
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity));
}

.bg-crossbrand-wh\/0 {
  background-color: rgb(255 255 255 / 0);
}

.bg-crossbrand-wh\/10 {
  background-color: rgb(255 255 255 / 0.1);
}

.bg-crossbrand-wh\/100 {
  background-color: rgb(255 255 255 / 1);
}

.bg-crossbrand-wh\/20 {
  background-color: rgb(255 255 255 / 0.2);
}

.bg-crossbrand-wh\/25 {
  background-color: rgb(255 255 255 / 0.25);
}

.bg-crossbrand-wh\/30 {
  background-color: rgb(255 255 255 / 0.3);
}

.bg-crossbrand-wh\/40 {
  background-color: rgb(255 255 255 / 0.4);
}

.bg-crossbrand-wh\/5 {
  background-color: rgb(255 255 255 / 0.05);
}

.bg-crossbrand-wh\/50 {
  background-color: rgb(255 255 255 / 0.5);
}

.bg-crossbrand-wh\/60 {
  background-color: rgb(255 255 255 / 0.6);
}

.bg-crossbrand-wh\/70 {
  background-color: rgb(255 255 255 / 0.7);
}

.bg-crossbrand-wh\/75 {
  background-color: rgb(255 255 255 / 0.75);
}

.bg-crossbrand-wh\/80 {
  background-color: rgb(255 255 255 / 0.8);
}

.bg-crossbrand-wh\/90 {
  background-color: rgb(255 255 255 / 0.9);
}

.bg-crossbrand-wh\/95 {
  background-color: rgb(255 255 255 / 0.95);
}

.bg-crossbrand-wrn1 {
  --tw-bg-opacity: 1;
  background-color: rgb(240 176 11 / var(--tw-bg-opacity));
}

.bg-crossbrand-wrn1\/0 {
  background-color: rgb(240 176 11 / 0);
}

.bg-crossbrand-wrn1\/10 {
  background-color: rgb(240 176 11 / 0.1);
}

.bg-crossbrand-wrn1\/100 {
  background-color: rgb(240 176 11 / 1);
}

.bg-crossbrand-wrn1\/20 {
  background-color: rgb(240 176 11 / 0.2);
}

.bg-crossbrand-wrn1\/25 {
  background-color: rgb(240 176 11 / 0.25);
}

.bg-crossbrand-wrn1\/30 {
  background-color: rgb(240 176 11 / 0.3);
}

.bg-crossbrand-wrn1\/40 {
  background-color: rgb(240 176 11 / 0.4);
}

.bg-crossbrand-wrn1\/5 {
  background-color: rgb(240 176 11 / 0.05);
}

.bg-crossbrand-wrn1\/50 {
  background-color: rgb(240 176 11 / 0.5);
}

.bg-crossbrand-wrn1\/60 {
  background-color: rgb(240 176 11 / 0.6);
}

.bg-crossbrand-wrn1\/70 {
  background-color: rgb(240 176 11 / 0.7);
}

.bg-crossbrand-wrn1\/75 {
  background-color: rgb(240 176 11 / 0.75);
}

.bg-crossbrand-wrn1\/80 {
  background-color: rgb(240 176 11 / 0.8);
}

.bg-crossbrand-wrn1\/90 {
  background-color: rgb(240 176 11 / 0.9);
}

.bg-crossbrand-wrn1\/95 {
  background-color: rgb(240 176 11 / 0.95);
}

.bg-current {
  background-color: currentColor;
}

.bg-cyan-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(207 250 254 / var(--tw-bg-opacity));
}

.bg-cyan-100\/0 {
  background-color: rgb(207 250 254 / 0);
}

.bg-cyan-100\/10 {
  background-color: rgb(207 250 254 / 0.1);
}

.bg-cyan-100\/100 {
  background-color: rgb(207 250 254 / 1);
}

.bg-cyan-100\/20 {
  background-color: rgb(207 250 254 / 0.2);
}

.bg-cyan-100\/25 {
  background-color: rgb(207 250 254 / 0.25);
}

.bg-cyan-100\/30 {
  background-color: rgb(207 250 254 / 0.3);
}

.bg-cyan-100\/40 {
  background-color: rgb(207 250 254 / 0.4);
}

.bg-cyan-100\/5 {
  background-color: rgb(207 250 254 / 0.05);
}

.bg-cyan-100\/50 {
  background-color: rgb(207 250 254 / 0.5);
}

.bg-cyan-100\/60 {
  background-color: rgb(207 250 254 / 0.6);
}

.bg-cyan-100\/70 {
  background-color: rgb(207 250 254 / 0.7);
}

.bg-cyan-100\/75 {
  background-color: rgb(207 250 254 / 0.75);
}

.bg-cyan-100\/80 {
  background-color: rgb(207 250 254 / 0.8);
}

.bg-cyan-100\/90 {
  background-color: rgb(207 250 254 / 0.9);
}

.bg-cyan-100\/95 {
  background-color: rgb(207 250 254 / 0.95);
}

.bg-cyan-200 {
  --tw-bg-opacity: 1;
  background-color: rgb(165 243 252 / var(--tw-bg-opacity));
}

.bg-cyan-200\/0 {
  background-color: rgb(165 243 252 / 0);
}

.bg-cyan-200\/10 {
  background-color: rgb(165 243 252 / 0.1);
}

.bg-cyan-200\/100 {
  background-color: rgb(165 243 252 / 1);
}

.bg-cyan-200\/20 {
  background-color: rgb(165 243 252 / 0.2);
}

.bg-cyan-200\/25 {
  background-color: rgb(165 243 252 / 0.25);
}

.bg-cyan-200\/30 {
  background-color: rgb(165 243 252 / 0.3);
}

.bg-cyan-200\/40 {
  background-color: rgb(165 243 252 / 0.4);
}

.bg-cyan-200\/5 {
  background-color: rgb(165 243 252 / 0.05);
}

.bg-cyan-200\/50 {
  background-color: rgb(165 243 252 / 0.5);
}

.bg-cyan-200\/60 {
  background-color: rgb(165 243 252 / 0.6);
}

.bg-cyan-200\/70 {
  background-color: rgb(165 243 252 / 0.7);
}

.bg-cyan-200\/75 {
  background-color: rgb(165 243 252 / 0.75);
}

.bg-cyan-200\/80 {
  background-color: rgb(165 243 252 / 0.8);
}

.bg-cyan-200\/90 {
  background-color: rgb(165 243 252 / 0.9);
}

.bg-cyan-200\/95 {
  background-color: rgb(165 243 252 / 0.95);
}

.bg-cyan-300 {
  --tw-bg-opacity: 1;
  background-color: rgb(103 232 249 / var(--tw-bg-opacity));
}

.bg-cyan-300\/0 {
  background-color: rgb(103 232 249 / 0);
}

.bg-cyan-300\/10 {
  background-color: rgb(103 232 249 / 0.1);
}

.bg-cyan-300\/100 {
  background-color: rgb(103 232 249 / 1);
}

.bg-cyan-300\/20 {
  background-color: rgb(103 232 249 / 0.2);
}

.bg-cyan-300\/25 {
  background-color: rgb(103 232 249 / 0.25);
}

.bg-cyan-300\/30 {
  background-color: rgb(103 232 249 / 0.3);
}

.bg-cyan-300\/40 {
  background-color: rgb(103 232 249 / 0.4);
}

.bg-cyan-300\/5 {
  background-color: rgb(103 232 249 / 0.05);
}

.bg-cyan-300\/50 {
  background-color: rgb(103 232 249 / 0.5);
}

.bg-cyan-300\/60 {
  background-color: rgb(103 232 249 / 0.6);
}

.bg-cyan-300\/70 {
  background-color: rgb(103 232 249 / 0.7);
}

.bg-cyan-300\/75 {
  background-color: rgb(103 232 249 / 0.75);
}

.bg-cyan-300\/80 {
  background-color: rgb(103 232 249 / 0.8);
}

.bg-cyan-300\/90 {
  background-color: rgb(103 232 249 / 0.9);
}

.bg-cyan-300\/95 {
  background-color: rgb(103 232 249 / 0.95);
}

.bg-cyan-400 {
  --tw-bg-opacity: 1;
  background-color: rgb(34 211 238 / var(--tw-bg-opacity));
}

.bg-cyan-400\/0 {
  background-color: rgb(34 211 238 / 0);
}

.bg-cyan-400\/10 {
  background-color: rgb(34 211 238 / 0.1);
}

.bg-cyan-400\/100 {
  background-color: rgb(34 211 238 / 1);
}

.bg-cyan-400\/20 {
  background-color: rgb(34 211 238 / 0.2);
}

.bg-cyan-400\/25 {
  background-color: rgb(34 211 238 / 0.25);
}

.bg-cyan-400\/30 {
  background-color: rgb(34 211 238 / 0.3);
}

.bg-cyan-400\/40 {
  background-color: rgb(34 211 238 / 0.4);
}

.bg-cyan-400\/5 {
  background-color: rgb(34 211 238 / 0.05);
}

.bg-cyan-400\/50 {
  background-color: rgb(34 211 238 / 0.5);
}

.bg-cyan-400\/60 {
  background-color: rgb(34 211 238 / 0.6);
}

.bg-cyan-400\/70 {
  background-color: rgb(34 211 238 / 0.7);
}

.bg-cyan-400\/75 {
  background-color: rgb(34 211 238 / 0.75);
}

.bg-cyan-400\/80 {
  background-color: rgb(34 211 238 / 0.8);
}

.bg-cyan-400\/90 {
  background-color: rgb(34 211 238 / 0.9);
}

.bg-cyan-400\/95 {
  background-color: rgb(34 211 238 / 0.95);
}

.bg-cyan-50 {
  --tw-bg-opacity: 1;
  background-color: rgb(236 254 255 / var(--tw-bg-opacity));
}

.bg-cyan-50\/0 {
  background-color: rgb(236 254 255 / 0);
}

.bg-cyan-50\/10 {
  background-color: rgb(236 254 255 / 0.1);
}

.bg-cyan-50\/100 {
  background-color: rgb(236 254 255 / 1);
}

.bg-cyan-50\/20 {
  background-color: rgb(236 254 255 / 0.2);
}

.bg-cyan-50\/25 {
  background-color: rgb(236 254 255 / 0.25);
}

.bg-cyan-50\/30 {
  background-color: rgb(236 254 255 / 0.3);
}

.bg-cyan-50\/40 {
  background-color: rgb(236 254 255 / 0.4);
}

.bg-cyan-50\/5 {
  background-color: rgb(236 254 255 / 0.05);
}

.bg-cyan-50\/50 {
  background-color: rgb(236 254 255 / 0.5);
}

.bg-cyan-50\/60 {
  background-color: rgb(236 254 255 / 0.6);
}

.bg-cyan-50\/70 {
  background-color: rgb(236 254 255 / 0.7);
}

.bg-cyan-50\/75 {
  background-color: rgb(236 254 255 / 0.75);
}

.bg-cyan-50\/80 {
  background-color: rgb(236 254 255 / 0.8);
}

.bg-cyan-50\/90 {
  background-color: rgb(236 254 255 / 0.9);
}

.bg-cyan-50\/95 {
  background-color: rgb(236 254 255 / 0.95);
}

.bg-cyan-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(6 182 212 / var(--tw-bg-opacity));
}

.bg-cyan-500\/0 {
  background-color: rgb(6 182 212 / 0);
}

.bg-cyan-500\/10 {
  background-color: rgb(6 182 212 / 0.1);
}

.bg-cyan-500\/100 {
  background-color: rgb(6 182 212 / 1);
}

.bg-cyan-500\/20 {
  background-color: rgb(6 182 212 / 0.2);
}

.bg-cyan-500\/25 {
  background-color: rgb(6 182 212 / 0.25);
}

.bg-cyan-500\/30 {
  background-color: rgb(6 182 212 / 0.3);
}

.bg-cyan-500\/40 {
  background-color: rgb(6 182 212 / 0.4);
}

.bg-cyan-500\/5 {
  background-color: rgb(6 182 212 / 0.05);
}

.bg-cyan-500\/50 {
  background-color: rgb(6 182 212 / 0.5);
}

.bg-cyan-500\/60 {
  background-color: rgb(6 182 212 / 0.6);
}

.bg-cyan-500\/70 {
  background-color: rgb(6 182 212 / 0.7);
}

.bg-cyan-500\/75 {
  background-color: rgb(6 182 212 / 0.75);
}

.bg-cyan-500\/80 {
  background-color: rgb(6 182 212 / 0.8);
}

.bg-cyan-500\/90 {
  background-color: rgb(6 182 212 / 0.9);
}

.bg-cyan-500\/95 {
  background-color: rgb(6 182 212 / 0.95);
}

.bg-cyan-600 {
  --tw-bg-opacity: 1;
  background-color: rgb(8 145 178 / var(--tw-bg-opacity));
}

.bg-cyan-600\/0 {
  background-color: rgb(8 145 178 / 0);
}

.bg-cyan-600\/10 {
  background-color: rgb(8 145 178 / 0.1);
}

.bg-cyan-600\/100 {
  background-color: rgb(8 145 178 / 1);
}

.bg-cyan-600\/20 {
  background-color: rgb(8 145 178 / 0.2);
}

.bg-cyan-600\/25 {
  background-color: rgb(8 145 178 / 0.25);
}

.bg-cyan-600\/30 {
  background-color: rgb(8 145 178 / 0.3);
}

.bg-cyan-600\/40 {
  background-color: rgb(8 145 178 / 0.4);
}

.bg-cyan-600\/5 {
  background-color: rgb(8 145 178 / 0.05);
}

.bg-cyan-600\/50 {
  background-color: rgb(8 145 178 / 0.5);
}

.bg-cyan-600\/60 {
  background-color: rgb(8 145 178 / 0.6);
}

.bg-cyan-600\/70 {
  background-color: rgb(8 145 178 / 0.7);
}

.bg-cyan-600\/75 {
  background-color: rgb(8 145 178 / 0.75);
}

.bg-cyan-600\/80 {
  background-color: rgb(8 145 178 / 0.8);
}

.bg-cyan-600\/90 {
  background-color: rgb(8 145 178 / 0.9);
}

.bg-cyan-600\/95 {
  background-color: rgb(8 145 178 / 0.95);
}

.bg-cyan-700 {
  --tw-bg-opacity: 1;
  background-color: rgb(14 116 144 / var(--tw-bg-opacity));
}

.bg-cyan-700\/0 {
  background-color: rgb(14 116 144 / 0);
}

.bg-cyan-700\/10 {
  background-color: rgb(14 116 144 / 0.1);
}

.bg-cyan-700\/100 {
  background-color: rgb(14 116 144 / 1);
}

.bg-cyan-700\/20 {
  background-color: rgb(14 116 144 / 0.2);
}

.bg-cyan-700\/25 {
  background-color: rgb(14 116 144 / 0.25);
}

.bg-cyan-700\/30 {
  background-color: rgb(14 116 144 / 0.3);
}

.bg-cyan-700\/40 {
  background-color: rgb(14 116 144 / 0.4);
}

.bg-cyan-700\/5 {
  background-color: rgb(14 116 144 / 0.05);
}

.bg-cyan-700\/50 {
  background-color: rgb(14 116 144 / 0.5);
}

.bg-cyan-700\/60 {
  background-color: rgb(14 116 144 / 0.6);
}

.bg-cyan-700\/70 {
  background-color: rgb(14 116 144 / 0.7);
}

.bg-cyan-700\/75 {
  background-color: rgb(14 116 144 / 0.75);
}

.bg-cyan-700\/80 {
  background-color: rgb(14 116 144 / 0.8);
}

.bg-cyan-700\/90 {
  background-color: rgb(14 116 144 / 0.9);
}

.bg-cyan-700\/95 {
  background-color: rgb(14 116 144 / 0.95);
}

.bg-cyan-800 {
  --tw-bg-opacity: 1;
  background-color: rgb(21 94 117 / var(--tw-bg-opacity));
}

.bg-cyan-800\/0 {
  background-color: rgb(21 94 117 / 0);
}

.bg-cyan-800\/10 {
  background-color: rgb(21 94 117 / 0.1);
}

.bg-cyan-800\/100 {
  background-color: rgb(21 94 117 / 1);
}

.bg-cyan-800\/20 {
  background-color: rgb(21 94 117 / 0.2);
}

.bg-cyan-800\/25 {
  background-color: rgb(21 94 117 / 0.25);
}

.bg-cyan-800\/30 {
  background-color: rgb(21 94 117 / 0.3);
}

.bg-cyan-800\/40 {
  background-color: rgb(21 94 117 / 0.4);
}

.bg-cyan-800\/5 {
  background-color: rgb(21 94 117 / 0.05);
}

.bg-cyan-800\/50 {
  background-color: rgb(21 94 117 / 0.5);
}

.bg-cyan-800\/60 {
  background-color: rgb(21 94 117 / 0.6);
}

.bg-cyan-800\/70 {
  background-color: rgb(21 94 117 / 0.7);
}

.bg-cyan-800\/75 {
  background-color: rgb(21 94 117 / 0.75);
}

.bg-cyan-800\/80 {
  background-color: rgb(21 94 117 / 0.8);
}

.bg-cyan-800\/90 {
  background-color: rgb(21 94 117 / 0.9);
}

.bg-cyan-800\/95 {
  background-color: rgb(21 94 117 / 0.95);
}

.bg-cyan-900 {
  --tw-bg-opacity: 1;
  background-color: rgb(22 78 99 / var(--tw-bg-opacity));
}

.bg-cyan-900\/0 {
  background-color: rgb(22 78 99 / 0);
}

.bg-cyan-900\/10 {
  background-color: rgb(22 78 99 / 0.1);
}

.bg-cyan-900\/100 {
  background-color: rgb(22 78 99 / 1);
}

.bg-cyan-900\/20 {
  background-color: rgb(22 78 99 / 0.2);
}

.bg-cyan-900\/25 {
  background-color: rgb(22 78 99 / 0.25);
}

.bg-cyan-900\/30 {
  background-color: rgb(22 78 99 / 0.3);
}

.bg-cyan-900\/40 {
  background-color: rgb(22 78 99 / 0.4);
}

.bg-cyan-900\/5 {
  background-color: rgb(22 78 99 / 0.05);
}

.bg-cyan-900\/50 {
  background-color: rgb(22 78 99 / 0.5);
}

.bg-cyan-900\/60 {
  background-color: rgb(22 78 99 / 0.6);
}

.bg-cyan-900\/70 {
  background-color: rgb(22 78 99 / 0.7);
}

.bg-cyan-900\/75 {
  background-color: rgb(22 78 99 / 0.75);
}

.bg-cyan-900\/80 {
  background-color: rgb(22 78 99 / 0.8);
}

.bg-cyan-900\/90 {
  background-color: rgb(22 78 99 / 0.9);
}

.bg-cyan-900\/95 {
  background-color: rgb(22 78 99 / 0.95);
}

.bg-cyan-950 {
  --tw-bg-opacity: 1;
  background-color: rgb(8 51 68 / var(--tw-bg-opacity));
}

.bg-cyan-950\/0 {
  background-color: rgb(8 51 68 / 0);
}

.bg-cyan-950\/10 {
  background-color: rgb(8 51 68 / 0.1);
}

.bg-cyan-950\/100 {
  background-color: rgb(8 51 68 / 1);
}

.bg-cyan-950\/20 {
  background-color: rgb(8 51 68 / 0.2);
}

.bg-cyan-950\/25 {
  background-color: rgb(8 51 68 / 0.25);
}

.bg-cyan-950\/30 {
  background-color: rgb(8 51 68 / 0.3);
}

.bg-cyan-950\/40 {
  background-color: rgb(8 51 68 / 0.4);
}

.bg-cyan-950\/5 {
  background-color: rgb(8 51 68 / 0.05);
}

.bg-cyan-950\/50 {
  background-color: rgb(8 51 68 / 0.5);
}

.bg-cyan-950\/60 {
  background-color: rgb(8 51 68 / 0.6);
}

.bg-cyan-950\/70 {
  background-color: rgb(8 51 68 / 0.7);
}

.bg-cyan-950\/75 {
  background-color: rgb(8 51 68 / 0.75);
}

.bg-cyan-950\/80 {
  background-color: rgb(8 51 68 / 0.8);
}

.bg-cyan-950\/90 {
  background-color: rgb(8 51 68 / 0.9);
}

.bg-cyan-950\/95 {
  background-color: rgb(8 51 68 / 0.95);
}

.bg-emerald-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(209 250 229 / var(--tw-bg-opacity));
}

.bg-emerald-100\/0 {
  background-color: rgb(209 250 229 / 0);
}

.bg-emerald-100\/10 {
  background-color: rgb(209 250 229 / 0.1);
}

.bg-emerald-100\/100 {
  background-color: rgb(209 250 229 / 1);
}

.bg-emerald-100\/20 {
  background-color: rgb(209 250 229 / 0.2);
}

.bg-emerald-100\/25 {
  background-color: rgb(209 250 229 / 0.25);
}

.bg-emerald-100\/30 {
  background-color: rgb(209 250 229 / 0.3);
}

.bg-emerald-100\/40 {
  background-color: rgb(209 250 229 / 0.4);
}

.bg-emerald-100\/5 {
  background-color: rgb(209 250 229 / 0.05);
}

.bg-emerald-100\/50 {
  background-color: rgb(209 250 229 / 0.5);
}

.bg-emerald-100\/60 {
  background-color: rgb(209 250 229 / 0.6);
}

.bg-emerald-100\/70 {
  background-color: rgb(209 250 229 / 0.7);
}

.bg-emerald-100\/75 {
  background-color: rgb(209 250 229 / 0.75);
}

.bg-emerald-100\/80 {
  background-color: rgb(209 250 229 / 0.8);
}

.bg-emerald-100\/90 {
  background-color: rgb(209 250 229 / 0.9);
}

.bg-emerald-100\/95 {
  background-color: rgb(209 250 229 / 0.95);
}

.bg-emerald-200 {
  --tw-bg-opacity: 1;
  background-color: rgb(167 243 208 / var(--tw-bg-opacity));
}

.bg-emerald-200\/0 {
  background-color: rgb(167 243 208 / 0);
}

.bg-emerald-200\/10 {
  background-color: rgb(167 243 208 / 0.1);
}

.bg-emerald-200\/100 {
  background-color: rgb(167 243 208 / 1);
}

.bg-emerald-200\/20 {
  background-color: rgb(167 243 208 / 0.2);
}

.bg-emerald-200\/25 {
  background-color: rgb(167 243 208 / 0.25);
}

.bg-emerald-200\/30 {
  background-color: rgb(167 243 208 / 0.3);
}

.bg-emerald-200\/40 {
  background-color: rgb(167 243 208 / 0.4);
}

.bg-emerald-200\/5 {
  background-color: rgb(167 243 208 / 0.05);
}

.bg-emerald-200\/50 {
  background-color: rgb(167 243 208 / 0.5);
}

.bg-emerald-200\/60 {
  background-color: rgb(167 243 208 / 0.6);
}

.bg-emerald-200\/70 {
  background-color: rgb(167 243 208 / 0.7);
}

.bg-emerald-200\/75 {
  background-color: rgb(167 243 208 / 0.75);
}

.bg-emerald-200\/80 {
  background-color: rgb(167 243 208 / 0.8);
}

.bg-emerald-200\/90 {
  background-color: rgb(167 243 208 / 0.9);
}

.bg-emerald-200\/95 {
  background-color: rgb(167 243 208 / 0.95);
}

.bg-emerald-300 {
  --tw-bg-opacity: 1;
  background-color: rgb(110 231 183 / var(--tw-bg-opacity));
}

.bg-emerald-300\/0 {
  background-color: rgb(110 231 183 / 0);
}

.bg-emerald-300\/10 {
  background-color: rgb(110 231 183 / 0.1);
}

.bg-emerald-300\/100 {
  background-color: rgb(110 231 183 / 1);
}

.bg-emerald-300\/20 {
  background-color: rgb(110 231 183 / 0.2);
}

.bg-emerald-300\/25 {
  background-color: rgb(110 231 183 / 0.25);
}

.bg-emerald-300\/30 {
  background-color: rgb(110 231 183 / 0.3);
}

.bg-emerald-300\/40 {
  background-color: rgb(110 231 183 / 0.4);
}

.bg-emerald-300\/5 {
  background-color: rgb(110 231 183 / 0.05);
}

.bg-emerald-300\/50 {
  background-color: rgb(110 231 183 / 0.5);
}

.bg-emerald-300\/60 {
  background-color: rgb(110 231 183 / 0.6);
}

.bg-emerald-300\/70 {
  background-color: rgb(110 231 183 / 0.7);
}

.bg-emerald-300\/75 {
  background-color: rgb(110 231 183 / 0.75);
}

.bg-emerald-300\/80 {
  background-color: rgb(110 231 183 / 0.8);
}

.bg-emerald-300\/90 {
  background-color: rgb(110 231 183 / 0.9);
}

.bg-emerald-300\/95 {
  background-color: rgb(110 231 183 / 0.95);
}

.bg-emerald-400 {
  --tw-bg-opacity: 1;
  background-color: rgb(52 211 153 / var(--tw-bg-opacity));
}

.bg-emerald-400\/0 {
  background-color: rgb(52 211 153 / 0);
}

.bg-emerald-400\/10 {
  background-color: rgb(52 211 153 / 0.1);
}

.bg-emerald-400\/100 {
  background-color: rgb(52 211 153 / 1);
}

.bg-emerald-400\/20 {
  background-color: rgb(52 211 153 / 0.2);
}

.bg-emerald-400\/25 {
  background-color: rgb(52 211 153 / 0.25);
}

.bg-emerald-400\/30 {
  background-color: rgb(52 211 153 / 0.3);
}

.bg-emerald-400\/40 {
  background-color: rgb(52 211 153 / 0.4);
}

.bg-emerald-400\/5 {
  background-color: rgb(52 211 153 / 0.05);
}

.bg-emerald-400\/50 {
  background-color: rgb(52 211 153 / 0.5);
}

.bg-emerald-400\/60 {
  background-color: rgb(52 211 153 / 0.6);
}

.bg-emerald-400\/70 {
  background-color: rgb(52 211 153 / 0.7);
}

.bg-emerald-400\/75 {
  background-color: rgb(52 211 153 / 0.75);
}

.bg-emerald-400\/80 {
  background-color: rgb(52 211 153 / 0.8);
}

.bg-emerald-400\/90 {
  background-color: rgb(52 211 153 / 0.9);
}

.bg-emerald-400\/95 {
  background-color: rgb(52 211 153 / 0.95);
}

.bg-emerald-50 {
  --tw-bg-opacity: 1;
  background-color: rgb(236 253 245 / var(--tw-bg-opacity));
}

.bg-emerald-50\/0 {
  background-color: rgb(236 253 245 / 0);
}

.bg-emerald-50\/10 {
  background-color: rgb(236 253 245 / 0.1);
}

.bg-emerald-50\/100 {
  background-color: rgb(236 253 245 / 1);
}

.bg-emerald-50\/20 {
  background-color: rgb(236 253 245 / 0.2);
}

.bg-emerald-50\/25 {
  background-color: rgb(236 253 245 / 0.25);
}

.bg-emerald-50\/30 {
  background-color: rgb(236 253 245 / 0.3);
}

.bg-emerald-50\/40 {
  background-color: rgb(236 253 245 / 0.4);
}

.bg-emerald-50\/5 {
  background-color: rgb(236 253 245 / 0.05);
}

.bg-emerald-50\/50 {
  background-color: rgb(236 253 245 / 0.5);
}

.bg-emerald-50\/60 {
  background-color: rgb(236 253 245 / 0.6);
}

.bg-emerald-50\/70 {
  background-color: rgb(236 253 245 / 0.7);
}

.bg-emerald-50\/75 {
  background-color: rgb(236 253 245 / 0.75);
}

.bg-emerald-50\/80 {
  background-color: rgb(236 253 245 / 0.8);
}

.bg-emerald-50\/90 {
  background-color: rgb(236 253 245 / 0.9);
}

.bg-emerald-50\/95 {
  background-color: rgb(236 253 245 / 0.95);
}

.bg-emerald-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(16 185 129 / var(--tw-bg-opacity));
}

.bg-emerald-500\/0 {
  background-color: rgb(16 185 129 / 0);
}

.bg-emerald-500\/10 {
  background-color: rgb(16 185 129 / 0.1);
}

.bg-emerald-500\/100 {
  background-color: rgb(16 185 129 / 1);
}

.bg-emerald-500\/20 {
  background-color: rgb(16 185 129 / 0.2);
}

.bg-emerald-500\/25 {
  background-color: rgb(16 185 129 / 0.25);
}

.bg-emerald-500\/30 {
  background-color: rgb(16 185 129 / 0.3);
}

.bg-emerald-500\/40 {
  background-color: rgb(16 185 129 / 0.4);
}

.bg-emerald-500\/5 {
  background-color: rgb(16 185 129 / 0.05);
}

.bg-emerald-500\/50 {
  background-color: rgb(16 185 129 / 0.5);
}

.bg-emerald-500\/60 {
  background-color: rgb(16 185 129 / 0.6);
}

.bg-emerald-500\/70 {
  background-color: rgb(16 185 129 / 0.7);
}

.bg-emerald-500\/75 {
  background-color: rgb(16 185 129 / 0.75);
}

.bg-emerald-500\/80 {
  background-color: rgb(16 185 129 / 0.8);
}

.bg-emerald-500\/90 {
  background-color: rgb(16 185 129 / 0.9);
}

.bg-emerald-500\/95 {
  background-color: rgb(16 185 129 / 0.95);
}

.bg-emerald-600 {
  --tw-bg-opacity: 1;
  background-color: rgb(5 150 105 / var(--tw-bg-opacity));
}

.bg-emerald-600\/0 {
  background-color: rgb(5 150 105 / 0);
}

.bg-emerald-600\/10 {
  background-color: rgb(5 150 105 / 0.1);
}

.bg-emerald-600\/100 {
  background-color: rgb(5 150 105 / 1);
}

.bg-emerald-600\/20 {
  background-color: rgb(5 150 105 / 0.2);
}

.bg-emerald-600\/25 {
  background-color: rgb(5 150 105 / 0.25);
}

.bg-emerald-600\/30 {
  background-color: rgb(5 150 105 / 0.3);
}

.bg-emerald-600\/40 {
  background-color: rgb(5 150 105 / 0.4);
}

.bg-emerald-600\/5 {
  background-color: rgb(5 150 105 / 0.05);
}

.bg-emerald-600\/50 {
  background-color: rgb(5 150 105 / 0.5);
}

.bg-emerald-600\/60 {
  background-color: rgb(5 150 105 / 0.6);
}

.bg-emerald-600\/70 {
  background-color: rgb(5 150 105 / 0.7);
}

.bg-emerald-600\/75 {
  background-color: rgb(5 150 105 / 0.75);
}

.bg-emerald-600\/80 {
  background-color: rgb(5 150 105 / 0.8);
}

.bg-emerald-600\/90 {
  background-color: rgb(5 150 105 / 0.9);
}

.bg-emerald-600\/95 {
  background-color: rgb(5 150 105 / 0.95);
}

.bg-emerald-700 {
  --tw-bg-opacity: 1;
  background-color: rgb(4 120 87 / var(--tw-bg-opacity));
}

.bg-emerald-700\/0 {
  background-color: rgb(4 120 87 / 0);
}

.bg-emerald-700\/10 {
  background-color: rgb(4 120 87 / 0.1);
}

.bg-emerald-700\/100 {
  background-color: rgb(4 120 87 / 1);
}

.bg-emerald-700\/20 {
  background-color: rgb(4 120 87 / 0.2);
}

.bg-emerald-700\/25 {
  background-color: rgb(4 120 87 / 0.25);
}

.bg-emerald-700\/30 {
  background-color: rgb(4 120 87 / 0.3);
}

.bg-emerald-700\/40 {
  background-color: rgb(4 120 87 / 0.4);
}

.bg-emerald-700\/5 {
  background-color: rgb(4 120 87 / 0.05);
}

.bg-emerald-700\/50 {
  background-color: rgb(4 120 87 / 0.5);
}

.bg-emerald-700\/60 {
  background-color: rgb(4 120 87 / 0.6);
}

.bg-emerald-700\/70 {
  background-color: rgb(4 120 87 / 0.7);
}

.bg-emerald-700\/75 {
  background-color: rgb(4 120 87 / 0.75);
}

.bg-emerald-700\/80 {
  background-color: rgb(4 120 87 / 0.8);
}

.bg-emerald-700\/90 {
  background-color: rgb(4 120 87 / 0.9);
}

.bg-emerald-700\/95 {
  background-color: rgb(4 120 87 / 0.95);
}

.bg-emerald-800 {
  --tw-bg-opacity: 1;
  background-color: rgb(6 95 70 / var(--tw-bg-opacity));
}

.bg-emerald-800\/0 {
  background-color: rgb(6 95 70 / 0);
}

.bg-emerald-800\/10 {
  background-color: rgb(6 95 70 / 0.1);
}

.bg-emerald-800\/100 {
  background-color: rgb(6 95 70 / 1);
}

.bg-emerald-800\/20 {
  background-color: rgb(6 95 70 / 0.2);
}

.bg-emerald-800\/25 {
  background-color: rgb(6 95 70 / 0.25);
}

.bg-emerald-800\/30 {
  background-color: rgb(6 95 70 / 0.3);
}

.bg-emerald-800\/40 {
  background-color: rgb(6 95 70 / 0.4);
}

.bg-emerald-800\/5 {
  background-color: rgb(6 95 70 / 0.05);
}

.bg-emerald-800\/50 {
  background-color: rgb(6 95 70 / 0.5);
}

.bg-emerald-800\/60 {
  background-color: rgb(6 95 70 / 0.6);
}

.bg-emerald-800\/70 {
  background-color: rgb(6 95 70 / 0.7);
}

.bg-emerald-800\/75 {
  background-color: rgb(6 95 70 / 0.75);
}

.bg-emerald-800\/80 {
  background-color: rgb(6 95 70 / 0.8);
}

.bg-emerald-800\/90 {
  background-color: rgb(6 95 70 / 0.9);
}

.bg-emerald-800\/95 {
  background-color: rgb(6 95 70 / 0.95);
}

.bg-emerald-900 {
  --tw-bg-opacity: 1;
  background-color: rgb(6 78 59 / var(--tw-bg-opacity));
}

.bg-emerald-900\/0 {
  background-color: rgb(6 78 59 / 0);
}

.bg-emerald-900\/10 {
  background-color: rgb(6 78 59 / 0.1);
}

.bg-emerald-900\/100 {
  background-color: rgb(6 78 59 / 1);
}

.bg-emerald-900\/20 {
  background-color: rgb(6 78 59 / 0.2);
}

.bg-emerald-900\/25 {
  background-color: rgb(6 78 59 / 0.25);
}

.bg-emerald-900\/30 {
  background-color: rgb(6 78 59 / 0.3);
}

.bg-emerald-900\/40 {
  background-color: rgb(6 78 59 / 0.4);
}

.bg-emerald-900\/5 {
  background-color: rgb(6 78 59 / 0.05);
}

.bg-emerald-900\/50 {
  background-color: rgb(6 78 59 / 0.5);
}

.bg-emerald-900\/60 {
  background-color: rgb(6 78 59 / 0.6);
}

.bg-emerald-900\/70 {
  background-color: rgb(6 78 59 / 0.7);
}

.bg-emerald-900\/75 {
  background-color: rgb(6 78 59 / 0.75);
}

.bg-emerald-900\/80 {
  background-color: rgb(6 78 59 / 0.8);
}

.bg-emerald-900\/90 {
  background-color: rgb(6 78 59 / 0.9);
}

.bg-emerald-900\/95 {
  background-color: rgb(6 78 59 / 0.95);
}

.bg-emerald-950 {
  --tw-bg-opacity: 1;
  background-color: rgb(2 44 34 / var(--tw-bg-opacity));
}

.bg-emerald-950\/0 {
  background-color: rgb(2 44 34 / 0);
}

.bg-emerald-950\/10 {
  background-color: rgb(2 44 34 / 0.1);
}

.bg-emerald-950\/100 {
  background-color: rgb(2 44 34 / 1);
}

.bg-emerald-950\/20 {
  background-color: rgb(2 44 34 / 0.2);
}

.bg-emerald-950\/25 {
  background-color: rgb(2 44 34 / 0.25);
}

.bg-emerald-950\/30 {
  background-color: rgb(2 44 34 / 0.3);
}

.bg-emerald-950\/40 {
  background-color: rgb(2 44 34 / 0.4);
}

.bg-emerald-950\/5 {
  background-color: rgb(2 44 34 / 0.05);
}

.bg-emerald-950\/50 {
  background-color: rgb(2 44 34 / 0.5);
}

.bg-emerald-950\/60 {
  background-color: rgb(2 44 34 / 0.6);
}

.bg-emerald-950\/70 {
  background-color: rgb(2 44 34 / 0.7);
}

.bg-emerald-950\/75 {
  background-color: rgb(2 44 34 / 0.75);
}

.bg-emerald-950\/80 {
  background-color: rgb(2 44 34 / 0.8);
}

.bg-emerald-950\/90 {
  background-color: rgb(2 44 34 / 0.9);
}

.bg-emerald-950\/95 {
  background-color: rgb(2 44 34 / 0.95);
}

.bg-err1 {
  --tw-bg-opacity: 1;
  background-color: rgb(208 0 0 / var(--tw-bg-opacity));
}

.bg-err1\/0 {
  background-color: rgb(208 0 0 / 0);
}

.bg-err1\/10 {
  background-color: rgb(208 0 0 / 0.1);
}

.bg-err1\/100 {
  background-color: rgb(208 0 0 / 1);
}

.bg-err1\/20 {
  background-color: rgb(208 0 0 / 0.2);
}

.bg-err1\/25 {
  background-color: rgb(208 0 0 / 0.25);
}

.bg-err1\/30 {
  background-color: rgb(208 0 0 / 0.3);
}

.bg-err1\/40 {
  background-color: rgb(208 0 0 / 0.4);
}

.bg-err1\/5 {
  background-color: rgb(208 0 0 / 0.05);
}

.bg-err1\/50 {
  background-color: rgb(208 0 0 / 0.5);
}

.bg-err1\/60 {
  background-color: rgb(208 0 0 / 0.6);
}

.bg-err1\/70 {
  background-color: rgb(208 0 0 / 0.7);
}

.bg-err1\/75 {
  background-color: rgb(208 0 0 / 0.75);
}

.bg-err1\/80 {
  background-color: rgb(208 0 0 / 0.8);
}

.bg-err1\/90 {
  background-color: rgb(208 0 0 / 0.9);
}

.bg-err1\/95 {
  background-color: rgb(208 0 0 / 0.95);
}

.bg-fuchsia-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(250 232 255 / var(--tw-bg-opacity));
}

.bg-fuchsia-100\/0 {
  background-color: rgb(250 232 255 / 0);
}

.bg-fuchsia-100\/10 {
  background-color: rgb(250 232 255 / 0.1);
}

.bg-fuchsia-100\/100 {
  background-color: rgb(250 232 255 / 1);
}

.bg-fuchsia-100\/20 {
  background-color: rgb(250 232 255 / 0.2);
}

.bg-fuchsia-100\/25 {
  background-color: rgb(250 232 255 / 0.25);
}

.bg-fuchsia-100\/30 {
  background-color: rgb(250 232 255 / 0.3);
}

.bg-fuchsia-100\/40 {
  background-color: rgb(250 232 255 / 0.4);
}

.bg-fuchsia-100\/5 {
  background-color: rgb(250 232 255 / 0.05);
}

.bg-fuchsia-100\/50 {
  background-color: rgb(250 232 255 / 0.5);
}

.bg-fuchsia-100\/60 {
  background-color: rgb(250 232 255 / 0.6);
}

.bg-fuchsia-100\/70 {
  background-color: rgb(250 232 255 / 0.7);
}

.bg-fuchsia-100\/75 {
  background-color: rgb(250 232 255 / 0.75);
}

.bg-fuchsia-100\/80 {
  background-color: rgb(250 232 255 / 0.8);
}

.bg-fuchsia-100\/90 {
  background-color: rgb(250 232 255 / 0.9);
}

.bg-fuchsia-100\/95 {
  background-color: rgb(250 232 255 / 0.95);
}

.bg-fuchsia-200 {
  --tw-bg-opacity: 1;
  background-color: rgb(245 208 254 / var(--tw-bg-opacity));
}

.bg-fuchsia-200\/0 {
  background-color: rgb(245 208 254 / 0);
}

.bg-fuchsia-200\/10 {
  background-color: rgb(245 208 254 / 0.1);
}

.bg-fuchsia-200\/100 {
  background-color: rgb(245 208 254 / 1);
}

.bg-fuchsia-200\/20 {
  background-color: rgb(245 208 254 / 0.2);
}

.bg-fuchsia-200\/25 {
  background-color: rgb(245 208 254 / 0.25);
}

.bg-fuchsia-200\/30 {
  background-color: rgb(245 208 254 / 0.3);
}

.bg-fuchsia-200\/40 {
  background-color: rgb(245 208 254 / 0.4);
}

.bg-fuchsia-200\/5 {
  background-color: rgb(245 208 254 / 0.05);
}

.bg-fuchsia-200\/50 {
  background-color: rgb(245 208 254 / 0.5);
}

.bg-fuchsia-200\/60 {
  background-color: rgb(245 208 254 / 0.6);
}

.bg-fuchsia-200\/70 {
  background-color: rgb(245 208 254 / 0.7);
}

.bg-fuchsia-200\/75 {
  background-color: rgb(245 208 254 / 0.75);
}

.bg-fuchsia-200\/80 {
  background-color: rgb(245 208 254 / 0.8);
}

.bg-fuchsia-200\/90 {
  background-color: rgb(245 208 254 / 0.9);
}

.bg-fuchsia-200\/95 {
  background-color: rgb(245 208 254 / 0.95);
}

.bg-fuchsia-300 {
  --tw-bg-opacity: 1;
  background-color: rgb(240 171 252 / var(--tw-bg-opacity));
}

.bg-fuchsia-300\/0 {
  background-color: rgb(240 171 252 / 0);
}

.bg-fuchsia-300\/10 {
  background-color: rgb(240 171 252 / 0.1);
}

.bg-fuchsia-300\/100 {
  background-color: rgb(240 171 252 / 1);
}

.bg-fuchsia-300\/20 {
  background-color: rgb(240 171 252 / 0.2);
}

.bg-fuchsia-300\/25 {
  background-color: rgb(240 171 252 / 0.25);
}

.bg-fuchsia-300\/30 {
  background-color: rgb(240 171 252 / 0.3);
}

.bg-fuchsia-300\/40 {
  background-color: rgb(240 171 252 / 0.4);
}

.bg-fuchsia-300\/5 {
  background-color: rgb(240 171 252 / 0.05);
}

.bg-fuchsia-300\/50 {
  background-color: rgb(240 171 252 / 0.5);
}

.bg-fuchsia-300\/60 {
  background-color: rgb(240 171 252 / 0.6);
}

.bg-fuchsia-300\/70 {
  background-color: rgb(240 171 252 / 0.7);
}

.bg-fuchsia-300\/75 {
  background-color: rgb(240 171 252 / 0.75);
}

.bg-fuchsia-300\/80 {
  background-color: rgb(240 171 252 / 0.8);
}

.bg-fuchsia-300\/90 {
  background-color: rgb(240 171 252 / 0.9);
}

.bg-fuchsia-300\/95 {
  background-color: rgb(240 171 252 / 0.95);
}

.bg-fuchsia-400 {
  --tw-bg-opacity: 1;
  background-color: rgb(232 121 249 / var(--tw-bg-opacity));
}

.bg-fuchsia-400\/0 {
  background-color: rgb(232 121 249 / 0);
}

.bg-fuchsia-400\/10 {
  background-color: rgb(232 121 249 / 0.1);
}

.bg-fuchsia-400\/100 {
  background-color: rgb(232 121 249 / 1);
}

.bg-fuchsia-400\/20 {
  background-color: rgb(232 121 249 / 0.2);
}

.bg-fuchsia-400\/25 {
  background-color: rgb(232 121 249 / 0.25);
}

.bg-fuchsia-400\/30 {
  background-color: rgb(232 121 249 / 0.3);
}

.bg-fuchsia-400\/40 {
  background-color: rgb(232 121 249 / 0.4);
}

.bg-fuchsia-400\/5 {
  background-color: rgb(232 121 249 / 0.05);
}

.bg-fuchsia-400\/50 {
  background-color: rgb(232 121 249 / 0.5);
}

.bg-fuchsia-400\/60 {
  background-color: rgb(232 121 249 / 0.6);
}

.bg-fuchsia-400\/70 {
  background-color: rgb(232 121 249 / 0.7);
}

.bg-fuchsia-400\/75 {
  background-color: rgb(232 121 249 / 0.75);
}

.bg-fuchsia-400\/80 {
  background-color: rgb(232 121 249 / 0.8);
}

.bg-fuchsia-400\/90 {
  background-color: rgb(232 121 249 / 0.9);
}

.bg-fuchsia-400\/95 {
  background-color: rgb(232 121 249 / 0.95);
}

.bg-fuchsia-50 {
  --tw-bg-opacity: 1;
  background-color: rgb(253 244 255 / var(--tw-bg-opacity));
}

.bg-fuchsia-50\/0 {
  background-color: rgb(253 244 255 / 0);
}

.bg-fuchsia-50\/10 {
  background-color: rgb(253 244 255 / 0.1);
}

.bg-fuchsia-50\/100 {
  background-color: rgb(253 244 255 / 1);
}

.bg-fuchsia-50\/20 {
  background-color: rgb(253 244 255 / 0.2);
}

.bg-fuchsia-50\/25 {
  background-color: rgb(253 244 255 / 0.25);
}

.bg-fuchsia-50\/30 {
  background-color: rgb(253 244 255 / 0.3);
}

.bg-fuchsia-50\/40 {
  background-color: rgb(253 244 255 / 0.4);
}

.bg-fuchsia-50\/5 {
  background-color: rgb(253 244 255 / 0.05);
}

.bg-fuchsia-50\/50 {
  background-color: rgb(253 244 255 / 0.5);
}

.bg-fuchsia-50\/60 {
  background-color: rgb(253 244 255 / 0.6);
}

.bg-fuchsia-50\/70 {
  background-color: rgb(253 244 255 / 0.7);
}

.bg-fuchsia-50\/75 {
  background-color: rgb(253 244 255 / 0.75);
}

.bg-fuchsia-50\/80 {
  background-color: rgb(253 244 255 / 0.8);
}

.bg-fuchsia-50\/90 {
  background-color: rgb(253 244 255 / 0.9);
}

.bg-fuchsia-50\/95 {
  background-color: rgb(253 244 255 / 0.95);
}

.bg-fuchsia-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(217 70 239 / var(--tw-bg-opacity));
}

.bg-fuchsia-500\/0 {
  background-color: rgb(217 70 239 / 0);
}

.bg-fuchsia-500\/10 {
  background-color: rgb(217 70 239 / 0.1);
}

.bg-fuchsia-500\/100 {
  background-color: rgb(217 70 239 / 1);
}

.bg-fuchsia-500\/20 {
  background-color: rgb(217 70 239 / 0.2);
}

.bg-fuchsia-500\/25 {
  background-color: rgb(217 70 239 / 0.25);
}

.bg-fuchsia-500\/30 {
  background-color: rgb(217 70 239 / 0.3);
}

.bg-fuchsia-500\/40 {
  background-color: rgb(217 70 239 / 0.4);
}

.bg-fuchsia-500\/5 {
  background-color: rgb(217 70 239 / 0.05);
}

.bg-fuchsia-500\/50 {
  background-color: rgb(217 70 239 / 0.5);
}

.bg-fuchsia-500\/60 {
  background-color: rgb(217 70 239 / 0.6);
}

.bg-fuchsia-500\/70 {
  background-color: rgb(217 70 239 / 0.7);
}

.bg-fuchsia-500\/75 {
  background-color: rgb(217 70 239 / 0.75);
}

.bg-fuchsia-500\/80 {
  background-color: rgb(217 70 239 / 0.8);
}

.bg-fuchsia-500\/90 {
  background-color: rgb(217 70 239 / 0.9);
}

.bg-fuchsia-500\/95 {
  background-color: rgb(217 70 239 / 0.95);
}

.bg-fuchsia-600 {
  --tw-bg-opacity: 1;
  background-color: rgb(192 38 211 / var(--tw-bg-opacity));
}

.bg-fuchsia-600\/0 {
  background-color: rgb(192 38 211 / 0);
}

.bg-fuchsia-600\/10 {
  background-color: rgb(192 38 211 / 0.1);
}

.bg-fuchsia-600\/100 {
  background-color: rgb(192 38 211 / 1);
}

.bg-fuchsia-600\/20 {
  background-color: rgb(192 38 211 / 0.2);
}

.bg-fuchsia-600\/25 {
  background-color: rgb(192 38 211 / 0.25);
}

.bg-fuchsia-600\/30 {
  background-color: rgb(192 38 211 / 0.3);
}

.bg-fuchsia-600\/40 {
  background-color: rgb(192 38 211 / 0.4);
}

.bg-fuchsia-600\/5 {
  background-color: rgb(192 38 211 / 0.05);
}

.bg-fuchsia-600\/50 {
  background-color: rgb(192 38 211 / 0.5);
}

.bg-fuchsia-600\/60 {
  background-color: rgb(192 38 211 / 0.6);
}

.bg-fuchsia-600\/70 {
  background-color: rgb(192 38 211 / 0.7);
}

.bg-fuchsia-600\/75 {
  background-color: rgb(192 38 211 / 0.75);
}

.bg-fuchsia-600\/80 {
  background-color: rgb(192 38 211 / 0.8);
}

.bg-fuchsia-600\/90 {
  background-color: rgb(192 38 211 / 0.9);
}

.bg-fuchsia-600\/95 {
  background-color: rgb(192 38 211 / 0.95);
}

.bg-fuchsia-700 {
  --tw-bg-opacity: 1;
  background-color: rgb(162 28 175 / var(--tw-bg-opacity));
}

.bg-fuchsia-700\/0 {
  background-color: rgb(162 28 175 / 0);
}

.bg-fuchsia-700\/10 {
  background-color: rgb(162 28 175 / 0.1);
}

.bg-fuchsia-700\/100 {
  background-color: rgb(162 28 175 / 1);
}

.bg-fuchsia-700\/20 {
  background-color: rgb(162 28 175 / 0.2);
}

.bg-fuchsia-700\/25 {
  background-color: rgb(162 28 175 / 0.25);
}

.bg-fuchsia-700\/30 {
  background-color: rgb(162 28 175 / 0.3);
}

.bg-fuchsia-700\/40 {
  background-color: rgb(162 28 175 / 0.4);
}

.bg-fuchsia-700\/5 {
  background-color: rgb(162 28 175 / 0.05);
}

.bg-fuchsia-700\/50 {
  background-color: rgb(162 28 175 / 0.5);
}

.bg-fuchsia-700\/60 {
  background-color: rgb(162 28 175 / 0.6);
}

.bg-fuchsia-700\/70 {
  background-color: rgb(162 28 175 / 0.7);
}

.bg-fuchsia-700\/75 {
  background-color: rgb(162 28 175 / 0.75);
}

.bg-fuchsia-700\/80 {
  background-color: rgb(162 28 175 / 0.8);
}

.bg-fuchsia-700\/90 {
  background-color: rgb(162 28 175 / 0.9);
}

.bg-fuchsia-700\/95 {
  background-color: rgb(162 28 175 / 0.95);
}

.bg-fuchsia-800 {
  --tw-bg-opacity: 1;
  background-color: rgb(134 25 143 / var(--tw-bg-opacity));
}

.bg-fuchsia-800\/0 {
  background-color: rgb(134 25 143 / 0);
}

.bg-fuchsia-800\/10 {
  background-color: rgb(134 25 143 / 0.1);
}

.bg-fuchsia-800\/100 {
  background-color: rgb(134 25 143 / 1);
}

.bg-fuchsia-800\/20 {
  background-color: rgb(134 25 143 / 0.2);
}

.bg-fuchsia-800\/25 {
  background-color: rgb(134 25 143 / 0.25);
}

.bg-fuchsia-800\/30 {
  background-color: rgb(134 25 143 / 0.3);
}

.bg-fuchsia-800\/40 {
  background-color: rgb(134 25 143 / 0.4);
}

.bg-fuchsia-800\/5 {
  background-color: rgb(134 25 143 / 0.05);
}

.bg-fuchsia-800\/50 {
  background-color: rgb(134 25 143 / 0.5);
}

.bg-fuchsia-800\/60 {
  background-color: rgb(134 25 143 / 0.6);
}

.bg-fuchsia-800\/70 {
  background-color: rgb(134 25 143 / 0.7);
}

.bg-fuchsia-800\/75 {
  background-color: rgb(134 25 143 / 0.75);
}

.bg-fuchsia-800\/80 {
  background-color: rgb(134 25 143 / 0.8);
}

.bg-fuchsia-800\/90 {
  background-color: rgb(134 25 143 / 0.9);
}

.bg-fuchsia-800\/95 {
  background-color: rgb(134 25 143 / 0.95);
}

.bg-fuchsia-900 {
  --tw-bg-opacity: 1;
  background-color: rgb(112 26 117 / var(--tw-bg-opacity));
}

.bg-fuchsia-900\/0 {
  background-color: rgb(112 26 117 / 0);
}

.bg-fuchsia-900\/10 {
  background-color: rgb(112 26 117 / 0.1);
}

.bg-fuchsia-900\/100 {
  background-color: rgb(112 26 117 / 1);
}

.bg-fuchsia-900\/20 {
  background-color: rgb(112 26 117 / 0.2);
}

.bg-fuchsia-900\/25 {
  background-color: rgb(112 26 117 / 0.25);
}

.bg-fuchsia-900\/30 {
  background-color: rgb(112 26 117 / 0.3);
}

.bg-fuchsia-900\/40 {
  background-color: rgb(112 26 117 / 0.4);
}

.bg-fuchsia-900\/5 {
  background-color: rgb(112 26 117 / 0.05);
}

.bg-fuchsia-900\/50 {
  background-color: rgb(112 26 117 / 0.5);
}

.bg-fuchsia-900\/60 {
  background-color: rgb(112 26 117 / 0.6);
}

.bg-fuchsia-900\/70 {
  background-color: rgb(112 26 117 / 0.7);
}

.bg-fuchsia-900\/75 {
  background-color: rgb(112 26 117 / 0.75);
}

.bg-fuchsia-900\/80 {
  background-color: rgb(112 26 117 / 0.8);
}

.bg-fuchsia-900\/90 {
  background-color: rgb(112 26 117 / 0.9);
}

.bg-fuchsia-900\/95 {
  background-color: rgb(112 26 117 / 0.95);
}

.bg-fuchsia-950 {
  --tw-bg-opacity: 1;
  background-color: rgb(74 4 78 / var(--tw-bg-opacity));
}

.bg-fuchsia-950\/0 {
  background-color: rgb(74 4 78 / 0);
}

.bg-fuchsia-950\/10 {
  background-color: rgb(74 4 78 / 0.1);
}

.bg-fuchsia-950\/100 {
  background-color: rgb(74 4 78 / 1);
}

.bg-fuchsia-950\/20 {
  background-color: rgb(74 4 78 / 0.2);
}

.bg-fuchsia-950\/25 {
  background-color: rgb(74 4 78 / 0.25);
}

.bg-fuchsia-950\/30 {
  background-color: rgb(74 4 78 / 0.3);
}

.bg-fuchsia-950\/40 {
  background-color: rgb(74 4 78 / 0.4);
}

.bg-fuchsia-950\/5 {
  background-color: rgb(74 4 78 / 0.05);
}

.bg-fuchsia-950\/50 {
  background-color: rgb(74 4 78 / 0.5);
}

.bg-fuchsia-950\/60 {
  background-color: rgb(74 4 78 / 0.6);
}

.bg-fuchsia-950\/70 {
  background-color: rgb(74 4 78 / 0.7);
}

.bg-fuchsia-950\/75 {
  background-color: rgb(74 4 78 / 0.75);
}

.bg-fuchsia-950\/80 {
  background-color: rgb(74 4 78 / 0.8);
}

.bg-fuchsia-950\/90 {
  background-color: rgb(74 4 78 / 0.9);
}

.bg-fuchsia-950\/95 {
  background-color: rgb(74 4 78 / 0.95);
}

.bg-g1 {
  --tw-bg-opacity: 1;
  background-color: rgb(51 51 51 / var(--tw-bg-opacity));
}

.bg-g1\/0 {
  background-color: rgb(51 51 51 / 0);
}

.bg-g1\/10 {
  background-color: rgb(51 51 51 / 0.1);
}

.bg-g1\/100 {
  background-color: rgb(51 51 51 / 1);
}

.bg-g1\/20 {
  background-color: rgb(51 51 51 / 0.2);
}

.bg-g1\/25 {
  background-color: rgb(51 51 51 / 0.25);
}

.bg-g1\/30 {
  background-color: rgb(51 51 51 / 0.3);
}

.bg-g1\/40 {
  background-color: rgb(51 51 51 / 0.4);
}

.bg-g1\/5 {
  background-color: rgb(51 51 51 / 0.05);
}

.bg-g1\/50 {
  background-color: rgb(51 51 51 / 0.5);
}

.bg-g1\/60 {
  background-color: rgb(51 51 51 / 0.6);
}

.bg-g1\/70 {
  background-color: rgb(51 51 51 / 0.7);
}

.bg-g1\/75 {
  background-color: rgb(51 51 51 / 0.75);
}

.bg-g1\/80 {
  background-color: rgb(51 51 51 / 0.8);
}

.bg-g1\/90 {
  background-color: rgb(51 51 51 / 0.9);
}

.bg-g1\/95 {
  background-color: rgb(51 51 51 / 0.95);
}

.bg-g2 {
  --tw-bg-opacity: 1;
  background-color: rgb(102 102 102 / var(--tw-bg-opacity));
}

.bg-g2\/0 {
  background-color: rgb(102 102 102 / 0);
}

.bg-g2\/10 {
  background-color: rgb(102 102 102 / 0.1);
}

.bg-g2\/100 {
  background-color: rgb(102 102 102 / 1);
}

.bg-g2\/20 {
  background-color: rgb(102 102 102 / 0.2);
}

.bg-g2\/25 {
  background-color: rgb(102 102 102 / 0.25);
}

.bg-g2\/30 {
  background-color: rgb(102 102 102 / 0.3);
}

.bg-g2\/40 {
  background-color: rgb(102 102 102 / 0.4);
}

.bg-g2\/5 {
  background-color: rgb(102 102 102 / 0.05);
}

.bg-g2\/50 {
  background-color: rgb(102 102 102 / 0.5);
}

.bg-g2\/60 {
  background-color: rgb(102 102 102 / 0.6);
}

.bg-g2\/70 {
  background-color: rgb(102 102 102 / 0.7);
}

.bg-g2\/75 {
  background-color: rgb(102 102 102 / 0.75);
}

.bg-g2\/80 {
  background-color: rgb(102 102 102 / 0.8);
}

.bg-g2\/90 {
  background-color: rgb(102 102 102 / 0.9);
}

.bg-g2\/95 {
  background-color: rgb(102 102 102 / 0.95);
}

.bg-g3 {
  --tw-bg-opacity: 1;
  background-color: rgb(167 169 172 / var(--tw-bg-opacity));
}

.bg-g3\/0 {
  background-color: rgb(167 169 172 / 0);
}

.bg-g3\/10 {
  background-color: rgb(167 169 172 / 0.1);
}

.bg-g3\/100 {
  background-color: rgb(167 169 172 / 1);
}

.bg-g3\/20 {
  background-color: rgb(167 169 172 / 0.2);
}

.bg-g3\/25 {
  background-color: rgb(167 169 172 / 0.25);
}

.bg-g3\/30 {
  background-color: rgb(167 169 172 / 0.3);
}

.bg-g3\/40 {
  background-color: rgb(167 169 172 / 0.4);
}

.bg-g3\/5 {
  background-color: rgb(167 169 172 / 0.05);
}

.bg-g3\/50 {
  background-color: rgb(167 169 172 / 0.5);
}

.bg-g3\/60 {
  background-color: rgb(167 169 172 / 0.6);
}

.bg-g3\/70 {
  background-color: rgb(167 169 172 / 0.7);
}

.bg-g3\/75 {
  background-color: rgb(167 169 172 / 0.75);
}

.bg-g3\/80 {
  background-color: rgb(167 169 172 / 0.8);
}

.bg-g3\/90 {
  background-color: rgb(167 169 172 / 0.9);
}

.bg-g3\/95 {
  background-color: rgb(167 169 172 / 0.95);
}

.bg-g4 {
  --tw-bg-opacity: 1;
  background-color: rgb(201 201 201 / var(--tw-bg-opacity));
}

.bg-g4\/0 {
  background-color: rgb(201 201 201 / 0);
}

.bg-g4\/10 {
  background-color: rgb(201 201 201 / 0.1);
}

.bg-g4\/100 {
  background-color: rgb(201 201 201 / 1);
}

.bg-g4\/20 {
  background-color: rgb(201 201 201 / 0.2);
}

.bg-g4\/25 {
  background-color: rgb(201 201 201 / 0.25);
}

.bg-g4\/30 {
  background-color: rgb(201 201 201 / 0.3);
}

.bg-g4\/40 {
  background-color: rgb(201 201 201 / 0.4);
}

.bg-g4\/5 {
  background-color: rgb(201 201 201 / 0.05);
}

.bg-g4\/50 {
  background-color: rgb(201 201 201 / 0.5);
}

.bg-g4\/60 {
  background-color: rgb(201 201 201 / 0.6);
}

.bg-g4\/70 {
  background-color: rgb(201 201 201 / 0.7);
}

.bg-g4\/75 {
  background-color: rgb(201 201 201 / 0.75);
}

.bg-g4\/80 {
  background-color: rgb(201 201 201 / 0.8);
}

.bg-g4\/90 {
  background-color: rgb(201 201 201 / 0.9);
}

.bg-g4\/95 {
  background-color: rgb(201 201 201 / 0.95);
}

.bg-g5 {
  --tw-bg-opacity: 1;
  background-color: rgb(224 224 224 / var(--tw-bg-opacity));
}

.bg-g5\/0 {
  background-color: rgb(224 224 224 / 0);
}

.bg-g5\/10 {
  background-color: rgb(224 224 224 / 0.1);
}

.bg-g5\/100 {
  background-color: rgb(224 224 224 / 1);
}

.bg-g5\/20 {
  background-color: rgb(224 224 224 / 0.2);
}

.bg-g5\/25 {
  background-color: rgb(224 224 224 / 0.25);
}

.bg-g5\/30 {
  background-color: rgb(224 224 224 / 0.3);
}

.bg-g5\/40 {
  background-color: rgb(224 224 224 / 0.4);
}

.bg-g5\/5 {
  background-color: rgb(224 224 224 / 0.05);
}

.bg-g5\/50 {
  background-color: rgb(224 224 224 / 0.5);
}

.bg-g5\/60 {
  background-color: rgb(224 224 224 / 0.6);
}

.bg-g5\/70 {
  background-color: rgb(224 224 224 / 0.7);
}

.bg-g5\/75 {
  background-color: rgb(224 224 224 / 0.75);
}

.bg-g5\/80 {
  background-color: rgb(224 224 224 / 0.8);
}

.bg-g5\/90 {
  background-color: rgb(224 224 224 / 0.9);
}

.bg-g5\/95 {
  background-color: rgb(224 224 224 / 0.95);
}

.bg-g6 {
  --tw-bg-opacity: 1;
  background-color: rgb(242 242 242 / var(--tw-bg-opacity));
}

.bg-g6\/0 {
  background-color: rgb(242 242 242 / 0);
}

.bg-g6\/10 {
  background-color: rgb(242 242 242 / 0.1);
}

.bg-g6\/100 {
  background-color: rgb(242 242 242 / 1);
}

.bg-g6\/20 {
  background-color: rgb(242 242 242 / 0.2);
}

.bg-g6\/25 {
  background-color: rgb(242 242 242 / 0.25);
}

.bg-g6\/30 {
  background-color: rgb(242 242 242 / 0.3);
}

.bg-g6\/40 {
  background-color: rgb(242 242 242 / 0.4);
}

.bg-g6\/5 {
  background-color: rgb(242 242 242 / 0.05);
}

.bg-g6\/50 {
  background-color: rgb(242 242 242 / 0.5);
}

.bg-g6\/60 {
  background-color: rgb(242 242 242 / 0.6);
}

.bg-g6\/70 {
  background-color: rgb(242 242 242 / 0.7);
}

.bg-g6\/75 {
  background-color: rgb(242 242 242 / 0.75);
}

.bg-g6\/80 {
  background-color: rgb(242 242 242 / 0.8);
}

.bg-g6\/90 {
  background-color: rgb(242 242 242 / 0.9);
}

.bg-g6\/95 {
  background-color: rgb(242 242 242 / 0.95);
}

.bg-gray-10 {
  --tw-bg-opacity: 1;
  background-color: rgb(229 229 229 / var(--tw-bg-opacity));
}

.bg-gray-10\/0 {
  background-color: rgb(229 229 229 / 0);
}

.bg-gray-10\/10 {
  background-color: rgb(229 229 229 / 0.1);
}

.bg-gray-10\/100 {
  background-color: rgb(229 229 229 / 1);
}

.bg-gray-10\/20 {
  background-color: rgb(229 229 229 / 0.2);
}

.bg-gray-10\/25 {
  background-color: rgb(229 229 229 / 0.25);
}

.bg-gray-10\/30 {
  background-color: rgb(229 229 229 / 0.3);
}

.bg-gray-10\/40 {
  background-color: rgb(229 229 229 / 0.4);
}

.bg-gray-10\/5 {
  background-color: rgb(229 229 229 / 0.05);
}

.bg-gray-10\/50 {
  background-color: rgb(229 229 229 / 0.5);
}

.bg-gray-10\/60 {
  background-color: rgb(229 229 229 / 0.6);
}

.bg-gray-10\/70 {
  background-color: rgb(229 229 229 / 0.7);
}

.bg-gray-10\/75 {
  background-color: rgb(229 229 229 / 0.75);
}

.bg-gray-10\/80 {
  background-color: rgb(229 229 229 / 0.8);
}

.bg-gray-10\/90 {
  background-color: rgb(229 229 229 / 0.9);
}

.bg-gray-10\/95 {
  background-color: rgb(229 229 229 / 0.95);
}

.bg-gray-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(243 244 246 / var(--tw-bg-opacity));
}

.bg-gray-100\/0 {
  background-color: rgb(243 244 246 / 0);
}

.bg-gray-100\/10 {
  background-color: rgb(243 244 246 / 0.1);
}

.bg-gray-100\/100 {
  background-color: rgb(243 244 246 / 1);
}

.bg-gray-100\/20 {
  background-color: rgb(243 244 246 / 0.2);
}

.bg-gray-100\/25 {
  background-color: rgb(243 244 246 / 0.25);
}

.bg-gray-100\/30 {
  background-color: rgb(243 244 246 / 0.3);
}

.bg-gray-100\/40 {
  background-color: rgb(243 244 246 / 0.4);
}

.bg-gray-100\/5 {
  background-color: rgb(243 244 246 / 0.05);
}

.bg-gray-100\/50 {
  background-color: rgb(243 244 246 / 0.5);
}

.bg-gray-100\/60 {
  background-color: rgb(243 244 246 / 0.6);
}

.bg-gray-100\/70 {
  background-color: rgb(243 244 246 / 0.7);
}

.bg-gray-100\/75 {
  background-color: rgb(243 244 246 / 0.75);
}

.bg-gray-100\/80 {
  background-color: rgb(243 244 246 / 0.8);
}

.bg-gray-100\/90 {
  background-color: rgb(243 244 246 / 0.9);
}

.bg-gray-100\/95 {
  background-color: rgb(243 244 246 / 0.95);
}

.bg-gray-20 {
  --tw-bg-opacity: 1;
  background-color: rgb(204 204 204 / var(--tw-bg-opacity));
}

.bg-gray-20\/0 {
  background-color: rgb(204 204 204 / 0);
}

.bg-gray-20\/10 {
  background-color: rgb(204 204 204 / 0.1);
}

.bg-gray-20\/100 {
  background-color: rgb(204 204 204 / 1);
}

.bg-gray-20\/20 {
  background-color: rgb(204 204 204 / 0.2);
}

.bg-gray-20\/25 {
  background-color: rgb(204 204 204 / 0.25);
}

.bg-gray-20\/30 {
  background-color: rgb(204 204 204 / 0.3);
}

.bg-gray-20\/40 {
  background-color: rgb(204 204 204 / 0.4);
}

.bg-gray-20\/5 {
  background-color: rgb(204 204 204 / 0.05);
}

.bg-gray-20\/50 {
  background-color: rgb(204 204 204 / 0.5);
}

.bg-gray-20\/60 {
  background-color: rgb(204 204 204 / 0.6);
}

.bg-gray-20\/70 {
  background-color: rgb(204 204 204 / 0.7);
}

.bg-gray-20\/75 {
  background-color: rgb(204 204 204 / 0.75);
}

.bg-gray-20\/80 {
  background-color: rgb(204 204 204 / 0.8);
}

.bg-gray-20\/90 {
  background-color: rgb(204 204 204 / 0.9);
}

.bg-gray-20\/95 {
  background-color: rgb(204 204 204 / 0.95);
}

.bg-gray-200 {
  --tw-bg-opacity: 1;
  background-color: rgb(229 231 235 / var(--tw-bg-opacity));
}

.bg-gray-200\/0 {
  background-color: rgb(229 231 235 / 0);
}

.bg-gray-200\/10 {
  background-color: rgb(229 231 235 / 0.1);
}

.bg-gray-200\/100 {
  background-color: rgb(229 231 235 / 1);
}

.bg-gray-200\/20 {
  background-color: rgb(229 231 235 / 0.2);
}

.bg-gray-200\/25 {
  background-color: rgb(229 231 235 / 0.25);
}

.bg-gray-200\/30 {
  background-color: rgb(229 231 235 / 0.3);
}

.bg-gray-200\/40 {
  background-color: rgb(229 231 235 / 0.4);
}

.bg-gray-200\/5 {
  background-color: rgb(229 231 235 / 0.05);
}

.bg-gray-200\/50 {
  background-color: rgb(229 231 235 / 0.5);
}

.bg-gray-200\/60 {
  background-color: rgb(229 231 235 / 0.6);
}

.bg-gray-200\/70 {
  background-color: rgb(229 231 235 / 0.7);
}

.bg-gray-200\/75 {
  background-color: rgb(229 231 235 / 0.75);
}

.bg-gray-200\/80 {
  background-color: rgb(229 231 235 / 0.8);
}

.bg-gray-200\/90 {
  background-color: rgb(229 231 235 / 0.9);
}

.bg-gray-200\/95 {
  background-color: rgb(229 231 235 / 0.95);
}

.bg-gray-30 {
  --tw-bg-opacity: 1;
  background-color: rgb(178 178 178 / var(--tw-bg-opacity));
}

.bg-gray-30\/0 {
  background-color: rgb(178 178 178 / 0);
}

.bg-gray-30\/10 {
  background-color: rgb(178 178 178 / 0.1);
}

.bg-gray-30\/100 {
  background-color: rgb(178 178 178 / 1);
}

.bg-gray-30\/20 {
  background-color: rgb(178 178 178 / 0.2);
}

.bg-gray-30\/25 {
  background-color: rgb(178 178 178 / 0.25);
}

.bg-gray-30\/30 {
  background-color: rgb(178 178 178 / 0.3);
}

.bg-gray-30\/40 {
  background-color: rgb(178 178 178 / 0.4);
}

.bg-gray-30\/5 {
  background-color: rgb(178 178 178 / 0.05);
}

.bg-gray-30\/50 {
  background-color: rgb(178 178 178 / 0.5);
}

.bg-gray-30\/60 {
  background-color: rgb(178 178 178 / 0.6);
}

.bg-gray-30\/70 {
  background-color: rgb(178 178 178 / 0.7);
}

.bg-gray-30\/75 {
  background-color: rgb(178 178 178 / 0.75);
}

.bg-gray-30\/80 {
  background-color: rgb(178 178 178 / 0.8);
}

.bg-gray-30\/90 {
  background-color: rgb(178 178 178 / 0.9);
}

.bg-gray-30\/95 {
  background-color: rgb(178 178 178 / 0.95);
}

.bg-gray-300 {
  --tw-bg-opacity: 1;
  background-color: rgb(209 213 219 / var(--tw-bg-opacity));
}

.bg-gray-300\/0 {
  background-color: rgb(209 213 219 / 0);
}

.bg-gray-300\/10 {
  background-color: rgb(209 213 219 / 0.1);
}

.bg-gray-300\/100 {
  background-color: rgb(209 213 219 / 1);
}

.bg-gray-300\/20 {
  background-color: rgb(209 213 219 / 0.2);
}

.bg-gray-300\/25 {
  background-color: rgb(209 213 219 / 0.25);
}

.bg-gray-300\/30 {
  background-color: rgb(209 213 219 / 0.3);
}

.bg-gray-300\/40 {
  background-color: rgb(209 213 219 / 0.4);
}

.bg-gray-300\/5 {
  background-color: rgb(209 213 219 / 0.05);
}

.bg-gray-300\/50 {
  background-color: rgb(209 213 219 / 0.5);
}

.bg-gray-300\/60 {
  background-color: rgb(209 213 219 / 0.6);
}

.bg-gray-300\/70 {
  background-color: rgb(209 213 219 / 0.7);
}

.bg-gray-300\/75 {
  background-color: rgb(209 213 219 / 0.75);
}

.bg-gray-300\/80 {
  background-color: rgb(209 213 219 / 0.8);
}

.bg-gray-300\/90 {
  background-color: rgb(209 213 219 / 0.9);
}

.bg-gray-300\/95 {
  background-color: rgb(209 213 219 / 0.95);
}

.bg-gray-40 {
  --tw-bg-opacity: 1;
  background-color: rgb(153 153 153 / var(--tw-bg-opacity));
}

.bg-gray-40\/0 {
  background-color: rgb(153 153 153 / 0);
}

.bg-gray-40\/10 {
  background-color: rgb(153 153 153 / 0.1);
}

.bg-gray-40\/100 {
  background-color: rgb(153 153 153 / 1);
}

.bg-gray-40\/20 {
  background-color: rgb(153 153 153 / 0.2);
}

.bg-gray-40\/25 {
  background-color: rgb(153 153 153 / 0.25);
}

.bg-gray-40\/30 {
  background-color: rgb(153 153 153 / 0.3);
}

.bg-gray-40\/40 {
  background-color: rgb(153 153 153 / 0.4);
}

.bg-gray-40\/5 {
  background-color: rgb(153 153 153 / 0.05);
}

.bg-gray-40\/50 {
  background-color: rgb(153 153 153 / 0.5);
}

.bg-gray-40\/60 {
  background-color: rgb(153 153 153 / 0.6);
}

.bg-gray-40\/70 {
  background-color: rgb(153 153 153 / 0.7);
}

.bg-gray-40\/75 {
  background-color: rgb(153 153 153 / 0.75);
}

.bg-gray-40\/80 {
  background-color: rgb(153 153 153 / 0.8);
}

.bg-gray-40\/90 {
  background-color: rgb(153 153 153 / 0.9);
}

.bg-gray-40\/95 {
  background-color: rgb(153 153 153 / 0.95);
}

.bg-gray-400 {
  --tw-bg-opacity: 1;
  background-color: rgb(156 163 175 / var(--tw-bg-opacity));
}

.bg-gray-400\/0 {
  background-color: rgb(156 163 175 / 0);
}

.bg-gray-400\/10 {
  background-color: rgb(156 163 175 / 0.1);
}

.bg-gray-400\/100 {
  background-color: rgb(156 163 175 / 1);
}

.bg-gray-400\/20 {
  background-color: rgb(156 163 175 / 0.2);
}

.bg-gray-400\/25 {
  background-color: rgb(156 163 175 / 0.25);
}

.bg-gray-400\/30 {
  background-color: rgb(156 163 175 / 0.3);
}

.bg-gray-400\/40 {
  background-color: rgb(156 163 175 / 0.4);
}

.bg-gray-400\/5 {
  background-color: rgb(156 163 175 / 0.05);
}

.bg-gray-400\/50 {
  background-color: rgb(156 163 175 / 0.5);
}

.bg-gray-400\/60 {
  background-color: rgb(156 163 175 / 0.6);
}

.bg-gray-400\/70 {
  background-color: rgb(156 163 175 / 0.7);
}

.bg-gray-400\/75 {
  background-color: rgb(156 163 175 / 0.75);
}

.bg-gray-400\/80 {
  background-color: rgb(156 163 175 / 0.8);
}

.bg-gray-400\/90 {
  background-color: rgb(156 163 175 / 0.9);
}

.bg-gray-400\/95 {
  background-color: rgb(156 163 175 / 0.95);
}

.bg-gray-5 {
  --tw-bg-opacity: 1;
  background-color: rgb(242 242 242 / var(--tw-bg-opacity));
}

.bg-gray-5\/0 {
  background-color: rgb(242 242 242 / 0);
}

.bg-gray-5\/10 {
  background-color: rgb(242 242 242 / 0.1);
}

.bg-gray-5\/100 {
  background-color: rgb(242 242 242 / 1);
}

.bg-gray-5\/20 {
  background-color: rgb(242 242 242 / 0.2);
}

.bg-gray-5\/25 {
  background-color: rgb(242 242 242 / 0.25);
}

.bg-gray-5\/30 {
  background-color: rgb(242 242 242 / 0.3);
}

.bg-gray-5\/40 {
  background-color: rgb(242 242 242 / 0.4);
}

.bg-gray-5\/5 {
  background-color: rgb(242 242 242 / 0.05);
}

.bg-gray-5\/50 {
  background-color: rgb(242 242 242 / 0.5);
}

.bg-gray-5\/60 {
  background-color: rgb(242 242 242 / 0.6);
}

.bg-gray-5\/70 {
  background-color: rgb(242 242 242 / 0.7);
}

.bg-gray-5\/75 {
  background-color: rgb(242 242 242 / 0.75);
}

.bg-gray-5\/80 {
  background-color: rgb(242 242 242 / 0.8);
}

.bg-gray-5\/90 {
  background-color: rgb(242 242 242 / 0.9);
}

.bg-gray-5\/95 {
  background-color: rgb(242 242 242 / 0.95);
}

.bg-gray-50 {
  --tw-bg-opacity: 1;
  background-color: rgb(127 127 127 / var(--tw-bg-opacity));
}

.bg-gray-50\/0 {
  background-color: rgb(127 127 127 / 0);
}

.bg-gray-50\/10 {
  background-color: rgb(127 127 127 / 0.1);
}

.bg-gray-50\/100 {
  background-color: rgb(127 127 127 / 1);
}

.bg-gray-50\/20 {
  background-color: rgb(127 127 127 / 0.2);
}

.bg-gray-50\/25 {
  background-color: rgb(127 127 127 / 0.25);
}

.bg-gray-50\/30 {
  background-color: rgb(127 127 127 / 0.3);
}

.bg-gray-50\/40 {
  background-color: rgb(127 127 127 / 0.4);
}

.bg-gray-50\/5 {
  background-color: rgb(127 127 127 / 0.05);
}

.bg-gray-50\/50 {
  background-color: rgb(127 127 127 / 0.5);
}

.bg-gray-50\/60 {
  background-color: rgb(127 127 127 / 0.6);
}

.bg-gray-50\/70 {
  background-color: rgb(127 127 127 / 0.7);
}

.bg-gray-50\/75 {
  background-color: rgb(127 127 127 / 0.75);
}

.bg-gray-50\/80 {
  background-color: rgb(127 127 127 / 0.8);
}

.bg-gray-50\/90 {
  background-color: rgb(127 127 127 / 0.9);
}

.bg-gray-50\/95 {
  background-color: rgb(127 127 127 / 0.95);
}

.bg-gray-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(107 114 128 / var(--tw-bg-opacity));
}

.bg-gray-500\/0 {
  background-color: rgb(107 114 128 / 0);
}

.bg-gray-500\/10 {
  background-color: rgb(107 114 128 / 0.1);
}

.bg-gray-500\/100 {
  background-color: rgb(107 114 128 / 1);
}

.bg-gray-500\/20 {
  background-color: rgb(107 114 128 / 0.2);
}

.bg-gray-500\/25 {
  background-color: rgb(107 114 128 / 0.25);
}

.bg-gray-500\/30 {
  background-color: rgb(107 114 128 / 0.3);
}

.bg-gray-500\/40 {
  background-color: rgb(107 114 128 / 0.4);
}

.bg-gray-500\/5 {
  background-color: rgb(107 114 128 / 0.05);
}

.bg-gray-500\/50 {
  background-color: rgb(107 114 128 / 0.5);
}

.bg-gray-500\/60 {
  background-color: rgb(107 114 128 / 0.6);
}

.bg-gray-500\/70 {
  background-color: rgb(107 114 128 / 0.7);
}

.bg-gray-500\/75 {
  background-color: rgb(107 114 128 / 0.75);
}

.bg-gray-500\/80 {
  background-color: rgb(107 114 128 / 0.8);
}

.bg-gray-500\/90 {
  background-color: rgb(107 114 128 / 0.9);
}

.bg-gray-500\/95 {
  background-color: rgb(107 114 128 / 0.95);
}

.bg-gray-54 {
  --tw-bg-opacity: 1;
  background-color: rgb(117 117 117 / var(--tw-bg-opacity));
}

.bg-gray-54\/0 {
  background-color: rgb(117 117 117 / 0);
}

.bg-gray-54\/10 {
  background-color: rgb(117 117 117 / 0.1);
}

.bg-gray-54\/100 {
  background-color: rgb(117 117 117 / 1);
}

.bg-gray-54\/20 {
  background-color: rgb(117 117 117 / 0.2);
}

.bg-gray-54\/25 {
  background-color: rgb(117 117 117 / 0.25);
}

.bg-gray-54\/30 {
  background-color: rgb(117 117 117 / 0.3);
}

.bg-gray-54\/40 {
  background-color: rgb(117 117 117 / 0.4);
}

.bg-gray-54\/5 {
  background-color: rgb(117 117 117 / 0.05);
}

.bg-gray-54\/50 {
  background-color: rgb(117 117 117 / 0.5);
}

.bg-gray-54\/60 {
  background-color: rgb(117 117 117 / 0.6);
}

.bg-gray-54\/70 {
  background-color: rgb(117 117 117 / 0.7);
}

.bg-gray-54\/75 {
  background-color: rgb(117 117 117 / 0.75);
}

.bg-gray-54\/80 {
  background-color: rgb(117 117 117 / 0.8);
}

.bg-gray-54\/90 {
  background-color: rgb(117 117 117 / 0.9);
}

.bg-gray-54\/95 {
  background-color: rgb(117 117 117 / 0.95);
}

.bg-gray-60 {
  --tw-bg-opacity: 1;
  background-color: rgb(102 102 102 / var(--tw-bg-opacity));
}

.bg-gray-60\/0 {
  background-color: rgb(102 102 102 / 0);
}

.bg-gray-60\/10 {
  background-color: rgb(102 102 102 / 0.1);
}

.bg-gray-60\/100 {
  background-color: rgb(102 102 102 / 1);
}

.bg-gray-60\/20 {
  background-color: rgb(102 102 102 / 0.2);
}

.bg-gray-60\/25 {
  background-color: rgb(102 102 102 / 0.25);
}

.bg-gray-60\/30 {
  background-color: rgb(102 102 102 / 0.3);
}

.bg-gray-60\/40 {
  background-color: rgb(102 102 102 / 0.4);
}

.bg-gray-60\/5 {
  background-color: rgb(102 102 102 / 0.05);
}

.bg-gray-60\/50 {
  background-color: rgb(102 102 102 / 0.5);
}

.bg-gray-60\/60 {
  background-color: rgb(102 102 102 / 0.6);
}

.bg-gray-60\/70 {
  background-color: rgb(102 102 102 / 0.7);
}

.bg-gray-60\/75 {
  background-color: rgb(102 102 102 / 0.75);
}

.bg-gray-60\/80 {
  background-color: rgb(102 102 102 / 0.8);
}

.bg-gray-60\/90 {
  background-color: rgb(102 102 102 / 0.9);
}

.bg-gray-60\/95 {
  background-color: rgb(102 102 102 / 0.95);
}

.bg-gray-600 {
  --tw-bg-opacity: 1;
  background-color: rgb(75 85 99 / var(--tw-bg-opacity));
}

.bg-gray-600\/0 {
  background-color: rgb(75 85 99 / 0);
}

.bg-gray-600\/10 {
  background-color: rgb(75 85 99 / 0.1);
}

.bg-gray-600\/100 {
  background-color: rgb(75 85 99 / 1);
}

.bg-gray-600\/20 {
  background-color: rgb(75 85 99 / 0.2);
}

.bg-gray-600\/25 {
  background-color: rgb(75 85 99 / 0.25);
}

.bg-gray-600\/30 {
  background-color: rgb(75 85 99 / 0.3);
}

.bg-gray-600\/40 {
  background-color: rgb(75 85 99 / 0.4);
}

.bg-gray-600\/5 {
  background-color: rgb(75 85 99 / 0.05);
}

.bg-gray-600\/50 {
  background-color: rgb(75 85 99 / 0.5);
}

.bg-gray-600\/60 {
  background-color: rgb(75 85 99 / 0.6);
}

.bg-gray-600\/70 {
  background-color: rgb(75 85 99 / 0.7);
}

.bg-gray-600\/75 {
  background-color: rgb(75 85 99 / 0.75);
}

.bg-gray-600\/80 {
  background-color: rgb(75 85 99 / 0.8);
}

.bg-gray-600\/90 {
  background-color: rgb(75 85 99 / 0.9);
}

.bg-gray-600\/95 {
  background-color: rgb(75 85 99 / 0.95);
}

.bg-gray-70 {
  --tw-bg-opacity: 1;
  background-color: rgb(76 76 76 / var(--tw-bg-opacity));
}

.bg-gray-70\/0 {
  background-color: rgb(76 76 76 / 0);
}

.bg-gray-70\/10 {
  background-color: rgb(76 76 76 / 0.1);
}

.bg-gray-70\/100 {
  background-color: rgb(76 76 76 / 1);
}

.bg-gray-70\/20 {
  background-color: rgb(76 76 76 / 0.2);
}

.bg-gray-70\/25 {
  background-color: rgb(76 76 76 / 0.25);
}

.bg-gray-70\/30 {
  background-color: rgb(76 76 76 / 0.3);
}

.bg-gray-70\/40 {
  background-color: rgb(76 76 76 / 0.4);
}

.bg-gray-70\/5 {
  background-color: rgb(76 76 76 / 0.05);
}

.bg-gray-70\/50 {
  background-color: rgb(76 76 76 / 0.5);
}

.bg-gray-70\/60 {
  background-color: rgb(76 76 76 / 0.6);
}

.bg-gray-70\/70 {
  background-color: rgb(76 76 76 / 0.7);
}

.bg-gray-70\/75 {
  background-color: rgb(76 76 76 / 0.75);
}

.bg-gray-70\/80 {
  background-color: rgb(76 76 76 / 0.8);
}

.bg-gray-70\/90 {
  background-color: rgb(76 76 76 / 0.9);
}

.bg-gray-70\/95 {
  background-color: rgb(76 76 76 / 0.95);
}

.bg-gray-700 {
  --tw-bg-opacity: 1;
  background-color: rgb(55 65 81 / var(--tw-bg-opacity));
}

.bg-gray-700\/0 {
  background-color: rgb(55 65 81 / 0);
}

.bg-gray-700\/10 {
  background-color: rgb(55 65 81 / 0.1);
}

.bg-gray-700\/100 {
  background-color: rgb(55 65 81 / 1);
}

.bg-gray-700\/20 {
  background-color: rgb(55 65 81 / 0.2);
}

.bg-gray-700\/25 {
  background-color: rgb(55 65 81 / 0.25);
}

.bg-gray-700\/30 {
  background-color: rgb(55 65 81 / 0.3);
}

.bg-gray-700\/40 {
  background-color: rgb(55 65 81 / 0.4);
}

.bg-gray-700\/5 {
  background-color: rgb(55 65 81 / 0.05);
}

.bg-gray-700\/50 {
  background-color: rgb(55 65 81 / 0.5);
}

.bg-gray-700\/60 {
  background-color: rgb(55 65 81 / 0.6);
}

.bg-gray-700\/70 {
  background-color: rgb(55 65 81 / 0.7);
}

.bg-gray-700\/75 {
  background-color: rgb(55 65 81 / 0.75);
}

.bg-gray-700\/80 {
  background-color: rgb(55 65 81 / 0.8);
}

.bg-gray-700\/90 {
  background-color: rgb(55 65 81 / 0.9);
}

.bg-gray-700\/95 {
  background-color: rgb(55 65 81 / 0.95);
}

.bg-gray-80 {
  --tw-bg-opacity: 1;
  background-color: rgb(51 51 51 / var(--tw-bg-opacity));
}

.bg-gray-80\/0 {
  background-color: rgb(51 51 51 / 0);
}

.bg-gray-80\/10 {
  background-color: rgb(51 51 51 / 0.1);
}

.bg-gray-80\/100 {
  background-color: rgb(51 51 51 / 1);
}

.bg-gray-80\/20 {
  background-color: rgb(51 51 51 / 0.2);
}

.bg-gray-80\/25 {
  background-color: rgb(51 51 51 / 0.25);
}

.bg-gray-80\/30 {
  background-color: rgb(51 51 51 / 0.3);
}

.bg-gray-80\/40 {
  background-color: rgb(51 51 51 / 0.4);
}

.bg-gray-80\/5 {
  background-color: rgb(51 51 51 / 0.05);
}

.bg-gray-80\/50 {
  background-color: rgb(51 51 51 / 0.5);
}

.bg-gray-80\/60 {
  background-color: rgb(51 51 51 / 0.6);
}

.bg-gray-80\/70 {
  background-color: rgb(51 51 51 / 0.7);
}

.bg-gray-80\/75 {
  background-color: rgb(51 51 51 / 0.75);
}

.bg-gray-80\/80 {
  background-color: rgb(51 51 51 / 0.8);
}

.bg-gray-80\/90 {
  background-color: rgb(51 51 51 / 0.9);
}

.bg-gray-80\/95 {
  background-color: rgb(51 51 51 / 0.95);
}

.bg-gray-800 {
  --tw-bg-opacity: 1;
  background-color: rgb(31 41 55 / var(--tw-bg-opacity));
}

.bg-gray-800\/0 {
  background-color: rgb(31 41 55 / 0);
}

.bg-gray-800\/10 {
  background-color: rgb(31 41 55 / 0.1);
}

.bg-gray-800\/100 {
  background-color: rgb(31 41 55 / 1);
}

.bg-gray-800\/20 {
  background-color: rgb(31 41 55 / 0.2);
}

.bg-gray-800\/25 {
  background-color: rgb(31 41 55 / 0.25);
}

.bg-gray-800\/30 {
  background-color: rgb(31 41 55 / 0.3);
}

.bg-gray-800\/40 {
  background-color: rgb(31 41 55 / 0.4);
}

.bg-gray-800\/5 {
  background-color: rgb(31 41 55 / 0.05);
}

.bg-gray-800\/50 {
  background-color: rgb(31 41 55 / 0.5);
}

.bg-gray-800\/60 {
  background-color: rgb(31 41 55 / 0.6);
}

.bg-gray-800\/70 {
  background-color: rgb(31 41 55 / 0.7);
}

.bg-gray-800\/75 {
  background-color: rgb(31 41 55 / 0.75);
}

.bg-gray-800\/80 {
  background-color: rgb(31 41 55 / 0.8);
}

.bg-gray-800\/90 {
  background-color: rgb(31 41 55 / 0.9);
}

.bg-gray-800\/95 {
  background-color: rgb(31 41 55 / 0.95);
}

.bg-gray-90 {
  --tw-bg-opacity: 1;
  background-color: rgb(25 25 25 / var(--tw-bg-opacity));
}

.bg-gray-90\/0 {
  background-color: rgb(25 25 25 / 0);
}

.bg-gray-90\/10 {
  background-color: rgb(25 25 25 / 0.1);
}

.bg-gray-90\/100 {
  background-color: rgb(25 25 25 / 1);
}

.bg-gray-90\/20 {
  background-color: rgb(25 25 25 / 0.2);
}

.bg-gray-90\/25 {
  background-color: rgb(25 25 25 / 0.25);
}

.bg-gray-90\/30 {
  background-color: rgb(25 25 25 / 0.3);
}

.bg-gray-90\/40 {
  background-color: rgb(25 25 25 / 0.4);
}

.bg-gray-90\/5 {
  background-color: rgb(25 25 25 / 0.05);
}

.bg-gray-90\/50 {
  background-color: rgb(25 25 25 / 0.5);
}

.bg-gray-90\/60 {
  background-color: rgb(25 25 25 / 0.6);
}

.bg-gray-90\/70 {
  background-color: rgb(25 25 25 / 0.7);
}

.bg-gray-90\/75 {
  background-color: rgb(25 25 25 / 0.75);
}

.bg-gray-90\/80 {
  background-color: rgb(25 25 25 / 0.8);
}

.bg-gray-90\/90 {
  background-color: rgb(25 25 25 / 0.9);
}

.bg-gray-90\/95 {
  background-color: rgb(25 25 25 / 0.95);
}

.bg-gray-900 {
  --tw-bg-opacity: 1;
  background-color: rgb(17 24 39 / var(--tw-bg-opacity));
}

.bg-gray-900\/0 {
  background-color: rgb(17 24 39 / 0);
}

.bg-gray-900\/10 {
  background-color: rgb(17 24 39 / 0.1);
}

.bg-gray-900\/100 {
  background-color: rgb(17 24 39 / 1);
}

.bg-gray-900\/20 {
  background-color: rgb(17 24 39 / 0.2);
}

.bg-gray-900\/25 {
  background-color: rgb(17 24 39 / 0.25);
}

.bg-gray-900\/30 {
  background-color: rgb(17 24 39 / 0.3);
}

.bg-gray-900\/40 {
  background-color: rgb(17 24 39 / 0.4);
}

.bg-gray-900\/5 {
  background-color: rgb(17 24 39 / 0.05);
}

.bg-gray-900\/50 {
  background-color: rgb(17 24 39 / 0.5);
}

.bg-gray-900\/60 {
  background-color: rgb(17 24 39 / 0.6);
}

.bg-gray-900\/70 {
  background-color: rgb(17 24 39 / 0.7);
}

.bg-gray-900\/75 {
  background-color: rgb(17 24 39 / 0.75);
}

.bg-gray-900\/80 {
  background-color: rgb(17 24 39 / 0.8);
}

.bg-gray-900\/90 {
  background-color: rgb(17 24 39 / 0.9);
}

.bg-gray-900\/95 {
  background-color: rgb(17 24 39 / 0.95);
}

.bg-gray-950 {
  --tw-bg-opacity: 1;
  background-color: rgb(3 7 18 / var(--tw-bg-opacity));
}

.bg-gray-950\/0 {
  background-color: rgb(3 7 18 / 0);
}

.bg-gray-950\/10 {
  background-color: rgb(3 7 18 / 0.1);
}

.bg-gray-950\/100 {
  background-color: rgb(3 7 18 / 1);
}

.bg-gray-950\/20 {
  background-color: rgb(3 7 18 / 0.2);
}

.bg-gray-950\/25 {
  background-color: rgb(3 7 18 / 0.25);
}

.bg-gray-950\/30 {
  background-color: rgb(3 7 18 / 0.3);
}

.bg-gray-950\/40 {
  background-color: rgb(3 7 18 / 0.4);
}

.bg-gray-950\/5 {
  background-color: rgb(3 7 18 / 0.05);
}

.bg-gray-950\/50 {
  background-color: rgb(3 7 18 / 0.5);
}

.bg-gray-950\/60 {
  background-color: rgb(3 7 18 / 0.6);
}

.bg-gray-950\/70 {
  background-color: rgb(3 7 18 / 0.7);
}

.bg-gray-950\/75 {
  background-color: rgb(3 7 18 / 0.75);
}

.bg-gray-950\/80 {
  background-color: rgb(3 7 18 / 0.8);
}

.bg-gray-950\/90 {
  background-color: rgb(3 7 18 / 0.9);
}

.bg-gray-950\/95 {
  background-color: rgb(3 7 18 / 0.95);
}

.bg-green-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(220 252 231 / var(--tw-bg-opacity));
}

.bg-green-100\/0 {
  background-color: rgb(220 252 231 / 0);
}

.bg-green-100\/10 {
  background-color: rgb(220 252 231 / 0.1);
}

.bg-green-100\/100 {
  background-color: rgb(220 252 231 / 1);
}

.bg-green-100\/20 {
  background-color: rgb(220 252 231 / 0.2);
}

.bg-green-100\/25 {
  background-color: rgb(220 252 231 / 0.25);
}

.bg-green-100\/30 {
  background-color: rgb(220 252 231 / 0.3);
}

.bg-green-100\/40 {
  background-color: rgb(220 252 231 / 0.4);
}

.bg-green-100\/5 {
  background-color: rgb(220 252 231 / 0.05);
}

.bg-green-100\/50 {
  background-color: rgb(220 252 231 / 0.5);
}

.bg-green-100\/60 {
  background-color: rgb(220 252 231 / 0.6);
}

.bg-green-100\/70 {
  background-color: rgb(220 252 231 / 0.7);
}

.bg-green-100\/75 {
  background-color: rgb(220 252 231 / 0.75);
}

.bg-green-100\/80 {
  background-color: rgb(220 252 231 / 0.8);
}

.bg-green-100\/90 {
  background-color: rgb(220 252 231 / 0.9);
}

.bg-green-100\/95 {
  background-color: rgb(220 252 231 / 0.95);
}

.bg-green-200 {
  --tw-bg-opacity: 1;
  background-color: rgb(187 247 208 / var(--tw-bg-opacity));
}

.bg-green-200\/0 {
  background-color: rgb(187 247 208 / 0);
}

.bg-green-200\/10 {
  background-color: rgb(187 247 208 / 0.1);
}

.bg-green-200\/100 {
  background-color: rgb(187 247 208 / 1);
}

.bg-green-200\/20 {
  background-color: rgb(187 247 208 / 0.2);
}

.bg-green-200\/25 {
  background-color: rgb(187 247 208 / 0.25);
}

.bg-green-200\/30 {
  background-color: rgb(187 247 208 / 0.3);
}

.bg-green-200\/40 {
  background-color: rgb(187 247 208 / 0.4);
}

.bg-green-200\/5 {
  background-color: rgb(187 247 208 / 0.05);
}

.bg-green-200\/50 {
  background-color: rgb(187 247 208 / 0.5);
}

.bg-green-200\/60 {
  background-color: rgb(187 247 208 / 0.6);
}

.bg-green-200\/70 {
  background-color: rgb(187 247 208 / 0.7);
}

.bg-green-200\/75 {
  background-color: rgb(187 247 208 / 0.75);
}

.bg-green-200\/80 {
  background-color: rgb(187 247 208 / 0.8);
}

.bg-green-200\/90 {
  background-color: rgb(187 247 208 / 0.9);
}

.bg-green-200\/95 {
  background-color: rgb(187 247 208 / 0.95);
}

.bg-green-300 {
  --tw-bg-opacity: 1;
  background-color: rgb(134 239 172 / var(--tw-bg-opacity));
}

.bg-green-300\/0 {
  background-color: rgb(134 239 172 / 0);
}

.bg-green-300\/10 {
  background-color: rgb(134 239 172 / 0.1);
}

.bg-green-300\/100 {
  background-color: rgb(134 239 172 / 1);
}

.bg-green-300\/20 {
  background-color: rgb(134 239 172 / 0.2);
}

.bg-green-300\/25 {
  background-color: rgb(134 239 172 / 0.25);
}

.bg-green-300\/30 {
  background-color: rgb(134 239 172 / 0.3);
}

.bg-green-300\/40 {
  background-color: rgb(134 239 172 / 0.4);
}

.bg-green-300\/5 {
  background-color: rgb(134 239 172 / 0.05);
}

.bg-green-300\/50 {
  background-color: rgb(134 239 172 / 0.5);
}

.bg-green-300\/60 {
  background-color: rgb(134 239 172 / 0.6);
}

.bg-green-300\/70 {
  background-color: rgb(134 239 172 / 0.7);
}

.bg-green-300\/75 {
  background-color: rgb(134 239 172 / 0.75);
}

.bg-green-300\/80 {
  background-color: rgb(134 239 172 / 0.8);
}

.bg-green-300\/90 {
  background-color: rgb(134 239 172 / 0.9);
}

.bg-green-300\/95 {
  background-color: rgb(134 239 172 / 0.95);
}

.bg-green-400 {
  --tw-bg-opacity: 1;
  background-color: rgb(74 222 128 / var(--tw-bg-opacity));
}

.bg-green-400\/0 {
  background-color: rgb(74 222 128 / 0);
}

.bg-green-400\/10 {
  background-color: rgb(74 222 128 / 0.1);
}

.bg-green-400\/100 {
  background-color: rgb(74 222 128 / 1);
}

.bg-green-400\/20 {
  background-color: rgb(74 222 128 / 0.2);
}

.bg-green-400\/25 {
  background-color: rgb(74 222 128 / 0.25);
}

.bg-green-400\/30 {
  background-color: rgb(74 222 128 / 0.3);
}

.bg-green-400\/40 {
  background-color: rgb(74 222 128 / 0.4);
}

.bg-green-400\/5 {
  background-color: rgb(74 222 128 / 0.05);
}

.bg-green-400\/50 {
  background-color: rgb(74 222 128 / 0.5);
}

.bg-green-400\/60 {
  background-color: rgb(74 222 128 / 0.6);
}

.bg-green-400\/70 {
  background-color: rgb(74 222 128 / 0.7);
}

.bg-green-400\/75 {
  background-color: rgb(74 222 128 / 0.75);
}

.bg-green-400\/80 {
  background-color: rgb(74 222 128 / 0.8);
}

.bg-green-400\/90 {
  background-color: rgb(74 222 128 / 0.9);
}

.bg-green-400\/95 {
  background-color: rgb(74 222 128 / 0.95);
}

.bg-green-50 {
  --tw-bg-opacity: 1;
  background-color: rgb(240 253 244 / var(--tw-bg-opacity));
}

.bg-green-50\/0 {
  background-color: rgb(240 253 244 / 0);
}

.bg-green-50\/10 {
  background-color: rgb(240 253 244 / 0.1);
}

.bg-green-50\/100 {
  background-color: rgb(240 253 244 / 1);
}

.bg-green-50\/20 {
  background-color: rgb(240 253 244 / 0.2);
}

.bg-green-50\/25 {
  background-color: rgb(240 253 244 / 0.25);
}

.bg-green-50\/30 {
  background-color: rgb(240 253 244 / 0.3);
}

.bg-green-50\/40 {
  background-color: rgb(240 253 244 / 0.4);
}

.bg-green-50\/5 {
  background-color: rgb(240 253 244 / 0.05);
}

.bg-green-50\/50 {
  background-color: rgb(240 253 244 / 0.5);
}

.bg-green-50\/60 {
  background-color: rgb(240 253 244 / 0.6);
}

.bg-green-50\/70 {
  background-color: rgb(240 253 244 / 0.7);
}

.bg-green-50\/75 {
  background-color: rgb(240 253 244 / 0.75);
}

.bg-green-50\/80 {
  background-color: rgb(240 253 244 / 0.8);
}

.bg-green-50\/90 {
  background-color: rgb(240 253 244 / 0.9);
}

.bg-green-50\/95 {
  background-color: rgb(240 253 244 / 0.95);
}

.bg-green-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(34 197 94 / var(--tw-bg-opacity));
}

.bg-green-500\/0 {
  background-color: rgb(34 197 94 / 0);
}

.bg-green-500\/10 {
  background-color: rgb(34 197 94 / 0.1);
}

.bg-green-500\/100 {
  background-color: rgb(34 197 94 / 1);
}

.bg-green-500\/20 {
  background-color: rgb(34 197 94 / 0.2);
}

.bg-green-500\/25 {
  background-color: rgb(34 197 94 / 0.25);
}

.bg-green-500\/30 {
  background-color: rgb(34 197 94 / 0.3);
}

.bg-green-500\/40 {
  background-color: rgb(34 197 94 / 0.4);
}

.bg-green-500\/5 {
  background-color: rgb(34 197 94 / 0.05);
}

.bg-green-500\/50 {
  background-color: rgb(34 197 94 / 0.5);
}

.bg-green-500\/60 {
  background-color: rgb(34 197 94 / 0.6);
}

.bg-green-500\/70 {
  background-color: rgb(34 197 94 / 0.7);
}

.bg-green-500\/75 {
  background-color: rgb(34 197 94 / 0.75);
}

.bg-green-500\/80 {
  background-color: rgb(34 197 94 / 0.8);
}

.bg-green-500\/90 {
  background-color: rgb(34 197 94 / 0.9);
}

.bg-green-500\/95 {
  background-color: rgb(34 197 94 / 0.95);
}

.bg-green-600 {
  --tw-bg-opacity: 1;
  background-color: rgb(22 163 74 / var(--tw-bg-opacity));
}

.bg-green-600\/0 {
  background-color: rgb(22 163 74 / 0);
}

.bg-green-600\/10 {
  background-color: rgb(22 163 74 / 0.1);
}

.bg-green-600\/100 {
  background-color: rgb(22 163 74 / 1);
}

.bg-green-600\/20 {
  background-color: rgb(22 163 74 / 0.2);
}

.bg-green-600\/25 {
  background-color: rgb(22 163 74 / 0.25);
}

.bg-green-600\/30 {
  background-color: rgb(22 163 74 / 0.3);
}

.bg-green-600\/40 {
  background-color: rgb(22 163 74 / 0.4);
}

.bg-green-600\/5 {
  background-color: rgb(22 163 74 / 0.05);
}

.bg-green-600\/50 {
  background-color: rgb(22 163 74 / 0.5);
}

.bg-green-600\/60 {
  background-color: rgb(22 163 74 / 0.6);
}

.bg-green-600\/70 {
  background-color: rgb(22 163 74 / 0.7);
}

.bg-green-600\/75 {
  background-color: rgb(22 163 74 / 0.75);
}

.bg-green-600\/80 {
  background-color: rgb(22 163 74 / 0.8);
}

.bg-green-600\/90 {
  background-color: rgb(22 163 74 / 0.9);
}

.bg-green-600\/95 {
  background-color: rgb(22 163 74 / 0.95);
}

.bg-green-700 {
  --tw-bg-opacity: 1;
  background-color: rgb(21 128 61 / var(--tw-bg-opacity));
}

.bg-green-700\/0 {
  background-color: rgb(21 128 61 / 0);
}

.bg-green-700\/10 {
  background-color: rgb(21 128 61 / 0.1);
}

.bg-green-700\/100 {
  background-color: rgb(21 128 61 / 1);
}

.bg-green-700\/20 {
  background-color: rgb(21 128 61 / 0.2);
}

.bg-green-700\/25 {
  background-color: rgb(21 128 61 / 0.25);
}

.bg-green-700\/30 {
  background-color: rgb(21 128 61 / 0.3);
}

.bg-green-700\/40 {
  background-color: rgb(21 128 61 / 0.4);
}

.bg-green-700\/5 {
  background-color: rgb(21 128 61 / 0.05);
}

.bg-green-700\/50 {
  background-color: rgb(21 128 61 / 0.5);
}

.bg-green-700\/60 {
  background-color: rgb(21 128 61 / 0.6);
}

.bg-green-700\/70 {
  background-color: rgb(21 128 61 / 0.7);
}

.bg-green-700\/75 {
  background-color: rgb(21 128 61 / 0.75);
}

.bg-green-700\/80 {
  background-color: rgb(21 128 61 / 0.8);
}

.bg-green-700\/90 {
  background-color: rgb(21 128 61 / 0.9);
}

.bg-green-700\/95 {
  background-color: rgb(21 128 61 / 0.95);
}

.bg-green-800 {
  --tw-bg-opacity: 1;
  background-color: rgb(22 101 52 / var(--tw-bg-opacity));
}

.bg-green-800\/0 {
  background-color: rgb(22 101 52 / 0);
}

.bg-green-800\/10 {
  background-color: rgb(22 101 52 / 0.1);
}

.bg-green-800\/100 {
  background-color: rgb(22 101 52 / 1);
}

.bg-green-800\/20 {
  background-color: rgb(22 101 52 / 0.2);
}

.bg-green-800\/25 {
  background-color: rgb(22 101 52 / 0.25);
}

.bg-green-800\/30 {
  background-color: rgb(22 101 52 / 0.3);
}

.bg-green-800\/40 {
  background-color: rgb(22 101 52 / 0.4);
}

.bg-green-800\/5 {
  background-color: rgb(22 101 52 / 0.05);
}

.bg-green-800\/50 {
  background-color: rgb(22 101 52 / 0.5);
}

.bg-green-800\/60 {
  background-color: rgb(22 101 52 / 0.6);
}

.bg-green-800\/70 {
  background-color: rgb(22 101 52 / 0.7);
}

.bg-green-800\/75 {
  background-color: rgb(22 101 52 / 0.75);
}

.bg-green-800\/80 {
  background-color: rgb(22 101 52 / 0.8);
}

.bg-green-800\/90 {
  background-color: rgb(22 101 52 / 0.9);
}

.bg-green-800\/95 {
  background-color: rgb(22 101 52 / 0.95);
}

.bg-green-900 {
  --tw-bg-opacity: 1;
  background-color: rgb(20 83 45 / var(--tw-bg-opacity));
}

.bg-green-900\/0 {
  background-color: rgb(20 83 45 / 0);
}

.bg-green-900\/10 {
  background-color: rgb(20 83 45 / 0.1);
}

.bg-green-900\/100 {
  background-color: rgb(20 83 45 / 1);
}

.bg-green-900\/20 {
  background-color: rgb(20 83 45 / 0.2);
}

.bg-green-900\/25 {
  background-color: rgb(20 83 45 / 0.25);
}

.bg-green-900\/30 {
  background-color: rgb(20 83 45 / 0.3);
}

.bg-green-900\/40 {
  background-color: rgb(20 83 45 / 0.4);
}

.bg-green-900\/5 {
  background-color: rgb(20 83 45 / 0.05);
}

.bg-green-900\/50 {
  background-color: rgb(20 83 45 / 0.5);
}

.bg-green-900\/60 {
  background-color: rgb(20 83 45 / 0.6);
}

.bg-green-900\/70 {
  background-color: rgb(20 83 45 / 0.7);
}

.bg-green-900\/75 {
  background-color: rgb(20 83 45 / 0.75);
}

.bg-green-900\/80 {
  background-color: rgb(20 83 45 / 0.8);
}

.bg-green-900\/90 {
  background-color: rgb(20 83 45 / 0.9);
}

.bg-green-900\/95 {
  background-color: rgb(20 83 45 / 0.95);
}

.bg-green-950 {
  --tw-bg-opacity: 1;
  background-color: rgb(5 46 22 / var(--tw-bg-opacity));
}

.bg-green-950\/0 {
  background-color: rgb(5 46 22 / 0);
}

.bg-green-950\/10 {
  background-color: rgb(5 46 22 / 0.1);
}

.bg-green-950\/100 {
  background-color: rgb(5 46 22 / 1);
}

.bg-green-950\/20 {
  background-color: rgb(5 46 22 / 0.2);
}

.bg-green-950\/25 {
  background-color: rgb(5 46 22 / 0.25);
}

.bg-green-950\/30 {
  background-color: rgb(5 46 22 / 0.3);
}

.bg-green-950\/40 {
  background-color: rgb(5 46 22 / 0.4);
}

.bg-green-950\/5 {
  background-color: rgb(5 46 22 / 0.05);
}

.bg-green-950\/50 {
  background-color: rgb(5 46 22 / 0.5);
}

.bg-green-950\/60 {
  background-color: rgb(5 46 22 / 0.6);
}

.bg-green-950\/70 {
  background-color: rgb(5 46 22 / 0.7);
}

.bg-green-950\/75 {
  background-color: rgb(5 46 22 / 0.75);
}

.bg-green-950\/80 {
  background-color: rgb(5 46 22 / 0.8);
}

.bg-green-950\/90 {
  background-color: rgb(5 46 22 / 0.9);
}

.bg-green-950\/95 {
  background-color: rgb(5 46 22 / 0.95);
}

.bg-indigo-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(224 231 255 / var(--tw-bg-opacity));
}

.bg-indigo-100\/0 {
  background-color: rgb(224 231 255 / 0);
}

.bg-indigo-100\/10 {
  background-color: rgb(224 231 255 / 0.1);
}

.bg-indigo-100\/100 {
  background-color: rgb(224 231 255 / 1);
}

.bg-indigo-100\/20 {
  background-color: rgb(224 231 255 / 0.2);
}

.bg-indigo-100\/25 {
  background-color: rgb(224 231 255 / 0.25);
}

.bg-indigo-100\/30 {
  background-color: rgb(224 231 255 / 0.3);
}

.bg-indigo-100\/40 {
  background-color: rgb(224 231 255 / 0.4);
}

.bg-indigo-100\/5 {
  background-color: rgb(224 231 255 / 0.05);
}

.bg-indigo-100\/50 {
  background-color: rgb(224 231 255 / 0.5);
}

.bg-indigo-100\/60 {
  background-color: rgb(224 231 255 / 0.6);
}

.bg-indigo-100\/70 {
  background-color: rgb(224 231 255 / 0.7);
}

.bg-indigo-100\/75 {
  background-color: rgb(224 231 255 / 0.75);
}

.bg-indigo-100\/80 {
  background-color: rgb(224 231 255 / 0.8);
}

.bg-indigo-100\/90 {
  background-color: rgb(224 231 255 / 0.9);
}

.bg-indigo-100\/95 {
  background-color: rgb(224 231 255 / 0.95);
}

.bg-indigo-200 {
  --tw-bg-opacity: 1;
  background-color: rgb(199 210 254 / var(--tw-bg-opacity));
}

.bg-indigo-200\/0 {
  background-color: rgb(199 210 254 / 0);
}

.bg-indigo-200\/10 {
  background-color: rgb(199 210 254 / 0.1);
}

.bg-indigo-200\/100 {
  background-color: rgb(199 210 254 / 1);
}

.bg-indigo-200\/20 {
  background-color: rgb(199 210 254 / 0.2);
}

.bg-indigo-200\/25 {
  background-color: rgb(199 210 254 / 0.25);
}

.bg-indigo-200\/30 {
  background-color: rgb(199 210 254 / 0.3);
}

.bg-indigo-200\/40 {
  background-color: rgb(199 210 254 / 0.4);
}

.bg-indigo-200\/5 {
  background-color: rgb(199 210 254 / 0.05);
}

.bg-indigo-200\/50 {
  background-color: rgb(199 210 254 / 0.5);
}

.bg-indigo-200\/60 {
  background-color: rgb(199 210 254 / 0.6);
}

.bg-indigo-200\/70 {
  background-color: rgb(199 210 254 / 0.7);
}

.bg-indigo-200\/75 {
  background-color: rgb(199 210 254 / 0.75);
}

.bg-indigo-200\/80 {
  background-color: rgb(199 210 254 / 0.8);
}

.bg-indigo-200\/90 {
  background-color: rgb(199 210 254 / 0.9);
}

.bg-indigo-200\/95 {
  background-color: rgb(199 210 254 / 0.95);
}

.bg-indigo-300 {
  --tw-bg-opacity: 1;
  background-color: rgb(165 180 252 / var(--tw-bg-opacity));
}

.bg-indigo-300\/0 {
  background-color: rgb(165 180 252 / 0);
}

.bg-indigo-300\/10 {
  background-color: rgb(165 180 252 / 0.1);
}

.bg-indigo-300\/100 {
  background-color: rgb(165 180 252 / 1);
}

.bg-indigo-300\/20 {
  background-color: rgb(165 180 252 / 0.2);
}

.bg-indigo-300\/25 {
  background-color: rgb(165 180 252 / 0.25);
}

.bg-indigo-300\/30 {
  background-color: rgb(165 180 252 / 0.3);
}

.bg-indigo-300\/40 {
  background-color: rgb(165 180 252 / 0.4);
}

.bg-indigo-300\/5 {
  background-color: rgb(165 180 252 / 0.05);
}

.bg-indigo-300\/50 {
  background-color: rgb(165 180 252 / 0.5);
}

.bg-indigo-300\/60 {
  background-color: rgb(165 180 252 / 0.6);
}

.bg-indigo-300\/70 {
  background-color: rgb(165 180 252 / 0.7);
}

.bg-indigo-300\/75 {
  background-color: rgb(165 180 252 / 0.75);
}

.bg-indigo-300\/80 {
  background-color: rgb(165 180 252 / 0.8);
}

.bg-indigo-300\/90 {
  background-color: rgb(165 180 252 / 0.9);
}

.bg-indigo-300\/95 {
  background-color: rgb(165 180 252 / 0.95);
}

.bg-indigo-400 {
  --tw-bg-opacity: 1;
  background-color: rgb(129 140 248 / var(--tw-bg-opacity));
}

.bg-indigo-400\/0 {
  background-color: rgb(129 140 248 / 0);
}

.bg-indigo-400\/10 {
  background-color: rgb(129 140 248 / 0.1);
}

.bg-indigo-400\/100 {
  background-color: rgb(129 140 248 / 1);
}

.bg-indigo-400\/20 {
  background-color: rgb(129 140 248 / 0.2);
}

.bg-indigo-400\/25 {
  background-color: rgb(129 140 248 / 0.25);
}

.bg-indigo-400\/30 {
  background-color: rgb(129 140 248 / 0.3);
}

.bg-indigo-400\/40 {
  background-color: rgb(129 140 248 / 0.4);
}

.bg-indigo-400\/5 {
  background-color: rgb(129 140 248 / 0.05);
}

.bg-indigo-400\/50 {
  background-color: rgb(129 140 248 / 0.5);
}

.bg-indigo-400\/60 {
  background-color: rgb(129 140 248 / 0.6);
}

.bg-indigo-400\/70 {
  background-color: rgb(129 140 248 / 0.7);
}

.bg-indigo-400\/75 {
  background-color: rgb(129 140 248 / 0.75);
}

.bg-indigo-400\/80 {
  background-color: rgb(129 140 248 / 0.8);
}

.bg-indigo-400\/90 {
  background-color: rgb(129 140 248 / 0.9);
}

.bg-indigo-400\/95 {
  background-color: rgb(129 140 248 / 0.95);
}

.bg-indigo-50 {
  --tw-bg-opacity: 1;
  background-color: rgb(238 242 255 / var(--tw-bg-opacity));
}

.bg-indigo-50\/0 {
  background-color: rgb(238 242 255 / 0);
}

.bg-indigo-50\/10 {
  background-color: rgb(238 242 255 / 0.1);
}

.bg-indigo-50\/100 {
  background-color: rgb(238 242 255 / 1);
}

.bg-indigo-50\/20 {
  background-color: rgb(238 242 255 / 0.2);
}

.bg-indigo-50\/25 {
  background-color: rgb(238 242 255 / 0.25);
}

.bg-indigo-50\/30 {
  background-color: rgb(238 242 255 / 0.3);
}

.bg-indigo-50\/40 {
  background-color: rgb(238 242 255 / 0.4);
}

.bg-indigo-50\/5 {
  background-color: rgb(238 242 255 / 0.05);
}

.bg-indigo-50\/50 {
  background-color: rgb(238 242 255 / 0.5);
}

.bg-indigo-50\/60 {
  background-color: rgb(238 242 255 / 0.6);
}

.bg-indigo-50\/70 {
  background-color: rgb(238 242 255 / 0.7);
}

.bg-indigo-50\/75 {
  background-color: rgb(238 242 255 / 0.75);
}

.bg-indigo-50\/80 {
  background-color: rgb(238 242 255 / 0.8);
}

.bg-indigo-50\/90 {
  background-color: rgb(238 242 255 / 0.9);
}

.bg-indigo-50\/95 {
  background-color: rgb(238 242 255 / 0.95);
}

.bg-indigo-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(99 102 241 / var(--tw-bg-opacity));
}

.bg-indigo-500\/0 {
  background-color: rgb(99 102 241 / 0);
}

.bg-indigo-500\/10 {
  background-color: rgb(99 102 241 / 0.1);
}

.bg-indigo-500\/100 {
  background-color: rgb(99 102 241 / 1);
}

.bg-indigo-500\/20 {
  background-color: rgb(99 102 241 / 0.2);
}

.bg-indigo-500\/25 {
  background-color: rgb(99 102 241 / 0.25);
}

.bg-indigo-500\/30 {
  background-color: rgb(99 102 241 / 0.3);
}

.bg-indigo-500\/40 {
  background-color: rgb(99 102 241 / 0.4);
}

.bg-indigo-500\/5 {
  background-color: rgb(99 102 241 / 0.05);
}

.bg-indigo-500\/50 {
  background-color: rgb(99 102 241 / 0.5);
}

.bg-indigo-500\/60 {
  background-color: rgb(99 102 241 / 0.6);
}

.bg-indigo-500\/70 {
  background-color: rgb(99 102 241 / 0.7);
}

.bg-indigo-500\/75 {
  background-color: rgb(99 102 241 / 0.75);
}

.bg-indigo-500\/80 {
  background-color: rgb(99 102 241 / 0.8);
}

.bg-indigo-500\/90 {
  background-color: rgb(99 102 241 / 0.9);
}

.bg-indigo-500\/95 {
  background-color: rgb(99 102 241 / 0.95);
}

.bg-indigo-600 {
  --tw-bg-opacity: 1;
  background-color: rgb(79 70 229 / var(--tw-bg-opacity));
}

.bg-indigo-600\/0 {
  background-color: rgb(79 70 229 / 0);
}

.bg-indigo-600\/10 {
  background-color: rgb(79 70 229 / 0.1);
}

.bg-indigo-600\/100 {
  background-color: rgb(79 70 229 / 1);
}

.bg-indigo-600\/20 {
  background-color: rgb(79 70 229 / 0.2);
}

.bg-indigo-600\/25 {
  background-color: rgb(79 70 229 / 0.25);
}

.bg-indigo-600\/30 {
  background-color: rgb(79 70 229 / 0.3);
}

.bg-indigo-600\/40 {
  background-color: rgb(79 70 229 / 0.4);
}

.bg-indigo-600\/5 {
  background-color: rgb(79 70 229 / 0.05);
}

.bg-indigo-600\/50 {
  background-color: rgb(79 70 229 / 0.5);
}

.bg-indigo-600\/60 {
  background-color: rgb(79 70 229 / 0.6);
}

.bg-indigo-600\/70 {
  background-color: rgb(79 70 229 / 0.7);
}

.bg-indigo-600\/75 {
  background-color: rgb(79 70 229 / 0.75);
}

.bg-indigo-600\/80 {
  background-color: rgb(79 70 229 / 0.8);
}

.bg-indigo-600\/90 {
  background-color: rgb(79 70 229 / 0.9);
}

.bg-indigo-600\/95 {
  background-color: rgb(79 70 229 / 0.95);
}

.bg-indigo-700 {
  --tw-bg-opacity: 1;
  background-color: rgb(67 56 202 / var(--tw-bg-opacity));
}

.bg-indigo-700\/0 {
  background-color: rgb(67 56 202 / 0);
}

.bg-indigo-700\/10 {
  background-color: rgb(67 56 202 / 0.1);
}

.bg-indigo-700\/100 {
  background-color: rgb(67 56 202 / 1);
}

.bg-indigo-700\/20 {
  background-color: rgb(67 56 202 / 0.2);
}

.bg-indigo-700\/25 {
  background-color: rgb(67 56 202 / 0.25);
}

.bg-indigo-700\/30 {
  background-color: rgb(67 56 202 / 0.3);
}

.bg-indigo-700\/40 {
  background-color: rgb(67 56 202 / 0.4);
}

.bg-indigo-700\/5 {
  background-color: rgb(67 56 202 / 0.05);
}

.bg-indigo-700\/50 {
  background-color: rgb(67 56 202 / 0.5);
}

.bg-indigo-700\/60 {
  background-color: rgb(67 56 202 / 0.6);
}

.bg-indigo-700\/70 {
  background-color: rgb(67 56 202 / 0.7);
}

.bg-indigo-700\/75 {
  background-color: rgb(67 56 202 / 0.75);
}

.bg-indigo-700\/80 {
  background-color: rgb(67 56 202 / 0.8);
}

.bg-indigo-700\/90 {
  background-color: rgb(67 56 202 / 0.9);
}

.bg-indigo-700\/95 {
  background-color: rgb(67 56 202 / 0.95);
}

.bg-indigo-800 {
  --tw-bg-opacity: 1;
  background-color: rgb(55 48 163 / var(--tw-bg-opacity));
}

.bg-indigo-800\/0 {
  background-color: rgb(55 48 163 / 0);
}

.bg-indigo-800\/10 {
  background-color: rgb(55 48 163 / 0.1);
}

.bg-indigo-800\/100 {
  background-color: rgb(55 48 163 / 1);
}

.bg-indigo-800\/20 {
  background-color: rgb(55 48 163 / 0.2);
}

.bg-indigo-800\/25 {
  background-color: rgb(55 48 163 / 0.25);
}

.bg-indigo-800\/30 {
  background-color: rgb(55 48 163 / 0.3);
}

.bg-indigo-800\/40 {
  background-color: rgb(55 48 163 / 0.4);
}

.bg-indigo-800\/5 {
  background-color: rgb(55 48 163 / 0.05);
}

.bg-indigo-800\/50 {
  background-color: rgb(55 48 163 / 0.5);
}

.bg-indigo-800\/60 {
  background-color: rgb(55 48 163 / 0.6);
}

.bg-indigo-800\/70 {
  background-color: rgb(55 48 163 / 0.7);
}

.bg-indigo-800\/75 {
  background-color: rgb(55 48 163 / 0.75);
}

.bg-indigo-800\/80 {
  background-color: rgb(55 48 163 / 0.8);
}

.bg-indigo-800\/90 {
  background-color: rgb(55 48 163 / 0.9);
}

.bg-indigo-800\/95 {
  background-color: rgb(55 48 163 / 0.95);
}

.bg-indigo-900 {
  --tw-bg-opacity: 1;
  background-color: rgb(49 46 129 / var(--tw-bg-opacity));
}

.bg-indigo-900\/0 {
  background-color: rgb(49 46 129 / 0);
}

.bg-indigo-900\/10 {
  background-color: rgb(49 46 129 / 0.1);
}

.bg-indigo-900\/100 {
  background-color: rgb(49 46 129 / 1);
}

.bg-indigo-900\/20 {
  background-color: rgb(49 46 129 / 0.2);
}

.bg-indigo-900\/25 {
  background-color: rgb(49 46 129 / 0.25);
}

.bg-indigo-900\/30 {
  background-color: rgb(49 46 129 / 0.3);
}

.bg-indigo-900\/40 {
  background-color: rgb(49 46 129 / 0.4);
}

.bg-indigo-900\/5 {
  background-color: rgb(49 46 129 / 0.05);
}

.bg-indigo-900\/50 {
  background-color: rgb(49 46 129 / 0.5);
}

.bg-indigo-900\/60 {
  background-color: rgb(49 46 129 / 0.6);
}

.bg-indigo-900\/70 {
  background-color: rgb(49 46 129 / 0.7);
}

.bg-indigo-900\/75 {
  background-color: rgb(49 46 129 / 0.75);
}

.bg-indigo-900\/80 {
  background-color: rgb(49 46 129 / 0.8);
}

.bg-indigo-900\/90 {
  background-color: rgb(49 46 129 / 0.9);
}

.bg-indigo-900\/95 {
  background-color: rgb(49 46 129 / 0.95);
}

.bg-indigo-950 {
  --tw-bg-opacity: 1;
  background-color: rgb(30 27 75 / var(--tw-bg-opacity));
}

.bg-indigo-950\/0 {
  background-color: rgb(30 27 75 / 0);
}

.bg-indigo-950\/10 {
  background-color: rgb(30 27 75 / 0.1);
}

.bg-indigo-950\/100 {
  background-color: rgb(30 27 75 / 1);
}

.bg-indigo-950\/20 {
  background-color: rgb(30 27 75 / 0.2);
}

.bg-indigo-950\/25 {
  background-color: rgb(30 27 75 / 0.25);
}

.bg-indigo-950\/30 {
  background-color: rgb(30 27 75 / 0.3);
}

.bg-indigo-950\/40 {
  background-color: rgb(30 27 75 / 0.4);
}

.bg-indigo-950\/5 {
  background-color: rgb(30 27 75 / 0.05);
}

.bg-indigo-950\/50 {
  background-color: rgb(30 27 75 / 0.5);
}

.bg-indigo-950\/60 {
  background-color: rgb(30 27 75 / 0.6);
}

.bg-indigo-950\/70 {
  background-color: rgb(30 27 75 / 0.7);
}

.bg-indigo-950\/75 {
  background-color: rgb(30 27 75 / 0.75);
}

.bg-indigo-950\/80 {
  background-color: rgb(30 27 75 / 0.8);
}

.bg-indigo-950\/90 {
  background-color: rgb(30 27 75 / 0.9);
}

.bg-indigo-950\/95 {
  background-color: rgb(30 27 75 / 0.95);
}

.bg-inf {
  --tw-bg-opacity: 1;
  background-color: rgb(255 120 7 / var(--tw-bg-opacity));
}

.bg-inf\/0 {
  background-color: rgb(255 120 7 / 0);
}

.bg-inf\/10 {
  background-color: rgb(255 120 7 / 0.1);
}

.bg-inf\/100 {
  background-color: rgb(255 120 7 / 1);
}

.bg-inf\/20 {
  background-color: rgb(255 120 7 / 0.2);
}

.bg-inf\/25 {
  background-color: rgb(255 120 7 / 0.25);
}

.bg-inf\/30 {
  background-color: rgb(255 120 7 / 0.3);
}

.bg-inf\/40 {
  background-color: rgb(255 120 7 / 0.4);
}

.bg-inf\/5 {
  background-color: rgb(255 120 7 / 0.05);
}

.bg-inf\/50 {
  background-color: rgb(255 120 7 / 0.5);
}

.bg-inf\/60 {
  background-color: rgb(255 120 7 / 0.6);
}

.bg-inf\/70 {
  background-color: rgb(255 120 7 / 0.7);
}

.bg-inf\/75 {
  background-color: rgb(255 120 7 / 0.75);
}

.bg-inf\/80 {
  background-color: rgb(255 120 7 / 0.8);
}

.bg-inf\/90 {
  background-color: rgb(255 120 7 / 0.9);
}

.bg-inf\/95 {
  background-color: rgb(255 120 7 / 0.95);
}

.bg-inherit {
  background-color: inherit;
}

.bg-inverse-b1 {
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity));
}

.bg-inverse-b1\/0 {
  background-color: rgb(255 255 255 / 0);
}

.bg-inverse-b1\/10 {
  background-color: rgb(255 255 255 / 0.1);
}

.bg-inverse-b1\/100 {
  background-color: rgb(255 255 255 / 1);
}

.bg-inverse-b1\/20 {
  background-color: rgb(255 255 255 / 0.2);
}

.bg-inverse-b1\/25 {
  background-color: rgb(255 255 255 / 0.25);
}

.bg-inverse-b1\/30 {
  background-color: rgb(255 255 255 / 0.3);
}

.bg-inverse-b1\/40 {
  background-color: rgb(255 255 255 / 0.4);
}

.bg-inverse-b1\/5 {
  background-color: rgb(255 255 255 / 0.05);
}

.bg-inverse-b1\/50 {
  background-color: rgb(255 255 255 / 0.5);
}

.bg-inverse-b1\/60 {
  background-color: rgb(255 255 255 / 0.6);
}

.bg-inverse-b1\/70 {
  background-color: rgb(255 255 255 / 0.7);
}

.bg-inverse-b1\/75 {
  background-color: rgb(255 255 255 / 0.75);
}

.bg-inverse-b1\/80 {
  background-color: rgb(255 255 255 / 0.8);
}

.bg-inverse-b1\/90 {
  background-color: rgb(255 255 255 / 0.9);
}

.bg-inverse-b1\/95 {
  background-color: rgb(255 255 255 / 0.95);
}

.bg-inverse-b2 {
  --tw-bg-opacity: 1;
  background-color: rgb(204 204 204 / var(--tw-bg-opacity));
}

.bg-inverse-b2\/0 {
  background-color: rgb(204 204 204 / 0);
}

.bg-inverse-b2\/10 {
  background-color: rgb(204 204 204 / 0.1);
}

.bg-inverse-b2\/100 {
  background-color: rgb(204 204 204 / 1);
}

.bg-inverse-b2\/20 {
  background-color: rgb(204 204 204 / 0.2);
}

.bg-inverse-b2\/25 {
  background-color: rgb(204 204 204 / 0.25);
}

.bg-inverse-b2\/30 {
  background-color: rgb(204 204 204 / 0.3);
}

.bg-inverse-b2\/40 {
  background-color: rgb(204 204 204 / 0.4);
}

.bg-inverse-b2\/5 {
  background-color: rgb(204 204 204 / 0.05);
}

.bg-inverse-b2\/50 {
  background-color: rgb(204 204 204 / 0.5);
}

.bg-inverse-b2\/60 {
  background-color: rgb(204 204 204 / 0.6);
}

.bg-inverse-b2\/70 {
  background-color: rgb(204 204 204 / 0.7);
}

.bg-inverse-b2\/75 {
  background-color: rgb(204 204 204 / 0.75);
}

.bg-inverse-b2\/80 {
  background-color: rgb(204 204 204 / 0.8);
}

.bg-inverse-b2\/90 {
  background-color: rgb(204 204 204 / 0.9);
}

.bg-inverse-b2\/95 {
  background-color: rgb(204 204 204 / 0.95);
}

.bg-inverse-g1 {
  --tw-bg-opacity: 1;
  background-color: rgb(85 85 85 / var(--tw-bg-opacity));
}

.bg-inverse-g1\/0 {
  background-color: rgb(85 85 85 / 0);
}

.bg-inverse-g1\/10 {
  background-color: rgb(85 85 85 / 0.1);
}

.bg-inverse-g1\/100 {
  background-color: rgb(85 85 85 / 1);
}

.bg-inverse-g1\/20 {
  background-color: rgb(85 85 85 / 0.2);
}

.bg-inverse-g1\/25 {
  background-color: rgb(85 85 85 / 0.25);
}

.bg-inverse-g1\/30 {
  background-color: rgb(85 85 85 / 0.3);
}

.bg-inverse-g1\/40 {
  background-color: rgb(85 85 85 / 0.4);
}

.bg-inverse-g1\/5 {
  background-color: rgb(85 85 85 / 0.05);
}

.bg-inverse-g1\/50 {
  background-color: rgb(85 85 85 / 0.5);
}

.bg-inverse-g1\/60 {
  background-color: rgb(85 85 85 / 0.6);
}

.bg-inverse-g1\/70 {
  background-color: rgb(85 85 85 / 0.7);
}

.bg-inverse-g1\/75 {
  background-color: rgb(85 85 85 / 0.75);
}

.bg-inverse-g1\/80 {
  background-color: rgb(85 85 85 / 0.8);
}

.bg-inverse-g1\/90 {
  background-color: rgb(85 85 85 / 0.9);
}

.bg-inverse-g1\/95 {
  background-color: rgb(85 85 85 / 0.95);
}

.bg-inverse-g2 {
  --tw-bg-opacity: 1;
  background-color: rgb(153 153 153 / var(--tw-bg-opacity));
}

.bg-inverse-g2\/0 {
  background-color: rgb(153 153 153 / 0);
}

.bg-inverse-g2\/10 {
  background-color: rgb(153 153 153 / 0.1);
}

.bg-inverse-g2\/100 {
  background-color: rgb(153 153 153 / 1);
}

.bg-inverse-g2\/20 {
  background-color: rgb(153 153 153 / 0.2);
}

.bg-inverse-g2\/25 {
  background-color: rgb(153 153 153 / 0.25);
}

.bg-inverse-g2\/30 {
  background-color: rgb(153 153 153 / 0.3);
}

.bg-inverse-g2\/40 {
  background-color: rgb(153 153 153 / 0.4);
}

.bg-inverse-g2\/5 {
  background-color: rgb(153 153 153 / 0.05);
}

.bg-inverse-g2\/50 {
  background-color: rgb(153 153 153 / 0.5);
}

.bg-inverse-g2\/60 {
  background-color: rgb(153 153 153 / 0.6);
}

.bg-inverse-g2\/70 {
  background-color: rgb(153 153 153 / 0.7);
}

.bg-inverse-g2\/75 {
  background-color: rgb(153 153 153 / 0.75);
}

.bg-inverse-g2\/80 {
  background-color: rgb(153 153 153 / 0.8);
}

.bg-inverse-g2\/90 {
  background-color: rgb(153 153 153 / 0.9);
}

.bg-inverse-g2\/95 {
  background-color: rgb(153 153 153 / 0.95);
}

.bg-lime-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(236 252 203 / var(--tw-bg-opacity));
}

.bg-lime-100\/0 {
  background-color: rgb(236 252 203 / 0);
}

.bg-lime-100\/10 {
  background-color: rgb(236 252 203 / 0.1);
}

.bg-lime-100\/100 {
  background-color: rgb(236 252 203 / 1);
}

.bg-lime-100\/20 {
  background-color: rgb(236 252 203 / 0.2);
}

.bg-lime-100\/25 {
  background-color: rgb(236 252 203 / 0.25);
}

.bg-lime-100\/30 {
  background-color: rgb(236 252 203 / 0.3);
}

.bg-lime-100\/40 {
  background-color: rgb(236 252 203 / 0.4);
}

.bg-lime-100\/5 {
  background-color: rgb(236 252 203 / 0.05);
}

.bg-lime-100\/50 {
  background-color: rgb(236 252 203 / 0.5);
}

.bg-lime-100\/60 {
  background-color: rgb(236 252 203 / 0.6);
}

.bg-lime-100\/70 {
  background-color: rgb(236 252 203 / 0.7);
}

.bg-lime-100\/75 {
  background-color: rgb(236 252 203 / 0.75);
}

.bg-lime-100\/80 {
  background-color: rgb(236 252 203 / 0.8);
}

.bg-lime-100\/90 {
  background-color: rgb(236 252 203 / 0.9);
}

.bg-lime-100\/95 {
  background-color: rgb(236 252 203 / 0.95);
}

.bg-lime-200 {
  --tw-bg-opacity: 1;
  background-color: rgb(217 249 157 / var(--tw-bg-opacity));
}

.bg-lime-200\/0 {
  background-color: rgb(217 249 157 / 0);
}

.bg-lime-200\/10 {
  background-color: rgb(217 249 157 / 0.1);
}

.bg-lime-200\/100 {
  background-color: rgb(217 249 157 / 1);
}

.bg-lime-200\/20 {
  background-color: rgb(217 249 157 / 0.2);
}

.bg-lime-200\/25 {
  background-color: rgb(217 249 157 / 0.25);
}

.bg-lime-200\/30 {
  background-color: rgb(217 249 157 / 0.3);
}

.bg-lime-200\/40 {
  background-color: rgb(217 249 157 / 0.4);
}

.bg-lime-200\/5 {
  background-color: rgb(217 249 157 / 0.05);
}

.bg-lime-200\/50 {
  background-color: rgb(217 249 157 / 0.5);
}

.bg-lime-200\/60 {
  background-color: rgb(217 249 157 / 0.6);
}

.bg-lime-200\/70 {
  background-color: rgb(217 249 157 / 0.7);
}

.bg-lime-200\/75 {
  background-color: rgb(217 249 157 / 0.75);
}

.bg-lime-200\/80 {
  background-color: rgb(217 249 157 / 0.8);
}

.bg-lime-200\/90 {
  background-color: rgb(217 249 157 / 0.9);
}

.bg-lime-200\/95 {
  background-color: rgb(217 249 157 / 0.95);
}

.bg-lime-300 {
  --tw-bg-opacity: 1;
  background-color: rgb(190 242 100 / var(--tw-bg-opacity));
}

.bg-lime-300\/0 {
  background-color: rgb(190 242 100 / 0);
}

.bg-lime-300\/10 {
  background-color: rgb(190 242 100 / 0.1);
}

.bg-lime-300\/100 {
  background-color: rgb(190 242 100 / 1);
}

.bg-lime-300\/20 {
  background-color: rgb(190 242 100 / 0.2);
}

.bg-lime-300\/25 {
  background-color: rgb(190 242 100 / 0.25);
}

.bg-lime-300\/30 {
  background-color: rgb(190 242 100 / 0.3);
}

.bg-lime-300\/40 {
  background-color: rgb(190 242 100 / 0.4);
}

.bg-lime-300\/5 {
  background-color: rgb(190 242 100 / 0.05);
}

.bg-lime-300\/50 {
  background-color: rgb(190 242 100 / 0.5);
}

.bg-lime-300\/60 {
  background-color: rgb(190 242 100 / 0.6);
}

.bg-lime-300\/70 {
  background-color: rgb(190 242 100 / 0.7);
}

.bg-lime-300\/75 {
  background-color: rgb(190 242 100 / 0.75);
}

.bg-lime-300\/80 {
  background-color: rgb(190 242 100 / 0.8);
}

.bg-lime-300\/90 {
  background-color: rgb(190 242 100 / 0.9);
}

.bg-lime-300\/95 {
  background-color: rgb(190 242 100 / 0.95);
}

.bg-lime-400 {
  --tw-bg-opacity: 1;
  background-color: rgb(163 230 53 / var(--tw-bg-opacity));
}

.bg-lime-400\/0 {
  background-color: rgb(163 230 53 / 0);
}

.bg-lime-400\/10 {
  background-color: rgb(163 230 53 / 0.1);
}

.bg-lime-400\/100 {
  background-color: rgb(163 230 53 / 1);
}

.bg-lime-400\/20 {
  background-color: rgb(163 230 53 / 0.2);
}

.bg-lime-400\/25 {
  background-color: rgb(163 230 53 / 0.25);
}

.bg-lime-400\/30 {
  background-color: rgb(163 230 53 / 0.3);
}

.bg-lime-400\/40 {
  background-color: rgb(163 230 53 / 0.4);
}

.bg-lime-400\/5 {
  background-color: rgb(163 230 53 / 0.05);
}

.bg-lime-400\/50 {
  background-color: rgb(163 230 53 / 0.5);
}

.bg-lime-400\/60 {
  background-color: rgb(163 230 53 / 0.6);
}

.bg-lime-400\/70 {
  background-color: rgb(163 230 53 / 0.7);
}

.bg-lime-400\/75 {
  background-color: rgb(163 230 53 / 0.75);
}

.bg-lime-400\/80 {
  background-color: rgb(163 230 53 / 0.8);
}

.bg-lime-400\/90 {
  background-color: rgb(163 230 53 / 0.9);
}

.bg-lime-400\/95 {
  background-color: rgb(163 230 53 / 0.95);
}

.bg-lime-50 {
  --tw-bg-opacity: 1;
  background-color: rgb(247 254 231 / var(--tw-bg-opacity));
}

.bg-lime-50\/0 {
  background-color: rgb(247 254 231 / 0);
}

.bg-lime-50\/10 {
  background-color: rgb(247 254 231 / 0.1);
}

.bg-lime-50\/100 {
  background-color: rgb(247 254 231 / 1);
}

.bg-lime-50\/20 {
  background-color: rgb(247 254 231 / 0.2);
}

.bg-lime-50\/25 {
  background-color: rgb(247 254 231 / 0.25);
}

.bg-lime-50\/30 {
  background-color: rgb(247 254 231 / 0.3);
}

.bg-lime-50\/40 {
  background-color: rgb(247 254 231 / 0.4);
}

.bg-lime-50\/5 {
  background-color: rgb(247 254 231 / 0.05);
}

.bg-lime-50\/50 {
  background-color: rgb(247 254 231 / 0.5);
}

.bg-lime-50\/60 {
  background-color: rgb(247 254 231 / 0.6);
}

.bg-lime-50\/70 {
  background-color: rgb(247 254 231 / 0.7);
}

.bg-lime-50\/75 {
  background-color: rgb(247 254 231 / 0.75);
}

.bg-lime-50\/80 {
  background-color: rgb(247 254 231 / 0.8);
}

.bg-lime-50\/90 {
  background-color: rgb(247 254 231 / 0.9);
}

.bg-lime-50\/95 {
  background-color: rgb(247 254 231 / 0.95);
}

.bg-lime-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(132 204 22 / var(--tw-bg-opacity));
}

.bg-lime-500\/0 {
  background-color: rgb(132 204 22 / 0);
}

.bg-lime-500\/10 {
  background-color: rgb(132 204 22 / 0.1);
}

.bg-lime-500\/100 {
  background-color: rgb(132 204 22 / 1);
}

.bg-lime-500\/20 {
  background-color: rgb(132 204 22 / 0.2);
}

.bg-lime-500\/25 {
  background-color: rgb(132 204 22 / 0.25);
}

.bg-lime-500\/30 {
  background-color: rgb(132 204 22 / 0.3);
}

.bg-lime-500\/40 {
  background-color: rgb(132 204 22 / 0.4);
}

.bg-lime-500\/5 {
  background-color: rgb(132 204 22 / 0.05);
}

.bg-lime-500\/50 {
  background-color: rgb(132 204 22 / 0.5);
}

.bg-lime-500\/60 {
  background-color: rgb(132 204 22 / 0.6);
}

.bg-lime-500\/70 {
  background-color: rgb(132 204 22 / 0.7);
}

.bg-lime-500\/75 {
  background-color: rgb(132 204 22 / 0.75);
}

.bg-lime-500\/80 {
  background-color: rgb(132 204 22 / 0.8);
}

.bg-lime-500\/90 {
  background-color: rgb(132 204 22 / 0.9);
}

.bg-lime-500\/95 {
  background-color: rgb(132 204 22 / 0.95);
}

.bg-lime-600 {
  --tw-bg-opacity: 1;
  background-color: rgb(101 163 13 / var(--tw-bg-opacity));
}

.bg-lime-600\/0 {
  background-color: rgb(101 163 13 / 0);
}

.bg-lime-600\/10 {
  background-color: rgb(101 163 13 / 0.1);
}

.bg-lime-600\/100 {
  background-color: rgb(101 163 13 / 1);
}

.bg-lime-600\/20 {
  background-color: rgb(101 163 13 / 0.2);
}

.bg-lime-600\/25 {
  background-color: rgb(101 163 13 / 0.25);
}

.bg-lime-600\/30 {
  background-color: rgb(101 163 13 / 0.3);
}

.bg-lime-600\/40 {
  background-color: rgb(101 163 13 / 0.4);
}

.bg-lime-600\/5 {
  background-color: rgb(101 163 13 / 0.05);
}

.bg-lime-600\/50 {
  background-color: rgb(101 163 13 / 0.5);
}

.bg-lime-600\/60 {
  background-color: rgb(101 163 13 / 0.6);
}

.bg-lime-600\/70 {
  background-color: rgb(101 163 13 / 0.7);
}

.bg-lime-600\/75 {
  background-color: rgb(101 163 13 / 0.75);
}

.bg-lime-600\/80 {
  background-color: rgb(101 163 13 / 0.8);
}

.bg-lime-600\/90 {
  background-color: rgb(101 163 13 / 0.9);
}

.bg-lime-600\/95 {
  background-color: rgb(101 163 13 / 0.95);
}

.bg-lime-700 {
  --tw-bg-opacity: 1;
  background-color: rgb(77 124 15 / var(--tw-bg-opacity));
}

.bg-lime-700\/0 {
  background-color: rgb(77 124 15 / 0);
}

.bg-lime-700\/10 {
  background-color: rgb(77 124 15 / 0.1);
}

.bg-lime-700\/100 {
  background-color: rgb(77 124 15 / 1);
}

.bg-lime-700\/20 {
  background-color: rgb(77 124 15 / 0.2);
}

.bg-lime-700\/25 {
  background-color: rgb(77 124 15 / 0.25);
}

.bg-lime-700\/30 {
  background-color: rgb(77 124 15 / 0.3);
}

.bg-lime-700\/40 {
  background-color: rgb(77 124 15 / 0.4);
}

.bg-lime-700\/5 {
  background-color: rgb(77 124 15 / 0.05);
}

.bg-lime-700\/50 {
  background-color: rgb(77 124 15 / 0.5);
}

.bg-lime-700\/60 {
  background-color: rgb(77 124 15 / 0.6);
}

.bg-lime-700\/70 {
  background-color: rgb(77 124 15 / 0.7);
}

.bg-lime-700\/75 {
  background-color: rgb(77 124 15 / 0.75);
}

.bg-lime-700\/80 {
  background-color: rgb(77 124 15 / 0.8);
}

.bg-lime-700\/90 {
  background-color: rgb(77 124 15 / 0.9);
}

.bg-lime-700\/95 {
  background-color: rgb(77 124 15 / 0.95);
}

.bg-lime-800 {
  --tw-bg-opacity: 1;
  background-color: rgb(63 98 18 / var(--tw-bg-opacity));
}

.bg-lime-800\/0 {
  background-color: rgb(63 98 18 / 0);
}

.bg-lime-800\/10 {
  background-color: rgb(63 98 18 / 0.1);
}

.bg-lime-800\/100 {
  background-color: rgb(63 98 18 / 1);
}

.bg-lime-800\/20 {
  background-color: rgb(63 98 18 / 0.2);
}

.bg-lime-800\/25 {
  background-color: rgb(63 98 18 / 0.25);
}

.bg-lime-800\/30 {
  background-color: rgb(63 98 18 / 0.3);
}

.bg-lime-800\/40 {
  background-color: rgb(63 98 18 / 0.4);
}

.bg-lime-800\/5 {
  background-color: rgb(63 98 18 / 0.05);
}

.bg-lime-800\/50 {
  background-color: rgb(63 98 18 / 0.5);
}

.bg-lime-800\/60 {
  background-color: rgb(63 98 18 / 0.6);
}

.bg-lime-800\/70 {
  background-color: rgb(63 98 18 / 0.7);
}

.bg-lime-800\/75 {
  background-color: rgb(63 98 18 / 0.75);
}

.bg-lime-800\/80 {
  background-color: rgb(63 98 18 / 0.8);
}

.bg-lime-800\/90 {
  background-color: rgb(63 98 18 / 0.9);
}

.bg-lime-800\/95 {
  background-color: rgb(63 98 18 / 0.95);
}

.bg-lime-900 {
  --tw-bg-opacity: 1;
  background-color: rgb(54 83 20 / var(--tw-bg-opacity));
}

.bg-lime-900\/0 {
  background-color: rgb(54 83 20 / 0);
}

.bg-lime-900\/10 {
  background-color: rgb(54 83 20 / 0.1);
}

.bg-lime-900\/100 {
  background-color: rgb(54 83 20 / 1);
}

.bg-lime-900\/20 {
  background-color: rgb(54 83 20 / 0.2);
}

.bg-lime-900\/25 {
  background-color: rgb(54 83 20 / 0.25);
}

.bg-lime-900\/30 {
  background-color: rgb(54 83 20 / 0.3);
}

.bg-lime-900\/40 {
  background-color: rgb(54 83 20 / 0.4);
}

.bg-lime-900\/5 {
  background-color: rgb(54 83 20 / 0.05);
}

.bg-lime-900\/50 {
  background-color: rgb(54 83 20 / 0.5);
}

.bg-lime-900\/60 {
  background-color: rgb(54 83 20 / 0.6);
}

.bg-lime-900\/70 {
  background-color: rgb(54 83 20 / 0.7);
}

.bg-lime-900\/75 {
  background-color: rgb(54 83 20 / 0.75);
}

.bg-lime-900\/80 {
  background-color: rgb(54 83 20 / 0.8);
}

.bg-lime-900\/90 {
  background-color: rgb(54 83 20 / 0.9);
}

.bg-lime-900\/95 {
  background-color: rgb(54 83 20 / 0.95);
}

.bg-lime-950 {
  --tw-bg-opacity: 1;
  background-color: rgb(26 46 5 / var(--tw-bg-opacity));
}

.bg-lime-950\/0 {
  background-color: rgb(26 46 5 / 0);
}

.bg-lime-950\/10 {
  background-color: rgb(26 46 5 / 0.1);
}

.bg-lime-950\/100 {
  background-color: rgb(26 46 5 / 1);
}

.bg-lime-950\/20 {
  background-color: rgb(26 46 5 / 0.2);
}

.bg-lime-950\/25 {
  background-color: rgb(26 46 5 / 0.25);
}

.bg-lime-950\/30 {
  background-color: rgb(26 46 5 / 0.3);
}

.bg-lime-950\/40 {
  background-color: rgb(26 46 5 / 0.4);
}

.bg-lime-950\/5 {
  background-color: rgb(26 46 5 / 0.05);
}

.bg-lime-950\/50 {
  background-color: rgb(26 46 5 / 0.5);
}

.bg-lime-950\/60 {
  background-color: rgb(26 46 5 / 0.6);
}

.bg-lime-950\/70 {
  background-color: rgb(26 46 5 / 0.7);
}

.bg-lime-950\/75 {
  background-color: rgb(26 46 5 / 0.75);
}

.bg-lime-950\/80 {
  background-color: rgb(26 46 5 / 0.8);
}

.bg-lime-950\/90 {
  background-color: rgb(26 46 5 / 0.9);
}

.bg-lime-950\/95 {
  background-color: rgb(26 46 5 / 0.95);
}

.bg-neutral-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(245 245 245 / var(--tw-bg-opacity));
}

.bg-neutral-100\/0 {
  background-color: rgb(245 245 245 / 0);
}

.bg-neutral-100\/10 {
  background-color: rgb(245 245 245 / 0.1);
}

.bg-neutral-100\/100 {
  background-color: rgb(245 245 245 / 1);
}

.bg-neutral-100\/20 {
  background-color: rgb(245 245 245 / 0.2);
}

.bg-neutral-100\/25 {
  background-color: rgb(245 245 245 / 0.25);
}

.bg-neutral-100\/30 {
  background-color: rgb(245 245 245 / 0.3);
}

.bg-neutral-100\/40 {
  background-color: rgb(245 245 245 / 0.4);
}

.bg-neutral-100\/5 {
  background-color: rgb(245 245 245 / 0.05);
}

.bg-neutral-100\/50 {
  background-color: rgb(245 245 245 / 0.5);
}

.bg-neutral-100\/60 {
  background-color: rgb(245 245 245 / 0.6);
}

.bg-neutral-100\/70 {
  background-color: rgb(245 245 245 / 0.7);
}

.bg-neutral-100\/75 {
  background-color: rgb(245 245 245 / 0.75);
}

.bg-neutral-100\/80 {
  background-color: rgb(245 245 245 / 0.8);
}

.bg-neutral-100\/90 {
  background-color: rgb(245 245 245 / 0.9);
}

.bg-neutral-100\/95 {
  background-color: rgb(245 245 245 / 0.95);
}

.bg-neutral-200 {
  --tw-bg-opacity: 1;
  background-color: rgb(229 229 229 / var(--tw-bg-opacity));
}

.bg-neutral-200\/0 {
  background-color: rgb(229 229 229 / 0);
}

.bg-neutral-200\/10 {
  background-color: rgb(229 229 229 / 0.1);
}

.bg-neutral-200\/100 {
  background-color: rgb(229 229 229 / 1);
}

.bg-neutral-200\/20 {
  background-color: rgb(229 229 229 / 0.2);
}

.bg-neutral-200\/25 {
  background-color: rgb(229 229 229 / 0.25);
}

.bg-neutral-200\/30 {
  background-color: rgb(229 229 229 / 0.3);
}

.bg-neutral-200\/40 {
  background-color: rgb(229 229 229 / 0.4);
}

.bg-neutral-200\/5 {
  background-color: rgb(229 229 229 / 0.05);
}

.bg-neutral-200\/50 {
  background-color: rgb(229 229 229 / 0.5);
}

.bg-neutral-200\/60 {
  background-color: rgb(229 229 229 / 0.6);
}

.bg-neutral-200\/70 {
  background-color: rgb(229 229 229 / 0.7);
}

.bg-neutral-200\/75 {
  background-color: rgb(229 229 229 / 0.75);
}

.bg-neutral-200\/80 {
  background-color: rgb(229 229 229 / 0.8);
}

.bg-neutral-200\/90 {
  background-color: rgb(229 229 229 / 0.9);
}

.bg-neutral-200\/95 {
  background-color: rgb(229 229 229 / 0.95);
}

.bg-neutral-300 {
  --tw-bg-opacity: 1;
  background-color: rgb(212 212 212 / var(--tw-bg-opacity));
}

.bg-neutral-300\/0 {
  background-color: rgb(212 212 212 / 0);
}

.bg-neutral-300\/10 {
  background-color: rgb(212 212 212 / 0.1);
}

.bg-neutral-300\/100 {
  background-color: rgb(212 212 212 / 1);
}

.bg-neutral-300\/20 {
  background-color: rgb(212 212 212 / 0.2);
}

.bg-neutral-300\/25 {
  background-color: rgb(212 212 212 / 0.25);
}

.bg-neutral-300\/30 {
  background-color: rgb(212 212 212 / 0.3);
}

.bg-neutral-300\/40 {
  background-color: rgb(212 212 212 / 0.4);
}

.bg-neutral-300\/5 {
  background-color: rgb(212 212 212 / 0.05);
}

.bg-neutral-300\/50 {
  background-color: rgb(212 212 212 / 0.5);
}

.bg-neutral-300\/60 {
  background-color: rgb(212 212 212 / 0.6);
}

.bg-neutral-300\/70 {
  background-color: rgb(212 212 212 / 0.7);
}

.bg-neutral-300\/75 {
  background-color: rgb(212 212 212 / 0.75);
}

.bg-neutral-300\/80 {
  background-color: rgb(212 212 212 / 0.8);
}

.bg-neutral-300\/90 {
  background-color: rgb(212 212 212 / 0.9);
}

.bg-neutral-300\/95 {
  background-color: rgb(212 212 212 / 0.95);
}

.bg-neutral-400 {
  --tw-bg-opacity: 1;
  background-color: rgb(163 163 163 / var(--tw-bg-opacity));
}

.bg-neutral-400\/0 {
  background-color: rgb(163 163 163 / 0);
}

.bg-neutral-400\/10 {
  background-color: rgb(163 163 163 / 0.1);
}

.bg-neutral-400\/100 {
  background-color: rgb(163 163 163 / 1);
}

.bg-neutral-400\/20 {
  background-color: rgb(163 163 163 / 0.2);
}

.bg-neutral-400\/25 {
  background-color: rgb(163 163 163 / 0.25);
}

.bg-neutral-400\/30 {
  background-color: rgb(163 163 163 / 0.3);
}

.bg-neutral-400\/40 {
  background-color: rgb(163 163 163 / 0.4);
}

.bg-neutral-400\/5 {
  background-color: rgb(163 163 163 / 0.05);
}

.bg-neutral-400\/50 {
  background-color: rgb(163 163 163 / 0.5);
}

.bg-neutral-400\/60 {
  background-color: rgb(163 163 163 / 0.6);
}

.bg-neutral-400\/70 {
  background-color: rgb(163 163 163 / 0.7);
}

.bg-neutral-400\/75 {
  background-color: rgb(163 163 163 / 0.75);
}

.bg-neutral-400\/80 {
  background-color: rgb(163 163 163 / 0.8);
}

.bg-neutral-400\/90 {
  background-color: rgb(163 163 163 / 0.9);
}

.bg-neutral-400\/95 {
  background-color: rgb(163 163 163 / 0.95);
}

.bg-neutral-50 {
  --tw-bg-opacity: 1;
  background-color: rgb(250 250 250 / var(--tw-bg-opacity));
}

.bg-neutral-50\/0 {
  background-color: rgb(250 250 250 / 0);
}

.bg-neutral-50\/10 {
  background-color: rgb(250 250 250 / 0.1);
}

.bg-neutral-50\/100 {
  background-color: rgb(250 250 250 / 1);
}

.bg-neutral-50\/20 {
  background-color: rgb(250 250 250 / 0.2);
}

.bg-neutral-50\/25 {
  background-color: rgb(250 250 250 / 0.25);
}

.bg-neutral-50\/30 {
  background-color: rgb(250 250 250 / 0.3);
}

.bg-neutral-50\/40 {
  background-color: rgb(250 250 250 / 0.4);
}

.bg-neutral-50\/5 {
  background-color: rgb(250 250 250 / 0.05);
}

.bg-neutral-50\/50 {
  background-color: rgb(250 250 250 / 0.5);
}

.bg-neutral-50\/60 {
  background-color: rgb(250 250 250 / 0.6);
}

.bg-neutral-50\/70 {
  background-color: rgb(250 250 250 / 0.7);
}

.bg-neutral-50\/75 {
  background-color: rgb(250 250 250 / 0.75);
}

.bg-neutral-50\/80 {
  background-color: rgb(250 250 250 / 0.8);
}

.bg-neutral-50\/90 {
  background-color: rgb(250 250 250 / 0.9);
}

.bg-neutral-50\/95 {
  background-color: rgb(250 250 250 / 0.95);
}

.bg-neutral-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(115 115 115 / var(--tw-bg-opacity));
}

.bg-neutral-500\/0 {
  background-color: rgb(115 115 115 / 0);
}

.bg-neutral-500\/10 {
  background-color: rgb(115 115 115 / 0.1);
}

.bg-neutral-500\/100 {
  background-color: rgb(115 115 115 / 1);
}

.bg-neutral-500\/20 {
  background-color: rgb(115 115 115 / 0.2);
}

.bg-neutral-500\/25 {
  background-color: rgb(115 115 115 / 0.25);
}

.bg-neutral-500\/30 {
  background-color: rgb(115 115 115 / 0.3);
}

.bg-neutral-500\/40 {
  background-color: rgb(115 115 115 / 0.4);
}

.bg-neutral-500\/5 {
  background-color: rgb(115 115 115 / 0.05);
}

.bg-neutral-500\/50 {
  background-color: rgb(115 115 115 / 0.5);
}

.bg-neutral-500\/60 {
  background-color: rgb(115 115 115 / 0.6);
}

.bg-neutral-500\/70 {
  background-color: rgb(115 115 115 / 0.7);
}

.bg-neutral-500\/75 {
  background-color: rgb(115 115 115 / 0.75);
}

.bg-neutral-500\/80 {
  background-color: rgb(115 115 115 / 0.8);
}

.bg-neutral-500\/90 {
  background-color: rgb(115 115 115 / 0.9);
}

.bg-neutral-500\/95 {
  background-color: rgb(115 115 115 / 0.95);
}

.bg-neutral-600 {
  --tw-bg-opacity: 1;
  background-color: rgb(82 82 82 / var(--tw-bg-opacity));
}

.bg-neutral-600\/0 {
  background-color: rgb(82 82 82 / 0);
}

.bg-neutral-600\/10 {
  background-color: rgb(82 82 82 / 0.1);
}

.bg-neutral-600\/100 {
  background-color: rgb(82 82 82 / 1);
}

.bg-neutral-600\/20 {
  background-color: rgb(82 82 82 / 0.2);
}

.bg-neutral-600\/25 {
  background-color: rgb(82 82 82 / 0.25);
}

.bg-neutral-600\/30 {
  background-color: rgb(82 82 82 / 0.3);
}

.bg-neutral-600\/40 {
  background-color: rgb(82 82 82 / 0.4);
}

.bg-neutral-600\/5 {
  background-color: rgb(82 82 82 / 0.05);
}

.bg-neutral-600\/50 {
  background-color: rgb(82 82 82 / 0.5);
}

.bg-neutral-600\/60 {
  background-color: rgb(82 82 82 / 0.6);
}

.bg-neutral-600\/70 {
  background-color: rgb(82 82 82 / 0.7);
}

.bg-neutral-600\/75 {
  background-color: rgb(82 82 82 / 0.75);
}

.bg-neutral-600\/80 {
  background-color: rgb(82 82 82 / 0.8);
}

.bg-neutral-600\/90 {
  background-color: rgb(82 82 82 / 0.9);
}

.bg-neutral-600\/95 {
  background-color: rgb(82 82 82 / 0.95);
}

.bg-neutral-700 {
  --tw-bg-opacity: 1;
  background-color: rgb(64 64 64 / var(--tw-bg-opacity));
}

.bg-neutral-700\/0 {
  background-color: rgb(64 64 64 / 0);
}

.bg-neutral-700\/10 {
  background-color: rgb(64 64 64 / 0.1);
}

.bg-neutral-700\/100 {
  background-color: rgb(64 64 64 / 1);
}

.bg-neutral-700\/20 {
  background-color: rgb(64 64 64 / 0.2);
}

.bg-neutral-700\/25 {
  background-color: rgb(64 64 64 / 0.25);
}

.bg-neutral-700\/30 {
  background-color: rgb(64 64 64 / 0.3);
}

.bg-neutral-700\/40 {
  background-color: rgb(64 64 64 / 0.4);
}

.bg-neutral-700\/5 {
  background-color: rgb(64 64 64 / 0.05);
}

.bg-neutral-700\/50 {
  background-color: rgb(64 64 64 / 0.5);
}

.bg-neutral-700\/60 {
  background-color: rgb(64 64 64 / 0.6);
}

.bg-neutral-700\/70 {
  background-color: rgb(64 64 64 / 0.7);
}

.bg-neutral-700\/75 {
  background-color: rgb(64 64 64 / 0.75);
}

.bg-neutral-700\/80 {
  background-color: rgb(64 64 64 / 0.8);
}

.bg-neutral-700\/90 {
  background-color: rgb(64 64 64 / 0.9);
}

.bg-neutral-700\/95 {
  background-color: rgb(64 64 64 / 0.95);
}

.bg-neutral-800 {
  --tw-bg-opacity: 1;
  background-color: rgb(38 38 38 / var(--tw-bg-opacity));
}

.bg-neutral-800\/0 {
  background-color: rgb(38 38 38 / 0);
}

.bg-neutral-800\/10 {
  background-color: rgb(38 38 38 / 0.1);
}

.bg-neutral-800\/100 {
  background-color: rgb(38 38 38 / 1);
}

.bg-neutral-800\/20 {
  background-color: rgb(38 38 38 / 0.2);
}

.bg-neutral-800\/25 {
  background-color: rgb(38 38 38 / 0.25);
}

.bg-neutral-800\/30 {
  background-color: rgb(38 38 38 / 0.3);
}

.bg-neutral-800\/40 {
  background-color: rgb(38 38 38 / 0.4);
}

.bg-neutral-800\/5 {
  background-color: rgb(38 38 38 / 0.05);
}

.bg-neutral-800\/50 {
  background-color: rgb(38 38 38 / 0.5);
}

.bg-neutral-800\/60 {
  background-color: rgb(38 38 38 / 0.6);
}

.bg-neutral-800\/70 {
  background-color: rgb(38 38 38 / 0.7);
}

.bg-neutral-800\/75 {
  background-color: rgb(38 38 38 / 0.75);
}

.bg-neutral-800\/80 {
  background-color: rgb(38 38 38 / 0.8);
}

.bg-neutral-800\/90 {
  background-color: rgb(38 38 38 / 0.9);
}

.bg-neutral-800\/95 {
  background-color: rgb(38 38 38 / 0.95);
}

.bg-neutral-900 {
  --tw-bg-opacity: 1;
  background-color: rgb(23 23 23 / var(--tw-bg-opacity));
}

.bg-neutral-900\/0 {
  background-color: rgb(23 23 23 / 0);
}

.bg-neutral-900\/10 {
  background-color: rgb(23 23 23 / 0.1);
}

.bg-neutral-900\/100 {
  background-color: rgb(23 23 23 / 1);
}

.bg-neutral-900\/20 {
  background-color: rgb(23 23 23 / 0.2);
}

.bg-neutral-900\/25 {
  background-color: rgb(23 23 23 / 0.25);
}

.bg-neutral-900\/30 {
  background-color: rgb(23 23 23 / 0.3);
}

.bg-neutral-900\/40 {
  background-color: rgb(23 23 23 / 0.4);
}

.bg-neutral-900\/5 {
  background-color: rgb(23 23 23 / 0.05);
}

.bg-neutral-900\/50 {
  background-color: rgb(23 23 23 / 0.5);
}

.bg-neutral-900\/60 {
  background-color: rgb(23 23 23 / 0.6);
}

.bg-neutral-900\/70 {
  background-color: rgb(23 23 23 / 0.7);
}

.bg-neutral-900\/75 {
  background-color: rgb(23 23 23 / 0.75);
}

.bg-neutral-900\/80 {
  background-color: rgb(23 23 23 / 0.8);
}

.bg-neutral-900\/90 {
  background-color: rgb(23 23 23 / 0.9);
}

.bg-neutral-900\/95 {
  background-color: rgb(23 23 23 / 0.95);
}

.bg-neutral-950 {
  --tw-bg-opacity: 1;
  background-color: rgb(10 10 10 / var(--tw-bg-opacity));
}

.bg-neutral-950\/0 {
  background-color: rgb(10 10 10 / 0);
}

.bg-neutral-950\/10 {
  background-color: rgb(10 10 10 / 0.1);
}

.bg-neutral-950\/100 {
  background-color: rgb(10 10 10 / 1);
}

.bg-neutral-950\/20 {
  background-color: rgb(10 10 10 / 0.2);
}

.bg-neutral-950\/25 {
  background-color: rgb(10 10 10 / 0.25);
}

.bg-neutral-950\/30 {
  background-color: rgb(10 10 10 / 0.3);
}

.bg-neutral-950\/40 {
  background-color: rgb(10 10 10 / 0.4);
}

.bg-neutral-950\/5 {
  background-color: rgb(10 10 10 / 0.05);
}

.bg-neutral-950\/50 {
  background-color: rgb(10 10 10 / 0.5);
}

.bg-neutral-950\/60 {
  background-color: rgb(10 10 10 / 0.6);
}

.bg-neutral-950\/70 {
  background-color: rgb(10 10 10 / 0.7);
}

.bg-neutral-950\/75 {
  background-color: rgb(10 10 10 / 0.75);
}

.bg-neutral-950\/80 {
  background-color: rgb(10 10 10 / 0.8);
}

.bg-neutral-950\/90 {
  background-color: rgb(10 10 10 / 0.9);
}

.bg-neutral-950\/95 {
  background-color: rgb(10 10 10 / 0.95);
}

.bg-orange-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(255 237 213 / var(--tw-bg-opacity));
}

.bg-orange-100\/0 {
  background-color: rgb(255 237 213 / 0);
}

.bg-orange-100\/10 {
  background-color: rgb(255 237 213 / 0.1);
}

.bg-orange-100\/100 {
  background-color: rgb(255 237 213 / 1);
}

.bg-orange-100\/20 {
  background-color: rgb(255 237 213 / 0.2);
}

.bg-orange-100\/25 {
  background-color: rgb(255 237 213 / 0.25);
}

.bg-orange-100\/30 {
  background-color: rgb(255 237 213 / 0.3);
}

.bg-orange-100\/40 {
  background-color: rgb(255 237 213 / 0.4);
}

.bg-orange-100\/5 {
  background-color: rgb(255 237 213 / 0.05);
}

.bg-orange-100\/50 {
  background-color: rgb(255 237 213 / 0.5);
}

.bg-orange-100\/60 {
  background-color: rgb(255 237 213 / 0.6);
}

.bg-orange-100\/70 {
  background-color: rgb(255 237 213 / 0.7);
}

.bg-orange-100\/75 {
  background-color: rgb(255 237 213 / 0.75);
}

.bg-orange-100\/80 {
  background-color: rgb(255 237 213 / 0.8);
}

.bg-orange-100\/90 {
  background-color: rgb(255 237 213 / 0.9);
}

.bg-orange-100\/95 {
  background-color: rgb(255 237 213 / 0.95);
}

.bg-orange-200 {
  --tw-bg-opacity: 1;
  background-color: rgb(254 215 170 / var(--tw-bg-opacity));
}

.bg-orange-200\/0 {
  background-color: rgb(254 215 170 / 0);
}

.bg-orange-200\/10 {
  background-color: rgb(254 215 170 / 0.1);
}

.bg-orange-200\/100 {
  background-color: rgb(254 215 170 / 1);
}

.bg-orange-200\/20 {
  background-color: rgb(254 215 170 / 0.2);
}

.bg-orange-200\/25 {
  background-color: rgb(254 215 170 / 0.25);
}

.bg-orange-200\/30 {
  background-color: rgb(254 215 170 / 0.3);
}

.bg-orange-200\/40 {
  background-color: rgb(254 215 170 / 0.4);
}

.bg-orange-200\/5 {
  background-color: rgb(254 215 170 / 0.05);
}

.bg-orange-200\/50 {
  background-color: rgb(254 215 170 / 0.5);
}

.bg-orange-200\/60 {
  background-color: rgb(254 215 170 / 0.6);
}

.bg-orange-200\/70 {
  background-color: rgb(254 215 170 / 0.7);
}

.bg-orange-200\/75 {
  background-color: rgb(254 215 170 / 0.75);
}

.bg-orange-200\/80 {
  background-color: rgb(254 215 170 / 0.8);
}

.bg-orange-200\/90 {
  background-color: rgb(254 215 170 / 0.9);
}

.bg-orange-200\/95 {
  background-color: rgb(254 215 170 / 0.95);
}

.bg-orange-300 {
  --tw-bg-opacity: 1;
  background-color: rgb(253 186 116 / var(--tw-bg-opacity));
}

.bg-orange-300\/0 {
  background-color: rgb(253 186 116 / 0);
}

.bg-orange-300\/10 {
  background-color: rgb(253 186 116 / 0.1);
}

.bg-orange-300\/100 {
  background-color: rgb(253 186 116 / 1);
}

.bg-orange-300\/20 {
  background-color: rgb(253 186 116 / 0.2);
}

.bg-orange-300\/25 {
  background-color: rgb(253 186 116 / 0.25);
}

.bg-orange-300\/30 {
  background-color: rgb(253 186 116 / 0.3);
}

.bg-orange-300\/40 {
  background-color: rgb(253 186 116 / 0.4);
}

.bg-orange-300\/5 {
  background-color: rgb(253 186 116 / 0.05);
}

.bg-orange-300\/50 {
  background-color: rgb(253 186 116 / 0.5);
}

.bg-orange-300\/60 {
  background-color: rgb(253 186 116 / 0.6);
}

.bg-orange-300\/70 {
  background-color: rgb(253 186 116 / 0.7);
}

.bg-orange-300\/75 {
  background-color: rgb(253 186 116 / 0.75);
}

.bg-orange-300\/80 {
  background-color: rgb(253 186 116 / 0.8);
}

.bg-orange-300\/90 {
  background-color: rgb(253 186 116 / 0.9);
}

.bg-orange-300\/95 {
  background-color: rgb(253 186 116 / 0.95);
}

.bg-orange-400 {
  --tw-bg-opacity: 1;
  background-color: rgb(251 146 60 / var(--tw-bg-opacity));
}

.bg-orange-400\/0 {
  background-color: rgb(251 146 60 / 0);
}

.bg-orange-400\/10 {
  background-color: rgb(251 146 60 / 0.1);
}

.bg-orange-400\/100 {
  background-color: rgb(251 146 60 / 1);
}

.bg-orange-400\/20 {
  background-color: rgb(251 146 60 / 0.2);
}

.bg-orange-400\/25 {
  background-color: rgb(251 146 60 / 0.25);
}

.bg-orange-400\/30 {
  background-color: rgb(251 146 60 / 0.3);
}

.bg-orange-400\/40 {
  background-color: rgb(251 146 60 / 0.4);
}

.bg-orange-400\/5 {
  background-color: rgb(251 146 60 / 0.05);
}

.bg-orange-400\/50 {
  background-color: rgb(251 146 60 / 0.5);
}

.bg-orange-400\/60 {
  background-color: rgb(251 146 60 / 0.6);
}

.bg-orange-400\/70 {
  background-color: rgb(251 146 60 / 0.7);
}

.bg-orange-400\/75 {
  background-color: rgb(251 146 60 / 0.75);
}

.bg-orange-400\/80 {
  background-color: rgb(251 146 60 / 0.8);
}

.bg-orange-400\/90 {
  background-color: rgb(251 146 60 / 0.9);
}

.bg-orange-400\/95 {
  background-color: rgb(251 146 60 / 0.95);
}

.bg-orange-50 {
  --tw-bg-opacity: 1;
  background-color: rgb(255 247 237 / var(--tw-bg-opacity));
}

.bg-orange-50\/0 {
  background-color: rgb(255 247 237 / 0);
}

.bg-orange-50\/10 {
  background-color: rgb(255 247 237 / 0.1);
}

.bg-orange-50\/100 {
  background-color: rgb(255 247 237 / 1);
}

.bg-orange-50\/20 {
  background-color: rgb(255 247 237 / 0.2);
}

.bg-orange-50\/25 {
  background-color: rgb(255 247 237 / 0.25);
}

.bg-orange-50\/30 {
  background-color: rgb(255 247 237 / 0.3);
}

.bg-orange-50\/40 {
  background-color: rgb(255 247 237 / 0.4);
}

.bg-orange-50\/5 {
  background-color: rgb(255 247 237 / 0.05);
}

.bg-orange-50\/50 {
  background-color: rgb(255 247 237 / 0.5);
}

.bg-orange-50\/60 {
  background-color: rgb(255 247 237 / 0.6);
}

.bg-orange-50\/70 {
  background-color: rgb(255 247 237 / 0.7);
}

.bg-orange-50\/75 {
  background-color: rgb(255 247 237 / 0.75);
}

.bg-orange-50\/80 {
  background-color: rgb(255 247 237 / 0.8);
}

.bg-orange-50\/90 {
  background-color: rgb(255 247 237 / 0.9);
}

.bg-orange-50\/95 {
  background-color: rgb(255 247 237 / 0.95);
}

.bg-orange-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(249 115 22 / var(--tw-bg-opacity));
}

.bg-orange-500\/0 {
  background-color: rgb(249 115 22 / 0);
}

.bg-orange-500\/10 {
  background-color: rgb(249 115 22 / 0.1);
}

.bg-orange-500\/100 {
  background-color: rgb(249 115 22 / 1);
}

.bg-orange-500\/20 {
  background-color: rgb(249 115 22 / 0.2);
}

.bg-orange-500\/25 {
  background-color: rgb(249 115 22 / 0.25);
}

.bg-orange-500\/30 {
  background-color: rgb(249 115 22 / 0.3);
}

.bg-orange-500\/40 {
  background-color: rgb(249 115 22 / 0.4);
}

.bg-orange-500\/5 {
  background-color: rgb(249 115 22 / 0.05);
}

.bg-orange-500\/50 {
  background-color: rgb(249 115 22 / 0.5);
}

.bg-orange-500\/60 {
  background-color: rgb(249 115 22 / 0.6);
}

.bg-orange-500\/70 {
  background-color: rgb(249 115 22 / 0.7);
}

.bg-orange-500\/75 {
  background-color: rgb(249 115 22 / 0.75);
}

.bg-orange-500\/80 {
  background-color: rgb(249 115 22 / 0.8);
}

.bg-orange-500\/90 {
  background-color: rgb(249 115 22 / 0.9);
}

.bg-orange-500\/95 {
  background-color: rgb(249 115 22 / 0.95);
}

.bg-orange-600 {
  --tw-bg-opacity: 1;
  background-color: rgb(234 88 12 / var(--tw-bg-opacity));
}

.bg-orange-600\/0 {
  background-color: rgb(234 88 12 / 0);
}

.bg-orange-600\/10 {
  background-color: rgb(234 88 12 / 0.1);
}

.bg-orange-600\/100 {
  background-color: rgb(234 88 12 / 1);
}

.bg-orange-600\/20 {
  background-color: rgb(234 88 12 / 0.2);
}

.bg-orange-600\/25 {
  background-color: rgb(234 88 12 / 0.25);
}

.bg-orange-600\/30 {
  background-color: rgb(234 88 12 / 0.3);
}

.bg-orange-600\/40 {
  background-color: rgb(234 88 12 / 0.4);
}

.bg-orange-600\/5 {
  background-color: rgb(234 88 12 / 0.05);
}

.bg-orange-600\/50 {
  background-color: rgb(234 88 12 / 0.5);
}

.bg-orange-600\/60 {
  background-color: rgb(234 88 12 / 0.6);
}

.bg-orange-600\/70 {
  background-color: rgb(234 88 12 / 0.7);
}

.bg-orange-600\/75 {
  background-color: rgb(234 88 12 / 0.75);
}

.bg-orange-600\/80 {
  background-color: rgb(234 88 12 / 0.8);
}

.bg-orange-600\/90 {
  background-color: rgb(234 88 12 / 0.9);
}

.bg-orange-600\/95 {
  background-color: rgb(234 88 12 / 0.95);
}

.bg-orange-700 {
  --tw-bg-opacity: 1;
  background-color: rgb(194 65 12 / var(--tw-bg-opacity));
}

.bg-orange-700\/0 {
  background-color: rgb(194 65 12 / 0);
}

.bg-orange-700\/10 {
  background-color: rgb(194 65 12 / 0.1);
}

.bg-orange-700\/100 {
  background-color: rgb(194 65 12 / 1);
}

.bg-orange-700\/20 {
  background-color: rgb(194 65 12 / 0.2);
}

.bg-orange-700\/25 {
  background-color: rgb(194 65 12 / 0.25);
}

.bg-orange-700\/30 {
  background-color: rgb(194 65 12 / 0.3);
}

.bg-orange-700\/40 {
  background-color: rgb(194 65 12 / 0.4);
}

.bg-orange-700\/5 {
  background-color: rgb(194 65 12 / 0.05);
}

.bg-orange-700\/50 {
  background-color: rgb(194 65 12 / 0.5);
}

.bg-orange-700\/60 {
  background-color: rgb(194 65 12 / 0.6);
}

.bg-orange-700\/70 {
  background-color: rgb(194 65 12 / 0.7);
}

.bg-orange-700\/75 {
  background-color: rgb(194 65 12 / 0.75);
}

.bg-orange-700\/80 {
  background-color: rgb(194 65 12 / 0.8);
}

.bg-orange-700\/90 {
  background-color: rgb(194 65 12 / 0.9);
}

.bg-orange-700\/95 {
  background-color: rgb(194 65 12 / 0.95);
}

.bg-orange-800 {
  --tw-bg-opacity: 1;
  background-color: rgb(154 52 18 / var(--tw-bg-opacity));
}

.bg-orange-800\/0 {
  background-color: rgb(154 52 18 / 0);
}

.bg-orange-800\/10 {
  background-color: rgb(154 52 18 / 0.1);
}

.bg-orange-800\/100 {
  background-color: rgb(154 52 18 / 1);
}

.bg-orange-800\/20 {
  background-color: rgb(154 52 18 / 0.2);
}

.bg-orange-800\/25 {
  background-color: rgb(154 52 18 / 0.25);
}

.bg-orange-800\/30 {
  background-color: rgb(154 52 18 / 0.3);
}

.bg-orange-800\/40 {
  background-color: rgb(154 52 18 / 0.4);
}

.bg-orange-800\/5 {
  background-color: rgb(154 52 18 / 0.05);
}

.bg-orange-800\/50 {
  background-color: rgb(154 52 18 / 0.5);
}

.bg-orange-800\/60 {
  background-color: rgb(154 52 18 / 0.6);
}

.bg-orange-800\/70 {
  background-color: rgb(154 52 18 / 0.7);
}

.bg-orange-800\/75 {
  background-color: rgb(154 52 18 / 0.75);
}

.bg-orange-800\/80 {
  background-color: rgb(154 52 18 / 0.8);
}

.bg-orange-800\/90 {
  background-color: rgb(154 52 18 / 0.9);
}

.bg-orange-800\/95 {
  background-color: rgb(154 52 18 / 0.95);
}

.bg-orange-900 {
  --tw-bg-opacity: 1;
  background-color: rgb(124 45 18 / var(--tw-bg-opacity));
}

.bg-orange-900\/0 {
  background-color: rgb(124 45 18 / 0);
}

.bg-orange-900\/10 {
  background-color: rgb(124 45 18 / 0.1);
}

.bg-orange-900\/100 {
  background-color: rgb(124 45 18 / 1);
}

.bg-orange-900\/20 {
  background-color: rgb(124 45 18 / 0.2);
}

.bg-orange-900\/25 {
  background-color: rgb(124 45 18 / 0.25);
}

.bg-orange-900\/30 {
  background-color: rgb(124 45 18 / 0.3);
}

.bg-orange-900\/40 {
  background-color: rgb(124 45 18 / 0.4);
}

.bg-orange-900\/5 {
  background-color: rgb(124 45 18 / 0.05);
}

.bg-orange-900\/50 {
  background-color: rgb(124 45 18 / 0.5);
}

.bg-orange-900\/60 {
  background-color: rgb(124 45 18 / 0.6);
}

.bg-orange-900\/70 {
  background-color: rgb(124 45 18 / 0.7);
}

.bg-orange-900\/75 {
  background-color: rgb(124 45 18 / 0.75);
}

.bg-orange-900\/80 {
  background-color: rgb(124 45 18 / 0.8);
}

.bg-orange-900\/90 {
  background-color: rgb(124 45 18 / 0.9);
}

.bg-orange-900\/95 {
  background-color: rgb(124 45 18 / 0.95);
}

.bg-orange-950 {
  --tw-bg-opacity: 1;
  background-color: rgb(67 20 7 / var(--tw-bg-opacity));
}

.bg-orange-950\/0 {
  background-color: rgb(67 20 7 / 0);
}

.bg-orange-950\/10 {
  background-color: rgb(67 20 7 / 0.1);
}

.bg-orange-950\/100 {
  background-color: rgb(67 20 7 / 1);
}

.bg-orange-950\/20 {
  background-color: rgb(67 20 7 / 0.2);
}

.bg-orange-950\/25 {
  background-color: rgb(67 20 7 / 0.25);
}

.bg-orange-950\/30 {
  background-color: rgb(67 20 7 / 0.3);
}

.bg-orange-950\/40 {
  background-color: rgb(67 20 7 / 0.4);
}

.bg-orange-950\/5 {
  background-color: rgb(67 20 7 / 0.05);
}

.bg-orange-950\/50 {
  background-color: rgb(67 20 7 / 0.5);
}

.bg-orange-950\/60 {
  background-color: rgb(67 20 7 / 0.6);
}

.bg-orange-950\/70 {
  background-color: rgb(67 20 7 / 0.7);
}

.bg-orange-950\/75 {
  background-color: rgb(67 20 7 / 0.75);
}

.bg-orange-950\/80 {
  background-color: rgb(67 20 7 / 0.8);
}

.bg-orange-950\/90 {
  background-color: rgb(67 20 7 / 0.9);
}

.bg-orange-950\/95 {
  background-color: rgb(67 20 7 / 0.95);
}

.bg-pink-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(252 231 243 / var(--tw-bg-opacity));
}

.bg-pink-100\/0 {
  background-color: rgb(252 231 243 / 0);
}

.bg-pink-100\/10 {
  background-color: rgb(252 231 243 / 0.1);
}

.bg-pink-100\/100 {
  background-color: rgb(252 231 243 / 1);
}

.bg-pink-100\/20 {
  background-color: rgb(252 231 243 / 0.2);
}

.bg-pink-100\/25 {
  background-color: rgb(252 231 243 / 0.25);
}

.bg-pink-100\/30 {
  background-color: rgb(252 231 243 / 0.3);
}

.bg-pink-100\/40 {
  background-color: rgb(252 231 243 / 0.4);
}

.bg-pink-100\/5 {
  background-color: rgb(252 231 243 / 0.05);
}

.bg-pink-100\/50 {
  background-color: rgb(252 231 243 / 0.5);
}

.bg-pink-100\/60 {
  background-color: rgb(252 231 243 / 0.6);
}

.bg-pink-100\/70 {
  background-color: rgb(252 231 243 / 0.7);
}

.bg-pink-100\/75 {
  background-color: rgb(252 231 243 / 0.75);
}

.bg-pink-100\/80 {
  background-color: rgb(252 231 243 / 0.8);
}

.bg-pink-100\/90 {
  background-color: rgb(252 231 243 / 0.9);
}

.bg-pink-100\/95 {
  background-color: rgb(252 231 243 / 0.95);
}

.bg-pink-200 {
  --tw-bg-opacity: 1;
  background-color: rgb(251 207 232 / var(--tw-bg-opacity));
}

.bg-pink-200\/0 {
  background-color: rgb(251 207 232 / 0);
}

.bg-pink-200\/10 {
  background-color: rgb(251 207 232 / 0.1);
}

.bg-pink-200\/100 {
  background-color: rgb(251 207 232 / 1);
}

.bg-pink-200\/20 {
  background-color: rgb(251 207 232 / 0.2);
}

.bg-pink-200\/25 {
  background-color: rgb(251 207 232 / 0.25);
}

.bg-pink-200\/30 {
  background-color: rgb(251 207 232 / 0.3);
}

.bg-pink-200\/40 {
  background-color: rgb(251 207 232 / 0.4);
}

.bg-pink-200\/5 {
  background-color: rgb(251 207 232 / 0.05);
}

.bg-pink-200\/50 {
  background-color: rgb(251 207 232 / 0.5);
}

.bg-pink-200\/60 {
  background-color: rgb(251 207 232 / 0.6);
}

.bg-pink-200\/70 {
  background-color: rgb(251 207 232 / 0.7);
}

.bg-pink-200\/75 {
  background-color: rgb(251 207 232 / 0.75);
}

.bg-pink-200\/80 {
  background-color: rgb(251 207 232 / 0.8);
}

.bg-pink-200\/90 {
  background-color: rgb(251 207 232 / 0.9);
}

.bg-pink-200\/95 {
  background-color: rgb(251 207 232 / 0.95);
}

.bg-pink-300 {
  --tw-bg-opacity: 1;
  background-color: rgb(249 168 212 / var(--tw-bg-opacity));
}

.bg-pink-300\/0 {
  background-color: rgb(249 168 212 / 0);
}

.bg-pink-300\/10 {
  background-color: rgb(249 168 212 / 0.1);
}

.bg-pink-300\/100 {
  background-color: rgb(249 168 212 / 1);
}

.bg-pink-300\/20 {
  background-color: rgb(249 168 212 / 0.2);
}

.bg-pink-300\/25 {
  background-color: rgb(249 168 212 / 0.25);
}

.bg-pink-300\/30 {
  background-color: rgb(249 168 212 / 0.3);
}

.bg-pink-300\/40 {
  background-color: rgb(249 168 212 / 0.4);
}

.bg-pink-300\/5 {
  background-color: rgb(249 168 212 / 0.05);
}

.bg-pink-300\/50 {
  background-color: rgb(249 168 212 / 0.5);
}

.bg-pink-300\/60 {
  background-color: rgb(249 168 212 / 0.6);
}

.bg-pink-300\/70 {
  background-color: rgb(249 168 212 / 0.7);
}

.bg-pink-300\/75 {
  background-color: rgb(249 168 212 / 0.75);
}

.bg-pink-300\/80 {
  background-color: rgb(249 168 212 / 0.8);
}

.bg-pink-300\/90 {
  background-color: rgb(249 168 212 / 0.9);
}

.bg-pink-300\/95 {
  background-color: rgb(249 168 212 / 0.95);
}

.bg-pink-400 {
  --tw-bg-opacity: 1;
  background-color: rgb(244 114 182 / var(--tw-bg-opacity));
}

.bg-pink-400\/0 {
  background-color: rgb(244 114 182 / 0);
}

.bg-pink-400\/10 {
  background-color: rgb(244 114 182 / 0.1);
}

.bg-pink-400\/100 {
  background-color: rgb(244 114 182 / 1);
}

.bg-pink-400\/20 {
  background-color: rgb(244 114 182 / 0.2);
}

.bg-pink-400\/25 {
  background-color: rgb(244 114 182 / 0.25);
}

.bg-pink-400\/30 {
  background-color: rgb(244 114 182 / 0.3);
}

.bg-pink-400\/40 {
  background-color: rgb(244 114 182 / 0.4);
}

.bg-pink-400\/5 {
  background-color: rgb(244 114 182 / 0.05);
}

.bg-pink-400\/50 {
  background-color: rgb(244 114 182 / 0.5);
}

.bg-pink-400\/60 {
  background-color: rgb(244 114 182 / 0.6);
}

.bg-pink-400\/70 {
  background-color: rgb(244 114 182 / 0.7);
}

.bg-pink-400\/75 {
  background-color: rgb(244 114 182 / 0.75);
}

.bg-pink-400\/80 {
  background-color: rgb(244 114 182 / 0.8);
}

.bg-pink-400\/90 {
  background-color: rgb(244 114 182 / 0.9);
}

.bg-pink-400\/95 {
  background-color: rgb(244 114 182 / 0.95);
}

.bg-pink-50 {
  --tw-bg-opacity: 1;
  background-color: rgb(253 242 248 / var(--tw-bg-opacity));
}

.bg-pink-50\/0 {
  background-color: rgb(253 242 248 / 0);
}

.bg-pink-50\/10 {
  background-color: rgb(253 242 248 / 0.1);
}

.bg-pink-50\/100 {
  background-color: rgb(253 242 248 / 1);
}

.bg-pink-50\/20 {
  background-color: rgb(253 242 248 / 0.2);
}

.bg-pink-50\/25 {
  background-color: rgb(253 242 248 / 0.25);
}

.bg-pink-50\/30 {
  background-color: rgb(253 242 248 / 0.3);
}

.bg-pink-50\/40 {
  background-color: rgb(253 242 248 / 0.4);
}

.bg-pink-50\/5 {
  background-color: rgb(253 242 248 / 0.05);
}

.bg-pink-50\/50 {
  background-color: rgb(253 242 248 / 0.5);
}

.bg-pink-50\/60 {
  background-color: rgb(253 242 248 / 0.6);
}

.bg-pink-50\/70 {
  background-color: rgb(253 242 248 / 0.7);
}

.bg-pink-50\/75 {
  background-color: rgb(253 242 248 / 0.75);
}

.bg-pink-50\/80 {
  background-color: rgb(253 242 248 / 0.8);
}

.bg-pink-50\/90 {
  background-color: rgb(253 242 248 / 0.9);
}

.bg-pink-50\/95 {
  background-color: rgb(253 242 248 / 0.95);
}

.bg-pink-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(236 72 153 / var(--tw-bg-opacity));
}

.bg-pink-500\/0 {
  background-color: rgb(236 72 153 / 0);
}

.bg-pink-500\/10 {
  background-color: rgb(236 72 153 / 0.1);
}

.bg-pink-500\/100 {
  background-color: rgb(236 72 153 / 1);
}

.bg-pink-500\/20 {
  background-color: rgb(236 72 153 / 0.2);
}

.bg-pink-500\/25 {
  background-color: rgb(236 72 153 / 0.25);
}

.bg-pink-500\/30 {
  background-color: rgb(236 72 153 / 0.3);
}

.bg-pink-500\/40 {
  background-color: rgb(236 72 153 / 0.4);
}

.bg-pink-500\/5 {
  background-color: rgb(236 72 153 / 0.05);
}

.bg-pink-500\/50 {
  background-color: rgb(236 72 153 / 0.5);
}

.bg-pink-500\/60 {
  background-color: rgb(236 72 153 / 0.6);
}

.bg-pink-500\/70 {
  background-color: rgb(236 72 153 / 0.7);
}

.bg-pink-500\/75 {
  background-color: rgb(236 72 153 / 0.75);
}

.bg-pink-500\/80 {
  background-color: rgb(236 72 153 / 0.8);
}

.bg-pink-500\/90 {
  background-color: rgb(236 72 153 / 0.9);
}

.bg-pink-500\/95 {
  background-color: rgb(236 72 153 / 0.95);
}

.bg-pink-600 {
  --tw-bg-opacity: 1;
  background-color: rgb(219 39 119 / var(--tw-bg-opacity));
}

.bg-pink-600\/0 {
  background-color: rgb(219 39 119 / 0);
}

.bg-pink-600\/10 {
  background-color: rgb(219 39 119 / 0.1);
}

.bg-pink-600\/100 {
  background-color: rgb(219 39 119 / 1);
}

.bg-pink-600\/20 {
  background-color: rgb(219 39 119 / 0.2);
}

.bg-pink-600\/25 {
  background-color: rgb(219 39 119 / 0.25);
}

.bg-pink-600\/30 {
  background-color: rgb(219 39 119 / 0.3);
}

.bg-pink-600\/40 {
  background-color: rgb(219 39 119 / 0.4);
}

.bg-pink-600\/5 {
  background-color: rgb(219 39 119 / 0.05);
}

.bg-pink-600\/50 {
  background-color: rgb(219 39 119 / 0.5);
}

.bg-pink-600\/60 {
  background-color: rgb(219 39 119 / 0.6);
}

.bg-pink-600\/70 {
  background-color: rgb(219 39 119 / 0.7);
}

.bg-pink-600\/75 {
  background-color: rgb(219 39 119 / 0.75);
}

.bg-pink-600\/80 {
  background-color: rgb(219 39 119 / 0.8);
}

.bg-pink-600\/90 {
  background-color: rgb(219 39 119 / 0.9);
}

.bg-pink-600\/95 {
  background-color: rgb(219 39 119 / 0.95);
}

.bg-pink-700 {
  --tw-bg-opacity: 1;
  background-color: rgb(190 24 93 / var(--tw-bg-opacity));
}

.bg-pink-700\/0 {
  background-color: rgb(190 24 93 / 0);
}

.bg-pink-700\/10 {
  background-color: rgb(190 24 93 / 0.1);
}

.bg-pink-700\/100 {
  background-color: rgb(190 24 93 / 1);
}

.bg-pink-700\/20 {
  background-color: rgb(190 24 93 / 0.2);
}

.bg-pink-700\/25 {
  background-color: rgb(190 24 93 / 0.25);
}

.bg-pink-700\/30 {
  background-color: rgb(190 24 93 / 0.3);
}

.bg-pink-700\/40 {
  background-color: rgb(190 24 93 / 0.4);
}

.bg-pink-700\/5 {
  background-color: rgb(190 24 93 / 0.05);
}

.bg-pink-700\/50 {
  background-color: rgb(190 24 93 / 0.5);
}

.bg-pink-700\/60 {
  background-color: rgb(190 24 93 / 0.6);
}

.bg-pink-700\/70 {
  background-color: rgb(190 24 93 / 0.7);
}

.bg-pink-700\/75 {
  background-color: rgb(190 24 93 / 0.75);
}

.bg-pink-700\/80 {
  background-color: rgb(190 24 93 / 0.8);
}

.bg-pink-700\/90 {
  background-color: rgb(190 24 93 / 0.9);
}

.bg-pink-700\/95 {
  background-color: rgb(190 24 93 / 0.95);
}

.bg-pink-800 {
  --tw-bg-opacity: 1;
  background-color: rgb(157 23 77 / var(--tw-bg-opacity));
}

.bg-pink-800\/0 {
  background-color: rgb(157 23 77 / 0);
}

.bg-pink-800\/10 {
  background-color: rgb(157 23 77 / 0.1);
}

.bg-pink-800\/100 {
  background-color: rgb(157 23 77 / 1);
}

.bg-pink-800\/20 {
  background-color: rgb(157 23 77 / 0.2);
}

.bg-pink-800\/25 {
  background-color: rgb(157 23 77 / 0.25);
}

.bg-pink-800\/30 {
  background-color: rgb(157 23 77 / 0.3);
}

.bg-pink-800\/40 {
  background-color: rgb(157 23 77 / 0.4);
}

.bg-pink-800\/5 {
  background-color: rgb(157 23 77 / 0.05);
}

.bg-pink-800\/50 {
  background-color: rgb(157 23 77 / 0.5);
}

.bg-pink-800\/60 {
  background-color: rgb(157 23 77 / 0.6);
}

.bg-pink-800\/70 {
  background-color: rgb(157 23 77 / 0.7);
}

.bg-pink-800\/75 {
  background-color: rgb(157 23 77 / 0.75);
}

.bg-pink-800\/80 {
  background-color: rgb(157 23 77 / 0.8);
}

.bg-pink-800\/90 {
  background-color: rgb(157 23 77 / 0.9);
}

.bg-pink-800\/95 {
  background-color: rgb(157 23 77 / 0.95);
}

.bg-pink-900 {
  --tw-bg-opacity: 1;
  background-color: rgb(131 24 67 / var(--tw-bg-opacity));
}

.bg-pink-900\/0 {
  background-color: rgb(131 24 67 / 0);
}

.bg-pink-900\/10 {
  background-color: rgb(131 24 67 / 0.1);
}

.bg-pink-900\/100 {
  background-color: rgb(131 24 67 / 1);
}

.bg-pink-900\/20 {
  background-color: rgb(131 24 67 / 0.2);
}

.bg-pink-900\/25 {
  background-color: rgb(131 24 67 / 0.25);
}

.bg-pink-900\/30 {
  background-color: rgb(131 24 67 / 0.3);
}

.bg-pink-900\/40 {
  background-color: rgb(131 24 67 / 0.4);
}

.bg-pink-900\/5 {
  background-color: rgb(131 24 67 / 0.05);
}

.bg-pink-900\/50 {
  background-color: rgb(131 24 67 / 0.5);
}

.bg-pink-900\/60 {
  background-color: rgb(131 24 67 / 0.6);
}

.bg-pink-900\/70 {
  background-color: rgb(131 24 67 / 0.7);
}

.bg-pink-900\/75 {
  background-color: rgb(131 24 67 / 0.75);
}

.bg-pink-900\/80 {
  background-color: rgb(131 24 67 / 0.8);
}

.bg-pink-900\/90 {
  background-color: rgb(131 24 67 / 0.9);
}

.bg-pink-900\/95 {
  background-color: rgb(131 24 67 / 0.95);
}

.bg-pink-950 {
  --tw-bg-opacity: 1;
  background-color: rgb(80 7 36 / var(--tw-bg-opacity));
}

.bg-pink-950\/0 {
  background-color: rgb(80 7 36 / 0);
}

.bg-pink-950\/10 {
  background-color: rgb(80 7 36 / 0.1);
}

.bg-pink-950\/100 {
  background-color: rgb(80 7 36 / 1);
}

.bg-pink-950\/20 {
  background-color: rgb(80 7 36 / 0.2);
}

.bg-pink-950\/25 {
  background-color: rgb(80 7 36 / 0.25);
}

.bg-pink-950\/30 {
  background-color: rgb(80 7 36 / 0.3);
}

.bg-pink-950\/40 {
  background-color: rgb(80 7 36 / 0.4);
}

.bg-pink-950\/5 {
  background-color: rgb(80 7 36 / 0.05);
}

.bg-pink-950\/50 {
  background-color: rgb(80 7 36 / 0.5);
}

.bg-pink-950\/60 {
  background-color: rgb(80 7 36 / 0.6);
}

.bg-pink-950\/70 {
  background-color: rgb(80 7 36 / 0.7);
}

.bg-pink-950\/75 {
  background-color: rgb(80 7 36 / 0.75);
}

.bg-pink-950\/80 {
  background-color: rgb(80 7 36 / 0.8);
}

.bg-pink-950\/90 {
  background-color: rgb(80 7 36 / 0.9);
}

.bg-pink-950\/95 {
  background-color: rgb(80 7 36 / 0.95);
}

.bg-purple-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(243 232 255 / var(--tw-bg-opacity));
}

.bg-purple-100\/0 {
  background-color: rgb(243 232 255 / 0);
}

.bg-purple-100\/10 {
  background-color: rgb(243 232 255 / 0.1);
}

.bg-purple-100\/100 {
  background-color: rgb(243 232 255 / 1);
}

.bg-purple-100\/20 {
  background-color: rgb(243 232 255 / 0.2);
}

.bg-purple-100\/25 {
  background-color: rgb(243 232 255 / 0.25);
}

.bg-purple-100\/30 {
  background-color: rgb(243 232 255 / 0.3);
}

.bg-purple-100\/40 {
  background-color: rgb(243 232 255 / 0.4);
}

.bg-purple-100\/5 {
  background-color: rgb(243 232 255 / 0.05);
}

.bg-purple-100\/50 {
  background-color: rgb(243 232 255 / 0.5);
}

.bg-purple-100\/60 {
  background-color: rgb(243 232 255 / 0.6);
}

.bg-purple-100\/70 {
  background-color: rgb(243 232 255 / 0.7);
}

.bg-purple-100\/75 {
  background-color: rgb(243 232 255 / 0.75);
}

.bg-purple-100\/80 {
  background-color: rgb(243 232 255 / 0.8);
}

.bg-purple-100\/90 {
  background-color: rgb(243 232 255 / 0.9);
}

.bg-purple-100\/95 {
  background-color: rgb(243 232 255 / 0.95);
}

.bg-purple-200 {
  --tw-bg-opacity: 1;
  background-color: rgb(233 213 255 / var(--tw-bg-opacity));
}

.bg-purple-200\/0 {
  background-color: rgb(233 213 255 / 0);
}

.bg-purple-200\/10 {
  background-color: rgb(233 213 255 / 0.1);
}

.bg-purple-200\/100 {
  background-color: rgb(233 213 255 / 1);
}

.bg-purple-200\/20 {
  background-color: rgb(233 213 255 / 0.2);
}

.bg-purple-200\/25 {
  background-color: rgb(233 213 255 / 0.25);
}

.bg-purple-200\/30 {
  background-color: rgb(233 213 255 / 0.3);
}

.bg-purple-200\/40 {
  background-color: rgb(233 213 255 / 0.4);
}

.bg-purple-200\/5 {
  background-color: rgb(233 213 255 / 0.05);
}

.bg-purple-200\/50 {
  background-color: rgb(233 213 255 / 0.5);
}

.bg-purple-200\/60 {
  background-color: rgb(233 213 255 / 0.6);
}

.bg-purple-200\/70 {
  background-color: rgb(233 213 255 / 0.7);
}

.bg-purple-200\/75 {
  background-color: rgb(233 213 255 / 0.75);
}

.bg-purple-200\/80 {
  background-color: rgb(233 213 255 / 0.8);
}

.bg-purple-200\/90 {
  background-color: rgb(233 213 255 / 0.9);
}

.bg-purple-200\/95 {
  background-color: rgb(233 213 255 / 0.95);
}

.bg-purple-300 {
  --tw-bg-opacity: 1;
  background-color: rgb(216 180 254 / var(--tw-bg-opacity));
}

.bg-purple-300\/0 {
  background-color: rgb(216 180 254 / 0);
}

.bg-purple-300\/10 {
  background-color: rgb(216 180 254 / 0.1);
}

.bg-purple-300\/100 {
  background-color: rgb(216 180 254 / 1);
}

.bg-purple-300\/20 {
  background-color: rgb(216 180 254 / 0.2);
}

.bg-purple-300\/25 {
  background-color: rgb(216 180 254 / 0.25);
}

.bg-purple-300\/30 {
  background-color: rgb(216 180 254 / 0.3);
}

.bg-purple-300\/40 {
  background-color: rgb(216 180 254 / 0.4);
}

.bg-purple-300\/5 {
  background-color: rgb(216 180 254 / 0.05);
}

.bg-purple-300\/50 {
  background-color: rgb(216 180 254 / 0.5);
}

.bg-purple-300\/60 {
  background-color: rgb(216 180 254 / 0.6);
}

.bg-purple-300\/70 {
  background-color: rgb(216 180 254 / 0.7);
}

.bg-purple-300\/75 {
  background-color: rgb(216 180 254 / 0.75);
}

.bg-purple-300\/80 {
  background-color: rgb(216 180 254 / 0.8);
}

.bg-purple-300\/90 {
  background-color: rgb(216 180 254 / 0.9);
}

.bg-purple-300\/95 {
  background-color: rgb(216 180 254 / 0.95);
}

.bg-purple-400 {
  --tw-bg-opacity: 1;
  background-color: rgb(192 132 252 / var(--tw-bg-opacity));
}

.bg-purple-400\/0 {
  background-color: rgb(192 132 252 / 0);
}

.bg-purple-400\/10 {
  background-color: rgb(192 132 252 / 0.1);
}

.bg-purple-400\/100 {
  background-color: rgb(192 132 252 / 1);
}

.bg-purple-400\/20 {
  background-color: rgb(192 132 252 / 0.2);
}

.bg-purple-400\/25 {
  background-color: rgb(192 132 252 / 0.25);
}

.bg-purple-400\/30 {
  background-color: rgb(192 132 252 / 0.3);
}

.bg-purple-400\/40 {
  background-color: rgb(192 132 252 / 0.4);
}

.bg-purple-400\/5 {
  background-color: rgb(192 132 252 / 0.05);
}

.bg-purple-400\/50 {
  background-color: rgb(192 132 252 / 0.5);
}

.bg-purple-400\/60 {
  background-color: rgb(192 132 252 / 0.6);
}

.bg-purple-400\/70 {
  background-color: rgb(192 132 252 / 0.7);
}

.bg-purple-400\/75 {
  background-color: rgb(192 132 252 / 0.75);
}

.bg-purple-400\/80 {
  background-color: rgb(192 132 252 / 0.8);
}

.bg-purple-400\/90 {
  background-color: rgb(192 132 252 / 0.9);
}

.bg-purple-400\/95 {
  background-color: rgb(192 132 252 / 0.95);
}

.bg-purple-50 {
  --tw-bg-opacity: 1;
  background-color: rgb(250 245 255 / var(--tw-bg-opacity));
}

.bg-purple-50\/0 {
  background-color: rgb(250 245 255 / 0);
}

.bg-purple-50\/10 {
  background-color: rgb(250 245 255 / 0.1);
}

.bg-purple-50\/100 {
  background-color: rgb(250 245 255 / 1);
}

.bg-purple-50\/20 {
  background-color: rgb(250 245 255 / 0.2);
}

.bg-purple-50\/25 {
  background-color: rgb(250 245 255 / 0.25);
}

.bg-purple-50\/30 {
  background-color: rgb(250 245 255 / 0.3);
}

.bg-purple-50\/40 {
  background-color: rgb(250 245 255 / 0.4);
}

.bg-purple-50\/5 {
  background-color: rgb(250 245 255 / 0.05);
}

.bg-purple-50\/50 {
  background-color: rgb(250 245 255 / 0.5);
}

.bg-purple-50\/60 {
  background-color: rgb(250 245 255 / 0.6);
}

.bg-purple-50\/70 {
  background-color: rgb(250 245 255 / 0.7);
}

.bg-purple-50\/75 {
  background-color: rgb(250 245 255 / 0.75);
}

.bg-purple-50\/80 {
  background-color: rgb(250 245 255 / 0.8);
}

.bg-purple-50\/90 {
  background-color: rgb(250 245 255 / 0.9);
}

.bg-purple-50\/95 {
  background-color: rgb(250 245 255 / 0.95);
}

.bg-purple-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(168 85 247 / var(--tw-bg-opacity));
}

.bg-purple-500\/0 {
  background-color: rgb(168 85 247 / 0);
}

.bg-purple-500\/10 {
  background-color: rgb(168 85 247 / 0.1);
}

.bg-purple-500\/100 {
  background-color: rgb(168 85 247 / 1);
}

.bg-purple-500\/20 {
  background-color: rgb(168 85 247 / 0.2);
}

.bg-purple-500\/25 {
  background-color: rgb(168 85 247 / 0.25);
}

.bg-purple-500\/30 {
  background-color: rgb(168 85 247 / 0.3);
}

.bg-purple-500\/40 {
  background-color: rgb(168 85 247 / 0.4);
}

.bg-purple-500\/5 {
  background-color: rgb(168 85 247 / 0.05);
}

.bg-purple-500\/50 {
  background-color: rgb(168 85 247 / 0.5);
}

.bg-purple-500\/60 {
  background-color: rgb(168 85 247 / 0.6);
}

.bg-purple-500\/70 {
  background-color: rgb(168 85 247 / 0.7);
}

.bg-purple-500\/75 {
  background-color: rgb(168 85 247 / 0.75);
}

.bg-purple-500\/80 {
  background-color: rgb(168 85 247 / 0.8);
}

.bg-purple-500\/90 {
  background-color: rgb(168 85 247 / 0.9);
}

.bg-purple-500\/95 {
  background-color: rgb(168 85 247 / 0.95);
}

.bg-purple-600 {
  --tw-bg-opacity: 1;
  background-color: rgb(147 51 234 / var(--tw-bg-opacity));
}

.bg-purple-600\/0 {
  background-color: rgb(147 51 234 / 0);
}

.bg-purple-600\/10 {
  background-color: rgb(147 51 234 / 0.1);
}

.bg-purple-600\/100 {
  background-color: rgb(147 51 234 / 1);
}

.bg-purple-600\/20 {
  background-color: rgb(147 51 234 / 0.2);
}

.bg-purple-600\/25 {
  background-color: rgb(147 51 234 / 0.25);
}

.bg-purple-600\/30 {
  background-color: rgb(147 51 234 / 0.3);
}

.bg-purple-600\/40 {
  background-color: rgb(147 51 234 / 0.4);
}

.bg-purple-600\/5 {
  background-color: rgb(147 51 234 / 0.05);
}

.bg-purple-600\/50 {
  background-color: rgb(147 51 234 / 0.5);
}

.bg-purple-600\/60 {
  background-color: rgb(147 51 234 / 0.6);
}

.bg-purple-600\/70 {
  background-color: rgb(147 51 234 / 0.7);
}

.bg-purple-600\/75 {
  background-color: rgb(147 51 234 / 0.75);
}

.bg-purple-600\/80 {
  background-color: rgb(147 51 234 / 0.8);
}

.bg-purple-600\/90 {
  background-color: rgb(147 51 234 / 0.9);
}

.bg-purple-600\/95 {
  background-color: rgb(147 51 234 / 0.95);
}

.bg-purple-700 {
  --tw-bg-opacity: 1;
  background-color: rgb(126 34 206 / var(--tw-bg-opacity));
}

.bg-purple-700\/0 {
  background-color: rgb(126 34 206 / 0);
}

.bg-purple-700\/10 {
  background-color: rgb(126 34 206 / 0.1);
}

.bg-purple-700\/100 {
  background-color: rgb(126 34 206 / 1);
}

.bg-purple-700\/20 {
  background-color: rgb(126 34 206 / 0.2);
}

.bg-purple-700\/25 {
  background-color: rgb(126 34 206 / 0.25);
}

.bg-purple-700\/30 {
  background-color: rgb(126 34 206 / 0.3);
}

.bg-purple-700\/40 {
  background-color: rgb(126 34 206 / 0.4);
}

.bg-purple-700\/5 {
  background-color: rgb(126 34 206 / 0.05);
}

.bg-purple-700\/50 {
  background-color: rgb(126 34 206 / 0.5);
}

.bg-purple-700\/60 {
  background-color: rgb(126 34 206 / 0.6);
}

.bg-purple-700\/70 {
  background-color: rgb(126 34 206 / 0.7);
}

.bg-purple-700\/75 {
  background-color: rgb(126 34 206 / 0.75);
}

.bg-purple-700\/80 {
  background-color: rgb(126 34 206 / 0.8);
}

.bg-purple-700\/90 {
  background-color: rgb(126 34 206 / 0.9);
}

.bg-purple-700\/95 {
  background-color: rgb(126 34 206 / 0.95);
}

.bg-purple-800 {
  --tw-bg-opacity: 1;
  background-color: rgb(107 33 168 / var(--tw-bg-opacity));
}

.bg-purple-800\/0 {
  background-color: rgb(107 33 168 / 0);
}

.bg-purple-800\/10 {
  background-color: rgb(107 33 168 / 0.1);
}

.bg-purple-800\/100 {
  background-color: rgb(107 33 168 / 1);
}

.bg-purple-800\/20 {
  background-color: rgb(107 33 168 / 0.2);
}

.bg-purple-800\/25 {
  background-color: rgb(107 33 168 / 0.25);
}

.bg-purple-800\/30 {
  background-color: rgb(107 33 168 / 0.3);
}

.bg-purple-800\/40 {
  background-color: rgb(107 33 168 / 0.4);
}

.bg-purple-800\/5 {
  background-color: rgb(107 33 168 / 0.05);
}

.bg-purple-800\/50 {
  background-color: rgb(107 33 168 / 0.5);
}

.bg-purple-800\/60 {
  background-color: rgb(107 33 168 / 0.6);
}

.bg-purple-800\/70 {
  background-color: rgb(107 33 168 / 0.7);
}

.bg-purple-800\/75 {
  background-color: rgb(107 33 168 / 0.75);
}

.bg-purple-800\/80 {
  background-color: rgb(107 33 168 / 0.8);
}

.bg-purple-800\/90 {
  background-color: rgb(107 33 168 / 0.9);
}

.bg-purple-800\/95 {
  background-color: rgb(107 33 168 / 0.95);
}

.bg-purple-900 {
  --tw-bg-opacity: 1;
  background-color: rgb(88 28 135 / var(--tw-bg-opacity));
}

.bg-purple-900\/0 {
  background-color: rgb(88 28 135 / 0);
}

.bg-purple-900\/10 {
  background-color: rgb(88 28 135 / 0.1);
}

.bg-purple-900\/100 {
  background-color: rgb(88 28 135 / 1);
}

.bg-purple-900\/20 {
  background-color: rgb(88 28 135 / 0.2);
}

.bg-purple-900\/25 {
  background-color: rgb(88 28 135 / 0.25);
}

.bg-purple-900\/30 {
  background-color: rgb(88 28 135 / 0.3);
}

.bg-purple-900\/40 {
  background-color: rgb(88 28 135 / 0.4);
}

.bg-purple-900\/5 {
  background-color: rgb(88 28 135 / 0.05);
}

.bg-purple-900\/50 {
  background-color: rgb(88 28 135 / 0.5);
}

.bg-purple-900\/60 {
  background-color: rgb(88 28 135 / 0.6);
}

.bg-purple-900\/70 {
  background-color: rgb(88 28 135 / 0.7);
}

.bg-purple-900\/75 {
  background-color: rgb(88 28 135 / 0.75);
}

.bg-purple-900\/80 {
  background-color: rgb(88 28 135 / 0.8);
}

.bg-purple-900\/90 {
  background-color: rgb(88 28 135 / 0.9);
}

.bg-purple-900\/95 {
  background-color: rgb(88 28 135 / 0.95);
}

.bg-purple-950 {
  --tw-bg-opacity: 1;
  background-color: rgb(59 7 100 / var(--tw-bg-opacity));
}

.bg-purple-950\/0 {
  background-color: rgb(59 7 100 / 0);
}

.bg-purple-950\/10 {
  background-color: rgb(59 7 100 / 0.1);
}

.bg-purple-950\/100 {
  background-color: rgb(59 7 100 / 1);
}

.bg-purple-950\/20 {
  background-color: rgb(59 7 100 / 0.2);
}

.bg-purple-950\/25 {
  background-color: rgb(59 7 100 / 0.25);
}

.bg-purple-950\/30 {
  background-color: rgb(59 7 100 / 0.3);
}

.bg-purple-950\/40 {
  background-color: rgb(59 7 100 / 0.4);
}

.bg-purple-950\/5 {
  background-color: rgb(59 7 100 / 0.05);
}

.bg-purple-950\/50 {
  background-color: rgb(59 7 100 / 0.5);
}

.bg-purple-950\/60 {
  background-color: rgb(59 7 100 / 0.6);
}

.bg-purple-950\/70 {
  background-color: rgb(59 7 100 / 0.7);
}

.bg-purple-950\/75 {
  background-color: rgb(59 7 100 / 0.75);
}

.bg-purple-950\/80 {
  background-color: rgb(59 7 100 / 0.8);
}

.bg-purple-950\/90 {
  background-color: rgb(59 7 100 / 0.9);
}

.bg-purple-950\/95 {
  background-color: rgb(59 7 100 / 0.95);
}

.bg-r1 {
  --tw-bg-opacity: 1;
  background-color: rgb(208 0 0 / var(--tw-bg-opacity));
}

.bg-r1\/0 {
  background-color: rgb(208 0 0 / 0);
}

.bg-r1\/10 {
  background-color: rgb(208 0 0 / 0.1);
}

.bg-r1\/100 {
  background-color: rgb(208 0 0 / 1);
}

.bg-r1\/20 {
  background-color: rgb(208 0 0 / 0.2);
}

.bg-r1\/25 {
  background-color: rgb(208 0 0 / 0.25);
}

.bg-r1\/30 {
  background-color: rgb(208 0 0 / 0.3);
}

.bg-r1\/40 {
  background-color: rgb(208 0 0 / 0.4);
}

.bg-r1\/5 {
  background-color: rgb(208 0 0 / 0.05);
}

.bg-r1\/50 {
  background-color: rgb(208 0 0 / 0.5);
}

.bg-r1\/60 {
  background-color: rgb(208 0 0 / 0.6);
}

.bg-r1\/70 {
  background-color: rgb(208 0 0 / 0.7);
}

.bg-r1\/75 {
  background-color: rgb(208 0 0 / 0.75);
}

.bg-r1\/80 {
  background-color: rgb(208 0 0 / 0.8);
}

.bg-r1\/90 {
  background-color: rgb(208 0 0 / 0.9);
}

.bg-r1\/95 {
  background-color: rgb(208 0 0 / 0.95);
}

.bg-red-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(254 226 226 / var(--tw-bg-opacity));
}

.bg-red-100\/0 {
  background-color: rgb(254 226 226 / 0);
}

.bg-red-100\/10 {
  background-color: rgb(254 226 226 / 0.1);
}

.bg-red-100\/100 {
  background-color: rgb(254 226 226 / 1);
}

.bg-red-100\/20 {
  background-color: rgb(254 226 226 / 0.2);
}

.bg-red-100\/25 {
  background-color: rgb(254 226 226 / 0.25);
}

.bg-red-100\/30 {
  background-color: rgb(254 226 226 / 0.3);
}

.bg-red-100\/40 {
  background-color: rgb(254 226 226 / 0.4);
}

.bg-red-100\/5 {
  background-color: rgb(254 226 226 / 0.05);
}

.bg-red-100\/50 {
  background-color: rgb(254 226 226 / 0.5);
}

.bg-red-100\/60 {
  background-color: rgb(254 226 226 / 0.6);
}

.bg-red-100\/70 {
  background-color: rgb(254 226 226 / 0.7);
}

.bg-red-100\/75 {
  background-color: rgb(254 226 226 / 0.75);
}

.bg-red-100\/80 {
  background-color: rgb(254 226 226 / 0.8);
}

.bg-red-100\/90 {
  background-color: rgb(254 226 226 / 0.9);
}

.bg-red-100\/95 {
  background-color: rgb(254 226 226 / 0.95);
}

.bg-red-200 {
  --tw-bg-opacity: 1;
  background-color: rgb(254 202 202 / var(--tw-bg-opacity));
}

.bg-red-200\/0 {
  background-color: rgb(254 202 202 / 0);
}

.bg-red-200\/10 {
  background-color: rgb(254 202 202 / 0.1);
}

.bg-red-200\/100 {
  background-color: rgb(254 202 202 / 1);
}

.bg-red-200\/20 {
  background-color: rgb(254 202 202 / 0.2);
}

.bg-red-200\/25 {
  background-color: rgb(254 202 202 / 0.25);
}

.bg-red-200\/30 {
  background-color: rgb(254 202 202 / 0.3);
}

.bg-red-200\/40 {
  background-color: rgb(254 202 202 / 0.4);
}

.bg-red-200\/5 {
  background-color: rgb(254 202 202 / 0.05);
}

.bg-red-200\/50 {
  background-color: rgb(254 202 202 / 0.5);
}

.bg-red-200\/60 {
  background-color: rgb(254 202 202 / 0.6);
}

.bg-red-200\/70 {
  background-color: rgb(254 202 202 / 0.7);
}

.bg-red-200\/75 {
  background-color: rgb(254 202 202 / 0.75);
}

.bg-red-200\/80 {
  background-color: rgb(254 202 202 / 0.8);
}

.bg-red-200\/90 {
  background-color: rgb(254 202 202 / 0.9);
}

.bg-red-200\/95 {
  background-color: rgb(254 202 202 / 0.95);
}

.bg-red-300 {
  --tw-bg-opacity: 1;
  background-color: rgb(252 165 165 / var(--tw-bg-opacity));
}

.bg-red-300\/0 {
  background-color: rgb(252 165 165 / 0);
}

.bg-red-300\/10 {
  background-color: rgb(252 165 165 / 0.1);
}

.bg-red-300\/100 {
  background-color: rgb(252 165 165 / 1);
}

.bg-red-300\/20 {
  background-color: rgb(252 165 165 / 0.2);
}

.bg-red-300\/25 {
  background-color: rgb(252 165 165 / 0.25);
}

.bg-red-300\/30 {
  background-color: rgb(252 165 165 / 0.3);
}

.bg-red-300\/40 {
  background-color: rgb(252 165 165 / 0.4);
}

.bg-red-300\/5 {
  background-color: rgb(252 165 165 / 0.05);
}

.bg-red-300\/50 {
  background-color: rgb(252 165 165 / 0.5);
}

.bg-red-300\/60 {
  background-color: rgb(252 165 165 / 0.6);
}

.bg-red-300\/70 {
  background-color: rgb(252 165 165 / 0.7);
}

.bg-red-300\/75 {
  background-color: rgb(252 165 165 / 0.75);
}

.bg-red-300\/80 {
  background-color: rgb(252 165 165 / 0.8);
}

.bg-red-300\/90 {
  background-color: rgb(252 165 165 / 0.9);
}

.bg-red-300\/95 {
  background-color: rgb(252 165 165 / 0.95);
}

.bg-red-400 {
  --tw-bg-opacity: 1;
  background-color: rgb(248 113 113 / var(--tw-bg-opacity));
}

.bg-red-400\/0 {
  background-color: rgb(248 113 113 / 0);
}

.bg-red-400\/10 {
  background-color: rgb(248 113 113 / 0.1);
}

.bg-red-400\/100 {
  background-color: rgb(248 113 113 / 1);
}

.bg-red-400\/20 {
  background-color: rgb(248 113 113 / 0.2);
}

.bg-red-400\/25 {
  background-color: rgb(248 113 113 / 0.25);
}

.bg-red-400\/30 {
  background-color: rgb(248 113 113 / 0.3);
}

.bg-red-400\/40 {
  background-color: rgb(248 113 113 / 0.4);
}

.bg-red-400\/5 {
  background-color: rgb(248 113 113 / 0.05);
}

.bg-red-400\/50 {
  background-color: rgb(248 113 113 / 0.5);
}

.bg-red-400\/60 {
  background-color: rgb(248 113 113 / 0.6);
}

.bg-red-400\/70 {
  background-color: rgb(248 113 113 / 0.7);
}

.bg-red-400\/75 {
  background-color: rgb(248 113 113 / 0.75);
}

.bg-red-400\/80 {
  background-color: rgb(248 113 113 / 0.8);
}

.bg-red-400\/90 {
  background-color: rgb(248 113 113 / 0.9);
}

.bg-red-400\/95 {
  background-color: rgb(248 113 113 / 0.95);
}

.bg-red-50 {
  --tw-bg-opacity: 1;
  background-color: rgb(254 242 242 / var(--tw-bg-opacity));
}

.bg-red-50\/0 {
  background-color: rgb(254 242 242 / 0);
}

.bg-red-50\/10 {
  background-color: rgb(254 242 242 / 0.1);
}

.bg-red-50\/100 {
  background-color: rgb(254 242 242 / 1);
}

.bg-red-50\/20 {
  background-color: rgb(254 242 242 / 0.2);
}

.bg-red-50\/25 {
  background-color: rgb(254 242 242 / 0.25);
}

.bg-red-50\/30 {
  background-color: rgb(254 242 242 / 0.3);
}

.bg-red-50\/40 {
  background-color: rgb(254 242 242 / 0.4);
}

.bg-red-50\/5 {
  background-color: rgb(254 242 242 / 0.05);
}

.bg-red-50\/50 {
  background-color: rgb(254 242 242 / 0.5);
}

.bg-red-50\/60 {
  background-color: rgb(254 242 242 / 0.6);
}

.bg-red-50\/70 {
  background-color: rgb(254 242 242 / 0.7);
}

.bg-red-50\/75 {
  background-color: rgb(254 242 242 / 0.75);
}

.bg-red-50\/80 {
  background-color: rgb(254 242 242 / 0.8);
}

.bg-red-50\/90 {
  background-color: rgb(254 242 242 / 0.9);
}

.bg-red-50\/95 {
  background-color: rgb(254 242 242 / 0.95);
}

.bg-red-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(239 68 68 / var(--tw-bg-opacity));
}

.bg-red-500\/0 {
  background-color: rgb(239 68 68 / 0);
}

.bg-red-500\/10 {
  background-color: rgb(239 68 68 / 0.1);
}

.bg-red-500\/100 {
  background-color: rgb(239 68 68 / 1);
}

.bg-red-500\/20 {
  background-color: rgb(239 68 68 / 0.2);
}

.bg-red-500\/25 {
  background-color: rgb(239 68 68 / 0.25);
}

.bg-red-500\/30 {
  background-color: rgb(239 68 68 / 0.3);
}

.bg-red-500\/40 {
  background-color: rgb(239 68 68 / 0.4);
}

.bg-red-500\/5 {
  background-color: rgb(239 68 68 / 0.05);
}

.bg-red-500\/50 {
  background-color: rgb(239 68 68 / 0.5);
}

.bg-red-500\/60 {
  background-color: rgb(239 68 68 / 0.6);
}

.bg-red-500\/70 {
  background-color: rgb(239 68 68 / 0.7);
}

.bg-red-500\/75 {
  background-color: rgb(239 68 68 / 0.75);
}

.bg-red-500\/80 {
  background-color: rgb(239 68 68 / 0.8);
}

.bg-red-500\/90 {
  background-color: rgb(239 68 68 / 0.9);
}

.bg-red-500\/95 {
  background-color: rgb(239 68 68 / 0.95);
}

.bg-red-600 {
  --tw-bg-opacity: 1;
  background-color: rgb(220 38 38 / var(--tw-bg-opacity));
}

.bg-red-600\/0 {
  background-color: rgb(220 38 38 / 0);
}

.bg-red-600\/10 {
  background-color: rgb(220 38 38 / 0.1);
}

.bg-red-600\/100 {
  background-color: rgb(220 38 38 / 1);
}

.bg-red-600\/20 {
  background-color: rgb(220 38 38 / 0.2);
}

.bg-red-600\/25 {
  background-color: rgb(220 38 38 / 0.25);
}

.bg-red-600\/30 {
  background-color: rgb(220 38 38 / 0.3);
}

.bg-red-600\/40 {
  background-color: rgb(220 38 38 / 0.4);
}

.bg-red-600\/5 {
  background-color: rgb(220 38 38 / 0.05);
}

.bg-red-600\/50 {
  background-color: rgb(220 38 38 / 0.5);
}

.bg-red-600\/60 {
  background-color: rgb(220 38 38 / 0.6);
}

.bg-red-600\/70 {
  background-color: rgb(220 38 38 / 0.7);
}

.bg-red-600\/75 {
  background-color: rgb(220 38 38 / 0.75);
}

.bg-red-600\/80 {
  background-color: rgb(220 38 38 / 0.8);
}

.bg-red-600\/90 {
  background-color: rgb(220 38 38 / 0.9);
}

.bg-red-600\/95 {
  background-color: rgb(220 38 38 / 0.95);
}

.bg-red-700 {
  --tw-bg-opacity: 1;
  background-color: rgb(185 28 28 / var(--tw-bg-opacity));
}

.bg-red-700\/0 {
  background-color: rgb(185 28 28 / 0);
}

.bg-red-700\/10 {
  background-color: rgb(185 28 28 / 0.1);
}

.bg-red-700\/100 {
  background-color: rgb(185 28 28 / 1);
}

.bg-red-700\/20 {
  background-color: rgb(185 28 28 / 0.2);
}

.bg-red-700\/25 {
  background-color: rgb(185 28 28 / 0.25);
}

.bg-red-700\/30 {
  background-color: rgb(185 28 28 / 0.3);
}

.bg-red-700\/40 {
  background-color: rgb(185 28 28 / 0.4);
}

.bg-red-700\/5 {
  background-color: rgb(185 28 28 / 0.05);
}

.bg-red-700\/50 {
  background-color: rgb(185 28 28 / 0.5);
}

.bg-red-700\/60 {
  background-color: rgb(185 28 28 / 0.6);
}

.bg-red-700\/70 {
  background-color: rgb(185 28 28 / 0.7);
}

.bg-red-700\/75 {
  background-color: rgb(185 28 28 / 0.75);
}

.bg-red-700\/80 {
  background-color: rgb(185 28 28 / 0.8);
}

.bg-red-700\/90 {
  background-color: rgb(185 28 28 / 0.9);
}

.bg-red-700\/95 {
  background-color: rgb(185 28 28 / 0.95);
}

.bg-red-800 {
  --tw-bg-opacity: 1;
  background-color: rgb(153 27 27 / var(--tw-bg-opacity));
}

.bg-red-800\/0 {
  background-color: rgb(153 27 27 / 0);
}

.bg-red-800\/10 {
  background-color: rgb(153 27 27 / 0.1);
}

.bg-red-800\/100 {
  background-color: rgb(153 27 27 / 1);
}

.bg-red-800\/20 {
  background-color: rgb(153 27 27 / 0.2);
}

.bg-red-800\/25 {
  background-color: rgb(153 27 27 / 0.25);
}

.bg-red-800\/30 {
  background-color: rgb(153 27 27 / 0.3);
}

.bg-red-800\/40 {
  background-color: rgb(153 27 27 / 0.4);
}

.bg-red-800\/5 {
  background-color: rgb(153 27 27 / 0.05);
}

.bg-red-800\/50 {
  background-color: rgb(153 27 27 / 0.5);
}

.bg-red-800\/60 {
  background-color: rgb(153 27 27 / 0.6);
}

.bg-red-800\/70 {
  background-color: rgb(153 27 27 / 0.7);
}

.bg-red-800\/75 {
  background-color: rgb(153 27 27 / 0.75);
}

.bg-red-800\/80 {
  background-color: rgb(153 27 27 / 0.8);
}

.bg-red-800\/90 {
  background-color: rgb(153 27 27 / 0.9);
}

.bg-red-800\/95 {
  background-color: rgb(153 27 27 / 0.95);
}

.bg-red-900 {
  --tw-bg-opacity: 1;
  background-color: rgb(127 29 29 / var(--tw-bg-opacity));
}

.bg-red-900\/0 {
  background-color: rgb(127 29 29 / 0);
}

.bg-red-900\/10 {
  background-color: rgb(127 29 29 / 0.1);
}

.bg-red-900\/100 {
  background-color: rgb(127 29 29 / 1);
}

.bg-red-900\/20 {
  background-color: rgb(127 29 29 / 0.2);
}

.bg-red-900\/25 {
  background-color: rgb(127 29 29 / 0.25);
}

.bg-red-900\/30 {
  background-color: rgb(127 29 29 / 0.3);
}

.bg-red-900\/40 {
  background-color: rgb(127 29 29 / 0.4);
}

.bg-red-900\/5 {
  background-color: rgb(127 29 29 / 0.05);
}

.bg-red-900\/50 {
  background-color: rgb(127 29 29 / 0.5);
}

.bg-red-900\/60 {
  background-color: rgb(127 29 29 / 0.6);
}

.bg-red-900\/70 {
  background-color: rgb(127 29 29 / 0.7);
}

.bg-red-900\/75 {
  background-color: rgb(127 29 29 / 0.75);
}

.bg-red-900\/80 {
  background-color: rgb(127 29 29 / 0.8);
}

.bg-red-900\/90 {
  background-color: rgb(127 29 29 / 0.9);
}

.bg-red-900\/95 {
  background-color: rgb(127 29 29 / 0.95);
}

.bg-red-950 {
  --tw-bg-opacity: 1;
  background-color: rgb(69 10 10 / var(--tw-bg-opacity));
}

.bg-red-950\/0 {
  background-color: rgb(69 10 10 / 0);
}

.bg-red-950\/10 {
  background-color: rgb(69 10 10 / 0.1);
}

.bg-red-950\/100 {
  background-color: rgb(69 10 10 / 1);
}

.bg-red-950\/20 {
  background-color: rgb(69 10 10 / 0.2);
}

.bg-red-950\/25 {
  background-color: rgb(69 10 10 / 0.25);
}

.bg-red-950\/30 {
  background-color: rgb(69 10 10 / 0.3);
}

.bg-red-950\/40 {
  background-color: rgb(69 10 10 / 0.4);
}

.bg-red-950\/5 {
  background-color: rgb(69 10 10 / 0.05);
}

.bg-red-950\/50 {
  background-color: rgb(69 10 10 / 0.5);
}

.bg-red-950\/60 {
  background-color: rgb(69 10 10 / 0.6);
}

.bg-red-950\/70 {
  background-color: rgb(69 10 10 / 0.7);
}

.bg-red-950\/75 {
  background-color: rgb(69 10 10 / 0.75);
}

.bg-red-950\/80 {
  background-color: rgb(69 10 10 / 0.8);
}

.bg-red-950\/90 {
  background-color: rgb(69 10 10 / 0.9);
}

.bg-red-950\/95 {
  background-color: rgb(69 10 10 / 0.95);
}

.bg-rose-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(255 228 230 / var(--tw-bg-opacity));
}

.bg-rose-100\/0 {
  background-color: rgb(255 228 230 / 0);
}

.bg-rose-100\/10 {
  background-color: rgb(255 228 230 / 0.1);
}

.bg-rose-100\/100 {
  background-color: rgb(255 228 230 / 1);
}

.bg-rose-100\/20 {
  background-color: rgb(255 228 230 / 0.2);
}

.bg-rose-100\/25 {
  background-color: rgb(255 228 230 / 0.25);
}

.bg-rose-100\/30 {
  background-color: rgb(255 228 230 / 0.3);
}

.bg-rose-100\/40 {
  background-color: rgb(255 228 230 / 0.4);
}

.bg-rose-100\/5 {
  background-color: rgb(255 228 230 / 0.05);
}

.bg-rose-100\/50 {
  background-color: rgb(255 228 230 / 0.5);
}

.bg-rose-100\/60 {
  background-color: rgb(255 228 230 / 0.6);
}

.bg-rose-100\/70 {
  background-color: rgb(255 228 230 / 0.7);
}

.bg-rose-100\/75 {
  background-color: rgb(255 228 230 / 0.75);
}

.bg-rose-100\/80 {
  background-color: rgb(255 228 230 / 0.8);
}

.bg-rose-100\/90 {
  background-color: rgb(255 228 230 / 0.9);
}

.bg-rose-100\/95 {
  background-color: rgb(255 228 230 / 0.95);
}

.bg-rose-200 {
  --tw-bg-opacity: 1;
  background-color: rgb(254 205 211 / var(--tw-bg-opacity));
}

.bg-rose-200\/0 {
  background-color: rgb(254 205 211 / 0);
}

.bg-rose-200\/10 {
  background-color: rgb(254 205 211 / 0.1);
}

.bg-rose-200\/100 {
  background-color: rgb(254 205 211 / 1);
}

.bg-rose-200\/20 {
  background-color: rgb(254 205 211 / 0.2);
}

.bg-rose-200\/25 {
  background-color: rgb(254 205 211 / 0.25);
}

.bg-rose-200\/30 {
  background-color: rgb(254 205 211 / 0.3);
}

.bg-rose-200\/40 {
  background-color: rgb(254 205 211 / 0.4);
}

.bg-rose-200\/5 {
  background-color: rgb(254 205 211 / 0.05);
}

.bg-rose-200\/50 {
  background-color: rgb(254 205 211 / 0.5);
}

.bg-rose-200\/60 {
  background-color: rgb(254 205 211 / 0.6);
}

.bg-rose-200\/70 {
  background-color: rgb(254 205 211 / 0.7);
}

.bg-rose-200\/75 {
  background-color: rgb(254 205 211 / 0.75);
}

.bg-rose-200\/80 {
  background-color: rgb(254 205 211 / 0.8);
}

.bg-rose-200\/90 {
  background-color: rgb(254 205 211 / 0.9);
}

.bg-rose-200\/95 {
  background-color: rgb(254 205 211 / 0.95);
}

.bg-rose-300 {
  --tw-bg-opacity: 1;
  background-color: rgb(253 164 175 / var(--tw-bg-opacity));
}

.bg-rose-300\/0 {
  background-color: rgb(253 164 175 / 0);
}

.bg-rose-300\/10 {
  background-color: rgb(253 164 175 / 0.1);
}

.bg-rose-300\/100 {
  background-color: rgb(253 164 175 / 1);
}

.bg-rose-300\/20 {
  background-color: rgb(253 164 175 / 0.2);
}

.bg-rose-300\/25 {
  background-color: rgb(253 164 175 / 0.25);
}

.bg-rose-300\/30 {
  background-color: rgb(253 164 175 / 0.3);
}

.bg-rose-300\/40 {
  background-color: rgb(253 164 175 / 0.4);
}

.bg-rose-300\/5 {
  background-color: rgb(253 164 175 / 0.05);
}

.bg-rose-300\/50 {
  background-color: rgb(253 164 175 / 0.5);
}

.bg-rose-300\/60 {
  background-color: rgb(253 164 175 / 0.6);
}

.bg-rose-300\/70 {
  background-color: rgb(253 164 175 / 0.7);
}

.bg-rose-300\/75 {
  background-color: rgb(253 164 175 / 0.75);
}

.bg-rose-300\/80 {
  background-color: rgb(253 164 175 / 0.8);
}

.bg-rose-300\/90 {
  background-color: rgb(253 164 175 / 0.9);
}

.bg-rose-300\/95 {
  background-color: rgb(253 164 175 / 0.95);
}

.bg-rose-400 {
  --tw-bg-opacity: 1;
  background-color: rgb(251 113 133 / var(--tw-bg-opacity));
}

.bg-rose-400\/0 {
  background-color: rgb(251 113 133 / 0);
}

.bg-rose-400\/10 {
  background-color: rgb(251 113 133 / 0.1);
}

.bg-rose-400\/100 {
  background-color: rgb(251 113 133 / 1);
}

.bg-rose-400\/20 {
  background-color: rgb(251 113 133 / 0.2);
}

.bg-rose-400\/25 {
  background-color: rgb(251 113 133 / 0.25);
}

.bg-rose-400\/30 {
  background-color: rgb(251 113 133 / 0.3);
}

.bg-rose-400\/40 {
  background-color: rgb(251 113 133 / 0.4);
}

.bg-rose-400\/5 {
  background-color: rgb(251 113 133 / 0.05);
}

.bg-rose-400\/50 {
  background-color: rgb(251 113 133 / 0.5);
}

.bg-rose-400\/60 {
  background-color: rgb(251 113 133 / 0.6);
}

.bg-rose-400\/70 {
  background-color: rgb(251 113 133 / 0.7);
}

.bg-rose-400\/75 {
  background-color: rgb(251 113 133 / 0.75);
}

.bg-rose-400\/80 {
  background-color: rgb(251 113 133 / 0.8);
}

.bg-rose-400\/90 {
  background-color: rgb(251 113 133 / 0.9);
}

.bg-rose-400\/95 {
  background-color: rgb(251 113 133 / 0.95);
}

.bg-rose-50 {
  --tw-bg-opacity: 1;
  background-color: rgb(255 241 242 / var(--tw-bg-opacity));
}

.bg-rose-50\/0 {
  background-color: rgb(255 241 242 / 0);
}

.bg-rose-50\/10 {
  background-color: rgb(255 241 242 / 0.1);
}

.bg-rose-50\/100 {
  background-color: rgb(255 241 242 / 1);
}

.bg-rose-50\/20 {
  background-color: rgb(255 241 242 / 0.2);
}

.bg-rose-50\/25 {
  background-color: rgb(255 241 242 / 0.25);
}

.bg-rose-50\/30 {
  background-color: rgb(255 241 242 / 0.3);
}

.bg-rose-50\/40 {
  background-color: rgb(255 241 242 / 0.4);
}

.bg-rose-50\/5 {
  background-color: rgb(255 241 242 / 0.05);
}

.bg-rose-50\/50 {
  background-color: rgb(255 241 242 / 0.5);
}

.bg-rose-50\/60 {
  background-color: rgb(255 241 242 / 0.6);
}

.bg-rose-50\/70 {
  background-color: rgb(255 241 242 / 0.7);
}

.bg-rose-50\/75 {
  background-color: rgb(255 241 242 / 0.75);
}

.bg-rose-50\/80 {
  background-color: rgb(255 241 242 / 0.8);
}

.bg-rose-50\/90 {
  background-color: rgb(255 241 242 / 0.9);
}

.bg-rose-50\/95 {
  background-color: rgb(255 241 242 / 0.95);
}

.bg-rose-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(244 63 94 / var(--tw-bg-opacity));
}

.bg-rose-500\/0 {
  background-color: rgb(244 63 94 / 0);
}

.bg-rose-500\/10 {
  background-color: rgb(244 63 94 / 0.1);
}

.bg-rose-500\/100 {
  background-color: rgb(244 63 94 / 1);
}

.bg-rose-500\/20 {
  background-color: rgb(244 63 94 / 0.2);
}

.bg-rose-500\/25 {
  background-color: rgb(244 63 94 / 0.25);
}

.bg-rose-500\/30 {
  background-color: rgb(244 63 94 / 0.3);
}

.bg-rose-500\/40 {
  background-color: rgb(244 63 94 / 0.4);
}

.bg-rose-500\/5 {
  background-color: rgb(244 63 94 / 0.05);
}

.bg-rose-500\/50 {
  background-color: rgb(244 63 94 / 0.5);
}

.bg-rose-500\/60 {
  background-color: rgb(244 63 94 / 0.6);
}

.bg-rose-500\/70 {
  background-color: rgb(244 63 94 / 0.7);
}

.bg-rose-500\/75 {
  background-color: rgb(244 63 94 / 0.75);
}

.bg-rose-500\/80 {
  background-color: rgb(244 63 94 / 0.8);
}

.bg-rose-500\/90 {
  background-color: rgb(244 63 94 / 0.9);
}

.bg-rose-500\/95 {
  background-color: rgb(244 63 94 / 0.95);
}

.bg-rose-600 {
  --tw-bg-opacity: 1;
  background-color: rgb(225 29 72 / var(--tw-bg-opacity));
}

.bg-rose-600\/0 {
  background-color: rgb(225 29 72 / 0);
}

.bg-rose-600\/10 {
  background-color: rgb(225 29 72 / 0.1);
}

.bg-rose-600\/100 {
  background-color: rgb(225 29 72 / 1);
}

.bg-rose-600\/20 {
  background-color: rgb(225 29 72 / 0.2);
}

.bg-rose-600\/25 {
  background-color: rgb(225 29 72 / 0.25);
}

.bg-rose-600\/30 {
  background-color: rgb(225 29 72 / 0.3);
}

.bg-rose-600\/40 {
  background-color: rgb(225 29 72 / 0.4);
}

.bg-rose-600\/5 {
  background-color: rgb(225 29 72 / 0.05);
}

.bg-rose-600\/50 {
  background-color: rgb(225 29 72 / 0.5);
}

.bg-rose-600\/60 {
  background-color: rgb(225 29 72 / 0.6);
}

.bg-rose-600\/70 {
  background-color: rgb(225 29 72 / 0.7);
}

.bg-rose-600\/75 {
  background-color: rgb(225 29 72 / 0.75);
}

.bg-rose-600\/80 {
  background-color: rgb(225 29 72 / 0.8);
}

.bg-rose-600\/90 {
  background-color: rgb(225 29 72 / 0.9);
}

.bg-rose-600\/95 {
  background-color: rgb(225 29 72 / 0.95);
}

.bg-rose-700 {
  --tw-bg-opacity: 1;
  background-color: rgb(190 18 60 / var(--tw-bg-opacity));
}

.bg-rose-700\/0 {
  background-color: rgb(190 18 60 / 0);
}

.bg-rose-700\/10 {
  background-color: rgb(190 18 60 / 0.1);
}

.bg-rose-700\/100 {
  background-color: rgb(190 18 60 / 1);
}

.bg-rose-700\/20 {
  background-color: rgb(190 18 60 / 0.2);
}

.bg-rose-700\/25 {
  background-color: rgb(190 18 60 / 0.25);
}

.bg-rose-700\/30 {
  background-color: rgb(190 18 60 / 0.3);
}

.bg-rose-700\/40 {
  background-color: rgb(190 18 60 / 0.4);
}

.bg-rose-700\/5 {
  background-color: rgb(190 18 60 / 0.05);
}

.bg-rose-700\/50 {
  background-color: rgb(190 18 60 / 0.5);
}

.bg-rose-700\/60 {
  background-color: rgb(190 18 60 / 0.6);
}

.bg-rose-700\/70 {
  background-color: rgb(190 18 60 / 0.7);
}

.bg-rose-700\/75 {
  background-color: rgb(190 18 60 / 0.75);
}

.bg-rose-700\/80 {
  background-color: rgb(190 18 60 / 0.8);
}

.bg-rose-700\/90 {
  background-color: rgb(190 18 60 / 0.9);
}

.bg-rose-700\/95 {
  background-color: rgb(190 18 60 / 0.95);
}

.bg-rose-800 {
  --tw-bg-opacity: 1;
  background-color: rgb(159 18 57 / var(--tw-bg-opacity));
}

.bg-rose-800\/0 {
  background-color: rgb(159 18 57 / 0);
}

.bg-rose-800\/10 {
  background-color: rgb(159 18 57 / 0.1);
}

.bg-rose-800\/100 {
  background-color: rgb(159 18 57 / 1);
}

.bg-rose-800\/20 {
  background-color: rgb(159 18 57 / 0.2);
}

.bg-rose-800\/25 {
  background-color: rgb(159 18 57 / 0.25);
}

.bg-rose-800\/30 {
  background-color: rgb(159 18 57 / 0.3);
}

.bg-rose-800\/40 {
  background-color: rgb(159 18 57 / 0.4);
}

.bg-rose-800\/5 {
  background-color: rgb(159 18 57 / 0.05);
}

.bg-rose-800\/50 {
  background-color: rgb(159 18 57 / 0.5);
}

.bg-rose-800\/60 {
  background-color: rgb(159 18 57 / 0.6);
}

.bg-rose-800\/70 {
  background-color: rgb(159 18 57 / 0.7);
}

.bg-rose-800\/75 {
  background-color: rgb(159 18 57 / 0.75);
}

.bg-rose-800\/80 {
  background-color: rgb(159 18 57 / 0.8);
}

.bg-rose-800\/90 {
  background-color: rgb(159 18 57 / 0.9);
}

.bg-rose-800\/95 {
  background-color: rgb(159 18 57 / 0.95);
}

.bg-rose-900 {
  --tw-bg-opacity: 1;
  background-color: rgb(136 19 55 / var(--tw-bg-opacity));
}

.bg-rose-900\/0 {
  background-color: rgb(136 19 55 / 0);
}

.bg-rose-900\/10 {
  background-color: rgb(136 19 55 / 0.1);
}

.bg-rose-900\/100 {
  background-color: rgb(136 19 55 / 1);
}

.bg-rose-900\/20 {
  background-color: rgb(136 19 55 / 0.2);
}

.bg-rose-900\/25 {
  background-color: rgb(136 19 55 / 0.25);
}

.bg-rose-900\/30 {
  background-color: rgb(136 19 55 / 0.3);
}

.bg-rose-900\/40 {
  background-color: rgb(136 19 55 / 0.4);
}

.bg-rose-900\/5 {
  background-color: rgb(136 19 55 / 0.05);
}

.bg-rose-900\/50 {
  background-color: rgb(136 19 55 / 0.5);
}

.bg-rose-900\/60 {
  background-color: rgb(136 19 55 / 0.6);
}

.bg-rose-900\/70 {
  background-color: rgb(136 19 55 / 0.7);
}

.bg-rose-900\/75 {
  background-color: rgb(136 19 55 / 0.75);
}

.bg-rose-900\/80 {
  background-color: rgb(136 19 55 / 0.8);
}

.bg-rose-900\/90 {
  background-color: rgb(136 19 55 / 0.9);
}

.bg-rose-900\/95 {
  background-color: rgb(136 19 55 / 0.95);
}

.bg-rose-950 {
  --tw-bg-opacity: 1;
  background-color: rgb(76 5 25 / var(--tw-bg-opacity));
}

.bg-rose-950\/0 {
  background-color: rgb(76 5 25 / 0);
}

.bg-rose-950\/10 {
  background-color: rgb(76 5 25 / 0.1);
}

.bg-rose-950\/100 {
  background-color: rgb(76 5 25 / 1);
}

.bg-rose-950\/20 {
  background-color: rgb(76 5 25 / 0.2);
}

.bg-rose-950\/25 {
  background-color: rgb(76 5 25 / 0.25);
}

.bg-rose-950\/30 {
  background-color: rgb(76 5 25 / 0.3);
}

.bg-rose-950\/40 {
  background-color: rgb(76 5 25 / 0.4);
}

.bg-rose-950\/5 {
  background-color: rgb(76 5 25 / 0.05);
}

.bg-rose-950\/50 {
  background-color: rgb(76 5 25 / 0.5);
}

.bg-rose-950\/60 {
  background-color: rgb(76 5 25 / 0.6);
}

.bg-rose-950\/70 {
  background-color: rgb(76 5 25 / 0.7);
}

.bg-rose-950\/75 {
  background-color: rgb(76 5 25 / 0.75);
}

.bg-rose-950\/80 {
  background-color: rgb(76 5 25 / 0.8);
}

.bg-rose-950\/90 {
  background-color: rgb(76 5 25 / 0.9);
}

.bg-rose-950\/95 {
  background-color: rgb(76 5 25 / 0.95);
}

.bg-sky-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(224 242 254 / var(--tw-bg-opacity));
}

.bg-sky-100\/0 {
  background-color: rgb(224 242 254 / 0);
}

.bg-sky-100\/10 {
  background-color: rgb(224 242 254 / 0.1);
}

.bg-sky-100\/100 {
  background-color: rgb(224 242 254 / 1);
}

.bg-sky-100\/20 {
  background-color: rgb(224 242 254 / 0.2);
}

.bg-sky-100\/25 {
  background-color: rgb(224 242 254 / 0.25);
}

.bg-sky-100\/30 {
  background-color: rgb(224 242 254 / 0.3);
}

.bg-sky-100\/40 {
  background-color: rgb(224 242 254 / 0.4);
}

.bg-sky-100\/5 {
  background-color: rgb(224 242 254 / 0.05);
}

.bg-sky-100\/50 {
  background-color: rgb(224 242 254 / 0.5);
}

.bg-sky-100\/60 {
  background-color: rgb(224 242 254 / 0.6);
}

.bg-sky-100\/70 {
  background-color: rgb(224 242 254 / 0.7);
}

.bg-sky-100\/75 {
  background-color: rgb(224 242 254 / 0.75);
}

.bg-sky-100\/80 {
  background-color: rgb(224 242 254 / 0.8);
}

.bg-sky-100\/90 {
  background-color: rgb(224 242 254 / 0.9);
}

.bg-sky-100\/95 {
  background-color: rgb(224 242 254 / 0.95);
}

.bg-sky-200 {
  --tw-bg-opacity: 1;
  background-color: rgb(186 230 253 / var(--tw-bg-opacity));
}

.bg-sky-200\/0 {
  background-color: rgb(186 230 253 / 0);
}

.bg-sky-200\/10 {
  background-color: rgb(186 230 253 / 0.1);
}

.bg-sky-200\/100 {
  background-color: rgb(186 230 253 / 1);
}

.bg-sky-200\/20 {
  background-color: rgb(186 230 253 / 0.2);
}

.bg-sky-200\/25 {
  background-color: rgb(186 230 253 / 0.25);
}

.bg-sky-200\/30 {
  background-color: rgb(186 230 253 / 0.3);
}

.bg-sky-200\/40 {
  background-color: rgb(186 230 253 / 0.4);
}

.bg-sky-200\/5 {
  background-color: rgb(186 230 253 / 0.05);
}

.bg-sky-200\/50 {
  background-color: rgb(186 230 253 / 0.5);
}

.bg-sky-200\/60 {
  background-color: rgb(186 230 253 / 0.6);
}

.bg-sky-200\/70 {
  background-color: rgb(186 230 253 / 0.7);
}

.bg-sky-200\/75 {
  background-color: rgb(186 230 253 / 0.75);
}

.bg-sky-200\/80 {
  background-color: rgb(186 230 253 / 0.8);
}

.bg-sky-200\/90 {
  background-color: rgb(186 230 253 / 0.9);
}

.bg-sky-200\/95 {
  background-color: rgb(186 230 253 / 0.95);
}

.bg-sky-300 {
  --tw-bg-opacity: 1;
  background-color: rgb(125 211 252 / var(--tw-bg-opacity));
}

.bg-sky-300\/0 {
  background-color: rgb(125 211 252 / 0);
}

.bg-sky-300\/10 {
  background-color: rgb(125 211 252 / 0.1);
}

.bg-sky-300\/100 {
  background-color: rgb(125 211 252 / 1);
}

.bg-sky-300\/20 {
  background-color: rgb(125 211 252 / 0.2);
}

.bg-sky-300\/25 {
  background-color: rgb(125 211 252 / 0.25);
}

.bg-sky-300\/30 {
  background-color: rgb(125 211 252 / 0.3);
}

.bg-sky-300\/40 {
  background-color: rgb(125 211 252 / 0.4);
}

.bg-sky-300\/5 {
  background-color: rgb(125 211 252 / 0.05);
}

.bg-sky-300\/50 {
  background-color: rgb(125 211 252 / 0.5);
}

.bg-sky-300\/60 {
  background-color: rgb(125 211 252 / 0.6);
}

.bg-sky-300\/70 {
  background-color: rgb(125 211 252 / 0.7);
}

.bg-sky-300\/75 {
  background-color: rgb(125 211 252 / 0.75);
}

.bg-sky-300\/80 {
  background-color: rgb(125 211 252 / 0.8);
}

.bg-sky-300\/90 {
  background-color: rgb(125 211 252 / 0.9);
}

.bg-sky-300\/95 {
  background-color: rgb(125 211 252 / 0.95);
}

.bg-sky-400 {
  --tw-bg-opacity: 1;
  background-color: rgb(56 189 248 / var(--tw-bg-opacity));
}

.bg-sky-400\/0 {
  background-color: rgb(56 189 248 / 0);
}

.bg-sky-400\/10 {
  background-color: rgb(56 189 248 / 0.1);
}

.bg-sky-400\/100 {
  background-color: rgb(56 189 248 / 1);
}

.bg-sky-400\/20 {
  background-color: rgb(56 189 248 / 0.2);
}

.bg-sky-400\/25 {
  background-color: rgb(56 189 248 / 0.25);
}

.bg-sky-400\/30 {
  background-color: rgb(56 189 248 / 0.3);
}

.bg-sky-400\/40 {
  background-color: rgb(56 189 248 / 0.4);
}

.bg-sky-400\/5 {
  background-color: rgb(56 189 248 / 0.05);
}

.bg-sky-400\/50 {
  background-color: rgb(56 189 248 / 0.5);
}

.bg-sky-400\/60 {
  background-color: rgb(56 189 248 / 0.6);
}

.bg-sky-400\/70 {
  background-color: rgb(56 189 248 / 0.7);
}

.bg-sky-400\/75 {
  background-color: rgb(56 189 248 / 0.75);
}

.bg-sky-400\/80 {
  background-color: rgb(56 189 248 / 0.8);
}

.bg-sky-400\/90 {
  background-color: rgb(56 189 248 / 0.9);
}

.bg-sky-400\/95 {
  background-color: rgb(56 189 248 / 0.95);
}

.bg-sky-50 {
  --tw-bg-opacity: 1;
  background-color: rgb(240 249 255 / var(--tw-bg-opacity));
}

.bg-sky-50\/0 {
  background-color: rgb(240 249 255 / 0);
}

.bg-sky-50\/10 {
  background-color: rgb(240 249 255 / 0.1);
}

.bg-sky-50\/100 {
  background-color: rgb(240 249 255 / 1);
}

.bg-sky-50\/20 {
  background-color: rgb(240 249 255 / 0.2);
}

.bg-sky-50\/25 {
  background-color: rgb(240 249 255 / 0.25);
}

.bg-sky-50\/30 {
  background-color: rgb(240 249 255 / 0.3);
}

.bg-sky-50\/40 {
  background-color: rgb(240 249 255 / 0.4);
}

.bg-sky-50\/5 {
  background-color: rgb(240 249 255 / 0.05);
}

.bg-sky-50\/50 {
  background-color: rgb(240 249 255 / 0.5);
}

.bg-sky-50\/60 {
  background-color: rgb(240 249 255 / 0.6);
}

.bg-sky-50\/70 {
  background-color: rgb(240 249 255 / 0.7);
}

.bg-sky-50\/75 {
  background-color: rgb(240 249 255 / 0.75);
}

.bg-sky-50\/80 {
  background-color: rgb(240 249 255 / 0.8);
}

.bg-sky-50\/90 {
  background-color: rgb(240 249 255 / 0.9);
}

.bg-sky-50\/95 {
  background-color: rgb(240 249 255 / 0.95);
}

.bg-sky-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(14 165 233 / var(--tw-bg-opacity));
}

.bg-sky-500\/0 {
  background-color: rgb(14 165 233 / 0);
}

.bg-sky-500\/10 {
  background-color: rgb(14 165 233 / 0.1);
}

.bg-sky-500\/100 {
  background-color: rgb(14 165 233 / 1);
}

.bg-sky-500\/20 {
  background-color: rgb(14 165 233 / 0.2);
}

.bg-sky-500\/25 {
  background-color: rgb(14 165 233 / 0.25);
}

.bg-sky-500\/30 {
  background-color: rgb(14 165 233 / 0.3);
}

.bg-sky-500\/40 {
  background-color: rgb(14 165 233 / 0.4);
}

.bg-sky-500\/5 {
  background-color: rgb(14 165 233 / 0.05);
}

.bg-sky-500\/50 {
  background-color: rgb(14 165 233 / 0.5);
}

.bg-sky-500\/60 {
  background-color: rgb(14 165 233 / 0.6);
}

.bg-sky-500\/70 {
  background-color: rgb(14 165 233 / 0.7);
}

.bg-sky-500\/75 {
  background-color: rgb(14 165 233 / 0.75);
}

.bg-sky-500\/80 {
  background-color: rgb(14 165 233 / 0.8);
}

.bg-sky-500\/90 {
  background-color: rgb(14 165 233 / 0.9);
}

.bg-sky-500\/95 {
  background-color: rgb(14 165 233 / 0.95);
}

.bg-sky-600 {
  --tw-bg-opacity: 1;
  background-color: rgb(2 132 199 / var(--tw-bg-opacity));
}

.bg-sky-600\/0 {
  background-color: rgb(2 132 199 / 0);
}

.bg-sky-600\/10 {
  background-color: rgb(2 132 199 / 0.1);
}

.bg-sky-600\/100 {
  background-color: rgb(2 132 199 / 1);
}

.bg-sky-600\/20 {
  background-color: rgb(2 132 199 / 0.2);
}

.bg-sky-600\/25 {
  background-color: rgb(2 132 199 / 0.25);
}

.bg-sky-600\/30 {
  background-color: rgb(2 132 199 / 0.3);
}

.bg-sky-600\/40 {
  background-color: rgb(2 132 199 / 0.4);
}

.bg-sky-600\/5 {
  background-color: rgb(2 132 199 / 0.05);
}

.bg-sky-600\/50 {
  background-color: rgb(2 132 199 / 0.5);
}

.bg-sky-600\/60 {
  background-color: rgb(2 132 199 / 0.6);
}

.bg-sky-600\/70 {
  background-color: rgb(2 132 199 / 0.7);
}

.bg-sky-600\/75 {
  background-color: rgb(2 132 199 / 0.75);
}

.bg-sky-600\/80 {
  background-color: rgb(2 132 199 / 0.8);
}

.bg-sky-600\/90 {
  background-color: rgb(2 132 199 / 0.9);
}

.bg-sky-600\/95 {
  background-color: rgb(2 132 199 / 0.95);
}

.bg-sky-700 {
  --tw-bg-opacity: 1;
  background-color: rgb(3 105 161 / var(--tw-bg-opacity));
}

.bg-sky-700\/0 {
  background-color: rgb(3 105 161 / 0);
}

.bg-sky-700\/10 {
  background-color: rgb(3 105 161 / 0.1);
}

.bg-sky-700\/100 {
  background-color: rgb(3 105 161 / 1);
}

.bg-sky-700\/20 {
  background-color: rgb(3 105 161 / 0.2);
}

.bg-sky-700\/25 {
  background-color: rgb(3 105 161 / 0.25);
}

.bg-sky-700\/30 {
  background-color: rgb(3 105 161 / 0.3);
}

.bg-sky-700\/40 {
  background-color: rgb(3 105 161 / 0.4);
}

.bg-sky-700\/5 {
  background-color: rgb(3 105 161 / 0.05);
}

.bg-sky-700\/50 {
  background-color: rgb(3 105 161 / 0.5);
}

.bg-sky-700\/60 {
  background-color: rgb(3 105 161 / 0.6);
}

.bg-sky-700\/70 {
  background-color: rgb(3 105 161 / 0.7);
}

.bg-sky-700\/75 {
  background-color: rgb(3 105 161 / 0.75);
}

.bg-sky-700\/80 {
  background-color: rgb(3 105 161 / 0.8);
}

.bg-sky-700\/90 {
  background-color: rgb(3 105 161 / 0.9);
}

.bg-sky-700\/95 {
  background-color: rgb(3 105 161 / 0.95);
}

.bg-sky-800 {
  --tw-bg-opacity: 1;
  background-color: rgb(7 89 133 / var(--tw-bg-opacity));
}

.bg-sky-800\/0 {
  background-color: rgb(7 89 133 / 0);
}

.bg-sky-800\/10 {
  background-color: rgb(7 89 133 / 0.1);
}

.bg-sky-800\/100 {
  background-color: rgb(7 89 133 / 1);
}

.bg-sky-800\/20 {
  background-color: rgb(7 89 133 / 0.2);
}

.bg-sky-800\/25 {
  background-color: rgb(7 89 133 / 0.25);
}

.bg-sky-800\/30 {
  background-color: rgb(7 89 133 / 0.3);
}

.bg-sky-800\/40 {
  background-color: rgb(7 89 133 / 0.4);
}

.bg-sky-800\/5 {
  background-color: rgb(7 89 133 / 0.05);
}

.bg-sky-800\/50 {
  background-color: rgb(7 89 133 / 0.5);
}

.bg-sky-800\/60 {
  background-color: rgb(7 89 133 / 0.6);
}

.bg-sky-800\/70 {
  background-color: rgb(7 89 133 / 0.7);
}

.bg-sky-800\/75 {
  background-color: rgb(7 89 133 / 0.75);
}

.bg-sky-800\/80 {
  background-color: rgb(7 89 133 / 0.8);
}

.bg-sky-800\/90 {
  background-color: rgb(7 89 133 / 0.9);
}

.bg-sky-800\/95 {
  background-color: rgb(7 89 133 / 0.95);
}

.bg-sky-900 {
  --tw-bg-opacity: 1;
  background-color: rgb(12 74 110 / var(--tw-bg-opacity));
}

.bg-sky-900\/0 {
  background-color: rgb(12 74 110 / 0);
}

.bg-sky-900\/10 {
  background-color: rgb(12 74 110 / 0.1);
}

.bg-sky-900\/100 {
  background-color: rgb(12 74 110 / 1);
}

.bg-sky-900\/20 {
  background-color: rgb(12 74 110 / 0.2);
}

.bg-sky-900\/25 {
  background-color: rgb(12 74 110 / 0.25);
}

.bg-sky-900\/30 {
  background-color: rgb(12 74 110 / 0.3);
}

.bg-sky-900\/40 {
  background-color: rgb(12 74 110 / 0.4);
}

.bg-sky-900\/5 {
  background-color: rgb(12 74 110 / 0.05);
}

.bg-sky-900\/50 {
  background-color: rgb(12 74 110 / 0.5);
}

.bg-sky-900\/60 {
  background-color: rgb(12 74 110 / 0.6);
}

.bg-sky-900\/70 {
  background-color: rgb(12 74 110 / 0.7);
}

.bg-sky-900\/75 {
  background-color: rgb(12 74 110 / 0.75);
}

.bg-sky-900\/80 {
  background-color: rgb(12 74 110 / 0.8);
}

.bg-sky-900\/90 {
  background-color: rgb(12 74 110 / 0.9);
}

.bg-sky-900\/95 {
  background-color: rgb(12 74 110 / 0.95);
}

.bg-sky-950 {
  --tw-bg-opacity: 1;
  background-color: rgb(8 47 73 / var(--tw-bg-opacity));
}

.bg-sky-950\/0 {
  background-color: rgb(8 47 73 / 0);
}

.bg-sky-950\/10 {
  background-color: rgb(8 47 73 / 0.1);
}

.bg-sky-950\/100 {
  background-color: rgb(8 47 73 / 1);
}

.bg-sky-950\/20 {
  background-color: rgb(8 47 73 / 0.2);
}

.bg-sky-950\/25 {
  background-color: rgb(8 47 73 / 0.25);
}

.bg-sky-950\/30 {
  background-color: rgb(8 47 73 / 0.3);
}

.bg-sky-950\/40 {
  background-color: rgb(8 47 73 / 0.4);
}

.bg-sky-950\/5 {
  background-color: rgb(8 47 73 / 0.05);
}

.bg-sky-950\/50 {
  background-color: rgb(8 47 73 / 0.5);
}

.bg-sky-950\/60 {
  background-color: rgb(8 47 73 / 0.6);
}

.bg-sky-950\/70 {
  background-color: rgb(8 47 73 / 0.7);
}

.bg-sky-950\/75 {
  background-color: rgb(8 47 73 / 0.75);
}

.bg-sky-950\/80 {
  background-color: rgb(8 47 73 / 0.8);
}

.bg-sky-950\/90 {
  background-color: rgb(8 47 73 / 0.9);
}

.bg-sky-950\/95 {
  background-color: rgb(8 47 73 / 0.95);
}

.bg-slate-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(241 245 249 / var(--tw-bg-opacity));
}

.bg-slate-100\/0 {
  background-color: rgb(241 245 249 / 0);
}

.bg-slate-100\/10 {
  background-color: rgb(241 245 249 / 0.1);
}

.bg-slate-100\/100 {
  background-color: rgb(241 245 249 / 1);
}

.bg-slate-100\/20 {
  background-color: rgb(241 245 249 / 0.2);
}

.bg-slate-100\/25 {
  background-color: rgb(241 245 249 / 0.25);
}

.bg-slate-100\/30 {
  background-color: rgb(241 245 249 / 0.3);
}

.bg-slate-100\/40 {
  background-color: rgb(241 245 249 / 0.4);
}

.bg-slate-100\/5 {
  background-color: rgb(241 245 249 / 0.05);
}

.bg-slate-100\/50 {
  background-color: rgb(241 245 249 / 0.5);
}

.bg-slate-100\/60 {
  background-color: rgb(241 245 249 / 0.6);
}

.bg-slate-100\/70 {
  background-color: rgb(241 245 249 / 0.7);
}

.bg-slate-100\/75 {
  background-color: rgb(241 245 249 / 0.75);
}

.bg-slate-100\/80 {
  background-color: rgb(241 245 249 / 0.8);
}

.bg-slate-100\/90 {
  background-color: rgb(241 245 249 / 0.9);
}

.bg-slate-100\/95 {
  background-color: rgb(241 245 249 / 0.95);
}

.bg-slate-200 {
  --tw-bg-opacity: 1;
  background-color: rgb(226 232 240 / var(--tw-bg-opacity));
}

.bg-slate-200\/0 {
  background-color: rgb(226 232 240 / 0);
}

.bg-slate-200\/10 {
  background-color: rgb(226 232 240 / 0.1);
}

.bg-slate-200\/100 {
  background-color: rgb(226 232 240 / 1);
}

.bg-slate-200\/20 {
  background-color: rgb(226 232 240 / 0.2);
}

.bg-slate-200\/25 {
  background-color: rgb(226 232 240 / 0.25);
}

.bg-slate-200\/30 {
  background-color: rgb(226 232 240 / 0.3);
}

.bg-slate-200\/40 {
  background-color: rgb(226 232 240 / 0.4);
}

.bg-slate-200\/5 {
  background-color: rgb(226 232 240 / 0.05);
}

.bg-slate-200\/50 {
  background-color: rgb(226 232 240 / 0.5);
}

.bg-slate-200\/60 {
  background-color: rgb(226 232 240 / 0.6);
}

.bg-slate-200\/70 {
  background-color: rgb(226 232 240 / 0.7);
}

.bg-slate-200\/75 {
  background-color: rgb(226 232 240 / 0.75);
}

.bg-slate-200\/80 {
  background-color: rgb(226 232 240 / 0.8);
}

.bg-slate-200\/90 {
  background-color: rgb(226 232 240 / 0.9);
}

.bg-slate-200\/95 {
  background-color: rgb(226 232 240 / 0.95);
}

.bg-slate-300 {
  --tw-bg-opacity: 1;
  background-color: rgb(203 213 225 / var(--tw-bg-opacity));
}

.bg-slate-300\/0 {
  background-color: rgb(203 213 225 / 0);
}

.bg-slate-300\/10 {
  background-color: rgb(203 213 225 / 0.1);
}

.bg-slate-300\/100 {
  background-color: rgb(203 213 225 / 1);
}

.bg-slate-300\/20 {
  background-color: rgb(203 213 225 / 0.2);
}

.bg-slate-300\/25 {
  background-color: rgb(203 213 225 / 0.25);
}

.bg-slate-300\/30 {
  background-color: rgb(203 213 225 / 0.3);
}

.bg-slate-300\/40 {
  background-color: rgb(203 213 225 / 0.4);
}

.bg-slate-300\/5 {
  background-color: rgb(203 213 225 / 0.05);
}

.bg-slate-300\/50 {
  background-color: rgb(203 213 225 / 0.5);
}

.bg-slate-300\/60 {
  background-color: rgb(203 213 225 / 0.6);
}

.bg-slate-300\/70 {
  background-color: rgb(203 213 225 / 0.7);
}

.bg-slate-300\/75 {
  background-color: rgb(203 213 225 / 0.75);
}

.bg-slate-300\/80 {
  background-color: rgb(203 213 225 / 0.8);
}

.bg-slate-300\/90 {
  background-color: rgb(203 213 225 / 0.9);
}

.bg-slate-300\/95 {
  background-color: rgb(203 213 225 / 0.95);
}

.bg-slate-400 {
  --tw-bg-opacity: 1;
  background-color: rgb(148 163 184 / var(--tw-bg-opacity));
}

.bg-slate-400\/0 {
  background-color: rgb(148 163 184 / 0);
}

.bg-slate-400\/10 {
  background-color: rgb(148 163 184 / 0.1);
}

.bg-slate-400\/100 {
  background-color: rgb(148 163 184 / 1);
}

.bg-slate-400\/20 {
  background-color: rgb(148 163 184 / 0.2);
}

.bg-slate-400\/25 {
  background-color: rgb(148 163 184 / 0.25);
}

.bg-slate-400\/30 {
  background-color: rgb(148 163 184 / 0.3);
}

.bg-slate-400\/40 {
  background-color: rgb(148 163 184 / 0.4);
}

.bg-slate-400\/5 {
  background-color: rgb(148 163 184 / 0.05);
}

.bg-slate-400\/50 {
  background-color: rgb(148 163 184 / 0.5);
}

.bg-slate-400\/60 {
  background-color: rgb(148 163 184 / 0.6);
}

.bg-slate-400\/70 {
  background-color: rgb(148 163 184 / 0.7);
}

.bg-slate-400\/75 {
  background-color: rgb(148 163 184 / 0.75);
}

.bg-slate-400\/80 {
  background-color: rgb(148 163 184 / 0.8);
}

.bg-slate-400\/90 {
  background-color: rgb(148 163 184 / 0.9);
}

.bg-slate-400\/95 {
  background-color: rgb(148 163 184 / 0.95);
}

.bg-slate-50 {
  --tw-bg-opacity: 1;
  background-color: rgb(248 250 252 / var(--tw-bg-opacity));
}

.bg-slate-50\/0 {
  background-color: rgb(248 250 252 / 0);
}

.bg-slate-50\/10 {
  background-color: rgb(248 250 252 / 0.1);
}

.bg-slate-50\/100 {
  background-color: rgb(248 250 252 / 1);
}

.bg-slate-50\/20 {
  background-color: rgb(248 250 252 / 0.2);
}

.bg-slate-50\/25 {
  background-color: rgb(248 250 252 / 0.25);
}

.bg-slate-50\/30 {
  background-color: rgb(248 250 252 / 0.3);
}

.bg-slate-50\/40 {
  background-color: rgb(248 250 252 / 0.4);
}

.bg-slate-50\/5 {
  background-color: rgb(248 250 252 / 0.05);
}

.bg-slate-50\/50 {
  background-color: rgb(248 250 252 / 0.5);
}

.bg-slate-50\/60 {
  background-color: rgb(248 250 252 / 0.6);
}

.bg-slate-50\/70 {
  background-color: rgb(248 250 252 / 0.7);
}

.bg-slate-50\/75 {
  background-color: rgb(248 250 252 / 0.75);
}

.bg-slate-50\/80 {
  background-color: rgb(248 250 252 / 0.8);
}

.bg-slate-50\/90 {
  background-color: rgb(248 250 252 / 0.9);
}

.bg-slate-50\/95 {
  background-color: rgb(248 250 252 / 0.95);
}

.bg-slate-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(100 116 139 / var(--tw-bg-opacity));
}

.bg-slate-500\/0 {
  background-color: rgb(100 116 139 / 0);
}

.bg-slate-500\/10 {
  background-color: rgb(100 116 139 / 0.1);
}

.bg-slate-500\/100 {
  background-color: rgb(100 116 139 / 1);
}

.bg-slate-500\/20 {
  background-color: rgb(100 116 139 / 0.2);
}

.bg-slate-500\/25 {
  background-color: rgb(100 116 139 / 0.25);
}

.bg-slate-500\/30 {
  background-color: rgb(100 116 139 / 0.3);
}

.bg-slate-500\/40 {
  background-color: rgb(100 116 139 / 0.4);
}

.bg-slate-500\/5 {
  background-color: rgb(100 116 139 / 0.05);
}

.bg-slate-500\/50 {
  background-color: rgb(100 116 139 / 0.5);
}

.bg-slate-500\/60 {
  background-color: rgb(100 116 139 / 0.6);
}

.bg-slate-500\/70 {
  background-color: rgb(100 116 139 / 0.7);
}

.bg-slate-500\/75 {
  background-color: rgb(100 116 139 / 0.75);
}

.bg-slate-500\/80 {
  background-color: rgb(100 116 139 / 0.8);
}

.bg-slate-500\/90 {
  background-color: rgb(100 116 139 / 0.9);
}

.bg-slate-500\/95 {
  background-color: rgb(100 116 139 / 0.95);
}

.bg-slate-600 {
  --tw-bg-opacity: 1;
  background-color: rgb(71 85 105 / var(--tw-bg-opacity));
}

.bg-slate-600\/0 {
  background-color: rgb(71 85 105 / 0);
}

.bg-slate-600\/10 {
  background-color: rgb(71 85 105 / 0.1);
}

.bg-slate-600\/100 {
  background-color: rgb(71 85 105 / 1);
}

.bg-slate-600\/20 {
  background-color: rgb(71 85 105 / 0.2);
}

.bg-slate-600\/25 {
  background-color: rgb(71 85 105 / 0.25);
}

.bg-slate-600\/30 {
  background-color: rgb(71 85 105 / 0.3);
}

.bg-slate-600\/40 {
  background-color: rgb(71 85 105 / 0.4);
}

.bg-slate-600\/5 {
  background-color: rgb(71 85 105 / 0.05);
}

.bg-slate-600\/50 {
  background-color: rgb(71 85 105 / 0.5);
}

.bg-slate-600\/60 {
  background-color: rgb(71 85 105 / 0.6);
}

.bg-slate-600\/70 {
  background-color: rgb(71 85 105 / 0.7);
}

.bg-slate-600\/75 {
  background-color: rgb(71 85 105 / 0.75);
}

.bg-slate-600\/80 {
  background-color: rgb(71 85 105 / 0.8);
}

.bg-slate-600\/90 {
  background-color: rgb(71 85 105 / 0.9);
}

.bg-slate-600\/95 {
  background-color: rgb(71 85 105 / 0.95);
}

.bg-slate-700 {
  --tw-bg-opacity: 1;
  background-color: rgb(51 65 85 / var(--tw-bg-opacity));
}

.bg-slate-700\/0 {
  background-color: rgb(51 65 85 / 0);
}

.bg-slate-700\/10 {
  background-color: rgb(51 65 85 / 0.1);
}

.bg-slate-700\/100 {
  background-color: rgb(51 65 85 / 1);
}

.bg-slate-700\/20 {
  background-color: rgb(51 65 85 / 0.2);
}

.bg-slate-700\/25 {
  background-color: rgb(51 65 85 / 0.25);
}

.bg-slate-700\/30 {
  background-color: rgb(51 65 85 / 0.3);
}

.bg-slate-700\/40 {
  background-color: rgb(51 65 85 / 0.4);
}

.bg-slate-700\/5 {
  background-color: rgb(51 65 85 / 0.05);
}

.bg-slate-700\/50 {
  background-color: rgb(51 65 85 / 0.5);
}

.bg-slate-700\/60 {
  background-color: rgb(51 65 85 / 0.6);
}

.bg-slate-700\/70 {
  background-color: rgb(51 65 85 / 0.7);
}

.bg-slate-700\/75 {
  background-color: rgb(51 65 85 / 0.75);
}

.bg-slate-700\/80 {
  background-color: rgb(51 65 85 / 0.8);
}

.bg-slate-700\/90 {
  background-color: rgb(51 65 85 / 0.9);
}

.bg-slate-700\/95 {
  background-color: rgb(51 65 85 / 0.95);
}

.bg-slate-800 {
  --tw-bg-opacity: 1;
  background-color: rgb(30 41 59 / var(--tw-bg-opacity));
}

.bg-slate-800\/0 {
  background-color: rgb(30 41 59 / 0);
}

.bg-slate-800\/10 {
  background-color: rgb(30 41 59 / 0.1);
}

.bg-slate-800\/100 {
  background-color: rgb(30 41 59 / 1);
}

.bg-slate-800\/20 {
  background-color: rgb(30 41 59 / 0.2);
}

.bg-slate-800\/25 {
  background-color: rgb(30 41 59 / 0.25);
}

.bg-slate-800\/30 {
  background-color: rgb(30 41 59 / 0.3);
}

.bg-slate-800\/40 {
  background-color: rgb(30 41 59 / 0.4);
}

.bg-slate-800\/5 {
  background-color: rgb(30 41 59 / 0.05);
}

.bg-slate-800\/50 {
  background-color: rgb(30 41 59 / 0.5);
}

.bg-slate-800\/60 {
  background-color: rgb(30 41 59 / 0.6);
}

.bg-slate-800\/70 {
  background-color: rgb(30 41 59 / 0.7);
}

.bg-slate-800\/75 {
  background-color: rgb(30 41 59 / 0.75);
}

.bg-slate-800\/80 {
  background-color: rgb(30 41 59 / 0.8);
}

.bg-slate-800\/90 {
  background-color: rgb(30 41 59 / 0.9);
}

.bg-slate-800\/95 {
  background-color: rgb(30 41 59 / 0.95);
}

.bg-slate-900 {
  --tw-bg-opacity: 1;
  background-color: rgb(15 23 42 / var(--tw-bg-opacity));
}

.bg-slate-900\/0 {
  background-color: rgb(15 23 42 / 0);
}

.bg-slate-900\/10 {
  background-color: rgb(15 23 42 / 0.1);
}

.bg-slate-900\/100 {
  background-color: rgb(15 23 42 / 1);
}

.bg-slate-900\/20 {
  background-color: rgb(15 23 42 / 0.2);
}

.bg-slate-900\/25 {
  background-color: rgb(15 23 42 / 0.25);
}

.bg-slate-900\/30 {
  background-color: rgb(15 23 42 / 0.3);
}

.bg-slate-900\/40 {
  background-color: rgb(15 23 42 / 0.4);
}

.bg-slate-900\/5 {
  background-color: rgb(15 23 42 / 0.05);
}

.bg-slate-900\/50 {
  background-color: rgb(15 23 42 / 0.5);
}

.bg-slate-900\/60 {
  background-color: rgb(15 23 42 / 0.6);
}

.bg-slate-900\/70 {
  background-color: rgb(15 23 42 / 0.7);
}

.bg-slate-900\/75 {
  background-color: rgb(15 23 42 / 0.75);
}

.bg-slate-900\/80 {
  background-color: rgb(15 23 42 / 0.8);
}

.bg-slate-900\/90 {
  background-color: rgb(15 23 42 / 0.9);
}

.bg-slate-900\/95 {
  background-color: rgb(15 23 42 / 0.95);
}

.bg-slate-950 {
  --tw-bg-opacity: 1;
  background-color: rgb(2 6 23 / var(--tw-bg-opacity));
}

.bg-slate-950\/0 {
  background-color: rgb(2 6 23 / 0);
}

.bg-slate-950\/10 {
  background-color: rgb(2 6 23 / 0.1);
}

.bg-slate-950\/100 {
  background-color: rgb(2 6 23 / 1);
}

.bg-slate-950\/20 {
  background-color: rgb(2 6 23 / 0.2);
}

.bg-slate-950\/25 {
  background-color: rgb(2 6 23 / 0.25);
}

.bg-slate-950\/30 {
  background-color: rgb(2 6 23 / 0.3);
}

.bg-slate-950\/40 {
  background-color: rgb(2 6 23 / 0.4);
}

.bg-slate-950\/5 {
  background-color: rgb(2 6 23 / 0.05);
}

.bg-slate-950\/50 {
  background-color: rgb(2 6 23 / 0.5);
}

.bg-slate-950\/60 {
  background-color: rgb(2 6 23 / 0.6);
}

.bg-slate-950\/70 {
  background-color: rgb(2 6 23 / 0.7);
}

.bg-slate-950\/75 {
  background-color: rgb(2 6 23 / 0.75);
}

.bg-slate-950\/80 {
  background-color: rgb(2 6 23 / 0.8);
}

.bg-slate-950\/90 {
  background-color: rgb(2 6 23 / 0.9);
}

.bg-slate-950\/95 {
  background-color: rgb(2 6 23 / 0.95);
}

.bg-stone-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(245 245 244 / var(--tw-bg-opacity));
}

.bg-stone-100\/0 {
  background-color: rgb(245 245 244 / 0);
}

.bg-stone-100\/10 {
  background-color: rgb(245 245 244 / 0.1);
}

.bg-stone-100\/100 {
  background-color: rgb(245 245 244 / 1);
}

.bg-stone-100\/20 {
  background-color: rgb(245 245 244 / 0.2);
}

.bg-stone-100\/25 {
  background-color: rgb(245 245 244 / 0.25);
}

.bg-stone-100\/30 {
  background-color: rgb(245 245 244 / 0.3);
}

.bg-stone-100\/40 {
  background-color: rgb(245 245 244 / 0.4);
}

.bg-stone-100\/5 {
  background-color: rgb(245 245 244 / 0.05);
}

.bg-stone-100\/50 {
  background-color: rgb(245 245 244 / 0.5);
}

.bg-stone-100\/60 {
  background-color: rgb(245 245 244 / 0.6);
}

.bg-stone-100\/70 {
  background-color: rgb(245 245 244 / 0.7);
}

.bg-stone-100\/75 {
  background-color: rgb(245 245 244 / 0.75);
}

.bg-stone-100\/80 {
  background-color: rgb(245 245 244 / 0.8);
}

.bg-stone-100\/90 {
  background-color: rgb(245 245 244 / 0.9);
}

.bg-stone-100\/95 {
  background-color: rgb(245 245 244 / 0.95);
}

.bg-stone-200 {
  --tw-bg-opacity: 1;
  background-color: rgb(231 229 228 / var(--tw-bg-opacity));
}

.bg-stone-200\/0 {
  background-color: rgb(231 229 228 / 0);
}

.bg-stone-200\/10 {
  background-color: rgb(231 229 228 / 0.1);
}

.bg-stone-200\/100 {
  background-color: rgb(231 229 228 / 1);
}

.bg-stone-200\/20 {
  background-color: rgb(231 229 228 / 0.2);
}

.bg-stone-200\/25 {
  background-color: rgb(231 229 228 / 0.25);
}

.bg-stone-200\/30 {
  background-color: rgb(231 229 228 / 0.3);
}

.bg-stone-200\/40 {
  background-color: rgb(231 229 228 / 0.4);
}

.bg-stone-200\/5 {
  background-color: rgb(231 229 228 / 0.05);
}

.bg-stone-200\/50 {
  background-color: rgb(231 229 228 / 0.5);
}

.bg-stone-200\/60 {
  background-color: rgb(231 229 228 / 0.6);
}

.bg-stone-200\/70 {
  background-color: rgb(231 229 228 / 0.7);
}

.bg-stone-200\/75 {
  background-color: rgb(231 229 228 / 0.75);
}

.bg-stone-200\/80 {
  background-color: rgb(231 229 228 / 0.8);
}

.bg-stone-200\/90 {
  background-color: rgb(231 229 228 / 0.9);
}

.bg-stone-200\/95 {
  background-color: rgb(231 229 228 / 0.95);
}

.bg-stone-300 {
  --tw-bg-opacity: 1;
  background-color: rgb(214 211 209 / var(--tw-bg-opacity));
}

.bg-stone-300\/0 {
  background-color: rgb(214 211 209 / 0);
}

.bg-stone-300\/10 {
  background-color: rgb(214 211 209 / 0.1);
}

.bg-stone-300\/100 {
  background-color: rgb(214 211 209 / 1);
}

.bg-stone-300\/20 {
  background-color: rgb(214 211 209 / 0.2);
}

.bg-stone-300\/25 {
  background-color: rgb(214 211 209 / 0.25);
}

.bg-stone-300\/30 {
  background-color: rgb(214 211 209 / 0.3);
}

.bg-stone-300\/40 {
  background-color: rgb(214 211 209 / 0.4);
}

.bg-stone-300\/5 {
  background-color: rgb(214 211 209 / 0.05);
}

.bg-stone-300\/50 {
  background-color: rgb(214 211 209 / 0.5);
}

.bg-stone-300\/60 {
  background-color: rgb(214 211 209 / 0.6);
}

.bg-stone-300\/70 {
  background-color: rgb(214 211 209 / 0.7);
}

.bg-stone-300\/75 {
  background-color: rgb(214 211 209 / 0.75);
}

.bg-stone-300\/80 {
  background-color: rgb(214 211 209 / 0.8);
}

.bg-stone-300\/90 {
  background-color: rgb(214 211 209 / 0.9);
}

.bg-stone-300\/95 {
  background-color: rgb(214 211 209 / 0.95);
}

.bg-stone-400 {
  --tw-bg-opacity: 1;
  background-color: rgb(168 162 158 / var(--tw-bg-opacity));
}

.bg-stone-400\/0 {
  background-color: rgb(168 162 158 / 0);
}

.bg-stone-400\/10 {
  background-color: rgb(168 162 158 / 0.1);
}

.bg-stone-400\/100 {
  background-color: rgb(168 162 158 / 1);
}

.bg-stone-400\/20 {
  background-color: rgb(168 162 158 / 0.2);
}

.bg-stone-400\/25 {
  background-color: rgb(168 162 158 / 0.25);
}

.bg-stone-400\/30 {
  background-color: rgb(168 162 158 / 0.3);
}

.bg-stone-400\/40 {
  background-color: rgb(168 162 158 / 0.4);
}

.bg-stone-400\/5 {
  background-color: rgb(168 162 158 / 0.05);
}

.bg-stone-400\/50 {
  background-color: rgb(168 162 158 / 0.5);
}

.bg-stone-400\/60 {
  background-color: rgb(168 162 158 / 0.6);
}

.bg-stone-400\/70 {
  background-color: rgb(168 162 158 / 0.7);
}

.bg-stone-400\/75 {
  background-color: rgb(168 162 158 / 0.75);
}

.bg-stone-400\/80 {
  background-color: rgb(168 162 158 / 0.8);
}

.bg-stone-400\/90 {
  background-color: rgb(168 162 158 / 0.9);
}

.bg-stone-400\/95 {
  background-color: rgb(168 162 158 / 0.95);
}

.bg-stone-50 {
  --tw-bg-opacity: 1;
  background-color: rgb(250 250 249 / var(--tw-bg-opacity));
}

.bg-stone-50\/0 {
  background-color: rgb(250 250 249 / 0);
}

.bg-stone-50\/10 {
  background-color: rgb(250 250 249 / 0.1);
}

.bg-stone-50\/100 {
  background-color: rgb(250 250 249 / 1);
}

.bg-stone-50\/20 {
  background-color: rgb(250 250 249 / 0.2);
}

.bg-stone-50\/25 {
  background-color: rgb(250 250 249 / 0.25);
}

.bg-stone-50\/30 {
  background-color: rgb(250 250 249 / 0.3);
}

.bg-stone-50\/40 {
  background-color: rgb(250 250 249 / 0.4);
}

.bg-stone-50\/5 {
  background-color: rgb(250 250 249 / 0.05);
}

.bg-stone-50\/50 {
  background-color: rgb(250 250 249 / 0.5);
}

.bg-stone-50\/60 {
  background-color: rgb(250 250 249 / 0.6);
}

.bg-stone-50\/70 {
  background-color: rgb(250 250 249 / 0.7);
}

.bg-stone-50\/75 {
  background-color: rgb(250 250 249 / 0.75);
}

.bg-stone-50\/80 {
  background-color: rgb(250 250 249 / 0.8);
}

.bg-stone-50\/90 {
  background-color: rgb(250 250 249 / 0.9);
}

.bg-stone-50\/95 {
  background-color: rgb(250 250 249 / 0.95);
}

.bg-stone-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(120 113 108 / var(--tw-bg-opacity));
}

.bg-stone-500\/0 {
  background-color: rgb(120 113 108 / 0);
}

.bg-stone-500\/10 {
  background-color: rgb(120 113 108 / 0.1);
}

.bg-stone-500\/100 {
  background-color: rgb(120 113 108 / 1);
}

.bg-stone-500\/20 {
  background-color: rgb(120 113 108 / 0.2);
}

.bg-stone-500\/25 {
  background-color: rgb(120 113 108 / 0.25);
}

.bg-stone-500\/30 {
  background-color: rgb(120 113 108 / 0.3);
}

.bg-stone-500\/40 {
  background-color: rgb(120 113 108 / 0.4);
}

.bg-stone-500\/5 {
  background-color: rgb(120 113 108 / 0.05);
}

.bg-stone-500\/50 {
  background-color: rgb(120 113 108 / 0.5);
}

.bg-stone-500\/60 {
  background-color: rgb(120 113 108 / 0.6);
}

.bg-stone-500\/70 {
  background-color: rgb(120 113 108 / 0.7);
}

.bg-stone-500\/75 {
  background-color: rgb(120 113 108 / 0.75);
}

.bg-stone-500\/80 {
  background-color: rgb(120 113 108 / 0.8);
}

.bg-stone-500\/90 {
  background-color: rgb(120 113 108 / 0.9);
}

.bg-stone-500\/95 {
  background-color: rgb(120 113 108 / 0.95);
}

.bg-stone-600 {
  --tw-bg-opacity: 1;
  background-color: rgb(87 83 78 / var(--tw-bg-opacity));
}

.bg-stone-600\/0 {
  background-color: rgb(87 83 78 / 0);
}

.bg-stone-600\/10 {
  background-color: rgb(87 83 78 / 0.1);
}

.bg-stone-600\/100 {
  background-color: rgb(87 83 78 / 1);
}

.bg-stone-600\/20 {
  background-color: rgb(87 83 78 / 0.2);
}

.bg-stone-600\/25 {
  background-color: rgb(87 83 78 / 0.25);
}

.bg-stone-600\/30 {
  background-color: rgb(87 83 78 / 0.3);
}

.bg-stone-600\/40 {
  background-color: rgb(87 83 78 / 0.4);
}

.bg-stone-600\/5 {
  background-color: rgb(87 83 78 / 0.05);
}

.bg-stone-600\/50 {
  background-color: rgb(87 83 78 / 0.5);
}

.bg-stone-600\/60 {
  background-color: rgb(87 83 78 / 0.6);
}

.bg-stone-600\/70 {
  background-color: rgb(87 83 78 / 0.7);
}

.bg-stone-600\/75 {
  background-color: rgb(87 83 78 / 0.75);
}

.bg-stone-600\/80 {
  background-color: rgb(87 83 78 / 0.8);
}

.bg-stone-600\/90 {
  background-color: rgb(87 83 78 / 0.9);
}

.bg-stone-600\/95 {
  background-color: rgb(87 83 78 / 0.95);
}

.bg-stone-700 {
  --tw-bg-opacity: 1;
  background-color: rgb(68 64 60 / var(--tw-bg-opacity));
}

.bg-stone-700\/0 {
  background-color: rgb(68 64 60 / 0);
}

.bg-stone-700\/10 {
  background-color: rgb(68 64 60 / 0.1);
}

.bg-stone-700\/100 {
  background-color: rgb(68 64 60 / 1);
}

.bg-stone-700\/20 {
  background-color: rgb(68 64 60 / 0.2);
}

.bg-stone-700\/25 {
  background-color: rgb(68 64 60 / 0.25);
}

.bg-stone-700\/30 {
  background-color: rgb(68 64 60 / 0.3);
}

.bg-stone-700\/40 {
  background-color: rgb(68 64 60 / 0.4);
}

.bg-stone-700\/5 {
  background-color: rgb(68 64 60 / 0.05);
}

.bg-stone-700\/50 {
  background-color: rgb(68 64 60 / 0.5);
}

.bg-stone-700\/60 {
  background-color: rgb(68 64 60 / 0.6);
}

.bg-stone-700\/70 {
  background-color: rgb(68 64 60 / 0.7);
}

.bg-stone-700\/75 {
  background-color: rgb(68 64 60 / 0.75);
}

.bg-stone-700\/80 {
  background-color: rgb(68 64 60 / 0.8);
}

.bg-stone-700\/90 {
  background-color: rgb(68 64 60 / 0.9);
}

.bg-stone-700\/95 {
  background-color: rgb(68 64 60 / 0.95);
}

.bg-stone-800 {
  --tw-bg-opacity: 1;
  background-color: rgb(41 37 36 / var(--tw-bg-opacity));
}

.bg-stone-800\/0 {
  background-color: rgb(41 37 36 / 0);
}

.bg-stone-800\/10 {
  background-color: rgb(41 37 36 / 0.1);
}

.bg-stone-800\/100 {
  background-color: rgb(41 37 36 / 1);
}

.bg-stone-800\/20 {
  background-color: rgb(41 37 36 / 0.2);
}

.bg-stone-800\/25 {
  background-color: rgb(41 37 36 / 0.25);
}

.bg-stone-800\/30 {
  background-color: rgb(41 37 36 / 0.3);
}

.bg-stone-800\/40 {
  background-color: rgb(41 37 36 / 0.4);
}

.bg-stone-800\/5 {
  background-color: rgb(41 37 36 / 0.05);
}

.bg-stone-800\/50 {
  background-color: rgb(41 37 36 / 0.5);
}

.bg-stone-800\/60 {
  background-color: rgb(41 37 36 / 0.6);
}

.bg-stone-800\/70 {
  background-color: rgb(41 37 36 / 0.7);
}

.bg-stone-800\/75 {
  background-color: rgb(41 37 36 / 0.75);
}

.bg-stone-800\/80 {
  background-color: rgb(41 37 36 / 0.8);
}

.bg-stone-800\/90 {
  background-color: rgb(41 37 36 / 0.9);
}

.bg-stone-800\/95 {
  background-color: rgb(41 37 36 / 0.95);
}

.bg-stone-900 {
  --tw-bg-opacity: 1;
  background-color: rgb(28 25 23 / var(--tw-bg-opacity));
}

.bg-stone-900\/0 {
  background-color: rgb(28 25 23 / 0);
}

.bg-stone-900\/10 {
  background-color: rgb(28 25 23 / 0.1);
}

.bg-stone-900\/100 {
  background-color: rgb(28 25 23 / 1);
}

.bg-stone-900\/20 {
  background-color: rgb(28 25 23 / 0.2);
}

.bg-stone-900\/25 {
  background-color: rgb(28 25 23 / 0.25);
}

.bg-stone-900\/30 {
  background-color: rgb(28 25 23 / 0.3);
}

.bg-stone-900\/40 {
  background-color: rgb(28 25 23 / 0.4);
}

.bg-stone-900\/5 {
  background-color: rgb(28 25 23 / 0.05);
}

.bg-stone-900\/50 {
  background-color: rgb(28 25 23 / 0.5);
}

.bg-stone-900\/60 {
  background-color: rgb(28 25 23 / 0.6);
}

.bg-stone-900\/70 {
  background-color: rgb(28 25 23 / 0.7);
}

.bg-stone-900\/75 {
  background-color: rgb(28 25 23 / 0.75);
}

.bg-stone-900\/80 {
  background-color: rgb(28 25 23 / 0.8);
}

.bg-stone-900\/90 {
  background-color: rgb(28 25 23 / 0.9);
}

.bg-stone-900\/95 {
  background-color: rgb(28 25 23 / 0.95);
}

.bg-stone-950 {
  --tw-bg-opacity: 1;
  background-color: rgb(12 10 9 / var(--tw-bg-opacity));
}

.bg-stone-950\/0 {
  background-color: rgb(12 10 9 / 0);
}

.bg-stone-950\/10 {
  background-color: rgb(12 10 9 / 0.1);
}

.bg-stone-950\/100 {
  background-color: rgb(12 10 9 / 1);
}

.bg-stone-950\/20 {
  background-color: rgb(12 10 9 / 0.2);
}

.bg-stone-950\/25 {
  background-color: rgb(12 10 9 / 0.25);
}

.bg-stone-950\/30 {
  background-color: rgb(12 10 9 / 0.3);
}

.bg-stone-950\/40 {
  background-color: rgb(12 10 9 / 0.4);
}

.bg-stone-950\/5 {
  background-color: rgb(12 10 9 / 0.05);
}

.bg-stone-950\/50 {
  background-color: rgb(12 10 9 / 0.5);
}

.bg-stone-950\/60 {
  background-color: rgb(12 10 9 / 0.6);
}

.bg-stone-950\/70 {
  background-color: rgb(12 10 9 / 0.7);
}

.bg-stone-950\/75 {
  background-color: rgb(12 10 9 / 0.75);
}

.bg-stone-950\/80 {
  background-color: rgb(12 10 9 / 0.8);
}

.bg-stone-950\/90 {
  background-color: rgb(12 10 9 / 0.9);
}

.bg-stone-950\/95 {
  background-color: rgb(12 10 9 / 0.95);
}

.bg-teal-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(204 251 241 / var(--tw-bg-opacity));
}

.bg-teal-100\/0 {
  background-color: rgb(204 251 241 / 0);
}

.bg-teal-100\/10 {
  background-color: rgb(204 251 241 / 0.1);
}

.bg-teal-100\/100 {
  background-color: rgb(204 251 241 / 1);
}

.bg-teal-100\/20 {
  background-color: rgb(204 251 241 / 0.2);
}

.bg-teal-100\/25 {
  background-color: rgb(204 251 241 / 0.25);
}

.bg-teal-100\/30 {
  background-color: rgb(204 251 241 / 0.3);
}

.bg-teal-100\/40 {
  background-color: rgb(204 251 241 / 0.4);
}

.bg-teal-100\/5 {
  background-color: rgb(204 251 241 / 0.05);
}

.bg-teal-100\/50 {
  background-color: rgb(204 251 241 / 0.5);
}

.bg-teal-100\/60 {
  background-color: rgb(204 251 241 / 0.6);
}

.bg-teal-100\/70 {
  background-color: rgb(204 251 241 / 0.7);
}

.bg-teal-100\/75 {
  background-color: rgb(204 251 241 / 0.75);
}

.bg-teal-100\/80 {
  background-color: rgb(204 251 241 / 0.8);
}

.bg-teal-100\/90 {
  background-color: rgb(204 251 241 / 0.9);
}

.bg-teal-100\/95 {
  background-color: rgb(204 251 241 / 0.95);
}

.bg-teal-200 {
  --tw-bg-opacity: 1;
  background-color: rgb(153 246 228 / var(--tw-bg-opacity));
}

.bg-teal-200\/0 {
  background-color: rgb(153 246 228 / 0);
}

.bg-teal-200\/10 {
  background-color: rgb(153 246 228 / 0.1);
}

.bg-teal-200\/100 {
  background-color: rgb(153 246 228 / 1);
}

.bg-teal-200\/20 {
  background-color: rgb(153 246 228 / 0.2);
}

.bg-teal-200\/25 {
  background-color: rgb(153 246 228 / 0.25);
}

.bg-teal-200\/30 {
  background-color: rgb(153 246 228 / 0.3);
}

.bg-teal-200\/40 {
  background-color: rgb(153 246 228 / 0.4);
}

.bg-teal-200\/5 {
  background-color: rgb(153 246 228 / 0.05);
}

.bg-teal-200\/50 {
  background-color: rgb(153 246 228 / 0.5);
}

.bg-teal-200\/60 {
  background-color: rgb(153 246 228 / 0.6);
}

.bg-teal-200\/70 {
  background-color: rgb(153 246 228 / 0.7);
}

.bg-teal-200\/75 {
  background-color: rgb(153 246 228 / 0.75);
}

.bg-teal-200\/80 {
  background-color: rgb(153 246 228 / 0.8);
}

.bg-teal-200\/90 {
  background-color: rgb(153 246 228 / 0.9);
}

.bg-teal-200\/95 {
  background-color: rgb(153 246 228 / 0.95);
}

.bg-teal-300 {
  --tw-bg-opacity: 1;
  background-color: rgb(94 234 212 / var(--tw-bg-opacity));
}

.bg-teal-300\/0 {
  background-color: rgb(94 234 212 / 0);
}

.bg-teal-300\/10 {
  background-color: rgb(94 234 212 / 0.1);
}

.bg-teal-300\/100 {
  background-color: rgb(94 234 212 / 1);
}

.bg-teal-300\/20 {
  background-color: rgb(94 234 212 / 0.2);
}

.bg-teal-300\/25 {
  background-color: rgb(94 234 212 / 0.25);
}

.bg-teal-300\/30 {
  background-color: rgb(94 234 212 / 0.3);
}

.bg-teal-300\/40 {
  background-color: rgb(94 234 212 / 0.4);
}

.bg-teal-300\/5 {
  background-color: rgb(94 234 212 / 0.05);
}

.bg-teal-300\/50 {
  background-color: rgb(94 234 212 / 0.5);
}

.bg-teal-300\/60 {
  background-color: rgb(94 234 212 / 0.6);
}

.bg-teal-300\/70 {
  background-color: rgb(94 234 212 / 0.7);
}

.bg-teal-300\/75 {
  background-color: rgb(94 234 212 / 0.75);
}

.bg-teal-300\/80 {
  background-color: rgb(94 234 212 / 0.8);
}

.bg-teal-300\/90 {
  background-color: rgb(94 234 212 / 0.9);
}

.bg-teal-300\/95 {
  background-color: rgb(94 234 212 / 0.95);
}

.bg-teal-400 {
  --tw-bg-opacity: 1;
  background-color: rgb(45 212 191 / var(--tw-bg-opacity));
}

.bg-teal-400\/0 {
  background-color: rgb(45 212 191 / 0);
}

.bg-teal-400\/10 {
  background-color: rgb(45 212 191 / 0.1);
}

.bg-teal-400\/100 {
  background-color: rgb(45 212 191 / 1);
}

.bg-teal-400\/20 {
  background-color: rgb(45 212 191 / 0.2);
}

.bg-teal-400\/25 {
  background-color: rgb(45 212 191 / 0.25);
}

.bg-teal-400\/30 {
  background-color: rgb(45 212 191 / 0.3);
}

.bg-teal-400\/40 {
  background-color: rgb(45 212 191 / 0.4);
}

.bg-teal-400\/5 {
  background-color: rgb(45 212 191 / 0.05);
}

.bg-teal-400\/50 {
  background-color: rgb(45 212 191 / 0.5);
}

.bg-teal-400\/60 {
  background-color: rgb(45 212 191 / 0.6);
}

.bg-teal-400\/70 {
  background-color: rgb(45 212 191 / 0.7);
}

.bg-teal-400\/75 {
  background-color: rgb(45 212 191 / 0.75);
}

.bg-teal-400\/80 {
  background-color: rgb(45 212 191 / 0.8);
}

.bg-teal-400\/90 {
  background-color: rgb(45 212 191 / 0.9);
}

.bg-teal-400\/95 {
  background-color: rgb(45 212 191 / 0.95);
}

.bg-teal-50 {
  --tw-bg-opacity: 1;
  background-color: rgb(240 253 250 / var(--tw-bg-opacity));
}

.bg-teal-50\/0 {
  background-color: rgb(240 253 250 / 0);
}

.bg-teal-50\/10 {
  background-color: rgb(240 253 250 / 0.1);
}

.bg-teal-50\/100 {
  background-color: rgb(240 253 250 / 1);
}

.bg-teal-50\/20 {
  background-color: rgb(240 253 250 / 0.2);
}

.bg-teal-50\/25 {
  background-color: rgb(240 253 250 / 0.25);
}

.bg-teal-50\/30 {
  background-color: rgb(240 253 250 / 0.3);
}

.bg-teal-50\/40 {
  background-color: rgb(240 253 250 / 0.4);
}

.bg-teal-50\/5 {
  background-color: rgb(240 253 250 / 0.05);
}

.bg-teal-50\/50 {
  background-color: rgb(240 253 250 / 0.5);
}

.bg-teal-50\/60 {
  background-color: rgb(240 253 250 / 0.6);
}

.bg-teal-50\/70 {
  background-color: rgb(240 253 250 / 0.7);
}

.bg-teal-50\/75 {
  background-color: rgb(240 253 250 / 0.75);
}

.bg-teal-50\/80 {
  background-color: rgb(240 253 250 / 0.8);
}

.bg-teal-50\/90 {
  background-color: rgb(240 253 250 / 0.9);
}

.bg-teal-50\/95 {
  background-color: rgb(240 253 250 / 0.95);
}

.bg-teal-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(20 184 166 / var(--tw-bg-opacity));
}

.bg-teal-500\/0 {
  background-color: rgb(20 184 166 / 0);
}

.bg-teal-500\/10 {
  background-color: rgb(20 184 166 / 0.1);
}

.bg-teal-500\/100 {
  background-color: rgb(20 184 166 / 1);
}

.bg-teal-500\/20 {
  background-color: rgb(20 184 166 / 0.2);
}

.bg-teal-500\/25 {
  background-color: rgb(20 184 166 / 0.25);
}

.bg-teal-500\/30 {
  background-color: rgb(20 184 166 / 0.3);
}

.bg-teal-500\/40 {
  background-color: rgb(20 184 166 / 0.4);
}

.bg-teal-500\/5 {
  background-color: rgb(20 184 166 / 0.05);
}

.bg-teal-500\/50 {
  background-color: rgb(20 184 166 / 0.5);
}

.bg-teal-500\/60 {
  background-color: rgb(20 184 166 / 0.6);
}

.bg-teal-500\/70 {
  background-color: rgb(20 184 166 / 0.7);
}

.bg-teal-500\/75 {
  background-color: rgb(20 184 166 / 0.75);
}

.bg-teal-500\/80 {
  background-color: rgb(20 184 166 / 0.8);
}

.bg-teal-500\/90 {
  background-color: rgb(20 184 166 / 0.9);
}

.bg-teal-500\/95 {
  background-color: rgb(20 184 166 / 0.95);
}

.bg-teal-600 {
  --tw-bg-opacity: 1;
  background-color: rgb(13 148 136 / var(--tw-bg-opacity));
}

.bg-teal-600\/0 {
  background-color: rgb(13 148 136 / 0);
}

.bg-teal-600\/10 {
  background-color: rgb(13 148 136 / 0.1);
}

.bg-teal-600\/100 {
  background-color: rgb(13 148 136 / 1);
}

.bg-teal-600\/20 {
  background-color: rgb(13 148 136 / 0.2);
}

.bg-teal-600\/25 {
  background-color: rgb(13 148 136 / 0.25);
}

.bg-teal-600\/30 {
  background-color: rgb(13 148 136 / 0.3);
}

.bg-teal-600\/40 {
  background-color: rgb(13 148 136 / 0.4);
}

.bg-teal-600\/5 {
  background-color: rgb(13 148 136 / 0.05);
}

.bg-teal-600\/50 {
  background-color: rgb(13 148 136 / 0.5);
}

.bg-teal-600\/60 {
  background-color: rgb(13 148 136 / 0.6);
}

.bg-teal-600\/70 {
  background-color: rgb(13 148 136 / 0.7);
}

.bg-teal-600\/75 {
  background-color: rgb(13 148 136 / 0.75);
}

.bg-teal-600\/80 {
  background-color: rgb(13 148 136 / 0.8);
}

.bg-teal-600\/90 {
  background-color: rgb(13 148 136 / 0.9);
}

.bg-teal-600\/95 {
  background-color: rgb(13 148 136 / 0.95);
}

.bg-teal-700 {
  --tw-bg-opacity: 1;
  background-color: rgb(15 118 110 / var(--tw-bg-opacity));
}

.bg-teal-700\/0 {
  background-color: rgb(15 118 110 / 0);
}

.bg-teal-700\/10 {
  background-color: rgb(15 118 110 / 0.1);
}

.bg-teal-700\/100 {
  background-color: rgb(15 118 110 / 1);
}

.bg-teal-700\/20 {
  background-color: rgb(15 118 110 / 0.2);
}

.bg-teal-700\/25 {
  background-color: rgb(15 118 110 / 0.25);
}

.bg-teal-700\/30 {
  background-color: rgb(15 118 110 / 0.3);
}

.bg-teal-700\/40 {
  background-color: rgb(15 118 110 / 0.4);
}

.bg-teal-700\/5 {
  background-color: rgb(15 118 110 / 0.05);
}

.bg-teal-700\/50 {
  background-color: rgb(15 118 110 / 0.5);
}

.bg-teal-700\/60 {
  background-color: rgb(15 118 110 / 0.6);
}

.bg-teal-700\/70 {
  background-color: rgb(15 118 110 / 0.7);
}

.bg-teal-700\/75 {
  background-color: rgb(15 118 110 / 0.75);
}

.bg-teal-700\/80 {
  background-color: rgb(15 118 110 / 0.8);
}

.bg-teal-700\/90 {
  background-color: rgb(15 118 110 / 0.9);
}

.bg-teal-700\/95 {
  background-color: rgb(15 118 110 / 0.95);
}

.bg-teal-800 {
  --tw-bg-opacity: 1;
  background-color: rgb(17 94 89 / var(--tw-bg-opacity));
}

.bg-teal-800\/0 {
  background-color: rgb(17 94 89 / 0);
}

.bg-teal-800\/10 {
  background-color: rgb(17 94 89 / 0.1);
}

.bg-teal-800\/100 {
  background-color: rgb(17 94 89 / 1);
}

.bg-teal-800\/20 {
  background-color: rgb(17 94 89 / 0.2);
}

.bg-teal-800\/25 {
  background-color: rgb(17 94 89 / 0.25);
}

.bg-teal-800\/30 {
  background-color: rgb(17 94 89 / 0.3);
}

.bg-teal-800\/40 {
  background-color: rgb(17 94 89 / 0.4);
}

.bg-teal-800\/5 {
  background-color: rgb(17 94 89 / 0.05);
}

.bg-teal-800\/50 {
  background-color: rgb(17 94 89 / 0.5);
}

.bg-teal-800\/60 {
  background-color: rgb(17 94 89 / 0.6);
}

.bg-teal-800\/70 {
  background-color: rgb(17 94 89 / 0.7);
}

.bg-teal-800\/75 {
  background-color: rgb(17 94 89 / 0.75);
}

.bg-teal-800\/80 {
  background-color: rgb(17 94 89 / 0.8);
}

.bg-teal-800\/90 {
  background-color: rgb(17 94 89 / 0.9);
}

.bg-teal-800\/95 {
  background-color: rgb(17 94 89 / 0.95);
}

.bg-teal-900 {
  --tw-bg-opacity: 1;
  background-color: rgb(19 78 74 / var(--tw-bg-opacity));
}

.bg-teal-900\/0 {
  background-color: rgb(19 78 74 / 0);
}

.bg-teal-900\/10 {
  background-color: rgb(19 78 74 / 0.1);
}

.bg-teal-900\/100 {
  background-color: rgb(19 78 74 / 1);
}

.bg-teal-900\/20 {
  background-color: rgb(19 78 74 / 0.2);
}

.bg-teal-900\/25 {
  background-color: rgb(19 78 74 / 0.25);
}

.bg-teal-900\/30 {
  background-color: rgb(19 78 74 / 0.3);
}

.bg-teal-900\/40 {
  background-color: rgb(19 78 74 / 0.4);
}

.bg-teal-900\/5 {
  background-color: rgb(19 78 74 / 0.05);
}

.bg-teal-900\/50 {
  background-color: rgb(19 78 74 / 0.5);
}

.bg-teal-900\/60 {
  background-color: rgb(19 78 74 / 0.6);
}

.bg-teal-900\/70 {
  background-color: rgb(19 78 74 / 0.7);
}

.bg-teal-900\/75 {
  background-color: rgb(19 78 74 / 0.75);
}

.bg-teal-900\/80 {
  background-color: rgb(19 78 74 / 0.8);
}

.bg-teal-900\/90 {
  background-color: rgb(19 78 74 / 0.9);
}

.bg-teal-900\/95 {
  background-color: rgb(19 78 74 / 0.95);
}

.bg-teal-950 {
  --tw-bg-opacity: 1;
  background-color: rgb(4 47 46 / var(--tw-bg-opacity));
}

.bg-teal-950\/0 {
  background-color: rgb(4 47 46 / 0);
}

.bg-teal-950\/10 {
  background-color: rgb(4 47 46 / 0.1);
}

.bg-teal-950\/100 {
  background-color: rgb(4 47 46 / 1);
}

.bg-teal-950\/20 {
  background-color: rgb(4 47 46 / 0.2);
}

.bg-teal-950\/25 {
  background-color: rgb(4 47 46 / 0.25);
}

.bg-teal-950\/30 {
  background-color: rgb(4 47 46 / 0.3);
}

.bg-teal-950\/40 {
  background-color: rgb(4 47 46 / 0.4);
}

.bg-teal-950\/5 {
  background-color: rgb(4 47 46 / 0.05);
}

.bg-teal-950\/50 {
  background-color: rgb(4 47 46 / 0.5);
}

.bg-teal-950\/60 {
  background-color: rgb(4 47 46 / 0.6);
}

.bg-teal-950\/70 {
  background-color: rgb(4 47 46 / 0.7);
}

.bg-teal-950\/75 {
  background-color: rgb(4 47 46 / 0.75);
}

.bg-teal-950\/80 {
  background-color: rgb(4 47 46 / 0.8);
}

.bg-teal-950\/90 {
  background-color: rgb(4 47 46 / 0.9);
}

.bg-teal-950\/95 {
  background-color: rgb(4 47 46 / 0.95);
}

.bg-transparent {
  background-color: transparent;
}

.bg-transparent\/0 {
  background-color: rgb(0 0 0 / 0);
}

.bg-transparent\/10 {
  background-color: rgb(0 0 0 / 0.1);
}

.bg-transparent\/100 {
  background-color: rgb(0 0 0 / 1);
}

.bg-transparent\/20 {
  background-color: rgb(0 0 0 / 0.2);
}

.bg-transparent\/25 {
  background-color: rgb(0 0 0 / 0.25);
}

.bg-transparent\/30 {
  background-color: rgb(0 0 0 / 0.3);
}

.bg-transparent\/40 {
  background-color: rgb(0 0 0 / 0.4);
}

.bg-transparent\/5 {
  background-color: rgb(0 0 0 / 0.05);
}

.bg-transparent\/50 {
  background-color: rgb(0 0 0 / 0.5);
}

.bg-transparent\/60 {
  background-color: rgb(0 0 0 / 0.6);
}

.bg-transparent\/70 {
  background-color: rgb(0 0 0 / 0.7);
}

.bg-transparent\/75 {
  background-color: rgb(0 0 0 / 0.75);
}

.bg-transparent\/80 {
  background-color: rgb(0 0 0 / 0.8);
}

.bg-transparent\/90 {
  background-color: rgb(0 0 0 / 0.9);
}

.bg-transparent\/95 {
  background-color: rgb(0 0 0 / 0.95);
}

.bg-valueDrawer-blu1 {
  --tw-bg-opacity: 1;
  background-color: rgb(0 55 100 / var(--tw-bg-opacity));
}

.bg-valueDrawer-blu1\/0 {
  background-color: rgb(0 55 100 / 0);
}

.bg-valueDrawer-blu1\/10 {
  background-color: rgb(0 55 100 / 0.1);
}

.bg-valueDrawer-blu1\/100 {
  background-color: rgb(0 55 100 / 1);
}

.bg-valueDrawer-blu1\/20 {
  background-color: rgb(0 55 100 / 0.2);
}

.bg-valueDrawer-blu1\/25 {
  background-color: rgb(0 55 100 / 0.25);
}

.bg-valueDrawer-blu1\/30 {
  background-color: rgb(0 55 100 / 0.3);
}

.bg-valueDrawer-blu1\/40 {
  background-color: rgb(0 55 100 / 0.4);
}

.bg-valueDrawer-blu1\/5 {
  background-color: rgb(0 55 100 / 0.05);
}

.bg-valueDrawer-blu1\/50 {
  background-color: rgb(0 55 100 / 0.5);
}

.bg-valueDrawer-blu1\/60 {
  background-color: rgb(0 55 100 / 0.6);
}

.bg-valueDrawer-blu1\/70 {
  background-color: rgb(0 55 100 / 0.7);
}

.bg-valueDrawer-blu1\/75 {
  background-color: rgb(0 55 100 / 0.75);
}

.bg-valueDrawer-blu1\/80 {
  background-color: rgb(0 55 100 / 0.8);
}

.bg-valueDrawer-blu1\/90 {
  background-color: rgb(0 55 100 / 0.9);
}

.bg-valueDrawer-blu1\/95 {
  background-color: rgb(0 55 100 / 0.95);
}

.bg-valueDrawer-grn1 {
  --tw-bg-opacity: 1;
  background-color: rgb(58 178 0 / var(--tw-bg-opacity));
}

.bg-valueDrawer-grn1\/0 {
  background-color: rgb(58 178 0 / 0);
}

.bg-valueDrawer-grn1\/10 {
  background-color: rgb(58 178 0 / 0.1);
}

.bg-valueDrawer-grn1\/100 {
  background-color: rgb(58 178 0 / 1);
}

.bg-valueDrawer-grn1\/20 {
  background-color: rgb(58 178 0 / 0.2);
}

.bg-valueDrawer-grn1\/25 {
  background-color: rgb(58 178 0 / 0.25);
}

.bg-valueDrawer-grn1\/30 {
  background-color: rgb(58 178 0 / 0.3);
}

.bg-valueDrawer-grn1\/40 {
  background-color: rgb(58 178 0 / 0.4);
}

.bg-valueDrawer-grn1\/5 {
  background-color: rgb(58 178 0 / 0.05);
}

.bg-valueDrawer-grn1\/50 {
  background-color: rgb(58 178 0 / 0.5);
}

.bg-valueDrawer-grn1\/60 {
  background-color: rgb(58 178 0 / 0.6);
}

.bg-valueDrawer-grn1\/70 {
  background-color: rgb(58 178 0 / 0.7);
}

.bg-valueDrawer-grn1\/75 {
  background-color: rgb(58 178 0 / 0.75);
}

.bg-valueDrawer-grn1\/80 {
  background-color: rgb(58 178 0 / 0.8);
}

.bg-valueDrawer-grn1\/90 {
  background-color: rgb(58 178 0 / 0.9);
}

.bg-valueDrawer-grn1\/95 {
  background-color: rgb(58 178 0 / 0.95);
}

.bg-valueDrawer-ylw1 {
  --tw-bg-opacity: 1;
  background-color: rgb(219 166 29 / var(--tw-bg-opacity));
}

.bg-valueDrawer-ylw1\/0 {
  background-color: rgb(219 166 29 / 0);
}

.bg-valueDrawer-ylw1\/10 {
  background-color: rgb(219 166 29 / 0.1);
}

.bg-valueDrawer-ylw1\/100 {
  background-color: rgb(219 166 29 / 1);
}

.bg-valueDrawer-ylw1\/20 {
  background-color: rgb(219 166 29 / 0.2);
}

.bg-valueDrawer-ylw1\/25 {
  background-color: rgb(219 166 29 / 0.25);
}

.bg-valueDrawer-ylw1\/30 {
  background-color: rgb(219 166 29 / 0.3);
}

.bg-valueDrawer-ylw1\/40 {
  background-color: rgb(219 166 29 / 0.4);
}

.bg-valueDrawer-ylw1\/5 {
  background-color: rgb(219 166 29 / 0.05);
}

.bg-valueDrawer-ylw1\/50 {
  background-color: rgb(219 166 29 / 0.5);
}

.bg-valueDrawer-ylw1\/60 {
  background-color: rgb(219 166 29 / 0.6);
}

.bg-valueDrawer-ylw1\/70 {
  background-color: rgb(219 166 29 / 0.7);
}

.bg-valueDrawer-ylw1\/75 {
  background-color: rgb(219 166 29 / 0.75);
}

.bg-valueDrawer-ylw1\/80 {
  background-color: rgb(219 166 29 / 0.8);
}

.bg-valueDrawer-ylw1\/90 {
  background-color: rgb(219 166 29 / 0.9);
}

.bg-valueDrawer-ylw1\/95 {
  background-color: rgb(219 166 29 / 0.95);
}

.bg-violet-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(237 233 254 / var(--tw-bg-opacity));
}

.bg-violet-100\/0 {
  background-color: rgb(237 233 254 / 0);
}

.bg-violet-100\/10 {
  background-color: rgb(237 233 254 / 0.1);
}

.bg-violet-100\/100 {
  background-color: rgb(237 233 254 / 1);
}

.bg-violet-100\/20 {
  background-color: rgb(237 233 254 / 0.2);
}

.bg-violet-100\/25 {
  background-color: rgb(237 233 254 / 0.25);
}

.bg-violet-100\/30 {
  background-color: rgb(237 233 254 / 0.3);
}

.bg-violet-100\/40 {
  background-color: rgb(237 233 254 / 0.4);
}

.bg-violet-100\/5 {
  background-color: rgb(237 233 254 / 0.05);
}

.bg-violet-100\/50 {
  background-color: rgb(237 233 254 / 0.5);
}

.bg-violet-100\/60 {
  background-color: rgb(237 233 254 / 0.6);
}

.bg-violet-100\/70 {
  background-color: rgb(237 233 254 / 0.7);
}

.bg-violet-100\/75 {
  background-color: rgb(237 233 254 / 0.75);
}

.bg-violet-100\/80 {
  background-color: rgb(237 233 254 / 0.8);
}

.bg-violet-100\/90 {
  background-color: rgb(237 233 254 / 0.9);
}

.bg-violet-100\/95 {
  background-color: rgb(237 233 254 / 0.95);
}

.bg-violet-200 {
  --tw-bg-opacity: 1;
  background-color: rgb(221 214 254 / var(--tw-bg-opacity));
}

.bg-violet-200\/0 {
  background-color: rgb(221 214 254 / 0);
}

.bg-violet-200\/10 {
  background-color: rgb(221 214 254 / 0.1);
}

.bg-violet-200\/100 {
  background-color: rgb(221 214 254 / 1);
}

.bg-violet-200\/20 {
  background-color: rgb(221 214 254 / 0.2);
}

.bg-violet-200\/25 {
  background-color: rgb(221 214 254 / 0.25);
}

.bg-violet-200\/30 {
  background-color: rgb(221 214 254 / 0.3);
}

.bg-violet-200\/40 {
  background-color: rgb(221 214 254 / 0.4);
}

.bg-violet-200\/5 {
  background-color: rgb(221 214 254 / 0.05);
}

.bg-violet-200\/50 {
  background-color: rgb(221 214 254 / 0.5);
}

.bg-violet-200\/60 {
  background-color: rgb(221 214 254 / 0.6);
}

.bg-violet-200\/70 {
  background-color: rgb(221 214 254 / 0.7);
}

.bg-violet-200\/75 {
  background-color: rgb(221 214 254 / 0.75);
}

.bg-violet-200\/80 {
  background-color: rgb(221 214 254 / 0.8);
}

.bg-violet-200\/90 {
  background-color: rgb(221 214 254 / 0.9);
}

.bg-violet-200\/95 {
  background-color: rgb(221 214 254 / 0.95);
}

.bg-violet-300 {
  --tw-bg-opacity: 1;
  background-color: rgb(196 181 253 / var(--tw-bg-opacity));
}

.bg-violet-300\/0 {
  background-color: rgb(196 181 253 / 0);
}

.bg-violet-300\/10 {
  background-color: rgb(196 181 253 / 0.1);
}

.bg-violet-300\/100 {
  background-color: rgb(196 181 253 / 1);
}

.bg-violet-300\/20 {
  background-color: rgb(196 181 253 / 0.2);
}

.bg-violet-300\/25 {
  background-color: rgb(196 181 253 / 0.25);
}

.bg-violet-300\/30 {
  background-color: rgb(196 181 253 / 0.3);
}

.bg-violet-300\/40 {
  background-color: rgb(196 181 253 / 0.4);
}

.bg-violet-300\/5 {
  background-color: rgb(196 181 253 / 0.05);
}

.bg-violet-300\/50 {
  background-color: rgb(196 181 253 / 0.5);
}

.bg-violet-300\/60 {
  background-color: rgb(196 181 253 / 0.6);
}

.bg-violet-300\/70 {
  background-color: rgb(196 181 253 / 0.7);
}

.bg-violet-300\/75 {
  background-color: rgb(196 181 253 / 0.75);
}

.bg-violet-300\/80 {
  background-color: rgb(196 181 253 / 0.8);
}

.bg-violet-300\/90 {
  background-color: rgb(196 181 253 / 0.9);
}

.bg-violet-300\/95 {
  background-color: rgb(196 181 253 / 0.95);
}

.bg-violet-400 {
  --tw-bg-opacity: 1;
  background-color: rgb(167 139 250 / var(--tw-bg-opacity));
}

.bg-violet-400\/0 {
  background-color: rgb(167 139 250 / 0);
}

.bg-violet-400\/10 {
  background-color: rgb(167 139 250 / 0.1);
}

.bg-violet-400\/100 {
  background-color: rgb(167 139 250 / 1);
}

.bg-violet-400\/20 {
  background-color: rgb(167 139 250 / 0.2);
}

.bg-violet-400\/25 {
  background-color: rgb(167 139 250 / 0.25);
}

.bg-violet-400\/30 {
  background-color: rgb(167 139 250 / 0.3);
}

.bg-violet-400\/40 {
  background-color: rgb(167 139 250 / 0.4);
}

.bg-violet-400\/5 {
  background-color: rgb(167 139 250 / 0.05);
}

.bg-violet-400\/50 {
  background-color: rgb(167 139 250 / 0.5);
}

.bg-violet-400\/60 {
  background-color: rgb(167 139 250 / 0.6);
}

.bg-violet-400\/70 {
  background-color: rgb(167 139 250 / 0.7);
}

.bg-violet-400\/75 {
  background-color: rgb(167 139 250 / 0.75);
}

.bg-violet-400\/80 {
  background-color: rgb(167 139 250 / 0.8);
}

.bg-violet-400\/90 {
  background-color: rgb(167 139 250 / 0.9);
}

.bg-violet-400\/95 {
  background-color: rgb(167 139 250 / 0.95);
}

.bg-violet-50 {
  --tw-bg-opacity: 1;
  background-color: rgb(245 243 255 / var(--tw-bg-opacity));
}

.bg-violet-50\/0 {
  background-color: rgb(245 243 255 / 0);
}

.bg-violet-50\/10 {
  background-color: rgb(245 243 255 / 0.1);
}

.bg-violet-50\/100 {
  background-color: rgb(245 243 255 / 1);
}

.bg-violet-50\/20 {
  background-color: rgb(245 243 255 / 0.2);
}

.bg-violet-50\/25 {
  background-color: rgb(245 243 255 / 0.25);
}

.bg-violet-50\/30 {
  background-color: rgb(245 243 255 / 0.3);
}

.bg-violet-50\/40 {
  background-color: rgb(245 243 255 / 0.4);
}

.bg-violet-50\/5 {
  background-color: rgb(245 243 255 / 0.05);
}

.bg-violet-50\/50 {
  background-color: rgb(245 243 255 / 0.5);
}

.bg-violet-50\/60 {
  background-color: rgb(245 243 255 / 0.6);
}

.bg-violet-50\/70 {
  background-color: rgb(245 243 255 / 0.7);
}

.bg-violet-50\/75 {
  background-color: rgb(245 243 255 / 0.75);
}

.bg-violet-50\/80 {
  background-color: rgb(245 243 255 / 0.8);
}

.bg-violet-50\/90 {
  background-color: rgb(245 243 255 / 0.9);
}

.bg-violet-50\/95 {
  background-color: rgb(245 243 255 / 0.95);
}

.bg-violet-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(139 92 246 / var(--tw-bg-opacity));
}

.bg-violet-500\/0 {
  background-color: rgb(139 92 246 / 0);
}

.bg-violet-500\/10 {
  background-color: rgb(139 92 246 / 0.1);
}

.bg-violet-500\/100 {
  background-color: rgb(139 92 246 / 1);
}

.bg-violet-500\/20 {
  background-color: rgb(139 92 246 / 0.2);
}

.bg-violet-500\/25 {
  background-color: rgb(139 92 246 / 0.25);
}

.bg-violet-500\/30 {
  background-color: rgb(139 92 246 / 0.3);
}

.bg-violet-500\/40 {
  background-color: rgb(139 92 246 / 0.4);
}

.bg-violet-500\/5 {
  background-color: rgb(139 92 246 / 0.05);
}

.bg-violet-500\/50 {
  background-color: rgb(139 92 246 / 0.5);
}

.bg-violet-500\/60 {
  background-color: rgb(139 92 246 / 0.6);
}

.bg-violet-500\/70 {
  background-color: rgb(139 92 246 / 0.7);
}

.bg-violet-500\/75 {
  background-color: rgb(139 92 246 / 0.75);
}

.bg-violet-500\/80 {
  background-color: rgb(139 92 246 / 0.8);
}

.bg-violet-500\/90 {
  background-color: rgb(139 92 246 / 0.9);
}

.bg-violet-500\/95 {
  background-color: rgb(139 92 246 / 0.95);
}

.bg-violet-600 {
  --tw-bg-opacity: 1;
  background-color: rgb(124 58 237 / var(--tw-bg-opacity));
}

.bg-violet-600\/0 {
  background-color: rgb(124 58 237 / 0);
}

.bg-violet-600\/10 {
  background-color: rgb(124 58 237 / 0.1);
}

.bg-violet-600\/100 {
  background-color: rgb(124 58 237 / 1);
}

.bg-violet-600\/20 {
  background-color: rgb(124 58 237 / 0.2);
}

.bg-violet-600\/25 {
  background-color: rgb(124 58 237 / 0.25);
}

.bg-violet-600\/30 {
  background-color: rgb(124 58 237 / 0.3);
}

.bg-violet-600\/40 {
  background-color: rgb(124 58 237 / 0.4);
}

.bg-violet-600\/5 {
  background-color: rgb(124 58 237 / 0.05);
}

.bg-violet-600\/50 {
  background-color: rgb(124 58 237 / 0.5);
}

.bg-violet-600\/60 {
  background-color: rgb(124 58 237 / 0.6);
}

.bg-violet-600\/70 {
  background-color: rgb(124 58 237 / 0.7);
}

.bg-violet-600\/75 {
  background-color: rgb(124 58 237 / 0.75);
}

.bg-violet-600\/80 {
  background-color: rgb(124 58 237 / 0.8);
}

.bg-violet-600\/90 {
  background-color: rgb(124 58 237 / 0.9);
}

.bg-violet-600\/95 {
  background-color: rgb(124 58 237 / 0.95);
}

.bg-violet-700 {
  --tw-bg-opacity: 1;
  background-color: rgb(109 40 217 / var(--tw-bg-opacity));
}

.bg-violet-700\/0 {
  background-color: rgb(109 40 217 / 0);
}

.bg-violet-700\/10 {
  background-color: rgb(109 40 217 / 0.1);
}

.bg-violet-700\/100 {
  background-color: rgb(109 40 217 / 1);
}

.bg-violet-700\/20 {
  background-color: rgb(109 40 217 / 0.2);
}

.bg-violet-700\/25 {
  background-color: rgb(109 40 217 / 0.25);
}

.bg-violet-700\/30 {
  background-color: rgb(109 40 217 / 0.3);
}

.bg-violet-700\/40 {
  background-color: rgb(109 40 217 / 0.4);
}

.bg-violet-700\/5 {
  background-color: rgb(109 40 217 / 0.05);
}

.bg-violet-700\/50 {
  background-color: rgb(109 40 217 / 0.5);
}

.bg-violet-700\/60 {
  background-color: rgb(109 40 217 / 0.6);
}

.bg-violet-700\/70 {
  background-color: rgb(109 40 217 / 0.7);
}

.bg-violet-700\/75 {
  background-color: rgb(109 40 217 / 0.75);
}

.bg-violet-700\/80 {
  background-color: rgb(109 40 217 / 0.8);
}

.bg-violet-700\/90 {
  background-color: rgb(109 40 217 / 0.9);
}

.bg-violet-700\/95 {
  background-color: rgb(109 40 217 / 0.95);
}

.bg-violet-800 {
  --tw-bg-opacity: 1;
  background-color: rgb(91 33 182 / var(--tw-bg-opacity));
}

.bg-violet-800\/0 {
  background-color: rgb(91 33 182 / 0);
}

.bg-violet-800\/10 {
  background-color: rgb(91 33 182 / 0.1);
}

.bg-violet-800\/100 {
  background-color: rgb(91 33 182 / 1);
}

.bg-violet-800\/20 {
  background-color: rgb(91 33 182 / 0.2);
}

.bg-violet-800\/25 {
  background-color: rgb(91 33 182 / 0.25);
}

.bg-violet-800\/30 {
  background-color: rgb(91 33 182 / 0.3);
}

.bg-violet-800\/40 {
  background-color: rgb(91 33 182 / 0.4);
}

.bg-violet-800\/5 {
  background-color: rgb(91 33 182 / 0.05);
}

.bg-violet-800\/50 {
  background-color: rgb(91 33 182 / 0.5);
}

.bg-violet-800\/60 {
  background-color: rgb(91 33 182 / 0.6);
}

.bg-violet-800\/70 {
  background-color: rgb(91 33 182 / 0.7);
}

.bg-violet-800\/75 {
  background-color: rgb(91 33 182 / 0.75);
}

.bg-violet-800\/80 {
  background-color: rgb(91 33 182 / 0.8);
}

.bg-violet-800\/90 {
  background-color: rgb(91 33 182 / 0.9);
}

.bg-violet-800\/95 {
  background-color: rgb(91 33 182 / 0.95);
}

.bg-violet-900 {
  --tw-bg-opacity: 1;
  background-color: rgb(76 29 149 / var(--tw-bg-opacity));
}

.bg-violet-900\/0 {
  background-color: rgb(76 29 149 / 0);
}

.bg-violet-900\/10 {
  background-color: rgb(76 29 149 / 0.1);
}

.bg-violet-900\/100 {
  background-color: rgb(76 29 149 / 1);
}

.bg-violet-900\/20 {
  background-color: rgb(76 29 149 / 0.2);
}

.bg-violet-900\/25 {
  background-color: rgb(76 29 149 / 0.25);
}

.bg-violet-900\/30 {
  background-color: rgb(76 29 149 / 0.3);
}

.bg-violet-900\/40 {
  background-color: rgb(76 29 149 / 0.4);
}

.bg-violet-900\/5 {
  background-color: rgb(76 29 149 / 0.05);
}

.bg-violet-900\/50 {
  background-color: rgb(76 29 149 / 0.5);
}

.bg-violet-900\/60 {
  background-color: rgb(76 29 149 / 0.6);
}

.bg-violet-900\/70 {
  background-color: rgb(76 29 149 / 0.7);
}

.bg-violet-900\/75 {
  background-color: rgb(76 29 149 / 0.75);
}

.bg-violet-900\/80 {
  background-color: rgb(76 29 149 / 0.8);
}

.bg-violet-900\/90 {
  background-color: rgb(76 29 149 / 0.9);
}

.bg-violet-900\/95 {
  background-color: rgb(76 29 149 / 0.95);
}

.bg-violet-950 {
  --tw-bg-opacity: 1;
  background-color: rgb(46 16 101 / var(--tw-bg-opacity));
}

.bg-violet-950\/0 {
  background-color: rgb(46 16 101 / 0);
}

.bg-violet-950\/10 {
  background-color: rgb(46 16 101 / 0.1);
}

.bg-violet-950\/100 {
  background-color: rgb(46 16 101 / 1);
}

.bg-violet-950\/20 {
  background-color: rgb(46 16 101 / 0.2);
}

.bg-violet-950\/25 {
  background-color: rgb(46 16 101 / 0.25);
}

.bg-violet-950\/30 {
  background-color: rgb(46 16 101 / 0.3);
}

.bg-violet-950\/40 {
  background-color: rgb(46 16 101 / 0.4);
}

.bg-violet-950\/5 {
  background-color: rgb(46 16 101 / 0.05);
}

.bg-violet-950\/50 {
  background-color: rgb(46 16 101 / 0.5);
}

.bg-violet-950\/60 {
  background-color: rgb(46 16 101 / 0.6);
}

.bg-violet-950\/70 {
  background-color: rgb(46 16 101 / 0.7);
}

.bg-violet-950\/75 {
  background-color: rgb(46 16 101 / 0.75);
}

.bg-violet-950\/80 {
  background-color: rgb(46 16 101 / 0.8);
}

.bg-violet-950\/90 {
  background-color: rgb(46 16 101 / 0.9);
}

.bg-violet-950\/95 {
  background-color: rgb(46 16 101 / 0.95);
}

.bg-wh {
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity));
}

.bg-wh\/0 {
  background-color: rgb(255 255 255 / 0);
}

.bg-wh\/10 {
  background-color: rgb(255 255 255 / 0.1);
}

.bg-wh\/100 {
  background-color: rgb(255 255 255 / 1);
}

.bg-wh\/20 {
  background-color: rgb(255 255 255 / 0.2);
}

.bg-wh\/25 {
  background-color: rgb(255 255 255 / 0.25);
}

.bg-wh\/30 {
  background-color: rgb(255 255 255 / 0.3);
}

.bg-wh\/40 {
  background-color: rgb(255 255 255 / 0.4);
}

.bg-wh\/5 {
  background-color: rgb(255 255 255 / 0.05);
}

.bg-wh\/50 {
  background-color: rgb(255 255 255 / 0.5);
}

.bg-wh\/60 {
  background-color: rgb(255 255 255 / 0.6);
}

.bg-wh\/70 {
  background-color: rgb(255 255 255 / 0.7);
}

.bg-wh\/75 {
  background-color: rgb(255 255 255 / 0.75);
}

.bg-wh\/80 {
  background-color: rgb(255 255 255 / 0.8);
}

.bg-wh\/90 {
  background-color: rgb(255 255 255 / 0.9);
}

.bg-wh\/95 {
  background-color: rgb(255 255 255 / 0.95);
}

.bg-whAlpha-25 {
  background-color: rgba(255, 255, 255, 0.25);
}

.bg-whAlpha-25\/0 {
  background-color: rgba(255, 255, 255, 0);
}

.bg-whAlpha-25\/10 {
  background-color: rgba(255, 255, 255, 0.1);
}

.bg-whAlpha-25\/100 {
  background-color: rgba(255, 255, 255, 1);
}

.bg-whAlpha-25\/20 {
  background-color: rgba(255, 255, 255, 0.2);
}

.bg-whAlpha-25\/25 {
  background-color: rgba(255, 255, 255, 0.25);
}

.bg-whAlpha-25\/30 {
  background-color: rgba(255, 255, 255, 0.3);
}

.bg-whAlpha-25\/40 {
  background-color: rgba(255, 255, 255, 0.4);
}

.bg-whAlpha-25\/5 {
  background-color: rgba(255, 255, 255, 0.05);
}

.bg-whAlpha-25\/50 {
  background-color: rgba(255, 255, 255, 0.5);
}

.bg-whAlpha-25\/60 {
  background-color: rgba(255, 255, 255, 0.6);
}

.bg-whAlpha-25\/70 {
  background-color: rgba(255, 255, 255, 0.7);
}

.bg-whAlpha-25\/75 {
  background-color: rgba(255, 255, 255, 0.75);
}

.bg-whAlpha-25\/80 {
  background-color: rgba(255, 255, 255, 0.8);
}

.bg-whAlpha-25\/90 {
  background-color: rgba(255, 255, 255, 0.9);
}

.bg-whAlpha-25\/95 {
  background-color: rgba(255, 255, 255, 0.95);
}

.bg-whAlpha-50 {
  background-color: rgba(255, 255, 255, 0.5);
}

.bg-whAlpha-50\/0 {
  background-color: rgba(255, 255, 255, 0);
}

.bg-whAlpha-50\/10 {
  background-color: rgba(255, 255, 255, 0.1);
}

.bg-whAlpha-50\/100 {
  background-color: rgba(255, 255, 255, 1);
}

.bg-whAlpha-50\/20 {
  background-color: rgba(255, 255, 255, 0.2);
}

.bg-whAlpha-50\/25 {
  background-color: rgba(255, 255, 255, 0.25);
}

.bg-whAlpha-50\/30 {
  background-color: rgba(255, 255, 255, 0.3);
}

.bg-whAlpha-50\/40 {
  background-color: rgba(255, 255, 255, 0.4);
}

.bg-whAlpha-50\/5 {
  background-color: rgba(255, 255, 255, 0.05);
}

.bg-whAlpha-50\/50 {
  background-color: rgba(255, 255, 255, 0.5);
}

.bg-whAlpha-50\/60 {
  background-color: rgba(255, 255, 255, 0.6);
}

.bg-whAlpha-50\/70 {
  background-color: rgba(255, 255, 255, 0.7);
}

.bg-whAlpha-50\/75 {
  background-color: rgba(255, 255, 255, 0.75);
}

.bg-whAlpha-50\/80 {
  background-color: rgba(255, 255, 255, 0.8);
}

.bg-whAlpha-50\/90 {
  background-color: rgba(255, 255, 255, 0.9);
}

.bg-whAlpha-50\/95 {
  background-color: rgba(255, 255, 255, 0.95);
}

.bg-whAlpha-75 {
  background-color: rgba(255, 255, 255, 0.75);
}

.bg-whAlpha-75\/0 {
  background-color: rgba(255, 255, 255, 0);
}

.bg-whAlpha-75\/10 {
  background-color: rgba(255, 255, 255, 0.1);
}

.bg-whAlpha-75\/100 {
  background-color: rgba(255, 255, 255, 1);
}

.bg-whAlpha-75\/20 {
  background-color: rgba(255, 255, 255, 0.2);
}

.bg-whAlpha-75\/25 {
  background-color: rgba(255, 255, 255, 0.25);
}

.bg-whAlpha-75\/30 {
  background-color: rgba(255, 255, 255, 0.3);
}

.bg-whAlpha-75\/40 {
  background-color: rgba(255, 255, 255, 0.4);
}

.bg-whAlpha-75\/5 {
  background-color: rgba(255, 255, 255, 0.05);
}

.bg-whAlpha-75\/50 {
  background-color: rgba(255, 255, 255, 0.5);
}

.bg-whAlpha-75\/60 {
  background-color: rgba(255, 255, 255, 0.6);
}

.bg-whAlpha-75\/70 {
  background-color: rgba(255, 255, 255, 0.7);
}

.bg-whAlpha-75\/75 {
  background-color: rgba(255, 255, 255, 0.75);
}

.bg-whAlpha-75\/80 {
  background-color: rgba(255, 255, 255, 0.8);
}

.bg-whAlpha-75\/90 {
  background-color: rgba(255, 255, 255, 0.9);
}

.bg-whAlpha-75\/95 {
  background-color: rgba(255, 255, 255, 0.95);
}

.bg-white {
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity));
}

.bg-white\/0 {
  background-color: rgb(255 255 255 / 0);
}

.bg-white\/10 {
  background-color: rgb(255 255 255 / 0.1);
}

.bg-white\/100 {
  background-color: rgb(255 255 255 / 1);
}

.bg-white\/20 {
  background-color: rgb(255 255 255 / 0.2);
}

.bg-white\/25 {
  background-color: rgb(255 255 255 / 0.25);
}

.bg-white\/30 {
  background-color: rgb(255 255 255 / 0.3);
}

.bg-white\/40 {
  background-color: rgb(255 255 255 / 0.4);
}

.bg-white\/5 {
  background-color: rgb(255 255 255 / 0.05);
}

.bg-white\/50 {
  background-color: rgb(255 255 255 / 0.5);
}

.bg-white\/60 {
  background-color: rgb(255 255 255 / 0.6);
}

.bg-white\/70 {
  background-color: rgb(255 255 255 / 0.7);
}

.bg-white\/75 {
  background-color: rgb(255 255 255 / 0.75);
}

.bg-white\/80 {
  background-color: rgb(255 255 255 / 0.8);
}

.bg-white\/90 {
  background-color: rgb(255 255 255 / 0.9);
}

.bg-white\/95 {
  background-color: rgb(255 255 255 / 0.95);
}

.bg-yellow-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(254 249 195 / var(--tw-bg-opacity));
}

.bg-yellow-100\/0 {
  background-color: rgb(254 249 195 / 0);
}

.bg-yellow-100\/10 {
  background-color: rgb(254 249 195 / 0.1);
}

.bg-yellow-100\/100 {
  background-color: rgb(254 249 195 / 1);
}

.bg-yellow-100\/20 {
  background-color: rgb(254 249 195 / 0.2);
}

.bg-yellow-100\/25 {
  background-color: rgb(254 249 195 / 0.25);
}

.bg-yellow-100\/30 {
  background-color: rgb(254 249 195 / 0.3);
}

.bg-yellow-100\/40 {
  background-color: rgb(254 249 195 / 0.4);
}

.bg-yellow-100\/5 {
  background-color: rgb(254 249 195 / 0.05);
}

.bg-yellow-100\/50 {
  background-color: rgb(254 249 195 / 0.5);
}

.bg-yellow-100\/60 {
  background-color: rgb(254 249 195 / 0.6);
}

.bg-yellow-100\/70 {
  background-color: rgb(254 249 195 / 0.7);
}

.bg-yellow-100\/75 {
  background-color: rgb(254 249 195 / 0.75);
}

.bg-yellow-100\/80 {
  background-color: rgb(254 249 195 / 0.8);
}

.bg-yellow-100\/90 {
  background-color: rgb(254 249 195 / 0.9);
}

.bg-yellow-100\/95 {
  background-color: rgb(254 249 195 / 0.95);
}

.bg-yellow-200 {
  --tw-bg-opacity: 1;
  background-color: rgb(254 240 138 / var(--tw-bg-opacity));
}

.bg-yellow-200\/0 {
  background-color: rgb(254 240 138 / 0);
}

.bg-yellow-200\/10 {
  background-color: rgb(254 240 138 / 0.1);
}

.bg-yellow-200\/100 {
  background-color: rgb(254 240 138 / 1);
}

.bg-yellow-200\/20 {
  background-color: rgb(254 240 138 / 0.2);
}

.bg-yellow-200\/25 {
  background-color: rgb(254 240 138 / 0.25);
}

.bg-yellow-200\/30 {
  background-color: rgb(254 240 138 / 0.3);
}

.bg-yellow-200\/40 {
  background-color: rgb(254 240 138 / 0.4);
}

.bg-yellow-200\/5 {
  background-color: rgb(254 240 138 / 0.05);
}

.bg-yellow-200\/50 {
  background-color: rgb(254 240 138 / 0.5);
}

.bg-yellow-200\/60 {
  background-color: rgb(254 240 138 / 0.6);
}

.bg-yellow-200\/70 {
  background-color: rgb(254 240 138 / 0.7);
}

.bg-yellow-200\/75 {
  background-color: rgb(254 240 138 / 0.75);
}

.bg-yellow-200\/80 {
  background-color: rgb(254 240 138 / 0.8);
}

.bg-yellow-200\/90 {
  background-color: rgb(254 240 138 / 0.9);
}

.bg-yellow-200\/95 {
  background-color: rgb(254 240 138 / 0.95);
}

.bg-yellow-300 {
  --tw-bg-opacity: 1;
  background-color: rgb(253 224 71 / var(--tw-bg-opacity));
}

.bg-yellow-300\/0 {
  background-color: rgb(253 224 71 / 0);
}

.bg-yellow-300\/10 {
  background-color: rgb(253 224 71 / 0.1);
}

.bg-yellow-300\/100 {
  background-color: rgb(253 224 71 / 1);
}

.bg-yellow-300\/20 {
  background-color: rgb(253 224 71 / 0.2);
}

.bg-yellow-300\/25 {
  background-color: rgb(253 224 71 / 0.25);
}

.bg-yellow-300\/30 {
  background-color: rgb(253 224 71 / 0.3);
}

.bg-yellow-300\/40 {
  background-color: rgb(253 224 71 / 0.4);
}

.bg-yellow-300\/5 {
  background-color: rgb(253 224 71 / 0.05);
}

.bg-yellow-300\/50 {
  background-color: rgb(253 224 71 / 0.5);
}

.bg-yellow-300\/60 {
  background-color: rgb(253 224 71 / 0.6);
}

.bg-yellow-300\/70 {
  background-color: rgb(253 224 71 / 0.7);
}

.bg-yellow-300\/75 {
  background-color: rgb(253 224 71 / 0.75);
}

.bg-yellow-300\/80 {
  background-color: rgb(253 224 71 / 0.8);
}

.bg-yellow-300\/90 {
  background-color: rgb(253 224 71 / 0.9);
}

.bg-yellow-300\/95 {
  background-color: rgb(253 224 71 / 0.95);
}

.bg-yellow-400 {
  --tw-bg-opacity: 1;
  background-color: rgb(250 204 21 / var(--tw-bg-opacity));
}

.bg-yellow-400\/0 {
  background-color: rgb(250 204 21 / 0);
}

.bg-yellow-400\/10 {
  background-color: rgb(250 204 21 / 0.1);
}

.bg-yellow-400\/100 {
  background-color: rgb(250 204 21 / 1);
}

.bg-yellow-400\/20 {
  background-color: rgb(250 204 21 / 0.2);
}

.bg-yellow-400\/25 {
  background-color: rgb(250 204 21 / 0.25);
}

.bg-yellow-400\/30 {
  background-color: rgb(250 204 21 / 0.3);
}

.bg-yellow-400\/40 {
  background-color: rgb(250 204 21 / 0.4);
}

.bg-yellow-400\/5 {
  background-color: rgb(250 204 21 / 0.05);
}

.bg-yellow-400\/50 {
  background-color: rgb(250 204 21 / 0.5);
}

.bg-yellow-400\/60 {
  background-color: rgb(250 204 21 / 0.6);
}

.bg-yellow-400\/70 {
  background-color: rgb(250 204 21 / 0.7);
}

.bg-yellow-400\/75 {
  background-color: rgb(250 204 21 / 0.75);
}

.bg-yellow-400\/80 {
  background-color: rgb(250 204 21 / 0.8);
}

.bg-yellow-400\/90 {
  background-color: rgb(250 204 21 / 0.9);
}

.bg-yellow-400\/95 {
  background-color: rgb(250 204 21 / 0.95);
}

.bg-yellow-50 {
  --tw-bg-opacity: 1;
  background-color: rgb(254 252 232 / var(--tw-bg-opacity));
}

.bg-yellow-50\/0 {
  background-color: rgb(254 252 232 / 0);
}

.bg-yellow-50\/10 {
  background-color: rgb(254 252 232 / 0.1);
}

.bg-yellow-50\/100 {
  background-color: rgb(254 252 232 / 1);
}

.bg-yellow-50\/20 {
  background-color: rgb(254 252 232 / 0.2);
}

.bg-yellow-50\/25 {
  background-color: rgb(254 252 232 / 0.25);
}

.bg-yellow-50\/30 {
  background-color: rgb(254 252 232 / 0.3);
}

.bg-yellow-50\/40 {
  background-color: rgb(254 252 232 / 0.4);
}

.bg-yellow-50\/5 {
  background-color: rgb(254 252 232 / 0.05);
}

.bg-yellow-50\/50 {
  background-color: rgb(254 252 232 / 0.5);
}

.bg-yellow-50\/60 {
  background-color: rgb(254 252 232 / 0.6);
}

.bg-yellow-50\/70 {
  background-color: rgb(254 252 232 / 0.7);
}

.bg-yellow-50\/75 {
  background-color: rgb(254 252 232 / 0.75);
}

.bg-yellow-50\/80 {
  background-color: rgb(254 252 232 / 0.8);
}

.bg-yellow-50\/90 {
  background-color: rgb(254 252 232 / 0.9);
}

.bg-yellow-50\/95 {
  background-color: rgb(254 252 232 / 0.95);
}

.bg-yellow-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(234 179 8 / var(--tw-bg-opacity));
}

.bg-yellow-500\/0 {
  background-color: rgb(234 179 8 / 0);
}

.bg-yellow-500\/10 {
  background-color: rgb(234 179 8 / 0.1);
}

.bg-yellow-500\/100 {
  background-color: rgb(234 179 8 / 1);
}

.bg-yellow-500\/20 {
  background-color: rgb(234 179 8 / 0.2);
}

.bg-yellow-500\/25 {
  background-color: rgb(234 179 8 / 0.25);
}

.bg-yellow-500\/30 {
  background-color: rgb(234 179 8 / 0.3);
}

.bg-yellow-500\/40 {
  background-color: rgb(234 179 8 / 0.4);
}

.bg-yellow-500\/5 {
  background-color: rgb(234 179 8 / 0.05);
}

.bg-yellow-500\/50 {
  background-color: rgb(234 179 8 / 0.5);
}

.bg-yellow-500\/60 {
  background-color: rgb(234 179 8 / 0.6);
}

.bg-yellow-500\/70 {
  background-color: rgb(234 179 8 / 0.7);
}

.bg-yellow-500\/75 {
  background-color: rgb(234 179 8 / 0.75);
}

.bg-yellow-500\/80 {
  background-color: rgb(234 179 8 / 0.8);
}

.bg-yellow-500\/90 {
  background-color: rgb(234 179 8 / 0.9);
}

.bg-yellow-500\/95 {
  background-color: rgb(234 179 8 / 0.95);
}

.bg-yellow-600 {
  --tw-bg-opacity: 1;
  background-color: rgb(202 138 4 / var(--tw-bg-opacity));
}

.bg-yellow-600\/0 {
  background-color: rgb(202 138 4 / 0);
}

.bg-yellow-600\/10 {
  background-color: rgb(202 138 4 / 0.1);
}

.bg-yellow-600\/100 {
  background-color: rgb(202 138 4 / 1);
}

.bg-yellow-600\/20 {
  background-color: rgb(202 138 4 / 0.2);
}

.bg-yellow-600\/25 {
  background-color: rgb(202 138 4 / 0.25);
}

.bg-yellow-600\/30 {
  background-color: rgb(202 138 4 / 0.3);
}

.bg-yellow-600\/40 {
  background-color: rgb(202 138 4 / 0.4);
}

.bg-yellow-600\/5 {
  background-color: rgb(202 138 4 / 0.05);
}

.bg-yellow-600\/50 {
  background-color: rgb(202 138 4 / 0.5);
}

.bg-yellow-600\/60 {
  background-color: rgb(202 138 4 / 0.6);
}

.bg-yellow-600\/70 {
  background-color: rgb(202 138 4 / 0.7);
}

.bg-yellow-600\/75 {
  background-color: rgb(202 138 4 / 0.75);
}

.bg-yellow-600\/80 {
  background-color: rgb(202 138 4 / 0.8);
}

.bg-yellow-600\/90 {
  background-color: rgb(202 138 4 / 0.9);
}

.bg-yellow-600\/95 {
  background-color: rgb(202 138 4 / 0.95);
}

.bg-yellow-700 {
  --tw-bg-opacity: 1;
  background-color: rgb(161 98 7 / var(--tw-bg-opacity));
}

.bg-yellow-700\/0 {
  background-color: rgb(161 98 7 / 0);
}

.bg-yellow-700\/10 {
  background-color: rgb(161 98 7 / 0.1);
}

.bg-yellow-700\/100 {
  background-color: rgb(161 98 7 / 1);
}

.bg-yellow-700\/20 {
  background-color: rgb(161 98 7 / 0.2);
}

.bg-yellow-700\/25 {
  background-color: rgb(161 98 7 / 0.25);
}

.bg-yellow-700\/30 {
  background-color: rgb(161 98 7 / 0.3);
}

.bg-yellow-700\/40 {
  background-color: rgb(161 98 7 / 0.4);
}

.bg-yellow-700\/5 {
  background-color: rgb(161 98 7 / 0.05);
}

.bg-yellow-700\/50 {
  background-color: rgb(161 98 7 / 0.5);
}

.bg-yellow-700\/60 {
  background-color: rgb(161 98 7 / 0.6);
}

.bg-yellow-700\/70 {
  background-color: rgb(161 98 7 / 0.7);
}

.bg-yellow-700\/75 {
  background-color: rgb(161 98 7 / 0.75);
}

.bg-yellow-700\/80 {
  background-color: rgb(161 98 7 / 0.8);
}

.bg-yellow-700\/90 {
  background-color: rgb(161 98 7 / 0.9);
}

.bg-yellow-700\/95 {
  background-color: rgb(161 98 7 / 0.95);
}

.bg-yellow-800 {
  --tw-bg-opacity: 1;
  background-color: rgb(133 77 14 / var(--tw-bg-opacity));
}

.bg-yellow-800\/0 {
  background-color: rgb(133 77 14 / 0);
}

.bg-yellow-800\/10 {
  background-color: rgb(133 77 14 / 0.1);
}

.bg-yellow-800\/100 {
  background-color: rgb(133 77 14 / 1);
}

.bg-yellow-800\/20 {
  background-color: rgb(133 77 14 / 0.2);
}

.bg-yellow-800\/25 {
  background-color: rgb(133 77 14 / 0.25);
}

.bg-yellow-800\/30 {
  background-color: rgb(133 77 14 / 0.3);
}

.bg-yellow-800\/40 {
  background-color: rgb(133 77 14 / 0.4);
}

.bg-yellow-800\/5 {
  background-color: rgb(133 77 14 / 0.05);
}

.bg-yellow-800\/50 {
  background-color: rgb(133 77 14 / 0.5);
}

.bg-yellow-800\/60 {
  background-color: rgb(133 77 14 / 0.6);
}

.bg-yellow-800\/70 {
  background-color: rgb(133 77 14 / 0.7);
}

.bg-yellow-800\/75 {
  background-color: rgb(133 77 14 / 0.75);
}

.bg-yellow-800\/80 {
  background-color: rgb(133 77 14 / 0.8);
}

.bg-yellow-800\/90 {
  background-color: rgb(133 77 14 / 0.9);
}

.bg-yellow-800\/95 {
  background-color: rgb(133 77 14 / 0.95);
}

.bg-yellow-900 {
  --tw-bg-opacity: 1;
  background-color: rgb(113 63 18 / var(--tw-bg-opacity));
}

.bg-yellow-900\/0 {
  background-color: rgb(113 63 18 / 0);
}

.bg-yellow-900\/10 {
  background-color: rgb(113 63 18 / 0.1);
}

.bg-yellow-900\/100 {
  background-color: rgb(113 63 18 / 1);
}

.bg-yellow-900\/20 {
  background-color: rgb(113 63 18 / 0.2);
}

.bg-yellow-900\/25 {
  background-color: rgb(113 63 18 / 0.25);
}

.bg-yellow-900\/30 {
  background-color: rgb(113 63 18 / 0.3);
}

.bg-yellow-900\/40 {
  background-color: rgb(113 63 18 / 0.4);
}

.bg-yellow-900\/5 {
  background-color: rgb(113 63 18 / 0.05);
}

.bg-yellow-900\/50 {
  background-color: rgb(113 63 18 / 0.5);
}

.bg-yellow-900\/60 {
  background-color: rgb(113 63 18 / 0.6);
}

.bg-yellow-900\/70 {
  background-color: rgb(113 63 18 / 0.7);
}

.bg-yellow-900\/75 {
  background-color: rgb(113 63 18 / 0.75);
}

.bg-yellow-900\/80 {
  background-color: rgb(113 63 18 / 0.8);
}

.bg-yellow-900\/90 {
  background-color: rgb(113 63 18 / 0.9);
}

.bg-yellow-900\/95 {
  background-color: rgb(113 63 18 / 0.95);
}

.bg-yellow-950 {
  --tw-bg-opacity: 1;
  background-color: rgb(66 32 6 / var(--tw-bg-opacity));
}

.bg-yellow-950\/0 {
  background-color: rgb(66 32 6 / 0);
}

.bg-yellow-950\/10 {
  background-color: rgb(66 32 6 / 0.1);
}

.bg-yellow-950\/100 {
  background-color: rgb(66 32 6 / 1);
}

.bg-yellow-950\/20 {
  background-color: rgb(66 32 6 / 0.2);
}

.bg-yellow-950\/25 {
  background-color: rgb(66 32 6 / 0.25);
}

.bg-yellow-950\/30 {
  background-color: rgb(66 32 6 / 0.3);
}

.bg-yellow-950\/40 {
  background-color: rgb(66 32 6 / 0.4);
}

.bg-yellow-950\/5 {
  background-color: rgb(66 32 6 / 0.05);
}

.bg-yellow-950\/50 {
  background-color: rgb(66 32 6 / 0.5);
}

.bg-yellow-950\/60 {
  background-color: rgb(66 32 6 / 0.6);
}

.bg-yellow-950\/70 {
  background-color: rgb(66 32 6 / 0.7);
}

.bg-yellow-950\/75 {
  background-color: rgb(66 32 6 / 0.75);
}

.bg-yellow-950\/80 {
  background-color: rgb(66 32 6 / 0.8);
}

.bg-yellow-950\/90 {
  background-color: rgb(66 32 6 / 0.9);
}

.bg-yellow-950\/95 {
  background-color: rgb(66 32 6 / 0.95);
}

.bg-zinc-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(244 244 245 / var(--tw-bg-opacity));
}

.bg-zinc-100\/0 {
  background-color: rgb(244 244 245 / 0);
}

.bg-zinc-100\/10 {
  background-color: rgb(244 244 245 / 0.1);
}

.bg-zinc-100\/100 {
  background-color: rgb(244 244 245 / 1);
}

.bg-zinc-100\/20 {
  background-color: rgb(244 244 245 / 0.2);
}

.bg-zinc-100\/25 {
  background-color: rgb(244 244 245 / 0.25);
}

.bg-zinc-100\/30 {
  background-color: rgb(244 244 245 / 0.3);
}

.bg-zinc-100\/40 {
  background-color: rgb(244 244 245 / 0.4);
}

.bg-zinc-100\/5 {
  background-color: rgb(244 244 245 / 0.05);
}

.bg-zinc-100\/50 {
  background-color: rgb(244 244 245 / 0.5);
}

.bg-zinc-100\/60 {
  background-color: rgb(244 244 245 / 0.6);
}

.bg-zinc-100\/70 {
  background-color: rgb(244 244 245 / 0.7);
}

.bg-zinc-100\/75 {
  background-color: rgb(244 244 245 / 0.75);
}

.bg-zinc-100\/80 {
  background-color: rgb(244 244 245 / 0.8);
}

.bg-zinc-100\/90 {
  background-color: rgb(244 244 245 / 0.9);
}

.bg-zinc-100\/95 {
  background-color: rgb(244 244 245 / 0.95);
}

.bg-zinc-200 {
  --tw-bg-opacity: 1;
  background-color: rgb(228 228 231 / var(--tw-bg-opacity));
}

.bg-zinc-200\/0 {
  background-color: rgb(228 228 231 / 0);
}

.bg-zinc-200\/10 {
  background-color: rgb(228 228 231 / 0.1);
}

.bg-zinc-200\/100 {
  background-color: rgb(228 228 231 / 1);
}

.bg-zinc-200\/20 {
  background-color: rgb(228 228 231 / 0.2);
}

.bg-zinc-200\/25 {
  background-color: rgb(228 228 231 / 0.25);
}

.bg-zinc-200\/30 {
  background-color: rgb(228 228 231 / 0.3);
}

.bg-zinc-200\/40 {
  background-color: rgb(228 228 231 / 0.4);
}

.bg-zinc-200\/5 {
  background-color: rgb(228 228 231 / 0.05);
}

.bg-zinc-200\/50 {
  background-color: rgb(228 228 231 / 0.5);
}

.bg-zinc-200\/60 {
  background-color: rgb(228 228 231 / 0.6);
}

.bg-zinc-200\/70 {
  background-color: rgb(228 228 231 / 0.7);
}

.bg-zinc-200\/75 {
  background-color: rgb(228 228 231 / 0.75);
}

.bg-zinc-200\/80 {
  background-color: rgb(228 228 231 / 0.8);
}

.bg-zinc-200\/90 {
  background-color: rgb(228 228 231 / 0.9);
}

.bg-zinc-200\/95 {
  background-color: rgb(228 228 231 / 0.95);
}

.bg-zinc-300 {
  --tw-bg-opacity: 1;
  background-color: rgb(212 212 216 / var(--tw-bg-opacity));
}

.bg-zinc-300\/0 {
  background-color: rgb(212 212 216 / 0);
}

.bg-zinc-300\/10 {
  background-color: rgb(212 212 216 / 0.1);
}

.bg-zinc-300\/100 {
  background-color: rgb(212 212 216 / 1);
}

.bg-zinc-300\/20 {
  background-color: rgb(212 212 216 / 0.2);
}

.bg-zinc-300\/25 {
  background-color: rgb(212 212 216 / 0.25);
}

.bg-zinc-300\/30 {
  background-color: rgb(212 212 216 / 0.3);
}

.bg-zinc-300\/40 {
  background-color: rgb(212 212 216 / 0.4);
}

.bg-zinc-300\/5 {
  background-color: rgb(212 212 216 / 0.05);
}

.bg-zinc-300\/50 {
  background-color: rgb(212 212 216 / 0.5);
}

.bg-zinc-300\/60 {
  background-color: rgb(212 212 216 / 0.6);
}

.bg-zinc-300\/70 {
  background-color: rgb(212 212 216 / 0.7);
}

.bg-zinc-300\/75 {
  background-color: rgb(212 212 216 / 0.75);
}

.bg-zinc-300\/80 {
  background-color: rgb(212 212 216 / 0.8);
}

.bg-zinc-300\/90 {
  background-color: rgb(212 212 216 / 0.9);
}

.bg-zinc-300\/95 {
  background-color: rgb(212 212 216 / 0.95);
}

.bg-zinc-400 {
  --tw-bg-opacity: 1;
  background-color: rgb(161 161 170 / var(--tw-bg-opacity));
}

.bg-zinc-400\/0 {
  background-color: rgb(161 161 170 / 0);
}

.bg-zinc-400\/10 {
  background-color: rgb(161 161 170 / 0.1);
}

.bg-zinc-400\/100 {
  background-color: rgb(161 161 170 / 1);
}

.bg-zinc-400\/20 {
  background-color: rgb(161 161 170 / 0.2);
}

.bg-zinc-400\/25 {
  background-color: rgb(161 161 170 / 0.25);
}

.bg-zinc-400\/30 {
  background-color: rgb(161 161 170 / 0.3);
}

.bg-zinc-400\/40 {
  background-color: rgb(161 161 170 / 0.4);
}

.bg-zinc-400\/5 {
  background-color: rgb(161 161 170 / 0.05);
}

.bg-zinc-400\/50 {
  background-color: rgb(161 161 170 / 0.5);
}

.bg-zinc-400\/60 {
  background-color: rgb(161 161 170 / 0.6);
}

.bg-zinc-400\/70 {
  background-color: rgb(161 161 170 / 0.7);
}

.bg-zinc-400\/75 {
  background-color: rgb(161 161 170 / 0.75);
}

.bg-zinc-400\/80 {
  background-color: rgb(161 161 170 / 0.8);
}

.bg-zinc-400\/90 {
  background-color: rgb(161 161 170 / 0.9);
}

.bg-zinc-400\/95 {
  background-color: rgb(161 161 170 / 0.95);
}

.bg-zinc-50 {
  --tw-bg-opacity: 1;
  background-color: rgb(250 250 250 / var(--tw-bg-opacity));
}

.bg-zinc-50\/0 {
  background-color: rgb(250 250 250 / 0);
}

.bg-zinc-50\/10 {
  background-color: rgb(250 250 250 / 0.1);
}

.bg-zinc-50\/100 {
  background-color: rgb(250 250 250 / 1);
}

.bg-zinc-50\/20 {
  background-color: rgb(250 250 250 / 0.2);
}

.bg-zinc-50\/25 {
  background-color: rgb(250 250 250 / 0.25);
}

.bg-zinc-50\/30 {
  background-color: rgb(250 250 250 / 0.3);
}

.bg-zinc-50\/40 {
  background-color: rgb(250 250 250 / 0.4);
}

.bg-zinc-50\/5 {
  background-color: rgb(250 250 250 / 0.05);
}

.bg-zinc-50\/50 {
  background-color: rgb(250 250 250 / 0.5);
}

.bg-zinc-50\/60 {
  background-color: rgb(250 250 250 / 0.6);
}

.bg-zinc-50\/70 {
  background-color: rgb(250 250 250 / 0.7);
}

.bg-zinc-50\/75 {
  background-color: rgb(250 250 250 / 0.75);
}

.bg-zinc-50\/80 {
  background-color: rgb(250 250 250 / 0.8);
}

.bg-zinc-50\/90 {
  background-color: rgb(250 250 250 / 0.9);
}

.bg-zinc-50\/95 {
  background-color: rgb(250 250 250 / 0.95);
}

.bg-zinc-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(113 113 122 / var(--tw-bg-opacity));
}

.bg-zinc-500\/0 {
  background-color: rgb(113 113 122 / 0);
}

.bg-zinc-500\/10 {
  background-color: rgb(113 113 122 / 0.1);
}

.bg-zinc-500\/100 {
  background-color: rgb(113 113 122 / 1);
}

.bg-zinc-500\/20 {
  background-color: rgb(113 113 122 / 0.2);
}

.bg-zinc-500\/25 {
  background-color: rgb(113 113 122 / 0.25);
}

.bg-zinc-500\/30 {
  background-color: rgb(113 113 122 / 0.3);
}

.bg-zinc-500\/40 {
  background-color: rgb(113 113 122 / 0.4);
}

.bg-zinc-500\/5 {
  background-color: rgb(113 113 122 / 0.05);
}

.bg-zinc-500\/50 {
  background-color: rgb(113 113 122 / 0.5);
}

.bg-zinc-500\/60 {
  background-color: rgb(113 113 122 / 0.6);
}

.bg-zinc-500\/70 {
  background-color: rgb(113 113 122 / 0.7);
}

.bg-zinc-500\/75 {
  background-color: rgb(113 113 122 / 0.75);
}

.bg-zinc-500\/80 {
  background-color: rgb(113 113 122 / 0.8);
}

.bg-zinc-500\/90 {
  background-color: rgb(113 113 122 / 0.9);
}

.bg-zinc-500\/95 {
  background-color: rgb(113 113 122 / 0.95);
}

.bg-zinc-600 {
  --tw-bg-opacity: 1;
  background-color: rgb(82 82 91 / var(--tw-bg-opacity));
}

.bg-zinc-600\/0 {
  background-color: rgb(82 82 91 / 0);
}

.bg-zinc-600\/10 {
  background-color: rgb(82 82 91 / 0.1);
}

.bg-zinc-600\/100 {
  background-color: rgb(82 82 91 / 1);
}

.bg-zinc-600\/20 {
  background-color: rgb(82 82 91 / 0.2);
}

.bg-zinc-600\/25 {
  background-color: rgb(82 82 91 / 0.25);
}

.bg-zinc-600\/30 {
  background-color: rgb(82 82 91 / 0.3);
}

.bg-zinc-600\/40 {
  background-color: rgb(82 82 91 / 0.4);
}

.bg-zinc-600\/5 {
  background-color: rgb(82 82 91 / 0.05);
}

.bg-zinc-600\/50 {
  background-color: rgb(82 82 91 / 0.5);
}

.bg-zinc-600\/60 {
  background-color: rgb(82 82 91 / 0.6);
}

.bg-zinc-600\/70 {
  background-color: rgb(82 82 91 / 0.7);
}

.bg-zinc-600\/75 {
  background-color: rgb(82 82 91 / 0.75);
}

.bg-zinc-600\/80 {
  background-color: rgb(82 82 91 / 0.8);
}

.bg-zinc-600\/90 {
  background-color: rgb(82 82 91 / 0.9);
}

.bg-zinc-600\/95 {
  background-color: rgb(82 82 91 / 0.95);
}

.bg-zinc-700 {
  --tw-bg-opacity: 1;
  background-color: rgb(63 63 70 / var(--tw-bg-opacity));
}

.bg-zinc-700\/0 {
  background-color: rgb(63 63 70 / 0);
}

.bg-zinc-700\/10 {
  background-color: rgb(63 63 70 / 0.1);
}

.bg-zinc-700\/100 {
  background-color: rgb(63 63 70 / 1);
}

.bg-zinc-700\/20 {
  background-color: rgb(63 63 70 / 0.2);
}

.bg-zinc-700\/25 {
  background-color: rgb(63 63 70 / 0.25);
}

.bg-zinc-700\/30 {
  background-color: rgb(63 63 70 / 0.3);
}

.bg-zinc-700\/40 {
  background-color: rgb(63 63 70 / 0.4);
}

.bg-zinc-700\/5 {
  background-color: rgb(63 63 70 / 0.05);
}

.bg-zinc-700\/50 {
  background-color: rgb(63 63 70 / 0.5);
}

.bg-zinc-700\/60 {
  background-color: rgb(63 63 70 / 0.6);
}

.bg-zinc-700\/70 {
  background-color: rgb(63 63 70 / 0.7);
}

.bg-zinc-700\/75 {
  background-color: rgb(63 63 70 / 0.75);
}

.bg-zinc-700\/80 {
  background-color: rgb(63 63 70 / 0.8);
}

.bg-zinc-700\/90 {
  background-color: rgb(63 63 70 / 0.9);
}

.bg-zinc-700\/95 {
  background-color: rgb(63 63 70 / 0.95);
}

.bg-zinc-800 {
  --tw-bg-opacity: 1;
  background-color: rgb(39 39 42 / var(--tw-bg-opacity));
}

.bg-zinc-800\/0 {
  background-color: rgb(39 39 42 / 0);
}

.bg-zinc-800\/10 {
  background-color: rgb(39 39 42 / 0.1);
}

.bg-zinc-800\/100 {
  background-color: rgb(39 39 42 / 1);
}

.bg-zinc-800\/20 {
  background-color: rgb(39 39 42 / 0.2);
}

.bg-zinc-800\/25 {
  background-color: rgb(39 39 42 / 0.25);
}

.bg-zinc-800\/30 {
  background-color: rgb(39 39 42 / 0.3);
}

.bg-zinc-800\/40 {
  background-color: rgb(39 39 42 / 0.4);
}

.bg-zinc-800\/5 {
  background-color: rgb(39 39 42 / 0.05);
}

.bg-zinc-800\/50 {
  background-color: rgb(39 39 42 / 0.5);
}

.bg-zinc-800\/60 {
  background-color: rgb(39 39 42 / 0.6);
}

.bg-zinc-800\/70 {
  background-color: rgb(39 39 42 / 0.7);
}

.bg-zinc-800\/75 {
  background-color: rgb(39 39 42 / 0.75);
}

.bg-zinc-800\/80 {
  background-color: rgb(39 39 42 / 0.8);
}

.bg-zinc-800\/90 {
  background-color: rgb(39 39 42 / 0.9);
}

.bg-zinc-800\/95 {
  background-color: rgb(39 39 42 / 0.95);
}

.bg-zinc-900 {
  --tw-bg-opacity: 1;
  background-color: rgb(24 24 27 / var(--tw-bg-opacity));
}

.bg-zinc-900\/0 {
  background-color: rgb(24 24 27 / 0);
}

.bg-zinc-900\/10 {
  background-color: rgb(24 24 27 / 0.1);
}

.bg-zinc-900\/100 {
  background-color: rgb(24 24 27 / 1);
}

.bg-zinc-900\/20 {
  background-color: rgb(24 24 27 / 0.2);
}

.bg-zinc-900\/25 {
  background-color: rgb(24 24 27 / 0.25);
}

.bg-zinc-900\/30 {
  background-color: rgb(24 24 27 / 0.3);
}

.bg-zinc-900\/40 {
  background-color: rgb(24 24 27 / 0.4);
}

.bg-zinc-900\/5 {
  background-color: rgb(24 24 27 / 0.05);
}

.bg-zinc-900\/50 {
  background-color: rgb(24 24 27 / 0.5);
}

.bg-zinc-900\/60 {
  background-color: rgb(24 24 27 / 0.6);
}

.bg-zinc-900\/70 {
  background-color: rgb(24 24 27 / 0.7);
}

.bg-zinc-900\/75 {
  background-color: rgb(24 24 27 / 0.75);
}

.bg-zinc-900\/80 {
  background-color: rgb(24 24 27 / 0.8);
}

.bg-zinc-900\/90 {
  background-color: rgb(24 24 27 / 0.9);
}

.bg-zinc-900\/95 {
  background-color: rgb(24 24 27 / 0.95);
}

.bg-zinc-950 {
  --tw-bg-opacity: 1;
  background-color: rgb(9 9 11 / var(--tw-bg-opacity));
}

.bg-zinc-950\/0 {
  background-color: rgb(9 9 11 / 0);
}

.bg-zinc-950\/10 {
  background-color: rgb(9 9 11 / 0.1);
}

.bg-zinc-950\/100 {
  background-color: rgb(9 9 11 / 1);
}

.bg-zinc-950\/20 {
  background-color: rgb(9 9 11 / 0.2);
}

.bg-zinc-950\/25 {
  background-color: rgb(9 9 11 / 0.25);
}

.bg-zinc-950\/30 {
  background-color: rgb(9 9 11 / 0.3);
}

.bg-zinc-950\/40 {
  background-color: rgb(9 9 11 / 0.4);
}

.bg-zinc-950\/5 {
  background-color: rgb(9 9 11 / 0.05);
}

.bg-zinc-950\/50 {
  background-color: rgb(9 9 11 / 0.5);
}

.bg-zinc-950\/60 {
  background-color: rgb(9 9 11 / 0.6);
}

.bg-zinc-950\/70 {
  background-color: rgb(9 9 11 / 0.7);
}

.bg-zinc-950\/75 {
  background-color: rgb(9 9 11 / 0.75);
}

.bg-zinc-950\/80 {
  background-color: rgb(9 9 11 / 0.8);
}

.bg-zinc-950\/90 {
  background-color: rgb(9 9 11 / 0.9);
}

.bg-zinc-950\/95 {
  background-color: rgb(9 9 11 / 0.95);
}

.bg-opacity-0 {
  --tw-bg-opacity: 0;
}

.bg-opacity-10 {
  --tw-bg-opacity: 0.1;
}

.bg-opacity-100 {
  --tw-bg-opacity: 1;
}

.bg-opacity-20 {
  --tw-bg-opacity: 0.2;
}

.bg-opacity-25 {
  --tw-bg-opacity: 0.25;
}

.bg-opacity-30 {
  --tw-bg-opacity: 0.3;
}

.bg-opacity-40 {
  --tw-bg-opacity: 0.4;
}

.bg-opacity-5 {
  --tw-bg-opacity: 0.05;
}

.bg-opacity-50 {
  --tw-bg-opacity: 0.5;
}

.bg-opacity-60 {
  --tw-bg-opacity: 0.6;
}

.bg-opacity-70 {
  --tw-bg-opacity: 0.7;
}

.bg-opacity-75 {
  --tw-bg-opacity: 0.75;
}

.bg-opacity-80 {
  --tw-bg-opacity: 0.8;
}

.bg-opacity-90 {
  --tw-bg-opacity: 0.9;
}

.bg-opacity-95 {
  --tw-bg-opacity: 0.95;
}

.bg-gradient-to-b {
  background-image: linear-gradient(to bottom, var(--tw-gradient-stops));
}

.bg-gradient-to-bl {
  background-image: linear-gradient(to bottom left, var(--tw-gradient-stops));
}

.bg-gradient-to-br {
  background-image: linear-gradient(to bottom right, var(--tw-gradient-stops));
}

.bg-gradient-to-l {
  background-image: linear-gradient(to left, var(--tw-gradient-stops));
}

.bg-gradient-to-r {
  background-image: linear-gradient(to right, var(--tw-gradient-stops));
}

.bg-gradient-to-t {
  background-image: linear-gradient(to top, var(--tw-gradient-stops));
}

.bg-gradient-to-tl {
  background-image: linear-gradient(to top left, var(--tw-gradient-stops));
}

.bg-gradient-to-tr {
  background-image: linear-gradient(to top right, var(--tw-gradient-stops));
}

.bg-none {
  background-image: none;
}

.bg-auto {
  background-size: auto;
}

.bg-contain {
  background-size: contain;
}

.bg-cover {
  background-size: cover;
}

.bg-fixed {
  background-attachment: fixed;
}

.bg-local {
  background-attachment: local;
}

.bg-scroll {
  background-attachment: scroll;
}

.bg-clip-border {
  background-clip: border-box;
}

.bg-clip-padding {
  background-clip: padding-box;
}

.bg-clip-content {
  background-clip: content-box;
}

.bg-clip-text {
  -webkit-background-clip: text;
  background-clip: text;
}

.bg-bottom {
  background-position: bottom;
}

.bg-center {
  background-position: center;
}

.bg-left {
  background-position: left;
}

.bg-left-bottom {
  background-position: left bottom;
}

.bg-left-top {
  background-position: left top;
}

.bg-right {
  background-position: right;
}

.bg-right-bottom {
  background-position: right bottom;
}

.bg-right-top {
  background-position: right top;
}

.bg-top {
  background-position: top;
}

.bg-repeat {
  background-repeat: repeat;
}

.bg-no-repeat {
  background-repeat: no-repeat;
}

.bg-repeat-x {
  background-repeat: repeat-x;
}

.bg-repeat-y {
  background-repeat: repeat-y;
}

.bg-repeat-round {
  background-repeat: round;
}

.bg-repeat-space {
  background-repeat: space;
}

.bg-origin-border {
  background-origin: border-box;
}

.bg-origin-padding {
  background-origin: padding-box;
}

.bg-origin-content {
  background-origin: content-box;
}

.fill-b2 {
  fill: #024a62;
}

.fill-bk {
  fill: #000;
}

.fill-wh {
  fill: #fff;
}

.stroke-inverse-b1 {
  stroke: #ffffff;
}

.p-1 {
  padding: 0.25rem;
}

.p-3 {
  padding: 0.75rem;
}

.px-0 {
  padding-left: 0px;
  padding-right: 0px;
}

.px-4 {
  padding-left: 1rem;
  padding-right: 1rem;
}

.px-6 {
  padding-left: 1.5rem;
  padding-right: 1.5rem;
}

.px-\[0\.64rem\] {
  padding-left: 0.64rem;
  padding-right: 0.64rem;
}

.py-0 {
  padding-top: 0px;
  padding-bottom: 0px;
}

.py-1 {
  padding-top: 0.25rem;
  padding-bottom: 0.25rem;
}

.py-2 {
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
}

.py-3 {
  padding-top: 0.75rem;
  padding-bottom: 0.75rem;
}

.py-\[0\.65rem\] {
  padding-top: 0.65rem;
  padding-bottom: 0.65rem;
}

.py-px {
  padding-top: 1px;
  padding-bottom: 1px;
}

.pl-\[0\.64rem\] {
  padding-left: 0.64rem;
}

.pr-6 {
  padding-right: 1.5rem;
}

.text-left {
  text-align: left;
}

.text-center {
  text-align: center;
}

.align-top {
  vertical-align: top;
}

.font-alt {
  font-family:
    var(--font-soehne),
    Times New Roman,
    serif;
}

.font-brand {
  font-family: var(--font-phantom-sans), 'Gap Sans', Helvetica, Arial, Roboto, sans-serif;
}

.font-crossbrand {
  font-family: var(--font-lato), Helvetica, Arial, sans-serif;
}

.text-2xl {
  font-size: 1.5rem;
  line-height: 2rem;
}

.text-\[0\.8rem\] {
  font-size: 0.8rem;
}

.text-\[75\%\] {
  font-size: 75%;
}

.text-\[81\.25\%\] {
  font-size: 81.25%;
}

.text-\[86\.7\%\] {
  font-size: 86.7%;
}

.text-sm {
  font-size: 0.875rem;
  line-height: 1.25rem;
}

.text-xs {
  font-size: 0.75rem;
  line-height: 1rem;
}

.font-bold {
  font-weight: 700;
}

.font-extrabold {
  font-weight: 800;
}

.font-light {
  font-weight: 300;
}

.font-medium {
  font-weight: 500;
}

.font-normal {
  font-weight: 400;
}

.font-semibold {
  font-weight: 600;
}

.uppercase {
  text-transform: uppercase;
}

.lowercase {
  text-transform: lowercase;
}

.italic {
  font-style: italic;
}

.tabular-nums {
  --tw-numeric-spacing: tabular-nums;
  font-variant-numeric: var(--tw-ordinal) var(--tw-slashed-zero) var(--tw-numeric-figure) var(--tw-numeric-spacing) var(--tw-numeric-fraction);
}

.text-\[\#5DBAC1\] {
  --tw-text-opacity: 1;
  color: rgb(93 186 193 / var(--tw-text-opacity));
}

.text-bk {
  --tw-text-opacity: 1;
  color: rgb(0 0 0 / var(--tw-text-opacity));
}

.text-g1 {
  --tw-text-opacity: 1;
  color: rgb(51 51 51 / var(--tw-text-opacity));
}

.text-g2 {
  --tw-text-opacity: 1;
  color: rgb(102 102 102 / var(--tw-text-opacity));
}

.text-inverse-b1 {
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}

.text-slate-900 {
  --tw-text-opacity: 1;
  color: rgb(15 23 42 / var(--tw-text-opacity));
}

.underline {
  text-decoration-line: underline;
}

.antialiased {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.opacity-100 {
  opacity: 1;
}

.opacity-\[\.88\] {
  opacity: 0.88;
}

.bg-blend-normal {
  background-blend-mode: normal;
}

.bg-blend-multiply {
  background-blend-mode: multiply;
}

.bg-blend-screen {
  background-blend-mode: screen;
}

.bg-blend-overlay {
  background-blend-mode: overlay;
}

.bg-blend-darken {
  background-blend-mode: darken;
}

.bg-blend-lighten {
  background-blend-mode: lighten;
}

.bg-blend-color-dodge {
  background-blend-mode: color-dodge;
}

.bg-blend-color-burn {
  background-blend-mode: color-burn;
}

.bg-blend-hard-light {
  background-blend-mode: hard-light;
}

.bg-blend-soft-light {
  background-blend-mode: soft-light;
}

.bg-blend-difference {
  background-blend-mode: difference;
}

.bg-blend-exclusion {
  background-blend-mode: exclusion;
}

.bg-blend-hue {
  background-blend-mode: hue;
}

.bg-blend-saturation {
  background-blend-mode: saturation;
}

.bg-blend-color {
  background-blend-mode: color;
}

.bg-blend-luminosity {
  background-blend-mode: luminosity;
}

.shadow {
  --tw-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 1px 3px 0 var(--tw-shadow-color), 0 1px 2px -1px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.outline {
  outline-style: solid;
}

.ring {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(3px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}

.blur {
  --tw-blur: blur(8px);
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia)
    var(--tw-drop-shadow);
}

.drop-shadow {
  --tw-drop-shadow: drop-shadow(0 1px 2px rgb(0 0 0 / 0.1)) drop-shadow(0 1px 1px rgb(0 0 0 / 0.06));
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia)
    var(--tw-drop-shadow);
}

.grayscale {
  --tw-grayscale: grayscale(100%);
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia)
    var(--tw-drop-shadow);
}

.invert {
  --tw-invert: invert(100%);
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia)
    var(--tw-drop-shadow);
}

.filter {
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia)
    var(--tw-drop-shadow);
}

.transition {
  transition-property:
    color,
    background-color,
    border-color,
    text-decoration-color,
    fill,
    stroke,
    opacity,
    box-shadow,
    transform,
    filter,
    -webkit-backdrop-filter;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;
  transition-property:
    color,
    background-color,
    border-color,
    text-decoration-color,
    fill,
    stroke,
    opacity,
    box-shadow,
    transform,
    filter,
    backdrop-filter,
    -webkit-backdrop-filter;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

.transition-all {
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

.transition-opacity {
  transition-property: opacity;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

.delay-0 {
  transition-delay: 0s;
}

.duration-200 {
  transition-duration: 200ms;
}

.duration-300 {
  transition-duration: 300ms;
}

.ease-in {
  transition-timing-function: cubic-bezier(0.4, 0, 1, 1);
}

.ease-in-out {
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}

.ease-out {
  transition-timing-function: cubic-bezier(0, 0, 0.2, 1);
}

.hover\:border-g2:hover {
  --tw-border-opacity: 1;
  border-color: rgb(102 102 102 / var(--tw-border-opacity));
}

.hover\:bg-g2:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(102 102 102 / var(--tw-bg-opacity));
}

.hover\:bg-inverse-b1:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity));
}

.hover\:text-bk:hover {
  --tw-text-opacity: 1;
  color: rgb(0 0 0 / var(--tw-text-opacity));
}

.hover\:text-g1:hover {
  --tw-text-opacity: 1;
  color: rgb(51 51 51 / var(--tw-text-opacity));
}

.hover\:text-g2:hover {
  --tw-text-opacity: 1;
  color: rgb(102 102 102 / var(--tw-text-opacity));
}

.hover\:opacity-100:hover {
  opacity: 1;
}

.hover\:ease-in:hover {
  transition-timing-function: cubic-bezier(0.4, 0, 1, 1);
}

@media (min-width: 640px) {
  .sm\:text-3xl {
    font-size: 1.875rem;
    line-height: 2.25rem;
  }
}
