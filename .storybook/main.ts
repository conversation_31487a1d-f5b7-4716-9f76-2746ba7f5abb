import path, { join, dirname } from 'path';
import type { StorybookConfig } from '@storybook/nextjs';
import TsconfigPathsPlugin from 'tsconfig-paths-webpack-plugin';

type WebpackArgs = NonNullable<StorybookConfig['webpackFinal']> extends (...args: infer U) => object ? U : never;

type WebpackConfig = WebpackArgs[0];

/**
 * This function is used to resolve the absolute path of a package.
 * It is needed in projects that use Yarn PnP or are set up within a monorepo.
 */
function getAbsolutePath(value: string) {
  return dirname(require.resolve(join(value, 'package.json')));
}
const config: StorybookConfig = {
  stories: ['../stories/**/*.mdx', '../stories/**/*.stories.@(js|jsx|mjs|ts|tsx)'],
  addons: [
    'storybook-addon-fetch-mock',
    getAbsolutePath('@storybook/addon-links'),
    getAbsolutePath('@storybook/addon-essentials'),
    getAbsolutePath('@storybook/addon-interactions'),
    getAbsolutePath('@storybook/addon-a11y'),
  ],
  framework: {
    name: getAbsolutePath('@storybook/nextjs'),
    options: {},
  },
  docs: {
    autodocs: 'tag',
  },
  staticDirs: [
    {
      from: '../packages/core/src/fonts/font-definitions',
      to: 'packages/core/src/fonts/font-definitions',
    },
  ],
  babel(config) {
    return {
      ...config,
      presets: [...(config.presets || []), '@emotion/babel-preset-css-prop'],
    };
  },
  webpackFinal: (config: WebpackConfig) => {
    if (config.resolve) {
      config.resolve.alias = {
        ...config.resolve.alias,
        'next/headers': path.resolve(__dirname, './mock.js'),
      };
    }
    config.resolve = config.resolve || {};
    config.resolve.plugins = config.resolve.plugins || [];
    config.resolve.plugins.push(new TsconfigPathsPlugin());

    config.resolve.alias = {
      ...(config.resolve.alias || {}),
      '@mui': path.resolve(__dirname, '../packages/marketing-ui/src'),
      'uuid/v4': path.resolve(__dirname, '../node_modules/uuid/dist/v4.js'),
      '@ecom-next/core/legacy/link': '@ecom-next/core/migration/link',

      // TODO: Remove these legacy aliases once buy-box is migrated
      '@product-page/legacy/product-selection': path.resolve(__dirname, '../packages/product/src/legacy/packages/product-selection/index.tsx'),
      '@product-page/legacy/styles': path.resolve(__dirname, '../packages/product/src/legacy/packages/styles/index.ts'),
      '@product-page/legacy/add-to-bag': path.resolve(__dirname, '../packages/product/src/legacy/packages/add-to-bag/index.tsx'),
      '@pdp/packages/helpers/util/preview-date': path.resolve(__dirname, '../packages/product/src/legacy/packages/helpers/util/preview-date.ts'),
      '@pdp/packages/helpers/util/error-messaging': path.resolve(__dirname, '../packages/product/src/legacy/packages/helpers/util/error-messaging.ts'),
      '@pdp/packages/helpers/util/evaluate-brand': path.resolve(__dirname, '../packages/product/src/legacy/packages/helpers/util/evaluate-brand.ts'),
      '@pdp/packages/styles/brand-styles/utils/util': path.resolve(__dirname, '../packages/product/src/legacy/packages/styles/brand-styles/utils/util.ts'),
      '@pdp/packages/styles/utility/mappings/colorMap': path.resolve(__dirname, '../packages/product/src/legacy/packages/styles/utility/mappings/colorMap.ts'),
      '@pdp/packages/styles/utility/mappings': path.resolve(__dirname, '../packages/product/src/legacy/packages/styles/utility/mappings/index.ts'),
    };
    return config;
  },
};
export default config;
