<script>
  // static data provided by server
  window.gap = {
    brand: 'at',
    market: 'us',
    locale: 'en_US',
    breakpoint: 'Desktop',
  };

  window.brandMarketInfo = {
    abbrBrand: 'at',
    abbrNameForBV: 'at',
    abbrNameForTealium: 'at',
    brand: 'athleta',
    displayName: 'Athleta',
    marketAgnosticBrandCode: 10,
    marketAwareBrandCode: 10,
    marketAwareBusinessAbbrName: 'AT_US_OL',
    marketAwareReportSuite: 'gapgidproduction, gapathletaproduction',
    marketAwareReportSuiteTest: 'gapgidtest, gapathletatest',
    marketAwareTrackingServer: 'metrics.gap.com',
    marketAwareTrackingSecureServer: 'securemetrics.gap.com',
  };

  window.newrelic = {
    noticeError: function () {},
  };
</script>
