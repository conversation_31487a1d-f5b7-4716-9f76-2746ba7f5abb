##Build Stage
FROM gacr2pbaseimages.azurecr.io/gap/base/node:22-alpine AS builder

ARG ARTIFACTORY_USERNAME
ARG ARTIFACTORY_PASSWORD
ARG APP_VERSION='unspecified'
ARG NETSTORAGE_UPLOAD

ENV APP_VERSION=${APP_VERSION}
ENV NETSTORAGE_UPLOAD=${NETSTORAGE_UPLOAD}
ENV ARTIFACTORY_USERNAME=${ARTIFACTORY_USERNAME}
ENV ARTIFACTORY_PASSWORD=${ARTIFACTORY_PASSWORD}
ENV NEXT_TELEMETRY_DISABLED=1
ENV TURBO_TELEMETRY_DISABLED=1

## Setting user root
USER root

RUN apk add --no-cache curl
WORKDIR /app

RUN echo registry=https://gapinc.jfrog.io/artifactory/api/npm/npm-repos > ./.npmrc && \
    curl -u ${ARTIFACTORY_USERNAME}:${ARTIFACTORY_PASSWORD} https://gapinc.jfrog.io/artifactory/api/npm/auth | tee -a ./.npmrc > /dev/null && \
    chmod 644 ./.npmrc && \
    cat ./.npmrc
RUN npm config fix

## Set Global npm config
RUN npm config set --global ignore-scripts true
RUN npm config set --global audit false

# Copying the source code to docker app directory
COPY . .

## Install dependencies
RUN npm ci
RUN npm remove sharp && npm install --platform=linuxmusl --arch=x64 sharp

# Run build code
RUN --mount=type=secret,id=secret3,dst=/secret3,mode=444 \
    export NEXT_PUBLIC_GOOGLE_API_KEY=$(cat /secret3) \
    && npm run build:ci

# Running netstorage upload with secrets
RUN --mount=type=secret,id=secret1,dst=/secret1,mode=444 \
    --mount=type=secret,id=secret2,dst=/secret2,mode=444 \
    ./packages/ecom-next/netstorage-upload.sh

# Building the runtime
FROM gacr2pbaseimages.azurecr.io/gap/base/node:22-alpine AS runtime

ARG APP_VERSION='unspecified'

ENV APP_VERSION=${APP_VERSION}
ENV NEXT_TELEMETRY_DISABLED=1
ENV TURBO_TELEMETRY_DISABLED=1
ENV NODE_ENV=production

# Creating a writable app folder for owner www-data
USER root

## making /app a writeable directory
RUN mkdir -p /app
VOLUME /app

## Giving correct permissions
RUN addgroup --system --gid 1001 nodejs
RUN adduser --system --uid 1001 nextjs
RUN chown nextjs:nodejs /app
##Changing workdir
WORKDIR /app

COPY --from=builder --chown=nextjs:nodejs /app/packages/ecom-next/.next/standalone ./
COPY --from=builder --chown=nextjs:nodejs /app/packages/ecom-next/.next/static ./packages/ecom-next/.next/static
COPY --from=builder --chown=nextjs:nodejs /app/packages/ecom-next/public ./packages/ecom-next/public
COPY --from=builder --chown=nextjs:nodejs /app/packages/ecom-next/newrelic.js ./

## Changing the user to nextjs
USER nextjs


ENV SERVICE_ARTIFACT_PATH="packages/ecom-next/server.js"