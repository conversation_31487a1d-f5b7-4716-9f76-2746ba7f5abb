## Depencies Stage
FROM gacr2pbaseimages.azurecr.io/gap/base/node:22-alpine as build

ARG ARTIFACTORY_USERNAME
ARG ARTIFACTORY_PASSWORD

ENV ARTIFACTORY_USERNAME=${ARTIFACTORY_USERNAME}
ENV ARTIFACTORY_PASSWORD=${ARTIFACTORY_PASSWORD}

USER root

RUN apk add --no-cache curl

WORKDIR /app

RUN echo registry=https://gapinc.jfrog.io/artifactory/api/npm/npm-repos > ./.npmrc && \
    curl -u ${ARTIFACTORY_USERNAME}:${ARTIFACTORY_PASSWORD} https://gapinc.jfrog.io/artifactory/api/npm/auth | tee -a ./.npmrc > /dev/null && \
    chmod 644 ./.npmrc && \
    cat ./.npmrc
RUN npm config fix

# Copying dependencies files
COPY package.json ./

##Installing node-modules
RUN npm i --no-audit --ignore-scripts

RUN npm remove sharp && npm install --platform=linuxmusl --arch=x64 sharp
#14 329.1 - Install for the current linuxmusl-x64 runtime: "npm install --platform=linuxmusl --arch=x64 sharp

COPY . .
## Run build

RUN npm i esbuild

RUN npm run build-storybook:mui --workspace=@ecom-next/marketing-ui

## Building the runtime
FROM gacr2pbaseimages.azurecr.io/gap/base/node:22-alpine as runtime

USER root
RUN mkdir -p /app
VOLUME /app
RUN chown www-data:www-data /app

#Changing workdir
WORKDIR /app

COPY --from=build /app/.npmrc ./.npmrc
RUN npm install -g http-server

### Copy relevant folders for the application
COPY --from=build --chown=www-data:www-data /app/packages/marketing-ui/storybook-static ./storybook-static
# Changing User
USER www-data
# Starting the app
ENTRYPOINT [ "http-server", "-p 3000", "./storybook-static" ]
