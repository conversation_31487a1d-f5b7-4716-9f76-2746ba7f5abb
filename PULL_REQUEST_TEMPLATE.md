# [TEAM-JIRA_TICKET_NUMBER](https://gapinc.atlassian.net/browse/[TEAM]-[JIRA_TICKET_NUMBER])
## Context & Problem
1. **Why is this change necessary?**  
2. **What problem does it solve or what gap does it fill?**
## Brands & Markets Impacted
1. **Which Brands are affected?**  
   - [ ] GAP  
   - [ ] AT  
   - [ ] ON  
   - [ ] BR  
   - [ ] GPFS  
   - [ ] BRFS  
2. **Which Markets are affected?**  
   - [ ] US  
   - [ ] CA  
## Description of Changes
1. **What exactly changed in the code?**  
2. **Are there any new dependencies or changes in the approach?**
## Steps to Test / Validate
1. **How can the reviewer test this locally?**  
2. **Are there specific commands or configurations required?**
## Before & After (if applicable)
1. **What was the behavior before?**  
2. **What is the behavior now?**  
3. **Can you provide screenshots, GIFs, or logs?**
## Impact Checklist
- [ ] **Breaking Changes?** If yes, what might break and how?  
- [ ] **Performance Impact?** Have you measured or estimated it?  
- [ ] **Security Considerations?** Any potential issues with data sensitivity or auth?  
- [ ] **Documentation Updated?** Do README or wikis reflect these changes?
## Potential Risks / Rollback Plan
1. **What are the main risks of merging this PR?**  
2. **How can we roll back if something goes wrong in production?**
## Reviewer Checklist
- [ ] Check across relevant brand URLs:
  - GAP: [http://www.local.gaptechol.com:3000](http://www.local.gaptechol.com:3000)
  - AT: [http://atol.local.gaptechol.com:3000](http://atol.local.gaptechol.com:3000)
  - ON: [http://onol.local.gaptechol.com:3000](http://onol.local.gaptechol.com:3000)
  - BR: [http://brol.local.gaptechol.com:3000/br](http://brol.local.gaptechol.com:3000/br)
  - GPFS: [http://www.local.factory-gaptechol.com](http://www.local.factory-gaptechol.com)
  - BRFS: [http://brfol.local.factory-gaptechol.com](http://brfol.local.factory-gaptechol.com)
- [ ] Ensure changes meet the acceptance criteria for the JIRA story.
- [ ] README or related documentation is up to date.
## Additional Observations or Next Steps (Optional)
1. **Are there future improvements or open tasks related to this change?**  
2. **Anything else the team should be aware of?**
### Contributors
@username
Collapse
