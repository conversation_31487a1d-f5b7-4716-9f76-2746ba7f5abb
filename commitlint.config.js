const config = require('@commitlint/config-conventional');
const { get, set } = require('lodash');

const typeEnumPath = "rules['type-enum'][2]";

const typeEnum = get(config, typeEnumPath).concat(['wip', 'dependencies', 'peerDependencies', 'devDependencies']);

set(config, 'plugins', [
  {
    rules: {
      'jira-task-id': ({ header }) => {
        const regex = /\[\w+-\d+\]$/;
        if (regex.test(header)) {
          return [true];
        }
        return [
          false,
          'Your commit message must end with a JIRA Card in brackets. Also, ensure you have added a commit message. E.g. feat(sitewide): message [JIRA-1234]',
        ];
      },
    },
  },
]);
set(config, typeEnumPath, typeEnum);
set(config, "rules['type-case'][2]", 'camel-case');
set(config, "rules['scope-enum']", [
  2,
  'always',
  [
    'sitewide',
    'product',
    'shopping-bag',
    'category',
    'core',
    'app',
    'all',
    'checkout',
    'marketing',
    'my-account',
    'utility-page',
    'storybook',
    'search',
    'utils',
    'stores',
    'seo',
    'plp',
  ],
]);

set(config, "rules['jira-task-id']", [2, 'always']);

module.exports = config;
