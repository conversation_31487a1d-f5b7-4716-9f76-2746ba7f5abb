export default process.env.TEST_MODE === 'LEGACY'
  ? {
      maxWorkers: '35%',
      reporters: ['<rootDir>/../../jestReporter.js', 'summary'],
      testRegex: ['.*.test.(ts|tsx|js)$', '.*.spec.(ts|tsx|js)$'],
      collectCoverage: true,
    }
  : {
      maxWorkers: '35%',
      reporters: ['<rootDir>/../../jestReporter.js', 'summary'],
      testRegex: ['__tests__/.*.test.(ts|tsx|js)$', '__tests__/.*.spec.(ts|tsx|js)$'],
      coveragePathIgnorePatterns: ['/legacy/*'],
      testPathIgnorePatterns: ['/legacy/*'],
      collectCoverage: true,
    };
