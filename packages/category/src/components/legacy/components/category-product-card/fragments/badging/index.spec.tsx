// @ts-nocheck
import React from 'react';
import { mount } from '@ecom-next/core/legacy/test';
import { Brands } from "@ecom-next/core/react-stitch";
import { BadgingFragment } from './index';

describe('<BadgingFragment/>', () => {
  it.each([Brands.Athleta, Brands.Gap, Brands.GapFactoryStore, Brands.OldNavy])(
    'should not render BadgingFragment when brand is %s',
    (brandName) => {
      const wrapper = mount(
        <BadgingFragment abbrBrand={brandName} badgingMessage="Best Seller" />
      );
      expect(wrapper.html()).toBeNull();
    }
  );
  it('should not render BadgingFragment when badgingMessage is not available', () => {
    const wrapper = mount(
      <BadgingFragment abbrBrand={Brands.BananaRepublic} />
    );
    expect(wrapper.html()).toBeNull();
  });
  it.each([Brands.BananaRepublic, Brands.BananaRepublicFactoryStore])(
    'should  render BadgingFragment when brand is %s',
    (brandName) => {
      const wrapper = mount(
        <BadgingFragment abbrBrand={brandName} badgingMessage="Best Seller" />
      );
      expect(wrapper.text()).toBe('Best Seller');
    }
  );
});
