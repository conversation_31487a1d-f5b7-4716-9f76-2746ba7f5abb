// @ts-nocheck
type FacetType = 'simple' | 'complex' | 'range';

type FacetSelectionType = 'single-select' | 'multi-select' | FacetType;

export interface Facet {
  isActive: boolean;
  order?: number;
  type: FacetSelectionType;
  name: string;
  searchFacetId?: string;
  displayName?: string;
  selectionType?: FacetSelectionType;
  searchFacetName?: string;
  searchFacetOptionGroupList?: SearchFacetOptionStyleParam;
  options?: FacetOption[];
}

export interface FacetWithConfig extends Facet {
  displayName?: string;
  facetDisplay?: string;
  facetLayout?: string;
  order?: number;
  hidden?: boolean;
  type: FacetType;
  isActive?: boolean;
  searchFacetName?: string;
}

export interface Option {
  id?: number;
  name?: string | null;
  applied?: boolean;
  options?: Option[];
}

export interface Options extends Option {
  options: Option[];
}

export type FacetWithApplied = {
  facet: Facet;
  appliedValues: FacetOption[];
};

interface AttributeMap {
  dimensionId: string;
  index: string;
  variantId: string;
  searchFacetOptionName2?: string;
}

interface Sizes {
  isActive: string;
  searchFacetOptionId: string;
  variantId: string;
  variantName: string;
  sfcId: string;
  sfcName: string;
  sfcDimName: string;
  dimId: string;
  attributeMap: AttributeMap;
  id: string;
  isSelected: string;
  selected: boolean;
  name: string;
  searchFacetOptionValue: string;
  tagDisplayLabel?: string;
  description?: string;
  dimName?: string;
  searchFacetOptionName: string;
}

interface V2Size {
  id: string;
  name: string;
  description: string;
  selected: boolean;
  tagDisplayLabel: string;
}

interface V2StyleGroup {
  id: string;
  name: string;
  sizes: V2Size[];
}

interface FacetsAdapterResult {
  facetOptions: Facet[];
  hasTags?: boolean;
}

interface V2Variant {
  id: string;
  name: string;
  styleGroups: V2StyleGroup[];
}

interface V2SizeFacet extends Facet {
  sizeVariants: V2Variant[];
  appliedValues: V2Size[];
}

export interface FlattenedSize {
  sizeOptions?: Array<Sizes>;
  selectedSizes?: Array<Sizes>;
}

export interface Styles extends Array<Sizes> {
  sfcId?: string;
}

interface StyleGroups {
  id?: string;
  name: string;
  sizes: Styles;
}

export interface SearchFacetOptionStyle {
  searchFacetOptionGroupList: SearchFacetOptionStyleParam[];
  searchFacetOptionList: Sizes[];
  searchFacetOptionGroupId: string;
  searchFacetOptionGroupName: string;
}

export interface SearchFacetOptionStyleParam extends SearchFacetOptionStyle {
  searchFacetOptionGroupList: SearchFacetOptionStyle | SearchFacetOptionStyle[];
}

export interface SearchFacetOptionList {
  searchFacetOptionList: Sizes[];
}

export interface SizeVariantsResponse {
  id: number;
  name: string;
  styleGroups: Array<StyleGroups>;
  selected?: boolean;
}

export interface PriceFacet extends Facet {
  range: PriceRange;
  appliedRange: PriceRange;
}

export interface PriceRange {
  min: number | string;
  max: number | string;
}

export type Empty<T> = T | undefined | null;

export type ChildProduct = {
  businessCatalogItemId: string;
  name: string;
  catalogItemTypeId: string;
  catalogItemSubtypeId: string;
  mcmStatusCode: string;
  isInStock: string;
  isActive?: string;
  inventoryStatusId: string;
  isAssignedAtStyleLevel: string;
  defaultSizeVariantId: string;
  inDcDate: string;
  reviewScore?: string;
  reviewCount?: string;
  parentStyleId: string;
  parentBusinessCatalogItemId: string;
  pristineImages?: {
    pristine1ImagePath: string;
  };
  zoomImages?: { [key: string]: string };
  quicklookImage: Empty<{ [key: string]: string }>;
  categoryLargeImage: Empty<{ [key: string]: string }>;
  outfitCategoryLargeImage?: { [key: string]: Empty<string> };
  avImages: Empty<{ [key: string]: Empty<string> }>;
  outfitQuickLookImage: { [key: string]: Empty<string> };
  displayColor: { [key: string]: string };
  price: { [key: string]: string };
  priceFormatXhtml: string;
  pricePercentageOffXhtml: string;
  sortOrder: string;
  isFirstStyleColorForSubcategory: string;
  isOutfit: string;
  variantName: string;
  isSearchable: string;
  isLowestPriceCC?: string;
  isMadeToOrder?: string;
  isExcludedFromPromotion?: string;
  isShowSellerName?: string;
  isFreeShipping?: string;
  webProductType?: string;
  vendorName?: string;
};

export type ChildCategory = {
  id?: string | undefined;
  name: string;
  childProducts?: ChildProduct[];
};

export type ProductCategory = {
  businessCatalogItemId: Empty<string>;
  name: string;
  childCategories?: ChildCategory[] | ChildCategory;
  productCategoryPaginator: { [key: string]: string | number };
  childProducts?: ChildProduct[] | ChildCategory;
};

export type ProductsResponse = {
  resourceVersion: string;
  resourceUrl: string;
  productCategoryFacetedSearch: {
    productCategory: ProductCategory;
    searchFacetInfo?: Record<string, unknown>;
    isRedirect: string;
    totalItemCount: string | number;
    searchText?: string;
    searchDivName?: string;
    isOutfitCategory: string;
    areAllSubCatsOutfit: string;
  };
  localeInfo: { [key: string]: string };
  totalItemCount?: string;
};

export type FacetOption = {
  id: string;
  name: string;
  value?: string;
  isActive?: string;
  applied?: boolean;
};

export interface OptionAttributeMap {
  index: string;
  variantId: string;
  dimensionId: string;
  searchFacetOptionName2?: string;
}

export interface AdaptedFacetStyle {
  id: number;
  name: string;
  localeName: string;
  options: AdaptedFacetDimension[];
}

export interface AdaptedFacetVariant {
  id: number;
  name: string;
  localeName: string;
  options: FacetOption[];
}

export interface AdaptedFacetDimension {
  id: number;
  name: string;
  localeName: string;
  options: AdaptedFacetVariant[];
}

export type SizeFacetWithConfig = SizeFacet & FacetConfig;

declare type OldFacet = {
  isActive: string;
  searchFacetId?: string;
  searchFacetName?: string;
  searchFacetOptionGroupList: OldFacetStyle[];
};

export interface OldFacetOption {
  isActive: string;
  isSelected: string;
  searchFacetOptionId: string;
  searchFacetOptionName: string;
  searchFacetOptionValue: string;
  parentSearchFacetOptionGroupId?: string;
  attributeMap: OptionAttributeMap;
}

export interface OldFacetDimension {
  searchFacetOptionGroupId: string;
  searchFacetOptionGroupName: string;
  parentSearchFacetOptionGroupId: string;
  searchFacetOptionGroupList: OldFacetVariant[];
}

export interface OldFacetVariant {
  searchFacetOptionList: OldFacetOption[];
  searchFacetOptionGroupId: string;
  searchFacetOptionGroupName: string;
  parentSearchFacetOptionGroupId: string;
}

export interface OldFacetStyle {
  searchFacetOptionGroupId: string;
  searchFacetOptionGroupName: string;
  searchFacetOptionGroupList?: OldFacetDimension | OldFacetDimension[];
}
