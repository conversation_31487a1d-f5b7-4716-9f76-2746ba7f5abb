// @ts-nocheck
import { mount } from "@ecom-next/core/legacy/test";
import {
  getHashParameter,
  updateHashParameter,
} from "@ecom-next/core/url-helper";
import { adaptProductModelToggle } from "@ecom-next/plp-ui/legacy/size-model-toggle";
import * as updateFacetsEvents from "@ecom-next/plp-ui/legacy/events";

import * as mediator from "./mediator";
import * as updateScrollPosition from "./collaborators/update-scroll-position";
import productFetcher from "./collaborators/fetch-products";
import oldContractProductFetcher from "./collaborators/fetch-products/old-contract";
import newContractProductFetcher from "./collaborators/fetch-products/new-contract";
import getUpdatedListOfAppliedFacets, {
  removeAllFacetsFromAppliedArray,
} from "./collaborators/update-applied-facets";
import { updateFacetData } from "./collaborators/update-facet-data";
import { facetEngagementDatalayerEvent } from "../../datalayer/events/facet-engagement";
import webHierarchyGap from "../../adapters/fixtures/webHierarchy-gap.json";
import { LeftFacetedGridMediatorProps } from "./types";
// @ts-expect-error
import * as previewBar from "preview-bar/read.js";
import { Facets } from "../product-grid-region/types";

jest.mock("@ecom-next/core/url-helper");
jest.mock("./collaborators/update-facet-data");
jest.mock("./collaborators/fetch-products/old-contract");
jest.mock("./collaborators/fetch-products/new-contract");
jest.mock("./collaborators/update-scroll-position");
jest.mock("./collaborators/update-applied-facets");
jest.mock("../../datalayer/events/facet-engagement");
jest.mock("@ecom-next/plp-ui/legacy/size-model-toggle");
jest.mock("@ecom-next/plp-ui/legacy/events");
jest.mock("@ecom-next/plp-ui/legacy/features");

const LeftFacetedGridMediator = mediator.RawLeftFacetedGridMediator;
const getOptionalPreviewState = mediator.getOptionalPreviewState;

describe("<LeftFacetedGridMediator />", () => {
  beforeAll(() => {
    console.error = jest.fn();
  });

  const params = { style: "1113004" };
  const colorFacetData = {
    facetName: "color",
    facetData: { id: "1020", name: "Pink" },
    indexableUrlFacetParamKey: "color",
    indexableUrlFacetParamValue: "pink",
  };
  const sleeveLengthFacetData = {
    facetName: "sleeveLength",
    facetData: { id: "1928", name: "Short Sleeve" },
    indexableUrlFacetParamKey: "sleeve-length",
    indexableUrlFacetParamValue: "short-sleeve",
  };
  const singleFacetParam = {
    indexableFacets: [colorFacetData],
  };
  const multipleFacetParams = {
    indexableFacets: [sleeveLengthFacetData, colorFacetData],
  };
  const mockedContentData = {
    sitewide: {
      topnav: {
        data: {
          isNavSticky: false,
        },
      },
    },
  };
  const currentCategory = {
    children: [],
    customUrl: "/browse/category.do?cid=6359#pageId=0&department=165",
    hasSubDivision: false,
    hidden: false,
    id: "6359",
    link: "/browse/category.do?cid=6359",
    name: "Jeans",
    parents: [],
    selected: true,
    type: "category",
  };
  const productsResponse = {
    facetData: { appliedFacets: {} },
    totalItemCount: 10,
  };
  const onFacetChange = () => false;
  const defaultProps = {
    bopisCurbsideEnabled: true,
    bopisData: {},
    contentData: mockedContentData,
    criticalResources: [],
    curbsideEnabled: false,
    departmentId: "",
    enabledFeatures: {},
    featureVariables: {
      "cat-model-toggle-us": {
        sizeGroups: {},
      },
      "cat-model-toggle-ca": {
        sizeGroups: {},
      },
    },
    isDepartmentAnOption: false,
    isEnableCatWithDept: undefined,
    isFlexFacetEnabled: false,
    isRatingsFacetEnabled: false,
    isSwatchSortEnabled: undefined,
    modelToggleData: {
      mixedGrid: false,
      sizeValue: false,
    },
    sizeGroups: undefined,
    swatchSortBy: undefined,
    onFacetChange,
  };

  const mediatorWrapper = (newProps: Partial<LeftFacetedGridMediatorProps>) =>
    mount(
      <LeftFacetedGridMediator {...defaultProps} {...newProps}>
        <div />
      </LeftFacetedGridMediator>
    );

  const mediatorWrapperWithStyle = (newProps: Partial<LeftFacetedGridMediatorProps>) =>
    mount(
      <LeftFacetedGridMediator {...defaultProps} {...newProps} params={params}>
        <div />
      </LeftFacetedGridMediator>
    );

  const mediatorWrapperWithSingleFacet = () =>
    mount(
      <LeftFacetedGridMediator {...defaultProps} params={singleFacetParam}>
        <div />
      </LeftFacetedGridMediator>
    );
  const mediatorWrapperWithMultipleFacets = () =>
    mount(
      <LeftFacetedGridMediator {...defaultProps} params={multipleFacetParams}>
        <div />
      </LeftFacetedGridMediator>
    );

  const mediatorWrapperWithCurrentCategory = () =>
    mount(
      <LeftFacetedGridMediator
        {...defaultProps}
        currentCategory={currentCategory}
      >
        <div />
      </LeftFacetedGridMediator>
    );

  const mediatorWrapperWithStyleAndCurrentCategory = () =>
    mount(
      <LeftFacetedGridMediator
        {...defaultProps}
        currentCategory={currentCategory}
        params={params}
      >
        <div />
      </LeftFacetedGridMediator>
    );

  beforeEach(() => {
    jest.clearAllMocks();

    updateScrollPosition.default.mockReturnValue({});
    removeAllFacetsFromAppliedArray.mockReturnValue(jest.fn());
    oldContractProductFetcher.mockReturnValue(
      Promise.resolve(productsResponse)
    );
    newContractProductFetcher.mockReturnValue(
      Promise.resolve(productsResponse)
    );
    getUpdatedListOfAppliedFacets.mockReturnValue({ appliedFacets: {} });

    window.location.hash = "";
    window.addEventListener = jest.fn();
    window.__CATEGORY_PAGE_STATE__ = {};
  });

  describe("facets", () => {
    describe("initial state and properties", () => {
      it("applied facets field is empty when parameters are not present in the url hash string", () => {
        const appliedFacets = mediatorWrapper().state("facets").appliedFacets;
        Object.keys(appliedFacets).forEach((facetName) => {
          expect(appliedFacets[facetName]).toHaveLength(0);
        });
      });

      it("sets sortBy in the applied facets object when the parameter is present in the url hash string", () => {
        window.location.hash = "sortByField=price&sortByDir=asc";
        getHashParameter
          .mockReturnValueOnce("price")
          .mockReturnValueOnce("asc");

        expect(mediatorWrapper().state("facets").appliedFacets).toEqual(
          expect.objectContaining({
            sortByField: [{ facetName: "sortByField", id: "price" }],
            sortByDir: [{ facetName: "sortByDir", id: "asc" }],
          })
        );

        window.location.hash = "";
      });

      it("should not have any initial value stored for latest query string", () => {
        expect(mediatorWrapper().instance().latestQueryString).toEqual("");
      });

      it("should be initialized with placeholders enabled", () => {
        expect(mediatorWrapper().instance().disablePlaceholders).toEqual(false);
      });

      it("ignores department facet when department-facet-hide flag is on", async () => {
        const wrapper = mediatorWrapper({
          abbrBrand: "gap",
          market: "us",
          enabledFeatures: {
            "department-facet-hide-us-gap": true,
          },
        });

        await new Promise(setImmediate);

        expect(wrapper.state().ignoredFacets.includes("department")).toBe(true);
      });
    });

    describe("latest query string", () => {
      describe("when url has hash", () => {
        afterEach(() => {
          window.location.hash = "";
        })
        it("should update with correct style from hash", () => {
          window.location.hash = "#style=1113004";

          const wrapper = mediatorWrapper().instance();
          expect(wrapper.latestQueryString).toEqual("style=1113004");
        });

        it("should update with correct department and pageId from hash", () => {
          window.location.hash = "#pageId=0&department=165";

          const wrapper = mediatorWrapper({}).instance();
          expect(wrapper.latestQueryString).toEqual("pageId=0&department=165");
        });

        it("should only keep value from hash when handleCustomParamsAndFacetsParams is off", () => {
          window.location.hash = "#department=165";

          const wrapper = mediatorWrapperWithStyle({}).instance();
          expect(wrapper.latestQueryString).toEqual("department=165");
        });
      });

      describe("when url hash is not present", () => {
        it("should update with correct style if present in url as a query parameter", () => {
          window.location.hash = "";

          const wrapper = mediatorWrapperWithStyle({}).instance();
          expect(wrapper.latestQueryString).toEqual("style=1113004");

          window.location.hash = "";
        });

        it("should update the search param correctly when there's single facet param presents in the request url", () => {
          window.location.hash = "";

          const wrapper = mediatorWrapperWithSingleFacet().instance();
          expect(wrapper.latestQueryString).toEqual("color=1020");

          window.location.hash = "";
        });

        it("should update the search param correctly when there's multiple facet params present in the request url", () => {
          window.location.hash = "";

          const wrapper = mediatorWrapperWithMultipleFacets().instance();
          expect(wrapper.latestQueryString).toEqual(
            "sleeveLength=1928&color=1020"
          );

          window.location.hash = "";
        });

        it("sets departmentId in state when department exists from customUrl hash", () => {
          const departmentId =
            mediatorWrapperWithCurrentCategory().state("departmentId");

          expect(departmentId).toEqual("165");
        });

        it("sets departmentId to an empty string state when department does not exist in customUrl hash", () => {
          const departmentId = mediatorWrapper().state("departmentId");

          expect(departmentId).toEqual("");
        });
      });

      describe("and navigation data has a customUrl with pageId", () => {
        it("should update with correct pageId from props", () => {
          const wrapper = mediatorWrapperWithCurrentCategory().instance();
          expect(wrapper.props.currentCategory.customUrl).toContain("pageId=0");
          expect(wrapper.latestQueryString).toEqual("pageId=0&department=165");
        });

        it("should style query", () => {
          const wrapper =
            mediatorWrapperWithStyleAndCurrentCategory().instance();
          expect(wrapper.props.currentCategory.customUrl).toContain("pageId=0");
          expect(wrapper.latestQueryString).toEqual("style=1113004");
        });
      });
    });

    describe("mediator actions over facets", () => {
      it("should fetch products, change the browser queryString and call updateFacetsEmitter on updateFacets", async () => {
        const wrapper = mediatorWrapper().instance();
        const historySpy = jest.spyOn(window.history, "pushState");
        const updateFacetsEmitterSpy = jest.spyOn(
          updateFacetsEvents,
          "updateFacetsEmitter"
        );
        wrapper.updateFacets();

        await new Promise(setImmediate);

        expect(historySpy).toHaveBeenCalled();
        expect(updateFacetsEmitterSpy).toHaveBeenCalled();
        expect(oldContractProductFetcher).toHaveBeenCalled();
        historySpy.mockRestore();
      });

      it("should reset facets and fetch products on clearAllFacets.", () => {
        // TODO: update facets to get the state out of default and check default again
        const historySpy = jest.spyOn(window.history, "pushState");
        removeAllFacetsFromAppliedArray.mockReturnValue({ appliedFacets: {} });
        const wrapper = mediatorWrapper();
        const updateFacetsSpy = jest.spyOn(wrapper.instance(), "updateFacets");

        wrapper.instance().clearAllFacets();
        expect(wrapper.state("facets").appliedFacets).toEqual({});
        expect(historySpy).toHaveBeenCalled();
        expect(updateFacetsSpy).toHaveBeenCalled();
      });

      it("should trigger facet update on clearFacet", () => {
        const wrapper = mediatorWrapper().instance();
        const updateFacetsSpy = jest.spyOn(wrapper, "updateFacets");
        wrapper.clearFacet();
        expect(updateFacetsSpy).toHaveBeenCalled();
      });
    });
  });

  describe("componentDidMount", () => {
    it("sortByField is empty array", () => {
      window.location.hash = "sortByField=price&sortByDir=asc";
      getHashParameter.mockReturnValue("");
      const { appliedFacets } = mediatorWrapper().state("facets");
      expect(appliedFacets.sortByField).toEqual([]);
      window.location.hash = "";
    });

    it("has sort by selected when the query parameter is in the in the hash string", () => {
      window.location.hash = "sortByField=price&sortByDir=asc";

      getHashParameter.mockReturnValue("price");
      const { appliedFacets } = mediatorWrapper().state("facets");
      expect(appliedFacets.sortByField).toEqual([
        { facetName: "sortByField", id: "price" },
      ]);

      window.location.hash = "";
    });

    it.skip("has out of stock toggle selected when cat-br-vintage is true and isOutOfStockFilterEnabled is true", () => {
      getHashParameter.mockReturnValue(false);
      const { appliedFacets } = mediatorWrapper({
        isOutOfStockFilterEnabled: true,
        enabledFeatures: {
          "cat-br-vintage": true,
        },
      }).state("facets");
      expect(appliedFacets.outOfStock).toEqual([
        {
          facetName: "out-of-stock",
          id: false,
          tagDisplayLabel: "Out-of-stock",
        },
      ]);
    });

    it("does not have sortByField selected when the query parameter is not in the hash string", () => {
      getHashParameter.mockReturnValue("");
      const { appliedFacets } = mediatorWrapper().state("facets");
      expect(appliedFacets.sortByField).toEqual([]);
    });

    it("updates facet data on the state when did mount", async () => {
      defaultProps.children = <div />;

      const resolve = () => new Promise(setImmediate);
      const previous = { ...defaultProps };
      const totalItemCount = 10;

      previous.disablePlaceholders = false;
      previous.error = "";
      previous.facets = {
        appliedFacets: {
          sortByDir: [],
          sortByField: [],
          outOfStock: [],
        },
      };
      previous.hideDepartmentFacet = undefined;
      previous.hideOutOfStockFacet = undefined;
      previous.ignoredFacets = [
        "sortByDir",
        "storeId",
        "modelOn",
        "sortByField",
      ];
      previous.appliedFacetsCount = 0;
      previous.reportedInitialResponse = false;
      previous.urlHash = "";
      previous.orderedFacets = [];
      previous.appliedQuickFacets = [];

      mediatorWrapper();

      await resolve();

      expect(updateFacetData).toHaveBeenCalledWith(
        defaultProps,
        previous,
        productsResponse,
        totalItemCount,
        ""
      );
    });
  });

  describe("componentDidUpdate", () => {
    it("does not call product fetcher when updating component with invalid storeId", async () => {
      const resolve = () => new Promise(setImmediate);

      const props = {
        ...defaultProps,
        bopisData: {
          bopisInitialed: "init",
          enabled: true,
          active: true,
          selectedStore: {
            storeId: undefined,
          },
        },
        children: <div />,
        disablePlaceholders: false,
        error: "",
        facets: {
          facetSelected: {
            sortBy: [],
          },
        },
        hideDepartmentFacet: undefined,
        ignoredFacets: ["sortBy", "storeId"],
        totalAppliedFacets: 0,
        urlHash: "",
      };

      const wrapper = mediatorWrapper();

      await resolve();

      wrapper.setProps(props).update();

      await resolve();

      expect(oldContractProductFetcher).not.toHaveBeenCalledTimes(2);
    });

    it("updates BOPIS data on the state when did update", async () => {
      const resolve = () => new Promise(setImmediate);

      const newProps = {
        ...defaultProps,
        children: <div />,
        disablePlaceholders: false,
        error: "",
        bopisData: {
          bopisInitialed: "init",
          enabled: true,
          active: true,
          activedPageType: "category",
          selectedStore: {
            storeId: 100,
          },
          setEnabled: () => false,
        },
        hideDepartmentFacet: undefined,
        ignoredFacets: ["sortBy", "storeId"],
        appliedFacetsCount: 0,
        urlHash: "",
        facets: {
          appliedFacets: {
            sortBy: [],
          },
        },
      };

      const wrapper = mediatorWrapper();

      await resolve();

      wrapper.setProps(newProps).update();

      await resolve();

      expect(wrapper.state().facets.appliedFacets.storeId).toStrictEqual([
        {
          facetName: "storeId",
          facetOption: 100,
          searchFacetOptionId: 100,
          isSelected: true,
        },
      ]);
    });

    it("updates store id on the URL hash when did update", async () => {
      const resolve = () => new Promise(setImmediate);

      const props = {
        ...defaultProps,
        appliedFacetsCount: 0,
        bopisData: {
          bopisInitialed: "init",
          enabled: true,
          active: true,
          activedPageType: "category",
          selectedStore: {
            storeId: 100,
          },
          setEnabled: () => false,
        },
        children: <div />,
        disablePlaceholders: false,
        error: "",
        facets: {
          appliedFacets: {
            sortBy: [],
          },
        },
        hideDepartmentFacet: undefined,
        ignoredFacets: ["sortBy", "storeId"],
        urlHash: "",
      };

      const wrapper = mediatorWrapper();

      await resolve();

      wrapper.setProps(props).update();

      await resolve();

      expect(updateHashParameter).toHaveBeenLastCalledWith("storeId", 100);
    });

    it("not able category-page apply bopis filters if some different page iterate with bopisContext", async () => {
      const resolve = () => new Promise(setImmediate);

      const props = {
        ...defaultProps,
        appliedFacetsCount: 0,
        bopisData: {
          bopisInitialed: "init",
          enabled: true,
          active: true,
          activedPageType: "product",
          selectedStore: {
            storeId: 250,
          },
          setEnabled: () => false,
        },
      };

      const wrapper = mediatorWrapper();

      await resolve();

      wrapper.setProps(props).update();

      await resolve();

      expect(updateHashParameter).not.toHaveBeenCalled();
    });
  });

  describe("hashChangeHandler", () => {
    it.skip("updates facet data on the state when hash changes", async () => {
      const resolve = () => new Promise(setImmediate);
      const props = {
        ...defaultProps,
        children: <div />,
      };
      const previousProps = {
        ...props,
        disablePlaceholders: false,
        error: "",
        facets: {
          appliedFacets: {
            sortByDir: [],
            sortByField: [],
            outOfStock: [],
          },
        },
        hideDepartmentFacet: undefined,
        ignoredFacets: ["sortByDir", "sortByField", "storeId", "modelOn"],
        appliedFacetsCount: 0,
        urlHash: "",
        reportedInitialResponse: true,
        orderedFacets: [],
        appliedQuickFacets: [],
      };
      const totalItemCount = 10;

      const wrapper = mediatorWrapper().instance();

      await resolve();

      wrapper.hashChangeHandler();

      await resolve();

      expect(updateFacetData).toHaveBeenCalledTimes(2);
      expect(updateFacetData).toHaveBeenLastCalledWith(
        props,
        previousProps,
        productsResponse,
        totalItemCount,
        ""
      );
    });

    it("uses style from query parameters when hash changes", async () => {
      const resolve = () => new Promise(setImmediate);

      const props = {
        cid: "1000",
        departmentId: "",
        productsUrl: "https://url.com",
        productsApiKey: "apiKey",
        abSeg: {},
        params: {
          style: 5000,
          color: "",
        },
        locale: "en_CA",
        isDynamicStyleFacetNameEnabled: true,
        webHierarchy: webHierarchyGap,
        selectedNodes: [
          {
            id: "5002",
            name: "women",
            type: "division",
          },
          {
            id: "69883",
            name: "dresses",
            type: "category",
          },
        ],
        isSwitchContractEnabled: false,
        contentType: "ecom",
      };

      const wrapper = mediatorWrapper(props).instance();

      await resolve();

      wrapper.hashChangeHandler();

      await resolve();

      expect(oldContractProductFetcher).toHaveBeenLastCalledWith(
        props.cid,
        "",
        expect.stringContaining("style=5000"),
        undefined,
        props.productsUrl,
        props.productsApiKey,
        undefined,
        false,
        props.abSeg,
        {},
        false,
        "en_CA",
        false,
        false,
        props.isDynamicStyleFacetNameEnabled,
        props.webHierarchy,
        props.selectedNodes
      );
    });
  });

  describe("pagination", () => {
    it("should trigger facet update on goToPage", () => {
      const wrapper = mediatorWrapper().instance();
      const updateFacetsSpy = jest.spyOn(wrapper, "updateFacets");
      wrapper.goToPage();
      expect(updateFacetsSpy).toHaveBeenCalled();
    });
  });

  describe("updateMegaNavHeightScroll", () => {
    it("resets wasFaceting on state on publish", async () => {
      const resolve = () => new Promise(setImmediate);

      const wrapper = mediatorWrapper().instance();

      await resolve();

      wrapper.updateMegaNavHeightScroll({});

      await resolve();

      expect(wrapper.state.wasFaceting).toBe(false);
    });

    it("invokes updateScrollPositionSpy if wasFaceting:true", async () => {
      const updateScrollPositionSpy = jest.spyOn(
        updateScrollPosition,
        "default"
      );
      const resolve = () => new Promise(setImmediate);
      const wrapper = mediatorWrapper({ wasFaceting: true }).instance();
      await resolve();
      wrapper.updateMegaNavHeightScroll({});
      await resolve();
      expect(updateScrollPositionSpy).toHaveBeenCalled();
      expect(wrapper.state.wasFaceting).toBe(false);
    });
  });

  describe("updateFacets", () => {
    it("updates facet data on the state when publish", async () => {
      const resolve = () => new Promise(setImmediate);
      const props = {
        ...defaultProps,
        children: <div />,
      };
      const previousProps = {
        ...props,
        disablePlaceholders: false,
        error: "",
        facets: {
          appliedFacets: {},
        },
        hideDepartmentFacet: undefined,
        hideOutOfStockFacet: undefined,
        ignoredFacets: ["sortByDir", "storeId", "modelOn", "sortByField"],
        appliedFacetsCount: 0,
        urlHash: "",
        reportedInitialResponse: true,
        wasFaceting: true,
        orderedFacets: [],
        appliedQuickFacets: [],
      };
      const totalItemCount = 10;

      const wrapper = mediatorWrapper().instance();

      await resolve();

      wrapper.updateFacets({});

      await resolve();

      expect(updateFacetData).toHaveBeenCalledTimes(2);
      expect(updateFacetData).toHaveBeenLastCalledWith(
        props,
        previousProps,
        productsResponse,
        totalItemCount,
        ""
      );
    });

    it("changes direction", async () => {
      const wrapper = mediatorWrapper().instance();
      const updateFacetsSpy = jest.spyOn(wrapper, "updateFacets");
      wrapper.updateFacets({ facetName: "sortByField" });
      expect(updateFacetsSpy).toHaveBeenCalledTimes(2);
    });
  });

  describe("callFetchProducts", () => {
    describe("should call fetch-products with isFlexFacetsEnabled", () => {
      it("TRUE when enabledFeatures contains flex facets flag", async () => {
        mediatorWrapper({
          abbrBrand: "gap",
          market: "us",
          abSeg: {
            gap35: "a",
          },
          isFlexFacetEnabled: true,
        });

        await new Promise(setImmediate); // flushes pending promises

        const [firstCall] = oldContractProductFetcher.mock.calls;
        const position = firstCall.length - 7;

        const isFlexFacetEnabled = firstCall[position];
        expect(isFlexFacetEnabled).toBe(true);
      });

      it("FALSE when enabledFeatures does not contains flex facets flag", async () => {
        mediatorWrapper({
          abbrBrand: "gap",
          market: "us",
          abSeg: {
            gap35: "a",
          },
        });

        await new Promise(setImmediate); // flushes pending promises

        const [firstCall] = oldContractProductFetcher.mock.calls;
        const position = firstCall.length - 7;

        const isFlexFacetEnabled = firstCall[position];
        expect(isFlexFacetEnabled).toBe(false);
      });

      it("FALSE when enabledFeatures does not contains abSeg", async () => {
        mediatorWrapper({
          abbrBrand: "gap",
          market: "us",
        });

        await new Promise(setImmediate); // flushes pending promises

        const [firstCall] = oldContractProductFetcher.mock.calls;
        const position = firstCall.length - 7;

        const isFlexFacetEnabled = firstCall[position];
        expect(isFlexFacetEnabled).toBe(false);
      });

      it("FALSE when enabledFeatures contains abSeg but the value is not a", async () => {
        mediatorWrapper({
          abbrBrand: "gap",
          market: "us",
          abSeg: {
            gap35: "x",
          },
        });

        await new Promise(setImmediate); // flushes pending promises

        const [firstCall] = oldContractProductFetcher.mock.calls;
        const position = firstCall.length - 7;

        const isFlexFacetEnabled = firstCall[position];
        expect(isFlexFacetEnabled).toBe(false);
      });

      it.skip("TRUE when enabledFeatures contains cat-br-vintage feature on true and outOfStock toggle is active", async () => {
        mediatorWrapper({
          abbrBrand: "gap",
          contentType: "ecom",
          market: "us",
          enabledFeatures: {
            "cat-br-vintage": true,
          },
          isOutOfStockFilterEnabled: true,
        });

        await new Promise(setImmediate);

        const [firstCall] = oldContractProductFetcher.mock.calls;
        const position = firstCall.length - 5;
        const ignoreInventory = firstCall[position];
        expect(ignoreInventory).toBe(true);
      });

      it("ignoreInventory is false when cat-br-vintage feature flag is false", async () => {
        mediatorWrapper({
          abbrBrand: "gap",
          contentType: "ecom",
          market: "us",
          enabledFeatures: {
            "cat-br-vintage": false,
          },
          isOutOfStockFilterEnabled: true,
        });

        await new Promise(setImmediate);

        const [firstCall] = oldContractProductFetcher.mock.calls;
        const position = firstCall.length - 5;

        const ignoreInventory = firstCall[position];
        expect(ignoreInventory).toBe(false);
      });

      it("ignoreInventory is true when  cat-br-vintage feature flag is false and out-of-stock toggle is on", async () => {
        (getHashParameter as jest.Mock).mockImplementation((param) =>
          param === "outOfStock" ? "true" : undefined
        );
        mediatorWrapper({
          abbrBrand: "gap",
          contentType: "ecom",
          market: "us",
          enabledFeatures: {
            "cat-br-vintage": false,
          },
          isOutOfStockFilterEnabled: true,
        });

        await new Promise(setImmediate);

        const [firstCall] = oldContractProductFetcher.mock.calls;
        const position = firstCall.length - 5;

        const ignoreInventory = firstCall[position];
        expect(ignoreInventory).toBe(true);
        (getHashParameter as jest.Mock).mockReset();
      });

      it("ignoreInventory is false when  cat-br-vintage feature flag is not available", async () => {
        mediatorWrapper({
          abbrBrand: "gap",
          contentType: "ecom",
          market: "us",
          enabledFeatures: {},
        });

        await new Promise(setImmediate);

        const [firstCall] = oldContractProductFetcher.mock.calls;
        const position = firstCall.length - 5;

        const ignoreInventory = firstCall[position];
        expect(ignoreInventory).toBe(false);
      });
    });

    describe("should call fetch-products with departmentId", () => {
      it("passes departmentId as an empty string when isDepartmentAnOption is true", async () => {
        mediatorWrapper({
          currentCategory,
          isDepartmentAnOption: true,
        });

        await new Promise(setImmediate); // flushes pending promises

        const [firstCall] = oldContractProductFetcher.mock.calls;
        const position = firstCall.length - 16;

        const departmentId = firstCall[position];
        expect(departmentId).toBe("");
      });

      it("passes departmentId from state when isDepartmentAnOption is false", async () => {
        mediatorWrapperWithCurrentCategory({
          isDepartmentAnOption: false,
        });

        await new Promise(setImmediate); // flushes pending promises

        const [firstCall] = oldContractProductFetcher.mock.calls;
        const position = firstCall.length - 16;

        const departmentId = firstCall[position];
        expect(departmentId).toBe("165");
      });
    });

    it("on callFetchProducts check if avImage model toggle can be handled", async () => {
      const resolve = () => new Promise(setImmediate);

      const wrapper = mediatorWrapper({
        sizeGroup: "S",
      }).instance();

      const spyed = jest.spyOn(wrapper, "updateProductGroupsBySizeModel");

      await resolve();

      expect(spyed).toHaveBeenCalledTimes(1);
      expect(spyed).toHaveBeenLastCalledWith("S");
    });

    it("on callFetchProducts save facetQueryString on sessionStorage", async () => {
      const setItem = jest.fn();

      Object.defineProperty(window, "sessionStorage", {
        value: { setItem, removeItem: jest.fn() },
        writable: true,
      });

      const wrapper = mediatorWrapper();

      wrapper.setState((current: Facets) => ({
        ...current,
        facetQueryString: "banner=banner",
        facets: {
          appliedFacets: {
            style: [
              {
                name: "Skinny",
              },
              {
                tagDisplayLabel: "Boot Cut",
              },
            ],
          },
        },
      }));

      await new Promise(setImmediate);

      expect(setItem).toHaveBeenCalledWith(
        "facetQueryString",
        "style=Skinny | style=Boot Cut | banner=banner"
      );
    });

    it("sessionStorage not available", async () => {
      Object.defineProperty(window, "sessionStorage", {
        value: null,
        writable: true,
      });
      const wrapper = mediatorWrapper();

      wrapper.setState((current: Facets) => ({
        ...current,
        facetQueryString: "banner=banner",
        facets: {
          appliedFacets: {
            style: [
              {
                name: "Skinny",
              },
              {
                tagDisplayLabel: "Boot Cut",
              },
            ],
          },
        },
      }));
      const engagementsString = jest.fn();
      jest.mock(
        "../../datalayer/events/facet-engagement/adapters",
        () => engagementsString
      );
      await new Promise(setImmediate);
      expect(engagementsString).not.toHaveBeenCalled();
    });

    it("does not setState", async () => {
      Object.defineProperty(window, "location", {
        value: { hash: "a=a&b=b" },
      });
      const wrapper = mediatorWrapper().instance();
      const setStateSpy = jest.spyOn(wrapper, "setState");
      wrapper.callFetchProducts(true, 1, "x");
      expect(setStateSpy).not.toHaveBeenCalled();
    });

    it("should send enableSwatchSort and sortSwatchesBy when isSwatchSortEnabled=true", async () => {
      mediatorWrapper({
        isSwitchContractEnabled: true,
        isSwatchSortEnabled: true,
        swatchSortBy: "test",
      });

      await new Promise(setImmediate); // flushes pending promises

      const [firstCall] = newContractProductFetcher.mock.calls;
      const enableSwatchSort = firstCall[1].enableSwatchSort;
      const swatchSortBy = firstCall[1].sortSwatchesBy;

      expect(enableSwatchSort).toBeTruthy();
      expect(swatchSortBy).toBe("test");
    });

    it("should not send enableSwatchSort and sortSwatchesBy when isSwatchSortEnabled=false", async () => {
      mediatorWrapper({
        isSwitchContractEnabled: true,
        isSwatchSortEnabled: false,
        swatchSortBy: "",
      });

      await new Promise(setImmediate); // flushes pending promises

      const [firstCall] = newContractProductFetcher.mock.calls;
      const enableSwatchSort = firstCall[1].enableSwatchSort;
      const swatchSortBy = firstCall[1].sortSwatchesBy;

      expect(enableSwatchSort).toBeFalsy();
      expect(swatchSortBy).toBe("");
    });
  });

  describe("updateProductGroupsBySizeModel", () => {
    it("validate the size params", () => {
      const wrapper = mediatorWrapper().instance();
      wrapper.updateProductGroupsBySizeModel();
      expect(wrapper.state.avImage).toBe(undefined);
    });

    it("productGroup could be undefined", () => {
      const wrapper = mediatorWrapper({
        productGroups: undefined,
      }).instance();
      wrapper.updateProductGroupsBySizeModel();
      expect(wrapper.state.productGroups).toBe(undefined);
    });

    it("always set the sizeGroup state", () => {
      const wrapper = mediatorWrapper({
        productGroups: undefined,
        isModelToggleEnabled: true,
      }).instance();
      wrapper.updateProductGroupsBySizeModel("M");
      expect(wrapper.state.productGroups).toBe(undefined);
      expect(wrapper.state.sizeGroup).toBe("M");
    });

    it("productGroups could be an empty array", () => {
      const wrapper = mediatorWrapper({
        productGroups: [],
        isModelToggleEnabled: true,
      }).instance();
      wrapper.updateProductGroupsBySizeModel();
      expect(wrapper.state.productGroups).toEqual([]);
    });

    it("should call adapter with proper parameters", () => {
      const product1 = { avImages: {}, url: "product1_p1.jpg" };
      const product2 = { avImages: {}, url: "product2_p1.jpg" };
      const abbrBrand = "gap";
      const market = "us";
      const wrapper = mediatorWrapper({
        productGroups: [
          {
            products: [product1, product2],
          },
        ],
        isModelToggleEnabled: true,
        abbrBrand,
        market,
      }).instance();
      wrapper.updateProductGroupsBySizeModel("M");
      expect(adaptProductModelToggle).toHaveBeenCalledWith(
        expect.objectContaining({
          sizeGroup: "M",
          product: product1,
          position: 0,
          brand: abbrBrand,
          sizeGroups:
            defaultProps.featureVariables?.[`cat-model-toggle-${market}`]
              ?.sizeGroups,
        })
      );
      expect(adaptProductModelToggle).toHaveBeenCalledTimes(2);
    });
  });

  describe("analytics requirements", () => {
    it("should call facetEngagementDatalayerEvent", () => {
      const mockFn = jest.fn();
      const wrapper = mediatorWrapper().instance();

      facetEngagementDatalayerEvent.mockImplementationOnce(mockFn);

      wrapper.sendDataToAnalytics();
      expect(mockFn).toHaveBeenCalled();
    });

    it("should call facetEngagementDatalayerEvent with modelToggle data", () => {
      const mockFn = jest.fn();
      const wrapper = mediatorWrapper().instance();
      facetEngagementDatalayerEvent.mockImplementationOnce(mockFn);
      wrapper.sendDataToAnalytics({
        mixedGrid: false,
        sizeValue: "S",
      });
      expect(mockFn).toHaveBeenCalled();
      expect(mockFn).toHaveBeenCalledWith(
        undefined,
        undefined,
        expect.anything(),
        expect.anything(),
        undefined,
        [],
        { mixedGrid: false, sizeValue: "S" },
        ""
      );
    });

    it("should set initialProductResponse on window page state upon initial call", async () => {
      mediatorWrapper().instance();
      await new Promise(setImmediate);
      expect(window.__CATEGORY_PAGE_STATE__.initialProductResponse).toEqual(
        productsResponse
      );
    });
  });

  describe("getOptionalPreviewState - success", () => {
    it("returns empty object", async () => {
      const result = await getOptionalPreviewState();
      expect(result).toEqual({});
    });

    it("getOptionalPreviewState - failure", async () => {
      jest.spyOn(previewBar, "all").mockImplementationOnce(() => {
        throw new Error();
      });

      const consoleErrorSpy = jest.spyOn(console, "error");
      const result = await getOptionalPreviewState(true);
      expect(consoleErrorSpy).toHaveBeenCalledWith(
        "preview state failed to load",
        new Error()
      );
      expect(result).toEqual({});
    });
  });

  describe("callFetchProducts - reject", () => {
    beforeEach(() => {
      oldContractProductFetcher.mockReturnValue(Promise.reject({}));
    });
    it("handles rejected promise", async () => {
      window.location.hash = "sortByField=price&sortByDir=asc";
      const wrapper = mediatorWrapper({ errorLogger: () => false }).instance();
      const setStateSpy = jest.spyOn(wrapper, "setState");
      await wrapper.callFetchProducts("1234", false, "");
      expect(setStateSpy).toHaveBeenCalledWith({
        disablePlaceholders: false,
        error: "",
      });
    });
  });

  describe("products data contracts", () => {
    it("default - retrieves products using old contract", async () => {
      productFetcher();
      expect(oldContractProductFetcher).toHaveBeenCalled();
      expect(newContractProductFetcher).not.toHaveBeenCalled();
    });

    it("retrieves products using new contract", async () => {
      productFetcher(
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        true,
        null,
        null,
        null,
        null
      );
      expect(newContractProductFetcher).toHaveBeenCalled();
      expect(oldContractProductFetcher).not.toHaveBeenCalled();
    });
  });

  describe("cat-switch-contract ff is enabled", () => {
    beforeEach(() => {
      jest.clearAllMocks();

      newContractProductFetcher.mockReturnValue(
        Promise.resolve(productsResponse)
      );
    });

    const departmentCategoryProps = {
      children: [],
      customUrl: "/browse/category.do?cid=6359#pageId=0&department=2180",
      hasSubDivision: false,
      hidden: false,
      id: "6359",
      link: "/browse/category.do?cid=6359",
      name: "Jeans",
      parents: [],
      selected: true,
      type: "category",
    };

    it("should not change department when value is not mapped", () => {
      const customProps = {
        enabledFeatures: {
          "cat-switch-contract": true,
        },
        currentCategory: {
          ...departmentCategoryProps,
          customUrl: "/browse/category.do?cid=6359#pageId=0&department=9999",
        },
      };
      const wrapper = mediatorWrapper(customProps).instance();
      expect(wrapper.latestQueryString).toContain("department=9999");
    });
  });

  describe("handleCustomParamsAndFacetsParams ff variable is enabled", () => {
    describe("should combine all search params from customUrl and query param", () => {
      it("should include pageId and facetOrder with style", () => {
        const wrapper = mediatorWrapper({
          currentCategory,
          params,
          handleCustomParamsAndFacetsParams: true,
        }).instance();
        expect(wrapper.props.currentCategory.customUrl).toContain("pageId=0");
        expect(wrapper.latestQueryString).toEqual(
          "pageId=0&department=165&style=1113004&facetOrder=style:1113004"
        );
      });

      it("should combine style and department from hash and query param when handleCustomParamsAndFacetsParams ff is on", () => {
        window.location.hash = "#department=165";

        const wrapper = mediatorWrapperWithStyle({ handleCustomParamsAndFacetsParams: true }).instance();
        expect(wrapper.latestQueryString).toEqual("department=165&style=1113004&facetOrder=department:165,style:1113004");

        window.location.hash = "";
      });

      it("when handleCustomParamsAndFacetsParams ff is on, should handle the facetOrder param properly from hash when the facetOrder includes price", () => {
        window.location.hash = "#department=136&price=0-35&facetOrder=department:136,price";

        const wrapper = mediatorWrapperWithStyle({ handleCustomParamsAndFacetsParams: true }).instance();
        expect(wrapper.latestQueryString).toEqual("department=136&price=0-35&style=1113004&facetOrder=department:136,price,style:1113004");

        window.location.hash = "";
      });

      it("when handleCustomParamsAndFacetsParams ff is on, should prioritize the params in the customUrl except we prioritize the 'pageId' param from the request URL", () => {
        window.location.hash = "#department=136&price=0-35&pageId=2&facetOrder=department:136,price";

        const wrapper = mediatorWrapper({
          currentCategory: {
            ...currentCategory,
            customUrl:
              "/browse/category.do?cid=6359#department=136&color=12345&pageId=0&facetOrder=color:12345",
          },
          params: { indexableFacets: [sleeveLengthFacetData] },
          handleCustomParamsAndFacetsParams: true,
        }).instance();

        expect(wrapper.latestQueryString).toEqual(
          "color=12345&department=136&pageId=2&price=0-35&sleeveLength=1928&facetOrder=color:12345,price,sleeveLength:1928"
        );

        window.location.hash = "";
      });

      it("should ONLY include customUrl color when we have color in both customUrl and query params", () => {
        const wrapper = mediatorWrapper({
          currentCategory: {
            ...currentCategory,
            customUrl:
              "/browse/category.do?cid=6359#department=165&color=12345",
          },
          params: { indexableFacets: [colorFacetData] },
          handleCustomParamsAndFacetsParams: true,
        }).instance();
        expect(wrapper.latestQueryString).toEqual("department=165&color=12345");
      });

      it("should ONLY include facetOrder from customUrl when there are no facet query params AND facetOrder exists in customUrl", () => {
        const wrapper = mediatorWrapper({
          currentCategory: {
            ...currentCategory,
            customUrl:
              "/browse/category.do?cid=6359#department=165&facetOrder=department:165",
          },
          params: { indexableFacets: [] },
          handleCustomParamsAndFacetsParams: true,
        }).instance();
        expect(wrapper.latestQueryString).toEqual(
          "department=165&facetOrder=department:165"
        );
      });

      it("should ONLY include facetOrder from query param when there is facet query param AND facetOrder does NOT exist in customUrl", () => {
        const wrapper = mediatorWrapper({
          currentCategory: {
            ...currentCategory,
            customUrl:
              "/browse/category.do?cid=6359#department=165&color=12345",
          },
          params: { indexableFacets: [sleeveLengthFacetData] },
          handleCustomParamsAndFacetsParams: true,
        }).instance();
        expect(wrapper.latestQueryString).toEqual(
          "department=165&color=12345&sleeveLength=1928&facetOrder=sleeveLength:1928"
        );
      });

      it("should NOT include facetOrder when there is NO facet query param AND facetOrder does NOT exist in customUrl", () => {
        const wrapper = mediatorWrapper({
          currentCategory: {
            ...currentCategory,
            customUrl:
              "/browse/category.do?cid=6359#department=165&color=12345",
          },
          params: { indexableFacets: [] },
          handleCustomParamsAndFacetsParams: true,
        }).instance();
        expect(wrapper.latestQueryString).toEqual("department=165&color=12345");
      });

      it("should combine the facetOrder from customUrl and facet query params if they both exist", () => {
        const wrapper = mediatorWrapper({
          currentCategory: {
            ...currentCategory,
            customUrl:
              "/browse/category.do?cid=6359#department=165&color=12345&facetOrder=color:12345",
          },
          params: { indexableFacets: [sleeveLengthFacetData] },
          handleCustomParamsAndFacetsParams: true,
        }).instance();
        expect(wrapper.latestQueryString).toEqual(
          "color=12345&department=165&sleeveLength=1928&facetOrder=color:12345,sleeveLength:1928"
        );
      });
    });
  });
});
