import type { CID } from '@ecom-next/marketing-ui/fetch';
import marketing from './marketing-data.json';

export const shipToBagData = {
  panels: {
    bagPanel: {
      lineItems: [
        {
          brand: 'GP',
          productSkuId: '6047960221105',
          productName: 'Kids Heavyweight Washed T-Shirt',
          productTypeName: 'boys long-sleeved tees',
          productStyleId: '604796',
          color: 'union blue',
          size: 'S (6/7)',
          price: { regularPrice: 34.95, salePrice: 34.95, discountedPrice: 17.47, mupPromoPrice: 34.95 },
          regularPrice: '34.95',
          salePrice: '34.95',
          discountedPrice: '17.47',
          totalPrice: '17.47',
          quantity: '1',
          imagePath: 'webcontent/0056/841/043/cn56841043.jpg',
          inventoryStatus: 'RESERVED',
          appliedDiscounts: [
            {
              code: 'EMPDISCOUNT',
              id: '1051937',
              promoDiscountTotal: 17.48,
              displayName: 'Employee Discount',
              promotionDescription:
                'Gap: Your employee discount will only be applied to items from the brand you are currently shopping. Other exclusions apply.',
              automatic: true,
              shippingPromo: false,
              promoType: 'LINE_ITEM_PRICE_DISCOUNT',
            },
          ],
          storeId: '',
        },
      ],
      bopisItems: [],
      bagTotal: 17.47,
    },
    checkoutPanel: {
      conditionals: {
        hasBopisItems: false,
        isBopisOnlyBag: false,
        isBopisGiftCardPurchase: false,
        bopisInBag: false,
        isInvalidBagError: false,
        isOrderLineItems: true,
        shouldDonationsDisplay: false,
        isSummaryViewEnabled: true,
        cvvInvalidErrors: false,
        cardProcessingError: false,
        isShippingMethods: true,
        isShippingAddress: true,
        isShippingAddressSelected: true,
        isPayment: false,
        isOrderReadyToPlace: true,
      },
      totalPrice: 26.49,
      lineItems: [
        {
          mergeType: 'Standard PDP',
          itemId: 'a8e845be88dc4ff3aee23cf594aec8ab',
          brand: 'GP',
          productSkuId: '6047960221105',
          productName: 'Kids Heavyweight Washed T-Shirt',
          customerChoiceNumber: '604796022',
          productTypeName: 'boys long-sleeved tees',
          productStyleId: '604796',
          color: 'union blue',
          size: 'S (6/7)',
          colorStyleNumber: '604796022',
          variantDescription: 'Regular',
          merchandiseType: '7',
          inventoryStatusId: 5,
          isBackOrderItem: false,
          backOrdershippingDate: false,
          autoAdded: false,
          noReturnItem: false,
          returnByMailItem: false,
          noIntlShipping: true,
          marketingFlag: '0',
          giftWrappable: true,
          productStyleDescription: 'Kids Heavyweight Washed T-Shirt',
          primaryCategoryName: 'T-Shirts',
          productURL: 'browse/product.do?pid=6047960221105',
          price: { regularPrice: 34.95, salePrice: 34.95, discountedPrice: 17.47, percentageOff: 0 },
          regularPrice: '34.95',
          salePrice: '34.95',
          discountedPrice: '17.47',
          totalPrice: '17.47',
          quantity: '1',
          imagePath: 'webcontent/0056/841/043/cn56841043.jpg',
          inventoryStatus: 'RESERVED',
          vendorId: '000002910',
          webVendorName: 'SHINWON CORPORATION',
          showSellerName: false,
          excludedFromPromotion: false,
          madeToOrder: false,
          estimatedShippingDate: '',
          eligibleReturnLocationCode: '',
          isExcludedFromRewardFreeShipping: false,
          vendorStyleNumber: '',
          vendorUPCCode: '',
          appliedDiscounts: [
            {
              code: 'EMPDISCOUNT',
              id: '1051937',
              promoDiscountTotal: 17.48,
              displayName: 'Employee Discount',
              promotionDescription:
                'Gap: Your employee discount will only be applied to items from the brand you are currently shopping. Other exclusions apply.',
              automatic: true,
              shippingPromo: false,
              promoType: 'LINE_ITEM_PRICE_DISCOUNT',
            },
          ],
          storeId: '',
        },
      ],
      totalSavings: 17.48,
      locale: 'en_US',
      bagType: { bopisOnlyBag: false, omniBag: false, shipToHomeBag: true },
      pickupOrderType: 'shipItemsOnly',
      summaryView: { shippingAddressPanel: 'C', deliveryGroupPanel: 'E', pickupPanel: 'H', paymentPanel: 'R', rewardsPanel: 'E', showDonationPanel: false },
      checkoutModuleStatus:
        'shippingAddressPanel:collapsed|deliveryGroupPanel:expanded|pickupPanel:hidden|paymentPanel:required|rewardsPanel:expanded|donationPanel:hidden',
    },
    donationPanel: {},
    giftCardPanel: {
      totalPrice: 26.49,
      appliedGiftCards: [],
      errors: [],
      conditionals: { hasGiftCardsApplied: false },
      isBopisGiftCardPurchase: false,
      isGiftcardPanelExpanded: false,
      isFormShown: true,
    },
    orderSummaryPanel: {
      isOrdersummarylineItemEnabled: true,
      hasPromotion: true,
      promotionDescription: 1,
      isBopisOnly: false,
      subTotal: 34.95,
      hasShippingPrice: true,
      shippingPrice: 7,
      shippingMessage: '(5-7 business days)',
      hasSavings: true,
      totalSavings: -17.48,
      hasRewards: false,
      rewardsSubTotal: 0,
      giftCardsTotal: 0,
      hasGiftCards: false,
      estimatedTax: 2.02,
      hasDonations: false,
      donationsTotal: 0,
      totalPrice: '26.49',
      retailDeliveryFee: 0,
      markdownPromoSavings: 17.48,
      rewardsSavings: 0,
      isAfterpaySelected: false,
    },
    paymentPanel: {
      paymentMethods: [
        {
          paymentId: 'c43fcc10-7db0-47b7-b00f-f6a1399ef6d2',
          paymentIndex: 1,
          creditCard: {
            cardType: '4',
            type: 'DISCOVER',
            cardToken: 'some-card-token-value',
            cardTokenFormat: 'VaultId',
            expiryMonth: '02',
            expiryYear: '2028',
            lastFourDigits: '4082',
            billingAddress: {
              firstName: 'Kundan',
              lastName: 'Keshidi',
              addressLine1: '7421 Frankford Rd Apt 1734',
              city: 'Dallas',
              state: 'TX',
              country: 'US',
              postalCode: '75252-8195',
              phone: '2149852610',
              verificationStatus: 'NOT_VERIFIED',
              addressId: '5e8208e3ac6643b5a385a4914050db9f',
              deliveryPointValidation: 'INVALID_DELIVERY_POINT',
              default: false,
            },
            cvvValidated: false,
            cvvRequired: true,
            temporary: false,
            defaultCard: true,
            isPLCC: false,
          },
          errorList: [
            { errorCode: '710', moreInfo: '/swagger-ui.html', userMessage: 'For your security, please re-enter your credit card information to proceed.' },
          ],
          isSelected: true,
          isDefault: true,
          cardInfo: {
            cardId: '3E6DE6C36BD594E0CAF05C11073758CA',
            cardNumber: 'XXXX XXXX XXXX 4082',
            expirationMonth: '02',
            expirationYear: '2028',
            lastFourDigits: '4082',
            isCvvValidated: false,
            billingAddressId: '5e8208e3ac6643b5a385a4914050db9f',
            saveCCInProfile: true,
            temporaryCardIndicator: false,
            isPLCC: false,
            cardType: 4,
            cvvRequired: true,
            defaultCard: true,
          },
          billingAddress: {
            addressId: '5e8208e3ac6643b5a385a4914050db9f',
            firstName: 'Kundan',
            lastName: 'Keshidi',
            addressLine1: '7421 Frankford Rd Apt 1734',
            postalCode: '75252-8195',
            city: 'Dallas',
            state: 'TX',
            country: 'US',
            phone: '2149852610',
            isSelected: false,
            isDefault: false,
          },
          isCardExpired: false,
        },
        {
          isSelected: false,
          isDefault: false,
          cardInfo: {
            cardId: '5C8FBD47270F314FE67801B69C2BFACB',
            cardNumber: 'XXXX XXXX XXXX 6781',
            expirationMonth: '07',
            expirationYear: '2026',
            lastFourDigits: '6781',
            isCvvValidated: false,
            billingAddressId: '7b1e5e073da14955800e9878a6de8548',
            saveCCInProfile: true,
            cardBrand: '',
            temporaryCardIndicator: false,
            creditCardTier: '',
            brand: '',
            cardType: 4,
          },
          billingAddress: {
            addressId: '7b1e5e073da14955800e9878a6de8548',
            firstName: 'Kundan',
            lastName: 'Keshidi',
            addressLine1: '7421 Frankford Rd Apt 1734',
            postalCode: '75252-8195',
            city: 'Dallas',
            state: 'TX',
            country: 'US',
            phone: '2149852610',
            isSelected: false,
            isDefault: false,
          },
          isCardExpired: false,
        },
      ],
      paymentId: 'c43fcc10-7db0-47b7-b00f-f6a1399ef6d2',
      validGiftCardSubTotal: 0,
      shippingAddresses: [
        {
          addressId: 'd6fa8da4b2f0442a8a7c916a84d3dd31',
          firstName: 'Kundan',
          lastName: 'Keshidi',
          addressLine1: '7421 Frankford Road',
          addressLine2: '2614',
          postalCode: '75252-8152',
          city: 'Dallas',
          state: 'TX',
          country: 'US',
          phone: '2149852610',
          isSelected: true,
          isDefault: true,
        },
        {
          addressId: 'd6fa8da4b2f0442a8a7c916a84d3dd31',
          firstName: 'Kundan',
          lastName: 'Keshidi',
          addressLine1: '7421 Frankford Road',
          addressLine2: '2614',
          city: 'Dallas',
          state: 'TX',
          country: 'US',
          postalCode: '75252-8152',
          phone: '2149852610',
          defaultAddress: true,
          default: true,
          isSelected: true,
          verificationStatus: 'NOT_VERIFIED',
          deliveryPointValidation: 'INVALID_DELIVERY_POINT',
        },
        {
          addressId: '1020a158ae0b43ef9e2f29860aa953d5',
          firstName: 'Kundan',
          lastName: 'Keshidi',
          addressLine1: '5508 Barrique Blvd',
          city: 'Mckinney',
          state: 'TX',
          country: 'US',
          postalCode: '75070-2562',
          phone: '2149852610',
          defaultAddress: false,
          default: false,
          isSelected: false,
          verificationStatus: 'VERIFIED',
          deliveryPointValidation: 'CONFIRMED_DELIVERY_POINT',
        },
        {
          addressId: '6b262307477649e9a7c613e3187748a2',
          firstName: 'Kundan',
          lastName: 'Keshidi',
          addressLine1: '7421 Frankford Rd Apt 1734',
          city: 'Dallas',
          state: 'TX',
          country: 'US',
          postalCode: '75252-8195',
          phone: '2149852610',
          defaultAddress: false,
          default: false,
          isSelected: false,
          verificationStatus: 'VERIFIED',
          deliveryPointValidation: 'CONFIRMED_DELIVERY_POINT',
        },
      ],
      billingAddress: {
        addressId: '5e8208e3ac6643b5a385a4914050db9f',
        firstName: 'Kundan',
        lastName: 'Keshidi',
        addressLine1: '7421 Frankford Rd Apt 1734',
        postalCode: '75252-8195',
        city: 'Dallas',
        state: 'TX',
        country: 'US',
        phone: '2149852610',
        isSelected: false,
        isDefault: false,
      },
      billingErrors: [],
      totalPrice: 26.49,
      conditionals: {
        bopisOnlyBag: false,
        shippingAddressSelected: true,
        omniBag: false,
        hasRewards: false,
        isOrderTotalZero: false,
        isPhoneError: false,
        hasBillingAddressId: true,
        hasBopisItems: false,
        hasShipToAddressItems: true,
        isBopisGiftCardPurchase: false,
        showGiftCard: true,
        hasStoreName: false,
        cvvInvalidErrors: true,
        showPaypalButton: true,
        cardProcessingError: false,
        paymentFailedError: false,
        isAfterPayProcessingError: false,
        isKlarnaProcessingError: false,
        isGiftCardProcessingError: false,
        isPaymentPanelError: true,
        isPaypalProcessingError: false,
        isPaypalSelected: false,
        isAfterpaySelected: false,
        isPaypalButtonEnabled: true,
        isPaypalDisplayed: true,
        showAfterPayButton: false,
        isGiftCardCoversOrder: false,
        isLegacyRewardsCodeDisplayed: false,
        isBarclayCodeEnabled: true,
        isBarclaySavingCalculatorEnabled: true,
        isUpsAccessPoint: false,
        isPaymentModalEnabled: true,
      },
      errors: ['710'],
      selectedAddress: {
        addressId: 'd6fa8da4b2f0442a8a7c916a84d3dd31',
        firstName: 'Kundan',
        lastName: 'Keshidi',
        addressLine1: '7421 Frankford Road',
        addressLine2: '2614',
        postalCode: '75252-8152',
        city: 'Dallas',
        state: 'TX',
        country: 'US',
        phone: '2149852610',
        isSelected: true,
        isDefault: true,
      },
      summaryView: { cardNumber: 'XXXX XXXX XXXX 4082', cardType: 4, lastFourDigits: '4082', giftCards: [] },
      isCardInValid: false,
      selectedPaymentMethod: {
        paymentId: 'c43fcc10-7db0-47b7-b00f-f6a1399ef6d2',
        paymentIndex: 1,
        creditCard: {
          cardType: '4',
          type: 'DISCOVER',
          cardToken: 'some-card-token-value',
          cardTokenFormat: 'VaultId',
          expiryMonth: '02',
          expiryYear: '2028',
          lastFourDigits: '4082',
          billingAddress: {
            firstName: 'Kundan',
            lastName: 'Keshidi',
            addressLine1: '7421 Frankford Rd Apt 1734',
            city: 'Dallas',
            state: 'TX',
            country: 'US',
            postalCode: '75252-8195',
            phone: '2149852610',
            verificationStatus: 'NOT_VERIFIED',
            addressId: '5e8208e3ac6643b5a385a4914050db9f',
            deliveryPointValidation: 'INVALID_DELIVERY_POINT',
            default: false,
          },
          cvvValidated: false,
          cvvRequired: true,
          temporary: false,
          defaultCard: true,
          isPLCC: false,
        },
        errorList: [
          { errorCode: '710', moreInfo: '/swagger-ui.html', userMessage: 'For your security, please re-enter your credit card information to proceed.' },
        ],
        isSelected: true,
        isDefault: true,
        cardInfo: {
          cardId: '3E6DE6C36BD594E0CAF05C11073758CA',
          cardNumber: 'XXXX XXXX XXXX 4082',
          expirationMonth: '02',
          expirationYear: '2028',
          lastFourDigits: '4082',
          isCvvValidated: false,
          billingAddressId: '5e8208e3ac6643b5a385a4914050db9f',
          saveCCInProfile: true,
          temporaryCardIndicator: false,
          isPLCC: false,
          cardType: 4,
          cvvRequired: true,
          defaultCard: true,
        },
        billingAddress: {
          addressId: '5e8208e3ac6643b5a385a4914050db9f',
          firstName: 'Kundan',
          lastName: 'Keshidi',
          addressLine1: '7421 Frankford Rd Apt 1734',
          postalCode: '75252-8195',
          city: 'Dallas',
          state: 'TX',
          country: 'US',
          phone: '2149852610',
          isSelected: false,
          isDefault: false,
        },
        isCardExpired: false,
      },
    },
    paypalLightBoxPanel: {
      flow: 'checkout',
      intent: 'order',
      displayName: 'Gap Inc',
      enableShippingAddress: true,
      shippingAddressEditable: false,
      amount: 26.49,
      currency: 'USD',
      locale: 'en_US',
      shippingAddressOverride: {
        recipientName: 'Kundan Keshidi',
        line1: '7421 Frankford Road',
        city: 'Dallas',
        state: 'TX',
        postalCode: '75252-8152',
        countryCode: 'US',
      },
    },
    pickupPanel: { storePickupInfoList: [], summaryView: { stores: [], totalQuantity: 0 } },
    placeOrderPanel: {
      appliedPromotions: [
        {
          code: 'EMPDISCOUNT',
          id: '1051937',
          promoDiscountTotal: 17.48,
          displayName: 'Employee Discount',
          promotionDescription: 'Gap: Your employee discount will only be applied to items from the brand you are currently shopping. Other exclusions apply.',
          automatic: true,
          shippingPromo: false,
          plcc: false,
          orderLevel: false,
          tenderPromo: false,
        },
      ],
      bopis_order_type: 'shipItemsOnly',
      currencyCode: 'USD',
      donationTotal: '',
      totalPrice: '26.49',
      subTotal: 34.95,
      shippingChargesSubTotal: 7,
      rewardsSubTotal: '',
      promoSubTotal: '',
      emailOptInIndicator: false,
      cvv: '',
      giftCards: [],
      giftCardSubTotal: '',
      isBopisOnlyBag: false,
      merchandiseSubTotal: 34.95,
      estimatedTax: 2.02,
      placeOrderErrors: [{ errorCode: '710', userMessage: 'For your security, please re-enter your credit card information to proceed.' }],
      customer: { isGuest: false, isLoggedIn: true, isRecognized: false },
      conditionals: { isPopupError: false },
      shippingMethods: [
        {
          shippingMethodName: 'No Rush',
          shippingTypeDescription: '7-9 business days',
          shippingPrice: 5,
          shippingTypeId: 7,
          isSelected: false,
          isEnabled: true,
          shippingId: 10948,
          deliveryDate: 'Mar 20th',
          deliveryWeekDay: 'Thursday',
          maxDays: 9,
          minDays: 7,
        },
        {
          shippingMethodName: 'Basic',
          shippingTypeDescription: '5-7 business days',
          shippingPrice: 7,
          shippingTypeId: 7,
          isSelected: true,
          isEnabled: true,
          shippingId: 11113,
          deliveryDate: 'Mar 18th',
          deliveryWeekDay: 'Tuesday',
          maxDays: 7,
          minDays: 5,
        },
        {
          shippingMethodName: 'Standard',
          shippingTypeDescription: '3-5 business days',
          shippingPrice: 7,
          shippingTypeId: 1,
          isSelected: false,
          isEnabled: true,
          shippingId: 11118,
          deliveryDate: 'Mar 14th',
          deliveryWeekDay: 'Friday',
          maxDays: 5,
          minDays: 3,
        },
        {
          shippingMethodName: 'Express',
          shippingTypeDescription: '2-3 business days',
          shippingPrice: 17,
          shippingTypeId: 3,
          isSelected: false,
          isEnabled: true,
          shippingId: 11129,
          deliveryDate: 'Mar 12th',
          deliveryWeekDay: 'Wednesday',
          maxDays: 3,
          minDays: 2,
        },
      ],
      orderItems: [
        {
          appliedDiscounts: [
            {
              code: 'EMPDISCOUNT',
              id: '1051937',
              promoDiscountTotal: 17.48,
              displayName: 'Employee Discount',
              promotionDescription:
                'Gap: Your employee discount will only be applied to items from the brand you are currently shopping. Other exclusions apply.',
              automatic: true,
              shippingPromo: false,
              promoType: 'LINE_ITEM_PRICE_DISCOUNT',
            },
          ],
          color: 'union blue',
          imagePath: 'webcontent/0056/841/043/cn56841043.jpg',
          inventoryStatus: 'RESERVED',
          price: { discountedPrice: 17.47, regularPrice: 34.95, salePrice: 34.95 },
          productName: 'Kids Heavyweight Washed T-Shirt',
          productSkuId: '6047960221105',
          productStyleId: '604796',
          productTypeName: 'Kids Heavyweight Washed T-Shirt',
          quantity: 1,
          size: 'S (6/7)',
          totalPrice: 17.47,
        },
      ],
      shippingAddresses: [
        {
          addressId: 'd6fa8da4b2f0442a8a7c916a84d3dd31',
          firstName: 'Kundan',
          lastName: 'Keshidi',
          addressLine1: '7421 Frankford Road',
          addressLine2: '2614',
          postalCode: '75252-8152',
          city: 'Dallas',
          state: 'TX',
          country: 'US',
          phone: '2149852610',
          isDefault: true,
          addressLocationType: '',
          verificationStatus: 'NOT_VERIFIED',
          deliveryPointValidation: 'INVALID_DELIVERY_POINT',
          isSelected: true,
        },
        {
          addressId: '1020a158ae0b43ef9e2f29860aa953d5',
          firstName: 'Kundan',
          lastName: 'Keshidi',
          addressLine1: '5508 Barrique Blvd',
          addressLine2: '',
          postalCode: '75070-2562',
          city: 'Mckinney',
          state: 'TX',
          country: 'US',
          phone: '2149852610',
          isDefault: false,
          addressLocationType: '',
          verificationStatus: 'VERIFIED',
          deliveryPointValidation: 'CONFIRMED_DELIVERY_POINT',
          isSelected: false,
        },
        {
          addressId: '6b262307477649e9a7c613e3187748a2',
          firstName: 'Kundan',
          lastName: 'Keshidi',
          addressLine1: '7421 Frankford Rd Apt 1734',
          addressLine2: '',
          postalCode: '75252-8195',
          city: 'Dallas',
          state: 'TX',
          country: 'US',
          phone: '2149852610',
          isDefault: false,
          addressLocationType: '',
          verificationStatus: 'VERIFIED',
          deliveryPointValidation: 'CONFIRMED_DELIVERY_POINT',
          isSelected: false,
        },
      ],
      paymentMethods: [
        {
          paymentId: 'c43fcc10-7db0-47b7-b00f-f6a1399ef6d2',
          paymentIndex: 1,
          creditCard: {
            cardType: '4',
            type: 'DISCOVER',
            cardToken: 'some-card-token-value',
            cardTokenFormat: 'VaultId',
            expiryMonth: '02',
            expiryYear: '2028',
            lastFourDigits: '4082',
            billingAddress: {
              firstName: 'Kundan',
              lastName: 'Keshidi',
              addressLine1: '7421 Frankford Rd Apt 1734',
              city: 'Dallas',
              state: 'TX',
              country: 'US',
              postalCode: '75252-8195',
              phone: '2149852610',
              verificationStatus: 'NOT_VERIFIED',
              addressId: '5e8208e3ac6643b5a385a4914050db9f',
              deliveryPointValidation: 'INVALID_DELIVERY_POINT',
              default: false,
            },
            cvvValidated: false,
            cvvRequired: true,
            temporary: false,
            defaultCard: true,
            isPLCC: false,
          },
          errorList: [
            { errorCode: '710', moreInfo: '/swagger-ui.html', userMessage: 'For your security, please re-enter your credit card information to proceed.' },
          ],
          isSelected: true,
          isDefault: true,
          cardInfo: {
            cardId: '3E6DE6C36BD594E0CAF05C11073758CA',
            cardNumber: 'XXXX XXXX XXXX 4082',
            expirationMonth: '02',
            expirationYear: '2028',
            lastFourDigits: '4082',
            isCvvValidated: false,
            billingAddressId: '5e8208e3ac6643b5a385a4914050db9f',
            saveCCInProfile: true,
            temporaryCardIndicator: false,
            isPLCC: false,
            cardType: 4,
            cvvRequired: true,
            defaultCard: true,
          },
          billingAddress: {
            addressId: '5e8208e3ac6643b5a385a4914050db9f',
            firstName: 'Kundan',
            lastName: 'Keshidi',
            addressLine1: '7421 Frankford Rd Apt 1734',
            postalCode: '75252-8195',
            city: 'Dallas',
            state: 'TX',
            country: 'US',
            phone: '2149852610',
            isSelected: false,
            isDefault: false,
          },
          isCardExpired: false,
        },
      ],
    },
    rewardsPanel: {
      conditionals: {
        isRewardsPanelDisplayed: true,
        isPartialResponse: false,
        isPromoRewardsApplied: true,
        isCvvError: false,
        isPromotionError: false,
        hasErrors: false,
      },
      brand: 'GP',
      market: 'US',
      isGuest: false,
      promoRewardsList: [
        {
          promoId: '1051937',
          rewardCode: 'EMPDISCOUNT',
          rewardType: 'promoReward',
          isApplied: true,
          displayName: 'Employee Discount',
          autoApplied: true,
          promoDescription: 'Gap: Your employee discount will only be applied to items from the brand you are currently shopping. Other exclusions apply.',
          discountTotal: 17.48,
        },
      ],
      rewardErrorCode: '',
      tier: 'CORE',
      merchandiseSubTotal: 17.47,
      draftOrderId: '4c6f35d2-b939-4184-9fde-85a097b75732',
      pointRewards: {
        pointRewardsCard: {
          rewardType: 'pointRewards',
          availablePoints: 169,
          pointsToValueList: [{ points: 100, amount: 1 }],
          isApplied: false,
          hasEnoughPoints: true,
          lessThanHundred: 0,
          minPoints: 100,
        },
        actionParams: { loyaltyEventType: 'PayWithPoints', loyaltyTierStatus: 'CORE', reasonCode: 539 },
      },
      summaryView: { maxReward: 1, amountApplied: 0, rewardCodes: ['EMPDISCOUNT'] },
    },
    shippingAddressPanel: {
      conditionals: { isHubBoxEnabled: true },
      shippingAddressList: [
        {
          addressId: 'd6fa8da4b2f0442a8a7c916a84d3dd31',
          firstName: 'Kundan',
          lastName: 'Keshidi',
          addressLine1: '7421 Frankford Road',
          addressLine2: '2614',
          postalCode: '75252-8152',
          city: 'Dallas',
          state: 'TX',
          country: 'US',
          phone: '2149852610',
          isDefault: true,
          addressLocationType: '',
          verificationStatus: 'NOT_VERIFIED',
          deliveryPointValidation: 'INVALID_DELIVERY_POINT',
          isSelected: true,
        },
        {
          addressId: '1020a158ae0b43ef9e2f29860aa953d5',
          firstName: 'Kundan',
          lastName: 'Keshidi',
          addressLine1: '5508 Barrique Blvd',
          addressLine2: '',
          postalCode: '75070-2562',
          city: 'Mckinney',
          state: 'TX',
          country: 'US',
          phone: '2149852610',
          isDefault: false,
          addressLocationType: '',
          verificationStatus: 'VERIFIED',
          deliveryPointValidation: 'CONFIRMED_DELIVERY_POINT',
          isSelected: false,
        },
        {
          addressId: '6b262307477649e9a7c613e3187748a2',
          firstName: 'Kundan',
          lastName: 'Keshidi',
          addressLine1: '7421 Frankford Rd Apt 1734',
          addressLine2: '',
          postalCode: '75252-8195',
          city: 'Dallas',
          state: 'TX',
          country: 'US',
          phone: '2149852610',
          isDefault: false,
          addressLocationType: '',
          verificationStatus: 'VERIFIED',
          deliveryPointValidation: 'CONFIRMED_DELIVERY_POINT',
          isSelected: false,
        },
      ],
      lineItemCount: 1,
      bopisInBag: false,
      isOmniBag: false,
      deliveryGroupId: 'GapRegularGroup',
      hasSavedShippingAddresses: true,
      hasShippingAddressSelected: true,
      summaryView: { isUPSStore: false, fullName: 'Kundan Keshidi', fullAddress: '7421 Frankford Road, 2614, Dallas, TX, US 75252-8152' },
      formInitialValues: { fullName: 'Kundan Keshidi', phone: '(*************' },
      notEligibleShippingItems: [],
      hasNoEligibleShippingItems: false,
      shouldDisplayGifting: true,
      showUPS: true,
    },
    signInPanel: {
      isGuest: false,
      isLoggedIn: true,
      isRecognized: false,
      emailId: 'k*****@gap.com',
      lineItems: [
        {
          mergeType: 'Standard PDP',
          itemId: 'a8e845be88dc4ff3aee23cf594aec8ab',
          brand: 'GP',
          productSkuId: '6047960221105',
          productName: 'Kids Heavyweight Washed T-Shirt',
          customerChoiceNumber: '604796022',
          productTypeName: 'boys long-sleeved tees',
          productStyleId: '604796',
          color: 'union blue',
          size: 'S (6/7)',
          colorStyleNumber: '604796022',
          variantDescription: 'Regular',
          merchandiseType: '7',
          inventoryStatusId: 5,
          isBackOrderItem: false,
          backOrdershippingDate: false,
          autoAdded: false,
          noReturnItem: false,
          returnByMailItem: false,
          noIntlShipping: true,
          marketingFlag: '0',
          giftWrappable: true,
          productStyleDescription: 'Kids Heavyweight Washed T-Shirt',
          primaryCategoryName: 'T-Shirts',
          productURL: 'browse/product.do?pid=6047960221105',
          price: { regularPrice: 34.95, salePrice: 34.95, discountedPrice: 17.47, percentageOff: 0 },
          regularPrice: '34.95',
          salePrice: '34.95',
          discountedPrice: '17.47',
          totalPrice: '17.47',
          quantity: '1',
          imagePath: 'webcontent/0056/841/043/cn56841043.jpg',
          inventoryStatus: 'RESERVED',
          vendorId: '000002910',
          webVendorName: 'SHINWON CORPORATION',
          showSellerName: false,
          excludedFromPromotion: false,
          madeToOrder: false,
          estimatedShippingDate: '',
          eligibleReturnLocationCode: '',
          isExcludedFromRewardFreeShipping: false,
          vendorStyleNumber: '',
          vendorUPCCode: '',
          appliedDiscounts: [
            {
              code: 'EMPDISCOUNT',
              id: '1051937',
              promoDiscountTotal: 17.48,
              displayName: 'Employee Discount',
              promotionDescription:
                'Gap: Your employee discount will only be applied to items from the brand you are currently shopping. Other exclusions apply.',
              automatic: true,
              shippingPromo: false,
              promoType: 'LINE_ITEM_PRICE_DISCOUNT',
            },
          ],
          storeId: '',
        },
      ],
    },
    deliveryGroupPanel: {
      deliveryGroupLists: [
        {
          deliveryGroupId: 'GapRegularGroup',
          pickupOrderType: 'shipItemsOnly',
          lineItemList: [
            {
              mergeType: 'Standard PDP',
              itemId: 'a8e845be88dc4ff3aee23cf594aec8ab',
              brand: 'GP',
              productSkuId: '6047960221105',
              productName: 'Kids Heavyweight Washed T-Shirt',
              customerChoiceNumber: '604796022',
              productTypeName: 'boys long-sleeved tees',
              productStyleId: '604796',
              color: 'union blue',
              size: 'S (6/7)',
              colorStyleNumber: '604796022',
              variantDescription: 'Regular',
              merchandiseType: '7',
              inventoryStatusId: 5,
              isBackOrderItem: false,
              backOrdershippingDate: false,
              autoAdded: false,
              noReturnItem: false,
              returnByMailItem: false,
              noIntlShipping: true,
              marketingFlag: '0',
              giftWrappable: true,
              productStyleDescription: 'Kids Heavyweight Washed T-Shirt',
              primaryCategoryName: 'T-Shirts',
              productURL: 'browse/product.do?pid=6047960221105',
              price: { regularPrice: 34.95, salePrice: 34.95, discountedPrice: 17.47, percentageOff: 0 },
              regularPrice: '34.95',
              salePrice: '34.95',
              discountedPrice: '17.47',
              totalPrice: '17.47',
              quantity: '1',
              imagePath: 'webcontent/0056/841/043/cn56841043.jpg',
              inventoryStatus: 'RESERVED',
              vendorId: '000002910',
              webVendorName: 'SHINWON CORPORATION',
              showSellerName: false,
              excludedFromPromotion: false,
              madeToOrder: false,
              estimatedShippingDate: '',
              eligibleReturnLocationCode: '',
              isExcludedFromRewardFreeShipping: false,
              vendorStyleNumber: '',
              vendorUPCCode: '',
              appliedDiscounts: [
                {
                  code: 'EMPDISCOUNT',
                  id: '1051937',
                  promoDiscountTotal: 17.48,
                  displayName: 'Employee Discount',
                  promotionDescription:
                    'Gap: Your employee discount will only be applied to items from the brand you are currently shopping. Other exclusions apply.',
                  automatic: true,
                  shippingPromo: false,
                  promoType: 'LINE_ITEM_PRICE_DISCOUNT',
                },
              ],
              storeId: '',
            },
          ],
          offerDetails: "NOT IN A HURRY?.\nSelect this option to give us a few extra days. We'll still do our best to ship your purchase quickly.",
          isBackOrder: false,
          isMadeToOrder: false,
          isDropShipItem: false,
          webVendorName: '',
          shippingMethodList: [
            {
              shippingMethodName: 'No Rush',
              shippingTypeDescription: '7-9 business days',
              shippingPrice: 5,
              shippingTypeId: 7,
              isSelected: false,
              isEnabled: true,
              shippingId: 10948,
              deliveryDate: '20th',
              deliveryWeekDay: 'Thursday,',
              deliveryMonth: 'Mar',
              maxDays: 9,
              minDays: 7,
            },
            {
              shippingMethodName: 'Basic',
              shippingTypeDescription: '5-7 business days',
              shippingPrice: 7,
              shippingTypeId: 7,
              isSelected: true,
              isEnabled: true,
              shippingId: 11113,
              deliveryDate: '18th',
              deliveryWeekDay: 'Tuesday,',
              deliveryMonth: 'Mar',
              maxDays: 7,
              minDays: 5,
            },
            {
              shippingMethodName: 'Standard',
              shippingTypeDescription: '3-5 business days',
              shippingPrice: 7,
              shippingTypeId: 1,
              isSelected: false,
              isEnabled: true,
              shippingId: 11118,
              deliveryDate: '14th',
              deliveryWeekDay: 'Friday,',
              deliveryMonth: 'Mar',
              maxDays: 5,
              minDays: 3,
            },
            {
              shippingMethodName: 'Express',
              shippingTypeDescription: '2-3 business days',
              shippingPrice: 17,
              shippingTypeId: 3,
              isSelected: false,
              isEnabled: true,
              shippingId: 11129,
              deliveryDate: '12th',
              deliveryWeekDay: 'Wednesday,',
              deliveryMonth: 'Mar',
              maxDays: 3,
              minDays: 2,
            },
          ],
        },
      ],
      summaryView: [
        {
          id: 'GapRegularGroup',
          name: 'Basic',
          itemCount: 1,
          deliveryBy: 'By Tuesday, Mar 18th',
          deliveryWeekDay: 'Tuesday,',
          deliveryDate: '18th',
          deliveryMonth: 'Mar',
          shippingPrice: 7,
        },
      ],
      currency: 'USD',
      payments: [
        {
          paymentId: 'c43fcc10-7db0-47b7-b00f-f6a1399ef6d2',
          paymentIndex: 1,
          creditCard: {
            cardType: '4',
            type: 'DISCOVER',
            cardToken: 'some-card-token-value',
            cardTokenFormat: 'VaultId',
            expiryMonth: '02',
            expiryYear: '2028',
            lastFourDigits: '4082',
            billingAddress: {
              firstName: 'Kundan',
              lastName: 'Keshidi',
              addressLine1: '7421 Frankford Rd Apt 1734',
              city: 'Dallas',
              state: 'TX',
              country: 'US',
              postalCode: '75252-8195',
              phone: '2149852610',
              verificationStatus: 'NOT_VERIFIED',
              addressId: '5e8208e3ac6643b5a385a4914050db9f',
              deliveryPointValidation: 'INVALID_DELIVERY_POINT',
              default: false,
            },
            cvvValidated: false,
            cvvRequired: true,
            temporary: false,
            defaultCard: true,
            isPLCC: false,
          },
          errorList: [
            { errorCode: '710', moreInfo: '/swagger-ui.html', userMessage: 'For your security, please re-enter your credit card information to proceed.' },
          ],
        },
      ],
      isEasyEnrollEligible: false,
      errors: [],
      hasDropshipItems: false,
      has4101Error: false,
      markdownSubtotal: 34.95,
    },
  },
  session: {
    email: 'k*****@gap.com',
    recognition_status: 'authenticated',
  },
  viewTag: {
    lv2: '',
    event_name: 'Checkout',
    page_type: 'Checkout',
    bopis_enabled: false,
    bopis_order_type: 'shipItemsOnly',
    brand_code: 'GAP',
    brand_name: 'Gap',
    brand_number: '1',
    business_unit_abbr_name: 'GAP_US_OL',
    business_unit_description: 'Gap',
    business_unit_id: 1,
    brand_short_name: 'gp',
    channel: 'gp:checkout',
    country_code: 'US',
    checkout_type: 'Returning Customer',
    language_code: 'en_US',
    shipping_options: '5-7 business days',
    pfs_order_shipping_method: '',
    checkout_version: 'Checkout_MVP',
    tier_status: '',
    cardholder_status: 'BRNONE|GPNONE|ONNONE|ATNONE',
    customer_uuid: '02CE178C23A9462FA8F148D2621C3ED0',
    recognition_status: 'authenticated',
    encrypted_customer_email: '',
    encrypted_customer_email_mm: '',
    hashed_customer_email: '',
    mtl_member_status: '',
    category_preference: '',
    division_preference: '',
    product_id: ['604796'],
    product_cc_id: ['604796022'],
    product_brand: ['GAP'],
    brand_mix: 'GAP',
    product_category: ['T-Shirts'],
    product_name: ['Kids Heavyweight Washed T-Shirt'],
    product_quantity: [1],
    product_sku: ['6047960221105'],
    product_markdown_amount: ['0.00'],
    product_gross_retail: ['34.95'],
    product_gross_merchandise: ['34.95'],
    product_net_demand: ['17.47'],
    product_page_type: ['Standard PDP'],
    order_gross_merchandise: 34.95,
    product_dropship: ['false'],
    product_seller_id: ['1'],
    product_seller_name: ['GAP'],
  },
  draftOrderId: '4c6f35d2-b939-4184-9fde-85a097b75732',
  features: {
    experiments: {
      EXP_BARCLAY_US: true,
      EXP_BARCLAY_OCP_US: false,
      EXP_BARCLAY_SAVINGS: true,
      EXP_CANARY: false,
      EXP_DONATION_FEATURE: false,
      EXP_ROKT: true,
      EXP_GUEST_FREE_SHIPPING_BANNER: true,
      EXP_ENABLE_BOPIS_GIFT_CARD_CANADA: false,
      EXP_ENABLE_BOPIS_SMS_CANADA: false,
      EXP_PAYMENT_AUTOSELECT: false,
      EXP_ACCELERATED_US: false,
      EXP_ACCELERATED_CA: false,
      EXP_ACCELERATED_US_SP: true,
      EXP_LOYALTY_CA: false,
      EXP_LOYALTY_US: true,
      EXP_PAYMENTMODAL_US: true,
      EXP_PAYMENTMODAL_CA: false,
      EXP_ORDER_SUMMARY_US: true,
      EXP_ORDER_SUMMARY_CA: false,
      EXP_MARKDOWN: false,
      EXP_OPTIMIZELY_MIGRATION_TEST: false,
      EXP_ACCELERATED_PPE: true,
    },
    experimentVariables: { AFTERPAY_MIN_ORDER_AMT: 35, AFTERPAY_MAX_ORDER_AMT: 1000, GUEST_FREE_SHIPPING_THRESHOLD: 50 },
    killSwitches: {
      ENABLE_AFTERPAY: true,
      ENABLE_AFTER_PAY_BACK_ORDER: false,
      ENABLE_BOPIS_FLAG: true,
      ENABLE_BOPIS_GIFTCARD: true,
      ENABLE_BOPIS_OCP: true,
      ENABLE_BOPIS_PICKUP_PANEL: true,
      ENABLE_BOPIS_SHOPPINGBAG_PANEL: false,
      ENABLE_BOPIS_SMS_FLAG: true,
      ENABLE_GOOGLE_AUTOCOMPLETE: true,
      ENABLE_PAYPAL_BUTTON: true,
      ENABLE_PAYPAL_BUTTON_CANADA: true,
      ENABLE_HUBBOX: true,
      ENABLE_SHIPPING_AUTOSELECT: true,
    },
    localizedText: {
      ENABLE_LOCALIZED_TEXT: true,
      bopisTitle: 'Hello',
      shippingMethodFooterText: '',
      myRewardsPanelFooterText: 'Redeemed rewards can’t exceed the subtotal of your order (before tax and shipping).',
    },
    segmentList: ['roktX', 'gfsbX', 'accusspX', 'loyaltyus', 'ordersummaryusX', 'accppeX'],
  },
};

const API = 'http://api.gap.com';


/**
 * Mocks the page data required for testing checkout components.
 *
 * @param cid - The customer or context identifier (CID) used to determine specific mock data.
 * @param featureFlags - An optional object specifying feature flags to override or extend the default enabled features.
 * @returns An object containing promises that resolve to mock data for enabled features, static data, navigation data, marketing data, and PMC preview editions data.
 *
 */
export const getPageDataMock = (cid: CID, featureFlags: Record<string, boolean> = {}) => {
  return {
    enabledFeaturesPromise: Promise.resolve({
      enabledFeatures: {
        'bag-ui-loyalty': true,
        'bag-ui-bopis-enhacement': true,
        'bag-ui-popular-items-banner': true,
        'bag-ui-paypal': true,
        'bag-ui-oos': true,
        'bag-ui-oos-v2': true,
        'checkout-ui-paypal-button': true,
        ...featureFlags
      },
      featureVariables: {},
    }),
    staticDataPromise: Promise.resolve({
      abSeg: {},
      apis: {
        valueDrawerApi: API,
        loyaltyCreditCardEnrollmentApi: API,
        autoLoginApi: API,
        promoDrawerApi: API,
        oneTrustApi: API,
        emailRegistrationApi: API,
        shoppingBagApi: API,
        smsRegistrationApi: API,
        newSmsRegistrationApi: API,
        newEmailRegistrationApi: API,
      },
      engineEndpointConfiguration: {
        ProductSearch: {
          autosuggest: {
            url: {
              us: API,
            },
          },
        },
      },
      appConfig: {
        brandCodeUrls: {
          unsecureUrl: 'https://www.gap.com',
        },
      },
    }),
    navDataPromise: Promise.resolve({
      desktopNav: {
        children: [],
        topSearchTerms: [],
        brandSiteData: {},
      },
      mobileNav: {},
      otherNav: {},
      currentSubcategories: [],
    }),
    marketingDataPromise: cid === '2078' ? Promise.resolve(marketing) : {},
    pmcsPreviewEditionsDataPromise: Promise.resolve({}),
  };
};

// export function MarketingMock(props) {
//   const { schema, name } = props;
//   return (
//     <>
//       <span>Marketing schema: {schema}</span>
//       <span>content name: {name}</span>
//     </>
//   );
// }
