/* eslint-disable typescript-sort-keys/interface */
import React from 'react';
import { FeatureFlagsProvider } from '@ecom-next/core/legacy/feature-flags';
import { PageAppState, PageContextProvider } from '@ecom-next/sitewide/app-state-provider';
import { DraftOrder } from '../../../../contexts/types';
import { render, screen, waitFor } from '../../../../utils/test-utils';
import { PaymentState } from '../../../../contexts/PaymentsProvider';
import * as checkoutUIStateProviderModule from '../../../../contexts/CheckoutUiStateProvider';
import * as orderSummaryModule from '../../../panels/OrderSummary/OrderSummary';
import { CheckoutContainer } from '../CheckoutContainer';
import { CheckoutApp } from '../index';
import {
  guestUserPayload,
  authenticateUserPayload,
  withPaymentMethod,
  withGiftCardCoveredTotal,
  mockDraftOrderExpCardDefaultSelectedWithValidatedCard,
} from '../../../../utils/__mockData__/checkoutContainer-mock';
import { mockDraftOrderGuestUser } from '../../../../utils/__mockData__/draftorder-mock';
import { usePaymentError } from '../../../panels/PaymentPanel/utils/usePaymentError';

let paymentState: PaymentState;

jest.mock('../../../panels/PaymentPanel/utils/usePaymentError', () => ({
  usePaymentError: jest.fn().mockReturnValue({
    errorMessage: '',
    bannerType: '',
  }),
}));

jest
  .mock('next/navigation', () => ({
    useRouter: jest.fn().mockReturnValue({}),
  }))
  .mock('@ecom-next/checkout/PaymentsProvider', () => ({
    ...jest.requireActual('@ecom-next/checkout/PaymentsProvider'),
    usePaymentState: jest.fn().mockReturnValue(() => paymentState),
  }));

jest.mock('../../../../contexts/CheckoutUiStateProvider', () => ({
  __esModule: true,
  ...jest.requireActual('../../../../contexts/CheckoutUiStateProvider'),
  useCheckoutUiStates: jest.fn().mockReturnValue({
    setUiStates: jest.fn(),
    paymentPanelState: {
      cvv: '',
      selectedThirdPartyPayment: false,
    },
  }),
}));

jest.mock('@ecom-next/product/legacy-components/fulfillment-product-detail', () => ({
  __esModule: true,
  default: jest.fn(),
}));

function HubBoxContainer() {
  return <div>HubBoxContainer</div>;
}
jest.mock('../../../modules/HubBox/HubBoxContainer', () => HubBoxContainer);

jest.mock('../../../panels/RewardsPanel/RewardsPanel', () => ({
  __esModule: true,
  RewardsPanel: function RewardsPanelMock(): JSX.Element {
    return <h2>Points & Rewards</h2>;
  },
}));

jest.mock('../../../panels/DonationsPanel/DonationsPanel', () => ({
  __esmodule: true,
  DonationsPanel: jest.fn(),
}));

jest.mock('../../../panels/OrderSummary/OrderSummary', () => ({
  __esModule: true,
  OrderSummary: jest.fn(),
}));

describe('CheckoutApp', () => {
  let spyConsoleError: jest.SpyInstance;

  beforeAll(() => {
    spyConsoleError = jest.spyOn(globalThis.console, 'error').mockImplementation(() => void 0);
    paymentState = {
      inlineFormState: false,
      editClickState: false,
      thirdPartyClickState: false,
      selectedPaymentMethodState: {
        entityType: 'creditCard',
        id: null,
      },
      uiError: null,
    };
  });

  afterAll(() => {
    spyConsoleError.mockRestore();
  });

  it('should render', () => {
    expect(render(<CheckoutApp />).container).toBeDefined();
  });

  describe('Rewards', () => {
    it('guest user - should render Rewards', () => {
      const { queryByText } = render(
        <FeatureFlagsProvider enabledFeatures={{}}>
          <CheckoutContainer />
        </FeatureFlagsProvider>,
        guestUserPayload
      );
      const rewardsElement = queryByText('Points & Rewards');
      expect(rewardsElement).not.toBeInTheDocument();
    });

    it('authenticated user - should not render Rewards', async () => {
      const { findByText } = render(
        <FeatureFlagsProvider enabledFeatures={{}}>
          <CheckoutContainer />
        </FeatureFlagsProvider>,
        authenticateUserPayload
      );

      const title = await findByText('Points & Rewards');
      expect(title).toBeInTheDocument();
    });
  });

  describe('PaymentContainer', () => {
    describe('Guest user', () => {
      it('should not display Add Credit or Debit Card button as a link', () => {
        const { queryAllByText } = render(
          <FeatureFlagsProvider enabledFeatures={{}}>
            <CheckoutContainer />
          </FeatureFlagsProvider>,
          guestUserPayload
        );
        expect(screen.queryAllByText('Security Code')).toHaveLength(0);
        expect(queryAllByText('Add Credit or Debit Card')).toHaveLength(0);
      });

      it('should display Add Credit or Debit Card button as a link and throw error if bannerType is error and errorCode like 810', () => {
        paymentState = {
          inlineFormState: true,
          editClickState: false,
          thirdPartyClickState: false,
          selectedPaymentMethodState: {
            entityType: 'creditCard',
            id: null,
          },
          uiError: null,
        };

        (usePaymentError as jest.Mock).mockReturnValue({
          setUiStates: jest.fn(),
          errorMessage: `We're having trouble processing your credit card. Try reentering your information or use a different card.`,
          bannerType: 'error',
        });

        const { queryByText } = render(
          <FeatureFlagsProvider enabledFeatures={{}}>
            <CheckoutContainer />
          </FeatureFlagsProvider>,
          mockDraftOrderGuestUser as unknown as DraftOrder
        );
        const addCardElement = queryByText('Add Credit or Debit Card');
        const cvvInput = screen.getAllByTestId('card-security-code'); // If card requires CVV, it should be displayed with error message
        const errorMessageElement = queryByText(`We're having trouble processing your credit card. Try reentering your information or use a different card.`);
        expect(addCardElement).toBeInTheDocument();
        expect(errorMessageElement).toBeInTheDocument();
        expect(cvvInput).toHaveLength(1);
      });
    });

    describe('Authenticated user', () => {
      it('should not display Add Credit or Debit Card button as a link', async () => {
        const { queryByText } = render(
          <FeatureFlagsProvider enabledFeatures={{}}>
            <CheckoutContainer />
          </FeatureFlagsProvider>,
          authenticateUserPayload
        );
        const addCreditcardText = queryByText('Add Credit or Debit Card');
        expect(addCreditcardText).not.toBeInTheDocument();
      });

      it('should display Add Credit or Debit Card button as a link', () => {
        const { queryByText } = render(
          <FeatureFlagsProvider enabledFeatures={{}}>
            <CheckoutContainer />
          </FeatureFlagsProvider>,
          withPaymentMethod
        );
        const addCardElement = queryByText('Add Credit or Debit Card');
        expect(addCardElement).toBeInTheDocument();
      });

      it('should not display PaymentList if gift card covers total', () => {
        const { queryByText } = render(
          <FeatureFlagsProvider enabledFeatures={{}}>
            <CheckoutContainer />
          </FeatureFlagsProvider>,
          withGiftCardCoveredTotal
        );
        const addCardElement = queryByText('Add Credit or Debit Card');
        const creditCardElement = queryByText('4305', { exact: false });
        expect(addCardElement).not.toBeInTheDocument();
        expect(creditCardElement).not.toBeInTheDocument();
      });
      it('Should display security code and error on payment panel for expired card with default selected with cvvRequired as false', () => {
        paymentState = {
          inlineFormState: true,
          editClickState: false,
          thirdPartyClickState: false,
          selectedPaymentMethodState: {
            entityType: 'creditCard',
            id: '781D3F501513C68882D072F61D36EEE0',
          },
          uiError: null,
        };

        (usePaymentError as jest.Mock).mockReturnValue({
          setUiStates: jest.fn(),
          errorMessage: `The card you selected has expired. Enter a new expiration date or select a different payment method.`,
          bannerType: 'error',
        });

        const { queryByText } = render(
          <FeatureFlagsProvider enabledFeatures={{}}>
            <CheckoutContainer />
          </FeatureFlagsProvider>,
          mockDraftOrderExpCardDefaultSelectedWithValidatedCard as unknown as DraftOrder
        );
        const cvvInput = screen.getAllByTestId('card-security-code');
        const errorMessageElement = queryByText(`The card you selected has expired. Enter a new expiration date or select a different payment method.`);
        expect(errorMessageElement).toBeInTheDocument();
        expect(cvvInput).toHaveLength(1);
      });
    });
  });

  describe('Donation Panel', () => {
    it('guest user - should not render Donation Panel when feature is disabled', async () => {
      const appState = { market: 'us', brand: 'gp' };
      const { queryByTestId } = render(
        <PageContextProvider value={appState as PageAppState}>
          <FeatureFlagsProvider enabledFeatures={{ isDonationPanelEnabled: false }}>
            <CheckoutContainer />
          </FeatureFlagsProvider>
        </PageContextProvider>,
        guestUserPayload
      );
      const donationPanelElement = await queryByTestId('Donation-testid');
      expect(donationPanelElement).not.toBeInTheDocument();
    });

    it('guest user - should render Donation Panel when feature is enabled', async () => {
      const appState = { market: 'us', brand: 'on' };
      const { queryByTestId } = render(
        <PageContextProvider value={appState as PageAppState}>
          <FeatureFlagsProvider enabledFeatures={{ isDonationPanelEnabled: true }}>
            <CheckoutContainer />
          </FeatureFlagsProvider>
        </PageContextProvider>,
        guestUserPayload
      );
      const donationPanelElement = await queryByTestId('Donation-testid');
      waitFor(() => expect(donationPanelElement).toBeInTheDocument());
    });

    it('authenticated user - should not render Donation Panel when feature is disabled', async () => {
      const appState = { market: 'us', brand: 'on' };
      const { queryByTestId } = render(
        <PageContextProvider value={appState as PageAppState}>
          <FeatureFlagsProvider enabledFeatures={{ isDonationPanelEnabled: false }}>
            <CheckoutContainer />
          </FeatureFlagsProvider>
        </PageContextProvider>,
        authenticateUserPayload
      );
      const donationPanelElement = await queryByTestId('Donation-testid');
      expect(donationPanelElement).not.toBeInTheDocument();
    });

    it('authenticated user - should render Donation Panel when feature is enabled', async () => {
      const appState = { market: 'us', brand: 'on' };
      const { queryByTestId } = render(
        <PageContextProvider value={appState as PageAppState}>
          <FeatureFlagsProvider enabledFeatures={{ isDonationPanelEnabled: true }}>
            <CheckoutContainer />
          </FeatureFlagsProvider>
        </PageContextProvider>,
        authenticateUserPayload
      );
      const donationPanelElement = await queryByTestId('Donation-testid');
      waitFor(() => expect(donationPanelElement).toBeInTheDocument());
    });
  });

  describe('place order button visibility', () => {
    it('should pass showPlaceOrderButton prop as false when third party payment', () => {
      const spyOrderSummary = jest.spyOn(orderSummaryModule, 'OrderSummary');

      (checkoutUIStateProviderModule.useCheckoutUiStates as jest.Mock).mockReturnValue({
        setUiStates: jest.fn(),
        paymentPanelState: {
          cvv: '',
          selectedThirdPartyPayment: true,
        },
      });

      render(
        <FeatureFlagsProvider enabledFeatures={{}}>
          <CheckoutContainer />
        </FeatureFlagsProvider>,
        guestUserPayload
      );

      expect(spyOrderSummary).toHaveBeenCalledWith({ showPlaceOrderButton: false }, {});

      spyOrderSummary.mockRestore();
    });

    it('should pass showPlaceOrderButton prop as true when non third party payment', () => {
      const spyOrderSummary = jest.spyOn(orderSummaryModule, 'OrderSummary');

      (checkoutUIStateProviderModule.useCheckoutUiStates as jest.Mock).mockReturnValue({
        setUiStates: jest.fn(),
        paymentPanelState: {
          cvv: '999',
          selectedThirdPartyPayment: false,
        },
      });

      const draftOrder = {
        ...authenticateUserPayload,
        panels: {
          ...authenticateUserPayload.panels,
          checkoutPanel: {
            ...authenticateUserPayload.panels.checkoutPanel,
            bagType: { shipToHomeBag: true },
          },
          paymentPanel: {
            ...authenticateUserPayload.panels.paymentPanel,
            paymentMethods: [
              {
                isSelected: true,
                cardInfo: { cvvRequired: true, cardId: '0xff', cardNumber: '1234' },
              },
            ],
          },
          shippingAddressPanel: {
            shippingAddressList: [
              {
                addressId: '1',
                addressLine1: '123 Main St',
                addressLine2: 'Apt 4B',
                city: 'Anytown',
                state: 'CA',
                postalCode: '12345',
                country: 'US',
                firstName: 'John',
                lastName: 'Doe',
                phone: '**********',
                isDefault: true,
                isSelected: true,
                addressLocationType: 'SHIP',
                verificationStatus: 'VERIFIED',
                deliveryPointValidation: 'CONFIRMED_DELIVERY_POINT',
              },
            ],
            hasShippingAddressSelected: true,
          },
        },
      } as unknown as DraftOrder;

      render(
        <FeatureFlagsProvider enabledFeatures={{}}>
          <CheckoutContainer />
        </FeatureFlagsProvider>,
        draftOrder
      );

      expect(spyOrderSummary).toHaveBeenCalledWith({ showPlaceOrderButton: true }, {});

      spyOrderSummary.mockRestore();
    });
  });

  describe('<PanelDetails /> condition', () => {
    it('should hide panelDetails when panelState is DISABLED', () => {
      render(
        <FeatureFlagsProvider enabledFeatures={{}}>
          <CheckoutContainer />
        </FeatureFlagsProvider>,
        guestUserPayload
      );

      // Note: this test is to ensure that the panelDetails is hidden when panelState is DISABLED
      const addCardElement = screen.queryByText('Add Credit or Debit Card');
      expect(addCardElement).not.toBeInTheDocument();
    });

    it('should not hide panelDetails when panelState is not in DISABLED', () => {
      render(
        <FeatureFlagsProvider enabledFeatures={{}}>
          <CheckoutContainer />
        </FeatureFlagsProvider>,
        withPaymentMethod
      );

      // Note: this test is to ensure that the panelDetails is not hidden when panelState is NOT DISABLED
      const addCardElement = screen.queryByText('Add Credit or Debit Card');
      expect(addCardElement).toBeInTheDocument();
    });
  });
});
