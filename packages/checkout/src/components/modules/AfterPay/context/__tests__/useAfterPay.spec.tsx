import { renderHook } from '@testing-library/react';
import { FC, PropsWithChildren } from 'react';
import { AfterPayProvider } from '../AfterPayProvider';
import { useAfterPay } from '../useAfterPay';

jest
  .mock('../../../../../contexts/CheckoutProvider', () => ({
    useCheckout: () => ({
      makeCheckoutXapiCall: jest.fn().mockResolvedValue({ token: 'test-token' }),
      loading: false,
      draftOrder: { draftOrderId: '', paymentPanel: { paymentId: '' }, shippingAddressPanel: { shippingAddressList: [] } },
    }),
  }))
  .mock('../../../../utils/useScript', () => ({
    useScript: () => ({
      loading: false,
      loaded: true,
      load: jest.fn(),
    }),
  }));

describe('useAfterPay', () => {
  beforeAll(() => {
    jest.spyOn(console, 'error').mockImplementation(() => {});
  });
  afterAll(() => {
    jest.restoreAllMocks();
  });

  it('throws error when not used within AfterPayProvider', () => {
    const renderer = () => renderHook(() => useAfterPay());
    expect(renderer).toThrow(/.*AfterPayProvider*/);
  });

  it('does not throw error when used within AfterPayProvider', () => {
    const wrapper: FC<PropsWithChildren> = ({ children }) => <AfterPayProvider>{children}</AfterPayProvider>;
    const { result } = renderHook(() => useAfterPay(), { wrapper });
    expect(result.current).toEqual(
      expect.objectContaining({
        loading: expect.any(Boolean),
        loadScript: expect.any(Function),
        error: expect.any(Boolean),
        openAfterPay: expect.any(Function),
      })
    );
  });
});
