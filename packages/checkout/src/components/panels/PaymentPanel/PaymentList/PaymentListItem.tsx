import { Button } from '@ecom-next/core/migration/button';
import { <PERSON><PERSON>ventHandler, useEffect } from 'react';
import { Radio } from '@ecom-next/core/migration/radio';
import { useLocalize } from '@ecom-next/sitewide/localization-provider';
import { useCheckout } from '../../../../contexts/CheckoutProvider';
import { PaymentMethod } from '../../../../contexts/types';
import { usePayments } from '../../../../contexts/PaymentsProvider';
import { useCheckoutUiStates } from '../../../../contexts/CheckoutUiStateProvider';
import { CardIcon } from '../CardIcon';
import { PaymentMethodInlineForm } from '../PaymentForms/PaymentInlineForm';
import { usePanelModalDrawer } from '../../../layout/PanelModalDrawer';
import { usePaymentError } from '../utils/usePaymentError';
import { useSelectPaymentMethod } from './useSelectPaymentMethod';

type ButtonProps = {
  onClick: MouseEventHandler<HTMLButtonElement>;
};

const EditButton = ({ onClick }: ButtonProps) => {
  const { localize } = useLocalize();

  return (
    <Button kind='text-underline-small' onClick={onClick} data-testid='edit-payment-button'>
      {localize('payment.paymentListItem.edit')}
    </Button>
  );
};

type PaymentListItemProps = {
  paymentMethod: PaymentMethod;
  selected?: boolean;
  setCurrentSelected: () => void;
};

export const PaymentListItem = ({ paymentMethod, selected, setCurrentSelected }: PaymentListItemProps) => {
  const { draftOrder, loading } = useCheckout() || {};
  const { paymentPanel } = draftOrder;
  const { isCardExpired, cardInfo } = paymentMethod || {};
  const lastFourDigits = cardInfo?.lastFourDigits;
  /* If Expired card, No xapi call happens, So cvvRequired won't be present.*/
  const cvvRequiredValue = paymentPanel?.selectedPaymentMethod?.cardInfo?.cvvRequired;

  const { paymentsState, setPaymentsState } = usePayments();
  const { inlineFormState, editClickState } = paymentsState;
  const { setUiStates, paymentPanelState } = useCheckoutUiStates();
  const { bannerType } = usePaymentError();

  const { onSelectPayment } = useSelectPaymentMethod(paymentMethod);
  const { openModal, closeModal, modalOpen } = usePanelModalDrawer();

  const formattedCardNumber = `•••• ${lastFourDigits}`;

  /* Below useEffect will take care of setting inlineFormState
    - If CVV required.
    - If thirdParty payment is not selected.
    - If xapi response still pending as loading.
    - If cvv state has value.
   */
  useEffect(() => {
    if (cvvRequiredValue && !editClickState && !paymentMethod.digitalWallet && !loading && !paymentPanelState.cvv) {
      setPaymentsState({
        inlineFormState: true,
      });
      selected && setCurrentSelected();
    } else {
      setPaymentsState({ inlineFormState: false });
    }
  }, [cvvRequiredValue, isCardExpired, loading, inlineFormState]);

  /* Below useEffect will take care of setting inlineFormState 
     when there is error on place order for invalid CVV then 
     user must see inline form open if cvv is required. 
     (error codes: 801,807,810,813,899)
  */
  useEffect(() => {
    if (bannerType === 'error' && cvvRequiredValue && !editClickState) {
      setPaymentsState({ inlineFormState: true });
    }
  }, [bannerType]);

  const handleSelectRadioChange = () => {
    if(selected) return;
    
    //Reset the cvv or selectedThirdPartyPayment state on select radio button.
    if (paymentPanelState.cvv || paymentPanelState.selectedThirdPartyPayment || isCardExpired !== paymentPanelState.isCardExpired) {
      setUiStates(state => ({
        ...state,
        paymentPanelState: {
          cvv: '',
          selectedThirdPartyPayment: false,
          isCardExpired: isCardExpired,
        },
      }));
    }
    setPaymentsState({ editClickState: false, selectedPaymentMethodState: { entityType: 'creditCard', id: cardInfo?.cardId || null }, uiError: null });
    !isCardExpired ? onSelectPayment() : null;
    setCurrentSelected();
  };

  const handleEditClick: MouseEventHandler<HTMLButtonElement> = event => {
    //Set Overlay state as Edit.
    event.stopPropagation();
    setPaymentsState({ editClickState: true, selectedPaymentMethodState: { entityType: 'creditCard', id: cardInfo?.cardId || null }, uiError: null });
    openModal({ titleKey: 'payment.editOverlayHeader' });
  };

  useEffect(() => {
    if (loading && modalOpen) {
      closeModal();
    }
  }, [modalOpen, loading]);

  return (
    <div className='cb-base-compact cursor-pointer py-4' onClick={handleSelectRadioChange} role='presentation'>
      <div className='flex items-center'>
        <div className='mr-auto flex items-center hover:underline'>
          <div data-testid='payment-method-radio'>
            <Radio id={lastFourDigits} checked={selected} isRadioSmall={false} name='PaymentList' onChange={handleSelectRadioChange} />
          </div>
          <div className='ml-4 flex w-40 items-center'>
            <div className='mr-2'>
              <CardIcon cardInfo={cardInfo!} large />
            </div>
            <span className='cb-base-compact fs-exclude'>{formattedCardNumber}</span>
          </div>
        </div>
        <EditButton onClick={handleEditClick} />
      </div>
      {(inlineFormState || isCardExpired) && selected && <PaymentMethodInlineForm cardInfo={cardInfo!} isCardExpired={isCardExpired} />}
    </div>
  );
};
