import { usePageContext } from '@ecom-next/sitewide/hooks/usePageContext';
import { render, screen, fireEvent, waitFor, makeCheckoutXapiCallMock } from '../../../../../utils/test-utils';
import { AvsWrapper } from '../AvsWrapper';
import { AvsResponse, ShippingFormData } from '../../types';
import { DraftOrder } from '../../../../../contexts/types';
import { fireTealiumLinkTag } from '../../../../../utils/tealium-utils';
import { draftOrder } from './mock-draftOrder';

jest.mock('../../../../../utils/newrelic-logger');

jest.mock('../../../../../utils/tealium-utils', () => ({
  fireTealiumLinkTag: jest.fn(),
}));

jest.mock('@ecom-next/sitewide/hooks/usePageContext');

const mockShippingAddressFormData: ShippingFormData = {
  fullName: '<PERSON>',
  addressLine1: '2 Folsom Street',
  addressLine2: '',
  city: 'San Franciscoo',
  state: 'California',
  postalCode: '94105-1205',
  phone: '(*************',
  country: 'US',
  default: false,
};

describe('AVSWrapper- featureFlag is on', () => {
  afterEach(() => {
    jest.clearAllMocks();
  });

  (usePageContext as jest.Mock).mockReturnValue({ brandAbbr: 'at', market: 'us' });

  describe('Exact Match', () => {
    const makeCheckoutXapiCall = jest.fn().mockResolvedValue({});

    const mockAvsResponse: AvsResponse = {
      match_accuracy: 'MATCH_FOUND',
      address_change_status: {
        address_line_changed: true,
        city_name_changed: true,
        state_code_changed: false,
        postal_code_changed: false,
      },
      normalized_address: {
        city_name: 'San Francisco',
        county_name: 'San Francisco',
        state_province_code: 'CA',
        postal_code: '94105-1205',
        country_code: 'US',
        verification_status: 'VERIFIED',
        delivery_point_validation_indicator: 'CONFIRMED_DELIVERY_POINT',
        address_line_1: '2 Folsom St',
      },
    };

    const props = {
      isClientSideAvsEnabled: true,
      isAVSOverlay: true,
      setOverlayDetails: () => {},
      shippingAddressFormData: mockShippingAddressFormData,
      avsResponse: mockAvsResponse,
      children: <></>,
    };

    it('should not call createShippingAddress and show suggestion and trigger tealium link', async () => {
      const changedTrueAvsMock = {
        ...mockAvsResponse,
        address_change_status: {
          ...mockAvsResponse.address_change_status,
          address_line_changed: true,
        },
      };
      const newProps = {
        ...props,
        avsResponse: changedTrueAvsMock,
      };
      render(<AvsWrapper {...newProps} />, draftOrder as unknown as DraftOrder);

      expect(makeCheckoutXapiCall).not.toHaveBeenCalled();

      const suggestionText = screen.getByText('The U.S. Postal Service suggests using the below address');
      const address = screen.getByText('Or keep the address you entered');
      const shippingAddressLine1 = screen.getByText('Franciscoo');
      const normalizedAddressLine1 = screen.getByText('Francisco');

      expect(suggestionText).toBeInTheDocument();
      expect(address).toBeInTheDocument();
      expect(shippingAddressLine1).toBeInTheDocument();
      expect(normalizedAddressLine1).toBeInTheDocument();

      await waitFor(() => {
        expect(fireTealiumLinkTag).toHaveBeenCalledWith({
          brandShortName: 'at',
          countryCode: 'us',
          event_name: 'checkout_cta_clicks',
          module: 'avs',
          checkout_cta_name: 'display module:address validation',
        });
      });
    });
  });

  describe('Single Recommendation', () => {
    const singleRecommendationMock: AvsResponse = {
      match_accuracy: 'ADDRESS_LINE_SUGGESTIONS_FOUND',
      address_change_status: {
        address_line_changed: false,
        city_name_changed: false,
        state_code_changed: false,
        postal_code_changed: false,
      },
      suggested_addresses: [
        {
          city_name: 'San Francisco',
          county_name: 'San Francisco',
          state_province_code: 'CA',
          postal_code: '94105-1205',
          country_code: 'US',
          verification_status: 'NOT_VERIFIED',
          delivery_point_validation_indicator: 'INDETERMINATE',
          address_line_1: '2 Folsom St',
          address_line_2: '',
        },
      ],
      normalized_address: {
        city_name: 'San Francisco',
        state_province_code: 'CA',
        postal_code: '94105-1205',
        country_code: 'US',
        delivery_point_validation_indicator: 'CONFIRMED_DELIVERY_POINT',
        address_line_1: '2 Folsom St',
      },
    };

    const props = {
      isClientSideAvsEnabled: true,
      isAVSOverlay: true,
      setOverlayDetails: () => {},
      shippingAddressFormData: mockShippingAddressFormData,
      avsResponse: singleRecommendationMock,
      children: <></>,
    };

    it('should display suggestioned address information', async () => {
      render(<AvsWrapper {...props} />, draftOrder as unknown as DraftOrder);

      const suggestionText = screen.getByText('The U.S. Postal Service suggests using the below address');
      const address = screen.getByText('Or keep the address you entered');

      expect(suggestionText).toBeInTheDocument();
      expect(address).toBeInTheDocument();
    });

    it('should send avs suggested address on click of accept suggested address button and trigger tealium link call', async () => {
      render(<AvsWrapper {...props} />, draftOrder as unknown as DraftOrder);

      const suggestedAddrBtn = screen.getByRole('button', {
        name: /Accept Suggested Address/i,
      });

      expect(suggestedAddrBtn).toBeInTheDocument();

      fireEvent.click(suggestedAddrBtn);

      await waitFor(() => {
        expect(fireTealiumLinkTag).toHaveBeenCalledWith({
          brandShortName: 'at',
          countryCode: 'us',
          event_name: 'checkout_cta_clicks',
          module: 'avs',
          checkout_cta_name: 'accept suggested address:address validation',
        });
      });

      await waitFor(() => {
        expect(makeCheckoutXapiCallMock).toHaveBeenCalledWith(
          expect.objectContaining({
            endpoint: 'createShippingAddress',
            body: expect.objectContaining({
              shippingAddress: expect.objectContaining({
                addressLine1: '2 Folsom St',
              }),
            }),
          })
        );
      });
    });

    it('should open modal on click of edit current address button and trigger tealium link call', async () => {
      render(<AvsWrapper {...props} />, draftOrder as unknown as DraftOrder);

      const editCurrentAddress = screen.getByRole('button', {
        name: /Edit Address/i,
      });

      expect(editCurrentAddress).toBeInTheDocument();

      fireEvent.click(editCurrentAddress);

      await waitFor(() => {
        expect(fireTealiumLinkTag).toHaveBeenCalledWith({
          brandShortName: 'at',
          countryCode: 'us',
          event_name: 'checkout_cta_clicks',
          module: 'avs',
          checkout_cta_name: 'edit address:address validation',
        });
      });
    });

    it('should submit the user-entered address when "Keep as Entered" is clicked and trigger a Tealium link call', async () => {
      render(<AvsWrapper {...props} />, draftOrder as unknown as DraftOrder);

      const keepAsEntered = screen.getByRole('button', {
        name: /Keep as Entered/i,
      });

      expect(keepAsEntered).toBeInTheDocument();

      fireEvent.click(keepAsEntered);

      await waitFor(() => {
        expect(fireTealiumLinkTag).toHaveBeenCalledWith({
          brandShortName: 'at',
          countryCode: 'us',
          event_name: 'checkout_cta_clicks',
          module: 'avs',
          checkout_cta_name: 'keep as entered:address validation',
        });
      });

      await waitFor(() => {
        expect(makeCheckoutXapiCallMock).toHaveBeenCalledWith(
          expect.objectContaining({
            endpoint: 'createShippingAddress',
            body: expect.objectContaining({
              shippingAddress: expect.objectContaining({
                addressLine1: '2 Folsom Street',
                verificationStatus: 'USER_OVERRIDDEN',
              }),
            }),
          })
        );
      });
    });
  });

  describe('Multiple Recommendations', () => {
    const multipleRecommendationsMock: AvsResponse = {
      match_accuracy: 'LAST_LINE_SUGGESTIONS_FOUND',
      address_change_status: {
        address_line_changed: false,
        city_name_changed: true,
        state_code_changed: true,
        postal_code_changed: false,
      },
      suggested_addresses: [
        {
          city_name: 'Saint Louis',
          county_name: 'Saint Louis',
          state_province_code: 'MO',
          postal_code: '63146-2902',
          country_code: 'US',
          verification_status: 'NOT_VERIFIED',
          delivery_point_validation_indicator: 'INDETERMINATE',
          address_line_1: '2 Folsom St',
          address_line_2: 'Pavilion Apt',
        },
        {
          city_name: 'Saint Louis',
          county_name: 'Saint Louis',
          state_province_code: 'MO',
          postal_code: '63146-2931',
          country_code: 'US',
          verification_status: 'NOT_VERIFIED',
          delivery_point_validation_indicator: 'INDETERMINATE',
          address_line_1: '2 Folsom St',
        },
      ],
      normalized_address: {
        city_name: 'St Louis',
        state_province_code: 'MO',
        postal_code: '63146',
        country_code: 'US',
        delivery_point_validation_indicator: 'CONFIRMED_DELIVERY_POINT',
        address_line_1: '2 Folsom St',
        address_line_2: '2',
      },
    };

    const props = {
      isClientSideAvsEnabled: true,
      isAVSOverlay: true,
      setOverlayDetails: () => {},
      shippingAddressFormData: mockShippingAddressFormData,
      avsResponse: multipleRecommendationsMock,
      children: <></>,
    };

    it('should display text related to multiple addresses', async () => {
      render(<AvsWrapper {...props} />, draftOrder as unknown as DraftOrder);

      const suggestionText = screen.getByText('Please confirm the address entered');
      const address = screen.getByText('Or select a suggested address');

      expect(suggestionText).toBeInTheDocument();
      expect(address).toBeInTheDocument();
    });

    it('should call create-shipping-addres with suggested address on click of select', async () => {
      render(<AvsWrapper {...props} />, draftOrder as unknown as DraftOrder);

      const [suggestedAddrBtn] = screen.getAllByRole('button', {
        name: /Select/i,
      });

      fireEvent.click(suggestedAddrBtn);
      await waitFor(() => {
        expect(makeCheckoutXapiCallMock).toHaveBeenCalledWith(
          expect.objectContaining({
            endpoint: 'createShippingAddress',
            body: expect.objectContaining({
              shippingAddress: expect.objectContaining({
                addressLine1: '2 Folsom St',
                addressLine2: 'Pavilion Apt',
              }),
            }),
          })
        );
      });
    });
  });

  describe('No Match Found', () => {
    it('should show copy indicating incomplete address', () => {
      const missingDetails: AvsResponse = {
        match_accuracy: 'NO_MATCH_FOUND',
        address_change_status: {
          address_line_changed: false,
          city_name_changed: false,
          state_code_changed: false,
          postal_code_changed: false,
        },
        validation_messages: [
          {
            code: 'MISSING_APT_OR_SUITE',
          },
        ],
        normalized_address: {
          city_name: 'Asdfasdf Asdfsad',
          state_province_code: 'TX',
          postal_code: '75252',
          country_code: 'US',
          verification_status: 'NOT_VERIFIED',
          delivery_point_validation_indicator: 'INVALID_DELIVERY_POINT',
          address_line_1: '7421 Asdfsa Asdfasf',
          address_line_2: '2',
        },
      };

      const props = {
        isClientSideAvsEnabled: true,
        isAVSOverlay: true,
        setOverlayDetails: () => {},
        shippingAddressFormData: mockShippingAddressFormData,
        avsResponse: missingDetails,
        children: <></>,
      };

      render(<AvsWrapper {...props} />, draftOrder as unknown as DraftOrder);

      const suggestionText = screen.getByText('The address entered may be incomplete. Please review and keep as entered or edit.');
      expect(suggestionText).toBeInTheDocument();
    });

    it('should show copy related indicating address not recognized', () => {
      const missingDetails: AvsResponse = {
        match_accuracy: 'NO_MATCH_FOUND',
        address_change_status: {
          address_line_changed: false,
          city_name_changed: false,
          state_code_changed: false,
          postal_code_changed: false,
        },
        validation_messages: [
          {
            code: 'INVALID_ADDRESS_LINE',
          },
        ],
        normalized_address: {
          city_name: 'Asdfasdf Asdfsad',
          state_province_code: 'TX',
          postal_code: '75252',
          country_code: 'US',
          verification_status: 'NOT_VERIFIED',
          delivery_point_validation_indicator: 'INVALID_DELIVERY_POINT',
          address_line_1: '7421 Asdfsa Asdfasf',
          address_line_2: '2',
        },
      };

      const props = {
        isClientSideAvsEnabled: true,
        isAVSOverlay: true,
        setOverlayDetails: () => {},
        shippingAddressFormData: mockShippingAddressFormData,
        avsResponse: missingDetails,
        children: <></>,
      };

      render(<AvsWrapper {...props} />, draftOrder as unknown as DraftOrder);

      const suggestionText = screen.getByText('The U.S. Postal Service doesn’t recognize the address entered');
      expect(suggestionText).toBeInTheDocument();
    });
  });
});
