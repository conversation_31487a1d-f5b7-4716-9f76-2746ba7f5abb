import type { DraftOrder } from '../../contexts/types';

export const draftOrder: DraftOrder = {
  panels: {
    bagPanel: {
      lineItems: [
        {
          brand: 'GP',
          productSkuId: '7953460120001',
          productName: 'Cotton Vintage Crewneck T-Shirt',
          productTypeName: 'petite short-sleeved tees',
          productStyleId: '795346',
          color: 'fresh white',
          size: 'S',
          price: {
            regularPrice: 24.95,
            salePrice: 24.95,
            discountedPrice: 24.95,
            mupPromoPrice: 24.95,
          },
          regularPrice: '24.95',
          salePrice: '24.95',
          discountedPrice: '24.95',
          totalPrice: '24.95',
          quantity: '1',
          imagePath: 'webcontent/0054/314/069/cn54314069.jpg',
          inventoryStatus: 'RESERVED',
          appliedDiscounts: [],
          storeId: '',
        },
        {
          brand: 'GP',
          productSkuId: '7401400220002',
          productName: 'Organic Cotton Vintage V-Neck T-Shirt',
          productTypeName: 'petite short-sleeved tees',
          productStyleId: '740140',
          color: 'fresh white',
          size: 'M',
          price: {
            regularPrice: 24.95,
            salePrice: 12,
            discountedPrice: 12,
            mupPromoPrice: 12,
            percentageOff: 51,
          },
          regularPrice: '24.95',
          salePrice: '12',
          discountedPrice: '12',
          totalPrice: '12',
          quantity: '1',
          imagePath: 'webcontent/0051/605/079/cn51605079.jpg',
          inventoryStatus: 'RESERVED',
          appliedDiscounts: [],
          storeId: '',
        },
        {
          brand: 'GP',
          productSkuId: '4941480021106',
          productName: 'GapFit Tech Kids Joggers',
          productTypeName: 'boys active bottoms',
          productStyleId: '494148',
          color: 'true black',
          size: 'M (8)',
          price: {
            regularPrice: 34.95,
            salePrice: 34.95,
            discountedPrice: 34.95,
            mupPromoPrice: 34.95,
          },
          regularPrice: '34.95',
          salePrice: '34.95',
          discountedPrice: '34.95',
          totalPrice: '34.95',
          quantity: '1',
          imagePath: 'webcontent/0017/225/024/cn17225024.jpg',
          inventoryStatus: 'RESERVED',
          appliedDiscounts: [],
          storeId: '',
        },
      ],
      bopisItems: [],
      bagTotal: 71.9,
    },
    checkoutPanel: {
      conditionals: {
        hasBopisItems: false,
        isBopisOnlyBag: false,
        isBopisGiftCardPurchase: false,
        bopisInBag: false,
        isInvalidBagError: false,
        isOrderLineItems: true,
        shouldDonationsDisplay: false,
        isSummaryViewEnabled: true,
        cvvInvalidErrors: false,
        cardProcessingError: false,
        isShippingMethods: true,
        isShippingAddress: true,
        isShippingAddressSelected: true,
        isPayment: true,
        isOrderReadyToPlace: true,
      },
      totalPrice: 78.1,
      lineItems: [
        {
          itemId: '328ca306092a46dbac88ac7418c6dfe9',
          brand: 'GP',
          productSkuId: '7953460120001',
          productName: 'Cotton Vintage Crewneck T-Shirt',
          customerChoiceNumber: '795346012',
          productTypeName: 'petite short-sleeved tees',
          productStyleId: '795346',
          color: 'fresh white',
          size: 'S',
          colorStyleNumber: '795346012',
          variantDescription: 'Regular',
          merchandiseType: '7',
          inventoryStatusId: 5,
          isBackOrderItem: false,
          backOrdershippingDate: false,
          autoAdded: false,
          noReturnItem: false,
          returnByMailItem: true,
          noIntlShipping: true,
          marketingFlag: '0',
          giftWrappable: true,
          productStyleDescription: 'Cotton Vintage Crewneck T-Shirt',
          primaryCategoryName: 'T-Shirts & Tanks',
          productURL: 'browse/product.do?pid=7953460120001',
          price: {
            regularPrice: 24.95,
            salePrice: 24.95,
            discountedPrice: 24.95,
            percentageOff: 0,
          },
          regularPrice: '24.95',
          salePrice: '24.95',
          discountedPrice: '24.95',
          totalPrice: '24.95',
          quantity: '1',
          imagePath: 'webcontent/0054/314/069/cn54314069.jpg',
          inventoryStatus: 'RESERVED',
          vendorId: '700086785',
          webVendorName: 'PRATIBHA SYNTEX LTD',
          showSellerName: false,
          excludedFromPromotion: false,
          madeToOrder: false,
          estimatedShippingDate: '',
          eligibleReturnLocationCode: 'M',
          isExcludedFromRewardFreeShipping: false,
          vendorStyleNumber: '',
          vendorUPCCode: '',
          appliedDiscounts: [],
          storeId: '',
        },
        {
          itemId: '11ae3e6726a3485ab8d6efa0ede26457',
          brand: 'GP',
          productSkuId: '7401400220002',
          productName: 'Organic Cotton Vintage V-Neck T-Shirt',
          customerChoiceNumber: '740140022',
          productTypeName: 'petite short-sleeved tees',
          productStyleId: '740140',
          color: 'fresh white',
          size: 'M',
          colorStyleNumber: '740140022',
          variantDescription: 'Regular',
          merchandiseType: '7',
          inventoryStatusId: 5,
          isBackOrderItem: false,
          backOrdershippingDate: false,
          autoAdded: false,
          noReturnItem: false,
          returnByMailItem: true,
          noIntlShipping: true,
          marketingFlag: '0',
          giftWrappable: true,
          productStyleDescription: 'Organic Cotton Vintage V-Neck T-Shirt',
          primaryCategoryName: 'T-Shirts & Tanks',
          productURL: 'browse/product.do?pid=7401400220002',
          price: {
            regularPrice: 24.95,
            salePrice: 12,
            discountedPrice: 12,
            percentageOff: 51,
          },
          regularPrice: '24.95',
          salePrice: '12',
          discountedPrice: '12',
          totalPrice: '12',
          quantity: '1',
          imagePath: 'webcontent/0051/605/079/cn51605079.jpg',
          inventoryStatus: 'RESERVED',
          vendorId: '700086785',
          webVendorName: 'PRATIBHA SYNTEX LTD',
          showSellerName: false,
          excludedFromPromotion: false,
          madeToOrder: false,
          estimatedShippingDate: '',
          eligibleReturnLocationCode: 'M',
          isExcludedFromRewardFreeShipping: false,
          vendorStyleNumber: '',
          vendorUPCCode: '',
          appliedDiscounts: [],
          storeId: '',
        },
        {
          itemId: 'e35f0d06151e4c929a6d60fb77b2cf2d',
          brand: 'GP',
          productSkuId: '4941480021106',
          productName: 'GapFit Tech Kids Joggers',
          customerChoiceNumber: '494148002',
          productTypeName: 'boys active bottoms',
          productStyleId: '494148',
          color: 'true black',
          size: 'M (8)',
          colorStyleNumber: '494148002',
          variantDescription: 'Regular',
          merchandiseType: '7',
          inventoryStatusId: 5,
          isBackOrderItem: false,
          backOrdershippingDate: false,
          autoAdded: false,
          noReturnItem: false,
          returnByMailItem: false,
          noIntlShipping: true,
          marketingFlag: '0',
          giftWrappable: true,
          productStyleDescription: 'GapFit Tech Kids Joggers',
          primaryCategoryName: 'GapFit & Activewear',
          productURL: 'browse/product.do?pid=4941480021106',
          price: {
            regularPrice: 34.95,
            salePrice: 34.95,
            discountedPrice: 34.95,
            percentageOff: 0,
          },
          regularPrice: '34.95',
          salePrice: '34.95',
          discountedPrice: '34.95',
          totalPrice: '34.95',
          quantity: '1',
          imagePath: 'webcontent/0017/225/024/cn17225024.jpg',
          inventoryStatus: 'RESERVED',
          vendorId: '000003489',
          webVendorName: 'MAKALOT INDUSTRIAL CO LTD',
          showSellerName: false,
          excludedFromPromotion: false,
          madeToOrder: false,
          estimatedShippingDate: '',
          eligibleReturnLocationCode: '',
          isExcludedFromRewardFreeShipping: false,
          vendorStyleNumber: '',
          vendorUPCCode: '',
          appliedDiscounts: [],
          storeId: '',
        },
      ],
      totalSavings: 12.95,
      locale: 'en_US',
      bagType: {
        bopisOnlyBag: false,
        omniBag: false,
        shipToHomeBag: true,
      },
      pickupOrderType: 'shipItemsOnly',
      summaryView: {
        shippingAddressPanel: 'C',
        deliveryGroupPanel: 'E',
        pickupPanel: 'H',
        paymentPanel: 'C',
        rewardsPanel: 'E',
        showDonationPanel: false,
      },
      checkoutModuleStatus:
        'shippingAddressPanel:collapsed|deliveryGroupPanel:expanded|pickupPanel:hidden|paymentPanel:collapsed|rewardsPanel:expanded|donationPanel:hidden',
    },
    donationPanel: {},
    giftCardPanel: {
      totalPrice: 78.1,
      appliedGiftCards: [],
      errors: [],
      conditionals: {
        hasGiftCardsApplied: false,
      },
      isBopisGiftCardPurchase: false,
      isGiftcardPanelExpanded: false,
      isFormShown: true,
    },
    orderSummaryPanel: {
      isOrdersummarylineItemEnabled: false,
      hasPromotion: false,
      promotionDescription: false,
      isBopisOnly: false,
      subTotal: 84.85,
      hasShippingPrice: true,
      shippingPrice: 0,
      shippingMessage: '(3-5 business days)',
      hasSavings: true,
      totalSavings: -12.95,
      hasRewards: false,
      rewardsSubTotal: 0,
      giftCardsTotal: 0,
      hasGiftCards: false,
      estimatedTax: 6.2,
      hasDonations: false,
      donationsTotal: 0,
      totalPrice: '78.10',
      retailDeliveryFee: 0,
      markdownPromoSavings: 12.95,
      rewardsSavings: 0,
      isAfterpaySelected: false,
    },
    paymentPanel: {
      paymentMethods: [
        {
          isSelected: false,
          isDefault: false,
          cardInfo: {
            cardId: '329866B7B65E247FE0E5E5F87D1AE86F',
            cardNumber: 'XXXX XXXX XXXX 9999',
            expirationMonth: '12',
            expirationYear: '2024',
            lastFourDigits: '9999',
            isCvvValidated: true,
            billingAddressId: '9d5ffb7627464697a0cb90ecc3237bb0',
            saveCCInProfile: true,
            cardBrand: '',
            temporaryCardIndicator: false,
            creditCardTier: '',
            brand: '',
            cardType: 1,
          },
          billingAddress: {
            addressId: '9d5ffb7627464697a0cb90ecc3237bb0',
            firstName: 'John',
            lastName: 'Doe',
            addressLine1: '1019 Pennsylvania Avenue',
            addressLine2: 'Apt 328',
            postalCode: '94107',
            city: 'San Francisco',
            state: 'CA',
            country: 'US',
            phone: '4154120001',
            isSelected: false,
            isDefault: false,
          },
          isCardExpired: false,
        },
        {
          isSelected: false,
          isDefault: false,
          cardInfo: {
            cardId: 'foobar',
            cardNumber: 'XXXX XXXX XXXX 1111',
            expirationMonth: '12',
            expirationYear: '2034',
            lastFourDigits: '1111',
            isCvvValidated: true,
            billingAddressId: '7de9a6b62a5c4a24acb965466c0eff42',
            saveCCInProfile: true,
            cardBrand: '',
            temporaryCardIndicator: false,
            creditCardTier: '',
            brand: '',
            cardType: 1,
          },
          billingAddress: {
            addressId: '7de9a6b62a5c4a24acb965466c0eff42',
            firstName: 'John',
            lastName: 'Doe',
            addressLine1: '1019 Pennsylvania Avenue',
            postalCode: '94107',
            city: 'San Francisco',
            state: 'CA',
            country: 'US',
            phone: '4154120001',
            isSelected: false,
            isDefault: false,
          },
          isCardExpired: false,
        },
        {
          isSelected: false,
          isDefault: false,
          cardInfo: {
            cardId: '981DE8E3AD836DD8AE67BA04F5EFEE84',
            cardNumber: 'XXXX XXXX XXXX 2134',
            expirationMonth: '07',
            expirationYear: '2027',
            lastFourDigits: '2134',
            isCvvValidated: true,
            billingAddressId: '72b8ed1e89c94d0ead31cb1b0a9f5de8',
            saveCCInProfile: true,
            cardBrand: '1',
            temporaryCardIndicator: true,
            creditCardTier: '',
            brand: '1',
            cardType: 2,
          },
          billingAddress: {
            addressId: '72b8ed1e89c94d0ead31cb1b0a9f5de8',
            firstName: 'TEST',
            lastName: 'TEST',
            addressLine1: '15 RIVER RD',
            postalCode: '31901',
            city: 'COLUMBUS',
            state: 'GA',
            country: 'US',
            phone: '7065551000',
            isSelected: false,
            isDefault: false,
          },
          isCardExpired: false,
        },
        {
          isSelected: false,
          isDefault: false,
          cardInfo: {
            cardId: 'BD669CE6BBB2575856C874ABF52FC09A',
            cardNumber: 'XXXX XXXX XXXX 8294',
            lastFourDigits: '8294',
            isCvvValidated: true,
            billingAddressId: '32b24d22ca7a4bc08b6a94d477547abd',
            saveCCInProfile: true,
            cardBrand: '2',
            temporaryCardIndicator: true,
            creditCardTier: '',
            brand: '2',
            cardType: 30,
          },
          billingAddress: {
            addressId: '32b24d22ca7a4bc08b6a94d477547abd',
            firstName: 'John',
            lastName: 'Doe',
            addressLine1: '1015 Pennsylvania Avenue',
            postalCode: '80302-6936',
            city: 'Boulder',
            state: 'CA',
            country: 'US',
            phone: '4154120001',
            isSelected: false,
            isDefault: false,
          },
          isCardExpired: false,
        },
        {
          isSelected: false,
          isDefault: false,
          cardInfo: {
            cardId: 'D88EF2C02344CF21156E3701EC320893',
            cardNumber: 'XXXX XXXX XXXX 5439',
            expirationMonth: '12',
            expirationYear: '2023',
            lastFourDigits: '5439',
            isCvvValidated: true,
            billingAddressId: 'be6bee944b654629896919784315692b',
            saveCCInProfile: true,
            cardBrand: '',
            temporaryCardIndicator: false,
            creditCardTier: '',
            brand: '',
            cardType: 1,
          },
          billingAddress: {
            addressId: 'be6bee944b654629896919784315692b',
            firstName: 'Harish A',
            lastName: 'Megharaj',
            addressLine1: '39663 Leslie Street, Apt 328',
            addressLine2: 'Apt 328',
            postalCode: '94538-2233',
            city: 'Fremont',
            state: 'CA',
            country: 'US',
            phone: '4086790016',
            isSelected: false,
            isDefault: false,
          },
          isCardExpired: true,
        },
        {
          isSelected: false,
          isDefault: false,
          cardInfo: {
            cardId: 'F34B9CD93CC98119A0383467F83F398E',
            cardNumber: 'XXXX XXXX XXXX 6290',
            expirationMonth: '10',
            expirationYear: '2026',
            lastFourDigits: '6290',
            isCvvValidated: false,
            billingAddressId: 'defbb78083554559b3b4469ed31c6571',
            saveCCInProfile: true,
            cardBrand: '2',
            temporaryCardIndicator: false,
            creditCardTier: '',
            brand: '2',
            cardType: 2,
          },
          billingAddress: {
            addressId: 'defbb78083554559b3b4469ed31c6571',
            firstName: 'Dsv',
            lastName: 'Fdf',
            addressLine1: '251 Nassau Ave',
            postalCode: '11222',
            city: 'Brooklyn',
            state: 'NY',
            country: 'US',
            phone: '8007425877',
            isSelected: false,
            isDefault: false,
          },
          isCardExpired: false,
        },
        {
          isSelected: false,
          isDefault: false,
          cardInfo: {
            cardId: 'D11E7F8E108C971FCDF42121C170CA61',
            cardNumber: 'XXXX XXXX XXXX 2376',
            expirationMonth: '12',
            expirationYear: '2025',
            lastFourDigits: '2376',
            isCvvValidated: false,
            billingAddressId: 'fb6799b688b24622a980998b4dca5f07',
            saveCCInProfile: true,
            cardBrand: '',
            temporaryCardIndicator: false,
            creditCardTier: '',
            brand: '',
            cardType: 3,
          },
          billingAddress: {
            addressId: 'fb6799b688b24622a980998b4dca5f07',
            firstName: 'John',
            lastName: 'Doe',
            addressLine1: '1019 Pennsylvania Avenue',
            postalCode: '94107',
            city: 'San Francisco',
            state: 'CA',
            country: 'US',
            phone: '4154120001',
            isSelected: false,
            isDefault: false,
          },
          isCardExpired: false,
        },
        {
          isSelected: false,
          isDefault: false,
          cardInfo: {
            cardId: '97F8F322D7FB5D9600AD858719B8F77D',
            cardNumber: 'XXXX XXXX XXXX 6909',
            expirationMonth: '12',
            expirationYear: '2026',
            lastFourDigits: '6909',
            isCvvValidated: false,
            billingAddressId: 'f6e2c286ad38461e80b07297ad1d877b',
            saveCCInProfile: true,
            cardBrand: '',
            temporaryCardIndicator: false,
            creditCardTier: '',
            brand: '',
            cardType: 4,
          },
          billingAddress: {
            addressId: 'f6e2c286ad38461e80b07297ad1d877b',
            firstName: 'John',
            lastName: 'Doe',
            addressLine1: '1015 Pennsylvania Avenue',
            postalCode: '80302-6936',
            city: 'Boulder',
            state: 'CA',
            country: 'US',
            phone: '4154120001',
            isSelected: false,
            isDefault: false,
          },
          isCardExpired: false,
        },
        {
          paymentId: '575d7ade-cef5-4849-b732-3b2a6c7457e9',
          paymentIndex: 1,
          digitalWallet: {
            type: 'PAYPAL',
            paypal: {
              orderIdentifier: 'EC-45T42580SH618541M',
              deviceData: '{"correlation_id":"c27c8c522110c27afc11db6e5605f4ca"}',
              billingAddress: {
                firstName: 'John',
                lastName: 'Doe',
                addressLine1: '1 Main St',
                city: 'San Jose',
                state: 'CA',
                country: 'US',
                postalCode: '95131',
                phone: '4082383466',
                verificationStatus: 'VERIFIED',
                default: false,
              },
              isCBCC: false,
            },
          },
          isSelected: true,
          isDefault: false,
          cardInfo: {
            cardNumber: 'XXXX XXXX XXXX undefined',
            saveCCInProfile: true,
            cardType: 1001,
          },
          billingAddress: {
            firstName: 'John',
            lastName: 'Doe',
            addressLine1: '1 Main St',
            postalCode: '95131',
            city: 'San Jose',
            state: 'CA',
            country: 'US',
            phone: '4082383466',
            isSelected: false,
            isDefault: false,
          },
          isCardExpired: false,
        },
      ],
      paymentId: '575d7ade-cef5-4849-b732-3b2a6c7457e9',
      validGiftCardSubTotal: 0,
      shippingAddresses: [
        {
          addressId: 'cdccca4c2c82472da6d19713a0b56a77',
          firstName: 'John',
          lastName: 'Doe',
          addressLine1: '1019 Pennsylvania Avenue',
          postalCode: '94107',
          city: 'San Francisco',
          state: 'CA',
          country: 'US',
          phone: '4154120001',
          isSelected: true,
          isDefault: false,
        },
        {
          addressId: 'cdccca4c2c82472da6d19713a0b56a77',
          firstName: 'John',
          lastName: 'Doe',
          addressLine1: '1019 Pennsylvania Avenue',
          city: 'San Francisco',
          state: 'CA',
          country: 'US',
          postalCode: '94107',
          phone: '4154120001',
          defaultAddress: false,
          default: false,
          isSelected: false,
          verificationStatus: 'VERIFIED',
          deliveryPointValidation: 'INVALID_DELIVERY_POINT',
        },
        {
          addressId: '4e0af374ee11442db64b600d301b55a3',
          firstName: 'Harish A',
          lastName: 'Megharaj',
          addressLine1: '3000 E 1ST Ave',
          city: 'Denver',
          state: 'CO',
          country: 'US',
          postalCode: '80206-5615',
          phone: '4086790016',
          defaultAddress: false,
          default: false,
          isSelected: false,
          verificationStatus: 'NOT_VERIFIED',
          deliveryPointValidation: 'INVALID_DELIVERY_POINT',
        },
        {
          addressId: '37a76692a3744349ad8d8e34295a85fa',
          firstName: 'Abc',
          lastName: 'Errrt',
          addressLine1: 'County Road R44',
          city: 'iowa',
          state: 'IA',
          country: 'US',
          postalCode: '18311',
          phone: '4086790016',
          defaultAddress: false,
          default: false,
          isSelected: false,
          verificationStatus: 'NOT_VERIFIED',
          deliveryPointValidation: 'INVALID_DELIVERY_POINT',
        },
        {
          addressId: 'e81ffa7125684aee9d1af2b1c48065ff',
          firstName: 'Shruti Kabra',
          lastName: 'Gattani',
          addressLine1: '33 Union Sq W',
          city: 'New York',
          state: 'NY',
          country: 'US',
          postalCode: '10003-3211',
          phone: '4084315581',
          defaultAddress: false,
          default: false,
          isSelected: false,
          verificationStatus: 'NOT_VERIFIED',
          deliveryPointValidation: 'INVALID_DELIVERY_POINT',
        },
      ],
      billingAddress: {
        firstName: 'John',
        lastName: 'Doe',
        addressLine1: '1 Main St',
        postalCode: '95131',
        city: 'San Jose',
        state: 'CA',
        country: 'US',
        phone: '4082383466',
        isSelected: false,
        isDefault: false,
      },
      billingErrors: [],
      totalPrice: 78.1,
      conditionals: {
        bopisOnlyBag: false,
        shippingAddressSelected: true,
        omniBag: false,
        hasRewards: false,
        isOrderTotalZero: false,
        isPhoneError: false,
        hasBillingAddressId: false,
        hasBopisItems: false,
        hasShipToAddressItems: true,
        isBopisGiftCardPurchase: false,
        showGiftCard: false,
        hasStoreName: false,
        cvvInvalidErrors: false,
        showPaypalButton: false,
        cardProcessingError: false,
        paymentFailedError: false,
        isAfterPayProcessingError: false,
        isGiftCardProcessingError: false,
        isKlarnaProcessingError: false,
        isPaymentPanelError: false,
        isPaypalProcessingError: false,
        isPaypalSelected: true,
        isAfterpaySelected: false,
        isPaypalButtonEnabled: true,
        isPaypalDisplayed: true,
        showAfterPayButton: true,
        isGiftCardCoversOrder: false,
        isLegacyRewardsCodeDisplayed: false,
        isBarclayCodeEnabled: true,
        isBarclaySavingCalculatorEnabled: true,
        isUpsAccessPoint: false,
        isPaymentModalEnabled: true,
      },
      errors: [],
      selectedAddress: {
        addressId: 'cdccca4c2c82472da6d19713a0b56a77',
        firstName: 'John',
        lastName: 'Doe',
        addressLine1: '1019 Pennsylvania Avenue',
        postalCode: '94107',
        city: 'San Francisco',
        state: 'CA',
        country: 'US',
        phone: '4154120001',
        isSelected: true,
        isDefault: false,
      },
      summaryView: {
        cardNumber: 'XXXX XXXX XXXX undefined',
        cardType: 1001,
        giftCards: [],
      },
      isCardInValid: false,
      selectedPaymentMethod: {
        paymentId: '575d7ade-cef5-4849-b732-3b2a6c7457e9',
        paymentIndex: 1,
        digitalWallet: {
          type: 'PAYPAL',
          paypal: {
            orderIdentifier: 'EC-45T42580SH618541M',
            deviceData: '{"correlation_id":"c27c8c522110c27afc11db6e5605f4ca"}',
            billingAddress: {
              firstName: 'John',
              lastName: 'Doe',
              addressLine1: '1 Main St',
              city: 'San Jose',
              state: 'CA',
              country: 'US',
              postalCode: '95131',
              phone: '4082383466',
              verificationStatus: 'VERIFIED',
              default: false,
            },
            isCBCC: false,
          },
        },
        isSelected: true,
        isDefault: false,
        cardInfo: {
          cardNumber: 'XXXX XXXX XXXX undefined',
          saveCCInProfile: true,
          cardType: 1001,
        },
        billingAddress: {
          firstName: 'John',
          lastName: 'Doe',
          addressLine1: '1 Main St',
          postalCode: '95131',
          city: 'San Jose',
          state: 'CA',
          country: 'US',
          phone: '4082383466',
          isSelected: false,
          isDefault: false,
        },
        isCardExpired: false,
      },
    },
    paypalLightBoxPanel: {
      flow: 'checkout',
      intent: 'order',
      displayName: 'Gap Inc',
      enableShippingAddress: true,
      shippingAddressEditable: false,
      amount: 78.1,
      currency: 'USD',
      locale: 'en_US',
      shippingAddressOverride: {
        recipientName: 'John Doe',
        line1: '1019 Pennsylvania Avenue',
        city: 'San Francisco',
        state: 'CA',
        postalCode: '94107',
        countryCode: 'US',
      },
    },
    pickupPanel: {
      storePickupInfoList: [],
      summaryView: {
        stores: [],
        totalQuantity: 0,
      },
    },
    placeOrderPanel: {
      appliedPromotions: [],
      bopis_order_type: 'shipItemsOnly',
      currencyCode: 'USD',
      donationTotal: '',
      totalPrice: '78.10',
      subTotal: 84.85,
      shippingChargesSubTotal: '',
      rewardsSubTotal: '',
      promoSubTotal: '',
      emailOptInIndicator: false,
      cvv: '',
      giftCards: [],
      giftCardSubTotal: '',
      isBopisOnlyBag: false,
      merchandiseSubTotal: 84.85,
      estimatedTax: 6.2,
      placeOrderErrors: [],
      customer: {
        isGuest: false,
        isLoggedIn: true,
        isRecognized: false,
      },
      conditionals: {
        isPopupError: false,
      },
      shippingMethods: [
        {
          shippingMethodName: 'No Rush',
          shippingTypeDescription: '7-9 business days',
          shippingPrice: 0,
          shippingTypeId: 7,
          isSelected: false,
          isEnabled: true,
          shippingId: 38697,
          deliveryDate: 'Jul 10th',
          deliveryWeekDay: 'Wednesday',
          maxDays: 9,
          minDays: 7,
        },
        {
          shippingMethodName: 'Basic',
          shippingTypeDescription: '5-7 business days',
          shippingPrice: 0,
          shippingTypeId: 7,
          isSelected: false,
          isEnabled: true,
          shippingId: 38704,
          deliveryDate: 'Jul 8th',
          deliveryWeekDay: 'Monday',
          maxDays: 7,
          minDays: 5,
        },
        {
          shippingMethodName: 'Standard',
          shippingTypeDescription: '3-5 business days',
          shippingPrice: 0,
          shippingTypeId: 1,
          isSelected: true,
          isEnabled: true,
          shippingId: 38702,
          deliveryDate: 'Jul 3rd',
          deliveryWeekDay: 'Wednesday',
          maxDays: 5,
          minDays: 3,
        },
        {
          shippingMethodName: 'Express',
          shippingTypeDescription: '2-3 business days',
          shippingPrice: 17,
          shippingTypeId: 3,
          isSelected: false,
          isEnabled: true,
          shippingId: 38698,
          deliveryDate: 'Jul 2nd',
          deliveryWeekDay: 'Tuesday',
          maxDays: 3,
          minDays: 2,
        },
        {
          shippingMethodName: 'Priority',
          shippingTypeDescription: '1 business day',
          shippingPrice: 25,
          shippingTypeId: 5,
          isSelected: false,
          isEnabled: true,
          shippingId: 38700,
          deliveryDate: 'Jun 28th',
          deliveryWeekDay: 'Friday',
          maxDays: 1,
          minDays: 1,
        },
      ],
      orderItems: [
        {
          appliedDiscounts: '',
          color: 'fresh white',
          imagePath: 'webcontent/0054/314/069/cn54314069.jpg',
          inventoryStatus: 'RESERVED',
          price: {
            discountedPrice: 24.95,
            regularPrice: 24.95,
            salePrice: 24.95,
          },
          productName: 'Cotton Vintage Crewneck T-Shirt',
          productSkuId: '7953460120001',
          productStyleId: '795346',
          productTypeName: 'Cotton Vintage Crewneck T-Shirt',
          quantity: 1,
          size: 'S',
          totalPrice: 24.95,
        },
        {
          appliedDiscounts: '',
          color: 'fresh white',
          imagePath: 'webcontent/0051/605/079/cn51605079.jpg',
          inventoryStatus: 'RESERVED',
          price: {
            discountedPrice: 12,
            regularPrice: 24.95,
            salePrice: 12,
          },
          productName: 'Organic Cotton Vintage V-Neck T-Shirt',
          productSkuId: '7401400220002',
          productStyleId: '740140',
          productTypeName: 'Organic Cotton Vintage V-Neck T-Shirt',
          quantity: 1,
          size: 'M',
          totalPrice: 12,
        },
        {
          appliedDiscounts: '',
          color: 'true black',
          imagePath: 'webcontent/0017/225/024/cn17225024.jpg',
          inventoryStatus: 'RESERVED',
          price: {
            discountedPrice: 34.95,
            regularPrice: 34.95,
            salePrice: 34.95,
          },
          productName: 'GapFit Tech Kids Joggers',
          productSkuId: '4941480021106',
          productStyleId: '494148',
          productTypeName: 'GapFit Tech Kids Joggers',
          quantity: 1,
          size: 'M (8)',
          totalPrice: 34.95,
        },
      ],
      shippingAddresses: [
        {
          addressId: 'cdccca4c2c82472da6d19713a0b56a77',
          firstName: 'John',
          lastName: 'Doe',
          addressLine1: '1019 Pennsylvania Avenue',
          addressLine2: '',
          postalCode: '94107',
          city: 'San Francisco',
          state: 'CA',
          country: 'US',
          phone: '4154120001',
          isDefault: false,
          addressLocationType: '',
          verificationStatus: 'VERIFIED',
          deliveryPointValidation: 'INVALID_DELIVERY_POINT',
          isSelected: true,
        },
        {
          addressId: '4e0af374ee11442db64b600d301b55a3',
          firstName: 'Harish A',
          lastName: 'Megharaj',
          addressLine1: '3000 E 1ST Ave',
          addressLine2: '',
          postalCode: '80206-5615',
          city: 'Denver',
          state: 'CO',
          country: 'US',
          phone: '4086790016',
          isDefault: false,
          addressLocationType: '',
          verificationStatus: 'NOT_VERIFIED',
          deliveryPointValidation: 'INVALID_DELIVERY_POINT',
          isSelected: false,
        },
        {
          addressId: '37a76692a3744349ad8d8e34295a85fa',
          firstName: 'Abc',
          lastName: 'Errrt',
          addressLine1: 'County Road R44',
          addressLine2: '',
          postalCode: '18311',
          city: 'iowa',
          state: 'IA',
          country: 'US',
          phone: '4086790016',
          isDefault: false,
          addressLocationType: '',
          verificationStatus: 'NOT_VERIFIED',
          deliveryPointValidation: 'INVALID_DELIVERY_POINT',
          isSelected: false,
        },
        {
          addressId: 'e81ffa7125684aee9d1af2b1c48065ff',
          firstName: 'Shruti Kabra',
          lastName: 'Gattani',
          addressLine1: '33 Union Sq W',
          addressLine2: '',
          postalCode: '10003-3211',
          city: 'New York',
          state: 'NY',
          country: 'US',
          phone: '4084315581',
          isDefault: false,
          addressLocationType: '',
          verificationStatus: 'NOT_VERIFIED',
          deliveryPointValidation: 'INVALID_DELIVERY_POINT',
          isSelected: false,
        },
      ],
      paymentMethods: [
        {
          paymentId: '575d7ade-cef5-4849-b732-3b2a6c7457e9',
          paymentIndex: 1,
          digitalWallet: {
            type: 'PAYPAL',
            paypal: {
              orderIdentifier: 'EC-45T42580SH618541M',
              deviceData: '{"correlation_id":"c27c8c522110c27afc11db6e5605f4ca"}',
              billingAddress: {
                firstName: 'John',
                lastName: 'Doe',
                addressLine1: '1 Main St',
                city: 'San Jose',
                state: 'CA',
                country: 'US',
                postalCode: '95131',
                phone: '4082383466',
                verificationStatus: 'VERIFIED',
                default: false,
              },
              isCBCC: false,
            },
          },
          isSelected: true,
          isDefault: false,
          cardInfo: {
            cardNumber: 'XXXX XXXX XXXX undefined',
            saveCCInProfile: true,
            cardType: 1001,
          },
          billingAddress: {
            firstName: 'John',
            lastName: 'Doe',
            addressLine1: '1 Main St',
            postalCode: '95131',
            city: 'San Jose',
            state: 'CA',
            country: 'US',
            phone: '4082383466',
            isSelected: false,
            isDefault: false,
          },
          isCardExpired: false,
        },
      ],
    },
    rewardsPanel: {
      conditionals: {
        isRewardsPanelDisplayed: true,
        isPartialResponse: false,
        isPromoRewardsApplied: false,
        isCvvError: false,
        isPromotionError: false,
        hasErrors: false,
      },
      brand: 'GP',
      market: 'US',
      isGuest: false,
      rewardErrorCode: '',
      tier: 'ENTHUSIAST',
      merchandiseSubTotal: 71.9,
      draftOrderId: '6100f81c-a2f8-4c95-a307-299f60aee433',
      pointRewards: {
        pointRewardsCard: {
          rewardType: 'pointRewards',
          availablePoints: 10371,
          pointsToValueList: [
            {
              points: 7100,
              amount: 71,
            },
            {
              points: 7000,
              amount: 70,
            },
            {
              points: 6900,
              amount: 69,
            },
            {
              points: 6800,
              amount: 68,
            },
            {
              points: 6700,
              amount: 67,
            },
            {
              points: 6600,
              amount: 66,
            },
            {
              points: 6500,
              amount: 65,
            },
            {
              points: 6400,
              amount: 64,
            },
            {
              points: 6300,
              amount: 63,
            },
            {
              points: 6200,
              amount: 62,
            },
            {
              points: 6100,
              amount: 61,
            },
            {
              points: 6000,
              amount: 60,
            },
            {
              points: 5900,
              amount: 59,
            },
            {
              points: 5800,
              amount: 58,
            },
            {
              points: 5700,
              amount: 57,
            },
            {
              points: 5600,
              amount: 56,
            },
            {
              points: 5500,
              amount: 55,
            },
            {
              points: 5400,
              amount: 54,
            },
            {
              points: 5300,
              amount: 53,
            },
            {
              points: 5200,
              amount: 52,
            },
            {
              points: 5100,
              amount: 51,
            },
            {
              points: 5000,
              amount: 50,
            },
            {
              points: 4900,
              amount: 49,
            },
            {
              points: 4800,
              amount: 48,
            },
            {
              points: 4700,
              amount: 47,
            },
            {
              points: 4600,
              amount: 46,
            },
            {
              points: 4500,
              amount: 45,
            },
            {
              points: 4400,
              amount: 44,
            },
            {
              points: 4300,
              amount: 43,
            },
            {
              points: 4200,
              amount: 42,
            },
            {
              points: 4100,
              amount: 41,
            },
            {
              points: 4000,
              amount: 40,
            },
            {
              points: 3900,
              amount: 39,
            },
            {
              points: 3800,
              amount: 38,
            },
            {
              points: 3700,
              amount: 37,
            },
            {
              points: 3600,
              amount: 36,
            },
            {
              points: 3500,
              amount: 35,
            },
            {
              points: 3400,
              amount: 34,
            },
            {
              points: 3300,
              amount: 33,
            },
            {
              points: 3200,
              amount: 32,
            },
            {
              points: 3100,
              amount: 31,
            },
            {
              points: 3000,
              amount: 30,
            },
            {
              points: 2900,
              amount: 29,
            },
            {
              points: 2800,
              amount: 28,
            },
            {
              points: 2700,
              amount: 27,
            },
            {
              points: 2600,
              amount: 26,
            },
            {
              points: 2500,
              amount: 25,
            },
            {
              points: 2400,
              amount: 24,
            },
            {
              points: 2300,
              amount: 23,
            },
            {
              points: 2200,
              amount: 22,
            },
            {
              points: 2100,
              amount: 21,
            },
            {
              points: 2000,
              amount: 20,
            },
            {
              points: 1900,
              amount: 19,
            },
            {
              points: 1800,
              amount: 18,
            },
            {
              points: 1700,
              amount: 17,
            },
            {
              points: 1600,
              amount: 16,
            },
            {
              points: 1500,
              amount: 15,
            },
            {
              points: 1400,
              amount: 14,
            },
            {
              points: 1300,
              amount: 13,
            },
            {
              points: 1200,
              amount: 12,
            },
            {
              points: 1100,
              amount: 11,
            },
            {
              points: 1000,
              amount: 10,
            },
            {
              points: 900,
              amount: 9,
            },
            {
              points: 800,
              amount: 8,
            },
            {
              points: 700,
              amount: 7,
            },
            {
              points: 600,
              amount: 6,
            },
            {
              points: 500,
              amount: 5,
            },
            {
              points: 400,
              amount: 4,
            },
            {
              points: 300,
              amount: 3,
            },
            {
              points: 200,
              amount: 2,
            },
            {
              points: 100,
              amount: 1,
            },
          ],
          isApplied: false,
          hasEnoughPoints: true,
          lessThanHundred: 0,
          minPoints: 100,
        },
        actionParams: {
          loyaltyEventType: 'PayWithPoints',
          loyaltyTierStatus: 'ENTHUSIAST',
          reasonCode: 539,
        },
      },
      summaryView: {
        maxReward: 71,
        amountApplied: 0,
        rewardCodes: [],
      },
    },
    shippingAddressPanel: {
      conditionals: {
        isHubBoxEnabled: true,
      },
      shippingAddressList: [
        {
          addressId: 'cdccca4c2c82472da6d19713a0b56a77',
          firstName: 'John',
          lastName: 'Doe',
          addressLine1: '1019 Pennsylvania Avenue',
          addressLine2: '',
          postalCode: '94107',
          city: 'San Francisco',
          state: 'CA',
          country: 'US',
          phone: '4154120001',
          isDefault: false,
          addressLocationType: '',
          verificationStatus: 'VERIFIED',
          deliveryPointValidation: 'INVALID_DELIVERY_POINT',
          isSelected: true,
        },
        {
          addressId: '4e0af374ee11442db64b600d301b55a3',
          firstName: 'Harish A',
          lastName: 'Megharaj',
          addressLine1: '3000 E 1ST Ave',
          addressLine2: '',
          postalCode: '80206-5615',
          city: 'Denver',
          state: 'CO',
          country: 'US',
          phone: '4086790016',
          isDefault: false,
          addressLocationType: '',
          verificationStatus: 'NOT_VERIFIED',
          deliveryPointValidation: 'INVALID_DELIVERY_POINT',
          isSelected: false,
        },
        {
          addressId: '37a76692a3744349ad8d8e34295a85fa',
          firstName: 'Abc',
          lastName: 'Errrt',
          addressLine1: 'County Road R44',
          addressLine2: '',
          postalCode: '18311',
          city: 'iowa',
          state: 'IA',
          country: 'US',
          phone: '4086790016',
          isDefault: false,
          addressLocationType: '',
          verificationStatus: 'NOT_VERIFIED',
          deliveryPointValidation: 'INVALID_DELIVERY_POINT',
          isSelected: false,
        },
        {
          addressId: 'e81ffa7125684aee9d1af2b1c48065ff',
          firstName: 'Shruti Kabra',
          lastName: 'Gattani',
          addressLine1: '33 Union Sq W',
          addressLine2: '',
          postalCode: '10003-3211',
          city: 'New York',
          state: 'NY',
          country: 'US',
          phone: '4084315581',
          isDefault: false,
          addressLocationType: '',
          verificationStatus: 'NOT_VERIFIED',
          deliveryPointValidation: 'INVALID_DELIVERY_POINT',
          isSelected: false,
        },
      ],
      lineItemCount: 3,
      bopisInBag: false,
      isOmniBag: false,
      deliveryGroupId: 'GapRegularGroup',
      hasSavedShippingAddresses: true,
      hasShippingAddressSelected: false,
      summaryView: {
        isUPSStore: false,
        fullName: 'John Doe',
        fullAddress: '1019 Pennsylvania Avenue, San Francisco, CA, US 94107',
      },
      formInitialValues: {
        fullName: 'John Doe',
        phone: '(*************',
      },
      notEligibleShippingItems: [],
      hasNoEligibleShippingItems: false,
      shouldDisplayGifting: true,
      showUPS: true,
    },
    signInPanel: {
      isGuest: false,
      isLoggedIn: true,
      isRecognized: false,
      marketCode: 'US',
      emailId: 'l*****@gmail.com',
      lineItems: [
        {
          itemId: '328ca306092a46dbac88ac7418c6dfe9',
          brand: 'GP',
          productSkuId: '7953460120001',
          productName: 'Cotton Vintage Crewneck T-Shirt',
          customerChoiceNumber: '795346012',
          productTypeName: 'petite short-sleeved tees',
          productStyleId: '795346',
          color: 'fresh white',
          size: 'S',
          colorStyleNumber: '795346012',
          variantDescription: 'Regular',
          merchandiseType: '7',
          inventoryStatusId: 5,
          isBackOrderItem: false,
          backOrdershippingDate: false,
          autoAdded: false,
          noReturnItem: false,
          returnByMailItem: true,
          noIntlShipping: true,
          marketingFlag: '0',
          giftWrappable: true,
          productStyleDescription: 'Cotton Vintage Crewneck T-Shirt',
          primaryCategoryName: 'T-Shirts & Tanks',
          productURL: 'browse/product.do?pid=7953460120001',
          price: {
            regularPrice: 24.95,
            salePrice: 24.95,
            discountedPrice: 24.95,
            percentageOff: 0,
          },
          regularPrice: '24.95',
          salePrice: '24.95',
          discountedPrice: '24.95',
          totalPrice: '24.95',
          quantity: '1',
          imagePath: 'webcontent/0054/314/069/cn54314069.jpg',
          inventoryStatus: 'RESERVED',
          vendorId: '700086785',
          webVendorName: 'PRATIBHA SYNTEX LTD',
          showSellerName: false,
          excludedFromPromotion: false,
          madeToOrder: false,
          estimatedShippingDate: '',
          eligibleReturnLocationCode: 'M',
          isExcludedFromRewardFreeShipping: false,
          vendorStyleNumber: '',
          vendorUPCCode: '',
          appliedDiscounts: [],
          storeId: '',
        },
        {
          itemId: '11ae3e6726a3485ab8d6efa0ede26457',
          brand: 'GP',
          productSkuId: '7401400220002',
          productName: 'Organic Cotton Vintage V-Neck T-Shirt',
          customerChoiceNumber: '740140022',
          productTypeName: 'petite short-sleeved tees',
          productStyleId: '740140',
          color: 'fresh white',
          size: 'M',
          colorStyleNumber: '740140022',
          variantDescription: 'Regular',
          merchandiseType: '7',
          inventoryStatusId: 5,
          isBackOrderItem: false,
          backOrdershippingDate: false,
          autoAdded: false,
          noReturnItem: false,
          returnByMailItem: true,
          noIntlShipping: true,
          marketingFlag: '0',
          giftWrappable: true,
          productStyleDescription: 'Organic Cotton Vintage V-Neck T-Shirt',
          primaryCategoryName: 'T-Shirts & Tanks',
          productURL: 'browse/product.do?pid=7401400220002',
          price: {
            regularPrice: 24.95,
            salePrice: 12,
            discountedPrice: 12,
            percentageOff: 51,
          },
          regularPrice: '24.95',
          salePrice: '12',
          discountedPrice: '12',
          totalPrice: '12',
          quantity: '1',
          imagePath: 'webcontent/0051/605/079/cn51605079.jpg',
          inventoryStatus: 'RESERVED',
          vendorId: '700086785',
          webVendorName: 'PRATIBHA SYNTEX LTD',
          showSellerName: false,
          excludedFromPromotion: false,
          madeToOrder: false,
          estimatedShippingDate: '',
          eligibleReturnLocationCode: 'M',
          isExcludedFromRewardFreeShipping: false,
          vendorStyleNumber: '',
          vendorUPCCode: '',
          appliedDiscounts: [],
          storeId: '',
        },
        {
          itemId: 'e35f0d06151e4c929a6d60fb77b2cf2d',
          brand: 'GP',
          productSkuId: '4941480021106',
          productName: 'GapFit Tech Kids Joggers',
          customerChoiceNumber: '494148002',
          productTypeName: 'boys active bottoms',
          productStyleId: '494148',
          color: 'true black',
          size: 'M (8)',
          colorStyleNumber: '494148002',
          variantDescription: 'Regular',
          merchandiseType: '7',
          inventoryStatusId: 5,
          isBackOrderItem: false,
          backOrdershippingDate: false,
          autoAdded: false,
          noReturnItem: false,
          returnByMailItem: false,
          noIntlShipping: true,
          marketingFlag: '0',
          giftWrappable: true,
          productStyleDescription: 'GapFit Tech Kids Joggers',
          primaryCategoryName: 'GapFit & Activewear',
          productURL: 'browse/product.do?pid=4941480021106',
          price: {
            regularPrice: 34.95,
            salePrice: 34.95,
            discountedPrice: 34.95,
            percentageOff: 0,
          },
          regularPrice: '34.95',
          salePrice: '34.95',
          discountedPrice: '34.95',
          totalPrice: '34.95',
          quantity: '1',
          imagePath: 'webcontent/0017/225/024/cn17225024.jpg',
          inventoryStatus: 'RESERVED',
          vendorId: '000003489',
          webVendorName: 'MAKALOT INDUSTRIAL CO LTD',
          showSellerName: false,
          excludedFromPromotion: false,
          madeToOrder: false,
          estimatedShippingDate: '',
          eligibleReturnLocationCode: '',
          isExcludedFromRewardFreeShipping: false,
          vendorStyleNumber: '',
          vendorUPCCode: '',
          appliedDiscounts: [],
          storeId: '',
        },
      ],
    },
    deliveryGroupPanel: {
      deliveryGroupLists: [
        {
          deliveryGroupId: 'GapRegularGroup',
          pickupOrderType: 'shipItemsOnly',
          lineItemList: [
            {
              itemId: '328ca306092a46dbac88ac7418c6dfe9',
              brand: 'GP',
              productSkuId: '7953460120001',
              productName: 'Cotton Vintage Crewneck T-Shirt',
              customerChoiceNumber: '795346012',
              productTypeName: 'petite short-sleeved tees',
              productStyleId: '795346',
              color: 'fresh white',
              size: 'S',
              colorStyleNumber: '795346012',
              variantDescription: 'Regular',
              merchandiseType: '7',
              inventoryStatusId: 5,
              isBackOrderItem: false,
              backOrdershippingDate: false,
              autoAdded: false,
              noReturnItem: false,
              returnByMailItem: true,
              noIntlShipping: true,
              marketingFlag: '0',
              giftWrappable: true,
              productStyleDescription: 'Cotton Vintage Crewneck T-Shirt',
              primaryCategoryName: 'T-Shirts & Tanks',
              productURL: 'browse/product.do?pid=7953460120001',
              price: {
                regularPrice: 24.95,
                salePrice: 24.95,
                discountedPrice: 24.95,
                percentageOff: 0,
              },
              regularPrice: '24.95',
              salePrice: '24.95',
              discountedPrice: '24.95',
              totalPrice: '24.95',
              quantity: '1',
              imagePath: 'webcontent/0054/314/069/cn54314069.jpg',
              inventoryStatus: 'RESERVED',
              vendorId: '700086785',
              webVendorName: 'PRATIBHA SYNTEX LTD',
              showSellerName: false,
              excludedFromPromotion: false,
              madeToOrder: false,
              estimatedShippingDate: '',
              eligibleReturnLocationCode: 'M',
              isExcludedFromRewardFreeShipping: false,
              vendorStyleNumber: '',
              vendorUPCCode: '',
              appliedDiscounts: [],
              storeId: '',
            },
            {
              itemId: '11ae3e6726a3485ab8d6efa0ede26457',
              brand: 'GP',
              productSkuId: '7401400220002',
              productName: 'Organic Cotton Vintage V-Neck T-Shirt',
              customerChoiceNumber: '740140022',
              productTypeName: 'petite short-sleeved tees',
              productStyleId: '740140',
              color: 'fresh white',
              size: 'M',
              colorStyleNumber: '740140022',
              variantDescription: 'Regular',
              merchandiseType: '7',
              inventoryStatusId: 5,
              isBackOrderItem: false,
              backOrdershippingDate: false,
              autoAdded: false,
              noReturnItem: false,
              returnByMailItem: true,
              noIntlShipping: true,
              marketingFlag: '0',
              giftWrappable: true,
              productStyleDescription: 'Organic Cotton Vintage V-Neck T-Shirt',
              primaryCategoryName: 'T-Shirts & Tanks',
              productURL: 'browse/product.do?pid=7401400220002',
              price: {
                regularPrice: 24.95,
                salePrice: 12,
                discountedPrice: 12,
                percentageOff: 51,
              },
              regularPrice: '24.95',
              salePrice: '12',
              discountedPrice: '12',
              totalPrice: '12',
              quantity: '1',
              imagePath: 'webcontent/0051/605/079/cn51605079.jpg',
              inventoryStatus: 'RESERVED',
              vendorId: '700086785',
              webVendorName: 'PRATIBHA SYNTEX LTD',
              showSellerName: false,
              excludedFromPromotion: false,
              madeToOrder: false,
              estimatedShippingDate: '',
              eligibleReturnLocationCode: 'M',
              isExcludedFromRewardFreeShipping: false,
              vendorStyleNumber: '',
              vendorUPCCode: '',
              appliedDiscounts: [],
              storeId: '',
            },
            {
              itemId: 'e35f0d06151e4c929a6d60fb77b2cf2d',
              brand: 'GP',
              productSkuId: '4941480021106',
              productName: 'GapFit Tech Kids Joggers',
              customerChoiceNumber: '494148002',
              productTypeName: 'boys active bottoms',
              productStyleId: '494148',
              color: 'true black',
              size: 'M (8)',
              colorStyleNumber: '494148002',
              variantDescription: 'Regular',
              merchandiseType: '7',
              inventoryStatusId: 5,
              isBackOrderItem: false,
              backOrdershippingDate: false,
              autoAdded: false,
              noReturnItem: false,
              returnByMailItem: false,
              noIntlShipping: true,
              marketingFlag: '0',
              giftWrappable: true,
              productStyleDescription: 'GapFit Tech Kids Joggers',
              primaryCategoryName: 'GapFit & Activewear',
              productURL: 'browse/product.do?pid=4941480021106',
              price: {
                regularPrice: 34.95,
                salePrice: 34.95,
                discountedPrice: 34.95,
                percentageOff: 0,
              },
              regularPrice: '34.95',
              salePrice: '34.95',
              discountedPrice: '34.95',
              totalPrice: '34.95',
              quantity: '1',
              imagePath: 'webcontent/0017/225/024/cn17225024.jpg',
              inventoryStatus: 'RESERVED',
              vendorId: '000003489',
              webVendorName: 'MAKALOT INDUSTRIAL CO LTD',
              showSellerName: false,
              excludedFromPromotion: false,
              madeToOrder: false,
              estimatedShippingDate: '',
              eligibleReturnLocationCode: '',
              isExcludedFromRewardFreeShipping: false,
              vendorStyleNumber: '',
              vendorUPCCode: '',
              appliedDiscounts: [],
              storeId: '',
            },
          ],
          offerDetails: "NOT A GIFT?.\nWe'll do our best to ship as quickly as possible. Our fulfillment teams would appreciate the extra time.",
          isBackOrder: false,
          isMadeToOrder: false,
          isDropShipItem: false,
          webVendorName: '',
          shippingMethodList: [
            {
              shippingMethodName: 'No Rush',
              shippingTypeDescription: '7-9 business days',
              shippingPrice: 0,
              shippingTypeId: 7,
              isSelected: false,
              isEnabled: true,
              shippingId: 38697,
              deliveryDate: '10th',
              deliveryWeekDay: 'Wednesday,',
              deliveryMonth: 'Jul',
              maxDays: 9,
              minDays: 7,
            },
            {
              shippingMethodName: 'Basic',
              shippingTypeDescription: '5-7 business days',
              shippingPrice: 0,
              shippingTypeId: 7,
              isSelected: false,
              isEnabled: true,
              shippingId: 38704,
              deliveryDate: '8th',
              deliveryWeekDay: 'Monday,',
              deliveryMonth: 'Jul',
              maxDays: 7,
              minDays: 5,
            },
            {
              shippingMethodName: 'Standard',
              shippingTypeDescription: '3-5 business days',
              shippingPrice: 0,
              shippingTypeId: 1,
              isSelected: true,
              isEnabled: true,
              shippingId: 38702,
              deliveryDate: '3rd',
              deliveryWeekDay: 'Wednesday,',
              deliveryMonth: 'Jul',
              maxDays: 5,
              minDays: 3,
            },
            {
              shippingMethodName: 'Express',
              shippingTypeDescription: '2-3 business days',
              shippingPrice: 17,
              shippingTypeId: 3,
              isSelected: false,
              isEnabled: true,
              shippingId: 38698,
              deliveryDate: '2nd',
              deliveryWeekDay: 'Tuesday,',
              deliveryMonth: 'Jul',
              maxDays: 3,
              minDays: 2,
            },
            {
              shippingMethodName: 'Priority',
              shippingTypeDescription: '1 business day',
              shippingPrice: 25,
              shippingTypeId: 5,
              isSelected: false,
              isEnabled: true,
              shippingId: 38700,
              deliveryDate: '28th',
              deliveryWeekDay: 'Friday,',
              deliveryMonth: 'Jun',
              maxDays: 1,
              minDays: 1,
            },
          ],
        },
      ],
      summaryView: [
        {
          id: 'GapRegularGroup',
          name: 'Standard',
          itemCount: 3,
          deliveryBy: 'By Wednesday, Jul 3rd',
          deliveryWeekDay: 'Wednesday,',
          deliveryDate: '3rd',
          deliveryMonth: 'Jul',
          shippingPrice: 0,
        },
      ],
      currency: 'USD',
      payments: [
        {
          paymentId: '575d7ade-cef5-4849-b732-3b2a6c7457e9',
          paymentIndex: 1,
          digitalWallet: {
            type: 'PAYPAL',
            paypal: {
              orderIdentifier: 'EC-45T42580SH618541M',
              deviceData: '{"correlation_id":"c27c8c522110c27afc11db6e5605f4ca"}',
              billingAddress: {
                firstName: 'John',
                lastName: 'Doe',
                addressLine1: '1 Main St',
                city: 'San Jose',
                state: 'CA',
                country: 'US',
                postalCode: '95131',
                phone: '4082383466',
                verificationStatus: 'VERIFIED',
                default: false,
              },
              isCBCC: false,
            },
          },
        },
      ],
      isEasyEnrollEligible: false,
      errors: [],
      hasDropshipItems: false,
      has4101Error: false,
    },
  },
  session: {
    email: 'l*****@gmail.com',
    recognition_status: 'authenticated',
  },
  draftOrderId: '6100f81c-a2f8-4c95-a307-299f60aee433',
  loading: false,
} as unknown as DraftOrder;

export const mockDraftOrderGuestUser = {
  panels: {
    bagPanel: {
      lineItems: [
        {
          brand: 'ON',
          productSkuId: '8650260120003',
          productName: 'Extra High-Waisted Fleece Pants for Women',
          productTypeName: 'z womens graphic tees',
          productStyleId: '865026',
          color: 'Sea Salt',
          size: 'L',
          price: {
            regularPrice: 34.99,
            salePrice: 34.99,
            discountedPrice: 33.24,
            mupPromoPrice: 34.99,
          },
          regularPrice: '34.99',
          salePrice: '34.99',
          discountedPrice: '33.24',
          totalPrice: '33.24',
          quantity: '1',
          imagePath: 'webcontent/0054/818/584/cn54818584.jpg',
          inventoryStatus: 'RESERVED',
          appliedDiscounts: [],
          storeId: '',
        },
      ],
      bopisItems: [],
      bagTotal: 33.24,
    },
    checkoutPanel: {
      conditionals: {
        hasBopisItems: false,
        isBopisOnlyBag: false,
        isBopisGiftCardPurchase: false,
        bopisInBag: false,
        isInvalidBagError: false,
        isOrderLineItems: true,
        shouldDonationsDisplay: true,
        isSummaryViewEnabled: true,
        cvvInvalidErrors: false,
        cardProcessingError: false,
        isShippingMethods: true,
        isShippingAddress: true,
        isShippingAddressSelected: true,
        isPayment: false,
        isOrderReadyToPlace: true,
      },
      totalPrice: 44.57,
      lineItems: [
        {
          mergeType: 'Standard PDP',
          itemId: 'ec290347f7014f89b3201bf7bef77540',
          brand: 'ON',
          productSkuId: '8650260120003',
          productName: 'Extra High-Waisted Fleece Pants for Women',
          customerChoiceNumber: '865026012',
          productTypeName: 'z womens graphic tees',
          productStyleId: '865026',
          color: 'Sea Salt',
          size: 'L',
          colorStyleNumber: '865026012',
          variantDescription: 'Regular',
          merchandiseType: '7',
          inventoryStatusId: 5,
          isBackOrderItem: false,
          backOrdershippingDate: false,
          autoAdded: false,
          noReturnItem: false,
          returnByMailItem: false,
          noIntlShipping: true,
          marketingFlag: '0',
          giftWrappable: true,
          productStyleDescription: 'Extra High-Waisted Fleece Pants for Women',
          primaryCategoryName: 'T-shirts',
          productURL: 'browse/product.do?pid=8650260120003',
          price: {
            regularPrice: 34.99,
            salePrice: 34.99,
            discountedPrice: 33.24,
            percentageOff: 0,
          },
          regularPrice: '34.99',
          salePrice: '34.99',
          discountedPrice: '33.24',
          totalPrice: '33.24',
          quantity: '1',
          imagePath: 'webcontent/0054/818/584/cn54818584.jpg',
          inventoryStatus: 'RESERVED',
          vendorId: '000002910',
          webVendorName: 'SHINWON CORPORATION',
          showSellerName: false,
          excludedFromPromotion: false,
          madeToOrder: false,
          estimatedShippingDate: '',
          eligibleReturnLocationCode: '',
          isExcludedFromRewardFreeShipping: false,
          vendorStyleNumber: '',
          vendorUPCCode: '',
          appliedDiscounts: [],
          storeId: '',
        },
      ],
      totalSavings: 0,
      locale: 'en_US',
      bagType: {
        bopisOnlyBag: false,
        omniBag: false,
        shipToHomeBag: true,
      },
      pickupOrderType: 'shipItemsOnly',
      summaryView: {
        shippingAddressPanel: 'C',
        deliveryGroupPanel: 'E',
        pickupPanel: 'H',
        paymentPanel: 'R',
        rewardsPanel: 'H',
        showDonationPanel: true,
      },
      checkoutModuleStatus:
        'shippingAddressPanel:collapsed|deliveryGroupPanel:expanded|pickupPanel:hidden|paymentPanel:required|rewardsPanel:hidden|donationPanel:expanded',
    },
    donationPanel: {
      selectedDonationOption: {
        amount: 0,
      },
      donationId: '',
      availableDonations: {
        brand: 'ON',
        charityName: 'Boys & Girls Clubs of America',
        charityInfo:
          'With ONward!, Old Navy partners with non-profits to empower the next generation with real-world skills, training and jobs to make a difference in our communities. Join our mission to turn learners into leaders with a nonrefundable donation to Boys & Girls Clubs of America.',
        availableAmounts: [
          {
            amount: 0,
          },
          {
            amount: 1,
          },
          {
            amount: 3,
          },
          {
            amount: 5,
          },
        ],
        imageUri: '/Asset_Archive/ONWeb/content/donations/BGCA.jpg',
        taxId: '12-5562976',
      },
      availableDonationsDropdownValues: [0, 1, 3, 5],
    },
    giftCardPanel: {
      totalPrice: 44.57,
      appliedGiftCards: [],
      errors: [],
      conditionals: {
        hasGiftCardsApplied: false,
      },
      isBopisGiftCardPurchase: false,
      isGiftcardPanelExpanded: false,
      isFormShown: true,
    },
    orderSummaryPanel: {
      isOrdersummarylineItemEnabled: false,
      hasPromotion: true,
      promotionDescription: 1,
      isBopisOnly: false,
      subTotal: 34.99,
      hasShippingPrice: true,
      shippingPrice: 7,
      shippingMessage: '(5-7 business days)',
      hasSavings: true,
      totalSavings: -1.75,
      hasRewards: false,
      rewardsSubTotal: 0,
      giftCardsTotal: 0,
      hasGiftCards: false,
      estimatedTax: 4.33,
      hasDonations: false,
      donationsTotal: 0,
      totalPrice: '44.57',
      retailDeliveryFee: 0,
      markdownPromoSavings: 1.75,
      rewardsSavings: 0,
      isAfterpaySelected: false,
    },
    paymentPanel: {
      paymentMethods: [
        {
          paymentId: '74f49f8e-cd64-494e-be52-e41ad89e8ee6',
          paymentIndex: 1,
          creditCard: {
            cardType: '1',
            type: 'VISA',
            cardToken: 'foobar',
            cardTokenFormat: 'VaultId',
            expiryMonth: '12',
            expiryYear: '2033',
            lastFourDigits: '1111',
            billingAddress: {
              firstName: 'Shruti Kabra',
              lastName: 'Gattani',
              addressLine1: '33 Union Sq Apt 1423',
              city: 'Union City',
              state: 'CA',
              country: 'US',
              postalCode: '94587-3571',
              phone: '4084315581',
              verificationStatus: 'NOT_VERIFIED',
              deliveryPointValidation: 'INVALID_DELIVERY_POINT',
              default: false,
            },
            cvvValidated: false,
            cvvRequired: true,
            temporary: false,
            defaultCard: false,
            isPLCC: false,
          },
          errorList: [
            {
              errorCode: '710',
              moreInfo: '/swagger-ui.html',
              userMessage: 'For your security, please re-enter your credit card information to proceed.',
            },
          ],
          isSelected: true,
          isDefault: false,
          cardInfo: {
            cardId: 'foobar',
            cardNumber: 'XXXX XXXX XXXX 1111',
            expirationMonth: '12',
            expirationYear: '2033',
            lastFourDigits: '1111',
            isCvvValidated: false,
            saveCCInProfile: true,
            temporaryCardIndicator: false,
            isPLCC: false,
            cardType: 1,
            cvvRequired: true,
            defaultCard: false,
          },
          billingAddress: {
            firstName: 'Shruti Kabra',
            lastName: 'Gattani',
            addressLine1: '33 Union Sq Apt 1423',
            postalCode: '94587-3571',
            city: 'Union City',
            state: 'CA',
            country: 'US',
            phone: '4084315581',
            isSelected: false,
            isDefault: false,
          },
          isCardExpired: false,
        },
      ],
      paymentId: '74f49f8e-cd64-494e-be52-e41ad89e8ee6',
      validGiftCardSubTotal: 0,
      shippingAddresses: [
        {
          firstName: 'Shruti Kabra',
          lastName: 'Gattani',
          addressLine1: '33 Union Sq Apt 1423',
          postalCode: '94587-3571',
          city: 'Union City',
          state: 'CA',
          country: 'US',
          phone: '4084315581',
          isSelected: true,
          isDefault: false,
        },
      ],
      billingAddress: {
        firstName: 'Shruti Kabra',
        lastName: 'Gattani',
        addressLine1: '33 Union Sq Apt 1423',
        postalCode: '94587-3571',
        city: 'Union City',
        state: 'CA',
        country: 'US',
        phone: '4084315581',
        isSelected: false,
        isDefault: false,
      },
      billingErrors: [],
      totalPrice: 44.57,
      conditionals: {
        bopisOnlyBag: false,
        shippingAddressSelected: true,
        omniBag: false,
        hasRewards: false,
        isOrderTotalZero: false,
        isPhoneError: false,
        hasBillingAddressId: false,
        hasBopisItems: false,
        hasShipToAddressItems: true,
        isBopisGiftCardPurchase: false,
        showGiftCard: true,
        hasStoreName: false,
        cvvInvalidErrors: true,
        showPaypalButton: true,
        cardProcessingError: false,
        paymentFailedError: false,
        isAfterPayProcessingError: false,
        isGiftCardProcessingError: false,
        isKlarnaProcessingError: false,
        isPaymentPanelError: true,
        isPaypalProcessingError: false,
        isPaypalSelected: false,
        isAfterpaySelected: false,
        isPaypalButtonEnabled: true,
        isPaypalDisplayed: true,
        showAfterPayButton: true,
        isGiftCardCoversOrder: false,
        isLegacyRewardsCodeDisplayed: true,
        isBarclayCodeEnabled: true,
        isBarclaySavingCalculatorEnabled: true,
        isUpsAccessPoint: false,
        isPaymentModalEnabled: true,
      },
      errors: ['710'],
      selectedAddress: {
        firstName: 'Shruti Kabra',
        lastName: 'Gattani',
        addressLine1: '33 Union Sq Apt 1423',
        postalCode: '94587-3571',
        city: 'Union City',
        state: 'CA',
        country: 'US',
        phone: '4084315581',
        isSelected: true,
        isDefault: false,
      },
      summaryView: {
        cardNumber: 'XXXX XXXX XXXX 1111',
        cardType: 1,
        lastFourDigits: '1111',
        giftCards: [],
      },
      isCardInValid: false,
      selectedPaymentMethod: {
        paymentId: '74f49f8e-cd64-494e-be52-e41ad89e8ee6',
        paymentIndex: 1,
        creditCard: {
          cardType: '1',
          type: 'VISA',
          cardToken: 'foobar',
          cardTokenFormat: 'VaultId',
          expiryMonth: '12',
          expiryYear: '2033',
          lastFourDigits: '1111',
          billingAddress: {
            firstName: 'Shruti Kabra',
            lastName: 'Gattani',
            addressLine1: '33 Union Sq Apt 1423',
            city: 'Union City',
            state: 'CA',
            country: 'US',
            postalCode: '94587-3571',
            phone: '4084315581',
            verificationStatus: 'NOT_VERIFIED',
            deliveryPointValidation: 'INVALID_DELIVERY_POINT',
            default: false,
          },
          cvvValidated: false,
          cvvRequired: true,
          temporary: false,
          defaultCard: false,
          isPLCC: false,
        },
        errorList: [
          {
            errorCode: '710',
            moreInfo: '/swagger-ui.html',
            userMessage: 'For your security, please re-enter your credit card information to proceed.',
          },
        ],
        isSelected: true,
        isDefault: false,
        cardInfo: {
          cardId: '781D3F501513C68882D072F61D36EEE0',
          cardNumber: 'XXXX XXXX XXXX 1111',
          expirationMonth: '12',
          expirationYear: '2033',
          lastFourDigits: '1111',
          isCvvValidated: false,
          saveCCInProfile: true,
          temporaryCardIndicator: false,
          isPLCC: false,
          cardType: 1,
          cvvRequired: true,
          defaultCard: false,
        },
        billingAddress: {
          firstName: 'Shruti Kabra',
          lastName: 'Gattani',
          addressLine1: '33 Union Sq Apt 1423',
          postalCode: '94587-3571',
          city: 'Union City',
          state: 'CA',
          country: 'US',
          phone: '4084315581',
          isSelected: false,
          isDefault: false,
        },
        isCardExpired: false,
      },
    },
    paypalLightBoxPanel: {
      flow: 'checkout',
      intent: 'order',
      displayName: 'Gap Inc',
      enableShippingAddress: true,
      shippingAddressEditable: false,
      amount: 44.57,
      currency: 'USD',
      locale: 'en_US',
      shippingAddressOverride: {
        recipientName: 'Shruti Kabra Gattani',
        line1: '33 Union Sq Apt 1423',
        city: 'Union City',
        state: 'CA',
        postalCode: '94587-3571',
        countryCode: 'US',
      },
    },
    pickupPanel: {
      storePickupInfoList: [],
      summaryView: {
        stores: [],
        totalQuantity: 0,
      },
    },
    placeOrderPanel: {
      appliedPromotions: [
        {
          code: 'ID-262758',
          id: '262758',
          promoDiscountTotal: 1.75,
          displayName: 'get 5% off',
          promotionDescription: 'get 5% off',
          automatic: true,
          shippingPromo: false,
          orderLevel: true,
          reward: false,
        },
      ],
      bopis_order_type: 'shipItemsOnly',
      currencyCode: 'USD',
      donationTotal: '',
      totalPrice: '44.57',
      subTotal: 34.99,
      shippingChargesSubTotal: 7,
      rewardsSubTotal: '',
      promoSubTotal: '',
      emailOptInIndicator: false,
      cvv: '',
      giftCards: [],
      giftCardSubTotal: '',
      isBopisOnlyBag: false,
      merchandiseSubTotal: 34.99,
      estimatedTax: 4.33,
      placeOrderErrors: [
        {
          errorCode: '710',
          userMessage: 'For your security, please re-enter your credit card information to proceed.',
        },
      ],
      customer: {
        isGuest: true,
        isLoggedIn: false,
        isRecognized: false,
      },
      conditionals: {
        isPopupError: false,
      },
      shippingMethods: [
        {
          shippingMethodName: 'No Rush',
          shippingTypeDescription: '7-9 business days',
          shippingPrice: 5,
          shippingTypeId: 7,
          isSelected: false,
          isEnabled: true,
          shippingId: 38696,
          deliveryDate: 'Sep 16th',
          deliveryWeekDay: 'Monday',
          maxDays: 9,
          minDays: 7,
        },
        {
          shippingMethodName: 'Basic',
          shippingTypeDescription: '5-7 business days',
          shippingPrice: 7,
          shippingTypeId: 7,
          isSelected: true,
          isEnabled: true,
          shippingId: 38703,
          deliveryDate: 'Sep 12th',
          deliveryWeekDay: 'Thursday',
          maxDays: 7,
          minDays: 5,
        },
        {
          shippingMethodName: 'Standard',
          shippingTypeDescription: '3-5 business days',
          shippingPrice: 9,
          shippingTypeId: 1,
          isSelected: false,
          isEnabled: true,
          shippingId: 75071,
          deliveryDate: 'Sep 10th',
          deliveryWeekDay: 'Tuesday',
          maxDays: 5,
          minDays: 3,
        },
        {
          shippingMethodName: 'Express',
          shippingTypeDescription: '2-3 business days',
          shippingPrice: 17,
          shippingTypeId: 3,
          isSelected: false,
          isEnabled: true,
          shippingId: 38698,
          deliveryDate: 'Sep 6th',
          deliveryWeekDay: 'Friday',
          maxDays: 3,
          minDays: 2,
        },
        {
          shippingMethodName: 'Priority',
          shippingTypeDescription: '1 business day',
          shippingPrice: 25,
          shippingTypeId: 5,
          isSelected: false,
          isEnabled: true,
          shippingId: 74458,
          deliveryDate: 'Sep 4th',
          deliveryWeekDay: 'Wednesday',
          maxDays: 1,
          minDays: 1,
        },
      ],
      orderItems: [
        {
          appliedDiscounts: '',
          color: 'Sea Salt',
          imagePath: 'webcontent/0054/818/584/cn54818584.jpg',
          inventoryStatus: 'RESERVED',
          price: {
            discountedPrice: 33.24,
            regularPrice: 34.99,
            salePrice: 34.99,
          },
          productName: 'Extra High-Waisted Fleece Pants for Women',
          productSkuId: '8650260120003',
          productStyleId: '865026',
          productTypeName: 'Extra High-Waisted Fleece Pants for Women',
          quantity: 1,
          size: 'L',
          totalPrice: 33.24,
        },
      ],
      shippingAddresses: [
        {
          firstName: 'Shruti Kabra',
          lastName: 'Gattani',
          addressLine1: '33 Union Sq Apt 1423',
          addressLine2: '',
          postalCode: '94587-3571',
          city: 'Union City',
          state: 'CA',
          country: 'US',
          phone: '4084315581',
          addressLocationType: '',
          verificationStatus: 'VERIFIED',
          deliveryPointValidation: 'CONFIRMED_DELIVERY_POINT',
          isSelected: true,
        },
      ],
      paymentMethods: [
        {
          paymentId: '74f49f8e-cd64-494e-be52-e41ad89e8ee6',
          paymentIndex: 1,
          creditCard: {
            cardType: '1',
            type: 'VISA',
            cardToken: 'foobar',
            cardTokenFormat: 'VaultId',
            expiryMonth: '12',
            expiryYear: '2033',
            lastFourDigits: '1111',
            billingAddress: {
              firstName: 'Shruti Kabra',
              lastName: 'Gattani',
              addressLine1: '33 Union Sq Apt 1423',
              city: 'Union City',
              state: 'CA',
              country: 'US',
              postalCode: '94587-3571',
              phone: '4084315581',
              verificationStatus: 'NOT_VERIFIED',
              deliveryPointValidation: 'INVALID_DELIVERY_POINT',
              default: false,
            },
            cvvValidated: false,
            cvvRequired: true,
            temporary: false,
            defaultCard: false,
            isPLCC: false,
          },
          errorList: [
            {
              errorCode: '710',
              moreInfo: '/swagger-ui.html',
              userMessage: 'For your security, please re-enter your credit card information to proceed.',
            },
          ],
          isSelected: true,
          isDefault: false,
          cardInfo: {
            cardId: 'foobar',
            cardNumber: 'XXXX XXXX XXXX 1111',
            expirationMonth: '12',
            expirationYear: '2033',
            lastFourDigits: '1111',
            isCvvValidated: false,
            saveCCInProfile: true,
            temporaryCardIndicator: false,
            isPLCC: false,
            cardType: 1,
            cvvRequired: true,
            defaultCard: false,
          },
          billingAddress: {
            firstName: 'Shruti Kabra',
            lastName: 'Gattani',
            addressLine1: '33 Union Sq Apt 1423',
            postalCode: '94587-3571',
            city: 'Union City',
            state: 'CA',
            country: 'US',
            phone: '4084315581',
            isSelected: false,
            isDefault: false,
          },
          isCardExpired: false,
        },
      ],
    },
    rewardsPanel: {
      conditionals: {
        isRewardsPanelDisplayed: false,
        isPartialResponse: true,
        isPromoRewardsApplied: true,
        isCvvError: false,
        isPromotionError: false,
        hasErrors: false,
      },
      brand: 'ON',
      market: 'US',
      isGuest: true,
      promoRewardsList: [
        {
          promoId: '262758',
          rewardCode: 'ID-262758',
          rewardType: 'promoReward',
          isApplied: true,
          displayName: 'get 5% off',
          autoApplied: true,
          promoDescription: 'get 5% off',
          discountTotal: 1.75,
        },
      ],
      rewardErrorCode: '',
    },
    shippingAddressPanel: {
      conditionals: {
        isHubBoxEnabled: true,
      },
      shippingAddressList: [
        {
          firstName: 'Shruti Kabra',
          lastName: 'Gattani',
          addressLine1: '33 Union Sq Apt 1423',
          addressLine2: '',
          postalCode: '94587-3571',
          city: 'Union City',
          state: 'CA',
          country: 'US',
          phone: '4084315581',
          addressLocationType: '',
          verificationStatus: 'VERIFIED',
          deliveryPointValidation: 'CONFIRMED_DELIVERY_POINT',
          isSelected: true,
        },
      ],
      lineItemCount: 1,
      bopisInBag: false,
      isOmniBag: false,
      deliveryGroupId: 'GapRegularGroup',
      hasSavedShippingAddresses: true,
      hasShippingAddressSelected: true,
      summaryView: {
        isUPSStore: false,
        fullName: 'Shruti Kabra Gattani',
        fullAddress: '33 Union Sq Apt 1423, Union City, CA, US 94587-3571',
      },
      formInitialValues: {
        fullName: 'Shruti Kabra Gattani',
        phone: '(*************',
      },
      notEligibleShippingItems: [],
      hasNoEligibleShippingItems: false,
      shouldDisplayGifting: true,
      showUPS: true,
    },
    signInPanel: {
      isGuest: true,
      isLoggedIn: false,
      isRecognized: false,
      marketCode: 'US',
      emailId: '<EMAIL>',
      lineItems: [
        {
          mergeType: 'Standard PDP',
          itemId: 'ec290347f7014f89b3201bf7bef77540',
          brand: 'ON',
          productSkuId: '8650260120003',
          productName: 'Extra High-Waisted Fleece Pants for Women',
          customerChoiceNumber: '865026012',
          productTypeName: 'z womens graphic tees',
          productStyleId: '865026',
          color: 'Sea Salt',
          size: 'L',
          colorStyleNumber: '865026012',
          variantDescription: 'Regular',
          merchandiseType: '7',
          inventoryStatusId: 5,
          isBackOrderItem: false,
          backOrdershippingDate: false,
          autoAdded: false,
          noReturnItem: false,
          returnByMailItem: false,
          noIntlShipping: true,
          marketingFlag: '0',
          giftWrappable: true,
          productStyleDescription: 'Extra High-Waisted Fleece Pants for Women',
          primaryCategoryName: 'T-shirts',
          productURL: 'browse/product.do?pid=8650260120003',
          price: {
            regularPrice: 34.99,
            salePrice: 34.99,
            discountedPrice: 33.24,
            percentageOff: 0,
          },
          regularPrice: '34.99',
          salePrice: '34.99',
          discountedPrice: '33.24',
          totalPrice: '33.24',
          quantity: '1',
          imagePath: 'webcontent/0054/818/584/cn54818584.jpg',
          inventoryStatus: 'RESERVED',
          vendorId: '000002910',
          webVendorName: 'SHINWON CORPORATION',
          showSellerName: false,
          excludedFromPromotion: false,
          madeToOrder: false,
          estimatedShippingDate: '',
          eligibleReturnLocationCode: '',
          isExcludedFromRewardFreeShipping: false,
          vendorStyleNumber: '',
          vendorUPCCode: '',
          appliedDiscounts: [],
          storeId: '',
        },
      ],
    },
    deliveryGroupPanel: {
      deliveryGroupLists: [
        {
          deliveryGroupId: 'GapRegularGroup',
          pickupOrderType: 'shipItemsOnly',
          lineItemList: [
            {
              mergeType: 'Standard PDP',
              itemId: 'ec290347f7014f89b3201bf7bef77540',
              brand: 'ON',
              productSkuId: '8650260120003',
              productName: 'Extra High-Waisted Fleece Pants for Women',
              customerChoiceNumber: '865026012',
              productTypeName: 'z womens graphic tees',
              productStyleId: '865026',
              color: 'Sea Salt',
              size: 'L',
              colorStyleNumber: '865026012',
              variantDescription: 'Regular',
              merchandiseType: '7',
              inventoryStatusId: 5,
              isBackOrderItem: false,
              backOrdershippingDate: false,
              autoAdded: false,
              noReturnItem: false,
              returnByMailItem: false,
              noIntlShipping: true,
              marketingFlag: '0',
              giftWrappable: true,
              productStyleDescription: 'Extra High-Waisted Fleece Pants for Women',
              primaryCategoryName: 'T-shirts',
              productURL: 'browse/product.do?pid=8650260120003',
              price: {
                regularPrice: 34.99,
                salePrice: 34.99,
                discountedPrice: 33.24,
                percentageOff: 0,
              },
              regularPrice: '34.99',
              salePrice: '34.99',
              discountedPrice: '33.24',
              totalPrice: '33.24',
              quantity: '1',
              imagePath: 'webcontent/0054/818/584/cn54818584.jpg',
              inventoryStatus: 'RESERVED',
              vendorId: '000002910',
              webVendorName: 'SHINWON CORPORATION',
              showSellerName: false,
              excludedFromPromotion: false,
              madeToOrder: false,
              estimatedShippingDate: '',
              eligibleReturnLocationCode: '',
              isExcludedFromRewardFreeShipping: false,
              vendorStyleNumber: '',
              vendorUPCCode: '',
              appliedDiscounts: [],
              storeId: '',
            },
          ],
          offerDetails: "NOT IN A HURRY?.\nSelect this option to give us a few extra days. We'll still do our best to ship your purchase quickly.",
          isBackOrder: false,
          isMadeToOrder: false,
          isDropShipItem: false,
          webVendorName: '',
          shippingMethodList: [
            {
              shippingMethodName: 'No Rush',
              shippingTypeDescription: '7-9 business days',
              shippingPrice: 5,
              shippingTypeId: 7,
              isSelected: false,
              isEnabled: true,
              shippingId: 38696,
              deliveryDate: '16th',
              deliveryWeekDay: 'Monday,',
              deliveryMonth: 'Sep',
              maxDays: 9,
              minDays: 7,
            },
            {
              shippingMethodName: 'Basic',
              shippingTypeDescription: '5-7 business days',
              shippingPrice: 7,
              shippingTypeId: 7,
              isSelected: true,
              isEnabled: true,
              shippingId: 38703,
              deliveryDate: '12th',
              deliveryWeekDay: 'Thursday,',
              deliveryMonth: 'Sep',
              maxDays: 7,
              minDays: 5,
            },
            {
              shippingMethodName: 'Standard',
              shippingTypeDescription: '3-5 business days',
              shippingPrice: 9,
              shippingTypeId: 1,
              isSelected: false,
              isEnabled: true,
              shippingId: 75071,
              deliveryDate: '10th',
              deliveryWeekDay: 'Tuesday,',
              deliveryMonth: 'Sep',
              maxDays: 5,
              minDays: 3,
            },
            {
              shippingMethodName: 'Express',
              shippingTypeDescription: '2-3 business days',
              shippingPrice: 17,
              shippingTypeId: 3,
              isSelected: false,
              isEnabled: true,
              shippingId: 38698,
              deliveryDate: '6th',
              deliveryWeekDay: 'Friday,',
              deliveryMonth: 'Sep',
              maxDays: 3,
              minDays: 2,
            },
            {
              shippingMethodName: 'Priority',
              shippingTypeDescription: '1 business day',
              shippingPrice: 25,
              shippingTypeId: 5,
              isSelected: false,
              isEnabled: true,
              shippingId: 74458,
              deliveryDate: '4th',
              deliveryWeekDay: 'Wednesday,',
              deliveryMonth: 'Sep',
              maxDays: 1,
              minDays: 1,
            },
          ],
        },
      ],
      summaryView: [
        {
          id: 'GapRegularGroup',
          name: 'Basic',
          itemCount: 1,
          deliveryBy: 'By Thursday, Sep 12th',
          deliveryWeekDay: 'Thursday,',
          deliveryDate: '12th',
          deliveryMonth: 'Sep',
          shippingPrice: 7,
        },
      ],
      currency: 'USD',
      payments: [
        {
          paymentId: '74f49f8e-cd64-494e-be52-e41ad89e8ee6',
          paymentIndex: 1,
          creditCard: {
            cardType: '1',
            type: 'VISA',
            cardToken: 'foobar',
            cardTokenFormat: 'VaultId',
            expiryMonth: '12',
            expiryYear: '2033',
            lastFourDigits: '1111',
            billingAddress: {
              firstName: 'Shruti Kabra',
              lastName: 'Gattani',
              addressLine1: '33 Union Sq Apt 1423',
              city: 'Union City',
              state: 'CA',
              country: 'US',
              postalCode: '94587-3571',
              phone: '4084315581',
              verificationStatus: 'NOT_VERIFIED',
              deliveryPointValidation: 'INVALID_DELIVERY_POINT',
              default: false,
            },
            cvvValidated: false,
            cvvRequired: true,
            temporary: false,
            defaultCard: false,
            isPLCC: false,
          },
          errorList: [
            {
              errorCode: '710',
              moreInfo: '/swagger-ui.html',
              userMessage: 'For your security, please re-enter your credit card information to proceed.',
            },
          ],
        },
      ],
      isEasyEnrollEligible: false,
      errors: [],
      hasDropshipItems: false,
      has4101Error: false,
      markdownSubtotal: 34.99,
    },
  },
  session: {
    email: '<EMAIL>',
    recognition_status: 'guest',
  },
  viewTag: {
    lv2: '',
    event_name: 'Checkout',
    page_type: 'Checkout',
    bopis_enabled: false,
    bopis_order_type: 'shipItemsOnly',
    brand_code: 'ON',
    brand_name: 'Old Navy',
    brand_number: '3',
    business_unit_abbr_name: 'ON_US_OL',
    business_unit_description: 'Old Navy',
    business_unit_id: 3,
    brand_short_name: 'on',
    channel: 'on:checkout',
    country_code: 'US',
    checkout_type: 'Guest Customer',
    language_code: 'en_US',
    shipping_options: 'Basic',
    pfs_order_shipping_method: '',
    checkout_version: 'Checkout_MVP',
    tier_status: '',
    cardholder_status: 'BRNONE|GPNONE|ONNONE|ATNONE',
    customer_uuid: 'e1c20d514b93432ba53605704ef4e7ce',
    recognition_status: 'guest',
    encrypted_customer_email: '5ffd3a8ab685185337f7e6921e6ba2bd0a99cff2152e60737646ef22075375f2c911140f7b138b6b6eaf8bdfd0cb4724',
    encrypted_customer_email_mm: 'c623377863abe83064a8f9124ef9c1277fb5ab1d',
    hashed_customer_email: 'f503b17299e6bb36f7854b4722506e07abb30a59ecb51fc2805365b4044ba843',
    mtl_member_status: 'false',
    category_preference: '',
    division_preference: '',
    product_id: ['865026'],
    product_cc_id: ['865026012'],
    product_brand: ['ON'],
    brand_mix: 'ON',
    product_category: ['T-shirts'],
    product_name: ['Extra High-Waisted Fleece Pants for Women'],
    product_quantity: [1],
    product_sku: ['8650260120003'],
    product_markdown_amount: ['0.00'],
    product_gross_retail: ['34.99'],
    product_gross_merchandise: ['34.99'],
    product_net_demand: ['33.24'],
    product_page_type: ['Standard PDP'],
    order_gross_merchandise: 34.99,
    product_dropship: ['false'],
    product_seller_id: ['3'],
    product_seller_name: ['ON'],
  },
  draftOrderId: '04928b95-5eb7-486c-b571-6a37e2431c82',
  features: {
    experiments: {
      EXP_BARCLAY_US: true,
      EXP_BARCLAY_OCP_US: false,
      EXP_BARCLAY_SAVINGS: true,
      EXP_CANARY: false,
      EXP_DONATION_FEATURE: true,
      EXP_ROKT: true,
      EXP_GUEST_FREE_SHIPPING_BANNER: true,
      EXP_ENABLE_BOPIS_GIFT_CARD_CANADA: false,
      EXP_ENABLE_BOPIS_SMS_CANADA: false,
      EXP_PAYMENT_AUTOSELECT: true,
      EXP_ACCELERATED_US: true,
      EXP_ACCELERATED_CA: false,
      EXP_ACCELERATED_US_SP: true,
      EXP_LOYALTY_CA: false,
      EXP_LOYALTY_US: true,
      EXP_PAYMENTMODAL_US: true,
      EXP_PAYMENTMODAL_CA: false,
      EXP_ORDER_SUMMARY_US: false,
      EXP_ORDER_SUMMARY_CA: false,
      EXP_VAULT_SERVICE: true,
      EXP_MARKDOWN: true,
      EXP_OPTIMIZELY_MIGRATION_TEST: true,
      EXP_ACCELERATED_PPE: false,
    },
    experimentVariables: {
      AFTERPAY_MIN_ORDER_AMT: 35,
      AFTERPAY_MAX_ORDER_AMT: 1000,
      GUEST_FREE_SHIPPING_THRESHOLD: 50,
    },
    killSwitches: {
      ENABLE_AFTERPAY: true,
      ENABLE_AFTER_PAY_BACK_ORDER: false,
      ENABLE_BOPIS_FLAG: true,
      ENABLE_BOPIS_GIFTCARD: true,
      ENABLE_BOPIS_OCP: false,
      ENABLE_BOPIS_PICKUP_PANEL: true,
      ENABLE_BOPIS_SHOPPINGBAG_PANEL: false,
      ENABLE_BOPIS_SMS_FLAG: true,
      ENABLE_GOOGLE_AUTOCOMPLETE: true,
      ENABLE_PAYPAL_BUTTON: true,
      ENABLE_PAYPAL_BUTTON_CANADA: true,
      ENABLE_HUBBOX: true,
      ENABLE_SHIPPING_AUTOSELECT: true,
    },
    localizedText: {
      ENABLE_LOCALIZED_TEXT: true,
      bopisTitle: 'Hello',
      shippingMethodFooterText: 'shipping pillview method ENUS',
      myRewardsPanelFooterText: 'Redeemed rewards can’t exceed the subtotal of your order (before tax and shipping).',
    },
    segmentList: ['roktX', 'gfsbX', 'PasX', 'accusX', 'accusspX', 'loyaltyus', 'vscuX', 'COMTX', 'accppeC'],
  },
};
