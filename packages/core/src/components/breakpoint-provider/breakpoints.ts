import { BreakpointType } from './types';

const landscape = ' and (min-aspect-ratio: 1/1)';
const portrait = ' and (max-aspect-ratio: 1/1)';

export const SMALL = 'small';
export const MEDIUM = 'medium';
export const LARGE = 'large';
export const XLARGE = 'x-large';

const SM_MAX = 567;
const MD_MIN = 568;
const MD_MAX = 767;
const LG_MIN = 768;
const LG_MAX = 1023;
const XL_MIN = 1024;

export const breakpointsRangeMap = new Map<number[], BreakpointType['size']>();
breakpointsRangeMap.set([-1, SM_MAX], SMALL);
breakpointsRangeMap.set([MD_MIN, MD_MAX], MEDIUM);
breakpointsRangeMap.set([LG_MIN, LG_MAX], LARGE);
breakpointsRangeMap.set([XL_MIN, Infinity], XLARGE);

export const breakpointRanges = Array.from(breakpointsRangeMap.keys());
export type BreakpointRange = number[];

export const defaultBreakpoint: BreakpointType = {
  size: XLARGE,
  // NOTE: Even when the portrait media query is matching,
  // once we hit x-large, the landscape layout is applied.
  // For this reason, we're broadcasting 'landscape'
  orientation: 'landscape',
  media: `(min-width: ${XL_MIN}px)${portrait}`,
};

export const breakpointsMap = {
  [SMALL]: {
    size: SMALL,
    orientation: 'portrait',
    media: `(max-width: ${SM_MAX}px)`,
  },
  [MEDIUM]: {
    size: MEDIUM,
    orientation: 'portrait',
    media: `(max-width: ${MD_MAX}px) and (min-width: ${MD_MIN}px)${portrait}`,
  },
  [LARGE]: {
    size: LARGE,
    orientation: 'portrait',
    media: `(max-width: ${LG_MAX}px) and (min-width: ${LG_MIN}px)${portrait}`,
  },
  [XLARGE]: defaultBreakpoint,
} as const;

export const breakpoints: Array<BreakpointType> = [
  breakpointsMap[SMALL],
  // Removed "landscape" orientation for max-width: 567px
  // and max-width: 300px on Feb 2021.
  // It was causing Android soft-keyboard issues,
  // and is not actively used.
  {
    size: MEDIUM,
    orientation: 'landscape',
    media: `(max-width: ${MD_MAX}px) and (min-width: ${MD_MIN}px)${landscape}`,
  },
  {
    size: MEDIUM,
    orientation: 'portrait',
    media: `(max-width: ${MD_MAX}px) and (min-width: ${MD_MIN}px)${portrait}`,
  },
  {
    size: LARGE,
    orientation: 'landscape',
    media: `(max-width: ${LG_MAX}px) and (min-width: ${LG_MIN}px)${landscape}`,
  },
  {
    size: LARGE,
    orientation: 'portrait',
    media: `(max-width: ${LG_MAX}px) and (min-width: ${LG_MIN}px)${portrait}`,
  },
  {
    size: XLARGE,
    orientation: 'landscape',
    media: `(min-width: ${XL_MIN}px)${landscape}`,
  },
  defaultBreakpoint,
  {
    size: XLARGE,
    orientation: 'landscape',
    media: `(min-width: ${XL_MIN}px)`,
  },
];
