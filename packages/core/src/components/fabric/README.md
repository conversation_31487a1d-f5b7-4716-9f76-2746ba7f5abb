# <span id='top' style='font-size: 2rem; width: 100%;'>MFE to HUI - Migration Instructions</span>

- [<span style='font-weight: bold; font-size: 1.2rem;'>Migration Approach</span>](#migration-approach)
  - [<span style='font-size: 1rem;'>Phase 1: Porting</span>](#phase-1-porting)
  - [<span style='font-size: 1rem;'>Phase 2: Migration</span>](#phase-2-migration)
    - [Replacing React-Stitch with Tailwind CSS](#replacing-react-stitch-with-tailwind-css)
  - [<span style='font-size: 1rem;'>Phase 3: Redesign</span>](#phase-3-redesign)
    - [Fabric Design adaptation](#fabric-design-adaptation)
      - [Figma Extraction](#figma-extraction)
      - [Building Core UI components in HUI](#building-core-ui-components-in-hui)
- [<span style='font-weight: bold; font-size: 1.2rem;'>Storybook & Coding Guidelines</span>](#storybook-coding-guidelines)
  - [<span style='font-size: 1rem;'>Storybook Controls</span>](#storybook-controls)
  - [<span style='font-size: 1rem;'>Coding Guidelines</span>](#coding-guidelines)
- [<span style='font-weight: bold; font-size: 1.2rem;'>FAQs</span>](#faqs)
- [<span style='font-weight: bold; font-size: 1.2rem;'>DEMO</span>](#demo)

<span id='migration-approach' style='background: lightslategray; padding: 0.75rem; display: inline-block; width: 100%; color: white; font-size: 1.5rem; text-transform: uppercase;'>Migration Approach <a href='#top'><span style='font-size: 0.75rem; color: yellow; margin-left: 0.5rem;'>Go to top</span></a></span>

The MFE (Micro Front End) to HUI (Headless UI) migration approach is a multiphase process as described below. The end state of this effort is to have all the components and pages redesigned using the new Fabric design.

**_The scope of this document focuses on Phases 2 and 3 of the migration approaches below assuming Phase 1 has been completed already._**

<span id='phase-1-porting' style='background: rgba(46,52,56,0.5); padding: 0.75rem; display: inline-block; width: 100%; color: white; font-size: 1.25rem; text-transform: uppercase;'>PHASE 1: PORTING <a href='#top'><span style='font-size: 0.75rem; color: yellow; margin-left: 0.5rem;'>Go to top</span></a></span>

<span style='padding-left: 0.75rem; font-size: 1rem;'>This phase focuses only on moving components from MFE to HUI. The components will still use the react-stitch + Emotion CSS implementation in the HUI repo.</span>

- Port the components and pages from MFE to the HUI repo and make them work with react-stitch.
- The ported Core UI components are placed under the `packages/core/src/components/legacy` directory.
- The ported pages are placed under the corresponding product package directory.
- <span style='color: darkorange;'>**NOTE:** The ported components and pages are not intended to be modified manually. Any manual change made in these files will be overwritten when the port script is run the next time.</span>
  <br/><br/>

<span id='phase-2-migration' style='background: rgba(46,52,56,0.5); padding: 0.75rem; display: inline-block; width: 100%; color: white; font-size: 1.25rem; text-transform: uppercase;'>PHASE 2: MIGRATION <a href='#top'><span style='font-size: 0.75rem; color: yellow; margin-left: 0.5rem;'>Go to top</span></a></span>

<span style='padding-left: 0.75rem; font-size: 1rem;'>This phase focuses on replacing the react-stitch + Emotion CSS implementation of the ported components with tailwind CSS.</span>

- Replace react-stitch and emotion css with tailwind css that is configured in HUI. Refer to the [Replacing React-Stitch with Tailwind CSS](#replacing-react-stitch-with-tailwind-css) section for details.
- This phase will use the tailwind theme files for each brand that is configured with the tailwind variables mapped to react-stitch variables. Reference: <a href='https://gapinc.atlassian.net/wiki/spaces/DSNEXT/pages/1531445473/React-Stitch+Tailwind+in+Core+UI' target='_blank'>**React-stitch variable mapping**</a>
- New tailwind theme variables can be created as needed for the components and pages.
- The legacy color variables can be viewed here - <a href='https://ecom-next-storybook.aks.test.azeus.gaptech.com/?path=/story/fabric-ds-foundation-colors-legacy--legacy-colors' target='_blank'>**Legacy Colors**</a>
  - <span style='color: darkorange;'>**NOTE:** These legacy color variables are the ported variables from react-stitch during the initial stages of the HUI project. Additional variables can be added as needed using the custom variable approach explained in the [Replacing React-Stitch with Tailwind CSS](#replacing-react-stitch-with-tailwind-css) section below.</span>
- The legacy font and typography details can be viewed here - <a href='https://ecom-next-storybook.aks.test.azeus.gaptech.com/?path=/story/fabric-ds-foundation-typography-legacy--fonts' target='_blank'>**Legacy Fonts**</a> & <a href='https://ecom-next-storybook.aks.test.azeus.gaptech.com/?path=/story/fabric-ds-foundation-typography-legacy--typography' target='_blank'>**Legacy Typography**</a>
- This phase will include the components that fall under the conditions below.
  - **Component exists only on the Buy Path in HUI under `migration` folder and needs to be rewritten using tailwind**
  - **Product component have been ported to EcomNext repo and needs to be rewritten using tailwind**
    <br/><br/>

### **Replacing React-Stitch with Tailwind CSS**

The Phase 2 of the migration approach focuses on replacing the react-stitch and emotion css with tailwind css that is configured in HUI. During the initial phases of the HUI project, some of the react-stitch variables were mapped to tailwind variables. This mapping was done for colors and font family only. The mapped variables are available in each brand's tailwind theme configuration file as `legacyColors` and `fontFamily` (files are listed below). The legacy base and crossbrand colors are included as part of each brand theme through the tailwind configuration.

- <a href='https://github.gapinc.com/ecomfrontend/ecom-next/blob/main/packages/core/src/themes/atTheme.ts' target='_blank'>**AT Theme File**</a>
- <a href='https://github.gapinc.com/ecomfrontend/ecom-next/blob/main/packages/core/src/themes/brTheme.ts' target='_blank'>**BR Theme File**</a>
- <a href='https://github.gapinc.com/ecomfrontend/ecom-next/blob/main/packages/core/src/themes/gapTheme.ts' target='_blank'>**GAP Theme File**</a>
- <a href='https://github.gapinc.com/ecomfrontend/ecom-next/blob/main/packages/core/src/themes/onTheme.ts' target='_blank'>**ON Theme File**</a>
- <a href='https://github.gapinc.com/ecomfrontend/ecom-next/blob/main/packages/core/src/themes/base/tailwind.preset.base-color.ts' target='_blank'>**Base & CrossBrand Color File**</a>

For the migration effort, the two scenarios below must be considered when replacing react-stitch with the new tailwind theme as not all react-stitch variables are available in the tailwind theme.

**SCENARIO 1: When the react-stitch variable is already mapped to a tailwind variable**

- Use the mapped tailwind variable to style the ported components and pages.
- Mapped react-stitch color variable in tailwind is available in the brand specific configuration file as `legacyColors`. Ex. `atTheme.ts`.

      To apply background color to a container, use background-color: theme('backgroundColor.b1') for a CSS class or `bg-b1` as the inline tailwind class which will apply the color `#333333` for Athleta.

- If the corresponding react-stitch variable has not been mapped to a tailwind variable, use the figma tailwind variable from the figma extract files for brands under `packages/core/src/themes/tools/figma/brand/semantic/merged`. These files can be used for all CSS properties like `colors`, `spacing`, `sizing`, etc.
- If the variables are not mapped or are not available in the figma extract files, please follow the instructions in **Scenario 2** below.

**SCENARIO 2: When the react-stitch variable has not been already mapped to a tailwind variable**

- In this scenario, the tailwind variable creation follows a different structure than Scenario 1 above.
- Since figma variables will be used for the new design (PHASE 3), the recommendation is to create the new variables for the migration using the variable structure followed for the new fabric design. This will ensure consistency and a brand-agnostic styling implementation.
- **Please note:** Only variables for the `colors` property must be created using this approach. For other properties like `spacing`, `sizing`, etc., values can be hard-coded (if they are not available in the figma extract files already mentioned in the **Scenario 1** above).
- For the instructions to create the legacy primitive and semantic variables for `colors`, refer to this documentation <a href='https://gapinc.atlassian.net/wiki/spaces/ECOMNEXT/pages/1590043530/Migrating+Ported+Components+-+Tailwind' target='_blank'>**Migrating Ported Components - Tailwind**</a>.

**POINTS TO CONSIDER**

- The HUI project focuses on eliminating brand-specific code logic inside the components for styling and make the styling implementation brand-agnostic.
- The new design approach emphasizes the use of the figma variable structure (primitive & semantic variables) to make the styling brand agnostic. It is recommended to use this variable structure to be consistent and minimize work later during redesign. This applies only to the creation of the `colors` property at the moment.
- While using tailwind CSS, it is recommended to use the `theme` object instead of the `@apply` directive to apply the tailwind classes. This is to avoid any potential complexities introduced by the `@apply` directive.

  - RECOMMENDED: tailwind `theme` object

  ```css
  @import 'tailwindcss/base';
  @import 'tailwindcss/components';
  @import 'tailwindcss/utilities';

  @layer components {
    .container {
      background-color: theme('colors.color-background');
      padding: theme('spacing.spacing-stack-medium');
      margin: theme('spacing.spacing-stack-medium');
      color: theme('colors.color-font');
    }
  }
  ```

  - NOT RECOMMENDED: tailwind `@apply` directive

  ```css
  @import 'tailwindcss/base';
  @import 'tailwindcss/components';
  @import 'tailwindcss/utilities';

  @layer components {
    .container {
      @apply bg-color-background p-spacing-stack-medium m-spacing-stack-medium text-color-font;
    }
  }
  ```

- There could be scenarios when brand-specific styling is unavoidable when replacing react-stitch with tailwind. In such cases, the brand-specific styling can be added to the component `styles.css` as show below. A class name with the brand value has been added to the root element which can be used to implement brand-specific styles.

  ```css
  @import 'tailwindcss/base';
  @import 'tailwindcss/components';
  @import 'tailwindcss/utilities';

  @layer components {
    .tag-link {
      display: inline-block;
      font-family: theme('fontFamily.brand');
      background: theme('colors.component-tags-tag-link-background-color');
      padding: theme('padding.padding-stack-x-small') theme('padding.padding-stack-medium');
      font-size: theme('fontSize.font-size-b-m-font-size');
      font-weight: theme('fontWeight.font-weight-bold-font-weight');
      line-height: theme('lineHeight.font-size-b-s-line-height');
      border-radius: theme('borderRadius.2xl');
      white-space: nowrap;
      margin-bottom: 0.75rem;

      &:hover {
        background: theme('colors.component-tags-tag-link-hover-background-color');
      }
    }

    .at,
    .on {
      .tag-link {
        border-radius: 0;
      }
    }

    .br,
    .brfs {
      .tag-link {
        border-radius: 0.25rem;
      }
    }

    .gap,
    .gapfs {
      .tag-link {
        border-radius: 0;
        color: theme('colors.component-tags-tag-link-color');
      }
    }
  }
  ```

  <br/>

- For the migration effort, usage of CSS modules is <span style='text-decoration: underline;'>recommended</span> but tailwind inline CSS can be used as needed. The example below shows how to convert a long inline CSS to a CSS module.

  ```jsx
  // Inline CSS
  <div className='w-full h-fit text-color-font-default p-spacing-inline-medium text-font-size-b-xl-font-size bg-color-surface-default leading-font-size-b-s-line-height font-font-weight-bold-font-weight tracking-font-tracking-b-regular-letter-spacing'>Content</div>

  // CSS Module
  import styles from './styles.module.css';
  <div className={styles.container}>Content</div>

  // styles.module.css
  .container {
    width: 100%;
    height: fit-content;
    color: theme('colors.color-font-default');
    padding: theme('spacing.spacing-inline-medium');
    font-size: theme('fontSize.font-size-b-xl-font-size');
    background-color: theme('colors.color-surface-default');
    line-height: theme('lineHeight.font-size-b-s-line-height');
    font-weight: theme('fontWeight.font-weight-bold-font-weight');
    letter-spacing: theme('tracking.font-tracking-b-regular-letter-spacing');
  }
  ```

  <br/>

- Following is an example of converting a react-stitch component using `forBrands` function to a component using tailwind.
  - The `legacy/change-store-modal/components/store-button/style.tsx` component is used as an example to show the conversion from react-stitch to tailwind.

```jsx
    "use client";
    import {styled, forBrands, Theme} from "@ecom-next/core/react-stitch";

    export const BopisStoreCardActionWrapper = styled.div({
        float: "right",
        width: "155px",
        fontSize: "1rem",
    });

    export const getHoverColor = (theme: Theme, selected: boolean) =>
    forBrands(theme, {
        default: () =>
            selected
            ? {backgroundColor: theme.color.gray60, color: theme.color.wh}
            : undefined,
        on: () =>
            selected
            ? {backgroundColor: "#40698b", color: theme.color.wh}
            : undefined,
        at: () =>
            selected
            ? {
            backgroundColor: theme.color.wh,
            color: theme.color.gray60,
            borderColor: theme.color.gray60,
            }
            : undefined,
    });

    export const getButtonColor = (theme: Theme, selected: boolean) =>
    forBrands(theme, {
        default: () => (selected ? theme.color.wh : theme.color.wh),
        at: () => (selected ? theme.color.b1 : theme.color.wh),
    });

    export const getBtnBackgroundColor = (theme: Theme, selected: boolean) =>
    forBrands(theme, {
        default: () => (selected ? theme.color.b1 : "inherited"),
        at: () => (selected ? theme.color.wh : undefined),
    });

    export const getBorderStyle = (theme: Theme, selected: boolean) =>
    forBrands(theme, {
        default: () => (selected ? "inherited" : `2px solid ${theme.color.b1}`),
        on: () => (selected ? "none" : `2px solid ${theme.color.b1}`),
        at: () => `${selected ? "1px" : "2px"} solid ${theme.color.gray80}`,
    });
```

- The styles of the `store-button` can be converted to a CSS class equivalent using CSS modules as below.

```jsx
    // styles.module.css
    .fds_bopis-store-card-action-wrapper {
      float: right;
      width: theme('spacing.component-bopis-store-card-width');
      font-size: theme('fontSize.font-size-b-xl-font-size');

       .fds_bopis-store-button {
          color: theme('colors.component-bopis-store-button-font-color');
          background-color: theme('colors.component-bopis-store-button-background-color');
          border: theme('spacing.component-bopis-store-button-border-width') solid theme('colors.component-bopis-store-button-border-color');

          &:hover {
            color: theme('colors.component-bopis-store-button-hover-font-color');
            background-color: theme('colors.component-bopis-store-button-hover-background-color');
          }
       }

       .fds_bopis-store-button--selected {
          color: theme('colors.component-bopis-store-button-selected-font-color');
          background-color: theme('colors.component-bopis-store-button-selected-background-color');
          border: theme('spacing.component-bopis-store-button-selected-border-width') solid theme('colors.component-bopis-store-button-selected-border-color');

          &:hover {
            color: theme('colors.component-bopis-store-button-selected-hover-font-color');
            background-color: theme('colors.component-bopis-store-button-selected-hover-background-color');
          }
       }
    }
```

<span id='phase-3-redesign' style='background: rgba(46,52,56,0.5); padding: 0.75rem; display: inline-block; width: 100%; color: white; font-size: 1.25rem; text-transform: uppercase;'>PHASE 3: REDESIGN <a href='#top'><span style='font-size: 0.75rem; color: yellow; margin-left: 0.5rem;'>Go to top</span></a></span>

<span style='padding-left: 0.75rem; font-size: 1rem;'>This phase focuses on redesigning the components and pages using the Fabric design theme variables.</span>

- In this phase, the new Fabric design figma variables will be used to style the components and pages.
- The figma API integration will be used to extract the figma variables from the figma file created by the designers.
- The extracted variables are configured in the tailwind theme for each brand including CrossBrand and are accessible to be used with the tailwind theme object in a CSS file or the tailwind utility classes used inline in the JSX.
- This is the final phase which starts after Phase 2 is implemented for common components used by all teams (Accordion) or is independent of Phase 2 for components exclusive to a product team (Buybox). The scenarios below fall under this phase.
  - **A Core UI component does not exist in the HUI component library - `migration` and `fabric` folders**
  - **Product team needs to create and style a new component that does not exist in HUI**
    <br/>

### **Fabric Design Adaptation**

This section describes the approach to create the Core UI component during the **REDESIGN** phase. This is the last phase of the process which must use figma variables for both theming and component design. The figma variables are extracted and configured in the tailwind theme for each brand. The styling of the Core UI components is implemented using these figma variables through the tailwind theme. The following sections describe how these figma variables are extracted from the figma file and configured in the HUI tailwind theme which is used to build the components.

#### **Figma Extraction**

The ecom-next app is integrated with Figma to extract the css variables used by Tailwind configuration. Following are the steps followed to extract figma variables and set up the tailwind theme.

- A node script is run by the Fabric Design developers to extract the primitive and semantic variables from Figma.
- The primitive and semantic files will be generated under the folder `/packages/core/src/themes/tools/figma/brand` for each brand including CrossBrand.
- These files are imported in the tailwind configuration files. Ex. `atTheme.ts` for Athleta.
- <span style='color: darkorange;'>**NOTE:** To extract the figma variables for the Product team designs, contact the Fabric DS development team through the <a href='https://gapinc.enterprise.slack.com/archives/C07NHPRFYFJ' target='_blank'>**fds-ecom-next-styling-support**</a> Slack channel for support.</span>

#### **Building Core UI components in HUI**

<span style='color: darkorange;'>Before you start building a component using Fabric design...</span>

- Verify if the component has been built already and meets your requirements using the storybook - <a href='https://ecom-next-storybook.aks.test.azeus.gaptech.com/?path=/docs/fabric-ds-accordion-docs--docs' target='_blank'>**HUI Core Components Storybook**</a>
- If the expected component exists already, use the component in your module by importing it from the `packages/core/src/components/fabric` folder. Adjust the props as required to make the component functional.
- Some of the new Core UI components will have different props and styling based on the new Fabric design. Check the documentation for the component under the storybook to verify the implementation details.
- In case of any issues or missing functionality, contact the Fabric DS development team through the <a href='https://gapinc.enterprise.slack.com/archives/C07NHPRFYFJ' target='_blank'>**fds-ecom-next-styling-support**</a> or <a href='https://gapinc.enterprise.slack.com/archives/C06SFMFQ3MY' target='_blank'>**ecom-next-support**</a> Slack channel for support.
- If the expected component does not exist under the `packages/core/src/components/fabric` folder, proceed to the next section.

Following are the instructions to build a new component using the Fabric design in HUI.

1. **Component Creation**

   - Create a new component file under the `packages/core/src/components/fabric` directory.
   - The component file must be named using the PascalCase naming convention. Eg. `Accordion.tsx`. Alternatively, you can run this node script to auto-create the files for the component - `npm run plop` and enter the component name.
   - The component must be built using the figma variables extracted and configured in the tailwind theme for each brand. These variables are available in the brand specific files listed below. These variables are also configured in tailwind and are available through the tailwind theme object used in CSS classes or through the tailwind utility classes used inline in JSX.
     - <a href='https://github.gapinc.com/ecomfrontend/ecom-next/blob/main/packages/core/src/themes/tools/figma/brand/semantic/merged/at.figma.merged.json' target='_blank'>**AT Figma Variables File**</a>
     - <a href='https://github.gapinc.com/ecomfrontend/ecom-next/blob/main/packages/core/src/themes/tools/figma/brand/semantic/merged/br.figma.merged.json' target='_blank'>**BR Figma Variables File**</a>
     - <a href='https://github.gapinc.com/ecomfrontend/ecom-next/blob/main/packages/core/src/themes/tools/figma/brand/semantic/merged/gap.figma.merged.json' target='_blank'>**GAP Figma Variables File**</a>
     - <a href='https://github.gapinc.com/ecomfrontend/ecom-next/blob/main/packages/core/src/themes/tools/figma/brand/semantic/merged/on.figma.merged.json' target='_blank'>**ON Figma Variables File**</a>
     - <a href='https://github.gapinc.com/ecomfrontend/ecom-next/blob/main/packages/core/src/themes/tools/figma/brand/semantic/merged/cb.figma.merged.json' target='_blank'>**CrossBrand Figma Variables File**</a>
   - <span style='color: darkorange;'>**NOTE:** For styling the components, only the Semantic variables must be used as this will ensure that the style changes in Figma will reflect correctly in HUI. If Primitive variables are used directly, it will not match the Figma design when the Primitive variable references of the Semantic variables are modified in Figma.</span>

   - The component must be built using the BEM (Block Element Modifier) naming convention for CSS class names. Refer to the [Using BEM for CSS class names](#using-bem-for-css-class-names) section for details.
   - The component must be built using CSS classes for styling. Refer to the [Using CSS modules](#using-css-modules) section for details.
   - The component must include unit tests to assert the proper functioning of different scenarios based on the props.
   - The component must include storybook stories to demonstrate the different scenarios based on the props. The storybook must follow the [Storybook Controls](#storybook-controls) structure for props organization.
   - The component must be compliant to WCAG 2.1 AA standards and must pass the accessibility tests.
     <br/><br/>

2. **Storybook Creation**
   - Create a new story file under the `packages/core/src/stories/Fabric DS` directory. The storybook name must be prefixed with the path `Fabric DS/`
   - The story file must be named using the PascalCase naming convention. Eg. `Accordion.stories.tsx`.
   - The story file must include the stories for the component based on the different scenarios and props.
   - Whenever applicable, the component storybook must include a `Showcase` story like <a href='https://ecom-next-hui-storybook-fds.aks.test.azeus.gaptech.com/?path=/story/fabric-ds-checkbox--showcase' target='_blank'>Checkbox - Showcase</a> to demonstrate all component variants in a single snapshot. This will be used in the visual regression testing of the component.
   - The story file must include the controls for the component props following the [Storybook Controls](#storybook-controls) structure.
   - The story file must include the documentation for the component using the `docs.mdx` file. The documentation must detail the props and styling used in the component with any additional information specific to the component.
   - Ensure that all accessibility tests pass for the component in the storybook.

## <span id='storybook-coding-guidelines' style='background: lightslategray; padding: 0.75rem; display: inline-block; width: 100%; color: white; font-size: 1.5rem; text-transform: uppercase;'>Storybook & Coding Guidelines <a href='#top'><span style='font-size: 0.75rem; color: yellow; margin-left: 0.5rem;'>Go to top</span></a></span>

### **Storybook Controls**

Every storybook must follow the structure below to keep the controls organized. Refer to the Fabric DS `Meter` component [`stories/Fabric DS/Meter/Meter.stories.tsx`] for examples.

- All the main component props must be uncategorized and placed at the top. Ex. percentage, isAnimated, animationDelay props of the Meter component.
- The brand props like `brand` and `isCrossBrand` must be listed under a `BRAND` section.
- All custom props used for Storybook only must be listed under a `CUSTOM` section.

### **Coding Guidelines**

#### Using CSS modules

CSS modules is recommended for the HUI project as it is a good approach that allows for scoped styling and prevents global CSS conflicts. The CSS module files are created with the `.module.css` extension for each component/page and are placed in the corresponding component or page directory. Please refer to the CSS Module in NextJS documentation for more details - <a href='https://nextjs.org/docs/app/building-your-application/styling/css-modules#css-modules' target='_blank'>**CSS Modules in Next JS**</a>.

#### Using BEM for CSS class names

For the Core UI components created using Fabric Design, class names are used. The class names follow the BEM (Block Element Modifier) naming convention as described below.

1. The class names are written in lowercase.
2. The class name for the block is the component name with a `fds` namespace prefix for Fabric DS component. Eg. `fds_modal`.
3. The class name for the element is the block name followed by two underscores and the element name. Eg. `fds_modal__header`.
4. The class name for the modifier is the block name followed by two hyphens and the modifier name. A modifier is a state (open/close) or characteristic of the element (info/warning/error). Eg. `fds_modal--warning`.
5. For deeply nested components (like a close button inside a modal container), the class name is the block name followed by two underscores and the component name to create a unique selector. Eg. `fds_modal__close-button` (not `fds_modal__container__content__button-container__close-button`).

BEM reference documentation: <a href='https://getbem.com/naming/' target='_blank'>BEM Naming</a>
<br/><br/>

## <span id='faqs' style='background: lightslategray; padding: 0.75rem; display: inline-block; width: 100%; color: white; font-size: 1.5rem; text-transform: uppercase;'>FAQs <a href='#top'><span style='font-size: 0.75rem; color: yellow; margin-left: 0.5rem;'>Go to top</span></a></span>

<span style='font-weight: bold; font-size: 1rem;'>`1.` How to request access to Figma?</span>

- Follow the instructions in this document to request Figma access: <a href='https://gapinc.atlassian.net/wiki/spaces/CIAIIQ/pages/1480983304/CIA+-+Figma' target='_blank'>**CIA - Figma Access Request**</a>
- Reach out to the Design System team in case of questions or issues. The team can be contacted through the following channels: -
  - <a href='https://teams.microsoft.com/l/channel/19%3ASY7r8T-0ftgVGlK_2zlOT3hsKeeXNQG6V9nGKoqbeJU1%40thread.tacv2/General?groupId=b2c7424c-3ad8-4e6d-bd46-ea1d1c52dc9d&tenantId=348a1296-55b6-466e-a7af-4ad1a1b79713' target='_blank'>**Design System Support Channel on Teams**</a>
  - Ecom Next Styling Support on Slack: <a href='https://gapinc.enterprise.slack.com/archives/C07NHPRFYFJ' target='_blank'>**fds-ecom-next-styling-support**</a> or <a href='https://gapinc.enterprise.slack.com/archives/C06SFMFQ3MY' target='_blank'>**ecom-next-support**</a>
    <br /><br />

<span style='font-weight: bold; font-size: 1rem;'>`2.` How to view and translate Figma theme variables?</span>

- The Figma library defines the theme variables for each brand including CrossBrand. It also includes the component designs for various product pages. It can be accessed using this link: <a href='https://www.figma.com/design/WbaZehUQDrUNGZOONbIIbq/Fabric-Design-System-Library-0.10.0?node-id=1-3714&node-type=canvas&t=3aXRtI1zA9Tx2Wo8-0' target='_blank'>**Figma Library**</a>
  <br /><br />

<span style='font-weight: bold; font-size: 1rem;'>`3.` How to request to pull latest Figma variables?</span>

- The Figma variables are extracted from the Figma library using a script maintained by the Fabric Design developers. Contact them through the <a href='https://gapinc.enterprise.slack.com/archives/C07NHPRFYFJ' target='_blank'>**fds-ecom-next-styling-support**</a> Slack channel. The FDS developers can also be contacted on the <a href='https://gapinc.enterprise.slack.com/archives/C06SFMFQ3MY' target='_blank'>**ecom-next-support**</a> Slack channel as an alternate option.
  <br /><br />

<span style='font-weight: bold; font-size: 1rem;'>`4.` How to use legacy react stitch variables (ie tag link group) correctly?</span>

- Refer to the [Replacing React-Stitch with Tailwind CSS](#replacing-react-stitch-with-tailwind-css) section for the instructions to use legacy react stitch variables.
  <br /><br />

<span style='font-weight: bold; font-size: 1rem;'>`5.` How to use font variables?</span>

- The font variables are available in the tailwind theme configuration file for each brand.
- For both the **MIGRATION** (tailwind replacement) and **REDESIGN** (new design) phases, `font-family` can be accessed as below.
  - **Inline CSS**: `font-brand` for main brands, `font-crossbrand` for CrossBrand
  - **CSS Class**: `font-family: theme('fontFamily.brand')` for main brands, `font-family: theme('fontFamily.crossbrand')` for CrossBrand
- For the **MIGRATION** phase, other font properties like `font-size`, `line-height`, `font-weight` must be used from the existing tailwind configuration if available. If not, they can be hard-coded as needed. Refer to the [Replacing React-Stitch with Tailwind CSS](#replacing-react-stitch-with-tailwind-css) section for the instructions to use legacy react stitch variables.
- For the **REDESIGN** phase, other font properties like `font-size`, `line-height`, `font-weight` can be accessed as below.
  - **Inline CSS**: `text-font-size-b-xl-font-size` for font size, `font-font-weight-bold-font-weight` for font weight, and `leading-font-size-b-l-line-height` for line height. Variables are prefixed with `cb-` for referencing CrossBrand variables like `text-cb-font-size-b-xl-font-size`.
  - **CSS Class**: `font-size: theme('fontSize.font-size-b-xl-font-size')` for font size, `font-weight: theme('fontWeight.font-weight-bold-font-weight')` for font weight, and `line-height: theme('lineHeight.font-size-b-l-line-height')` for line height. Variables are prefixed with `cb-` for referencing CrossBrand variables like `font-size: theme('fontSize.cb-font-size-b-xl-font-size')`.
- The legacy font and typography details can be viewed here - <a href='https://ecom-next-storybook.aks.test.azeus.gaptech.com/?path=/story/fabric-ds-foundation-typography-legacy--fonts' target='_blank'>**Legacy Fonts**</a> & <a href='https://ecom-next-storybook.aks.test.azeus.gaptech.com/?path=/story/fabric-ds-foundation-typography-legacy--typography' target='_blank'>**Legacy Typography**</a>
- The new Fabric DS font details can be viewed here - <a href='https://ecom-next-storybook.aks.test.azeus.gaptech.com/?path=/story/fabric-ds-foundation-typography--fonts' target='_blank'>**Fabric DS Fonts**</a>
  <br /><br />

<span style='font-weight: bold; font-size: 1rem;'>`6.` When / how to use existing variables (colors, spacing, etc.)?</span>

- For the **MIGRATION** phase (tailwind replacement), refer to the storybooks below.
  - <a href='https://ecom-next-storybook.aks.test.azeus.gaptech.com/?path=/story/fabric-ds-foundation-colors-legacy--legacy-colors' target='_blank'>**Legacy Colors**</a>
  - <a href='https://ecom-next-storybook.aks.test.azeus.gaptech.com/?path=/story/fabric-ds-foundation-typography-legacy--fonts' target='_blank'>**Legacy Fonts**</a>
  - <a href='https://ecom-next-storybook.aks.test.azeus.gaptech.com/?path=/story/fabric-ds-foundation-typography-legacy--typography' target='_blank'>**Legacy Typography**</a>
    <br/><br/>
- For the **REDESIGN** phase (new design), refer to the storybooks below.
  - <a href='https://ecom-next-storybook.aks.test.azeus.gaptech.com/?path=/story/fabric-ds-foundation-colors-fabriccolors--fabric-colors' target='_blank'>**Fabric DS Colors**</a>
  - <a href='https://ecom-next-storybook.aks.test.azeus.gaptech.com/?path=/story/fabric-ds-foundation-typography--fonts' target='_blank'>**Fabric DS Fonts**</a>
    <br /><br />

<span style='font-weight: bold; font-size: 1rem;'>`7.` How to check if a Core UI component has been built already in HUI using Fabric DS?</span>

- The Fabric DS Core UI components are available under this directory: `packages/core/src/components/fabric`
  - Importing a Fabric DS Core UI component (Button) and its prop types.
  ```jsx
  import { Button, ButtonProps } from '@ecom-next/core/fabric/button';
  ```
  - For the Fabric Design components using the figma variables, check the Fabric DS Storybook for component availability - <a href='https://ecom-next-storybook.aks.test.azeus.gaptech.com/?path=/docs/fabric-ds-accordion-docs--docs' target='_blank'>**Fabric DS Storybook**</a>
    <br /><br />
- Apart from the Fabric DS Core UI components, the legacy and migrated Core UI components could be found under the directories below.
  - **Legacy Core UI Components**: `packages/core/src/components/legacy` - These are ported components using react-stitch with Emotion CSS.
  - **Migrated Core UI Components**: `packages/core/src/components/migration` - These are ported components that replaced react-stitch with Tailwind CSS. This is an interim solution until the new design is implemented.

<span style='font-weight: bold; font-size: 1rem;'>`8.` How/why to set up classnames using BEM (and our BEM methodology)?</span>

- The BEM (Block Element Modifier) naming standard is used for the CSS class names in HUI. This methodology helps to avoid conflicts and maintain a clean and organized CSS structure to improve readability. Refer to the BEM documentation for more details: <a href='https://getbem.com/naming/' target='_blank'>BEM Naming</a>
  <br /><br />

<span style='font-weight: bold; font-size: 1rem;'>`9.` How to import styles and css files rather than inline CSS?</span>

- For the Fabric DS components in HUI CSS implementation, CSS modules are <span style='text-decoration: underline;'>recommended</span> for HUI to keep the styling scoped and organized and prevent global CSS conflicts. The inline CSS is not recommended as it is difficult to maintain and causes bloating. Developers can still use inline CSS as needed but CSS modules are preferred.
- To use CSS modules, the style file is created under the component folder with the `.module.css` extension. The CSS file is imported into the component file and the class names are used in the JSX. Refer to the CSS Module in NextJS documentation for more details - <a href='https://nextjs.org/docs/app/building-your-application/styling/css-modules#css-modules' target='_blank'>**CSS Modules in Next JS**</a>
  <br /><br />

<span style='font-weight: bold; font-size: 1rem;'>`10.` What to do when functionality is missing?</span>

- Contact <a href='https://gapinc.enterprise.slack.com/archives/C07NHPRFYFJ' target='_blank'>**Design Support Channel**</a> on Slack~ specifically for styling / rewrite questions.

## <span id='demo' style='background: lightslategray; padding: 0.75rem; display: inline-block; width: 100%; color: white; font-size: 1.5rem; text-transform: uppercase;'>DEMO <a href='#top'><span style='font-size: 0.75rem; color: yellow; margin-left: 0.5rem;'>Go to top</span></a></span>

<h3>**Migration Demo**</h3>
<a href='https://gapinc.zoom.us/rec/share/ph9x-UXskv9Xv39JSgrBmnqE6jhffQnzh-Q7_fqbx_8i3qLCY6FhPppNCF7jUR-D.FvqQTLirGUBwSXCL' target='_blank'>https://gapinc.zoom.us/rec/share/ph9x-UXskv9Xv39JSgrBmnqE6jhffQnzh-Q7_fqbx_8i3qLCY6FhPppNCF7jUR-D.FvqQTLirGUBwSXCL</a>
**Passcode:** cMa6+A
<br /><br />
The migration demo covers:
- Application of the Fabric Design in HUI code using component examples.
- Styling components using theme variables & recommendations.
- Implementing styles for Product components.
