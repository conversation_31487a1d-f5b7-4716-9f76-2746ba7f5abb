@import 'tailwindcss/components';
@import 'tailwindcss/utilities';
@import './cb.styles.css';

@layer components {
  .fds_drawer {
    display: flex;
    overflow: auto;
    position: absolute;
    text-align: center;
    z-index: theme('zIndex.drawer');
    font-family: theme('fontFamily.brand-base');
    background-color: theme('colors.color-background-default--white');
    transition-delay: 0ms;
    transition-duration: 500ms;
    transition-timing-function: cubic-bezier(0.25, 0, 0.25, 1);

    &.fds_drawer__top {
      top: 0;
      left: 0;
      right: 0;
      width: 100%;
      max-height: 100%;
      flex-direction: column;
      justify-content: space-between;
      transform: translateY(-100%);
      border-bottom-right-radius: theme('borderRadius.style-modal-border-radius');
      border-bottom-left-radius: theme('borderRadius.style-modal-border-radius');

      &.fds_drawer__top--opened {
        transform: translateY(0%);
      }

      @media (max-width: theme('screens.sm')) {
        bottom: 10%;
      }

      &.fds_drawer__full_screen {
        @media (max-width: theme('screens.sm')) {
          bottom: 0;
          border-radius: 0;
        }
      }
    }

    &.fds_drawer__bottom {
      left: 0;
      right: 0;
      bottom: 0;
      width: 100%;
      max-height: 100%;
      flex-direction: column;
      justify-content: space-between;
      transform: translateY(100%);
      border-top-right-radius: theme('borderRadius.style-modal-border-radius');
      border-top-left-radius: theme('borderRadius.style-modal-border-radius');

      &.fds_drawer__bottom--opened {
        transform: translateY(0%);
      }

      @media (max-width: theme('screens.sm')) {
        top: 10%;
      }

      &.fds_drawer__full_screen {
        @media (max-width: theme('screens.sm')) {
          top: 0;
          border-radius: 0;
        }
      }
    }

    &.fds_drawer__left {
      top: 0;
      left: 0;
      bottom: 0;
      height: inherit;
      flex-direction: column;
      justify-content: space-between;
      transform: translateX(-100%);

      @media (max-width: theme('screens.sm')) {
        right: 20%;
      }

      &.fds_drawer__full_screen {
        @media (max-width: theme('screens.sm')) {
          right: 0;
          border-radius: 0;
        }
      }

      &.fds_drawer__left--opened {
        transform: translateX(0%);
      }
    }

    &.fds_drawer__right {
      top: 0;
      right: 0;
      bottom: 0;
      height: inherit;
      flex-direction: column;
      justify-content: space-between;
      transform: translateX(100%);

      @media (max-width: theme('screens.sm')) {
        left: 20%;
      }

      &.fds_drawer__full_screen {
        @media (max-width: 500px) {
          left: 0;
          border-radius: 0;
        }
      }

      &.fds_drawer__right--opened {
        transform: translateX(0%);
      }
    }

    .fds_drawer__container {
      display: flex;
      flex-direction: column;
      justify-content: flex-start;

      .fds_drawer__header-container {
        width: 100%;
        display: flex;
        padding: theme('spacing.spacing-m');
        background-color: theme('colors.color-background-default--white');
        top: 0;
        position: sticky;
        align-items: center;

        @media (min-width: theme('screens.sm')) {
          padding: theme('spacing.spacing-m');
        }

        &.fds_drawer__header-container--no-header {
          display: none;
        }

        &.fds_drawer__header-container--icon-only {
          border-bottom: none;
          height: fit-content;

          .fds_drawer__header-container-header {
            @media (min-width: theme('screens.sm')) {
              padding-bottom: 0;
            }
          }
        }

        .fds_drawer__header-container__header {
          width: 100%;
          display: flex;
          overflow: hidden;
          text-overflow: ellipsis;
          color: theme('colors.color-type-copy');
          font-size: theme('fontSize.font-size-1');
          font-weight: theme('fontWeight.font-weight-base-heavier');
          letter-spacing: theme('letterSpacing.font-letter-spacing-base');
        }

        .fds_drawer__header-container__close-icon {
          display: flex;
          position: absolute;
          align-items: center;
          justify-content: center;
          right: theme('spacing.spacing-m');
          top: calc(theme('spacing.spacing-m') - 2px);

          svg {
            fill: theme('colors.color-icon-default');
          }
        }
      }

      .fds_drawer__content {
        display: flex;
        width: fit-content;
      }
    }

    .fds_drawer__button-content {
      height: 100%;
      display: flex;
      align-items: flex-end;
    }

    .fds_drawer__footer {
      flex-shrink: 0;
      position: sticky;
      bottom: 0;
      background-color: theme('colors.color-background-default--white');
      border-top: theme('borderWidth.border-width-default') solid theme('colors.color-border-disabled');
      padding: theme('spacing.utk-spacing-l');
    }
  }

  :is(.br, .brfs) .fds_drawer .fds_drawer__container .fds_drawer__header-container .fds_drawer__header-container__header {
    text-transform: uppercase;
  }
}
