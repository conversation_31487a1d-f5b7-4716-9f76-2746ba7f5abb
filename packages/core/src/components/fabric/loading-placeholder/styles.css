@import 'tailwindcss/components';
@import 'tailwindcss/utilities';

@layer components {
  .fds_loading-placeholder {
    position: relative;
    background: linear-gradient(
      300.82deg,
      theme('colors.color-surface-subtle') 6.57%,
      theme('colors.color-page-background-hazy') 51.24%,
      theme('colors.color-surface-subtle') 93.09%
    );
    background-size: 300% 100%;

    &.crossbrand {
      background: linear-gradient(
        300.82deg,
        theme('colors.cb-color-surface-subtle') 6.57%,
        theme('colors.cb-color-page-background-hazy') 51.24%,
        theme('colors.cb-color-surface-subtle') 93.09%
      );
      background-size: 300% 100%;
    }
  }
}
