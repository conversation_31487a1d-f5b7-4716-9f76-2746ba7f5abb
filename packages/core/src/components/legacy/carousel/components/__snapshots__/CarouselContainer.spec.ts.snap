// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`CarouselContainer snapshots renders customDotStyles state correctly Athleta 1`] = `
<DocumentFragment>
  .emotion-0 {
  position: relative;
}

.emotion-0 .slick-slider {
  position: relative;
  display: block;
  box-sizing: border-box;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  -webkit-touch-callout: none;
  touch-action: pan-y;
  -webkit-tap-highlight-color: transparent;
  scroll-behavior: smooth;
}

.emotion-0 .slick-slider .slick-track,
.emotion-0 .slick-slider .slick-list {
  -webkit-transform: translate3d(0, 0, 0);
  -moz-transform: translate3d(0, 0, 0);
  -ms-transform: translate3d(0, 0, 0);
  transform: translate3d(0, 0, 0);
}

.emotion-0 .slick-list {
  position: relative;
  display: block;
  overflow-x: hidden;
  overflow-y: hidden;
  margin: 0;
  padding: 0;
}

.emotion-0 .slick-list:focus {
  outline: none;
}

.emotion-0 .slick-list.dragging {
  cursor: pointer;
  cursor: hand;
}

.emotion-0 .slick-track {
  position: relative;
  top: 0;
  left: 0;
  display: block;
  margin-left: auto;
  margin-right: auto;
}

.emotion-0 .slick-track:before,
.emotion-0 .slick-track:after {
  display: table;
  content: "";
}

.emotion-0 .slick-track:after {
  clear: both;
}

.emotion-0 .slick-slide {
  width: 48px!important;
  display: none;
  float: left;
  height: 100%;
  min-height: 1px;
}

.emotion-0 .slick-slide img {
  display: block;
}

.emotion-0 .slick-slide.slick-loading img {
  display: none;
}

.emotion-0 .slick-slide.dragging img {
  pointer-events: none;
}

.emotion-0 [dir="rtl"] .slick-slide {
  float: right;
}

.emotion-0 .slick-initialized .slick-slide,
.emotion-0 .slick-vertical .slick-slide {
  display: block;
}

.emotion-0 .slick-vertical .slick-slide {
  border: 1px solid transparent;
  height: auto;
}

.emotion-0 .slick-loading .slick-track,
.emotion-0 .slick-loading .slick-slide {
  visibility: hidden;
}

.emotion-0 .slick-loading .slick-list {
  background: #fff url("./ajax-loader.gif") center center no-repeat;
}

.emotion-0 .slick-arrow.slick-hidden {
  display: none;
}

.emotion-0 .slick-prev,
.emotion-0 .slick-next {
  font-size: 0;
  line-height: 0;
  position: absolute;
  top: 50%;
  display: block;
  padding: 0;
  cursor: pointer;
  color: transparent;
  border: none;
  outline: none;
  background: transparent;
}

.emotion-0 .slick-prev:hover,
.emotion-0 .slick-next:hover,
.emotion-0 .slick-prev:focus,
.emotion-0 .slick-next:focus {
  color: transparent;
  outline: none;
  background: transparent;
  opacity: 1;
}

.emotion-0 .slick-prev.slick-disabled,
.emotion-0 .slick-next.slick-disabled {
  opacity: 0.25;
}

.emotion-0 .slick-prev {
  left: -25px;
  -webkit-transform: translate(0, -50%) rotate(90deg);
  -moz-transform: translate(0, -50%) rotate(90deg);
  -ms-transform: translate(0, -50%) rotate(90deg);
  transform: translate(0, -50%) rotate(90deg);
}

.emotion-0 .slick-prev span {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-0 [dir="rtl"] .slick-prev {
  right: -25px;
  left: auto;
}

.emotion-0 .slick-next {
  right: -25px;
  -webkit-transform: translate(0, -50%) rotate(-90deg);
  -moz-transform: translate(0, -50%) rotate(-90deg);
  -ms-transform: translate(0, -50%) rotate(-90deg);
  transform: translate(0, -50%) rotate(-90deg);
}

.emotion-0 .slick-next span {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-0 [dir="rtl"] .slick-next {
  right: auto;
  left: -25px;
}

.emotion-0 .slick-dotted.slick-slider {
  margin-bottom: 30px;
}

.emotion-0 .slick-dots {
  position: absolute;
  bottom: -25px;
  display: block;
  width: 100%;
  padding: 0;
  margin: 0;
  list-style: none;
  text-align: center;
  bottom: -12px;
}

.emotion-0 .slick-dots li {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: 20px;
  height: 20px;
  margin: 0 5px;
  padding: 0;
  cursor: pointer;
}

.emotion-0 .slick-dots li.slick-active button:before {
  opacity: 0.75;
  color: black;
}

.emotion-0 .slick-dots li button {
  font-size: 0;
  line-height: 0;
  display: block;
  padding: 5px;
  cursor: pointer;
  color: transparent;
  border: 0;
  outline: none;
  background: transparent;
}

.emotion-0 .slick-dots li button:hover,
.emotion-0 .slick-dots li button:focus {
  outline: none;
}

.emotion-0 .slick-dots li button:hover:before,
.emotion-0 .slick-dots li button:focus:before,
.emotion-0 .slick-dots li button:hover:before,
.emotion-0 .slick-dots li button:focus:before {
  opacity: 1;
}

.emotion-0 .slick-dots li button:before {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 5px;
  height: 5px;
  content: "";
  opacity: 0.25;
  background-color: black;
  border-radius: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-0 .slick-dots li {
  width: 24px;
  height: 8px;
}

.emotion-0 .slick-dots li button {
  width: 24px;
  height: 8px;
}

.emotion-0 .slick-dots li button:before {
  width: 24px;
  height: 8px;
  background-color: red;
  border-radius: initial;
}

<div
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`CarouselContainer snapshots renders customDotStyles state correctly Athleta in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  position: relative;
}

.emotion-0 .slick-slider {
  position: relative;
  display: block;
  box-sizing: border-box;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  -webkit-touch-callout: none;
  touch-action: pan-y;
  -webkit-tap-highlight-color: transparent;
  scroll-behavior: smooth;
}

.emotion-0 .slick-slider .slick-track,
.emotion-0 .slick-slider .slick-list {
  -webkit-transform: translate3d(0, 0, 0);
  -moz-transform: translate3d(0, 0, 0);
  -ms-transform: translate3d(0, 0, 0);
  transform: translate3d(0, 0, 0);
}

.emotion-0 .slick-list {
  position: relative;
  display: block;
  overflow-x: hidden;
  overflow-y: hidden;
  margin: 0;
  padding: 0;
}

.emotion-0 .slick-list:focus {
  outline: none;
}

.emotion-0 .slick-list.dragging {
  cursor: pointer;
  cursor: hand;
}

.emotion-0 .slick-track {
  position: relative;
  top: 0;
  left: 0;
  display: block;
  margin-left: auto;
  margin-right: auto;
}

.emotion-0 .slick-track:before,
.emotion-0 .slick-track:after {
  display: table;
  content: "";
}

.emotion-0 .slick-track:after {
  clear: both;
}

.emotion-0 .slick-slide {
  width: 48px!important;
  display: none;
  float: left;
  height: 100%;
  min-height: 1px;
}

.emotion-0 .slick-slide img {
  display: block;
}

.emotion-0 .slick-slide.slick-loading img {
  display: none;
}

.emotion-0 .slick-slide.dragging img {
  pointer-events: none;
}

.emotion-0 [dir="rtl"] .slick-slide {
  float: right;
}

.emotion-0 .slick-initialized .slick-slide,
.emotion-0 .slick-vertical .slick-slide {
  display: block;
}

.emotion-0 .slick-vertical .slick-slide {
  border: 1px solid transparent;
  height: auto;
}

.emotion-0 .slick-loading .slick-track,
.emotion-0 .slick-loading .slick-slide {
  visibility: hidden;
}

.emotion-0 .slick-loading .slick-list {
  background: #fff url("./ajax-loader.gif") center center no-repeat;
}

.emotion-0 .slick-arrow.slick-hidden {
  display: none;
}

.emotion-0 .slick-prev,
.emotion-0 .slick-next {
  font-size: 0;
  line-height: 0;
  position: absolute;
  top: 50%;
  display: block;
  padding: 0;
  cursor: pointer;
  color: transparent;
  border: none;
  outline: none;
  background: transparent;
}

.emotion-0 .slick-prev:hover,
.emotion-0 .slick-next:hover,
.emotion-0 .slick-prev:focus,
.emotion-0 .slick-next:focus {
  color: transparent;
  outline: none;
  background: transparent;
  opacity: 1;
}

.emotion-0 .slick-prev.slick-disabled,
.emotion-0 .slick-next.slick-disabled {
  opacity: 0.25;
}

.emotion-0 .slick-prev {
  left: -25px;
  -webkit-transform: translate(0, -50%) rotate(90deg);
  -moz-transform: translate(0, -50%) rotate(90deg);
  -ms-transform: translate(0, -50%) rotate(90deg);
  transform: translate(0, -50%) rotate(90deg);
}

.emotion-0 .slick-prev span {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-0 [dir="rtl"] .slick-prev {
  right: -25px;
  left: auto;
}

.emotion-0 .slick-next {
  right: -25px;
  -webkit-transform: translate(0, -50%) rotate(-90deg);
  -moz-transform: translate(0, -50%) rotate(-90deg);
  -ms-transform: translate(0, -50%) rotate(-90deg);
  transform: translate(0, -50%) rotate(-90deg);
}

.emotion-0 .slick-next span {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-0 [dir="rtl"] .slick-next {
  right: auto;
  left: -25px;
}

.emotion-0 .slick-dotted.slick-slider {
  margin-bottom: 30px;
}

.emotion-0 .slick-dots {
  position: absolute;
  bottom: -25px;
  display: block;
  width: 100%;
  padding: 0;
  margin: 0;
  list-style: none;
  text-align: center;
  bottom: -12px;
}

.emotion-0 .slick-dots li {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: 20px;
  height: 20px;
  margin: 0 5px;
  padding: 0;
  cursor: pointer;
}

.emotion-0 .slick-dots li.slick-active button:before {
  opacity: 0.75;
  color: black;
}

.emotion-0 .slick-dots li button {
  font-size: 0;
  line-height: 0;
  display: block;
  padding: 5px;
  cursor: pointer;
  color: transparent;
  border: 0;
  outline: none;
  background: transparent;
}

.emotion-0 .slick-dots li button:hover,
.emotion-0 .slick-dots li button:focus {
  outline: none;
}

.emotion-0 .slick-dots li button:hover:before,
.emotion-0 .slick-dots li button:focus:before,
.emotion-0 .slick-dots li button:hover:before,
.emotion-0 .slick-dots li button:focus:before {
  opacity: 1;
}

.emotion-0 .slick-dots li button:before {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 5px;
  height: 5px;
  content: "";
  opacity: 0.25;
  background-color: black;
  border-radius: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-0 .slick-dots li {
  width: 24px;
  height: 8px;
}

.emotion-0 .slick-dots li button {
  width: 24px;
  height: 8px;
}

.emotion-0 .slick-dots li button:before {
  width: 24px;
  height: 8px;
  background-color: red;
  border-radius: initial;
}

<div
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`CarouselContainer snapshots renders customDotStyles state correctly BananaRepublic 1`] = `
<DocumentFragment>
  .emotion-0 {
  position: relative;
}

.emotion-0 .slick-slider {
  position: relative;
  display: block;
  box-sizing: border-box;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  -webkit-touch-callout: none;
  touch-action: pan-y;
  -webkit-tap-highlight-color: transparent;
  scroll-behavior: smooth;
}

.emotion-0 .slick-slider .slick-track,
.emotion-0 .slick-slider .slick-list {
  -webkit-transform: translate3d(0, 0, 0);
  -moz-transform: translate3d(0, 0, 0);
  -ms-transform: translate3d(0, 0, 0);
  transform: translate3d(0, 0, 0);
}

.emotion-0 .slick-list {
  position: relative;
  display: block;
  overflow-x: hidden;
  overflow-y: hidden;
  margin: 0;
  padding: 0;
}

.emotion-0 .slick-list:focus {
  outline: none;
}

.emotion-0 .slick-list.dragging {
  cursor: pointer;
  cursor: hand;
}

.emotion-0 .slick-track {
  position: relative;
  top: 0;
  left: 0;
  display: block;
  margin-left: auto;
  margin-right: auto;
}

.emotion-0 .slick-track:before,
.emotion-0 .slick-track:after {
  display: table;
  content: "";
}

.emotion-0 .slick-track:after {
  clear: both;
}

.emotion-0 .slick-slide {
  width: 48px!important;
  display: none;
  float: left;
  height: 100%;
  min-height: 1px;
}

.emotion-0 .slick-slide img {
  display: block;
}

.emotion-0 .slick-slide.slick-loading img {
  display: none;
}

.emotion-0 .slick-slide.dragging img {
  pointer-events: none;
}

.emotion-0 [dir="rtl"] .slick-slide {
  float: right;
}

.emotion-0 .slick-initialized .slick-slide,
.emotion-0 .slick-vertical .slick-slide {
  display: block;
}

.emotion-0 .slick-vertical .slick-slide {
  border: 1px solid transparent;
  height: auto;
}

.emotion-0 .slick-loading .slick-track,
.emotion-0 .slick-loading .slick-slide {
  visibility: hidden;
}

.emotion-0 .slick-loading .slick-list {
  background: #fff url("./ajax-loader.gif") center center no-repeat;
}

.emotion-0 .slick-arrow.slick-hidden {
  display: none;
}

.emotion-0 .slick-prev,
.emotion-0 .slick-next {
  font-size: 0;
  line-height: 0;
  position: absolute;
  top: 50%;
  display: block;
  padding: 0;
  cursor: pointer;
  color: transparent;
  border: none;
  outline: none;
  background: transparent;
}

.emotion-0 .slick-prev:hover,
.emotion-0 .slick-next:hover,
.emotion-0 .slick-prev:focus,
.emotion-0 .slick-next:focus {
  color: transparent;
  outline: none;
  background: transparent;
  opacity: 1;
}

.emotion-0 .slick-prev.slick-disabled,
.emotion-0 .slick-next.slick-disabled {
  opacity: 0.25;
}

.emotion-0 .slick-prev {
  left: -25px;
  -webkit-transform: translate(0, -50%) rotate(90deg);
  -moz-transform: translate(0, -50%) rotate(90deg);
  -ms-transform: translate(0, -50%) rotate(90deg);
  transform: translate(0, -50%) rotate(90deg);
}

.emotion-0 .slick-prev span {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-0 [dir="rtl"] .slick-prev {
  right: -25px;
  left: auto;
}

.emotion-0 .slick-next {
  right: -25px;
  -webkit-transform: translate(0, -50%) rotate(-90deg);
  -moz-transform: translate(0, -50%) rotate(-90deg);
  -ms-transform: translate(0, -50%) rotate(-90deg);
  transform: translate(0, -50%) rotate(-90deg);
}

.emotion-0 .slick-next span {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-0 [dir="rtl"] .slick-next {
  right: auto;
  left: -25px;
}

.emotion-0 .slick-dotted.slick-slider {
  margin-bottom: 30px;
}

.emotion-0 .slick-dots {
  position: absolute;
  bottom: -25px;
  display: block;
  width: 100%;
  padding: 0;
  margin: 0;
  list-style: none;
  text-align: center;
  bottom: -12px;
}

.emotion-0 .slick-dots li {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: 20px;
  height: 20px;
  margin: 0 5px;
  padding: 0;
  cursor: pointer;
}

.emotion-0 .slick-dots li.slick-active button:before {
  opacity: 0.75;
  color: black;
}

.emotion-0 .slick-dots li button {
  font-size: 0;
  line-height: 0;
  display: block;
  padding: 5px;
  cursor: pointer;
  color: transparent;
  border: 0;
  outline: none;
  background: transparent;
}

.emotion-0 .slick-dots li button:hover,
.emotion-0 .slick-dots li button:focus {
  outline: none;
}

.emotion-0 .slick-dots li button:hover:before,
.emotion-0 .slick-dots li button:focus:before,
.emotion-0 .slick-dots li button:hover:before,
.emotion-0 .slick-dots li button:focus:before {
  opacity: 1;
}

.emotion-0 .slick-dots li button:before {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 5px;
  height: 5px;
  content: "";
  opacity: 0.25;
  background-color: black;
  border-radius: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-0 .slick-dots li {
  width: 24px;
  height: 8px;
}

.emotion-0 .slick-dots li button {
  width: 24px;
  height: 8px;
}

.emotion-0 .slick-dots li button:before {
  width: 24px;
  height: 8px;
  background-color: red;
  border-radius: initial;
}

<div
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`CarouselContainer snapshots renders customDotStyles state correctly BananaRepublic in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  position: relative;
}

.emotion-0 .slick-slider {
  position: relative;
  display: block;
  box-sizing: border-box;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  -webkit-touch-callout: none;
  touch-action: pan-y;
  -webkit-tap-highlight-color: transparent;
  scroll-behavior: smooth;
}

.emotion-0 .slick-slider .slick-track,
.emotion-0 .slick-slider .slick-list {
  -webkit-transform: translate3d(0, 0, 0);
  -moz-transform: translate3d(0, 0, 0);
  -ms-transform: translate3d(0, 0, 0);
  transform: translate3d(0, 0, 0);
}

.emotion-0 .slick-list {
  position: relative;
  display: block;
  overflow-x: hidden;
  overflow-y: hidden;
  margin: 0;
  padding: 0;
}

.emotion-0 .slick-list:focus {
  outline: none;
}

.emotion-0 .slick-list.dragging {
  cursor: pointer;
  cursor: hand;
}

.emotion-0 .slick-track {
  position: relative;
  top: 0;
  left: 0;
  display: block;
  margin-left: auto;
  margin-right: auto;
}

.emotion-0 .slick-track:before,
.emotion-0 .slick-track:after {
  display: table;
  content: "";
}

.emotion-0 .slick-track:after {
  clear: both;
}

.emotion-0 .slick-slide {
  width: 48px!important;
  display: none;
  float: left;
  height: 100%;
  min-height: 1px;
}

.emotion-0 .slick-slide img {
  display: block;
}

.emotion-0 .slick-slide.slick-loading img {
  display: none;
}

.emotion-0 .slick-slide.dragging img {
  pointer-events: none;
}

.emotion-0 [dir="rtl"] .slick-slide {
  float: right;
}

.emotion-0 .slick-initialized .slick-slide,
.emotion-0 .slick-vertical .slick-slide {
  display: block;
}

.emotion-0 .slick-vertical .slick-slide {
  border: 1px solid transparent;
  height: auto;
}

.emotion-0 .slick-loading .slick-track,
.emotion-0 .slick-loading .slick-slide {
  visibility: hidden;
}

.emotion-0 .slick-loading .slick-list {
  background: #fff url("./ajax-loader.gif") center center no-repeat;
}

.emotion-0 .slick-arrow.slick-hidden {
  display: none;
}

.emotion-0 .slick-prev,
.emotion-0 .slick-next {
  font-size: 0;
  line-height: 0;
  position: absolute;
  top: 50%;
  display: block;
  padding: 0;
  cursor: pointer;
  color: transparent;
  border: none;
  outline: none;
  background: transparent;
}

.emotion-0 .slick-prev:hover,
.emotion-0 .slick-next:hover,
.emotion-0 .slick-prev:focus,
.emotion-0 .slick-next:focus {
  color: transparent;
  outline: none;
  background: transparent;
  opacity: 1;
}

.emotion-0 .slick-prev.slick-disabled,
.emotion-0 .slick-next.slick-disabled {
  opacity: 0.25;
}

.emotion-0 .slick-prev {
  left: -25px;
  -webkit-transform: translate(0, -50%) rotate(90deg);
  -moz-transform: translate(0, -50%) rotate(90deg);
  -ms-transform: translate(0, -50%) rotate(90deg);
  transform: translate(0, -50%) rotate(90deg);
}

.emotion-0 .slick-prev span {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-0 [dir="rtl"] .slick-prev {
  right: -25px;
  left: auto;
}

.emotion-0 .slick-next {
  right: -25px;
  -webkit-transform: translate(0, -50%) rotate(-90deg);
  -moz-transform: translate(0, -50%) rotate(-90deg);
  -ms-transform: translate(0, -50%) rotate(-90deg);
  transform: translate(0, -50%) rotate(-90deg);
}

.emotion-0 .slick-next span {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-0 [dir="rtl"] .slick-next {
  right: auto;
  left: -25px;
}

.emotion-0 .slick-dotted.slick-slider {
  margin-bottom: 30px;
}

.emotion-0 .slick-dots {
  position: absolute;
  bottom: -25px;
  display: block;
  width: 100%;
  padding: 0;
  margin: 0;
  list-style: none;
  text-align: center;
  bottom: -12px;
}

.emotion-0 .slick-dots li {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: 20px;
  height: 20px;
  margin: 0 5px;
  padding: 0;
  cursor: pointer;
}

.emotion-0 .slick-dots li.slick-active button:before {
  opacity: 0.75;
  color: black;
}

.emotion-0 .slick-dots li button {
  font-size: 0;
  line-height: 0;
  display: block;
  padding: 5px;
  cursor: pointer;
  color: transparent;
  border: 0;
  outline: none;
  background: transparent;
}

.emotion-0 .slick-dots li button:hover,
.emotion-0 .slick-dots li button:focus {
  outline: none;
}

.emotion-0 .slick-dots li button:hover:before,
.emotion-0 .slick-dots li button:focus:before,
.emotion-0 .slick-dots li button:hover:before,
.emotion-0 .slick-dots li button:focus:before {
  opacity: 1;
}

.emotion-0 .slick-dots li button:before {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 5px;
  height: 5px;
  content: "";
  opacity: 0.25;
  background-color: black;
  border-radius: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-0 .slick-dots li {
  width: 24px;
  height: 8px;
}

.emotion-0 .slick-dots li button {
  width: 24px;
  height: 8px;
}

.emotion-0 .slick-dots li button:before {
  width: 24px;
  height: 8px;
  background-color: red;
  border-radius: initial;
}

<div
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`CarouselContainer snapshots renders customDotStyles state correctly BananaRepublicFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  position: relative;
}

.emotion-0 .slick-slider {
  position: relative;
  display: block;
  box-sizing: border-box;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  -webkit-touch-callout: none;
  touch-action: pan-y;
  -webkit-tap-highlight-color: transparent;
  scroll-behavior: smooth;
}

.emotion-0 .slick-slider .slick-track,
.emotion-0 .slick-slider .slick-list {
  -webkit-transform: translate3d(0, 0, 0);
  -moz-transform: translate3d(0, 0, 0);
  -ms-transform: translate3d(0, 0, 0);
  transform: translate3d(0, 0, 0);
}

.emotion-0 .slick-list {
  position: relative;
  display: block;
  overflow-x: hidden;
  overflow-y: hidden;
  margin: 0;
  padding: 0;
}

.emotion-0 .slick-list:focus {
  outline: none;
}

.emotion-0 .slick-list.dragging {
  cursor: pointer;
  cursor: hand;
}

.emotion-0 .slick-track {
  position: relative;
  top: 0;
  left: 0;
  display: block;
  margin-left: auto;
  margin-right: auto;
}

.emotion-0 .slick-track:before,
.emotion-0 .slick-track:after {
  display: table;
  content: "";
}

.emotion-0 .slick-track:after {
  clear: both;
}

.emotion-0 .slick-slide {
  width: 48px!important;
  display: none;
  float: left;
  height: 100%;
  min-height: 1px;
}

.emotion-0 .slick-slide img {
  display: block;
}

.emotion-0 .slick-slide.slick-loading img {
  display: none;
}

.emotion-0 .slick-slide.dragging img {
  pointer-events: none;
}

.emotion-0 [dir="rtl"] .slick-slide {
  float: right;
}

.emotion-0 .slick-initialized .slick-slide,
.emotion-0 .slick-vertical .slick-slide {
  display: block;
}

.emotion-0 .slick-vertical .slick-slide {
  border: 1px solid transparent;
  height: auto;
}

.emotion-0 .slick-loading .slick-track,
.emotion-0 .slick-loading .slick-slide {
  visibility: hidden;
}

.emotion-0 .slick-loading .slick-list {
  background: #fff url("./ajax-loader.gif") center center no-repeat;
}

.emotion-0 .slick-arrow.slick-hidden {
  display: none;
}

.emotion-0 .slick-prev,
.emotion-0 .slick-next {
  font-size: 0;
  line-height: 0;
  position: absolute;
  top: 50%;
  display: block;
  padding: 0;
  cursor: pointer;
  color: transparent;
  border: none;
  outline: none;
  background: transparent;
}

.emotion-0 .slick-prev:hover,
.emotion-0 .slick-next:hover,
.emotion-0 .slick-prev:focus,
.emotion-0 .slick-next:focus {
  color: transparent;
  outline: none;
  background: transparent;
  opacity: 1;
}

.emotion-0 .slick-prev.slick-disabled,
.emotion-0 .slick-next.slick-disabled {
  opacity: 0.25;
}

.emotion-0 .slick-prev {
  left: -25px;
  -webkit-transform: translate(0, -50%) rotate(90deg);
  -moz-transform: translate(0, -50%) rotate(90deg);
  -ms-transform: translate(0, -50%) rotate(90deg);
  transform: translate(0, -50%) rotate(90deg);
}

.emotion-0 .slick-prev span {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-0 [dir="rtl"] .slick-prev {
  right: -25px;
  left: auto;
}

.emotion-0 .slick-next {
  right: -25px;
  -webkit-transform: translate(0, -50%) rotate(-90deg);
  -moz-transform: translate(0, -50%) rotate(-90deg);
  -ms-transform: translate(0, -50%) rotate(-90deg);
  transform: translate(0, -50%) rotate(-90deg);
}

.emotion-0 .slick-next span {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-0 [dir="rtl"] .slick-next {
  right: auto;
  left: -25px;
}

.emotion-0 .slick-dotted.slick-slider {
  margin-bottom: 30px;
}

.emotion-0 .slick-dots {
  position: absolute;
  bottom: -25px;
  display: block;
  width: 100%;
  padding: 0;
  margin: 0;
  list-style: none;
  text-align: center;
  bottom: -12px;
}

.emotion-0 .slick-dots li {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: 20px;
  height: 20px;
  margin: 0 5px;
  padding: 0;
  cursor: pointer;
}

.emotion-0 .slick-dots li.slick-active button:before {
  opacity: 0.75;
  color: black;
}

.emotion-0 .slick-dots li button {
  font-size: 0;
  line-height: 0;
  display: block;
  padding: 5px;
  cursor: pointer;
  color: transparent;
  border: 0;
  outline: none;
  background: transparent;
}

.emotion-0 .slick-dots li button:hover,
.emotion-0 .slick-dots li button:focus {
  outline: none;
}

.emotion-0 .slick-dots li button:hover:before,
.emotion-0 .slick-dots li button:focus:before,
.emotion-0 .slick-dots li button:hover:before,
.emotion-0 .slick-dots li button:focus:before {
  opacity: 1;
}

.emotion-0 .slick-dots li button:before {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 5px;
  height: 5px;
  content: "";
  opacity: 0.25;
  background-color: black;
  border-radius: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-0 .slick-dots li {
  width: 24px;
  height: 8px;
}

.emotion-0 .slick-dots li button {
  width: 24px;
  height: 8px;
}

.emotion-0 .slick-dots li button:before {
  width: 24px;
  height: 8px;
  background-color: red;
  border-radius: initial;
}

<div
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`CarouselContainer snapshots renders customDotStyles state correctly BananaRepublicFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  position: relative;
}

.emotion-0 .slick-slider {
  position: relative;
  display: block;
  box-sizing: border-box;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  -webkit-touch-callout: none;
  touch-action: pan-y;
  -webkit-tap-highlight-color: transparent;
  scroll-behavior: smooth;
}

.emotion-0 .slick-slider .slick-track,
.emotion-0 .slick-slider .slick-list {
  -webkit-transform: translate3d(0, 0, 0);
  -moz-transform: translate3d(0, 0, 0);
  -ms-transform: translate3d(0, 0, 0);
  transform: translate3d(0, 0, 0);
}

.emotion-0 .slick-list {
  position: relative;
  display: block;
  overflow-x: hidden;
  overflow-y: hidden;
  margin: 0;
  padding: 0;
}

.emotion-0 .slick-list:focus {
  outline: none;
}

.emotion-0 .slick-list.dragging {
  cursor: pointer;
  cursor: hand;
}

.emotion-0 .slick-track {
  position: relative;
  top: 0;
  left: 0;
  display: block;
  margin-left: auto;
  margin-right: auto;
}

.emotion-0 .slick-track:before,
.emotion-0 .slick-track:after {
  display: table;
  content: "";
}

.emotion-0 .slick-track:after {
  clear: both;
}

.emotion-0 .slick-slide {
  width: 48px!important;
  display: none;
  float: left;
  height: 100%;
  min-height: 1px;
}

.emotion-0 .slick-slide img {
  display: block;
}

.emotion-0 .slick-slide.slick-loading img {
  display: none;
}

.emotion-0 .slick-slide.dragging img {
  pointer-events: none;
}

.emotion-0 [dir="rtl"] .slick-slide {
  float: right;
}

.emotion-0 .slick-initialized .slick-slide,
.emotion-0 .slick-vertical .slick-slide {
  display: block;
}

.emotion-0 .slick-vertical .slick-slide {
  border: 1px solid transparent;
  height: auto;
}

.emotion-0 .slick-loading .slick-track,
.emotion-0 .slick-loading .slick-slide {
  visibility: hidden;
}

.emotion-0 .slick-loading .slick-list {
  background: #fff url("./ajax-loader.gif") center center no-repeat;
}

.emotion-0 .slick-arrow.slick-hidden {
  display: none;
}

.emotion-0 .slick-prev,
.emotion-0 .slick-next {
  font-size: 0;
  line-height: 0;
  position: absolute;
  top: 50%;
  display: block;
  padding: 0;
  cursor: pointer;
  color: transparent;
  border: none;
  outline: none;
  background: transparent;
}

.emotion-0 .slick-prev:hover,
.emotion-0 .slick-next:hover,
.emotion-0 .slick-prev:focus,
.emotion-0 .slick-next:focus {
  color: transparent;
  outline: none;
  background: transparent;
  opacity: 1;
}

.emotion-0 .slick-prev.slick-disabled,
.emotion-0 .slick-next.slick-disabled {
  opacity: 0.25;
}

.emotion-0 .slick-prev {
  left: -25px;
  -webkit-transform: translate(0, -50%) rotate(90deg);
  -moz-transform: translate(0, -50%) rotate(90deg);
  -ms-transform: translate(0, -50%) rotate(90deg);
  transform: translate(0, -50%) rotate(90deg);
}

.emotion-0 .slick-prev span {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-0 [dir="rtl"] .slick-prev {
  right: -25px;
  left: auto;
}

.emotion-0 .slick-next {
  right: -25px;
  -webkit-transform: translate(0, -50%) rotate(-90deg);
  -moz-transform: translate(0, -50%) rotate(-90deg);
  -ms-transform: translate(0, -50%) rotate(-90deg);
  transform: translate(0, -50%) rotate(-90deg);
}

.emotion-0 .slick-next span {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-0 [dir="rtl"] .slick-next {
  right: auto;
  left: -25px;
}

.emotion-0 .slick-dotted.slick-slider {
  margin-bottom: 30px;
}

.emotion-0 .slick-dots {
  position: absolute;
  bottom: -25px;
  display: block;
  width: 100%;
  padding: 0;
  margin: 0;
  list-style: none;
  text-align: center;
  bottom: -12px;
}

.emotion-0 .slick-dots li {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: 20px;
  height: 20px;
  margin: 0 5px;
  padding: 0;
  cursor: pointer;
}

.emotion-0 .slick-dots li.slick-active button:before {
  opacity: 0.75;
  color: black;
}

.emotion-0 .slick-dots li button {
  font-size: 0;
  line-height: 0;
  display: block;
  padding: 5px;
  cursor: pointer;
  color: transparent;
  border: 0;
  outline: none;
  background: transparent;
}

.emotion-0 .slick-dots li button:hover,
.emotion-0 .slick-dots li button:focus {
  outline: none;
}

.emotion-0 .slick-dots li button:hover:before,
.emotion-0 .slick-dots li button:focus:before,
.emotion-0 .slick-dots li button:hover:before,
.emotion-0 .slick-dots li button:focus:before {
  opacity: 1;
}

.emotion-0 .slick-dots li button:before {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 5px;
  height: 5px;
  content: "";
  opacity: 0.25;
  background-color: black;
  border-radius: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-0 .slick-dots li {
  width: 24px;
  height: 8px;
}

.emotion-0 .slick-dots li button {
  width: 24px;
  height: 8px;
}

.emotion-0 .slick-dots li button:before {
  width: 24px;
  height: 8px;
  background-color: red;
  border-radius: initial;
}

<div
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`CarouselContainer snapshots renders customDotStyles state correctly Gap 1`] = `
<DocumentFragment>
  .emotion-0 {
  position: relative;
}

.emotion-0 .slick-slider {
  position: relative;
  display: block;
  box-sizing: border-box;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  -webkit-touch-callout: none;
  touch-action: pan-y;
  -webkit-tap-highlight-color: transparent;
  scroll-behavior: smooth;
}

.emotion-0 .slick-slider .slick-track,
.emotion-0 .slick-slider .slick-list {
  -webkit-transform: translate3d(0, 0, 0);
  -moz-transform: translate3d(0, 0, 0);
  -ms-transform: translate3d(0, 0, 0);
  transform: translate3d(0, 0, 0);
}

.emotion-0 .slick-list {
  position: relative;
  display: block;
  overflow-x: hidden;
  overflow-y: hidden;
  margin: 0;
  padding: 0;
}

.emotion-0 .slick-list:focus {
  outline: none;
}

.emotion-0 .slick-list.dragging {
  cursor: pointer;
  cursor: hand;
}

.emotion-0 .slick-track {
  position: relative;
  top: 0;
  left: 0;
  display: block;
  margin-left: auto;
  margin-right: auto;
}

.emotion-0 .slick-track:before,
.emotion-0 .slick-track:after {
  display: table;
  content: "";
}

.emotion-0 .slick-track:after {
  clear: both;
}

.emotion-0 .slick-slide {
  width: 48px!important;
  display: none;
  float: left;
  height: 100%;
  min-height: 1px;
}

.emotion-0 .slick-slide img {
  display: block;
}

.emotion-0 .slick-slide.slick-loading img {
  display: none;
}

.emotion-0 .slick-slide.dragging img {
  pointer-events: none;
}

.emotion-0 [dir="rtl"] .slick-slide {
  float: right;
}

.emotion-0 .slick-initialized .slick-slide,
.emotion-0 .slick-vertical .slick-slide {
  display: block;
}

.emotion-0 .slick-vertical .slick-slide {
  border: 1px solid transparent;
  height: auto;
}

.emotion-0 .slick-loading .slick-track,
.emotion-0 .slick-loading .slick-slide {
  visibility: hidden;
}

.emotion-0 .slick-loading .slick-list {
  background: #fff url("./ajax-loader.gif") center center no-repeat;
}

.emotion-0 .slick-arrow.slick-hidden {
  display: none;
}

.emotion-0 .slick-prev,
.emotion-0 .slick-next {
  font-size: 0;
  line-height: 0;
  position: absolute;
  top: 50%;
  display: block;
  padding: 0;
  cursor: pointer;
  color: transparent;
  border: none;
  outline: none;
  background: transparent;
}

.emotion-0 .slick-prev:hover,
.emotion-0 .slick-next:hover,
.emotion-0 .slick-prev:focus,
.emotion-0 .slick-next:focus {
  color: transparent;
  outline: none;
  background: transparent;
  opacity: 1;
}

.emotion-0 .slick-prev.slick-disabled,
.emotion-0 .slick-next.slick-disabled {
  opacity: 0.25;
}

.emotion-0 .slick-prev {
  left: -25px;
  -webkit-transform: translate(0, -50%) rotate(90deg);
  -moz-transform: translate(0, -50%) rotate(90deg);
  -ms-transform: translate(0, -50%) rotate(90deg);
  transform: translate(0, -50%) rotate(90deg);
}

.emotion-0 .slick-prev span {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-0 [dir="rtl"] .slick-prev {
  right: -25px;
  left: auto;
}

.emotion-0 .slick-next {
  right: -25px;
  -webkit-transform: translate(0, -50%) rotate(-90deg);
  -moz-transform: translate(0, -50%) rotate(-90deg);
  -ms-transform: translate(0, -50%) rotate(-90deg);
  transform: translate(0, -50%) rotate(-90deg);
}

.emotion-0 .slick-next span {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-0 [dir="rtl"] .slick-next {
  right: auto;
  left: -25px;
}

.emotion-0 .slick-dotted.slick-slider {
  margin-bottom: 30px;
}

.emotion-0 .slick-dots {
  position: absolute;
  bottom: -25px;
  display: block;
  width: 100%;
  padding: 0;
  margin: 0;
  list-style: none;
  text-align: center;
  bottom: -12px;
}

.emotion-0 .slick-dots li {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: 20px;
  height: 20px;
  margin: 0 5px;
  padding: 0;
  cursor: pointer;
}

.emotion-0 .slick-dots li.slick-active button:before {
  opacity: 0.75;
  color: black;
}

.emotion-0 .slick-dots li button {
  font-size: 0;
  line-height: 0;
  display: block;
  padding: 5px;
  cursor: pointer;
  color: transparent;
  border: 0;
  outline: none;
  background: transparent;
}

.emotion-0 .slick-dots li button:hover,
.emotion-0 .slick-dots li button:focus {
  outline: none;
}

.emotion-0 .slick-dots li button:hover:before,
.emotion-0 .slick-dots li button:focus:before,
.emotion-0 .slick-dots li button:hover:before,
.emotion-0 .slick-dots li button:focus:before {
  opacity: 1;
}

.emotion-0 .slick-dots li button:before {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 5px;
  height: 5px;
  content: "";
  opacity: 0.25;
  background-color: black;
  border-radius: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-0 .slick-dots li {
  width: 24px;
  height: 8px;
}

.emotion-0 .slick-dots li button {
  width: 24px;
  height: 8px;
}

.emotion-0 .slick-dots li button:before {
  width: 24px;
  height: 8px;
  background-color: red;
  border-radius: initial;
}

<div
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`CarouselContainer snapshots renders customDotStyles state correctly Gap in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  position: relative;
}

.emotion-0 .slick-slider {
  position: relative;
  display: block;
  box-sizing: border-box;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  -webkit-touch-callout: none;
  touch-action: pan-y;
  -webkit-tap-highlight-color: transparent;
  scroll-behavior: smooth;
}

.emotion-0 .slick-slider .slick-track,
.emotion-0 .slick-slider .slick-list {
  -webkit-transform: translate3d(0, 0, 0);
  -moz-transform: translate3d(0, 0, 0);
  -ms-transform: translate3d(0, 0, 0);
  transform: translate3d(0, 0, 0);
}

.emotion-0 .slick-list {
  position: relative;
  display: block;
  overflow-x: hidden;
  overflow-y: hidden;
  margin: 0;
  padding: 0;
}

.emotion-0 .slick-list:focus {
  outline: none;
}

.emotion-0 .slick-list.dragging {
  cursor: pointer;
  cursor: hand;
}

.emotion-0 .slick-track {
  position: relative;
  top: 0;
  left: 0;
  display: block;
  margin-left: auto;
  margin-right: auto;
}

.emotion-0 .slick-track:before,
.emotion-0 .slick-track:after {
  display: table;
  content: "";
}

.emotion-0 .slick-track:after {
  clear: both;
}

.emotion-0 .slick-slide {
  width: 48px!important;
  display: none;
  float: left;
  height: 100%;
  min-height: 1px;
}

.emotion-0 .slick-slide img {
  display: block;
}

.emotion-0 .slick-slide.slick-loading img {
  display: none;
}

.emotion-0 .slick-slide.dragging img {
  pointer-events: none;
}

.emotion-0 [dir="rtl"] .slick-slide {
  float: right;
}

.emotion-0 .slick-initialized .slick-slide,
.emotion-0 .slick-vertical .slick-slide {
  display: block;
}

.emotion-0 .slick-vertical .slick-slide {
  border: 1px solid transparent;
  height: auto;
}

.emotion-0 .slick-loading .slick-track,
.emotion-0 .slick-loading .slick-slide {
  visibility: hidden;
}

.emotion-0 .slick-loading .slick-list {
  background: #fff url("./ajax-loader.gif") center center no-repeat;
}

.emotion-0 .slick-arrow.slick-hidden {
  display: none;
}

.emotion-0 .slick-prev,
.emotion-0 .slick-next {
  font-size: 0;
  line-height: 0;
  position: absolute;
  top: 50%;
  display: block;
  padding: 0;
  cursor: pointer;
  color: transparent;
  border: none;
  outline: none;
  background: transparent;
}

.emotion-0 .slick-prev:hover,
.emotion-0 .slick-next:hover,
.emotion-0 .slick-prev:focus,
.emotion-0 .slick-next:focus {
  color: transparent;
  outline: none;
  background: transparent;
  opacity: 1;
}

.emotion-0 .slick-prev.slick-disabled,
.emotion-0 .slick-next.slick-disabled {
  opacity: 0.25;
}

.emotion-0 .slick-prev {
  left: -25px;
  -webkit-transform: translate(0, -50%) rotate(90deg);
  -moz-transform: translate(0, -50%) rotate(90deg);
  -ms-transform: translate(0, -50%) rotate(90deg);
  transform: translate(0, -50%) rotate(90deg);
}

.emotion-0 .slick-prev span {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-0 [dir="rtl"] .slick-prev {
  right: -25px;
  left: auto;
}

.emotion-0 .slick-next {
  right: -25px;
  -webkit-transform: translate(0, -50%) rotate(-90deg);
  -moz-transform: translate(0, -50%) rotate(-90deg);
  -ms-transform: translate(0, -50%) rotate(-90deg);
  transform: translate(0, -50%) rotate(-90deg);
}

.emotion-0 .slick-next span {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-0 [dir="rtl"] .slick-next {
  right: auto;
  left: -25px;
}

.emotion-0 .slick-dotted.slick-slider {
  margin-bottom: 30px;
}

.emotion-0 .slick-dots {
  position: absolute;
  bottom: -25px;
  display: block;
  width: 100%;
  padding: 0;
  margin: 0;
  list-style: none;
  text-align: center;
  bottom: -12px;
}

.emotion-0 .slick-dots li {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: 20px;
  height: 20px;
  margin: 0 5px;
  padding: 0;
  cursor: pointer;
}

.emotion-0 .slick-dots li.slick-active button:before {
  opacity: 0.75;
  color: black;
}

.emotion-0 .slick-dots li button {
  font-size: 0;
  line-height: 0;
  display: block;
  padding: 5px;
  cursor: pointer;
  color: transparent;
  border: 0;
  outline: none;
  background: transparent;
}

.emotion-0 .slick-dots li button:hover,
.emotion-0 .slick-dots li button:focus {
  outline: none;
}

.emotion-0 .slick-dots li button:hover:before,
.emotion-0 .slick-dots li button:focus:before,
.emotion-0 .slick-dots li button:hover:before,
.emotion-0 .slick-dots li button:focus:before {
  opacity: 1;
}

.emotion-0 .slick-dots li button:before {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 5px;
  height: 5px;
  content: "";
  opacity: 0.25;
  background-color: black;
  border-radius: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-0 .slick-dots li {
  width: 24px;
  height: 8px;
}

.emotion-0 .slick-dots li button {
  width: 24px;
  height: 8px;
}

.emotion-0 .slick-dots li button:before {
  width: 24px;
  height: 8px;
  background-color: red;
  border-radius: initial;
}

<div
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`CarouselContainer snapshots renders customDotStyles state correctly GapFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  position: relative;
}

.emotion-0 .slick-slider {
  position: relative;
  display: block;
  box-sizing: border-box;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  -webkit-touch-callout: none;
  touch-action: pan-y;
  -webkit-tap-highlight-color: transparent;
  scroll-behavior: smooth;
}

.emotion-0 .slick-slider .slick-track,
.emotion-0 .slick-slider .slick-list {
  -webkit-transform: translate3d(0, 0, 0);
  -moz-transform: translate3d(0, 0, 0);
  -ms-transform: translate3d(0, 0, 0);
  transform: translate3d(0, 0, 0);
}

.emotion-0 .slick-list {
  position: relative;
  display: block;
  overflow-x: hidden;
  overflow-y: hidden;
  margin: 0;
  padding: 0;
}

.emotion-0 .slick-list:focus {
  outline: none;
}

.emotion-0 .slick-list.dragging {
  cursor: pointer;
  cursor: hand;
}

.emotion-0 .slick-track {
  position: relative;
  top: 0;
  left: 0;
  display: block;
  margin-left: auto;
  margin-right: auto;
}

.emotion-0 .slick-track:before,
.emotion-0 .slick-track:after {
  display: table;
  content: "";
}

.emotion-0 .slick-track:after {
  clear: both;
}

.emotion-0 .slick-slide {
  width: 48px!important;
  display: none;
  float: left;
  height: 100%;
  min-height: 1px;
}

.emotion-0 .slick-slide img {
  display: block;
}

.emotion-0 .slick-slide.slick-loading img {
  display: none;
}

.emotion-0 .slick-slide.dragging img {
  pointer-events: none;
}

.emotion-0 [dir="rtl"] .slick-slide {
  float: right;
}

.emotion-0 .slick-initialized .slick-slide,
.emotion-0 .slick-vertical .slick-slide {
  display: block;
}

.emotion-0 .slick-vertical .slick-slide {
  border: 1px solid transparent;
  height: auto;
}

.emotion-0 .slick-loading .slick-track,
.emotion-0 .slick-loading .slick-slide {
  visibility: hidden;
}

.emotion-0 .slick-loading .slick-list {
  background: #fff url("./ajax-loader.gif") center center no-repeat;
}

.emotion-0 .slick-arrow.slick-hidden {
  display: none;
}

.emotion-0 .slick-prev,
.emotion-0 .slick-next {
  font-size: 0;
  line-height: 0;
  position: absolute;
  top: 50%;
  display: block;
  padding: 0;
  cursor: pointer;
  color: transparent;
  border: none;
  outline: none;
  background: transparent;
}

.emotion-0 .slick-prev:hover,
.emotion-0 .slick-next:hover,
.emotion-0 .slick-prev:focus,
.emotion-0 .slick-next:focus {
  color: transparent;
  outline: none;
  background: transparent;
  opacity: 1;
}

.emotion-0 .slick-prev.slick-disabled,
.emotion-0 .slick-next.slick-disabled {
  opacity: 0.25;
}

.emotion-0 .slick-prev {
  left: -25px;
  -webkit-transform: translate(0, -50%) rotate(90deg);
  -moz-transform: translate(0, -50%) rotate(90deg);
  -ms-transform: translate(0, -50%) rotate(90deg);
  transform: translate(0, -50%) rotate(90deg);
}

.emotion-0 .slick-prev span {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-0 [dir="rtl"] .slick-prev {
  right: -25px;
  left: auto;
}

.emotion-0 .slick-next {
  right: -25px;
  -webkit-transform: translate(0, -50%) rotate(-90deg);
  -moz-transform: translate(0, -50%) rotate(-90deg);
  -ms-transform: translate(0, -50%) rotate(-90deg);
  transform: translate(0, -50%) rotate(-90deg);
}

.emotion-0 .slick-next span {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-0 [dir="rtl"] .slick-next {
  right: auto;
  left: -25px;
}

.emotion-0 .slick-dotted.slick-slider {
  margin-bottom: 30px;
}

.emotion-0 .slick-dots {
  position: absolute;
  bottom: -25px;
  display: block;
  width: 100%;
  padding: 0;
  margin: 0;
  list-style: none;
  text-align: center;
  bottom: -12px;
}

.emotion-0 .slick-dots li {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: 20px;
  height: 20px;
  margin: 0 5px;
  padding: 0;
  cursor: pointer;
}

.emotion-0 .slick-dots li.slick-active button:before {
  opacity: 0.75;
  color: black;
}

.emotion-0 .slick-dots li button {
  font-size: 0;
  line-height: 0;
  display: block;
  padding: 5px;
  cursor: pointer;
  color: transparent;
  border: 0;
  outline: none;
  background: transparent;
}

.emotion-0 .slick-dots li button:hover,
.emotion-0 .slick-dots li button:focus {
  outline: none;
}

.emotion-0 .slick-dots li button:hover:before,
.emotion-0 .slick-dots li button:focus:before,
.emotion-0 .slick-dots li button:hover:before,
.emotion-0 .slick-dots li button:focus:before {
  opacity: 1;
}

.emotion-0 .slick-dots li button:before {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 5px;
  height: 5px;
  content: "";
  opacity: 0.25;
  background-color: black;
  border-radius: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-0 .slick-dots li {
  width: 24px;
  height: 8px;
}

.emotion-0 .slick-dots li button {
  width: 24px;
  height: 8px;
}

.emotion-0 .slick-dots li button:before {
  width: 24px;
  height: 8px;
  background-color: red;
  border-radius: initial;
}

<div
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`CarouselContainer snapshots renders customDotStyles state correctly GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  position: relative;
}

.emotion-0 .slick-slider {
  position: relative;
  display: block;
  box-sizing: border-box;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  -webkit-touch-callout: none;
  touch-action: pan-y;
  -webkit-tap-highlight-color: transparent;
  scroll-behavior: smooth;
}

.emotion-0 .slick-slider .slick-track,
.emotion-0 .slick-slider .slick-list {
  -webkit-transform: translate3d(0, 0, 0);
  -moz-transform: translate3d(0, 0, 0);
  -ms-transform: translate3d(0, 0, 0);
  transform: translate3d(0, 0, 0);
}

.emotion-0 .slick-list {
  position: relative;
  display: block;
  overflow-x: hidden;
  overflow-y: hidden;
  margin: 0;
  padding: 0;
}

.emotion-0 .slick-list:focus {
  outline: none;
}

.emotion-0 .slick-list.dragging {
  cursor: pointer;
  cursor: hand;
}

.emotion-0 .slick-track {
  position: relative;
  top: 0;
  left: 0;
  display: block;
  margin-left: auto;
  margin-right: auto;
}

.emotion-0 .slick-track:before,
.emotion-0 .slick-track:after {
  display: table;
  content: "";
}

.emotion-0 .slick-track:after {
  clear: both;
}

.emotion-0 .slick-slide {
  width: 48px!important;
  display: none;
  float: left;
  height: 100%;
  min-height: 1px;
}

.emotion-0 .slick-slide img {
  display: block;
}

.emotion-0 .slick-slide.slick-loading img {
  display: none;
}

.emotion-0 .slick-slide.dragging img {
  pointer-events: none;
}

.emotion-0 [dir="rtl"] .slick-slide {
  float: right;
}

.emotion-0 .slick-initialized .slick-slide,
.emotion-0 .slick-vertical .slick-slide {
  display: block;
}

.emotion-0 .slick-vertical .slick-slide {
  border: 1px solid transparent;
  height: auto;
}

.emotion-0 .slick-loading .slick-track,
.emotion-0 .slick-loading .slick-slide {
  visibility: hidden;
}

.emotion-0 .slick-loading .slick-list {
  background: #fff url("./ajax-loader.gif") center center no-repeat;
}

.emotion-0 .slick-arrow.slick-hidden {
  display: none;
}

.emotion-0 .slick-prev,
.emotion-0 .slick-next {
  font-size: 0;
  line-height: 0;
  position: absolute;
  top: 50%;
  display: block;
  padding: 0;
  cursor: pointer;
  color: transparent;
  border: none;
  outline: none;
  background: transparent;
}

.emotion-0 .slick-prev:hover,
.emotion-0 .slick-next:hover,
.emotion-0 .slick-prev:focus,
.emotion-0 .slick-next:focus {
  color: transparent;
  outline: none;
  background: transparent;
  opacity: 1;
}

.emotion-0 .slick-prev.slick-disabled,
.emotion-0 .slick-next.slick-disabled {
  opacity: 0.25;
}

.emotion-0 .slick-prev {
  left: -25px;
  -webkit-transform: translate(0, -50%) rotate(90deg);
  -moz-transform: translate(0, -50%) rotate(90deg);
  -ms-transform: translate(0, -50%) rotate(90deg);
  transform: translate(0, -50%) rotate(90deg);
}

.emotion-0 .slick-prev span {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-0 [dir="rtl"] .slick-prev {
  right: -25px;
  left: auto;
}

.emotion-0 .slick-next {
  right: -25px;
  -webkit-transform: translate(0, -50%) rotate(-90deg);
  -moz-transform: translate(0, -50%) rotate(-90deg);
  -ms-transform: translate(0, -50%) rotate(-90deg);
  transform: translate(0, -50%) rotate(-90deg);
}

.emotion-0 .slick-next span {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-0 [dir="rtl"] .slick-next {
  right: auto;
  left: -25px;
}

.emotion-0 .slick-dotted.slick-slider {
  margin-bottom: 30px;
}

.emotion-0 .slick-dots {
  position: absolute;
  bottom: -25px;
  display: block;
  width: 100%;
  padding: 0;
  margin: 0;
  list-style: none;
  text-align: center;
  bottom: -12px;
}

.emotion-0 .slick-dots li {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: 20px;
  height: 20px;
  margin: 0 5px;
  padding: 0;
  cursor: pointer;
}

.emotion-0 .slick-dots li.slick-active button:before {
  opacity: 0.75;
  color: black;
}

.emotion-0 .slick-dots li button {
  font-size: 0;
  line-height: 0;
  display: block;
  padding: 5px;
  cursor: pointer;
  color: transparent;
  border: 0;
  outline: none;
  background: transparent;
}

.emotion-0 .slick-dots li button:hover,
.emotion-0 .slick-dots li button:focus {
  outline: none;
}

.emotion-0 .slick-dots li button:hover:before,
.emotion-0 .slick-dots li button:focus:before,
.emotion-0 .slick-dots li button:hover:before,
.emotion-0 .slick-dots li button:focus:before {
  opacity: 1;
}

.emotion-0 .slick-dots li button:before {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 5px;
  height: 5px;
  content: "";
  opacity: 0.25;
  background-color: black;
  border-radius: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-0 .slick-dots li {
  width: 24px;
  height: 8px;
}

.emotion-0 .slick-dots li button {
  width: 24px;
  height: 8px;
}

.emotion-0 .slick-dots li button:before {
  width: 24px;
  height: 8px;
  background-color: red;
  border-radius: initial;
}

<div
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`CarouselContainer snapshots renders customDotStyles state correctly OldNavy 1`] = `
<DocumentFragment>
  .emotion-0 {
  position: relative;
}

.emotion-0 .slick-slider {
  position: relative;
  display: block;
  box-sizing: border-box;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  -webkit-touch-callout: none;
  touch-action: pan-y;
  -webkit-tap-highlight-color: transparent;
  scroll-behavior: smooth;
}

.emotion-0 .slick-slider .slick-track,
.emotion-0 .slick-slider .slick-list {
  -webkit-transform: translate3d(0, 0, 0);
  -moz-transform: translate3d(0, 0, 0);
  -ms-transform: translate3d(0, 0, 0);
  transform: translate3d(0, 0, 0);
}

.emotion-0 .slick-list {
  position: relative;
  display: block;
  overflow-x: hidden;
  overflow-y: hidden;
  margin: 0;
  padding: 0;
}

.emotion-0 .slick-list:focus {
  outline: none;
}

.emotion-0 .slick-list.dragging {
  cursor: pointer;
  cursor: hand;
}

.emotion-0 .slick-track {
  position: relative;
  top: 0;
  left: 0;
  display: block;
  margin-left: auto;
  margin-right: auto;
}

.emotion-0 .slick-track:before,
.emotion-0 .slick-track:after {
  display: table;
  content: "";
}

.emotion-0 .slick-track:after {
  clear: both;
}

.emotion-0 .slick-slide {
  width: 48px!important;
  display: none;
  float: left;
  height: 100%;
  min-height: 1px;
}

.emotion-0 .slick-slide img {
  display: block;
}

.emotion-0 .slick-slide.slick-loading img {
  display: none;
}

.emotion-0 .slick-slide.dragging img {
  pointer-events: none;
}

.emotion-0 [dir="rtl"] .slick-slide {
  float: right;
}

.emotion-0 .slick-initialized .slick-slide,
.emotion-0 .slick-vertical .slick-slide {
  display: block;
}

.emotion-0 .slick-vertical .slick-slide {
  border: 1px solid transparent;
  height: auto;
}

.emotion-0 .slick-loading .slick-track,
.emotion-0 .slick-loading .slick-slide {
  visibility: hidden;
}

.emotion-0 .slick-loading .slick-list {
  background: #fff url("./ajax-loader.gif") center center no-repeat;
}

.emotion-0 .slick-arrow.slick-hidden {
  display: none;
}

.emotion-0 .slick-prev,
.emotion-0 .slick-next {
  font-size: 0;
  line-height: 0;
  position: absolute;
  top: 50%;
  display: block;
  padding: 0;
  cursor: pointer;
  color: transparent;
  border: none;
  outline: none;
  background: transparent;
}

.emotion-0 .slick-prev:hover,
.emotion-0 .slick-next:hover,
.emotion-0 .slick-prev:focus,
.emotion-0 .slick-next:focus {
  color: transparent;
  outline: none;
  background: transparent;
  opacity: 1;
}

.emotion-0 .slick-prev.slick-disabled,
.emotion-0 .slick-next.slick-disabled {
  opacity: 0.25;
}

.emotion-0 .slick-prev {
  left: -25px;
  -webkit-transform: translate(0, -50%) rotate(90deg);
  -moz-transform: translate(0, -50%) rotate(90deg);
  -ms-transform: translate(0, -50%) rotate(90deg);
  transform: translate(0, -50%) rotate(90deg);
}

.emotion-0 .slick-prev span {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-0 [dir="rtl"] .slick-prev {
  right: -25px;
  left: auto;
}

.emotion-0 .slick-next {
  right: -25px;
  -webkit-transform: translate(0, -50%) rotate(-90deg);
  -moz-transform: translate(0, -50%) rotate(-90deg);
  -ms-transform: translate(0, -50%) rotate(-90deg);
  transform: translate(0, -50%) rotate(-90deg);
}

.emotion-0 .slick-next span {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-0 [dir="rtl"] .slick-next {
  right: auto;
  left: -25px;
}

.emotion-0 .slick-dotted.slick-slider {
  margin-bottom: 30px;
}

.emotion-0 .slick-dots {
  position: absolute;
  bottom: -25px;
  display: block;
  width: 100%;
  padding: 0;
  margin: 0;
  list-style: none;
  text-align: center;
  bottom: -12px;
}

.emotion-0 .slick-dots li {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: 20px;
  height: 20px;
  margin: 0 5px;
  padding: 0;
  cursor: pointer;
}

.emotion-0 .slick-dots li.slick-active button:before {
  opacity: 0.75;
  color: black;
}

.emotion-0 .slick-dots li button {
  font-size: 0;
  line-height: 0;
  display: block;
  padding: 5px;
  cursor: pointer;
  color: transparent;
  border: 0;
  outline: none;
  background: transparent;
}

.emotion-0 .slick-dots li button:hover,
.emotion-0 .slick-dots li button:focus {
  outline: none;
}

.emotion-0 .slick-dots li button:hover:before,
.emotion-0 .slick-dots li button:focus:before,
.emotion-0 .slick-dots li button:hover:before,
.emotion-0 .slick-dots li button:focus:before {
  opacity: 1;
}

.emotion-0 .slick-dots li button:before {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 5px;
  height: 5px;
  content: "";
  opacity: 0.25;
  background-color: black;
  border-radius: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-0 .slick-dots li {
  width: 24px;
  height: 8px;
}

.emotion-0 .slick-dots li button {
  width: 24px;
  height: 8px;
}

.emotion-0 .slick-dots li button:before {
  width: 24px;
  height: 8px;
  background-color: red;
  border-radius: initial;
}

<div
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`CarouselContainer snapshots renders customDotStyles state correctly OldNavy in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  position: relative;
}

.emotion-0 .slick-slider {
  position: relative;
  display: block;
  box-sizing: border-box;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  -webkit-touch-callout: none;
  touch-action: pan-y;
  -webkit-tap-highlight-color: transparent;
  scroll-behavior: smooth;
}

.emotion-0 .slick-slider .slick-track,
.emotion-0 .slick-slider .slick-list {
  -webkit-transform: translate3d(0, 0, 0);
  -moz-transform: translate3d(0, 0, 0);
  -ms-transform: translate3d(0, 0, 0);
  transform: translate3d(0, 0, 0);
}

.emotion-0 .slick-list {
  position: relative;
  display: block;
  overflow-x: hidden;
  overflow-y: hidden;
  margin: 0;
  padding: 0;
}

.emotion-0 .slick-list:focus {
  outline: none;
}

.emotion-0 .slick-list.dragging {
  cursor: pointer;
  cursor: hand;
}

.emotion-0 .slick-track {
  position: relative;
  top: 0;
  left: 0;
  display: block;
  margin-left: auto;
  margin-right: auto;
}

.emotion-0 .slick-track:before,
.emotion-0 .slick-track:after {
  display: table;
  content: "";
}

.emotion-0 .slick-track:after {
  clear: both;
}

.emotion-0 .slick-slide {
  width: 48px!important;
  display: none;
  float: left;
  height: 100%;
  min-height: 1px;
}

.emotion-0 .slick-slide img {
  display: block;
}

.emotion-0 .slick-slide.slick-loading img {
  display: none;
}

.emotion-0 .slick-slide.dragging img {
  pointer-events: none;
}

.emotion-0 [dir="rtl"] .slick-slide {
  float: right;
}

.emotion-0 .slick-initialized .slick-slide,
.emotion-0 .slick-vertical .slick-slide {
  display: block;
}

.emotion-0 .slick-vertical .slick-slide {
  border: 1px solid transparent;
  height: auto;
}

.emotion-0 .slick-loading .slick-track,
.emotion-0 .slick-loading .slick-slide {
  visibility: hidden;
}

.emotion-0 .slick-loading .slick-list {
  background: #fff url("./ajax-loader.gif") center center no-repeat;
}

.emotion-0 .slick-arrow.slick-hidden {
  display: none;
}

.emotion-0 .slick-prev,
.emotion-0 .slick-next {
  font-size: 0;
  line-height: 0;
  position: absolute;
  top: 50%;
  display: block;
  padding: 0;
  cursor: pointer;
  color: transparent;
  border: none;
  outline: none;
  background: transparent;
}

.emotion-0 .slick-prev:hover,
.emotion-0 .slick-next:hover,
.emotion-0 .slick-prev:focus,
.emotion-0 .slick-next:focus {
  color: transparent;
  outline: none;
  background: transparent;
  opacity: 1;
}

.emotion-0 .slick-prev.slick-disabled,
.emotion-0 .slick-next.slick-disabled {
  opacity: 0.25;
}

.emotion-0 .slick-prev {
  left: -25px;
  -webkit-transform: translate(0, -50%) rotate(90deg);
  -moz-transform: translate(0, -50%) rotate(90deg);
  -ms-transform: translate(0, -50%) rotate(90deg);
  transform: translate(0, -50%) rotate(90deg);
}

.emotion-0 .slick-prev span {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-0 [dir="rtl"] .slick-prev {
  right: -25px;
  left: auto;
}

.emotion-0 .slick-next {
  right: -25px;
  -webkit-transform: translate(0, -50%) rotate(-90deg);
  -moz-transform: translate(0, -50%) rotate(-90deg);
  -ms-transform: translate(0, -50%) rotate(-90deg);
  transform: translate(0, -50%) rotate(-90deg);
}

.emotion-0 .slick-next span {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-0 [dir="rtl"] .slick-next {
  right: auto;
  left: -25px;
}

.emotion-0 .slick-dotted.slick-slider {
  margin-bottom: 30px;
}

.emotion-0 .slick-dots {
  position: absolute;
  bottom: -25px;
  display: block;
  width: 100%;
  padding: 0;
  margin: 0;
  list-style: none;
  text-align: center;
  bottom: -12px;
}

.emotion-0 .slick-dots li {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: 20px;
  height: 20px;
  margin: 0 5px;
  padding: 0;
  cursor: pointer;
}

.emotion-0 .slick-dots li.slick-active button:before {
  opacity: 0.75;
  color: black;
}

.emotion-0 .slick-dots li button {
  font-size: 0;
  line-height: 0;
  display: block;
  padding: 5px;
  cursor: pointer;
  color: transparent;
  border: 0;
  outline: none;
  background: transparent;
}

.emotion-0 .slick-dots li button:hover,
.emotion-0 .slick-dots li button:focus {
  outline: none;
}

.emotion-0 .slick-dots li button:hover:before,
.emotion-0 .slick-dots li button:focus:before,
.emotion-0 .slick-dots li button:hover:before,
.emotion-0 .slick-dots li button:focus:before {
  opacity: 1;
}

.emotion-0 .slick-dots li button:before {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 5px;
  height: 5px;
  content: "";
  opacity: 0.25;
  background-color: black;
  border-radius: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-0 .slick-dots li {
  width: 24px;
  height: 8px;
}

.emotion-0 .slick-dots li button {
  width: 24px;
  height: 8px;
}

.emotion-0 .slick-dots li button:before {
  width: 24px;
  height: 8px;
  background-color: red;
  border-radius: initial;
}

<div
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`CarouselContainer snapshots renders default state correctly Athleta 1`] = `
<DocumentFragment>
  .emotion-0 {
  position: relative;
}

.emotion-0 .slick-slider {
  position: relative;
  display: block;
  box-sizing: border-box;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  -webkit-touch-callout: none;
  touch-action: pan-y;
  -webkit-tap-highlight-color: transparent;
  scroll-behavior: smooth;
}

.emotion-0 .slick-slider .slick-track,
.emotion-0 .slick-slider .slick-list {
  -webkit-transform: translate3d(0, 0, 0);
  -moz-transform: translate3d(0, 0, 0);
  -ms-transform: translate3d(0, 0, 0);
  transform: translate3d(0, 0, 0);
}

.emotion-0 .slick-list {
  position: relative;
  display: block;
  overflow-x: hidden;
  overflow-y: hidden;
  margin: 0;
  padding: 0;
}

.emotion-0 .slick-list:focus {
  outline: none;
}

.emotion-0 .slick-list.dragging {
  cursor: pointer;
  cursor: hand;
}

.emotion-0 .slick-track {
  position: relative;
  top: 0;
  left: 0;
  display: block;
  margin-left: auto;
  margin-right: auto;
}

.emotion-0 .slick-track:before,
.emotion-0 .slick-track:after {
  display: table;
  content: "";
}

.emotion-0 .slick-track:after {
  clear: both;
}

.emotion-0 .slick-slide {
  width: 48px!important;
  display: none;
  float: left;
  height: 100%;
  min-height: 1px;
}

.emotion-0 .slick-slide img {
  display: block;
}

.emotion-0 .slick-slide.slick-loading img {
  display: none;
}

.emotion-0 .slick-slide.dragging img {
  pointer-events: none;
}

.emotion-0 [dir="rtl"] .slick-slide {
  float: right;
}

.emotion-0 .slick-initialized .slick-slide,
.emotion-0 .slick-vertical .slick-slide {
  display: block;
}

.emotion-0 .slick-vertical .slick-slide {
  border: 1px solid transparent;
  height: auto;
}

.emotion-0 .slick-loading .slick-track,
.emotion-0 .slick-loading .slick-slide {
  visibility: hidden;
}

.emotion-0 .slick-loading .slick-list {
  background: #fff url("./ajax-loader.gif") center center no-repeat;
}

.emotion-0 .slick-arrow.slick-hidden {
  display: none;
}

.emotion-0 .slick-prev,
.emotion-0 .slick-next {
  font-size: 0;
  line-height: 0;
  position: absolute;
  top: 50%;
  display: block;
  padding: 0;
  cursor: pointer;
  color: transparent;
  border: none;
  outline: none;
  background: transparent;
}

.emotion-0 .slick-prev:hover,
.emotion-0 .slick-next:hover,
.emotion-0 .slick-prev:focus,
.emotion-0 .slick-next:focus {
  color: transparent;
  outline: none;
  background: transparent;
  opacity: 1;
}

.emotion-0 .slick-prev.slick-disabled,
.emotion-0 .slick-next.slick-disabled {
  opacity: 0.25;
}

.emotion-0 .slick-prev {
  left: -25px;
  -webkit-transform: translate(0, -50%) rotate(90deg);
  -moz-transform: translate(0, -50%) rotate(90deg);
  -ms-transform: translate(0, -50%) rotate(90deg);
  transform: translate(0, -50%) rotate(90deg);
}

.emotion-0 .slick-prev span {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-0 [dir="rtl"] .slick-prev {
  right: -25px;
  left: auto;
}

.emotion-0 .slick-next {
  right: -25px;
  -webkit-transform: translate(0, -50%) rotate(-90deg);
  -moz-transform: translate(0, -50%) rotate(-90deg);
  -ms-transform: translate(0, -50%) rotate(-90deg);
  transform: translate(0, -50%) rotate(-90deg);
}

.emotion-0 .slick-next span {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-0 [dir="rtl"] .slick-next {
  right: auto;
  left: -25px;
}

.emotion-0 .slick-dotted.slick-slider {
  margin-bottom: 30px;
}

.emotion-0 .slick-dots {
  position: absolute;
  bottom: -25px;
  display: block;
  width: 100%;
  padding: 0;
  margin: 0;
  list-style: none;
  text-align: center;
}

.emotion-0 .slick-dots li {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: 20px;
  height: 20px;
  margin: 0 5px;
  padding: 0;
  cursor: pointer;
}

.emotion-0 .slick-dots li.slick-active button:before {
  opacity: 0.75;
  color: black;
}

.emotion-0 .slick-dots li button {
  font-size: 0;
  line-height: 0;
  display: block;
  padding: 5px;
  cursor: pointer;
  color: transparent;
  border: 0;
  outline: none;
  background: transparent;
}

.emotion-0 .slick-dots li button:hover,
.emotion-0 .slick-dots li button:focus {
  outline: none;
}

.emotion-0 .slick-dots li button:hover:before,
.emotion-0 .slick-dots li button:focus:before,
.emotion-0 .slick-dots li button:hover:before,
.emotion-0 .slick-dots li button:focus:before {
  opacity: 1;
}

.emotion-0 .slick-dots li button:before {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 5px;
  height: 5px;
  content: "";
  opacity: 0.25;
  background-color: black;
  border-radius: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

<div
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`CarouselContainer snapshots renders default state correctly Athleta in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  position: relative;
}

.emotion-0 .slick-slider {
  position: relative;
  display: block;
  box-sizing: border-box;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  -webkit-touch-callout: none;
  touch-action: pan-y;
  -webkit-tap-highlight-color: transparent;
  scroll-behavior: smooth;
}

.emotion-0 .slick-slider .slick-track,
.emotion-0 .slick-slider .slick-list {
  -webkit-transform: translate3d(0, 0, 0);
  -moz-transform: translate3d(0, 0, 0);
  -ms-transform: translate3d(0, 0, 0);
  transform: translate3d(0, 0, 0);
}

.emotion-0 .slick-list {
  position: relative;
  display: block;
  overflow-x: hidden;
  overflow-y: hidden;
  margin: 0;
  padding: 0;
}

.emotion-0 .slick-list:focus {
  outline: none;
}

.emotion-0 .slick-list.dragging {
  cursor: pointer;
  cursor: hand;
}

.emotion-0 .slick-track {
  position: relative;
  top: 0;
  left: 0;
  display: block;
  margin-left: auto;
  margin-right: auto;
}

.emotion-0 .slick-track:before,
.emotion-0 .slick-track:after {
  display: table;
  content: "";
}

.emotion-0 .slick-track:after {
  clear: both;
}

.emotion-0 .slick-slide {
  width: 48px!important;
  display: none;
  float: left;
  height: 100%;
  min-height: 1px;
}

.emotion-0 .slick-slide img {
  display: block;
}

.emotion-0 .slick-slide.slick-loading img {
  display: none;
}

.emotion-0 .slick-slide.dragging img {
  pointer-events: none;
}

.emotion-0 [dir="rtl"] .slick-slide {
  float: right;
}

.emotion-0 .slick-initialized .slick-slide,
.emotion-0 .slick-vertical .slick-slide {
  display: block;
}

.emotion-0 .slick-vertical .slick-slide {
  border: 1px solid transparent;
  height: auto;
}

.emotion-0 .slick-loading .slick-track,
.emotion-0 .slick-loading .slick-slide {
  visibility: hidden;
}

.emotion-0 .slick-loading .slick-list {
  background: #fff url("./ajax-loader.gif") center center no-repeat;
}

.emotion-0 .slick-arrow.slick-hidden {
  display: none;
}

.emotion-0 .slick-prev,
.emotion-0 .slick-next {
  font-size: 0;
  line-height: 0;
  position: absolute;
  top: 50%;
  display: block;
  padding: 0;
  cursor: pointer;
  color: transparent;
  border: none;
  outline: none;
  background: transparent;
}

.emotion-0 .slick-prev:hover,
.emotion-0 .slick-next:hover,
.emotion-0 .slick-prev:focus,
.emotion-0 .slick-next:focus {
  color: transparent;
  outline: none;
  background: transparent;
  opacity: 1;
}

.emotion-0 .slick-prev.slick-disabled,
.emotion-0 .slick-next.slick-disabled {
  opacity: 0.25;
}

.emotion-0 .slick-prev {
  left: -25px;
  -webkit-transform: translate(0, -50%) rotate(90deg);
  -moz-transform: translate(0, -50%) rotate(90deg);
  -ms-transform: translate(0, -50%) rotate(90deg);
  transform: translate(0, -50%) rotate(90deg);
}

.emotion-0 .slick-prev span {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-0 [dir="rtl"] .slick-prev {
  right: -25px;
  left: auto;
}

.emotion-0 .slick-next {
  right: -25px;
  -webkit-transform: translate(0, -50%) rotate(-90deg);
  -moz-transform: translate(0, -50%) rotate(-90deg);
  -ms-transform: translate(0, -50%) rotate(-90deg);
  transform: translate(0, -50%) rotate(-90deg);
}

.emotion-0 .slick-next span {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-0 [dir="rtl"] .slick-next {
  right: auto;
  left: -25px;
}

.emotion-0 .slick-dotted.slick-slider {
  margin-bottom: 30px;
}

.emotion-0 .slick-dots {
  position: absolute;
  bottom: -25px;
  display: block;
  width: 100%;
  padding: 0;
  margin: 0;
  list-style: none;
  text-align: center;
}

.emotion-0 .slick-dots li {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: 20px;
  height: 20px;
  margin: 0 5px;
  padding: 0;
  cursor: pointer;
}

.emotion-0 .slick-dots li.slick-active button:before {
  opacity: 0.75;
  color: black;
}

.emotion-0 .slick-dots li button {
  font-size: 0;
  line-height: 0;
  display: block;
  padding: 5px;
  cursor: pointer;
  color: transparent;
  border: 0;
  outline: none;
  background: transparent;
}

.emotion-0 .slick-dots li button:hover,
.emotion-0 .slick-dots li button:focus {
  outline: none;
}

.emotion-0 .slick-dots li button:hover:before,
.emotion-0 .slick-dots li button:focus:before,
.emotion-0 .slick-dots li button:hover:before,
.emotion-0 .slick-dots li button:focus:before {
  opacity: 1;
}

.emotion-0 .slick-dots li button:before {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 5px;
  height: 5px;
  content: "";
  opacity: 0.25;
  background-color: black;
  border-radius: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

<div
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`CarouselContainer snapshots renders default state correctly BananaRepublic 1`] = `
<DocumentFragment>
  .emotion-0 {
  position: relative;
}

.emotion-0 .slick-slider {
  position: relative;
  display: block;
  box-sizing: border-box;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  -webkit-touch-callout: none;
  touch-action: pan-y;
  -webkit-tap-highlight-color: transparent;
  scroll-behavior: smooth;
}

.emotion-0 .slick-slider .slick-track,
.emotion-0 .slick-slider .slick-list {
  -webkit-transform: translate3d(0, 0, 0);
  -moz-transform: translate3d(0, 0, 0);
  -ms-transform: translate3d(0, 0, 0);
  transform: translate3d(0, 0, 0);
}

.emotion-0 .slick-list {
  position: relative;
  display: block;
  overflow-x: hidden;
  overflow-y: hidden;
  margin: 0;
  padding: 0;
}

.emotion-0 .slick-list:focus {
  outline: none;
}

.emotion-0 .slick-list.dragging {
  cursor: pointer;
  cursor: hand;
}

.emotion-0 .slick-track {
  position: relative;
  top: 0;
  left: 0;
  display: block;
  margin-left: auto;
  margin-right: auto;
}

.emotion-0 .slick-track:before,
.emotion-0 .slick-track:after {
  display: table;
  content: "";
}

.emotion-0 .slick-track:after {
  clear: both;
}

.emotion-0 .slick-slide {
  width: 48px!important;
  display: none;
  float: left;
  height: 100%;
  min-height: 1px;
}

.emotion-0 .slick-slide img {
  display: block;
}

.emotion-0 .slick-slide.slick-loading img {
  display: none;
}

.emotion-0 .slick-slide.dragging img {
  pointer-events: none;
}

.emotion-0 [dir="rtl"] .slick-slide {
  float: right;
}

.emotion-0 .slick-initialized .slick-slide,
.emotion-0 .slick-vertical .slick-slide {
  display: block;
}

.emotion-0 .slick-vertical .slick-slide {
  border: 1px solid transparent;
  height: auto;
}

.emotion-0 .slick-loading .slick-track,
.emotion-0 .slick-loading .slick-slide {
  visibility: hidden;
}

.emotion-0 .slick-loading .slick-list {
  background: #fff url("./ajax-loader.gif") center center no-repeat;
}

.emotion-0 .slick-arrow.slick-hidden {
  display: none;
}

.emotion-0 .slick-prev,
.emotion-0 .slick-next {
  font-size: 0;
  line-height: 0;
  position: absolute;
  top: 50%;
  display: block;
  padding: 0;
  cursor: pointer;
  color: transparent;
  border: none;
  outline: none;
  background: transparent;
}

.emotion-0 .slick-prev:hover,
.emotion-0 .slick-next:hover,
.emotion-0 .slick-prev:focus,
.emotion-0 .slick-next:focus {
  color: transparent;
  outline: none;
  background: transparent;
  opacity: 1;
}

.emotion-0 .slick-prev.slick-disabled,
.emotion-0 .slick-next.slick-disabled {
  opacity: 0.25;
}

.emotion-0 .slick-prev {
  left: -25px;
  -webkit-transform: translate(0, -50%) rotate(90deg);
  -moz-transform: translate(0, -50%) rotate(90deg);
  -ms-transform: translate(0, -50%) rotate(90deg);
  transform: translate(0, -50%) rotate(90deg);
}

.emotion-0 .slick-prev span {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-0 [dir="rtl"] .slick-prev {
  right: -25px;
  left: auto;
}

.emotion-0 .slick-next {
  right: -25px;
  -webkit-transform: translate(0, -50%) rotate(-90deg);
  -moz-transform: translate(0, -50%) rotate(-90deg);
  -ms-transform: translate(0, -50%) rotate(-90deg);
  transform: translate(0, -50%) rotate(-90deg);
}

.emotion-0 .slick-next span {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-0 [dir="rtl"] .slick-next {
  right: auto;
  left: -25px;
}

.emotion-0 .slick-dotted.slick-slider {
  margin-bottom: 30px;
}

.emotion-0 .slick-dots {
  position: absolute;
  bottom: -25px;
  display: block;
  width: 100%;
  padding: 0;
  margin: 0;
  list-style: none;
  text-align: center;
}

.emotion-0 .slick-dots li {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: 20px;
  height: 20px;
  margin: 0 5px;
  padding: 0;
  cursor: pointer;
}

.emotion-0 .slick-dots li.slick-active button:before {
  opacity: 0.75;
  color: black;
}

.emotion-0 .slick-dots li button {
  font-size: 0;
  line-height: 0;
  display: block;
  padding: 5px;
  cursor: pointer;
  color: transparent;
  border: 0;
  outline: none;
  background: transparent;
}

.emotion-0 .slick-dots li button:hover,
.emotion-0 .slick-dots li button:focus {
  outline: none;
}

.emotion-0 .slick-dots li button:hover:before,
.emotion-0 .slick-dots li button:focus:before,
.emotion-0 .slick-dots li button:hover:before,
.emotion-0 .slick-dots li button:focus:before {
  opacity: 1;
}

.emotion-0 .slick-dots li button:before {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 5px;
  height: 5px;
  content: "";
  opacity: 0.25;
  background-color: black;
  border-radius: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

<div
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`CarouselContainer snapshots renders default state correctly BananaRepublic in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  position: relative;
}

.emotion-0 .slick-slider {
  position: relative;
  display: block;
  box-sizing: border-box;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  -webkit-touch-callout: none;
  touch-action: pan-y;
  -webkit-tap-highlight-color: transparent;
  scroll-behavior: smooth;
}

.emotion-0 .slick-slider .slick-track,
.emotion-0 .slick-slider .slick-list {
  -webkit-transform: translate3d(0, 0, 0);
  -moz-transform: translate3d(0, 0, 0);
  -ms-transform: translate3d(0, 0, 0);
  transform: translate3d(0, 0, 0);
}

.emotion-0 .slick-list {
  position: relative;
  display: block;
  overflow-x: hidden;
  overflow-y: hidden;
  margin: 0;
  padding: 0;
}

.emotion-0 .slick-list:focus {
  outline: none;
}

.emotion-0 .slick-list.dragging {
  cursor: pointer;
  cursor: hand;
}

.emotion-0 .slick-track {
  position: relative;
  top: 0;
  left: 0;
  display: block;
  margin-left: auto;
  margin-right: auto;
}

.emotion-0 .slick-track:before,
.emotion-0 .slick-track:after {
  display: table;
  content: "";
}

.emotion-0 .slick-track:after {
  clear: both;
}

.emotion-0 .slick-slide {
  width: 48px!important;
  display: none;
  float: left;
  height: 100%;
  min-height: 1px;
}

.emotion-0 .slick-slide img {
  display: block;
}

.emotion-0 .slick-slide.slick-loading img {
  display: none;
}

.emotion-0 .slick-slide.dragging img {
  pointer-events: none;
}

.emotion-0 [dir="rtl"] .slick-slide {
  float: right;
}

.emotion-0 .slick-initialized .slick-slide,
.emotion-0 .slick-vertical .slick-slide {
  display: block;
}

.emotion-0 .slick-vertical .slick-slide {
  border: 1px solid transparent;
  height: auto;
}

.emotion-0 .slick-loading .slick-track,
.emotion-0 .slick-loading .slick-slide {
  visibility: hidden;
}

.emotion-0 .slick-loading .slick-list {
  background: #fff url("./ajax-loader.gif") center center no-repeat;
}

.emotion-0 .slick-arrow.slick-hidden {
  display: none;
}

.emotion-0 .slick-prev,
.emotion-0 .slick-next {
  font-size: 0;
  line-height: 0;
  position: absolute;
  top: 50%;
  display: block;
  padding: 0;
  cursor: pointer;
  color: transparent;
  border: none;
  outline: none;
  background: transparent;
}

.emotion-0 .slick-prev:hover,
.emotion-0 .slick-next:hover,
.emotion-0 .slick-prev:focus,
.emotion-0 .slick-next:focus {
  color: transparent;
  outline: none;
  background: transparent;
  opacity: 1;
}

.emotion-0 .slick-prev.slick-disabled,
.emotion-0 .slick-next.slick-disabled {
  opacity: 0.25;
}

.emotion-0 .slick-prev {
  left: -25px;
  -webkit-transform: translate(0, -50%) rotate(90deg);
  -moz-transform: translate(0, -50%) rotate(90deg);
  -ms-transform: translate(0, -50%) rotate(90deg);
  transform: translate(0, -50%) rotate(90deg);
}

.emotion-0 .slick-prev span {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-0 [dir="rtl"] .slick-prev {
  right: -25px;
  left: auto;
}

.emotion-0 .slick-next {
  right: -25px;
  -webkit-transform: translate(0, -50%) rotate(-90deg);
  -moz-transform: translate(0, -50%) rotate(-90deg);
  -ms-transform: translate(0, -50%) rotate(-90deg);
  transform: translate(0, -50%) rotate(-90deg);
}

.emotion-0 .slick-next span {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-0 [dir="rtl"] .slick-next {
  right: auto;
  left: -25px;
}

.emotion-0 .slick-dotted.slick-slider {
  margin-bottom: 30px;
}

.emotion-0 .slick-dots {
  position: absolute;
  bottom: -25px;
  display: block;
  width: 100%;
  padding: 0;
  margin: 0;
  list-style: none;
  text-align: center;
}

.emotion-0 .slick-dots li {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: 20px;
  height: 20px;
  margin: 0 5px;
  padding: 0;
  cursor: pointer;
}

.emotion-0 .slick-dots li.slick-active button:before {
  opacity: 0.75;
  color: black;
}

.emotion-0 .slick-dots li button {
  font-size: 0;
  line-height: 0;
  display: block;
  padding: 5px;
  cursor: pointer;
  color: transparent;
  border: 0;
  outline: none;
  background: transparent;
}

.emotion-0 .slick-dots li button:hover,
.emotion-0 .slick-dots li button:focus {
  outline: none;
}

.emotion-0 .slick-dots li button:hover:before,
.emotion-0 .slick-dots li button:focus:before,
.emotion-0 .slick-dots li button:hover:before,
.emotion-0 .slick-dots li button:focus:before {
  opacity: 1;
}

.emotion-0 .slick-dots li button:before {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 5px;
  height: 5px;
  content: "";
  opacity: 0.25;
  background-color: black;
  border-radius: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

<div
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`CarouselContainer snapshots renders default state correctly BananaRepublicFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  position: relative;
}

.emotion-0 .slick-slider {
  position: relative;
  display: block;
  box-sizing: border-box;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  -webkit-touch-callout: none;
  touch-action: pan-y;
  -webkit-tap-highlight-color: transparent;
  scroll-behavior: smooth;
}

.emotion-0 .slick-slider .slick-track,
.emotion-0 .slick-slider .slick-list {
  -webkit-transform: translate3d(0, 0, 0);
  -moz-transform: translate3d(0, 0, 0);
  -ms-transform: translate3d(0, 0, 0);
  transform: translate3d(0, 0, 0);
}

.emotion-0 .slick-list {
  position: relative;
  display: block;
  overflow-x: hidden;
  overflow-y: hidden;
  margin: 0;
  padding: 0;
}

.emotion-0 .slick-list:focus {
  outline: none;
}

.emotion-0 .slick-list.dragging {
  cursor: pointer;
  cursor: hand;
}

.emotion-0 .slick-track {
  position: relative;
  top: 0;
  left: 0;
  display: block;
  margin-left: auto;
  margin-right: auto;
}

.emotion-0 .slick-track:before,
.emotion-0 .slick-track:after {
  display: table;
  content: "";
}

.emotion-0 .slick-track:after {
  clear: both;
}

.emotion-0 .slick-slide {
  width: 48px!important;
  display: none;
  float: left;
  height: 100%;
  min-height: 1px;
}

.emotion-0 .slick-slide img {
  display: block;
}

.emotion-0 .slick-slide.slick-loading img {
  display: none;
}

.emotion-0 .slick-slide.dragging img {
  pointer-events: none;
}

.emotion-0 [dir="rtl"] .slick-slide {
  float: right;
}

.emotion-0 .slick-initialized .slick-slide,
.emotion-0 .slick-vertical .slick-slide {
  display: block;
}

.emotion-0 .slick-vertical .slick-slide {
  border: 1px solid transparent;
  height: auto;
}

.emotion-0 .slick-loading .slick-track,
.emotion-0 .slick-loading .slick-slide {
  visibility: hidden;
}

.emotion-0 .slick-loading .slick-list {
  background: #fff url("./ajax-loader.gif") center center no-repeat;
}

.emotion-0 .slick-arrow.slick-hidden {
  display: none;
}

.emotion-0 .slick-prev,
.emotion-0 .slick-next {
  font-size: 0;
  line-height: 0;
  position: absolute;
  top: 50%;
  display: block;
  padding: 0;
  cursor: pointer;
  color: transparent;
  border: none;
  outline: none;
  background: transparent;
}

.emotion-0 .slick-prev:hover,
.emotion-0 .slick-next:hover,
.emotion-0 .slick-prev:focus,
.emotion-0 .slick-next:focus {
  color: transparent;
  outline: none;
  background: transparent;
  opacity: 1;
}

.emotion-0 .slick-prev.slick-disabled,
.emotion-0 .slick-next.slick-disabled {
  opacity: 0.25;
}

.emotion-0 .slick-prev {
  left: -25px;
  -webkit-transform: translate(0, -50%) rotate(90deg);
  -moz-transform: translate(0, -50%) rotate(90deg);
  -ms-transform: translate(0, -50%) rotate(90deg);
  transform: translate(0, -50%) rotate(90deg);
}

.emotion-0 .slick-prev span {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-0 [dir="rtl"] .slick-prev {
  right: -25px;
  left: auto;
}

.emotion-0 .slick-next {
  right: -25px;
  -webkit-transform: translate(0, -50%) rotate(-90deg);
  -moz-transform: translate(0, -50%) rotate(-90deg);
  -ms-transform: translate(0, -50%) rotate(-90deg);
  transform: translate(0, -50%) rotate(-90deg);
}

.emotion-0 .slick-next span {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-0 [dir="rtl"] .slick-next {
  right: auto;
  left: -25px;
}

.emotion-0 .slick-dotted.slick-slider {
  margin-bottom: 30px;
}

.emotion-0 .slick-dots {
  position: absolute;
  bottom: -25px;
  display: block;
  width: 100%;
  padding: 0;
  margin: 0;
  list-style: none;
  text-align: center;
}

.emotion-0 .slick-dots li {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: 20px;
  height: 20px;
  margin: 0 5px;
  padding: 0;
  cursor: pointer;
}

.emotion-0 .slick-dots li.slick-active button:before {
  opacity: 0.75;
  color: black;
}

.emotion-0 .slick-dots li button {
  font-size: 0;
  line-height: 0;
  display: block;
  padding: 5px;
  cursor: pointer;
  color: transparent;
  border: 0;
  outline: none;
  background: transparent;
}

.emotion-0 .slick-dots li button:hover,
.emotion-0 .slick-dots li button:focus {
  outline: none;
}

.emotion-0 .slick-dots li button:hover:before,
.emotion-0 .slick-dots li button:focus:before,
.emotion-0 .slick-dots li button:hover:before,
.emotion-0 .slick-dots li button:focus:before {
  opacity: 1;
}

.emotion-0 .slick-dots li button:before {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 5px;
  height: 5px;
  content: "";
  opacity: 0.25;
  background-color: black;
  border-radius: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

<div
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`CarouselContainer snapshots renders default state correctly BananaRepublicFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  position: relative;
}

.emotion-0 .slick-slider {
  position: relative;
  display: block;
  box-sizing: border-box;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  -webkit-touch-callout: none;
  touch-action: pan-y;
  -webkit-tap-highlight-color: transparent;
  scroll-behavior: smooth;
}

.emotion-0 .slick-slider .slick-track,
.emotion-0 .slick-slider .slick-list {
  -webkit-transform: translate3d(0, 0, 0);
  -moz-transform: translate3d(0, 0, 0);
  -ms-transform: translate3d(0, 0, 0);
  transform: translate3d(0, 0, 0);
}

.emotion-0 .slick-list {
  position: relative;
  display: block;
  overflow-x: hidden;
  overflow-y: hidden;
  margin: 0;
  padding: 0;
}

.emotion-0 .slick-list:focus {
  outline: none;
}

.emotion-0 .slick-list.dragging {
  cursor: pointer;
  cursor: hand;
}

.emotion-0 .slick-track {
  position: relative;
  top: 0;
  left: 0;
  display: block;
  margin-left: auto;
  margin-right: auto;
}

.emotion-0 .slick-track:before,
.emotion-0 .slick-track:after {
  display: table;
  content: "";
}

.emotion-0 .slick-track:after {
  clear: both;
}

.emotion-0 .slick-slide {
  width: 48px!important;
  display: none;
  float: left;
  height: 100%;
  min-height: 1px;
}

.emotion-0 .slick-slide img {
  display: block;
}

.emotion-0 .slick-slide.slick-loading img {
  display: none;
}

.emotion-0 .slick-slide.dragging img {
  pointer-events: none;
}

.emotion-0 [dir="rtl"] .slick-slide {
  float: right;
}

.emotion-0 .slick-initialized .slick-slide,
.emotion-0 .slick-vertical .slick-slide {
  display: block;
}

.emotion-0 .slick-vertical .slick-slide {
  border: 1px solid transparent;
  height: auto;
}

.emotion-0 .slick-loading .slick-track,
.emotion-0 .slick-loading .slick-slide {
  visibility: hidden;
}

.emotion-0 .slick-loading .slick-list {
  background: #fff url("./ajax-loader.gif") center center no-repeat;
}

.emotion-0 .slick-arrow.slick-hidden {
  display: none;
}

.emotion-0 .slick-prev,
.emotion-0 .slick-next {
  font-size: 0;
  line-height: 0;
  position: absolute;
  top: 50%;
  display: block;
  padding: 0;
  cursor: pointer;
  color: transparent;
  border: none;
  outline: none;
  background: transparent;
}

.emotion-0 .slick-prev:hover,
.emotion-0 .slick-next:hover,
.emotion-0 .slick-prev:focus,
.emotion-0 .slick-next:focus {
  color: transparent;
  outline: none;
  background: transparent;
  opacity: 1;
}

.emotion-0 .slick-prev.slick-disabled,
.emotion-0 .slick-next.slick-disabled {
  opacity: 0.25;
}

.emotion-0 .slick-prev {
  left: -25px;
  -webkit-transform: translate(0, -50%) rotate(90deg);
  -moz-transform: translate(0, -50%) rotate(90deg);
  -ms-transform: translate(0, -50%) rotate(90deg);
  transform: translate(0, -50%) rotate(90deg);
}

.emotion-0 .slick-prev span {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-0 [dir="rtl"] .slick-prev {
  right: -25px;
  left: auto;
}

.emotion-0 .slick-next {
  right: -25px;
  -webkit-transform: translate(0, -50%) rotate(-90deg);
  -moz-transform: translate(0, -50%) rotate(-90deg);
  -ms-transform: translate(0, -50%) rotate(-90deg);
  transform: translate(0, -50%) rotate(-90deg);
}

.emotion-0 .slick-next span {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-0 [dir="rtl"] .slick-next {
  right: auto;
  left: -25px;
}

.emotion-0 .slick-dotted.slick-slider {
  margin-bottom: 30px;
}

.emotion-0 .slick-dots {
  position: absolute;
  bottom: -25px;
  display: block;
  width: 100%;
  padding: 0;
  margin: 0;
  list-style: none;
  text-align: center;
}

.emotion-0 .slick-dots li {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: 20px;
  height: 20px;
  margin: 0 5px;
  padding: 0;
  cursor: pointer;
}

.emotion-0 .slick-dots li.slick-active button:before {
  opacity: 0.75;
  color: black;
}

.emotion-0 .slick-dots li button {
  font-size: 0;
  line-height: 0;
  display: block;
  padding: 5px;
  cursor: pointer;
  color: transparent;
  border: 0;
  outline: none;
  background: transparent;
}

.emotion-0 .slick-dots li button:hover,
.emotion-0 .slick-dots li button:focus {
  outline: none;
}

.emotion-0 .slick-dots li button:hover:before,
.emotion-0 .slick-dots li button:focus:before,
.emotion-0 .slick-dots li button:hover:before,
.emotion-0 .slick-dots li button:focus:before {
  opacity: 1;
}

.emotion-0 .slick-dots li button:before {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 5px;
  height: 5px;
  content: "";
  opacity: 0.25;
  background-color: black;
  border-radius: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

<div
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`CarouselContainer snapshots renders default state correctly Gap 1`] = `
<DocumentFragment>
  .emotion-0 {
  position: relative;
}

.emotion-0 .slick-slider {
  position: relative;
  display: block;
  box-sizing: border-box;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  -webkit-touch-callout: none;
  touch-action: pan-y;
  -webkit-tap-highlight-color: transparent;
  scroll-behavior: smooth;
}

.emotion-0 .slick-slider .slick-track,
.emotion-0 .slick-slider .slick-list {
  -webkit-transform: translate3d(0, 0, 0);
  -moz-transform: translate3d(0, 0, 0);
  -ms-transform: translate3d(0, 0, 0);
  transform: translate3d(0, 0, 0);
}

.emotion-0 .slick-list {
  position: relative;
  display: block;
  overflow-x: hidden;
  overflow-y: hidden;
  margin: 0;
  padding: 0;
}

.emotion-0 .slick-list:focus {
  outline: none;
}

.emotion-0 .slick-list.dragging {
  cursor: pointer;
  cursor: hand;
}

.emotion-0 .slick-track {
  position: relative;
  top: 0;
  left: 0;
  display: block;
  margin-left: auto;
  margin-right: auto;
}

.emotion-0 .slick-track:before,
.emotion-0 .slick-track:after {
  display: table;
  content: "";
}

.emotion-0 .slick-track:after {
  clear: both;
}

.emotion-0 .slick-slide {
  width: 48px!important;
  display: none;
  float: left;
  height: 100%;
  min-height: 1px;
}

.emotion-0 .slick-slide img {
  display: block;
}

.emotion-0 .slick-slide.slick-loading img {
  display: none;
}

.emotion-0 .slick-slide.dragging img {
  pointer-events: none;
}

.emotion-0 [dir="rtl"] .slick-slide {
  float: right;
}

.emotion-0 .slick-initialized .slick-slide,
.emotion-0 .slick-vertical .slick-slide {
  display: block;
}

.emotion-0 .slick-vertical .slick-slide {
  border: 1px solid transparent;
  height: auto;
}

.emotion-0 .slick-loading .slick-track,
.emotion-0 .slick-loading .slick-slide {
  visibility: hidden;
}

.emotion-0 .slick-loading .slick-list {
  background: #fff url("./ajax-loader.gif") center center no-repeat;
}

.emotion-0 .slick-arrow.slick-hidden {
  display: none;
}

.emotion-0 .slick-prev,
.emotion-0 .slick-next {
  font-size: 0;
  line-height: 0;
  position: absolute;
  top: 50%;
  display: block;
  padding: 0;
  cursor: pointer;
  color: transparent;
  border: none;
  outline: none;
  background: transparent;
}

.emotion-0 .slick-prev:hover,
.emotion-0 .slick-next:hover,
.emotion-0 .slick-prev:focus,
.emotion-0 .slick-next:focus {
  color: transparent;
  outline: none;
  background: transparent;
  opacity: 1;
}

.emotion-0 .slick-prev.slick-disabled,
.emotion-0 .slick-next.slick-disabled {
  opacity: 0.25;
}

.emotion-0 .slick-prev {
  left: -25px;
  -webkit-transform: translate(0, -50%) rotate(90deg);
  -moz-transform: translate(0, -50%) rotate(90deg);
  -ms-transform: translate(0, -50%) rotate(90deg);
  transform: translate(0, -50%) rotate(90deg);
}

.emotion-0 .slick-prev span {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-0 [dir="rtl"] .slick-prev {
  right: -25px;
  left: auto;
}

.emotion-0 .slick-next {
  right: -25px;
  -webkit-transform: translate(0, -50%) rotate(-90deg);
  -moz-transform: translate(0, -50%) rotate(-90deg);
  -ms-transform: translate(0, -50%) rotate(-90deg);
  transform: translate(0, -50%) rotate(-90deg);
}

.emotion-0 .slick-next span {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-0 [dir="rtl"] .slick-next {
  right: auto;
  left: -25px;
}

.emotion-0 .slick-dotted.slick-slider {
  margin-bottom: 30px;
}

.emotion-0 .slick-dots {
  position: absolute;
  bottom: -25px;
  display: block;
  width: 100%;
  padding: 0;
  margin: 0;
  list-style: none;
  text-align: center;
}

.emotion-0 .slick-dots li {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: 20px;
  height: 20px;
  margin: 0 5px;
  padding: 0;
  cursor: pointer;
}

.emotion-0 .slick-dots li.slick-active button:before {
  opacity: 0.75;
  color: black;
}

.emotion-0 .slick-dots li button {
  font-size: 0;
  line-height: 0;
  display: block;
  padding: 5px;
  cursor: pointer;
  color: transparent;
  border: 0;
  outline: none;
  background: transparent;
}

.emotion-0 .slick-dots li button:hover,
.emotion-0 .slick-dots li button:focus {
  outline: none;
}

.emotion-0 .slick-dots li button:hover:before,
.emotion-0 .slick-dots li button:focus:before,
.emotion-0 .slick-dots li button:hover:before,
.emotion-0 .slick-dots li button:focus:before {
  opacity: 1;
}

.emotion-0 .slick-dots li button:before {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 5px;
  height: 5px;
  content: "";
  opacity: 0.25;
  background-color: black;
  border-radius: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

<div
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`CarouselContainer snapshots renders default state correctly Gap in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  position: relative;
}

.emotion-0 .slick-slider {
  position: relative;
  display: block;
  box-sizing: border-box;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  -webkit-touch-callout: none;
  touch-action: pan-y;
  -webkit-tap-highlight-color: transparent;
  scroll-behavior: smooth;
}

.emotion-0 .slick-slider .slick-track,
.emotion-0 .slick-slider .slick-list {
  -webkit-transform: translate3d(0, 0, 0);
  -moz-transform: translate3d(0, 0, 0);
  -ms-transform: translate3d(0, 0, 0);
  transform: translate3d(0, 0, 0);
}

.emotion-0 .slick-list {
  position: relative;
  display: block;
  overflow-x: hidden;
  overflow-y: hidden;
  margin: 0;
  padding: 0;
}

.emotion-0 .slick-list:focus {
  outline: none;
}

.emotion-0 .slick-list.dragging {
  cursor: pointer;
  cursor: hand;
}

.emotion-0 .slick-track {
  position: relative;
  top: 0;
  left: 0;
  display: block;
  margin-left: auto;
  margin-right: auto;
}

.emotion-0 .slick-track:before,
.emotion-0 .slick-track:after {
  display: table;
  content: "";
}

.emotion-0 .slick-track:after {
  clear: both;
}

.emotion-0 .slick-slide {
  width: 48px!important;
  display: none;
  float: left;
  height: 100%;
  min-height: 1px;
}

.emotion-0 .slick-slide img {
  display: block;
}

.emotion-0 .slick-slide.slick-loading img {
  display: none;
}

.emotion-0 .slick-slide.dragging img {
  pointer-events: none;
}

.emotion-0 [dir="rtl"] .slick-slide {
  float: right;
}

.emotion-0 .slick-initialized .slick-slide,
.emotion-0 .slick-vertical .slick-slide {
  display: block;
}

.emotion-0 .slick-vertical .slick-slide {
  border: 1px solid transparent;
  height: auto;
}

.emotion-0 .slick-loading .slick-track,
.emotion-0 .slick-loading .slick-slide {
  visibility: hidden;
}

.emotion-0 .slick-loading .slick-list {
  background: #fff url("./ajax-loader.gif") center center no-repeat;
}

.emotion-0 .slick-arrow.slick-hidden {
  display: none;
}

.emotion-0 .slick-prev,
.emotion-0 .slick-next {
  font-size: 0;
  line-height: 0;
  position: absolute;
  top: 50%;
  display: block;
  padding: 0;
  cursor: pointer;
  color: transparent;
  border: none;
  outline: none;
  background: transparent;
}

.emotion-0 .slick-prev:hover,
.emotion-0 .slick-next:hover,
.emotion-0 .slick-prev:focus,
.emotion-0 .slick-next:focus {
  color: transparent;
  outline: none;
  background: transparent;
  opacity: 1;
}

.emotion-0 .slick-prev.slick-disabled,
.emotion-0 .slick-next.slick-disabled {
  opacity: 0.25;
}

.emotion-0 .slick-prev {
  left: -25px;
  -webkit-transform: translate(0, -50%) rotate(90deg);
  -moz-transform: translate(0, -50%) rotate(90deg);
  -ms-transform: translate(0, -50%) rotate(90deg);
  transform: translate(0, -50%) rotate(90deg);
}

.emotion-0 .slick-prev span {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-0 [dir="rtl"] .slick-prev {
  right: -25px;
  left: auto;
}

.emotion-0 .slick-next {
  right: -25px;
  -webkit-transform: translate(0, -50%) rotate(-90deg);
  -moz-transform: translate(0, -50%) rotate(-90deg);
  -ms-transform: translate(0, -50%) rotate(-90deg);
  transform: translate(0, -50%) rotate(-90deg);
}

.emotion-0 .slick-next span {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-0 [dir="rtl"] .slick-next {
  right: auto;
  left: -25px;
}

.emotion-0 .slick-dotted.slick-slider {
  margin-bottom: 30px;
}

.emotion-0 .slick-dots {
  position: absolute;
  bottom: -25px;
  display: block;
  width: 100%;
  padding: 0;
  margin: 0;
  list-style: none;
  text-align: center;
}

.emotion-0 .slick-dots li {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: 20px;
  height: 20px;
  margin: 0 5px;
  padding: 0;
  cursor: pointer;
}

.emotion-0 .slick-dots li.slick-active button:before {
  opacity: 0.75;
  color: black;
}

.emotion-0 .slick-dots li button {
  font-size: 0;
  line-height: 0;
  display: block;
  padding: 5px;
  cursor: pointer;
  color: transparent;
  border: 0;
  outline: none;
  background: transparent;
}

.emotion-0 .slick-dots li button:hover,
.emotion-0 .slick-dots li button:focus {
  outline: none;
}

.emotion-0 .slick-dots li button:hover:before,
.emotion-0 .slick-dots li button:focus:before,
.emotion-0 .slick-dots li button:hover:before,
.emotion-0 .slick-dots li button:focus:before {
  opacity: 1;
}

.emotion-0 .slick-dots li button:before {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 5px;
  height: 5px;
  content: "";
  opacity: 0.25;
  background-color: black;
  border-radius: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

<div
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`CarouselContainer snapshots renders default state correctly GapFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  position: relative;
}

.emotion-0 .slick-slider {
  position: relative;
  display: block;
  box-sizing: border-box;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  -webkit-touch-callout: none;
  touch-action: pan-y;
  -webkit-tap-highlight-color: transparent;
  scroll-behavior: smooth;
}

.emotion-0 .slick-slider .slick-track,
.emotion-0 .slick-slider .slick-list {
  -webkit-transform: translate3d(0, 0, 0);
  -moz-transform: translate3d(0, 0, 0);
  -ms-transform: translate3d(0, 0, 0);
  transform: translate3d(0, 0, 0);
}

.emotion-0 .slick-list {
  position: relative;
  display: block;
  overflow-x: hidden;
  overflow-y: hidden;
  margin: 0;
  padding: 0;
}

.emotion-0 .slick-list:focus {
  outline: none;
}

.emotion-0 .slick-list.dragging {
  cursor: pointer;
  cursor: hand;
}

.emotion-0 .slick-track {
  position: relative;
  top: 0;
  left: 0;
  display: block;
  margin-left: auto;
  margin-right: auto;
}

.emotion-0 .slick-track:before,
.emotion-0 .slick-track:after {
  display: table;
  content: "";
}

.emotion-0 .slick-track:after {
  clear: both;
}

.emotion-0 .slick-slide {
  width: 48px!important;
  display: none;
  float: left;
  height: 100%;
  min-height: 1px;
}

.emotion-0 .slick-slide img {
  display: block;
}

.emotion-0 .slick-slide.slick-loading img {
  display: none;
}

.emotion-0 .slick-slide.dragging img {
  pointer-events: none;
}

.emotion-0 [dir="rtl"] .slick-slide {
  float: right;
}

.emotion-0 .slick-initialized .slick-slide,
.emotion-0 .slick-vertical .slick-slide {
  display: block;
}

.emotion-0 .slick-vertical .slick-slide {
  border: 1px solid transparent;
  height: auto;
}

.emotion-0 .slick-loading .slick-track,
.emotion-0 .slick-loading .slick-slide {
  visibility: hidden;
}

.emotion-0 .slick-loading .slick-list {
  background: #fff url("./ajax-loader.gif") center center no-repeat;
}

.emotion-0 .slick-arrow.slick-hidden {
  display: none;
}

.emotion-0 .slick-prev,
.emotion-0 .slick-next {
  font-size: 0;
  line-height: 0;
  position: absolute;
  top: 50%;
  display: block;
  padding: 0;
  cursor: pointer;
  color: transparent;
  border: none;
  outline: none;
  background: transparent;
}

.emotion-0 .slick-prev:hover,
.emotion-0 .slick-next:hover,
.emotion-0 .slick-prev:focus,
.emotion-0 .slick-next:focus {
  color: transparent;
  outline: none;
  background: transparent;
  opacity: 1;
}

.emotion-0 .slick-prev.slick-disabled,
.emotion-0 .slick-next.slick-disabled {
  opacity: 0.25;
}

.emotion-0 .slick-prev {
  left: -25px;
  -webkit-transform: translate(0, -50%) rotate(90deg);
  -moz-transform: translate(0, -50%) rotate(90deg);
  -ms-transform: translate(0, -50%) rotate(90deg);
  transform: translate(0, -50%) rotate(90deg);
}

.emotion-0 .slick-prev span {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-0 [dir="rtl"] .slick-prev {
  right: -25px;
  left: auto;
}

.emotion-0 .slick-next {
  right: -25px;
  -webkit-transform: translate(0, -50%) rotate(-90deg);
  -moz-transform: translate(0, -50%) rotate(-90deg);
  -ms-transform: translate(0, -50%) rotate(-90deg);
  transform: translate(0, -50%) rotate(-90deg);
}

.emotion-0 .slick-next span {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-0 [dir="rtl"] .slick-next {
  right: auto;
  left: -25px;
}

.emotion-0 .slick-dotted.slick-slider {
  margin-bottom: 30px;
}

.emotion-0 .slick-dots {
  position: absolute;
  bottom: -25px;
  display: block;
  width: 100%;
  padding: 0;
  margin: 0;
  list-style: none;
  text-align: center;
}

.emotion-0 .slick-dots li {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: 20px;
  height: 20px;
  margin: 0 5px;
  padding: 0;
  cursor: pointer;
}

.emotion-0 .slick-dots li.slick-active button:before {
  opacity: 0.75;
  color: black;
}

.emotion-0 .slick-dots li button {
  font-size: 0;
  line-height: 0;
  display: block;
  padding: 5px;
  cursor: pointer;
  color: transparent;
  border: 0;
  outline: none;
  background: transparent;
}

.emotion-0 .slick-dots li button:hover,
.emotion-0 .slick-dots li button:focus {
  outline: none;
}

.emotion-0 .slick-dots li button:hover:before,
.emotion-0 .slick-dots li button:focus:before,
.emotion-0 .slick-dots li button:hover:before,
.emotion-0 .slick-dots li button:focus:before {
  opacity: 1;
}

.emotion-0 .slick-dots li button:before {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 5px;
  height: 5px;
  content: "";
  opacity: 0.25;
  background-color: black;
  border-radius: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

<div
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`CarouselContainer snapshots renders default state correctly GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  position: relative;
}

.emotion-0 .slick-slider {
  position: relative;
  display: block;
  box-sizing: border-box;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  -webkit-touch-callout: none;
  touch-action: pan-y;
  -webkit-tap-highlight-color: transparent;
  scroll-behavior: smooth;
}

.emotion-0 .slick-slider .slick-track,
.emotion-0 .slick-slider .slick-list {
  -webkit-transform: translate3d(0, 0, 0);
  -moz-transform: translate3d(0, 0, 0);
  -ms-transform: translate3d(0, 0, 0);
  transform: translate3d(0, 0, 0);
}

.emotion-0 .slick-list {
  position: relative;
  display: block;
  overflow-x: hidden;
  overflow-y: hidden;
  margin: 0;
  padding: 0;
}

.emotion-0 .slick-list:focus {
  outline: none;
}

.emotion-0 .slick-list.dragging {
  cursor: pointer;
  cursor: hand;
}

.emotion-0 .slick-track {
  position: relative;
  top: 0;
  left: 0;
  display: block;
  margin-left: auto;
  margin-right: auto;
}

.emotion-0 .slick-track:before,
.emotion-0 .slick-track:after {
  display: table;
  content: "";
}

.emotion-0 .slick-track:after {
  clear: both;
}

.emotion-0 .slick-slide {
  width: 48px!important;
  display: none;
  float: left;
  height: 100%;
  min-height: 1px;
}

.emotion-0 .slick-slide img {
  display: block;
}

.emotion-0 .slick-slide.slick-loading img {
  display: none;
}

.emotion-0 .slick-slide.dragging img {
  pointer-events: none;
}

.emotion-0 [dir="rtl"] .slick-slide {
  float: right;
}

.emotion-0 .slick-initialized .slick-slide,
.emotion-0 .slick-vertical .slick-slide {
  display: block;
}

.emotion-0 .slick-vertical .slick-slide {
  border: 1px solid transparent;
  height: auto;
}

.emotion-0 .slick-loading .slick-track,
.emotion-0 .slick-loading .slick-slide {
  visibility: hidden;
}

.emotion-0 .slick-loading .slick-list {
  background: #fff url("./ajax-loader.gif") center center no-repeat;
}

.emotion-0 .slick-arrow.slick-hidden {
  display: none;
}

.emotion-0 .slick-prev,
.emotion-0 .slick-next {
  font-size: 0;
  line-height: 0;
  position: absolute;
  top: 50%;
  display: block;
  padding: 0;
  cursor: pointer;
  color: transparent;
  border: none;
  outline: none;
  background: transparent;
}

.emotion-0 .slick-prev:hover,
.emotion-0 .slick-next:hover,
.emotion-0 .slick-prev:focus,
.emotion-0 .slick-next:focus {
  color: transparent;
  outline: none;
  background: transparent;
  opacity: 1;
}

.emotion-0 .slick-prev.slick-disabled,
.emotion-0 .slick-next.slick-disabled {
  opacity: 0.25;
}

.emotion-0 .slick-prev {
  left: -25px;
  -webkit-transform: translate(0, -50%) rotate(90deg);
  -moz-transform: translate(0, -50%) rotate(90deg);
  -ms-transform: translate(0, -50%) rotate(90deg);
  transform: translate(0, -50%) rotate(90deg);
}

.emotion-0 .slick-prev span {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-0 [dir="rtl"] .slick-prev {
  right: -25px;
  left: auto;
}

.emotion-0 .slick-next {
  right: -25px;
  -webkit-transform: translate(0, -50%) rotate(-90deg);
  -moz-transform: translate(0, -50%) rotate(-90deg);
  -ms-transform: translate(0, -50%) rotate(-90deg);
  transform: translate(0, -50%) rotate(-90deg);
}

.emotion-0 .slick-next span {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-0 [dir="rtl"] .slick-next {
  right: auto;
  left: -25px;
}

.emotion-0 .slick-dotted.slick-slider {
  margin-bottom: 30px;
}

.emotion-0 .slick-dots {
  position: absolute;
  bottom: -25px;
  display: block;
  width: 100%;
  padding: 0;
  margin: 0;
  list-style: none;
  text-align: center;
}

.emotion-0 .slick-dots li {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: 20px;
  height: 20px;
  margin: 0 5px;
  padding: 0;
  cursor: pointer;
}

.emotion-0 .slick-dots li.slick-active button:before {
  opacity: 0.75;
  color: black;
}

.emotion-0 .slick-dots li button {
  font-size: 0;
  line-height: 0;
  display: block;
  padding: 5px;
  cursor: pointer;
  color: transparent;
  border: 0;
  outline: none;
  background: transparent;
}

.emotion-0 .slick-dots li button:hover,
.emotion-0 .slick-dots li button:focus {
  outline: none;
}

.emotion-0 .slick-dots li button:hover:before,
.emotion-0 .slick-dots li button:focus:before,
.emotion-0 .slick-dots li button:hover:before,
.emotion-0 .slick-dots li button:focus:before {
  opacity: 1;
}

.emotion-0 .slick-dots li button:before {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 5px;
  height: 5px;
  content: "";
  opacity: 0.25;
  background-color: black;
  border-radius: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

<div
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`CarouselContainer snapshots renders default state correctly OldNavy 1`] = `
<DocumentFragment>
  .emotion-0 {
  position: relative;
}

.emotion-0 .slick-slider {
  position: relative;
  display: block;
  box-sizing: border-box;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  -webkit-touch-callout: none;
  touch-action: pan-y;
  -webkit-tap-highlight-color: transparent;
  scroll-behavior: smooth;
}

.emotion-0 .slick-slider .slick-track,
.emotion-0 .slick-slider .slick-list {
  -webkit-transform: translate3d(0, 0, 0);
  -moz-transform: translate3d(0, 0, 0);
  -ms-transform: translate3d(0, 0, 0);
  transform: translate3d(0, 0, 0);
}

.emotion-0 .slick-list {
  position: relative;
  display: block;
  overflow-x: hidden;
  overflow-y: hidden;
  margin: 0;
  padding: 0;
}

.emotion-0 .slick-list:focus {
  outline: none;
}

.emotion-0 .slick-list.dragging {
  cursor: pointer;
  cursor: hand;
}

.emotion-0 .slick-track {
  position: relative;
  top: 0;
  left: 0;
  display: block;
  margin-left: auto;
  margin-right: auto;
}

.emotion-0 .slick-track:before,
.emotion-0 .slick-track:after {
  display: table;
  content: "";
}

.emotion-0 .slick-track:after {
  clear: both;
}

.emotion-0 .slick-slide {
  width: 48px!important;
  display: none;
  float: left;
  height: 100%;
  min-height: 1px;
}

.emotion-0 .slick-slide img {
  display: block;
}

.emotion-0 .slick-slide.slick-loading img {
  display: none;
}

.emotion-0 .slick-slide.dragging img {
  pointer-events: none;
}

.emotion-0 [dir="rtl"] .slick-slide {
  float: right;
}

.emotion-0 .slick-initialized .slick-slide,
.emotion-0 .slick-vertical .slick-slide {
  display: block;
}

.emotion-0 .slick-vertical .slick-slide {
  border: 1px solid transparent;
  height: auto;
}

.emotion-0 .slick-loading .slick-track,
.emotion-0 .slick-loading .slick-slide {
  visibility: hidden;
}

.emotion-0 .slick-loading .slick-list {
  background: #fff url("./ajax-loader.gif") center center no-repeat;
}

.emotion-0 .slick-arrow.slick-hidden {
  display: none;
}

.emotion-0 .slick-prev,
.emotion-0 .slick-next {
  font-size: 0;
  line-height: 0;
  position: absolute;
  top: 50%;
  display: block;
  padding: 0;
  cursor: pointer;
  color: transparent;
  border: none;
  outline: none;
  background: transparent;
}

.emotion-0 .slick-prev:hover,
.emotion-0 .slick-next:hover,
.emotion-0 .slick-prev:focus,
.emotion-0 .slick-next:focus {
  color: transparent;
  outline: none;
  background: transparent;
  opacity: 1;
}

.emotion-0 .slick-prev.slick-disabled,
.emotion-0 .slick-next.slick-disabled {
  opacity: 0.25;
}

.emotion-0 .slick-prev {
  left: -25px;
  -webkit-transform: translate(0, -50%) rotate(90deg);
  -moz-transform: translate(0, -50%) rotate(90deg);
  -ms-transform: translate(0, -50%) rotate(90deg);
  transform: translate(0, -50%) rotate(90deg);
}

.emotion-0 .slick-prev span {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-0 [dir="rtl"] .slick-prev {
  right: -25px;
  left: auto;
}

.emotion-0 .slick-next {
  right: -25px;
  -webkit-transform: translate(0, -50%) rotate(-90deg);
  -moz-transform: translate(0, -50%) rotate(-90deg);
  -ms-transform: translate(0, -50%) rotate(-90deg);
  transform: translate(0, -50%) rotate(-90deg);
}

.emotion-0 .slick-next span {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-0 [dir="rtl"] .slick-next {
  right: auto;
  left: -25px;
}

.emotion-0 .slick-dotted.slick-slider {
  margin-bottom: 30px;
}

.emotion-0 .slick-dots {
  position: absolute;
  bottom: -25px;
  display: block;
  width: 100%;
  padding: 0;
  margin: 0;
  list-style: none;
  text-align: center;
}

.emotion-0 .slick-dots li {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: 20px;
  height: 20px;
  margin: 0 5px;
  padding: 0;
  cursor: pointer;
}

.emotion-0 .slick-dots li.slick-active button:before {
  opacity: 0.75;
  color: black;
}

.emotion-0 .slick-dots li button {
  font-size: 0;
  line-height: 0;
  display: block;
  padding: 5px;
  cursor: pointer;
  color: transparent;
  border: 0;
  outline: none;
  background: transparent;
}

.emotion-0 .slick-dots li button:hover,
.emotion-0 .slick-dots li button:focus {
  outline: none;
}

.emotion-0 .slick-dots li button:hover:before,
.emotion-0 .slick-dots li button:focus:before,
.emotion-0 .slick-dots li button:hover:before,
.emotion-0 .slick-dots li button:focus:before {
  opacity: 1;
}

.emotion-0 .slick-dots li button:before {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 5px;
  height: 5px;
  content: "";
  opacity: 0.25;
  background-color: black;
  border-radius: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

<div
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`CarouselContainer snapshots renders default state correctly OldNavy in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  position: relative;
}

.emotion-0 .slick-slider {
  position: relative;
  display: block;
  box-sizing: border-box;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  -webkit-touch-callout: none;
  touch-action: pan-y;
  -webkit-tap-highlight-color: transparent;
  scroll-behavior: smooth;
}

.emotion-0 .slick-slider .slick-track,
.emotion-0 .slick-slider .slick-list {
  -webkit-transform: translate3d(0, 0, 0);
  -moz-transform: translate3d(0, 0, 0);
  -ms-transform: translate3d(0, 0, 0);
  transform: translate3d(0, 0, 0);
}

.emotion-0 .slick-list {
  position: relative;
  display: block;
  overflow-x: hidden;
  overflow-y: hidden;
  margin: 0;
  padding: 0;
}

.emotion-0 .slick-list:focus {
  outline: none;
}

.emotion-0 .slick-list.dragging {
  cursor: pointer;
  cursor: hand;
}

.emotion-0 .slick-track {
  position: relative;
  top: 0;
  left: 0;
  display: block;
  margin-left: auto;
  margin-right: auto;
}

.emotion-0 .slick-track:before,
.emotion-0 .slick-track:after {
  display: table;
  content: "";
}

.emotion-0 .slick-track:after {
  clear: both;
}

.emotion-0 .slick-slide {
  width: 48px!important;
  display: none;
  float: left;
  height: 100%;
  min-height: 1px;
}

.emotion-0 .slick-slide img {
  display: block;
}

.emotion-0 .slick-slide.slick-loading img {
  display: none;
}

.emotion-0 .slick-slide.dragging img {
  pointer-events: none;
}

.emotion-0 [dir="rtl"] .slick-slide {
  float: right;
}

.emotion-0 .slick-initialized .slick-slide,
.emotion-0 .slick-vertical .slick-slide {
  display: block;
}

.emotion-0 .slick-vertical .slick-slide {
  border: 1px solid transparent;
  height: auto;
}

.emotion-0 .slick-loading .slick-track,
.emotion-0 .slick-loading .slick-slide {
  visibility: hidden;
}

.emotion-0 .slick-loading .slick-list {
  background: #fff url("./ajax-loader.gif") center center no-repeat;
}

.emotion-0 .slick-arrow.slick-hidden {
  display: none;
}

.emotion-0 .slick-prev,
.emotion-0 .slick-next {
  font-size: 0;
  line-height: 0;
  position: absolute;
  top: 50%;
  display: block;
  padding: 0;
  cursor: pointer;
  color: transparent;
  border: none;
  outline: none;
  background: transparent;
}

.emotion-0 .slick-prev:hover,
.emotion-0 .slick-next:hover,
.emotion-0 .slick-prev:focus,
.emotion-0 .slick-next:focus {
  color: transparent;
  outline: none;
  background: transparent;
  opacity: 1;
}

.emotion-0 .slick-prev.slick-disabled,
.emotion-0 .slick-next.slick-disabled {
  opacity: 0.25;
}

.emotion-0 .slick-prev {
  left: -25px;
  -webkit-transform: translate(0, -50%) rotate(90deg);
  -moz-transform: translate(0, -50%) rotate(90deg);
  -ms-transform: translate(0, -50%) rotate(90deg);
  transform: translate(0, -50%) rotate(90deg);
}

.emotion-0 .slick-prev span {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-0 [dir="rtl"] .slick-prev {
  right: -25px;
  left: auto;
}

.emotion-0 .slick-next {
  right: -25px;
  -webkit-transform: translate(0, -50%) rotate(-90deg);
  -moz-transform: translate(0, -50%) rotate(-90deg);
  -ms-transform: translate(0, -50%) rotate(-90deg);
  transform: translate(0, -50%) rotate(-90deg);
}

.emotion-0 .slick-next span {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-0 [dir="rtl"] .slick-next {
  right: auto;
  left: -25px;
}

.emotion-0 .slick-dotted.slick-slider {
  margin-bottom: 30px;
}

.emotion-0 .slick-dots {
  position: absolute;
  bottom: -25px;
  display: block;
  width: 100%;
  padding: 0;
  margin: 0;
  list-style: none;
  text-align: center;
}

.emotion-0 .slick-dots li {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: 20px;
  height: 20px;
  margin: 0 5px;
  padding: 0;
  cursor: pointer;
}

.emotion-0 .slick-dots li.slick-active button:before {
  opacity: 0.75;
  color: black;
}

.emotion-0 .slick-dots li button {
  font-size: 0;
  line-height: 0;
  display: block;
  padding: 5px;
  cursor: pointer;
  color: transparent;
  border: 0;
  outline: none;
  background: transparent;
}

.emotion-0 .slick-dots li button:hover,
.emotion-0 .slick-dots li button:focus {
  outline: none;
}

.emotion-0 .slick-dots li button:hover:before,
.emotion-0 .slick-dots li button:focus:before,
.emotion-0 .slick-dots li button:hover:before,
.emotion-0 .slick-dots li button:focus:before {
  opacity: 1;
}

.emotion-0 .slick-dots li button:before {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 5px;
  height: 5px;
  content: "";
  opacity: 0.25;
  background-color: black;
  border-radius: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

<div
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`CarouselContainer snapshots renders fade state correctly Athleta 1`] = `
<DocumentFragment>
  .emotion-0 {
  position: relative;
}

.emotion-0 .slick-slide {
  pointer-events: none;
}

.emotion-0 .slick-active {
  pointer-events: auto;
}

.emotion-0 .slick-slider {
  position: relative;
  display: block;
  box-sizing: border-box;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  -webkit-touch-callout: none;
  touch-action: pan-y;
  -webkit-tap-highlight-color: transparent;
  scroll-behavior: smooth;
}

.emotion-0 .slick-slider .slick-track,
.emotion-0 .slick-slider .slick-list {
  -webkit-transform: translate3d(0, 0, 0);
  -moz-transform: translate3d(0, 0, 0);
  -ms-transform: translate3d(0, 0, 0);
  transform: translate3d(0, 0, 0);
}

.emotion-0 .slick-list {
  position: relative;
  display: block;
  overflow-x: hidden;
  overflow-y: hidden;
  margin: 0;
  padding: 0;
}

.emotion-0 .slick-list:focus {
  outline: none;
}

.emotion-0 .slick-list.dragging {
  cursor: pointer;
  cursor: hand;
}

.emotion-0 .slick-track {
  position: relative;
  top: 0;
  left: 0;
  display: block;
  margin-left: auto;
  margin-right: auto;
}

.emotion-0 .slick-track:before,
.emotion-0 .slick-track:after {
  display: table;
  content: "";
}

.emotion-0 .slick-track:after {
  clear: both;
}

.emotion-0 .slick-slide {
  width: 48px!important;
  display: none;
  float: left;
  height: 100%;
  min-height: 1px;
}

.emotion-0 .slick-slide img {
  display: block;
}

.emotion-0 .slick-slide.slick-loading img {
  display: none;
}

.emotion-0 .slick-slide.dragging img {
  pointer-events: none;
}

.emotion-0 [dir="rtl"] .slick-slide {
  float: right;
}

.emotion-0 .slick-initialized .slick-slide,
.emotion-0 .slick-vertical .slick-slide {
  display: block;
}

.emotion-0 .slick-vertical .slick-slide {
  border: 1px solid transparent;
  height: auto;
}

.emotion-0 .slick-loading .slick-track,
.emotion-0 .slick-loading .slick-slide {
  visibility: hidden;
}

.emotion-0 .slick-loading .slick-list {
  background: #fff url("./ajax-loader.gif") center center no-repeat;
}

.emotion-0 .slick-arrow.slick-hidden {
  display: none;
}

.emotion-0 .slick-prev,
.emotion-0 .slick-next {
  font-size: 0;
  line-height: 0;
  position: absolute;
  top: 50%;
  display: block;
  padding: 0;
  cursor: pointer;
  color: transparent;
  border: none;
  outline: none;
  background: transparent;
}

.emotion-0 .slick-prev:hover,
.emotion-0 .slick-next:hover,
.emotion-0 .slick-prev:focus,
.emotion-0 .slick-next:focus {
  color: transparent;
  outline: none;
  background: transparent;
  opacity: 1;
}

.emotion-0 .slick-prev.slick-disabled,
.emotion-0 .slick-next.slick-disabled {
  opacity: 0.25;
}

.emotion-0 .slick-prev {
  left: -25px;
  -webkit-transform: translate(0, -50%) rotate(90deg);
  -moz-transform: translate(0, -50%) rotate(90deg);
  -ms-transform: translate(0, -50%) rotate(90deg);
  transform: translate(0, -50%) rotate(90deg);
}

.emotion-0 .slick-prev span {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-0 [dir="rtl"] .slick-prev {
  right: -25px;
  left: auto;
}

.emotion-0 .slick-next {
  right: -25px;
  -webkit-transform: translate(0, -50%) rotate(-90deg);
  -moz-transform: translate(0, -50%) rotate(-90deg);
  -ms-transform: translate(0, -50%) rotate(-90deg);
  transform: translate(0, -50%) rotate(-90deg);
}

.emotion-0 .slick-next span {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-0 [dir="rtl"] .slick-next {
  right: auto;
  left: -25px;
}

.emotion-0 .slick-dotted.slick-slider {
  margin-bottom: 30px;
}

.emotion-0 .slick-dots {
  position: absolute;
  bottom: -25px;
  display: block;
  width: 100%;
  padding: 0;
  margin: 0;
  list-style: none;
  text-align: center;
}

.emotion-0 .slick-dots li {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: 20px;
  height: 20px;
  margin: 0 5px;
  padding: 0;
  cursor: pointer;
}

.emotion-0 .slick-dots li.slick-active button:before {
  opacity: 0.75;
  color: black;
}

.emotion-0 .slick-dots li button {
  font-size: 0;
  line-height: 0;
  display: block;
  padding: 5px;
  cursor: pointer;
  color: transparent;
  border: 0;
  outline: none;
  background: transparent;
}

.emotion-0 .slick-dots li button:hover,
.emotion-0 .slick-dots li button:focus {
  outline: none;
}

.emotion-0 .slick-dots li button:hover:before,
.emotion-0 .slick-dots li button:focus:before,
.emotion-0 .slick-dots li button:hover:before,
.emotion-0 .slick-dots li button:focus:before {
  opacity: 1;
}

.emotion-0 .slick-dots li button:before {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 5px;
  height: 5px;
  content: "";
  opacity: 0.25;
  background-color: black;
  border-radius: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

<div
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`CarouselContainer snapshots renders fade state correctly Athleta in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  position: relative;
}

.emotion-0 .slick-slide {
  pointer-events: none;
}

.emotion-0 .slick-active {
  pointer-events: auto;
}

.emotion-0 .slick-slider {
  position: relative;
  display: block;
  box-sizing: border-box;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  -webkit-touch-callout: none;
  touch-action: pan-y;
  -webkit-tap-highlight-color: transparent;
  scroll-behavior: smooth;
}

.emotion-0 .slick-slider .slick-track,
.emotion-0 .slick-slider .slick-list {
  -webkit-transform: translate3d(0, 0, 0);
  -moz-transform: translate3d(0, 0, 0);
  -ms-transform: translate3d(0, 0, 0);
  transform: translate3d(0, 0, 0);
}

.emotion-0 .slick-list {
  position: relative;
  display: block;
  overflow-x: hidden;
  overflow-y: hidden;
  margin: 0;
  padding: 0;
}

.emotion-0 .slick-list:focus {
  outline: none;
}

.emotion-0 .slick-list.dragging {
  cursor: pointer;
  cursor: hand;
}

.emotion-0 .slick-track {
  position: relative;
  top: 0;
  left: 0;
  display: block;
  margin-left: auto;
  margin-right: auto;
}

.emotion-0 .slick-track:before,
.emotion-0 .slick-track:after {
  display: table;
  content: "";
}

.emotion-0 .slick-track:after {
  clear: both;
}

.emotion-0 .slick-slide {
  width: 48px!important;
  display: none;
  float: left;
  height: 100%;
  min-height: 1px;
}

.emotion-0 .slick-slide img {
  display: block;
}

.emotion-0 .slick-slide.slick-loading img {
  display: none;
}

.emotion-0 .slick-slide.dragging img {
  pointer-events: none;
}

.emotion-0 [dir="rtl"] .slick-slide {
  float: right;
}

.emotion-0 .slick-initialized .slick-slide,
.emotion-0 .slick-vertical .slick-slide {
  display: block;
}

.emotion-0 .slick-vertical .slick-slide {
  border: 1px solid transparent;
  height: auto;
}

.emotion-0 .slick-loading .slick-track,
.emotion-0 .slick-loading .slick-slide {
  visibility: hidden;
}

.emotion-0 .slick-loading .slick-list {
  background: #fff url("./ajax-loader.gif") center center no-repeat;
}

.emotion-0 .slick-arrow.slick-hidden {
  display: none;
}

.emotion-0 .slick-prev,
.emotion-0 .slick-next {
  font-size: 0;
  line-height: 0;
  position: absolute;
  top: 50%;
  display: block;
  padding: 0;
  cursor: pointer;
  color: transparent;
  border: none;
  outline: none;
  background: transparent;
}

.emotion-0 .slick-prev:hover,
.emotion-0 .slick-next:hover,
.emotion-0 .slick-prev:focus,
.emotion-0 .slick-next:focus {
  color: transparent;
  outline: none;
  background: transparent;
  opacity: 1;
}

.emotion-0 .slick-prev.slick-disabled,
.emotion-0 .slick-next.slick-disabled {
  opacity: 0.25;
}

.emotion-0 .slick-prev {
  left: -25px;
  -webkit-transform: translate(0, -50%) rotate(90deg);
  -moz-transform: translate(0, -50%) rotate(90deg);
  -ms-transform: translate(0, -50%) rotate(90deg);
  transform: translate(0, -50%) rotate(90deg);
}

.emotion-0 .slick-prev span {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-0 [dir="rtl"] .slick-prev {
  right: -25px;
  left: auto;
}

.emotion-0 .slick-next {
  right: -25px;
  -webkit-transform: translate(0, -50%) rotate(-90deg);
  -moz-transform: translate(0, -50%) rotate(-90deg);
  -ms-transform: translate(0, -50%) rotate(-90deg);
  transform: translate(0, -50%) rotate(-90deg);
}

.emotion-0 .slick-next span {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-0 [dir="rtl"] .slick-next {
  right: auto;
  left: -25px;
}

.emotion-0 .slick-dotted.slick-slider {
  margin-bottom: 30px;
}

.emotion-0 .slick-dots {
  position: absolute;
  bottom: -25px;
  display: block;
  width: 100%;
  padding: 0;
  margin: 0;
  list-style: none;
  text-align: center;
}

.emotion-0 .slick-dots li {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: 20px;
  height: 20px;
  margin: 0 5px;
  padding: 0;
  cursor: pointer;
}

.emotion-0 .slick-dots li.slick-active button:before {
  opacity: 0.75;
  color: black;
}

.emotion-0 .slick-dots li button {
  font-size: 0;
  line-height: 0;
  display: block;
  padding: 5px;
  cursor: pointer;
  color: transparent;
  border: 0;
  outline: none;
  background: transparent;
}

.emotion-0 .slick-dots li button:hover,
.emotion-0 .slick-dots li button:focus {
  outline: none;
}

.emotion-0 .slick-dots li button:hover:before,
.emotion-0 .slick-dots li button:focus:before,
.emotion-0 .slick-dots li button:hover:before,
.emotion-0 .slick-dots li button:focus:before {
  opacity: 1;
}

.emotion-0 .slick-dots li button:before {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 5px;
  height: 5px;
  content: "";
  opacity: 0.25;
  background-color: black;
  border-radius: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

<div
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`CarouselContainer snapshots renders fade state correctly BananaRepublic 1`] = `
<DocumentFragment>
  .emotion-0 {
  position: relative;
}

.emotion-0 .slick-slide {
  pointer-events: none;
}

.emotion-0 .slick-active {
  pointer-events: auto;
}

.emotion-0 .slick-slider {
  position: relative;
  display: block;
  box-sizing: border-box;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  -webkit-touch-callout: none;
  touch-action: pan-y;
  -webkit-tap-highlight-color: transparent;
  scroll-behavior: smooth;
}

.emotion-0 .slick-slider .slick-track,
.emotion-0 .slick-slider .slick-list {
  -webkit-transform: translate3d(0, 0, 0);
  -moz-transform: translate3d(0, 0, 0);
  -ms-transform: translate3d(0, 0, 0);
  transform: translate3d(0, 0, 0);
}

.emotion-0 .slick-list {
  position: relative;
  display: block;
  overflow-x: hidden;
  overflow-y: hidden;
  margin: 0;
  padding: 0;
}

.emotion-0 .slick-list:focus {
  outline: none;
}

.emotion-0 .slick-list.dragging {
  cursor: pointer;
  cursor: hand;
}

.emotion-0 .slick-track {
  position: relative;
  top: 0;
  left: 0;
  display: block;
  margin-left: auto;
  margin-right: auto;
}

.emotion-0 .slick-track:before,
.emotion-0 .slick-track:after {
  display: table;
  content: "";
}

.emotion-0 .slick-track:after {
  clear: both;
}

.emotion-0 .slick-slide {
  width: 48px!important;
  display: none;
  float: left;
  height: 100%;
  min-height: 1px;
}

.emotion-0 .slick-slide img {
  display: block;
}

.emotion-0 .slick-slide.slick-loading img {
  display: none;
}

.emotion-0 .slick-slide.dragging img {
  pointer-events: none;
}

.emotion-0 [dir="rtl"] .slick-slide {
  float: right;
}

.emotion-0 .slick-initialized .slick-slide,
.emotion-0 .slick-vertical .slick-slide {
  display: block;
}

.emotion-0 .slick-vertical .slick-slide {
  border: 1px solid transparent;
  height: auto;
}

.emotion-0 .slick-loading .slick-track,
.emotion-0 .slick-loading .slick-slide {
  visibility: hidden;
}

.emotion-0 .slick-loading .slick-list {
  background: #fff url("./ajax-loader.gif") center center no-repeat;
}

.emotion-0 .slick-arrow.slick-hidden {
  display: none;
}

.emotion-0 .slick-prev,
.emotion-0 .slick-next {
  font-size: 0;
  line-height: 0;
  position: absolute;
  top: 50%;
  display: block;
  padding: 0;
  cursor: pointer;
  color: transparent;
  border: none;
  outline: none;
  background: transparent;
}

.emotion-0 .slick-prev:hover,
.emotion-0 .slick-next:hover,
.emotion-0 .slick-prev:focus,
.emotion-0 .slick-next:focus {
  color: transparent;
  outline: none;
  background: transparent;
  opacity: 1;
}

.emotion-0 .slick-prev.slick-disabled,
.emotion-0 .slick-next.slick-disabled {
  opacity: 0.25;
}

.emotion-0 .slick-prev {
  left: -25px;
  -webkit-transform: translate(0, -50%) rotate(90deg);
  -moz-transform: translate(0, -50%) rotate(90deg);
  -ms-transform: translate(0, -50%) rotate(90deg);
  transform: translate(0, -50%) rotate(90deg);
}

.emotion-0 .slick-prev span {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-0 [dir="rtl"] .slick-prev {
  right: -25px;
  left: auto;
}

.emotion-0 .slick-next {
  right: -25px;
  -webkit-transform: translate(0, -50%) rotate(-90deg);
  -moz-transform: translate(0, -50%) rotate(-90deg);
  -ms-transform: translate(0, -50%) rotate(-90deg);
  transform: translate(0, -50%) rotate(-90deg);
}

.emotion-0 .slick-next span {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-0 [dir="rtl"] .slick-next {
  right: auto;
  left: -25px;
}

.emotion-0 .slick-dotted.slick-slider {
  margin-bottom: 30px;
}

.emotion-0 .slick-dots {
  position: absolute;
  bottom: -25px;
  display: block;
  width: 100%;
  padding: 0;
  margin: 0;
  list-style: none;
  text-align: center;
}

.emotion-0 .slick-dots li {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: 20px;
  height: 20px;
  margin: 0 5px;
  padding: 0;
  cursor: pointer;
}

.emotion-0 .slick-dots li.slick-active button:before {
  opacity: 0.75;
  color: black;
}

.emotion-0 .slick-dots li button {
  font-size: 0;
  line-height: 0;
  display: block;
  padding: 5px;
  cursor: pointer;
  color: transparent;
  border: 0;
  outline: none;
  background: transparent;
}

.emotion-0 .slick-dots li button:hover,
.emotion-0 .slick-dots li button:focus {
  outline: none;
}

.emotion-0 .slick-dots li button:hover:before,
.emotion-0 .slick-dots li button:focus:before,
.emotion-0 .slick-dots li button:hover:before,
.emotion-0 .slick-dots li button:focus:before {
  opacity: 1;
}

.emotion-0 .slick-dots li button:before {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 5px;
  height: 5px;
  content: "";
  opacity: 0.25;
  background-color: black;
  border-radius: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

<div
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`CarouselContainer snapshots renders fade state correctly BananaRepublic in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  position: relative;
}

.emotion-0 .slick-slide {
  pointer-events: none;
}

.emotion-0 .slick-active {
  pointer-events: auto;
}

.emotion-0 .slick-slider {
  position: relative;
  display: block;
  box-sizing: border-box;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  -webkit-touch-callout: none;
  touch-action: pan-y;
  -webkit-tap-highlight-color: transparent;
  scroll-behavior: smooth;
}

.emotion-0 .slick-slider .slick-track,
.emotion-0 .slick-slider .slick-list {
  -webkit-transform: translate3d(0, 0, 0);
  -moz-transform: translate3d(0, 0, 0);
  -ms-transform: translate3d(0, 0, 0);
  transform: translate3d(0, 0, 0);
}

.emotion-0 .slick-list {
  position: relative;
  display: block;
  overflow-x: hidden;
  overflow-y: hidden;
  margin: 0;
  padding: 0;
}

.emotion-0 .slick-list:focus {
  outline: none;
}

.emotion-0 .slick-list.dragging {
  cursor: pointer;
  cursor: hand;
}

.emotion-0 .slick-track {
  position: relative;
  top: 0;
  left: 0;
  display: block;
  margin-left: auto;
  margin-right: auto;
}

.emotion-0 .slick-track:before,
.emotion-0 .slick-track:after {
  display: table;
  content: "";
}

.emotion-0 .slick-track:after {
  clear: both;
}

.emotion-0 .slick-slide {
  width: 48px!important;
  display: none;
  float: left;
  height: 100%;
  min-height: 1px;
}

.emotion-0 .slick-slide img {
  display: block;
}

.emotion-0 .slick-slide.slick-loading img {
  display: none;
}

.emotion-0 .slick-slide.dragging img {
  pointer-events: none;
}

.emotion-0 [dir="rtl"] .slick-slide {
  float: right;
}

.emotion-0 .slick-initialized .slick-slide,
.emotion-0 .slick-vertical .slick-slide {
  display: block;
}

.emotion-0 .slick-vertical .slick-slide {
  border: 1px solid transparent;
  height: auto;
}

.emotion-0 .slick-loading .slick-track,
.emotion-0 .slick-loading .slick-slide {
  visibility: hidden;
}

.emotion-0 .slick-loading .slick-list {
  background: #fff url("./ajax-loader.gif") center center no-repeat;
}

.emotion-0 .slick-arrow.slick-hidden {
  display: none;
}

.emotion-0 .slick-prev,
.emotion-0 .slick-next {
  font-size: 0;
  line-height: 0;
  position: absolute;
  top: 50%;
  display: block;
  padding: 0;
  cursor: pointer;
  color: transparent;
  border: none;
  outline: none;
  background: transparent;
}

.emotion-0 .slick-prev:hover,
.emotion-0 .slick-next:hover,
.emotion-0 .slick-prev:focus,
.emotion-0 .slick-next:focus {
  color: transparent;
  outline: none;
  background: transparent;
  opacity: 1;
}

.emotion-0 .slick-prev.slick-disabled,
.emotion-0 .slick-next.slick-disabled {
  opacity: 0.25;
}

.emotion-0 .slick-prev {
  left: -25px;
  -webkit-transform: translate(0, -50%) rotate(90deg);
  -moz-transform: translate(0, -50%) rotate(90deg);
  -ms-transform: translate(0, -50%) rotate(90deg);
  transform: translate(0, -50%) rotate(90deg);
}

.emotion-0 .slick-prev span {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-0 [dir="rtl"] .slick-prev {
  right: -25px;
  left: auto;
}

.emotion-0 .slick-next {
  right: -25px;
  -webkit-transform: translate(0, -50%) rotate(-90deg);
  -moz-transform: translate(0, -50%) rotate(-90deg);
  -ms-transform: translate(0, -50%) rotate(-90deg);
  transform: translate(0, -50%) rotate(-90deg);
}

.emotion-0 .slick-next span {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-0 [dir="rtl"] .slick-next {
  right: auto;
  left: -25px;
}

.emotion-0 .slick-dotted.slick-slider {
  margin-bottom: 30px;
}

.emotion-0 .slick-dots {
  position: absolute;
  bottom: -25px;
  display: block;
  width: 100%;
  padding: 0;
  margin: 0;
  list-style: none;
  text-align: center;
}

.emotion-0 .slick-dots li {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: 20px;
  height: 20px;
  margin: 0 5px;
  padding: 0;
  cursor: pointer;
}

.emotion-0 .slick-dots li.slick-active button:before {
  opacity: 0.75;
  color: black;
}

.emotion-0 .slick-dots li button {
  font-size: 0;
  line-height: 0;
  display: block;
  padding: 5px;
  cursor: pointer;
  color: transparent;
  border: 0;
  outline: none;
  background: transparent;
}

.emotion-0 .slick-dots li button:hover,
.emotion-0 .slick-dots li button:focus {
  outline: none;
}

.emotion-0 .slick-dots li button:hover:before,
.emotion-0 .slick-dots li button:focus:before,
.emotion-0 .slick-dots li button:hover:before,
.emotion-0 .slick-dots li button:focus:before {
  opacity: 1;
}

.emotion-0 .slick-dots li button:before {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 5px;
  height: 5px;
  content: "";
  opacity: 0.25;
  background-color: black;
  border-radius: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

<div
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`CarouselContainer snapshots renders fade state correctly BananaRepublicFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  position: relative;
}

.emotion-0 .slick-slide {
  pointer-events: none;
}

.emotion-0 .slick-active {
  pointer-events: auto;
}

.emotion-0 .slick-slider {
  position: relative;
  display: block;
  box-sizing: border-box;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  -webkit-touch-callout: none;
  touch-action: pan-y;
  -webkit-tap-highlight-color: transparent;
  scroll-behavior: smooth;
}

.emotion-0 .slick-slider .slick-track,
.emotion-0 .slick-slider .slick-list {
  -webkit-transform: translate3d(0, 0, 0);
  -moz-transform: translate3d(0, 0, 0);
  -ms-transform: translate3d(0, 0, 0);
  transform: translate3d(0, 0, 0);
}

.emotion-0 .slick-list {
  position: relative;
  display: block;
  overflow-x: hidden;
  overflow-y: hidden;
  margin: 0;
  padding: 0;
}

.emotion-0 .slick-list:focus {
  outline: none;
}

.emotion-0 .slick-list.dragging {
  cursor: pointer;
  cursor: hand;
}

.emotion-0 .slick-track {
  position: relative;
  top: 0;
  left: 0;
  display: block;
  margin-left: auto;
  margin-right: auto;
}

.emotion-0 .slick-track:before,
.emotion-0 .slick-track:after {
  display: table;
  content: "";
}

.emotion-0 .slick-track:after {
  clear: both;
}

.emotion-0 .slick-slide {
  width: 48px!important;
  display: none;
  float: left;
  height: 100%;
  min-height: 1px;
}

.emotion-0 .slick-slide img {
  display: block;
}

.emotion-0 .slick-slide.slick-loading img {
  display: none;
}

.emotion-0 .slick-slide.dragging img {
  pointer-events: none;
}

.emotion-0 [dir="rtl"] .slick-slide {
  float: right;
}

.emotion-0 .slick-initialized .slick-slide,
.emotion-0 .slick-vertical .slick-slide {
  display: block;
}

.emotion-0 .slick-vertical .slick-slide {
  border: 1px solid transparent;
  height: auto;
}

.emotion-0 .slick-loading .slick-track,
.emotion-0 .slick-loading .slick-slide {
  visibility: hidden;
}

.emotion-0 .slick-loading .slick-list {
  background: #fff url("./ajax-loader.gif") center center no-repeat;
}

.emotion-0 .slick-arrow.slick-hidden {
  display: none;
}

.emotion-0 .slick-prev,
.emotion-0 .slick-next {
  font-size: 0;
  line-height: 0;
  position: absolute;
  top: 50%;
  display: block;
  padding: 0;
  cursor: pointer;
  color: transparent;
  border: none;
  outline: none;
  background: transparent;
}

.emotion-0 .slick-prev:hover,
.emotion-0 .slick-next:hover,
.emotion-0 .slick-prev:focus,
.emotion-0 .slick-next:focus {
  color: transparent;
  outline: none;
  background: transparent;
  opacity: 1;
}

.emotion-0 .slick-prev.slick-disabled,
.emotion-0 .slick-next.slick-disabled {
  opacity: 0.25;
}

.emotion-0 .slick-prev {
  left: -25px;
  -webkit-transform: translate(0, -50%) rotate(90deg);
  -moz-transform: translate(0, -50%) rotate(90deg);
  -ms-transform: translate(0, -50%) rotate(90deg);
  transform: translate(0, -50%) rotate(90deg);
}

.emotion-0 .slick-prev span {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-0 [dir="rtl"] .slick-prev {
  right: -25px;
  left: auto;
}

.emotion-0 .slick-next {
  right: -25px;
  -webkit-transform: translate(0, -50%) rotate(-90deg);
  -moz-transform: translate(0, -50%) rotate(-90deg);
  -ms-transform: translate(0, -50%) rotate(-90deg);
  transform: translate(0, -50%) rotate(-90deg);
}

.emotion-0 .slick-next span {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-0 [dir="rtl"] .slick-next {
  right: auto;
  left: -25px;
}

.emotion-0 .slick-dotted.slick-slider {
  margin-bottom: 30px;
}

.emotion-0 .slick-dots {
  position: absolute;
  bottom: -25px;
  display: block;
  width: 100%;
  padding: 0;
  margin: 0;
  list-style: none;
  text-align: center;
}

.emotion-0 .slick-dots li {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: 20px;
  height: 20px;
  margin: 0 5px;
  padding: 0;
  cursor: pointer;
}

.emotion-0 .slick-dots li.slick-active button:before {
  opacity: 0.75;
  color: black;
}

.emotion-0 .slick-dots li button {
  font-size: 0;
  line-height: 0;
  display: block;
  padding: 5px;
  cursor: pointer;
  color: transparent;
  border: 0;
  outline: none;
  background: transparent;
}

.emotion-0 .slick-dots li button:hover,
.emotion-0 .slick-dots li button:focus {
  outline: none;
}

.emotion-0 .slick-dots li button:hover:before,
.emotion-0 .slick-dots li button:focus:before,
.emotion-0 .slick-dots li button:hover:before,
.emotion-0 .slick-dots li button:focus:before {
  opacity: 1;
}

.emotion-0 .slick-dots li button:before {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 5px;
  height: 5px;
  content: "";
  opacity: 0.25;
  background-color: black;
  border-radius: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

<div
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`CarouselContainer snapshots renders fade state correctly BananaRepublicFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  position: relative;
}

.emotion-0 .slick-slide {
  pointer-events: none;
}

.emotion-0 .slick-active {
  pointer-events: auto;
}

.emotion-0 .slick-slider {
  position: relative;
  display: block;
  box-sizing: border-box;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  -webkit-touch-callout: none;
  touch-action: pan-y;
  -webkit-tap-highlight-color: transparent;
  scroll-behavior: smooth;
}

.emotion-0 .slick-slider .slick-track,
.emotion-0 .slick-slider .slick-list {
  -webkit-transform: translate3d(0, 0, 0);
  -moz-transform: translate3d(0, 0, 0);
  -ms-transform: translate3d(0, 0, 0);
  transform: translate3d(0, 0, 0);
}

.emotion-0 .slick-list {
  position: relative;
  display: block;
  overflow-x: hidden;
  overflow-y: hidden;
  margin: 0;
  padding: 0;
}

.emotion-0 .slick-list:focus {
  outline: none;
}

.emotion-0 .slick-list.dragging {
  cursor: pointer;
  cursor: hand;
}

.emotion-0 .slick-track {
  position: relative;
  top: 0;
  left: 0;
  display: block;
  margin-left: auto;
  margin-right: auto;
}

.emotion-0 .slick-track:before,
.emotion-0 .slick-track:after {
  display: table;
  content: "";
}

.emotion-0 .slick-track:after {
  clear: both;
}

.emotion-0 .slick-slide {
  width: 48px!important;
  display: none;
  float: left;
  height: 100%;
  min-height: 1px;
}

.emotion-0 .slick-slide img {
  display: block;
}

.emotion-0 .slick-slide.slick-loading img {
  display: none;
}

.emotion-0 .slick-slide.dragging img {
  pointer-events: none;
}

.emotion-0 [dir="rtl"] .slick-slide {
  float: right;
}

.emotion-0 .slick-initialized .slick-slide,
.emotion-0 .slick-vertical .slick-slide {
  display: block;
}

.emotion-0 .slick-vertical .slick-slide {
  border: 1px solid transparent;
  height: auto;
}

.emotion-0 .slick-loading .slick-track,
.emotion-0 .slick-loading .slick-slide {
  visibility: hidden;
}

.emotion-0 .slick-loading .slick-list {
  background: #fff url("./ajax-loader.gif") center center no-repeat;
}

.emotion-0 .slick-arrow.slick-hidden {
  display: none;
}

.emotion-0 .slick-prev,
.emotion-0 .slick-next {
  font-size: 0;
  line-height: 0;
  position: absolute;
  top: 50%;
  display: block;
  padding: 0;
  cursor: pointer;
  color: transparent;
  border: none;
  outline: none;
  background: transparent;
}

.emotion-0 .slick-prev:hover,
.emotion-0 .slick-next:hover,
.emotion-0 .slick-prev:focus,
.emotion-0 .slick-next:focus {
  color: transparent;
  outline: none;
  background: transparent;
  opacity: 1;
}

.emotion-0 .slick-prev.slick-disabled,
.emotion-0 .slick-next.slick-disabled {
  opacity: 0.25;
}

.emotion-0 .slick-prev {
  left: -25px;
  -webkit-transform: translate(0, -50%) rotate(90deg);
  -moz-transform: translate(0, -50%) rotate(90deg);
  -ms-transform: translate(0, -50%) rotate(90deg);
  transform: translate(0, -50%) rotate(90deg);
}

.emotion-0 .slick-prev span {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-0 [dir="rtl"] .slick-prev {
  right: -25px;
  left: auto;
}

.emotion-0 .slick-next {
  right: -25px;
  -webkit-transform: translate(0, -50%) rotate(-90deg);
  -moz-transform: translate(0, -50%) rotate(-90deg);
  -ms-transform: translate(0, -50%) rotate(-90deg);
  transform: translate(0, -50%) rotate(-90deg);
}

.emotion-0 .slick-next span {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-0 [dir="rtl"] .slick-next {
  right: auto;
  left: -25px;
}

.emotion-0 .slick-dotted.slick-slider {
  margin-bottom: 30px;
}

.emotion-0 .slick-dots {
  position: absolute;
  bottom: -25px;
  display: block;
  width: 100%;
  padding: 0;
  margin: 0;
  list-style: none;
  text-align: center;
}

.emotion-0 .slick-dots li {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: 20px;
  height: 20px;
  margin: 0 5px;
  padding: 0;
  cursor: pointer;
}

.emotion-0 .slick-dots li.slick-active button:before {
  opacity: 0.75;
  color: black;
}

.emotion-0 .slick-dots li button {
  font-size: 0;
  line-height: 0;
  display: block;
  padding: 5px;
  cursor: pointer;
  color: transparent;
  border: 0;
  outline: none;
  background: transparent;
}

.emotion-0 .slick-dots li button:hover,
.emotion-0 .slick-dots li button:focus {
  outline: none;
}

.emotion-0 .slick-dots li button:hover:before,
.emotion-0 .slick-dots li button:focus:before,
.emotion-0 .slick-dots li button:hover:before,
.emotion-0 .slick-dots li button:focus:before {
  opacity: 1;
}

.emotion-0 .slick-dots li button:before {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 5px;
  height: 5px;
  content: "";
  opacity: 0.25;
  background-color: black;
  border-radius: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

<div
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`CarouselContainer snapshots renders fade state correctly Gap 1`] = `
<DocumentFragment>
  .emotion-0 {
  position: relative;
}

.emotion-0 .slick-slide {
  pointer-events: none;
}

.emotion-0 .slick-active {
  pointer-events: auto;
}

.emotion-0 .slick-slider {
  position: relative;
  display: block;
  box-sizing: border-box;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  -webkit-touch-callout: none;
  touch-action: pan-y;
  -webkit-tap-highlight-color: transparent;
  scroll-behavior: smooth;
}

.emotion-0 .slick-slider .slick-track,
.emotion-0 .slick-slider .slick-list {
  -webkit-transform: translate3d(0, 0, 0);
  -moz-transform: translate3d(0, 0, 0);
  -ms-transform: translate3d(0, 0, 0);
  transform: translate3d(0, 0, 0);
}

.emotion-0 .slick-list {
  position: relative;
  display: block;
  overflow-x: hidden;
  overflow-y: hidden;
  margin: 0;
  padding: 0;
}

.emotion-0 .slick-list:focus {
  outline: none;
}

.emotion-0 .slick-list.dragging {
  cursor: pointer;
  cursor: hand;
}

.emotion-0 .slick-track {
  position: relative;
  top: 0;
  left: 0;
  display: block;
  margin-left: auto;
  margin-right: auto;
}

.emotion-0 .slick-track:before,
.emotion-0 .slick-track:after {
  display: table;
  content: "";
}

.emotion-0 .slick-track:after {
  clear: both;
}

.emotion-0 .slick-slide {
  width: 48px!important;
  display: none;
  float: left;
  height: 100%;
  min-height: 1px;
}

.emotion-0 .slick-slide img {
  display: block;
}

.emotion-0 .slick-slide.slick-loading img {
  display: none;
}

.emotion-0 .slick-slide.dragging img {
  pointer-events: none;
}

.emotion-0 [dir="rtl"] .slick-slide {
  float: right;
}

.emotion-0 .slick-initialized .slick-slide,
.emotion-0 .slick-vertical .slick-slide {
  display: block;
}

.emotion-0 .slick-vertical .slick-slide {
  border: 1px solid transparent;
  height: auto;
}

.emotion-0 .slick-loading .slick-track,
.emotion-0 .slick-loading .slick-slide {
  visibility: hidden;
}

.emotion-0 .slick-loading .slick-list {
  background: #fff url("./ajax-loader.gif") center center no-repeat;
}

.emotion-0 .slick-arrow.slick-hidden {
  display: none;
}

.emotion-0 .slick-prev,
.emotion-0 .slick-next {
  font-size: 0;
  line-height: 0;
  position: absolute;
  top: 50%;
  display: block;
  padding: 0;
  cursor: pointer;
  color: transparent;
  border: none;
  outline: none;
  background: transparent;
}

.emotion-0 .slick-prev:hover,
.emotion-0 .slick-next:hover,
.emotion-0 .slick-prev:focus,
.emotion-0 .slick-next:focus {
  color: transparent;
  outline: none;
  background: transparent;
  opacity: 1;
}

.emotion-0 .slick-prev.slick-disabled,
.emotion-0 .slick-next.slick-disabled {
  opacity: 0.25;
}

.emotion-0 .slick-prev {
  left: -25px;
  -webkit-transform: translate(0, -50%) rotate(90deg);
  -moz-transform: translate(0, -50%) rotate(90deg);
  -ms-transform: translate(0, -50%) rotate(90deg);
  transform: translate(0, -50%) rotate(90deg);
}

.emotion-0 .slick-prev span {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-0 [dir="rtl"] .slick-prev {
  right: -25px;
  left: auto;
}

.emotion-0 .slick-next {
  right: -25px;
  -webkit-transform: translate(0, -50%) rotate(-90deg);
  -moz-transform: translate(0, -50%) rotate(-90deg);
  -ms-transform: translate(0, -50%) rotate(-90deg);
  transform: translate(0, -50%) rotate(-90deg);
}

.emotion-0 .slick-next span {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-0 [dir="rtl"] .slick-next {
  right: auto;
  left: -25px;
}

.emotion-0 .slick-dotted.slick-slider {
  margin-bottom: 30px;
}

.emotion-0 .slick-dots {
  position: absolute;
  bottom: -25px;
  display: block;
  width: 100%;
  padding: 0;
  margin: 0;
  list-style: none;
  text-align: center;
}

.emotion-0 .slick-dots li {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: 20px;
  height: 20px;
  margin: 0 5px;
  padding: 0;
  cursor: pointer;
}

.emotion-0 .slick-dots li.slick-active button:before {
  opacity: 0.75;
  color: black;
}

.emotion-0 .slick-dots li button {
  font-size: 0;
  line-height: 0;
  display: block;
  padding: 5px;
  cursor: pointer;
  color: transparent;
  border: 0;
  outline: none;
  background: transparent;
}

.emotion-0 .slick-dots li button:hover,
.emotion-0 .slick-dots li button:focus {
  outline: none;
}

.emotion-0 .slick-dots li button:hover:before,
.emotion-0 .slick-dots li button:focus:before,
.emotion-0 .slick-dots li button:hover:before,
.emotion-0 .slick-dots li button:focus:before {
  opacity: 1;
}

.emotion-0 .slick-dots li button:before {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 5px;
  height: 5px;
  content: "";
  opacity: 0.25;
  background-color: black;
  border-radius: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

<div
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`CarouselContainer snapshots renders fade state correctly Gap in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  position: relative;
}

.emotion-0 .slick-slide {
  pointer-events: none;
}

.emotion-0 .slick-active {
  pointer-events: auto;
}

.emotion-0 .slick-slider {
  position: relative;
  display: block;
  box-sizing: border-box;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  -webkit-touch-callout: none;
  touch-action: pan-y;
  -webkit-tap-highlight-color: transparent;
  scroll-behavior: smooth;
}

.emotion-0 .slick-slider .slick-track,
.emotion-0 .slick-slider .slick-list {
  -webkit-transform: translate3d(0, 0, 0);
  -moz-transform: translate3d(0, 0, 0);
  -ms-transform: translate3d(0, 0, 0);
  transform: translate3d(0, 0, 0);
}

.emotion-0 .slick-list {
  position: relative;
  display: block;
  overflow-x: hidden;
  overflow-y: hidden;
  margin: 0;
  padding: 0;
}

.emotion-0 .slick-list:focus {
  outline: none;
}

.emotion-0 .slick-list.dragging {
  cursor: pointer;
  cursor: hand;
}

.emotion-0 .slick-track {
  position: relative;
  top: 0;
  left: 0;
  display: block;
  margin-left: auto;
  margin-right: auto;
}

.emotion-0 .slick-track:before,
.emotion-0 .slick-track:after {
  display: table;
  content: "";
}

.emotion-0 .slick-track:after {
  clear: both;
}

.emotion-0 .slick-slide {
  width: 48px!important;
  display: none;
  float: left;
  height: 100%;
  min-height: 1px;
}

.emotion-0 .slick-slide img {
  display: block;
}

.emotion-0 .slick-slide.slick-loading img {
  display: none;
}

.emotion-0 .slick-slide.dragging img {
  pointer-events: none;
}

.emotion-0 [dir="rtl"] .slick-slide {
  float: right;
}

.emotion-0 .slick-initialized .slick-slide,
.emotion-0 .slick-vertical .slick-slide {
  display: block;
}

.emotion-0 .slick-vertical .slick-slide {
  border: 1px solid transparent;
  height: auto;
}

.emotion-0 .slick-loading .slick-track,
.emotion-0 .slick-loading .slick-slide {
  visibility: hidden;
}

.emotion-0 .slick-loading .slick-list {
  background: #fff url("./ajax-loader.gif") center center no-repeat;
}

.emotion-0 .slick-arrow.slick-hidden {
  display: none;
}

.emotion-0 .slick-prev,
.emotion-0 .slick-next {
  font-size: 0;
  line-height: 0;
  position: absolute;
  top: 50%;
  display: block;
  padding: 0;
  cursor: pointer;
  color: transparent;
  border: none;
  outline: none;
  background: transparent;
}

.emotion-0 .slick-prev:hover,
.emotion-0 .slick-next:hover,
.emotion-0 .slick-prev:focus,
.emotion-0 .slick-next:focus {
  color: transparent;
  outline: none;
  background: transparent;
  opacity: 1;
}

.emotion-0 .slick-prev.slick-disabled,
.emotion-0 .slick-next.slick-disabled {
  opacity: 0.25;
}

.emotion-0 .slick-prev {
  left: -25px;
  -webkit-transform: translate(0, -50%) rotate(90deg);
  -moz-transform: translate(0, -50%) rotate(90deg);
  -ms-transform: translate(0, -50%) rotate(90deg);
  transform: translate(0, -50%) rotate(90deg);
}

.emotion-0 .slick-prev span {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-0 [dir="rtl"] .slick-prev {
  right: -25px;
  left: auto;
}

.emotion-0 .slick-next {
  right: -25px;
  -webkit-transform: translate(0, -50%) rotate(-90deg);
  -moz-transform: translate(0, -50%) rotate(-90deg);
  -ms-transform: translate(0, -50%) rotate(-90deg);
  transform: translate(0, -50%) rotate(-90deg);
}

.emotion-0 .slick-next span {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-0 [dir="rtl"] .slick-next {
  right: auto;
  left: -25px;
}

.emotion-0 .slick-dotted.slick-slider {
  margin-bottom: 30px;
}

.emotion-0 .slick-dots {
  position: absolute;
  bottom: -25px;
  display: block;
  width: 100%;
  padding: 0;
  margin: 0;
  list-style: none;
  text-align: center;
}

.emotion-0 .slick-dots li {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: 20px;
  height: 20px;
  margin: 0 5px;
  padding: 0;
  cursor: pointer;
}

.emotion-0 .slick-dots li.slick-active button:before {
  opacity: 0.75;
  color: black;
}

.emotion-0 .slick-dots li button {
  font-size: 0;
  line-height: 0;
  display: block;
  padding: 5px;
  cursor: pointer;
  color: transparent;
  border: 0;
  outline: none;
  background: transparent;
}

.emotion-0 .slick-dots li button:hover,
.emotion-0 .slick-dots li button:focus {
  outline: none;
}

.emotion-0 .slick-dots li button:hover:before,
.emotion-0 .slick-dots li button:focus:before,
.emotion-0 .slick-dots li button:hover:before,
.emotion-0 .slick-dots li button:focus:before {
  opacity: 1;
}

.emotion-0 .slick-dots li button:before {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 5px;
  height: 5px;
  content: "";
  opacity: 0.25;
  background-color: black;
  border-radius: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

<div
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`CarouselContainer snapshots renders fade state correctly GapFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  position: relative;
}

.emotion-0 .slick-slide {
  pointer-events: none;
}

.emotion-0 .slick-active {
  pointer-events: auto;
}

.emotion-0 .slick-slider {
  position: relative;
  display: block;
  box-sizing: border-box;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  -webkit-touch-callout: none;
  touch-action: pan-y;
  -webkit-tap-highlight-color: transparent;
  scroll-behavior: smooth;
}

.emotion-0 .slick-slider .slick-track,
.emotion-0 .slick-slider .slick-list {
  -webkit-transform: translate3d(0, 0, 0);
  -moz-transform: translate3d(0, 0, 0);
  -ms-transform: translate3d(0, 0, 0);
  transform: translate3d(0, 0, 0);
}

.emotion-0 .slick-list {
  position: relative;
  display: block;
  overflow-x: hidden;
  overflow-y: hidden;
  margin: 0;
  padding: 0;
}

.emotion-0 .slick-list:focus {
  outline: none;
}

.emotion-0 .slick-list.dragging {
  cursor: pointer;
  cursor: hand;
}

.emotion-0 .slick-track {
  position: relative;
  top: 0;
  left: 0;
  display: block;
  margin-left: auto;
  margin-right: auto;
}

.emotion-0 .slick-track:before,
.emotion-0 .slick-track:after {
  display: table;
  content: "";
}

.emotion-0 .slick-track:after {
  clear: both;
}

.emotion-0 .slick-slide {
  width: 48px!important;
  display: none;
  float: left;
  height: 100%;
  min-height: 1px;
}

.emotion-0 .slick-slide img {
  display: block;
}

.emotion-0 .slick-slide.slick-loading img {
  display: none;
}

.emotion-0 .slick-slide.dragging img {
  pointer-events: none;
}

.emotion-0 [dir="rtl"] .slick-slide {
  float: right;
}

.emotion-0 .slick-initialized .slick-slide,
.emotion-0 .slick-vertical .slick-slide {
  display: block;
}

.emotion-0 .slick-vertical .slick-slide {
  border: 1px solid transparent;
  height: auto;
}

.emotion-0 .slick-loading .slick-track,
.emotion-0 .slick-loading .slick-slide {
  visibility: hidden;
}

.emotion-0 .slick-loading .slick-list {
  background: #fff url("./ajax-loader.gif") center center no-repeat;
}

.emotion-0 .slick-arrow.slick-hidden {
  display: none;
}

.emotion-0 .slick-prev,
.emotion-0 .slick-next {
  font-size: 0;
  line-height: 0;
  position: absolute;
  top: 50%;
  display: block;
  padding: 0;
  cursor: pointer;
  color: transparent;
  border: none;
  outline: none;
  background: transparent;
}

.emotion-0 .slick-prev:hover,
.emotion-0 .slick-next:hover,
.emotion-0 .slick-prev:focus,
.emotion-0 .slick-next:focus {
  color: transparent;
  outline: none;
  background: transparent;
  opacity: 1;
}

.emotion-0 .slick-prev.slick-disabled,
.emotion-0 .slick-next.slick-disabled {
  opacity: 0.25;
}

.emotion-0 .slick-prev {
  left: -25px;
  -webkit-transform: translate(0, -50%) rotate(90deg);
  -moz-transform: translate(0, -50%) rotate(90deg);
  -ms-transform: translate(0, -50%) rotate(90deg);
  transform: translate(0, -50%) rotate(90deg);
}

.emotion-0 .slick-prev span {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-0 [dir="rtl"] .slick-prev {
  right: -25px;
  left: auto;
}

.emotion-0 .slick-next {
  right: -25px;
  -webkit-transform: translate(0, -50%) rotate(-90deg);
  -moz-transform: translate(0, -50%) rotate(-90deg);
  -ms-transform: translate(0, -50%) rotate(-90deg);
  transform: translate(0, -50%) rotate(-90deg);
}

.emotion-0 .slick-next span {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-0 [dir="rtl"] .slick-next {
  right: auto;
  left: -25px;
}

.emotion-0 .slick-dotted.slick-slider {
  margin-bottom: 30px;
}

.emotion-0 .slick-dots {
  position: absolute;
  bottom: -25px;
  display: block;
  width: 100%;
  padding: 0;
  margin: 0;
  list-style: none;
  text-align: center;
}

.emotion-0 .slick-dots li {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: 20px;
  height: 20px;
  margin: 0 5px;
  padding: 0;
  cursor: pointer;
}

.emotion-0 .slick-dots li.slick-active button:before {
  opacity: 0.75;
  color: black;
}

.emotion-0 .slick-dots li button {
  font-size: 0;
  line-height: 0;
  display: block;
  padding: 5px;
  cursor: pointer;
  color: transparent;
  border: 0;
  outline: none;
  background: transparent;
}

.emotion-0 .slick-dots li button:hover,
.emotion-0 .slick-dots li button:focus {
  outline: none;
}

.emotion-0 .slick-dots li button:hover:before,
.emotion-0 .slick-dots li button:focus:before,
.emotion-0 .slick-dots li button:hover:before,
.emotion-0 .slick-dots li button:focus:before {
  opacity: 1;
}

.emotion-0 .slick-dots li button:before {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 5px;
  height: 5px;
  content: "";
  opacity: 0.25;
  background-color: black;
  border-radius: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

<div
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`CarouselContainer snapshots renders fade state correctly GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  position: relative;
}

.emotion-0 .slick-slide {
  pointer-events: none;
}

.emotion-0 .slick-active {
  pointer-events: auto;
}

.emotion-0 .slick-slider {
  position: relative;
  display: block;
  box-sizing: border-box;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  -webkit-touch-callout: none;
  touch-action: pan-y;
  -webkit-tap-highlight-color: transparent;
  scroll-behavior: smooth;
}

.emotion-0 .slick-slider .slick-track,
.emotion-0 .slick-slider .slick-list {
  -webkit-transform: translate3d(0, 0, 0);
  -moz-transform: translate3d(0, 0, 0);
  -ms-transform: translate3d(0, 0, 0);
  transform: translate3d(0, 0, 0);
}

.emotion-0 .slick-list {
  position: relative;
  display: block;
  overflow-x: hidden;
  overflow-y: hidden;
  margin: 0;
  padding: 0;
}

.emotion-0 .slick-list:focus {
  outline: none;
}

.emotion-0 .slick-list.dragging {
  cursor: pointer;
  cursor: hand;
}

.emotion-0 .slick-track {
  position: relative;
  top: 0;
  left: 0;
  display: block;
  margin-left: auto;
  margin-right: auto;
}

.emotion-0 .slick-track:before,
.emotion-0 .slick-track:after {
  display: table;
  content: "";
}

.emotion-0 .slick-track:after {
  clear: both;
}

.emotion-0 .slick-slide {
  width: 48px!important;
  display: none;
  float: left;
  height: 100%;
  min-height: 1px;
}

.emotion-0 .slick-slide img {
  display: block;
}

.emotion-0 .slick-slide.slick-loading img {
  display: none;
}

.emotion-0 .slick-slide.dragging img {
  pointer-events: none;
}

.emotion-0 [dir="rtl"] .slick-slide {
  float: right;
}

.emotion-0 .slick-initialized .slick-slide,
.emotion-0 .slick-vertical .slick-slide {
  display: block;
}

.emotion-0 .slick-vertical .slick-slide {
  border: 1px solid transparent;
  height: auto;
}

.emotion-0 .slick-loading .slick-track,
.emotion-0 .slick-loading .slick-slide {
  visibility: hidden;
}

.emotion-0 .slick-loading .slick-list {
  background: #fff url("./ajax-loader.gif") center center no-repeat;
}

.emotion-0 .slick-arrow.slick-hidden {
  display: none;
}

.emotion-0 .slick-prev,
.emotion-0 .slick-next {
  font-size: 0;
  line-height: 0;
  position: absolute;
  top: 50%;
  display: block;
  padding: 0;
  cursor: pointer;
  color: transparent;
  border: none;
  outline: none;
  background: transparent;
}

.emotion-0 .slick-prev:hover,
.emotion-0 .slick-next:hover,
.emotion-0 .slick-prev:focus,
.emotion-0 .slick-next:focus {
  color: transparent;
  outline: none;
  background: transparent;
  opacity: 1;
}

.emotion-0 .slick-prev.slick-disabled,
.emotion-0 .slick-next.slick-disabled {
  opacity: 0.25;
}

.emotion-0 .slick-prev {
  left: -25px;
  -webkit-transform: translate(0, -50%) rotate(90deg);
  -moz-transform: translate(0, -50%) rotate(90deg);
  -ms-transform: translate(0, -50%) rotate(90deg);
  transform: translate(0, -50%) rotate(90deg);
}

.emotion-0 .slick-prev span {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-0 [dir="rtl"] .slick-prev {
  right: -25px;
  left: auto;
}

.emotion-0 .slick-next {
  right: -25px;
  -webkit-transform: translate(0, -50%) rotate(-90deg);
  -moz-transform: translate(0, -50%) rotate(-90deg);
  -ms-transform: translate(0, -50%) rotate(-90deg);
  transform: translate(0, -50%) rotate(-90deg);
}

.emotion-0 .slick-next span {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-0 [dir="rtl"] .slick-next {
  right: auto;
  left: -25px;
}

.emotion-0 .slick-dotted.slick-slider {
  margin-bottom: 30px;
}

.emotion-0 .slick-dots {
  position: absolute;
  bottom: -25px;
  display: block;
  width: 100%;
  padding: 0;
  margin: 0;
  list-style: none;
  text-align: center;
}

.emotion-0 .slick-dots li {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: 20px;
  height: 20px;
  margin: 0 5px;
  padding: 0;
  cursor: pointer;
}

.emotion-0 .slick-dots li.slick-active button:before {
  opacity: 0.75;
  color: black;
}

.emotion-0 .slick-dots li button {
  font-size: 0;
  line-height: 0;
  display: block;
  padding: 5px;
  cursor: pointer;
  color: transparent;
  border: 0;
  outline: none;
  background: transparent;
}

.emotion-0 .slick-dots li button:hover,
.emotion-0 .slick-dots li button:focus {
  outline: none;
}

.emotion-0 .slick-dots li button:hover:before,
.emotion-0 .slick-dots li button:focus:before,
.emotion-0 .slick-dots li button:hover:before,
.emotion-0 .slick-dots li button:focus:before {
  opacity: 1;
}

.emotion-0 .slick-dots li button:before {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 5px;
  height: 5px;
  content: "";
  opacity: 0.25;
  background-color: black;
  border-radius: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

<div
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`CarouselContainer snapshots renders fade state correctly OldNavy 1`] = `
<DocumentFragment>
  .emotion-0 {
  position: relative;
}

.emotion-0 .slick-slide {
  pointer-events: none;
}

.emotion-0 .slick-active {
  pointer-events: auto;
}

.emotion-0 .slick-slider {
  position: relative;
  display: block;
  box-sizing: border-box;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  -webkit-touch-callout: none;
  touch-action: pan-y;
  -webkit-tap-highlight-color: transparent;
  scroll-behavior: smooth;
}

.emotion-0 .slick-slider .slick-track,
.emotion-0 .slick-slider .slick-list {
  -webkit-transform: translate3d(0, 0, 0);
  -moz-transform: translate3d(0, 0, 0);
  -ms-transform: translate3d(0, 0, 0);
  transform: translate3d(0, 0, 0);
}

.emotion-0 .slick-list {
  position: relative;
  display: block;
  overflow-x: hidden;
  overflow-y: hidden;
  margin: 0;
  padding: 0;
}

.emotion-0 .slick-list:focus {
  outline: none;
}

.emotion-0 .slick-list.dragging {
  cursor: pointer;
  cursor: hand;
}

.emotion-0 .slick-track {
  position: relative;
  top: 0;
  left: 0;
  display: block;
  margin-left: auto;
  margin-right: auto;
}

.emotion-0 .slick-track:before,
.emotion-0 .slick-track:after {
  display: table;
  content: "";
}

.emotion-0 .slick-track:after {
  clear: both;
}

.emotion-0 .slick-slide {
  width: 48px!important;
  display: none;
  float: left;
  height: 100%;
  min-height: 1px;
}

.emotion-0 .slick-slide img {
  display: block;
}

.emotion-0 .slick-slide.slick-loading img {
  display: none;
}

.emotion-0 .slick-slide.dragging img {
  pointer-events: none;
}

.emotion-0 [dir="rtl"] .slick-slide {
  float: right;
}

.emotion-0 .slick-initialized .slick-slide,
.emotion-0 .slick-vertical .slick-slide {
  display: block;
}

.emotion-0 .slick-vertical .slick-slide {
  border: 1px solid transparent;
  height: auto;
}

.emotion-0 .slick-loading .slick-track,
.emotion-0 .slick-loading .slick-slide {
  visibility: hidden;
}

.emotion-0 .slick-loading .slick-list {
  background: #fff url("./ajax-loader.gif") center center no-repeat;
}

.emotion-0 .slick-arrow.slick-hidden {
  display: none;
}

.emotion-0 .slick-prev,
.emotion-0 .slick-next {
  font-size: 0;
  line-height: 0;
  position: absolute;
  top: 50%;
  display: block;
  padding: 0;
  cursor: pointer;
  color: transparent;
  border: none;
  outline: none;
  background: transparent;
}

.emotion-0 .slick-prev:hover,
.emotion-0 .slick-next:hover,
.emotion-0 .slick-prev:focus,
.emotion-0 .slick-next:focus {
  color: transparent;
  outline: none;
  background: transparent;
  opacity: 1;
}

.emotion-0 .slick-prev.slick-disabled,
.emotion-0 .slick-next.slick-disabled {
  opacity: 0.25;
}

.emotion-0 .slick-prev {
  left: -25px;
  -webkit-transform: translate(0, -50%) rotate(90deg);
  -moz-transform: translate(0, -50%) rotate(90deg);
  -ms-transform: translate(0, -50%) rotate(90deg);
  transform: translate(0, -50%) rotate(90deg);
}

.emotion-0 .slick-prev span {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-0 [dir="rtl"] .slick-prev {
  right: -25px;
  left: auto;
}

.emotion-0 .slick-next {
  right: -25px;
  -webkit-transform: translate(0, -50%) rotate(-90deg);
  -moz-transform: translate(0, -50%) rotate(-90deg);
  -ms-transform: translate(0, -50%) rotate(-90deg);
  transform: translate(0, -50%) rotate(-90deg);
}

.emotion-0 .slick-next span {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-0 [dir="rtl"] .slick-next {
  right: auto;
  left: -25px;
}

.emotion-0 .slick-dotted.slick-slider {
  margin-bottom: 30px;
}

.emotion-0 .slick-dots {
  position: absolute;
  bottom: -25px;
  display: block;
  width: 100%;
  padding: 0;
  margin: 0;
  list-style: none;
  text-align: center;
}

.emotion-0 .slick-dots li {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: 20px;
  height: 20px;
  margin: 0 5px;
  padding: 0;
  cursor: pointer;
}

.emotion-0 .slick-dots li.slick-active button:before {
  opacity: 0.75;
  color: black;
}

.emotion-0 .slick-dots li button {
  font-size: 0;
  line-height: 0;
  display: block;
  padding: 5px;
  cursor: pointer;
  color: transparent;
  border: 0;
  outline: none;
  background: transparent;
}

.emotion-0 .slick-dots li button:hover,
.emotion-0 .slick-dots li button:focus {
  outline: none;
}

.emotion-0 .slick-dots li button:hover:before,
.emotion-0 .slick-dots li button:focus:before,
.emotion-0 .slick-dots li button:hover:before,
.emotion-0 .slick-dots li button:focus:before {
  opacity: 1;
}

.emotion-0 .slick-dots li button:before {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 5px;
  height: 5px;
  content: "";
  opacity: 0.25;
  background-color: black;
  border-radius: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

<div
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`CarouselContainer snapshots renders fade state correctly OldNavy in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  position: relative;
}

.emotion-0 .slick-slide {
  pointer-events: none;
}

.emotion-0 .slick-active {
  pointer-events: auto;
}

.emotion-0 .slick-slider {
  position: relative;
  display: block;
  box-sizing: border-box;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  -webkit-touch-callout: none;
  touch-action: pan-y;
  -webkit-tap-highlight-color: transparent;
  scroll-behavior: smooth;
}

.emotion-0 .slick-slider .slick-track,
.emotion-0 .slick-slider .slick-list {
  -webkit-transform: translate3d(0, 0, 0);
  -moz-transform: translate3d(0, 0, 0);
  -ms-transform: translate3d(0, 0, 0);
  transform: translate3d(0, 0, 0);
}

.emotion-0 .slick-list {
  position: relative;
  display: block;
  overflow-x: hidden;
  overflow-y: hidden;
  margin: 0;
  padding: 0;
}

.emotion-0 .slick-list:focus {
  outline: none;
}

.emotion-0 .slick-list.dragging {
  cursor: pointer;
  cursor: hand;
}

.emotion-0 .slick-track {
  position: relative;
  top: 0;
  left: 0;
  display: block;
  margin-left: auto;
  margin-right: auto;
}

.emotion-0 .slick-track:before,
.emotion-0 .slick-track:after {
  display: table;
  content: "";
}

.emotion-0 .slick-track:after {
  clear: both;
}

.emotion-0 .slick-slide {
  width: 48px!important;
  display: none;
  float: left;
  height: 100%;
  min-height: 1px;
}

.emotion-0 .slick-slide img {
  display: block;
}

.emotion-0 .slick-slide.slick-loading img {
  display: none;
}

.emotion-0 .slick-slide.dragging img {
  pointer-events: none;
}

.emotion-0 [dir="rtl"] .slick-slide {
  float: right;
}

.emotion-0 .slick-initialized .slick-slide,
.emotion-0 .slick-vertical .slick-slide {
  display: block;
}

.emotion-0 .slick-vertical .slick-slide {
  border: 1px solid transparent;
  height: auto;
}

.emotion-0 .slick-loading .slick-track,
.emotion-0 .slick-loading .slick-slide {
  visibility: hidden;
}

.emotion-0 .slick-loading .slick-list {
  background: #fff url("./ajax-loader.gif") center center no-repeat;
}

.emotion-0 .slick-arrow.slick-hidden {
  display: none;
}

.emotion-0 .slick-prev,
.emotion-0 .slick-next {
  font-size: 0;
  line-height: 0;
  position: absolute;
  top: 50%;
  display: block;
  padding: 0;
  cursor: pointer;
  color: transparent;
  border: none;
  outline: none;
  background: transparent;
}

.emotion-0 .slick-prev:hover,
.emotion-0 .slick-next:hover,
.emotion-0 .slick-prev:focus,
.emotion-0 .slick-next:focus {
  color: transparent;
  outline: none;
  background: transparent;
  opacity: 1;
}

.emotion-0 .slick-prev.slick-disabled,
.emotion-0 .slick-next.slick-disabled {
  opacity: 0.25;
}

.emotion-0 .slick-prev {
  left: -25px;
  -webkit-transform: translate(0, -50%) rotate(90deg);
  -moz-transform: translate(0, -50%) rotate(90deg);
  -ms-transform: translate(0, -50%) rotate(90deg);
  transform: translate(0, -50%) rotate(90deg);
}

.emotion-0 .slick-prev span {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-0 [dir="rtl"] .slick-prev {
  right: -25px;
  left: auto;
}

.emotion-0 .slick-next {
  right: -25px;
  -webkit-transform: translate(0, -50%) rotate(-90deg);
  -moz-transform: translate(0, -50%) rotate(-90deg);
  -ms-transform: translate(0, -50%) rotate(-90deg);
  transform: translate(0, -50%) rotate(-90deg);
}

.emotion-0 .slick-next span {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-0 [dir="rtl"] .slick-next {
  right: auto;
  left: -25px;
}

.emotion-0 .slick-dotted.slick-slider {
  margin-bottom: 30px;
}

.emotion-0 .slick-dots {
  position: absolute;
  bottom: -25px;
  display: block;
  width: 100%;
  padding: 0;
  margin: 0;
  list-style: none;
  text-align: center;
}

.emotion-0 .slick-dots li {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: 20px;
  height: 20px;
  margin: 0 5px;
  padding: 0;
  cursor: pointer;
}

.emotion-0 .slick-dots li.slick-active button:before {
  opacity: 0.75;
  color: black;
}

.emotion-0 .slick-dots li button {
  font-size: 0;
  line-height: 0;
  display: block;
  padding: 5px;
  cursor: pointer;
  color: transparent;
  border: 0;
  outline: none;
  background: transparent;
}

.emotion-0 .slick-dots li button:hover,
.emotion-0 .slick-dots li button:focus {
  outline: none;
}

.emotion-0 .slick-dots li button:hover:before,
.emotion-0 .slick-dots li button:focus:before,
.emotion-0 .slick-dots li button:hover:before,
.emotion-0 .slick-dots li button:focus:before {
  opacity: 1;
}

.emotion-0 .slick-dots li button:before {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 5px;
  height: 5px;
  content: "";
  opacity: 0.25;
  background-color: black;
  border-radius: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

<div
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`CarouselContainer snapshots renders isBR2023Swatches true state correctly Athleta 1`] = `
<DocumentFragment>
  .emotion-0 {
  position: relative;
}

.emotion-0 .slick-slider {
  position: relative;
  display: block;
  box-sizing: border-box;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  -webkit-touch-callout: none;
  touch-action: pan-y;
  -webkit-tap-highlight-color: transparent;
  scroll-behavior: smooth;
}

.emotion-0 .slick-slider .slick-track,
.emotion-0 .slick-slider .slick-list {
  -webkit-transform: translate3d(0, 0, 0);
  -moz-transform: translate3d(0, 0, 0);
  -ms-transform: translate3d(0, 0, 0);
  transform: translate3d(0, 0, 0);
  height: 32px;
}

.emotion-0 .slick-list {
  position: relative;
  display: block;
  overflow-x: hidden;
  overflow-y: hidden;
  margin: 0;
  padding: 0;
}

.emotion-0 .slick-list:focus {
  outline: none;
}

.emotion-0 .slick-list.dragging {
  cursor: pointer;
  cursor: hand;
}

.emotion-0 .slick-track {
  position: relative;
  top: 0;
  left: 0;
  display: block;
  margin-left: auto;
  margin-right: auto;
}

.emotion-0 .slick-track:before,
.emotion-0 .slick-track:after {
  display: table;
  content: "";
}

.emotion-0 .slick-track:after {
  clear: both;
}

.emotion-0 .slick-slide {
  width: 48px!important;
  display: none;
  float: left;
  height: 100%;
  min-height: 1px;
}

.emotion-0 .slick-slide img {
  display: block;
}

.emotion-0 .slick-slide.slick-loading img {
  display: none;
}

.emotion-0 .slick-slide.dragging img {
  pointer-events: none;
}

.emotion-0 [dir="rtl"] .slick-slide {
  float: right;
}

.emotion-0 .slick-initialized .slick-slide,
.emotion-0 .slick-vertical .slick-slide {
  display: block;
}

.emotion-0 .slick-vertical .slick-slide {
  border: 1px solid transparent;
  height: auto;
}

.emotion-0 .slick-loading .slick-track,
.emotion-0 .slick-loading .slick-slide {
  visibility: hidden;
}

.emotion-0 .slick-loading .slick-list {
  background: #fff url("./ajax-loader.gif") center center no-repeat;
}

.emotion-0 .slick-arrow.slick-hidden {
  display: none;
}

.emotion-0 .slick-prev,
.emotion-0 .slick-next {
  font-size: 0;
  line-height: 0;
  position: absolute;
  top: 50%;
  display: block;
  padding: 0;
  cursor: pointer;
  color: transparent;
  border: none;
  outline: none;
  background: transparent;
}

.emotion-0 .slick-prev:hover,
.emotion-0 .slick-next:hover,
.emotion-0 .slick-prev:focus,
.emotion-0 .slick-next:focus {
  color: transparent;
  outline: none;
  background: transparent;
  opacity: 1;
}

.emotion-0 .slick-prev.slick-disabled,
.emotion-0 .slick-next.slick-disabled {
  opacity: 0.25;
}

.emotion-0 .slick-prev {
  left: -25px;
  -webkit-transform: translate(0, -50%) rotate(90deg);
  -moz-transform: translate(0, -50%) rotate(90deg);
  -ms-transform: translate(0, -50%) rotate(90deg);
  transform: translate(0, -50%) rotate(90deg);
}

.emotion-0 .slick-prev span {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-0 [dir="rtl"] .slick-prev {
  right: -25px;
  left: auto;
}

.emotion-0 .slick-next {
  right: -25px;
  -webkit-transform: translate(0, -50%) rotate(-90deg);
  -moz-transform: translate(0, -50%) rotate(-90deg);
  -ms-transform: translate(0, -50%) rotate(-90deg);
  transform: translate(0, -50%) rotate(-90deg);
}

.emotion-0 .slick-next span {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-0 [dir="rtl"] .slick-next {
  right: auto;
  left: -25px;
}

.emotion-0 .slick-dotted.slick-slider {
  margin-bottom: 30px;
}

.emotion-0 .slick-dots {
  position: absolute;
  bottom: -25px;
  display: block;
  width: 100%;
  padding: 0;
  margin: 0;
  list-style: none;
  text-align: center;
}

.emotion-0 .slick-dots li {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: 20px;
  height: 20px;
  margin: 0 5px;
  padding: 0;
  cursor: pointer;
}

.emotion-0 .slick-dots li.slick-active button:before {
  opacity: 0.75;
  color: black;
}

.emotion-0 .slick-dots li button {
  font-size: 0;
  line-height: 0;
  display: block;
  padding: 5px;
  cursor: pointer;
  color: transparent;
  border: 0;
  outline: none;
  background: transparent;
}

.emotion-0 .slick-dots li button:hover,
.emotion-0 .slick-dots li button:focus {
  outline: none;
}

.emotion-0 .slick-dots li button:hover:before,
.emotion-0 .slick-dots li button:focus:before,
.emotion-0 .slick-dots li button:hover:before,
.emotion-0 .slick-dots li button:focus:before {
  opacity: 1;
}

.emotion-0 .slick-dots li button:before {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 5px;
  height: 5px;
  content: "";
  opacity: 0.25;
  background-color: black;
  border-radius: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

<div
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`CarouselContainer snapshots renders isBR2023Swatches true state correctly Athleta in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  position: relative;
}

.emotion-0 .slick-slider {
  position: relative;
  display: block;
  box-sizing: border-box;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  -webkit-touch-callout: none;
  touch-action: pan-y;
  -webkit-tap-highlight-color: transparent;
  scroll-behavior: smooth;
}

.emotion-0 .slick-slider .slick-track,
.emotion-0 .slick-slider .slick-list {
  -webkit-transform: translate3d(0, 0, 0);
  -moz-transform: translate3d(0, 0, 0);
  -ms-transform: translate3d(0, 0, 0);
  transform: translate3d(0, 0, 0);
  height: 32px;
}

.emotion-0 .slick-list {
  position: relative;
  display: block;
  overflow-x: hidden;
  overflow-y: hidden;
  margin: 0;
  padding: 0;
}

.emotion-0 .slick-list:focus {
  outline: none;
}

.emotion-0 .slick-list.dragging {
  cursor: pointer;
  cursor: hand;
}

.emotion-0 .slick-track {
  position: relative;
  top: 0;
  left: 0;
  display: block;
  margin-left: auto;
  margin-right: auto;
}

.emotion-0 .slick-track:before,
.emotion-0 .slick-track:after {
  display: table;
  content: "";
}

.emotion-0 .slick-track:after {
  clear: both;
}

.emotion-0 .slick-slide {
  width: 48px!important;
  display: none;
  float: left;
  height: 100%;
  min-height: 1px;
}

.emotion-0 .slick-slide img {
  display: block;
}

.emotion-0 .slick-slide.slick-loading img {
  display: none;
}

.emotion-0 .slick-slide.dragging img {
  pointer-events: none;
}

.emotion-0 [dir="rtl"] .slick-slide {
  float: right;
}

.emotion-0 .slick-initialized .slick-slide,
.emotion-0 .slick-vertical .slick-slide {
  display: block;
}

.emotion-0 .slick-vertical .slick-slide {
  border: 1px solid transparent;
  height: auto;
}

.emotion-0 .slick-loading .slick-track,
.emotion-0 .slick-loading .slick-slide {
  visibility: hidden;
}

.emotion-0 .slick-loading .slick-list {
  background: #fff url("./ajax-loader.gif") center center no-repeat;
}

.emotion-0 .slick-arrow.slick-hidden {
  display: none;
}

.emotion-0 .slick-prev,
.emotion-0 .slick-next {
  font-size: 0;
  line-height: 0;
  position: absolute;
  top: 50%;
  display: block;
  padding: 0;
  cursor: pointer;
  color: transparent;
  border: none;
  outline: none;
  background: transparent;
}

.emotion-0 .slick-prev:hover,
.emotion-0 .slick-next:hover,
.emotion-0 .slick-prev:focus,
.emotion-0 .slick-next:focus {
  color: transparent;
  outline: none;
  background: transparent;
  opacity: 1;
}

.emotion-0 .slick-prev.slick-disabled,
.emotion-0 .slick-next.slick-disabled {
  opacity: 0.25;
}

.emotion-0 .slick-prev {
  left: -25px;
  -webkit-transform: translate(0, -50%) rotate(90deg);
  -moz-transform: translate(0, -50%) rotate(90deg);
  -ms-transform: translate(0, -50%) rotate(90deg);
  transform: translate(0, -50%) rotate(90deg);
}

.emotion-0 .slick-prev span {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-0 [dir="rtl"] .slick-prev {
  right: -25px;
  left: auto;
}

.emotion-0 .slick-next {
  right: -25px;
  -webkit-transform: translate(0, -50%) rotate(-90deg);
  -moz-transform: translate(0, -50%) rotate(-90deg);
  -ms-transform: translate(0, -50%) rotate(-90deg);
  transform: translate(0, -50%) rotate(-90deg);
}

.emotion-0 .slick-next span {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-0 [dir="rtl"] .slick-next {
  right: auto;
  left: -25px;
}

.emotion-0 .slick-dotted.slick-slider {
  margin-bottom: 30px;
}

.emotion-0 .slick-dots {
  position: absolute;
  bottom: -25px;
  display: block;
  width: 100%;
  padding: 0;
  margin: 0;
  list-style: none;
  text-align: center;
}

.emotion-0 .slick-dots li {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: 20px;
  height: 20px;
  margin: 0 5px;
  padding: 0;
  cursor: pointer;
}

.emotion-0 .slick-dots li.slick-active button:before {
  opacity: 0.75;
  color: black;
}

.emotion-0 .slick-dots li button {
  font-size: 0;
  line-height: 0;
  display: block;
  padding: 5px;
  cursor: pointer;
  color: transparent;
  border: 0;
  outline: none;
  background: transparent;
}

.emotion-0 .slick-dots li button:hover,
.emotion-0 .slick-dots li button:focus {
  outline: none;
}

.emotion-0 .slick-dots li button:hover:before,
.emotion-0 .slick-dots li button:focus:before,
.emotion-0 .slick-dots li button:hover:before,
.emotion-0 .slick-dots li button:focus:before {
  opacity: 1;
}

.emotion-0 .slick-dots li button:before {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 5px;
  height: 5px;
  content: "";
  opacity: 0.25;
  background-color: black;
  border-radius: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

<div
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`CarouselContainer snapshots renders isBR2023Swatches true state correctly BananaRepublic 1`] = `
<DocumentFragment>
  .emotion-0 {
  position: relative;
}

.emotion-0 .slick-slider {
  position: relative;
  display: block;
  box-sizing: border-box;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  -webkit-touch-callout: none;
  touch-action: pan-y;
  -webkit-tap-highlight-color: transparent;
  scroll-behavior: smooth;
}

.emotion-0 .slick-slider .slick-track,
.emotion-0 .slick-slider .slick-list {
  -webkit-transform: translate3d(0, 0, 0);
  -moz-transform: translate3d(0, 0, 0);
  -ms-transform: translate3d(0, 0, 0);
  transform: translate3d(0, 0, 0);
  height: 32px;
}

.emotion-0 .slick-list {
  position: relative;
  display: block;
  overflow-x: hidden;
  overflow-y: hidden;
  margin: 0;
  padding: 0;
}

.emotion-0 .slick-list:focus {
  outline: none;
}

.emotion-0 .slick-list.dragging {
  cursor: pointer;
  cursor: hand;
}

.emotion-0 .slick-track {
  position: relative;
  top: 0;
  left: 0;
  display: block;
  margin-left: auto;
  margin-right: auto;
}

.emotion-0 .slick-track:before,
.emotion-0 .slick-track:after {
  display: table;
  content: "";
}

.emotion-0 .slick-track:after {
  clear: both;
}

.emotion-0 .slick-slide {
  width: 48px!important;
  display: none;
  float: left;
  height: 100%;
  min-height: 1px;
}

.emotion-0 .slick-slide img {
  display: block;
}

.emotion-0 .slick-slide.slick-loading img {
  display: none;
}

.emotion-0 .slick-slide.dragging img {
  pointer-events: none;
}

.emotion-0 [dir="rtl"] .slick-slide {
  float: right;
}

.emotion-0 .slick-initialized .slick-slide,
.emotion-0 .slick-vertical .slick-slide {
  display: block;
}

.emotion-0 .slick-vertical .slick-slide {
  border: 1px solid transparent;
  height: auto;
}

.emotion-0 .slick-loading .slick-track,
.emotion-0 .slick-loading .slick-slide {
  visibility: hidden;
}

.emotion-0 .slick-loading .slick-list {
  background: #fff url("./ajax-loader.gif") center center no-repeat;
}

.emotion-0 .slick-arrow.slick-hidden {
  display: none;
}

.emotion-0 .slick-prev,
.emotion-0 .slick-next {
  font-size: 0;
  line-height: 0;
  position: absolute;
  top: 50%;
  display: block;
  padding: 0;
  cursor: pointer;
  color: transparent;
  border: none;
  outline: none;
  background: transparent;
}

.emotion-0 .slick-prev:hover,
.emotion-0 .slick-next:hover,
.emotion-0 .slick-prev:focus,
.emotion-0 .slick-next:focus {
  color: transparent;
  outline: none;
  background: transparent;
  opacity: 1;
}

.emotion-0 .slick-prev.slick-disabled,
.emotion-0 .slick-next.slick-disabled {
  opacity: 0.25;
}

.emotion-0 .slick-prev {
  left: -25px;
  -webkit-transform: translate(0, -50%) rotate(90deg);
  -moz-transform: translate(0, -50%) rotate(90deg);
  -ms-transform: translate(0, -50%) rotate(90deg);
  transform: translate(0, -50%) rotate(90deg);
}

.emotion-0 .slick-prev span {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-0 [dir="rtl"] .slick-prev {
  right: -25px;
  left: auto;
}

.emotion-0 .slick-next {
  right: -25px;
  -webkit-transform: translate(0, -50%) rotate(-90deg);
  -moz-transform: translate(0, -50%) rotate(-90deg);
  -ms-transform: translate(0, -50%) rotate(-90deg);
  transform: translate(0, -50%) rotate(-90deg);
}

.emotion-0 .slick-next span {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-0 [dir="rtl"] .slick-next {
  right: auto;
  left: -25px;
}

.emotion-0 .slick-dotted.slick-slider {
  margin-bottom: 30px;
}

.emotion-0 .slick-dots {
  position: absolute;
  bottom: -25px;
  display: block;
  width: 100%;
  padding: 0;
  margin: 0;
  list-style: none;
  text-align: center;
}

.emotion-0 .slick-dots li {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: 20px;
  height: 20px;
  margin: 0 5px;
  padding: 0;
  cursor: pointer;
}

.emotion-0 .slick-dots li.slick-active button:before {
  opacity: 0.75;
  color: black;
}

.emotion-0 .slick-dots li button {
  font-size: 0;
  line-height: 0;
  display: block;
  padding: 5px;
  cursor: pointer;
  color: transparent;
  border: 0;
  outline: none;
  background: transparent;
}

.emotion-0 .slick-dots li button:hover,
.emotion-0 .slick-dots li button:focus {
  outline: none;
}

.emotion-0 .slick-dots li button:hover:before,
.emotion-0 .slick-dots li button:focus:before,
.emotion-0 .slick-dots li button:hover:before,
.emotion-0 .slick-dots li button:focus:before {
  opacity: 1;
}

.emotion-0 .slick-dots li button:before {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 5px;
  height: 5px;
  content: "";
  opacity: 0.25;
  background-color: black;
  border-radius: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

<div
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`CarouselContainer snapshots renders isBR2023Swatches true state correctly BananaRepublic in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  position: relative;
}

.emotion-0 .slick-slider {
  position: relative;
  display: block;
  box-sizing: border-box;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  -webkit-touch-callout: none;
  touch-action: pan-y;
  -webkit-tap-highlight-color: transparent;
  scroll-behavior: smooth;
}

.emotion-0 .slick-slider .slick-track,
.emotion-0 .slick-slider .slick-list {
  -webkit-transform: translate3d(0, 0, 0);
  -moz-transform: translate3d(0, 0, 0);
  -ms-transform: translate3d(0, 0, 0);
  transform: translate3d(0, 0, 0);
  height: 32px;
}

.emotion-0 .slick-list {
  position: relative;
  display: block;
  overflow-x: hidden;
  overflow-y: hidden;
  margin: 0;
  padding: 0;
}

.emotion-0 .slick-list:focus {
  outline: none;
}

.emotion-0 .slick-list.dragging {
  cursor: pointer;
  cursor: hand;
}

.emotion-0 .slick-track {
  position: relative;
  top: 0;
  left: 0;
  display: block;
  margin-left: auto;
  margin-right: auto;
}

.emotion-0 .slick-track:before,
.emotion-0 .slick-track:after {
  display: table;
  content: "";
}

.emotion-0 .slick-track:after {
  clear: both;
}

.emotion-0 .slick-slide {
  width: 48px!important;
  display: none;
  float: left;
  height: 100%;
  min-height: 1px;
}

.emotion-0 .slick-slide img {
  display: block;
}

.emotion-0 .slick-slide.slick-loading img {
  display: none;
}

.emotion-0 .slick-slide.dragging img {
  pointer-events: none;
}

.emotion-0 [dir="rtl"] .slick-slide {
  float: right;
}

.emotion-0 .slick-initialized .slick-slide,
.emotion-0 .slick-vertical .slick-slide {
  display: block;
}

.emotion-0 .slick-vertical .slick-slide {
  border: 1px solid transparent;
  height: auto;
}

.emotion-0 .slick-loading .slick-track,
.emotion-0 .slick-loading .slick-slide {
  visibility: hidden;
}

.emotion-0 .slick-loading .slick-list {
  background: #fff url("./ajax-loader.gif") center center no-repeat;
}

.emotion-0 .slick-arrow.slick-hidden {
  display: none;
}

.emotion-0 .slick-prev,
.emotion-0 .slick-next {
  font-size: 0;
  line-height: 0;
  position: absolute;
  top: 50%;
  display: block;
  padding: 0;
  cursor: pointer;
  color: transparent;
  border: none;
  outline: none;
  background: transparent;
}

.emotion-0 .slick-prev:hover,
.emotion-0 .slick-next:hover,
.emotion-0 .slick-prev:focus,
.emotion-0 .slick-next:focus {
  color: transparent;
  outline: none;
  background: transparent;
  opacity: 1;
}

.emotion-0 .slick-prev.slick-disabled,
.emotion-0 .slick-next.slick-disabled {
  opacity: 0.25;
}

.emotion-0 .slick-prev {
  left: -25px;
  -webkit-transform: translate(0, -50%) rotate(90deg);
  -moz-transform: translate(0, -50%) rotate(90deg);
  -ms-transform: translate(0, -50%) rotate(90deg);
  transform: translate(0, -50%) rotate(90deg);
}

.emotion-0 .slick-prev span {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-0 [dir="rtl"] .slick-prev {
  right: -25px;
  left: auto;
}

.emotion-0 .slick-next {
  right: -25px;
  -webkit-transform: translate(0, -50%) rotate(-90deg);
  -moz-transform: translate(0, -50%) rotate(-90deg);
  -ms-transform: translate(0, -50%) rotate(-90deg);
  transform: translate(0, -50%) rotate(-90deg);
}

.emotion-0 .slick-next span {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-0 [dir="rtl"] .slick-next {
  right: auto;
  left: -25px;
}

.emotion-0 .slick-dotted.slick-slider {
  margin-bottom: 30px;
}

.emotion-0 .slick-dots {
  position: absolute;
  bottom: -25px;
  display: block;
  width: 100%;
  padding: 0;
  margin: 0;
  list-style: none;
  text-align: center;
}

.emotion-0 .slick-dots li {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: 20px;
  height: 20px;
  margin: 0 5px;
  padding: 0;
  cursor: pointer;
}

.emotion-0 .slick-dots li.slick-active button:before {
  opacity: 0.75;
  color: black;
}

.emotion-0 .slick-dots li button {
  font-size: 0;
  line-height: 0;
  display: block;
  padding: 5px;
  cursor: pointer;
  color: transparent;
  border: 0;
  outline: none;
  background: transparent;
}

.emotion-0 .slick-dots li button:hover,
.emotion-0 .slick-dots li button:focus {
  outline: none;
}

.emotion-0 .slick-dots li button:hover:before,
.emotion-0 .slick-dots li button:focus:before,
.emotion-0 .slick-dots li button:hover:before,
.emotion-0 .slick-dots li button:focus:before {
  opacity: 1;
}

.emotion-0 .slick-dots li button:before {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 5px;
  height: 5px;
  content: "";
  opacity: 0.25;
  background-color: black;
  border-radius: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

<div
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`CarouselContainer snapshots renders isBR2023Swatches true state correctly BananaRepublicFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  position: relative;
}

.emotion-0 .slick-slider {
  position: relative;
  display: block;
  box-sizing: border-box;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  -webkit-touch-callout: none;
  touch-action: pan-y;
  -webkit-tap-highlight-color: transparent;
  scroll-behavior: smooth;
}

.emotion-0 .slick-slider .slick-track,
.emotion-0 .slick-slider .slick-list {
  -webkit-transform: translate3d(0, 0, 0);
  -moz-transform: translate3d(0, 0, 0);
  -ms-transform: translate3d(0, 0, 0);
  transform: translate3d(0, 0, 0);
  height: 32px;
}

.emotion-0 .slick-list {
  position: relative;
  display: block;
  overflow-x: hidden;
  overflow-y: hidden;
  margin: 0;
  padding: 0;
}

.emotion-0 .slick-list:focus {
  outline: none;
}

.emotion-0 .slick-list.dragging {
  cursor: pointer;
  cursor: hand;
}

.emotion-0 .slick-track {
  position: relative;
  top: 0;
  left: 0;
  display: block;
  margin-left: auto;
  margin-right: auto;
}

.emotion-0 .slick-track:before,
.emotion-0 .slick-track:after {
  display: table;
  content: "";
}

.emotion-0 .slick-track:after {
  clear: both;
}

.emotion-0 .slick-slide {
  width: 48px!important;
  display: none;
  float: left;
  height: 100%;
  min-height: 1px;
}

.emotion-0 .slick-slide img {
  display: block;
}

.emotion-0 .slick-slide.slick-loading img {
  display: none;
}

.emotion-0 .slick-slide.dragging img {
  pointer-events: none;
}

.emotion-0 [dir="rtl"] .slick-slide {
  float: right;
}

.emotion-0 .slick-initialized .slick-slide,
.emotion-0 .slick-vertical .slick-slide {
  display: block;
}

.emotion-0 .slick-vertical .slick-slide {
  border: 1px solid transparent;
  height: auto;
}

.emotion-0 .slick-loading .slick-track,
.emotion-0 .slick-loading .slick-slide {
  visibility: hidden;
}

.emotion-0 .slick-loading .slick-list {
  background: #fff url("./ajax-loader.gif") center center no-repeat;
}

.emotion-0 .slick-arrow.slick-hidden {
  display: none;
}

.emotion-0 .slick-prev,
.emotion-0 .slick-next {
  font-size: 0;
  line-height: 0;
  position: absolute;
  top: 50%;
  display: block;
  padding: 0;
  cursor: pointer;
  color: transparent;
  border: none;
  outline: none;
  background: transparent;
}

.emotion-0 .slick-prev:hover,
.emotion-0 .slick-next:hover,
.emotion-0 .slick-prev:focus,
.emotion-0 .slick-next:focus {
  color: transparent;
  outline: none;
  background: transparent;
  opacity: 1;
}

.emotion-0 .slick-prev.slick-disabled,
.emotion-0 .slick-next.slick-disabled {
  opacity: 0.25;
}

.emotion-0 .slick-prev {
  left: -25px;
  -webkit-transform: translate(0, -50%) rotate(90deg);
  -moz-transform: translate(0, -50%) rotate(90deg);
  -ms-transform: translate(0, -50%) rotate(90deg);
  transform: translate(0, -50%) rotate(90deg);
}

.emotion-0 .slick-prev span {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-0 [dir="rtl"] .slick-prev {
  right: -25px;
  left: auto;
}

.emotion-0 .slick-next {
  right: -25px;
  -webkit-transform: translate(0, -50%) rotate(-90deg);
  -moz-transform: translate(0, -50%) rotate(-90deg);
  -ms-transform: translate(0, -50%) rotate(-90deg);
  transform: translate(0, -50%) rotate(-90deg);
}

.emotion-0 .slick-next span {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-0 [dir="rtl"] .slick-next {
  right: auto;
  left: -25px;
}

.emotion-0 .slick-dotted.slick-slider {
  margin-bottom: 30px;
}

.emotion-0 .slick-dots {
  position: absolute;
  bottom: -25px;
  display: block;
  width: 100%;
  padding: 0;
  margin: 0;
  list-style: none;
  text-align: center;
}

.emotion-0 .slick-dots li {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: 20px;
  height: 20px;
  margin: 0 5px;
  padding: 0;
  cursor: pointer;
}

.emotion-0 .slick-dots li.slick-active button:before {
  opacity: 0.75;
  color: black;
}

.emotion-0 .slick-dots li button {
  font-size: 0;
  line-height: 0;
  display: block;
  padding: 5px;
  cursor: pointer;
  color: transparent;
  border: 0;
  outline: none;
  background: transparent;
}

.emotion-0 .slick-dots li button:hover,
.emotion-0 .slick-dots li button:focus {
  outline: none;
}

.emotion-0 .slick-dots li button:hover:before,
.emotion-0 .slick-dots li button:focus:before,
.emotion-0 .slick-dots li button:hover:before,
.emotion-0 .slick-dots li button:focus:before {
  opacity: 1;
}

.emotion-0 .slick-dots li button:before {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 5px;
  height: 5px;
  content: "";
  opacity: 0.25;
  background-color: black;
  border-radius: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

<div
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`CarouselContainer snapshots renders isBR2023Swatches true state correctly BananaRepublicFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  position: relative;
}

.emotion-0 .slick-slider {
  position: relative;
  display: block;
  box-sizing: border-box;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  -webkit-touch-callout: none;
  touch-action: pan-y;
  -webkit-tap-highlight-color: transparent;
  scroll-behavior: smooth;
}

.emotion-0 .slick-slider .slick-track,
.emotion-0 .slick-slider .slick-list {
  -webkit-transform: translate3d(0, 0, 0);
  -moz-transform: translate3d(0, 0, 0);
  -ms-transform: translate3d(0, 0, 0);
  transform: translate3d(0, 0, 0);
  height: 32px;
}

.emotion-0 .slick-list {
  position: relative;
  display: block;
  overflow-x: hidden;
  overflow-y: hidden;
  margin: 0;
  padding: 0;
}

.emotion-0 .slick-list:focus {
  outline: none;
}

.emotion-0 .slick-list.dragging {
  cursor: pointer;
  cursor: hand;
}

.emotion-0 .slick-track {
  position: relative;
  top: 0;
  left: 0;
  display: block;
  margin-left: auto;
  margin-right: auto;
}

.emotion-0 .slick-track:before,
.emotion-0 .slick-track:after {
  display: table;
  content: "";
}

.emotion-0 .slick-track:after {
  clear: both;
}

.emotion-0 .slick-slide {
  width: 48px!important;
  display: none;
  float: left;
  height: 100%;
  min-height: 1px;
}

.emotion-0 .slick-slide img {
  display: block;
}

.emotion-0 .slick-slide.slick-loading img {
  display: none;
}

.emotion-0 .slick-slide.dragging img {
  pointer-events: none;
}

.emotion-0 [dir="rtl"] .slick-slide {
  float: right;
}

.emotion-0 .slick-initialized .slick-slide,
.emotion-0 .slick-vertical .slick-slide {
  display: block;
}

.emotion-0 .slick-vertical .slick-slide {
  border: 1px solid transparent;
  height: auto;
}

.emotion-0 .slick-loading .slick-track,
.emotion-0 .slick-loading .slick-slide {
  visibility: hidden;
}

.emotion-0 .slick-loading .slick-list {
  background: #fff url("./ajax-loader.gif") center center no-repeat;
}

.emotion-0 .slick-arrow.slick-hidden {
  display: none;
}

.emotion-0 .slick-prev,
.emotion-0 .slick-next {
  font-size: 0;
  line-height: 0;
  position: absolute;
  top: 50%;
  display: block;
  padding: 0;
  cursor: pointer;
  color: transparent;
  border: none;
  outline: none;
  background: transparent;
}

.emotion-0 .slick-prev:hover,
.emotion-0 .slick-next:hover,
.emotion-0 .slick-prev:focus,
.emotion-0 .slick-next:focus {
  color: transparent;
  outline: none;
  background: transparent;
  opacity: 1;
}

.emotion-0 .slick-prev.slick-disabled,
.emotion-0 .slick-next.slick-disabled {
  opacity: 0.25;
}

.emotion-0 .slick-prev {
  left: -25px;
  -webkit-transform: translate(0, -50%) rotate(90deg);
  -moz-transform: translate(0, -50%) rotate(90deg);
  -ms-transform: translate(0, -50%) rotate(90deg);
  transform: translate(0, -50%) rotate(90deg);
}

.emotion-0 .slick-prev span {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-0 [dir="rtl"] .slick-prev {
  right: -25px;
  left: auto;
}

.emotion-0 .slick-next {
  right: -25px;
  -webkit-transform: translate(0, -50%) rotate(-90deg);
  -moz-transform: translate(0, -50%) rotate(-90deg);
  -ms-transform: translate(0, -50%) rotate(-90deg);
  transform: translate(0, -50%) rotate(-90deg);
}

.emotion-0 .slick-next span {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-0 [dir="rtl"] .slick-next {
  right: auto;
  left: -25px;
}

.emotion-0 .slick-dotted.slick-slider {
  margin-bottom: 30px;
}

.emotion-0 .slick-dots {
  position: absolute;
  bottom: -25px;
  display: block;
  width: 100%;
  padding: 0;
  margin: 0;
  list-style: none;
  text-align: center;
}

.emotion-0 .slick-dots li {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: 20px;
  height: 20px;
  margin: 0 5px;
  padding: 0;
  cursor: pointer;
}

.emotion-0 .slick-dots li.slick-active button:before {
  opacity: 0.75;
  color: black;
}

.emotion-0 .slick-dots li button {
  font-size: 0;
  line-height: 0;
  display: block;
  padding: 5px;
  cursor: pointer;
  color: transparent;
  border: 0;
  outline: none;
  background: transparent;
}

.emotion-0 .slick-dots li button:hover,
.emotion-0 .slick-dots li button:focus {
  outline: none;
}

.emotion-0 .slick-dots li button:hover:before,
.emotion-0 .slick-dots li button:focus:before,
.emotion-0 .slick-dots li button:hover:before,
.emotion-0 .slick-dots li button:focus:before {
  opacity: 1;
}

.emotion-0 .slick-dots li button:before {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 5px;
  height: 5px;
  content: "";
  opacity: 0.25;
  background-color: black;
  border-radius: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

<div
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`CarouselContainer snapshots renders isBR2023Swatches true state correctly Gap 1`] = `
<DocumentFragment>
  .emotion-0 {
  position: relative;
}

.emotion-0 .slick-slider {
  position: relative;
  display: block;
  box-sizing: border-box;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  -webkit-touch-callout: none;
  touch-action: pan-y;
  -webkit-tap-highlight-color: transparent;
  scroll-behavior: smooth;
}

.emotion-0 .slick-slider .slick-track,
.emotion-0 .slick-slider .slick-list {
  -webkit-transform: translate3d(0, 0, 0);
  -moz-transform: translate3d(0, 0, 0);
  -ms-transform: translate3d(0, 0, 0);
  transform: translate3d(0, 0, 0);
  height: 32px;
}

.emotion-0 .slick-list {
  position: relative;
  display: block;
  overflow-x: hidden;
  overflow-y: hidden;
  margin: 0;
  padding: 0;
}

.emotion-0 .slick-list:focus {
  outline: none;
}

.emotion-0 .slick-list.dragging {
  cursor: pointer;
  cursor: hand;
}

.emotion-0 .slick-track {
  position: relative;
  top: 0;
  left: 0;
  display: block;
  margin-left: auto;
  margin-right: auto;
}

.emotion-0 .slick-track:before,
.emotion-0 .slick-track:after {
  display: table;
  content: "";
}

.emotion-0 .slick-track:after {
  clear: both;
}

.emotion-0 .slick-slide {
  width: 48px!important;
  display: none;
  float: left;
  height: 100%;
  min-height: 1px;
}

.emotion-0 .slick-slide img {
  display: block;
}

.emotion-0 .slick-slide.slick-loading img {
  display: none;
}

.emotion-0 .slick-slide.dragging img {
  pointer-events: none;
}

.emotion-0 [dir="rtl"] .slick-slide {
  float: right;
}

.emotion-0 .slick-initialized .slick-slide,
.emotion-0 .slick-vertical .slick-slide {
  display: block;
}

.emotion-0 .slick-vertical .slick-slide {
  border: 1px solid transparent;
  height: auto;
}

.emotion-0 .slick-loading .slick-track,
.emotion-0 .slick-loading .slick-slide {
  visibility: hidden;
}

.emotion-0 .slick-loading .slick-list {
  background: #fff url("./ajax-loader.gif") center center no-repeat;
}

.emotion-0 .slick-arrow.slick-hidden {
  display: none;
}

.emotion-0 .slick-prev,
.emotion-0 .slick-next {
  font-size: 0;
  line-height: 0;
  position: absolute;
  top: 50%;
  display: block;
  padding: 0;
  cursor: pointer;
  color: transparent;
  border: none;
  outline: none;
  background: transparent;
}

.emotion-0 .slick-prev:hover,
.emotion-0 .slick-next:hover,
.emotion-0 .slick-prev:focus,
.emotion-0 .slick-next:focus {
  color: transparent;
  outline: none;
  background: transparent;
  opacity: 1;
}

.emotion-0 .slick-prev.slick-disabled,
.emotion-0 .slick-next.slick-disabled {
  opacity: 0.25;
}

.emotion-0 .slick-prev {
  left: -25px;
  -webkit-transform: translate(0, -50%) rotate(90deg);
  -moz-transform: translate(0, -50%) rotate(90deg);
  -ms-transform: translate(0, -50%) rotate(90deg);
  transform: translate(0, -50%) rotate(90deg);
}

.emotion-0 .slick-prev span {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-0 [dir="rtl"] .slick-prev {
  right: -25px;
  left: auto;
}

.emotion-0 .slick-next {
  right: -25px;
  -webkit-transform: translate(0, -50%) rotate(-90deg);
  -moz-transform: translate(0, -50%) rotate(-90deg);
  -ms-transform: translate(0, -50%) rotate(-90deg);
  transform: translate(0, -50%) rotate(-90deg);
}

.emotion-0 .slick-next span {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-0 [dir="rtl"] .slick-next {
  right: auto;
  left: -25px;
}

.emotion-0 .slick-dotted.slick-slider {
  margin-bottom: 30px;
}

.emotion-0 .slick-dots {
  position: absolute;
  bottom: -25px;
  display: block;
  width: 100%;
  padding: 0;
  margin: 0;
  list-style: none;
  text-align: center;
}

.emotion-0 .slick-dots li {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: 20px;
  height: 20px;
  margin: 0 5px;
  padding: 0;
  cursor: pointer;
}

.emotion-0 .slick-dots li.slick-active button:before {
  opacity: 0.75;
  color: black;
}

.emotion-0 .slick-dots li button {
  font-size: 0;
  line-height: 0;
  display: block;
  padding: 5px;
  cursor: pointer;
  color: transparent;
  border: 0;
  outline: none;
  background: transparent;
}

.emotion-0 .slick-dots li button:hover,
.emotion-0 .slick-dots li button:focus {
  outline: none;
}

.emotion-0 .slick-dots li button:hover:before,
.emotion-0 .slick-dots li button:focus:before,
.emotion-0 .slick-dots li button:hover:before,
.emotion-0 .slick-dots li button:focus:before {
  opacity: 1;
}

.emotion-0 .slick-dots li button:before {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 5px;
  height: 5px;
  content: "";
  opacity: 0.25;
  background-color: black;
  border-radius: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

<div
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`CarouselContainer snapshots renders isBR2023Swatches true state correctly Gap in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  position: relative;
}

.emotion-0 .slick-slider {
  position: relative;
  display: block;
  box-sizing: border-box;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  -webkit-touch-callout: none;
  touch-action: pan-y;
  -webkit-tap-highlight-color: transparent;
  scroll-behavior: smooth;
}

.emotion-0 .slick-slider .slick-track,
.emotion-0 .slick-slider .slick-list {
  -webkit-transform: translate3d(0, 0, 0);
  -moz-transform: translate3d(0, 0, 0);
  -ms-transform: translate3d(0, 0, 0);
  transform: translate3d(0, 0, 0);
  height: 32px;
}

.emotion-0 .slick-list {
  position: relative;
  display: block;
  overflow-x: hidden;
  overflow-y: hidden;
  margin: 0;
  padding: 0;
}

.emotion-0 .slick-list:focus {
  outline: none;
}

.emotion-0 .slick-list.dragging {
  cursor: pointer;
  cursor: hand;
}

.emotion-0 .slick-track {
  position: relative;
  top: 0;
  left: 0;
  display: block;
  margin-left: auto;
  margin-right: auto;
}

.emotion-0 .slick-track:before,
.emotion-0 .slick-track:after {
  display: table;
  content: "";
}

.emotion-0 .slick-track:after {
  clear: both;
}

.emotion-0 .slick-slide {
  width: 48px!important;
  display: none;
  float: left;
  height: 100%;
  min-height: 1px;
}

.emotion-0 .slick-slide img {
  display: block;
}

.emotion-0 .slick-slide.slick-loading img {
  display: none;
}

.emotion-0 .slick-slide.dragging img {
  pointer-events: none;
}

.emotion-0 [dir="rtl"] .slick-slide {
  float: right;
}

.emotion-0 .slick-initialized .slick-slide,
.emotion-0 .slick-vertical .slick-slide {
  display: block;
}

.emotion-0 .slick-vertical .slick-slide {
  border: 1px solid transparent;
  height: auto;
}

.emotion-0 .slick-loading .slick-track,
.emotion-0 .slick-loading .slick-slide {
  visibility: hidden;
}

.emotion-0 .slick-loading .slick-list {
  background: #fff url("./ajax-loader.gif") center center no-repeat;
}

.emotion-0 .slick-arrow.slick-hidden {
  display: none;
}

.emotion-0 .slick-prev,
.emotion-0 .slick-next {
  font-size: 0;
  line-height: 0;
  position: absolute;
  top: 50%;
  display: block;
  padding: 0;
  cursor: pointer;
  color: transparent;
  border: none;
  outline: none;
  background: transparent;
}

.emotion-0 .slick-prev:hover,
.emotion-0 .slick-next:hover,
.emotion-0 .slick-prev:focus,
.emotion-0 .slick-next:focus {
  color: transparent;
  outline: none;
  background: transparent;
  opacity: 1;
}

.emotion-0 .slick-prev.slick-disabled,
.emotion-0 .slick-next.slick-disabled {
  opacity: 0.25;
}

.emotion-0 .slick-prev {
  left: -25px;
  -webkit-transform: translate(0, -50%) rotate(90deg);
  -moz-transform: translate(0, -50%) rotate(90deg);
  -ms-transform: translate(0, -50%) rotate(90deg);
  transform: translate(0, -50%) rotate(90deg);
}

.emotion-0 .slick-prev span {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-0 [dir="rtl"] .slick-prev {
  right: -25px;
  left: auto;
}

.emotion-0 .slick-next {
  right: -25px;
  -webkit-transform: translate(0, -50%) rotate(-90deg);
  -moz-transform: translate(0, -50%) rotate(-90deg);
  -ms-transform: translate(0, -50%) rotate(-90deg);
  transform: translate(0, -50%) rotate(-90deg);
}

.emotion-0 .slick-next span {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-0 [dir="rtl"] .slick-next {
  right: auto;
  left: -25px;
}

.emotion-0 .slick-dotted.slick-slider {
  margin-bottom: 30px;
}

.emotion-0 .slick-dots {
  position: absolute;
  bottom: -25px;
  display: block;
  width: 100%;
  padding: 0;
  margin: 0;
  list-style: none;
  text-align: center;
}

.emotion-0 .slick-dots li {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: 20px;
  height: 20px;
  margin: 0 5px;
  padding: 0;
  cursor: pointer;
}

.emotion-0 .slick-dots li.slick-active button:before {
  opacity: 0.75;
  color: black;
}

.emotion-0 .slick-dots li button {
  font-size: 0;
  line-height: 0;
  display: block;
  padding: 5px;
  cursor: pointer;
  color: transparent;
  border: 0;
  outline: none;
  background: transparent;
}

.emotion-0 .slick-dots li button:hover,
.emotion-0 .slick-dots li button:focus {
  outline: none;
}

.emotion-0 .slick-dots li button:hover:before,
.emotion-0 .slick-dots li button:focus:before,
.emotion-0 .slick-dots li button:hover:before,
.emotion-0 .slick-dots li button:focus:before {
  opacity: 1;
}

.emotion-0 .slick-dots li button:before {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 5px;
  height: 5px;
  content: "";
  opacity: 0.25;
  background-color: black;
  border-radius: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

<div
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`CarouselContainer snapshots renders isBR2023Swatches true state correctly GapFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  position: relative;
}

.emotion-0 .slick-slider {
  position: relative;
  display: block;
  box-sizing: border-box;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  -webkit-touch-callout: none;
  touch-action: pan-y;
  -webkit-tap-highlight-color: transparent;
  scroll-behavior: smooth;
}

.emotion-0 .slick-slider .slick-track,
.emotion-0 .slick-slider .slick-list {
  -webkit-transform: translate3d(0, 0, 0);
  -moz-transform: translate3d(0, 0, 0);
  -ms-transform: translate3d(0, 0, 0);
  transform: translate3d(0, 0, 0);
  height: 32px;
}

.emotion-0 .slick-list {
  position: relative;
  display: block;
  overflow-x: hidden;
  overflow-y: hidden;
  margin: 0;
  padding: 0;
}

.emotion-0 .slick-list:focus {
  outline: none;
}

.emotion-0 .slick-list.dragging {
  cursor: pointer;
  cursor: hand;
}

.emotion-0 .slick-track {
  position: relative;
  top: 0;
  left: 0;
  display: block;
  margin-left: auto;
  margin-right: auto;
}

.emotion-0 .slick-track:before,
.emotion-0 .slick-track:after {
  display: table;
  content: "";
}

.emotion-0 .slick-track:after {
  clear: both;
}

.emotion-0 .slick-slide {
  width: 48px!important;
  display: none;
  float: left;
  height: 100%;
  min-height: 1px;
}

.emotion-0 .slick-slide img {
  display: block;
}

.emotion-0 .slick-slide.slick-loading img {
  display: none;
}

.emotion-0 .slick-slide.dragging img {
  pointer-events: none;
}

.emotion-0 [dir="rtl"] .slick-slide {
  float: right;
}

.emotion-0 .slick-initialized .slick-slide,
.emotion-0 .slick-vertical .slick-slide {
  display: block;
}

.emotion-0 .slick-vertical .slick-slide {
  border: 1px solid transparent;
  height: auto;
}

.emotion-0 .slick-loading .slick-track,
.emotion-0 .slick-loading .slick-slide {
  visibility: hidden;
}

.emotion-0 .slick-loading .slick-list {
  background: #fff url("./ajax-loader.gif") center center no-repeat;
}

.emotion-0 .slick-arrow.slick-hidden {
  display: none;
}

.emotion-0 .slick-prev,
.emotion-0 .slick-next {
  font-size: 0;
  line-height: 0;
  position: absolute;
  top: 50%;
  display: block;
  padding: 0;
  cursor: pointer;
  color: transparent;
  border: none;
  outline: none;
  background: transparent;
}

.emotion-0 .slick-prev:hover,
.emotion-0 .slick-next:hover,
.emotion-0 .slick-prev:focus,
.emotion-0 .slick-next:focus {
  color: transparent;
  outline: none;
  background: transparent;
  opacity: 1;
}

.emotion-0 .slick-prev.slick-disabled,
.emotion-0 .slick-next.slick-disabled {
  opacity: 0.25;
}

.emotion-0 .slick-prev {
  left: -25px;
  -webkit-transform: translate(0, -50%) rotate(90deg);
  -moz-transform: translate(0, -50%) rotate(90deg);
  -ms-transform: translate(0, -50%) rotate(90deg);
  transform: translate(0, -50%) rotate(90deg);
}

.emotion-0 .slick-prev span {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-0 [dir="rtl"] .slick-prev {
  right: -25px;
  left: auto;
}

.emotion-0 .slick-next {
  right: -25px;
  -webkit-transform: translate(0, -50%) rotate(-90deg);
  -moz-transform: translate(0, -50%) rotate(-90deg);
  -ms-transform: translate(0, -50%) rotate(-90deg);
  transform: translate(0, -50%) rotate(-90deg);
}

.emotion-0 .slick-next span {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-0 [dir="rtl"] .slick-next {
  right: auto;
  left: -25px;
}

.emotion-0 .slick-dotted.slick-slider {
  margin-bottom: 30px;
}

.emotion-0 .slick-dots {
  position: absolute;
  bottom: -25px;
  display: block;
  width: 100%;
  padding: 0;
  margin: 0;
  list-style: none;
  text-align: center;
}

.emotion-0 .slick-dots li {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: 20px;
  height: 20px;
  margin: 0 5px;
  padding: 0;
  cursor: pointer;
}

.emotion-0 .slick-dots li.slick-active button:before {
  opacity: 0.75;
  color: black;
}

.emotion-0 .slick-dots li button {
  font-size: 0;
  line-height: 0;
  display: block;
  padding: 5px;
  cursor: pointer;
  color: transparent;
  border: 0;
  outline: none;
  background: transparent;
}

.emotion-0 .slick-dots li button:hover,
.emotion-0 .slick-dots li button:focus {
  outline: none;
}

.emotion-0 .slick-dots li button:hover:before,
.emotion-0 .slick-dots li button:focus:before,
.emotion-0 .slick-dots li button:hover:before,
.emotion-0 .slick-dots li button:focus:before {
  opacity: 1;
}

.emotion-0 .slick-dots li button:before {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 5px;
  height: 5px;
  content: "";
  opacity: 0.25;
  background-color: black;
  border-radius: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

<div
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`CarouselContainer snapshots renders isBR2023Swatches true state correctly GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  position: relative;
}

.emotion-0 .slick-slider {
  position: relative;
  display: block;
  box-sizing: border-box;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  -webkit-touch-callout: none;
  touch-action: pan-y;
  -webkit-tap-highlight-color: transparent;
  scroll-behavior: smooth;
}

.emotion-0 .slick-slider .slick-track,
.emotion-0 .slick-slider .slick-list {
  -webkit-transform: translate3d(0, 0, 0);
  -moz-transform: translate3d(0, 0, 0);
  -ms-transform: translate3d(0, 0, 0);
  transform: translate3d(0, 0, 0);
  height: 32px;
}

.emotion-0 .slick-list {
  position: relative;
  display: block;
  overflow-x: hidden;
  overflow-y: hidden;
  margin: 0;
  padding: 0;
}

.emotion-0 .slick-list:focus {
  outline: none;
}

.emotion-0 .slick-list.dragging {
  cursor: pointer;
  cursor: hand;
}

.emotion-0 .slick-track {
  position: relative;
  top: 0;
  left: 0;
  display: block;
  margin-left: auto;
  margin-right: auto;
}

.emotion-0 .slick-track:before,
.emotion-0 .slick-track:after {
  display: table;
  content: "";
}

.emotion-0 .slick-track:after {
  clear: both;
}

.emotion-0 .slick-slide {
  width: 48px!important;
  display: none;
  float: left;
  height: 100%;
  min-height: 1px;
}

.emotion-0 .slick-slide img {
  display: block;
}

.emotion-0 .slick-slide.slick-loading img {
  display: none;
}

.emotion-0 .slick-slide.dragging img {
  pointer-events: none;
}

.emotion-0 [dir="rtl"] .slick-slide {
  float: right;
}

.emotion-0 .slick-initialized .slick-slide,
.emotion-0 .slick-vertical .slick-slide {
  display: block;
}

.emotion-0 .slick-vertical .slick-slide {
  border: 1px solid transparent;
  height: auto;
}

.emotion-0 .slick-loading .slick-track,
.emotion-0 .slick-loading .slick-slide {
  visibility: hidden;
}

.emotion-0 .slick-loading .slick-list {
  background: #fff url("./ajax-loader.gif") center center no-repeat;
}

.emotion-0 .slick-arrow.slick-hidden {
  display: none;
}

.emotion-0 .slick-prev,
.emotion-0 .slick-next {
  font-size: 0;
  line-height: 0;
  position: absolute;
  top: 50%;
  display: block;
  padding: 0;
  cursor: pointer;
  color: transparent;
  border: none;
  outline: none;
  background: transparent;
}

.emotion-0 .slick-prev:hover,
.emotion-0 .slick-next:hover,
.emotion-0 .slick-prev:focus,
.emotion-0 .slick-next:focus {
  color: transparent;
  outline: none;
  background: transparent;
  opacity: 1;
}

.emotion-0 .slick-prev.slick-disabled,
.emotion-0 .slick-next.slick-disabled {
  opacity: 0.25;
}

.emotion-0 .slick-prev {
  left: -25px;
  -webkit-transform: translate(0, -50%) rotate(90deg);
  -moz-transform: translate(0, -50%) rotate(90deg);
  -ms-transform: translate(0, -50%) rotate(90deg);
  transform: translate(0, -50%) rotate(90deg);
}

.emotion-0 .slick-prev span {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-0 [dir="rtl"] .slick-prev {
  right: -25px;
  left: auto;
}

.emotion-0 .slick-next {
  right: -25px;
  -webkit-transform: translate(0, -50%) rotate(-90deg);
  -moz-transform: translate(0, -50%) rotate(-90deg);
  -ms-transform: translate(0, -50%) rotate(-90deg);
  transform: translate(0, -50%) rotate(-90deg);
}

.emotion-0 .slick-next span {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-0 [dir="rtl"] .slick-next {
  right: auto;
  left: -25px;
}

.emotion-0 .slick-dotted.slick-slider {
  margin-bottom: 30px;
}

.emotion-0 .slick-dots {
  position: absolute;
  bottom: -25px;
  display: block;
  width: 100%;
  padding: 0;
  margin: 0;
  list-style: none;
  text-align: center;
}

.emotion-0 .slick-dots li {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: 20px;
  height: 20px;
  margin: 0 5px;
  padding: 0;
  cursor: pointer;
}

.emotion-0 .slick-dots li.slick-active button:before {
  opacity: 0.75;
  color: black;
}

.emotion-0 .slick-dots li button {
  font-size: 0;
  line-height: 0;
  display: block;
  padding: 5px;
  cursor: pointer;
  color: transparent;
  border: 0;
  outline: none;
  background: transparent;
}

.emotion-0 .slick-dots li button:hover,
.emotion-0 .slick-dots li button:focus {
  outline: none;
}

.emotion-0 .slick-dots li button:hover:before,
.emotion-0 .slick-dots li button:focus:before,
.emotion-0 .slick-dots li button:hover:before,
.emotion-0 .slick-dots li button:focus:before {
  opacity: 1;
}

.emotion-0 .slick-dots li button:before {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 5px;
  height: 5px;
  content: "";
  opacity: 0.25;
  background-color: black;
  border-radius: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

<div
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`CarouselContainer snapshots renders isBR2023Swatches true state correctly OldNavy 1`] = `
<DocumentFragment>
  .emotion-0 {
  position: relative;
}

.emotion-0 .slick-slider {
  position: relative;
  display: block;
  box-sizing: border-box;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  -webkit-touch-callout: none;
  touch-action: pan-y;
  -webkit-tap-highlight-color: transparent;
  scroll-behavior: smooth;
}

.emotion-0 .slick-slider .slick-track,
.emotion-0 .slick-slider .slick-list {
  -webkit-transform: translate3d(0, 0, 0);
  -moz-transform: translate3d(0, 0, 0);
  -ms-transform: translate3d(0, 0, 0);
  transform: translate3d(0, 0, 0);
  height: 32px;
}

.emotion-0 .slick-list {
  position: relative;
  display: block;
  overflow-x: hidden;
  overflow-y: hidden;
  margin: 0;
  padding: 0;
}

.emotion-0 .slick-list:focus {
  outline: none;
}

.emotion-0 .slick-list.dragging {
  cursor: pointer;
  cursor: hand;
}

.emotion-0 .slick-track {
  position: relative;
  top: 0;
  left: 0;
  display: block;
  margin-left: auto;
  margin-right: auto;
}

.emotion-0 .slick-track:before,
.emotion-0 .slick-track:after {
  display: table;
  content: "";
}

.emotion-0 .slick-track:after {
  clear: both;
}

.emotion-0 .slick-slide {
  width: 48px!important;
  display: none;
  float: left;
  height: 100%;
  min-height: 1px;
}

.emotion-0 .slick-slide img {
  display: block;
}

.emotion-0 .slick-slide.slick-loading img {
  display: none;
}

.emotion-0 .slick-slide.dragging img {
  pointer-events: none;
}

.emotion-0 [dir="rtl"] .slick-slide {
  float: right;
}

.emotion-0 .slick-initialized .slick-slide,
.emotion-0 .slick-vertical .slick-slide {
  display: block;
}

.emotion-0 .slick-vertical .slick-slide {
  border: 1px solid transparent;
  height: auto;
}

.emotion-0 .slick-loading .slick-track,
.emotion-0 .slick-loading .slick-slide {
  visibility: hidden;
}

.emotion-0 .slick-loading .slick-list {
  background: #fff url("./ajax-loader.gif") center center no-repeat;
}

.emotion-0 .slick-arrow.slick-hidden {
  display: none;
}

.emotion-0 .slick-prev,
.emotion-0 .slick-next {
  font-size: 0;
  line-height: 0;
  position: absolute;
  top: 50%;
  display: block;
  padding: 0;
  cursor: pointer;
  color: transparent;
  border: none;
  outline: none;
  background: transparent;
}

.emotion-0 .slick-prev:hover,
.emotion-0 .slick-next:hover,
.emotion-0 .slick-prev:focus,
.emotion-0 .slick-next:focus {
  color: transparent;
  outline: none;
  background: transparent;
  opacity: 1;
}

.emotion-0 .slick-prev.slick-disabled,
.emotion-0 .slick-next.slick-disabled {
  opacity: 0.25;
}

.emotion-0 .slick-prev {
  left: -25px;
  -webkit-transform: translate(0, -50%) rotate(90deg);
  -moz-transform: translate(0, -50%) rotate(90deg);
  -ms-transform: translate(0, -50%) rotate(90deg);
  transform: translate(0, -50%) rotate(90deg);
}

.emotion-0 .slick-prev span {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-0 [dir="rtl"] .slick-prev {
  right: -25px;
  left: auto;
}

.emotion-0 .slick-next {
  right: -25px;
  -webkit-transform: translate(0, -50%) rotate(-90deg);
  -moz-transform: translate(0, -50%) rotate(-90deg);
  -ms-transform: translate(0, -50%) rotate(-90deg);
  transform: translate(0, -50%) rotate(-90deg);
}

.emotion-0 .slick-next span {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-0 [dir="rtl"] .slick-next {
  right: auto;
  left: -25px;
}

.emotion-0 .slick-dotted.slick-slider {
  margin-bottom: 30px;
}

.emotion-0 .slick-dots {
  position: absolute;
  bottom: -25px;
  display: block;
  width: 100%;
  padding: 0;
  margin: 0;
  list-style: none;
  text-align: center;
}

.emotion-0 .slick-dots li {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: 20px;
  height: 20px;
  margin: 0 5px;
  padding: 0;
  cursor: pointer;
}

.emotion-0 .slick-dots li.slick-active button:before {
  opacity: 0.75;
  color: black;
}

.emotion-0 .slick-dots li button {
  font-size: 0;
  line-height: 0;
  display: block;
  padding: 5px;
  cursor: pointer;
  color: transparent;
  border: 0;
  outline: none;
  background: transparent;
}

.emotion-0 .slick-dots li button:hover,
.emotion-0 .slick-dots li button:focus {
  outline: none;
}

.emotion-0 .slick-dots li button:hover:before,
.emotion-0 .slick-dots li button:focus:before,
.emotion-0 .slick-dots li button:hover:before,
.emotion-0 .slick-dots li button:focus:before {
  opacity: 1;
}

.emotion-0 .slick-dots li button:before {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 5px;
  height: 5px;
  content: "";
  opacity: 0.25;
  background-color: black;
  border-radius: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

<div
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`CarouselContainer snapshots renders isBR2023Swatches true state correctly OldNavy in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  position: relative;
}

.emotion-0 .slick-slider {
  position: relative;
  display: block;
  box-sizing: border-box;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  -webkit-touch-callout: none;
  touch-action: pan-y;
  -webkit-tap-highlight-color: transparent;
  scroll-behavior: smooth;
}

.emotion-0 .slick-slider .slick-track,
.emotion-0 .slick-slider .slick-list {
  -webkit-transform: translate3d(0, 0, 0);
  -moz-transform: translate3d(0, 0, 0);
  -ms-transform: translate3d(0, 0, 0);
  transform: translate3d(0, 0, 0);
  height: 32px;
}

.emotion-0 .slick-list {
  position: relative;
  display: block;
  overflow-x: hidden;
  overflow-y: hidden;
  margin: 0;
  padding: 0;
}

.emotion-0 .slick-list:focus {
  outline: none;
}

.emotion-0 .slick-list.dragging {
  cursor: pointer;
  cursor: hand;
}

.emotion-0 .slick-track {
  position: relative;
  top: 0;
  left: 0;
  display: block;
  margin-left: auto;
  margin-right: auto;
}

.emotion-0 .slick-track:before,
.emotion-0 .slick-track:after {
  display: table;
  content: "";
}

.emotion-0 .slick-track:after {
  clear: both;
}

.emotion-0 .slick-slide {
  width: 48px!important;
  display: none;
  float: left;
  height: 100%;
  min-height: 1px;
}

.emotion-0 .slick-slide img {
  display: block;
}

.emotion-0 .slick-slide.slick-loading img {
  display: none;
}

.emotion-0 .slick-slide.dragging img {
  pointer-events: none;
}

.emotion-0 [dir="rtl"] .slick-slide {
  float: right;
}

.emotion-0 .slick-initialized .slick-slide,
.emotion-0 .slick-vertical .slick-slide {
  display: block;
}

.emotion-0 .slick-vertical .slick-slide {
  border: 1px solid transparent;
  height: auto;
}

.emotion-0 .slick-loading .slick-track,
.emotion-0 .slick-loading .slick-slide {
  visibility: hidden;
}

.emotion-0 .slick-loading .slick-list {
  background: #fff url("./ajax-loader.gif") center center no-repeat;
}

.emotion-0 .slick-arrow.slick-hidden {
  display: none;
}

.emotion-0 .slick-prev,
.emotion-0 .slick-next {
  font-size: 0;
  line-height: 0;
  position: absolute;
  top: 50%;
  display: block;
  padding: 0;
  cursor: pointer;
  color: transparent;
  border: none;
  outline: none;
  background: transparent;
}

.emotion-0 .slick-prev:hover,
.emotion-0 .slick-next:hover,
.emotion-0 .slick-prev:focus,
.emotion-0 .slick-next:focus {
  color: transparent;
  outline: none;
  background: transparent;
  opacity: 1;
}

.emotion-0 .slick-prev.slick-disabled,
.emotion-0 .slick-next.slick-disabled {
  opacity: 0.25;
}

.emotion-0 .slick-prev {
  left: -25px;
  -webkit-transform: translate(0, -50%) rotate(90deg);
  -moz-transform: translate(0, -50%) rotate(90deg);
  -ms-transform: translate(0, -50%) rotate(90deg);
  transform: translate(0, -50%) rotate(90deg);
}

.emotion-0 .slick-prev span {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-0 [dir="rtl"] .slick-prev {
  right: -25px;
  left: auto;
}

.emotion-0 .slick-next {
  right: -25px;
  -webkit-transform: translate(0, -50%) rotate(-90deg);
  -moz-transform: translate(0, -50%) rotate(-90deg);
  -ms-transform: translate(0, -50%) rotate(-90deg);
  transform: translate(0, -50%) rotate(-90deg);
}

.emotion-0 .slick-next span {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-0 [dir="rtl"] .slick-next {
  right: auto;
  left: -25px;
}

.emotion-0 .slick-dotted.slick-slider {
  margin-bottom: 30px;
}

.emotion-0 .slick-dots {
  position: absolute;
  bottom: -25px;
  display: block;
  width: 100%;
  padding: 0;
  margin: 0;
  list-style: none;
  text-align: center;
}

.emotion-0 .slick-dots li {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: 20px;
  height: 20px;
  margin: 0 5px;
  padding: 0;
  cursor: pointer;
}

.emotion-0 .slick-dots li.slick-active button:before {
  opacity: 0.75;
  color: black;
}

.emotion-0 .slick-dots li button {
  font-size: 0;
  line-height: 0;
  display: block;
  padding: 5px;
  cursor: pointer;
  color: transparent;
  border: 0;
  outline: none;
  background: transparent;
}

.emotion-0 .slick-dots li button:hover,
.emotion-0 .slick-dots li button:focus {
  outline: none;
}

.emotion-0 .slick-dots li button:hover:before,
.emotion-0 .slick-dots li button:focus:before,
.emotion-0 .slick-dots li button:hover:before,
.emotion-0 .slick-dots li button:focus:before {
  opacity: 1;
}

.emotion-0 .slick-dots li button:before {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 5px;
  height: 5px;
  content: "";
  opacity: 0.25;
  background-color: black;
  border-radius: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

<div
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`CarouselContainer snapshots renders swatch size default state correctly Athleta 1`] = `
<DocumentFragment>
  .emotion-0 {
  position: relative;
}

.emotion-0 .slick-slider {
  position: relative;
  display: block;
  box-sizing: border-box;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  -webkit-touch-callout: none;
  touch-action: pan-y;
  -webkit-tap-highlight-color: transparent;
  scroll-behavior: smooth;
}

.emotion-0 .slick-slider .slick-track,
.emotion-0 .slick-slider .slick-list {
  -webkit-transform: translate3d(0, 0, 0);
  -moz-transform: translate3d(0, 0, 0);
  -ms-transform: translate3d(0, 0, 0);
  transform: translate3d(0, 0, 0);
}

.emotion-0 .slick-list {
  position: relative;
  display: block;
  overflow-x: hidden;
  overflow-y: hidden;
  margin: 0;
  padding: 0;
}

.emotion-0 .slick-list:focus {
  outline: none;
}

.emotion-0 .slick-list.dragging {
  cursor: pointer;
  cursor: hand;
}

.emotion-0 .slick-track {
  position: relative;
  top: 0;
  left: 0;
  display: block;
  margin-left: auto;
  margin-right: auto;
}

.emotion-0 .slick-track:before,
.emotion-0 .slick-track:after {
  display: table;
  content: "";
}

.emotion-0 .slick-track:after {
  clear: both;
}

.emotion-0 .slick-slide {
  width: 48px!important;
  display: none;
  float: left;
  height: 100%;
  min-height: 1px;
}

.emotion-0 .slick-slide img {
  display: block;
}

.emotion-0 .slick-slide.slick-loading img {
  display: none;
}

.emotion-0 .slick-slide.dragging img {
  pointer-events: none;
}

.emotion-0 [dir="rtl"] .slick-slide {
  float: right;
}

.emotion-0 .slick-initialized .slick-slide,
.emotion-0 .slick-vertical .slick-slide {
  display: block;
}

.emotion-0 .slick-vertical .slick-slide {
  border: 1px solid transparent;
  height: auto;
}

.emotion-0 .slick-loading .slick-track,
.emotion-0 .slick-loading .slick-slide {
  visibility: hidden;
}

.emotion-0 .slick-loading .slick-list {
  background: #fff url("./ajax-loader.gif") center center no-repeat;
}

.emotion-0 .slick-arrow.slick-hidden {
  display: none;
}

.emotion-0 .slick-prev,
.emotion-0 .slick-next {
  font-size: 0;
  line-height: 0;
  position: absolute;
  top: 50%;
  display: block;
  padding: 0;
  cursor: pointer;
  color: transparent;
  border: none;
  outline: none;
  background: transparent;
}

.emotion-0 .slick-prev:hover,
.emotion-0 .slick-next:hover,
.emotion-0 .slick-prev:focus,
.emotion-0 .slick-next:focus {
  color: transparent;
  outline: none;
  background: transparent;
  opacity: 1;
}

.emotion-0 .slick-prev.slick-disabled,
.emotion-0 .slick-next.slick-disabled {
  opacity: 0.25;
}

.emotion-0 .slick-prev {
  left: -25px;
  -webkit-transform: translate(0, -50%) rotate(90deg);
  -moz-transform: translate(0, -50%) rotate(90deg);
  -ms-transform: translate(0, -50%) rotate(90deg);
  transform: translate(0, -50%) rotate(90deg);
}

.emotion-0 .slick-prev span {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-0 [dir="rtl"] .slick-prev {
  right: -25px;
  left: auto;
}

.emotion-0 .slick-next {
  right: -25px;
  -webkit-transform: translate(0, -50%) rotate(-90deg);
  -moz-transform: translate(0, -50%) rotate(-90deg);
  -ms-transform: translate(0, -50%) rotate(-90deg);
  transform: translate(0, -50%) rotate(-90deg);
}

.emotion-0 .slick-next span {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-0 [dir="rtl"] .slick-next {
  right: auto;
  left: -25px;
}

.emotion-0 .slick-dotted.slick-slider {
  margin-bottom: 30px;
}

.emotion-0 .slick-dots {
  position: absolute;
  bottom: -25px;
  display: block;
  width: 100%;
  padding: 0;
  margin: 0;
  list-style: none;
  text-align: center;
}

.emotion-0 .slick-dots li {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: 20px;
  height: 20px;
  margin: 0 5px;
  padding: 0;
  cursor: pointer;
}

.emotion-0 .slick-dots li.slick-active button:before {
  opacity: 0.75;
  color: black;
}

.emotion-0 .slick-dots li button {
  font-size: 0;
  line-height: 0;
  display: block;
  padding: 5px;
  cursor: pointer;
  color: transparent;
  border: 0;
  outline: none;
  background: transparent;
}

.emotion-0 .slick-dots li button:hover,
.emotion-0 .slick-dots li button:focus {
  outline: none;
}

.emotion-0 .slick-dots li button:hover:before,
.emotion-0 .slick-dots li button:focus:before,
.emotion-0 .slick-dots li button:hover:before,
.emotion-0 .slick-dots li button:focus:before {
  opacity: 1;
}

.emotion-0 .slick-dots li button:before {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 5px;
  height: 5px;
  content: "";
  opacity: 0.25;
  background-color: black;
  border-radius: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

<div
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`CarouselContainer snapshots renders swatch size default state correctly Athleta in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  position: relative;
}

.emotion-0 .slick-slider {
  position: relative;
  display: block;
  box-sizing: border-box;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  -webkit-touch-callout: none;
  touch-action: pan-y;
  -webkit-tap-highlight-color: transparent;
  scroll-behavior: smooth;
}

.emotion-0 .slick-slider .slick-track,
.emotion-0 .slick-slider .slick-list {
  -webkit-transform: translate3d(0, 0, 0);
  -moz-transform: translate3d(0, 0, 0);
  -ms-transform: translate3d(0, 0, 0);
  transform: translate3d(0, 0, 0);
}

.emotion-0 .slick-list {
  position: relative;
  display: block;
  overflow-x: hidden;
  overflow-y: hidden;
  margin: 0;
  padding: 0;
}

.emotion-0 .slick-list:focus {
  outline: none;
}

.emotion-0 .slick-list.dragging {
  cursor: pointer;
  cursor: hand;
}

.emotion-0 .slick-track {
  position: relative;
  top: 0;
  left: 0;
  display: block;
  margin-left: auto;
  margin-right: auto;
}

.emotion-0 .slick-track:before,
.emotion-0 .slick-track:after {
  display: table;
  content: "";
}

.emotion-0 .slick-track:after {
  clear: both;
}

.emotion-0 .slick-slide {
  width: 48px!important;
  display: none;
  float: left;
  height: 100%;
  min-height: 1px;
}

.emotion-0 .slick-slide img {
  display: block;
}

.emotion-0 .slick-slide.slick-loading img {
  display: none;
}

.emotion-0 .slick-slide.dragging img {
  pointer-events: none;
}

.emotion-0 [dir="rtl"] .slick-slide {
  float: right;
}

.emotion-0 .slick-initialized .slick-slide,
.emotion-0 .slick-vertical .slick-slide {
  display: block;
}

.emotion-0 .slick-vertical .slick-slide {
  border: 1px solid transparent;
  height: auto;
}

.emotion-0 .slick-loading .slick-track,
.emotion-0 .slick-loading .slick-slide {
  visibility: hidden;
}

.emotion-0 .slick-loading .slick-list {
  background: #fff url("./ajax-loader.gif") center center no-repeat;
}

.emotion-0 .slick-arrow.slick-hidden {
  display: none;
}

.emotion-0 .slick-prev,
.emotion-0 .slick-next {
  font-size: 0;
  line-height: 0;
  position: absolute;
  top: 50%;
  display: block;
  padding: 0;
  cursor: pointer;
  color: transparent;
  border: none;
  outline: none;
  background: transparent;
}

.emotion-0 .slick-prev:hover,
.emotion-0 .slick-next:hover,
.emotion-0 .slick-prev:focus,
.emotion-0 .slick-next:focus {
  color: transparent;
  outline: none;
  background: transparent;
  opacity: 1;
}

.emotion-0 .slick-prev.slick-disabled,
.emotion-0 .slick-next.slick-disabled {
  opacity: 0.25;
}

.emotion-0 .slick-prev {
  left: -25px;
  -webkit-transform: translate(0, -50%) rotate(90deg);
  -moz-transform: translate(0, -50%) rotate(90deg);
  -ms-transform: translate(0, -50%) rotate(90deg);
  transform: translate(0, -50%) rotate(90deg);
}

.emotion-0 .slick-prev span {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-0 [dir="rtl"] .slick-prev {
  right: -25px;
  left: auto;
}

.emotion-0 .slick-next {
  right: -25px;
  -webkit-transform: translate(0, -50%) rotate(-90deg);
  -moz-transform: translate(0, -50%) rotate(-90deg);
  -ms-transform: translate(0, -50%) rotate(-90deg);
  transform: translate(0, -50%) rotate(-90deg);
}

.emotion-0 .slick-next span {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-0 [dir="rtl"] .slick-next {
  right: auto;
  left: -25px;
}

.emotion-0 .slick-dotted.slick-slider {
  margin-bottom: 30px;
}

.emotion-0 .slick-dots {
  position: absolute;
  bottom: -25px;
  display: block;
  width: 100%;
  padding: 0;
  margin: 0;
  list-style: none;
  text-align: center;
}

.emotion-0 .slick-dots li {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: 20px;
  height: 20px;
  margin: 0 5px;
  padding: 0;
  cursor: pointer;
}

.emotion-0 .slick-dots li.slick-active button:before {
  opacity: 0.75;
  color: black;
}

.emotion-0 .slick-dots li button {
  font-size: 0;
  line-height: 0;
  display: block;
  padding: 5px;
  cursor: pointer;
  color: transparent;
  border: 0;
  outline: none;
  background: transparent;
}

.emotion-0 .slick-dots li button:hover,
.emotion-0 .slick-dots li button:focus {
  outline: none;
}

.emotion-0 .slick-dots li button:hover:before,
.emotion-0 .slick-dots li button:focus:before,
.emotion-0 .slick-dots li button:hover:before,
.emotion-0 .slick-dots li button:focus:before {
  opacity: 1;
}

.emotion-0 .slick-dots li button:before {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 5px;
  height: 5px;
  content: "";
  opacity: 0.25;
  background-color: black;
  border-radius: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

<div
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`CarouselContainer snapshots renders swatch size default state correctly BananaRepublic 1`] = `
<DocumentFragment>
  .emotion-0 {
  position: relative;
}

.emotion-0 .slick-slider {
  position: relative;
  display: block;
  box-sizing: border-box;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  -webkit-touch-callout: none;
  touch-action: pan-y;
  -webkit-tap-highlight-color: transparent;
  scroll-behavior: smooth;
}

.emotion-0 .slick-slider .slick-track,
.emotion-0 .slick-slider .slick-list {
  -webkit-transform: translate3d(0, 0, 0);
  -moz-transform: translate3d(0, 0, 0);
  -ms-transform: translate3d(0, 0, 0);
  transform: translate3d(0, 0, 0);
}

.emotion-0 .slick-list {
  position: relative;
  display: block;
  overflow-x: hidden;
  overflow-y: hidden;
  margin: 0;
  padding: 0;
}

.emotion-0 .slick-list:focus {
  outline: none;
}

.emotion-0 .slick-list.dragging {
  cursor: pointer;
  cursor: hand;
}

.emotion-0 .slick-track {
  position: relative;
  top: 0;
  left: 0;
  display: block;
  margin-left: auto;
  margin-right: auto;
}

.emotion-0 .slick-track:before,
.emotion-0 .slick-track:after {
  display: table;
  content: "";
}

.emotion-0 .slick-track:after {
  clear: both;
}

.emotion-0 .slick-slide {
  width: 48px!important;
  display: none;
  float: left;
  height: 100%;
  min-height: 1px;
}

.emotion-0 .slick-slide img {
  display: block;
}

.emotion-0 .slick-slide.slick-loading img {
  display: none;
}

.emotion-0 .slick-slide.dragging img {
  pointer-events: none;
}

.emotion-0 [dir="rtl"] .slick-slide {
  float: right;
}

.emotion-0 .slick-initialized .slick-slide,
.emotion-0 .slick-vertical .slick-slide {
  display: block;
}

.emotion-0 .slick-vertical .slick-slide {
  border: 1px solid transparent;
  height: auto;
}

.emotion-0 .slick-loading .slick-track,
.emotion-0 .slick-loading .slick-slide {
  visibility: hidden;
}

.emotion-0 .slick-loading .slick-list {
  background: #fff url("./ajax-loader.gif") center center no-repeat;
}

.emotion-0 .slick-arrow.slick-hidden {
  display: none;
}

.emotion-0 .slick-prev,
.emotion-0 .slick-next {
  font-size: 0;
  line-height: 0;
  position: absolute;
  top: 50%;
  display: block;
  padding: 0;
  cursor: pointer;
  color: transparent;
  border: none;
  outline: none;
  background: transparent;
}

.emotion-0 .slick-prev:hover,
.emotion-0 .slick-next:hover,
.emotion-0 .slick-prev:focus,
.emotion-0 .slick-next:focus {
  color: transparent;
  outline: none;
  background: transparent;
  opacity: 1;
}

.emotion-0 .slick-prev.slick-disabled,
.emotion-0 .slick-next.slick-disabled {
  opacity: 0.25;
}

.emotion-0 .slick-prev {
  left: -25px;
  -webkit-transform: translate(0, -50%) rotate(90deg);
  -moz-transform: translate(0, -50%) rotate(90deg);
  -ms-transform: translate(0, -50%) rotate(90deg);
  transform: translate(0, -50%) rotate(90deg);
}

.emotion-0 .slick-prev span {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-0 [dir="rtl"] .slick-prev {
  right: -25px;
  left: auto;
}

.emotion-0 .slick-next {
  right: -25px;
  -webkit-transform: translate(0, -50%) rotate(-90deg);
  -moz-transform: translate(0, -50%) rotate(-90deg);
  -ms-transform: translate(0, -50%) rotate(-90deg);
  transform: translate(0, -50%) rotate(-90deg);
}

.emotion-0 .slick-next span {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-0 [dir="rtl"] .slick-next {
  right: auto;
  left: -25px;
}

.emotion-0 .slick-dotted.slick-slider {
  margin-bottom: 30px;
}

.emotion-0 .slick-dots {
  position: absolute;
  bottom: -25px;
  display: block;
  width: 100%;
  padding: 0;
  margin: 0;
  list-style: none;
  text-align: center;
}

.emotion-0 .slick-dots li {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: 20px;
  height: 20px;
  margin: 0 5px;
  padding: 0;
  cursor: pointer;
}

.emotion-0 .slick-dots li.slick-active button:before {
  opacity: 0.75;
  color: black;
}

.emotion-0 .slick-dots li button {
  font-size: 0;
  line-height: 0;
  display: block;
  padding: 5px;
  cursor: pointer;
  color: transparent;
  border: 0;
  outline: none;
  background: transparent;
}

.emotion-0 .slick-dots li button:hover,
.emotion-0 .slick-dots li button:focus {
  outline: none;
}

.emotion-0 .slick-dots li button:hover:before,
.emotion-0 .slick-dots li button:focus:before,
.emotion-0 .slick-dots li button:hover:before,
.emotion-0 .slick-dots li button:focus:before {
  opacity: 1;
}

.emotion-0 .slick-dots li button:before {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 5px;
  height: 5px;
  content: "";
  opacity: 0.25;
  background-color: black;
  border-radius: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

<div
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`CarouselContainer snapshots renders swatch size default state correctly BananaRepublic in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  position: relative;
}

.emotion-0 .slick-slider {
  position: relative;
  display: block;
  box-sizing: border-box;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  -webkit-touch-callout: none;
  touch-action: pan-y;
  -webkit-tap-highlight-color: transparent;
  scroll-behavior: smooth;
}

.emotion-0 .slick-slider .slick-track,
.emotion-0 .slick-slider .slick-list {
  -webkit-transform: translate3d(0, 0, 0);
  -moz-transform: translate3d(0, 0, 0);
  -ms-transform: translate3d(0, 0, 0);
  transform: translate3d(0, 0, 0);
}

.emotion-0 .slick-list {
  position: relative;
  display: block;
  overflow-x: hidden;
  overflow-y: hidden;
  margin: 0;
  padding: 0;
}

.emotion-0 .slick-list:focus {
  outline: none;
}

.emotion-0 .slick-list.dragging {
  cursor: pointer;
  cursor: hand;
}

.emotion-0 .slick-track {
  position: relative;
  top: 0;
  left: 0;
  display: block;
  margin-left: auto;
  margin-right: auto;
}

.emotion-0 .slick-track:before,
.emotion-0 .slick-track:after {
  display: table;
  content: "";
}

.emotion-0 .slick-track:after {
  clear: both;
}

.emotion-0 .slick-slide {
  width: 48px!important;
  display: none;
  float: left;
  height: 100%;
  min-height: 1px;
}

.emotion-0 .slick-slide img {
  display: block;
}

.emotion-0 .slick-slide.slick-loading img {
  display: none;
}

.emotion-0 .slick-slide.dragging img {
  pointer-events: none;
}

.emotion-0 [dir="rtl"] .slick-slide {
  float: right;
}

.emotion-0 .slick-initialized .slick-slide,
.emotion-0 .slick-vertical .slick-slide {
  display: block;
}

.emotion-0 .slick-vertical .slick-slide {
  border: 1px solid transparent;
  height: auto;
}

.emotion-0 .slick-loading .slick-track,
.emotion-0 .slick-loading .slick-slide {
  visibility: hidden;
}

.emotion-0 .slick-loading .slick-list {
  background: #fff url("./ajax-loader.gif") center center no-repeat;
}

.emotion-0 .slick-arrow.slick-hidden {
  display: none;
}

.emotion-0 .slick-prev,
.emotion-0 .slick-next {
  font-size: 0;
  line-height: 0;
  position: absolute;
  top: 50%;
  display: block;
  padding: 0;
  cursor: pointer;
  color: transparent;
  border: none;
  outline: none;
  background: transparent;
}

.emotion-0 .slick-prev:hover,
.emotion-0 .slick-next:hover,
.emotion-0 .slick-prev:focus,
.emotion-0 .slick-next:focus {
  color: transparent;
  outline: none;
  background: transparent;
  opacity: 1;
}

.emotion-0 .slick-prev.slick-disabled,
.emotion-0 .slick-next.slick-disabled {
  opacity: 0.25;
}

.emotion-0 .slick-prev {
  left: -25px;
  -webkit-transform: translate(0, -50%) rotate(90deg);
  -moz-transform: translate(0, -50%) rotate(90deg);
  -ms-transform: translate(0, -50%) rotate(90deg);
  transform: translate(0, -50%) rotate(90deg);
}

.emotion-0 .slick-prev span {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-0 [dir="rtl"] .slick-prev {
  right: -25px;
  left: auto;
}

.emotion-0 .slick-next {
  right: -25px;
  -webkit-transform: translate(0, -50%) rotate(-90deg);
  -moz-transform: translate(0, -50%) rotate(-90deg);
  -ms-transform: translate(0, -50%) rotate(-90deg);
  transform: translate(0, -50%) rotate(-90deg);
}

.emotion-0 .slick-next span {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-0 [dir="rtl"] .slick-next {
  right: auto;
  left: -25px;
}

.emotion-0 .slick-dotted.slick-slider {
  margin-bottom: 30px;
}

.emotion-0 .slick-dots {
  position: absolute;
  bottom: -25px;
  display: block;
  width: 100%;
  padding: 0;
  margin: 0;
  list-style: none;
  text-align: center;
}

.emotion-0 .slick-dots li {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: 20px;
  height: 20px;
  margin: 0 5px;
  padding: 0;
  cursor: pointer;
}

.emotion-0 .slick-dots li.slick-active button:before {
  opacity: 0.75;
  color: black;
}

.emotion-0 .slick-dots li button {
  font-size: 0;
  line-height: 0;
  display: block;
  padding: 5px;
  cursor: pointer;
  color: transparent;
  border: 0;
  outline: none;
  background: transparent;
}

.emotion-0 .slick-dots li button:hover,
.emotion-0 .slick-dots li button:focus {
  outline: none;
}

.emotion-0 .slick-dots li button:hover:before,
.emotion-0 .slick-dots li button:focus:before,
.emotion-0 .slick-dots li button:hover:before,
.emotion-0 .slick-dots li button:focus:before {
  opacity: 1;
}

.emotion-0 .slick-dots li button:before {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 5px;
  height: 5px;
  content: "";
  opacity: 0.25;
  background-color: black;
  border-radius: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

<div
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`CarouselContainer snapshots renders swatch size default state correctly BananaRepublicFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  position: relative;
}

.emotion-0 .slick-slider {
  position: relative;
  display: block;
  box-sizing: border-box;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  -webkit-touch-callout: none;
  touch-action: pan-y;
  -webkit-tap-highlight-color: transparent;
  scroll-behavior: smooth;
}

.emotion-0 .slick-slider .slick-track,
.emotion-0 .slick-slider .slick-list {
  -webkit-transform: translate3d(0, 0, 0);
  -moz-transform: translate3d(0, 0, 0);
  -ms-transform: translate3d(0, 0, 0);
  transform: translate3d(0, 0, 0);
}

.emotion-0 .slick-list {
  position: relative;
  display: block;
  overflow-x: hidden;
  overflow-y: hidden;
  margin: 0;
  padding: 0;
}

.emotion-0 .slick-list:focus {
  outline: none;
}

.emotion-0 .slick-list.dragging {
  cursor: pointer;
  cursor: hand;
}

.emotion-0 .slick-track {
  position: relative;
  top: 0;
  left: 0;
  display: block;
  margin-left: auto;
  margin-right: auto;
}

.emotion-0 .slick-track:before,
.emotion-0 .slick-track:after {
  display: table;
  content: "";
}

.emotion-0 .slick-track:after {
  clear: both;
}

.emotion-0 .slick-slide {
  width: 48px!important;
  display: none;
  float: left;
  height: 100%;
  min-height: 1px;
}

.emotion-0 .slick-slide img {
  display: block;
}

.emotion-0 .slick-slide.slick-loading img {
  display: none;
}

.emotion-0 .slick-slide.dragging img {
  pointer-events: none;
}

.emotion-0 [dir="rtl"] .slick-slide {
  float: right;
}

.emotion-0 .slick-initialized .slick-slide,
.emotion-0 .slick-vertical .slick-slide {
  display: block;
}

.emotion-0 .slick-vertical .slick-slide {
  border: 1px solid transparent;
  height: auto;
}

.emotion-0 .slick-loading .slick-track,
.emotion-0 .slick-loading .slick-slide {
  visibility: hidden;
}

.emotion-0 .slick-loading .slick-list {
  background: #fff url("./ajax-loader.gif") center center no-repeat;
}

.emotion-0 .slick-arrow.slick-hidden {
  display: none;
}

.emotion-0 .slick-prev,
.emotion-0 .slick-next {
  font-size: 0;
  line-height: 0;
  position: absolute;
  top: 50%;
  display: block;
  padding: 0;
  cursor: pointer;
  color: transparent;
  border: none;
  outline: none;
  background: transparent;
}

.emotion-0 .slick-prev:hover,
.emotion-0 .slick-next:hover,
.emotion-0 .slick-prev:focus,
.emotion-0 .slick-next:focus {
  color: transparent;
  outline: none;
  background: transparent;
  opacity: 1;
}

.emotion-0 .slick-prev.slick-disabled,
.emotion-0 .slick-next.slick-disabled {
  opacity: 0.25;
}

.emotion-0 .slick-prev {
  left: -25px;
  -webkit-transform: translate(0, -50%) rotate(90deg);
  -moz-transform: translate(0, -50%) rotate(90deg);
  -ms-transform: translate(0, -50%) rotate(90deg);
  transform: translate(0, -50%) rotate(90deg);
}

.emotion-0 .slick-prev span {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-0 [dir="rtl"] .slick-prev {
  right: -25px;
  left: auto;
}

.emotion-0 .slick-next {
  right: -25px;
  -webkit-transform: translate(0, -50%) rotate(-90deg);
  -moz-transform: translate(0, -50%) rotate(-90deg);
  -ms-transform: translate(0, -50%) rotate(-90deg);
  transform: translate(0, -50%) rotate(-90deg);
}

.emotion-0 .slick-next span {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-0 [dir="rtl"] .slick-next {
  right: auto;
  left: -25px;
}

.emotion-0 .slick-dotted.slick-slider {
  margin-bottom: 30px;
}

.emotion-0 .slick-dots {
  position: absolute;
  bottom: -25px;
  display: block;
  width: 100%;
  padding: 0;
  margin: 0;
  list-style: none;
  text-align: center;
}

.emotion-0 .slick-dots li {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: 20px;
  height: 20px;
  margin: 0 5px;
  padding: 0;
  cursor: pointer;
}

.emotion-0 .slick-dots li.slick-active button:before {
  opacity: 0.75;
  color: black;
}

.emotion-0 .slick-dots li button {
  font-size: 0;
  line-height: 0;
  display: block;
  padding: 5px;
  cursor: pointer;
  color: transparent;
  border: 0;
  outline: none;
  background: transparent;
}

.emotion-0 .slick-dots li button:hover,
.emotion-0 .slick-dots li button:focus {
  outline: none;
}

.emotion-0 .slick-dots li button:hover:before,
.emotion-0 .slick-dots li button:focus:before,
.emotion-0 .slick-dots li button:hover:before,
.emotion-0 .slick-dots li button:focus:before {
  opacity: 1;
}

.emotion-0 .slick-dots li button:before {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 5px;
  height: 5px;
  content: "";
  opacity: 0.25;
  background-color: black;
  border-radius: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

<div
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`CarouselContainer snapshots renders swatch size default state correctly BananaRepublicFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  position: relative;
}

.emotion-0 .slick-slider {
  position: relative;
  display: block;
  box-sizing: border-box;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  -webkit-touch-callout: none;
  touch-action: pan-y;
  -webkit-tap-highlight-color: transparent;
  scroll-behavior: smooth;
}

.emotion-0 .slick-slider .slick-track,
.emotion-0 .slick-slider .slick-list {
  -webkit-transform: translate3d(0, 0, 0);
  -moz-transform: translate3d(0, 0, 0);
  -ms-transform: translate3d(0, 0, 0);
  transform: translate3d(0, 0, 0);
}

.emotion-0 .slick-list {
  position: relative;
  display: block;
  overflow-x: hidden;
  overflow-y: hidden;
  margin: 0;
  padding: 0;
}

.emotion-0 .slick-list:focus {
  outline: none;
}

.emotion-0 .slick-list.dragging {
  cursor: pointer;
  cursor: hand;
}

.emotion-0 .slick-track {
  position: relative;
  top: 0;
  left: 0;
  display: block;
  margin-left: auto;
  margin-right: auto;
}

.emotion-0 .slick-track:before,
.emotion-0 .slick-track:after {
  display: table;
  content: "";
}

.emotion-0 .slick-track:after {
  clear: both;
}

.emotion-0 .slick-slide {
  width: 48px!important;
  display: none;
  float: left;
  height: 100%;
  min-height: 1px;
}

.emotion-0 .slick-slide img {
  display: block;
}

.emotion-0 .slick-slide.slick-loading img {
  display: none;
}

.emotion-0 .slick-slide.dragging img {
  pointer-events: none;
}

.emotion-0 [dir="rtl"] .slick-slide {
  float: right;
}

.emotion-0 .slick-initialized .slick-slide,
.emotion-0 .slick-vertical .slick-slide {
  display: block;
}

.emotion-0 .slick-vertical .slick-slide {
  border: 1px solid transparent;
  height: auto;
}

.emotion-0 .slick-loading .slick-track,
.emotion-0 .slick-loading .slick-slide {
  visibility: hidden;
}

.emotion-0 .slick-loading .slick-list {
  background: #fff url("./ajax-loader.gif") center center no-repeat;
}

.emotion-0 .slick-arrow.slick-hidden {
  display: none;
}

.emotion-0 .slick-prev,
.emotion-0 .slick-next {
  font-size: 0;
  line-height: 0;
  position: absolute;
  top: 50%;
  display: block;
  padding: 0;
  cursor: pointer;
  color: transparent;
  border: none;
  outline: none;
  background: transparent;
}

.emotion-0 .slick-prev:hover,
.emotion-0 .slick-next:hover,
.emotion-0 .slick-prev:focus,
.emotion-0 .slick-next:focus {
  color: transparent;
  outline: none;
  background: transparent;
  opacity: 1;
}

.emotion-0 .slick-prev.slick-disabled,
.emotion-0 .slick-next.slick-disabled {
  opacity: 0.25;
}

.emotion-0 .slick-prev {
  left: -25px;
  -webkit-transform: translate(0, -50%) rotate(90deg);
  -moz-transform: translate(0, -50%) rotate(90deg);
  -ms-transform: translate(0, -50%) rotate(90deg);
  transform: translate(0, -50%) rotate(90deg);
}

.emotion-0 .slick-prev span {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-0 [dir="rtl"] .slick-prev {
  right: -25px;
  left: auto;
}

.emotion-0 .slick-next {
  right: -25px;
  -webkit-transform: translate(0, -50%) rotate(-90deg);
  -moz-transform: translate(0, -50%) rotate(-90deg);
  -ms-transform: translate(0, -50%) rotate(-90deg);
  transform: translate(0, -50%) rotate(-90deg);
}

.emotion-0 .slick-next span {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-0 [dir="rtl"] .slick-next {
  right: auto;
  left: -25px;
}

.emotion-0 .slick-dotted.slick-slider {
  margin-bottom: 30px;
}

.emotion-0 .slick-dots {
  position: absolute;
  bottom: -25px;
  display: block;
  width: 100%;
  padding: 0;
  margin: 0;
  list-style: none;
  text-align: center;
}

.emotion-0 .slick-dots li {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: 20px;
  height: 20px;
  margin: 0 5px;
  padding: 0;
  cursor: pointer;
}

.emotion-0 .slick-dots li.slick-active button:before {
  opacity: 0.75;
  color: black;
}

.emotion-0 .slick-dots li button {
  font-size: 0;
  line-height: 0;
  display: block;
  padding: 5px;
  cursor: pointer;
  color: transparent;
  border: 0;
  outline: none;
  background: transparent;
}

.emotion-0 .slick-dots li button:hover,
.emotion-0 .slick-dots li button:focus {
  outline: none;
}

.emotion-0 .slick-dots li button:hover:before,
.emotion-0 .slick-dots li button:focus:before,
.emotion-0 .slick-dots li button:hover:before,
.emotion-0 .slick-dots li button:focus:before {
  opacity: 1;
}

.emotion-0 .slick-dots li button:before {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 5px;
  height: 5px;
  content: "";
  opacity: 0.25;
  background-color: black;
  border-radius: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

<div
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`CarouselContainer snapshots renders swatch size default state correctly Gap 1`] = `
<DocumentFragment>
  .emotion-0 {
  position: relative;
}

.emotion-0 .slick-slider {
  position: relative;
  display: block;
  box-sizing: border-box;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  -webkit-touch-callout: none;
  touch-action: pan-y;
  -webkit-tap-highlight-color: transparent;
  scroll-behavior: smooth;
}

.emotion-0 .slick-slider .slick-track,
.emotion-0 .slick-slider .slick-list {
  -webkit-transform: translate3d(0, 0, 0);
  -moz-transform: translate3d(0, 0, 0);
  -ms-transform: translate3d(0, 0, 0);
  transform: translate3d(0, 0, 0);
}

.emotion-0 .slick-list {
  position: relative;
  display: block;
  overflow-x: hidden;
  overflow-y: hidden;
  margin: 0;
  padding: 0;
}

.emotion-0 .slick-list:focus {
  outline: none;
}

.emotion-0 .slick-list.dragging {
  cursor: pointer;
  cursor: hand;
}

.emotion-0 .slick-track {
  position: relative;
  top: 0;
  left: 0;
  display: block;
  margin-left: auto;
  margin-right: auto;
}

.emotion-0 .slick-track:before,
.emotion-0 .slick-track:after {
  display: table;
  content: "";
}

.emotion-0 .slick-track:after {
  clear: both;
}

.emotion-0 .slick-slide {
  width: 48px!important;
  display: none;
  float: left;
  height: 100%;
  min-height: 1px;
}

.emotion-0 .slick-slide img {
  display: block;
}

.emotion-0 .slick-slide.slick-loading img {
  display: none;
}

.emotion-0 .slick-slide.dragging img {
  pointer-events: none;
}

.emotion-0 [dir="rtl"] .slick-slide {
  float: right;
}

.emotion-0 .slick-initialized .slick-slide,
.emotion-0 .slick-vertical .slick-slide {
  display: block;
}

.emotion-0 .slick-vertical .slick-slide {
  border: 1px solid transparent;
  height: auto;
}

.emotion-0 .slick-loading .slick-track,
.emotion-0 .slick-loading .slick-slide {
  visibility: hidden;
}

.emotion-0 .slick-loading .slick-list {
  background: #fff url("./ajax-loader.gif") center center no-repeat;
}

.emotion-0 .slick-arrow.slick-hidden {
  display: none;
}

.emotion-0 .slick-prev,
.emotion-0 .slick-next {
  font-size: 0;
  line-height: 0;
  position: absolute;
  top: 50%;
  display: block;
  padding: 0;
  cursor: pointer;
  color: transparent;
  border: none;
  outline: none;
  background: transparent;
}

.emotion-0 .slick-prev:hover,
.emotion-0 .slick-next:hover,
.emotion-0 .slick-prev:focus,
.emotion-0 .slick-next:focus {
  color: transparent;
  outline: none;
  background: transparent;
  opacity: 1;
}

.emotion-0 .slick-prev.slick-disabled,
.emotion-0 .slick-next.slick-disabled {
  opacity: 0.25;
}

.emotion-0 .slick-prev {
  left: -25px;
  -webkit-transform: translate(0, -50%) rotate(90deg);
  -moz-transform: translate(0, -50%) rotate(90deg);
  -ms-transform: translate(0, -50%) rotate(90deg);
  transform: translate(0, -50%) rotate(90deg);
}

.emotion-0 .slick-prev span {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-0 [dir="rtl"] .slick-prev {
  right: -25px;
  left: auto;
}

.emotion-0 .slick-next {
  right: -25px;
  -webkit-transform: translate(0, -50%) rotate(-90deg);
  -moz-transform: translate(0, -50%) rotate(-90deg);
  -ms-transform: translate(0, -50%) rotate(-90deg);
  transform: translate(0, -50%) rotate(-90deg);
}

.emotion-0 .slick-next span {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-0 [dir="rtl"] .slick-next {
  right: auto;
  left: -25px;
}

.emotion-0 .slick-dotted.slick-slider {
  margin-bottom: 30px;
}

.emotion-0 .slick-dots {
  position: absolute;
  bottom: -25px;
  display: block;
  width: 100%;
  padding: 0;
  margin: 0;
  list-style: none;
  text-align: center;
}

.emotion-0 .slick-dots li {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: 20px;
  height: 20px;
  margin: 0 5px;
  padding: 0;
  cursor: pointer;
}

.emotion-0 .slick-dots li.slick-active button:before {
  opacity: 0.75;
  color: black;
}

.emotion-0 .slick-dots li button {
  font-size: 0;
  line-height: 0;
  display: block;
  padding: 5px;
  cursor: pointer;
  color: transparent;
  border: 0;
  outline: none;
  background: transparent;
}

.emotion-0 .slick-dots li button:hover,
.emotion-0 .slick-dots li button:focus {
  outline: none;
}

.emotion-0 .slick-dots li button:hover:before,
.emotion-0 .slick-dots li button:focus:before,
.emotion-0 .slick-dots li button:hover:before,
.emotion-0 .slick-dots li button:focus:before {
  opacity: 1;
}

.emotion-0 .slick-dots li button:before {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 5px;
  height: 5px;
  content: "";
  opacity: 0.25;
  background-color: black;
  border-radius: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

<div
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`CarouselContainer snapshots renders swatch size default state correctly Gap in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  position: relative;
}

.emotion-0 .slick-slider {
  position: relative;
  display: block;
  box-sizing: border-box;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  -webkit-touch-callout: none;
  touch-action: pan-y;
  -webkit-tap-highlight-color: transparent;
  scroll-behavior: smooth;
}

.emotion-0 .slick-slider .slick-track,
.emotion-0 .slick-slider .slick-list {
  -webkit-transform: translate3d(0, 0, 0);
  -moz-transform: translate3d(0, 0, 0);
  -ms-transform: translate3d(0, 0, 0);
  transform: translate3d(0, 0, 0);
}

.emotion-0 .slick-list {
  position: relative;
  display: block;
  overflow-x: hidden;
  overflow-y: hidden;
  margin: 0;
  padding: 0;
}

.emotion-0 .slick-list:focus {
  outline: none;
}

.emotion-0 .slick-list.dragging {
  cursor: pointer;
  cursor: hand;
}

.emotion-0 .slick-track {
  position: relative;
  top: 0;
  left: 0;
  display: block;
  margin-left: auto;
  margin-right: auto;
}

.emotion-0 .slick-track:before,
.emotion-0 .slick-track:after {
  display: table;
  content: "";
}

.emotion-0 .slick-track:after {
  clear: both;
}

.emotion-0 .slick-slide {
  width: 48px!important;
  display: none;
  float: left;
  height: 100%;
  min-height: 1px;
}

.emotion-0 .slick-slide img {
  display: block;
}

.emotion-0 .slick-slide.slick-loading img {
  display: none;
}

.emotion-0 .slick-slide.dragging img {
  pointer-events: none;
}

.emotion-0 [dir="rtl"] .slick-slide {
  float: right;
}

.emotion-0 .slick-initialized .slick-slide,
.emotion-0 .slick-vertical .slick-slide {
  display: block;
}

.emotion-0 .slick-vertical .slick-slide {
  border: 1px solid transparent;
  height: auto;
}

.emotion-0 .slick-loading .slick-track,
.emotion-0 .slick-loading .slick-slide {
  visibility: hidden;
}

.emotion-0 .slick-loading .slick-list {
  background: #fff url("./ajax-loader.gif") center center no-repeat;
}

.emotion-0 .slick-arrow.slick-hidden {
  display: none;
}

.emotion-0 .slick-prev,
.emotion-0 .slick-next {
  font-size: 0;
  line-height: 0;
  position: absolute;
  top: 50%;
  display: block;
  padding: 0;
  cursor: pointer;
  color: transparent;
  border: none;
  outline: none;
  background: transparent;
}

.emotion-0 .slick-prev:hover,
.emotion-0 .slick-next:hover,
.emotion-0 .slick-prev:focus,
.emotion-0 .slick-next:focus {
  color: transparent;
  outline: none;
  background: transparent;
  opacity: 1;
}

.emotion-0 .slick-prev.slick-disabled,
.emotion-0 .slick-next.slick-disabled {
  opacity: 0.25;
}

.emotion-0 .slick-prev {
  left: -25px;
  -webkit-transform: translate(0, -50%) rotate(90deg);
  -moz-transform: translate(0, -50%) rotate(90deg);
  -ms-transform: translate(0, -50%) rotate(90deg);
  transform: translate(0, -50%) rotate(90deg);
}

.emotion-0 .slick-prev span {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-0 [dir="rtl"] .slick-prev {
  right: -25px;
  left: auto;
}

.emotion-0 .slick-next {
  right: -25px;
  -webkit-transform: translate(0, -50%) rotate(-90deg);
  -moz-transform: translate(0, -50%) rotate(-90deg);
  -ms-transform: translate(0, -50%) rotate(-90deg);
  transform: translate(0, -50%) rotate(-90deg);
}

.emotion-0 .slick-next span {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-0 [dir="rtl"] .slick-next {
  right: auto;
  left: -25px;
}

.emotion-0 .slick-dotted.slick-slider {
  margin-bottom: 30px;
}

.emotion-0 .slick-dots {
  position: absolute;
  bottom: -25px;
  display: block;
  width: 100%;
  padding: 0;
  margin: 0;
  list-style: none;
  text-align: center;
}

.emotion-0 .slick-dots li {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: 20px;
  height: 20px;
  margin: 0 5px;
  padding: 0;
  cursor: pointer;
}

.emotion-0 .slick-dots li.slick-active button:before {
  opacity: 0.75;
  color: black;
}

.emotion-0 .slick-dots li button {
  font-size: 0;
  line-height: 0;
  display: block;
  padding: 5px;
  cursor: pointer;
  color: transparent;
  border: 0;
  outline: none;
  background: transparent;
}

.emotion-0 .slick-dots li button:hover,
.emotion-0 .slick-dots li button:focus {
  outline: none;
}

.emotion-0 .slick-dots li button:hover:before,
.emotion-0 .slick-dots li button:focus:before,
.emotion-0 .slick-dots li button:hover:before,
.emotion-0 .slick-dots li button:focus:before {
  opacity: 1;
}

.emotion-0 .slick-dots li button:before {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 5px;
  height: 5px;
  content: "";
  opacity: 0.25;
  background-color: black;
  border-radius: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

<div
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`CarouselContainer snapshots renders swatch size default state correctly GapFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  position: relative;
}

.emotion-0 .slick-slider {
  position: relative;
  display: block;
  box-sizing: border-box;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  -webkit-touch-callout: none;
  touch-action: pan-y;
  -webkit-tap-highlight-color: transparent;
  scroll-behavior: smooth;
}

.emotion-0 .slick-slider .slick-track,
.emotion-0 .slick-slider .slick-list {
  -webkit-transform: translate3d(0, 0, 0);
  -moz-transform: translate3d(0, 0, 0);
  -ms-transform: translate3d(0, 0, 0);
  transform: translate3d(0, 0, 0);
}

.emotion-0 .slick-list {
  position: relative;
  display: block;
  overflow-x: hidden;
  overflow-y: hidden;
  margin: 0;
  padding: 0;
}

.emotion-0 .slick-list:focus {
  outline: none;
}

.emotion-0 .slick-list.dragging {
  cursor: pointer;
  cursor: hand;
}

.emotion-0 .slick-track {
  position: relative;
  top: 0;
  left: 0;
  display: block;
  margin-left: auto;
  margin-right: auto;
}

.emotion-0 .slick-track:before,
.emotion-0 .slick-track:after {
  display: table;
  content: "";
}

.emotion-0 .slick-track:after {
  clear: both;
}

.emotion-0 .slick-slide {
  width: 48px!important;
  display: none;
  float: left;
  height: 100%;
  min-height: 1px;
}

.emotion-0 .slick-slide img {
  display: block;
}

.emotion-0 .slick-slide.slick-loading img {
  display: none;
}

.emotion-0 .slick-slide.dragging img {
  pointer-events: none;
}

.emotion-0 [dir="rtl"] .slick-slide {
  float: right;
}

.emotion-0 .slick-initialized .slick-slide,
.emotion-0 .slick-vertical .slick-slide {
  display: block;
}

.emotion-0 .slick-vertical .slick-slide {
  border: 1px solid transparent;
  height: auto;
}

.emotion-0 .slick-loading .slick-track,
.emotion-0 .slick-loading .slick-slide {
  visibility: hidden;
}

.emotion-0 .slick-loading .slick-list {
  background: #fff url("./ajax-loader.gif") center center no-repeat;
}

.emotion-0 .slick-arrow.slick-hidden {
  display: none;
}

.emotion-0 .slick-prev,
.emotion-0 .slick-next {
  font-size: 0;
  line-height: 0;
  position: absolute;
  top: 50%;
  display: block;
  padding: 0;
  cursor: pointer;
  color: transparent;
  border: none;
  outline: none;
  background: transparent;
}

.emotion-0 .slick-prev:hover,
.emotion-0 .slick-next:hover,
.emotion-0 .slick-prev:focus,
.emotion-0 .slick-next:focus {
  color: transparent;
  outline: none;
  background: transparent;
  opacity: 1;
}

.emotion-0 .slick-prev.slick-disabled,
.emotion-0 .slick-next.slick-disabled {
  opacity: 0.25;
}

.emotion-0 .slick-prev {
  left: -25px;
  -webkit-transform: translate(0, -50%) rotate(90deg);
  -moz-transform: translate(0, -50%) rotate(90deg);
  -ms-transform: translate(0, -50%) rotate(90deg);
  transform: translate(0, -50%) rotate(90deg);
}

.emotion-0 .slick-prev span {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-0 [dir="rtl"] .slick-prev {
  right: -25px;
  left: auto;
}

.emotion-0 .slick-next {
  right: -25px;
  -webkit-transform: translate(0, -50%) rotate(-90deg);
  -moz-transform: translate(0, -50%) rotate(-90deg);
  -ms-transform: translate(0, -50%) rotate(-90deg);
  transform: translate(0, -50%) rotate(-90deg);
}

.emotion-0 .slick-next span {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-0 [dir="rtl"] .slick-next {
  right: auto;
  left: -25px;
}

.emotion-0 .slick-dotted.slick-slider {
  margin-bottom: 30px;
}

.emotion-0 .slick-dots {
  position: absolute;
  bottom: -25px;
  display: block;
  width: 100%;
  padding: 0;
  margin: 0;
  list-style: none;
  text-align: center;
}

.emotion-0 .slick-dots li {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: 20px;
  height: 20px;
  margin: 0 5px;
  padding: 0;
  cursor: pointer;
}

.emotion-0 .slick-dots li.slick-active button:before {
  opacity: 0.75;
  color: black;
}

.emotion-0 .slick-dots li button {
  font-size: 0;
  line-height: 0;
  display: block;
  padding: 5px;
  cursor: pointer;
  color: transparent;
  border: 0;
  outline: none;
  background: transparent;
}

.emotion-0 .slick-dots li button:hover,
.emotion-0 .slick-dots li button:focus {
  outline: none;
}

.emotion-0 .slick-dots li button:hover:before,
.emotion-0 .slick-dots li button:focus:before,
.emotion-0 .slick-dots li button:hover:before,
.emotion-0 .slick-dots li button:focus:before {
  opacity: 1;
}

.emotion-0 .slick-dots li button:before {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 5px;
  height: 5px;
  content: "";
  opacity: 0.25;
  background-color: black;
  border-radius: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

<div
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`CarouselContainer snapshots renders swatch size default state correctly GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  position: relative;
}

.emotion-0 .slick-slider {
  position: relative;
  display: block;
  box-sizing: border-box;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  -webkit-touch-callout: none;
  touch-action: pan-y;
  -webkit-tap-highlight-color: transparent;
  scroll-behavior: smooth;
}

.emotion-0 .slick-slider .slick-track,
.emotion-0 .slick-slider .slick-list {
  -webkit-transform: translate3d(0, 0, 0);
  -moz-transform: translate3d(0, 0, 0);
  -ms-transform: translate3d(0, 0, 0);
  transform: translate3d(0, 0, 0);
}

.emotion-0 .slick-list {
  position: relative;
  display: block;
  overflow-x: hidden;
  overflow-y: hidden;
  margin: 0;
  padding: 0;
}

.emotion-0 .slick-list:focus {
  outline: none;
}

.emotion-0 .slick-list.dragging {
  cursor: pointer;
  cursor: hand;
}

.emotion-0 .slick-track {
  position: relative;
  top: 0;
  left: 0;
  display: block;
  margin-left: auto;
  margin-right: auto;
}

.emotion-0 .slick-track:before,
.emotion-0 .slick-track:after {
  display: table;
  content: "";
}

.emotion-0 .slick-track:after {
  clear: both;
}

.emotion-0 .slick-slide {
  width: 48px!important;
  display: none;
  float: left;
  height: 100%;
  min-height: 1px;
}

.emotion-0 .slick-slide img {
  display: block;
}

.emotion-0 .slick-slide.slick-loading img {
  display: none;
}

.emotion-0 .slick-slide.dragging img {
  pointer-events: none;
}

.emotion-0 [dir="rtl"] .slick-slide {
  float: right;
}

.emotion-0 .slick-initialized .slick-slide,
.emotion-0 .slick-vertical .slick-slide {
  display: block;
}

.emotion-0 .slick-vertical .slick-slide {
  border: 1px solid transparent;
  height: auto;
}

.emotion-0 .slick-loading .slick-track,
.emotion-0 .slick-loading .slick-slide {
  visibility: hidden;
}

.emotion-0 .slick-loading .slick-list {
  background: #fff url("./ajax-loader.gif") center center no-repeat;
}

.emotion-0 .slick-arrow.slick-hidden {
  display: none;
}

.emotion-0 .slick-prev,
.emotion-0 .slick-next {
  font-size: 0;
  line-height: 0;
  position: absolute;
  top: 50%;
  display: block;
  padding: 0;
  cursor: pointer;
  color: transparent;
  border: none;
  outline: none;
  background: transparent;
}

.emotion-0 .slick-prev:hover,
.emotion-0 .slick-next:hover,
.emotion-0 .slick-prev:focus,
.emotion-0 .slick-next:focus {
  color: transparent;
  outline: none;
  background: transparent;
  opacity: 1;
}

.emotion-0 .slick-prev.slick-disabled,
.emotion-0 .slick-next.slick-disabled {
  opacity: 0.25;
}

.emotion-0 .slick-prev {
  left: -25px;
  -webkit-transform: translate(0, -50%) rotate(90deg);
  -moz-transform: translate(0, -50%) rotate(90deg);
  -ms-transform: translate(0, -50%) rotate(90deg);
  transform: translate(0, -50%) rotate(90deg);
}

.emotion-0 .slick-prev span {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-0 [dir="rtl"] .slick-prev {
  right: -25px;
  left: auto;
}

.emotion-0 .slick-next {
  right: -25px;
  -webkit-transform: translate(0, -50%) rotate(-90deg);
  -moz-transform: translate(0, -50%) rotate(-90deg);
  -ms-transform: translate(0, -50%) rotate(-90deg);
  transform: translate(0, -50%) rotate(-90deg);
}

.emotion-0 .slick-next span {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-0 [dir="rtl"] .slick-next {
  right: auto;
  left: -25px;
}

.emotion-0 .slick-dotted.slick-slider {
  margin-bottom: 30px;
}

.emotion-0 .slick-dots {
  position: absolute;
  bottom: -25px;
  display: block;
  width: 100%;
  padding: 0;
  margin: 0;
  list-style: none;
  text-align: center;
}

.emotion-0 .slick-dots li {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: 20px;
  height: 20px;
  margin: 0 5px;
  padding: 0;
  cursor: pointer;
}

.emotion-0 .slick-dots li.slick-active button:before {
  opacity: 0.75;
  color: black;
}

.emotion-0 .slick-dots li button {
  font-size: 0;
  line-height: 0;
  display: block;
  padding: 5px;
  cursor: pointer;
  color: transparent;
  border: 0;
  outline: none;
  background: transparent;
}

.emotion-0 .slick-dots li button:hover,
.emotion-0 .slick-dots li button:focus {
  outline: none;
}

.emotion-0 .slick-dots li button:hover:before,
.emotion-0 .slick-dots li button:focus:before,
.emotion-0 .slick-dots li button:hover:before,
.emotion-0 .slick-dots li button:focus:before {
  opacity: 1;
}

.emotion-0 .slick-dots li button:before {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 5px;
  height: 5px;
  content: "";
  opacity: 0.25;
  background-color: black;
  border-radius: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

<div
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`CarouselContainer snapshots renders swatch size default state correctly OldNavy 1`] = `
<DocumentFragment>
  .emotion-0 {
  position: relative;
}

.emotion-0 .slick-slider {
  position: relative;
  display: block;
  box-sizing: border-box;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  -webkit-touch-callout: none;
  touch-action: pan-y;
  -webkit-tap-highlight-color: transparent;
  scroll-behavior: smooth;
}

.emotion-0 .slick-slider .slick-track,
.emotion-0 .slick-slider .slick-list {
  -webkit-transform: translate3d(0, 0, 0);
  -moz-transform: translate3d(0, 0, 0);
  -ms-transform: translate3d(0, 0, 0);
  transform: translate3d(0, 0, 0);
}

.emotion-0 .slick-list {
  position: relative;
  display: block;
  overflow-x: hidden;
  overflow-y: hidden;
  margin: 0;
  padding: 0;
}

.emotion-0 .slick-list:focus {
  outline: none;
}

.emotion-0 .slick-list.dragging {
  cursor: pointer;
  cursor: hand;
}

.emotion-0 .slick-track {
  position: relative;
  top: 0;
  left: 0;
  display: block;
  margin-left: auto;
  margin-right: auto;
}

.emotion-0 .slick-track:before,
.emotion-0 .slick-track:after {
  display: table;
  content: "";
}

.emotion-0 .slick-track:after {
  clear: both;
}

.emotion-0 .slick-slide {
  width: 48px!important;
  display: none;
  float: left;
  height: 100%;
  min-height: 1px;
}

.emotion-0 .slick-slide img {
  display: block;
}

.emotion-0 .slick-slide.slick-loading img {
  display: none;
}

.emotion-0 .slick-slide.dragging img {
  pointer-events: none;
}

.emotion-0 [dir="rtl"] .slick-slide {
  float: right;
}

.emotion-0 .slick-initialized .slick-slide,
.emotion-0 .slick-vertical .slick-slide {
  display: block;
}

.emotion-0 .slick-vertical .slick-slide {
  border: 1px solid transparent;
  height: auto;
}

.emotion-0 .slick-loading .slick-track,
.emotion-0 .slick-loading .slick-slide {
  visibility: hidden;
}

.emotion-0 .slick-loading .slick-list {
  background: #fff url("./ajax-loader.gif") center center no-repeat;
}

.emotion-0 .slick-arrow.slick-hidden {
  display: none;
}

.emotion-0 .slick-prev,
.emotion-0 .slick-next {
  font-size: 0;
  line-height: 0;
  position: absolute;
  top: 50%;
  display: block;
  padding: 0;
  cursor: pointer;
  color: transparent;
  border: none;
  outline: none;
  background: transparent;
}

.emotion-0 .slick-prev:hover,
.emotion-0 .slick-next:hover,
.emotion-0 .slick-prev:focus,
.emotion-0 .slick-next:focus {
  color: transparent;
  outline: none;
  background: transparent;
  opacity: 1;
}

.emotion-0 .slick-prev.slick-disabled,
.emotion-0 .slick-next.slick-disabled {
  opacity: 0.25;
}

.emotion-0 .slick-prev {
  left: -25px;
  -webkit-transform: translate(0, -50%) rotate(90deg);
  -moz-transform: translate(0, -50%) rotate(90deg);
  -ms-transform: translate(0, -50%) rotate(90deg);
  transform: translate(0, -50%) rotate(90deg);
}

.emotion-0 .slick-prev span {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-0 [dir="rtl"] .slick-prev {
  right: -25px;
  left: auto;
}

.emotion-0 .slick-next {
  right: -25px;
  -webkit-transform: translate(0, -50%) rotate(-90deg);
  -moz-transform: translate(0, -50%) rotate(-90deg);
  -ms-transform: translate(0, -50%) rotate(-90deg);
  transform: translate(0, -50%) rotate(-90deg);
}

.emotion-0 .slick-next span {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-0 [dir="rtl"] .slick-next {
  right: auto;
  left: -25px;
}

.emotion-0 .slick-dotted.slick-slider {
  margin-bottom: 30px;
}

.emotion-0 .slick-dots {
  position: absolute;
  bottom: -25px;
  display: block;
  width: 100%;
  padding: 0;
  margin: 0;
  list-style: none;
  text-align: center;
}

.emotion-0 .slick-dots li {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: 20px;
  height: 20px;
  margin: 0 5px;
  padding: 0;
  cursor: pointer;
}

.emotion-0 .slick-dots li.slick-active button:before {
  opacity: 0.75;
  color: black;
}

.emotion-0 .slick-dots li button {
  font-size: 0;
  line-height: 0;
  display: block;
  padding: 5px;
  cursor: pointer;
  color: transparent;
  border: 0;
  outline: none;
  background: transparent;
}

.emotion-0 .slick-dots li button:hover,
.emotion-0 .slick-dots li button:focus {
  outline: none;
}

.emotion-0 .slick-dots li button:hover:before,
.emotion-0 .slick-dots li button:focus:before,
.emotion-0 .slick-dots li button:hover:before,
.emotion-0 .slick-dots li button:focus:before {
  opacity: 1;
}

.emotion-0 .slick-dots li button:before {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 5px;
  height: 5px;
  content: "";
  opacity: 0.25;
  background-color: black;
  border-radius: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

<div
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`CarouselContainer snapshots renders swatch size default state correctly OldNavy in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  position: relative;
}

.emotion-0 .slick-slider {
  position: relative;
  display: block;
  box-sizing: border-box;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  -webkit-touch-callout: none;
  touch-action: pan-y;
  -webkit-tap-highlight-color: transparent;
  scroll-behavior: smooth;
}

.emotion-0 .slick-slider .slick-track,
.emotion-0 .slick-slider .slick-list {
  -webkit-transform: translate3d(0, 0, 0);
  -moz-transform: translate3d(0, 0, 0);
  -ms-transform: translate3d(0, 0, 0);
  transform: translate3d(0, 0, 0);
}

.emotion-0 .slick-list {
  position: relative;
  display: block;
  overflow-x: hidden;
  overflow-y: hidden;
  margin: 0;
  padding: 0;
}

.emotion-0 .slick-list:focus {
  outline: none;
}

.emotion-0 .slick-list.dragging {
  cursor: pointer;
  cursor: hand;
}

.emotion-0 .slick-track {
  position: relative;
  top: 0;
  left: 0;
  display: block;
  margin-left: auto;
  margin-right: auto;
}

.emotion-0 .slick-track:before,
.emotion-0 .slick-track:after {
  display: table;
  content: "";
}

.emotion-0 .slick-track:after {
  clear: both;
}

.emotion-0 .slick-slide {
  width: 48px!important;
  display: none;
  float: left;
  height: 100%;
  min-height: 1px;
}

.emotion-0 .slick-slide img {
  display: block;
}

.emotion-0 .slick-slide.slick-loading img {
  display: none;
}

.emotion-0 .slick-slide.dragging img {
  pointer-events: none;
}

.emotion-0 [dir="rtl"] .slick-slide {
  float: right;
}

.emotion-0 .slick-initialized .slick-slide,
.emotion-0 .slick-vertical .slick-slide {
  display: block;
}

.emotion-0 .slick-vertical .slick-slide {
  border: 1px solid transparent;
  height: auto;
}

.emotion-0 .slick-loading .slick-track,
.emotion-0 .slick-loading .slick-slide {
  visibility: hidden;
}

.emotion-0 .slick-loading .slick-list {
  background: #fff url("./ajax-loader.gif") center center no-repeat;
}

.emotion-0 .slick-arrow.slick-hidden {
  display: none;
}

.emotion-0 .slick-prev,
.emotion-0 .slick-next {
  font-size: 0;
  line-height: 0;
  position: absolute;
  top: 50%;
  display: block;
  padding: 0;
  cursor: pointer;
  color: transparent;
  border: none;
  outline: none;
  background: transparent;
}

.emotion-0 .slick-prev:hover,
.emotion-0 .slick-next:hover,
.emotion-0 .slick-prev:focus,
.emotion-0 .slick-next:focus {
  color: transparent;
  outline: none;
  background: transparent;
  opacity: 1;
}

.emotion-0 .slick-prev.slick-disabled,
.emotion-0 .slick-next.slick-disabled {
  opacity: 0.25;
}

.emotion-0 .slick-prev {
  left: -25px;
  -webkit-transform: translate(0, -50%) rotate(90deg);
  -moz-transform: translate(0, -50%) rotate(90deg);
  -ms-transform: translate(0, -50%) rotate(90deg);
  transform: translate(0, -50%) rotate(90deg);
}

.emotion-0 .slick-prev span {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-0 [dir="rtl"] .slick-prev {
  right: -25px;
  left: auto;
}

.emotion-0 .slick-next {
  right: -25px;
  -webkit-transform: translate(0, -50%) rotate(-90deg);
  -moz-transform: translate(0, -50%) rotate(-90deg);
  -ms-transform: translate(0, -50%) rotate(-90deg);
  transform: translate(0, -50%) rotate(-90deg);
}

.emotion-0 .slick-next span {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-0 [dir="rtl"] .slick-next {
  right: auto;
  left: -25px;
}

.emotion-0 .slick-dotted.slick-slider {
  margin-bottom: 30px;
}

.emotion-0 .slick-dots {
  position: absolute;
  bottom: -25px;
  display: block;
  width: 100%;
  padding: 0;
  margin: 0;
  list-style: none;
  text-align: center;
}

.emotion-0 .slick-dots li {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: 20px;
  height: 20px;
  margin: 0 5px;
  padding: 0;
  cursor: pointer;
}

.emotion-0 .slick-dots li.slick-active button:before {
  opacity: 0.75;
  color: black;
}

.emotion-0 .slick-dots li button {
  font-size: 0;
  line-height: 0;
  display: block;
  padding: 5px;
  cursor: pointer;
  color: transparent;
  border: 0;
  outline: none;
  background: transparent;
}

.emotion-0 .slick-dots li button:hover,
.emotion-0 .slick-dots li button:focus {
  outline: none;
}

.emotion-0 .slick-dots li button:hover:before,
.emotion-0 .slick-dots li button:focus:before,
.emotion-0 .slick-dots li button:hover:before,
.emotion-0 .slick-dots li button:focus:before {
  opacity: 1;
}

.emotion-0 .slick-dots li button:before {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 5px;
  height: 5px;
  content: "";
  opacity: 0.25;
  background-color: black;
  border-radius: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

<div
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`CarouselContainer snapshots renders swatch size small state correctly Athleta 1`] = `
<DocumentFragment>
  .emotion-0 {
  position: relative;
}

.emotion-0 .slick-slider {
  position: relative;
  display: block;
  box-sizing: border-box;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  -webkit-touch-callout: none;
  touch-action: pan-y;
  -webkit-tap-highlight-color: transparent;
  scroll-behavior: smooth;
}

.emotion-0 .slick-slider .slick-track,
.emotion-0 .slick-slider .slick-list {
  -webkit-transform: translate3d(0, 0, 0);
  -moz-transform: translate3d(0, 0, 0);
  -ms-transform: translate3d(0, 0, 0);
  transform: translate3d(0, 0, 0);
}

.emotion-0 .slick-list {
  position: relative;
  display: block;
  overflow-x: hidden;
  overflow-y: hidden;
  margin: 0;
  padding: 0;
}

.emotion-0 .slick-list:focus {
  outline: none;
}

.emotion-0 .slick-list.dragging {
  cursor: pointer;
  cursor: hand;
}

.emotion-0 .slick-track {
  position: relative;
  top: 0;
  left: 0;
  display: block;
  margin-left: auto;
  margin-right: auto;
}

.emotion-0 .slick-track:before,
.emotion-0 .slick-track:after {
  display: table;
  content: "";
}

.emotion-0 .slick-track:after {
  clear: both;
}

.emotion-0 .slick-slide {
  width: 36px!important;
  display: none;
  float: left;
  height: 100%;
  min-height: 1px;
}

.emotion-0 .slick-slide img {
  display: block;
}

.emotion-0 .slick-slide.slick-loading img {
  display: none;
}

.emotion-0 .slick-slide.dragging img {
  pointer-events: none;
}

.emotion-0 [dir="rtl"] .slick-slide {
  float: right;
}

.emotion-0 .slick-initialized .slick-slide,
.emotion-0 .slick-vertical .slick-slide {
  display: block;
}

.emotion-0 .slick-vertical .slick-slide {
  border: 1px solid transparent;
  height: auto;
}

.emotion-0 .slick-loading .slick-track,
.emotion-0 .slick-loading .slick-slide {
  visibility: hidden;
}

.emotion-0 .slick-loading .slick-list {
  background: #fff url("./ajax-loader.gif") center center no-repeat;
}

.emotion-0 .slick-arrow.slick-hidden {
  display: none;
}

.emotion-0 .slick-prev,
.emotion-0 .slick-next {
  font-size: 0;
  line-height: 0;
  position: absolute;
  top: 50%;
  display: block;
  padding: 0;
  cursor: pointer;
  color: transparent;
  border: none;
  outline: none;
  background: transparent;
}

.emotion-0 .slick-prev:hover,
.emotion-0 .slick-next:hover,
.emotion-0 .slick-prev:focus,
.emotion-0 .slick-next:focus {
  color: transparent;
  outline: none;
  background: transparent;
  opacity: 1;
}

.emotion-0 .slick-prev.slick-disabled,
.emotion-0 .slick-next.slick-disabled {
  opacity: 0.25;
}

.emotion-0 .slick-prev {
  left: -25px;
  -webkit-transform: translate(0, -50%) rotate(90deg);
  -moz-transform: translate(0, -50%) rotate(90deg);
  -ms-transform: translate(0, -50%) rotate(90deg);
  transform: translate(0, -50%) rotate(90deg);
}

.emotion-0 .slick-prev span {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-0 [dir="rtl"] .slick-prev {
  right: -25px;
  left: auto;
}

.emotion-0 .slick-next {
  right: -25px;
  -webkit-transform: translate(0, -50%) rotate(-90deg);
  -moz-transform: translate(0, -50%) rotate(-90deg);
  -ms-transform: translate(0, -50%) rotate(-90deg);
  transform: translate(0, -50%) rotate(-90deg);
}

.emotion-0 .slick-next span {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-0 [dir="rtl"] .slick-next {
  right: auto;
  left: -25px;
}

.emotion-0 .slick-dotted.slick-slider {
  margin-bottom: 30px;
}

.emotion-0 .slick-dots {
  position: absolute;
  bottom: -25px;
  display: block;
  width: 100%;
  padding: 0;
  margin: 0;
  list-style: none;
  text-align: center;
}

.emotion-0 .slick-dots li {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: 20px;
  height: 20px;
  margin: 0 5px;
  padding: 0;
  cursor: pointer;
}

.emotion-0 .slick-dots li.slick-active button:before {
  opacity: 0.75;
  color: black;
}

.emotion-0 .slick-dots li button {
  font-size: 0;
  line-height: 0;
  display: block;
  padding: 5px;
  cursor: pointer;
  color: transparent;
  border: 0;
  outline: none;
  background: transparent;
}

.emotion-0 .slick-dots li button:hover,
.emotion-0 .slick-dots li button:focus {
  outline: none;
}

.emotion-0 .slick-dots li button:hover:before,
.emotion-0 .slick-dots li button:focus:before,
.emotion-0 .slick-dots li button:hover:before,
.emotion-0 .slick-dots li button:focus:before {
  opacity: 1;
}

.emotion-0 .slick-dots li button:before {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 5px;
  height: 5px;
  content: "";
  opacity: 0.25;
  background-color: black;
  border-radius: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

<div
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`CarouselContainer snapshots renders swatch size small state correctly Athleta in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  position: relative;
}

.emotion-0 .slick-slider {
  position: relative;
  display: block;
  box-sizing: border-box;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  -webkit-touch-callout: none;
  touch-action: pan-y;
  -webkit-tap-highlight-color: transparent;
  scroll-behavior: smooth;
}

.emotion-0 .slick-slider .slick-track,
.emotion-0 .slick-slider .slick-list {
  -webkit-transform: translate3d(0, 0, 0);
  -moz-transform: translate3d(0, 0, 0);
  -ms-transform: translate3d(0, 0, 0);
  transform: translate3d(0, 0, 0);
}

.emotion-0 .slick-list {
  position: relative;
  display: block;
  overflow-x: hidden;
  overflow-y: hidden;
  margin: 0;
  padding: 0;
}

.emotion-0 .slick-list:focus {
  outline: none;
}

.emotion-0 .slick-list.dragging {
  cursor: pointer;
  cursor: hand;
}

.emotion-0 .slick-track {
  position: relative;
  top: 0;
  left: 0;
  display: block;
  margin-left: auto;
  margin-right: auto;
}

.emotion-0 .slick-track:before,
.emotion-0 .slick-track:after {
  display: table;
  content: "";
}

.emotion-0 .slick-track:after {
  clear: both;
}

.emotion-0 .slick-slide {
  width: 36px!important;
  display: none;
  float: left;
  height: 100%;
  min-height: 1px;
}

.emotion-0 .slick-slide img {
  display: block;
}

.emotion-0 .slick-slide.slick-loading img {
  display: none;
}

.emotion-0 .slick-slide.dragging img {
  pointer-events: none;
}

.emotion-0 [dir="rtl"] .slick-slide {
  float: right;
}

.emotion-0 .slick-initialized .slick-slide,
.emotion-0 .slick-vertical .slick-slide {
  display: block;
}

.emotion-0 .slick-vertical .slick-slide {
  border: 1px solid transparent;
  height: auto;
}

.emotion-0 .slick-loading .slick-track,
.emotion-0 .slick-loading .slick-slide {
  visibility: hidden;
}

.emotion-0 .slick-loading .slick-list {
  background: #fff url("./ajax-loader.gif") center center no-repeat;
}

.emotion-0 .slick-arrow.slick-hidden {
  display: none;
}

.emotion-0 .slick-prev,
.emotion-0 .slick-next {
  font-size: 0;
  line-height: 0;
  position: absolute;
  top: 50%;
  display: block;
  padding: 0;
  cursor: pointer;
  color: transparent;
  border: none;
  outline: none;
  background: transparent;
}

.emotion-0 .slick-prev:hover,
.emotion-0 .slick-next:hover,
.emotion-0 .slick-prev:focus,
.emotion-0 .slick-next:focus {
  color: transparent;
  outline: none;
  background: transparent;
  opacity: 1;
}

.emotion-0 .slick-prev.slick-disabled,
.emotion-0 .slick-next.slick-disabled {
  opacity: 0.25;
}

.emotion-0 .slick-prev {
  left: -25px;
  -webkit-transform: translate(0, -50%) rotate(90deg);
  -moz-transform: translate(0, -50%) rotate(90deg);
  -ms-transform: translate(0, -50%) rotate(90deg);
  transform: translate(0, -50%) rotate(90deg);
}

.emotion-0 .slick-prev span {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-0 [dir="rtl"] .slick-prev {
  right: -25px;
  left: auto;
}

.emotion-0 .slick-next {
  right: -25px;
  -webkit-transform: translate(0, -50%) rotate(-90deg);
  -moz-transform: translate(0, -50%) rotate(-90deg);
  -ms-transform: translate(0, -50%) rotate(-90deg);
  transform: translate(0, -50%) rotate(-90deg);
}

.emotion-0 .slick-next span {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-0 [dir="rtl"] .slick-next {
  right: auto;
  left: -25px;
}

.emotion-0 .slick-dotted.slick-slider {
  margin-bottom: 30px;
}

.emotion-0 .slick-dots {
  position: absolute;
  bottom: -25px;
  display: block;
  width: 100%;
  padding: 0;
  margin: 0;
  list-style: none;
  text-align: center;
}

.emotion-0 .slick-dots li {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: 20px;
  height: 20px;
  margin: 0 5px;
  padding: 0;
  cursor: pointer;
}

.emotion-0 .slick-dots li.slick-active button:before {
  opacity: 0.75;
  color: black;
}

.emotion-0 .slick-dots li button {
  font-size: 0;
  line-height: 0;
  display: block;
  padding: 5px;
  cursor: pointer;
  color: transparent;
  border: 0;
  outline: none;
  background: transparent;
}

.emotion-0 .slick-dots li button:hover,
.emotion-0 .slick-dots li button:focus {
  outline: none;
}

.emotion-0 .slick-dots li button:hover:before,
.emotion-0 .slick-dots li button:focus:before,
.emotion-0 .slick-dots li button:hover:before,
.emotion-0 .slick-dots li button:focus:before {
  opacity: 1;
}

.emotion-0 .slick-dots li button:before {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 5px;
  height: 5px;
  content: "";
  opacity: 0.25;
  background-color: black;
  border-radius: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

<div
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`CarouselContainer snapshots renders swatch size small state correctly BananaRepublic 1`] = `
<DocumentFragment>
  .emotion-0 {
  position: relative;
}

.emotion-0 .slick-slider {
  position: relative;
  display: block;
  box-sizing: border-box;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  -webkit-touch-callout: none;
  touch-action: pan-y;
  -webkit-tap-highlight-color: transparent;
  scroll-behavior: smooth;
}

.emotion-0 .slick-slider .slick-track,
.emotion-0 .slick-slider .slick-list {
  -webkit-transform: translate3d(0, 0, 0);
  -moz-transform: translate3d(0, 0, 0);
  -ms-transform: translate3d(0, 0, 0);
  transform: translate3d(0, 0, 0);
}

.emotion-0 .slick-list {
  position: relative;
  display: block;
  overflow-x: hidden;
  overflow-y: hidden;
  margin: 0;
  padding: 0;
}

.emotion-0 .slick-list:focus {
  outline: none;
}

.emotion-0 .slick-list.dragging {
  cursor: pointer;
  cursor: hand;
}

.emotion-0 .slick-track {
  position: relative;
  top: 0;
  left: 0;
  display: block;
  margin-left: auto;
  margin-right: auto;
}

.emotion-0 .slick-track:before,
.emotion-0 .slick-track:after {
  display: table;
  content: "";
}

.emotion-0 .slick-track:after {
  clear: both;
}

.emotion-0 .slick-slide {
  width: 36px!important;
  display: none;
  float: left;
  height: 100%;
  min-height: 1px;
}

.emotion-0 .slick-slide img {
  display: block;
}

.emotion-0 .slick-slide.slick-loading img {
  display: none;
}

.emotion-0 .slick-slide.dragging img {
  pointer-events: none;
}

.emotion-0 [dir="rtl"] .slick-slide {
  float: right;
}

.emotion-0 .slick-initialized .slick-slide,
.emotion-0 .slick-vertical .slick-slide {
  display: block;
}

.emotion-0 .slick-vertical .slick-slide {
  border: 1px solid transparent;
  height: auto;
}

.emotion-0 .slick-loading .slick-track,
.emotion-0 .slick-loading .slick-slide {
  visibility: hidden;
}

.emotion-0 .slick-loading .slick-list {
  background: #fff url("./ajax-loader.gif") center center no-repeat;
}

.emotion-0 .slick-arrow.slick-hidden {
  display: none;
}

.emotion-0 .slick-prev,
.emotion-0 .slick-next {
  font-size: 0;
  line-height: 0;
  position: absolute;
  top: 50%;
  display: block;
  padding: 0;
  cursor: pointer;
  color: transparent;
  border: none;
  outline: none;
  background: transparent;
}

.emotion-0 .slick-prev:hover,
.emotion-0 .slick-next:hover,
.emotion-0 .slick-prev:focus,
.emotion-0 .slick-next:focus {
  color: transparent;
  outline: none;
  background: transparent;
  opacity: 1;
}

.emotion-0 .slick-prev.slick-disabled,
.emotion-0 .slick-next.slick-disabled {
  opacity: 0.25;
}

.emotion-0 .slick-prev {
  left: -25px;
  -webkit-transform: translate(0, -50%) rotate(90deg);
  -moz-transform: translate(0, -50%) rotate(90deg);
  -ms-transform: translate(0, -50%) rotate(90deg);
  transform: translate(0, -50%) rotate(90deg);
}

.emotion-0 .slick-prev span {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-0 [dir="rtl"] .slick-prev {
  right: -25px;
  left: auto;
}

.emotion-0 .slick-next {
  right: -25px;
  -webkit-transform: translate(0, -50%) rotate(-90deg);
  -moz-transform: translate(0, -50%) rotate(-90deg);
  -ms-transform: translate(0, -50%) rotate(-90deg);
  transform: translate(0, -50%) rotate(-90deg);
}

.emotion-0 .slick-next span {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-0 [dir="rtl"] .slick-next {
  right: auto;
  left: -25px;
}

.emotion-0 .slick-dotted.slick-slider {
  margin-bottom: 30px;
}

.emotion-0 .slick-dots {
  position: absolute;
  bottom: -25px;
  display: block;
  width: 100%;
  padding: 0;
  margin: 0;
  list-style: none;
  text-align: center;
}

.emotion-0 .slick-dots li {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: 20px;
  height: 20px;
  margin: 0 5px;
  padding: 0;
  cursor: pointer;
}

.emotion-0 .slick-dots li.slick-active button:before {
  opacity: 0.75;
  color: black;
}

.emotion-0 .slick-dots li button {
  font-size: 0;
  line-height: 0;
  display: block;
  padding: 5px;
  cursor: pointer;
  color: transparent;
  border: 0;
  outline: none;
  background: transparent;
}

.emotion-0 .slick-dots li button:hover,
.emotion-0 .slick-dots li button:focus {
  outline: none;
}

.emotion-0 .slick-dots li button:hover:before,
.emotion-0 .slick-dots li button:focus:before,
.emotion-0 .slick-dots li button:hover:before,
.emotion-0 .slick-dots li button:focus:before {
  opacity: 1;
}

.emotion-0 .slick-dots li button:before {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 5px;
  height: 5px;
  content: "";
  opacity: 0.25;
  background-color: black;
  border-radius: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

<div
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`CarouselContainer snapshots renders swatch size small state correctly BananaRepublic in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  position: relative;
}

.emotion-0 .slick-slider {
  position: relative;
  display: block;
  box-sizing: border-box;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  -webkit-touch-callout: none;
  touch-action: pan-y;
  -webkit-tap-highlight-color: transparent;
  scroll-behavior: smooth;
}

.emotion-0 .slick-slider .slick-track,
.emotion-0 .slick-slider .slick-list {
  -webkit-transform: translate3d(0, 0, 0);
  -moz-transform: translate3d(0, 0, 0);
  -ms-transform: translate3d(0, 0, 0);
  transform: translate3d(0, 0, 0);
}

.emotion-0 .slick-list {
  position: relative;
  display: block;
  overflow-x: hidden;
  overflow-y: hidden;
  margin: 0;
  padding: 0;
}

.emotion-0 .slick-list:focus {
  outline: none;
}

.emotion-0 .slick-list.dragging {
  cursor: pointer;
  cursor: hand;
}

.emotion-0 .slick-track {
  position: relative;
  top: 0;
  left: 0;
  display: block;
  margin-left: auto;
  margin-right: auto;
}

.emotion-0 .slick-track:before,
.emotion-0 .slick-track:after {
  display: table;
  content: "";
}

.emotion-0 .slick-track:after {
  clear: both;
}

.emotion-0 .slick-slide {
  width: 36px!important;
  display: none;
  float: left;
  height: 100%;
  min-height: 1px;
}

.emotion-0 .slick-slide img {
  display: block;
}

.emotion-0 .slick-slide.slick-loading img {
  display: none;
}

.emotion-0 .slick-slide.dragging img {
  pointer-events: none;
}

.emotion-0 [dir="rtl"] .slick-slide {
  float: right;
}

.emotion-0 .slick-initialized .slick-slide,
.emotion-0 .slick-vertical .slick-slide {
  display: block;
}

.emotion-0 .slick-vertical .slick-slide {
  border: 1px solid transparent;
  height: auto;
}

.emotion-0 .slick-loading .slick-track,
.emotion-0 .slick-loading .slick-slide {
  visibility: hidden;
}

.emotion-0 .slick-loading .slick-list {
  background: #fff url("./ajax-loader.gif") center center no-repeat;
}

.emotion-0 .slick-arrow.slick-hidden {
  display: none;
}

.emotion-0 .slick-prev,
.emotion-0 .slick-next {
  font-size: 0;
  line-height: 0;
  position: absolute;
  top: 50%;
  display: block;
  padding: 0;
  cursor: pointer;
  color: transparent;
  border: none;
  outline: none;
  background: transparent;
}

.emotion-0 .slick-prev:hover,
.emotion-0 .slick-next:hover,
.emotion-0 .slick-prev:focus,
.emotion-0 .slick-next:focus {
  color: transparent;
  outline: none;
  background: transparent;
  opacity: 1;
}

.emotion-0 .slick-prev.slick-disabled,
.emotion-0 .slick-next.slick-disabled {
  opacity: 0.25;
}

.emotion-0 .slick-prev {
  left: -25px;
  -webkit-transform: translate(0, -50%) rotate(90deg);
  -moz-transform: translate(0, -50%) rotate(90deg);
  -ms-transform: translate(0, -50%) rotate(90deg);
  transform: translate(0, -50%) rotate(90deg);
}

.emotion-0 .slick-prev span {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-0 [dir="rtl"] .slick-prev {
  right: -25px;
  left: auto;
}

.emotion-0 .slick-next {
  right: -25px;
  -webkit-transform: translate(0, -50%) rotate(-90deg);
  -moz-transform: translate(0, -50%) rotate(-90deg);
  -ms-transform: translate(0, -50%) rotate(-90deg);
  transform: translate(0, -50%) rotate(-90deg);
}

.emotion-0 .slick-next span {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-0 [dir="rtl"] .slick-next {
  right: auto;
  left: -25px;
}

.emotion-0 .slick-dotted.slick-slider {
  margin-bottom: 30px;
}

.emotion-0 .slick-dots {
  position: absolute;
  bottom: -25px;
  display: block;
  width: 100%;
  padding: 0;
  margin: 0;
  list-style: none;
  text-align: center;
}

.emotion-0 .slick-dots li {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: 20px;
  height: 20px;
  margin: 0 5px;
  padding: 0;
  cursor: pointer;
}

.emotion-0 .slick-dots li.slick-active button:before {
  opacity: 0.75;
  color: black;
}

.emotion-0 .slick-dots li button {
  font-size: 0;
  line-height: 0;
  display: block;
  padding: 5px;
  cursor: pointer;
  color: transparent;
  border: 0;
  outline: none;
  background: transparent;
}

.emotion-0 .slick-dots li button:hover,
.emotion-0 .slick-dots li button:focus {
  outline: none;
}

.emotion-0 .slick-dots li button:hover:before,
.emotion-0 .slick-dots li button:focus:before,
.emotion-0 .slick-dots li button:hover:before,
.emotion-0 .slick-dots li button:focus:before {
  opacity: 1;
}

.emotion-0 .slick-dots li button:before {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 5px;
  height: 5px;
  content: "";
  opacity: 0.25;
  background-color: black;
  border-radius: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

<div
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`CarouselContainer snapshots renders swatch size small state correctly BananaRepublicFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  position: relative;
}

.emotion-0 .slick-slider {
  position: relative;
  display: block;
  box-sizing: border-box;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  -webkit-touch-callout: none;
  touch-action: pan-y;
  -webkit-tap-highlight-color: transparent;
  scroll-behavior: smooth;
}

.emotion-0 .slick-slider .slick-track,
.emotion-0 .slick-slider .slick-list {
  -webkit-transform: translate3d(0, 0, 0);
  -moz-transform: translate3d(0, 0, 0);
  -ms-transform: translate3d(0, 0, 0);
  transform: translate3d(0, 0, 0);
}

.emotion-0 .slick-list {
  position: relative;
  display: block;
  overflow-x: hidden;
  overflow-y: hidden;
  margin: 0;
  padding: 0;
}

.emotion-0 .slick-list:focus {
  outline: none;
}

.emotion-0 .slick-list.dragging {
  cursor: pointer;
  cursor: hand;
}

.emotion-0 .slick-track {
  position: relative;
  top: 0;
  left: 0;
  display: block;
  margin-left: auto;
  margin-right: auto;
}

.emotion-0 .slick-track:before,
.emotion-0 .slick-track:after {
  display: table;
  content: "";
}

.emotion-0 .slick-track:after {
  clear: both;
}

.emotion-0 .slick-slide {
  width: 36px!important;
  display: none;
  float: left;
  height: 100%;
  min-height: 1px;
}

.emotion-0 .slick-slide img {
  display: block;
}

.emotion-0 .slick-slide.slick-loading img {
  display: none;
}

.emotion-0 .slick-slide.dragging img {
  pointer-events: none;
}

.emotion-0 [dir="rtl"] .slick-slide {
  float: right;
}

.emotion-0 .slick-initialized .slick-slide,
.emotion-0 .slick-vertical .slick-slide {
  display: block;
}

.emotion-0 .slick-vertical .slick-slide {
  border: 1px solid transparent;
  height: auto;
}

.emotion-0 .slick-loading .slick-track,
.emotion-0 .slick-loading .slick-slide {
  visibility: hidden;
}

.emotion-0 .slick-loading .slick-list {
  background: #fff url("./ajax-loader.gif") center center no-repeat;
}

.emotion-0 .slick-arrow.slick-hidden {
  display: none;
}

.emotion-0 .slick-prev,
.emotion-0 .slick-next {
  font-size: 0;
  line-height: 0;
  position: absolute;
  top: 50%;
  display: block;
  padding: 0;
  cursor: pointer;
  color: transparent;
  border: none;
  outline: none;
  background: transparent;
}

.emotion-0 .slick-prev:hover,
.emotion-0 .slick-next:hover,
.emotion-0 .slick-prev:focus,
.emotion-0 .slick-next:focus {
  color: transparent;
  outline: none;
  background: transparent;
  opacity: 1;
}

.emotion-0 .slick-prev.slick-disabled,
.emotion-0 .slick-next.slick-disabled {
  opacity: 0.25;
}

.emotion-0 .slick-prev {
  left: -25px;
  -webkit-transform: translate(0, -50%) rotate(90deg);
  -moz-transform: translate(0, -50%) rotate(90deg);
  -ms-transform: translate(0, -50%) rotate(90deg);
  transform: translate(0, -50%) rotate(90deg);
}

.emotion-0 .slick-prev span {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-0 [dir="rtl"] .slick-prev {
  right: -25px;
  left: auto;
}

.emotion-0 .slick-next {
  right: -25px;
  -webkit-transform: translate(0, -50%) rotate(-90deg);
  -moz-transform: translate(0, -50%) rotate(-90deg);
  -ms-transform: translate(0, -50%) rotate(-90deg);
  transform: translate(0, -50%) rotate(-90deg);
}

.emotion-0 .slick-next span {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-0 [dir="rtl"] .slick-next {
  right: auto;
  left: -25px;
}

.emotion-0 .slick-dotted.slick-slider {
  margin-bottom: 30px;
}

.emotion-0 .slick-dots {
  position: absolute;
  bottom: -25px;
  display: block;
  width: 100%;
  padding: 0;
  margin: 0;
  list-style: none;
  text-align: center;
}

.emotion-0 .slick-dots li {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: 20px;
  height: 20px;
  margin: 0 5px;
  padding: 0;
  cursor: pointer;
}

.emotion-0 .slick-dots li.slick-active button:before {
  opacity: 0.75;
  color: black;
}

.emotion-0 .slick-dots li button {
  font-size: 0;
  line-height: 0;
  display: block;
  padding: 5px;
  cursor: pointer;
  color: transparent;
  border: 0;
  outline: none;
  background: transparent;
}

.emotion-0 .slick-dots li button:hover,
.emotion-0 .slick-dots li button:focus {
  outline: none;
}

.emotion-0 .slick-dots li button:hover:before,
.emotion-0 .slick-dots li button:focus:before,
.emotion-0 .slick-dots li button:hover:before,
.emotion-0 .slick-dots li button:focus:before {
  opacity: 1;
}

.emotion-0 .slick-dots li button:before {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 5px;
  height: 5px;
  content: "";
  opacity: 0.25;
  background-color: black;
  border-radius: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

<div
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`CarouselContainer snapshots renders swatch size small state correctly BananaRepublicFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  position: relative;
}

.emotion-0 .slick-slider {
  position: relative;
  display: block;
  box-sizing: border-box;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  -webkit-touch-callout: none;
  touch-action: pan-y;
  -webkit-tap-highlight-color: transparent;
  scroll-behavior: smooth;
}

.emotion-0 .slick-slider .slick-track,
.emotion-0 .slick-slider .slick-list {
  -webkit-transform: translate3d(0, 0, 0);
  -moz-transform: translate3d(0, 0, 0);
  -ms-transform: translate3d(0, 0, 0);
  transform: translate3d(0, 0, 0);
}

.emotion-0 .slick-list {
  position: relative;
  display: block;
  overflow-x: hidden;
  overflow-y: hidden;
  margin: 0;
  padding: 0;
}

.emotion-0 .slick-list:focus {
  outline: none;
}

.emotion-0 .slick-list.dragging {
  cursor: pointer;
  cursor: hand;
}

.emotion-0 .slick-track {
  position: relative;
  top: 0;
  left: 0;
  display: block;
  margin-left: auto;
  margin-right: auto;
}

.emotion-0 .slick-track:before,
.emotion-0 .slick-track:after {
  display: table;
  content: "";
}

.emotion-0 .slick-track:after {
  clear: both;
}

.emotion-0 .slick-slide {
  width: 36px!important;
  display: none;
  float: left;
  height: 100%;
  min-height: 1px;
}

.emotion-0 .slick-slide img {
  display: block;
}

.emotion-0 .slick-slide.slick-loading img {
  display: none;
}

.emotion-0 .slick-slide.dragging img {
  pointer-events: none;
}

.emotion-0 [dir="rtl"] .slick-slide {
  float: right;
}

.emotion-0 .slick-initialized .slick-slide,
.emotion-0 .slick-vertical .slick-slide {
  display: block;
}

.emotion-0 .slick-vertical .slick-slide {
  border: 1px solid transparent;
  height: auto;
}

.emotion-0 .slick-loading .slick-track,
.emotion-0 .slick-loading .slick-slide {
  visibility: hidden;
}

.emotion-0 .slick-loading .slick-list {
  background: #fff url("./ajax-loader.gif") center center no-repeat;
}

.emotion-0 .slick-arrow.slick-hidden {
  display: none;
}

.emotion-0 .slick-prev,
.emotion-0 .slick-next {
  font-size: 0;
  line-height: 0;
  position: absolute;
  top: 50%;
  display: block;
  padding: 0;
  cursor: pointer;
  color: transparent;
  border: none;
  outline: none;
  background: transparent;
}

.emotion-0 .slick-prev:hover,
.emotion-0 .slick-next:hover,
.emotion-0 .slick-prev:focus,
.emotion-0 .slick-next:focus {
  color: transparent;
  outline: none;
  background: transparent;
  opacity: 1;
}

.emotion-0 .slick-prev.slick-disabled,
.emotion-0 .slick-next.slick-disabled {
  opacity: 0.25;
}

.emotion-0 .slick-prev {
  left: -25px;
  -webkit-transform: translate(0, -50%) rotate(90deg);
  -moz-transform: translate(0, -50%) rotate(90deg);
  -ms-transform: translate(0, -50%) rotate(90deg);
  transform: translate(0, -50%) rotate(90deg);
}

.emotion-0 .slick-prev span {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-0 [dir="rtl"] .slick-prev {
  right: -25px;
  left: auto;
}

.emotion-0 .slick-next {
  right: -25px;
  -webkit-transform: translate(0, -50%) rotate(-90deg);
  -moz-transform: translate(0, -50%) rotate(-90deg);
  -ms-transform: translate(0, -50%) rotate(-90deg);
  transform: translate(0, -50%) rotate(-90deg);
}

.emotion-0 .slick-next span {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-0 [dir="rtl"] .slick-next {
  right: auto;
  left: -25px;
}

.emotion-0 .slick-dotted.slick-slider {
  margin-bottom: 30px;
}

.emotion-0 .slick-dots {
  position: absolute;
  bottom: -25px;
  display: block;
  width: 100%;
  padding: 0;
  margin: 0;
  list-style: none;
  text-align: center;
}

.emotion-0 .slick-dots li {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: 20px;
  height: 20px;
  margin: 0 5px;
  padding: 0;
  cursor: pointer;
}

.emotion-0 .slick-dots li.slick-active button:before {
  opacity: 0.75;
  color: black;
}

.emotion-0 .slick-dots li button {
  font-size: 0;
  line-height: 0;
  display: block;
  padding: 5px;
  cursor: pointer;
  color: transparent;
  border: 0;
  outline: none;
  background: transparent;
}

.emotion-0 .slick-dots li button:hover,
.emotion-0 .slick-dots li button:focus {
  outline: none;
}

.emotion-0 .slick-dots li button:hover:before,
.emotion-0 .slick-dots li button:focus:before,
.emotion-0 .slick-dots li button:hover:before,
.emotion-0 .slick-dots li button:focus:before {
  opacity: 1;
}

.emotion-0 .slick-dots li button:before {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 5px;
  height: 5px;
  content: "";
  opacity: 0.25;
  background-color: black;
  border-radius: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

<div
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`CarouselContainer snapshots renders swatch size small state correctly Gap 1`] = `
<DocumentFragment>
  .emotion-0 {
  position: relative;
}

.emotion-0 .slick-slider {
  position: relative;
  display: block;
  box-sizing: border-box;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  -webkit-touch-callout: none;
  touch-action: pan-y;
  -webkit-tap-highlight-color: transparent;
  scroll-behavior: smooth;
}

.emotion-0 .slick-slider .slick-track,
.emotion-0 .slick-slider .slick-list {
  -webkit-transform: translate3d(0, 0, 0);
  -moz-transform: translate3d(0, 0, 0);
  -ms-transform: translate3d(0, 0, 0);
  transform: translate3d(0, 0, 0);
}

.emotion-0 .slick-list {
  position: relative;
  display: block;
  overflow-x: hidden;
  overflow-y: hidden;
  margin: 0;
  padding: 0;
}

.emotion-0 .slick-list:focus {
  outline: none;
}

.emotion-0 .slick-list.dragging {
  cursor: pointer;
  cursor: hand;
}

.emotion-0 .slick-track {
  position: relative;
  top: 0;
  left: 0;
  display: block;
  margin-left: auto;
  margin-right: auto;
}

.emotion-0 .slick-track:before,
.emotion-0 .slick-track:after {
  display: table;
  content: "";
}

.emotion-0 .slick-track:after {
  clear: both;
}

.emotion-0 .slick-slide {
  width: 36px!important;
  display: none;
  float: left;
  height: 100%;
  min-height: 1px;
}

.emotion-0 .slick-slide img {
  display: block;
}

.emotion-0 .slick-slide.slick-loading img {
  display: none;
}

.emotion-0 .slick-slide.dragging img {
  pointer-events: none;
}

.emotion-0 [dir="rtl"] .slick-slide {
  float: right;
}

.emotion-0 .slick-initialized .slick-slide,
.emotion-0 .slick-vertical .slick-slide {
  display: block;
}

.emotion-0 .slick-vertical .slick-slide {
  border: 1px solid transparent;
  height: auto;
}

.emotion-0 .slick-loading .slick-track,
.emotion-0 .slick-loading .slick-slide {
  visibility: hidden;
}

.emotion-0 .slick-loading .slick-list {
  background: #fff url("./ajax-loader.gif") center center no-repeat;
}

.emotion-0 .slick-arrow.slick-hidden {
  display: none;
}

.emotion-0 .slick-prev,
.emotion-0 .slick-next {
  font-size: 0;
  line-height: 0;
  position: absolute;
  top: 50%;
  display: block;
  padding: 0;
  cursor: pointer;
  color: transparent;
  border: none;
  outline: none;
  background: transparent;
}

.emotion-0 .slick-prev:hover,
.emotion-0 .slick-next:hover,
.emotion-0 .slick-prev:focus,
.emotion-0 .slick-next:focus {
  color: transparent;
  outline: none;
  background: transparent;
  opacity: 1;
}

.emotion-0 .slick-prev.slick-disabled,
.emotion-0 .slick-next.slick-disabled {
  opacity: 0.25;
}

.emotion-0 .slick-prev {
  left: -25px;
  -webkit-transform: translate(0, -50%) rotate(90deg);
  -moz-transform: translate(0, -50%) rotate(90deg);
  -ms-transform: translate(0, -50%) rotate(90deg);
  transform: translate(0, -50%) rotate(90deg);
}

.emotion-0 .slick-prev span {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-0 [dir="rtl"] .slick-prev {
  right: -25px;
  left: auto;
}

.emotion-0 .slick-next {
  right: -25px;
  -webkit-transform: translate(0, -50%) rotate(-90deg);
  -moz-transform: translate(0, -50%) rotate(-90deg);
  -ms-transform: translate(0, -50%) rotate(-90deg);
  transform: translate(0, -50%) rotate(-90deg);
}

.emotion-0 .slick-next span {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-0 [dir="rtl"] .slick-next {
  right: auto;
  left: -25px;
}

.emotion-0 .slick-dotted.slick-slider {
  margin-bottom: 30px;
}

.emotion-0 .slick-dots {
  position: absolute;
  bottom: -25px;
  display: block;
  width: 100%;
  padding: 0;
  margin: 0;
  list-style: none;
  text-align: center;
}

.emotion-0 .slick-dots li {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: 20px;
  height: 20px;
  margin: 0 5px;
  padding: 0;
  cursor: pointer;
}

.emotion-0 .slick-dots li.slick-active button:before {
  opacity: 0.75;
  color: black;
}

.emotion-0 .slick-dots li button {
  font-size: 0;
  line-height: 0;
  display: block;
  padding: 5px;
  cursor: pointer;
  color: transparent;
  border: 0;
  outline: none;
  background: transparent;
}

.emotion-0 .slick-dots li button:hover,
.emotion-0 .slick-dots li button:focus {
  outline: none;
}

.emotion-0 .slick-dots li button:hover:before,
.emotion-0 .slick-dots li button:focus:before,
.emotion-0 .slick-dots li button:hover:before,
.emotion-0 .slick-dots li button:focus:before {
  opacity: 1;
}

.emotion-0 .slick-dots li button:before {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 5px;
  height: 5px;
  content: "";
  opacity: 0.25;
  background-color: black;
  border-radius: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

<div
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`CarouselContainer snapshots renders swatch size small state correctly Gap in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  position: relative;
}

.emotion-0 .slick-slider {
  position: relative;
  display: block;
  box-sizing: border-box;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  -webkit-touch-callout: none;
  touch-action: pan-y;
  -webkit-tap-highlight-color: transparent;
  scroll-behavior: smooth;
}

.emotion-0 .slick-slider .slick-track,
.emotion-0 .slick-slider .slick-list {
  -webkit-transform: translate3d(0, 0, 0);
  -moz-transform: translate3d(0, 0, 0);
  -ms-transform: translate3d(0, 0, 0);
  transform: translate3d(0, 0, 0);
}

.emotion-0 .slick-list {
  position: relative;
  display: block;
  overflow-x: hidden;
  overflow-y: hidden;
  margin: 0;
  padding: 0;
}

.emotion-0 .slick-list:focus {
  outline: none;
}

.emotion-0 .slick-list.dragging {
  cursor: pointer;
  cursor: hand;
}

.emotion-0 .slick-track {
  position: relative;
  top: 0;
  left: 0;
  display: block;
  margin-left: auto;
  margin-right: auto;
}

.emotion-0 .slick-track:before,
.emotion-0 .slick-track:after {
  display: table;
  content: "";
}

.emotion-0 .slick-track:after {
  clear: both;
}

.emotion-0 .slick-slide {
  width: 36px!important;
  display: none;
  float: left;
  height: 100%;
  min-height: 1px;
}

.emotion-0 .slick-slide img {
  display: block;
}

.emotion-0 .slick-slide.slick-loading img {
  display: none;
}

.emotion-0 .slick-slide.dragging img {
  pointer-events: none;
}

.emotion-0 [dir="rtl"] .slick-slide {
  float: right;
}

.emotion-0 .slick-initialized .slick-slide,
.emotion-0 .slick-vertical .slick-slide {
  display: block;
}

.emotion-0 .slick-vertical .slick-slide {
  border: 1px solid transparent;
  height: auto;
}

.emotion-0 .slick-loading .slick-track,
.emotion-0 .slick-loading .slick-slide {
  visibility: hidden;
}

.emotion-0 .slick-loading .slick-list {
  background: #fff url("./ajax-loader.gif") center center no-repeat;
}

.emotion-0 .slick-arrow.slick-hidden {
  display: none;
}

.emotion-0 .slick-prev,
.emotion-0 .slick-next {
  font-size: 0;
  line-height: 0;
  position: absolute;
  top: 50%;
  display: block;
  padding: 0;
  cursor: pointer;
  color: transparent;
  border: none;
  outline: none;
  background: transparent;
}

.emotion-0 .slick-prev:hover,
.emotion-0 .slick-next:hover,
.emotion-0 .slick-prev:focus,
.emotion-0 .slick-next:focus {
  color: transparent;
  outline: none;
  background: transparent;
  opacity: 1;
}

.emotion-0 .slick-prev.slick-disabled,
.emotion-0 .slick-next.slick-disabled {
  opacity: 0.25;
}

.emotion-0 .slick-prev {
  left: -25px;
  -webkit-transform: translate(0, -50%) rotate(90deg);
  -moz-transform: translate(0, -50%) rotate(90deg);
  -ms-transform: translate(0, -50%) rotate(90deg);
  transform: translate(0, -50%) rotate(90deg);
}

.emotion-0 .slick-prev span {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-0 [dir="rtl"] .slick-prev {
  right: -25px;
  left: auto;
}

.emotion-0 .slick-next {
  right: -25px;
  -webkit-transform: translate(0, -50%) rotate(-90deg);
  -moz-transform: translate(0, -50%) rotate(-90deg);
  -ms-transform: translate(0, -50%) rotate(-90deg);
  transform: translate(0, -50%) rotate(-90deg);
}

.emotion-0 .slick-next span {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-0 [dir="rtl"] .slick-next {
  right: auto;
  left: -25px;
}

.emotion-0 .slick-dotted.slick-slider {
  margin-bottom: 30px;
}

.emotion-0 .slick-dots {
  position: absolute;
  bottom: -25px;
  display: block;
  width: 100%;
  padding: 0;
  margin: 0;
  list-style: none;
  text-align: center;
}

.emotion-0 .slick-dots li {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: 20px;
  height: 20px;
  margin: 0 5px;
  padding: 0;
  cursor: pointer;
}

.emotion-0 .slick-dots li.slick-active button:before {
  opacity: 0.75;
  color: black;
}

.emotion-0 .slick-dots li button {
  font-size: 0;
  line-height: 0;
  display: block;
  padding: 5px;
  cursor: pointer;
  color: transparent;
  border: 0;
  outline: none;
  background: transparent;
}

.emotion-0 .slick-dots li button:hover,
.emotion-0 .slick-dots li button:focus {
  outline: none;
}

.emotion-0 .slick-dots li button:hover:before,
.emotion-0 .slick-dots li button:focus:before,
.emotion-0 .slick-dots li button:hover:before,
.emotion-0 .slick-dots li button:focus:before {
  opacity: 1;
}

.emotion-0 .slick-dots li button:before {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 5px;
  height: 5px;
  content: "";
  opacity: 0.25;
  background-color: black;
  border-radius: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

<div
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`CarouselContainer snapshots renders swatch size small state correctly GapFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  position: relative;
}

.emotion-0 .slick-slider {
  position: relative;
  display: block;
  box-sizing: border-box;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  -webkit-touch-callout: none;
  touch-action: pan-y;
  -webkit-tap-highlight-color: transparent;
  scroll-behavior: smooth;
}

.emotion-0 .slick-slider .slick-track,
.emotion-0 .slick-slider .slick-list {
  -webkit-transform: translate3d(0, 0, 0);
  -moz-transform: translate3d(0, 0, 0);
  -ms-transform: translate3d(0, 0, 0);
  transform: translate3d(0, 0, 0);
}

.emotion-0 .slick-list {
  position: relative;
  display: block;
  overflow-x: hidden;
  overflow-y: hidden;
  margin: 0;
  padding: 0;
}

.emotion-0 .slick-list:focus {
  outline: none;
}

.emotion-0 .slick-list.dragging {
  cursor: pointer;
  cursor: hand;
}

.emotion-0 .slick-track {
  position: relative;
  top: 0;
  left: 0;
  display: block;
  margin-left: auto;
  margin-right: auto;
}

.emotion-0 .slick-track:before,
.emotion-0 .slick-track:after {
  display: table;
  content: "";
}

.emotion-0 .slick-track:after {
  clear: both;
}

.emotion-0 .slick-slide {
  width: 36px!important;
  display: none;
  float: left;
  height: 100%;
  min-height: 1px;
}

.emotion-0 .slick-slide img {
  display: block;
}

.emotion-0 .slick-slide.slick-loading img {
  display: none;
}

.emotion-0 .slick-slide.dragging img {
  pointer-events: none;
}

.emotion-0 [dir="rtl"] .slick-slide {
  float: right;
}

.emotion-0 .slick-initialized .slick-slide,
.emotion-0 .slick-vertical .slick-slide {
  display: block;
}

.emotion-0 .slick-vertical .slick-slide {
  border: 1px solid transparent;
  height: auto;
}

.emotion-0 .slick-loading .slick-track,
.emotion-0 .slick-loading .slick-slide {
  visibility: hidden;
}

.emotion-0 .slick-loading .slick-list {
  background: #fff url("./ajax-loader.gif") center center no-repeat;
}

.emotion-0 .slick-arrow.slick-hidden {
  display: none;
}

.emotion-0 .slick-prev,
.emotion-0 .slick-next {
  font-size: 0;
  line-height: 0;
  position: absolute;
  top: 50%;
  display: block;
  padding: 0;
  cursor: pointer;
  color: transparent;
  border: none;
  outline: none;
  background: transparent;
}

.emotion-0 .slick-prev:hover,
.emotion-0 .slick-next:hover,
.emotion-0 .slick-prev:focus,
.emotion-0 .slick-next:focus {
  color: transparent;
  outline: none;
  background: transparent;
  opacity: 1;
}

.emotion-0 .slick-prev.slick-disabled,
.emotion-0 .slick-next.slick-disabled {
  opacity: 0.25;
}

.emotion-0 .slick-prev {
  left: -25px;
  -webkit-transform: translate(0, -50%) rotate(90deg);
  -moz-transform: translate(0, -50%) rotate(90deg);
  -ms-transform: translate(0, -50%) rotate(90deg);
  transform: translate(0, -50%) rotate(90deg);
}

.emotion-0 .slick-prev span {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-0 [dir="rtl"] .slick-prev {
  right: -25px;
  left: auto;
}

.emotion-0 .slick-next {
  right: -25px;
  -webkit-transform: translate(0, -50%) rotate(-90deg);
  -moz-transform: translate(0, -50%) rotate(-90deg);
  -ms-transform: translate(0, -50%) rotate(-90deg);
  transform: translate(0, -50%) rotate(-90deg);
}

.emotion-0 .slick-next span {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-0 [dir="rtl"] .slick-next {
  right: auto;
  left: -25px;
}

.emotion-0 .slick-dotted.slick-slider {
  margin-bottom: 30px;
}

.emotion-0 .slick-dots {
  position: absolute;
  bottom: -25px;
  display: block;
  width: 100%;
  padding: 0;
  margin: 0;
  list-style: none;
  text-align: center;
}

.emotion-0 .slick-dots li {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: 20px;
  height: 20px;
  margin: 0 5px;
  padding: 0;
  cursor: pointer;
}

.emotion-0 .slick-dots li.slick-active button:before {
  opacity: 0.75;
  color: black;
}

.emotion-0 .slick-dots li button {
  font-size: 0;
  line-height: 0;
  display: block;
  padding: 5px;
  cursor: pointer;
  color: transparent;
  border: 0;
  outline: none;
  background: transparent;
}

.emotion-0 .slick-dots li button:hover,
.emotion-0 .slick-dots li button:focus {
  outline: none;
}

.emotion-0 .slick-dots li button:hover:before,
.emotion-0 .slick-dots li button:focus:before,
.emotion-0 .slick-dots li button:hover:before,
.emotion-0 .slick-dots li button:focus:before {
  opacity: 1;
}

.emotion-0 .slick-dots li button:before {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 5px;
  height: 5px;
  content: "";
  opacity: 0.25;
  background-color: black;
  border-radius: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

<div
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`CarouselContainer snapshots renders swatch size small state correctly GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  position: relative;
}

.emotion-0 .slick-slider {
  position: relative;
  display: block;
  box-sizing: border-box;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  -webkit-touch-callout: none;
  touch-action: pan-y;
  -webkit-tap-highlight-color: transparent;
  scroll-behavior: smooth;
}

.emotion-0 .slick-slider .slick-track,
.emotion-0 .slick-slider .slick-list {
  -webkit-transform: translate3d(0, 0, 0);
  -moz-transform: translate3d(0, 0, 0);
  -ms-transform: translate3d(0, 0, 0);
  transform: translate3d(0, 0, 0);
}

.emotion-0 .slick-list {
  position: relative;
  display: block;
  overflow-x: hidden;
  overflow-y: hidden;
  margin: 0;
  padding: 0;
}

.emotion-0 .slick-list:focus {
  outline: none;
}

.emotion-0 .slick-list.dragging {
  cursor: pointer;
  cursor: hand;
}

.emotion-0 .slick-track {
  position: relative;
  top: 0;
  left: 0;
  display: block;
  margin-left: auto;
  margin-right: auto;
}

.emotion-0 .slick-track:before,
.emotion-0 .slick-track:after {
  display: table;
  content: "";
}

.emotion-0 .slick-track:after {
  clear: both;
}

.emotion-0 .slick-slide {
  width: 36px!important;
  display: none;
  float: left;
  height: 100%;
  min-height: 1px;
}

.emotion-0 .slick-slide img {
  display: block;
}

.emotion-0 .slick-slide.slick-loading img {
  display: none;
}

.emotion-0 .slick-slide.dragging img {
  pointer-events: none;
}

.emotion-0 [dir="rtl"] .slick-slide {
  float: right;
}

.emotion-0 .slick-initialized .slick-slide,
.emotion-0 .slick-vertical .slick-slide {
  display: block;
}

.emotion-0 .slick-vertical .slick-slide {
  border: 1px solid transparent;
  height: auto;
}

.emotion-0 .slick-loading .slick-track,
.emotion-0 .slick-loading .slick-slide {
  visibility: hidden;
}

.emotion-0 .slick-loading .slick-list {
  background: #fff url("./ajax-loader.gif") center center no-repeat;
}

.emotion-0 .slick-arrow.slick-hidden {
  display: none;
}

.emotion-0 .slick-prev,
.emotion-0 .slick-next {
  font-size: 0;
  line-height: 0;
  position: absolute;
  top: 50%;
  display: block;
  padding: 0;
  cursor: pointer;
  color: transparent;
  border: none;
  outline: none;
  background: transparent;
}

.emotion-0 .slick-prev:hover,
.emotion-0 .slick-next:hover,
.emotion-0 .slick-prev:focus,
.emotion-0 .slick-next:focus {
  color: transparent;
  outline: none;
  background: transparent;
  opacity: 1;
}

.emotion-0 .slick-prev.slick-disabled,
.emotion-0 .slick-next.slick-disabled {
  opacity: 0.25;
}

.emotion-0 .slick-prev {
  left: -25px;
  -webkit-transform: translate(0, -50%) rotate(90deg);
  -moz-transform: translate(0, -50%) rotate(90deg);
  -ms-transform: translate(0, -50%) rotate(90deg);
  transform: translate(0, -50%) rotate(90deg);
}

.emotion-0 .slick-prev span {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-0 [dir="rtl"] .slick-prev {
  right: -25px;
  left: auto;
}

.emotion-0 .slick-next {
  right: -25px;
  -webkit-transform: translate(0, -50%) rotate(-90deg);
  -moz-transform: translate(0, -50%) rotate(-90deg);
  -ms-transform: translate(0, -50%) rotate(-90deg);
  transform: translate(0, -50%) rotate(-90deg);
}

.emotion-0 .slick-next span {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-0 [dir="rtl"] .slick-next {
  right: auto;
  left: -25px;
}

.emotion-0 .slick-dotted.slick-slider {
  margin-bottom: 30px;
}

.emotion-0 .slick-dots {
  position: absolute;
  bottom: -25px;
  display: block;
  width: 100%;
  padding: 0;
  margin: 0;
  list-style: none;
  text-align: center;
}

.emotion-0 .slick-dots li {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: 20px;
  height: 20px;
  margin: 0 5px;
  padding: 0;
  cursor: pointer;
}

.emotion-0 .slick-dots li.slick-active button:before {
  opacity: 0.75;
  color: black;
}

.emotion-0 .slick-dots li button {
  font-size: 0;
  line-height: 0;
  display: block;
  padding: 5px;
  cursor: pointer;
  color: transparent;
  border: 0;
  outline: none;
  background: transparent;
}

.emotion-0 .slick-dots li button:hover,
.emotion-0 .slick-dots li button:focus {
  outline: none;
}

.emotion-0 .slick-dots li button:hover:before,
.emotion-0 .slick-dots li button:focus:before,
.emotion-0 .slick-dots li button:hover:before,
.emotion-0 .slick-dots li button:focus:before {
  opacity: 1;
}

.emotion-0 .slick-dots li button:before {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 5px;
  height: 5px;
  content: "";
  opacity: 0.25;
  background-color: black;
  border-radius: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

<div
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`CarouselContainer snapshots renders swatch size small state correctly OldNavy 1`] = `
<DocumentFragment>
  .emotion-0 {
  position: relative;
}

.emotion-0 .slick-slider {
  position: relative;
  display: block;
  box-sizing: border-box;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  -webkit-touch-callout: none;
  touch-action: pan-y;
  -webkit-tap-highlight-color: transparent;
  scroll-behavior: smooth;
}

.emotion-0 .slick-slider .slick-track,
.emotion-0 .slick-slider .slick-list {
  -webkit-transform: translate3d(0, 0, 0);
  -moz-transform: translate3d(0, 0, 0);
  -ms-transform: translate3d(0, 0, 0);
  transform: translate3d(0, 0, 0);
}

.emotion-0 .slick-list {
  position: relative;
  display: block;
  overflow-x: hidden;
  overflow-y: hidden;
  margin: 0;
  padding: 0;
}

.emotion-0 .slick-list:focus {
  outline: none;
}

.emotion-0 .slick-list.dragging {
  cursor: pointer;
  cursor: hand;
}

.emotion-0 .slick-track {
  position: relative;
  top: 0;
  left: 0;
  display: block;
  margin-left: auto;
  margin-right: auto;
}

.emotion-0 .slick-track:before,
.emotion-0 .slick-track:after {
  display: table;
  content: "";
}

.emotion-0 .slick-track:after {
  clear: both;
}

.emotion-0 .slick-slide {
  width: 36px!important;
  display: none;
  float: left;
  height: 100%;
  min-height: 1px;
}

.emotion-0 .slick-slide img {
  display: block;
}

.emotion-0 .slick-slide.slick-loading img {
  display: none;
}

.emotion-0 .slick-slide.dragging img {
  pointer-events: none;
}

.emotion-0 [dir="rtl"] .slick-slide {
  float: right;
}

.emotion-0 .slick-initialized .slick-slide,
.emotion-0 .slick-vertical .slick-slide {
  display: block;
}

.emotion-0 .slick-vertical .slick-slide {
  border: 1px solid transparent;
  height: auto;
}

.emotion-0 .slick-loading .slick-track,
.emotion-0 .slick-loading .slick-slide {
  visibility: hidden;
}

.emotion-0 .slick-loading .slick-list {
  background: #fff url("./ajax-loader.gif") center center no-repeat;
}

.emotion-0 .slick-arrow.slick-hidden {
  display: none;
}

.emotion-0 .slick-prev,
.emotion-0 .slick-next {
  font-size: 0;
  line-height: 0;
  position: absolute;
  top: 50%;
  display: block;
  padding: 0;
  cursor: pointer;
  color: transparent;
  border: none;
  outline: none;
  background: transparent;
}

.emotion-0 .slick-prev:hover,
.emotion-0 .slick-next:hover,
.emotion-0 .slick-prev:focus,
.emotion-0 .slick-next:focus {
  color: transparent;
  outline: none;
  background: transparent;
  opacity: 1;
}

.emotion-0 .slick-prev.slick-disabled,
.emotion-0 .slick-next.slick-disabled {
  opacity: 0.25;
}

.emotion-0 .slick-prev {
  left: -25px;
  -webkit-transform: translate(0, -50%) rotate(90deg);
  -moz-transform: translate(0, -50%) rotate(90deg);
  -ms-transform: translate(0, -50%) rotate(90deg);
  transform: translate(0, -50%) rotate(90deg);
}

.emotion-0 .slick-prev span {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-0 [dir="rtl"] .slick-prev {
  right: -25px;
  left: auto;
}

.emotion-0 .slick-next {
  right: -25px;
  -webkit-transform: translate(0, -50%) rotate(-90deg);
  -moz-transform: translate(0, -50%) rotate(-90deg);
  -ms-transform: translate(0, -50%) rotate(-90deg);
  transform: translate(0, -50%) rotate(-90deg);
}

.emotion-0 .slick-next span {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-0 [dir="rtl"] .slick-next {
  right: auto;
  left: -25px;
}

.emotion-0 .slick-dotted.slick-slider {
  margin-bottom: 30px;
}

.emotion-0 .slick-dots {
  position: absolute;
  bottom: -25px;
  display: block;
  width: 100%;
  padding: 0;
  margin: 0;
  list-style: none;
  text-align: center;
}

.emotion-0 .slick-dots li {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: 20px;
  height: 20px;
  margin: 0 5px;
  padding: 0;
  cursor: pointer;
}

.emotion-0 .slick-dots li.slick-active button:before {
  opacity: 0.75;
  color: black;
}

.emotion-0 .slick-dots li button {
  font-size: 0;
  line-height: 0;
  display: block;
  padding: 5px;
  cursor: pointer;
  color: transparent;
  border: 0;
  outline: none;
  background: transparent;
}

.emotion-0 .slick-dots li button:hover,
.emotion-0 .slick-dots li button:focus {
  outline: none;
}

.emotion-0 .slick-dots li button:hover:before,
.emotion-0 .slick-dots li button:focus:before,
.emotion-0 .slick-dots li button:hover:before,
.emotion-0 .slick-dots li button:focus:before {
  opacity: 1;
}

.emotion-0 .slick-dots li button:before {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 5px;
  height: 5px;
  content: "";
  opacity: 0.25;
  background-color: black;
  border-radius: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

<div
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`CarouselContainer snapshots renders swatch size small state correctly OldNavy in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  position: relative;
}

.emotion-0 .slick-slider {
  position: relative;
  display: block;
  box-sizing: border-box;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  -webkit-touch-callout: none;
  touch-action: pan-y;
  -webkit-tap-highlight-color: transparent;
  scroll-behavior: smooth;
}

.emotion-0 .slick-slider .slick-track,
.emotion-0 .slick-slider .slick-list {
  -webkit-transform: translate3d(0, 0, 0);
  -moz-transform: translate3d(0, 0, 0);
  -ms-transform: translate3d(0, 0, 0);
  transform: translate3d(0, 0, 0);
}

.emotion-0 .slick-list {
  position: relative;
  display: block;
  overflow-x: hidden;
  overflow-y: hidden;
  margin: 0;
  padding: 0;
}

.emotion-0 .slick-list:focus {
  outline: none;
}

.emotion-0 .slick-list.dragging {
  cursor: pointer;
  cursor: hand;
}

.emotion-0 .slick-track {
  position: relative;
  top: 0;
  left: 0;
  display: block;
  margin-left: auto;
  margin-right: auto;
}

.emotion-0 .slick-track:before,
.emotion-0 .slick-track:after {
  display: table;
  content: "";
}

.emotion-0 .slick-track:after {
  clear: both;
}

.emotion-0 .slick-slide {
  width: 36px!important;
  display: none;
  float: left;
  height: 100%;
  min-height: 1px;
}

.emotion-0 .slick-slide img {
  display: block;
}

.emotion-0 .slick-slide.slick-loading img {
  display: none;
}

.emotion-0 .slick-slide.dragging img {
  pointer-events: none;
}

.emotion-0 [dir="rtl"] .slick-slide {
  float: right;
}

.emotion-0 .slick-initialized .slick-slide,
.emotion-0 .slick-vertical .slick-slide {
  display: block;
}

.emotion-0 .slick-vertical .slick-slide {
  border: 1px solid transparent;
  height: auto;
}

.emotion-0 .slick-loading .slick-track,
.emotion-0 .slick-loading .slick-slide {
  visibility: hidden;
}

.emotion-0 .slick-loading .slick-list {
  background: #fff url("./ajax-loader.gif") center center no-repeat;
}

.emotion-0 .slick-arrow.slick-hidden {
  display: none;
}

.emotion-0 .slick-prev,
.emotion-0 .slick-next {
  font-size: 0;
  line-height: 0;
  position: absolute;
  top: 50%;
  display: block;
  padding: 0;
  cursor: pointer;
  color: transparent;
  border: none;
  outline: none;
  background: transparent;
}

.emotion-0 .slick-prev:hover,
.emotion-0 .slick-next:hover,
.emotion-0 .slick-prev:focus,
.emotion-0 .slick-next:focus {
  color: transparent;
  outline: none;
  background: transparent;
  opacity: 1;
}

.emotion-0 .slick-prev.slick-disabled,
.emotion-0 .slick-next.slick-disabled {
  opacity: 0.25;
}

.emotion-0 .slick-prev {
  left: -25px;
  -webkit-transform: translate(0, -50%) rotate(90deg);
  -moz-transform: translate(0, -50%) rotate(90deg);
  -ms-transform: translate(0, -50%) rotate(90deg);
  transform: translate(0, -50%) rotate(90deg);
}

.emotion-0 .slick-prev span {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-0 [dir="rtl"] .slick-prev {
  right: -25px;
  left: auto;
}

.emotion-0 .slick-next {
  right: -25px;
  -webkit-transform: translate(0, -50%) rotate(-90deg);
  -moz-transform: translate(0, -50%) rotate(-90deg);
  -ms-transform: translate(0, -50%) rotate(-90deg);
  transform: translate(0, -50%) rotate(-90deg);
}

.emotion-0 .slick-next span {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-0 [dir="rtl"] .slick-next {
  right: auto;
  left: -25px;
}

.emotion-0 .slick-dotted.slick-slider {
  margin-bottom: 30px;
}

.emotion-0 .slick-dots {
  position: absolute;
  bottom: -25px;
  display: block;
  width: 100%;
  padding: 0;
  margin: 0;
  list-style: none;
  text-align: center;
}

.emotion-0 .slick-dots li {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: 20px;
  height: 20px;
  margin: 0 5px;
  padding: 0;
  cursor: pointer;
}

.emotion-0 .slick-dots li.slick-active button:before {
  opacity: 0.75;
  color: black;
}

.emotion-0 .slick-dots li button {
  font-size: 0;
  line-height: 0;
  display: block;
  padding: 5px;
  cursor: pointer;
  color: transparent;
  border: 0;
  outline: none;
  background: transparent;
}

.emotion-0 .slick-dots li button:hover,
.emotion-0 .slick-dots li button:focus {
  outline: none;
}

.emotion-0 .slick-dots li button:hover:before,
.emotion-0 .slick-dots li button:focus:before,
.emotion-0 .slick-dots li button:hover:before,
.emotion-0 .slick-dots li button:focus:before {
  opacity: 1;
}

.emotion-0 .slick-dots li button:before {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 5px;
  height: 5px;
  content: "";
  opacity: 0.25;
  background-color: black;
  border-radius: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

<div
    class="emotion-0"
  />
</DocumentFragment>
`;
