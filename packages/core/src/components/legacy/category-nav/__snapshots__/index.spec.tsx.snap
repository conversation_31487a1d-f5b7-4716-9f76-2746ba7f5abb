// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`<CategoryNav /> Snapshots Branded renders default state correctly Athleta 1`] = `
<DocumentFragment>
  .emotion-0 {
  padding: 0 0.5rem;
  max-width: 13rem;
  padding-left: 2rem;
  display: block;
  float: left;
  padding-bottom: 1rem;
  margin-right: 0.5rem;
}

.emotion-1 {
  position: absolute;
  height: 1px;
  width: 1px;
  overflow: hidden;
  clip: rect(0 0 0 0);
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  background-color: rgba(255, 255, 255, 0.75);
  top: 0;
  color: #FFF;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  text-align: center;
}

.emotion-1:focus {
  position: relative;
  height: auto;
  width: auto;
  overflow: visible;
  clip: initial;
  z-index: 20;
  left: 0;
}

.emotion-2 {
  width: auto;
  border-radius: 0;
  display: block;
  border: none;
  background-color: #000;
  padding: 1.5rem;
  text-align: center;
  text-transform: uppercase;
}

.emotion-10 {
  font-family: 'Avenir Next','Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  color: #333;
  font-variant-ligatures: none;
  font-size: 1rem;
  letter-spacing: 0.125rem;
  text-transform: uppercase;
  padding-top: 0.25rem;
  padding-bottom: 0.25rem;
}

@media (min-width: 47.375rem) {
  .emotion-10 {
    font-size: 1.125rem;
  }
}

.emotion-11 {
  font-family: 'Avenir Next','Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  display: block;
  color: #333;
  font-variant-ligatures: none;
  font-size: 0.875rem;
  line-height: 1.4;
  text-transform: capitalize;
  padding-top: 0.25rem;
  padding-bottom: 0.25rem;
}

.emotion-15 {
  padding-top: 0;
  margin-top: 1.25rem;
}

.emotion-18 {
  color: inherit;
}

.emotion-18:hover {
  color: inherit;
}

.emotion-20 {
  font-family: 'Avenir Next','Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 600;
  position: relative;
  color: #333;
}

.emotion-20::before {
  content: "";
  position: absolute;
  top: 0;
  left: -1.675rem;
  height: 100%;
  width: 0.25rem;
  background-color: #333;
}

<ul
    class="emotion-0"
    role="navigation"
  >
    <a
      class="emotion-1"
      href="#quick-filters"
    >
      <span
        class="emotion-2"
      >
        left-nav.skip-to-quick-filters
      </span>
    </a>
    <a
      class="emotion-1"
      href="#facets"
    >
      <span
        class="emotion-2"
      >
        left-nav.skip-to-product-filters
      </span>
    </a>
    <a
      class="emotion-1"
      href="/"
    >
      <span
        class="emotion-2"
      >
        left-nav.skip-to New & Now
      </span>
    </a>
    <a
      class="emotion-1"
      href="/"
    >
      <span
        class="emotion-2"
      >
        left-nav.skip-to Shop By Category
      </span>
    </a>
    <div
      class="emotion-9"
    >
      <h2
        class="emotion-10"
      >
        New & Now
      </h2>
      <ul>
        <li
          class="emotion-11"
        >
          <a
            aria-label="New Arrivals"
            class="emotion-12"
            href="/"
          >
            New Arrivals
          </a>
        </li>
        <li
          class="emotion-11"
        >
          <a
            aria-label="On Trend"
            class="emotion-12"
            href="/"
          >
            On Trend
          </a>
        </li>
      </ul>
    </div>
    <div
      class="emotion-15"
    >
      <h2
        class="emotion-10"
      >
        Shop By Category
      </h2>
      <ul>
        <li
          class="emotion-11"
        >
          <a
            aria-label="The Tee Shop"
            class="emotion-18"
            href="/"
          >
            The Tee Shop
          </a>
        </li>
        <li
          class="emotion-11"
        >
          <a
            aria-label="Sleep & Lounge"
            class="emotion-20"
            href="/"
          >
            Sleep & Lounge
          </a>
        </li>
      </ul>
    </div>
  </ul>
</DocumentFragment>
`;

exports[`<CategoryNav /> Snapshots Branded renders default state correctly Athleta in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  padding: 0 0.5rem;
  max-width: 13rem;
  padding-left: 2rem;
  display: block;
  float: left;
  padding-bottom: 1rem;
  margin-right: 0.5rem;
}

.emotion-1 {
  position: absolute;
  height: 1px;
  width: 1px;
  overflow: hidden;
  clip: rect(0 0 0 0);
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  background-color: rgba(255, 255, 255, 0.75);
  top: 0;
  color: #FFF;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  text-align: center;
}

.emotion-1:focus {
  position: relative;
  height: auto;
  width: auto;
  overflow: visible;
  clip: initial;
  z-index: 20;
  left: 0;
}

.emotion-2 {
  width: auto;
  border-radius: 0;
  display: block;
  border: none;
  background-color: #000;
  padding: 1.5rem;
  text-align: center;
  text-transform: uppercase;
}

.emotion-10 {
  font-family: 'Avenir Next','Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  color: #333;
  font-variant-ligatures: none;
  font-size: 1rem;
  letter-spacing: 0.125rem;
  text-transform: uppercase;
  padding-top: 0.25rem;
  padding-bottom: 0.25rem;
}

@media (min-width: 47.375rem) {
  .emotion-10 {
    font-size: 1.125rem;
  }
}

.emotion-11 {
  font-family: 'Avenir Next','Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  display: block;
  color: #333;
  font-variant-ligatures: none;
  font-size: 0.875rem;
  line-height: 1.4;
  text-transform: capitalize;
  padding-top: 0.25rem;
  padding-bottom: 0.25rem;
}

.emotion-15 {
  padding-top: 0;
  margin-top: 1.25rem;
}

.emotion-18 {
  color: inherit;
}

.emotion-18:hover {
  color: inherit;
}

.emotion-20 {
  font-family: 'Avenir Next','Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 600;
  position: relative;
  color: #333;
}

.emotion-20::before {
  content: "";
  position: absolute;
  top: 0;
  left: -1.675rem;
  height: 100%;
  width: 0.25rem;
  background-color: #333;
}

<ul
    class="emotion-0"
    role="navigation"
  >
    <a
      class="emotion-1"
      href="#quick-filters"
    >
      <span
        class="emotion-2"
      >
        left-nav.skip-to-quick-filters
      </span>
    </a>
    <a
      class="emotion-1"
      href="#facets"
    >
      <span
        class="emotion-2"
      >
        left-nav.skip-to-product-filters
      </span>
    </a>
    <a
      class="emotion-1"
      href="/"
    >
      <span
        class="emotion-2"
      >
        left-nav.skip-to New & Now
      </span>
    </a>
    <a
      class="emotion-1"
      href="/"
    >
      <span
        class="emotion-2"
      >
        left-nav.skip-to Shop By Category
      </span>
    </a>
    <div
      class="emotion-9"
    >
      <h2
        class="emotion-10"
      >
        New & Now
      </h2>
      <ul>
        <li
          class="emotion-11"
        >
          <a
            aria-label="New Arrivals"
            class="emotion-12"
            href="/"
          >
            New Arrivals
          </a>
        </li>
        <li
          class="emotion-11"
        >
          <a
            aria-label="On Trend"
            class="emotion-12"
            href="/"
          >
            On Trend
          </a>
        </li>
      </ul>
    </div>
    <div
      class="emotion-15"
    >
      <h2
        class="emotion-10"
      >
        Shop By Category
      </h2>
      <ul>
        <li
          class="emotion-11"
        >
          <a
            aria-label="The Tee Shop"
            class="emotion-18"
            href="/"
          >
            The Tee Shop
          </a>
        </li>
        <li
          class="emotion-11"
        >
          <a
            aria-label="Sleep & Lounge"
            class="emotion-20"
            href="/"
          >
            Sleep & Lounge
          </a>
        </li>
      </ul>
    </div>
  </ul>
</DocumentFragment>
`;

exports[`<CategoryNav /> Snapshots Branded renders default state correctly BananaRepublic 1`] = `
<DocumentFragment>
  .emotion-0 {
  padding: 0 0.5rem;
  max-width: 13rem;
  padding-left: 2rem;
  display: block;
  float: left;
  padding-bottom: 1rem;
  margin-right: 0.5rem;
}

.emotion-1 {
  position: absolute;
  height: 1px;
  width: 1px;
  overflow: hidden;
  clip: rect(0 0 0 0);
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  background-color: rgba(255, 255, 255, 0.75);
  top: 0;
  color: #FFF;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  text-align: center;
}

.emotion-1:focus {
  position: relative;
  height: auto;
  width: auto;
  overflow: visible;
  clip: initial;
  z-index: 20;
  left: 0;
}

.emotion-2 {
  width: auto;
  border-radius: 0;
  display: block;
  border: none;
  background-color: #000;
  padding: 1.5rem;
  text-align: center;
  text-transform: uppercase;
}

.emotion-10 {
  font-family: 'Lynstone',Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.1rem;
  color: #000;
  line-height: 1.25;
  padding-top: 0.375rem;
  padding-bottom: 0.375rem;
  font-size: 0.8125rem;
}

.emotion-11 {
  font-family: 'Lynstone',Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  display: block;
  color: #000;
  font-size: 0.75rem;
  text-transform: uppercase;
  letter-spacing: 0.1rem;
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
}

.emotion-15 {
  margin-top: 2rem;
}

.emotion-18 {
  color: #D00000;
}

.emotion-18:hover {
  color: #D00000;
}

.emotion-20 {
  font-family: 'Lynstone',Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 600;
  position: relative;
  color: #000;
}

.emotion-20::before {
  content: "";
  position: absolute;
  top: 0;
  left: -1.675rem;
  height: 100%;
  width: 0.25rem;
  background-color: #000;
}

<ul
    class="emotion-0"
    role="navigation"
  >
    <a
      class="emotion-1"
      href="#quick-filters"
    >
      <span
        class="emotion-2"
      >
        left-nav.skip-to-quick-filters
      </span>
    </a>
    <a
      class="emotion-1"
      href="#facets"
    >
      <span
        class="emotion-2"
      >
        left-nav.skip-to-product-filters
      </span>
    </a>
    <a
      class="emotion-1"
      href="/"
    >
      <span
        class="emotion-2"
      >
        left-nav.skip-to New & Now
      </span>
    </a>
    <a
      class="emotion-1"
      href="/"
    >
      <span
        class="emotion-2"
      >
        left-nav.skip-to Shop By Category
      </span>
    </a>
    <div
      class="emotion-9"
    >
      <h2
        class="emotion-10"
      >
        New & Now
      </h2>
      <ul>
        <li
          class="emotion-11"
        >
          <a
            aria-label="New Arrivals"
            class="emotion-12"
            href="/"
          >
            New Arrivals
          </a>
        </li>
        <li
          class="emotion-11"
        >
          <a
            aria-label="On Trend"
            class="emotion-12"
            href="/"
          >
            On Trend
          </a>
        </li>
      </ul>
    </div>
    <div
      class="emotion-15"
    >
      <h2
        class="emotion-10"
      >
        Shop By Category
      </h2>
      <ul>
        <li
          class="emotion-11"
        >
          <a
            aria-label="The Tee Shop"
            class="emotion-18"
            href="/"
          >
            The Tee Shop
          </a>
        </li>
        <li
          class="emotion-11"
        >
          <a
            aria-label="Sleep & Lounge"
            class="emotion-20"
            href="/"
          >
            Sleep & Lounge
          </a>
        </li>
      </ul>
    </div>
  </ul>
</DocumentFragment>
`;

exports[`<CategoryNav /> Snapshots Branded renders default state correctly BananaRepublic in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  padding: 0 0.5rem;
  max-width: 13rem;
  padding-left: 2rem;
  display: block;
  float: left;
  padding-bottom: 1rem;
  margin-right: 0.5rem;
}

.emotion-1 {
  position: absolute;
  height: 1px;
  width: 1px;
  overflow: hidden;
  clip: rect(0 0 0 0);
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  background-color: rgba(255, 255, 255, 0.75);
  top: 0;
  color: #FFF;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  text-align: center;
}

.emotion-1:focus {
  position: relative;
  height: auto;
  width: auto;
  overflow: visible;
  clip: initial;
  z-index: 20;
  left: 0;
}

.emotion-2 {
  width: auto;
  border-radius: 0;
  display: block;
  border: none;
  background-color: #000;
  padding: 1.5rem;
  text-align: center;
  text-transform: uppercase;
}

.emotion-10 {
  font-family: 'Lynstone',Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.1rem;
  color: #000;
  line-height: 1.25;
  padding-top: 0.375rem;
  padding-bottom: 0.375rem;
  font-size: 0.8125rem;
}

.emotion-11 {
  font-family: 'Lynstone',Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  display: block;
  color: #000;
  font-size: 0.75rem;
  text-transform: uppercase;
  letter-spacing: 0.1rem;
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
}

.emotion-15 {
  margin-top: 2rem;
}

.emotion-18 {
  color: #D00000;
}

.emotion-18:hover {
  color: #D00000;
}

.emotion-20 {
  font-family: 'Lynstone',Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 600;
  position: relative;
  color: #000;
}

.emotion-20::before {
  content: "";
  position: absolute;
  top: 0;
  left: -1.675rem;
  height: 100%;
  width: 0.25rem;
  background-color: #000;
}

<ul
    class="emotion-0"
    role="navigation"
  >
    <a
      class="emotion-1"
      href="#quick-filters"
    >
      <span
        class="emotion-2"
      >
        left-nav.skip-to-quick-filters
      </span>
    </a>
    <a
      class="emotion-1"
      href="#facets"
    >
      <span
        class="emotion-2"
      >
        left-nav.skip-to-product-filters
      </span>
    </a>
    <a
      class="emotion-1"
      href="/"
    >
      <span
        class="emotion-2"
      >
        left-nav.skip-to New & Now
      </span>
    </a>
    <a
      class="emotion-1"
      href="/"
    >
      <span
        class="emotion-2"
      >
        left-nav.skip-to Shop By Category
      </span>
    </a>
    <div
      class="emotion-9"
    >
      <h2
        class="emotion-10"
      >
        New & Now
      </h2>
      <ul>
        <li
          class="emotion-11"
        >
          <a
            aria-label="New Arrivals"
            class="emotion-12"
            href="/"
          >
            New Arrivals
          </a>
        </li>
        <li
          class="emotion-11"
        >
          <a
            aria-label="On Trend"
            class="emotion-12"
            href="/"
          >
            On Trend
          </a>
        </li>
      </ul>
    </div>
    <div
      class="emotion-15"
    >
      <h2
        class="emotion-10"
      >
        Shop By Category
      </h2>
      <ul>
        <li
          class="emotion-11"
        >
          <a
            aria-label="The Tee Shop"
            class="emotion-18"
            href="/"
          >
            The Tee Shop
          </a>
        </li>
        <li
          class="emotion-11"
        >
          <a
            aria-label="Sleep & Lounge"
            class="emotion-20"
            href="/"
          >
            Sleep & Lounge
          </a>
        </li>
      </ul>
    </div>
  </ul>
</DocumentFragment>
`;

exports[`<CategoryNav /> Snapshots Branded renders default state correctly BananaRepublicFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  padding: 0 0.5rem;
  max-width: 13rem;
  padding-left: 2rem;
  display: block;
  float: left;
  padding-bottom: 1rem;
  margin-right: 0.5rem;
}

.emotion-1 {
  position: absolute;
  height: 1px;
  width: 1px;
  overflow: hidden;
  clip: rect(0 0 0 0);
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  background-color: rgba(255, 255, 255, 0.75);
  top: 0;
  color: #FFF;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  text-align: center;
}

.emotion-1:focus {
  position: relative;
  height: auto;
  width: auto;
  overflow: visible;
  clip: initial;
  z-index: 20;
  left: 0;
}

.emotion-2 {
  width: auto;
  border-radius: 0;
  display: block;
  border: none;
  background-color: #000;
  padding: 1.5rem;
  text-align: center;
  text-transform: uppercase;
}

.emotion-10 {
  font-family: 'Lynstone',Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 700;
  text-transform: uppercase;
  letter-spacing: 0.1rem;
  color: #000;
  line-height: 1.25;
  padding-top: 0.375rem;
  padding-bottom: 0.375rem;
  font-size: 0.8125rem;
}

.emotion-11 {
  font-family: 'Lynstone',Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  display: block;
  color: #000;
  font-size: 0.75rem;
  text-transform: uppercase;
  letter-spacing: 0.1rem;
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
}

.emotion-15 {
  margin-top: 2rem;
}

.emotion-18 {
  color: #D00000;
}

.emotion-18:hover {
  color: #D00000;
}

.emotion-20 {
  font-family: 'Lynstone',Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 700;
  position: relative;
  color: #000;
}

.emotion-20::before {
  content: "";
  position: absolute;
  top: 0;
  left: -1.675rem;
  height: 100%;
  width: 0.25rem;
  background-color: #000;
}

<ul
    class="emotion-0"
    role="navigation"
  >
    <a
      class="emotion-1"
      href="#quick-filters"
    >
      <span
        class="emotion-2"
      >
        left-nav.skip-to-quick-filters
      </span>
    </a>
    <a
      class="emotion-1"
      href="#facets"
    >
      <span
        class="emotion-2"
      >
        left-nav.skip-to-product-filters
      </span>
    </a>
    <a
      class="emotion-1"
      href="/"
    >
      <span
        class="emotion-2"
      >
        left-nav.skip-to New & Now
      </span>
    </a>
    <a
      class="emotion-1"
      href="/"
    >
      <span
        class="emotion-2"
      >
        left-nav.skip-to Shop By Category
      </span>
    </a>
    <div
      class="emotion-9"
    >
      <h2
        class="emotion-10"
      >
        New & Now
      </h2>
      <ul>
        <li
          class="emotion-11"
        >
          <a
            aria-label="New Arrivals"
            class="emotion-12"
            href="/"
          >
            New Arrivals
          </a>
        </li>
        <li
          class="emotion-11"
        >
          <a
            aria-label="On Trend"
            class="emotion-12"
            href="/"
          >
            On Trend
          </a>
        </li>
      </ul>
    </div>
    <div
      class="emotion-15"
    >
      <h2
        class="emotion-10"
      >
        Shop By Category
      </h2>
      <ul>
        <li
          class="emotion-11"
        >
          <a
            aria-label="The Tee Shop"
            class="emotion-18"
            href="/"
          >
            The Tee Shop
          </a>
        </li>
        <li
          class="emotion-11"
        >
          <a
            aria-label="Sleep & Lounge"
            class="emotion-20"
            href="/"
          >
            Sleep & Lounge
          </a>
        </li>
      </ul>
    </div>
  </ul>
</DocumentFragment>
`;

exports[`<CategoryNav /> Snapshots Branded renders default state correctly BananaRepublicFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  padding: 0 0.5rem;
  max-width: 13rem;
  padding-left: 2rem;
  display: block;
  float: left;
  padding-bottom: 1rem;
  margin-right: 0.5rem;
}

.emotion-1 {
  position: absolute;
  height: 1px;
  width: 1px;
  overflow: hidden;
  clip: rect(0 0 0 0);
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  background-color: rgba(255, 255, 255, 0.75);
  top: 0;
  color: #FFF;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  text-align: center;
}

.emotion-1:focus {
  position: relative;
  height: auto;
  width: auto;
  overflow: visible;
  clip: initial;
  z-index: 20;
  left: 0;
}

.emotion-2 {
  width: auto;
  border-radius: 0;
  display: block;
  border: none;
  background-color: #000;
  padding: 1.5rem;
  text-align: center;
  text-transform: uppercase;
}

.emotion-10 {
  font-family: 'Lynstone',Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 700;
  text-transform: uppercase;
  letter-spacing: 0.1rem;
  color: #000;
  line-height: 1.25;
  padding-top: 0.375rem;
  padding-bottom: 0.375rem;
  font-size: 0.8125rem;
}

.emotion-11 {
  font-family: 'Lynstone',Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  display: block;
  color: #000;
  font-size: 0.75rem;
  text-transform: uppercase;
  letter-spacing: 0.1rem;
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
}

.emotion-15 {
  margin-top: 2rem;
}

.emotion-18 {
  color: #D00000;
}

.emotion-18:hover {
  color: #D00000;
}

.emotion-20 {
  font-family: 'Lynstone',Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 700;
  position: relative;
  color: #000;
}

.emotion-20::before {
  content: "";
  position: absolute;
  top: 0;
  left: -1.675rem;
  height: 100%;
  width: 0.25rem;
  background-color: #000;
}

<ul
    class="emotion-0"
    role="navigation"
  >
    <a
      class="emotion-1"
      href="#quick-filters"
    >
      <span
        class="emotion-2"
      >
        left-nav.skip-to-quick-filters
      </span>
    </a>
    <a
      class="emotion-1"
      href="#facets"
    >
      <span
        class="emotion-2"
      >
        left-nav.skip-to-product-filters
      </span>
    </a>
    <a
      class="emotion-1"
      href="/"
    >
      <span
        class="emotion-2"
      >
        left-nav.skip-to New & Now
      </span>
    </a>
    <a
      class="emotion-1"
      href="/"
    >
      <span
        class="emotion-2"
      >
        left-nav.skip-to Shop By Category
      </span>
    </a>
    <div
      class="emotion-9"
    >
      <h2
        class="emotion-10"
      >
        New & Now
      </h2>
      <ul>
        <li
          class="emotion-11"
        >
          <a
            aria-label="New Arrivals"
            class="emotion-12"
            href="/"
          >
            New Arrivals
          </a>
        </li>
        <li
          class="emotion-11"
        >
          <a
            aria-label="On Trend"
            class="emotion-12"
            href="/"
          >
            On Trend
          </a>
        </li>
      </ul>
    </div>
    <div
      class="emotion-15"
    >
      <h2
        class="emotion-10"
      >
        Shop By Category
      </h2>
      <ul>
        <li
          class="emotion-11"
        >
          <a
            aria-label="The Tee Shop"
            class="emotion-18"
            href="/"
          >
            The Tee Shop
          </a>
        </li>
        <li
          class="emotion-11"
        >
          <a
            aria-label="Sleep & Lounge"
            class="emotion-20"
            href="/"
          >
            Sleep & Lounge
          </a>
        </li>
      </ul>
    </div>
  </ul>
</DocumentFragment>
`;

exports[`<CategoryNav /> Snapshots Branded renders default state correctly Gap 1`] = `
<DocumentFragment>
  .emotion-0 {
  padding: 0 0.5rem;
  max-width: 13rem;
  padding-left: 2rem;
  display: block;
  float: left;
  padding-bottom: 1rem;
  margin-right: 0.5rem;
}

.emotion-1 {
  position: absolute;
  height: 1px;
  width: 1px;
  overflow: hidden;
  clip: rect(0 0 0 0);
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  background-color: rgba(255, 255, 255, 0.75);
  top: 0;
  color: #FFF;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  text-align: center;
}

.emotion-1:focus {
  position: relative;
  height: auto;
  width: auto;
  overflow: visible;
  clip: initial;
  z-index: 20;
  left: 0;
}

.emotion-2 {
  width: auto;
  border-radius: 0;
  display: block;
  border: none;
  background-color: #000;
  padding: 1.5rem;
  text-align: center;
  text-transform: uppercase;
}

.emotion-10 {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 700;
  color: #002554;
  font-size: 1.067rem;
  text-transform: capitalize;
  padding-top: 0.375rem;
  padding-bottom: 0.375rem;
}

.emotion-11 {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  display: block;
  color: #666;
  line-height: 1.38;
  font-size: 0.867rem;
  padding-top: 0.375rem;
  padding-bottom: 0.375rem;
}

.emotion-11:hover {
  color: #002554;
}

.emotion-15 {
  margin-top: 0.675rem;
}

.emotion-18 {
  color: #D00000;
}

.emotion-18:hover {
  color: #D00000;
}

.emotion-20 {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 700;
  position: relative;
  color: #002554;
}

.emotion-20::before {
  content: "";
  position: absolute;
  top: 0;
  left: -1.675rem;
  height: 100%;
  width: 0.25rem;
  background-color: #002554;
}

<ul
    class="emotion-0"
    role="navigation"
  >
    <a
      class="emotion-1"
      href="#quick-filters"
    >
      <span
        class="emotion-2"
      >
        left-nav.skip-to-quick-filters
      </span>
    </a>
    <a
      class="emotion-1"
      href="#facets"
    >
      <span
        class="emotion-2"
      >
        left-nav.skip-to-product-filters
      </span>
    </a>
    <a
      class="emotion-1"
      href="/"
    >
      <span
        class="emotion-2"
      >
        left-nav.skip-to New & Now
      </span>
    </a>
    <a
      class="emotion-1"
      href="/"
    >
      <span
        class="emotion-2"
      >
        left-nav.skip-to Shop By Category
      </span>
    </a>
    <div
      class="emotion-9"
    >
      <h2
        class="emotion-10"
      >
        New & Now
      </h2>
      <ul>
        <li
          class="emotion-11"
        >
          <a
            aria-label="New Arrivals"
            class="emotion-12"
            href="/"
          >
            New Arrivals
          </a>
        </li>
        <li
          class="emotion-11"
        >
          <a
            aria-label="On Trend"
            class="emotion-12"
            href="/"
          >
            On Trend
          </a>
        </li>
      </ul>
    </div>
    <div
      class="emotion-15"
    >
      <h2
        class="emotion-10"
      >
        Shop By Category
      </h2>
      <ul>
        <li
          class="emotion-11"
        >
          <a
            aria-label="The Tee Shop"
            class="emotion-18"
            href="/"
          >
            The Tee Shop
          </a>
        </li>
        <li
          class="emotion-11"
        >
          <a
            aria-label="Sleep & Lounge"
            class="emotion-20"
            href="/"
          >
            Sleep & Lounge
          </a>
        </li>
      </ul>
    </div>
  </ul>
</DocumentFragment>
`;

exports[`<CategoryNav /> Snapshots Branded renders default state correctly Gap in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  padding: 0 0.5rem;
  max-width: 13rem;
  padding-left: 2rem;
  display: block;
  float: left;
  padding-bottom: 1rem;
  margin-right: 0.5rem;
}

.emotion-1 {
  position: absolute;
  height: 1px;
  width: 1px;
  overflow: hidden;
  clip: rect(0 0 0 0);
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  background-color: rgba(255, 255, 255, 0.75);
  top: 0;
  color: #FFF;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  text-align: center;
}

.emotion-1:focus {
  position: relative;
  height: auto;
  width: auto;
  overflow: visible;
  clip: initial;
  z-index: 20;
  left: 0;
}

.emotion-2 {
  width: auto;
  border-radius: 0;
  display: block;
  border: none;
  background-color: #000;
  padding: 1.5rem;
  text-align: center;
  text-transform: uppercase;
}

.emotion-10 {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 700;
  color: #002554;
  font-size: 1.067rem;
  text-transform: capitalize;
  padding-top: 0.375rem;
  padding-bottom: 0.375rem;
}

.emotion-11 {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  display: block;
  color: #666;
  line-height: 1.38;
  font-size: 0.867rem;
  padding-top: 0.375rem;
  padding-bottom: 0.375rem;
}

.emotion-11:hover {
  color: #002554;
}

.emotion-15 {
  margin-top: 0.675rem;
}

.emotion-18 {
  color: #D00000;
}

.emotion-18:hover {
  color: #D00000;
}

.emotion-20 {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 700;
  position: relative;
  color: #002554;
}

.emotion-20::before {
  content: "";
  position: absolute;
  top: 0;
  left: -1.675rem;
  height: 100%;
  width: 0.25rem;
  background-color: #002554;
}

<ul
    class="emotion-0"
    role="navigation"
  >
    <a
      class="emotion-1"
      href="#quick-filters"
    >
      <span
        class="emotion-2"
      >
        left-nav.skip-to-quick-filters
      </span>
    </a>
    <a
      class="emotion-1"
      href="#facets"
    >
      <span
        class="emotion-2"
      >
        left-nav.skip-to-product-filters
      </span>
    </a>
    <a
      class="emotion-1"
      href="/"
    >
      <span
        class="emotion-2"
      >
        left-nav.skip-to New & Now
      </span>
    </a>
    <a
      class="emotion-1"
      href="/"
    >
      <span
        class="emotion-2"
      >
        left-nav.skip-to Shop By Category
      </span>
    </a>
    <div
      class="emotion-9"
    >
      <h2
        class="emotion-10"
      >
        New & Now
      </h2>
      <ul>
        <li
          class="emotion-11"
        >
          <a
            aria-label="New Arrivals"
            class="emotion-12"
            href="/"
          >
            New Arrivals
          </a>
        </li>
        <li
          class="emotion-11"
        >
          <a
            aria-label="On Trend"
            class="emotion-12"
            href="/"
          >
            On Trend
          </a>
        </li>
      </ul>
    </div>
    <div
      class="emotion-15"
    >
      <h2
        class="emotion-10"
      >
        Shop By Category
      </h2>
      <ul>
        <li
          class="emotion-11"
        >
          <a
            aria-label="The Tee Shop"
            class="emotion-18"
            href="/"
          >
            The Tee Shop
          </a>
        </li>
        <li
          class="emotion-11"
        >
          <a
            aria-label="Sleep & Lounge"
            class="emotion-20"
            href="/"
          >
            Sleep & Lounge
          </a>
        </li>
      </ul>
    </div>
  </ul>
</DocumentFragment>
`;

exports[`<CategoryNav /> Snapshots Branded renders default state correctly GapFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  padding: 0 0.5rem;
  max-width: 13rem;
  padding-left: 2rem;
  display: block;
  float: left;
  padding-bottom: 1rem;
  margin-right: 0.5rem;
}

.emotion-1 {
  position: absolute;
  height: 1px;
  width: 1px;
  overflow: hidden;
  clip: rect(0 0 0 0);
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  background-color: rgba(255, 255, 255, 0.75);
  top: 0;
  color: #FFF;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  text-align: center;
}

.emotion-1:focus {
  position: relative;
  height: auto;
  width: auto;
  overflow: visible;
  clip: initial;
  z-index: 20;
  left: 0;
}

.emotion-2 {
  width: auto;
  border-radius: 0;
  display: block;
  border: none;
  background-color: #000;
  padding: 1.5rem;
  text-align: center;
  text-transform: uppercase;
}

.emotion-10 {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 700;
  color: #002554;
  font-size: 1.067rem;
  text-transform: capitalize;
  padding-top: 0.375rem;
  padding-bottom: 0.375rem;
}

.emotion-11 {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  display: block;
  color: #666;
  line-height: 1.38;
  font-size: 0.867rem;
  padding-top: 0.375rem;
  padding-bottom: 0.375rem;
}

.emotion-11:hover {
  color: #002554;
}

.emotion-15 {
  margin-top: 0.675rem;
}

.emotion-18 {
  color: #D00000;
}

.emotion-18:hover {
  color: #D00000;
}

.emotion-20 {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 700;
  position: relative;
  color: #002554;
}

.emotion-20::before {
  content: "";
  position: absolute;
  top: 0;
  left: -1.675rem;
  height: 100%;
  width: 0.25rem;
  background-color: #002554;
}

<ul
    class="emotion-0"
    role="navigation"
  >
    <a
      class="emotion-1"
      href="#quick-filters"
    >
      <span
        class="emotion-2"
      >
        left-nav.skip-to-quick-filters
      </span>
    </a>
    <a
      class="emotion-1"
      href="#facets"
    >
      <span
        class="emotion-2"
      >
        left-nav.skip-to-product-filters
      </span>
    </a>
    <a
      class="emotion-1"
      href="/"
    >
      <span
        class="emotion-2"
      >
        left-nav.skip-to New & Now
      </span>
    </a>
    <a
      class="emotion-1"
      href="/"
    >
      <span
        class="emotion-2"
      >
        left-nav.skip-to Shop By Category
      </span>
    </a>
    <div
      class="emotion-9"
    >
      <h2
        class="emotion-10"
      >
        New & Now
      </h2>
      <ul>
        <li
          class="emotion-11"
        >
          <a
            aria-label="New Arrivals"
            class="emotion-12"
            href="/"
          >
            New Arrivals
          </a>
        </li>
        <li
          class="emotion-11"
        >
          <a
            aria-label="On Trend"
            class="emotion-12"
            href="/"
          >
            On Trend
          </a>
        </li>
      </ul>
    </div>
    <div
      class="emotion-15"
    >
      <h2
        class="emotion-10"
      >
        Shop By Category
      </h2>
      <ul>
        <li
          class="emotion-11"
        >
          <a
            aria-label="The Tee Shop"
            class="emotion-18"
            href="/"
          >
            The Tee Shop
          </a>
        </li>
        <li
          class="emotion-11"
        >
          <a
            aria-label="Sleep & Lounge"
            class="emotion-20"
            href="/"
          >
            Sleep & Lounge
          </a>
        </li>
      </ul>
    </div>
  </ul>
</DocumentFragment>
`;

exports[`<CategoryNav /> Snapshots Branded renders default state correctly GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  padding: 0 0.5rem;
  max-width: 13rem;
  padding-left: 2rem;
  display: block;
  float: left;
  padding-bottom: 1rem;
  margin-right: 0.5rem;
}

.emotion-1 {
  position: absolute;
  height: 1px;
  width: 1px;
  overflow: hidden;
  clip: rect(0 0 0 0);
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  background-color: rgba(255, 255, 255, 0.75);
  top: 0;
  color: #FFF;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  text-align: center;
}

.emotion-1:focus {
  position: relative;
  height: auto;
  width: auto;
  overflow: visible;
  clip: initial;
  z-index: 20;
  left: 0;
}

.emotion-2 {
  width: auto;
  border-radius: 0;
  display: block;
  border: none;
  background-color: #000;
  padding: 1.5rem;
  text-align: center;
  text-transform: uppercase;
}

.emotion-10 {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 700;
  color: #002554;
  font-size: 1.067rem;
  text-transform: capitalize;
  padding-top: 0.375rem;
  padding-bottom: 0.375rem;
}

.emotion-11 {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  display: block;
  color: #666;
  line-height: 1.38;
  font-size: 0.867rem;
  padding-top: 0.375rem;
  padding-bottom: 0.375rem;
}

.emotion-11:hover {
  color: #002554;
}

.emotion-15 {
  margin-top: 0.675rem;
}

.emotion-18 {
  color: #D00000;
}

.emotion-18:hover {
  color: #D00000;
}

.emotion-20 {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 700;
  position: relative;
  color: #002554;
}

.emotion-20::before {
  content: "";
  position: absolute;
  top: 0;
  left: -1.675rem;
  height: 100%;
  width: 0.25rem;
  background-color: #002554;
}

<ul
    class="emotion-0"
    role="navigation"
  >
    <a
      class="emotion-1"
      href="#quick-filters"
    >
      <span
        class="emotion-2"
      >
        left-nav.skip-to-quick-filters
      </span>
    </a>
    <a
      class="emotion-1"
      href="#facets"
    >
      <span
        class="emotion-2"
      >
        left-nav.skip-to-product-filters
      </span>
    </a>
    <a
      class="emotion-1"
      href="/"
    >
      <span
        class="emotion-2"
      >
        left-nav.skip-to New & Now
      </span>
    </a>
    <a
      class="emotion-1"
      href="/"
    >
      <span
        class="emotion-2"
      >
        left-nav.skip-to Shop By Category
      </span>
    </a>
    <div
      class="emotion-9"
    >
      <h2
        class="emotion-10"
      >
        New & Now
      </h2>
      <ul>
        <li
          class="emotion-11"
        >
          <a
            aria-label="New Arrivals"
            class="emotion-12"
            href="/"
          >
            New Arrivals
          </a>
        </li>
        <li
          class="emotion-11"
        >
          <a
            aria-label="On Trend"
            class="emotion-12"
            href="/"
          >
            On Trend
          </a>
        </li>
      </ul>
    </div>
    <div
      class="emotion-15"
    >
      <h2
        class="emotion-10"
      >
        Shop By Category
      </h2>
      <ul>
        <li
          class="emotion-11"
        >
          <a
            aria-label="The Tee Shop"
            class="emotion-18"
            href="/"
          >
            The Tee Shop
          </a>
        </li>
        <li
          class="emotion-11"
        >
          <a
            aria-label="Sleep & Lounge"
            class="emotion-20"
            href="/"
          >
            Sleep & Lounge
          </a>
        </li>
      </ul>
    </div>
  </ul>
</DocumentFragment>
`;

exports[`<CategoryNav /> Snapshots Branded renders default state correctly OldNavy 1`] = `
<DocumentFragment>
  .emotion-0 {
  padding: 0 0.5rem;
  max-width: 13rem;
  padding-left: 2rem;
  display: block;
  float: left;
  padding-bottom: 1rem;
  margin-right: 0.5rem;
}

.emotion-1 {
  position: absolute;
  height: 1px;
  width: 1px;
  overflow: hidden;
  clip: rect(0 0 0 0);
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  background-color: rgba(255, 255, 255, 0.75);
  top: 0;
  color: #FFF;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  text-align: center;
}

.emotion-1:focus {
  position: relative;
  height: auto;
  width: auto;
  overflow: visible;
  clip: initial;
  z-index: 20;
  left: 0;
}

.emotion-2 {
  width: auto;
  border-radius: 0;
  display: block;
  border: none;
  background-color: #000;
  padding: 1.5rem;
  text-align: center;
  text-transform: uppercase;
}

.emotion-10 {
  font-family: 'Gotham','Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 700;
  font-size: 0.9rem;
  color: #666;
  text-transform: capitalize;
  padding-top: 0.3125rem;
  padding-bottom: 0.3125rem;
}

.emotion-11 {
  font-family: 'Gotham','Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  display: block;
  color: #666;
  font-size: 0.8rem;
  text-transform: capitalize;
  padding-top: 0.375rem;
  padding-bottom: 0.375rem;
}

.emotion-11:hover {
  color: #003764;
}

.emotion-15 {
  margin-top: 2rem;
}

.emotion-18 {
  color: #D00000;
}

.emotion-18:hover {
  color: #D00000;
}

.emotion-20 {
  font-family: 'Gotham','Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 700;
  position: relative;
  color: #003764;
}

.emotion-20::before {
  content: "";
  position: absolute;
  top: 0;
  left: -1.675rem;
  height: 100%;
  width: 0.25rem;
  background-color: #003764;
}

<ul
    class="emotion-0"
    role="navigation"
  >
    <a
      class="emotion-1"
      href="#quick-filters"
    >
      <span
        class="emotion-2"
      >
        left-nav.skip-to-quick-filters
      </span>
    </a>
    <a
      class="emotion-1"
      href="#facets"
    >
      <span
        class="emotion-2"
      >
        left-nav.skip-to-product-filters
      </span>
    </a>
    <a
      class="emotion-1"
      href="/"
    >
      <span
        class="emotion-2"
      >
        left-nav.skip-to New & Now
      </span>
    </a>
    <a
      class="emotion-1"
      href="/"
    >
      <span
        class="emotion-2"
      >
        left-nav.skip-to Shop By Category
      </span>
    </a>
    <div
      class="emotion-9"
    >
      <h2
        class="emotion-10"
      >
        New & Now
      </h2>
      <ul>
        <li
          class="emotion-11"
        >
          <a
            aria-label="New Arrivals"
            class="emotion-12"
            href="/"
          >
            New Arrivals
          </a>
        </li>
        <li
          class="emotion-11"
        >
          <a
            aria-label="On Trend"
            class="emotion-12"
            href="/"
          >
            On Trend
          </a>
        </li>
      </ul>
    </div>
    <div
      class="emotion-15"
    >
      <h2
        class="emotion-10"
      >
        Shop By Category
      </h2>
      <ul>
        <li
          class="emotion-11"
        >
          <a
            aria-label="The Tee Shop"
            class="emotion-18"
            href="/"
          >
            The Tee Shop
          </a>
        </li>
        <li
          class="emotion-11"
        >
          <a
            aria-label="Sleep & Lounge"
            class="emotion-20"
            href="/"
          >
            Sleep & Lounge
          </a>
        </li>
      </ul>
    </div>
  </ul>
</DocumentFragment>
`;

exports[`<CategoryNav /> Snapshots Branded renders default state correctly OldNavy in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  padding: 0 0.5rem;
  max-width: 13rem;
  padding-left: 2rem;
  display: block;
  float: left;
  padding-bottom: 1rem;
  margin-right: 0.5rem;
}

.emotion-1 {
  position: absolute;
  height: 1px;
  width: 1px;
  overflow: hidden;
  clip: rect(0 0 0 0);
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  background-color: rgba(255, 255, 255, 0.75);
  top: 0;
  color: #FFF;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  text-align: center;
}

.emotion-1:focus {
  position: relative;
  height: auto;
  width: auto;
  overflow: visible;
  clip: initial;
  z-index: 20;
  left: 0;
}

.emotion-2 {
  width: auto;
  border-radius: 0;
  display: block;
  border: none;
  background-color: #000;
  padding: 1.5rem;
  text-align: center;
  text-transform: uppercase;
}

.emotion-10 {
  font-family: 'Gotham','Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 700;
  font-size: 0.9rem;
  color: #666;
  text-transform: capitalize;
  padding-top: 0.3125rem;
  padding-bottom: 0.3125rem;
}

.emotion-11 {
  font-family: 'Gotham','Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  display: block;
  color: #666;
  font-size: 0.8rem;
  text-transform: capitalize;
  padding-top: 0.375rem;
  padding-bottom: 0.375rem;
}

.emotion-11:hover {
  color: #003764;
}

.emotion-15 {
  margin-top: 2rem;
}

.emotion-18 {
  color: #D00000;
}

.emotion-18:hover {
  color: #D00000;
}

.emotion-20 {
  font-family: 'Gotham','Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 700;
  position: relative;
  color: #003764;
}

.emotion-20::before {
  content: "";
  position: absolute;
  top: 0;
  left: -1.675rem;
  height: 100%;
  width: 0.25rem;
  background-color: #003764;
}

<ul
    class="emotion-0"
    role="navigation"
  >
    <a
      class="emotion-1"
      href="#quick-filters"
    >
      <span
        class="emotion-2"
      >
        left-nav.skip-to-quick-filters
      </span>
    </a>
    <a
      class="emotion-1"
      href="#facets"
    >
      <span
        class="emotion-2"
      >
        left-nav.skip-to-product-filters
      </span>
    </a>
    <a
      class="emotion-1"
      href="/"
    >
      <span
        class="emotion-2"
      >
        left-nav.skip-to New & Now
      </span>
    </a>
    <a
      class="emotion-1"
      href="/"
    >
      <span
        class="emotion-2"
      >
        left-nav.skip-to Shop By Category
      </span>
    </a>
    <div
      class="emotion-9"
    >
      <h2
        class="emotion-10"
      >
        New & Now
      </h2>
      <ul>
        <li
          class="emotion-11"
        >
          <a
            aria-label="New Arrivals"
            class="emotion-12"
            href="/"
          >
            New Arrivals
          </a>
        </li>
        <li
          class="emotion-11"
        >
          <a
            aria-label="On Trend"
            class="emotion-12"
            href="/"
          >
            On Trend
          </a>
        </li>
      </ul>
    </div>
    <div
      class="emotion-15"
    >
      <h2
        class="emotion-10"
      >
        Shop By Category
      </h2>
      <ul>
        <li
          class="emotion-11"
        >
          <a
            aria-label="The Tee Shop"
            class="emotion-18"
            href="/"
          >
            The Tee Shop
          </a>
        </li>
        <li
          class="emotion-11"
        >
          <a
            aria-label="Sleep & Lounge"
            class="emotion-20"
            href="/"
          >
            Sleep & Lounge
          </a>
        </li>
      </ul>
    </div>
  </ul>
</DocumentFragment>
`;

exports[`<CategoryNav /> Snapshots Breakpoints for x-large breakpoint renders default state correctly Athleta 1`] = `
<DocumentFragment>
  .emotion-0 {
  padding: 0 0.5rem;
  max-width: 13rem;
  padding-left: 2rem;
  display: block;
  float: left;
  padding-bottom: 1rem;
  margin-right: 0.5rem;
}

.emotion-1 {
  position: absolute;
  height: 1px;
  width: 1px;
  overflow: hidden;
  clip: rect(0 0 0 0);
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  background-color: rgba(255, 255, 255, 0.75);
  top: 0;
  color: #FFF;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  text-align: center;
}

.emotion-1:focus {
  position: relative;
  height: auto;
  width: auto;
  overflow: visible;
  clip: initial;
  z-index: 20;
  left: 0;
}

.emotion-2 {
  width: auto;
  border-radius: 0;
  display: block;
  border: none;
  background-color: #000;
  padding: 1.5rem;
  text-align: center;
  text-transform: uppercase;
}

.emotion-12 {
  font-family: 'Avenir Next','Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  color: #333;
  font-variant-ligatures: none;
  font-size: 1rem;
  letter-spacing: 0.125rem;
  text-transform: uppercase;
  padding-top: 0.25rem;
  padding-bottom: 0.25rem;
}

@media (min-width: 47.375rem) {
  .emotion-12 {
    font-size: 1.125rem;
  }
}

.emotion-13 {
  font-family: 'Avenir Next','Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  display: block;
  color: #333;
  font-variant-ligatures: none;
  font-size: 0.875rem;
  line-height: 1.4;
  text-transform: capitalize;
  padding-top: 0.25rem;
  padding-bottom: 0.25rem;
}

.emotion-17 {
  padding-top: 0;
  margin-top: 1.25rem;
}

.emotion-20 {
  color: inherit;
}

.emotion-20:hover {
  color: inherit;
}

.emotion-22 {
  font-family: 'Avenir Next','Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 600;
  position: relative;
  color: #333;
}

.emotion-22::before {
  content: "";
  position: absolute;
  top: 0;
  left: -1.675rem;
  height: 100%;
  width: 0.25rem;
  background-color: #333;
}

<ul
    class="emotion-0"
    role="navigation"
  >
    <a
      class="emotion-1"
      href="#quick-filters"
    >
      <span
        class="emotion-2"
      >
        left-nav.skip-to-quick-filters
      </span>
    </a>
    <a
      class="emotion-1"
      href="#facets"
    >
      <span
        class="emotion-2"
      >
        left-nav.skip-to-product-filters
      </span>
    </a>
    <a
      class="emotion-1"
      href="#faceted-grid"
    >
      <span
        class="emotion-2"
      >
        left-nav.skip-to-products
      </span>
    </a>
    <a
      class="emotion-1"
      href="/"
    >
      <span
        class="emotion-2"
      >
        left-nav.skip-to New & Now
      </span>
    </a>
    <a
      class="emotion-1"
      href="/"
    >
      <span
        class="emotion-2"
      >
        left-nav.skip-to Shop By Category
      </span>
    </a>
    <div
      class="emotion-11"
    >
      <h2
        class="emotion-12"
      >
        New & Now
      </h2>
      <ul>
        <li
          class="emotion-13"
        >
          <a
            aria-label="New Arrivals"
            class="emotion-14"
            href="/"
          >
            New Arrivals
          </a>
        </li>
        <li
          class="emotion-13"
        >
          <a
            aria-label="On Trend"
            class="emotion-14"
            href="/"
          >
            On Trend
          </a>
        </li>
      </ul>
    </div>
    <div
      class="emotion-17"
    >
      <h2
        class="emotion-12"
      >
        Shop By Category
      </h2>
      <ul>
        <li
          class="emotion-13"
        >
          <a
            aria-label="The Tee Shop"
            class="emotion-20"
            href="/"
          >
            The Tee Shop
          </a>
        </li>
        <li
          class="emotion-13"
        >
          <a
            aria-label="Sleep & Lounge"
            class="emotion-22"
            href="/"
          >
            Sleep & Lounge
          </a>
        </li>
      </ul>
    </div>
  </ul>
</DocumentFragment>
`;

exports[`<CategoryNav /> Snapshots Breakpoints for x-large breakpoint renders default state correctly Athleta in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  padding: 0 0.5rem;
  max-width: 13rem;
  padding-left: 2rem;
  display: block;
  float: left;
  padding-bottom: 1rem;
  margin-right: 0.5rem;
}

.emotion-1 {
  position: absolute;
  height: 1px;
  width: 1px;
  overflow: hidden;
  clip: rect(0 0 0 0);
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  background-color: rgba(255, 255, 255, 0.75);
  top: 0;
  color: #FFF;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  text-align: center;
}

.emotion-1:focus {
  position: relative;
  height: auto;
  width: auto;
  overflow: visible;
  clip: initial;
  z-index: 20;
  left: 0;
}

.emotion-2 {
  width: auto;
  border-radius: 0;
  display: block;
  border: none;
  background-color: #000;
  padding: 1.5rem;
  text-align: center;
  text-transform: uppercase;
}

.emotion-12 {
  font-family: 'Avenir Next','Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  color: #333;
  font-variant-ligatures: none;
  font-size: 1rem;
  letter-spacing: 0.125rem;
  text-transform: uppercase;
  padding-top: 0.25rem;
  padding-bottom: 0.25rem;
}

@media (min-width: 47.375rem) {
  .emotion-12 {
    font-size: 1.125rem;
  }
}

.emotion-13 {
  font-family: 'Avenir Next','Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  display: block;
  color: #333;
  font-variant-ligatures: none;
  font-size: 0.875rem;
  line-height: 1.4;
  text-transform: capitalize;
  padding-top: 0.25rem;
  padding-bottom: 0.25rem;
}

.emotion-17 {
  padding-top: 0;
  margin-top: 1.25rem;
}

.emotion-20 {
  color: inherit;
}

.emotion-20:hover {
  color: inherit;
}

.emotion-22 {
  font-family: 'Avenir Next','Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 600;
  position: relative;
  color: #333;
}

.emotion-22::before {
  content: "";
  position: absolute;
  top: 0;
  left: -1.675rem;
  height: 100%;
  width: 0.25rem;
  background-color: #333;
}

<ul
    class="emotion-0"
    role="navigation"
  >
    <a
      class="emotion-1"
      href="#quick-filters"
    >
      <span
        class="emotion-2"
      >
        left-nav.skip-to-quick-filters
      </span>
    </a>
    <a
      class="emotion-1"
      href="#facets"
    >
      <span
        class="emotion-2"
      >
        left-nav.skip-to-product-filters
      </span>
    </a>
    <a
      class="emotion-1"
      href="#faceted-grid"
    >
      <span
        class="emotion-2"
      >
        left-nav.skip-to-products
      </span>
    </a>
    <a
      class="emotion-1"
      href="/"
    >
      <span
        class="emotion-2"
      >
        left-nav.skip-to New & Now
      </span>
    </a>
    <a
      class="emotion-1"
      href="/"
    >
      <span
        class="emotion-2"
      >
        left-nav.skip-to Shop By Category
      </span>
    </a>
    <div
      class="emotion-11"
    >
      <h2
        class="emotion-12"
      >
        New & Now
      </h2>
      <ul>
        <li
          class="emotion-13"
        >
          <a
            aria-label="New Arrivals"
            class="emotion-14"
            href="/"
          >
            New Arrivals
          </a>
        </li>
        <li
          class="emotion-13"
        >
          <a
            aria-label="On Trend"
            class="emotion-14"
            href="/"
          >
            On Trend
          </a>
        </li>
      </ul>
    </div>
    <div
      class="emotion-17"
    >
      <h2
        class="emotion-12"
      >
        Shop By Category
      </h2>
      <ul>
        <li
          class="emotion-13"
        >
          <a
            aria-label="The Tee Shop"
            class="emotion-20"
            href="/"
          >
            The Tee Shop
          </a>
        </li>
        <li
          class="emotion-13"
        >
          <a
            aria-label="Sleep & Lounge"
            class="emotion-22"
            href="/"
          >
            Sleep & Lounge
          </a>
        </li>
      </ul>
    </div>
  </ul>
</DocumentFragment>
`;

exports[`<CategoryNav /> Snapshots Breakpoints for x-large breakpoint renders default state correctly BananaRepublic 1`] = `
<DocumentFragment>
  .emotion-0 {
  padding: 0 0.5rem;
  max-width: 13rem;
  padding-left: 2rem;
  display: block;
  float: left;
  padding-bottom: 1rem;
  margin-right: 0.5rem;
}

.emotion-1 {
  position: absolute;
  height: 1px;
  width: 1px;
  overflow: hidden;
  clip: rect(0 0 0 0);
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  background-color: rgba(255, 255, 255, 0.75);
  top: 0;
  color: #FFF;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  text-align: center;
}

.emotion-1:focus {
  position: relative;
  height: auto;
  width: auto;
  overflow: visible;
  clip: initial;
  z-index: 20;
  left: 0;
}

.emotion-2 {
  width: auto;
  border-radius: 0;
  display: block;
  border: none;
  background-color: #000;
  padding: 1.5rem;
  text-align: center;
  text-transform: uppercase;
}

.emotion-12 {
  font-family: 'Lynstone',Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.1rem;
  color: #000;
  line-height: 1.25;
  padding-top: 0.375rem;
  padding-bottom: 0.375rem;
  font-size: 0.8125rem;
}

.emotion-13 {
  font-family: 'Lynstone',Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  display: block;
  color: #000;
  font-size: 0.75rem;
  text-transform: uppercase;
  letter-spacing: 0.1rem;
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
}

.emotion-17 {
  margin-top: 2rem;
}

.emotion-20 {
  color: #D00000;
}

.emotion-20:hover {
  color: #D00000;
}

.emotion-22 {
  font-family: 'Lynstone',Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 600;
  position: relative;
  color: #000;
}

.emotion-22::before {
  content: "";
  position: absolute;
  top: 0;
  left: -1.675rem;
  height: 100%;
  width: 0.25rem;
  background-color: #000;
}

<ul
    class="emotion-0"
    role="navigation"
  >
    <a
      class="emotion-1"
      href="#quick-filters"
    >
      <span
        class="emotion-2"
      >
        left-nav.skip-to-quick-filters
      </span>
    </a>
    <a
      class="emotion-1"
      href="#facets"
    >
      <span
        class="emotion-2"
      >
        left-nav.skip-to-product-filters
      </span>
    </a>
    <a
      class="emotion-1"
      href="#faceted-grid"
    >
      <span
        class="emotion-2"
      >
        left-nav.skip-to-products
      </span>
    </a>
    <a
      class="emotion-1"
      href="/"
    >
      <span
        class="emotion-2"
      >
        left-nav.skip-to New & Now
      </span>
    </a>
    <a
      class="emotion-1"
      href="/"
    >
      <span
        class="emotion-2"
      >
        left-nav.skip-to Shop By Category
      </span>
    </a>
    <div
      class="emotion-11"
    >
      <h2
        class="emotion-12"
      >
        New & Now
      </h2>
      <ul>
        <li
          class="emotion-13"
        >
          <a
            aria-label="New Arrivals"
            class="emotion-14"
            href="/"
          >
            New Arrivals
          </a>
        </li>
        <li
          class="emotion-13"
        >
          <a
            aria-label="On Trend"
            class="emotion-14"
            href="/"
          >
            On Trend
          </a>
        </li>
      </ul>
    </div>
    <div
      class="emotion-17"
    >
      <h2
        class="emotion-12"
      >
        Shop By Category
      </h2>
      <ul>
        <li
          class="emotion-13"
        >
          <a
            aria-label="The Tee Shop"
            class="emotion-20"
            href="/"
          >
            The Tee Shop
          </a>
        </li>
        <li
          class="emotion-13"
        >
          <a
            aria-label="Sleep & Lounge"
            class="emotion-22"
            href="/"
          >
            Sleep & Lounge
          </a>
        </li>
      </ul>
    </div>
  </ul>
</DocumentFragment>
`;

exports[`<CategoryNav /> Snapshots Breakpoints for x-large breakpoint renders default state correctly BananaRepublic in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  padding: 0 0.5rem;
  max-width: 13rem;
  padding-left: 2rem;
  display: block;
  float: left;
  padding-bottom: 1rem;
  margin-right: 0.5rem;
}

.emotion-1 {
  position: absolute;
  height: 1px;
  width: 1px;
  overflow: hidden;
  clip: rect(0 0 0 0);
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  background-color: rgba(255, 255, 255, 0.75);
  top: 0;
  color: #FFF;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  text-align: center;
}

.emotion-1:focus {
  position: relative;
  height: auto;
  width: auto;
  overflow: visible;
  clip: initial;
  z-index: 20;
  left: 0;
}

.emotion-2 {
  width: auto;
  border-radius: 0;
  display: block;
  border: none;
  background-color: #000;
  padding: 1.5rem;
  text-align: center;
  text-transform: uppercase;
}

.emotion-12 {
  font-family: 'Lynstone',Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.1rem;
  color: #000;
  line-height: 1.25;
  padding-top: 0.375rem;
  padding-bottom: 0.375rem;
  font-size: 0.8125rem;
}

.emotion-13 {
  font-family: 'Lynstone',Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  display: block;
  color: #000;
  font-size: 0.75rem;
  text-transform: uppercase;
  letter-spacing: 0.1rem;
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
}

.emotion-17 {
  margin-top: 2rem;
}

.emotion-20 {
  color: #D00000;
}

.emotion-20:hover {
  color: #D00000;
}

.emotion-22 {
  font-family: 'Lynstone',Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 600;
  position: relative;
  color: #000;
}

.emotion-22::before {
  content: "";
  position: absolute;
  top: 0;
  left: -1.675rem;
  height: 100%;
  width: 0.25rem;
  background-color: #000;
}

<ul
    class="emotion-0"
    role="navigation"
  >
    <a
      class="emotion-1"
      href="#quick-filters"
    >
      <span
        class="emotion-2"
      >
        left-nav.skip-to-quick-filters
      </span>
    </a>
    <a
      class="emotion-1"
      href="#facets"
    >
      <span
        class="emotion-2"
      >
        left-nav.skip-to-product-filters
      </span>
    </a>
    <a
      class="emotion-1"
      href="#faceted-grid"
    >
      <span
        class="emotion-2"
      >
        left-nav.skip-to-products
      </span>
    </a>
    <a
      class="emotion-1"
      href="/"
    >
      <span
        class="emotion-2"
      >
        left-nav.skip-to New & Now
      </span>
    </a>
    <a
      class="emotion-1"
      href="/"
    >
      <span
        class="emotion-2"
      >
        left-nav.skip-to Shop By Category
      </span>
    </a>
    <div
      class="emotion-11"
    >
      <h2
        class="emotion-12"
      >
        New & Now
      </h2>
      <ul>
        <li
          class="emotion-13"
        >
          <a
            aria-label="New Arrivals"
            class="emotion-14"
            href="/"
          >
            New Arrivals
          </a>
        </li>
        <li
          class="emotion-13"
        >
          <a
            aria-label="On Trend"
            class="emotion-14"
            href="/"
          >
            On Trend
          </a>
        </li>
      </ul>
    </div>
    <div
      class="emotion-17"
    >
      <h2
        class="emotion-12"
      >
        Shop By Category
      </h2>
      <ul>
        <li
          class="emotion-13"
        >
          <a
            aria-label="The Tee Shop"
            class="emotion-20"
            href="/"
          >
            The Tee Shop
          </a>
        </li>
        <li
          class="emotion-13"
        >
          <a
            aria-label="Sleep & Lounge"
            class="emotion-22"
            href="/"
          >
            Sleep & Lounge
          </a>
        </li>
      </ul>
    </div>
  </ul>
</DocumentFragment>
`;

exports[`<CategoryNav /> Snapshots Breakpoints for x-large breakpoint renders default state correctly BananaRepublicFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  padding: 0 0.5rem;
  max-width: 13rem;
  padding-left: 2rem;
  display: block;
  float: left;
  padding-bottom: 1rem;
  margin-right: 0.5rem;
}

.emotion-1 {
  position: absolute;
  height: 1px;
  width: 1px;
  overflow: hidden;
  clip: rect(0 0 0 0);
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  background-color: rgba(255, 255, 255, 0.75);
  top: 0;
  color: #FFF;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  text-align: center;
}

.emotion-1:focus {
  position: relative;
  height: auto;
  width: auto;
  overflow: visible;
  clip: initial;
  z-index: 20;
  left: 0;
}

.emotion-2 {
  width: auto;
  border-radius: 0;
  display: block;
  border: none;
  background-color: #000;
  padding: 1.5rem;
  text-align: center;
  text-transform: uppercase;
}

.emotion-12 {
  font-family: 'Lynstone',Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 700;
  text-transform: uppercase;
  letter-spacing: 0.1rem;
  color: #000;
  line-height: 1.25;
  padding-top: 0.375rem;
  padding-bottom: 0.375rem;
  font-size: 0.8125rem;
}

.emotion-13 {
  font-family: 'Lynstone',Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  display: block;
  color: #000;
  font-size: 0.75rem;
  text-transform: uppercase;
  letter-spacing: 0.1rem;
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
}

.emotion-17 {
  margin-top: 2rem;
}

.emotion-20 {
  color: #D00000;
}

.emotion-20:hover {
  color: #D00000;
}

.emotion-22 {
  font-family: 'Lynstone',Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 700;
  position: relative;
  color: #000;
}

.emotion-22::before {
  content: "";
  position: absolute;
  top: 0;
  left: -1.675rem;
  height: 100%;
  width: 0.25rem;
  background-color: #000;
}

<ul
    class="emotion-0"
    role="navigation"
  >
    <a
      class="emotion-1"
      href="#quick-filters"
    >
      <span
        class="emotion-2"
      >
        left-nav.skip-to-quick-filters
      </span>
    </a>
    <a
      class="emotion-1"
      href="#facets"
    >
      <span
        class="emotion-2"
      >
        left-nav.skip-to-product-filters
      </span>
    </a>
    <a
      class="emotion-1"
      href="#faceted-grid"
    >
      <span
        class="emotion-2"
      >
        left-nav.skip-to-products
      </span>
    </a>
    <a
      class="emotion-1"
      href="/"
    >
      <span
        class="emotion-2"
      >
        left-nav.skip-to New & Now
      </span>
    </a>
    <a
      class="emotion-1"
      href="/"
    >
      <span
        class="emotion-2"
      >
        left-nav.skip-to Shop By Category
      </span>
    </a>
    <div
      class="emotion-11"
    >
      <h2
        class="emotion-12"
      >
        New & Now
      </h2>
      <ul>
        <li
          class="emotion-13"
        >
          <a
            aria-label="New Arrivals"
            class="emotion-14"
            href="/"
          >
            New Arrivals
          </a>
        </li>
        <li
          class="emotion-13"
        >
          <a
            aria-label="On Trend"
            class="emotion-14"
            href="/"
          >
            On Trend
          </a>
        </li>
      </ul>
    </div>
    <div
      class="emotion-17"
    >
      <h2
        class="emotion-12"
      >
        Shop By Category
      </h2>
      <ul>
        <li
          class="emotion-13"
        >
          <a
            aria-label="The Tee Shop"
            class="emotion-20"
            href="/"
          >
            The Tee Shop
          </a>
        </li>
        <li
          class="emotion-13"
        >
          <a
            aria-label="Sleep & Lounge"
            class="emotion-22"
            href="/"
          >
            Sleep & Lounge
          </a>
        </li>
      </ul>
    </div>
  </ul>
</DocumentFragment>
`;

exports[`<CategoryNav /> Snapshots Breakpoints for x-large breakpoint renders default state correctly BananaRepublicFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  padding: 0 0.5rem;
  max-width: 13rem;
  padding-left: 2rem;
  display: block;
  float: left;
  padding-bottom: 1rem;
  margin-right: 0.5rem;
}

.emotion-1 {
  position: absolute;
  height: 1px;
  width: 1px;
  overflow: hidden;
  clip: rect(0 0 0 0);
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  background-color: rgba(255, 255, 255, 0.75);
  top: 0;
  color: #FFF;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  text-align: center;
}

.emotion-1:focus {
  position: relative;
  height: auto;
  width: auto;
  overflow: visible;
  clip: initial;
  z-index: 20;
  left: 0;
}

.emotion-2 {
  width: auto;
  border-radius: 0;
  display: block;
  border: none;
  background-color: #000;
  padding: 1.5rem;
  text-align: center;
  text-transform: uppercase;
}

.emotion-12 {
  font-family: 'Lynstone',Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 700;
  text-transform: uppercase;
  letter-spacing: 0.1rem;
  color: #000;
  line-height: 1.25;
  padding-top: 0.375rem;
  padding-bottom: 0.375rem;
  font-size: 0.8125rem;
}

.emotion-13 {
  font-family: 'Lynstone',Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  display: block;
  color: #000;
  font-size: 0.75rem;
  text-transform: uppercase;
  letter-spacing: 0.1rem;
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
}

.emotion-17 {
  margin-top: 2rem;
}

.emotion-20 {
  color: #D00000;
}

.emotion-20:hover {
  color: #D00000;
}

.emotion-22 {
  font-family: 'Lynstone',Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 700;
  position: relative;
  color: #000;
}

.emotion-22::before {
  content: "";
  position: absolute;
  top: 0;
  left: -1.675rem;
  height: 100%;
  width: 0.25rem;
  background-color: #000;
}

<ul
    class="emotion-0"
    role="navigation"
  >
    <a
      class="emotion-1"
      href="#quick-filters"
    >
      <span
        class="emotion-2"
      >
        left-nav.skip-to-quick-filters
      </span>
    </a>
    <a
      class="emotion-1"
      href="#facets"
    >
      <span
        class="emotion-2"
      >
        left-nav.skip-to-product-filters
      </span>
    </a>
    <a
      class="emotion-1"
      href="#faceted-grid"
    >
      <span
        class="emotion-2"
      >
        left-nav.skip-to-products
      </span>
    </a>
    <a
      class="emotion-1"
      href="/"
    >
      <span
        class="emotion-2"
      >
        left-nav.skip-to New & Now
      </span>
    </a>
    <a
      class="emotion-1"
      href="/"
    >
      <span
        class="emotion-2"
      >
        left-nav.skip-to Shop By Category
      </span>
    </a>
    <div
      class="emotion-11"
    >
      <h2
        class="emotion-12"
      >
        New & Now
      </h2>
      <ul>
        <li
          class="emotion-13"
        >
          <a
            aria-label="New Arrivals"
            class="emotion-14"
            href="/"
          >
            New Arrivals
          </a>
        </li>
        <li
          class="emotion-13"
        >
          <a
            aria-label="On Trend"
            class="emotion-14"
            href="/"
          >
            On Trend
          </a>
        </li>
      </ul>
    </div>
    <div
      class="emotion-17"
    >
      <h2
        class="emotion-12"
      >
        Shop By Category
      </h2>
      <ul>
        <li
          class="emotion-13"
        >
          <a
            aria-label="The Tee Shop"
            class="emotion-20"
            href="/"
          >
            The Tee Shop
          </a>
        </li>
        <li
          class="emotion-13"
        >
          <a
            aria-label="Sleep & Lounge"
            class="emotion-22"
            href="/"
          >
            Sleep & Lounge
          </a>
        </li>
      </ul>
    </div>
  </ul>
</DocumentFragment>
`;

exports[`<CategoryNav /> Snapshots Breakpoints for x-large breakpoint renders default state correctly Gap 1`] = `
<DocumentFragment>
  .emotion-0 {
  padding: 0 0.5rem;
  max-width: 13rem;
  padding-left: 2rem;
  display: block;
  float: left;
  padding-bottom: 1rem;
  margin-right: 0.5rem;
}

.emotion-1 {
  position: absolute;
  height: 1px;
  width: 1px;
  overflow: hidden;
  clip: rect(0 0 0 0);
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  background-color: rgba(255, 255, 255, 0.75);
  top: 0;
  color: #FFF;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  text-align: center;
}

.emotion-1:focus {
  position: relative;
  height: auto;
  width: auto;
  overflow: visible;
  clip: initial;
  z-index: 20;
  left: 0;
}

.emotion-2 {
  width: auto;
  border-radius: 0;
  display: block;
  border: none;
  background-color: #000;
  padding: 1.5rem;
  text-align: center;
  text-transform: uppercase;
}

.emotion-12 {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 700;
  color: #002554;
  font-size: 1.067rem;
  text-transform: capitalize;
  padding-top: 0.375rem;
  padding-bottom: 0.375rem;
}

.emotion-13 {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  display: block;
  color: #666;
  line-height: 1.38;
  font-size: 0.867rem;
  padding-top: 0.375rem;
  padding-bottom: 0.375rem;
}

.emotion-13:hover {
  color: #002554;
}

.emotion-17 {
  margin-top: 0.675rem;
}

.emotion-20 {
  color: #D00000;
}

.emotion-20:hover {
  color: #D00000;
}

.emotion-22 {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 700;
  position: relative;
  color: #002554;
}

.emotion-22::before {
  content: "";
  position: absolute;
  top: 0;
  left: -1.675rem;
  height: 100%;
  width: 0.25rem;
  background-color: #002554;
}

<ul
    class="emotion-0"
    role="navigation"
  >
    <a
      class="emotion-1"
      href="#quick-filters"
    >
      <span
        class="emotion-2"
      >
        left-nav.skip-to-quick-filters
      </span>
    </a>
    <a
      class="emotion-1"
      href="#facets"
    >
      <span
        class="emotion-2"
      >
        left-nav.skip-to-product-filters
      </span>
    </a>
    <a
      class="emotion-1"
      href="#faceted-grid"
    >
      <span
        class="emotion-2"
      >
        left-nav.skip-to-products
      </span>
    </a>
    <a
      class="emotion-1"
      href="/"
    >
      <span
        class="emotion-2"
      >
        left-nav.skip-to New & Now
      </span>
    </a>
    <a
      class="emotion-1"
      href="/"
    >
      <span
        class="emotion-2"
      >
        left-nav.skip-to Shop By Category
      </span>
    </a>
    <div
      class="emotion-11"
    >
      <h2
        class="emotion-12"
      >
        New & Now
      </h2>
      <ul>
        <li
          class="emotion-13"
        >
          <a
            aria-label="New Arrivals"
            class="emotion-14"
            href="/"
          >
            New Arrivals
          </a>
        </li>
        <li
          class="emotion-13"
        >
          <a
            aria-label="On Trend"
            class="emotion-14"
            href="/"
          >
            On Trend
          </a>
        </li>
      </ul>
    </div>
    <div
      class="emotion-17"
    >
      <h2
        class="emotion-12"
      >
        Shop By Category
      </h2>
      <ul>
        <li
          class="emotion-13"
        >
          <a
            aria-label="The Tee Shop"
            class="emotion-20"
            href="/"
          >
            The Tee Shop
          </a>
        </li>
        <li
          class="emotion-13"
        >
          <a
            aria-label="Sleep & Lounge"
            class="emotion-22"
            href="/"
          >
            Sleep & Lounge
          </a>
        </li>
      </ul>
    </div>
  </ul>
</DocumentFragment>
`;

exports[`<CategoryNav /> Snapshots Breakpoints for x-large breakpoint renders default state correctly Gap in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  padding: 0 0.5rem;
  max-width: 13rem;
  padding-left: 2rem;
  display: block;
  float: left;
  padding-bottom: 1rem;
  margin-right: 0.5rem;
}

.emotion-1 {
  position: absolute;
  height: 1px;
  width: 1px;
  overflow: hidden;
  clip: rect(0 0 0 0);
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  background-color: rgba(255, 255, 255, 0.75);
  top: 0;
  color: #FFF;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  text-align: center;
}

.emotion-1:focus {
  position: relative;
  height: auto;
  width: auto;
  overflow: visible;
  clip: initial;
  z-index: 20;
  left: 0;
}

.emotion-2 {
  width: auto;
  border-radius: 0;
  display: block;
  border: none;
  background-color: #000;
  padding: 1.5rem;
  text-align: center;
  text-transform: uppercase;
}

.emotion-12 {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 700;
  color: #002554;
  font-size: 1.067rem;
  text-transform: capitalize;
  padding-top: 0.375rem;
  padding-bottom: 0.375rem;
}

.emotion-13 {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  display: block;
  color: #666;
  line-height: 1.38;
  font-size: 0.867rem;
  padding-top: 0.375rem;
  padding-bottom: 0.375rem;
}

.emotion-13:hover {
  color: #002554;
}

.emotion-17 {
  margin-top: 0.675rem;
}

.emotion-20 {
  color: #D00000;
}

.emotion-20:hover {
  color: #D00000;
}

.emotion-22 {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 700;
  position: relative;
  color: #002554;
}

.emotion-22::before {
  content: "";
  position: absolute;
  top: 0;
  left: -1.675rem;
  height: 100%;
  width: 0.25rem;
  background-color: #002554;
}

<ul
    class="emotion-0"
    role="navigation"
  >
    <a
      class="emotion-1"
      href="#quick-filters"
    >
      <span
        class="emotion-2"
      >
        left-nav.skip-to-quick-filters
      </span>
    </a>
    <a
      class="emotion-1"
      href="#facets"
    >
      <span
        class="emotion-2"
      >
        left-nav.skip-to-product-filters
      </span>
    </a>
    <a
      class="emotion-1"
      href="#faceted-grid"
    >
      <span
        class="emotion-2"
      >
        left-nav.skip-to-products
      </span>
    </a>
    <a
      class="emotion-1"
      href="/"
    >
      <span
        class="emotion-2"
      >
        left-nav.skip-to New & Now
      </span>
    </a>
    <a
      class="emotion-1"
      href="/"
    >
      <span
        class="emotion-2"
      >
        left-nav.skip-to Shop By Category
      </span>
    </a>
    <div
      class="emotion-11"
    >
      <h2
        class="emotion-12"
      >
        New & Now
      </h2>
      <ul>
        <li
          class="emotion-13"
        >
          <a
            aria-label="New Arrivals"
            class="emotion-14"
            href="/"
          >
            New Arrivals
          </a>
        </li>
        <li
          class="emotion-13"
        >
          <a
            aria-label="On Trend"
            class="emotion-14"
            href="/"
          >
            On Trend
          </a>
        </li>
      </ul>
    </div>
    <div
      class="emotion-17"
    >
      <h2
        class="emotion-12"
      >
        Shop By Category
      </h2>
      <ul>
        <li
          class="emotion-13"
        >
          <a
            aria-label="The Tee Shop"
            class="emotion-20"
            href="/"
          >
            The Tee Shop
          </a>
        </li>
        <li
          class="emotion-13"
        >
          <a
            aria-label="Sleep & Lounge"
            class="emotion-22"
            href="/"
          >
            Sleep & Lounge
          </a>
        </li>
      </ul>
    </div>
  </ul>
</DocumentFragment>
`;

exports[`<CategoryNav /> Snapshots Breakpoints for x-large breakpoint renders default state correctly GapFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  padding: 0 0.5rem;
  max-width: 13rem;
  padding-left: 2rem;
  display: block;
  float: left;
  padding-bottom: 1rem;
  margin-right: 0.5rem;
}

.emotion-1 {
  position: absolute;
  height: 1px;
  width: 1px;
  overflow: hidden;
  clip: rect(0 0 0 0);
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  background-color: rgba(255, 255, 255, 0.75);
  top: 0;
  color: #FFF;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  text-align: center;
}

.emotion-1:focus {
  position: relative;
  height: auto;
  width: auto;
  overflow: visible;
  clip: initial;
  z-index: 20;
  left: 0;
}

.emotion-2 {
  width: auto;
  border-radius: 0;
  display: block;
  border: none;
  background-color: #000;
  padding: 1.5rem;
  text-align: center;
  text-transform: uppercase;
}

.emotion-12 {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 700;
  color: #002554;
  font-size: 1.067rem;
  text-transform: capitalize;
  padding-top: 0.375rem;
  padding-bottom: 0.375rem;
}

.emotion-13 {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  display: block;
  color: #666;
  line-height: 1.38;
  font-size: 0.867rem;
  padding-top: 0.375rem;
  padding-bottom: 0.375rem;
}

.emotion-13:hover {
  color: #002554;
}

.emotion-17 {
  margin-top: 0.675rem;
}

.emotion-20 {
  color: #D00000;
}

.emotion-20:hover {
  color: #D00000;
}

.emotion-22 {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 700;
  position: relative;
  color: #002554;
}

.emotion-22::before {
  content: "";
  position: absolute;
  top: 0;
  left: -1.675rem;
  height: 100%;
  width: 0.25rem;
  background-color: #002554;
}

<ul
    class="emotion-0"
    role="navigation"
  >
    <a
      class="emotion-1"
      href="#quick-filters"
    >
      <span
        class="emotion-2"
      >
        left-nav.skip-to-quick-filters
      </span>
    </a>
    <a
      class="emotion-1"
      href="#facets"
    >
      <span
        class="emotion-2"
      >
        left-nav.skip-to-product-filters
      </span>
    </a>
    <a
      class="emotion-1"
      href="#faceted-grid"
    >
      <span
        class="emotion-2"
      >
        left-nav.skip-to-products
      </span>
    </a>
    <a
      class="emotion-1"
      href="/"
    >
      <span
        class="emotion-2"
      >
        left-nav.skip-to New & Now
      </span>
    </a>
    <a
      class="emotion-1"
      href="/"
    >
      <span
        class="emotion-2"
      >
        left-nav.skip-to Shop By Category
      </span>
    </a>
    <div
      class="emotion-11"
    >
      <h2
        class="emotion-12"
      >
        New & Now
      </h2>
      <ul>
        <li
          class="emotion-13"
        >
          <a
            aria-label="New Arrivals"
            class="emotion-14"
            href="/"
          >
            New Arrivals
          </a>
        </li>
        <li
          class="emotion-13"
        >
          <a
            aria-label="On Trend"
            class="emotion-14"
            href="/"
          >
            On Trend
          </a>
        </li>
      </ul>
    </div>
    <div
      class="emotion-17"
    >
      <h2
        class="emotion-12"
      >
        Shop By Category
      </h2>
      <ul>
        <li
          class="emotion-13"
        >
          <a
            aria-label="The Tee Shop"
            class="emotion-20"
            href="/"
          >
            The Tee Shop
          </a>
        </li>
        <li
          class="emotion-13"
        >
          <a
            aria-label="Sleep & Lounge"
            class="emotion-22"
            href="/"
          >
            Sleep & Lounge
          </a>
        </li>
      </ul>
    </div>
  </ul>
</DocumentFragment>
`;

exports[`<CategoryNav /> Snapshots Breakpoints for x-large breakpoint renders default state correctly GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  padding: 0 0.5rem;
  max-width: 13rem;
  padding-left: 2rem;
  display: block;
  float: left;
  padding-bottom: 1rem;
  margin-right: 0.5rem;
}

.emotion-1 {
  position: absolute;
  height: 1px;
  width: 1px;
  overflow: hidden;
  clip: rect(0 0 0 0);
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  background-color: rgba(255, 255, 255, 0.75);
  top: 0;
  color: #FFF;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  text-align: center;
}

.emotion-1:focus {
  position: relative;
  height: auto;
  width: auto;
  overflow: visible;
  clip: initial;
  z-index: 20;
  left: 0;
}

.emotion-2 {
  width: auto;
  border-radius: 0;
  display: block;
  border: none;
  background-color: #000;
  padding: 1.5rem;
  text-align: center;
  text-transform: uppercase;
}

.emotion-12 {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 700;
  color: #002554;
  font-size: 1.067rem;
  text-transform: capitalize;
  padding-top: 0.375rem;
  padding-bottom: 0.375rem;
}

.emotion-13 {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  display: block;
  color: #666;
  line-height: 1.38;
  font-size: 0.867rem;
  padding-top: 0.375rem;
  padding-bottom: 0.375rem;
}

.emotion-13:hover {
  color: #002554;
}

.emotion-17 {
  margin-top: 0.675rem;
}

.emotion-20 {
  color: #D00000;
}

.emotion-20:hover {
  color: #D00000;
}

.emotion-22 {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 700;
  position: relative;
  color: #002554;
}

.emotion-22::before {
  content: "";
  position: absolute;
  top: 0;
  left: -1.675rem;
  height: 100%;
  width: 0.25rem;
  background-color: #002554;
}

<ul
    class="emotion-0"
    role="navigation"
  >
    <a
      class="emotion-1"
      href="#quick-filters"
    >
      <span
        class="emotion-2"
      >
        left-nav.skip-to-quick-filters
      </span>
    </a>
    <a
      class="emotion-1"
      href="#facets"
    >
      <span
        class="emotion-2"
      >
        left-nav.skip-to-product-filters
      </span>
    </a>
    <a
      class="emotion-1"
      href="#faceted-grid"
    >
      <span
        class="emotion-2"
      >
        left-nav.skip-to-products
      </span>
    </a>
    <a
      class="emotion-1"
      href="/"
    >
      <span
        class="emotion-2"
      >
        left-nav.skip-to New & Now
      </span>
    </a>
    <a
      class="emotion-1"
      href="/"
    >
      <span
        class="emotion-2"
      >
        left-nav.skip-to Shop By Category
      </span>
    </a>
    <div
      class="emotion-11"
    >
      <h2
        class="emotion-12"
      >
        New & Now
      </h2>
      <ul>
        <li
          class="emotion-13"
        >
          <a
            aria-label="New Arrivals"
            class="emotion-14"
            href="/"
          >
            New Arrivals
          </a>
        </li>
        <li
          class="emotion-13"
        >
          <a
            aria-label="On Trend"
            class="emotion-14"
            href="/"
          >
            On Trend
          </a>
        </li>
      </ul>
    </div>
    <div
      class="emotion-17"
    >
      <h2
        class="emotion-12"
      >
        Shop By Category
      </h2>
      <ul>
        <li
          class="emotion-13"
        >
          <a
            aria-label="The Tee Shop"
            class="emotion-20"
            href="/"
          >
            The Tee Shop
          </a>
        </li>
        <li
          class="emotion-13"
        >
          <a
            aria-label="Sleep & Lounge"
            class="emotion-22"
            href="/"
          >
            Sleep & Lounge
          </a>
        </li>
      </ul>
    </div>
  </ul>
</DocumentFragment>
`;

exports[`<CategoryNav /> Snapshots Breakpoints for x-large breakpoint renders default state correctly OldNavy 1`] = `
<DocumentFragment>
  .emotion-0 {
  padding: 0 0.5rem;
  max-width: 13rem;
  padding-left: 2rem;
  display: block;
  float: left;
  padding-bottom: 1rem;
  margin-right: 0.5rem;
}

.emotion-1 {
  position: absolute;
  height: 1px;
  width: 1px;
  overflow: hidden;
  clip: rect(0 0 0 0);
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  background-color: rgba(255, 255, 255, 0.75);
  top: 0;
  color: #FFF;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  text-align: center;
}

.emotion-1:focus {
  position: relative;
  height: auto;
  width: auto;
  overflow: visible;
  clip: initial;
  z-index: 20;
  left: 0;
}

.emotion-2 {
  width: auto;
  border-radius: 0;
  display: block;
  border: none;
  background-color: #000;
  padding: 1.5rem;
  text-align: center;
  text-transform: uppercase;
}

.emotion-12 {
  font-family: 'Gotham','Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 700;
  font-size: 0.9rem;
  color: #666;
  text-transform: capitalize;
  padding-top: 0.3125rem;
  padding-bottom: 0.3125rem;
}

.emotion-13 {
  font-family: 'Gotham','Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  display: block;
  color: #666;
  font-size: 0.8rem;
  text-transform: capitalize;
  padding-top: 0.375rem;
  padding-bottom: 0.375rem;
}

.emotion-13:hover {
  color: #003764;
}

.emotion-17 {
  margin-top: 2rem;
}

.emotion-20 {
  color: #D00000;
}

.emotion-20:hover {
  color: #D00000;
}

.emotion-22 {
  font-family: 'Gotham','Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 700;
  position: relative;
  color: #003764;
}

.emotion-22::before {
  content: "";
  position: absolute;
  top: 0;
  left: -1.675rem;
  height: 100%;
  width: 0.25rem;
  background-color: #003764;
}

<ul
    class="emotion-0"
    role="navigation"
  >
    <a
      class="emotion-1"
      href="#quick-filters"
    >
      <span
        class="emotion-2"
      >
        left-nav.skip-to-quick-filters
      </span>
    </a>
    <a
      class="emotion-1"
      href="#facets"
    >
      <span
        class="emotion-2"
      >
        left-nav.skip-to-product-filters
      </span>
    </a>
    <a
      class="emotion-1"
      href="#faceted-grid"
    >
      <span
        class="emotion-2"
      >
        left-nav.skip-to-products
      </span>
    </a>
    <a
      class="emotion-1"
      href="/"
    >
      <span
        class="emotion-2"
      >
        left-nav.skip-to New & Now
      </span>
    </a>
    <a
      class="emotion-1"
      href="/"
    >
      <span
        class="emotion-2"
      >
        left-nav.skip-to Shop By Category
      </span>
    </a>
    <div
      class="emotion-11"
    >
      <h2
        class="emotion-12"
      >
        New & Now
      </h2>
      <ul>
        <li
          class="emotion-13"
        >
          <a
            aria-label="New Arrivals"
            class="emotion-14"
            href="/"
          >
            New Arrivals
          </a>
        </li>
        <li
          class="emotion-13"
        >
          <a
            aria-label="On Trend"
            class="emotion-14"
            href="/"
          >
            On Trend
          </a>
        </li>
      </ul>
    </div>
    <div
      class="emotion-17"
    >
      <h2
        class="emotion-12"
      >
        Shop By Category
      </h2>
      <ul>
        <li
          class="emotion-13"
        >
          <a
            aria-label="The Tee Shop"
            class="emotion-20"
            href="/"
          >
            The Tee Shop
          </a>
        </li>
        <li
          class="emotion-13"
        >
          <a
            aria-label="Sleep & Lounge"
            class="emotion-22"
            href="/"
          >
            Sleep & Lounge
          </a>
        </li>
      </ul>
    </div>
  </ul>
</DocumentFragment>
`;

exports[`<CategoryNav /> Snapshots Breakpoints for x-large breakpoint renders default state correctly OldNavy in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  padding: 0 0.5rem;
  max-width: 13rem;
  padding-left: 2rem;
  display: block;
  float: left;
  padding-bottom: 1rem;
  margin-right: 0.5rem;
}

.emotion-1 {
  position: absolute;
  height: 1px;
  width: 1px;
  overflow: hidden;
  clip: rect(0 0 0 0);
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  background-color: rgba(255, 255, 255, 0.75);
  top: 0;
  color: #FFF;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  text-align: center;
}

.emotion-1:focus {
  position: relative;
  height: auto;
  width: auto;
  overflow: visible;
  clip: initial;
  z-index: 20;
  left: 0;
}

.emotion-2 {
  width: auto;
  border-radius: 0;
  display: block;
  border: none;
  background-color: #000;
  padding: 1.5rem;
  text-align: center;
  text-transform: uppercase;
}

.emotion-12 {
  font-family: 'Gotham','Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 700;
  font-size: 0.9rem;
  color: #666;
  text-transform: capitalize;
  padding-top: 0.3125rem;
  padding-bottom: 0.3125rem;
}

.emotion-13 {
  font-family: 'Gotham','Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  display: block;
  color: #666;
  font-size: 0.8rem;
  text-transform: capitalize;
  padding-top: 0.375rem;
  padding-bottom: 0.375rem;
}

.emotion-13:hover {
  color: #003764;
}

.emotion-17 {
  margin-top: 2rem;
}

.emotion-20 {
  color: #D00000;
}

.emotion-20:hover {
  color: #D00000;
}

.emotion-22 {
  font-family: 'Gotham','Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 700;
  position: relative;
  color: #003764;
}

.emotion-22::before {
  content: "";
  position: absolute;
  top: 0;
  left: -1.675rem;
  height: 100%;
  width: 0.25rem;
  background-color: #003764;
}

<ul
    class="emotion-0"
    role="navigation"
  >
    <a
      class="emotion-1"
      href="#quick-filters"
    >
      <span
        class="emotion-2"
      >
        left-nav.skip-to-quick-filters
      </span>
    </a>
    <a
      class="emotion-1"
      href="#facets"
    >
      <span
        class="emotion-2"
      >
        left-nav.skip-to-product-filters
      </span>
    </a>
    <a
      class="emotion-1"
      href="#faceted-grid"
    >
      <span
        class="emotion-2"
      >
        left-nav.skip-to-products
      </span>
    </a>
    <a
      class="emotion-1"
      href="/"
    >
      <span
        class="emotion-2"
      >
        left-nav.skip-to New & Now
      </span>
    </a>
    <a
      class="emotion-1"
      href="/"
    >
      <span
        class="emotion-2"
      >
        left-nav.skip-to Shop By Category
      </span>
    </a>
    <div
      class="emotion-11"
    >
      <h2
        class="emotion-12"
      >
        New & Now
      </h2>
      <ul>
        <li
          class="emotion-13"
        >
          <a
            aria-label="New Arrivals"
            class="emotion-14"
            href="/"
          >
            New Arrivals
          </a>
        </li>
        <li
          class="emotion-13"
        >
          <a
            aria-label="On Trend"
            class="emotion-14"
            href="/"
          >
            On Trend
          </a>
        </li>
      </ul>
    </div>
    <div
      class="emotion-17"
    >
      <h2
        class="emotion-12"
      >
        Shop By Category
      </h2>
      <ul>
        <li
          class="emotion-13"
        >
          <a
            aria-label="The Tee Shop"
            class="emotion-20"
            href="/"
          >
            The Tee Shop
          </a>
        </li>
        <li
          class="emotion-13"
        >
          <a
            aria-label="Sleep & Lounge"
            class="emotion-22"
            href="/"
          >
            Sleep & Lounge
          </a>
        </li>
      </ul>
    </div>
  </ul>
</DocumentFragment>
`;
