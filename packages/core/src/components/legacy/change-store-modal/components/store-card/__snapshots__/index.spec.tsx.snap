// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`<StoreCard /> snapshots renders default selected state correctly Athleta 1`] = `
<DocumentFragment>
  .emotion-0 {
  border-bottom: 1px solid #CCC;
  padding: 1rem 0;
  font-size: 0.875rem;
  color: #666;
  line-height: 1.38;
  font-weight: 400;
}

.emotion-1 {
  float: left;
  padding-right: 0.5rem;
  width: 36%;
}

@media (min-width: 320px) {
  .emotion-1 {
    width: 38%;
  }
}

@media (min-width: 350px) {
  .emotion-1 {
    width: 43%;
  }
}

@media (min-width: 375px) {
  .emotion-1 {
    width: 48%;
  }
}

@media (min-width: 415px) {
  .emotion-1 {
    width: 54%;
  }
}

@media (min-width: 475px) {
  .emotion-1 {
    width: 58%;
  }
}

@media (min-width: 500px) {
  .emotion-1 {
    width: 60%;
  }
}

.emotion-2 {
  white-space: nowrap;
  line-height: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  text-transform: uppercase;
  font-size: 1rem;
  color: #333;
}

.emotion-2 fontSet {
  font-family: 'Avenir Next','Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-4 {
  float: right;
  width: 155px;
  font-size: 1rem;
}

.emotion-5 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 1.5px solid transparent;
  border-radius: 0;
  font-size: 16px;
  font-weight: 500;
  letter-spacing: 0px;
  min-height: auto;
  max-height: 60px;
  line-height: 1.25;
  padding: 22px 40px;
  width: 100%;
  font-family: 'Avenir Next','Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #FFF;
  background-color: #000;
  border-color: #000;
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  box-sizing: border-box;
  cursor: pointer;
  text-align: center;
  text-transform: uppercase;
  -webkit-transition: all 200ms ease;
  transition: all 200ms ease;
  height: 2.75rem;
  background-color: #333;
  border: none;
  color: #FFF;
  font-size: 1.1875rem;
  font-weight: 600;
  letter-spacing: 1px;
  line-height: normal;
  padding: 0.0625em 0.52em 0.125em;
  color: #333;
  padding: 0.3rem;
  border: 1px solid #333;
  background-color: #FFF;
  font-size: 81.25%;
  font-weight: 700;
  letter-spacing: 0px;
  border-radius: 0px;
  text-align: center;
  min-height: 0px;
  height: 2.0625rem;
}

.emotion-5:focus {
  outline: none;
}

.emotion-5>span {
  padding: 1px 0;
}

.emotion-5:disabled,
.emotion-5:disabled:hover {
  cursor: default;
}

.emotion-5:hover {
  background-color: #666;
  border-color: transparent;
  color: #FFF;
}

.emotion-5:disabled,
.emotion-5:disabled:hover {
  background-color: #CCC;
  border-color: transparent;
  color: #FFF;
}

.emotion-5:hover {
  background-color: #FFF;
  color: #666;
  border-color: #666;
}

.emotion-6 {
  clear: both;
  display: block;
  padding-top: 0.5rem;
  line-height: 1;
  width: 100%;
  text-transform: capitalize;
}

.emotion-7 {
  position: absolute;
  left: -999em;
  top: -999em;
}

<div
    class="emotion-0"
  >
    <div
      class="emotion-1"
    >
      <h2
        class="emotion-2"
      >
        <a
          aria-label=""
          class="map-link"
          href="https://www.google.com/maps/dir/?api=1&destination=150 N. State St.,CHICAGO,IL,60601"
          rel="noopener noreferrer"
          target="_blank"
        >
          150 N. STATE ST.
        </a>
      </h2>
      <div
        class="emotion-3"
      >
        (
        <span>
          0.0
        </span>
         Mi)
      </div>
    </div>
    <div
      class="emotion-4"
    >
      <button
        class="emotion-5"
      >
        changeStoreModal.selectedButton
      </button>
    </div>
    <div
      class="emotion-6"
    >
      <span>
        150 n. state st., chicago, IL, 60601
      </span>
      <div
        class="emotion-7"
      >
        <span
          id="storeDescription-3997"
        >
          150 N. STATE ST.. Address: 150 n. state st., chicago, IL, 60601 0.0 miles away
        </span>
      </div>
    </div>
  </div>
</DocumentFragment>
`;

exports[`<StoreCard /> snapshots renders default selected state correctly Athleta in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  border-bottom: 1px solid #CCC;
  padding: 1rem 0;
  font-size: 0.875rem;
  color: #666;
  line-height: 1.38;
  font-weight: 400;
}

.emotion-1 {
  float: left;
  padding-right: 0.5rem;
  width: 36%;
}

@media (min-width: 320px) {
  .emotion-1 {
    width: 38%;
  }
}

@media (min-width: 350px) {
  .emotion-1 {
    width: 43%;
  }
}

@media (min-width: 375px) {
  .emotion-1 {
    width: 48%;
  }
}

@media (min-width: 415px) {
  .emotion-1 {
    width: 54%;
  }
}

@media (min-width: 475px) {
  .emotion-1 {
    width: 58%;
  }
}

@media (min-width: 500px) {
  .emotion-1 {
    width: 60%;
  }
}

.emotion-2 {
  white-space: nowrap;
  line-height: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  text-transform: uppercase;
  font-size: 1rem;
  color: #333;
}

.emotion-2 fontSet {
  font-family: 'Avenir Next','Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-4 {
  float: right;
  width: 155px;
  font-size: 1rem;
}

.emotion-5 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 1.5px solid transparent;
  border-radius: 0;
  font-size: 16px;
  font-weight: 500;
  letter-spacing: 0px;
  min-height: auto;
  max-height: 60px;
  line-height: 1.25;
  padding: 22px 40px;
  width: 100%;
  font-family: 'Avenir Next','Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #FFF;
  background-color: #000;
  border-color: #000;
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  box-sizing: border-box;
  cursor: pointer;
  text-align: center;
  text-transform: uppercase;
  -webkit-transition: all 200ms ease;
  transition: all 200ms ease;
  height: 2.75rem;
  background-color: #333;
  border: none;
  color: #FFF;
  font-size: 1.1875rem;
  font-weight: 600;
  letter-spacing: 1px;
  line-height: normal;
  padding: 0.0625em 0.52em 0.125em;
  color: #333;
  padding: 0.3rem;
  border: 1px solid #333;
  background-color: #FFF;
  font-size: 81.25%;
  font-weight: 700;
  letter-spacing: 0px;
  border-radius: 0px;
  text-align: center;
  min-height: 0px;
  height: 2.0625rem;
}

.emotion-5:focus {
  outline: none;
}

.emotion-5>span {
  padding: 1px 0;
}

.emotion-5:disabled,
.emotion-5:disabled:hover {
  cursor: default;
}

.emotion-5:hover {
  background-color: #666;
  border-color: transparent;
  color: #FFF;
}

.emotion-5:disabled,
.emotion-5:disabled:hover {
  background-color: #CCC;
  border-color: transparent;
  color: #FFF;
}

.emotion-5:hover {
  background-color: #FFF;
  color: #666;
  border-color: #666;
}

.emotion-6 {
  clear: both;
  display: block;
  padding-top: 0.5rem;
  line-height: 1;
  width: 100%;
  text-transform: capitalize;
}

.emotion-7 {
  position: absolute;
  left: -999em;
  top: -999em;
}

<div
    class="emotion-0"
  >
    <div
      class="emotion-1"
    >
      <h2
        class="emotion-2"
      >
        <a
          aria-label=""
          class="map-link"
          href="https://www.google.com/maps/dir/?api=1&destination=150 N. State St.,CHICAGO,IL,60601"
          rel="noopener noreferrer"
          target="_blank"
        >
          150 N. STATE ST.
        </a>
      </h2>
      <div
        class="emotion-3"
      >
        (
        <span>
          0.0
        </span>
         Mi)
      </div>
    </div>
    <div
      class="emotion-4"
    >
      <button
        class="emotion-5"
      >
        changeStoreModal.selectedButton
      </button>
    </div>
    <div
      class="emotion-6"
    >
      <span>
        150 n. state st., chicago, IL, 60601
      </span>
      <div
        class="emotion-7"
      >
        <span
          id="storeDescription-3997"
        >
          150 N. STATE ST.. Address: 150 n. state st., chicago, IL, 60601 0.0 miles away
        </span>
      </div>
    </div>
  </div>
</DocumentFragment>
`;

exports[`<StoreCard /> snapshots renders default selected state correctly BananaRepublic 1`] = `
<DocumentFragment>
  .emotion-0 {
  border-bottom: 1px solid #CCC;
  padding: 1rem 0;
  font-size: 0.875rem;
  color: #666;
  line-height: 1.38;
  font-weight: 400;
}

.emotion-1 {
  float: left;
  padding-right: 0.5rem;
  width: 36%;
}

@media (min-width: 320px) {
  .emotion-1 {
    width: 38%;
  }
}

@media (min-width: 350px) {
  .emotion-1 {
    width: 43%;
  }
}

@media (min-width: 375px) {
  .emotion-1 {
    width: 48%;
  }
}

@media (min-width: 415px) {
  .emotion-1 {
    width: 54%;
  }
}

@media (min-width: 475px) {
  .emotion-1 {
    width: 58%;
  }
}

@media (min-width: 500px) {
  .emotion-1 {
    width: 60%;
  }
}

.emotion-2 {
  white-space: nowrap;
  line-height: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  text-transform: uppercase;
  font-size: 1rem;
  color: #000;
}

.emotion-2 fontSet {
  font-family: 'Lynstone',Helvetica,Arial,sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-4 {
  float: right;
  width: 155px;
  font-size: 1rem;
}

.emotion-5 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 0;
  text-align: center;
  display: block;
  width: 100%;
  padding: 0.5em 0.8em;
  font-size: 1rem;
  font-family: 'Lynstone',Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 600;
  color: #FFF;
  background-color: #000;
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  box-sizing: border-box;
  cursor: pointer;
  text-align: center;
  text-transform: uppercase;
  -webkit-transition: all 200ms ease;
  transition: all 200ms ease;
  height: 2.75rem;
  background-color: #000;
  border: none;
  color: #FFF;
  font-size: 1.0625rem;
  font-weight: 600;
  letter-spacing: 2px;
  line-height: normal;
  padding: 0.53em;
  color: #FFF;
  padding: 0.3rem;
  border: inherited;
  background-color: #000;
  font-size: 81.25%;
  font-weight: 700;
  letter-spacing: 0px;
  border-radius: 0px;
  text-align: center;
  min-height: 0px;
  height: 2.0625rem;
}

.emotion-5:focus {
  outline: none;
}

.emotion-5:disabled,
.emotion-5:disabled:hover {
  cursor: default;
}

.emotion-5:hover {
  background-color: #3f3f3f;
  border-color: transparent;
  color: #FFF;
}

.emotion-5:disabled,
.emotion-5:disabled:hover {
  background-color: #CCC;
  border-color: transparent;
  color: #FFF;
}

.emotion-5:hover {
  background-color: #666;
  color: #FFF;
}

.emotion-6 {
  clear: both;
  display: block;
  padding-top: 0.5rem;
  line-height: 1;
  width: 100%;
  text-transform: capitalize;
}

.emotion-7 {
  position: absolute;
  left: -999em;
  top: -999em;
}

<div
    class="emotion-0"
  >
    <div
      class="emotion-1"
    >
      <h2
        class="emotion-2"
      >
        <a
          aria-label=""
          class="map-link"
          href="https://www.google.com/maps/dir/?api=1&destination=150 N. State St.,CHICAGO,IL,60601"
          rel="noopener noreferrer"
          target="_blank"
        >
          150 N. STATE ST.
        </a>
      </h2>
      <div
        class="emotion-3"
      >
        (
        <span>
          0.0
        </span>
         Mi)
      </div>
    </div>
    <div
      class="emotion-4"
    >
      <button
        class="emotion-5"
      >
        changeStoreModal.selectedButton
      </button>
    </div>
    <div
      class="emotion-6"
    >
      <span>
        150 n. state st., chicago, IL, 60601
      </span>
      <div
        class="emotion-7"
      >
        <span
          id="storeDescription-3997"
        >
          150 N. STATE ST.. Address: 150 n. state st., chicago, IL, 60601 0.0 miles away
        </span>
      </div>
    </div>
  </div>
</DocumentFragment>
`;

exports[`<StoreCard /> snapshots renders default selected state correctly BananaRepublic in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  border-bottom: 1px solid #CCC;
  padding: 1rem 0;
  font-size: 0.875rem;
  color: #666;
  line-height: 1.38;
  font-weight: 400;
}

.emotion-1 {
  float: left;
  padding-right: 0.5rem;
  width: 36%;
}

@media (min-width: 320px) {
  .emotion-1 {
    width: 38%;
  }
}

@media (min-width: 350px) {
  .emotion-1 {
    width: 43%;
  }
}

@media (min-width: 375px) {
  .emotion-1 {
    width: 48%;
  }
}

@media (min-width: 415px) {
  .emotion-1 {
    width: 54%;
  }
}

@media (min-width: 475px) {
  .emotion-1 {
    width: 58%;
  }
}

@media (min-width: 500px) {
  .emotion-1 {
    width: 60%;
  }
}

.emotion-2 {
  white-space: nowrap;
  line-height: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  text-transform: uppercase;
  font-size: 1rem;
  color: #000;
}

.emotion-2 fontSet {
  font-family: 'Lynstone',Helvetica,Arial,sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-4 {
  float: right;
  width: 155px;
  font-size: 1rem;
}

.emotion-5 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 0;
  text-align: center;
  display: block;
  width: 100%;
  padding: 0.5em 0.8em;
  font-size: 1rem;
  font-family: 'Lynstone',Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 600;
  color: #FFF;
  background-color: #000;
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  box-sizing: border-box;
  cursor: pointer;
  text-align: center;
  text-transform: uppercase;
  -webkit-transition: all 200ms ease;
  transition: all 200ms ease;
  height: 2.75rem;
  background-color: #000;
  border: none;
  color: #FFF;
  font-size: 1.0625rem;
  font-weight: 600;
  letter-spacing: 2px;
  line-height: normal;
  padding: 0.53em;
  color: #FFF;
  padding: 0.3rem;
  border: inherited;
  background-color: #000;
  font-size: 81.25%;
  font-weight: 700;
  letter-spacing: 0px;
  border-radius: 0px;
  text-align: center;
  min-height: 0px;
  height: 2.0625rem;
}

.emotion-5:focus {
  outline: none;
}

.emotion-5:disabled,
.emotion-5:disabled:hover {
  cursor: default;
}

.emotion-5:hover {
  background-color: #3f3f3f;
  border-color: transparent;
  color: #FFF;
}

.emotion-5:disabled,
.emotion-5:disabled:hover {
  background-color: #CCC;
  border-color: transparent;
  color: #FFF;
}

.emotion-5:hover {
  background-color: #666;
  color: #FFF;
}

.emotion-6 {
  clear: both;
  display: block;
  padding-top: 0.5rem;
  line-height: 1;
  width: 100%;
  text-transform: capitalize;
}

.emotion-7 {
  position: absolute;
  left: -999em;
  top: -999em;
}

<div
    class="emotion-0"
  >
    <div
      class="emotion-1"
    >
      <h2
        class="emotion-2"
      >
        <a
          aria-label=""
          class="map-link"
          href="https://www.google.com/maps/dir/?api=1&destination=150 N. State St.,CHICAGO,IL,60601"
          rel="noopener noreferrer"
          target="_blank"
        >
          150 N. STATE ST.
        </a>
      </h2>
      <div
        class="emotion-3"
      >
        (
        <span>
          0.0
        </span>
         Mi)
      </div>
    </div>
    <div
      class="emotion-4"
    >
      <button
        class="emotion-5"
      >
        changeStoreModal.selectedButton
      </button>
    </div>
    <div
      class="emotion-6"
    >
      <span>
        150 n. state st., chicago, IL, 60601
      </span>
      <div
        class="emotion-7"
      >
        <span
          id="storeDescription-3997"
        >
          150 N. STATE ST.. Address: 150 n. state st., chicago, IL, 60601 0.0 miles away
        </span>
      </div>
    </div>
  </div>
</DocumentFragment>
`;

exports[`<StoreCard /> snapshots renders default selected state correctly BananaRepublicFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  border-bottom: 1px solid #CCC;
  padding: 1rem 0;
  font-size: 0.875rem;
  color: #666;
  line-height: 1.38;
  font-weight: 400;
}

.emotion-1 {
  float: left;
  padding-right: 0.5rem;
  width: 36%;
}

@media (min-width: 320px) {
  .emotion-1 {
    width: 38%;
  }
}

@media (min-width: 350px) {
  .emotion-1 {
    width: 43%;
  }
}

@media (min-width: 375px) {
  .emotion-1 {
    width: 48%;
  }
}

@media (min-width: 415px) {
  .emotion-1 {
    width: 54%;
  }
}

@media (min-width: 475px) {
  .emotion-1 {
    width: 58%;
  }
}

@media (min-width: 500px) {
  .emotion-1 {
    width: 60%;
  }
}

.emotion-2 {
  white-space: nowrap;
  line-height: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  text-transform: uppercase;
  font-size: 1rem;
  color: #000;
}

.emotion-2 fontSet {
  font-family: 'Lynstone',Helvetica,Arial,sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-4 {
  float: right;
  width: 155px;
  font-size: 1rem;
}

.emotion-5 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 0;
  text-align: center;
  display: block;
  width: 100%;
  padding: 0.5em 0.8em;
  font-size: 1rem;
  font-family: 'Lynstone',Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 700;
  color: #FFF;
  background-color: #000;
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  box-sizing: border-box;
  cursor: pointer;
  text-align: center;
  text-transform: uppercase;
  -webkit-transition: all 200ms ease;
  transition: all 200ms ease;
  height: 2.75rem;
  background-color: #000;
  border: none;
  color: #FFF;
  font-size: 1.0625rem;
  font-weight: 600;
  letter-spacing: 2px;
  line-height: normal;
  padding: 0.53em;
  color: #FFF;
  padding: 0.3rem;
  border: inherited;
  background-color: #000;
  font-size: 81.25%;
  font-weight: 700;
  letter-spacing: 0px;
  border-radius: 0px;
  text-align: center;
  min-height: 0px;
  height: 2.0625rem;
}

.emotion-5:focus {
  outline: none;
}

.emotion-5:disabled,
.emotion-5:disabled:hover {
  cursor: default;
}

.emotion-5:hover {
  background-color: #3f3f3f;
  border-color: transparent;
  color: #FFF;
}

.emotion-5:disabled,
.emotion-5:disabled:hover {
  background-color: #CCC;
  border-color: transparent;
  color: #FFF;
}

.emotion-5:hover {
  background-color: #666;
  color: #FFF;
}

.emotion-6 {
  clear: both;
  display: block;
  padding-top: 0.5rem;
  line-height: 1;
  width: 100%;
  text-transform: capitalize;
}

.emotion-7 {
  position: absolute;
  left: -999em;
  top: -999em;
}

<div
    class="emotion-0"
  >
    <div
      class="emotion-1"
    >
      <h2
        class="emotion-2"
      >
        <a
          aria-label=""
          class="map-link"
          href="https://www.google.com/maps/dir/?api=1&destination=150 N. State St.,CHICAGO,IL,60601"
          rel="noopener noreferrer"
          target="_blank"
        >
          150 N. STATE ST.
        </a>
      </h2>
      <div
        class="emotion-3"
      >
        (
        <span>
          0.0
        </span>
         Mi)
      </div>
    </div>
    <div
      class="emotion-4"
    >
      <button
        class="emotion-5"
      >
        changeStoreModal.selectedButton
      </button>
    </div>
    <div
      class="emotion-6"
    >
      <span>
        150 n. state st., chicago, IL, 60601
      </span>
      <div
        class="emotion-7"
      >
        <span
          id="storeDescription-3997"
        >
          150 N. STATE ST.. Address: 150 n. state st., chicago, IL, 60601 0.0 miles away
        </span>
      </div>
    </div>
  </div>
</DocumentFragment>
`;

exports[`<StoreCard /> snapshots renders default selected state correctly BananaRepublicFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  border-bottom: 1px solid #CCC;
  padding: 1rem 0;
  font-size: 0.875rem;
  color: #666;
  line-height: 1.38;
  font-weight: 400;
}

.emotion-1 {
  float: left;
  padding-right: 0.5rem;
  width: 36%;
}

@media (min-width: 320px) {
  .emotion-1 {
    width: 38%;
  }
}

@media (min-width: 350px) {
  .emotion-1 {
    width: 43%;
  }
}

@media (min-width: 375px) {
  .emotion-1 {
    width: 48%;
  }
}

@media (min-width: 415px) {
  .emotion-1 {
    width: 54%;
  }
}

@media (min-width: 475px) {
  .emotion-1 {
    width: 58%;
  }
}

@media (min-width: 500px) {
  .emotion-1 {
    width: 60%;
  }
}

.emotion-2 {
  white-space: nowrap;
  line-height: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  text-transform: uppercase;
  font-size: 1rem;
  color: #000;
}

.emotion-2 fontSet {
  font-family: 'Lynstone',Helvetica,Arial,sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-4 {
  float: right;
  width: 155px;
  font-size: 1rem;
}

.emotion-5 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 0;
  text-align: center;
  display: block;
  width: 100%;
  padding: 0.5em 0.8em;
  font-size: 1rem;
  font-family: 'Lynstone',Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 700;
  color: #FFF;
  background-color: #000;
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  box-sizing: border-box;
  cursor: pointer;
  text-align: center;
  text-transform: uppercase;
  -webkit-transition: all 200ms ease;
  transition: all 200ms ease;
  height: 2.75rem;
  background-color: #000;
  border: none;
  color: #FFF;
  font-size: 1.0625rem;
  font-weight: 600;
  letter-spacing: 2px;
  line-height: normal;
  padding: 0.53em;
  color: #FFF;
  padding: 0.3rem;
  border: inherited;
  background-color: #000;
  font-size: 81.25%;
  font-weight: 700;
  letter-spacing: 0px;
  border-radius: 0px;
  text-align: center;
  min-height: 0px;
  height: 2.0625rem;
}

.emotion-5:focus {
  outline: none;
}

.emotion-5:disabled,
.emotion-5:disabled:hover {
  cursor: default;
}

.emotion-5:hover {
  background-color: #3f3f3f;
  border-color: transparent;
  color: #FFF;
}

.emotion-5:disabled,
.emotion-5:disabled:hover {
  background-color: #CCC;
  border-color: transparent;
  color: #FFF;
}

.emotion-5:hover {
  background-color: #666;
  color: #FFF;
}

.emotion-6 {
  clear: both;
  display: block;
  padding-top: 0.5rem;
  line-height: 1;
  width: 100%;
  text-transform: capitalize;
}

.emotion-7 {
  position: absolute;
  left: -999em;
  top: -999em;
}

<div
    class="emotion-0"
  >
    <div
      class="emotion-1"
    >
      <h2
        class="emotion-2"
      >
        <a
          aria-label=""
          class="map-link"
          href="https://www.google.com/maps/dir/?api=1&destination=150 N. State St.,CHICAGO,IL,60601"
          rel="noopener noreferrer"
          target="_blank"
        >
          150 N. STATE ST.
        </a>
      </h2>
      <div
        class="emotion-3"
      >
        (
        <span>
          0.0
        </span>
         Mi)
      </div>
    </div>
    <div
      class="emotion-4"
    >
      <button
        class="emotion-5"
      >
        changeStoreModal.selectedButton
      </button>
    </div>
    <div
      class="emotion-6"
    >
      <span>
        150 n. state st., chicago, IL, 60601
      </span>
      <div
        class="emotion-7"
      >
        <span
          id="storeDescription-3997"
        >
          150 N. STATE ST.. Address: 150 n. state st., chicago, IL, 60601 0.0 miles away
        </span>
      </div>
    </div>
  </div>
</DocumentFragment>
`;

exports[`<StoreCard /> snapshots renders default selected state correctly Gap 1`] = `
<DocumentFragment>
  .emotion-0 {
  border-bottom: 1px solid #CCC;
  padding: 1rem 0;
  font-size: 0.875rem;
  color: #666;
  line-height: 1.38;
  font-weight: 400;
}

.emotion-1 {
  float: left;
  padding-right: 0.5rem;
  width: 36%;
}

@media (min-width: 320px) {
  .emotion-1 {
    width: 38%;
  }
}

@media (min-width: 350px) {
  .emotion-1 {
    width: 43%;
  }
}

@media (min-width: 375px) {
  .emotion-1 {
    width: 48%;
  }
}

@media (min-width: 415px) {
  .emotion-1 {
    width: 54%;
  }
}

@media (min-width: 475px) {
  .emotion-1 {
    width: 58%;
  }
}

@media (min-width: 500px) {
  .emotion-1 {
    width: 60%;
  }
}

.emotion-2 {
  white-space: nowrap;
  line-height: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  text-transform: uppercase;
  font-size: 1rem;
  color: #002554;
}

.emotion-2 fontSet {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-4 {
  float: right;
  width: 155px;
  font-size: 1rem;
}

.emotion-5 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 1px solid transparent;
  border-radius: 0;
  font-size: 16px;
  font-weight: 400;
  letter-spacing: normal;
  min-height: 45px;
  max-height: auto;
  line-height: 1.125;
  padding: 12px 20px 11px;
  width: 100%;
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #FFF;
  background-color: #002554;
  border-color: #002554;
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  box-sizing: border-box;
  cursor: pointer;
  text-align: center;
  text-transform: uppercase;
  -webkit-transition: all 200ms ease;
  transition: all 200ms ease;
  height: 2.75rem;
  background-color: #002554;
  border: 2px solid #002554;
  color: #FFF;
  font-size: 1.0625rem;
  font-weight: 700;
  letter-spacing: 1px;
  line-height: normal;
  padding: 0.0625em 0.59em 0.125em;
  color: #FFF;
  padding: 0.3rem;
  border: inherited;
  background-color: #002554;
  font-size: 81.25%;
  font-weight: 700;
  letter-spacing: 0px;
  border-radius: 0px;
  text-align: center;
  min-height: 0px;
  height: 2.0625rem;
}

.emotion-5:focus {
  outline: none;
}

.emotion-5>span {
  padding: 1px 0;
}

.emotion-5:disabled,
.emotion-5:disabled:hover {
  cursor: default;
}

.emotion-5:hover {
  background-color: #3f5b7e;
  border-color: #3f5b7e;
  color: #FFF;
}

.emotion-5:disabled,
.emotion-5:disabled:hover {
  background-color: #CCC;
  border-color: transparent;
  color: #FFF;
}

.emotion-5:hover {
  background-color: #666;
  color: #FFF;
}

.emotion-6 {
  clear: both;
  display: block;
  padding-top: 0.5rem;
  line-height: 1;
  width: 100%;
  text-transform: capitalize;
}

.emotion-7 {
  position: absolute;
  left: -999em;
  top: -999em;
}

<div
    class="emotion-0"
  >
    <div
      class="emotion-1"
    >
      <h2
        class="emotion-2"
      >
        <a
          aria-label=""
          class="map-link"
          href="https://www.google.com/maps/dir/?api=1&destination=150 N. State St.,CHICAGO,IL,60601"
          rel="noopener noreferrer"
          target="_blank"
        >
          150 N. STATE ST.
        </a>
      </h2>
      <div
        class="emotion-3"
      >
        (
        <span>
          0.0
        </span>
         Mi)
      </div>
    </div>
    <div
      class="emotion-4"
    >
      <button
        class="emotion-5"
      >
        changeStoreModal.selectedButton
      </button>
    </div>
    <div
      class="emotion-6"
    >
      <span>
        150 n. state st., chicago, IL, 60601
      </span>
      <div
        class="emotion-7"
      >
        <span
          id="storeDescription-3997"
        >
          150 N. STATE ST.. Address: 150 n. state st., chicago, IL, 60601 0.0 miles away
        </span>
      </div>
    </div>
  </div>
</DocumentFragment>
`;

exports[`<StoreCard /> snapshots renders default selected state correctly Gap in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  border-bottom: 1px solid #CCC;
  padding: 1rem 0;
  font-size: 0.875rem;
  color: #666;
  line-height: 1.38;
  font-weight: 400;
}

.emotion-1 {
  float: left;
  padding-right: 0.5rem;
  width: 36%;
}

@media (min-width: 320px) {
  .emotion-1 {
    width: 38%;
  }
}

@media (min-width: 350px) {
  .emotion-1 {
    width: 43%;
  }
}

@media (min-width: 375px) {
  .emotion-1 {
    width: 48%;
  }
}

@media (min-width: 415px) {
  .emotion-1 {
    width: 54%;
  }
}

@media (min-width: 475px) {
  .emotion-1 {
    width: 58%;
  }
}

@media (min-width: 500px) {
  .emotion-1 {
    width: 60%;
  }
}

.emotion-2 {
  white-space: nowrap;
  line-height: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  text-transform: uppercase;
  font-size: 1rem;
  color: #002554;
}

.emotion-2 fontSet {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-4 {
  float: right;
  width: 155px;
  font-size: 1rem;
}

.emotion-5 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 1px solid transparent;
  border-radius: 0;
  font-size: 16px;
  font-weight: 400;
  letter-spacing: normal;
  min-height: 45px;
  max-height: auto;
  line-height: 1.125;
  padding: 12px 20px 11px;
  width: 100%;
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #FFF;
  background-color: #002554;
  border-color: #002554;
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  box-sizing: border-box;
  cursor: pointer;
  text-align: center;
  text-transform: uppercase;
  -webkit-transition: all 200ms ease;
  transition: all 200ms ease;
  height: 2.75rem;
  background-color: #002554;
  border: 2px solid #002554;
  color: #FFF;
  font-size: 1.0625rem;
  font-weight: 700;
  letter-spacing: 1px;
  line-height: normal;
  padding: 0.0625em 0.59em 0.125em;
  color: #FFF;
  padding: 0.3rem;
  border: inherited;
  background-color: #002554;
  font-size: 81.25%;
  font-weight: 700;
  letter-spacing: 0px;
  border-radius: 0px;
  text-align: center;
  min-height: 0px;
  height: 2.0625rem;
}

.emotion-5:focus {
  outline: none;
}

.emotion-5>span {
  padding: 1px 0;
}

.emotion-5:disabled,
.emotion-5:disabled:hover {
  cursor: default;
}

.emotion-5:hover {
  background-color: #3f5b7e;
  border-color: #3f5b7e;
  color: #FFF;
}

.emotion-5:disabled,
.emotion-5:disabled:hover {
  background-color: #CCC;
  border-color: transparent;
  color: #FFF;
}

.emotion-5:hover {
  background-color: #666;
  color: #FFF;
}

.emotion-6 {
  clear: both;
  display: block;
  padding-top: 0.5rem;
  line-height: 1;
  width: 100%;
  text-transform: capitalize;
}

.emotion-7 {
  position: absolute;
  left: -999em;
  top: -999em;
}

<div
    class="emotion-0"
  >
    <div
      class="emotion-1"
    >
      <h2
        class="emotion-2"
      >
        <a
          aria-label=""
          class="map-link"
          href="https://www.google.com/maps/dir/?api=1&destination=150 N. State St.,CHICAGO,IL,60601"
          rel="noopener noreferrer"
          target="_blank"
        >
          150 N. STATE ST.
        </a>
      </h2>
      <div
        class="emotion-3"
      >
        (
        <span>
          0.0
        </span>
         Mi)
      </div>
    </div>
    <div
      class="emotion-4"
    >
      <button
        class="emotion-5"
      >
        changeStoreModal.selectedButton
      </button>
    </div>
    <div
      class="emotion-6"
    >
      <span>
        150 n. state st., chicago, IL, 60601
      </span>
      <div
        class="emotion-7"
      >
        <span
          id="storeDescription-3997"
        >
          150 N. STATE ST.. Address: 150 n. state st., chicago, IL, 60601 0.0 miles away
        </span>
      </div>
    </div>
  </div>
</DocumentFragment>
`;

exports[`<StoreCard /> snapshots renders default selected state correctly GapFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  border-bottom: 1px solid #CCC;
  padding: 1rem 0;
  font-size: 0.875rem;
  color: #666;
  line-height: 1.38;
  font-weight: 400;
}

.emotion-1 {
  float: left;
  padding-right: 0.5rem;
  width: 36%;
}

@media (min-width: 320px) {
  .emotion-1 {
    width: 38%;
  }
}

@media (min-width: 350px) {
  .emotion-1 {
    width: 43%;
  }
}

@media (min-width: 375px) {
  .emotion-1 {
    width: 48%;
  }
}

@media (min-width: 415px) {
  .emotion-1 {
    width: 54%;
  }
}

@media (min-width: 475px) {
  .emotion-1 {
    width: 58%;
  }
}

@media (min-width: 500px) {
  .emotion-1 {
    width: 60%;
  }
}

.emotion-2 {
  white-space: nowrap;
  line-height: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  text-transform: uppercase;
  font-size: 1rem;
  color: #333;
}

.emotion-2 fontSet {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-4 {
  float: right;
  width: 155px;
  font-size: 1rem;
}

.emotion-5 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 1px solid transparent;
  border-radius: 0;
  font-size: 16px;
  font-weight: 400;
  letter-spacing: normal;
  min-height: 45px;
  max-height: auto;
  line-height: 1.125;
  padding: 12px 20px 11px;
  width: 100%;
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #FFF;
  background-color: #002554;
  border-color: #002554;
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  box-sizing: border-box;
  cursor: pointer;
  text-align: center;
  text-transform: uppercase;
  -webkit-transition: all 200ms ease;
  transition: all 200ms ease;
  height: 2.75rem;
  background-color: #002554;
  border: 2px solid #002554;
  color: #FFF;
  font-size: 1.0625rem;
  font-weight: 700;
  letter-spacing: 1px;
  line-height: normal;
  padding: 0.0625em 0.59em 0.125em;
  color: #FFF;
  padding: 0.3rem;
  border: inherited;
  background-color: #002554;
  font-size: 81.25%;
  font-weight: 700;
  letter-spacing: 0px;
  border-radius: 0px;
  text-align: center;
  min-height: 0px;
  height: 2.0625rem;
}

.emotion-5:focus {
  outline: none;
}

.emotion-5>span {
  padding: 1px 0;
}

.emotion-5:disabled,
.emotion-5:disabled:hover {
  cursor: default;
}

.emotion-5:hover {
  background-color: #3f5b7e;
  border-color: #3f5b7e;
  color: #FFF;
}

.emotion-5:disabled,
.emotion-5:disabled:hover {
  background-color: #CCC;
  border-color: transparent;
  color: #FFF;
}

.emotion-5:hover {
  background-color: #666;
  color: #FFF;
}

.emotion-6 {
  clear: both;
  display: block;
  padding-top: 0.5rem;
  line-height: 1;
  width: 100%;
  text-transform: capitalize;
}

.emotion-7 {
  position: absolute;
  left: -999em;
  top: -999em;
}

<div
    class="emotion-0"
  >
    <div
      class="emotion-1"
    >
      <h2
        class="emotion-2"
      >
        <a
          aria-label=""
          class="map-link"
          href="https://www.google.com/maps/dir/?api=1&destination=150 N. State St.,CHICAGO,IL,60601"
          rel="noopener noreferrer"
          target="_blank"
        >
          150 N. STATE ST.
        </a>
      </h2>
      <div
        class="emotion-3"
      >
        (
        <span>
          0.0
        </span>
         Mi)
      </div>
    </div>
    <div
      class="emotion-4"
    >
      <button
        class="emotion-5"
      >
        changeStoreModal.selectedButton
      </button>
    </div>
    <div
      class="emotion-6"
    >
      <span>
        150 n. state st., chicago, IL, 60601
      </span>
      <div
        class="emotion-7"
      >
        <span
          id="storeDescription-3997"
        >
          150 N. STATE ST.. Address: 150 n. state st., chicago, IL, 60601 0.0 miles away
        </span>
      </div>
    </div>
  </div>
</DocumentFragment>
`;

exports[`<StoreCard /> snapshots renders default selected state correctly GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  border-bottom: 1px solid #CCC;
  padding: 1rem 0;
  font-size: 0.875rem;
  color: #666;
  line-height: 1.38;
  font-weight: 400;
}

.emotion-1 {
  float: left;
  padding-right: 0.5rem;
  width: 36%;
}

@media (min-width: 320px) {
  .emotion-1 {
    width: 38%;
  }
}

@media (min-width: 350px) {
  .emotion-1 {
    width: 43%;
  }
}

@media (min-width: 375px) {
  .emotion-1 {
    width: 48%;
  }
}

@media (min-width: 415px) {
  .emotion-1 {
    width: 54%;
  }
}

@media (min-width: 475px) {
  .emotion-1 {
    width: 58%;
  }
}

@media (min-width: 500px) {
  .emotion-1 {
    width: 60%;
  }
}

.emotion-2 {
  white-space: nowrap;
  line-height: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  text-transform: uppercase;
  font-size: 1rem;
  color: #333;
}

.emotion-2 fontSet {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-4 {
  float: right;
  width: 155px;
  font-size: 1rem;
}

.emotion-5 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 1px solid transparent;
  border-radius: 0;
  font-size: 16px;
  font-weight: 400;
  letter-spacing: normal;
  min-height: 45px;
  max-height: auto;
  line-height: 1.125;
  padding: 12px 20px 11px;
  width: 100%;
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #FFF;
  background-color: #002554;
  border-color: #002554;
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  box-sizing: border-box;
  cursor: pointer;
  text-align: center;
  text-transform: uppercase;
  -webkit-transition: all 200ms ease;
  transition: all 200ms ease;
  height: 2.75rem;
  background-color: #002554;
  border: 2px solid #002554;
  color: #FFF;
  font-size: 1.0625rem;
  font-weight: 700;
  letter-spacing: 1px;
  line-height: normal;
  padding: 0.0625em 0.59em 0.125em;
  color: #FFF;
  padding: 0.3rem;
  border: inherited;
  background-color: #002554;
  font-size: 81.25%;
  font-weight: 700;
  letter-spacing: 0px;
  border-radius: 0px;
  text-align: center;
  min-height: 0px;
  height: 2.0625rem;
}

.emotion-5:focus {
  outline: none;
}

.emotion-5>span {
  padding: 1px 0;
}

.emotion-5:disabled,
.emotion-5:disabled:hover {
  cursor: default;
}

.emotion-5:hover {
  background-color: #3f5b7e;
  border-color: #3f5b7e;
  color: #FFF;
}

.emotion-5:disabled,
.emotion-5:disabled:hover {
  background-color: #CCC;
  border-color: transparent;
  color: #FFF;
}

.emotion-5:hover {
  background-color: #666;
  color: #FFF;
}

.emotion-6 {
  clear: both;
  display: block;
  padding-top: 0.5rem;
  line-height: 1;
  width: 100%;
  text-transform: capitalize;
}

.emotion-7 {
  position: absolute;
  left: -999em;
  top: -999em;
}

<div
    class="emotion-0"
  >
    <div
      class="emotion-1"
    >
      <h2
        class="emotion-2"
      >
        <a
          aria-label=""
          class="map-link"
          href="https://www.google.com/maps/dir/?api=1&destination=150 N. State St.,CHICAGO,IL,60601"
          rel="noopener noreferrer"
          target="_blank"
        >
          150 N. STATE ST.
        </a>
      </h2>
      <div
        class="emotion-3"
      >
        (
        <span>
          0.0
        </span>
         Mi)
      </div>
    </div>
    <div
      class="emotion-4"
    >
      <button
        class="emotion-5"
      >
        changeStoreModal.selectedButton
      </button>
    </div>
    <div
      class="emotion-6"
    >
      <span>
        150 n. state st., chicago, IL, 60601
      </span>
      <div
        class="emotion-7"
      >
        <span
          id="storeDescription-3997"
        >
          150 N. STATE ST.. Address: 150 n. state st., chicago, IL, 60601 0.0 miles away
        </span>
      </div>
    </div>
  </div>
</DocumentFragment>
`;

exports[`<StoreCard /> snapshots renders default selected state correctly OldNavy 1`] = `
<DocumentFragment>
  .emotion-0 {
  border-bottom: 1px solid #CCC;
  padding: 1rem 0;
  font-size: 0.875rem;
  color: #666;
  line-height: 1.38;
  font-weight: 400;
}

.emotion-1 {
  float: left;
  padding-right: 0.5rem;
  width: 36%;
}

@media (min-width: 320px) {
  .emotion-1 {
    width: 38%;
  }
}

@media (min-width: 350px) {
  .emotion-1 {
    width: 43%;
  }
}

@media (min-width: 375px) {
  .emotion-1 {
    width: 48%;
  }
}

@media (min-width: 415px) {
  .emotion-1 {
    width: 54%;
  }
}

@media (min-width: 475px) {
  .emotion-1 {
    width: 58%;
  }
}

@media (min-width: 500px) {
  .emotion-1 {
    width: 60%;
  }
}

.emotion-2 {
  white-space: nowrap;
  line-height: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  text-transform: uppercase;
  font-size: 1rem;
  color: #003764;
}

.emotion-2 fontSet {
  font-family: 'Gotham','Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-4 {
  float: right;
  width: 155px;
  font-size: 1rem;
}

.emotion-5 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 700;
  letter-spacing: 1.6800000000000002px;
  min-height: 44px;
  max-height: auto;
  line-height: 1.1428571428571428;
  padding: 12px 22px;
  width: 100%;
  font-family: 'Gotham','Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #FFF;
  background-color: #003764;
  border-color: #003764;
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  box-sizing: border-box;
  cursor: pointer;
  text-align: center;
  text-transform: uppercase;
  -webkit-transition: all 200ms ease;
  transition: all 200ms ease;
  height: 2.75rem;
  background-color: #003764;
  border: none;
  color: #FFF;
  font-size: 1.1875rem;
  font-weight: 700;
  letter-spacing: 1px;
  line-height: normal;
  padding: 0.45em;
  color: #FFF;
  padding: 0.3rem;
  border: none;
  background-color: #003764;
  font-size: 81.25%;
  font-weight: 700;
  letter-spacing: 0px;
  border-radius: 0px;
  text-align: center;
  min-height: 0px;
  height: 2.0625rem;
}

.emotion-5:focus {
  outline: none;
}

.emotion-5>span {
  padding: 1px 0;
}

.emotion-5:disabled,
.emotion-5:disabled:hover {
  cursor: default;
}

.emotion-5:hover {
  background-color: #3f698a;
  border-color: transparent;
  color: #FFF;
}

.emotion-5:disabled,
.emotion-5:disabled:hover {
  background-color: #CCC;
  border-color: transparent;
  color: #FFF;
}

.emotion-5:hover {
  background-color: #40698b;
  color: #FFF;
}

.emotion-6 {
  clear: both;
  display: block;
  padding-top: 0.5rem;
  line-height: 1;
  width: 100%;
  text-transform: capitalize;
}

.emotion-7 {
  position: absolute;
  left: -999em;
  top: -999em;
}

<div
    class="emotion-0"
  >
    <div
      class="emotion-1"
    >
      <h2
        class="emotion-2"
      >
        <a
          aria-label=""
          class="map-link"
          href="https://www.google.com/maps/dir/?api=1&destination=150 N. State St.,CHICAGO,IL,60601"
          rel="noopener noreferrer"
          target="_blank"
        >
          150 N. STATE ST.
        </a>
      </h2>
      <div
        class="emotion-3"
      >
        (
        <span>
          0.0
        </span>
         Mi)
      </div>
    </div>
    <div
      class="emotion-4"
    >
      <button
        class="emotion-5"
      >
        changeStoreModal.selectedButton
      </button>
    </div>
    <div
      class="emotion-6"
    >
      <span>
        150 n. state st., chicago, IL, 60601
      </span>
      <div
        class="emotion-7"
      >
        <span
          id="storeDescription-3997"
        >
          150 N. STATE ST.. Address: 150 n. state st., chicago, IL, 60601 0.0 miles away
        </span>
      </div>
    </div>
  </div>
</DocumentFragment>
`;

exports[`<StoreCard /> snapshots renders default selected state correctly OldNavy in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  border-bottom: 1px solid #CCC;
  padding: 1rem 0;
  font-size: 0.875rem;
  color: #666;
  line-height: 1.38;
  font-weight: 400;
}

.emotion-1 {
  float: left;
  padding-right: 0.5rem;
  width: 36%;
}

@media (min-width: 320px) {
  .emotion-1 {
    width: 38%;
  }
}

@media (min-width: 350px) {
  .emotion-1 {
    width: 43%;
  }
}

@media (min-width: 375px) {
  .emotion-1 {
    width: 48%;
  }
}

@media (min-width: 415px) {
  .emotion-1 {
    width: 54%;
  }
}

@media (min-width: 475px) {
  .emotion-1 {
    width: 58%;
  }
}

@media (min-width: 500px) {
  .emotion-1 {
    width: 60%;
  }
}

.emotion-2 {
  white-space: nowrap;
  line-height: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  text-transform: uppercase;
  font-size: 1rem;
  color: #003764;
}

.emotion-2 fontSet {
  font-family: 'Gotham','Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-4 {
  float: right;
  width: 155px;
  font-size: 1rem;
}

.emotion-5 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 700;
  letter-spacing: 1.6800000000000002px;
  min-height: 44px;
  max-height: auto;
  line-height: 1.1428571428571428;
  padding: 12px 22px;
  width: 100%;
  font-family: 'Gotham','Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #FFF;
  background-color: #003764;
  border-color: #003764;
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  box-sizing: border-box;
  cursor: pointer;
  text-align: center;
  text-transform: uppercase;
  -webkit-transition: all 200ms ease;
  transition: all 200ms ease;
  height: 2.75rem;
  background-color: #003764;
  border: none;
  color: #FFF;
  font-size: 1.1875rem;
  font-weight: 700;
  letter-spacing: 1px;
  line-height: normal;
  padding: 0.45em;
  color: #FFF;
  padding: 0.3rem;
  border: none;
  background-color: #003764;
  font-size: 81.25%;
  font-weight: 700;
  letter-spacing: 0px;
  border-radius: 0px;
  text-align: center;
  min-height: 0px;
  height: 2.0625rem;
}

.emotion-5:focus {
  outline: none;
}

.emotion-5>span {
  padding: 1px 0;
}

.emotion-5:disabled,
.emotion-5:disabled:hover {
  cursor: default;
}

.emotion-5:hover {
  background-color: #3f698a;
  border-color: transparent;
  color: #FFF;
}

.emotion-5:disabled,
.emotion-5:disabled:hover {
  background-color: #CCC;
  border-color: transparent;
  color: #FFF;
}

.emotion-5:hover {
  background-color: #40698b;
  color: #FFF;
}

.emotion-6 {
  clear: both;
  display: block;
  padding-top: 0.5rem;
  line-height: 1;
  width: 100%;
  text-transform: capitalize;
}

.emotion-7 {
  position: absolute;
  left: -999em;
  top: -999em;
}

<div
    class="emotion-0"
  >
    <div
      class="emotion-1"
    >
      <h2
        class="emotion-2"
      >
        <a
          aria-label=""
          class="map-link"
          href="https://www.google.com/maps/dir/?api=1&destination=150 N. State St.,CHICAGO,IL,60601"
          rel="noopener noreferrer"
          target="_blank"
        >
          150 N. STATE ST.
        </a>
      </h2>
      <div
        class="emotion-3"
      >
        (
        <span>
          0.0
        </span>
         Mi)
      </div>
    </div>
    <div
      class="emotion-4"
    >
      <button
        class="emotion-5"
      >
        changeStoreModal.selectedButton
      </button>
    </div>
    <div
      class="emotion-6"
    >
      <span>
        150 n. state st., chicago, IL, 60601
      </span>
      <div
        class="emotion-7"
      >
        <span
          id="storeDescription-3997"
        >
          150 N. STATE ST.. Address: 150 n. state st., chicago, IL, 60601 0.0 miles away
        </span>
      </div>
    </div>
  </div>
</DocumentFragment>
`;

exports[`<StoreCard /> snapshots renders default state correctly Athleta 1`] = `
<DocumentFragment>
  .emotion-0 {
  border-bottom: 1px solid #CCC;
  padding: 1rem 0;
  font-size: 0.875rem;
  color: #666;
  line-height: 1.38;
  font-weight: 400;
}

.emotion-1 {
  float: left;
  padding-right: 0.5rem;
  width: 36%;
}

@media (min-width: 320px) {
  .emotion-1 {
    width: 38%;
  }
}

@media (min-width: 350px) {
  .emotion-1 {
    width: 43%;
  }
}

@media (min-width: 375px) {
  .emotion-1 {
    width: 48%;
  }
}

@media (min-width: 415px) {
  .emotion-1 {
    width: 54%;
  }
}

@media (min-width: 475px) {
  .emotion-1 {
    width: 58%;
  }
}

@media (min-width: 500px) {
  .emotion-1 {
    width: 60%;
  }
}

.emotion-2 {
  white-space: nowrap;
  line-height: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  text-transform: uppercase;
  font-size: 1rem;
  color: #333;
}

.emotion-2 fontSet {
  font-family: 'Avenir Next','Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-4 {
  float: right;
  width: 155px;
  font-size: 1rem;
}

.emotion-5 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 1.5px solid transparent;
  border-radius: 0;
  font-size: 16px;
  font-weight: 500;
  letter-spacing: 0px;
  min-height: auto;
  max-height: 60px;
  line-height: 1.25;
  padding: 22px 40px;
  width: 100%;
  font-family: 'Avenir Next','Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #000;
  background: transparent;
  border-color: #000;
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  box-sizing: border-box;
  cursor: pointer;
  text-align: center;
  text-transform: uppercase;
  -webkit-transition: all 200ms ease;
  transition: all 200ms ease;
  height: 2.75rem;
  background-color: #333;
  border: none;
  color: #FFF;
  font-size: 1.1875rem;
  font-weight: 600;
  letter-spacing: 1px;
  line-height: normal;
  padding: 0.0625em 0.52em 0.125em;
  color: #FFF;
  padding: 0.3rem;
  border: 2px solid #333;
  background-color: inherited;
  font-size: 81.25%;
  font-weight: 700;
  letter-spacing: 0px;
  border-radius: 0px;
  text-align: center;
  min-height: 0px;
  height: 2.0625rem;
}

.emotion-5:focus {
  outline: none;
}

.emotion-5>span {
  padding: 1px 0;
}

.emotion-5:disabled,
.emotion-5:disabled:hover {
  cursor: default;
}

.emotion-5:hover {
  background-color: #666;
  border-color: transparent;
  color: #FFF;
}

.emotion-5:disabled,
.emotion-5:disabled:hover {
  background-color: #CCC;
  border-color: transparent;
  color: #FFF;
}

.emotion-6 {
  clear: both;
  display: block;
  padding-top: 0.5rem;
  line-height: 1;
  width: 100%;
  text-transform: capitalize;
}

.emotion-7 {
  position: absolute;
  left: -999em;
  top: -999em;
}

<div
    class="emotion-0"
  >
    <div
      class="emotion-1"
    >
      <h2
        class="emotion-2"
      >
        <a
          aria-label=""
          class="map-link"
          href="https://www.google.com/maps/dir/?api=1&destination=150 N. State St.,CHICAGO,IL,60601"
          rel="noopener noreferrer"
          target="_blank"
        >
          150 N. STATE ST.
        </a>
      </h2>
      <div
        class="emotion-3"
      >
        (
        <span>
          0.0
        </span>
         Mi)
      </div>
    </div>
    <div
      class="emotion-4"
    >
      <button
        class="emotion-5"
      >
        changeStoreModal.seeAvailability
      </button>
    </div>
    <div
      class="emotion-6"
    >
      <span>
        150 n. state st., chicago, IL, 60601
      </span>
      <div
        class="emotion-7"
      >
        <span
          id="storeDescription-3997"
        >
          150 N. STATE ST.. Address: 150 n. state st., chicago, IL, 60601 0.0 miles away
        </span>
      </div>
    </div>
  </div>
</DocumentFragment>
`;

exports[`<StoreCard /> snapshots renders default state correctly Athleta in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  border-bottom: 1px solid #CCC;
  padding: 1rem 0;
  font-size: 0.875rem;
  color: #666;
  line-height: 1.38;
  font-weight: 400;
}

.emotion-1 {
  float: left;
  padding-right: 0.5rem;
  width: 36%;
}

@media (min-width: 320px) {
  .emotion-1 {
    width: 38%;
  }
}

@media (min-width: 350px) {
  .emotion-1 {
    width: 43%;
  }
}

@media (min-width: 375px) {
  .emotion-1 {
    width: 48%;
  }
}

@media (min-width: 415px) {
  .emotion-1 {
    width: 54%;
  }
}

@media (min-width: 475px) {
  .emotion-1 {
    width: 58%;
  }
}

@media (min-width: 500px) {
  .emotion-1 {
    width: 60%;
  }
}

.emotion-2 {
  white-space: nowrap;
  line-height: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  text-transform: uppercase;
  font-size: 1rem;
  color: #333;
}

.emotion-2 fontSet {
  font-family: 'Avenir Next','Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-4 {
  float: right;
  width: 155px;
  font-size: 1rem;
}

.emotion-5 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 1.5px solid transparent;
  border-radius: 0;
  font-size: 16px;
  font-weight: 500;
  letter-spacing: 0px;
  min-height: auto;
  max-height: 60px;
  line-height: 1.25;
  padding: 22px 40px;
  width: 100%;
  font-family: 'Avenir Next','Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #000;
  background: transparent;
  border-color: #000;
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  box-sizing: border-box;
  cursor: pointer;
  text-align: center;
  text-transform: uppercase;
  -webkit-transition: all 200ms ease;
  transition: all 200ms ease;
  height: 2.75rem;
  background-color: #333;
  border: none;
  color: #FFF;
  font-size: 1.1875rem;
  font-weight: 600;
  letter-spacing: 1px;
  line-height: normal;
  padding: 0.0625em 0.52em 0.125em;
  color: #FFF;
  padding: 0.3rem;
  border: 2px solid #333;
  background-color: inherited;
  font-size: 81.25%;
  font-weight: 700;
  letter-spacing: 0px;
  border-radius: 0px;
  text-align: center;
  min-height: 0px;
  height: 2.0625rem;
}

.emotion-5:focus {
  outline: none;
}

.emotion-5>span {
  padding: 1px 0;
}

.emotion-5:disabled,
.emotion-5:disabled:hover {
  cursor: default;
}

.emotion-5:hover {
  background-color: #666;
  border-color: transparent;
  color: #FFF;
}

.emotion-5:disabled,
.emotion-5:disabled:hover {
  background-color: #CCC;
  border-color: transparent;
  color: #FFF;
}

.emotion-6 {
  clear: both;
  display: block;
  padding-top: 0.5rem;
  line-height: 1;
  width: 100%;
  text-transform: capitalize;
}

.emotion-7 {
  position: absolute;
  left: -999em;
  top: -999em;
}

<div
    class="emotion-0"
  >
    <div
      class="emotion-1"
    >
      <h2
        class="emotion-2"
      >
        <a
          aria-label=""
          class="map-link"
          href="https://www.google.com/maps/dir/?api=1&destination=150 N. State St.,CHICAGO,IL,60601"
          rel="noopener noreferrer"
          target="_blank"
        >
          150 N. STATE ST.
        </a>
      </h2>
      <div
        class="emotion-3"
      >
        (
        <span>
          0.0
        </span>
         Mi)
      </div>
    </div>
    <div
      class="emotion-4"
    >
      <button
        class="emotion-5"
      >
        changeStoreModal.seeAvailability
      </button>
    </div>
    <div
      class="emotion-6"
    >
      <span>
        150 n. state st., chicago, IL, 60601
      </span>
      <div
        class="emotion-7"
      >
        <span
          id="storeDescription-3997"
        >
          150 N. STATE ST.. Address: 150 n. state st., chicago, IL, 60601 0.0 miles away
        </span>
      </div>
    </div>
  </div>
</DocumentFragment>
`;

exports[`<StoreCard /> snapshots renders default state correctly BananaRepublic 1`] = `
<DocumentFragment>
  .emotion-0 {
  border-bottom: 1px solid #CCC;
  padding: 1rem 0;
  font-size: 0.875rem;
  color: #666;
  line-height: 1.38;
  font-weight: 400;
}

.emotion-1 {
  float: left;
  padding-right: 0.5rem;
  width: 36%;
}

@media (min-width: 320px) {
  .emotion-1 {
    width: 38%;
  }
}

@media (min-width: 350px) {
  .emotion-1 {
    width: 43%;
  }
}

@media (min-width: 375px) {
  .emotion-1 {
    width: 48%;
  }
}

@media (min-width: 415px) {
  .emotion-1 {
    width: 54%;
  }
}

@media (min-width: 475px) {
  .emotion-1 {
    width: 58%;
  }
}

@media (min-width: 500px) {
  .emotion-1 {
    width: 60%;
  }
}

.emotion-2 {
  white-space: nowrap;
  line-height: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  text-transform: uppercase;
  font-size: 1rem;
  color: #000;
}

.emotion-2 fontSet {
  font-family: 'Lynstone',Helvetica,Arial,sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-4 {
  float: right;
  width: 155px;
  font-size: 1rem;
}

.emotion-5 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 0;
  text-align: center;
  display: block;
  width: 100%;
  padding: 0.5em 0.8em;
  font-size: 1rem;
  font-family: 'Lynstone',Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 600;
  color: #000;
  background-color: transparent;
  border-color: #000;
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  box-sizing: border-box;
  cursor: pointer;
  text-align: center;
  text-transform: uppercase;
  -webkit-transition: all 200ms ease;
  transition: all 200ms ease;
  height: 2.75rem;
  background-color: #000;
  border: none;
  color: #FFF;
  font-size: 1.0625rem;
  font-weight: 600;
  letter-spacing: 2px;
  line-height: normal;
  padding: 0.53em;
  color: #FFF;
  padding: 0.3rem;
  border: 2px solid #000;
  background-color: inherited;
  font-size: 81.25%;
  font-weight: 700;
  letter-spacing: 0px;
  border-radius: 0px;
  text-align: center;
  min-height: 0px;
  height: 2.0625rem;
}

.emotion-5:focus {
  outline: none;
}

.emotion-5:disabled,
.emotion-5:disabled:hover {
  cursor: default;
}

.emotion-5:hover {
  background-color: #3f3f3f;
  border-color: transparent;
  color: #FFF;
}

.emotion-5:disabled,
.emotion-5:disabled:hover {
  background-color: #CCC;
  border-color: transparent;
  color: #FFF;
}

.emotion-6 {
  clear: both;
  display: block;
  padding-top: 0.5rem;
  line-height: 1;
  width: 100%;
  text-transform: capitalize;
}

.emotion-7 {
  position: absolute;
  left: -999em;
  top: -999em;
}

<div
    class="emotion-0"
  >
    <div
      class="emotion-1"
    >
      <h2
        class="emotion-2"
      >
        <a
          aria-label=""
          class="map-link"
          href="https://www.google.com/maps/dir/?api=1&destination=150 N. State St.,CHICAGO,IL,60601"
          rel="noopener noreferrer"
          target="_blank"
        >
          150 N. STATE ST.
        </a>
      </h2>
      <div
        class="emotion-3"
      >
        (
        <span>
          0.0
        </span>
         Mi)
      </div>
    </div>
    <div
      class="emotion-4"
    >
      <button
        class="emotion-5"
      >
        changeStoreModal.seeAvailability
      </button>
    </div>
    <div
      class="emotion-6"
    >
      <span>
        150 n. state st., chicago, IL, 60601
      </span>
      <div
        class="emotion-7"
      >
        <span
          id="storeDescription-3997"
        >
          150 N. STATE ST.. Address: 150 n. state st., chicago, IL, 60601 0.0 miles away
        </span>
      </div>
    </div>
  </div>
</DocumentFragment>
`;

exports[`<StoreCard /> snapshots renders default state correctly BananaRepublic in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  border-bottom: 1px solid #CCC;
  padding: 1rem 0;
  font-size: 0.875rem;
  color: #666;
  line-height: 1.38;
  font-weight: 400;
}

.emotion-1 {
  float: left;
  padding-right: 0.5rem;
  width: 36%;
}

@media (min-width: 320px) {
  .emotion-1 {
    width: 38%;
  }
}

@media (min-width: 350px) {
  .emotion-1 {
    width: 43%;
  }
}

@media (min-width: 375px) {
  .emotion-1 {
    width: 48%;
  }
}

@media (min-width: 415px) {
  .emotion-1 {
    width: 54%;
  }
}

@media (min-width: 475px) {
  .emotion-1 {
    width: 58%;
  }
}

@media (min-width: 500px) {
  .emotion-1 {
    width: 60%;
  }
}

.emotion-2 {
  white-space: nowrap;
  line-height: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  text-transform: uppercase;
  font-size: 1rem;
  color: #000;
}

.emotion-2 fontSet {
  font-family: 'Lynstone',Helvetica,Arial,sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-4 {
  float: right;
  width: 155px;
  font-size: 1rem;
}

.emotion-5 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 0;
  text-align: center;
  display: block;
  width: 100%;
  padding: 0.5em 0.8em;
  font-size: 1rem;
  font-family: 'Lynstone',Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 600;
  color: #000;
  background-color: transparent;
  border-color: #000;
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  box-sizing: border-box;
  cursor: pointer;
  text-align: center;
  text-transform: uppercase;
  -webkit-transition: all 200ms ease;
  transition: all 200ms ease;
  height: 2.75rem;
  background-color: #000;
  border: none;
  color: #FFF;
  font-size: 1.0625rem;
  font-weight: 600;
  letter-spacing: 2px;
  line-height: normal;
  padding: 0.53em;
  color: #FFF;
  padding: 0.3rem;
  border: 2px solid #000;
  background-color: inherited;
  font-size: 81.25%;
  font-weight: 700;
  letter-spacing: 0px;
  border-radius: 0px;
  text-align: center;
  min-height: 0px;
  height: 2.0625rem;
}

.emotion-5:focus {
  outline: none;
}

.emotion-5:disabled,
.emotion-5:disabled:hover {
  cursor: default;
}

.emotion-5:hover {
  background-color: #3f3f3f;
  border-color: transparent;
  color: #FFF;
}

.emotion-5:disabled,
.emotion-5:disabled:hover {
  background-color: #CCC;
  border-color: transparent;
  color: #FFF;
}

.emotion-6 {
  clear: both;
  display: block;
  padding-top: 0.5rem;
  line-height: 1;
  width: 100%;
  text-transform: capitalize;
}

.emotion-7 {
  position: absolute;
  left: -999em;
  top: -999em;
}

<div
    class="emotion-0"
  >
    <div
      class="emotion-1"
    >
      <h2
        class="emotion-2"
      >
        <a
          aria-label=""
          class="map-link"
          href="https://www.google.com/maps/dir/?api=1&destination=150 N. State St.,CHICAGO,IL,60601"
          rel="noopener noreferrer"
          target="_blank"
        >
          150 N. STATE ST.
        </a>
      </h2>
      <div
        class="emotion-3"
      >
        (
        <span>
          0.0
        </span>
         Mi)
      </div>
    </div>
    <div
      class="emotion-4"
    >
      <button
        class="emotion-5"
      >
        changeStoreModal.seeAvailability
      </button>
    </div>
    <div
      class="emotion-6"
    >
      <span>
        150 n. state st., chicago, IL, 60601
      </span>
      <div
        class="emotion-7"
      >
        <span
          id="storeDescription-3997"
        >
          150 N. STATE ST.. Address: 150 n. state st., chicago, IL, 60601 0.0 miles away
        </span>
      </div>
    </div>
  </div>
</DocumentFragment>
`;

exports[`<StoreCard /> snapshots renders default state correctly BananaRepublicFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  border-bottom: 1px solid #CCC;
  padding: 1rem 0;
  font-size: 0.875rem;
  color: #666;
  line-height: 1.38;
  font-weight: 400;
}

.emotion-1 {
  float: left;
  padding-right: 0.5rem;
  width: 36%;
}

@media (min-width: 320px) {
  .emotion-1 {
    width: 38%;
  }
}

@media (min-width: 350px) {
  .emotion-1 {
    width: 43%;
  }
}

@media (min-width: 375px) {
  .emotion-1 {
    width: 48%;
  }
}

@media (min-width: 415px) {
  .emotion-1 {
    width: 54%;
  }
}

@media (min-width: 475px) {
  .emotion-1 {
    width: 58%;
  }
}

@media (min-width: 500px) {
  .emotion-1 {
    width: 60%;
  }
}

.emotion-2 {
  white-space: nowrap;
  line-height: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  text-transform: uppercase;
  font-size: 1rem;
  color: #000;
}

.emotion-2 fontSet {
  font-family: 'Lynstone',Helvetica,Arial,sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-4 {
  float: right;
  width: 155px;
  font-size: 1rem;
}

.emotion-5 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 0;
  text-align: center;
  display: block;
  width: 100%;
  padding: 0.5em 0.8em;
  font-size: 1rem;
  font-family: 'Lynstone',Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 700;
  color: #000;
  background-color: transparent;
  border-color: #000;
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  box-sizing: border-box;
  cursor: pointer;
  text-align: center;
  text-transform: uppercase;
  -webkit-transition: all 200ms ease;
  transition: all 200ms ease;
  height: 2.75rem;
  background-color: #000;
  border: none;
  color: #FFF;
  font-size: 1.0625rem;
  font-weight: 600;
  letter-spacing: 2px;
  line-height: normal;
  padding: 0.53em;
  color: #FFF;
  padding: 0.3rem;
  border: 2px solid #000;
  background-color: inherited;
  font-size: 81.25%;
  font-weight: 700;
  letter-spacing: 0px;
  border-radius: 0px;
  text-align: center;
  min-height: 0px;
  height: 2.0625rem;
}

.emotion-5:focus {
  outline: none;
}

.emotion-5:disabled,
.emotion-5:disabled:hover {
  cursor: default;
}

.emotion-5:hover {
  background-color: #3f3f3f;
  border-color: transparent;
  color: #FFF;
}

.emotion-5:disabled,
.emotion-5:disabled:hover {
  background-color: #CCC;
  border-color: transparent;
  color: #FFF;
}

.emotion-6 {
  clear: both;
  display: block;
  padding-top: 0.5rem;
  line-height: 1;
  width: 100%;
  text-transform: capitalize;
}

.emotion-7 {
  position: absolute;
  left: -999em;
  top: -999em;
}

<div
    class="emotion-0"
  >
    <div
      class="emotion-1"
    >
      <h2
        class="emotion-2"
      >
        <a
          aria-label=""
          class="map-link"
          href="https://www.google.com/maps/dir/?api=1&destination=150 N. State St.,CHICAGO,IL,60601"
          rel="noopener noreferrer"
          target="_blank"
        >
          150 N. STATE ST.
        </a>
      </h2>
      <div
        class="emotion-3"
      >
        (
        <span>
          0.0
        </span>
         Mi)
      </div>
    </div>
    <div
      class="emotion-4"
    >
      <button
        class="emotion-5"
      >
        changeStoreModal.seeAvailability
      </button>
    </div>
    <div
      class="emotion-6"
    >
      <span>
        150 n. state st., chicago, IL, 60601
      </span>
      <div
        class="emotion-7"
      >
        <span
          id="storeDescription-3997"
        >
          150 N. STATE ST.. Address: 150 n. state st., chicago, IL, 60601 0.0 miles away
        </span>
      </div>
    </div>
  </div>
</DocumentFragment>
`;

exports[`<StoreCard /> snapshots renders default state correctly BananaRepublicFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  border-bottom: 1px solid #CCC;
  padding: 1rem 0;
  font-size: 0.875rem;
  color: #666;
  line-height: 1.38;
  font-weight: 400;
}

.emotion-1 {
  float: left;
  padding-right: 0.5rem;
  width: 36%;
}

@media (min-width: 320px) {
  .emotion-1 {
    width: 38%;
  }
}

@media (min-width: 350px) {
  .emotion-1 {
    width: 43%;
  }
}

@media (min-width: 375px) {
  .emotion-1 {
    width: 48%;
  }
}

@media (min-width: 415px) {
  .emotion-1 {
    width: 54%;
  }
}

@media (min-width: 475px) {
  .emotion-1 {
    width: 58%;
  }
}

@media (min-width: 500px) {
  .emotion-1 {
    width: 60%;
  }
}

.emotion-2 {
  white-space: nowrap;
  line-height: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  text-transform: uppercase;
  font-size: 1rem;
  color: #000;
}

.emotion-2 fontSet {
  font-family: 'Lynstone',Helvetica,Arial,sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-4 {
  float: right;
  width: 155px;
  font-size: 1rem;
}

.emotion-5 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 0;
  text-align: center;
  display: block;
  width: 100%;
  padding: 0.5em 0.8em;
  font-size: 1rem;
  font-family: 'Lynstone',Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 700;
  color: #000;
  background-color: transparent;
  border-color: #000;
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  box-sizing: border-box;
  cursor: pointer;
  text-align: center;
  text-transform: uppercase;
  -webkit-transition: all 200ms ease;
  transition: all 200ms ease;
  height: 2.75rem;
  background-color: #000;
  border: none;
  color: #FFF;
  font-size: 1.0625rem;
  font-weight: 600;
  letter-spacing: 2px;
  line-height: normal;
  padding: 0.53em;
  color: #FFF;
  padding: 0.3rem;
  border: 2px solid #000;
  background-color: inherited;
  font-size: 81.25%;
  font-weight: 700;
  letter-spacing: 0px;
  border-radius: 0px;
  text-align: center;
  min-height: 0px;
  height: 2.0625rem;
}

.emotion-5:focus {
  outline: none;
}

.emotion-5:disabled,
.emotion-5:disabled:hover {
  cursor: default;
}

.emotion-5:hover {
  background-color: #3f3f3f;
  border-color: transparent;
  color: #FFF;
}

.emotion-5:disabled,
.emotion-5:disabled:hover {
  background-color: #CCC;
  border-color: transparent;
  color: #FFF;
}

.emotion-6 {
  clear: both;
  display: block;
  padding-top: 0.5rem;
  line-height: 1;
  width: 100%;
  text-transform: capitalize;
}

.emotion-7 {
  position: absolute;
  left: -999em;
  top: -999em;
}

<div
    class="emotion-0"
  >
    <div
      class="emotion-1"
    >
      <h2
        class="emotion-2"
      >
        <a
          aria-label=""
          class="map-link"
          href="https://www.google.com/maps/dir/?api=1&destination=150 N. State St.,CHICAGO,IL,60601"
          rel="noopener noreferrer"
          target="_blank"
        >
          150 N. STATE ST.
        </a>
      </h2>
      <div
        class="emotion-3"
      >
        (
        <span>
          0.0
        </span>
         Mi)
      </div>
    </div>
    <div
      class="emotion-4"
    >
      <button
        class="emotion-5"
      >
        changeStoreModal.seeAvailability
      </button>
    </div>
    <div
      class="emotion-6"
    >
      <span>
        150 n. state st., chicago, IL, 60601
      </span>
      <div
        class="emotion-7"
      >
        <span
          id="storeDescription-3997"
        >
          150 N. STATE ST.. Address: 150 n. state st., chicago, IL, 60601 0.0 miles away
        </span>
      </div>
    </div>
  </div>
</DocumentFragment>
`;

exports[`<StoreCard /> snapshots renders default state correctly Gap 1`] = `
<DocumentFragment>
  .emotion-0 {
  border-bottom: 1px solid #CCC;
  padding: 1rem 0;
  font-size: 0.875rem;
  color: #666;
  line-height: 1.38;
  font-weight: 400;
}

.emotion-1 {
  float: left;
  padding-right: 0.5rem;
  width: 36%;
}

@media (min-width: 320px) {
  .emotion-1 {
    width: 38%;
  }
}

@media (min-width: 350px) {
  .emotion-1 {
    width: 43%;
  }
}

@media (min-width: 375px) {
  .emotion-1 {
    width: 48%;
  }
}

@media (min-width: 415px) {
  .emotion-1 {
    width: 54%;
  }
}

@media (min-width: 475px) {
  .emotion-1 {
    width: 58%;
  }
}

@media (min-width: 500px) {
  .emotion-1 {
    width: 60%;
  }
}

.emotion-2 {
  white-space: nowrap;
  line-height: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  text-transform: uppercase;
  font-size: 1rem;
  color: #002554;
}

.emotion-2 fontSet {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-4 {
  float: right;
  width: 155px;
  font-size: 1rem;
}

.emotion-5 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 1px solid transparent;
  border-radius: 0;
  font-size: 16px;
  font-weight: 400;
  letter-spacing: normal;
  min-height: 45px;
  max-height: auto;
  line-height: 1.125;
  padding: 12px 20px 11px;
  width: 100%;
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #002554;
  background: transparent;
  border-color: #002554;
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  box-sizing: border-box;
  cursor: pointer;
  text-align: center;
  text-transform: uppercase;
  -webkit-transition: all 200ms ease;
  transition: all 200ms ease;
  height: 2.75rem;
  background-color: #002554;
  border: 2px solid #002554;
  color: #FFF;
  font-size: 1.0625rem;
  font-weight: 700;
  letter-spacing: 1px;
  line-height: normal;
  padding: 0.0625em 0.59em 0.125em;
  color: #FFF;
  padding: 0.3rem;
  border: 2px solid #002554;
  background-color: inherited;
  font-size: 81.25%;
  font-weight: 700;
  letter-spacing: 0px;
  border-radius: 0px;
  text-align: center;
  min-height: 0px;
  height: 2.0625rem;
}

.emotion-5:focus {
  outline: none;
}

.emotion-5>span {
  padding: 1px 0;
}

.emotion-5:disabled,
.emotion-5:disabled:hover {
  cursor: default;
}

.emotion-5:hover {
  background-color: #3f5b7e;
  border-color: #3f5b7e;
  color: #FFF;
}

.emotion-5:disabled,
.emotion-5:disabled:hover {
  background-color: #CCC;
  border-color: transparent;
  color: #FFF;
}

.emotion-6 {
  clear: both;
  display: block;
  padding-top: 0.5rem;
  line-height: 1;
  width: 100%;
  text-transform: capitalize;
}

.emotion-7 {
  position: absolute;
  left: -999em;
  top: -999em;
}

<div
    class="emotion-0"
  >
    <div
      class="emotion-1"
    >
      <h2
        class="emotion-2"
      >
        <a
          aria-label=""
          class="map-link"
          href="https://www.google.com/maps/dir/?api=1&destination=150 N. State St.,CHICAGO,IL,60601"
          rel="noopener noreferrer"
          target="_blank"
        >
          150 N. STATE ST.
        </a>
      </h2>
      <div
        class="emotion-3"
      >
        (
        <span>
          0.0
        </span>
         Mi)
      </div>
    </div>
    <div
      class="emotion-4"
    >
      <button
        class="emotion-5"
      >
        changeStoreModal.seeAvailability
      </button>
    </div>
    <div
      class="emotion-6"
    >
      <span>
        150 n. state st., chicago, IL, 60601
      </span>
      <div
        class="emotion-7"
      >
        <span
          id="storeDescription-3997"
        >
          150 N. STATE ST.. Address: 150 n. state st., chicago, IL, 60601 0.0 miles away
        </span>
      </div>
    </div>
  </div>
</DocumentFragment>
`;

exports[`<StoreCard /> snapshots renders default state correctly Gap in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  border-bottom: 1px solid #CCC;
  padding: 1rem 0;
  font-size: 0.875rem;
  color: #666;
  line-height: 1.38;
  font-weight: 400;
}

.emotion-1 {
  float: left;
  padding-right: 0.5rem;
  width: 36%;
}

@media (min-width: 320px) {
  .emotion-1 {
    width: 38%;
  }
}

@media (min-width: 350px) {
  .emotion-1 {
    width: 43%;
  }
}

@media (min-width: 375px) {
  .emotion-1 {
    width: 48%;
  }
}

@media (min-width: 415px) {
  .emotion-1 {
    width: 54%;
  }
}

@media (min-width: 475px) {
  .emotion-1 {
    width: 58%;
  }
}

@media (min-width: 500px) {
  .emotion-1 {
    width: 60%;
  }
}

.emotion-2 {
  white-space: nowrap;
  line-height: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  text-transform: uppercase;
  font-size: 1rem;
  color: #002554;
}

.emotion-2 fontSet {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-4 {
  float: right;
  width: 155px;
  font-size: 1rem;
}

.emotion-5 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 1px solid transparent;
  border-radius: 0;
  font-size: 16px;
  font-weight: 400;
  letter-spacing: normal;
  min-height: 45px;
  max-height: auto;
  line-height: 1.125;
  padding: 12px 20px 11px;
  width: 100%;
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #002554;
  background: transparent;
  border-color: #002554;
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  box-sizing: border-box;
  cursor: pointer;
  text-align: center;
  text-transform: uppercase;
  -webkit-transition: all 200ms ease;
  transition: all 200ms ease;
  height: 2.75rem;
  background-color: #002554;
  border: 2px solid #002554;
  color: #FFF;
  font-size: 1.0625rem;
  font-weight: 700;
  letter-spacing: 1px;
  line-height: normal;
  padding: 0.0625em 0.59em 0.125em;
  color: #FFF;
  padding: 0.3rem;
  border: 2px solid #002554;
  background-color: inherited;
  font-size: 81.25%;
  font-weight: 700;
  letter-spacing: 0px;
  border-radius: 0px;
  text-align: center;
  min-height: 0px;
  height: 2.0625rem;
}

.emotion-5:focus {
  outline: none;
}

.emotion-5>span {
  padding: 1px 0;
}

.emotion-5:disabled,
.emotion-5:disabled:hover {
  cursor: default;
}

.emotion-5:hover {
  background-color: #3f5b7e;
  border-color: #3f5b7e;
  color: #FFF;
}

.emotion-5:disabled,
.emotion-5:disabled:hover {
  background-color: #CCC;
  border-color: transparent;
  color: #FFF;
}

.emotion-6 {
  clear: both;
  display: block;
  padding-top: 0.5rem;
  line-height: 1;
  width: 100%;
  text-transform: capitalize;
}

.emotion-7 {
  position: absolute;
  left: -999em;
  top: -999em;
}

<div
    class="emotion-0"
  >
    <div
      class="emotion-1"
    >
      <h2
        class="emotion-2"
      >
        <a
          aria-label=""
          class="map-link"
          href="https://www.google.com/maps/dir/?api=1&destination=150 N. State St.,CHICAGO,IL,60601"
          rel="noopener noreferrer"
          target="_blank"
        >
          150 N. STATE ST.
        </a>
      </h2>
      <div
        class="emotion-3"
      >
        (
        <span>
          0.0
        </span>
         Mi)
      </div>
    </div>
    <div
      class="emotion-4"
    >
      <button
        class="emotion-5"
      >
        changeStoreModal.seeAvailability
      </button>
    </div>
    <div
      class="emotion-6"
    >
      <span>
        150 n. state st., chicago, IL, 60601
      </span>
      <div
        class="emotion-7"
      >
        <span
          id="storeDescription-3997"
        >
          150 N. STATE ST.. Address: 150 n. state st., chicago, IL, 60601 0.0 miles away
        </span>
      </div>
    </div>
  </div>
</DocumentFragment>
`;

exports[`<StoreCard /> snapshots renders default state correctly GapFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  border-bottom: 1px solid #CCC;
  padding: 1rem 0;
  font-size: 0.875rem;
  color: #666;
  line-height: 1.38;
  font-weight: 400;
}

.emotion-1 {
  float: left;
  padding-right: 0.5rem;
  width: 36%;
}

@media (min-width: 320px) {
  .emotion-1 {
    width: 38%;
  }
}

@media (min-width: 350px) {
  .emotion-1 {
    width: 43%;
  }
}

@media (min-width: 375px) {
  .emotion-1 {
    width: 48%;
  }
}

@media (min-width: 415px) {
  .emotion-1 {
    width: 54%;
  }
}

@media (min-width: 475px) {
  .emotion-1 {
    width: 58%;
  }
}

@media (min-width: 500px) {
  .emotion-1 {
    width: 60%;
  }
}

.emotion-2 {
  white-space: nowrap;
  line-height: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  text-transform: uppercase;
  font-size: 1rem;
  color: #333;
}

.emotion-2 fontSet {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-4 {
  float: right;
  width: 155px;
  font-size: 1rem;
}

.emotion-5 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 1px solid transparent;
  border-radius: 0;
  font-size: 16px;
  font-weight: 400;
  letter-spacing: normal;
  min-height: 45px;
  max-height: auto;
  line-height: 1.125;
  padding: 12px 20px 11px;
  width: 100%;
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #002554;
  background: transparent;
  border-color: #002554;
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  box-sizing: border-box;
  cursor: pointer;
  text-align: center;
  text-transform: uppercase;
  -webkit-transition: all 200ms ease;
  transition: all 200ms ease;
  height: 2.75rem;
  background-color: #002554;
  border: 2px solid #002554;
  color: #FFF;
  font-size: 1.0625rem;
  font-weight: 700;
  letter-spacing: 1px;
  line-height: normal;
  padding: 0.0625em 0.59em 0.125em;
  color: #FFF;
  padding: 0.3rem;
  border: 2px solid #002554;
  background-color: inherited;
  font-size: 81.25%;
  font-weight: 700;
  letter-spacing: 0px;
  border-radius: 0px;
  text-align: center;
  min-height: 0px;
  height: 2.0625rem;
}

.emotion-5:focus {
  outline: none;
}

.emotion-5>span {
  padding: 1px 0;
}

.emotion-5:disabled,
.emotion-5:disabled:hover {
  cursor: default;
}

.emotion-5:hover {
  background-color: #3f5b7e;
  border-color: #3f5b7e;
  color: #FFF;
}

.emotion-5:disabled,
.emotion-5:disabled:hover {
  background-color: #CCC;
  border-color: transparent;
  color: #FFF;
}

.emotion-6 {
  clear: both;
  display: block;
  padding-top: 0.5rem;
  line-height: 1;
  width: 100%;
  text-transform: capitalize;
}

.emotion-7 {
  position: absolute;
  left: -999em;
  top: -999em;
}

<div
    class="emotion-0"
  >
    <div
      class="emotion-1"
    >
      <h2
        class="emotion-2"
      >
        <a
          aria-label=""
          class="map-link"
          href="https://www.google.com/maps/dir/?api=1&destination=150 N. State St.,CHICAGO,IL,60601"
          rel="noopener noreferrer"
          target="_blank"
        >
          150 N. STATE ST.
        </a>
      </h2>
      <div
        class="emotion-3"
      >
        (
        <span>
          0.0
        </span>
         Mi)
      </div>
    </div>
    <div
      class="emotion-4"
    >
      <button
        class="emotion-5"
      >
        changeStoreModal.seeAvailability
      </button>
    </div>
    <div
      class="emotion-6"
    >
      <span>
        150 n. state st., chicago, IL, 60601
      </span>
      <div
        class="emotion-7"
      >
        <span
          id="storeDescription-3997"
        >
          150 N. STATE ST.. Address: 150 n. state st., chicago, IL, 60601 0.0 miles away
        </span>
      </div>
    </div>
  </div>
</DocumentFragment>
`;

exports[`<StoreCard /> snapshots renders default state correctly GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  border-bottom: 1px solid #CCC;
  padding: 1rem 0;
  font-size: 0.875rem;
  color: #666;
  line-height: 1.38;
  font-weight: 400;
}

.emotion-1 {
  float: left;
  padding-right: 0.5rem;
  width: 36%;
}

@media (min-width: 320px) {
  .emotion-1 {
    width: 38%;
  }
}

@media (min-width: 350px) {
  .emotion-1 {
    width: 43%;
  }
}

@media (min-width: 375px) {
  .emotion-1 {
    width: 48%;
  }
}

@media (min-width: 415px) {
  .emotion-1 {
    width: 54%;
  }
}

@media (min-width: 475px) {
  .emotion-1 {
    width: 58%;
  }
}

@media (min-width: 500px) {
  .emotion-1 {
    width: 60%;
  }
}

.emotion-2 {
  white-space: nowrap;
  line-height: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  text-transform: uppercase;
  font-size: 1rem;
  color: #333;
}

.emotion-2 fontSet {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-4 {
  float: right;
  width: 155px;
  font-size: 1rem;
}

.emotion-5 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 1px solid transparent;
  border-radius: 0;
  font-size: 16px;
  font-weight: 400;
  letter-spacing: normal;
  min-height: 45px;
  max-height: auto;
  line-height: 1.125;
  padding: 12px 20px 11px;
  width: 100%;
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #002554;
  background: transparent;
  border-color: #002554;
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  box-sizing: border-box;
  cursor: pointer;
  text-align: center;
  text-transform: uppercase;
  -webkit-transition: all 200ms ease;
  transition: all 200ms ease;
  height: 2.75rem;
  background-color: #002554;
  border: 2px solid #002554;
  color: #FFF;
  font-size: 1.0625rem;
  font-weight: 700;
  letter-spacing: 1px;
  line-height: normal;
  padding: 0.0625em 0.59em 0.125em;
  color: #FFF;
  padding: 0.3rem;
  border: 2px solid #002554;
  background-color: inherited;
  font-size: 81.25%;
  font-weight: 700;
  letter-spacing: 0px;
  border-radius: 0px;
  text-align: center;
  min-height: 0px;
  height: 2.0625rem;
}

.emotion-5:focus {
  outline: none;
}

.emotion-5>span {
  padding: 1px 0;
}

.emotion-5:disabled,
.emotion-5:disabled:hover {
  cursor: default;
}

.emotion-5:hover {
  background-color: #3f5b7e;
  border-color: #3f5b7e;
  color: #FFF;
}

.emotion-5:disabled,
.emotion-5:disabled:hover {
  background-color: #CCC;
  border-color: transparent;
  color: #FFF;
}

.emotion-6 {
  clear: both;
  display: block;
  padding-top: 0.5rem;
  line-height: 1;
  width: 100%;
  text-transform: capitalize;
}

.emotion-7 {
  position: absolute;
  left: -999em;
  top: -999em;
}

<div
    class="emotion-0"
  >
    <div
      class="emotion-1"
    >
      <h2
        class="emotion-2"
      >
        <a
          aria-label=""
          class="map-link"
          href="https://www.google.com/maps/dir/?api=1&destination=150 N. State St.,CHICAGO,IL,60601"
          rel="noopener noreferrer"
          target="_blank"
        >
          150 N. STATE ST.
        </a>
      </h2>
      <div
        class="emotion-3"
      >
        (
        <span>
          0.0
        </span>
         Mi)
      </div>
    </div>
    <div
      class="emotion-4"
    >
      <button
        class="emotion-5"
      >
        changeStoreModal.seeAvailability
      </button>
    </div>
    <div
      class="emotion-6"
    >
      <span>
        150 n. state st., chicago, IL, 60601
      </span>
      <div
        class="emotion-7"
      >
        <span
          id="storeDescription-3997"
        >
          150 N. STATE ST.. Address: 150 n. state st., chicago, IL, 60601 0.0 miles away
        </span>
      </div>
    </div>
  </div>
</DocumentFragment>
`;

exports[`<StoreCard /> snapshots renders default state correctly OldNavy 1`] = `
<DocumentFragment>
  .emotion-0 {
  border-bottom: 1px solid #CCC;
  padding: 1rem 0;
  font-size: 0.875rem;
  color: #666;
  line-height: 1.38;
  font-weight: 400;
}

.emotion-1 {
  float: left;
  padding-right: 0.5rem;
  width: 36%;
}

@media (min-width: 320px) {
  .emotion-1 {
    width: 38%;
  }
}

@media (min-width: 350px) {
  .emotion-1 {
    width: 43%;
  }
}

@media (min-width: 375px) {
  .emotion-1 {
    width: 48%;
  }
}

@media (min-width: 415px) {
  .emotion-1 {
    width: 54%;
  }
}

@media (min-width: 475px) {
  .emotion-1 {
    width: 58%;
  }
}

@media (min-width: 500px) {
  .emotion-1 {
    width: 60%;
  }
}

.emotion-2 {
  white-space: nowrap;
  line-height: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  text-transform: uppercase;
  font-size: 1rem;
  color: #003764;
}

.emotion-2 fontSet {
  font-family: 'Gotham','Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-4 {
  float: right;
  width: 155px;
  font-size: 1rem;
}

.emotion-5 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 700;
  letter-spacing: 1.6800000000000002px;
  min-height: 44px;
  max-height: auto;
  line-height: 1.1428571428571428;
  padding: 12px 22px;
  width: 100%;
  font-family: 'Gotham','Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #003764;
  background: transparent;
  border-color: #003764;
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  box-sizing: border-box;
  cursor: pointer;
  text-align: center;
  text-transform: uppercase;
  -webkit-transition: all 200ms ease;
  transition: all 200ms ease;
  height: 2.75rem;
  background-color: #003764;
  border: none;
  color: #FFF;
  font-size: 1.1875rem;
  font-weight: 700;
  letter-spacing: 1px;
  line-height: normal;
  padding: 0.45em;
  color: #FFF;
  padding: 0.3rem;
  border: 2px solid #003764;
  background-color: inherited;
  font-size: 81.25%;
  font-weight: 700;
  letter-spacing: 0px;
  border-radius: 0px;
  text-align: center;
  min-height: 0px;
  height: 2.0625rem;
}

.emotion-5:focus {
  outline: none;
}

.emotion-5>span {
  padding: 1px 0;
}

.emotion-5:disabled,
.emotion-5:disabled:hover {
  cursor: default;
}

.emotion-5:hover {
  background-color: #3f698a;
  border-color: transparent;
  color: #FFF;
}

.emotion-5:disabled,
.emotion-5:disabled:hover {
  background-color: #CCC;
  border-color: transparent;
  color: #FFF;
}

.emotion-6 {
  clear: both;
  display: block;
  padding-top: 0.5rem;
  line-height: 1;
  width: 100%;
  text-transform: capitalize;
}

.emotion-7 {
  position: absolute;
  left: -999em;
  top: -999em;
}

<div
    class="emotion-0"
  >
    <div
      class="emotion-1"
    >
      <h2
        class="emotion-2"
      >
        <a
          aria-label=""
          class="map-link"
          href="https://www.google.com/maps/dir/?api=1&destination=150 N. State St.,CHICAGO,IL,60601"
          rel="noopener noreferrer"
          target="_blank"
        >
          150 N. STATE ST.
        </a>
      </h2>
      <div
        class="emotion-3"
      >
        (
        <span>
          0.0
        </span>
         Mi)
      </div>
    </div>
    <div
      class="emotion-4"
    >
      <button
        class="emotion-5"
      >
        changeStoreModal.seeAvailability
      </button>
    </div>
    <div
      class="emotion-6"
    >
      <span>
        150 n. state st., chicago, IL, 60601
      </span>
      <div
        class="emotion-7"
      >
        <span
          id="storeDescription-3997"
        >
          150 N. STATE ST.. Address: 150 n. state st., chicago, IL, 60601 0.0 miles away
        </span>
      </div>
    </div>
  </div>
</DocumentFragment>
`;

exports[`<StoreCard /> snapshots renders default state correctly OldNavy in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  border-bottom: 1px solid #CCC;
  padding: 1rem 0;
  font-size: 0.875rem;
  color: #666;
  line-height: 1.38;
  font-weight: 400;
}

.emotion-1 {
  float: left;
  padding-right: 0.5rem;
  width: 36%;
}

@media (min-width: 320px) {
  .emotion-1 {
    width: 38%;
  }
}

@media (min-width: 350px) {
  .emotion-1 {
    width: 43%;
  }
}

@media (min-width: 375px) {
  .emotion-1 {
    width: 48%;
  }
}

@media (min-width: 415px) {
  .emotion-1 {
    width: 54%;
  }
}

@media (min-width: 475px) {
  .emotion-1 {
    width: 58%;
  }
}

@media (min-width: 500px) {
  .emotion-1 {
    width: 60%;
  }
}

.emotion-2 {
  white-space: nowrap;
  line-height: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  text-transform: uppercase;
  font-size: 1rem;
  color: #003764;
}

.emotion-2 fontSet {
  font-family: 'Gotham','Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-4 {
  float: right;
  width: 155px;
  font-size: 1rem;
}

.emotion-5 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 700;
  letter-spacing: 1.6800000000000002px;
  min-height: 44px;
  max-height: auto;
  line-height: 1.1428571428571428;
  padding: 12px 22px;
  width: 100%;
  font-family: 'Gotham','Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #003764;
  background: transparent;
  border-color: #003764;
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  box-sizing: border-box;
  cursor: pointer;
  text-align: center;
  text-transform: uppercase;
  -webkit-transition: all 200ms ease;
  transition: all 200ms ease;
  height: 2.75rem;
  background-color: #003764;
  border: none;
  color: #FFF;
  font-size: 1.1875rem;
  font-weight: 700;
  letter-spacing: 1px;
  line-height: normal;
  padding: 0.45em;
  color: #FFF;
  padding: 0.3rem;
  border: 2px solid #003764;
  background-color: inherited;
  font-size: 81.25%;
  font-weight: 700;
  letter-spacing: 0px;
  border-radius: 0px;
  text-align: center;
  min-height: 0px;
  height: 2.0625rem;
}

.emotion-5:focus {
  outline: none;
}

.emotion-5>span {
  padding: 1px 0;
}

.emotion-5:disabled,
.emotion-5:disabled:hover {
  cursor: default;
}

.emotion-5:hover {
  background-color: #3f698a;
  border-color: transparent;
  color: #FFF;
}

.emotion-5:disabled,
.emotion-5:disabled:hover {
  background-color: #CCC;
  border-color: transparent;
  color: #FFF;
}

.emotion-6 {
  clear: both;
  display: block;
  padding-top: 0.5rem;
  line-height: 1;
  width: 100%;
  text-transform: capitalize;
}

.emotion-7 {
  position: absolute;
  left: -999em;
  top: -999em;
}

<div
    class="emotion-0"
  >
    <div
      class="emotion-1"
    >
      <h2
        class="emotion-2"
      >
        <a
          aria-label=""
          class="map-link"
          href="https://www.google.com/maps/dir/?api=1&destination=150 N. State St.,CHICAGO,IL,60601"
          rel="noopener noreferrer"
          target="_blank"
        >
          150 N. STATE ST.
        </a>
      </h2>
      <div
        class="emotion-3"
      >
        (
        <span>
          0.0
        </span>
         Mi)
      </div>
    </div>
    <div
      class="emotion-4"
    >
      <button
        class="emotion-5"
      >
        changeStoreModal.seeAvailability
      </button>
    </div>
    <div
      class="emotion-6"
    >
      <span>
        150 n. state st., chicago, IL, 60601
      </span>
      <div
        class="emotion-7"
      >
        <span
          id="storeDescription-3997"
        >
          150 N. STATE ST.. Address: 150 n. state st., chicago, IL, 60601 0.0 miles away
        </span>
      </div>
    </div>
  </div>
</DocumentFragment>
`;
