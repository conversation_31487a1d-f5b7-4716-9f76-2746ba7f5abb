// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`<Checkbox/> BR Redesign Should match snapshot for Brand: "br" 1`] = `
.emotion-0 {
  opacity: 0;
  position: absolute;
}

.emotion-0[data-focus-visible-added]+div {
  outline: 0;
  box-shadow: 0 0 0 0.1875rem #CCC;
}

<input
  class="emotion-0"
  type="checkbox"
/>
`;

exports[`<Checkbox/> BR Redesign Should match snapshot for Brand: "brfs" 1`] = `
.emotion-0 {
  opacity: 0;
  position: absolute;
}

.emotion-0[data-focus-visible-added]+div {
  outline: 0;
  box-shadow: 0 0 0 0.1875rem #CCC;
}

<input
  class="emotion-0"
  type="checkbox"
/>
`;

exports[`<Checkbox/> Checkbox with BR color 2023 renders default state correctly Athleta 1`] = `
<DocumentFragment>
  .emotion-0 {
  margin: 0 0 1.5rem;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  max-width: calc(100% - 2.3025rem);
  line-height: 0.75rem;
  font-family: 'Lynstone',Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  cursor: pointer;
  color: #000;
  font-size: 0.875rem;
  font-weight: 400;
}

.emotion-0:hover {
  -webkit-text-decoration: underline;
  text-decoration: underline;
}

.emotion-1 {
  opacity: 0;
  position: absolute;
}

.emotion-1[data-focus-visible-added]+div {
  outline: 0;
  box-shadow: 0 0 0 0.1875rem #CCC;
}

.emotion-2 {
  box-sizing: border-box;
  position: relative;
  display: block;
  margin-right: 0.75rem;
  min-width: 1.375rem;
  min-height: 1.375rem;
  border: 0.0625rem solid #000;
}

.emotion-2::before {
  content: "";
  display: block;
  position: absolute;
  width: calc(100% + 0.0625rem);
  height: calc(100% + 0.0625rem);
  -webkit-transform: scale(0);
  -moz-transform: scale(0);
  -ms-transform: scale(0);
  transform: scale(0);
  background-color: #000;
}

.emotion-2::after {
  content: "";
  display: block;
  width: 100%;
  height: 100%;
  position: absolute;
  background-repeat: no-repeat;
  background-position-x: 0;
  background-position-y: 0.125rem;
  -webkit-background-size: 1.875rem;
  background-size: 1.875rem;
  -webkit-background-clip: border-box;
  background-clip: border-box;
  background-color: transparent;
}

<label
    class="emotion-0"
    id="checkbox-id"
  >
    <input
      class="emotion-1"
      type="checkbox"
    />
    <div
      aria-checked="false"
      aria-hidden="true"
      aria-labelledby="checkbox-id"
      class="emotion-2"
      role="checkbox"
    />
    <div>
      I am default text
    </div>
  </label>
  <link
    data-testid="font-tracker"
    href="https://fast.fonts.net/t/1.css?apiType=css&projectid=0e5ae342-8d48-4e7a-9491-e82fee9dcc17"
    media="print"
    rel="stylesheet"
  />
</DocumentFragment>
`;

exports[`<Checkbox/> Checkbox with BR color 2023 renders default state correctly Athleta in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  margin: 0 0 1.5rem;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  max-width: calc(100% - 2.3025rem);
  line-height: 0.75rem;
  font-family: 'Lynstone',Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  cursor: pointer;
  color: #000;
  font-size: 0.875rem;
  font-weight: 400;
}

.emotion-0:hover {
  -webkit-text-decoration: underline;
  text-decoration: underline;
}

.emotion-1 {
  opacity: 0;
  position: absolute;
}

.emotion-1[data-focus-visible-added]+div {
  outline: 0;
  box-shadow: 0 0 0 0.1875rem #CCC;
}

.emotion-2 {
  box-sizing: border-box;
  position: relative;
  display: block;
  margin-right: 0.75rem;
  min-width: 1.375rem;
  min-height: 1.375rem;
  border: 0.0625rem solid #000;
}

.emotion-2::before {
  content: "";
  display: block;
  position: absolute;
  width: calc(100% + 0.0625rem);
  height: calc(100% + 0.0625rem);
  -webkit-transform: scale(0);
  -moz-transform: scale(0);
  -ms-transform: scale(0);
  transform: scale(0);
  background-color: #000;
}

.emotion-2::after {
  content: "";
  display: block;
  width: 100%;
  height: 100%;
  position: absolute;
  background-repeat: no-repeat;
  background-position-x: 0;
  background-position-y: 0.125rem;
  -webkit-background-size: 1.875rem;
  background-size: 1.875rem;
  -webkit-background-clip: border-box;
  background-clip: border-box;
  background-color: transparent;
}

<label
    class="emotion-0"
    id="checkbox-id"
  >
    <input
      class="emotion-1"
      type="checkbox"
    />
    <div
      aria-checked="false"
      aria-hidden="true"
      aria-labelledby="checkbox-id"
      class="emotion-2"
      role="checkbox"
    />
    <div>
      I am default text
    </div>
  </label>
  <link
    data-testid="font-tracker"
    href="https://fast.fonts.net/t/1.css?apiType=css&projectid=0e5ae342-8d48-4e7a-9491-e82fee9dcc17"
    media="print"
    rel="stylesheet"
  />
</DocumentFragment>
`;

exports[`<Checkbox/> Checkbox with BR color 2023 renders default state correctly BananaRepublic 1`] = `
<DocumentFragment>
  .emotion-0 {
  margin: 0 0 1.5rem;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  max-width: calc(100% - 2.3025rem);
  line-height: 0.75rem;
  font-family: 'Lynstone',Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  cursor: pointer;
  color: #000;
  font-size: 0.875rem;
  font-weight: 400;
}

.emotion-0:hover {
  -webkit-text-decoration: underline;
  text-decoration: underline;
}

.emotion-1 {
  opacity: 0;
  position: absolute;
}

.emotion-1[data-focus-visible-added]+div {
  outline: 0;
  box-shadow: 0 0 0 0.1875rem #CCC;
}

.emotion-2 {
  box-sizing: border-box;
  position: relative;
  display: block;
  margin-right: 0.75rem;
  min-width: 1.375rem;
  min-height: 1.375rem;
  border: 0.0625rem solid #000;
}

.emotion-2::before {
  content: "";
  display: block;
  position: absolute;
  width: calc(100% + 0.0625rem);
  height: calc(100% + 0.0625rem);
  -webkit-transform: scale(0);
  -moz-transform: scale(0);
  -ms-transform: scale(0);
  transform: scale(0);
  background-color: #000;
}

.emotion-2::after {
  content: "";
  display: block;
  width: 100%;
  height: 100%;
  position: absolute;
  background-repeat: no-repeat;
  background-position-x: 0;
  background-position-y: 0.125rem;
  -webkit-background-size: 1.875rem;
  background-size: 1.875rem;
  -webkit-background-clip: border-box;
  background-clip: border-box;
  background-color: transparent;
}

<label
    class="emotion-0"
    id="checkbox-id"
  >
    <input
      class="emotion-1"
      type="checkbox"
    />
    <div
      aria-checked="false"
      aria-hidden="true"
      aria-labelledby="checkbox-id"
      class="emotion-2"
      role="checkbox"
    />
    <div>
      I am default text
    </div>
  </label>
  <link
    data-testid="font-tracker"
    href="https://fast.fonts.net/t/1.css?apiType=css&projectid=0e5ae342-8d48-4e7a-9491-e82fee9dcc17"
    media="print"
    rel="stylesheet"
  />
</DocumentFragment>
`;

exports[`<Checkbox/> Checkbox with BR color 2023 renders default state correctly BananaRepublic in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  margin: 0 0 1.5rem;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  max-width: calc(100% - 2.3025rem);
  line-height: 0.75rem;
  font-family: 'Lynstone',Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  cursor: pointer;
  color: #000;
  font-size: 0.875rem;
  font-weight: 400;
}

.emotion-0:hover {
  -webkit-text-decoration: underline;
  text-decoration: underline;
}

.emotion-1 {
  opacity: 0;
  position: absolute;
}

.emotion-1[data-focus-visible-added]+div {
  outline: 0;
  box-shadow: 0 0 0 0.1875rem #CCC;
}

.emotion-2 {
  box-sizing: border-box;
  position: relative;
  display: block;
  margin-right: 0.75rem;
  min-width: 1.375rem;
  min-height: 1.375rem;
  border: 0.0625rem solid #000;
}

.emotion-2::before {
  content: "";
  display: block;
  position: absolute;
  width: calc(100% + 0.0625rem);
  height: calc(100% + 0.0625rem);
  -webkit-transform: scale(0);
  -moz-transform: scale(0);
  -ms-transform: scale(0);
  transform: scale(0);
  background-color: #000;
}

.emotion-2::after {
  content: "";
  display: block;
  width: 100%;
  height: 100%;
  position: absolute;
  background-repeat: no-repeat;
  background-position-x: 0;
  background-position-y: 0.125rem;
  -webkit-background-size: 1.875rem;
  background-size: 1.875rem;
  -webkit-background-clip: border-box;
  background-clip: border-box;
  background-color: transparent;
}

<label
    class="emotion-0"
    id="checkbox-id"
  >
    <input
      class="emotion-1"
      type="checkbox"
    />
    <div
      aria-checked="false"
      aria-hidden="true"
      aria-labelledby="checkbox-id"
      class="emotion-2"
      role="checkbox"
    />
    <div>
      I am default text
    </div>
  </label>
  <link
    data-testid="font-tracker"
    href="https://fast.fonts.net/t/1.css?apiType=css&projectid=0e5ae342-8d48-4e7a-9491-e82fee9dcc17"
    media="print"
    rel="stylesheet"
  />
</DocumentFragment>
`;

exports[`<Checkbox/> Checkbox with BR color 2023 renders default state correctly BananaRepublicFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  margin: 0 0 1.5rem;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  max-width: calc(100% - 2.3025rem);
  line-height: 0.75rem;
  font-family: 'Lynstone',Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  cursor: pointer;
  color: #000;
  font-size: 0.875rem;
  font-weight: 400;
}

.emotion-0:hover {
  -webkit-text-decoration: underline;
  text-decoration: underline;
}

.emotion-1 {
  opacity: 0;
  position: absolute;
}

.emotion-1[data-focus-visible-added]+div {
  outline: 0;
  box-shadow: 0 0 0 0.1875rem #CCC;
}

.emotion-2 {
  box-sizing: border-box;
  position: relative;
  display: block;
  margin-right: 0.75rem;
  min-width: 1.375rem;
  min-height: 1.375rem;
  border: 0.0625rem solid #000;
}

.emotion-2::before {
  content: "";
  display: block;
  position: absolute;
  width: calc(100% + 0.0625rem);
  height: calc(100% + 0.0625rem);
  -webkit-transform: scale(0);
  -moz-transform: scale(0);
  -ms-transform: scale(0);
  transform: scale(0);
  background-color: #000;
}

.emotion-2::after {
  content: "";
  display: block;
  width: 100%;
  height: 100%;
  position: absolute;
  background-repeat: no-repeat;
  background-position-x: 0;
  background-position-y: 0.125rem;
  -webkit-background-size: 1.875rem;
  background-size: 1.875rem;
  -webkit-background-clip: border-box;
  background-clip: border-box;
  background-color: transparent;
}

<label
    class="emotion-0"
    id="checkbox-id"
  >
    <input
      class="emotion-1"
      type="checkbox"
    />
    <div
      aria-checked="false"
      aria-hidden="true"
      aria-labelledby="checkbox-id"
      class="emotion-2"
      role="checkbox"
    />
    <div>
      I am default text
    </div>
  </label>
  <link
    data-testid="font-tracker"
    href="https://fast.fonts.net/t/1.css?apiType=css&projectid=0e5ae342-8d48-4e7a-9491-e82fee9dcc17"
    media="print"
    rel="stylesheet"
  />
</DocumentFragment>
`;

exports[`<Checkbox/> Checkbox with BR color 2023 renders default state correctly BananaRepublicFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  margin: 0 0 1.5rem;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  max-width: calc(100% - 2.3025rem);
  line-height: 0.75rem;
  font-family: 'Lynstone',Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  cursor: pointer;
  color: #000;
  font-size: 0.875rem;
  font-weight: 400;
}

.emotion-0:hover {
  -webkit-text-decoration: underline;
  text-decoration: underline;
}

.emotion-1 {
  opacity: 0;
  position: absolute;
}

.emotion-1[data-focus-visible-added]+div {
  outline: 0;
  box-shadow: 0 0 0 0.1875rem #CCC;
}

.emotion-2 {
  box-sizing: border-box;
  position: relative;
  display: block;
  margin-right: 0.75rem;
  min-width: 1.375rem;
  min-height: 1.375rem;
  border: 0.0625rem solid #000;
}

.emotion-2::before {
  content: "";
  display: block;
  position: absolute;
  width: calc(100% + 0.0625rem);
  height: calc(100% + 0.0625rem);
  -webkit-transform: scale(0);
  -moz-transform: scale(0);
  -ms-transform: scale(0);
  transform: scale(0);
  background-color: #000;
}

.emotion-2::after {
  content: "";
  display: block;
  width: 100%;
  height: 100%;
  position: absolute;
  background-repeat: no-repeat;
  background-position-x: 0;
  background-position-y: 0.125rem;
  -webkit-background-size: 1.875rem;
  background-size: 1.875rem;
  -webkit-background-clip: border-box;
  background-clip: border-box;
  background-color: transparent;
}

<label
    class="emotion-0"
    id="checkbox-id"
  >
    <input
      class="emotion-1"
      type="checkbox"
    />
    <div
      aria-checked="false"
      aria-hidden="true"
      aria-labelledby="checkbox-id"
      class="emotion-2"
      role="checkbox"
    />
    <div>
      I am default text
    </div>
  </label>
  <link
    data-testid="font-tracker"
    href="https://fast.fonts.net/t/1.css?apiType=css&projectid=0e5ae342-8d48-4e7a-9491-e82fee9dcc17"
    media="print"
    rel="stylesheet"
  />
</DocumentFragment>
`;

exports[`<Checkbox/> Checkbox with BR color 2023 renders default state correctly Gap 1`] = `
<DocumentFragment>
  .emotion-0 {
  margin: 0 0 1.5rem;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  max-width: calc(100% - 2.3025rem);
  line-height: 0.75rem;
  font-family: 'Lynstone',Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  cursor: pointer;
  color: #000;
  font-size: 0.875rem;
  font-weight: 400;
}

.emotion-0:hover {
  -webkit-text-decoration: underline;
  text-decoration: underline;
}

.emotion-1 {
  opacity: 0;
  position: absolute;
}

.emotion-1[data-focus-visible-added]+div {
  outline: 0;
  box-shadow: 0 0 0 0.1875rem #CCC;
}

.emotion-2 {
  box-sizing: border-box;
  position: relative;
  display: block;
  margin-right: 0.75rem;
  min-width: 1.375rem;
  min-height: 1.375rem;
  border: 0.0625rem solid #000;
}

.emotion-2::before {
  content: "";
  display: block;
  position: absolute;
  width: calc(100% + 0.0625rem);
  height: calc(100% + 0.0625rem);
  -webkit-transform: scale(0);
  -moz-transform: scale(0);
  -ms-transform: scale(0);
  transform: scale(0);
  background-color: #000;
}

.emotion-2::after {
  content: "";
  display: block;
  width: 100%;
  height: 100%;
  position: absolute;
  background-repeat: no-repeat;
  background-position-x: 0;
  background-position-y: 0.125rem;
  -webkit-background-size: 1.875rem;
  background-size: 1.875rem;
  -webkit-background-clip: border-box;
  background-clip: border-box;
  background-color: transparent;
}

<label
    class="emotion-0"
    id="checkbox-id"
  >
    <input
      class="emotion-1"
      type="checkbox"
    />
    <div
      aria-checked="false"
      aria-hidden="true"
      aria-labelledby="checkbox-id"
      class="emotion-2"
      role="checkbox"
    />
    <div>
      I am default text
    </div>
  </label>
  <link
    data-testid="font-tracker"
    href="https://fast.fonts.net/t/1.css?apiType=css&projectid=0e5ae342-8d48-4e7a-9491-e82fee9dcc17"
    media="print"
    rel="stylesheet"
  />
</DocumentFragment>
`;

exports[`<Checkbox/> Checkbox with BR color 2023 renders default state correctly Gap in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  margin: 0 0 1.5rem;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  max-width: calc(100% - 2.3025rem);
  line-height: 0.75rem;
  font-family: 'Lynstone',Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  cursor: pointer;
  color: #000;
  font-size: 0.875rem;
  font-weight: 400;
}

.emotion-0:hover {
  -webkit-text-decoration: underline;
  text-decoration: underline;
}

.emotion-1 {
  opacity: 0;
  position: absolute;
}

.emotion-1[data-focus-visible-added]+div {
  outline: 0;
  box-shadow: 0 0 0 0.1875rem #CCC;
}

.emotion-2 {
  box-sizing: border-box;
  position: relative;
  display: block;
  margin-right: 0.75rem;
  min-width: 1.375rem;
  min-height: 1.375rem;
  border: 0.0625rem solid #000;
}

.emotion-2::before {
  content: "";
  display: block;
  position: absolute;
  width: calc(100% + 0.0625rem);
  height: calc(100% + 0.0625rem);
  -webkit-transform: scale(0);
  -moz-transform: scale(0);
  -ms-transform: scale(0);
  transform: scale(0);
  background-color: #000;
}

.emotion-2::after {
  content: "";
  display: block;
  width: 100%;
  height: 100%;
  position: absolute;
  background-repeat: no-repeat;
  background-position-x: 0;
  background-position-y: 0.125rem;
  -webkit-background-size: 1.875rem;
  background-size: 1.875rem;
  -webkit-background-clip: border-box;
  background-clip: border-box;
  background-color: transparent;
}

<label
    class="emotion-0"
    id="checkbox-id"
  >
    <input
      class="emotion-1"
      type="checkbox"
    />
    <div
      aria-checked="false"
      aria-hidden="true"
      aria-labelledby="checkbox-id"
      class="emotion-2"
      role="checkbox"
    />
    <div>
      I am default text
    </div>
  </label>
  <link
    data-testid="font-tracker"
    href="https://fast.fonts.net/t/1.css?apiType=css&projectid=0e5ae342-8d48-4e7a-9491-e82fee9dcc17"
    media="print"
    rel="stylesheet"
  />
</DocumentFragment>
`;

exports[`<Checkbox/> Checkbox with BR color 2023 renders default state correctly GapFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  margin: 0 0 1.5rem;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  max-width: calc(100% - 2.3025rem);
  line-height: 0.75rem;
  font-family: 'Lynstone',Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  cursor: pointer;
  color: #000;
  font-size: 0.875rem;
  font-weight: 400;
}

.emotion-0:hover {
  -webkit-text-decoration: underline;
  text-decoration: underline;
}

.emotion-1 {
  opacity: 0;
  position: absolute;
}

.emotion-1[data-focus-visible-added]+div {
  outline: 0;
  box-shadow: 0 0 0 0.1875rem #CCC;
}

.emotion-2 {
  box-sizing: border-box;
  position: relative;
  display: block;
  margin-right: 0.75rem;
  min-width: 1.375rem;
  min-height: 1.375rem;
  border: 0.0625rem solid #000;
}

.emotion-2::before {
  content: "";
  display: block;
  position: absolute;
  width: calc(100% + 0.0625rem);
  height: calc(100% + 0.0625rem);
  -webkit-transform: scale(0);
  -moz-transform: scale(0);
  -ms-transform: scale(0);
  transform: scale(0);
  background-color: #000;
}

.emotion-2::after {
  content: "";
  display: block;
  width: 100%;
  height: 100%;
  position: absolute;
  background-repeat: no-repeat;
  background-position-x: 0;
  background-position-y: 0.125rem;
  -webkit-background-size: 1.875rem;
  background-size: 1.875rem;
  -webkit-background-clip: border-box;
  background-clip: border-box;
  background-color: transparent;
}

<label
    class="emotion-0"
    id="checkbox-id"
  >
    <input
      class="emotion-1"
      type="checkbox"
    />
    <div
      aria-checked="false"
      aria-hidden="true"
      aria-labelledby="checkbox-id"
      class="emotion-2"
      role="checkbox"
    />
    <div>
      I am default text
    </div>
  </label>
  <link
    data-testid="font-tracker"
    href="https://fast.fonts.net/t/1.css?apiType=css&projectid=0e5ae342-8d48-4e7a-9491-e82fee9dcc17"
    media="print"
    rel="stylesheet"
  />
</DocumentFragment>
`;

exports[`<Checkbox/> Checkbox with BR color 2023 renders default state correctly GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  margin: 0 0 1.5rem;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  max-width: calc(100% - 2.3025rem);
  line-height: 0.75rem;
  font-family: 'Lynstone',Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  cursor: pointer;
  color: #000;
  font-size: 0.875rem;
  font-weight: 400;
}

.emotion-0:hover {
  -webkit-text-decoration: underline;
  text-decoration: underline;
}

.emotion-1 {
  opacity: 0;
  position: absolute;
}

.emotion-1[data-focus-visible-added]+div {
  outline: 0;
  box-shadow: 0 0 0 0.1875rem #CCC;
}

.emotion-2 {
  box-sizing: border-box;
  position: relative;
  display: block;
  margin-right: 0.75rem;
  min-width: 1.375rem;
  min-height: 1.375rem;
  border: 0.0625rem solid #000;
}

.emotion-2::before {
  content: "";
  display: block;
  position: absolute;
  width: calc(100% + 0.0625rem);
  height: calc(100% + 0.0625rem);
  -webkit-transform: scale(0);
  -moz-transform: scale(0);
  -ms-transform: scale(0);
  transform: scale(0);
  background-color: #000;
}

.emotion-2::after {
  content: "";
  display: block;
  width: 100%;
  height: 100%;
  position: absolute;
  background-repeat: no-repeat;
  background-position-x: 0;
  background-position-y: 0.125rem;
  -webkit-background-size: 1.875rem;
  background-size: 1.875rem;
  -webkit-background-clip: border-box;
  background-clip: border-box;
  background-color: transparent;
}

<label
    class="emotion-0"
    id="checkbox-id"
  >
    <input
      class="emotion-1"
      type="checkbox"
    />
    <div
      aria-checked="false"
      aria-hidden="true"
      aria-labelledby="checkbox-id"
      class="emotion-2"
      role="checkbox"
    />
    <div>
      I am default text
    </div>
  </label>
  <link
    data-testid="font-tracker"
    href="https://fast.fonts.net/t/1.css?apiType=css&projectid=0e5ae342-8d48-4e7a-9491-e82fee9dcc17"
    media="print"
    rel="stylesheet"
  />
</DocumentFragment>
`;

exports[`<Checkbox/> Checkbox with BR color 2023 renders default state correctly OldNavy 1`] = `
<DocumentFragment>
  .emotion-0 {
  margin: 0 0 1.5rem;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  max-width: calc(100% - 2.3025rem);
  line-height: 0.75rem;
  font-family: 'Lynstone',Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  cursor: pointer;
  color: #000;
  font-size: 0.875rem;
  font-weight: 400;
}

.emotion-0:hover {
  -webkit-text-decoration: underline;
  text-decoration: underline;
}

.emotion-1 {
  opacity: 0;
  position: absolute;
}

.emotion-1[data-focus-visible-added]+div {
  outline: 0;
  box-shadow: 0 0 0 0.1875rem #CCC;
}

.emotion-2 {
  box-sizing: border-box;
  position: relative;
  display: block;
  margin-right: 0.75rem;
  min-width: 1.375rem;
  min-height: 1.375rem;
  border: 0.0625rem solid #000;
}

.emotion-2::before {
  content: "";
  display: block;
  position: absolute;
  width: calc(100% + 0.0625rem);
  height: calc(100% + 0.0625rem);
  -webkit-transform: scale(0);
  -moz-transform: scale(0);
  -ms-transform: scale(0);
  transform: scale(0);
  background-color: #000;
}

.emotion-2::after {
  content: "";
  display: block;
  width: 100%;
  height: 100%;
  position: absolute;
  background-repeat: no-repeat;
  background-position-x: 0;
  background-position-y: 0.125rem;
  -webkit-background-size: 1.875rem;
  background-size: 1.875rem;
  -webkit-background-clip: border-box;
  background-clip: border-box;
  background-color: transparent;
}

<label
    class="emotion-0"
    id="checkbox-id"
  >
    <input
      class="emotion-1"
      type="checkbox"
    />
    <div
      aria-checked="false"
      aria-hidden="true"
      aria-labelledby="checkbox-id"
      class="emotion-2"
      role="checkbox"
    />
    <div>
      I am default text
    </div>
  </label>
  <link
    data-testid="font-tracker"
    href="https://fast.fonts.net/t/1.css?apiType=css&projectid=0e5ae342-8d48-4e7a-9491-e82fee9dcc17"
    media="print"
    rel="stylesheet"
  />
</DocumentFragment>
`;

exports[`<Checkbox/> Checkbox with BR color 2023 renders default state correctly OldNavy in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  margin: 0 0 1.5rem;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  max-width: calc(100% - 2.3025rem);
  line-height: 0.75rem;
  font-family: 'Lynstone',Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  cursor: pointer;
  color: #000;
  font-size: 0.875rem;
  font-weight: 400;
}

.emotion-0:hover {
  -webkit-text-decoration: underline;
  text-decoration: underline;
}

.emotion-1 {
  opacity: 0;
  position: absolute;
}

.emotion-1[data-focus-visible-added]+div {
  outline: 0;
  box-shadow: 0 0 0 0.1875rem #CCC;
}

.emotion-2 {
  box-sizing: border-box;
  position: relative;
  display: block;
  margin-right: 0.75rem;
  min-width: 1.375rem;
  min-height: 1.375rem;
  border: 0.0625rem solid #000;
}

.emotion-2::before {
  content: "";
  display: block;
  position: absolute;
  width: calc(100% + 0.0625rem);
  height: calc(100% + 0.0625rem);
  -webkit-transform: scale(0);
  -moz-transform: scale(0);
  -ms-transform: scale(0);
  transform: scale(0);
  background-color: #000;
}

.emotion-2::after {
  content: "";
  display: block;
  width: 100%;
  height: 100%;
  position: absolute;
  background-repeat: no-repeat;
  background-position-x: 0;
  background-position-y: 0.125rem;
  -webkit-background-size: 1.875rem;
  background-size: 1.875rem;
  -webkit-background-clip: border-box;
  background-clip: border-box;
  background-color: transparent;
}

<label
    class="emotion-0"
    id="checkbox-id"
  >
    <input
      class="emotion-1"
      type="checkbox"
    />
    <div
      aria-checked="false"
      aria-hidden="true"
      aria-labelledby="checkbox-id"
      class="emotion-2"
      role="checkbox"
    />
    <div>
      I am default text
    </div>
  </label>
  <link
    data-testid="font-tracker"
    href="https://fast.fonts.net/t/1.css?apiType=css&projectid=0e5ae342-8d48-4e7a-9491-e82fee9dcc17"
    media="print"
    rel="stylesheet"
  />
</DocumentFragment>
`;

exports[`<Checkbox/> Checkbox with BRFS color 2023 renders default state correctly Athleta 1`] = `
<DocumentFragment>
  .emotion-0 {
  margin: 0 0 1.5rem;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  max-width: calc(100% - 2.3025rem);
  line-height: 0.75rem;
  font-family: 'Lynstone',Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  cursor: pointer;
  color: #000;
  font-size: 0.875rem;
  font-weight: 400;
}

.emotion-0:hover {
  -webkit-text-decoration: underline;
  text-decoration: underline;
}

.emotion-1 {
  opacity: 0;
  position: absolute;
}

.emotion-1[data-focus-visible-added]+div {
  outline: 0;
  box-shadow: 0 0 0 0.1875rem #CCC;
}

.emotion-2 {
  box-sizing: border-box;
  position: relative;
  display: block;
  margin-right: 0.75rem;
  min-width: 1.375rem;
  min-height: 1.375rem;
  border: 0.0625rem solid #000;
}

.emotion-2::before {
  content: "";
  display: block;
  position: absolute;
  width: calc(100% + 0.0625rem);
  height: calc(100% + 0.0625rem);
  -webkit-transform: scale(0);
  -moz-transform: scale(0);
  -ms-transform: scale(0);
  transform: scale(0);
  background-color: #000;
}

.emotion-2::after {
  content: "";
  display: block;
  width: 100%;
  height: 100%;
  position: absolute;
  background-repeat: no-repeat;
  background-position-x: 0;
  background-position-y: 0.125rem;
  -webkit-background-size: 1.875rem;
  background-size: 1.875rem;
  -webkit-background-clip: border-box;
  background-clip: border-box;
  background-color: transparent;
}

<label
    class="emotion-0"
    id="checkbox-id"
  >
    <input
      class="emotion-1"
      type="checkbox"
    />
    <div
      aria-checked="false"
      aria-hidden="true"
      aria-labelledby="checkbox-id"
      class="emotion-2"
      role="checkbox"
    />
    <div>
      I am default text
    </div>
  </label>
</DocumentFragment>
`;

exports[`<Checkbox/> Checkbox with BRFS color 2023 renders default state correctly Athleta in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  margin: 0 0 1.5rem;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  max-width: calc(100% - 2.3025rem);
  line-height: 0.75rem;
  font-family: 'Lynstone',Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  cursor: pointer;
  color: #000;
  font-size: 0.875rem;
  font-weight: 400;
}

.emotion-0:hover {
  -webkit-text-decoration: underline;
  text-decoration: underline;
}

.emotion-1 {
  opacity: 0;
  position: absolute;
}

.emotion-1[data-focus-visible-added]+div {
  outline: 0;
  box-shadow: 0 0 0 0.1875rem #CCC;
}

.emotion-2 {
  box-sizing: border-box;
  position: relative;
  display: block;
  margin-right: 0.75rem;
  min-width: 1.375rem;
  min-height: 1.375rem;
  border: 0.0625rem solid #000;
}

.emotion-2::before {
  content: "";
  display: block;
  position: absolute;
  width: calc(100% + 0.0625rem);
  height: calc(100% + 0.0625rem);
  -webkit-transform: scale(0);
  -moz-transform: scale(0);
  -ms-transform: scale(0);
  transform: scale(0);
  background-color: #000;
}

.emotion-2::after {
  content: "";
  display: block;
  width: 100%;
  height: 100%;
  position: absolute;
  background-repeat: no-repeat;
  background-position-x: 0;
  background-position-y: 0.125rem;
  -webkit-background-size: 1.875rem;
  background-size: 1.875rem;
  -webkit-background-clip: border-box;
  background-clip: border-box;
  background-color: transparent;
}

<label
    class="emotion-0"
    id="checkbox-id"
  >
    <input
      class="emotion-1"
      type="checkbox"
    />
    <div
      aria-checked="false"
      aria-hidden="true"
      aria-labelledby="checkbox-id"
      class="emotion-2"
      role="checkbox"
    />
    <div>
      I am default text
    </div>
  </label>
</DocumentFragment>
`;

exports[`<Checkbox/> Checkbox with BRFS color 2023 renders default state correctly BananaRepublic 1`] = `
<DocumentFragment>
  .emotion-0 {
  margin: 0 0 1.5rem;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  max-width: calc(100% - 2.3025rem);
  line-height: 0.75rem;
  font-family: 'Lynstone',Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  cursor: pointer;
  color: #000;
  font-size: 0.875rem;
  font-weight: 400;
}

.emotion-0:hover {
  -webkit-text-decoration: underline;
  text-decoration: underline;
}

.emotion-1 {
  opacity: 0;
  position: absolute;
}

.emotion-1[data-focus-visible-added]+div {
  outline: 0;
  box-shadow: 0 0 0 0.1875rem #CCC;
}

.emotion-2 {
  box-sizing: border-box;
  position: relative;
  display: block;
  margin-right: 0.75rem;
  min-width: 1.375rem;
  min-height: 1.375rem;
  border: 0.0625rem solid #000;
}

.emotion-2::before {
  content: "";
  display: block;
  position: absolute;
  width: calc(100% + 0.0625rem);
  height: calc(100% + 0.0625rem);
  -webkit-transform: scale(0);
  -moz-transform: scale(0);
  -ms-transform: scale(0);
  transform: scale(0);
  background-color: #000;
}

.emotion-2::after {
  content: "";
  display: block;
  width: 100%;
  height: 100%;
  position: absolute;
  background-repeat: no-repeat;
  background-position-x: 0;
  background-position-y: 0.125rem;
  -webkit-background-size: 1.875rem;
  background-size: 1.875rem;
  -webkit-background-clip: border-box;
  background-clip: border-box;
  background-color: transparent;
}

<label
    class="emotion-0"
    id="checkbox-id"
  >
    <input
      class="emotion-1"
      type="checkbox"
    />
    <div
      aria-checked="false"
      aria-hidden="true"
      aria-labelledby="checkbox-id"
      class="emotion-2"
      role="checkbox"
    />
    <div>
      I am default text
    </div>
  </label>
</DocumentFragment>
`;

exports[`<Checkbox/> Checkbox with BRFS color 2023 renders default state correctly BananaRepublic in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  margin: 0 0 1.5rem;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  max-width: calc(100% - 2.3025rem);
  line-height: 0.75rem;
  font-family: 'Lynstone',Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  cursor: pointer;
  color: #000;
  font-size: 0.875rem;
  font-weight: 400;
}

.emotion-0:hover {
  -webkit-text-decoration: underline;
  text-decoration: underline;
}

.emotion-1 {
  opacity: 0;
  position: absolute;
}

.emotion-1[data-focus-visible-added]+div {
  outline: 0;
  box-shadow: 0 0 0 0.1875rem #CCC;
}

.emotion-2 {
  box-sizing: border-box;
  position: relative;
  display: block;
  margin-right: 0.75rem;
  min-width: 1.375rem;
  min-height: 1.375rem;
  border: 0.0625rem solid #000;
}

.emotion-2::before {
  content: "";
  display: block;
  position: absolute;
  width: calc(100% + 0.0625rem);
  height: calc(100% + 0.0625rem);
  -webkit-transform: scale(0);
  -moz-transform: scale(0);
  -ms-transform: scale(0);
  transform: scale(0);
  background-color: #000;
}

.emotion-2::after {
  content: "";
  display: block;
  width: 100%;
  height: 100%;
  position: absolute;
  background-repeat: no-repeat;
  background-position-x: 0;
  background-position-y: 0.125rem;
  -webkit-background-size: 1.875rem;
  background-size: 1.875rem;
  -webkit-background-clip: border-box;
  background-clip: border-box;
  background-color: transparent;
}

<label
    class="emotion-0"
    id="checkbox-id"
  >
    <input
      class="emotion-1"
      type="checkbox"
    />
    <div
      aria-checked="false"
      aria-hidden="true"
      aria-labelledby="checkbox-id"
      class="emotion-2"
      role="checkbox"
    />
    <div>
      I am default text
    </div>
  </label>
</DocumentFragment>
`;

exports[`<Checkbox/> Checkbox with BRFS color 2023 renders default state correctly BananaRepublicFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  margin: 0 0 1.5rem;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  max-width: calc(100% - 2.3025rem);
  line-height: 0.75rem;
  font-family: 'Lynstone',Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  cursor: pointer;
  color: #000;
  font-size: 0.875rem;
  font-weight: 400;
}

.emotion-0:hover {
  -webkit-text-decoration: underline;
  text-decoration: underline;
}

.emotion-1 {
  opacity: 0;
  position: absolute;
}

.emotion-1[data-focus-visible-added]+div {
  outline: 0;
  box-shadow: 0 0 0 0.1875rem #CCC;
}

.emotion-2 {
  box-sizing: border-box;
  position: relative;
  display: block;
  margin-right: 0.75rem;
  min-width: 1.375rem;
  min-height: 1.375rem;
  border: 0.0625rem solid #000;
}

.emotion-2::before {
  content: "";
  display: block;
  position: absolute;
  width: calc(100% + 0.0625rem);
  height: calc(100% + 0.0625rem);
  -webkit-transform: scale(0);
  -moz-transform: scale(0);
  -ms-transform: scale(0);
  transform: scale(0);
  background-color: #000;
}

.emotion-2::after {
  content: "";
  display: block;
  width: 100%;
  height: 100%;
  position: absolute;
  background-repeat: no-repeat;
  background-position-x: 0;
  background-position-y: 0.125rem;
  -webkit-background-size: 1.875rem;
  background-size: 1.875rem;
  -webkit-background-clip: border-box;
  background-clip: border-box;
  background-color: transparent;
}

<label
    class="emotion-0"
    id="checkbox-id"
  >
    <input
      class="emotion-1"
      type="checkbox"
    />
    <div
      aria-checked="false"
      aria-hidden="true"
      aria-labelledby="checkbox-id"
      class="emotion-2"
      role="checkbox"
    />
    <div>
      I am default text
    </div>
  </label>
</DocumentFragment>
`;

exports[`<Checkbox/> Checkbox with BRFS color 2023 renders default state correctly BananaRepublicFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  margin: 0 0 1.5rem;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  max-width: calc(100% - 2.3025rem);
  line-height: 0.75rem;
  font-family: 'Lynstone',Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  cursor: pointer;
  color: #000;
  font-size: 0.875rem;
  font-weight: 400;
}

.emotion-0:hover {
  -webkit-text-decoration: underline;
  text-decoration: underline;
}

.emotion-1 {
  opacity: 0;
  position: absolute;
}

.emotion-1[data-focus-visible-added]+div {
  outline: 0;
  box-shadow: 0 0 0 0.1875rem #CCC;
}

.emotion-2 {
  box-sizing: border-box;
  position: relative;
  display: block;
  margin-right: 0.75rem;
  min-width: 1.375rem;
  min-height: 1.375rem;
  border: 0.0625rem solid #000;
}

.emotion-2::before {
  content: "";
  display: block;
  position: absolute;
  width: calc(100% + 0.0625rem);
  height: calc(100% + 0.0625rem);
  -webkit-transform: scale(0);
  -moz-transform: scale(0);
  -ms-transform: scale(0);
  transform: scale(0);
  background-color: #000;
}

.emotion-2::after {
  content: "";
  display: block;
  width: 100%;
  height: 100%;
  position: absolute;
  background-repeat: no-repeat;
  background-position-x: 0;
  background-position-y: 0.125rem;
  -webkit-background-size: 1.875rem;
  background-size: 1.875rem;
  -webkit-background-clip: border-box;
  background-clip: border-box;
  background-color: transparent;
}

<label
    class="emotion-0"
    id="checkbox-id"
  >
    <input
      class="emotion-1"
      type="checkbox"
    />
    <div
      aria-checked="false"
      aria-hidden="true"
      aria-labelledby="checkbox-id"
      class="emotion-2"
      role="checkbox"
    />
    <div>
      I am default text
    </div>
  </label>
</DocumentFragment>
`;

exports[`<Checkbox/> Checkbox with BRFS color 2023 renders default state correctly Gap 1`] = `
<DocumentFragment>
  .emotion-0 {
  margin: 0 0 1.5rem;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  max-width: calc(100% - 2.3025rem);
  line-height: 0.75rem;
  font-family: 'Lynstone',Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  cursor: pointer;
  color: #000;
  font-size: 0.875rem;
  font-weight: 400;
}

.emotion-0:hover {
  -webkit-text-decoration: underline;
  text-decoration: underline;
}

.emotion-1 {
  opacity: 0;
  position: absolute;
}

.emotion-1[data-focus-visible-added]+div {
  outline: 0;
  box-shadow: 0 0 0 0.1875rem #CCC;
}

.emotion-2 {
  box-sizing: border-box;
  position: relative;
  display: block;
  margin-right: 0.75rem;
  min-width: 1.375rem;
  min-height: 1.375rem;
  border: 0.0625rem solid #000;
}

.emotion-2::before {
  content: "";
  display: block;
  position: absolute;
  width: calc(100% + 0.0625rem);
  height: calc(100% + 0.0625rem);
  -webkit-transform: scale(0);
  -moz-transform: scale(0);
  -ms-transform: scale(0);
  transform: scale(0);
  background-color: #000;
}

.emotion-2::after {
  content: "";
  display: block;
  width: 100%;
  height: 100%;
  position: absolute;
  background-repeat: no-repeat;
  background-position-x: 0;
  background-position-y: 0.125rem;
  -webkit-background-size: 1.875rem;
  background-size: 1.875rem;
  -webkit-background-clip: border-box;
  background-clip: border-box;
  background-color: transparent;
}

<label
    class="emotion-0"
    id="checkbox-id"
  >
    <input
      class="emotion-1"
      type="checkbox"
    />
    <div
      aria-checked="false"
      aria-hidden="true"
      aria-labelledby="checkbox-id"
      class="emotion-2"
      role="checkbox"
    />
    <div>
      I am default text
    </div>
  </label>
</DocumentFragment>
`;

exports[`<Checkbox/> Checkbox with BRFS color 2023 renders default state correctly Gap in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  margin: 0 0 1.5rem;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  max-width: calc(100% - 2.3025rem);
  line-height: 0.75rem;
  font-family: 'Lynstone',Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  cursor: pointer;
  color: #000;
  font-size: 0.875rem;
  font-weight: 400;
}

.emotion-0:hover {
  -webkit-text-decoration: underline;
  text-decoration: underline;
}

.emotion-1 {
  opacity: 0;
  position: absolute;
}

.emotion-1[data-focus-visible-added]+div {
  outline: 0;
  box-shadow: 0 0 0 0.1875rem #CCC;
}

.emotion-2 {
  box-sizing: border-box;
  position: relative;
  display: block;
  margin-right: 0.75rem;
  min-width: 1.375rem;
  min-height: 1.375rem;
  border: 0.0625rem solid #000;
}

.emotion-2::before {
  content: "";
  display: block;
  position: absolute;
  width: calc(100% + 0.0625rem);
  height: calc(100% + 0.0625rem);
  -webkit-transform: scale(0);
  -moz-transform: scale(0);
  -ms-transform: scale(0);
  transform: scale(0);
  background-color: #000;
}

.emotion-2::after {
  content: "";
  display: block;
  width: 100%;
  height: 100%;
  position: absolute;
  background-repeat: no-repeat;
  background-position-x: 0;
  background-position-y: 0.125rem;
  -webkit-background-size: 1.875rem;
  background-size: 1.875rem;
  -webkit-background-clip: border-box;
  background-clip: border-box;
  background-color: transparent;
}

<label
    class="emotion-0"
    id="checkbox-id"
  >
    <input
      class="emotion-1"
      type="checkbox"
    />
    <div
      aria-checked="false"
      aria-hidden="true"
      aria-labelledby="checkbox-id"
      class="emotion-2"
      role="checkbox"
    />
    <div>
      I am default text
    </div>
  </label>
</DocumentFragment>
`;

exports[`<Checkbox/> Checkbox with BRFS color 2023 renders default state correctly GapFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  margin: 0 0 1.5rem;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  max-width: calc(100% - 2.3025rem);
  line-height: 0.75rem;
  font-family: 'Lynstone',Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  cursor: pointer;
  color: #000;
  font-size: 0.875rem;
  font-weight: 400;
}

.emotion-0:hover {
  -webkit-text-decoration: underline;
  text-decoration: underline;
}

.emotion-1 {
  opacity: 0;
  position: absolute;
}

.emotion-1[data-focus-visible-added]+div {
  outline: 0;
  box-shadow: 0 0 0 0.1875rem #CCC;
}

.emotion-2 {
  box-sizing: border-box;
  position: relative;
  display: block;
  margin-right: 0.75rem;
  min-width: 1.375rem;
  min-height: 1.375rem;
  border: 0.0625rem solid #000;
}

.emotion-2::before {
  content: "";
  display: block;
  position: absolute;
  width: calc(100% + 0.0625rem);
  height: calc(100% + 0.0625rem);
  -webkit-transform: scale(0);
  -moz-transform: scale(0);
  -ms-transform: scale(0);
  transform: scale(0);
  background-color: #000;
}

.emotion-2::after {
  content: "";
  display: block;
  width: 100%;
  height: 100%;
  position: absolute;
  background-repeat: no-repeat;
  background-position-x: 0;
  background-position-y: 0.125rem;
  -webkit-background-size: 1.875rem;
  background-size: 1.875rem;
  -webkit-background-clip: border-box;
  background-clip: border-box;
  background-color: transparent;
}

<label
    class="emotion-0"
    id="checkbox-id"
  >
    <input
      class="emotion-1"
      type="checkbox"
    />
    <div
      aria-checked="false"
      aria-hidden="true"
      aria-labelledby="checkbox-id"
      class="emotion-2"
      role="checkbox"
    />
    <div>
      I am default text
    </div>
  </label>
</DocumentFragment>
`;

exports[`<Checkbox/> Checkbox with BRFS color 2023 renders default state correctly GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  margin: 0 0 1.5rem;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  max-width: calc(100% - 2.3025rem);
  line-height: 0.75rem;
  font-family: 'Lynstone',Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  cursor: pointer;
  color: #000;
  font-size: 0.875rem;
  font-weight: 400;
}

.emotion-0:hover {
  -webkit-text-decoration: underline;
  text-decoration: underline;
}

.emotion-1 {
  opacity: 0;
  position: absolute;
}

.emotion-1[data-focus-visible-added]+div {
  outline: 0;
  box-shadow: 0 0 0 0.1875rem #CCC;
}

.emotion-2 {
  box-sizing: border-box;
  position: relative;
  display: block;
  margin-right: 0.75rem;
  min-width: 1.375rem;
  min-height: 1.375rem;
  border: 0.0625rem solid #000;
}

.emotion-2::before {
  content: "";
  display: block;
  position: absolute;
  width: calc(100% + 0.0625rem);
  height: calc(100% + 0.0625rem);
  -webkit-transform: scale(0);
  -moz-transform: scale(0);
  -ms-transform: scale(0);
  transform: scale(0);
  background-color: #000;
}

.emotion-2::after {
  content: "";
  display: block;
  width: 100%;
  height: 100%;
  position: absolute;
  background-repeat: no-repeat;
  background-position-x: 0;
  background-position-y: 0.125rem;
  -webkit-background-size: 1.875rem;
  background-size: 1.875rem;
  -webkit-background-clip: border-box;
  background-clip: border-box;
  background-color: transparent;
}

<label
    class="emotion-0"
    id="checkbox-id"
  >
    <input
      class="emotion-1"
      type="checkbox"
    />
    <div
      aria-checked="false"
      aria-hidden="true"
      aria-labelledby="checkbox-id"
      class="emotion-2"
      role="checkbox"
    />
    <div>
      I am default text
    </div>
  </label>
</DocumentFragment>
`;

exports[`<Checkbox/> Checkbox with BRFS color 2023 renders default state correctly OldNavy 1`] = `
<DocumentFragment>
  .emotion-0 {
  margin: 0 0 1.5rem;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  max-width: calc(100% - 2.3025rem);
  line-height: 0.75rem;
  font-family: 'Lynstone',Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  cursor: pointer;
  color: #000;
  font-size: 0.875rem;
  font-weight: 400;
}

.emotion-0:hover {
  -webkit-text-decoration: underline;
  text-decoration: underline;
}

.emotion-1 {
  opacity: 0;
  position: absolute;
}

.emotion-1[data-focus-visible-added]+div {
  outline: 0;
  box-shadow: 0 0 0 0.1875rem #CCC;
}

.emotion-2 {
  box-sizing: border-box;
  position: relative;
  display: block;
  margin-right: 0.75rem;
  min-width: 1.375rem;
  min-height: 1.375rem;
  border: 0.0625rem solid #000;
}

.emotion-2::before {
  content: "";
  display: block;
  position: absolute;
  width: calc(100% + 0.0625rem);
  height: calc(100% + 0.0625rem);
  -webkit-transform: scale(0);
  -moz-transform: scale(0);
  -ms-transform: scale(0);
  transform: scale(0);
  background-color: #000;
}

.emotion-2::after {
  content: "";
  display: block;
  width: 100%;
  height: 100%;
  position: absolute;
  background-repeat: no-repeat;
  background-position-x: 0;
  background-position-y: 0.125rem;
  -webkit-background-size: 1.875rem;
  background-size: 1.875rem;
  -webkit-background-clip: border-box;
  background-clip: border-box;
  background-color: transparent;
}

<label
    class="emotion-0"
    id="checkbox-id"
  >
    <input
      class="emotion-1"
      type="checkbox"
    />
    <div
      aria-checked="false"
      aria-hidden="true"
      aria-labelledby="checkbox-id"
      class="emotion-2"
      role="checkbox"
    />
    <div>
      I am default text
    </div>
  </label>
</DocumentFragment>
`;

exports[`<Checkbox/> Checkbox with BRFS color 2023 renders default state correctly OldNavy in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  margin: 0 0 1.5rem;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  max-width: calc(100% - 2.3025rem);
  line-height: 0.75rem;
  font-family: 'Lynstone',Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  cursor: pointer;
  color: #000;
  font-size: 0.875rem;
  font-weight: 400;
}

.emotion-0:hover {
  -webkit-text-decoration: underline;
  text-decoration: underline;
}

.emotion-1 {
  opacity: 0;
  position: absolute;
}

.emotion-1[data-focus-visible-added]+div {
  outline: 0;
  box-shadow: 0 0 0 0.1875rem #CCC;
}

.emotion-2 {
  box-sizing: border-box;
  position: relative;
  display: block;
  margin-right: 0.75rem;
  min-width: 1.375rem;
  min-height: 1.375rem;
  border: 0.0625rem solid #000;
}

.emotion-2::before {
  content: "";
  display: block;
  position: absolute;
  width: calc(100% + 0.0625rem);
  height: calc(100% + 0.0625rem);
  -webkit-transform: scale(0);
  -moz-transform: scale(0);
  -ms-transform: scale(0);
  transform: scale(0);
  background-color: #000;
}

.emotion-2::after {
  content: "";
  display: block;
  width: 100%;
  height: 100%;
  position: absolute;
  background-repeat: no-repeat;
  background-position-x: 0;
  background-position-y: 0.125rem;
  -webkit-background-size: 1.875rem;
  background-size: 1.875rem;
  -webkit-background-clip: border-box;
  background-clip: border-box;
  background-color: transparent;
}

<label
    class="emotion-0"
    id="checkbox-id"
  >
    <input
      class="emotion-1"
      type="checkbox"
    />
    <div
      aria-checked="false"
      aria-hidden="true"
      aria-labelledby="checkbox-id"
      class="emotion-2"
      role="checkbox"
    />
    <div>
      I am default text
    </div>
  </label>
</DocumentFragment>
`;

exports[`<Checkbox/> snapshots renders default state correctly Athleta 1`] = `
<DocumentFragment>
  .emotion-0 {
  margin: 0 0 1.5rem;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  max-width: calc(100% - 2.3025rem);
  line-height: 1;
  font-family: 'Avenir Next','Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  cursor: pointer;
  color: #333;
  font-size: 1.063rem;
}

.emotion-0:hover {
  -webkit-text-decoration: underline;
  text-decoration: underline;
}

.emotion-1 {
  opacity: 0;
  position: absolute;
}

.emotion-1[data-focus-visible-added]+div {
  outline: 0;
  box-shadow: 0 0 0 0.1875rem #5CABF7;
}

.emotion-2 {
  box-sizing: border-box;
  position: relative;
  display: block;
  margin-right: 0.625rem;
  min-width: 1.375rem;
  min-height: 1.375rem;
  border: 0.0625rem solid #666;
}

.emotion-2::before {
  content: "";
  display: block;
  position: absolute;
  width: calc(100% + 0.0625rem);
  height: calc(100% + 0.0625rem);
  -webkit-transform: scale(0);
  -moz-transform: scale(0);
  -ms-transform: scale(0);
  transform: scale(0);
  background-color: #333;
}

.emotion-2::after {
  content: "";
  display: block;
  width: 100%;
  height: 100%;
  position: absolute;
  background-repeat: no-repeat;
  background-position-x: 0;
  background-position-y: 0.125rem;
  -webkit-background-size: 1.875rem;
  background-size: 1.875rem;
  -webkit-background-clip: border-box;
  background-clip: border-box;
  background-color: transparent;
}

<label
    class="emotion-0"
    id="checkbox-id"
  >
    <input
      class="emotion-1"
      type="checkbox"
    />
    <div
      aria-checked="false"
      aria-hidden="true"
      aria-labelledby="checkbox-id"
      class="emotion-2"
      role="checkbox"
    />
    <div>
      I am default text
    </div>
  </label>
</DocumentFragment>
`;

exports[`<Checkbox/> snapshots renders default state correctly Athleta in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  margin: 0 0 1.5rem;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  max-width: calc(100% - 2.3025rem);
  line-height: 1;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  cursor: pointer;
  color: #000;
  font-size: 1.1rem;
}

.emotion-0:hover {
  -webkit-text-decoration: underline;
  text-decoration: underline;
}

.emotion-1 {
  opacity: 0;
  position: absolute;
}

.emotion-1[data-focus-visible-added]+div {
  outline: 0;
  box-shadow: 0 0 0 0.125rem #5CABF7;
}

.emotion-2 {
  box-sizing: content-box;
  position: relative;
  display: block;
  margin-right: 0.625rem;
  min-width: 1.375rem;
  min-height: 1.375rem;
  border: 0.0625rem solid #666;
}

.emotion-2::before {
  content: "";
  display: block;
  position: absolute;
  width: calc(100% + 0.0625rem);
  height: calc(100% + 0.0625rem);
  -webkit-transform: scale(0);
  -moz-transform: scale(0);
  -ms-transform: scale(0);
  transform: scale(0);
  background-color: #0466CA;
}

.emotion-2::after {
  content: "";
  display: block;
  width: 100%;
  height: 100%;
  position: absolute;
  background-repeat: no-repeat;
  background-position-x: 0;
  background-position-y: 0.125rem;
  -webkit-background-size: 1.875rem;
  background-size: 1.875rem;
  -webkit-background-clip: border-box;
  background-clip: border-box;
  background-color: transparent;
}

<label
    class="emotion-0"
    id="checkbox-id"
  >
    <input
      class="emotion-1"
      type="checkbox"
    />
    <div
      aria-checked="false"
      aria-hidden="true"
      aria-labelledby="checkbox-id"
      class="emotion-2"
      role="checkbox"
    />
    <div>
      I am default text
    </div>
  </label>
</DocumentFragment>
`;

exports[`<Checkbox/> snapshots renders default state correctly BananaRepublic 1`] = `
<DocumentFragment>
  .emotion-0 {
  margin: 0 0 1.5rem;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  max-width: calc(100% - 2.3025rem);
  line-height: 0.75rem;
  font-family: 'Lynstone',Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  cursor: pointer;
  color: #2C2824;
  font-size: 0.875rem;
  font-weight: 400;
}

.emotion-0:hover {
  -webkit-text-decoration: underline;
  text-decoration: underline;
}

.emotion-1 {
  opacity: 0;
  position: absolute;
}

.emotion-1[data-focus-visible-added]+div {
  outline: 0;
  box-shadow: 0 0 0 0.1875rem #CCC;
}

.emotion-2 {
  box-sizing: border-box;
  position: relative;
  display: block;
  margin-right: 0.75rem;
  min-width: 1.375rem;
  min-height: 1.375rem;
  border: 0.0625rem solid #2C2824;
}

.emotion-2::before {
  content: "";
  display: block;
  position: absolute;
  width: calc(100% + 0.0625rem);
  height: calc(100% + 0.0625rem);
  -webkit-transform: scale(0);
  -moz-transform: scale(0);
  -ms-transform: scale(0);
  transform: scale(0);
  background-color: #2C2824;
}

.emotion-2::after {
  content: "";
  display: block;
  width: 100%;
  height: 100%;
  position: absolute;
  background-repeat: no-repeat;
  background-position-x: 0;
  background-position-y: 0.125rem;
  -webkit-background-size: 1.875rem;
  background-size: 1.875rem;
  -webkit-background-clip: border-box;
  background-clip: border-box;
  background-color: transparent;
}

<label
    class="emotion-0"
    id="checkbox-id"
  >
    <input
      class="emotion-1"
      type="checkbox"
    />
    <div
      aria-checked="false"
      aria-hidden="true"
      aria-labelledby="checkbox-id"
      class="emotion-2"
      role="checkbox"
    />
    <div>
      I am default text
    </div>
  </label>
</DocumentFragment>
`;

exports[`<Checkbox/> snapshots renders default state correctly BananaRepublic in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  margin: 0 0 1.5rem;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  max-width: calc(100% - 2.3025rem);
  line-height: 1;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  cursor: pointer;
  color: #000;
  font-size: 1.1rem;
}

.emotion-0:hover {
  -webkit-text-decoration: underline;
  text-decoration: underline;
}

.emotion-1 {
  opacity: 0;
  position: absolute;
}

.emotion-1[data-focus-visible-added]+div {
  outline: 0;
  box-shadow: 0 0 0 0.125rem #5CABF7;
}

.emotion-2 {
  box-sizing: content-box;
  position: relative;
  display: block;
  margin-right: 0.625rem;
  min-width: 1.375rem;
  min-height: 1.375rem;
  border: 0.0625rem solid #666;
}

.emotion-2::before {
  content: "";
  display: block;
  position: absolute;
  width: calc(100% + 0.0625rem);
  height: calc(100% + 0.0625rem);
  -webkit-transform: scale(0);
  -moz-transform: scale(0);
  -ms-transform: scale(0);
  transform: scale(0);
  background-color: #0466CA;
}

.emotion-2::after {
  content: "";
  display: block;
  width: 100%;
  height: 100%;
  position: absolute;
  background-repeat: no-repeat;
  background-position-x: 0;
  background-position-y: 0.125rem;
  -webkit-background-size: 1.875rem;
  background-size: 1.875rem;
  -webkit-background-clip: border-box;
  background-clip: border-box;
  background-color: transparent;
}

<label
    class="emotion-0"
    id="checkbox-id"
  >
    <input
      class="emotion-1"
      type="checkbox"
    />
    <div
      aria-checked="false"
      aria-hidden="true"
      aria-labelledby="checkbox-id"
      class="emotion-2"
      role="checkbox"
    />
    <div>
      I am default text
    </div>
  </label>
</DocumentFragment>
`;

exports[`<Checkbox/> snapshots renders default state correctly BananaRepublicFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  margin: 0 0 1.5rem;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  max-width: calc(100% - 2.3025rem);
  line-height: 0.75rem;
  font-family: 'Lynstone',Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  cursor: pointer;
  color: #2C2824;
  font-size: 0.875rem;
  font-weight: 400;
}

.emotion-0:hover {
  -webkit-text-decoration: underline;
  text-decoration: underline;
}

.emotion-1 {
  opacity: 0;
  position: absolute;
}

.emotion-1[data-focus-visible-added]+div {
  outline: 0;
  box-shadow: 0 0 0 0.1875rem #CCC;
}

.emotion-2 {
  box-sizing: border-box;
  position: relative;
  display: block;
  margin-right: 0.75rem;
  min-width: 1.375rem;
  min-height: 1.375rem;
  border: 0.0625rem solid #2C2824;
}

.emotion-2::before {
  content: "";
  display: block;
  position: absolute;
  width: calc(100% + 0.0625rem);
  height: calc(100% + 0.0625rem);
  -webkit-transform: scale(0);
  -moz-transform: scale(0);
  -ms-transform: scale(0);
  transform: scale(0);
  background-color: #2C2824;
}

.emotion-2::after {
  content: "";
  display: block;
  width: 100%;
  height: 100%;
  position: absolute;
  background-repeat: no-repeat;
  background-position-x: 0;
  background-position-y: 0.125rem;
  -webkit-background-size: 1.875rem;
  background-size: 1.875rem;
  -webkit-background-clip: border-box;
  background-clip: border-box;
  background-color: transparent;
}

<label
    class="emotion-0"
    id="checkbox-id"
  >
    <input
      class="emotion-1"
      type="checkbox"
    />
    <div
      aria-checked="false"
      aria-hidden="true"
      aria-labelledby="checkbox-id"
      class="emotion-2"
      role="checkbox"
    />
    <div>
      I am default text
    </div>
  </label>
</DocumentFragment>
`;

exports[`<Checkbox/> snapshots renders default state correctly BananaRepublicFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  margin: 0 0 1.5rem;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  max-width: calc(100% - 2.3025rem);
  line-height: 1;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  cursor: pointer;
  color: #000;
  font-size: 1.1rem;
}

.emotion-0:hover {
  -webkit-text-decoration: underline;
  text-decoration: underline;
}

.emotion-1 {
  opacity: 0;
  position: absolute;
}

.emotion-1[data-focus-visible-added]+div {
  outline: 0;
  box-shadow: 0 0 0 0.125rem #5CABF7;
}

.emotion-2 {
  box-sizing: content-box;
  position: relative;
  display: block;
  margin-right: 0.625rem;
  min-width: 1.375rem;
  min-height: 1.375rem;
  border: 0.0625rem solid #666;
}

.emotion-2::before {
  content: "";
  display: block;
  position: absolute;
  width: calc(100% + 0.0625rem);
  height: calc(100% + 0.0625rem);
  -webkit-transform: scale(0);
  -moz-transform: scale(0);
  -ms-transform: scale(0);
  transform: scale(0);
  background-color: #0466CA;
}

.emotion-2::after {
  content: "";
  display: block;
  width: 100%;
  height: 100%;
  position: absolute;
  background-repeat: no-repeat;
  background-position-x: 0;
  background-position-y: 0.125rem;
  -webkit-background-size: 1.875rem;
  background-size: 1.875rem;
  -webkit-background-clip: border-box;
  background-clip: border-box;
  background-color: transparent;
}

<label
    class="emotion-0"
    id="checkbox-id"
  >
    <input
      class="emotion-1"
      type="checkbox"
    />
    <div
      aria-checked="false"
      aria-hidden="true"
      aria-labelledby="checkbox-id"
      class="emotion-2"
      role="checkbox"
    />
    <div>
      I am default text
    </div>
  </label>
</DocumentFragment>
`;

exports[`<Checkbox/> snapshots renders default state correctly Gap 1`] = `
<DocumentFragment>
  .emotion-0 {
  margin: 0 0 1.5rem;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  max-width: calc(100% - 2.3025rem);
  line-height: 1;
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  cursor: pointer;
  color: #000;
  font-size: 1.063rem;
}

.emotion-0:hover {
  -webkit-text-decoration: underline;
  text-decoration: underline;
}

.emotion-1 {
  opacity: 0;
  position: absolute;
}

.emotion-1[data-focus-visible-added]+div {
  outline: 0;
  box-shadow: 0 0 0 0.1875rem #5CABF7;
}

.emotion-2 {
  box-sizing: border-box;
  position: relative;
  display: block;
  margin-right: 0.625rem;
  min-width: 1.375rem;
  min-height: 1.375rem;
  border: 0.0625rem solid #666;
}

.emotion-2::before {
  content: "";
  display: block;
  position: absolute;
  width: calc(100% + 0.0625rem);
  height: calc(100% + 0.0625rem);
  -webkit-transform: scale(0);
  -moz-transform: scale(0);
  -ms-transform: scale(0);
  transform: scale(0);
  background-color: #002554;
}

.emotion-2::after {
  content: "";
  display: block;
  width: 100%;
  height: 100%;
  position: absolute;
  background-repeat: no-repeat;
  background-position-x: 0;
  background-position-y: 0.125rem;
  -webkit-background-size: 1.875rem;
  background-size: 1.875rem;
  -webkit-background-clip: border-box;
  background-clip: border-box;
  background-color: transparent;
}

<label
    class="emotion-0"
    id="checkbox-id"
  >
    <input
      class="emotion-1"
      type="checkbox"
    />
    <div
      aria-checked="false"
      aria-hidden="true"
      aria-labelledby="checkbox-id"
      class="emotion-2"
      role="checkbox"
    />
    <div>
      I am default text
    </div>
  </label>
</DocumentFragment>
`;

exports[`<Checkbox/> snapshots renders default state correctly Gap in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  margin: 0 0 1.5rem;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  max-width: calc(100% - 2.3025rem);
  line-height: 1;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  cursor: pointer;
  color: #000;
  font-size: 1.1rem;
}

.emotion-0:hover {
  -webkit-text-decoration: underline;
  text-decoration: underline;
}

.emotion-1 {
  opacity: 0;
  position: absolute;
}

.emotion-1[data-focus-visible-added]+div {
  outline: 0;
  box-shadow: 0 0 0 0.125rem #5CABF7;
}

.emotion-2 {
  box-sizing: content-box;
  position: relative;
  display: block;
  margin-right: 0.625rem;
  min-width: 1.375rem;
  min-height: 1.375rem;
  border: 0.0625rem solid #666;
}

.emotion-2::before {
  content: "";
  display: block;
  position: absolute;
  width: calc(100% + 0.0625rem);
  height: calc(100% + 0.0625rem);
  -webkit-transform: scale(0);
  -moz-transform: scale(0);
  -ms-transform: scale(0);
  transform: scale(0);
  background-color: #0466CA;
}

.emotion-2::after {
  content: "";
  display: block;
  width: 100%;
  height: 100%;
  position: absolute;
  background-repeat: no-repeat;
  background-position-x: 0;
  background-position-y: 0.125rem;
  -webkit-background-size: 1.875rem;
  background-size: 1.875rem;
  -webkit-background-clip: border-box;
  background-clip: border-box;
  background-color: transparent;
}

<label
    class="emotion-0"
    id="checkbox-id"
  >
    <input
      class="emotion-1"
      type="checkbox"
    />
    <div
      aria-checked="false"
      aria-hidden="true"
      aria-labelledby="checkbox-id"
      class="emotion-2"
      role="checkbox"
    />
    <div>
      I am default text
    </div>
  </label>
</DocumentFragment>
`;

exports[`<Checkbox/> snapshots renders default state correctly GapFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  margin: 0 0 1.5rem;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  max-width: calc(100% - 2.3025rem);
  line-height: 1;
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  cursor: pointer;
  color: #000;
  font-size: 1.063rem;
}

.emotion-0:hover {
  -webkit-text-decoration: underline;
  text-decoration: underline;
}

.emotion-1 {
  opacity: 0;
  position: absolute;
}

.emotion-1[data-focus-visible-added]+div {
  outline: 0;
  box-shadow: 0 0 0 0.1875rem #5CABF7;
}

.emotion-2 {
  box-sizing: border-box;
  position: relative;
  display: block;
  margin-right: 0.625rem;
  min-width: 1.375rem;
  min-height: 1.375rem;
  border: 0.0625rem solid #666;
}

.emotion-2::before {
  content: "";
  display: block;
  position: absolute;
  width: calc(100% + 0.0625rem);
  height: calc(100% + 0.0625rem);
  -webkit-transform: scale(0);
  -moz-transform: scale(0);
  -ms-transform: scale(0);
  transform: scale(0);
  background-color: #002554;
}

.emotion-2::after {
  content: "";
  display: block;
  width: 100%;
  height: 100%;
  position: absolute;
  background-repeat: no-repeat;
  background-position-x: 0;
  background-position-y: 0.125rem;
  -webkit-background-size: 1.875rem;
  background-size: 1.875rem;
  -webkit-background-clip: border-box;
  background-clip: border-box;
  background-color: transparent;
}

<label
    class="emotion-0"
    id="checkbox-id"
  >
    <input
      class="emotion-1"
      type="checkbox"
    />
    <div
      aria-checked="false"
      aria-hidden="true"
      aria-labelledby="checkbox-id"
      class="emotion-2"
      role="checkbox"
    />
    <div>
      I am default text
    </div>
  </label>
</DocumentFragment>
`;

exports[`<Checkbox/> snapshots renders default state correctly GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  margin: 0 0 1.5rem;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  max-width: calc(100% - 2.3025rem);
  line-height: 1;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  cursor: pointer;
  color: #000;
  font-size: 1.1rem;
}

.emotion-0:hover {
  -webkit-text-decoration: underline;
  text-decoration: underline;
}

.emotion-1 {
  opacity: 0;
  position: absolute;
}

.emotion-1[data-focus-visible-added]+div {
  outline: 0;
  box-shadow: 0 0 0 0.125rem #5CABF7;
}

.emotion-2 {
  box-sizing: content-box;
  position: relative;
  display: block;
  margin-right: 0.625rem;
  min-width: 1.375rem;
  min-height: 1.375rem;
  border: 0.0625rem solid #666;
}

.emotion-2::before {
  content: "";
  display: block;
  position: absolute;
  width: calc(100% + 0.0625rem);
  height: calc(100% + 0.0625rem);
  -webkit-transform: scale(0);
  -moz-transform: scale(0);
  -ms-transform: scale(0);
  transform: scale(0);
  background-color: #0466CA;
}

.emotion-2::after {
  content: "";
  display: block;
  width: 100%;
  height: 100%;
  position: absolute;
  background-repeat: no-repeat;
  background-position-x: 0;
  background-position-y: 0.125rem;
  -webkit-background-size: 1.875rem;
  background-size: 1.875rem;
  -webkit-background-clip: border-box;
  background-clip: border-box;
  background-color: transparent;
}

<label
    class="emotion-0"
    id="checkbox-id"
  >
    <input
      class="emotion-1"
      type="checkbox"
    />
    <div
      aria-checked="false"
      aria-hidden="true"
      aria-labelledby="checkbox-id"
      class="emotion-2"
      role="checkbox"
    />
    <div>
      I am default text
    </div>
  </label>
</DocumentFragment>
`;

exports[`<Checkbox/> snapshots renders default state correctly OldNavy 1`] = `
<DocumentFragment>
  .emotion-0 {
  margin: 0 0 1.5rem;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  max-width: calc(100% - 2.3025rem);
  line-height: 1;
  font-family: 'Gotham','Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  cursor: pointer;
  color: #000;
  font-size: 1rem;
}

.emotion-0:hover {
  -webkit-text-decoration: underline;
  text-decoration: underline;
}

.emotion-1 {
  opacity: 0;
  position: absolute;
}

.emotion-1[data-focus-visible-added]+div {
  outline: 0;
  box-shadow: 0 0 0 0.1875rem #5CABF7;
}

.emotion-2 {
  box-sizing: border-box;
  position: relative;
  display: block;
  margin-right: 0.625rem;
  min-width: 1.375rem;
  min-height: 1.375rem;
  border: 0.0625rem solid #666;
}

.emotion-2::before {
  content: "";
  display: block;
  position: absolute;
  width: calc(100% + 0.0625rem);
  height: calc(100% + 0.0625rem);
  -webkit-transform: scale(0);
  -moz-transform: scale(0);
  -ms-transform: scale(0);
  transform: scale(0);
  background-color: #003764;
}

.emotion-2::after {
  content: "";
  display: block;
  width: 100%;
  height: 100%;
  position: absolute;
  background-repeat: no-repeat;
  background-position-x: 0;
  background-position-y: 0.125rem;
  -webkit-background-size: 1.875rem;
  background-size: 1.875rem;
  -webkit-background-clip: border-box;
  background-clip: border-box;
  background-color: transparent;
}

<label
    class="emotion-0"
    id="checkbox-id"
  >
    <input
      class="emotion-1"
      type="checkbox"
    />
    <div
      aria-checked="false"
      aria-hidden="true"
      aria-labelledby="checkbox-id"
      class="emotion-2"
      role="checkbox"
    />
    <div>
      I am default text
    </div>
  </label>
</DocumentFragment>
`;

exports[`<Checkbox/> snapshots renders default state correctly OldNavy in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  margin: 0 0 1.5rem;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  max-width: calc(100% - 2.3025rem);
  line-height: 1;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  cursor: pointer;
  color: #000;
  font-size: 1.1rem;
}

.emotion-0:hover {
  -webkit-text-decoration: underline;
  text-decoration: underline;
}

.emotion-1 {
  opacity: 0;
  position: absolute;
}

.emotion-1[data-focus-visible-added]+div {
  outline: 0;
  box-shadow: 0 0 0 0.125rem #5CABF7;
}

.emotion-2 {
  box-sizing: content-box;
  position: relative;
  display: block;
  margin-right: 0.625rem;
  min-width: 1.375rem;
  min-height: 1.375rem;
  border: 0.0625rem solid #666;
}

.emotion-2::before {
  content: "";
  display: block;
  position: absolute;
  width: calc(100% + 0.0625rem);
  height: calc(100% + 0.0625rem);
  -webkit-transform: scale(0);
  -moz-transform: scale(0);
  -ms-transform: scale(0);
  transform: scale(0);
  background-color: #0466CA;
}

.emotion-2::after {
  content: "";
  display: block;
  width: 100%;
  height: 100%;
  position: absolute;
  background-repeat: no-repeat;
  background-position-x: 0;
  background-position-y: 0.125rem;
  -webkit-background-size: 1.875rem;
  background-size: 1.875rem;
  -webkit-background-clip: border-box;
  background-clip: border-box;
  background-color: transparent;
}

<label
    class="emotion-0"
    id="checkbox-id"
  >
    <input
      class="emotion-1"
      type="checkbox"
    />
    <div
      aria-checked="false"
      aria-hidden="true"
      aria-labelledby="checkbox-id"
      class="emotion-2"
      role="checkbox"
    />
    <div>
      I am default text
    </div>
  </label>
</DocumentFragment>
`;

exports[`<Checkbox/> snapshots renders disabled state correctly Athleta 1`] = `
<DocumentFragment>
  .emotion-0 {
  margin: 0 0 1.5rem;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  max-width: calc(100% - 2.3025rem);
  line-height: 1;
  font-family: 'Avenir Next','Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  cursor: default;
  color: #CCC;
  font-size: 1.063rem;
}

.emotion-0:hover {
  -webkit-text-decoration: underline;
  text-decoration: underline;
}

.emotion-1 {
  opacity: 0;
  position: absolute;
}

.emotion-1[data-focus-visible-added]+div {
  outline: 0;
  box-shadow: 0 0 0 0.1875rem #5CABF7;
}

.emotion-2 {
  box-sizing: border-box;
  position: relative;
  display: block;
  margin-right: 0.625rem;
  min-width: 1.375rem;
  min-height: 1.375rem;
  border: 0.0625rem solid #CCC;
}

.emotion-2::before {
  content: "";
  display: block;
  position: absolute;
  width: calc(100% + 0.0625rem);
  height: calc(100% + 0.0625rem);
  -webkit-transform: scale(0);
  -moz-transform: scale(0);
  -ms-transform: scale(0);
  transform: scale(0);
  background-color: #333;
}

.emotion-2::after {
  content: "";
  display: block;
  width: 100%;
  height: 100%;
  position: absolute;
  background-repeat: no-repeat;
  background-position-x: 0;
  background-position-y: 0.125rem;
  -webkit-background-size: 1.875rem;
  background-size: 1.875rem;
  -webkit-background-clip: border-box;
  background-clip: border-box;
  background-color: transparent;
}

<label
    class="emotion-0"
    disabled=""
    id="checkbox-id"
  >
    <input
      class="emotion-1"
      disabled=""
      type="checkbox"
    />
    <div
      aria-checked="false"
      aria-hidden="true"
      aria-labelledby="checkbox-id"
      class="emotion-2"
      disabled=""
      role="checkbox"
    />
    <div>
      I am default text
    </div>
  </label>
</DocumentFragment>
`;

exports[`<Checkbox/> snapshots renders disabled state correctly Athleta in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  margin: 0 0 1.5rem;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  max-width: calc(100% - 2.3025rem);
  line-height: 1;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  cursor: default;
  color: #CCC;
  font-size: 1.1rem;
}

.emotion-0:hover {
  -webkit-text-decoration: underline;
  text-decoration: underline;
}

.emotion-1 {
  opacity: 0;
  position: absolute;
}

.emotion-1[data-focus-visible-added]+div {
  outline: 0;
  box-shadow: 0 0 0 0.125rem #5CABF7;
}

.emotion-2 {
  box-sizing: content-box;
  position: relative;
  display: block;
  margin-right: 0.625rem;
  min-width: 1.375rem;
  min-height: 1.375rem;
  border: 0.0625rem solid #CCC;
}

.emotion-2::before {
  content: "";
  display: block;
  position: absolute;
  width: calc(100% + 0.0625rem);
  height: calc(100% + 0.0625rem);
  -webkit-transform: scale(0);
  -moz-transform: scale(0);
  -ms-transform: scale(0);
  transform: scale(0);
  background-color: #0466CA;
}

.emotion-2::after {
  content: "";
  display: block;
  width: 100%;
  height: 100%;
  position: absolute;
  background-repeat: no-repeat;
  background-position-x: 0;
  background-position-y: 0.125rem;
  -webkit-background-size: 1.875rem;
  background-size: 1.875rem;
  -webkit-background-clip: border-box;
  background-clip: border-box;
  background-color: transparent;
}

<label
    class="emotion-0"
    disabled=""
    id="checkbox-id"
  >
    <input
      class="emotion-1"
      disabled=""
      type="checkbox"
    />
    <div
      aria-checked="false"
      aria-hidden="true"
      aria-labelledby="checkbox-id"
      class="emotion-2"
      disabled=""
      role="checkbox"
    />
    <div>
      I am default text
    </div>
  </label>
</DocumentFragment>
`;

exports[`<Checkbox/> snapshots renders disabled state correctly BananaRepublic 1`] = `
<DocumentFragment>
  .emotion-0 {
  margin: 0 0 1.5rem;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  max-width: calc(100% - 2.3025rem);
  line-height: 0.75rem;
  font-family: 'Lynstone',Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  cursor: default;
  color: #CCC;
  font-size: 0.875rem;
  font-weight: 400;
}

.emotion-0:hover {
  -webkit-text-decoration: underline;
  text-decoration: underline;
}

.emotion-1 {
  opacity: 0;
  position: absolute;
}

.emotion-1[data-focus-visible-added]+div {
  outline: 0;
  box-shadow: 0 0 0 0.1875rem #CCC;
}

.emotion-2 {
  box-sizing: border-box;
  position: relative;
  display: block;
  margin-right: 0.75rem;
  min-width: 1.375rem;
  min-height: 1.375rem;
  border: 0.0625rem solid #CCC;
}

.emotion-2::before {
  content: "";
  display: block;
  position: absolute;
  width: calc(100% + 0.0625rem);
  height: calc(100% + 0.0625rem);
  -webkit-transform: scale(0);
  -moz-transform: scale(0);
  -ms-transform: scale(0);
  transform: scale(0);
  background-color: #2C2824;
}

.emotion-2::after {
  content: "";
  display: block;
  width: 100%;
  height: 100%;
  position: absolute;
  background-repeat: no-repeat;
  background-position-x: 0;
  background-position-y: 0.125rem;
  -webkit-background-size: 1.875rem;
  background-size: 1.875rem;
  -webkit-background-clip: border-box;
  background-clip: border-box;
  background-color: transparent;
}

<label
    class="emotion-0"
    disabled=""
    id="checkbox-id"
  >
    <input
      class="emotion-1"
      disabled=""
      type="checkbox"
    />
    <div
      aria-checked="false"
      aria-hidden="true"
      aria-labelledby="checkbox-id"
      class="emotion-2"
      disabled=""
      role="checkbox"
    />
    <div>
      I am default text
    </div>
  </label>
</DocumentFragment>
`;

exports[`<Checkbox/> snapshots renders disabled state correctly BananaRepublic in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  margin: 0 0 1.5rem;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  max-width: calc(100% - 2.3025rem);
  line-height: 1;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  cursor: default;
  color: #CCC;
  font-size: 1.1rem;
}

.emotion-0:hover {
  -webkit-text-decoration: underline;
  text-decoration: underline;
}

.emotion-1 {
  opacity: 0;
  position: absolute;
}

.emotion-1[data-focus-visible-added]+div {
  outline: 0;
  box-shadow: 0 0 0 0.125rem #5CABF7;
}

.emotion-2 {
  box-sizing: content-box;
  position: relative;
  display: block;
  margin-right: 0.625rem;
  min-width: 1.375rem;
  min-height: 1.375rem;
  border: 0.0625rem solid #CCC;
}

.emotion-2::before {
  content: "";
  display: block;
  position: absolute;
  width: calc(100% + 0.0625rem);
  height: calc(100% + 0.0625rem);
  -webkit-transform: scale(0);
  -moz-transform: scale(0);
  -ms-transform: scale(0);
  transform: scale(0);
  background-color: #0466CA;
}

.emotion-2::after {
  content: "";
  display: block;
  width: 100%;
  height: 100%;
  position: absolute;
  background-repeat: no-repeat;
  background-position-x: 0;
  background-position-y: 0.125rem;
  -webkit-background-size: 1.875rem;
  background-size: 1.875rem;
  -webkit-background-clip: border-box;
  background-clip: border-box;
  background-color: transparent;
}

<label
    class="emotion-0"
    disabled=""
    id="checkbox-id"
  >
    <input
      class="emotion-1"
      disabled=""
      type="checkbox"
    />
    <div
      aria-checked="false"
      aria-hidden="true"
      aria-labelledby="checkbox-id"
      class="emotion-2"
      disabled=""
      role="checkbox"
    />
    <div>
      I am default text
    </div>
  </label>
</DocumentFragment>
`;

exports[`<Checkbox/> snapshots renders disabled state correctly BananaRepublicFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  margin: 0 0 1.5rem;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  max-width: calc(100% - 2.3025rem);
  line-height: 0.75rem;
  font-family: 'Lynstone',Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  cursor: default;
  color: #CCC;
  font-size: 0.875rem;
  font-weight: 400;
}

.emotion-0:hover {
  -webkit-text-decoration: underline;
  text-decoration: underline;
}

.emotion-1 {
  opacity: 0;
  position: absolute;
}

.emotion-1[data-focus-visible-added]+div {
  outline: 0;
  box-shadow: 0 0 0 0.1875rem #CCC;
}

.emotion-2 {
  box-sizing: border-box;
  position: relative;
  display: block;
  margin-right: 0.75rem;
  min-width: 1.375rem;
  min-height: 1.375rem;
  border: 0.0625rem solid #CCC;
}

.emotion-2::before {
  content: "";
  display: block;
  position: absolute;
  width: calc(100% + 0.0625rem);
  height: calc(100% + 0.0625rem);
  -webkit-transform: scale(0);
  -moz-transform: scale(0);
  -ms-transform: scale(0);
  transform: scale(0);
  background-color: #2C2824;
}

.emotion-2::after {
  content: "";
  display: block;
  width: 100%;
  height: 100%;
  position: absolute;
  background-repeat: no-repeat;
  background-position-x: 0;
  background-position-y: 0.125rem;
  -webkit-background-size: 1.875rem;
  background-size: 1.875rem;
  -webkit-background-clip: border-box;
  background-clip: border-box;
  background-color: transparent;
}

<label
    class="emotion-0"
    disabled=""
    id="checkbox-id"
  >
    <input
      class="emotion-1"
      disabled=""
      type="checkbox"
    />
    <div
      aria-checked="false"
      aria-hidden="true"
      aria-labelledby="checkbox-id"
      class="emotion-2"
      disabled=""
      role="checkbox"
    />
    <div>
      I am default text
    </div>
  </label>
</DocumentFragment>
`;

exports[`<Checkbox/> snapshots renders disabled state correctly BananaRepublicFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  margin: 0 0 1.5rem;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  max-width: calc(100% - 2.3025rem);
  line-height: 1;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  cursor: default;
  color: #CCC;
  font-size: 1.1rem;
}

.emotion-0:hover {
  -webkit-text-decoration: underline;
  text-decoration: underline;
}

.emotion-1 {
  opacity: 0;
  position: absolute;
}

.emotion-1[data-focus-visible-added]+div {
  outline: 0;
  box-shadow: 0 0 0 0.125rem #5CABF7;
}

.emotion-2 {
  box-sizing: content-box;
  position: relative;
  display: block;
  margin-right: 0.625rem;
  min-width: 1.375rem;
  min-height: 1.375rem;
  border: 0.0625rem solid #CCC;
}

.emotion-2::before {
  content: "";
  display: block;
  position: absolute;
  width: calc(100% + 0.0625rem);
  height: calc(100% + 0.0625rem);
  -webkit-transform: scale(0);
  -moz-transform: scale(0);
  -ms-transform: scale(0);
  transform: scale(0);
  background-color: #0466CA;
}

.emotion-2::after {
  content: "";
  display: block;
  width: 100%;
  height: 100%;
  position: absolute;
  background-repeat: no-repeat;
  background-position-x: 0;
  background-position-y: 0.125rem;
  -webkit-background-size: 1.875rem;
  background-size: 1.875rem;
  -webkit-background-clip: border-box;
  background-clip: border-box;
  background-color: transparent;
}

<label
    class="emotion-0"
    disabled=""
    id="checkbox-id"
  >
    <input
      class="emotion-1"
      disabled=""
      type="checkbox"
    />
    <div
      aria-checked="false"
      aria-hidden="true"
      aria-labelledby="checkbox-id"
      class="emotion-2"
      disabled=""
      role="checkbox"
    />
    <div>
      I am default text
    </div>
  </label>
</DocumentFragment>
`;

exports[`<Checkbox/> snapshots renders disabled state correctly Gap 1`] = `
<DocumentFragment>
  .emotion-0 {
  margin: 0 0 1.5rem;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  max-width: calc(100% - 2.3025rem);
  line-height: 1;
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  cursor: default;
  color: #CCC;
  font-size: 1.063rem;
}

.emotion-0:hover {
  -webkit-text-decoration: underline;
  text-decoration: underline;
}

.emotion-1 {
  opacity: 0;
  position: absolute;
}

.emotion-1[data-focus-visible-added]+div {
  outline: 0;
  box-shadow: 0 0 0 0.1875rem #5CABF7;
}

.emotion-2 {
  box-sizing: border-box;
  position: relative;
  display: block;
  margin-right: 0.625rem;
  min-width: 1.375rem;
  min-height: 1.375rem;
  border: 0.0625rem solid #CCC;
}

.emotion-2::before {
  content: "";
  display: block;
  position: absolute;
  width: calc(100% + 0.0625rem);
  height: calc(100% + 0.0625rem);
  -webkit-transform: scale(0);
  -moz-transform: scale(0);
  -ms-transform: scale(0);
  transform: scale(0);
  background-color: #002554;
}

.emotion-2::after {
  content: "";
  display: block;
  width: 100%;
  height: 100%;
  position: absolute;
  background-repeat: no-repeat;
  background-position-x: 0;
  background-position-y: 0.125rem;
  -webkit-background-size: 1.875rem;
  background-size: 1.875rem;
  -webkit-background-clip: border-box;
  background-clip: border-box;
  background-color: transparent;
}

<label
    class="emotion-0"
    disabled=""
    id="checkbox-id"
  >
    <input
      class="emotion-1"
      disabled=""
      type="checkbox"
    />
    <div
      aria-checked="false"
      aria-hidden="true"
      aria-labelledby="checkbox-id"
      class="emotion-2"
      disabled=""
      role="checkbox"
    />
    <div>
      I am default text
    </div>
  </label>
</DocumentFragment>
`;

exports[`<Checkbox/> snapshots renders disabled state correctly Gap in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  margin: 0 0 1.5rem;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  max-width: calc(100% - 2.3025rem);
  line-height: 1;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  cursor: default;
  color: #CCC;
  font-size: 1.1rem;
}

.emotion-0:hover {
  -webkit-text-decoration: underline;
  text-decoration: underline;
}

.emotion-1 {
  opacity: 0;
  position: absolute;
}

.emotion-1[data-focus-visible-added]+div {
  outline: 0;
  box-shadow: 0 0 0 0.125rem #5CABF7;
}

.emotion-2 {
  box-sizing: content-box;
  position: relative;
  display: block;
  margin-right: 0.625rem;
  min-width: 1.375rem;
  min-height: 1.375rem;
  border: 0.0625rem solid #CCC;
}

.emotion-2::before {
  content: "";
  display: block;
  position: absolute;
  width: calc(100% + 0.0625rem);
  height: calc(100% + 0.0625rem);
  -webkit-transform: scale(0);
  -moz-transform: scale(0);
  -ms-transform: scale(0);
  transform: scale(0);
  background-color: #0466CA;
}

.emotion-2::after {
  content: "";
  display: block;
  width: 100%;
  height: 100%;
  position: absolute;
  background-repeat: no-repeat;
  background-position-x: 0;
  background-position-y: 0.125rem;
  -webkit-background-size: 1.875rem;
  background-size: 1.875rem;
  -webkit-background-clip: border-box;
  background-clip: border-box;
  background-color: transparent;
}

<label
    class="emotion-0"
    disabled=""
    id="checkbox-id"
  >
    <input
      class="emotion-1"
      disabled=""
      type="checkbox"
    />
    <div
      aria-checked="false"
      aria-hidden="true"
      aria-labelledby="checkbox-id"
      class="emotion-2"
      disabled=""
      role="checkbox"
    />
    <div>
      I am default text
    </div>
  </label>
</DocumentFragment>
`;

exports[`<Checkbox/> snapshots renders disabled state correctly GapFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  margin: 0 0 1.5rem;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  max-width: calc(100% - 2.3025rem);
  line-height: 1;
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  cursor: default;
  color: #CCC;
  font-size: 1.063rem;
}

.emotion-0:hover {
  -webkit-text-decoration: underline;
  text-decoration: underline;
}

.emotion-1 {
  opacity: 0;
  position: absolute;
}

.emotion-1[data-focus-visible-added]+div {
  outline: 0;
  box-shadow: 0 0 0 0.1875rem #5CABF7;
}

.emotion-2 {
  box-sizing: border-box;
  position: relative;
  display: block;
  margin-right: 0.625rem;
  min-width: 1.375rem;
  min-height: 1.375rem;
  border: 0.0625rem solid #CCC;
}

.emotion-2::before {
  content: "";
  display: block;
  position: absolute;
  width: calc(100% + 0.0625rem);
  height: calc(100% + 0.0625rem);
  -webkit-transform: scale(0);
  -moz-transform: scale(0);
  -ms-transform: scale(0);
  transform: scale(0);
  background-color: #002554;
}

.emotion-2::after {
  content: "";
  display: block;
  width: 100%;
  height: 100%;
  position: absolute;
  background-repeat: no-repeat;
  background-position-x: 0;
  background-position-y: 0.125rem;
  -webkit-background-size: 1.875rem;
  background-size: 1.875rem;
  -webkit-background-clip: border-box;
  background-clip: border-box;
  background-color: transparent;
}

<label
    class="emotion-0"
    disabled=""
    id="checkbox-id"
  >
    <input
      class="emotion-1"
      disabled=""
      type="checkbox"
    />
    <div
      aria-checked="false"
      aria-hidden="true"
      aria-labelledby="checkbox-id"
      class="emotion-2"
      disabled=""
      role="checkbox"
    />
    <div>
      I am default text
    </div>
  </label>
</DocumentFragment>
`;

exports[`<Checkbox/> snapshots renders disabled state correctly GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  margin: 0 0 1.5rem;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  max-width: calc(100% - 2.3025rem);
  line-height: 1;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  cursor: default;
  color: #CCC;
  font-size: 1.1rem;
}

.emotion-0:hover {
  -webkit-text-decoration: underline;
  text-decoration: underline;
}

.emotion-1 {
  opacity: 0;
  position: absolute;
}

.emotion-1[data-focus-visible-added]+div {
  outline: 0;
  box-shadow: 0 0 0 0.125rem #5CABF7;
}

.emotion-2 {
  box-sizing: content-box;
  position: relative;
  display: block;
  margin-right: 0.625rem;
  min-width: 1.375rem;
  min-height: 1.375rem;
  border: 0.0625rem solid #CCC;
}

.emotion-2::before {
  content: "";
  display: block;
  position: absolute;
  width: calc(100% + 0.0625rem);
  height: calc(100% + 0.0625rem);
  -webkit-transform: scale(0);
  -moz-transform: scale(0);
  -ms-transform: scale(0);
  transform: scale(0);
  background-color: #0466CA;
}

.emotion-2::after {
  content: "";
  display: block;
  width: 100%;
  height: 100%;
  position: absolute;
  background-repeat: no-repeat;
  background-position-x: 0;
  background-position-y: 0.125rem;
  -webkit-background-size: 1.875rem;
  background-size: 1.875rem;
  -webkit-background-clip: border-box;
  background-clip: border-box;
  background-color: transparent;
}

<label
    class="emotion-0"
    disabled=""
    id="checkbox-id"
  >
    <input
      class="emotion-1"
      disabled=""
      type="checkbox"
    />
    <div
      aria-checked="false"
      aria-hidden="true"
      aria-labelledby="checkbox-id"
      class="emotion-2"
      disabled=""
      role="checkbox"
    />
    <div>
      I am default text
    </div>
  </label>
</DocumentFragment>
`;

exports[`<Checkbox/> snapshots renders disabled state correctly OldNavy 1`] = `
<DocumentFragment>
  .emotion-0 {
  margin: 0 0 1.5rem;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  max-width: calc(100% - 2.3025rem);
  line-height: 1;
  font-family: 'Gotham','Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  cursor: default;
  color: #CCC;
  font-size: 1rem;
}

.emotion-0:hover {
  -webkit-text-decoration: underline;
  text-decoration: underline;
}

.emotion-1 {
  opacity: 0;
  position: absolute;
}

.emotion-1[data-focus-visible-added]+div {
  outline: 0;
  box-shadow: 0 0 0 0.1875rem #5CABF7;
}

.emotion-2 {
  box-sizing: border-box;
  position: relative;
  display: block;
  margin-right: 0.625rem;
  min-width: 1.375rem;
  min-height: 1.375rem;
  border: 0.0625rem solid #CCC;
}

.emotion-2::before {
  content: "";
  display: block;
  position: absolute;
  width: calc(100% + 0.0625rem);
  height: calc(100% + 0.0625rem);
  -webkit-transform: scale(0);
  -moz-transform: scale(0);
  -ms-transform: scale(0);
  transform: scale(0);
  background-color: #003764;
}

.emotion-2::after {
  content: "";
  display: block;
  width: 100%;
  height: 100%;
  position: absolute;
  background-repeat: no-repeat;
  background-position-x: 0;
  background-position-y: 0.125rem;
  -webkit-background-size: 1.875rem;
  background-size: 1.875rem;
  -webkit-background-clip: border-box;
  background-clip: border-box;
  background-color: transparent;
}

<label
    class="emotion-0"
    disabled=""
    id="checkbox-id"
  >
    <input
      class="emotion-1"
      disabled=""
      type="checkbox"
    />
    <div
      aria-checked="false"
      aria-hidden="true"
      aria-labelledby="checkbox-id"
      class="emotion-2"
      disabled=""
      role="checkbox"
    />
    <div>
      I am default text
    </div>
  </label>
</DocumentFragment>
`;

exports[`<Checkbox/> snapshots renders disabled state correctly OldNavy in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  margin: 0 0 1.5rem;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  max-width: calc(100% - 2.3025rem);
  line-height: 1;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  cursor: default;
  color: #CCC;
  font-size: 1.1rem;
}

.emotion-0:hover {
  -webkit-text-decoration: underline;
  text-decoration: underline;
}

.emotion-1 {
  opacity: 0;
  position: absolute;
}

.emotion-1[data-focus-visible-added]+div {
  outline: 0;
  box-shadow: 0 0 0 0.125rem #5CABF7;
}

.emotion-2 {
  box-sizing: content-box;
  position: relative;
  display: block;
  margin-right: 0.625rem;
  min-width: 1.375rem;
  min-height: 1.375rem;
  border: 0.0625rem solid #CCC;
}

.emotion-2::before {
  content: "";
  display: block;
  position: absolute;
  width: calc(100% + 0.0625rem);
  height: calc(100% + 0.0625rem);
  -webkit-transform: scale(0);
  -moz-transform: scale(0);
  -ms-transform: scale(0);
  transform: scale(0);
  background-color: #0466CA;
}

.emotion-2::after {
  content: "";
  display: block;
  width: 100%;
  height: 100%;
  position: absolute;
  background-repeat: no-repeat;
  background-position-x: 0;
  background-position-y: 0.125rem;
  -webkit-background-size: 1.875rem;
  background-size: 1.875rem;
  -webkit-background-clip: border-box;
  background-clip: border-box;
  background-color: transparent;
}

<label
    class="emotion-0"
    disabled=""
    id="checkbox-id"
  >
    <input
      class="emotion-1"
      disabled=""
      type="checkbox"
    />
    <div
      aria-checked="false"
      aria-hidden="true"
      aria-labelledby="checkbox-id"
      class="emotion-2"
      disabled=""
      role="checkbox"
    />
    <div>
      I am default text
    </div>
  </label>
</DocumentFragment>
`;

exports[`<Checkbox/> snapshots renders hasError state correctly Athleta 1`] = `
<DocumentFragment>
  .emotion-0 {
  margin: 0 0 1.5rem;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  max-width: calc(100% - 2.3025rem);
  line-height: 1;
  font-family: 'Avenir Next','Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  cursor: pointer;
  color: #333;
  font-size: 1.063rem;
}

.emotion-0:hover {
  -webkit-text-decoration: underline;
  text-decoration: underline;
}

.emotion-1 {
  opacity: 0;
  position: absolute;
}

.emotion-1[data-focus-visible-added]+div {
  outline: 0;
  box-shadow: 0 0 0 0.1875rem #5CABF7;
}

.emotion-2 {
  box-sizing: border-box;
  position: relative;
  display: block;
  margin-right: 0.625rem;
  min-width: 1.375rem;
  min-height: 1.375rem;
  border: 0.0625rem solid transparent;
  box-shadow: 0 0 0 0.125rem #D00000;
}

.emotion-2::before {
  content: "";
  display: block;
  position: absolute;
  width: calc(100% + 0.0625rem);
  height: calc(100% + 0.0625rem);
  -webkit-transform: scale(0);
  -moz-transform: scale(0);
  -ms-transform: scale(0);
  transform: scale(0);
  background-color: #333;
}

.emotion-2::after {
  content: "";
  display: block;
  width: 100%;
  height: 100%;
  position: absolute;
  background-repeat: no-repeat;
  background-position-x: 0;
  background-position-y: 0.125rem;
  -webkit-background-size: 1.875rem;
  background-size: 1.875rem;
  -webkit-background-clip: border-box;
  background-clip: border-box;
  background-color: transparent;
}

.emotion-3 {
  display: block;
  font-size: 0.8125rem;
  line-height: 1.2;
  margin-top: 0.25rem;
  color: #D00000;
  font-family: 'Avenir Next','Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  position: absolute;
}

<label
    class="emotion-0"
    id="checkbox-id"
  >
    <input
      class="emotion-1"
      required=""
      type="checkbox"
    />
    <div
      aria-checked="false"
      aria-hidden="true"
      aria-labelledby="checkbox-id"
      class="emotion-2"
      role="checkbox"
    />
    <div>
      I am default text
      <div
        class="emotion-3 emotion-4"
      >
        error
      </div>
    </div>
  </label>
</DocumentFragment>
`;

exports[`<Checkbox/> snapshots renders hasError state correctly Athleta in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  margin: 0 0 1.5rem;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  max-width: calc(100% - 2.3025rem);
  line-height: 1;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  cursor: pointer;
  color: #000;
  font-size: 1.1rem;
}

.emotion-0:hover {
  -webkit-text-decoration: underline;
  text-decoration: underline;
}

.emotion-1 {
  opacity: 0;
  position: absolute;
}

.emotion-1[data-focus-visible-added]+div {
  outline: 0;
  box-shadow: 0 0 0 0.125rem #5CABF7;
}

.emotion-2 {
  box-sizing: content-box;
  position: relative;
  display: block;
  margin-right: 0.625rem;
  min-width: 1.375rem;
  min-height: 1.375rem;
  border: 0.0625rem solid transparent;
  box-shadow: 0 0 0 0.125rem #D00000;
}

.emotion-2::before {
  content: "";
  display: block;
  position: absolute;
  width: calc(100% + 0.0625rem);
  height: calc(100% + 0.0625rem);
  -webkit-transform: scale(0);
  -moz-transform: scale(0);
  -ms-transform: scale(0);
  transform: scale(0);
  background-color: #0466CA;
}

.emotion-2::after {
  content: "";
  display: block;
  width: 100%;
  height: 100%;
  position: absolute;
  background-repeat: no-repeat;
  background-position-x: 0;
  background-position-y: 0.125rem;
  -webkit-background-size: 1.875rem;
  background-size: 1.875rem;
  -webkit-background-clip: border-box;
  background-clip: border-box;
  background-color: transparent;
}

.emotion-3 {
  display: block;
  font-size: 0.8125rem;
  line-height: 1.2;
  margin-top: 0.25rem;
  color: #D00000;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  position: absolute;
}

<label
    class="emotion-0"
    id="checkbox-id"
  >
    <input
      class="emotion-1"
      required=""
      type="checkbox"
    />
    <div
      aria-checked="false"
      aria-hidden="true"
      aria-labelledby="checkbox-id"
      class="emotion-2"
      role="checkbox"
    />
    <div>
      I am default text
      <div
        class="emotion-3 emotion-4"
      >
        error
      </div>
    </div>
  </label>
</DocumentFragment>
`;

exports[`<Checkbox/> snapshots renders hasError state correctly BananaRepublic 1`] = `
<DocumentFragment>
  .emotion-0 {
  margin: 0 0 1.5rem;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  max-width: calc(100% - 2.3025rem);
  line-height: 0.75rem;
  font-family: 'Lynstone',Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  cursor: pointer;
  color: #2C2824;
  font-size: 0.875rem;
  font-weight: 400;
}

.emotion-0:hover {
  -webkit-text-decoration: underline;
  text-decoration: underline;
}

.emotion-1 {
  opacity: 0;
  position: absolute;
}

.emotion-1[data-focus-visible-added]+div {
  outline: 0;
  box-shadow: 0 0 0 0.1875rem #CCC;
}

.emotion-2 {
  box-sizing: border-box;
  position: relative;
  display: block;
  margin-right: 0.75rem;
  min-width: 1.375rem;
  min-height: 1.375rem;
  border: 0.0625rem solid transparent;
  box-shadow: 0 0 0 0.125rem #D00000;
}

.emotion-2::before {
  content: "";
  display: block;
  position: absolute;
  width: calc(100% + 0.0625rem);
  height: calc(100% + 0.0625rem);
  -webkit-transform: scale(0);
  -moz-transform: scale(0);
  -ms-transform: scale(0);
  transform: scale(0);
  background-color: #2C2824;
}

.emotion-2::after {
  content: "";
  display: block;
  width: 100%;
  height: 100%;
  position: absolute;
  background-repeat: no-repeat;
  background-position-x: 0;
  background-position-y: 0.125rem;
  -webkit-background-size: 1.875rem;
  background-size: 1.875rem;
  -webkit-background-clip: border-box;
  background-clip: border-box;
  background-color: transparent;
}

.emotion-3 {
  display: block;
  font-size: 0.75rem;
  line-height: 1.2;
  margin-top: 0.25rem;
  color: #D00000;
  font-family: 'Lynstone',Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  position: absolute;
}

<label
    class="emotion-0"
    id="checkbox-id"
  >
    <input
      class="emotion-1"
      required=""
      type="checkbox"
    />
    <div
      aria-checked="false"
      aria-hidden="true"
      aria-labelledby="checkbox-id"
      class="emotion-2"
      role="checkbox"
    />
    <div>
      I am default text
      <div
        class="emotion-3 emotion-4"
      >
        error
      </div>
    </div>
  </label>
</DocumentFragment>
`;

exports[`<Checkbox/> snapshots renders hasError state correctly BananaRepublic in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  margin: 0 0 1.5rem;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  max-width: calc(100% - 2.3025rem);
  line-height: 1;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  cursor: pointer;
  color: #000;
  font-size: 1.1rem;
}

.emotion-0:hover {
  -webkit-text-decoration: underline;
  text-decoration: underline;
}

.emotion-1 {
  opacity: 0;
  position: absolute;
}

.emotion-1[data-focus-visible-added]+div {
  outline: 0;
  box-shadow: 0 0 0 0.125rem #5CABF7;
}

.emotion-2 {
  box-sizing: content-box;
  position: relative;
  display: block;
  margin-right: 0.625rem;
  min-width: 1.375rem;
  min-height: 1.375rem;
  border: 0.0625rem solid transparent;
  box-shadow: 0 0 0 0.125rem #D00000;
}

.emotion-2::before {
  content: "";
  display: block;
  position: absolute;
  width: calc(100% + 0.0625rem);
  height: calc(100% + 0.0625rem);
  -webkit-transform: scale(0);
  -moz-transform: scale(0);
  -ms-transform: scale(0);
  transform: scale(0);
  background-color: #0466CA;
}

.emotion-2::after {
  content: "";
  display: block;
  width: 100%;
  height: 100%;
  position: absolute;
  background-repeat: no-repeat;
  background-position-x: 0;
  background-position-y: 0.125rem;
  -webkit-background-size: 1.875rem;
  background-size: 1.875rem;
  -webkit-background-clip: border-box;
  background-clip: border-box;
  background-color: transparent;
}

.emotion-3 {
  display: block;
  font-size: 0.8125rem;
  line-height: 1.2;
  margin-top: 0.25rem;
  color: #D00000;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  position: absolute;
}

<label
    class="emotion-0"
    id="checkbox-id"
  >
    <input
      class="emotion-1"
      required=""
      type="checkbox"
    />
    <div
      aria-checked="false"
      aria-hidden="true"
      aria-labelledby="checkbox-id"
      class="emotion-2"
      role="checkbox"
    />
    <div>
      I am default text
      <div
        class="emotion-3 emotion-4"
      >
        error
      </div>
    </div>
  </label>
</DocumentFragment>
`;

exports[`<Checkbox/> snapshots renders hasError state correctly BananaRepublicFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  margin: 0 0 1.5rem;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  max-width: calc(100% - 2.3025rem);
  line-height: 0.75rem;
  font-family: 'Lynstone',Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  cursor: pointer;
  color: #2C2824;
  font-size: 0.875rem;
  font-weight: 400;
}

.emotion-0:hover {
  -webkit-text-decoration: underline;
  text-decoration: underline;
}

.emotion-1 {
  opacity: 0;
  position: absolute;
}

.emotion-1[data-focus-visible-added]+div {
  outline: 0;
  box-shadow: 0 0 0 0.1875rem #CCC;
}

.emotion-2 {
  box-sizing: border-box;
  position: relative;
  display: block;
  margin-right: 0.75rem;
  min-width: 1.375rem;
  min-height: 1.375rem;
  border: 0.0625rem solid transparent;
  box-shadow: 0 0 0 0.125rem #D00000;
}

.emotion-2::before {
  content: "";
  display: block;
  position: absolute;
  width: calc(100% + 0.0625rem);
  height: calc(100% + 0.0625rem);
  -webkit-transform: scale(0);
  -moz-transform: scale(0);
  -ms-transform: scale(0);
  transform: scale(0);
  background-color: #2C2824;
}

.emotion-2::after {
  content: "";
  display: block;
  width: 100%;
  height: 100%;
  position: absolute;
  background-repeat: no-repeat;
  background-position-x: 0;
  background-position-y: 0.125rem;
  -webkit-background-size: 1.875rem;
  background-size: 1.875rem;
  -webkit-background-clip: border-box;
  background-clip: border-box;
  background-color: transparent;
}

.emotion-3 {
  display: block;
  font-size: 0.75rem;
  line-height: 1.2;
  margin-top: 0.25rem;
  color: #D00000;
  font-family: 'Lynstone',Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  position: absolute;
}

<label
    class="emotion-0"
    id="checkbox-id"
  >
    <input
      class="emotion-1"
      required=""
      type="checkbox"
    />
    <div
      aria-checked="false"
      aria-hidden="true"
      aria-labelledby="checkbox-id"
      class="emotion-2"
      role="checkbox"
    />
    <div>
      I am default text
      <div
        class="emotion-3 emotion-4"
      >
        error
      </div>
    </div>
  </label>
</DocumentFragment>
`;

exports[`<Checkbox/> snapshots renders hasError state correctly BananaRepublicFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  margin: 0 0 1.5rem;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  max-width: calc(100% - 2.3025rem);
  line-height: 1;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  cursor: pointer;
  color: #000;
  font-size: 1.1rem;
}

.emotion-0:hover {
  -webkit-text-decoration: underline;
  text-decoration: underline;
}

.emotion-1 {
  opacity: 0;
  position: absolute;
}

.emotion-1[data-focus-visible-added]+div {
  outline: 0;
  box-shadow: 0 0 0 0.125rem #5CABF7;
}

.emotion-2 {
  box-sizing: content-box;
  position: relative;
  display: block;
  margin-right: 0.625rem;
  min-width: 1.375rem;
  min-height: 1.375rem;
  border: 0.0625rem solid transparent;
  box-shadow: 0 0 0 0.125rem #D00000;
}

.emotion-2::before {
  content: "";
  display: block;
  position: absolute;
  width: calc(100% + 0.0625rem);
  height: calc(100% + 0.0625rem);
  -webkit-transform: scale(0);
  -moz-transform: scale(0);
  -ms-transform: scale(0);
  transform: scale(0);
  background-color: #0466CA;
}

.emotion-2::after {
  content: "";
  display: block;
  width: 100%;
  height: 100%;
  position: absolute;
  background-repeat: no-repeat;
  background-position-x: 0;
  background-position-y: 0.125rem;
  -webkit-background-size: 1.875rem;
  background-size: 1.875rem;
  -webkit-background-clip: border-box;
  background-clip: border-box;
  background-color: transparent;
}

.emotion-3 {
  display: block;
  font-size: 0.8125rem;
  line-height: 1.2;
  margin-top: 0.25rem;
  color: #D00000;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  position: absolute;
}

<label
    class="emotion-0"
    id="checkbox-id"
  >
    <input
      class="emotion-1"
      required=""
      type="checkbox"
    />
    <div
      aria-checked="false"
      aria-hidden="true"
      aria-labelledby="checkbox-id"
      class="emotion-2"
      role="checkbox"
    />
    <div>
      I am default text
      <div
        class="emotion-3 emotion-4"
      >
        error
      </div>
    </div>
  </label>
</DocumentFragment>
`;

exports[`<Checkbox/> snapshots renders hasError state correctly Gap 1`] = `
<DocumentFragment>
  .emotion-0 {
  margin: 0 0 1.5rem;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  max-width: calc(100% - 2.3025rem);
  line-height: 1;
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  cursor: pointer;
  color: #000;
  font-size: 1.063rem;
}

.emotion-0:hover {
  -webkit-text-decoration: underline;
  text-decoration: underline;
}

.emotion-1 {
  opacity: 0;
  position: absolute;
}

.emotion-1[data-focus-visible-added]+div {
  outline: 0;
  box-shadow: 0 0 0 0.1875rem #5CABF7;
}

.emotion-2 {
  box-sizing: border-box;
  position: relative;
  display: block;
  margin-right: 0.625rem;
  min-width: 1.375rem;
  min-height: 1.375rem;
  border: 0.0625rem solid transparent;
  box-shadow: 0 0 0 0.125rem #D00000;
}

.emotion-2::before {
  content: "";
  display: block;
  position: absolute;
  width: calc(100% + 0.0625rem);
  height: calc(100% + 0.0625rem);
  -webkit-transform: scale(0);
  -moz-transform: scale(0);
  -ms-transform: scale(0);
  transform: scale(0);
  background-color: #002554;
}

.emotion-2::after {
  content: "";
  display: block;
  width: 100%;
  height: 100%;
  position: absolute;
  background-repeat: no-repeat;
  background-position-x: 0;
  background-position-y: 0.125rem;
  -webkit-background-size: 1.875rem;
  background-size: 1.875rem;
  -webkit-background-clip: border-box;
  background-clip: border-box;
  background-color: transparent;
}

.emotion-3 {
  display: block;
  font-size: 0.75rem;
  line-height: 1.2;
  margin-top: 0.25rem;
  color: #D00000;
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  position: absolute;
}

<label
    class="emotion-0"
    id="checkbox-id"
  >
    <input
      class="emotion-1"
      required=""
      type="checkbox"
    />
    <div
      aria-checked="false"
      aria-hidden="true"
      aria-labelledby="checkbox-id"
      class="emotion-2"
      role="checkbox"
    />
    <div>
      I am default text
      <div
        class="emotion-3 emotion-4"
      >
        error
      </div>
    </div>
  </label>
</DocumentFragment>
`;

exports[`<Checkbox/> snapshots renders hasError state correctly Gap in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  margin: 0 0 1.5rem;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  max-width: calc(100% - 2.3025rem);
  line-height: 1;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  cursor: pointer;
  color: #000;
  font-size: 1.1rem;
}

.emotion-0:hover {
  -webkit-text-decoration: underline;
  text-decoration: underline;
}

.emotion-1 {
  opacity: 0;
  position: absolute;
}

.emotion-1[data-focus-visible-added]+div {
  outline: 0;
  box-shadow: 0 0 0 0.125rem #5CABF7;
}

.emotion-2 {
  box-sizing: content-box;
  position: relative;
  display: block;
  margin-right: 0.625rem;
  min-width: 1.375rem;
  min-height: 1.375rem;
  border: 0.0625rem solid transparent;
  box-shadow: 0 0 0 0.125rem #D00000;
}

.emotion-2::before {
  content: "";
  display: block;
  position: absolute;
  width: calc(100% + 0.0625rem);
  height: calc(100% + 0.0625rem);
  -webkit-transform: scale(0);
  -moz-transform: scale(0);
  -ms-transform: scale(0);
  transform: scale(0);
  background-color: #0466CA;
}

.emotion-2::after {
  content: "";
  display: block;
  width: 100%;
  height: 100%;
  position: absolute;
  background-repeat: no-repeat;
  background-position-x: 0;
  background-position-y: 0.125rem;
  -webkit-background-size: 1.875rem;
  background-size: 1.875rem;
  -webkit-background-clip: border-box;
  background-clip: border-box;
  background-color: transparent;
}

.emotion-3 {
  display: block;
  font-size: 0.8125rem;
  line-height: 1.2;
  margin-top: 0.25rem;
  color: #D00000;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  position: absolute;
}

<label
    class="emotion-0"
    id="checkbox-id"
  >
    <input
      class="emotion-1"
      required=""
      type="checkbox"
    />
    <div
      aria-checked="false"
      aria-hidden="true"
      aria-labelledby="checkbox-id"
      class="emotion-2"
      role="checkbox"
    />
    <div>
      I am default text
      <div
        class="emotion-3 emotion-4"
      >
        error
      </div>
    </div>
  </label>
</DocumentFragment>
`;

exports[`<Checkbox/> snapshots renders hasError state correctly GapFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  margin: 0 0 1.5rem;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  max-width: calc(100% - 2.3025rem);
  line-height: 1;
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  cursor: pointer;
  color: #000;
  font-size: 1.063rem;
}

.emotion-0:hover {
  -webkit-text-decoration: underline;
  text-decoration: underline;
}

.emotion-1 {
  opacity: 0;
  position: absolute;
}

.emotion-1[data-focus-visible-added]+div {
  outline: 0;
  box-shadow: 0 0 0 0.1875rem #5CABF7;
}

.emotion-2 {
  box-sizing: border-box;
  position: relative;
  display: block;
  margin-right: 0.625rem;
  min-width: 1.375rem;
  min-height: 1.375rem;
  border: 0.0625rem solid transparent;
  box-shadow: 0 0 0 0.125rem #D00000;
}

.emotion-2::before {
  content: "";
  display: block;
  position: absolute;
  width: calc(100% + 0.0625rem);
  height: calc(100% + 0.0625rem);
  -webkit-transform: scale(0);
  -moz-transform: scale(0);
  -ms-transform: scale(0);
  transform: scale(0);
  background-color: #002554;
}

.emotion-2::after {
  content: "";
  display: block;
  width: 100%;
  height: 100%;
  position: absolute;
  background-repeat: no-repeat;
  background-position-x: 0;
  background-position-y: 0.125rem;
  -webkit-background-size: 1.875rem;
  background-size: 1.875rem;
  -webkit-background-clip: border-box;
  background-clip: border-box;
  background-color: transparent;
}

.emotion-3 {
  display: block;
  font-size: 0.75rem;
  line-height: 1.2;
  margin-top: 0.25rem;
  color: #D00000;
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  position: absolute;
}

<label
    class="emotion-0"
    id="checkbox-id"
  >
    <input
      class="emotion-1"
      required=""
      type="checkbox"
    />
    <div
      aria-checked="false"
      aria-hidden="true"
      aria-labelledby="checkbox-id"
      class="emotion-2"
      role="checkbox"
    />
    <div>
      I am default text
      <div
        class="emotion-3 emotion-4"
      >
        error
      </div>
    </div>
  </label>
</DocumentFragment>
`;

exports[`<Checkbox/> snapshots renders hasError state correctly GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  margin: 0 0 1.5rem;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  max-width: calc(100% - 2.3025rem);
  line-height: 1;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  cursor: pointer;
  color: #000;
  font-size: 1.1rem;
}

.emotion-0:hover {
  -webkit-text-decoration: underline;
  text-decoration: underline;
}

.emotion-1 {
  opacity: 0;
  position: absolute;
}

.emotion-1[data-focus-visible-added]+div {
  outline: 0;
  box-shadow: 0 0 0 0.125rem #5CABF7;
}

.emotion-2 {
  box-sizing: content-box;
  position: relative;
  display: block;
  margin-right: 0.625rem;
  min-width: 1.375rem;
  min-height: 1.375rem;
  border: 0.0625rem solid transparent;
  box-shadow: 0 0 0 0.125rem #D00000;
}

.emotion-2::before {
  content: "";
  display: block;
  position: absolute;
  width: calc(100% + 0.0625rem);
  height: calc(100% + 0.0625rem);
  -webkit-transform: scale(0);
  -moz-transform: scale(0);
  -ms-transform: scale(0);
  transform: scale(0);
  background-color: #0466CA;
}

.emotion-2::after {
  content: "";
  display: block;
  width: 100%;
  height: 100%;
  position: absolute;
  background-repeat: no-repeat;
  background-position-x: 0;
  background-position-y: 0.125rem;
  -webkit-background-size: 1.875rem;
  background-size: 1.875rem;
  -webkit-background-clip: border-box;
  background-clip: border-box;
  background-color: transparent;
}

.emotion-3 {
  display: block;
  font-size: 0.8125rem;
  line-height: 1.2;
  margin-top: 0.25rem;
  color: #D00000;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  position: absolute;
}

<label
    class="emotion-0"
    id="checkbox-id"
  >
    <input
      class="emotion-1"
      required=""
      type="checkbox"
    />
    <div
      aria-checked="false"
      aria-hidden="true"
      aria-labelledby="checkbox-id"
      class="emotion-2"
      role="checkbox"
    />
    <div>
      I am default text
      <div
        class="emotion-3 emotion-4"
      >
        error
      </div>
    </div>
  </label>
</DocumentFragment>
`;

exports[`<Checkbox/> snapshots renders hasError state correctly OldNavy 1`] = `
<DocumentFragment>
  .emotion-0 {
  margin: 0 0 1.5rem;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  max-width: calc(100% - 2.3025rem);
  line-height: 1;
  font-family: 'Gotham','Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  cursor: pointer;
  color: #000;
  font-size: 1rem;
}

.emotion-0:hover {
  -webkit-text-decoration: underline;
  text-decoration: underline;
}

.emotion-1 {
  opacity: 0;
  position: absolute;
}

.emotion-1[data-focus-visible-added]+div {
  outline: 0;
  box-shadow: 0 0 0 0.1875rem #5CABF7;
}

.emotion-2 {
  box-sizing: border-box;
  position: relative;
  display: block;
  margin-right: 0.625rem;
  min-width: 1.375rem;
  min-height: 1.375rem;
  border: 0.0625rem solid transparent;
  box-shadow: 0 0 0 0.125rem #D00000;
}

.emotion-2::before {
  content: "";
  display: block;
  position: absolute;
  width: calc(100% + 0.0625rem);
  height: calc(100% + 0.0625rem);
  -webkit-transform: scale(0);
  -moz-transform: scale(0);
  -ms-transform: scale(0);
  transform: scale(0);
  background-color: #003764;
}

.emotion-2::after {
  content: "";
  display: block;
  width: 100%;
  height: 100%;
  position: absolute;
  background-repeat: no-repeat;
  background-position-x: 0;
  background-position-y: 0.125rem;
  -webkit-background-size: 1.875rem;
  background-size: 1.875rem;
  -webkit-background-clip: border-box;
  background-clip: border-box;
  background-color: transparent;
}

.emotion-3 {
  display: block;
  font-size: 0.8125rem;
  line-height: 1.2;
  margin-top: 0.25rem;
  color: #D00000;
  font-family: 'Gotham','Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  position: absolute;
}

<label
    class="emotion-0"
    id="checkbox-id"
  >
    <input
      class="emotion-1"
      required=""
      type="checkbox"
    />
    <div
      aria-checked="false"
      aria-hidden="true"
      aria-labelledby="checkbox-id"
      class="emotion-2"
      role="checkbox"
    />
    <div>
      I am default text
      <div
        class="emotion-3 emotion-4"
      >
        error
      </div>
    </div>
  </label>
</DocumentFragment>
`;

exports[`<Checkbox/> snapshots renders hasError state correctly OldNavy in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  margin: 0 0 1.5rem;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  max-width: calc(100% - 2.3025rem);
  line-height: 1;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  cursor: pointer;
  color: #000;
  font-size: 1.1rem;
}

.emotion-0:hover {
  -webkit-text-decoration: underline;
  text-decoration: underline;
}

.emotion-1 {
  opacity: 0;
  position: absolute;
}

.emotion-1[data-focus-visible-added]+div {
  outline: 0;
  box-shadow: 0 0 0 0.125rem #5CABF7;
}

.emotion-2 {
  box-sizing: content-box;
  position: relative;
  display: block;
  margin-right: 0.625rem;
  min-width: 1.375rem;
  min-height: 1.375rem;
  border: 0.0625rem solid transparent;
  box-shadow: 0 0 0 0.125rem #D00000;
}

.emotion-2::before {
  content: "";
  display: block;
  position: absolute;
  width: calc(100% + 0.0625rem);
  height: calc(100% + 0.0625rem);
  -webkit-transform: scale(0);
  -moz-transform: scale(0);
  -ms-transform: scale(0);
  transform: scale(0);
  background-color: #0466CA;
}

.emotion-2::after {
  content: "";
  display: block;
  width: 100%;
  height: 100%;
  position: absolute;
  background-repeat: no-repeat;
  background-position-x: 0;
  background-position-y: 0.125rem;
  -webkit-background-size: 1.875rem;
  background-size: 1.875rem;
  -webkit-background-clip: border-box;
  background-clip: border-box;
  background-color: transparent;
}

.emotion-3 {
  display: block;
  font-size: 0.8125rem;
  line-height: 1.2;
  margin-top: 0.25rem;
  color: #D00000;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  position: absolute;
}

<label
    class="emotion-0"
    id="checkbox-id"
  >
    <input
      class="emotion-1"
      required=""
      type="checkbox"
    />
    <div
      aria-checked="false"
      aria-hidden="true"
      aria-labelledby="checkbox-id"
      class="emotion-2"
      role="checkbox"
    />
    <div>
      I am default text
      <div
        class="emotion-3 emotion-4"
      >
        error
      </div>
    </div>
  </label>
</DocumentFragment>
`;

exports[`<Checkbox/> snapshots renders hasURL state correctly Athleta 1`] = `
<DocumentFragment>
  .emotion-0 {
  margin: 0 0 1.5rem;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  max-width: calc(100% - 2.3025rem);
  line-height: 1;
  font-family: 'Avenir Next','Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  cursor: pointer;
  color: #333;
  font-size: 1.063rem;
}

.emotion-0:hover {
  -webkit-text-decoration: underline;
  text-decoration: underline;
}

.emotion-1 {
  opacity: 0;
  position: absolute;
}

.emotion-1[data-focus-visible-added]+div {
  outline: 0;
  box-shadow: 0 0 0 0.1875rem #5CABF7;
}

.emotion-2 {
  box-sizing: border-box;
  position: relative;
  display: block;
  margin-right: 0.625rem;
  min-width: 1.375rem;
  min-height: 1.375rem;
  border: 0.0625rem solid #666;
}

.emotion-2::before {
  content: "";
  display: block;
  position: absolute;
  width: calc(100% + 0.0625rem);
  height: calc(100% + 0.0625rem);
  -webkit-transform: scale(0);
  -moz-transform: scale(0);
  -ms-transform: scale(0);
  transform: scale(0);
  background-color: #333;
}

.emotion-2::after {
  content: "";
  display: block;
  width: 100%;
  height: 100%;
  position: absolute;
  background-repeat: no-repeat;
  background-position-x: 0;
  background-position-y: 0.125rem;
  -webkit-background-size: 1.875rem;
  background-size: 1.875rem;
  -webkit-background-clip: border-box;
  background-clip: border-box;
  background-color: transparent;
}

<label
    class="emotion-0"
    id="checkbox-id"
  >
    <input
      class="emotion-1"
      type="checkbox"
    />
    <div
      aria-checked="false"
      aria-hidden="true"
      aria-labelledby="checkbox-id"
      class="emotion-2"
      role="checkbox"
    />
    <div>
      <a
        href="https://gap.com/"
      >
        I am default text
      </a>
    </div>
  </label>
</DocumentFragment>
`;

exports[`<Checkbox/> snapshots renders hasURL state correctly Athleta in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  margin: 0 0 1.5rem;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  max-width: calc(100% - 2.3025rem);
  line-height: 1;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  cursor: pointer;
  color: #000;
  font-size: 1.1rem;
}

.emotion-0:hover {
  -webkit-text-decoration: underline;
  text-decoration: underline;
}

.emotion-1 {
  opacity: 0;
  position: absolute;
}

.emotion-1[data-focus-visible-added]+div {
  outline: 0;
  box-shadow: 0 0 0 0.125rem #5CABF7;
}

.emotion-2 {
  box-sizing: content-box;
  position: relative;
  display: block;
  margin-right: 0.625rem;
  min-width: 1.375rem;
  min-height: 1.375rem;
  border: 0.0625rem solid #666;
}

.emotion-2::before {
  content: "";
  display: block;
  position: absolute;
  width: calc(100% + 0.0625rem);
  height: calc(100% + 0.0625rem);
  -webkit-transform: scale(0);
  -moz-transform: scale(0);
  -ms-transform: scale(0);
  transform: scale(0);
  background-color: #0466CA;
}

.emotion-2::after {
  content: "";
  display: block;
  width: 100%;
  height: 100%;
  position: absolute;
  background-repeat: no-repeat;
  background-position-x: 0;
  background-position-y: 0.125rem;
  -webkit-background-size: 1.875rem;
  background-size: 1.875rem;
  -webkit-background-clip: border-box;
  background-clip: border-box;
  background-color: transparent;
}

<label
    class="emotion-0"
    id="checkbox-id"
  >
    <input
      class="emotion-1"
      type="checkbox"
    />
    <div
      aria-checked="false"
      aria-hidden="true"
      aria-labelledby="checkbox-id"
      class="emotion-2"
      role="checkbox"
    />
    <div>
      <a
        href="https://gap.com/"
      >
        I am default text
      </a>
    </div>
  </label>
</DocumentFragment>
`;

exports[`<Checkbox/> snapshots renders hasURL state correctly BananaRepublic 1`] = `
<DocumentFragment>
  .emotion-0 {
  margin: 0 0 1.5rem;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  max-width: calc(100% - 2.3025rem);
  line-height: 0.75rem;
  font-family: 'Lynstone',Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  cursor: pointer;
  color: #2C2824;
  font-size: 0.875rem;
  font-weight: 400;
}

.emotion-0:hover {
  -webkit-text-decoration: underline;
  text-decoration: underline;
}

.emotion-1 {
  opacity: 0;
  position: absolute;
}

.emotion-1[data-focus-visible-added]+div {
  outline: 0;
  box-shadow: 0 0 0 0.1875rem #CCC;
}

.emotion-2 {
  box-sizing: border-box;
  position: relative;
  display: block;
  margin-right: 0.75rem;
  min-width: 1.375rem;
  min-height: 1.375rem;
  border: 0.0625rem solid #2C2824;
}

.emotion-2::before {
  content: "";
  display: block;
  position: absolute;
  width: calc(100% + 0.0625rem);
  height: calc(100% + 0.0625rem);
  -webkit-transform: scale(0);
  -moz-transform: scale(0);
  -ms-transform: scale(0);
  transform: scale(0);
  background-color: #2C2824;
}

.emotion-2::after {
  content: "";
  display: block;
  width: 100%;
  height: 100%;
  position: absolute;
  background-repeat: no-repeat;
  background-position-x: 0;
  background-position-y: 0.125rem;
  -webkit-background-size: 1.875rem;
  background-size: 1.875rem;
  -webkit-background-clip: border-box;
  background-clip: border-box;
  background-color: transparent;
}

<label
    class="emotion-0"
    id="checkbox-id"
  >
    <input
      class="emotion-1"
      type="checkbox"
    />
    <div
      aria-checked="false"
      aria-hidden="true"
      aria-labelledby="checkbox-id"
      class="emotion-2"
      role="checkbox"
    />
    <div>
      <a
        href="https://gap.com/"
      >
        I am default text
      </a>
    </div>
  </label>
</DocumentFragment>
`;

exports[`<Checkbox/> snapshots renders hasURL state correctly BananaRepublic in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  margin: 0 0 1.5rem;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  max-width: calc(100% - 2.3025rem);
  line-height: 1;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  cursor: pointer;
  color: #000;
  font-size: 1.1rem;
}

.emotion-0:hover {
  -webkit-text-decoration: underline;
  text-decoration: underline;
}

.emotion-1 {
  opacity: 0;
  position: absolute;
}

.emotion-1[data-focus-visible-added]+div {
  outline: 0;
  box-shadow: 0 0 0 0.125rem #5CABF7;
}

.emotion-2 {
  box-sizing: content-box;
  position: relative;
  display: block;
  margin-right: 0.625rem;
  min-width: 1.375rem;
  min-height: 1.375rem;
  border: 0.0625rem solid #666;
}

.emotion-2::before {
  content: "";
  display: block;
  position: absolute;
  width: calc(100% + 0.0625rem);
  height: calc(100% + 0.0625rem);
  -webkit-transform: scale(0);
  -moz-transform: scale(0);
  -ms-transform: scale(0);
  transform: scale(0);
  background-color: #0466CA;
}

.emotion-2::after {
  content: "";
  display: block;
  width: 100%;
  height: 100%;
  position: absolute;
  background-repeat: no-repeat;
  background-position-x: 0;
  background-position-y: 0.125rem;
  -webkit-background-size: 1.875rem;
  background-size: 1.875rem;
  -webkit-background-clip: border-box;
  background-clip: border-box;
  background-color: transparent;
}

<label
    class="emotion-0"
    id="checkbox-id"
  >
    <input
      class="emotion-1"
      type="checkbox"
    />
    <div
      aria-checked="false"
      aria-hidden="true"
      aria-labelledby="checkbox-id"
      class="emotion-2"
      role="checkbox"
    />
    <div>
      <a
        href="https://gap.com/"
      >
        I am default text
      </a>
    </div>
  </label>
</DocumentFragment>
`;

exports[`<Checkbox/> snapshots renders hasURL state correctly BananaRepublicFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  margin: 0 0 1.5rem;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  max-width: calc(100% - 2.3025rem);
  line-height: 0.75rem;
  font-family: 'Lynstone',Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  cursor: pointer;
  color: #2C2824;
  font-size: 0.875rem;
  font-weight: 400;
}

.emotion-0:hover {
  -webkit-text-decoration: underline;
  text-decoration: underline;
}

.emotion-1 {
  opacity: 0;
  position: absolute;
}

.emotion-1[data-focus-visible-added]+div {
  outline: 0;
  box-shadow: 0 0 0 0.1875rem #CCC;
}

.emotion-2 {
  box-sizing: border-box;
  position: relative;
  display: block;
  margin-right: 0.75rem;
  min-width: 1.375rem;
  min-height: 1.375rem;
  border: 0.0625rem solid #2C2824;
}

.emotion-2::before {
  content: "";
  display: block;
  position: absolute;
  width: calc(100% + 0.0625rem);
  height: calc(100% + 0.0625rem);
  -webkit-transform: scale(0);
  -moz-transform: scale(0);
  -ms-transform: scale(0);
  transform: scale(0);
  background-color: #2C2824;
}

.emotion-2::after {
  content: "";
  display: block;
  width: 100%;
  height: 100%;
  position: absolute;
  background-repeat: no-repeat;
  background-position-x: 0;
  background-position-y: 0.125rem;
  -webkit-background-size: 1.875rem;
  background-size: 1.875rem;
  -webkit-background-clip: border-box;
  background-clip: border-box;
  background-color: transparent;
}

<label
    class="emotion-0"
    id="checkbox-id"
  >
    <input
      class="emotion-1"
      type="checkbox"
    />
    <div
      aria-checked="false"
      aria-hidden="true"
      aria-labelledby="checkbox-id"
      class="emotion-2"
      role="checkbox"
    />
    <div>
      <a
        href="https://gap.com/"
      >
        I am default text
      </a>
    </div>
  </label>
</DocumentFragment>
`;

exports[`<Checkbox/> snapshots renders hasURL state correctly BananaRepublicFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  margin: 0 0 1.5rem;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  max-width: calc(100% - 2.3025rem);
  line-height: 1;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  cursor: pointer;
  color: #000;
  font-size: 1.1rem;
}

.emotion-0:hover {
  -webkit-text-decoration: underline;
  text-decoration: underline;
}

.emotion-1 {
  opacity: 0;
  position: absolute;
}

.emotion-1[data-focus-visible-added]+div {
  outline: 0;
  box-shadow: 0 0 0 0.125rem #5CABF7;
}

.emotion-2 {
  box-sizing: content-box;
  position: relative;
  display: block;
  margin-right: 0.625rem;
  min-width: 1.375rem;
  min-height: 1.375rem;
  border: 0.0625rem solid #666;
}

.emotion-2::before {
  content: "";
  display: block;
  position: absolute;
  width: calc(100% + 0.0625rem);
  height: calc(100% + 0.0625rem);
  -webkit-transform: scale(0);
  -moz-transform: scale(0);
  -ms-transform: scale(0);
  transform: scale(0);
  background-color: #0466CA;
}

.emotion-2::after {
  content: "";
  display: block;
  width: 100%;
  height: 100%;
  position: absolute;
  background-repeat: no-repeat;
  background-position-x: 0;
  background-position-y: 0.125rem;
  -webkit-background-size: 1.875rem;
  background-size: 1.875rem;
  -webkit-background-clip: border-box;
  background-clip: border-box;
  background-color: transparent;
}

<label
    class="emotion-0"
    id="checkbox-id"
  >
    <input
      class="emotion-1"
      type="checkbox"
    />
    <div
      aria-checked="false"
      aria-hidden="true"
      aria-labelledby="checkbox-id"
      class="emotion-2"
      role="checkbox"
    />
    <div>
      <a
        href="https://gap.com/"
      >
        I am default text
      </a>
    </div>
  </label>
</DocumentFragment>
`;

exports[`<Checkbox/> snapshots renders hasURL state correctly Gap 1`] = `
<DocumentFragment>
  .emotion-0 {
  margin: 0 0 1.5rem;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  max-width: calc(100% - 2.3025rem);
  line-height: 1;
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  cursor: pointer;
  color: #000;
  font-size: 1.063rem;
}

.emotion-0:hover {
  -webkit-text-decoration: underline;
  text-decoration: underline;
}

.emotion-1 {
  opacity: 0;
  position: absolute;
}

.emotion-1[data-focus-visible-added]+div {
  outline: 0;
  box-shadow: 0 0 0 0.1875rem #5CABF7;
}

.emotion-2 {
  box-sizing: border-box;
  position: relative;
  display: block;
  margin-right: 0.625rem;
  min-width: 1.375rem;
  min-height: 1.375rem;
  border: 0.0625rem solid #666;
}

.emotion-2::before {
  content: "";
  display: block;
  position: absolute;
  width: calc(100% + 0.0625rem);
  height: calc(100% + 0.0625rem);
  -webkit-transform: scale(0);
  -moz-transform: scale(0);
  -ms-transform: scale(0);
  transform: scale(0);
  background-color: #002554;
}

.emotion-2::after {
  content: "";
  display: block;
  width: 100%;
  height: 100%;
  position: absolute;
  background-repeat: no-repeat;
  background-position-x: 0;
  background-position-y: 0.125rem;
  -webkit-background-size: 1.875rem;
  background-size: 1.875rem;
  -webkit-background-clip: border-box;
  background-clip: border-box;
  background-color: transparent;
}

<label
    class="emotion-0"
    id="checkbox-id"
  >
    <input
      class="emotion-1"
      type="checkbox"
    />
    <div
      aria-checked="false"
      aria-hidden="true"
      aria-labelledby="checkbox-id"
      class="emotion-2"
      role="checkbox"
    />
    <div>
      <a
        href="https://gap.com/"
      >
        I am default text
      </a>
    </div>
  </label>
</DocumentFragment>
`;

exports[`<Checkbox/> snapshots renders hasURL state correctly Gap in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  margin: 0 0 1.5rem;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  max-width: calc(100% - 2.3025rem);
  line-height: 1;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  cursor: pointer;
  color: #000;
  font-size: 1.1rem;
}

.emotion-0:hover {
  -webkit-text-decoration: underline;
  text-decoration: underline;
}

.emotion-1 {
  opacity: 0;
  position: absolute;
}

.emotion-1[data-focus-visible-added]+div {
  outline: 0;
  box-shadow: 0 0 0 0.125rem #5CABF7;
}

.emotion-2 {
  box-sizing: content-box;
  position: relative;
  display: block;
  margin-right: 0.625rem;
  min-width: 1.375rem;
  min-height: 1.375rem;
  border: 0.0625rem solid #666;
}

.emotion-2::before {
  content: "";
  display: block;
  position: absolute;
  width: calc(100% + 0.0625rem);
  height: calc(100% + 0.0625rem);
  -webkit-transform: scale(0);
  -moz-transform: scale(0);
  -ms-transform: scale(0);
  transform: scale(0);
  background-color: #0466CA;
}

.emotion-2::after {
  content: "";
  display: block;
  width: 100%;
  height: 100%;
  position: absolute;
  background-repeat: no-repeat;
  background-position-x: 0;
  background-position-y: 0.125rem;
  -webkit-background-size: 1.875rem;
  background-size: 1.875rem;
  -webkit-background-clip: border-box;
  background-clip: border-box;
  background-color: transparent;
}

<label
    class="emotion-0"
    id="checkbox-id"
  >
    <input
      class="emotion-1"
      type="checkbox"
    />
    <div
      aria-checked="false"
      aria-hidden="true"
      aria-labelledby="checkbox-id"
      class="emotion-2"
      role="checkbox"
    />
    <div>
      <a
        href="https://gap.com/"
      >
        I am default text
      </a>
    </div>
  </label>
</DocumentFragment>
`;

exports[`<Checkbox/> snapshots renders hasURL state correctly GapFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  margin: 0 0 1.5rem;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  max-width: calc(100% - 2.3025rem);
  line-height: 1;
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  cursor: pointer;
  color: #000;
  font-size: 1.063rem;
}

.emotion-0:hover {
  -webkit-text-decoration: underline;
  text-decoration: underline;
}

.emotion-1 {
  opacity: 0;
  position: absolute;
}

.emotion-1[data-focus-visible-added]+div {
  outline: 0;
  box-shadow: 0 0 0 0.1875rem #5CABF7;
}

.emotion-2 {
  box-sizing: border-box;
  position: relative;
  display: block;
  margin-right: 0.625rem;
  min-width: 1.375rem;
  min-height: 1.375rem;
  border: 0.0625rem solid #666;
}

.emotion-2::before {
  content: "";
  display: block;
  position: absolute;
  width: calc(100% + 0.0625rem);
  height: calc(100% + 0.0625rem);
  -webkit-transform: scale(0);
  -moz-transform: scale(0);
  -ms-transform: scale(0);
  transform: scale(0);
  background-color: #002554;
}

.emotion-2::after {
  content: "";
  display: block;
  width: 100%;
  height: 100%;
  position: absolute;
  background-repeat: no-repeat;
  background-position-x: 0;
  background-position-y: 0.125rem;
  -webkit-background-size: 1.875rem;
  background-size: 1.875rem;
  -webkit-background-clip: border-box;
  background-clip: border-box;
  background-color: transparent;
}

<label
    class="emotion-0"
    id="checkbox-id"
  >
    <input
      class="emotion-1"
      type="checkbox"
    />
    <div
      aria-checked="false"
      aria-hidden="true"
      aria-labelledby="checkbox-id"
      class="emotion-2"
      role="checkbox"
    />
    <div>
      <a
        href="https://gap.com/"
      >
        I am default text
      </a>
    </div>
  </label>
</DocumentFragment>
`;

exports[`<Checkbox/> snapshots renders hasURL state correctly GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  margin: 0 0 1.5rem;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  max-width: calc(100% - 2.3025rem);
  line-height: 1;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  cursor: pointer;
  color: #000;
  font-size: 1.1rem;
}

.emotion-0:hover {
  -webkit-text-decoration: underline;
  text-decoration: underline;
}

.emotion-1 {
  opacity: 0;
  position: absolute;
}

.emotion-1[data-focus-visible-added]+div {
  outline: 0;
  box-shadow: 0 0 0 0.125rem #5CABF7;
}

.emotion-2 {
  box-sizing: content-box;
  position: relative;
  display: block;
  margin-right: 0.625rem;
  min-width: 1.375rem;
  min-height: 1.375rem;
  border: 0.0625rem solid #666;
}

.emotion-2::before {
  content: "";
  display: block;
  position: absolute;
  width: calc(100% + 0.0625rem);
  height: calc(100% + 0.0625rem);
  -webkit-transform: scale(0);
  -moz-transform: scale(0);
  -ms-transform: scale(0);
  transform: scale(0);
  background-color: #0466CA;
}

.emotion-2::after {
  content: "";
  display: block;
  width: 100%;
  height: 100%;
  position: absolute;
  background-repeat: no-repeat;
  background-position-x: 0;
  background-position-y: 0.125rem;
  -webkit-background-size: 1.875rem;
  background-size: 1.875rem;
  -webkit-background-clip: border-box;
  background-clip: border-box;
  background-color: transparent;
}

<label
    class="emotion-0"
    id="checkbox-id"
  >
    <input
      class="emotion-1"
      type="checkbox"
    />
    <div
      aria-checked="false"
      aria-hidden="true"
      aria-labelledby="checkbox-id"
      class="emotion-2"
      role="checkbox"
    />
    <div>
      <a
        href="https://gap.com/"
      >
        I am default text
      </a>
    </div>
  </label>
</DocumentFragment>
`;

exports[`<Checkbox/> snapshots renders hasURL state correctly OldNavy 1`] = `
<DocumentFragment>
  .emotion-0 {
  margin: 0 0 1.5rem;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  max-width: calc(100% - 2.3025rem);
  line-height: 1;
  font-family: 'Gotham','Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  cursor: pointer;
  color: #000;
  font-size: 1rem;
}

.emotion-0:hover {
  -webkit-text-decoration: underline;
  text-decoration: underline;
}

.emotion-1 {
  opacity: 0;
  position: absolute;
}

.emotion-1[data-focus-visible-added]+div {
  outline: 0;
  box-shadow: 0 0 0 0.1875rem #5CABF7;
}

.emotion-2 {
  box-sizing: border-box;
  position: relative;
  display: block;
  margin-right: 0.625rem;
  min-width: 1.375rem;
  min-height: 1.375rem;
  border: 0.0625rem solid #666;
}

.emotion-2::before {
  content: "";
  display: block;
  position: absolute;
  width: calc(100% + 0.0625rem);
  height: calc(100% + 0.0625rem);
  -webkit-transform: scale(0);
  -moz-transform: scale(0);
  -ms-transform: scale(0);
  transform: scale(0);
  background-color: #003764;
}

.emotion-2::after {
  content: "";
  display: block;
  width: 100%;
  height: 100%;
  position: absolute;
  background-repeat: no-repeat;
  background-position-x: 0;
  background-position-y: 0.125rem;
  -webkit-background-size: 1.875rem;
  background-size: 1.875rem;
  -webkit-background-clip: border-box;
  background-clip: border-box;
  background-color: transparent;
}

<label
    class="emotion-0"
    id="checkbox-id"
  >
    <input
      class="emotion-1"
      type="checkbox"
    />
    <div
      aria-checked="false"
      aria-hidden="true"
      aria-labelledby="checkbox-id"
      class="emotion-2"
      role="checkbox"
    />
    <div>
      <a
        href="https://gap.com/"
      >
        I am default text
      </a>
    </div>
  </label>
</DocumentFragment>
`;

exports[`<Checkbox/> snapshots renders hasURL state correctly OldNavy in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  margin: 0 0 1.5rem;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  max-width: calc(100% - 2.3025rem);
  line-height: 1;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  cursor: pointer;
  color: #000;
  font-size: 1.1rem;
}

.emotion-0:hover {
  -webkit-text-decoration: underline;
  text-decoration: underline;
}

.emotion-1 {
  opacity: 0;
  position: absolute;
}

.emotion-1[data-focus-visible-added]+div {
  outline: 0;
  box-shadow: 0 0 0 0.125rem #5CABF7;
}

.emotion-2 {
  box-sizing: content-box;
  position: relative;
  display: block;
  margin-right: 0.625rem;
  min-width: 1.375rem;
  min-height: 1.375rem;
  border: 0.0625rem solid #666;
}

.emotion-2::before {
  content: "";
  display: block;
  position: absolute;
  width: calc(100% + 0.0625rem);
  height: calc(100% + 0.0625rem);
  -webkit-transform: scale(0);
  -moz-transform: scale(0);
  -ms-transform: scale(0);
  transform: scale(0);
  background-color: #0466CA;
}

.emotion-2::after {
  content: "";
  display: block;
  width: 100%;
  height: 100%;
  position: absolute;
  background-repeat: no-repeat;
  background-position-x: 0;
  background-position-y: 0.125rem;
  -webkit-background-size: 1.875rem;
  background-size: 1.875rem;
  -webkit-background-clip: border-box;
  background-clip: border-box;
  background-color: transparent;
}

<label
    class="emotion-0"
    id="checkbox-id"
  >
    <input
      class="emotion-1"
      type="checkbox"
    />
    <div
      aria-checked="false"
      aria-hidden="true"
      aria-labelledby="checkbox-id"
      class="emotion-2"
      role="checkbox"
    />
    <div>
      <a
        href="https://gap.com/"
      >
        I am default text
      </a>
    </div>
  </label>
</DocumentFragment>
`;

exports[`<Checkbox/> snapshots renders isChecked state correctly Athleta 1`] = `
<DocumentFragment>
  @keyframes animation-0 {
  0% {
    -webkit-transform: scale(0);
    -moz-transform: scale(0);
    -ms-transform: scale(0);
    transform: scale(0);
  }

  90% {
    -webkit-transform: scale(1.1);
    -moz-transform: scale(1.1);
    -ms-transform: scale(1.1);
    transform: scale(1.1);
  }

  100% {
    -webkit-transform: scale(1);
    -moz-transform: scale(1);
    -ms-transform: scale(1);
    transform: scale(1);
  }
}

.emotion-0 {
  margin: 0 0 1.5rem;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  max-width: calc(100% - 2.3025rem);
  line-height: 1;
  font-family: 'Avenir Next','Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  cursor: pointer;
  color: #333;
  font-size: 1.063rem;
}

.emotion-0:hover {
  -webkit-text-decoration: underline;
  text-decoration: underline;
}

.emotion-1 {
  opacity: 0;
  position: absolute;
}

.emotion-1[data-focus-visible-added]+div {
  outline: 0;
  box-shadow: 0 0 0 0.1875rem #5CABF7;
}

.emotion-2 {
  box-sizing: border-box;
  position: relative;
  display: block;
  margin-right: 0.625rem;
  min-width: 1.375rem;
  min-height: 1.375rem;
  border: 0.0625rem solid #000;
}

.emotion-2::before {
  content: "";
  display: block;
  position: absolute;
  width: calc(100% + 0.0625rem);
  height: calc(100% + 0.0625rem);
  -webkit-transform: scale(1);
  -moz-transform: scale(1);
  -ms-transform: scale(1);
  transform: scale(1);
  background-color: #333;
  -webkit-animation: animation-0 250ms cubic-bezier(0.175, 0.885, 0.32, 1.275);
  animation: animation-0 250ms cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

.emotion-2::after {
  content: "";
  display: block;
  width: 100%;
  height: 100%;
  position: absolute;
  background-repeat: no-repeat;
  background-position-x: 0.125rem;
  background-position-y: 0.25rem;
  -webkit-background-size: 1.6875rem;
  background-size: 1.6875rem;
  -webkit-background-clip: border-box;
  background-clip: border-box;
  background-color: transparent;
  background-image: url("data:image/svg+xml;charset=utf8,%3Csvg%20width%3D%2220%22%20height%3D%2220%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%3E%0A%0A%3Cpath%20d%3D%22M11.884 0L4.091 7.793.858 4.61 0 5.467l4.088 4.026.01-.01.009.01L12.74.857z%22%20fill%3D%22%23FFF%22%20fill-rule%3D%22nonzero%22%20stroke%3D%22transparent%22%20stroke-width%3D%220%22%20style%3D%22%22%20%2F%3E%3C%2Fsvg%3E%0A%0A%0A");
}

<label
    class="emotion-0"
    id="checkbox-id"
  >
    <input
      checked=""
      class="emotion-1"
      type="checkbox"
    />
    <div
      aria-checked="true"
      aria-hidden="true"
      aria-labelledby="checkbox-id"
      class="emotion-2"
      role="checkbox"
    />
    <div>
      I am default text
    </div>
  </label>
</DocumentFragment>
`;

exports[`<Checkbox/> snapshots renders isChecked state correctly Athleta in crossBrand 1`] = `
<DocumentFragment>
  @keyframes animation-0 {
  0% {
    -webkit-transform: scale(0);
    -moz-transform: scale(0);
    -ms-transform: scale(0);
    transform: scale(0);
  }

  90% {
    -webkit-transform: scale(1.1);
    -moz-transform: scale(1.1);
    -ms-transform: scale(1.1);
    transform: scale(1.1);
  }

  100% {
    -webkit-transform: scale(1);
    -moz-transform: scale(1);
    -ms-transform: scale(1);
    transform: scale(1);
  }
}

@keyframes animation-0 {
  0% {
    -webkit-transform: scale(0);
    -moz-transform: scale(0);
    -ms-transform: scale(0);
    transform: scale(0);
  }

  90% {
    -webkit-transform: scale(1.1);
    -moz-transform: scale(1.1);
    -ms-transform: scale(1.1);
    transform: scale(1.1);
  }

  100% {
    -webkit-transform: scale(1);
    -moz-transform: scale(1);
    -ms-transform: scale(1);
    transform: scale(1);
  }
}

.emotion-0 {
  margin: 0 0 1.5rem;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  max-width: calc(100% - 2.3025rem);
  line-height: 1;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  cursor: pointer;
  color: #000;
  font-size: 1.1rem;
}

.emotion-0:hover {
  -webkit-text-decoration: underline;
  text-decoration: underline;
}

.emotion-1 {
  opacity: 0;
  position: absolute;
}

.emotion-1[data-focus-visible-added]+div {
  outline: 0;
  box-shadow: 0 0 0 0.125rem #5CABF7;
}

.emotion-2 {
  box-sizing: content-box;
  position: relative;
  display: block;
  margin-right: 0.625rem;
  min-width: 1.375rem;
  min-height: 1.375rem;
  border: 0.0625rem solid #0466CA;
}

.emotion-2::before {
  content: "";
  display: block;
  position: absolute;
  width: calc(100% + 0.0625rem);
  height: calc(100% + 0.0625rem);
  -webkit-transform: scale(1);
  -moz-transform: scale(1);
  -ms-transform: scale(1);
  transform: scale(1);
  background-color: #0466CA;
  -webkit-animation: animation-0 250ms cubic-bezier(0.175, 0.885, 0.32, 1.275);
  animation: animation-0 250ms cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

.emotion-2::after {
  content: "";
  display: block;
  width: 100%;
  height: 100%;
  position: absolute;
  background-repeat: no-repeat;
  background-position-x: 0.18755rem;
  background-position-y: 0.3125rem;
  -webkit-background-size: 1.6875rem;
  background-size: 1.6875rem;
  -webkit-background-clip: border-box;
  background-clip: border-box;
  background-color: transparent;
  background-image: url("data:image/svg+xml;charset=utf8,%3Csvg%20width%3D%2220%22%20height%3D%2220%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%3E%0A%0A%3Cpath%20d%3D%22M11.884 0L4.091 7.793.858 4.61 0 5.467l4.088 4.026.01-.01.009.01L12.74.857z%22%20fill%3D%22%23FFF%22%20fill-rule%3D%22nonzero%22%20stroke%3D%22transparent%22%20stroke-width%3D%220%22%20style%3D%22%22%20%2F%3E%3C%2Fsvg%3E%0A%0A%0A");
}

<label
    class="emotion-0"
    id="checkbox-id"
  >
    <input
      checked=""
      class="emotion-1"
      type="checkbox"
    />
    <div
      aria-checked="true"
      aria-hidden="true"
      aria-labelledby="checkbox-id"
      class="emotion-2"
      role="checkbox"
    />
    <div>
      I am default text
    </div>
  </label>
</DocumentFragment>
`;

exports[`<Checkbox/> snapshots renders isChecked state correctly BananaRepublic 1`] = `
<DocumentFragment>
  @keyframes animation-0 {
  0% {
    -webkit-transform: scale(0);
    -moz-transform: scale(0);
    -ms-transform: scale(0);
    transform: scale(0);
  }

  90% {
    -webkit-transform: scale(1.1);
    -moz-transform: scale(1.1);
    -ms-transform: scale(1.1);
    transform: scale(1.1);
  }

  100% {
    -webkit-transform: scale(1);
    -moz-transform: scale(1);
    -ms-transform: scale(1);
    transform: scale(1);
  }
}

@keyframes animation-0 {
  0% {
    -webkit-transform: scale(0);
    -moz-transform: scale(0);
    -ms-transform: scale(0);
    transform: scale(0);
  }

  90% {
    -webkit-transform: scale(1.1);
    -moz-transform: scale(1.1);
    -ms-transform: scale(1.1);
    transform: scale(1.1);
  }

  100% {
    -webkit-transform: scale(1);
    -moz-transform: scale(1);
    -ms-transform: scale(1);
    transform: scale(1);
  }
}

@keyframes animation-0 {
  0% {
    -webkit-transform: scale(0);
    -moz-transform: scale(0);
    -ms-transform: scale(0);
    transform: scale(0);
  }

  90% {
    -webkit-transform: scale(1.1);
    -moz-transform: scale(1.1);
    -ms-transform: scale(1.1);
    transform: scale(1.1);
  }

  100% {
    -webkit-transform: scale(1);
    -moz-transform: scale(1);
    -ms-transform: scale(1);
    transform: scale(1);
  }
}

.emotion-0 {
  margin: 0 0 1.5rem;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  max-width: calc(100% - 2.3025rem);
  line-height: 0.75rem;
  font-family: 'Lynstone',Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  cursor: pointer;
  color: #2C2824;
  font-size: 0.875rem;
  font-weight: 400;
}

.emotion-0:hover {
  -webkit-text-decoration: underline;
  text-decoration: underline;
}

.emotion-1 {
  opacity: 0;
  position: absolute;
}

.emotion-1[data-focus-visible-added]+div {
  outline: 0;
  box-shadow: 0 0 0 0.1875rem #CCC;
}

.emotion-2 {
  box-sizing: border-box;
  position: relative;
  display: block;
  margin-right: 0.75rem;
  min-width: 1.375rem;
  min-height: 1.375rem;
  border: 0.0625rem solid #2C2824;
}

.emotion-2::before {
  content: "";
  display: block;
  position: absolute;
  width: calc(100% + 0.0625rem);
  height: calc(100% + 0.0625rem);
  -webkit-transform: scale(1);
  -moz-transform: scale(1);
  -ms-transform: scale(1);
  transform: scale(1);
  background-color: #2C2824;
  -webkit-animation: animation-0 250ms cubic-bezier(0.175, 0.885, 0.32, 1.275);
  animation: animation-0 250ms cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

.emotion-2::after {
  content: "";
  display: block;
  width: 100%;
  height: 100%;
  position: absolute;
  background-repeat: no-repeat;
  background-position-x: 0.125rem;
  background-position-y: 0.25rem;
  -webkit-background-size: 1.6875rem;
  background-size: 1.6875rem;
  -webkit-background-clip: border-box;
  background-clip: border-box;
  background-color: transparent;
  background-image: url("data:image/svg+xml;charset=utf8,%3Csvg%20width%3D%2220%22%20height%3D%2220%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%3E%0A%0A%3Cpath%20d%3D%22M11.884 0L4.091 7.793.858 4.61 0 5.467l4.088 4.026.01-.01.009.01L12.74.857z%22%20fill%3D%22%23FFF%22%20fill-rule%3D%22nonzero%22%20stroke%3D%22transparent%22%20stroke-width%3D%220%22%20style%3D%22%22%20%2F%3E%3C%2Fsvg%3E%0A%0A%0A");
}

<label
    class="emotion-0"
    id="checkbox-id"
  >
    <input
      checked=""
      class="emotion-1"
      type="checkbox"
    />
    <div
      aria-checked="true"
      aria-hidden="true"
      aria-labelledby="checkbox-id"
      class="emotion-2"
      role="checkbox"
    />
    <div>
      I am default text
    </div>
  </label>
</DocumentFragment>
`;

exports[`<Checkbox/> snapshots renders isChecked state correctly BananaRepublic in crossBrand 1`] = `
<DocumentFragment>
  @keyframes animation-0 {
  0% {
    -webkit-transform: scale(0);
    -moz-transform: scale(0);
    -ms-transform: scale(0);
    transform: scale(0);
  }

  90% {
    -webkit-transform: scale(1.1);
    -moz-transform: scale(1.1);
    -ms-transform: scale(1.1);
    transform: scale(1.1);
  }

  100% {
    -webkit-transform: scale(1);
    -moz-transform: scale(1);
    -ms-transform: scale(1);
    transform: scale(1);
  }
}

@keyframes animation-0 {
  0% {
    -webkit-transform: scale(0);
    -moz-transform: scale(0);
    -ms-transform: scale(0);
    transform: scale(0);
  }

  90% {
    -webkit-transform: scale(1.1);
    -moz-transform: scale(1.1);
    -ms-transform: scale(1.1);
    transform: scale(1.1);
  }

  100% {
    -webkit-transform: scale(1);
    -moz-transform: scale(1);
    -ms-transform: scale(1);
    transform: scale(1);
  }
}

@keyframes animation-0 {
  0% {
    -webkit-transform: scale(0);
    -moz-transform: scale(0);
    -ms-transform: scale(0);
    transform: scale(0);
  }

  90% {
    -webkit-transform: scale(1.1);
    -moz-transform: scale(1.1);
    -ms-transform: scale(1.1);
    transform: scale(1.1);
  }

  100% {
    -webkit-transform: scale(1);
    -moz-transform: scale(1);
    -ms-transform: scale(1);
    transform: scale(1);
  }
}

.emotion-0 {
  margin: 0 0 1.5rem;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  max-width: calc(100% - 2.3025rem);
  line-height: 1;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  cursor: pointer;
  color: #000;
  font-size: 1.1rem;
}

.emotion-0:hover {
  -webkit-text-decoration: underline;
  text-decoration: underline;
}

.emotion-1 {
  opacity: 0;
  position: absolute;
}

.emotion-1[data-focus-visible-added]+div {
  outline: 0;
  box-shadow: 0 0 0 0.125rem #5CABF7;
}

.emotion-2 {
  box-sizing: content-box;
  position: relative;
  display: block;
  margin-right: 0.625rem;
  min-width: 1.375rem;
  min-height: 1.375rem;
  border: 0.0625rem solid #0466CA;
}

.emotion-2::before {
  content: "";
  display: block;
  position: absolute;
  width: calc(100% + 0.0625rem);
  height: calc(100% + 0.0625rem);
  -webkit-transform: scale(1);
  -moz-transform: scale(1);
  -ms-transform: scale(1);
  transform: scale(1);
  background-color: #0466CA;
  -webkit-animation: animation-0 250ms cubic-bezier(0.175, 0.885, 0.32, 1.275);
  animation: animation-0 250ms cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

.emotion-2::after {
  content: "";
  display: block;
  width: 100%;
  height: 100%;
  position: absolute;
  background-repeat: no-repeat;
  background-position-x: 0.18755rem;
  background-position-y: 0.3125rem;
  -webkit-background-size: 1.6875rem;
  background-size: 1.6875rem;
  -webkit-background-clip: border-box;
  background-clip: border-box;
  background-color: transparent;
  background-image: url("data:image/svg+xml;charset=utf8,%3Csvg%20width%3D%2220%22%20height%3D%2220%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%3E%0A%0A%3Cpath%20d%3D%22M11.884 0L4.091 7.793.858 4.61 0 5.467l4.088 4.026.01-.01.009.01L12.74.857z%22%20fill%3D%22%23FFF%22%20fill-rule%3D%22nonzero%22%20stroke%3D%22transparent%22%20stroke-width%3D%220%22%20style%3D%22%22%20%2F%3E%3C%2Fsvg%3E%0A%0A%0A");
}

<label
    class="emotion-0"
    id="checkbox-id"
  >
    <input
      checked=""
      class="emotion-1"
      type="checkbox"
    />
    <div
      aria-checked="true"
      aria-hidden="true"
      aria-labelledby="checkbox-id"
      class="emotion-2"
      role="checkbox"
    />
    <div>
      I am default text
    </div>
  </label>
</DocumentFragment>
`;

exports[`<Checkbox/> snapshots renders isChecked state correctly BananaRepublicFactoryStore 1`] = `
<DocumentFragment>
  @keyframes animation-0 {
  0% {
    -webkit-transform: scale(0);
    -moz-transform: scale(0);
    -ms-transform: scale(0);
    transform: scale(0);
  }

  90% {
    -webkit-transform: scale(1.1);
    -moz-transform: scale(1.1);
    -ms-transform: scale(1.1);
    transform: scale(1.1);
  }

  100% {
    -webkit-transform: scale(1);
    -moz-transform: scale(1);
    -ms-transform: scale(1);
    transform: scale(1);
  }
}

@keyframes animation-0 {
  0% {
    -webkit-transform: scale(0);
    -moz-transform: scale(0);
    -ms-transform: scale(0);
    transform: scale(0);
  }

  90% {
    -webkit-transform: scale(1.1);
    -moz-transform: scale(1.1);
    -ms-transform: scale(1.1);
    transform: scale(1.1);
  }

  100% {
    -webkit-transform: scale(1);
    -moz-transform: scale(1);
    -ms-transform: scale(1);
    transform: scale(1);
  }
}

@keyframes animation-0 {
  0% {
    -webkit-transform: scale(0);
    -moz-transform: scale(0);
    -ms-transform: scale(0);
    transform: scale(0);
  }

  90% {
    -webkit-transform: scale(1.1);
    -moz-transform: scale(1.1);
    -ms-transform: scale(1.1);
    transform: scale(1.1);
  }

  100% {
    -webkit-transform: scale(1);
    -moz-transform: scale(1);
    -ms-transform: scale(1);
    transform: scale(1);
  }
}

.emotion-0 {
  margin: 0 0 1.5rem;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  max-width: calc(100% - 2.3025rem);
  line-height: 0.75rem;
  font-family: 'Lynstone',Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  cursor: pointer;
  color: #2C2824;
  font-size: 0.875rem;
  font-weight: 400;
}

.emotion-0:hover {
  -webkit-text-decoration: underline;
  text-decoration: underline;
}

.emotion-1 {
  opacity: 0;
  position: absolute;
}

.emotion-1[data-focus-visible-added]+div {
  outline: 0;
  box-shadow: 0 0 0 0.1875rem #CCC;
}

.emotion-2 {
  box-sizing: border-box;
  position: relative;
  display: block;
  margin-right: 0.75rem;
  min-width: 1.375rem;
  min-height: 1.375rem;
  border: 0.0625rem solid #2C2824;
}

.emotion-2::before {
  content: "";
  display: block;
  position: absolute;
  width: calc(100% + 0.0625rem);
  height: calc(100% + 0.0625rem);
  -webkit-transform: scale(1);
  -moz-transform: scale(1);
  -ms-transform: scale(1);
  transform: scale(1);
  background-color: #2C2824;
  -webkit-animation: animation-0 250ms cubic-bezier(0.175, 0.885, 0.32, 1.275);
  animation: animation-0 250ms cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

.emotion-2::after {
  content: "";
  display: block;
  width: 100%;
  height: 100%;
  position: absolute;
  background-repeat: no-repeat;
  background-position-x: 0.125rem;
  background-position-y: 0.25rem;
  -webkit-background-size: 1.6875rem;
  background-size: 1.6875rem;
  -webkit-background-clip: border-box;
  background-clip: border-box;
  background-color: transparent;
  background-image: url("data:image/svg+xml;charset=utf8,%3Csvg%20width%3D%2220%22%20height%3D%2220%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%3E%0A%0A%3Cpath%20d%3D%22M11.884 0L4.091 7.793.858 4.61 0 5.467l4.088 4.026.01-.01.009.01L12.74.857z%22%20fill%3D%22%23FFF%22%20fill-rule%3D%22nonzero%22%20stroke%3D%22transparent%22%20stroke-width%3D%220%22%20style%3D%22%22%20%2F%3E%3C%2Fsvg%3E%0A%0A%0A");
}

<label
    class="emotion-0"
    id="checkbox-id"
  >
    <input
      checked=""
      class="emotion-1"
      type="checkbox"
    />
    <div
      aria-checked="true"
      aria-hidden="true"
      aria-labelledby="checkbox-id"
      class="emotion-2"
      role="checkbox"
    />
    <div>
      I am default text
    </div>
  </label>
</DocumentFragment>
`;

exports[`<Checkbox/> snapshots renders isChecked state correctly BananaRepublicFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  @keyframes animation-0 {
  0% {
    -webkit-transform: scale(0);
    -moz-transform: scale(0);
    -ms-transform: scale(0);
    transform: scale(0);
  }

  90% {
    -webkit-transform: scale(1.1);
    -moz-transform: scale(1.1);
    -ms-transform: scale(1.1);
    transform: scale(1.1);
  }

  100% {
    -webkit-transform: scale(1);
    -moz-transform: scale(1);
    -ms-transform: scale(1);
    transform: scale(1);
  }
}

@keyframes animation-0 {
  0% {
    -webkit-transform: scale(0);
    -moz-transform: scale(0);
    -ms-transform: scale(0);
    transform: scale(0);
  }

  90% {
    -webkit-transform: scale(1.1);
    -moz-transform: scale(1.1);
    -ms-transform: scale(1.1);
    transform: scale(1.1);
  }

  100% {
    -webkit-transform: scale(1);
    -moz-transform: scale(1);
    -ms-transform: scale(1);
    transform: scale(1);
  }
}

@keyframes animation-0 {
  0% {
    -webkit-transform: scale(0);
    -moz-transform: scale(0);
    -ms-transform: scale(0);
    transform: scale(0);
  }

  90% {
    -webkit-transform: scale(1.1);
    -moz-transform: scale(1.1);
    -ms-transform: scale(1.1);
    transform: scale(1.1);
  }

  100% {
    -webkit-transform: scale(1);
    -moz-transform: scale(1);
    -ms-transform: scale(1);
    transform: scale(1);
  }
}

.emotion-0 {
  margin: 0 0 1.5rem;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  max-width: calc(100% - 2.3025rem);
  line-height: 1;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  cursor: pointer;
  color: #000;
  font-size: 1.1rem;
}

.emotion-0:hover {
  -webkit-text-decoration: underline;
  text-decoration: underline;
}

.emotion-1 {
  opacity: 0;
  position: absolute;
}

.emotion-1[data-focus-visible-added]+div {
  outline: 0;
  box-shadow: 0 0 0 0.125rem #5CABF7;
}

.emotion-2 {
  box-sizing: content-box;
  position: relative;
  display: block;
  margin-right: 0.625rem;
  min-width: 1.375rem;
  min-height: 1.375rem;
  border: 0.0625rem solid #0466CA;
}

.emotion-2::before {
  content: "";
  display: block;
  position: absolute;
  width: calc(100% + 0.0625rem);
  height: calc(100% + 0.0625rem);
  -webkit-transform: scale(1);
  -moz-transform: scale(1);
  -ms-transform: scale(1);
  transform: scale(1);
  background-color: #0466CA;
  -webkit-animation: animation-0 250ms cubic-bezier(0.175, 0.885, 0.32, 1.275);
  animation: animation-0 250ms cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

.emotion-2::after {
  content: "";
  display: block;
  width: 100%;
  height: 100%;
  position: absolute;
  background-repeat: no-repeat;
  background-position-x: 0.18755rem;
  background-position-y: 0.3125rem;
  -webkit-background-size: 1.6875rem;
  background-size: 1.6875rem;
  -webkit-background-clip: border-box;
  background-clip: border-box;
  background-color: transparent;
  background-image: url("data:image/svg+xml;charset=utf8,%3Csvg%20width%3D%2220%22%20height%3D%2220%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%3E%0A%0A%3Cpath%20d%3D%22M11.884 0L4.091 7.793.858 4.61 0 5.467l4.088 4.026.01-.01.009.01L12.74.857z%22%20fill%3D%22%23FFF%22%20fill-rule%3D%22nonzero%22%20stroke%3D%22transparent%22%20stroke-width%3D%220%22%20style%3D%22%22%20%2F%3E%3C%2Fsvg%3E%0A%0A%0A");
}

<label
    class="emotion-0"
    id="checkbox-id"
  >
    <input
      checked=""
      class="emotion-1"
      type="checkbox"
    />
    <div
      aria-checked="true"
      aria-hidden="true"
      aria-labelledby="checkbox-id"
      class="emotion-2"
      role="checkbox"
    />
    <div>
      I am default text
    </div>
  </label>
</DocumentFragment>
`;

exports[`<Checkbox/> snapshots renders isChecked state correctly Gap 1`] = `
<DocumentFragment>
  @keyframes animation-0 {
  0% {
    -webkit-transform: scale(0);
    -moz-transform: scale(0);
    -ms-transform: scale(0);
    transform: scale(0);
  }

  90% {
    -webkit-transform: scale(1.1);
    -moz-transform: scale(1.1);
    -ms-transform: scale(1.1);
    transform: scale(1.1);
  }

  100% {
    -webkit-transform: scale(1);
    -moz-transform: scale(1);
    -ms-transform: scale(1);
    transform: scale(1);
  }
}

@keyframes animation-0 {
  0% {
    -webkit-transform: scale(0);
    -moz-transform: scale(0);
    -ms-transform: scale(0);
    transform: scale(0);
  }

  90% {
    -webkit-transform: scale(1.1);
    -moz-transform: scale(1.1);
    -ms-transform: scale(1.1);
    transform: scale(1.1);
  }

  100% {
    -webkit-transform: scale(1);
    -moz-transform: scale(1);
    -ms-transform: scale(1);
    transform: scale(1);
  }
}

@keyframes animation-0 {
  0% {
    -webkit-transform: scale(0);
    -moz-transform: scale(0);
    -ms-transform: scale(0);
    transform: scale(0);
  }

  90% {
    -webkit-transform: scale(1.1);
    -moz-transform: scale(1.1);
    -ms-transform: scale(1.1);
    transform: scale(1.1);
  }

  100% {
    -webkit-transform: scale(1);
    -moz-transform: scale(1);
    -ms-transform: scale(1);
    transform: scale(1);
  }
}

@keyframes animation-0 {
  0% {
    -webkit-transform: scale(0);
    -moz-transform: scale(0);
    -ms-transform: scale(0);
    transform: scale(0);
  }

  90% {
    -webkit-transform: scale(1.1);
    -moz-transform: scale(1.1);
    -ms-transform: scale(1.1);
    transform: scale(1.1);
  }

  100% {
    -webkit-transform: scale(1);
    -moz-transform: scale(1);
    -ms-transform: scale(1);
    transform: scale(1);
  }
}

.emotion-0 {
  margin: 0 0 1.5rem;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  max-width: calc(100% - 2.3025rem);
  line-height: 1;
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  cursor: pointer;
  color: #000;
  font-size: 1.063rem;
}

.emotion-0:hover {
  -webkit-text-decoration: underline;
  text-decoration: underline;
}

.emotion-1 {
  opacity: 0;
  position: absolute;
}

.emotion-1[data-focus-visible-added]+div {
  outline: 0;
  box-shadow: 0 0 0 0.1875rem #5CABF7;
}

.emotion-2 {
  box-sizing: border-box;
  position: relative;
  display: block;
  margin-right: 0.625rem;
  min-width: 1.375rem;
  min-height: 1.375rem;
  border: 0.0625rem solid #002554;
}

.emotion-2::before {
  content: "";
  display: block;
  position: absolute;
  width: calc(100% + 0.0625rem);
  height: calc(100% + 0.0625rem);
  -webkit-transform: scale(1);
  -moz-transform: scale(1);
  -ms-transform: scale(1);
  transform: scale(1);
  background-color: #002554;
  -webkit-animation: animation-0 250ms cubic-bezier(0.175, 0.885, 0.32, 1.275);
  animation: animation-0 250ms cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

.emotion-2::after {
  content: "";
  display: block;
  width: 100%;
  height: 100%;
  position: absolute;
  background-repeat: no-repeat;
  background-position-x: 0.125rem;
  background-position-y: 0.25rem;
  -webkit-background-size: 1.6875rem;
  background-size: 1.6875rem;
  -webkit-background-clip: border-box;
  background-clip: border-box;
  background-color: transparent;
  background-image: url("data:image/svg+xml;charset=utf8,%3Csvg%20width%3D%2220%22%20height%3D%2220%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%3E%0A%0A%3Cpath%20d%3D%22M11.884 0L4.091 7.793.858 4.61 0 5.467l4.088 4.026.01-.01.009.01L12.74.857z%22%20fill%3D%22%23FFF%22%20fill-rule%3D%22nonzero%22%20stroke%3D%22transparent%22%20stroke-width%3D%220%22%20style%3D%22%22%20%2F%3E%3C%2Fsvg%3E%0A%0A%0A");
}

<label
    class="emotion-0"
    id="checkbox-id"
  >
    <input
      checked=""
      class="emotion-1"
      type="checkbox"
    />
    <div
      aria-checked="true"
      aria-hidden="true"
      aria-labelledby="checkbox-id"
      class="emotion-2"
      role="checkbox"
    />
    <div>
      I am default text
    </div>
  </label>
</DocumentFragment>
`;

exports[`<Checkbox/> snapshots renders isChecked state correctly Gap in crossBrand 1`] = `
<DocumentFragment>
  @keyframes animation-0 {
  0% {
    -webkit-transform: scale(0);
    -moz-transform: scale(0);
    -ms-transform: scale(0);
    transform: scale(0);
  }

  90% {
    -webkit-transform: scale(1.1);
    -moz-transform: scale(1.1);
    -ms-transform: scale(1.1);
    transform: scale(1.1);
  }

  100% {
    -webkit-transform: scale(1);
    -moz-transform: scale(1);
    -ms-transform: scale(1);
    transform: scale(1);
  }
}

@keyframes animation-0 {
  0% {
    -webkit-transform: scale(0);
    -moz-transform: scale(0);
    -ms-transform: scale(0);
    transform: scale(0);
  }

  90% {
    -webkit-transform: scale(1.1);
    -moz-transform: scale(1.1);
    -ms-transform: scale(1.1);
    transform: scale(1.1);
  }

  100% {
    -webkit-transform: scale(1);
    -moz-transform: scale(1);
    -ms-transform: scale(1);
    transform: scale(1);
  }
}

@keyframes animation-0 {
  0% {
    -webkit-transform: scale(0);
    -moz-transform: scale(0);
    -ms-transform: scale(0);
    transform: scale(0);
  }

  90% {
    -webkit-transform: scale(1.1);
    -moz-transform: scale(1.1);
    -ms-transform: scale(1.1);
    transform: scale(1.1);
  }

  100% {
    -webkit-transform: scale(1);
    -moz-transform: scale(1);
    -ms-transform: scale(1);
    transform: scale(1);
  }
}

@keyframes animation-0 {
  0% {
    -webkit-transform: scale(0);
    -moz-transform: scale(0);
    -ms-transform: scale(0);
    transform: scale(0);
  }

  90% {
    -webkit-transform: scale(1.1);
    -moz-transform: scale(1.1);
    -ms-transform: scale(1.1);
    transform: scale(1.1);
  }

  100% {
    -webkit-transform: scale(1);
    -moz-transform: scale(1);
    -ms-transform: scale(1);
    transform: scale(1);
  }
}

.emotion-0 {
  margin: 0 0 1.5rem;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  max-width: calc(100% - 2.3025rem);
  line-height: 1;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  cursor: pointer;
  color: #000;
  font-size: 1.1rem;
}

.emotion-0:hover {
  -webkit-text-decoration: underline;
  text-decoration: underline;
}

.emotion-1 {
  opacity: 0;
  position: absolute;
}

.emotion-1[data-focus-visible-added]+div {
  outline: 0;
  box-shadow: 0 0 0 0.125rem #5CABF7;
}

.emotion-2 {
  box-sizing: content-box;
  position: relative;
  display: block;
  margin-right: 0.625rem;
  min-width: 1.375rem;
  min-height: 1.375rem;
  border: 0.0625rem solid #0466CA;
}

.emotion-2::before {
  content: "";
  display: block;
  position: absolute;
  width: calc(100% + 0.0625rem);
  height: calc(100% + 0.0625rem);
  -webkit-transform: scale(1);
  -moz-transform: scale(1);
  -ms-transform: scale(1);
  transform: scale(1);
  background-color: #0466CA;
  -webkit-animation: animation-0 250ms cubic-bezier(0.175, 0.885, 0.32, 1.275);
  animation: animation-0 250ms cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

.emotion-2::after {
  content: "";
  display: block;
  width: 100%;
  height: 100%;
  position: absolute;
  background-repeat: no-repeat;
  background-position-x: 0.18755rem;
  background-position-y: 0.3125rem;
  -webkit-background-size: 1.6875rem;
  background-size: 1.6875rem;
  -webkit-background-clip: border-box;
  background-clip: border-box;
  background-color: transparent;
  background-image: url("data:image/svg+xml;charset=utf8,%3Csvg%20width%3D%2220%22%20height%3D%2220%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%3E%0A%0A%3Cpath%20d%3D%22M11.884 0L4.091 7.793.858 4.61 0 5.467l4.088 4.026.01-.01.009.01L12.74.857z%22%20fill%3D%22%23FFF%22%20fill-rule%3D%22nonzero%22%20stroke%3D%22transparent%22%20stroke-width%3D%220%22%20style%3D%22%22%20%2F%3E%3C%2Fsvg%3E%0A%0A%0A");
}

<label
    class="emotion-0"
    id="checkbox-id"
  >
    <input
      checked=""
      class="emotion-1"
      type="checkbox"
    />
    <div
      aria-checked="true"
      aria-hidden="true"
      aria-labelledby="checkbox-id"
      class="emotion-2"
      role="checkbox"
    />
    <div>
      I am default text
    </div>
  </label>
</DocumentFragment>
`;

exports[`<Checkbox/> snapshots renders isChecked state correctly GapFactoryStore 1`] = `
<DocumentFragment>
  @keyframes animation-0 {
  0% {
    -webkit-transform: scale(0);
    -moz-transform: scale(0);
    -ms-transform: scale(0);
    transform: scale(0);
  }

  90% {
    -webkit-transform: scale(1.1);
    -moz-transform: scale(1.1);
    -ms-transform: scale(1.1);
    transform: scale(1.1);
  }

  100% {
    -webkit-transform: scale(1);
    -moz-transform: scale(1);
    -ms-transform: scale(1);
    transform: scale(1);
  }
}

@keyframes animation-0 {
  0% {
    -webkit-transform: scale(0);
    -moz-transform: scale(0);
    -ms-transform: scale(0);
    transform: scale(0);
  }

  90% {
    -webkit-transform: scale(1.1);
    -moz-transform: scale(1.1);
    -ms-transform: scale(1.1);
    transform: scale(1.1);
  }

  100% {
    -webkit-transform: scale(1);
    -moz-transform: scale(1);
    -ms-transform: scale(1);
    transform: scale(1);
  }
}

@keyframes animation-0 {
  0% {
    -webkit-transform: scale(0);
    -moz-transform: scale(0);
    -ms-transform: scale(0);
    transform: scale(0);
  }

  90% {
    -webkit-transform: scale(1.1);
    -moz-transform: scale(1.1);
    -ms-transform: scale(1.1);
    transform: scale(1.1);
  }

  100% {
    -webkit-transform: scale(1);
    -moz-transform: scale(1);
    -ms-transform: scale(1);
    transform: scale(1);
  }
}

@keyframes animation-0 {
  0% {
    -webkit-transform: scale(0);
    -moz-transform: scale(0);
    -ms-transform: scale(0);
    transform: scale(0);
  }

  90% {
    -webkit-transform: scale(1.1);
    -moz-transform: scale(1.1);
    -ms-transform: scale(1.1);
    transform: scale(1.1);
  }

  100% {
    -webkit-transform: scale(1);
    -moz-transform: scale(1);
    -ms-transform: scale(1);
    transform: scale(1);
  }
}

.emotion-0 {
  margin: 0 0 1.5rem;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  max-width: calc(100% - 2.3025rem);
  line-height: 1;
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  cursor: pointer;
  color: #000;
  font-size: 1.063rem;
}

.emotion-0:hover {
  -webkit-text-decoration: underline;
  text-decoration: underline;
}

.emotion-1 {
  opacity: 0;
  position: absolute;
}

.emotion-1[data-focus-visible-added]+div {
  outline: 0;
  box-shadow: 0 0 0 0.1875rem #5CABF7;
}

.emotion-2 {
  box-sizing: border-box;
  position: relative;
  display: block;
  margin-right: 0.625rem;
  min-width: 1.375rem;
  min-height: 1.375rem;
  border: 0.0625rem solid #002554;
}

.emotion-2::before {
  content: "";
  display: block;
  position: absolute;
  width: calc(100% + 0.0625rem);
  height: calc(100% + 0.0625rem);
  -webkit-transform: scale(1);
  -moz-transform: scale(1);
  -ms-transform: scale(1);
  transform: scale(1);
  background-color: #002554;
  -webkit-animation: animation-0 250ms cubic-bezier(0.175, 0.885, 0.32, 1.275);
  animation: animation-0 250ms cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

.emotion-2::after {
  content: "";
  display: block;
  width: 100%;
  height: 100%;
  position: absolute;
  background-repeat: no-repeat;
  background-position-x: 0.125rem;
  background-position-y: 0.25rem;
  -webkit-background-size: 1.6875rem;
  background-size: 1.6875rem;
  -webkit-background-clip: border-box;
  background-clip: border-box;
  background-color: transparent;
  background-image: url("data:image/svg+xml;charset=utf8,%3Csvg%20width%3D%2220%22%20height%3D%2220%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%3E%0A%0A%3Cpath%20d%3D%22M11.884 0L4.091 7.793.858 4.61 0 5.467l4.088 4.026.01-.01.009.01L12.74.857z%22%20fill%3D%22%23FFF%22%20fill-rule%3D%22nonzero%22%20stroke%3D%22transparent%22%20stroke-width%3D%220%22%20style%3D%22%22%20%2F%3E%3C%2Fsvg%3E%0A%0A%0A");
}

<label
    class="emotion-0"
    id="checkbox-id"
  >
    <input
      checked=""
      class="emotion-1"
      type="checkbox"
    />
    <div
      aria-checked="true"
      aria-hidden="true"
      aria-labelledby="checkbox-id"
      class="emotion-2"
      role="checkbox"
    />
    <div>
      I am default text
    </div>
  </label>
</DocumentFragment>
`;

exports[`<Checkbox/> snapshots renders isChecked state correctly GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  @keyframes animation-0 {
  0% {
    -webkit-transform: scale(0);
    -moz-transform: scale(0);
    -ms-transform: scale(0);
    transform: scale(0);
  }

  90% {
    -webkit-transform: scale(1.1);
    -moz-transform: scale(1.1);
    -ms-transform: scale(1.1);
    transform: scale(1.1);
  }

  100% {
    -webkit-transform: scale(1);
    -moz-transform: scale(1);
    -ms-transform: scale(1);
    transform: scale(1);
  }
}

@keyframes animation-0 {
  0% {
    -webkit-transform: scale(0);
    -moz-transform: scale(0);
    -ms-transform: scale(0);
    transform: scale(0);
  }

  90% {
    -webkit-transform: scale(1.1);
    -moz-transform: scale(1.1);
    -ms-transform: scale(1.1);
    transform: scale(1.1);
  }

  100% {
    -webkit-transform: scale(1);
    -moz-transform: scale(1);
    -ms-transform: scale(1);
    transform: scale(1);
  }
}

@keyframes animation-0 {
  0% {
    -webkit-transform: scale(0);
    -moz-transform: scale(0);
    -ms-transform: scale(0);
    transform: scale(0);
  }

  90% {
    -webkit-transform: scale(1.1);
    -moz-transform: scale(1.1);
    -ms-transform: scale(1.1);
    transform: scale(1.1);
  }

  100% {
    -webkit-transform: scale(1);
    -moz-transform: scale(1);
    -ms-transform: scale(1);
    transform: scale(1);
  }
}

@keyframes animation-0 {
  0% {
    -webkit-transform: scale(0);
    -moz-transform: scale(0);
    -ms-transform: scale(0);
    transform: scale(0);
  }

  90% {
    -webkit-transform: scale(1.1);
    -moz-transform: scale(1.1);
    -ms-transform: scale(1.1);
    transform: scale(1.1);
  }

  100% {
    -webkit-transform: scale(1);
    -moz-transform: scale(1);
    -ms-transform: scale(1);
    transform: scale(1);
  }
}

.emotion-0 {
  margin: 0 0 1.5rem;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  max-width: calc(100% - 2.3025rem);
  line-height: 1;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  cursor: pointer;
  color: #000;
  font-size: 1.1rem;
}

.emotion-0:hover {
  -webkit-text-decoration: underline;
  text-decoration: underline;
}

.emotion-1 {
  opacity: 0;
  position: absolute;
}

.emotion-1[data-focus-visible-added]+div {
  outline: 0;
  box-shadow: 0 0 0 0.125rem #5CABF7;
}

.emotion-2 {
  box-sizing: content-box;
  position: relative;
  display: block;
  margin-right: 0.625rem;
  min-width: 1.375rem;
  min-height: 1.375rem;
  border: 0.0625rem solid #0466CA;
}

.emotion-2::before {
  content: "";
  display: block;
  position: absolute;
  width: calc(100% + 0.0625rem);
  height: calc(100% + 0.0625rem);
  -webkit-transform: scale(1);
  -moz-transform: scale(1);
  -ms-transform: scale(1);
  transform: scale(1);
  background-color: #0466CA;
  -webkit-animation: animation-0 250ms cubic-bezier(0.175, 0.885, 0.32, 1.275);
  animation: animation-0 250ms cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

.emotion-2::after {
  content: "";
  display: block;
  width: 100%;
  height: 100%;
  position: absolute;
  background-repeat: no-repeat;
  background-position-x: 0.18755rem;
  background-position-y: 0.3125rem;
  -webkit-background-size: 1.6875rem;
  background-size: 1.6875rem;
  -webkit-background-clip: border-box;
  background-clip: border-box;
  background-color: transparent;
  background-image: url("data:image/svg+xml;charset=utf8,%3Csvg%20width%3D%2220%22%20height%3D%2220%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%3E%0A%0A%3Cpath%20d%3D%22M11.884 0L4.091 7.793.858 4.61 0 5.467l4.088 4.026.01-.01.009.01L12.74.857z%22%20fill%3D%22%23FFF%22%20fill-rule%3D%22nonzero%22%20stroke%3D%22transparent%22%20stroke-width%3D%220%22%20style%3D%22%22%20%2F%3E%3C%2Fsvg%3E%0A%0A%0A");
}

<label
    class="emotion-0"
    id="checkbox-id"
  >
    <input
      checked=""
      class="emotion-1"
      type="checkbox"
    />
    <div
      aria-checked="true"
      aria-hidden="true"
      aria-labelledby="checkbox-id"
      class="emotion-2"
      role="checkbox"
    />
    <div>
      I am default text
    </div>
  </label>
</DocumentFragment>
`;

exports[`<Checkbox/> snapshots renders isChecked state correctly OldNavy 1`] = `
<DocumentFragment>
  @keyframes animation-0 {
  0% {
    -webkit-transform: scale(0);
    -moz-transform: scale(0);
    -ms-transform: scale(0);
    transform: scale(0);
  }

  90% {
    -webkit-transform: scale(1.1);
    -moz-transform: scale(1.1);
    -ms-transform: scale(1.1);
    transform: scale(1.1);
  }

  100% {
    -webkit-transform: scale(1);
    -moz-transform: scale(1);
    -ms-transform: scale(1);
    transform: scale(1);
  }
}

@keyframes animation-0 {
  0% {
    -webkit-transform: scale(0);
    -moz-transform: scale(0);
    -ms-transform: scale(0);
    transform: scale(0);
  }

  90% {
    -webkit-transform: scale(1.1);
    -moz-transform: scale(1.1);
    -ms-transform: scale(1.1);
    transform: scale(1.1);
  }

  100% {
    -webkit-transform: scale(1);
    -moz-transform: scale(1);
    -ms-transform: scale(1);
    transform: scale(1);
  }
}

@keyframes animation-0 {
  0% {
    -webkit-transform: scale(0);
    -moz-transform: scale(0);
    -ms-transform: scale(0);
    transform: scale(0);
  }

  90% {
    -webkit-transform: scale(1.1);
    -moz-transform: scale(1.1);
    -ms-transform: scale(1.1);
    transform: scale(1.1);
  }

  100% {
    -webkit-transform: scale(1);
    -moz-transform: scale(1);
    -ms-transform: scale(1);
    transform: scale(1);
  }
}

@keyframes animation-0 {
  0% {
    -webkit-transform: scale(0);
    -moz-transform: scale(0);
    -ms-transform: scale(0);
    transform: scale(0);
  }

  90% {
    -webkit-transform: scale(1.1);
    -moz-transform: scale(1.1);
    -ms-transform: scale(1.1);
    transform: scale(1.1);
  }

  100% {
    -webkit-transform: scale(1);
    -moz-transform: scale(1);
    -ms-transform: scale(1);
    transform: scale(1);
  }
}

@keyframes animation-0 {
  0% {
    -webkit-transform: scale(0);
    -moz-transform: scale(0);
    -ms-transform: scale(0);
    transform: scale(0);
  }

  90% {
    -webkit-transform: scale(1.1);
    -moz-transform: scale(1.1);
    -ms-transform: scale(1.1);
    transform: scale(1.1);
  }

  100% {
    -webkit-transform: scale(1);
    -moz-transform: scale(1);
    -ms-transform: scale(1);
    transform: scale(1);
  }
}

.emotion-0 {
  margin: 0 0 1.5rem;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  max-width: calc(100% - 2.3025rem);
  line-height: 1;
  font-family: 'Gotham','Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  cursor: pointer;
  color: #000;
  font-size: 1rem;
}

.emotion-0:hover {
  -webkit-text-decoration: underline;
  text-decoration: underline;
}

.emotion-1 {
  opacity: 0;
  position: absolute;
}

.emotion-1[data-focus-visible-added]+div {
  outline: 0;
  box-shadow: 0 0 0 0.1875rem #5CABF7;
}

.emotion-2 {
  box-sizing: border-box;
  position: relative;
  display: block;
  margin-right: 0.625rem;
  min-width: 1.375rem;
  min-height: 1.375rem;
  border: 0.0625rem solid #003764;
}

.emotion-2::before {
  content: "";
  display: block;
  position: absolute;
  width: calc(100% + 0.0625rem);
  height: calc(100% + 0.0625rem);
  -webkit-transform: scale(1);
  -moz-transform: scale(1);
  -ms-transform: scale(1);
  transform: scale(1);
  background-color: #003764;
  -webkit-animation: animation-0 250ms cubic-bezier(0.175, 0.885, 0.32, 1.275);
  animation: animation-0 250ms cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

.emotion-2::after {
  content: "";
  display: block;
  width: 100%;
  height: 100%;
  position: absolute;
  background-repeat: no-repeat;
  background-position-x: 0.125rem;
  background-position-y: 0.25rem;
  -webkit-background-size: 1.6875rem;
  background-size: 1.6875rem;
  -webkit-background-clip: border-box;
  background-clip: border-box;
  background-color: transparent;
  background-image: url("data:image/svg+xml;charset=utf8,%3Csvg%20width%3D%2220%22%20height%3D%2220%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%3E%0A%0A%3Cpath%20d%3D%22M11.884 0L4.091 7.793.858 4.61 0 5.467l4.088 4.026.01-.01.009.01L12.74.857z%22%20fill%3D%22%23FFF%22%20fill-rule%3D%22nonzero%22%20stroke%3D%22transparent%22%20stroke-width%3D%220%22%20style%3D%22%22%20%2F%3E%3C%2Fsvg%3E%0A%0A%0A");
}

<label
    class="emotion-0"
    id="checkbox-id"
  >
    <input
      checked=""
      class="emotion-1"
      type="checkbox"
    />
    <div
      aria-checked="true"
      aria-hidden="true"
      aria-labelledby="checkbox-id"
      class="emotion-2"
      role="checkbox"
    />
    <div>
      I am default text
    </div>
  </label>
</DocumentFragment>
`;

exports[`<Checkbox/> snapshots renders isChecked state correctly OldNavy in crossBrand 1`] = `
<DocumentFragment>
  @keyframes animation-0 {
  0% {
    -webkit-transform: scale(0);
    -moz-transform: scale(0);
    -ms-transform: scale(0);
    transform: scale(0);
  }

  90% {
    -webkit-transform: scale(1.1);
    -moz-transform: scale(1.1);
    -ms-transform: scale(1.1);
    transform: scale(1.1);
  }

  100% {
    -webkit-transform: scale(1);
    -moz-transform: scale(1);
    -ms-transform: scale(1);
    transform: scale(1);
  }
}

@keyframes animation-0 {
  0% {
    -webkit-transform: scale(0);
    -moz-transform: scale(0);
    -ms-transform: scale(0);
    transform: scale(0);
  }

  90% {
    -webkit-transform: scale(1.1);
    -moz-transform: scale(1.1);
    -ms-transform: scale(1.1);
    transform: scale(1.1);
  }

  100% {
    -webkit-transform: scale(1);
    -moz-transform: scale(1);
    -ms-transform: scale(1);
    transform: scale(1);
  }
}

@keyframes animation-0 {
  0% {
    -webkit-transform: scale(0);
    -moz-transform: scale(0);
    -ms-transform: scale(0);
    transform: scale(0);
  }

  90% {
    -webkit-transform: scale(1.1);
    -moz-transform: scale(1.1);
    -ms-transform: scale(1.1);
    transform: scale(1.1);
  }

  100% {
    -webkit-transform: scale(1);
    -moz-transform: scale(1);
    -ms-transform: scale(1);
    transform: scale(1);
  }
}

@keyframes animation-0 {
  0% {
    -webkit-transform: scale(0);
    -moz-transform: scale(0);
    -ms-transform: scale(0);
    transform: scale(0);
  }

  90% {
    -webkit-transform: scale(1.1);
    -moz-transform: scale(1.1);
    -ms-transform: scale(1.1);
    transform: scale(1.1);
  }

  100% {
    -webkit-transform: scale(1);
    -moz-transform: scale(1);
    -ms-transform: scale(1);
    transform: scale(1);
  }
}

@keyframes animation-0 {
  0% {
    -webkit-transform: scale(0);
    -moz-transform: scale(0);
    -ms-transform: scale(0);
    transform: scale(0);
  }

  90% {
    -webkit-transform: scale(1.1);
    -moz-transform: scale(1.1);
    -ms-transform: scale(1.1);
    transform: scale(1.1);
  }

  100% {
    -webkit-transform: scale(1);
    -moz-transform: scale(1);
    -ms-transform: scale(1);
    transform: scale(1);
  }
}

.emotion-0 {
  margin: 0 0 1.5rem;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  max-width: calc(100% - 2.3025rem);
  line-height: 1;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  cursor: pointer;
  color: #000;
  font-size: 1.1rem;
}

.emotion-0:hover {
  -webkit-text-decoration: underline;
  text-decoration: underline;
}

.emotion-1 {
  opacity: 0;
  position: absolute;
}

.emotion-1[data-focus-visible-added]+div {
  outline: 0;
  box-shadow: 0 0 0 0.125rem #5CABF7;
}

.emotion-2 {
  box-sizing: content-box;
  position: relative;
  display: block;
  margin-right: 0.625rem;
  min-width: 1.375rem;
  min-height: 1.375rem;
  border: 0.0625rem solid #0466CA;
}

.emotion-2::before {
  content: "";
  display: block;
  position: absolute;
  width: calc(100% + 0.0625rem);
  height: calc(100% + 0.0625rem);
  -webkit-transform: scale(1);
  -moz-transform: scale(1);
  -ms-transform: scale(1);
  transform: scale(1);
  background-color: #0466CA;
  -webkit-animation: animation-0 250ms cubic-bezier(0.175, 0.885, 0.32, 1.275);
  animation: animation-0 250ms cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

.emotion-2::after {
  content: "";
  display: block;
  width: 100%;
  height: 100%;
  position: absolute;
  background-repeat: no-repeat;
  background-position-x: 0.18755rem;
  background-position-y: 0.3125rem;
  -webkit-background-size: 1.6875rem;
  background-size: 1.6875rem;
  -webkit-background-clip: border-box;
  background-clip: border-box;
  background-color: transparent;
  background-image: url("data:image/svg+xml;charset=utf8,%3Csvg%20width%3D%2220%22%20height%3D%2220%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%3E%0A%0A%3Cpath%20d%3D%22M11.884 0L4.091 7.793.858 4.61 0 5.467l4.088 4.026.01-.01.009.01L12.74.857z%22%20fill%3D%22%23FFF%22%20fill-rule%3D%22nonzero%22%20stroke%3D%22transparent%22%20stroke-width%3D%220%22%20style%3D%22%22%20%2F%3E%3C%2Fsvg%3E%0A%0A%0A");
}

<label
    class="emotion-0"
    id="checkbox-id"
  >
    <input
      checked=""
      class="emotion-1"
      type="checkbox"
    />
    <div
      aria-checked="true"
      aria-hidden="true"
      aria-labelledby="checkbox-id"
      class="emotion-2"
      role="checkbox"
    />
    <div>
      I am default text
    </div>
  </label>
</DocumentFragment>
`;

exports[`<Checkbox/> snapshots renders isCrossBrand state correctly Athleta 1`] = `
<DocumentFragment>
  .emotion-0 {
  margin: 0 0 1.5rem;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  max-width: calc(100% - 2.3025rem);
  line-height: 1;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  cursor: pointer;
  color: #000;
  font-size: 1.1rem;
}

.emotion-0:hover {
  -webkit-text-decoration: underline;
  text-decoration: underline;
}

.emotion-1 {
  opacity: 0;
  position: absolute;
}

.emotion-1[data-focus-visible-added]+div {
  outline: 0;
  box-shadow: 0 0 0 0.125rem #5CABF7;
}

.emotion-2 {
  box-sizing: content-box;
  position: relative;
  display: block;
  margin-right: 0.625rem;
  min-width: 1.375rem;
  min-height: 1.375rem;
  border: 0.0625rem solid #666;
}

.emotion-2::before {
  content: "";
  display: block;
  position: absolute;
  width: calc(100% + 0.0625rem);
  height: calc(100% + 0.0625rem);
  -webkit-transform: scale(0);
  -moz-transform: scale(0);
  -ms-transform: scale(0);
  transform: scale(0);
  background-color: #0466CA;
}

.emotion-2::after {
  content: "";
  display: block;
  width: 100%;
  height: 100%;
  position: absolute;
  background-repeat: no-repeat;
  background-position-x: 0;
  background-position-y: 0.125rem;
  -webkit-background-size: 1.875rem;
  background-size: 1.875rem;
  -webkit-background-clip: border-box;
  background-clip: border-box;
  background-color: transparent;
}

<label
    class="emotion-0"
    id="checkbox-id"
  >
    <input
      class="emotion-1"
      type="checkbox"
    />
    <div
      aria-checked="false"
      aria-hidden="true"
      aria-labelledby="checkbox-id"
      class="emotion-2"
      role="checkbox"
    />
    <div>
      I am default text
    </div>
  </label>
</DocumentFragment>
`;

exports[`<Checkbox/> snapshots renders isCrossBrand state correctly Athleta in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  margin: 0 0 1.5rem;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  max-width: calc(100% - 2.3025rem);
  line-height: 1;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  cursor: pointer;
  color: #000;
  font-size: 1.1rem;
}

.emotion-0:hover {
  -webkit-text-decoration: underline;
  text-decoration: underline;
}

.emotion-1 {
  opacity: 0;
  position: absolute;
}

.emotion-1[data-focus-visible-added]+div {
  outline: 0;
  box-shadow: 0 0 0 0.125rem #5CABF7;
}

.emotion-2 {
  box-sizing: content-box;
  position: relative;
  display: block;
  margin-right: 0.625rem;
  min-width: 1.375rem;
  min-height: 1.375rem;
  border: 0.0625rem solid #666;
}

.emotion-2::before {
  content: "";
  display: block;
  position: absolute;
  width: calc(100% + 0.0625rem);
  height: calc(100% + 0.0625rem);
  -webkit-transform: scale(0);
  -moz-transform: scale(0);
  -ms-transform: scale(0);
  transform: scale(0);
  background-color: #0466CA;
}

.emotion-2::after {
  content: "";
  display: block;
  width: 100%;
  height: 100%;
  position: absolute;
  background-repeat: no-repeat;
  background-position-x: 0;
  background-position-y: 0.125rem;
  -webkit-background-size: 1.875rem;
  background-size: 1.875rem;
  -webkit-background-clip: border-box;
  background-clip: border-box;
  background-color: transparent;
}

<label
    class="emotion-0"
    id="checkbox-id"
  >
    <input
      class="emotion-1"
      type="checkbox"
    />
    <div
      aria-checked="false"
      aria-hidden="true"
      aria-labelledby="checkbox-id"
      class="emotion-2"
      role="checkbox"
    />
    <div>
      I am default text
    </div>
  </label>
</DocumentFragment>
`;

exports[`<Checkbox/> snapshots renders isCrossBrand state correctly BananaRepublic 1`] = `
<DocumentFragment>
  .emotion-0 {
  margin: 0 0 1.5rem;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  max-width: calc(100% - 2.3025rem);
  line-height: 1;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  cursor: pointer;
  color: #000;
  font-size: 1.1rem;
}

.emotion-0:hover {
  -webkit-text-decoration: underline;
  text-decoration: underline;
}

.emotion-1 {
  opacity: 0;
  position: absolute;
}

.emotion-1[data-focus-visible-added]+div {
  outline: 0;
  box-shadow: 0 0 0 0.125rem #5CABF7;
}

.emotion-2 {
  box-sizing: content-box;
  position: relative;
  display: block;
  margin-right: 0.625rem;
  min-width: 1.375rem;
  min-height: 1.375rem;
  border: 0.0625rem solid #666;
}

.emotion-2::before {
  content: "";
  display: block;
  position: absolute;
  width: calc(100% + 0.0625rem);
  height: calc(100% + 0.0625rem);
  -webkit-transform: scale(0);
  -moz-transform: scale(0);
  -ms-transform: scale(0);
  transform: scale(0);
  background-color: #0466CA;
}

.emotion-2::after {
  content: "";
  display: block;
  width: 100%;
  height: 100%;
  position: absolute;
  background-repeat: no-repeat;
  background-position-x: 0;
  background-position-y: 0.125rem;
  -webkit-background-size: 1.875rem;
  background-size: 1.875rem;
  -webkit-background-clip: border-box;
  background-clip: border-box;
  background-color: transparent;
}

<label
    class="emotion-0"
    id="checkbox-id"
  >
    <input
      class="emotion-1"
      type="checkbox"
    />
    <div
      aria-checked="false"
      aria-hidden="true"
      aria-labelledby="checkbox-id"
      class="emotion-2"
      role="checkbox"
    />
    <div>
      I am default text
    </div>
  </label>
</DocumentFragment>
`;

exports[`<Checkbox/> snapshots renders isCrossBrand state correctly BananaRepublic in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  margin: 0 0 1.5rem;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  max-width: calc(100% - 2.3025rem);
  line-height: 1;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  cursor: pointer;
  color: #000;
  font-size: 1.1rem;
}

.emotion-0:hover {
  -webkit-text-decoration: underline;
  text-decoration: underline;
}

.emotion-1 {
  opacity: 0;
  position: absolute;
}

.emotion-1[data-focus-visible-added]+div {
  outline: 0;
  box-shadow: 0 0 0 0.125rem #5CABF7;
}

.emotion-2 {
  box-sizing: content-box;
  position: relative;
  display: block;
  margin-right: 0.625rem;
  min-width: 1.375rem;
  min-height: 1.375rem;
  border: 0.0625rem solid #666;
}

.emotion-2::before {
  content: "";
  display: block;
  position: absolute;
  width: calc(100% + 0.0625rem);
  height: calc(100% + 0.0625rem);
  -webkit-transform: scale(0);
  -moz-transform: scale(0);
  -ms-transform: scale(0);
  transform: scale(0);
  background-color: #0466CA;
}

.emotion-2::after {
  content: "";
  display: block;
  width: 100%;
  height: 100%;
  position: absolute;
  background-repeat: no-repeat;
  background-position-x: 0;
  background-position-y: 0.125rem;
  -webkit-background-size: 1.875rem;
  background-size: 1.875rem;
  -webkit-background-clip: border-box;
  background-clip: border-box;
  background-color: transparent;
}

<label
    class="emotion-0"
    id="checkbox-id"
  >
    <input
      class="emotion-1"
      type="checkbox"
    />
    <div
      aria-checked="false"
      aria-hidden="true"
      aria-labelledby="checkbox-id"
      class="emotion-2"
      role="checkbox"
    />
    <div>
      I am default text
    </div>
  </label>
</DocumentFragment>
`;

exports[`<Checkbox/> snapshots renders isCrossBrand state correctly BananaRepublicFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  margin: 0 0 1.5rem;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  max-width: calc(100% - 2.3025rem);
  line-height: 1;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  cursor: pointer;
  color: #000;
  font-size: 1.1rem;
}

.emotion-0:hover {
  -webkit-text-decoration: underline;
  text-decoration: underline;
}

.emotion-1 {
  opacity: 0;
  position: absolute;
}

.emotion-1[data-focus-visible-added]+div {
  outline: 0;
  box-shadow: 0 0 0 0.125rem #5CABF7;
}

.emotion-2 {
  box-sizing: content-box;
  position: relative;
  display: block;
  margin-right: 0.625rem;
  min-width: 1.375rem;
  min-height: 1.375rem;
  border: 0.0625rem solid #666;
}

.emotion-2::before {
  content: "";
  display: block;
  position: absolute;
  width: calc(100% + 0.0625rem);
  height: calc(100% + 0.0625rem);
  -webkit-transform: scale(0);
  -moz-transform: scale(0);
  -ms-transform: scale(0);
  transform: scale(0);
  background-color: #0466CA;
}

.emotion-2::after {
  content: "";
  display: block;
  width: 100%;
  height: 100%;
  position: absolute;
  background-repeat: no-repeat;
  background-position-x: 0;
  background-position-y: 0.125rem;
  -webkit-background-size: 1.875rem;
  background-size: 1.875rem;
  -webkit-background-clip: border-box;
  background-clip: border-box;
  background-color: transparent;
}

<label
    class="emotion-0"
    id="checkbox-id"
  >
    <input
      class="emotion-1"
      type="checkbox"
    />
    <div
      aria-checked="false"
      aria-hidden="true"
      aria-labelledby="checkbox-id"
      class="emotion-2"
      role="checkbox"
    />
    <div>
      I am default text
    </div>
  </label>
</DocumentFragment>
`;

exports[`<Checkbox/> snapshots renders isCrossBrand state correctly BananaRepublicFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  margin: 0 0 1.5rem;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  max-width: calc(100% - 2.3025rem);
  line-height: 1;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  cursor: pointer;
  color: #000;
  font-size: 1.1rem;
}

.emotion-0:hover {
  -webkit-text-decoration: underline;
  text-decoration: underline;
}

.emotion-1 {
  opacity: 0;
  position: absolute;
}

.emotion-1[data-focus-visible-added]+div {
  outline: 0;
  box-shadow: 0 0 0 0.125rem #5CABF7;
}

.emotion-2 {
  box-sizing: content-box;
  position: relative;
  display: block;
  margin-right: 0.625rem;
  min-width: 1.375rem;
  min-height: 1.375rem;
  border: 0.0625rem solid #666;
}

.emotion-2::before {
  content: "";
  display: block;
  position: absolute;
  width: calc(100% + 0.0625rem);
  height: calc(100% + 0.0625rem);
  -webkit-transform: scale(0);
  -moz-transform: scale(0);
  -ms-transform: scale(0);
  transform: scale(0);
  background-color: #0466CA;
}

.emotion-2::after {
  content: "";
  display: block;
  width: 100%;
  height: 100%;
  position: absolute;
  background-repeat: no-repeat;
  background-position-x: 0;
  background-position-y: 0.125rem;
  -webkit-background-size: 1.875rem;
  background-size: 1.875rem;
  -webkit-background-clip: border-box;
  background-clip: border-box;
  background-color: transparent;
}

<label
    class="emotion-0"
    id="checkbox-id"
  >
    <input
      class="emotion-1"
      type="checkbox"
    />
    <div
      aria-checked="false"
      aria-hidden="true"
      aria-labelledby="checkbox-id"
      class="emotion-2"
      role="checkbox"
    />
    <div>
      I am default text
    </div>
  </label>
</DocumentFragment>
`;

exports[`<Checkbox/> snapshots renders isCrossBrand state correctly Gap 1`] = `
<DocumentFragment>
  .emotion-0 {
  margin: 0 0 1.5rem;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  max-width: calc(100% - 2.3025rem);
  line-height: 1;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  cursor: pointer;
  color: #000;
  font-size: 1.1rem;
}

.emotion-0:hover {
  -webkit-text-decoration: underline;
  text-decoration: underline;
}

.emotion-1 {
  opacity: 0;
  position: absolute;
}

.emotion-1[data-focus-visible-added]+div {
  outline: 0;
  box-shadow: 0 0 0 0.125rem #5CABF7;
}

.emotion-2 {
  box-sizing: content-box;
  position: relative;
  display: block;
  margin-right: 0.625rem;
  min-width: 1.375rem;
  min-height: 1.375rem;
  border: 0.0625rem solid #666;
}

.emotion-2::before {
  content: "";
  display: block;
  position: absolute;
  width: calc(100% + 0.0625rem);
  height: calc(100% + 0.0625rem);
  -webkit-transform: scale(0);
  -moz-transform: scale(0);
  -ms-transform: scale(0);
  transform: scale(0);
  background-color: #0466CA;
}

.emotion-2::after {
  content: "";
  display: block;
  width: 100%;
  height: 100%;
  position: absolute;
  background-repeat: no-repeat;
  background-position-x: 0;
  background-position-y: 0.125rem;
  -webkit-background-size: 1.875rem;
  background-size: 1.875rem;
  -webkit-background-clip: border-box;
  background-clip: border-box;
  background-color: transparent;
}

<label
    class="emotion-0"
    id="checkbox-id"
  >
    <input
      class="emotion-1"
      type="checkbox"
    />
    <div
      aria-checked="false"
      aria-hidden="true"
      aria-labelledby="checkbox-id"
      class="emotion-2"
      role="checkbox"
    />
    <div>
      I am default text
    </div>
  </label>
</DocumentFragment>
`;

exports[`<Checkbox/> snapshots renders isCrossBrand state correctly Gap in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  margin: 0 0 1.5rem;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  max-width: calc(100% - 2.3025rem);
  line-height: 1;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  cursor: pointer;
  color: #000;
  font-size: 1.1rem;
}

.emotion-0:hover {
  -webkit-text-decoration: underline;
  text-decoration: underline;
}

.emotion-1 {
  opacity: 0;
  position: absolute;
}

.emotion-1[data-focus-visible-added]+div {
  outline: 0;
  box-shadow: 0 0 0 0.125rem #5CABF7;
}

.emotion-2 {
  box-sizing: content-box;
  position: relative;
  display: block;
  margin-right: 0.625rem;
  min-width: 1.375rem;
  min-height: 1.375rem;
  border: 0.0625rem solid #666;
}

.emotion-2::before {
  content: "";
  display: block;
  position: absolute;
  width: calc(100% + 0.0625rem);
  height: calc(100% + 0.0625rem);
  -webkit-transform: scale(0);
  -moz-transform: scale(0);
  -ms-transform: scale(0);
  transform: scale(0);
  background-color: #0466CA;
}

.emotion-2::after {
  content: "";
  display: block;
  width: 100%;
  height: 100%;
  position: absolute;
  background-repeat: no-repeat;
  background-position-x: 0;
  background-position-y: 0.125rem;
  -webkit-background-size: 1.875rem;
  background-size: 1.875rem;
  -webkit-background-clip: border-box;
  background-clip: border-box;
  background-color: transparent;
}

<label
    class="emotion-0"
    id="checkbox-id"
  >
    <input
      class="emotion-1"
      type="checkbox"
    />
    <div
      aria-checked="false"
      aria-hidden="true"
      aria-labelledby="checkbox-id"
      class="emotion-2"
      role="checkbox"
    />
    <div>
      I am default text
    </div>
  </label>
</DocumentFragment>
`;

exports[`<Checkbox/> snapshots renders isCrossBrand state correctly GapFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  margin: 0 0 1.5rem;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  max-width: calc(100% - 2.3025rem);
  line-height: 1;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  cursor: pointer;
  color: #000;
  font-size: 1.1rem;
}

.emotion-0:hover {
  -webkit-text-decoration: underline;
  text-decoration: underline;
}

.emotion-1 {
  opacity: 0;
  position: absolute;
}

.emotion-1[data-focus-visible-added]+div {
  outline: 0;
  box-shadow: 0 0 0 0.125rem #5CABF7;
}

.emotion-2 {
  box-sizing: content-box;
  position: relative;
  display: block;
  margin-right: 0.625rem;
  min-width: 1.375rem;
  min-height: 1.375rem;
  border: 0.0625rem solid #666;
}

.emotion-2::before {
  content: "";
  display: block;
  position: absolute;
  width: calc(100% + 0.0625rem);
  height: calc(100% + 0.0625rem);
  -webkit-transform: scale(0);
  -moz-transform: scale(0);
  -ms-transform: scale(0);
  transform: scale(0);
  background-color: #0466CA;
}

.emotion-2::after {
  content: "";
  display: block;
  width: 100%;
  height: 100%;
  position: absolute;
  background-repeat: no-repeat;
  background-position-x: 0;
  background-position-y: 0.125rem;
  -webkit-background-size: 1.875rem;
  background-size: 1.875rem;
  -webkit-background-clip: border-box;
  background-clip: border-box;
  background-color: transparent;
}

<label
    class="emotion-0"
    id="checkbox-id"
  >
    <input
      class="emotion-1"
      type="checkbox"
    />
    <div
      aria-checked="false"
      aria-hidden="true"
      aria-labelledby="checkbox-id"
      class="emotion-2"
      role="checkbox"
    />
    <div>
      I am default text
    </div>
  </label>
</DocumentFragment>
`;

exports[`<Checkbox/> snapshots renders isCrossBrand state correctly GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  margin: 0 0 1.5rem;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  max-width: calc(100% - 2.3025rem);
  line-height: 1;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  cursor: pointer;
  color: #000;
  font-size: 1.1rem;
}

.emotion-0:hover {
  -webkit-text-decoration: underline;
  text-decoration: underline;
}

.emotion-1 {
  opacity: 0;
  position: absolute;
}

.emotion-1[data-focus-visible-added]+div {
  outline: 0;
  box-shadow: 0 0 0 0.125rem #5CABF7;
}

.emotion-2 {
  box-sizing: content-box;
  position: relative;
  display: block;
  margin-right: 0.625rem;
  min-width: 1.375rem;
  min-height: 1.375rem;
  border: 0.0625rem solid #666;
}

.emotion-2::before {
  content: "";
  display: block;
  position: absolute;
  width: calc(100% + 0.0625rem);
  height: calc(100% + 0.0625rem);
  -webkit-transform: scale(0);
  -moz-transform: scale(0);
  -ms-transform: scale(0);
  transform: scale(0);
  background-color: #0466CA;
}

.emotion-2::after {
  content: "";
  display: block;
  width: 100%;
  height: 100%;
  position: absolute;
  background-repeat: no-repeat;
  background-position-x: 0;
  background-position-y: 0.125rem;
  -webkit-background-size: 1.875rem;
  background-size: 1.875rem;
  -webkit-background-clip: border-box;
  background-clip: border-box;
  background-color: transparent;
}

<label
    class="emotion-0"
    id="checkbox-id"
  >
    <input
      class="emotion-1"
      type="checkbox"
    />
    <div
      aria-checked="false"
      aria-hidden="true"
      aria-labelledby="checkbox-id"
      class="emotion-2"
      role="checkbox"
    />
    <div>
      I am default text
    </div>
  </label>
</DocumentFragment>
`;

exports[`<Checkbox/> snapshots renders isCrossBrand state correctly OldNavy 1`] = `
<DocumentFragment>
  .emotion-0 {
  margin: 0 0 1.5rem;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  max-width: calc(100% - 2.3025rem);
  line-height: 1;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  cursor: pointer;
  color: #000;
  font-size: 1.1rem;
}

.emotion-0:hover {
  -webkit-text-decoration: underline;
  text-decoration: underline;
}

.emotion-1 {
  opacity: 0;
  position: absolute;
}

.emotion-1[data-focus-visible-added]+div {
  outline: 0;
  box-shadow: 0 0 0 0.125rem #5CABF7;
}

.emotion-2 {
  box-sizing: content-box;
  position: relative;
  display: block;
  margin-right: 0.625rem;
  min-width: 1.375rem;
  min-height: 1.375rem;
  border: 0.0625rem solid #666;
}

.emotion-2::before {
  content: "";
  display: block;
  position: absolute;
  width: calc(100% + 0.0625rem);
  height: calc(100% + 0.0625rem);
  -webkit-transform: scale(0);
  -moz-transform: scale(0);
  -ms-transform: scale(0);
  transform: scale(0);
  background-color: #0466CA;
}

.emotion-2::after {
  content: "";
  display: block;
  width: 100%;
  height: 100%;
  position: absolute;
  background-repeat: no-repeat;
  background-position-x: 0;
  background-position-y: 0.125rem;
  -webkit-background-size: 1.875rem;
  background-size: 1.875rem;
  -webkit-background-clip: border-box;
  background-clip: border-box;
  background-color: transparent;
}

<label
    class="emotion-0"
    id="checkbox-id"
  >
    <input
      class="emotion-1"
      type="checkbox"
    />
    <div
      aria-checked="false"
      aria-hidden="true"
      aria-labelledby="checkbox-id"
      class="emotion-2"
      role="checkbox"
    />
    <div>
      I am default text
    </div>
  </label>
</DocumentFragment>
`;

exports[`<Checkbox/> snapshots renders isCrossBrand state correctly OldNavy in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  margin: 0 0 1.5rem;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  max-width: calc(100% - 2.3025rem);
  line-height: 1;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  cursor: pointer;
  color: #000;
  font-size: 1.1rem;
}

.emotion-0:hover {
  -webkit-text-decoration: underline;
  text-decoration: underline;
}

.emotion-1 {
  opacity: 0;
  position: absolute;
}

.emotion-1[data-focus-visible-added]+div {
  outline: 0;
  box-shadow: 0 0 0 0.125rem #5CABF7;
}

.emotion-2 {
  box-sizing: content-box;
  position: relative;
  display: block;
  margin-right: 0.625rem;
  min-width: 1.375rem;
  min-height: 1.375rem;
  border: 0.0625rem solid #666;
}

.emotion-2::before {
  content: "";
  display: block;
  position: absolute;
  width: calc(100% + 0.0625rem);
  height: calc(100% + 0.0625rem);
  -webkit-transform: scale(0);
  -moz-transform: scale(0);
  -ms-transform: scale(0);
  transform: scale(0);
  background-color: #0466CA;
}

.emotion-2::after {
  content: "";
  display: block;
  width: 100%;
  height: 100%;
  position: absolute;
  background-repeat: no-repeat;
  background-position-x: 0;
  background-position-y: 0.125rem;
  -webkit-background-size: 1.875rem;
  background-size: 1.875rem;
  -webkit-background-clip: border-box;
  background-clip: border-box;
  background-color: transparent;
}

<label
    class="emotion-0"
    id="checkbox-id"
  >
    <input
      class="emotion-1"
      type="checkbox"
    />
    <div
      aria-checked="false"
      aria-hidden="true"
      aria-labelledby="checkbox-id"
      class="emotion-2"
      role="checkbox"
    />
    <div>
      I am default text
    </div>
  </label>
</DocumentFragment>
`;
