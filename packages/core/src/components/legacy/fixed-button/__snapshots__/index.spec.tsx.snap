// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`<FixedButton /> snapshots renders crossBrand state correctly Athleta 1`] = `
<DocumentFragment>
  .emotion-0 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 0;
  text-align: center;
  display: block;
  width: 100%;
  padding: 0.5em 0.8em;
  font-size: 1rem;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 700;
  color: #FFF;
  background-color: #000;
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  box-sizing: border-box;
  cursor: pointer;
  text-align: center;
  text-transform: uppercase;
  -webkit-transition: all 200ms ease;
  transition: all 200ms ease;
  height: 2.75rem;
  background-color: #F43D00;
  border: 2px solid transparent;
  color: #FFF;
  font-size: 1rem;
  font-weight: 600;
  letter-spacing: normal;
  line-height: 1.5;
  padding: 0.5rem;
}

.emotion-0:focus {
  outline: none;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  cursor: default;
}

.emotion-0:hover {
  background-color: #f66d3f;
  border-color: transparent;
  color: #FFF;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  background-color: #CCC;
  border-color: transparent;
  color: #FFF;
}

<button
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`<FixedButton /> snapshots renders crossBrand state correctly Athleta in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 0;
  text-align: center;
  display: block;
  width: 100%;
  padding: 0.5em 0.8em;
  font-size: 1rem;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 700;
  color: #FFF;
  background-color: #000;
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  box-sizing: border-box;
  cursor: pointer;
  text-align: center;
  text-transform: uppercase;
  -webkit-transition: all 200ms ease;
  transition: all 200ms ease;
  height: 2.75rem;
  background-color: #F43D00;
  border: 2px solid transparent;
  color: #FFF;
  font-size: 1rem;
  font-weight: 600;
  letter-spacing: normal;
  line-height: 1.5;
  padding: 0.5rem;
}

.emotion-0:focus {
  outline: none;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  cursor: default;
}

.emotion-0:hover {
  background-color: #f66d3f;
  border-color: transparent;
  color: #FFF;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  background-color: #CCC;
  border-color: transparent;
  color: #FFF;
}

<button
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`<FixedButton /> snapshots renders crossBrand state correctly BananaRepublic 1`] = `
<DocumentFragment>
  .emotion-0 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 0;
  text-align: center;
  display: block;
  width: 100%;
  padding: 0.5em 0.8em;
  font-size: 1rem;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 700;
  color: #FFF;
  background-color: #000;
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  box-sizing: border-box;
  cursor: pointer;
  text-align: center;
  text-transform: uppercase;
  -webkit-transition: all 200ms ease;
  transition: all 200ms ease;
  height: 2.75rem;
  background-color: #F43D00;
  border: 2px solid transparent;
  color: #FFF;
  font-size: 1rem;
  font-weight: 600;
  letter-spacing: normal;
  line-height: 1.5;
  padding: 0.5rem;
}

.emotion-0:focus {
  outline: none;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  cursor: default;
}

.emotion-0:hover {
  background-color: #f66d3f;
  border-color: transparent;
  color: #FFF;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  background-color: #CCC;
  border-color: transparent;
  color: #FFF;
}

<button
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`<FixedButton /> snapshots renders crossBrand state correctly BananaRepublic in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 0;
  text-align: center;
  display: block;
  width: 100%;
  padding: 0.5em 0.8em;
  font-size: 1rem;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 700;
  color: #FFF;
  background-color: #000;
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  box-sizing: border-box;
  cursor: pointer;
  text-align: center;
  text-transform: uppercase;
  -webkit-transition: all 200ms ease;
  transition: all 200ms ease;
  height: 2.75rem;
  background-color: #F43D00;
  border: 2px solid transparent;
  color: #FFF;
  font-size: 1rem;
  font-weight: 600;
  letter-spacing: normal;
  line-height: 1.5;
  padding: 0.5rem;
}

.emotion-0:focus {
  outline: none;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  cursor: default;
}

.emotion-0:hover {
  background-color: #f66d3f;
  border-color: transparent;
  color: #FFF;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  background-color: #CCC;
  border-color: transparent;
  color: #FFF;
}

<button
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`<FixedButton /> snapshots renders crossBrand state correctly BananaRepublicFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 0;
  text-align: center;
  display: block;
  width: 100%;
  padding: 0.5em 0.8em;
  font-size: 1rem;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 700;
  color: #FFF;
  background-color: #000;
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  box-sizing: border-box;
  cursor: pointer;
  text-align: center;
  text-transform: uppercase;
  -webkit-transition: all 200ms ease;
  transition: all 200ms ease;
  height: 2.75rem;
  background-color: #F43D00;
  border: 2px solid transparent;
  color: #FFF;
  font-size: 1rem;
  font-weight: 600;
  letter-spacing: normal;
  line-height: 1.5;
  padding: 0.5rem;
}

.emotion-0:focus {
  outline: none;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  cursor: default;
}

.emotion-0:hover {
  background-color: #f66d3f;
  border-color: transparent;
  color: #FFF;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  background-color: #CCC;
  border-color: transparent;
  color: #FFF;
}

<button
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`<FixedButton /> snapshots renders crossBrand state correctly BananaRepublicFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 0;
  text-align: center;
  display: block;
  width: 100%;
  padding: 0.5em 0.8em;
  font-size: 1rem;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 700;
  color: #FFF;
  background-color: #000;
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  box-sizing: border-box;
  cursor: pointer;
  text-align: center;
  text-transform: uppercase;
  -webkit-transition: all 200ms ease;
  transition: all 200ms ease;
  height: 2.75rem;
  background-color: #F43D00;
  border: 2px solid transparent;
  color: #FFF;
  font-size: 1rem;
  font-weight: 600;
  letter-spacing: normal;
  line-height: 1.5;
  padding: 0.5rem;
}

.emotion-0:focus {
  outline: none;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  cursor: default;
}

.emotion-0:hover {
  background-color: #f66d3f;
  border-color: transparent;
  color: #FFF;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  background-color: #CCC;
  border-color: transparent;
  color: #FFF;
}

<button
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`<FixedButton /> snapshots renders crossBrand state correctly Gap 1`] = `
<DocumentFragment>
  .emotion-0 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 0;
  text-align: center;
  display: block;
  width: 100%;
  padding: 0.5em 0.8em;
  font-size: 1rem;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 700;
  color: #FFF;
  background-color: #000;
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  box-sizing: border-box;
  cursor: pointer;
  text-align: center;
  text-transform: uppercase;
  -webkit-transition: all 200ms ease;
  transition: all 200ms ease;
  height: 2.75rem;
  background-color: #F43D00;
  border: 2px solid transparent;
  color: #FFF;
  font-size: 1rem;
  font-weight: 600;
  letter-spacing: normal;
  line-height: 1.5;
  padding: 0.5rem;
}

.emotion-0:focus {
  outline: none;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  cursor: default;
}

.emotion-0:hover {
  background-color: #f66d3f;
  border-color: transparent;
  color: #FFF;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  background-color: #CCC;
  border-color: transparent;
  color: #FFF;
}

<button
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`<FixedButton /> snapshots renders crossBrand state correctly Gap in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 0;
  text-align: center;
  display: block;
  width: 100%;
  padding: 0.5em 0.8em;
  font-size: 1rem;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 700;
  color: #FFF;
  background-color: #000;
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  box-sizing: border-box;
  cursor: pointer;
  text-align: center;
  text-transform: uppercase;
  -webkit-transition: all 200ms ease;
  transition: all 200ms ease;
  height: 2.75rem;
  background-color: #F43D00;
  border: 2px solid transparent;
  color: #FFF;
  font-size: 1rem;
  font-weight: 600;
  letter-spacing: normal;
  line-height: 1.5;
  padding: 0.5rem;
}

.emotion-0:focus {
  outline: none;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  cursor: default;
}

.emotion-0:hover {
  background-color: #f66d3f;
  border-color: transparent;
  color: #FFF;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  background-color: #CCC;
  border-color: transparent;
  color: #FFF;
}

<button
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`<FixedButton /> snapshots renders crossBrand state correctly GapFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 0;
  text-align: center;
  display: block;
  width: 100%;
  padding: 0.5em 0.8em;
  font-size: 1rem;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 700;
  color: #FFF;
  background-color: #000;
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  box-sizing: border-box;
  cursor: pointer;
  text-align: center;
  text-transform: uppercase;
  -webkit-transition: all 200ms ease;
  transition: all 200ms ease;
  height: 2.75rem;
  background-color: #F43D00;
  border: 2px solid transparent;
  color: #FFF;
  font-size: 1rem;
  font-weight: 600;
  letter-spacing: normal;
  line-height: 1.5;
  padding: 0.5rem;
}

.emotion-0:focus {
  outline: none;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  cursor: default;
}

.emotion-0:hover {
  background-color: #f66d3f;
  border-color: transparent;
  color: #FFF;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  background-color: #CCC;
  border-color: transparent;
  color: #FFF;
}

<button
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`<FixedButton /> snapshots renders crossBrand state correctly GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 0;
  text-align: center;
  display: block;
  width: 100%;
  padding: 0.5em 0.8em;
  font-size: 1rem;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 700;
  color: #FFF;
  background-color: #000;
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  box-sizing: border-box;
  cursor: pointer;
  text-align: center;
  text-transform: uppercase;
  -webkit-transition: all 200ms ease;
  transition: all 200ms ease;
  height: 2.75rem;
  background-color: #F43D00;
  border: 2px solid transparent;
  color: #FFF;
  font-size: 1rem;
  font-weight: 600;
  letter-spacing: normal;
  line-height: 1.5;
  padding: 0.5rem;
}

.emotion-0:focus {
  outline: none;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  cursor: default;
}

.emotion-0:hover {
  background-color: #f66d3f;
  border-color: transparent;
  color: #FFF;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  background-color: #CCC;
  border-color: transparent;
  color: #FFF;
}

<button
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`<FixedButton /> snapshots renders crossBrand state correctly OldNavy 1`] = `
<DocumentFragment>
  .emotion-0 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 0;
  text-align: center;
  display: block;
  width: 100%;
  padding: 0.5em 0.8em;
  font-size: 1rem;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 700;
  color: #FFF;
  background-color: #000;
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  box-sizing: border-box;
  cursor: pointer;
  text-align: center;
  text-transform: uppercase;
  -webkit-transition: all 200ms ease;
  transition: all 200ms ease;
  height: 2.75rem;
  background-color: #F43D00;
  border: 2px solid transparent;
  color: #FFF;
  font-size: 1rem;
  font-weight: 600;
  letter-spacing: normal;
  line-height: 1.5;
  padding: 0.5rem;
}

.emotion-0:focus {
  outline: none;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  cursor: default;
}

.emotion-0:hover {
  background-color: #f66d3f;
  border-color: transparent;
  color: #FFF;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  background-color: #CCC;
  border-color: transparent;
  color: #FFF;
}

<button
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`<FixedButton /> snapshots renders crossBrand state correctly OldNavy in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 0;
  text-align: center;
  display: block;
  width: 100%;
  padding: 0.5em 0.8em;
  font-size: 1rem;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 700;
  color: #FFF;
  background-color: #000;
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  box-sizing: border-box;
  cursor: pointer;
  text-align: center;
  text-transform: uppercase;
  -webkit-transition: all 200ms ease;
  transition: all 200ms ease;
  height: 2.75rem;
  background-color: #F43D00;
  border: 2px solid transparent;
  color: #FFF;
  font-size: 1rem;
  font-weight: 600;
  letter-spacing: normal;
  line-height: 1.5;
  padding: 0.5rem;
}

.emotion-0:focus {
  outline: none;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  cursor: default;
}

.emotion-0:hover {
  background-color: #f66d3f;
  border-color: transparent;
  color: #FFF;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  background-color: #CCC;
  border-color: transparent;
  color: #FFF;
}

<button
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`<FixedButton /> snapshots renders default state correctly Athleta 1`] = `
<DocumentFragment>
  .emotion-0 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 1.5px solid transparent;
  border-radius: 0;
  font-size: 16px;
  font-weight: 500;
  letter-spacing: 0px;
  min-height: auto;
  max-height: 60px;
  line-height: 1.25;
  padding: 22px 40px;
  width: 100%;
  font-family: 'Avenir Next','Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #FFF;
  background-color: #000;
  border-color: #000;
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  box-sizing: border-box;
  cursor: pointer;
  text-align: center;
  text-transform: uppercase;
  -webkit-transition: all 200ms ease;
  transition: all 200ms ease;
  height: 2.75rem;
  background-color: #333;
  border: none;
  color: #FFF;
  font-size: 1.1875rem;
  font-weight: 600;
  letter-spacing: 1px;
  line-height: normal;
  padding: 0.0625em 0.52em 0.125em;
}

.emotion-0:focus {
  outline: none;
}

.emotion-0>span {
  padding: 1px 0;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  cursor: default;
}

.emotion-0:hover {
  background-color: #666;
  border-color: transparent;
  color: #FFF;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  background-color: #CCC;
  border-color: transparent;
  color: #FFF;
}

<button
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`<FixedButton /> snapshots renders default state correctly Athleta in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 0;
  text-align: center;
  display: block;
  width: 100%;
  padding: 0.5em 0.8em;
  font-size: 1rem;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 700;
  color: #FFF;
  background-color: #000;
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  box-sizing: border-box;
  cursor: pointer;
  text-align: center;
  text-transform: uppercase;
  -webkit-transition: all 200ms ease;
  transition: all 200ms ease;
  height: 2.75rem;
  background-color: #F43D00;
  border: 2px solid transparent;
  color: #FFF;
  font-size: 1rem;
  font-weight: 600;
  letter-spacing: normal;
  line-height: 1.5;
  padding: 0.5rem;
}

.emotion-0:focus {
  outline: none;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  cursor: default;
}

.emotion-0:hover {
  background-color: #f66d3f;
  border-color: transparent;
  color: #FFF;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  background-color: #CCC;
  border-color: transparent;
  color: #FFF;
}

<button
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`<FixedButton /> snapshots renders default state correctly BananaRepublic 1`] = `
<DocumentFragment>
  .emotion-0 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 0;
  text-align: center;
  display: block;
  width: 100%;
  padding: 0.5em 0.8em;
  font-size: 1rem;
  font-family: 'Lynstone',Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 600;
  color: #FFF;
  background-color: #000;
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  box-sizing: border-box;
  cursor: pointer;
  text-align: center;
  text-transform: uppercase;
  -webkit-transition: all 200ms ease;
  transition: all 200ms ease;
  height: 2.75rem;
  background-color: #000;
  border: none;
  color: #FFF;
  font-size: 1.0625rem;
  font-weight: 600;
  letter-spacing: 2px;
  line-height: normal;
  padding: 0.53em;
}

.emotion-0:focus {
  outline: none;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  cursor: default;
}

.emotion-0:hover {
  background-color: #3f3f3f;
  border-color: transparent;
  color: #FFF;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  background-color: #CCC;
  border-color: transparent;
  color: #FFF;
}

<button
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`<FixedButton /> snapshots renders default state correctly BananaRepublic in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 0;
  text-align: center;
  display: block;
  width: 100%;
  padding: 0.5em 0.8em;
  font-size: 1rem;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 700;
  color: #FFF;
  background-color: #000;
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  box-sizing: border-box;
  cursor: pointer;
  text-align: center;
  text-transform: uppercase;
  -webkit-transition: all 200ms ease;
  transition: all 200ms ease;
  height: 2.75rem;
  background-color: #F43D00;
  border: 2px solid transparent;
  color: #FFF;
  font-size: 1rem;
  font-weight: 600;
  letter-spacing: normal;
  line-height: 1.5;
  padding: 0.5rem;
}

.emotion-0:focus {
  outline: none;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  cursor: default;
}

.emotion-0:hover {
  background-color: #f66d3f;
  border-color: transparent;
  color: #FFF;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  background-color: #CCC;
  border-color: transparent;
  color: #FFF;
}

<button
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`<FixedButton /> snapshots renders default state correctly BananaRepublicFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 0;
  text-align: center;
  display: block;
  width: 100%;
  padding: 0.5em 0.8em;
  font-size: 1rem;
  font-family: 'Lynstone',Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 700;
  color: #FFF;
  background-color: #000;
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  box-sizing: border-box;
  cursor: pointer;
  text-align: center;
  text-transform: uppercase;
  -webkit-transition: all 200ms ease;
  transition: all 200ms ease;
  height: 2.75rem;
  background-color: #000;
  border: none;
  color: #FFF;
  font-size: 1.0625rem;
  font-weight: 600;
  letter-spacing: 2px;
  line-height: normal;
  padding: 0.53em;
}

.emotion-0:focus {
  outline: none;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  cursor: default;
}

.emotion-0:hover {
  background-color: #3f3f3f;
  border-color: transparent;
  color: #FFF;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  background-color: #CCC;
  border-color: transparent;
  color: #FFF;
}

<button
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`<FixedButton /> snapshots renders default state correctly BananaRepublicFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 0;
  text-align: center;
  display: block;
  width: 100%;
  padding: 0.5em 0.8em;
  font-size: 1rem;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 700;
  color: #FFF;
  background-color: #000;
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  box-sizing: border-box;
  cursor: pointer;
  text-align: center;
  text-transform: uppercase;
  -webkit-transition: all 200ms ease;
  transition: all 200ms ease;
  height: 2.75rem;
  background-color: #F43D00;
  border: 2px solid transparent;
  color: #FFF;
  font-size: 1rem;
  font-weight: 600;
  letter-spacing: normal;
  line-height: 1.5;
  padding: 0.5rem;
}

.emotion-0:focus {
  outline: none;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  cursor: default;
}

.emotion-0:hover {
  background-color: #f66d3f;
  border-color: transparent;
  color: #FFF;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  background-color: #CCC;
  border-color: transparent;
  color: #FFF;
}

<button
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`<FixedButton /> snapshots renders default state correctly Gap 1`] = `
<DocumentFragment>
  .emotion-0 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 1px solid transparent;
  border-radius: 0;
  font-size: 16px;
  font-weight: 400;
  letter-spacing: normal;
  min-height: 45px;
  max-height: auto;
  line-height: 1.125;
  padding: 12px 20px 11px;
  width: 100%;
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #FFF;
  background-color: #002554;
  border-color: #002554;
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  box-sizing: border-box;
  cursor: pointer;
  text-align: center;
  text-transform: uppercase;
  -webkit-transition: all 200ms ease;
  transition: all 200ms ease;
  height: 2.75rem;
  background-color: #002554;
  border: 2px solid #002554;
  color: #FFF;
  font-size: 1.0625rem;
  font-weight: 700;
  letter-spacing: 1px;
  line-height: normal;
  padding: 0.0625em 0.59em 0.125em;
}

.emotion-0:focus {
  outline: none;
}

.emotion-0>span {
  padding: 1px 0;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  cursor: default;
}

.emotion-0:hover {
  background-color: #3f5b7e;
  border-color: #3f5b7e;
  color: #FFF;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  background-color: #CCC;
  border-color: transparent;
  color: #FFF;
}

<button
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`<FixedButton /> snapshots renders default state correctly Gap in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 0;
  text-align: center;
  display: block;
  width: 100%;
  padding: 0.5em 0.8em;
  font-size: 1rem;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 700;
  color: #FFF;
  background-color: #000;
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  box-sizing: border-box;
  cursor: pointer;
  text-align: center;
  text-transform: uppercase;
  -webkit-transition: all 200ms ease;
  transition: all 200ms ease;
  height: 2.75rem;
  background-color: #F43D00;
  border: 2px solid transparent;
  color: #FFF;
  font-size: 1rem;
  font-weight: 600;
  letter-spacing: normal;
  line-height: 1.5;
  padding: 0.5rem;
}

.emotion-0:focus {
  outline: none;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  cursor: default;
}

.emotion-0:hover {
  background-color: #f66d3f;
  border-color: transparent;
  color: #FFF;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  background-color: #CCC;
  border-color: transparent;
  color: #FFF;
}

<button
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`<FixedButton /> snapshots renders default state correctly GapFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 1px solid transparent;
  border-radius: 0;
  font-size: 16px;
  font-weight: 400;
  letter-spacing: normal;
  min-height: 45px;
  max-height: auto;
  line-height: 1.125;
  padding: 12px 20px 11px;
  width: 100%;
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #FFF;
  background-color: #002554;
  border-color: #002554;
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  box-sizing: border-box;
  cursor: pointer;
  text-align: center;
  text-transform: uppercase;
  -webkit-transition: all 200ms ease;
  transition: all 200ms ease;
  height: 2.75rem;
  background-color: #002554;
  border: 2px solid #002554;
  color: #FFF;
  font-size: 1.0625rem;
  font-weight: 700;
  letter-spacing: 1px;
  line-height: normal;
  padding: 0.0625em 0.59em 0.125em;
}

.emotion-0:focus {
  outline: none;
}

.emotion-0>span {
  padding: 1px 0;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  cursor: default;
}

.emotion-0:hover {
  background-color: #3f5b7e;
  border-color: #3f5b7e;
  color: #FFF;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  background-color: #CCC;
  border-color: transparent;
  color: #FFF;
}

<button
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`<FixedButton /> snapshots renders default state correctly GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 0;
  text-align: center;
  display: block;
  width: 100%;
  padding: 0.5em 0.8em;
  font-size: 1rem;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 700;
  color: #FFF;
  background-color: #000;
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  box-sizing: border-box;
  cursor: pointer;
  text-align: center;
  text-transform: uppercase;
  -webkit-transition: all 200ms ease;
  transition: all 200ms ease;
  height: 2.75rem;
  background-color: #F43D00;
  border: 2px solid transparent;
  color: #FFF;
  font-size: 1rem;
  font-weight: 600;
  letter-spacing: normal;
  line-height: 1.5;
  padding: 0.5rem;
}

.emotion-0:focus {
  outline: none;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  cursor: default;
}

.emotion-0:hover {
  background-color: #f66d3f;
  border-color: transparent;
  color: #FFF;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  background-color: #CCC;
  border-color: transparent;
  color: #FFF;
}

<button
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`<FixedButton /> snapshots renders default state correctly OldNavy 1`] = `
<DocumentFragment>
  .emotion-0 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 700;
  letter-spacing: 1.6800000000000002px;
  min-height: 44px;
  max-height: auto;
  line-height: 1.1428571428571428;
  padding: 12px 22px;
  width: 100%;
  font-family: 'Gotham','Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #FFF;
  background-color: #003764;
  border-color: #003764;
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  box-sizing: border-box;
  cursor: pointer;
  text-align: center;
  text-transform: uppercase;
  -webkit-transition: all 200ms ease;
  transition: all 200ms ease;
  height: 2.75rem;
  background-color: #003764;
  border: none;
  color: #FFF;
  font-size: 1.1875rem;
  font-weight: 700;
  letter-spacing: 1px;
  line-height: normal;
  padding: 0.45em;
}

.emotion-0:focus {
  outline: none;
}

.emotion-0>span {
  padding: 1px 0;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  cursor: default;
}

.emotion-0:hover {
  background-color: #3f698a;
  border-color: transparent;
  color: #FFF;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  background-color: #CCC;
  border-color: transparent;
  color: #FFF;
}

<button
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`<FixedButton /> snapshots renders default state correctly OldNavy in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 0;
  text-align: center;
  display: block;
  width: 100%;
  padding: 0.5em 0.8em;
  font-size: 1rem;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 700;
  color: #FFF;
  background-color: #000;
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  box-sizing: border-box;
  cursor: pointer;
  text-align: center;
  text-transform: uppercase;
  -webkit-transition: all 200ms ease;
  transition: all 200ms ease;
  height: 2.75rem;
  background-color: #F43D00;
  border: 2px solid transparent;
  color: #FFF;
  font-size: 1rem;
  font-weight: 600;
  letter-spacing: normal;
  line-height: 1.5;
  padding: 0.5rem;
}

.emotion-0:focus {
  outline: none;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  cursor: default;
}

.emotion-0:hover {
  background-color: #f66d3f;
  border-color: transparent;
  color: #FFF;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  background-color: #CCC;
  border-color: transparent;
  color: #FFF;
}

<button
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`<FixedButton /> snapshots renders disabled primary state correctly Athleta 1`] = `
<DocumentFragment>
  .emotion-0 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 1.5px solid transparent;
  border-radius: 0;
  font-size: 16px;
  font-weight: 500;
  letter-spacing: 0px;
  min-height: auto;
  max-height: 60px;
  line-height: 1.25;
  padding: 22px 40px;
  width: 100%;
  font-family: 'Avenir Next','Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #FFF;
  background-color: #000;
  border-color: #000;
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  box-sizing: border-box;
  cursor: pointer;
  text-align: center;
  text-transform: uppercase;
  -webkit-transition: all 200ms ease;
  transition: all 200ms ease;
  height: 2.75rem;
  background-color: #333;
  border: none;
  color: #FFF;
  font-size: 1.1875rem;
  font-weight: 600;
  letter-spacing: 1px;
  line-height: normal;
  padding: 0.0625em 0.52em 0.125em;
}

.emotion-0:focus {
  outline: none;
}

.emotion-0>span {
  padding: 1px 0;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  cursor: default;
}

.emotion-0:hover {
  background-color: #666;
  border-color: transparent;
  color: #FFF;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  background-color: #CCC;
  border-color: transparent;
  color: #FFF;
}

<button
    class="emotion-0"
    disabled=""
  />
</DocumentFragment>
`;

exports[`<FixedButton /> snapshots renders disabled primary state correctly Athleta in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 0;
  text-align: center;
  display: block;
  width: 100%;
  padding: 0.5em 0.8em;
  font-size: 1rem;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 700;
  color: #FFF;
  background-color: #000;
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  box-sizing: border-box;
  cursor: pointer;
  text-align: center;
  text-transform: uppercase;
  -webkit-transition: all 200ms ease;
  transition: all 200ms ease;
  height: 2.75rem;
  background-color: #F43D00;
  border: 2px solid transparent;
  color: #FFF;
  font-size: 1rem;
  font-weight: 600;
  letter-spacing: normal;
  line-height: 1.5;
  padding: 0.5rem;
}

.emotion-0:focus {
  outline: none;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  cursor: default;
}

.emotion-0:hover {
  background-color: #f66d3f;
  border-color: transparent;
  color: #FFF;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  background-color: #CCC;
  border-color: transparent;
  color: #FFF;
}

<button
    class="emotion-0"
    disabled=""
  />
</DocumentFragment>
`;

exports[`<FixedButton /> snapshots renders disabled primary state correctly BananaRepublic 1`] = `
<DocumentFragment>
  .emotion-0 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 0;
  text-align: center;
  display: block;
  width: 100%;
  padding: 0.5em 0.8em;
  font-size: 1rem;
  font-family: 'Lynstone',Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 600;
  color: #FFF;
  background-color: #000;
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  box-sizing: border-box;
  cursor: pointer;
  text-align: center;
  text-transform: uppercase;
  -webkit-transition: all 200ms ease;
  transition: all 200ms ease;
  height: 2.75rem;
  background-color: #000;
  border: none;
  color: #FFF;
  font-size: 1.0625rem;
  font-weight: 600;
  letter-spacing: 2px;
  line-height: normal;
  padding: 0.53em;
}

.emotion-0:focus {
  outline: none;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  cursor: default;
}

.emotion-0:hover {
  background-color: #3f3f3f;
  border-color: transparent;
  color: #FFF;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  background-color: #CCC;
  border-color: transparent;
  color: #FFF;
}

<button
    class="emotion-0"
    disabled=""
  />
</DocumentFragment>
`;

exports[`<FixedButton /> snapshots renders disabled primary state correctly BananaRepublic in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 0;
  text-align: center;
  display: block;
  width: 100%;
  padding: 0.5em 0.8em;
  font-size: 1rem;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 700;
  color: #FFF;
  background-color: #000;
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  box-sizing: border-box;
  cursor: pointer;
  text-align: center;
  text-transform: uppercase;
  -webkit-transition: all 200ms ease;
  transition: all 200ms ease;
  height: 2.75rem;
  background-color: #F43D00;
  border: 2px solid transparent;
  color: #FFF;
  font-size: 1rem;
  font-weight: 600;
  letter-spacing: normal;
  line-height: 1.5;
  padding: 0.5rem;
}

.emotion-0:focus {
  outline: none;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  cursor: default;
}

.emotion-0:hover {
  background-color: #f66d3f;
  border-color: transparent;
  color: #FFF;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  background-color: #CCC;
  border-color: transparent;
  color: #FFF;
}

<button
    class="emotion-0"
    disabled=""
  />
</DocumentFragment>
`;

exports[`<FixedButton /> snapshots renders disabled primary state correctly BananaRepublicFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 0;
  text-align: center;
  display: block;
  width: 100%;
  padding: 0.5em 0.8em;
  font-size: 1rem;
  font-family: 'Lynstone',Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 700;
  color: #FFF;
  background-color: #000;
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  box-sizing: border-box;
  cursor: pointer;
  text-align: center;
  text-transform: uppercase;
  -webkit-transition: all 200ms ease;
  transition: all 200ms ease;
  height: 2.75rem;
  background-color: #000;
  border: none;
  color: #FFF;
  font-size: 1.0625rem;
  font-weight: 600;
  letter-spacing: 2px;
  line-height: normal;
  padding: 0.53em;
}

.emotion-0:focus {
  outline: none;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  cursor: default;
}

.emotion-0:hover {
  background-color: #3f3f3f;
  border-color: transparent;
  color: #FFF;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  background-color: #CCC;
  border-color: transparent;
  color: #FFF;
}

<button
    class="emotion-0"
    disabled=""
  />
</DocumentFragment>
`;

exports[`<FixedButton /> snapshots renders disabled primary state correctly BananaRepublicFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 0;
  text-align: center;
  display: block;
  width: 100%;
  padding: 0.5em 0.8em;
  font-size: 1rem;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 700;
  color: #FFF;
  background-color: #000;
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  box-sizing: border-box;
  cursor: pointer;
  text-align: center;
  text-transform: uppercase;
  -webkit-transition: all 200ms ease;
  transition: all 200ms ease;
  height: 2.75rem;
  background-color: #F43D00;
  border: 2px solid transparent;
  color: #FFF;
  font-size: 1rem;
  font-weight: 600;
  letter-spacing: normal;
  line-height: 1.5;
  padding: 0.5rem;
}

.emotion-0:focus {
  outline: none;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  cursor: default;
}

.emotion-0:hover {
  background-color: #f66d3f;
  border-color: transparent;
  color: #FFF;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  background-color: #CCC;
  border-color: transparent;
  color: #FFF;
}

<button
    class="emotion-0"
    disabled=""
  />
</DocumentFragment>
`;

exports[`<FixedButton /> snapshots renders disabled primary state correctly Gap 1`] = `
<DocumentFragment>
  .emotion-0 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 1px solid transparent;
  border-radius: 0;
  font-size: 16px;
  font-weight: 400;
  letter-spacing: normal;
  min-height: 45px;
  max-height: auto;
  line-height: 1.125;
  padding: 12px 20px 11px;
  width: 100%;
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #FFF;
  background-color: #002554;
  border-color: #002554;
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  box-sizing: border-box;
  cursor: pointer;
  text-align: center;
  text-transform: uppercase;
  -webkit-transition: all 200ms ease;
  transition: all 200ms ease;
  height: 2.75rem;
  background-color: #002554;
  border: 2px solid #002554;
  color: #FFF;
  font-size: 1.0625rem;
  font-weight: 700;
  letter-spacing: 1px;
  line-height: normal;
  padding: 0.0625em 0.59em 0.125em;
}

.emotion-0:focus {
  outline: none;
}

.emotion-0>span {
  padding: 1px 0;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  cursor: default;
}

.emotion-0:hover {
  background-color: #3f5b7e;
  border-color: #3f5b7e;
  color: #FFF;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  background-color: #CCC;
  border-color: transparent;
  color: #FFF;
}

<button
    class="emotion-0"
    disabled=""
  />
</DocumentFragment>
`;

exports[`<FixedButton /> snapshots renders disabled primary state correctly Gap in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 0;
  text-align: center;
  display: block;
  width: 100%;
  padding: 0.5em 0.8em;
  font-size: 1rem;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 700;
  color: #FFF;
  background-color: #000;
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  box-sizing: border-box;
  cursor: pointer;
  text-align: center;
  text-transform: uppercase;
  -webkit-transition: all 200ms ease;
  transition: all 200ms ease;
  height: 2.75rem;
  background-color: #F43D00;
  border: 2px solid transparent;
  color: #FFF;
  font-size: 1rem;
  font-weight: 600;
  letter-spacing: normal;
  line-height: 1.5;
  padding: 0.5rem;
}

.emotion-0:focus {
  outline: none;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  cursor: default;
}

.emotion-0:hover {
  background-color: #f66d3f;
  border-color: transparent;
  color: #FFF;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  background-color: #CCC;
  border-color: transparent;
  color: #FFF;
}

<button
    class="emotion-0"
    disabled=""
  />
</DocumentFragment>
`;

exports[`<FixedButton /> snapshots renders disabled primary state correctly GapFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 1px solid transparent;
  border-radius: 0;
  font-size: 16px;
  font-weight: 400;
  letter-spacing: normal;
  min-height: 45px;
  max-height: auto;
  line-height: 1.125;
  padding: 12px 20px 11px;
  width: 100%;
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #FFF;
  background-color: #002554;
  border-color: #002554;
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  box-sizing: border-box;
  cursor: pointer;
  text-align: center;
  text-transform: uppercase;
  -webkit-transition: all 200ms ease;
  transition: all 200ms ease;
  height: 2.75rem;
  background-color: #002554;
  border: 2px solid #002554;
  color: #FFF;
  font-size: 1.0625rem;
  font-weight: 700;
  letter-spacing: 1px;
  line-height: normal;
  padding: 0.0625em 0.59em 0.125em;
}

.emotion-0:focus {
  outline: none;
}

.emotion-0>span {
  padding: 1px 0;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  cursor: default;
}

.emotion-0:hover {
  background-color: #3f5b7e;
  border-color: #3f5b7e;
  color: #FFF;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  background-color: #CCC;
  border-color: transparent;
  color: #FFF;
}

<button
    class="emotion-0"
    disabled=""
  />
</DocumentFragment>
`;

exports[`<FixedButton /> snapshots renders disabled primary state correctly GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 0;
  text-align: center;
  display: block;
  width: 100%;
  padding: 0.5em 0.8em;
  font-size: 1rem;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 700;
  color: #FFF;
  background-color: #000;
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  box-sizing: border-box;
  cursor: pointer;
  text-align: center;
  text-transform: uppercase;
  -webkit-transition: all 200ms ease;
  transition: all 200ms ease;
  height: 2.75rem;
  background-color: #F43D00;
  border: 2px solid transparent;
  color: #FFF;
  font-size: 1rem;
  font-weight: 600;
  letter-spacing: normal;
  line-height: 1.5;
  padding: 0.5rem;
}

.emotion-0:focus {
  outline: none;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  cursor: default;
}

.emotion-0:hover {
  background-color: #f66d3f;
  border-color: transparent;
  color: #FFF;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  background-color: #CCC;
  border-color: transparent;
  color: #FFF;
}

<button
    class="emotion-0"
    disabled=""
  />
</DocumentFragment>
`;

exports[`<FixedButton /> snapshots renders disabled primary state correctly OldNavy 1`] = `
<DocumentFragment>
  .emotion-0 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 700;
  letter-spacing: 1.6800000000000002px;
  min-height: 44px;
  max-height: auto;
  line-height: 1.1428571428571428;
  padding: 12px 22px;
  width: 100%;
  font-family: 'Gotham','Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #FFF;
  background-color: #003764;
  border-color: #003764;
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  box-sizing: border-box;
  cursor: pointer;
  text-align: center;
  text-transform: uppercase;
  -webkit-transition: all 200ms ease;
  transition: all 200ms ease;
  height: 2.75rem;
  background-color: #003764;
  border: none;
  color: #FFF;
  font-size: 1.1875rem;
  font-weight: 700;
  letter-spacing: 1px;
  line-height: normal;
  padding: 0.45em;
}

.emotion-0:focus {
  outline: none;
}

.emotion-0>span {
  padding: 1px 0;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  cursor: default;
}

.emotion-0:hover {
  background-color: #3f698a;
  border-color: transparent;
  color: #FFF;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  background-color: #CCC;
  border-color: transparent;
  color: #FFF;
}

<button
    class="emotion-0"
    disabled=""
  />
</DocumentFragment>
`;

exports[`<FixedButton /> snapshots renders disabled primary state correctly OldNavy in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 0;
  text-align: center;
  display: block;
  width: 100%;
  padding: 0.5em 0.8em;
  font-size: 1rem;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 700;
  color: #FFF;
  background-color: #000;
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  box-sizing: border-box;
  cursor: pointer;
  text-align: center;
  text-transform: uppercase;
  -webkit-transition: all 200ms ease;
  transition: all 200ms ease;
  height: 2.75rem;
  background-color: #F43D00;
  border: 2px solid transparent;
  color: #FFF;
  font-size: 1rem;
  font-weight: 600;
  letter-spacing: normal;
  line-height: 1.5;
  padding: 0.5rem;
}

.emotion-0:focus {
  outline: none;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  cursor: default;
}

.emotion-0:hover {
  background-color: #f66d3f;
  border-color: transparent;
  color: #FFF;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  background-color: #CCC;
  border-color: transparent;
  color: #FFF;
}

<button
    class="emotion-0"
    disabled=""
  />
</DocumentFragment>
`;

exports[`<FixedButton /> snapshots renders disabled secondary flat state correctly Athleta 1`] = `
<DocumentFragment>
  .emotion-0 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 1.5px solid transparent;
  border-radius: 0;
  font-size: 16px;
  font-weight: 500;
  letter-spacing: 0px;
  min-height: auto;
  max-height: 60px;
  line-height: 1.25;
  padding: 22px 40px;
  width: 100%;
  font-family: 'Avenir Next','Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #FFF;
  background-color: #000;
  border-color: #000;
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  box-sizing: border-box;
  cursor: pointer;
  text-align: center;
  text-transform: uppercase;
  -webkit-transition: all 200ms ease;
  transition: all 200ms ease;
  height: 2.0625rem;
  background-color: transparent;
  border: none;
  color: #333;
  font-size: 1.1875rem;
  font-weight: 400;
  letter-spacing: 1px;
  line-height: normal;
  padding: 0.52em;
}

.emotion-0:focus {
  outline: none;
}

.emotion-0>span {
  padding: 1px 0;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  cursor: default;
}

.emotion-0:hover {
  background-color: rgba(255, 255, 255, 0.25);
  border-color: transparent;
  color: #666;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  background-color: #FFF;
  border-color: transparent;
  color: #CCC;
}

<button
    class="emotion-0"
    disabled=""
  />
</DocumentFragment>
`;

exports[`<FixedButton /> snapshots renders disabled secondary flat state correctly Athleta in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 0;
  text-align: center;
  display: block;
  width: 100%;
  padding: 0.5em 0.8em;
  font-size: 1rem;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 700;
  color: #FFF;
  background-color: #000;
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  box-sizing: border-box;
  cursor: pointer;
  text-align: center;
  text-transform: uppercase;
  -webkit-transition: all 200ms ease;
  transition: all 200ms ease;
  height: 2.0625rem;
  background-color: transparent;
  border: 2px solid transparent;
  color: #0466CA;
  font-size: 1rem;
  font-weight: 600;
  letter-spacing: normal;
  line-height: 1.5;
  padding: 0.5rem;
}

.emotion-0:focus {
  outline: none;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  cursor: default;
}

.emotion-0:hover {
  background-color: #FFF;
  border-color: transparent;
  color: #428cd7;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  background-color: #FFF;
  border-color: transparent;
  color: #CCC;
}

<button
    class="emotion-0"
    disabled=""
  />
</DocumentFragment>
`;

exports[`<FixedButton /> snapshots renders disabled secondary flat state correctly BananaRepublic 1`] = `
<DocumentFragment>
  .emotion-0 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 0;
  text-align: center;
  display: block;
  width: 100%;
  padding: 0.5em 0.8em;
  font-size: 1rem;
  font-family: 'Lynstone',Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 600;
  color: #FFF;
  background-color: #000;
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  box-sizing: border-box;
  cursor: pointer;
  text-align: center;
  text-transform: uppercase;
  -webkit-transition: all 200ms ease;
  transition: all 200ms ease;
  height: 2.0625rem;
  background-color: transparent;
  border: none;
  color: #000;
  font-size: 1.0625rem;
  font-weight: 600;
  letter-spacing: 2px;
  line-height: normal;
  padding: 0.53em;
}

.emotion-0:focus {
  outline: none;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  cursor: default;
}

.emotion-0:hover {
  background-color: rgba(255, 255, 255, 0.25);
  border-color: transparent;
  color: #3f3f3f;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  background-color: #FFF;
  border-color: transparent;
  color: #CCC;
}

<button
    class="emotion-0"
    disabled=""
  />
</DocumentFragment>
`;

exports[`<FixedButton /> snapshots renders disabled secondary flat state correctly BananaRepublic in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 0;
  text-align: center;
  display: block;
  width: 100%;
  padding: 0.5em 0.8em;
  font-size: 1rem;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 700;
  color: #FFF;
  background-color: #000;
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  box-sizing: border-box;
  cursor: pointer;
  text-align: center;
  text-transform: uppercase;
  -webkit-transition: all 200ms ease;
  transition: all 200ms ease;
  height: 2.0625rem;
  background-color: transparent;
  border: 2px solid transparent;
  color: #0466CA;
  font-size: 1rem;
  font-weight: 600;
  letter-spacing: normal;
  line-height: 1.5;
  padding: 0.5rem;
}

.emotion-0:focus {
  outline: none;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  cursor: default;
}

.emotion-0:hover {
  background-color: #FFF;
  border-color: transparent;
  color: #428cd7;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  background-color: #FFF;
  border-color: transparent;
  color: #CCC;
}

<button
    class="emotion-0"
    disabled=""
  />
</DocumentFragment>
`;

exports[`<FixedButton /> snapshots renders disabled secondary flat state correctly BananaRepublicFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 0;
  text-align: center;
  display: block;
  width: 100%;
  padding: 0.5em 0.8em;
  font-size: 1rem;
  font-family: 'Lynstone',Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 700;
  color: #FFF;
  background-color: #000;
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  box-sizing: border-box;
  cursor: pointer;
  text-align: center;
  text-transform: uppercase;
  -webkit-transition: all 200ms ease;
  transition: all 200ms ease;
  height: 2.0625rem;
  background-color: transparent;
  border: none;
  color: #000;
  font-size: 1.0625rem;
  font-weight: 600;
  letter-spacing: 2px;
  line-height: normal;
  padding: 0.53em;
}

.emotion-0:focus {
  outline: none;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  cursor: default;
}

.emotion-0:hover {
  background-color: rgba(255, 255, 255, 0.25);
  border-color: transparent;
  color: #3f3f3f;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  background-color: #FFF;
  border-color: transparent;
  color: #CCC;
}

<button
    class="emotion-0"
    disabled=""
  />
</DocumentFragment>
`;

exports[`<FixedButton /> snapshots renders disabled secondary flat state correctly BananaRepublicFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 0;
  text-align: center;
  display: block;
  width: 100%;
  padding: 0.5em 0.8em;
  font-size: 1rem;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 700;
  color: #FFF;
  background-color: #000;
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  box-sizing: border-box;
  cursor: pointer;
  text-align: center;
  text-transform: uppercase;
  -webkit-transition: all 200ms ease;
  transition: all 200ms ease;
  height: 2.0625rem;
  background-color: transparent;
  border: 2px solid transparent;
  color: #0466CA;
  font-size: 1rem;
  font-weight: 600;
  letter-spacing: normal;
  line-height: 1.5;
  padding: 0.5rem;
}

.emotion-0:focus {
  outline: none;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  cursor: default;
}

.emotion-0:hover {
  background-color: #FFF;
  border-color: transparent;
  color: #428cd7;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  background-color: #FFF;
  border-color: transparent;
  color: #CCC;
}

<button
    class="emotion-0"
    disabled=""
  />
</DocumentFragment>
`;

exports[`<FixedButton /> snapshots renders disabled secondary flat state correctly Gap 1`] = `
<DocumentFragment>
  .emotion-0 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 1px solid transparent;
  border-radius: 0;
  font-size: 16px;
  font-weight: 400;
  letter-spacing: normal;
  min-height: 45px;
  max-height: auto;
  line-height: 1.125;
  padding: 12px 20px 11px;
  width: 100%;
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #FFF;
  background-color: #002554;
  border-color: #002554;
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  box-sizing: border-box;
  cursor: pointer;
  text-align: center;
  text-transform: uppercase;
  -webkit-transition: all 200ms ease;
  transition: all 200ms ease;
  height: 2.0625rem;
  background-color: transparent;
  border: none;
  color: #0A5694;
  font-size: 1.0625rem;
  font-weight: 700;
  letter-spacing: 1px;
  line-height: normal;
  padding: 0.59em;
}

.emotion-0:focus {
  outline: none;
}

.emotion-0>span {
  padding: 1px 0;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  cursor: default;
}

.emotion-0:hover {
  background-color: rgba(255, 255, 255, 0.25);
  border-color: transparent;
  color: #4780ae;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  background-color: #FFF;
  border-color: transparent;
  color: #CCC;
}

<button
    class="emotion-0"
    disabled=""
  />
</DocumentFragment>
`;

exports[`<FixedButton /> snapshots renders disabled secondary flat state correctly Gap in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 0;
  text-align: center;
  display: block;
  width: 100%;
  padding: 0.5em 0.8em;
  font-size: 1rem;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 700;
  color: #FFF;
  background-color: #000;
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  box-sizing: border-box;
  cursor: pointer;
  text-align: center;
  text-transform: uppercase;
  -webkit-transition: all 200ms ease;
  transition: all 200ms ease;
  height: 2.0625rem;
  background-color: transparent;
  border: 2px solid transparent;
  color: #0466CA;
  font-size: 1rem;
  font-weight: 600;
  letter-spacing: normal;
  line-height: 1.5;
  padding: 0.5rem;
}

.emotion-0:focus {
  outline: none;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  cursor: default;
}

.emotion-0:hover {
  background-color: #FFF;
  border-color: transparent;
  color: #428cd7;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  background-color: #FFF;
  border-color: transparent;
  color: #CCC;
}

<button
    class="emotion-0"
    disabled=""
  />
</DocumentFragment>
`;

exports[`<FixedButton /> snapshots renders disabled secondary flat state correctly GapFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 1px solid transparent;
  border-radius: 0;
  font-size: 16px;
  font-weight: 400;
  letter-spacing: normal;
  min-height: 45px;
  max-height: auto;
  line-height: 1.125;
  padding: 12px 20px 11px;
  width: 100%;
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #FFF;
  background-color: #002554;
  border-color: #002554;
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  box-sizing: border-box;
  cursor: pointer;
  text-align: center;
  text-transform: uppercase;
  -webkit-transition: all 200ms ease;
  transition: all 200ms ease;
  height: 2.0625rem;
  background-color: transparent;
  border: none;
  color: #0A5694;
  font-size: 1.0625rem;
  font-weight: 700;
  letter-spacing: 1px;
  line-height: normal;
  padding: 0.59em;
}

.emotion-0:focus {
  outline: none;
}

.emotion-0>span {
  padding: 1px 0;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  cursor: default;
}

.emotion-0:hover {
  background-color: rgba(255, 255, 255, 0.25);
  border-color: transparent;
  color: #4780ae;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  background-color: #FFF;
  border-color: transparent;
  color: #CCC;
}

<button
    class="emotion-0"
    disabled=""
  />
</DocumentFragment>
`;

exports[`<FixedButton /> snapshots renders disabled secondary flat state correctly GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 0;
  text-align: center;
  display: block;
  width: 100%;
  padding: 0.5em 0.8em;
  font-size: 1rem;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 700;
  color: #FFF;
  background-color: #000;
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  box-sizing: border-box;
  cursor: pointer;
  text-align: center;
  text-transform: uppercase;
  -webkit-transition: all 200ms ease;
  transition: all 200ms ease;
  height: 2.0625rem;
  background-color: transparent;
  border: 2px solid transparent;
  color: #0466CA;
  font-size: 1rem;
  font-weight: 600;
  letter-spacing: normal;
  line-height: 1.5;
  padding: 0.5rem;
}

.emotion-0:focus {
  outline: none;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  cursor: default;
}

.emotion-0:hover {
  background-color: #FFF;
  border-color: transparent;
  color: #428cd7;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  background-color: #FFF;
  border-color: transparent;
  color: #CCC;
}

<button
    class="emotion-0"
    disabled=""
  />
</DocumentFragment>
`;

exports[`<FixedButton /> snapshots renders disabled secondary flat state correctly OldNavy 1`] = `
<DocumentFragment>
  .emotion-0 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 700;
  letter-spacing: 1.6800000000000002px;
  min-height: 44px;
  max-height: auto;
  line-height: 1.1428571428571428;
  padding: 12px 22px;
  width: 100%;
  font-family: 'Gotham','Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #FFF;
  background-color: #003764;
  border-color: #003764;
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  box-sizing: border-box;
  cursor: pointer;
  text-align: center;
  text-transform: uppercase;
  -webkit-transition: all 200ms ease;
  transition: all 200ms ease;
  height: 2.0625rem;
  background-color: transparent;
  border: none;
  color: #003764;
  font-size: 1.1875rem;
  font-weight: 700;
  letter-spacing: 1px;
  line-height: normal;
  padding: 0.45em;
}

.emotion-0:focus {
  outline: none;
}

.emotion-0>span {
  padding: 1px 0;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  cursor: default;
}

.emotion-0:hover {
  background-color: rgba(255, 255, 255, 0.25);
  border-color: transparent;
  color: #3f698a;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  background-color: #FFF;
  border-color: transparent;
  color: #CCC;
}

<button
    class="emotion-0"
    disabled=""
  />
</DocumentFragment>
`;

exports[`<FixedButton /> snapshots renders disabled secondary flat state correctly OldNavy in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 0;
  text-align: center;
  display: block;
  width: 100%;
  padding: 0.5em 0.8em;
  font-size: 1rem;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 700;
  color: #FFF;
  background-color: #000;
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  box-sizing: border-box;
  cursor: pointer;
  text-align: center;
  text-transform: uppercase;
  -webkit-transition: all 200ms ease;
  transition: all 200ms ease;
  height: 2.0625rem;
  background-color: transparent;
  border: 2px solid transparent;
  color: #0466CA;
  font-size: 1rem;
  font-weight: 600;
  letter-spacing: normal;
  line-height: 1.5;
  padding: 0.5rem;
}

.emotion-0:focus {
  outline: none;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  cursor: default;
}

.emotion-0:hover {
  background-color: #FFF;
  border-color: transparent;
  color: #428cd7;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  background-color: #FFF;
  border-color: transparent;
  color: #CCC;
}

<button
    class="emotion-0"
    disabled=""
  />
</DocumentFragment>
`;

exports[`<FixedButton /> snapshots renders disabled secondary outline state correctly Athleta 1`] = `
<DocumentFragment>
  .emotion-0 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 1.5px solid transparent;
  border-radius: 0;
  font-size: 16px;
  font-weight: 500;
  letter-spacing: 0px;
  min-height: auto;
  max-height: 60px;
  line-height: 1.25;
  padding: 22px 40px;
  width: 100%;
  font-family: 'Avenir Next','Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #FFF;
  background-color: #000;
  border-color: #000;
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  box-sizing: border-box;
  cursor: pointer;
  text-align: center;
  text-transform: uppercase;
  -webkit-transition: all 200ms ease;
  transition: all 200ms ease;
  height: 2.75rem;
  background-color: #FFF;
  border: 1px solid #333;
  color: #333;
  font-size: 1.1875rem;
  font-weight: 400;
  letter-spacing: 1px;
  line-height: normal;
  padding: 0.0625em 0.59em 0.125em;
}

.emotion-0:focus {
  outline: none;
}

.emotion-0>span {
  padding: 1px 0;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  cursor: default;
}

.emotion-0:hover {
  background-color: rgba(255, 255, 255, 0.25);
  border-color: #666;
  color: #666;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  background-color: #FFF;
  border-color: #CCC;
  color: #CCC;
}

<button
    class="emotion-0"
    disabled=""
  />
</DocumentFragment>
`;

exports[`<FixedButton /> snapshots renders disabled secondary outline state correctly Athleta in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 0;
  text-align: center;
  display: block;
  width: 100%;
  padding: 0.5em 0.8em;
  font-size: 1rem;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 700;
  color: #FFF;
  background-color: #000;
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  box-sizing: border-box;
  cursor: pointer;
  text-align: center;
  text-transform: uppercase;
  -webkit-transition: all 200ms ease;
  transition: all 200ms ease;
  height: 2.75rem;
  background-color: transparent;
  border: 2px solid #333;
  color: #333;
  font-size: 1rem;
  font-weight: 600;
  letter-spacing: normal;
  line-height: normal;
  padding: 0.5rem;
}

.emotion-0:focus {
  outline: none;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  cursor: default;
}

.emotion-0:hover {
  background-color: #FFF;
  border-color: #666;
  color: #666;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  background-color: #CCC;
  border-color: transparent;
  color: #FFF;
}

<button
    class="emotion-0"
    disabled=""
  />
</DocumentFragment>
`;

exports[`<FixedButton /> snapshots renders disabled secondary outline state correctly BananaRepublic 1`] = `
<DocumentFragment>
  .emotion-0 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 0;
  text-align: center;
  display: block;
  width: 100%;
  padding: 0.5em 0.8em;
  font-size: 1rem;
  font-family: 'Lynstone',Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 600;
  color: #FFF;
  background-color: #000;
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  box-sizing: border-box;
  cursor: pointer;
  text-align: center;
  text-transform: uppercase;
  -webkit-transition: all 200ms ease;
  transition: all 200ms ease;
  height: 2.75rem;
  background-color: transparent;
  border: 1px solid #000;
  color: #000;
  font-size: 1.0625rem;
  font-weight: 600;
  letter-spacing: 2px;
  line-height: normal;
  padding: 0.48em;
}

.emotion-0:focus {
  outline: none;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  cursor: default;
}

.emotion-0:hover {
  background-color: rgba(255, 255, 255, 0.25);
  border-color: #3f3f3f;
  color: #3f3f3f;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  background-color: #FFF;
  border-color: #CCC;
  color: #CCC;
}

<button
    class="emotion-0"
    disabled=""
  />
</DocumentFragment>
`;

exports[`<FixedButton /> snapshots renders disabled secondary outline state correctly BananaRepublic in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 0;
  text-align: center;
  display: block;
  width: 100%;
  padding: 0.5em 0.8em;
  font-size: 1rem;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 700;
  color: #FFF;
  background-color: #000;
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  box-sizing: border-box;
  cursor: pointer;
  text-align: center;
  text-transform: uppercase;
  -webkit-transition: all 200ms ease;
  transition: all 200ms ease;
  height: 2.75rem;
  background-color: transparent;
  border: 2px solid #333;
  color: #333;
  font-size: 1rem;
  font-weight: 600;
  letter-spacing: normal;
  line-height: normal;
  padding: 0.5rem;
}

.emotion-0:focus {
  outline: none;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  cursor: default;
}

.emotion-0:hover {
  background-color: #FFF;
  border-color: #666;
  color: #666;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  background-color: #CCC;
  border-color: transparent;
  color: #FFF;
}

<button
    class="emotion-0"
    disabled=""
  />
</DocumentFragment>
`;

exports[`<FixedButton /> snapshots renders disabled secondary outline state correctly BananaRepublicFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 0;
  text-align: center;
  display: block;
  width: 100%;
  padding: 0.5em 0.8em;
  font-size: 1rem;
  font-family: 'Lynstone',Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 700;
  color: #FFF;
  background-color: #000;
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  box-sizing: border-box;
  cursor: pointer;
  text-align: center;
  text-transform: uppercase;
  -webkit-transition: all 200ms ease;
  transition: all 200ms ease;
  height: 2.75rem;
  background-color: transparent;
  border: 1px solid #000;
  color: #000;
  font-size: 1.0625rem;
  font-weight: 600;
  letter-spacing: 2px;
  line-height: normal;
  padding: 0.48em;
}

.emotion-0:focus {
  outline: none;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  cursor: default;
}

.emotion-0:hover {
  background-color: rgba(255, 255, 255, 0.25);
  border-color: #3f3f3f;
  color: #3f3f3f;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  background-color: #FFF;
  border-color: #CCC;
  color: #CCC;
}

<button
    class="emotion-0"
    disabled=""
  />
</DocumentFragment>
`;

exports[`<FixedButton /> snapshots renders disabled secondary outline state correctly BananaRepublicFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 0;
  text-align: center;
  display: block;
  width: 100%;
  padding: 0.5em 0.8em;
  font-size: 1rem;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 700;
  color: #FFF;
  background-color: #000;
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  box-sizing: border-box;
  cursor: pointer;
  text-align: center;
  text-transform: uppercase;
  -webkit-transition: all 200ms ease;
  transition: all 200ms ease;
  height: 2.75rem;
  background-color: transparent;
  border: 2px solid #333;
  color: #333;
  font-size: 1rem;
  font-weight: 600;
  letter-spacing: normal;
  line-height: normal;
  padding: 0.5rem;
}

.emotion-0:focus {
  outline: none;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  cursor: default;
}

.emotion-0:hover {
  background-color: #FFF;
  border-color: #666;
  color: #666;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  background-color: #CCC;
  border-color: transparent;
  color: #FFF;
}

<button
    class="emotion-0"
    disabled=""
  />
</DocumentFragment>
`;

exports[`<FixedButton /> snapshots renders disabled secondary outline state correctly Gap 1`] = `
<DocumentFragment>
  .emotion-0 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 1px solid transparent;
  border-radius: 0;
  font-size: 16px;
  font-weight: 400;
  letter-spacing: normal;
  min-height: 45px;
  max-height: auto;
  line-height: 1.125;
  padding: 12px 20px 11px;
  width: 100%;
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #FFF;
  background-color: #002554;
  border-color: #002554;
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  box-sizing: border-box;
  cursor: pointer;
  text-align: center;
  text-transform: uppercase;
  -webkit-transition: all 200ms ease;
  transition: all 200ms ease;
  height: 2.75rem;
  background-color: transparent;
  border: 2px solid #0A5694;
  color: #0A5694;
  font-size: 1.0625rem;
  font-weight: 700;
  letter-spacing: 1px;
  line-height: normal;
  padding: 0.0625em 0.375em;
}

.emotion-0:focus {
  outline: none;
}

.emotion-0>span {
  padding: 1px 0;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  cursor: default;
}

.emotion-0:hover {
  background-color: rgba(255, 255, 255, 0.25);
  border-color: #4780ae;
  color: #4780ae;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  background-color: #FFF;
  border-color: #CCC;
  color: #CCC;
}

<button
    class="emotion-0"
    disabled=""
  />
</DocumentFragment>
`;

exports[`<FixedButton /> snapshots renders disabled secondary outline state correctly Gap in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 0;
  text-align: center;
  display: block;
  width: 100%;
  padding: 0.5em 0.8em;
  font-size: 1rem;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 700;
  color: #FFF;
  background-color: #000;
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  box-sizing: border-box;
  cursor: pointer;
  text-align: center;
  text-transform: uppercase;
  -webkit-transition: all 200ms ease;
  transition: all 200ms ease;
  height: 2.75rem;
  background-color: transparent;
  border: 2px solid #333;
  color: #333;
  font-size: 1rem;
  font-weight: 600;
  letter-spacing: normal;
  line-height: normal;
  padding: 0.5rem;
}

.emotion-0:focus {
  outline: none;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  cursor: default;
}

.emotion-0:hover {
  background-color: #FFF;
  border-color: #666;
  color: #666;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  background-color: #CCC;
  border-color: transparent;
  color: #FFF;
}

<button
    class="emotion-0"
    disabled=""
  />
</DocumentFragment>
`;

exports[`<FixedButton /> snapshots renders disabled secondary outline state correctly GapFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 1px solid transparent;
  border-radius: 0;
  font-size: 16px;
  font-weight: 400;
  letter-spacing: normal;
  min-height: 45px;
  max-height: auto;
  line-height: 1.125;
  padding: 12px 20px 11px;
  width: 100%;
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #FFF;
  background-color: #002554;
  border-color: #002554;
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  box-sizing: border-box;
  cursor: pointer;
  text-align: center;
  text-transform: uppercase;
  -webkit-transition: all 200ms ease;
  transition: all 200ms ease;
  height: 2.75rem;
  background-color: transparent;
  border: 2px solid #0A5694;
  color: #0A5694;
  font-size: 1.0625rem;
  font-weight: 700;
  letter-spacing: 1px;
  line-height: normal;
  padding: 0.0625em 0.375em;
}

.emotion-0:focus {
  outline: none;
}

.emotion-0>span {
  padding: 1px 0;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  cursor: default;
}

.emotion-0:hover {
  background-color: rgba(255, 255, 255, 0.25);
  border-color: #4780ae;
  color: #4780ae;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  background-color: #FFF;
  border-color: #CCC;
  color: #CCC;
}

<button
    class="emotion-0"
    disabled=""
  />
</DocumentFragment>
`;

exports[`<FixedButton /> snapshots renders disabled secondary outline state correctly GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 0;
  text-align: center;
  display: block;
  width: 100%;
  padding: 0.5em 0.8em;
  font-size: 1rem;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 700;
  color: #FFF;
  background-color: #000;
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  box-sizing: border-box;
  cursor: pointer;
  text-align: center;
  text-transform: uppercase;
  -webkit-transition: all 200ms ease;
  transition: all 200ms ease;
  height: 2.75rem;
  background-color: transparent;
  border: 2px solid #333;
  color: #333;
  font-size: 1rem;
  font-weight: 600;
  letter-spacing: normal;
  line-height: normal;
  padding: 0.5rem;
}

.emotion-0:focus {
  outline: none;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  cursor: default;
}

.emotion-0:hover {
  background-color: #FFF;
  border-color: #666;
  color: #666;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  background-color: #CCC;
  border-color: transparent;
  color: #FFF;
}

<button
    class="emotion-0"
    disabled=""
  />
</DocumentFragment>
`;

exports[`<FixedButton /> snapshots renders disabled secondary outline state correctly OldNavy 1`] = `
<DocumentFragment>
  .emotion-0 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 700;
  letter-spacing: 1.6800000000000002px;
  min-height: 44px;
  max-height: auto;
  line-height: 1.1428571428571428;
  padding: 12px 22px;
  width: 100%;
  font-family: 'Gotham','Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #FFF;
  background-color: #003764;
  border-color: #003764;
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  box-sizing: border-box;
  cursor: pointer;
  text-align: center;
  text-transform: uppercase;
  -webkit-transition: all 200ms ease;
  transition: all 200ms ease;
  height: 2.75rem;
  background-color: transparent;
  border: 2px solid #666;
  color: #666;
  font-size: 1.1875rem;
  font-weight: 700;
  letter-spacing: 1px;
  line-height: normal;
  padding: 0.5em;
}

.emotion-0:focus {
  outline: none;
}

.emotion-0>span {
  padding: 1px 0;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  cursor: default;
}

.emotion-0:hover {
  background-color: #FFF;
  border-color: #666;
  color: #8c8c8c;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  background-color: #CCC;
  border-color: transparent;
  color: #FFF;
}

<button
    class="emotion-0"
    disabled=""
  />
</DocumentFragment>
`;

exports[`<FixedButton /> snapshots renders disabled secondary outline state correctly OldNavy in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 0;
  text-align: center;
  display: block;
  width: 100%;
  padding: 0.5em 0.8em;
  font-size: 1rem;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 700;
  color: #FFF;
  background-color: #000;
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  box-sizing: border-box;
  cursor: pointer;
  text-align: center;
  text-transform: uppercase;
  -webkit-transition: all 200ms ease;
  transition: all 200ms ease;
  height: 2.75rem;
  background-color: transparent;
  border: 2px solid #333;
  color: #333;
  font-size: 1rem;
  font-weight: 600;
  letter-spacing: normal;
  line-height: normal;
  padding: 0.5rem;
}

.emotion-0:focus {
  outline: none;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  cursor: default;
}

.emotion-0:hover {
  background-color: #FFF;
  border-color: #666;
  color: #666;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  background-color: #CCC;
  border-color: transparent;
  color: #FFF;
}

<button
    class="emotion-0"
    disabled=""
  />
</DocumentFragment>
`;

exports[`<FixedButton /> snapshots renders disabled secondary state correctly Athleta 1`] = `
<DocumentFragment>
  .emotion-0 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 1.5px solid transparent;
  border-radius: 0;
  font-size: 16px;
  font-weight: 500;
  letter-spacing: 0px;
  min-height: auto;
  max-height: 60px;
  line-height: 1.25;
  padding: 22px 40px;
  width: 100%;
  font-family: 'Avenir Next','Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #FFF;
  background-color: #000;
  border-color: #000;
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  box-sizing: border-box;
  cursor: pointer;
  text-align: center;
  text-transform: uppercase;
  -webkit-transition: all 200ms ease;
  transition: all 200ms ease;
  height: 2.75rem;
  background-color: #FFF;
  border: 1px solid #333;
  color: #333;
  font-size: 1.1875rem;
  font-weight: 400;
  letter-spacing: 1px;
  line-height: normal;
  padding: 0.0625em 0.59em 0.125em;
}

.emotion-0:focus {
  outline: none;
}

.emotion-0>span {
  padding: 1px 0;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  cursor: default;
}

.emotion-0:hover {
  background-color: rgba(255, 255, 255, 0.25);
  border-color: #666;
  color: #666;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  background-color: #FFF;
  border-color: #CCC;
  color: #CCC;
}

<button
    class="emotion-0"
    disabled=""
  />
</DocumentFragment>
`;

exports[`<FixedButton /> snapshots renders disabled secondary state correctly Athleta in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 0;
  text-align: center;
  display: block;
  width: 100%;
  padding: 0.5em 0.8em;
  font-size: 1rem;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 700;
  color: #FFF;
  background-color: #000;
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  box-sizing: border-box;
  cursor: pointer;
  text-align: center;
  text-transform: uppercase;
  -webkit-transition: all 200ms ease;
  transition: all 200ms ease;
  height: 2.75rem;
  background-color: #333;
  border: 2px solid transparent;
  color: #FFF;
  font-size: 1rem;
  font-weight: 600;
  letter-spacing: normal;
  line-height: 1.5;
  padding: 0.5rem;
}

.emotion-0:focus {
  outline: none;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  cursor: default;
}

.emotion-0:hover {
  background-color: #666;
  border-color: transparent;
  color: #FFF;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  background-color: #CCC;
  border-color: transparent;
  color: #FFF;
}

<button
    class="emotion-0"
    disabled=""
  />
</DocumentFragment>
`;

exports[`<FixedButton /> snapshots renders disabled secondary state correctly BananaRepublic 1`] = `
<DocumentFragment>
  .emotion-0 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 0;
  text-align: center;
  display: block;
  width: 100%;
  padding: 0.5em 0.8em;
  font-size: 1rem;
  font-family: 'Lynstone',Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 600;
  color: #FFF;
  background-color: #000;
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  box-sizing: border-box;
  cursor: pointer;
  text-align: center;
  text-transform: uppercase;
  -webkit-transition: all 200ms ease;
  transition: all 200ms ease;
  height: 2.75rem;
  background-color: transparent;
  border: 1px solid #000;
  color: #000;
  font-size: 1.0625rem;
  font-weight: 600;
  letter-spacing: 2px;
  line-height: normal;
  padding: 0.48em;
}

.emotion-0:focus {
  outline: none;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  cursor: default;
}

.emotion-0:hover {
  background-color: rgba(255, 255, 255, 0.25);
  border-color: #3f3f3f;
  color: #3f3f3f;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  background-color: #FFF;
  border-color: #CCC;
  color: #CCC;
}

<button
    class="emotion-0"
    disabled=""
  />
</DocumentFragment>
`;

exports[`<FixedButton /> snapshots renders disabled secondary state correctly BananaRepublic in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 0;
  text-align: center;
  display: block;
  width: 100%;
  padding: 0.5em 0.8em;
  font-size: 1rem;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 700;
  color: #FFF;
  background-color: #000;
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  box-sizing: border-box;
  cursor: pointer;
  text-align: center;
  text-transform: uppercase;
  -webkit-transition: all 200ms ease;
  transition: all 200ms ease;
  height: 2.75rem;
  background-color: #333;
  border: 2px solid transparent;
  color: #FFF;
  font-size: 1rem;
  font-weight: 600;
  letter-spacing: normal;
  line-height: 1.5;
  padding: 0.5rem;
}

.emotion-0:focus {
  outline: none;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  cursor: default;
}

.emotion-0:hover {
  background-color: #666;
  border-color: transparent;
  color: #FFF;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  background-color: #CCC;
  border-color: transparent;
  color: #FFF;
}

<button
    class="emotion-0"
    disabled=""
  />
</DocumentFragment>
`;

exports[`<FixedButton /> snapshots renders disabled secondary state correctly BananaRepublicFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 0;
  text-align: center;
  display: block;
  width: 100%;
  padding: 0.5em 0.8em;
  font-size: 1rem;
  font-family: 'Lynstone',Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 700;
  color: #FFF;
  background-color: #000;
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  box-sizing: border-box;
  cursor: pointer;
  text-align: center;
  text-transform: uppercase;
  -webkit-transition: all 200ms ease;
  transition: all 200ms ease;
  height: 2.75rem;
  background-color: transparent;
  border: 1px solid #000;
  color: #000;
  font-size: 1.0625rem;
  font-weight: 600;
  letter-spacing: 2px;
  line-height: normal;
  padding: 0.48em;
}

.emotion-0:focus {
  outline: none;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  cursor: default;
}

.emotion-0:hover {
  background-color: rgba(255, 255, 255, 0.25);
  border-color: #3f3f3f;
  color: #3f3f3f;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  background-color: #FFF;
  border-color: #CCC;
  color: #CCC;
}

<button
    class="emotion-0"
    disabled=""
  />
</DocumentFragment>
`;

exports[`<FixedButton /> snapshots renders disabled secondary state correctly BananaRepublicFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 0;
  text-align: center;
  display: block;
  width: 100%;
  padding: 0.5em 0.8em;
  font-size: 1rem;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 700;
  color: #FFF;
  background-color: #000;
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  box-sizing: border-box;
  cursor: pointer;
  text-align: center;
  text-transform: uppercase;
  -webkit-transition: all 200ms ease;
  transition: all 200ms ease;
  height: 2.75rem;
  background-color: #333;
  border: 2px solid transparent;
  color: #FFF;
  font-size: 1rem;
  font-weight: 600;
  letter-spacing: normal;
  line-height: 1.5;
  padding: 0.5rem;
}

.emotion-0:focus {
  outline: none;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  cursor: default;
}

.emotion-0:hover {
  background-color: #666;
  border-color: transparent;
  color: #FFF;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  background-color: #CCC;
  border-color: transparent;
  color: #FFF;
}

<button
    class="emotion-0"
    disabled=""
  />
</DocumentFragment>
`;

exports[`<FixedButton /> snapshots renders disabled secondary state correctly Gap 1`] = `
<DocumentFragment>
  .emotion-0 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 1px solid transparent;
  border-radius: 0;
  font-size: 16px;
  font-weight: 400;
  letter-spacing: normal;
  min-height: 45px;
  max-height: auto;
  line-height: 1.125;
  padding: 12px 20px 11px;
  width: 100%;
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #FFF;
  background-color: #002554;
  border-color: #002554;
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  box-sizing: border-box;
  cursor: pointer;
  text-align: center;
  text-transform: uppercase;
  -webkit-transition: all 200ms ease;
  transition: all 200ms ease;
  height: 2.75rem;
  background-color: transparent;
  border: 2px solid #0A5694;
  color: #0A5694;
  font-size: 1.0625rem;
  font-weight: 700;
  letter-spacing: 1px;
  line-height: normal;
  padding: 0.0625em 0.59em 0.125em;
}

.emotion-0:focus {
  outline: none;
}

.emotion-0>span {
  padding: 1px 0;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  cursor: default;
}

.emotion-0:hover {
  background-color: rgba(255, 255, 255, 0.25);
  border-color: #4780ae;
  color: #4780ae;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  background-color: #FFF;
  border-color: #CCC;
  color: #CCC;
}

<button
    class="emotion-0"
    disabled=""
  />
</DocumentFragment>
`;

exports[`<FixedButton /> snapshots renders disabled secondary state correctly Gap in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 0;
  text-align: center;
  display: block;
  width: 100%;
  padding: 0.5em 0.8em;
  font-size: 1rem;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 700;
  color: #FFF;
  background-color: #000;
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  box-sizing: border-box;
  cursor: pointer;
  text-align: center;
  text-transform: uppercase;
  -webkit-transition: all 200ms ease;
  transition: all 200ms ease;
  height: 2.75rem;
  background-color: #333;
  border: 2px solid transparent;
  color: #FFF;
  font-size: 1rem;
  font-weight: 600;
  letter-spacing: normal;
  line-height: 1.5;
  padding: 0.5rem;
}

.emotion-0:focus {
  outline: none;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  cursor: default;
}

.emotion-0:hover {
  background-color: #666;
  border-color: transparent;
  color: #FFF;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  background-color: #CCC;
  border-color: transparent;
  color: #FFF;
}

<button
    class="emotion-0"
    disabled=""
  />
</DocumentFragment>
`;

exports[`<FixedButton /> snapshots renders disabled secondary state correctly GapFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 1px solid transparent;
  border-radius: 0;
  font-size: 16px;
  font-weight: 400;
  letter-spacing: normal;
  min-height: 45px;
  max-height: auto;
  line-height: 1.125;
  padding: 12px 20px 11px;
  width: 100%;
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #FFF;
  background-color: #002554;
  border-color: #002554;
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  box-sizing: border-box;
  cursor: pointer;
  text-align: center;
  text-transform: uppercase;
  -webkit-transition: all 200ms ease;
  transition: all 200ms ease;
  height: 2.75rem;
  background-color: transparent;
  border: 2px solid #0A5694;
  color: #0A5694;
  font-size: 1.0625rem;
  font-weight: 700;
  letter-spacing: 1px;
  line-height: normal;
  padding: 0.0625em 0.59em 0.125em;
}

.emotion-0:focus {
  outline: none;
}

.emotion-0>span {
  padding: 1px 0;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  cursor: default;
}

.emotion-0:hover {
  background-color: rgba(255, 255, 255, 0.25);
  border-color: #4780ae;
  color: #4780ae;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  background-color: #FFF;
  border-color: #CCC;
  color: #CCC;
}

<button
    class="emotion-0"
    disabled=""
  />
</DocumentFragment>
`;

exports[`<FixedButton /> snapshots renders disabled secondary state correctly GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 0;
  text-align: center;
  display: block;
  width: 100%;
  padding: 0.5em 0.8em;
  font-size: 1rem;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 700;
  color: #FFF;
  background-color: #000;
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  box-sizing: border-box;
  cursor: pointer;
  text-align: center;
  text-transform: uppercase;
  -webkit-transition: all 200ms ease;
  transition: all 200ms ease;
  height: 2.75rem;
  background-color: #333;
  border: 2px solid transparent;
  color: #FFF;
  font-size: 1rem;
  font-weight: 600;
  letter-spacing: normal;
  line-height: 1.5;
  padding: 0.5rem;
}

.emotion-0:focus {
  outline: none;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  cursor: default;
}

.emotion-0:hover {
  background-color: #666;
  border-color: transparent;
  color: #FFF;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  background-color: #CCC;
  border-color: transparent;
  color: #FFF;
}

<button
    class="emotion-0"
    disabled=""
  />
</DocumentFragment>
`;

exports[`<FixedButton /> snapshots renders disabled secondary state correctly OldNavy 1`] = `
<DocumentFragment>
  .emotion-0 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 700;
  letter-spacing: 1.6800000000000002px;
  min-height: 44px;
  max-height: auto;
  line-height: 1.1428571428571428;
  padding: 12px 22px;
  width: 100%;
  font-family: 'Gotham','Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #FFF;
  background-color: #003764;
  border-color: #003764;
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  box-sizing: border-box;
  cursor: pointer;
  text-align: center;
  text-transform: uppercase;
  -webkit-transition: all 200ms ease;
  transition: all 200ms ease;
  height: 2.75rem;
  background-color: #666;
  border: none;
  color: #FFF;
  font-size: 1.1875rem;
  font-weight: 700;
  letter-spacing: 1px;
  line-height: normal;
  padding: 0.5em;
}

.emotion-0:focus {
  outline: none;
}

.emotion-0>span {
  padding: 1px 0;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  cursor: default;
}

.emotion-0:hover {
  background-color: #8c8c8c;
  border-color: transparent;
  color: #FFF;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  background-color: #CCC;
  border-color: transparent;
  color: #FFF;
}

<button
    class="emotion-0"
    disabled=""
  />
</DocumentFragment>
`;

exports[`<FixedButton /> snapshots renders disabled secondary state correctly OldNavy in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 0;
  text-align: center;
  display: block;
  width: 100%;
  padding: 0.5em 0.8em;
  font-size: 1rem;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 700;
  color: #FFF;
  background-color: #000;
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  box-sizing: border-box;
  cursor: pointer;
  text-align: center;
  text-transform: uppercase;
  -webkit-transition: all 200ms ease;
  transition: all 200ms ease;
  height: 2.75rem;
  background-color: #333;
  border: 2px solid transparent;
  color: #FFF;
  font-size: 1rem;
  font-weight: 600;
  letter-spacing: normal;
  line-height: 1.5;
  padding: 0.5rem;
}

.emotion-0:focus {
  outline: none;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  cursor: default;
}

.emotion-0:hover {
  background-color: #666;
  border-color: transparent;
  color: #FFF;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  background-color: #CCC;
  border-color: transparent;
  color: #FFF;
}

<button
    class="emotion-0"
    disabled=""
  />
</DocumentFragment>
`;

exports[`<FixedButton /> snapshots renders disabled state correctly Athleta 1`] = `
<DocumentFragment>
  .emotion-0 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 1.5px solid transparent;
  border-radius: 0;
  font-size: 16px;
  font-weight: 500;
  letter-spacing: 0px;
  min-height: auto;
  max-height: 60px;
  line-height: 1.25;
  padding: 22px 40px;
  width: 100%;
  font-family: 'Avenir Next','Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #FFF;
  background-color: #000;
  border-color: #000;
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  box-sizing: border-box;
  cursor: pointer;
  text-align: center;
  text-transform: uppercase;
  -webkit-transition: all 200ms ease;
  transition: all 200ms ease;
  height: 2.75rem;
  background-color: #333;
  border: none;
  color: #FFF;
  font-size: 1.1875rem;
  font-weight: 600;
  letter-spacing: 1px;
  line-height: normal;
  padding: 0.0625em 0.52em 0.125em;
}

.emotion-0:focus {
  outline: none;
}

.emotion-0>span {
  padding: 1px 0;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  cursor: default;
}

.emotion-0:hover {
  background-color: #666;
  border-color: transparent;
  color: #FFF;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  background-color: #CCC;
  border-color: transparent;
  color: #FFF;
}

<button
    class="emotion-0"
    disabled=""
  />
</DocumentFragment>
`;

exports[`<FixedButton /> snapshots renders disabled state correctly Athleta in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 0;
  text-align: center;
  display: block;
  width: 100%;
  padding: 0.5em 0.8em;
  font-size: 1rem;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 700;
  color: #FFF;
  background-color: #000;
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  box-sizing: border-box;
  cursor: pointer;
  text-align: center;
  text-transform: uppercase;
  -webkit-transition: all 200ms ease;
  transition: all 200ms ease;
  height: 2.75rem;
  background-color: #F43D00;
  border: 2px solid transparent;
  color: #FFF;
  font-size: 1rem;
  font-weight: 600;
  letter-spacing: normal;
  line-height: 1.5;
  padding: 0.5rem;
}

.emotion-0:focus {
  outline: none;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  cursor: default;
}

.emotion-0:hover {
  background-color: #f66d3f;
  border-color: transparent;
  color: #FFF;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  background-color: #CCC;
  border-color: transparent;
  color: #FFF;
}

<button
    class="emotion-0"
    disabled=""
  />
</DocumentFragment>
`;

exports[`<FixedButton /> snapshots renders disabled state correctly BananaRepublic 1`] = `
<DocumentFragment>
  .emotion-0 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 0;
  text-align: center;
  display: block;
  width: 100%;
  padding: 0.5em 0.8em;
  font-size: 1rem;
  font-family: 'Lynstone',Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 600;
  color: #FFF;
  background-color: #000;
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  box-sizing: border-box;
  cursor: pointer;
  text-align: center;
  text-transform: uppercase;
  -webkit-transition: all 200ms ease;
  transition: all 200ms ease;
  height: 2.75rem;
  background-color: #000;
  border: none;
  color: #FFF;
  font-size: 1.0625rem;
  font-weight: 600;
  letter-spacing: 2px;
  line-height: normal;
  padding: 0.53em;
}

.emotion-0:focus {
  outline: none;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  cursor: default;
}

.emotion-0:hover {
  background-color: #3f3f3f;
  border-color: transparent;
  color: #FFF;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  background-color: #CCC;
  border-color: transparent;
  color: #FFF;
}

<button
    class="emotion-0"
    disabled=""
  />
</DocumentFragment>
`;

exports[`<FixedButton /> snapshots renders disabled state correctly BananaRepublic in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 0;
  text-align: center;
  display: block;
  width: 100%;
  padding: 0.5em 0.8em;
  font-size: 1rem;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 700;
  color: #FFF;
  background-color: #000;
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  box-sizing: border-box;
  cursor: pointer;
  text-align: center;
  text-transform: uppercase;
  -webkit-transition: all 200ms ease;
  transition: all 200ms ease;
  height: 2.75rem;
  background-color: #F43D00;
  border: 2px solid transparent;
  color: #FFF;
  font-size: 1rem;
  font-weight: 600;
  letter-spacing: normal;
  line-height: 1.5;
  padding: 0.5rem;
}

.emotion-0:focus {
  outline: none;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  cursor: default;
}

.emotion-0:hover {
  background-color: #f66d3f;
  border-color: transparent;
  color: #FFF;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  background-color: #CCC;
  border-color: transparent;
  color: #FFF;
}

<button
    class="emotion-0"
    disabled=""
  />
</DocumentFragment>
`;

exports[`<FixedButton /> snapshots renders disabled state correctly BananaRepublicFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 0;
  text-align: center;
  display: block;
  width: 100%;
  padding: 0.5em 0.8em;
  font-size: 1rem;
  font-family: 'Lynstone',Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 700;
  color: #FFF;
  background-color: #000;
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  box-sizing: border-box;
  cursor: pointer;
  text-align: center;
  text-transform: uppercase;
  -webkit-transition: all 200ms ease;
  transition: all 200ms ease;
  height: 2.75rem;
  background-color: #000;
  border: none;
  color: #FFF;
  font-size: 1.0625rem;
  font-weight: 600;
  letter-spacing: 2px;
  line-height: normal;
  padding: 0.53em;
}

.emotion-0:focus {
  outline: none;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  cursor: default;
}

.emotion-0:hover {
  background-color: #3f3f3f;
  border-color: transparent;
  color: #FFF;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  background-color: #CCC;
  border-color: transparent;
  color: #FFF;
}

<button
    class="emotion-0"
    disabled=""
  />
</DocumentFragment>
`;

exports[`<FixedButton /> snapshots renders disabled state correctly BananaRepublicFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 0;
  text-align: center;
  display: block;
  width: 100%;
  padding: 0.5em 0.8em;
  font-size: 1rem;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 700;
  color: #FFF;
  background-color: #000;
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  box-sizing: border-box;
  cursor: pointer;
  text-align: center;
  text-transform: uppercase;
  -webkit-transition: all 200ms ease;
  transition: all 200ms ease;
  height: 2.75rem;
  background-color: #F43D00;
  border: 2px solid transparent;
  color: #FFF;
  font-size: 1rem;
  font-weight: 600;
  letter-spacing: normal;
  line-height: 1.5;
  padding: 0.5rem;
}

.emotion-0:focus {
  outline: none;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  cursor: default;
}

.emotion-0:hover {
  background-color: #f66d3f;
  border-color: transparent;
  color: #FFF;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  background-color: #CCC;
  border-color: transparent;
  color: #FFF;
}

<button
    class="emotion-0"
    disabled=""
  />
</DocumentFragment>
`;

exports[`<FixedButton /> snapshots renders disabled state correctly Gap 1`] = `
<DocumentFragment>
  .emotion-0 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 1px solid transparent;
  border-radius: 0;
  font-size: 16px;
  font-weight: 400;
  letter-spacing: normal;
  min-height: 45px;
  max-height: auto;
  line-height: 1.125;
  padding: 12px 20px 11px;
  width: 100%;
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #FFF;
  background-color: #002554;
  border-color: #002554;
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  box-sizing: border-box;
  cursor: pointer;
  text-align: center;
  text-transform: uppercase;
  -webkit-transition: all 200ms ease;
  transition: all 200ms ease;
  height: 2.75rem;
  background-color: #002554;
  border: 2px solid #002554;
  color: #FFF;
  font-size: 1.0625rem;
  font-weight: 700;
  letter-spacing: 1px;
  line-height: normal;
  padding: 0.0625em 0.59em 0.125em;
}

.emotion-0:focus {
  outline: none;
}

.emotion-0>span {
  padding: 1px 0;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  cursor: default;
}

.emotion-0:hover {
  background-color: #3f5b7e;
  border-color: #3f5b7e;
  color: #FFF;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  background-color: #CCC;
  border-color: transparent;
  color: #FFF;
}

<button
    class="emotion-0"
    disabled=""
  />
</DocumentFragment>
`;

exports[`<FixedButton /> snapshots renders disabled state correctly Gap in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 0;
  text-align: center;
  display: block;
  width: 100%;
  padding: 0.5em 0.8em;
  font-size: 1rem;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 700;
  color: #FFF;
  background-color: #000;
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  box-sizing: border-box;
  cursor: pointer;
  text-align: center;
  text-transform: uppercase;
  -webkit-transition: all 200ms ease;
  transition: all 200ms ease;
  height: 2.75rem;
  background-color: #F43D00;
  border: 2px solid transparent;
  color: #FFF;
  font-size: 1rem;
  font-weight: 600;
  letter-spacing: normal;
  line-height: 1.5;
  padding: 0.5rem;
}

.emotion-0:focus {
  outline: none;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  cursor: default;
}

.emotion-0:hover {
  background-color: #f66d3f;
  border-color: transparent;
  color: #FFF;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  background-color: #CCC;
  border-color: transparent;
  color: #FFF;
}

<button
    class="emotion-0"
    disabled=""
  />
</DocumentFragment>
`;

exports[`<FixedButton /> snapshots renders disabled state correctly GapFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 1px solid transparent;
  border-radius: 0;
  font-size: 16px;
  font-weight: 400;
  letter-spacing: normal;
  min-height: 45px;
  max-height: auto;
  line-height: 1.125;
  padding: 12px 20px 11px;
  width: 100%;
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #FFF;
  background-color: #002554;
  border-color: #002554;
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  box-sizing: border-box;
  cursor: pointer;
  text-align: center;
  text-transform: uppercase;
  -webkit-transition: all 200ms ease;
  transition: all 200ms ease;
  height: 2.75rem;
  background-color: #002554;
  border: 2px solid #002554;
  color: #FFF;
  font-size: 1.0625rem;
  font-weight: 700;
  letter-spacing: 1px;
  line-height: normal;
  padding: 0.0625em 0.59em 0.125em;
}

.emotion-0:focus {
  outline: none;
}

.emotion-0>span {
  padding: 1px 0;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  cursor: default;
}

.emotion-0:hover {
  background-color: #3f5b7e;
  border-color: #3f5b7e;
  color: #FFF;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  background-color: #CCC;
  border-color: transparent;
  color: #FFF;
}

<button
    class="emotion-0"
    disabled=""
  />
</DocumentFragment>
`;

exports[`<FixedButton /> snapshots renders disabled state correctly GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 0;
  text-align: center;
  display: block;
  width: 100%;
  padding: 0.5em 0.8em;
  font-size: 1rem;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 700;
  color: #FFF;
  background-color: #000;
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  box-sizing: border-box;
  cursor: pointer;
  text-align: center;
  text-transform: uppercase;
  -webkit-transition: all 200ms ease;
  transition: all 200ms ease;
  height: 2.75rem;
  background-color: #F43D00;
  border: 2px solid transparent;
  color: #FFF;
  font-size: 1rem;
  font-weight: 600;
  letter-spacing: normal;
  line-height: 1.5;
  padding: 0.5rem;
}

.emotion-0:focus {
  outline: none;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  cursor: default;
}

.emotion-0:hover {
  background-color: #f66d3f;
  border-color: transparent;
  color: #FFF;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  background-color: #CCC;
  border-color: transparent;
  color: #FFF;
}

<button
    class="emotion-0"
    disabled=""
  />
</DocumentFragment>
`;

exports[`<FixedButton /> snapshots renders disabled state correctly OldNavy 1`] = `
<DocumentFragment>
  .emotion-0 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 700;
  letter-spacing: 1.6800000000000002px;
  min-height: 44px;
  max-height: auto;
  line-height: 1.1428571428571428;
  padding: 12px 22px;
  width: 100%;
  font-family: 'Gotham','Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #FFF;
  background-color: #003764;
  border-color: #003764;
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  box-sizing: border-box;
  cursor: pointer;
  text-align: center;
  text-transform: uppercase;
  -webkit-transition: all 200ms ease;
  transition: all 200ms ease;
  height: 2.75rem;
  background-color: #003764;
  border: none;
  color: #FFF;
  font-size: 1.1875rem;
  font-weight: 700;
  letter-spacing: 1px;
  line-height: normal;
  padding: 0.45em;
}

.emotion-0:focus {
  outline: none;
}

.emotion-0>span {
  padding: 1px 0;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  cursor: default;
}

.emotion-0:hover {
  background-color: #3f698a;
  border-color: transparent;
  color: #FFF;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  background-color: #CCC;
  border-color: transparent;
  color: #FFF;
}

<button
    class="emotion-0"
    disabled=""
  />
</DocumentFragment>
`;

exports[`<FixedButton /> snapshots renders disabled state correctly OldNavy in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 0;
  text-align: center;
  display: block;
  width: 100%;
  padding: 0.5em 0.8em;
  font-size: 1rem;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 700;
  color: #FFF;
  background-color: #000;
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  box-sizing: border-box;
  cursor: pointer;
  text-align: center;
  text-transform: uppercase;
  -webkit-transition: all 200ms ease;
  transition: all 200ms ease;
  height: 2.75rem;
  background-color: #F43D00;
  border: 2px solid transparent;
  color: #FFF;
  font-size: 1rem;
  font-weight: 600;
  letter-spacing: normal;
  line-height: 1.5;
  padding: 0.5rem;
}

.emotion-0:focus {
  outline: none;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  cursor: default;
}

.emotion-0:hover {
  background-color: #f66d3f;
  border-color: transparent;
  color: #FFF;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  background-color: #CCC;
  border-color: transparent;
  color: #FFF;
}

<button
    class="emotion-0"
    disabled=""
  />
</DocumentFragment>
`;

exports[`<FixedButton /> snapshots renders disabled tertiary flat state correctly Athleta 1`] = `
<DocumentFragment>
  .emotion-0 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 1.5px solid transparent;
  border-radius: 0;
  font-size: 16px;
  font-weight: 500;
  letter-spacing: 0px;
  min-height: auto;
  max-height: 60px;
  line-height: 1.25;
  padding: 22px 40px;
  width: 100%;
  font-family: 'Avenir Next','Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #FFF;
  background-color: #000;
  border-color: #000;
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  box-sizing: border-box;
  cursor: pointer;
  text-align: center;
  text-transform: uppercase;
  -webkit-transition: all 200ms ease;
  transition: all 200ms ease;
  height: 2.0625rem;
  background-color: transparent;
  border: none;
  color: #333;
  font-size: 81.25%;
  font-weight: 600;
  letter-spacing: 0.1em;
  line-height: normal;
  padding: 0.25rem 1rem;
}

.emotion-0:focus {
  outline: none;
}

.emotion-0>span {
  padding: 1px 0;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  cursor: default;
}

.emotion-0:hover {
  background-color: rgba(255, 255, 255, 0.25);
  border-color: transparent;
  color: #666;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  background-color: #FFF;
  border-color: transparent;
  color: #CCC;
}

<button
    class="emotion-0"
    disabled=""
  />
</DocumentFragment>
`;

exports[`<FixedButton /> snapshots renders disabled tertiary flat state correctly Athleta in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 0;
  text-align: center;
  display: block;
  width: 100%;
  padding: 0.5em 0.8em;
  font-size: 1rem;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 700;
  color: #FFF;
  background-color: #000;
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  box-sizing: border-box;
  cursor: pointer;
  text-align: center;
  text-transform: uppercase;
  -webkit-transition: all 200ms ease;
  transition: all 200ms ease;
  height: 2.0625rem;
  background-color: transparent;
  border: 2px solid transparent;
  color: #0466CA;
  font-size: 0.8125em;
  font-weight: 600;
  letter-spacing: normal;
  line-height: 1.5;
  padding: 0.1rem;
}

.emotion-0:focus {
  outline: none;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  cursor: default;
}

.emotion-0:hover {
  background-color: rgba(255, 255, 255, 0.25);
  border-color: transparent;
  color: #428cd7;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  background-color: #FFF;
  border-color: transparent;
  color: #CCC;
}

<button
    class="emotion-0"
    disabled=""
  />
</DocumentFragment>
`;

exports[`<FixedButton /> snapshots renders disabled tertiary flat state correctly BananaRepublic 1`] = `
<DocumentFragment>
  .emotion-0 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 0;
  text-align: center;
  display: block;
  width: 100%;
  padding: 0.5em 0.8em;
  font-size: 1rem;
  font-family: 'Lynstone',Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 600;
  color: #FFF;
  background-color: #000;
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  box-sizing: border-box;
  cursor: pointer;
  text-align: center;
  text-transform: uppercase;
  -webkit-transition: all 200ms ease;
  transition: all 200ms ease;
  height: 2.0625rem;
  background-color: transparent;
  border: none;
  color: #000;
  font-size: 0.9375rem;
  font-weight: 400;
  letter-spacing: 0.1em;
  line-height: normal;
  padding: 0.25rem 1rem;
}

.emotion-0:focus {
  outline: none;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  cursor: default;
}

.emotion-0:hover {
  background-color: rgba(255, 255, 255, 0.25);
  border-color: transparent;
  color: #3f3f3f;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  background-color: #FFF;
  border-color: transparent;
  color: #CCC;
}

<button
    class="emotion-0"
    disabled=""
  />
</DocumentFragment>
`;

exports[`<FixedButton /> snapshots renders disabled tertiary flat state correctly BananaRepublic in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 0;
  text-align: center;
  display: block;
  width: 100%;
  padding: 0.5em 0.8em;
  font-size: 1rem;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 700;
  color: #FFF;
  background-color: #000;
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  box-sizing: border-box;
  cursor: pointer;
  text-align: center;
  text-transform: uppercase;
  -webkit-transition: all 200ms ease;
  transition: all 200ms ease;
  height: 2.0625rem;
  background-color: transparent;
  border: 2px solid transparent;
  color: #0466CA;
  font-size: 0.8125em;
  font-weight: 600;
  letter-spacing: normal;
  line-height: 1.5;
  padding: 0.1rem;
}

.emotion-0:focus {
  outline: none;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  cursor: default;
}

.emotion-0:hover {
  background-color: rgba(255, 255, 255, 0.25);
  border-color: transparent;
  color: #428cd7;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  background-color: #FFF;
  border-color: transparent;
  color: #CCC;
}

<button
    class="emotion-0"
    disabled=""
  />
</DocumentFragment>
`;

exports[`<FixedButton /> snapshots renders disabled tertiary flat state correctly BananaRepublicFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 0;
  text-align: center;
  display: block;
  width: 100%;
  padding: 0.5em 0.8em;
  font-size: 1rem;
  font-family: 'Lynstone',Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 700;
  color: #FFF;
  background-color: #000;
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  box-sizing: border-box;
  cursor: pointer;
  text-align: center;
  text-transform: uppercase;
  -webkit-transition: all 200ms ease;
  transition: all 200ms ease;
  height: 2.0625rem;
  background-color: transparent;
  border: none;
  color: #000;
  font-size: 0.9375rem;
  font-weight: 400;
  letter-spacing: 0.1em;
  line-height: normal;
  padding: 0.25rem 1rem;
}

.emotion-0:focus {
  outline: none;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  cursor: default;
}

.emotion-0:hover {
  background-color: rgba(255, 255, 255, 0.25);
  border-color: transparent;
  color: #3f3f3f;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  background-color: #FFF;
  border-color: transparent;
  color: #CCC;
}

<button
    class="emotion-0"
    disabled=""
  />
</DocumentFragment>
`;

exports[`<FixedButton /> snapshots renders disabled tertiary flat state correctly BananaRepublicFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 0;
  text-align: center;
  display: block;
  width: 100%;
  padding: 0.5em 0.8em;
  font-size: 1rem;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 700;
  color: #FFF;
  background-color: #000;
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  box-sizing: border-box;
  cursor: pointer;
  text-align: center;
  text-transform: uppercase;
  -webkit-transition: all 200ms ease;
  transition: all 200ms ease;
  height: 2.0625rem;
  background-color: transparent;
  border: 2px solid transparent;
  color: #0466CA;
  font-size: 0.8125em;
  font-weight: 600;
  letter-spacing: normal;
  line-height: 1.5;
  padding: 0.1rem;
}

.emotion-0:focus {
  outline: none;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  cursor: default;
}

.emotion-0:hover {
  background-color: rgba(255, 255, 255, 0.25);
  border-color: transparent;
  color: #428cd7;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  background-color: #FFF;
  border-color: transparent;
  color: #CCC;
}

<button
    class="emotion-0"
    disabled=""
  />
</DocumentFragment>
`;

exports[`<FixedButton /> snapshots renders disabled tertiary flat state correctly Gap 1`] = `
<DocumentFragment>
  .emotion-0 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 1px solid transparent;
  border-radius: 0;
  font-size: 16px;
  font-weight: 400;
  letter-spacing: normal;
  min-height: 45px;
  max-height: auto;
  line-height: 1.125;
  padding: 12px 20px 11px;
  width: 100%;
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #FFF;
  background-color: #002554;
  border-color: #002554;
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  box-sizing: border-box;
  cursor: pointer;
  text-align: center;
  text-transform: uppercase;
  -webkit-transition: all 200ms ease;
  transition: all 200ms ease;
  height: 2.0625rem;
  background-color: transparent;
  border: none;
  color: #0A5694;
  font-size: 81.25%;
  font-weight: 400;
  letter-spacing: 0.1em;
  line-height: normal;
  padding: 0.45rem 1rem;
}

.emotion-0:focus {
  outline: none;
}

.emotion-0>span {
  padding: 1px 0;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  cursor: default;
}

.emotion-0:hover {
  background-color: rgba(255, 255, 255, 0.25);
  border-color: transparent;
  color: #4780ae;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  background-color: #FFF;
  border-color: transparent;
  color: #CCC;
}

<button
    class="emotion-0"
    disabled=""
  />
</DocumentFragment>
`;

exports[`<FixedButton /> snapshots renders disabled tertiary flat state correctly Gap in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 0;
  text-align: center;
  display: block;
  width: 100%;
  padding: 0.5em 0.8em;
  font-size: 1rem;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 700;
  color: #FFF;
  background-color: #000;
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  box-sizing: border-box;
  cursor: pointer;
  text-align: center;
  text-transform: uppercase;
  -webkit-transition: all 200ms ease;
  transition: all 200ms ease;
  height: 2.0625rem;
  background-color: transparent;
  border: 2px solid transparent;
  color: #0466CA;
  font-size: 0.8125em;
  font-weight: 600;
  letter-spacing: normal;
  line-height: 1.5;
  padding: 0.1rem;
}

.emotion-0:focus {
  outline: none;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  cursor: default;
}

.emotion-0:hover {
  background-color: rgba(255, 255, 255, 0.25);
  border-color: transparent;
  color: #428cd7;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  background-color: #FFF;
  border-color: transparent;
  color: #CCC;
}

<button
    class="emotion-0"
    disabled=""
  />
</DocumentFragment>
`;

exports[`<FixedButton /> snapshots renders disabled tertiary flat state correctly GapFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 1px solid transparent;
  border-radius: 0;
  font-size: 16px;
  font-weight: 400;
  letter-spacing: normal;
  min-height: 45px;
  max-height: auto;
  line-height: 1.125;
  padding: 12px 20px 11px;
  width: 100%;
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #FFF;
  background-color: #002554;
  border-color: #002554;
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  box-sizing: border-box;
  cursor: pointer;
  text-align: center;
  text-transform: uppercase;
  -webkit-transition: all 200ms ease;
  transition: all 200ms ease;
  height: 2.0625rem;
  background-color: transparent;
  border: none;
  color: #0A5694;
  font-size: 81.25%;
  font-weight: 400;
  letter-spacing: 0.1em;
  line-height: normal;
  padding: 0.45rem 1rem;
}

.emotion-0:focus {
  outline: none;
}

.emotion-0>span {
  padding: 1px 0;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  cursor: default;
}

.emotion-0:hover {
  background-color: rgba(255, 255, 255, 0.25);
  border-color: transparent;
  color: #4780ae;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  background-color: #FFF;
  border-color: transparent;
  color: #CCC;
}

<button
    class="emotion-0"
    disabled=""
  />
</DocumentFragment>
`;

exports[`<FixedButton /> snapshots renders disabled tertiary flat state correctly GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 0;
  text-align: center;
  display: block;
  width: 100%;
  padding: 0.5em 0.8em;
  font-size: 1rem;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 700;
  color: #FFF;
  background-color: #000;
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  box-sizing: border-box;
  cursor: pointer;
  text-align: center;
  text-transform: uppercase;
  -webkit-transition: all 200ms ease;
  transition: all 200ms ease;
  height: 2.0625rem;
  background-color: transparent;
  border: 2px solid transparent;
  color: #0466CA;
  font-size: 0.8125em;
  font-weight: 600;
  letter-spacing: normal;
  line-height: 1.5;
  padding: 0.1rem;
}

.emotion-0:focus {
  outline: none;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  cursor: default;
}

.emotion-0:hover {
  background-color: rgba(255, 255, 255, 0.25);
  border-color: transparent;
  color: #428cd7;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  background-color: #FFF;
  border-color: transparent;
  color: #CCC;
}

<button
    class="emotion-0"
    disabled=""
  />
</DocumentFragment>
`;

exports[`<FixedButton /> snapshots renders disabled tertiary flat state correctly OldNavy 1`] = `
<DocumentFragment>
  .emotion-0 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 700;
  letter-spacing: 1.6800000000000002px;
  min-height: 44px;
  max-height: auto;
  line-height: 1.1428571428571428;
  padding: 12px 22px;
  width: 100%;
  font-family: 'Gotham','Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #FFF;
  background-color: #003764;
  border-color: #003764;
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  box-sizing: border-box;
  cursor: pointer;
  text-align: center;
  text-transform: uppercase;
  -webkit-transition: all 200ms ease;
  transition: all 200ms ease;
  height: 2.0625rem;
  background-color: transparent;
  border: none;
  color: #003764;
  font-size: 81.25%;
  font-weight: 700;
  letter-spacing: 0.1em;
  line-height: normal;
  padding: 0.25rem 1rem;
}

.emotion-0:focus {
  outline: none;
}

.emotion-0>span {
  padding: 1px 0;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  cursor: default;
}

.emotion-0:hover {
  background-color: rgba(255, 255, 255, 0.25);
  border-color: transparent;
  color: #3f698a;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  background-color: #FFF;
  border-color: transparent;
  color: #CCC;
}

<button
    class="emotion-0"
    disabled=""
  />
</DocumentFragment>
`;

exports[`<FixedButton /> snapshots renders disabled tertiary flat state correctly OldNavy in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 0;
  text-align: center;
  display: block;
  width: 100%;
  padding: 0.5em 0.8em;
  font-size: 1rem;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 700;
  color: #FFF;
  background-color: #000;
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  box-sizing: border-box;
  cursor: pointer;
  text-align: center;
  text-transform: uppercase;
  -webkit-transition: all 200ms ease;
  transition: all 200ms ease;
  height: 2.0625rem;
  background-color: transparent;
  border: 2px solid transparent;
  color: #0466CA;
  font-size: 0.8125em;
  font-weight: 600;
  letter-spacing: normal;
  line-height: 1.5;
  padding: 0.1rem;
}

.emotion-0:focus {
  outline: none;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  cursor: default;
}

.emotion-0:hover {
  background-color: rgba(255, 255, 255, 0.25);
  border-color: transparent;
  color: #428cd7;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  background-color: #FFF;
  border-color: transparent;
  color: #CCC;
}

<button
    class="emotion-0"
    disabled=""
  />
</DocumentFragment>
`;

exports[`<FixedButton /> snapshots renders disabled tertiary outline state correctly Athleta 1`] = `
<DocumentFragment>
  .emotion-0 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 1.5px solid transparent;
  border-radius: 0;
  font-size: 16px;
  font-weight: 500;
  letter-spacing: 0px;
  min-height: auto;
  max-height: 60px;
  line-height: 1.25;
  padding: 22px 40px;
  width: 100%;
  font-family: 'Avenir Next','Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #FFF;
  background-color: #000;
  border-color: #000;
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  box-sizing: border-box;
  cursor: pointer;
  text-align: center;
  text-transform: uppercase;
  -webkit-transition: all 200ms ease;
  transition: all 200ms ease;
  height: 2.0625rem;
  background-color: transparent;
  border: 1px solid #333;
  color: #333;
  font-size: 81.25%;
  font-weight: 600;
  letter-spacing: normal;
  line-height: normal;
  padding: 0.1rem;
}

.emotion-0:focus {
  outline: none;
}

.emotion-0>span {
  padding: 1px 0;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  cursor: default;
}

.emotion-0:hover {
  background-color: rgba(255, 255, 255, 0.25);
  border-color: #666;
  color: #666;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  background-color: #FFF;
  border-color: #CCC;
  color: #CCC;
}

<button
    class="emotion-0"
    disabled=""
  />
</DocumentFragment>
`;

exports[`<FixedButton /> snapshots renders disabled tertiary outline state correctly Athleta in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 0;
  text-align: center;
  display: block;
  width: 100%;
  padding: 0.5em 0.8em;
  font-size: 1rem;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 700;
  color: #FFF;
  background-color: #000;
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  box-sizing: border-box;
  cursor: pointer;
  text-align: center;
  text-transform: uppercase;
  -webkit-transition: all 200ms ease;
  transition: all 200ms ease;
  height: 2.0625rem;
  background-color: transparent;
  border: 2px solid #333;
  color: #333;
  font-size: 0.8125rem;
  font-weight: 600;
  letter-spacing: normal;
  line-height: normal;
  padding: 0.1rem;
}

.emotion-0:focus {
  outline: none;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  cursor: default;
}

.emotion-0:hover {
  background-color: #FFF;
  border-color: #666;
  color: #666;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  background-color: #CCC;
  border-color: transparent;
  color: #FFF;
}

<button
    class="emotion-0"
    disabled=""
  />
</DocumentFragment>
`;

exports[`<FixedButton /> snapshots renders disabled tertiary outline state correctly BananaRepublic 1`] = `
<DocumentFragment>
  .emotion-0 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 0;
  text-align: center;
  display: block;
  width: 100%;
  padding: 0.5em 0.8em;
  font-size: 1rem;
  font-family: 'Lynstone',Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 600;
  color: #FFF;
  background-color: #000;
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  box-sizing: border-box;
  cursor: pointer;
  text-align: center;
  text-transform: uppercase;
  -webkit-transition: all 200ms ease;
  transition: all 200ms ease;
  height: 2.0625rem;
  background-color: transparent;
  border: 1px solid #000;
  color: #000;
  font-size: 93.75%;
  font-weight: 400;
  letter-spacing: normal;
  line-height: normal;
  padding: 0.1rem;
}

.emotion-0:focus {
  outline: none;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  cursor: default;
}

.emotion-0:hover {
  background-color: rgba(255, 255, 255, 0.25);
  border-color: #3f3f3f;
  color: #3f3f3f;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  background-color: #FFF;
  border-color: #CCC;
  color: #CCC;
}

<button
    class="emotion-0"
    disabled=""
  />
</DocumentFragment>
`;

exports[`<FixedButton /> snapshots renders disabled tertiary outline state correctly BananaRepublic in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 0;
  text-align: center;
  display: block;
  width: 100%;
  padding: 0.5em 0.8em;
  font-size: 1rem;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 700;
  color: #FFF;
  background-color: #000;
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  box-sizing: border-box;
  cursor: pointer;
  text-align: center;
  text-transform: uppercase;
  -webkit-transition: all 200ms ease;
  transition: all 200ms ease;
  height: 2.0625rem;
  background-color: transparent;
  border: 2px solid #333;
  color: #333;
  font-size: 0.8125rem;
  font-weight: 600;
  letter-spacing: normal;
  line-height: normal;
  padding: 0.1rem;
}

.emotion-0:focus {
  outline: none;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  cursor: default;
}

.emotion-0:hover {
  background-color: #FFF;
  border-color: #666;
  color: #666;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  background-color: #CCC;
  border-color: transparent;
  color: #FFF;
}

<button
    class="emotion-0"
    disabled=""
  />
</DocumentFragment>
`;

exports[`<FixedButton /> snapshots renders disabled tertiary outline state correctly BananaRepublicFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 0;
  text-align: center;
  display: block;
  width: 100%;
  padding: 0.5em 0.8em;
  font-size: 1rem;
  font-family: 'Lynstone',Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 700;
  color: #FFF;
  background-color: #000;
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  box-sizing: border-box;
  cursor: pointer;
  text-align: center;
  text-transform: uppercase;
  -webkit-transition: all 200ms ease;
  transition: all 200ms ease;
  height: 2.0625rem;
  background-color: transparent;
  border: 1px solid #000;
  color: #000;
  font-size: 93.75%;
  font-weight: 400;
  letter-spacing: normal;
  line-height: normal;
  padding: 0.1rem;
}

.emotion-0:focus {
  outline: none;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  cursor: default;
}

.emotion-0:hover {
  background-color: rgba(255, 255, 255, 0.25);
  border-color: #3f3f3f;
  color: #3f3f3f;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  background-color: #FFF;
  border-color: #CCC;
  color: #CCC;
}

<button
    class="emotion-0"
    disabled=""
  />
</DocumentFragment>
`;

exports[`<FixedButton /> snapshots renders disabled tertiary outline state correctly BananaRepublicFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 0;
  text-align: center;
  display: block;
  width: 100%;
  padding: 0.5em 0.8em;
  font-size: 1rem;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 700;
  color: #FFF;
  background-color: #000;
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  box-sizing: border-box;
  cursor: pointer;
  text-align: center;
  text-transform: uppercase;
  -webkit-transition: all 200ms ease;
  transition: all 200ms ease;
  height: 2.0625rem;
  background-color: transparent;
  border: 2px solid #333;
  color: #333;
  font-size: 0.8125rem;
  font-weight: 600;
  letter-spacing: normal;
  line-height: normal;
  padding: 0.1rem;
}

.emotion-0:focus {
  outline: none;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  cursor: default;
}

.emotion-0:hover {
  background-color: #FFF;
  border-color: #666;
  color: #666;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  background-color: #CCC;
  border-color: transparent;
  color: #FFF;
}

<button
    class="emotion-0"
    disabled=""
  />
</DocumentFragment>
`;

exports[`<FixedButton /> snapshots renders disabled tertiary outline state correctly Gap 1`] = `
<DocumentFragment>
  .emotion-0 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 1px solid transparent;
  border-radius: 0;
  font-size: 16px;
  font-weight: 400;
  letter-spacing: normal;
  min-height: 45px;
  max-height: auto;
  line-height: 1.125;
  padding: 12px 20px 11px;
  width: 100%;
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #FFF;
  background-color: #002554;
  border-color: #002554;
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  box-sizing: border-box;
  cursor: pointer;
  text-align: center;
  text-transform: uppercase;
  -webkit-transition: all 200ms ease;
  transition: all 200ms ease;
  height: 2.0625rem;
  background-color: transparent;
  border: 2px solid #0A5694;
  color: #0A5694;
  font-size: 81.25%;
  font-weight: 700;
  letter-spacing: normal;
  line-height: normal;
  padding: 0.1rem;
}

.emotion-0:focus {
  outline: none;
}

.emotion-0>span {
  padding: 1px 0;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  cursor: default;
}

.emotion-0:hover {
  background-color: rgba(255, 255, 255, 0.25);
  border-color: #4780ae;
  color: #4780ae;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  background-color: #FFF;
  border-color: #CCC;
  color: #CCC;
}

<button
    class="emotion-0"
    disabled=""
  />
</DocumentFragment>
`;

exports[`<FixedButton /> snapshots renders disabled tertiary outline state correctly Gap in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 0;
  text-align: center;
  display: block;
  width: 100%;
  padding: 0.5em 0.8em;
  font-size: 1rem;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 700;
  color: #FFF;
  background-color: #000;
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  box-sizing: border-box;
  cursor: pointer;
  text-align: center;
  text-transform: uppercase;
  -webkit-transition: all 200ms ease;
  transition: all 200ms ease;
  height: 2.0625rem;
  background-color: transparent;
  border: 2px solid #333;
  color: #333;
  font-size: 0.8125rem;
  font-weight: 600;
  letter-spacing: normal;
  line-height: normal;
  padding: 0.1rem;
}

.emotion-0:focus {
  outline: none;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  cursor: default;
}

.emotion-0:hover {
  background-color: #FFF;
  border-color: #666;
  color: #666;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  background-color: #CCC;
  border-color: transparent;
  color: #FFF;
}

<button
    class="emotion-0"
    disabled=""
  />
</DocumentFragment>
`;

exports[`<FixedButton /> snapshots renders disabled tertiary outline state correctly GapFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 1px solid transparent;
  border-radius: 0;
  font-size: 16px;
  font-weight: 400;
  letter-spacing: normal;
  min-height: 45px;
  max-height: auto;
  line-height: 1.125;
  padding: 12px 20px 11px;
  width: 100%;
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #FFF;
  background-color: #002554;
  border-color: #002554;
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  box-sizing: border-box;
  cursor: pointer;
  text-align: center;
  text-transform: uppercase;
  -webkit-transition: all 200ms ease;
  transition: all 200ms ease;
  height: 2.0625rem;
  background-color: transparent;
  border: 2px solid #0A5694;
  color: #0A5694;
  font-size: 81.25%;
  font-weight: 700;
  letter-spacing: normal;
  line-height: normal;
  padding: 0.1rem;
}

.emotion-0:focus {
  outline: none;
}

.emotion-0>span {
  padding: 1px 0;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  cursor: default;
}

.emotion-0:hover {
  background-color: rgba(255, 255, 255, 0.25);
  border-color: #4780ae;
  color: #4780ae;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  background-color: #FFF;
  border-color: #CCC;
  color: #CCC;
}

<button
    class="emotion-0"
    disabled=""
  />
</DocumentFragment>
`;

exports[`<FixedButton /> snapshots renders disabled tertiary outline state correctly GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 0;
  text-align: center;
  display: block;
  width: 100%;
  padding: 0.5em 0.8em;
  font-size: 1rem;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 700;
  color: #FFF;
  background-color: #000;
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  box-sizing: border-box;
  cursor: pointer;
  text-align: center;
  text-transform: uppercase;
  -webkit-transition: all 200ms ease;
  transition: all 200ms ease;
  height: 2.0625rem;
  background-color: transparent;
  border: 2px solid #333;
  color: #333;
  font-size: 0.8125rem;
  font-weight: 600;
  letter-spacing: normal;
  line-height: normal;
  padding: 0.1rem;
}

.emotion-0:focus {
  outline: none;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  cursor: default;
}

.emotion-0:hover {
  background-color: #FFF;
  border-color: #666;
  color: #666;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  background-color: #CCC;
  border-color: transparent;
  color: #FFF;
}

<button
    class="emotion-0"
    disabled=""
  />
</DocumentFragment>
`;

exports[`<FixedButton /> snapshots renders disabled tertiary outline state correctly OldNavy 1`] = `
<DocumentFragment>
  .emotion-0 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 700;
  letter-spacing: 1.6800000000000002px;
  min-height: 44px;
  max-height: auto;
  line-height: 1.1428571428571428;
  padding: 12px 22px;
  width: 100%;
  font-family: 'Gotham','Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #FFF;
  background-color: #003764;
  border-color: #003764;
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  box-sizing: border-box;
  cursor: pointer;
  text-align: center;
  text-transform: uppercase;
  -webkit-transition: all 200ms ease;
  transition: all 200ms ease;
  height: 2.0625rem;
  background-color: transparent;
  border: 2px solid #003764;
  color: #003764;
  font-size: 87.5%;
  font-weight: 700;
  letter-spacing: normal;
  line-height: normal;
  padding: 0.1rem;
}

.emotion-0:focus {
  outline: none;
}

.emotion-0>span {
  padding: 1px 0;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  cursor: default;
}

.emotion-0:hover {
  background-color: #FFF;
  border-color: #3f698a;
  color: #3f698a;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  background-color: #CCC;
  border-color: transparent;
  color: #FFF;
}

<button
    class="emotion-0"
    disabled=""
  />
</DocumentFragment>
`;

exports[`<FixedButton /> snapshots renders disabled tertiary outline state correctly OldNavy in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 0;
  text-align: center;
  display: block;
  width: 100%;
  padding: 0.5em 0.8em;
  font-size: 1rem;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 700;
  color: #FFF;
  background-color: #000;
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  box-sizing: border-box;
  cursor: pointer;
  text-align: center;
  text-transform: uppercase;
  -webkit-transition: all 200ms ease;
  transition: all 200ms ease;
  height: 2.0625rem;
  background-color: transparent;
  border: 2px solid #333;
  color: #333;
  font-size: 0.8125rem;
  font-weight: 600;
  letter-spacing: normal;
  line-height: normal;
  padding: 0.1rem;
}

.emotion-0:focus {
  outline: none;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  cursor: default;
}

.emotion-0:hover {
  background-color: #FFF;
  border-color: #666;
  color: #666;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  background-color: #CCC;
  border-color: transparent;
  color: #FFF;
}

<button
    class="emotion-0"
    disabled=""
  />
</DocumentFragment>
`;

exports[`<FixedButton /> snapshots renders disabled tertiary state correctly Athleta 1`] = `
<DocumentFragment>
  .emotion-0 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 1.5px solid transparent;
  border-radius: 0;
  font-size: 16px;
  font-weight: 500;
  letter-spacing: 0px;
  min-height: auto;
  max-height: 60px;
  line-height: 1.25;
  padding: 22px 40px;
  width: 100%;
  font-family: 'Avenir Next','Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #FFF;
  background-color: #000;
  border-color: #000;
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  box-sizing: border-box;
  cursor: pointer;
  text-align: center;
  text-transform: uppercase;
  -webkit-transition: all 200ms ease;
  transition: all 200ms ease;
  height: 2.0625rem;
  background-color: #FFF;
  border: 1px solid #333;
  color: #333;
  font-size: 81.25%;
  font-weight: 600;
  letter-spacing: normal;
  line-height: normal;
  padding: 0.5em 1rem 0.655em 1rem;
}

.emotion-0:focus {
  outline: none;
}

.emotion-0>span {
  padding: 1px 0;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  cursor: default;
}

.emotion-0:hover {
  background-color: #fff;
  border-color: #666;
  color: #666;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  background-color: #FFF;
  border-color: #CCC;
  color: #CCC;
}

<button
    class="emotion-0"
    disabled=""
  />
</DocumentFragment>
`;

exports[`<FixedButton /> snapshots renders disabled tertiary state correctly Athleta in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 0;
  text-align: center;
  display: block;
  width: 100%;
  padding: 0.5em 0.8em;
  font-size: 1rem;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 700;
  color: #FFF;
  background-color: #000;
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  box-sizing: border-box;
  cursor: pointer;
  text-align: center;
  text-transform: uppercase;
  -webkit-transition: all 200ms ease;
  transition: all 200ms ease;
  height: 2.0625rem;
  background-color: #333;
  border: 2px solid transparent;
  color: #FFF;
  font-size: 0.8125rem;
  font-weight: 600;
  letter-spacing: normal;
  line-height: normal;
  padding: 0.1rem;
}

.emotion-0:focus {
  outline: none;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  cursor: default;
}

.emotion-0:hover {
  background-color: #666;
  border-color: transparent;
  color: #fff;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  background-color: #CCC;
  border-color: transparent;
  color: #FFF;
}

<button
    class="emotion-0"
    disabled=""
  />
</DocumentFragment>
`;

exports[`<FixedButton /> snapshots renders disabled tertiary state correctly BananaRepublic 1`] = `
<DocumentFragment>
  .emotion-0 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 0;
  text-align: center;
  display: block;
  width: 100%;
  padding: 0.5em 0.8em;
  font-size: 1rem;
  font-family: 'Lynstone',Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 600;
  color: #FFF;
  background-color: #000;
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  box-sizing: border-box;
  cursor: pointer;
  text-align: center;
  text-transform: uppercase;
  -webkit-transition: all 200ms ease;
  transition: all 200ms ease;
  height: 2.0625rem;
  background-color: #FFF;
  border: 1px solid #000;
  color: #000;
  font-size: 93.75%;
  font-weight: 400;
  letter-spacing: normal;
  line-height: normal;
  padding: 0.313em 1rem;
}

.emotion-0:focus {
  outline: none;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  cursor: default;
}

.emotion-0:hover {
  background-color: #fff;
  border-color: #3f3f3f;
  color: #3f3f3f;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  background-color: #FFF;
  border-color: #CCC;
  color: #CCC;
}

<button
    class="emotion-0"
    disabled=""
  />
</DocumentFragment>
`;

exports[`<FixedButton /> snapshots renders disabled tertiary state correctly BananaRepublic in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 0;
  text-align: center;
  display: block;
  width: 100%;
  padding: 0.5em 0.8em;
  font-size: 1rem;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 700;
  color: #FFF;
  background-color: #000;
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  box-sizing: border-box;
  cursor: pointer;
  text-align: center;
  text-transform: uppercase;
  -webkit-transition: all 200ms ease;
  transition: all 200ms ease;
  height: 2.0625rem;
  background-color: #333;
  border: 2px solid transparent;
  color: #FFF;
  font-size: 0.8125rem;
  font-weight: 600;
  letter-spacing: normal;
  line-height: normal;
  padding: 0.1rem;
}

.emotion-0:focus {
  outline: none;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  cursor: default;
}

.emotion-0:hover {
  background-color: #666;
  border-color: transparent;
  color: #fff;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  background-color: #CCC;
  border-color: transparent;
  color: #FFF;
}

<button
    class="emotion-0"
    disabled=""
  />
</DocumentFragment>
`;

exports[`<FixedButton /> snapshots renders disabled tertiary state correctly BananaRepublicFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 0;
  text-align: center;
  display: block;
  width: 100%;
  padding: 0.5em 0.8em;
  font-size: 1rem;
  font-family: 'Lynstone',Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 700;
  color: #FFF;
  background-color: #000;
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  box-sizing: border-box;
  cursor: pointer;
  text-align: center;
  text-transform: uppercase;
  -webkit-transition: all 200ms ease;
  transition: all 200ms ease;
  height: 2.0625rem;
  background-color: #FFF;
  border: 1px solid #000;
  color: #000;
  font-size: 93.75%;
  font-weight: 400;
  letter-spacing: normal;
  line-height: normal;
  padding: 0.313em 1rem;
}

.emotion-0:focus {
  outline: none;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  cursor: default;
}

.emotion-0:hover {
  background-color: #fff;
  border-color: #3f3f3f;
  color: #3f3f3f;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  background-color: #FFF;
  border-color: #CCC;
  color: #CCC;
}

<button
    class="emotion-0"
    disabled=""
  />
</DocumentFragment>
`;

exports[`<FixedButton /> snapshots renders disabled tertiary state correctly BananaRepublicFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 0;
  text-align: center;
  display: block;
  width: 100%;
  padding: 0.5em 0.8em;
  font-size: 1rem;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 700;
  color: #FFF;
  background-color: #000;
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  box-sizing: border-box;
  cursor: pointer;
  text-align: center;
  text-transform: uppercase;
  -webkit-transition: all 200ms ease;
  transition: all 200ms ease;
  height: 2.0625rem;
  background-color: #333;
  border: 2px solid transparent;
  color: #FFF;
  font-size: 0.8125rem;
  font-weight: 600;
  letter-spacing: normal;
  line-height: normal;
  padding: 0.1rem;
}

.emotion-0:focus {
  outline: none;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  cursor: default;
}

.emotion-0:hover {
  background-color: #666;
  border-color: transparent;
  color: #fff;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  background-color: #CCC;
  border-color: transparent;
  color: #FFF;
}

<button
    class="emotion-0"
    disabled=""
  />
</DocumentFragment>
`;

exports[`<FixedButton /> snapshots renders disabled tertiary state correctly Gap 1`] = `
<DocumentFragment>
  .emotion-0 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 1px solid transparent;
  border-radius: 0;
  font-size: 16px;
  font-weight: 400;
  letter-spacing: normal;
  min-height: 45px;
  max-height: auto;
  line-height: 1.125;
  padding: 12px 20px 11px;
  width: 100%;
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #FFF;
  background-color: #002554;
  border-color: #002554;
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  box-sizing: border-box;
  cursor: pointer;
  text-align: center;
  text-transform: uppercase;
  -webkit-transition: all 200ms ease;
  transition: all 200ms ease;
  height: 2.0625rem;
  background-color: #0A5694;
  border: none;
  color: #FFF;
  font-size: 81.25%;
  font-weight: 700;
  letter-spacing: normal;
  line-height: normal;
  padding: 0.6em 1rem 0.693em 1rem;
}

.emotion-0:focus {
  outline: none;
}

.emotion-0>span {
  padding: 1px 0;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  cursor: default;
}

.emotion-0:hover {
  background-color: #4780ae;
  border-color: transparent;
  color: #fff;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  background-color: #CCC;
  border-color: transparent;
  color: #FFF;
}

<button
    class="emotion-0"
    disabled=""
  />
</DocumentFragment>
`;

exports[`<FixedButton /> snapshots renders disabled tertiary state correctly Gap in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 0;
  text-align: center;
  display: block;
  width: 100%;
  padding: 0.5em 0.8em;
  font-size: 1rem;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 700;
  color: #FFF;
  background-color: #000;
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  box-sizing: border-box;
  cursor: pointer;
  text-align: center;
  text-transform: uppercase;
  -webkit-transition: all 200ms ease;
  transition: all 200ms ease;
  height: 2.0625rem;
  background-color: #333;
  border: 2px solid transparent;
  color: #FFF;
  font-size: 0.8125rem;
  font-weight: 600;
  letter-spacing: normal;
  line-height: normal;
  padding: 0.1rem;
}

.emotion-0:focus {
  outline: none;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  cursor: default;
}

.emotion-0:hover {
  background-color: #666;
  border-color: transparent;
  color: #fff;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  background-color: #CCC;
  border-color: transparent;
  color: #FFF;
}

<button
    class="emotion-0"
    disabled=""
  />
</DocumentFragment>
`;

exports[`<FixedButton /> snapshots renders disabled tertiary state correctly GapFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 1px solid transparent;
  border-radius: 0;
  font-size: 16px;
  font-weight: 400;
  letter-spacing: normal;
  min-height: 45px;
  max-height: auto;
  line-height: 1.125;
  padding: 12px 20px 11px;
  width: 100%;
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #FFF;
  background-color: #002554;
  border-color: #002554;
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  box-sizing: border-box;
  cursor: pointer;
  text-align: center;
  text-transform: uppercase;
  -webkit-transition: all 200ms ease;
  transition: all 200ms ease;
  height: 2.0625rem;
  background-color: #0A5694;
  border: none;
  color: #FFF;
  font-size: 81.25%;
  font-weight: 700;
  letter-spacing: normal;
  line-height: normal;
  padding: 0.6em 1rem 0.693em 1rem;
}

.emotion-0:focus {
  outline: none;
}

.emotion-0>span {
  padding: 1px 0;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  cursor: default;
}

.emotion-0:hover {
  background-color: #4780ae;
  border-color: transparent;
  color: #fff;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  background-color: #CCC;
  border-color: transparent;
  color: #FFF;
}

<button
    class="emotion-0"
    disabled=""
  />
</DocumentFragment>
`;

exports[`<FixedButton /> snapshots renders disabled tertiary state correctly GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 0;
  text-align: center;
  display: block;
  width: 100%;
  padding: 0.5em 0.8em;
  font-size: 1rem;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 700;
  color: #FFF;
  background-color: #000;
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  box-sizing: border-box;
  cursor: pointer;
  text-align: center;
  text-transform: uppercase;
  -webkit-transition: all 200ms ease;
  transition: all 200ms ease;
  height: 2.0625rem;
  background-color: #333;
  border: 2px solid transparent;
  color: #FFF;
  font-size: 0.8125rem;
  font-weight: 600;
  letter-spacing: normal;
  line-height: normal;
  padding: 0.1rem;
}

.emotion-0:focus {
  outline: none;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  cursor: default;
}

.emotion-0:hover {
  background-color: #666;
  border-color: transparent;
  color: #fff;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  background-color: #CCC;
  border-color: transparent;
  color: #FFF;
}

<button
    class="emotion-0"
    disabled=""
  />
</DocumentFragment>
`;

exports[`<FixedButton /> snapshots renders disabled tertiary state correctly OldNavy 1`] = `
<DocumentFragment>
  .emotion-0 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 700;
  letter-spacing: 1.6800000000000002px;
  min-height: 44px;
  max-height: auto;
  line-height: 1.1428571428571428;
  padding: 12px 22px;
  width: 100%;
  font-family: 'Gotham','Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #FFF;
  background-color: #003764;
  border-color: #003764;
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  box-sizing: border-box;
  cursor: pointer;
  text-align: center;
  text-transform: uppercase;
  -webkit-transition: all 200ms ease;
  transition: all 200ms ease;
  height: 2.0625rem;
  background-color: #003764;
  border: none;
  color: #FFF;
  font-size: 87.5%;
  font-weight: 700;
  letter-spacing: normal;
  line-height: normal;
  padding: 0.5em 1rem;
}

.emotion-0:focus {
  outline: none;
}

.emotion-0>span {
  padding: 1px 0;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  cursor: default;
}

.emotion-0:hover {
  background-color: #3f698a;
  border-color: transparent;
  color: #fff;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  background-color: #CCC;
  border-color: transparent;
  color: #FFF;
}

<button
    class="emotion-0"
    disabled=""
  />
</DocumentFragment>
`;

exports[`<FixedButton /> snapshots renders disabled tertiary state correctly OldNavy in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 0;
  text-align: center;
  display: block;
  width: 100%;
  padding: 0.5em 0.8em;
  font-size: 1rem;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 700;
  color: #FFF;
  background-color: #000;
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  box-sizing: border-box;
  cursor: pointer;
  text-align: center;
  text-transform: uppercase;
  -webkit-transition: all 200ms ease;
  transition: all 200ms ease;
  height: 2.0625rem;
  background-color: #333;
  border: 2px solid transparent;
  color: #FFF;
  font-size: 0.8125rem;
  font-weight: 600;
  letter-spacing: normal;
  line-height: normal;
  padding: 0.1rem;
}

.emotion-0:focus {
  outline: none;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  cursor: default;
}

.emotion-0:hover {
  background-color: #666;
  border-color: transparent;
  color: #fff;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  background-color: #CCC;
  border-color: transparent;
  color: #FFF;
}

<button
    class="emotion-0"
    disabled=""
  />
</DocumentFragment>
`;

exports[`<FixedButton /> snapshots renders full-width state correctly Athleta 1`] = `
<DocumentFragment>
  .emotion-0 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 1.5px solid transparent;
  border-radius: 0;
  font-size: 16px;
  font-weight: 500;
  letter-spacing: 0px;
  min-height: auto;
  max-height: 60px;
  line-height: 1.25;
  padding: 22px 40px;
  width: 100%;
  font-family: 'Avenir Next','Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #FFF;
  background-color: #000;
  border-color: #000;
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  box-sizing: border-box;
  cursor: pointer;
  text-align: center;
  text-transform: uppercase;
  -webkit-transition: all 200ms ease;
  transition: all 200ms ease;
  height: 2.75rem;
  background-color: #333;
  border: none;
  color: #FFF;
  font-size: 1.1875rem;
  font-weight: 600;
  letter-spacing: 1px;
  line-height: normal;
  padding: 0.0625em 0.52em 0.125em;
}

.emotion-0:focus {
  outline: none;
}

.emotion-0>span {
  padding: 1px 0;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  cursor: default;
}

.emotion-0:hover {
  background-color: #666;
  border-color: transparent;
  color: #FFF;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  background-color: #CCC;
  border-color: transparent;
  color: #FFF;
}

<button
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`<FixedButton /> snapshots renders full-width state correctly Athleta in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 0;
  text-align: center;
  display: block;
  width: 100%;
  padding: 0.5em 0.8em;
  font-size: 1rem;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 700;
  color: #FFF;
  background-color: #000;
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  box-sizing: border-box;
  cursor: pointer;
  text-align: center;
  text-transform: uppercase;
  -webkit-transition: all 200ms ease;
  transition: all 200ms ease;
  height: 2.75rem;
  background-color: #F43D00;
  border: 2px solid transparent;
  color: #FFF;
  font-size: 1rem;
  font-weight: 600;
  letter-spacing: normal;
  line-height: 1.5;
  padding: 0.5rem;
}

.emotion-0:focus {
  outline: none;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  cursor: default;
}

.emotion-0:hover {
  background-color: #f66d3f;
  border-color: transparent;
  color: #FFF;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  background-color: #CCC;
  border-color: transparent;
  color: #FFF;
}

<button
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`<FixedButton /> snapshots renders full-width state correctly BananaRepublic 1`] = `
<DocumentFragment>
  .emotion-0 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 0;
  text-align: center;
  display: block;
  width: 100%;
  padding: 0.5em 0.8em;
  font-size: 1rem;
  font-family: 'Lynstone',Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 600;
  color: #FFF;
  background-color: #000;
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  box-sizing: border-box;
  cursor: pointer;
  text-align: center;
  text-transform: uppercase;
  -webkit-transition: all 200ms ease;
  transition: all 200ms ease;
  height: 2.75rem;
  background-color: #000;
  border: none;
  color: #FFF;
  font-size: 1.0625rem;
  font-weight: 600;
  letter-spacing: 2px;
  line-height: normal;
  padding: 0.53em;
}

.emotion-0:focus {
  outline: none;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  cursor: default;
}

.emotion-0:hover {
  background-color: #3f3f3f;
  border-color: transparent;
  color: #FFF;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  background-color: #CCC;
  border-color: transparent;
  color: #FFF;
}

<button
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`<FixedButton /> snapshots renders full-width state correctly BananaRepublic in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 0;
  text-align: center;
  display: block;
  width: 100%;
  padding: 0.5em 0.8em;
  font-size: 1rem;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 700;
  color: #FFF;
  background-color: #000;
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  box-sizing: border-box;
  cursor: pointer;
  text-align: center;
  text-transform: uppercase;
  -webkit-transition: all 200ms ease;
  transition: all 200ms ease;
  height: 2.75rem;
  background-color: #F43D00;
  border: 2px solid transparent;
  color: #FFF;
  font-size: 1rem;
  font-weight: 600;
  letter-spacing: normal;
  line-height: 1.5;
  padding: 0.5rem;
}

.emotion-0:focus {
  outline: none;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  cursor: default;
}

.emotion-0:hover {
  background-color: #f66d3f;
  border-color: transparent;
  color: #FFF;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  background-color: #CCC;
  border-color: transparent;
  color: #FFF;
}

<button
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`<FixedButton /> snapshots renders full-width state correctly BananaRepublicFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 0;
  text-align: center;
  display: block;
  width: 100%;
  padding: 0.5em 0.8em;
  font-size: 1rem;
  font-family: 'Lynstone',Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 700;
  color: #FFF;
  background-color: #000;
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  box-sizing: border-box;
  cursor: pointer;
  text-align: center;
  text-transform: uppercase;
  -webkit-transition: all 200ms ease;
  transition: all 200ms ease;
  height: 2.75rem;
  background-color: #000;
  border: none;
  color: #FFF;
  font-size: 1.0625rem;
  font-weight: 600;
  letter-spacing: 2px;
  line-height: normal;
  padding: 0.53em;
}

.emotion-0:focus {
  outline: none;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  cursor: default;
}

.emotion-0:hover {
  background-color: #3f3f3f;
  border-color: transparent;
  color: #FFF;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  background-color: #CCC;
  border-color: transparent;
  color: #FFF;
}

<button
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`<FixedButton /> snapshots renders full-width state correctly BananaRepublicFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 0;
  text-align: center;
  display: block;
  width: 100%;
  padding: 0.5em 0.8em;
  font-size: 1rem;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 700;
  color: #FFF;
  background-color: #000;
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  box-sizing: border-box;
  cursor: pointer;
  text-align: center;
  text-transform: uppercase;
  -webkit-transition: all 200ms ease;
  transition: all 200ms ease;
  height: 2.75rem;
  background-color: #F43D00;
  border: 2px solid transparent;
  color: #FFF;
  font-size: 1rem;
  font-weight: 600;
  letter-spacing: normal;
  line-height: 1.5;
  padding: 0.5rem;
}

.emotion-0:focus {
  outline: none;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  cursor: default;
}

.emotion-0:hover {
  background-color: #f66d3f;
  border-color: transparent;
  color: #FFF;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  background-color: #CCC;
  border-color: transparent;
  color: #FFF;
}

<button
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`<FixedButton /> snapshots renders full-width state correctly Gap 1`] = `
<DocumentFragment>
  .emotion-0 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 1px solid transparent;
  border-radius: 0;
  font-size: 16px;
  font-weight: 400;
  letter-spacing: normal;
  min-height: 45px;
  max-height: auto;
  line-height: 1.125;
  padding: 12px 20px 11px;
  width: 100%;
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #FFF;
  background-color: #002554;
  border-color: #002554;
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  box-sizing: border-box;
  cursor: pointer;
  text-align: center;
  text-transform: uppercase;
  -webkit-transition: all 200ms ease;
  transition: all 200ms ease;
  height: 2.75rem;
  background-color: #002554;
  border: 2px solid #002554;
  color: #FFF;
  font-size: 1.0625rem;
  font-weight: 700;
  letter-spacing: 1px;
  line-height: normal;
  padding: 0.0625em 0.59em 0.125em;
}

.emotion-0:focus {
  outline: none;
}

.emotion-0>span {
  padding: 1px 0;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  cursor: default;
}

.emotion-0:hover {
  background-color: #3f5b7e;
  border-color: #3f5b7e;
  color: #FFF;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  background-color: #CCC;
  border-color: transparent;
  color: #FFF;
}

<button
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`<FixedButton /> snapshots renders full-width state correctly Gap in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 0;
  text-align: center;
  display: block;
  width: 100%;
  padding: 0.5em 0.8em;
  font-size: 1rem;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 700;
  color: #FFF;
  background-color: #000;
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  box-sizing: border-box;
  cursor: pointer;
  text-align: center;
  text-transform: uppercase;
  -webkit-transition: all 200ms ease;
  transition: all 200ms ease;
  height: 2.75rem;
  background-color: #F43D00;
  border: 2px solid transparent;
  color: #FFF;
  font-size: 1rem;
  font-weight: 600;
  letter-spacing: normal;
  line-height: 1.5;
  padding: 0.5rem;
}

.emotion-0:focus {
  outline: none;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  cursor: default;
}

.emotion-0:hover {
  background-color: #f66d3f;
  border-color: transparent;
  color: #FFF;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  background-color: #CCC;
  border-color: transparent;
  color: #FFF;
}

<button
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`<FixedButton /> snapshots renders full-width state correctly GapFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 1px solid transparent;
  border-radius: 0;
  font-size: 16px;
  font-weight: 400;
  letter-spacing: normal;
  min-height: 45px;
  max-height: auto;
  line-height: 1.125;
  padding: 12px 20px 11px;
  width: 100%;
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #FFF;
  background-color: #002554;
  border-color: #002554;
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  box-sizing: border-box;
  cursor: pointer;
  text-align: center;
  text-transform: uppercase;
  -webkit-transition: all 200ms ease;
  transition: all 200ms ease;
  height: 2.75rem;
  background-color: #002554;
  border: 2px solid #002554;
  color: #FFF;
  font-size: 1.0625rem;
  font-weight: 700;
  letter-spacing: 1px;
  line-height: normal;
  padding: 0.0625em 0.59em 0.125em;
}

.emotion-0:focus {
  outline: none;
}

.emotion-0>span {
  padding: 1px 0;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  cursor: default;
}

.emotion-0:hover {
  background-color: #3f5b7e;
  border-color: #3f5b7e;
  color: #FFF;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  background-color: #CCC;
  border-color: transparent;
  color: #FFF;
}

<button
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`<FixedButton /> snapshots renders full-width state correctly GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 0;
  text-align: center;
  display: block;
  width: 100%;
  padding: 0.5em 0.8em;
  font-size: 1rem;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 700;
  color: #FFF;
  background-color: #000;
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  box-sizing: border-box;
  cursor: pointer;
  text-align: center;
  text-transform: uppercase;
  -webkit-transition: all 200ms ease;
  transition: all 200ms ease;
  height: 2.75rem;
  background-color: #F43D00;
  border: 2px solid transparent;
  color: #FFF;
  font-size: 1rem;
  font-weight: 600;
  letter-spacing: normal;
  line-height: 1.5;
  padding: 0.5rem;
}

.emotion-0:focus {
  outline: none;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  cursor: default;
}

.emotion-0:hover {
  background-color: #f66d3f;
  border-color: transparent;
  color: #FFF;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  background-color: #CCC;
  border-color: transparent;
  color: #FFF;
}

<button
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`<FixedButton /> snapshots renders full-width state correctly OldNavy 1`] = `
<DocumentFragment>
  .emotion-0 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 700;
  letter-spacing: 1.6800000000000002px;
  min-height: 44px;
  max-height: auto;
  line-height: 1.1428571428571428;
  padding: 12px 22px;
  width: 100%;
  font-family: 'Gotham','Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #FFF;
  background-color: #003764;
  border-color: #003764;
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  box-sizing: border-box;
  cursor: pointer;
  text-align: center;
  text-transform: uppercase;
  -webkit-transition: all 200ms ease;
  transition: all 200ms ease;
  height: 2.75rem;
  background-color: #003764;
  border: none;
  color: #FFF;
  font-size: 1.1875rem;
  font-weight: 700;
  letter-spacing: 1px;
  line-height: normal;
  padding: 0.45em;
}

.emotion-0:focus {
  outline: none;
}

.emotion-0>span {
  padding: 1px 0;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  cursor: default;
}

.emotion-0:hover {
  background-color: #3f698a;
  border-color: transparent;
  color: #FFF;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  background-color: #CCC;
  border-color: transparent;
  color: #FFF;
}

<button
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`<FixedButton /> snapshots renders full-width state correctly OldNavy in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 0;
  text-align: center;
  display: block;
  width: 100%;
  padding: 0.5em 0.8em;
  font-size: 1rem;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 700;
  color: #FFF;
  background-color: #000;
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  box-sizing: border-box;
  cursor: pointer;
  text-align: center;
  text-transform: uppercase;
  -webkit-transition: all 200ms ease;
  transition: all 200ms ease;
  height: 2.75rem;
  background-color: #F43D00;
  border: 2px solid transparent;
  color: #FFF;
  font-size: 1rem;
  font-weight: 600;
  letter-spacing: normal;
  line-height: 1.5;
  padding: 0.5rem;
}

.emotion-0:focus {
  outline: none;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  cursor: default;
}

.emotion-0:hover {
  background-color: #f66d3f;
  border-color: transparent;
  color: #FFF;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  background-color: #CCC;
  border-color: transparent;
  color: #FFF;
}

<button
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`<FixedButton /> snapshots renders primary state correctly Athleta 1`] = `
<DocumentFragment>
  .emotion-0 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 1.5px solid transparent;
  border-radius: 0;
  font-size: 16px;
  font-weight: 500;
  letter-spacing: 0px;
  min-height: auto;
  max-height: 60px;
  line-height: 1.25;
  padding: 22px 40px;
  width: 100%;
  font-family: 'Avenir Next','Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #FFF;
  background-color: #000;
  border-color: #000;
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  box-sizing: border-box;
  cursor: pointer;
  text-align: center;
  text-transform: uppercase;
  -webkit-transition: all 200ms ease;
  transition: all 200ms ease;
  height: 2.75rem;
  background-color: #333;
  border: none;
  color: #FFF;
  font-size: 1.1875rem;
  font-weight: 600;
  letter-spacing: 1px;
  line-height: normal;
  padding: 0.0625em 0.52em 0.125em;
}

.emotion-0:focus {
  outline: none;
}

.emotion-0>span {
  padding: 1px 0;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  cursor: default;
}

.emotion-0:hover {
  background-color: #666;
  border-color: transparent;
  color: #FFF;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  background-color: #CCC;
  border-color: transparent;
  color: #FFF;
}

<button
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`<FixedButton /> snapshots renders primary state correctly Athleta in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 0;
  text-align: center;
  display: block;
  width: 100%;
  padding: 0.5em 0.8em;
  font-size: 1rem;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 700;
  color: #FFF;
  background-color: #000;
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  box-sizing: border-box;
  cursor: pointer;
  text-align: center;
  text-transform: uppercase;
  -webkit-transition: all 200ms ease;
  transition: all 200ms ease;
  height: 2.75rem;
  background-color: #F43D00;
  border: 2px solid transparent;
  color: #FFF;
  font-size: 1rem;
  font-weight: 600;
  letter-spacing: normal;
  line-height: 1.5;
  padding: 0.5rem;
}

.emotion-0:focus {
  outline: none;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  cursor: default;
}

.emotion-0:hover {
  background-color: #f66d3f;
  border-color: transparent;
  color: #FFF;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  background-color: #CCC;
  border-color: transparent;
  color: #FFF;
}

<button
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`<FixedButton /> snapshots renders primary state correctly BananaRepublic 1`] = `
<DocumentFragment>
  .emotion-0 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 0;
  text-align: center;
  display: block;
  width: 100%;
  padding: 0.5em 0.8em;
  font-size: 1rem;
  font-family: 'Lynstone',Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 600;
  color: #FFF;
  background-color: #000;
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  box-sizing: border-box;
  cursor: pointer;
  text-align: center;
  text-transform: uppercase;
  -webkit-transition: all 200ms ease;
  transition: all 200ms ease;
  height: 2.75rem;
  background-color: #000;
  border: none;
  color: #FFF;
  font-size: 1.0625rem;
  font-weight: 600;
  letter-spacing: 2px;
  line-height: normal;
  padding: 0.53em;
}

.emotion-0:focus {
  outline: none;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  cursor: default;
}

.emotion-0:hover {
  background-color: #3f3f3f;
  border-color: transparent;
  color: #FFF;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  background-color: #CCC;
  border-color: transparent;
  color: #FFF;
}

<button
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`<FixedButton /> snapshots renders primary state correctly BananaRepublic in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 0;
  text-align: center;
  display: block;
  width: 100%;
  padding: 0.5em 0.8em;
  font-size: 1rem;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 700;
  color: #FFF;
  background-color: #000;
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  box-sizing: border-box;
  cursor: pointer;
  text-align: center;
  text-transform: uppercase;
  -webkit-transition: all 200ms ease;
  transition: all 200ms ease;
  height: 2.75rem;
  background-color: #F43D00;
  border: 2px solid transparent;
  color: #FFF;
  font-size: 1rem;
  font-weight: 600;
  letter-spacing: normal;
  line-height: 1.5;
  padding: 0.5rem;
}

.emotion-0:focus {
  outline: none;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  cursor: default;
}

.emotion-0:hover {
  background-color: #f66d3f;
  border-color: transparent;
  color: #FFF;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  background-color: #CCC;
  border-color: transparent;
  color: #FFF;
}

<button
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`<FixedButton /> snapshots renders primary state correctly BananaRepublicFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 0;
  text-align: center;
  display: block;
  width: 100%;
  padding: 0.5em 0.8em;
  font-size: 1rem;
  font-family: 'Lynstone',Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 700;
  color: #FFF;
  background-color: #000;
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  box-sizing: border-box;
  cursor: pointer;
  text-align: center;
  text-transform: uppercase;
  -webkit-transition: all 200ms ease;
  transition: all 200ms ease;
  height: 2.75rem;
  background-color: #000;
  border: none;
  color: #FFF;
  font-size: 1.0625rem;
  font-weight: 600;
  letter-spacing: 2px;
  line-height: normal;
  padding: 0.53em;
}

.emotion-0:focus {
  outline: none;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  cursor: default;
}

.emotion-0:hover {
  background-color: #3f3f3f;
  border-color: transparent;
  color: #FFF;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  background-color: #CCC;
  border-color: transparent;
  color: #FFF;
}

<button
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`<FixedButton /> snapshots renders primary state correctly BananaRepublicFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 0;
  text-align: center;
  display: block;
  width: 100%;
  padding: 0.5em 0.8em;
  font-size: 1rem;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 700;
  color: #FFF;
  background-color: #000;
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  box-sizing: border-box;
  cursor: pointer;
  text-align: center;
  text-transform: uppercase;
  -webkit-transition: all 200ms ease;
  transition: all 200ms ease;
  height: 2.75rem;
  background-color: #F43D00;
  border: 2px solid transparent;
  color: #FFF;
  font-size: 1rem;
  font-weight: 600;
  letter-spacing: normal;
  line-height: 1.5;
  padding: 0.5rem;
}

.emotion-0:focus {
  outline: none;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  cursor: default;
}

.emotion-0:hover {
  background-color: #f66d3f;
  border-color: transparent;
  color: #FFF;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  background-color: #CCC;
  border-color: transparent;
  color: #FFF;
}

<button
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`<FixedButton /> snapshots renders primary state correctly Gap 1`] = `
<DocumentFragment>
  .emotion-0 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 1px solid transparent;
  border-radius: 0;
  font-size: 16px;
  font-weight: 400;
  letter-spacing: normal;
  min-height: 45px;
  max-height: auto;
  line-height: 1.125;
  padding: 12px 20px 11px;
  width: 100%;
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #FFF;
  background-color: #002554;
  border-color: #002554;
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  box-sizing: border-box;
  cursor: pointer;
  text-align: center;
  text-transform: uppercase;
  -webkit-transition: all 200ms ease;
  transition: all 200ms ease;
  height: 2.75rem;
  background-color: #002554;
  border: 2px solid #002554;
  color: #FFF;
  font-size: 1.0625rem;
  font-weight: 700;
  letter-spacing: 1px;
  line-height: normal;
  padding: 0.0625em 0.59em 0.125em;
}

.emotion-0:focus {
  outline: none;
}

.emotion-0>span {
  padding: 1px 0;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  cursor: default;
}

.emotion-0:hover {
  background-color: #3f5b7e;
  border-color: #3f5b7e;
  color: #FFF;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  background-color: #CCC;
  border-color: transparent;
  color: #FFF;
}

<button
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`<FixedButton /> snapshots renders primary state correctly Gap in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 0;
  text-align: center;
  display: block;
  width: 100%;
  padding: 0.5em 0.8em;
  font-size: 1rem;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 700;
  color: #FFF;
  background-color: #000;
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  box-sizing: border-box;
  cursor: pointer;
  text-align: center;
  text-transform: uppercase;
  -webkit-transition: all 200ms ease;
  transition: all 200ms ease;
  height: 2.75rem;
  background-color: #F43D00;
  border: 2px solid transparent;
  color: #FFF;
  font-size: 1rem;
  font-weight: 600;
  letter-spacing: normal;
  line-height: 1.5;
  padding: 0.5rem;
}

.emotion-0:focus {
  outline: none;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  cursor: default;
}

.emotion-0:hover {
  background-color: #f66d3f;
  border-color: transparent;
  color: #FFF;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  background-color: #CCC;
  border-color: transparent;
  color: #FFF;
}

<button
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`<FixedButton /> snapshots renders primary state correctly GapFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 1px solid transparent;
  border-radius: 0;
  font-size: 16px;
  font-weight: 400;
  letter-spacing: normal;
  min-height: 45px;
  max-height: auto;
  line-height: 1.125;
  padding: 12px 20px 11px;
  width: 100%;
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #FFF;
  background-color: #002554;
  border-color: #002554;
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  box-sizing: border-box;
  cursor: pointer;
  text-align: center;
  text-transform: uppercase;
  -webkit-transition: all 200ms ease;
  transition: all 200ms ease;
  height: 2.75rem;
  background-color: #002554;
  border: 2px solid #002554;
  color: #FFF;
  font-size: 1.0625rem;
  font-weight: 700;
  letter-spacing: 1px;
  line-height: normal;
  padding: 0.0625em 0.59em 0.125em;
}

.emotion-0:focus {
  outline: none;
}

.emotion-0>span {
  padding: 1px 0;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  cursor: default;
}

.emotion-0:hover {
  background-color: #3f5b7e;
  border-color: #3f5b7e;
  color: #FFF;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  background-color: #CCC;
  border-color: transparent;
  color: #FFF;
}

<button
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`<FixedButton /> snapshots renders primary state correctly GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 0;
  text-align: center;
  display: block;
  width: 100%;
  padding: 0.5em 0.8em;
  font-size: 1rem;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 700;
  color: #FFF;
  background-color: #000;
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  box-sizing: border-box;
  cursor: pointer;
  text-align: center;
  text-transform: uppercase;
  -webkit-transition: all 200ms ease;
  transition: all 200ms ease;
  height: 2.75rem;
  background-color: #F43D00;
  border: 2px solid transparent;
  color: #FFF;
  font-size: 1rem;
  font-weight: 600;
  letter-spacing: normal;
  line-height: 1.5;
  padding: 0.5rem;
}

.emotion-0:focus {
  outline: none;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  cursor: default;
}

.emotion-0:hover {
  background-color: #f66d3f;
  border-color: transparent;
  color: #FFF;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  background-color: #CCC;
  border-color: transparent;
  color: #FFF;
}

<button
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`<FixedButton /> snapshots renders primary state correctly OldNavy 1`] = `
<DocumentFragment>
  .emotion-0 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 700;
  letter-spacing: 1.6800000000000002px;
  min-height: 44px;
  max-height: auto;
  line-height: 1.1428571428571428;
  padding: 12px 22px;
  width: 100%;
  font-family: 'Gotham','Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #FFF;
  background-color: #003764;
  border-color: #003764;
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  box-sizing: border-box;
  cursor: pointer;
  text-align: center;
  text-transform: uppercase;
  -webkit-transition: all 200ms ease;
  transition: all 200ms ease;
  height: 2.75rem;
  background-color: #003764;
  border: none;
  color: #FFF;
  font-size: 1.1875rem;
  font-weight: 700;
  letter-spacing: 1px;
  line-height: normal;
  padding: 0.45em;
}

.emotion-0:focus {
  outline: none;
}

.emotion-0>span {
  padding: 1px 0;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  cursor: default;
}

.emotion-0:hover {
  background-color: #3f698a;
  border-color: transparent;
  color: #FFF;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  background-color: #CCC;
  border-color: transparent;
  color: #FFF;
}

<button
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`<FixedButton /> snapshots renders primary state correctly OldNavy in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 0;
  text-align: center;
  display: block;
  width: 100%;
  padding: 0.5em 0.8em;
  font-size: 1rem;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 700;
  color: #FFF;
  background-color: #000;
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  box-sizing: border-box;
  cursor: pointer;
  text-align: center;
  text-transform: uppercase;
  -webkit-transition: all 200ms ease;
  transition: all 200ms ease;
  height: 2.75rem;
  background-color: #F43D00;
  border: 2px solid transparent;
  color: #FFF;
  font-size: 1rem;
  font-weight: 600;
  letter-spacing: normal;
  line-height: 1.5;
  padding: 0.5rem;
}

.emotion-0:focus {
  outline: none;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  cursor: default;
}

.emotion-0:hover {
  background-color: #f66d3f;
  border-color: transparent;
  color: #FFF;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  background-color: #CCC;
  border-color: transparent;
  color: #FFF;
}

<button
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`<FixedButton /> snapshots renders secondary flat state correctly Athleta 1`] = `
<DocumentFragment>
  .emotion-0 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 1.5px solid transparent;
  border-radius: 0;
  font-size: 16px;
  font-weight: 500;
  letter-spacing: 0px;
  min-height: auto;
  max-height: 60px;
  line-height: 1.25;
  padding: 22px 40px;
  width: 100%;
  font-family: 'Avenir Next','Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #FFF;
  background-color: #000;
  border-color: #000;
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  box-sizing: border-box;
  cursor: pointer;
  text-align: center;
  text-transform: uppercase;
  -webkit-transition: all 200ms ease;
  transition: all 200ms ease;
  height: 2.0625rem;
  background-color: transparent;
  border: none;
  color: #333;
  font-size: 1.1875rem;
  font-weight: 400;
  letter-spacing: 1px;
  line-height: normal;
  padding: 0.52em;
}

.emotion-0:focus {
  outline: none;
}

.emotion-0>span {
  padding: 1px 0;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  cursor: default;
}

.emotion-0:hover {
  background-color: rgba(255, 255, 255, 0.25);
  border-color: transparent;
  color: #666;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  background-color: #FFF;
  border-color: transparent;
  color: #CCC;
}

<button
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`<FixedButton /> snapshots renders secondary flat state correctly Athleta in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 0;
  text-align: center;
  display: block;
  width: 100%;
  padding: 0.5em 0.8em;
  font-size: 1rem;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 700;
  color: #FFF;
  background-color: #000;
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  box-sizing: border-box;
  cursor: pointer;
  text-align: center;
  text-transform: uppercase;
  -webkit-transition: all 200ms ease;
  transition: all 200ms ease;
  height: 2.0625rem;
  background-color: transparent;
  border: 2px solid transparent;
  color: #0466CA;
  font-size: 1rem;
  font-weight: 600;
  letter-spacing: normal;
  line-height: 1.5;
  padding: 0.5rem;
}

.emotion-0:focus {
  outline: none;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  cursor: default;
}

.emotion-0:hover {
  background-color: #FFF;
  border-color: transparent;
  color: #428cd7;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  background-color: #FFF;
  border-color: transparent;
  color: #CCC;
}

<button
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`<FixedButton /> snapshots renders secondary flat state correctly BananaRepublic 1`] = `
<DocumentFragment>
  .emotion-0 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 0;
  text-align: center;
  display: block;
  width: 100%;
  padding: 0.5em 0.8em;
  font-size: 1rem;
  font-family: 'Lynstone',Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 600;
  color: #FFF;
  background-color: #000;
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  box-sizing: border-box;
  cursor: pointer;
  text-align: center;
  text-transform: uppercase;
  -webkit-transition: all 200ms ease;
  transition: all 200ms ease;
  height: 2.0625rem;
  background-color: transparent;
  border: none;
  color: #000;
  font-size: 1.0625rem;
  font-weight: 600;
  letter-spacing: 2px;
  line-height: normal;
  padding: 0.53em;
}

.emotion-0:focus {
  outline: none;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  cursor: default;
}

.emotion-0:hover {
  background-color: rgba(255, 255, 255, 0.25);
  border-color: transparent;
  color: #3f3f3f;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  background-color: #FFF;
  border-color: transparent;
  color: #CCC;
}

<button
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`<FixedButton /> snapshots renders secondary flat state correctly BananaRepublic in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 0;
  text-align: center;
  display: block;
  width: 100%;
  padding: 0.5em 0.8em;
  font-size: 1rem;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 700;
  color: #FFF;
  background-color: #000;
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  box-sizing: border-box;
  cursor: pointer;
  text-align: center;
  text-transform: uppercase;
  -webkit-transition: all 200ms ease;
  transition: all 200ms ease;
  height: 2.0625rem;
  background-color: transparent;
  border: 2px solid transparent;
  color: #0466CA;
  font-size: 1rem;
  font-weight: 600;
  letter-spacing: normal;
  line-height: 1.5;
  padding: 0.5rem;
}

.emotion-0:focus {
  outline: none;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  cursor: default;
}

.emotion-0:hover {
  background-color: #FFF;
  border-color: transparent;
  color: #428cd7;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  background-color: #FFF;
  border-color: transparent;
  color: #CCC;
}

<button
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`<FixedButton /> snapshots renders secondary flat state correctly BananaRepublicFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 0;
  text-align: center;
  display: block;
  width: 100%;
  padding: 0.5em 0.8em;
  font-size: 1rem;
  font-family: 'Lynstone',Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 700;
  color: #FFF;
  background-color: #000;
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  box-sizing: border-box;
  cursor: pointer;
  text-align: center;
  text-transform: uppercase;
  -webkit-transition: all 200ms ease;
  transition: all 200ms ease;
  height: 2.0625rem;
  background-color: transparent;
  border: none;
  color: #000;
  font-size: 1.0625rem;
  font-weight: 600;
  letter-spacing: 2px;
  line-height: normal;
  padding: 0.53em;
}

.emotion-0:focus {
  outline: none;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  cursor: default;
}

.emotion-0:hover {
  background-color: rgba(255, 255, 255, 0.25);
  border-color: transparent;
  color: #3f3f3f;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  background-color: #FFF;
  border-color: transparent;
  color: #CCC;
}

<button
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`<FixedButton /> snapshots renders secondary flat state correctly BananaRepublicFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 0;
  text-align: center;
  display: block;
  width: 100%;
  padding: 0.5em 0.8em;
  font-size: 1rem;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 700;
  color: #FFF;
  background-color: #000;
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  box-sizing: border-box;
  cursor: pointer;
  text-align: center;
  text-transform: uppercase;
  -webkit-transition: all 200ms ease;
  transition: all 200ms ease;
  height: 2.0625rem;
  background-color: transparent;
  border: 2px solid transparent;
  color: #0466CA;
  font-size: 1rem;
  font-weight: 600;
  letter-spacing: normal;
  line-height: 1.5;
  padding: 0.5rem;
}

.emotion-0:focus {
  outline: none;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  cursor: default;
}

.emotion-0:hover {
  background-color: #FFF;
  border-color: transparent;
  color: #428cd7;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  background-color: #FFF;
  border-color: transparent;
  color: #CCC;
}

<button
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`<FixedButton /> snapshots renders secondary flat state correctly Gap 1`] = `
<DocumentFragment>
  .emotion-0 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 1px solid transparent;
  border-radius: 0;
  font-size: 16px;
  font-weight: 400;
  letter-spacing: normal;
  min-height: 45px;
  max-height: auto;
  line-height: 1.125;
  padding: 12px 20px 11px;
  width: 100%;
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #FFF;
  background-color: #002554;
  border-color: #002554;
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  box-sizing: border-box;
  cursor: pointer;
  text-align: center;
  text-transform: uppercase;
  -webkit-transition: all 200ms ease;
  transition: all 200ms ease;
  height: 2.0625rem;
  background-color: transparent;
  border: none;
  color: #0A5694;
  font-size: 1.0625rem;
  font-weight: 700;
  letter-spacing: 1px;
  line-height: normal;
  padding: 0.59em;
}

.emotion-0:focus {
  outline: none;
}

.emotion-0>span {
  padding: 1px 0;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  cursor: default;
}

.emotion-0:hover {
  background-color: rgba(255, 255, 255, 0.25);
  border-color: transparent;
  color: #4780ae;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  background-color: #FFF;
  border-color: transparent;
  color: #CCC;
}

<button
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`<FixedButton /> snapshots renders secondary flat state correctly Gap in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 0;
  text-align: center;
  display: block;
  width: 100%;
  padding: 0.5em 0.8em;
  font-size: 1rem;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 700;
  color: #FFF;
  background-color: #000;
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  box-sizing: border-box;
  cursor: pointer;
  text-align: center;
  text-transform: uppercase;
  -webkit-transition: all 200ms ease;
  transition: all 200ms ease;
  height: 2.0625rem;
  background-color: transparent;
  border: 2px solid transparent;
  color: #0466CA;
  font-size: 1rem;
  font-weight: 600;
  letter-spacing: normal;
  line-height: 1.5;
  padding: 0.5rem;
}

.emotion-0:focus {
  outline: none;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  cursor: default;
}

.emotion-0:hover {
  background-color: #FFF;
  border-color: transparent;
  color: #428cd7;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  background-color: #FFF;
  border-color: transparent;
  color: #CCC;
}

<button
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`<FixedButton /> snapshots renders secondary flat state correctly GapFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 1px solid transparent;
  border-radius: 0;
  font-size: 16px;
  font-weight: 400;
  letter-spacing: normal;
  min-height: 45px;
  max-height: auto;
  line-height: 1.125;
  padding: 12px 20px 11px;
  width: 100%;
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #FFF;
  background-color: #002554;
  border-color: #002554;
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  box-sizing: border-box;
  cursor: pointer;
  text-align: center;
  text-transform: uppercase;
  -webkit-transition: all 200ms ease;
  transition: all 200ms ease;
  height: 2.0625rem;
  background-color: transparent;
  border: none;
  color: #0A5694;
  font-size: 1.0625rem;
  font-weight: 700;
  letter-spacing: 1px;
  line-height: normal;
  padding: 0.59em;
}

.emotion-0:focus {
  outline: none;
}

.emotion-0>span {
  padding: 1px 0;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  cursor: default;
}

.emotion-0:hover {
  background-color: rgba(255, 255, 255, 0.25);
  border-color: transparent;
  color: #4780ae;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  background-color: #FFF;
  border-color: transparent;
  color: #CCC;
}

<button
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`<FixedButton /> snapshots renders secondary flat state correctly GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 0;
  text-align: center;
  display: block;
  width: 100%;
  padding: 0.5em 0.8em;
  font-size: 1rem;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 700;
  color: #FFF;
  background-color: #000;
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  box-sizing: border-box;
  cursor: pointer;
  text-align: center;
  text-transform: uppercase;
  -webkit-transition: all 200ms ease;
  transition: all 200ms ease;
  height: 2.0625rem;
  background-color: transparent;
  border: 2px solid transparent;
  color: #0466CA;
  font-size: 1rem;
  font-weight: 600;
  letter-spacing: normal;
  line-height: 1.5;
  padding: 0.5rem;
}

.emotion-0:focus {
  outline: none;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  cursor: default;
}

.emotion-0:hover {
  background-color: #FFF;
  border-color: transparent;
  color: #428cd7;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  background-color: #FFF;
  border-color: transparent;
  color: #CCC;
}

<button
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`<FixedButton /> snapshots renders secondary flat state correctly OldNavy 1`] = `
<DocumentFragment>
  .emotion-0 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 700;
  letter-spacing: 1.6800000000000002px;
  min-height: 44px;
  max-height: auto;
  line-height: 1.1428571428571428;
  padding: 12px 22px;
  width: 100%;
  font-family: 'Gotham','Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #FFF;
  background-color: #003764;
  border-color: #003764;
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  box-sizing: border-box;
  cursor: pointer;
  text-align: center;
  text-transform: uppercase;
  -webkit-transition: all 200ms ease;
  transition: all 200ms ease;
  height: 2.0625rem;
  background-color: transparent;
  border: none;
  color: #003764;
  font-size: 1.1875rem;
  font-weight: 700;
  letter-spacing: 1px;
  line-height: normal;
  padding: 0.45em;
}

.emotion-0:focus {
  outline: none;
}

.emotion-0>span {
  padding: 1px 0;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  cursor: default;
}

.emotion-0:hover {
  background-color: rgba(255, 255, 255, 0.25);
  border-color: transparent;
  color: #3f698a;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  background-color: #FFF;
  border-color: transparent;
  color: #CCC;
}

<button
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`<FixedButton /> snapshots renders secondary flat state correctly OldNavy in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 0;
  text-align: center;
  display: block;
  width: 100%;
  padding: 0.5em 0.8em;
  font-size: 1rem;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 700;
  color: #FFF;
  background-color: #000;
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  box-sizing: border-box;
  cursor: pointer;
  text-align: center;
  text-transform: uppercase;
  -webkit-transition: all 200ms ease;
  transition: all 200ms ease;
  height: 2.0625rem;
  background-color: transparent;
  border: 2px solid transparent;
  color: #0466CA;
  font-size: 1rem;
  font-weight: 600;
  letter-spacing: normal;
  line-height: 1.5;
  padding: 0.5rem;
}

.emotion-0:focus {
  outline: none;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  cursor: default;
}

.emotion-0:hover {
  background-color: #FFF;
  border-color: transparent;
  color: #428cd7;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  background-color: #FFF;
  border-color: transparent;
  color: #CCC;
}

<button
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`<FixedButton /> snapshots renders secondary outline state correctly Athleta 1`] = `
<DocumentFragment>
  .emotion-0 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 1.5px solid transparent;
  border-radius: 0;
  font-size: 16px;
  font-weight: 500;
  letter-spacing: 0px;
  min-height: auto;
  max-height: 60px;
  line-height: 1.25;
  padding: 22px 40px;
  width: 100%;
  font-family: 'Avenir Next','Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #FFF;
  background-color: #000;
  border-color: #000;
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  box-sizing: border-box;
  cursor: pointer;
  text-align: center;
  text-transform: uppercase;
  -webkit-transition: all 200ms ease;
  transition: all 200ms ease;
  height: 2.75rem;
  background-color: #FFF;
  border: 1px solid #333;
  color: #333;
  font-size: 1.1875rem;
  font-weight: 400;
  letter-spacing: 1px;
  line-height: normal;
  padding: 0.0625em 0.59em 0.125em;
}

.emotion-0:focus {
  outline: none;
}

.emotion-0>span {
  padding: 1px 0;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  cursor: default;
}

.emotion-0:hover {
  background-color: rgba(255, 255, 255, 0.25);
  border-color: #666;
  color: #666;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  background-color: #FFF;
  border-color: #CCC;
  color: #CCC;
}

<button
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`<FixedButton /> snapshots renders secondary outline state correctly Athleta in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 0;
  text-align: center;
  display: block;
  width: 100%;
  padding: 0.5em 0.8em;
  font-size: 1rem;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 700;
  color: #FFF;
  background-color: #000;
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  box-sizing: border-box;
  cursor: pointer;
  text-align: center;
  text-transform: uppercase;
  -webkit-transition: all 200ms ease;
  transition: all 200ms ease;
  height: 2.75rem;
  background-color: transparent;
  border: 2px solid #333;
  color: #333;
  font-size: 1rem;
  font-weight: 600;
  letter-spacing: normal;
  line-height: normal;
  padding: 0.5rem;
}

.emotion-0:focus {
  outline: none;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  cursor: default;
}

.emotion-0:hover {
  background-color: #FFF;
  border-color: #666;
  color: #666;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  background-color: #CCC;
  border-color: transparent;
  color: #FFF;
}

<button
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`<FixedButton /> snapshots renders secondary outline state correctly BananaRepublic 1`] = `
<DocumentFragment>
  .emotion-0 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 0;
  text-align: center;
  display: block;
  width: 100%;
  padding: 0.5em 0.8em;
  font-size: 1rem;
  font-family: 'Lynstone',Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 600;
  color: #FFF;
  background-color: #000;
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  box-sizing: border-box;
  cursor: pointer;
  text-align: center;
  text-transform: uppercase;
  -webkit-transition: all 200ms ease;
  transition: all 200ms ease;
  height: 2.75rem;
  background-color: transparent;
  border: 1px solid #000;
  color: #000;
  font-size: 1.0625rem;
  font-weight: 600;
  letter-spacing: 2px;
  line-height: normal;
  padding: 0.48em;
}

.emotion-0:focus {
  outline: none;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  cursor: default;
}

.emotion-0:hover {
  background-color: rgba(255, 255, 255, 0.25);
  border-color: #3f3f3f;
  color: #3f3f3f;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  background-color: #FFF;
  border-color: #CCC;
  color: #CCC;
}

<button
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`<FixedButton /> snapshots renders secondary outline state correctly BananaRepublic in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 0;
  text-align: center;
  display: block;
  width: 100%;
  padding: 0.5em 0.8em;
  font-size: 1rem;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 700;
  color: #FFF;
  background-color: #000;
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  box-sizing: border-box;
  cursor: pointer;
  text-align: center;
  text-transform: uppercase;
  -webkit-transition: all 200ms ease;
  transition: all 200ms ease;
  height: 2.75rem;
  background-color: transparent;
  border: 2px solid #333;
  color: #333;
  font-size: 1rem;
  font-weight: 600;
  letter-spacing: normal;
  line-height: normal;
  padding: 0.5rem;
}

.emotion-0:focus {
  outline: none;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  cursor: default;
}

.emotion-0:hover {
  background-color: #FFF;
  border-color: #666;
  color: #666;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  background-color: #CCC;
  border-color: transparent;
  color: #FFF;
}

<button
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`<FixedButton /> snapshots renders secondary outline state correctly BananaRepublicFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 0;
  text-align: center;
  display: block;
  width: 100%;
  padding: 0.5em 0.8em;
  font-size: 1rem;
  font-family: 'Lynstone',Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 700;
  color: #FFF;
  background-color: #000;
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  box-sizing: border-box;
  cursor: pointer;
  text-align: center;
  text-transform: uppercase;
  -webkit-transition: all 200ms ease;
  transition: all 200ms ease;
  height: 2.75rem;
  background-color: transparent;
  border: 1px solid #000;
  color: #000;
  font-size: 1.0625rem;
  font-weight: 600;
  letter-spacing: 2px;
  line-height: normal;
  padding: 0.48em;
}

.emotion-0:focus {
  outline: none;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  cursor: default;
}

.emotion-0:hover {
  background-color: rgba(255, 255, 255, 0.25);
  border-color: #3f3f3f;
  color: #3f3f3f;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  background-color: #FFF;
  border-color: #CCC;
  color: #CCC;
}

<button
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`<FixedButton /> snapshots renders secondary outline state correctly BananaRepublicFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 0;
  text-align: center;
  display: block;
  width: 100%;
  padding: 0.5em 0.8em;
  font-size: 1rem;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 700;
  color: #FFF;
  background-color: #000;
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  box-sizing: border-box;
  cursor: pointer;
  text-align: center;
  text-transform: uppercase;
  -webkit-transition: all 200ms ease;
  transition: all 200ms ease;
  height: 2.75rem;
  background-color: transparent;
  border: 2px solid #333;
  color: #333;
  font-size: 1rem;
  font-weight: 600;
  letter-spacing: normal;
  line-height: normal;
  padding: 0.5rem;
}

.emotion-0:focus {
  outline: none;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  cursor: default;
}

.emotion-0:hover {
  background-color: #FFF;
  border-color: #666;
  color: #666;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  background-color: #CCC;
  border-color: transparent;
  color: #FFF;
}

<button
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`<FixedButton /> snapshots renders secondary outline state correctly Gap 1`] = `
<DocumentFragment>
  .emotion-0 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 1px solid transparent;
  border-radius: 0;
  font-size: 16px;
  font-weight: 400;
  letter-spacing: normal;
  min-height: 45px;
  max-height: auto;
  line-height: 1.125;
  padding: 12px 20px 11px;
  width: 100%;
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #FFF;
  background-color: #002554;
  border-color: #002554;
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  box-sizing: border-box;
  cursor: pointer;
  text-align: center;
  text-transform: uppercase;
  -webkit-transition: all 200ms ease;
  transition: all 200ms ease;
  height: 2.75rem;
  background-color: transparent;
  border: 2px solid #0A5694;
  color: #0A5694;
  font-size: 1.0625rem;
  font-weight: 700;
  letter-spacing: 1px;
  line-height: normal;
  padding: 0.0625em 0.375em;
}

.emotion-0:focus {
  outline: none;
}

.emotion-0>span {
  padding: 1px 0;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  cursor: default;
}

.emotion-0:hover {
  background-color: rgba(255, 255, 255, 0.25);
  border-color: #4780ae;
  color: #4780ae;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  background-color: #FFF;
  border-color: #CCC;
  color: #CCC;
}

<button
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`<FixedButton /> snapshots renders secondary outline state correctly Gap in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 0;
  text-align: center;
  display: block;
  width: 100%;
  padding: 0.5em 0.8em;
  font-size: 1rem;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 700;
  color: #FFF;
  background-color: #000;
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  box-sizing: border-box;
  cursor: pointer;
  text-align: center;
  text-transform: uppercase;
  -webkit-transition: all 200ms ease;
  transition: all 200ms ease;
  height: 2.75rem;
  background-color: transparent;
  border: 2px solid #333;
  color: #333;
  font-size: 1rem;
  font-weight: 600;
  letter-spacing: normal;
  line-height: normal;
  padding: 0.5rem;
}

.emotion-0:focus {
  outline: none;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  cursor: default;
}

.emotion-0:hover {
  background-color: #FFF;
  border-color: #666;
  color: #666;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  background-color: #CCC;
  border-color: transparent;
  color: #FFF;
}

<button
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`<FixedButton /> snapshots renders secondary outline state correctly GapFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 1px solid transparent;
  border-radius: 0;
  font-size: 16px;
  font-weight: 400;
  letter-spacing: normal;
  min-height: 45px;
  max-height: auto;
  line-height: 1.125;
  padding: 12px 20px 11px;
  width: 100%;
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #FFF;
  background-color: #002554;
  border-color: #002554;
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  box-sizing: border-box;
  cursor: pointer;
  text-align: center;
  text-transform: uppercase;
  -webkit-transition: all 200ms ease;
  transition: all 200ms ease;
  height: 2.75rem;
  background-color: transparent;
  border: 2px solid #0A5694;
  color: #0A5694;
  font-size: 1.0625rem;
  font-weight: 700;
  letter-spacing: 1px;
  line-height: normal;
  padding: 0.0625em 0.375em;
}

.emotion-0:focus {
  outline: none;
}

.emotion-0>span {
  padding: 1px 0;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  cursor: default;
}

.emotion-0:hover {
  background-color: rgba(255, 255, 255, 0.25);
  border-color: #4780ae;
  color: #4780ae;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  background-color: #FFF;
  border-color: #CCC;
  color: #CCC;
}

<button
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`<FixedButton /> snapshots renders secondary outline state correctly GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 0;
  text-align: center;
  display: block;
  width: 100%;
  padding: 0.5em 0.8em;
  font-size: 1rem;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 700;
  color: #FFF;
  background-color: #000;
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  box-sizing: border-box;
  cursor: pointer;
  text-align: center;
  text-transform: uppercase;
  -webkit-transition: all 200ms ease;
  transition: all 200ms ease;
  height: 2.75rem;
  background-color: transparent;
  border: 2px solid #333;
  color: #333;
  font-size: 1rem;
  font-weight: 600;
  letter-spacing: normal;
  line-height: normal;
  padding: 0.5rem;
}

.emotion-0:focus {
  outline: none;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  cursor: default;
}

.emotion-0:hover {
  background-color: #FFF;
  border-color: #666;
  color: #666;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  background-color: #CCC;
  border-color: transparent;
  color: #FFF;
}

<button
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`<FixedButton /> snapshots renders secondary outline state correctly OldNavy 1`] = `
<DocumentFragment>
  .emotion-0 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 700;
  letter-spacing: 1.6800000000000002px;
  min-height: 44px;
  max-height: auto;
  line-height: 1.1428571428571428;
  padding: 12px 22px;
  width: 100%;
  font-family: 'Gotham','Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #FFF;
  background-color: #003764;
  border-color: #003764;
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  box-sizing: border-box;
  cursor: pointer;
  text-align: center;
  text-transform: uppercase;
  -webkit-transition: all 200ms ease;
  transition: all 200ms ease;
  height: 2.75rem;
  background-color: transparent;
  border: 2px solid #666;
  color: #666;
  font-size: 1.1875rem;
  font-weight: 700;
  letter-spacing: 1px;
  line-height: normal;
  padding: 0.5em;
}

.emotion-0:focus {
  outline: none;
}

.emotion-0>span {
  padding: 1px 0;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  cursor: default;
}

.emotion-0:hover {
  background-color: #FFF;
  border-color: #666;
  color: #8c8c8c;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  background-color: #CCC;
  border-color: transparent;
  color: #FFF;
}

<button
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`<FixedButton /> snapshots renders secondary outline state correctly OldNavy in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 0;
  text-align: center;
  display: block;
  width: 100%;
  padding: 0.5em 0.8em;
  font-size: 1rem;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 700;
  color: #FFF;
  background-color: #000;
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  box-sizing: border-box;
  cursor: pointer;
  text-align: center;
  text-transform: uppercase;
  -webkit-transition: all 200ms ease;
  transition: all 200ms ease;
  height: 2.75rem;
  background-color: transparent;
  border: 2px solid #333;
  color: #333;
  font-size: 1rem;
  font-weight: 600;
  letter-spacing: normal;
  line-height: normal;
  padding: 0.5rem;
}

.emotion-0:focus {
  outline: none;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  cursor: default;
}

.emotion-0:hover {
  background-color: #FFF;
  border-color: #666;
  color: #666;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  background-color: #CCC;
  border-color: transparent;
  color: #FFF;
}

<button
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`<FixedButton /> snapshots renders secondary state correctly Athleta 1`] = `
<DocumentFragment>
  .emotion-0 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 1.5px solid transparent;
  border-radius: 0;
  font-size: 16px;
  font-weight: 500;
  letter-spacing: 0px;
  min-height: auto;
  max-height: 60px;
  line-height: 1.25;
  padding: 22px 40px;
  width: 100%;
  font-family: 'Avenir Next','Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #FFF;
  background-color: #000;
  border-color: #000;
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  box-sizing: border-box;
  cursor: pointer;
  text-align: center;
  text-transform: uppercase;
  -webkit-transition: all 200ms ease;
  transition: all 200ms ease;
  height: 2.75rem;
  background-color: #FFF;
  border: 1px solid #333;
  color: #333;
  font-size: 1.1875rem;
  font-weight: 400;
  letter-spacing: 1px;
  line-height: normal;
  padding: 0.0625em 0.59em 0.125em;
}

.emotion-0:focus {
  outline: none;
}

.emotion-0>span {
  padding: 1px 0;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  cursor: default;
}

.emotion-0:hover {
  background-color: rgba(255, 255, 255, 0.25);
  border-color: #666;
  color: #666;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  background-color: #FFF;
  border-color: #CCC;
  color: #CCC;
}

<button
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`<FixedButton /> snapshots renders secondary state correctly Athleta in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 0;
  text-align: center;
  display: block;
  width: 100%;
  padding: 0.5em 0.8em;
  font-size: 1rem;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 700;
  color: #FFF;
  background-color: #000;
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  box-sizing: border-box;
  cursor: pointer;
  text-align: center;
  text-transform: uppercase;
  -webkit-transition: all 200ms ease;
  transition: all 200ms ease;
  height: 2.75rem;
  background-color: #333;
  border: 2px solid transparent;
  color: #FFF;
  font-size: 1rem;
  font-weight: 600;
  letter-spacing: normal;
  line-height: 1.5;
  padding: 0.5rem;
}

.emotion-0:focus {
  outline: none;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  cursor: default;
}

.emotion-0:hover {
  background-color: #666;
  border-color: transparent;
  color: #FFF;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  background-color: #CCC;
  border-color: transparent;
  color: #FFF;
}

<button
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`<FixedButton /> snapshots renders secondary state correctly BananaRepublic 1`] = `
<DocumentFragment>
  .emotion-0 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 0;
  text-align: center;
  display: block;
  width: 100%;
  padding: 0.5em 0.8em;
  font-size: 1rem;
  font-family: 'Lynstone',Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 600;
  color: #FFF;
  background-color: #000;
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  box-sizing: border-box;
  cursor: pointer;
  text-align: center;
  text-transform: uppercase;
  -webkit-transition: all 200ms ease;
  transition: all 200ms ease;
  height: 2.75rem;
  background-color: transparent;
  border: 1px solid #000;
  color: #000;
  font-size: 1.0625rem;
  font-weight: 600;
  letter-spacing: 2px;
  line-height: normal;
  padding: 0.48em;
}

.emotion-0:focus {
  outline: none;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  cursor: default;
}

.emotion-0:hover {
  background-color: rgba(255, 255, 255, 0.25);
  border-color: #3f3f3f;
  color: #3f3f3f;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  background-color: #FFF;
  border-color: #CCC;
  color: #CCC;
}

<button
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`<FixedButton /> snapshots renders secondary state correctly BananaRepublic in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 0;
  text-align: center;
  display: block;
  width: 100%;
  padding: 0.5em 0.8em;
  font-size: 1rem;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 700;
  color: #FFF;
  background-color: #000;
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  box-sizing: border-box;
  cursor: pointer;
  text-align: center;
  text-transform: uppercase;
  -webkit-transition: all 200ms ease;
  transition: all 200ms ease;
  height: 2.75rem;
  background-color: #333;
  border: 2px solid transparent;
  color: #FFF;
  font-size: 1rem;
  font-weight: 600;
  letter-spacing: normal;
  line-height: 1.5;
  padding: 0.5rem;
}

.emotion-0:focus {
  outline: none;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  cursor: default;
}

.emotion-0:hover {
  background-color: #666;
  border-color: transparent;
  color: #FFF;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  background-color: #CCC;
  border-color: transparent;
  color: #FFF;
}

<button
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`<FixedButton /> snapshots renders secondary state correctly BananaRepublicFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 0;
  text-align: center;
  display: block;
  width: 100%;
  padding: 0.5em 0.8em;
  font-size: 1rem;
  font-family: 'Lynstone',Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 700;
  color: #FFF;
  background-color: #000;
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  box-sizing: border-box;
  cursor: pointer;
  text-align: center;
  text-transform: uppercase;
  -webkit-transition: all 200ms ease;
  transition: all 200ms ease;
  height: 2.75rem;
  background-color: transparent;
  border: 1px solid #000;
  color: #000;
  font-size: 1.0625rem;
  font-weight: 600;
  letter-spacing: 2px;
  line-height: normal;
  padding: 0.48em;
}

.emotion-0:focus {
  outline: none;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  cursor: default;
}

.emotion-0:hover {
  background-color: rgba(255, 255, 255, 0.25);
  border-color: #3f3f3f;
  color: #3f3f3f;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  background-color: #FFF;
  border-color: #CCC;
  color: #CCC;
}

<button
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`<FixedButton /> snapshots renders secondary state correctly BananaRepublicFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 0;
  text-align: center;
  display: block;
  width: 100%;
  padding: 0.5em 0.8em;
  font-size: 1rem;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 700;
  color: #FFF;
  background-color: #000;
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  box-sizing: border-box;
  cursor: pointer;
  text-align: center;
  text-transform: uppercase;
  -webkit-transition: all 200ms ease;
  transition: all 200ms ease;
  height: 2.75rem;
  background-color: #333;
  border: 2px solid transparent;
  color: #FFF;
  font-size: 1rem;
  font-weight: 600;
  letter-spacing: normal;
  line-height: 1.5;
  padding: 0.5rem;
}

.emotion-0:focus {
  outline: none;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  cursor: default;
}

.emotion-0:hover {
  background-color: #666;
  border-color: transparent;
  color: #FFF;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  background-color: #CCC;
  border-color: transparent;
  color: #FFF;
}

<button
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`<FixedButton /> snapshots renders secondary state correctly Gap 1`] = `
<DocumentFragment>
  .emotion-0 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 1px solid transparent;
  border-radius: 0;
  font-size: 16px;
  font-weight: 400;
  letter-spacing: normal;
  min-height: 45px;
  max-height: auto;
  line-height: 1.125;
  padding: 12px 20px 11px;
  width: 100%;
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #FFF;
  background-color: #002554;
  border-color: #002554;
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  box-sizing: border-box;
  cursor: pointer;
  text-align: center;
  text-transform: uppercase;
  -webkit-transition: all 200ms ease;
  transition: all 200ms ease;
  height: 2.75rem;
  background-color: transparent;
  border: 2px solid #0A5694;
  color: #0A5694;
  font-size: 1.0625rem;
  font-weight: 700;
  letter-spacing: 1px;
  line-height: normal;
  padding: 0.0625em 0.59em 0.125em;
}

.emotion-0:focus {
  outline: none;
}

.emotion-0>span {
  padding: 1px 0;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  cursor: default;
}

.emotion-0:hover {
  background-color: rgba(255, 255, 255, 0.25);
  border-color: #4780ae;
  color: #4780ae;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  background-color: #FFF;
  border-color: #CCC;
  color: #CCC;
}

<button
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`<FixedButton /> snapshots renders secondary state correctly Gap in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 0;
  text-align: center;
  display: block;
  width: 100%;
  padding: 0.5em 0.8em;
  font-size: 1rem;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 700;
  color: #FFF;
  background-color: #000;
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  box-sizing: border-box;
  cursor: pointer;
  text-align: center;
  text-transform: uppercase;
  -webkit-transition: all 200ms ease;
  transition: all 200ms ease;
  height: 2.75rem;
  background-color: #333;
  border: 2px solid transparent;
  color: #FFF;
  font-size: 1rem;
  font-weight: 600;
  letter-spacing: normal;
  line-height: 1.5;
  padding: 0.5rem;
}

.emotion-0:focus {
  outline: none;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  cursor: default;
}

.emotion-0:hover {
  background-color: #666;
  border-color: transparent;
  color: #FFF;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  background-color: #CCC;
  border-color: transparent;
  color: #FFF;
}

<button
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`<FixedButton /> snapshots renders secondary state correctly GapFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 1px solid transparent;
  border-radius: 0;
  font-size: 16px;
  font-weight: 400;
  letter-spacing: normal;
  min-height: 45px;
  max-height: auto;
  line-height: 1.125;
  padding: 12px 20px 11px;
  width: 100%;
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #FFF;
  background-color: #002554;
  border-color: #002554;
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  box-sizing: border-box;
  cursor: pointer;
  text-align: center;
  text-transform: uppercase;
  -webkit-transition: all 200ms ease;
  transition: all 200ms ease;
  height: 2.75rem;
  background-color: transparent;
  border: 2px solid #0A5694;
  color: #0A5694;
  font-size: 1.0625rem;
  font-weight: 700;
  letter-spacing: 1px;
  line-height: normal;
  padding: 0.0625em 0.59em 0.125em;
}

.emotion-0:focus {
  outline: none;
}

.emotion-0>span {
  padding: 1px 0;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  cursor: default;
}

.emotion-0:hover {
  background-color: rgba(255, 255, 255, 0.25);
  border-color: #4780ae;
  color: #4780ae;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  background-color: #FFF;
  border-color: #CCC;
  color: #CCC;
}

<button
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`<FixedButton /> snapshots renders secondary state correctly GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 0;
  text-align: center;
  display: block;
  width: 100%;
  padding: 0.5em 0.8em;
  font-size: 1rem;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 700;
  color: #FFF;
  background-color: #000;
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  box-sizing: border-box;
  cursor: pointer;
  text-align: center;
  text-transform: uppercase;
  -webkit-transition: all 200ms ease;
  transition: all 200ms ease;
  height: 2.75rem;
  background-color: #333;
  border: 2px solid transparent;
  color: #FFF;
  font-size: 1rem;
  font-weight: 600;
  letter-spacing: normal;
  line-height: 1.5;
  padding: 0.5rem;
}

.emotion-0:focus {
  outline: none;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  cursor: default;
}

.emotion-0:hover {
  background-color: #666;
  border-color: transparent;
  color: #FFF;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  background-color: #CCC;
  border-color: transparent;
  color: #FFF;
}

<button
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`<FixedButton /> snapshots renders secondary state correctly OldNavy 1`] = `
<DocumentFragment>
  .emotion-0 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 700;
  letter-spacing: 1.6800000000000002px;
  min-height: 44px;
  max-height: auto;
  line-height: 1.1428571428571428;
  padding: 12px 22px;
  width: 100%;
  font-family: 'Gotham','Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #FFF;
  background-color: #003764;
  border-color: #003764;
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  box-sizing: border-box;
  cursor: pointer;
  text-align: center;
  text-transform: uppercase;
  -webkit-transition: all 200ms ease;
  transition: all 200ms ease;
  height: 2.75rem;
  background-color: #666;
  border: none;
  color: #FFF;
  font-size: 1.1875rem;
  font-weight: 700;
  letter-spacing: 1px;
  line-height: normal;
  padding: 0.5em;
}

.emotion-0:focus {
  outline: none;
}

.emotion-0>span {
  padding: 1px 0;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  cursor: default;
}

.emotion-0:hover {
  background-color: #8c8c8c;
  border-color: transparent;
  color: #FFF;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  background-color: #CCC;
  border-color: transparent;
  color: #FFF;
}

<button
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`<FixedButton /> snapshots renders secondary state correctly OldNavy in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 0;
  text-align: center;
  display: block;
  width: 100%;
  padding: 0.5em 0.8em;
  font-size: 1rem;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 700;
  color: #FFF;
  background-color: #000;
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  box-sizing: border-box;
  cursor: pointer;
  text-align: center;
  text-transform: uppercase;
  -webkit-transition: all 200ms ease;
  transition: all 200ms ease;
  height: 2.75rem;
  background-color: #333;
  border: 2px solid transparent;
  color: #FFF;
  font-size: 1rem;
  font-weight: 600;
  letter-spacing: normal;
  line-height: 1.5;
  padding: 0.5rem;
}

.emotion-0:focus {
  outline: none;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  cursor: default;
}

.emotion-0:hover {
  background-color: #666;
  border-color: transparent;
  color: #FFF;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  background-color: #CCC;
  border-color: transparent;
  color: #FFF;
}

<button
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`<FixedButton /> snapshots renders tertiary flat state correctly Athleta 1`] = `
<DocumentFragment>
  .emotion-0 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 1.5px solid transparent;
  border-radius: 0;
  font-size: 16px;
  font-weight: 500;
  letter-spacing: 0px;
  min-height: auto;
  max-height: 60px;
  line-height: 1.25;
  padding: 22px 40px;
  width: 100%;
  font-family: 'Avenir Next','Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #FFF;
  background-color: #000;
  border-color: #000;
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  box-sizing: border-box;
  cursor: pointer;
  text-align: center;
  text-transform: uppercase;
  -webkit-transition: all 200ms ease;
  transition: all 200ms ease;
  height: 2.0625rem;
  background-color: transparent;
  border: none;
  color: #333;
  font-size: 81.25%;
  font-weight: 600;
  letter-spacing: 0.1em;
  line-height: normal;
  padding: 0.25rem 1rem;
}

.emotion-0:focus {
  outline: none;
}

.emotion-0>span {
  padding: 1px 0;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  cursor: default;
}

.emotion-0:hover {
  background-color: rgba(255, 255, 255, 0.25);
  border-color: transparent;
  color: #666;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  background-color: #FFF;
  border-color: transparent;
  color: #CCC;
}

<button
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`<FixedButton /> snapshots renders tertiary flat state correctly Athleta in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 0;
  text-align: center;
  display: block;
  width: 100%;
  padding: 0.5em 0.8em;
  font-size: 1rem;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 700;
  color: #FFF;
  background-color: #000;
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  box-sizing: border-box;
  cursor: pointer;
  text-align: center;
  text-transform: uppercase;
  -webkit-transition: all 200ms ease;
  transition: all 200ms ease;
  height: 2.0625rem;
  background-color: transparent;
  border: 2px solid transparent;
  color: #0466CA;
  font-size: 0.8125em;
  font-weight: 600;
  letter-spacing: normal;
  line-height: 1.5;
  padding: 0.1rem;
}

.emotion-0:focus {
  outline: none;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  cursor: default;
}

.emotion-0:hover {
  background-color: rgba(255, 255, 255, 0.25);
  border-color: transparent;
  color: #428cd7;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  background-color: #FFF;
  border-color: transparent;
  color: #CCC;
}

<button
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`<FixedButton /> snapshots renders tertiary flat state correctly BananaRepublic 1`] = `
<DocumentFragment>
  .emotion-0 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 0;
  text-align: center;
  display: block;
  width: 100%;
  padding: 0.5em 0.8em;
  font-size: 1rem;
  font-family: 'Lynstone',Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 600;
  color: #FFF;
  background-color: #000;
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  box-sizing: border-box;
  cursor: pointer;
  text-align: center;
  text-transform: uppercase;
  -webkit-transition: all 200ms ease;
  transition: all 200ms ease;
  height: 2.0625rem;
  background-color: transparent;
  border: none;
  color: #000;
  font-size: 0.9375rem;
  font-weight: 400;
  letter-spacing: 0.1em;
  line-height: normal;
  padding: 0.25rem 1rem;
}

.emotion-0:focus {
  outline: none;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  cursor: default;
}

.emotion-0:hover {
  background-color: rgba(255, 255, 255, 0.25);
  border-color: transparent;
  color: #3f3f3f;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  background-color: #FFF;
  border-color: transparent;
  color: #CCC;
}

<button
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`<FixedButton /> snapshots renders tertiary flat state correctly BananaRepublic in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 0;
  text-align: center;
  display: block;
  width: 100%;
  padding: 0.5em 0.8em;
  font-size: 1rem;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 700;
  color: #FFF;
  background-color: #000;
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  box-sizing: border-box;
  cursor: pointer;
  text-align: center;
  text-transform: uppercase;
  -webkit-transition: all 200ms ease;
  transition: all 200ms ease;
  height: 2.0625rem;
  background-color: transparent;
  border: 2px solid transparent;
  color: #0466CA;
  font-size: 0.8125em;
  font-weight: 600;
  letter-spacing: normal;
  line-height: 1.5;
  padding: 0.1rem;
}

.emotion-0:focus {
  outline: none;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  cursor: default;
}

.emotion-0:hover {
  background-color: rgba(255, 255, 255, 0.25);
  border-color: transparent;
  color: #428cd7;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  background-color: #FFF;
  border-color: transparent;
  color: #CCC;
}

<button
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`<FixedButton /> snapshots renders tertiary flat state correctly BananaRepublicFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 0;
  text-align: center;
  display: block;
  width: 100%;
  padding: 0.5em 0.8em;
  font-size: 1rem;
  font-family: 'Lynstone',Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 700;
  color: #FFF;
  background-color: #000;
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  box-sizing: border-box;
  cursor: pointer;
  text-align: center;
  text-transform: uppercase;
  -webkit-transition: all 200ms ease;
  transition: all 200ms ease;
  height: 2.0625rem;
  background-color: transparent;
  border: none;
  color: #000;
  font-size: 0.9375rem;
  font-weight: 400;
  letter-spacing: 0.1em;
  line-height: normal;
  padding: 0.25rem 1rem;
}

.emotion-0:focus {
  outline: none;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  cursor: default;
}

.emotion-0:hover {
  background-color: rgba(255, 255, 255, 0.25);
  border-color: transparent;
  color: #3f3f3f;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  background-color: #FFF;
  border-color: transparent;
  color: #CCC;
}

<button
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`<FixedButton /> snapshots renders tertiary flat state correctly BananaRepublicFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 0;
  text-align: center;
  display: block;
  width: 100%;
  padding: 0.5em 0.8em;
  font-size: 1rem;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 700;
  color: #FFF;
  background-color: #000;
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  box-sizing: border-box;
  cursor: pointer;
  text-align: center;
  text-transform: uppercase;
  -webkit-transition: all 200ms ease;
  transition: all 200ms ease;
  height: 2.0625rem;
  background-color: transparent;
  border: 2px solid transparent;
  color: #0466CA;
  font-size: 0.8125em;
  font-weight: 600;
  letter-spacing: normal;
  line-height: 1.5;
  padding: 0.1rem;
}

.emotion-0:focus {
  outline: none;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  cursor: default;
}

.emotion-0:hover {
  background-color: rgba(255, 255, 255, 0.25);
  border-color: transparent;
  color: #428cd7;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  background-color: #FFF;
  border-color: transparent;
  color: #CCC;
}

<button
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`<FixedButton /> snapshots renders tertiary flat state correctly Gap 1`] = `
<DocumentFragment>
  .emotion-0 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 1px solid transparent;
  border-radius: 0;
  font-size: 16px;
  font-weight: 400;
  letter-spacing: normal;
  min-height: 45px;
  max-height: auto;
  line-height: 1.125;
  padding: 12px 20px 11px;
  width: 100%;
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #FFF;
  background-color: #002554;
  border-color: #002554;
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  box-sizing: border-box;
  cursor: pointer;
  text-align: center;
  text-transform: uppercase;
  -webkit-transition: all 200ms ease;
  transition: all 200ms ease;
  height: 2.0625rem;
  background-color: transparent;
  border: none;
  color: #0A5694;
  font-size: 81.25%;
  font-weight: 400;
  letter-spacing: 0.1em;
  line-height: normal;
  padding: 0.45rem 1rem;
}

.emotion-0:focus {
  outline: none;
}

.emotion-0>span {
  padding: 1px 0;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  cursor: default;
}

.emotion-0:hover {
  background-color: rgba(255, 255, 255, 0.25);
  border-color: transparent;
  color: #4780ae;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  background-color: #FFF;
  border-color: transparent;
  color: #CCC;
}

<button
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`<FixedButton /> snapshots renders tertiary flat state correctly Gap in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 0;
  text-align: center;
  display: block;
  width: 100%;
  padding: 0.5em 0.8em;
  font-size: 1rem;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 700;
  color: #FFF;
  background-color: #000;
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  box-sizing: border-box;
  cursor: pointer;
  text-align: center;
  text-transform: uppercase;
  -webkit-transition: all 200ms ease;
  transition: all 200ms ease;
  height: 2.0625rem;
  background-color: transparent;
  border: 2px solid transparent;
  color: #0466CA;
  font-size: 0.8125em;
  font-weight: 600;
  letter-spacing: normal;
  line-height: 1.5;
  padding: 0.1rem;
}

.emotion-0:focus {
  outline: none;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  cursor: default;
}

.emotion-0:hover {
  background-color: rgba(255, 255, 255, 0.25);
  border-color: transparent;
  color: #428cd7;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  background-color: #FFF;
  border-color: transparent;
  color: #CCC;
}

<button
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`<FixedButton /> snapshots renders tertiary flat state correctly GapFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 1px solid transparent;
  border-radius: 0;
  font-size: 16px;
  font-weight: 400;
  letter-spacing: normal;
  min-height: 45px;
  max-height: auto;
  line-height: 1.125;
  padding: 12px 20px 11px;
  width: 100%;
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #FFF;
  background-color: #002554;
  border-color: #002554;
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  box-sizing: border-box;
  cursor: pointer;
  text-align: center;
  text-transform: uppercase;
  -webkit-transition: all 200ms ease;
  transition: all 200ms ease;
  height: 2.0625rem;
  background-color: transparent;
  border: none;
  color: #0A5694;
  font-size: 81.25%;
  font-weight: 400;
  letter-spacing: 0.1em;
  line-height: normal;
  padding: 0.45rem 1rem;
}

.emotion-0:focus {
  outline: none;
}

.emotion-0>span {
  padding: 1px 0;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  cursor: default;
}

.emotion-0:hover {
  background-color: rgba(255, 255, 255, 0.25);
  border-color: transparent;
  color: #4780ae;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  background-color: #FFF;
  border-color: transparent;
  color: #CCC;
}

<button
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`<FixedButton /> snapshots renders tertiary flat state correctly GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 0;
  text-align: center;
  display: block;
  width: 100%;
  padding: 0.5em 0.8em;
  font-size: 1rem;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 700;
  color: #FFF;
  background-color: #000;
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  box-sizing: border-box;
  cursor: pointer;
  text-align: center;
  text-transform: uppercase;
  -webkit-transition: all 200ms ease;
  transition: all 200ms ease;
  height: 2.0625rem;
  background-color: transparent;
  border: 2px solid transparent;
  color: #0466CA;
  font-size: 0.8125em;
  font-weight: 600;
  letter-spacing: normal;
  line-height: 1.5;
  padding: 0.1rem;
}

.emotion-0:focus {
  outline: none;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  cursor: default;
}

.emotion-0:hover {
  background-color: rgba(255, 255, 255, 0.25);
  border-color: transparent;
  color: #428cd7;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  background-color: #FFF;
  border-color: transparent;
  color: #CCC;
}

<button
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`<FixedButton /> snapshots renders tertiary flat state correctly OldNavy 1`] = `
<DocumentFragment>
  .emotion-0 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 700;
  letter-spacing: 1.6800000000000002px;
  min-height: 44px;
  max-height: auto;
  line-height: 1.1428571428571428;
  padding: 12px 22px;
  width: 100%;
  font-family: 'Gotham','Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #FFF;
  background-color: #003764;
  border-color: #003764;
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  box-sizing: border-box;
  cursor: pointer;
  text-align: center;
  text-transform: uppercase;
  -webkit-transition: all 200ms ease;
  transition: all 200ms ease;
  height: 2.0625rem;
  background-color: transparent;
  border: none;
  color: #003764;
  font-size: 81.25%;
  font-weight: 700;
  letter-spacing: 0.1em;
  line-height: normal;
  padding: 0.25rem 1rem;
}

.emotion-0:focus {
  outline: none;
}

.emotion-0>span {
  padding: 1px 0;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  cursor: default;
}

.emotion-0:hover {
  background-color: rgba(255, 255, 255, 0.25);
  border-color: transparent;
  color: #3f698a;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  background-color: #FFF;
  border-color: transparent;
  color: #CCC;
}

<button
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`<FixedButton /> snapshots renders tertiary flat state correctly OldNavy in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 0;
  text-align: center;
  display: block;
  width: 100%;
  padding: 0.5em 0.8em;
  font-size: 1rem;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 700;
  color: #FFF;
  background-color: #000;
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  box-sizing: border-box;
  cursor: pointer;
  text-align: center;
  text-transform: uppercase;
  -webkit-transition: all 200ms ease;
  transition: all 200ms ease;
  height: 2.0625rem;
  background-color: transparent;
  border: 2px solid transparent;
  color: #0466CA;
  font-size: 0.8125em;
  font-weight: 600;
  letter-spacing: normal;
  line-height: 1.5;
  padding: 0.1rem;
}

.emotion-0:focus {
  outline: none;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  cursor: default;
}

.emotion-0:hover {
  background-color: rgba(255, 255, 255, 0.25);
  border-color: transparent;
  color: #428cd7;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  background-color: #FFF;
  border-color: transparent;
  color: #CCC;
}

<button
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`<FixedButton /> snapshots renders tertiary outline state correctly Athleta 1`] = `
<DocumentFragment>
  .emotion-0 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 1.5px solid transparent;
  border-radius: 0;
  font-size: 16px;
  font-weight: 500;
  letter-spacing: 0px;
  min-height: auto;
  max-height: 60px;
  line-height: 1.25;
  padding: 22px 40px;
  width: 100%;
  font-family: 'Avenir Next','Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #FFF;
  background-color: #000;
  border-color: #000;
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  box-sizing: border-box;
  cursor: pointer;
  text-align: center;
  text-transform: uppercase;
  -webkit-transition: all 200ms ease;
  transition: all 200ms ease;
  height: 2.0625rem;
  background-color: transparent;
  border: 1px solid #333;
  color: #333;
  font-size: 81.25%;
  font-weight: 600;
  letter-spacing: normal;
  line-height: normal;
  padding: 0.1rem;
}

.emotion-0:focus {
  outline: none;
}

.emotion-0>span {
  padding: 1px 0;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  cursor: default;
}

.emotion-0:hover {
  background-color: rgba(255, 255, 255, 0.25);
  border-color: #666;
  color: #666;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  background-color: #FFF;
  border-color: #CCC;
  color: #CCC;
}

<button
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`<FixedButton /> snapshots renders tertiary outline state correctly Athleta in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 0;
  text-align: center;
  display: block;
  width: 100%;
  padding: 0.5em 0.8em;
  font-size: 1rem;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 700;
  color: #FFF;
  background-color: #000;
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  box-sizing: border-box;
  cursor: pointer;
  text-align: center;
  text-transform: uppercase;
  -webkit-transition: all 200ms ease;
  transition: all 200ms ease;
  height: 2.0625rem;
  background-color: transparent;
  border: 2px solid #333;
  color: #333;
  font-size: 0.8125rem;
  font-weight: 600;
  letter-spacing: normal;
  line-height: normal;
  padding: 0.1rem;
}

.emotion-0:focus {
  outline: none;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  cursor: default;
}

.emotion-0:hover {
  background-color: #FFF;
  border-color: #666;
  color: #666;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  background-color: #CCC;
  border-color: transparent;
  color: #FFF;
}

<button
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`<FixedButton /> snapshots renders tertiary outline state correctly BananaRepublic 1`] = `
<DocumentFragment>
  .emotion-0 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 0;
  text-align: center;
  display: block;
  width: 100%;
  padding: 0.5em 0.8em;
  font-size: 1rem;
  font-family: 'Lynstone',Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 600;
  color: #FFF;
  background-color: #000;
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  box-sizing: border-box;
  cursor: pointer;
  text-align: center;
  text-transform: uppercase;
  -webkit-transition: all 200ms ease;
  transition: all 200ms ease;
  height: 2.0625rem;
  background-color: transparent;
  border: 1px solid #000;
  color: #000;
  font-size: 93.75%;
  font-weight: 400;
  letter-spacing: normal;
  line-height: normal;
  padding: 0.1rem;
}

.emotion-0:focus {
  outline: none;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  cursor: default;
}

.emotion-0:hover {
  background-color: rgba(255, 255, 255, 0.25);
  border-color: #3f3f3f;
  color: #3f3f3f;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  background-color: #FFF;
  border-color: #CCC;
  color: #CCC;
}

<button
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`<FixedButton /> snapshots renders tertiary outline state correctly BananaRepublic in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 0;
  text-align: center;
  display: block;
  width: 100%;
  padding: 0.5em 0.8em;
  font-size: 1rem;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 700;
  color: #FFF;
  background-color: #000;
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  box-sizing: border-box;
  cursor: pointer;
  text-align: center;
  text-transform: uppercase;
  -webkit-transition: all 200ms ease;
  transition: all 200ms ease;
  height: 2.0625rem;
  background-color: transparent;
  border: 2px solid #333;
  color: #333;
  font-size: 0.8125rem;
  font-weight: 600;
  letter-spacing: normal;
  line-height: normal;
  padding: 0.1rem;
}

.emotion-0:focus {
  outline: none;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  cursor: default;
}

.emotion-0:hover {
  background-color: #FFF;
  border-color: #666;
  color: #666;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  background-color: #CCC;
  border-color: transparent;
  color: #FFF;
}

<button
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`<FixedButton /> snapshots renders tertiary outline state correctly BananaRepublicFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 0;
  text-align: center;
  display: block;
  width: 100%;
  padding: 0.5em 0.8em;
  font-size: 1rem;
  font-family: 'Lynstone',Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 700;
  color: #FFF;
  background-color: #000;
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  box-sizing: border-box;
  cursor: pointer;
  text-align: center;
  text-transform: uppercase;
  -webkit-transition: all 200ms ease;
  transition: all 200ms ease;
  height: 2.0625rem;
  background-color: transparent;
  border: 1px solid #000;
  color: #000;
  font-size: 93.75%;
  font-weight: 400;
  letter-spacing: normal;
  line-height: normal;
  padding: 0.1rem;
}

.emotion-0:focus {
  outline: none;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  cursor: default;
}

.emotion-0:hover {
  background-color: rgba(255, 255, 255, 0.25);
  border-color: #3f3f3f;
  color: #3f3f3f;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  background-color: #FFF;
  border-color: #CCC;
  color: #CCC;
}

<button
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`<FixedButton /> snapshots renders tertiary outline state correctly BananaRepublicFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 0;
  text-align: center;
  display: block;
  width: 100%;
  padding: 0.5em 0.8em;
  font-size: 1rem;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 700;
  color: #FFF;
  background-color: #000;
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  box-sizing: border-box;
  cursor: pointer;
  text-align: center;
  text-transform: uppercase;
  -webkit-transition: all 200ms ease;
  transition: all 200ms ease;
  height: 2.0625rem;
  background-color: transparent;
  border: 2px solid #333;
  color: #333;
  font-size: 0.8125rem;
  font-weight: 600;
  letter-spacing: normal;
  line-height: normal;
  padding: 0.1rem;
}

.emotion-0:focus {
  outline: none;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  cursor: default;
}

.emotion-0:hover {
  background-color: #FFF;
  border-color: #666;
  color: #666;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  background-color: #CCC;
  border-color: transparent;
  color: #FFF;
}

<button
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`<FixedButton /> snapshots renders tertiary outline state correctly Gap 1`] = `
<DocumentFragment>
  .emotion-0 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 1px solid transparent;
  border-radius: 0;
  font-size: 16px;
  font-weight: 400;
  letter-spacing: normal;
  min-height: 45px;
  max-height: auto;
  line-height: 1.125;
  padding: 12px 20px 11px;
  width: 100%;
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #FFF;
  background-color: #002554;
  border-color: #002554;
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  box-sizing: border-box;
  cursor: pointer;
  text-align: center;
  text-transform: uppercase;
  -webkit-transition: all 200ms ease;
  transition: all 200ms ease;
  height: 2.0625rem;
  background-color: transparent;
  border: 2px solid #0A5694;
  color: #0A5694;
  font-size: 81.25%;
  font-weight: 700;
  letter-spacing: normal;
  line-height: normal;
  padding: 0.1rem;
}

.emotion-0:focus {
  outline: none;
}

.emotion-0>span {
  padding: 1px 0;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  cursor: default;
}

.emotion-0:hover {
  background-color: rgba(255, 255, 255, 0.25);
  border-color: #4780ae;
  color: #4780ae;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  background-color: #FFF;
  border-color: #CCC;
  color: #CCC;
}

<button
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`<FixedButton /> snapshots renders tertiary outline state correctly Gap in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 0;
  text-align: center;
  display: block;
  width: 100%;
  padding: 0.5em 0.8em;
  font-size: 1rem;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 700;
  color: #FFF;
  background-color: #000;
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  box-sizing: border-box;
  cursor: pointer;
  text-align: center;
  text-transform: uppercase;
  -webkit-transition: all 200ms ease;
  transition: all 200ms ease;
  height: 2.0625rem;
  background-color: transparent;
  border: 2px solid #333;
  color: #333;
  font-size: 0.8125rem;
  font-weight: 600;
  letter-spacing: normal;
  line-height: normal;
  padding: 0.1rem;
}

.emotion-0:focus {
  outline: none;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  cursor: default;
}

.emotion-0:hover {
  background-color: #FFF;
  border-color: #666;
  color: #666;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  background-color: #CCC;
  border-color: transparent;
  color: #FFF;
}

<button
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`<FixedButton /> snapshots renders tertiary outline state correctly GapFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 1px solid transparent;
  border-radius: 0;
  font-size: 16px;
  font-weight: 400;
  letter-spacing: normal;
  min-height: 45px;
  max-height: auto;
  line-height: 1.125;
  padding: 12px 20px 11px;
  width: 100%;
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #FFF;
  background-color: #002554;
  border-color: #002554;
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  box-sizing: border-box;
  cursor: pointer;
  text-align: center;
  text-transform: uppercase;
  -webkit-transition: all 200ms ease;
  transition: all 200ms ease;
  height: 2.0625rem;
  background-color: transparent;
  border: 2px solid #0A5694;
  color: #0A5694;
  font-size: 81.25%;
  font-weight: 700;
  letter-spacing: normal;
  line-height: normal;
  padding: 0.1rem;
}

.emotion-0:focus {
  outline: none;
}

.emotion-0>span {
  padding: 1px 0;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  cursor: default;
}

.emotion-0:hover {
  background-color: rgba(255, 255, 255, 0.25);
  border-color: #4780ae;
  color: #4780ae;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  background-color: #FFF;
  border-color: #CCC;
  color: #CCC;
}

<button
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`<FixedButton /> snapshots renders tertiary outline state correctly GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 0;
  text-align: center;
  display: block;
  width: 100%;
  padding: 0.5em 0.8em;
  font-size: 1rem;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 700;
  color: #FFF;
  background-color: #000;
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  box-sizing: border-box;
  cursor: pointer;
  text-align: center;
  text-transform: uppercase;
  -webkit-transition: all 200ms ease;
  transition: all 200ms ease;
  height: 2.0625rem;
  background-color: transparent;
  border: 2px solid #333;
  color: #333;
  font-size: 0.8125rem;
  font-weight: 600;
  letter-spacing: normal;
  line-height: normal;
  padding: 0.1rem;
}

.emotion-0:focus {
  outline: none;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  cursor: default;
}

.emotion-0:hover {
  background-color: #FFF;
  border-color: #666;
  color: #666;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  background-color: #CCC;
  border-color: transparent;
  color: #FFF;
}

<button
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`<FixedButton /> snapshots renders tertiary outline state correctly OldNavy 1`] = `
<DocumentFragment>
  .emotion-0 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 700;
  letter-spacing: 1.6800000000000002px;
  min-height: 44px;
  max-height: auto;
  line-height: 1.1428571428571428;
  padding: 12px 22px;
  width: 100%;
  font-family: 'Gotham','Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #FFF;
  background-color: #003764;
  border-color: #003764;
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  box-sizing: border-box;
  cursor: pointer;
  text-align: center;
  text-transform: uppercase;
  -webkit-transition: all 200ms ease;
  transition: all 200ms ease;
  height: 2.0625rem;
  background-color: transparent;
  border: 2px solid #003764;
  color: #003764;
  font-size: 87.5%;
  font-weight: 700;
  letter-spacing: normal;
  line-height: normal;
  padding: 0.1rem;
}

.emotion-0:focus {
  outline: none;
}

.emotion-0>span {
  padding: 1px 0;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  cursor: default;
}

.emotion-0:hover {
  background-color: #FFF;
  border-color: #3f698a;
  color: #3f698a;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  background-color: #CCC;
  border-color: transparent;
  color: #FFF;
}

<button
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`<FixedButton /> snapshots renders tertiary outline state correctly OldNavy in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 0;
  text-align: center;
  display: block;
  width: 100%;
  padding: 0.5em 0.8em;
  font-size: 1rem;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 700;
  color: #FFF;
  background-color: #000;
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  box-sizing: border-box;
  cursor: pointer;
  text-align: center;
  text-transform: uppercase;
  -webkit-transition: all 200ms ease;
  transition: all 200ms ease;
  height: 2.0625rem;
  background-color: transparent;
  border: 2px solid #333;
  color: #333;
  font-size: 0.8125rem;
  font-weight: 600;
  letter-spacing: normal;
  line-height: normal;
  padding: 0.1rem;
}

.emotion-0:focus {
  outline: none;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  cursor: default;
}

.emotion-0:hover {
  background-color: #FFF;
  border-color: #666;
  color: #666;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  background-color: #CCC;
  border-color: transparent;
  color: #FFF;
}

<button
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`<FixedButton /> snapshots renders tertiary state correctly Athleta 1`] = `
<DocumentFragment>
  .emotion-0 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 1.5px solid transparent;
  border-radius: 0;
  font-size: 16px;
  font-weight: 500;
  letter-spacing: 0px;
  min-height: auto;
  max-height: 60px;
  line-height: 1.25;
  padding: 22px 40px;
  width: 100%;
  font-family: 'Avenir Next','Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #FFF;
  background-color: #000;
  border-color: #000;
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  box-sizing: border-box;
  cursor: pointer;
  text-align: center;
  text-transform: uppercase;
  -webkit-transition: all 200ms ease;
  transition: all 200ms ease;
  height: 2.0625rem;
  background-color: #FFF;
  border: 1px solid #333;
  color: #333;
  font-size: 81.25%;
  font-weight: 600;
  letter-spacing: normal;
  line-height: normal;
  padding: 0.5em 1rem 0.655em 1rem;
}

.emotion-0:focus {
  outline: none;
}

.emotion-0>span {
  padding: 1px 0;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  cursor: default;
}

.emotion-0:hover {
  background-color: #fff;
  border-color: #666;
  color: #666;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  background-color: #FFF;
  border-color: #CCC;
  color: #CCC;
}

<button
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`<FixedButton /> snapshots renders tertiary state correctly Athleta in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 0;
  text-align: center;
  display: block;
  width: 100%;
  padding: 0.5em 0.8em;
  font-size: 1rem;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 700;
  color: #FFF;
  background-color: #000;
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  box-sizing: border-box;
  cursor: pointer;
  text-align: center;
  text-transform: uppercase;
  -webkit-transition: all 200ms ease;
  transition: all 200ms ease;
  height: 2.0625rem;
  background-color: #333;
  border: 2px solid transparent;
  color: #FFF;
  font-size: 0.8125rem;
  font-weight: 600;
  letter-spacing: normal;
  line-height: normal;
  padding: 0.1rem;
}

.emotion-0:focus {
  outline: none;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  cursor: default;
}

.emotion-0:hover {
  background-color: #666;
  border-color: transparent;
  color: #fff;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  background-color: #CCC;
  border-color: transparent;
  color: #FFF;
}

<button
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`<FixedButton /> snapshots renders tertiary state correctly BananaRepublic 1`] = `
<DocumentFragment>
  .emotion-0 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 0;
  text-align: center;
  display: block;
  width: 100%;
  padding: 0.5em 0.8em;
  font-size: 1rem;
  font-family: 'Lynstone',Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 600;
  color: #FFF;
  background-color: #000;
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  box-sizing: border-box;
  cursor: pointer;
  text-align: center;
  text-transform: uppercase;
  -webkit-transition: all 200ms ease;
  transition: all 200ms ease;
  height: 2.0625rem;
  background-color: #FFF;
  border: 1px solid #000;
  color: #000;
  font-size: 93.75%;
  font-weight: 400;
  letter-spacing: normal;
  line-height: normal;
  padding: 0.313em 1rem;
}

.emotion-0:focus {
  outline: none;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  cursor: default;
}

.emotion-0:hover {
  background-color: #fff;
  border-color: #3f3f3f;
  color: #3f3f3f;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  background-color: #FFF;
  border-color: #CCC;
  color: #CCC;
}

<button
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`<FixedButton /> snapshots renders tertiary state correctly BananaRepublic in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 0;
  text-align: center;
  display: block;
  width: 100%;
  padding: 0.5em 0.8em;
  font-size: 1rem;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 700;
  color: #FFF;
  background-color: #000;
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  box-sizing: border-box;
  cursor: pointer;
  text-align: center;
  text-transform: uppercase;
  -webkit-transition: all 200ms ease;
  transition: all 200ms ease;
  height: 2.0625rem;
  background-color: #333;
  border: 2px solid transparent;
  color: #FFF;
  font-size: 0.8125rem;
  font-weight: 600;
  letter-spacing: normal;
  line-height: normal;
  padding: 0.1rem;
}

.emotion-0:focus {
  outline: none;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  cursor: default;
}

.emotion-0:hover {
  background-color: #666;
  border-color: transparent;
  color: #fff;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  background-color: #CCC;
  border-color: transparent;
  color: #FFF;
}

<button
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`<FixedButton /> snapshots renders tertiary state correctly BananaRepublicFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 0;
  text-align: center;
  display: block;
  width: 100%;
  padding: 0.5em 0.8em;
  font-size: 1rem;
  font-family: 'Lynstone',Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 700;
  color: #FFF;
  background-color: #000;
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  box-sizing: border-box;
  cursor: pointer;
  text-align: center;
  text-transform: uppercase;
  -webkit-transition: all 200ms ease;
  transition: all 200ms ease;
  height: 2.0625rem;
  background-color: #FFF;
  border: 1px solid #000;
  color: #000;
  font-size: 93.75%;
  font-weight: 400;
  letter-spacing: normal;
  line-height: normal;
  padding: 0.313em 1rem;
}

.emotion-0:focus {
  outline: none;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  cursor: default;
}

.emotion-0:hover {
  background-color: #fff;
  border-color: #3f3f3f;
  color: #3f3f3f;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  background-color: #FFF;
  border-color: #CCC;
  color: #CCC;
}

<button
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`<FixedButton /> snapshots renders tertiary state correctly BananaRepublicFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 0;
  text-align: center;
  display: block;
  width: 100%;
  padding: 0.5em 0.8em;
  font-size: 1rem;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 700;
  color: #FFF;
  background-color: #000;
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  box-sizing: border-box;
  cursor: pointer;
  text-align: center;
  text-transform: uppercase;
  -webkit-transition: all 200ms ease;
  transition: all 200ms ease;
  height: 2.0625rem;
  background-color: #333;
  border: 2px solid transparent;
  color: #FFF;
  font-size: 0.8125rem;
  font-weight: 600;
  letter-spacing: normal;
  line-height: normal;
  padding: 0.1rem;
}

.emotion-0:focus {
  outline: none;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  cursor: default;
}

.emotion-0:hover {
  background-color: #666;
  border-color: transparent;
  color: #fff;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  background-color: #CCC;
  border-color: transparent;
  color: #FFF;
}

<button
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`<FixedButton /> snapshots renders tertiary state correctly Gap 1`] = `
<DocumentFragment>
  .emotion-0 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 1px solid transparent;
  border-radius: 0;
  font-size: 16px;
  font-weight: 400;
  letter-spacing: normal;
  min-height: 45px;
  max-height: auto;
  line-height: 1.125;
  padding: 12px 20px 11px;
  width: 100%;
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #FFF;
  background-color: #002554;
  border-color: #002554;
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  box-sizing: border-box;
  cursor: pointer;
  text-align: center;
  text-transform: uppercase;
  -webkit-transition: all 200ms ease;
  transition: all 200ms ease;
  height: 2.0625rem;
  background-color: #0A5694;
  border: none;
  color: #FFF;
  font-size: 81.25%;
  font-weight: 700;
  letter-spacing: normal;
  line-height: normal;
  padding: 0.6em 1rem 0.693em 1rem;
}

.emotion-0:focus {
  outline: none;
}

.emotion-0>span {
  padding: 1px 0;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  cursor: default;
}

.emotion-0:hover {
  background-color: #4780ae;
  border-color: transparent;
  color: #fff;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  background-color: #CCC;
  border-color: transparent;
  color: #FFF;
}

<button
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`<FixedButton /> snapshots renders tertiary state correctly Gap in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 0;
  text-align: center;
  display: block;
  width: 100%;
  padding: 0.5em 0.8em;
  font-size: 1rem;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 700;
  color: #FFF;
  background-color: #000;
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  box-sizing: border-box;
  cursor: pointer;
  text-align: center;
  text-transform: uppercase;
  -webkit-transition: all 200ms ease;
  transition: all 200ms ease;
  height: 2.0625rem;
  background-color: #333;
  border: 2px solid transparent;
  color: #FFF;
  font-size: 0.8125rem;
  font-weight: 600;
  letter-spacing: normal;
  line-height: normal;
  padding: 0.1rem;
}

.emotion-0:focus {
  outline: none;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  cursor: default;
}

.emotion-0:hover {
  background-color: #666;
  border-color: transparent;
  color: #fff;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  background-color: #CCC;
  border-color: transparent;
  color: #FFF;
}

<button
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`<FixedButton /> snapshots renders tertiary state correctly GapFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 1px solid transparent;
  border-radius: 0;
  font-size: 16px;
  font-weight: 400;
  letter-spacing: normal;
  min-height: 45px;
  max-height: auto;
  line-height: 1.125;
  padding: 12px 20px 11px;
  width: 100%;
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #FFF;
  background-color: #002554;
  border-color: #002554;
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  box-sizing: border-box;
  cursor: pointer;
  text-align: center;
  text-transform: uppercase;
  -webkit-transition: all 200ms ease;
  transition: all 200ms ease;
  height: 2.0625rem;
  background-color: #0A5694;
  border: none;
  color: #FFF;
  font-size: 81.25%;
  font-weight: 700;
  letter-spacing: normal;
  line-height: normal;
  padding: 0.6em 1rem 0.693em 1rem;
}

.emotion-0:focus {
  outline: none;
}

.emotion-0>span {
  padding: 1px 0;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  cursor: default;
}

.emotion-0:hover {
  background-color: #4780ae;
  border-color: transparent;
  color: #fff;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  background-color: #CCC;
  border-color: transparent;
  color: #FFF;
}

<button
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`<FixedButton /> snapshots renders tertiary state correctly GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 0;
  text-align: center;
  display: block;
  width: 100%;
  padding: 0.5em 0.8em;
  font-size: 1rem;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 700;
  color: #FFF;
  background-color: #000;
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  box-sizing: border-box;
  cursor: pointer;
  text-align: center;
  text-transform: uppercase;
  -webkit-transition: all 200ms ease;
  transition: all 200ms ease;
  height: 2.0625rem;
  background-color: #333;
  border: 2px solid transparent;
  color: #FFF;
  font-size: 0.8125rem;
  font-weight: 600;
  letter-spacing: normal;
  line-height: normal;
  padding: 0.1rem;
}

.emotion-0:focus {
  outline: none;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  cursor: default;
}

.emotion-0:hover {
  background-color: #666;
  border-color: transparent;
  color: #fff;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  background-color: #CCC;
  border-color: transparent;
  color: #FFF;
}

<button
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`<FixedButton /> snapshots renders tertiary state correctly OldNavy 1`] = `
<DocumentFragment>
  .emotion-0 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 700;
  letter-spacing: 1.6800000000000002px;
  min-height: 44px;
  max-height: auto;
  line-height: 1.1428571428571428;
  padding: 12px 22px;
  width: 100%;
  font-family: 'Gotham','Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #FFF;
  background-color: #003764;
  border-color: #003764;
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  box-sizing: border-box;
  cursor: pointer;
  text-align: center;
  text-transform: uppercase;
  -webkit-transition: all 200ms ease;
  transition: all 200ms ease;
  height: 2.0625rem;
  background-color: #003764;
  border: none;
  color: #FFF;
  font-size: 87.5%;
  font-weight: 700;
  letter-spacing: normal;
  line-height: normal;
  padding: 0.5em 1rem;
}

.emotion-0:focus {
  outline: none;
}

.emotion-0>span {
  padding: 1px 0;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  cursor: default;
}

.emotion-0:hover {
  background-color: #3f698a;
  border-color: transparent;
  color: #fff;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  background-color: #CCC;
  border-color: transparent;
  color: #FFF;
}

<button
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`<FixedButton /> snapshots renders tertiary state correctly OldNavy in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 0;
  text-align: center;
  display: block;
  width: 100%;
  padding: 0.5em 0.8em;
  font-size: 1rem;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 700;
  color: #FFF;
  background-color: #000;
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  box-sizing: border-box;
  cursor: pointer;
  text-align: center;
  text-transform: uppercase;
  -webkit-transition: all 200ms ease;
  transition: all 200ms ease;
  height: 2.0625rem;
  background-color: #333;
  border: 2px solid transparent;
  color: #FFF;
  font-size: 0.8125rem;
  font-weight: 600;
  letter-spacing: normal;
  line-height: normal;
  padding: 0.1rem;
}

.emotion-0:focus {
  outline: none;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  cursor: default;
}

.emotion-0:hover {
  background-color: #666;
  border-color: transparent;
  color: #fff;
}

.emotion-0:disabled,
.emotion-0:disabled:hover {
  background-color: #CCC;
  border-color: transparent;
  color: #FFF;
}

<button
    class="emotion-0"
  />
</DocumentFragment>
`;
