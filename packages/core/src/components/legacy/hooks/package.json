{"sideEffects": false, "name": "@core-ui/hooks", "version": "3.2.4", "description": "Custom React Hooks for core-ui", "author": "Foundation UI", "main": "src/index.ts", "scripts": {"build": "npm run clean && npm run build:js && npm run build:types", "build:js": "rollup -c", "build:types": "tsc --project tsconfig.publish.json", "check-types": "tsc --noEmit", "clean": "rm -rf build", "coverage": "npm test -- --coverage", "lint": "eslint --ext .jsx,.js,.ts,.tsx .", "lint:fix": "npm run lint -- --fix", "prepublishOnly": "npm run build && node ../../tasks/copy-files.js", "test": "jest --colors", "test:update": "npm test -- --updateSnapshot"}, "devDependencies": {"@core-ui/eslint-config-fui": "0.8.0"}, "peerDependencies": {"@core-ui/core": ">=0.4.3", "react": "^16.9.0"}, "migratedToCore": "3.0.0"}