// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`<Meter /> snapshots renders colors state correctly Athleta 1`] = `
<DocumentFragment>
  .emotion-0 {
  background: white;
  border: 1px solid gray;
  box-sizing: border-box;
  height: 0.5rem;
  width: 100%;
  border-radius: 0.25rem;
}

undefined@media not all and (min-resolution:.001dpcm) {
  @supports (-webkit-appearance: none) {
    .emotion-0 {
      border: none;
      will-change: transform;
      overflow: hidden;
    }
  }
}

@-moz-document url-prefix() {
  .emotion-0 {
    overflow: hidden;
  }
}

.emotion-0::-webkit-meter-bar {
  background: white;
  border: 1px solid gray;
  box-sizing: border-box;
  height: 0.5rem;
  width: 100%;
  border-radius: 0.25rem;
}

.emotion-0:-moz-meter-optimum::-moz-meter-bar {
  background: purple;
  border-radius: 0.25rem;
}

.emotion-0::-webkit-meter-optimum-value {
  background: purple;
  border-radius: 0.25rem;
}

<meter
    aria-hidden="true"
    aria-label=""
    class=" emotion-0"
    max="100"
    value="30"
  />
</DocumentFragment>
`;

exports[`<Meter /> snapshots renders colors state correctly Athleta in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  background: white;
  border: 1px solid gray;
  box-sizing: border-box;
  height: 0.5rem;
  width: 100%;
  border-radius: 0.25rem;
}

undefined@media not all and (min-resolution:.001dpcm) {
  @supports (-webkit-appearance: none) {
    .emotion-0 {
      border: none;
      will-change: transform;
      overflow: hidden;
    }
  }
}

@-moz-document url-prefix() {
  .emotion-0 {
    overflow: hidden;
  }
}

.emotion-0::-webkit-meter-bar {
  background: white;
  border: 1px solid gray;
  box-sizing: border-box;
  height: 0.5rem;
  width: 100%;
  border-radius: 0.25rem;
}

.emotion-0:-moz-meter-optimum::-moz-meter-bar {
  background: purple;
  border-radius: 0.25rem;
}

.emotion-0::-webkit-meter-optimum-value {
  background: purple;
  border-radius: 0.25rem;
}

<meter
    aria-hidden="true"
    aria-label=""
    class=" emotion-0"
    max="100"
    value="30"
  />
</DocumentFragment>
`;

exports[`<Meter /> snapshots renders colors state correctly BananaRepublic 1`] = `
<DocumentFragment>
  .emotion-0 {
  background: white;
  border: 1px solid gray;
  box-sizing: border-box;
  height: 0.5rem;
  width: 100%;
  border-radius: 0.25rem;
}

undefined@media not all and (min-resolution:.001dpcm) {
  @supports (-webkit-appearance: none) {
    .emotion-0 {
      border: none;
      will-change: transform;
      overflow: hidden;
    }
  }
}

@-moz-document url-prefix() {
  .emotion-0 {
    overflow: hidden;
  }
}

.emotion-0::-webkit-meter-bar {
  background: white;
  border: 1px solid gray;
  box-sizing: border-box;
  height: 0.5rem;
  width: 100%;
  border-radius: 0.25rem;
}

.emotion-0:-moz-meter-optimum::-moz-meter-bar {
  background: purple;
  border-radius: 0.25rem;
}

.emotion-0::-webkit-meter-optimum-value {
  background: purple;
  border-radius: 0.25rem;
}

<meter
    aria-hidden="true"
    aria-label=""
    class=" emotion-0"
    max="100"
    value="30"
  />
</DocumentFragment>
`;

exports[`<Meter /> snapshots renders colors state correctly BananaRepublic in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  background: white;
  border: 1px solid gray;
  box-sizing: border-box;
  height: 0.5rem;
  width: 100%;
  border-radius: 0.25rem;
}

undefined@media not all and (min-resolution:.001dpcm) {
  @supports (-webkit-appearance: none) {
    .emotion-0 {
      border: none;
      will-change: transform;
      overflow: hidden;
    }
  }
}

@-moz-document url-prefix() {
  .emotion-0 {
    overflow: hidden;
  }
}

.emotion-0::-webkit-meter-bar {
  background: white;
  border: 1px solid gray;
  box-sizing: border-box;
  height: 0.5rem;
  width: 100%;
  border-radius: 0.25rem;
}

.emotion-0:-moz-meter-optimum::-moz-meter-bar {
  background: purple;
  border-radius: 0.25rem;
}

.emotion-0::-webkit-meter-optimum-value {
  background: purple;
  border-radius: 0.25rem;
}

<meter
    aria-hidden="true"
    aria-label=""
    class=" emotion-0"
    max="100"
    value="30"
  />
</DocumentFragment>
`;

exports[`<Meter /> snapshots renders colors state correctly BananaRepublicFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  background: white;
  border: 1px solid gray;
  box-sizing: border-box;
  height: 0.5rem;
  width: 100%;
  border-radius: 0.25rem;
}

undefined@media not all and (min-resolution:.001dpcm) {
  @supports (-webkit-appearance: none) {
    .emotion-0 {
      border: none;
      will-change: transform;
      overflow: hidden;
    }
  }
}

@-moz-document url-prefix() {
  .emotion-0 {
    overflow: hidden;
  }
}

.emotion-0::-webkit-meter-bar {
  background: white;
  border: 1px solid gray;
  box-sizing: border-box;
  height: 0.5rem;
  width: 100%;
  border-radius: 0.25rem;
}

.emotion-0:-moz-meter-optimum::-moz-meter-bar {
  background: purple;
  border-radius: 0.25rem;
}

.emotion-0::-webkit-meter-optimum-value {
  background: purple;
  border-radius: 0.25rem;
}

<meter
    aria-hidden="true"
    aria-label=""
    class=" emotion-0"
    max="100"
    value="30"
  />
</DocumentFragment>
`;

exports[`<Meter /> snapshots renders colors state correctly BananaRepublicFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  background: white;
  border: 1px solid gray;
  box-sizing: border-box;
  height: 0.5rem;
  width: 100%;
  border-radius: 0.25rem;
}

undefined@media not all and (min-resolution:.001dpcm) {
  @supports (-webkit-appearance: none) {
    .emotion-0 {
      border: none;
      will-change: transform;
      overflow: hidden;
    }
  }
}

@-moz-document url-prefix() {
  .emotion-0 {
    overflow: hidden;
  }
}

.emotion-0::-webkit-meter-bar {
  background: white;
  border: 1px solid gray;
  box-sizing: border-box;
  height: 0.5rem;
  width: 100%;
  border-radius: 0.25rem;
}

.emotion-0:-moz-meter-optimum::-moz-meter-bar {
  background: purple;
  border-radius: 0.25rem;
}

.emotion-0::-webkit-meter-optimum-value {
  background: purple;
  border-radius: 0.25rem;
}

<meter
    aria-hidden="true"
    aria-label=""
    class=" emotion-0"
    max="100"
    value="30"
  />
</DocumentFragment>
`;

exports[`<Meter /> snapshots renders colors state correctly Gap 1`] = `
<DocumentFragment>
  .emotion-0 {
  background: white;
  border: 1px solid gray;
  box-sizing: border-box;
  height: 0.5rem;
  width: 100%;
  border-radius: 0.25rem;
}

undefined@media not all and (min-resolution:.001dpcm) {
  @supports (-webkit-appearance: none) {
    .emotion-0 {
      border: none;
      will-change: transform;
      overflow: hidden;
    }
  }
}

@-moz-document url-prefix() {
  .emotion-0 {
    overflow: hidden;
  }
}

.emotion-0::-webkit-meter-bar {
  background: white;
  border: 1px solid gray;
  box-sizing: border-box;
  height: 0.5rem;
  width: 100%;
  border-radius: 0.25rem;
}

.emotion-0:-moz-meter-optimum::-moz-meter-bar {
  background: purple;
  border-radius: 0.25rem;
}

.emotion-0::-webkit-meter-optimum-value {
  background: purple;
  border-radius: 0.25rem;
}

<meter
    aria-hidden="true"
    aria-label=""
    class=" emotion-0"
    max="100"
    value="30"
  />
</DocumentFragment>
`;

exports[`<Meter /> snapshots renders colors state correctly Gap in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  background: white;
  border: 1px solid gray;
  box-sizing: border-box;
  height: 0.5rem;
  width: 100%;
  border-radius: 0.25rem;
}

undefined@media not all and (min-resolution:.001dpcm) {
  @supports (-webkit-appearance: none) {
    .emotion-0 {
      border: none;
      will-change: transform;
      overflow: hidden;
    }
  }
}

@-moz-document url-prefix() {
  .emotion-0 {
    overflow: hidden;
  }
}

.emotion-0::-webkit-meter-bar {
  background: white;
  border: 1px solid gray;
  box-sizing: border-box;
  height: 0.5rem;
  width: 100%;
  border-radius: 0.25rem;
}

.emotion-0:-moz-meter-optimum::-moz-meter-bar {
  background: purple;
  border-radius: 0.25rem;
}

.emotion-0::-webkit-meter-optimum-value {
  background: purple;
  border-radius: 0.25rem;
}

<meter
    aria-hidden="true"
    aria-label=""
    class=" emotion-0"
    max="100"
    value="30"
  />
</DocumentFragment>
`;

exports[`<Meter /> snapshots renders colors state correctly GapFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  background: white;
  border: 1px solid gray;
  box-sizing: border-box;
  height: 0.5rem;
  width: 100%;
  border-radius: 0.25rem;
}

undefined@media not all and (min-resolution:.001dpcm) {
  @supports (-webkit-appearance: none) {
    .emotion-0 {
      border: none;
      will-change: transform;
      overflow: hidden;
    }
  }
}

@-moz-document url-prefix() {
  .emotion-0 {
    overflow: hidden;
  }
}

.emotion-0::-webkit-meter-bar {
  background: white;
  border: 1px solid gray;
  box-sizing: border-box;
  height: 0.5rem;
  width: 100%;
  border-radius: 0.25rem;
}

.emotion-0:-moz-meter-optimum::-moz-meter-bar {
  background: purple;
  border-radius: 0.25rem;
}

.emotion-0::-webkit-meter-optimum-value {
  background: purple;
  border-radius: 0.25rem;
}

<meter
    aria-hidden="true"
    aria-label=""
    class=" emotion-0"
    max="100"
    value="30"
  />
</DocumentFragment>
`;

exports[`<Meter /> snapshots renders colors state correctly GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  background: white;
  border: 1px solid gray;
  box-sizing: border-box;
  height: 0.5rem;
  width: 100%;
  border-radius: 0.25rem;
}

undefined@media not all and (min-resolution:.001dpcm) {
  @supports (-webkit-appearance: none) {
    .emotion-0 {
      border: none;
      will-change: transform;
      overflow: hidden;
    }
  }
}

@-moz-document url-prefix() {
  .emotion-0 {
    overflow: hidden;
  }
}

.emotion-0::-webkit-meter-bar {
  background: white;
  border: 1px solid gray;
  box-sizing: border-box;
  height: 0.5rem;
  width: 100%;
  border-radius: 0.25rem;
}

.emotion-0:-moz-meter-optimum::-moz-meter-bar {
  background: purple;
  border-radius: 0.25rem;
}

.emotion-0::-webkit-meter-optimum-value {
  background: purple;
  border-radius: 0.25rem;
}

<meter
    aria-hidden="true"
    aria-label=""
    class=" emotion-0"
    max="100"
    value="30"
  />
</DocumentFragment>
`;

exports[`<Meter /> snapshots renders colors state correctly OldNavy 1`] = `
<DocumentFragment>
  .emotion-0 {
  background: white;
  border: 1px solid gray;
  box-sizing: border-box;
  height: 0.5rem;
  width: 100%;
  border-radius: 0.25rem;
}

undefined@media not all and (min-resolution:.001dpcm) {
  @supports (-webkit-appearance: none) {
    .emotion-0 {
      border: none;
      will-change: transform;
      overflow: hidden;
    }
  }
}

@-moz-document url-prefix() {
  .emotion-0 {
    overflow: hidden;
  }
}

.emotion-0::-webkit-meter-bar {
  background: white;
  border: 1px solid gray;
  box-sizing: border-box;
  height: 0.5rem;
  width: 100%;
  border-radius: 0.25rem;
}

.emotion-0:-moz-meter-optimum::-moz-meter-bar {
  background: purple;
  border-radius: 0.25rem;
}

.emotion-0::-webkit-meter-optimum-value {
  background: purple;
  border-radius: 0.25rem;
}

<meter
    aria-hidden="true"
    aria-label=""
    class=" emotion-0"
    max="100"
    value="30"
  />
</DocumentFragment>
`;

exports[`<Meter /> snapshots renders colors state correctly OldNavy in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  background: white;
  border: 1px solid gray;
  box-sizing: border-box;
  height: 0.5rem;
  width: 100%;
  border-radius: 0.25rem;
}

undefined@media not all and (min-resolution:.001dpcm) {
  @supports (-webkit-appearance: none) {
    .emotion-0 {
      border: none;
      will-change: transform;
      overflow: hidden;
    }
  }
}

@-moz-document url-prefix() {
  .emotion-0 {
    overflow: hidden;
  }
}

.emotion-0::-webkit-meter-bar {
  background: white;
  border: 1px solid gray;
  box-sizing: border-box;
  height: 0.5rem;
  width: 100%;
  border-radius: 0.25rem;
}

.emotion-0:-moz-meter-optimum::-moz-meter-bar {
  background: purple;
  border-radius: 0.25rem;
}

.emotion-0::-webkit-meter-optimum-value {
  background: purple;
  border-radius: 0.25rem;
}

<meter
    aria-hidden="true"
    aria-label=""
    class=" emotion-0"
    max="100"
    value="30"
  />
</DocumentFragment>
`;

exports[`<Meter /> snapshots renders default state correctly Athleta 1`] = `
<DocumentFragment>
  .emotion-0 {
  background: #CCC;
  border: 1px solid #7F7F7F;
  box-sizing: border-box;
  height: 0.5rem;
  width: 100%;
  border-radius: 0.25rem;
}

undefined@media not all and (min-resolution:.001dpcm) {
  @supports (-webkit-appearance: none) {
    .emotion-0 {
      border: none;
      will-change: transform;
      overflow: hidden;
    }
  }
}

@-moz-document url-prefix() {
  .emotion-0 {
    overflow: hidden;
  }
}

.emotion-0::-webkit-meter-bar {
  background: #CCC;
  border: 1px solid #7F7F7F;
  box-sizing: border-box;
  height: 0.5rem;
  width: 100%;
  border-radius: 0.25rem;
}

.emotion-0:-moz-meter-optimum::-moz-meter-bar {
  background: #000;
  border-radius: 0.25rem;
}

.emotion-0::-webkit-meter-optimum-value {
  background: #000;
  border-radius: 0.25rem;
}

<meter
    aria-hidden="true"
    aria-label=""
    class=" emotion-0"
    max="100"
    value="30"
  />
</DocumentFragment>
`;

exports[`<Meter /> snapshots renders default state correctly Athleta in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  background: #CCC;
  border: 1px solid #7F7F7F;
  box-sizing: border-box;
  height: 0.5rem;
  width: 100%;
  border-radius: 0.25rem;
}

undefined@media not all and (min-resolution:.001dpcm) {
  @supports (-webkit-appearance: none) {
    .emotion-0 {
      border: none;
      will-change: transform;
      overflow: hidden;
    }
  }
}

@-moz-document url-prefix() {
  .emotion-0 {
    overflow: hidden;
  }
}

.emotion-0::-webkit-meter-bar {
  background: #CCC;
  border: 1px solid #7F7F7F;
  box-sizing: border-box;
  height: 0.5rem;
  width: 100%;
  border-radius: 0.25rem;
}

.emotion-0:-moz-meter-optimum::-moz-meter-bar {
  background: #000;
  border-radius: 0.25rem;
}

.emotion-0::-webkit-meter-optimum-value {
  background: #000;
  border-radius: 0.25rem;
}

<meter
    aria-hidden="true"
    aria-label=""
    class=" emotion-0"
    max="100"
    value="30"
  />
</DocumentFragment>
`;

exports[`<Meter /> snapshots renders default state correctly BananaRepublic 1`] = `
<DocumentFragment>
  .emotion-0 {
  background: #CCC;
  border: 1px solid #7F7F7F;
  box-sizing: border-box;
  height: 0.5rem;
  width: 100%;
  border-radius: 0.25rem;
}

undefined@media not all and (min-resolution:.001dpcm) {
  @supports (-webkit-appearance: none) {
    .emotion-0 {
      border: none;
      will-change: transform;
      overflow: hidden;
    }
  }
}

@-moz-document url-prefix() {
  .emotion-0 {
    overflow: hidden;
  }
}

.emotion-0::-webkit-meter-bar {
  background: #CCC;
  border: 1px solid #7F7F7F;
  box-sizing: border-box;
  height: 0.5rem;
  width: 100%;
  border-radius: 0.25rem;
}

.emotion-0:-moz-meter-optimum::-moz-meter-bar {
  background: #000;
  border-radius: 0.25rem;
}

.emotion-0::-webkit-meter-optimum-value {
  background: #000;
  border-radius: 0.25rem;
}

<meter
    aria-hidden="true"
    aria-label=""
    class=" emotion-0"
    max="100"
    value="30"
  />
</DocumentFragment>
`;

exports[`<Meter /> snapshots renders default state correctly BananaRepublic in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  background: #CCC;
  border: 1px solid #7F7F7F;
  box-sizing: border-box;
  height: 0.5rem;
  width: 100%;
  border-radius: 0.25rem;
}

undefined@media not all and (min-resolution:.001dpcm) {
  @supports (-webkit-appearance: none) {
    .emotion-0 {
      border: none;
      will-change: transform;
      overflow: hidden;
    }
  }
}

@-moz-document url-prefix() {
  .emotion-0 {
    overflow: hidden;
  }
}

.emotion-0::-webkit-meter-bar {
  background: #CCC;
  border: 1px solid #7F7F7F;
  box-sizing: border-box;
  height: 0.5rem;
  width: 100%;
  border-radius: 0.25rem;
}

.emotion-0:-moz-meter-optimum::-moz-meter-bar {
  background: #000;
  border-radius: 0.25rem;
}

.emotion-0::-webkit-meter-optimum-value {
  background: #000;
  border-radius: 0.25rem;
}

<meter
    aria-hidden="true"
    aria-label=""
    class=" emotion-0"
    max="100"
    value="30"
  />
</DocumentFragment>
`;

exports[`<Meter /> snapshots renders default state correctly BananaRepublicFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  background: #CCC;
  border: 1px solid #7F7F7F;
  box-sizing: border-box;
  height: 0.5rem;
  width: 100%;
  border-radius: 0.25rem;
}

undefined@media not all and (min-resolution:.001dpcm) {
  @supports (-webkit-appearance: none) {
    .emotion-0 {
      border: none;
      will-change: transform;
      overflow: hidden;
    }
  }
}

@-moz-document url-prefix() {
  .emotion-0 {
    overflow: hidden;
  }
}

.emotion-0::-webkit-meter-bar {
  background: #CCC;
  border: 1px solid #7F7F7F;
  box-sizing: border-box;
  height: 0.5rem;
  width: 100%;
  border-radius: 0.25rem;
}

.emotion-0:-moz-meter-optimum::-moz-meter-bar {
  background: #000;
  border-radius: 0.25rem;
}

.emotion-0::-webkit-meter-optimum-value {
  background: #000;
  border-radius: 0.25rem;
}

<meter
    aria-hidden="true"
    aria-label=""
    class=" emotion-0"
    max="100"
    value="30"
  />
</DocumentFragment>
`;

exports[`<Meter /> snapshots renders default state correctly BananaRepublicFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  background: #CCC;
  border: 1px solid #7F7F7F;
  box-sizing: border-box;
  height: 0.5rem;
  width: 100%;
  border-radius: 0.25rem;
}

undefined@media not all and (min-resolution:.001dpcm) {
  @supports (-webkit-appearance: none) {
    .emotion-0 {
      border: none;
      will-change: transform;
      overflow: hidden;
    }
  }
}

@-moz-document url-prefix() {
  .emotion-0 {
    overflow: hidden;
  }
}

.emotion-0::-webkit-meter-bar {
  background: #CCC;
  border: 1px solid #7F7F7F;
  box-sizing: border-box;
  height: 0.5rem;
  width: 100%;
  border-radius: 0.25rem;
}

.emotion-0:-moz-meter-optimum::-moz-meter-bar {
  background: #000;
  border-radius: 0.25rem;
}

.emotion-0::-webkit-meter-optimum-value {
  background: #000;
  border-radius: 0.25rem;
}

<meter
    aria-hidden="true"
    aria-label=""
    class=" emotion-0"
    max="100"
    value="30"
  />
</DocumentFragment>
`;

exports[`<Meter /> snapshots renders default state correctly Gap 1`] = `
<DocumentFragment>
  .emotion-0 {
  background: #CCC;
  border: 1px solid #7F7F7F;
  box-sizing: border-box;
  height: 0.5rem;
  width: 100%;
  border-radius: 0.25rem;
}

undefined@media not all and (min-resolution:.001dpcm) {
  @supports (-webkit-appearance: none) {
    .emotion-0 {
      border: none;
      will-change: transform;
      overflow: hidden;
    }
  }
}

@-moz-document url-prefix() {
  .emotion-0 {
    overflow: hidden;
  }
}

.emotion-0::-webkit-meter-bar {
  background: #CCC;
  border: 1px solid #7F7F7F;
  box-sizing: border-box;
  height: 0.5rem;
  width: 100%;
  border-radius: 0.25rem;
}

.emotion-0:-moz-meter-optimum::-moz-meter-bar {
  background: #000;
  border-radius: 0.25rem;
}

.emotion-0::-webkit-meter-optimum-value {
  background: #000;
  border-radius: 0.25rem;
}

<meter
    aria-hidden="true"
    aria-label=""
    class=" emotion-0"
    max="100"
    value="30"
  />
</DocumentFragment>
`;

exports[`<Meter /> snapshots renders default state correctly Gap in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  background: #CCC;
  border: 1px solid #7F7F7F;
  box-sizing: border-box;
  height: 0.5rem;
  width: 100%;
  border-radius: 0.25rem;
}

undefined@media not all and (min-resolution:.001dpcm) {
  @supports (-webkit-appearance: none) {
    .emotion-0 {
      border: none;
      will-change: transform;
      overflow: hidden;
    }
  }
}

@-moz-document url-prefix() {
  .emotion-0 {
    overflow: hidden;
  }
}

.emotion-0::-webkit-meter-bar {
  background: #CCC;
  border: 1px solid #7F7F7F;
  box-sizing: border-box;
  height: 0.5rem;
  width: 100%;
  border-radius: 0.25rem;
}

.emotion-0:-moz-meter-optimum::-moz-meter-bar {
  background: #000;
  border-radius: 0.25rem;
}

.emotion-0::-webkit-meter-optimum-value {
  background: #000;
  border-radius: 0.25rem;
}

<meter
    aria-hidden="true"
    aria-label=""
    class=" emotion-0"
    max="100"
    value="30"
  />
</DocumentFragment>
`;

exports[`<Meter /> snapshots renders default state correctly GapFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  background: #CCC;
  border: 1px solid #7F7F7F;
  box-sizing: border-box;
  height: 0.5rem;
  width: 100%;
  border-radius: 0.25rem;
}

undefined@media not all and (min-resolution:.001dpcm) {
  @supports (-webkit-appearance: none) {
    .emotion-0 {
      border: none;
      will-change: transform;
      overflow: hidden;
    }
  }
}

@-moz-document url-prefix() {
  .emotion-0 {
    overflow: hidden;
  }
}

.emotion-0::-webkit-meter-bar {
  background: #CCC;
  border: 1px solid #7F7F7F;
  box-sizing: border-box;
  height: 0.5rem;
  width: 100%;
  border-radius: 0.25rem;
}

.emotion-0:-moz-meter-optimum::-moz-meter-bar {
  background: #000;
  border-radius: 0.25rem;
}

.emotion-0::-webkit-meter-optimum-value {
  background: #000;
  border-radius: 0.25rem;
}

<meter
    aria-hidden="true"
    aria-label=""
    class=" emotion-0"
    max="100"
    value="30"
  />
</DocumentFragment>
`;

exports[`<Meter /> snapshots renders default state correctly GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  background: #CCC;
  border: 1px solid #7F7F7F;
  box-sizing: border-box;
  height: 0.5rem;
  width: 100%;
  border-radius: 0.25rem;
}

undefined@media not all and (min-resolution:.001dpcm) {
  @supports (-webkit-appearance: none) {
    .emotion-0 {
      border: none;
      will-change: transform;
      overflow: hidden;
    }
  }
}

@-moz-document url-prefix() {
  .emotion-0 {
    overflow: hidden;
  }
}

.emotion-0::-webkit-meter-bar {
  background: #CCC;
  border: 1px solid #7F7F7F;
  box-sizing: border-box;
  height: 0.5rem;
  width: 100%;
  border-radius: 0.25rem;
}

.emotion-0:-moz-meter-optimum::-moz-meter-bar {
  background: #000;
  border-radius: 0.25rem;
}

.emotion-0::-webkit-meter-optimum-value {
  background: #000;
  border-radius: 0.25rem;
}

<meter
    aria-hidden="true"
    aria-label=""
    class=" emotion-0"
    max="100"
    value="30"
  />
</DocumentFragment>
`;

exports[`<Meter /> snapshots renders default state correctly OldNavy 1`] = `
<DocumentFragment>
  .emotion-0 {
  background: #CCC;
  border: 1px solid #7F7F7F;
  box-sizing: border-box;
  height: 0.5rem;
  width: 100%;
  border-radius: 0.25rem;
}

undefined@media not all and (min-resolution:.001dpcm) {
  @supports (-webkit-appearance: none) {
    .emotion-0 {
      border: none;
      will-change: transform;
      overflow: hidden;
    }
  }
}

@-moz-document url-prefix() {
  .emotion-0 {
    overflow: hidden;
  }
}

.emotion-0::-webkit-meter-bar {
  background: #CCC;
  border: 1px solid #7F7F7F;
  box-sizing: border-box;
  height: 0.5rem;
  width: 100%;
  border-radius: 0.25rem;
}

.emotion-0:-moz-meter-optimum::-moz-meter-bar {
  background: #000;
  border-radius: 0.25rem;
}

.emotion-0::-webkit-meter-optimum-value {
  background: #000;
  border-radius: 0.25rem;
}

<meter
    aria-hidden="true"
    aria-label=""
    class=" emotion-0"
    max="100"
    value="30"
  />
</DocumentFragment>
`;

exports[`<Meter /> snapshots renders default state correctly OldNavy in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  background: #CCC;
  border: 1px solid #7F7F7F;
  box-sizing: border-box;
  height: 0.5rem;
  width: 100%;
  border-radius: 0.25rem;
}

undefined@media not all and (min-resolution:.001dpcm) {
  @supports (-webkit-appearance: none) {
    .emotion-0 {
      border: none;
      will-change: transform;
      overflow: hidden;
    }
  }
}

@-moz-document url-prefix() {
  .emotion-0 {
    overflow: hidden;
  }
}

.emotion-0::-webkit-meter-bar {
  background: #CCC;
  border: 1px solid #7F7F7F;
  box-sizing: border-box;
  height: 0.5rem;
  width: 100%;
  border-radius: 0.25rem;
}

.emotion-0:-moz-meter-optimum::-moz-meter-bar {
  background: #000;
  border-radius: 0.25rem;
}

.emotion-0::-webkit-meter-optimum-value {
  background: #000;
  border-radius: 0.25rem;
}

<meter
    aria-hidden="true"
    aria-label=""
    class=" emotion-0"
    max="100"
    value="30"
  />
</DocumentFragment>
`;

exports[`<Meter /> snapshots renders largeVariant state correctly Athleta 1`] = `
<DocumentFragment>
  .emotion-0 {
  background: #CCC;
  border: 1px solid #7F7F7F;
  box-sizing: border-box;
  height: 0.5rem;
  width: 100%;
  border-radius: 10px;
  border: transparent;
  height: 12px;
}

@media not all and (min-resolution:.001dpcm) {
  @supports (-webkit-appearance: none) {
    .emotion-0 {
      border: none;
      will-change: transform;
      overflow: hidden;
    }
  }
}

@-moz-document url-prefix() {
  .emotion-0 {
    overflow: hidden;
  }
}

.emotion-0::-webkit-meter-bar {
  background: #CCC;
  border: 1px solid #7F7F7F;
  box-sizing: border-box;
  height: 0.5rem;
  width: 100%;
  border-radius: 10px;
  border: transparent;
  height: 12px;
}

.emotion-0:-moz-meter-optimum::-moz-meter-bar {
  background: #000;
  border-radius: 10px;
}

.emotion-0::-webkit-meter-optimum-value {
  background: #000;
  border-radius: 10px;
}

<meter
    aria-hidden="true"
    aria-label=""
    class=" emotion-0"
    max="100"
    value="30"
  />
</DocumentFragment>
`;

exports[`<Meter /> snapshots renders largeVariant state correctly Athleta in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  background: #CCC;
  border: 1px solid #7F7F7F;
  box-sizing: border-box;
  height: 0.5rem;
  width: 100%;
  border-radius: 10px;
  border: transparent;
  height: 12px;
}

@media not all and (min-resolution:.001dpcm) {
  @supports (-webkit-appearance: none) {
    .emotion-0 {
      border: none;
      will-change: transform;
      overflow: hidden;
    }
  }
}

@-moz-document url-prefix() {
  .emotion-0 {
    overflow: hidden;
  }
}

.emotion-0::-webkit-meter-bar {
  background: #CCC;
  border: 1px solid #7F7F7F;
  box-sizing: border-box;
  height: 0.5rem;
  width: 100%;
  border-radius: 10px;
  border: transparent;
  height: 12px;
}

.emotion-0:-moz-meter-optimum::-moz-meter-bar {
  background: #000;
  border-radius: 10px;
}

.emotion-0::-webkit-meter-optimum-value {
  background: #000;
  border-radius: 10px;
}

<meter
    aria-hidden="true"
    aria-label=""
    class=" emotion-0"
    max="100"
    value="30"
  />
</DocumentFragment>
`;

exports[`<Meter /> snapshots renders largeVariant state correctly BananaRepublic 1`] = `
<DocumentFragment>
  .emotion-0 {
  background: #CCC;
  border: 1px solid #7F7F7F;
  box-sizing: border-box;
  height: 0.5rem;
  width: 100%;
  border-radius: 10px;
  border: transparent;
  height: 12px;
}

@media not all and (min-resolution:.001dpcm) {
  @supports (-webkit-appearance: none) {
    .emotion-0 {
      border: none;
      will-change: transform;
      overflow: hidden;
    }
  }
}

@-moz-document url-prefix() {
  .emotion-0 {
    overflow: hidden;
  }
}

.emotion-0::-webkit-meter-bar {
  background: #CCC;
  border: 1px solid #7F7F7F;
  box-sizing: border-box;
  height: 0.5rem;
  width: 100%;
  border-radius: 10px;
  border: transparent;
  height: 12px;
}

.emotion-0:-moz-meter-optimum::-moz-meter-bar {
  background: #000;
  border-radius: 10px;
}

.emotion-0::-webkit-meter-optimum-value {
  background: #000;
  border-radius: 10px;
}

<meter
    aria-hidden="true"
    aria-label=""
    class=" emotion-0"
    max="100"
    value="30"
  />
</DocumentFragment>
`;

exports[`<Meter /> snapshots renders largeVariant state correctly BananaRepublic in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  background: #CCC;
  border: 1px solid #7F7F7F;
  box-sizing: border-box;
  height: 0.5rem;
  width: 100%;
  border-radius: 10px;
  border: transparent;
  height: 12px;
}

@media not all and (min-resolution:.001dpcm) {
  @supports (-webkit-appearance: none) {
    .emotion-0 {
      border: none;
      will-change: transform;
      overflow: hidden;
    }
  }
}

@-moz-document url-prefix() {
  .emotion-0 {
    overflow: hidden;
  }
}

.emotion-0::-webkit-meter-bar {
  background: #CCC;
  border: 1px solid #7F7F7F;
  box-sizing: border-box;
  height: 0.5rem;
  width: 100%;
  border-radius: 10px;
  border: transparent;
  height: 12px;
}

.emotion-0:-moz-meter-optimum::-moz-meter-bar {
  background: #000;
  border-radius: 10px;
}

.emotion-0::-webkit-meter-optimum-value {
  background: #000;
  border-radius: 10px;
}

<meter
    aria-hidden="true"
    aria-label=""
    class=" emotion-0"
    max="100"
    value="30"
  />
</DocumentFragment>
`;

exports[`<Meter /> snapshots renders largeVariant state correctly BananaRepublicFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  background: #CCC;
  border: 1px solid #7F7F7F;
  box-sizing: border-box;
  height: 0.5rem;
  width: 100%;
  border-radius: 10px;
  border: transparent;
  height: 12px;
}

@media not all and (min-resolution:.001dpcm) {
  @supports (-webkit-appearance: none) {
    .emotion-0 {
      border: none;
      will-change: transform;
      overflow: hidden;
    }
  }
}

@-moz-document url-prefix() {
  .emotion-0 {
    overflow: hidden;
  }
}

.emotion-0::-webkit-meter-bar {
  background: #CCC;
  border: 1px solid #7F7F7F;
  box-sizing: border-box;
  height: 0.5rem;
  width: 100%;
  border-radius: 10px;
  border: transparent;
  height: 12px;
}

.emotion-0:-moz-meter-optimum::-moz-meter-bar {
  background: #000;
  border-radius: 10px;
}

.emotion-0::-webkit-meter-optimum-value {
  background: #000;
  border-radius: 10px;
}

<meter
    aria-hidden="true"
    aria-label=""
    class=" emotion-0"
    max="100"
    value="30"
  />
</DocumentFragment>
`;

exports[`<Meter /> snapshots renders largeVariant state correctly BananaRepublicFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  background: #CCC;
  border: 1px solid #7F7F7F;
  box-sizing: border-box;
  height: 0.5rem;
  width: 100%;
  border-radius: 10px;
  border: transparent;
  height: 12px;
}

@media not all and (min-resolution:.001dpcm) {
  @supports (-webkit-appearance: none) {
    .emotion-0 {
      border: none;
      will-change: transform;
      overflow: hidden;
    }
  }
}

@-moz-document url-prefix() {
  .emotion-0 {
    overflow: hidden;
  }
}

.emotion-0::-webkit-meter-bar {
  background: #CCC;
  border: 1px solid #7F7F7F;
  box-sizing: border-box;
  height: 0.5rem;
  width: 100%;
  border-radius: 10px;
  border: transparent;
  height: 12px;
}

.emotion-0:-moz-meter-optimum::-moz-meter-bar {
  background: #000;
  border-radius: 10px;
}

.emotion-0::-webkit-meter-optimum-value {
  background: #000;
  border-radius: 10px;
}

<meter
    aria-hidden="true"
    aria-label=""
    class=" emotion-0"
    max="100"
    value="30"
  />
</DocumentFragment>
`;

exports[`<Meter /> snapshots renders largeVariant state correctly Gap 1`] = `
<DocumentFragment>
  .emotion-0 {
  background: #CCC;
  border: 1px solid #7F7F7F;
  box-sizing: border-box;
  height: 0.5rem;
  width: 100%;
  border-radius: 10px;
  border: transparent;
  height: 12px;
}

@media not all and (min-resolution:.001dpcm) {
  @supports (-webkit-appearance: none) {
    .emotion-0 {
      border: none;
      will-change: transform;
      overflow: hidden;
    }
  }
}

@-moz-document url-prefix() {
  .emotion-0 {
    overflow: hidden;
  }
}

.emotion-0::-webkit-meter-bar {
  background: #CCC;
  border: 1px solid #7F7F7F;
  box-sizing: border-box;
  height: 0.5rem;
  width: 100%;
  border-radius: 10px;
  border: transparent;
  height: 12px;
}

.emotion-0:-moz-meter-optimum::-moz-meter-bar {
  background: #000;
  border-radius: 10px;
}

.emotion-0::-webkit-meter-optimum-value {
  background: #000;
  border-radius: 10px;
}

<meter
    aria-hidden="true"
    aria-label=""
    class=" emotion-0"
    max="100"
    value="30"
  />
</DocumentFragment>
`;

exports[`<Meter /> snapshots renders largeVariant state correctly Gap in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  background: #CCC;
  border: 1px solid #7F7F7F;
  box-sizing: border-box;
  height: 0.5rem;
  width: 100%;
  border-radius: 10px;
  border: transparent;
  height: 12px;
}

@media not all and (min-resolution:.001dpcm) {
  @supports (-webkit-appearance: none) {
    .emotion-0 {
      border: none;
      will-change: transform;
      overflow: hidden;
    }
  }
}

@-moz-document url-prefix() {
  .emotion-0 {
    overflow: hidden;
  }
}

.emotion-0::-webkit-meter-bar {
  background: #CCC;
  border: 1px solid #7F7F7F;
  box-sizing: border-box;
  height: 0.5rem;
  width: 100%;
  border-radius: 10px;
  border: transparent;
  height: 12px;
}

.emotion-0:-moz-meter-optimum::-moz-meter-bar {
  background: #000;
  border-radius: 10px;
}

.emotion-0::-webkit-meter-optimum-value {
  background: #000;
  border-radius: 10px;
}

<meter
    aria-hidden="true"
    aria-label=""
    class=" emotion-0"
    max="100"
    value="30"
  />
</DocumentFragment>
`;

exports[`<Meter /> snapshots renders largeVariant state correctly GapFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  background: #CCC;
  border: 1px solid #7F7F7F;
  box-sizing: border-box;
  height: 0.5rem;
  width: 100%;
  border-radius: 10px;
  border: transparent;
  height: 12px;
}

@media not all and (min-resolution:.001dpcm) {
  @supports (-webkit-appearance: none) {
    .emotion-0 {
      border: none;
      will-change: transform;
      overflow: hidden;
    }
  }
}

@-moz-document url-prefix() {
  .emotion-0 {
    overflow: hidden;
  }
}

.emotion-0::-webkit-meter-bar {
  background: #CCC;
  border: 1px solid #7F7F7F;
  box-sizing: border-box;
  height: 0.5rem;
  width: 100%;
  border-radius: 10px;
  border: transparent;
  height: 12px;
}

.emotion-0:-moz-meter-optimum::-moz-meter-bar {
  background: #000;
  border-radius: 10px;
}

.emotion-0::-webkit-meter-optimum-value {
  background: #000;
  border-radius: 10px;
}

<meter
    aria-hidden="true"
    aria-label=""
    class=" emotion-0"
    max="100"
    value="30"
  />
</DocumentFragment>
`;

exports[`<Meter /> snapshots renders largeVariant state correctly GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  background: #CCC;
  border: 1px solid #7F7F7F;
  box-sizing: border-box;
  height: 0.5rem;
  width: 100%;
  border-radius: 10px;
  border: transparent;
  height: 12px;
}

@media not all and (min-resolution:.001dpcm) {
  @supports (-webkit-appearance: none) {
    .emotion-0 {
      border: none;
      will-change: transform;
      overflow: hidden;
    }
  }
}

@-moz-document url-prefix() {
  .emotion-0 {
    overflow: hidden;
  }
}

.emotion-0::-webkit-meter-bar {
  background: #CCC;
  border: 1px solid #7F7F7F;
  box-sizing: border-box;
  height: 0.5rem;
  width: 100%;
  border-radius: 10px;
  border: transparent;
  height: 12px;
}

.emotion-0:-moz-meter-optimum::-moz-meter-bar {
  background: #000;
  border-radius: 10px;
}

.emotion-0::-webkit-meter-optimum-value {
  background: #000;
  border-radius: 10px;
}

<meter
    aria-hidden="true"
    aria-label=""
    class=" emotion-0"
    max="100"
    value="30"
  />
</DocumentFragment>
`;

exports[`<Meter /> snapshots renders largeVariant state correctly OldNavy 1`] = `
<DocumentFragment>
  .emotion-0 {
  background: #CCC;
  border: 1px solid #7F7F7F;
  box-sizing: border-box;
  height: 0.5rem;
  width: 100%;
  border-radius: 10px;
  border: transparent;
  height: 12px;
}

@media not all and (min-resolution:.001dpcm) {
  @supports (-webkit-appearance: none) {
    .emotion-0 {
      border: none;
      will-change: transform;
      overflow: hidden;
    }
  }
}

@-moz-document url-prefix() {
  .emotion-0 {
    overflow: hidden;
  }
}

.emotion-0::-webkit-meter-bar {
  background: #CCC;
  border: 1px solid #7F7F7F;
  box-sizing: border-box;
  height: 0.5rem;
  width: 100%;
  border-radius: 10px;
  border: transparent;
  height: 12px;
}

.emotion-0:-moz-meter-optimum::-moz-meter-bar {
  background: #000;
  border-radius: 10px;
}

.emotion-0::-webkit-meter-optimum-value {
  background: #000;
  border-radius: 10px;
}

<meter
    aria-hidden="true"
    aria-label=""
    class=" emotion-0"
    max="100"
    value="30"
  />
</DocumentFragment>
`;

exports[`<Meter /> snapshots renders largeVariant state correctly OldNavy in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  background: #CCC;
  border: 1px solid #7F7F7F;
  box-sizing: border-box;
  height: 0.5rem;
  width: 100%;
  border-radius: 10px;
  border: transparent;
  height: 12px;
}

@media not all and (min-resolution:.001dpcm) {
  @supports (-webkit-appearance: none) {
    .emotion-0 {
      border: none;
      will-change: transform;
      overflow: hidden;
    }
  }
}

@-moz-document url-prefix() {
  .emotion-0 {
    overflow: hidden;
  }
}

.emotion-0::-webkit-meter-bar {
  background: #CCC;
  border: 1px solid #7F7F7F;
  box-sizing: border-box;
  height: 0.5rem;
  width: 100%;
  border-radius: 10px;
  border: transparent;
  height: 12px;
}

.emotion-0:-moz-meter-optimum::-moz-meter-bar {
  background: #000;
  border-radius: 10px;
}

.emotion-0::-webkit-meter-optimum-value {
  background: #000;
  border-radius: 10px;
}

<meter
    aria-hidden="true"
    aria-label=""
    class=" emotion-0"
    max="100"
    value="30"
  />
</DocumentFragment>
`;
