// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`<Notification /> notification snapshot tests for inverse styles renders custom border styles state correctly Athleta 1`] = `
<DocumentFragment>
  .emotion-0 {
  transition-property: opacity;
  transition-duration: 250ms;
  transition-timing-function: cubic-bezier(0.25, 0, 0.25, 1);
  transition-delay: 0ms;
  opacity: 1;
}

.emotion-1 {
  line-height: 1.25;
  padding: 0.5rem;
  margin-bottom: 1.125rem;
  border-style: solid;
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  font-family: 'Avenir Next','Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 0.875rem;
  color: #FFFFFF;
  border-color: red;
  border-width: 1px 1px 1px 8px;
}

.emotion-2 {
  display: inline-block;
  height: 20px;
  width: 20px;
  min-height: 20px;
  min-width: 20px;
}

.emotion-2 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-2 svg path {
  fill: #5DABF5;
}

.emotion-2 svg rect {
  fill: #5DABF5;
}

.emotion-3 {
  padding-left: 0.75rem;
}

<div
    class="emotion-0"
  >
    <div
      aria-live="polite"
      class="emotion-1"
      kind="information"
    >
      <span
        aria-hidden="true"
        class="emotion-2"
      >
        <svg
          viewBox="0 0 33 33"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M16.961 32.338c-8.836 0-16-7.163-16-16 0-8.836 7.164-16 16-16 8.837 0 16 7.164 16 16 0 8.837-7.163 16-16 16zm.19-20.969c1.321 0 2.31-.993 2.31-2.32 0-1.32-.991-2.31-2.31-2.31-1.32 0-2.3.99-2.3 2.31 0 1.327.978 2.32 2.3 2.32zm-1.915 13.827h3.84V13.628h-3.84v11.568z"
            fill="#5CABF7"
            fill-rule="evenodd"
          />
        </svg>
      </span>
      <span
        class="emotion-3"
      />
    </div>
  </div>
</DocumentFragment>
`;

exports[`<Notification /> notification snapshot tests for inverse styles renders custom border styles state correctly Athleta in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  transition-property: opacity;
  transition-duration: 250ms;
  transition-timing-function: cubic-bezier(0.25, 0, 0.25, 1);
  transition-delay: 0ms;
  opacity: 1;
}

.emotion-1 {
  line-height: 1.25;
  padding: 0.5rem;
  margin-bottom: 1.125rem;
  border-style: solid;
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 0.875rem;
  color: #FFFFFF;
  border-color: red;
  border-width: 1px 1px 1px 8px;
}

.emotion-2 {
  display: inline-block;
  height: 20px;
  width: 20px;
  min-height: 20px;
  min-width: 20px;
}

.emotion-2 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-2 svg path {
  fill: #5DABF5;
}

.emotion-2 svg rect {
  fill: #5DABF5;
}

.emotion-3 {
  padding-left: 0.75rem;
}

<div
    class="emotion-0"
  >
    <div
      aria-live="polite"
      class="emotion-1"
      kind="information"
    >
      <span
        aria-hidden="true"
        class="emotion-2"
      >
        <svg
          viewBox="0 0 33 33"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M16.961 32.338c-8.836 0-16-7.163-16-16 0-8.836 7.164-16 16-16 8.837 0 16 7.164 16 16 0 8.837-7.163 16-16 16zm.19-20.969c1.321 0 2.31-.993 2.31-2.32 0-1.32-.991-2.31-2.31-2.31-1.32 0-2.3.99-2.3 2.31 0 1.327.978 2.32 2.3 2.32zm-1.915 13.827h3.84V13.628h-3.84v11.568z"
            fill="#5CABF7"
            fill-rule="evenodd"
          />
        </svg>
      </span>
      <span
        class="emotion-3"
      />
    </div>
  </div>
</DocumentFragment>
`;

exports[`<Notification /> notification snapshot tests for inverse styles renders custom border styles state correctly BananaRepublic 1`] = `
<DocumentFragment>
  .emotion-0 {
  transition-property: opacity;
  transition-duration: 250ms;
  transition-timing-function: cubic-bezier(0.25, 0, 0.25, 1);
  transition-delay: 0ms;
  opacity: 1;
}

.emotion-1 {
  line-height: 1.25;
  padding: 0.5rem;
  margin-bottom: 1.125rem;
  border-style: solid;
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  font-family: 'Lynstone',Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 0.8125rem;
  color: #F6F4EB;
  border-color: red;
  border-width: 1px 1px 1px 8px;
}

.emotion-2 {
  display: inline-block;
  height: 20px;
  width: 20px;
  min-height: 20px;
  min-width: 20px;
}

.emotion-2 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-2 svg path {
  fill: #5DABF5;
}

.emotion-2 svg rect {
  fill: #5DABF5;
}

.emotion-3 {
  padding-left: 0.75rem;
}

<div
    class="emotion-0"
  >
    <div
      aria-live="polite"
      class="emotion-1"
      kind="information"
    >
      <span
        aria-hidden="true"
        class="emotion-2"
      >
        <svg
          viewBox="0 0 33 33"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M16.961 32.338c-8.836 0-16-7.163-16-16 0-8.836 7.164-16 16-16 8.837 0 16 7.164 16 16 0 8.837-7.163 16-16 16zm.19-20.969c1.321 0 2.31-.993 2.31-2.32 0-1.32-.991-2.31-2.31-2.31-1.32 0-2.3.99-2.3 2.31 0 1.327.978 2.32 2.3 2.32zm-1.915 13.827h3.84V13.628h-3.84v11.568z"
            fill="#5CABF7"
            fill-rule="evenodd"
          />
        </svg>
      </span>
      <span
        class="emotion-3"
      />
    </div>
  </div>
</DocumentFragment>
`;

exports[`<Notification /> notification snapshot tests for inverse styles renders custom border styles state correctly BananaRepublic in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  transition-property: opacity;
  transition-duration: 250ms;
  transition-timing-function: cubic-bezier(0.25, 0, 0.25, 1);
  transition-delay: 0ms;
  opacity: 1;
}

.emotion-1 {
  line-height: 1.25;
  padding: 0.5rem;
  margin-bottom: 1.125rem;
  border-style: solid;
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 0.875rem;
  color: #F6F4EB;
  border-color: red;
  border-width: 1px 1px 1px 8px;
}

.emotion-2 {
  display: inline-block;
  height: 20px;
  width: 20px;
  min-height: 20px;
  min-width: 20px;
}

.emotion-2 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-2 svg path {
  fill: #5DABF5;
}

.emotion-2 svg rect {
  fill: #5DABF5;
}

.emotion-3 {
  padding-left: 0.75rem;
}

<div
    class="emotion-0"
  >
    <div
      aria-live="polite"
      class="emotion-1"
      kind="information"
    >
      <span
        aria-hidden="true"
        class="emotion-2"
      >
        <svg
          viewBox="0 0 33 33"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M16.961 32.338c-8.836 0-16-7.163-16-16 0-8.836 7.164-16 16-16 8.837 0 16 7.164 16 16 0 8.837-7.163 16-16 16zm.19-20.969c1.321 0 2.31-.993 2.31-2.32 0-1.32-.991-2.31-2.31-2.31-1.32 0-2.3.99-2.3 2.31 0 1.327.978 2.32 2.3 2.32zm-1.915 13.827h3.84V13.628h-3.84v11.568z"
            fill="#5CABF7"
            fill-rule="evenodd"
          />
        </svg>
      </span>
      <span
        class="emotion-3"
      />
    </div>
  </div>
</DocumentFragment>
`;

exports[`<Notification /> notification snapshot tests for inverse styles renders custom border styles state correctly BananaRepublicFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  transition-property: opacity;
  transition-duration: 250ms;
  transition-timing-function: cubic-bezier(0.25, 0, 0.25, 1);
  transition-delay: 0ms;
  opacity: 1;
}

.emotion-1 {
  line-height: 1.25;
  padding: 0.5rem;
  margin-bottom: 1.125rem;
  border-style: solid;
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  font-family: 'Lynstone',Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 0.8125rem;
  color: #F6F4EB;
  border-color: red;
  border-width: 1px 1px 1px 8px;
}

.emotion-2 {
  display: inline-block;
  height: 20px;
  width: 20px;
  min-height: 20px;
  min-width: 20px;
}

.emotion-2 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-2 svg path {
  fill: #5DABF5;
}

.emotion-2 svg rect {
  fill: #5DABF5;
}

.emotion-3 {
  padding-left: 0.75rem;
}

<div
    class="emotion-0"
  >
    <div
      aria-live="polite"
      class="emotion-1"
      kind="information"
    >
      <span
        aria-hidden="true"
        class="emotion-2"
      >
        <svg
          viewBox="0 0 33 33"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M16.961 32.338c-8.836 0-16-7.163-16-16 0-8.836 7.164-16 16-16 8.837 0 16 7.164 16 16 0 8.837-7.163 16-16 16zm.19-20.969c1.321 0 2.31-.993 2.31-2.32 0-1.32-.991-2.31-2.31-2.31-1.32 0-2.3.99-2.3 2.31 0 1.327.978 2.32 2.3 2.32zm-1.915 13.827h3.84V13.628h-3.84v11.568z"
            fill="#5CABF7"
            fill-rule="evenodd"
          />
        </svg>
      </span>
      <span
        class="emotion-3"
      />
    </div>
  </div>
</DocumentFragment>
`;

exports[`<Notification /> notification snapshot tests for inverse styles renders custom border styles state correctly BananaRepublicFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  transition-property: opacity;
  transition-duration: 250ms;
  transition-timing-function: cubic-bezier(0.25, 0, 0.25, 1);
  transition-delay: 0ms;
  opacity: 1;
}

.emotion-1 {
  line-height: 1.25;
  padding: 0.5rem;
  margin-bottom: 1.125rem;
  border-style: solid;
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 0.875rem;
  color: #F6F4EB;
  border-color: red;
  border-width: 1px 1px 1px 8px;
}

.emotion-2 {
  display: inline-block;
  height: 20px;
  width: 20px;
  min-height: 20px;
  min-width: 20px;
}

.emotion-2 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-2 svg path {
  fill: #5DABF5;
}

.emotion-2 svg rect {
  fill: #5DABF5;
}

.emotion-3 {
  padding-left: 0.75rem;
}

<div
    class="emotion-0"
  >
    <div
      aria-live="polite"
      class="emotion-1"
      kind="information"
    >
      <span
        aria-hidden="true"
        class="emotion-2"
      >
        <svg
          viewBox="0 0 33 33"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M16.961 32.338c-8.836 0-16-7.163-16-16 0-8.836 7.164-16 16-16 8.837 0 16 7.164 16 16 0 8.837-7.163 16-16 16zm.19-20.969c1.321 0 2.31-.993 2.31-2.32 0-1.32-.991-2.31-2.31-2.31-1.32 0-2.3.99-2.3 2.31 0 1.327.978 2.32 2.3 2.32zm-1.915 13.827h3.84V13.628h-3.84v11.568z"
            fill="#5CABF7"
            fill-rule="evenodd"
          />
        </svg>
      </span>
      <span
        class="emotion-3"
      />
    </div>
  </div>
</DocumentFragment>
`;

exports[`<Notification /> notification snapshot tests for inverse styles renders custom border styles state correctly Gap 1`] = `
<DocumentFragment>
  .emotion-0 {
  transition-property: opacity;
  transition-duration: 250ms;
  transition-timing-function: cubic-bezier(0.25, 0, 0.25, 1);
  transition-delay: 0ms;
  opacity: 1;
}

.emotion-1 {
  line-height: 1.25;
  padding: 0.5rem;
  margin-bottom: 1.125rem;
  border-style: solid;
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 0.8125rem;
  color: #FFFFFF;
  border-color: red;
  border-width: 1px 1px 1px 8px;
}

.emotion-2 {
  display: inline-block;
  height: 20px;
  width: 20px;
  min-height: 20px;
  min-width: 20px;
}

.emotion-2 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-2 svg path {
  fill: #5DABF5;
}

.emotion-2 svg rect {
  fill: #5DABF5;
}

.emotion-3 {
  padding-left: 0.75rem;
}

<div
    class="emotion-0"
  >
    <div
      aria-live="polite"
      class="emotion-1"
      kind="information"
    >
      <span
        aria-hidden="true"
        class="emotion-2"
      >
        <svg
          viewBox="0 0 33 33"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M16.961 32.338c-8.836 0-16-7.163-16-16 0-8.836 7.164-16 16-16 8.837 0 16 7.164 16 16 0 8.837-7.163 16-16 16zm.19-20.969c1.321 0 2.31-.993 2.31-2.32 0-1.32-.991-2.31-2.31-2.31-1.32 0-2.3.99-2.3 2.31 0 1.327.978 2.32 2.3 2.32zm-1.915 13.827h3.84V13.628h-3.84v11.568z"
            fill="#5CABF7"
            fill-rule="evenodd"
          />
        </svg>
      </span>
      <span
        class="emotion-3"
      />
    </div>
  </div>
</DocumentFragment>
`;

exports[`<Notification /> notification snapshot tests for inverse styles renders custom border styles state correctly Gap in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  transition-property: opacity;
  transition-duration: 250ms;
  transition-timing-function: cubic-bezier(0.25, 0, 0.25, 1);
  transition-delay: 0ms;
  opacity: 1;
}

.emotion-1 {
  line-height: 1.25;
  padding: 0.5rem;
  margin-bottom: 1.125rem;
  border-style: solid;
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 0.875rem;
  color: #FFFFFF;
  border-color: red;
  border-width: 1px 1px 1px 8px;
}

.emotion-2 {
  display: inline-block;
  height: 20px;
  width: 20px;
  min-height: 20px;
  min-width: 20px;
}

.emotion-2 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-2 svg path {
  fill: #5DABF5;
}

.emotion-2 svg rect {
  fill: #5DABF5;
}

.emotion-3 {
  padding-left: 0.75rem;
}

<div
    class="emotion-0"
  >
    <div
      aria-live="polite"
      class="emotion-1"
      kind="information"
    >
      <span
        aria-hidden="true"
        class="emotion-2"
      >
        <svg
          viewBox="0 0 33 33"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M16.961 32.338c-8.836 0-16-7.163-16-16 0-8.836 7.164-16 16-16 8.837 0 16 7.164 16 16 0 8.837-7.163 16-16 16zm.19-20.969c1.321 0 2.31-.993 2.31-2.32 0-1.32-.991-2.31-2.31-2.31-1.32 0-2.3.99-2.3 2.31 0 1.327.978 2.32 2.3 2.32zm-1.915 13.827h3.84V13.628h-3.84v11.568z"
            fill="#5CABF7"
            fill-rule="evenodd"
          />
        </svg>
      </span>
      <span
        class="emotion-3"
      />
    </div>
  </div>
</DocumentFragment>
`;

exports[`<Notification /> notification snapshot tests for inverse styles renders custom border styles state correctly GapFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  transition-property: opacity;
  transition-duration: 250ms;
  transition-timing-function: cubic-bezier(0.25, 0, 0.25, 1);
  transition-delay: 0ms;
  opacity: 1;
}

.emotion-1 {
  line-height: 1.25;
  padding: 0.5rem;
  margin-bottom: 1.125rem;
  border-style: solid;
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 0.8125rem;
  color: #FFFFFF;
  border-color: red;
  border-width: 1px 1px 1px 8px;
}

.emotion-2 {
  display: inline-block;
  height: 20px;
  width: 20px;
  min-height: 20px;
  min-width: 20px;
}

.emotion-2 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-2 svg path {
  fill: #5DABF5;
}

.emotion-2 svg rect {
  fill: #5DABF5;
}

.emotion-3 {
  padding-left: 0.75rem;
}

<div
    class="emotion-0"
  >
    <div
      aria-live="polite"
      class="emotion-1"
      kind="information"
    >
      <span
        aria-hidden="true"
        class="emotion-2"
      >
        <svg
          viewBox="0 0 33 33"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M16.961 32.338c-8.836 0-16-7.163-16-16 0-8.836 7.164-16 16-16 8.837 0 16 7.164 16 16 0 8.837-7.163 16-16 16zm.19-20.969c1.321 0 2.31-.993 2.31-2.32 0-1.32-.991-2.31-2.31-2.31-1.32 0-2.3.99-2.3 2.31 0 1.327.978 2.32 2.3 2.32zm-1.915 13.827h3.84V13.628h-3.84v11.568z"
            fill="#5CABF7"
            fill-rule="evenodd"
          />
        </svg>
      </span>
      <span
        class="emotion-3"
      />
    </div>
  </div>
</DocumentFragment>
`;

exports[`<Notification /> notification snapshot tests for inverse styles renders custom border styles state correctly GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  transition-property: opacity;
  transition-duration: 250ms;
  transition-timing-function: cubic-bezier(0.25, 0, 0.25, 1);
  transition-delay: 0ms;
  opacity: 1;
}

.emotion-1 {
  line-height: 1.25;
  padding: 0.5rem;
  margin-bottom: 1.125rem;
  border-style: solid;
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 0.875rem;
  color: #FFFFFF;
  border-color: red;
  border-width: 1px 1px 1px 8px;
}

.emotion-2 {
  display: inline-block;
  height: 20px;
  width: 20px;
  min-height: 20px;
  min-width: 20px;
}

.emotion-2 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-2 svg path {
  fill: #5DABF5;
}

.emotion-2 svg rect {
  fill: #5DABF5;
}

.emotion-3 {
  padding-left: 0.75rem;
}

<div
    class="emotion-0"
  >
    <div
      aria-live="polite"
      class="emotion-1"
      kind="information"
    >
      <span
        aria-hidden="true"
        class="emotion-2"
      >
        <svg
          viewBox="0 0 33 33"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M16.961 32.338c-8.836 0-16-7.163-16-16 0-8.836 7.164-16 16-16 8.837 0 16 7.164 16 16 0 8.837-7.163 16-16 16zm.19-20.969c1.321 0 2.31-.993 2.31-2.32 0-1.32-.991-2.31-2.31-2.31-1.32 0-2.3.99-2.3 2.31 0 1.327.978 2.32 2.3 2.32zm-1.915 13.827h3.84V13.628h-3.84v11.568z"
            fill="#5CABF7"
            fill-rule="evenodd"
          />
        </svg>
      </span>
      <span
        class="emotion-3"
      />
    </div>
  </div>
</DocumentFragment>
`;

exports[`<Notification /> notification snapshot tests for inverse styles renders custom border styles state correctly OldNavy 1`] = `
<DocumentFragment>
  .emotion-0 {
  transition-property: opacity;
  transition-duration: 250ms;
  transition-timing-function: cubic-bezier(0.25, 0, 0.25, 1);
  transition-delay: 0ms;
  opacity: 1;
}

.emotion-1 {
  line-height: 1.25;
  padding: 0.5rem;
  margin-bottom: 1.125rem;
  border-style: solid;
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  font-family: 'Gotham','Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 0.875rem;
  color: #FFFFFF;
  border-color: red;
  border-width: 1px 1px 1px 8px;
}

.emotion-2 {
  display: inline-block;
  height: 20px;
  width: 20px;
  min-height: 20px;
  min-width: 20px;
}

.emotion-2 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-2 svg path {
  fill: #5DABF5;
}

.emotion-2 svg rect {
  fill: #5DABF5;
}

.emotion-3 {
  padding-left: 0.75rem;
}

<div
    class="emotion-0"
  >
    <div
      aria-live="polite"
      class="emotion-1"
      kind="information"
    >
      <span
        aria-hidden="true"
        class="emotion-2"
      >
        <svg
          viewBox="0 0 33 33"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M16.961 32.338c-8.836 0-16-7.163-16-16 0-8.836 7.164-16 16-16 8.837 0 16 7.164 16 16 0 8.837-7.163 16-16 16zm.19-20.969c1.321 0 2.31-.993 2.31-2.32 0-1.32-.991-2.31-2.31-2.31-1.32 0-2.3.99-2.3 2.31 0 1.327.978 2.32 2.3 2.32zm-1.915 13.827h3.84V13.628h-3.84v11.568z"
            fill="#5CABF7"
            fill-rule="evenodd"
          />
        </svg>
      </span>
      <span
        class="emotion-3"
      />
    </div>
  </div>
</DocumentFragment>
`;

exports[`<Notification /> notification snapshot tests for inverse styles renders custom border styles state correctly OldNavy in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  transition-property: opacity;
  transition-duration: 250ms;
  transition-timing-function: cubic-bezier(0.25, 0, 0.25, 1);
  transition-delay: 0ms;
  opacity: 1;
}

.emotion-1 {
  line-height: 1.25;
  padding: 0.5rem;
  margin-bottom: 1.125rem;
  border-style: solid;
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 0.875rem;
  color: #FFFFFF;
  border-color: red;
  border-width: 1px 1px 1px 8px;
}

.emotion-2 {
  display: inline-block;
  height: 20px;
  width: 20px;
  min-height: 20px;
  min-width: 20px;
}

.emotion-2 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-2 svg path {
  fill: #5DABF5;
}

.emotion-2 svg rect {
  fill: #5DABF5;
}

.emotion-3 {
  padding-left: 0.75rem;
}

<div
    class="emotion-0"
  >
    <div
      aria-live="polite"
      class="emotion-1"
      kind="information"
    >
      <span
        aria-hidden="true"
        class="emotion-2"
      >
        <svg
          viewBox="0 0 33 33"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M16.961 32.338c-8.836 0-16-7.163-16-16 0-8.836 7.164-16 16-16 8.837 0 16 7.164 16 16 0 8.837-7.163 16-16 16zm.19-20.969c1.321 0 2.31-.993 2.31-2.32 0-1.32-.991-2.31-2.31-2.31-1.32 0-2.3.99-2.3 2.31 0 1.327.978 2.32 2.3 2.32zm-1.915 13.827h3.84V13.628h-3.84v11.568z"
            fill="#5CABF7"
            fill-rule="evenodd"
          />
        </svg>
      </span>
      <span
        class="emotion-3"
      />
    </div>
  </div>
</DocumentFragment>
`;

exports[`<Notification /> notification snapshot tests for inverse styles renders dismissible state correctly Athleta 1`] = `
<DocumentFragment>
  .emotion-0 {
  transition-property: opacity;
  transition-duration: 250ms;
  transition-timing-function: cubic-bezier(0.25, 0, 0.25, 1);
  transition-delay: 0ms;
  opacity: 1;
}

.emotion-1 {
  line-height: 1.25;
  padding: 0.5rem;
  margin-bottom: 1.125rem;
  border-style: solid;
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  font-family: 'Avenir Next','Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 0.875rem;
  color: #FFFFFF;
  border-color: #5DABF5;
  border-width: 2px 2px 2px 8px;
}

.emotion-2 {
  display: inline-block;
  height: 20px;
  width: 20px;
  min-height: 20px;
  min-width: 20px;
}

.emotion-2 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-2 svg path {
  fill: #5DABF5;
}

.emotion-2 svg rect {
  fill: #5DABF5;
}

.emotion-3 {
  padding-left: 0.75rem;
}

.emotion-4 {
  background: none;
  border: none;
  width: auto;
  margin: 0;
  padding: 0;
  text-align: inherit;
  font: inherit;
  padding-left: 0.65rem;
  margin-left: auto;
  opacity: 0.7;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-self: flex-start;
  -ms-flex-item-align: flex-start;
  align-self: flex-start;
}

.emotion-4:hover,
.emotion-4:focus {
  opacity: 1;
}

.emotion-5 {
  display: inline-block;
  height: 12px;
  width: 12px;
  min-height: 12px;
  min-width: 12px;
}

.emotion-5 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-5 svg path {
  fill: #fff;
}

.emotion-5 svg rect {
  fill: #fff;
}

<div
    class="emotion-0"
  >
    <div
      aria-live="polite"
      class="emotion-1"
      kind="information"
    >
      <span
        aria-hidden="true"
        class="emotion-2"
      >
        <svg
          viewBox="0 0 33 33"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M16.961 32.338c-8.836 0-16-7.163-16-16 0-8.836 7.164-16 16-16 8.837 0 16 7.164 16 16 0 8.837-7.163 16-16 16zm.19-20.969c1.321 0 2.31-.993 2.31-2.32 0-1.32-.991-2.31-2.31-2.31-1.32 0-2.3.99-2.3 2.31 0 1.327.978 2.32 2.3 2.32zm-1.915 13.827h3.84V13.628h-3.84v11.568z"
            fill="#5CABF7"
            fill-rule="evenodd"
          />
        </svg>
      </span>
      <span
        class="emotion-3"
      />
      <button
        aria-label="Close Notification"
        class="emotion-4"
        type="button"
      >
        <span
          aria-hidden="true"
          class="emotion-5"
        >
          <svg
            fill="none"
            viewBox="0 0 22 22"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              clip-rule="evenodd"
              d="M1.391 1.55603c-.585787.58579-.585786 1.53554 0 2.12133L8.71364 11 1.391 18.3226c-.585787.5858-.585786 1.5356 0 2.1214l.16503.165c.58579.5858 1.53554.5858 2.12133 0L11 13.2864l7.3226 7.3226c.5858.5858 1.5356.5858 2.1214 0l.165-.165c.5858-.5858.5858-1.5356 0-2.1214L13.2864 11l7.3226-7.32264c.5858-.58579.5858-1.53554 0-2.12133l-.165-.16503c-.5858-.585787-1.5356-.585786-2.1214 0L11 8.71364 3.67736 1.391c-.58579-.585787-1.53554-.585786-2.12133 0l-.16503.16503z"
              fill="#666"
              fill-rule="evenodd"
            />
          </svg>
        </span>
      </button>
    </div>
  </div>
</DocumentFragment>
`;

exports[`<Notification /> notification snapshot tests for inverse styles renders dismissible state correctly Athleta in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  transition-property: opacity;
  transition-duration: 250ms;
  transition-timing-function: cubic-bezier(0.25, 0, 0.25, 1);
  transition-delay: 0ms;
  opacity: 1;
}

.emotion-1 {
  line-height: 1.25;
  padding: 0.5rem;
  margin-bottom: 1.125rem;
  border-style: solid;
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 0.875rem;
  color: #FFFFFF;
  border-color: #5DABF5;
  border-width: 2px 2px 2px 8px;
}

.emotion-2 {
  display: inline-block;
  height: 20px;
  width: 20px;
  min-height: 20px;
  min-width: 20px;
}

.emotion-2 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-2 svg path {
  fill: #5DABF5;
}

.emotion-2 svg rect {
  fill: #5DABF5;
}

.emotion-3 {
  padding-left: 0.75rem;
}

.emotion-4 {
  background: none;
  border: none;
  width: auto;
  margin: 0;
  padding: 0;
  text-align: inherit;
  font: inherit;
  padding-left: 0.65rem;
  margin-left: auto;
  opacity: 0.7;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-self: flex-start;
  -ms-flex-item-align: flex-start;
  align-self: flex-start;
}

.emotion-4:hover,
.emotion-4:focus {
  opacity: 1;
}

.emotion-5 {
  display: inline-block;
  height: 12px;
  width: 12px;
  min-height: 12px;
  min-width: 12px;
}

.emotion-5 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-5 svg path {
  fill: #fff;
}

.emotion-5 svg rect {
  fill: #fff;
}

<div
    class="emotion-0"
  >
    <div
      aria-live="polite"
      class="emotion-1"
      kind="information"
    >
      <span
        aria-hidden="true"
        class="emotion-2"
      >
        <svg
          viewBox="0 0 33 33"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M16.961 32.338c-8.836 0-16-7.163-16-16 0-8.836 7.164-16 16-16 8.837 0 16 7.164 16 16 0 8.837-7.163 16-16 16zm.19-20.969c1.321 0 2.31-.993 2.31-2.32 0-1.32-.991-2.31-2.31-2.31-1.32 0-2.3.99-2.3 2.31 0 1.327.978 2.32 2.3 2.32zm-1.915 13.827h3.84V13.628h-3.84v11.568z"
            fill="#5CABF7"
            fill-rule="evenodd"
          />
        </svg>
      </span>
      <span
        class="emotion-3"
      />
      <button
        aria-label="Close Notification"
        class="emotion-4"
        type="button"
      >
        <span
          aria-hidden="true"
          class="emotion-5"
        >
          <svg
            fill="none"
            viewBox="0 0 22 22"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              clip-rule="evenodd"
              d="M1.391 1.55603c-.585787.58579-.585786 1.53554 0 2.12133L8.71364 11 1.391 18.3226c-.585787.5858-.585786 1.5356 0 2.1214l.16503.165c.58579.5858 1.53554.5858 2.12133 0L11 13.2864l7.3226 7.3226c.5858.5858 1.5356.5858 2.1214 0l.165-.165c.5858-.5858.5858-1.5356 0-2.1214L13.2864 11l7.3226-7.32264c.5858-.58579.5858-1.53554 0-2.12133l-.165-.16503c-.5858-.585787-1.5356-.585786-2.1214 0L11 8.71364 3.67736 1.391c-.58579-.585787-1.53554-.585786-2.12133 0l-.16503.16503z"
              fill="#666"
              fill-rule="evenodd"
            />
          </svg>
        </span>
      </button>
    </div>
  </div>
</DocumentFragment>
`;

exports[`<Notification /> notification snapshot tests for inverse styles renders dismissible state correctly BananaRepublic 1`] = `
<DocumentFragment>
  .emotion-0 {
  transition-property: opacity;
  transition-duration: 250ms;
  transition-timing-function: cubic-bezier(0.25, 0, 0.25, 1);
  transition-delay: 0ms;
  opacity: 1;
}

.emotion-1 {
  line-height: 1.25;
  padding: 0.5rem;
  margin-bottom: 1.125rem;
  border-style: solid;
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  font-family: 'Lynstone',Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 0.8125rem;
  color: #F6F4EB;
  border-color: #5DABF5;
  border-width: 2px 2px 2px 8px;
}

.emotion-2 {
  display: inline-block;
  height: 20px;
  width: 20px;
  min-height: 20px;
  min-width: 20px;
}

.emotion-2 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-2 svg path {
  fill: #5DABF5;
}

.emotion-2 svg rect {
  fill: #5DABF5;
}

.emotion-3 {
  padding-left: 0.75rem;
}

.emotion-4 {
  background: none;
  border: none;
  width: auto;
  margin: 0;
  padding: 0;
  text-align: inherit;
  font: inherit;
  padding-left: 0.65rem;
  margin-left: auto;
  opacity: 0.7;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-self: flex-start;
  -ms-flex-item-align: flex-start;
  align-self: flex-start;
}

.emotion-4:hover,
.emotion-4:focus {
  opacity: 1;
}

.emotion-5 {
  display: inline-block;
  height: 12px;
  width: 12px;
  min-height: 12px;
  min-width: 12px;
}

.emotion-5 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-5 svg path {
  fill: #fff;
}

.emotion-5 svg rect {
  fill: #fff;
}

<div
    class="emotion-0"
  >
    <div
      aria-live="polite"
      class="emotion-1"
      kind="information"
    >
      <span
        aria-hidden="true"
        class="emotion-2"
      >
        <svg
          viewBox="0 0 33 33"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M16.961 32.338c-8.836 0-16-7.163-16-16 0-8.836 7.164-16 16-16 8.837 0 16 7.164 16 16 0 8.837-7.163 16-16 16zm.19-20.969c1.321 0 2.31-.993 2.31-2.32 0-1.32-.991-2.31-2.31-2.31-1.32 0-2.3.99-2.3 2.31 0 1.327.978 2.32 2.3 2.32zm-1.915 13.827h3.84V13.628h-3.84v11.568z"
            fill="#5CABF7"
            fill-rule="evenodd"
          />
        </svg>
      </span>
      <span
        class="emotion-3"
      />
      <button
        aria-label="Close Notification"
        class="emotion-4"
        type="button"
      >
        <span
          aria-hidden="true"
          class="emotion-5"
        >
          <svg
            fill="none"
            viewBox="0 0 22 22"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              clip-rule="evenodd"
              d="M.0340576 1.62505L1.62505.0340576 10.9058 9.31483 20.1866.0340576l1.591 1.5909924-9.2808 9.28075 9.2808 9.2808-1.591 1.591-9.2808-9.2808-9.28075 9.2808-1.5909911-1.591L9.31483 10.9058.0340576 1.62505z"
              fill="#000000"
              fill-rule="evenodd"
            />
          </svg>
        </span>
      </button>
    </div>
  </div>
</DocumentFragment>
`;

exports[`<Notification /> notification snapshot tests for inverse styles renders dismissible state correctly BananaRepublic in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  transition-property: opacity;
  transition-duration: 250ms;
  transition-timing-function: cubic-bezier(0.25, 0, 0.25, 1);
  transition-delay: 0ms;
  opacity: 1;
}

.emotion-1 {
  line-height: 1.25;
  padding: 0.5rem;
  margin-bottom: 1.125rem;
  border-style: solid;
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 0.875rem;
  color: #F6F4EB;
  border-color: #5DABF5;
  border-width: 2px 2px 2px 8px;
}

.emotion-2 {
  display: inline-block;
  height: 20px;
  width: 20px;
  min-height: 20px;
  min-width: 20px;
}

.emotion-2 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-2 svg path {
  fill: #5DABF5;
}

.emotion-2 svg rect {
  fill: #5DABF5;
}

.emotion-3 {
  padding-left: 0.75rem;
}

.emotion-4 {
  background: none;
  border: none;
  width: auto;
  margin: 0;
  padding: 0;
  text-align: inherit;
  font: inherit;
  padding-left: 0.65rem;
  margin-left: auto;
  opacity: 0.7;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-self: flex-start;
  -ms-flex-item-align: flex-start;
  align-self: flex-start;
}

.emotion-4:hover,
.emotion-4:focus {
  opacity: 1;
}

.emotion-5 {
  display: inline-block;
  height: 12px;
  width: 12px;
  min-height: 12px;
  min-width: 12px;
}

.emotion-5 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-5 svg path {
  fill: #fff;
}

.emotion-5 svg rect {
  fill: #fff;
}

<div
    class="emotion-0"
  >
    <div
      aria-live="polite"
      class="emotion-1"
      kind="information"
    >
      <span
        aria-hidden="true"
        class="emotion-2"
      >
        <svg
          viewBox="0 0 33 33"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M16.961 32.338c-8.836 0-16-7.163-16-16 0-8.836 7.164-16 16-16 8.837 0 16 7.164 16 16 0 8.837-7.163 16-16 16zm.19-20.969c1.321 0 2.31-.993 2.31-2.32 0-1.32-.991-2.31-2.31-2.31-1.32 0-2.3.99-2.3 2.31 0 1.327.978 2.32 2.3 2.32zm-1.915 13.827h3.84V13.628h-3.84v11.568z"
            fill="#5CABF7"
            fill-rule="evenodd"
          />
        </svg>
      </span>
      <span
        class="emotion-3"
      />
      <button
        aria-label="Close Notification"
        class="emotion-4"
        type="button"
      >
        <span
          aria-hidden="true"
          class="emotion-5"
        >
          <svg
            fill="none"
            viewBox="0 0 22 22"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              clip-rule="evenodd"
              d="M.0340576 1.62505L1.62505.0340576 10.9058 9.31483 20.1866.0340576l1.591 1.5909924-9.2808 9.28075 9.2808 9.2808-1.591 1.591-9.2808-9.2808-9.28075 9.2808-1.5909911-1.591L9.31483 10.9058.0340576 1.62505z"
              fill="#000000"
              fill-rule="evenodd"
            />
          </svg>
        </span>
      </button>
    </div>
  </div>
</DocumentFragment>
`;

exports[`<Notification /> notification snapshot tests for inverse styles renders dismissible state correctly BananaRepublicFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  transition-property: opacity;
  transition-duration: 250ms;
  transition-timing-function: cubic-bezier(0.25, 0, 0.25, 1);
  transition-delay: 0ms;
  opacity: 1;
}

.emotion-1 {
  line-height: 1.25;
  padding: 0.5rem;
  margin-bottom: 1.125rem;
  border-style: solid;
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  font-family: 'Lynstone',Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 0.8125rem;
  color: #F6F4EB;
  border-color: #5DABF5;
  border-width: 2px 2px 2px 8px;
}

.emotion-2 {
  display: inline-block;
  height: 20px;
  width: 20px;
  min-height: 20px;
  min-width: 20px;
}

.emotion-2 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-2 svg path {
  fill: #5DABF5;
}

.emotion-2 svg rect {
  fill: #5DABF5;
}

.emotion-3 {
  padding-left: 0.75rem;
}

.emotion-4 {
  background: none;
  border: none;
  width: auto;
  margin: 0;
  padding: 0;
  text-align: inherit;
  font: inherit;
  padding-left: 0.65rem;
  margin-left: auto;
  opacity: 0.7;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-self: flex-start;
  -ms-flex-item-align: flex-start;
  align-self: flex-start;
}

.emotion-4:hover,
.emotion-4:focus {
  opacity: 1;
}

.emotion-5 {
  display: inline-block;
  height: 12px;
  width: 12px;
  min-height: 12px;
  min-width: 12px;
}

.emotion-5 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-5 svg path {
  fill: #fff;
}

.emotion-5 svg rect {
  fill: #fff;
}

<div
    class="emotion-0"
  >
    <div
      aria-live="polite"
      class="emotion-1"
      kind="information"
    >
      <span
        aria-hidden="true"
        class="emotion-2"
      >
        <svg
          viewBox="0 0 33 33"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M16.961 32.338c-8.836 0-16-7.163-16-16 0-8.836 7.164-16 16-16 8.837 0 16 7.164 16 16 0 8.837-7.163 16-16 16zm.19-20.969c1.321 0 2.31-.993 2.31-2.32 0-1.32-.991-2.31-2.31-2.31-1.32 0-2.3.99-2.3 2.31 0 1.327.978 2.32 2.3 2.32zm-1.915 13.827h3.84V13.628h-3.84v11.568z"
            fill="#5CABF7"
            fill-rule="evenodd"
          />
        </svg>
      </span>
      <span
        class="emotion-3"
      />
      <button
        aria-label="Close Notification"
        class="emotion-4"
        type="button"
      >
        <span
          aria-hidden="true"
          class="emotion-5"
        >
          <svg
            fill="none"
            viewBox="0 0 22 22"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              clip-rule="evenodd"
              d="M.0340576 1.62505L1.62505.0340576 10.9058 9.31483 20.1866.0340576l1.591 1.5909924-9.2808 9.28075 9.2808 9.2808-1.591 1.591-9.2808-9.2808-9.28075 9.2808-1.5909911-1.591L9.31483 10.9058.0340576 1.62505z"
              fill="#000000"
              fill-rule="evenodd"
            />
          </svg>
        </span>
      </button>
    </div>
  </div>
</DocumentFragment>
`;

exports[`<Notification /> notification snapshot tests for inverse styles renders dismissible state correctly BananaRepublicFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  transition-property: opacity;
  transition-duration: 250ms;
  transition-timing-function: cubic-bezier(0.25, 0, 0.25, 1);
  transition-delay: 0ms;
  opacity: 1;
}

.emotion-1 {
  line-height: 1.25;
  padding: 0.5rem;
  margin-bottom: 1.125rem;
  border-style: solid;
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 0.875rem;
  color: #F6F4EB;
  border-color: #5DABF5;
  border-width: 2px 2px 2px 8px;
}

.emotion-2 {
  display: inline-block;
  height: 20px;
  width: 20px;
  min-height: 20px;
  min-width: 20px;
}

.emotion-2 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-2 svg path {
  fill: #5DABF5;
}

.emotion-2 svg rect {
  fill: #5DABF5;
}

.emotion-3 {
  padding-left: 0.75rem;
}

.emotion-4 {
  background: none;
  border: none;
  width: auto;
  margin: 0;
  padding: 0;
  text-align: inherit;
  font: inherit;
  padding-left: 0.65rem;
  margin-left: auto;
  opacity: 0.7;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-self: flex-start;
  -ms-flex-item-align: flex-start;
  align-self: flex-start;
}

.emotion-4:hover,
.emotion-4:focus {
  opacity: 1;
}

.emotion-5 {
  display: inline-block;
  height: 12px;
  width: 12px;
  min-height: 12px;
  min-width: 12px;
}

.emotion-5 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-5 svg path {
  fill: #fff;
}

.emotion-5 svg rect {
  fill: #fff;
}

<div
    class="emotion-0"
  >
    <div
      aria-live="polite"
      class="emotion-1"
      kind="information"
    >
      <span
        aria-hidden="true"
        class="emotion-2"
      >
        <svg
          viewBox="0 0 33 33"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M16.961 32.338c-8.836 0-16-7.163-16-16 0-8.836 7.164-16 16-16 8.837 0 16 7.164 16 16 0 8.837-7.163 16-16 16zm.19-20.969c1.321 0 2.31-.993 2.31-2.32 0-1.32-.991-2.31-2.31-2.31-1.32 0-2.3.99-2.3 2.31 0 1.327.978 2.32 2.3 2.32zm-1.915 13.827h3.84V13.628h-3.84v11.568z"
            fill="#5CABF7"
            fill-rule="evenodd"
          />
        </svg>
      </span>
      <span
        class="emotion-3"
      />
      <button
        aria-label="Close Notification"
        class="emotion-4"
        type="button"
      >
        <span
          aria-hidden="true"
          class="emotion-5"
        >
          <svg
            fill="none"
            viewBox="0 0 22 22"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              clip-rule="evenodd"
              d="M.0340576 1.62505L1.62505.0340576 10.9058 9.31483 20.1866.0340576l1.591 1.5909924-9.2808 9.28075 9.2808 9.2808-1.591 1.591-9.2808-9.2808-9.28075 9.2808-1.5909911-1.591L9.31483 10.9058.0340576 1.62505z"
              fill="#000000"
              fill-rule="evenodd"
            />
          </svg>
        </span>
      </button>
    </div>
  </div>
</DocumentFragment>
`;

exports[`<Notification /> notification snapshot tests for inverse styles renders dismissible state correctly Gap 1`] = `
<DocumentFragment>
  .emotion-0 {
  transition-property: opacity;
  transition-duration: 250ms;
  transition-timing-function: cubic-bezier(0.25, 0, 0.25, 1);
  transition-delay: 0ms;
  opacity: 1;
}

.emotion-1 {
  line-height: 1.25;
  padding: 0.5rem;
  margin-bottom: 1.125rem;
  border-style: solid;
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 0.8125rem;
  color: #FFFFFF;
  border-color: #5DABF5;
  border-width: 2px 2px 2px 8px;
}

.emotion-2 {
  display: inline-block;
  height: 20px;
  width: 20px;
  min-height: 20px;
  min-width: 20px;
}

.emotion-2 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-2 svg path {
  fill: #5DABF5;
}

.emotion-2 svg rect {
  fill: #5DABF5;
}

.emotion-3 {
  padding-left: 0.75rem;
}

.emotion-4 {
  background: none;
  border: none;
  width: auto;
  margin: 0;
  padding: 0;
  text-align: inherit;
  font: inherit;
  padding-left: 0.65rem;
  margin-left: auto;
  opacity: 0.7;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-self: flex-start;
  -ms-flex-item-align: flex-start;
  align-self: flex-start;
}

.emotion-4:hover,
.emotion-4:focus {
  opacity: 1;
}

.emotion-5 {
  display: inline-block;
  height: 12px;
  width: 12px;
  min-height: 12px;
  min-width: 12px;
}

.emotion-5 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-5 svg path {
  fill: #fff;
}

.emotion-5 svg rect {
  fill: #fff;
}

<div
    class="emotion-0"
  >
    <div
      aria-live="polite"
      class="emotion-1"
      kind="information"
    >
      <span
        aria-hidden="true"
        class="emotion-2"
      >
        <svg
          viewBox="0 0 33 33"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M16.961 32.338c-8.836 0-16-7.163-16-16 0-8.836 7.164-16 16-16 8.837 0 16 7.164 16 16 0 8.837-7.163 16-16 16zm.19-20.969c1.321 0 2.31-.993 2.31-2.32 0-1.32-.991-2.31-2.31-2.31-1.32 0-2.3.99-2.3 2.31 0 1.327.978 2.32 2.3 2.32zm-1.915 13.827h3.84V13.628h-3.84v11.568z"
            fill="#5CABF7"
            fill-rule="evenodd"
          />
        </svg>
      </span>
      <span
        class="emotion-3"
      />
      <button
        aria-label="Close Notification"
        class="emotion-4"
        type="button"
      >
        <span
          aria-hidden="true"
          class="emotion-5"
        >
          <svg
            viewBox="0 0 10.126 10.313"
            xmlns="http://www.w3.org/2000/svg"
          >
            <g>
              <path
                d="m9.213.187.913.913-9.211 9.212-.913-.913z"
                fill="#002554"
              />
            </g>
            <g>
              <path
                d="m10.125 9.212-.912.913L0 .914.914 0z"
                fill="#002554"
              />
            </g>
          </svg>
        </span>
      </button>
    </div>
  </div>
</DocumentFragment>
`;

exports[`<Notification /> notification snapshot tests for inverse styles renders dismissible state correctly Gap in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  transition-property: opacity;
  transition-duration: 250ms;
  transition-timing-function: cubic-bezier(0.25, 0, 0.25, 1);
  transition-delay: 0ms;
  opacity: 1;
}

.emotion-1 {
  line-height: 1.25;
  padding: 0.5rem;
  margin-bottom: 1.125rem;
  border-style: solid;
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 0.875rem;
  color: #FFFFFF;
  border-color: #5DABF5;
  border-width: 2px 2px 2px 8px;
}

.emotion-2 {
  display: inline-block;
  height: 20px;
  width: 20px;
  min-height: 20px;
  min-width: 20px;
}

.emotion-2 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-2 svg path {
  fill: #5DABF5;
}

.emotion-2 svg rect {
  fill: #5DABF5;
}

.emotion-3 {
  padding-left: 0.75rem;
}

.emotion-4 {
  background: none;
  border: none;
  width: auto;
  margin: 0;
  padding: 0;
  text-align: inherit;
  font: inherit;
  padding-left: 0.65rem;
  margin-left: auto;
  opacity: 0.7;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-self: flex-start;
  -ms-flex-item-align: flex-start;
  align-self: flex-start;
}

.emotion-4:hover,
.emotion-4:focus {
  opacity: 1;
}

.emotion-5 {
  display: inline-block;
  height: 12px;
  width: 12px;
  min-height: 12px;
  min-width: 12px;
}

.emotion-5 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-5 svg path {
  fill: #fff;
}

.emotion-5 svg rect {
  fill: #fff;
}

<div
    class="emotion-0"
  >
    <div
      aria-live="polite"
      class="emotion-1"
      kind="information"
    >
      <span
        aria-hidden="true"
        class="emotion-2"
      >
        <svg
          viewBox="0 0 33 33"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M16.961 32.338c-8.836 0-16-7.163-16-16 0-8.836 7.164-16 16-16 8.837 0 16 7.164 16 16 0 8.837-7.163 16-16 16zm.19-20.969c1.321 0 2.31-.993 2.31-2.32 0-1.32-.991-2.31-2.31-2.31-1.32 0-2.3.99-2.3 2.31 0 1.327.978 2.32 2.3 2.32zm-1.915 13.827h3.84V13.628h-3.84v11.568z"
            fill="#5CABF7"
            fill-rule="evenodd"
          />
        </svg>
      </span>
      <span
        class="emotion-3"
      />
      <button
        aria-label="Close Notification"
        class="emotion-4"
        type="button"
      >
        <span
          aria-hidden="true"
          class="emotion-5"
        >
          <svg
            viewBox="0 0 10.126 10.313"
            xmlns="http://www.w3.org/2000/svg"
          >
            <g>
              <path
                d="m9.213.187.913.913-9.211 9.212-.913-.913z"
                fill="#002554"
              />
            </g>
            <g>
              <path
                d="m10.125 9.212-.912.913L0 .914.914 0z"
                fill="#002554"
              />
            </g>
          </svg>
        </span>
      </button>
    </div>
  </div>
</DocumentFragment>
`;

exports[`<Notification /> notification snapshot tests for inverse styles renders dismissible state correctly GapFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  transition-property: opacity;
  transition-duration: 250ms;
  transition-timing-function: cubic-bezier(0.25, 0, 0.25, 1);
  transition-delay: 0ms;
  opacity: 1;
}

.emotion-1 {
  line-height: 1.25;
  padding: 0.5rem;
  margin-bottom: 1.125rem;
  border-style: solid;
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 0.8125rem;
  color: #FFFFFF;
  border-color: #5DABF5;
  border-width: 2px 2px 2px 8px;
}

.emotion-2 {
  display: inline-block;
  height: 20px;
  width: 20px;
  min-height: 20px;
  min-width: 20px;
}

.emotion-2 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-2 svg path {
  fill: #5DABF5;
}

.emotion-2 svg rect {
  fill: #5DABF5;
}

.emotion-3 {
  padding-left: 0.75rem;
}

.emotion-4 {
  background: none;
  border: none;
  width: auto;
  margin: 0;
  padding: 0;
  text-align: inherit;
  font: inherit;
  padding-left: 0.65rem;
  margin-left: auto;
  opacity: 0.7;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-self: flex-start;
  -ms-flex-item-align: flex-start;
  align-self: flex-start;
}

.emotion-4:hover,
.emotion-4:focus {
  opacity: 1;
}

.emotion-5 {
  display: inline-block;
  height: 12px;
  width: 12px;
  min-height: 12px;
  min-width: 12px;
}

.emotion-5 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-5 svg path {
  fill: #fff;
}

.emotion-5 svg rect {
  fill: #fff;
}

<div
    class="emotion-0"
  >
    <div
      aria-live="polite"
      class="emotion-1"
      kind="information"
    >
      <span
        aria-hidden="true"
        class="emotion-2"
      >
        <svg
          viewBox="0 0 33 33"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M16.961 32.338c-8.836 0-16-7.163-16-16 0-8.836 7.164-16 16-16 8.837 0 16 7.164 16 16 0 8.837-7.163 16-16 16zm.19-20.969c1.321 0 2.31-.993 2.31-2.32 0-1.32-.991-2.31-2.31-2.31-1.32 0-2.3.99-2.3 2.31 0 1.327.978 2.32 2.3 2.32zm-1.915 13.827h3.84V13.628h-3.84v11.568z"
            fill="#5CABF7"
            fill-rule="evenodd"
          />
        </svg>
      </span>
      <span
        class="emotion-3"
      />
      <button
        aria-label="Close Notification"
        class="emotion-4"
        type="button"
      >
        <span
          aria-hidden="true"
          class="emotion-5"
        >
          <svg
            viewBox="0 0 10.126 10.313"
            xmlns="http://www.w3.org/2000/svg"
          >
            <g>
              <path
                d="m9.213.187.913.913-9.211 9.212-.913-.913z"
                fill="#002554"
              />
            </g>
            <g>
              <path
                d="m10.125 9.212-.912.913L0 .914.914 0z"
                fill="#002554"
              />
            </g>
          </svg>
        </span>
      </button>
    </div>
  </div>
</DocumentFragment>
`;

exports[`<Notification /> notification snapshot tests for inverse styles renders dismissible state correctly GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  transition-property: opacity;
  transition-duration: 250ms;
  transition-timing-function: cubic-bezier(0.25, 0, 0.25, 1);
  transition-delay: 0ms;
  opacity: 1;
}

.emotion-1 {
  line-height: 1.25;
  padding: 0.5rem;
  margin-bottom: 1.125rem;
  border-style: solid;
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 0.875rem;
  color: #FFFFFF;
  border-color: #5DABF5;
  border-width: 2px 2px 2px 8px;
}

.emotion-2 {
  display: inline-block;
  height: 20px;
  width: 20px;
  min-height: 20px;
  min-width: 20px;
}

.emotion-2 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-2 svg path {
  fill: #5DABF5;
}

.emotion-2 svg rect {
  fill: #5DABF5;
}

.emotion-3 {
  padding-left: 0.75rem;
}

.emotion-4 {
  background: none;
  border: none;
  width: auto;
  margin: 0;
  padding: 0;
  text-align: inherit;
  font: inherit;
  padding-left: 0.65rem;
  margin-left: auto;
  opacity: 0.7;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-self: flex-start;
  -ms-flex-item-align: flex-start;
  align-self: flex-start;
}

.emotion-4:hover,
.emotion-4:focus {
  opacity: 1;
}

.emotion-5 {
  display: inline-block;
  height: 12px;
  width: 12px;
  min-height: 12px;
  min-width: 12px;
}

.emotion-5 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-5 svg path {
  fill: #fff;
}

.emotion-5 svg rect {
  fill: #fff;
}

<div
    class="emotion-0"
  >
    <div
      aria-live="polite"
      class="emotion-1"
      kind="information"
    >
      <span
        aria-hidden="true"
        class="emotion-2"
      >
        <svg
          viewBox="0 0 33 33"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M16.961 32.338c-8.836 0-16-7.163-16-16 0-8.836 7.164-16 16-16 8.837 0 16 7.164 16 16 0 8.837-7.163 16-16 16zm.19-20.969c1.321 0 2.31-.993 2.31-2.32 0-1.32-.991-2.31-2.31-2.31-1.32 0-2.3.99-2.3 2.31 0 1.327.978 2.32 2.3 2.32zm-1.915 13.827h3.84V13.628h-3.84v11.568z"
            fill="#5CABF7"
            fill-rule="evenodd"
          />
        </svg>
      </span>
      <span
        class="emotion-3"
      />
      <button
        aria-label="Close Notification"
        class="emotion-4"
        type="button"
      >
        <span
          aria-hidden="true"
          class="emotion-5"
        >
          <svg
            viewBox="0 0 10.126 10.313"
            xmlns="http://www.w3.org/2000/svg"
          >
            <g>
              <path
                d="m9.213.187.913.913-9.211 9.212-.913-.913z"
                fill="#002554"
              />
            </g>
            <g>
              <path
                d="m10.125 9.212-.912.913L0 .914.914 0z"
                fill="#002554"
              />
            </g>
          </svg>
        </span>
      </button>
    </div>
  </div>
</DocumentFragment>
`;

exports[`<Notification /> notification snapshot tests for inverse styles renders dismissible state correctly OldNavy 1`] = `
<DocumentFragment>
  .emotion-0 {
  transition-property: opacity;
  transition-duration: 250ms;
  transition-timing-function: cubic-bezier(0.25, 0, 0.25, 1);
  transition-delay: 0ms;
  opacity: 1;
}

.emotion-1 {
  line-height: 1.25;
  padding: 0.5rem;
  margin-bottom: 1.125rem;
  border-style: solid;
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  font-family: 'Gotham','Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 0.875rem;
  color: #FFFFFF;
  border-color: #5DABF5;
  border-width: 2px 2px 2px 8px;
}

.emotion-2 {
  display: inline-block;
  height: 20px;
  width: 20px;
  min-height: 20px;
  min-width: 20px;
}

.emotion-2 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-2 svg path {
  fill: #5DABF5;
}

.emotion-2 svg rect {
  fill: #5DABF5;
}

.emotion-3 {
  padding-left: 0.75rem;
}

.emotion-4 {
  background: none;
  border: none;
  width: auto;
  margin: 0;
  padding: 0;
  text-align: inherit;
  font: inherit;
  padding-left: 0.65rem;
  margin-left: auto;
  opacity: 0.7;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-self: flex-start;
  -ms-flex-item-align: flex-start;
  align-self: flex-start;
}

.emotion-4:hover,
.emotion-4:focus {
  opacity: 1;
}

.emotion-5 {
  display: inline-block;
  height: 12px;
  width: 12px;
  min-height: 12px;
  min-width: 12px;
}

.emotion-5 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-5 svg path {
  fill: #fff;
}

.emotion-5 svg rect {
  fill: #fff;
}

<div
    class="emotion-0"
  >
    <div
      aria-live="polite"
      class="emotion-1"
      kind="information"
    >
      <span
        aria-hidden="true"
        class="emotion-2"
      >
        <svg
          viewBox="0 0 33 33"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M16.961 32.338c-8.836 0-16-7.163-16-16 0-8.836 7.164-16 16-16 8.837 0 16 7.164 16 16 0 8.837-7.163 16-16 16zm.19-20.969c1.321 0 2.31-.993 2.31-2.32 0-1.32-.991-2.31-2.31-2.31-1.32 0-2.3.99-2.3 2.31 0 1.327.978 2.32 2.3 2.32zm-1.915 13.827h3.84V13.628h-3.84v11.568z"
            fill="#5CABF7"
            fill-rule="evenodd"
          />
        </svg>
      </span>
      <span
        class="emotion-3"
      />
      <button
        aria-label="Close Notification"
        class="emotion-4"
        type="button"
      >
        <span
          aria-hidden="true"
          class="emotion-5"
        >
          <svg
            fill="none"
            viewBox="0 0 22 22"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              clip-rule="evenodd"
              d="M.0339966 1.62505L1.62499.0340576 10.9058 9.31483 20.1865.0340576l1.591 1.5909924-9.2807 9.28075 9.2807 9.2808-1.591 1.591-9.2807-9.2808-9.28081 9.2808-1.5909922-1.591L9.31477 10.9058.0339966 1.62505z"
              fill="#043863"
              fill-rule="evenodd"
            />
          </svg>
        </span>
      </button>
    </div>
  </div>
</DocumentFragment>
`;

exports[`<Notification /> notification snapshot tests for inverse styles renders dismissible state correctly OldNavy in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  transition-property: opacity;
  transition-duration: 250ms;
  transition-timing-function: cubic-bezier(0.25, 0, 0.25, 1);
  transition-delay: 0ms;
  opacity: 1;
}

.emotion-1 {
  line-height: 1.25;
  padding: 0.5rem;
  margin-bottom: 1.125rem;
  border-style: solid;
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 0.875rem;
  color: #FFFFFF;
  border-color: #5DABF5;
  border-width: 2px 2px 2px 8px;
}

.emotion-2 {
  display: inline-block;
  height: 20px;
  width: 20px;
  min-height: 20px;
  min-width: 20px;
}

.emotion-2 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-2 svg path {
  fill: #5DABF5;
}

.emotion-2 svg rect {
  fill: #5DABF5;
}

.emotion-3 {
  padding-left: 0.75rem;
}

.emotion-4 {
  background: none;
  border: none;
  width: auto;
  margin: 0;
  padding: 0;
  text-align: inherit;
  font: inherit;
  padding-left: 0.65rem;
  margin-left: auto;
  opacity: 0.7;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-self: flex-start;
  -ms-flex-item-align: flex-start;
  align-self: flex-start;
}

.emotion-4:hover,
.emotion-4:focus {
  opacity: 1;
}

.emotion-5 {
  display: inline-block;
  height: 12px;
  width: 12px;
  min-height: 12px;
  min-width: 12px;
}

.emotion-5 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-5 svg path {
  fill: #fff;
}

.emotion-5 svg rect {
  fill: #fff;
}

<div
    class="emotion-0"
  >
    <div
      aria-live="polite"
      class="emotion-1"
      kind="information"
    >
      <span
        aria-hidden="true"
        class="emotion-2"
      >
        <svg
          viewBox="0 0 33 33"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M16.961 32.338c-8.836 0-16-7.163-16-16 0-8.836 7.164-16 16-16 8.837 0 16 7.164 16 16 0 8.837-7.163 16-16 16zm.19-20.969c1.321 0 2.31-.993 2.31-2.32 0-1.32-.991-2.31-2.31-2.31-1.32 0-2.3.99-2.3 2.31 0 1.327.978 2.32 2.3 2.32zm-1.915 13.827h3.84V13.628h-3.84v11.568z"
            fill="#5CABF7"
            fill-rule="evenodd"
          />
        </svg>
      </span>
      <span
        class="emotion-3"
      />
      <button
        aria-label="Close Notification"
        class="emotion-4"
        type="button"
      >
        <span
          aria-hidden="true"
          class="emotion-5"
        >
          <svg
            fill="none"
            viewBox="0 0 22 22"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              clip-rule="evenodd"
              d="M.0339966 1.62505L1.62499.0340576 10.9058 9.31483 20.1865.0340576l1.591 1.5909924-9.2807 9.28075 9.2807 9.2808-1.591 1.591-9.2807-9.2808-9.28081 9.2808-1.5909922-1.591L9.31477 10.9058.0339966 1.62505z"
              fill="#043863"
              fill-rule="evenodd"
            />
          </svg>
        </span>
      </button>
    </div>
  </div>
</DocumentFragment>
`;

exports[`<Notification /> notification snapshot tests for inverse styles renders error state correctly Athleta 1`] = `
<DocumentFragment>
  .emotion-0 {
  transition-property: opacity;
  transition-duration: 250ms;
  transition-timing-function: cubic-bezier(0.25, 0, 0.25, 1);
  transition-delay: 0ms;
  opacity: 1;
}

.emotion-1 {
  line-height: 1.25;
  padding: 0.5rem;
  margin-bottom: 1.125rem;
  border-style: solid;
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  font-family: 'Avenir Next','Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 0.875rem;
  color: #FFFFFF;
  border-color: #FD6565;
  border-width: 2px 2px 2px 8px;
}

.emotion-2 {
  display: inline-block;
  height: 20px;
  width: 20px;
  min-height: 20px;
  min-width: 20px;
}

.emotion-2 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-2 svg path {
  fill: #FD6565;
}

.emotion-2 svg rect {
  fill: #FD6565;
}

.emotion-3 {
  padding-left: 0.75rem;
}

<div
    class="emotion-0"
  >
    <div
      aria-live="assertive"
      class="emotion-1"
      kind="error"
    >
      <span
        aria-hidden="true"
        class="emotion-2"
      >
        <svg
          viewBox="0 0 33 33"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M16.039.661c8.836 0 16 7.164 16 16 0 8.837-7.164 16-16 16-8.837 0-16-7.163-16-16 0-8.836 7.163-16 16-16zm-.19 20.97c-1.321 0-2.31.993-2.31 2.32 0 1.32.991 2.31 2.31 2.31 1.32 0 2.3-.99 2.3-2.31 0-1.327-.978-2.32-2.3-2.32zm1.915-13.827h-3.84v11.568h3.84V7.804z"
            fill="#D00000"
            fill-rule="evenodd"
          />
        </svg>
      </span>
      <span
        class="emotion-3"
      />
    </div>
  </div>
</DocumentFragment>
`;

exports[`<Notification /> notification snapshot tests for inverse styles renders error state correctly Athleta in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  transition-property: opacity;
  transition-duration: 250ms;
  transition-timing-function: cubic-bezier(0.25, 0, 0.25, 1);
  transition-delay: 0ms;
  opacity: 1;
}

.emotion-1 {
  line-height: 1.25;
  padding: 0.5rem;
  margin-bottom: 1.125rem;
  border-style: solid;
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 0.875rem;
  color: #FFFFFF;
  border-color: #FD6565;
  border-width: 2px 2px 2px 8px;
}

.emotion-2 {
  display: inline-block;
  height: 20px;
  width: 20px;
  min-height: 20px;
  min-width: 20px;
}

.emotion-2 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-2 svg path {
  fill: #FD6565;
}

.emotion-2 svg rect {
  fill: #FD6565;
}

.emotion-3 {
  padding-left: 0.75rem;
}

<div
    class="emotion-0"
  >
    <div
      aria-live="assertive"
      class="emotion-1"
      kind="error"
    >
      <span
        aria-hidden="true"
        class="emotion-2"
      >
        <svg
          viewBox="0 0 33 33"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M16.039.661c8.836 0 16 7.164 16 16 0 8.837-7.164 16-16 16-8.837 0-16-7.163-16-16 0-8.836 7.163-16 16-16zm-.19 20.97c-1.321 0-2.31.993-2.31 2.32 0 1.32.991 2.31 2.31 2.31 1.32 0 2.3-.99 2.3-2.31 0-1.327-.978-2.32-2.3-2.32zm1.915-13.827h-3.84v11.568h3.84V7.804z"
            fill="#D00000"
            fill-rule="evenodd"
          />
        </svg>
      </span>
      <span
        class="emotion-3"
      />
    </div>
  </div>
</DocumentFragment>
`;

exports[`<Notification /> notification snapshot tests for inverse styles renders error state correctly BananaRepublic 1`] = `
<DocumentFragment>
  .emotion-0 {
  transition-property: opacity;
  transition-duration: 250ms;
  transition-timing-function: cubic-bezier(0.25, 0, 0.25, 1);
  transition-delay: 0ms;
  opacity: 1;
}

.emotion-1 {
  line-height: 1.25;
  padding: 0.5rem;
  margin-bottom: 1.125rem;
  border-style: solid;
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  font-family: 'Lynstone',Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 0.8125rem;
  color: #F6F4EB;
  border-color: #FD6565;
  border-width: 2px 2px 2px 8px;
}

.emotion-2 {
  display: inline-block;
  height: 20px;
  width: 20px;
  min-height: 20px;
  min-width: 20px;
}

.emotion-2 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-2 svg path {
  fill: #FD6565;
}

.emotion-2 svg rect {
  fill: #FD6565;
}

.emotion-3 {
  padding-left: 0.75rem;
}

<div
    class="emotion-0"
  >
    <div
      aria-live="assertive"
      class="emotion-1"
      kind="error"
    >
      <span
        aria-hidden="true"
        class="emotion-2"
      >
        <svg
          viewBox="0 0 33 33"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M16.039.661c8.836 0 16 7.164 16 16 0 8.837-7.164 16-16 16-8.837 0-16-7.163-16-16 0-8.836 7.163-16 16-16zm-.19 20.97c-1.321 0-2.31.993-2.31 2.32 0 1.32.991 2.31 2.31 2.31 1.32 0 2.3-.99 2.3-2.31 0-1.327-.978-2.32-2.3-2.32zm1.915-13.827h-3.84v11.568h3.84V7.804z"
            fill="#D00000"
            fill-rule="evenodd"
          />
        </svg>
      </span>
      <span
        class="emotion-3"
      />
    </div>
  </div>
</DocumentFragment>
`;

exports[`<Notification /> notification snapshot tests for inverse styles renders error state correctly BananaRepublic in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  transition-property: opacity;
  transition-duration: 250ms;
  transition-timing-function: cubic-bezier(0.25, 0, 0.25, 1);
  transition-delay: 0ms;
  opacity: 1;
}

.emotion-1 {
  line-height: 1.25;
  padding: 0.5rem;
  margin-bottom: 1.125rem;
  border-style: solid;
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 0.875rem;
  color: #F6F4EB;
  border-color: #FD6565;
  border-width: 2px 2px 2px 8px;
}

.emotion-2 {
  display: inline-block;
  height: 20px;
  width: 20px;
  min-height: 20px;
  min-width: 20px;
}

.emotion-2 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-2 svg path {
  fill: #FD6565;
}

.emotion-2 svg rect {
  fill: #FD6565;
}

.emotion-3 {
  padding-left: 0.75rem;
}

<div
    class="emotion-0"
  >
    <div
      aria-live="assertive"
      class="emotion-1"
      kind="error"
    >
      <span
        aria-hidden="true"
        class="emotion-2"
      >
        <svg
          viewBox="0 0 33 33"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M16.039.661c8.836 0 16 7.164 16 16 0 8.837-7.164 16-16 16-8.837 0-16-7.163-16-16 0-8.836 7.163-16 16-16zm-.19 20.97c-1.321 0-2.31.993-2.31 2.32 0 1.32.991 2.31 2.31 2.31 1.32 0 2.3-.99 2.3-2.31 0-1.327-.978-2.32-2.3-2.32zm1.915-13.827h-3.84v11.568h3.84V7.804z"
            fill="#D00000"
            fill-rule="evenodd"
          />
        </svg>
      </span>
      <span
        class="emotion-3"
      />
    </div>
  </div>
</DocumentFragment>
`;

exports[`<Notification /> notification snapshot tests for inverse styles renders error state correctly BananaRepublicFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  transition-property: opacity;
  transition-duration: 250ms;
  transition-timing-function: cubic-bezier(0.25, 0, 0.25, 1);
  transition-delay: 0ms;
  opacity: 1;
}

.emotion-1 {
  line-height: 1.25;
  padding: 0.5rem;
  margin-bottom: 1.125rem;
  border-style: solid;
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  font-family: 'Lynstone',Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 0.8125rem;
  color: #F6F4EB;
  border-color: #FD6565;
  border-width: 2px 2px 2px 8px;
}

.emotion-2 {
  display: inline-block;
  height: 20px;
  width: 20px;
  min-height: 20px;
  min-width: 20px;
}

.emotion-2 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-2 svg path {
  fill: #FD6565;
}

.emotion-2 svg rect {
  fill: #FD6565;
}

.emotion-3 {
  padding-left: 0.75rem;
}

<div
    class="emotion-0"
  >
    <div
      aria-live="assertive"
      class="emotion-1"
      kind="error"
    >
      <span
        aria-hidden="true"
        class="emotion-2"
      >
        <svg
          viewBox="0 0 33 33"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M16.039.661c8.836 0 16 7.164 16 16 0 8.837-7.164 16-16 16-8.837 0-16-7.163-16-16 0-8.836 7.163-16 16-16zm-.19 20.97c-1.321 0-2.31.993-2.31 2.32 0 1.32.991 2.31 2.31 2.31 1.32 0 2.3-.99 2.3-2.31 0-1.327-.978-2.32-2.3-2.32zm1.915-13.827h-3.84v11.568h3.84V7.804z"
            fill="#D00000"
            fill-rule="evenodd"
          />
        </svg>
      </span>
      <span
        class="emotion-3"
      />
    </div>
  </div>
</DocumentFragment>
`;

exports[`<Notification /> notification snapshot tests for inverse styles renders error state correctly BananaRepublicFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  transition-property: opacity;
  transition-duration: 250ms;
  transition-timing-function: cubic-bezier(0.25, 0, 0.25, 1);
  transition-delay: 0ms;
  opacity: 1;
}

.emotion-1 {
  line-height: 1.25;
  padding: 0.5rem;
  margin-bottom: 1.125rem;
  border-style: solid;
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 0.875rem;
  color: #F6F4EB;
  border-color: #FD6565;
  border-width: 2px 2px 2px 8px;
}

.emotion-2 {
  display: inline-block;
  height: 20px;
  width: 20px;
  min-height: 20px;
  min-width: 20px;
}

.emotion-2 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-2 svg path {
  fill: #FD6565;
}

.emotion-2 svg rect {
  fill: #FD6565;
}

.emotion-3 {
  padding-left: 0.75rem;
}

<div
    class="emotion-0"
  >
    <div
      aria-live="assertive"
      class="emotion-1"
      kind="error"
    >
      <span
        aria-hidden="true"
        class="emotion-2"
      >
        <svg
          viewBox="0 0 33 33"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M16.039.661c8.836 0 16 7.164 16 16 0 8.837-7.164 16-16 16-8.837 0-16-7.163-16-16 0-8.836 7.163-16 16-16zm-.19 20.97c-1.321 0-2.31.993-2.31 2.32 0 1.32.991 2.31 2.31 2.31 1.32 0 2.3-.99 2.3-2.31 0-1.327-.978-2.32-2.3-2.32zm1.915-13.827h-3.84v11.568h3.84V7.804z"
            fill="#D00000"
            fill-rule="evenodd"
          />
        </svg>
      </span>
      <span
        class="emotion-3"
      />
    </div>
  </div>
</DocumentFragment>
`;

exports[`<Notification /> notification snapshot tests for inverse styles renders error state correctly Gap 1`] = `
<DocumentFragment>
  .emotion-0 {
  transition-property: opacity;
  transition-duration: 250ms;
  transition-timing-function: cubic-bezier(0.25, 0, 0.25, 1);
  transition-delay: 0ms;
  opacity: 1;
}

.emotion-1 {
  line-height: 1.25;
  padding: 0.5rem;
  margin-bottom: 1.125rem;
  border-style: solid;
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 0.8125rem;
  color: #FFFFFF;
  border-color: #FD6565;
  border-width: 2px 2px 2px 8px;
}

.emotion-2 {
  display: inline-block;
  height: 20px;
  width: 20px;
  min-height: 20px;
  min-width: 20px;
}

.emotion-2 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-2 svg path {
  fill: #FD6565;
}

.emotion-2 svg rect {
  fill: #FD6565;
}

.emotion-3 {
  padding-left: 0.75rem;
}

<div
    class="emotion-0"
  >
    <div
      aria-live="assertive"
      class="emotion-1"
      kind="error"
    >
      <span
        aria-hidden="true"
        class="emotion-2"
      >
        <svg
          viewBox="0 0 33 33"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M16.039.661c8.836 0 16 7.164 16 16 0 8.837-7.164 16-16 16-8.837 0-16-7.163-16-16 0-8.836 7.163-16 16-16zm-.19 20.97c-1.321 0-2.31.993-2.31 2.32 0 1.32.991 2.31 2.31 2.31 1.32 0 2.3-.99 2.3-2.31 0-1.327-.978-2.32-2.3-2.32zm1.915-13.827h-3.84v11.568h3.84V7.804z"
            fill="#D00000"
            fill-rule="evenodd"
          />
        </svg>
      </span>
      <span
        class="emotion-3"
      />
    </div>
  </div>
</DocumentFragment>
`;

exports[`<Notification /> notification snapshot tests for inverse styles renders error state correctly Gap in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  transition-property: opacity;
  transition-duration: 250ms;
  transition-timing-function: cubic-bezier(0.25, 0, 0.25, 1);
  transition-delay: 0ms;
  opacity: 1;
}

.emotion-1 {
  line-height: 1.25;
  padding: 0.5rem;
  margin-bottom: 1.125rem;
  border-style: solid;
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 0.875rem;
  color: #FFFFFF;
  border-color: #FD6565;
  border-width: 2px 2px 2px 8px;
}

.emotion-2 {
  display: inline-block;
  height: 20px;
  width: 20px;
  min-height: 20px;
  min-width: 20px;
}

.emotion-2 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-2 svg path {
  fill: #FD6565;
}

.emotion-2 svg rect {
  fill: #FD6565;
}

.emotion-3 {
  padding-left: 0.75rem;
}

<div
    class="emotion-0"
  >
    <div
      aria-live="assertive"
      class="emotion-1"
      kind="error"
    >
      <span
        aria-hidden="true"
        class="emotion-2"
      >
        <svg
          viewBox="0 0 33 33"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M16.039.661c8.836 0 16 7.164 16 16 0 8.837-7.164 16-16 16-8.837 0-16-7.163-16-16 0-8.836 7.163-16 16-16zm-.19 20.97c-1.321 0-2.31.993-2.31 2.32 0 1.32.991 2.31 2.31 2.31 1.32 0 2.3-.99 2.3-2.31 0-1.327-.978-2.32-2.3-2.32zm1.915-13.827h-3.84v11.568h3.84V7.804z"
            fill="#D00000"
            fill-rule="evenodd"
          />
        </svg>
      </span>
      <span
        class="emotion-3"
      />
    </div>
  </div>
</DocumentFragment>
`;

exports[`<Notification /> notification snapshot tests for inverse styles renders error state correctly GapFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  transition-property: opacity;
  transition-duration: 250ms;
  transition-timing-function: cubic-bezier(0.25, 0, 0.25, 1);
  transition-delay: 0ms;
  opacity: 1;
}

.emotion-1 {
  line-height: 1.25;
  padding: 0.5rem;
  margin-bottom: 1.125rem;
  border-style: solid;
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 0.8125rem;
  color: #FFFFFF;
  border-color: #FD6565;
  border-width: 2px 2px 2px 8px;
}

.emotion-2 {
  display: inline-block;
  height: 20px;
  width: 20px;
  min-height: 20px;
  min-width: 20px;
}

.emotion-2 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-2 svg path {
  fill: #FD6565;
}

.emotion-2 svg rect {
  fill: #FD6565;
}

.emotion-3 {
  padding-left: 0.75rem;
}

<div
    class="emotion-0"
  >
    <div
      aria-live="assertive"
      class="emotion-1"
      kind="error"
    >
      <span
        aria-hidden="true"
        class="emotion-2"
      >
        <svg
          viewBox="0 0 33 33"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M16.039.661c8.836 0 16 7.164 16 16 0 8.837-7.164 16-16 16-8.837 0-16-7.163-16-16 0-8.836 7.163-16 16-16zm-.19 20.97c-1.321 0-2.31.993-2.31 2.32 0 1.32.991 2.31 2.31 2.31 1.32 0 2.3-.99 2.3-2.31 0-1.327-.978-2.32-2.3-2.32zm1.915-13.827h-3.84v11.568h3.84V7.804z"
            fill="#D00000"
            fill-rule="evenodd"
          />
        </svg>
      </span>
      <span
        class="emotion-3"
      />
    </div>
  </div>
</DocumentFragment>
`;

exports[`<Notification /> notification snapshot tests for inverse styles renders error state correctly GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  transition-property: opacity;
  transition-duration: 250ms;
  transition-timing-function: cubic-bezier(0.25, 0, 0.25, 1);
  transition-delay: 0ms;
  opacity: 1;
}

.emotion-1 {
  line-height: 1.25;
  padding: 0.5rem;
  margin-bottom: 1.125rem;
  border-style: solid;
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 0.875rem;
  color: #FFFFFF;
  border-color: #FD6565;
  border-width: 2px 2px 2px 8px;
}

.emotion-2 {
  display: inline-block;
  height: 20px;
  width: 20px;
  min-height: 20px;
  min-width: 20px;
}

.emotion-2 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-2 svg path {
  fill: #FD6565;
}

.emotion-2 svg rect {
  fill: #FD6565;
}

.emotion-3 {
  padding-left: 0.75rem;
}

<div
    class="emotion-0"
  >
    <div
      aria-live="assertive"
      class="emotion-1"
      kind="error"
    >
      <span
        aria-hidden="true"
        class="emotion-2"
      >
        <svg
          viewBox="0 0 33 33"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M16.039.661c8.836 0 16 7.164 16 16 0 8.837-7.164 16-16 16-8.837 0-16-7.163-16-16 0-8.836 7.163-16 16-16zm-.19 20.97c-1.321 0-2.31.993-2.31 2.32 0 1.32.991 2.31 2.31 2.31 1.32 0 2.3-.99 2.3-2.31 0-1.327-.978-2.32-2.3-2.32zm1.915-13.827h-3.84v11.568h3.84V7.804z"
            fill="#D00000"
            fill-rule="evenodd"
          />
        </svg>
      </span>
      <span
        class="emotion-3"
      />
    </div>
  </div>
</DocumentFragment>
`;

exports[`<Notification /> notification snapshot tests for inverse styles renders error state correctly OldNavy 1`] = `
<DocumentFragment>
  .emotion-0 {
  transition-property: opacity;
  transition-duration: 250ms;
  transition-timing-function: cubic-bezier(0.25, 0, 0.25, 1);
  transition-delay: 0ms;
  opacity: 1;
}

.emotion-1 {
  line-height: 1.25;
  padding: 0.5rem;
  margin-bottom: 1.125rem;
  border-style: solid;
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  font-family: 'Gotham','Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 0.875rem;
  color: #FFFFFF;
  border-color: #FD6565;
  border-width: 2px 2px 2px 8px;
}

.emotion-2 {
  display: inline-block;
  height: 20px;
  width: 20px;
  min-height: 20px;
  min-width: 20px;
}

.emotion-2 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-2 svg path {
  fill: #FD6565;
}

.emotion-2 svg rect {
  fill: #FD6565;
}

.emotion-3 {
  padding-left: 0.75rem;
}

<div
    class="emotion-0"
  >
    <div
      aria-live="assertive"
      class="emotion-1"
      kind="error"
    >
      <span
        aria-hidden="true"
        class="emotion-2"
      >
        <svg
          viewBox="0 0 33 33"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M16.039.661c8.836 0 16 7.164 16 16 0 8.837-7.164 16-16 16-8.837 0-16-7.163-16-16 0-8.836 7.163-16 16-16zm-.19 20.97c-1.321 0-2.31.993-2.31 2.32 0 1.32.991 2.31 2.31 2.31 1.32 0 2.3-.99 2.3-2.31 0-1.327-.978-2.32-2.3-2.32zm1.915-13.827h-3.84v11.568h3.84V7.804z"
            fill="#D00000"
            fill-rule="evenodd"
          />
        </svg>
      </span>
      <span
        class="emotion-3"
      />
    </div>
  </div>
</DocumentFragment>
`;

exports[`<Notification /> notification snapshot tests for inverse styles renders error state correctly OldNavy in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  transition-property: opacity;
  transition-duration: 250ms;
  transition-timing-function: cubic-bezier(0.25, 0, 0.25, 1);
  transition-delay: 0ms;
  opacity: 1;
}

.emotion-1 {
  line-height: 1.25;
  padding: 0.5rem;
  margin-bottom: 1.125rem;
  border-style: solid;
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 0.875rem;
  color: #FFFFFF;
  border-color: #FD6565;
  border-width: 2px 2px 2px 8px;
}

.emotion-2 {
  display: inline-block;
  height: 20px;
  width: 20px;
  min-height: 20px;
  min-width: 20px;
}

.emotion-2 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-2 svg path {
  fill: #FD6565;
}

.emotion-2 svg rect {
  fill: #FD6565;
}

.emotion-3 {
  padding-left: 0.75rem;
}

<div
    class="emotion-0"
  >
    <div
      aria-live="assertive"
      class="emotion-1"
      kind="error"
    >
      <span
        aria-hidden="true"
        class="emotion-2"
      >
        <svg
          viewBox="0 0 33 33"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M16.039.661c8.836 0 16 7.164 16 16 0 8.837-7.164 16-16 16-8.837 0-16-7.163-16-16 0-8.836 7.163-16 16-16zm-.19 20.97c-1.321 0-2.31.993-2.31 2.32 0 1.32.991 2.31 2.31 2.31 1.32 0 2.3-.99 2.3-2.31 0-1.327-.978-2.32-2.3-2.32zm1.915-13.827h-3.84v11.568h3.84V7.804z"
            fill="#D00000"
            fill-rule="evenodd"
          />
        </svg>
      </span>
      <span
        class="emotion-3"
      />
    </div>
  </div>
</DocumentFragment>
`;

exports[`<Notification /> notification snapshot tests for inverse styles renders information state correctly Athleta 1`] = `
<DocumentFragment>
  .emotion-0 {
  transition-property: opacity;
  transition-duration: 250ms;
  transition-timing-function: cubic-bezier(0.25, 0, 0.25, 1);
  transition-delay: 0ms;
  opacity: 1;
}

.emotion-1 {
  line-height: 1.25;
  padding: 0.5rem;
  margin-bottom: 1.125rem;
  border-style: solid;
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  font-family: 'Avenir Next','Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 0.875rem;
  color: #FFFFFF;
  border-color: #5DABF5;
  border-width: 2px 2px 2px 8px;
}

.emotion-2 {
  display: inline-block;
  height: 20px;
  width: 20px;
  min-height: 20px;
  min-width: 20px;
}

.emotion-2 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-2 svg path {
  fill: #5DABF5;
}

.emotion-2 svg rect {
  fill: #5DABF5;
}

.emotion-3 {
  padding-left: 0.75rem;
}

<div
    class="emotion-0"
  >
    <div
      aria-live="polite"
      class="emotion-1"
      kind="information"
    >
      <span
        aria-hidden="true"
        class="emotion-2"
      >
        <svg
          viewBox="0 0 33 33"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M16.961 32.338c-8.836 0-16-7.163-16-16 0-8.836 7.164-16 16-16 8.837 0 16 7.164 16 16 0 8.837-7.163 16-16 16zm.19-20.969c1.321 0 2.31-.993 2.31-2.32 0-1.32-.991-2.31-2.31-2.31-1.32 0-2.3.99-2.3 2.31 0 1.327.978 2.32 2.3 2.32zm-1.915 13.827h3.84V13.628h-3.84v11.568z"
            fill="#5CABF7"
            fill-rule="evenodd"
          />
        </svg>
      </span>
      <span
        class="emotion-3"
      />
    </div>
  </div>
</DocumentFragment>
`;

exports[`<Notification /> notification snapshot tests for inverse styles renders information state correctly Athleta in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  transition-property: opacity;
  transition-duration: 250ms;
  transition-timing-function: cubic-bezier(0.25, 0, 0.25, 1);
  transition-delay: 0ms;
  opacity: 1;
}

.emotion-1 {
  line-height: 1.25;
  padding: 0.5rem;
  margin-bottom: 1.125rem;
  border-style: solid;
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 0.875rem;
  color: #FFFFFF;
  border-color: #5DABF5;
  border-width: 2px 2px 2px 8px;
}

.emotion-2 {
  display: inline-block;
  height: 20px;
  width: 20px;
  min-height: 20px;
  min-width: 20px;
}

.emotion-2 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-2 svg path {
  fill: #5DABF5;
}

.emotion-2 svg rect {
  fill: #5DABF5;
}

.emotion-3 {
  padding-left: 0.75rem;
}

<div
    class="emotion-0"
  >
    <div
      aria-live="polite"
      class="emotion-1"
      kind="information"
    >
      <span
        aria-hidden="true"
        class="emotion-2"
      >
        <svg
          viewBox="0 0 33 33"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M16.961 32.338c-8.836 0-16-7.163-16-16 0-8.836 7.164-16 16-16 8.837 0 16 7.164 16 16 0 8.837-7.163 16-16 16zm.19-20.969c1.321 0 2.31-.993 2.31-2.32 0-1.32-.991-2.31-2.31-2.31-1.32 0-2.3.99-2.3 2.31 0 1.327.978 2.32 2.3 2.32zm-1.915 13.827h3.84V13.628h-3.84v11.568z"
            fill="#5CABF7"
            fill-rule="evenodd"
          />
        </svg>
      </span>
      <span
        class="emotion-3"
      />
    </div>
  </div>
</DocumentFragment>
`;

exports[`<Notification /> notification snapshot tests for inverse styles renders information state correctly BananaRepublic 1`] = `
<DocumentFragment>
  .emotion-0 {
  transition-property: opacity;
  transition-duration: 250ms;
  transition-timing-function: cubic-bezier(0.25, 0, 0.25, 1);
  transition-delay: 0ms;
  opacity: 1;
}

.emotion-1 {
  line-height: 1.25;
  padding: 0.5rem;
  margin-bottom: 1.125rem;
  border-style: solid;
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  font-family: 'Lynstone',Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 0.8125rem;
  color: #F6F4EB;
  border-color: #5DABF5;
  border-width: 2px 2px 2px 8px;
}

.emotion-2 {
  display: inline-block;
  height: 20px;
  width: 20px;
  min-height: 20px;
  min-width: 20px;
}

.emotion-2 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-2 svg path {
  fill: #5DABF5;
}

.emotion-2 svg rect {
  fill: #5DABF5;
}

.emotion-3 {
  padding-left: 0.75rem;
}

<div
    class="emotion-0"
  >
    <div
      aria-live="polite"
      class="emotion-1"
      kind="information"
    >
      <span
        aria-hidden="true"
        class="emotion-2"
      >
        <svg
          viewBox="0 0 33 33"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M16.961 32.338c-8.836 0-16-7.163-16-16 0-8.836 7.164-16 16-16 8.837 0 16 7.164 16 16 0 8.837-7.163 16-16 16zm.19-20.969c1.321 0 2.31-.993 2.31-2.32 0-1.32-.991-2.31-2.31-2.31-1.32 0-2.3.99-2.3 2.31 0 1.327.978 2.32 2.3 2.32zm-1.915 13.827h3.84V13.628h-3.84v11.568z"
            fill="#5CABF7"
            fill-rule="evenodd"
          />
        </svg>
      </span>
      <span
        class="emotion-3"
      />
    </div>
  </div>
</DocumentFragment>
`;

exports[`<Notification /> notification snapshot tests for inverse styles renders information state correctly BananaRepublic in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  transition-property: opacity;
  transition-duration: 250ms;
  transition-timing-function: cubic-bezier(0.25, 0, 0.25, 1);
  transition-delay: 0ms;
  opacity: 1;
}

.emotion-1 {
  line-height: 1.25;
  padding: 0.5rem;
  margin-bottom: 1.125rem;
  border-style: solid;
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 0.875rem;
  color: #F6F4EB;
  border-color: #5DABF5;
  border-width: 2px 2px 2px 8px;
}

.emotion-2 {
  display: inline-block;
  height: 20px;
  width: 20px;
  min-height: 20px;
  min-width: 20px;
}

.emotion-2 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-2 svg path {
  fill: #5DABF5;
}

.emotion-2 svg rect {
  fill: #5DABF5;
}

.emotion-3 {
  padding-left: 0.75rem;
}

<div
    class="emotion-0"
  >
    <div
      aria-live="polite"
      class="emotion-1"
      kind="information"
    >
      <span
        aria-hidden="true"
        class="emotion-2"
      >
        <svg
          viewBox="0 0 33 33"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M16.961 32.338c-8.836 0-16-7.163-16-16 0-8.836 7.164-16 16-16 8.837 0 16 7.164 16 16 0 8.837-7.163 16-16 16zm.19-20.969c1.321 0 2.31-.993 2.31-2.32 0-1.32-.991-2.31-2.31-2.31-1.32 0-2.3.99-2.3 2.31 0 1.327.978 2.32 2.3 2.32zm-1.915 13.827h3.84V13.628h-3.84v11.568z"
            fill="#5CABF7"
            fill-rule="evenodd"
          />
        </svg>
      </span>
      <span
        class="emotion-3"
      />
    </div>
  </div>
</DocumentFragment>
`;

exports[`<Notification /> notification snapshot tests for inverse styles renders information state correctly BananaRepublicFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  transition-property: opacity;
  transition-duration: 250ms;
  transition-timing-function: cubic-bezier(0.25, 0, 0.25, 1);
  transition-delay: 0ms;
  opacity: 1;
}

.emotion-1 {
  line-height: 1.25;
  padding: 0.5rem;
  margin-bottom: 1.125rem;
  border-style: solid;
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  font-family: 'Lynstone',Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 0.8125rem;
  color: #F6F4EB;
  border-color: #5DABF5;
  border-width: 2px 2px 2px 8px;
}

.emotion-2 {
  display: inline-block;
  height: 20px;
  width: 20px;
  min-height: 20px;
  min-width: 20px;
}

.emotion-2 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-2 svg path {
  fill: #5DABF5;
}

.emotion-2 svg rect {
  fill: #5DABF5;
}

.emotion-3 {
  padding-left: 0.75rem;
}

<div
    class="emotion-0"
  >
    <div
      aria-live="polite"
      class="emotion-1"
      kind="information"
    >
      <span
        aria-hidden="true"
        class="emotion-2"
      >
        <svg
          viewBox="0 0 33 33"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M16.961 32.338c-8.836 0-16-7.163-16-16 0-8.836 7.164-16 16-16 8.837 0 16 7.164 16 16 0 8.837-7.163 16-16 16zm.19-20.969c1.321 0 2.31-.993 2.31-2.32 0-1.32-.991-2.31-2.31-2.31-1.32 0-2.3.99-2.3 2.31 0 1.327.978 2.32 2.3 2.32zm-1.915 13.827h3.84V13.628h-3.84v11.568z"
            fill="#5CABF7"
            fill-rule="evenodd"
          />
        </svg>
      </span>
      <span
        class="emotion-3"
      />
    </div>
  </div>
</DocumentFragment>
`;

exports[`<Notification /> notification snapshot tests for inverse styles renders information state correctly BananaRepublicFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  transition-property: opacity;
  transition-duration: 250ms;
  transition-timing-function: cubic-bezier(0.25, 0, 0.25, 1);
  transition-delay: 0ms;
  opacity: 1;
}

.emotion-1 {
  line-height: 1.25;
  padding: 0.5rem;
  margin-bottom: 1.125rem;
  border-style: solid;
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 0.875rem;
  color: #F6F4EB;
  border-color: #5DABF5;
  border-width: 2px 2px 2px 8px;
}

.emotion-2 {
  display: inline-block;
  height: 20px;
  width: 20px;
  min-height: 20px;
  min-width: 20px;
}

.emotion-2 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-2 svg path {
  fill: #5DABF5;
}

.emotion-2 svg rect {
  fill: #5DABF5;
}

.emotion-3 {
  padding-left: 0.75rem;
}

<div
    class="emotion-0"
  >
    <div
      aria-live="polite"
      class="emotion-1"
      kind="information"
    >
      <span
        aria-hidden="true"
        class="emotion-2"
      >
        <svg
          viewBox="0 0 33 33"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M16.961 32.338c-8.836 0-16-7.163-16-16 0-8.836 7.164-16 16-16 8.837 0 16 7.164 16 16 0 8.837-7.163 16-16 16zm.19-20.969c1.321 0 2.31-.993 2.31-2.32 0-1.32-.991-2.31-2.31-2.31-1.32 0-2.3.99-2.3 2.31 0 1.327.978 2.32 2.3 2.32zm-1.915 13.827h3.84V13.628h-3.84v11.568z"
            fill="#5CABF7"
            fill-rule="evenodd"
          />
        </svg>
      </span>
      <span
        class="emotion-3"
      />
    </div>
  </div>
</DocumentFragment>
`;

exports[`<Notification /> notification snapshot tests for inverse styles renders information state correctly Gap 1`] = `
<DocumentFragment>
  .emotion-0 {
  transition-property: opacity;
  transition-duration: 250ms;
  transition-timing-function: cubic-bezier(0.25, 0, 0.25, 1);
  transition-delay: 0ms;
  opacity: 1;
}

.emotion-1 {
  line-height: 1.25;
  padding: 0.5rem;
  margin-bottom: 1.125rem;
  border-style: solid;
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 0.8125rem;
  color: #FFFFFF;
  border-color: #5DABF5;
  border-width: 2px 2px 2px 8px;
}

.emotion-2 {
  display: inline-block;
  height: 20px;
  width: 20px;
  min-height: 20px;
  min-width: 20px;
}

.emotion-2 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-2 svg path {
  fill: #5DABF5;
}

.emotion-2 svg rect {
  fill: #5DABF5;
}

.emotion-3 {
  padding-left: 0.75rem;
}

<div
    class="emotion-0"
  >
    <div
      aria-live="polite"
      class="emotion-1"
      kind="information"
    >
      <span
        aria-hidden="true"
        class="emotion-2"
      >
        <svg
          viewBox="0 0 33 33"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M16.961 32.338c-8.836 0-16-7.163-16-16 0-8.836 7.164-16 16-16 8.837 0 16 7.164 16 16 0 8.837-7.163 16-16 16zm.19-20.969c1.321 0 2.31-.993 2.31-2.32 0-1.32-.991-2.31-2.31-2.31-1.32 0-2.3.99-2.3 2.31 0 1.327.978 2.32 2.3 2.32zm-1.915 13.827h3.84V13.628h-3.84v11.568z"
            fill="#5CABF7"
            fill-rule="evenodd"
          />
        </svg>
      </span>
      <span
        class="emotion-3"
      />
    </div>
  </div>
</DocumentFragment>
`;

exports[`<Notification /> notification snapshot tests for inverse styles renders information state correctly Gap in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  transition-property: opacity;
  transition-duration: 250ms;
  transition-timing-function: cubic-bezier(0.25, 0, 0.25, 1);
  transition-delay: 0ms;
  opacity: 1;
}

.emotion-1 {
  line-height: 1.25;
  padding: 0.5rem;
  margin-bottom: 1.125rem;
  border-style: solid;
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 0.875rem;
  color: #FFFFFF;
  border-color: #5DABF5;
  border-width: 2px 2px 2px 8px;
}

.emotion-2 {
  display: inline-block;
  height: 20px;
  width: 20px;
  min-height: 20px;
  min-width: 20px;
}

.emotion-2 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-2 svg path {
  fill: #5DABF5;
}

.emotion-2 svg rect {
  fill: #5DABF5;
}

.emotion-3 {
  padding-left: 0.75rem;
}

<div
    class="emotion-0"
  >
    <div
      aria-live="polite"
      class="emotion-1"
      kind="information"
    >
      <span
        aria-hidden="true"
        class="emotion-2"
      >
        <svg
          viewBox="0 0 33 33"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M16.961 32.338c-8.836 0-16-7.163-16-16 0-8.836 7.164-16 16-16 8.837 0 16 7.164 16 16 0 8.837-7.163 16-16 16zm.19-20.969c1.321 0 2.31-.993 2.31-2.32 0-1.32-.991-2.31-2.31-2.31-1.32 0-2.3.99-2.3 2.31 0 1.327.978 2.32 2.3 2.32zm-1.915 13.827h3.84V13.628h-3.84v11.568z"
            fill="#5CABF7"
            fill-rule="evenodd"
          />
        </svg>
      </span>
      <span
        class="emotion-3"
      />
    </div>
  </div>
</DocumentFragment>
`;

exports[`<Notification /> notification snapshot tests for inverse styles renders information state correctly GapFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  transition-property: opacity;
  transition-duration: 250ms;
  transition-timing-function: cubic-bezier(0.25, 0, 0.25, 1);
  transition-delay: 0ms;
  opacity: 1;
}

.emotion-1 {
  line-height: 1.25;
  padding: 0.5rem;
  margin-bottom: 1.125rem;
  border-style: solid;
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 0.8125rem;
  color: #FFFFFF;
  border-color: #5DABF5;
  border-width: 2px 2px 2px 8px;
}

.emotion-2 {
  display: inline-block;
  height: 20px;
  width: 20px;
  min-height: 20px;
  min-width: 20px;
}

.emotion-2 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-2 svg path {
  fill: #5DABF5;
}

.emotion-2 svg rect {
  fill: #5DABF5;
}

.emotion-3 {
  padding-left: 0.75rem;
}

<div
    class="emotion-0"
  >
    <div
      aria-live="polite"
      class="emotion-1"
      kind="information"
    >
      <span
        aria-hidden="true"
        class="emotion-2"
      >
        <svg
          viewBox="0 0 33 33"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M16.961 32.338c-8.836 0-16-7.163-16-16 0-8.836 7.164-16 16-16 8.837 0 16 7.164 16 16 0 8.837-7.163 16-16 16zm.19-20.969c1.321 0 2.31-.993 2.31-2.32 0-1.32-.991-2.31-2.31-2.31-1.32 0-2.3.99-2.3 2.31 0 1.327.978 2.32 2.3 2.32zm-1.915 13.827h3.84V13.628h-3.84v11.568z"
            fill="#5CABF7"
            fill-rule="evenodd"
          />
        </svg>
      </span>
      <span
        class="emotion-3"
      />
    </div>
  </div>
</DocumentFragment>
`;

exports[`<Notification /> notification snapshot tests for inverse styles renders information state correctly GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  transition-property: opacity;
  transition-duration: 250ms;
  transition-timing-function: cubic-bezier(0.25, 0, 0.25, 1);
  transition-delay: 0ms;
  opacity: 1;
}

.emotion-1 {
  line-height: 1.25;
  padding: 0.5rem;
  margin-bottom: 1.125rem;
  border-style: solid;
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 0.875rem;
  color: #FFFFFF;
  border-color: #5DABF5;
  border-width: 2px 2px 2px 8px;
}

.emotion-2 {
  display: inline-block;
  height: 20px;
  width: 20px;
  min-height: 20px;
  min-width: 20px;
}

.emotion-2 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-2 svg path {
  fill: #5DABF5;
}

.emotion-2 svg rect {
  fill: #5DABF5;
}

.emotion-3 {
  padding-left: 0.75rem;
}

<div
    class="emotion-0"
  >
    <div
      aria-live="polite"
      class="emotion-1"
      kind="information"
    >
      <span
        aria-hidden="true"
        class="emotion-2"
      >
        <svg
          viewBox="0 0 33 33"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M16.961 32.338c-8.836 0-16-7.163-16-16 0-8.836 7.164-16 16-16 8.837 0 16 7.164 16 16 0 8.837-7.163 16-16 16zm.19-20.969c1.321 0 2.31-.993 2.31-2.32 0-1.32-.991-2.31-2.31-2.31-1.32 0-2.3.99-2.3 2.31 0 1.327.978 2.32 2.3 2.32zm-1.915 13.827h3.84V13.628h-3.84v11.568z"
            fill="#5CABF7"
            fill-rule="evenodd"
          />
        </svg>
      </span>
      <span
        class="emotion-3"
      />
    </div>
  </div>
</DocumentFragment>
`;

exports[`<Notification /> notification snapshot tests for inverse styles renders information state correctly OldNavy 1`] = `
<DocumentFragment>
  .emotion-0 {
  transition-property: opacity;
  transition-duration: 250ms;
  transition-timing-function: cubic-bezier(0.25, 0, 0.25, 1);
  transition-delay: 0ms;
  opacity: 1;
}

.emotion-1 {
  line-height: 1.25;
  padding: 0.5rem;
  margin-bottom: 1.125rem;
  border-style: solid;
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  font-family: 'Gotham','Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 0.875rem;
  color: #FFFFFF;
  border-color: #5DABF5;
  border-width: 2px 2px 2px 8px;
}

.emotion-2 {
  display: inline-block;
  height: 20px;
  width: 20px;
  min-height: 20px;
  min-width: 20px;
}

.emotion-2 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-2 svg path {
  fill: #5DABF5;
}

.emotion-2 svg rect {
  fill: #5DABF5;
}

.emotion-3 {
  padding-left: 0.75rem;
}

<div
    class="emotion-0"
  >
    <div
      aria-live="polite"
      class="emotion-1"
      kind="information"
    >
      <span
        aria-hidden="true"
        class="emotion-2"
      >
        <svg
          viewBox="0 0 33 33"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M16.961 32.338c-8.836 0-16-7.163-16-16 0-8.836 7.164-16 16-16 8.837 0 16 7.164 16 16 0 8.837-7.163 16-16 16zm.19-20.969c1.321 0 2.31-.993 2.31-2.32 0-1.32-.991-2.31-2.31-2.31-1.32 0-2.3.99-2.3 2.31 0 1.327.978 2.32 2.3 2.32zm-1.915 13.827h3.84V13.628h-3.84v11.568z"
            fill="#5CABF7"
            fill-rule="evenodd"
          />
        </svg>
      </span>
      <span
        class="emotion-3"
      />
    </div>
  </div>
</DocumentFragment>
`;

exports[`<Notification /> notification snapshot tests for inverse styles renders information state correctly OldNavy in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  transition-property: opacity;
  transition-duration: 250ms;
  transition-timing-function: cubic-bezier(0.25, 0, 0.25, 1);
  transition-delay: 0ms;
  opacity: 1;
}

.emotion-1 {
  line-height: 1.25;
  padding: 0.5rem;
  margin-bottom: 1.125rem;
  border-style: solid;
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 0.875rem;
  color: #FFFFFF;
  border-color: #5DABF5;
  border-width: 2px 2px 2px 8px;
}

.emotion-2 {
  display: inline-block;
  height: 20px;
  width: 20px;
  min-height: 20px;
  min-width: 20px;
}

.emotion-2 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-2 svg path {
  fill: #5DABF5;
}

.emotion-2 svg rect {
  fill: #5DABF5;
}

.emotion-3 {
  padding-left: 0.75rem;
}

<div
    class="emotion-0"
  >
    <div
      aria-live="polite"
      class="emotion-1"
      kind="information"
    >
      <span
        aria-hidden="true"
        class="emotion-2"
      >
        <svg
          viewBox="0 0 33 33"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M16.961 32.338c-8.836 0-16-7.163-16-16 0-8.836 7.164-16 16-16 8.837 0 16 7.164 16 16 0 8.837-7.163 16-16 16zm.19-20.969c1.321 0 2.31-.993 2.31-2.32 0-1.32-.991-2.31-2.31-2.31-1.32 0-2.3.99-2.3 2.31 0 1.327.978 2.32 2.3 2.32zm-1.915 13.827h3.84V13.628h-3.84v11.568z"
            fill="#5CABF7"
            fill-rule="evenodd"
          />
        </svg>
      </span>
      <span
        class="emotion-3"
      />
    </div>
  </div>
</DocumentFragment>
`;

exports[`<Notification /> notification snapshot tests for inverse styles renders inverse error state correctly Athleta 1`] = `
<DocumentFragment>
  .emotion-0 {
  transition-property: opacity;
  transition-duration: 250ms;
  transition-timing-function: cubic-bezier(0.25, 0, 0.25, 1);
  transition-delay: 0ms;
  opacity: 1;
}

.emotion-1 {
  line-height: 1.25;
  padding: 0.5rem;
  margin-bottom: 1.125rem;
  border-style: solid;
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  font-family: 'Avenir Next','Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 0.875rem;
  color: #FFFFFF;
  border-color: #FD6565;
  border-width: 2px 2px 2px 8px;
}

.emotion-2 {
  display: inline-block;
  height: 20px;
  width: 20px;
  min-height: 20px;
  min-width: 20px;
}

.emotion-2 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-2 svg path {
  fill: #FD6565;
}

.emotion-2 svg rect {
  fill: #FD6565;
}

.emotion-3 {
  padding-left: 0.75rem;
}

<div
    class="emotion-0"
  >
    <div
      aria-live="assertive"
      class="emotion-1"
      kind="error"
    >
      <span
        aria-hidden="true"
        class="emotion-2"
      >
        <svg
          viewBox="0 0 33 33"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M16.039.661c8.836 0 16 7.164 16 16 0 8.837-7.164 16-16 16-8.837 0-16-7.163-16-16 0-8.836 7.163-16 16-16zm-.19 20.97c-1.321 0-2.31.993-2.31 2.32 0 1.32.991 2.31 2.31 2.31 1.32 0 2.3-.99 2.3-2.31 0-1.327-.978-2.32-2.3-2.32zm1.915-13.827h-3.84v11.568h3.84V7.804z"
            fill="#D00000"
            fill-rule="evenodd"
          />
        </svg>
      </span>
      <span
        class="emotion-3"
      />
    </div>
  </div>
</DocumentFragment>
`;

exports[`<Notification /> notification snapshot tests for inverse styles renders inverse error state correctly Athleta in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  transition-property: opacity;
  transition-duration: 250ms;
  transition-timing-function: cubic-bezier(0.25, 0, 0.25, 1);
  transition-delay: 0ms;
  opacity: 1;
}

.emotion-1 {
  line-height: 1.25;
  padding: 0.5rem;
  margin-bottom: 1.125rem;
  border-style: solid;
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 0.875rem;
  color: #FFFFFF;
  border-color: #FD6565;
  border-width: 2px 2px 2px 8px;
}

.emotion-2 {
  display: inline-block;
  height: 20px;
  width: 20px;
  min-height: 20px;
  min-width: 20px;
}

.emotion-2 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-2 svg path {
  fill: #FD6565;
}

.emotion-2 svg rect {
  fill: #FD6565;
}

.emotion-3 {
  padding-left: 0.75rem;
}

<div
    class="emotion-0"
  >
    <div
      aria-live="assertive"
      class="emotion-1"
      kind="error"
    >
      <span
        aria-hidden="true"
        class="emotion-2"
      >
        <svg
          viewBox="0 0 33 33"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M16.039.661c8.836 0 16 7.164 16 16 0 8.837-7.164 16-16 16-8.837 0-16-7.163-16-16 0-8.836 7.163-16 16-16zm-.19 20.97c-1.321 0-2.31.993-2.31 2.32 0 1.32.991 2.31 2.31 2.31 1.32 0 2.3-.99 2.3-2.31 0-1.327-.978-2.32-2.3-2.32zm1.915-13.827h-3.84v11.568h3.84V7.804z"
            fill="#D00000"
            fill-rule="evenodd"
          />
        </svg>
      </span>
      <span
        class="emotion-3"
      />
    </div>
  </div>
</DocumentFragment>
`;

exports[`<Notification /> notification snapshot tests for inverse styles renders inverse error state correctly BananaRepublic 1`] = `
<DocumentFragment>
  .emotion-0 {
  transition-property: opacity;
  transition-duration: 250ms;
  transition-timing-function: cubic-bezier(0.25, 0, 0.25, 1);
  transition-delay: 0ms;
  opacity: 1;
}

.emotion-1 {
  line-height: 1.25;
  padding: 0.5rem;
  margin-bottom: 1.125rem;
  border-style: solid;
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  font-family: 'Lynstone',Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 0.8125rem;
  color: #F6F4EB;
  border-color: #FD6565;
  border-width: 2px 2px 2px 8px;
}

.emotion-2 {
  display: inline-block;
  height: 20px;
  width: 20px;
  min-height: 20px;
  min-width: 20px;
}

.emotion-2 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-2 svg path {
  fill: #FD6565;
}

.emotion-2 svg rect {
  fill: #FD6565;
}

.emotion-3 {
  padding-left: 0.75rem;
}

<div
    class="emotion-0"
  >
    <div
      aria-live="assertive"
      class="emotion-1"
      kind="error"
    >
      <span
        aria-hidden="true"
        class="emotion-2"
      >
        <svg
          viewBox="0 0 33 33"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M16.039.661c8.836 0 16 7.164 16 16 0 8.837-7.164 16-16 16-8.837 0-16-7.163-16-16 0-8.836 7.163-16 16-16zm-.19 20.97c-1.321 0-2.31.993-2.31 2.32 0 1.32.991 2.31 2.31 2.31 1.32 0 2.3-.99 2.3-2.31 0-1.327-.978-2.32-2.3-2.32zm1.915-13.827h-3.84v11.568h3.84V7.804z"
            fill="#D00000"
            fill-rule="evenodd"
          />
        </svg>
      </span>
      <span
        class="emotion-3"
      />
    </div>
  </div>
</DocumentFragment>
`;

exports[`<Notification /> notification snapshot tests for inverse styles renders inverse error state correctly BananaRepublic in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  transition-property: opacity;
  transition-duration: 250ms;
  transition-timing-function: cubic-bezier(0.25, 0, 0.25, 1);
  transition-delay: 0ms;
  opacity: 1;
}

.emotion-1 {
  line-height: 1.25;
  padding: 0.5rem;
  margin-bottom: 1.125rem;
  border-style: solid;
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 0.875rem;
  color: #F6F4EB;
  border-color: #FD6565;
  border-width: 2px 2px 2px 8px;
}

.emotion-2 {
  display: inline-block;
  height: 20px;
  width: 20px;
  min-height: 20px;
  min-width: 20px;
}

.emotion-2 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-2 svg path {
  fill: #FD6565;
}

.emotion-2 svg rect {
  fill: #FD6565;
}

.emotion-3 {
  padding-left: 0.75rem;
}

<div
    class="emotion-0"
  >
    <div
      aria-live="assertive"
      class="emotion-1"
      kind="error"
    >
      <span
        aria-hidden="true"
        class="emotion-2"
      >
        <svg
          viewBox="0 0 33 33"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M16.039.661c8.836 0 16 7.164 16 16 0 8.837-7.164 16-16 16-8.837 0-16-7.163-16-16 0-8.836 7.163-16 16-16zm-.19 20.97c-1.321 0-2.31.993-2.31 2.32 0 1.32.991 2.31 2.31 2.31 1.32 0 2.3-.99 2.3-2.31 0-1.327-.978-2.32-2.3-2.32zm1.915-13.827h-3.84v11.568h3.84V7.804z"
            fill="#D00000"
            fill-rule="evenodd"
          />
        </svg>
      </span>
      <span
        class="emotion-3"
      />
    </div>
  </div>
</DocumentFragment>
`;

exports[`<Notification /> notification snapshot tests for inverse styles renders inverse error state correctly BananaRepublicFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  transition-property: opacity;
  transition-duration: 250ms;
  transition-timing-function: cubic-bezier(0.25, 0, 0.25, 1);
  transition-delay: 0ms;
  opacity: 1;
}

.emotion-1 {
  line-height: 1.25;
  padding: 0.5rem;
  margin-bottom: 1.125rem;
  border-style: solid;
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  font-family: 'Lynstone',Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 0.8125rem;
  color: #F6F4EB;
  border-color: #FD6565;
  border-width: 2px 2px 2px 8px;
}

.emotion-2 {
  display: inline-block;
  height: 20px;
  width: 20px;
  min-height: 20px;
  min-width: 20px;
}

.emotion-2 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-2 svg path {
  fill: #FD6565;
}

.emotion-2 svg rect {
  fill: #FD6565;
}

.emotion-3 {
  padding-left: 0.75rem;
}

<div
    class="emotion-0"
  >
    <div
      aria-live="assertive"
      class="emotion-1"
      kind="error"
    >
      <span
        aria-hidden="true"
        class="emotion-2"
      >
        <svg
          viewBox="0 0 33 33"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M16.039.661c8.836 0 16 7.164 16 16 0 8.837-7.164 16-16 16-8.837 0-16-7.163-16-16 0-8.836 7.163-16 16-16zm-.19 20.97c-1.321 0-2.31.993-2.31 2.32 0 1.32.991 2.31 2.31 2.31 1.32 0 2.3-.99 2.3-2.31 0-1.327-.978-2.32-2.3-2.32zm1.915-13.827h-3.84v11.568h3.84V7.804z"
            fill="#D00000"
            fill-rule="evenodd"
          />
        </svg>
      </span>
      <span
        class="emotion-3"
      />
    </div>
  </div>
</DocumentFragment>
`;

exports[`<Notification /> notification snapshot tests for inverse styles renders inverse error state correctly BananaRepublicFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  transition-property: opacity;
  transition-duration: 250ms;
  transition-timing-function: cubic-bezier(0.25, 0, 0.25, 1);
  transition-delay: 0ms;
  opacity: 1;
}

.emotion-1 {
  line-height: 1.25;
  padding: 0.5rem;
  margin-bottom: 1.125rem;
  border-style: solid;
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 0.875rem;
  color: #F6F4EB;
  border-color: #FD6565;
  border-width: 2px 2px 2px 8px;
}

.emotion-2 {
  display: inline-block;
  height: 20px;
  width: 20px;
  min-height: 20px;
  min-width: 20px;
}

.emotion-2 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-2 svg path {
  fill: #FD6565;
}

.emotion-2 svg rect {
  fill: #FD6565;
}

.emotion-3 {
  padding-left: 0.75rem;
}

<div
    class="emotion-0"
  >
    <div
      aria-live="assertive"
      class="emotion-1"
      kind="error"
    >
      <span
        aria-hidden="true"
        class="emotion-2"
      >
        <svg
          viewBox="0 0 33 33"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M16.039.661c8.836 0 16 7.164 16 16 0 8.837-7.164 16-16 16-8.837 0-16-7.163-16-16 0-8.836 7.163-16 16-16zm-.19 20.97c-1.321 0-2.31.993-2.31 2.32 0 1.32.991 2.31 2.31 2.31 1.32 0 2.3-.99 2.3-2.31 0-1.327-.978-2.32-2.3-2.32zm1.915-13.827h-3.84v11.568h3.84V7.804z"
            fill="#D00000"
            fill-rule="evenodd"
          />
        </svg>
      </span>
      <span
        class="emotion-3"
      />
    </div>
  </div>
</DocumentFragment>
`;

exports[`<Notification /> notification snapshot tests for inverse styles renders inverse error state correctly Gap 1`] = `
<DocumentFragment>
  .emotion-0 {
  transition-property: opacity;
  transition-duration: 250ms;
  transition-timing-function: cubic-bezier(0.25, 0, 0.25, 1);
  transition-delay: 0ms;
  opacity: 1;
}

.emotion-1 {
  line-height: 1.25;
  padding: 0.5rem;
  margin-bottom: 1.125rem;
  border-style: solid;
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 0.8125rem;
  color: #FFFFFF;
  border-color: #FD6565;
  border-width: 2px 2px 2px 8px;
}

.emotion-2 {
  display: inline-block;
  height: 20px;
  width: 20px;
  min-height: 20px;
  min-width: 20px;
}

.emotion-2 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-2 svg path {
  fill: #FD6565;
}

.emotion-2 svg rect {
  fill: #FD6565;
}

.emotion-3 {
  padding-left: 0.75rem;
}

<div
    class="emotion-0"
  >
    <div
      aria-live="assertive"
      class="emotion-1"
      kind="error"
    >
      <span
        aria-hidden="true"
        class="emotion-2"
      >
        <svg
          viewBox="0 0 33 33"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M16.039.661c8.836 0 16 7.164 16 16 0 8.837-7.164 16-16 16-8.837 0-16-7.163-16-16 0-8.836 7.163-16 16-16zm-.19 20.97c-1.321 0-2.31.993-2.31 2.32 0 1.32.991 2.31 2.31 2.31 1.32 0 2.3-.99 2.3-2.31 0-1.327-.978-2.32-2.3-2.32zm1.915-13.827h-3.84v11.568h3.84V7.804z"
            fill="#D00000"
            fill-rule="evenodd"
          />
        </svg>
      </span>
      <span
        class="emotion-3"
      />
    </div>
  </div>
</DocumentFragment>
`;

exports[`<Notification /> notification snapshot tests for inverse styles renders inverse error state correctly Gap in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  transition-property: opacity;
  transition-duration: 250ms;
  transition-timing-function: cubic-bezier(0.25, 0, 0.25, 1);
  transition-delay: 0ms;
  opacity: 1;
}

.emotion-1 {
  line-height: 1.25;
  padding: 0.5rem;
  margin-bottom: 1.125rem;
  border-style: solid;
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 0.875rem;
  color: #FFFFFF;
  border-color: #FD6565;
  border-width: 2px 2px 2px 8px;
}

.emotion-2 {
  display: inline-block;
  height: 20px;
  width: 20px;
  min-height: 20px;
  min-width: 20px;
}

.emotion-2 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-2 svg path {
  fill: #FD6565;
}

.emotion-2 svg rect {
  fill: #FD6565;
}

.emotion-3 {
  padding-left: 0.75rem;
}

<div
    class="emotion-0"
  >
    <div
      aria-live="assertive"
      class="emotion-1"
      kind="error"
    >
      <span
        aria-hidden="true"
        class="emotion-2"
      >
        <svg
          viewBox="0 0 33 33"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M16.039.661c8.836 0 16 7.164 16 16 0 8.837-7.164 16-16 16-8.837 0-16-7.163-16-16 0-8.836 7.163-16 16-16zm-.19 20.97c-1.321 0-2.31.993-2.31 2.32 0 1.32.991 2.31 2.31 2.31 1.32 0 2.3-.99 2.3-2.31 0-1.327-.978-2.32-2.3-2.32zm1.915-13.827h-3.84v11.568h3.84V7.804z"
            fill="#D00000"
            fill-rule="evenodd"
          />
        </svg>
      </span>
      <span
        class="emotion-3"
      />
    </div>
  </div>
</DocumentFragment>
`;

exports[`<Notification /> notification snapshot tests for inverse styles renders inverse error state correctly GapFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  transition-property: opacity;
  transition-duration: 250ms;
  transition-timing-function: cubic-bezier(0.25, 0, 0.25, 1);
  transition-delay: 0ms;
  opacity: 1;
}

.emotion-1 {
  line-height: 1.25;
  padding: 0.5rem;
  margin-bottom: 1.125rem;
  border-style: solid;
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 0.8125rem;
  color: #FFFFFF;
  border-color: #FD6565;
  border-width: 2px 2px 2px 8px;
}

.emotion-2 {
  display: inline-block;
  height: 20px;
  width: 20px;
  min-height: 20px;
  min-width: 20px;
}

.emotion-2 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-2 svg path {
  fill: #FD6565;
}

.emotion-2 svg rect {
  fill: #FD6565;
}

.emotion-3 {
  padding-left: 0.75rem;
}

<div
    class="emotion-0"
  >
    <div
      aria-live="assertive"
      class="emotion-1"
      kind="error"
    >
      <span
        aria-hidden="true"
        class="emotion-2"
      >
        <svg
          viewBox="0 0 33 33"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M16.039.661c8.836 0 16 7.164 16 16 0 8.837-7.164 16-16 16-8.837 0-16-7.163-16-16 0-8.836 7.163-16 16-16zm-.19 20.97c-1.321 0-2.31.993-2.31 2.32 0 1.32.991 2.31 2.31 2.31 1.32 0 2.3-.99 2.3-2.31 0-1.327-.978-2.32-2.3-2.32zm1.915-13.827h-3.84v11.568h3.84V7.804z"
            fill="#D00000"
            fill-rule="evenodd"
          />
        </svg>
      </span>
      <span
        class="emotion-3"
      />
    </div>
  </div>
</DocumentFragment>
`;

exports[`<Notification /> notification snapshot tests for inverse styles renders inverse error state correctly GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  transition-property: opacity;
  transition-duration: 250ms;
  transition-timing-function: cubic-bezier(0.25, 0, 0.25, 1);
  transition-delay: 0ms;
  opacity: 1;
}

.emotion-1 {
  line-height: 1.25;
  padding: 0.5rem;
  margin-bottom: 1.125rem;
  border-style: solid;
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 0.875rem;
  color: #FFFFFF;
  border-color: #FD6565;
  border-width: 2px 2px 2px 8px;
}

.emotion-2 {
  display: inline-block;
  height: 20px;
  width: 20px;
  min-height: 20px;
  min-width: 20px;
}

.emotion-2 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-2 svg path {
  fill: #FD6565;
}

.emotion-2 svg rect {
  fill: #FD6565;
}

.emotion-3 {
  padding-left: 0.75rem;
}

<div
    class="emotion-0"
  >
    <div
      aria-live="assertive"
      class="emotion-1"
      kind="error"
    >
      <span
        aria-hidden="true"
        class="emotion-2"
      >
        <svg
          viewBox="0 0 33 33"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M16.039.661c8.836 0 16 7.164 16 16 0 8.837-7.164 16-16 16-8.837 0-16-7.163-16-16 0-8.836 7.163-16 16-16zm-.19 20.97c-1.321 0-2.31.993-2.31 2.32 0 1.32.991 2.31 2.31 2.31 1.32 0 2.3-.99 2.3-2.31 0-1.327-.978-2.32-2.3-2.32zm1.915-13.827h-3.84v11.568h3.84V7.804z"
            fill="#D00000"
            fill-rule="evenodd"
          />
        </svg>
      </span>
      <span
        class="emotion-3"
      />
    </div>
  </div>
</DocumentFragment>
`;

exports[`<Notification /> notification snapshot tests for inverse styles renders inverse error state correctly OldNavy 1`] = `
<DocumentFragment>
  .emotion-0 {
  transition-property: opacity;
  transition-duration: 250ms;
  transition-timing-function: cubic-bezier(0.25, 0, 0.25, 1);
  transition-delay: 0ms;
  opacity: 1;
}

.emotion-1 {
  line-height: 1.25;
  padding: 0.5rem;
  margin-bottom: 1.125rem;
  border-style: solid;
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  font-family: 'Gotham','Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 0.875rem;
  color: #FFFFFF;
  border-color: #FD6565;
  border-width: 2px 2px 2px 8px;
}

.emotion-2 {
  display: inline-block;
  height: 20px;
  width: 20px;
  min-height: 20px;
  min-width: 20px;
}

.emotion-2 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-2 svg path {
  fill: #FD6565;
}

.emotion-2 svg rect {
  fill: #FD6565;
}

.emotion-3 {
  padding-left: 0.75rem;
}

<div
    class="emotion-0"
  >
    <div
      aria-live="assertive"
      class="emotion-1"
      kind="error"
    >
      <span
        aria-hidden="true"
        class="emotion-2"
      >
        <svg
          viewBox="0 0 33 33"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M16.039.661c8.836 0 16 7.164 16 16 0 8.837-7.164 16-16 16-8.837 0-16-7.163-16-16 0-8.836 7.163-16 16-16zm-.19 20.97c-1.321 0-2.31.993-2.31 2.32 0 1.32.991 2.31 2.31 2.31 1.32 0 2.3-.99 2.3-2.31 0-1.327-.978-2.32-2.3-2.32zm1.915-13.827h-3.84v11.568h3.84V7.804z"
            fill="#D00000"
            fill-rule="evenodd"
          />
        </svg>
      </span>
      <span
        class="emotion-3"
      />
    </div>
  </div>
</DocumentFragment>
`;

exports[`<Notification /> notification snapshot tests for inverse styles renders inverse error state correctly OldNavy in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  transition-property: opacity;
  transition-duration: 250ms;
  transition-timing-function: cubic-bezier(0.25, 0, 0.25, 1);
  transition-delay: 0ms;
  opacity: 1;
}

.emotion-1 {
  line-height: 1.25;
  padding: 0.5rem;
  margin-bottom: 1.125rem;
  border-style: solid;
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 0.875rem;
  color: #FFFFFF;
  border-color: #FD6565;
  border-width: 2px 2px 2px 8px;
}

.emotion-2 {
  display: inline-block;
  height: 20px;
  width: 20px;
  min-height: 20px;
  min-width: 20px;
}

.emotion-2 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-2 svg path {
  fill: #FD6565;
}

.emotion-2 svg rect {
  fill: #FD6565;
}

.emotion-3 {
  padding-left: 0.75rem;
}

<div
    class="emotion-0"
  >
    <div
      aria-live="assertive"
      class="emotion-1"
      kind="error"
    >
      <span
        aria-hidden="true"
        class="emotion-2"
      >
        <svg
          viewBox="0 0 33 33"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M16.039.661c8.836 0 16 7.164 16 16 0 8.837-7.164 16-16 16-8.837 0-16-7.163-16-16 0-8.836 7.163-16 16-16zm-.19 20.97c-1.321 0-2.31.993-2.31 2.32 0 1.32.991 2.31 2.31 2.31 1.32 0 2.3-.99 2.3-2.31 0-1.327-.978-2.32-2.3-2.32zm1.915-13.827h-3.84v11.568h3.84V7.804z"
            fill="#D00000"
            fill-rule="evenodd"
          />
        </svg>
      </span>
      <span
        class="emotion-3"
      />
    </div>
  </div>
</DocumentFragment>
`;

exports[`<Notification /> notification snapshot tests for inverse styles renders inverse information state correctly Athleta 1`] = `
<DocumentFragment>
  .emotion-0 {
  transition-property: opacity;
  transition-duration: 250ms;
  transition-timing-function: cubic-bezier(0.25, 0, 0.25, 1);
  transition-delay: 0ms;
  opacity: 1;
}

.emotion-1 {
  line-height: 1.25;
  padding: 0.5rem;
  margin-bottom: 1.125rem;
  border-style: solid;
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  font-family: 'Avenir Next','Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 0.875rem;
  color: #FFFFFF;
  border-color: #5DABF5;
  border-width: 2px 2px 2px 8px;
}

.emotion-2 {
  display: inline-block;
  height: 20px;
  width: 20px;
  min-height: 20px;
  min-width: 20px;
}

.emotion-2 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-2 svg path {
  fill: #5DABF5;
}

.emotion-2 svg rect {
  fill: #5DABF5;
}

.emotion-3 {
  padding-left: 0.75rem;
}

<div
    class="emotion-0"
  >
    <div
      aria-live="polite"
      class="emotion-1"
      kind="information"
    >
      <span
        aria-hidden="true"
        class="emotion-2"
      >
        <svg
          viewBox="0 0 33 33"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M16.961 32.338c-8.836 0-16-7.163-16-16 0-8.836 7.164-16 16-16 8.837 0 16 7.164 16 16 0 8.837-7.163 16-16 16zm.19-20.969c1.321 0 2.31-.993 2.31-2.32 0-1.32-.991-2.31-2.31-2.31-1.32 0-2.3.99-2.3 2.31 0 1.327.978 2.32 2.3 2.32zm-1.915 13.827h3.84V13.628h-3.84v11.568z"
            fill="#5CABF7"
            fill-rule="evenodd"
          />
        </svg>
      </span>
      <span
        class="emotion-3"
      />
    </div>
  </div>
</DocumentFragment>
`;

exports[`<Notification /> notification snapshot tests for inverse styles renders inverse information state correctly Athleta in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  transition-property: opacity;
  transition-duration: 250ms;
  transition-timing-function: cubic-bezier(0.25, 0, 0.25, 1);
  transition-delay: 0ms;
  opacity: 1;
}

.emotion-1 {
  line-height: 1.25;
  padding: 0.5rem;
  margin-bottom: 1.125rem;
  border-style: solid;
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 0.875rem;
  color: #FFFFFF;
  border-color: #5DABF5;
  border-width: 2px 2px 2px 8px;
}

.emotion-2 {
  display: inline-block;
  height: 20px;
  width: 20px;
  min-height: 20px;
  min-width: 20px;
}

.emotion-2 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-2 svg path {
  fill: #5DABF5;
}

.emotion-2 svg rect {
  fill: #5DABF5;
}

.emotion-3 {
  padding-left: 0.75rem;
}

<div
    class="emotion-0"
  >
    <div
      aria-live="polite"
      class="emotion-1"
      kind="information"
    >
      <span
        aria-hidden="true"
        class="emotion-2"
      >
        <svg
          viewBox="0 0 33 33"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M16.961 32.338c-8.836 0-16-7.163-16-16 0-8.836 7.164-16 16-16 8.837 0 16 7.164 16 16 0 8.837-7.163 16-16 16zm.19-20.969c1.321 0 2.31-.993 2.31-2.32 0-1.32-.991-2.31-2.31-2.31-1.32 0-2.3.99-2.3 2.31 0 1.327.978 2.32 2.3 2.32zm-1.915 13.827h3.84V13.628h-3.84v11.568z"
            fill="#5CABF7"
            fill-rule="evenodd"
          />
        </svg>
      </span>
      <span
        class="emotion-3"
      />
    </div>
  </div>
</DocumentFragment>
`;

exports[`<Notification /> notification snapshot tests for inverse styles renders inverse information state correctly BananaRepublic 1`] = `
<DocumentFragment>
  .emotion-0 {
  transition-property: opacity;
  transition-duration: 250ms;
  transition-timing-function: cubic-bezier(0.25, 0, 0.25, 1);
  transition-delay: 0ms;
  opacity: 1;
}

.emotion-1 {
  line-height: 1.25;
  padding: 0.5rem;
  margin-bottom: 1.125rem;
  border-style: solid;
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  font-family: 'Lynstone',Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 0.8125rem;
  color: #F6F4EB;
  border-color: #5DABF5;
  border-width: 2px 2px 2px 8px;
}

.emotion-2 {
  display: inline-block;
  height: 20px;
  width: 20px;
  min-height: 20px;
  min-width: 20px;
}

.emotion-2 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-2 svg path {
  fill: #5DABF5;
}

.emotion-2 svg rect {
  fill: #5DABF5;
}

.emotion-3 {
  padding-left: 0.75rem;
}

<div
    class="emotion-0"
  >
    <div
      aria-live="polite"
      class="emotion-1"
      kind="information"
    >
      <span
        aria-hidden="true"
        class="emotion-2"
      >
        <svg
          viewBox="0 0 33 33"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M16.961 32.338c-8.836 0-16-7.163-16-16 0-8.836 7.164-16 16-16 8.837 0 16 7.164 16 16 0 8.837-7.163 16-16 16zm.19-20.969c1.321 0 2.31-.993 2.31-2.32 0-1.32-.991-2.31-2.31-2.31-1.32 0-2.3.99-2.3 2.31 0 1.327.978 2.32 2.3 2.32zm-1.915 13.827h3.84V13.628h-3.84v11.568z"
            fill="#5CABF7"
            fill-rule="evenodd"
          />
        </svg>
      </span>
      <span
        class="emotion-3"
      />
    </div>
  </div>
</DocumentFragment>
`;

exports[`<Notification /> notification snapshot tests for inverse styles renders inverse information state correctly BananaRepublic in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  transition-property: opacity;
  transition-duration: 250ms;
  transition-timing-function: cubic-bezier(0.25, 0, 0.25, 1);
  transition-delay: 0ms;
  opacity: 1;
}

.emotion-1 {
  line-height: 1.25;
  padding: 0.5rem;
  margin-bottom: 1.125rem;
  border-style: solid;
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 0.875rem;
  color: #F6F4EB;
  border-color: #5DABF5;
  border-width: 2px 2px 2px 8px;
}

.emotion-2 {
  display: inline-block;
  height: 20px;
  width: 20px;
  min-height: 20px;
  min-width: 20px;
}

.emotion-2 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-2 svg path {
  fill: #5DABF5;
}

.emotion-2 svg rect {
  fill: #5DABF5;
}

.emotion-3 {
  padding-left: 0.75rem;
}

<div
    class="emotion-0"
  >
    <div
      aria-live="polite"
      class="emotion-1"
      kind="information"
    >
      <span
        aria-hidden="true"
        class="emotion-2"
      >
        <svg
          viewBox="0 0 33 33"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M16.961 32.338c-8.836 0-16-7.163-16-16 0-8.836 7.164-16 16-16 8.837 0 16 7.164 16 16 0 8.837-7.163 16-16 16zm.19-20.969c1.321 0 2.31-.993 2.31-2.32 0-1.32-.991-2.31-2.31-2.31-1.32 0-2.3.99-2.3 2.31 0 1.327.978 2.32 2.3 2.32zm-1.915 13.827h3.84V13.628h-3.84v11.568z"
            fill="#5CABF7"
            fill-rule="evenodd"
          />
        </svg>
      </span>
      <span
        class="emotion-3"
      />
    </div>
  </div>
</DocumentFragment>
`;

exports[`<Notification /> notification snapshot tests for inverse styles renders inverse information state correctly BananaRepublicFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  transition-property: opacity;
  transition-duration: 250ms;
  transition-timing-function: cubic-bezier(0.25, 0, 0.25, 1);
  transition-delay: 0ms;
  opacity: 1;
}

.emotion-1 {
  line-height: 1.25;
  padding: 0.5rem;
  margin-bottom: 1.125rem;
  border-style: solid;
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  font-family: 'Lynstone',Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 0.8125rem;
  color: #F6F4EB;
  border-color: #5DABF5;
  border-width: 2px 2px 2px 8px;
}

.emotion-2 {
  display: inline-block;
  height: 20px;
  width: 20px;
  min-height: 20px;
  min-width: 20px;
}

.emotion-2 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-2 svg path {
  fill: #5DABF5;
}

.emotion-2 svg rect {
  fill: #5DABF5;
}

.emotion-3 {
  padding-left: 0.75rem;
}

<div
    class="emotion-0"
  >
    <div
      aria-live="polite"
      class="emotion-1"
      kind="information"
    >
      <span
        aria-hidden="true"
        class="emotion-2"
      >
        <svg
          viewBox="0 0 33 33"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M16.961 32.338c-8.836 0-16-7.163-16-16 0-8.836 7.164-16 16-16 8.837 0 16 7.164 16 16 0 8.837-7.163 16-16 16zm.19-20.969c1.321 0 2.31-.993 2.31-2.32 0-1.32-.991-2.31-2.31-2.31-1.32 0-2.3.99-2.3 2.31 0 1.327.978 2.32 2.3 2.32zm-1.915 13.827h3.84V13.628h-3.84v11.568z"
            fill="#5CABF7"
            fill-rule="evenodd"
          />
        </svg>
      </span>
      <span
        class="emotion-3"
      />
    </div>
  </div>
</DocumentFragment>
`;

exports[`<Notification /> notification snapshot tests for inverse styles renders inverse information state correctly BananaRepublicFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  transition-property: opacity;
  transition-duration: 250ms;
  transition-timing-function: cubic-bezier(0.25, 0, 0.25, 1);
  transition-delay: 0ms;
  opacity: 1;
}

.emotion-1 {
  line-height: 1.25;
  padding: 0.5rem;
  margin-bottom: 1.125rem;
  border-style: solid;
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 0.875rem;
  color: #F6F4EB;
  border-color: #5DABF5;
  border-width: 2px 2px 2px 8px;
}

.emotion-2 {
  display: inline-block;
  height: 20px;
  width: 20px;
  min-height: 20px;
  min-width: 20px;
}

.emotion-2 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-2 svg path {
  fill: #5DABF5;
}

.emotion-2 svg rect {
  fill: #5DABF5;
}

.emotion-3 {
  padding-left: 0.75rem;
}

<div
    class="emotion-0"
  >
    <div
      aria-live="polite"
      class="emotion-1"
      kind="information"
    >
      <span
        aria-hidden="true"
        class="emotion-2"
      >
        <svg
          viewBox="0 0 33 33"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M16.961 32.338c-8.836 0-16-7.163-16-16 0-8.836 7.164-16 16-16 8.837 0 16 7.164 16 16 0 8.837-7.163 16-16 16zm.19-20.969c1.321 0 2.31-.993 2.31-2.32 0-1.32-.991-2.31-2.31-2.31-1.32 0-2.3.99-2.3 2.31 0 1.327.978 2.32 2.3 2.32zm-1.915 13.827h3.84V13.628h-3.84v11.568z"
            fill="#5CABF7"
            fill-rule="evenodd"
          />
        </svg>
      </span>
      <span
        class="emotion-3"
      />
    </div>
  </div>
</DocumentFragment>
`;

exports[`<Notification /> notification snapshot tests for inverse styles renders inverse information state correctly Gap 1`] = `
<DocumentFragment>
  .emotion-0 {
  transition-property: opacity;
  transition-duration: 250ms;
  transition-timing-function: cubic-bezier(0.25, 0, 0.25, 1);
  transition-delay: 0ms;
  opacity: 1;
}

.emotion-1 {
  line-height: 1.25;
  padding: 0.5rem;
  margin-bottom: 1.125rem;
  border-style: solid;
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 0.8125rem;
  color: #FFFFFF;
  border-color: #5DABF5;
  border-width: 2px 2px 2px 8px;
}

.emotion-2 {
  display: inline-block;
  height: 20px;
  width: 20px;
  min-height: 20px;
  min-width: 20px;
}

.emotion-2 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-2 svg path {
  fill: #5DABF5;
}

.emotion-2 svg rect {
  fill: #5DABF5;
}

.emotion-3 {
  padding-left: 0.75rem;
}

<div
    class="emotion-0"
  >
    <div
      aria-live="polite"
      class="emotion-1"
      kind="information"
    >
      <span
        aria-hidden="true"
        class="emotion-2"
      >
        <svg
          viewBox="0 0 33 33"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M16.961 32.338c-8.836 0-16-7.163-16-16 0-8.836 7.164-16 16-16 8.837 0 16 7.164 16 16 0 8.837-7.163 16-16 16zm.19-20.969c1.321 0 2.31-.993 2.31-2.32 0-1.32-.991-2.31-2.31-2.31-1.32 0-2.3.99-2.3 2.31 0 1.327.978 2.32 2.3 2.32zm-1.915 13.827h3.84V13.628h-3.84v11.568z"
            fill="#5CABF7"
            fill-rule="evenodd"
          />
        </svg>
      </span>
      <span
        class="emotion-3"
      />
    </div>
  </div>
</DocumentFragment>
`;

exports[`<Notification /> notification snapshot tests for inverse styles renders inverse information state correctly Gap in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  transition-property: opacity;
  transition-duration: 250ms;
  transition-timing-function: cubic-bezier(0.25, 0, 0.25, 1);
  transition-delay: 0ms;
  opacity: 1;
}

.emotion-1 {
  line-height: 1.25;
  padding: 0.5rem;
  margin-bottom: 1.125rem;
  border-style: solid;
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 0.875rem;
  color: #FFFFFF;
  border-color: #5DABF5;
  border-width: 2px 2px 2px 8px;
}

.emotion-2 {
  display: inline-block;
  height: 20px;
  width: 20px;
  min-height: 20px;
  min-width: 20px;
}

.emotion-2 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-2 svg path {
  fill: #5DABF5;
}

.emotion-2 svg rect {
  fill: #5DABF5;
}

.emotion-3 {
  padding-left: 0.75rem;
}

<div
    class="emotion-0"
  >
    <div
      aria-live="polite"
      class="emotion-1"
      kind="information"
    >
      <span
        aria-hidden="true"
        class="emotion-2"
      >
        <svg
          viewBox="0 0 33 33"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M16.961 32.338c-8.836 0-16-7.163-16-16 0-8.836 7.164-16 16-16 8.837 0 16 7.164 16 16 0 8.837-7.163 16-16 16zm.19-20.969c1.321 0 2.31-.993 2.31-2.32 0-1.32-.991-2.31-2.31-2.31-1.32 0-2.3.99-2.3 2.31 0 1.327.978 2.32 2.3 2.32zm-1.915 13.827h3.84V13.628h-3.84v11.568z"
            fill="#5CABF7"
            fill-rule="evenodd"
          />
        </svg>
      </span>
      <span
        class="emotion-3"
      />
    </div>
  </div>
</DocumentFragment>
`;

exports[`<Notification /> notification snapshot tests for inverse styles renders inverse information state correctly GapFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  transition-property: opacity;
  transition-duration: 250ms;
  transition-timing-function: cubic-bezier(0.25, 0, 0.25, 1);
  transition-delay: 0ms;
  opacity: 1;
}

.emotion-1 {
  line-height: 1.25;
  padding: 0.5rem;
  margin-bottom: 1.125rem;
  border-style: solid;
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 0.8125rem;
  color: #FFFFFF;
  border-color: #5DABF5;
  border-width: 2px 2px 2px 8px;
}

.emotion-2 {
  display: inline-block;
  height: 20px;
  width: 20px;
  min-height: 20px;
  min-width: 20px;
}

.emotion-2 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-2 svg path {
  fill: #5DABF5;
}

.emotion-2 svg rect {
  fill: #5DABF5;
}

.emotion-3 {
  padding-left: 0.75rem;
}

<div
    class="emotion-0"
  >
    <div
      aria-live="polite"
      class="emotion-1"
      kind="information"
    >
      <span
        aria-hidden="true"
        class="emotion-2"
      >
        <svg
          viewBox="0 0 33 33"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M16.961 32.338c-8.836 0-16-7.163-16-16 0-8.836 7.164-16 16-16 8.837 0 16 7.164 16 16 0 8.837-7.163 16-16 16zm.19-20.969c1.321 0 2.31-.993 2.31-2.32 0-1.32-.991-2.31-2.31-2.31-1.32 0-2.3.99-2.3 2.31 0 1.327.978 2.32 2.3 2.32zm-1.915 13.827h3.84V13.628h-3.84v11.568z"
            fill="#5CABF7"
            fill-rule="evenodd"
          />
        </svg>
      </span>
      <span
        class="emotion-3"
      />
    </div>
  </div>
</DocumentFragment>
`;

exports[`<Notification /> notification snapshot tests for inverse styles renders inverse information state correctly GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  transition-property: opacity;
  transition-duration: 250ms;
  transition-timing-function: cubic-bezier(0.25, 0, 0.25, 1);
  transition-delay: 0ms;
  opacity: 1;
}

.emotion-1 {
  line-height: 1.25;
  padding: 0.5rem;
  margin-bottom: 1.125rem;
  border-style: solid;
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 0.875rem;
  color: #FFFFFF;
  border-color: #5DABF5;
  border-width: 2px 2px 2px 8px;
}

.emotion-2 {
  display: inline-block;
  height: 20px;
  width: 20px;
  min-height: 20px;
  min-width: 20px;
}

.emotion-2 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-2 svg path {
  fill: #5DABF5;
}

.emotion-2 svg rect {
  fill: #5DABF5;
}

.emotion-3 {
  padding-left: 0.75rem;
}

<div
    class="emotion-0"
  >
    <div
      aria-live="polite"
      class="emotion-1"
      kind="information"
    >
      <span
        aria-hidden="true"
        class="emotion-2"
      >
        <svg
          viewBox="0 0 33 33"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M16.961 32.338c-8.836 0-16-7.163-16-16 0-8.836 7.164-16 16-16 8.837 0 16 7.164 16 16 0 8.837-7.163 16-16 16zm.19-20.969c1.321 0 2.31-.993 2.31-2.32 0-1.32-.991-2.31-2.31-2.31-1.32 0-2.3.99-2.3 2.31 0 1.327.978 2.32 2.3 2.32zm-1.915 13.827h3.84V13.628h-3.84v11.568z"
            fill="#5CABF7"
            fill-rule="evenodd"
          />
        </svg>
      </span>
      <span
        class="emotion-3"
      />
    </div>
  </div>
</DocumentFragment>
`;

exports[`<Notification /> notification snapshot tests for inverse styles renders inverse information state correctly OldNavy 1`] = `
<DocumentFragment>
  .emotion-0 {
  transition-property: opacity;
  transition-duration: 250ms;
  transition-timing-function: cubic-bezier(0.25, 0, 0.25, 1);
  transition-delay: 0ms;
  opacity: 1;
}

.emotion-1 {
  line-height: 1.25;
  padding: 0.5rem;
  margin-bottom: 1.125rem;
  border-style: solid;
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  font-family: 'Gotham','Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 0.875rem;
  color: #FFFFFF;
  border-color: #5DABF5;
  border-width: 2px 2px 2px 8px;
}

.emotion-2 {
  display: inline-block;
  height: 20px;
  width: 20px;
  min-height: 20px;
  min-width: 20px;
}

.emotion-2 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-2 svg path {
  fill: #5DABF5;
}

.emotion-2 svg rect {
  fill: #5DABF5;
}

.emotion-3 {
  padding-left: 0.75rem;
}

<div
    class="emotion-0"
  >
    <div
      aria-live="polite"
      class="emotion-1"
      kind="information"
    >
      <span
        aria-hidden="true"
        class="emotion-2"
      >
        <svg
          viewBox="0 0 33 33"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M16.961 32.338c-8.836 0-16-7.163-16-16 0-8.836 7.164-16 16-16 8.837 0 16 7.164 16 16 0 8.837-7.163 16-16 16zm.19-20.969c1.321 0 2.31-.993 2.31-2.32 0-1.32-.991-2.31-2.31-2.31-1.32 0-2.3.99-2.3 2.31 0 1.327.978 2.32 2.3 2.32zm-1.915 13.827h3.84V13.628h-3.84v11.568z"
            fill="#5CABF7"
            fill-rule="evenodd"
          />
        </svg>
      </span>
      <span
        class="emotion-3"
      />
    </div>
  </div>
</DocumentFragment>
`;

exports[`<Notification /> notification snapshot tests for inverse styles renders inverse information state correctly OldNavy in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  transition-property: opacity;
  transition-duration: 250ms;
  transition-timing-function: cubic-bezier(0.25, 0, 0.25, 1);
  transition-delay: 0ms;
  opacity: 1;
}

.emotion-1 {
  line-height: 1.25;
  padding: 0.5rem;
  margin-bottom: 1.125rem;
  border-style: solid;
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 0.875rem;
  color: #FFFFFF;
  border-color: #5DABF5;
  border-width: 2px 2px 2px 8px;
}

.emotion-2 {
  display: inline-block;
  height: 20px;
  width: 20px;
  min-height: 20px;
  min-width: 20px;
}

.emotion-2 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-2 svg path {
  fill: #5DABF5;
}

.emotion-2 svg rect {
  fill: #5DABF5;
}

.emotion-3 {
  padding-left: 0.75rem;
}

<div
    class="emotion-0"
  >
    <div
      aria-live="polite"
      class="emotion-1"
      kind="information"
    >
      <span
        aria-hidden="true"
        class="emotion-2"
      >
        <svg
          viewBox="0 0 33 33"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M16.961 32.338c-8.836 0-16-7.163-16-16 0-8.836 7.164-16 16-16 8.837 0 16 7.164 16 16 0 8.837-7.163 16-16 16zm.19-20.969c1.321 0 2.31-.993 2.31-2.32 0-1.32-.991-2.31-2.31-2.31-1.32 0-2.3.99-2.3 2.31 0 1.327.978 2.32 2.3 2.32zm-1.915 13.827h3.84V13.628h-3.84v11.568z"
            fill="#5CABF7"
            fill-rule="evenodd"
          />
        </svg>
      </span>
      <span
        class="emotion-3"
      />
    </div>
  </div>
</DocumentFragment>
`;

exports[`<Notification /> notification snapshot tests for inverse styles renders inverse success state correctly Athleta 1`] = `
<DocumentFragment>
  .emotion-0 {
  transition-property: opacity;
  transition-duration: 250ms;
  transition-timing-function: cubic-bezier(0.25, 0, 0.25, 1);
  transition-delay: 0ms;
  opacity: 1;
}

.emotion-1 {
  line-height: 1.25;
  padding: 0.5rem;
  margin-bottom: 1.125rem;
  border-style: solid;
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  font-family: 'Avenir Next','Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 0.875rem;
  color: #FFFFFF;
  border-color: #82B05D;
  border-width: 2px 2px 2px 8px;
}

.emotion-2 {
  display: inline-block;
  height: 20px;
  width: 20px;
  min-height: 20px;
  min-width: 20px;
}

.emotion-2 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-2 svg path {
  fill: #82B05D;
}

.emotion-2 svg rect {
  fill: #82B05D;
}

.emotion-3 {
  padding-left: 0.75rem;
}

<div
    class="emotion-0"
  >
    <div
      aria-live="polite"
      class="emotion-1"
      kind="success"
    >
      <span
        aria-hidden="true"
        class="emotion-2"
      >
        <svg
          viewBox="0 0 32 32"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M16.118 0c8.837 0 16 7.163 16 16s-7.163 16-16 16-16-7.163-16-16 7.163-16 16-16zm6.552 9l-9.146 9.146-4.526-4.526-2.88 2.88 7.5 7.5 12.028-12.026L22.67 9z"
            fill="#16A816"
            fill-rule="nonzero"
          />
        </svg>
      </span>
      <span
        class="emotion-3"
      />
    </div>
  </div>
</DocumentFragment>
`;

exports[`<Notification /> notification snapshot tests for inverse styles renders inverse success state correctly Athleta in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  transition-property: opacity;
  transition-duration: 250ms;
  transition-timing-function: cubic-bezier(0.25, 0, 0.25, 1);
  transition-delay: 0ms;
  opacity: 1;
}

.emotion-1 {
  line-height: 1.25;
  padding: 0.5rem;
  margin-bottom: 1.125rem;
  border-style: solid;
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 0.875rem;
  color: #FFFFFF;
  border-color: #82B05D;
  border-width: 2px 2px 2px 8px;
}

.emotion-2 {
  display: inline-block;
  height: 20px;
  width: 20px;
  min-height: 20px;
  min-width: 20px;
}

.emotion-2 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-2 svg path {
  fill: #82B05D;
}

.emotion-2 svg rect {
  fill: #82B05D;
}

.emotion-3 {
  padding-left: 0.75rem;
}

<div
    class="emotion-0"
  >
    <div
      aria-live="polite"
      class="emotion-1"
      kind="success"
    >
      <span
        aria-hidden="true"
        class="emotion-2"
      >
        <svg
          viewBox="0 0 32 32"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M16.118 0c8.837 0 16 7.163 16 16s-7.163 16-16 16-16-7.163-16-16 7.163-16 16-16zm6.552 9l-9.146 9.146-4.526-4.526-2.88 2.88 7.5 7.5 12.028-12.026L22.67 9z"
            fill="#16A816"
            fill-rule="nonzero"
          />
        </svg>
      </span>
      <span
        class="emotion-3"
      />
    </div>
  </div>
</DocumentFragment>
`;

exports[`<Notification /> notification snapshot tests for inverse styles renders inverse success state correctly BananaRepublic 1`] = `
<DocumentFragment>
  .emotion-0 {
  transition-property: opacity;
  transition-duration: 250ms;
  transition-timing-function: cubic-bezier(0.25, 0, 0.25, 1);
  transition-delay: 0ms;
  opacity: 1;
}

.emotion-1 {
  line-height: 1.25;
  padding: 0.5rem;
  margin-bottom: 1.125rem;
  border-style: solid;
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  font-family: 'Lynstone',Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 0.8125rem;
  color: #F6F4EB;
  border-color: #82B05D;
  border-width: 2px 2px 2px 8px;
}

.emotion-2 {
  display: inline-block;
  height: 20px;
  width: 20px;
  min-height: 20px;
  min-width: 20px;
}

.emotion-2 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-2 svg path {
  fill: #82B05D;
}

.emotion-2 svg rect {
  fill: #82B05D;
}

.emotion-3 {
  padding-left: 0.75rem;
}

<div
    class="emotion-0"
  >
    <div
      aria-live="polite"
      class="emotion-1"
      kind="success"
    >
      <span
        aria-hidden="true"
        class="emotion-2"
      >
        <svg
          viewBox="0 0 32 32"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M16.118 0c8.837 0 16 7.163 16 16s-7.163 16-16 16-16-7.163-16-16 7.163-16 16-16zm6.552 9l-9.146 9.146-4.526-4.526-2.88 2.88 7.5 7.5 12.028-12.026L22.67 9z"
            fill="#16A816"
            fill-rule="nonzero"
          />
        </svg>
      </span>
      <span
        class="emotion-3"
      />
    </div>
  </div>
</DocumentFragment>
`;

exports[`<Notification /> notification snapshot tests for inverse styles renders inverse success state correctly BananaRepublic in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  transition-property: opacity;
  transition-duration: 250ms;
  transition-timing-function: cubic-bezier(0.25, 0, 0.25, 1);
  transition-delay: 0ms;
  opacity: 1;
}

.emotion-1 {
  line-height: 1.25;
  padding: 0.5rem;
  margin-bottom: 1.125rem;
  border-style: solid;
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 0.875rem;
  color: #F6F4EB;
  border-color: #82B05D;
  border-width: 2px 2px 2px 8px;
}

.emotion-2 {
  display: inline-block;
  height: 20px;
  width: 20px;
  min-height: 20px;
  min-width: 20px;
}

.emotion-2 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-2 svg path {
  fill: #82B05D;
}

.emotion-2 svg rect {
  fill: #82B05D;
}

.emotion-3 {
  padding-left: 0.75rem;
}

<div
    class="emotion-0"
  >
    <div
      aria-live="polite"
      class="emotion-1"
      kind="success"
    >
      <span
        aria-hidden="true"
        class="emotion-2"
      >
        <svg
          viewBox="0 0 32 32"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M16.118 0c8.837 0 16 7.163 16 16s-7.163 16-16 16-16-7.163-16-16 7.163-16 16-16zm6.552 9l-9.146 9.146-4.526-4.526-2.88 2.88 7.5 7.5 12.028-12.026L22.67 9z"
            fill="#16A816"
            fill-rule="nonzero"
          />
        </svg>
      </span>
      <span
        class="emotion-3"
      />
    </div>
  </div>
</DocumentFragment>
`;

exports[`<Notification /> notification snapshot tests for inverse styles renders inverse success state correctly BananaRepublicFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  transition-property: opacity;
  transition-duration: 250ms;
  transition-timing-function: cubic-bezier(0.25, 0, 0.25, 1);
  transition-delay: 0ms;
  opacity: 1;
}

.emotion-1 {
  line-height: 1.25;
  padding: 0.5rem;
  margin-bottom: 1.125rem;
  border-style: solid;
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  font-family: 'Lynstone',Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 0.8125rem;
  color: #F6F4EB;
  border-color: #82B05D;
  border-width: 2px 2px 2px 8px;
}

.emotion-2 {
  display: inline-block;
  height: 20px;
  width: 20px;
  min-height: 20px;
  min-width: 20px;
}

.emotion-2 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-2 svg path {
  fill: #82B05D;
}

.emotion-2 svg rect {
  fill: #82B05D;
}

.emotion-3 {
  padding-left: 0.75rem;
}

<div
    class="emotion-0"
  >
    <div
      aria-live="polite"
      class="emotion-1"
      kind="success"
    >
      <span
        aria-hidden="true"
        class="emotion-2"
      >
        <svg
          viewBox="0 0 32 32"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M16.118 0c8.837 0 16 7.163 16 16s-7.163 16-16 16-16-7.163-16-16 7.163-16 16-16zm6.552 9l-9.146 9.146-4.526-4.526-2.88 2.88 7.5 7.5 12.028-12.026L22.67 9z"
            fill="#16A816"
            fill-rule="nonzero"
          />
        </svg>
      </span>
      <span
        class="emotion-3"
      />
    </div>
  </div>
</DocumentFragment>
`;

exports[`<Notification /> notification snapshot tests for inverse styles renders inverse success state correctly BananaRepublicFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  transition-property: opacity;
  transition-duration: 250ms;
  transition-timing-function: cubic-bezier(0.25, 0, 0.25, 1);
  transition-delay: 0ms;
  opacity: 1;
}

.emotion-1 {
  line-height: 1.25;
  padding: 0.5rem;
  margin-bottom: 1.125rem;
  border-style: solid;
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 0.875rem;
  color: #F6F4EB;
  border-color: #82B05D;
  border-width: 2px 2px 2px 8px;
}

.emotion-2 {
  display: inline-block;
  height: 20px;
  width: 20px;
  min-height: 20px;
  min-width: 20px;
}

.emotion-2 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-2 svg path {
  fill: #82B05D;
}

.emotion-2 svg rect {
  fill: #82B05D;
}

.emotion-3 {
  padding-left: 0.75rem;
}

<div
    class="emotion-0"
  >
    <div
      aria-live="polite"
      class="emotion-1"
      kind="success"
    >
      <span
        aria-hidden="true"
        class="emotion-2"
      >
        <svg
          viewBox="0 0 32 32"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M16.118 0c8.837 0 16 7.163 16 16s-7.163 16-16 16-16-7.163-16-16 7.163-16 16-16zm6.552 9l-9.146 9.146-4.526-4.526-2.88 2.88 7.5 7.5 12.028-12.026L22.67 9z"
            fill="#16A816"
            fill-rule="nonzero"
          />
        </svg>
      </span>
      <span
        class="emotion-3"
      />
    </div>
  </div>
</DocumentFragment>
`;

exports[`<Notification /> notification snapshot tests for inverse styles renders inverse success state correctly Gap 1`] = `
<DocumentFragment>
  .emotion-0 {
  transition-property: opacity;
  transition-duration: 250ms;
  transition-timing-function: cubic-bezier(0.25, 0, 0.25, 1);
  transition-delay: 0ms;
  opacity: 1;
}

.emotion-1 {
  line-height: 1.25;
  padding: 0.5rem;
  margin-bottom: 1.125rem;
  border-style: solid;
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 0.8125rem;
  color: #FFFFFF;
  border-color: #82B05D;
  border-width: 2px 2px 2px 8px;
}

.emotion-2 {
  display: inline-block;
  height: 20px;
  width: 20px;
  min-height: 20px;
  min-width: 20px;
}

.emotion-2 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-2 svg path {
  fill: #82B05D;
}

.emotion-2 svg rect {
  fill: #82B05D;
}

.emotion-3 {
  padding-left: 0.75rem;
}

<div
    class="emotion-0"
  >
    <div
      aria-live="polite"
      class="emotion-1"
      kind="success"
    >
      <span
        aria-hidden="true"
        class="emotion-2"
      >
        <svg
          viewBox="0 0 32 32"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M16.118 0c8.837 0 16 7.163 16 16s-7.163 16-16 16-16-7.163-16-16 7.163-16 16-16zm6.552 9l-9.146 9.146-4.526-4.526-2.88 2.88 7.5 7.5 12.028-12.026L22.67 9z"
            fill="#16A816"
            fill-rule="nonzero"
          />
        </svg>
      </span>
      <span
        class="emotion-3"
      />
    </div>
  </div>
</DocumentFragment>
`;

exports[`<Notification /> notification snapshot tests for inverse styles renders inverse success state correctly Gap in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  transition-property: opacity;
  transition-duration: 250ms;
  transition-timing-function: cubic-bezier(0.25, 0, 0.25, 1);
  transition-delay: 0ms;
  opacity: 1;
}

.emotion-1 {
  line-height: 1.25;
  padding: 0.5rem;
  margin-bottom: 1.125rem;
  border-style: solid;
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 0.875rem;
  color: #FFFFFF;
  border-color: #82B05D;
  border-width: 2px 2px 2px 8px;
}

.emotion-2 {
  display: inline-block;
  height: 20px;
  width: 20px;
  min-height: 20px;
  min-width: 20px;
}

.emotion-2 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-2 svg path {
  fill: #82B05D;
}

.emotion-2 svg rect {
  fill: #82B05D;
}

.emotion-3 {
  padding-left: 0.75rem;
}

<div
    class="emotion-0"
  >
    <div
      aria-live="polite"
      class="emotion-1"
      kind="success"
    >
      <span
        aria-hidden="true"
        class="emotion-2"
      >
        <svg
          viewBox="0 0 32 32"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M16.118 0c8.837 0 16 7.163 16 16s-7.163 16-16 16-16-7.163-16-16 7.163-16 16-16zm6.552 9l-9.146 9.146-4.526-4.526-2.88 2.88 7.5 7.5 12.028-12.026L22.67 9z"
            fill="#16A816"
            fill-rule="nonzero"
          />
        </svg>
      </span>
      <span
        class="emotion-3"
      />
    </div>
  </div>
</DocumentFragment>
`;

exports[`<Notification /> notification snapshot tests for inverse styles renders inverse success state correctly GapFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  transition-property: opacity;
  transition-duration: 250ms;
  transition-timing-function: cubic-bezier(0.25, 0, 0.25, 1);
  transition-delay: 0ms;
  opacity: 1;
}

.emotion-1 {
  line-height: 1.25;
  padding: 0.5rem;
  margin-bottom: 1.125rem;
  border-style: solid;
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 0.8125rem;
  color: #FFFFFF;
  border-color: #82B05D;
  border-width: 2px 2px 2px 8px;
}

.emotion-2 {
  display: inline-block;
  height: 20px;
  width: 20px;
  min-height: 20px;
  min-width: 20px;
}

.emotion-2 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-2 svg path {
  fill: #82B05D;
}

.emotion-2 svg rect {
  fill: #82B05D;
}

.emotion-3 {
  padding-left: 0.75rem;
}

<div
    class="emotion-0"
  >
    <div
      aria-live="polite"
      class="emotion-1"
      kind="success"
    >
      <span
        aria-hidden="true"
        class="emotion-2"
      >
        <svg
          viewBox="0 0 32 32"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M16.118 0c8.837 0 16 7.163 16 16s-7.163 16-16 16-16-7.163-16-16 7.163-16 16-16zm6.552 9l-9.146 9.146-4.526-4.526-2.88 2.88 7.5 7.5 12.028-12.026L22.67 9z"
            fill="#16A816"
            fill-rule="nonzero"
          />
        </svg>
      </span>
      <span
        class="emotion-3"
      />
    </div>
  </div>
</DocumentFragment>
`;

exports[`<Notification /> notification snapshot tests for inverse styles renders inverse success state correctly GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  transition-property: opacity;
  transition-duration: 250ms;
  transition-timing-function: cubic-bezier(0.25, 0, 0.25, 1);
  transition-delay: 0ms;
  opacity: 1;
}

.emotion-1 {
  line-height: 1.25;
  padding: 0.5rem;
  margin-bottom: 1.125rem;
  border-style: solid;
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 0.875rem;
  color: #FFFFFF;
  border-color: #82B05D;
  border-width: 2px 2px 2px 8px;
}

.emotion-2 {
  display: inline-block;
  height: 20px;
  width: 20px;
  min-height: 20px;
  min-width: 20px;
}

.emotion-2 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-2 svg path {
  fill: #82B05D;
}

.emotion-2 svg rect {
  fill: #82B05D;
}

.emotion-3 {
  padding-left: 0.75rem;
}

<div
    class="emotion-0"
  >
    <div
      aria-live="polite"
      class="emotion-1"
      kind="success"
    >
      <span
        aria-hidden="true"
        class="emotion-2"
      >
        <svg
          viewBox="0 0 32 32"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M16.118 0c8.837 0 16 7.163 16 16s-7.163 16-16 16-16-7.163-16-16 7.163-16 16-16zm6.552 9l-9.146 9.146-4.526-4.526-2.88 2.88 7.5 7.5 12.028-12.026L22.67 9z"
            fill="#16A816"
            fill-rule="nonzero"
          />
        </svg>
      </span>
      <span
        class="emotion-3"
      />
    </div>
  </div>
</DocumentFragment>
`;

exports[`<Notification /> notification snapshot tests for inverse styles renders inverse success state correctly OldNavy 1`] = `
<DocumentFragment>
  .emotion-0 {
  transition-property: opacity;
  transition-duration: 250ms;
  transition-timing-function: cubic-bezier(0.25, 0, 0.25, 1);
  transition-delay: 0ms;
  opacity: 1;
}

.emotion-1 {
  line-height: 1.25;
  padding: 0.5rem;
  margin-bottom: 1.125rem;
  border-style: solid;
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  font-family: 'Gotham','Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 0.875rem;
  color: #FFFFFF;
  border-color: #82B05D;
  border-width: 2px 2px 2px 8px;
}

.emotion-2 {
  display: inline-block;
  height: 20px;
  width: 20px;
  min-height: 20px;
  min-width: 20px;
}

.emotion-2 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-2 svg path {
  fill: #82B05D;
}

.emotion-2 svg rect {
  fill: #82B05D;
}

.emotion-3 {
  padding-left: 0.75rem;
}

<div
    class="emotion-0"
  >
    <div
      aria-live="polite"
      class="emotion-1"
      kind="success"
    >
      <span
        aria-hidden="true"
        class="emotion-2"
      >
        <svg
          viewBox="0 0 32 32"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M16.118 0c8.837 0 16 7.163 16 16s-7.163 16-16 16-16-7.163-16-16 7.163-16 16-16zm6.552 9l-9.146 9.146-4.526-4.526-2.88 2.88 7.5 7.5 12.028-12.026L22.67 9z"
            fill="#16A816"
            fill-rule="nonzero"
          />
        </svg>
      </span>
      <span
        class="emotion-3"
      />
    </div>
  </div>
</DocumentFragment>
`;

exports[`<Notification /> notification snapshot tests for inverse styles renders inverse success state correctly OldNavy in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  transition-property: opacity;
  transition-duration: 250ms;
  transition-timing-function: cubic-bezier(0.25, 0, 0.25, 1);
  transition-delay: 0ms;
  opacity: 1;
}

.emotion-1 {
  line-height: 1.25;
  padding: 0.5rem;
  margin-bottom: 1.125rem;
  border-style: solid;
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 0.875rem;
  color: #FFFFFF;
  border-color: #82B05D;
  border-width: 2px 2px 2px 8px;
}

.emotion-2 {
  display: inline-block;
  height: 20px;
  width: 20px;
  min-height: 20px;
  min-width: 20px;
}

.emotion-2 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-2 svg path {
  fill: #82B05D;
}

.emotion-2 svg rect {
  fill: #82B05D;
}

.emotion-3 {
  padding-left: 0.75rem;
}

<div
    class="emotion-0"
  >
    <div
      aria-live="polite"
      class="emotion-1"
      kind="success"
    >
      <span
        aria-hidden="true"
        class="emotion-2"
      >
        <svg
          viewBox="0 0 32 32"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M16.118 0c8.837 0 16 7.163 16 16s-7.163 16-16 16-16-7.163-16-16 7.163-16 16-16zm6.552 9l-9.146 9.146-4.526-4.526-2.88 2.88 7.5 7.5 12.028-12.026L22.67 9z"
            fill="#16A816"
            fill-rule="nonzero"
          />
        </svg>
      </span>
      <span
        class="emotion-3"
      />
    </div>
  </div>
</DocumentFragment>
`;

exports[`<Notification /> notification snapshot tests for inverse styles renders inverse warning state correctly Athleta 1`] = `
<DocumentFragment>
  .emotion-0 {
  transition-property: opacity;
  transition-duration: 250ms;
  transition-timing-function: cubic-bezier(0.25, 0, 0.25, 1);
  transition-delay: 0ms;
  opacity: 1;
}

.emotion-1 {
  line-height: 1.25;
  padding: 0.5rem;
  margin-bottom: 1.125rem;
  border-style: solid;
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  font-family: 'Avenir Next','Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 0.875rem;
  color: #FFFFFF;
  border-color: #FFC83A;
  border-width: 2px 2px 2px 8px;
}

.emotion-2 {
  display: inline-block;
  height: 20px;
  width: 20px;
  min-height: 20px;
  min-width: 20px;
}

.emotion-2 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-2 svg path {
  fill: #FFC83A;
}

.emotion-2 svg rect {
  fill: #FFC83A;
}

.emotion-3 {
  padding-left: 0.75rem;
}

<div
    class="emotion-0"
  >
    <div
      aria-live="polite"
      class="emotion-1"
      kind="warning"
    >
      <span
        aria-hidden="true"
        class="emotion-2"
      >
        <svg
          viewBox="0 0 33 33"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M18.117.402c.659.33 1.193.864 1.522 1.522l12.597 25.194a3.403 3.403 0 01-3.044 4.925H4a3.403 3.403 0 01-3.044-4.925L13.552 1.924A3.403 3.403 0 0118.117.402zm-1.516 23.156c-1.393 0-2.436 1.047-2.436 2.445 0 1.39 1.045 2.434 2.436 2.434s2.426-1.044 2.426-2.434c0-1.398-1.032-2.445-2.426-2.445zm2.02-14.565h-4.05v12.186h4.05V8.993z"
            fill="#F0B00B"
            fill-rule="nonzero"
          />
        </svg>
      </span>
      <span
        class="emotion-3"
      />
    </div>
  </div>
</DocumentFragment>
`;

exports[`<Notification /> notification snapshot tests for inverse styles renders inverse warning state correctly Athleta in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  transition-property: opacity;
  transition-duration: 250ms;
  transition-timing-function: cubic-bezier(0.25, 0, 0.25, 1);
  transition-delay: 0ms;
  opacity: 1;
}

.emotion-1 {
  line-height: 1.25;
  padding: 0.5rem;
  margin-bottom: 1.125rem;
  border-style: solid;
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 0.875rem;
  color: #FFFFFF;
  border-color: #FFC83A;
  border-width: 2px 2px 2px 8px;
}

.emotion-2 {
  display: inline-block;
  height: 20px;
  width: 20px;
  min-height: 20px;
  min-width: 20px;
}

.emotion-2 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-2 svg path {
  fill: #FFC83A;
}

.emotion-2 svg rect {
  fill: #FFC83A;
}

.emotion-3 {
  padding-left: 0.75rem;
}

<div
    class="emotion-0"
  >
    <div
      aria-live="polite"
      class="emotion-1"
      kind="warning"
    >
      <span
        aria-hidden="true"
        class="emotion-2"
      >
        <svg
          viewBox="0 0 33 33"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M18.117.402c.659.33 1.193.864 1.522 1.522l12.597 25.194a3.403 3.403 0 01-3.044 4.925H4a3.403 3.403 0 01-3.044-4.925L13.552 1.924A3.403 3.403 0 0118.117.402zm-1.516 23.156c-1.393 0-2.436 1.047-2.436 2.445 0 1.39 1.045 2.434 2.436 2.434s2.426-1.044 2.426-2.434c0-1.398-1.032-2.445-2.426-2.445zm2.02-14.565h-4.05v12.186h4.05V8.993z"
            fill="#F0B00B"
            fill-rule="nonzero"
          />
        </svg>
      </span>
      <span
        class="emotion-3"
      />
    </div>
  </div>
</DocumentFragment>
`;

exports[`<Notification /> notification snapshot tests for inverse styles renders inverse warning state correctly BananaRepublic 1`] = `
<DocumentFragment>
  .emotion-0 {
  transition-property: opacity;
  transition-duration: 250ms;
  transition-timing-function: cubic-bezier(0.25, 0, 0.25, 1);
  transition-delay: 0ms;
  opacity: 1;
}

.emotion-1 {
  line-height: 1.25;
  padding: 0.5rem;
  margin-bottom: 1.125rem;
  border-style: solid;
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  font-family: 'Lynstone',Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 0.8125rem;
  color: #F6F4EB;
  border-color: #FFC83A;
  border-width: 2px 2px 2px 8px;
}

.emotion-2 {
  display: inline-block;
  height: 20px;
  width: 20px;
  min-height: 20px;
  min-width: 20px;
}

.emotion-2 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-2 svg path {
  fill: #FFC83A;
}

.emotion-2 svg rect {
  fill: #FFC83A;
}

.emotion-3 {
  padding-left: 0.75rem;
}

<div
    class="emotion-0"
  >
    <div
      aria-live="polite"
      class="emotion-1"
      kind="warning"
    >
      <span
        aria-hidden="true"
        class="emotion-2"
      >
        <svg
          viewBox="0 0 33 33"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M18.117.402c.659.33 1.193.864 1.522 1.522l12.597 25.194a3.403 3.403 0 01-3.044 4.925H4a3.403 3.403 0 01-3.044-4.925L13.552 1.924A3.403 3.403 0 0118.117.402zm-1.516 23.156c-1.393 0-2.436 1.047-2.436 2.445 0 1.39 1.045 2.434 2.436 2.434s2.426-1.044 2.426-2.434c0-1.398-1.032-2.445-2.426-2.445zm2.02-14.565h-4.05v12.186h4.05V8.993z"
            fill="#F0B00B"
            fill-rule="nonzero"
          />
        </svg>
      </span>
      <span
        class="emotion-3"
      />
    </div>
  </div>
</DocumentFragment>
`;

exports[`<Notification /> notification snapshot tests for inverse styles renders inverse warning state correctly BananaRepublic in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  transition-property: opacity;
  transition-duration: 250ms;
  transition-timing-function: cubic-bezier(0.25, 0, 0.25, 1);
  transition-delay: 0ms;
  opacity: 1;
}

.emotion-1 {
  line-height: 1.25;
  padding: 0.5rem;
  margin-bottom: 1.125rem;
  border-style: solid;
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 0.875rem;
  color: #F6F4EB;
  border-color: #FFC83A;
  border-width: 2px 2px 2px 8px;
}

.emotion-2 {
  display: inline-block;
  height: 20px;
  width: 20px;
  min-height: 20px;
  min-width: 20px;
}

.emotion-2 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-2 svg path {
  fill: #FFC83A;
}

.emotion-2 svg rect {
  fill: #FFC83A;
}

.emotion-3 {
  padding-left: 0.75rem;
}

<div
    class="emotion-0"
  >
    <div
      aria-live="polite"
      class="emotion-1"
      kind="warning"
    >
      <span
        aria-hidden="true"
        class="emotion-2"
      >
        <svg
          viewBox="0 0 33 33"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M18.117.402c.659.33 1.193.864 1.522 1.522l12.597 25.194a3.403 3.403 0 01-3.044 4.925H4a3.403 3.403 0 01-3.044-4.925L13.552 1.924A3.403 3.403 0 0118.117.402zm-1.516 23.156c-1.393 0-2.436 1.047-2.436 2.445 0 1.39 1.045 2.434 2.436 2.434s2.426-1.044 2.426-2.434c0-1.398-1.032-2.445-2.426-2.445zm2.02-14.565h-4.05v12.186h4.05V8.993z"
            fill="#F0B00B"
            fill-rule="nonzero"
          />
        </svg>
      </span>
      <span
        class="emotion-3"
      />
    </div>
  </div>
</DocumentFragment>
`;

exports[`<Notification /> notification snapshot tests for inverse styles renders inverse warning state correctly BananaRepublicFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  transition-property: opacity;
  transition-duration: 250ms;
  transition-timing-function: cubic-bezier(0.25, 0, 0.25, 1);
  transition-delay: 0ms;
  opacity: 1;
}

.emotion-1 {
  line-height: 1.25;
  padding: 0.5rem;
  margin-bottom: 1.125rem;
  border-style: solid;
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  font-family: 'Lynstone',Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 0.8125rem;
  color: #F6F4EB;
  border-color: #FFC83A;
  border-width: 2px 2px 2px 8px;
}

.emotion-2 {
  display: inline-block;
  height: 20px;
  width: 20px;
  min-height: 20px;
  min-width: 20px;
}

.emotion-2 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-2 svg path {
  fill: #FFC83A;
}

.emotion-2 svg rect {
  fill: #FFC83A;
}

.emotion-3 {
  padding-left: 0.75rem;
}

<div
    class="emotion-0"
  >
    <div
      aria-live="polite"
      class="emotion-1"
      kind="warning"
    >
      <span
        aria-hidden="true"
        class="emotion-2"
      >
        <svg
          viewBox="0 0 33 33"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M18.117.402c.659.33 1.193.864 1.522 1.522l12.597 25.194a3.403 3.403 0 01-3.044 4.925H4a3.403 3.403 0 01-3.044-4.925L13.552 1.924A3.403 3.403 0 0118.117.402zm-1.516 23.156c-1.393 0-2.436 1.047-2.436 2.445 0 1.39 1.045 2.434 2.436 2.434s2.426-1.044 2.426-2.434c0-1.398-1.032-2.445-2.426-2.445zm2.02-14.565h-4.05v12.186h4.05V8.993z"
            fill="#F0B00B"
            fill-rule="nonzero"
          />
        </svg>
      </span>
      <span
        class="emotion-3"
      />
    </div>
  </div>
</DocumentFragment>
`;

exports[`<Notification /> notification snapshot tests for inverse styles renders inverse warning state correctly BananaRepublicFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  transition-property: opacity;
  transition-duration: 250ms;
  transition-timing-function: cubic-bezier(0.25, 0, 0.25, 1);
  transition-delay: 0ms;
  opacity: 1;
}

.emotion-1 {
  line-height: 1.25;
  padding: 0.5rem;
  margin-bottom: 1.125rem;
  border-style: solid;
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 0.875rem;
  color: #F6F4EB;
  border-color: #FFC83A;
  border-width: 2px 2px 2px 8px;
}

.emotion-2 {
  display: inline-block;
  height: 20px;
  width: 20px;
  min-height: 20px;
  min-width: 20px;
}

.emotion-2 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-2 svg path {
  fill: #FFC83A;
}

.emotion-2 svg rect {
  fill: #FFC83A;
}

.emotion-3 {
  padding-left: 0.75rem;
}

<div
    class="emotion-0"
  >
    <div
      aria-live="polite"
      class="emotion-1"
      kind="warning"
    >
      <span
        aria-hidden="true"
        class="emotion-2"
      >
        <svg
          viewBox="0 0 33 33"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M18.117.402c.659.33 1.193.864 1.522 1.522l12.597 25.194a3.403 3.403 0 01-3.044 4.925H4a3.403 3.403 0 01-3.044-4.925L13.552 1.924A3.403 3.403 0 0118.117.402zm-1.516 23.156c-1.393 0-2.436 1.047-2.436 2.445 0 1.39 1.045 2.434 2.436 2.434s2.426-1.044 2.426-2.434c0-1.398-1.032-2.445-2.426-2.445zm2.02-14.565h-4.05v12.186h4.05V8.993z"
            fill="#F0B00B"
            fill-rule="nonzero"
          />
        </svg>
      </span>
      <span
        class="emotion-3"
      />
    </div>
  </div>
</DocumentFragment>
`;

exports[`<Notification /> notification snapshot tests for inverse styles renders inverse warning state correctly Gap 1`] = `
<DocumentFragment>
  .emotion-0 {
  transition-property: opacity;
  transition-duration: 250ms;
  transition-timing-function: cubic-bezier(0.25, 0, 0.25, 1);
  transition-delay: 0ms;
  opacity: 1;
}

.emotion-1 {
  line-height: 1.25;
  padding: 0.5rem;
  margin-bottom: 1.125rem;
  border-style: solid;
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 0.8125rem;
  color: #FFFFFF;
  border-color: #FFC83A;
  border-width: 2px 2px 2px 8px;
}

.emotion-2 {
  display: inline-block;
  height: 20px;
  width: 20px;
  min-height: 20px;
  min-width: 20px;
}

.emotion-2 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-2 svg path {
  fill: #FFC83A;
}

.emotion-2 svg rect {
  fill: #FFC83A;
}

.emotion-3 {
  padding-left: 0.75rem;
}

<div
    class="emotion-0"
  >
    <div
      aria-live="polite"
      class="emotion-1"
      kind="warning"
    >
      <span
        aria-hidden="true"
        class="emotion-2"
      >
        <svg
          viewBox="0 0 33 33"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M18.117.402c.659.33 1.193.864 1.522 1.522l12.597 25.194a3.403 3.403 0 01-3.044 4.925H4a3.403 3.403 0 01-3.044-4.925L13.552 1.924A3.403 3.403 0 0118.117.402zm-1.516 23.156c-1.393 0-2.436 1.047-2.436 2.445 0 1.39 1.045 2.434 2.436 2.434s2.426-1.044 2.426-2.434c0-1.398-1.032-2.445-2.426-2.445zm2.02-14.565h-4.05v12.186h4.05V8.993z"
            fill="#F0B00B"
            fill-rule="nonzero"
          />
        </svg>
      </span>
      <span
        class="emotion-3"
      />
    </div>
  </div>
</DocumentFragment>
`;

exports[`<Notification /> notification snapshot tests for inverse styles renders inverse warning state correctly Gap in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  transition-property: opacity;
  transition-duration: 250ms;
  transition-timing-function: cubic-bezier(0.25, 0, 0.25, 1);
  transition-delay: 0ms;
  opacity: 1;
}

.emotion-1 {
  line-height: 1.25;
  padding: 0.5rem;
  margin-bottom: 1.125rem;
  border-style: solid;
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 0.875rem;
  color: #FFFFFF;
  border-color: #FFC83A;
  border-width: 2px 2px 2px 8px;
}

.emotion-2 {
  display: inline-block;
  height: 20px;
  width: 20px;
  min-height: 20px;
  min-width: 20px;
}

.emotion-2 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-2 svg path {
  fill: #FFC83A;
}

.emotion-2 svg rect {
  fill: #FFC83A;
}

.emotion-3 {
  padding-left: 0.75rem;
}

<div
    class="emotion-0"
  >
    <div
      aria-live="polite"
      class="emotion-1"
      kind="warning"
    >
      <span
        aria-hidden="true"
        class="emotion-2"
      >
        <svg
          viewBox="0 0 33 33"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M18.117.402c.659.33 1.193.864 1.522 1.522l12.597 25.194a3.403 3.403 0 01-3.044 4.925H4a3.403 3.403 0 01-3.044-4.925L13.552 1.924A3.403 3.403 0 0118.117.402zm-1.516 23.156c-1.393 0-2.436 1.047-2.436 2.445 0 1.39 1.045 2.434 2.436 2.434s2.426-1.044 2.426-2.434c0-1.398-1.032-2.445-2.426-2.445zm2.02-14.565h-4.05v12.186h4.05V8.993z"
            fill="#F0B00B"
            fill-rule="nonzero"
          />
        </svg>
      </span>
      <span
        class="emotion-3"
      />
    </div>
  </div>
</DocumentFragment>
`;

exports[`<Notification /> notification snapshot tests for inverse styles renders inverse warning state correctly GapFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  transition-property: opacity;
  transition-duration: 250ms;
  transition-timing-function: cubic-bezier(0.25, 0, 0.25, 1);
  transition-delay: 0ms;
  opacity: 1;
}

.emotion-1 {
  line-height: 1.25;
  padding: 0.5rem;
  margin-bottom: 1.125rem;
  border-style: solid;
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 0.8125rem;
  color: #FFFFFF;
  border-color: #FFC83A;
  border-width: 2px 2px 2px 8px;
}

.emotion-2 {
  display: inline-block;
  height: 20px;
  width: 20px;
  min-height: 20px;
  min-width: 20px;
}

.emotion-2 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-2 svg path {
  fill: #FFC83A;
}

.emotion-2 svg rect {
  fill: #FFC83A;
}

.emotion-3 {
  padding-left: 0.75rem;
}

<div
    class="emotion-0"
  >
    <div
      aria-live="polite"
      class="emotion-1"
      kind="warning"
    >
      <span
        aria-hidden="true"
        class="emotion-2"
      >
        <svg
          viewBox="0 0 33 33"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M18.117.402c.659.33 1.193.864 1.522 1.522l12.597 25.194a3.403 3.403 0 01-3.044 4.925H4a3.403 3.403 0 01-3.044-4.925L13.552 1.924A3.403 3.403 0 0118.117.402zm-1.516 23.156c-1.393 0-2.436 1.047-2.436 2.445 0 1.39 1.045 2.434 2.436 2.434s2.426-1.044 2.426-2.434c0-1.398-1.032-2.445-2.426-2.445zm2.02-14.565h-4.05v12.186h4.05V8.993z"
            fill="#F0B00B"
            fill-rule="nonzero"
          />
        </svg>
      </span>
      <span
        class="emotion-3"
      />
    </div>
  </div>
</DocumentFragment>
`;

exports[`<Notification /> notification snapshot tests for inverse styles renders inverse warning state correctly GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  transition-property: opacity;
  transition-duration: 250ms;
  transition-timing-function: cubic-bezier(0.25, 0, 0.25, 1);
  transition-delay: 0ms;
  opacity: 1;
}

.emotion-1 {
  line-height: 1.25;
  padding: 0.5rem;
  margin-bottom: 1.125rem;
  border-style: solid;
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 0.875rem;
  color: #FFFFFF;
  border-color: #FFC83A;
  border-width: 2px 2px 2px 8px;
}

.emotion-2 {
  display: inline-block;
  height: 20px;
  width: 20px;
  min-height: 20px;
  min-width: 20px;
}

.emotion-2 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-2 svg path {
  fill: #FFC83A;
}

.emotion-2 svg rect {
  fill: #FFC83A;
}

.emotion-3 {
  padding-left: 0.75rem;
}

<div
    class="emotion-0"
  >
    <div
      aria-live="polite"
      class="emotion-1"
      kind="warning"
    >
      <span
        aria-hidden="true"
        class="emotion-2"
      >
        <svg
          viewBox="0 0 33 33"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M18.117.402c.659.33 1.193.864 1.522 1.522l12.597 25.194a3.403 3.403 0 01-3.044 4.925H4a3.403 3.403 0 01-3.044-4.925L13.552 1.924A3.403 3.403 0 0118.117.402zm-1.516 23.156c-1.393 0-2.436 1.047-2.436 2.445 0 1.39 1.045 2.434 2.436 2.434s2.426-1.044 2.426-2.434c0-1.398-1.032-2.445-2.426-2.445zm2.02-14.565h-4.05v12.186h4.05V8.993z"
            fill="#F0B00B"
            fill-rule="nonzero"
          />
        </svg>
      </span>
      <span
        class="emotion-3"
      />
    </div>
  </div>
</DocumentFragment>
`;

exports[`<Notification /> notification snapshot tests for inverse styles renders inverse warning state correctly OldNavy 1`] = `
<DocumentFragment>
  .emotion-0 {
  transition-property: opacity;
  transition-duration: 250ms;
  transition-timing-function: cubic-bezier(0.25, 0, 0.25, 1);
  transition-delay: 0ms;
  opacity: 1;
}

.emotion-1 {
  line-height: 1.25;
  padding: 0.5rem;
  margin-bottom: 1.125rem;
  border-style: solid;
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  font-family: 'Gotham','Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 0.875rem;
  color: #FFFFFF;
  border-color: #FFC83A;
  border-width: 2px 2px 2px 8px;
}

.emotion-2 {
  display: inline-block;
  height: 20px;
  width: 20px;
  min-height: 20px;
  min-width: 20px;
}

.emotion-2 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-2 svg path {
  fill: #FFC83A;
}

.emotion-2 svg rect {
  fill: #FFC83A;
}

.emotion-3 {
  padding-left: 0.75rem;
}

<div
    class="emotion-0"
  >
    <div
      aria-live="polite"
      class="emotion-1"
      kind="warning"
    >
      <span
        aria-hidden="true"
        class="emotion-2"
      >
        <svg
          viewBox="0 0 33 33"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M18.117.402c.659.33 1.193.864 1.522 1.522l12.597 25.194a3.403 3.403 0 01-3.044 4.925H4a3.403 3.403 0 01-3.044-4.925L13.552 1.924A3.403 3.403 0 0118.117.402zm-1.516 23.156c-1.393 0-2.436 1.047-2.436 2.445 0 1.39 1.045 2.434 2.436 2.434s2.426-1.044 2.426-2.434c0-1.398-1.032-2.445-2.426-2.445zm2.02-14.565h-4.05v12.186h4.05V8.993z"
            fill="#F0B00B"
            fill-rule="nonzero"
          />
        </svg>
      </span>
      <span
        class="emotion-3"
      />
    </div>
  </div>
</DocumentFragment>
`;

exports[`<Notification /> notification snapshot tests for inverse styles renders inverse warning state correctly OldNavy in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  transition-property: opacity;
  transition-duration: 250ms;
  transition-timing-function: cubic-bezier(0.25, 0, 0.25, 1);
  transition-delay: 0ms;
  opacity: 1;
}

.emotion-1 {
  line-height: 1.25;
  padding: 0.5rem;
  margin-bottom: 1.125rem;
  border-style: solid;
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 0.875rem;
  color: #FFFFFF;
  border-color: #FFC83A;
  border-width: 2px 2px 2px 8px;
}

.emotion-2 {
  display: inline-block;
  height: 20px;
  width: 20px;
  min-height: 20px;
  min-width: 20px;
}

.emotion-2 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-2 svg path {
  fill: #FFC83A;
}

.emotion-2 svg rect {
  fill: #FFC83A;
}

.emotion-3 {
  padding-left: 0.75rem;
}

<div
    class="emotion-0"
  >
    <div
      aria-live="polite"
      class="emotion-1"
      kind="warning"
    >
      <span
        aria-hidden="true"
        class="emotion-2"
      >
        <svg
          viewBox="0 0 33 33"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M18.117.402c.659.33 1.193.864 1.522 1.522l12.597 25.194a3.403 3.403 0 01-3.044 4.925H4a3.403 3.403 0 01-3.044-4.925L13.552 1.924A3.403 3.403 0 0118.117.402zm-1.516 23.156c-1.393 0-2.436 1.047-2.436 2.445 0 1.39 1.045 2.434 2.436 2.434s2.426-1.044 2.426-2.434c0-1.398-1.032-2.445-2.426-2.445zm2.02-14.565h-4.05v12.186h4.05V8.993z"
            fill="#F0B00B"
            fill-rule="nonzero"
          />
        </svg>
      </span>
      <span
        class="emotion-3"
      />
    </div>
  </div>
</DocumentFragment>
`;

exports[`<Notification /> notification snapshot tests for inverse styles renders success state correctly Athleta 1`] = `
<DocumentFragment>
  .emotion-0 {
  transition-property: opacity;
  transition-duration: 250ms;
  transition-timing-function: cubic-bezier(0.25, 0, 0.25, 1);
  transition-delay: 0ms;
  opacity: 1;
}

.emotion-1 {
  line-height: 1.25;
  padding: 0.5rem;
  margin-bottom: 1.125rem;
  border-style: solid;
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  font-family: 'Avenir Next','Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 0.875rem;
  color: #FFFFFF;
  border-color: #82B05D;
  border-width: 2px 2px 2px 8px;
}

.emotion-2 {
  display: inline-block;
  height: 20px;
  width: 20px;
  min-height: 20px;
  min-width: 20px;
}

.emotion-2 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-2 svg path {
  fill: #82B05D;
}

.emotion-2 svg rect {
  fill: #82B05D;
}

.emotion-3 {
  padding-left: 0.75rem;
}

<div
    class="emotion-0"
  >
    <div
      aria-live="polite"
      class="emotion-1"
      kind="success"
    >
      <span
        aria-hidden="true"
        class="emotion-2"
      >
        <svg
          viewBox="0 0 32 32"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M16.118 0c8.837 0 16 7.163 16 16s-7.163 16-16 16-16-7.163-16-16 7.163-16 16-16zm6.552 9l-9.146 9.146-4.526-4.526-2.88 2.88 7.5 7.5 12.028-12.026L22.67 9z"
            fill="#16A816"
            fill-rule="nonzero"
          />
        </svg>
      </span>
      <span
        class="emotion-3"
      />
    </div>
  </div>
</DocumentFragment>
`;

exports[`<Notification /> notification snapshot tests for inverse styles renders success state correctly Athleta in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  transition-property: opacity;
  transition-duration: 250ms;
  transition-timing-function: cubic-bezier(0.25, 0, 0.25, 1);
  transition-delay: 0ms;
  opacity: 1;
}

.emotion-1 {
  line-height: 1.25;
  padding: 0.5rem;
  margin-bottom: 1.125rem;
  border-style: solid;
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 0.875rem;
  color: #FFFFFF;
  border-color: #82B05D;
  border-width: 2px 2px 2px 8px;
}

.emotion-2 {
  display: inline-block;
  height: 20px;
  width: 20px;
  min-height: 20px;
  min-width: 20px;
}

.emotion-2 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-2 svg path {
  fill: #82B05D;
}

.emotion-2 svg rect {
  fill: #82B05D;
}

.emotion-3 {
  padding-left: 0.75rem;
}

<div
    class="emotion-0"
  >
    <div
      aria-live="polite"
      class="emotion-1"
      kind="success"
    >
      <span
        aria-hidden="true"
        class="emotion-2"
      >
        <svg
          viewBox="0 0 32 32"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M16.118 0c8.837 0 16 7.163 16 16s-7.163 16-16 16-16-7.163-16-16 7.163-16 16-16zm6.552 9l-9.146 9.146-4.526-4.526-2.88 2.88 7.5 7.5 12.028-12.026L22.67 9z"
            fill="#16A816"
            fill-rule="nonzero"
          />
        </svg>
      </span>
      <span
        class="emotion-3"
      />
    </div>
  </div>
</DocumentFragment>
`;

exports[`<Notification /> notification snapshot tests for inverse styles renders success state correctly BananaRepublic 1`] = `
<DocumentFragment>
  .emotion-0 {
  transition-property: opacity;
  transition-duration: 250ms;
  transition-timing-function: cubic-bezier(0.25, 0, 0.25, 1);
  transition-delay: 0ms;
  opacity: 1;
}

.emotion-1 {
  line-height: 1.25;
  padding: 0.5rem;
  margin-bottom: 1.125rem;
  border-style: solid;
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  font-family: 'Lynstone',Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 0.8125rem;
  color: #F6F4EB;
  border-color: #82B05D;
  border-width: 2px 2px 2px 8px;
}

.emotion-2 {
  display: inline-block;
  height: 20px;
  width: 20px;
  min-height: 20px;
  min-width: 20px;
}

.emotion-2 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-2 svg path {
  fill: #82B05D;
}

.emotion-2 svg rect {
  fill: #82B05D;
}

.emotion-3 {
  padding-left: 0.75rem;
}

<div
    class="emotion-0"
  >
    <div
      aria-live="polite"
      class="emotion-1"
      kind="success"
    >
      <span
        aria-hidden="true"
        class="emotion-2"
      >
        <svg
          viewBox="0 0 32 32"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M16.118 0c8.837 0 16 7.163 16 16s-7.163 16-16 16-16-7.163-16-16 7.163-16 16-16zm6.552 9l-9.146 9.146-4.526-4.526-2.88 2.88 7.5 7.5 12.028-12.026L22.67 9z"
            fill="#16A816"
            fill-rule="nonzero"
          />
        </svg>
      </span>
      <span
        class="emotion-3"
      />
    </div>
  </div>
</DocumentFragment>
`;

exports[`<Notification /> notification snapshot tests for inverse styles renders success state correctly BananaRepublic in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  transition-property: opacity;
  transition-duration: 250ms;
  transition-timing-function: cubic-bezier(0.25, 0, 0.25, 1);
  transition-delay: 0ms;
  opacity: 1;
}

.emotion-1 {
  line-height: 1.25;
  padding: 0.5rem;
  margin-bottom: 1.125rem;
  border-style: solid;
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 0.875rem;
  color: #F6F4EB;
  border-color: #82B05D;
  border-width: 2px 2px 2px 8px;
}

.emotion-2 {
  display: inline-block;
  height: 20px;
  width: 20px;
  min-height: 20px;
  min-width: 20px;
}

.emotion-2 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-2 svg path {
  fill: #82B05D;
}

.emotion-2 svg rect {
  fill: #82B05D;
}

.emotion-3 {
  padding-left: 0.75rem;
}

<div
    class="emotion-0"
  >
    <div
      aria-live="polite"
      class="emotion-1"
      kind="success"
    >
      <span
        aria-hidden="true"
        class="emotion-2"
      >
        <svg
          viewBox="0 0 32 32"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M16.118 0c8.837 0 16 7.163 16 16s-7.163 16-16 16-16-7.163-16-16 7.163-16 16-16zm6.552 9l-9.146 9.146-4.526-4.526-2.88 2.88 7.5 7.5 12.028-12.026L22.67 9z"
            fill="#16A816"
            fill-rule="nonzero"
          />
        </svg>
      </span>
      <span
        class="emotion-3"
      />
    </div>
  </div>
</DocumentFragment>
`;

exports[`<Notification /> notification snapshot tests for inverse styles renders success state correctly BananaRepublicFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  transition-property: opacity;
  transition-duration: 250ms;
  transition-timing-function: cubic-bezier(0.25, 0, 0.25, 1);
  transition-delay: 0ms;
  opacity: 1;
}

.emotion-1 {
  line-height: 1.25;
  padding: 0.5rem;
  margin-bottom: 1.125rem;
  border-style: solid;
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  font-family: 'Lynstone',Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 0.8125rem;
  color: #F6F4EB;
  border-color: #82B05D;
  border-width: 2px 2px 2px 8px;
}

.emotion-2 {
  display: inline-block;
  height: 20px;
  width: 20px;
  min-height: 20px;
  min-width: 20px;
}

.emotion-2 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-2 svg path {
  fill: #82B05D;
}

.emotion-2 svg rect {
  fill: #82B05D;
}

.emotion-3 {
  padding-left: 0.75rem;
}

<div
    class="emotion-0"
  >
    <div
      aria-live="polite"
      class="emotion-1"
      kind="success"
    >
      <span
        aria-hidden="true"
        class="emotion-2"
      >
        <svg
          viewBox="0 0 32 32"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M16.118 0c8.837 0 16 7.163 16 16s-7.163 16-16 16-16-7.163-16-16 7.163-16 16-16zm6.552 9l-9.146 9.146-4.526-4.526-2.88 2.88 7.5 7.5 12.028-12.026L22.67 9z"
            fill="#16A816"
            fill-rule="nonzero"
          />
        </svg>
      </span>
      <span
        class="emotion-3"
      />
    </div>
  </div>
</DocumentFragment>
`;

exports[`<Notification /> notification snapshot tests for inverse styles renders success state correctly BananaRepublicFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  transition-property: opacity;
  transition-duration: 250ms;
  transition-timing-function: cubic-bezier(0.25, 0, 0.25, 1);
  transition-delay: 0ms;
  opacity: 1;
}

.emotion-1 {
  line-height: 1.25;
  padding: 0.5rem;
  margin-bottom: 1.125rem;
  border-style: solid;
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 0.875rem;
  color: #F6F4EB;
  border-color: #82B05D;
  border-width: 2px 2px 2px 8px;
}

.emotion-2 {
  display: inline-block;
  height: 20px;
  width: 20px;
  min-height: 20px;
  min-width: 20px;
}

.emotion-2 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-2 svg path {
  fill: #82B05D;
}

.emotion-2 svg rect {
  fill: #82B05D;
}

.emotion-3 {
  padding-left: 0.75rem;
}

<div
    class="emotion-0"
  >
    <div
      aria-live="polite"
      class="emotion-1"
      kind="success"
    >
      <span
        aria-hidden="true"
        class="emotion-2"
      >
        <svg
          viewBox="0 0 32 32"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M16.118 0c8.837 0 16 7.163 16 16s-7.163 16-16 16-16-7.163-16-16 7.163-16 16-16zm6.552 9l-9.146 9.146-4.526-4.526-2.88 2.88 7.5 7.5 12.028-12.026L22.67 9z"
            fill="#16A816"
            fill-rule="nonzero"
          />
        </svg>
      </span>
      <span
        class="emotion-3"
      />
    </div>
  </div>
</DocumentFragment>
`;

exports[`<Notification /> notification snapshot tests for inverse styles renders success state correctly Gap 1`] = `
<DocumentFragment>
  .emotion-0 {
  transition-property: opacity;
  transition-duration: 250ms;
  transition-timing-function: cubic-bezier(0.25, 0, 0.25, 1);
  transition-delay: 0ms;
  opacity: 1;
}

.emotion-1 {
  line-height: 1.25;
  padding: 0.5rem;
  margin-bottom: 1.125rem;
  border-style: solid;
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 0.8125rem;
  color: #FFFFFF;
  border-color: #82B05D;
  border-width: 2px 2px 2px 8px;
}

.emotion-2 {
  display: inline-block;
  height: 20px;
  width: 20px;
  min-height: 20px;
  min-width: 20px;
}

.emotion-2 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-2 svg path {
  fill: #82B05D;
}

.emotion-2 svg rect {
  fill: #82B05D;
}

.emotion-3 {
  padding-left: 0.75rem;
}

<div
    class="emotion-0"
  >
    <div
      aria-live="polite"
      class="emotion-1"
      kind="success"
    >
      <span
        aria-hidden="true"
        class="emotion-2"
      >
        <svg
          viewBox="0 0 32 32"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M16.118 0c8.837 0 16 7.163 16 16s-7.163 16-16 16-16-7.163-16-16 7.163-16 16-16zm6.552 9l-9.146 9.146-4.526-4.526-2.88 2.88 7.5 7.5 12.028-12.026L22.67 9z"
            fill="#16A816"
            fill-rule="nonzero"
          />
        </svg>
      </span>
      <span
        class="emotion-3"
      />
    </div>
  </div>
</DocumentFragment>
`;

exports[`<Notification /> notification snapshot tests for inverse styles renders success state correctly Gap in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  transition-property: opacity;
  transition-duration: 250ms;
  transition-timing-function: cubic-bezier(0.25, 0, 0.25, 1);
  transition-delay: 0ms;
  opacity: 1;
}

.emotion-1 {
  line-height: 1.25;
  padding: 0.5rem;
  margin-bottom: 1.125rem;
  border-style: solid;
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 0.875rem;
  color: #FFFFFF;
  border-color: #82B05D;
  border-width: 2px 2px 2px 8px;
}

.emotion-2 {
  display: inline-block;
  height: 20px;
  width: 20px;
  min-height: 20px;
  min-width: 20px;
}

.emotion-2 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-2 svg path {
  fill: #82B05D;
}

.emotion-2 svg rect {
  fill: #82B05D;
}

.emotion-3 {
  padding-left: 0.75rem;
}

<div
    class="emotion-0"
  >
    <div
      aria-live="polite"
      class="emotion-1"
      kind="success"
    >
      <span
        aria-hidden="true"
        class="emotion-2"
      >
        <svg
          viewBox="0 0 32 32"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M16.118 0c8.837 0 16 7.163 16 16s-7.163 16-16 16-16-7.163-16-16 7.163-16 16-16zm6.552 9l-9.146 9.146-4.526-4.526-2.88 2.88 7.5 7.5 12.028-12.026L22.67 9z"
            fill="#16A816"
            fill-rule="nonzero"
          />
        </svg>
      </span>
      <span
        class="emotion-3"
      />
    </div>
  </div>
</DocumentFragment>
`;

exports[`<Notification /> notification snapshot tests for inverse styles renders success state correctly GapFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  transition-property: opacity;
  transition-duration: 250ms;
  transition-timing-function: cubic-bezier(0.25, 0, 0.25, 1);
  transition-delay: 0ms;
  opacity: 1;
}

.emotion-1 {
  line-height: 1.25;
  padding: 0.5rem;
  margin-bottom: 1.125rem;
  border-style: solid;
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 0.8125rem;
  color: #FFFFFF;
  border-color: #82B05D;
  border-width: 2px 2px 2px 8px;
}

.emotion-2 {
  display: inline-block;
  height: 20px;
  width: 20px;
  min-height: 20px;
  min-width: 20px;
}

.emotion-2 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-2 svg path {
  fill: #82B05D;
}

.emotion-2 svg rect {
  fill: #82B05D;
}

.emotion-3 {
  padding-left: 0.75rem;
}

<div
    class="emotion-0"
  >
    <div
      aria-live="polite"
      class="emotion-1"
      kind="success"
    >
      <span
        aria-hidden="true"
        class="emotion-2"
      >
        <svg
          viewBox="0 0 32 32"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M16.118 0c8.837 0 16 7.163 16 16s-7.163 16-16 16-16-7.163-16-16 7.163-16 16-16zm6.552 9l-9.146 9.146-4.526-4.526-2.88 2.88 7.5 7.5 12.028-12.026L22.67 9z"
            fill="#16A816"
            fill-rule="nonzero"
          />
        </svg>
      </span>
      <span
        class="emotion-3"
      />
    </div>
  </div>
</DocumentFragment>
`;

exports[`<Notification /> notification snapshot tests for inverse styles renders success state correctly GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  transition-property: opacity;
  transition-duration: 250ms;
  transition-timing-function: cubic-bezier(0.25, 0, 0.25, 1);
  transition-delay: 0ms;
  opacity: 1;
}

.emotion-1 {
  line-height: 1.25;
  padding: 0.5rem;
  margin-bottom: 1.125rem;
  border-style: solid;
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 0.875rem;
  color: #FFFFFF;
  border-color: #82B05D;
  border-width: 2px 2px 2px 8px;
}

.emotion-2 {
  display: inline-block;
  height: 20px;
  width: 20px;
  min-height: 20px;
  min-width: 20px;
}

.emotion-2 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-2 svg path {
  fill: #82B05D;
}

.emotion-2 svg rect {
  fill: #82B05D;
}

.emotion-3 {
  padding-left: 0.75rem;
}

<div
    class="emotion-0"
  >
    <div
      aria-live="polite"
      class="emotion-1"
      kind="success"
    >
      <span
        aria-hidden="true"
        class="emotion-2"
      >
        <svg
          viewBox="0 0 32 32"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M16.118 0c8.837 0 16 7.163 16 16s-7.163 16-16 16-16-7.163-16-16 7.163-16 16-16zm6.552 9l-9.146 9.146-4.526-4.526-2.88 2.88 7.5 7.5 12.028-12.026L22.67 9z"
            fill="#16A816"
            fill-rule="nonzero"
          />
        </svg>
      </span>
      <span
        class="emotion-3"
      />
    </div>
  </div>
</DocumentFragment>
`;

exports[`<Notification /> notification snapshot tests for inverse styles renders success state correctly OldNavy 1`] = `
<DocumentFragment>
  .emotion-0 {
  transition-property: opacity;
  transition-duration: 250ms;
  transition-timing-function: cubic-bezier(0.25, 0, 0.25, 1);
  transition-delay: 0ms;
  opacity: 1;
}

.emotion-1 {
  line-height: 1.25;
  padding: 0.5rem;
  margin-bottom: 1.125rem;
  border-style: solid;
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  font-family: 'Gotham','Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 0.875rem;
  color: #FFFFFF;
  border-color: #82B05D;
  border-width: 2px 2px 2px 8px;
}

.emotion-2 {
  display: inline-block;
  height: 20px;
  width: 20px;
  min-height: 20px;
  min-width: 20px;
}

.emotion-2 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-2 svg path {
  fill: #82B05D;
}

.emotion-2 svg rect {
  fill: #82B05D;
}

.emotion-3 {
  padding-left: 0.75rem;
}

<div
    class="emotion-0"
  >
    <div
      aria-live="polite"
      class="emotion-1"
      kind="success"
    >
      <span
        aria-hidden="true"
        class="emotion-2"
      >
        <svg
          viewBox="0 0 32 32"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M16.118 0c8.837 0 16 7.163 16 16s-7.163 16-16 16-16-7.163-16-16 7.163-16 16-16zm6.552 9l-9.146 9.146-4.526-4.526-2.88 2.88 7.5 7.5 12.028-12.026L22.67 9z"
            fill="#16A816"
            fill-rule="nonzero"
          />
        </svg>
      </span>
      <span
        class="emotion-3"
      />
    </div>
  </div>
</DocumentFragment>
`;

exports[`<Notification /> notification snapshot tests for inverse styles renders success state correctly OldNavy in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  transition-property: opacity;
  transition-duration: 250ms;
  transition-timing-function: cubic-bezier(0.25, 0, 0.25, 1);
  transition-delay: 0ms;
  opacity: 1;
}

.emotion-1 {
  line-height: 1.25;
  padding: 0.5rem;
  margin-bottom: 1.125rem;
  border-style: solid;
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 0.875rem;
  color: #FFFFFF;
  border-color: #82B05D;
  border-width: 2px 2px 2px 8px;
}

.emotion-2 {
  display: inline-block;
  height: 20px;
  width: 20px;
  min-height: 20px;
  min-width: 20px;
}

.emotion-2 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-2 svg path {
  fill: #82B05D;
}

.emotion-2 svg rect {
  fill: #82B05D;
}

.emotion-3 {
  padding-left: 0.75rem;
}

<div
    class="emotion-0"
  >
    <div
      aria-live="polite"
      class="emotion-1"
      kind="success"
    >
      <span
        aria-hidden="true"
        class="emotion-2"
      >
        <svg
          viewBox="0 0 32 32"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M16.118 0c8.837 0 16 7.163 16 16s-7.163 16-16 16-16-7.163-16-16 7.163-16 16-16zm6.552 9l-9.146 9.146-4.526-4.526-2.88 2.88 7.5 7.5 12.028-12.026L22.67 9z"
            fill="#16A816"
            fill-rule="nonzero"
          />
        </svg>
      </span>
      <span
        class="emotion-3"
      />
    </div>
  </div>
</DocumentFragment>
`;

exports[`<Notification /> notification snapshot tests for inverse styles renders warning state correctly Athleta 1`] = `
<DocumentFragment>
  .emotion-0 {
  transition-property: opacity;
  transition-duration: 250ms;
  transition-timing-function: cubic-bezier(0.25, 0, 0.25, 1);
  transition-delay: 0ms;
  opacity: 1;
}

.emotion-1 {
  line-height: 1.25;
  padding: 0.5rem;
  margin-bottom: 1.125rem;
  border-style: solid;
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  font-family: 'Avenir Next','Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 0.875rem;
  color: #FFFFFF;
  border-color: #FFC83A;
  border-width: 2px 2px 2px 8px;
}

.emotion-2 {
  display: inline-block;
  height: 20px;
  width: 20px;
  min-height: 20px;
  min-width: 20px;
}

.emotion-2 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-2 svg path {
  fill: #FFC83A;
}

.emotion-2 svg rect {
  fill: #FFC83A;
}

.emotion-3 {
  padding-left: 0.75rem;
}

<div
    class="emotion-0"
  >
    <div
      aria-live="polite"
      class="emotion-1"
      kind="warning"
    >
      <span
        aria-hidden="true"
        class="emotion-2"
      >
        <svg
          viewBox="0 0 33 33"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M18.117.402c.659.33 1.193.864 1.522 1.522l12.597 25.194a3.403 3.403 0 01-3.044 4.925H4a3.403 3.403 0 01-3.044-4.925L13.552 1.924A3.403 3.403 0 0118.117.402zm-1.516 23.156c-1.393 0-2.436 1.047-2.436 2.445 0 1.39 1.045 2.434 2.436 2.434s2.426-1.044 2.426-2.434c0-1.398-1.032-2.445-2.426-2.445zm2.02-14.565h-4.05v12.186h4.05V8.993z"
            fill="#F0B00B"
            fill-rule="nonzero"
          />
        </svg>
      </span>
      <span
        class="emotion-3"
      />
    </div>
  </div>
</DocumentFragment>
`;

exports[`<Notification /> notification snapshot tests for inverse styles renders warning state correctly Athleta in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  transition-property: opacity;
  transition-duration: 250ms;
  transition-timing-function: cubic-bezier(0.25, 0, 0.25, 1);
  transition-delay: 0ms;
  opacity: 1;
}

.emotion-1 {
  line-height: 1.25;
  padding: 0.5rem;
  margin-bottom: 1.125rem;
  border-style: solid;
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 0.875rem;
  color: #FFFFFF;
  border-color: #FFC83A;
  border-width: 2px 2px 2px 8px;
}

.emotion-2 {
  display: inline-block;
  height: 20px;
  width: 20px;
  min-height: 20px;
  min-width: 20px;
}

.emotion-2 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-2 svg path {
  fill: #FFC83A;
}

.emotion-2 svg rect {
  fill: #FFC83A;
}

.emotion-3 {
  padding-left: 0.75rem;
}

<div
    class="emotion-0"
  >
    <div
      aria-live="polite"
      class="emotion-1"
      kind="warning"
    >
      <span
        aria-hidden="true"
        class="emotion-2"
      >
        <svg
          viewBox="0 0 33 33"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M18.117.402c.659.33 1.193.864 1.522 1.522l12.597 25.194a3.403 3.403 0 01-3.044 4.925H4a3.403 3.403 0 01-3.044-4.925L13.552 1.924A3.403 3.403 0 0118.117.402zm-1.516 23.156c-1.393 0-2.436 1.047-2.436 2.445 0 1.39 1.045 2.434 2.436 2.434s2.426-1.044 2.426-2.434c0-1.398-1.032-2.445-2.426-2.445zm2.02-14.565h-4.05v12.186h4.05V8.993z"
            fill="#F0B00B"
            fill-rule="nonzero"
          />
        </svg>
      </span>
      <span
        class="emotion-3"
      />
    </div>
  </div>
</DocumentFragment>
`;

exports[`<Notification /> notification snapshot tests for inverse styles renders warning state correctly BananaRepublic 1`] = `
<DocumentFragment>
  .emotion-0 {
  transition-property: opacity;
  transition-duration: 250ms;
  transition-timing-function: cubic-bezier(0.25, 0, 0.25, 1);
  transition-delay: 0ms;
  opacity: 1;
}

.emotion-1 {
  line-height: 1.25;
  padding: 0.5rem;
  margin-bottom: 1.125rem;
  border-style: solid;
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  font-family: 'Lynstone',Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 0.8125rem;
  color: #F6F4EB;
  border-color: #FFC83A;
  border-width: 2px 2px 2px 8px;
}

.emotion-2 {
  display: inline-block;
  height: 20px;
  width: 20px;
  min-height: 20px;
  min-width: 20px;
}

.emotion-2 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-2 svg path {
  fill: #FFC83A;
}

.emotion-2 svg rect {
  fill: #FFC83A;
}

.emotion-3 {
  padding-left: 0.75rem;
}

<div
    class="emotion-0"
  >
    <div
      aria-live="polite"
      class="emotion-1"
      kind="warning"
    >
      <span
        aria-hidden="true"
        class="emotion-2"
      >
        <svg
          viewBox="0 0 33 33"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M18.117.402c.659.33 1.193.864 1.522 1.522l12.597 25.194a3.403 3.403 0 01-3.044 4.925H4a3.403 3.403 0 01-3.044-4.925L13.552 1.924A3.403 3.403 0 0118.117.402zm-1.516 23.156c-1.393 0-2.436 1.047-2.436 2.445 0 1.39 1.045 2.434 2.436 2.434s2.426-1.044 2.426-2.434c0-1.398-1.032-2.445-2.426-2.445zm2.02-14.565h-4.05v12.186h4.05V8.993z"
            fill="#F0B00B"
            fill-rule="nonzero"
          />
        </svg>
      </span>
      <span
        class="emotion-3"
      />
    </div>
  </div>
</DocumentFragment>
`;

exports[`<Notification /> notification snapshot tests for inverse styles renders warning state correctly BananaRepublic in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  transition-property: opacity;
  transition-duration: 250ms;
  transition-timing-function: cubic-bezier(0.25, 0, 0.25, 1);
  transition-delay: 0ms;
  opacity: 1;
}

.emotion-1 {
  line-height: 1.25;
  padding: 0.5rem;
  margin-bottom: 1.125rem;
  border-style: solid;
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 0.875rem;
  color: #F6F4EB;
  border-color: #FFC83A;
  border-width: 2px 2px 2px 8px;
}

.emotion-2 {
  display: inline-block;
  height: 20px;
  width: 20px;
  min-height: 20px;
  min-width: 20px;
}

.emotion-2 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-2 svg path {
  fill: #FFC83A;
}

.emotion-2 svg rect {
  fill: #FFC83A;
}

.emotion-3 {
  padding-left: 0.75rem;
}

<div
    class="emotion-0"
  >
    <div
      aria-live="polite"
      class="emotion-1"
      kind="warning"
    >
      <span
        aria-hidden="true"
        class="emotion-2"
      >
        <svg
          viewBox="0 0 33 33"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M18.117.402c.659.33 1.193.864 1.522 1.522l12.597 25.194a3.403 3.403 0 01-3.044 4.925H4a3.403 3.403 0 01-3.044-4.925L13.552 1.924A3.403 3.403 0 0118.117.402zm-1.516 23.156c-1.393 0-2.436 1.047-2.436 2.445 0 1.39 1.045 2.434 2.436 2.434s2.426-1.044 2.426-2.434c0-1.398-1.032-2.445-2.426-2.445zm2.02-14.565h-4.05v12.186h4.05V8.993z"
            fill="#F0B00B"
            fill-rule="nonzero"
          />
        </svg>
      </span>
      <span
        class="emotion-3"
      />
    </div>
  </div>
</DocumentFragment>
`;

exports[`<Notification /> notification snapshot tests for inverse styles renders warning state correctly BananaRepublicFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  transition-property: opacity;
  transition-duration: 250ms;
  transition-timing-function: cubic-bezier(0.25, 0, 0.25, 1);
  transition-delay: 0ms;
  opacity: 1;
}

.emotion-1 {
  line-height: 1.25;
  padding: 0.5rem;
  margin-bottom: 1.125rem;
  border-style: solid;
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  font-family: 'Lynstone',Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 0.8125rem;
  color: #F6F4EB;
  border-color: #FFC83A;
  border-width: 2px 2px 2px 8px;
}

.emotion-2 {
  display: inline-block;
  height: 20px;
  width: 20px;
  min-height: 20px;
  min-width: 20px;
}

.emotion-2 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-2 svg path {
  fill: #FFC83A;
}

.emotion-2 svg rect {
  fill: #FFC83A;
}

.emotion-3 {
  padding-left: 0.75rem;
}

<div
    class="emotion-0"
  >
    <div
      aria-live="polite"
      class="emotion-1"
      kind="warning"
    >
      <span
        aria-hidden="true"
        class="emotion-2"
      >
        <svg
          viewBox="0 0 33 33"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M18.117.402c.659.33 1.193.864 1.522 1.522l12.597 25.194a3.403 3.403 0 01-3.044 4.925H4a3.403 3.403 0 01-3.044-4.925L13.552 1.924A3.403 3.403 0 0118.117.402zm-1.516 23.156c-1.393 0-2.436 1.047-2.436 2.445 0 1.39 1.045 2.434 2.436 2.434s2.426-1.044 2.426-2.434c0-1.398-1.032-2.445-2.426-2.445zm2.02-14.565h-4.05v12.186h4.05V8.993z"
            fill="#F0B00B"
            fill-rule="nonzero"
          />
        </svg>
      </span>
      <span
        class="emotion-3"
      />
    </div>
  </div>
</DocumentFragment>
`;

exports[`<Notification /> notification snapshot tests for inverse styles renders warning state correctly BananaRepublicFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  transition-property: opacity;
  transition-duration: 250ms;
  transition-timing-function: cubic-bezier(0.25, 0, 0.25, 1);
  transition-delay: 0ms;
  opacity: 1;
}

.emotion-1 {
  line-height: 1.25;
  padding: 0.5rem;
  margin-bottom: 1.125rem;
  border-style: solid;
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 0.875rem;
  color: #F6F4EB;
  border-color: #FFC83A;
  border-width: 2px 2px 2px 8px;
}

.emotion-2 {
  display: inline-block;
  height: 20px;
  width: 20px;
  min-height: 20px;
  min-width: 20px;
}

.emotion-2 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-2 svg path {
  fill: #FFC83A;
}

.emotion-2 svg rect {
  fill: #FFC83A;
}

.emotion-3 {
  padding-left: 0.75rem;
}

<div
    class="emotion-0"
  >
    <div
      aria-live="polite"
      class="emotion-1"
      kind="warning"
    >
      <span
        aria-hidden="true"
        class="emotion-2"
      >
        <svg
          viewBox="0 0 33 33"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M18.117.402c.659.33 1.193.864 1.522 1.522l12.597 25.194a3.403 3.403 0 01-3.044 4.925H4a3.403 3.403 0 01-3.044-4.925L13.552 1.924A3.403 3.403 0 0118.117.402zm-1.516 23.156c-1.393 0-2.436 1.047-2.436 2.445 0 1.39 1.045 2.434 2.436 2.434s2.426-1.044 2.426-2.434c0-1.398-1.032-2.445-2.426-2.445zm2.02-14.565h-4.05v12.186h4.05V8.993z"
            fill="#F0B00B"
            fill-rule="nonzero"
          />
        </svg>
      </span>
      <span
        class="emotion-3"
      />
    </div>
  </div>
</DocumentFragment>
`;

exports[`<Notification /> notification snapshot tests for inverse styles renders warning state correctly Gap 1`] = `
<DocumentFragment>
  .emotion-0 {
  transition-property: opacity;
  transition-duration: 250ms;
  transition-timing-function: cubic-bezier(0.25, 0, 0.25, 1);
  transition-delay: 0ms;
  opacity: 1;
}

.emotion-1 {
  line-height: 1.25;
  padding: 0.5rem;
  margin-bottom: 1.125rem;
  border-style: solid;
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 0.8125rem;
  color: #FFFFFF;
  border-color: #FFC83A;
  border-width: 2px 2px 2px 8px;
}

.emotion-2 {
  display: inline-block;
  height: 20px;
  width: 20px;
  min-height: 20px;
  min-width: 20px;
}

.emotion-2 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-2 svg path {
  fill: #FFC83A;
}

.emotion-2 svg rect {
  fill: #FFC83A;
}

.emotion-3 {
  padding-left: 0.75rem;
}

<div
    class="emotion-0"
  >
    <div
      aria-live="polite"
      class="emotion-1"
      kind="warning"
    >
      <span
        aria-hidden="true"
        class="emotion-2"
      >
        <svg
          viewBox="0 0 33 33"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M18.117.402c.659.33 1.193.864 1.522 1.522l12.597 25.194a3.403 3.403 0 01-3.044 4.925H4a3.403 3.403 0 01-3.044-4.925L13.552 1.924A3.403 3.403 0 0118.117.402zm-1.516 23.156c-1.393 0-2.436 1.047-2.436 2.445 0 1.39 1.045 2.434 2.436 2.434s2.426-1.044 2.426-2.434c0-1.398-1.032-2.445-2.426-2.445zm2.02-14.565h-4.05v12.186h4.05V8.993z"
            fill="#F0B00B"
            fill-rule="nonzero"
          />
        </svg>
      </span>
      <span
        class="emotion-3"
      />
    </div>
  </div>
</DocumentFragment>
`;

exports[`<Notification /> notification snapshot tests for inverse styles renders warning state correctly Gap in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  transition-property: opacity;
  transition-duration: 250ms;
  transition-timing-function: cubic-bezier(0.25, 0, 0.25, 1);
  transition-delay: 0ms;
  opacity: 1;
}

.emotion-1 {
  line-height: 1.25;
  padding: 0.5rem;
  margin-bottom: 1.125rem;
  border-style: solid;
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 0.875rem;
  color: #FFFFFF;
  border-color: #FFC83A;
  border-width: 2px 2px 2px 8px;
}

.emotion-2 {
  display: inline-block;
  height: 20px;
  width: 20px;
  min-height: 20px;
  min-width: 20px;
}

.emotion-2 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-2 svg path {
  fill: #FFC83A;
}

.emotion-2 svg rect {
  fill: #FFC83A;
}

.emotion-3 {
  padding-left: 0.75rem;
}

<div
    class="emotion-0"
  >
    <div
      aria-live="polite"
      class="emotion-1"
      kind="warning"
    >
      <span
        aria-hidden="true"
        class="emotion-2"
      >
        <svg
          viewBox="0 0 33 33"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M18.117.402c.659.33 1.193.864 1.522 1.522l12.597 25.194a3.403 3.403 0 01-3.044 4.925H4a3.403 3.403 0 01-3.044-4.925L13.552 1.924A3.403 3.403 0 0118.117.402zm-1.516 23.156c-1.393 0-2.436 1.047-2.436 2.445 0 1.39 1.045 2.434 2.436 2.434s2.426-1.044 2.426-2.434c0-1.398-1.032-2.445-2.426-2.445zm2.02-14.565h-4.05v12.186h4.05V8.993z"
            fill="#F0B00B"
            fill-rule="nonzero"
          />
        </svg>
      </span>
      <span
        class="emotion-3"
      />
    </div>
  </div>
</DocumentFragment>
`;

exports[`<Notification /> notification snapshot tests for inverse styles renders warning state correctly GapFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  transition-property: opacity;
  transition-duration: 250ms;
  transition-timing-function: cubic-bezier(0.25, 0, 0.25, 1);
  transition-delay: 0ms;
  opacity: 1;
}

.emotion-1 {
  line-height: 1.25;
  padding: 0.5rem;
  margin-bottom: 1.125rem;
  border-style: solid;
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 0.8125rem;
  color: #FFFFFF;
  border-color: #FFC83A;
  border-width: 2px 2px 2px 8px;
}

.emotion-2 {
  display: inline-block;
  height: 20px;
  width: 20px;
  min-height: 20px;
  min-width: 20px;
}

.emotion-2 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-2 svg path {
  fill: #FFC83A;
}

.emotion-2 svg rect {
  fill: #FFC83A;
}

.emotion-3 {
  padding-left: 0.75rem;
}

<div
    class="emotion-0"
  >
    <div
      aria-live="polite"
      class="emotion-1"
      kind="warning"
    >
      <span
        aria-hidden="true"
        class="emotion-2"
      >
        <svg
          viewBox="0 0 33 33"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M18.117.402c.659.33 1.193.864 1.522 1.522l12.597 25.194a3.403 3.403 0 01-3.044 4.925H4a3.403 3.403 0 01-3.044-4.925L13.552 1.924A3.403 3.403 0 0118.117.402zm-1.516 23.156c-1.393 0-2.436 1.047-2.436 2.445 0 1.39 1.045 2.434 2.436 2.434s2.426-1.044 2.426-2.434c0-1.398-1.032-2.445-2.426-2.445zm2.02-14.565h-4.05v12.186h4.05V8.993z"
            fill="#F0B00B"
            fill-rule="nonzero"
          />
        </svg>
      </span>
      <span
        class="emotion-3"
      />
    </div>
  </div>
</DocumentFragment>
`;

exports[`<Notification /> notification snapshot tests for inverse styles renders warning state correctly GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  transition-property: opacity;
  transition-duration: 250ms;
  transition-timing-function: cubic-bezier(0.25, 0, 0.25, 1);
  transition-delay: 0ms;
  opacity: 1;
}

.emotion-1 {
  line-height: 1.25;
  padding: 0.5rem;
  margin-bottom: 1.125rem;
  border-style: solid;
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 0.875rem;
  color: #FFFFFF;
  border-color: #FFC83A;
  border-width: 2px 2px 2px 8px;
}

.emotion-2 {
  display: inline-block;
  height: 20px;
  width: 20px;
  min-height: 20px;
  min-width: 20px;
}

.emotion-2 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-2 svg path {
  fill: #FFC83A;
}

.emotion-2 svg rect {
  fill: #FFC83A;
}

.emotion-3 {
  padding-left: 0.75rem;
}

<div
    class="emotion-0"
  >
    <div
      aria-live="polite"
      class="emotion-1"
      kind="warning"
    >
      <span
        aria-hidden="true"
        class="emotion-2"
      >
        <svg
          viewBox="0 0 33 33"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M18.117.402c.659.33 1.193.864 1.522 1.522l12.597 25.194a3.403 3.403 0 01-3.044 4.925H4a3.403 3.403 0 01-3.044-4.925L13.552 1.924A3.403 3.403 0 0118.117.402zm-1.516 23.156c-1.393 0-2.436 1.047-2.436 2.445 0 1.39 1.045 2.434 2.436 2.434s2.426-1.044 2.426-2.434c0-1.398-1.032-2.445-2.426-2.445zm2.02-14.565h-4.05v12.186h4.05V8.993z"
            fill="#F0B00B"
            fill-rule="nonzero"
          />
        </svg>
      </span>
      <span
        class="emotion-3"
      />
    </div>
  </div>
</DocumentFragment>
`;

exports[`<Notification /> notification snapshot tests for inverse styles renders warning state correctly OldNavy 1`] = `
<DocumentFragment>
  .emotion-0 {
  transition-property: opacity;
  transition-duration: 250ms;
  transition-timing-function: cubic-bezier(0.25, 0, 0.25, 1);
  transition-delay: 0ms;
  opacity: 1;
}

.emotion-1 {
  line-height: 1.25;
  padding: 0.5rem;
  margin-bottom: 1.125rem;
  border-style: solid;
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  font-family: 'Gotham','Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 0.875rem;
  color: #FFFFFF;
  border-color: #FFC83A;
  border-width: 2px 2px 2px 8px;
}

.emotion-2 {
  display: inline-block;
  height: 20px;
  width: 20px;
  min-height: 20px;
  min-width: 20px;
}

.emotion-2 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-2 svg path {
  fill: #FFC83A;
}

.emotion-2 svg rect {
  fill: #FFC83A;
}

.emotion-3 {
  padding-left: 0.75rem;
}

<div
    class="emotion-0"
  >
    <div
      aria-live="polite"
      class="emotion-1"
      kind="warning"
    >
      <span
        aria-hidden="true"
        class="emotion-2"
      >
        <svg
          viewBox="0 0 33 33"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M18.117.402c.659.33 1.193.864 1.522 1.522l12.597 25.194a3.403 3.403 0 01-3.044 4.925H4a3.403 3.403 0 01-3.044-4.925L13.552 1.924A3.403 3.403 0 0118.117.402zm-1.516 23.156c-1.393 0-2.436 1.047-2.436 2.445 0 1.39 1.045 2.434 2.436 2.434s2.426-1.044 2.426-2.434c0-1.398-1.032-2.445-2.426-2.445zm2.02-14.565h-4.05v12.186h4.05V8.993z"
            fill="#F0B00B"
            fill-rule="nonzero"
          />
        </svg>
      </span>
      <span
        class="emotion-3"
      />
    </div>
  </div>
</DocumentFragment>
`;

exports[`<Notification /> notification snapshot tests for inverse styles renders warning state correctly OldNavy in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  transition-property: opacity;
  transition-duration: 250ms;
  transition-timing-function: cubic-bezier(0.25, 0, 0.25, 1);
  transition-delay: 0ms;
  opacity: 1;
}

.emotion-1 {
  line-height: 1.25;
  padding: 0.5rem;
  margin-bottom: 1.125rem;
  border-style: solid;
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 0.875rem;
  color: #FFFFFF;
  border-color: #FFC83A;
  border-width: 2px 2px 2px 8px;
}

.emotion-2 {
  display: inline-block;
  height: 20px;
  width: 20px;
  min-height: 20px;
  min-width: 20px;
}

.emotion-2 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-2 svg path {
  fill: #FFC83A;
}

.emotion-2 svg rect {
  fill: #FFC83A;
}

.emotion-3 {
  padding-left: 0.75rem;
}

<div
    class="emotion-0"
  >
    <div
      aria-live="polite"
      class="emotion-1"
      kind="warning"
    >
      <span
        aria-hidden="true"
        class="emotion-2"
      >
        <svg
          viewBox="0 0 33 33"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M18.117.402c.659.33 1.193.864 1.522 1.522l12.597 25.194a3.403 3.403 0 01-3.044 4.925H4a3.403 3.403 0 01-3.044-4.925L13.552 1.924A3.403 3.403 0 0118.117.402zm-1.516 23.156c-1.393 0-2.436 1.047-2.436 2.445 0 1.39 1.045 2.434 2.436 2.434s2.426-1.044 2.426-2.434c0-1.398-1.032-2.445-2.426-2.445zm2.02-14.565h-4.05v12.186h4.05V8.993z"
            fill="#F0B00B"
            fill-rule="nonzero"
          />
        </svg>
      </span>
      <span
        class="emotion-3"
      />
    </div>
  </div>
</DocumentFragment>
`;

exports[`<Notification /> notification snapshot tests renders custom border styles state correctly Athleta 1`] = `
<DocumentFragment>
  .emotion-0 {
  transition-property: opacity;
  transition-duration: 250ms;
  transition-timing-function: cubic-bezier(0.25, 0, 0.25, 1);
  transition-delay: 0ms;
  opacity: 1;
}

.emotion-1 {
  line-height: 1.25;
  padding: 0.5rem;
  margin-bottom: 1.125rem;
  border-style: solid;
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  font-family: 'Avenir Next','Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 0.875rem;
  border-color: red;
  border-width: 1px 1px 1px 8px;
}

.emotion-2 {
  display: inline-block;
  height: 20px;
  width: 20px;
  min-height: 20px;
  min-width: 20px;
}

.emotion-2 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-3 {
  padding-left: 0.75rem;
}

<div
    class="emotion-0"
  >
    <div
      aria-live="polite"
      class="emotion-1"
      kind="information"
    >
      <span
        aria-hidden="true"
        class="emotion-2"
      >
        <svg
          viewBox="0 0 33 33"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M16.961 32.338c-8.836 0-16-7.163-16-16 0-8.836 7.164-16 16-16 8.837 0 16 7.164 16 16 0 8.837-7.163 16-16 16zm.19-20.969c1.321 0 2.31-.993 2.31-2.32 0-1.32-.991-2.31-2.31-2.31-1.32 0-2.3.99-2.3 2.31 0 1.327.978 2.32 2.3 2.32zm-1.915 13.827h3.84V13.628h-3.84v11.568z"
            fill="#5CABF7"
            fill-rule="evenodd"
          />
        </svg>
      </span>
      <span
        class="emotion-3"
      />
    </div>
  </div>
</DocumentFragment>
`;

exports[`<Notification /> notification snapshot tests renders custom border styles state correctly Athleta in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  transition-property: opacity;
  transition-duration: 250ms;
  transition-timing-function: cubic-bezier(0.25, 0, 0.25, 1);
  transition-delay: 0ms;
  opacity: 1;
}

.emotion-1 {
  line-height: 1.25;
  padding: 0.5rem;
  margin-bottom: 1.125rem;
  border-style: solid;
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 0.875rem;
  border-color: red;
  border-width: 1px 1px 1px 8px;
}

.emotion-2 {
  display: inline-block;
  height: 20px;
  width: 20px;
  min-height: 20px;
  min-width: 20px;
}

.emotion-2 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-3 {
  padding-left: 0.75rem;
}

<div
    class="emotion-0"
  >
    <div
      aria-live="polite"
      class="emotion-1"
      kind="information"
    >
      <span
        aria-hidden="true"
        class="emotion-2"
      >
        <svg
          viewBox="0 0 33 33"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M16.961 32.338c-8.836 0-16-7.163-16-16 0-8.836 7.164-16 16-16 8.837 0 16 7.164 16 16 0 8.837-7.163 16-16 16zm.19-20.969c1.321 0 2.31-.993 2.31-2.32 0-1.32-.991-2.31-2.31-2.31-1.32 0-2.3.99-2.3 2.31 0 1.327.978 2.32 2.3 2.32zm-1.915 13.827h3.84V13.628h-3.84v11.568z"
            fill="#5CABF7"
            fill-rule="evenodd"
          />
        </svg>
      </span>
      <span
        class="emotion-3"
      />
    </div>
  </div>
</DocumentFragment>
`;

exports[`<Notification /> notification snapshot tests renders custom border styles state correctly BananaRepublic 1`] = `
<DocumentFragment>
  .emotion-0 {
  transition-property: opacity;
  transition-duration: 250ms;
  transition-timing-function: cubic-bezier(0.25, 0, 0.25, 1);
  transition-delay: 0ms;
  opacity: 1;
}

.emotion-1 {
  line-height: 1.25;
  padding: 0.5rem;
  margin-bottom: 1.125rem;
  border-style: solid;
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  font-family: 'Lynstone',Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 0.8125rem;
  border-color: red;
  border-width: 1px 1px 1px 8px;
}

.emotion-2 {
  display: inline-block;
  height: 20px;
  width: 20px;
  min-height: 20px;
  min-width: 20px;
}

.emotion-2 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-3 {
  padding-left: 0.75rem;
}

<div
    class="emotion-0"
  >
    <div
      aria-live="polite"
      class="emotion-1"
      kind="information"
    >
      <span
        aria-hidden="true"
        class="emotion-2"
      >
        <svg
          viewBox="0 0 33 33"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M16.961 32.338c-8.836 0-16-7.163-16-16 0-8.836 7.164-16 16-16 8.837 0 16 7.164 16 16 0 8.837-7.163 16-16 16zm.19-20.969c1.321 0 2.31-.993 2.31-2.32 0-1.32-.991-2.31-2.31-2.31-1.32 0-2.3.99-2.3 2.31 0 1.327.978 2.32 2.3 2.32zm-1.915 13.827h3.84V13.628h-3.84v11.568z"
            fill="#5CABF7"
            fill-rule="evenodd"
          />
        </svg>
      </span>
      <span
        class="emotion-3"
      />
    </div>
  </div>
</DocumentFragment>
`;

exports[`<Notification /> notification snapshot tests renders custom border styles state correctly BananaRepublic in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  transition-property: opacity;
  transition-duration: 250ms;
  transition-timing-function: cubic-bezier(0.25, 0, 0.25, 1);
  transition-delay: 0ms;
  opacity: 1;
}

.emotion-1 {
  line-height: 1.25;
  padding: 0.5rem;
  margin-bottom: 1.125rem;
  border-style: solid;
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 0.875rem;
  border-color: red;
  border-width: 1px 1px 1px 8px;
}

.emotion-2 {
  display: inline-block;
  height: 20px;
  width: 20px;
  min-height: 20px;
  min-width: 20px;
}

.emotion-2 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-3 {
  padding-left: 0.75rem;
}

<div
    class="emotion-0"
  >
    <div
      aria-live="polite"
      class="emotion-1"
      kind="information"
    >
      <span
        aria-hidden="true"
        class="emotion-2"
      >
        <svg
          viewBox="0 0 33 33"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M16.961 32.338c-8.836 0-16-7.163-16-16 0-8.836 7.164-16 16-16 8.837 0 16 7.164 16 16 0 8.837-7.163 16-16 16zm.19-20.969c1.321 0 2.31-.993 2.31-2.32 0-1.32-.991-2.31-2.31-2.31-1.32 0-2.3.99-2.3 2.31 0 1.327.978 2.32 2.3 2.32zm-1.915 13.827h3.84V13.628h-3.84v11.568z"
            fill="#5CABF7"
            fill-rule="evenodd"
          />
        </svg>
      </span>
      <span
        class="emotion-3"
      />
    </div>
  </div>
</DocumentFragment>
`;

exports[`<Notification /> notification snapshot tests renders custom border styles state correctly BananaRepublicFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  transition-property: opacity;
  transition-duration: 250ms;
  transition-timing-function: cubic-bezier(0.25, 0, 0.25, 1);
  transition-delay: 0ms;
  opacity: 1;
}

.emotion-1 {
  line-height: 1.25;
  padding: 0.5rem;
  margin-bottom: 1.125rem;
  border-style: solid;
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  font-family: 'Lynstone',Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 0.8125rem;
  border-color: red;
  border-width: 1px 1px 1px 8px;
}

.emotion-2 {
  display: inline-block;
  height: 20px;
  width: 20px;
  min-height: 20px;
  min-width: 20px;
}

.emotion-2 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-3 {
  padding-left: 0.75rem;
}

<div
    class="emotion-0"
  >
    <div
      aria-live="polite"
      class="emotion-1"
      kind="information"
    >
      <span
        aria-hidden="true"
        class="emotion-2"
      >
        <svg
          viewBox="0 0 33 33"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M16.961 32.338c-8.836 0-16-7.163-16-16 0-8.836 7.164-16 16-16 8.837 0 16 7.164 16 16 0 8.837-7.163 16-16 16zm.19-20.969c1.321 0 2.31-.993 2.31-2.32 0-1.32-.991-2.31-2.31-2.31-1.32 0-2.3.99-2.3 2.31 0 1.327.978 2.32 2.3 2.32zm-1.915 13.827h3.84V13.628h-3.84v11.568z"
            fill="#5CABF7"
            fill-rule="evenodd"
          />
        </svg>
      </span>
      <span
        class="emotion-3"
      />
    </div>
  </div>
</DocumentFragment>
`;

exports[`<Notification /> notification snapshot tests renders custom border styles state correctly BananaRepublicFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  transition-property: opacity;
  transition-duration: 250ms;
  transition-timing-function: cubic-bezier(0.25, 0, 0.25, 1);
  transition-delay: 0ms;
  opacity: 1;
}

.emotion-1 {
  line-height: 1.25;
  padding: 0.5rem;
  margin-bottom: 1.125rem;
  border-style: solid;
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 0.875rem;
  border-color: red;
  border-width: 1px 1px 1px 8px;
}

.emotion-2 {
  display: inline-block;
  height: 20px;
  width: 20px;
  min-height: 20px;
  min-width: 20px;
}

.emotion-2 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-3 {
  padding-left: 0.75rem;
}

<div
    class="emotion-0"
  >
    <div
      aria-live="polite"
      class="emotion-1"
      kind="information"
    >
      <span
        aria-hidden="true"
        class="emotion-2"
      >
        <svg
          viewBox="0 0 33 33"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M16.961 32.338c-8.836 0-16-7.163-16-16 0-8.836 7.164-16 16-16 8.837 0 16 7.164 16 16 0 8.837-7.163 16-16 16zm.19-20.969c1.321 0 2.31-.993 2.31-2.32 0-1.32-.991-2.31-2.31-2.31-1.32 0-2.3.99-2.3 2.31 0 1.327.978 2.32 2.3 2.32zm-1.915 13.827h3.84V13.628h-3.84v11.568z"
            fill="#5CABF7"
            fill-rule="evenodd"
          />
        </svg>
      </span>
      <span
        class="emotion-3"
      />
    </div>
  </div>
</DocumentFragment>
`;

exports[`<Notification /> notification snapshot tests renders custom border styles state correctly Gap 1`] = `
<DocumentFragment>
  .emotion-0 {
  transition-property: opacity;
  transition-duration: 250ms;
  transition-timing-function: cubic-bezier(0.25, 0, 0.25, 1);
  transition-delay: 0ms;
  opacity: 1;
}

.emotion-1 {
  line-height: 1.25;
  padding: 0.5rem;
  margin-bottom: 1.125rem;
  border-style: solid;
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 0.8125rem;
  border-color: red;
  border-width: 1px 1px 1px 8px;
}

.emotion-2 {
  display: inline-block;
  height: 20px;
  width: 20px;
  min-height: 20px;
  min-width: 20px;
}

.emotion-2 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-3 {
  padding-left: 0.75rem;
}

<div
    class="emotion-0"
  >
    <div
      aria-live="polite"
      class="emotion-1"
      kind="information"
    >
      <span
        aria-hidden="true"
        class="emotion-2"
      >
        <svg
          viewBox="0 0 33 33"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M16.961 32.338c-8.836 0-16-7.163-16-16 0-8.836 7.164-16 16-16 8.837 0 16 7.164 16 16 0 8.837-7.163 16-16 16zm.19-20.969c1.321 0 2.31-.993 2.31-2.32 0-1.32-.991-2.31-2.31-2.31-1.32 0-2.3.99-2.3 2.31 0 1.327.978 2.32 2.3 2.32zm-1.915 13.827h3.84V13.628h-3.84v11.568z"
            fill="#5CABF7"
            fill-rule="evenodd"
          />
        </svg>
      </span>
      <span
        class="emotion-3"
      />
    </div>
  </div>
</DocumentFragment>
`;

exports[`<Notification /> notification snapshot tests renders custom border styles state correctly Gap in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  transition-property: opacity;
  transition-duration: 250ms;
  transition-timing-function: cubic-bezier(0.25, 0, 0.25, 1);
  transition-delay: 0ms;
  opacity: 1;
}

.emotion-1 {
  line-height: 1.25;
  padding: 0.5rem;
  margin-bottom: 1.125rem;
  border-style: solid;
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 0.875rem;
  border-color: red;
  border-width: 1px 1px 1px 8px;
}

.emotion-2 {
  display: inline-block;
  height: 20px;
  width: 20px;
  min-height: 20px;
  min-width: 20px;
}

.emotion-2 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-3 {
  padding-left: 0.75rem;
}

<div
    class="emotion-0"
  >
    <div
      aria-live="polite"
      class="emotion-1"
      kind="information"
    >
      <span
        aria-hidden="true"
        class="emotion-2"
      >
        <svg
          viewBox="0 0 33 33"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M16.961 32.338c-8.836 0-16-7.163-16-16 0-8.836 7.164-16 16-16 8.837 0 16 7.164 16 16 0 8.837-7.163 16-16 16zm.19-20.969c1.321 0 2.31-.993 2.31-2.32 0-1.32-.991-2.31-2.31-2.31-1.32 0-2.3.99-2.3 2.31 0 1.327.978 2.32 2.3 2.32zm-1.915 13.827h3.84V13.628h-3.84v11.568z"
            fill="#5CABF7"
            fill-rule="evenodd"
          />
        </svg>
      </span>
      <span
        class="emotion-3"
      />
    </div>
  </div>
</DocumentFragment>
`;

exports[`<Notification /> notification snapshot tests renders custom border styles state correctly GapFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  transition-property: opacity;
  transition-duration: 250ms;
  transition-timing-function: cubic-bezier(0.25, 0, 0.25, 1);
  transition-delay: 0ms;
  opacity: 1;
}

.emotion-1 {
  line-height: 1.25;
  padding: 0.5rem;
  margin-bottom: 1.125rem;
  border-style: solid;
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 0.8125rem;
  border-color: red;
  border-width: 1px 1px 1px 8px;
}

.emotion-2 {
  display: inline-block;
  height: 20px;
  width: 20px;
  min-height: 20px;
  min-width: 20px;
}

.emotion-2 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-3 {
  padding-left: 0.75rem;
}

<div
    class="emotion-0"
  >
    <div
      aria-live="polite"
      class="emotion-1"
      kind="information"
    >
      <span
        aria-hidden="true"
        class="emotion-2"
      >
        <svg
          viewBox="0 0 33 33"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M16.961 32.338c-8.836 0-16-7.163-16-16 0-8.836 7.164-16 16-16 8.837 0 16 7.164 16 16 0 8.837-7.163 16-16 16zm.19-20.969c1.321 0 2.31-.993 2.31-2.32 0-1.32-.991-2.31-2.31-2.31-1.32 0-2.3.99-2.3 2.31 0 1.327.978 2.32 2.3 2.32zm-1.915 13.827h3.84V13.628h-3.84v11.568z"
            fill="#5CABF7"
            fill-rule="evenodd"
          />
        </svg>
      </span>
      <span
        class="emotion-3"
      />
    </div>
  </div>
</DocumentFragment>
`;

exports[`<Notification /> notification snapshot tests renders custom border styles state correctly GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  transition-property: opacity;
  transition-duration: 250ms;
  transition-timing-function: cubic-bezier(0.25, 0, 0.25, 1);
  transition-delay: 0ms;
  opacity: 1;
}

.emotion-1 {
  line-height: 1.25;
  padding: 0.5rem;
  margin-bottom: 1.125rem;
  border-style: solid;
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 0.875rem;
  border-color: red;
  border-width: 1px 1px 1px 8px;
}

.emotion-2 {
  display: inline-block;
  height: 20px;
  width: 20px;
  min-height: 20px;
  min-width: 20px;
}

.emotion-2 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-3 {
  padding-left: 0.75rem;
}

<div
    class="emotion-0"
  >
    <div
      aria-live="polite"
      class="emotion-1"
      kind="information"
    >
      <span
        aria-hidden="true"
        class="emotion-2"
      >
        <svg
          viewBox="0 0 33 33"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M16.961 32.338c-8.836 0-16-7.163-16-16 0-8.836 7.164-16 16-16 8.837 0 16 7.164 16 16 0 8.837-7.163 16-16 16zm.19-20.969c1.321 0 2.31-.993 2.31-2.32 0-1.32-.991-2.31-2.31-2.31-1.32 0-2.3.99-2.3 2.31 0 1.327.978 2.32 2.3 2.32zm-1.915 13.827h3.84V13.628h-3.84v11.568z"
            fill="#5CABF7"
            fill-rule="evenodd"
          />
        </svg>
      </span>
      <span
        class="emotion-3"
      />
    </div>
  </div>
</DocumentFragment>
`;

exports[`<Notification /> notification snapshot tests renders custom border styles state correctly OldNavy 1`] = `
<DocumentFragment>
  .emotion-0 {
  transition-property: opacity;
  transition-duration: 250ms;
  transition-timing-function: cubic-bezier(0.25, 0, 0.25, 1);
  transition-delay: 0ms;
  opacity: 1;
}

.emotion-1 {
  line-height: 1.25;
  padding: 0.5rem;
  margin-bottom: 1.125rem;
  border-style: solid;
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  font-family: 'Gotham','Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 0.875rem;
  border-color: red;
  border-width: 1px 1px 1px 8px;
}

.emotion-2 {
  display: inline-block;
  height: 20px;
  width: 20px;
  min-height: 20px;
  min-width: 20px;
}

.emotion-2 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-3 {
  padding-left: 0.75rem;
}

<div
    class="emotion-0"
  >
    <div
      aria-live="polite"
      class="emotion-1"
      kind="information"
    >
      <span
        aria-hidden="true"
        class="emotion-2"
      >
        <svg
          viewBox="0 0 33 33"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M16.961 32.338c-8.836 0-16-7.163-16-16 0-8.836 7.164-16 16-16 8.837 0 16 7.164 16 16 0 8.837-7.163 16-16 16zm.19-20.969c1.321 0 2.31-.993 2.31-2.32 0-1.32-.991-2.31-2.31-2.31-1.32 0-2.3.99-2.3 2.31 0 1.327.978 2.32 2.3 2.32zm-1.915 13.827h3.84V13.628h-3.84v11.568z"
            fill="#5CABF7"
            fill-rule="evenodd"
          />
        </svg>
      </span>
      <span
        class="emotion-3"
      />
    </div>
  </div>
</DocumentFragment>
`;

exports[`<Notification /> notification snapshot tests renders custom border styles state correctly OldNavy in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  transition-property: opacity;
  transition-duration: 250ms;
  transition-timing-function: cubic-bezier(0.25, 0, 0.25, 1);
  transition-delay: 0ms;
  opacity: 1;
}

.emotion-1 {
  line-height: 1.25;
  padding: 0.5rem;
  margin-bottom: 1.125rem;
  border-style: solid;
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 0.875rem;
  border-color: red;
  border-width: 1px 1px 1px 8px;
}

.emotion-2 {
  display: inline-block;
  height: 20px;
  width: 20px;
  min-height: 20px;
  min-width: 20px;
}

.emotion-2 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-3 {
  padding-left: 0.75rem;
}

<div
    class="emotion-0"
  >
    <div
      aria-live="polite"
      class="emotion-1"
      kind="information"
    >
      <span
        aria-hidden="true"
        class="emotion-2"
      >
        <svg
          viewBox="0 0 33 33"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M16.961 32.338c-8.836 0-16-7.163-16-16 0-8.836 7.164-16 16-16 8.837 0 16 7.164 16 16 0 8.837-7.163 16-16 16zm.19-20.969c1.321 0 2.31-.993 2.31-2.32 0-1.32-.991-2.31-2.31-2.31-1.32 0-2.3.99-2.3 2.31 0 1.327.978 2.32 2.3 2.32zm-1.915 13.827h3.84V13.628h-3.84v11.568z"
            fill="#5CABF7"
            fill-rule="evenodd"
          />
        </svg>
      </span>
      <span
        class="emotion-3"
      />
    </div>
  </div>
</DocumentFragment>
`;

exports[`<Notification /> notification snapshot tests renders dismissible state correctly Athleta 1`] = `
<DocumentFragment>
  .emotion-0 {
  transition-property: opacity;
  transition-duration: 250ms;
  transition-timing-function: cubic-bezier(0.25, 0, 0.25, 1);
  transition-delay: 0ms;
  opacity: 1;
}

.emotion-1 {
  line-height: 1.25;
  padding: 0.5rem;
  margin-bottom: 1.125rem;
  border-style: solid;
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  font-family: 'Avenir Next','Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 0.875rem;
  border-color: #5CABF7;
  border-width: 2px 2px 2px 8px;
}

.emotion-2 {
  display: inline-block;
  height: 20px;
  width: 20px;
  min-height: 20px;
  min-width: 20px;
}

.emotion-2 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-3 {
  padding-left: 0.75rem;
}

.emotion-4 {
  background: none;
  border: none;
  width: auto;
  margin: 0;
  padding: 0;
  text-align: inherit;
  font: inherit;
  padding-left: 0.65rem;
  margin-left: auto;
  opacity: 0.7;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-self: flex-start;
  -ms-flex-item-align: flex-start;
  align-self: flex-start;
}

.emotion-4:hover,
.emotion-4:focus {
  opacity: 1;
}

.emotion-5 {
  display: inline-block;
  height: 12px;
  width: 12px;
  min-height: 12px;
  min-width: 12px;
}

.emotion-5 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

<div
    class="emotion-0"
  >
    <div
      aria-live="polite"
      class="emotion-1"
      kind="information"
    >
      <span
        aria-hidden="true"
        class="emotion-2"
      >
        <svg
          viewBox="0 0 33 33"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M16.961 32.338c-8.836 0-16-7.163-16-16 0-8.836 7.164-16 16-16 8.837 0 16 7.164 16 16 0 8.837-7.163 16-16 16zm.19-20.969c1.321 0 2.31-.993 2.31-2.32 0-1.32-.991-2.31-2.31-2.31-1.32 0-2.3.99-2.3 2.31 0 1.327.978 2.32 2.3 2.32zm-1.915 13.827h3.84V13.628h-3.84v11.568z"
            fill="#5CABF7"
            fill-rule="evenodd"
          />
        </svg>
      </span>
      <span
        class="emotion-3"
      />
      <button
        aria-label="Close Notification"
        class="emotion-4"
        type="button"
      >
        <span
          aria-hidden="true"
          class="emotion-5"
        >
          <svg
            fill="none"
            viewBox="0 0 22 22"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              clip-rule="evenodd"
              d="M1.391 1.55603c-.585787.58579-.585786 1.53554 0 2.12133L8.71364 11 1.391 18.3226c-.585787.5858-.585786 1.5356 0 2.1214l.16503.165c.58579.5858 1.53554.5858 2.12133 0L11 13.2864l7.3226 7.3226c.5858.5858 1.5356.5858 2.1214 0l.165-.165c.5858-.5858.5858-1.5356 0-2.1214L13.2864 11l7.3226-7.32264c.5858-.58579.5858-1.53554 0-2.12133l-.165-.16503c-.5858-.585787-1.5356-.585786-2.1214 0L11 8.71364 3.67736 1.391c-.58579-.585787-1.53554-.585786-2.12133 0l-.16503.16503z"
              fill="#666"
              fill-rule="evenodd"
            />
          </svg>
        </span>
      </button>
    </div>
  </div>
</DocumentFragment>
`;

exports[`<Notification /> notification snapshot tests renders dismissible state correctly Athleta in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  transition-property: opacity;
  transition-duration: 250ms;
  transition-timing-function: cubic-bezier(0.25, 0, 0.25, 1);
  transition-delay: 0ms;
  opacity: 1;
}

.emotion-1 {
  line-height: 1.25;
  padding: 0.5rem;
  margin-bottom: 1.125rem;
  border-style: solid;
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 0.875rem;
  border-color: #5CABF7;
  border-width: 2px 2px 2px 8px;
}

.emotion-2 {
  display: inline-block;
  height: 20px;
  width: 20px;
  min-height: 20px;
  min-width: 20px;
}

.emotion-2 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-3 {
  padding-left: 0.75rem;
}

.emotion-4 {
  background: none;
  border: none;
  width: auto;
  margin: 0;
  padding: 0;
  text-align: inherit;
  font: inherit;
  padding-left: 0.65rem;
  margin-left: auto;
  opacity: 0.7;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-self: flex-start;
  -ms-flex-item-align: flex-start;
  align-self: flex-start;
}

.emotion-4:hover,
.emotion-4:focus {
  opacity: 1;
}

.emotion-5 {
  display: inline-block;
  height: 12px;
  width: 12px;
  min-height: 12px;
  min-width: 12px;
}

.emotion-5 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

<div
    class="emotion-0"
  >
    <div
      aria-live="polite"
      class="emotion-1"
      kind="information"
    >
      <span
        aria-hidden="true"
        class="emotion-2"
      >
        <svg
          viewBox="0 0 33 33"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M16.961 32.338c-8.836 0-16-7.163-16-16 0-8.836 7.164-16 16-16 8.837 0 16 7.164 16 16 0 8.837-7.163 16-16 16zm.19-20.969c1.321 0 2.31-.993 2.31-2.32 0-1.32-.991-2.31-2.31-2.31-1.32 0-2.3.99-2.3 2.31 0 1.327.978 2.32 2.3 2.32zm-1.915 13.827h3.84V13.628h-3.84v11.568z"
            fill="#5CABF7"
            fill-rule="evenodd"
          />
        </svg>
      </span>
      <span
        class="emotion-3"
      />
      <button
        aria-label="Close Notification"
        class="emotion-4"
        type="button"
      >
        <span
          aria-hidden="true"
          class="emotion-5"
        >
          <svg
            fill="none"
            viewBox="0 0 22 22"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              clip-rule="evenodd"
              d="M1.391 1.55603c-.585787.58579-.585786 1.53554 0 2.12133L8.71364 11 1.391 18.3226c-.585787.5858-.585786 1.5356 0 2.1214l.16503.165c.58579.5858 1.53554.5858 2.12133 0L11 13.2864l7.3226 7.3226c.5858.5858 1.5356.5858 2.1214 0l.165-.165c.5858-.5858.5858-1.5356 0-2.1214L13.2864 11l7.3226-7.32264c.5858-.58579.5858-1.53554 0-2.12133l-.165-.16503c-.5858-.585787-1.5356-.585786-2.1214 0L11 8.71364 3.67736 1.391c-.58579-.585787-1.53554-.585786-2.12133 0l-.16503.16503z"
              fill="#666"
              fill-rule="evenodd"
            />
          </svg>
        </span>
      </button>
    </div>
  </div>
</DocumentFragment>
`;

exports[`<Notification /> notification snapshot tests renders dismissible state correctly BananaRepublic 1`] = `
<DocumentFragment>
  .emotion-0 {
  transition-property: opacity;
  transition-duration: 250ms;
  transition-timing-function: cubic-bezier(0.25, 0, 0.25, 1);
  transition-delay: 0ms;
  opacity: 1;
}

.emotion-1 {
  line-height: 1.25;
  padding: 0.5rem;
  margin-bottom: 1.125rem;
  border-style: solid;
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  font-family: 'Lynstone',Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 0.8125rem;
  border-color: #5CABF7;
  border-width: 2px 2px 2px 8px;
}

.emotion-2 {
  display: inline-block;
  height: 20px;
  width: 20px;
  min-height: 20px;
  min-width: 20px;
}

.emotion-2 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-3 {
  padding-left: 0.75rem;
}

.emotion-4 {
  background: none;
  border: none;
  width: auto;
  margin: 0;
  padding: 0;
  text-align: inherit;
  font: inherit;
  padding-left: 0.65rem;
  margin-left: auto;
  opacity: 0.7;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-self: flex-start;
  -ms-flex-item-align: flex-start;
  align-self: flex-start;
}

.emotion-4:hover,
.emotion-4:focus {
  opacity: 1;
}

.emotion-5 {
  display: inline-block;
  height: 12px;
  width: 12px;
  min-height: 12px;
  min-width: 12px;
}

.emotion-5 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

<div
    class="emotion-0"
  >
    <div
      aria-live="polite"
      class="emotion-1"
      kind="information"
    >
      <span
        aria-hidden="true"
        class="emotion-2"
      >
        <svg
          viewBox="0 0 33 33"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M16.961 32.338c-8.836 0-16-7.163-16-16 0-8.836 7.164-16 16-16 8.837 0 16 7.164 16 16 0 8.837-7.163 16-16 16zm.19-20.969c1.321 0 2.31-.993 2.31-2.32 0-1.32-.991-2.31-2.31-2.31-1.32 0-2.3.99-2.3 2.31 0 1.327.978 2.32 2.3 2.32zm-1.915 13.827h3.84V13.628h-3.84v11.568z"
            fill="#5CABF7"
            fill-rule="evenodd"
          />
        </svg>
      </span>
      <span
        class="emotion-3"
      />
      <button
        aria-label="Close Notification"
        class="emotion-4"
        type="button"
      >
        <span
          aria-hidden="true"
          class="emotion-5"
        >
          <svg
            fill="none"
            viewBox="0 0 22 22"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              clip-rule="evenodd"
              d="M.0340576 1.62505L1.62505.0340576 10.9058 9.31483 20.1866.0340576l1.591 1.5909924-9.2808 9.28075 9.2808 9.2808-1.591 1.591-9.2808-9.2808-9.28075 9.2808-1.5909911-1.591L9.31483 10.9058.0340576 1.62505z"
              fill="#000000"
              fill-rule="evenodd"
            />
          </svg>
        </span>
      </button>
    </div>
  </div>
</DocumentFragment>
`;

exports[`<Notification /> notification snapshot tests renders dismissible state correctly BananaRepublic in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  transition-property: opacity;
  transition-duration: 250ms;
  transition-timing-function: cubic-bezier(0.25, 0, 0.25, 1);
  transition-delay: 0ms;
  opacity: 1;
}

.emotion-1 {
  line-height: 1.25;
  padding: 0.5rem;
  margin-bottom: 1.125rem;
  border-style: solid;
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 0.875rem;
  border-color: #5CABF7;
  border-width: 2px 2px 2px 8px;
}

.emotion-2 {
  display: inline-block;
  height: 20px;
  width: 20px;
  min-height: 20px;
  min-width: 20px;
}

.emotion-2 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-3 {
  padding-left: 0.75rem;
}

.emotion-4 {
  background: none;
  border: none;
  width: auto;
  margin: 0;
  padding: 0;
  text-align: inherit;
  font: inherit;
  padding-left: 0.65rem;
  margin-left: auto;
  opacity: 0.7;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-self: flex-start;
  -ms-flex-item-align: flex-start;
  align-self: flex-start;
}

.emotion-4:hover,
.emotion-4:focus {
  opacity: 1;
}

.emotion-5 {
  display: inline-block;
  height: 12px;
  width: 12px;
  min-height: 12px;
  min-width: 12px;
}

.emotion-5 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

<div
    class="emotion-0"
  >
    <div
      aria-live="polite"
      class="emotion-1"
      kind="information"
    >
      <span
        aria-hidden="true"
        class="emotion-2"
      >
        <svg
          viewBox="0 0 33 33"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M16.961 32.338c-8.836 0-16-7.163-16-16 0-8.836 7.164-16 16-16 8.837 0 16 7.164 16 16 0 8.837-7.163 16-16 16zm.19-20.969c1.321 0 2.31-.993 2.31-2.32 0-1.32-.991-2.31-2.31-2.31-1.32 0-2.3.99-2.3 2.31 0 1.327.978 2.32 2.3 2.32zm-1.915 13.827h3.84V13.628h-3.84v11.568z"
            fill="#5CABF7"
            fill-rule="evenodd"
          />
        </svg>
      </span>
      <span
        class="emotion-3"
      />
      <button
        aria-label="Close Notification"
        class="emotion-4"
        type="button"
      >
        <span
          aria-hidden="true"
          class="emotion-5"
        >
          <svg
            fill="none"
            viewBox="0 0 22 22"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              clip-rule="evenodd"
              d="M.0340576 1.62505L1.62505.0340576 10.9058 9.31483 20.1866.0340576l1.591 1.5909924-9.2808 9.28075 9.2808 9.2808-1.591 1.591-9.2808-9.2808-9.28075 9.2808-1.5909911-1.591L9.31483 10.9058.0340576 1.62505z"
              fill="#000000"
              fill-rule="evenodd"
            />
          </svg>
        </span>
      </button>
    </div>
  </div>
</DocumentFragment>
`;

exports[`<Notification /> notification snapshot tests renders dismissible state correctly BananaRepublicFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  transition-property: opacity;
  transition-duration: 250ms;
  transition-timing-function: cubic-bezier(0.25, 0, 0.25, 1);
  transition-delay: 0ms;
  opacity: 1;
}

.emotion-1 {
  line-height: 1.25;
  padding: 0.5rem;
  margin-bottom: 1.125rem;
  border-style: solid;
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  font-family: 'Lynstone',Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 0.8125rem;
  border-color: #5CABF7;
  border-width: 2px 2px 2px 8px;
}

.emotion-2 {
  display: inline-block;
  height: 20px;
  width: 20px;
  min-height: 20px;
  min-width: 20px;
}

.emotion-2 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-3 {
  padding-left: 0.75rem;
}

.emotion-4 {
  background: none;
  border: none;
  width: auto;
  margin: 0;
  padding: 0;
  text-align: inherit;
  font: inherit;
  padding-left: 0.65rem;
  margin-left: auto;
  opacity: 0.7;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-self: flex-start;
  -ms-flex-item-align: flex-start;
  align-self: flex-start;
}

.emotion-4:hover,
.emotion-4:focus {
  opacity: 1;
}

.emotion-5 {
  display: inline-block;
  height: 12px;
  width: 12px;
  min-height: 12px;
  min-width: 12px;
}

.emotion-5 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

<div
    class="emotion-0"
  >
    <div
      aria-live="polite"
      class="emotion-1"
      kind="information"
    >
      <span
        aria-hidden="true"
        class="emotion-2"
      >
        <svg
          viewBox="0 0 33 33"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M16.961 32.338c-8.836 0-16-7.163-16-16 0-8.836 7.164-16 16-16 8.837 0 16 7.164 16 16 0 8.837-7.163 16-16 16zm.19-20.969c1.321 0 2.31-.993 2.31-2.32 0-1.32-.991-2.31-2.31-2.31-1.32 0-2.3.99-2.3 2.31 0 1.327.978 2.32 2.3 2.32zm-1.915 13.827h3.84V13.628h-3.84v11.568z"
            fill="#5CABF7"
            fill-rule="evenodd"
          />
        </svg>
      </span>
      <span
        class="emotion-3"
      />
      <button
        aria-label="Close Notification"
        class="emotion-4"
        type="button"
      >
        <span
          aria-hidden="true"
          class="emotion-5"
        >
          <svg
            fill="none"
            viewBox="0 0 22 22"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              clip-rule="evenodd"
              d="M.0340576 1.62505L1.62505.0340576 10.9058 9.31483 20.1866.0340576l1.591 1.5909924-9.2808 9.28075 9.2808 9.2808-1.591 1.591-9.2808-9.2808-9.28075 9.2808-1.5909911-1.591L9.31483 10.9058.0340576 1.62505z"
              fill="#000000"
              fill-rule="evenodd"
            />
          </svg>
        </span>
      </button>
    </div>
  </div>
</DocumentFragment>
`;

exports[`<Notification /> notification snapshot tests renders dismissible state correctly BananaRepublicFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  transition-property: opacity;
  transition-duration: 250ms;
  transition-timing-function: cubic-bezier(0.25, 0, 0.25, 1);
  transition-delay: 0ms;
  opacity: 1;
}

.emotion-1 {
  line-height: 1.25;
  padding: 0.5rem;
  margin-bottom: 1.125rem;
  border-style: solid;
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 0.875rem;
  border-color: #5CABF7;
  border-width: 2px 2px 2px 8px;
}

.emotion-2 {
  display: inline-block;
  height: 20px;
  width: 20px;
  min-height: 20px;
  min-width: 20px;
}

.emotion-2 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-3 {
  padding-left: 0.75rem;
}

.emotion-4 {
  background: none;
  border: none;
  width: auto;
  margin: 0;
  padding: 0;
  text-align: inherit;
  font: inherit;
  padding-left: 0.65rem;
  margin-left: auto;
  opacity: 0.7;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-self: flex-start;
  -ms-flex-item-align: flex-start;
  align-self: flex-start;
}

.emotion-4:hover,
.emotion-4:focus {
  opacity: 1;
}

.emotion-5 {
  display: inline-block;
  height: 12px;
  width: 12px;
  min-height: 12px;
  min-width: 12px;
}

.emotion-5 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

<div
    class="emotion-0"
  >
    <div
      aria-live="polite"
      class="emotion-1"
      kind="information"
    >
      <span
        aria-hidden="true"
        class="emotion-2"
      >
        <svg
          viewBox="0 0 33 33"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M16.961 32.338c-8.836 0-16-7.163-16-16 0-8.836 7.164-16 16-16 8.837 0 16 7.164 16 16 0 8.837-7.163 16-16 16zm.19-20.969c1.321 0 2.31-.993 2.31-2.32 0-1.32-.991-2.31-2.31-2.31-1.32 0-2.3.99-2.3 2.31 0 1.327.978 2.32 2.3 2.32zm-1.915 13.827h3.84V13.628h-3.84v11.568z"
            fill="#5CABF7"
            fill-rule="evenodd"
          />
        </svg>
      </span>
      <span
        class="emotion-3"
      />
      <button
        aria-label="Close Notification"
        class="emotion-4"
        type="button"
      >
        <span
          aria-hidden="true"
          class="emotion-5"
        >
          <svg
            fill="none"
            viewBox="0 0 22 22"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              clip-rule="evenodd"
              d="M.0340576 1.62505L1.62505.0340576 10.9058 9.31483 20.1866.0340576l1.591 1.5909924-9.2808 9.28075 9.2808 9.2808-1.591 1.591-9.2808-9.2808-9.28075 9.2808-1.5909911-1.591L9.31483 10.9058.0340576 1.62505z"
              fill="#000000"
              fill-rule="evenodd"
            />
          </svg>
        </span>
      </button>
    </div>
  </div>
</DocumentFragment>
`;

exports[`<Notification /> notification snapshot tests renders dismissible state correctly Gap 1`] = `
<DocumentFragment>
  .emotion-0 {
  transition-property: opacity;
  transition-duration: 250ms;
  transition-timing-function: cubic-bezier(0.25, 0, 0.25, 1);
  transition-delay: 0ms;
  opacity: 1;
}

.emotion-1 {
  line-height: 1.25;
  padding: 0.5rem;
  margin-bottom: 1.125rem;
  border-style: solid;
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 0.8125rem;
  border-color: #5CABF7;
  border-width: 2px 2px 2px 8px;
}

.emotion-2 {
  display: inline-block;
  height: 20px;
  width: 20px;
  min-height: 20px;
  min-width: 20px;
}

.emotion-2 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-3 {
  padding-left: 0.75rem;
}

.emotion-4 {
  background: none;
  border: none;
  width: auto;
  margin: 0;
  padding: 0;
  text-align: inherit;
  font: inherit;
  padding-left: 0.65rem;
  margin-left: auto;
  opacity: 0.7;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-self: flex-start;
  -ms-flex-item-align: flex-start;
  align-self: flex-start;
}

.emotion-4:hover,
.emotion-4:focus {
  opacity: 1;
}

.emotion-5 {
  display: inline-block;
  height: 12px;
  width: 12px;
  min-height: 12px;
  min-width: 12px;
}

.emotion-5 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

<div
    class="emotion-0"
  >
    <div
      aria-live="polite"
      class="emotion-1"
      kind="information"
    >
      <span
        aria-hidden="true"
        class="emotion-2"
      >
        <svg
          viewBox="0 0 33 33"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M16.961 32.338c-8.836 0-16-7.163-16-16 0-8.836 7.164-16 16-16 8.837 0 16 7.164 16 16 0 8.837-7.163 16-16 16zm.19-20.969c1.321 0 2.31-.993 2.31-2.32 0-1.32-.991-2.31-2.31-2.31-1.32 0-2.3.99-2.3 2.31 0 1.327.978 2.32 2.3 2.32zm-1.915 13.827h3.84V13.628h-3.84v11.568z"
            fill="#5CABF7"
            fill-rule="evenodd"
          />
        </svg>
      </span>
      <span
        class="emotion-3"
      />
      <button
        aria-label="Close Notification"
        class="emotion-4"
        type="button"
      >
        <span
          aria-hidden="true"
          class="emotion-5"
        >
          <svg
            viewBox="0 0 10.126 10.313"
            xmlns="http://www.w3.org/2000/svg"
          >
            <g>
              <path
                d="m9.213.187.913.913-9.211 9.212-.913-.913z"
                fill="#002554"
              />
            </g>
            <g>
              <path
                d="m10.125 9.212-.912.913L0 .914.914 0z"
                fill="#002554"
              />
            </g>
          </svg>
        </span>
      </button>
    </div>
  </div>
</DocumentFragment>
`;

exports[`<Notification /> notification snapshot tests renders dismissible state correctly Gap in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  transition-property: opacity;
  transition-duration: 250ms;
  transition-timing-function: cubic-bezier(0.25, 0, 0.25, 1);
  transition-delay: 0ms;
  opacity: 1;
}

.emotion-1 {
  line-height: 1.25;
  padding: 0.5rem;
  margin-bottom: 1.125rem;
  border-style: solid;
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 0.875rem;
  border-color: #5CABF7;
  border-width: 2px 2px 2px 8px;
}

.emotion-2 {
  display: inline-block;
  height: 20px;
  width: 20px;
  min-height: 20px;
  min-width: 20px;
}

.emotion-2 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-3 {
  padding-left: 0.75rem;
}

.emotion-4 {
  background: none;
  border: none;
  width: auto;
  margin: 0;
  padding: 0;
  text-align: inherit;
  font: inherit;
  padding-left: 0.65rem;
  margin-left: auto;
  opacity: 0.7;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-self: flex-start;
  -ms-flex-item-align: flex-start;
  align-self: flex-start;
}

.emotion-4:hover,
.emotion-4:focus {
  opacity: 1;
}

.emotion-5 {
  display: inline-block;
  height: 12px;
  width: 12px;
  min-height: 12px;
  min-width: 12px;
}

.emotion-5 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

<div
    class="emotion-0"
  >
    <div
      aria-live="polite"
      class="emotion-1"
      kind="information"
    >
      <span
        aria-hidden="true"
        class="emotion-2"
      >
        <svg
          viewBox="0 0 33 33"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M16.961 32.338c-8.836 0-16-7.163-16-16 0-8.836 7.164-16 16-16 8.837 0 16 7.164 16 16 0 8.837-7.163 16-16 16zm.19-20.969c1.321 0 2.31-.993 2.31-2.32 0-1.32-.991-2.31-2.31-2.31-1.32 0-2.3.99-2.3 2.31 0 1.327.978 2.32 2.3 2.32zm-1.915 13.827h3.84V13.628h-3.84v11.568z"
            fill="#5CABF7"
            fill-rule="evenodd"
          />
        </svg>
      </span>
      <span
        class="emotion-3"
      />
      <button
        aria-label="Close Notification"
        class="emotion-4"
        type="button"
      >
        <span
          aria-hidden="true"
          class="emotion-5"
        >
          <svg
            viewBox="0 0 10.126 10.313"
            xmlns="http://www.w3.org/2000/svg"
          >
            <g>
              <path
                d="m9.213.187.913.913-9.211 9.212-.913-.913z"
                fill="#002554"
              />
            </g>
            <g>
              <path
                d="m10.125 9.212-.912.913L0 .914.914 0z"
                fill="#002554"
              />
            </g>
          </svg>
        </span>
      </button>
    </div>
  </div>
</DocumentFragment>
`;

exports[`<Notification /> notification snapshot tests renders dismissible state correctly GapFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  transition-property: opacity;
  transition-duration: 250ms;
  transition-timing-function: cubic-bezier(0.25, 0, 0.25, 1);
  transition-delay: 0ms;
  opacity: 1;
}

.emotion-1 {
  line-height: 1.25;
  padding: 0.5rem;
  margin-bottom: 1.125rem;
  border-style: solid;
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 0.8125rem;
  border-color: #5CABF7;
  border-width: 2px 2px 2px 8px;
}

.emotion-2 {
  display: inline-block;
  height: 20px;
  width: 20px;
  min-height: 20px;
  min-width: 20px;
}

.emotion-2 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-3 {
  padding-left: 0.75rem;
}

.emotion-4 {
  background: none;
  border: none;
  width: auto;
  margin: 0;
  padding: 0;
  text-align: inherit;
  font: inherit;
  padding-left: 0.65rem;
  margin-left: auto;
  opacity: 0.7;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-self: flex-start;
  -ms-flex-item-align: flex-start;
  align-self: flex-start;
}

.emotion-4:hover,
.emotion-4:focus {
  opacity: 1;
}

.emotion-5 {
  display: inline-block;
  height: 12px;
  width: 12px;
  min-height: 12px;
  min-width: 12px;
}

.emotion-5 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

<div
    class="emotion-0"
  >
    <div
      aria-live="polite"
      class="emotion-1"
      kind="information"
    >
      <span
        aria-hidden="true"
        class="emotion-2"
      >
        <svg
          viewBox="0 0 33 33"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M16.961 32.338c-8.836 0-16-7.163-16-16 0-8.836 7.164-16 16-16 8.837 0 16 7.164 16 16 0 8.837-7.163 16-16 16zm.19-20.969c1.321 0 2.31-.993 2.31-2.32 0-1.32-.991-2.31-2.31-2.31-1.32 0-2.3.99-2.3 2.31 0 1.327.978 2.32 2.3 2.32zm-1.915 13.827h3.84V13.628h-3.84v11.568z"
            fill="#5CABF7"
            fill-rule="evenodd"
          />
        </svg>
      </span>
      <span
        class="emotion-3"
      />
      <button
        aria-label="Close Notification"
        class="emotion-4"
        type="button"
      >
        <span
          aria-hidden="true"
          class="emotion-5"
        >
          <svg
            viewBox="0 0 10.126 10.313"
            xmlns="http://www.w3.org/2000/svg"
          >
            <g>
              <path
                d="m9.213.187.913.913-9.211 9.212-.913-.913z"
                fill="#002554"
              />
            </g>
            <g>
              <path
                d="m10.125 9.212-.912.913L0 .914.914 0z"
                fill="#002554"
              />
            </g>
          </svg>
        </span>
      </button>
    </div>
  </div>
</DocumentFragment>
`;

exports[`<Notification /> notification snapshot tests renders dismissible state correctly GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  transition-property: opacity;
  transition-duration: 250ms;
  transition-timing-function: cubic-bezier(0.25, 0, 0.25, 1);
  transition-delay: 0ms;
  opacity: 1;
}

.emotion-1 {
  line-height: 1.25;
  padding: 0.5rem;
  margin-bottom: 1.125rem;
  border-style: solid;
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 0.875rem;
  border-color: #5CABF7;
  border-width: 2px 2px 2px 8px;
}

.emotion-2 {
  display: inline-block;
  height: 20px;
  width: 20px;
  min-height: 20px;
  min-width: 20px;
}

.emotion-2 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-3 {
  padding-left: 0.75rem;
}

.emotion-4 {
  background: none;
  border: none;
  width: auto;
  margin: 0;
  padding: 0;
  text-align: inherit;
  font: inherit;
  padding-left: 0.65rem;
  margin-left: auto;
  opacity: 0.7;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-self: flex-start;
  -ms-flex-item-align: flex-start;
  align-self: flex-start;
}

.emotion-4:hover,
.emotion-4:focus {
  opacity: 1;
}

.emotion-5 {
  display: inline-block;
  height: 12px;
  width: 12px;
  min-height: 12px;
  min-width: 12px;
}

.emotion-5 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

<div
    class="emotion-0"
  >
    <div
      aria-live="polite"
      class="emotion-1"
      kind="information"
    >
      <span
        aria-hidden="true"
        class="emotion-2"
      >
        <svg
          viewBox="0 0 33 33"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M16.961 32.338c-8.836 0-16-7.163-16-16 0-8.836 7.164-16 16-16 8.837 0 16 7.164 16 16 0 8.837-7.163 16-16 16zm.19-20.969c1.321 0 2.31-.993 2.31-2.32 0-1.32-.991-2.31-2.31-2.31-1.32 0-2.3.99-2.3 2.31 0 1.327.978 2.32 2.3 2.32zm-1.915 13.827h3.84V13.628h-3.84v11.568z"
            fill="#5CABF7"
            fill-rule="evenodd"
          />
        </svg>
      </span>
      <span
        class="emotion-3"
      />
      <button
        aria-label="Close Notification"
        class="emotion-4"
        type="button"
      >
        <span
          aria-hidden="true"
          class="emotion-5"
        >
          <svg
            viewBox="0 0 10.126 10.313"
            xmlns="http://www.w3.org/2000/svg"
          >
            <g>
              <path
                d="m9.213.187.913.913-9.211 9.212-.913-.913z"
                fill="#002554"
              />
            </g>
            <g>
              <path
                d="m10.125 9.212-.912.913L0 .914.914 0z"
                fill="#002554"
              />
            </g>
          </svg>
        </span>
      </button>
    </div>
  </div>
</DocumentFragment>
`;

exports[`<Notification /> notification snapshot tests renders dismissible state correctly OldNavy 1`] = `
<DocumentFragment>
  .emotion-0 {
  transition-property: opacity;
  transition-duration: 250ms;
  transition-timing-function: cubic-bezier(0.25, 0, 0.25, 1);
  transition-delay: 0ms;
  opacity: 1;
}

.emotion-1 {
  line-height: 1.25;
  padding: 0.5rem;
  margin-bottom: 1.125rem;
  border-style: solid;
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  font-family: 'Gotham','Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 0.875rem;
  border-color: #5CABF7;
  border-width: 2px 2px 2px 8px;
}

.emotion-2 {
  display: inline-block;
  height: 20px;
  width: 20px;
  min-height: 20px;
  min-width: 20px;
}

.emotion-2 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-3 {
  padding-left: 0.75rem;
}

.emotion-4 {
  background: none;
  border: none;
  width: auto;
  margin: 0;
  padding: 0;
  text-align: inherit;
  font: inherit;
  padding-left: 0.65rem;
  margin-left: auto;
  opacity: 0.7;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-self: flex-start;
  -ms-flex-item-align: flex-start;
  align-self: flex-start;
}

.emotion-4:hover,
.emotion-4:focus {
  opacity: 1;
}

.emotion-5 {
  display: inline-block;
  height: 12px;
  width: 12px;
  min-height: 12px;
  min-width: 12px;
}

.emotion-5 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

<div
    class="emotion-0"
  >
    <div
      aria-live="polite"
      class="emotion-1"
      kind="information"
    >
      <span
        aria-hidden="true"
        class="emotion-2"
      >
        <svg
          viewBox="0 0 33 33"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M16.961 32.338c-8.836 0-16-7.163-16-16 0-8.836 7.164-16 16-16 8.837 0 16 7.164 16 16 0 8.837-7.163 16-16 16zm.19-20.969c1.321 0 2.31-.993 2.31-2.32 0-1.32-.991-2.31-2.31-2.31-1.32 0-2.3.99-2.3 2.31 0 1.327.978 2.32 2.3 2.32zm-1.915 13.827h3.84V13.628h-3.84v11.568z"
            fill="#5CABF7"
            fill-rule="evenodd"
          />
        </svg>
      </span>
      <span
        class="emotion-3"
      />
      <button
        aria-label="Close Notification"
        class="emotion-4"
        type="button"
      >
        <span
          aria-hidden="true"
          class="emotion-5"
        >
          <svg
            fill="none"
            viewBox="0 0 22 22"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              clip-rule="evenodd"
              d="M.0339966 1.62505L1.62499.0340576 10.9058 9.31483 20.1865.0340576l1.591 1.5909924-9.2807 9.28075 9.2807 9.2808-1.591 1.591-9.2807-9.2808-9.28081 9.2808-1.5909922-1.591L9.31477 10.9058.0339966 1.62505z"
              fill="#043863"
              fill-rule="evenodd"
            />
          </svg>
        </span>
      </button>
    </div>
  </div>
</DocumentFragment>
`;

exports[`<Notification /> notification snapshot tests renders dismissible state correctly OldNavy in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  transition-property: opacity;
  transition-duration: 250ms;
  transition-timing-function: cubic-bezier(0.25, 0, 0.25, 1);
  transition-delay: 0ms;
  opacity: 1;
}

.emotion-1 {
  line-height: 1.25;
  padding: 0.5rem;
  margin-bottom: 1.125rem;
  border-style: solid;
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 0.875rem;
  border-color: #5CABF7;
  border-width: 2px 2px 2px 8px;
}

.emotion-2 {
  display: inline-block;
  height: 20px;
  width: 20px;
  min-height: 20px;
  min-width: 20px;
}

.emotion-2 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-3 {
  padding-left: 0.75rem;
}

.emotion-4 {
  background: none;
  border: none;
  width: auto;
  margin: 0;
  padding: 0;
  text-align: inherit;
  font: inherit;
  padding-left: 0.65rem;
  margin-left: auto;
  opacity: 0.7;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-self: flex-start;
  -ms-flex-item-align: flex-start;
  align-self: flex-start;
}

.emotion-4:hover,
.emotion-4:focus {
  opacity: 1;
}

.emotion-5 {
  display: inline-block;
  height: 12px;
  width: 12px;
  min-height: 12px;
  min-width: 12px;
}

.emotion-5 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

<div
    class="emotion-0"
  >
    <div
      aria-live="polite"
      class="emotion-1"
      kind="information"
    >
      <span
        aria-hidden="true"
        class="emotion-2"
      >
        <svg
          viewBox="0 0 33 33"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M16.961 32.338c-8.836 0-16-7.163-16-16 0-8.836 7.164-16 16-16 8.837 0 16 7.164 16 16 0 8.837-7.163 16-16 16zm.19-20.969c1.321 0 2.31-.993 2.31-2.32 0-1.32-.991-2.31-2.31-2.31-1.32 0-2.3.99-2.3 2.31 0 1.327.978 2.32 2.3 2.32zm-1.915 13.827h3.84V13.628h-3.84v11.568z"
            fill="#5CABF7"
            fill-rule="evenodd"
          />
        </svg>
      </span>
      <span
        class="emotion-3"
      />
      <button
        aria-label="Close Notification"
        class="emotion-4"
        type="button"
      >
        <span
          aria-hidden="true"
          class="emotion-5"
        >
          <svg
            fill="none"
            viewBox="0 0 22 22"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              clip-rule="evenodd"
              d="M.0339966 1.62505L1.62499.0340576 10.9058 9.31483 20.1865.0340576l1.591 1.5909924-9.2807 9.28075 9.2807 9.2808-1.591 1.591-9.2807-9.2808-9.28081 9.2808-1.5909922-1.591L9.31477 10.9058.0339966 1.62505z"
              fill="#043863"
              fill-rule="evenodd"
            />
          </svg>
        </span>
      </button>
    </div>
  </div>
</DocumentFragment>
`;

exports[`<Notification /> notification snapshot tests renders error state correctly Athleta 1`] = `
<DocumentFragment>
  .emotion-0 {
  transition-property: opacity;
  transition-duration: 250ms;
  transition-timing-function: cubic-bezier(0.25, 0, 0.25, 1);
  transition-delay: 0ms;
  opacity: 1;
}

.emotion-1 {
  line-height: 1.25;
  padding: 0.5rem;
  margin-bottom: 1.125rem;
  border-style: solid;
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  font-family: 'Avenir Next','Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 0.875rem;
  border-color: #D00000;
  border-width: 2px 2px 2px 8px;
}

.emotion-2 {
  display: inline-block;
  height: 20px;
  width: 20px;
  min-height: 20px;
  min-width: 20px;
}

.emotion-2 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-3 {
  padding-left: 0.75rem;
}

<div
    class="emotion-0"
  >
    <div
      aria-live="assertive"
      class="emotion-1"
      kind="error"
    >
      <span
        aria-hidden="true"
        class="emotion-2"
      >
        <svg
          viewBox="0 0 33 33"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M16.039.661c8.836 0 16 7.164 16 16 0 8.837-7.164 16-16 16-8.837 0-16-7.163-16-16 0-8.836 7.163-16 16-16zm-.19 20.97c-1.321 0-2.31.993-2.31 2.32 0 1.32.991 2.31 2.31 2.31 1.32 0 2.3-.99 2.3-2.31 0-1.327-.978-2.32-2.3-2.32zm1.915-13.827h-3.84v11.568h3.84V7.804z"
            fill="#D00000"
            fill-rule="evenodd"
          />
        </svg>
      </span>
      <span
        class="emotion-3"
      />
    </div>
  </div>
</DocumentFragment>
`;

exports[`<Notification /> notification snapshot tests renders error state correctly Athleta in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  transition-property: opacity;
  transition-duration: 250ms;
  transition-timing-function: cubic-bezier(0.25, 0, 0.25, 1);
  transition-delay: 0ms;
  opacity: 1;
}

.emotion-1 {
  line-height: 1.25;
  padding: 0.5rem;
  margin-bottom: 1.125rem;
  border-style: solid;
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 0.875rem;
  border-color: #D00000;
  border-width: 2px 2px 2px 8px;
}

.emotion-2 {
  display: inline-block;
  height: 20px;
  width: 20px;
  min-height: 20px;
  min-width: 20px;
}

.emotion-2 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-3 {
  padding-left: 0.75rem;
}

<div
    class="emotion-0"
  >
    <div
      aria-live="assertive"
      class="emotion-1"
      kind="error"
    >
      <span
        aria-hidden="true"
        class="emotion-2"
      >
        <svg
          viewBox="0 0 33 33"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M16.039.661c8.836 0 16 7.164 16 16 0 8.837-7.164 16-16 16-8.837 0-16-7.163-16-16 0-8.836 7.163-16 16-16zm-.19 20.97c-1.321 0-2.31.993-2.31 2.32 0 1.32.991 2.31 2.31 2.31 1.32 0 2.3-.99 2.3-2.31 0-1.327-.978-2.32-2.3-2.32zm1.915-13.827h-3.84v11.568h3.84V7.804z"
            fill="#D00000"
            fill-rule="evenodd"
          />
        </svg>
      </span>
      <span
        class="emotion-3"
      />
    </div>
  </div>
</DocumentFragment>
`;

exports[`<Notification /> notification snapshot tests renders error state correctly BananaRepublic 1`] = `
<DocumentFragment>
  .emotion-0 {
  transition-property: opacity;
  transition-duration: 250ms;
  transition-timing-function: cubic-bezier(0.25, 0, 0.25, 1);
  transition-delay: 0ms;
  opacity: 1;
}

.emotion-1 {
  line-height: 1.25;
  padding: 0.5rem;
  margin-bottom: 1.125rem;
  border-style: solid;
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  font-family: 'Lynstone',Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 0.8125rem;
  border-color: #D00000;
  border-width: 2px 2px 2px 8px;
}

.emotion-2 {
  display: inline-block;
  height: 20px;
  width: 20px;
  min-height: 20px;
  min-width: 20px;
}

.emotion-2 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-3 {
  padding-left: 0.75rem;
}

<div
    class="emotion-0"
  >
    <div
      aria-live="assertive"
      class="emotion-1"
      kind="error"
    >
      <span
        aria-hidden="true"
        class="emotion-2"
      >
        <svg
          viewBox="0 0 33 33"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M16.039.661c8.836 0 16 7.164 16 16 0 8.837-7.164 16-16 16-8.837 0-16-7.163-16-16 0-8.836 7.163-16 16-16zm-.19 20.97c-1.321 0-2.31.993-2.31 2.32 0 1.32.991 2.31 2.31 2.31 1.32 0 2.3-.99 2.3-2.31 0-1.327-.978-2.32-2.3-2.32zm1.915-13.827h-3.84v11.568h3.84V7.804z"
            fill="#D00000"
            fill-rule="evenodd"
          />
        </svg>
      </span>
      <span
        class="emotion-3"
      />
    </div>
  </div>
</DocumentFragment>
`;

exports[`<Notification /> notification snapshot tests renders error state correctly BananaRepublic in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  transition-property: opacity;
  transition-duration: 250ms;
  transition-timing-function: cubic-bezier(0.25, 0, 0.25, 1);
  transition-delay: 0ms;
  opacity: 1;
}

.emotion-1 {
  line-height: 1.25;
  padding: 0.5rem;
  margin-bottom: 1.125rem;
  border-style: solid;
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 0.875rem;
  border-color: #D00000;
  border-width: 2px 2px 2px 8px;
}

.emotion-2 {
  display: inline-block;
  height: 20px;
  width: 20px;
  min-height: 20px;
  min-width: 20px;
}

.emotion-2 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-3 {
  padding-left: 0.75rem;
}

<div
    class="emotion-0"
  >
    <div
      aria-live="assertive"
      class="emotion-1"
      kind="error"
    >
      <span
        aria-hidden="true"
        class="emotion-2"
      >
        <svg
          viewBox="0 0 33 33"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M16.039.661c8.836 0 16 7.164 16 16 0 8.837-7.164 16-16 16-8.837 0-16-7.163-16-16 0-8.836 7.163-16 16-16zm-.19 20.97c-1.321 0-2.31.993-2.31 2.32 0 1.32.991 2.31 2.31 2.31 1.32 0 2.3-.99 2.3-2.31 0-1.327-.978-2.32-2.3-2.32zm1.915-13.827h-3.84v11.568h3.84V7.804z"
            fill="#D00000"
            fill-rule="evenodd"
          />
        </svg>
      </span>
      <span
        class="emotion-3"
      />
    </div>
  </div>
</DocumentFragment>
`;

exports[`<Notification /> notification snapshot tests renders error state correctly BananaRepublicFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  transition-property: opacity;
  transition-duration: 250ms;
  transition-timing-function: cubic-bezier(0.25, 0, 0.25, 1);
  transition-delay: 0ms;
  opacity: 1;
}

.emotion-1 {
  line-height: 1.25;
  padding: 0.5rem;
  margin-bottom: 1.125rem;
  border-style: solid;
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  font-family: 'Lynstone',Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 0.8125rem;
  border-color: #D00000;
  border-width: 2px 2px 2px 8px;
}

.emotion-2 {
  display: inline-block;
  height: 20px;
  width: 20px;
  min-height: 20px;
  min-width: 20px;
}

.emotion-2 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-3 {
  padding-left: 0.75rem;
}

<div
    class="emotion-0"
  >
    <div
      aria-live="assertive"
      class="emotion-1"
      kind="error"
    >
      <span
        aria-hidden="true"
        class="emotion-2"
      >
        <svg
          viewBox="0 0 33 33"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M16.039.661c8.836 0 16 7.164 16 16 0 8.837-7.164 16-16 16-8.837 0-16-7.163-16-16 0-8.836 7.163-16 16-16zm-.19 20.97c-1.321 0-2.31.993-2.31 2.32 0 1.32.991 2.31 2.31 2.31 1.32 0 2.3-.99 2.3-2.31 0-1.327-.978-2.32-2.3-2.32zm1.915-13.827h-3.84v11.568h3.84V7.804z"
            fill="#D00000"
            fill-rule="evenodd"
          />
        </svg>
      </span>
      <span
        class="emotion-3"
      />
    </div>
  </div>
</DocumentFragment>
`;

exports[`<Notification /> notification snapshot tests renders error state correctly BananaRepublicFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  transition-property: opacity;
  transition-duration: 250ms;
  transition-timing-function: cubic-bezier(0.25, 0, 0.25, 1);
  transition-delay: 0ms;
  opacity: 1;
}

.emotion-1 {
  line-height: 1.25;
  padding: 0.5rem;
  margin-bottom: 1.125rem;
  border-style: solid;
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 0.875rem;
  border-color: #D00000;
  border-width: 2px 2px 2px 8px;
}

.emotion-2 {
  display: inline-block;
  height: 20px;
  width: 20px;
  min-height: 20px;
  min-width: 20px;
}

.emotion-2 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-3 {
  padding-left: 0.75rem;
}

<div
    class="emotion-0"
  >
    <div
      aria-live="assertive"
      class="emotion-1"
      kind="error"
    >
      <span
        aria-hidden="true"
        class="emotion-2"
      >
        <svg
          viewBox="0 0 33 33"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M16.039.661c8.836 0 16 7.164 16 16 0 8.837-7.164 16-16 16-8.837 0-16-7.163-16-16 0-8.836 7.163-16 16-16zm-.19 20.97c-1.321 0-2.31.993-2.31 2.32 0 1.32.991 2.31 2.31 2.31 1.32 0 2.3-.99 2.3-2.31 0-1.327-.978-2.32-2.3-2.32zm1.915-13.827h-3.84v11.568h3.84V7.804z"
            fill="#D00000"
            fill-rule="evenodd"
          />
        </svg>
      </span>
      <span
        class="emotion-3"
      />
    </div>
  </div>
</DocumentFragment>
`;

exports[`<Notification /> notification snapshot tests renders error state correctly Gap 1`] = `
<DocumentFragment>
  .emotion-0 {
  transition-property: opacity;
  transition-duration: 250ms;
  transition-timing-function: cubic-bezier(0.25, 0, 0.25, 1);
  transition-delay: 0ms;
  opacity: 1;
}

.emotion-1 {
  line-height: 1.25;
  padding: 0.5rem;
  margin-bottom: 1.125rem;
  border-style: solid;
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 0.8125rem;
  border-color: #D00000;
  border-width: 2px 2px 2px 8px;
}

.emotion-2 {
  display: inline-block;
  height: 20px;
  width: 20px;
  min-height: 20px;
  min-width: 20px;
}

.emotion-2 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-3 {
  padding-left: 0.75rem;
}

<div
    class="emotion-0"
  >
    <div
      aria-live="assertive"
      class="emotion-1"
      kind="error"
    >
      <span
        aria-hidden="true"
        class="emotion-2"
      >
        <svg
          viewBox="0 0 33 33"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M16.039.661c8.836 0 16 7.164 16 16 0 8.837-7.164 16-16 16-8.837 0-16-7.163-16-16 0-8.836 7.163-16 16-16zm-.19 20.97c-1.321 0-2.31.993-2.31 2.32 0 1.32.991 2.31 2.31 2.31 1.32 0 2.3-.99 2.3-2.31 0-1.327-.978-2.32-2.3-2.32zm1.915-13.827h-3.84v11.568h3.84V7.804z"
            fill="#D00000"
            fill-rule="evenodd"
          />
        </svg>
      </span>
      <span
        class="emotion-3"
      />
    </div>
  </div>
</DocumentFragment>
`;

exports[`<Notification /> notification snapshot tests renders error state correctly Gap in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  transition-property: opacity;
  transition-duration: 250ms;
  transition-timing-function: cubic-bezier(0.25, 0, 0.25, 1);
  transition-delay: 0ms;
  opacity: 1;
}

.emotion-1 {
  line-height: 1.25;
  padding: 0.5rem;
  margin-bottom: 1.125rem;
  border-style: solid;
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 0.875rem;
  border-color: #D00000;
  border-width: 2px 2px 2px 8px;
}

.emotion-2 {
  display: inline-block;
  height: 20px;
  width: 20px;
  min-height: 20px;
  min-width: 20px;
}

.emotion-2 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-3 {
  padding-left: 0.75rem;
}

<div
    class="emotion-0"
  >
    <div
      aria-live="assertive"
      class="emotion-1"
      kind="error"
    >
      <span
        aria-hidden="true"
        class="emotion-2"
      >
        <svg
          viewBox="0 0 33 33"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M16.039.661c8.836 0 16 7.164 16 16 0 8.837-7.164 16-16 16-8.837 0-16-7.163-16-16 0-8.836 7.163-16 16-16zm-.19 20.97c-1.321 0-2.31.993-2.31 2.32 0 1.32.991 2.31 2.31 2.31 1.32 0 2.3-.99 2.3-2.31 0-1.327-.978-2.32-2.3-2.32zm1.915-13.827h-3.84v11.568h3.84V7.804z"
            fill="#D00000"
            fill-rule="evenodd"
          />
        </svg>
      </span>
      <span
        class="emotion-3"
      />
    </div>
  </div>
</DocumentFragment>
`;

exports[`<Notification /> notification snapshot tests renders error state correctly GapFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  transition-property: opacity;
  transition-duration: 250ms;
  transition-timing-function: cubic-bezier(0.25, 0, 0.25, 1);
  transition-delay: 0ms;
  opacity: 1;
}

.emotion-1 {
  line-height: 1.25;
  padding: 0.5rem;
  margin-bottom: 1.125rem;
  border-style: solid;
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 0.8125rem;
  border-color: #D00000;
  border-width: 2px 2px 2px 8px;
}

.emotion-2 {
  display: inline-block;
  height: 20px;
  width: 20px;
  min-height: 20px;
  min-width: 20px;
}

.emotion-2 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-3 {
  padding-left: 0.75rem;
}

<div
    class="emotion-0"
  >
    <div
      aria-live="assertive"
      class="emotion-1"
      kind="error"
    >
      <span
        aria-hidden="true"
        class="emotion-2"
      >
        <svg
          viewBox="0 0 33 33"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M16.039.661c8.836 0 16 7.164 16 16 0 8.837-7.164 16-16 16-8.837 0-16-7.163-16-16 0-8.836 7.163-16 16-16zm-.19 20.97c-1.321 0-2.31.993-2.31 2.32 0 1.32.991 2.31 2.31 2.31 1.32 0 2.3-.99 2.3-2.31 0-1.327-.978-2.32-2.3-2.32zm1.915-13.827h-3.84v11.568h3.84V7.804z"
            fill="#D00000"
            fill-rule="evenodd"
          />
        </svg>
      </span>
      <span
        class="emotion-3"
      />
    </div>
  </div>
</DocumentFragment>
`;

exports[`<Notification /> notification snapshot tests renders error state correctly GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  transition-property: opacity;
  transition-duration: 250ms;
  transition-timing-function: cubic-bezier(0.25, 0, 0.25, 1);
  transition-delay: 0ms;
  opacity: 1;
}

.emotion-1 {
  line-height: 1.25;
  padding: 0.5rem;
  margin-bottom: 1.125rem;
  border-style: solid;
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 0.875rem;
  border-color: #D00000;
  border-width: 2px 2px 2px 8px;
}

.emotion-2 {
  display: inline-block;
  height: 20px;
  width: 20px;
  min-height: 20px;
  min-width: 20px;
}

.emotion-2 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-3 {
  padding-left: 0.75rem;
}

<div
    class="emotion-0"
  >
    <div
      aria-live="assertive"
      class="emotion-1"
      kind="error"
    >
      <span
        aria-hidden="true"
        class="emotion-2"
      >
        <svg
          viewBox="0 0 33 33"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M16.039.661c8.836 0 16 7.164 16 16 0 8.837-7.164 16-16 16-8.837 0-16-7.163-16-16 0-8.836 7.163-16 16-16zm-.19 20.97c-1.321 0-2.31.993-2.31 2.32 0 1.32.991 2.31 2.31 2.31 1.32 0 2.3-.99 2.3-2.31 0-1.327-.978-2.32-2.3-2.32zm1.915-13.827h-3.84v11.568h3.84V7.804z"
            fill="#D00000"
            fill-rule="evenodd"
          />
        </svg>
      </span>
      <span
        class="emotion-3"
      />
    </div>
  </div>
</DocumentFragment>
`;

exports[`<Notification /> notification snapshot tests renders error state correctly OldNavy 1`] = `
<DocumentFragment>
  .emotion-0 {
  transition-property: opacity;
  transition-duration: 250ms;
  transition-timing-function: cubic-bezier(0.25, 0, 0.25, 1);
  transition-delay: 0ms;
  opacity: 1;
}

.emotion-1 {
  line-height: 1.25;
  padding: 0.5rem;
  margin-bottom: 1.125rem;
  border-style: solid;
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  font-family: 'Gotham','Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 0.875rem;
  border-color: #D00000;
  border-width: 2px 2px 2px 8px;
}

.emotion-2 {
  display: inline-block;
  height: 20px;
  width: 20px;
  min-height: 20px;
  min-width: 20px;
}

.emotion-2 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-3 {
  padding-left: 0.75rem;
}

<div
    class="emotion-0"
  >
    <div
      aria-live="assertive"
      class="emotion-1"
      kind="error"
    >
      <span
        aria-hidden="true"
        class="emotion-2"
      >
        <svg
          viewBox="0 0 33 33"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M16.039.661c8.836 0 16 7.164 16 16 0 8.837-7.164 16-16 16-8.837 0-16-7.163-16-16 0-8.836 7.163-16 16-16zm-.19 20.97c-1.321 0-2.31.993-2.31 2.32 0 1.32.991 2.31 2.31 2.31 1.32 0 2.3-.99 2.3-2.31 0-1.327-.978-2.32-2.3-2.32zm1.915-13.827h-3.84v11.568h3.84V7.804z"
            fill="#D00000"
            fill-rule="evenodd"
          />
        </svg>
      </span>
      <span
        class="emotion-3"
      />
    </div>
  </div>
</DocumentFragment>
`;

exports[`<Notification /> notification snapshot tests renders error state correctly OldNavy in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  transition-property: opacity;
  transition-duration: 250ms;
  transition-timing-function: cubic-bezier(0.25, 0, 0.25, 1);
  transition-delay: 0ms;
  opacity: 1;
}

.emotion-1 {
  line-height: 1.25;
  padding: 0.5rem;
  margin-bottom: 1.125rem;
  border-style: solid;
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 0.875rem;
  border-color: #D00000;
  border-width: 2px 2px 2px 8px;
}

.emotion-2 {
  display: inline-block;
  height: 20px;
  width: 20px;
  min-height: 20px;
  min-width: 20px;
}

.emotion-2 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-3 {
  padding-left: 0.75rem;
}

<div
    class="emotion-0"
  >
    <div
      aria-live="assertive"
      class="emotion-1"
      kind="error"
    >
      <span
        aria-hidden="true"
        class="emotion-2"
      >
        <svg
          viewBox="0 0 33 33"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M16.039.661c8.836 0 16 7.164 16 16 0 8.837-7.164 16-16 16-8.837 0-16-7.163-16-16 0-8.836 7.163-16 16-16zm-.19 20.97c-1.321 0-2.31.993-2.31 2.32 0 1.32.991 2.31 2.31 2.31 1.32 0 2.3-.99 2.3-2.31 0-1.327-.978-2.32-2.3-2.32zm1.915-13.827h-3.84v11.568h3.84V7.804z"
            fill="#D00000"
            fill-rule="evenodd"
          />
        </svg>
      </span>
      <span
        class="emotion-3"
      />
    </div>
  </div>
</DocumentFragment>
`;

exports[`<Notification /> notification snapshot tests renders information state correctly Athleta 1`] = `
<DocumentFragment>
  .emotion-0 {
  transition-property: opacity;
  transition-duration: 250ms;
  transition-timing-function: cubic-bezier(0.25, 0, 0.25, 1);
  transition-delay: 0ms;
  opacity: 1;
}

.emotion-1 {
  line-height: 1.25;
  padding: 0.5rem;
  margin-bottom: 1.125rem;
  border-style: solid;
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  font-family: 'Avenir Next','Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 0.875rem;
  border-color: #5CABF7;
  border-width: 2px 2px 2px 8px;
}

.emotion-2 {
  display: inline-block;
  height: 20px;
  width: 20px;
  min-height: 20px;
  min-width: 20px;
}

.emotion-2 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-3 {
  padding-left: 0.75rem;
}

<div
    class="emotion-0"
  >
    <div
      aria-live="polite"
      class="emotion-1"
      kind="information"
    >
      <span
        aria-hidden="true"
        class="emotion-2"
      >
        <svg
          viewBox="0 0 33 33"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M16.961 32.338c-8.836 0-16-7.163-16-16 0-8.836 7.164-16 16-16 8.837 0 16 7.164 16 16 0 8.837-7.163 16-16 16zm.19-20.969c1.321 0 2.31-.993 2.31-2.32 0-1.32-.991-2.31-2.31-2.31-1.32 0-2.3.99-2.3 2.31 0 1.327.978 2.32 2.3 2.32zm-1.915 13.827h3.84V13.628h-3.84v11.568z"
            fill="#5CABF7"
            fill-rule="evenodd"
          />
        </svg>
      </span>
      <span
        class="emotion-3"
      />
    </div>
  </div>
</DocumentFragment>
`;

exports[`<Notification /> notification snapshot tests renders information state correctly Athleta in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  transition-property: opacity;
  transition-duration: 250ms;
  transition-timing-function: cubic-bezier(0.25, 0, 0.25, 1);
  transition-delay: 0ms;
  opacity: 1;
}

.emotion-1 {
  line-height: 1.25;
  padding: 0.5rem;
  margin-bottom: 1.125rem;
  border-style: solid;
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 0.875rem;
  border-color: #5CABF7;
  border-width: 2px 2px 2px 8px;
}

.emotion-2 {
  display: inline-block;
  height: 20px;
  width: 20px;
  min-height: 20px;
  min-width: 20px;
}

.emotion-2 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-3 {
  padding-left: 0.75rem;
}

<div
    class="emotion-0"
  >
    <div
      aria-live="polite"
      class="emotion-1"
      kind="information"
    >
      <span
        aria-hidden="true"
        class="emotion-2"
      >
        <svg
          viewBox="0 0 33 33"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M16.961 32.338c-8.836 0-16-7.163-16-16 0-8.836 7.164-16 16-16 8.837 0 16 7.164 16 16 0 8.837-7.163 16-16 16zm.19-20.969c1.321 0 2.31-.993 2.31-2.32 0-1.32-.991-2.31-2.31-2.31-1.32 0-2.3.99-2.3 2.31 0 1.327.978 2.32 2.3 2.32zm-1.915 13.827h3.84V13.628h-3.84v11.568z"
            fill="#5CABF7"
            fill-rule="evenodd"
          />
        </svg>
      </span>
      <span
        class="emotion-3"
      />
    </div>
  </div>
</DocumentFragment>
`;

exports[`<Notification /> notification snapshot tests renders information state correctly BananaRepublic 1`] = `
<DocumentFragment>
  .emotion-0 {
  transition-property: opacity;
  transition-duration: 250ms;
  transition-timing-function: cubic-bezier(0.25, 0, 0.25, 1);
  transition-delay: 0ms;
  opacity: 1;
}

.emotion-1 {
  line-height: 1.25;
  padding: 0.5rem;
  margin-bottom: 1.125rem;
  border-style: solid;
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  font-family: 'Lynstone',Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 0.8125rem;
  border-color: #5CABF7;
  border-width: 2px 2px 2px 8px;
}

.emotion-2 {
  display: inline-block;
  height: 20px;
  width: 20px;
  min-height: 20px;
  min-width: 20px;
}

.emotion-2 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-3 {
  padding-left: 0.75rem;
}

<div
    class="emotion-0"
  >
    <div
      aria-live="polite"
      class="emotion-1"
      kind="information"
    >
      <span
        aria-hidden="true"
        class="emotion-2"
      >
        <svg
          viewBox="0 0 33 33"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M16.961 32.338c-8.836 0-16-7.163-16-16 0-8.836 7.164-16 16-16 8.837 0 16 7.164 16 16 0 8.837-7.163 16-16 16zm.19-20.969c1.321 0 2.31-.993 2.31-2.32 0-1.32-.991-2.31-2.31-2.31-1.32 0-2.3.99-2.3 2.31 0 1.327.978 2.32 2.3 2.32zm-1.915 13.827h3.84V13.628h-3.84v11.568z"
            fill="#5CABF7"
            fill-rule="evenodd"
          />
        </svg>
      </span>
      <span
        class="emotion-3"
      />
    </div>
  </div>
</DocumentFragment>
`;

exports[`<Notification /> notification snapshot tests renders information state correctly BananaRepublic in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  transition-property: opacity;
  transition-duration: 250ms;
  transition-timing-function: cubic-bezier(0.25, 0, 0.25, 1);
  transition-delay: 0ms;
  opacity: 1;
}

.emotion-1 {
  line-height: 1.25;
  padding: 0.5rem;
  margin-bottom: 1.125rem;
  border-style: solid;
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 0.875rem;
  border-color: #5CABF7;
  border-width: 2px 2px 2px 8px;
}

.emotion-2 {
  display: inline-block;
  height: 20px;
  width: 20px;
  min-height: 20px;
  min-width: 20px;
}

.emotion-2 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-3 {
  padding-left: 0.75rem;
}

<div
    class="emotion-0"
  >
    <div
      aria-live="polite"
      class="emotion-1"
      kind="information"
    >
      <span
        aria-hidden="true"
        class="emotion-2"
      >
        <svg
          viewBox="0 0 33 33"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M16.961 32.338c-8.836 0-16-7.163-16-16 0-8.836 7.164-16 16-16 8.837 0 16 7.164 16 16 0 8.837-7.163 16-16 16zm.19-20.969c1.321 0 2.31-.993 2.31-2.32 0-1.32-.991-2.31-2.31-2.31-1.32 0-2.3.99-2.3 2.31 0 1.327.978 2.32 2.3 2.32zm-1.915 13.827h3.84V13.628h-3.84v11.568z"
            fill="#5CABF7"
            fill-rule="evenodd"
          />
        </svg>
      </span>
      <span
        class="emotion-3"
      />
    </div>
  </div>
</DocumentFragment>
`;

exports[`<Notification /> notification snapshot tests renders information state correctly BananaRepublicFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  transition-property: opacity;
  transition-duration: 250ms;
  transition-timing-function: cubic-bezier(0.25, 0, 0.25, 1);
  transition-delay: 0ms;
  opacity: 1;
}

.emotion-1 {
  line-height: 1.25;
  padding: 0.5rem;
  margin-bottom: 1.125rem;
  border-style: solid;
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  font-family: 'Lynstone',Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 0.8125rem;
  border-color: #5CABF7;
  border-width: 2px 2px 2px 8px;
}

.emotion-2 {
  display: inline-block;
  height: 20px;
  width: 20px;
  min-height: 20px;
  min-width: 20px;
}

.emotion-2 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-3 {
  padding-left: 0.75rem;
}

<div
    class="emotion-0"
  >
    <div
      aria-live="polite"
      class="emotion-1"
      kind="information"
    >
      <span
        aria-hidden="true"
        class="emotion-2"
      >
        <svg
          viewBox="0 0 33 33"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M16.961 32.338c-8.836 0-16-7.163-16-16 0-8.836 7.164-16 16-16 8.837 0 16 7.164 16 16 0 8.837-7.163 16-16 16zm.19-20.969c1.321 0 2.31-.993 2.31-2.32 0-1.32-.991-2.31-2.31-2.31-1.32 0-2.3.99-2.3 2.31 0 1.327.978 2.32 2.3 2.32zm-1.915 13.827h3.84V13.628h-3.84v11.568z"
            fill="#5CABF7"
            fill-rule="evenodd"
          />
        </svg>
      </span>
      <span
        class="emotion-3"
      />
    </div>
  </div>
</DocumentFragment>
`;

exports[`<Notification /> notification snapshot tests renders information state correctly BananaRepublicFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  transition-property: opacity;
  transition-duration: 250ms;
  transition-timing-function: cubic-bezier(0.25, 0, 0.25, 1);
  transition-delay: 0ms;
  opacity: 1;
}

.emotion-1 {
  line-height: 1.25;
  padding: 0.5rem;
  margin-bottom: 1.125rem;
  border-style: solid;
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 0.875rem;
  border-color: #5CABF7;
  border-width: 2px 2px 2px 8px;
}

.emotion-2 {
  display: inline-block;
  height: 20px;
  width: 20px;
  min-height: 20px;
  min-width: 20px;
}

.emotion-2 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-3 {
  padding-left: 0.75rem;
}

<div
    class="emotion-0"
  >
    <div
      aria-live="polite"
      class="emotion-1"
      kind="information"
    >
      <span
        aria-hidden="true"
        class="emotion-2"
      >
        <svg
          viewBox="0 0 33 33"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M16.961 32.338c-8.836 0-16-7.163-16-16 0-8.836 7.164-16 16-16 8.837 0 16 7.164 16 16 0 8.837-7.163 16-16 16zm.19-20.969c1.321 0 2.31-.993 2.31-2.32 0-1.32-.991-2.31-2.31-2.31-1.32 0-2.3.99-2.3 2.31 0 1.327.978 2.32 2.3 2.32zm-1.915 13.827h3.84V13.628h-3.84v11.568z"
            fill="#5CABF7"
            fill-rule="evenodd"
          />
        </svg>
      </span>
      <span
        class="emotion-3"
      />
    </div>
  </div>
</DocumentFragment>
`;

exports[`<Notification /> notification snapshot tests renders information state correctly Gap 1`] = `
<DocumentFragment>
  .emotion-0 {
  transition-property: opacity;
  transition-duration: 250ms;
  transition-timing-function: cubic-bezier(0.25, 0, 0.25, 1);
  transition-delay: 0ms;
  opacity: 1;
}

.emotion-1 {
  line-height: 1.25;
  padding: 0.5rem;
  margin-bottom: 1.125rem;
  border-style: solid;
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 0.8125rem;
  border-color: #5CABF7;
  border-width: 2px 2px 2px 8px;
}

.emotion-2 {
  display: inline-block;
  height: 20px;
  width: 20px;
  min-height: 20px;
  min-width: 20px;
}

.emotion-2 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-3 {
  padding-left: 0.75rem;
}

<div
    class="emotion-0"
  >
    <div
      aria-live="polite"
      class="emotion-1"
      kind="information"
    >
      <span
        aria-hidden="true"
        class="emotion-2"
      >
        <svg
          viewBox="0 0 33 33"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M16.961 32.338c-8.836 0-16-7.163-16-16 0-8.836 7.164-16 16-16 8.837 0 16 7.164 16 16 0 8.837-7.163 16-16 16zm.19-20.969c1.321 0 2.31-.993 2.31-2.32 0-1.32-.991-2.31-2.31-2.31-1.32 0-2.3.99-2.3 2.31 0 1.327.978 2.32 2.3 2.32zm-1.915 13.827h3.84V13.628h-3.84v11.568z"
            fill="#5CABF7"
            fill-rule="evenodd"
          />
        </svg>
      </span>
      <span
        class="emotion-3"
      />
    </div>
  </div>
</DocumentFragment>
`;

exports[`<Notification /> notification snapshot tests renders information state correctly Gap in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  transition-property: opacity;
  transition-duration: 250ms;
  transition-timing-function: cubic-bezier(0.25, 0, 0.25, 1);
  transition-delay: 0ms;
  opacity: 1;
}

.emotion-1 {
  line-height: 1.25;
  padding: 0.5rem;
  margin-bottom: 1.125rem;
  border-style: solid;
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 0.875rem;
  border-color: #5CABF7;
  border-width: 2px 2px 2px 8px;
}

.emotion-2 {
  display: inline-block;
  height: 20px;
  width: 20px;
  min-height: 20px;
  min-width: 20px;
}

.emotion-2 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-3 {
  padding-left: 0.75rem;
}

<div
    class="emotion-0"
  >
    <div
      aria-live="polite"
      class="emotion-1"
      kind="information"
    >
      <span
        aria-hidden="true"
        class="emotion-2"
      >
        <svg
          viewBox="0 0 33 33"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M16.961 32.338c-8.836 0-16-7.163-16-16 0-8.836 7.164-16 16-16 8.837 0 16 7.164 16 16 0 8.837-7.163 16-16 16zm.19-20.969c1.321 0 2.31-.993 2.31-2.32 0-1.32-.991-2.31-2.31-2.31-1.32 0-2.3.99-2.3 2.31 0 1.327.978 2.32 2.3 2.32zm-1.915 13.827h3.84V13.628h-3.84v11.568z"
            fill="#5CABF7"
            fill-rule="evenodd"
          />
        </svg>
      </span>
      <span
        class="emotion-3"
      />
    </div>
  </div>
</DocumentFragment>
`;

exports[`<Notification /> notification snapshot tests renders information state correctly GapFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  transition-property: opacity;
  transition-duration: 250ms;
  transition-timing-function: cubic-bezier(0.25, 0, 0.25, 1);
  transition-delay: 0ms;
  opacity: 1;
}

.emotion-1 {
  line-height: 1.25;
  padding: 0.5rem;
  margin-bottom: 1.125rem;
  border-style: solid;
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 0.8125rem;
  border-color: #5CABF7;
  border-width: 2px 2px 2px 8px;
}

.emotion-2 {
  display: inline-block;
  height: 20px;
  width: 20px;
  min-height: 20px;
  min-width: 20px;
}

.emotion-2 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-3 {
  padding-left: 0.75rem;
}

<div
    class="emotion-0"
  >
    <div
      aria-live="polite"
      class="emotion-1"
      kind="information"
    >
      <span
        aria-hidden="true"
        class="emotion-2"
      >
        <svg
          viewBox="0 0 33 33"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M16.961 32.338c-8.836 0-16-7.163-16-16 0-8.836 7.164-16 16-16 8.837 0 16 7.164 16 16 0 8.837-7.163 16-16 16zm.19-20.969c1.321 0 2.31-.993 2.31-2.32 0-1.32-.991-2.31-2.31-2.31-1.32 0-2.3.99-2.3 2.31 0 1.327.978 2.32 2.3 2.32zm-1.915 13.827h3.84V13.628h-3.84v11.568z"
            fill="#5CABF7"
            fill-rule="evenodd"
          />
        </svg>
      </span>
      <span
        class="emotion-3"
      />
    </div>
  </div>
</DocumentFragment>
`;

exports[`<Notification /> notification snapshot tests renders information state correctly GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  transition-property: opacity;
  transition-duration: 250ms;
  transition-timing-function: cubic-bezier(0.25, 0, 0.25, 1);
  transition-delay: 0ms;
  opacity: 1;
}

.emotion-1 {
  line-height: 1.25;
  padding: 0.5rem;
  margin-bottom: 1.125rem;
  border-style: solid;
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 0.875rem;
  border-color: #5CABF7;
  border-width: 2px 2px 2px 8px;
}

.emotion-2 {
  display: inline-block;
  height: 20px;
  width: 20px;
  min-height: 20px;
  min-width: 20px;
}

.emotion-2 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-3 {
  padding-left: 0.75rem;
}

<div
    class="emotion-0"
  >
    <div
      aria-live="polite"
      class="emotion-1"
      kind="information"
    >
      <span
        aria-hidden="true"
        class="emotion-2"
      >
        <svg
          viewBox="0 0 33 33"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M16.961 32.338c-8.836 0-16-7.163-16-16 0-8.836 7.164-16 16-16 8.837 0 16 7.164 16 16 0 8.837-7.163 16-16 16zm.19-20.969c1.321 0 2.31-.993 2.31-2.32 0-1.32-.991-2.31-2.31-2.31-1.32 0-2.3.99-2.3 2.31 0 1.327.978 2.32 2.3 2.32zm-1.915 13.827h3.84V13.628h-3.84v11.568z"
            fill="#5CABF7"
            fill-rule="evenodd"
          />
        </svg>
      </span>
      <span
        class="emotion-3"
      />
    </div>
  </div>
</DocumentFragment>
`;

exports[`<Notification /> notification snapshot tests renders information state correctly OldNavy 1`] = `
<DocumentFragment>
  .emotion-0 {
  transition-property: opacity;
  transition-duration: 250ms;
  transition-timing-function: cubic-bezier(0.25, 0, 0.25, 1);
  transition-delay: 0ms;
  opacity: 1;
}

.emotion-1 {
  line-height: 1.25;
  padding: 0.5rem;
  margin-bottom: 1.125rem;
  border-style: solid;
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  font-family: 'Gotham','Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 0.875rem;
  border-color: #5CABF7;
  border-width: 2px 2px 2px 8px;
}

.emotion-2 {
  display: inline-block;
  height: 20px;
  width: 20px;
  min-height: 20px;
  min-width: 20px;
}

.emotion-2 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-3 {
  padding-left: 0.75rem;
}

<div
    class="emotion-0"
  >
    <div
      aria-live="polite"
      class="emotion-1"
      kind="information"
    >
      <span
        aria-hidden="true"
        class="emotion-2"
      >
        <svg
          viewBox="0 0 33 33"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M16.961 32.338c-8.836 0-16-7.163-16-16 0-8.836 7.164-16 16-16 8.837 0 16 7.164 16 16 0 8.837-7.163 16-16 16zm.19-20.969c1.321 0 2.31-.993 2.31-2.32 0-1.32-.991-2.31-2.31-2.31-1.32 0-2.3.99-2.3 2.31 0 1.327.978 2.32 2.3 2.32zm-1.915 13.827h3.84V13.628h-3.84v11.568z"
            fill="#5CABF7"
            fill-rule="evenodd"
          />
        </svg>
      </span>
      <span
        class="emotion-3"
      />
    </div>
  </div>
</DocumentFragment>
`;

exports[`<Notification /> notification snapshot tests renders information state correctly OldNavy in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  transition-property: opacity;
  transition-duration: 250ms;
  transition-timing-function: cubic-bezier(0.25, 0, 0.25, 1);
  transition-delay: 0ms;
  opacity: 1;
}

.emotion-1 {
  line-height: 1.25;
  padding: 0.5rem;
  margin-bottom: 1.125rem;
  border-style: solid;
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 0.875rem;
  border-color: #5CABF7;
  border-width: 2px 2px 2px 8px;
}

.emotion-2 {
  display: inline-block;
  height: 20px;
  width: 20px;
  min-height: 20px;
  min-width: 20px;
}

.emotion-2 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-3 {
  padding-left: 0.75rem;
}

<div
    class="emotion-0"
  >
    <div
      aria-live="polite"
      class="emotion-1"
      kind="information"
    >
      <span
        aria-hidden="true"
        class="emotion-2"
      >
        <svg
          viewBox="0 0 33 33"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M16.961 32.338c-8.836 0-16-7.163-16-16 0-8.836 7.164-16 16-16 8.837 0 16 7.164 16 16 0 8.837-7.163 16-16 16zm.19-20.969c1.321 0 2.31-.993 2.31-2.32 0-1.32-.991-2.31-2.31-2.31-1.32 0-2.3.99-2.3 2.31 0 1.327.978 2.32 2.3 2.32zm-1.915 13.827h3.84V13.628h-3.84v11.568z"
            fill="#5CABF7"
            fill-rule="evenodd"
          />
        </svg>
      </span>
      <span
        class="emotion-3"
      />
    </div>
  </div>
</DocumentFragment>
`;

exports[`<Notification /> notification snapshot tests renders inverse error state correctly Athleta 1`] = `
<DocumentFragment>
  .emotion-0 {
  transition-property: opacity;
  transition-duration: 250ms;
  transition-timing-function: cubic-bezier(0.25, 0, 0.25, 1);
  transition-delay: 0ms;
  opacity: 1;
}

.emotion-1 {
  line-height: 1.25;
  padding: 0.5rem;
  margin-bottom: 1.125rem;
  border-style: solid;
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  font-family: 'Avenir Next','Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 0.875rem;
  color: #FFFFFF;
  border-color: #FD6565;
  border-width: 2px 2px 2px 8px;
}

.emotion-2 {
  display: inline-block;
  height: 20px;
  width: 20px;
  min-height: 20px;
  min-width: 20px;
}

.emotion-2 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-2 svg path {
  fill: #FD6565;
}

.emotion-2 svg rect {
  fill: #FD6565;
}

.emotion-3 {
  padding-left: 0.75rem;
}

<div
    class="emotion-0"
  >
    <div
      aria-live="assertive"
      class="emotion-1"
      kind="error"
    >
      <span
        aria-hidden="true"
        class="emotion-2"
      >
        <svg
          viewBox="0 0 33 33"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M16.039.661c8.836 0 16 7.164 16 16 0 8.837-7.164 16-16 16-8.837 0-16-7.163-16-16 0-8.836 7.163-16 16-16zm-.19 20.97c-1.321 0-2.31.993-2.31 2.32 0 1.32.991 2.31 2.31 2.31 1.32 0 2.3-.99 2.3-2.31 0-1.327-.978-2.32-2.3-2.32zm1.915-13.827h-3.84v11.568h3.84V7.804z"
            fill="#D00000"
            fill-rule="evenodd"
          />
        </svg>
      </span>
      <span
        class="emotion-3"
      />
    </div>
  </div>
</DocumentFragment>
`;

exports[`<Notification /> notification snapshot tests renders inverse error state correctly Athleta in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  transition-property: opacity;
  transition-duration: 250ms;
  transition-timing-function: cubic-bezier(0.25, 0, 0.25, 1);
  transition-delay: 0ms;
  opacity: 1;
}

.emotion-1 {
  line-height: 1.25;
  padding: 0.5rem;
  margin-bottom: 1.125rem;
  border-style: solid;
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 0.875rem;
  color: #FFFFFF;
  border-color: #FD6565;
  border-width: 2px 2px 2px 8px;
}

.emotion-2 {
  display: inline-block;
  height: 20px;
  width: 20px;
  min-height: 20px;
  min-width: 20px;
}

.emotion-2 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-2 svg path {
  fill: #FD6565;
}

.emotion-2 svg rect {
  fill: #FD6565;
}

.emotion-3 {
  padding-left: 0.75rem;
}

<div
    class="emotion-0"
  >
    <div
      aria-live="assertive"
      class="emotion-1"
      kind="error"
    >
      <span
        aria-hidden="true"
        class="emotion-2"
      >
        <svg
          viewBox="0 0 33 33"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M16.039.661c8.836 0 16 7.164 16 16 0 8.837-7.164 16-16 16-8.837 0-16-7.163-16-16 0-8.836 7.163-16 16-16zm-.19 20.97c-1.321 0-2.31.993-2.31 2.32 0 1.32.991 2.31 2.31 2.31 1.32 0 2.3-.99 2.3-2.31 0-1.327-.978-2.32-2.3-2.32zm1.915-13.827h-3.84v11.568h3.84V7.804z"
            fill="#D00000"
            fill-rule="evenodd"
          />
        </svg>
      </span>
      <span
        class="emotion-3"
      />
    </div>
  </div>
</DocumentFragment>
`;

exports[`<Notification /> notification snapshot tests renders inverse error state correctly BananaRepublic 1`] = `
<DocumentFragment>
  .emotion-0 {
  transition-property: opacity;
  transition-duration: 250ms;
  transition-timing-function: cubic-bezier(0.25, 0, 0.25, 1);
  transition-delay: 0ms;
  opacity: 1;
}

.emotion-1 {
  line-height: 1.25;
  padding: 0.5rem;
  margin-bottom: 1.125rem;
  border-style: solid;
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  font-family: 'Lynstone',Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 0.8125rem;
  color: #F6F4EB;
  border-color: #FD6565;
  border-width: 2px 2px 2px 8px;
}

.emotion-2 {
  display: inline-block;
  height: 20px;
  width: 20px;
  min-height: 20px;
  min-width: 20px;
}

.emotion-2 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-2 svg path {
  fill: #FD6565;
}

.emotion-2 svg rect {
  fill: #FD6565;
}

.emotion-3 {
  padding-left: 0.75rem;
}

<div
    class="emotion-0"
  >
    <div
      aria-live="assertive"
      class="emotion-1"
      kind="error"
    >
      <span
        aria-hidden="true"
        class="emotion-2"
      >
        <svg
          viewBox="0 0 33 33"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M16.039.661c8.836 0 16 7.164 16 16 0 8.837-7.164 16-16 16-8.837 0-16-7.163-16-16 0-8.836 7.163-16 16-16zm-.19 20.97c-1.321 0-2.31.993-2.31 2.32 0 1.32.991 2.31 2.31 2.31 1.32 0 2.3-.99 2.3-2.31 0-1.327-.978-2.32-2.3-2.32zm1.915-13.827h-3.84v11.568h3.84V7.804z"
            fill="#D00000"
            fill-rule="evenodd"
          />
        </svg>
      </span>
      <span
        class="emotion-3"
      />
    </div>
  </div>
</DocumentFragment>
`;

exports[`<Notification /> notification snapshot tests renders inverse error state correctly BananaRepublic in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  transition-property: opacity;
  transition-duration: 250ms;
  transition-timing-function: cubic-bezier(0.25, 0, 0.25, 1);
  transition-delay: 0ms;
  opacity: 1;
}

.emotion-1 {
  line-height: 1.25;
  padding: 0.5rem;
  margin-bottom: 1.125rem;
  border-style: solid;
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 0.875rem;
  color: #F6F4EB;
  border-color: #FD6565;
  border-width: 2px 2px 2px 8px;
}

.emotion-2 {
  display: inline-block;
  height: 20px;
  width: 20px;
  min-height: 20px;
  min-width: 20px;
}

.emotion-2 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-2 svg path {
  fill: #FD6565;
}

.emotion-2 svg rect {
  fill: #FD6565;
}

.emotion-3 {
  padding-left: 0.75rem;
}

<div
    class="emotion-0"
  >
    <div
      aria-live="assertive"
      class="emotion-1"
      kind="error"
    >
      <span
        aria-hidden="true"
        class="emotion-2"
      >
        <svg
          viewBox="0 0 33 33"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M16.039.661c8.836 0 16 7.164 16 16 0 8.837-7.164 16-16 16-8.837 0-16-7.163-16-16 0-8.836 7.163-16 16-16zm-.19 20.97c-1.321 0-2.31.993-2.31 2.32 0 1.32.991 2.31 2.31 2.31 1.32 0 2.3-.99 2.3-2.31 0-1.327-.978-2.32-2.3-2.32zm1.915-13.827h-3.84v11.568h3.84V7.804z"
            fill="#D00000"
            fill-rule="evenodd"
          />
        </svg>
      </span>
      <span
        class="emotion-3"
      />
    </div>
  </div>
</DocumentFragment>
`;

exports[`<Notification /> notification snapshot tests renders inverse error state correctly BananaRepublicFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  transition-property: opacity;
  transition-duration: 250ms;
  transition-timing-function: cubic-bezier(0.25, 0, 0.25, 1);
  transition-delay: 0ms;
  opacity: 1;
}

.emotion-1 {
  line-height: 1.25;
  padding: 0.5rem;
  margin-bottom: 1.125rem;
  border-style: solid;
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  font-family: 'Lynstone',Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 0.8125rem;
  color: #F6F4EB;
  border-color: #FD6565;
  border-width: 2px 2px 2px 8px;
}

.emotion-2 {
  display: inline-block;
  height: 20px;
  width: 20px;
  min-height: 20px;
  min-width: 20px;
}

.emotion-2 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-2 svg path {
  fill: #FD6565;
}

.emotion-2 svg rect {
  fill: #FD6565;
}

.emotion-3 {
  padding-left: 0.75rem;
}

<div
    class="emotion-0"
  >
    <div
      aria-live="assertive"
      class="emotion-1"
      kind="error"
    >
      <span
        aria-hidden="true"
        class="emotion-2"
      >
        <svg
          viewBox="0 0 33 33"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M16.039.661c8.836 0 16 7.164 16 16 0 8.837-7.164 16-16 16-8.837 0-16-7.163-16-16 0-8.836 7.163-16 16-16zm-.19 20.97c-1.321 0-2.31.993-2.31 2.32 0 1.32.991 2.31 2.31 2.31 1.32 0 2.3-.99 2.3-2.31 0-1.327-.978-2.32-2.3-2.32zm1.915-13.827h-3.84v11.568h3.84V7.804z"
            fill="#D00000"
            fill-rule="evenodd"
          />
        </svg>
      </span>
      <span
        class="emotion-3"
      />
    </div>
  </div>
</DocumentFragment>
`;

exports[`<Notification /> notification snapshot tests renders inverse error state correctly BananaRepublicFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  transition-property: opacity;
  transition-duration: 250ms;
  transition-timing-function: cubic-bezier(0.25, 0, 0.25, 1);
  transition-delay: 0ms;
  opacity: 1;
}

.emotion-1 {
  line-height: 1.25;
  padding: 0.5rem;
  margin-bottom: 1.125rem;
  border-style: solid;
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 0.875rem;
  color: #F6F4EB;
  border-color: #FD6565;
  border-width: 2px 2px 2px 8px;
}

.emotion-2 {
  display: inline-block;
  height: 20px;
  width: 20px;
  min-height: 20px;
  min-width: 20px;
}

.emotion-2 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-2 svg path {
  fill: #FD6565;
}

.emotion-2 svg rect {
  fill: #FD6565;
}

.emotion-3 {
  padding-left: 0.75rem;
}

<div
    class="emotion-0"
  >
    <div
      aria-live="assertive"
      class="emotion-1"
      kind="error"
    >
      <span
        aria-hidden="true"
        class="emotion-2"
      >
        <svg
          viewBox="0 0 33 33"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M16.039.661c8.836 0 16 7.164 16 16 0 8.837-7.164 16-16 16-8.837 0-16-7.163-16-16 0-8.836 7.163-16 16-16zm-.19 20.97c-1.321 0-2.31.993-2.31 2.32 0 1.32.991 2.31 2.31 2.31 1.32 0 2.3-.99 2.3-2.31 0-1.327-.978-2.32-2.3-2.32zm1.915-13.827h-3.84v11.568h3.84V7.804z"
            fill="#D00000"
            fill-rule="evenodd"
          />
        </svg>
      </span>
      <span
        class="emotion-3"
      />
    </div>
  </div>
</DocumentFragment>
`;

exports[`<Notification /> notification snapshot tests renders inverse error state correctly Gap 1`] = `
<DocumentFragment>
  .emotion-0 {
  transition-property: opacity;
  transition-duration: 250ms;
  transition-timing-function: cubic-bezier(0.25, 0, 0.25, 1);
  transition-delay: 0ms;
  opacity: 1;
}

.emotion-1 {
  line-height: 1.25;
  padding: 0.5rem;
  margin-bottom: 1.125rem;
  border-style: solid;
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 0.8125rem;
  color: #FFFFFF;
  border-color: #FD6565;
  border-width: 2px 2px 2px 8px;
}

.emotion-2 {
  display: inline-block;
  height: 20px;
  width: 20px;
  min-height: 20px;
  min-width: 20px;
}

.emotion-2 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-2 svg path {
  fill: #FD6565;
}

.emotion-2 svg rect {
  fill: #FD6565;
}

.emotion-3 {
  padding-left: 0.75rem;
}

<div
    class="emotion-0"
  >
    <div
      aria-live="assertive"
      class="emotion-1"
      kind="error"
    >
      <span
        aria-hidden="true"
        class="emotion-2"
      >
        <svg
          viewBox="0 0 33 33"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M16.039.661c8.836 0 16 7.164 16 16 0 8.837-7.164 16-16 16-8.837 0-16-7.163-16-16 0-8.836 7.163-16 16-16zm-.19 20.97c-1.321 0-2.31.993-2.31 2.32 0 1.32.991 2.31 2.31 2.31 1.32 0 2.3-.99 2.3-2.31 0-1.327-.978-2.32-2.3-2.32zm1.915-13.827h-3.84v11.568h3.84V7.804z"
            fill="#D00000"
            fill-rule="evenodd"
          />
        </svg>
      </span>
      <span
        class="emotion-3"
      />
    </div>
  </div>
</DocumentFragment>
`;

exports[`<Notification /> notification snapshot tests renders inverse error state correctly Gap in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  transition-property: opacity;
  transition-duration: 250ms;
  transition-timing-function: cubic-bezier(0.25, 0, 0.25, 1);
  transition-delay: 0ms;
  opacity: 1;
}

.emotion-1 {
  line-height: 1.25;
  padding: 0.5rem;
  margin-bottom: 1.125rem;
  border-style: solid;
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 0.875rem;
  color: #FFFFFF;
  border-color: #FD6565;
  border-width: 2px 2px 2px 8px;
}

.emotion-2 {
  display: inline-block;
  height: 20px;
  width: 20px;
  min-height: 20px;
  min-width: 20px;
}

.emotion-2 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-2 svg path {
  fill: #FD6565;
}

.emotion-2 svg rect {
  fill: #FD6565;
}

.emotion-3 {
  padding-left: 0.75rem;
}

<div
    class="emotion-0"
  >
    <div
      aria-live="assertive"
      class="emotion-1"
      kind="error"
    >
      <span
        aria-hidden="true"
        class="emotion-2"
      >
        <svg
          viewBox="0 0 33 33"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M16.039.661c8.836 0 16 7.164 16 16 0 8.837-7.164 16-16 16-8.837 0-16-7.163-16-16 0-8.836 7.163-16 16-16zm-.19 20.97c-1.321 0-2.31.993-2.31 2.32 0 1.32.991 2.31 2.31 2.31 1.32 0 2.3-.99 2.3-2.31 0-1.327-.978-2.32-2.3-2.32zm1.915-13.827h-3.84v11.568h3.84V7.804z"
            fill="#D00000"
            fill-rule="evenodd"
          />
        </svg>
      </span>
      <span
        class="emotion-3"
      />
    </div>
  </div>
</DocumentFragment>
`;

exports[`<Notification /> notification snapshot tests renders inverse error state correctly GapFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  transition-property: opacity;
  transition-duration: 250ms;
  transition-timing-function: cubic-bezier(0.25, 0, 0.25, 1);
  transition-delay: 0ms;
  opacity: 1;
}

.emotion-1 {
  line-height: 1.25;
  padding: 0.5rem;
  margin-bottom: 1.125rem;
  border-style: solid;
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 0.8125rem;
  color: #FFFFFF;
  border-color: #FD6565;
  border-width: 2px 2px 2px 8px;
}

.emotion-2 {
  display: inline-block;
  height: 20px;
  width: 20px;
  min-height: 20px;
  min-width: 20px;
}

.emotion-2 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-2 svg path {
  fill: #FD6565;
}

.emotion-2 svg rect {
  fill: #FD6565;
}

.emotion-3 {
  padding-left: 0.75rem;
}

<div
    class="emotion-0"
  >
    <div
      aria-live="assertive"
      class="emotion-1"
      kind="error"
    >
      <span
        aria-hidden="true"
        class="emotion-2"
      >
        <svg
          viewBox="0 0 33 33"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M16.039.661c8.836 0 16 7.164 16 16 0 8.837-7.164 16-16 16-8.837 0-16-7.163-16-16 0-8.836 7.163-16 16-16zm-.19 20.97c-1.321 0-2.31.993-2.31 2.32 0 1.32.991 2.31 2.31 2.31 1.32 0 2.3-.99 2.3-2.31 0-1.327-.978-2.32-2.3-2.32zm1.915-13.827h-3.84v11.568h3.84V7.804z"
            fill="#D00000"
            fill-rule="evenodd"
          />
        </svg>
      </span>
      <span
        class="emotion-3"
      />
    </div>
  </div>
</DocumentFragment>
`;

exports[`<Notification /> notification snapshot tests renders inverse error state correctly GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  transition-property: opacity;
  transition-duration: 250ms;
  transition-timing-function: cubic-bezier(0.25, 0, 0.25, 1);
  transition-delay: 0ms;
  opacity: 1;
}

.emotion-1 {
  line-height: 1.25;
  padding: 0.5rem;
  margin-bottom: 1.125rem;
  border-style: solid;
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 0.875rem;
  color: #FFFFFF;
  border-color: #FD6565;
  border-width: 2px 2px 2px 8px;
}

.emotion-2 {
  display: inline-block;
  height: 20px;
  width: 20px;
  min-height: 20px;
  min-width: 20px;
}

.emotion-2 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-2 svg path {
  fill: #FD6565;
}

.emotion-2 svg rect {
  fill: #FD6565;
}

.emotion-3 {
  padding-left: 0.75rem;
}

<div
    class="emotion-0"
  >
    <div
      aria-live="assertive"
      class="emotion-1"
      kind="error"
    >
      <span
        aria-hidden="true"
        class="emotion-2"
      >
        <svg
          viewBox="0 0 33 33"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M16.039.661c8.836 0 16 7.164 16 16 0 8.837-7.164 16-16 16-8.837 0-16-7.163-16-16 0-8.836 7.163-16 16-16zm-.19 20.97c-1.321 0-2.31.993-2.31 2.32 0 1.32.991 2.31 2.31 2.31 1.32 0 2.3-.99 2.3-2.31 0-1.327-.978-2.32-2.3-2.32zm1.915-13.827h-3.84v11.568h3.84V7.804z"
            fill="#D00000"
            fill-rule="evenodd"
          />
        </svg>
      </span>
      <span
        class="emotion-3"
      />
    </div>
  </div>
</DocumentFragment>
`;

exports[`<Notification /> notification snapshot tests renders inverse error state correctly OldNavy 1`] = `
<DocumentFragment>
  .emotion-0 {
  transition-property: opacity;
  transition-duration: 250ms;
  transition-timing-function: cubic-bezier(0.25, 0, 0.25, 1);
  transition-delay: 0ms;
  opacity: 1;
}

.emotion-1 {
  line-height: 1.25;
  padding: 0.5rem;
  margin-bottom: 1.125rem;
  border-style: solid;
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  font-family: 'Gotham','Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 0.875rem;
  color: #FFFFFF;
  border-color: #FD6565;
  border-width: 2px 2px 2px 8px;
}

.emotion-2 {
  display: inline-block;
  height: 20px;
  width: 20px;
  min-height: 20px;
  min-width: 20px;
}

.emotion-2 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-2 svg path {
  fill: #FD6565;
}

.emotion-2 svg rect {
  fill: #FD6565;
}

.emotion-3 {
  padding-left: 0.75rem;
}

<div
    class="emotion-0"
  >
    <div
      aria-live="assertive"
      class="emotion-1"
      kind="error"
    >
      <span
        aria-hidden="true"
        class="emotion-2"
      >
        <svg
          viewBox="0 0 33 33"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M16.039.661c8.836 0 16 7.164 16 16 0 8.837-7.164 16-16 16-8.837 0-16-7.163-16-16 0-8.836 7.163-16 16-16zm-.19 20.97c-1.321 0-2.31.993-2.31 2.32 0 1.32.991 2.31 2.31 2.31 1.32 0 2.3-.99 2.3-2.31 0-1.327-.978-2.32-2.3-2.32zm1.915-13.827h-3.84v11.568h3.84V7.804z"
            fill="#D00000"
            fill-rule="evenodd"
          />
        </svg>
      </span>
      <span
        class="emotion-3"
      />
    </div>
  </div>
</DocumentFragment>
`;

exports[`<Notification /> notification snapshot tests renders inverse error state correctly OldNavy in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  transition-property: opacity;
  transition-duration: 250ms;
  transition-timing-function: cubic-bezier(0.25, 0, 0.25, 1);
  transition-delay: 0ms;
  opacity: 1;
}

.emotion-1 {
  line-height: 1.25;
  padding: 0.5rem;
  margin-bottom: 1.125rem;
  border-style: solid;
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 0.875rem;
  color: #FFFFFF;
  border-color: #FD6565;
  border-width: 2px 2px 2px 8px;
}

.emotion-2 {
  display: inline-block;
  height: 20px;
  width: 20px;
  min-height: 20px;
  min-width: 20px;
}

.emotion-2 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-2 svg path {
  fill: #FD6565;
}

.emotion-2 svg rect {
  fill: #FD6565;
}

.emotion-3 {
  padding-left: 0.75rem;
}

<div
    class="emotion-0"
  >
    <div
      aria-live="assertive"
      class="emotion-1"
      kind="error"
    >
      <span
        aria-hidden="true"
        class="emotion-2"
      >
        <svg
          viewBox="0 0 33 33"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M16.039.661c8.836 0 16 7.164 16 16 0 8.837-7.164 16-16 16-8.837 0-16-7.163-16-16 0-8.836 7.163-16 16-16zm-.19 20.97c-1.321 0-2.31.993-2.31 2.32 0 1.32.991 2.31 2.31 2.31 1.32 0 2.3-.99 2.3-2.31 0-1.327-.978-2.32-2.3-2.32zm1.915-13.827h-3.84v11.568h3.84V7.804z"
            fill="#D00000"
            fill-rule="evenodd"
          />
        </svg>
      </span>
      <span
        class="emotion-3"
      />
    </div>
  </div>
</DocumentFragment>
`;

exports[`<Notification /> notification snapshot tests renders inverse information state correctly Athleta 1`] = `
<DocumentFragment>
  .emotion-0 {
  transition-property: opacity;
  transition-duration: 250ms;
  transition-timing-function: cubic-bezier(0.25, 0, 0.25, 1);
  transition-delay: 0ms;
  opacity: 1;
}

.emotion-1 {
  line-height: 1.25;
  padding: 0.5rem;
  margin-bottom: 1.125rem;
  border-style: solid;
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  font-family: 'Avenir Next','Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 0.875rem;
  color: #FFFFFF;
  border-color: #5DABF5;
  border-width: 2px 2px 2px 8px;
}

.emotion-2 {
  display: inline-block;
  height: 20px;
  width: 20px;
  min-height: 20px;
  min-width: 20px;
}

.emotion-2 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-2 svg path {
  fill: #5DABF5;
}

.emotion-2 svg rect {
  fill: #5DABF5;
}

.emotion-3 {
  padding-left: 0.75rem;
}

<div
    class="emotion-0"
  >
    <div
      aria-live="polite"
      class="emotion-1"
      kind="information"
    >
      <span
        aria-hidden="true"
        class="emotion-2"
      >
        <svg
          viewBox="0 0 33 33"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M16.961 32.338c-8.836 0-16-7.163-16-16 0-8.836 7.164-16 16-16 8.837 0 16 7.164 16 16 0 8.837-7.163 16-16 16zm.19-20.969c1.321 0 2.31-.993 2.31-2.32 0-1.32-.991-2.31-2.31-2.31-1.32 0-2.3.99-2.3 2.31 0 1.327.978 2.32 2.3 2.32zm-1.915 13.827h3.84V13.628h-3.84v11.568z"
            fill="#5CABF7"
            fill-rule="evenodd"
          />
        </svg>
      </span>
      <span
        class="emotion-3"
      />
    </div>
  </div>
</DocumentFragment>
`;

exports[`<Notification /> notification snapshot tests renders inverse information state correctly Athleta in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  transition-property: opacity;
  transition-duration: 250ms;
  transition-timing-function: cubic-bezier(0.25, 0, 0.25, 1);
  transition-delay: 0ms;
  opacity: 1;
}

.emotion-1 {
  line-height: 1.25;
  padding: 0.5rem;
  margin-bottom: 1.125rem;
  border-style: solid;
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 0.875rem;
  color: #FFFFFF;
  border-color: #5DABF5;
  border-width: 2px 2px 2px 8px;
}

.emotion-2 {
  display: inline-block;
  height: 20px;
  width: 20px;
  min-height: 20px;
  min-width: 20px;
}

.emotion-2 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-2 svg path {
  fill: #5DABF5;
}

.emotion-2 svg rect {
  fill: #5DABF5;
}

.emotion-3 {
  padding-left: 0.75rem;
}

<div
    class="emotion-0"
  >
    <div
      aria-live="polite"
      class="emotion-1"
      kind="information"
    >
      <span
        aria-hidden="true"
        class="emotion-2"
      >
        <svg
          viewBox="0 0 33 33"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M16.961 32.338c-8.836 0-16-7.163-16-16 0-8.836 7.164-16 16-16 8.837 0 16 7.164 16 16 0 8.837-7.163 16-16 16zm.19-20.969c1.321 0 2.31-.993 2.31-2.32 0-1.32-.991-2.31-2.31-2.31-1.32 0-2.3.99-2.3 2.31 0 1.327.978 2.32 2.3 2.32zm-1.915 13.827h3.84V13.628h-3.84v11.568z"
            fill="#5CABF7"
            fill-rule="evenodd"
          />
        </svg>
      </span>
      <span
        class="emotion-3"
      />
    </div>
  </div>
</DocumentFragment>
`;

exports[`<Notification /> notification snapshot tests renders inverse information state correctly BananaRepublic 1`] = `
<DocumentFragment>
  .emotion-0 {
  transition-property: opacity;
  transition-duration: 250ms;
  transition-timing-function: cubic-bezier(0.25, 0, 0.25, 1);
  transition-delay: 0ms;
  opacity: 1;
}

.emotion-1 {
  line-height: 1.25;
  padding: 0.5rem;
  margin-bottom: 1.125rem;
  border-style: solid;
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  font-family: 'Lynstone',Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 0.8125rem;
  color: #F6F4EB;
  border-color: #5DABF5;
  border-width: 2px 2px 2px 8px;
}

.emotion-2 {
  display: inline-block;
  height: 20px;
  width: 20px;
  min-height: 20px;
  min-width: 20px;
}

.emotion-2 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-2 svg path {
  fill: #5DABF5;
}

.emotion-2 svg rect {
  fill: #5DABF5;
}

.emotion-3 {
  padding-left: 0.75rem;
}

<div
    class="emotion-0"
  >
    <div
      aria-live="polite"
      class="emotion-1"
      kind="information"
    >
      <span
        aria-hidden="true"
        class="emotion-2"
      >
        <svg
          viewBox="0 0 33 33"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M16.961 32.338c-8.836 0-16-7.163-16-16 0-8.836 7.164-16 16-16 8.837 0 16 7.164 16 16 0 8.837-7.163 16-16 16zm.19-20.969c1.321 0 2.31-.993 2.31-2.32 0-1.32-.991-2.31-2.31-2.31-1.32 0-2.3.99-2.3 2.31 0 1.327.978 2.32 2.3 2.32zm-1.915 13.827h3.84V13.628h-3.84v11.568z"
            fill="#5CABF7"
            fill-rule="evenodd"
          />
        </svg>
      </span>
      <span
        class="emotion-3"
      />
    </div>
  </div>
</DocumentFragment>
`;

exports[`<Notification /> notification snapshot tests renders inverse information state correctly BananaRepublic in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  transition-property: opacity;
  transition-duration: 250ms;
  transition-timing-function: cubic-bezier(0.25, 0, 0.25, 1);
  transition-delay: 0ms;
  opacity: 1;
}

.emotion-1 {
  line-height: 1.25;
  padding: 0.5rem;
  margin-bottom: 1.125rem;
  border-style: solid;
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 0.875rem;
  color: #F6F4EB;
  border-color: #5DABF5;
  border-width: 2px 2px 2px 8px;
}

.emotion-2 {
  display: inline-block;
  height: 20px;
  width: 20px;
  min-height: 20px;
  min-width: 20px;
}

.emotion-2 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-2 svg path {
  fill: #5DABF5;
}

.emotion-2 svg rect {
  fill: #5DABF5;
}

.emotion-3 {
  padding-left: 0.75rem;
}

<div
    class="emotion-0"
  >
    <div
      aria-live="polite"
      class="emotion-1"
      kind="information"
    >
      <span
        aria-hidden="true"
        class="emotion-2"
      >
        <svg
          viewBox="0 0 33 33"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M16.961 32.338c-8.836 0-16-7.163-16-16 0-8.836 7.164-16 16-16 8.837 0 16 7.164 16 16 0 8.837-7.163 16-16 16zm.19-20.969c1.321 0 2.31-.993 2.31-2.32 0-1.32-.991-2.31-2.31-2.31-1.32 0-2.3.99-2.3 2.31 0 1.327.978 2.32 2.3 2.32zm-1.915 13.827h3.84V13.628h-3.84v11.568z"
            fill="#5CABF7"
            fill-rule="evenodd"
          />
        </svg>
      </span>
      <span
        class="emotion-3"
      />
    </div>
  </div>
</DocumentFragment>
`;

exports[`<Notification /> notification snapshot tests renders inverse information state correctly BananaRepublicFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  transition-property: opacity;
  transition-duration: 250ms;
  transition-timing-function: cubic-bezier(0.25, 0, 0.25, 1);
  transition-delay: 0ms;
  opacity: 1;
}

.emotion-1 {
  line-height: 1.25;
  padding: 0.5rem;
  margin-bottom: 1.125rem;
  border-style: solid;
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  font-family: 'Lynstone',Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 0.8125rem;
  color: #F6F4EB;
  border-color: #5DABF5;
  border-width: 2px 2px 2px 8px;
}

.emotion-2 {
  display: inline-block;
  height: 20px;
  width: 20px;
  min-height: 20px;
  min-width: 20px;
}

.emotion-2 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-2 svg path {
  fill: #5DABF5;
}

.emotion-2 svg rect {
  fill: #5DABF5;
}

.emotion-3 {
  padding-left: 0.75rem;
}

<div
    class="emotion-0"
  >
    <div
      aria-live="polite"
      class="emotion-1"
      kind="information"
    >
      <span
        aria-hidden="true"
        class="emotion-2"
      >
        <svg
          viewBox="0 0 33 33"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M16.961 32.338c-8.836 0-16-7.163-16-16 0-8.836 7.164-16 16-16 8.837 0 16 7.164 16 16 0 8.837-7.163 16-16 16zm.19-20.969c1.321 0 2.31-.993 2.31-2.32 0-1.32-.991-2.31-2.31-2.31-1.32 0-2.3.99-2.3 2.31 0 1.327.978 2.32 2.3 2.32zm-1.915 13.827h3.84V13.628h-3.84v11.568z"
            fill="#5CABF7"
            fill-rule="evenodd"
          />
        </svg>
      </span>
      <span
        class="emotion-3"
      />
    </div>
  </div>
</DocumentFragment>
`;

exports[`<Notification /> notification snapshot tests renders inverse information state correctly BananaRepublicFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  transition-property: opacity;
  transition-duration: 250ms;
  transition-timing-function: cubic-bezier(0.25, 0, 0.25, 1);
  transition-delay: 0ms;
  opacity: 1;
}

.emotion-1 {
  line-height: 1.25;
  padding: 0.5rem;
  margin-bottom: 1.125rem;
  border-style: solid;
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 0.875rem;
  color: #F6F4EB;
  border-color: #5DABF5;
  border-width: 2px 2px 2px 8px;
}

.emotion-2 {
  display: inline-block;
  height: 20px;
  width: 20px;
  min-height: 20px;
  min-width: 20px;
}

.emotion-2 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-2 svg path {
  fill: #5DABF5;
}

.emotion-2 svg rect {
  fill: #5DABF5;
}

.emotion-3 {
  padding-left: 0.75rem;
}

<div
    class="emotion-0"
  >
    <div
      aria-live="polite"
      class="emotion-1"
      kind="information"
    >
      <span
        aria-hidden="true"
        class="emotion-2"
      >
        <svg
          viewBox="0 0 33 33"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M16.961 32.338c-8.836 0-16-7.163-16-16 0-8.836 7.164-16 16-16 8.837 0 16 7.164 16 16 0 8.837-7.163 16-16 16zm.19-20.969c1.321 0 2.31-.993 2.31-2.32 0-1.32-.991-2.31-2.31-2.31-1.32 0-2.3.99-2.3 2.31 0 1.327.978 2.32 2.3 2.32zm-1.915 13.827h3.84V13.628h-3.84v11.568z"
            fill="#5CABF7"
            fill-rule="evenodd"
          />
        </svg>
      </span>
      <span
        class="emotion-3"
      />
    </div>
  </div>
</DocumentFragment>
`;

exports[`<Notification /> notification snapshot tests renders inverse information state correctly Gap 1`] = `
<DocumentFragment>
  .emotion-0 {
  transition-property: opacity;
  transition-duration: 250ms;
  transition-timing-function: cubic-bezier(0.25, 0, 0.25, 1);
  transition-delay: 0ms;
  opacity: 1;
}

.emotion-1 {
  line-height: 1.25;
  padding: 0.5rem;
  margin-bottom: 1.125rem;
  border-style: solid;
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 0.8125rem;
  color: #FFFFFF;
  border-color: #5DABF5;
  border-width: 2px 2px 2px 8px;
}

.emotion-2 {
  display: inline-block;
  height: 20px;
  width: 20px;
  min-height: 20px;
  min-width: 20px;
}

.emotion-2 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-2 svg path {
  fill: #5DABF5;
}

.emotion-2 svg rect {
  fill: #5DABF5;
}

.emotion-3 {
  padding-left: 0.75rem;
}

<div
    class="emotion-0"
  >
    <div
      aria-live="polite"
      class="emotion-1"
      kind="information"
    >
      <span
        aria-hidden="true"
        class="emotion-2"
      >
        <svg
          viewBox="0 0 33 33"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M16.961 32.338c-8.836 0-16-7.163-16-16 0-8.836 7.164-16 16-16 8.837 0 16 7.164 16 16 0 8.837-7.163 16-16 16zm.19-20.969c1.321 0 2.31-.993 2.31-2.32 0-1.32-.991-2.31-2.31-2.31-1.32 0-2.3.99-2.3 2.31 0 1.327.978 2.32 2.3 2.32zm-1.915 13.827h3.84V13.628h-3.84v11.568z"
            fill="#5CABF7"
            fill-rule="evenodd"
          />
        </svg>
      </span>
      <span
        class="emotion-3"
      />
    </div>
  </div>
</DocumentFragment>
`;

exports[`<Notification /> notification snapshot tests renders inverse information state correctly Gap in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  transition-property: opacity;
  transition-duration: 250ms;
  transition-timing-function: cubic-bezier(0.25, 0, 0.25, 1);
  transition-delay: 0ms;
  opacity: 1;
}

.emotion-1 {
  line-height: 1.25;
  padding: 0.5rem;
  margin-bottom: 1.125rem;
  border-style: solid;
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 0.875rem;
  color: #FFFFFF;
  border-color: #5DABF5;
  border-width: 2px 2px 2px 8px;
}

.emotion-2 {
  display: inline-block;
  height: 20px;
  width: 20px;
  min-height: 20px;
  min-width: 20px;
}

.emotion-2 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-2 svg path {
  fill: #5DABF5;
}

.emotion-2 svg rect {
  fill: #5DABF5;
}

.emotion-3 {
  padding-left: 0.75rem;
}

<div
    class="emotion-0"
  >
    <div
      aria-live="polite"
      class="emotion-1"
      kind="information"
    >
      <span
        aria-hidden="true"
        class="emotion-2"
      >
        <svg
          viewBox="0 0 33 33"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M16.961 32.338c-8.836 0-16-7.163-16-16 0-8.836 7.164-16 16-16 8.837 0 16 7.164 16 16 0 8.837-7.163 16-16 16zm.19-20.969c1.321 0 2.31-.993 2.31-2.32 0-1.32-.991-2.31-2.31-2.31-1.32 0-2.3.99-2.3 2.31 0 1.327.978 2.32 2.3 2.32zm-1.915 13.827h3.84V13.628h-3.84v11.568z"
            fill="#5CABF7"
            fill-rule="evenodd"
          />
        </svg>
      </span>
      <span
        class="emotion-3"
      />
    </div>
  </div>
</DocumentFragment>
`;

exports[`<Notification /> notification snapshot tests renders inverse information state correctly GapFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  transition-property: opacity;
  transition-duration: 250ms;
  transition-timing-function: cubic-bezier(0.25, 0, 0.25, 1);
  transition-delay: 0ms;
  opacity: 1;
}

.emotion-1 {
  line-height: 1.25;
  padding: 0.5rem;
  margin-bottom: 1.125rem;
  border-style: solid;
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 0.8125rem;
  color: #FFFFFF;
  border-color: #5DABF5;
  border-width: 2px 2px 2px 8px;
}

.emotion-2 {
  display: inline-block;
  height: 20px;
  width: 20px;
  min-height: 20px;
  min-width: 20px;
}

.emotion-2 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-2 svg path {
  fill: #5DABF5;
}

.emotion-2 svg rect {
  fill: #5DABF5;
}

.emotion-3 {
  padding-left: 0.75rem;
}

<div
    class="emotion-0"
  >
    <div
      aria-live="polite"
      class="emotion-1"
      kind="information"
    >
      <span
        aria-hidden="true"
        class="emotion-2"
      >
        <svg
          viewBox="0 0 33 33"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M16.961 32.338c-8.836 0-16-7.163-16-16 0-8.836 7.164-16 16-16 8.837 0 16 7.164 16 16 0 8.837-7.163 16-16 16zm.19-20.969c1.321 0 2.31-.993 2.31-2.32 0-1.32-.991-2.31-2.31-2.31-1.32 0-2.3.99-2.3 2.31 0 1.327.978 2.32 2.3 2.32zm-1.915 13.827h3.84V13.628h-3.84v11.568z"
            fill="#5CABF7"
            fill-rule="evenodd"
          />
        </svg>
      </span>
      <span
        class="emotion-3"
      />
    </div>
  </div>
</DocumentFragment>
`;

exports[`<Notification /> notification snapshot tests renders inverse information state correctly GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  transition-property: opacity;
  transition-duration: 250ms;
  transition-timing-function: cubic-bezier(0.25, 0, 0.25, 1);
  transition-delay: 0ms;
  opacity: 1;
}

.emotion-1 {
  line-height: 1.25;
  padding: 0.5rem;
  margin-bottom: 1.125rem;
  border-style: solid;
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 0.875rem;
  color: #FFFFFF;
  border-color: #5DABF5;
  border-width: 2px 2px 2px 8px;
}

.emotion-2 {
  display: inline-block;
  height: 20px;
  width: 20px;
  min-height: 20px;
  min-width: 20px;
}

.emotion-2 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-2 svg path {
  fill: #5DABF5;
}

.emotion-2 svg rect {
  fill: #5DABF5;
}

.emotion-3 {
  padding-left: 0.75rem;
}

<div
    class="emotion-0"
  >
    <div
      aria-live="polite"
      class="emotion-1"
      kind="information"
    >
      <span
        aria-hidden="true"
        class="emotion-2"
      >
        <svg
          viewBox="0 0 33 33"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M16.961 32.338c-8.836 0-16-7.163-16-16 0-8.836 7.164-16 16-16 8.837 0 16 7.164 16 16 0 8.837-7.163 16-16 16zm.19-20.969c1.321 0 2.31-.993 2.31-2.32 0-1.32-.991-2.31-2.31-2.31-1.32 0-2.3.99-2.3 2.31 0 1.327.978 2.32 2.3 2.32zm-1.915 13.827h3.84V13.628h-3.84v11.568z"
            fill="#5CABF7"
            fill-rule="evenodd"
          />
        </svg>
      </span>
      <span
        class="emotion-3"
      />
    </div>
  </div>
</DocumentFragment>
`;

exports[`<Notification /> notification snapshot tests renders inverse information state correctly OldNavy 1`] = `
<DocumentFragment>
  .emotion-0 {
  transition-property: opacity;
  transition-duration: 250ms;
  transition-timing-function: cubic-bezier(0.25, 0, 0.25, 1);
  transition-delay: 0ms;
  opacity: 1;
}

.emotion-1 {
  line-height: 1.25;
  padding: 0.5rem;
  margin-bottom: 1.125rem;
  border-style: solid;
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  font-family: 'Gotham','Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 0.875rem;
  color: #FFFFFF;
  border-color: #5DABF5;
  border-width: 2px 2px 2px 8px;
}

.emotion-2 {
  display: inline-block;
  height: 20px;
  width: 20px;
  min-height: 20px;
  min-width: 20px;
}

.emotion-2 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-2 svg path {
  fill: #5DABF5;
}

.emotion-2 svg rect {
  fill: #5DABF5;
}

.emotion-3 {
  padding-left: 0.75rem;
}

<div
    class="emotion-0"
  >
    <div
      aria-live="polite"
      class="emotion-1"
      kind="information"
    >
      <span
        aria-hidden="true"
        class="emotion-2"
      >
        <svg
          viewBox="0 0 33 33"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M16.961 32.338c-8.836 0-16-7.163-16-16 0-8.836 7.164-16 16-16 8.837 0 16 7.164 16 16 0 8.837-7.163 16-16 16zm.19-20.969c1.321 0 2.31-.993 2.31-2.32 0-1.32-.991-2.31-2.31-2.31-1.32 0-2.3.99-2.3 2.31 0 1.327.978 2.32 2.3 2.32zm-1.915 13.827h3.84V13.628h-3.84v11.568z"
            fill="#5CABF7"
            fill-rule="evenodd"
          />
        </svg>
      </span>
      <span
        class="emotion-3"
      />
    </div>
  </div>
</DocumentFragment>
`;

exports[`<Notification /> notification snapshot tests renders inverse information state correctly OldNavy in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  transition-property: opacity;
  transition-duration: 250ms;
  transition-timing-function: cubic-bezier(0.25, 0, 0.25, 1);
  transition-delay: 0ms;
  opacity: 1;
}

.emotion-1 {
  line-height: 1.25;
  padding: 0.5rem;
  margin-bottom: 1.125rem;
  border-style: solid;
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 0.875rem;
  color: #FFFFFF;
  border-color: #5DABF5;
  border-width: 2px 2px 2px 8px;
}

.emotion-2 {
  display: inline-block;
  height: 20px;
  width: 20px;
  min-height: 20px;
  min-width: 20px;
}

.emotion-2 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-2 svg path {
  fill: #5DABF5;
}

.emotion-2 svg rect {
  fill: #5DABF5;
}

.emotion-3 {
  padding-left: 0.75rem;
}

<div
    class="emotion-0"
  >
    <div
      aria-live="polite"
      class="emotion-1"
      kind="information"
    >
      <span
        aria-hidden="true"
        class="emotion-2"
      >
        <svg
          viewBox="0 0 33 33"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M16.961 32.338c-8.836 0-16-7.163-16-16 0-8.836 7.164-16 16-16 8.837 0 16 7.164 16 16 0 8.837-7.163 16-16 16zm.19-20.969c1.321 0 2.31-.993 2.31-2.32 0-1.32-.991-2.31-2.31-2.31-1.32 0-2.3.99-2.3 2.31 0 1.327.978 2.32 2.3 2.32zm-1.915 13.827h3.84V13.628h-3.84v11.568z"
            fill="#5CABF7"
            fill-rule="evenodd"
          />
        </svg>
      </span>
      <span
        class="emotion-3"
      />
    </div>
  </div>
</DocumentFragment>
`;

exports[`<Notification /> notification snapshot tests renders inverse success state correctly Athleta 1`] = `
<DocumentFragment>
  .emotion-0 {
  transition-property: opacity;
  transition-duration: 250ms;
  transition-timing-function: cubic-bezier(0.25, 0, 0.25, 1);
  transition-delay: 0ms;
  opacity: 1;
}

.emotion-1 {
  line-height: 1.25;
  padding: 0.5rem;
  margin-bottom: 1.125rem;
  border-style: solid;
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  font-family: 'Avenir Next','Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 0.875rem;
  color: #FFFFFF;
  border-color: #82B05D;
  border-width: 2px 2px 2px 8px;
}

.emotion-2 {
  display: inline-block;
  height: 20px;
  width: 20px;
  min-height: 20px;
  min-width: 20px;
}

.emotion-2 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-2 svg path {
  fill: #82B05D;
}

.emotion-2 svg rect {
  fill: #82B05D;
}

.emotion-3 {
  padding-left: 0.75rem;
}

<div
    class="emotion-0"
  >
    <div
      aria-live="polite"
      class="emotion-1"
      kind="success"
    >
      <span
        aria-hidden="true"
        class="emotion-2"
      >
        <svg
          viewBox="0 0 32 32"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M16.118 0c8.837 0 16 7.163 16 16s-7.163 16-16 16-16-7.163-16-16 7.163-16 16-16zm6.552 9l-9.146 9.146-4.526-4.526-2.88 2.88 7.5 7.5 12.028-12.026L22.67 9z"
            fill="#16A816"
            fill-rule="nonzero"
          />
        </svg>
      </span>
      <span
        class="emotion-3"
      />
    </div>
  </div>
</DocumentFragment>
`;

exports[`<Notification /> notification snapshot tests renders inverse success state correctly Athleta in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  transition-property: opacity;
  transition-duration: 250ms;
  transition-timing-function: cubic-bezier(0.25, 0, 0.25, 1);
  transition-delay: 0ms;
  opacity: 1;
}

.emotion-1 {
  line-height: 1.25;
  padding: 0.5rem;
  margin-bottom: 1.125rem;
  border-style: solid;
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 0.875rem;
  color: #FFFFFF;
  border-color: #82B05D;
  border-width: 2px 2px 2px 8px;
}

.emotion-2 {
  display: inline-block;
  height: 20px;
  width: 20px;
  min-height: 20px;
  min-width: 20px;
}

.emotion-2 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-2 svg path {
  fill: #82B05D;
}

.emotion-2 svg rect {
  fill: #82B05D;
}

.emotion-3 {
  padding-left: 0.75rem;
}

<div
    class="emotion-0"
  >
    <div
      aria-live="polite"
      class="emotion-1"
      kind="success"
    >
      <span
        aria-hidden="true"
        class="emotion-2"
      >
        <svg
          viewBox="0 0 32 32"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M16.118 0c8.837 0 16 7.163 16 16s-7.163 16-16 16-16-7.163-16-16 7.163-16 16-16zm6.552 9l-9.146 9.146-4.526-4.526-2.88 2.88 7.5 7.5 12.028-12.026L22.67 9z"
            fill="#16A816"
            fill-rule="nonzero"
          />
        </svg>
      </span>
      <span
        class="emotion-3"
      />
    </div>
  </div>
</DocumentFragment>
`;

exports[`<Notification /> notification snapshot tests renders inverse success state correctly BananaRepublic 1`] = `
<DocumentFragment>
  .emotion-0 {
  transition-property: opacity;
  transition-duration: 250ms;
  transition-timing-function: cubic-bezier(0.25, 0, 0.25, 1);
  transition-delay: 0ms;
  opacity: 1;
}

.emotion-1 {
  line-height: 1.25;
  padding: 0.5rem;
  margin-bottom: 1.125rem;
  border-style: solid;
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  font-family: 'Lynstone',Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 0.8125rem;
  color: #F6F4EB;
  border-color: #82B05D;
  border-width: 2px 2px 2px 8px;
}

.emotion-2 {
  display: inline-block;
  height: 20px;
  width: 20px;
  min-height: 20px;
  min-width: 20px;
}

.emotion-2 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-2 svg path {
  fill: #82B05D;
}

.emotion-2 svg rect {
  fill: #82B05D;
}

.emotion-3 {
  padding-left: 0.75rem;
}

<div
    class="emotion-0"
  >
    <div
      aria-live="polite"
      class="emotion-1"
      kind="success"
    >
      <span
        aria-hidden="true"
        class="emotion-2"
      >
        <svg
          viewBox="0 0 32 32"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M16.118 0c8.837 0 16 7.163 16 16s-7.163 16-16 16-16-7.163-16-16 7.163-16 16-16zm6.552 9l-9.146 9.146-4.526-4.526-2.88 2.88 7.5 7.5 12.028-12.026L22.67 9z"
            fill="#16A816"
            fill-rule="nonzero"
          />
        </svg>
      </span>
      <span
        class="emotion-3"
      />
    </div>
  </div>
</DocumentFragment>
`;

exports[`<Notification /> notification snapshot tests renders inverse success state correctly BananaRepublic in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  transition-property: opacity;
  transition-duration: 250ms;
  transition-timing-function: cubic-bezier(0.25, 0, 0.25, 1);
  transition-delay: 0ms;
  opacity: 1;
}

.emotion-1 {
  line-height: 1.25;
  padding: 0.5rem;
  margin-bottom: 1.125rem;
  border-style: solid;
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 0.875rem;
  color: #F6F4EB;
  border-color: #82B05D;
  border-width: 2px 2px 2px 8px;
}

.emotion-2 {
  display: inline-block;
  height: 20px;
  width: 20px;
  min-height: 20px;
  min-width: 20px;
}

.emotion-2 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-2 svg path {
  fill: #82B05D;
}

.emotion-2 svg rect {
  fill: #82B05D;
}

.emotion-3 {
  padding-left: 0.75rem;
}

<div
    class="emotion-0"
  >
    <div
      aria-live="polite"
      class="emotion-1"
      kind="success"
    >
      <span
        aria-hidden="true"
        class="emotion-2"
      >
        <svg
          viewBox="0 0 32 32"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M16.118 0c8.837 0 16 7.163 16 16s-7.163 16-16 16-16-7.163-16-16 7.163-16 16-16zm6.552 9l-9.146 9.146-4.526-4.526-2.88 2.88 7.5 7.5 12.028-12.026L22.67 9z"
            fill="#16A816"
            fill-rule="nonzero"
          />
        </svg>
      </span>
      <span
        class="emotion-3"
      />
    </div>
  </div>
</DocumentFragment>
`;

exports[`<Notification /> notification snapshot tests renders inverse success state correctly BananaRepublicFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  transition-property: opacity;
  transition-duration: 250ms;
  transition-timing-function: cubic-bezier(0.25, 0, 0.25, 1);
  transition-delay: 0ms;
  opacity: 1;
}

.emotion-1 {
  line-height: 1.25;
  padding: 0.5rem;
  margin-bottom: 1.125rem;
  border-style: solid;
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  font-family: 'Lynstone',Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 0.8125rem;
  color: #F6F4EB;
  border-color: #82B05D;
  border-width: 2px 2px 2px 8px;
}

.emotion-2 {
  display: inline-block;
  height: 20px;
  width: 20px;
  min-height: 20px;
  min-width: 20px;
}

.emotion-2 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-2 svg path {
  fill: #82B05D;
}

.emotion-2 svg rect {
  fill: #82B05D;
}

.emotion-3 {
  padding-left: 0.75rem;
}

<div
    class="emotion-0"
  >
    <div
      aria-live="polite"
      class="emotion-1"
      kind="success"
    >
      <span
        aria-hidden="true"
        class="emotion-2"
      >
        <svg
          viewBox="0 0 32 32"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M16.118 0c8.837 0 16 7.163 16 16s-7.163 16-16 16-16-7.163-16-16 7.163-16 16-16zm6.552 9l-9.146 9.146-4.526-4.526-2.88 2.88 7.5 7.5 12.028-12.026L22.67 9z"
            fill="#16A816"
            fill-rule="nonzero"
          />
        </svg>
      </span>
      <span
        class="emotion-3"
      />
    </div>
  </div>
</DocumentFragment>
`;

exports[`<Notification /> notification snapshot tests renders inverse success state correctly BananaRepublicFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  transition-property: opacity;
  transition-duration: 250ms;
  transition-timing-function: cubic-bezier(0.25, 0, 0.25, 1);
  transition-delay: 0ms;
  opacity: 1;
}

.emotion-1 {
  line-height: 1.25;
  padding: 0.5rem;
  margin-bottom: 1.125rem;
  border-style: solid;
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 0.875rem;
  color: #F6F4EB;
  border-color: #82B05D;
  border-width: 2px 2px 2px 8px;
}

.emotion-2 {
  display: inline-block;
  height: 20px;
  width: 20px;
  min-height: 20px;
  min-width: 20px;
}

.emotion-2 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-2 svg path {
  fill: #82B05D;
}

.emotion-2 svg rect {
  fill: #82B05D;
}

.emotion-3 {
  padding-left: 0.75rem;
}

<div
    class="emotion-0"
  >
    <div
      aria-live="polite"
      class="emotion-1"
      kind="success"
    >
      <span
        aria-hidden="true"
        class="emotion-2"
      >
        <svg
          viewBox="0 0 32 32"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M16.118 0c8.837 0 16 7.163 16 16s-7.163 16-16 16-16-7.163-16-16 7.163-16 16-16zm6.552 9l-9.146 9.146-4.526-4.526-2.88 2.88 7.5 7.5 12.028-12.026L22.67 9z"
            fill="#16A816"
            fill-rule="nonzero"
          />
        </svg>
      </span>
      <span
        class="emotion-3"
      />
    </div>
  </div>
</DocumentFragment>
`;

exports[`<Notification /> notification snapshot tests renders inverse success state correctly Gap 1`] = `
<DocumentFragment>
  .emotion-0 {
  transition-property: opacity;
  transition-duration: 250ms;
  transition-timing-function: cubic-bezier(0.25, 0, 0.25, 1);
  transition-delay: 0ms;
  opacity: 1;
}

.emotion-1 {
  line-height: 1.25;
  padding: 0.5rem;
  margin-bottom: 1.125rem;
  border-style: solid;
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 0.8125rem;
  color: #FFFFFF;
  border-color: #82B05D;
  border-width: 2px 2px 2px 8px;
}

.emotion-2 {
  display: inline-block;
  height: 20px;
  width: 20px;
  min-height: 20px;
  min-width: 20px;
}

.emotion-2 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-2 svg path {
  fill: #82B05D;
}

.emotion-2 svg rect {
  fill: #82B05D;
}

.emotion-3 {
  padding-left: 0.75rem;
}

<div
    class="emotion-0"
  >
    <div
      aria-live="polite"
      class="emotion-1"
      kind="success"
    >
      <span
        aria-hidden="true"
        class="emotion-2"
      >
        <svg
          viewBox="0 0 32 32"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M16.118 0c8.837 0 16 7.163 16 16s-7.163 16-16 16-16-7.163-16-16 7.163-16 16-16zm6.552 9l-9.146 9.146-4.526-4.526-2.88 2.88 7.5 7.5 12.028-12.026L22.67 9z"
            fill="#16A816"
            fill-rule="nonzero"
          />
        </svg>
      </span>
      <span
        class="emotion-3"
      />
    </div>
  </div>
</DocumentFragment>
`;

exports[`<Notification /> notification snapshot tests renders inverse success state correctly Gap in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  transition-property: opacity;
  transition-duration: 250ms;
  transition-timing-function: cubic-bezier(0.25, 0, 0.25, 1);
  transition-delay: 0ms;
  opacity: 1;
}

.emotion-1 {
  line-height: 1.25;
  padding: 0.5rem;
  margin-bottom: 1.125rem;
  border-style: solid;
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 0.875rem;
  color: #FFFFFF;
  border-color: #82B05D;
  border-width: 2px 2px 2px 8px;
}

.emotion-2 {
  display: inline-block;
  height: 20px;
  width: 20px;
  min-height: 20px;
  min-width: 20px;
}

.emotion-2 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-2 svg path {
  fill: #82B05D;
}

.emotion-2 svg rect {
  fill: #82B05D;
}

.emotion-3 {
  padding-left: 0.75rem;
}

<div
    class="emotion-0"
  >
    <div
      aria-live="polite"
      class="emotion-1"
      kind="success"
    >
      <span
        aria-hidden="true"
        class="emotion-2"
      >
        <svg
          viewBox="0 0 32 32"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M16.118 0c8.837 0 16 7.163 16 16s-7.163 16-16 16-16-7.163-16-16 7.163-16 16-16zm6.552 9l-9.146 9.146-4.526-4.526-2.88 2.88 7.5 7.5 12.028-12.026L22.67 9z"
            fill="#16A816"
            fill-rule="nonzero"
          />
        </svg>
      </span>
      <span
        class="emotion-3"
      />
    </div>
  </div>
</DocumentFragment>
`;

exports[`<Notification /> notification snapshot tests renders inverse success state correctly GapFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  transition-property: opacity;
  transition-duration: 250ms;
  transition-timing-function: cubic-bezier(0.25, 0, 0.25, 1);
  transition-delay: 0ms;
  opacity: 1;
}

.emotion-1 {
  line-height: 1.25;
  padding: 0.5rem;
  margin-bottom: 1.125rem;
  border-style: solid;
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 0.8125rem;
  color: #FFFFFF;
  border-color: #82B05D;
  border-width: 2px 2px 2px 8px;
}

.emotion-2 {
  display: inline-block;
  height: 20px;
  width: 20px;
  min-height: 20px;
  min-width: 20px;
}

.emotion-2 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-2 svg path {
  fill: #82B05D;
}

.emotion-2 svg rect {
  fill: #82B05D;
}

.emotion-3 {
  padding-left: 0.75rem;
}

<div
    class="emotion-0"
  >
    <div
      aria-live="polite"
      class="emotion-1"
      kind="success"
    >
      <span
        aria-hidden="true"
        class="emotion-2"
      >
        <svg
          viewBox="0 0 32 32"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M16.118 0c8.837 0 16 7.163 16 16s-7.163 16-16 16-16-7.163-16-16 7.163-16 16-16zm6.552 9l-9.146 9.146-4.526-4.526-2.88 2.88 7.5 7.5 12.028-12.026L22.67 9z"
            fill="#16A816"
            fill-rule="nonzero"
          />
        </svg>
      </span>
      <span
        class="emotion-3"
      />
    </div>
  </div>
</DocumentFragment>
`;

exports[`<Notification /> notification snapshot tests renders inverse success state correctly GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  transition-property: opacity;
  transition-duration: 250ms;
  transition-timing-function: cubic-bezier(0.25, 0, 0.25, 1);
  transition-delay: 0ms;
  opacity: 1;
}

.emotion-1 {
  line-height: 1.25;
  padding: 0.5rem;
  margin-bottom: 1.125rem;
  border-style: solid;
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 0.875rem;
  color: #FFFFFF;
  border-color: #82B05D;
  border-width: 2px 2px 2px 8px;
}

.emotion-2 {
  display: inline-block;
  height: 20px;
  width: 20px;
  min-height: 20px;
  min-width: 20px;
}

.emotion-2 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-2 svg path {
  fill: #82B05D;
}

.emotion-2 svg rect {
  fill: #82B05D;
}

.emotion-3 {
  padding-left: 0.75rem;
}

<div
    class="emotion-0"
  >
    <div
      aria-live="polite"
      class="emotion-1"
      kind="success"
    >
      <span
        aria-hidden="true"
        class="emotion-2"
      >
        <svg
          viewBox="0 0 32 32"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M16.118 0c8.837 0 16 7.163 16 16s-7.163 16-16 16-16-7.163-16-16 7.163-16 16-16zm6.552 9l-9.146 9.146-4.526-4.526-2.88 2.88 7.5 7.5 12.028-12.026L22.67 9z"
            fill="#16A816"
            fill-rule="nonzero"
          />
        </svg>
      </span>
      <span
        class="emotion-3"
      />
    </div>
  </div>
</DocumentFragment>
`;

exports[`<Notification /> notification snapshot tests renders inverse success state correctly OldNavy 1`] = `
<DocumentFragment>
  .emotion-0 {
  transition-property: opacity;
  transition-duration: 250ms;
  transition-timing-function: cubic-bezier(0.25, 0, 0.25, 1);
  transition-delay: 0ms;
  opacity: 1;
}

.emotion-1 {
  line-height: 1.25;
  padding: 0.5rem;
  margin-bottom: 1.125rem;
  border-style: solid;
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  font-family: 'Gotham','Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 0.875rem;
  color: #FFFFFF;
  border-color: #82B05D;
  border-width: 2px 2px 2px 8px;
}

.emotion-2 {
  display: inline-block;
  height: 20px;
  width: 20px;
  min-height: 20px;
  min-width: 20px;
}

.emotion-2 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-2 svg path {
  fill: #82B05D;
}

.emotion-2 svg rect {
  fill: #82B05D;
}

.emotion-3 {
  padding-left: 0.75rem;
}

<div
    class="emotion-0"
  >
    <div
      aria-live="polite"
      class="emotion-1"
      kind="success"
    >
      <span
        aria-hidden="true"
        class="emotion-2"
      >
        <svg
          viewBox="0 0 32 32"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M16.118 0c8.837 0 16 7.163 16 16s-7.163 16-16 16-16-7.163-16-16 7.163-16 16-16zm6.552 9l-9.146 9.146-4.526-4.526-2.88 2.88 7.5 7.5 12.028-12.026L22.67 9z"
            fill="#16A816"
            fill-rule="nonzero"
          />
        </svg>
      </span>
      <span
        class="emotion-3"
      />
    </div>
  </div>
</DocumentFragment>
`;

exports[`<Notification /> notification snapshot tests renders inverse success state correctly OldNavy in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  transition-property: opacity;
  transition-duration: 250ms;
  transition-timing-function: cubic-bezier(0.25, 0, 0.25, 1);
  transition-delay: 0ms;
  opacity: 1;
}

.emotion-1 {
  line-height: 1.25;
  padding: 0.5rem;
  margin-bottom: 1.125rem;
  border-style: solid;
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 0.875rem;
  color: #FFFFFF;
  border-color: #82B05D;
  border-width: 2px 2px 2px 8px;
}

.emotion-2 {
  display: inline-block;
  height: 20px;
  width: 20px;
  min-height: 20px;
  min-width: 20px;
}

.emotion-2 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-2 svg path {
  fill: #82B05D;
}

.emotion-2 svg rect {
  fill: #82B05D;
}

.emotion-3 {
  padding-left: 0.75rem;
}

<div
    class="emotion-0"
  >
    <div
      aria-live="polite"
      class="emotion-1"
      kind="success"
    >
      <span
        aria-hidden="true"
        class="emotion-2"
      >
        <svg
          viewBox="0 0 32 32"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M16.118 0c8.837 0 16 7.163 16 16s-7.163 16-16 16-16-7.163-16-16 7.163-16 16-16zm6.552 9l-9.146 9.146-4.526-4.526-2.88 2.88 7.5 7.5 12.028-12.026L22.67 9z"
            fill="#16A816"
            fill-rule="nonzero"
          />
        </svg>
      </span>
      <span
        class="emotion-3"
      />
    </div>
  </div>
</DocumentFragment>
`;

exports[`<Notification /> notification snapshot tests renders inverse warning state correctly Athleta 1`] = `
<DocumentFragment>
  .emotion-0 {
  transition-property: opacity;
  transition-duration: 250ms;
  transition-timing-function: cubic-bezier(0.25, 0, 0.25, 1);
  transition-delay: 0ms;
  opacity: 1;
}

.emotion-1 {
  line-height: 1.25;
  padding: 0.5rem;
  margin-bottom: 1.125rem;
  border-style: solid;
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  font-family: 'Avenir Next','Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 0.875rem;
  color: #FFFFFF;
  border-color: #FFC83A;
  border-width: 2px 2px 2px 8px;
}

.emotion-2 {
  display: inline-block;
  height: 20px;
  width: 20px;
  min-height: 20px;
  min-width: 20px;
}

.emotion-2 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-2 svg path {
  fill: #FFC83A;
}

.emotion-2 svg rect {
  fill: #FFC83A;
}

.emotion-3 {
  padding-left: 0.75rem;
}

<div
    class="emotion-0"
  >
    <div
      aria-live="polite"
      class="emotion-1"
      kind="warning"
    >
      <span
        aria-hidden="true"
        class="emotion-2"
      >
        <svg
          viewBox="0 0 33 33"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M18.117.402c.659.33 1.193.864 1.522 1.522l12.597 25.194a3.403 3.403 0 01-3.044 4.925H4a3.403 3.403 0 01-3.044-4.925L13.552 1.924A3.403 3.403 0 0118.117.402zm-1.516 23.156c-1.393 0-2.436 1.047-2.436 2.445 0 1.39 1.045 2.434 2.436 2.434s2.426-1.044 2.426-2.434c0-1.398-1.032-2.445-2.426-2.445zm2.02-14.565h-4.05v12.186h4.05V8.993z"
            fill="#F0B00B"
            fill-rule="nonzero"
          />
        </svg>
      </span>
      <span
        class="emotion-3"
      />
    </div>
  </div>
</DocumentFragment>
`;

exports[`<Notification /> notification snapshot tests renders inverse warning state correctly Athleta in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  transition-property: opacity;
  transition-duration: 250ms;
  transition-timing-function: cubic-bezier(0.25, 0, 0.25, 1);
  transition-delay: 0ms;
  opacity: 1;
}

.emotion-1 {
  line-height: 1.25;
  padding: 0.5rem;
  margin-bottom: 1.125rem;
  border-style: solid;
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 0.875rem;
  color: #FFFFFF;
  border-color: #FFC83A;
  border-width: 2px 2px 2px 8px;
}

.emotion-2 {
  display: inline-block;
  height: 20px;
  width: 20px;
  min-height: 20px;
  min-width: 20px;
}

.emotion-2 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-2 svg path {
  fill: #FFC83A;
}

.emotion-2 svg rect {
  fill: #FFC83A;
}

.emotion-3 {
  padding-left: 0.75rem;
}

<div
    class="emotion-0"
  >
    <div
      aria-live="polite"
      class="emotion-1"
      kind="warning"
    >
      <span
        aria-hidden="true"
        class="emotion-2"
      >
        <svg
          viewBox="0 0 33 33"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M18.117.402c.659.33 1.193.864 1.522 1.522l12.597 25.194a3.403 3.403 0 01-3.044 4.925H4a3.403 3.403 0 01-3.044-4.925L13.552 1.924A3.403 3.403 0 0118.117.402zm-1.516 23.156c-1.393 0-2.436 1.047-2.436 2.445 0 1.39 1.045 2.434 2.436 2.434s2.426-1.044 2.426-2.434c0-1.398-1.032-2.445-2.426-2.445zm2.02-14.565h-4.05v12.186h4.05V8.993z"
            fill="#F0B00B"
            fill-rule="nonzero"
          />
        </svg>
      </span>
      <span
        class="emotion-3"
      />
    </div>
  </div>
</DocumentFragment>
`;

exports[`<Notification /> notification snapshot tests renders inverse warning state correctly BananaRepublic 1`] = `
<DocumentFragment>
  .emotion-0 {
  transition-property: opacity;
  transition-duration: 250ms;
  transition-timing-function: cubic-bezier(0.25, 0, 0.25, 1);
  transition-delay: 0ms;
  opacity: 1;
}

.emotion-1 {
  line-height: 1.25;
  padding: 0.5rem;
  margin-bottom: 1.125rem;
  border-style: solid;
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  font-family: 'Lynstone',Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 0.8125rem;
  color: #F6F4EB;
  border-color: #FFC83A;
  border-width: 2px 2px 2px 8px;
}

.emotion-2 {
  display: inline-block;
  height: 20px;
  width: 20px;
  min-height: 20px;
  min-width: 20px;
}

.emotion-2 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-2 svg path {
  fill: #FFC83A;
}

.emotion-2 svg rect {
  fill: #FFC83A;
}

.emotion-3 {
  padding-left: 0.75rem;
}

<div
    class="emotion-0"
  >
    <div
      aria-live="polite"
      class="emotion-1"
      kind="warning"
    >
      <span
        aria-hidden="true"
        class="emotion-2"
      >
        <svg
          viewBox="0 0 33 33"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M18.117.402c.659.33 1.193.864 1.522 1.522l12.597 25.194a3.403 3.403 0 01-3.044 4.925H4a3.403 3.403 0 01-3.044-4.925L13.552 1.924A3.403 3.403 0 0118.117.402zm-1.516 23.156c-1.393 0-2.436 1.047-2.436 2.445 0 1.39 1.045 2.434 2.436 2.434s2.426-1.044 2.426-2.434c0-1.398-1.032-2.445-2.426-2.445zm2.02-14.565h-4.05v12.186h4.05V8.993z"
            fill="#F0B00B"
            fill-rule="nonzero"
          />
        </svg>
      </span>
      <span
        class="emotion-3"
      />
    </div>
  </div>
</DocumentFragment>
`;

exports[`<Notification /> notification snapshot tests renders inverse warning state correctly BananaRepublic in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  transition-property: opacity;
  transition-duration: 250ms;
  transition-timing-function: cubic-bezier(0.25, 0, 0.25, 1);
  transition-delay: 0ms;
  opacity: 1;
}

.emotion-1 {
  line-height: 1.25;
  padding: 0.5rem;
  margin-bottom: 1.125rem;
  border-style: solid;
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 0.875rem;
  color: #F6F4EB;
  border-color: #FFC83A;
  border-width: 2px 2px 2px 8px;
}

.emotion-2 {
  display: inline-block;
  height: 20px;
  width: 20px;
  min-height: 20px;
  min-width: 20px;
}

.emotion-2 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-2 svg path {
  fill: #FFC83A;
}

.emotion-2 svg rect {
  fill: #FFC83A;
}

.emotion-3 {
  padding-left: 0.75rem;
}

<div
    class="emotion-0"
  >
    <div
      aria-live="polite"
      class="emotion-1"
      kind="warning"
    >
      <span
        aria-hidden="true"
        class="emotion-2"
      >
        <svg
          viewBox="0 0 33 33"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M18.117.402c.659.33 1.193.864 1.522 1.522l12.597 25.194a3.403 3.403 0 01-3.044 4.925H4a3.403 3.403 0 01-3.044-4.925L13.552 1.924A3.403 3.403 0 0118.117.402zm-1.516 23.156c-1.393 0-2.436 1.047-2.436 2.445 0 1.39 1.045 2.434 2.436 2.434s2.426-1.044 2.426-2.434c0-1.398-1.032-2.445-2.426-2.445zm2.02-14.565h-4.05v12.186h4.05V8.993z"
            fill="#F0B00B"
            fill-rule="nonzero"
          />
        </svg>
      </span>
      <span
        class="emotion-3"
      />
    </div>
  </div>
</DocumentFragment>
`;

exports[`<Notification /> notification snapshot tests renders inverse warning state correctly BananaRepublicFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  transition-property: opacity;
  transition-duration: 250ms;
  transition-timing-function: cubic-bezier(0.25, 0, 0.25, 1);
  transition-delay: 0ms;
  opacity: 1;
}

.emotion-1 {
  line-height: 1.25;
  padding: 0.5rem;
  margin-bottom: 1.125rem;
  border-style: solid;
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  font-family: 'Lynstone',Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 0.8125rem;
  color: #F6F4EB;
  border-color: #FFC83A;
  border-width: 2px 2px 2px 8px;
}

.emotion-2 {
  display: inline-block;
  height: 20px;
  width: 20px;
  min-height: 20px;
  min-width: 20px;
}

.emotion-2 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-2 svg path {
  fill: #FFC83A;
}

.emotion-2 svg rect {
  fill: #FFC83A;
}

.emotion-3 {
  padding-left: 0.75rem;
}

<div
    class="emotion-0"
  >
    <div
      aria-live="polite"
      class="emotion-1"
      kind="warning"
    >
      <span
        aria-hidden="true"
        class="emotion-2"
      >
        <svg
          viewBox="0 0 33 33"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M18.117.402c.659.33 1.193.864 1.522 1.522l12.597 25.194a3.403 3.403 0 01-3.044 4.925H4a3.403 3.403 0 01-3.044-4.925L13.552 1.924A3.403 3.403 0 0118.117.402zm-1.516 23.156c-1.393 0-2.436 1.047-2.436 2.445 0 1.39 1.045 2.434 2.436 2.434s2.426-1.044 2.426-2.434c0-1.398-1.032-2.445-2.426-2.445zm2.02-14.565h-4.05v12.186h4.05V8.993z"
            fill="#F0B00B"
            fill-rule="nonzero"
          />
        </svg>
      </span>
      <span
        class="emotion-3"
      />
    </div>
  </div>
</DocumentFragment>
`;

exports[`<Notification /> notification snapshot tests renders inverse warning state correctly BananaRepublicFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  transition-property: opacity;
  transition-duration: 250ms;
  transition-timing-function: cubic-bezier(0.25, 0, 0.25, 1);
  transition-delay: 0ms;
  opacity: 1;
}

.emotion-1 {
  line-height: 1.25;
  padding: 0.5rem;
  margin-bottom: 1.125rem;
  border-style: solid;
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 0.875rem;
  color: #F6F4EB;
  border-color: #FFC83A;
  border-width: 2px 2px 2px 8px;
}

.emotion-2 {
  display: inline-block;
  height: 20px;
  width: 20px;
  min-height: 20px;
  min-width: 20px;
}

.emotion-2 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-2 svg path {
  fill: #FFC83A;
}

.emotion-2 svg rect {
  fill: #FFC83A;
}

.emotion-3 {
  padding-left: 0.75rem;
}

<div
    class="emotion-0"
  >
    <div
      aria-live="polite"
      class="emotion-1"
      kind="warning"
    >
      <span
        aria-hidden="true"
        class="emotion-2"
      >
        <svg
          viewBox="0 0 33 33"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M18.117.402c.659.33 1.193.864 1.522 1.522l12.597 25.194a3.403 3.403 0 01-3.044 4.925H4a3.403 3.403 0 01-3.044-4.925L13.552 1.924A3.403 3.403 0 0118.117.402zm-1.516 23.156c-1.393 0-2.436 1.047-2.436 2.445 0 1.39 1.045 2.434 2.436 2.434s2.426-1.044 2.426-2.434c0-1.398-1.032-2.445-2.426-2.445zm2.02-14.565h-4.05v12.186h4.05V8.993z"
            fill="#F0B00B"
            fill-rule="nonzero"
          />
        </svg>
      </span>
      <span
        class="emotion-3"
      />
    </div>
  </div>
</DocumentFragment>
`;

exports[`<Notification /> notification snapshot tests renders inverse warning state correctly Gap 1`] = `
<DocumentFragment>
  .emotion-0 {
  transition-property: opacity;
  transition-duration: 250ms;
  transition-timing-function: cubic-bezier(0.25, 0, 0.25, 1);
  transition-delay: 0ms;
  opacity: 1;
}

.emotion-1 {
  line-height: 1.25;
  padding: 0.5rem;
  margin-bottom: 1.125rem;
  border-style: solid;
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 0.8125rem;
  color: #FFFFFF;
  border-color: #FFC83A;
  border-width: 2px 2px 2px 8px;
}

.emotion-2 {
  display: inline-block;
  height: 20px;
  width: 20px;
  min-height: 20px;
  min-width: 20px;
}

.emotion-2 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-2 svg path {
  fill: #FFC83A;
}

.emotion-2 svg rect {
  fill: #FFC83A;
}

.emotion-3 {
  padding-left: 0.75rem;
}

<div
    class="emotion-0"
  >
    <div
      aria-live="polite"
      class="emotion-1"
      kind="warning"
    >
      <span
        aria-hidden="true"
        class="emotion-2"
      >
        <svg
          viewBox="0 0 33 33"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M18.117.402c.659.33 1.193.864 1.522 1.522l12.597 25.194a3.403 3.403 0 01-3.044 4.925H4a3.403 3.403 0 01-3.044-4.925L13.552 1.924A3.403 3.403 0 0118.117.402zm-1.516 23.156c-1.393 0-2.436 1.047-2.436 2.445 0 1.39 1.045 2.434 2.436 2.434s2.426-1.044 2.426-2.434c0-1.398-1.032-2.445-2.426-2.445zm2.02-14.565h-4.05v12.186h4.05V8.993z"
            fill="#F0B00B"
            fill-rule="nonzero"
          />
        </svg>
      </span>
      <span
        class="emotion-3"
      />
    </div>
  </div>
</DocumentFragment>
`;

exports[`<Notification /> notification snapshot tests renders inverse warning state correctly Gap in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  transition-property: opacity;
  transition-duration: 250ms;
  transition-timing-function: cubic-bezier(0.25, 0, 0.25, 1);
  transition-delay: 0ms;
  opacity: 1;
}

.emotion-1 {
  line-height: 1.25;
  padding: 0.5rem;
  margin-bottom: 1.125rem;
  border-style: solid;
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 0.875rem;
  color: #FFFFFF;
  border-color: #FFC83A;
  border-width: 2px 2px 2px 8px;
}

.emotion-2 {
  display: inline-block;
  height: 20px;
  width: 20px;
  min-height: 20px;
  min-width: 20px;
}

.emotion-2 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-2 svg path {
  fill: #FFC83A;
}

.emotion-2 svg rect {
  fill: #FFC83A;
}

.emotion-3 {
  padding-left: 0.75rem;
}

<div
    class="emotion-0"
  >
    <div
      aria-live="polite"
      class="emotion-1"
      kind="warning"
    >
      <span
        aria-hidden="true"
        class="emotion-2"
      >
        <svg
          viewBox="0 0 33 33"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M18.117.402c.659.33 1.193.864 1.522 1.522l12.597 25.194a3.403 3.403 0 01-3.044 4.925H4a3.403 3.403 0 01-3.044-4.925L13.552 1.924A3.403 3.403 0 0118.117.402zm-1.516 23.156c-1.393 0-2.436 1.047-2.436 2.445 0 1.39 1.045 2.434 2.436 2.434s2.426-1.044 2.426-2.434c0-1.398-1.032-2.445-2.426-2.445zm2.02-14.565h-4.05v12.186h4.05V8.993z"
            fill="#F0B00B"
            fill-rule="nonzero"
          />
        </svg>
      </span>
      <span
        class="emotion-3"
      />
    </div>
  </div>
</DocumentFragment>
`;

exports[`<Notification /> notification snapshot tests renders inverse warning state correctly GapFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  transition-property: opacity;
  transition-duration: 250ms;
  transition-timing-function: cubic-bezier(0.25, 0, 0.25, 1);
  transition-delay: 0ms;
  opacity: 1;
}

.emotion-1 {
  line-height: 1.25;
  padding: 0.5rem;
  margin-bottom: 1.125rem;
  border-style: solid;
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 0.8125rem;
  color: #FFFFFF;
  border-color: #FFC83A;
  border-width: 2px 2px 2px 8px;
}

.emotion-2 {
  display: inline-block;
  height: 20px;
  width: 20px;
  min-height: 20px;
  min-width: 20px;
}

.emotion-2 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-2 svg path {
  fill: #FFC83A;
}

.emotion-2 svg rect {
  fill: #FFC83A;
}

.emotion-3 {
  padding-left: 0.75rem;
}

<div
    class="emotion-0"
  >
    <div
      aria-live="polite"
      class="emotion-1"
      kind="warning"
    >
      <span
        aria-hidden="true"
        class="emotion-2"
      >
        <svg
          viewBox="0 0 33 33"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M18.117.402c.659.33 1.193.864 1.522 1.522l12.597 25.194a3.403 3.403 0 01-3.044 4.925H4a3.403 3.403 0 01-3.044-4.925L13.552 1.924A3.403 3.403 0 0118.117.402zm-1.516 23.156c-1.393 0-2.436 1.047-2.436 2.445 0 1.39 1.045 2.434 2.436 2.434s2.426-1.044 2.426-2.434c0-1.398-1.032-2.445-2.426-2.445zm2.02-14.565h-4.05v12.186h4.05V8.993z"
            fill="#F0B00B"
            fill-rule="nonzero"
          />
        </svg>
      </span>
      <span
        class="emotion-3"
      />
    </div>
  </div>
</DocumentFragment>
`;

exports[`<Notification /> notification snapshot tests renders inverse warning state correctly GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  transition-property: opacity;
  transition-duration: 250ms;
  transition-timing-function: cubic-bezier(0.25, 0, 0.25, 1);
  transition-delay: 0ms;
  opacity: 1;
}

.emotion-1 {
  line-height: 1.25;
  padding: 0.5rem;
  margin-bottom: 1.125rem;
  border-style: solid;
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 0.875rem;
  color: #FFFFFF;
  border-color: #FFC83A;
  border-width: 2px 2px 2px 8px;
}

.emotion-2 {
  display: inline-block;
  height: 20px;
  width: 20px;
  min-height: 20px;
  min-width: 20px;
}

.emotion-2 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-2 svg path {
  fill: #FFC83A;
}

.emotion-2 svg rect {
  fill: #FFC83A;
}

.emotion-3 {
  padding-left: 0.75rem;
}

<div
    class="emotion-0"
  >
    <div
      aria-live="polite"
      class="emotion-1"
      kind="warning"
    >
      <span
        aria-hidden="true"
        class="emotion-2"
      >
        <svg
          viewBox="0 0 33 33"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M18.117.402c.659.33 1.193.864 1.522 1.522l12.597 25.194a3.403 3.403 0 01-3.044 4.925H4a3.403 3.403 0 01-3.044-4.925L13.552 1.924A3.403 3.403 0 0118.117.402zm-1.516 23.156c-1.393 0-2.436 1.047-2.436 2.445 0 1.39 1.045 2.434 2.436 2.434s2.426-1.044 2.426-2.434c0-1.398-1.032-2.445-2.426-2.445zm2.02-14.565h-4.05v12.186h4.05V8.993z"
            fill="#F0B00B"
            fill-rule="nonzero"
          />
        </svg>
      </span>
      <span
        class="emotion-3"
      />
    </div>
  </div>
</DocumentFragment>
`;

exports[`<Notification /> notification snapshot tests renders inverse warning state correctly OldNavy 1`] = `
<DocumentFragment>
  .emotion-0 {
  transition-property: opacity;
  transition-duration: 250ms;
  transition-timing-function: cubic-bezier(0.25, 0, 0.25, 1);
  transition-delay: 0ms;
  opacity: 1;
}

.emotion-1 {
  line-height: 1.25;
  padding: 0.5rem;
  margin-bottom: 1.125rem;
  border-style: solid;
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  font-family: 'Gotham','Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 0.875rem;
  color: #FFFFFF;
  border-color: #FFC83A;
  border-width: 2px 2px 2px 8px;
}

.emotion-2 {
  display: inline-block;
  height: 20px;
  width: 20px;
  min-height: 20px;
  min-width: 20px;
}

.emotion-2 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-2 svg path {
  fill: #FFC83A;
}

.emotion-2 svg rect {
  fill: #FFC83A;
}

.emotion-3 {
  padding-left: 0.75rem;
}

<div
    class="emotion-0"
  >
    <div
      aria-live="polite"
      class="emotion-1"
      kind="warning"
    >
      <span
        aria-hidden="true"
        class="emotion-2"
      >
        <svg
          viewBox="0 0 33 33"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M18.117.402c.659.33 1.193.864 1.522 1.522l12.597 25.194a3.403 3.403 0 01-3.044 4.925H4a3.403 3.403 0 01-3.044-4.925L13.552 1.924A3.403 3.403 0 0118.117.402zm-1.516 23.156c-1.393 0-2.436 1.047-2.436 2.445 0 1.39 1.045 2.434 2.436 2.434s2.426-1.044 2.426-2.434c0-1.398-1.032-2.445-2.426-2.445zm2.02-14.565h-4.05v12.186h4.05V8.993z"
            fill="#F0B00B"
            fill-rule="nonzero"
          />
        </svg>
      </span>
      <span
        class="emotion-3"
      />
    </div>
  </div>
</DocumentFragment>
`;

exports[`<Notification /> notification snapshot tests renders inverse warning state correctly OldNavy in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  transition-property: opacity;
  transition-duration: 250ms;
  transition-timing-function: cubic-bezier(0.25, 0, 0.25, 1);
  transition-delay: 0ms;
  opacity: 1;
}

.emotion-1 {
  line-height: 1.25;
  padding: 0.5rem;
  margin-bottom: 1.125rem;
  border-style: solid;
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 0.875rem;
  color: #FFFFFF;
  border-color: #FFC83A;
  border-width: 2px 2px 2px 8px;
}

.emotion-2 {
  display: inline-block;
  height: 20px;
  width: 20px;
  min-height: 20px;
  min-width: 20px;
}

.emotion-2 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-2 svg path {
  fill: #FFC83A;
}

.emotion-2 svg rect {
  fill: #FFC83A;
}

.emotion-3 {
  padding-left: 0.75rem;
}

<div
    class="emotion-0"
  >
    <div
      aria-live="polite"
      class="emotion-1"
      kind="warning"
    >
      <span
        aria-hidden="true"
        class="emotion-2"
      >
        <svg
          viewBox="0 0 33 33"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M18.117.402c.659.33 1.193.864 1.522 1.522l12.597 25.194a3.403 3.403 0 01-3.044 4.925H4a3.403 3.403 0 01-3.044-4.925L13.552 1.924A3.403 3.403 0 0118.117.402zm-1.516 23.156c-1.393 0-2.436 1.047-2.436 2.445 0 1.39 1.045 2.434 2.436 2.434s2.426-1.044 2.426-2.434c0-1.398-1.032-2.445-2.426-2.445zm2.02-14.565h-4.05v12.186h4.05V8.993z"
            fill="#F0B00B"
            fill-rule="nonzero"
          />
        </svg>
      </span>
      <span
        class="emotion-3"
      />
    </div>
  </div>
</DocumentFragment>
`;

exports[`<Notification /> notification snapshot tests renders success state correctly Athleta 1`] = `
<DocumentFragment>
  .emotion-0 {
  transition-property: opacity;
  transition-duration: 250ms;
  transition-timing-function: cubic-bezier(0.25, 0, 0.25, 1);
  transition-delay: 0ms;
  opacity: 1;
}

.emotion-1 {
  line-height: 1.25;
  padding: 0.5rem;
  margin-bottom: 1.125rem;
  border-style: solid;
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  font-family: 'Avenir Next','Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 0.875rem;
  border-color: #16A816;
  border-width: 2px 2px 2px 8px;
}

.emotion-2 {
  display: inline-block;
  height: 20px;
  width: 20px;
  min-height: 20px;
  min-width: 20px;
}

.emotion-2 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-3 {
  padding-left: 0.75rem;
}

<div
    class="emotion-0"
  >
    <div
      aria-live="polite"
      class="emotion-1"
      kind="success"
    >
      <span
        aria-hidden="true"
        class="emotion-2"
      >
        <svg
          viewBox="0 0 32 32"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M16.118 0c8.837 0 16 7.163 16 16s-7.163 16-16 16-16-7.163-16-16 7.163-16 16-16zm6.552 9l-9.146 9.146-4.526-4.526-2.88 2.88 7.5 7.5 12.028-12.026L22.67 9z"
            fill="#16A816"
            fill-rule="nonzero"
          />
        </svg>
      </span>
      <span
        class="emotion-3"
      />
    </div>
  </div>
</DocumentFragment>
`;

exports[`<Notification /> notification snapshot tests renders success state correctly Athleta in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  transition-property: opacity;
  transition-duration: 250ms;
  transition-timing-function: cubic-bezier(0.25, 0, 0.25, 1);
  transition-delay: 0ms;
  opacity: 1;
}

.emotion-1 {
  line-height: 1.25;
  padding: 0.5rem;
  margin-bottom: 1.125rem;
  border-style: solid;
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 0.875rem;
  border-color: #16A816;
  border-width: 2px 2px 2px 8px;
}

.emotion-2 {
  display: inline-block;
  height: 20px;
  width: 20px;
  min-height: 20px;
  min-width: 20px;
}

.emotion-2 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-3 {
  padding-left: 0.75rem;
}

<div
    class="emotion-0"
  >
    <div
      aria-live="polite"
      class="emotion-1"
      kind="success"
    >
      <span
        aria-hidden="true"
        class="emotion-2"
      >
        <svg
          viewBox="0 0 32 32"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M16.118 0c8.837 0 16 7.163 16 16s-7.163 16-16 16-16-7.163-16-16 7.163-16 16-16zm6.552 9l-9.146 9.146-4.526-4.526-2.88 2.88 7.5 7.5 12.028-12.026L22.67 9z"
            fill="#16A816"
            fill-rule="nonzero"
          />
        </svg>
      </span>
      <span
        class="emotion-3"
      />
    </div>
  </div>
</DocumentFragment>
`;

exports[`<Notification /> notification snapshot tests renders success state correctly BananaRepublic 1`] = `
<DocumentFragment>
  .emotion-0 {
  transition-property: opacity;
  transition-duration: 250ms;
  transition-timing-function: cubic-bezier(0.25, 0, 0.25, 1);
  transition-delay: 0ms;
  opacity: 1;
}

.emotion-1 {
  line-height: 1.25;
  padding: 0.5rem;
  margin-bottom: 1.125rem;
  border-style: solid;
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  font-family: 'Lynstone',Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 0.8125rem;
  border-color: #16A816;
  border-width: 2px 2px 2px 8px;
}

.emotion-2 {
  display: inline-block;
  height: 20px;
  width: 20px;
  min-height: 20px;
  min-width: 20px;
}

.emotion-2 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-3 {
  padding-left: 0.75rem;
}

<div
    class="emotion-0"
  >
    <div
      aria-live="polite"
      class="emotion-1"
      kind="success"
    >
      <span
        aria-hidden="true"
        class="emotion-2"
      >
        <svg
          viewBox="0 0 32 32"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M16.118 0c8.837 0 16 7.163 16 16s-7.163 16-16 16-16-7.163-16-16 7.163-16 16-16zm6.552 9l-9.146 9.146-4.526-4.526-2.88 2.88 7.5 7.5 12.028-12.026L22.67 9z"
            fill="#16A816"
            fill-rule="nonzero"
          />
        </svg>
      </span>
      <span
        class="emotion-3"
      />
    </div>
  </div>
</DocumentFragment>
`;

exports[`<Notification /> notification snapshot tests renders success state correctly BananaRepublic in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  transition-property: opacity;
  transition-duration: 250ms;
  transition-timing-function: cubic-bezier(0.25, 0, 0.25, 1);
  transition-delay: 0ms;
  opacity: 1;
}

.emotion-1 {
  line-height: 1.25;
  padding: 0.5rem;
  margin-bottom: 1.125rem;
  border-style: solid;
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 0.875rem;
  border-color: #16A816;
  border-width: 2px 2px 2px 8px;
}

.emotion-2 {
  display: inline-block;
  height: 20px;
  width: 20px;
  min-height: 20px;
  min-width: 20px;
}

.emotion-2 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-3 {
  padding-left: 0.75rem;
}

<div
    class="emotion-0"
  >
    <div
      aria-live="polite"
      class="emotion-1"
      kind="success"
    >
      <span
        aria-hidden="true"
        class="emotion-2"
      >
        <svg
          viewBox="0 0 32 32"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M16.118 0c8.837 0 16 7.163 16 16s-7.163 16-16 16-16-7.163-16-16 7.163-16 16-16zm6.552 9l-9.146 9.146-4.526-4.526-2.88 2.88 7.5 7.5 12.028-12.026L22.67 9z"
            fill="#16A816"
            fill-rule="nonzero"
          />
        </svg>
      </span>
      <span
        class="emotion-3"
      />
    </div>
  </div>
</DocumentFragment>
`;

exports[`<Notification /> notification snapshot tests renders success state correctly BananaRepublicFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  transition-property: opacity;
  transition-duration: 250ms;
  transition-timing-function: cubic-bezier(0.25, 0, 0.25, 1);
  transition-delay: 0ms;
  opacity: 1;
}

.emotion-1 {
  line-height: 1.25;
  padding: 0.5rem;
  margin-bottom: 1.125rem;
  border-style: solid;
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  font-family: 'Lynstone',Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 0.8125rem;
  border-color: #16A816;
  border-width: 2px 2px 2px 8px;
}

.emotion-2 {
  display: inline-block;
  height: 20px;
  width: 20px;
  min-height: 20px;
  min-width: 20px;
}

.emotion-2 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-3 {
  padding-left: 0.75rem;
}

<div
    class="emotion-0"
  >
    <div
      aria-live="polite"
      class="emotion-1"
      kind="success"
    >
      <span
        aria-hidden="true"
        class="emotion-2"
      >
        <svg
          viewBox="0 0 32 32"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M16.118 0c8.837 0 16 7.163 16 16s-7.163 16-16 16-16-7.163-16-16 7.163-16 16-16zm6.552 9l-9.146 9.146-4.526-4.526-2.88 2.88 7.5 7.5 12.028-12.026L22.67 9z"
            fill="#16A816"
            fill-rule="nonzero"
          />
        </svg>
      </span>
      <span
        class="emotion-3"
      />
    </div>
  </div>
</DocumentFragment>
`;

exports[`<Notification /> notification snapshot tests renders success state correctly BananaRepublicFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  transition-property: opacity;
  transition-duration: 250ms;
  transition-timing-function: cubic-bezier(0.25, 0, 0.25, 1);
  transition-delay: 0ms;
  opacity: 1;
}

.emotion-1 {
  line-height: 1.25;
  padding: 0.5rem;
  margin-bottom: 1.125rem;
  border-style: solid;
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 0.875rem;
  border-color: #16A816;
  border-width: 2px 2px 2px 8px;
}

.emotion-2 {
  display: inline-block;
  height: 20px;
  width: 20px;
  min-height: 20px;
  min-width: 20px;
}

.emotion-2 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-3 {
  padding-left: 0.75rem;
}

<div
    class="emotion-0"
  >
    <div
      aria-live="polite"
      class="emotion-1"
      kind="success"
    >
      <span
        aria-hidden="true"
        class="emotion-2"
      >
        <svg
          viewBox="0 0 32 32"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M16.118 0c8.837 0 16 7.163 16 16s-7.163 16-16 16-16-7.163-16-16 7.163-16 16-16zm6.552 9l-9.146 9.146-4.526-4.526-2.88 2.88 7.5 7.5 12.028-12.026L22.67 9z"
            fill="#16A816"
            fill-rule="nonzero"
          />
        </svg>
      </span>
      <span
        class="emotion-3"
      />
    </div>
  </div>
</DocumentFragment>
`;

exports[`<Notification /> notification snapshot tests renders success state correctly Gap 1`] = `
<DocumentFragment>
  .emotion-0 {
  transition-property: opacity;
  transition-duration: 250ms;
  transition-timing-function: cubic-bezier(0.25, 0, 0.25, 1);
  transition-delay: 0ms;
  opacity: 1;
}

.emotion-1 {
  line-height: 1.25;
  padding: 0.5rem;
  margin-bottom: 1.125rem;
  border-style: solid;
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 0.8125rem;
  border-color: #16A816;
  border-width: 2px 2px 2px 8px;
}

.emotion-2 {
  display: inline-block;
  height: 20px;
  width: 20px;
  min-height: 20px;
  min-width: 20px;
}

.emotion-2 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-3 {
  padding-left: 0.75rem;
}

<div
    class="emotion-0"
  >
    <div
      aria-live="polite"
      class="emotion-1"
      kind="success"
    >
      <span
        aria-hidden="true"
        class="emotion-2"
      >
        <svg
          viewBox="0 0 32 32"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M16.118 0c8.837 0 16 7.163 16 16s-7.163 16-16 16-16-7.163-16-16 7.163-16 16-16zm6.552 9l-9.146 9.146-4.526-4.526-2.88 2.88 7.5 7.5 12.028-12.026L22.67 9z"
            fill="#16A816"
            fill-rule="nonzero"
          />
        </svg>
      </span>
      <span
        class="emotion-3"
      />
    </div>
  </div>
</DocumentFragment>
`;

exports[`<Notification /> notification snapshot tests renders success state correctly Gap in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  transition-property: opacity;
  transition-duration: 250ms;
  transition-timing-function: cubic-bezier(0.25, 0, 0.25, 1);
  transition-delay: 0ms;
  opacity: 1;
}

.emotion-1 {
  line-height: 1.25;
  padding: 0.5rem;
  margin-bottom: 1.125rem;
  border-style: solid;
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 0.875rem;
  border-color: #16A816;
  border-width: 2px 2px 2px 8px;
}

.emotion-2 {
  display: inline-block;
  height: 20px;
  width: 20px;
  min-height: 20px;
  min-width: 20px;
}

.emotion-2 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-3 {
  padding-left: 0.75rem;
}

<div
    class="emotion-0"
  >
    <div
      aria-live="polite"
      class="emotion-1"
      kind="success"
    >
      <span
        aria-hidden="true"
        class="emotion-2"
      >
        <svg
          viewBox="0 0 32 32"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M16.118 0c8.837 0 16 7.163 16 16s-7.163 16-16 16-16-7.163-16-16 7.163-16 16-16zm6.552 9l-9.146 9.146-4.526-4.526-2.88 2.88 7.5 7.5 12.028-12.026L22.67 9z"
            fill="#16A816"
            fill-rule="nonzero"
          />
        </svg>
      </span>
      <span
        class="emotion-3"
      />
    </div>
  </div>
</DocumentFragment>
`;

exports[`<Notification /> notification snapshot tests renders success state correctly GapFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  transition-property: opacity;
  transition-duration: 250ms;
  transition-timing-function: cubic-bezier(0.25, 0, 0.25, 1);
  transition-delay: 0ms;
  opacity: 1;
}

.emotion-1 {
  line-height: 1.25;
  padding: 0.5rem;
  margin-bottom: 1.125rem;
  border-style: solid;
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 0.8125rem;
  border-color: #16A816;
  border-width: 2px 2px 2px 8px;
}

.emotion-2 {
  display: inline-block;
  height: 20px;
  width: 20px;
  min-height: 20px;
  min-width: 20px;
}

.emotion-2 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-3 {
  padding-left: 0.75rem;
}

<div
    class="emotion-0"
  >
    <div
      aria-live="polite"
      class="emotion-1"
      kind="success"
    >
      <span
        aria-hidden="true"
        class="emotion-2"
      >
        <svg
          viewBox="0 0 32 32"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M16.118 0c8.837 0 16 7.163 16 16s-7.163 16-16 16-16-7.163-16-16 7.163-16 16-16zm6.552 9l-9.146 9.146-4.526-4.526-2.88 2.88 7.5 7.5 12.028-12.026L22.67 9z"
            fill="#16A816"
            fill-rule="nonzero"
          />
        </svg>
      </span>
      <span
        class="emotion-3"
      />
    </div>
  </div>
</DocumentFragment>
`;

exports[`<Notification /> notification snapshot tests renders success state correctly GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  transition-property: opacity;
  transition-duration: 250ms;
  transition-timing-function: cubic-bezier(0.25, 0, 0.25, 1);
  transition-delay: 0ms;
  opacity: 1;
}

.emotion-1 {
  line-height: 1.25;
  padding: 0.5rem;
  margin-bottom: 1.125rem;
  border-style: solid;
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 0.875rem;
  border-color: #16A816;
  border-width: 2px 2px 2px 8px;
}

.emotion-2 {
  display: inline-block;
  height: 20px;
  width: 20px;
  min-height: 20px;
  min-width: 20px;
}

.emotion-2 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-3 {
  padding-left: 0.75rem;
}

<div
    class="emotion-0"
  >
    <div
      aria-live="polite"
      class="emotion-1"
      kind="success"
    >
      <span
        aria-hidden="true"
        class="emotion-2"
      >
        <svg
          viewBox="0 0 32 32"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M16.118 0c8.837 0 16 7.163 16 16s-7.163 16-16 16-16-7.163-16-16 7.163-16 16-16zm6.552 9l-9.146 9.146-4.526-4.526-2.88 2.88 7.5 7.5 12.028-12.026L22.67 9z"
            fill="#16A816"
            fill-rule="nonzero"
          />
        </svg>
      </span>
      <span
        class="emotion-3"
      />
    </div>
  </div>
</DocumentFragment>
`;

exports[`<Notification /> notification snapshot tests renders success state correctly OldNavy 1`] = `
<DocumentFragment>
  .emotion-0 {
  transition-property: opacity;
  transition-duration: 250ms;
  transition-timing-function: cubic-bezier(0.25, 0, 0.25, 1);
  transition-delay: 0ms;
  opacity: 1;
}

.emotion-1 {
  line-height: 1.25;
  padding: 0.5rem;
  margin-bottom: 1.125rem;
  border-style: solid;
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  font-family: 'Gotham','Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 0.875rem;
  border-color: #16A816;
  border-width: 2px 2px 2px 8px;
}

.emotion-2 {
  display: inline-block;
  height: 20px;
  width: 20px;
  min-height: 20px;
  min-width: 20px;
}

.emotion-2 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-3 {
  padding-left: 0.75rem;
}

<div
    class="emotion-0"
  >
    <div
      aria-live="polite"
      class="emotion-1"
      kind="success"
    >
      <span
        aria-hidden="true"
        class="emotion-2"
      >
        <svg
          viewBox="0 0 32 32"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M16.118 0c8.837 0 16 7.163 16 16s-7.163 16-16 16-16-7.163-16-16 7.163-16 16-16zm6.552 9l-9.146 9.146-4.526-4.526-2.88 2.88 7.5 7.5 12.028-12.026L22.67 9z"
            fill="#16A816"
            fill-rule="nonzero"
          />
        </svg>
      </span>
      <span
        class="emotion-3"
      />
    </div>
  </div>
</DocumentFragment>
`;

exports[`<Notification /> notification snapshot tests renders success state correctly OldNavy in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  transition-property: opacity;
  transition-duration: 250ms;
  transition-timing-function: cubic-bezier(0.25, 0, 0.25, 1);
  transition-delay: 0ms;
  opacity: 1;
}

.emotion-1 {
  line-height: 1.25;
  padding: 0.5rem;
  margin-bottom: 1.125rem;
  border-style: solid;
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 0.875rem;
  border-color: #16A816;
  border-width: 2px 2px 2px 8px;
}

.emotion-2 {
  display: inline-block;
  height: 20px;
  width: 20px;
  min-height: 20px;
  min-width: 20px;
}

.emotion-2 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-3 {
  padding-left: 0.75rem;
}

<div
    class="emotion-0"
  >
    <div
      aria-live="polite"
      class="emotion-1"
      kind="success"
    >
      <span
        aria-hidden="true"
        class="emotion-2"
      >
        <svg
          viewBox="0 0 32 32"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M16.118 0c8.837 0 16 7.163 16 16s-7.163 16-16 16-16-7.163-16-16 7.163-16 16-16zm6.552 9l-9.146 9.146-4.526-4.526-2.88 2.88 7.5 7.5 12.028-12.026L22.67 9z"
            fill="#16A816"
            fill-rule="nonzero"
          />
        </svg>
      </span>
      <span
        class="emotion-3"
      />
    </div>
  </div>
</DocumentFragment>
`;

exports[`<Notification /> notification snapshot tests renders warning state correctly Athleta 1`] = `
<DocumentFragment>
  .emotion-0 {
  transition-property: opacity;
  transition-duration: 250ms;
  transition-timing-function: cubic-bezier(0.25, 0, 0.25, 1);
  transition-delay: 0ms;
  opacity: 1;
}

.emotion-1 {
  line-height: 1.25;
  padding: 0.5rem;
  margin-bottom: 1.125rem;
  border-style: solid;
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  font-family: 'Avenir Next','Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 0.875rem;
  border-color: #F0B00B;
  border-width: 2px 2px 2px 8px;
}

.emotion-2 {
  display: inline-block;
  height: 20px;
  width: 20px;
  min-height: 20px;
  min-width: 20px;
}

.emotion-2 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-3 {
  padding-left: 0.75rem;
}

<div
    class="emotion-0"
  >
    <div
      aria-live="polite"
      class="emotion-1"
      kind="warning"
    >
      <span
        aria-hidden="true"
        class="emotion-2"
      >
        <svg
          viewBox="0 0 33 33"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M18.117.402c.659.33 1.193.864 1.522 1.522l12.597 25.194a3.403 3.403 0 01-3.044 4.925H4a3.403 3.403 0 01-3.044-4.925L13.552 1.924A3.403 3.403 0 0118.117.402zm-1.516 23.156c-1.393 0-2.436 1.047-2.436 2.445 0 1.39 1.045 2.434 2.436 2.434s2.426-1.044 2.426-2.434c0-1.398-1.032-2.445-2.426-2.445zm2.02-14.565h-4.05v12.186h4.05V8.993z"
            fill="#F0B00B"
            fill-rule="nonzero"
          />
        </svg>
      </span>
      <span
        class="emotion-3"
      />
    </div>
  </div>
</DocumentFragment>
`;

exports[`<Notification /> notification snapshot tests renders warning state correctly Athleta in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  transition-property: opacity;
  transition-duration: 250ms;
  transition-timing-function: cubic-bezier(0.25, 0, 0.25, 1);
  transition-delay: 0ms;
  opacity: 1;
}

.emotion-1 {
  line-height: 1.25;
  padding: 0.5rem;
  margin-bottom: 1.125rem;
  border-style: solid;
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 0.875rem;
  border-color: #F0B00B;
  border-width: 2px 2px 2px 8px;
}

.emotion-2 {
  display: inline-block;
  height: 20px;
  width: 20px;
  min-height: 20px;
  min-width: 20px;
}

.emotion-2 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-3 {
  padding-left: 0.75rem;
}

<div
    class="emotion-0"
  >
    <div
      aria-live="polite"
      class="emotion-1"
      kind="warning"
    >
      <span
        aria-hidden="true"
        class="emotion-2"
      >
        <svg
          viewBox="0 0 33 33"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M18.117.402c.659.33 1.193.864 1.522 1.522l12.597 25.194a3.403 3.403 0 01-3.044 4.925H4a3.403 3.403 0 01-3.044-4.925L13.552 1.924A3.403 3.403 0 0118.117.402zm-1.516 23.156c-1.393 0-2.436 1.047-2.436 2.445 0 1.39 1.045 2.434 2.436 2.434s2.426-1.044 2.426-2.434c0-1.398-1.032-2.445-2.426-2.445zm2.02-14.565h-4.05v12.186h4.05V8.993z"
            fill="#F0B00B"
            fill-rule="nonzero"
          />
        </svg>
      </span>
      <span
        class="emotion-3"
      />
    </div>
  </div>
</DocumentFragment>
`;

exports[`<Notification /> notification snapshot tests renders warning state correctly BananaRepublic 1`] = `
<DocumentFragment>
  .emotion-0 {
  transition-property: opacity;
  transition-duration: 250ms;
  transition-timing-function: cubic-bezier(0.25, 0, 0.25, 1);
  transition-delay: 0ms;
  opacity: 1;
}

.emotion-1 {
  line-height: 1.25;
  padding: 0.5rem;
  margin-bottom: 1.125rem;
  border-style: solid;
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  font-family: 'Lynstone',Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 0.8125rem;
  border-color: #F0B00B;
  border-width: 2px 2px 2px 8px;
}

.emotion-2 {
  display: inline-block;
  height: 20px;
  width: 20px;
  min-height: 20px;
  min-width: 20px;
}

.emotion-2 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-3 {
  padding-left: 0.75rem;
}

<div
    class="emotion-0"
  >
    <div
      aria-live="polite"
      class="emotion-1"
      kind="warning"
    >
      <span
        aria-hidden="true"
        class="emotion-2"
      >
        <svg
          viewBox="0 0 33 33"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M18.117.402c.659.33 1.193.864 1.522 1.522l12.597 25.194a3.403 3.403 0 01-3.044 4.925H4a3.403 3.403 0 01-3.044-4.925L13.552 1.924A3.403 3.403 0 0118.117.402zm-1.516 23.156c-1.393 0-2.436 1.047-2.436 2.445 0 1.39 1.045 2.434 2.436 2.434s2.426-1.044 2.426-2.434c0-1.398-1.032-2.445-2.426-2.445zm2.02-14.565h-4.05v12.186h4.05V8.993z"
            fill="#F0B00B"
            fill-rule="nonzero"
          />
        </svg>
      </span>
      <span
        class="emotion-3"
      />
    </div>
  </div>
</DocumentFragment>
`;

exports[`<Notification /> notification snapshot tests renders warning state correctly BananaRepublic in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  transition-property: opacity;
  transition-duration: 250ms;
  transition-timing-function: cubic-bezier(0.25, 0, 0.25, 1);
  transition-delay: 0ms;
  opacity: 1;
}

.emotion-1 {
  line-height: 1.25;
  padding: 0.5rem;
  margin-bottom: 1.125rem;
  border-style: solid;
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 0.875rem;
  border-color: #F0B00B;
  border-width: 2px 2px 2px 8px;
}

.emotion-2 {
  display: inline-block;
  height: 20px;
  width: 20px;
  min-height: 20px;
  min-width: 20px;
}

.emotion-2 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-3 {
  padding-left: 0.75rem;
}

<div
    class="emotion-0"
  >
    <div
      aria-live="polite"
      class="emotion-1"
      kind="warning"
    >
      <span
        aria-hidden="true"
        class="emotion-2"
      >
        <svg
          viewBox="0 0 33 33"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M18.117.402c.659.33 1.193.864 1.522 1.522l12.597 25.194a3.403 3.403 0 01-3.044 4.925H4a3.403 3.403 0 01-3.044-4.925L13.552 1.924A3.403 3.403 0 0118.117.402zm-1.516 23.156c-1.393 0-2.436 1.047-2.436 2.445 0 1.39 1.045 2.434 2.436 2.434s2.426-1.044 2.426-2.434c0-1.398-1.032-2.445-2.426-2.445zm2.02-14.565h-4.05v12.186h4.05V8.993z"
            fill="#F0B00B"
            fill-rule="nonzero"
          />
        </svg>
      </span>
      <span
        class="emotion-3"
      />
    </div>
  </div>
</DocumentFragment>
`;

exports[`<Notification /> notification snapshot tests renders warning state correctly BananaRepublicFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  transition-property: opacity;
  transition-duration: 250ms;
  transition-timing-function: cubic-bezier(0.25, 0, 0.25, 1);
  transition-delay: 0ms;
  opacity: 1;
}

.emotion-1 {
  line-height: 1.25;
  padding: 0.5rem;
  margin-bottom: 1.125rem;
  border-style: solid;
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  font-family: 'Lynstone',Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 0.8125rem;
  border-color: #F0B00B;
  border-width: 2px 2px 2px 8px;
}

.emotion-2 {
  display: inline-block;
  height: 20px;
  width: 20px;
  min-height: 20px;
  min-width: 20px;
}

.emotion-2 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-3 {
  padding-left: 0.75rem;
}

<div
    class="emotion-0"
  >
    <div
      aria-live="polite"
      class="emotion-1"
      kind="warning"
    >
      <span
        aria-hidden="true"
        class="emotion-2"
      >
        <svg
          viewBox="0 0 33 33"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M18.117.402c.659.33 1.193.864 1.522 1.522l12.597 25.194a3.403 3.403 0 01-3.044 4.925H4a3.403 3.403 0 01-3.044-4.925L13.552 1.924A3.403 3.403 0 0118.117.402zm-1.516 23.156c-1.393 0-2.436 1.047-2.436 2.445 0 1.39 1.045 2.434 2.436 2.434s2.426-1.044 2.426-2.434c0-1.398-1.032-2.445-2.426-2.445zm2.02-14.565h-4.05v12.186h4.05V8.993z"
            fill="#F0B00B"
            fill-rule="nonzero"
          />
        </svg>
      </span>
      <span
        class="emotion-3"
      />
    </div>
  </div>
</DocumentFragment>
`;

exports[`<Notification /> notification snapshot tests renders warning state correctly BananaRepublicFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  transition-property: opacity;
  transition-duration: 250ms;
  transition-timing-function: cubic-bezier(0.25, 0, 0.25, 1);
  transition-delay: 0ms;
  opacity: 1;
}

.emotion-1 {
  line-height: 1.25;
  padding: 0.5rem;
  margin-bottom: 1.125rem;
  border-style: solid;
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 0.875rem;
  border-color: #F0B00B;
  border-width: 2px 2px 2px 8px;
}

.emotion-2 {
  display: inline-block;
  height: 20px;
  width: 20px;
  min-height: 20px;
  min-width: 20px;
}

.emotion-2 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-3 {
  padding-left: 0.75rem;
}

<div
    class="emotion-0"
  >
    <div
      aria-live="polite"
      class="emotion-1"
      kind="warning"
    >
      <span
        aria-hidden="true"
        class="emotion-2"
      >
        <svg
          viewBox="0 0 33 33"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M18.117.402c.659.33 1.193.864 1.522 1.522l12.597 25.194a3.403 3.403 0 01-3.044 4.925H4a3.403 3.403 0 01-3.044-4.925L13.552 1.924A3.403 3.403 0 0118.117.402zm-1.516 23.156c-1.393 0-2.436 1.047-2.436 2.445 0 1.39 1.045 2.434 2.436 2.434s2.426-1.044 2.426-2.434c0-1.398-1.032-2.445-2.426-2.445zm2.02-14.565h-4.05v12.186h4.05V8.993z"
            fill="#F0B00B"
            fill-rule="nonzero"
          />
        </svg>
      </span>
      <span
        class="emotion-3"
      />
    </div>
  </div>
</DocumentFragment>
`;

exports[`<Notification /> notification snapshot tests renders warning state correctly Gap 1`] = `
<DocumentFragment>
  .emotion-0 {
  transition-property: opacity;
  transition-duration: 250ms;
  transition-timing-function: cubic-bezier(0.25, 0, 0.25, 1);
  transition-delay: 0ms;
  opacity: 1;
}

.emotion-1 {
  line-height: 1.25;
  padding: 0.5rem;
  margin-bottom: 1.125rem;
  border-style: solid;
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 0.8125rem;
  border-color: #F0B00B;
  border-width: 2px 2px 2px 8px;
}

.emotion-2 {
  display: inline-block;
  height: 20px;
  width: 20px;
  min-height: 20px;
  min-width: 20px;
}

.emotion-2 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-3 {
  padding-left: 0.75rem;
}

<div
    class="emotion-0"
  >
    <div
      aria-live="polite"
      class="emotion-1"
      kind="warning"
    >
      <span
        aria-hidden="true"
        class="emotion-2"
      >
        <svg
          viewBox="0 0 33 33"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M18.117.402c.659.33 1.193.864 1.522 1.522l12.597 25.194a3.403 3.403 0 01-3.044 4.925H4a3.403 3.403 0 01-3.044-4.925L13.552 1.924A3.403 3.403 0 0118.117.402zm-1.516 23.156c-1.393 0-2.436 1.047-2.436 2.445 0 1.39 1.045 2.434 2.436 2.434s2.426-1.044 2.426-2.434c0-1.398-1.032-2.445-2.426-2.445zm2.02-14.565h-4.05v12.186h4.05V8.993z"
            fill="#F0B00B"
            fill-rule="nonzero"
          />
        </svg>
      </span>
      <span
        class="emotion-3"
      />
    </div>
  </div>
</DocumentFragment>
`;

exports[`<Notification /> notification snapshot tests renders warning state correctly Gap in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  transition-property: opacity;
  transition-duration: 250ms;
  transition-timing-function: cubic-bezier(0.25, 0, 0.25, 1);
  transition-delay: 0ms;
  opacity: 1;
}

.emotion-1 {
  line-height: 1.25;
  padding: 0.5rem;
  margin-bottom: 1.125rem;
  border-style: solid;
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 0.875rem;
  border-color: #F0B00B;
  border-width: 2px 2px 2px 8px;
}

.emotion-2 {
  display: inline-block;
  height: 20px;
  width: 20px;
  min-height: 20px;
  min-width: 20px;
}

.emotion-2 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-3 {
  padding-left: 0.75rem;
}

<div
    class="emotion-0"
  >
    <div
      aria-live="polite"
      class="emotion-1"
      kind="warning"
    >
      <span
        aria-hidden="true"
        class="emotion-2"
      >
        <svg
          viewBox="0 0 33 33"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M18.117.402c.659.33 1.193.864 1.522 1.522l12.597 25.194a3.403 3.403 0 01-3.044 4.925H4a3.403 3.403 0 01-3.044-4.925L13.552 1.924A3.403 3.403 0 0118.117.402zm-1.516 23.156c-1.393 0-2.436 1.047-2.436 2.445 0 1.39 1.045 2.434 2.436 2.434s2.426-1.044 2.426-2.434c0-1.398-1.032-2.445-2.426-2.445zm2.02-14.565h-4.05v12.186h4.05V8.993z"
            fill="#F0B00B"
            fill-rule="nonzero"
          />
        </svg>
      </span>
      <span
        class="emotion-3"
      />
    </div>
  </div>
</DocumentFragment>
`;

exports[`<Notification /> notification snapshot tests renders warning state correctly GapFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  transition-property: opacity;
  transition-duration: 250ms;
  transition-timing-function: cubic-bezier(0.25, 0, 0.25, 1);
  transition-delay: 0ms;
  opacity: 1;
}

.emotion-1 {
  line-height: 1.25;
  padding: 0.5rem;
  margin-bottom: 1.125rem;
  border-style: solid;
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 0.8125rem;
  border-color: #F0B00B;
  border-width: 2px 2px 2px 8px;
}

.emotion-2 {
  display: inline-block;
  height: 20px;
  width: 20px;
  min-height: 20px;
  min-width: 20px;
}

.emotion-2 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-3 {
  padding-left: 0.75rem;
}

<div
    class="emotion-0"
  >
    <div
      aria-live="polite"
      class="emotion-1"
      kind="warning"
    >
      <span
        aria-hidden="true"
        class="emotion-2"
      >
        <svg
          viewBox="0 0 33 33"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M18.117.402c.659.33 1.193.864 1.522 1.522l12.597 25.194a3.403 3.403 0 01-3.044 4.925H4a3.403 3.403 0 01-3.044-4.925L13.552 1.924A3.403 3.403 0 0118.117.402zm-1.516 23.156c-1.393 0-2.436 1.047-2.436 2.445 0 1.39 1.045 2.434 2.436 2.434s2.426-1.044 2.426-2.434c0-1.398-1.032-2.445-2.426-2.445zm2.02-14.565h-4.05v12.186h4.05V8.993z"
            fill="#F0B00B"
            fill-rule="nonzero"
          />
        </svg>
      </span>
      <span
        class="emotion-3"
      />
    </div>
  </div>
</DocumentFragment>
`;

exports[`<Notification /> notification snapshot tests renders warning state correctly GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  transition-property: opacity;
  transition-duration: 250ms;
  transition-timing-function: cubic-bezier(0.25, 0, 0.25, 1);
  transition-delay: 0ms;
  opacity: 1;
}

.emotion-1 {
  line-height: 1.25;
  padding: 0.5rem;
  margin-bottom: 1.125rem;
  border-style: solid;
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 0.875rem;
  border-color: #F0B00B;
  border-width: 2px 2px 2px 8px;
}

.emotion-2 {
  display: inline-block;
  height: 20px;
  width: 20px;
  min-height: 20px;
  min-width: 20px;
}

.emotion-2 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-3 {
  padding-left: 0.75rem;
}

<div
    class="emotion-0"
  >
    <div
      aria-live="polite"
      class="emotion-1"
      kind="warning"
    >
      <span
        aria-hidden="true"
        class="emotion-2"
      >
        <svg
          viewBox="0 0 33 33"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M18.117.402c.659.33 1.193.864 1.522 1.522l12.597 25.194a3.403 3.403 0 01-3.044 4.925H4a3.403 3.403 0 01-3.044-4.925L13.552 1.924A3.403 3.403 0 0118.117.402zm-1.516 23.156c-1.393 0-2.436 1.047-2.436 2.445 0 1.39 1.045 2.434 2.436 2.434s2.426-1.044 2.426-2.434c0-1.398-1.032-2.445-2.426-2.445zm2.02-14.565h-4.05v12.186h4.05V8.993z"
            fill="#F0B00B"
            fill-rule="nonzero"
          />
        </svg>
      </span>
      <span
        class="emotion-3"
      />
    </div>
  </div>
</DocumentFragment>
`;

exports[`<Notification /> notification snapshot tests renders warning state correctly OldNavy 1`] = `
<DocumentFragment>
  .emotion-0 {
  transition-property: opacity;
  transition-duration: 250ms;
  transition-timing-function: cubic-bezier(0.25, 0, 0.25, 1);
  transition-delay: 0ms;
  opacity: 1;
}

.emotion-1 {
  line-height: 1.25;
  padding: 0.5rem;
  margin-bottom: 1.125rem;
  border-style: solid;
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  font-family: 'Gotham','Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 0.875rem;
  border-color: #F0B00B;
  border-width: 2px 2px 2px 8px;
}

.emotion-2 {
  display: inline-block;
  height: 20px;
  width: 20px;
  min-height: 20px;
  min-width: 20px;
}

.emotion-2 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-3 {
  padding-left: 0.75rem;
}

<div
    class="emotion-0"
  >
    <div
      aria-live="polite"
      class="emotion-1"
      kind="warning"
    >
      <span
        aria-hidden="true"
        class="emotion-2"
      >
        <svg
          viewBox="0 0 33 33"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M18.117.402c.659.33 1.193.864 1.522 1.522l12.597 25.194a3.403 3.403 0 01-3.044 4.925H4a3.403 3.403 0 01-3.044-4.925L13.552 1.924A3.403 3.403 0 0118.117.402zm-1.516 23.156c-1.393 0-2.436 1.047-2.436 2.445 0 1.39 1.045 2.434 2.436 2.434s2.426-1.044 2.426-2.434c0-1.398-1.032-2.445-2.426-2.445zm2.02-14.565h-4.05v12.186h4.05V8.993z"
            fill="#F0B00B"
            fill-rule="nonzero"
          />
        </svg>
      </span>
      <span
        class="emotion-3"
      />
    </div>
  </div>
</DocumentFragment>
`;

exports[`<Notification /> notification snapshot tests renders warning state correctly OldNavy in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  transition-property: opacity;
  transition-duration: 250ms;
  transition-timing-function: cubic-bezier(0.25, 0, 0.25, 1);
  transition-delay: 0ms;
  opacity: 1;
}

.emotion-1 {
  line-height: 1.25;
  padding: 0.5rem;
  margin-bottom: 1.125rem;
  border-style: solid;
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 0.875rem;
  border-color: #F0B00B;
  border-width: 2px 2px 2px 8px;
}

.emotion-2 {
  display: inline-block;
  height: 20px;
  width: 20px;
  min-height: 20px;
  min-width: 20px;
}

.emotion-2 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-3 {
  padding-left: 0.75rem;
}

<div
    class="emotion-0"
  >
    <div
      aria-live="polite"
      class="emotion-1"
      kind="warning"
    >
      <span
        aria-hidden="true"
        class="emotion-2"
      >
        <svg
          viewBox="0 0 33 33"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M18.117.402c.659.33 1.193.864 1.522 1.522l12.597 25.194a3.403 3.403 0 01-3.044 4.925H4a3.403 3.403 0 01-3.044-4.925L13.552 1.924A3.403 3.403 0 0118.117.402zm-1.516 23.156c-1.393 0-2.436 1.047-2.436 2.445 0 1.39 1.045 2.434 2.436 2.434s2.426-1.044 2.426-2.434c0-1.398-1.032-2.445-2.426-2.445zm2.02-14.565h-4.05v12.186h4.05V8.993z"
            fill="#F0B00B"
            fill-rule="nonzero"
          />
        </svg>
      </span>
      <span
        class="emotion-3"
      />
    </div>
  </div>
</DocumentFragment>
`;
