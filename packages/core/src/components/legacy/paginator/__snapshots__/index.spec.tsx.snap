// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Paginator snapshots for inverse styles renders default state correctly Athleta 1`] = `
<DocumentFragment>
  .emotion-0 {
  clear: both;
  text-align: center;
}

.emotion-1 {
  background: none;
  border: none;
  width: auto;
  margin: 0;
  padding: 0;
  text-align: inherit;
  font: inherit;
  display: inline-block;
  vertical-align: middle;
  box-sizing: border-box;
  height: 2.5em;
  width: 2.5em;
  cursor: pointer;
}

.emotion-1 path {
  fill: #FFF;
}

.emotion-1:hover path,
.emotion-1:active path {
  fill: #FFF;
}

.emotion-2 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  padding-bottom: 2px;
}

.emotion-3 {
  vertical-align: middle;
  display: inline-block;
}

.emotion-4 {
  font-family: 'Avenir Next','Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  color: #FFFFFF;
  font-weight: 600;
  letter-spacing: 0.5px;
  font-size: 1rem;
  display: inline-block;
}

<nav
    aria-label="Paginator"
    class="emotion-0"
  >
    <button
      aria-label="Previous Page"
      class="emotion-1"
    >
      <div
        class="emotion-2"
      >
        <svg
          class="emotion-3"
          enable-background="new -134 82.3 7 12"
          height="12"
          viewBox="-134 82.3 7 12"
          width="7"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M-127 92.3l-4.3-4.4 4.2-4.2-1.4-1.4-5.5 5.6 5.6 5.7 1.4-1.3z"
            fill="#979797"
          />
        </svg>
      </div>
    </button>
    <div
      class="emotion-4"
    >
      <span>
        Page 3
      </span>
      <span>
         of 
      </span>
      4
    </div>
    <button
      aria-label="Next Page"
      class="emotion-1"
    >
      <div
        class="emotion-2"
      >
        <svg
          enable-background="new -134 82.3 7 12"
          height="12"
          viewBox="-134 82.3 7 12"
          width="7"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M-134 92.3l4.2-4.4-4.1-4.2 1.4-1.4 5.5 5.6-5.6 5.7-1.4-1.3z"
          />
        </svg>
      </div>
    </button>
  </nav>
</DocumentFragment>
`;

exports[`Paginator snapshots for inverse styles renders default state correctly Athleta in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  clear: both;
  text-align: center;
}

.emotion-1 {
  background: none;
  border: none;
  width: auto;
  margin: 0;
  padding: 0;
  text-align: inherit;
  font: inherit;
  display: inline-block;
  vertical-align: middle;
  box-sizing: border-box;
  height: 2.5em;
  width: 2.5em;
  cursor: pointer;
}

.emotion-1 path {
  fill: #FFFFFF;
}

.emotion-1:hover path,
.emotion-1:active path {
  fill: #FFF;
}

.emotion-2 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  padding-bottom: 2px;
}

.emotion-3 {
  vertical-align: middle;
  display: inline-block;
}

.emotion-4 {
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  color: #FFFFFF;
  font-size: 1rem;
  display: inline-block;
}

<nav
    aria-label="Paginator"
    class="emotion-0"
  >
    <button
      aria-label="Previous Page"
      class="emotion-1"
    >
      <div
        class="emotion-2"
      >
        <svg
          class="emotion-3"
          enable-background="new -134 82.3 7 12"
          height="12"
          viewBox="-134 82.3 7 12"
          width="7"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M-127 92.3l-4.3-4.4 4.2-4.2-1.4-1.4-5.5 5.6 5.6 5.7 1.4-1.3z"
            fill="#979797"
          />
        </svg>
      </div>
    </button>
    <div
      class="emotion-4"
    >
      <span>
        Page 3
      </span>
      <span>
         of 
      </span>
      4
    </div>
    <button
      aria-label="Next Page"
      class="emotion-1"
    >
      <div
        class="emotion-2"
      >
        <svg
          enable-background="new -134 82.3 7 12"
          height="12"
          viewBox="-134 82.3 7 12"
          width="7"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M-134 92.3l4.2-4.4-4.1-4.2 1.4-1.4 5.5 5.6-5.6 5.7-1.4-1.3z"
          />
        </svg>
      </div>
    </button>
  </nav>
</DocumentFragment>
`;

exports[`Paginator snapshots for inverse styles renders default state correctly BananaRepublic 1`] = `
<DocumentFragment>
  .emotion-0 {
  clear: both;
  text-align: center;
}

.emotion-1 {
  background: none;
  border: none;
  width: auto;
  margin: 0;
  padding: 0;
  text-align: inherit;
  font: inherit;
  display: inline-block;
  vertical-align: middle;
  box-sizing: border-box;
  height: 2.5em;
  width: 2.5em;
  cursor: pointer;
}

.emotion-1 path {
  fill: #F6F4EB;
}

.emotion-1:hover path,
.emotion-1:active path {
  fill: #F6F4EB;
}

.emotion-2 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  padding-bottom: 2px;
}

.emotion-3 {
  vertical-align: middle;
  display: inline-block;
}

.emotion-4 {
  font-family: 'Lynstone',Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  color: #F6F4EB;
  font-size: 0.875rem;
  display: inline-block;
}

<nav
    aria-label="Paginator"
    class="emotion-0"
  >
    <button
      aria-label="Previous Page"
      class="emotion-1"
    >
      <div
        class="emotion-2"
      >
        <svg
          class="emotion-3"
          enable-background="new -134 82.3 7 12"
          height="12"
          viewBox="-134 82.3 7 12"
          width="7"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M-127 92.3l-4.3-4.4 4.2-4.2-1.4-1.4-5.5 5.6 5.6 5.7 1.4-1.3z"
            fill="#979797"
          />
        </svg>
      </div>
    </button>
    <div
      class="emotion-4"
    >
      <span>
        Page 3
      </span>
      <span>
         of 
      </span>
      4
    </div>
    <button
      aria-label="Next Page"
      class="emotion-1"
    >
      <div
        class="emotion-2"
      >
        <svg
          enable-background="new -134 82.3 7 12"
          height="12"
          viewBox="-134 82.3 7 12"
          width="7"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M-134 92.3l4.2-4.4-4.1-4.2 1.4-1.4 5.5 5.6-5.6 5.7-1.4-1.3z"
          />
        </svg>
      </div>
    </button>
  </nav>
</DocumentFragment>
`;

exports[`Paginator snapshots for inverse styles renders default state correctly BananaRepublic in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  clear: both;
  text-align: center;
}

.emotion-1 {
  background: none;
  border: none;
  width: auto;
  margin: 0;
  padding: 0;
  text-align: inherit;
  font: inherit;
  display: inline-block;
  vertical-align: middle;
  box-sizing: border-box;
  height: 2.5em;
  width: 2.5em;
  cursor: pointer;
}

.emotion-1 path {
  fill: #FFFFFF;
}

.emotion-1:hover path,
.emotion-1:active path {
  fill: #FFF;
}

.emotion-2 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  padding-bottom: 2px;
}

.emotion-3 {
  vertical-align: middle;
  display: inline-block;
}

.emotion-4 {
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  color: #F6F4EB;
  font-size: 1rem;
  display: inline-block;
}

<nav
    aria-label="Paginator"
    class="emotion-0"
  >
    <button
      aria-label="Previous Page"
      class="emotion-1"
    >
      <div
        class="emotion-2"
      >
        <svg
          class="emotion-3"
          enable-background="new -134 82.3 7 12"
          height="12"
          viewBox="-134 82.3 7 12"
          width="7"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M-127 92.3l-4.3-4.4 4.2-4.2-1.4-1.4-5.5 5.6 5.6 5.7 1.4-1.3z"
            fill="#979797"
          />
        </svg>
      </div>
    </button>
    <div
      class="emotion-4"
    >
      <span>
        Page 3
      </span>
      <span>
         of 
      </span>
      4
    </div>
    <button
      aria-label="Next Page"
      class="emotion-1"
    >
      <div
        class="emotion-2"
      >
        <svg
          enable-background="new -134 82.3 7 12"
          height="12"
          viewBox="-134 82.3 7 12"
          width="7"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M-134 92.3l4.2-4.4-4.1-4.2 1.4-1.4 5.5 5.6-5.6 5.7-1.4-1.3z"
          />
        </svg>
      </div>
    </button>
  </nav>
</DocumentFragment>
`;

exports[`Paginator snapshots for inverse styles renders default state correctly BananaRepublicFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  clear: both;
  text-align: center;
}

.emotion-1 {
  background: none;
  border: none;
  width: auto;
  margin: 0;
  padding: 0;
  text-align: inherit;
  font: inherit;
  display: inline-block;
  vertical-align: middle;
  box-sizing: border-box;
  height: 2.5em;
  width: 2.5em;
  cursor: pointer;
}

.emotion-1 path {
  fill: #F6F4EB;
}

.emotion-1:hover path,
.emotion-1:active path {
  fill: #F6F4EB;
}

.emotion-2 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  padding-bottom: 2px;
}

.emotion-3 {
  vertical-align: middle;
  display: inline-block;
}

.emotion-4 {
  font-family: 'Lynstone',Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  color: #F6F4EB;
  font-size: 0.875rem;
  display: inline-block;
}

<nav
    aria-label="Paginator"
    class="emotion-0"
  >
    <button
      aria-label="Previous Page"
      class="emotion-1"
    >
      <div
        class="emotion-2"
      >
        <svg
          class="emotion-3"
          enable-background="new -134 82.3 7 12"
          height="12"
          viewBox="-134 82.3 7 12"
          width="7"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M-127 92.3l-4.3-4.4 4.2-4.2-1.4-1.4-5.5 5.6 5.6 5.7 1.4-1.3z"
            fill="#979797"
          />
        </svg>
      </div>
    </button>
    <div
      class="emotion-4"
    >
      <span>
        Page 3
      </span>
      <span>
         of 
      </span>
      4
    </div>
    <button
      aria-label="Next Page"
      class="emotion-1"
    >
      <div
        class="emotion-2"
      >
        <svg
          enable-background="new -134 82.3 7 12"
          height="12"
          viewBox="-134 82.3 7 12"
          width="7"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M-134 92.3l4.2-4.4-4.1-4.2 1.4-1.4 5.5 5.6-5.6 5.7-1.4-1.3z"
          />
        </svg>
      </div>
    </button>
  </nav>
</DocumentFragment>
`;

exports[`Paginator snapshots for inverse styles renders default state correctly BananaRepublicFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  clear: both;
  text-align: center;
}

.emotion-1 {
  background: none;
  border: none;
  width: auto;
  margin: 0;
  padding: 0;
  text-align: inherit;
  font: inherit;
  display: inline-block;
  vertical-align: middle;
  box-sizing: border-box;
  height: 2.5em;
  width: 2.5em;
  cursor: pointer;
}

.emotion-1 path {
  fill: #FFFFFF;
}

.emotion-1:hover path,
.emotion-1:active path {
  fill: #FFF;
}

.emotion-2 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  padding-bottom: 2px;
}

.emotion-3 {
  vertical-align: middle;
  display: inline-block;
}

.emotion-4 {
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  color: #F6F4EB;
  font-size: 1rem;
  display: inline-block;
}

<nav
    aria-label="Paginator"
    class="emotion-0"
  >
    <button
      aria-label="Previous Page"
      class="emotion-1"
    >
      <div
        class="emotion-2"
      >
        <svg
          class="emotion-3"
          enable-background="new -134 82.3 7 12"
          height="12"
          viewBox="-134 82.3 7 12"
          width="7"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M-127 92.3l-4.3-4.4 4.2-4.2-1.4-1.4-5.5 5.6 5.6 5.7 1.4-1.3z"
            fill="#979797"
          />
        </svg>
      </div>
    </button>
    <div
      class="emotion-4"
    >
      <span>
        Page 3
      </span>
      <span>
         of 
      </span>
      4
    </div>
    <button
      aria-label="Next Page"
      class="emotion-1"
    >
      <div
        class="emotion-2"
      >
        <svg
          enable-background="new -134 82.3 7 12"
          height="12"
          viewBox="-134 82.3 7 12"
          width="7"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M-134 92.3l4.2-4.4-4.1-4.2 1.4-1.4 5.5 5.6-5.6 5.7-1.4-1.3z"
          />
        </svg>
      </div>
    </button>
  </nav>
</DocumentFragment>
`;

exports[`Paginator snapshots for inverse styles renders default state correctly Gap 1`] = `
<DocumentFragment>
  .emotion-0 {
  clear: both;
  text-align: center;
}

.emotion-1 {
  background: none;
  border: none;
  width: auto;
  margin: 0;
  padding: 0;
  text-align: inherit;
  font: inherit;
  display: inline-block;
  vertical-align: middle;
  box-sizing: border-box;
  height: 2.5em;
  width: 2.5em;
  cursor: pointer;
}

.emotion-1 path {
  fill: #FFFFFF;
}

.emotion-1:hover path,
.emotion-1:active path {
  fill: #FFFFFF;
}

.emotion-2 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  padding-bottom: 2px;
}

.emotion-3 {
  vertical-align: middle;
  display: inline-block;
}

.emotion-4 {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  color: #FFFFFF;
  font-size: 1.067rem;
  display: inline-block;
}

<nav
    aria-label="Paginator"
    class="emotion-0"
  >
    <button
      aria-label="Previous Page"
      class="emotion-1"
    >
      <div
        class="emotion-2"
      >
        <svg
          class="emotion-3"
          enable-background="new -134 82.3 7 12"
          height="12"
          viewBox="-134 82.3 7 12"
          width="7"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M-127 92.3l-4.3-4.4 4.2-4.2-1.4-1.4-5.5 5.6 5.6 5.7 1.4-1.3z"
            fill="#979797"
          />
        </svg>
      </div>
    </button>
    <div
      class="emotion-4"
    >
      <span>
        Page 3
      </span>
      <span>
         of 
      </span>
      4
    </div>
    <button
      aria-label="Next Page"
      class="emotion-1"
    >
      <div
        class="emotion-2"
      >
        <svg
          enable-background="new -134 82.3 7 12"
          height="12"
          viewBox="-134 82.3 7 12"
          width="7"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M-134 92.3l4.2-4.4-4.1-4.2 1.4-1.4 5.5 5.6-5.6 5.7-1.4-1.3z"
          />
        </svg>
      </div>
    </button>
  </nav>
</DocumentFragment>
`;

exports[`Paginator snapshots for inverse styles renders default state correctly Gap in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  clear: both;
  text-align: center;
}

.emotion-1 {
  background: none;
  border: none;
  width: auto;
  margin: 0;
  padding: 0;
  text-align: inherit;
  font: inherit;
  display: inline-block;
  vertical-align: middle;
  box-sizing: border-box;
  height: 2.5em;
  width: 2.5em;
  cursor: pointer;
}

.emotion-1 path {
  fill: #FFFFFF;
}

.emotion-1:hover path,
.emotion-1:active path {
  fill: #FFF;
}

.emotion-2 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  padding-bottom: 2px;
}

.emotion-3 {
  vertical-align: middle;
  display: inline-block;
}

.emotion-4 {
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  color: #FFFFFF;
  font-size: 1rem;
  display: inline-block;
}

<nav
    aria-label="Paginator"
    class="emotion-0"
  >
    <button
      aria-label="Previous Page"
      class="emotion-1"
    >
      <div
        class="emotion-2"
      >
        <svg
          class="emotion-3"
          enable-background="new -134 82.3 7 12"
          height="12"
          viewBox="-134 82.3 7 12"
          width="7"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M-127 92.3l-4.3-4.4 4.2-4.2-1.4-1.4-5.5 5.6 5.6 5.7 1.4-1.3z"
            fill="#979797"
          />
        </svg>
      </div>
    </button>
    <div
      class="emotion-4"
    >
      <span>
        Page 3
      </span>
      <span>
         of 
      </span>
      4
    </div>
    <button
      aria-label="Next Page"
      class="emotion-1"
    >
      <div
        class="emotion-2"
      >
        <svg
          enable-background="new -134 82.3 7 12"
          height="12"
          viewBox="-134 82.3 7 12"
          width="7"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M-134 92.3l4.2-4.4-4.1-4.2 1.4-1.4 5.5 5.6-5.6 5.7-1.4-1.3z"
          />
        </svg>
      </div>
    </button>
  </nav>
</DocumentFragment>
`;

exports[`Paginator snapshots for inverse styles renders default state correctly GapFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  clear: both;
  text-align: center;
}

.emotion-1 {
  background: none;
  border: none;
  width: auto;
  margin: 0;
  padding: 0;
  text-align: inherit;
  font: inherit;
  display: inline-block;
  vertical-align: middle;
  box-sizing: border-box;
  height: 2.5em;
  width: 2.5em;
  cursor: pointer;
}

.emotion-1 path {
  fill: #FFFFFF;
}

.emotion-1:hover path,
.emotion-1:active path {
  fill: #FFFFFF;
}

.emotion-2 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  padding-bottom: 2px;
}

.emotion-3 {
  vertical-align: middle;
  display: inline-block;
}

.emotion-4 {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  color: #FFFFFF;
  font-size: 1.067rem;
  display: inline-block;
}

<nav
    aria-label="Paginator"
    class="emotion-0"
  >
    <button
      aria-label="Previous Page"
      class="emotion-1"
    >
      <div
        class="emotion-2"
      >
        <svg
          class="emotion-3"
          enable-background="new -134 82.3 7 12"
          height="12"
          viewBox="-134 82.3 7 12"
          width="7"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M-127 92.3l-4.3-4.4 4.2-4.2-1.4-1.4-5.5 5.6 5.6 5.7 1.4-1.3z"
            fill="#979797"
          />
        </svg>
      </div>
    </button>
    <div
      class="emotion-4"
    >
      <span>
        Page 3
      </span>
      <span>
         of 
      </span>
      4
    </div>
    <button
      aria-label="Next Page"
      class="emotion-1"
    >
      <div
        class="emotion-2"
      >
        <svg
          enable-background="new -134 82.3 7 12"
          height="12"
          viewBox="-134 82.3 7 12"
          width="7"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M-134 92.3l4.2-4.4-4.1-4.2 1.4-1.4 5.5 5.6-5.6 5.7-1.4-1.3z"
          />
        </svg>
      </div>
    </button>
  </nav>
</DocumentFragment>
`;

exports[`Paginator snapshots for inverse styles renders default state correctly GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  clear: both;
  text-align: center;
}

.emotion-1 {
  background: none;
  border: none;
  width: auto;
  margin: 0;
  padding: 0;
  text-align: inherit;
  font: inherit;
  display: inline-block;
  vertical-align: middle;
  box-sizing: border-box;
  height: 2.5em;
  width: 2.5em;
  cursor: pointer;
}

.emotion-1 path {
  fill: #FFFFFF;
}

.emotion-1:hover path,
.emotion-1:active path {
  fill: #FFF;
}

.emotion-2 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  padding-bottom: 2px;
}

.emotion-3 {
  vertical-align: middle;
  display: inline-block;
}

.emotion-4 {
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  color: #FFFFFF;
  font-size: 1rem;
  display: inline-block;
}

<nav
    aria-label="Paginator"
    class="emotion-0"
  >
    <button
      aria-label="Previous Page"
      class="emotion-1"
    >
      <div
        class="emotion-2"
      >
        <svg
          class="emotion-3"
          enable-background="new -134 82.3 7 12"
          height="12"
          viewBox="-134 82.3 7 12"
          width="7"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M-127 92.3l-4.3-4.4 4.2-4.2-1.4-1.4-5.5 5.6 5.6 5.7 1.4-1.3z"
            fill="#979797"
          />
        </svg>
      </div>
    </button>
    <div
      class="emotion-4"
    >
      <span>
        Page 3
      </span>
      <span>
         of 
      </span>
      4
    </div>
    <button
      aria-label="Next Page"
      class="emotion-1"
    >
      <div
        class="emotion-2"
      >
        <svg
          enable-background="new -134 82.3 7 12"
          height="12"
          viewBox="-134 82.3 7 12"
          width="7"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M-134 92.3l4.2-4.4-4.1-4.2 1.4-1.4 5.5 5.6-5.6 5.7-1.4-1.3z"
          />
        </svg>
      </div>
    </button>
  </nav>
</DocumentFragment>
`;

exports[`Paginator snapshots for inverse styles renders default state correctly OldNavy 1`] = `
<DocumentFragment>
  .emotion-0 {
  clear: both;
  text-align: center;
}

.emotion-1 {
  background: none;
  border: none;
  width: auto;
  margin: 0;
  padding: 0;
  text-align: inherit;
  font: inherit;
  display: inline-block;
  vertical-align: middle;
  box-sizing: border-box;
  height: 2.5em;
  width: 2.5em;
  cursor: pointer;
}

.emotion-1 path {
  fill: #FFFFFF;
}

.emotion-1:hover path,
.emotion-1:active path {
  fill: #FFFFFF;
}

.emotion-2 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  padding-bottom: 2px;
}

.emotion-3 {
  vertical-align: middle;
  display: inline-block;
}

.emotion-4 {
  font-family: 'Gotham','Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  color: #FFFFFF;
  font-size: 1.067rem;
  display: inline-block;
}

<nav
    aria-label="Paginator"
    class="emotion-0"
  >
    <button
      aria-label="Previous Page"
      class="emotion-1"
    >
      <div
        class="emotion-2"
      >
        <svg
          class="emotion-3"
          enable-background="new -134 82.3 7 12"
          height="12"
          viewBox="-134 82.3 7 12"
          width="7"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M-127 92.3l-4.3-4.4 4.2-4.2-1.4-1.4-5.5 5.6 5.6 5.7 1.4-1.3z"
            fill="#979797"
          />
        </svg>
      </div>
    </button>
    <div
      class="emotion-4"
    >
      <span>
        Page 3
      </span>
      <span>
         of 
      </span>
      4
    </div>
    <button
      aria-label="Next Page"
      class="emotion-1"
    >
      <div
        class="emotion-2"
      >
        <svg
          enable-background="new -134 82.3 7 12"
          height="12"
          viewBox="-134 82.3 7 12"
          width="7"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M-134 92.3l4.2-4.4-4.1-4.2 1.4-1.4 5.5 5.6-5.6 5.7-1.4-1.3z"
          />
        </svg>
      </div>
    </button>
  </nav>
</DocumentFragment>
`;

exports[`Paginator snapshots for inverse styles renders default state correctly OldNavy in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  clear: both;
  text-align: center;
}

.emotion-1 {
  background: none;
  border: none;
  width: auto;
  margin: 0;
  padding: 0;
  text-align: inherit;
  font: inherit;
  display: inline-block;
  vertical-align: middle;
  box-sizing: border-box;
  height: 2.5em;
  width: 2.5em;
  cursor: pointer;
}

.emotion-1 path {
  fill: #FFFFFF;
}

.emotion-1:hover path,
.emotion-1:active path {
  fill: #FFF;
}

.emotion-2 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  padding-bottom: 2px;
}

.emotion-3 {
  vertical-align: middle;
  display: inline-block;
}

.emotion-4 {
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  color: #FFFFFF;
  font-size: 1rem;
  display: inline-block;
}

<nav
    aria-label="Paginator"
    class="emotion-0"
  >
    <button
      aria-label="Previous Page"
      class="emotion-1"
    >
      <div
        class="emotion-2"
      >
        <svg
          class="emotion-3"
          enable-background="new -134 82.3 7 12"
          height="12"
          viewBox="-134 82.3 7 12"
          width="7"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M-127 92.3l-4.3-4.4 4.2-4.2-1.4-1.4-5.5 5.6 5.6 5.7 1.4-1.3z"
            fill="#979797"
          />
        </svg>
      </div>
    </button>
    <div
      class="emotion-4"
    >
      <span>
        Page 3
      </span>
      <span>
         of 
      </span>
      4
    </div>
    <button
      aria-label="Next Page"
      class="emotion-1"
    >
      <div
        class="emotion-2"
      >
        <svg
          enable-background="new -134 82.3 7 12"
          height="12"
          viewBox="-134 82.3 7 12"
          width="7"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M-134 92.3l4.2-4.4-4.1-4.2 1.4-1.4 5.5 5.6-5.6 5.7-1.4-1.3z"
          />
        </svg>
      </div>
    </button>
  </nav>
</DocumentFragment>
`;

exports[`Paginator snapshots renders alignment center state correctly Athleta 1`] = `
<DocumentFragment>
  .emotion-0 {
  clear: both;
  text-align: center;
}

.emotion-1 {
  background: none;
  border: none;
  width: auto;
  margin: 0;
  padding: 0;
  text-align: inherit;
  font: inherit;
  display: inline-block;
  vertical-align: middle;
  box-sizing: border-box;
  height: 2.5em;
  width: 2.5em;
  cursor: pointer;
}

.emotion-1 path {
  fill: #979797;
}

.emotion-1:hover path,
.emotion-1:active path {
  fill: #979797;
}

.emotion-2 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  padding-bottom: 2px;
}

.emotion-3 {
  vertical-align: middle;
  display: inline-block;
}

.emotion-4 {
  font-family: 'Avenir Next','Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  color: #666;
  font-weight: 600;
  letter-spacing: 0.5px;
  font-size: 1rem;
  display: inline-block;
}

<nav
    aria-label="Paginator"
    class="emotion-0"
  >
    <button
      aria-label="Previous Page"
      class="emotion-1"
    >
      <div
        class="emotion-2"
      >
        <svg
          class="emotion-3"
          enable-background="new -134 82.3 7 12"
          height="12"
          viewBox="-134 82.3 7 12"
          width="7"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M-127 92.3l-4.3-4.4 4.2-4.2-1.4-1.4-5.5 5.6 5.6 5.7 1.4-1.3z"
            fill="#979797"
          />
        </svg>
      </div>
    </button>
    <div
      class="emotion-4"
    >
      <span>
        Page 3
      </span>
      <span>
         of 
      </span>
      4
    </div>
    <button
      aria-label="Next Page"
      class="emotion-1"
    >
      <div
        class="emotion-2"
      >
        <svg
          enable-background="new -134 82.3 7 12"
          height="12"
          viewBox="-134 82.3 7 12"
          width="7"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M-134 92.3l4.2-4.4-4.1-4.2 1.4-1.4 5.5 5.6-5.6 5.7-1.4-1.3z"
          />
        </svg>
      </div>
    </button>
  </nav>
</DocumentFragment>
`;

exports[`Paginator snapshots renders alignment center state correctly Athleta in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  clear: both;
  text-align: center;
}

.emotion-1 {
  background: none;
  border: none;
  width: auto;
  margin: 0;
  padding: 0;
  text-align: inherit;
  font: inherit;
  display: inline-block;
  vertical-align: middle;
  box-sizing: border-box;
  height: 2.5em;
  width: 2.5em;
  cursor: pointer;
}

.emotion-1 path {
  fill: #666;
}

.emotion-1:hover path,
.emotion-1:active path {
  fill: #979797;
}

.emotion-2 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  padding-bottom: 2px;
}

.emotion-3 {
  vertical-align: middle;
  display: inline-block;
}

.emotion-4 {
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  color: #666;
  font-size: 1rem;
  display: inline-block;
}

<nav
    aria-label="Paginator"
    class="emotion-0"
  >
    <button
      aria-label="Previous Page"
      class="emotion-1"
    >
      <div
        class="emotion-2"
      >
        <svg
          class="emotion-3"
          enable-background="new -134 82.3 7 12"
          height="12"
          viewBox="-134 82.3 7 12"
          width="7"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M-127 92.3l-4.3-4.4 4.2-4.2-1.4-1.4-5.5 5.6 5.6 5.7 1.4-1.3z"
            fill="#979797"
          />
        </svg>
      </div>
    </button>
    <div
      class="emotion-4"
    >
      <span>
        Page 3
      </span>
      <span>
         of 
      </span>
      4
    </div>
    <button
      aria-label="Next Page"
      class="emotion-1"
    >
      <div
        class="emotion-2"
      >
        <svg
          enable-background="new -134 82.3 7 12"
          height="12"
          viewBox="-134 82.3 7 12"
          width="7"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M-134 92.3l4.2-4.4-4.1-4.2 1.4-1.4 5.5 5.6-5.6 5.7-1.4-1.3z"
          />
        </svg>
      </div>
    </button>
  </nav>
</DocumentFragment>
`;

exports[`Paginator snapshots renders alignment center state correctly BananaRepublic 1`] = `
<DocumentFragment>
  .emotion-0 {
  clear: both;
  text-align: center;
}

.emotion-1 {
  background: none;
  border: none;
  width: auto;
  margin: 0;
  padding: 0;
  text-align: inherit;
  font: inherit;
  display: inline-block;
  vertical-align: middle;
  box-sizing: border-box;
  height: 2.5em;
  width: 2.5em;
  cursor: pointer;
}

.emotion-1 path {
  fill: #000;
}

.emotion-1:hover path,
.emotion-1:active path {
  fill: #000;
}

.emotion-2 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  padding-bottom: 2px;
}

.emotion-3 {
  vertical-align: middle;
  display: inline-block;
}

.emotion-4 {
  font-family: 'Lynstone',Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  color: #666;
  font-size: 0.875rem;
  display: inline-block;
}

<nav
    aria-label="Paginator"
    class="emotion-0"
  >
    <button
      aria-label="Previous Page"
      class="emotion-1"
    >
      <div
        class="emotion-2"
      >
        <svg
          class="emotion-3"
          enable-background="new -134 82.3 7 12"
          height="12"
          viewBox="-134 82.3 7 12"
          width="7"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M-127 92.3l-4.3-4.4 4.2-4.2-1.4-1.4-5.5 5.6 5.6 5.7 1.4-1.3z"
            fill="#979797"
          />
        </svg>
      </div>
    </button>
    <div
      class="emotion-4"
    >
      <span>
        Page 3
      </span>
      <span>
         of 
      </span>
      4
    </div>
    <button
      aria-label="Next Page"
      class="emotion-1"
    >
      <div
        class="emotion-2"
      >
        <svg
          enable-background="new -134 82.3 7 12"
          height="12"
          viewBox="-134 82.3 7 12"
          width="7"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M-134 92.3l4.2-4.4-4.1-4.2 1.4-1.4 5.5 5.6-5.6 5.7-1.4-1.3z"
          />
        </svg>
      </div>
    </button>
  </nav>
</DocumentFragment>
`;

exports[`Paginator snapshots renders alignment center state correctly BananaRepublic in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  clear: both;
  text-align: center;
}

.emotion-1 {
  background: none;
  border: none;
  width: auto;
  margin: 0;
  padding: 0;
  text-align: inherit;
  font: inherit;
  display: inline-block;
  vertical-align: middle;
  box-sizing: border-box;
  height: 2.5em;
  width: 2.5em;
  cursor: pointer;
}

.emotion-1 path {
  fill: #666;
}

.emotion-1:hover path,
.emotion-1:active path {
  fill: #979797;
}

.emotion-2 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  padding-bottom: 2px;
}

.emotion-3 {
  vertical-align: middle;
  display: inline-block;
}

.emotion-4 {
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  color: #666;
  font-size: 1rem;
  display: inline-block;
}

<nav
    aria-label="Paginator"
    class="emotion-0"
  >
    <button
      aria-label="Previous Page"
      class="emotion-1"
    >
      <div
        class="emotion-2"
      >
        <svg
          class="emotion-3"
          enable-background="new -134 82.3 7 12"
          height="12"
          viewBox="-134 82.3 7 12"
          width="7"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M-127 92.3l-4.3-4.4 4.2-4.2-1.4-1.4-5.5 5.6 5.6 5.7 1.4-1.3z"
            fill="#979797"
          />
        </svg>
      </div>
    </button>
    <div
      class="emotion-4"
    >
      <span>
        Page 3
      </span>
      <span>
         of 
      </span>
      4
    </div>
    <button
      aria-label="Next Page"
      class="emotion-1"
    >
      <div
        class="emotion-2"
      >
        <svg
          enable-background="new -134 82.3 7 12"
          height="12"
          viewBox="-134 82.3 7 12"
          width="7"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M-134 92.3l4.2-4.4-4.1-4.2 1.4-1.4 5.5 5.6-5.6 5.7-1.4-1.3z"
          />
        </svg>
      </div>
    </button>
  </nav>
</DocumentFragment>
`;

exports[`Paginator snapshots renders alignment center state correctly BananaRepublicFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  clear: both;
  text-align: center;
}

.emotion-1 {
  background: none;
  border: none;
  width: auto;
  margin: 0;
  padding: 0;
  text-align: inherit;
  font: inherit;
  display: inline-block;
  vertical-align: middle;
  box-sizing: border-box;
  height: 2.5em;
  width: 2.5em;
  cursor: pointer;
}

.emotion-1 path {
  fill: #000;
}

.emotion-1:hover path,
.emotion-1:active path {
  fill: #000;
}

.emotion-2 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  padding-bottom: 2px;
}

.emotion-3 {
  vertical-align: middle;
  display: inline-block;
}

.emotion-4 {
  font-family: 'Lynstone',Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  color: #666;
  font-size: 0.875rem;
  display: inline-block;
}

<nav
    aria-label="Paginator"
    class="emotion-0"
  >
    <button
      aria-label="Previous Page"
      class="emotion-1"
    >
      <div
        class="emotion-2"
      >
        <svg
          class="emotion-3"
          enable-background="new -134 82.3 7 12"
          height="12"
          viewBox="-134 82.3 7 12"
          width="7"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M-127 92.3l-4.3-4.4 4.2-4.2-1.4-1.4-5.5 5.6 5.6 5.7 1.4-1.3z"
            fill="#979797"
          />
        </svg>
      </div>
    </button>
    <div
      class="emotion-4"
    >
      <span>
        Page 3
      </span>
      <span>
         of 
      </span>
      4
    </div>
    <button
      aria-label="Next Page"
      class="emotion-1"
    >
      <div
        class="emotion-2"
      >
        <svg
          enable-background="new -134 82.3 7 12"
          height="12"
          viewBox="-134 82.3 7 12"
          width="7"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M-134 92.3l4.2-4.4-4.1-4.2 1.4-1.4 5.5 5.6-5.6 5.7-1.4-1.3z"
          />
        </svg>
      </div>
    </button>
  </nav>
</DocumentFragment>
`;

exports[`Paginator snapshots renders alignment center state correctly BananaRepublicFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  clear: both;
  text-align: center;
}

.emotion-1 {
  background: none;
  border: none;
  width: auto;
  margin: 0;
  padding: 0;
  text-align: inherit;
  font: inherit;
  display: inline-block;
  vertical-align: middle;
  box-sizing: border-box;
  height: 2.5em;
  width: 2.5em;
  cursor: pointer;
}

.emotion-1 path {
  fill: #666;
}

.emotion-1:hover path,
.emotion-1:active path {
  fill: #979797;
}

.emotion-2 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  padding-bottom: 2px;
}

.emotion-3 {
  vertical-align: middle;
  display: inline-block;
}

.emotion-4 {
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  color: #666;
  font-size: 1rem;
  display: inline-block;
}

<nav
    aria-label="Paginator"
    class="emotion-0"
  >
    <button
      aria-label="Previous Page"
      class="emotion-1"
    >
      <div
        class="emotion-2"
      >
        <svg
          class="emotion-3"
          enable-background="new -134 82.3 7 12"
          height="12"
          viewBox="-134 82.3 7 12"
          width="7"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M-127 92.3l-4.3-4.4 4.2-4.2-1.4-1.4-5.5 5.6 5.6 5.7 1.4-1.3z"
            fill="#979797"
          />
        </svg>
      </div>
    </button>
    <div
      class="emotion-4"
    >
      <span>
        Page 3
      </span>
      <span>
         of 
      </span>
      4
    </div>
    <button
      aria-label="Next Page"
      class="emotion-1"
    >
      <div
        class="emotion-2"
      >
        <svg
          enable-background="new -134 82.3 7 12"
          height="12"
          viewBox="-134 82.3 7 12"
          width="7"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M-134 92.3l4.2-4.4-4.1-4.2 1.4-1.4 5.5 5.6-5.6 5.7-1.4-1.3z"
          />
        </svg>
      </div>
    </button>
  </nav>
</DocumentFragment>
`;

exports[`Paginator snapshots renders alignment center state correctly Gap 1`] = `
<DocumentFragment>
  .emotion-0 {
  clear: both;
  text-align: center;
}

.emotion-1 {
  background: none;
  border: none;
  width: auto;
  margin: 0;
  padding: 0;
  text-align: inherit;
  font: inherit;
  display: inline-block;
  vertical-align: middle;
  box-sizing: border-box;
  height: 2.5em;
  width: 2.5em;
  cursor: pointer;
}

.emotion-1 path {
  fill: #0A5694;
}

.emotion-1:hover path,
.emotion-1:active path {
  fill: #002554;
}

.emotion-2 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  padding-bottom: 2px;
}

.emotion-3 {
  vertical-align: middle;
  display: inline-block;
}

.emotion-4 {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  color: #666;
  font-size: 1.067rem;
  display: inline-block;
}

<nav
    aria-label="Paginator"
    class="emotion-0"
  >
    <button
      aria-label="Previous Page"
      class="emotion-1"
    >
      <div
        class="emotion-2"
      >
        <svg
          class="emotion-3"
          enable-background="new -134 82.3 7 12"
          height="12"
          viewBox="-134 82.3 7 12"
          width="7"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M-127 92.3l-4.3-4.4 4.2-4.2-1.4-1.4-5.5 5.6 5.6 5.7 1.4-1.3z"
            fill="#979797"
          />
        </svg>
      </div>
    </button>
    <div
      class="emotion-4"
    >
      <span>
        Page 3
      </span>
      <span>
         of 
      </span>
      4
    </div>
    <button
      aria-label="Next Page"
      class="emotion-1"
    >
      <div
        class="emotion-2"
      >
        <svg
          enable-background="new -134 82.3 7 12"
          height="12"
          viewBox="-134 82.3 7 12"
          width="7"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M-134 92.3l4.2-4.4-4.1-4.2 1.4-1.4 5.5 5.6-5.6 5.7-1.4-1.3z"
          />
        </svg>
      </div>
    </button>
  </nav>
</DocumentFragment>
`;

exports[`Paginator snapshots renders alignment center state correctly Gap in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  clear: both;
  text-align: center;
}

.emotion-1 {
  background: none;
  border: none;
  width: auto;
  margin: 0;
  padding: 0;
  text-align: inherit;
  font: inherit;
  display: inline-block;
  vertical-align: middle;
  box-sizing: border-box;
  height: 2.5em;
  width: 2.5em;
  cursor: pointer;
}

.emotion-1 path {
  fill: #666;
}

.emotion-1:hover path,
.emotion-1:active path {
  fill: #979797;
}

.emotion-2 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  padding-bottom: 2px;
}

.emotion-3 {
  vertical-align: middle;
  display: inline-block;
}

.emotion-4 {
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  color: #666;
  font-size: 1rem;
  display: inline-block;
}

<nav
    aria-label="Paginator"
    class="emotion-0"
  >
    <button
      aria-label="Previous Page"
      class="emotion-1"
    >
      <div
        class="emotion-2"
      >
        <svg
          class="emotion-3"
          enable-background="new -134 82.3 7 12"
          height="12"
          viewBox="-134 82.3 7 12"
          width="7"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M-127 92.3l-4.3-4.4 4.2-4.2-1.4-1.4-5.5 5.6 5.6 5.7 1.4-1.3z"
            fill="#979797"
          />
        </svg>
      </div>
    </button>
    <div
      class="emotion-4"
    >
      <span>
        Page 3
      </span>
      <span>
         of 
      </span>
      4
    </div>
    <button
      aria-label="Next Page"
      class="emotion-1"
    >
      <div
        class="emotion-2"
      >
        <svg
          enable-background="new -134 82.3 7 12"
          height="12"
          viewBox="-134 82.3 7 12"
          width="7"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M-134 92.3l4.2-4.4-4.1-4.2 1.4-1.4 5.5 5.6-5.6 5.7-1.4-1.3z"
          />
        </svg>
      </div>
    </button>
  </nav>
</DocumentFragment>
`;

exports[`Paginator snapshots renders alignment center state correctly GapFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  clear: both;
  text-align: center;
}

.emotion-1 {
  background: none;
  border: none;
  width: auto;
  margin: 0;
  padding: 0;
  text-align: inherit;
  font: inherit;
  display: inline-block;
  vertical-align: middle;
  box-sizing: border-box;
  height: 2.5em;
  width: 2.5em;
  cursor: pointer;
}

.emotion-1 path {
  fill: #0A5694;
}

.emotion-1:hover path,
.emotion-1:active path {
  fill: #002554;
}

.emotion-2 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  padding-bottom: 2px;
}

.emotion-3 {
  vertical-align: middle;
  display: inline-block;
}

.emotion-4 {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  color: #666;
  font-size: 1.067rem;
  display: inline-block;
}

<nav
    aria-label="Paginator"
    class="emotion-0"
  >
    <button
      aria-label="Previous Page"
      class="emotion-1"
    >
      <div
        class="emotion-2"
      >
        <svg
          class="emotion-3"
          enable-background="new -134 82.3 7 12"
          height="12"
          viewBox="-134 82.3 7 12"
          width="7"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M-127 92.3l-4.3-4.4 4.2-4.2-1.4-1.4-5.5 5.6 5.6 5.7 1.4-1.3z"
            fill="#979797"
          />
        </svg>
      </div>
    </button>
    <div
      class="emotion-4"
    >
      <span>
        Page 3
      </span>
      <span>
         of 
      </span>
      4
    </div>
    <button
      aria-label="Next Page"
      class="emotion-1"
    >
      <div
        class="emotion-2"
      >
        <svg
          enable-background="new -134 82.3 7 12"
          height="12"
          viewBox="-134 82.3 7 12"
          width="7"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M-134 92.3l4.2-4.4-4.1-4.2 1.4-1.4 5.5 5.6-5.6 5.7-1.4-1.3z"
          />
        </svg>
      </div>
    </button>
  </nav>
</DocumentFragment>
`;

exports[`Paginator snapshots renders alignment center state correctly GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  clear: both;
  text-align: center;
}

.emotion-1 {
  background: none;
  border: none;
  width: auto;
  margin: 0;
  padding: 0;
  text-align: inherit;
  font: inherit;
  display: inline-block;
  vertical-align: middle;
  box-sizing: border-box;
  height: 2.5em;
  width: 2.5em;
  cursor: pointer;
}

.emotion-1 path {
  fill: #666;
}

.emotion-1:hover path,
.emotion-1:active path {
  fill: #979797;
}

.emotion-2 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  padding-bottom: 2px;
}

.emotion-3 {
  vertical-align: middle;
  display: inline-block;
}

.emotion-4 {
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  color: #666;
  font-size: 1rem;
  display: inline-block;
}

<nav
    aria-label="Paginator"
    class="emotion-0"
  >
    <button
      aria-label="Previous Page"
      class="emotion-1"
    >
      <div
        class="emotion-2"
      >
        <svg
          class="emotion-3"
          enable-background="new -134 82.3 7 12"
          height="12"
          viewBox="-134 82.3 7 12"
          width="7"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M-127 92.3l-4.3-4.4 4.2-4.2-1.4-1.4-5.5 5.6 5.6 5.7 1.4-1.3z"
            fill="#979797"
          />
        </svg>
      </div>
    </button>
    <div
      class="emotion-4"
    >
      <span>
        Page 3
      </span>
      <span>
         of 
      </span>
      4
    </div>
    <button
      aria-label="Next Page"
      class="emotion-1"
    >
      <div
        class="emotion-2"
      >
        <svg
          enable-background="new -134 82.3 7 12"
          height="12"
          viewBox="-134 82.3 7 12"
          width="7"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M-134 92.3l4.2-4.4-4.1-4.2 1.4-1.4 5.5 5.6-5.6 5.7-1.4-1.3z"
          />
        </svg>
      </div>
    </button>
  </nav>
</DocumentFragment>
`;

exports[`Paginator snapshots renders alignment center state correctly OldNavy 1`] = `
<DocumentFragment>
  .emotion-0 {
  clear: both;
  text-align: center;
}

.emotion-1 {
  background: none;
  border: none;
  width: auto;
  margin: 0;
  padding: 0;
  text-align: inherit;
  font: inherit;
  display: inline-block;
  vertical-align: middle;
  box-sizing: border-box;
  height: 2.5em;
  width: 2.5em;
  cursor: pointer;
}

.emotion-1 path {
  fill: #003764;
}

.emotion-1:hover path,
.emotion-1:active path {
  fill: #003764;
}

.emotion-2 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  padding-bottom: 2px;
}

.emotion-3 {
  vertical-align: middle;
  display: inline-block;
}

.emotion-4 {
  font-family: 'Gotham','Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  color: #666;
  font-size: 1.067rem;
  display: inline-block;
}

<nav
    aria-label="Paginator"
    class="emotion-0"
  >
    <button
      aria-label="Previous Page"
      class="emotion-1"
    >
      <div
        class="emotion-2"
      >
        <svg
          class="emotion-3"
          enable-background="new -134 82.3 7 12"
          height="12"
          viewBox="-134 82.3 7 12"
          width="7"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M-127 92.3l-4.3-4.4 4.2-4.2-1.4-1.4-5.5 5.6 5.6 5.7 1.4-1.3z"
            fill="#979797"
          />
        </svg>
      </div>
    </button>
    <div
      class="emotion-4"
    >
      <span>
        Page 3
      </span>
      <span>
         of 
      </span>
      4
    </div>
    <button
      aria-label="Next Page"
      class="emotion-1"
    >
      <div
        class="emotion-2"
      >
        <svg
          enable-background="new -134 82.3 7 12"
          height="12"
          viewBox="-134 82.3 7 12"
          width="7"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M-134 92.3l4.2-4.4-4.1-4.2 1.4-1.4 5.5 5.6-5.6 5.7-1.4-1.3z"
          />
        </svg>
      </div>
    </button>
  </nav>
</DocumentFragment>
`;

exports[`Paginator snapshots renders alignment center state correctly OldNavy in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  clear: both;
  text-align: center;
}

.emotion-1 {
  background: none;
  border: none;
  width: auto;
  margin: 0;
  padding: 0;
  text-align: inherit;
  font: inherit;
  display: inline-block;
  vertical-align: middle;
  box-sizing: border-box;
  height: 2.5em;
  width: 2.5em;
  cursor: pointer;
}

.emotion-1 path {
  fill: #666;
}

.emotion-1:hover path,
.emotion-1:active path {
  fill: #979797;
}

.emotion-2 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  padding-bottom: 2px;
}

.emotion-3 {
  vertical-align: middle;
  display: inline-block;
}

.emotion-4 {
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  color: #666;
  font-size: 1rem;
  display: inline-block;
}

<nav
    aria-label="Paginator"
    class="emotion-0"
  >
    <button
      aria-label="Previous Page"
      class="emotion-1"
    >
      <div
        class="emotion-2"
      >
        <svg
          class="emotion-3"
          enable-background="new -134 82.3 7 12"
          height="12"
          viewBox="-134 82.3 7 12"
          width="7"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M-127 92.3l-4.3-4.4 4.2-4.2-1.4-1.4-5.5 5.6 5.6 5.7 1.4-1.3z"
            fill="#979797"
          />
        </svg>
      </div>
    </button>
    <div
      class="emotion-4"
    >
      <span>
        Page 3
      </span>
      <span>
         of 
      </span>
      4
    </div>
    <button
      aria-label="Next Page"
      class="emotion-1"
    >
      <div
        class="emotion-2"
      >
        <svg
          enable-background="new -134 82.3 7 12"
          height="12"
          viewBox="-134 82.3 7 12"
          width="7"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M-134 92.3l4.2-4.4-4.1-4.2 1.4-1.4 5.5 5.6-5.6 5.7-1.4-1.3z"
          />
        </svg>
      </div>
    </button>
  </nav>
</DocumentFragment>
`;

exports[`Paginator snapshots renders alignment left state correctly Athleta 1`] = `
<DocumentFragment>
  .emotion-0 {
  clear: both;
  text-align: left;
}

.emotion-1 {
  background: none;
  border: none;
  width: auto;
  margin: 0;
  padding: 0;
  text-align: inherit;
  font: inherit;
  display: inline-block;
  vertical-align: middle;
  box-sizing: border-box;
  height: 2.5em;
  width: 2.5em;
  cursor: pointer;
}

.emotion-1 path {
  fill: #979797;
}

.emotion-1:hover path,
.emotion-1:active path {
  fill: #979797;
}

.emotion-2 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  padding-bottom: 2px;
}

.emotion-3 {
  vertical-align: middle;
  display: inline-block;
}

.emotion-4 {
  font-family: 'Avenir Next','Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  color: #666;
  font-weight: 600;
  letter-spacing: 0.5px;
  font-size: 1rem;
  display: inline-block;
}

<nav
    aria-label="Paginator"
    class="emotion-0"
  >
    <button
      aria-label="Previous Page"
      class="emotion-1"
    >
      <div
        class="emotion-2"
      >
        <svg
          class="emotion-3"
          enable-background="new -134 82.3 7 12"
          height="12"
          viewBox="-134 82.3 7 12"
          width="7"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M-127 92.3l-4.3-4.4 4.2-4.2-1.4-1.4-5.5 5.6 5.6 5.7 1.4-1.3z"
            fill="#979797"
          />
        </svg>
      </div>
    </button>
    <div
      class="emotion-4"
    >
      <span>
        Page 3
      </span>
      <span>
         of 
      </span>
      4
    </div>
    <button
      aria-label="Next Page"
      class="emotion-1"
    >
      <div
        class="emotion-2"
      >
        <svg
          enable-background="new -134 82.3 7 12"
          height="12"
          viewBox="-134 82.3 7 12"
          width="7"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M-134 92.3l4.2-4.4-4.1-4.2 1.4-1.4 5.5 5.6-5.6 5.7-1.4-1.3z"
          />
        </svg>
      </div>
    </button>
  </nav>
</DocumentFragment>
`;

exports[`Paginator snapshots renders alignment left state correctly Athleta in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  clear: both;
  text-align: left;
}

.emotion-1 {
  background: none;
  border: none;
  width: auto;
  margin: 0;
  padding: 0;
  text-align: inherit;
  font: inherit;
  display: inline-block;
  vertical-align: middle;
  box-sizing: border-box;
  height: 2.5em;
  width: 2.5em;
  cursor: pointer;
}

.emotion-1 path {
  fill: #666;
}

.emotion-1:hover path,
.emotion-1:active path {
  fill: #979797;
}

.emotion-2 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  padding-bottom: 2px;
}

.emotion-3 {
  vertical-align: middle;
  display: inline-block;
}

.emotion-4 {
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  color: #666;
  font-size: 1rem;
  display: inline-block;
}

<nav
    aria-label="Paginator"
    class="emotion-0"
  >
    <button
      aria-label="Previous Page"
      class="emotion-1"
    >
      <div
        class="emotion-2"
      >
        <svg
          class="emotion-3"
          enable-background="new -134 82.3 7 12"
          height="12"
          viewBox="-134 82.3 7 12"
          width="7"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M-127 92.3l-4.3-4.4 4.2-4.2-1.4-1.4-5.5 5.6 5.6 5.7 1.4-1.3z"
            fill="#979797"
          />
        </svg>
      </div>
    </button>
    <div
      class="emotion-4"
    >
      <span>
        Page 3
      </span>
      <span>
         of 
      </span>
      4
    </div>
    <button
      aria-label="Next Page"
      class="emotion-1"
    >
      <div
        class="emotion-2"
      >
        <svg
          enable-background="new -134 82.3 7 12"
          height="12"
          viewBox="-134 82.3 7 12"
          width="7"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M-134 92.3l4.2-4.4-4.1-4.2 1.4-1.4 5.5 5.6-5.6 5.7-1.4-1.3z"
          />
        </svg>
      </div>
    </button>
  </nav>
</DocumentFragment>
`;

exports[`Paginator snapshots renders alignment left state correctly BananaRepublic 1`] = `
<DocumentFragment>
  .emotion-0 {
  clear: both;
  text-align: left;
}

.emotion-1 {
  background: none;
  border: none;
  width: auto;
  margin: 0;
  padding: 0;
  text-align: inherit;
  font: inherit;
  display: inline-block;
  vertical-align: middle;
  box-sizing: border-box;
  height: 2.5em;
  width: 2.5em;
  cursor: pointer;
}

.emotion-1 path {
  fill: #000;
}

.emotion-1:hover path,
.emotion-1:active path {
  fill: #000;
}

.emotion-2 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  padding-bottom: 2px;
}

.emotion-3 {
  vertical-align: middle;
  display: inline-block;
}

.emotion-4 {
  font-family: 'Lynstone',Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  color: #666;
  font-size: 0.875rem;
  display: inline-block;
}

<nav
    aria-label="Paginator"
    class="emotion-0"
  >
    <button
      aria-label="Previous Page"
      class="emotion-1"
    >
      <div
        class="emotion-2"
      >
        <svg
          class="emotion-3"
          enable-background="new -134 82.3 7 12"
          height="12"
          viewBox="-134 82.3 7 12"
          width="7"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M-127 92.3l-4.3-4.4 4.2-4.2-1.4-1.4-5.5 5.6 5.6 5.7 1.4-1.3z"
            fill="#979797"
          />
        </svg>
      </div>
    </button>
    <div
      class="emotion-4"
    >
      <span>
        Page 3
      </span>
      <span>
         of 
      </span>
      4
    </div>
    <button
      aria-label="Next Page"
      class="emotion-1"
    >
      <div
        class="emotion-2"
      >
        <svg
          enable-background="new -134 82.3 7 12"
          height="12"
          viewBox="-134 82.3 7 12"
          width="7"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M-134 92.3l4.2-4.4-4.1-4.2 1.4-1.4 5.5 5.6-5.6 5.7-1.4-1.3z"
          />
        </svg>
      </div>
    </button>
  </nav>
</DocumentFragment>
`;

exports[`Paginator snapshots renders alignment left state correctly BananaRepublic in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  clear: both;
  text-align: left;
}

.emotion-1 {
  background: none;
  border: none;
  width: auto;
  margin: 0;
  padding: 0;
  text-align: inherit;
  font: inherit;
  display: inline-block;
  vertical-align: middle;
  box-sizing: border-box;
  height: 2.5em;
  width: 2.5em;
  cursor: pointer;
}

.emotion-1 path {
  fill: #666;
}

.emotion-1:hover path,
.emotion-1:active path {
  fill: #979797;
}

.emotion-2 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  padding-bottom: 2px;
}

.emotion-3 {
  vertical-align: middle;
  display: inline-block;
}

.emotion-4 {
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  color: #666;
  font-size: 1rem;
  display: inline-block;
}

<nav
    aria-label="Paginator"
    class="emotion-0"
  >
    <button
      aria-label="Previous Page"
      class="emotion-1"
    >
      <div
        class="emotion-2"
      >
        <svg
          class="emotion-3"
          enable-background="new -134 82.3 7 12"
          height="12"
          viewBox="-134 82.3 7 12"
          width="7"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M-127 92.3l-4.3-4.4 4.2-4.2-1.4-1.4-5.5 5.6 5.6 5.7 1.4-1.3z"
            fill="#979797"
          />
        </svg>
      </div>
    </button>
    <div
      class="emotion-4"
    >
      <span>
        Page 3
      </span>
      <span>
         of 
      </span>
      4
    </div>
    <button
      aria-label="Next Page"
      class="emotion-1"
    >
      <div
        class="emotion-2"
      >
        <svg
          enable-background="new -134 82.3 7 12"
          height="12"
          viewBox="-134 82.3 7 12"
          width="7"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M-134 92.3l4.2-4.4-4.1-4.2 1.4-1.4 5.5 5.6-5.6 5.7-1.4-1.3z"
          />
        </svg>
      </div>
    </button>
  </nav>
</DocumentFragment>
`;

exports[`Paginator snapshots renders alignment left state correctly BananaRepublicFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  clear: both;
  text-align: left;
}

.emotion-1 {
  background: none;
  border: none;
  width: auto;
  margin: 0;
  padding: 0;
  text-align: inherit;
  font: inherit;
  display: inline-block;
  vertical-align: middle;
  box-sizing: border-box;
  height: 2.5em;
  width: 2.5em;
  cursor: pointer;
}

.emotion-1 path {
  fill: #000;
}

.emotion-1:hover path,
.emotion-1:active path {
  fill: #000;
}

.emotion-2 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  padding-bottom: 2px;
}

.emotion-3 {
  vertical-align: middle;
  display: inline-block;
}

.emotion-4 {
  font-family: 'Lynstone',Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  color: #666;
  font-size: 0.875rem;
  display: inline-block;
}

<nav
    aria-label="Paginator"
    class="emotion-0"
  >
    <button
      aria-label="Previous Page"
      class="emotion-1"
    >
      <div
        class="emotion-2"
      >
        <svg
          class="emotion-3"
          enable-background="new -134 82.3 7 12"
          height="12"
          viewBox="-134 82.3 7 12"
          width="7"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M-127 92.3l-4.3-4.4 4.2-4.2-1.4-1.4-5.5 5.6 5.6 5.7 1.4-1.3z"
            fill="#979797"
          />
        </svg>
      </div>
    </button>
    <div
      class="emotion-4"
    >
      <span>
        Page 3
      </span>
      <span>
         of 
      </span>
      4
    </div>
    <button
      aria-label="Next Page"
      class="emotion-1"
    >
      <div
        class="emotion-2"
      >
        <svg
          enable-background="new -134 82.3 7 12"
          height="12"
          viewBox="-134 82.3 7 12"
          width="7"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M-134 92.3l4.2-4.4-4.1-4.2 1.4-1.4 5.5 5.6-5.6 5.7-1.4-1.3z"
          />
        </svg>
      </div>
    </button>
  </nav>
</DocumentFragment>
`;

exports[`Paginator snapshots renders alignment left state correctly BananaRepublicFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  clear: both;
  text-align: left;
}

.emotion-1 {
  background: none;
  border: none;
  width: auto;
  margin: 0;
  padding: 0;
  text-align: inherit;
  font: inherit;
  display: inline-block;
  vertical-align: middle;
  box-sizing: border-box;
  height: 2.5em;
  width: 2.5em;
  cursor: pointer;
}

.emotion-1 path {
  fill: #666;
}

.emotion-1:hover path,
.emotion-1:active path {
  fill: #979797;
}

.emotion-2 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  padding-bottom: 2px;
}

.emotion-3 {
  vertical-align: middle;
  display: inline-block;
}

.emotion-4 {
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  color: #666;
  font-size: 1rem;
  display: inline-block;
}

<nav
    aria-label="Paginator"
    class="emotion-0"
  >
    <button
      aria-label="Previous Page"
      class="emotion-1"
    >
      <div
        class="emotion-2"
      >
        <svg
          class="emotion-3"
          enable-background="new -134 82.3 7 12"
          height="12"
          viewBox="-134 82.3 7 12"
          width="7"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M-127 92.3l-4.3-4.4 4.2-4.2-1.4-1.4-5.5 5.6 5.6 5.7 1.4-1.3z"
            fill="#979797"
          />
        </svg>
      </div>
    </button>
    <div
      class="emotion-4"
    >
      <span>
        Page 3
      </span>
      <span>
         of 
      </span>
      4
    </div>
    <button
      aria-label="Next Page"
      class="emotion-1"
    >
      <div
        class="emotion-2"
      >
        <svg
          enable-background="new -134 82.3 7 12"
          height="12"
          viewBox="-134 82.3 7 12"
          width="7"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M-134 92.3l4.2-4.4-4.1-4.2 1.4-1.4 5.5 5.6-5.6 5.7-1.4-1.3z"
          />
        </svg>
      </div>
    </button>
  </nav>
</DocumentFragment>
`;

exports[`Paginator snapshots renders alignment left state correctly Gap 1`] = `
<DocumentFragment>
  .emotion-0 {
  clear: both;
  text-align: left;
}

.emotion-1 {
  background: none;
  border: none;
  width: auto;
  margin: 0;
  padding: 0;
  text-align: inherit;
  font: inherit;
  display: inline-block;
  vertical-align: middle;
  box-sizing: border-box;
  height: 2.5em;
  width: 2.5em;
  cursor: pointer;
}

.emotion-1 path {
  fill: #0A5694;
}

.emotion-1:hover path,
.emotion-1:active path {
  fill: #002554;
}

.emotion-2 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  padding-bottom: 2px;
}

.emotion-3 {
  vertical-align: middle;
  display: inline-block;
}

.emotion-4 {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  color: #666;
  font-size: 1.067rem;
  display: inline-block;
}

<nav
    aria-label="Paginator"
    class="emotion-0"
  >
    <button
      aria-label="Previous Page"
      class="emotion-1"
    >
      <div
        class="emotion-2"
      >
        <svg
          class="emotion-3"
          enable-background="new -134 82.3 7 12"
          height="12"
          viewBox="-134 82.3 7 12"
          width="7"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M-127 92.3l-4.3-4.4 4.2-4.2-1.4-1.4-5.5 5.6 5.6 5.7 1.4-1.3z"
            fill="#979797"
          />
        </svg>
      </div>
    </button>
    <div
      class="emotion-4"
    >
      <span>
        Page 3
      </span>
      <span>
         of 
      </span>
      4
    </div>
    <button
      aria-label="Next Page"
      class="emotion-1"
    >
      <div
        class="emotion-2"
      >
        <svg
          enable-background="new -134 82.3 7 12"
          height="12"
          viewBox="-134 82.3 7 12"
          width="7"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M-134 92.3l4.2-4.4-4.1-4.2 1.4-1.4 5.5 5.6-5.6 5.7-1.4-1.3z"
          />
        </svg>
      </div>
    </button>
  </nav>
</DocumentFragment>
`;

exports[`Paginator snapshots renders alignment left state correctly Gap in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  clear: both;
  text-align: left;
}

.emotion-1 {
  background: none;
  border: none;
  width: auto;
  margin: 0;
  padding: 0;
  text-align: inherit;
  font: inherit;
  display: inline-block;
  vertical-align: middle;
  box-sizing: border-box;
  height: 2.5em;
  width: 2.5em;
  cursor: pointer;
}

.emotion-1 path {
  fill: #666;
}

.emotion-1:hover path,
.emotion-1:active path {
  fill: #979797;
}

.emotion-2 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  padding-bottom: 2px;
}

.emotion-3 {
  vertical-align: middle;
  display: inline-block;
}

.emotion-4 {
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  color: #666;
  font-size: 1rem;
  display: inline-block;
}

<nav
    aria-label="Paginator"
    class="emotion-0"
  >
    <button
      aria-label="Previous Page"
      class="emotion-1"
    >
      <div
        class="emotion-2"
      >
        <svg
          class="emotion-3"
          enable-background="new -134 82.3 7 12"
          height="12"
          viewBox="-134 82.3 7 12"
          width="7"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M-127 92.3l-4.3-4.4 4.2-4.2-1.4-1.4-5.5 5.6 5.6 5.7 1.4-1.3z"
            fill="#979797"
          />
        </svg>
      </div>
    </button>
    <div
      class="emotion-4"
    >
      <span>
        Page 3
      </span>
      <span>
         of 
      </span>
      4
    </div>
    <button
      aria-label="Next Page"
      class="emotion-1"
    >
      <div
        class="emotion-2"
      >
        <svg
          enable-background="new -134 82.3 7 12"
          height="12"
          viewBox="-134 82.3 7 12"
          width="7"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M-134 92.3l4.2-4.4-4.1-4.2 1.4-1.4 5.5 5.6-5.6 5.7-1.4-1.3z"
          />
        </svg>
      </div>
    </button>
  </nav>
</DocumentFragment>
`;

exports[`Paginator snapshots renders alignment left state correctly GapFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  clear: both;
  text-align: left;
}

.emotion-1 {
  background: none;
  border: none;
  width: auto;
  margin: 0;
  padding: 0;
  text-align: inherit;
  font: inherit;
  display: inline-block;
  vertical-align: middle;
  box-sizing: border-box;
  height: 2.5em;
  width: 2.5em;
  cursor: pointer;
}

.emotion-1 path {
  fill: #0A5694;
}

.emotion-1:hover path,
.emotion-1:active path {
  fill: #002554;
}

.emotion-2 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  padding-bottom: 2px;
}

.emotion-3 {
  vertical-align: middle;
  display: inline-block;
}

.emotion-4 {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  color: #666;
  font-size: 1.067rem;
  display: inline-block;
}

<nav
    aria-label="Paginator"
    class="emotion-0"
  >
    <button
      aria-label="Previous Page"
      class="emotion-1"
    >
      <div
        class="emotion-2"
      >
        <svg
          class="emotion-3"
          enable-background="new -134 82.3 7 12"
          height="12"
          viewBox="-134 82.3 7 12"
          width="7"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M-127 92.3l-4.3-4.4 4.2-4.2-1.4-1.4-5.5 5.6 5.6 5.7 1.4-1.3z"
            fill="#979797"
          />
        </svg>
      </div>
    </button>
    <div
      class="emotion-4"
    >
      <span>
        Page 3
      </span>
      <span>
         of 
      </span>
      4
    </div>
    <button
      aria-label="Next Page"
      class="emotion-1"
    >
      <div
        class="emotion-2"
      >
        <svg
          enable-background="new -134 82.3 7 12"
          height="12"
          viewBox="-134 82.3 7 12"
          width="7"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M-134 92.3l4.2-4.4-4.1-4.2 1.4-1.4 5.5 5.6-5.6 5.7-1.4-1.3z"
          />
        </svg>
      </div>
    </button>
  </nav>
</DocumentFragment>
`;

exports[`Paginator snapshots renders alignment left state correctly GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  clear: both;
  text-align: left;
}

.emotion-1 {
  background: none;
  border: none;
  width: auto;
  margin: 0;
  padding: 0;
  text-align: inherit;
  font: inherit;
  display: inline-block;
  vertical-align: middle;
  box-sizing: border-box;
  height: 2.5em;
  width: 2.5em;
  cursor: pointer;
}

.emotion-1 path {
  fill: #666;
}

.emotion-1:hover path,
.emotion-1:active path {
  fill: #979797;
}

.emotion-2 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  padding-bottom: 2px;
}

.emotion-3 {
  vertical-align: middle;
  display: inline-block;
}

.emotion-4 {
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  color: #666;
  font-size: 1rem;
  display: inline-block;
}

<nav
    aria-label="Paginator"
    class="emotion-0"
  >
    <button
      aria-label="Previous Page"
      class="emotion-1"
    >
      <div
        class="emotion-2"
      >
        <svg
          class="emotion-3"
          enable-background="new -134 82.3 7 12"
          height="12"
          viewBox="-134 82.3 7 12"
          width="7"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M-127 92.3l-4.3-4.4 4.2-4.2-1.4-1.4-5.5 5.6 5.6 5.7 1.4-1.3z"
            fill="#979797"
          />
        </svg>
      </div>
    </button>
    <div
      class="emotion-4"
    >
      <span>
        Page 3
      </span>
      <span>
         of 
      </span>
      4
    </div>
    <button
      aria-label="Next Page"
      class="emotion-1"
    >
      <div
        class="emotion-2"
      >
        <svg
          enable-background="new -134 82.3 7 12"
          height="12"
          viewBox="-134 82.3 7 12"
          width="7"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M-134 92.3l4.2-4.4-4.1-4.2 1.4-1.4 5.5 5.6-5.6 5.7-1.4-1.3z"
          />
        </svg>
      </div>
    </button>
  </nav>
</DocumentFragment>
`;

exports[`Paginator snapshots renders alignment left state correctly OldNavy 1`] = `
<DocumentFragment>
  .emotion-0 {
  clear: both;
  text-align: left;
}

.emotion-1 {
  background: none;
  border: none;
  width: auto;
  margin: 0;
  padding: 0;
  text-align: inherit;
  font: inherit;
  display: inline-block;
  vertical-align: middle;
  box-sizing: border-box;
  height: 2.5em;
  width: 2.5em;
  cursor: pointer;
}

.emotion-1 path {
  fill: #003764;
}

.emotion-1:hover path,
.emotion-1:active path {
  fill: #003764;
}

.emotion-2 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  padding-bottom: 2px;
}

.emotion-3 {
  vertical-align: middle;
  display: inline-block;
}

.emotion-4 {
  font-family: 'Gotham','Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  color: #666;
  font-size: 1.067rem;
  display: inline-block;
}

<nav
    aria-label="Paginator"
    class="emotion-0"
  >
    <button
      aria-label="Previous Page"
      class="emotion-1"
    >
      <div
        class="emotion-2"
      >
        <svg
          class="emotion-3"
          enable-background="new -134 82.3 7 12"
          height="12"
          viewBox="-134 82.3 7 12"
          width="7"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M-127 92.3l-4.3-4.4 4.2-4.2-1.4-1.4-5.5 5.6 5.6 5.7 1.4-1.3z"
            fill="#979797"
          />
        </svg>
      </div>
    </button>
    <div
      class="emotion-4"
    >
      <span>
        Page 3
      </span>
      <span>
         of 
      </span>
      4
    </div>
    <button
      aria-label="Next Page"
      class="emotion-1"
    >
      <div
        class="emotion-2"
      >
        <svg
          enable-background="new -134 82.3 7 12"
          height="12"
          viewBox="-134 82.3 7 12"
          width="7"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M-134 92.3l4.2-4.4-4.1-4.2 1.4-1.4 5.5 5.6-5.6 5.7-1.4-1.3z"
          />
        </svg>
      </div>
    </button>
  </nav>
</DocumentFragment>
`;

exports[`Paginator snapshots renders alignment left state correctly OldNavy in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  clear: both;
  text-align: left;
}

.emotion-1 {
  background: none;
  border: none;
  width: auto;
  margin: 0;
  padding: 0;
  text-align: inherit;
  font: inherit;
  display: inline-block;
  vertical-align: middle;
  box-sizing: border-box;
  height: 2.5em;
  width: 2.5em;
  cursor: pointer;
}

.emotion-1 path {
  fill: #666;
}

.emotion-1:hover path,
.emotion-1:active path {
  fill: #979797;
}

.emotion-2 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  padding-bottom: 2px;
}

.emotion-3 {
  vertical-align: middle;
  display: inline-block;
}

.emotion-4 {
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  color: #666;
  font-size: 1rem;
  display: inline-block;
}

<nav
    aria-label="Paginator"
    class="emotion-0"
  >
    <button
      aria-label="Previous Page"
      class="emotion-1"
    >
      <div
        class="emotion-2"
      >
        <svg
          class="emotion-3"
          enable-background="new -134 82.3 7 12"
          height="12"
          viewBox="-134 82.3 7 12"
          width="7"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M-127 92.3l-4.3-4.4 4.2-4.2-1.4-1.4-5.5 5.6 5.6 5.7 1.4-1.3z"
            fill="#979797"
          />
        </svg>
      </div>
    </button>
    <div
      class="emotion-4"
    >
      <span>
        Page 3
      </span>
      <span>
         of 
      </span>
      4
    </div>
    <button
      aria-label="Next Page"
      class="emotion-1"
    >
      <div
        class="emotion-2"
      >
        <svg
          enable-background="new -134 82.3 7 12"
          height="12"
          viewBox="-134 82.3 7 12"
          width="7"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M-134 92.3l4.2-4.4-4.1-4.2 1.4-1.4 5.5 5.6-5.6 5.7-1.4-1.3z"
          />
        </svg>
      </div>
    </button>
  </nav>
</DocumentFragment>
`;

exports[`Paginator snapshots renders alignment right state correctly Athleta 1`] = `
<DocumentFragment>
  .emotion-0 {
  clear: both;
  text-align: right;
}

.emotion-1 {
  background: none;
  border: none;
  width: auto;
  margin: 0;
  padding: 0;
  text-align: inherit;
  font: inherit;
  display: inline-block;
  vertical-align: middle;
  box-sizing: border-box;
  height: 2.5em;
  width: 2.5em;
  cursor: pointer;
}

.emotion-1 path {
  fill: #979797;
}

.emotion-1:hover path,
.emotion-1:active path {
  fill: #979797;
}

.emotion-2 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  padding-bottom: 2px;
}

.emotion-3 {
  vertical-align: middle;
  display: inline-block;
}

.emotion-4 {
  font-family: 'Avenir Next','Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  color: #666;
  font-weight: 600;
  letter-spacing: 0.5px;
  font-size: 1rem;
  display: inline-block;
}

<nav
    aria-label="Paginator"
    class="emotion-0"
  >
    <button
      aria-label="Previous Page"
      class="emotion-1"
    >
      <div
        class="emotion-2"
      >
        <svg
          class="emotion-3"
          enable-background="new -134 82.3 7 12"
          height="12"
          viewBox="-134 82.3 7 12"
          width="7"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M-127 92.3l-4.3-4.4 4.2-4.2-1.4-1.4-5.5 5.6 5.6 5.7 1.4-1.3z"
            fill="#979797"
          />
        </svg>
      </div>
    </button>
    <div
      class="emotion-4"
    >
      <span>
        Page 3
      </span>
      <span>
         of 
      </span>
      4
    </div>
    <button
      aria-label="Next Page"
      class="emotion-1"
    >
      <div
        class="emotion-2"
      >
        <svg
          enable-background="new -134 82.3 7 12"
          height="12"
          viewBox="-134 82.3 7 12"
          width="7"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M-134 92.3l4.2-4.4-4.1-4.2 1.4-1.4 5.5 5.6-5.6 5.7-1.4-1.3z"
          />
        </svg>
      </div>
    </button>
  </nav>
</DocumentFragment>
`;

exports[`Paginator snapshots renders alignment right state correctly Athleta in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  clear: both;
  text-align: right;
}

.emotion-1 {
  background: none;
  border: none;
  width: auto;
  margin: 0;
  padding: 0;
  text-align: inherit;
  font: inherit;
  display: inline-block;
  vertical-align: middle;
  box-sizing: border-box;
  height: 2.5em;
  width: 2.5em;
  cursor: pointer;
}

.emotion-1 path {
  fill: #666;
}

.emotion-1:hover path,
.emotion-1:active path {
  fill: #979797;
}

.emotion-2 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  padding-bottom: 2px;
}

.emotion-3 {
  vertical-align: middle;
  display: inline-block;
}

.emotion-4 {
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  color: #666;
  font-size: 1rem;
  display: inline-block;
}

<nav
    aria-label="Paginator"
    class="emotion-0"
  >
    <button
      aria-label="Previous Page"
      class="emotion-1"
    >
      <div
        class="emotion-2"
      >
        <svg
          class="emotion-3"
          enable-background="new -134 82.3 7 12"
          height="12"
          viewBox="-134 82.3 7 12"
          width="7"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M-127 92.3l-4.3-4.4 4.2-4.2-1.4-1.4-5.5 5.6 5.6 5.7 1.4-1.3z"
            fill="#979797"
          />
        </svg>
      </div>
    </button>
    <div
      class="emotion-4"
    >
      <span>
        Page 3
      </span>
      <span>
         of 
      </span>
      4
    </div>
    <button
      aria-label="Next Page"
      class="emotion-1"
    >
      <div
        class="emotion-2"
      >
        <svg
          enable-background="new -134 82.3 7 12"
          height="12"
          viewBox="-134 82.3 7 12"
          width="7"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M-134 92.3l4.2-4.4-4.1-4.2 1.4-1.4 5.5 5.6-5.6 5.7-1.4-1.3z"
          />
        </svg>
      </div>
    </button>
  </nav>
</DocumentFragment>
`;

exports[`Paginator snapshots renders alignment right state correctly BananaRepublic 1`] = `
<DocumentFragment>
  .emotion-0 {
  clear: both;
  text-align: right;
}

.emotion-1 {
  background: none;
  border: none;
  width: auto;
  margin: 0;
  padding: 0;
  text-align: inherit;
  font: inherit;
  display: inline-block;
  vertical-align: middle;
  box-sizing: border-box;
  height: 2.5em;
  width: 2.5em;
  cursor: pointer;
}

.emotion-1 path {
  fill: #000;
}

.emotion-1:hover path,
.emotion-1:active path {
  fill: #000;
}

.emotion-2 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  padding-bottom: 2px;
}

.emotion-3 {
  vertical-align: middle;
  display: inline-block;
}

.emotion-4 {
  font-family: 'Lynstone',Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  color: #666;
  font-size: 0.875rem;
  display: inline-block;
}

<nav
    aria-label="Paginator"
    class="emotion-0"
  >
    <button
      aria-label="Previous Page"
      class="emotion-1"
    >
      <div
        class="emotion-2"
      >
        <svg
          class="emotion-3"
          enable-background="new -134 82.3 7 12"
          height="12"
          viewBox="-134 82.3 7 12"
          width="7"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M-127 92.3l-4.3-4.4 4.2-4.2-1.4-1.4-5.5 5.6 5.6 5.7 1.4-1.3z"
            fill="#979797"
          />
        </svg>
      </div>
    </button>
    <div
      class="emotion-4"
    >
      <span>
        Page 3
      </span>
      <span>
         of 
      </span>
      4
    </div>
    <button
      aria-label="Next Page"
      class="emotion-1"
    >
      <div
        class="emotion-2"
      >
        <svg
          enable-background="new -134 82.3 7 12"
          height="12"
          viewBox="-134 82.3 7 12"
          width="7"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M-134 92.3l4.2-4.4-4.1-4.2 1.4-1.4 5.5 5.6-5.6 5.7-1.4-1.3z"
          />
        </svg>
      </div>
    </button>
  </nav>
</DocumentFragment>
`;

exports[`Paginator snapshots renders alignment right state correctly BananaRepublic in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  clear: both;
  text-align: right;
}

.emotion-1 {
  background: none;
  border: none;
  width: auto;
  margin: 0;
  padding: 0;
  text-align: inherit;
  font: inherit;
  display: inline-block;
  vertical-align: middle;
  box-sizing: border-box;
  height: 2.5em;
  width: 2.5em;
  cursor: pointer;
}

.emotion-1 path {
  fill: #666;
}

.emotion-1:hover path,
.emotion-1:active path {
  fill: #979797;
}

.emotion-2 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  padding-bottom: 2px;
}

.emotion-3 {
  vertical-align: middle;
  display: inline-block;
}

.emotion-4 {
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  color: #666;
  font-size: 1rem;
  display: inline-block;
}

<nav
    aria-label="Paginator"
    class="emotion-0"
  >
    <button
      aria-label="Previous Page"
      class="emotion-1"
    >
      <div
        class="emotion-2"
      >
        <svg
          class="emotion-3"
          enable-background="new -134 82.3 7 12"
          height="12"
          viewBox="-134 82.3 7 12"
          width="7"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M-127 92.3l-4.3-4.4 4.2-4.2-1.4-1.4-5.5 5.6 5.6 5.7 1.4-1.3z"
            fill="#979797"
          />
        </svg>
      </div>
    </button>
    <div
      class="emotion-4"
    >
      <span>
        Page 3
      </span>
      <span>
         of 
      </span>
      4
    </div>
    <button
      aria-label="Next Page"
      class="emotion-1"
    >
      <div
        class="emotion-2"
      >
        <svg
          enable-background="new -134 82.3 7 12"
          height="12"
          viewBox="-134 82.3 7 12"
          width="7"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M-134 92.3l4.2-4.4-4.1-4.2 1.4-1.4 5.5 5.6-5.6 5.7-1.4-1.3z"
          />
        </svg>
      </div>
    </button>
  </nav>
</DocumentFragment>
`;

exports[`Paginator snapshots renders alignment right state correctly BananaRepublicFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  clear: both;
  text-align: right;
}

.emotion-1 {
  background: none;
  border: none;
  width: auto;
  margin: 0;
  padding: 0;
  text-align: inherit;
  font: inherit;
  display: inline-block;
  vertical-align: middle;
  box-sizing: border-box;
  height: 2.5em;
  width: 2.5em;
  cursor: pointer;
}

.emotion-1 path {
  fill: #000;
}

.emotion-1:hover path,
.emotion-1:active path {
  fill: #000;
}

.emotion-2 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  padding-bottom: 2px;
}

.emotion-3 {
  vertical-align: middle;
  display: inline-block;
}

.emotion-4 {
  font-family: 'Lynstone',Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  color: #666;
  font-size: 0.875rem;
  display: inline-block;
}

<nav
    aria-label="Paginator"
    class="emotion-0"
  >
    <button
      aria-label="Previous Page"
      class="emotion-1"
    >
      <div
        class="emotion-2"
      >
        <svg
          class="emotion-3"
          enable-background="new -134 82.3 7 12"
          height="12"
          viewBox="-134 82.3 7 12"
          width="7"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M-127 92.3l-4.3-4.4 4.2-4.2-1.4-1.4-5.5 5.6 5.6 5.7 1.4-1.3z"
            fill="#979797"
          />
        </svg>
      </div>
    </button>
    <div
      class="emotion-4"
    >
      <span>
        Page 3
      </span>
      <span>
         of 
      </span>
      4
    </div>
    <button
      aria-label="Next Page"
      class="emotion-1"
    >
      <div
        class="emotion-2"
      >
        <svg
          enable-background="new -134 82.3 7 12"
          height="12"
          viewBox="-134 82.3 7 12"
          width="7"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M-134 92.3l4.2-4.4-4.1-4.2 1.4-1.4 5.5 5.6-5.6 5.7-1.4-1.3z"
          />
        </svg>
      </div>
    </button>
  </nav>
</DocumentFragment>
`;

exports[`Paginator snapshots renders alignment right state correctly BananaRepublicFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  clear: both;
  text-align: right;
}

.emotion-1 {
  background: none;
  border: none;
  width: auto;
  margin: 0;
  padding: 0;
  text-align: inherit;
  font: inherit;
  display: inline-block;
  vertical-align: middle;
  box-sizing: border-box;
  height: 2.5em;
  width: 2.5em;
  cursor: pointer;
}

.emotion-1 path {
  fill: #666;
}

.emotion-1:hover path,
.emotion-1:active path {
  fill: #979797;
}

.emotion-2 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  padding-bottom: 2px;
}

.emotion-3 {
  vertical-align: middle;
  display: inline-block;
}

.emotion-4 {
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  color: #666;
  font-size: 1rem;
  display: inline-block;
}

<nav
    aria-label="Paginator"
    class="emotion-0"
  >
    <button
      aria-label="Previous Page"
      class="emotion-1"
    >
      <div
        class="emotion-2"
      >
        <svg
          class="emotion-3"
          enable-background="new -134 82.3 7 12"
          height="12"
          viewBox="-134 82.3 7 12"
          width="7"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M-127 92.3l-4.3-4.4 4.2-4.2-1.4-1.4-5.5 5.6 5.6 5.7 1.4-1.3z"
            fill="#979797"
          />
        </svg>
      </div>
    </button>
    <div
      class="emotion-4"
    >
      <span>
        Page 3
      </span>
      <span>
         of 
      </span>
      4
    </div>
    <button
      aria-label="Next Page"
      class="emotion-1"
    >
      <div
        class="emotion-2"
      >
        <svg
          enable-background="new -134 82.3 7 12"
          height="12"
          viewBox="-134 82.3 7 12"
          width="7"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M-134 92.3l4.2-4.4-4.1-4.2 1.4-1.4 5.5 5.6-5.6 5.7-1.4-1.3z"
          />
        </svg>
      </div>
    </button>
  </nav>
</DocumentFragment>
`;

exports[`Paginator snapshots renders alignment right state correctly Gap 1`] = `
<DocumentFragment>
  .emotion-0 {
  clear: both;
  text-align: right;
}

.emotion-1 {
  background: none;
  border: none;
  width: auto;
  margin: 0;
  padding: 0;
  text-align: inherit;
  font: inherit;
  display: inline-block;
  vertical-align: middle;
  box-sizing: border-box;
  height: 2.5em;
  width: 2.5em;
  cursor: pointer;
}

.emotion-1 path {
  fill: #0A5694;
}

.emotion-1:hover path,
.emotion-1:active path {
  fill: #002554;
}

.emotion-2 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  padding-bottom: 2px;
}

.emotion-3 {
  vertical-align: middle;
  display: inline-block;
}

.emotion-4 {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  color: #666;
  font-size: 1.067rem;
  display: inline-block;
}

<nav
    aria-label="Paginator"
    class="emotion-0"
  >
    <button
      aria-label="Previous Page"
      class="emotion-1"
    >
      <div
        class="emotion-2"
      >
        <svg
          class="emotion-3"
          enable-background="new -134 82.3 7 12"
          height="12"
          viewBox="-134 82.3 7 12"
          width="7"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M-127 92.3l-4.3-4.4 4.2-4.2-1.4-1.4-5.5 5.6 5.6 5.7 1.4-1.3z"
            fill="#979797"
          />
        </svg>
      </div>
    </button>
    <div
      class="emotion-4"
    >
      <span>
        Page 3
      </span>
      <span>
         of 
      </span>
      4
    </div>
    <button
      aria-label="Next Page"
      class="emotion-1"
    >
      <div
        class="emotion-2"
      >
        <svg
          enable-background="new -134 82.3 7 12"
          height="12"
          viewBox="-134 82.3 7 12"
          width="7"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M-134 92.3l4.2-4.4-4.1-4.2 1.4-1.4 5.5 5.6-5.6 5.7-1.4-1.3z"
          />
        </svg>
      </div>
    </button>
  </nav>
</DocumentFragment>
`;

exports[`Paginator snapshots renders alignment right state correctly Gap in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  clear: both;
  text-align: right;
}

.emotion-1 {
  background: none;
  border: none;
  width: auto;
  margin: 0;
  padding: 0;
  text-align: inherit;
  font: inherit;
  display: inline-block;
  vertical-align: middle;
  box-sizing: border-box;
  height: 2.5em;
  width: 2.5em;
  cursor: pointer;
}

.emotion-1 path {
  fill: #666;
}

.emotion-1:hover path,
.emotion-1:active path {
  fill: #979797;
}

.emotion-2 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  padding-bottom: 2px;
}

.emotion-3 {
  vertical-align: middle;
  display: inline-block;
}

.emotion-4 {
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  color: #666;
  font-size: 1rem;
  display: inline-block;
}

<nav
    aria-label="Paginator"
    class="emotion-0"
  >
    <button
      aria-label="Previous Page"
      class="emotion-1"
    >
      <div
        class="emotion-2"
      >
        <svg
          class="emotion-3"
          enable-background="new -134 82.3 7 12"
          height="12"
          viewBox="-134 82.3 7 12"
          width="7"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M-127 92.3l-4.3-4.4 4.2-4.2-1.4-1.4-5.5 5.6 5.6 5.7 1.4-1.3z"
            fill="#979797"
          />
        </svg>
      </div>
    </button>
    <div
      class="emotion-4"
    >
      <span>
        Page 3
      </span>
      <span>
         of 
      </span>
      4
    </div>
    <button
      aria-label="Next Page"
      class="emotion-1"
    >
      <div
        class="emotion-2"
      >
        <svg
          enable-background="new -134 82.3 7 12"
          height="12"
          viewBox="-134 82.3 7 12"
          width="7"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M-134 92.3l4.2-4.4-4.1-4.2 1.4-1.4 5.5 5.6-5.6 5.7-1.4-1.3z"
          />
        </svg>
      </div>
    </button>
  </nav>
</DocumentFragment>
`;

exports[`Paginator snapshots renders alignment right state correctly GapFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  clear: both;
  text-align: right;
}

.emotion-1 {
  background: none;
  border: none;
  width: auto;
  margin: 0;
  padding: 0;
  text-align: inherit;
  font: inherit;
  display: inline-block;
  vertical-align: middle;
  box-sizing: border-box;
  height: 2.5em;
  width: 2.5em;
  cursor: pointer;
}

.emotion-1 path {
  fill: #0A5694;
}

.emotion-1:hover path,
.emotion-1:active path {
  fill: #002554;
}

.emotion-2 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  padding-bottom: 2px;
}

.emotion-3 {
  vertical-align: middle;
  display: inline-block;
}

.emotion-4 {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  color: #666;
  font-size: 1.067rem;
  display: inline-block;
}

<nav
    aria-label="Paginator"
    class="emotion-0"
  >
    <button
      aria-label="Previous Page"
      class="emotion-1"
    >
      <div
        class="emotion-2"
      >
        <svg
          class="emotion-3"
          enable-background="new -134 82.3 7 12"
          height="12"
          viewBox="-134 82.3 7 12"
          width="7"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M-127 92.3l-4.3-4.4 4.2-4.2-1.4-1.4-5.5 5.6 5.6 5.7 1.4-1.3z"
            fill="#979797"
          />
        </svg>
      </div>
    </button>
    <div
      class="emotion-4"
    >
      <span>
        Page 3
      </span>
      <span>
         of 
      </span>
      4
    </div>
    <button
      aria-label="Next Page"
      class="emotion-1"
    >
      <div
        class="emotion-2"
      >
        <svg
          enable-background="new -134 82.3 7 12"
          height="12"
          viewBox="-134 82.3 7 12"
          width="7"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M-134 92.3l4.2-4.4-4.1-4.2 1.4-1.4 5.5 5.6-5.6 5.7-1.4-1.3z"
          />
        </svg>
      </div>
    </button>
  </nav>
</DocumentFragment>
`;

exports[`Paginator snapshots renders alignment right state correctly GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  clear: both;
  text-align: right;
}

.emotion-1 {
  background: none;
  border: none;
  width: auto;
  margin: 0;
  padding: 0;
  text-align: inherit;
  font: inherit;
  display: inline-block;
  vertical-align: middle;
  box-sizing: border-box;
  height: 2.5em;
  width: 2.5em;
  cursor: pointer;
}

.emotion-1 path {
  fill: #666;
}

.emotion-1:hover path,
.emotion-1:active path {
  fill: #979797;
}

.emotion-2 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  padding-bottom: 2px;
}

.emotion-3 {
  vertical-align: middle;
  display: inline-block;
}

.emotion-4 {
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  color: #666;
  font-size: 1rem;
  display: inline-block;
}

<nav
    aria-label="Paginator"
    class="emotion-0"
  >
    <button
      aria-label="Previous Page"
      class="emotion-1"
    >
      <div
        class="emotion-2"
      >
        <svg
          class="emotion-3"
          enable-background="new -134 82.3 7 12"
          height="12"
          viewBox="-134 82.3 7 12"
          width="7"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M-127 92.3l-4.3-4.4 4.2-4.2-1.4-1.4-5.5 5.6 5.6 5.7 1.4-1.3z"
            fill="#979797"
          />
        </svg>
      </div>
    </button>
    <div
      class="emotion-4"
    >
      <span>
        Page 3
      </span>
      <span>
         of 
      </span>
      4
    </div>
    <button
      aria-label="Next Page"
      class="emotion-1"
    >
      <div
        class="emotion-2"
      >
        <svg
          enable-background="new -134 82.3 7 12"
          height="12"
          viewBox="-134 82.3 7 12"
          width="7"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M-134 92.3l4.2-4.4-4.1-4.2 1.4-1.4 5.5 5.6-5.6 5.7-1.4-1.3z"
          />
        </svg>
      </div>
    </button>
  </nav>
</DocumentFragment>
`;

exports[`Paginator snapshots renders alignment right state correctly OldNavy 1`] = `
<DocumentFragment>
  .emotion-0 {
  clear: both;
  text-align: right;
}

.emotion-1 {
  background: none;
  border: none;
  width: auto;
  margin: 0;
  padding: 0;
  text-align: inherit;
  font: inherit;
  display: inline-block;
  vertical-align: middle;
  box-sizing: border-box;
  height: 2.5em;
  width: 2.5em;
  cursor: pointer;
}

.emotion-1 path {
  fill: #003764;
}

.emotion-1:hover path,
.emotion-1:active path {
  fill: #003764;
}

.emotion-2 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  padding-bottom: 2px;
}

.emotion-3 {
  vertical-align: middle;
  display: inline-block;
}

.emotion-4 {
  font-family: 'Gotham','Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  color: #666;
  font-size: 1.067rem;
  display: inline-block;
}

<nav
    aria-label="Paginator"
    class="emotion-0"
  >
    <button
      aria-label="Previous Page"
      class="emotion-1"
    >
      <div
        class="emotion-2"
      >
        <svg
          class="emotion-3"
          enable-background="new -134 82.3 7 12"
          height="12"
          viewBox="-134 82.3 7 12"
          width="7"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M-127 92.3l-4.3-4.4 4.2-4.2-1.4-1.4-5.5 5.6 5.6 5.7 1.4-1.3z"
            fill="#979797"
          />
        </svg>
      </div>
    </button>
    <div
      class="emotion-4"
    >
      <span>
        Page 3
      </span>
      <span>
         of 
      </span>
      4
    </div>
    <button
      aria-label="Next Page"
      class="emotion-1"
    >
      <div
        class="emotion-2"
      >
        <svg
          enable-background="new -134 82.3 7 12"
          height="12"
          viewBox="-134 82.3 7 12"
          width="7"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M-134 92.3l4.2-4.4-4.1-4.2 1.4-1.4 5.5 5.6-5.6 5.7-1.4-1.3z"
          />
        </svg>
      </div>
    </button>
  </nav>
</DocumentFragment>
`;

exports[`Paginator snapshots renders alignment right state correctly OldNavy in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  clear: both;
  text-align: right;
}

.emotion-1 {
  background: none;
  border: none;
  width: auto;
  margin: 0;
  padding: 0;
  text-align: inherit;
  font: inherit;
  display: inline-block;
  vertical-align: middle;
  box-sizing: border-box;
  height: 2.5em;
  width: 2.5em;
  cursor: pointer;
}

.emotion-1 path {
  fill: #666;
}

.emotion-1:hover path,
.emotion-1:active path {
  fill: #979797;
}

.emotion-2 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  padding-bottom: 2px;
}

.emotion-3 {
  vertical-align: middle;
  display: inline-block;
}

.emotion-4 {
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  color: #666;
  font-size: 1rem;
  display: inline-block;
}

<nav
    aria-label="Paginator"
    class="emotion-0"
  >
    <button
      aria-label="Previous Page"
      class="emotion-1"
    >
      <div
        class="emotion-2"
      >
        <svg
          class="emotion-3"
          enable-background="new -134 82.3 7 12"
          height="12"
          viewBox="-134 82.3 7 12"
          width="7"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M-127 92.3l-4.3-4.4 4.2-4.2-1.4-1.4-5.5 5.6 5.6 5.7 1.4-1.3z"
            fill="#979797"
          />
        </svg>
      </div>
    </button>
    <div
      class="emotion-4"
    >
      <span>
        Page 3
      </span>
      <span>
         of 
      </span>
      4
    </div>
    <button
      aria-label="Next Page"
      class="emotion-1"
    >
      <div
        class="emotion-2"
      >
        <svg
          enable-background="new -134 82.3 7 12"
          height="12"
          viewBox="-134 82.3 7 12"
          width="7"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M-134 92.3l4.2-4.4-4.1-4.2 1.4-1.4 5.5 5.6-5.6 5.7-1.4-1.3z"
          />
        </svg>
      </div>
    </button>
  </nav>
</DocumentFragment>
`;

exports[`Paginator snapshots renders default state correctly Athleta 1`] = `
<DocumentFragment>
  .emotion-0 {
  clear: both;
  text-align: center;
}

.emotion-1 {
  background: none;
  border: none;
  width: auto;
  margin: 0;
  padding: 0;
  text-align: inherit;
  font: inherit;
  display: inline-block;
  vertical-align: middle;
  box-sizing: border-box;
  height: 2.5em;
  width: 2.5em;
  cursor: pointer;
}

.emotion-1 path {
  fill: #979797;
}

.emotion-1:hover path,
.emotion-1:active path {
  fill: #979797;
}

.emotion-2 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  padding-bottom: 2px;
}

.emotion-3 {
  vertical-align: middle;
  display: inline-block;
}

.emotion-4 {
  font-family: 'Avenir Next','Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  color: #666;
  font-weight: 600;
  letter-spacing: 0.5px;
  font-size: 1rem;
  display: inline-block;
}

<nav
    aria-label="Paginator"
    class="emotion-0"
  >
    <button
      aria-label="Previous Page"
      class="emotion-1"
    >
      <div
        class="emotion-2"
      >
        <svg
          class="emotion-3"
          enable-background="new -134 82.3 7 12"
          height="12"
          viewBox="-134 82.3 7 12"
          width="7"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M-127 92.3l-4.3-4.4 4.2-4.2-1.4-1.4-5.5 5.6 5.6 5.7 1.4-1.3z"
            fill="#979797"
          />
        </svg>
      </div>
    </button>
    <div
      class="emotion-4"
    >
      <span>
        Page 3
      </span>
      <span>
         of 
      </span>
      4
    </div>
    <button
      aria-label="Next Page"
      class="emotion-1"
    >
      <div
        class="emotion-2"
      >
        <svg
          enable-background="new -134 82.3 7 12"
          height="12"
          viewBox="-134 82.3 7 12"
          width="7"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M-134 92.3l4.2-4.4-4.1-4.2 1.4-1.4 5.5 5.6-5.6 5.7-1.4-1.3z"
          />
        </svg>
      </div>
    </button>
  </nav>
</DocumentFragment>
`;

exports[`Paginator snapshots renders default state correctly Athleta in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  clear: both;
  text-align: center;
}

.emotion-1 {
  background: none;
  border: none;
  width: auto;
  margin: 0;
  padding: 0;
  text-align: inherit;
  font: inherit;
  display: inline-block;
  vertical-align: middle;
  box-sizing: border-box;
  height: 2.5em;
  width: 2.5em;
  cursor: pointer;
}

.emotion-1 path {
  fill: #666;
}

.emotion-1:hover path,
.emotion-1:active path {
  fill: #979797;
}

.emotion-2 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  padding-bottom: 2px;
}

.emotion-3 {
  vertical-align: middle;
  display: inline-block;
}

.emotion-4 {
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  color: #666;
  font-size: 1rem;
  display: inline-block;
}

<nav
    aria-label="Paginator"
    class="emotion-0"
  >
    <button
      aria-label="Previous Page"
      class="emotion-1"
    >
      <div
        class="emotion-2"
      >
        <svg
          class="emotion-3"
          enable-background="new -134 82.3 7 12"
          height="12"
          viewBox="-134 82.3 7 12"
          width="7"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M-127 92.3l-4.3-4.4 4.2-4.2-1.4-1.4-5.5 5.6 5.6 5.7 1.4-1.3z"
            fill="#979797"
          />
        </svg>
      </div>
    </button>
    <div
      class="emotion-4"
    >
      <span>
        Page 3
      </span>
      <span>
         of 
      </span>
      4
    </div>
    <button
      aria-label="Next Page"
      class="emotion-1"
    >
      <div
        class="emotion-2"
      >
        <svg
          enable-background="new -134 82.3 7 12"
          height="12"
          viewBox="-134 82.3 7 12"
          width="7"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M-134 92.3l4.2-4.4-4.1-4.2 1.4-1.4 5.5 5.6-5.6 5.7-1.4-1.3z"
          />
        </svg>
      </div>
    </button>
  </nav>
</DocumentFragment>
`;

exports[`Paginator snapshots renders default state correctly BananaRepublic 1`] = `
<DocumentFragment>
  .emotion-0 {
  clear: both;
  text-align: center;
}

.emotion-1 {
  background: none;
  border: none;
  width: auto;
  margin: 0;
  padding: 0;
  text-align: inherit;
  font: inherit;
  display: inline-block;
  vertical-align: middle;
  box-sizing: border-box;
  height: 2.5em;
  width: 2.5em;
  cursor: pointer;
}

.emotion-1 path {
  fill: #000;
}

.emotion-1:hover path,
.emotion-1:active path {
  fill: #000;
}

.emotion-2 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  padding-bottom: 2px;
}

.emotion-3 {
  vertical-align: middle;
  display: inline-block;
}

.emotion-4 {
  font-family: 'Lynstone',Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  color: #666;
  font-size: 0.875rem;
  display: inline-block;
}

<nav
    aria-label="Paginator"
    class="emotion-0"
  >
    <button
      aria-label="Previous Page"
      class="emotion-1"
    >
      <div
        class="emotion-2"
      >
        <svg
          class="emotion-3"
          enable-background="new -134 82.3 7 12"
          height="12"
          viewBox="-134 82.3 7 12"
          width="7"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M-127 92.3l-4.3-4.4 4.2-4.2-1.4-1.4-5.5 5.6 5.6 5.7 1.4-1.3z"
            fill="#979797"
          />
        </svg>
      </div>
    </button>
    <div
      class="emotion-4"
    >
      <span>
        Page 3
      </span>
      <span>
         of 
      </span>
      4
    </div>
    <button
      aria-label="Next Page"
      class="emotion-1"
    >
      <div
        class="emotion-2"
      >
        <svg
          enable-background="new -134 82.3 7 12"
          height="12"
          viewBox="-134 82.3 7 12"
          width="7"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M-134 92.3l4.2-4.4-4.1-4.2 1.4-1.4 5.5 5.6-5.6 5.7-1.4-1.3z"
          />
        </svg>
      </div>
    </button>
  </nav>
</DocumentFragment>
`;

exports[`Paginator snapshots renders default state correctly BananaRepublic in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  clear: both;
  text-align: center;
}

.emotion-1 {
  background: none;
  border: none;
  width: auto;
  margin: 0;
  padding: 0;
  text-align: inherit;
  font: inherit;
  display: inline-block;
  vertical-align: middle;
  box-sizing: border-box;
  height: 2.5em;
  width: 2.5em;
  cursor: pointer;
}

.emotion-1 path {
  fill: #666;
}

.emotion-1:hover path,
.emotion-1:active path {
  fill: #979797;
}

.emotion-2 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  padding-bottom: 2px;
}

.emotion-3 {
  vertical-align: middle;
  display: inline-block;
}

.emotion-4 {
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  color: #666;
  font-size: 1rem;
  display: inline-block;
}

<nav
    aria-label="Paginator"
    class="emotion-0"
  >
    <button
      aria-label="Previous Page"
      class="emotion-1"
    >
      <div
        class="emotion-2"
      >
        <svg
          class="emotion-3"
          enable-background="new -134 82.3 7 12"
          height="12"
          viewBox="-134 82.3 7 12"
          width="7"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M-127 92.3l-4.3-4.4 4.2-4.2-1.4-1.4-5.5 5.6 5.6 5.7 1.4-1.3z"
            fill="#979797"
          />
        </svg>
      </div>
    </button>
    <div
      class="emotion-4"
    >
      <span>
        Page 3
      </span>
      <span>
         of 
      </span>
      4
    </div>
    <button
      aria-label="Next Page"
      class="emotion-1"
    >
      <div
        class="emotion-2"
      >
        <svg
          enable-background="new -134 82.3 7 12"
          height="12"
          viewBox="-134 82.3 7 12"
          width="7"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M-134 92.3l4.2-4.4-4.1-4.2 1.4-1.4 5.5 5.6-5.6 5.7-1.4-1.3z"
          />
        </svg>
      </div>
    </button>
  </nav>
</DocumentFragment>
`;

exports[`Paginator snapshots renders default state correctly BananaRepublicFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  clear: both;
  text-align: center;
}

.emotion-1 {
  background: none;
  border: none;
  width: auto;
  margin: 0;
  padding: 0;
  text-align: inherit;
  font: inherit;
  display: inline-block;
  vertical-align: middle;
  box-sizing: border-box;
  height: 2.5em;
  width: 2.5em;
  cursor: pointer;
}

.emotion-1 path {
  fill: #000;
}

.emotion-1:hover path,
.emotion-1:active path {
  fill: #000;
}

.emotion-2 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  padding-bottom: 2px;
}

.emotion-3 {
  vertical-align: middle;
  display: inline-block;
}

.emotion-4 {
  font-family: 'Lynstone',Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  color: #666;
  font-size: 0.875rem;
  display: inline-block;
}

<nav
    aria-label="Paginator"
    class="emotion-0"
  >
    <button
      aria-label="Previous Page"
      class="emotion-1"
    >
      <div
        class="emotion-2"
      >
        <svg
          class="emotion-3"
          enable-background="new -134 82.3 7 12"
          height="12"
          viewBox="-134 82.3 7 12"
          width="7"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M-127 92.3l-4.3-4.4 4.2-4.2-1.4-1.4-5.5 5.6 5.6 5.7 1.4-1.3z"
            fill="#979797"
          />
        </svg>
      </div>
    </button>
    <div
      class="emotion-4"
    >
      <span>
        Page 3
      </span>
      <span>
         of 
      </span>
      4
    </div>
    <button
      aria-label="Next Page"
      class="emotion-1"
    >
      <div
        class="emotion-2"
      >
        <svg
          enable-background="new -134 82.3 7 12"
          height="12"
          viewBox="-134 82.3 7 12"
          width="7"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M-134 92.3l4.2-4.4-4.1-4.2 1.4-1.4 5.5 5.6-5.6 5.7-1.4-1.3z"
          />
        </svg>
      </div>
    </button>
  </nav>
</DocumentFragment>
`;

exports[`Paginator snapshots renders default state correctly BananaRepublicFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  clear: both;
  text-align: center;
}

.emotion-1 {
  background: none;
  border: none;
  width: auto;
  margin: 0;
  padding: 0;
  text-align: inherit;
  font: inherit;
  display: inline-block;
  vertical-align: middle;
  box-sizing: border-box;
  height: 2.5em;
  width: 2.5em;
  cursor: pointer;
}

.emotion-1 path {
  fill: #666;
}

.emotion-1:hover path,
.emotion-1:active path {
  fill: #979797;
}

.emotion-2 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  padding-bottom: 2px;
}

.emotion-3 {
  vertical-align: middle;
  display: inline-block;
}

.emotion-4 {
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  color: #666;
  font-size: 1rem;
  display: inline-block;
}

<nav
    aria-label="Paginator"
    class="emotion-0"
  >
    <button
      aria-label="Previous Page"
      class="emotion-1"
    >
      <div
        class="emotion-2"
      >
        <svg
          class="emotion-3"
          enable-background="new -134 82.3 7 12"
          height="12"
          viewBox="-134 82.3 7 12"
          width="7"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M-127 92.3l-4.3-4.4 4.2-4.2-1.4-1.4-5.5 5.6 5.6 5.7 1.4-1.3z"
            fill="#979797"
          />
        </svg>
      </div>
    </button>
    <div
      class="emotion-4"
    >
      <span>
        Page 3
      </span>
      <span>
         of 
      </span>
      4
    </div>
    <button
      aria-label="Next Page"
      class="emotion-1"
    >
      <div
        class="emotion-2"
      >
        <svg
          enable-background="new -134 82.3 7 12"
          height="12"
          viewBox="-134 82.3 7 12"
          width="7"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M-134 92.3l4.2-4.4-4.1-4.2 1.4-1.4 5.5 5.6-5.6 5.7-1.4-1.3z"
          />
        </svg>
      </div>
    </button>
  </nav>
</DocumentFragment>
`;

exports[`Paginator snapshots renders default state correctly Gap 1`] = `
<DocumentFragment>
  .emotion-0 {
  clear: both;
  text-align: center;
}

.emotion-1 {
  background: none;
  border: none;
  width: auto;
  margin: 0;
  padding: 0;
  text-align: inherit;
  font: inherit;
  display: inline-block;
  vertical-align: middle;
  box-sizing: border-box;
  height: 2.5em;
  width: 2.5em;
  cursor: pointer;
}

.emotion-1 path {
  fill: #0A5694;
}

.emotion-1:hover path,
.emotion-1:active path {
  fill: #002554;
}

.emotion-2 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  padding-bottom: 2px;
}

.emotion-3 {
  vertical-align: middle;
  display: inline-block;
}

.emotion-4 {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  color: #666;
  font-size: 1.067rem;
  display: inline-block;
}

<nav
    aria-label="Paginator"
    class="emotion-0"
  >
    <button
      aria-label="Previous Page"
      class="emotion-1"
    >
      <div
        class="emotion-2"
      >
        <svg
          class="emotion-3"
          enable-background="new -134 82.3 7 12"
          height="12"
          viewBox="-134 82.3 7 12"
          width="7"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M-127 92.3l-4.3-4.4 4.2-4.2-1.4-1.4-5.5 5.6 5.6 5.7 1.4-1.3z"
            fill="#979797"
          />
        </svg>
      </div>
    </button>
    <div
      class="emotion-4"
    >
      <span>
        Page 3
      </span>
      <span>
         of 
      </span>
      4
    </div>
    <button
      aria-label="Next Page"
      class="emotion-1"
    >
      <div
        class="emotion-2"
      >
        <svg
          enable-background="new -134 82.3 7 12"
          height="12"
          viewBox="-134 82.3 7 12"
          width="7"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M-134 92.3l4.2-4.4-4.1-4.2 1.4-1.4 5.5 5.6-5.6 5.7-1.4-1.3z"
          />
        </svg>
      </div>
    </button>
  </nav>
</DocumentFragment>
`;

exports[`Paginator snapshots renders default state correctly Gap in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  clear: both;
  text-align: center;
}

.emotion-1 {
  background: none;
  border: none;
  width: auto;
  margin: 0;
  padding: 0;
  text-align: inherit;
  font: inherit;
  display: inline-block;
  vertical-align: middle;
  box-sizing: border-box;
  height: 2.5em;
  width: 2.5em;
  cursor: pointer;
}

.emotion-1 path {
  fill: #666;
}

.emotion-1:hover path,
.emotion-1:active path {
  fill: #979797;
}

.emotion-2 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  padding-bottom: 2px;
}

.emotion-3 {
  vertical-align: middle;
  display: inline-block;
}

.emotion-4 {
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  color: #666;
  font-size: 1rem;
  display: inline-block;
}

<nav
    aria-label="Paginator"
    class="emotion-0"
  >
    <button
      aria-label="Previous Page"
      class="emotion-1"
    >
      <div
        class="emotion-2"
      >
        <svg
          class="emotion-3"
          enable-background="new -134 82.3 7 12"
          height="12"
          viewBox="-134 82.3 7 12"
          width="7"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M-127 92.3l-4.3-4.4 4.2-4.2-1.4-1.4-5.5 5.6 5.6 5.7 1.4-1.3z"
            fill="#979797"
          />
        </svg>
      </div>
    </button>
    <div
      class="emotion-4"
    >
      <span>
        Page 3
      </span>
      <span>
         of 
      </span>
      4
    </div>
    <button
      aria-label="Next Page"
      class="emotion-1"
    >
      <div
        class="emotion-2"
      >
        <svg
          enable-background="new -134 82.3 7 12"
          height="12"
          viewBox="-134 82.3 7 12"
          width="7"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M-134 92.3l4.2-4.4-4.1-4.2 1.4-1.4 5.5 5.6-5.6 5.7-1.4-1.3z"
          />
        </svg>
      </div>
    </button>
  </nav>
</DocumentFragment>
`;

exports[`Paginator snapshots renders default state correctly GapFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  clear: both;
  text-align: center;
}

.emotion-1 {
  background: none;
  border: none;
  width: auto;
  margin: 0;
  padding: 0;
  text-align: inherit;
  font: inherit;
  display: inline-block;
  vertical-align: middle;
  box-sizing: border-box;
  height: 2.5em;
  width: 2.5em;
  cursor: pointer;
}

.emotion-1 path {
  fill: #0A5694;
}

.emotion-1:hover path,
.emotion-1:active path {
  fill: #002554;
}

.emotion-2 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  padding-bottom: 2px;
}

.emotion-3 {
  vertical-align: middle;
  display: inline-block;
}

.emotion-4 {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  color: #666;
  font-size: 1.067rem;
  display: inline-block;
}

<nav
    aria-label="Paginator"
    class="emotion-0"
  >
    <button
      aria-label="Previous Page"
      class="emotion-1"
    >
      <div
        class="emotion-2"
      >
        <svg
          class="emotion-3"
          enable-background="new -134 82.3 7 12"
          height="12"
          viewBox="-134 82.3 7 12"
          width="7"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M-127 92.3l-4.3-4.4 4.2-4.2-1.4-1.4-5.5 5.6 5.6 5.7 1.4-1.3z"
            fill="#979797"
          />
        </svg>
      </div>
    </button>
    <div
      class="emotion-4"
    >
      <span>
        Page 3
      </span>
      <span>
         of 
      </span>
      4
    </div>
    <button
      aria-label="Next Page"
      class="emotion-1"
    >
      <div
        class="emotion-2"
      >
        <svg
          enable-background="new -134 82.3 7 12"
          height="12"
          viewBox="-134 82.3 7 12"
          width="7"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M-134 92.3l4.2-4.4-4.1-4.2 1.4-1.4 5.5 5.6-5.6 5.7-1.4-1.3z"
          />
        </svg>
      </div>
    </button>
  </nav>
</DocumentFragment>
`;

exports[`Paginator snapshots renders default state correctly GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  clear: both;
  text-align: center;
}

.emotion-1 {
  background: none;
  border: none;
  width: auto;
  margin: 0;
  padding: 0;
  text-align: inherit;
  font: inherit;
  display: inline-block;
  vertical-align: middle;
  box-sizing: border-box;
  height: 2.5em;
  width: 2.5em;
  cursor: pointer;
}

.emotion-1 path {
  fill: #666;
}

.emotion-1:hover path,
.emotion-1:active path {
  fill: #979797;
}

.emotion-2 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  padding-bottom: 2px;
}

.emotion-3 {
  vertical-align: middle;
  display: inline-block;
}

.emotion-4 {
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  color: #666;
  font-size: 1rem;
  display: inline-block;
}

<nav
    aria-label="Paginator"
    class="emotion-0"
  >
    <button
      aria-label="Previous Page"
      class="emotion-1"
    >
      <div
        class="emotion-2"
      >
        <svg
          class="emotion-3"
          enable-background="new -134 82.3 7 12"
          height="12"
          viewBox="-134 82.3 7 12"
          width="7"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M-127 92.3l-4.3-4.4 4.2-4.2-1.4-1.4-5.5 5.6 5.6 5.7 1.4-1.3z"
            fill="#979797"
          />
        </svg>
      </div>
    </button>
    <div
      class="emotion-4"
    >
      <span>
        Page 3
      </span>
      <span>
         of 
      </span>
      4
    </div>
    <button
      aria-label="Next Page"
      class="emotion-1"
    >
      <div
        class="emotion-2"
      >
        <svg
          enable-background="new -134 82.3 7 12"
          height="12"
          viewBox="-134 82.3 7 12"
          width="7"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M-134 92.3l4.2-4.4-4.1-4.2 1.4-1.4 5.5 5.6-5.6 5.7-1.4-1.3z"
          />
        </svg>
      </div>
    </button>
  </nav>
</DocumentFragment>
`;

exports[`Paginator snapshots renders default state correctly OldNavy 1`] = `
<DocumentFragment>
  .emotion-0 {
  clear: both;
  text-align: center;
}

.emotion-1 {
  background: none;
  border: none;
  width: auto;
  margin: 0;
  padding: 0;
  text-align: inherit;
  font: inherit;
  display: inline-block;
  vertical-align: middle;
  box-sizing: border-box;
  height: 2.5em;
  width: 2.5em;
  cursor: pointer;
}

.emotion-1 path {
  fill: #003764;
}

.emotion-1:hover path,
.emotion-1:active path {
  fill: #003764;
}

.emotion-2 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  padding-bottom: 2px;
}

.emotion-3 {
  vertical-align: middle;
  display: inline-block;
}

.emotion-4 {
  font-family: 'Gotham','Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  color: #666;
  font-size: 1.067rem;
  display: inline-block;
}

<nav
    aria-label="Paginator"
    class="emotion-0"
  >
    <button
      aria-label="Previous Page"
      class="emotion-1"
    >
      <div
        class="emotion-2"
      >
        <svg
          class="emotion-3"
          enable-background="new -134 82.3 7 12"
          height="12"
          viewBox="-134 82.3 7 12"
          width="7"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M-127 92.3l-4.3-4.4 4.2-4.2-1.4-1.4-5.5 5.6 5.6 5.7 1.4-1.3z"
            fill="#979797"
          />
        </svg>
      </div>
    </button>
    <div
      class="emotion-4"
    >
      <span>
        Page 3
      </span>
      <span>
         of 
      </span>
      4
    </div>
    <button
      aria-label="Next Page"
      class="emotion-1"
    >
      <div
        class="emotion-2"
      >
        <svg
          enable-background="new -134 82.3 7 12"
          height="12"
          viewBox="-134 82.3 7 12"
          width="7"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M-134 92.3l4.2-4.4-4.1-4.2 1.4-1.4 5.5 5.6-5.6 5.7-1.4-1.3z"
          />
        </svg>
      </div>
    </button>
  </nav>
</DocumentFragment>
`;

exports[`Paginator snapshots renders default state correctly OldNavy in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  clear: both;
  text-align: center;
}

.emotion-1 {
  background: none;
  border: none;
  width: auto;
  margin: 0;
  padding: 0;
  text-align: inherit;
  font: inherit;
  display: inline-block;
  vertical-align: middle;
  box-sizing: border-box;
  height: 2.5em;
  width: 2.5em;
  cursor: pointer;
}

.emotion-1 path {
  fill: #666;
}

.emotion-1:hover path,
.emotion-1:active path {
  fill: #979797;
}

.emotion-2 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  padding-bottom: 2px;
}

.emotion-3 {
  vertical-align: middle;
  display: inline-block;
}

.emotion-4 {
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  color: #666;
  font-size: 1rem;
  display: inline-block;
}

<nav
    aria-label="Paginator"
    class="emotion-0"
  >
    <button
      aria-label="Previous Page"
      class="emotion-1"
    >
      <div
        class="emotion-2"
      >
        <svg
          class="emotion-3"
          enable-background="new -134 82.3 7 12"
          height="12"
          viewBox="-134 82.3 7 12"
          width="7"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M-127 92.3l-4.3-4.4 4.2-4.2-1.4-1.4-5.5 5.6 5.6 5.7 1.4-1.3z"
            fill="#979797"
          />
        </svg>
      </div>
    </button>
    <div
      class="emotion-4"
    >
      <span>
        Page 3
      </span>
      <span>
         of 
      </span>
      4
    </div>
    <button
      aria-label="Next Page"
      class="emotion-1"
    >
      <div
        class="emotion-2"
      >
        <svg
          enable-background="new -134 82.3 7 12"
          height="12"
          viewBox="-134 82.3 7 12"
          width="7"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M-134 92.3l4.2-4.4-4.1-4.2 1.4-1.4 5.5 5.6-5.6 5.7-1.4-1.3z"
          />
        </svg>
      </div>
    </button>
  </nav>
</DocumentFragment>
`;
