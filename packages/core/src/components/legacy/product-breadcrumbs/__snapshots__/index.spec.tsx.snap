// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`snapshots  for inverse styles renders default style state correctly Athleta 1`] = `
<DocumentFragment>
  .emotion-0 {
  font-family: 'Avenir Next','Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: left;
  font-weight: 350;
  font-size: 14px;
  line-height: 18.9px;
  color: #FFFFFF;
}

@media (min-width: 1024px) {
  .emotion-0 {
    width: 100%;
    max-width: 1034px;
  }
}

@media (min-width: 1280px) {
  .emotion-0 {
    max-width: 1250px;
  }
}

.emotion-1 {
  color: inherit;
  font-weight: 400;
}

.emotion-1:hover {
  -webkit-text-decoration: underline;
  text-decoration: underline;
}

<nav
    aria-label="product_breadcrumbs.product_breadcrumbs_aria_label"
    class="emotion-0"
  >
    <span>
      <a
        class="emotion-1"
        href="/browse/division.do?cid=5155&bc=true"
      >
        Women
      </a>
       / Jeans
    </span>
  </nav>
</DocumentFragment>
`;

exports[`snapshots  for inverse styles renders default style state correctly Athleta in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  font-family: 'Avenir Next','Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: left;
  font-weight: 350;
  font-size: 14px;
  line-height: 18.9px;
  color: #FFFFFF;
}

@media (min-width: 1024px) {
  .emotion-0 {
    width: 100%;
    max-width: 1034px;
  }
}

@media (min-width: 1280px) {
  .emotion-0 {
    max-width: 1250px;
  }
}

.emotion-1 {
  color: inherit;
  font-weight: 400;
}

.emotion-1:hover {
  -webkit-text-decoration: underline;
  text-decoration: underline;
}

<nav
    aria-label="product_breadcrumbs.product_breadcrumbs_aria_label"
    class="emotion-0"
  >
    <span>
      <a
        class="emotion-1"
        href="/browse/division.do?cid=5155&bc=true"
      >
        Women
      </a>
       / Jeans
    </span>
  </nav>
</DocumentFragment>
`;

exports[`snapshots  for inverse styles renders default style state correctly BananaRepublic 1`] = `
<DocumentFragment>
  .emotion-0 {
  font-family: 'Lynstone',Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  letter-spacing: 0.5px;
  text-align: left;
  font-weight: 350;
  font-size: 14px;
  line-height: 18.9px;
  color: #F6F4EB;
}

@media (min-width: 1024px) {
  .emotion-0 {
    width: 100%;
    max-width: 1034px;
  }
}

@media (min-width: 1280px) {
  .emotion-0 {
    max-width: 1250px;
  }
}

.emotion-1 {
  color: inherit;
  font-weight: 400;
}

.emotion-1:hover {
  -webkit-text-decoration: underline;
  text-decoration: underline;
}

<nav
    aria-label="product_breadcrumbs.product_breadcrumbs_aria_label"
    class="emotion-0"
  >
    <span>
      <a
        class="emotion-1"
        href="/browse/division.do?cid=5155&bc=true"
      >
        Women
      </a>
       / Jeans
    </span>
  </nav>
</DocumentFragment>
`;

exports[`snapshots  for inverse styles renders default style state correctly BananaRepublic in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  font-family: 'Lynstone',Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  letter-spacing: 0.5px;
  text-align: left;
  font-weight: 350;
  font-size: 14px;
  line-height: 18.9px;
  color: #F6F4EB;
}

@media (min-width: 1024px) {
  .emotion-0 {
    width: 100%;
    max-width: 1034px;
  }
}

@media (min-width: 1280px) {
  .emotion-0 {
    max-width: 1250px;
  }
}

.emotion-1 {
  color: inherit;
  font-weight: 400;
}

.emotion-1:hover {
  -webkit-text-decoration: underline;
  text-decoration: underline;
}

<nav
    aria-label="product_breadcrumbs.product_breadcrumbs_aria_label"
    class="emotion-0"
  >
    <span>
      <a
        class="emotion-1"
        href="/browse/division.do?cid=5155&bc=true"
      >
        Women
      </a>
       / Jeans
    </span>
  </nav>
</DocumentFragment>
`;

exports[`snapshots  for inverse styles renders default style state correctly BananaRepublicFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  font-family: 'Lynstone',Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: left;
  font-weight: 350;
  font-size: 14px;
  line-height: 18.9px;
  color: #F6F4EB;
}

@media (min-width: 1024px) {
  .emotion-0 {
    width: 100%;
    max-width: 1034px;
  }
}

@media (min-width: 1280px) {
  .emotion-0 {
    max-width: 1250px;
  }
}

.emotion-1 {
  color: inherit;
  font-weight: 400;
}

.emotion-1:hover {
  -webkit-text-decoration: underline;
  text-decoration: underline;
}

<nav
    aria-label="product_breadcrumbs.product_breadcrumbs_aria_label"
    class="emotion-0"
  >
    <span>
      <a
        class="emotion-1"
        href="/browse/division.do?cid=5155&bc=true"
      >
        Women
      </a>
       / Jeans
    </span>
  </nav>
</DocumentFragment>
`;

exports[`snapshots  for inverse styles renders default style state correctly BananaRepublicFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  font-family: 'Lynstone',Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: left;
  font-weight: 350;
  font-size: 14px;
  line-height: 18.9px;
  color: #F6F4EB;
}

@media (min-width: 1024px) {
  .emotion-0 {
    width: 100%;
    max-width: 1034px;
  }
}

@media (min-width: 1280px) {
  .emotion-0 {
    max-width: 1250px;
  }
}

.emotion-1 {
  color: inherit;
  font-weight: 400;
}

.emotion-1:hover {
  -webkit-text-decoration: underline;
  text-decoration: underline;
}

<nav
    aria-label="product_breadcrumbs.product_breadcrumbs_aria_label"
    class="emotion-0"
  >
    <span>
      <a
        class="emotion-1"
        href="/browse/division.do?cid=5155&bc=true"
      >
        Women
      </a>
       / Jeans
    </span>
  </nav>
</DocumentFragment>
`;

exports[`snapshots  for inverse styles renders default style state correctly Gap 1`] = `
<DocumentFragment>
  .emotion-0 {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: left;
  font-weight: 350;
  font-size: 14px;
  line-height: 18.9px;
  color: #FFFFFF;
}

@media (min-width: 1024px) {
  .emotion-0 {
    width: 100%;
    max-width: 1034px;
  }
}

@media (min-width: 1280px) {
  .emotion-0 {
    max-width: 1250px;
  }
}

.emotion-1 {
  color: inherit;
  font-weight: 400;
}

.emotion-1:hover {
  -webkit-text-decoration: underline;
  text-decoration: underline;
}

<nav
    aria-label="product_breadcrumbs.product_breadcrumbs_aria_label"
    class="emotion-0"
  >
    <span>
      <a
        class="emotion-1"
        href="/browse/division.do?cid=5155&bc=true"
      >
        Women
      </a>
       / Jeans
    </span>
  </nav>
</DocumentFragment>
`;

exports[`snapshots  for inverse styles renders default style state correctly Gap in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: left;
  font-weight: 350;
  font-size: 14px;
  line-height: 18.9px;
  color: #FFFFFF;
}

@media (min-width: 1024px) {
  .emotion-0 {
    width: 100%;
    max-width: 1034px;
  }
}

@media (min-width: 1280px) {
  .emotion-0 {
    max-width: 1250px;
  }
}

.emotion-1 {
  color: inherit;
  font-weight: 400;
}

.emotion-1:hover {
  -webkit-text-decoration: underline;
  text-decoration: underline;
}

<nav
    aria-label="product_breadcrumbs.product_breadcrumbs_aria_label"
    class="emotion-0"
  >
    <span>
      <a
        class="emotion-1"
        href="/browse/division.do?cid=5155&bc=true"
      >
        Women
      </a>
       / Jeans
    </span>
  </nav>
</DocumentFragment>
`;

exports[`snapshots  for inverse styles renders default style state correctly GapFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: left;
  font-weight: 350;
  font-size: 14px;
  line-height: 18.9px;
  color: #FFFFFF;
}

@media (min-width: 1024px) {
  .emotion-0 {
    width: 100%;
    max-width: 1034px;
  }
}

@media (min-width: 1280px) {
  .emotion-0 {
    max-width: 1250px;
  }
}

.emotion-1 {
  color: inherit;
  font-weight: 400;
}

.emotion-1:hover {
  -webkit-text-decoration: underline;
  text-decoration: underline;
}

<nav
    aria-label="product_breadcrumbs.product_breadcrumbs_aria_label"
    class="emotion-0"
  >
    <span>
      <a
        class="emotion-1"
        href="/browse/division.do?cid=5155&bc=true"
      >
        Women
      </a>
       / Jeans
    </span>
  </nav>
</DocumentFragment>
`;

exports[`snapshots  for inverse styles renders default style state correctly GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: left;
  font-weight: 350;
  font-size: 14px;
  line-height: 18.9px;
  color: #FFFFFF;
}

@media (min-width: 1024px) {
  .emotion-0 {
    width: 100%;
    max-width: 1034px;
  }
}

@media (min-width: 1280px) {
  .emotion-0 {
    max-width: 1250px;
  }
}

.emotion-1 {
  color: inherit;
  font-weight: 400;
}

.emotion-1:hover {
  -webkit-text-decoration: underline;
  text-decoration: underline;
}

<nav
    aria-label="product_breadcrumbs.product_breadcrumbs_aria_label"
    class="emotion-0"
  >
    <span>
      <a
        class="emotion-1"
        href="/browse/division.do?cid=5155&bc=true"
      >
        Women
      </a>
       / Jeans
    </span>
  </nav>
</DocumentFragment>
`;

exports[`snapshots  for inverse styles renders default style state correctly OldNavy 1`] = `
<DocumentFragment>
  .emotion-0 {
  font-family: 'Gotham','Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: left;
  font-weight: 350;
  font-size: 14px;
  line-height: 18.9px;
  color: #FFFFFF;
}

@media (min-width: 1024px) {
  .emotion-0 {
    width: 100%;
    max-width: 1034px;
  }
}

@media (min-width: 1280px) {
  .emotion-0 {
    max-width: 1250px;
  }
}

.emotion-1 {
  color: inherit;
  font-weight: 400;
}

.emotion-1:hover {
  -webkit-text-decoration: underline;
  text-decoration: underline;
}

<nav
    aria-label="product_breadcrumbs.product_breadcrumbs_aria_label"
    class="emotion-0"
  >
    <span>
      <a
        class="emotion-1"
        href="/browse/division.do?cid=5155&bc=true"
      >
        Women
      </a>
       / Jeans
    </span>
  </nav>
</DocumentFragment>
`;

exports[`snapshots  for inverse styles renders default style state correctly OldNavy in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  font-family: 'Gotham','Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: left;
  font-weight: 350;
  font-size: 14px;
  line-height: 18.9px;
  color: #FFFFFF;
}

@media (min-width: 1024px) {
  .emotion-0 {
    width: 100%;
    max-width: 1034px;
  }
}

@media (min-width: 1280px) {
  .emotion-0 {
    max-width: 1250px;
  }
}

.emotion-1 {
  color: inherit;
  font-weight: 400;
}

.emotion-1:hover {
  -webkit-text-decoration: underline;
  text-decoration: underline;
}

<nav
    aria-label="product_breadcrumbs.product_breadcrumbs_aria_label"
    class="emotion-0"
  >
    <span>
      <a
        class="emotion-1"
        href="/browse/division.do?cid=5155&bc=true"
      >
        Women
      </a>
       / Jeans
    </span>
  </nav>
</DocumentFragment>
`;

exports[`snapshots  for large breakpoint renders default style state correctly Athleta 1`] = `
<DocumentFragment>
  .emotion-0 {
  font-family: 'Avenir Next','Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: left;
  font-weight: 350;
  font-size: 14px;
  line-height: 18.9px;
  color: #666;
}

@media (min-width: 1024px) {
  .emotion-0 {
    width: 100%;
    max-width: 1034px;
  }
}

@media (min-width: 1280px) {
  .emotion-0 {
    max-width: 1250px;
  }
}

.emotion-1 {
  color: inherit;
  font-weight: 400;
}

.emotion-1:hover {
  -webkit-text-decoration: underline;
  text-decoration: underline;
}

<nav
    aria-label="product_breadcrumbs.product_breadcrumbs_aria_label"
    class="emotion-0"
  >
    <span>
      <a
        class="emotion-1"
        href="/browse/division.do?cid=5155&bc=true"
      >
        Women
      </a>
       / Jeans
    </span>
  </nav>
</DocumentFragment>
`;

exports[`snapshots  for large breakpoint renders default style state correctly Athleta in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  font-family: 'Avenir Next','Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: left;
  font-weight: 350;
  font-size: 14px;
  line-height: 18.9px;
  color: #666;
}

@media (min-width: 1024px) {
  .emotion-0 {
    width: 100%;
    max-width: 1034px;
  }
}

@media (min-width: 1280px) {
  .emotion-0 {
    max-width: 1250px;
  }
}

.emotion-1 {
  color: inherit;
  font-weight: 400;
}

.emotion-1:hover {
  -webkit-text-decoration: underline;
  text-decoration: underline;
}

<nav
    aria-label="product_breadcrumbs.product_breadcrumbs_aria_label"
    class="emotion-0"
  >
    <span>
      <a
        class="emotion-1"
        href="/browse/division.do?cid=5155&bc=true"
      >
        Women
      </a>
       / Jeans
    </span>
  </nav>
</DocumentFragment>
`;

exports[`snapshots  for large breakpoint renders default style state correctly BananaRepublic 1`] = `
<DocumentFragment>
  .emotion-0 {
  font-family: 'Lynstone',Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  letter-spacing: 0.5px;
  text-align: left;
  font-weight: 350;
  font-size: 14px;
  line-height: 18.9px;
  color: #666;
}

@media (min-width: 1024px) {
  .emotion-0 {
    width: 100%;
    max-width: 1034px;
  }
}

@media (min-width: 1280px) {
  .emotion-0 {
    max-width: 1250px;
  }
}

.emotion-1 {
  color: inherit;
  font-weight: 400;
}

.emotion-1:hover {
  -webkit-text-decoration: underline;
  text-decoration: underline;
}

<nav
    aria-label="product_breadcrumbs.product_breadcrumbs_aria_label"
    class="emotion-0"
  >
    <span>
      <a
        class="emotion-1"
        href="/browse/division.do?cid=5155&bc=true"
      >
        Women
      </a>
       / Jeans
    </span>
  </nav>
</DocumentFragment>
`;

exports[`snapshots  for large breakpoint renders default style state correctly BananaRepublic in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  font-family: 'Lynstone',Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  letter-spacing: 0.5px;
  text-align: left;
  font-weight: 350;
  font-size: 14px;
  line-height: 18.9px;
  color: #666;
}

@media (min-width: 1024px) {
  .emotion-0 {
    width: 100%;
    max-width: 1034px;
  }
}

@media (min-width: 1280px) {
  .emotion-0 {
    max-width: 1250px;
  }
}

.emotion-1 {
  color: inherit;
  font-weight: 400;
}

.emotion-1:hover {
  -webkit-text-decoration: underline;
  text-decoration: underline;
}

<nav
    aria-label="product_breadcrumbs.product_breadcrumbs_aria_label"
    class="emotion-0"
  >
    <span>
      <a
        class="emotion-1"
        href="/browse/division.do?cid=5155&bc=true"
      >
        Women
      </a>
       / Jeans
    </span>
  </nav>
</DocumentFragment>
`;

exports[`snapshots  for large breakpoint renders default style state correctly BananaRepublicFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  font-family: 'Lynstone',Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: left;
  font-weight: 350;
  font-size: 14px;
  line-height: 18.9px;
  color: #666;
}

@media (min-width: 1024px) {
  .emotion-0 {
    width: 100%;
    max-width: 1034px;
  }
}

@media (min-width: 1280px) {
  .emotion-0 {
    max-width: 1250px;
  }
}

.emotion-1 {
  color: inherit;
  font-weight: 400;
}

.emotion-1:hover {
  -webkit-text-decoration: underline;
  text-decoration: underline;
}

<nav
    aria-label="product_breadcrumbs.product_breadcrumbs_aria_label"
    class="emotion-0"
  >
    <span>
      <a
        class="emotion-1"
        href="/browse/division.do?cid=5155&bc=true"
      >
        Women
      </a>
       / Jeans
    </span>
  </nav>
</DocumentFragment>
`;

exports[`snapshots  for large breakpoint renders default style state correctly BananaRepublicFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  font-family: 'Lynstone',Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: left;
  font-weight: 350;
  font-size: 14px;
  line-height: 18.9px;
  color: #666;
}

@media (min-width: 1024px) {
  .emotion-0 {
    width: 100%;
    max-width: 1034px;
  }
}

@media (min-width: 1280px) {
  .emotion-0 {
    max-width: 1250px;
  }
}

.emotion-1 {
  color: inherit;
  font-weight: 400;
}

.emotion-1:hover {
  -webkit-text-decoration: underline;
  text-decoration: underline;
}

<nav
    aria-label="product_breadcrumbs.product_breadcrumbs_aria_label"
    class="emotion-0"
  >
    <span>
      <a
        class="emotion-1"
        href="/browse/division.do?cid=5155&bc=true"
      >
        Women
      </a>
       / Jeans
    </span>
  </nav>
</DocumentFragment>
`;

exports[`snapshots  for large breakpoint renders default style state correctly Gap 1`] = `
<DocumentFragment>
  .emotion-0 {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: left;
  font-weight: 350;
  font-size: 14px;
  line-height: 18.9px;
  color: #666;
}

@media (min-width: 1024px) {
  .emotion-0 {
    width: 100%;
    max-width: 1034px;
  }
}

@media (min-width: 1280px) {
  .emotion-0 {
    max-width: 1250px;
  }
}

.emotion-1 {
  color: inherit;
  font-weight: 400;
}

.emotion-1:hover {
  -webkit-text-decoration: underline;
  text-decoration: underline;
}

<nav
    aria-label="product_breadcrumbs.product_breadcrumbs_aria_label"
    class="emotion-0"
  >
    <span>
      <a
        class="emotion-1"
        href="/browse/division.do?cid=5155&bc=true"
      >
        Women
      </a>
       / Jeans
    </span>
  </nav>
</DocumentFragment>
`;

exports[`snapshots  for large breakpoint renders default style state correctly Gap in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: left;
  font-weight: 350;
  font-size: 14px;
  line-height: 18.9px;
  color: #666;
}

@media (min-width: 1024px) {
  .emotion-0 {
    width: 100%;
    max-width: 1034px;
  }
}

@media (min-width: 1280px) {
  .emotion-0 {
    max-width: 1250px;
  }
}

.emotion-1 {
  color: inherit;
  font-weight: 400;
}

.emotion-1:hover {
  -webkit-text-decoration: underline;
  text-decoration: underline;
}

<nav
    aria-label="product_breadcrumbs.product_breadcrumbs_aria_label"
    class="emotion-0"
  >
    <span>
      <a
        class="emotion-1"
        href="/browse/division.do?cid=5155&bc=true"
      >
        Women
      </a>
       / Jeans
    </span>
  </nav>
</DocumentFragment>
`;

exports[`snapshots  for large breakpoint renders default style state correctly GapFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: left;
  font-weight: 350;
  font-size: 14px;
  line-height: 18.9px;
  color: #666;
}

@media (min-width: 1024px) {
  .emotion-0 {
    width: 100%;
    max-width: 1034px;
  }
}

@media (min-width: 1280px) {
  .emotion-0 {
    max-width: 1250px;
  }
}

.emotion-1 {
  color: inherit;
  font-weight: 400;
}

.emotion-1:hover {
  -webkit-text-decoration: underline;
  text-decoration: underline;
}

<nav
    aria-label="product_breadcrumbs.product_breadcrumbs_aria_label"
    class="emotion-0"
  >
    <span>
      <a
        class="emotion-1"
        href="/browse/division.do?cid=5155&bc=true"
      >
        Women
      </a>
       / Jeans
    </span>
  </nav>
</DocumentFragment>
`;

exports[`snapshots  for large breakpoint renders default style state correctly GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: left;
  font-weight: 350;
  font-size: 14px;
  line-height: 18.9px;
  color: #666;
}

@media (min-width: 1024px) {
  .emotion-0 {
    width: 100%;
    max-width: 1034px;
  }
}

@media (min-width: 1280px) {
  .emotion-0 {
    max-width: 1250px;
  }
}

.emotion-1 {
  color: inherit;
  font-weight: 400;
}

.emotion-1:hover {
  -webkit-text-decoration: underline;
  text-decoration: underline;
}

<nav
    aria-label="product_breadcrumbs.product_breadcrumbs_aria_label"
    class="emotion-0"
  >
    <span>
      <a
        class="emotion-1"
        href="/browse/division.do?cid=5155&bc=true"
      >
        Women
      </a>
       / Jeans
    </span>
  </nav>
</DocumentFragment>
`;

exports[`snapshots  for large breakpoint renders default style state correctly OldNavy 1`] = `
<DocumentFragment>
  .emotion-0 {
  font-family: 'Gotham','Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: left;
  font-weight: 350;
  font-size: 14px;
  line-height: 18.9px;
  color: #666;
}

@media (min-width: 1024px) {
  .emotion-0 {
    width: 100%;
    max-width: 1034px;
  }
}

@media (min-width: 1280px) {
  .emotion-0 {
    max-width: 1250px;
  }
}

.emotion-1 {
  color: inherit;
  font-weight: 400;
}

.emotion-1:hover {
  -webkit-text-decoration: underline;
  text-decoration: underline;
}

<nav
    aria-label="product_breadcrumbs.product_breadcrumbs_aria_label"
    class="emotion-0"
  >
    <span>
      <a
        class="emotion-1"
        href="/browse/division.do?cid=5155&bc=true"
      >
        Women
      </a>
       / Jeans
    </span>
  </nav>
</DocumentFragment>
`;

exports[`snapshots  for large breakpoint renders default style state correctly OldNavy in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  font-family: 'Gotham','Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: left;
  font-weight: 350;
  font-size: 14px;
  line-height: 18.9px;
  color: #666;
}

@media (min-width: 1024px) {
  .emotion-0 {
    width: 100%;
    max-width: 1034px;
  }
}

@media (min-width: 1280px) {
  .emotion-0 {
    max-width: 1250px;
  }
}

.emotion-1 {
  color: inherit;
  font-weight: 400;
}

.emotion-1:hover {
  -webkit-text-decoration: underline;
  text-decoration: underline;
}

<nav
    aria-label="product_breadcrumbs.product_breadcrumbs_aria_label"
    class="emotion-0"
  >
    <span>
      <a
        class="emotion-1"
        href="/browse/division.do?cid=5155&bc=true"
      >
        Women
      </a>
       / Jeans
    </span>
  </nav>
</DocumentFragment>
`;

exports[`snapshots  for small breakpoint renders default style state correctly Athleta 1`] = `
<DocumentFragment>
  .emotion-0 {
  font-family: 'Avenir Next','Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: left;
  font-weight: 350;
  font-size: 14px;
  line-height: 18.9px;
  color: #666;
}

@media (min-width: 1024px) {
  .emotion-0 {
    width: 100%;
    max-width: 1034px;
  }
}

@media (min-width: 1280px) {
  .emotion-0 {
    max-width: 1250px;
  }
}

.emotion-1 {
  color: inherit;
  font-weight: 400;
}

.emotion-1:hover {
  -webkit-text-decoration: underline;
  text-decoration: underline;
}

<nav
    aria-label="product_breadcrumbs.product_breadcrumbs_aria_label"
    class="emotion-0"
  >
    <span>
      <a
        class="emotion-1"
        href="/browse/division.do?cid=5155&bc=true"
      >
        Women
      </a>
       / Jeans
    </span>
  </nav>
</DocumentFragment>
`;

exports[`snapshots  for small breakpoint renders default style state correctly Athleta in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  font-family: 'Avenir Next','Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: left;
  font-weight: 350;
  font-size: 14px;
  line-height: 18.9px;
  color: #666;
}

@media (min-width: 1024px) {
  .emotion-0 {
    width: 100%;
    max-width: 1034px;
  }
}

@media (min-width: 1280px) {
  .emotion-0 {
    max-width: 1250px;
  }
}

.emotion-1 {
  color: inherit;
  font-weight: 400;
}

.emotion-1:hover {
  -webkit-text-decoration: underline;
  text-decoration: underline;
}

<nav
    aria-label="product_breadcrumbs.product_breadcrumbs_aria_label"
    class="emotion-0"
  >
    <span>
      <a
        class="emotion-1"
        href="/browse/division.do?cid=5155&bc=true"
      >
        Women
      </a>
       / Jeans
    </span>
  </nav>
</DocumentFragment>
`;

exports[`snapshots  for small breakpoint renders default style state correctly BananaRepublic 1`] = `
<DocumentFragment>
  .emotion-0 {
  font-family: 'Lynstone',Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  letter-spacing: 0.5px;
  text-align: left;
  font-weight: 350;
  font-size: 14px;
  line-height: 18.9px;
  color: #666;
}

@media (min-width: 1024px) {
  .emotion-0 {
    width: 100%;
    max-width: 1034px;
  }
}

@media (min-width: 1280px) {
  .emotion-0 {
    max-width: 1250px;
  }
}

.emotion-1 {
  color: inherit;
  font-weight: 400;
}

.emotion-1:hover {
  -webkit-text-decoration: underline;
  text-decoration: underline;
}

<nav
    aria-label="product_breadcrumbs.product_breadcrumbs_aria_label"
    class="emotion-0"
  >
    <span>
      <a
        class="emotion-1"
        href="/browse/division.do?cid=5155&bc=true"
      >
        Women
      </a>
       / Jeans
    </span>
  </nav>
</DocumentFragment>
`;

exports[`snapshots  for small breakpoint renders default style state correctly BananaRepublic in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  font-family: 'Lynstone',Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  letter-spacing: 0.5px;
  text-align: left;
  font-weight: 350;
  font-size: 14px;
  line-height: 18.9px;
  color: #666;
}

@media (min-width: 1024px) {
  .emotion-0 {
    width: 100%;
    max-width: 1034px;
  }
}

@media (min-width: 1280px) {
  .emotion-0 {
    max-width: 1250px;
  }
}

.emotion-1 {
  color: inherit;
  font-weight: 400;
}

.emotion-1:hover {
  -webkit-text-decoration: underline;
  text-decoration: underline;
}

<nav
    aria-label="product_breadcrumbs.product_breadcrumbs_aria_label"
    class="emotion-0"
  >
    <span>
      <a
        class="emotion-1"
        href="/browse/division.do?cid=5155&bc=true"
      >
        Women
      </a>
       / Jeans
    </span>
  </nav>
</DocumentFragment>
`;

exports[`snapshots  for small breakpoint renders default style state correctly BananaRepublicFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  font-family: 'Lynstone',Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: left;
  font-weight: 350;
  font-size: 14px;
  line-height: 18.9px;
  color: #666;
}

@media (min-width: 1024px) {
  .emotion-0 {
    width: 100%;
    max-width: 1034px;
  }
}

@media (min-width: 1280px) {
  .emotion-0 {
    max-width: 1250px;
  }
}

.emotion-1 {
  color: inherit;
  font-weight: 400;
}

.emotion-1:hover {
  -webkit-text-decoration: underline;
  text-decoration: underline;
}

<nav
    aria-label="product_breadcrumbs.product_breadcrumbs_aria_label"
    class="emotion-0"
  >
    <span>
      <a
        class="emotion-1"
        href="/browse/division.do?cid=5155&bc=true"
      >
        Women
      </a>
       / Jeans
    </span>
  </nav>
</DocumentFragment>
`;

exports[`snapshots  for small breakpoint renders default style state correctly BananaRepublicFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  font-family: 'Lynstone',Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: left;
  font-weight: 350;
  font-size: 14px;
  line-height: 18.9px;
  color: #666;
}

@media (min-width: 1024px) {
  .emotion-0 {
    width: 100%;
    max-width: 1034px;
  }
}

@media (min-width: 1280px) {
  .emotion-0 {
    max-width: 1250px;
  }
}

.emotion-1 {
  color: inherit;
  font-weight: 400;
}

.emotion-1:hover {
  -webkit-text-decoration: underline;
  text-decoration: underline;
}

<nav
    aria-label="product_breadcrumbs.product_breadcrumbs_aria_label"
    class="emotion-0"
  >
    <span>
      <a
        class="emotion-1"
        href="/browse/division.do?cid=5155&bc=true"
      >
        Women
      </a>
       / Jeans
    </span>
  </nav>
</DocumentFragment>
`;

exports[`snapshots  for small breakpoint renders default style state correctly Gap 1`] = `
<DocumentFragment>
  .emotion-0 {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: left;
  font-weight: 350;
  font-size: 14px;
  line-height: 18.9px;
  color: #666;
}

@media (min-width: 1024px) {
  .emotion-0 {
    width: 100%;
    max-width: 1034px;
  }
}

@media (min-width: 1280px) {
  .emotion-0 {
    max-width: 1250px;
  }
}

.emotion-1 {
  color: inherit;
  font-weight: 400;
}

.emotion-1:hover {
  -webkit-text-decoration: underline;
  text-decoration: underline;
}

<nav
    aria-label="product_breadcrumbs.product_breadcrumbs_aria_label"
    class="emotion-0"
  >
    <span>
      <a
        class="emotion-1"
        href="/browse/division.do?cid=5155&bc=true"
      >
        Women
      </a>
       / Jeans
    </span>
  </nav>
</DocumentFragment>
`;

exports[`snapshots  for small breakpoint renders default style state correctly Gap in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: left;
  font-weight: 350;
  font-size: 14px;
  line-height: 18.9px;
  color: #666;
}

@media (min-width: 1024px) {
  .emotion-0 {
    width: 100%;
    max-width: 1034px;
  }
}

@media (min-width: 1280px) {
  .emotion-0 {
    max-width: 1250px;
  }
}

.emotion-1 {
  color: inherit;
  font-weight: 400;
}

.emotion-1:hover {
  -webkit-text-decoration: underline;
  text-decoration: underline;
}

<nav
    aria-label="product_breadcrumbs.product_breadcrumbs_aria_label"
    class="emotion-0"
  >
    <span>
      <a
        class="emotion-1"
        href="/browse/division.do?cid=5155&bc=true"
      >
        Women
      </a>
       / Jeans
    </span>
  </nav>
</DocumentFragment>
`;

exports[`snapshots  for small breakpoint renders default style state correctly GapFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: left;
  font-weight: 350;
  font-size: 14px;
  line-height: 18.9px;
  color: #666;
}

@media (min-width: 1024px) {
  .emotion-0 {
    width: 100%;
    max-width: 1034px;
  }
}

@media (min-width: 1280px) {
  .emotion-0 {
    max-width: 1250px;
  }
}

.emotion-1 {
  color: inherit;
  font-weight: 400;
}

.emotion-1:hover {
  -webkit-text-decoration: underline;
  text-decoration: underline;
}

<nav
    aria-label="product_breadcrumbs.product_breadcrumbs_aria_label"
    class="emotion-0"
  >
    <span>
      <a
        class="emotion-1"
        href="/browse/division.do?cid=5155&bc=true"
      >
        Women
      </a>
       / Jeans
    </span>
  </nav>
</DocumentFragment>
`;

exports[`snapshots  for small breakpoint renders default style state correctly GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: left;
  font-weight: 350;
  font-size: 14px;
  line-height: 18.9px;
  color: #666;
}

@media (min-width: 1024px) {
  .emotion-0 {
    width: 100%;
    max-width: 1034px;
  }
}

@media (min-width: 1280px) {
  .emotion-0 {
    max-width: 1250px;
  }
}

.emotion-1 {
  color: inherit;
  font-weight: 400;
}

.emotion-1:hover {
  -webkit-text-decoration: underline;
  text-decoration: underline;
}

<nav
    aria-label="product_breadcrumbs.product_breadcrumbs_aria_label"
    class="emotion-0"
  >
    <span>
      <a
        class="emotion-1"
        href="/browse/division.do?cid=5155&bc=true"
      >
        Women
      </a>
       / Jeans
    </span>
  </nav>
</DocumentFragment>
`;

exports[`snapshots  for small breakpoint renders default style state correctly OldNavy 1`] = `
<DocumentFragment>
  .emotion-0 {
  font-family: 'Gotham','Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: left;
  font-weight: 350;
  font-size: 14px;
  line-height: 18.9px;
  color: #666;
}

@media (min-width: 1024px) {
  .emotion-0 {
    width: 100%;
    max-width: 1034px;
  }
}

@media (min-width: 1280px) {
  .emotion-0 {
    max-width: 1250px;
  }
}

.emotion-1 {
  color: inherit;
  font-weight: 400;
}

.emotion-1:hover {
  -webkit-text-decoration: underline;
  text-decoration: underline;
}

<nav
    aria-label="product_breadcrumbs.product_breadcrumbs_aria_label"
    class="emotion-0"
  >
    <span>
      <a
        class="emotion-1"
        href="/browse/division.do?cid=5155&bc=true"
      >
        Women
      </a>
       / Jeans
    </span>
  </nav>
</DocumentFragment>
`;

exports[`snapshots  for small breakpoint renders default style state correctly OldNavy in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  font-family: 'Gotham','Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: left;
  font-weight: 350;
  font-size: 14px;
  line-height: 18.9px;
  color: #666;
}

@media (min-width: 1024px) {
  .emotion-0 {
    width: 100%;
    max-width: 1034px;
  }
}

@media (min-width: 1280px) {
  .emotion-0 {
    max-width: 1250px;
  }
}

.emotion-1 {
  color: inherit;
  font-weight: 400;
}

.emotion-1:hover {
  -webkit-text-decoration: underline;
  text-decoration: underline;
}

<nav
    aria-label="product_breadcrumbs.product_breadcrumbs_aria_label"
    class="emotion-0"
  >
    <span>
      <a
        class="emotion-1"
        href="/browse/division.do?cid=5155&bc=true"
      >
        Women
      </a>
       / Jeans
    </span>
  </nav>
</DocumentFragment>
`;

exports[`snapshots  renders default nav state correctly Athleta 1`] = `<DocumentFragment />`;

exports[`snapshots  renders default nav state correctly Athleta in crossBrand 1`] = `<DocumentFragment />`;

exports[`snapshots  renders default nav state correctly BananaRepublic 1`] = `<DocumentFragment />`;

exports[`snapshots  renders default nav state correctly BananaRepublic in crossBrand 1`] = `<DocumentFragment />`;

exports[`snapshots  renders default nav state correctly BananaRepublicFactoryStore 1`] = `<DocumentFragment />`;

exports[`snapshots  renders default nav state correctly BananaRepublicFactoryStore in crossBrand 1`] = `<DocumentFragment />`;

exports[`snapshots  renders default nav state correctly Gap 1`] = `<DocumentFragment />`;

exports[`snapshots  renders default nav state correctly Gap in crossBrand 1`] = `<DocumentFragment />`;

exports[`snapshots  renders default nav state correctly GapFactoryStore 1`] = `<DocumentFragment />`;

exports[`snapshots  renders default nav state correctly GapFactoryStore in crossBrand 1`] = `<DocumentFragment />`;

exports[`snapshots  renders default nav state correctly OldNavy 1`] = `<DocumentFragment />`;

exports[`snapshots  renders default nav state correctly OldNavy in crossBrand 1`] = `<DocumentFragment />`;
