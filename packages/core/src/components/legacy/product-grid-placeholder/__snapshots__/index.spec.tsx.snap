// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`<ProductGridPlaceholder/> BR Redesign For Brand: "br" Should render placeholders when featureFlag: "search-br-redesign" is set 1`] = `
@keyframes animation-0 {
  0%, 25% {
    -webkit-background-position: 100% 100%;
    background-position: 100% 100%;
  }

  90%, 100% {
    -webkit-background-position: 0 0;
    background-position: 0 0;
  }
}

@keyframes animation-0 {
  0%, 25% {
    -webkit-background-position: 100% 100%;
    background-position: 100% 100%;
  }

  90%, 100% {
    -webkit-background-position: 0 0;
    background-position: 0 0;
  }
}

@keyframes animation-0 {
  0%, 25% {
    -webkit-background-position: 100% 100%;
    background-position: 100% 100%;
  }

  90%, 100% {
    -webkit-background-position: 0 0;
    background-position: 0 0;
  }
}

@keyframes animation-0 {
  0%, 25% {
    -webkit-background-position: 100% 100%;
    background-position: 100% 100%;
  }

  90%, 100% {
    -webkit-background-position: 0 0;
    background-position: 0 0;
  }
}

.emotion-1 {
  display: grid;
  width: 100%;
  gap: 0.4rem;
}

@media (max-width: 767px) {
  .emotion-1 {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (min-width: 768px) and (max-width: 1279px) {
  .emotion-1 {
    grid-template-columns: repeat(3, 1fr);
  }
}

@media (min-width: 1280px) and (max-width: 1440px) {
  .emotion-1 {
    grid-template-columns: repeat(3, 1fr);
  }
}

@media (min-width: 1441px) {
  .emotion-1 {
    grid-template-columns: repeat(3, 1fr);
  }
}

.emotion-1 .placeholder-grid__container {
  width: 100%;
}

.emotion-1 .placeholder-grid__image {
  max-width: 100%;
  width: 100%;
}

.emotion-2 {
  background: linear-gradient(300.82deg, #D9D9D9 6.57%, #FBFAFA 51.24%, #D9D9D9 93.09%);
  position: relative;
  -webkit-background-size: 300% 100%;
  background-size: 300% 100%;
  -webkit-animation: animation-0 0.9s alternate 0s infinite ease-in-out;
  animation: animation-0 0.9s alternate 0s infinite ease-in-out;
  padding-bottom: 133.750%;
}

.emotion-3 {
  background: linear-gradient(300.82deg, #D9D9D9 6.57%, #FBFAFA 51.24%, #D9D9D9 93.09%);
  position: relative;
  -webkit-background-size: 300% 100%;
  background-size: 300% 100%;
  -webkit-animation: animation-0 0.9s alternate 0s infinite ease-in-out;
  animation: animation-0 0.9s alternate 0s infinite ease-in-out;
  width: 140px;
  height: 18px;
  padding-bottom: 0;
}

.emotion-4 {
  background: linear-gradient(300.82deg, #D9D9D9 6.57%, #FBFAFA 51.24%, #D9D9D9 93.09%);
  position: relative;
  -webkit-background-size: 300% 100%;
  background-size: 300% 100%;
  -webkit-animation: animation-0 0.9s alternate 0s infinite ease-in-out;
  animation: animation-0 0.9s alternate 0s infinite ease-in-out;
  width: 60px;
  height: 18px;
  padding-bottom: 0;
}

.emotion-5 {
  background: linear-gradient(300.82deg, #D9D9D9 6.57%, #FBFAFA 51.24%, #D9D9D9 93.09%);
  position: relative;
  -webkit-background-size: 300% 100%;
  background-size: 300% 100%;
  -webkit-animation: animation-0 0.9s alternate 0s infinite ease-in-out;
  animation: animation-0 0.9s alternate 0s infinite ease-in-out;
  width: 120px;
  height: 18px;
  padding-bottom: 0;
}

<div>
  <div
    class="emotion-0"
  >
    <div
      class="placeholder-grid emotion-1"
    >
      <div
        class="placeholder-grid__container"
        style="float: left; overflow: hidden;"
      >
        <div
          style="padding-bottom: .5rem;"
        >
          <div
            class="placeholder-grid__image emotion-2"
          />
        </div>
        <div
          style="padding-bottom: .5rem;"
        >
          <div
            class="emotion-3"
          />
        </div>
        <div
          style="padding-bottom: .5rem;"
        >
          <div
            class="emotion-4"
          />
        </div>
        <div>
          <div
            class="emotion-5"
          />
        </div>
      </div>
    </div>
  </div>
  <link
    data-testid="font-tracker"
    href="https://fast.fonts.net/t/1.css?apiType=css&projectid=0e5ae342-8d48-4e7a-9491-e82fee9dcc17"
    media="print"
    rel="stylesheet"
  />
</div>
`;

exports[`<ProductGridPlaceholder/> BR Redesign For Brand: "brfs" Should render placeholders when featureFlag: "search-br-redesign" is set 1`] = `
@keyframes animation-0 {
  0%, 25% {
    -webkit-background-position: 100% 100%;
    background-position: 100% 100%;
  }

  90%, 100% {
    -webkit-background-position: 0 0;
    background-position: 0 0;
  }
}

@keyframes animation-0 {
  0%, 25% {
    -webkit-background-position: 100% 100%;
    background-position: 100% 100%;
  }

  90%, 100% {
    -webkit-background-position: 0 0;
    background-position: 0 0;
  }
}

@keyframes animation-0 {
  0%, 25% {
    -webkit-background-position: 100% 100%;
    background-position: 100% 100%;
  }

  90%, 100% {
    -webkit-background-position: 0 0;
    background-position: 0 0;
  }
}

@keyframes animation-0 {
  0%, 25% {
    -webkit-background-position: 100% 100%;
    background-position: 100% 100%;
  }

  90%, 100% {
    -webkit-background-position: 0 0;
    background-position: 0 0;
  }
}

.emotion-1 {
  display: grid;
  width: 100%;
  gap: 0.4rem;
}

@media (max-width: 767px) {
  .emotion-1 {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (min-width: 768px) and (max-width: 1279px) {
  .emotion-1 {
    grid-template-columns: repeat(3, 1fr);
  }
}

@media (min-width: 1280px) and (max-width: 1440px) {
  .emotion-1 {
    grid-template-columns: repeat(3, 1fr);
  }
}

@media (min-width: 1441px) {
  .emotion-1 {
    grid-template-columns: repeat(3, 1fr);
  }
}

.emotion-1 .placeholder-grid__container {
  width: 100%;
}

.emotion-1 .placeholder-grid__image {
  max-width: 100%;
  width: 100%;
}

.emotion-2 {
  background: linear-gradient(300.82deg, #D9D9D9 6.57%, #FBFAFA 51.24%, #D9D9D9 93.09%);
  position: relative;
  -webkit-background-size: 300% 100%;
  background-size: 300% 100%;
  -webkit-animation: animation-0 0.9s alternate 0s infinite ease-in-out;
  animation: animation-0 0.9s alternate 0s infinite ease-in-out;
  padding-bottom: 133.750%;
}

.emotion-3 {
  background: linear-gradient(300.82deg, #D9D9D9 6.57%, #FBFAFA 51.24%, #D9D9D9 93.09%);
  position: relative;
  -webkit-background-size: 300% 100%;
  background-size: 300% 100%;
  -webkit-animation: animation-0 0.9s alternate 0s infinite ease-in-out;
  animation: animation-0 0.9s alternate 0s infinite ease-in-out;
  width: 140px;
  height: 18px;
  padding-bottom: 0;
}

.emotion-4 {
  background: linear-gradient(300.82deg, #D9D9D9 6.57%, #FBFAFA 51.24%, #D9D9D9 93.09%);
  position: relative;
  -webkit-background-size: 300% 100%;
  background-size: 300% 100%;
  -webkit-animation: animation-0 0.9s alternate 0s infinite ease-in-out;
  animation: animation-0 0.9s alternate 0s infinite ease-in-out;
  width: 60px;
  height: 18px;
  padding-bottom: 0;
}

.emotion-5 {
  background: linear-gradient(300.82deg, #D9D9D9 6.57%, #FBFAFA 51.24%, #D9D9D9 93.09%);
  position: relative;
  -webkit-background-size: 300% 100%;
  background-size: 300% 100%;
  -webkit-animation: animation-0 0.9s alternate 0s infinite ease-in-out;
  animation: animation-0 0.9s alternate 0s infinite ease-in-out;
  width: 120px;
  height: 18px;
  padding-bottom: 0;
}

<div>
  <div
    class="emotion-0"
  >
    <div
      class="placeholder-grid emotion-1"
    >
      <div
        class="placeholder-grid__container"
        style="float: left; overflow: hidden;"
      >
        <div
          style="padding-bottom: .5rem;"
        >
          <div
            class="placeholder-grid__image emotion-2"
          />
        </div>
        <div
          style="padding-bottom: .5rem;"
        >
          <div
            class="emotion-3"
          />
        </div>
        <div
          style="padding-bottom: .5rem;"
        >
          <div
            class="emotion-4"
          />
        </div>
        <div>
          <div
            class="emotion-5"
          />
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`<ProductGridPlaceholder/> for medium breakpoint renders crossBrand state correctly Athleta 1`] = `
<DocumentFragment>
  <div
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`<ProductGridPlaceholder/> for medium breakpoint renders crossBrand state correctly Athleta in crossBrand 1`] = `
<DocumentFragment>
  <div
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`<ProductGridPlaceholder/> for medium breakpoint renders crossBrand state correctly BananaRepublic 1`] = `
<DocumentFragment>
  <div
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`<ProductGridPlaceholder/> for medium breakpoint renders crossBrand state correctly BananaRepublic in crossBrand 1`] = `
<DocumentFragment>
  <div
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`<ProductGridPlaceholder/> for medium breakpoint renders crossBrand state correctly BananaRepublicFactoryStore 1`] = `
<DocumentFragment>
  <div
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`<ProductGridPlaceholder/> for medium breakpoint renders crossBrand state correctly BananaRepublicFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  <div
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`<ProductGridPlaceholder/> for medium breakpoint renders crossBrand state correctly Gap 1`] = `
<DocumentFragment>
  <div
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`<ProductGridPlaceholder/> for medium breakpoint renders crossBrand state correctly Gap in crossBrand 1`] = `
<DocumentFragment>
  <div
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`<ProductGridPlaceholder/> for medium breakpoint renders crossBrand state correctly GapFactoryStore 1`] = `
<DocumentFragment>
  <div
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`<ProductGridPlaceholder/> for medium breakpoint renders crossBrand state correctly GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  <div
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`<ProductGridPlaceholder/> for medium breakpoint renders crossBrand state correctly OldNavy 1`] = `
<DocumentFragment>
  <div
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`<ProductGridPlaceholder/> for medium breakpoint renders crossBrand state correctly OldNavy in crossBrand 1`] = `
<DocumentFragment>
  <div
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`<ProductGridPlaceholder/> for small breakpoint renders crossBrand state correctly Athleta 1`] = `
<DocumentFragment>
  <div
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`<ProductGridPlaceholder/> for small breakpoint renders crossBrand state correctly Athleta in crossBrand 1`] = `
<DocumentFragment>
  <div
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`<ProductGridPlaceholder/> for small breakpoint renders crossBrand state correctly BananaRepublic 1`] = `
<DocumentFragment>
  <div
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`<ProductGridPlaceholder/> for small breakpoint renders crossBrand state correctly BananaRepublic in crossBrand 1`] = `
<DocumentFragment>
  <div
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`<ProductGridPlaceholder/> for small breakpoint renders crossBrand state correctly BananaRepublicFactoryStore 1`] = `
<DocumentFragment>
  <div
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`<ProductGridPlaceholder/> for small breakpoint renders crossBrand state correctly BananaRepublicFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  <div
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`<ProductGridPlaceholder/> for small breakpoint renders crossBrand state correctly Gap 1`] = `
<DocumentFragment>
  <div
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`<ProductGridPlaceholder/> for small breakpoint renders crossBrand state correctly Gap in crossBrand 1`] = `
<DocumentFragment>
  <div
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`<ProductGridPlaceholder/> for small breakpoint renders crossBrand state correctly GapFactoryStore 1`] = `
<DocumentFragment>
  <div
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`<ProductGridPlaceholder/> for small breakpoint renders crossBrand state correctly GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  <div
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`<ProductGridPlaceholder/> for small breakpoint renders crossBrand state correctly OldNavy 1`] = `
<DocumentFragment>
  <div
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`<ProductGridPlaceholder/> for small breakpoint renders crossBrand state correctly OldNavy in crossBrand 1`] = `
<DocumentFragment>
  <div
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`<ProductGridPlaceholder/> for x-large breakpoint renders crossBrand state correctly Athleta 1`] = `
<DocumentFragment>
  <div
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`<ProductGridPlaceholder/> for x-large breakpoint renders crossBrand state correctly Athleta in crossBrand 1`] = `
<DocumentFragment>
  <div
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`<ProductGridPlaceholder/> for x-large breakpoint renders crossBrand state correctly BananaRepublic 1`] = `
<DocumentFragment>
  <div
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`<ProductGridPlaceholder/> for x-large breakpoint renders crossBrand state correctly BananaRepublic in crossBrand 1`] = `
<DocumentFragment>
  <div
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`<ProductGridPlaceholder/> for x-large breakpoint renders crossBrand state correctly BananaRepublicFactoryStore 1`] = `
<DocumentFragment>
  <div
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`<ProductGridPlaceholder/> for x-large breakpoint renders crossBrand state correctly BananaRepublicFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  <div
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`<ProductGridPlaceholder/> for x-large breakpoint renders crossBrand state correctly Gap 1`] = `
<DocumentFragment>
  <div
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`<ProductGridPlaceholder/> for x-large breakpoint renders crossBrand state correctly Gap in crossBrand 1`] = `
<DocumentFragment>
  <div
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`<ProductGridPlaceholder/> for x-large breakpoint renders crossBrand state correctly GapFactoryStore 1`] = `
<DocumentFragment>
  <div
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`<ProductGridPlaceholder/> for x-large breakpoint renders crossBrand state correctly GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  <div
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`<ProductGridPlaceholder/> for x-large breakpoint renders crossBrand state correctly OldNavy 1`] = `
<DocumentFragment>
  <div
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`<ProductGridPlaceholder/> for x-large breakpoint renders crossBrand state correctly OldNavy in crossBrand 1`] = `
<DocumentFragment>
  <div
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`<ProductGridPlaceholder/> renders default state correctly Athleta 1`] = `
<DocumentFragment>
  @keyframes animation-0 {
  0%, 25% {
    -webkit-background-position: 100% 100%;
    background-position: 100% 100%;
  }

  90%, 100% {
    -webkit-background-position: 0 0;
    background-position: 0 0;
  }
}

@keyframes animation-0 {
  0%, 25% {
    -webkit-background-position: 100% 100%;
    background-position: 100% 100%;
  }

  90%, 100% {
    -webkit-background-position: 0 0;
    background-position: 0 0;
  }
}

@keyframes animation-0 {
  0%, 25% {
    -webkit-background-position: 100% 100%;
    background-position: 100% 100%;
  }

  90%, 100% {
    -webkit-background-position: 0 0;
    background-position: 0 0;
  }
}

@keyframes animation-0 {
  0%, 25% {
    -webkit-background-position: 100% 100%;
    background-position: 100% 100%;
  }

  90%, 100% {
    -webkit-background-position: 0 0;
    background-position: 0 0;
  }
}

.emotion-1 {
  display: grid;
  width: 100%;
  gap: 1em;
}

@media (max-width: 767px) {
  .emotion-1 {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (min-width: 768px) and (max-width: 1279px) {
  .emotion-1 {
    grid-template-columns: repeat(3, 1fr);
  }
}

@media (min-width: 1280px) and (max-width: 1440px) {
  .emotion-1 {
    grid-template-columns: repeat(4, 1fr);
  }
}

@media (min-width: 1441px) {
  .emotion-1 {
    grid-template-columns: repeat(4, 1fr);
  }
}

.emotion-1 .placeholder-grid__container {
  width: 100%;
}

.emotion-1 .placeholder-grid__image {
  max-width: 100%;
  width: 100%;
}

.emotion-2 {
  background: linear-gradient(300.82deg, #D9D9D9 6.57%, #FBFAFA 51.24%, #D9D9D9 93.09%);
  position: relative;
  -webkit-background-size: 300% 100%;
  background-size: 300% 100%;
  -webkit-animation: animation-0 0.9s alternate 0s infinite ease-in-out;
  animation: animation-0 0.9s alternate 0s infinite ease-in-out;
  padding-bottom: 133.750%;
}

.emotion-3 {
  background: linear-gradient(300.82deg, #D9D9D9 6.57%, #FBFAFA 51.24%, #D9D9D9 93.09%);
  position: relative;
  -webkit-background-size: 300% 100%;
  background-size: 300% 100%;
  -webkit-animation: animation-0 0.9s alternate 0s infinite ease-in-out;
  animation: animation-0 0.9s alternate 0s infinite ease-in-out;
  width: 140px;
  height: 18px;
  padding-bottom: 0;
}

.emotion-4 {
  background: linear-gradient(300.82deg, #D9D9D9 6.57%, #FBFAFA 51.24%, #D9D9D9 93.09%);
  position: relative;
  -webkit-background-size: 300% 100%;
  background-size: 300% 100%;
  -webkit-animation: animation-0 0.9s alternate 0s infinite ease-in-out;
  animation: animation-0 0.9s alternate 0s infinite ease-in-out;
  width: 60px;
  height: 18px;
  padding-bottom: 0;
}

.emotion-5 {
  background: linear-gradient(300.82deg, #D9D9D9 6.57%, #FBFAFA 51.24%, #D9D9D9 93.09%);
  position: relative;
  -webkit-background-size: 300% 100%;
  background-size: 300% 100%;
  -webkit-animation: animation-0 0.9s alternate 0s infinite ease-in-out;
  animation: animation-0 0.9s alternate 0s infinite ease-in-out;
  width: 120px;
  height: 18px;
  padding-bottom: 0;
}

<div
    class="emotion-0"
  >
    <div
      class="placeholder-grid emotion-1"
    >
      <div
        class="placeholder-grid__container"
        style="float: left; overflow: hidden;"
      >
        <div
          style="padding-bottom: .5rem;"
        >
          <div
            class="placeholder-grid__image emotion-2"
          />
        </div>
        <div
          style="padding-bottom: .5rem;"
        >
          <div
            class="emotion-3"
          />
        </div>
        <div
          style="padding-bottom: .5rem;"
        >
          <div
            class="emotion-4"
          />
        </div>
        <div>
          <div
            class="emotion-5"
          />
        </div>
      </div>
    </div>
  </div>
</DocumentFragment>
`;

exports[`<ProductGridPlaceholder/> renders default state correctly Athleta in crossBrand 1`] = `
<DocumentFragment>
  @keyframes animation-0 {
  0%, 25% {
    -webkit-background-position: 100% 100%;
    background-position: 100% 100%;
  }

  90%, 100% {
    -webkit-background-position: 0 0;
    background-position: 0 0;
  }
}

@keyframes animation-0 {
  0%, 25% {
    -webkit-background-position: 100% 100%;
    background-position: 100% 100%;
  }

  90%, 100% {
    -webkit-background-position: 0 0;
    background-position: 0 0;
  }
}

@keyframes animation-0 {
  0%, 25% {
    -webkit-background-position: 100% 100%;
    background-position: 100% 100%;
  }

  90%, 100% {
    -webkit-background-position: 0 0;
    background-position: 0 0;
  }
}

@keyframes animation-0 {
  0%, 25% {
    -webkit-background-position: 100% 100%;
    background-position: 100% 100%;
  }

  90%, 100% {
    -webkit-background-position: 0 0;
    background-position: 0 0;
  }
}

.emotion-1 {
  display: grid;
  width: 100%;
  gap: 1em;
}

@media (max-width: 767px) {
  .emotion-1 {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (min-width: 768px) and (max-width: 1279px) {
  .emotion-1 {
    grid-template-columns: repeat(3, 1fr);
  }
}

@media (min-width: 1280px) and (max-width: 1440px) {
  .emotion-1 {
    grid-template-columns: repeat(4, 1fr);
  }
}

@media (min-width: 1441px) {
  .emotion-1 {
    grid-template-columns: repeat(4, 1fr);
  }
}

.emotion-1 .placeholder-grid__container {
  width: 100%;
}

.emotion-1 .placeholder-grid__image {
  max-width: 100%;
  width: 100%;
}

.emotion-2 {
  background: linear-gradient(300.82deg, #D9D9D9 6.57%, #FBFAFA 51.24%, #D9D9D9 93.09%);
  position: relative;
  -webkit-background-size: 300% 100%;
  background-size: 300% 100%;
  -webkit-animation: animation-0 0.9s alternate 0s infinite ease-in-out;
  animation: animation-0 0.9s alternate 0s infinite ease-in-out;
  padding-bottom: 133.750%;
}

.emotion-3 {
  background: linear-gradient(300.82deg, #D9D9D9 6.57%, #FBFAFA 51.24%, #D9D9D9 93.09%);
  position: relative;
  -webkit-background-size: 300% 100%;
  background-size: 300% 100%;
  -webkit-animation: animation-0 0.9s alternate 0s infinite ease-in-out;
  animation: animation-0 0.9s alternate 0s infinite ease-in-out;
  width: 140px;
  height: 18px;
  padding-bottom: 0;
}

.emotion-4 {
  background: linear-gradient(300.82deg, #D9D9D9 6.57%, #FBFAFA 51.24%, #D9D9D9 93.09%);
  position: relative;
  -webkit-background-size: 300% 100%;
  background-size: 300% 100%;
  -webkit-animation: animation-0 0.9s alternate 0s infinite ease-in-out;
  animation: animation-0 0.9s alternate 0s infinite ease-in-out;
  width: 60px;
  height: 18px;
  padding-bottom: 0;
}

.emotion-5 {
  background: linear-gradient(300.82deg, #D9D9D9 6.57%, #FBFAFA 51.24%, #D9D9D9 93.09%);
  position: relative;
  -webkit-background-size: 300% 100%;
  background-size: 300% 100%;
  -webkit-animation: animation-0 0.9s alternate 0s infinite ease-in-out;
  animation: animation-0 0.9s alternate 0s infinite ease-in-out;
  width: 120px;
  height: 18px;
  padding-bottom: 0;
}

<div
    class="emotion-0"
  >
    <div
      class="placeholder-grid emotion-1"
    >
      <div
        class="placeholder-grid__container"
        style="float: left; overflow: hidden;"
      >
        <div
          style="padding-bottom: .5rem;"
        >
          <div
            class="placeholder-grid__image emotion-2"
          />
        </div>
        <div
          style="padding-bottom: .5rem;"
        >
          <div
            class="emotion-3"
          />
        </div>
        <div
          style="padding-bottom: .5rem;"
        >
          <div
            class="emotion-4"
          />
        </div>
        <div>
          <div
            class="emotion-5"
          />
        </div>
      </div>
    </div>
  </div>
</DocumentFragment>
`;

exports[`<ProductGridPlaceholder/> renders default state correctly BananaRepublic 1`] = `
<DocumentFragment>
  @keyframes animation-0 {
  0%, 25% {
    -webkit-background-position: 100% 100%;
    background-position: 100% 100%;
  }

  90%, 100% {
    -webkit-background-position: 0 0;
    background-position: 0 0;
  }
}

@keyframes animation-0 {
  0%, 25% {
    -webkit-background-position: 100% 100%;
    background-position: 100% 100%;
  }

  90%, 100% {
    -webkit-background-position: 0 0;
    background-position: 0 0;
  }
}

@keyframes animation-0 {
  0%, 25% {
    -webkit-background-position: 100% 100%;
    background-position: 100% 100%;
  }

  90%, 100% {
    -webkit-background-position: 0 0;
    background-position: 0 0;
  }
}

@keyframes animation-0 {
  0%, 25% {
    -webkit-background-position: 100% 100%;
    background-position: 100% 100%;
  }

  90%, 100% {
    -webkit-background-position: 0 0;
    background-position: 0 0;
  }
}

.emotion-1 {
  display: grid;
  width: 100%;
  gap: 1em;
}

@media (max-width: 767px) {
  .emotion-1 {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (min-width: 768px) and (max-width: 1279px) {
  .emotion-1 {
    grid-template-columns: repeat(3, 1fr);
  }
}

@media (min-width: 1280px) and (max-width: 1440px) {
  .emotion-1 {
    grid-template-columns: repeat(4, 1fr);
  }
}

@media (min-width: 1441px) {
  .emotion-1 {
    grid-template-columns: repeat(4, 1fr);
  }
}

.emotion-1 .placeholder-grid__container {
  width: 100%;
}

.emotion-1 .placeholder-grid__image {
  max-width: 100%;
  width: 100%;
}

.emotion-2 {
  background: linear-gradient(300.82deg, #D9D9D9 6.57%, #FBFAFA 51.24%, #D9D9D9 93.09%);
  position: relative;
  -webkit-background-size: 300% 100%;
  background-size: 300% 100%;
  -webkit-animation: animation-0 0.9s alternate 0s infinite ease-in-out;
  animation: animation-0 0.9s alternate 0s infinite ease-in-out;
  padding-bottom: 133.750%;
}

.emotion-3 {
  background: linear-gradient(300.82deg, #D9D9D9 6.57%, #FBFAFA 51.24%, #D9D9D9 93.09%);
  position: relative;
  -webkit-background-size: 300% 100%;
  background-size: 300% 100%;
  -webkit-animation: animation-0 0.9s alternate 0s infinite ease-in-out;
  animation: animation-0 0.9s alternate 0s infinite ease-in-out;
  width: 140px;
  height: 18px;
  padding-bottom: 0;
}

.emotion-4 {
  background: linear-gradient(300.82deg, #D9D9D9 6.57%, #FBFAFA 51.24%, #D9D9D9 93.09%);
  position: relative;
  -webkit-background-size: 300% 100%;
  background-size: 300% 100%;
  -webkit-animation: animation-0 0.9s alternate 0s infinite ease-in-out;
  animation: animation-0 0.9s alternate 0s infinite ease-in-out;
  width: 60px;
  height: 18px;
  padding-bottom: 0;
}

.emotion-5 {
  background: linear-gradient(300.82deg, #D9D9D9 6.57%, #FBFAFA 51.24%, #D9D9D9 93.09%);
  position: relative;
  -webkit-background-size: 300% 100%;
  background-size: 300% 100%;
  -webkit-animation: animation-0 0.9s alternate 0s infinite ease-in-out;
  animation: animation-0 0.9s alternate 0s infinite ease-in-out;
  width: 120px;
  height: 18px;
  padding-bottom: 0;
}

<div
    class="emotion-0"
  >
    <div
      class="placeholder-grid emotion-1"
    >
      <div
        class="placeholder-grid__container"
        style="float: left; overflow: hidden;"
      >
        <div
          style="padding-bottom: .5rem;"
        >
          <div
            class="placeholder-grid__image emotion-2"
          />
        </div>
        <div
          style="padding-bottom: .5rem;"
        >
          <div
            class="emotion-3"
          />
        </div>
        <div
          style="padding-bottom: .5rem;"
        >
          <div
            class="emotion-4"
          />
        </div>
        <div>
          <div
            class="emotion-5"
          />
        </div>
      </div>
    </div>
  </div>
</DocumentFragment>
`;

exports[`<ProductGridPlaceholder/> renders default state correctly BananaRepublic in crossBrand 1`] = `
<DocumentFragment>
  @keyframes animation-0 {
  0%, 25% {
    -webkit-background-position: 100% 100%;
    background-position: 100% 100%;
  }

  90%, 100% {
    -webkit-background-position: 0 0;
    background-position: 0 0;
  }
}

@keyframes animation-0 {
  0%, 25% {
    -webkit-background-position: 100% 100%;
    background-position: 100% 100%;
  }

  90%, 100% {
    -webkit-background-position: 0 0;
    background-position: 0 0;
  }
}

@keyframes animation-0 {
  0%, 25% {
    -webkit-background-position: 100% 100%;
    background-position: 100% 100%;
  }

  90%, 100% {
    -webkit-background-position: 0 0;
    background-position: 0 0;
  }
}

@keyframes animation-0 {
  0%, 25% {
    -webkit-background-position: 100% 100%;
    background-position: 100% 100%;
  }

  90%, 100% {
    -webkit-background-position: 0 0;
    background-position: 0 0;
  }
}

.emotion-1 {
  display: grid;
  width: 100%;
  gap: 1em;
}

@media (max-width: 767px) {
  .emotion-1 {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (min-width: 768px) and (max-width: 1279px) {
  .emotion-1 {
    grid-template-columns: repeat(3, 1fr);
  }
}

@media (min-width: 1280px) and (max-width: 1440px) {
  .emotion-1 {
    grid-template-columns: repeat(4, 1fr);
  }
}

@media (min-width: 1441px) {
  .emotion-1 {
    grid-template-columns: repeat(4, 1fr);
  }
}

.emotion-1 .placeholder-grid__container {
  width: 100%;
}

.emotion-1 .placeholder-grid__image {
  max-width: 100%;
  width: 100%;
}

.emotion-2 {
  background: linear-gradient(300.82deg, #D9D9D9 6.57%, #FBFAFA 51.24%, #D9D9D9 93.09%);
  position: relative;
  -webkit-background-size: 300% 100%;
  background-size: 300% 100%;
  -webkit-animation: animation-0 0.9s alternate 0s infinite ease-in-out;
  animation: animation-0 0.9s alternate 0s infinite ease-in-out;
  padding-bottom: 133.750%;
}

.emotion-3 {
  background: linear-gradient(300.82deg, #D9D9D9 6.57%, #FBFAFA 51.24%, #D9D9D9 93.09%);
  position: relative;
  -webkit-background-size: 300% 100%;
  background-size: 300% 100%;
  -webkit-animation: animation-0 0.9s alternate 0s infinite ease-in-out;
  animation: animation-0 0.9s alternate 0s infinite ease-in-out;
  width: 140px;
  height: 18px;
  padding-bottom: 0;
}

.emotion-4 {
  background: linear-gradient(300.82deg, #D9D9D9 6.57%, #FBFAFA 51.24%, #D9D9D9 93.09%);
  position: relative;
  -webkit-background-size: 300% 100%;
  background-size: 300% 100%;
  -webkit-animation: animation-0 0.9s alternate 0s infinite ease-in-out;
  animation: animation-0 0.9s alternate 0s infinite ease-in-out;
  width: 60px;
  height: 18px;
  padding-bottom: 0;
}

.emotion-5 {
  background: linear-gradient(300.82deg, #D9D9D9 6.57%, #FBFAFA 51.24%, #D9D9D9 93.09%);
  position: relative;
  -webkit-background-size: 300% 100%;
  background-size: 300% 100%;
  -webkit-animation: animation-0 0.9s alternate 0s infinite ease-in-out;
  animation: animation-0 0.9s alternate 0s infinite ease-in-out;
  width: 120px;
  height: 18px;
  padding-bottom: 0;
}

<div
    class="emotion-0"
  >
    <div
      class="placeholder-grid emotion-1"
    >
      <div
        class="placeholder-grid__container"
        style="float: left; overflow: hidden;"
      >
        <div
          style="padding-bottom: .5rem;"
        >
          <div
            class="placeholder-grid__image emotion-2"
          />
        </div>
        <div
          style="padding-bottom: .5rem;"
        >
          <div
            class="emotion-3"
          />
        </div>
        <div
          style="padding-bottom: .5rem;"
        >
          <div
            class="emotion-4"
          />
        </div>
        <div>
          <div
            class="emotion-5"
          />
        </div>
      </div>
    </div>
  </div>
</DocumentFragment>
`;

exports[`<ProductGridPlaceholder/> renders default state correctly BananaRepublicFactoryStore 1`] = `
<DocumentFragment>
  @keyframes animation-0 {
  0%, 25% {
    -webkit-background-position: 100% 100%;
    background-position: 100% 100%;
  }

  90%, 100% {
    -webkit-background-position: 0 0;
    background-position: 0 0;
  }
}

@keyframes animation-0 {
  0%, 25% {
    -webkit-background-position: 100% 100%;
    background-position: 100% 100%;
  }

  90%, 100% {
    -webkit-background-position: 0 0;
    background-position: 0 0;
  }
}

@keyframes animation-0 {
  0%, 25% {
    -webkit-background-position: 100% 100%;
    background-position: 100% 100%;
  }

  90%, 100% {
    -webkit-background-position: 0 0;
    background-position: 0 0;
  }
}

@keyframes animation-0 {
  0%, 25% {
    -webkit-background-position: 100% 100%;
    background-position: 100% 100%;
  }

  90%, 100% {
    -webkit-background-position: 0 0;
    background-position: 0 0;
  }
}

.emotion-1 {
  display: grid;
  width: 100%;
  gap: 1em;
}

@media (max-width: 767px) {
  .emotion-1 {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (min-width: 768px) and (max-width: 1279px) {
  .emotion-1 {
    grid-template-columns: repeat(3, 1fr);
  }
}

@media (min-width: 1280px) and (max-width: 1440px) {
  .emotion-1 {
    grid-template-columns: repeat(4, 1fr);
  }
}

@media (min-width: 1441px) {
  .emotion-1 {
    grid-template-columns: repeat(4, 1fr);
  }
}

.emotion-1 .placeholder-grid__container {
  width: 100%;
}

.emotion-1 .placeholder-grid__image {
  max-width: 100%;
  width: 100%;
}

.emotion-2 {
  background: linear-gradient(300.82deg, #D9D9D9 6.57%, #FBFAFA 51.24%, #D9D9D9 93.09%);
  position: relative;
  -webkit-background-size: 300% 100%;
  background-size: 300% 100%;
  -webkit-animation: animation-0 0.9s alternate 0s infinite ease-in-out;
  animation: animation-0 0.9s alternate 0s infinite ease-in-out;
  padding-bottom: 133.750%;
}

.emotion-3 {
  background: linear-gradient(300.82deg, #D9D9D9 6.57%, #FBFAFA 51.24%, #D9D9D9 93.09%);
  position: relative;
  -webkit-background-size: 300% 100%;
  background-size: 300% 100%;
  -webkit-animation: animation-0 0.9s alternate 0s infinite ease-in-out;
  animation: animation-0 0.9s alternate 0s infinite ease-in-out;
  width: 140px;
  height: 18px;
  padding-bottom: 0;
}

.emotion-4 {
  background: linear-gradient(300.82deg, #D9D9D9 6.57%, #FBFAFA 51.24%, #D9D9D9 93.09%);
  position: relative;
  -webkit-background-size: 300% 100%;
  background-size: 300% 100%;
  -webkit-animation: animation-0 0.9s alternate 0s infinite ease-in-out;
  animation: animation-0 0.9s alternate 0s infinite ease-in-out;
  width: 60px;
  height: 18px;
  padding-bottom: 0;
}

.emotion-5 {
  background: linear-gradient(300.82deg, #D9D9D9 6.57%, #FBFAFA 51.24%, #D9D9D9 93.09%);
  position: relative;
  -webkit-background-size: 300% 100%;
  background-size: 300% 100%;
  -webkit-animation: animation-0 0.9s alternate 0s infinite ease-in-out;
  animation: animation-0 0.9s alternate 0s infinite ease-in-out;
  width: 120px;
  height: 18px;
  padding-bottom: 0;
}

<div
    class="emotion-0"
  >
    <div
      class="placeholder-grid emotion-1"
    >
      <div
        class="placeholder-grid__container"
        style="float: left; overflow: hidden;"
      >
        <div
          style="padding-bottom: .5rem;"
        >
          <div
            class="placeholder-grid__image emotion-2"
          />
        </div>
        <div
          style="padding-bottom: .5rem;"
        >
          <div
            class="emotion-3"
          />
        </div>
        <div
          style="padding-bottom: .5rem;"
        >
          <div
            class="emotion-4"
          />
        </div>
        <div>
          <div
            class="emotion-5"
          />
        </div>
      </div>
    </div>
  </div>
</DocumentFragment>
`;

exports[`<ProductGridPlaceholder/> renders default state correctly BananaRepublicFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  @keyframes animation-0 {
  0%, 25% {
    -webkit-background-position: 100% 100%;
    background-position: 100% 100%;
  }

  90%, 100% {
    -webkit-background-position: 0 0;
    background-position: 0 0;
  }
}

@keyframes animation-0 {
  0%, 25% {
    -webkit-background-position: 100% 100%;
    background-position: 100% 100%;
  }

  90%, 100% {
    -webkit-background-position: 0 0;
    background-position: 0 0;
  }
}

@keyframes animation-0 {
  0%, 25% {
    -webkit-background-position: 100% 100%;
    background-position: 100% 100%;
  }

  90%, 100% {
    -webkit-background-position: 0 0;
    background-position: 0 0;
  }
}

@keyframes animation-0 {
  0%, 25% {
    -webkit-background-position: 100% 100%;
    background-position: 100% 100%;
  }

  90%, 100% {
    -webkit-background-position: 0 0;
    background-position: 0 0;
  }
}

.emotion-1 {
  display: grid;
  width: 100%;
  gap: 1em;
}

@media (max-width: 767px) {
  .emotion-1 {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (min-width: 768px) and (max-width: 1279px) {
  .emotion-1 {
    grid-template-columns: repeat(3, 1fr);
  }
}

@media (min-width: 1280px) and (max-width: 1440px) {
  .emotion-1 {
    grid-template-columns: repeat(4, 1fr);
  }
}

@media (min-width: 1441px) {
  .emotion-1 {
    grid-template-columns: repeat(4, 1fr);
  }
}

.emotion-1 .placeholder-grid__container {
  width: 100%;
}

.emotion-1 .placeholder-grid__image {
  max-width: 100%;
  width: 100%;
}

.emotion-2 {
  background: linear-gradient(300.82deg, #D9D9D9 6.57%, #FBFAFA 51.24%, #D9D9D9 93.09%);
  position: relative;
  -webkit-background-size: 300% 100%;
  background-size: 300% 100%;
  -webkit-animation: animation-0 0.9s alternate 0s infinite ease-in-out;
  animation: animation-0 0.9s alternate 0s infinite ease-in-out;
  padding-bottom: 133.750%;
}

.emotion-3 {
  background: linear-gradient(300.82deg, #D9D9D9 6.57%, #FBFAFA 51.24%, #D9D9D9 93.09%);
  position: relative;
  -webkit-background-size: 300% 100%;
  background-size: 300% 100%;
  -webkit-animation: animation-0 0.9s alternate 0s infinite ease-in-out;
  animation: animation-0 0.9s alternate 0s infinite ease-in-out;
  width: 140px;
  height: 18px;
  padding-bottom: 0;
}

.emotion-4 {
  background: linear-gradient(300.82deg, #D9D9D9 6.57%, #FBFAFA 51.24%, #D9D9D9 93.09%);
  position: relative;
  -webkit-background-size: 300% 100%;
  background-size: 300% 100%;
  -webkit-animation: animation-0 0.9s alternate 0s infinite ease-in-out;
  animation: animation-0 0.9s alternate 0s infinite ease-in-out;
  width: 60px;
  height: 18px;
  padding-bottom: 0;
}

.emotion-5 {
  background: linear-gradient(300.82deg, #D9D9D9 6.57%, #FBFAFA 51.24%, #D9D9D9 93.09%);
  position: relative;
  -webkit-background-size: 300% 100%;
  background-size: 300% 100%;
  -webkit-animation: animation-0 0.9s alternate 0s infinite ease-in-out;
  animation: animation-0 0.9s alternate 0s infinite ease-in-out;
  width: 120px;
  height: 18px;
  padding-bottom: 0;
}

<div
    class="emotion-0"
  >
    <div
      class="placeholder-grid emotion-1"
    >
      <div
        class="placeholder-grid__container"
        style="float: left; overflow: hidden;"
      >
        <div
          style="padding-bottom: .5rem;"
        >
          <div
            class="placeholder-grid__image emotion-2"
          />
        </div>
        <div
          style="padding-bottom: .5rem;"
        >
          <div
            class="emotion-3"
          />
        </div>
        <div
          style="padding-bottom: .5rem;"
        >
          <div
            class="emotion-4"
          />
        </div>
        <div>
          <div
            class="emotion-5"
          />
        </div>
      </div>
    </div>
  </div>
</DocumentFragment>
`;

exports[`<ProductGridPlaceholder/> renders default state correctly Gap 1`] = `
<DocumentFragment>
  @keyframes animation-0 {
  0%, 25% {
    -webkit-background-position: 100% 100%;
    background-position: 100% 100%;
  }

  90%, 100% {
    -webkit-background-position: 0 0;
    background-position: 0 0;
  }
}

@keyframes animation-0 {
  0%, 25% {
    -webkit-background-position: 100% 100%;
    background-position: 100% 100%;
  }

  90%, 100% {
    -webkit-background-position: 0 0;
    background-position: 0 0;
  }
}

@keyframes animation-0 {
  0%, 25% {
    -webkit-background-position: 100% 100%;
    background-position: 100% 100%;
  }

  90%, 100% {
    -webkit-background-position: 0 0;
    background-position: 0 0;
  }
}

@keyframes animation-0 {
  0%, 25% {
    -webkit-background-position: 100% 100%;
    background-position: 100% 100%;
  }

  90%, 100% {
    -webkit-background-position: 0 0;
    background-position: 0 0;
  }
}

.emotion-1 {
  display: grid;
  width: 100%;
  gap: 1em;
}

@media (max-width: 767px) {
  .emotion-1 {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (min-width: 768px) and (max-width: 1279px) {
  .emotion-1 {
    grid-template-columns: repeat(3, 1fr);
  }
}

@media (min-width: 1280px) and (max-width: 1440px) {
  .emotion-1 {
    grid-template-columns: repeat(4, 1fr);
  }
}

@media (min-width: 1441px) {
  .emotion-1 {
    grid-template-columns: repeat(4, 1fr);
  }
}

.emotion-1 .placeholder-grid__container {
  width: 100%;
}

.emotion-1 .placeholder-grid__image {
  max-width: 100%;
  width: 100%;
}

.emotion-2 {
  background: linear-gradient(300.82deg, #D9D9D9 6.57%, #FBFAFA 51.24%, #D9D9D9 93.09%);
  position: relative;
  -webkit-background-size: 300% 100%;
  background-size: 300% 100%;
  -webkit-animation: animation-0 0.9s alternate 0s infinite ease-in-out;
  animation: animation-0 0.9s alternate 0s infinite ease-in-out;
  padding-bottom: 133.750%;
}

.emotion-3 {
  background: linear-gradient(300.82deg, #D9D9D9 6.57%, #FBFAFA 51.24%, #D9D9D9 93.09%);
  position: relative;
  -webkit-background-size: 300% 100%;
  background-size: 300% 100%;
  -webkit-animation: animation-0 0.9s alternate 0s infinite ease-in-out;
  animation: animation-0 0.9s alternate 0s infinite ease-in-out;
  width: 140px;
  height: 18px;
  padding-bottom: 0;
}

.emotion-4 {
  background: linear-gradient(300.82deg, #D9D9D9 6.57%, #FBFAFA 51.24%, #D9D9D9 93.09%);
  position: relative;
  -webkit-background-size: 300% 100%;
  background-size: 300% 100%;
  -webkit-animation: animation-0 0.9s alternate 0s infinite ease-in-out;
  animation: animation-0 0.9s alternate 0s infinite ease-in-out;
  width: 60px;
  height: 18px;
  padding-bottom: 0;
}

.emotion-5 {
  background: linear-gradient(300.82deg, #D9D9D9 6.57%, #FBFAFA 51.24%, #D9D9D9 93.09%);
  position: relative;
  -webkit-background-size: 300% 100%;
  background-size: 300% 100%;
  -webkit-animation: animation-0 0.9s alternate 0s infinite ease-in-out;
  animation: animation-0 0.9s alternate 0s infinite ease-in-out;
  width: 120px;
  height: 18px;
  padding-bottom: 0;
}

<div
    class="emotion-0"
  >
    <div
      class="placeholder-grid emotion-1"
    >
      <div
        class="placeholder-grid__container"
        style="float: left; overflow: hidden;"
      >
        <div
          style="padding-bottom: .5rem;"
        >
          <div
            class="placeholder-grid__image emotion-2"
          />
        </div>
        <div
          style="padding-bottom: .5rem;"
        >
          <div
            class="emotion-3"
          />
        </div>
        <div
          style="padding-bottom: .5rem;"
        >
          <div
            class="emotion-4"
          />
        </div>
        <div>
          <div
            class="emotion-5"
          />
        </div>
      </div>
    </div>
  </div>
</DocumentFragment>
`;

exports[`<ProductGridPlaceholder/> renders default state correctly Gap in crossBrand 1`] = `
<DocumentFragment>
  @keyframes animation-0 {
  0%, 25% {
    -webkit-background-position: 100% 100%;
    background-position: 100% 100%;
  }

  90%, 100% {
    -webkit-background-position: 0 0;
    background-position: 0 0;
  }
}

@keyframes animation-0 {
  0%, 25% {
    -webkit-background-position: 100% 100%;
    background-position: 100% 100%;
  }

  90%, 100% {
    -webkit-background-position: 0 0;
    background-position: 0 0;
  }
}

@keyframes animation-0 {
  0%, 25% {
    -webkit-background-position: 100% 100%;
    background-position: 100% 100%;
  }

  90%, 100% {
    -webkit-background-position: 0 0;
    background-position: 0 0;
  }
}

@keyframes animation-0 {
  0%, 25% {
    -webkit-background-position: 100% 100%;
    background-position: 100% 100%;
  }

  90%, 100% {
    -webkit-background-position: 0 0;
    background-position: 0 0;
  }
}

.emotion-1 {
  display: grid;
  width: 100%;
  gap: 1em;
}

@media (max-width: 767px) {
  .emotion-1 {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (min-width: 768px) and (max-width: 1279px) {
  .emotion-1 {
    grid-template-columns: repeat(3, 1fr);
  }
}

@media (min-width: 1280px) and (max-width: 1440px) {
  .emotion-1 {
    grid-template-columns: repeat(4, 1fr);
  }
}

@media (min-width: 1441px) {
  .emotion-1 {
    grid-template-columns: repeat(4, 1fr);
  }
}

.emotion-1 .placeholder-grid__container {
  width: 100%;
}

.emotion-1 .placeholder-grid__image {
  max-width: 100%;
  width: 100%;
}

.emotion-2 {
  background: linear-gradient(300.82deg, #D9D9D9 6.57%, #FBFAFA 51.24%, #D9D9D9 93.09%);
  position: relative;
  -webkit-background-size: 300% 100%;
  background-size: 300% 100%;
  -webkit-animation: animation-0 0.9s alternate 0s infinite ease-in-out;
  animation: animation-0 0.9s alternate 0s infinite ease-in-out;
  padding-bottom: 133.750%;
}

.emotion-3 {
  background: linear-gradient(300.82deg, #D9D9D9 6.57%, #FBFAFA 51.24%, #D9D9D9 93.09%);
  position: relative;
  -webkit-background-size: 300% 100%;
  background-size: 300% 100%;
  -webkit-animation: animation-0 0.9s alternate 0s infinite ease-in-out;
  animation: animation-0 0.9s alternate 0s infinite ease-in-out;
  width: 140px;
  height: 18px;
  padding-bottom: 0;
}

.emotion-4 {
  background: linear-gradient(300.82deg, #D9D9D9 6.57%, #FBFAFA 51.24%, #D9D9D9 93.09%);
  position: relative;
  -webkit-background-size: 300% 100%;
  background-size: 300% 100%;
  -webkit-animation: animation-0 0.9s alternate 0s infinite ease-in-out;
  animation: animation-0 0.9s alternate 0s infinite ease-in-out;
  width: 60px;
  height: 18px;
  padding-bottom: 0;
}

.emotion-5 {
  background: linear-gradient(300.82deg, #D9D9D9 6.57%, #FBFAFA 51.24%, #D9D9D9 93.09%);
  position: relative;
  -webkit-background-size: 300% 100%;
  background-size: 300% 100%;
  -webkit-animation: animation-0 0.9s alternate 0s infinite ease-in-out;
  animation: animation-0 0.9s alternate 0s infinite ease-in-out;
  width: 120px;
  height: 18px;
  padding-bottom: 0;
}

<div
    class="emotion-0"
  >
    <div
      class="placeholder-grid emotion-1"
    >
      <div
        class="placeholder-grid__container"
        style="float: left; overflow: hidden;"
      >
        <div
          style="padding-bottom: .5rem;"
        >
          <div
            class="placeholder-grid__image emotion-2"
          />
        </div>
        <div
          style="padding-bottom: .5rem;"
        >
          <div
            class="emotion-3"
          />
        </div>
        <div
          style="padding-bottom: .5rem;"
        >
          <div
            class="emotion-4"
          />
        </div>
        <div>
          <div
            class="emotion-5"
          />
        </div>
      </div>
    </div>
  </div>
</DocumentFragment>
`;

exports[`<ProductGridPlaceholder/> renders default state correctly GapFactoryStore 1`] = `
<DocumentFragment>
  @keyframes animation-0 {
  0%, 25% {
    -webkit-background-position: 100% 100%;
    background-position: 100% 100%;
  }

  90%, 100% {
    -webkit-background-position: 0 0;
    background-position: 0 0;
  }
}

@keyframes animation-0 {
  0%, 25% {
    -webkit-background-position: 100% 100%;
    background-position: 100% 100%;
  }

  90%, 100% {
    -webkit-background-position: 0 0;
    background-position: 0 0;
  }
}

@keyframes animation-0 {
  0%, 25% {
    -webkit-background-position: 100% 100%;
    background-position: 100% 100%;
  }

  90%, 100% {
    -webkit-background-position: 0 0;
    background-position: 0 0;
  }
}

@keyframes animation-0 {
  0%, 25% {
    -webkit-background-position: 100% 100%;
    background-position: 100% 100%;
  }

  90%, 100% {
    -webkit-background-position: 0 0;
    background-position: 0 0;
  }
}

.emotion-1 {
  display: grid;
  width: 100%;
  gap: 1em;
}

@media (max-width: 767px) {
  .emotion-1 {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (min-width: 768px) and (max-width: 1279px) {
  .emotion-1 {
    grid-template-columns: repeat(3, 1fr);
  }
}

@media (min-width: 1280px) and (max-width: 1440px) {
  .emotion-1 {
    grid-template-columns: repeat(4, 1fr);
  }
}

@media (min-width: 1441px) {
  .emotion-1 {
    grid-template-columns: repeat(4, 1fr);
  }
}

.emotion-1 .placeholder-grid__container {
  width: 100%;
}

.emotion-1 .placeholder-grid__image {
  max-width: 100%;
  width: 100%;
}

.emotion-2 {
  background: linear-gradient(300.82deg, #D9D9D9 6.57%, #FBFAFA 51.24%, #D9D9D9 93.09%);
  position: relative;
  -webkit-background-size: 300% 100%;
  background-size: 300% 100%;
  -webkit-animation: animation-0 0.9s alternate 0s infinite ease-in-out;
  animation: animation-0 0.9s alternate 0s infinite ease-in-out;
  padding-bottom: 133.750%;
}

.emotion-3 {
  background: linear-gradient(300.82deg, #D9D9D9 6.57%, #FBFAFA 51.24%, #D9D9D9 93.09%);
  position: relative;
  -webkit-background-size: 300% 100%;
  background-size: 300% 100%;
  -webkit-animation: animation-0 0.9s alternate 0s infinite ease-in-out;
  animation: animation-0 0.9s alternate 0s infinite ease-in-out;
  width: 140px;
  height: 18px;
  padding-bottom: 0;
}

.emotion-4 {
  background: linear-gradient(300.82deg, #D9D9D9 6.57%, #FBFAFA 51.24%, #D9D9D9 93.09%);
  position: relative;
  -webkit-background-size: 300% 100%;
  background-size: 300% 100%;
  -webkit-animation: animation-0 0.9s alternate 0s infinite ease-in-out;
  animation: animation-0 0.9s alternate 0s infinite ease-in-out;
  width: 60px;
  height: 18px;
  padding-bottom: 0;
}

.emotion-5 {
  background: linear-gradient(300.82deg, #D9D9D9 6.57%, #FBFAFA 51.24%, #D9D9D9 93.09%);
  position: relative;
  -webkit-background-size: 300% 100%;
  background-size: 300% 100%;
  -webkit-animation: animation-0 0.9s alternate 0s infinite ease-in-out;
  animation: animation-0 0.9s alternate 0s infinite ease-in-out;
  width: 120px;
  height: 18px;
  padding-bottom: 0;
}

<div
    class="emotion-0"
  >
    <div
      class="placeholder-grid emotion-1"
    >
      <div
        class="placeholder-grid__container"
        style="float: left; overflow: hidden;"
      >
        <div
          style="padding-bottom: .5rem;"
        >
          <div
            class="placeholder-grid__image emotion-2"
          />
        </div>
        <div
          style="padding-bottom: .5rem;"
        >
          <div
            class="emotion-3"
          />
        </div>
        <div
          style="padding-bottom: .5rem;"
        >
          <div
            class="emotion-4"
          />
        </div>
        <div>
          <div
            class="emotion-5"
          />
        </div>
      </div>
    </div>
  </div>
</DocumentFragment>
`;

exports[`<ProductGridPlaceholder/> renders default state correctly GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  @keyframes animation-0 {
  0%, 25% {
    -webkit-background-position: 100% 100%;
    background-position: 100% 100%;
  }

  90%, 100% {
    -webkit-background-position: 0 0;
    background-position: 0 0;
  }
}

@keyframes animation-0 {
  0%, 25% {
    -webkit-background-position: 100% 100%;
    background-position: 100% 100%;
  }

  90%, 100% {
    -webkit-background-position: 0 0;
    background-position: 0 0;
  }
}

@keyframes animation-0 {
  0%, 25% {
    -webkit-background-position: 100% 100%;
    background-position: 100% 100%;
  }

  90%, 100% {
    -webkit-background-position: 0 0;
    background-position: 0 0;
  }
}

@keyframes animation-0 {
  0%, 25% {
    -webkit-background-position: 100% 100%;
    background-position: 100% 100%;
  }

  90%, 100% {
    -webkit-background-position: 0 0;
    background-position: 0 0;
  }
}

.emotion-1 {
  display: grid;
  width: 100%;
  gap: 1em;
}

@media (max-width: 767px) {
  .emotion-1 {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (min-width: 768px) and (max-width: 1279px) {
  .emotion-1 {
    grid-template-columns: repeat(3, 1fr);
  }
}

@media (min-width: 1280px) and (max-width: 1440px) {
  .emotion-1 {
    grid-template-columns: repeat(4, 1fr);
  }
}

@media (min-width: 1441px) {
  .emotion-1 {
    grid-template-columns: repeat(4, 1fr);
  }
}

.emotion-1 .placeholder-grid__container {
  width: 100%;
}

.emotion-1 .placeholder-grid__image {
  max-width: 100%;
  width: 100%;
}

.emotion-2 {
  background: linear-gradient(300.82deg, #D9D9D9 6.57%, #FBFAFA 51.24%, #D9D9D9 93.09%);
  position: relative;
  -webkit-background-size: 300% 100%;
  background-size: 300% 100%;
  -webkit-animation: animation-0 0.9s alternate 0s infinite ease-in-out;
  animation: animation-0 0.9s alternate 0s infinite ease-in-out;
  padding-bottom: 133.750%;
}

.emotion-3 {
  background: linear-gradient(300.82deg, #D9D9D9 6.57%, #FBFAFA 51.24%, #D9D9D9 93.09%);
  position: relative;
  -webkit-background-size: 300% 100%;
  background-size: 300% 100%;
  -webkit-animation: animation-0 0.9s alternate 0s infinite ease-in-out;
  animation: animation-0 0.9s alternate 0s infinite ease-in-out;
  width: 140px;
  height: 18px;
  padding-bottom: 0;
}

.emotion-4 {
  background: linear-gradient(300.82deg, #D9D9D9 6.57%, #FBFAFA 51.24%, #D9D9D9 93.09%);
  position: relative;
  -webkit-background-size: 300% 100%;
  background-size: 300% 100%;
  -webkit-animation: animation-0 0.9s alternate 0s infinite ease-in-out;
  animation: animation-0 0.9s alternate 0s infinite ease-in-out;
  width: 60px;
  height: 18px;
  padding-bottom: 0;
}

.emotion-5 {
  background: linear-gradient(300.82deg, #D9D9D9 6.57%, #FBFAFA 51.24%, #D9D9D9 93.09%);
  position: relative;
  -webkit-background-size: 300% 100%;
  background-size: 300% 100%;
  -webkit-animation: animation-0 0.9s alternate 0s infinite ease-in-out;
  animation: animation-0 0.9s alternate 0s infinite ease-in-out;
  width: 120px;
  height: 18px;
  padding-bottom: 0;
}

<div
    class="emotion-0"
  >
    <div
      class="placeholder-grid emotion-1"
    >
      <div
        class="placeholder-grid__container"
        style="float: left; overflow: hidden;"
      >
        <div
          style="padding-bottom: .5rem;"
        >
          <div
            class="placeholder-grid__image emotion-2"
          />
        </div>
        <div
          style="padding-bottom: .5rem;"
        >
          <div
            class="emotion-3"
          />
        </div>
        <div
          style="padding-bottom: .5rem;"
        >
          <div
            class="emotion-4"
          />
        </div>
        <div>
          <div
            class="emotion-5"
          />
        </div>
      </div>
    </div>
  </div>
</DocumentFragment>
`;

exports[`<ProductGridPlaceholder/> renders default state correctly OldNavy 1`] = `
<DocumentFragment>
  @keyframes animation-0 {
  0%, 25% {
    -webkit-background-position: 100% 100%;
    background-position: 100% 100%;
  }

  90%, 100% {
    -webkit-background-position: 0 0;
    background-position: 0 0;
  }
}

@keyframes animation-0 {
  0%, 25% {
    -webkit-background-position: 100% 100%;
    background-position: 100% 100%;
  }

  90%, 100% {
    -webkit-background-position: 0 0;
    background-position: 0 0;
  }
}

@keyframes animation-0 {
  0%, 25% {
    -webkit-background-position: 100% 100%;
    background-position: 100% 100%;
  }

  90%, 100% {
    -webkit-background-position: 0 0;
    background-position: 0 0;
  }
}

@keyframes animation-0 {
  0%, 25% {
    -webkit-background-position: 100% 100%;
    background-position: 100% 100%;
  }

  90%, 100% {
    -webkit-background-position: 0 0;
    background-position: 0 0;
  }
}

.emotion-1 {
  display: grid;
  width: 100%;
  gap: 1em;
}

@media (max-width: 767px) {
  .emotion-1 {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (min-width: 768px) and (max-width: 1279px) {
  .emotion-1 {
    grid-template-columns: repeat(3, 1fr);
  }
}

@media (min-width: 1280px) and (max-width: 1440px) {
  .emotion-1 {
    grid-template-columns: repeat(4, 1fr);
  }
}

@media (min-width: 1441px) {
  .emotion-1 {
    grid-template-columns: repeat(4, 1fr);
  }
}

.emotion-1 .placeholder-grid__container {
  width: 100%;
}

.emotion-1 .placeholder-grid__image {
  max-width: 100%;
  width: 100%;
}

.emotion-2 {
  background: linear-gradient(300.82deg, #D9D9D9 6.57%, #FBFAFA 51.24%, #D9D9D9 93.09%);
  position: relative;
  -webkit-background-size: 300% 100%;
  background-size: 300% 100%;
  -webkit-animation: animation-0 0.9s alternate 0s infinite ease-in-out;
  animation: animation-0 0.9s alternate 0s infinite ease-in-out;
  padding-bottom: 133.750%;
}

.emotion-3 {
  background: linear-gradient(300.82deg, #D9D9D9 6.57%, #FBFAFA 51.24%, #D9D9D9 93.09%);
  position: relative;
  -webkit-background-size: 300% 100%;
  background-size: 300% 100%;
  -webkit-animation: animation-0 0.9s alternate 0s infinite ease-in-out;
  animation: animation-0 0.9s alternate 0s infinite ease-in-out;
  width: 140px;
  height: 18px;
  padding-bottom: 0;
}

.emotion-4 {
  background: linear-gradient(300.82deg, #D9D9D9 6.57%, #FBFAFA 51.24%, #D9D9D9 93.09%);
  position: relative;
  -webkit-background-size: 300% 100%;
  background-size: 300% 100%;
  -webkit-animation: animation-0 0.9s alternate 0s infinite ease-in-out;
  animation: animation-0 0.9s alternate 0s infinite ease-in-out;
  width: 60px;
  height: 18px;
  padding-bottom: 0;
}

.emotion-5 {
  background: linear-gradient(300.82deg, #D9D9D9 6.57%, #FBFAFA 51.24%, #D9D9D9 93.09%);
  position: relative;
  -webkit-background-size: 300% 100%;
  background-size: 300% 100%;
  -webkit-animation: animation-0 0.9s alternate 0s infinite ease-in-out;
  animation: animation-0 0.9s alternate 0s infinite ease-in-out;
  width: 120px;
  height: 18px;
  padding-bottom: 0;
}

<div
    class="emotion-0"
  >
    <div
      class="placeholder-grid emotion-1"
    >
      <div
        class="placeholder-grid__container"
        style="float: left; overflow: hidden;"
      >
        <div
          style="padding-bottom: .5rem;"
        >
          <div
            class="placeholder-grid__image emotion-2"
          />
        </div>
        <div
          style="padding-bottom: .5rem;"
        >
          <div
            class="emotion-3"
          />
        </div>
        <div
          style="padding-bottom: .5rem;"
        >
          <div
            class="emotion-4"
          />
        </div>
        <div>
          <div
            class="emotion-5"
          />
        </div>
      </div>
    </div>
  </div>
</DocumentFragment>
`;

exports[`<ProductGridPlaceholder/> renders default state correctly OldNavy in crossBrand 1`] = `
<DocumentFragment>
  @keyframes animation-0 {
  0%, 25% {
    -webkit-background-position: 100% 100%;
    background-position: 100% 100%;
  }

  90%, 100% {
    -webkit-background-position: 0 0;
    background-position: 0 0;
  }
}

@keyframes animation-0 {
  0%, 25% {
    -webkit-background-position: 100% 100%;
    background-position: 100% 100%;
  }

  90%, 100% {
    -webkit-background-position: 0 0;
    background-position: 0 0;
  }
}

@keyframes animation-0 {
  0%, 25% {
    -webkit-background-position: 100% 100%;
    background-position: 100% 100%;
  }

  90%, 100% {
    -webkit-background-position: 0 0;
    background-position: 0 0;
  }
}

@keyframes animation-0 {
  0%, 25% {
    -webkit-background-position: 100% 100%;
    background-position: 100% 100%;
  }

  90%, 100% {
    -webkit-background-position: 0 0;
    background-position: 0 0;
  }
}

.emotion-1 {
  display: grid;
  width: 100%;
  gap: 1em;
}

@media (max-width: 767px) {
  .emotion-1 {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (min-width: 768px) and (max-width: 1279px) {
  .emotion-1 {
    grid-template-columns: repeat(3, 1fr);
  }
}

@media (min-width: 1280px) and (max-width: 1440px) {
  .emotion-1 {
    grid-template-columns: repeat(4, 1fr);
  }
}

@media (min-width: 1441px) {
  .emotion-1 {
    grid-template-columns: repeat(4, 1fr);
  }
}

.emotion-1 .placeholder-grid__container {
  width: 100%;
}

.emotion-1 .placeholder-grid__image {
  max-width: 100%;
  width: 100%;
}

.emotion-2 {
  background: linear-gradient(300.82deg, #D9D9D9 6.57%, #FBFAFA 51.24%, #D9D9D9 93.09%);
  position: relative;
  -webkit-background-size: 300% 100%;
  background-size: 300% 100%;
  -webkit-animation: animation-0 0.9s alternate 0s infinite ease-in-out;
  animation: animation-0 0.9s alternate 0s infinite ease-in-out;
  padding-bottom: 133.750%;
}

.emotion-3 {
  background: linear-gradient(300.82deg, #D9D9D9 6.57%, #FBFAFA 51.24%, #D9D9D9 93.09%);
  position: relative;
  -webkit-background-size: 300% 100%;
  background-size: 300% 100%;
  -webkit-animation: animation-0 0.9s alternate 0s infinite ease-in-out;
  animation: animation-0 0.9s alternate 0s infinite ease-in-out;
  width: 140px;
  height: 18px;
  padding-bottom: 0;
}

.emotion-4 {
  background: linear-gradient(300.82deg, #D9D9D9 6.57%, #FBFAFA 51.24%, #D9D9D9 93.09%);
  position: relative;
  -webkit-background-size: 300% 100%;
  background-size: 300% 100%;
  -webkit-animation: animation-0 0.9s alternate 0s infinite ease-in-out;
  animation: animation-0 0.9s alternate 0s infinite ease-in-out;
  width: 60px;
  height: 18px;
  padding-bottom: 0;
}

.emotion-5 {
  background: linear-gradient(300.82deg, #D9D9D9 6.57%, #FBFAFA 51.24%, #D9D9D9 93.09%);
  position: relative;
  -webkit-background-size: 300% 100%;
  background-size: 300% 100%;
  -webkit-animation: animation-0 0.9s alternate 0s infinite ease-in-out;
  animation: animation-0 0.9s alternate 0s infinite ease-in-out;
  width: 120px;
  height: 18px;
  padding-bottom: 0;
}

<div
    class="emotion-0"
  >
    <div
      class="placeholder-grid emotion-1"
    >
      <div
        class="placeholder-grid__container"
        style="float: left; overflow: hidden;"
      >
        <div
          style="padding-bottom: .5rem;"
        >
          <div
            class="placeholder-grid__image emotion-2"
          />
        </div>
        <div
          style="padding-bottom: .5rem;"
        >
          <div
            class="emotion-3"
          />
        </div>
        <div
          style="padding-bottom: .5rem;"
        >
          <div
            class="emotion-4"
          />
        </div>
        <div>
          <div
            class="emotion-5"
          />
        </div>
      </div>
    </div>
  </div>
</DocumentFragment>
`;

exports[`<ProductGridPlaceholder/> renders placeholders in desktop view with required size  1`] = `
@keyframes animation-0 {
  0%, 25% {
    -webkit-background-position: 100% 100%;
    background-position: 100% 100%;
  }

  90%, 100% {
    -webkit-background-position: 0 0;
    background-position: 0 0;
  }
}

@keyframes animation-0 {
  0%, 25% {
    -webkit-background-position: 100% 100%;
    background-position: 100% 100%;
  }

  90%, 100% {
    -webkit-background-position: 0 0;
    background-position: 0 0;
  }
}

@keyframes animation-0 {
  0%, 25% {
    -webkit-background-position: 100% 100%;
    background-position: 100% 100%;
  }

  90%, 100% {
    -webkit-background-position: 0 0;
    background-position: 0 0;
  }
}

@keyframes animation-0 {
  0%, 25% {
    -webkit-background-position: 100% 100%;
    background-position: 100% 100%;
  }

  90%, 100% {
    -webkit-background-position: 0 0;
    background-position: 0 0;
  }
}

@keyframes animation-0 {
  0%, 25% {
    -webkit-background-position: 100% 100%;
    background-position: 100% 100%;
  }

  90%, 100% {
    -webkit-background-position: 0 0;
    background-position: 0 0;
  }
}

@keyframes animation-0 {
  0%, 25% {
    -webkit-background-position: 100% 100%;
    background-position: 100% 100%;
  }

  90%, 100% {
    -webkit-background-position: 0 0;
    background-position: 0 0;
  }
}

.emotion-0 {
  background: linear-gradient(300.82deg, #D9D9D9 6.57%, #FBFAFA 51.24%, #D9D9D9 93.09%);
  position: relative;
  -webkit-background-size: 300% 100%;
  background-size: 300% 100%;
  -webkit-animation: animation-0 0.9s alternate 0s infinite ease-in-out;
  animation: animation-0 0.9s alternate 0s infinite ease-in-out;
  padding-bottom: 133.750%;
}

.emotion-1 {
  background: linear-gradient(300.82deg, #D9D9D9 6.57%, #FBFAFA 51.24%, #D9D9D9 93.09%);
  position: relative;
  -webkit-background-size: 300% 100%;
  background-size: 300% 100%;
  -webkit-animation: animation-0 0.9s alternate 0s infinite ease-in-out;
  animation: animation-0 0.9s alternate 0s infinite ease-in-out;
  width: 140px;
  height: 18px;
  padding-bottom: 0;
}

.emotion-2 {
  background: linear-gradient(300.82deg, #D9D9D9 6.57%, #FBFAFA 51.24%, #D9D9D9 93.09%);
  position: relative;
  -webkit-background-size: 300% 100%;
  background-size: 300% 100%;
  -webkit-animation: animation-0 0.9s alternate 0s infinite ease-in-out;
  animation: animation-0 0.9s alternate 0s infinite ease-in-out;
  width: 60px;
  height: 18px;
  padding-bottom: 0;
}

.emotion-3 {
  background: linear-gradient(300.82deg, #D9D9D9 6.57%, #FBFAFA 51.24%, #D9D9D9 93.09%);
  position: relative;
  -webkit-background-size: 300% 100%;
  background-size: 300% 100%;
  -webkit-animation: animation-0 0.9s alternate 0s infinite ease-in-out;
  animation: animation-0 0.9s alternate 0s infinite ease-in-out;
  width: 120px;
  height: 18px;
  padding-bottom: 0;
}

<div>
  <div
    class="placeholder-grid__container"
    style="float: left; overflow: hidden;"
  >
    <div
      style="padding-bottom: .5rem;"
    >
      <div
        class="placeholder-grid__image emotion-0"
      />
    </div>
    <div
      style="padding-bottom: .5rem;"
    >
      <div
        class="emotion-1"
      />
    </div>
    <div
      style="padding-bottom: .5rem;"
    >
      <div
        class="emotion-2"
      />
    </div>
    <div>
      <div
        class="emotion-3"
      />
    </div>
  </div>
</div>
`;

exports[`<ProductGridPlaceholder/> renders placeholders in desktop view with required size  2`] = `
@keyframes animation-0 {
  0%, 25% {
    -webkit-background-position: 100% 100%;
    background-position: 100% 100%;
  }

  90%, 100% {
    -webkit-background-position: 0 0;
    background-position: 0 0;
  }
}

@keyframes animation-0 {
  0%, 25% {
    -webkit-background-position: 100% 100%;
    background-position: 100% 100%;
  }

  90%, 100% {
    -webkit-background-position: 0 0;
    background-position: 0 0;
  }
}

@keyframes animation-0 {
  0%, 25% {
    -webkit-background-position: 100% 100%;
    background-position: 100% 100%;
  }

  90%, 100% {
    -webkit-background-position: 0 0;
    background-position: 0 0;
  }
}

@keyframes animation-0 {
  0%, 25% {
    -webkit-background-position: 100% 100%;
    background-position: 100% 100%;
  }

  90%, 100% {
    -webkit-background-position: 0 0;
    background-position: 0 0;
  }
}

@keyframes animation-0 {
  0%, 25% {
    -webkit-background-position: 100% 100%;
    background-position: 100% 100%;
  }

  90%, 100% {
    -webkit-background-position: 0 0;
    background-position: 0 0;
  }
}

@keyframes animation-0 {
  0%, 25% {
    -webkit-background-position: 100% 100%;
    background-position: 100% 100%;
  }

  90%, 100% {
    -webkit-background-position: 0 0;
    background-position: 0 0;
  }
}

.emotion-0 {
  background: linear-gradient(300.82deg, #D9D9D9 6.57%, #FBFAFA 51.24%, #D9D9D9 93.09%);
  position: relative;
  -webkit-background-size: 300% 100%;
  background-size: 300% 100%;
  -webkit-animation: animation-0 0.9s alternate 0s infinite ease-in-out;
  animation: animation-0 0.9s alternate 0s infinite ease-in-out;
  padding-bottom: 133.750%;
}

.emotion-1 {
  background: linear-gradient(300.82deg, #D9D9D9 6.57%, #FBFAFA 51.24%, #D9D9D9 93.09%);
  position: relative;
  -webkit-background-size: 300% 100%;
  background-size: 300% 100%;
  -webkit-animation: animation-0 0.9s alternate 0s infinite ease-in-out;
  animation: animation-0 0.9s alternate 0s infinite ease-in-out;
  width: 140px;
  height: 18px;
  padding-bottom: 0;
}

.emotion-2 {
  background: linear-gradient(300.82deg, #D9D9D9 6.57%, #FBFAFA 51.24%, #D9D9D9 93.09%);
  position: relative;
  -webkit-background-size: 300% 100%;
  background-size: 300% 100%;
  -webkit-animation: animation-0 0.9s alternate 0s infinite ease-in-out;
  animation: animation-0 0.9s alternate 0s infinite ease-in-out;
  width: 60px;
  height: 18px;
  padding-bottom: 0;
}

.emotion-3 {
  background: linear-gradient(300.82deg, #D9D9D9 6.57%, #FBFAFA 51.24%, #D9D9D9 93.09%);
  position: relative;
  -webkit-background-size: 300% 100%;
  background-size: 300% 100%;
  -webkit-animation: animation-0 0.9s alternate 0s infinite ease-in-out;
  animation: animation-0 0.9s alternate 0s infinite ease-in-out;
  width: 120px;
  height: 18px;
  padding-bottom: 0;
}

<div>
  <div
    class="placeholder-grid__container"
    style="float: left; overflow: hidden;"
  >
    <div
      style="padding-bottom: .5rem;"
    >
      <div
        class="placeholder-grid__image emotion-0"
      />
    </div>
    <div
      style="padding-bottom: .5rem;"
    >
      <div
        class="emotion-1"
      />
    </div>
    <div
      style="padding-bottom: .5rem;"
    >
      <div
        class="emotion-2"
      />
    </div>
    <div>
      <div
        class="emotion-3"
      />
    </div>
  </div>
</div>
`;

exports[`<ProductGridPlaceholder/> renders placeholders in mobile view with required size  1`] = `
@keyframes animation-0 {
  0%, 25% {
    -webkit-background-position: 100% 100%;
    background-position: 100% 100%;
  }

  90%, 100% {
    -webkit-background-position: 0 0;
    background-position: 0 0;
  }
}

@keyframes animation-0 {
  0%, 25% {
    -webkit-background-position: 100% 100%;
    background-position: 100% 100%;
  }

  90%, 100% {
    -webkit-background-position: 0 0;
    background-position: 0 0;
  }
}

@keyframes animation-0 {
  0%, 25% {
    -webkit-background-position: 100% 100%;
    background-position: 100% 100%;
  }

  90%, 100% {
    -webkit-background-position: 0 0;
    background-position: 0 0;
  }
}

@keyframes animation-0 {
  0%, 25% {
    -webkit-background-position: 100% 100%;
    background-position: 100% 100%;
  }

  90%, 100% {
    -webkit-background-position: 0 0;
    background-position: 0 0;
  }
}

@keyframes animation-0 {
  0%, 25% {
    -webkit-background-position: 100% 100%;
    background-position: 100% 100%;
  }

  90%, 100% {
    -webkit-background-position: 0 0;
    background-position: 0 0;
  }
}

@keyframes animation-0 {
  0%, 25% {
    -webkit-background-position: 100% 100%;
    background-position: 100% 100%;
  }

  90%, 100% {
    -webkit-background-position: 0 0;
    background-position: 0 0;
  }
}

.emotion-0 {
  background: linear-gradient(300.82deg, #D9D9D9 6.57%, #FBFAFA 51.24%, #D9D9D9 93.09%);
  position: relative;
  -webkit-background-size: 300% 100%;
  background-size: 300% 100%;
  -webkit-animation: animation-0 0.9s alternate 0s infinite ease-in-out;
  animation: animation-0 0.9s alternate 0s infinite ease-in-out;
  padding-bottom: 133.750%;
}

.emotion-1 {
  background: linear-gradient(300.82deg, #D9D9D9 6.57%, #FBFAFA 51.24%, #D9D9D9 93.09%);
  position: relative;
  -webkit-background-size: 300% 100%;
  background-size: 300% 100%;
  -webkit-animation: animation-0 0.9s alternate 0s infinite ease-in-out;
  animation: animation-0 0.9s alternate 0s infinite ease-in-out;
  width: 140px;
  height: 18px;
  padding-bottom: 0;
}

.emotion-2 {
  background: linear-gradient(300.82deg, #D9D9D9 6.57%, #FBFAFA 51.24%, #D9D9D9 93.09%);
  position: relative;
  -webkit-background-size: 300% 100%;
  background-size: 300% 100%;
  -webkit-animation: animation-0 0.9s alternate 0s infinite ease-in-out;
  animation: animation-0 0.9s alternate 0s infinite ease-in-out;
  width: 60px;
  height: 18px;
  padding-bottom: 0;
}

.emotion-3 {
  background: linear-gradient(300.82deg, #D9D9D9 6.57%, #FBFAFA 51.24%, #D9D9D9 93.09%);
  position: relative;
  -webkit-background-size: 300% 100%;
  background-size: 300% 100%;
  -webkit-animation: animation-0 0.9s alternate 0s infinite ease-in-out;
  animation: animation-0 0.9s alternate 0s infinite ease-in-out;
  width: 120px;
  height: 18px;
  padding-bottom: 0;
}

<div>
  <div
    class="placeholder-grid__container"
    style="float: left; overflow: hidden;"
  >
    <div
      style="padding-bottom: .5rem;"
    >
      <div
        class="placeholder-grid__image emotion-0"
      />
    </div>
    <div
      style="padding-bottom: .5rem;"
    >
      <div
        class="emotion-1"
      />
    </div>
    <div
      style="padding-bottom: .5rem;"
    >
      <div
        class="emotion-2"
      />
    </div>
    <div>
      <div
        class="emotion-3"
      />
    </div>
  </div>
</div>
`;

exports[`<ProductGridPlaceholder/> renders placeholders in tablet view with required size  1`] = `
@keyframes animation-0 {
  0%, 25% {
    -webkit-background-position: 100% 100%;
    background-position: 100% 100%;
  }

  90%, 100% {
    -webkit-background-position: 0 0;
    background-position: 0 0;
  }
}

@keyframes animation-0 {
  0%, 25% {
    -webkit-background-position: 100% 100%;
    background-position: 100% 100%;
  }

  90%, 100% {
    -webkit-background-position: 0 0;
    background-position: 0 0;
  }
}

@keyframes animation-0 {
  0%, 25% {
    -webkit-background-position: 100% 100%;
    background-position: 100% 100%;
  }

  90%, 100% {
    -webkit-background-position: 0 0;
    background-position: 0 0;
  }
}

@keyframes animation-0 {
  0%, 25% {
    -webkit-background-position: 100% 100%;
    background-position: 100% 100%;
  }

  90%, 100% {
    -webkit-background-position: 0 0;
    background-position: 0 0;
  }
}

@keyframes animation-0 {
  0%, 25% {
    -webkit-background-position: 100% 100%;
    background-position: 100% 100%;
  }

  90%, 100% {
    -webkit-background-position: 0 0;
    background-position: 0 0;
  }
}

@keyframes animation-0 {
  0%, 25% {
    -webkit-background-position: 100% 100%;
    background-position: 100% 100%;
  }

  90%, 100% {
    -webkit-background-position: 0 0;
    background-position: 0 0;
  }
}

.emotion-0 {
  background: linear-gradient(300.82deg, #D9D9D9 6.57%, #FBFAFA 51.24%, #D9D9D9 93.09%);
  position: relative;
  -webkit-background-size: 300% 100%;
  background-size: 300% 100%;
  -webkit-animation: animation-0 0.9s alternate 0s infinite ease-in-out;
  animation: animation-0 0.9s alternate 0s infinite ease-in-out;
  padding-bottom: 133.750%;
}

.emotion-1 {
  background: linear-gradient(300.82deg, #D9D9D9 6.57%, #FBFAFA 51.24%, #D9D9D9 93.09%);
  position: relative;
  -webkit-background-size: 300% 100%;
  background-size: 300% 100%;
  -webkit-animation: animation-0 0.9s alternate 0s infinite ease-in-out;
  animation: animation-0 0.9s alternate 0s infinite ease-in-out;
  width: 140px;
  height: 18px;
  padding-bottom: 0;
}

.emotion-2 {
  background: linear-gradient(300.82deg, #D9D9D9 6.57%, #FBFAFA 51.24%, #D9D9D9 93.09%);
  position: relative;
  -webkit-background-size: 300% 100%;
  background-size: 300% 100%;
  -webkit-animation: animation-0 0.9s alternate 0s infinite ease-in-out;
  animation: animation-0 0.9s alternate 0s infinite ease-in-out;
  width: 60px;
  height: 18px;
  padding-bottom: 0;
}

.emotion-3 {
  background: linear-gradient(300.82deg, #D9D9D9 6.57%, #FBFAFA 51.24%, #D9D9D9 93.09%);
  position: relative;
  -webkit-background-size: 300% 100%;
  background-size: 300% 100%;
  -webkit-animation: animation-0 0.9s alternate 0s infinite ease-in-out;
  animation: animation-0 0.9s alternate 0s infinite ease-in-out;
  width: 120px;
  height: 18px;
  padding-bottom: 0;
}

<div>
  <div
    class="placeholder-grid__container"
    style="float: left; overflow: hidden;"
  >
    <div
      style="padding-bottom: .5rem;"
    >
      <div
        class="placeholder-grid__image emotion-0"
      />
    </div>
    <div
      style="padding-bottom: .5rem;"
    >
      <div
        class="emotion-1"
      />
    </div>
    <div
      style="padding-bottom: .5rem;"
    >
      <div
        class="emotion-2"
      />
    </div>
    <div>
      <div
        class="emotion-3"
      />
    </div>
  </div>
</div>
`;
