// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`<ProductGroup /> ISM container renders 2x ISM container divs correctly in Desktop (PMCS) 1`] = `
{
  "asFragment": [Function],
  "baseElement": .emotion-0 {
  box-sizing: border-box;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-flex-wrap: wrap;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
}

.emotion-1 {
  box-sizing: border-box;
  -webkit-flex-basis: 100%;
  -ms-flex-preferred-size: 100%;
  flex-basis: 100%;
  -webkit-box-flex: 0;
  -webkit-flex-grow: 0;
  -ms-flex-positive: 0;
  flex-grow: 0;
  margin: 0;
  max-width: 100%;
  padding: 0em;
}

.emotion-2 {
  overflow: hidden;
  margin: 0;
  margin-bottom: 2rem;
  padding: 0;
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 700;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 1.067rem;
  color: #666;
  line-height: normal;
  text-align: center;
}

.emotion-3 {
  text-transform: uppercase;
  letter-spacing: 0.15em;
  display: inline-block;
  position: relative;
  padding: 0 0.5rem;
}

.emotion-3::before,
.emotion-3::after {
  border-top: 2px solid #666;
  display: block;
  position: absolute;
  width: 100em;
  top: 50%;
  content: '';
}

.emotion-3::before {
  right: 100%;
}

.emotion-3::after {
  left: 100%;
}

.emotion-4 {
  --columnSize: 1;
  box-sizing: border-box;
  -webkit-flex-basis: 25%;
  -ms-flex-preferred-size: 25%;
  flex-basis: 25%;
  -webkit-box-flex: 0;
  -webkit-flex-grow: 0;
  -ms-flex-positive: 0;
  flex-grow: 0;
  margin: 0;
  max-width: 25%;
  padding: 0.5em;
}

.emotion-6 {
  min-height: 430px;
  position: relative;
}

.emotion-6 .product-card__image {
  width: 100%;
  display: block;
}

.emotion-7 {
  margin-bottom: 0.5rem;
}

.emotion-9 {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  color: #666;
  line-height: 1.4;
  text-transform: none;
  font-weight: normal;
  font-size: 0.8rem;
}

.emotion-10 {
  text-transform: none;
  line-height: 1.4;
  color: #666;
  font-size: 0.8rem!important;
  letter-spacing: normal;
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-10 .product-price__markdown,
.emotion-10 .product-price--markdown {
  color: #666;
}

.emotion-10 .product-price__highlight,
.emotion-10 .product-price--highlight {
  text-transform: none;
  color: #D00000;
}

.emotion-10 .product-price__highlight priceHighlightFont,
.emotion-10 .product-price--highlight priceHighlightFont {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-10 .product-price__highlight--br,
.emotion-10 .product-price--highlight-br {
  text-transform: none;
  font-size: 0.8125rem;
}

.emotion-10 .product-price__markdown {
  text-transform: none;
  color: #666;
}

.emotion-10 .product-price__no-strike {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-10 .product-price__option {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 700;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-10 .product-price__option--br {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-10 .product-price__percentage-off {
  color: #D00000;
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-transform: none;
  padding-left: 5px;
}

.emotion-10 .product-price__percentage-off-no-padding {
  color: #D00000;
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-transform: none;
}

.emotion-10 .product-price__regular {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-10 .product-price__sale {
  color: #666;
}

.emotion-10 .product-price__strike {
  -webkit-text-decoration: line-through;
  text-decoration: line-through;
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-10 .product-price__same-line {
  padding-left: 5px;
}

.emotion-11 {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  color: #666;
  line-height: 1.4;
  text-transform: none;
  font-weight: bold;
  font-size: 0.8rem;
}

<body>
    <div>
      <div
        class="emotion-0"
        data-testid="grid-root"
        spacing="0.5"
      >
        <div
          class="emotion-1"
          data-testid="grid-root"
          spacing="0"
        >
          <div
            id="subcatHeader_1234"
          />
          <div
            class="emotion-2"
          >
            <h2
              class="emotion-3"
            >
              test group
            </h2>
          </div>
        </div>
        <div
          class="emotion-4"
          data-testid="grid-root"
          spacing="0.5"
        />
        <div
          class="emotion-4"
          data-testid="grid-root"
          spacing="0.5"
        >
          <div
            class="product-card emotion-6"
          >
            <div
              class="product-card__image-wrapper emotion-7"
              id="product123"
            >
              <a
                aria-hidden="false"
                class="emotion-8"
                href="http://localhost/browse/product.do?pid=123&cid=1234&pcid=23456&grid=source_1_4_1"
                target="_self"
              >
                <img
                  alt="sherpa fleece hoodie"
                  class="product-card__image"
                  src="http://www3.assets-gap.com/webcontent/0013/882/196/cn13882196.jpg"
                />
              </a>
            </div>
            <a
              href="http://localhost/browse/product.do?pid=123&cid=1234&pcid=23456&grid=source_1_4_1"
            >
              <div
                class="emotion-9"
              >
                Sherpa fleece hoodie
              </div>
            </a>
            <div>
              <div
                class="product-card-price"
              >
                <div
                  class="emotion-10"
                >
                  <div>
                    <div
                      class="product-price__markdown"
                    >
                      <span
                        aria-label="price.regular_price_aria_label"
                        class="product-price__no-strike"
                        role="text"
                      >
                        $69.95
                      </span>
                    </div>
                    <div
                      aria-label="price.current_price_range_aria_label"
                      class="product-price__highlight"
                      role="text"
                    >
                      <span
                        class="product-price__highlight"
                      >
                        $49.99
                         - 
                        $54.99
                      </span>
                    </div>
                  </div>
                </div>
              </div>
              <div
                class="product-card__marketing-flag emotion-11"
              >
                Final Sale
              </div>
            </div>
          </div>
        </div>
        <div
          class="emotion-4"
          data-testid="grid-root"
          spacing="0.5"
        >
          <div
            class="product-card emotion-6"
          >
            <div
              class="product-card__image-wrapper emotion-7"
              id="product123"
            >
              <a
                aria-hidden="false"
                class="emotion-8"
                href="http://localhost/browse/product.do?pid=123&cid=1234&pcid=23456&grid=source_2_4_1"
                target="_self"
              >
                <img
                  alt="sherpa fleece hoodie"
                  class="product-card__image"
                  src="http://www3.assets-gap.com/webcontent/0013/882/196/cn13882196.jpg"
                />
              </a>
            </div>
            <a
              href="http://localhost/browse/product.do?pid=123&cid=1234&pcid=23456&grid=source_2_4_1"
            >
              <div
                class="emotion-9"
              >
                Sherpa fleece hoodie
              </div>
            </a>
            <div>
              <div
                class="product-card-price"
              >
                <div
                  class="emotion-10"
                >
                  <div>
                    <div
                      class="product-price__markdown"
                    >
                      <span
                        aria-label="price.regular_price_aria_label"
                        class="product-price__no-strike"
                        role="text"
                      >
                        $69.95
                      </span>
                    </div>
                    <div
                      aria-label="price.current_price_range_aria_label"
                      class="product-price__highlight"
                      role="text"
                    >
                      <span
                        class="product-price__highlight"
                      >
                        $49.99
                         - 
                        $54.99
                      </span>
                    </div>
                  </div>
                </div>
              </div>
              <div
                class="product-card__marketing-flag emotion-11"
              >
                Final Sale
              </div>
            </div>
          </div>
        </div>
        <div
          class="emotion-4"
          data-testid="grid-root"
          spacing="0.5"
        >
          <div
            class="product-card emotion-6"
          >
            <div
              class="product-card__image-wrapper emotion-7"
              id="product123"
            >
              <a
                aria-hidden="false"
                class="emotion-8"
                href="http://localhost/browse/product.do?pid=123&cid=1234&pcid=23456&grid=source_3_4_1"
                target="_self"
              >
                <img
                  alt="sherpa fleece hoodie"
                  class="product-card__image"
                  src="http://www3.assets-gap.com/webcontent/0013/882/196/cn13882196.jpg"
                />
              </a>
            </div>
            <a
              href="http://localhost/browse/product.do?pid=123&cid=1234&pcid=23456&grid=source_3_4_1"
            >
              <div
                class="emotion-9"
              >
                Sherpa fleece hoodie
              </div>
            </a>
            <div>
              <div
                class="product-card-price"
              >
                <div
                  class="emotion-10"
                >
                  <div>
                    <div
                      class="product-price__markdown"
                    >
                      <span
                        aria-label="price.regular_price_aria_label"
                        class="product-price__no-strike"
                        role="text"
                      >
                        $69.95
                      </span>
                    </div>
                    <div
                      aria-label="price.current_price_range_aria_label"
                      class="product-price__highlight"
                      role="text"
                    >
                      <span
                        class="product-price__highlight"
                      >
                        $49.99
                         - 
                        $54.99
                      </span>
                    </div>
                  </div>
                </div>
              </div>
              <div
                class="product-card__marketing-flag emotion-11"
              >
                Final Sale
              </div>
            </div>
          </div>
        </div>
        <div
          class="emotion-4"
          data-testid="grid-root"
          spacing="0.5"
        >
          <div
            class="product-card emotion-6"
          >
            <div
              class="product-card__image-wrapper emotion-7"
              id="product124"
            >
              <a
                aria-hidden="false"
                class="emotion-8"
                href="http://localhost/browse/product.do?pid=124&cid=1234&pcid=23456&grid=source_4_4_1"
                target="_self"
              >
                <img
                  alt="Indigo zip hoodie"
                  class="product-card__image"
                  src="http://www.gap.com/webcontent/0013/707/658/cn13707658.jpg"
                />
              </a>
            </div>
            <a
              href="http://localhost/browse/product.do?pid=124&cid=1234&pcid=23456&grid=source_4_4_1"
            >
              <div
                class="emotion-9"
              >
                Indigo zip hoodie
              </div>
            </a>
            <div>
              <div
                class="product-card-price"
              >
                <div
                  class="emotion-10"
                >
                  <div>
                    <div
                      class="product-price__markdown"
                    >
                      <span
                        aria-label="price.regular_price_aria_label"
                        class="product-price__no-strike"
                        role="text"
                      >
                        $69.95
                      </span>
                    </div>
                    <div
                      aria-label="price.current_price_range_aria_label"
                      class="product-price__highlight"
                      role="text"
                    >
                      <span
                        class="product-price__highlight"
                      >
                        $49.99
                         - 
                        $54.99
                      </span>
                    </div>
                  </div>
                </div>
              </div>
              <div
                class="product-card__marketing-flag emotion-11"
              >
                Final Sale
              </div>
            </div>
          </div>
        </div>
        <div
          class="emotion-1"
          data-testid="grid-root"
          spacing="0"
        >
          <div
            id="subcatHeader_1234"
          />
          <div />
        </div>
      </div>
    </div>
  </body>,
  "container": .emotion-0 {
  box-sizing: border-box;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-flex-wrap: wrap;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
}

.emotion-1 {
  box-sizing: border-box;
  -webkit-flex-basis: 100%;
  -ms-flex-preferred-size: 100%;
  flex-basis: 100%;
  -webkit-box-flex: 0;
  -webkit-flex-grow: 0;
  -ms-flex-positive: 0;
  flex-grow: 0;
  margin: 0;
  max-width: 100%;
  padding: 0em;
}

.emotion-2 {
  overflow: hidden;
  margin: 0;
  margin-bottom: 2rem;
  padding: 0;
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 700;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 1.067rem;
  color: #666;
  line-height: normal;
  text-align: center;
}

.emotion-3 {
  text-transform: uppercase;
  letter-spacing: 0.15em;
  display: inline-block;
  position: relative;
  padding: 0 0.5rem;
}

.emotion-3::before,
.emotion-3::after {
  border-top: 2px solid #666;
  display: block;
  position: absolute;
  width: 100em;
  top: 50%;
  content: '';
}

.emotion-3::before {
  right: 100%;
}

.emotion-3::after {
  left: 100%;
}

.emotion-4 {
  --columnSize: 1;
  box-sizing: border-box;
  -webkit-flex-basis: 25%;
  -ms-flex-preferred-size: 25%;
  flex-basis: 25%;
  -webkit-box-flex: 0;
  -webkit-flex-grow: 0;
  -ms-flex-positive: 0;
  flex-grow: 0;
  margin: 0;
  max-width: 25%;
  padding: 0.5em;
}

.emotion-6 {
  min-height: 430px;
  position: relative;
}

.emotion-6 .product-card__image {
  width: 100%;
  display: block;
}

.emotion-7 {
  margin-bottom: 0.5rem;
}

.emotion-9 {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  color: #666;
  line-height: 1.4;
  text-transform: none;
  font-weight: normal;
  font-size: 0.8rem;
}

.emotion-10 {
  text-transform: none;
  line-height: 1.4;
  color: #666;
  font-size: 0.8rem!important;
  letter-spacing: normal;
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-10 .product-price__markdown,
.emotion-10 .product-price--markdown {
  color: #666;
}

.emotion-10 .product-price__highlight,
.emotion-10 .product-price--highlight {
  text-transform: none;
  color: #D00000;
}

.emotion-10 .product-price__highlight priceHighlightFont,
.emotion-10 .product-price--highlight priceHighlightFont {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-10 .product-price__highlight--br,
.emotion-10 .product-price--highlight-br {
  text-transform: none;
  font-size: 0.8125rem;
}

.emotion-10 .product-price__markdown {
  text-transform: none;
  color: #666;
}

.emotion-10 .product-price__no-strike {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-10 .product-price__option {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 700;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-10 .product-price__option--br {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-10 .product-price__percentage-off {
  color: #D00000;
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-transform: none;
  padding-left: 5px;
}

.emotion-10 .product-price__percentage-off-no-padding {
  color: #D00000;
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-transform: none;
}

.emotion-10 .product-price__regular {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-10 .product-price__sale {
  color: #666;
}

.emotion-10 .product-price__strike {
  -webkit-text-decoration: line-through;
  text-decoration: line-through;
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-10 .product-price__same-line {
  padding-left: 5px;
}

.emotion-11 {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  color: #666;
  line-height: 1.4;
  text-transform: none;
  font-weight: bold;
  font-size: 0.8rem;
}

<div>
    <div
      class="emotion-0"
      data-testid="grid-root"
      spacing="0.5"
    >
      <div
        class="emotion-1"
        data-testid="grid-root"
        spacing="0"
      >
        <div
          id="subcatHeader_1234"
        />
        <div
          class="emotion-2"
        >
          <h2
            class="emotion-3"
          >
            test group
          </h2>
        </div>
      </div>
      <div
        class="emotion-4"
        data-testid="grid-root"
        spacing="0.5"
      />
      <div
        class="emotion-4"
        data-testid="grid-root"
        spacing="0.5"
      >
        <div
          class="product-card emotion-6"
        >
          <div
            class="product-card__image-wrapper emotion-7"
            id="product123"
          >
            <a
              aria-hidden="false"
              class="emotion-8"
              href="http://localhost/browse/product.do?pid=123&cid=1234&pcid=23456&grid=source_1_4_1"
              target="_self"
            >
              <img
                alt="sherpa fleece hoodie"
                class="product-card__image"
                src="http://www3.assets-gap.com/webcontent/0013/882/196/cn13882196.jpg"
              />
            </a>
          </div>
          <a
            href="http://localhost/browse/product.do?pid=123&cid=1234&pcid=23456&grid=source_1_4_1"
          >
            <div
              class="emotion-9"
            >
              Sherpa fleece hoodie
            </div>
          </a>
          <div>
            <div
              class="product-card-price"
            >
              <div
                class="emotion-10"
              >
                <div>
                  <div
                    class="product-price__markdown"
                  >
                    <span
                      aria-label="price.regular_price_aria_label"
                      class="product-price__no-strike"
                      role="text"
                    >
                      $69.95
                    </span>
                  </div>
                  <div
                    aria-label="price.current_price_range_aria_label"
                    class="product-price__highlight"
                    role="text"
                  >
                    <span
                      class="product-price__highlight"
                    >
                      $49.99
                       - 
                      $54.99
                    </span>
                  </div>
                </div>
              </div>
            </div>
            <div
              class="product-card__marketing-flag emotion-11"
            >
              Final Sale
            </div>
          </div>
        </div>
      </div>
      <div
        class="emotion-4"
        data-testid="grid-root"
        spacing="0.5"
      >
        <div
          class="product-card emotion-6"
        >
          <div
            class="product-card__image-wrapper emotion-7"
            id="product123"
          >
            <a
              aria-hidden="false"
              class="emotion-8"
              href="http://localhost/browse/product.do?pid=123&cid=1234&pcid=23456&grid=source_2_4_1"
              target="_self"
            >
              <img
                alt="sherpa fleece hoodie"
                class="product-card__image"
                src="http://www3.assets-gap.com/webcontent/0013/882/196/cn13882196.jpg"
              />
            </a>
          </div>
          <a
            href="http://localhost/browse/product.do?pid=123&cid=1234&pcid=23456&grid=source_2_4_1"
          >
            <div
              class="emotion-9"
            >
              Sherpa fleece hoodie
            </div>
          </a>
          <div>
            <div
              class="product-card-price"
            >
              <div
                class="emotion-10"
              >
                <div>
                  <div
                    class="product-price__markdown"
                  >
                    <span
                      aria-label="price.regular_price_aria_label"
                      class="product-price__no-strike"
                      role="text"
                    >
                      $69.95
                    </span>
                  </div>
                  <div
                    aria-label="price.current_price_range_aria_label"
                    class="product-price__highlight"
                    role="text"
                  >
                    <span
                      class="product-price__highlight"
                    >
                      $49.99
                       - 
                      $54.99
                    </span>
                  </div>
                </div>
              </div>
            </div>
            <div
              class="product-card__marketing-flag emotion-11"
            >
              Final Sale
            </div>
          </div>
        </div>
      </div>
      <div
        class="emotion-4"
        data-testid="grid-root"
        spacing="0.5"
      >
        <div
          class="product-card emotion-6"
        >
          <div
            class="product-card__image-wrapper emotion-7"
            id="product123"
          >
            <a
              aria-hidden="false"
              class="emotion-8"
              href="http://localhost/browse/product.do?pid=123&cid=1234&pcid=23456&grid=source_3_4_1"
              target="_self"
            >
              <img
                alt="sherpa fleece hoodie"
                class="product-card__image"
                src="http://www3.assets-gap.com/webcontent/0013/882/196/cn13882196.jpg"
              />
            </a>
          </div>
          <a
            href="http://localhost/browse/product.do?pid=123&cid=1234&pcid=23456&grid=source_3_4_1"
          >
            <div
              class="emotion-9"
            >
              Sherpa fleece hoodie
            </div>
          </a>
          <div>
            <div
              class="product-card-price"
            >
              <div
                class="emotion-10"
              >
                <div>
                  <div
                    class="product-price__markdown"
                  >
                    <span
                      aria-label="price.regular_price_aria_label"
                      class="product-price__no-strike"
                      role="text"
                    >
                      $69.95
                    </span>
                  </div>
                  <div
                    aria-label="price.current_price_range_aria_label"
                    class="product-price__highlight"
                    role="text"
                  >
                    <span
                      class="product-price__highlight"
                    >
                      $49.99
                       - 
                      $54.99
                    </span>
                  </div>
                </div>
              </div>
            </div>
            <div
              class="product-card__marketing-flag emotion-11"
            >
              Final Sale
            </div>
          </div>
        </div>
      </div>
      <div
        class="emotion-4"
        data-testid="grid-root"
        spacing="0.5"
      >
        <div
          class="product-card emotion-6"
        >
          <div
            class="product-card__image-wrapper emotion-7"
            id="product124"
          >
            <a
              aria-hidden="false"
              class="emotion-8"
              href="http://localhost/browse/product.do?pid=124&cid=1234&pcid=23456&grid=source_4_4_1"
              target="_self"
            >
              <img
                alt="Indigo zip hoodie"
                class="product-card__image"
                src="http://www.gap.com/webcontent/0013/707/658/cn13707658.jpg"
              />
            </a>
          </div>
          <a
            href="http://localhost/browse/product.do?pid=124&cid=1234&pcid=23456&grid=source_4_4_1"
          >
            <div
              class="emotion-9"
            >
              Indigo zip hoodie
            </div>
          </a>
          <div>
            <div
              class="product-card-price"
            >
              <div
                class="emotion-10"
              >
                <div>
                  <div
                    class="product-price__markdown"
                  >
                    <span
                      aria-label="price.regular_price_aria_label"
                      class="product-price__no-strike"
                      role="text"
                    >
                      $69.95
                    </span>
                  </div>
                  <div
                    aria-label="price.current_price_range_aria_label"
                    class="product-price__highlight"
                    role="text"
                  >
                    <span
                      class="product-price__highlight"
                    >
                      $49.99
                       - 
                      $54.99
                    </span>
                  </div>
                </div>
              </div>
            </div>
            <div
              class="product-card__marketing-flag emotion-11"
            >
              Final Sale
            </div>
          </div>
        </div>
      </div>
      <div
        class="emotion-1"
        data-testid="grid-root"
        spacing="0"
      >
        <div
          id="subcatHeader_1234"
        />
        <div />
      </div>
    </div>
  </div>,
  "debug": [Function],
  "findAllByAltText": [Function],
  "findAllByDisplayValue": [Function],
  "findAllByLabelText": [Function],
  "findAllByPlaceholderText": [Function],
  "findAllByRole": [Function],
  "findAllByTestId": [Function],
  "findAllByText": [Function],
  "findAllByTitle": [Function],
  "findByAltText": [Function],
  "findByDisplayValue": [Function],
  "findByLabelText": [Function],
  "findByPlaceholderText": [Function],
  "findByRole": [Function],
  "findByTestId": [Function],
  "findByText": [Function],
  "findByTitle": [Function],
  "getAllByAltText": [Function],
  "getAllByDisplayValue": [Function],
  "getAllByLabelText": [Function],
  "getAllByPlaceholderText": [Function],
  "getAllByRole": [Function],
  "getAllByTestId": [Function],
  "getAllByText": [Function],
  "getAllByTitle": [Function],
  "getByAltText": [Function],
  "getByDisplayValue": [Function],
  "getByLabelText": [Function],
  "getByPlaceholderText": [Function],
  "getByRole": [Function],
  "getByTestId": [Function],
  "getByText": [Function],
  "getByTitle": [Function],
  "queryAllByAltText": [Function],
  "queryAllByDisplayValue": [Function],
  "queryAllByLabelText": [Function],
  "queryAllByPlaceholderText": [Function],
  "queryAllByRole": [Function],
  "queryAllByTestId": [Function],
  "queryAllByText": [Function],
  "queryAllByTitle": [Function],
  "queryByAltText": [Function],
  "queryByDisplayValue": [Function],
  "queryByLabelText": [Function],
  "queryByPlaceholderText": [Function],
  "queryByRole": [Function],
  "queryByTestId": [Function],
  "queryByText": [Function],
  "queryByTitle": [Function],
  "rerender": [Function],
  "unmount": [Function],
}
`;

exports[`<ProductGroup /> ISM container renders ISM container divs correctly in Desktop 1`] = `
{
  "asFragment": [Function],
  "baseElement": .emotion-0 {
  box-sizing: border-box;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-flex-wrap: wrap;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
}

.emotion-1 {
  box-sizing: border-box;
  -webkit-flex-basis: 100%;
  -ms-flex-preferred-size: 100%;
  flex-basis: 100%;
  -webkit-box-flex: 0;
  -webkit-flex-grow: 0;
  -ms-flex-positive: 0;
  flex-grow: 0;
  margin: 0;
  max-width: 100%;
  padding: 0em;
}

.emotion-2 {
  overflow: hidden;
  margin: 0;
  margin-bottom: 2rem;
  padding: 0;
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 700;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 1.067rem;
  color: #666;
  line-height: normal;
  text-align: center;
}

.emotion-3 {
  text-transform: uppercase;
  letter-spacing: 0.15em;
  display: inline-block;
  position: relative;
  padding: 0 0.5rem;
}

.emotion-3::before,
.emotion-3::after {
  border-top: 2px solid #666;
  display: block;
  position: absolute;
  width: 100em;
  top: 50%;
  content: '';
}

.emotion-3::before {
  right: 100%;
}

.emotion-3::after {
  left: 100%;
}

.emotion-4 {
  --columnSize: 1;
  box-sizing: border-box;
  -webkit-flex-basis: 25%;
  -ms-flex-preferred-size: 25%;
  flex-basis: 25%;
  -webkit-box-flex: 0;
  -webkit-flex-grow: 0;
  -ms-flex-positive: 0;
  flex-grow: 0;
  margin: 0;
  max-width: 25%;
  padding: 0.5em;
}

.emotion-6 {
  min-height: 430px;
  position: relative;
}

.emotion-6 .product-card__image {
  width: 100%;
  display: block;
}

.emotion-7 {
  margin-bottom: 0.5rem;
}

.emotion-9 {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  color: #666;
  line-height: 1.4;
  text-transform: none;
  font-weight: normal;
  font-size: 0.8rem;
}

.emotion-10 {
  text-transform: none;
  line-height: 1.4;
  color: #666;
  font-size: 0.8rem!important;
  letter-spacing: normal;
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-10 .product-price__markdown,
.emotion-10 .product-price--markdown {
  color: #666;
}

.emotion-10 .product-price__highlight,
.emotion-10 .product-price--highlight {
  text-transform: none;
  color: #D00000;
}

.emotion-10 .product-price__highlight priceHighlightFont,
.emotion-10 .product-price--highlight priceHighlightFont {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-10 .product-price__highlight--br,
.emotion-10 .product-price--highlight-br {
  text-transform: none;
  font-size: 0.8125rem;
}

.emotion-10 .product-price__markdown {
  text-transform: none;
  color: #666;
}

.emotion-10 .product-price__no-strike {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-10 .product-price__option {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 700;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-10 .product-price__option--br {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-10 .product-price__percentage-off {
  color: #D00000;
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-transform: none;
  padding-left: 5px;
}

.emotion-10 .product-price__percentage-off-no-padding {
  color: #D00000;
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-transform: none;
}

.emotion-10 .product-price__regular {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-10 .product-price__sale {
  color: #666;
}

.emotion-10 .product-price__strike {
  -webkit-text-decoration: line-through;
  text-decoration: line-through;
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-10 .product-price__same-line {
  padding-left: 5px;
}

.emotion-11 {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  color: #666;
  line-height: 1.4;
  text-transform: none;
  font-weight: bold;
  font-size: 0.8rem;
}

<body>
    <div>
      <div
        class="emotion-0"
        data-testid="grid-root"
        spacing="0.5"
      >
        <div
          class="emotion-1"
          data-testid="grid-root"
          spacing="0"
        >
          <div
            id="subcatHeader_1234"
          />
          <div
            class="emotion-2"
          >
            <h2
              class="emotion-3"
            >
              test group
            </h2>
          </div>
        </div>
        <div
          class="emotion-4"
          data-testid="grid-root"
          spacing="0.5"
        />
        <div
          class="emotion-4"
          data-testid="grid-root"
          spacing="0.5"
        >
          <div
            class="product-card emotion-6"
          >
            <div
              class="product-card__image-wrapper emotion-7"
              id="product123"
            >
              <a
                aria-hidden="false"
                class="emotion-8"
                href="http://localhost/browse/product.do?pid=123&cid=1234&pcid=23456&grid=source_1_4_1"
                target="_self"
              >
                <img
                  alt="sherpa fleece hoodie"
                  class="product-card__image"
                  src="http://www3.assets-gap.com/webcontent/0013/882/196/cn13882196.jpg"
                />
              </a>
            </div>
            <a
              href="http://localhost/browse/product.do?pid=123&cid=1234&pcid=23456&grid=source_1_4_1"
            >
              <div
                class="emotion-9"
              >
                Sherpa fleece hoodie
              </div>
            </a>
            <div>
              <div
                class="product-card-price"
              >
                <div
                  class="emotion-10"
                >
                  <div>
                    <div
                      class="product-price__markdown"
                    >
                      <span
                        aria-label="price.regular_price_aria_label"
                        class="product-price__no-strike"
                        role="text"
                      >
                        $69.95
                      </span>
                    </div>
                    <div
                      aria-label="price.current_price_range_aria_label"
                      class="product-price__highlight"
                      role="text"
                    >
                      <span
                        class="product-price__highlight"
                      >
                        $49.99
                         - 
                        $54.99
                      </span>
                    </div>
                  </div>
                </div>
              </div>
              <div
                class="product-card__marketing-flag emotion-11"
              >
                Final Sale
              </div>
            </div>
          </div>
        </div>
        <div
          class="emotion-4"
          data-testid="grid-root"
          spacing="0.5"
        >
          <div
            class="product-card emotion-6"
          >
            <div
              class="product-card__image-wrapper emotion-7"
              id="product123"
            >
              <a
                aria-hidden="false"
                class="emotion-8"
                href="http://localhost/browse/product.do?pid=123&cid=1234&pcid=23456&grid=source_2_4_1"
                target="_self"
              >
                <img
                  alt="sherpa fleece hoodie"
                  class="product-card__image"
                  src="http://www3.assets-gap.com/webcontent/0013/882/196/cn13882196.jpg"
                />
              </a>
            </div>
            <a
              href="http://localhost/browse/product.do?pid=123&cid=1234&pcid=23456&grid=source_2_4_1"
            >
              <div
                class="emotion-9"
              >
                Sherpa fleece hoodie
              </div>
            </a>
            <div>
              <div
                class="product-card-price"
              >
                <div
                  class="emotion-10"
                >
                  <div>
                    <div
                      class="product-price__markdown"
                    >
                      <span
                        aria-label="price.regular_price_aria_label"
                        class="product-price__no-strike"
                        role="text"
                      >
                        $69.95
                      </span>
                    </div>
                    <div
                      aria-label="price.current_price_range_aria_label"
                      class="product-price__highlight"
                      role="text"
                    >
                      <span
                        class="product-price__highlight"
                      >
                        $49.99
                         - 
                        $54.99
                      </span>
                    </div>
                  </div>
                </div>
              </div>
              <div
                class="product-card__marketing-flag emotion-11"
              >
                Final Sale
              </div>
            </div>
          </div>
        </div>
        <div
          class="emotion-4"
          data-testid="grid-root"
          spacing="0.5"
        >
          <div
            class="product-card emotion-6"
          >
            <div
              class="product-card__image-wrapper emotion-7"
              id="product123"
            >
              <a
                aria-hidden="false"
                class="emotion-8"
                href="http://localhost/browse/product.do?pid=123&cid=1234&pcid=23456&grid=source_3_4_1"
                target="_self"
              >
                <img
                  alt="sherpa fleece hoodie"
                  class="product-card__image"
                  src="http://www3.assets-gap.com/webcontent/0013/882/196/cn13882196.jpg"
                />
              </a>
            </div>
            <a
              href="http://localhost/browse/product.do?pid=123&cid=1234&pcid=23456&grid=source_3_4_1"
            >
              <div
                class="emotion-9"
              >
                Sherpa fleece hoodie
              </div>
            </a>
            <div>
              <div
                class="product-card-price"
              >
                <div
                  class="emotion-10"
                >
                  <div>
                    <div
                      class="product-price__markdown"
                    >
                      <span
                        aria-label="price.regular_price_aria_label"
                        class="product-price__no-strike"
                        role="text"
                      >
                        $69.95
                      </span>
                    </div>
                    <div
                      aria-label="price.current_price_range_aria_label"
                      class="product-price__highlight"
                      role="text"
                    >
                      <span
                        class="product-price__highlight"
                      >
                        $49.99
                         - 
                        $54.99
                      </span>
                    </div>
                  </div>
                </div>
              </div>
              <div
                class="product-card__marketing-flag emotion-11"
              >
                Final Sale
              </div>
            </div>
          </div>
        </div>
        <div
          class="emotion-4"
          data-testid="grid-root"
          spacing="0.5"
        >
          <div
            class="product-card emotion-6"
          >
            <div
              class="product-card__image-wrapper emotion-7"
              id="product124"
            >
              <a
                aria-hidden="false"
                class="emotion-8"
                href="http://localhost/browse/product.do?pid=124&cid=1234&pcid=23456&grid=source_4_4_1"
                target="_self"
              >
                <img
                  alt="Indigo zip hoodie"
                  class="product-card__image"
                  src="http://www.gap.com/webcontent/0013/707/658/cn13707658.jpg"
                />
              </a>
            </div>
            <a
              href="http://localhost/browse/product.do?pid=124&cid=1234&pcid=23456&grid=source_4_4_1"
            >
              <div
                class="emotion-9"
              >
                Indigo zip hoodie
              </div>
            </a>
            <div>
              <div
                class="product-card-price"
              >
                <div
                  class="emotion-10"
                >
                  <div>
                    <div
                      class="product-price__markdown"
                    >
                      <span
                        aria-label="price.regular_price_aria_label"
                        class="product-price__no-strike"
                        role="text"
                      >
                        $69.95
                      </span>
                    </div>
                    <div
                      aria-label="price.current_price_range_aria_label"
                      class="product-price__highlight"
                      role="text"
                    >
                      <span
                        class="product-price__highlight"
                      >
                        $49.99
                         - 
                        $54.99
                      </span>
                    </div>
                  </div>
                </div>
              </div>
              <div
                class="product-card__marketing-flag emotion-11"
              >
                Final Sale
              </div>
            </div>
          </div>
        </div>
        <div
          class="emotion-1"
          data-testid="grid-root"
          spacing="0"
        >
          <div
            id="subcatHeader_1234"
          />
          <div />
        </div>
      </div>
    </div>
  </body>,
  "container": .emotion-0 {
  box-sizing: border-box;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-flex-wrap: wrap;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
}

.emotion-1 {
  box-sizing: border-box;
  -webkit-flex-basis: 100%;
  -ms-flex-preferred-size: 100%;
  flex-basis: 100%;
  -webkit-box-flex: 0;
  -webkit-flex-grow: 0;
  -ms-flex-positive: 0;
  flex-grow: 0;
  margin: 0;
  max-width: 100%;
  padding: 0em;
}

.emotion-2 {
  overflow: hidden;
  margin: 0;
  margin-bottom: 2rem;
  padding: 0;
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 700;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 1.067rem;
  color: #666;
  line-height: normal;
  text-align: center;
}

.emotion-3 {
  text-transform: uppercase;
  letter-spacing: 0.15em;
  display: inline-block;
  position: relative;
  padding: 0 0.5rem;
}

.emotion-3::before,
.emotion-3::after {
  border-top: 2px solid #666;
  display: block;
  position: absolute;
  width: 100em;
  top: 50%;
  content: '';
}

.emotion-3::before {
  right: 100%;
}

.emotion-3::after {
  left: 100%;
}

.emotion-4 {
  --columnSize: 1;
  box-sizing: border-box;
  -webkit-flex-basis: 25%;
  -ms-flex-preferred-size: 25%;
  flex-basis: 25%;
  -webkit-box-flex: 0;
  -webkit-flex-grow: 0;
  -ms-flex-positive: 0;
  flex-grow: 0;
  margin: 0;
  max-width: 25%;
  padding: 0.5em;
}

.emotion-6 {
  min-height: 430px;
  position: relative;
}

.emotion-6 .product-card__image {
  width: 100%;
  display: block;
}

.emotion-7 {
  margin-bottom: 0.5rem;
}

.emotion-9 {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  color: #666;
  line-height: 1.4;
  text-transform: none;
  font-weight: normal;
  font-size: 0.8rem;
}

.emotion-10 {
  text-transform: none;
  line-height: 1.4;
  color: #666;
  font-size: 0.8rem!important;
  letter-spacing: normal;
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-10 .product-price__markdown,
.emotion-10 .product-price--markdown {
  color: #666;
}

.emotion-10 .product-price__highlight,
.emotion-10 .product-price--highlight {
  text-transform: none;
  color: #D00000;
}

.emotion-10 .product-price__highlight priceHighlightFont,
.emotion-10 .product-price--highlight priceHighlightFont {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-10 .product-price__highlight--br,
.emotion-10 .product-price--highlight-br {
  text-transform: none;
  font-size: 0.8125rem;
}

.emotion-10 .product-price__markdown {
  text-transform: none;
  color: #666;
}

.emotion-10 .product-price__no-strike {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-10 .product-price__option {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 700;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-10 .product-price__option--br {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-10 .product-price__percentage-off {
  color: #D00000;
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-transform: none;
  padding-left: 5px;
}

.emotion-10 .product-price__percentage-off-no-padding {
  color: #D00000;
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-transform: none;
}

.emotion-10 .product-price__regular {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-10 .product-price__sale {
  color: #666;
}

.emotion-10 .product-price__strike {
  -webkit-text-decoration: line-through;
  text-decoration: line-through;
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-10 .product-price__same-line {
  padding-left: 5px;
}

.emotion-11 {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  color: #666;
  line-height: 1.4;
  text-transform: none;
  font-weight: bold;
  font-size: 0.8rem;
}

<div>
    <div
      class="emotion-0"
      data-testid="grid-root"
      spacing="0.5"
    >
      <div
        class="emotion-1"
        data-testid="grid-root"
        spacing="0"
      >
        <div
          id="subcatHeader_1234"
        />
        <div
          class="emotion-2"
        >
          <h2
            class="emotion-3"
          >
            test group
          </h2>
        </div>
      </div>
      <div
        class="emotion-4"
        data-testid="grid-root"
        spacing="0.5"
      />
      <div
        class="emotion-4"
        data-testid="grid-root"
        spacing="0.5"
      >
        <div
          class="product-card emotion-6"
        >
          <div
            class="product-card__image-wrapper emotion-7"
            id="product123"
          >
            <a
              aria-hidden="false"
              class="emotion-8"
              href="http://localhost/browse/product.do?pid=123&cid=1234&pcid=23456&grid=source_1_4_1"
              target="_self"
            >
              <img
                alt="sherpa fleece hoodie"
                class="product-card__image"
                src="http://www3.assets-gap.com/webcontent/0013/882/196/cn13882196.jpg"
              />
            </a>
          </div>
          <a
            href="http://localhost/browse/product.do?pid=123&cid=1234&pcid=23456&grid=source_1_4_1"
          >
            <div
              class="emotion-9"
            >
              Sherpa fleece hoodie
            </div>
          </a>
          <div>
            <div
              class="product-card-price"
            >
              <div
                class="emotion-10"
              >
                <div>
                  <div
                    class="product-price__markdown"
                  >
                    <span
                      aria-label="price.regular_price_aria_label"
                      class="product-price__no-strike"
                      role="text"
                    >
                      $69.95
                    </span>
                  </div>
                  <div
                    aria-label="price.current_price_range_aria_label"
                    class="product-price__highlight"
                    role="text"
                  >
                    <span
                      class="product-price__highlight"
                    >
                      $49.99
                       - 
                      $54.99
                    </span>
                  </div>
                </div>
              </div>
            </div>
            <div
              class="product-card__marketing-flag emotion-11"
            >
              Final Sale
            </div>
          </div>
        </div>
      </div>
      <div
        class="emotion-4"
        data-testid="grid-root"
        spacing="0.5"
      >
        <div
          class="product-card emotion-6"
        >
          <div
            class="product-card__image-wrapper emotion-7"
            id="product123"
          >
            <a
              aria-hidden="false"
              class="emotion-8"
              href="http://localhost/browse/product.do?pid=123&cid=1234&pcid=23456&grid=source_2_4_1"
              target="_self"
            >
              <img
                alt="sherpa fleece hoodie"
                class="product-card__image"
                src="http://www3.assets-gap.com/webcontent/0013/882/196/cn13882196.jpg"
              />
            </a>
          </div>
          <a
            href="http://localhost/browse/product.do?pid=123&cid=1234&pcid=23456&grid=source_2_4_1"
          >
            <div
              class="emotion-9"
            >
              Sherpa fleece hoodie
            </div>
          </a>
          <div>
            <div
              class="product-card-price"
            >
              <div
                class="emotion-10"
              >
                <div>
                  <div
                    class="product-price__markdown"
                  >
                    <span
                      aria-label="price.regular_price_aria_label"
                      class="product-price__no-strike"
                      role="text"
                    >
                      $69.95
                    </span>
                  </div>
                  <div
                    aria-label="price.current_price_range_aria_label"
                    class="product-price__highlight"
                    role="text"
                  >
                    <span
                      class="product-price__highlight"
                    >
                      $49.99
                       - 
                      $54.99
                    </span>
                  </div>
                </div>
              </div>
            </div>
            <div
              class="product-card__marketing-flag emotion-11"
            >
              Final Sale
            </div>
          </div>
        </div>
      </div>
      <div
        class="emotion-4"
        data-testid="grid-root"
        spacing="0.5"
      >
        <div
          class="product-card emotion-6"
        >
          <div
            class="product-card__image-wrapper emotion-7"
            id="product123"
          >
            <a
              aria-hidden="false"
              class="emotion-8"
              href="http://localhost/browse/product.do?pid=123&cid=1234&pcid=23456&grid=source_3_4_1"
              target="_self"
            >
              <img
                alt="sherpa fleece hoodie"
                class="product-card__image"
                src="http://www3.assets-gap.com/webcontent/0013/882/196/cn13882196.jpg"
              />
            </a>
          </div>
          <a
            href="http://localhost/browse/product.do?pid=123&cid=1234&pcid=23456&grid=source_3_4_1"
          >
            <div
              class="emotion-9"
            >
              Sherpa fleece hoodie
            </div>
          </a>
          <div>
            <div
              class="product-card-price"
            >
              <div
                class="emotion-10"
              >
                <div>
                  <div
                    class="product-price__markdown"
                  >
                    <span
                      aria-label="price.regular_price_aria_label"
                      class="product-price__no-strike"
                      role="text"
                    >
                      $69.95
                    </span>
                  </div>
                  <div
                    aria-label="price.current_price_range_aria_label"
                    class="product-price__highlight"
                    role="text"
                  >
                    <span
                      class="product-price__highlight"
                    >
                      $49.99
                       - 
                      $54.99
                    </span>
                  </div>
                </div>
              </div>
            </div>
            <div
              class="product-card__marketing-flag emotion-11"
            >
              Final Sale
            </div>
          </div>
        </div>
      </div>
      <div
        class="emotion-4"
        data-testid="grid-root"
        spacing="0.5"
      >
        <div
          class="product-card emotion-6"
        >
          <div
            class="product-card__image-wrapper emotion-7"
            id="product124"
          >
            <a
              aria-hidden="false"
              class="emotion-8"
              href="http://localhost/browse/product.do?pid=124&cid=1234&pcid=23456&grid=source_4_4_1"
              target="_self"
            >
              <img
                alt="Indigo zip hoodie"
                class="product-card__image"
                src="http://www.gap.com/webcontent/0013/707/658/cn13707658.jpg"
              />
            </a>
          </div>
          <a
            href="http://localhost/browse/product.do?pid=124&cid=1234&pcid=23456&grid=source_4_4_1"
          >
            <div
              class="emotion-9"
            >
              Indigo zip hoodie
            </div>
          </a>
          <div>
            <div
              class="product-card-price"
            >
              <div
                class="emotion-10"
              >
                <div>
                  <div
                    class="product-price__markdown"
                  >
                    <span
                      aria-label="price.regular_price_aria_label"
                      class="product-price__no-strike"
                      role="text"
                    >
                      $69.95
                    </span>
                  </div>
                  <div
                    aria-label="price.current_price_range_aria_label"
                    class="product-price__highlight"
                    role="text"
                  >
                    <span
                      class="product-price__highlight"
                    >
                      $49.99
                       - 
                      $54.99
                    </span>
                  </div>
                </div>
              </div>
            </div>
            <div
              class="product-card__marketing-flag emotion-11"
            >
              Final Sale
            </div>
          </div>
        </div>
      </div>
      <div
        class="emotion-1"
        data-testid="grid-root"
        spacing="0"
      >
        <div
          id="subcatHeader_1234"
        />
        <div />
      </div>
    </div>
  </div>,
  "debug": [Function],
  "findAllByAltText": [Function],
  "findAllByDisplayValue": [Function],
  "findAllByLabelText": [Function],
  "findAllByPlaceholderText": [Function],
  "findAllByRole": [Function],
  "findAllByTestId": [Function],
  "findAllByText": [Function],
  "findAllByTitle": [Function],
  "findByAltText": [Function],
  "findByDisplayValue": [Function],
  "findByLabelText": [Function],
  "findByPlaceholderText": [Function],
  "findByRole": [Function],
  "findByTestId": [Function],
  "findByText": [Function],
  "findByTitle": [Function],
  "getAllByAltText": [Function],
  "getAllByDisplayValue": [Function],
  "getAllByLabelText": [Function],
  "getAllByPlaceholderText": [Function],
  "getAllByRole": [Function],
  "getAllByTestId": [Function],
  "getAllByText": [Function],
  "getAllByTitle": [Function],
  "getByAltText": [Function],
  "getByDisplayValue": [Function],
  "getByLabelText": [Function],
  "getByPlaceholderText": [Function],
  "getByRole": [Function],
  "getByTestId": [Function],
  "getByText": [Function],
  "getByTitle": [Function],
  "queryAllByAltText": [Function],
  "queryAllByDisplayValue": [Function],
  "queryAllByLabelText": [Function],
  "queryAllByPlaceholderText": [Function],
  "queryAllByRole": [Function],
  "queryAllByTestId": [Function],
  "queryAllByText": [Function],
  "queryAllByTitle": [Function],
  "queryByAltText": [Function],
  "queryByDisplayValue": [Function],
  "queryByLabelText": [Function],
  "queryByPlaceholderText": [Function],
  "queryByRole": [Function],
  "queryByTestId": [Function],
  "queryByText": [Function],
  "queryByTitle": [Function],
  "rerender": [Function],
  "unmount": [Function],
}
`;

exports[`<ProductGroup /> ISM container renders ISM container divs correctly in Mobile 1`] = `
{
  "asFragment": [Function],
  "baseElement": .emotion-0 {
  box-sizing: border-box;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-flex-wrap: wrap;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
}

.emotion-1 {
  box-sizing: border-box;
  -webkit-flex-basis: 100%;
  -ms-flex-preferred-size: 100%;
  flex-basis: 100%;
  -webkit-box-flex: 0;
  -webkit-flex-grow: 0;
  -ms-flex-positive: 0;
  flex-grow: 0;
  margin: 0;
  max-width: 100%;
  padding: 0em;
}

.emotion-2 {
  overflow: hidden;
  margin: 0;
  margin-bottom: 2rem;
  padding: 0;
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 700;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 1.067rem;
  color: #666;
  line-height: normal;
  text-align: center;
}

.emotion-3 {
  text-transform: uppercase;
  letter-spacing: 0.15em;
  display: inline-block;
  position: relative;
  padding: 0 0.5rem;
}

.emotion-3::before,
.emotion-3::after {
  border-top: 2px solid #666;
  display: block;
  position: absolute;
  width: 100em;
  top: 50%;
  content: '';
}

.emotion-3::before {
  right: 100%;
}

.emotion-3::after {
  left: 100%;
}

.emotion-4 {
  --columnSize: 1;
  box-sizing: border-box;
  -webkit-flex-basis: 50%;
  -ms-flex-preferred-size: 50%;
  flex-basis: 50%;
  -webkit-box-flex: 0;
  -webkit-flex-grow: 0;
  -ms-flex-positive: 0;
  flex-grow: 0;
  margin: 0;
  max-width: 50%;
  padding: 0.5em;
}

.emotion-5 {
  min-height: 340px;
  position: relative;
}

.emotion-5 .product-card__image {
  width: 100%;
  display: block;
}

.emotion-6 {
  margin-bottom: 0.5rem;
}

.emotion-8 {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  color: #666;
  line-height: 1.4;
  text-transform: none;
  font-weight: normal;
  font-size: 0.8rem;
}

.emotion-9 {
  text-transform: none;
  line-height: 1.4;
  color: #666;
  font-size: 0.8rem!important;
  letter-spacing: normal;
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-9 .product-price__markdown,
.emotion-9 .product-price--markdown {
  color: #666;
}

.emotion-9 .product-price__highlight,
.emotion-9 .product-price--highlight {
  text-transform: none;
  color: #D00000;
}

.emotion-9 .product-price__highlight priceHighlightFont,
.emotion-9 .product-price--highlight priceHighlightFont {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-9 .product-price__highlight--br,
.emotion-9 .product-price--highlight-br {
  text-transform: none;
  font-size: 0.8125rem;
}

.emotion-9 .product-price__markdown {
  text-transform: none;
  color: #666;
}

.emotion-9 .product-price__no-strike {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-9 .product-price__option {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 700;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-9 .product-price__option--br {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-9 .product-price__percentage-off {
  color: #D00000;
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-transform: none;
  padding-left: 5px;
}

.emotion-9 .product-price__percentage-off-no-padding {
  color: #D00000;
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-transform: none;
}

.emotion-9 .product-price__regular {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-9 .product-price__sale {
  color: #666;
}

.emotion-9 .product-price__strike {
  -webkit-text-decoration: line-through;
  text-decoration: line-through;
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-9 .product-price__same-line {
  padding-left: 5px;
}

.emotion-10 {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  color: #666;
  line-height: 1.4;
  text-transform: none;
  font-weight: bold;
  font-size: 0.8rem;
}

<body>
    <div>
      <div
        class="emotion-0"
        data-testid="grid-root"
        spacing="0.5"
      >
        <div
          class="emotion-1"
          data-testid="grid-root"
          spacing="0"
        >
          <div
            id="subcatHeader_1234"
          />
          <div
            class="emotion-2"
          >
            <h2
              class="emotion-3"
            >
              test group
            </h2>
          </div>
        </div>
        <div
          class="emotion-4"
          data-testid="grid-root"
          spacing="0.5"
        >
          <div
            class="product-card emotion-5"
          >
            <div
              class="product-card__image-wrapper emotion-6"
              id="product123"
            >
              <a
                aria-hidden="false"
                class="emotion-7"
                href="http://localhost/browse/product.do?pid=123&cid=1234&pcid=23456&grid=source_0_4_1"
                target="_self"
              >
                <img
                  alt="sherpa fleece hoodie"
                  class="product-card__image"
                  src="http://www3.assets-gap.com/webcontent/0013/882/196/cn13882196.jpg"
                />
              </a>
            </div>
            <a
              href="http://localhost/browse/product.do?pid=123&cid=1234&pcid=23456&grid=source_0_4_1"
            >
              <div
                class="emotion-8"
              >
                Sherpa fleece hoodie
              </div>
            </a>
            <div>
              <div
                class="product-card-price"
              >
                <div
                  class="emotion-9"
                >
                  <div>
                    <div
                      class="product-price__markdown"
                    >
                      <span
                        aria-label="price.regular_price_aria_label"
                        class="product-price__no-strike"
                        role="text"
                      >
                        $69.95
                      </span>
                    </div>
                    <div
                      aria-label="price.current_price_range_aria_label"
                      class="product-price__highlight"
                      role="text"
                    >
                      <span
                        class="product-price__highlight"
                      >
                        $49.99
                         - 
                        $54.99
                      </span>
                    </div>
                  </div>
                </div>
              </div>
              <div
                class="product-card__marketing-flag emotion-10"
              >
                Final Sale
              </div>
            </div>
          </div>
        </div>
        <div
          class="emotion-4"
          data-testid="grid-root"
          spacing="0.5"
        >
          <div
            class="product-card emotion-5"
          >
            <div
              class="product-card__image-wrapper emotion-6"
              id="product123"
            >
              <a
                aria-hidden="false"
                class="emotion-7"
                href="http://localhost/browse/product.do?pid=123&cid=1234&pcid=23456&grid=source_1_4_1"
                target="_self"
              >
                <img
                  alt="sherpa fleece hoodie"
                  class="product-card__image"
                  src="http://www3.assets-gap.com/webcontent/0013/882/196/cn13882196.jpg"
                />
              </a>
            </div>
            <a
              href="http://localhost/browse/product.do?pid=123&cid=1234&pcid=23456&grid=source_1_4_1"
            >
              <div
                class="emotion-8"
              >
                Sherpa fleece hoodie
              </div>
            </a>
            <div>
              <div
                class="product-card-price"
              >
                <div
                  class="emotion-9"
                >
                  <div>
                    <div
                      class="product-price__markdown"
                    >
                      <span
                        aria-label="price.regular_price_aria_label"
                        class="product-price__no-strike"
                        role="text"
                      >
                        $69.95
                      </span>
                    </div>
                    <div
                      aria-label="price.current_price_range_aria_label"
                      class="product-price__highlight"
                      role="text"
                    >
                      <span
                        class="product-price__highlight"
                      >
                        $49.99
                         - 
                        $54.99
                      </span>
                    </div>
                  </div>
                </div>
              </div>
              <div
                class="product-card__marketing-flag emotion-10"
              >
                Final Sale
              </div>
            </div>
          </div>
        </div>
        <div
          class="emotion-4"
          data-testid="grid-root"
          spacing="0.5"
        >
          <div
            class="product-card emotion-5"
          >
            <div
              class="product-card__image-wrapper emotion-6"
              id="product123"
            >
              <a
                aria-hidden="false"
                class="emotion-7"
                href="http://localhost/browse/product.do?pid=123&cid=1234&pcid=23456&grid=source_2_4_1"
                target="_self"
              >
                <img
                  alt="sherpa fleece hoodie"
                  class="product-card__image"
                  src="http://www3.assets-gap.com/webcontent/0013/882/196/cn13882196.jpg"
                />
              </a>
            </div>
            <a
              href="http://localhost/browse/product.do?pid=123&cid=1234&pcid=23456&grid=source_2_4_1"
            >
              <div
                class="emotion-8"
              >
                Sherpa fleece hoodie
              </div>
            </a>
            <div>
              <div
                class="product-card-price"
              >
                <div
                  class="emotion-9"
                >
                  <div>
                    <div
                      class="product-price__markdown"
                    >
                      <span
                        aria-label="price.regular_price_aria_label"
                        class="product-price__no-strike"
                        role="text"
                      >
                        $69.95
                      </span>
                    </div>
                    <div
                      aria-label="price.current_price_range_aria_label"
                      class="product-price__highlight"
                      role="text"
                    >
                      <span
                        class="product-price__highlight"
                      >
                        $49.99
                         - 
                        $54.99
                      </span>
                    </div>
                  </div>
                </div>
              </div>
              <div
                class="product-card__marketing-flag emotion-10"
              >
                Final Sale
              </div>
            </div>
          </div>
        </div>
        <div
          class="emotion-4"
          data-testid="grid-root"
          spacing="0.5"
        >
          <div
            class="product-card emotion-5"
          >
            <div
              class="product-card__image-wrapper emotion-6"
              id="product124"
            >
              <a
                aria-hidden="false"
                class="emotion-7"
                href="http://localhost/browse/product.do?pid=124&cid=1234&pcid=23456&grid=source_3_4_1"
                target="_self"
              >
                <img
                  alt="Indigo zip hoodie"
                  class="product-card__image"
                  src="http://www.gap.com/webcontent/0013/707/658/cn13707658.jpg"
                />
              </a>
            </div>
            <a
              href="http://localhost/browse/product.do?pid=124&cid=1234&pcid=23456&grid=source_3_4_1"
            >
              <div
                class="emotion-8"
              >
                Indigo zip hoodie
              </div>
            </a>
            <div>
              <div
                class="product-card-price"
              >
                <div
                  class="emotion-9"
                >
                  <div>
                    <div
                      class="product-price__markdown"
                    >
                      <span
                        aria-label="price.regular_price_aria_label"
                        class="product-price__no-strike"
                        role="text"
                      >
                        $69.95
                      </span>
                    </div>
                    <div
                      aria-label="price.current_price_range_aria_label"
                      class="product-price__highlight"
                      role="text"
                    >
                      <span
                        class="product-price__highlight"
                      >
                        $49.99
                         - 
                        $54.99
                      </span>
                    </div>
                  </div>
                </div>
              </div>
              <div
                class="product-card__marketing-flag emotion-10"
              >
                Final Sale
              </div>
            </div>
          </div>
        </div>
        <div
          class="emotion-1"
          data-testid="grid-root"
          spacing="0"
        >
          <div
            id="subcatHeader_1234"
          />
          <div />
        </div>
      </div>
    </div>
  </body>,
  "container": .emotion-0 {
  box-sizing: border-box;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-flex-wrap: wrap;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
}

.emotion-1 {
  box-sizing: border-box;
  -webkit-flex-basis: 100%;
  -ms-flex-preferred-size: 100%;
  flex-basis: 100%;
  -webkit-box-flex: 0;
  -webkit-flex-grow: 0;
  -ms-flex-positive: 0;
  flex-grow: 0;
  margin: 0;
  max-width: 100%;
  padding: 0em;
}

.emotion-2 {
  overflow: hidden;
  margin: 0;
  margin-bottom: 2rem;
  padding: 0;
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 700;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 1.067rem;
  color: #666;
  line-height: normal;
  text-align: center;
}

.emotion-3 {
  text-transform: uppercase;
  letter-spacing: 0.15em;
  display: inline-block;
  position: relative;
  padding: 0 0.5rem;
}

.emotion-3::before,
.emotion-3::after {
  border-top: 2px solid #666;
  display: block;
  position: absolute;
  width: 100em;
  top: 50%;
  content: '';
}

.emotion-3::before {
  right: 100%;
}

.emotion-3::after {
  left: 100%;
}

.emotion-4 {
  --columnSize: 1;
  box-sizing: border-box;
  -webkit-flex-basis: 50%;
  -ms-flex-preferred-size: 50%;
  flex-basis: 50%;
  -webkit-box-flex: 0;
  -webkit-flex-grow: 0;
  -ms-flex-positive: 0;
  flex-grow: 0;
  margin: 0;
  max-width: 50%;
  padding: 0.5em;
}

.emotion-5 {
  min-height: 340px;
  position: relative;
}

.emotion-5 .product-card__image {
  width: 100%;
  display: block;
}

.emotion-6 {
  margin-bottom: 0.5rem;
}

.emotion-8 {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  color: #666;
  line-height: 1.4;
  text-transform: none;
  font-weight: normal;
  font-size: 0.8rem;
}

.emotion-9 {
  text-transform: none;
  line-height: 1.4;
  color: #666;
  font-size: 0.8rem!important;
  letter-spacing: normal;
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-9 .product-price__markdown,
.emotion-9 .product-price--markdown {
  color: #666;
}

.emotion-9 .product-price__highlight,
.emotion-9 .product-price--highlight {
  text-transform: none;
  color: #D00000;
}

.emotion-9 .product-price__highlight priceHighlightFont,
.emotion-9 .product-price--highlight priceHighlightFont {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-9 .product-price__highlight--br,
.emotion-9 .product-price--highlight-br {
  text-transform: none;
  font-size: 0.8125rem;
}

.emotion-9 .product-price__markdown {
  text-transform: none;
  color: #666;
}

.emotion-9 .product-price__no-strike {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-9 .product-price__option {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 700;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-9 .product-price__option--br {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-9 .product-price__percentage-off {
  color: #D00000;
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-transform: none;
  padding-left: 5px;
}

.emotion-9 .product-price__percentage-off-no-padding {
  color: #D00000;
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-transform: none;
}

.emotion-9 .product-price__regular {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-9 .product-price__sale {
  color: #666;
}

.emotion-9 .product-price__strike {
  -webkit-text-decoration: line-through;
  text-decoration: line-through;
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-9 .product-price__same-line {
  padding-left: 5px;
}

.emotion-10 {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  color: #666;
  line-height: 1.4;
  text-transform: none;
  font-weight: bold;
  font-size: 0.8rem;
}

<div>
    <div
      class="emotion-0"
      data-testid="grid-root"
      spacing="0.5"
    >
      <div
        class="emotion-1"
        data-testid="grid-root"
        spacing="0"
      >
        <div
          id="subcatHeader_1234"
        />
        <div
          class="emotion-2"
        >
          <h2
            class="emotion-3"
          >
            test group
          </h2>
        </div>
      </div>
      <div
        class="emotion-4"
        data-testid="grid-root"
        spacing="0.5"
      >
        <div
          class="product-card emotion-5"
        >
          <div
            class="product-card__image-wrapper emotion-6"
            id="product123"
          >
            <a
              aria-hidden="false"
              class="emotion-7"
              href="http://localhost/browse/product.do?pid=123&cid=1234&pcid=23456&grid=source_0_4_1"
              target="_self"
            >
              <img
                alt="sherpa fleece hoodie"
                class="product-card__image"
                src="http://www3.assets-gap.com/webcontent/0013/882/196/cn13882196.jpg"
              />
            </a>
          </div>
          <a
            href="http://localhost/browse/product.do?pid=123&cid=1234&pcid=23456&grid=source_0_4_1"
          >
            <div
              class="emotion-8"
            >
              Sherpa fleece hoodie
            </div>
          </a>
          <div>
            <div
              class="product-card-price"
            >
              <div
                class="emotion-9"
              >
                <div>
                  <div
                    class="product-price__markdown"
                  >
                    <span
                      aria-label="price.regular_price_aria_label"
                      class="product-price__no-strike"
                      role="text"
                    >
                      $69.95
                    </span>
                  </div>
                  <div
                    aria-label="price.current_price_range_aria_label"
                    class="product-price__highlight"
                    role="text"
                  >
                    <span
                      class="product-price__highlight"
                    >
                      $49.99
                       - 
                      $54.99
                    </span>
                  </div>
                </div>
              </div>
            </div>
            <div
              class="product-card__marketing-flag emotion-10"
            >
              Final Sale
            </div>
          </div>
        </div>
      </div>
      <div
        class="emotion-4"
        data-testid="grid-root"
        spacing="0.5"
      >
        <div
          class="product-card emotion-5"
        >
          <div
            class="product-card__image-wrapper emotion-6"
            id="product123"
          >
            <a
              aria-hidden="false"
              class="emotion-7"
              href="http://localhost/browse/product.do?pid=123&cid=1234&pcid=23456&grid=source_1_4_1"
              target="_self"
            >
              <img
                alt="sherpa fleece hoodie"
                class="product-card__image"
                src="http://www3.assets-gap.com/webcontent/0013/882/196/cn13882196.jpg"
              />
            </a>
          </div>
          <a
            href="http://localhost/browse/product.do?pid=123&cid=1234&pcid=23456&grid=source_1_4_1"
          >
            <div
              class="emotion-8"
            >
              Sherpa fleece hoodie
            </div>
          </a>
          <div>
            <div
              class="product-card-price"
            >
              <div
                class="emotion-9"
              >
                <div>
                  <div
                    class="product-price__markdown"
                  >
                    <span
                      aria-label="price.regular_price_aria_label"
                      class="product-price__no-strike"
                      role="text"
                    >
                      $69.95
                    </span>
                  </div>
                  <div
                    aria-label="price.current_price_range_aria_label"
                    class="product-price__highlight"
                    role="text"
                  >
                    <span
                      class="product-price__highlight"
                    >
                      $49.99
                       - 
                      $54.99
                    </span>
                  </div>
                </div>
              </div>
            </div>
            <div
              class="product-card__marketing-flag emotion-10"
            >
              Final Sale
            </div>
          </div>
        </div>
      </div>
      <div
        class="emotion-4"
        data-testid="grid-root"
        spacing="0.5"
      >
        <div
          class="product-card emotion-5"
        >
          <div
            class="product-card__image-wrapper emotion-6"
            id="product123"
          >
            <a
              aria-hidden="false"
              class="emotion-7"
              href="http://localhost/browse/product.do?pid=123&cid=1234&pcid=23456&grid=source_2_4_1"
              target="_self"
            >
              <img
                alt="sherpa fleece hoodie"
                class="product-card__image"
                src="http://www3.assets-gap.com/webcontent/0013/882/196/cn13882196.jpg"
              />
            </a>
          </div>
          <a
            href="http://localhost/browse/product.do?pid=123&cid=1234&pcid=23456&grid=source_2_4_1"
          >
            <div
              class="emotion-8"
            >
              Sherpa fleece hoodie
            </div>
          </a>
          <div>
            <div
              class="product-card-price"
            >
              <div
                class="emotion-9"
              >
                <div>
                  <div
                    class="product-price__markdown"
                  >
                    <span
                      aria-label="price.regular_price_aria_label"
                      class="product-price__no-strike"
                      role="text"
                    >
                      $69.95
                    </span>
                  </div>
                  <div
                    aria-label="price.current_price_range_aria_label"
                    class="product-price__highlight"
                    role="text"
                  >
                    <span
                      class="product-price__highlight"
                    >
                      $49.99
                       - 
                      $54.99
                    </span>
                  </div>
                </div>
              </div>
            </div>
            <div
              class="product-card__marketing-flag emotion-10"
            >
              Final Sale
            </div>
          </div>
        </div>
      </div>
      <div
        class="emotion-4"
        data-testid="grid-root"
        spacing="0.5"
      >
        <div
          class="product-card emotion-5"
        >
          <div
            class="product-card__image-wrapper emotion-6"
            id="product124"
          >
            <a
              aria-hidden="false"
              class="emotion-7"
              href="http://localhost/browse/product.do?pid=124&cid=1234&pcid=23456&grid=source_3_4_1"
              target="_self"
            >
              <img
                alt="Indigo zip hoodie"
                class="product-card__image"
                src="http://www.gap.com/webcontent/0013/707/658/cn13707658.jpg"
              />
            </a>
          </div>
          <a
            href="http://localhost/browse/product.do?pid=124&cid=1234&pcid=23456&grid=source_3_4_1"
          >
            <div
              class="emotion-8"
            >
              Indigo zip hoodie
            </div>
          </a>
          <div>
            <div
              class="product-card-price"
            >
              <div
                class="emotion-9"
              >
                <div>
                  <div
                    class="product-price__markdown"
                  >
                    <span
                      aria-label="price.regular_price_aria_label"
                      class="product-price__no-strike"
                      role="text"
                    >
                      $69.95
                    </span>
                  </div>
                  <div
                    aria-label="price.current_price_range_aria_label"
                    class="product-price__highlight"
                    role="text"
                  >
                    <span
                      class="product-price__highlight"
                    >
                      $49.99
                       - 
                      $54.99
                    </span>
                  </div>
                </div>
              </div>
            </div>
            <div
              class="product-card__marketing-flag emotion-10"
            >
              Final Sale
            </div>
          </div>
        </div>
      </div>
      <div
        class="emotion-1"
        data-testid="grid-root"
        spacing="0"
      >
        <div
          id="subcatHeader_1234"
        />
        <div />
      </div>
    </div>
  </div>,
  "debug": [Function],
  "findAllByAltText": [Function],
  "findAllByDisplayValue": [Function],
  "findAllByLabelText": [Function],
  "findAllByPlaceholderText": [Function],
  "findAllByRole": [Function],
  "findAllByTestId": [Function],
  "findAllByText": [Function],
  "findAllByTitle": [Function],
  "findByAltText": [Function],
  "findByDisplayValue": [Function],
  "findByLabelText": [Function],
  "findByPlaceholderText": [Function],
  "findByRole": [Function],
  "findByTestId": [Function],
  "findByText": [Function],
  "findByTitle": [Function],
  "getAllByAltText": [Function],
  "getAllByDisplayValue": [Function],
  "getAllByLabelText": [Function],
  "getAllByPlaceholderText": [Function],
  "getAllByRole": [Function],
  "getAllByTestId": [Function],
  "getAllByText": [Function],
  "getAllByTitle": [Function],
  "getByAltText": [Function],
  "getByDisplayValue": [Function],
  "getByLabelText": [Function],
  "getByPlaceholderText": [Function],
  "getByRole": [Function],
  "getByTestId": [Function],
  "getByText": [Function],
  "getByTitle": [Function],
  "queryAllByAltText": [Function],
  "queryAllByDisplayValue": [Function],
  "queryAllByLabelText": [Function],
  "queryAllByPlaceholderText": [Function],
  "queryAllByRole": [Function],
  "queryAllByTestId": [Function],
  "queryAllByText": [Function],
  "queryAllByTitle": [Function],
  "queryByAltText": [Function],
  "queryByDisplayValue": [Function],
  "queryByLabelText": [Function],
  "queryByPlaceholderText": [Function],
  "queryByRole": [Function],
  "queryByTestId": [Function],
  "queryByText": [Function],
  "queryByTitle": [Function],
  "rerender": [Function],
  "unmount": [Function],
}
`;

exports[`<ProductGroup /> ISM container renders ISM container divs correctly in Tablet 1`] = `
{
  "asFragment": [Function],
  "baseElement": .emotion-0 {
  box-sizing: border-box;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-flex-wrap: wrap;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
}

.emotion-1 {
  box-sizing: border-box;
  -webkit-flex-basis: 100%;
  -ms-flex-preferred-size: 100%;
  flex-basis: 100%;
  -webkit-box-flex: 0;
  -webkit-flex-grow: 0;
  -ms-flex-positive: 0;
  flex-grow: 0;
  margin: 0;
  max-width: 100%;
  padding: 0em;
}

.emotion-2 {
  overflow: hidden;
  margin: 0;
  margin-bottom: 2rem;
  padding: 0;
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 700;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 1.067rem;
  color: #666;
  line-height: normal;
  text-align: center;
}

.emotion-3 {
  text-transform: uppercase;
  letter-spacing: 0.15em;
  display: inline-block;
  position: relative;
  padding: 0 0.5rem;
}

.emotion-3::before,
.emotion-3::after {
  border-top: 2px solid #666;
  display: block;
  position: absolute;
  width: 100em;
  top: 50%;
  content: '';
}

.emotion-3::before {
  right: 100%;
}

.emotion-3::after {
  left: 100%;
}

.emotion-4 {
  --columnSize: 1;
  box-sizing: border-box;
  -webkit-flex-basis: 33.333333333333336%;
  -ms-flex-preferred-size: 33.333333333333336%;
  flex-basis: 33.333333333333336%;
  -webkit-box-flex: 0;
  -webkit-flex-grow: 0;
  -ms-flex-positive: 0;
  flex-grow: 0;
  margin: 0;
  max-width: 33.333333333333336%;
  padding: 0.5em;
}

.emotion-5 {
  min-height: 430px;
  position: relative;
}

.emotion-5 .product-card__image {
  width: 100%;
  display: block;
}

.emotion-6 {
  margin-bottom: 0.5rem;
}

.emotion-8 {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  color: #666;
  line-height: 1.4;
  text-transform: none;
  font-weight: normal;
  font-size: 0.8rem;
}

.emotion-9 {
  text-transform: none;
  line-height: 1.4;
  color: #666;
  font-size: 0.8rem!important;
  letter-spacing: normal;
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-9 .product-price__markdown,
.emotion-9 .product-price--markdown {
  color: #666;
}

.emotion-9 .product-price__highlight,
.emotion-9 .product-price--highlight {
  text-transform: none;
  color: #D00000;
}

.emotion-9 .product-price__highlight priceHighlightFont,
.emotion-9 .product-price--highlight priceHighlightFont {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-9 .product-price__highlight--br,
.emotion-9 .product-price--highlight-br {
  text-transform: none;
  font-size: 0.8125rem;
}

.emotion-9 .product-price__markdown {
  text-transform: none;
  color: #666;
}

.emotion-9 .product-price__no-strike {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-9 .product-price__option {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 700;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-9 .product-price__option--br {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-9 .product-price__percentage-off {
  color: #D00000;
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-transform: none;
  padding-left: 5px;
}

.emotion-9 .product-price__percentage-off-no-padding {
  color: #D00000;
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-transform: none;
}

.emotion-9 .product-price__regular {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-9 .product-price__sale {
  color: #666;
}

.emotion-9 .product-price__strike {
  -webkit-text-decoration: line-through;
  text-decoration: line-through;
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-9 .product-price__same-line {
  padding-left: 5px;
}

.emotion-10 {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  color: #666;
  line-height: 1.4;
  text-transform: none;
  font-weight: bold;
  font-size: 0.8rem;
}

<body>
    <div>
      <div
        class="emotion-0"
        data-testid="grid-root"
        spacing="0.5"
      >
        <div
          class="emotion-1"
          data-testid="grid-root"
          spacing="0"
        >
          <div
            id="subcatHeader_1234"
          />
          <div
            class="emotion-2"
          >
            <h2
              class="emotion-3"
            >
              test group
            </h2>
          </div>
        </div>
        <div
          class="emotion-4"
          data-testid="grid-root"
          spacing="0.5"
        >
          <div
            class="product-card emotion-5"
          >
            <div
              class="product-card__image-wrapper emotion-6"
              id="product123"
            >
              <a
                aria-hidden="false"
                class="emotion-7"
                href="http://localhost/browse/product.do?pid=123&cid=1234&pcid=23456&grid=source_0_4_1"
                target="_self"
              >
                <img
                  alt="sherpa fleece hoodie"
                  class="product-card__image"
                  src="http://www3.assets-gap.com/webcontent/0013/882/196/cn13882196.jpg"
                />
              </a>
            </div>
            <a
              href="http://localhost/browse/product.do?pid=123&cid=1234&pcid=23456&grid=source_0_4_1"
            >
              <div
                class="emotion-8"
              >
                Sherpa fleece hoodie
              </div>
            </a>
            <div>
              <div
                class="product-card-price"
              >
                <div
                  class="emotion-9"
                >
                  <div>
                    <div
                      class="product-price__markdown"
                    >
                      <span
                        aria-label="price.regular_price_aria_label"
                        class="product-price__no-strike"
                        role="text"
                      >
                        $69.95
                      </span>
                    </div>
                    <div
                      aria-label="price.current_price_range_aria_label"
                      class="product-price__highlight"
                      role="text"
                    >
                      <span
                        class="product-price__highlight"
                      >
                        $49.99
                         - 
                        $54.99
                      </span>
                    </div>
                  </div>
                </div>
              </div>
              <div
                class="product-card__marketing-flag emotion-10"
              >
                Final Sale
              </div>
            </div>
          </div>
        </div>
        <div
          class="emotion-4"
          data-testid="grid-root"
          spacing="0.5"
        >
          <div
            class="product-card emotion-5"
          >
            <div
              class="product-card__image-wrapper emotion-6"
              id="product123"
            >
              <a
                aria-hidden="false"
                class="emotion-7"
                href="http://localhost/browse/product.do?pid=123&cid=1234&pcid=23456&grid=source_1_4_1"
                target="_self"
              >
                <img
                  alt="sherpa fleece hoodie"
                  class="product-card__image"
                  src="http://www3.assets-gap.com/webcontent/0013/882/196/cn13882196.jpg"
                />
              </a>
            </div>
            <a
              href="http://localhost/browse/product.do?pid=123&cid=1234&pcid=23456&grid=source_1_4_1"
            >
              <div
                class="emotion-8"
              >
                Sherpa fleece hoodie
              </div>
            </a>
            <div>
              <div
                class="product-card-price"
              >
                <div
                  class="emotion-9"
                >
                  <div>
                    <div
                      class="product-price__markdown"
                    >
                      <span
                        aria-label="price.regular_price_aria_label"
                        class="product-price__no-strike"
                        role="text"
                      >
                        $69.95
                      </span>
                    </div>
                    <div
                      aria-label="price.current_price_range_aria_label"
                      class="product-price__highlight"
                      role="text"
                    >
                      <span
                        class="product-price__highlight"
                      >
                        $49.99
                         - 
                        $54.99
                      </span>
                    </div>
                  </div>
                </div>
              </div>
              <div
                class="product-card__marketing-flag emotion-10"
              >
                Final Sale
              </div>
            </div>
          </div>
        </div>
        <div
          class="emotion-4"
          data-testid="grid-root"
          spacing="0.5"
        >
          <div
            class="product-card emotion-5"
          >
            <div
              class="product-card__image-wrapper emotion-6"
              id="product123"
            >
              <a
                aria-hidden="false"
                class="emotion-7"
                href="http://localhost/browse/product.do?pid=123&cid=1234&pcid=23456&grid=source_2_4_1"
                target="_self"
              >
                <img
                  alt="sherpa fleece hoodie"
                  class="product-card__image"
                  src="http://www3.assets-gap.com/webcontent/0013/882/196/cn13882196.jpg"
                />
              </a>
            </div>
            <a
              href="http://localhost/browse/product.do?pid=123&cid=1234&pcid=23456&grid=source_2_4_1"
            >
              <div
                class="emotion-8"
              >
                Sherpa fleece hoodie
              </div>
            </a>
            <div>
              <div
                class="product-card-price"
              >
                <div
                  class="emotion-9"
                >
                  <div>
                    <div
                      class="product-price__markdown"
                    >
                      <span
                        aria-label="price.regular_price_aria_label"
                        class="product-price__no-strike"
                        role="text"
                      >
                        $69.95
                      </span>
                    </div>
                    <div
                      aria-label="price.current_price_range_aria_label"
                      class="product-price__highlight"
                      role="text"
                    >
                      <span
                        class="product-price__highlight"
                      >
                        $49.99
                         - 
                        $54.99
                      </span>
                    </div>
                  </div>
                </div>
              </div>
              <div
                class="product-card__marketing-flag emotion-10"
              >
                Final Sale
              </div>
            </div>
          </div>
        </div>
        <div
          class="emotion-4"
          data-testid="grid-root"
          spacing="0.5"
        >
          <div
            class="product-card emotion-5"
          >
            <div
              class="product-card__image-wrapper emotion-6"
              id="product124"
            >
              <a
                aria-hidden="false"
                class="emotion-7"
                href="http://localhost/browse/product.do?pid=124&cid=1234&pcid=23456&grid=source_3_4_1"
                target="_self"
              >
                <img
                  alt="Indigo zip hoodie"
                  class="product-card__image"
                  src="http://www.gap.com/webcontent/0013/707/658/cn13707658.jpg"
                />
              </a>
            </div>
            <a
              href="http://localhost/browse/product.do?pid=124&cid=1234&pcid=23456&grid=source_3_4_1"
            >
              <div
                class="emotion-8"
              >
                Indigo zip hoodie
              </div>
            </a>
            <div>
              <div
                class="product-card-price"
              >
                <div
                  class="emotion-9"
                >
                  <div>
                    <div
                      class="product-price__markdown"
                    >
                      <span
                        aria-label="price.regular_price_aria_label"
                        class="product-price__no-strike"
                        role="text"
                      >
                        $69.95
                      </span>
                    </div>
                    <div
                      aria-label="price.current_price_range_aria_label"
                      class="product-price__highlight"
                      role="text"
                    >
                      <span
                        class="product-price__highlight"
                      >
                        $49.99
                         - 
                        $54.99
                      </span>
                    </div>
                  </div>
                </div>
              </div>
              <div
                class="product-card__marketing-flag emotion-10"
              >
                Final Sale
              </div>
            </div>
          </div>
        </div>
        <div
          class="emotion-1"
          data-testid="grid-root"
          spacing="0"
        >
          <div
            id="subcatHeader_1234"
          />
          <div />
        </div>
      </div>
    </div>
  </body>,
  "container": .emotion-0 {
  box-sizing: border-box;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-flex-wrap: wrap;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
}

.emotion-1 {
  box-sizing: border-box;
  -webkit-flex-basis: 100%;
  -ms-flex-preferred-size: 100%;
  flex-basis: 100%;
  -webkit-box-flex: 0;
  -webkit-flex-grow: 0;
  -ms-flex-positive: 0;
  flex-grow: 0;
  margin: 0;
  max-width: 100%;
  padding: 0em;
}

.emotion-2 {
  overflow: hidden;
  margin: 0;
  margin-bottom: 2rem;
  padding: 0;
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 700;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 1.067rem;
  color: #666;
  line-height: normal;
  text-align: center;
}

.emotion-3 {
  text-transform: uppercase;
  letter-spacing: 0.15em;
  display: inline-block;
  position: relative;
  padding: 0 0.5rem;
}

.emotion-3::before,
.emotion-3::after {
  border-top: 2px solid #666;
  display: block;
  position: absolute;
  width: 100em;
  top: 50%;
  content: '';
}

.emotion-3::before {
  right: 100%;
}

.emotion-3::after {
  left: 100%;
}

.emotion-4 {
  --columnSize: 1;
  box-sizing: border-box;
  -webkit-flex-basis: 33.333333333333336%;
  -ms-flex-preferred-size: 33.333333333333336%;
  flex-basis: 33.333333333333336%;
  -webkit-box-flex: 0;
  -webkit-flex-grow: 0;
  -ms-flex-positive: 0;
  flex-grow: 0;
  margin: 0;
  max-width: 33.333333333333336%;
  padding: 0.5em;
}

.emotion-5 {
  min-height: 430px;
  position: relative;
}

.emotion-5 .product-card__image {
  width: 100%;
  display: block;
}

.emotion-6 {
  margin-bottom: 0.5rem;
}

.emotion-8 {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  color: #666;
  line-height: 1.4;
  text-transform: none;
  font-weight: normal;
  font-size: 0.8rem;
}

.emotion-9 {
  text-transform: none;
  line-height: 1.4;
  color: #666;
  font-size: 0.8rem!important;
  letter-spacing: normal;
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-9 .product-price__markdown,
.emotion-9 .product-price--markdown {
  color: #666;
}

.emotion-9 .product-price__highlight,
.emotion-9 .product-price--highlight {
  text-transform: none;
  color: #D00000;
}

.emotion-9 .product-price__highlight priceHighlightFont,
.emotion-9 .product-price--highlight priceHighlightFont {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-9 .product-price__highlight--br,
.emotion-9 .product-price--highlight-br {
  text-transform: none;
  font-size: 0.8125rem;
}

.emotion-9 .product-price__markdown {
  text-transform: none;
  color: #666;
}

.emotion-9 .product-price__no-strike {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-9 .product-price__option {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 700;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-9 .product-price__option--br {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-9 .product-price__percentage-off {
  color: #D00000;
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-transform: none;
  padding-left: 5px;
}

.emotion-9 .product-price__percentage-off-no-padding {
  color: #D00000;
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-transform: none;
}

.emotion-9 .product-price__regular {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-9 .product-price__sale {
  color: #666;
}

.emotion-9 .product-price__strike {
  -webkit-text-decoration: line-through;
  text-decoration: line-through;
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-9 .product-price__same-line {
  padding-left: 5px;
}

.emotion-10 {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  color: #666;
  line-height: 1.4;
  text-transform: none;
  font-weight: bold;
  font-size: 0.8rem;
}

<div>
    <div
      class="emotion-0"
      data-testid="grid-root"
      spacing="0.5"
    >
      <div
        class="emotion-1"
        data-testid="grid-root"
        spacing="0"
      >
        <div
          id="subcatHeader_1234"
        />
        <div
          class="emotion-2"
        >
          <h2
            class="emotion-3"
          >
            test group
          </h2>
        </div>
      </div>
      <div
        class="emotion-4"
        data-testid="grid-root"
        spacing="0.5"
      >
        <div
          class="product-card emotion-5"
        >
          <div
            class="product-card__image-wrapper emotion-6"
            id="product123"
          >
            <a
              aria-hidden="false"
              class="emotion-7"
              href="http://localhost/browse/product.do?pid=123&cid=1234&pcid=23456&grid=source_0_4_1"
              target="_self"
            >
              <img
                alt="sherpa fleece hoodie"
                class="product-card__image"
                src="http://www3.assets-gap.com/webcontent/0013/882/196/cn13882196.jpg"
              />
            </a>
          </div>
          <a
            href="http://localhost/browse/product.do?pid=123&cid=1234&pcid=23456&grid=source_0_4_1"
          >
            <div
              class="emotion-8"
            >
              Sherpa fleece hoodie
            </div>
          </a>
          <div>
            <div
              class="product-card-price"
            >
              <div
                class="emotion-9"
              >
                <div>
                  <div
                    class="product-price__markdown"
                  >
                    <span
                      aria-label="price.regular_price_aria_label"
                      class="product-price__no-strike"
                      role="text"
                    >
                      $69.95
                    </span>
                  </div>
                  <div
                    aria-label="price.current_price_range_aria_label"
                    class="product-price__highlight"
                    role="text"
                  >
                    <span
                      class="product-price__highlight"
                    >
                      $49.99
                       - 
                      $54.99
                    </span>
                  </div>
                </div>
              </div>
            </div>
            <div
              class="product-card__marketing-flag emotion-10"
            >
              Final Sale
            </div>
          </div>
        </div>
      </div>
      <div
        class="emotion-4"
        data-testid="grid-root"
        spacing="0.5"
      >
        <div
          class="product-card emotion-5"
        >
          <div
            class="product-card__image-wrapper emotion-6"
            id="product123"
          >
            <a
              aria-hidden="false"
              class="emotion-7"
              href="http://localhost/browse/product.do?pid=123&cid=1234&pcid=23456&grid=source_1_4_1"
              target="_self"
            >
              <img
                alt="sherpa fleece hoodie"
                class="product-card__image"
                src="http://www3.assets-gap.com/webcontent/0013/882/196/cn13882196.jpg"
              />
            </a>
          </div>
          <a
            href="http://localhost/browse/product.do?pid=123&cid=1234&pcid=23456&grid=source_1_4_1"
          >
            <div
              class="emotion-8"
            >
              Sherpa fleece hoodie
            </div>
          </a>
          <div>
            <div
              class="product-card-price"
            >
              <div
                class="emotion-9"
              >
                <div>
                  <div
                    class="product-price__markdown"
                  >
                    <span
                      aria-label="price.regular_price_aria_label"
                      class="product-price__no-strike"
                      role="text"
                    >
                      $69.95
                    </span>
                  </div>
                  <div
                    aria-label="price.current_price_range_aria_label"
                    class="product-price__highlight"
                    role="text"
                  >
                    <span
                      class="product-price__highlight"
                    >
                      $49.99
                       - 
                      $54.99
                    </span>
                  </div>
                </div>
              </div>
            </div>
            <div
              class="product-card__marketing-flag emotion-10"
            >
              Final Sale
            </div>
          </div>
        </div>
      </div>
      <div
        class="emotion-4"
        data-testid="grid-root"
        spacing="0.5"
      >
        <div
          class="product-card emotion-5"
        >
          <div
            class="product-card__image-wrapper emotion-6"
            id="product123"
          >
            <a
              aria-hidden="false"
              class="emotion-7"
              href="http://localhost/browse/product.do?pid=123&cid=1234&pcid=23456&grid=source_2_4_1"
              target="_self"
            >
              <img
                alt="sherpa fleece hoodie"
                class="product-card__image"
                src="http://www3.assets-gap.com/webcontent/0013/882/196/cn13882196.jpg"
              />
            </a>
          </div>
          <a
            href="http://localhost/browse/product.do?pid=123&cid=1234&pcid=23456&grid=source_2_4_1"
          >
            <div
              class="emotion-8"
            >
              Sherpa fleece hoodie
            </div>
          </a>
          <div>
            <div
              class="product-card-price"
            >
              <div
                class="emotion-9"
              >
                <div>
                  <div
                    class="product-price__markdown"
                  >
                    <span
                      aria-label="price.regular_price_aria_label"
                      class="product-price__no-strike"
                      role="text"
                    >
                      $69.95
                    </span>
                  </div>
                  <div
                    aria-label="price.current_price_range_aria_label"
                    class="product-price__highlight"
                    role="text"
                  >
                    <span
                      class="product-price__highlight"
                    >
                      $49.99
                       - 
                      $54.99
                    </span>
                  </div>
                </div>
              </div>
            </div>
            <div
              class="product-card__marketing-flag emotion-10"
            >
              Final Sale
            </div>
          </div>
        </div>
      </div>
      <div
        class="emotion-4"
        data-testid="grid-root"
        spacing="0.5"
      >
        <div
          class="product-card emotion-5"
        >
          <div
            class="product-card__image-wrapper emotion-6"
            id="product124"
          >
            <a
              aria-hidden="false"
              class="emotion-7"
              href="http://localhost/browse/product.do?pid=124&cid=1234&pcid=23456&grid=source_3_4_1"
              target="_self"
            >
              <img
                alt="Indigo zip hoodie"
                class="product-card__image"
                src="http://www.gap.com/webcontent/0013/707/658/cn13707658.jpg"
              />
            </a>
          </div>
          <a
            href="http://localhost/browse/product.do?pid=124&cid=1234&pcid=23456&grid=source_3_4_1"
          >
            <div
              class="emotion-8"
            >
              Indigo zip hoodie
            </div>
          </a>
          <div>
            <div
              class="product-card-price"
            >
              <div
                class="emotion-9"
              >
                <div>
                  <div
                    class="product-price__markdown"
                  >
                    <span
                      aria-label="price.regular_price_aria_label"
                      class="product-price__no-strike"
                      role="text"
                    >
                      $69.95
                    </span>
                  </div>
                  <div
                    aria-label="price.current_price_range_aria_label"
                    class="product-price__highlight"
                    role="text"
                  >
                    <span
                      class="product-price__highlight"
                    >
                      $49.99
                       - 
                      $54.99
                    </span>
                  </div>
                </div>
              </div>
            </div>
            <div
              class="product-card__marketing-flag emotion-10"
            >
              Final Sale
            </div>
          </div>
        </div>
      </div>
      <div
        class="emotion-1"
        data-testid="grid-root"
        spacing="0"
      >
        <div
          id="subcatHeader_1234"
        />
        <div />
      </div>
    </div>
  </div>,
  "debug": [Function],
  "findAllByAltText": [Function],
  "findAllByDisplayValue": [Function],
  "findAllByLabelText": [Function],
  "findAllByPlaceholderText": [Function],
  "findAllByRole": [Function],
  "findAllByTestId": [Function],
  "findAllByText": [Function],
  "findAllByTitle": [Function],
  "findByAltText": [Function],
  "findByDisplayValue": [Function],
  "findByLabelText": [Function],
  "findByPlaceholderText": [Function],
  "findByRole": [Function],
  "findByTestId": [Function],
  "findByText": [Function],
  "findByTitle": [Function],
  "getAllByAltText": [Function],
  "getAllByDisplayValue": [Function],
  "getAllByLabelText": [Function],
  "getAllByPlaceholderText": [Function],
  "getAllByRole": [Function],
  "getAllByTestId": [Function],
  "getAllByText": [Function],
  "getAllByTitle": [Function],
  "getByAltText": [Function],
  "getByDisplayValue": [Function],
  "getByLabelText": [Function],
  "getByPlaceholderText": [Function],
  "getByRole": [Function],
  "getByTestId": [Function],
  "getByText": [Function],
  "getByTitle": [Function],
  "queryAllByAltText": [Function],
  "queryAllByDisplayValue": [Function],
  "queryAllByLabelText": [Function],
  "queryAllByPlaceholderText": [Function],
  "queryAllByRole": [Function],
  "queryAllByTestId": [Function],
  "queryAllByText": [Function],
  "queryAllByTitle": [Function],
  "queryByAltText": [Function],
  "queryByDisplayValue": [Function],
  "queryByLabelText": [Function],
  "queryByPlaceholderText": [Function],
  "queryByRole": [Function],
  "queryByTestId": [Function],
  "queryByText": [Function],
  "queryByTitle": [Function],
  "rerender": [Function],
  "unmount": [Function],
}
`;

exports[`<ProductGroup /> ISM container should render PageMarketing component when the object is an ISM on productMaps 1`] = `
{
  "asFragment": [Function],
  "baseElement": .emotion-0 {
  box-sizing: border-box;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-flex-wrap: wrap;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
}

.emotion-1 {
  box-sizing: border-box;
  -webkit-flex-basis: 100%;
  -ms-flex-preferred-size: 100%;
  flex-basis: 100%;
  -webkit-box-flex: 0;
  -webkit-flex-grow: 0;
  -ms-flex-positive: 0;
  flex-grow: 0;
  margin: 0;
  max-width: 100%;
  padding: 0em;
}

.emotion-2 {
  overflow: hidden;
  margin: 0;
  margin-bottom: 2rem;
  padding: 0;
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 700;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 1.067rem;
  color: #666;
  line-height: normal;
  text-align: center;
}

.emotion-3 {
  text-transform: uppercase;
  letter-spacing: 0.15em;
  display: inline-block;
  position: relative;
  padding: 0 0.5rem;
}

.emotion-3::before,
.emotion-3::after {
  border-top: 2px solid #666;
  display: block;
  position: absolute;
  width: 100em;
  top: 50%;
  content: '';
}

.emotion-3::before {
  right: 100%;
}

.emotion-3::after {
  left: 100%;
}

.emotion-4 {
  --columnSize: 1;
  box-sizing: border-box;
  -webkit-flex-basis: 33.333333333333336%;
  -ms-flex-preferred-size: 33.333333333333336%;
  flex-basis: 33.333333333333336%;
  -webkit-box-flex: 0;
  -webkit-flex-grow: 0;
  -ms-flex-positive: 0;
  flex-grow: 0;
  margin: 0;
  max-width: 33.333333333333336%;
  padding: 0.5em;
}

.emotion-6 {
  min-height: 430px;
  position: relative;
}

.emotion-6 .product-card__image {
  width: 100%;
  display: block;
}

.emotion-7 {
  margin-bottom: 0.5rem;
}

.emotion-9 {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  color: #666;
  line-height: 1.4;
  text-transform: none;
  font-weight: normal;
  font-size: 0.8rem;
}

.emotion-10 {
  text-transform: none;
  line-height: 1.4;
  color: #666;
  font-size: 0.8rem!important;
  letter-spacing: normal;
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-10 .product-price__markdown,
.emotion-10 .product-price--markdown {
  color: #666;
}

.emotion-10 .product-price__highlight,
.emotion-10 .product-price--highlight {
  text-transform: none;
  color: #D00000;
}

.emotion-10 .product-price__highlight priceHighlightFont,
.emotion-10 .product-price--highlight priceHighlightFont {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-10 .product-price__highlight--br,
.emotion-10 .product-price--highlight-br {
  text-transform: none;
  font-size: 0.8125rem;
}

.emotion-10 .product-price__markdown {
  text-transform: none;
  color: #666;
}

.emotion-10 .product-price__no-strike {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-10 .product-price__option {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 700;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-10 .product-price__option--br {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-10 .product-price__percentage-off {
  color: #D00000;
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-transform: none;
  padding-left: 5px;
}

.emotion-10 .product-price__percentage-off-no-padding {
  color: #D00000;
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-transform: none;
}

.emotion-10 .product-price__regular {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-10 .product-price__sale {
  color: #666;
}

.emotion-10 .product-price__strike {
  -webkit-text-decoration: line-through;
  text-decoration: line-through;
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-10 .product-price__same-line {
  padding-left: 5px;
}

.emotion-11 {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  color: #666;
  line-height: 1.4;
  text-transform: none;
  font-weight: bold;
  font-size: 0.8rem;
}

<body>
    <div>
      <div
        class="emotion-0"
        data-testid="grid-root"
        spacing="0.5"
      >
        <div
          class="emotion-1"
          data-testid="grid-root"
          spacing="0"
        >
          <div
            id="subcatHeader_1234"
          />
          <div
            class="emotion-2"
          >
            <h2
              class="emotion-3"
            >
              test group
            </h2>
          </div>
        </div>
        <div
          class="emotion-4"
          data-testid="grid-root"
          spacing="0.5"
        />
        <div
          class="emotion-4"
          data-testid="grid-root"
          spacing="0.5"
        >
          <div
            class="product-card emotion-6"
          >
            <div
              class="product-card__image-wrapper emotion-7"
              id="product123"
            >
              <a
                aria-hidden="false"
                class="emotion-8"
                href="http://localhost/browse/product.do?pid=123&cid=1234&pcid=23456&grid=source_1_4_1"
                target="_self"
              >
                <img
                  alt="sherpa fleece hoodie"
                  class="product-card__image"
                  src="http://www3.assets-gap.com/webcontent/0013/882/196/cn13882196.jpg"
                />
              </a>
            </div>
            <a
              href="http://localhost/browse/product.do?pid=123&cid=1234&pcid=23456&grid=source_1_4_1"
            >
              <div
                class="emotion-9"
              >
                Sherpa fleece hoodie
              </div>
            </a>
            <div>
              <div
                class="product-card-price"
              >
                <div
                  class="emotion-10"
                >
                  <div>
                    <div
                      class="product-price__markdown"
                    >
                      <span
                        aria-label="price.regular_price_aria_label"
                        class="product-price__no-strike"
                        role="text"
                      >
                        $69.95
                      </span>
                    </div>
                    <div
                      aria-label="price.current_price_range_aria_label"
                      class="product-price__highlight"
                      role="text"
                    >
                      <span
                        class="product-price__highlight"
                      >
                        $49.99
                         - 
                        $54.99
                      </span>
                    </div>
                  </div>
                </div>
              </div>
              <div
                class="product-card__marketing-flag emotion-11"
              >
                Final Sale
              </div>
            </div>
          </div>
        </div>
        <div
          class="emotion-4"
          data-testid="grid-root"
          spacing="0.5"
        >
          <div
            class="product-card emotion-6"
          >
            <div
              class="product-card__image-wrapper emotion-7"
              id="product123"
            >
              <a
                aria-hidden="false"
                class="emotion-8"
                href="http://localhost/browse/product.do?pid=123&cid=1234&pcid=23456&grid=source_2_4_1"
                target="_self"
              >
                <img
                  alt="sherpa fleece hoodie"
                  class="product-card__image"
                  src="http://www3.assets-gap.com/webcontent/0013/882/196/cn13882196.jpg"
                />
              </a>
            </div>
            <a
              href="http://localhost/browse/product.do?pid=123&cid=1234&pcid=23456&grid=source_2_4_1"
            >
              <div
                class="emotion-9"
              >
                Sherpa fleece hoodie
              </div>
            </a>
            <div>
              <div
                class="product-card-price"
              >
                <div
                  class="emotion-10"
                >
                  <div>
                    <div
                      class="product-price__markdown"
                    >
                      <span
                        aria-label="price.regular_price_aria_label"
                        class="product-price__no-strike"
                        role="text"
                      >
                        $69.95
                      </span>
                    </div>
                    <div
                      aria-label="price.current_price_range_aria_label"
                      class="product-price__highlight"
                      role="text"
                    >
                      <span
                        class="product-price__highlight"
                      >
                        $49.99
                         - 
                        $54.99
                      </span>
                    </div>
                  </div>
                </div>
              </div>
              <div
                class="product-card__marketing-flag emotion-11"
              >
                Final Sale
              </div>
            </div>
          </div>
        </div>
        <div
          class="emotion-4"
          data-testid="grid-root"
          spacing="0.5"
        >
          <div
            class="product-card emotion-6"
          >
            <div
              class="product-card__image-wrapper emotion-7"
              id="product123"
            >
              <a
                aria-hidden="false"
                class="emotion-8"
                href="http://localhost/browse/product.do?pid=123&cid=1234&pcid=23456&grid=source_3_4_1"
                target="_self"
              >
                <img
                  alt="sherpa fleece hoodie"
                  class="product-card__image"
                  src="http://www3.assets-gap.com/webcontent/0013/882/196/cn13882196.jpg"
                />
              </a>
            </div>
            <a
              href="http://localhost/browse/product.do?pid=123&cid=1234&pcid=23456&grid=source_3_4_1"
            >
              <div
                class="emotion-9"
              >
                Sherpa fleece hoodie
              </div>
            </a>
            <div>
              <div
                class="product-card-price"
              >
                <div
                  class="emotion-10"
                >
                  <div>
                    <div
                      class="product-price__markdown"
                    >
                      <span
                        aria-label="price.regular_price_aria_label"
                        class="product-price__no-strike"
                        role="text"
                      >
                        $69.95
                      </span>
                    </div>
                    <div
                      aria-label="price.current_price_range_aria_label"
                      class="product-price__highlight"
                      role="text"
                    >
                      <span
                        class="product-price__highlight"
                      >
                        $49.99
                         - 
                        $54.99
                      </span>
                    </div>
                  </div>
                </div>
              </div>
              <div
                class="product-card__marketing-flag emotion-11"
              >
                Final Sale
              </div>
            </div>
          </div>
        </div>
        <div
          class="emotion-4"
          data-testid="grid-root"
          spacing="0.5"
        >
          <div
            class="product-card emotion-6"
          >
            <div
              class="product-card__image-wrapper emotion-7"
              id="product124"
            >
              <a
                aria-hidden="false"
                class="emotion-8"
                href="http://localhost/browse/product.do?pid=124&cid=1234&pcid=23456&grid=source_4_4_1"
                target="_self"
              >
                <img
                  alt="Indigo zip hoodie"
                  class="product-card__image"
                  src="http://www.gap.com/webcontent/0013/707/658/cn13707658.jpg"
                />
              </a>
            </div>
            <a
              href="http://localhost/browse/product.do?pid=124&cid=1234&pcid=23456&grid=source_4_4_1"
            >
              <div
                class="emotion-9"
              >
                Indigo zip hoodie
              </div>
            </a>
            <div>
              <div
                class="product-card-price"
              >
                <div
                  class="emotion-10"
                >
                  <div>
                    <div
                      class="product-price__markdown"
                    >
                      <span
                        aria-label="price.regular_price_aria_label"
                        class="product-price__no-strike"
                        role="text"
                      >
                        $69.95
                      </span>
                    </div>
                    <div
                      aria-label="price.current_price_range_aria_label"
                      class="product-price__highlight"
                      role="text"
                    >
                      <span
                        class="product-price__highlight"
                      >
                        $49.99
                         - 
                        $54.99
                      </span>
                    </div>
                  </div>
                </div>
              </div>
              <div
                class="product-card__marketing-flag emotion-11"
              >
                Final Sale
              </div>
            </div>
          </div>
        </div>
        <div
          class="emotion-1"
          data-testid="grid-root"
          spacing="0"
        >
          <div
            id="subcatHeader_1234"
          />
          <div />
        </div>
      </div>
    </div>
  </body>,
  "container": .emotion-0 {
  box-sizing: border-box;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-flex-wrap: wrap;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
}

.emotion-1 {
  box-sizing: border-box;
  -webkit-flex-basis: 100%;
  -ms-flex-preferred-size: 100%;
  flex-basis: 100%;
  -webkit-box-flex: 0;
  -webkit-flex-grow: 0;
  -ms-flex-positive: 0;
  flex-grow: 0;
  margin: 0;
  max-width: 100%;
  padding: 0em;
}

.emotion-2 {
  overflow: hidden;
  margin: 0;
  margin-bottom: 2rem;
  padding: 0;
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 700;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 1.067rem;
  color: #666;
  line-height: normal;
  text-align: center;
}

.emotion-3 {
  text-transform: uppercase;
  letter-spacing: 0.15em;
  display: inline-block;
  position: relative;
  padding: 0 0.5rem;
}

.emotion-3::before,
.emotion-3::after {
  border-top: 2px solid #666;
  display: block;
  position: absolute;
  width: 100em;
  top: 50%;
  content: '';
}

.emotion-3::before {
  right: 100%;
}

.emotion-3::after {
  left: 100%;
}

.emotion-4 {
  --columnSize: 1;
  box-sizing: border-box;
  -webkit-flex-basis: 33.333333333333336%;
  -ms-flex-preferred-size: 33.333333333333336%;
  flex-basis: 33.333333333333336%;
  -webkit-box-flex: 0;
  -webkit-flex-grow: 0;
  -ms-flex-positive: 0;
  flex-grow: 0;
  margin: 0;
  max-width: 33.333333333333336%;
  padding: 0.5em;
}

.emotion-6 {
  min-height: 430px;
  position: relative;
}

.emotion-6 .product-card__image {
  width: 100%;
  display: block;
}

.emotion-7 {
  margin-bottom: 0.5rem;
}

.emotion-9 {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  color: #666;
  line-height: 1.4;
  text-transform: none;
  font-weight: normal;
  font-size: 0.8rem;
}

.emotion-10 {
  text-transform: none;
  line-height: 1.4;
  color: #666;
  font-size: 0.8rem!important;
  letter-spacing: normal;
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-10 .product-price__markdown,
.emotion-10 .product-price--markdown {
  color: #666;
}

.emotion-10 .product-price__highlight,
.emotion-10 .product-price--highlight {
  text-transform: none;
  color: #D00000;
}

.emotion-10 .product-price__highlight priceHighlightFont,
.emotion-10 .product-price--highlight priceHighlightFont {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-10 .product-price__highlight--br,
.emotion-10 .product-price--highlight-br {
  text-transform: none;
  font-size: 0.8125rem;
}

.emotion-10 .product-price__markdown {
  text-transform: none;
  color: #666;
}

.emotion-10 .product-price__no-strike {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-10 .product-price__option {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 700;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-10 .product-price__option--br {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-10 .product-price__percentage-off {
  color: #D00000;
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-transform: none;
  padding-left: 5px;
}

.emotion-10 .product-price__percentage-off-no-padding {
  color: #D00000;
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-transform: none;
}

.emotion-10 .product-price__regular {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-10 .product-price__sale {
  color: #666;
}

.emotion-10 .product-price__strike {
  -webkit-text-decoration: line-through;
  text-decoration: line-through;
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-10 .product-price__same-line {
  padding-left: 5px;
}

.emotion-11 {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  color: #666;
  line-height: 1.4;
  text-transform: none;
  font-weight: bold;
  font-size: 0.8rem;
}

<div>
    <div
      class="emotion-0"
      data-testid="grid-root"
      spacing="0.5"
    >
      <div
        class="emotion-1"
        data-testid="grid-root"
        spacing="0"
      >
        <div
          id="subcatHeader_1234"
        />
        <div
          class="emotion-2"
        >
          <h2
            class="emotion-3"
          >
            test group
          </h2>
        </div>
      </div>
      <div
        class="emotion-4"
        data-testid="grid-root"
        spacing="0.5"
      />
      <div
        class="emotion-4"
        data-testid="grid-root"
        spacing="0.5"
      >
        <div
          class="product-card emotion-6"
        >
          <div
            class="product-card__image-wrapper emotion-7"
            id="product123"
          >
            <a
              aria-hidden="false"
              class="emotion-8"
              href="http://localhost/browse/product.do?pid=123&cid=1234&pcid=23456&grid=source_1_4_1"
              target="_self"
            >
              <img
                alt="sherpa fleece hoodie"
                class="product-card__image"
                src="http://www3.assets-gap.com/webcontent/0013/882/196/cn13882196.jpg"
              />
            </a>
          </div>
          <a
            href="http://localhost/browse/product.do?pid=123&cid=1234&pcid=23456&grid=source_1_4_1"
          >
            <div
              class="emotion-9"
            >
              Sherpa fleece hoodie
            </div>
          </a>
          <div>
            <div
              class="product-card-price"
            >
              <div
                class="emotion-10"
              >
                <div>
                  <div
                    class="product-price__markdown"
                  >
                    <span
                      aria-label="price.regular_price_aria_label"
                      class="product-price__no-strike"
                      role="text"
                    >
                      $69.95
                    </span>
                  </div>
                  <div
                    aria-label="price.current_price_range_aria_label"
                    class="product-price__highlight"
                    role="text"
                  >
                    <span
                      class="product-price__highlight"
                    >
                      $49.99
                       - 
                      $54.99
                    </span>
                  </div>
                </div>
              </div>
            </div>
            <div
              class="product-card__marketing-flag emotion-11"
            >
              Final Sale
            </div>
          </div>
        </div>
      </div>
      <div
        class="emotion-4"
        data-testid="grid-root"
        spacing="0.5"
      >
        <div
          class="product-card emotion-6"
        >
          <div
            class="product-card__image-wrapper emotion-7"
            id="product123"
          >
            <a
              aria-hidden="false"
              class="emotion-8"
              href="http://localhost/browse/product.do?pid=123&cid=1234&pcid=23456&grid=source_2_4_1"
              target="_self"
            >
              <img
                alt="sherpa fleece hoodie"
                class="product-card__image"
                src="http://www3.assets-gap.com/webcontent/0013/882/196/cn13882196.jpg"
              />
            </a>
          </div>
          <a
            href="http://localhost/browse/product.do?pid=123&cid=1234&pcid=23456&grid=source_2_4_1"
          >
            <div
              class="emotion-9"
            >
              Sherpa fleece hoodie
            </div>
          </a>
          <div>
            <div
              class="product-card-price"
            >
              <div
                class="emotion-10"
              >
                <div>
                  <div
                    class="product-price__markdown"
                  >
                    <span
                      aria-label="price.regular_price_aria_label"
                      class="product-price__no-strike"
                      role="text"
                    >
                      $69.95
                    </span>
                  </div>
                  <div
                    aria-label="price.current_price_range_aria_label"
                    class="product-price__highlight"
                    role="text"
                  >
                    <span
                      class="product-price__highlight"
                    >
                      $49.99
                       - 
                      $54.99
                    </span>
                  </div>
                </div>
              </div>
            </div>
            <div
              class="product-card__marketing-flag emotion-11"
            >
              Final Sale
            </div>
          </div>
        </div>
      </div>
      <div
        class="emotion-4"
        data-testid="grid-root"
        spacing="0.5"
      >
        <div
          class="product-card emotion-6"
        >
          <div
            class="product-card__image-wrapper emotion-7"
            id="product123"
          >
            <a
              aria-hidden="false"
              class="emotion-8"
              href="http://localhost/browse/product.do?pid=123&cid=1234&pcid=23456&grid=source_3_4_1"
              target="_self"
            >
              <img
                alt="sherpa fleece hoodie"
                class="product-card__image"
                src="http://www3.assets-gap.com/webcontent/0013/882/196/cn13882196.jpg"
              />
            </a>
          </div>
          <a
            href="http://localhost/browse/product.do?pid=123&cid=1234&pcid=23456&grid=source_3_4_1"
          >
            <div
              class="emotion-9"
            >
              Sherpa fleece hoodie
            </div>
          </a>
          <div>
            <div
              class="product-card-price"
            >
              <div
                class="emotion-10"
              >
                <div>
                  <div
                    class="product-price__markdown"
                  >
                    <span
                      aria-label="price.regular_price_aria_label"
                      class="product-price__no-strike"
                      role="text"
                    >
                      $69.95
                    </span>
                  </div>
                  <div
                    aria-label="price.current_price_range_aria_label"
                    class="product-price__highlight"
                    role="text"
                  >
                    <span
                      class="product-price__highlight"
                    >
                      $49.99
                       - 
                      $54.99
                    </span>
                  </div>
                </div>
              </div>
            </div>
            <div
              class="product-card__marketing-flag emotion-11"
            >
              Final Sale
            </div>
          </div>
        </div>
      </div>
      <div
        class="emotion-4"
        data-testid="grid-root"
        spacing="0.5"
      >
        <div
          class="product-card emotion-6"
        >
          <div
            class="product-card__image-wrapper emotion-7"
            id="product124"
          >
            <a
              aria-hidden="false"
              class="emotion-8"
              href="http://localhost/browse/product.do?pid=124&cid=1234&pcid=23456&grid=source_4_4_1"
              target="_self"
            >
              <img
                alt="Indigo zip hoodie"
                class="product-card__image"
                src="http://www.gap.com/webcontent/0013/707/658/cn13707658.jpg"
              />
            </a>
          </div>
          <a
            href="http://localhost/browse/product.do?pid=124&cid=1234&pcid=23456&grid=source_4_4_1"
          >
            <div
              class="emotion-9"
            >
              Indigo zip hoodie
            </div>
          </a>
          <div>
            <div
              class="product-card-price"
            >
              <div
                class="emotion-10"
              >
                <div>
                  <div
                    class="product-price__markdown"
                  >
                    <span
                      aria-label="price.regular_price_aria_label"
                      class="product-price__no-strike"
                      role="text"
                    >
                      $69.95
                    </span>
                  </div>
                  <div
                    aria-label="price.current_price_range_aria_label"
                    class="product-price__highlight"
                    role="text"
                  >
                    <span
                      class="product-price__highlight"
                    >
                      $49.99
                       - 
                      $54.99
                    </span>
                  </div>
                </div>
              </div>
            </div>
            <div
              class="product-card__marketing-flag emotion-11"
            >
              Final Sale
            </div>
          </div>
        </div>
      </div>
      <div
        class="emotion-1"
        data-testid="grid-root"
        spacing="0"
      >
        <div
          id="subcatHeader_1234"
        />
        <div />
      </div>
    </div>
  </div>,
  "debug": [Function],
  "findAllByAltText": [Function],
  "findAllByDisplayValue": [Function],
  "findAllByLabelText": [Function],
  "findAllByPlaceholderText": [Function],
  "findAllByRole": [Function],
  "findAllByTestId": [Function],
  "findAllByText": [Function],
  "findAllByTitle": [Function],
  "findByAltText": [Function],
  "findByDisplayValue": [Function],
  "findByLabelText": [Function],
  "findByPlaceholderText": [Function],
  "findByRole": [Function],
  "findByTestId": [Function],
  "findByText": [Function],
  "findByTitle": [Function],
  "getAllByAltText": [Function],
  "getAllByDisplayValue": [Function],
  "getAllByLabelText": [Function],
  "getAllByPlaceholderText": [Function],
  "getAllByRole": [Function],
  "getAllByTestId": [Function],
  "getAllByText": [Function],
  "getAllByTitle": [Function],
  "getByAltText": [Function],
  "getByDisplayValue": [Function],
  "getByLabelText": [Function],
  "getByPlaceholderText": [Function],
  "getByRole": [Function],
  "getByTestId": [Function],
  "getByText": [Function],
  "getByTitle": [Function],
  "queryAllByAltText": [Function],
  "queryAllByDisplayValue": [Function],
  "queryAllByLabelText": [Function],
  "queryAllByPlaceholderText": [Function],
  "queryAllByRole": [Function],
  "queryAllByTestId": [Function],
  "queryAllByText": [Function],
  "queryAllByTitle": [Function],
  "queryByAltText": [Function],
  "queryByDisplayValue": [Function],
  "queryByLabelText": [Function],
  "queryByPlaceholderText": [Function],
  "queryByRole": [Function],
  "queryByTestId": [Function],
  "queryByText": [Function],
  "queryByTitle": [Function],
  "rerender": [Function],
  "unmount": [Function],
}
`;

exports[`<ProductGroup /> render banana republic brands should render product group for br 1`] = `
{
  "asFragment": [Function],
  "baseElement": .emotion-0 {
  box-sizing: border-box;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-flex-wrap: wrap;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
}

.emotion-1 {
  box-sizing: border-box;
  -webkit-flex-basis: 100%;
  -ms-flex-preferred-size: 100%;
  flex-basis: 100%;
  -webkit-box-flex: 0;
  -webkit-flex-grow: 0;
  -ms-flex-positive: 0;
  flex-grow: 0;
  margin: 0;
  max-width: 100%;
  padding: 0em;
}

.emotion-2 {
  display: none;
  overflow: hidden;
  margin: 0;
  margin-bottom: 2rem;
  padding: 0;
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 700;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 1.067rem;
  color: #666;
  line-height: normal;
  text-align: center;
}

.emotion-3 {
  text-transform: uppercase;
  letter-spacing: 0.15em;
  display: inline-block;
  position: relative;
  padding: 0 0.5rem;
}

.emotion-3::before,
.emotion-3::after {
  border-top: 2px solid #666;
  display: block;
  position: absolute;
  width: 100em;
  top: 50%;
  content: '';
}

.emotion-3::before {
  right: 100%;
}

.emotion-3::after {
  left: 100%;
}

.emotion-4 {
  --columnSize: 1;
  box-sizing: border-box;
  -webkit-flex-basis: 33.333333333333336%;
  -ms-flex-preferred-size: 33.333333333333336%;
  flex-basis: 33.333333333333336%;
  -webkit-box-flex: 0;
  -webkit-flex-grow: 0;
  -ms-flex-positive: 0;
  flex-grow: 0;
  margin: 0;
  max-width: 33.333333333333336%;
  padding: 0em;
}

.emotion-5 {
  min-height: 430px;
  position: relative;
}

.emotion-5 .product-card__image {
  width: 100%;
  display: block;
}

.emotion-6 {
  margin-bottom: 0.5rem;
}

.emotion-8 {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  color: #666;
  line-height: 1.4;
  text-transform: none;
  font-weight: normal;
  font-size: 0.8rem;
}

.emotion-9 {
  text-transform: none;
  line-height: 1.4;
  color: #666;
  font-size: 0.8rem!important;
  letter-spacing: normal;
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-9 .product-price__markdown,
.emotion-9 .product-price--markdown {
  color: #666;
}

.emotion-9 .product-price__highlight,
.emotion-9 .product-price--highlight {
  text-transform: none;
  color: #D00000;
}

.emotion-9 .product-price__highlight priceHighlightFont,
.emotion-9 .product-price--highlight priceHighlightFont {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-9 .product-price__highlight--br,
.emotion-9 .product-price--highlight-br {
  text-transform: none;
  font-size: 0.8125rem;
}

.emotion-9 .product-price__markdown {
  text-transform: none;
  color: #666;
}

.emotion-9 .product-price__no-strike {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-9 .product-price__option {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 700;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-9 .product-price__option--br {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-9 .product-price__percentage-off {
  color: #D00000;
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-transform: none;
  padding-left: 5px;
}

.emotion-9 .product-price__percentage-off-no-padding {
  color: #D00000;
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-transform: none;
}

.emotion-9 .product-price__regular {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-9 .product-price__sale {
  color: #666;
}

.emotion-9 .product-price__strike {
  -webkit-text-decoration: line-through;
  text-decoration: line-through;
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-9 .product-price__same-line {
  padding-left: 5px;
}

.emotion-10 {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  color: #666;
  line-height: 1.4;
  text-transform: none;
  font-weight: bold;
  font-size: 0.8rem;
}

<body>
    <div>
      <div
        class="emotion-0"
        data-testid="grid-root"
        spacing="0"
      >
        <div
          class="emotion-1"
          data-testid="grid-root"
          spacing="0"
        >
          <div
            id="subcatHeader_1234"
          />
          <div
            class="emotion-2"
          >
            <h2
              class="emotion-3"
            >
              test group
            </h2>
          </div>
        </div>
        <div
          class="emotion-4"
          data-testid="grid-root"
          spacing="0"
        >
          <div
            class="product-card emotion-5"
          >
            <div
              class="product-card__image-wrapper emotion-6"
              id="product123"
            >
              <a
                aria-hidden="false"
                class="emotion-7"
                href="http://localhost/browse/product.do?pid=123&cid=1234&pcid=23456&grid=source_0_4_1"
                target="_self"
              >
                <img
                  alt="sherpa fleece hoodie"
                  class="product-card__image"
                  src="http://www3.assets-gap.com/webcontent/0013/882/196/cn13882196.jpg"
                />
              </a>
            </div>
            <a
              href="http://localhost/browse/product.do?pid=123&cid=1234&pcid=23456&grid=source_0_4_1"
            >
              <div
                class="emotion-8"
              >
                Sherpa fleece hoodie
              </div>
            </a>
            <div>
              <div
                class="product-card-price"
              >
                <div
                  class="emotion-9"
                >
                  <div>
                    <div
                      class="product-price__markdown"
                    >
                      <span
                        aria-label="price.regular_price_aria_label"
                        class="product-price__no-strike"
                        role="text"
                      >
                        $69.95
                      </span>
                    </div>
                    <div
                      aria-label="price.current_price_range_aria_label"
                      class="product-price__highlight"
                      role="text"
                    >
                      <span
                        class="product-price__highlight"
                      >
                        $49.99
                         - 
                        $54.99
                      </span>
                    </div>
                  </div>
                </div>
              </div>
              <div
                class="product-card__marketing-flag emotion-10"
              >
                Final Sale
              </div>
            </div>
          </div>
        </div>
        <div
          class="emotion-4"
          data-testid="grid-root"
          spacing="0"
        >
          <div
            class="product-card emotion-5"
          >
            <div
              class="product-card__image-wrapper emotion-6"
              id="product123"
            >
              <a
                aria-hidden="false"
                class="emotion-7"
                href="http://localhost/browse/product.do?pid=123&cid=1234&pcid=23456&grid=source_1_4_1"
                target="_self"
              >
                <img
                  alt="sherpa fleece hoodie"
                  class="product-card__image"
                  src="http://www3.assets-gap.com/webcontent/0013/882/196/cn13882196.jpg"
                />
              </a>
            </div>
            <a
              href="http://localhost/browse/product.do?pid=123&cid=1234&pcid=23456&grid=source_1_4_1"
            >
              <div
                class="emotion-8"
              >
                Sherpa fleece hoodie
              </div>
            </a>
            <div>
              <div
                class="product-card-price"
              >
                <div
                  class="emotion-9"
                >
                  <div>
                    <div
                      class="product-price__markdown"
                    >
                      <span
                        aria-label="price.regular_price_aria_label"
                        class="product-price__no-strike"
                        role="text"
                      >
                        $69.95
                      </span>
                    </div>
                    <div
                      aria-label="price.current_price_range_aria_label"
                      class="product-price__highlight"
                      role="text"
                    >
                      <span
                        class="product-price__highlight"
                      >
                        $49.99
                         - 
                        $54.99
                      </span>
                    </div>
                  </div>
                </div>
              </div>
              <div
                class="product-card__marketing-flag emotion-10"
              >
                Final Sale
              </div>
            </div>
          </div>
        </div>
        <div
          class="emotion-4"
          data-testid="grid-root"
          spacing="0"
        >
          <div
            class="product-card emotion-5"
          >
            <div
              class="product-card__image-wrapper emotion-6"
              id="product123"
            >
              <a
                aria-hidden="false"
                class="emotion-7"
                href="http://localhost/browse/product.do?pid=123&cid=1234&pcid=23456&grid=source_2_4_1"
                target="_self"
              >
                <img
                  alt="sherpa fleece hoodie"
                  class="product-card__image"
                  src="http://www3.assets-gap.com/webcontent/0013/882/196/cn13882196.jpg"
                />
              </a>
            </div>
            <a
              href="http://localhost/browse/product.do?pid=123&cid=1234&pcid=23456&grid=source_2_4_1"
            >
              <div
                class="emotion-8"
              >
                Sherpa fleece hoodie
              </div>
            </a>
            <div>
              <div
                class="product-card-price"
              >
                <div
                  class="emotion-9"
                >
                  <div>
                    <div
                      class="product-price__markdown"
                    >
                      <span
                        aria-label="price.regular_price_aria_label"
                        class="product-price__no-strike"
                        role="text"
                      >
                        $69.95
                      </span>
                    </div>
                    <div
                      aria-label="price.current_price_range_aria_label"
                      class="product-price__highlight"
                      role="text"
                    >
                      <span
                        class="product-price__highlight"
                      >
                        $49.99
                         - 
                        $54.99
                      </span>
                    </div>
                  </div>
                </div>
              </div>
              <div
                class="product-card__marketing-flag emotion-10"
              >
                Final Sale
              </div>
            </div>
          </div>
        </div>
        <div
          class="emotion-4"
          data-testid="grid-root"
          spacing="0"
        >
          <div
            class="product-card emotion-5"
          >
            <div
              class="product-card__image-wrapper emotion-6"
              id="product124"
            >
              <a
                aria-hidden="false"
                class="emotion-7"
                href="http://localhost/browse/product.do?pid=124&cid=1234&pcid=23456&grid=source_3_4_1"
                target="_self"
              >
                <img
                  alt="Indigo zip hoodie"
                  class="product-card__image"
                  src="http://www.gap.com/webcontent/0013/707/658/cn13707658.jpg"
                />
              </a>
            </div>
            <a
              href="http://localhost/browse/product.do?pid=124&cid=1234&pcid=23456&grid=source_3_4_1"
            >
              <div
                class="emotion-8"
              >
                Indigo zip hoodie
              </div>
            </a>
            <div>
              <div
                class="product-card-price"
              >
                <div
                  class="emotion-9"
                >
                  <div>
                    <div
                      class="product-price__markdown"
                    >
                      <span
                        aria-label="price.regular_price_aria_label"
                        class="product-price__no-strike"
                        role="text"
                      >
                        $69.95
                      </span>
                    </div>
                    <div
                      aria-label="price.current_price_range_aria_label"
                      class="product-price__highlight"
                      role="text"
                    >
                      <span
                        class="product-price__highlight"
                      >
                        $49.99
                         - 
                        $54.99
                      </span>
                    </div>
                  </div>
                </div>
              </div>
              <div
                class="product-card__marketing-flag emotion-10"
              >
                Final Sale
              </div>
            </div>
          </div>
        </div>
        <div
          class="emotion-1"
          data-testid="grid-root"
          spacing="0"
        >
          <div
            id="subcatHeader_1234"
          />
          <div />
        </div>
      </div>
    </div>
  </body>,
  "container": .emotion-0 {
  box-sizing: border-box;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-flex-wrap: wrap;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
}

.emotion-1 {
  box-sizing: border-box;
  -webkit-flex-basis: 100%;
  -ms-flex-preferred-size: 100%;
  flex-basis: 100%;
  -webkit-box-flex: 0;
  -webkit-flex-grow: 0;
  -ms-flex-positive: 0;
  flex-grow: 0;
  margin: 0;
  max-width: 100%;
  padding: 0em;
}

.emotion-2 {
  display: none;
  overflow: hidden;
  margin: 0;
  margin-bottom: 2rem;
  padding: 0;
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 700;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 1.067rem;
  color: #666;
  line-height: normal;
  text-align: center;
}

.emotion-3 {
  text-transform: uppercase;
  letter-spacing: 0.15em;
  display: inline-block;
  position: relative;
  padding: 0 0.5rem;
}

.emotion-3::before,
.emotion-3::after {
  border-top: 2px solid #666;
  display: block;
  position: absolute;
  width: 100em;
  top: 50%;
  content: '';
}

.emotion-3::before {
  right: 100%;
}

.emotion-3::after {
  left: 100%;
}

.emotion-4 {
  --columnSize: 1;
  box-sizing: border-box;
  -webkit-flex-basis: 33.333333333333336%;
  -ms-flex-preferred-size: 33.333333333333336%;
  flex-basis: 33.333333333333336%;
  -webkit-box-flex: 0;
  -webkit-flex-grow: 0;
  -ms-flex-positive: 0;
  flex-grow: 0;
  margin: 0;
  max-width: 33.333333333333336%;
  padding: 0em;
}

.emotion-5 {
  min-height: 430px;
  position: relative;
}

.emotion-5 .product-card__image {
  width: 100%;
  display: block;
}

.emotion-6 {
  margin-bottom: 0.5rem;
}

.emotion-8 {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  color: #666;
  line-height: 1.4;
  text-transform: none;
  font-weight: normal;
  font-size: 0.8rem;
}

.emotion-9 {
  text-transform: none;
  line-height: 1.4;
  color: #666;
  font-size: 0.8rem!important;
  letter-spacing: normal;
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-9 .product-price__markdown,
.emotion-9 .product-price--markdown {
  color: #666;
}

.emotion-9 .product-price__highlight,
.emotion-9 .product-price--highlight {
  text-transform: none;
  color: #D00000;
}

.emotion-9 .product-price__highlight priceHighlightFont,
.emotion-9 .product-price--highlight priceHighlightFont {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-9 .product-price__highlight--br,
.emotion-9 .product-price--highlight-br {
  text-transform: none;
  font-size: 0.8125rem;
}

.emotion-9 .product-price__markdown {
  text-transform: none;
  color: #666;
}

.emotion-9 .product-price__no-strike {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-9 .product-price__option {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 700;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-9 .product-price__option--br {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-9 .product-price__percentage-off {
  color: #D00000;
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-transform: none;
  padding-left: 5px;
}

.emotion-9 .product-price__percentage-off-no-padding {
  color: #D00000;
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-transform: none;
}

.emotion-9 .product-price__regular {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-9 .product-price__sale {
  color: #666;
}

.emotion-9 .product-price__strike {
  -webkit-text-decoration: line-through;
  text-decoration: line-through;
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-9 .product-price__same-line {
  padding-left: 5px;
}

.emotion-10 {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  color: #666;
  line-height: 1.4;
  text-transform: none;
  font-weight: bold;
  font-size: 0.8rem;
}

<div>
    <div
      class="emotion-0"
      data-testid="grid-root"
      spacing="0"
    >
      <div
        class="emotion-1"
        data-testid="grid-root"
        spacing="0"
      >
        <div
          id="subcatHeader_1234"
        />
        <div
          class="emotion-2"
        >
          <h2
            class="emotion-3"
          >
            test group
          </h2>
        </div>
      </div>
      <div
        class="emotion-4"
        data-testid="grid-root"
        spacing="0"
      >
        <div
          class="product-card emotion-5"
        >
          <div
            class="product-card__image-wrapper emotion-6"
            id="product123"
          >
            <a
              aria-hidden="false"
              class="emotion-7"
              href="http://localhost/browse/product.do?pid=123&cid=1234&pcid=23456&grid=source_0_4_1"
              target="_self"
            >
              <img
                alt="sherpa fleece hoodie"
                class="product-card__image"
                src="http://www3.assets-gap.com/webcontent/0013/882/196/cn13882196.jpg"
              />
            </a>
          </div>
          <a
            href="http://localhost/browse/product.do?pid=123&cid=1234&pcid=23456&grid=source_0_4_1"
          >
            <div
              class="emotion-8"
            >
              Sherpa fleece hoodie
            </div>
          </a>
          <div>
            <div
              class="product-card-price"
            >
              <div
                class="emotion-9"
              >
                <div>
                  <div
                    class="product-price__markdown"
                  >
                    <span
                      aria-label="price.regular_price_aria_label"
                      class="product-price__no-strike"
                      role="text"
                    >
                      $69.95
                    </span>
                  </div>
                  <div
                    aria-label="price.current_price_range_aria_label"
                    class="product-price__highlight"
                    role="text"
                  >
                    <span
                      class="product-price__highlight"
                    >
                      $49.99
                       - 
                      $54.99
                    </span>
                  </div>
                </div>
              </div>
            </div>
            <div
              class="product-card__marketing-flag emotion-10"
            >
              Final Sale
            </div>
          </div>
        </div>
      </div>
      <div
        class="emotion-4"
        data-testid="grid-root"
        spacing="0"
      >
        <div
          class="product-card emotion-5"
        >
          <div
            class="product-card__image-wrapper emotion-6"
            id="product123"
          >
            <a
              aria-hidden="false"
              class="emotion-7"
              href="http://localhost/browse/product.do?pid=123&cid=1234&pcid=23456&grid=source_1_4_1"
              target="_self"
            >
              <img
                alt="sherpa fleece hoodie"
                class="product-card__image"
                src="http://www3.assets-gap.com/webcontent/0013/882/196/cn13882196.jpg"
              />
            </a>
          </div>
          <a
            href="http://localhost/browse/product.do?pid=123&cid=1234&pcid=23456&grid=source_1_4_1"
          >
            <div
              class="emotion-8"
            >
              Sherpa fleece hoodie
            </div>
          </a>
          <div>
            <div
              class="product-card-price"
            >
              <div
                class="emotion-9"
              >
                <div>
                  <div
                    class="product-price__markdown"
                  >
                    <span
                      aria-label="price.regular_price_aria_label"
                      class="product-price__no-strike"
                      role="text"
                    >
                      $69.95
                    </span>
                  </div>
                  <div
                    aria-label="price.current_price_range_aria_label"
                    class="product-price__highlight"
                    role="text"
                  >
                    <span
                      class="product-price__highlight"
                    >
                      $49.99
                       - 
                      $54.99
                    </span>
                  </div>
                </div>
              </div>
            </div>
            <div
              class="product-card__marketing-flag emotion-10"
            >
              Final Sale
            </div>
          </div>
        </div>
      </div>
      <div
        class="emotion-4"
        data-testid="grid-root"
        spacing="0"
      >
        <div
          class="product-card emotion-5"
        >
          <div
            class="product-card__image-wrapper emotion-6"
            id="product123"
          >
            <a
              aria-hidden="false"
              class="emotion-7"
              href="http://localhost/browse/product.do?pid=123&cid=1234&pcid=23456&grid=source_2_4_1"
              target="_self"
            >
              <img
                alt="sherpa fleece hoodie"
                class="product-card__image"
                src="http://www3.assets-gap.com/webcontent/0013/882/196/cn13882196.jpg"
              />
            </a>
          </div>
          <a
            href="http://localhost/browse/product.do?pid=123&cid=1234&pcid=23456&grid=source_2_4_1"
          >
            <div
              class="emotion-8"
            >
              Sherpa fleece hoodie
            </div>
          </a>
          <div>
            <div
              class="product-card-price"
            >
              <div
                class="emotion-9"
              >
                <div>
                  <div
                    class="product-price__markdown"
                  >
                    <span
                      aria-label="price.regular_price_aria_label"
                      class="product-price__no-strike"
                      role="text"
                    >
                      $69.95
                    </span>
                  </div>
                  <div
                    aria-label="price.current_price_range_aria_label"
                    class="product-price__highlight"
                    role="text"
                  >
                    <span
                      class="product-price__highlight"
                    >
                      $49.99
                       - 
                      $54.99
                    </span>
                  </div>
                </div>
              </div>
            </div>
            <div
              class="product-card__marketing-flag emotion-10"
            >
              Final Sale
            </div>
          </div>
        </div>
      </div>
      <div
        class="emotion-4"
        data-testid="grid-root"
        spacing="0"
      >
        <div
          class="product-card emotion-5"
        >
          <div
            class="product-card__image-wrapper emotion-6"
            id="product124"
          >
            <a
              aria-hidden="false"
              class="emotion-7"
              href="http://localhost/browse/product.do?pid=124&cid=1234&pcid=23456&grid=source_3_4_1"
              target="_self"
            >
              <img
                alt="Indigo zip hoodie"
                class="product-card__image"
                src="http://www.gap.com/webcontent/0013/707/658/cn13707658.jpg"
              />
            </a>
          </div>
          <a
            href="http://localhost/browse/product.do?pid=124&cid=1234&pcid=23456&grid=source_3_4_1"
          >
            <div
              class="emotion-8"
            >
              Indigo zip hoodie
            </div>
          </a>
          <div>
            <div
              class="product-card-price"
            >
              <div
                class="emotion-9"
              >
                <div>
                  <div
                    class="product-price__markdown"
                  >
                    <span
                      aria-label="price.regular_price_aria_label"
                      class="product-price__no-strike"
                      role="text"
                    >
                      $69.95
                    </span>
                  </div>
                  <div
                    aria-label="price.current_price_range_aria_label"
                    class="product-price__highlight"
                    role="text"
                  >
                    <span
                      class="product-price__highlight"
                    >
                      $49.99
                       - 
                      $54.99
                    </span>
                  </div>
                </div>
              </div>
            </div>
            <div
              class="product-card__marketing-flag emotion-10"
            >
              Final Sale
            </div>
          </div>
        </div>
      </div>
      <div
        class="emotion-1"
        data-testid="grid-root"
        spacing="0"
      >
        <div
          id="subcatHeader_1234"
        />
        <div />
      </div>
    </div>
  </div>,
  "debug": [Function],
  "findAllByAltText": [Function],
  "findAllByDisplayValue": [Function],
  "findAllByLabelText": [Function],
  "findAllByPlaceholderText": [Function],
  "findAllByRole": [Function],
  "findAllByTestId": [Function],
  "findAllByText": [Function],
  "findAllByTitle": [Function],
  "findByAltText": [Function],
  "findByDisplayValue": [Function],
  "findByLabelText": [Function],
  "findByPlaceholderText": [Function],
  "findByRole": [Function],
  "findByTestId": [Function],
  "findByText": [Function],
  "findByTitle": [Function],
  "getAllByAltText": [Function],
  "getAllByDisplayValue": [Function],
  "getAllByLabelText": [Function],
  "getAllByPlaceholderText": [Function],
  "getAllByRole": [Function],
  "getAllByTestId": [Function],
  "getAllByText": [Function],
  "getAllByTitle": [Function],
  "getByAltText": [Function],
  "getByDisplayValue": [Function],
  "getByLabelText": [Function],
  "getByPlaceholderText": [Function],
  "getByRole": [Function],
  "getByTestId": [Function],
  "getByText": [Function],
  "getByTitle": [Function],
  "queryAllByAltText": [Function],
  "queryAllByDisplayValue": [Function],
  "queryAllByLabelText": [Function],
  "queryAllByPlaceholderText": [Function],
  "queryAllByRole": [Function],
  "queryAllByTestId": [Function],
  "queryAllByText": [Function],
  "queryAllByTitle": [Function],
  "queryByAltText": [Function],
  "queryByDisplayValue": [Function],
  "queryByLabelText": [Function],
  "queryByPlaceholderText": [Function],
  "queryByRole": [Function],
  "queryByTestId": [Function],
  "queryByText": [Function],
  "queryByTitle": [Function],
  "rerender": [Function],
  "unmount": [Function],
}
`;

exports[`<ProductGroup /> render banana republic brands should render product group for brfs 1`] = `
{
  "asFragment": [Function],
  "baseElement": .emotion-0 {
  box-sizing: border-box;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-flex-wrap: wrap;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
}

.emotion-1 {
  box-sizing: border-box;
  -webkit-flex-basis: 100%;
  -ms-flex-preferred-size: 100%;
  flex-basis: 100%;
  -webkit-box-flex: 0;
  -webkit-flex-grow: 0;
  -ms-flex-positive: 0;
  flex-grow: 0;
  margin: 0;
  max-width: 100%;
  padding: 0em;
}

.emotion-2 {
  display: none;
  overflow: hidden;
  margin: 0;
  margin-bottom: 2rem;
  padding: 0;
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 700;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 1.067rem;
  color: #666;
  line-height: normal;
  text-align: center;
}

.emotion-3 {
  text-transform: uppercase;
  letter-spacing: 0.15em;
  display: inline-block;
  position: relative;
  padding: 0 0.5rem;
}

.emotion-3::before,
.emotion-3::after {
  border-top: 2px solid #666;
  display: block;
  position: absolute;
  width: 100em;
  top: 50%;
  content: '';
}

.emotion-3::before {
  right: 100%;
}

.emotion-3::after {
  left: 100%;
}

.emotion-4 {
  --columnSize: 1;
  box-sizing: border-box;
  -webkit-flex-basis: 33.333333333333336%;
  -ms-flex-preferred-size: 33.333333333333336%;
  flex-basis: 33.333333333333336%;
  -webkit-box-flex: 0;
  -webkit-flex-grow: 0;
  -ms-flex-positive: 0;
  flex-grow: 0;
  margin: 0;
  max-width: 33.333333333333336%;
  padding: 0em;
}

.emotion-5 {
  min-height: 430px;
  position: relative;
}

.emotion-5 .product-card__image {
  width: 100%;
  display: block;
}

.emotion-6 {
  margin-bottom: 0.5rem;
}

.emotion-8 {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  color: #666;
  line-height: 1.4;
  text-transform: none;
  font-weight: normal;
  font-size: 0.8rem;
}

.emotion-9 {
  text-transform: none;
  line-height: 1.4;
  color: #666;
  font-size: 0.8rem!important;
  letter-spacing: normal;
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-9 .product-price__markdown,
.emotion-9 .product-price--markdown {
  color: #666;
}

.emotion-9 .product-price__highlight,
.emotion-9 .product-price--highlight {
  text-transform: none;
  color: #D00000;
}

.emotion-9 .product-price__highlight priceHighlightFont,
.emotion-9 .product-price--highlight priceHighlightFont {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-9 .product-price__highlight--br,
.emotion-9 .product-price--highlight-br {
  text-transform: none;
  font-size: 0.8125rem;
}

.emotion-9 .product-price__markdown {
  text-transform: none;
  color: #666;
}

.emotion-9 .product-price__no-strike {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-9 .product-price__option {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 700;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-9 .product-price__option--br {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-9 .product-price__percentage-off {
  color: #D00000;
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-transform: none;
  padding-left: 5px;
}

.emotion-9 .product-price__percentage-off-no-padding {
  color: #D00000;
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-transform: none;
}

.emotion-9 .product-price__regular {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-9 .product-price__sale {
  color: #666;
}

.emotion-9 .product-price__strike {
  -webkit-text-decoration: line-through;
  text-decoration: line-through;
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-9 .product-price__same-line {
  padding-left: 5px;
}

.emotion-10 {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  color: #666;
  line-height: 1.4;
  text-transform: none;
  font-weight: bold;
  font-size: 0.8rem;
}

<body>
    <div>
      <div
        class="emotion-0"
        data-testid="grid-root"
        spacing="0"
      >
        <div
          class="emotion-1"
          data-testid="grid-root"
          spacing="0"
        >
          <div
            id="subcatHeader_1234"
          />
          <div
            class="emotion-2"
          >
            <h2
              class="emotion-3"
            >
              test group
            </h2>
          </div>
        </div>
        <div
          class="emotion-4"
          data-testid="grid-root"
          spacing="0"
        >
          <div
            class="product-card emotion-5"
          >
            <div
              class="product-card__image-wrapper emotion-6"
              id="product123"
            >
              <a
                aria-hidden="false"
                class="emotion-7"
                href="http://localhost/browse/product.do?pid=123&cid=1234&pcid=23456&grid=source_0_4_1"
                target="_self"
              >
                <img
                  alt="sherpa fleece hoodie"
                  class="product-card__image"
                  src="http://www3.assets-gap.com/webcontent/0013/882/196/cn13882196.jpg"
                />
              </a>
            </div>
            <a
              href="http://localhost/browse/product.do?pid=123&cid=1234&pcid=23456&grid=source_0_4_1"
            >
              <div
                class="emotion-8"
              >
                Sherpa fleece hoodie
              </div>
            </a>
            <div>
              <div
                class="product-card-price"
              >
                <div
                  class="emotion-9"
                >
                  <div>
                    <div
                      class="product-price__markdown"
                    >
                      <span
                        aria-label="price.regular_price_aria_label"
                        class="product-price__no-strike"
                        role="text"
                      >
                        $69.95
                      </span>
                    </div>
                    <div
                      aria-label="price.current_price_range_aria_label"
                      class="product-price__highlight"
                      role="text"
                    >
                      <span
                        class="product-price__highlight"
                      >
                        $49.99
                         - 
                        $54.99
                      </span>
                    </div>
                  </div>
                </div>
              </div>
              <div
                class="product-card__marketing-flag emotion-10"
              >
                Final Sale
              </div>
            </div>
          </div>
        </div>
        <div
          class="emotion-4"
          data-testid="grid-root"
          spacing="0"
        >
          <div
            class="product-card emotion-5"
          >
            <div
              class="product-card__image-wrapper emotion-6"
              id="product123"
            >
              <a
                aria-hidden="false"
                class="emotion-7"
                href="http://localhost/browse/product.do?pid=123&cid=1234&pcid=23456&grid=source_1_4_1"
                target="_self"
              >
                <img
                  alt="sherpa fleece hoodie"
                  class="product-card__image"
                  src="http://www3.assets-gap.com/webcontent/0013/882/196/cn13882196.jpg"
                />
              </a>
            </div>
            <a
              href="http://localhost/browse/product.do?pid=123&cid=1234&pcid=23456&grid=source_1_4_1"
            >
              <div
                class="emotion-8"
              >
                Sherpa fleece hoodie
              </div>
            </a>
            <div>
              <div
                class="product-card-price"
              >
                <div
                  class="emotion-9"
                >
                  <div>
                    <div
                      class="product-price__markdown"
                    >
                      <span
                        aria-label="price.regular_price_aria_label"
                        class="product-price__no-strike"
                        role="text"
                      >
                        $69.95
                      </span>
                    </div>
                    <div
                      aria-label="price.current_price_range_aria_label"
                      class="product-price__highlight"
                      role="text"
                    >
                      <span
                        class="product-price__highlight"
                      >
                        $49.99
                         - 
                        $54.99
                      </span>
                    </div>
                  </div>
                </div>
              </div>
              <div
                class="product-card__marketing-flag emotion-10"
              >
                Final Sale
              </div>
            </div>
          </div>
        </div>
        <div
          class="emotion-4"
          data-testid="grid-root"
          spacing="0"
        >
          <div
            class="product-card emotion-5"
          >
            <div
              class="product-card__image-wrapper emotion-6"
              id="product123"
            >
              <a
                aria-hidden="false"
                class="emotion-7"
                href="http://localhost/browse/product.do?pid=123&cid=1234&pcid=23456&grid=source_2_4_1"
                target="_self"
              >
                <img
                  alt="sherpa fleece hoodie"
                  class="product-card__image"
                  src="http://www3.assets-gap.com/webcontent/0013/882/196/cn13882196.jpg"
                />
              </a>
            </div>
            <a
              href="http://localhost/browse/product.do?pid=123&cid=1234&pcid=23456&grid=source_2_4_1"
            >
              <div
                class="emotion-8"
              >
                Sherpa fleece hoodie
              </div>
            </a>
            <div>
              <div
                class="product-card-price"
              >
                <div
                  class="emotion-9"
                >
                  <div>
                    <div
                      class="product-price__markdown"
                    >
                      <span
                        aria-label="price.regular_price_aria_label"
                        class="product-price__no-strike"
                        role="text"
                      >
                        $69.95
                      </span>
                    </div>
                    <div
                      aria-label="price.current_price_range_aria_label"
                      class="product-price__highlight"
                      role="text"
                    >
                      <span
                        class="product-price__highlight"
                      >
                        $49.99
                         - 
                        $54.99
                      </span>
                    </div>
                  </div>
                </div>
              </div>
              <div
                class="product-card__marketing-flag emotion-10"
              >
                Final Sale
              </div>
            </div>
          </div>
        </div>
        <div
          class="emotion-4"
          data-testid="grid-root"
          spacing="0"
        >
          <div
            class="product-card emotion-5"
          >
            <div
              class="product-card__image-wrapper emotion-6"
              id="product124"
            >
              <a
                aria-hidden="false"
                class="emotion-7"
                href="http://localhost/browse/product.do?pid=124&cid=1234&pcid=23456&grid=source_3_4_1"
                target="_self"
              >
                <img
                  alt="Indigo zip hoodie"
                  class="product-card__image"
                  src="http://www.gap.com/webcontent/0013/707/658/cn13707658.jpg"
                />
              </a>
            </div>
            <a
              href="http://localhost/browse/product.do?pid=124&cid=1234&pcid=23456&grid=source_3_4_1"
            >
              <div
                class="emotion-8"
              >
                Indigo zip hoodie
              </div>
            </a>
            <div>
              <div
                class="product-card-price"
              >
                <div
                  class="emotion-9"
                >
                  <div>
                    <div
                      class="product-price__markdown"
                    >
                      <span
                        aria-label="price.regular_price_aria_label"
                        class="product-price__no-strike"
                        role="text"
                      >
                        $69.95
                      </span>
                    </div>
                    <div
                      aria-label="price.current_price_range_aria_label"
                      class="product-price__highlight"
                      role="text"
                    >
                      <span
                        class="product-price__highlight"
                      >
                        $49.99
                         - 
                        $54.99
                      </span>
                    </div>
                  </div>
                </div>
              </div>
              <div
                class="product-card__marketing-flag emotion-10"
              >
                Final Sale
              </div>
            </div>
          </div>
        </div>
        <div
          class="emotion-1"
          data-testid="grid-root"
          spacing="0"
        >
          <div
            id="subcatHeader_1234"
          />
          <div />
        </div>
      </div>
    </div>
  </body>,
  "container": .emotion-0 {
  box-sizing: border-box;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-flex-wrap: wrap;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
}

.emotion-1 {
  box-sizing: border-box;
  -webkit-flex-basis: 100%;
  -ms-flex-preferred-size: 100%;
  flex-basis: 100%;
  -webkit-box-flex: 0;
  -webkit-flex-grow: 0;
  -ms-flex-positive: 0;
  flex-grow: 0;
  margin: 0;
  max-width: 100%;
  padding: 0em;
}

.emotion-2 {
  display: none;
  overflow: hidden;
  margin: 0;
  margin-bottom: 2rem;
  padding: 0;
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 700;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 1.067rem;
  color: #666;
  line-height: normal;
  text-align: center;
}

.emotion-3 {
  text-transform: uppercase;
  letter-spacing: 0.15em;
  display: inline-block;
  position: relative;
  padding: 0 0.5rem;
}

.emotion-3::before,
.emotion-3::after {
  border-top: 2px solid #666;
  display: block;
  position: absolute;
  width: 100em;
  top: 50%;
  content: '';
}

.emotion-3::before {
  right: 100%;
}

.emotion-3::after {
  left: 100%;
}

.emotion-4 {
  --columnSize: 1;
  box-sizing: border-box;
  -webkit-flex-basis: 33.333333333333336%;
  -ms-flex-preferred-size: 33.333333333333336%;
  flex-basis: 33.333333333333336%;
  -webkit-box-flex: 0;
  -webkit-flex-grow: 0;
  -ms-flex-positive: 0;
  flex-grow: 0;
  margin: 0;
  max-width: 33.333333333333336%;
  padding: 0em;
}

.emotion-5 {
  min-height: 430px;
  position: relative;
}

.emotion-5 .product-card__image {
  width: 100%;
  display: block;
}

.emotion-6 {
  margin-bottom: 0.5rem;
}

.emotion-8 {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  color: #666;
  line-height: 1.4;
  text-transform: none;
  font-weight: normal;
  font-size: 0.8rem;
}

.emotion-9 {
  text-transform: none;
  line-height: 1.4;
  color: #666;
  font-size: 0.8rem!important;
  letter-spacing: normal;
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-9 .product-price__markdown,
.emotion-9 .product-price--markdown {
  color: #666;
}

.emotion-9 .product-price__highlight,
.emotion-9 .product-price--highlight {
  text-transform: none;
  color: #D00000;
}

.emotion-9 .product-price__highlight priceHighlightFont,
.emotion-9 .product-price--highlight priceHighlightFont {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-9 .product-price__highlight--br,
.emotion-9 .product-price--highlight-br {
  text-transform: none;
  font-size: 0.8125rem;
}

.emotion-9 .product-price__markdown {
  text-transform: none;
  color: #666;
}

.emotion-9 .product-price__no-strike {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-9 .product-price__option {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 700;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-9 .product-price__option--br {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-9 .product-price__percentage-off {
  color: #D00000;
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-transform: none;
  padding-left: 5px;
}

.emotion-9 .product-price__percentage-off-no-padding {
  color: #D00000;
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-transform: none;
}

.emotion-9 .product-price__regular {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-9 .product-price__sale {
  color: #666;
}

.emotion-9 .product-price__strike {
  -webkit-text-decoration: line-through;
  text-decoration: line-through;
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-9 .product-price__same-line {
  padding-left: 5px;
}

.emotion-10 {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  color: #666;
  line-height: 1.4;
  text-transform: none;
  font-weight: bold;
  font-size: 0.8rem;
}

<div>
    <div
      class="emotion-0"
      data-testid="grid-root"
      spacing="0"
    >
      <div
        class="emotion-1"
        data-testid="grid-root"
        spacing="0"
      >
        <div
          id="subcatHeader_1234"
        />
        <div
          class="emotion-2"
        >
          <h2
            class="emotion-3"
          >
            test group
          </h2>
        </div>
      </div>
      <div
        class="emotion-4"
        data-testid="grid-root"
        spacing="0"
      >
        <div
          class="product-card emotion-5"
        >
          <div
            class="product-card__image-wrapper emotion-6"
            id="product123"
          >
            <a
              aria-hidden="false"
              class="emotion-7"
              href="http://localhost/browse/product.do?pid=123&cid=1234&pcid=23456&grid=source_0_4_1"
              target="_self"
            >
              <img
                alt="sherpa fleece hoodie"
                class="product-card__image"
                src="http://www3.assets-gap.com/webcontent/0013/882/196/cn13882196.jpg"
              />
            </a>
          </div>
          <a
            href="http://localhost/browse/product.do?pid=123&cid=1234&pcid=23456&grid=source_0_4_1"
          >
            <div
              class="emotion-8"
            >
              Sherpa fleece hoodie
            </div>
          </a>
          <div>
            <div
              class="product-card-price"
            >
              <div
                class="emotion-9"
              >
                <div>
                  <div
                    class="product-price__markdown"
                  >
                    <span
                      aria-label="price.regular_price_aria_label"
                      class="product-price__no-strike"
                      role="text"
                    >
                      $69.95
                    </span>
                  </div>
                  <div
                    aria-label="price.current_price_range_aria_label"
                    class="product-price__highlight"
                    role="text"
                  >
                    <span
                      class="product-price__highlight"
                    >
                      $49.99
                       - 
                      $54.99
                    </span>
                  </div>
                </div>
              </div>
            </div>
            <div
              class="product-card__marketing-flag emotion-10"
            >
              Final Sale
            </div>
          </div>
        </div>
      </div>
      <div
        class="emotion-4"
        data-testid="grid-root"
        spacing="0"
      >
        <div
          class="product-card emotion-5"
        >
          <div
            class="product-card__image-wrapper emotion-6"
            id="product123"
          >
            <a
              aria-hidden="false"
              class="emotion-7"
              href="http://localhost/browse/product.do?pid=123&cid=1234&pcid=23456&grid=source_1_4_1"
              target="_self"
            >
              <img
                alt="sherpa fleece hoodie"
                class="product-card__image"
                src="http://www3.assets-gap.com/webcontent/0013/882/196/cn13882196.jpg"
              />
            </a>
          </div>
          <a
            href="http://localhost/browse/product.do?pid=123&cid=1234&pcid=23456&grid=source_1_4_1"
          >
            <div
              class="emotion-8"
            >
              Sherpa fleece hoodie
            </div>
          </a>
          <div>
            <div
              class="product-card-price"
            >
              <div
                class="emotion-9"
              >
                <div>
                  <div
                    class="product-price__markdown"
                  >
                    <span
                      aria-label="price.regular_price_aria_label"
                      class="product-price__no-strike"
                      role="text"
                    >
                      $69.95
                    </span>
                  </div>
                  <div
                    aria-label="price.current_price_range_aria_label"
                    class="product-price__highlight"
                    role="text"
                  >
                    <span
                      class="product-price__highlight"
                    >
                      $49.99
                       - 
                      $54.99
                    </span>
                  </div>
                </div>
              </div>
            </div>
            <div
              class="product-card__marketing-flag emotion-10"
            >
              Final Sale
            </div>
          </div>
        </div>
      </div>
      <div
        class="emotion-4"
        data-testid="grid-root"
        spacing="0"
      >
        <div
          class="product-card emotion-5"
        >
          <div
            class="product-card__image-wrapper emotion-6"
            id="product123"
          >
            <a
              aria-hidden="false"
              class="emotion-7"
              href="http://localhost/browse/product.do?pid=123&cid=1234&pcid=23456&grid=source_2_4_1"
              target="_self"
            >
              <img
                alt="sherpa fleece hoodie"
                class="product-card__image"
                src="http://www3.assets-gap.com/webcontent/0013/882/196/cn13882196.jpg"
              />
            </a>
          </div>
          <a
            href="http://localhost/browse/product.do?pid=123&cid=1234&pcid=23456&grid=source_2_4_1"
          >
            <div
              class="emotion-8"
            >
              Sherpa fleece hoodie
            </div>
          </a>
          <div>
            <div
              class="product-card-price"
            >
              <div
                class="emotion-9"
              >
                <div>
                  <div
                    class="product-price__markdown"
                  >
                    <span
                      aria-label="price.regular_price_aria_label"
                      class="product-price__no-strike"
                      role="text"
                    >
                      $69.95
                    </span>
                  </div>
                  <div
                    aria-label="price.current_price_range_aria_label"
                    class="product-price__highlight"
                    role="text"
                  >
                    <span
                      class="product-price__highlight"
                    >
                      $49.99
                       - 
                      $54.99
                    </span>
                  </div>
                </div>
              </div>
            </div>
            <div
              class="product-card__marketing-flag emotion-10"
            >
              Final Sale
            </div>
          </div>
        </div>
      </div>
      <div
        class="emotion-4"
        data-testid="grid-root"
        spacing="0"
      >
        <div
          class="product-card emotion-5"
        >
          <div
            class="product-card__image-wrapper emotion-6"
            id="product124"
          >
            <a
              aria-hidden="false"
              class="emotion-7"
              href="http://localhost/browse/product.do?pid=124&cid=1234&pcid=23456&grid=source_3_4_1"
              target="_self"
            >
              <img
                alt="Indigo zip hoodie"
                class="product-card__image"
                src="http://www.gap.com/webcontent/0013/707/658/cn13707658.jpg"
              />
            </a>
          </div>
          <a
            href="http://localhost/browse/product.do?pid=124&cid=1234&pcid=23456&grid=source_3_4_1"
          >
            <div
              class="emotion-8"
            >
              Indigo zip hoodie
            </div>
          </a>
          <div>
            <div
              class="product-card-price"
            >
              <div
                class="emotion-9"
              >
                <div>
                  <div
                    class="product-price__markdown"
                  >
                    <span
                      aria-label="price.regular_price_aria_label"
                      class="product-price__no-strike"
                      role="text"
                    >
                      $69.95
                    </span>
                  </div>
                  <div
                    aria-label="price.current_price_range_aria_label"
                    class="product-price__highlight"
                    role="text"
                  >
                    <span
                      class="product-price__highlight"
                    >
                      $49.99
                       - 
                      $54.99
                    </span>
                  </div>
                </div>
              </div>
            </div>
            <div
              class="product-card__marketing-flag emotion-10"
            >
              Final Sale
            </div>
          </div>
        </div>
      </div>
      <div
        class="emotion-1"
        data-testid="grid-root"
        spacing="0"
      >
        <div
          id="subcatHeader_1234"
        />
        <div />
      </div>
    </div>
  </div>,
  "debug": [Function],
  "findAllByAltText": [Function],
  "findAllByDisplayValue": [Function],
  "findAllByLabelText": [Function],
  "findAllByPlaceholderText": [Function],
  "findAllByRole": [Function],
  "findAllByTestId": [Function],
  "findAllByText": [Function],
  "findAllByTitle": [Function],
  "findByAltText": [Function],
  "findByDisplayValue": [Function],
  "findByLabelText": [Function],
  "findByPlaceholderText": [Function],
  "findByRole": [Function],
  "findByTestId": [Function],
  "findByText": [Function],
  "findByTitle": [Function],
  "getAllByAltText": [Function],
  "getAllByDisplayValue": [Function],
  "getAllByLabelText": [Function],
  "getAllByPlaceholderText": [Function],
  "getAllByRole": [Function],
  "getAllByTestId": [Function],
  "getAllByText": [Function],
  "getAllByTitle": [Function],
  "getByAltText": [Function],
  "getByDisplayValue": [Function],
  "getByLabelText": [Function],
  "getByPlaceholderText": [Function],
  "getByRole": [Function],
  "getByTestId": [Function],
  "getByText": [Function],
  "getByTitle": [Function],
  "queryAllByAltText": [Function],
  "queryAllByDisplayValue": [Function],
  "queryAllByLabelText": [Function],
  "queryAllByPlaceholderText": [Function],
  "queryAllByRole": [Function],
  "queryAllByTestId": [Function],
  "queryAllByText": [Function],
  "queryAllByTitle": [Function],
  "queryByAltText": [Function],
  "queryByDisplayValue": [Function],
  "queryByLabelText": [Function],
  "queryByPlaceholderText": [Function],
  "queryByRole": [Function],
  "queryByTestId": [Function],
  "queryByText": [Function],
  "queryByTitle": [Function],
  "rerender": [Function],
  "unmount": [Function],
}
`;

exports[`<ProductGroup /> renders correctly 1`] = `
{
  "asFragment": [Function],
  "baseElement": .emotion-0 {
  box-sizing: border-box;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-flex-wrap: wrap;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
}

.emotion-1 {
  box-sizing: border-box;
  -webkit-flex-basis: 100%;
  -ms-flex-preferred-size: 100%;
  flex-basis: 100%;
  -webkit-box-flex: 0;
  -webkit-flex-grow: 0;
  -ms-flex-positive: 0;
  flex-grow: 0;
  margin: 0;
  max-width: 100%;
  padding: 0em;
}

.emotion-2 {
  overflow: hidden;
  margin: 0;
  margin-bottom: 2rem;
  padding: 0;
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 700;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 1.067rem;
  color: #666;
  line-height: normal;
  text-align: center;
}

.emotion-3 {
  text-transform: uppercase;
  letter-spacing: 0.15em;
  display: inline-block;
  position: relative;
  padding: 0 0.5rem;
}

.emotion-3::before,
.emotion-3::after {
  border-top: 2px solid #666;
  display: block;
  position: absolute;
  width: 100em;
  top: 50%;
  content: '';
}

.emotion-3::before {
  right: 100%;
}

.emotion-3::after {
  left: 100%;
}

.emotion-4 {
  --columnSize: 1;
  box-sizing: border-box;
  -webkit-flex-basis: 33.333333333333336%;
  -ms-flex-preferred-size: 33.333333333333336%;
  flex-basis: 33.333333333333336%;
  -webkit-box-flex: 0;
  -webkit-flex-grow: 0;
  -ms-flex-positive: 0;
  flex-grow: 0;
  margin: 0;
  max-width: 33.333333333333336%;
  padding: 0.5em;
}

.emotion-5 {
  min-height: 430px;
  position: relative;
}

.emotion-5 .product-card__image {
  width: 100%;
  display: block;
}

.emotion-6 {
  margin-bottom: 0.5rem;
}

.emotion-8 {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  color: #666;
  line-height: 1.4;
  text-transform: none;
  font-weight: normal;
  font-size: 0.8rem;
}

.emotion-9 {
  text-transform: none;
  line-height: 1.4;
  color: #666;
  font-size: 0.8rem!important;
  letter-spacing: normal;
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-9 .product-price__markdown,
.emotion-9 .product-price--markdown {
  color: #666;
}

.emotion-9 .product-price__highlight,
.emotion-9 .product-price--highlight {
  text-transform: none;
  color: #D00000;
}

.emotion-9 .product-price__highlight priceHighlightFont,
.emotion-9 .product-price--highlight priceHighlightFont {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-9 .product-price__highlight--br,
.emotion-9 .product-price--highlight-br {
  text-transform: none;
  font-size: 0.8125rem;
}

.emotion-9 .product-price__markdown {
  text-transform: none;
  color: #666;
}

.emotion-9 .product-price__no-strike {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-9 .product-price__option {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 700;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-9 .product-price__option--br {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-9 .product-price__percentage-off {
  color: #D00000;
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-transform: none;
  padding-left: 5px;
}

.emotion-9 .product-price__percentage-off-no-padding {
  color: #D00000;
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-transform: none;
}

.emotion-9 .product-price__regular {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-9 .product-price__sale {
  color: #666;
}

.emotion-9 .product-price__strike {
  -webkit-text-decoration: line-through;
  text-decoration: line-through;
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-9 .product-price__same-line {
  padding-left: 5px;
}

.emotion-10 {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  color: #666;
  line-height: 1.4;
  text-transform: none;
  font-weight: bold;
  font-size: 0.8rem;
}

<body>
    <div>
      <div
        class="emotion-0"
        data-testid="grid-root"
        spacing="0.5"
      >
        <div
          class="emotion-1"
          data-testid="grid-root"
          spacing="0"
        >
          <div
            id="subcatHeader_1234"
          />
          <div
            class="emotion-2"
          >
            <h2
              class="emotion-3"
            >
              test group
            </h2>
          </div>
        </div>
        <div
          class="emotion-4"
          data-testid="grid-root"
          spacing="0.5"
        >
          <div
            class="product-card emotion-5"
          >
            <div
              class="product-card__image-wrapper emotion-6"
              id="product123"
            >
              <a
                aria-hidden="false"
                class="emotion-7"
                href="http://localhost/browse/product.do?pid=123&cid=1234&pcid=23456&grid=source_0_4_1"
                target="_self"
              >
                <img
                  alt="sherpa fleece hoodie"
                  class="product-card__image"
                  src="http://www3.assets-gap.com/webcontent/0013/882/196/cn13882196.jpg"
                />
              </a>
            </div>
            <a
              href="http://localhost/browse/product.do?pid=123&cid=1234&pcid=23456&grid=source_0_4_1"
            >
              <div
                class="emotion-8"
              >
                Sherpa fleece hoodie
              </div>
            </a>
            <div>
              <div
                class="product-card-price"
              >
                <div
                  class="emotion-9"
                >
                  <div>
                    <div
                      class="product-price__markdown"
                    >
                      <span
                        aria-label="price.regular_price_aria_label"
                        class="product-price__no-strike"
                        role="text"
                      >
                        $69.95
                      </span>
                    </div>
                    <div
                      aria-label="price.current_price_range_aria_label"
                      class="product-price__highlight"
                      role="text"
                    >
                      <span
                        class="product-price__highlight"
                      >
                        $49.99
                         - 
                        $54.99
                      </span>
                    </div>
                  </div>
                </div>
              </div>
              <div
                class="product-card__marketing-flag emotion-10"
              >
                Final Sale
              </div>
            </div>
          </div>
        </div>
        <div
          class="emotion-4"
          data-testid="grid-root"
          spacing="0.5"
        >
          <div
            class="product-card emotion-5"
          >
            <div
              class="product-card__image-wrapper emotion-6"
              id="product123"
            >
              <a
                aria-hidden="false"
                class="emotion-7"
                href="http://localhost/browse/product.do?pid=123&cid=1234&pcid=23456&grid=source_1_4_1"
                target="_self"
              >
                <img
                  alt="sherpa fleece hoodie"
                  class="product-card__image"
                  src="http://www3.assets-gap.com/webcontent/0013/882/196/cn13882196.jpg"
                />
              </a>
            </div>
            <a
              href="http://localhost/browse/product.do?pid=123&cid=1234&pcid=23456&grid=source_1_4_1"
            >
              <div
                class="emotion-8"
              >
                Sherpa fleece hoodie
              </div>
            </a>
            <div>
              <div
                class="product-card-price"
              >
                <div
                  class="emotion-9"
                >
                  <div>
                    <div
                      class="product-price__markdown"
                    >
                      <span
                        aria-label="price.regular_price_aria_label"
                        class="product-price__no-strike"
                        role="text"
                      >
                        $69.95
                      </span>
                    </div>
                    <div
                      aria-label="price.current_price_range_aria_label"
                      class="product-price__highlight"
                      role="text"
                    >
                      <span
                        class="product-price__highlight"
                      >
                        $49.99
                         - 
                        $54.99
                      </span>
                    </div>
                  </div>
                </div>
              </div>
              <div
                class="product-card__marketing-flag emotion-10"
              >
                Final Sale
              </div>
            </div>
          </div>
        </div>
        <div
          class="emotion-4"
          data-testid="grid-root"
          spacing="0.5"
        >
          <div
            class="product-card emotion-5"
          >
            <div
              class="product-card__image-wrapper emotion-6"
              id="product123"
            >
              <a
                aria-hidden="false"
                class="emotion-7"
                href="http://localhost/browse/product.do?pid=123&cid=1234&pcid=23456&grid=source_2_4_1"
                target="_self"
              >
                <img
                  alt="sherpa fleece hoodie"
                  class="product-card__image"
                  src="http://www3.assets-gap.com/webcontent/0013/882/196/cn13882196.jpg"
                />
              </a>
            </div>
            <a
              href="http://localhost/browse/product.do?pid=123&cid=1234&pcid=23456&grid=source_2_4_1"
            >
              <div
                class="emotion-8"
              >
                Sherpa fleece hoodie
              </div>
            </a>
            <div>
              <div
                class="product-card-price"
              >
                <div
                  class="emotion-9"
                >
                  <div>
                    <div
                      class="product-price__markdown"
                    >
                      <span
                        aria-label="price.regular_price_aria_label"
                        class="product-price__no-strike"
                        role="text"
                      >
                        $69.95
                      </span>
                    </div>
                    <div
                      aria-label="price.current_price_range_aria_label"
                      class="product-price__highlight"
                      role="text"
                    >
                      <span
                        class="product-price__highlight"
                      >
                        $49.99
                         - 
                        $54.99
                      </span>
                    </div>
                  </div>
                </div>
              </div>
              <div
                class="product-card__marketing-flag emotion-10"
              >
                Final Sale
              </div>
            </div>
          </div>
        </div>
        <div
          class="emotion-4"
          data-testid="grid-root"
          spacing="0.5"
        >
          <div
            class="product-card emotion-5"
          >
            <div
              class="product-card__image-wrapper emotion-6"
              id="product124"
            >
              <a
                aria-hidden="false"
                class="emotion-7"
                href="http://localhost/browse/product.do?pid=124&cid=1234&pcid=23456&grid=source_3_4_1"
                target="_self"
              >
                <img
                  alt="Indigo zip hoodie"
                  class="product-card__image"
                  src="http://www.gap.com/webcontent/0013/707/658/cn13707658.jpg"
                />
              </a>
            </div>
            <a
              href="http://localhost/browse/product.do?pid=124&cid=1234&pcid=23456&grid=source_3_4_1"
            >
              <div
                class="emotion-8"
              >
                Indigo zip hoodie
              </div>
            </a>
            <div>
              <div
                class="product-card-price"
              >
                <div
                  class="emotion-9"
                >
                  <div>
                    <div
                      class="product-price__markdown"
                    >
                      <span
                        aria-label="price.regular_price_aria_label"
                        class="product-price__no-strike"
                        role="text"
                      >
                        $69.95
                      </span>
                    </div>
                    <div
                      aria-label="price.current_price_range_aria_label"
                      class="product-price__highlight"
                      role="text"
                    >
                      <span
                        class="product-price__highlight"
                      >
                        $49.99
                         - 
                        $54.99
                      </span>
                    </div>
                  </div>
                </div>
              </div>
              <div
                class="product-card__marketing-flag emotion-10"
              >
                Final Sale
              </div>
            </div>
          </div>
        </div>
        <div
          class="emotion-1"
          data-testid="grid-root"
          spacing="0"
        >
          <div
            id="subcatHeader_1234"
          />
          <div />
        </div>
      </div>
    </div>
  </body>,
  "container": .emotion-0 {
  box-sizing: border-box;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-flex-wrap: wrap;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
}

.emotion-1 {
  box-sizing: border-box;
  -webkit-flex-basis: 100%;
  -ms-flex-preferred-size: 100%;
  flex-basis: 100%;
  -webkit-box-flex: 0;
  -webkit-flex-grow: 0;
  -ms-flex-positive: 0;
  flex-grow: 0;
  margin: 0;
  max-width: 100%;
  padding: 0em;
}

.emotion-2 {
  overflow: hidden;
  margin: 0;
  margin-bottom: 2rem;
  padding: 0;
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 700;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 1.067rem;
  color: #666;
  line-height: normal;
  text-align: center;
}

.emotion-3 {
  text-transform: uppercase;
  letter-spacing: 0.15em;
  display: inline-block;
  position: relative;
  padding: 0 0.5rem;
}

.emotion-3::before,
.emotion-3::after {
  border-top: 2px solid #666;
  display: block;
  position: absolute;
  width: 100em;
  top: 50%;
  content: '';
}

.emotion-3::before {
  right: 100%;
}

.emotion-3::after {
  left: 100%;
}

.emotion-4 {
  --columnSize: 1;
  box-sizing: border-box;
  -webkit-flex-basis: 33.333333333333336%;
  -ms-flex-preferred-size: 33.333333333333336%;
  flex-basis: 33.333333333333336%;
  -webkit-box-flex: 0;
  -webkit-flex-grow: 0;
  -ms-flex-positive: 0;
  flex-grow: 0;
  margin: 0;
  max-width: 33.333333333333336%;
  padding: 0.5em;
}

.emotion-5 {
  min-height: 430px;
  position: relative;
}

.emotion-5 .product-card__image {
  width: 100%;
  display: block;
}

.emotion-6 {
  margin-bottom: 0.5rem;
}

.emotion-8 {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  color: #666;
  line-height: 1.4;
  text-transform: none;
  font-weight: normal;
  font-size: 0.8rem;
}

.emotion-9 {
  text-transform: none;
  line-height: 1.4;
  color: #666;
  font-size: 0.8rem!important;
  letter-spacing: normal;
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-9 .product-price__markdown,
.emotion-9 .product-price--markdown {
  color: #666;
}

.emotion-9 .product-price__highlight,
.emotion-9 .product-price--highlight {
  text-transform: none;
  color: #D00000;
}

.emotion-9 .product-price__highlight priceHighlightFont,
.emotion-9 .product-price--highlight priceHighlightFont {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-9 .product-price__highlight--br,
.emotion-9 .product-price--highlight-br {
  text-transform: none;
  font-size: 0.8125rem;
}

.emotion-9 .product-price__markdown {
  text-transform: none;
  color: #666;
}

.emotion-9 .product-price__no-strike {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-9 .product-price__option {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 700;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-9 .product-price__option--br {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-9 .product-price__percentage-off {
  color: #D00000;
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-transform: none;
  padding-left: 5px;
}

.emotion-9 .product-price__percentage-off-no-padding {
  color: #D00000;
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-transform: none;
}

.emotion-9 .product-price__regular {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-9 .product-price__sale {
  color: #666;
}

.emotion-9 .product-price__strike {
  -webkit-text-decoration: line-through;
  text-decoration: line-through;
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-9 .product-price__same-line {
  padding-left: 5px;
}

.emotion-10 {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  color: #666;
  line-height: 1.4;
  text-transform: none;
  font-weight: bold;
  font-size: 0.8rem;
}

<div>
    <div
      class="emotion-0"
      data-testid="grid-root"
      spacing="0.5"
    >
      <div
        class="emotion-1"
        data-testid="grid-root"
        spacing="0"
      >
        <div
          id="subcatHeader_1234"
        />
        <div
          class="emotion-2"
        >
          <h2
            class="emotion-3"
          >
            test group
          </h2>
        </div>
      </div>
      <div
        class="emotion-4"
        data-testid="grid-root"
        spacing="0.5"
      >
        <div
          class="product-card emotion-5"
        >
          <div
            class="product-card__image-wrapper emotion-6"
            id="product123"
          >
            <a
              aria-hidden="false"
              class="emotion-7"
              href="http://localhost/browse/product.do?pid=123&cid=1234&pcid=23456&grid=source_0_4_1"
              target="_self"
            >
              <img
                alt="sherpa fleece hoodie"
                class="product-card__image"
                src="http://www3.assets-gap.com/webcontent/0013/882/196/cn13882196.jpg"
              />
            </a>
          </div>
          <a
            href="http://localhost/browse/product.do?pid=123&cid=1234&pcid=23456&grid=source_0_4_1"
          >
            <div
              class="emotion-8"
            >
              Sherpa fleece hoodie
            </div>
          </a>
          <div>
            <div
              class="product-card-price"
            >
              <div
                class="emotion-9"
              >
                <div>
                  <div
                    class="product-price__markdown"
                  >
                    <span
                      aria-label="price.regular_price_aria_label"
                      class="product-price__no-strike"
                      role="text"
                    >
                      $69.95
                    </span>
                  </div>
                  <div
                    aria-label="price.current_price_range_aria_label"
                    class="product-price__highlight"
                    role="text"
                  >
                    <span
                      class="product-price__highlight"
                    >
                      $49.99
                       - 
                      $54.99
                    </span>
                  </div>
                </div>
              </div>
            </div>
            <div
              class="product-card__marketing-flag emotion-10"
            >
              Final Sale
            </div>
          </div>
        </div>
      </div>
      <div
        class="emotion-4"
        data-testid="grid-root"
        spacing="0.5"
      >
        <div
          class="product-card emotion-5"
        >
          <div
            class="product-card__image-wrapper emotion-6"
            id="product123"
          >
            <a
              aria-hidden="false"
              class="emotion-7"
              href="http://localhost/browse/product.do?pid=123&cid=1234&pcid=23456&grid=source_1_4_1"
              target="_self"
            >
              <img
                alt="sherpa fleece hoodie"
                class="product-card__image"
                src="http://www3.assets-gap.com/webcontent/0013/882/196/cn13882196.jpg"
              />
            </a>
          </div>
          <a
            href="http://localhost/browse/product.do?pid=123&cid=1234&pcid=23456&grid=source_1_4_1"
          >
            <div
              class="emotion-8"
            >
              Sherpa fleece hoodie
            </div>
          </a>
          <div>
            <div
              class="product-card-price"
            >
              <div
                class="emotion-9"
              >
                <div>
                  <div
                    class="product-price__markdown"
                  >
                    <span
                      aria-label="price.regular_price_aria_label"
                      class="product-price__no-strike"
                      role="text"
                    >
                      $69.95
                    </span>
                  </div>
                  <div
                    aria-label="price.current_price_range_aria_label"
                    class="product-price__highlight"
                    role="text"
                  >
                    <span
                      class="product-price__highlight"
                    >
                      $49.99
                       - 
                      $54.99
                    </span>
                  </div>
                </div>
              </div>
            </div>
            <div
              class="product-card__marketing-flag emotion-10"
            >
              Final Sale
            </div>
          </div>
        </div>
      </div>
      <div
        class="emotion-4"
        data-testid="grid-root"
        spacing="0.5"
      >
        <div
          class="product-card emotion-5"
        >
          <div
            class="product-card__image-wrapper emotion-6"
            id="product123"
          >
            <a
              aria-hidden="false"
              class="emotion-7"
              href="http://localhost/browse/product.do?pid=123&cid=1234&pcid=23456&grid=source_2_4_1"
              target="_self"
            >
              <img
                alt="sherpa fleece hoodie"
                class="product-card__image"
                src="http://www3.assets-gap.com/webcontent/0013/882/196/cn13882196.jpg"
              />
            </a>
          </div>
          <a
            href="http://localhost/browse/product.do?pid=123&cid=1234&pcid=23456&grid=source_2_4_1"
          >
            <div
              class="emotion-8"
            >
              Sherpa fleece hoodie
            </div>
          </a>
          <div>
            <div
              class="product-card-price"
            >
              <div
                class="emotion-9"
              >
                <div>
                  <div
                    class="product-price__markdown"
                  >
                    <span
                      aria-label="price.regular_price_aria_label"
                      class="product-price__no-strike"
                      role="text"
                    >
                      $69.95
                    </span>
                  </div>
                  <div
                    aria-label="price.current_price_range_aria_label"
                    class="product-price__highlight"
                    role="text"
                  >
                    <span
                      class="product-price__highlight"
                    >
                      $49.99
                       - 
                      $54.99
                    </span>
                  </div>
                </div>
              </div>
            </div>
            <div
              class="product-card__marketing-flag emotion-10"
            >
              Final Sale
            </div>
          </div>
        </div>
      </div>
      <div
        class="emotion-4"
        data-testid="grid-root"
        spacing="0.5"
      >
        <div
          class="product-card emotion-5"
        >
          <div
            class="product-card__image-wrapper emotion-6"
            id="product124"
          >
            <a
              aria-hidden="false"
              class="emotion-7"
              href="http://localhost/browse/product.do?pid=124&cid=1234&pcid=23456&grid=source_3_4_1"
              target="_self"
            >
              <img
                alt="Indigo zip hoodie"
                class="product-card__image"
                src="http://www.gap.com/webcontent/0013/707/658/cn13707658.jpg"
              />
            </a>
          </div>
          <a
            href="http://localhost/browse/product.do?pid=124&cid=1234&pcid=23456&grid=source_3_4_1"
          >
            <div
              class="emotion-8"
            >
              Indigo zip hoodie
            </div>
          </a>
          <div>
            <div
              class="product-card-price"
            >
              <div
                class="emotion-9"
              >
                <div>
                  <div
                    class="product-price__markdown"
                  >
                    <span
                      aria-label="price.regular_price_aria_label"
                      class="product-price__no-strike"
                      role="text"
                    >
                      $69.95
                    </span>
                  </div>
                  <div
                    aria-label="price.current_price_range_aria_label"
                    class="product-price__highlight"
                    role="text"
                  >
                    <span
                      class="product-price__highlight"
                    >
                      $49.99
                       - 
                      $54.99
                    </span>
                  </div>
                </div>
              </div>
            </div>
            <div
              class="product-card__marketing-flag emotion-10"
            >
              Final Sale
            </div>
          </div>
        </div>
      </div>
      <div
        class="emotion-1"
        data-testid="grid-root"
        spacing="0"
      >
        <div
          id="subcatHeader_1234"
        />
        <div />
      </div>
    </div>
  </div>,
  "debug": [Function],
  "findAllByAltText": [Function],
  "findAllByDisplayValue": [Function],
  "findAllByLabelText": [Function],
  "findAllByPlaceholderText": [Function],
  "findAllByRole": [Function],
  "findAllByTestId": [Function],
  "findAllByText": [Function],
  "findAllByTitle": [Function],
  "findByAltText": [Function],
  "findByDisplayValue": [Function],
  "findByLabelText": [Function],
  "findByPlaceholderText": [Function],
  "findByRole": [Function],
  "findByTestId": [Function],
  "findByText": [Function],
  "findByTitle": [Function],
  "getAllByAltText": [Function],
  "getAllByDisplayValue": [Function],
  "getAllByLabelText": [Function],
  "getAllByPlaceholderText": [Function],
  "getAllByRole": [Function],
  "getAllByTestId": [Function],
  "getAllByText": [Function],
  "getAllByTitle": [Function],
  "getByAltText": [Function],
  "getByDisplayValue": [Function],
  "getByLabelText": [Function],
  "getByPlaceholderText": [Function],
  "getByRole": [Function],
  "getByTestId": [Function],
  "getByText": [Function],
  "getByTitle": [Function],
  "queryAllByAltText": [Function],
  "queryAllByDisplayValue": [Function],
  "queryAllByLabelText": [Function],
  "queryAllByPlaceholderText": [Function],
  "queryAllByRole": [Function],
  "queryAllByTestId": [Function],
  "queryAllByText": [Function],
  "queryAllByTitle": [Function],
  "queryByAltText": [Function],
  "queryByDisplayValue": [Function],
  "queryByLabelText": [Function],
  "queryByPlaceholderText": [Function],
  "queryByRole": [Function],
  "queryByTestId": [Function],
  "queryByText": [Function],
  "queryByTitle": [Function],
  "rerender": [Function],
  "unmount": [Function],
}
`;

exports[`<ProductGroup /> renders correctly a lazy grid 1`] = `
{
  "asFragment": [Function],
  "baseElement": .emotion-0 {
  box-sizing: border-box;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-flex-wrap: wrap;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
}

.emotion-1 {
  box-sizing: border-box;
  -webkit-flex-basis: 100%;
  -ms-flex-preferred-size: 100%;
  flex-basis: 100%;
  -webkit-box-flex: 0;
  -webkit-flex-grow: 0;
  -ms-flex-positive: 0;
  flex-grow: 0;
  margin: 0;
  max-width: 100%;
  padding: 0em;
}

.emotion-2 {
  --columnSize: 1;
  box-sizing: border-box;
  -webkit-flex-basis: 33.333333333333336%;
  -ms-flex-preferred-size: 33.333333333333336%;
  flex-basis: 33.333333333333336%;
  -webkit-box-flex: 0;
  -webkit-flex-grow: 0;
  -ms-flex-positive: 0;
  flex-grow: 0;
  margin: 0;
  max-width: 33.333333333333336%;
  padding: 0.5em;
}

<body>
    <div>
      <div
        class="emotion-0"
        data-testid="grid-root"
        spacing="0.5"
      >
        <div
          class="emotion-1"
          data-testid="grid-root"
          spacing="0"
        />
        <div
          class="emotion-2"
          data-testid="grid-root"
          spacing="0.5"
        />
        <div
          class="emotion-2"
          data-testid="grid-root"
          spacing="0.5"
        />
        <div
          class="emotion-2"
          data-testid="grid-root"
          spacing="0.5"
        />
        <div
          class="emotion-2"
          data-testid="grid-root"
          spacing="0.5"
        />
        <div
          class="emotion-1"
          data-testid="grid-root"
          spacing="0"
        />
      </div>
    </div>
  </body>,
  "container": .emotion-0 {
  box-sizing: border-box;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-flex-wrap: wrap;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
}

.emotion-1 {
  box-sizing: border-box;
  -webkit-flex-basis: 100%;
  -ms-flex-preferred-size: 100%;
  flex-basis: 100%;
  -webkit-box-flex: 0;
  -webkit-flex-grow: 0;
  -ms-flex-positive: 0;
  flex-grow: 0;
  margin: 0;
  max-width: 100%;
  padding: 0em;
}

.emotion-2 {
  --columnSize: 1;
  box-sizing: border-box;
  -webkit-flex-basis: 33.333333333333336%;
  -ms-flex-preferred-size: 33.333333333333336%;
  flex-basis: 33.333333333333336%;
  -webkit-box-flex: 0;
  -webkit-flex-grow: 0;
  -ms-flex-positive: 0;
  flex-grow: 0;
  margin: 0;
  max-width: 33.333333333333336%;
  padding: 0.5em;
}

<div>
    <div
      class="emotion-0"
      data-testid="grid-root"
      spacing="0.5"
    >
      <div
        class="emotion-1"
        data-testid="grid-root"
        spacing="0"
      />
      <div
        class="emotion-2"
        data-testid="grid-root"
        spacing="0.5"
      />
      <div
        class="emotion-2"
        data-testid="grid-root"
        spacing="0.5"
      />
      <div
        class="emotion-2"
        data-testid="grid-root"
        spacing="0.5"
      />
      <div
        class="emotion-2"
        data-testid="grid-root"
        spacing="0.5"
      />
      <div
        class="emotion-1"
        data-testid="grid-root"
        spacing="0"
      />
    </div>
  </div>,
  "debug": [Function],
  "findAllByAltText": [Function],
  "findAllByDisplayValue": [Function],
  "findAllByLabelText": [Function],
  "findAllByPlaceholderText": [Function],
  "findAllByRole": [Function],
  "findAllByTestId": [Function],
  "findAllByText": [Function],
  "findAllByTitle": [Function],
  "findByAltText": [Function],
  "findByDisplayValue": [Function],
  "findByLabelText": [Function],
  "findByPlaceholderText": [Function],
  "findByRole": [Function],
  "findByTestId": [Function],
  "findByText": [Function],
  "findByTitle": [Function],
  "getAllByAltText": [Function],
  "getAllByDisplayValue": [Function],
  "getAllByLabelText": [Function],
  "getAllByPlaceholderText": [Function],
  "getAllByRole": [Function],
  "getAllByTestId": [Function],
  "getAllByText": [Function],
  "getAllByTitle": [Function],
  "getByAltText": [Function],
  "getByDisplayValue": [Function],
  "getByLabelText": [Function],
  "getByPlaceholderText": [Function],
  "getByRole": [Function],
  "getByTestId": [Function],
  "getByText": [Function],
  "getByTitle": [Function],
  "queryAllByAltText": [Function],
  "queryAllByDisplayValue": [Function],
  "queryAllByLabelText": [Function],
  "queryAllByPlaceholderText": [Function],
  "queryAllByRole": [Function],
  "queryAllByTestId": [Function],
  "queryAllByText": [Function],
  "queryAllByTitle": [Function],
  "queryByAltText": [Function],
  "queryByDisplayValue": [Function],
  "queryByLabelText": [Function],
  "queryByPlaceholderText": [Function],
  "queryByRole": [Function],
  "queryByTestId": [Function],
  "queryByText": [Function],
  "queryByTitle": [Function],
  "rerender": [Function],
  "unmount": [Function],
}
`;

exports[`<ProductGroup /> renders correctly with altViewExperimentEnabled and altViewFeatureEnabled are enabled 1`] = `
{
  "asFragment": [Function],
  "baseElement": .emotion-0 {
  box-sizing: border-box;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-flex-wrap: wrap;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
}

.emotion-1 {
  box-sizing: border-box;
  -webkit-flex-basis: 100%;
  -ms-flex-preferred-size: 100%;
  flex-basis: 100%;
  -webkit-box-flex: 0;
  -webkit-flex-grow: 0;
  -ms-flex-positive: 0;
  flex-grow: 0;
  margin: 0;
  max-width: 100%;
  padding: 0em;
}

.emotion-2 {
  overflow: hidden;
  margin: 0;
  margin-bottom: 2rem;
  padding: 0;
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 700;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 1.067rem;
  color: #666;
  line-height: normal;
  text-align: center;
}

.emotion-3 {
  text-transform: uppercase;
  letter-spacing: 0.15em;
  display: inline-block;
  position: relative;
  padding: 0 0.5rem;
}

.emotion-3::before,
.emotion-3::after {
  border-top: 2px solid #666;
  display: block;
  position: absolute;
  width: 100em;
  top: 50%;
  content: '';
}

.emotion-3::before {
  right: 100%;
}

.emotion-3::after {
  left: 100%;
}

.emotion-4 {
  --columnSize: 1;
  box-sizing: border-box;
  -webkit-flex-basis: 33.333333333333336%;
  -ms-flex-preferred-size: 33.333333333333336%;
  flex-basis: 33.333333333333336%;
  -webkit-box-flex: 0;
  -webkit-flex-grow: 0;
  -ms-flex-positive: 0;
  flex-grow: 0;
  margin: 0;
  max-width: 33.333333333333336%;
  padding: 0.5em;
}

.emotion-5 {
  min-height: 430px;
  position: relative;
}

.emotion-5 .product-card__image {
  width: 100%;
  display: block;
}

.emotion-6 {
  margin-bottom: 0.5rem;
}

.emotion-8 {
  width: 100%;
  object-fit: cover;
}

.emotion-9 {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  color: #666;
  line-height: 1.4;
  text-transform: none;
  font-weight: normal;
  font-size: 0.8rem;
}

.emotion-10 {
  text-transform: none;
  line-height: 1.4;
  color: #666;
  font-size: 0.8rem!important;
  letter-spacing: normal;
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-10 .product-price__markdown,
.emotion-10 .product-price--markdown {
  color: #666;
}

.emotion-10 .product-price__highlight,
.emotion-10 .product-price--highlight {
  text-transform: none;
  color: #D00000;
}

.emotion-10 .product-price__highlight priceHighlightFont,
.emotion-10 .product-price--highlight priceHighlightFont {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-10 .product-price__highlight--br,
.emotion-10 .product-price--highlight-br {
  text-transform: none;
  font-size: 0.8125rem;
}

.emotion-10 .product-price__markdown {
  text-transform: none;
  color: #666;
}

.emotion-10 .product-price__no-strike {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-10 .product-price__option {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 700;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-10 .product-price__option--br {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-10 .product-price__percentage-off {
  color: #D00000;
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-transform: none;
  padding-left: 5px;
}

.emotion-10 .product-price__percentage-off-no-padding {
  color: #D00000;
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-transform: none;
}

.emotion-10 .product-price__regular {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-10 .product-price__sale {
  color: #666;
}

.emotion-10 .product-price__strike {
  -webkit-text-decoration: line-through;
  text-decoration: line-through;
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-10 .product-price__same-line {
  padding-left: 5px;
}

.emotion-11 {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  color: #666;
  line-height: 1.4;
  text-transform: none;
  font-weight: bold;
  font-size: 0.8rem;
}

<body>
    <div>
      <div
        class="emotion-0"
        data-testid="grid-root"
        spacing="0.5"
      >
        <div
          class="emotion-1"
          data-testid="grid-root"
          spacing="0"
        >
          <div
            id="subcatHeader_1234"
          />
          <div
            class="emotion-2"
          >
            <h2
              class="emotion-3"
            >
              test group
            </h2>
          </div>
        </div>
        <div
          class="emotion-4"
          data-testid="grid-root"
          spacing="0.5"
        >
          <div
            class="product-card emotion-5"
          >
            <div
              class="product-card__image-wrapper emotion-6"
              id="product123"
            >
              <a
                aria-hidden="false"
                class="emotion-7"
                href="http://localhost/browse/product.do?pid=123&cid=1234&pcid=23456&grid=source_0_4_1"
                target="_self"
              >
                <img
                  alt="sherpa fleece hoodie"
                  class="product-card__image emotion-8"
                  src="http://www3.assets-gap.com/webcontent/0013/882/196/cn13882196.jpg"
                />
              </a>
            </div>
            <a
              href="http://localhost/browse/product.do?pid=123&cid=1234&pcid=23456&grid=source_0_4_1"
            >
              <div
                class="emotion-9"
              >
                Sherpa fleece hoodie
              </div>
            </a>
            <div>
              <div
                class="product-card-price"
              >
                <div
                  class="emotion-10"
                >
                  <div>
                    <div
                      class="product-price__markdown"
                    >
                      <span
                        aria-label="price.regular_price_aria_label"
                        class="product-price__no-strike"
                        role="text"
                      >
                        $69.95
                      </span>
                    </div>
                    <div
                      aria-label="price.current_price_range_aria_label"
                      class="product-price__highlight"
                      role="text"
                    >
                      <span
                        class="product-price__highlight"
                      >
                        $49.99
                         - 
                        $54.99
                      </span>
                    </div>
                  </div>
                </div>
              </div>
              <div
                class="product-card__marketing-flag emotion-11"
              >
                Final Sale
              </div>
            </div>
          </div>
        </div>
        <div
          class="emotion-4"
          data-testid="grid-root"
          spacing="0.5"
        >
          <div
            class="product-card emotion-5"
          >
            <div
              class="product-card__image-wrapper emotion-6"
              id="product123"
            >
              <a
                aria-hidden="false"
                class="emotion-7"
                href="http://localhost/browse/product.do?pid=123&cid=1234&pcid=23456&grid=source_1_4_1"
                target="_self"
              >
                <img
                  alt="sherpa fleece hoodie"
                  class="product-card__image"
                  src="http://www3.assets-gap.com/webcontent/0013/882/196/cn13882196.jpg"
                />
              </a>
            </div>
            <a
              href="http://localhost/browse/product.do?pid=123&cid=1234&pcid=23456&grid=source_1_4_1"
            >
              <div
                class="emotion-9"
              >
                Sherpa fleece hoodie
              </div>
            </a>
            <div>
              <div
                class="product-card-price"
              >
                <div
                  class="emotion-10"
                >
                  <div>
                    <div
                      class="product-price__markdown"
                    >
                      <span
                        aria-label="price.regular_price_aria_label"
                        class="product-price__no-strike"
                        role="text"
                      >
                        $69.95
                      </span>
                    </div>
                    <div
                      aria-label="price.current_price_range_aria_label"
                      class="product-price__highlight"
                      role="text"
                    >
                      <span
                        class="product-price__highlight"
                      >
                        $49.99
                         - 
                        $54.99
                      </span>
                    </div>
                  </div>
                </div>
              </div>
              <div
                class="product-card__marketing-flag emotion-11"
              >
                Final Sale
              </div>
            </div>
          </div>
        </div>
        <div
          class="emotion-4"
          data-testid="grid-root"
          spacing="0.5"
        >
          <div
            class="product-card emotion-5"
          >
            <div
              class="product-card__image-wrapper emotion-6"
              id="product123"
            >
              <a
                aria-hidden="false"
                class="emotion-7"
                href="http://localhost/browse/product.do?pid=123&cid=1234&pcid=23456&grid=source_2_4_1"
                target="_self"
              >
                <img
                  alt="sherpa fleece hoodie"
                  class="product-card__image"
                  src="http://www3.assets-gap.com/webcontent/0013/882/196/cn13882196.jpg"
                />
              </a>
            </div>
            <a
              href="http://localhost/browse/product.do?pid=123&cid=1234&pcid=23456&grid=source_2_4_1"
            >
              <div
                class="emotion-9"
              >
                Sherpa fleece hoodie
              </div>
            </a>
            <div>
              <div
                class="product-card-price"
              >
                <div
                  class="emotion-10"
                >
                  <div>
                    <div
                      class="product-price__markdown"
                    >
                      <span
                        aria-label="price.regular_price_aria_label"
                        class="product-price__no-strike"
                        role="text"
                      >
                        $69.95
                      </span>
                    </div>
                    <div
                      aria-label="price.current_price_range_aria_label"
                      class="product-price__highlight"
                      role="text"
                    >
                      <span
                        class="product-price__highlight"
                      >
                        $49.99
                         - 
                        $54.99
                      </span>
                    </div>
                  </div>
                </div>
              </div>
              <div
                class="product-card__marketing-flag emotion-11"
              >
                Final Sale
              </div>
            </div>
          </div>
        </div>
        <div
          class="emotion-4"
          data-testid="grid-root"
          spacing="0.5"
        >
          <div
            class="product-card emotion-5"
          >
            <div
              class="product-card__image-wrapper emotion-6"
              id="product124"
            >
              <a
                aria-hidden="false"
                class="emotion-7"
                href="http://localhost/browse/product.do?pid=124&cid=1234&pcid=23456&grid=source_3_4_1"
                target="_self"
              >
                <img
                  alt="Indigo zip hoodie"
                  class="product-card__image"
                  src="http://www.gap.com/webcontent/0013/707/658/cn13707658.jpg"
                />
              </a>
            </div>
            <a
              href="http://localhost/browse/product.do?pid=124&cid=1234&pcid=23456&grid=source_3_4_1"
            >
              <div
                class="emotion-9"
              >
                Indigo zip hoodie
              </div>
            </a>
            <div>
              <div
                class="product-card-price"
              >
                <div
                  class="emotion-10"
                >
                  <div>
                    <div
                      class="product-price__markdown"
                    >
                      <span
                        aria-label="price.regular_price_aria_label"
                        class="product-price__no-strike"
                        role="text"
                      >
                        $69.95
                      </span>
                    </div>
                    <div
                      aria-label="price.current_price_range_aria_label"
                      class="product-price__highlight"
                      role="text"
                    >
                      <span
                        class="product-price__highlight"
                      >
                        $49.99
                         - 
                        $54.99
                      </span>
                    </div>
                  </div>
                </div>
              </div>
              <div
                class="product-card__marketing-flag emotion-11"
              >
                Final Sale
              </div>
            </div>
          </div>
        </div>
        <div
          class="emotion-1"
          data-testid="grid-root"
          spacing="0"
        >
          <div
            id="subcatHeader_1234"
          />
          <div />
        </div>
      </div>
    </div>
  </body>,
  "container": .emotion-0 {
  box-sizing: border-box;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-flex-wrap: wrap;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
}

.emotion-1 {
  box-sizing: border-box;
  -webkit-flex-basis: 100%;
  -ms-flex-preferred-size: 100%;
  flex-basis: 100%;
  -webkit-box-flex: 0;
  -webkit-flex-grow: 0;
  -ms-flex-positive: 0;
  flex-grow: 0;
  margin: 0;
  max-width: 100%;
  padding: 0em;
}

.emotion-2 {
  overflow: hidden;
  margin: 0;
  margin-bottom: 2rem;
  padding: 0;
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 700;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 1.067rem;
  color: #666;
  line-height: normal;
  text-align: center;
}

.emotion-3 {
  text-transform: uppercase;
  letter-spacing: 0.15em;
  display: inline-block;
  position: relative;
  padding: 0 0.5rem;
}

.emotion-3::before,
.emotion-3::after {
  border-top: 2px solid #666;
  display: block;
  position: absolute;
  width: 100em;
  top: 50%;
  content: '';
}

.emotion-3::before {
  right: 100%;
}

.emotion-3::after {
  left: 100%;
}

.emotion-4 {
  --columnSize: 1;
  box-sizing: border-box;
  -webkit-flex-basis: 33.333333333333336%;
  -ms-flex-preferred-size: 33.333333333333336%;
  flex-basis: 33.333333333333336%;
  -webkit-box-flex: 0;
  -webkit-flex-grow: 0;
  -ms-flex-positive: 0;
  flex-grow: 0;
  margin: 0;
  max-width: 33.333333333333336%;
  padding: 0.5em;
}

.emotion-5 {
  min-height: 430px;
  position: relative;
}

.emotion-5 .product-card__image {
  width: 100%;
  display: block;
}

.emotion-6 {
  margin-bottom: 0.5rem;
}

.emotion-8 {
  width: 100%;
  object-fit: cover;
}

.emotion-9 {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  color: #666;
  line-height: 1.4;
  text-transform: none;
  font-weight: normal;
  font-size: 0.8rem;
}

.emotion-10 {
  text-transform: none;
  line-height: 1.4;
  color: #666;
  font-size: 0.8rem!important;
  letter-spacing: normal;
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-10 .product-price__markdown,
.emotion-10 .product-price--markdown {
  color: #666;
}

.emotion-10 .product-price__highlight,
.emotion-10 .product-price--highlight {
  text-transform: none;
  color: #D00000;
}

.emotion-10 .product-price__highlight priceHighlightFont,
.emotion-10 .product-price--highlight priceHighlightFont {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-10 .product-price__highlight--br,
.emotion-10 .product-price--highlight-br {
  text-transform: none;
  font-size: 0.8125rem;
}

.emotion-10 .product-price__markdown {
  text-transform: none;
  color: #666;
}

.emotion-10 .product-price__no-strike {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-10 .product-price__option {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 700;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-10 .product-price__option--br {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-10 .product-price__percentage-off {
  color: #D00000;
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-transform: none;
  padding-left: 5px;
}

.emotion-10 .product-price__percentage-off-no-padding {
  color: #D00000;
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-transform: none;
}

.emotion-10 .product-price__regular {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-10 .product-price__sale {
  color: #666;
}

.emotion-10 .product-price__strike {
  -webkit-text-decoration: line-through;
  text-decoration: line-through;
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-10 .product-price__same-line {
  padding-left: 5px;
}

.emotion-11 {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  color: #666;
  line-height: 1.4;
  text-transform: none;
  font-weight: bold;
  font-size: 0.8rem;
}

<div>
    <div
      class="emotion-0"
      data-testid="grid-root"
      spacing="0.5"
    >
      <div
        class="emotion-1"
        data-testid="grid-root"
        spacing="0"
      >
        <div
          id="subcatHeader_1234"
        />
        <div
          class="emotion-2"
        >
          <h2
            class="emotion-3"
          >
            test group
          </h2>
        </div>
      </div>
      <div
        class="emotion-4"
        data-testid="grid-root"
        spacing="0.5"
      >
        <div
          class="product-card emotion-5"
        >
          <div
            class="product-card__image-wrapper emotion-6"
            id="product123"
          >
            <a
              aria-hidden="false"
              class="emotion-7"
              href="http://localhost/browse/product.do?pid=123&cid=1234&pcid=23456&grid=source_0_4_1"
              target="_self"
            >
              <img
                alt="sherpa fleece hoodie"
                class="product-card__image emotion-8"
                src="http://www3.assets-gap.com/webcontent/0013/882/196/cn13882196.jpg"
              />
            </a>
          </div>
          <a
            href="http://localhost/browse/product.do?pid=123&cid=1234&pcid=23456&grid=source_0_4_1"
          >
            <div
              class="emotion-9"
            >
              Sherpa fleece hoodie
            </div>
          </a>
          <div>
            <div
              class="product-card-price"
            >
              <div
                class="emotion-10"
              >
                <div>
                  <div
                    class="product-price__markdown"
                  >
                    <span
                      aria-label="price.regular_price_aria_label"
                      class="product-price__no-strike"
                      role="text"
                    >
                      $69.95
                    </span>
                  </div>
                  <div
                    aria-label="price.current_price_range_aria_label"
                    class="product-price__highlight"
                    role="text"
                  >
                    <span
                      class="product-price__highlight"
                    >
                      $49.99
                       - 
                      $54.99
                    </span>
                  </div>
                </div>
              </div>
            </div>
            <div
              class="product-card__marketing-flag emotion-11"
            >
              Final Sale
            </div>
          </div>
        </div>
      </div>
      <div
        class="emotion-4"
        data-testid="grid-root"
        spacing="0.5"
      >
        <div
          class="product-card emotion-5"
        >
          <div
            class="product-card__image-wrapper emotion-6"
            id="product123"
          >
            <a
              aria-hidden="false"
              class="emotion-7"
              href="http://localhost/browse/product.do?pid=123&cid=1234&pcid=23456&grid=source_1_4_1"
              target="_self"
            >
              <img
                alt="sherpa fleece hoodie"
                class="product-card__image"
                src="http://www3.assets-gap.com/webcontent/0013/882/196/cn13882196.jpg"
              />
            </a>
          </div>
          <a
            href="http://localhost/browse/product.do?pid=123&cid=1234&pcid=23456&grid=source_1_4_1"
          >
            <div
              class="emotion-9"
            >
              Sherpa fleece hoodie
            </div>
          </a>
          <div>
            <div
              class="product-card-price"
            >
              <div
                class="emotion-10"
              >
                <div>
                  <div
                    class="product-price__markdown"
                  >
                    <span
                      aria-label="price.regular_price_aria_label"
                      class="product-price__no-strike"
                      role="text"
                    >
                      $69.95
                    </span>
                  </div>
                  <div
                    aria-label="price.current_price_range_aria_label"
                    class="product-price__highlight"
                    role="text"
                  >
                    <span
                      class="product-price__highlight"
                    >
                      $49.99
                       - 
                      $54.99
                    </span>
                  </div>
                </div>
              </div>
            </div>
            <div
              class="product-card__marketing-flag emotion-11"
            >
              Final Sale
            </div>
          </div>
        </div>
      </div>
      <div
        class="emotion-4"
        data-testid="grid-root"
        spacing="0.5"
      >
        <div
          class="product-card emotion-5"
        >
          <div
            class="product-card__image-wrapper emotion-6"
            id="product123"
          >
            <a
              aria-hidden="false"
              class="emotion-7"
              href="http://localhost/browse/product.do?pid=123&cid=1234&pcid=23456&grid=source_2_4_1"
              target="_self"
            >
              <img
                alt="sherpa fleece hoodie"
                class="product-card__image"
                src="http://www3.assets-gap.com/webcontent/0013/882/196/cn13882196.jpg"
              />
            </a>
          </div>
          <a
            href="http://localhost/browse/product.do?pid=123&cid=1234&pcid=23456&grid=source_2_4_1"
          >
            <div
              class="emotion-9"
            >
              Sherpa fleece hoodie
            </div>
          </a>
          <div>
            <div
              class="product-card-price"
            >
              <div
                class="emotion-10"
              >
                <div>
                  <div
                    class="product-price__markdown"
                  >
                    <span
                      aria-label="price.regular_price_aria_label"
                      class="product-price__no-strike"
                      role="text"
                    >
                      $69.95
                    </span>
                  </div>
                  <div
                    aria-label="price.current_price_range_aria_label"
                    class="product-price__highlight"
                    role="text"
                  >
                    <span
                      class="product-price__highlight"
                    >
                      $49.99
                       - 
                      $54.99
                    </span>
                  </div>
                </div>
              </div>
            </div>
            <div
              class="product-card__marketing-flag emotion-11"
            >
              Final Sale
            </div>
          </div>
        </div>
      </div>
      <div
        class="emotion-4"
        data-testid="grid-root"
        spacing="0.5"
      >
        <div
          class="product-card emotion-5"
        >
          <div
            class="product-card__image-wrapper emotion-6"
            id="product124"
          >
            <a
              aria-hidden="false"
              class="emotion-7"
              href="http://localhost/browse/product.do?pid=124&cid=1234&pcid=23456&grid=source_3_4_1"
              target="_self"
            >
              <img
                alt="Indigo zip hoodie"
                class="product-card__image"
                src="http://www.gap.com/webcontent/0013/707/658/cn13707658.jpg"
              />
            </a>
          </div>
          <a
            href="http://localhost/browse/product.do?pid=124&cid=1234&pcid=23456&grid=source_3_4_1"
          >
            <div
              class="emotion-9"
            >
              Indigo zip hoodie
            </div>
          </a>
          <div>
            <div
              class="product-card-price"
            >
              <div
                class="emotion-10"
              >
                <div>
                  <div
                    class="product-price__markdown"
                  >
                    <span
                      aria-label="price.regular_price_aria_label"
                      class="product-price__no-strike"
                      role="text"
                    >
                      $69.95
                    </span>
                  </div>
                  <div
                    aria-label="price.current_price_range_aria_label"
                    class="product-price__highlight"
                    role="text"
                  >
                    <span
                      class="product-price__highlight"
                    >
                      $49.99
                       - 
                      $54.99
                    </span>
                  </div>
                </div>
              </div>
            </div>
            <div
              class="product-card__marketing-flag emotion-11"
            >
              Final Sale
            </div>
          </div>
        </div>
      </div>
      <div
        class="emotion-1"
        data-testid="grid-root"
        spacing="0"
      >
        <div
          id="subcatHeader_1234"
        />
        <div />
      </div>
    </div>
  </div>,
  "debug": [Function],
  "findAllByAltText": [Function],
  "findAllByDisplayValue": [Function],
  "findAllByLabelText": [Function],
  "findAllByPlaceholderText": [Function],
  "findAllByRole": [Function],
  "findAllByTestId": [Function],
  "findAllByText": [Function],
  "findAllByTitle": [Function],
  "findByAltText": [Function],
  "findByDisplayValue": [Function],
  "findByLabelText": [Function],
  "findByPlaceholderText": [Function],
  "findByRole": [Function],
  "findByTestId": [Function],
  "findByText": [Function],
  "findByTitle": [Function],
  "getAllByAltText": [Function],
  "getAllByDisplayValue": [Function],
  "getAllByLabelText": [Function],
  "getAllByPlaceholderText": [Function],
  "getAllByRole": [Function],
  "getAllByTestId": [Function],
  "getAllByText": [Function],
  "getAllByTitle": [Function],
  "getByAltText": [Function],
  "getByDisplayValue": [Function],
  "getByLabelText": [Function],
  "getByPlaceholderText": [Function],
  "getByRole": [Function],
  "getByTestId": [Function],
  "getByText": [Function],
  "getByTitle": [Function],
  "queryAllByAltText": [Function],
  "queryAllByDisplayValue": [Function],
  "queryAllByLabelText": [Function],
  "queryAllByPlaceholderText": [Function],
  "queryAllByRole": [Function],
  "queryAllByTestId": [Function],
  "queryAllByText": [Function],
  "queryAllByTitle": [Function],
  "queryByAltText": [Function],
  "queryByDisplayValue": [Function],
  "queryByLabelText": [Function],
  "queryByPlaceholderText": [Function],
  "queryByRole": [Function],
  "queryByTestId": [Function],
  "queryByText": [Function],
  "queryByTitle": [Function],
  "rerender": [Function],
  "unmount": [Function],
}
`;

exports[`<ProductGroup /> renders correctly with only altViewExperimentEnabled is enabled 1`] = `
{
  "asFragment": [Function],
  "baseElement": .emotion-0 {
  box-sizing: border-box;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-flex-wrap: wrap;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
}

.emotion-1 {
  box-sizing: border-box;
  -webkit-flex-basis: 100%;
  -ms-flex-preferred-size: 100%;
  flex-basis: 100%;
  -webkit-box-flex: 0;
  -webkit-flex-grow: 0;
  -ms-flex-positive: 0;
  flex-grow: 0;
  margin: 0;
  max-width: 100%;
  padding: 0em;
}

.emotion-2 {
  overflow: hidden;
  margin: 0;
  margin-bottom: 2rem;
  padding: 0;
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 700;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 1.067rem;
  color: #666;
  line-height: normal;
  text-align: center;
}

.emotion-3 {
  text-transform: uppercase;
  letter-spacing: 0.15em;
  display: inline-block;
  position: relative;
  padding: 0 0.5rem;
}

.emotion-3::before,
.emotion-3::after {
  border-top: 2px solid #666;
  display: block;
  position: absolute;
  width: 100em;
  top: 50%;
  content: '';
}

.emotion-3::before {
  right: 100%;
}

.emotion-3::after {
  left: 100%;
}

.emotion-4 {
  --columnSize: 1;
  box-sizing: border-box;
  -webkit-flex-basis: 33.333333333333336%;
  -ms-flex-preferred-size: 33.333333333333336%;
  flex-basis: 33.333333333333336%;
  -webkit-box-flex: 0;
  -webkit-flex-grow: 0;
  -ms-flex-positive: 0;
  flex-grow: 0;
  margin: 0;
  max-width: 33.333333333333336%;
  padding: 0.5em;
}

.emotion-5 {
  min-height: 430px;
  position: relative;
}

.emotion-5 .product-card__image {
  width: 100%;
  display: block;
}

.emotion-6 {
  margin-bottom: 0.5rem;
}

.emotion-8 {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  color: #666;
  line-height: 1.4;
  text-transform: none;
  font-weight: normal;
  font-size: 0.8rem;
}

.emotion-9 {
  text-transform: none;
  line-height: 1.4;
  color: #666;
  font-size: 0.8rem!important;
  letter-spacing: normal;
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-9 .product-price__markdown,
.emotion-9 .product-price--markdown {
  color: #666;
}

.emotion-9 .product-price__highlight,
.emotion-9 .product-price--highlight {
  text-transform: none;
  color: #D00000;
}

.emotion-9 .product-price__highlight priceHighlightFont,
.emotion-9 .product-price--highlight priceHighlightFont {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-9 .product-price__highlight--br,
.emotion-9 .product-price--highlight-br {
  text-transform: none;
  font-size: 0.8125rem;
}

.emotion-9 .product-price__markdown {
  text-transform: none;
  color: #666;
}

.emotion-9 .product-price__no-strike {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-9 .product-price__option {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 700;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-9 .product-price__option--br {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-9 .product-price__percentage-off {
  color: #D00000;
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-transform: none;
  padding-left: 5px;
}

.emotion-9 .product-price__percentage-off-no-padding {
  color: #D00000;
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-transform: none;
}

.emotion-9 .product-price__regular {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-9 .product-price__sale {
  color: #666;
}

.emotion-9 .product-price__strike {
  -webkit-text-decoration: line-through;
  text-decoration: line-through;
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-9 .product-price__same-line {
  padding-left: 5px;
}

.emotion-10 {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  color: #666;
  line-height: 1.4;
  text-transform: none;
  font-weight: bold;
  font-size: 0.8rem;
}

<body>
    <div>
      <div
        class="emotion-0"
        data-testid="grid-root"
        spacing="0.5"
      >
        <div
          class="emotion-1"
          data-testid="grid-root"
          spacing="0"
        >
          <div
            id="subcatHeader_1234"
          />
          <div
            class="emotion-2"
          >
            <h2
              class="emotion-3"
            >
              test group
            </h2>
          </div>
        </div>
        <div
          class="emotion-4"
          data-testid="grid-root"
          spacing="0.5"
        >
          <div
            class="product-card emotion-5"
          >
            <div
              class="product-card__image-wrapper emotion-6"
              id="product123"
            >
              <a
                aria-hidden="false"
                class="emotion-7"
                href="http://localhost/browse/product.do?pid=123&cid=1234&pcid=23456&grid=source_0_4_1"
                target="_self"
              >
                <img
                  alt="sherpa fleece hoodie"
                  class="product-card__image"
                  src="http://www3.assets-gap.com/webcontent/0013/882/196/cn13882196.jpg"
                />
              </a>
            </div>
            <a
              href="http://localhost/browse/product.do?pid=123&cid=1234&pcid=23456&grid=source_0_4_1"
            >
              <div
                class="emotion-8"
              >
                Sherpa fleece hoodie
              </div>
            </a>
            <div>
              <div
                class="product-card-price"
              >
                <div
                  class="emotion-9"
                >
                  <div>
                    <div
                      class="product-price__markdown"
                    >
                      <span
                        aria-label="price.regular_price_aria_label"
                        class="product-price__no-strike"
                        role="text"
                      >
                        $69.95
                      </span>
                    </div>
                    <div
                      aria-label="price.current_price_range_aria_label"
                      class="product-price__highlight"
                      role="text"
                    >
                      <span
                        class="product-price__highlight"
                      >
                        $49.99
                         - 
                        $54.99
                      </span>
                    </div>
                  </div>
                </div>
              </div>
              <div
                class="product-card__marketing-flag emotion-10"
              >
                Final Sale
              </div>
            </div>
          </div>
        </div>
        <div
          class="emotion-4"
          data-testid="grid-root"
          spacing="0.5"
        >
          <div
            class="product-card emotion-5"
          >
            <div
              class="product-card__image-wrapper emotion-6"
              id="product123"
            >
              <a
                aria-hidden="false"
                class="emotion-7"
                href="http://localhost/browse/product.do?pid=123&cid=1234&pcid=23456&grid=source_1_4_1"
                target="_self"
              >
                <img
                  alt="sherpa fleece hoodie"
                  class="product-card__image"
                  src="http://www3.assets-gap.com/webcontent/0013/882/196/cn13882196.jpg"
                />
              </a>
            </div>
            <a
              href="http://localhost/browse/product.do?pid=123&cid=1234&pcid=23456&grid=source_1_4_1"
            >
              <div
                class="emotion-8"
              >
                Sherpa fleece hoodie
              </div>
            </a>
            <div>
              <div
                class="product-card-price"
              >
                <div
                  class="emotion-9"
                >
                  <div>
                    <div
                      class="product-price__markdown"
                    >
                      <span
                        aria-label="price.regular_price_aria_label"
                        class="product-price__no-strike"
                        role="text"
                      >
                        $69.95
                      </span>
                    </div>
                    <div
                      aria-label="price.current_price_range_aria_label"
                      class="product-price__highlight"
                      role="text"
                    >
                      <span
                        class="product-price__highlight"
                      >
                        $49.99
                         - 
                        $54.99
                      </span>
                    </div>
                  </div>
                </div>
              </div>
              <div
                class="product-card__marketing-flag emotion-10"
              >
                Final Sale
              </div>
            </div>
          </div>
        </div>
        <div
          class="emotion-4"
          data-testid="grid-root"
          spacing="0.5"
        >
          <div
            class="product-card emotion-5"
          >
            <div
              class="product-card__image-wrapper emotion-6"
              id="product123"
            >
              <a
                aria-hidden="false"
                class="emotion-7"
                href="http://localhost/browse/product.do?pid=123&cid=1234&pcid=23456&grid=source_2_4_1"
                target="_self"
              >
                <img
                  alt="sherpa fleece hoodie"
                  class="product-card__image"
                  src="http://www3.assets-gap.com/webcontent/0013/882/196/cn13882196.jpg"
                />
              </a>
            </div>
            <a
              href="http://localhost/browse/product.do?pid=123&cid=1234&pcid=23456&grid=source_2_4_1"
            >
              <div
                class="emotion-8"
              >
                Sherpa fleece hoodie
              </div>
            </a>
            <div>
              <div
                class="product-card-price"
              >
                <div
                  class="emotion-9"
                >
                  <div>
                    <div
                      class="product-price__markdown"
                    >
                      <span
                        aria-label="price.regular_price_aria_label"
                        class="product-price__no-strike"
                        role="text"
                      >
                        $69.95
                      </span>
                    </div>
                    <div
                      aria-label="price.current_price_range_aria_label"
                      class="product-price__highlight"
                      role="text"
                    >
                      <span
                        class="product-price__highlight"
                      >
                        $49.99
                         - 
                        $54.99
                      </span>
                    </div>
                  </div>
                </div>
              </div>
              <div
                class="product-card__marketing-flag emotion-10"
              >
                Final Sale
              </div>
            </div>
          </div>
        </div>
        <div
          class="emotion-4"
          data-testid="grid-root"
          spacing="0.5"
        >
          <div
            class="product-card emotion-5"
          >
            <div
              class="product-card__image-wrapper emotion-6"
              id="product124"
            >
              <a
                aria-hidden="false"
                class="emotion-7"
                href="http://localhost/browse/product.do?pid=124&cid=1234&pcid=23456&grid=source_3_4_1"
                target="_self"
              >
                <img
                  alt="Indigo zip hoodie"
                  class="product-card__image"
                  src="http://www.gap.com/webcontent/0013/707/658/cn13707658.jpg"
                />
              </a>
            </div>
            <a
              href="http://localhost/browse/product.do?pid=124&cid=1234&pcid=23456&grid=source_3_4_1"
            >
              <div
                class="emotion-8"
              >
                Indigo zip hoodie
              </div>
            </a>
            <div>
              <div
                class="product-card-price"
              >
                <div
                  class="emotion-9"
                >
                  <div>
                    <div
                      class="product-price__markdown"
                    >
                      <span
                        aria-label="price.regular_price_aria_label"
                        class="product-price__no-strike"
                        role="text"
                      >
                        $69.95
                      </span>
                    </div>
                    <div
                      aria-label="price.current_price_range_aria_label"
                      class="product-price__highlight"
                      role="text"
                    >
                      <span
                        class="product-price__highlight"
                      >
                        $49.99
                         - 
                        $54.99
                      </span>
                    </div>
                  </div>
                </div>
              </div>
              <div
                class="product-card__marketing-flag emotion-10"
              >
                Final Sale
              </div>
            </div>
          </div>
        </div>
        <div
          class="emotion-1"
          data-testid="grid-root"
          spacing="0"
        >
          <div
            id="subcatHeader_1234"
          />
          <div />
        </div>
      </div>
    </div>
  </body>,
  "container": .emotion-0 {
  box-sizing: border-box;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-flex-wrap: wrap;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
}

.emotion-1 {
  box-sizing: border-box;
  -webkit-flex-basis: 100%;
  -ms-flex-preferred-size: 100%;
  flex-basis: 100%;
  -webkit-box-flex: 0;
  -webkit-flex-grow: 0;
  -ms-flex-positive: 0;
  flex-grow: 0;
  margin: 0;
  max-width: 100%;
  padding: 0em;
}

.emotion-2 {
  overflow: hidden;
  margin: 0;
  margin-bottom: 2rem;
  padding: 0;
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 700;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 1.067rem;
  color: #666;
  line-height: normal;
  text-align: center;
}

.emotion-3 {
  text-transform: uppercase;
  letter-spacing: 0.15em;
  display: inline-block;
  position: relative;
  padding: 0 0.5rem;
}

.emotion-3::before,
.emotion-3::after {
  border-top: 2px solid #666;
  display: block;
  position: absolute;
  width: 100em;
  top: 50%;
  content: '';
}

.emotion-3::before {
  right: 100%;
}

.emotion-3::after {
  left: 100%;
}

.emotion-4 {
  --columnSize: 1;
  box-sizing: border-box;
  -webkit-flex-basis: 33.333333333333336%;
  -ms-flex-preferred-size: 33.333333333333336%;
  flex-basis: 33.333333333333336%;
  -webkit-box-flex: 0;
  -webkit-flex-grow: 0;
  -ms-flex-positive: 0;
  flex-grow: 0;
  margin: 0;
  max-width: 33.333333333333336%;
  padding: 0.5em;
}

.emotion-5 {
  min-height: 430px;
  position: relative;
}

.emotion-5 .product-card__image {
  width: 100%;
  display: block;
}

.emotion-6 {
  margin-bottom: 0.5rem;
}

.emotion-8 {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  color: #666;
  line-height: 1.4;
  text-transform: none;
  font-weight: normal;
  font-size: 0.8rem;
}

.emotion-9 {
  text-transform: none;
  line-height: 1.4;
  color: #666;
  font-size: 0.8rem!important;
  letter-spacing: normal;
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-9 .product-price__markdown,
.emotion-9 .product-price--markdown {
  color: #666;
}

.emotion-9 .product-price__highlight,
.emotion-9 .product-price--highlight {
  text-transform: none;
  color: #D00000;
}

.emotion-9 .product-price__highlight priceHighlightFont,
.emotion-9 .product-price--highlight priceHighlightFont {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-9 .product-price__highlight--br,
.emotion-9 .product-price--highlight-br {
  text-transform: none;
  font-size: 0.8125rem;
}

.emotion-9 .product-price__markdown {
  text-transform: none;
  color: #666;
}

.emotion-9 .product-price__no-strike {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-9 .product-price__option {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 700;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-9 .product-price__option--br {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-9 .product-price__percentage-off {
  color: #D00000;
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-transform: none;
  padding-left: 5px;
}

.emotion-9 .product-price__percentage-off-no-padding {
  color: #D00000;
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-transform: none;
}

.emotion-9 .product-price__regular {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-9 .product-price__sale {
  color: #666;
}

.emotion-9 .product-price__strike {
  -webkit-text-decoration: line-through;
  text-decoration: line-through;
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-9 .product-price__same-line {
  padding-left: 5px;
}

.emotion-10 {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  color: #666;
  line-height: 1.4;
  text-transform: none;
  font-weight: bold;
  font-size: 0.8rem;
}

<div>
    <div
      class="emotion-0"
      data-testid="grid-root"
      spacing="0.5"
    >
      <div
        class="emotion-1"
        data-testid="grid-root"
        spacing="0"
      >
        <div
          id="subcatHeader_1234"
        />
        <div
          class="emotion-2"
        >
          <h2
            class="emotion-3"
          >
            test group
          </h2>
        </div>
      </div>
      <div
        class="emotion-4"
        data-testid="grid-root"
        spacing="0.5"
      >
        <div
          class="product-card emotion-5"
        >
          <div
            class="product-card__image-wrapper emotion-6"
            id="product123"
          >
            <a
              aria-hidden="false"
              class="emotion-7"
              href="http://localhost/browse/product.do?pid=123&cid=1234&pcid=23456&grid=source_0_4_1"
              target="_self"
            >
              <img
                alt="sherpa fleece hoodie"
                class="product-card__image"
                src="http://www3.assets-gap.com/webcontent/0013/882/196/cn13882196.jpg"
              />
            </a>
          </div>
          <a
            href="http://localhost/browse/product.do?pid=123&cid=1234&pcid=23456&grid=source_0_4_1"
          >
            <div
              class="emotion-8"
            >
              Sherpa fleece hoodie
            </div>
          </a>
          <div>
            <div
              class="product-card-price"
            >
              <div
                class="emotion-9"
              >
                <div>
                  <div
                    class="product-price__markdown"
                  >
                    <span
                      aria-label="price.regular_price_aria_label"
                      class="product-price__no-strike"
                      role="text"
                    >
                      $69.95
                    </span>
                  </div>
                  <div
                    aria-label="price.current_price_range_aria_label"
                    class="product-price__highlight"
                    role="text"
                  >
                    <span
                      class="product-price__highlight"
                    >
                      $49.99
                       - 
                      $54.99
                    </span>
                  </div>
                </div>
              </div>
            </div>
            <div
              class="product-card__marketing-flag emotion-10"
            >
              Final Sale
            </div>
          </div>
        </div>
      </div>
      <div
        class="emotion-4"
        data-testid="grid-root"
        spacing="0.5"
      >
        <div
          class="product-card emotion-5"
        >
          <div
            class="product-card__image-wrapper emotion-6"
            id="product123"
          >
            <a
              aria-hidden="false"
              class="emotion-7"
              href="http://localhost/browse/product.do?pid=123&cid=1234&pcid=23456&grid=source_1_4_1"
              target="_self"
            >
              <img
                alt="sherpa fleece hoodie"
                class="product-card__image"
                src="http://www3.assets-gap.com/webcontent/0013/882/196/cn13882196.jpg"
              />
            </a>
          </div>
          <a
            href="http://localhost/browse/product.do?pid=123&cid=1234&pcid=23456&grid=source_1_4_1"
          >
            <div
              class="emotion-8"
            >
              Sherpa fleece hoodie
            </div>
          </a>
          <div>
            <div
              class="product-card-price"
            >
              <div
                class="emotion-9"
              >
                <div>
                  <div
                    class="product-price__markdown"
                  >
                    <span
                      aria-label="price.regular_price_aria_label"
                      class="product-price__no-strike"
                      role="text"
                    >
                      $69.95
                    </span>
                  </div>
                  <div
                    aria-label="price.current_price_range_aria_label"
                    class="product-price__highlight"
                    role="text"
                  >
                    <span
                      class="product-price__highlight"
                    >
                      $49.99
                       - 
                      $54.99
                    </span>
                  </div>
                </div>
              </div>
            </div>
            <div
              class="product-card__marketing-flag emotion-10"
            >
              Final Sale
            </div>
          </div>
        </div>
      </div>
      <div
        class="emotion-4"
        data-testid="grid-root"
        spacing="0.5"
      >
        <div
          class="product-card emotion-5"
        >
          <div
            class="product-card__image-wrapper emotion-6"
            id="product123"
          >
            <a
              aria-hidden="false"
              class="emotion-7"
              href="http://localhost/browse/product.do?pid=123&cid=1234&pcid=23456&grid=source_2_4_1"
              target="_self"
            >
              <img
                alt="sherpa fleece hoodie"
                class="product-card__image"
                src="http://www3.assets-gap.com/webcontent/0013/882/196/cn13882196.jpg"
              />
            </a>
          </div>
          <a
            href="http://localhost/browse/product.do?pid=123&cid=1234&pcid=23456&grid=source_2_4_1"
          >
            <div
              class="emotion-8"
            >
              Sherpa fleece hoodie
            </div>
          </a>
          <div>
            <div
              class="product-card-price"
            >
              <div
                class="emotion-9"
              >
                <div>
                  <div
                    class="product-price__markdown"
                  >
                    <span
                      aria-label="price.regular_price_aria_label"
                      class="product-price__no-strike"
                      role="text"
                    >
                      $69.95
                    </span>
                  </div>
                  <div
                    aria-label="price.current_price_range_aria_label"
                    class="product-price__highlight"
                    role="text"
                  >
                    <span
                      class="product-price__highlight"
                    >
                      $49.99
                       - 
                      $54.99
                    </span>
                  </div>
                </div>
              </div>
            </div>
            <div
              class="product-card__marketing-flag emotion-10"
            >
              Final Sale
            </div>
          </div>
        </div>
      </div>
      <div
        class="emotion-4"
        data-testid="grid-root"
        spacing="0.5"
      >
        <div
          class="product-card emotion-5"
        >
          <div
            class="product-card__image-wrapper emotion-6"
            id="product124"
          >
            <a
              aria-hidden="false"
              class="emotion-7"
              href="http://localhost/browse/product.do?pid=124&cid=1234&pcid=23456&grid=source_3_4_1"
              target="_self"
            >
              <img
                alt="Indigo zip hoodie"
                class="product-card__image"
                src="http://www.gap.com/webcontent/0013/707/658/cn13707658.jpg"
              />
            </a>
          </div>
          <a
            href="http://localhost/browse/product.do?pid=124&cid=1234&pcid=23456&grid=source_3_4_1"
          >
            <div
              class="emotion-8"
            >
              Indigo zip hoodie
            </div>
          </a>
          <div>
            <div
              class="product-card-price"
            >
              <div
                class="emotion-9"
              >
                <div>
                  <div
                    class="product-price__markdown"
                  >
                    <span
                      aria-label="price.regular_price_aria_label"
                      class="product-price__no-strike"
                      role="text"
                    >
                      $69.95
                    </span>
                  </div>
                  <div
                    aria-label="price.current_price_range_aria_label"
                    class="product-price__highlight"
                    role="text"
                  >
                    <span
                      class="product-price__highlight"
                    >
                      $49.99
                       - 
                      $54.99
                    </span>
                  </div>
                </div>
              </div>
            </div>
            <div
              class="product-card__marketing-flag emotion-10"
            >
              Final Sale
            </div>
          </div>
        </div>
      </div>
      <div
        class="emotion-1"
        data-testid="grid-root"
        spacing="0"
      >
        <div
          id="subcatHeader_1234"
        />
        <div />
      </div>
    </div>
  </div>,
  "debug": [Function],
  "findAllByAltText": [Function],
  "findAllByDisplayValue": [Function],
  "findAllByLabelText": [Function],
  "findAllByPlaceholderText": [Function],
  "findAllByRole": [Function],
  "findAllByTestId": [Function],
  "findAllByText": [Function],
  "findAllByTitle": [Function],
  "findByAltText": [Function],
  "findByDisplayValue": [Function],
  "findByLabelText": [Function],
  "findByPlaceholderText": [Function],
  "findByRole": [Function],
  "findByTestId": [Function],
  "findByText": [Function],
  "findByTitle": [Function],
  "getAllByAltText": [Function],
  "getAllByDisplayValue": [Function],
  "getAllByLabelText": [Function],
  "getAllByPlaceholderText": [Function],
  "getAllByRole": [Function],
  "getAllByTestId": [Function],
  "getAllByText": [Function],
  "getAllByTitle": [Function],
  "getByAltText": [Function],
  "getByDisplayValue": [Function],
  "getByLabelText": [Function],
  "getByPlaceholderText": [Function],
  "getByRole": [Function],
  "getByTestId": [Function],
  "getByText": [Function],
  "getByTitle": [Function],
  "queryAllByAltText": [Function],
  "queryAllByDisplayValue": [Function],
  "queryAllByLabelText": [Function],
  "queryAllByPlaceholderText": [Function],
  "queryAllByRole": [Function],
  "queryAllByTestId": [Function],
  "queryAllByText": [Function],
  "queryAllByTitle": [Function],
  "queryByAltText": [Function],
  "queryByDisplayValue": [Function],
  "queryByLabelText": [Function],
  "queryByPlaceholderText": [Function],
  "queryByRole": [Function],
  "queryByTestId": [Function],
  "queryByText": [Function],
  "queryByTitle": [Function],
  "rerender": [Function],
  "unmount": [Function],
}
`;

exports[`<ProductGroup /> renders correctly with productCardWrapper and altViewExperimentEnabled 1`] = `
{
  "asFragment": [Function],
  "baseElement": .emotion-0 {
  box-sizing: border-box;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-flex-wrap: wrap;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
}

.emotion-1 {
  box-sizing: border-box;
  -webkit-flex-basis: 100%;
  -ms-flex-preferred-size: 100%;
  flex-basis: 100%;
  -webkit-box-flex: 0;
  -webkit-flex-grow: 0;
  -ms-flex-positive: 0;
  flex-grow: 0;
  margin: 0;
  max-width: 100%;
  padding: 0em;
}

.emotion-2 {
  overflow: hidden;
  margin: 0;
  margin-bottom: 2rem;
  padding: 0;
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 700;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 1.067rem;
  color: #666;
  line-height: normal;
  text-align: center;
}

.emotion-3 {
  text-transform: uppercase;
  letter-spacing: 0.15em;
  display: inline-block;
  position: relative;
  padding: 0 0.5rem;
}

.emotion-3::before,
.emotion-3::after {
  border-top: 2px solid #666;
  display: block;
  position: absolute;
  width: 100em;
  top: 50%;
  content: '';
}

.emotion-3::before {
  right: 100%;
}

.emotion-3::after {
  left: 100%;
}

.emotion-4 {
  --columnSize: 1;
  box-sizing: border-box;
  -webkit-flex-basis: 33.333333333333336%;
  -ms-flex-preferred-size: 33.333333333333336%;
  flex-basis: 33.333333333333336%;
  -webkit-box-flex: 0;
  -webkit-flex-grow: 0;
  -ms-flex-positive: 0;
  flex-grow: 0;
  margin: 0;
  max-width: 33.333333333333336%;
  padding: 0.5em;
}

.emotion-5 {
  min-height: 430px;
  position: relative;
}

.emotion-5 .product-card__image {
  width: 100%;
  display: block;
}

.emotion-6 {
  margin-bottom: 0.5rem;
}

.emotion-8 {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  color: #666;
  line-height: 1.4;
  text-transform: none;
  font-weight: normal;
  font-size: 0.8rem;
}

.emotion-9 {
  text-transform: none;
  line-height: 1.4;
  color: #666;
  font-size: 0.8rem!important;
  letter-spacing: normal;
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-9 .product-price__markdown,
.emotion-9 .product-price--markdown {
  color: #666;
}

.emotion-9 .product-price__highlight,
.emotion-9 .product-price--highlight {
  text-transform: none;
  color: #D00000;
}

.emotion-9 .product-price__highlight priceHighlightFont,
.emotion-9 .product-price--highlight priceHighlightFont {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-9 .product-price__highlight--br,
.emotion-9 .product-price--highlight-br {
  text-transform: none;
  font-size: 0.8125rem;
}

.emotion-9 .product-price__markdown {
  text-transform: none;
  color: #666;
}

.emotion-9 .product-price__no-strike {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-9 .product-price__option {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 700;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-9 .product-price__option--br {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-9 .product-price__percentage-off {
  color: #D00000;
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-transform: none;
  padding-left: 5px;
}

.emotion-9 .product-price__percentage-off-no-padding {
  color: #D00000;
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-transform: none;
}

.emotion-9 .product-price__regular {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-9 .product-price__sale {
  color: #666;
}

.emotion-9 .product-price__strike {
  -webkit-text-decoration: line-through;
  text-decoration: line-through;
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-9 .product-price__same-line {
  padding-left: 5px;
}

.emotion-10 {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  color: #666;
  line-height: 1.4;
  text-transform: none;
  font-weight: bold;
  font-size: 0.8rem;
}

<body>
    <div>
      <div
        class="emotion-0"
        data-testid="grid-root"
        spacing="0.5"
      >
        <div
          class="emotion-1"
          data-testid="grid-root"
          spacing="0"
        >
          <div
            id="subcatHeader_1234"
          />
          <div
            class="emotion-2"
          >
            <h2
              class="emotion-3"
            >
              test group
            </h2>
          </div>
        </div>
        <div
          class="emotion-4"
          data-testid="grid-root"
          spacing="0.5"
        >
          <div
            class="product-card emotion-5"
          >
            <div
              class="product-card__image-wrapper emotion-6"
              id="product123"
            >
              <a
                aria-hidden="false"
                class="emotion-7"
                href="http://localhost/browse/product.do?pid=123&cid=1234&pcid=23456&grid=source_0_4_1"
                target="_self"
              >
                <img
                  alt="sherpa fleece hoodie"
                  class="product-card__image"
                  src="http://www3.assets-gap.com/webcontent/0013/882/196/cn13882196.jpg"
                />
              </a>
            </div>
            <a
              href="http://localhost/browse/product.do?pid=123&cid=1234&pcid=23456&grid=source_0_4_1"
            >
              <div
                class="emotion-8"
              >
                Sherpa fleece hoodie
              </div>
            </a>
            <div>
              <div
                class="product-card-price"
              >
                <div
                  class="emotion-9"
                >
                  <div>
                    <div
                      class="product-price__markdown"
                    >
                      <span
                        aria-label="price.regular_price_aria_label"
                        class="product-price__no-strike"
                        role="text"
                      >
                        $69.95
                      </span>
                    </div>
                    <div
                      aria-label="price.current_price_range_aria_label"
                      class="product-price__highlight"
                      role="text"
                    >
                      <span
                        class="product-price__highlight"
                      >
                        $49.99
                         - 
                        $54.99
                      </span>
                    </div>
                  </div>
                </div>
              </div>
              <div
                class="product-card__marketing-flag emotion-10"
              >
                Final Sale
              </div>
            </div>
          </div>
        </div>
        <div
          class="emotion-4"
          data-testid="grid-root"
          spacing="0.5"
        >
          <div
            class="product-card emotion-5"
          >
            <div
              class="product-card__image-wrapper emotion-6"
              id="product123"
            >
              <a
                aria-hidden="false"
                class="emotion-7"
                href="http://localhost/browse/product.do?pid=123&cid=1234&pcid=23456&grid=source_1_4_1"
                target="_self"
              >
                <img
                  alt="sherpa fleece hoodie"
                  class="product-card__image"
                  src="http://www3.assets-gap.com/webcontent/0013/882/196/cn13882196.jpg"
                />
              </a>
            </div>
            <a
              href="http://localhost/browse/product.do?pid=123&cid=1234&pcid=23456&grid=source_1_4_1"
            >
              <div
                class="emotion-8"
              >
                Sherpa fleece hoodie
              </div>
            </a>
            <div>
              <div
                class="product-card-price"
              >
                <div
                  class="emotion-9"
                >
                  <div>
                    <div
                      class="product-price__markdown"
                    >
                      <span
                        aria-label="price.regular_price_aria_label"
                        class="product-price__no-strike"
                        role="text"
                      >
                        $69.95
                      </span>
                    </div>
                    <div
                      aria-label="price.current_price_range_aria_label"
                      class="product-price__highlight"
                      role="text"
                    >
                      <span
                        class="product-price__highlight"
                      >
                        $49.99
                         - 
                        $54.99
                      </span>
                    </div>
                  </div>
                </div>
              </div>
              <div
                class="product-card__marketing-flag emotion-10"
              >
                Final Sale
              </div>
            </div>
          </div>
        </div>
        <div
          class="emotion-4"
          data-testid="grid-root"
          spacing="0.5"
        >
          <div
            class="product-card emotion-5"
          >
            <div
              class="product-card__image-wrapper emotion-6"
              id="product123"
            >
              <a
                aria-hidden="false"
                class="emotion-7"
                href="http://localhost/browse/product.do?pid=123&cid=1234&pcid=23456&grid=source_2_4_1"
                target="_self"
              >
                <img
                  alt="sherpa fleece hoodie"
                  class="product-card__image"
                  src="http://www3.assets-gap.com/webcontent/0013/882/196/cn13882196.jpg"
                />
              </a>
            </div>
            <a
              href="http://localhost/browse/product.do?pid=123&cid=1234&pcid=23456&grid=source_2_4_1"
            >
              <div
                class="emotion-8"
              >
                Sherpa fleece hoodie
              </div>
            </a>
            <div>
              <div
                class="product-card-price"
              >
                <div
                  class="emotion-9"
                >
                  <div>
                    <div
                      class="product-price__markdown"
                    >
                      <span
                        aria-label="price.regular_price_aria_label"
                        class="product-price__no-strike"
                        role="text"
                      >
                        $69.95
                      </span>
                    </div>
                    <div
                      aria-label="price.current_price_range_aria_label"
                      class="product-price__highlight"
                      role="text"
                    >
                      <span
                        class="product-price__highlight"
                      >
                        $49.99
                         - 
                        $54.99
                      </span>
                    </div>
                  </div>
                </div>
              </div>
              <div
                class="product-card__marketing-flag emotion-10"
              >
                Final Sale
              </div>
            </div>
          </div>
        </div>
        <div
          class="emotion-4"
          data-testid="grid-root"
          spacing="0.5"
        >
          <div
            class="product-card emotion-5"
          >
            <div
              class="product-card__image-wrapper emotion-6"
              id="product124"
            >
              <a
                aria-hidden="false"
                class="emotion-7"
                href="http://localhost/browse/product.do?pid=124&cid=1234&pcid=23456&grid=source_3_4_1"
                target="_self"
              >
                <img
                  alt="Indigo zip hoodie"
                  class="product-card__image"
                  src="http://www.gap.com/webcontent/0013/707/658/cn13707658.jpg"
                />
              </a>
            </div>
            <a
              href="http://localhost/browse/product.do?pid=124&cid=1234&pcid=23456&grid=source_3_4_1"
            >
              <div
                class="emotion-8"
              >
                Indigo zip hoodie
              </div>
            </a>
            <div>
              <div
                class="product-card-price"
              >
                <div
                  class="emotion-9"
                >
                  <div>
                    <div
                      class="product-price__markdown"
                    >
                      <span
                        aria-label="price.regular_price_aria_label"
                        class="product-price__no-strike"
                        role="text"
                      >
                        $69.95
                      </span>
                    </div>
                    <div
                      aria-label="price.current_price_range_aria_label"
                      class="product-price__highlight"
                      role="text"
                    >
                      <span
                        class="product-price__highlight"
                      >
                        $49.99
                         - 
                        $54.99
                      </span>
                    </div>
                  </div>
                </div>
              </div>
              <div
                class="product-card__marketing-flag emotion-10"
              >
                Final Sale
              </div>
            </div>
          </div>
        </div>
        <div
          class="emotion-1"
          data-testid="grid-root"
          spacing="0"
        >
          <div
            id="subcatHeader_1234"
          />
          <div />
        </div>
      </div>
    </div>
  </body>,
  "container": .emotion-0 {
  box-sizing: border-box;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-flex-wrap: wrap;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
}

.emotion-1 {
  box-sizing: border-box;
  -webkit-flex-basis: 100%;
  -ms-flex-preferred-size: 100%;
  flex-basis: 100%;
  -webkit-box-flex: 0;
  -webkit-flex-grow: 0;
  -ms-flex-positive: 0;
  flex-grow: 0;
  margin: 0;
  max-width: 100%;
  padding: 0em;
}

.emotion-2 {
  overflow: hidden;
  margin: 0;
  margin-bottom: 2rem;
  padding: 0;
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 700;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 1.067rem;
  color: #666;
  line-height: normal;
  text-align: center;
}

.emotion-3 {
  text-transform: uppercase;
  letter-spacing: 0.15em;
  display: inline-block;
  position: relative;
  padding: 0 0.5rem;
}

.emotion-3::before,
.emotion-3::after {
  border-top: 2px solid #666;
  display: block;
  position: absolute;
  width: 100em;
  top: 50%;
  content: '';
}

.emotion-3::before {
  right: 100%;
}

.emotion-3::after {
  left: 100%;
}

.emotion-4 {
  --columnSize: 1;
  box-sizing: border-box;
  -webkit-flex-basis: 33.333333333333336%;
  -ms-flex-preferred-size: 33.333333333333336%;
  flex-basis: 33.333333333333336%;
  -webkit-box-flex: 0;
  -webkit-flex-grow: 0;
  -ms-flex-positive: 0;
  flex-grow: 0;
  margin: 0;
  max-width: 33.333333333333336%;
  padding: 0.5em;
}

.emotion-5 {
  min-height: 430px;
  position: relative;
}

.emotion-5 .product-card__image {
  width: 100%;
  display: block;
}

.emotion-6 {
  margin-bottom: 0.5rem;
}

.emotion-8 {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  color: #666;
  line-height: 1.4;
  text-transform: none;
  font-weight: normal;
  font-size: 0.8rem;
}

.emotion-9 {
  text-transform: none;
  line-height: 1.4;
  color: #666;
  font-size: 0.8rem!important;
  letter-spacing: normal;
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-9 .product-price__markdown,
.emotion-9 .product-price--markdown {
  color: #666;
}

.emotion-9 .product-price__highlight,
.emotion-9 .product-price--highlight {
  text-transform: none;
  color: #D00000;
}

.emotion-9 .product-price__highlight priceHighlightFont,
.emotion-9 .product-price--highlight priceHighlightFont {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-9 .product-price__highlight--br,
.emotion-9 .product-price--highlight-br {
  text-transform: none;
  font-size: 0.8125rem;
}

.emotion-9 .product-price__markdown {
  text-transform: none;
  color: #666;
}

.emotion-9 .product-price__no-strike {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-9 .product-price__option {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 700;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-9 .product-price__option--br {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-9 .product-price__percentage-off {
  color: #D00000;
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-transform: none;
  padding-left: 5px;
}

.emotion-9 .product-price__percentage-off-no-padding {
  color: #D00000;
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-transform: none;
}

.emotion-9 .product-price__regular {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-9 .product-price__sale {
  color: #666;
}

.emotion-9 .product-price__strike {
  -webkit-text-decoration: line-through;
  text-decoration: line-through;
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-9 .product-price__same-line {
  padding-left: 5px;
}

.emotion-10 {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  color: #666;
  line-height: 1.4;
  text-transform: none;
  font-weight: bold;
  font-size: 0.8rem;
}

<div>
    <div
      class="emotion-0"
      data-testid="grid-root"
      spacing="0.5"
    >
      <div
        class="emotion-1"
        data-testid="grid-root"
        spacing="0"
      >
        <div
          id="subcatHeader_1234"
        />
        <div
          class="emotion-2"
        >
          <h2
            class="emotion-3"
          >
            test group
          </h2>
        </div>
      </div>
      <div
        class="emotion-4"
        data-testid="grid-root"
        spacing="0.5"
      >
        <div
          class="product-card emotion-5"
        >
          <div
            class="product-card__image-wrapper emotion-6"
            id="product123"
          >
            <a
              aria-hidden="false"
              class="emotion-7"
              href="http://localhost/browse/product.do?pid=123&cid=1234&pcid=23456&grid=source_0_4_1"
              target="_self"
            >
              <img
                alt="sherpa fleece hoodie"
                class="product-card__image"
                src="http://www3.assets-gap.com/webcontent/0013/882/196/cn13882196.jpg"
              />
            </a>
          </div>
          <a
            href="http://localhost/browse/product.do?pid=123&cid=1234&pcid=23456&grid=source_0_4_1"
          >
            <div
              class="emotion-8"
            >
              Sherpa fleece hoodie
            </div>
          </a>
          <div>
            <div
              class="product-card-price"
            >
              <div
                class="emotion-9"
              >
                <div>
                  <div
                    class="product-price__markdown"
                  >
                    <span
                      aria-label="price.regular_price_aria_label"
                      class="product-price__no-strike"
                      role="text"
                    >
                      $69.95
                    </span>
                  </div>
                  <div
                    aria-label="price.current_price_range_aria_label"
                    class="product-price__highlight"
                    role="text"
                  >
                    <span
                      class="product-price__highlight"
                    >
                      $49.99
                       - 
                      $54.99
                    </span>
                  </div>
                </div>
              </div>
            </div>
            <div
              class="product-card__marketing-flag emotion-10"
            >
              Final Sale
            </div>
          </div>
        </div>
      </div>
      <div
        class="emotion-4"
        data-testid="grid-root"
        spacing="0.5"
      >
        <div
          class="product-card emotion-5"
        >
          <div
            class="product-card__image-wrapper emotion-6"
            id="product123"
          >
            <a
              aria-hidden="false"
              class="emotion-7"
              href="http://localhost/browse/product.do?pid=123&cid=1234&pcid=23456&grid=source_1_4_1"
              target="_self"
            >
              <img
                alt="sherpa fleece hoodie"
                class="product-card__image"
                src="http://www3.assets-gap.com/webcontent/0013/882/196/cn13882196.jpg"
              />
            </a>
          </div>
          <a
            href="http://localhost/browse/product.do?pid=123&cid=1234&pcid=23456&grid=source_1_4_1"
          >
            <div
              class="emotion-8"
            >
              Sherpa fleece hoodie
            </div>
          </a>
          <div>
            <div
              class="product-card-price"
            >
              <div
                class="emotion-9"
              >
                <div>
                  <div
                    class="product-price__markdown"
                  >
                    <span
                      aria-label="price.regular_price_aria_label"
                      class="product-price__no-strike"
                      role="text"
                    >
                      $69.95
                    </span>
                  </div>
                  <div
                    aria-label="price.current_price_range_aria_label"
                    class="product-price__highlight"
                    role="text"
                  >
                    <span
                      class="product-price__highlight"
                    >
                      $49.99
                       - 
                      $54.99
                    </span>
                  </div>
                </div>
              </div>
            </div>
            <div
              class="product-card__marketing-flag emotion-10"
            >
              Final Sale
            </div>
          </div>
        </div>
      </div>
      <div
        class="emotion-4"
        data-testid="grid-root"
        spacing="0.5"
      >
        <div
          class="product-card emotion-5"
        >
          <div
            class="product-card__image-wrapper emotion-6"
            id="product123"
          >
            <a
              aria-hidden="false"
              class="emotion-7"
              href="http://localhost/browse/product.do?pid=123&cid=1234&pcid=23456&grid=source_2_4_1"
              target="_self"
            >
              <img
                alt="sherpa fleece hoodie"
                class="product-card__image"
                src="http://www3.assets-gap.com/webcontent/0013/882/196/cn13882196.jpg"
              />
            </a>
          </div>
          <a
            href="http://localhost/browse/product.do?pid=123&cid=1234&pcid=23456&grid=source_2_4_1"
          >
            <div
              class="emotion-8"
            >
              Sherpa fleece hoodie
            </div>
          </a>
          <div>
            <div
              class="product-card-price"
            >
              <div
                class="emotion-9"
              >
                <div>
                  <div
                    class="product-price__markdown"
                  >
                    <span
                      aria-label="price.regular_price_aria_label"
                      class="product-price__no-strike"
                      role="text"
                    >
                      $69.95
                    </span>
                  </div>
                  <div
                    aria-label="price.current_price_range_aria_label"
                    class="product-price__highlight"
                    role="text"
                  >
                    <span
                      class="product-price__highlight"
                    >
                      $49.99
                       - 
                      $54.99
                    </span>
                  </div>
                </div>
              </div>
            </div>
            <div
              class="product-card__marketing-flag emotion-10"
            >
              Final Sale
            </div>
          </div>
        </div>
      </div>
      <div
        class="emotion-4"
        data-testid="grid-root"
        spacing="0.5"
      >
        <div
          class="product-card emotion-5"
        >
          <div
            class="product-card__image-wrapper emotion-6"
            id="product124"
          >
            <a
              aria-hidden="false"
              class="emotion-7"
              href="http://localhost/browse/product.do?pid=124&cid=1234&pcid=23456&grid=source_3_4_1"
              target="_self"
            >
              <img
                alt="Indigo zip hoodie"
                class="product-card__image"
                src="http://www.gap.com/webcontent/0013/707/658/cn13707658.jpg"
              />
            </a>
          </div>
          <a
            href="http://localhost/browse/product.do?pid=124&cid=1234&pcid=23456&grid=source_3_4_1"
          >
            <div
              class="emotion-8"
            >
              Indigo zip hoodie
            </div>
          </a>
          <div>
            <div
              class="product-card-price"
            >
              <div
                class="emotion-9"
              >
                <div>
                  <div
                    class="product-price__markdown"
                  >
                    <span
                      aria-label="price.regular_price_aria_label"
                      class="product-price__no-strike"
                      role="text"
                    >
                      $69.95
                    </span>
                  </div>
                  <div
                    aria-label="price.current_price_range_aria_label"
                    class="product-price__highlight"
                    role="text"
                  >
                    <span
                      class="product-price__highlight"
                    >
                      $49.99
                       - 
                      $54.99
                    </span>
                  </div>
                </div>
              </div>
            </div>
            <div
              class="product-card__marketing-flag emotion-10"
            >
              Final Sale
            </div>
          </div>
        </div>
      </div>
      <div
        class="emotion-1"
        data-testid="grid-root"
        spacing="0"
      >
        <div
          id="subcatHeader_1234"
        />
        <div />
      </div>
    </div>
  </div>,
  "debug": [Function],
  "findAllByAltText": [Function],
  "findAllByDisplayValue": [Function],
  "findAllByLabelText": [Function],
  "findAllByPlaceholderText": [Function],
  "findAllByRole": [Function],
  "findAllByTestId": [Function],
  "findAllByText": [Function],
  "findAllByTitle": [Function],
  "findByAltText": [Function],
  "findByDisplayValue": [Function],
  "findByLabelText": [Function],
  "findByPlaceholderText": [Function],
  "findByRole": [Function],
  "findByTestId": [Function],
  "findByText": [Function],
  "findByTitle": [Function],
  "getAllByAltText": [Function],
  "getAllByDisplayValue": [Function],
  "getAllByLabelText": [Function],
  "getAllByPlaceholderText": [Function],
  "getAllByRole": [Function],
  "getAllByTestId": [Function],
  "getAllByText": [Function],
  "getAllByTitle": [Function],
  "getByAltText": [Function],
  "getByDisplayValue": [Function],
  "getByLabelText": [Function],
  "getByPlaceholderText": [Function],
  "getByRole": [Function],
  "getByTestId": [Function],
  "getByText": [Function],
  "getByTitle": [Function],
  "queryAllByAltText": [Function],
  "queryAllByDisplayValue": [Function],
  "queryAllByLabelText": [Function],
  "queryAllByPlaceholderText": [Function],
  "queryAllByRole": [Function],
  "queryAllByTestId": [Function],
  "queryAllByText": [Function],
  "queryAllByTitle": [Function],
  "queryByAltText": [Function],
  "queryByDisplayValue": [Function],
  "queryByLabelText": [Function],
  "queryByPlaceholderText": [Function],
  "queryByRole": [Function],
  "queryByTestId": [Function],
  "queryByText": [Function],
  "queryByTitle": [Function],
  "rerender": [Function],
  "unmount": [Function],
}
`;

exports[`<ProductGroup /> renders default product card if none was sent 1`] = `
{
  "asFragment": [Function],
  "baseElement": .emotion-0 {
  box-sizing: border-box;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-flex-wrap: wrap;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
}

.emotion-1 {
  box-sizing: border-box;
  -webkit-flex-basis: 100%;
  -ms-flex-preferred-size: 100%;
  flex-basis: 100%;
  -webkit-box-flex: 0;
  -webkit-flex-grow: 0;
  -ms-flex-positive: 0;
  flex-grow: 0;
  margin: 0;
  max-width: 100%;
  padding: 0em;
}

.emotion-2 {
  overflow: hidden;
  margin: 0;
  margin-bottom: 2rem;
  padding: 0;
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 700;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 1.067rem;
  color: #666;
  line-height: normal;
  text-align: center;
}

.emotion-3 {
  text-transform: uppercase;
  letter-spacing: 0.15em;
  display: inline-block;
  position: relative;
  padding: 0 0.5rem;
}

.emotion-3::before,
.emotion-3::after {
  border-top: 2px solid #666;
  display: block;
  position: absolute;
  width: 100em;
  top: 50%;
  content: '';
}

.emotion-3::before {
  right: 100%;
}

.emotion-3::after {
  left: 100%;
}

.emotion-4 {
  --columnSize: 1;
  box-sizing: border-box;
  -webkit-flex-basis: 33.333333333333336%;
  -ms-flex-preferred-size: 33.333333333333336%;
  flex-basis: 33.333333333333336%;
  -webkit-box-flex: 0;
  -webkit-flex-grow: 0;
  -ms-flex-positive: 0;
  flex-grow: 0;
  margin: 0;
  max-width: 33.333333333333336%;
  padding: 0.5em;
}

.emotion-5 {
  min-height: 430px;
  position: relative;
}

.emotion-5 .product-card__image {
  width: 100%;
  display: block;
}

.emotion-6 {
  margin-bottom: 0.5rem;
}

.emotion-8 {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  color: #666;
  line-height: 1.4;
  text-transform: none;
  font-weight: normal;
  font-size: 0.8rem;
}

.emotion-9 {
  text-transform: none;
  line-height: 1.4;
  color: #666;
  font-size: 0.8rem!important;
  letter-spacing: normal;
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-9 .product-price__markdown,
.emotion-9 .product-price--markdown {
  color: #666;
}

.emotion-9 .product-price__highlight,
.emotion-9 .product-price--highlight {
  text-transform: none;
  color: #D00000;
}

.emotion-9 .product-price__highlight priceHighlightFont,
.emotion-9 .product-price--highlight priceHighlightFont {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-9 .product-price__highlight--br,
.emotion-9 .product-price--highlight-br {
  text-transform: none;
  font-size: 0.8125rem;
}

.emotion-9 .product-price__markdown {
  text-transform: none;
  color: #666;
}

.emotion-9 .product-price__no-strike {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-9 .product-price__option {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 700;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-9 .product-price__option--br {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-9 .product-price__percentage-off {
  color: #D00000;
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-transform: none;
  padding-left: 5px;
}

.emotion-9 .product-price__percentage-off-no-padding {
  color: #D00000;
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-transform: none;
}

.emotion-9 .product-price__regular {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-9 .product-price__sale {
  color: #666;
}

.emotion-9 .product-price__strike {
  -webkit-text-decoration: line-through;
  text-decoration: line-through;
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-9 .product-price__same-line {
  padding-left: 5px;
}

.emotion-10 {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  color: #666;
  line-height: 1.4;
  text-transform: none;
  font-weight: bold;
  font-size: 0.8rem;
}

<body>
    <div>
      <div
        class="emotion-0"
        data-testid="grid-root"
        spacing="0.5"
      >
        <div
          class="emotion-1"
          data-testid="grid-root"
          spacing="0"
        >
          <div
            id="subcatHeader_1234"
          />
          <div
            class="emotion-2"
          >
            <h2
              class="emotion-3"
            >
              test group
            </h2>
          </div>
        </div>
        <div
          class="emotion-4"
          data-testid="grid-root"
          spacing="0.5"
        >
          <div
            class="product-card emotion-5"
          >
            <div
              class="product-card__image-wrapper emotion-6"
              id="product123"
            >
              <a
                aria-hidden="false"
                class="emotion-7"
                href="http://localhost/browse/product.do?pid=123&cid=1234&pcid=23456&grid=source_0_4_1"
                target="_self"
              >
                <img
                  alt="sherpa fleece hoodie"
                  class="product-card__image"
                  src="http://www3.assets-gap.com/webcontent/0013/882/196/cn13882196.jpg"
                />
              </a>
            </div>
            <a
              href="http://localhost/browse/product.do?pid=123&cid=1234&pcid=23456&grid=source_0_4_1"
            >
              <div
                class="emotion-8"
              >
                Sherpa fleece hoodie
              </div>
            </a>
            <div>
              <div
                class="product-card-price"
              >
                <div
                  class="emotion-9"
                >
                  <div>
                    <div
                      class="product-price__markdown"
                    >
                      <span
                        aria-label="price.regular_price_aria_label"
                        class="product-price__no-strike"
                        role="text"
                      >
                        $69.95
                      </span>
                    </div>
                    <div
                      aria-label="price.current_price_range_aria_label"
                      class="product-price__highlight"
                      role="text"
                    >
                      <span
                        class="product-price__highlight"
                      >
                        $49.99
                         - 
                        $54.99
                      </span>
                    </div>
                  </div>
                </div>
              </div>
              <div
                class="product-card__marketing-flag emotion-10"
              >
                Final Sale
              </div>
            </div>
          </div>
        </div>
        <div
          class="emotion-4"
          data-testid="grid-root"
          spacing="0.5"
        >
          <div
            class="product-card emotion-5"
          >
            <div
              class="product-card__image-wrapper emotion-6"
              id="product123"
            >
              <a
                aria-hidden="false"
                class="emotion-7"
                href="http://localhost/browse/product.do?pid=123&cid=1234&pcid=23456&grid=source_1_4_1"
                target="_self"
              >
                <img
                  alt="sherpa fleece hoodie"
                  class="product-card__image"
                  src="http://www3.assets-gap.com/webcontent/0013/882/196/cn13882196.jpg"
                />
              </a>
            </div>
            <a
              href="http://localhost/browse/product.do?pid=123&cid=1234&pcid=23456&grid=source_1_4_1"
            >
              <div
                class="emotion-8"
              >
                Sherpa fleece hoodie
              </div>
            </a>
            <div>
              <div
                class="product-card-price"
              >
                <div
                  class="emotion-9"
                >
                  <div>
                    <div
                      class="product-price__markdown"
                    >
                      <span
                        aria-label="price.regular_price_aria_label"
                        class="product-price__no-strike"
                        role="text"
                      >
                        $69.95
                      </span>
                    </div>
                    <div
                      aria-label="price.current_price_range_aria_label"
                      class="product-price__highlight"
                      role="text"
                    >
                      <span
                        class="product-price__highlight"
                      >
                        $49.99
                         - 
                        $54.99
                      </span>
                    </div>
                  </div>
                </div>
              </div>
              <div
                class="product-card__marketing-flag emotion-10"
              >
                Final Sale
              </div>
            </div>
          </div>
        </div>
        <div
          class="emotion-4"
          data-testid="grid-root"
          spacing="0.5"
        >
          <div
            class="product-card emotion-5"
          >
            <div
              class="product-card__image-wrapper emotion-6"
              id="product123"
            >
              <a
                aria-hidden="false"
                class="emotion-7"
                href="http://localhost/browse/product.do?pid=123&cid=1234&pcid=23456&grid=source_2_4_1"
                target="_self"
              >
                <img
                  alt="sherpa fleece hoodie"
                  class="product-card__image"
                  src="http://www3.assets-gap.com/webcontent/0013/882/196/cn13882196.jpg"
                />
              </a>
            </div>
            <a
              href="http://localhost/browse/product.do?pid=123&cid=1234&pcid=23456&grid=source_2_4_1"
            >
              <div
                class="emotion-8"
              >
                Sherpa fleece hoodie
              </div>
            </a>
            <div>
              <div
                class="product-card-price"
              >
                <div
                  class="emotion-9"
                >
                  <div>
                    <div
                      class="product-price__markdown"
                    >
                      <span
                        aria-label="price.regular_price_aria_label"
                        class="product-price__no-strike"
                        role="text"
                      >
                        $69.95
                      </span>
                    </div>
                    <div
                      aria-label="price.current_price_range_aria_label"
                      class="product-price__highlight"
                      role="text"
                    >
                      <span
                        class="product-price__highlight"
                      >
                        $49.99
                         - 
                        $54.99
                      </span>
                    </div>
                  </div>
                </div>
              </div>
              <div
                class="product-card__marketing-flag emotion-10"
              >
                Final Sale
              </div>
            </div>
          </div>
        </div>
        <div
          class="emotion-4"
          data-testid="grid-root"
          spacing="0.5"
        >
          <div
            class="product-card emotion-5"
          >
            <div
              class="product-card__image-wrapper emotion-6"
              id="product124"
            >
              <a
                aria-hidden="false"
                class="emotion-7"
                href="http://localhost/browse/product.do?pid=124&cid=1234&pcid=23456&grid=source_3_4_1"
                target="_self"
              >
                <img
                  alt="Indigo zip hoodie"
                  class="product-card__image"
                  src="http://www.gap.com/webcontent/0013/707/658/cn13707658.jpg"
                />
              </a>
            </div>
            <a
              href="http://localhost/browse/product.do?pid=124&cid=1234&pcid=23456&grid=source_3_4_1"
            >
              <div
                class="emotion-8"
              >
                Indigo zip hoodie
              </div>
            </a>
            <div>
              <div
                class="product-card-price"
              >
                <div
                  class="emotion-9"
                >
                  <div>
                    <div
                      class="product-price__markdown"
                    >
                      <span
                        aria-label="price.regular_price_aria_label"
                        class="product-price__no-strike"
                        role="text"
                      >
                        $69.95
                      </span>
                    </div>
                    <div
                      aria-label="price.current_price_range_aria_label"
                      class="product-price__highlight"
                      role="text"
                    >
                      <span
                        class="product-price__highlight"
                      >
                        $49.99
                         - 
                        $54.99
                      </span>
                    </div>
                  </div>
                </div>
              </div>
              <div
                class="product-card__marketing-flag emotion-10"
              >
                Final Sale
              </div>
            </div>
          </div>
        </div>
        <div
          class="emotion-1"
          data-testid="grid-root"
          spacing="0"
        >
          <div
            id="subcatHeader_1234"
          />
          <div />
        </div>
      </div>
    </div>
  </body>,
  "container": .emotion-0 {
  box-sizing: border-box;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-flex-wrap: wrap;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
}

.emotion-1 {
  box-sizing: border-box;
  -webkit-flex-basis: 100%;
  -ms-flex-preferred-size: 100%;
  flex-basis: 100%;
  -webkit-box-flex: 0;
  -webkit-flex-grow: 0;
  -ms-flex-positive: 0;
  flex-grow: 0;
  margin: 0;
  max-width: 100%;
  padding: 0em;
}

.emotion-2 {
  overflow: hidden;
  margin: 0;
  margin-bottom: 2rem;
  padding: 0;
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 700;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 1.067rem;
  color: #666;
  line-height: normal;
  text-align: center;
}

.emotion-3 {
  text-transform: uppercase;
  letter-spacing: 0.15em;
  display: inline-block;
  position: relative;
  padding: 0 0.5rem;
}

.emotion-3::before,
.emotion-3::after {
  border-top: 2px solid #666;
  display: block;
  position: absolute;
  width: 100em;
  top: 50%;
  content: '';
}

.emotion-3::before {
  right: 100%;
}

.emotion-3::after {
  left: 100%;
}

.emotion-4 {
  --columnSize: 1;
  box-sizing: border-box;
  -webkit-flex-basis: 33.333333333333336%;
  -ms-flex-preferred-size: 33.333333333333336%;
  flex-basis: 33.333333333333336%;
  -webkit-box-flex: 0;
  -webkit-flex-grow: 0;
  -ms-flex-positive: 0;
  flex-grow: 0;
  margin: 0;
  max-width: 33.333333333333336%;
  padding: 0.5em;
}

.emotion-5 {
  min-height: 430px;
  position: relative;
}

.emotion-5 .product-card__image {
  width: 100%;
  display: block;
}

.emotion-6 {
  margin-bottom: 0.5rem;
}

.emotion-8 {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  color: #666;
  line-height: 1.4;
  text-transform: none;
  font-weight: normal;
  font-size: 0.8rem;
}

.emotion-9 {
  text-transform: none;
  line-height: 1.4;
  color: #666;
  font-size: 0.8rem!important;
  letter-spacing: normal;
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-9 .product-price__markdown,
.emotion-9 .product-price--markdown {
  color: #666;
}

.emotion-9 .product-price__highlight,
.emotion-9 .product-price--highlight {
  text-transform: none;
  color: #D00000;
}

.emotion-9 .product-price__highlight priceHighlightFont,
.emotion-9 .product-price--highlight priceHighlightFont {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-9 .product-price__highlight--br,
.emotion-9 .product-price--highlight-br {
  text-transform: none;
  font-size: 0.8125rem;
}

.emotion-9 .product-price__markdown {
  text-transform: none;
  color: #666;
}

.emotion-9 .product-price__no-strike {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-9 .product-price__option {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 700;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-9 .product-price__option--br {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-9 .product-price__percentage-off {
  color: #D00000;
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-transform: none;
  padding-left: 5px;
}

.emotion-9 .product-price__percentage-off-no-padding {
  color: #D00000;
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-transform: none;
}

.emotion-9 .product-price__regular {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-9 .product-price__sale {
  color: #666;
}

.emotion-9 .product-price__strike {
  -webkit-text-decoration: line-through;
  text-decoration: line-through;
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-9 .product-price__same-line {
  padding-left: 5px;
}

.emotion-10 {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  color: #666;
  line-height: 1.4;
  text-transform: none;
  font-weight: bold;
  font-size: 0.8rem;
}

<div>
    <div
      class="emotion-0"
      data-testid="grid-root"
      spacing="0.5"
    >
      <div
        class="emotion-1"
        data-testid="grid-root"
        spacing="0"
      >
        <div
          id="subcatHeader_1234"
        />
        <div
          class="emotion-2"
        >
          <h2
            class="emotion-3"
          >
            test group
          </h2>
        </div>
      </div>
      <div
        class="emotion-4"
        data-testid="grid-root"
        spacing="0.5"
      >
        <div
          class="product-card emotion-5"
        >
          <div
            class="product-card__image-wrapper emotion-6"
            id="product123"
          >
            <a
              aria-hidden="false"
              class="emotion-7"
              href="http://localhost/browse/product.do?pid=123&cid=1234&pcid=23456&grid=source_0_4_1"
              target="_self"
            >
              <img
                alt="sherpa fleece hoodie"
                class="product-card__image"
                src="http://www3.assets-gap.com/webcontent/0013/882/196/cn13882196.jpg"
              />
            </a>
          </div>
          <a
            href="http://localhost/browse/product.do?pid=123&cid=1234&pcid=23456&grid=source_0_4_1"
          >
            <div
              class="emotion-8"
            >
              Sherpa fleece hoodie
            </div>
          </a>
          <div>
            <div
              class="product-card-price"
            >
              <div
                class="emotion-9"
              >
                <div>
                  <div
                    class="product-price__markdown"
                  >
                    <span
                      aria-label="price.regular_price_aria_label"
                      class="product-price__no-strike"
                      role="text"
                    >
                      $69.95
                    </span>
                  </div>
                  <div
                    aria-label="price.current_price_range_aria_label"
                    class="product-price__highlight"
                    role="text"
                  >
                    <span
                      class="product-price__highlight"
                    >
                      $49.99
                       - 
                      $54.99
                    </span>
                  </div>
                </div>
              </div>
            </div>
            <div
              class="product-card__marketing-flag emotion-10"
            >
              Final Sale
            </div>
          </div>
        </div>
      </div>
      <div
        class="emotion-4"
        data-testid="grid-root"
        spacing="0.5"
      >
        <div
          class="product-card emotion-5"
        >
          <div
            class="product-card__image-wrapper emotion-6"
            id="product123"
          >
            <a
              aria-hidden="false"
              class="emotion-7"
              href="http://localhost/browse/product.do?pid=123&cid=1234&pcid=23456&grid=source_1_4_1"
              target="_self"
            >
              <img
                alt="sherpa fleece hoodie"
                class="product-card__image"
                src="http://www3.assets-gap.com/webcontent/0013/882/196/cn13882196.jpg"
              />
            </a>
          </div>
          <a
            href="http://localhost/browse/product.do?pid=123&cid=1234&pcid=23456&grid=source_1_4_1"
          >
            <div
              class="emotion-8"
            >
              Sherpa fleece hoodie
            </div>
          </a>
          <div>
            <div
              class="product-card-price"
            >
              <div
                class="emotion-9"
              >
                <div>
                  <div
                    class="product-price__markdown"
                  >
                    <span
                      aria-label="price.regular_price_aria_label"
                      class="product-price__no-strike"
                      role="text"
                    >
                      $69.95
                    </span>
                  </div>
                  <div
                    aria-label="price.current_price_range_aria_label"
                    class="product-price__highlight"
                    role="text"
                  >
                    <span
                      class="product-price__highlight"
                    >
                      $49.99
                       - 
                      $54.99
                    </span>
                  </div>
                </div>
              </div>
            </div>
            <div
              class="product-card__marketing-flag emotion-10"
            >
              Final Sale
            </div>
          </div>
        </div>
      </div>
      <div
        class="emotion-4"
        data-testid="grid-root"
        spacing="0.5"
      >
        <div
          class="product-card emotion-5"
        >
          <div
            class="product-card__image-wrapper emotion-6"
            id="product123"
          >
            <a
              aria-hidden="false"
              class="emotion-7"
              href="http://localhost/browse/product.do?pid=123&cid=1234&pcid=23456&grid=source_2_4_1"
              target="_self"
            >
              <img
                alt="sherpa fleece hoodie"
                class="product-card__image"
                src="http://www3.assets-gap.com/webcontent/0013/882/196/cn13882196.jpg"
              />
            </a>
          </div>
          <a
            href="http://localhost/browse/product.do?pid=123&cid=1234&pcid=23456&grid=source_2_4_1"
          >
            <div
              class="emotion-8"
            >
              Sherpa fleece hoodie
            </div>
          </a>
          <div>
            <div
              class="product-card-price"
            >
              <div
                class="emotion-9"
              >
                <div>
                  <div
                    class="product-price__markdown"
                  >
                    <span
                      aria-label="price.regular_price_aria_label"
                      class="product-price__no-strike"
                      role="text"
                    >
                      $69.95
                    </span>
                  </div>
                  <div
                    aria-label="price.current_price_range_aria_label"
                    class="product-price__highlight"
                    role="text"
                  >
                    <span
                      class="product-price__highlight"
                    >
                      $49.99
                       - 
                      $54.99
                    </span>
                  </div>
                </div>
              </div>
            </div>
            <div
              class="product-card__marketing-flag emotion-10"
            >
              Final Sale
            </div>
          </div>
        </div>
      </div>
      <div
        class="emotion-4"
        data-testid="grid-root"
        spacing="0.5"
      >
        <div
          class="product-card emotion-5"
        >
          <div
            class="product-card__image-wrapper emotion-6"
            id="product124"
          >
            <a
              aria-hidden="false"
              class="emotion-7"
              href="http://localhost/browse/product.do?pid=124&cid=1234&pcid=23456&grid=source_3_4_1"
              target="_self"
            >
              <img
                alt="Indigo zip hoodie"
                class="product-card__image"
                src="http://www.gap.com/webcontent/0013/707/658/cn13707658.jpg"
              />
            </a>
          </div>
          <a
            href="http://localhost/browse/product.do?pid=124&cid=1234&pcid=23456&grid=source_3_4_1"
          >
            <div
              class="emotion-8"
            >
              Indigo zip hoodie
            </div>
          </a>
          <div>
            <div
              class="product-card-price"
            >
              <div
                class="emotion-9"
              >
                <div>
                  <div
                    class="product-price__markdown"
                  >
                    <span
                      aria-label="price.regular_price_aria_label"
                      class="product-price__no-strike"
                      role="text"
                    >
                      $69.95
                    </span>
                  </div>
                  <div
                    aria-label="price.current_price_range_aria_label"
                    class="product-price__highlight"
                    role="text"
                  >
                    <span
                      class="product-price__highlight"
                    >
                      $49.99
                       - 
                      $54.99
                    </span>
                  </div>
                </div>
              </div>
            </div>
            <div
              class="product-card__marketing-flag emotion-10"
            >
              Final Sale
            </div>
          </div>
        </div>
      </div>
      <div
        class="emotion-1"
        data-testid="grid-root"
        spacing="0"
      >
        <div
          id="subcatHeader_1234"
        />
        <div />
      </div>
    </div>
  </div>,
  "debug": [Function],
  "findAllByAltText": [Function],
  "findAllByDisplayValue": [Function],
  "findAllByLabelText": [Function],
  "findAllByPlaceholderText": [Function],
  "findAllByRole": [Function],
  "findAllByTestId": [Function],
  "findAllByText": [Function],
  "findAllByTitle": [Function],
  "findByAltText": [Function],
  "findByDisplayValue": [Function],
  "findByLabelText": [Function],
  "findByPlaceholderText": [Function],
  "findByRole": [Function],
  "findByTestId": [Function],
  "findByText": [Function],
  "findByTitle": [Function],
  "getAllByAltText": [Function],
  "getAllByDisplayValue": [Function],
  "getAllByLabelText": [Function],
  "getAllByPlaceholderText": [Function],
  "getAllByRole": [Function],
  "getAllByTestId": [Function],
  "getAllByText": [Function],
  "getAllByTitle": [Function],
  "getByAltText": [Function],
  "getByDisplayValue": [Function],
  "getByLabelText": [Function],
  "getByPlaceholderText": [Function],
  "getByRole": [Function],
  "getByTestId": [Function],
  "getByText": [Function],
  "getByTitle": [Function],
  "queryAllByAltText": [Function],
  "queryAllByDisplayValue": [Function],
  "queryAllByLabelText": [Function],
  "queryAllByPlaceholderText": [Function],
  "queryAllByRole": [Function],
  "queryAllByTestId": [Function],
  "queryAllByText": [Function],
  "queryAllByTitle": [Function],
  "queryByAltText": [Function],
  "queryByDisplayValue": [Function],
  "queryByLabelText": [Function],
  "queryByPlaceholderText": [Function],
  "queryByRole": [Function],
  "queryByTestId": [Function],
  "queryByText": [Function],
  "queryByTitle": [Function],
  "rerender": [Function],
  "unmount": [Function],
}
`;

exports[`<ProductGroup /> renders product card received in productCardComponent prop 1`] = `
{
  "asFragment": [Function],
  "baseElement": .emotion-0 {
  box-sizing: border-box;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-flex-wrap: wrap;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
}

.emotion-1 {
  box-sizing: border-box;
  -webkit-flex-basis: 100%;
  -ms-flex-preferred-size: 100%;
  flex-basis: 100%;
  -webkit-box-flex: 0;
  -webkit-flex-grow: 0;
  -ms-flex-positive: 0;
  flex-grow: 0;
  margin: 0;
  max-width: 100%;
  padding: 0em;
}

.emotion-2 {
  overflow: hidden;
  margin: 0;
  margin-bottom: 2rem;
  padding: 0;
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 700;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 1.067rem;
  color: #666;
  line-height: normal;
  text-align: center;
}

.emotion-3 {
  text-transform: uppercase;
  letter-spacing: 0.15em;
  display: inline-block;
  position: relative;
  padding: 0 0.5rem;
}

.emotion-3::before,
.emotion-3::after {
  border-top: 2px solid #666;
  display: block;
  position: absolute;
  width: 100em;
  top: 50%;
  content: '';
}

.emotion-3::before {
  right: 100%;
}

.emotion-3::after {
  left: 100%;
}

.emotion-4 {
  --columnSize: 1;
  box-sizing: border-box;
  -webkit-flex-basis: 33.333333333333336%;
  -ms-flex-preferred-size: 33.333333333333336%;
  flex-basis: 33.333333333333336%;
  -webkit-box-flex: 0;
  -webkit-flex-grow: 0;
  -ms-flex-positive: 0;
  flex-grow: 0;
  margin: 0;
  max-width: 33.333333333333336%;
  padding: 0.5em;
}

<body>
    <div>
      <div
        class="emotion-0"
        data-testid="grid-root"
        spacing="0.5"
      >
        <div
          class="emotion-1"
          data-testid="grid-root"
          spacing="0"
        >
          <div
            id="subcatHeader_1234"
          />
          <div
            class="emotion-2"
          >
            <h2
              class="emotion-3"
            >
              test group
            </h2>
          </div>
        </div>
        <div
          class="emotion-4"
          data-testid="grid-root"
          spacing="0.5"
        >
          <div>
            Product Card Component
          </div>
        </div>
        <div
          class="emotion-4"
          data-testid="grid-root"
          spacing="0.5"
        >
          <div>
            Product Card Component
          </div>
        </div>
        <div
          class="emotion-4"
          data-testid="grid-root"
          spacing="0.5"
        >
          <div>
            Product Card Component
          </div>
        </div>
        <div
          class="emotion-4"
          data-testid="grid-root"
          spacing="0.5"
        >
          <div>
            Product Card Component
          </div>
        </div>
        <div
          class="emotion-1"
          data-testid="grid-root"
          spacing="0"
        >
          <div
            id="subcatHeader_1234"
          />
          <div />
        </div>
      </div>
    </div>
  </body>,
  "container": .emotion-0 {
  box-sizing: border-box;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-flex-wrap: wrap;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
}

.emotion-1 {
  box-sizing: border-box;
  -webkit-flex-basis: 100%;
  -ms-flex-preferred-size: 100%;
  flex-basis: 100%;
  -webkit-box-flex: 0;
  -webkit-flex-grow: 0;
  -ms-flex-positive: 0;
  flex-grow: 0;
  margin: 0;
  max-width: 100%;
  padding: 0em;
}

.emotion-2 {
  overflow: hidden;
  margin: 0;
  margin-bottom: 2rem;
  padding: 0;
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 700;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 1.067rem;
  color: #666;
  line-height: normal;
  text-align: center;
}

.emotion-3 {
  text-transform: uppercase;
  letter-spacing: 0.15em;
  display: inline-block;
  position: relative;
  padding: 0 0.5rem;
}

.emotion-3::before,
.emotion-3::after {
  border-top: 2px solid #666;
  display: block;
  position: absolute;
  width: 100em;
  top: 50%;
  content: '';
}

.emotion-3::before {
  right: 100%;
}

.emotion-3::after {
  left: 100%;
}

.emotion-4 {
  --columnSize: 1;
  box-sizing: border-box;
  -webkit-flex-basis: 33.333333333333336%;
  -ms-flex-preferred-size: 33.333333333333336%;
  flex-basis: 33.333333333333336%;
  -webkit-box-flex: 0;
  -webkit-flex-grow: 0;
  -ms-flex-positive: 0;
  flex-grow: 0;
  margin: 0;
  max-width: 33.333333333333336%;
  padding: 0.5em;
}

<div>
    <div
      class="emotion-0"
      data-testid="grid-root"
      spacing="0.5"
    >
      <div
        class="emotion-1"
        data-testid="grid-root"
        spacing="0"
      >
        <div
          id="subcatHeader_1234"
        />
        <div
          class="emotion-2"
        >
          <h2
            class="emotion-3"
          >
            test group
          </h2>
        </div>
      </div>
      <div
        class="emotion-4"
        data-testid="grid-root"
        spacing="0.5"
      >
        <div>
          Product Card Component
        </div>
      </div>
      <div
        class="emotion-4"
        data-testid="grid-root"
        spacing="0.5"
      >
        <div>
          Product Card Component
        </div>
      </div>
      <div
        class="emotion-4"
        data-testid="grid-root"
        spacing="0.5"
      >
        <div>
          Product Card Component
        </div>
      </div>
      <div
        class="emotion-4"
        data-testid="grid-root"
        spacing="0.5"
      >
        <div>
          Product Card Component
        </div>
      </div>
      <div
        class="emotion-1"
        data-testid="grid-root"
        spacing="0"
      >
        <div
          id="subcatHeader_1234"
        />
        <div />
      </div>
    </div>
  </div>,
  "debug": [Function],
  "findAllByAltText": [Function],
  "findAllByDisplayValue": [Function],
  "findAllByLabelText": [Function],
  "findAllByPlaceholderText": [Function],
  "findAllByRole": [Function],
  "findAllByTestId": [Function],
  "findAllByText": [Function],
  "findAllByTitle": [Function],
  "findByAltText": [Function],
  "findByDisplayValue": [Function],
  "findByLabelText": [Function],
  "findByPlaceholderText": [Function],
  "findByRole": [Function],
  "findByTestId": [Function],
  "findByText": [Function],
  "findByTitle": [Function],
  "getAllByAltText": [Function],
  "getAllByDisplayValue": [Function],
  "getAllByLabelText": [Function],
  "getAllByPlaceholderText": [Function],
  "getAllByRole": [Function],
  "getAllByTestId": [Function],
  "getAllByText": [Function],
  "getAllByTitle": [Function],
  "getByAltText": [Function],
  "getByDisplayValue": [Function],
  "getByLabelText": [Function],
  "getByPlaceholderText": [Function],
  "getByRole": [Function],
  "getByTestId": [Function],
  "getByText": [Function],
  "getByTitle": [Function],
  "queryAllByAltText": [Function],
  "queryAllByDisplayValue": [Function],
  "queryAllByLabelText": [Function],
  "queryAllByPlaceholderText": [Function],
  "queryAllByRole": [Function],
  "queryAllByTestId": [Function],
  "queryAllByText": [Function],
  "queryAllByTitle": [Function],
  "queryByAltText": [Function],
  "queryByDisplayValue": [Function],
  "queryByLabelText": [Function],
  "queryByPlaceholderText": [Function],
  "queryByRole": [Function],
  "queryByTestId": [Function],
  "queryByText": [Function],
  "queryByTitle": [Function],
  "rerender": [Function],
  "unmount": [Function],
}
`;

exports[`<ProductGroup /> when useQuickAddFeature is true when quick add experiment is on for category page should render quick add if feature flag is true 1`] = `
{
  "asFragment": [Function],
  "baseElement": .emotion-0 {
  box-sizing: border-box;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-flex-wrap: wrap;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
}

.emotion-1 {
  box-sizing: border-box;
  -webkit-flex-basis: 100%;
  -ms-flex-preferred-size: 100%;
  flex-basis: 100%;
  -webkit-box-flex: 0;
  -webkit-flex-grow: 0;
  -ms-flex-positive: 0;
  flex-grow: 0;
  margin: 0;
  max-width: 100%;
  padding: 0em;
}

.emotion-2 {
  overflow: hidden;
  margin: 0;
  margin-bottom: 2rem;
  padding: 0;
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 700;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 1.067rem;
  color: #666;
  line-height: normal;
  text-align: center;
}

.emotion-3 {
  text-transform: uppercase;
  letter-spacing: 0.15em;
  display: inline-block;
  position: relative;
  padding: 0 0.5rem;
}

.emotion-3::before,
.emotion-3::after {
  border-top: 2px solid #666;
  display: block;
  position: absolute;
  width: 100em;
  top: 50%;
  content: '';
}

.emotion-3::before {
  right: 100%;
}

.emotion-3::after {
  left: 100%;
}

.emotion-4 {
  --columnSize: 1;
  box-sizing: border-box;
  -webkit-flex-basis: 25%;
  -ms-flex-preferred-size: 25%;
  flex-basis: 25%;
  -webkit-box-flex: 0;
  -webkit-flex-grow: 0;
  -ms-flex-positive: 0;
  flex-grow: 0;
  margin: 0;
  max-width: 25%;
  padding: 0.5em;
}

.emotion-5 {
  min-height: 430px;
  position: relative;
}

.emotion-5 .product-card__image {
  width: 100%;
  display: block;
}

.emotion-6 {
  margin-bottom: 0.5rem;
}

.emotion-7 {
  position: relative;
  margin-bottom: 0.5rem;
}

.emotion-9 {
  width: 100%;
  object-fit: cover;
  display: block;
}

.emotion-10 {
  outline: none;
  border: none;
  position: absolute;
  padding: 0;
  height: 45px;
  width: 45px;
  bottom: 0.75rem;
  right: 0.75rem;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  background: transparent;
  cursor: pointer;
}

.emotion-10:hover circle:first-of-type,
.emotion-10:focus circle:first-of-type {
  stroke: #0A5694;
  fill: #F5F5F5;
  opacity: 1;
  stroke-width: 1.5px;
}

.emotion-11 {
  -webkit-filter: drop-shadow(0 0 3px rgba(0, 0, 0, 0.075));
  filter: drop-shadow(0 0 3px rgba(0, 0, 0, 0.075));
  overflow: visible;
}

.emotion-12 {
  stroke: #002554;
}

.emotion-13 {
  fill: #002554;
}

.emotion-15 {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  color: #666;
  line-height: 1.4;
  text-transform: none;
  font-weight: normal;
  font-size: 0.8rem;
}

.emotion-16 {
  text-transform: none;
  line-height: 1.4;
  color: #666;
  font-size: 0.8rem!important;
  letter-spacing: normal;
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-16 .product-price__markdown,
.emotion-16 .product-price--markdown {
  color: #666;
}

.emotion-16 .product-price__highlight,
.emotion-16 .product-price--highlight {
  text-transform: none;
  color: #D00000;
}

.emotion-16 .product-price__highlight priceHighlightFont,
.emotion-16 .product-price--highlight priceHighlightFont {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-16 .product-price__highlight--br,
.emotion-16 .product-price--highlight-br {
  text-transform: none;
  font-size: 0.8125rem;
}

.emotion-16 .product-price__markdown {
  text-transform: none;
  color: #666;
}

.emotion-16 .product-price__no-strike {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-16 .product-price__option {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 700;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-16 .product-price__option--br {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-16 .product-price__percentage-off {
  color: #D00000;
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-transform: none;
  padding-left: 5px;
}

.emotion-16 .product-price__percentage-off-no-padding {
  color: #D00000;
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-transform: none;
}

.emotion-16 .product-price__regular {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-16 .product-price__sale {
  color: #666;
}

.emotion-16 .product-price__strike {
  -webkit-text-decoration: line-through;
  text-decoration: line-through;
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-16 .product-price__same-line {
  padding-left: 5px;
}

.emotion-17 {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  color: #666;
  line-height: 1.4;
  text-transform: none;
  font-weight: bold;
  font-size: 0.8rem;
}

<body>
    <div>
      <div
        class="emotion-0"
        data-testid="grid-root"
        spacing="0.5"
      >
        <div
          class="emotion-1"
          data-testid="grid-root"
          spacing="0"
        >
          <div
            id="subcatHeader_1234"
          />
          <div
            class="emotion-2"
          >
            <h2
              class="emotion-3"
            >
              test group
            </h2>
          </div>
        </div>
        <div
          class="emotion-4"
          data-testid="grid-root"
          spacing="0.5"
        >
          <div
            class="product-card emotion-5"
          >
            <div
              class="product-card__image-wrapper emotion-6"
              id="product123"
            >
              <div
                class="cat_product-image emotion-7"
              >
                <a
                  aria-hidden="false"
                  class="emotion-8"
                  href="http://localhost/browse/product.do?pid=123&cid=1234&pcid=23456&grid=source_0_4_1"
                  target="_self"
                >
                  <img
                    alt="sherpa fleece hoodie"
                    class="product-card__image emotion-9"
                    market="US"
                    src="http://www3.assets-gap.com/webcontent/0013/882/196/cn13882196.jpg"
                  />
                </a>
                <div
                  class="cat_quick-add"
                  id="cat-product-image-123"
                >
                  <button
                    aria-label="quickAdd.toggle-open-button-aria-label"
                    class="cat_quick-add__toggle-button emotion-10"
                    type="button"
                  >
                    <svg
                      class="cat_quick-add__icon"
                      height="36"
                      viewBox="0 0 36 36"
                      width="36"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <g
                        fill="none"
                      >
                        <circle
                          class="emotion-11"
                          cx="18"
                          cy="18"
                          fill="#FFFFFF"
                          opacity=".8"
                          r="17"
                        />
                        <path
                          class="emotion-12"
                          d="M16.253 6.826c-1.178 0-1.923.43-2.37 1.044-.626.858-.724 1.995-.716 2.681l6.165-.218c-.016-.713-.152-1.745-.754-2.523-.451-.583-1.186-.984-2.325-.984zM22.811 10.574H9.962v13.25a.934.934 0 0 0 .937.938h10.975a.934.934 0 0 0 .937-.937V10.574z"
                        />
                        <circle
                          class="emotion-13"
                          cx="22.41"
                          cy="22.754"
                          r="6.692"
                        />
                         
                        <circle
                          cx="22.41"
                          cy="22.754"
                          r="4.818"
                        />
                        <g
                          stroke="#FFFFFF"
                        >
                          <path
                            class="emotion-13"
                            d="M20.096 22.888h4.36M22.275 20.708v4.36"
                          />
                        </g>
                      </g>
                    </svg>
                  </button>
                </div>
              </div>
            </div>
            <a
              href="http://localhost/browse/product.do?pid=123&cid=1234&pcid=23456&grid=source_0_4_1"
            >
              <div
                class="emotion-15"
              >
                Sherpa fleece hoodie
              </div>
            </a>
            <div>
              <div
                class="product-card-price"
              >
                <div
                  class="emotion-16"
                >
                  <div>
                    <div
                      class="product-price__markdown"
                    >
                      <span
                        aria-label="price.regular_price_aria_label"
                        class="product-price__no-strike"
                        role="text"
                      >
                        $69.95
                      </span>
                    </div>
                    <div
                      aria-label="price.current_price_range_aria_label"
                      class="product-price__highlight"
                      role="text"
                    >
                      <span
                        class="product-price__highlight"
                      >
                        $49.99
                         - 
                        $54.99
                      </span>
                    </div>
                  </div>
                </div>
              </div>
              <div
                class="product-card__marketing-flag emotion-17"
              >
                Final Sale
              </div>
            </div>
          </div>
        </div>
        <div
          class="emotion-4"
          data-testid="grid-root"
          spacing="0.5"
        >
          <div
            class="product-card emotion-5"
          >
            <div
              class="product-card__image-wrapper emotion-6"
              id="product123"
            >
              <div
                class="cat_product-image emotion-7"
              >
                <a
                  aria-hidden="false"
                  class="emotion-8"
                  href="http://localhost/browse/product.do?pid=123&cid=1234&pcid=23456&grid=source_1_4_1"
                  target="_self"
                >
                  <img
                    alt="sherpa fleece hoodie"
                    class="product-card__image emotion-9"
                    market="US"
                    src="http://www3.assets-gap.com/webcontent/0013/882/196/cn13882196.jpg"
                  />
                </a>
                <div
                  class="cat_quick-add"
                  id="cat-product-image-123"
                >
                  <button
                    aria-label="quickAdd.toggle-open-button-aria-label"
                    class="cat_quick-add__toggle-button emotion-10"
                    type="button"
                  >
                    <svg
                      class="cat_quick-add__icon"
                      height="36"
                      viewBox="0 0 36 36"
                      width="36"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <g
                        fill="none"
                      >
                        <circle
                          class="emotion-11"
                          cx="18"
                          cy="18"
                          fill="#FFFFFF"
                          opacity=".8"
                          r="17"
                        />
                        <path
                          class="emotion-12"
                          d="M16.253 6.826c-1.178 0-1.923.43-2.37 1.044-.626.858-.724 1.995-.716 2.681l6.165-.218c-.016-.713-.152-1.745-.754-2.523-.451-.583-1.186-.984-2.325-.984zM22.811 10.574H9.962v13.25a.934.934 0 0 0 .937.938h10.975a.934.934 0 0 0 .937-.937V10.574z"
                        />
                        <circle
                          class="emotion-13"
                          cx="22.41"
                          cy="22.754"
                          r="6.692"
                        />
                         
                        <circle
                          cx="22.41"
                          cy="22.754"
                          r="4.818"
                        />
                        <g
                          stroke="#FFFFFF"
                        >
                          <path
                            class="emotion-13"
                            d="M20.096 22.888h4.36M22.275 20.708v4.36"
                          />
                        </g>
                      </g>
                    </svg>
                  </button>
                </div>
              </div>
            </div>
            <a
              href="http://localhost/browse/product.do?pid=123&cid=1234&pcid=23456&grid=source_1_4_1"
            >
              <div
                class="emotion-15"
              >
                Sherpa fleece hoodie
              </div>
            </a>
            <div>
              <div
                class="product-card-price"
              >
                <div
                  class="emotion-16"
                >
                  <div>
                    <div
                      class="product-price__markdown"
                    >
                      <span
                        aria-label="price.regular_price_aria_label"
                        class="product-price__no-strike"
                        role="text"
                      >
                        $69.95
                      </span>
                    </div>
                    <div
                      aria-label="price.current_price_range_aria_label"
                      class="product-price__highlight"
                      role="text"
                    >
                      <span
                        class="product-price__highlight"
                      >
                        $49.99
                         - 
                        $54.99
                      </span>
                    </div>
                  </div>
                </div>
              </div>
              <div
                class="product-card__marketing-flag emotion-17"
              >
                Final Sale
              </div>
            </div>
          </div>
        </div>
        <div
          class="emotion-4"
          data-testid="grid-root"
          spacing="0.5"
        >
          <div
            class="product-card emotion-5"
          >
            <div
              class="product-card__image-wrapper emotion-6"
              id="product123"
            >
              <div
                class="cat_product-image emotion-7"
              >
                <a
                  aria-hidden="false"
                  class="emotion-8"
                  href="http://localhost/browse/product.do?pid=123&cid=1234&pcid=23456&grid=source_2_4_1"
                  target="_self"
                >
                  <img
                    alt="sherpa fleece hoodie"
                    class="product-card__image emotion-9"
                    market="US"
                    src="http://www3.assets-gap.com/webcontent/0013/882/196/cn13882196.jpg"
                  />
                </a>
                <div
                  class="cat_quick-add"
                  id="cat-product-image-123"
                >
                  <button
                    aria-label="quickAdd.toggle-open-button-aria-label"
                    class="cat_quick-add__toggle-button emotion-10"
                    type="button"
                  >
                    <svg
                      class="cat_quick-add__icon"
                      height="36"
                      viewBox="0 0 36 36"
                      width="36"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <g
                        fill="none"
                      >
                        <circle
                          class="emotion-11"
                          cx="18"
                          cy="18"
                          fill="#FFFFFF"
                          opacity=".8"
                          r="17"
                        />
                        <path
                          class="emotion-12"
                          d="M16.253 6.826c-1.178 0-1.923.43-2.37 1.044-.626.858-.724 1.995-.716 2.681l6.165-.218c-.016-.713-.152-1.745-.754-2.523-.451-.583-1.186-.984-2.325-.984zM22.811 10.574H9.962v13.25a.934.934 0 0 0 .937.938h10.975a.934.934 0 0 0 .937-.937V10.574z"
                        />
                        <circle
                          class="emotion-13"
                          cx="22.41"
                          cy="22.754"
                          r="6.692"
                        />
                         
                        <circle
                          cx="22.41"
                          cy="22.754"
                          r="4.818"
                        />
                        <g
                          stroke="#FFFFFF"
                        >
                          <path
                            class="emotion-13"
                            d="M20.096 22.888h4.36M22.275 20.708v4.36"
                          />
                        </g>
                      </g>
                    </svg>
                  </button>
                </div>
              </div>
            </div>
            <a
              href="http://localhost/browse/product.do?pid=123&cid=1234&pcid=23456&grid=source_2_4_1"
            >
              <div
                class="emotion-15"
              >
                Sherpa fleece hoodie
              </div>
            </a>
            <div>
              <div
                class="product-card-price"
              >
                <div
                  class="emotion-16"
                >
                  <div>
                    <div
                      class="product-price__markdown"
                    >
                      <span
                        aria-label="price.regular_price_aria_label"
                        class="product-price__no-strike"
                        role="text"
                      >
                        $69.95
                      </span>
                    </div>
                    <div
                      aria-label="price.current_price_range_aria_label"
                      class="product-price__highlight"
                      role="text"
                    >
                      <span
                        class="product-price__highlight"
                      >
                        $49.99
                         - 
                        $54.99
                      </span>
                    </div>
                  </div>
                </div>
              </div>
              <div
                class="product-card__marketing-flag emotion-17"
              >
                Final Sale
              </div>
            </div>
          </div>
        </div>
        <div
          class="emotion-4"
          data-testid="grid-root"
          spacing="0.5"
        >
          <div
            class="product-card emotion-5"
          >
            <div
              class="product-card__image-wrapper emotion-6"
              id="product124"
            >
              <div
                class="cat_product-image emotion-7"
              >
                <a
                  aria-hidden="false"
                  class="emotion-8"
                  href="http://localhost/browse/product.do?pid=124&cid=1234&pcid=23456&grid=source_3_4_1"
                  target="_self"
                >
                  <img
                    alt="Indigo zip hoodie"
                    class="product-card__image emotion-9"
                    market="US"
                    src="http://www.gap.com/webcontent/0013/707/658/cn13707658.jpg"
                  />
                </a>
                <div
                  class="cat_quick-add"
                  id="cat-product-image-124"
                >
                  <button
                    aria-label="quickAdd.toggle-open-button-aria-label"
                    class="cat_quick-add__toggle-button emotion-10"
                    type="button"
                  >
                    <svg
                      class="cat_quick-add__icon"
                      height="36"
                      viewBox="0 0 36 36"
                      width="36"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <g
                        fill="none"
                      >
                        <circle
                          class="emotion-11"
                          cx="18"
                          cy="18"
                          fill="#FFFFFF"
                          opacity=".8"
                          r="17"
                        />
                        <path
                          class="emotion-12"
                          d="M16.253 6.826c-1.178 0-1.923.43-2.37 1.044-.626.858-.724 1.995-.716 2.681l6.165-.218c-.016-.713-.152-1.745-.754-2.523-.451-.583-1.186-.984-2.325-.984zM22.811 10.574H9.962v13.25a.934.934 0 0 0 .937.938h10.975a.934.934 0 0 0 .937-.937V10.574z"
                        />
                        <circle
                          class="emotion-13"
                          cx="22.41"
                          cy="22.754"
                          r="6.692"
                        />
                         
                        <circle
                          cx="22.41"
                          cy="22.754"
                          r="4.818"
                        />
                        <g
                          stroke="#FFFFFF"
                        >
                          <path
                            class="emotion-13"
                            d="M20.096 22.888h4.36M22.275 20.708v4.36"
                          />
                        </g>
                      </g>
                    </svg>
                  </button>
                </div>
              </div>
            </div>
            <a
              href="http://localhost/browse/product.do?pid=124&cid=1234&pcid=23456&grid=source_3_4_1"
            >
              <div
                class="emotion-15"
              >
                Indigo zip hoodie
              </div>
            </a>
            <div>
              <div
                class="product-card-price"
              >
                <div
                  class="emotion-16"
                >
                  <div>
                    <div
                      class="product-price__markdown"
                    >
                      <span
                        aria-label="price.regular_price_aria_label"
                        class="product-price__no-strike"
                        role="text"
                      >
                        $69.95
                      </span>
                    </div>
                    <div
                      aria-label="price.current_price_range_aria_label"
                      class="product-price__highlight"
                      role="text"
                    >
                      <span
                        class="product-price__highlight"
                      >
                        $49.99
                         - 
                        $54.99
                      </span>
                    </div>
                  </div>
                </div>
              </div>
              <div
                class="product-card__marketing-flag emotion-17"
              >
                Final Sale
              </div>
            </div>
          </div>
        </div>
        <div
          class="emotion-1"
          data-testid="grid-root"
          spacing="0"
        >
          <div
            id="subcatHeader_1234"
          />
          <div />
        </div>
      </div>
    </div>
  </body>,
  "container": .emotion-0 {
  box-sizing: border-box;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-flex-wrap: wrap;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
}

.emotion-1 {
  box-sizing: border-box;
  -webkit-flex-basis: 100%;
  -ms-flex-preferred-size: 100%;
  flex-basis: 100%;
  -webkit-box-flex: 0;
  -webkit-flex-grow: 0;
  -ms-flex-positive: 0;
  flex-grow: 0;
  margin: 0;
  max-width: 100%;
  padding: 0em;
}

.emotion-2 {
  overflow: hidden;
  margin: 0;
  margin-bottom: 2rem;
  padding: 0;
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 700;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 1.067rem;
  color: #666;
  line-height: normal;
  text-align: center;
}

.emotion-3 {
  text-transform: uppercase;
  letter-spacing: 0.15em;
  display: inline-block;
  position: relative;
  padding: 0 0.5rem;
}

.emotion-3::before,
.emotion-3::after {
  border-top: 2px solid #666;
  display: block;
  position: absolute;
  width: 100em;
  top: 50%;
  content: '';
}

.emotion-3::before {
  right: 100%;
}

.emotion-3::after {
  left: 100%;
}

.emotion-4 {
  --columnSize: 1;
  box-sizing: border-box;
  -webkit-flex-basis: 25%;
  -ms-flex-preferred-size: 25%;
  flex-basis: 25%;
  -webkit-box-flex: 0;
  -webkit-flex-grow: 0;
  -ms-flex-positive: 0;
  flex-grow: 0;
  margin: 0;
  max-width: 25%;
  padding: 0.5em;
}

.emotion-5 {
  min-height: 430px;
  position: relative;
}

.emotion-5 .product-card__image {
  width: 100%;
  display: block;
}

.emotion-6 {
  margin-bottom: 0.5rem;
}

.emotion-7 {
  position: relative;
  margin-bottom: 0.5rem;
}

.emotion-9 {
  width: 100%;
  object-fit: cover;
  display: block;
}

.emotion-10 {
  outline: none;
  border: none;
  position: absolute;
  padding: 0;
  height: 45px;
  width: 45px;
  bottom: 0.75rem;
  right: 0.75rem;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  background: transparent;
  cursor: pointer;
}

.emotion-10:hover circle:first-of-type,
.emotion-10:focus circle:first-of-type {
  stroke: #0A5694;
  fill: #F5F5F5;
  opacity: 1;
  stroke-width: 1.5px;
}

.emotion-11 {
  -webkit-filter: drop-shadow(0 0 3px rgba(0, 0, 0, 0.075));
  filter: drop-shadow(0 0 3px rgba(0, 0, 0, 0.075));
  overflow: visible;
}

.emotion-12 {
  stroke: #002554;
}

.emotion-13 {
  fill: #002554;
}

.emotion-15 {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  color: #666;
  line-height: 1.4;
  text-transform: none;
  font-weight: normal;
  font-size: 0.8rem;
}

.emotion-16 {
  text-transform: none;
  line-height: 1.4;
  color: #666;
  font-size: 0.8rem!important;
  letter-spacing: normal;
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-16 .product-price__markdown,
.emotion-16 .product-price--markdown {
  color: #666;
}

.emotion-16 .product-price__highlight,
.emotion-16 .product-price--highlight {
  text-transform: none;
  color: #D00000;
}

.emotion-16 .product-price__highlight priceHighlightFont,
.emotion-16 .product-price--highlight priceHighlightFont {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-16 .product-price__highlight--br,
.emotion-16 .product-price--highlight-br {
  text-transform: none;
  font-size: 0.8125rem;
}

.emotion-16 .product-price__markdown {
  text-transform: none;
  color: #666;
}

.emotion-16 .product-price__no-strike {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-16 .product-price__option {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 700;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-16 .product-price__option--br {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-16 .product-price__percentage-off {
  color: #D00000;
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-transform: none;
  padding-left: 5px;
}

.emotion-16 .product-price__percentage-off-no-padding {
  color: #D00000;
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-transform: none;
}

.emotion-16 .product-price__regular {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-16 .product-price__sale {
  color: #666;
}

.emotion-16 .product-price__strike {
  -webkit-text-decoration: line-through;
  text-decoration: line-through;
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-16 .product-price__same-line {
  padding-left: 5px;
}

.emotion-17 {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  color: #666;
  line-height: 1.4;
  text-transform: none;
  font-weight: bold;
  font-size: 0.8rem;
}

<div>
    <div
      class="emotion-0"
      data-testid="grid-root"
      spacing="0.5"
    >
      <div
        class="emotion-1"
        data-testid="grid-root"
        spacing="0"
      >
        <div
          id="subcatHeader_1234"
        />
        <div
          class="emotion-2"
        >
          <h2
            class="emotion-3"
          >
            test group
          </h2>
        </div>
      </div>
      <div
        class="emotion-4"
        data-testid="grid-root"
        spacing="0.5"
      >
        <div
          class="product-card emotion-5"
        >
          <div
            class="product-card__image-wrapper emotion-6"
            id="product123"
          >
            <div
              class="cat_product-image emotion-7"
            >
              <a
                aria-hidden="false"
                class="emotion-8"
                href="http://localhost/browse/product.do?pid=123&cid=1234&pcid=23456&grid=source_0_4_1"
                target="_self"
              >
                <img
                  alt="sherpa fleece hoodie"
                  class="product-card__image emotion-9"
                  market="US"
                  src="http://www3.assets-gap.com/webcontent/0013/882/196/cn13882196.jpg"
                />
              </a>
              <div
                class="cat_quick-add"
                id="cat-product-image-123"
              >
                <button
                  aria-label="quickAdd.toggle-open-button-aria-label"
                  class="cat_quick-add__toggle-button emotion-10"
                  type="button"
                >
                  <svg
                    class="cat_quick-add__icon"
                    height="36"
                    viewBox="0 0 36 36"
                    width="36"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <g
                      fill="none"
                    >
                      <circle
                        class="emotion-11"
                        cx="18"
                        cy="18"
                        fill="#FFFFFF"
                        opacity=".8"
                        r="17"
                      />
                      <path
                        class="emotion-12"
                        d="M16.253 6.826c-1.178 0-1.923.43-2.37 1.044-.626.858-.724 1.995-.716 2.681l6.165-.218c-.016-.713-.152-1.745-.754-2.523-.451-.583-1.186-.984-2.325-.984zM22.811 10.574H9.962v13.25a.934.934 0 0 0 .937.938h10.975a.934.934 0 0 0 .937-.937V10.574z"
                      />
                      <circle
                        class="emotion-13"
                        cx="22.41"
                        cy="22.754"
                        r="6.692"
                      />
                       
                      <circle
                        cx="22.41"
                        cy="22.754"
                        r="4.818"
                      />
                      <g
                        stroke="#FFFFFF"
                      >
                        <path
                          class="emotion-13"
                          d="M20.096 22.888h4.36M22.275 20.708v4.36"
                        />
                      </g>
                    </g>
                  </svg>
                </button>
              </div>
            </div>
          </div>
          <a
            href="http://localhost/browse/product.do?pid=123&cid=1234&pcid=23456&grid=source_0_4_1"
          >
            <div
              class="emotion-15"
            >
              Sherpa fleece hoodie
            </div>
          </a>
          <div>
            <div
              class="product-card-price"
            >
              <div
                class="emotion-16"
              >
                <div>
                  <div
                    class="product-price__markdown"
                  >
                    <span
                      aria-label="price.regular_price_aria_label"
                      class="product-price__no-strike"
                      role="text"
                    >
                      $69.95
                    </span>
                  </div>
                  <div
                    aria-label="price.current_price_range_aria_label"
                    class="product-price__highlight"
                    role="text"
                  >
                    <span
                      class="product-price__highlight"
                    >
                      $49.99
                       - 
                      $54.99
                    </span>
                  </div>
                </div>
              </div>
            </div>
            <div
              class="product-card__marketing-flag emotion-17"
            >
              Final Sale
            </div>
          </div>
        </div>
      </div>
      <div
        class="emotion-4"
        data-testid="grid-root"
        spacing="0.5"
      >
        <div
          class="product-card emotion-5"
        >
          <div
            class="product-card__image-wrapper emotion-6"
            id="product123"
          >
            <div
              class="cat_product-image emotion-7"
            >
              <a
                aria-hidden="false"
                class="emotion-8"
                href="http://localhost/browse/product.do?pid=123&cid=1234&pcid=23456&grid=source_1_4_1"
                target="_self"
              >
                <img
                  alt="sherpa fleece hoodie"
                  class="product-card__image emotion-9"
                  market="US"
                  src="http://www3.assets-gap.com/webcontent/0013/882/196/cn13882196.jpg"
                />
              </a>
              <div
                class="cat_quick-add"
                id="cat-product-image-123"
              >
                <button
                  aria-label="quickAdd.toggle-open-button-aria-label"
                  class="cat_quick-add__toggle-button emotion-10"
                  type="button"
                >
                  <svg
                    class="cat_quick-add__icon"
                    height="36"
                    viewBox="0 0 36 36"
                    width="36"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <g
                      fill="none"
                    >
                      <circle
                        class="emotion-11"
                        cx="18"
                        cy="18"
                        fill="#FFFFFF"
                        opacity=".8"
                        r="17"
                      />
                      <path
                        class="emotion-12"
                        d="M16.253 6.826c-1.178 0-1.923.43-2.37 1.044-.626.858-.724 1.995-.716 2.681l6.165-.218c-.016-.713-.152-1.745-.754-2.523-.451-.583-1.186-.984-2.325-.984zM22.811 10.574H9.962v13.25a.934.934 0 0 0 .937.938h10.975a.934.934 0 0 0 .937-.937V10.574z"
                      />
                      <circle
                        class="emotion-13"
                        cx="22.41"
                        cy="22.754"
                        r="6.692"
                      />
                       
                      <circle
                        cx="22.41"
                        cy="22.754"
                        r="4.818"
                      />
                      <g
                        stroke="#FFFFFF"
                      >
                        <path
                          class="emotion-13"
                          d="M20.096 22.888h4.36M22.275 20.708v4.36"
                        />
                      </g>
                    </g>
                  </svg>
                </button>
              </div>
            </div>
          </div>
          <a
            href="http://localhost/browse/product.do?pid=123&cid=1234&pcid=23456&grid=source_1_4_1"
          >
            <div
              class="emotion-15"
            >
              Sherpa fleece hoodie
            </div>
          </a>
          <div>
            <div
              class="product-card-price"
            >
              <div
                class="emotion-16"
              >
                <div>
                  <div
                    class="product-price__markdown"
                  >
                    <span
                      aria-label="price.regular_price_aria_label"
                      class="product-price__no-strike"
                      role="text"
                    >
                      $69.95
                    </span>
                  </div>
                  <div
                    aria-label="price.current_price_range_aria_label"
                    class="product-price__highlight"
                    role="text"
                  >
                    <span
                      class="product-price__highlight"
                    >
                      $49.99
                       - 
                      $54.99
                    </span>
                  </div>
                </div>
              </div>
            </div>
            <div
              class="product-card__marketing-flag emotion-17"
            >
              Final Sale
            </div>
          </div>
        </div>
      </div>
      <div
        class="emotion-4"
        data-testid="grid-root"
        spacing="0.5"
      >
        <div
          class="product-card emotion-5"
        >
          <div
            class="product-card__image-wrapper emotion-6"
            id="product123"
          >
            <div
              class="cat_product-image emotion-7"
            >
              <a
                aria-hidden="false"
                class="emotion-8"
                href="http://localhost/browse/product.do?pid=123&cid=1234&pcid=23456&grid=source_2_4_1"
                target="_self"
              >
                <img
                  alt="sherpa fleece hoodie"
                  class="product-card__image emotion-9"
                  market="US"
                  src="http://www3.assets-gap.com/webcontent/0013/882/196/cn13882196.jpg"
                />
              </a>
              <div
                class="cat_quick-add"
                id="cat-product-image-123"
              >
                <button
                  aria-label="quickAdd.toggle-open-button-aria-label"
                  class="cat_quick-add__toggle-button emotion-10"
                  type="button"
                >
                  <svg
                    class="cat_quick-add__icon"
                    height="36"
                    viewBox="0 0 36 36"
                    width="36"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <g
                      fill="none"
                    >
                      <circle
                        class="emotion-11"
                        cx="18"
                        cy="18"
                        fill="#FFFFFF"
                        opacity=".8"
                        r="17"
                      />
                      <path
                        class="emotion-12"
                        d="M16.253 6.826c-1.178 0-1.923.43-2.37 1.044-.626.858-.724 1.995-.716 2.681l6.165-.218c-.016-.713-.152-1.745-.754-2.523-.451-.583-1.186-.984-2.325-.984zM22.811 10.574H9.962v13.25a.934.934 0 0 0 .937.938h10.975a.934.934 0 0 0 .937-.937V10.574z"
                      />
                      <circle
                        class="emotion-13"
                        cx="22.41"
                        cy="22.754"
                        r="6.692"
                      />
                       
                      <circle
                        cx="22.41"
                        cy="22.754"
                        r="4.818"
                      />
                      <g
                        stroke="#FFFFFF"
                      >
                        <path
                          class="emotion-13"
                          d="M20.096 22.888h4.36M22.275 20.708v4.36"
                        />
                      </g>
                    </g>
                  </svg>
                </button>
              </div>
            </div>
          </div>
          <a
            href="http://localhost/browse/product.do?pid=123&cid=1234&pcid=23456&grid=source_2_4_1"
          >
            <div
              class="emotion-15"
            >
              Sherpa fleece hoodie
            </div>
          </a>
          <div>
            <div
              class="product-card-price"
            >
              <div
                class="emotion-16"
              >
                <div>
                  <div
                    class="product-price__markdown"
                  >
                    <span
                      aria-label="price.regular_price_aria_label"
                      class="product-price__no-strike"
                      role="text"
                    >
                      $69.95
                    </span>
                  </div>
                  <div
                    aria-label="price.current_price_range_aria_label"
                    class="product-price__highlight"
                    role="text"
                  >
                    <span
                      class="product-price__highlight"
                    >
                      $49.99
                       - 
                      $54.99
                    </span>
                  </div>
                </div>
              </div>
            </div>
            <div
              class="product-card__marketing-flag emotion-17"
            >
              Final Sale
            </div>
          </div>
        </div>
      </div>
      <div
        class="emotion-4"
        data-testid="grid-root"
        spacing="0.5"
      >
        <div
          class="product-card emotion-5"
        >
          <div
            class="product-card__image-wrapper emotion-6"
            id="product124"
          >
            <div
              class="cat_product-image emotion-7"
            >
              <a
                aria-hidden="false"
                class="emotion-8"
                href="http://localhost/browse/product.do?pid=124&cid=1234&pcid=23456&grid=source_3_4_1"
                target="_self"
              >
                <img
                  alt="Indigo zip hoodie"
                  class="product-card__image emotion-9"
                  market="US"
                  src="http://www.gap.com/webcontent/0013/707/658/cn13707658.jpg"
                />
              </a>
              <div
                class="cat_quick-add"
                id="cat-product-image-124"
              >
                <button
                  aria-label="quickAdd.toggle-open-button-aria-label"
                  class="cat_quick-add__toggle-button emotion-10"
                  type="button"
                >
                  <svg
                    class="cat_quick-add__icon"
                    height="36"
                    viewBox="0 0 36 36"
                    width="36"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <g
                      fill="none"
                    >
                      <circle
                        class="emotion-11"
                        cx="18"
                        cy="18"
                        fill="#FFFFFF"
                        opacity=".8"
                        r="17"
                      />
                      <path
                        class="emotion-12"
                        d="M16.253 6.826c-1.178 0-1.923.43-2.37 1.044-.626.858-.724 1.995-.716 2.681l6.165-.218c-.016-.713-.152-1.745-.754-2.523-.451-.583-1.186-.984-2.325-.984zM22.811 10.574H9.962v13.25a.934.934 0 0 0 .937.938h10.975a.934.934 0 0 0 .937-.937V10.574z"
                      />
                      <circle
                        class="emotion-13"
                        cx="22.41"
                        cy="22.754"
                        r="6.692"
                      />
                       
                      <circle
                        cx="22.41"
                        cy="22.754"
                        r="4.818"
                      />
                      <g
                        stroke="#FFFFFF"
                      >
                        <path
                          class="emotion-13"
                          d="M20.096 22.888h4.36M22.275 20.708v4.36"
                        />
                      </g>
                    </g>
                  </svg>
                </button>
              </div>
            </div>
          </div>
          <a
            href="http://localhost/browse/product.do?pid=124&cid=1234&pcid=23456&grid=source_3_4_1"
          >
            <div
              class="emotion-15"
            >
              Indigo zip hoodie
            </div>
          </a>
          <div>
            <div
              class="product-card-price"
            >
              <div
                class="emotion-16"
              >
                <div>
                  <div
                    class="product-price__markdown"
                  >
                    <span
                      aria-label="price.regular_price_aria_label"
                      class="product-price__no-strike"
                      role="text"
                    >
                      $69.95
                    </span>
                  </div>
                  <div
                    aria-label="price.current_price_range_aria_label"
                    class="product-price__highlight"
                    role="text"
                  >
                    <span
                      class="product-price__highlight"
                    >
                      $49.99
                       - 
                      $54.99
                    </span>
                  </div>
                </div>
              </div>
            </div>
            <div
              class="product-card__marketing-flag emotion-17"
            >
              Final Sale
            </div>
          </div>
        </div>
      </div>
      <div
        class="emotion-1"
        data-testid="grid-root"
        spacing="0"
      >
        <div
          id="subcatHeader_1234"
        />
        <div />
      </div>
    </div>
  </div>,
  "debug": [Function],
  "findAllByAltText": [Function],
  "findAllByDisplayValue": [Function],
  "findAllByLabelText": [Function],
  "findAllByPlaceholderText": [Function],
  "findAllByRole": [Function],
  "findAllByTestId": [Function],
  "findAllByText": [Function],
  "findAllByTitle": [Function],
  "findByAltText": [Function],
  "findByDisplayValue": [Function],
  "findByLabelText": [Function],
  "findByPlaceholderText": [Function],
  "findByRole": [Function],
  "findByTestId": [Function],
  "findByText": [Function],
  "findByTitle": [Function],
  "getAllByAltText": [Function],
  "getAllByDisplayValue": [Function],
  "getAllByLabelText": [Function],
  "getAllByPlaceholderText": [Function],
  "getAllByRole": [Function],
  "getAllByTestId": [Function],
  "getAllByText": [Function],
  "getAllByTitle": [Function],
  "getByAltText": [Function],
  "getByDisplayValue": [Function],
  "getByLabelText": [Function],
  "getByPlaceholderText": [Function],
  "getByRole": [Function],
  "getByTestId": [Function],
  "getByText": [Function],
  "getByTitle": [Function],
  "queryAllByAltText": [Function],
  "queryAllByDisplayValue": [Function],
  "queryAllByLabelText": [Function],
  "queryAllByPlaceholderText": [Function],
  "queryAllByRole": [Function],
  "queryAllByTestId": [Function],
  "queryAllByText": [Function],
  "queryAllByTitle": [Function],
  "queryByAltText": [Function],
  "queryByDisplayValue": [Function],
  "queryByLabelText": [Function],
  "queryByPlaceholderText": [Function],
  "queryByRole": [Function],
  "queryByTestId": [Function],
  "queryByText": [Function],
  "queryByTitle": [Function],
  "rerender": [Function],
  "unmount": [Function],
}
`;

exports[`<ProductGroup /> when useQuickAddFeature is true when quick add experiment is on for search page should render quick add if feature flag is true 1`] = `
{
  "asFragment": [Function],
  "baseElement": .emotion-0 {
  box-sizing: border-box;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-flex-wrap: wrap;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
}

.emotion-1 {
  box-sizing: border-box;
  -webkit-flex-basis: 100%;
  -ms-flex-preferred-size: 100%;
  flex-basis: 100%;
  -webkit-box-flex: 0;
  -webkit-flex-grow: 0;
  -ms-flex-positive: 0;
  flex-grow: 0;
  margin: 0;
  max-width: 100%;
  padding: 0em;
}

.emotion-2 {
  overflow: hidden;
  margin: 0;
  margin-bottom: 2rem;
  padding: 0;
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 700;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 1.067rem;
  color: #666;
  line-height: normal;
  text-align: center;
}

.emotion-3 {
  text-transform: uppercase;
  letter-spacing: 0.15em;
  display: inline-block;
  position: relative;
  padding: 0 0.5rem;
}

.emotion-3::before,
.emotion-3::after {
  border-top: 2px solid #666;
  display: block;
  position: absolute;
  width: 100em;
  top: 50%;
  content: '';
}

.emotion-3::before {
  right: 100%;
}

.emotion-3::after {
  left: 100%;
}

.emotion-4 {
  --columnSize: 1;
  box-sizing: border-box;
  -webkit-flex-basis: 25%;
  -ms-flex-preferred-size: 25%;
  flex-basis: 25%;
  -webkit-box-flex: 0;
  -webkit-flex-grow: 0;
  -ms-flex-positive: 0;
  flex-grow: 0;
  margin: 0;
  max-width: 25%;
  padding: 0.5em;
}

.emotion-5 {
  min-height: 430px;
  position: relative;
}

.emotion-5 .product-card__image {
  width: 100%;
  display: block;
}

.emotion-6 {
  margin-bottom: 0.5rem;
}

.emotion-7 {
  position: relative;
  margin-bottom: 0.5rem;
}

.emotion-9 {
  width: 100%;
  object-fit: cover;
  display: block;
}

.emotion-10 {
  outline: none;
  border: none;
  position: absolute;
  padding: 0;
  height: 45px;
  width: 45px;
  bottom: 0.75rem;
  right: 0.75rem;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  background: transparent;
  cursor: pointer;
}

.emotion-10:hover circle:first-of-type,
.emotion-10:focus circle:first-of-type {
  stroke: #0A5694;
  fill: #F5F5F5;
  opacity: 1;
  stroke-width: 1.5px;
}

.emotion-11 {
  -webkit-filter: drop-shadow(0 0 3px rgba(0, 0, 0, 0.075));
  filter: drop-shadow(0 0 3px rgba(0, 0, 0, 0.075));
  overflow: visible;
}

.emotion-12 {
  stroke: #002554;
}

.emotion-13 {
  fill: #002554;
}

.emotion-15 {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  color: #666;
  line-height: 1.4;
  text-transform: none;
  font-weight: normal;
  font-size: 0.8rem;
}

.emotion-16 {
  text-transform: none;
  line-height: 1.4;
  color: #666;
  font-size: 0.8rem!important;
  letter-spacing: normal;
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-16 .product-price__markdown,
.emotion-16 .product-price--markdown {
  color: #666;
}

.emotion-16 .product-price__highlight,
.emotion-16 .product-price--highlight {
  text-transform: none;
  color: #D00000;
}

.emotion-16 .product-price__highlight priceHighlightFont,
.emotion-16 .product-price--highlight priceHighlightFont {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-16 .product-price__highlight--br,
.emotion-16 .product-price--highlight-br {
  text-transform: none;
  font-size: 0.8125rem;
}

.emotion-16 .product-price__markdown {
  text-transform: none;
  color: #666;
}

.emotion-16 .product-price__no-strike {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-16 .product-price__option {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 700;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-16 .product-price__option--br {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-16 .product-price__percentage-off {
  color: #D00000;
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-transform: none;
  padding-left: 5px;
}

.emotion-16 .product-price__percentage-off-no-padding {
  color: #D00000;
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-transform: none;
}

.emotion-16 .product-price__regular {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-16 .product-price__sale {
  color: #666;
}

.emotion-16 .product-price__strike {
  -webkit-text-decoration: line-through;
  text-decoration: line-through;
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-16 .product-price__same-line {
  padding-left: 5px;
}

.emotion-17 {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  color: #666;
  line-height: 1.4;
  text-transform: none;
  font-weight: bold;
  font-size: 0.8rem;
}

<body>
    <div>
      <div
        class="emotion-0"
        data-testid="grid-root"
        spacing="0.5"
      >
        <div
          class="emotion-1"
          data-testid="grid-root"
          spacing="0"
        >
          <div
            id="subcatHeader_1234"
          />
          <div
            class="emotion-2"
          >
            <h2
              class="emotion-3"
            >
              test group
            </h2>
          </div>
        </div>
        <div
          class="emotion-4"
          data-testid="grid-root"
          spacing="0.5"
        >
          <div
            class="product-card emotion-5"
          >
            <div
              class="product-card__image-wrapper emotion-6"
              id="product123"
            >
              <div
                class="cat_product-image emotion-7"
              >
                <a
                  aria-hidden="false"
                  class="emotion-8"
                  href="http://localhost/browse/product.do?pid=123&cid=1234&pcid=23456&grid=source_0_4_1"
                  target="_self"
                >
                  <img
                    alt="sherpa fleece hoodie"
                    class="product-card__image emotion-9"
                    market="US"
                    src="http://www3.assets-gap.com/webcontent/0013/882/196/cn13882196.jpg"
                  />
                </a>
                <div
                  class="cat_quick-add"
                  id="cat-product-image-123"
                >
                  <button
                    aria-label="quickAdd.toggle-open-button-aria-label"
                    class="cat_quick-add__toggle-button emotion-10"
                    type="button"
                  >
                    <svg
                      class="cat_quick-add__icon"
                      height="36"
                      viewBox="0 0 36 36"
                      width="36"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <g
                        fill="none"
                      >
                        <circle
                          class="emotion-11"
                          cx="18"
                          cy="18"
                          fill="#FFFFFF"
                          opacity=".8"
                          r="17"
                        />
                        <path
                          class="emotion-12"
                          d="M16.253 6.826c-1.178 0-1.923.43-2.37 1.044-.626.858-.724 1.995-.716 2.681l6.165-.218c-.016-.713-.152-1.745-.754-2.523-.451-.583-1.186-.984-2.325-.984zM22.811 10.574H9.962v13.25a.934.934 0 0 0 .937.938h10.975a.934.934 0 0 0 .937-.937V10.574z"
                        />
                        <circle
                          class="emotion-13"
                          cx="22.41"
                          cy="22.754"
                          r="6.692"
                        />
                         
                        <circle
                          cx="22.41"
                          cy="22.754"
                          r="4.818"
                        />
                        <g
                          stroke="#FFFFFF"
                        >
                          <path
                            class="emotion-13"
                            d="M20.096 22.888h4.36M22.275 20.708v4.36"
                          />
                        </g>
                      </g>
                    </svg>
                  </button>
                </div>
              </div>
            </div>
            <a
              href="http://localhost/browse/product.do?pid=123&cid=1234&pcid=23456&grid=source_0_4_1"
            >
              <div
                class="emotion-15"
              >
                Sherpa fleece hoodie
              </div>
            </a>
            <div>
              <div
                class="product-card-price"
              >
                <div
                  class="emotion-16"
                >
                  <div>
                    <div
                      class="product-price__markdown"
                    >
                      <span
                        aria-label="price.regular_price_aria_label"
                        class="product-price__no-strike"
                        role="text"
                      >
                        $69.95
                      </span>
                    </div>
                    <div
                      aria-label="price.current_price_range_aria_label"
                      class="product-price__highlight"
                      role="text"
                    >
                      <span
                        class="product-price__highlight"
                      >
                        $49.99
                         - 
                        $54.99
                      </span>
                    </div>
                  </div>
                </div>
              </div>
              <div
                class="product-card__marketing-flag emotion-17"
              >
                Final Sale
              </div>
            </div>
          </div>
        </div>
        <div
          class="emotion-4"
          data-testid="grid-root"
          spacing="0.5"
        >
          <div
            class="product-card emotion-5"
          >
            <div
              class="product-card__image-wrapper emotion-6"
              id="product123"
            >
              <div
                class="cat_product-image emotion-7"
              >
                <a
                  aria-hidden="false"
                  class="emotion-8"
                  href="http://localhost/browse/product.do?pid=123&cid=1234&pcid=23456&grid=source_1_4_1"
                  target="_self"
                >
                  <img
                    alt="sherpa fleece hoodie"
                    class="product-card__image emotion-9"
                    market="US"
                    src="http://www3.assets-gap.com/webcontent/0013/882/196/cn13882196.jpg"
                  />
                </a>
                <div
                  class="cat_quick-add"
                  id="cat-product-image-123"
                >
                  <button
                    aria-label="quickAdd.toggle-open-button-aria-label"
                    class="cat_quick-add__toggle-button emotion-10"
                    type="button"
                  >
                    <svg
                      class="cat_quick-add__icon"
                      height="36"
                      viewBox="0 0 36 36"
                      width="36"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <g
                        fill="none"
                      >
                        <circle
                          class="emotion-11"
                          cx="18"
                          cy="18"
                          fill="#FFFFFF"
                          opacity=".8"
                          r="17"
                        />
                        <path
                          class="emotion-12"
                          d="M16.253 6.826c-1.178 0-1.923.43-2.37 1.044-.626.858-.724 1.995-.716 2.681l6.165-.218c-.016-.713-.152-1.745-.754-2.523-.451-.583-1.186-.984-2.325-.984zM22.811 10.574H9.962v13.25a.934.934 0 0 0 .937.938h10.975a.934.934 0 0 0 .937-.937V10.574z"
                        />
                        <circle
                          class="emotion-13"
                          cx="22.41"
                          cy="22.754"
                          r="6.692"
                        />
                         
                        <circle
                          cx="22.41"
                          cy="22.754"
                          r="4.818"
                        />
                        <g
                          stroke="#FFFFFF"
                        >
                          <path
                            class="emotion-13"
                            d="M20.096 22.888h4.36M22.275 20.708v4.36"
                          />
                        </g>
                      </g>
                    </svg>
                  </button>
                </div>
              </div>
            </div>
            <a
              href="http://localhost/browse/product.do?pid=123&cid=1234&pcid=23456&grid=source_1_4_1"
            >
              <div
                class="emotion-15"
              >
                Sherpa fleece hoodie
              </div>
            </a>
            <div>
              <div
                class="product-card-price"
              >
                <div
                  class="emotion-16"
                >
                  <div>
                    <div
                      class="product-price__markdown"
                    >
                      <span
                        aria-label="price.regular_price_aria_label"
                        class="product-price__no-strike"
                        role="text"
                      >
                        $69.95
                      </span>
                    </div>
                    <div
                      aria-label="price.current_price_range_aria_label"
                      class="product-price__highlight"
                      role="text"
                    >
                      <span
                        class="product-price__highlight"
                      >
                        $49.99
                         - 
                        $54.99
                      </span>
                    </div>
                  </div>
                </div>
              </div>
              <div
                class="product-card__marketing-flag emotion-17"
              >
                Final Sale
              </div>
            </div>
          </div>
        </div>
        <div
          class="emotion-4"
          data-testid="grid-root"
          spacing="0.5"
        >
          <div
            class="product-card emotion-5"
          >
            <div
              class="product-card__image-wrapper emotion-6"
              id="product123"
            >
              <div
                class="cat_product-image emotion-7"
              >
                <a
                  aria-hidden="false"
                  class="emotion-8"
                  href="http://localhost/browse/product.do?pid=123&cid=1234&pcid=23456&grid=source_2_4_1"
                  target="_self"
                >
                  <img
                    alt="sherpa fleece hoodie"
                    class="product-card__image emotion-9"
                    market="US"
                    src="http://www3.assets-gap.com/webcontent/0013/882/196/cn13882196.jpg"
                  />
                </a>
                <div
                  class="cat_quick-add"
                  id="cat-product-image-123"
                >
                  <button
                    aria-label="quickAdd.toggle-open-button-aria-label"
                    class="cat_quick-add__toggle-button emotion-10"
                    type="button"
                  >
                    <svg
                      class="cat_quick-add__icon"
                      height="36"
                      viewBox="0 0 36 36"
                      width="36"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <g
                        fill="none"
                      >
                        <circle
                          class="emotion-11"
                          cx="18"
                          cy="18"
                          fill="#FFFFFF"
                          opacity=".8"
                          r="17"
                        />
                        <path
                          class="emotion-12"
                          d="M16.253 6.826c-1.178 0-1.923.43-2.37 1.044-.626.858-.724 1.995-.716 2.681l6.165-.218c-.016-.713-.152-1.745-.754-2.523-.451-.583-1.186-.984-2.325-.984zM22.811 10.574H9.962v13.25a.934.934 0 0 0 .937.938h10.975a.934.934 0 0 0 .937-.937V10.574z"
                        />
                        <circle
                          class="emotion-13"
                          cx="22.41"
                          cy="22.754"
                          r="6.692"
                        />
                         
                        <circle
                          cx="22.41"
                          cy="22.754"
                          r="4.818"
                        />
                        <g
                          stroke="#FFFFFF"
                        >
                          <path
                            class="emotion-13"
                            d="M20.096 22.888h4.36M22.275 20.708v4.36"
                          />
                        </g>
                      </g>
                    </svg>
                  </button>
                </div>
              </div>
            </div>
            <a
              href="http://localhost/browse/product.do?pid=123&cid=1234&pcid=23456&grid=source_2_4_1"
            >
              <div
                class="emotion-15"
              >
                Sherpa fleece hoodie
              </div>
            </a>
            <div>
              <div
                class="product-card-price"
              >
                <div
                  class="emotion-16"
                >
                  <div>
                    <div
                      class="product-price__markdown"
                    >
                      <span
                        aria-label="price.regular_price_aria_label"
                        class="product-price__no-strike"
                        role="text"
                      >
                        $69.95
                      </span>
                    </div>
                    <div
                      aria-label="price.current_price_range_aria_label"
                      class="product-price__highlight"
                      role="text"
                    >
                      <span
                        class="product-price__highlight"
                      >
                        $49.99
                         - 
                        $54.99
                      </span>
                    </div>
                  </div>
                </div>
              </div>
              <div
                class="product-card__marketing-flag emotion-17"
              >
                Final Sale
              </div>
            </div>
          </div>
        </div>
        <div
          class="emotion-4"
          data-testid="grid-root"
          spacing="0.5"
        >
          <div
            class="product-card emotion-5"
          >
            <div
              class="product-card__image-wrapper emotion-6"
              id="product124"
            >
              <div
                class="cat_product-image emotion-7"
              >
                <a
                  aria-hidden="false"
                  class="emotion-8"
                  href="http://localhost/browse/product.do?pid=124&cid=1234&pcid=23456&grid=source_3_4_1"
                  target="_self"
                >
                  <img
                    alt="Indigo zip hoodie"
                    class="product-card__image emotion-9"
                    market="US"
                    src="http://www.gap.com/webcontent/0013/707/658/cn13707658.jpg"
                  />
                </a>
                <div
                  class="cat_quick-add"
                  id="cat-product-image-124"
                >
                  <button
                    aria-label="quickAdd.toggle-open-button-aria-label"
                    class="cat_quick-add__toggle-button emotion-10"
                    type="button"
                  >
                    <svg
                      class="cat_quick-add__icon"
                      height="36"
                      viewBox="0 0 36 36"
                      width="36"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <g
                        fill="none"
                      >
                        <circle
                          class="emotion-11"
                          cx="18"
                          cy="18"
                          fill="#FFFFFF"
                          opacity=".8"
                          r="17"
                        />
                        <path
                          class="emotion-12"
                          d="M16.253 6.826c-1.178 0-1.923.43-2.37 1.044-.626.858-.724 1.995-.716 2.681l6.165-.218c-.016-.713-.152-1.745-.754-2.523-.451-.583-1.186-.984-2.325-.984zM22.811 10.574H9.962v13.25a.934.934 0 0 0 .937.938h10.975a.934.934 0 0 0 .937-.937V10.574z"
                        />
                        <circle
                          class="emotion-13"
                          cx="22.41"
                          cy="22.754"
                          r="6.692"
                        />
                         
                        <circle
                          cx="22.41"
                          cy="22.754"
                          r="4.818"
                        />
                        <g
                          stroke="#FFFFFF"
                        >
                          <path
                            class="emotion-13"
                            d="M20.096 22.888h4.36M22.275 20.708v4.36"
                          />
                        </g>
                      </g>
                    </svg>
                  </button>
                </div>
              </div>
            </div>
            <a
              href="http://localhost/browse/product.do?pid=124&cid=1234&pcid=23456&grid=source_3_4_1"
            >
              <div
                class="emotion-15"
              >
                Indigo zip hoodie
              </div>
            </a>
            <div>
              <div
                class="product-card-price"
              >
                <div
                  class="emotion-16"
                >
                  <div>
                    <div
                      class="product-price__markdown"
                    >
                      <span
                        aria-label="price.regular_price_aria_label"
                        class="product-price__no-strike"
                        role="text"
                      >
                        $69.95
                      </span>
                    </div>
                    <div
                      aria-label="price.current_price_range_aria_label"
                      class="product-price__highlight"
                      role="text"
                    >
                      <span
                        class="product-price__highlight"
                      >
                        $49.99
                         - 
                        $54.99
                      </span>
                    </div>
                  </div>
                </div>
              </div>
              <div
                class="product-card__marketing-flag emotion-17"
              >
                Final Sale
              </div>
            </div>
          </div>
        </div>
        <div
          class="emotion-1"
          data-testid="grid-root"
          spacing="0"
        >
          <div
            id="subcatHeader_1234"
          />
          <div />
        </div>
      </div>
    </div>
  </body>,
  "container": .emotion-0 {
  box-sizing: border-box;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-flex-wrap: wrap;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
}

.emotion-1 {
  box-sizing: border-box;
  -webkit-flex-basis: 100%;
  -ms-flex-preferred-size: 100%;
  flex-basis: 100%;
  -webkit-box-flex: 0;
  -webkit-flex-grow: 0;
  -ms-flex-positive: 0;
  flex-grow: 0;
  margin: 0;
  max-width: 100%;
  padding: 0em;
}

.emotion-2 {
  overflow: hidden;
  margin: 0;
  margin-bottom: 2rem;
  padding: 0;
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 700;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 1.067rem;
  color: #666;
  line-height: normal;
  text-align: center;
}

.emotion-3 {
  text-transform: uppercase;
  letter-spacing: 0.15em;
  display: inline-block;
  position: relative;
  padding: 0 0.5rem;
}

.emotion-3::before,
.emotion-3::after {
  border-top: 2px solid #666;
  display: block;
  position: absolute;
  width: 100em;
  top: 50%;
  content: '';
}

.emotion-3::before {
  right: 100%;
}

.emotion-3::after {
  left: 100%;
}

.emotion-4 {
  --columnSize: 1;
  box-sizing: border-box;
  -webkit-flex-basis: 25%;
  -ms-flex-preferred-size: 25%;
  flex-basis: 25%;
  -webkit-box-flex: 0;
  -webkit-flex-grow: 0;
  -ms-flex-positive: 0;
  flex-grow: 0;
  margin: 0;
  max-width: 25%;
  padding: 0.5em;
}

.emotion-5 {
  min-height: 430px;
  position: relative;
}

.emotion-5 .product-card__image {
  width: 100%;
  display: block;
}

.emotion-6 {
  margin-bottom: 0.5rem;
}

.emotion-7 {
  position: relative;
  margin-bottom: 0.5rem;
}

.emotion-9 {
  width: 100%;
  object-fit: cover;
  display: block;
}

.emotion-10 {
  outline: none;
  border: none;
  position: absolute;
  padding: 0;
  height: 45px;
  width: 45px;
  bottom: 0.75rem;
  right: 0.75rem;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  background: transparent;
  cursor: pointer;
}

.emotion-10:hover circle:first-of-type,
.emotion-10:focus circle:first-of-type {
  stroke: #0A5694;
  fill: #F5F5F5;
  opacity: 1;
  stroke-width: 1.5px;
}

.emotion-11 {
  -webkit-filter: drop-shadow(0 0 3px rgba(0, 0, 0, 0.075));
  filter: drop-shadow(0 0 3px rgba(0, 0, 0, 0.075));
  overflow: visible;
}

.emotion-12 {
  stroke: #002554;
}

.emotion-13 {
  fill: #002554;
}

.emotion-15 {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  color: #666;
  line-height: 1.4;
  text-transform: none;
  font-weight: normal;
  font-size: 0.8rem;
}

.emotion-16 {
  text-transform: none;
  line-height: 1.4;
  color: #666;
  font-size: 0.8rem!important;
  letter-spacing: normal;
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-16 .product-price__markdown,
.emotion-16 .product-price--markdown {
  color: #666;
}

.emotion-16 .product-price__highlight,
.emotion-16 .product-price--highlight {
  text-transform: none;
  color: #D00000;
}

.emotion-16 .product-price__highlight priceHighlightFont,
.emotion-16 .product-price--highlight priceHighlightFont {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-16 .product-price__highlight--br,
.emotion-16 .product-price--highlight-br {
  text-transform: none;
  font-size: 0.8125rem;
}

.emotion-16 .product-price__markdown {
  text-transform: none;
  color: #666;
}

.emotion-16 .product-price__no-strike {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-16 .product-price__option {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 700;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-16 .product-price__option--br {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-16 .product-price__percentage-off {
  color: #D00000;
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-transform: none;
  padding-left: 5px;
}

.emotion-16 .product-price__percentage-off-no-padding {
  color: #D00000;
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-transform: none;
}

.emotion-16 .product-price__regular {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-16 .product-price__sale {
  color: #666;
}

.emotion-16 .product-price__strike {
  -webkit-text-decoration: line-through;
  text-decoration: line-through;
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-16 .product-price__same-line {
  padding-left: 5px;
}

.emotion-17 {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  color: #666;
  line-height: 1.4;
  text-transform: none;
  font-weight: bold;
  font-size: 0.8rem;
}

<div>
    <div
      class="emotion-0"
      data-testid="grid-root"
      spacing="0.5"
    >
      <div
        class="emotion-1"
        data-testid="grid-root"
        spacing="0"
      >
        <div
          id="subcatHeader_1234"
        />
        <div
          class="emotion-2"
        >
          <h2
            class="emotion-3"
          >
            test group
          </h2>
        </div>
      </div>
      <div
        class="emotion-4"
        data-testid="grid-root"
        spacing="0.5"
      >
        <div
          class="product-card emotion-5"
        >
          <div
            class="product-card__image-wrapper emotion-6"
            id="product123"
          >
            <div
              class="cat_product-image emotion-7"
            >
              <a
                aria-hidden="false"
                class="emotion-8"
                href="http://localhost/browse/product.do?pid=123&cid=1234&pcid=23456&grid=source_0_4_1"
                target="_self"
              >
                <img
                  alt="sherpa fleece hoodie"
                  class="product-card__image emotion-9"
                  market="US"
                  src="http://www3.assets-gap.com/webcontent/0013/882/196/cn13882196.jpg"
                />
              </a>
              <div
                class="cat_quick-add"
                id="cat-product-image-123"
              >
                <button
                  aria-label="quickAdd.toggle-open-button-aria-label"
                  class="cat_quick-add__toggle-button emotion-10"
                  type="button"
                >
                  <svg
                    class="cat_quick-add__icon"
                    height="36"
                    viewBox="0 0 36 36"
                    width="36"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <g
                      fill="none"
                    >
                      <circle
                        class="emotion-11"
                        cx="18"
                        cy="18"
                        fill="#FFFFFF"
                        opacity=".8"
                        r="17"
                      />
                      <path
                        class="emotion-12"
                        d="M16.253 6.826c-1.178 0-1.923.43-2.37 1.044-.626.858-.724 1.995-.716 2.681l6.165-.218c-.016-.713-.152-1.745-.754-2.523-.451-.583-1.186-.984-2.325-.984zM22.811 10.574H9.962v13.25a.934.934 0 0 0 .937.938h10.975a.934.934 0 0 0 .937-.937V10.574z"
                      />
                      <circle
                        class="emotion-13"
                        cx="22.41"
                        cy="22.754"
                        r="6.692"
                      />
                       
                      <circle
                        cx="22.41"
                        cy="22.754"
                        r="4.818"
                      />
                      <g
                        stroke="#FFFFFF"
                      >
                        <path
                          class="emotion-13"
                          d="M20.096 22.888h4.36M22.275 20.708v4.36"
                        />
                      </g>
                    </g>
                  </svg>
                </button>
              </div>
            </div>
          </div>
          <a
            href="http://localhost/browse/product.do?pid=123&cid=1234&pcid=23456&grid=source_0_4_1"
          >
            <div
              class="emotion-15"
            >
              Sherpa fleece hoodie
            </div>
          </a>
          <div>
            <div
              class="product-card-price"
            >
              <div
                class="emotion-16"
              >
                <div>
                  <div
                    class="product-price__markdown"
                  >
                    <span
                      aria-label="price.regular_price_aria_label"
                      class="product-price__no-strike"
                      role="text"
                    >
                      $69.95
                    </span>
                  </div>
                  <div
                    aria-label="price.current_price_range_aria_label"
                    class="product-price__highlight"
                    role="text"
                  >
                    <span
                      class="product-price__highlight"
                    >
                      $49.99
                       - 
                      $54.99
                    </span>
                  </div>
                </div>
              </div>
            </div>
            <div
              class="product-card__marketing-flag emotion-17"
            >
              Final Sale
            </div>
          </div>
        </div>
      </div>
      <div
        class="emotion-4"
        data-testid="grid-root"
        spacing="0.5"
      >
        <div
          class="product-card emotion-5"
        >
          <div
            class="product-card__image-wrapper emotion-6"
            id="product123"
          >
            <div
              class="cat_product-image emotion-7"
            >
              <a
                aria-hidden="false"
                class="emotion-8"
                href="http://localhost/browse/product.do?pid=123&cid=1234&pcid=23456&grid=source_1_4_1"
                target="_self"
              >
                <img
                  alt="sherpa fleece hoodie"
                  class="product-card__image emotion-9"
                  market="US"
                  src="http://www3.assets-gap.com/webcontent/0013/882/196/cn13882196.jpg"
                />
              </a>
              <div
                class="cat_quick-add"
                id="cat-product-image-123"
              >
                <button
                  aria-label="quickAdd.toggle-open-button-aria-label"
                  class="cat_quick-add__toggle-button emotion-10"
                  type="button"
                >
                  <svg
                    class="cat_quick-add__icon"
                    height="36"
                    viewBox="0 0 36 36"
                    width="36"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <g
                      fill="none"
                    >
                      <circle
                        class="emotion-11"
                        cx="18"
                        cy="18"
                        fill="#FFFFFF"
                        opacity=".8"
                        r="17"
                      />
                      <path
                        class="emotion-12"
                        d="M16.253 6.826c-1.178 0-1.923.43-2.37 1.044-.626.858-.724 1.995-.716 2.681l6.165-.218c-.016-.713-.152-1.745-.754-2.523-.451-.583-1.186-.984-2.325-.984zM22.811 10.574H9.962v13.25a.934.934 0 0 0 .937.938h10.975a.934.934 0 0 0 .937-.937V10.574z"
                      />
                      <circle
                        class="emotion-13"
                        cx="22.41"
                        cy="22.754"
                        r="6.692"
                      />
                       
                      <circle
                        cx="22.41"
                        cy="22.754"
                        r="4.818"
                      />
                      <g
                        stroke="#FFFFFF"
                      >
                        <path
                          class="emotion-13"
                          d="M20.096 22.888h4.36M22.275 20.708v4.36"
                        />
                      </g>
                    </g>
                  </svg>
                </button>
              </div>
            </div>
          </div>
          <a
            href="http://localhost/browse/product.do?pid=123&cid=1234&pcid=23456&grid=source_1_4_1"
          >
            <div
              class="emotion-15"
            >
              Sherpa fleece hoodie
            </div>
          </a>
          <div>
            <div
              class="product-card-price"
            >
              <div
                class="emotion-16"
              >
                <div>
                  <div
                    class="product-price__markdown"
                  >
                    <span
                      aria-label="price.regular_price_aria_label"
                      class="product-price__no-strike"
                      role="text"
                    >
                      $69.95
                    </span>
                  </div>
                  <div
                    aria-label="price.current_price_range_aria_label"
                    class="product-price__highlight"
                    role="text"
                  >
                    <span
                      class="product-price__highlight"
                    >
                      $49.99
                       - 
                      $54.99
                    </span>
                  </div>
                </div>
              </div>
            </div>
            <div
              class="product-card__marketing-flag emotion-17"
            >
              Final Sale
            </div>
          </div>
        </div>
      </div>
      <div
        class="emotion-4"
        data-testid="grid-root"
        spacing="0.5"
      >
        <div
          class="product-card emotion-5"
        >
          <div
            class="product-card__image-wrapper emotion-6"
            id="product123"
          >
            <div
              class="cat_product-image emotion-7"
            >
              <a
                aria-hidden="false"
                class="emotion-8"
                href="http://localhost/browse/product.do?pid=123&cid=1234&pcid=23456&grid=source_2_4_1"
                target="_self"
              >
                <img
                  alt="sherpa fleece hoodie"
                  class="product-card__image emotion-9"
                  market="US"
                  src="http://www3.assets-gap.com/webcontent/0013/882/196/cn13882196.jpg"
                />
              </a>
              <div
                class="cat_quick-add"
                id="cat-product-image-123"
              >
                <button
                  aria-label="quickAdd.toggle-open-button-aria-label"
                  class="cat_quick-add__toggle-button emotion-10"
                  type="button"
                >
                  <svg
                    class="cat_quick-add__icon"
                    height="36"
                    viewBox="0 0 36 36"
                    width="36"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <g
                      fill="none"
                    >
                      <circle
                        class="emotion-11"
                        cx="18"
                        cy="18"
                        fill="#FFFFFF"
                        opacity=".8"
                        r="17"
                      />
                      <path
                        class="emotion-12"
                        d="M16.253 6.826c-1.178 0-1.923.43-2.37 1.044-.626.858-.724 1.995-.716 2.681l6.165-.218c-.016-.713-.152-1.745-.754-2.523-.451-.583-1.186-.984-2.325-.984zM22.811 10.574H9.962v13.25a.934.934 0 0 0 .937.938h10.975a.934.934 0 0 0 .937-.937V10.574z"
                      />
                      <circle
                        class="emotion-13"
                        cx="22.41"
                        cy="22.754"
                        r="6.692"
                      />
                       
                      <circle
                        cx="22.41"
                        cy="22.754"
                        r="4.818"
                      />
                      <g
                        stroke="#FFFFFF"
                      >
                        <path
                          class="emotion-13"
                          d="M20.096 22.888h4.36M22.275 20.708v4.36"
                        />
                      </g>
                    </g>
                  </svg>
                </button>
              </div>
            </div>
          </div>
          <a
            href="http://localhost/browse/product.do?pid=123&cid=1234&pcid=23456&grid=source_2_4_1"
          >
            <div
              class="emotion-15"
            >
              Sherpa fleece hoodie
            </div>
          </a>
          <div>
            <div
              class="product-card-price"
            >
              <div
                class="emotion-16"
              >
                <div>
                  <div
                    class="product-price__markdown"
                  >
                    <span
                      aria-label="price.regular_price_aria_label"
                      class="product-price__no-strike"
                      role="text"
                    >
                      $69.95
                    </span>
                  </div>
                  <div
                    aria-label="price.current_price_range_aria_label"
                    class="product-price__highlight"
                    role="text"
                  >
                    <span
                      class="product-price__highlight"
                    >
                      $49.99
                       - 
                      $54.99
                    </span>
                  </div>
                </div>
              </div>
            </div>
            <div
              class="product-card__marketing-flag emotion-17"
            >
              Final Sale
            </div>
          </div>
        </div>
      </div>
      <div
        class="emotion-4"
        data-testid="grid-root"
        spacing="0.5"
      >
        <div
          class="product-card emotion-5"
        >
          <div
            class="product-card__image-wrapper emotion-6"
            id="product124"
          >
            <div
              class="cat_product-image emotion-7"
            >
              <a
                aria-hidden="false"
                class="emotion-8"
                href="http://localhost/browse/product.do?pid=124&cid=1234&pcid=23456&grid=source_3_4_1"
                target="_self"
              >
                <img
                  alt="Indigo zip hoodie"
                  class="product-card__image emotion-9"
                  market="US"
                  src="http://www.gap.com/webcontent/0013/707/658/cn13707658.jpg"
                />
              </a>
              <div
                class="cat_quick-add"
                id="cat-product-image-124"
              >
                <button
                  aria-label="quickAdd.toggle-open-button-aria-label"
                  class="cat_quick-add__toggle-button emotion-10"
                  type="button"
                >
                  <svg
                    class="cat_quick-add__icon"
                    height="36"
                    viewBox="0 0 36 36"
                    width="36"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <g
                      fill="none"
                    >
                      <circle
                        class="emotion-11"
                        cx="18"
                        cy="18"
                        fill="#FFFFFF"
                        opacity=".8"
                        r="17"
                      />
                      <path
                        class="emotion-12"
                        d="M16.253 6.826c-1.178 0-1.923.43-2.37 1.044-.626.858-.724 1.995-.716 2.681l6.165-.218c-.016-.713-.152-1.745-.754-2.523-.451-.583-1.186-.984-2.325-.984zM22.811 10.574H9.962v13.25a.934.934 0 0 0 .937.938h10.975a.934.934 0 0 0 .937-.937V10.574z"
                      />
                      <circle
                        class="emotion-13"
                        cx="22.41"
                        cy="22.754"
                        r="6.692"
                      />
                       
                      <circle
                        cx="22.41"
                        cy="22.754"
                        r="4.818"
                      />
                      <g
                        stroke="#FFFFFF"
                      >
                        <path
                          class="emotion-13"
                          d="M20.096 22.888h4.36M22.275 20.708v4.36"
                        />
                      </g>
                    </g>
                  </svg>
                </button>
              </div>
            </div>
          </div>
          <a
            href="http://localhost/browse/product.do?pid=124&cid=1234&pcid=23456&grid=source_3_4_1"
          >
            <div
              class="emotion-15"
            >
              Indigo zip hoodie
            </div>
          </a>
          <div>
            <div
              class="product-card-price"
            >
              <div
                class="emotion-16"
              >
                <div>
                  <div
                    class="product-price__markdown"
                  >
                    <span
                      aria-label="price.regular_price_aria_label"
                      class="product-price__no-strike"
                      role="text"
                    >
                      $69.95
                    </span>
                  </div>
                  <div
                    aria-label="price.current_price_range_aria_label"
                    class="product-price__highlight"
                    role="text"
                  >
                    <span
                      class="product-price__highlight"
                    >
                      $49.99
                       - 
                      $54.99
                    </span>
                  </div>
                </div>
              </div>
            </div>
            <div
              class="product-card__marketing-flag emotion-17"
            >
              Final Sale
            </div>
          </div>
        </div>
      </div>
      <div
        class="emotion-1"
        data-testid="grid-root"
        spacing="0"
      >
        <div
          id="subcatHeader_1234"
        />
        <div />
      </div>
    </div>
  </div>,
  "debug": [Function],
  "findAllByAltText": [Function],
  "findAllByDisplayValue": [Function],
  "findAllByLabelText": [Function],
  "findAllByPlaceholderText": [Function],
  "findAllByRole": [Function],
  "findAllByTestId": [Function],
  "findAllByText": [Function],
  "findAllByTitle": [Function],
  "findByAltText": [Function],
  "findByDisplayValue": [Function],
  "findByLabelText": [Function],
  "findByPlaceholderText": [Function],
  "findByRole": [Function],
  "findByTestId": [Function],
  "findByText": [Function],
  "findByTitle": [Function],
  "getAllByAltText": [Function],
  "getAllByDisplayValue": [Function],
  "getAllByLabelText": [Function],
  "getAllByPlaceholderText": [Function],
  "getAllByRole": [Function],
  "getAllByTestId": [Function],
  "getAllByText": [Function],
  "getAllByTitle": [Function],
  "getByAltText": [Function],
  "getByDisplayValue": [Function],
  "getByLabelText": [Function],
  "getByPlaceholderText": [Function],
  "getByRole": [Function],
  "getByTestId": [Function],
  "getByText": [Function],
  "getByTitle": [Function],
  "queryAllByAltText": [Function],
  "queryAllByDisplayValue": [Function],
  "queryAllByLabelText": [Function],
  "queryAllByPlaceholderText": [Function],
  "queryAllByRole": [Function],
  "queryAllByTestId": [Function],
  "queryAllByText": [Function],
  "queryAllByTitle": [Function],
  "queryByAltText": [Function],
  "queryByDisplayValue": [Function],
  "queryByLabelText": [Function],
  "queryByPlaceholderText": [Function],
  "queryByRole": [Function],
  "queryByTestId": [Function],
  "queryByText": [Function],
  "queryByTitle": [Function],
  "rerender": [Function],
  "unmount": [Function],
}
`;
