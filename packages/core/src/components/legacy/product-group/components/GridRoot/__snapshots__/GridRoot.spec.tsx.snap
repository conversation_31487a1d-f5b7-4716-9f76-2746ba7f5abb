// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`<GridRoot/> renders as a container state correctly Athleta 1`] = `
<DocumentFragment>
  .emotion-0 {
  box-sizing: border-box;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-flex-wrap: wrap;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
}

<div
    class="emotion-0"
    spacing="1"
  />
</DocumentFragment>
`;

exports[`<GridRoot/> renders as a container state correctly Athleta in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  box-sizing: border-box;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-flex-wrap: wrap;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
}

<div
    class="emotion-0"
    spacing="1"
  />
</DocumentFragment>
`;

exports[`<GridRoot/> renders as a container state correctly BananaRepublic 1`] = `
<DocumentFragment>
  .emotion-0 {
  box-sizing: border-box;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-flex-wrap: wrap;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
}

<div
    class="emotion-0"
    spacing="1"
  />
</DocumentFragment>
`;

exports[`<GridRoot/> renders as a container state correctly BananaRepublic in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  box-sizing: border-box;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-flex-wrap: wrap;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
}

<div
    class="emotion-0"
    spacing="1"
  />
</DocumentFragment>
`;

exports[`<GridRoot/> renders as a container state correctly BananaRepublicFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  box-sizing: border-box;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-flex-wrap: wrap;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
}

<div
    class="emotion-0"
    spacing="1"
  />
</DocumentFragment>
`;

exports[`<GridRoot/> renders as a container state correctly BananaRepublicFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  box-sizing: border-box;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-flex-wrap: wrap;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
}

<div
    class="emotion-0"
    spacing="1"
  />
</DocumentFragment>
`;

exports[`<GridRoot/> renders as a container state correctly Gap 1`] = `
<DocumentFragment>
  .emotion-0 {
  box-sizing: border-box;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-flex-wrap: wrap;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
}

<div
    class="emotion-0"
    spacing="1"
  />
</DocumentFragment>
`;

exports[`<GridRoot/> renders as a container state correctly Gap in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  box-sizing: border-box;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-flex-wrap: wrap;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
}

<div
    class="emotion-0"
    spacing="1"
  />
</DocumentFragment>
`;

exports[`<GridRoot/> renders as a container state correctly GapFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  box-sizing: border-box;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-flex-wrap: wrap;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
}

<div
    class="emotion-0"
    spacing="1"
  />
</DocumentFragment>
`;

exports[`<GridRoot/> renders as a container state correctly GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  box-sizing: border-box;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-flex-wrap: wrap;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
}

<div
    class="emotion-0"
    spacing="1"
  />
</DocumentFragment>
`;

exports[`<GridRoot/> renders as a container state correctly OldNavy 1`] = `
<DocumentFragment>
  .emotion-0 {
  box-sizing: border-box;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-flex-wrap: wrap;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
}

<div
    class="emotion-0"
    spacing="1"
  />
</DocumentFragment>
`;

exports[`<GridRoot/> renders as a container state correctly OldNavy in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  box-sizing: border-box;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-flex-wrap: wrap;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
}

<div
    class="emotion-0"
    spacing="1"
  />
</DocumentFragment>
`;

exports[`<GridRoot/> renders as an item with 1 column state correctly Athleta 1`] = `
<DocumentFragment>
  .emotion-0 {
  --columnSize: 1;
  box-sizing: border-box;
  -webkit-flex-basis: 25%;
  -ms-flex-preferred-size: 25%;
  flex-basis: 25%;
  -webkit-box-flex: 0;
  -webkit-flex-grow: 0;
  -ms-flex-positive: 0;
  flex-grow: 0;
  margin: 0;
  max-width: 25%;
  padding: 1em;
}

<div
    class="emotion-0"
    spacing="1"
  />
</DocumentFragment>
`;

exports[`<GridRoot/> renders as an item with 1 column state correctly Athleta in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  --columnSize: 1;
  box-sizing: border-box;
  -webkit-flex-basis: 25%;
  -ms-flex-preferred-size: 25%;
  flex-basis: 25%;
  -webkit-box-flex: 0;
  -webkit-flex-grow: 0;
  -ms-flex-positive: 0;
  flex-grow: 0;
  margin: 0;
  max-width: 25%;
  padding: 1em;
}

<div
    class="emotion-0"
    spacing="1"
  />
</DocumentFragment>
`;

exports[`<GridRoot/> renders as an item with 1 column state correctly BananaRepublic 1`] = `
<DocumentFragment>
  .emotion-0 {
  --columnSize: 1;
  box-sizing: border-box;
  -webkit-flex-basis: 25%;
  -ms-flex-preferred-size: 25%;
  flex-basis: 25%;
  -webkit-box-flex: 0;
  -webkit-flex-grow: 0;
  -ms-flex-positive: 0;
  flex-grow: 0;
  margin: 0;
  max-width: 25%;
  padding: 1em;
}

<div
    class="emotion-0"
    spacing="1"
  />
</DocumentFragment>
`;

exports[`<GridRoot/> renders as an item with 1 column state correctly BananaRepublic in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  --columnSize: 1;
  box-sizing: border-box;
  -webkit-flex-basis: 25%;
  -ms-flex-preferred-size: 25%;
  flex-basis: 25%;
  -webkit-box-flex: 0;
  -webkit-flex-grow: 0;
  -ms-flex-positive: 0;
  flex-grow: 0;
  margin: 0;
  max-width: 25%;
  padding: 1em;
}

<div
    class="emotion-0"
    spacing="1"
  />
</DocumentFragment>
`;

exports[`<GridRoot/> renders as an item with 1 column state correctly BananaRepublicFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  --columnSize: 1;
  box-sizing: border-box;
  -webkit-flex-basis: 25%;
  -ms-flex-preferred-size: 25%;
  flex-basis: 25%;
  -webkit-box-flex: 0;
  -webkit-flex-grow: 0;
  -ms-flex-positive: 0;
  flex-grow: 0;
  margin: 0;
  max-width: 25%;
  padding: 1em;
}

<div
    class="emotion-0"
    spacing="1"
  />
</DocumentFragment>
`;

exports[`<GridRoot/> renders as an item with 1 column state correctly BananaRepublicFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  --columnSize: 1;
  box-sizing: border-box;
  -webkit-flex-basis: 25%;
  -ms-flex-preferred-size: 25%;
  flex-basis: 25%;
  -webkit-box-flex: 0;
  -webkit-flex-grow: 0;
  -ms-flex-positive: 0;
  flex-grow: 0;
  margin: 0;
  max-width: 25%;
  padding: 1em;
}

<div
    class="emotion-0"
    spacing="1"
  />
</DocumentFragment>
`;

exports[`<GridRoot/> renders as an item with 1 column state correctly Gap 1`] = `
<DocumentFragment>
  .emotion-0 {
  --columnSize: 1;
  box-sizing: border-box;
  -webkit-flex-basis: 25%;
  -ms-flex-preferred-size: 25%;
  flex-basis: 25%;
  -webkit-box-flex: 0;
  -webkit-flex-grow: 0;
  -ms-flex-positive: 0;
  flex-grow: 0;
  margin: 0;
  max-width: 25%;
  padding: 1em;
}

<div
    class="emotion-0"
    spacing="1"
  />
</DocumentFragment>
`;

exports[`<GridRoot/> renders as an item with 1 column state correctly Gap in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  --columnSize: 1;
  box-sizing: border-box;
  -webkit-flex-basis: 25%;
  -ms-flex-preferred-size: 25%;
  flex-basis: 25%;
  -webkit-box-flex: 0;
  -webkit-flex-grow: 0;
  -ms-flex-positive: 0;
  flex-grow: 0;
  margin: 0;
  max-width: 25%;
  padding: 1em;
}

<div
    class="emotion-0"
    spacing="1"
  />
</DocumentFragment>
`;

exports[`<GridRoot/> renders as an item with 1 column state correctly GapFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  --columnSize: 1;
  box-sizing: border-box;
  -webkit-flex-basis: 25%;
  -ms-flex-preferred-size: 25%;
  flex-basis: 25%;
  -webkit-box-flex: 0;
  -webkit-flex-grow: 0;
  -ms-flex-positive: 0;
  flex-grow: 0;
  margin: 0;
  max-width: 25%;
  padding: 1em;
}

<div
    class="emotion-0"
    spacing="1"
  />
</DocumentFragment>
`;

exports[`<GridRoot/> renders as an item with 1 column state correctly GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  --columnSize: 1;
  box-sizing: border-box;
  -webkit-flex-basis: 25%;
  -ms-flex-preferred-size: 25%;
  flex-basis: 25%;
  -webkit-box-flex: 0;
  -webkit-flex-grow: 0;
  -ms-flex-positive: 0;
  flex-grow: 0;
  margin: 0;
  max-width: 25%;
  padding: 1em;
}

<div
    class="emotion-0"
    spacing="1"
  />
</DocumentFragment>
`;

exports[`<GridRoot/> renders as an item with 1 column state correctly OldNavy 1`] = `
<DocumentFragment>
  .emotion-0 {
  --columnSize: 1;
  box-sizing: border-box;
  -webkit-flex-basis: 25%;
  -ms-flex-preferred-size: 25%;
  flex-basis: 25%;
  -webkit-box-flex: 0;
  -webkit-flex-grow: 0;
  -ms-flex-positive: 0;
  flex-grow: 0;
  margin: 0;
  max-width: 25%;
  padding: 1em;
}

<div
    class="emotion-0"
    spacing="1"
  />
</DocumentFragment>
`;

exports[`<GridRoot/> renders as an item with 1 column state correctly OldNavy in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  --columnSize: 1;
  box-sizing: border-box;
  -webkit-flex-basis: 25%;
  -ms-flex-preferred-size: 25%;
  flex-basis: 25%;
  -webkit-box-flex: 0;
  -webkit-flex-grow: 0;
  -ms-flex-positive: 0;
  flex-grow: 0;
  margin: 0;
  max-width: 25%;
  padding: 1em;
}

<div
    class="emotion-0"
    spacing="1"
  />
</DocumentFragment>
`;

exports[`<GridRoot/> renders as an item with 2 columns state correctly Athleta 1`] = `
<DocumentFragment>
  .emotion-0 {
  --columnSize: 2;
  box-sizing: border-box;
  -webkit-flex-basis: 50%;
  -ms-flex-preferred-size: 50%;
  flex-basis: 50%;
  -webkit-box-flex: 0;
  -webkit-flex-grow: 0;
  -ms-flex-positive: 0;
  flex-grow: 0;
  margin: 0;
  max-width: 50%;
  padding: 1em;
}

<div
    class="emotion-0"
    spacing="1"
  />
</DocumentFragment>
`;

exports[`<GridRoot/> renders as an item with 2 columns state correctly Athleta in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  --columnSize: 2;
  box-sizing: border-box;
  -webkit-flex-basis: 50%;
  -ms-flex-preferred-size: 50%;
  flex-basis: 50%;
  -webkit-box-flex: 0;
  -webkit-flex-grow: 0;
  -ms-flex-positive: 0;
  flex-grow: 0;
  margin: 0;
  max-width: 50%;
  padding: 1em;
}

<div
    class="emotion-0"
    spacing="1"
  />
</DocumentFragment>
`;

exports[`<GridRoot/> renders as an item with 2 columns state correctly BananaRepublic 1`] = `
<DocumentFragment>
  .emotion-0 {
  --columnSize: 2;
  box-sizing: border-box;
  -webkit-flex-basis: 50%;
  -ms-flex-preferred-size: 50%;
  flex-basis: 50%;
  -webkit-box-flex: 0;
  -webkit-flex-grow: 0;
  -ms-flex-positive: 0;
  flex-grow: 0;
  margin: 0;
  max-width: 50%;
  padding: 1em;
}

<div
    class="emotion-0"
    spacing="1"
  />
</DocumentFragment>
`;

exports[`<GridRoot/> renders as an item with 2 columns state correctly BananaRepublic in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  --columnSize: 2;
  box-sizing: border-box;
  -webkit-flex-basis: 50%;
  -ms-flex-preferred-size: 50%;
  flex-basis: 50%;
  -webkit-box-flex: 0;
  -webkit-flex-grow: 0;
  -ms-flex-positive: 0;
  flex-grow: 0;
  margin: 0;
  max-width: 50%;
  padding: 1em;
}

<div
    class="emotion-0"
    spacing="1"
  />
</DocumentFragment>
`;

exports[`<GridRoot/> renders as an item with 2 columns state correctly BananaRepublicFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  --columnSize: 2;
  box-sizing: border-box;
  -webkit-flex-basis: 50%;
  -ms-flex-preferred-size: 50%;
  flex-basis: 50%;
  -webkit-box-flex: 0;
  -webkit-flex-grow: 0;
  -ms-flex-positive: 0;
  flex-grow: 0;
  margin: 0;
  max-width: 50%;
  padding: 1em;
}

<div
    class="emotion-0"
    spacing="1"
  />
</DocumentFragment>
`;

exports[`<GridRoot/> renders as an item with 2 columns state correctly BananaRepublicFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  --columnSize: 2;
  box-sizing: border-box;
  -webkit-flex-basis: 50%;
  -ms-flex-preferred-size: 50%;
  flex-basis: 50%;
  -webkit-box-flex: 0;
  -webkit-flex-grow: 0;
  -ms-flex-positive: 0;
  flex-grow: 0;
  margin: 0;
  max-width: 50%;
  padding: 1em;
}

<div
    class="emotion-0"
    spacing="1"
  />
</DocumentFragment>
`;

exports[`<GridRoot/> renders as an item with 2 columns state correctly Gap 1`] = `
<DocumentFragment>
  .emotion-0 {
  --columnSize: 2;
  box-sizing: border-box;
  -webkit-flex-basis: 50%;
  -ms-flex-preferred-size: 50%;
  flex-basis: 50%;
  -webkit-box-flex: 0;
  -webkit-flex-grow: 0;
  -ms-flex-positive: 0;
  flex-grow: 0;
  margin: 0;
  max-width: 50%;
  padding: 1em;
}

<div
    class="emotion-0"
    spacing="1"
  />
</DocumentFragment>
`;

exports[`<GridRoot/> renders as an item with 2 columns state correctly Gap in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  --columnSize: 2;
  box-sizing: border-box;
  -webkit-flex-basis: 50%;
  -ms-flex-preferred-size: 50%;
  flex-basis: 50%;
  -webkit-box-flex: 0;
  -webkit-flex-grow: 0;
  -ms-flex-positive: 0;
  flex-grow: 0;
  margin: 0;
  max-width: 50%;
  padding: 1em;
}

<div
    class="emotion-0"
    spacing="1"
  />
</DocumentFragment>
`;

exports[`<GridRoot/> renders as an item with 2 columns state correctly GapFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  --columnSize: 2;
  box-sizing: border-box;
  -webkit-flex-basis: 50%;
  -ms-flex-preferred-size: 50%;
  flex-basis: 50%;
  -webkit-box-flex: 0;
  -webkit-flex-grow: 0;
  -ms-flex-positive: 0;
  flex-grow: 0;
  margin: 0;
  max-width: 50%;
  padding: 1em;
}

<div
    class="emotion-0"
    spacing="1"
  />
</DocumentFragment>
`;

exports[`<GridRoot/> renders as an item with 2 columns state correctly GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  --columnSize: 2;
  box-sizing: border-box;
  -webkit-flex-basis: 50%;
  -ms-flex-preferred-size: 50%;
  flex-basis: 50%;
  -webkit-box-flex: 0;
  -webkit-flex-grow: 0;
  -ms-flex-positive: 0;
  flex-grow: 0;
  margin: 0;
  max-width: 50%;
  padding: 1em;
}

<div
    class="emotion-0"
    spacing="1"
  />
</DocumentFragment>
`;

exports[`<GridRoot/> renders as an item with 2 columns state correctly OldNavy 1`] = `
<DocumentFragment>
  .emotion-0 {
  --columnSize: 2;
  box-sizing: border-box;
  -webkit-flex-basis: 50%;
  -ms-flex-preferred-size: 50%;
  flex-basis: 50%;
  -webkit-box-flex: 0;
  -webkit-flex-grow: 0;
  -ms-flex-positive: 0;
  flex-grow: 0;
  margin: 0;
  max-width: 50%;
  padding: 1em;
}

<div
    class="emotion-0"
    spacing="1"
  />
</DocumentFragment>
`;

exports[`<GridRoot/> renders as an item with 2 columns state correctly OldNavy in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  --columnSize: 2;
  box-sizing: border-box;
  -webkit-flex-basis: 50%;
  -ms-flex-preferred-size: 50%;
  flex-basis: 50%;
  -webkit-box-flex: 0;
  -webkit-flex-grow: 0;
  -ms-flex-positive: 0;
  flex-grow: 0;
  margin: 0;
  max-width: 50%;
  padding: 1em;
}

<div
    class="emotion-0"
    spacing="1"
  />
</DocumentFragment>
`;

exports[`<GridRoot/> renders as an item with 3 columns state correctly Athleta 1`] = `
<DocumentFragment>
  .emotion-0 {
  --columnSize: 3;
  box-sizing: border-box;
  -webkit-flex-basis: 75%;
  -ms-flex-preferred-size: 75%;
  flex-basis: 75%;
  -webkit-box-flex: 0;
  -webkit-flex-grow: 0;
  -ms-flex-positive: 0;
  flex-grow: 0;
  margin: 0;
  max-width: 75%;
  padding: 1em;
}

<div
    class="emotion-0"
    spacing="1"
  />
</DocumentFragment>
`;

exports[`<GridRoot/> renders as an item with 3 columns state correctly Athleta in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  --columnSize: 3;
  box-sizing: border-box;
  -webkit-flex-basis: 75%;
  -ms-flex-preferred-size: 75%;
  flex-basis: 75%;
  -webkit-box-flex: 0;
  -webkit-flex-grow: 0;
  -ms-flex-positive: 0;
  flex-grow: 0;
  margin: 0;
  max-width: 75%;
  padding: 1em;
}

<div
    class="emotion-0"
    spacing="1"
  />
</DocumentFragment>
`;

exports[`<GridRoot/> renders as an item with 3 columns state correctly BananaRepublic 1`] = `
<DocumentFragment>
  .emotion-0 {
  --columnSize: 3;
  box-sizing: border-box;
  -webkit-flex-basis: 75%;
  -ms-flex-preferred-size: 75%;
  flex-basis: 75%;
  -webkit-box-flex: 0;
  -webkit-flex-grow: 0;
  -ms-flex-positive: 0;
  flex-grow: 0;
  margin: 0;
  max-width: 75%;
  padding: 1em;
}

<div
    class="emotion-0"
    spacing="1"
  />
</DocumentFragment>
`;

exports[`<GridRoot/> renders as an item with 3 columns state correctly BananaRepublic in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  --columnSize: 3;
  box-sizing: border-box;
  -webkit-flex-basis: 75%;
  -ms-flex-preferred-size: 75%;
  flex-basis: 75%;
  -webkit-box-flex: 0;
  -webkit-flex-grow: 0;
  -ms-flex-positive: 0;
  flex-grow: 0;
  margin: 0;
  max-width: 75%;
  padding: 1em;
}

<div
    class="emotion-0"
    spacing="1"
  />
</DocumentFragment>
`;

exports[`<GridRoot/> renders as an item with 3 columns state correctly BananaRepublicFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  --columnSize: 3;
  box-sizing: border-box;
  -webkit-flex-basis: 75%;
  -ms-flex-preferred-size: 75%;
  flex-basis: 75%;
  -webkit-box-flex: 0;
  -webkit-flex-grow: 0;
  -ms-flex-positive: 0;
  flex-grow: 0;
  margin: 0;
  max-width: 75%;
  padding: 1em;
}

<div
    class="emotion-0"
    spacing="1"
  />
</DocumentFragment>
`;

exports[`<GridRoot/> renders as an item with 3 columns state correctly BananaRepublicFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  --columnSize: 3;
  box-sizing: border-box;
  -webkit-flex-basis: 75%;
  -ms-flex-preferred-size: 75%;
  flex-basis: 75%;
  -webkit-box-flex: 0;
  -webkit-flex-grow: 0;
  -ms-flex-positive: 0;
  flex-grow: 0;
  margin: 0;
  max-width: 75%;
  padding: 1em;
}

<div
    class="emotion-0"
    spacing="1"
  />
</DocumentFragment>
`;

exports[`<GridRoot/> renders as an item with 3 columns state correctly Gap 1`] = `
<DocumentFragment>
  .emotion-0 {
  --columnSize: 3;
  box-sizing: border-box;
  -webkit-flex-basis: 75%;
  -ms-flex-preferred-size: 75%;
  flex-basis: 75%;
  -webkit-box-flex: 0;
  -webkit-flex-grow: 0;
  -ms-flex-positive: 0;
  flex-grow: 0;
  margin: 0;
  max-width: 75%;
  padding: 1em;
}

<div
    class="emotion-0"
    spacing="1"
  />
</DocumentFragment>
`;

exports[`<GridRoot/> renders as an item with 3 columns state correctly Gap in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  --columnSize: 3;
  box-sizing: border-box;
  -webkit-flex-basis: 75%;
  -ms-flex-preferred-size: 75%;
  flex-basis: 75%;
  -webkit-box-flex: 0;
  -webkit-flex-grow: 0;
  -ms-flex-positive: 0;
  flex-grow: 0;
  margin: 0;
  max-width: 75%;
  padding: 1em;
}

<div
    class="emotion-0"
    spacing="1"
  />
</DocumentFragment>
`;

exports[`<GridRoot/> renders as an item with 3 columns state correctly GapFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  --columnSize: 3;
  box-sizing: border-box;
  -webkit-flex-basis: 75%;
  -ms-flex-preferred-size: 75%;
  flex-basis: 75%;
  -webkit-box-flex: 0;
  -webkit-flex-grow: 0;
  -ms-flex-positive: 0;
  flex-grow: 0;
  margin: 0;
  max-width: 75%;
  padding: 1em;
}

<div
    class="emotion-0"
    spacing="1"
  />
</DocumentFragment>
`;

exports[`<GridRoot/> renders as an item with 3 columns state correctly GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  --columnSize: 3;
  box-sizing: border-box;
  -webkit-flex-basis: 75%;
  -ms-flex-preferred-size: 75%;
  flex-basis: 75%;
  -webkit-box-flex: 0;
  -webkit-flex-grow: 0;
  -ms-flex-positive: 0;
  flex-grow: 0;
  margin: 0;
  max-width: 75%;
  padding: 1em;
}

<div
    class="emotion-0"
    spacing="1"
  />
</DocumentFragment>
`;

exports[`<GridRoot/> renders as an item with 3 columns state correctly OldNavy 1`] = `
<DocumentFragment>
  .emotion-0 {
  --columnSize: 3;
  box-sizing: border-box;
  -webkit-flex-basis: 75%;
  -ms-flex-preferred-size: 75%;
  flex-basis: 75%;
  -webkit-box-flex: 0;
  -webkit-flex-grow: 0;
  -ms-flex-positive: 0;
  flex-grow: 0;
  margin: 0;
  max-width: 75%;
  padding: 1em;
}

<div
    class="emotion-0"
    spacing="1"
  />
</DocumentFragment>
`;

exports[`<GridRoot/> renders as an item with 3 columns state correctly OldNavy in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  --columnSize: 3;
  box-sizing: border-box;
  -webkit-flex-basis: 75%;
  -ms-flex-preferred-size: 75%;
  flex-basis: 75%;
  -webkit-box-flex: 0;
  -webkit-flex-grow: 0;
  -ms-flex-positive: 0;
  flex-grow: 0;
  margin: 0;
  max-width: 75%;
  padding: 1em;
}

<div
    class="emotion-0"
    spacing="1"
  />
</DocumentFragment>
`;

exports[`<GridRoot/> renders as an item with 4 columns state correctly Athleta 1`] = `
<DocumentFragment>
  .emotion-0 {
  --columnSize: 4;
  box-sizing: border-box;
  -webkit-flex-basis: 100%;
  -ms-flex-preferred-size: 100%;
  flex-basis: 100%;
  -webkit-box-flex: 0;
  -webkit-flex-grow: 0;
  -ms-flex-positive: 0;
  flex-grow: 0;
  margin: 0;
  max-width: 100%;
  padding: 1em;
}

<div
    class="emotion-0"
    spacing="1"
  />
</DocumentFragment>
`;

exports[`<GridRoot/> renders as an item with 4 columns state correctly Athleta in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  --columnSize: 4;
  box-sizing: border-box;
  -webkit-flex-basis: 100%;
  -ms-flex-preferred-size: 100%;
  flex-basis: 100%;
  -webkit-box-flex: 0;
  -webkit-flex-grow: 0;
  -ms-flex-positive: 0;
  flex-grow: 0;
  margin: 0;
  max-width: 100%;
  padding: 1em;
}

<div
    class="emotion-0"
    spacing="1"
  />
</DocumentFragment>
`;

exports[`<GridRoot/> renders as an item with 4 columns state correctly BananaRepublic 1`] = `
<DocumentFragment>
  .emotion-0 {
  --columnSize: 4;
  box-sizing: border-box;
  -webkit-flex-basis: 100%;
  -ms-flex-preferred-size: 100%;
  flex-basis: 100%;
  -webkit-box-flex: 0;
  -webkit-flex-grow: 0;
  -ms-flex-positive: 0;
  flex-grow: 0;
  margin: 0;
  max-width: 100%;
  padding: 1em;
}

<div
    class="emotion-0"
    spacing="1"
  />
</DocumentFragment>
`;

exports[`<GridRoot/> renders as an item with 4 columns state correctly BananaRepublic in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  --columnSize: 4;
  box-sizing: border-box;
  -webkit-flex-basis: 100%;
  -ms-flex-preferred-size: 100%;
  flex-basis: 100%;
  -webkit-box-flex: 0;
  -webkit-flex-grow: 0;
  -ms-flex-positive: 0;
  flex-grow: 0;
  margin: 0;
  max-width: 100%;
  padding: 1em;
}

<div
    class="emotion-0"
    spacing="1"
  />
</DocumentFragment>
`;

exports[`<GridRoot/> renders as an item with 4 columns state correctly BananaRepublicFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  --columnSize: 4;
  box-sizing: border-box;
  -webkit-flex-basis: 100%;
  -ms-flex-preferred-size: 100%;
  flex-basis: 100%;
  -webkit-box-flex: 0;
  -webkit-flex-grow: 0;
  -ms-flex-positive: 0;
  flex-grow: 0;
  margin: 0;
  max-width: 100%;
  padding: 1em;
}

<div
    class="emotion-0"
    spacing="1"
  />
</DocumentFragment>
`;

exports[`<GridRoot/> renders as an item with 4 columns state correctly BananaRepublicFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  --columnSize: 4;
  box-sizing: border-box;
  -webkit-flex-basis: 100%;
  -ms-flex-preferred-size: 100%;
  flex-basis: 100%;
  -webkit-box-flex: 0;
  -webkit-flex-grow: 0;
  -ms-flex-positive: 0;
  flex-grow: 0;
  margin: 0;
  max-width: 100%;
  padding: 1em;
}

<div
    class="emotion-0"
    spacing="1"
  />
</DocumentFragment>
`;

exports[`<GridRoot/> renders as an item with 4 columns state correctly Gap 1`] = `
<DocumentFragment>
  .emotion-0 {
  --columnSize: 4;
  box-sizing: border-box;
  -webkit-flex-basis: 100%;
  -ms-flex-preferred-size: 100%;
  flex-basis: 100%;
  -webkit-box-flex: 0;
  -webkit-flex-grow: 0;
  -ms-flex-positive: 0;
  flex-grow: 0;
  margin: 0;
  max-width: 100%;
  padding: 1em;
}

<div
    class="emotion-0"
    spacing="1"
  />
</DocumentFragment>
`;

exports[`<GridRoot/> renders as an item with 4 columns state correctly Gap in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  --columnSize: 4;
  box-sizing: border-box;
  -webkit-flex-basis: 100%;
  -ms-flex-preferred-size: 100%;
  flex-basis: 100%;
  -webkit-box-flex: 0;
  -webkit-flex-grow: 0;
  -ms-flex-positive: 0;
  flex-grow: 0;
  margin: 0;
  max-width: 100%;
  padding: 1em;
}

<div
    class="emotion-0"
    spacing="1"
  />
</DocumentFragment>
`;

exports[`<GridRoot/> renders as an item with 4 columns state correctly GapFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  --columnSize: 4;
  box-sizing: border-box;
  -webkit-flex-basis: 100%;
  -ms-flex-preferred-size: 100%;
  flex-basis: 100%;
  -webkit-box-flex: 0;
  -webkit-flex-grow: 0;
  -ms-flex-positive: 0;
  flex-grow: 0;
  margin: 0;
  max-width: 100%;
  padding: 1em;
}

<div
    class="emotion-0"
    spacing="1"
  />
</DocumentFragment>
`;

exports[`<GridRoot/> renders as an item with 4 columns state correctly GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  --columnSize: 4;
  box-sizing: border-box;
  -webkit-flex-basis: 100%;
  -ms-flex-preferred-size: 100%;
  flex-basis: 100%;
  -webkit-box-flex: 0;
  -webkit-flex-grow: 0;
  -ms-flex-positive: 0;
  flex-grow: 0;
  margin: 0;
  max-width: 100%;
  padding: 1em;
}

<div
    class="emotion-0"
    spacing="1"
  />
</DocumentFragment>
`;

exports[`<GridRoot/> renders as an item with 4 columns state correctly OldNavy 1`] = `
<DocumentFragment>
  .emotion-0 {
  --columnSize: 4;
  box-sizing: border-box;
  -webkit-flex-basis: 100%;
  -ms-flex-preferred-size: 100%;
  flex-basis: 100%;
  -webkit-box-flex: 0;
  -webkit-flex-grow: 0;
  -ms-flex-positive: 0;
  flex-grow: 0;
  margin: 0;
  max-width: 100%;
  padding: 1em;
}

<div
    class="emotion-0"
    spacing="1"
  />
</DocumentFragment>
`;

exports[`<GridRoot/> renders as an item with 4 columns state correctly OldNavy in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  --columnSize: 4;
  box-sizing: border-box;
  -webkit-flex-basis: 100%;
  -ms-flex-preferred-size: 100%;
  flex-basis: 100%;
  -webkit-box-flex: 0;
  -webkit-flex-grow: 0;
  -ms-flex-positive: 0;
  flex-grow: 0;
  margin: 0;
  max-width: 100%;
  padding: 1em;
}

<div
    class="emotion-0"
    spacing="1"
  />
</DocumentFragment>
`;

exports[`<GridRoot/> renders as an item without column size state correctly Athleta 1`] = `
<DocumentFragment>
  .emotion-0 {
  box-sizing: border-box;
  -webkit-flex-basis: 100%;
  -ms-flex-preferred-size: 100%;
  flex-basis: 100%;
  -webkit-box-flex: 0;
  -webkit-flex-grow: 0;
  -ms-flex-positive: 0;
  flex-grow: 0;
  margin: 0;
  max-width: 100%;
  padding: 1em;
}

<div
    class="emotion-0"
    spacing="1"
  />
</DocumentFragment>
`;

exports[`<GridRoot/> renders as an item without column size state correctly Athleta in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  box-sizing: border-box;
  -webkit-flex-basis: 100%;
  -ms-flex-preferred-size: 100%;
  flex-basis: 100%;
  -webkit-box-flex: 0;
  -webkit-flex-grow: 0;
  -ms-flex-positive: 0;
  flex-grow: 0;
  margin: 0;
  max-width: 100%;
  padding: 1em;
}

<div
    class="emotion-0"
    spacing="1"
  />
</DocumentFragment>
`;

exports[`<GridRoot/> renders as an item without column size state correctly BananaRepublic 1`] = `
<DocumentFragment>
  .emotion-0 {
  box-sizing: border-box;
  -webkit-flex-basis: 100%;
  -ms-flex-preferred-size: 100%;
  flex-basis: 100%;
  -webkit-box-flex: 0;
  -webkit-flex-grow: 0;
  -ms-flex-positive: 0;
  flex-grow: 0;
  margin: 0;
  max-width: 100%;
  padding: 1em;
}

<div
    class="emotion-0"
    spacing="1"
  />
</DocumentFragment>
`;

exports[`<GridRoot/> renders as an item without column size state correctly BananaRepublic in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  box-sizing: border-box;
  -webkit-flex-basis: 100%;
  -ms-flex-preferred-size: 100%;
  flex-basis: 100%;
  -webkit-box-flex: 0;
  -webkit-flex-grow: 0;
  -ms-flex-positive: 0;
  flex-grow: 0;
  margin: 0;
  max-width: 100%;
  padding: 1em;
}

<div
    class="emotion-0"
    spacing="1"
  />
</DocumentFragment>
`;

exports[`<GridRoot/> renders as an item without column size state correctly BananaRepublicFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  box-sizing: border-box;
  -webkit-flex-basis: 100%;
  -ms-flex-preferred-size: 100%;
  flex-basis: 100%;
  -webkit-box-flex: 0;
  -webkit-flex-grow: 0;
  -ms-flex-positive: 0;
  flex-grow: 0;
  margin: 0;
  max-width: 100%;
  padding: 1em;
}

<div
    class="emotion-0"
    spacing="1"
  />
</DocumentFragment>
`;

exports[`<GridRoot/> renders as an item without column size state correctly BananaRepublicFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  box-sizing: border-box;
  -webkit-flex-basis: 100%;
  -ms-flex-preferred-size: 100%;
  flex-basis: 100%;
  -webkit-box-flex: 0;
  -webkit-flex-grow: 0;
  -ms-flex-positive: 0;
  flex-grow: 0;
  margin: 0;
  max-width: 100%;
  padding: 1em;
}

<div
    class="emotion-0"
    spacing="1"
  />
</DocumentFragment>
`;

exports[`<GridRoot/> renders as an item without column size state correctly Gap 1`] = `
<DocumentFragment>
  .emotion-0 {
  box-sizing: border-box;
  -webkit-flex-basis: 100%;
  -ms-flex-preferred-size: 100%;
  flex-basis: 100%;
  -webkit-box-flex: 0;
  -webkit-flex-grow: 0;
  -ms-flex-positive: 0;
  flex-grow: 0;
  margin: 0;
  max-width: 100%;
  padding: 1em;
}

<div
    class="emotion-0"
    spacing="1"
  />
</DocumentFragment>
`;

exports[`<GridRoot/> renders as an item without column size state correctly Gap in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  box-sizing: border-box;
  -webkit-flex-basis: 100%;
  -ms-flex-preferred-size: 100%;
  flex-basis: 100%;
  -webkit-box-flex: 0;
  -webkit-flex-grow: 0;
  -ms-flex-positive: 0;
  flex-grow: 0;
  margin: 0;
  max-width: 100%;
  padding: 1em;
}

<div
    class="emotion-0"
    spacing="1"
  />
</DocumentFragment>
`;

exports[`<GridRoot/> renders as an item without column size state correctly GapFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  box-sizing: border-box;
  -webkit-flex-basis: 100%;
  -ms-flex-preferred-size: 100%;
  flex-basis: 100%;
  -webkit-box-flex: 0;
  -webkit-flex-grow: 0;
  -ms-flex-positive: 0;
  flex-grow: 0;
  margin: 0;
  max-width: 100%;
  padding: 1em;
}

<div
    class="emotion-0"
    spacing="1"
  />
</DocumentFragment>
`;

exports[`<GridRoot/> renders as an item without column size state correctly GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  box-sizing: border-box;
  -webkit-flex-basis: 100%;
  -ms-flex-preferred-size: 100%;
  flex-basis: 100%;
  -webkit-box-flex: 0;
  -webkit-flex-grow: 0;
  -ms-flex-positive: 0;
  flex-grow: 0;
  margin: 0;
  max-width: 100%;
  padding: 1em;
}

<div
    class="emotion-0"
    spacing="1"
  />
</DocumentFragment>
`;

exports[`<GridRoot/> renders as an item without column size state correctly OldNavy 1`] = `
<DocumentFragment>
  .emotion-0 {
  box-sizing: border-box;
  -webkit-flex-basis: 100%;
  -ms-flex-preferred-size: 100%;
  flex-basis: 100%;
  -webkit-box-flex: 0;
  -webkit-flex-grow: 0;
  -ms-flex-positive: 0;
  flex-grow: 0;
  margin: 0;
  max-width: 100%;
  padding: 1em;
}

<div
    class="emotion-0"
    spacing="1"
  />
</DocumentFragment>
`;

exports[`<GridRoot/> renders as an item without column size state correctly OldNavy in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  box-sizing: border-box;
  -webkit-flex-basis: 100%;
  -ms-flex-preferred-size: 100%;
  flex-basis: 100%;
  -webkit-box-flex: 0;
  -webkit-flex-grow: 0;
  -ms-flex-positive: 0;
  flex-grow: 0;
  margin: 0;
  max-width: 100%;
  padding: 1em;
}

<div
    class="emotion-0"
    spacing="1"
  />
</DocumentFragment>
`;
