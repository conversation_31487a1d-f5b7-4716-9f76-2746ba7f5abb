// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`<Radio /> label can render with children 1`] = `
{
  "asFragment": [Function],
  "baseElement": .emotion-0 {
  margin: 0 0 1.5rem;
}

.emotion-0:hover {
  -webkit-text-decoration: underline;
  text-decoration: underline;
}

.emotion-1 {
  opacity: 0;
  position: absolute;
}

.emotion-1[data-focus-visible-added]~.fui_radio-button {
  outline: 0;
  box-shadow: 0px 0px 0 3px #5CABF7;
}

.emotion-2 {
  display: block;
  position: relative;
  float: left;
  margin-right: 0.625rem;
  width: 1.5rem;
  height: 1.5rem;
  border: 1px solid #666;
  border-radius: 100%;
}

.emotion-2::after {
  content: "";
  position: absolute;
  width: 0;
  height: 0;
}

.emotion-2::before {
  content: "";
  position: absolute;
  display: block;
  width: calc(100% + 1px);
  height: calc(100% + 1px);
  border-radius: 100%;
  left: -1px;
  top: -1px;
}

.emotion-3 {
  display: inline-block;
  vertical-align: middle;
  line-height: 1.5;
  color: #666;
  max-width: calc(100% - 2.3025rem);
  font-size: 1.063rem;
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
}

.emotion-4 {
  font-weight: 700;
}

<body>
    <div>
      <label
        class=" emotion-0"
      >
        <input
          class="emotion-1"
          id="radio-button"
          type="radio"
          value="selected"
        />
        <div
          class="fui_radio-button emotion-2"
        />
        <span
          class="emotion-3"
        >
          <div
            class="custom-radio custom-radio__label-children"
          >
            A 
            <span
              class="emotion-4"
            >
              Child
            </span>
             element in a Radio button.
          </div>
        </span>
      </label>
    </div>
  </body>,
  "container": .emotion-0 {
  margin: 0 0 1.5rem;
}

.emotion-0:hover {
  -webkit-text-decoration: underline;
  text-decoration: underline;
}

.emotion-1 {
  opacity: 0;
  position: absolute;
}

.emotion-1[data-focus-visible-added]~.fui_radio-button {
  outline: 0;
  box-shadow: 0px 0px 0 3px #5CABF7;
}

.emotion-2 {
  display: block;
  position: relative;
  float: left;
  margin-right: 0.625rem;
  width: 1.5rem;
  height: 1.5rem;
  border: 1px solid #666;
  border-radius: 100%;
}

.emotion-2::after {
  content: "";
  position: absolute;
  width: 0;
  height: 0;
}

.emotion-2::before {
  content: "";
  position: absolute;
  display: block;
  width: calc(100% + 1px);
  height: calc(100% + 1px);
  border-radius: 100%;
  left: -1px;
  top: -1px;
}

.emotion-3 {
  display: inline-block;
  vertical-align: middle;
  line-height: 1.5;
  color: #666;
  max-width: calc(100% - 2.3025rem);
  font-size: 1.063rem;
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
}

.emotion-4 {
  font-weight: 700;
}

<div>
    <label
      class=" emotion-0"
    >
      <input
        class="emotion-1"
        id="radio-button"
        type="radio"
        value="selected"
      />
      <div
        class="fui_radio-button emotion-2"
      />
      <span
        class="emotion-3"
      >
        <div
          class="custom-radio custom-radio__label-children"
        >
          A 
          <span
            class="emotion-4"
          >
            Child
          </span>
           element in a Radio button.
        </div>
      </span>
    </label>
  </div>,
  "debug": [Function],
  "findAllByAltText": [Function],
  "findAllByDisplayValue": [Function],
  "findAllByLabelText": [Function],
  "findAllByPlaceholderText": [Function],
  "findAllByRole": [Function],
  "findAllByTestId": [Function],
  "findAllByText": [Function],
  "findAllByTitle": [Function],
  "findByAltText": [Function],
  "findByDisplayValue": [Function],
  "findByLabelText": [Function],
  "findByPlaceholderText": [Function],
  "findByRole": [Function],
  "findByTestId": [Function],
  "findByText": [Function],
  "findByTitle": [Function],
  "getAllByAltText": [Function],
  "getAllByDisplayValue": [Function],
  "getAllByLabelText": [Function],
  "getAllByPlaceholderText": [Function],
  "getAllByRole": [Function],
  "getAllByTestId": [Function],
  "getAllByText": [Function],
  "getAllByTitle": [Function],
  "getByAltText": [Function],
  "getByDisplayValue": [Function],
  "getByLabelText": [Function],
  "getByPlaceholderText": [Function],
  "getByRole": [Function],
  "getByTestId": [Function],
  "getByText": [Function],
  "getByTitle": [Function],
  "queryAllByAltText": [Function],
  "queryAllByDisplayValue": [Function],
  "queryAllByLabelText": [Function],
  "queryAllByPlaceholderText": [Function],
  "queryAllByRole": [Function],
  "queryAllByTestId": [Function],
  "queryAllByText": [Function],
  "queryAllByTitle": [Function],
  "queryByAltText": [Function],
  "queryByDisplayValue": [Function],
  "queryByLabelText": [Function],
  "queryByPlaceholderText": [Function],
  "queryByRole": [Function],
  "queryByTestId": [Function],
  "queryByText": [Function],
  "queryByTitle": [Function],
  "rerender": [Function],
  "unmount": [Function],
}
`;

exports[`<Radio /> snapshots renders checked state correctly Athleta 1`] = `
<DocumentFragment>
  @keyframes animation-0 {
  0% {
    -webkit-transform: scale(0);
    -moz-transform: scale(0);
    -ms-transform: scale(0);
    transform: scale(0);
  }

  90% {
    -webkit-transform: scale(1.1);
    -moz-transform: scale(1.1);
    -ms-transform: scale(1.1);
    transform: scale(1.1);
  }

  100% {
    -webkit-transform: scale(1);
    -moz-transform: scale(1);
    -ms-transform: scale(1);
    transform: scale(1);
  }
}

.emotion-0 {
  margin: 0 0 1.5rem;
}

.emotion-0:hover {
  -webkit-text-decoration: underline;
  text-decoration: underline;
}

.emotion-1 {
  opacity: 0;
  position: absolute;
}

.emotion-1[data-focus-visible-added]~.fui_radio-button {
  outline: 0;
  box-shadow: 0.5px 0.5px 0 3px #5CABF7;
}

.emotion-2 {
  display: block;
  position: relative;
  float: left;
  margin-right: 0.625rem;
  width: 1.5rem;
  height: 1.5rem;
  border: 1px solid #666;
  border-radius: 100%;
}

.emotion-2::after {
  content: "";
  position: absolute;
  width: 0;
  height: 0;
  width: 100%;
  height: 100%;
  background-color: transparent;
  background-position-x: 4px;
  background-position-y: 7px;
  -webkit-background-size: 27px;
  background-size: 27px;
  background-repeat: no-repeat;
  -webkit-background-clip: border-box;
  background-clip: border-box;
  border-top: none;
  border-right: none;
}

.emotion-2::before {
  content: "";
  position: absolute;
  display: block;
  width: calc(100% + 1px);
  height: calc(100% + 1px);
  border-radius: 100%;
  left: -1px;
  top: -1px;
  -webkit-animation: animation-0 250ms cubic-bezier(0.175, 0.885, 0.32, 1.275);
  animation: animation-0 250ms cubic-bezier(0.175, 0.885, 0.32, 1.275);
  border: 1px solid #333;
  background-color: #333;
}

.emotion-3 {
  display: inline-block;
  vertical-align: middle;
  line-height: 1.5;
  color: #666;
  max-width: calc(100% - 2.3025rem);
  font-size: 1.063rem;
  font-family: 'Avenir Next','Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
}

<label
    class=" emotion-0"
  >
    <input
      checked=""
      class="emotion-1"
      id="radio-button"
      type="radio"
      value="selected"
    />
    <div
      class="fui_radio-button emotion-2"
    />
    <span
      class="emotion-3"
    >
      radio button
    </span>
  </label>
</DocumentFragment>
`;

exports[`<Radio /> snapshots renders checked state correctly Athleta in crossBrand 1`] = `
<DocumentFragment>
  @keyframes animation-0 {
  0% {
    -webkit-transform: scale(0);
    -moz-transform: scale(0);
    -ms-transform: scale(0);
    transform: scale(0);
  }

  90% {
    -webkit-transform: scale(1.1);
    -moz-transform: scale(1.1);
    -ms-transform: scale(1.1);
    transform: scale(1.1);
  }

  100% {
    -webkit-transform: scale(1);
    -moz-transform: scale(1);
    -ms-transform: scale(1);
    transform: scale(1);
  }
}

@keyframes animation-0 {
  0% {
    -webkit-transform: scale(0);
    -moz-transform: scale(0);
    -ms-transform: scale(0);
    transform: scale(0);
  }

  90% {
    -webkit-transform: scale(1.1);
    -moz-transform: scale(1.1);
    -ms-transform: scale(1.1);
    transform: scale(1.1);
  }

  100% {
    -webkit-transform: scale(1);
    -moz-transform: scale(1);
    -ms-transform: scale(1);
    transform: scale(1);
  }
}

.emotion-0 {
  margin: 0 0 1.5rem;
}

.emotion-0:hover {
  -webkit-text-decoration: underline;
  text-decoration: underline;
}

.emotion-1 {
  opacity: 0;
  position: absolute;
}

.emotion-1[data-focus-visible-added]~.fui_radio-button {
  outline: 0;
  box-shadow: 0.5px 0.5px 0 3px #5CABF7;
}

.emotion-2 {
  display: block;
  position: relative;
  float: left;
  margin-right: 0.625rem;
  width: 1.5rem;
  height: 1.5rem;
  border: 1px solid #666;
  border-radius: 100%;
}

.emotion-2::after {
  content: "";
  position: absolute;
  width: 0;
  height: 0;
  width: 100%;
  height: 100%;
  background-color: transparent;
  background-position-x: 4px;
  background-position-y: 7px;
  -webkit-background-size: 27px;
  background-size: 27px;
  background-repeat: no-repeat;
  -webkit-background-clip: border-box;
  background-clip: border-box;
  border-top: none;
  border-right: none;
  background-image: url("data:image/svg+xml;charset=utf8,%3Csvg%20width%3D%2220%22%20height%3D%2220%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%3E%0A%0A%3Cpath%20d%3D%22M11.884 0L4.091 7.793.858 4.61 0 5.467l4.088 4.026.01-.01.009.01L12.74.857z%22%20fill%3D%22%23FFF%22%20fill-rule%3D%22nonzero%22%20stroke%3D%22%23FFF%22%20stroke-width%3D%220.35%22%20style%3D%22%22%20%2F%3E%3C%2Fsvg%3E%0A%0A%0A");
}

.emotion-2::before {
  content: "";
  position: absolute;
  display: block;
  width: calc(100% + 1px);
  height: calc(100% + 1px);
  border-radius: 100%;
  left: -1px;
  top: -1px;
  -webkit-animation: animation-0 250ms cubic-bezier(0.175, 0.885, 0.32, 1.275);
  animation: animation-0 250ms cubic-bezier(0.175, 0.885, 0.32, 1.275);
  border: 1px solid #0466CA;
  background-color: #0466CA;
}

.emotion-3 {
  display: inline-block;
  vertical-align: middle;
  line-height: 1.5;
  color: #666;
  max-width: calc(100% - 2.3025rem);
  font-size: 1.1rem;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
}

<label
    class=" emotion-0"
  >
    <input
      checked=""
      class="emotion-1"
      id="radio-button"
      type="radio"
      value="selected"
    />
    <div
      class="fui_radio-button emotion-2"
    />
    <span
      class="emotion-3"
    >
      radio button
    </span>
  </label>
</DocumentFragment>
`;

exports[`<Radio /> snapshots renders checked state correctly BananaRepublic 1`] = `
<DocumentFragment>
  @keyframes animation-0 {
  0% {
    -webkit-transform: scale(0);
    -moz-transform: scale(0);
    -ms-transform: scale(0);
    transform: scale(0);
  }

  90% {
    -webkit-transform: scale(1.1);
    -moz-transform: scale(1.1);
    -ms-transform: scale(1.1);
    transform: scale(1.1);
  }

  100% {
    -webkit-transform: scale(1);
    -moz-transform: scale(1);
    -ms-transform: scale(1);
    transform: scale(1);
  }
}

@keyframes animation-0 {
  0% {
    -webkit-transform: scale(0);
    -moz-transform: scale(0);
    -ms-transform: scale(0);
    transform: scale(0);
  }

  90% {
    -webkit-transform: scale(1.1);
    -moz-transform: scale(1.1);
    -ms-transform: scale(1.1);
    transform: scale(1.1);
  }

  100% {
    -webkit-transform: scale(1);
    -moz-transform: scale(1);
    -ms-transform: scale(1);
    transform: scale(1);
  }
}

@keyframes animation-0 {
  0% {
    -webkit-transform: scale(0);
    -moz-transform: scale(0);
    -ms-transform: scale(0);
    transform: scale(0);
  }

  90% {
    -webkit-transform: scale(1.1);
    -moz-transform: scale(1.1);
    -ms-transform: scale(1.1);
    transform: scale(1.1);
  }

  100% {
    -webkit-transform: scale(1);
    -moz-transform: scale(1);
    -ms-transform: scale(1);
    transform: scale(1);
  }
}

.emotion-0 {
  margin: 0 0 1.5rem;
}

.emotion-0:hover {
  -webkit-text-decoration: underline;
  text-decoration: underline;
}

.emotion-1 {
  opacity: 0;
  position: absolute;
}

.emotion-1[data-focus-visible-added]~.fui_radio-button {
  outline: 0;
  box-shadow: 0.5px 0.5px 0 3px #CCC;
}

.emotion-2 {
  display: block;
  position: relative;
  float: left;
  margin-right: 0.625rem;
  width: 1.5rem;
  height: 1.5rem;
  border: 1px solid #666;
  border-radius: 100%;
}

.emotion-2::after {
  content: "";
  position: absolute;
  width: 0;
  height: 0;
  width: 100%;
  height: 100%;
  background-color: transparent;
  background-position-x: 4px;
  background-position-y: 7px;
  -webkit-background-size: 27px;
  background-size: 27px;
  background-repeat: no-repeat;
  -webkit-background-clip: border-box;
  background-clip: border-box;
  border-top: none;
  border-right: none;
}

.emotion-2::before {
  content: "";
  position: absolute;
  display: block;
  width: calc(100% + 1px);
  height: calc(100% + 1px);
  border-radius: 100%;
  left: -1px;
  top: -1px;
  -webkit-animation: animation-0 250ms cubic-bezier(0.175, 0.885, 0.32, 1.275);
  animation: animation-0 250ms cubic-bezier(0.175, 0.885, 0.32, 1.275);
  border: 1px solid #000;
  background-color: #000;
}

.emotion-3 {
  display: inline-block;
  vertical-align: middle;
  line-height: 1.5;
  color: #666;
  max-width: calc(100% - 2.3025rem);
  font-size: 1rem;
  font-family: 'Lynstone',Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
}

<label
    class=" emotion-0"
  >
    <input
      checked=""
      class="emotion-1"
      id="radio-button"
      type="radio"
      value="selected"
    />
    <div
      class="fui_radio-button emotion-2"
    />
    <span
      class="emotion-3"
    >
      radio button
    </span>
  </label>
</DocumentFragment>
`;

exports[`<Radio /> snapshots renders checked state correctly BananaRepublic in crossBrand 1`] = `
<DocumentFragment>
  @keyframes animation-0 {
  0% {
    -webkit-transform: scale(0);
    -moz-transform: scale(0);
    -ms-transform: scale(0);
    transform: scale(0);
  }

  90% {
    -webkit-transform: scale(1.1);
    -moz-transform: scale(1.1);
    -ms-transform: scale(1.1);
    transform: scale(1.1);
  }

  100% {
    -webkit-transform: scale(1);
    -moz-transform: scale(1);
    -ms-transform: scale(1);
    transform: scale(1);
  }
}

@keyframes animation-0 {
  0% {
    -webkit-transform: scale(0);
    -moz-transform: scale(0);
    -ms-transform: scale(0);
    transform: scale(0);
  }

  90% {
    -webkit-transform: scale(1.1);
    -moz-transform: scale(1.1);
    -ms-transform: scale(1.1);
    transform: scale(1.1);
  }

  100% {
    -webkit-transform: scale(1);
    -moz-transform: scale(1);
    -ms-transform: scale(1);
    transform: scale(1);
  }
}

@keyframes animation-0 {
  0% {
    -webkit-transform: scale(0);
    -moz-transform: scale(0);
    -ms-transform: scale(0);
    transform: scale(0);
  }

  90% {
    -webkit-transform: scale(1.1);
    -moz-transform: scale(1.1);
    -ms-transform: scale(1.1);
    transform: scale(1.1);
  }

  100% {
    -webkit-transform: scale(1);
    -moz-transform: scale(1);
    -ms-transform: scale(1);
    transform: scale(1);
  }
}

.emotion-0 {
  margin: 0 0 1.5rem;
}

.emotion-0:hover {
  -webkit-text-decoration: underline;
  text-decoration: underline;
}

.emotion-1 {
  opacity: 0;
  position: absolute;
}

.emotion-1[data-focus-visible-added]~.fui_radio-button {
  outline: 0;
  box-shadow: 0.5px 0.5px 0 3px #5CABF7;
}

.emotion-2 {
  display: block;
  position: relative;
  float: left;
  margin-right: 0.625rem;
  width: 1.5rem;
  height: 1.5rem;
  border: 1px solid #666;
  border-radius: 100%;
}

.emotion-2::after {
  content: "";
  position: absolute;
  width: 0;
  height: 0;
  width: 100%;
  height: 100%;
  background-color: transparent;
  background-position-x: 4px;
  background-position-y: 7px;
  -webkit-background-size: 27px;
  background-size: 27px;
  background-repeat: no-repeat;
  -webkit-background-clip: border-box;
  background-clip: border-box;
  border-top: none;
  border-right: none;
  background-image: url("data:image/svg+xml;charset=utf8,%3Csvg%20width%3D%2220%22%20height%3D%2220%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%3E%0A%0A%3Cpath%20d%3D%22M11.884 0L4.091 7.793.858 4.61 0 5.467l4.088 4.026.01-.01.009.01L12.74.857z%22%20fill%3D%22%23FFF%22%20fill-rule%3D%22nonzero%22%20stroke%3D%22%23FFF%22%20stroke-width%3D%220.35%22%20style%3D%22%22%20%2F%3E%3C%2Fsvg%3E%0A%0A%0A");
}

.emotion-2::before {
  content: "";
  position: absolute;
  display: block;
  width: calc(100% + 1px);
  height: calc(100% + 1px);
  border-radius: 100%;
  left: -1px;
  top: -1px;
  -webkit-animation: animation-0 250ms cubic-bezier(0.175, 0.885, 0.32, 1.275);
  animation: animation-0 250ms cubic-bezier(0.175, 0.885, 0.32, 1.275);
  border: 1px solid #0466CA;
  background-color: #0466CA;
}

.emotion-3 {
  display: inline-block;
  vertical-align: middle;
  line-height: 1.5;
  color: #666;
  max-width: calc(100% - 2.3025rem);
  font-size: 1.1rem;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
}

<label
    class=" emotion-0"
  >
    <input
      checked=""
      class="emotion-1"
      id="radio-button"
      type="radio"
      value="selected"
    />
    <div
      class="fui_radio-button emotion-2"
    />
    <span
      class="emotion-3"
    >
      radio button
    </span>
  </label>
</DocumentFragment>
`;

exports[`<Radio /> snapshots renders checked state correctly BananaRepublicFactoryStore 1`] = `
<DocumentFragment>
  @keyframes animation-0 {
  0% {
    -webkit-transform: scale(0);
    -moz-transform: scale(0);
    -ms-transform: scale(0);
    transform: scale(0);
  }

  90% {
    -webkit-transform: scale(1.1);
    -moz-transform: scale(1.1);
    -ms-transform: scale(1.1);
    transform: scale(1.1);
  }

  100% {
    -webkit-transform: scale(1);
    -moz-transform: scale(1);
    -ms-transform: scale(1);
    transform: scale(1);
  }
}

@keyframes animation-0 {
  0% {
    -webkit-transform: scale(0);
    -moz-transform: scale(0);
    -ms-transform: scale(0);
    transform: scale(0);
  }

  90% {
    -webkit-transform: scale(1.1);
    -moz-transform: scale(1.1);
    -ms-transform: scale(1.1);
    transform: scale(1.1);
  }

  100% {
    -webkit-transform: scale(1);
    -moz-transform: scale(1);
    -ms-transform: scale(1);
    transform: scale(1);
  }
}

@keyframes animation-0 {
  0% {
    -webkit-transform: scale(0);
    -moz-transform: scale(0);
    -ms-transform: scale(0);
    transform: scale(0);
  }

  90% {
    -webkit-transform: scale(1.1);
    -moz-transform: scale(1.1);
    -ms-transform: scale(1.1);
    transform: scale(1.1);
  }

  100% {
    -webkit-transform: scale(1);
    -moz-transform: scale(1);
    -ms-transform: scale(1);
    transform: scale(1);
  }
}

.emotion-0 {
  margin: 0 0 1.5rem;
}

.emotion-0:hover {
  -webkit-text-decoration: underline;
  text-decoration: underline;
}

.emotion-1 {
  opacity: 0;
  position: absolute;
}

.emotion-1[data-focus-visible-added]~.fui_radio-button {
  outline: 0;
  box-shadow: 0.5px 0.5px 0 3px #CCC;
}

.emotion-2 {
  display: block;
  position: relative;
  float: left;
  margin-right: 0.625rem;
  width: 1.5rem;
  height: 1.5rem;
  border: 1px solid #666;
  border-radius: 100%;
}

.emotion-2::after {
  content: "";
  position: absolute;
  width: 0;
  height: 0;
  width: 100%;
  height: 100%;
  background-color: transparent;
  background-position-x: 4px;
  background-position-y: 7px;
  -webkit-background-size: 27px;
  background-size: 27px;
  background-repeat: no-repeat;
  -webkit-background-clip: border-box;
  background-clip: border-box;
  border-top: none;
  border-right: none;
}

.emotion-2::before {
  content: "";
  position: absolute;
  display: block;
  width: calc(100% + 1px);
  height: calc(100% + 1px);
  border-radius: 100%;
  left: -1px;
  top: -1px;
  -webkit-animation: animation-0 250ms cubic-bezier(0.175, 0.885, 0.32, 1.275);
  animation: animation-0 250ms cubic-bezier(0.175, 0.885, 0.32, 1.275);
  border: 1px solid #000;
  background-color: #000;
}

.emotion-3 {
  display: inline-block;
  vertical-align: middle;
  line-height: 1.5;
  color: #666;
  max-width: calc(100% - 2.3025rem);
  font-size: 1rem;
  font-family: 'Lynstone',Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
}

<label
    class=" emotion-0"
  >
    <input
      checked=""
      class="emotion-1"
      id="radio-button"
      type="radio"
      value="selected"
    />
    <div
      class="fui_radio-button emotion-2"
    />
    <span
      class="emotion-3"
    >
      radio button
    </span>
  </label>
</DocumentFragment>
`;

exports[`<Radio /> snapshots renders checked state correctly BananaRepublicFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  @keyframes animation-0 {
  0% {
    -webkit-transform: scale(0);
    -moz-transform: scale(0);
    -ms-transform: scale(0);
    transform: scale(0);
  }

  90% {
    -webkit-transform: scale(1.1);
    -moz-transform: scale(1.1);
    -ms-transform: scale(1.1);
    transform: scale(1.1);
  }

  100% {
    -webkit-transform: scale(1);
    -moz-transform: scale(1);
    -ms-transform: scale(1);
    transform: scale(1);
  }
}

@keyframes animation-0 {
  0% {
    -webkit-transform: scale(0);
    -moz-transform: scale(0);
    -ms-transform: scale(0);
    transform: scale(0);
  }

  90% {
    -webkit-transform: scale(1.1);
    -moz-transform: scale(1.1);
    -ms-transform: scale(1.1);
    transform: scale(1.1);
  }

  100% {
    -webkit-transform: scale(1);
    -moz-transform: scale(1);
    -ms-transform: scale(1);
    transform: scale(1);
  }
}

@keyframes animation-0 {
  0% {
    -webkit-transform: scale(0);
    -moz-transform: scale(0);
    -ms-transform: scale(0);
    transform: scale(0);
  }

  90% {
    -webkit-transform: scale(1.1);
    -moz-transform: scale(1.1);
    -ms-transform: scale(1.1);
    transform: scale(1.1);
  }

  100% {
    -webkit-transform: scale(1);
    -moz-transform: scale(1);
    -ms-transform: scale(1);
    transform: scale(1);
  }
}

.emotion-0 {
  margin: 0 0 1.5rem;
}

.emotion-0:hover {
  -webkit-text-decoration: underline;
  text-decoration: underline;
}

.emotion-1 {
  opacity: 0;
  position: absolute;
}

.emotion-1[data-focus-visible-added]~.fui_radio-button {
  outline: 0;
  box-shadow: 0.5px 0.5px 0 3px #5CABF7;
}

.emotion-2 {
  display: block;
  position: relative;
  float: left;
  margin-right: 0.625rem;
  width: 1.5rem;
  height: 1.5rem;
  border: 1px solid #666;
  border-radius: 100%;
}

.emotion-2::after {
  content: "";
  position: absolute;
  width: 0;
  height: 0;
  width: 100%;
  height: 100%;
  background-color: transparent;
  background-position-x: 4px;
  background-position-y: 7px;
  -webkit-background-size: 27px;
  background-size: 27px;
  background-repeat: no-repeat;
  -webkit-background-clip: border-box;
  background-clip: border-box;
  border-top: none;
  border-right: none;
  background-image: url("data:image/svg+xml;charset=utf8,%3Csvg%20width%3D%2220%22%20height%3D%2220%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%3E%0A%0A%3Cpath%20d%3D%22M11.884 0L4.091 7.793.858 4.61 0 5.467l4.088 4.026.01-.01.009.01L12.74.857z%22%20fill%3D%22%23FFF%22%20fill-rule%3D%22nonzero%22%20stroke%3D%22%23FFF%22%20stroke-width%3D%220.35%22%20style%3D%22%22%20%2F%3E%3C%2Fsvg%3E%0A%0A%0A");
}

.emotion-2::before {
  content: "";
  position: absolute;
  display: block;
  width: calc(100% + 1px);
  height: calc(100% + 1px);
  border-radius: 100%;
  left: -1px;
  top: -1px;
  -webkit-animation: animation-0 250ms cubic-bezier(0.175, 0.885, 0.32, 1.275);
  animation: animation-0 250ms cubic-bezier(0.175, 0.885, 0.32, 1.275);
  border: 1px solid #0466CA;
  background-color: #0466CA;
}

.emotion-3 {
  display: inline-block;
  vertical-align: middle;
  line-height: 1.5;
  color: #666;
  max-width: calc(100% - 2.3025rem);
  font-size: 1.1rem;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
}

<label
    class=" emotion-0"
  >
    <input
      checked=""
      class="emotion-1"
      id="radio-button"
      type="radio"
      value="selected"
    />
    <div
      class="fui_radio-button emotion-2"
    />
    <span
      class="emotion-3"
    >
      radio button
    </span>
  </label>
</DocumentFragment>
`;

exports[`<Radio /> snapshots renders checked state correctly Gap 1`] = `
<DocumentFragment>
  @keyframes animation-0 {
  0% {
    -webkit-transform: scale(0);
    -moz-transform: scale(0);
    -ms-transform: scale(0);
    transform: scale(0);
  }

  90% {
    -webkit-transform: scale(1.1);
    -moz-transform: scale(1.1);
    -ms-transform: scale(1.1);
    transform: scale(1.1);
  }

  100% {
    -webkit-transform: scale(1);
    -moz-transform: scale(1);
    -ms-transform: scale(1);
    transform: scale(1);
  }
}

@keyframes animation-0 {
  0% {
    -webkit-transform: scale(0);
    -moz-transform: scale(0);
    -ms-transform: scale(0);
    transform: scale(0);
  }

  90% {
    -webkit-transform: scale(1.1);
    -moz-transform: scale(1.1);
    -ms-transform: scale(1.1);
    transform: scale(1.1);
  }

  100% {
    -webkit-transform: scale(1);
    -moz-transform: scale(1);
    -ms-transform: scale(1);
    transform: scale(1);
  }
}

@keyframes animation-0 {
  0% {
    -webkit-transform: scale(0);
    -moz-transform: scale(0);
    -ms-transform: scale(0);
    transform: scale(0);
  }

  90% {
    -webkit-transform: scale(1.1);
    -moz-transform: scale(1.1);
    -ms-transform: scale(1.1);
    transform: scale(1.1);
  }

  100% {
    -webkit-transform: scale(1);
    -moz-transform: scale(1);
    -ms-transform: scale(1);
    transform: scale(1);
  }
}

@keyframes animation-0 {
  0% {
    -webkit-transform: scale(0);
    -moz-transform: scale(0);
    -ms-transform: scale(0);
    transform: scale(0);
  }

  90% {
    -webkit-transform: scale(1.1);
    -moz-transform: scale(1.1);
    -ms-transform: scale(1.1);
    transform: scale(1.1);
  }

  100% {
    -webkit-transform: scale(1);
    -moz-transform: scale(1);
    -ms-transform: scale(1);
    transform: scale(1);
  }
}

.emotion-0 {
  margin: 0 0 1.5rem;
}

.emotion-0:hover {
  -webkit-text-decoration: underline;
  text-decoration: underline;
}

.emotion-1 {
  opacity: 0;
  position: absolute;
}

.emotion-1[data-focus-visible-added]~.fui_radio-button {
  outline: 0;
  box-shadow: 0.5px 0.5px 0 3px #5CABF7;
}

.emotion-2 {
  display: block;
  position: relative;
  float: left;
  margin-right: 0.625rem;
  width: 1.5rem;
  height: 1.5rem;
  border: 1px solid #666;
  border-radius: 100%;
}

.emotion-2::after {
  content: "";
  position: absolute;
  width: 0;
  height: 0;
  width: 100%;
  height: 100%;
  background-color: transparent;
  background-position-x: 4px;
  background-position-y: 7px;
  -webkit-background-size: 27px;
  background-size: 27px;
  background-repeat: no-repeat;
  -webkit-background-clip: border-box;
  background-clip: border-box;
  border-top: none;
  border-right: none;
}

.emotion-2::before {
  content: "";
  position: absolute;
  display: block;
  width: calc(100% + 1px);
  height: calc(100% + 1px);
  border-radius: 100%;
  left: -1px;
  top: -1px;
  -webkit-animation: animation-0 250ms cubic-bezier(0.175, 0.885, 0.32, 1.275);
  animation: animation-0 250ms cubic-bezier(0.175, 0.885, 0.32, 1.275);
  border: 1px solid #002554;
  background-color: #002554;
}

.emotion-3 {
  display: inline-block;
  vertical-align: middle;
  line-height: 1.5;
  color: #666;
  max-width: calc(100% - 2.3025rem);
  font-size: 1.063rem;
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
}

<label
    class=" emotion-0"
  >
    <input
      checked=""
      class="emotion-1"
      id="radio-button"
      type="radio"
      value="selected"
    />
    <div
      class="fui_radio-button emotion-2"
    />
    <span
      class="emotion-3"
    >
      radio button
    </span>
  </label>
</DocumentFragment>
`;

exports[`<Radio /> snapshots renders checked state correctly Gap in crossBrand 1`] = `
<DocumentFragment>
  @keyframes animation-0 {
  0% {
    -webkit-transform: scale(0);
    -moz-transform: scale(0);
    -ms-transform: scale(0);
    transform: scale(0);
  }

  90% {
    -webkit-transform: scale(1.1);
    -moz-transform: scale(1.1);
    -ms-transform: scale(1.1);
    transform: scale(1.1);
  }

  100% {
    -webkit-transform: scale(1);
    -moz-transform: scale(1);
    -ms-transform: scale(1);
    transform: scale(1);
  }
}

@keyframes animation-0 {
  0% {
    -webkit-transform: scale(0);
    -moz-transform: scale(0);
    -ms-transform: scale(0);
    transform: scale(0);
  }

  90% {
    -webkit-transform: scale(1.1);
    -moz-transform: scale(1.1);
    -ms-transform: scale(1.1);
    transform: scale(1.1);
  }

  100% {
    -webkit-transform: scale(1);
    -moz-transform: scale(1);
    -ms-transform: scale(1);
    transform: scale(1);
  }
}

@keyframes animation-0 {
  0% {
    -webkit-transform: scale(0);
    -moz-transform: scale(0);
    -ms-transform: scale(0);
    transform: scale(0);
  }

  90% {
    -webkit-transform: scale(1.1);
    -moz-transform: scale(1.1);
    -ms-transform: scale(1.1);
    transform: scale(1.1);
  }

  100% {
    -webkit-transform: scale(1);
    -moz-transform: scale(1);
    -ms-transform: scale(1);
    transform: scale(1);
  }
}

@keyframes animation-0 {
  0% {
    -webkit-transform: scale(0);
    -moz-transform: scale(0);
    -ms-transform: scale(0);
    transform: scale(0);
  }

  90% {
    -webkit-transform: scale(1.1);
    -moz-transform: scale(1.1);
    -ms-transform: scale(1.1);
    transform: scale(1.1);
  }

  100% {
    -webkit-transform: scale(1);
    -moz-transform: scale(1);
    -ms-transform: scale(1);
    transform: scale(1);
  }
}

.emotion-0 {
  margin: 0 0 1.5rem;
}

.emotion-0:hover {
  -webkit-text-decoration: underline;
  text-decoration: underline;
}

.emotion-1 {
  opacity: 0;
  position: absolute;
}

.emotion-1[data-focus-visible-added]~.fui_radio-button {
  outline: 0;
  box-shadow: 0.5px 0.5px 0 3px #5CABF7;
}

.emotion-2 {
  display: block;
  position: relative;
  float: left;
  margin-right: 0.625rem;
  width: 1.5rem;
  height: 1.5rem;
  border: 1px solid #666;
  border-radius: 100%;
}

.emotion-2::after {
  content: "";
  position: absolute;
  width: 0;
  height: 0;
  width: 100%;
  height: 100%;
  background-color: transparent;
  background-position-x: 4px;
  background-position-y: 7px;
  -webkit-background-size: 27px;
  background-size: 27px;
  background-repeat: no-repeat;
  -webkit-background-clip: border-box;
  background-clip: border-box;
  border-top: none;
  border-right: none;
  background-image: url("data:image/svg+xml;charset=utf8,%3Csvg%20width%3D%2220%22%20height%3D%2220%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%3E%0A%0A%3Cpath%20d%3D%22M11.884 0L4.091 7.793.858 4.61 0 5.467l4.088 4.026.01-.01.009.01L12.74.857z%22%20fill%3D%22%23FFF%22%20fill-rule%3D%22nonzero%22%20stroke%3D%22%23FFF%22%20stroke-width%3D%220.35%22%20style%3D%22%22%20%2F%3E%3C%2Fsvg%3E%0A%0A%0A");
}

.emotion-2::before {
  content: "";
  position: absolute;
  display: block;
  width: calc(100% + 1px);
  height: calc(100% + 1px);
  border-radius: 100%;
  left: -1px;
  top: -1px;
  -webkit-animation: animation-0 250ms cubic-bezier(0.175, 0.885, 0.32, 1.275);
  animation: animation-0 250ms cubic-bezier(0.175, 0.885, 0.32, 1.275);
  border: 1px solid #0466CA;
  background-color: #0466CA;
}

.emotion-3 {
  display: inline-block;
  vertical-align: middle;
  line-height: 1.5;
  color: #666;
  max-width: calc(100% - 2.3025rem);
  font-size: 1.1rem;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
}

<label
    class=" emotion-0"
  >
    <input
      checked=""
      class="emotion-1"
      id="radio-button"
      type="radio"
      value="selected"
    />
    <div
      class="fui_radio-button emotion-2"
    />
    <span
      class="emotion-3"
    >
      radio button
    </span>
  </label>
</DocumentFragment>
`;

exports[`<Radio /> snapshots renders checked state correctly GapFactoryStore 1`] = `
<DocumentFragment>
  @keyframes animation-0 {
  0% {
    -webkit-transform: scale(0);
    -moz-transform: scale(0);
    -ms-transform: scale(0);
    transform: scale(0);
  }

  90% {
    -webkit-transform: scale(1.1);
    -moz-transform: scale(1.1);
    -ms-transform: scale(1.1);
    transform: scale(1.1);
  }

  100% {
    -webkit-transform: scale(1);
    -moz-transform: scale(1);
    -ms-transform: scale(1);
    transform: scale(1);
  }
}

@keyframes animation-0 {
  0% {
    -webkit-transform: scale(0);
    -moz-transform: scale(0);
    -ms-transform: scale(0);
    transform: scale(0);
  }

  90% {
    -webkit-transform: scale(1.1);
    -moz-transform: scale(1.1);
    -ms-transform: scale(1.1);
    transform: scale(1.1);
  }

  100% {
    -webkit-transform: scale(1);
    -moz-transform: scale(1);
    -ms-transform: scale(1);
    transform: scale(1);
  }
}

@keyframes animation-0 {
  0% {
    -webkit-transform: scale(0);
    -moz-transform: scale(0);
    -ms-transform: scale(0);
    transform: scale(0);
  }

  90% {
    -webkit-transform: scale(1.1);
    -moz-transform: scale(1.1);
    -ms-transform: scale(1.1);
    transform: scale(1.1);
  }

  100% {
    -webkit-transform: scale(1);
    -moz-transform: scale(1);
    -ms-transform: scale(1);
    transform: scale(1);
  }
}

@keyframes animation-0 {
  0% {
    -webkit-transform: scale(0);
    -moz-transform: scale(0);
    -ms-transform: scale(0);
    transform: scale(0);
  }

  90% {
    -webkit-transform: scale(1.1);
    -moz-transform: scale(1.1);
    -ms-transform: scale(1.1);
    transform: scale(1.1);
  }

  100% {
    -webkit-transform: scale(1);
    -moz-transform: scale(1);
    -ms-transform: scale(1);
    transform: scale(1);
  }
}

.emotion-0 {
  margin: 0 0 1.5rem;
}

.emotion-0:hover {
  -webkit-text-decoration: underline;
  text-decoration: underline;
}

.emotion-1 {
  opacity: 0;
  position: absolute;
}

.emotion-1[data-focus-visible-added]~.fui_radio-button {
  outline: 0;
  box-shadow: 0.5px 0.5px 0 3px #5CABF7;
}

.emotion-2 {
  display: block;
  position: relative;
  float: left;
  margin-right: 0.625rem;
  width: 1.5rem;
  height: 1.5rem;
  border: 1px solid #666;
  border-radius: 100%;
}

.emotion-2::after {
  content: "";
  position: absolute;
  width: 0;
  height: 0;
  width: 100%;
  height: 100%;
  background-color: transparent;
  background-position-x: 4px;
  background-position-y: 7px;
  -webkit-background-size: 27px;
  background-size: 27px;
  background-repeat: no-repeat;
  -webkit-background-clip: border-box;
  background-clip: border-box;
  border-top: none;
  border-right: none;
}

.emotion-2::before {
  content: "";
  position: absolute;
  display: block;
  width: calc(100% + 1px);
  height: calc(100% + 1px);
  border-radius: 100%;
  left: -1px;
  top: -1px;
  -webkit-animation: animation-0 250ms cubic-bezier(0.175, 0.885, 0.32, 1.275);
  animation: animation-0 250ms cubic-bezier(0.175, 0.885, 0.32, 1.275);
  border: 1px solid #002554;
  background-color: #002554;
}

.emotion-3 {
  display: inline-block;
  vertical-align: middle;
  line-height: 1.5;
  color: #666;
  max-width: calc(100% - 2.3025rem);
  font-size: 1.063rem;
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
}

<label
    class=" emotion-0"
  >
    <input
      checked=""
      class="emotion-1"
      id="radio-button"
      type="radio"
      value="selected"
    />
    <div
      class="fui_radio-button emotion-2"
    />
    <span
      class="emotion-3"
    >
      radio button
    </span>
  </label>
</DocumentFragment>
`;

exports[`<Radio /> snapshots renders checked state correctly GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  @keyframes animation-0 {
  0% {
    -webkit-transform: scale(0);
    -moz-transform: scale(0);
    -ms-transform: scale(0);
    transform: scale(0);
  }

  90% {
    -webkit-transform: scale(1.1);
    -moz-transform: scale(1.1);
    -ms-transform: scale(1.1);
    transform: scale(1.1);
  }

  100% {
    -webkit-transform: scale(1);
    -moz-transform: scale(1);
    -ms-transform: scale(1);
    transform: scale(1);
  }
}

@keyframes animation-0 {
  0% {
    -webkit-transform: scale(0);
    -moz-transform: scale(0);
    -ms-transform: scale(0);
    transform: scale(0);
  }

  90% {
    -webkit-transform: scale(1.1);
    -moz-transform: scale(1.1);
    -ms-transform: scale(1.1);
    transform: scale(1.1);
  }

  100% {
    -webkit-transform: scale(1);
    -moz-transform: scale(1);
    -ms-transform: scale(1);
    transform: scale(1);
  }
}

@keyframes animation-0 {
  0% {
    -webkit-transform: scale(0);
    -moz-transform: scale(0);
    -ms-transform: scale(0);
    transform: scale(0);
  }

  90% {
    -webkit-transform: scale(1.1);
    -moz-transform: scale(1.1);
    -ms-transform: scale(1.1);
    transform: scale(1.1);
  }

  100% {
    -webkit-transform: scale(1);
    -moz-transform: scale(1);
    -ms-transform: scale(1);
    transform: scale(1);
  }
}

@keyframes animation-0 {
  0% {
    -webkit-transform: scale(0);
    -moz-transform: scale(0);
    -ms-transform: scale(0);
    transform: scale(0);
  }

  90% {
    -webkit-transform: scale(1.1);
    -moz-transform: scale(1.1);
    -ms-transform: scale(1.1);
    transform: scale(1.1);
  }

  100% {
    -webkit-transform: scale(1);
    -moz-transform: scale(1);
    -ms-transform: scale(1);
    transform: scale(1);
  }
}

.emotion-0 {
  margin: 0 0 1.5rem;
}

.emotion-0:hover {
  -webkit-text-decoration: underline;
  text-decoration: underline;
}

.emotion-1 {
  opacity: 0;
  position: absolute;
}

.emotion-1[data-focus-visible-added]~.fui_radio-button {
  outline: 0;
  box-shadow: 0.5px 0.5px 0 3px #5CABF7;
}

.emotion-2 {
  display: block;
  position: relative;
  float: left;
  margin-right: 0.625rem;
  width: 1.5rem;
  height: 1.5rem;
  border: 1px solid #666;
  border-radius: 100%;
}

.emotion-2::after {
  content: "";
  position: absolute;
  width: 0;
  height: 0;
  width: 100%;
  height: 100%;
  background-color: transparent;
  background-position-x: 4px;
  background-position-y: 7px;
  -webkit-background-size: 27px;
  background-size: 27px;
  background-repeat: no-repeat;
  -webkit-background-clip: border-box;
  background-clip: border-box;
  border-top: none;
  border-right: none;
  background-image: url("data:image/svg+xml;charset=utf8,%3Csvg%20width%3D%2220%22%20height%3D%2220%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%3E%0A%0A%3Cpath%20d%3D%22M11.884 0L4.091 7.793.858 4.61 0 5.467l4.088 4.026.01-.01.009.01L12.74.857z%22%20fill%3D%22%23FFF%22%20fill-rule%3D%22nonzero%22%20stroke%3D%22%23FFF%22%20stroke-width%3D%220.35%22%20style%3D%22%22%20%2F%3E%3C%2Fsvg%3E%0A%0A%0A");
}

.emotion-2::before {
  content: "";
  position: absolute;
  display: block;
  width: calc(100% + 1px);
  height: calc(100% + 1px);
  border-radius: 100%;
  left: -1px;
  top: -1px;
  -webkit-animation: animation-0 250ms cubic-bezier(0.175, 0.885, 0.32, 1.275);
  animation: animation-0 250ms cubic-bezier(0.175, 0.885, 0.32, 1.275);
  border: 1px solid #0466CA;
  background-color: #0466CA;
}

.emotion-3 {
  display: inline-block;
  vertical-align: middle;
  line-height: 1.5;
  color: #666;
  max-width: calc(100% - 2.3025rem);
  font-size: 1.1rem;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
}

<label
    class=" emotion-0"
  >
    <input
      checked=""
      class="emotion-1"
      id="radio-button"
      type="radio"
      value="selected"
    />
    <div
      class="fui_radio-button emotion-2"
    />
    <span
      class="emotion-3"
    >
      radio button
    </span>
  </label>
</DocumentFragment>
`;

exports[`<Radio /> snapshots renders checked state correctly OldNavy 1`] = `
<DocumentFragment>
  @keyframes animation-0 {
  0% {
    -webkit-transform: scale(0);
    -moz-transform: scale(0);
    -ms-transform: scale(0);
    transform: scale(0);
  }

  90% {
    -webkit-transform: scale(1.1);
    -moz-transform: scale(1.1);
    -ms-transform: scale(1.1);
    transform: scale(1.1);
  }

  100% {
    -webkit-transform: scale(1);
    -moz-transform: scale(1);
    -ms-transform: scale(1);
    transform: scale(1);
  }
}

@keyframes animation-0 {
  0% {
    -webkit-transform: scale(0);
    -moz-transform: scale(0);
    -ms-transform: scale(0);
    transform: scale(0);
  }

  90% {
    -webkit-transform: scale(1.1);
    -moz-transform: scale(1.1);
    -ms-transform: scale(1.1);
    transform: scale(1.1);
  }

  100% {
    -webkit-transform: scale(1);
    -moz-transform: scale(1);
    -ms-transform: scale(1);
    transform: scale(1);
  }
}

@keyframes animation-0 {
  0% {
    -webkit-transform: scale(0);
    -moz-transform: scale(0);
    -ms-transform: scale(0);
    transform: scale(0);
  }

  90% {
    -webkit-transform: scale(1.1);
    -moz-transform: scale(1.1);
    -ms-transform: scale(1.1);
    transform: scale(1.1);
  }

  100% {
    -webkit-transform: scale(1);
    -moz-transform: scale(1);
    -ms-transform: scale(1);
    transform: scale(1);
  }
}

@keyframes animation-0 {
  0% {
    -webkit-transform: scale(0);
    -moz-transform: scale(0);
    -ms-transform: scale(0);
    transform: scale(0);
  }

  90% {
    -webkit-transform: scale(1.1);
    -moz-transform: scale(1.1);
    -ms-transform: scale(1.1);
    transform: scale(1.1);
  }

  100% {
    -webkit-transform: scale(1);
    -moz-transform: scale(1);
    -ms-transform: scale(1);
    transform: scale(1);
  }
}

@keyframes animation-0 {
  0% {
    -webkit-transform: scale(0);
    -moz-transform: scale(0);
    -ms-transform: scale(0);
    transform: scale(0);
  }

  90% {
    -webkit-transform: scale(1.1);
    -moz-transform: scale(1.1);
    -ms-transform: scale(1.1);
    transform: scale(1.1);
  }

  100% {
    -webkit-transform: scale(1);
    -moz-transform: scale(1);
    -ms-transform: scale(1);
    transform: scale(1);
  }
}

.emotion-0 {
  margin: 0 0 1.5rem;
}

.emotion-0:hover {
  -webkit-text-decoration: underline;
  text-decoration: underline;
}

.emotion-1 {
  opacity: 0;
  position: absolute;
}

.emotion-1[data-focus-visible-added]~.fui_radio-button {
  outline: 0;
  box-shadow: 0.5px 0.5px 0 3px #5CABF7;
}

.emotion-2 {
  display: block;
  position: relative;
  float: left;
  margin-right: 0.625rem;
  width: 1.5rem;
  height: 1.5rem;
  border: 1px solid #666;
  border-radius: 100%;
}

.emotion-2::after {
  content: "";
  position: absolute;
  width: 0;
  height: 0;
  width: 100%;
  height: 100%;
  background-color: transparent;
  background-position-x: 4px;
  background-position-y: 7px;
  -webkit-background-size: 27px;
  background-size: 27px;
  background-repeat: no-repeat;
  -webkit-background-clip: border-box;
  background-clip: border-box;
  border-top: none;
  border-right: none;
}

.emotion-2::before {
  content: "";
  position: absolute;
  display: block;
  width: calc(100% + 1px);
  height: calc(100% + 1px);
  border-radius: 100%;
  left: -1px;
  top: -1px;
  -webkit-animation: animation-0 250ms cubic-bezier(0.175, 0.885, 0.32, 1.275);
  animation: animation-0 250ms cubic-bezier(0.175, 0.885, 0.32, 1.275);
  border: 1px solid #003764;
  background-color: #003764;
}

.emotion-3 {
  display: inline-block;
  vertical-align: middle;
  line-height: 1.5;
  color: #666;
  max-width: calc(100% - 2.3025rem);
  font-size: 1rem;
  font-family: 'Gotham','Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
}

<label
    class=" emotion-0"
  >
    <input
      checked=""
      class="emotion-1"
      id="radio-button"
      type="radio"
      value="selected"
    />
    <div
      class="fui_radio-button emotion-2"
    />
    <span
      class="emotion-3"
    >
      radio button
    </span>
  </label>
</DocumentFragment>
`;

exports[`<Radio /> snapshots renders checked state correctly OldNavy in crossBrand 1`] = `
<DocumentFragment>
  @keyframes animation-0 {
  0% {
    -webkit-transform: scale(0);
    -moz-transform: scale(0);
    -ms-transform: scale(0);
    transform: scale(0);
  }

  90% {
    -webkit-transform: scale(1.1);
    -moz-transform: scale(1.1);
    -ms-transform: scale(1.1);
    transform: scale(1.1);
  }

  100% {
    -webkit-transform: scale(1);
    -moz-transform: scale(1);
    -ms-transform: scale(1);
    transform: scale(1);
  }
}

@keyframes animation-0 {
  0% {
    -webkit-transform: scale(0);
    -moz-transform: scale(0);
    -ms-transform: scale(0);
    transform: scale(0);
  }

  90% {
    -webkit-transform: scale(1.1);
    -moz-transform: scale(1.1);
    -ms-transform: scale(1.1);
    transform: scale(1.1);
  }

  100% {
    -webkit-transform: scale(1);
    -moz-transform: scale(1);
    -ms-transform: scale(1);
    transform: scale(1);
  }
}

@keyframes animation-0 {
  0% {
    -webkit-transform: scale(0);
    -moz-transform: scale(0);
    -ms-transform: scale(0);
    transform: scale(0);
  }

  90% {
    -webkit-transform: scale(1.1);
    -moz-transform: scale(1.1);
    -ms-transform: scale(1.1);
    transform: scale(1.1);
  }

  100% {
    -webkit-transform: scale(1);
    -moz-transform: scale(1);
    -ms-transform: scale(1);
    transform: scale(1);
  }
}

@keyframes animation-0 {
  0% {
    -webkit-transform: scale(0);
    -moz-transform: scale(0);
    -ms-transform: scale(0);
    transform: scale(0);
  }

  90% {
    -webkit-transform: scale(1.1);
    -moz-transform: scale(1.1);
    -ms-transform: scale(1.1);
    transform: scale(1.1);
  }

  100% {
    -webkit-transform: scale(1);
    -moz-transform: scale(1);
    -ms-transform: scale(1);
    transform: scale(1);
  }
}

@keyframes animation-0 {
  0% {
    -webkit-transform: scale(0);
    -moz-transform: scale(0);
    -ms-transform: scale(0);
    transform: scale(0);
  }

  90% {
    -webkit-transform: scale(1.1);
    -moz-transform: scale(1.1);
    -ms-transform: scale(1.1);
    transform: scale(1.1);
  }

  100% {
    -webkit-transform: scale(1);
    -moz-transform: scale(1);
    -ms-transform: scale(1);
    transform: scale(1);
  }
}

.emotion-0 {
  margin: 0 0 1.5rem;
}

.emotion-0:hover {
  -webkit-text-decoration: underline;
  text-decoration: underline;
}

.emotion-1 {
  opacity: 0;
  position: absolute;
}

.emotion-1[data-focus-visible-added]~.fui_radio-button {
  outline: 0;
  box-shadow: 0.5px 0.5px 0 3px #5CABF7;
}

.emotion-2 {
  display: block;
  position: relative;
  float: left;
  margin-right: 0.625rem;
  width: 1.5rem;
  height: 1.5rem;
  border: 1px solid #666;
  border-radius: 100%;
}

.emotion-2::after {
  content: "";
  position: absolute;
  width: 0;
  height: 0;
  width: 100%;
  height: 100%;
  background-color: transparent;
  background-position-x: 4px;
  background-position-y: 7px;
  -webkit-background-size: 27px;
  background-size: 27px;
  background-repeat: no-repeat;
  -webkit-background-clip: border-box;
  background-clip: border-box;
  border-top: none;
  border-right: none;
  background-image: url("data:image/svg+xml;charset=utf8,%3Csvg%20width%3D%2220%22%20height%3D%2220%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%3E%0A%0A%3Cpath%20d%3D%22M11.884 0L4.091 7.793.858 4.61 0 5.467l4.088 4.026.01-.01.009.01L12.74.857z%22%20fill%3D%22%23FFF%22%20fill-rule%3D%22nonzero%22%20stroke%3D%22%23FFF%22%20stroke-width%3D%220.35%22%20style%3D%22%22%20%2F%3E%3C%2Fsvg%3E%0A%0A%0A");
}

.emotion-2::before {
  content: "";
  position: absolute;
  display: block;
  width: calc(100% + 1px);
  height: calc(100% + 1px);
  border-radius: 100%;
  left: -1px;
  top: -1px;
  -webkit-animation: animation-0 250ms cubic-bezier(0.175, 0.885, 0.32, 1.275);
  animation: animation-0 250ms cubic-bezier(0.175, 0.885, 0.32, 1.275);
  border: 1px solid #0466CA;
  background-color: #0466CA;
}

.emotion-3 {
  display: inline-block;
  vertical-align: middle;
  line-height: 1.5;
  color: #666;
  max-width: calc(100% - 2.3025rem);
  font-size: 1.1rem;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
}

<label
    class=" emotion-0"
  >
    <input
      checked=""
      class="emotion-1"
      id="radio-button"
      type="radio"
      value="selected"
    />
    <div
      class="fui_radio-button emotion-2"
    />
    <span
      class="emotion-3"
    >
      radio button
    </span>
  </label>
</DocumentFragment>
`;

exports[`<Radio /> snapshots renders default state correctly Athleta 1`] = `
<DocumentFragment>
  .emotion-0 {
  margin: 0 0 1.5rem;
}

.emotion-0:hover {
  -webkit-text-decoration: underline;
  text-decoration: underline;
}

.emotion-1 {
  opacity: 0;
  position: absolute;
}

.emotion-1[data-focus-visible-added]~.fui_radio-button {
  outline: 0;
  box-shadow: 0px 0px 0 3px #5CABF7;
}

.emotion-2 {
  display: block;
  position: relative;
  float: left;
  margin-right: 0.625rem;
  width: 1.5rem;
  height: 1.5rem;
  border: 1px solid #666;
  border-radius: 100%;
}

.emotion-2::after {
  content: "";
  position: absolute;
  width: 0;
  height: 0;
}

.emotion-2::before {
  content: "";
  position: absolute;
  display: block;
  width: calc(100% + 1px);
  height: calc(100% + 1px);
  border-radius: 100%;
  left: -1px;
  top: -1px;
}

.emotion-3 {
  display: inline-block;
  vertical-align: middle;
  line-height: 1.5;
  color: #666;
  max-width: calc(100% - 2.3025rem);
  font-size: 1.063rem;
  font-family: 'Avenir Next','Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
}

<label
    class=" emotion-0"
  >
    <input
      class="emotion-1"
      id="radio-button"
      type="radio"
      value="selected"
    />
    <div
      class="fui_radio-button emotion-2"
    />
    <span
      class="emotion-3"
    >
      radio button
    </span>
  </label>
</DocumentFragment>
`;

exports[`<Radio /> snapshots renders default state correctly Athleta in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  margin: 0 0 1.5rem;
}

.emotion-0:hover {
  -webkit-text-decoration: underline;
  text-decoration: underline;
}

.emotion-1 {
  opacity: 0;
  position: absolute;
}

.emotion-1[data-focus-visible-added]~.fui_radio-button {
  outline: 0;
  box-shadow: 0px 0px 0 3px #5CABF7;
}

.emotion-2 {
  display: block;
  position: relative;
  float: left;
  margin-right: 0.625rem;
  width: 1.5rem;
  height: 1.5rem;
  border: 1px solid #666;
  border-radius: 100%;
}

.emotion-2::after {
  content: "";
  position: absolute;
  width: 0;
  height: 0;
}

.emotion-2::before {
  content: "";
  position: absolute;
  display: block;
  width: calc(100% + 1px);
  height: calc(100% + 1px);
  border-radius: 100%;
  left: -1px;
  top: -1px;
}

.emotion-3 {
  display: inline-block;
  vertical-align: middle;
  line-height: 1.5;
  color: #666;
  max-width: calc(100% - 2.3025rem);
  font-size: 1.1rem;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
}

<label
    class=" emotion-0"
  >
    <input
      class="emotion-1"
      id="radio-button"
      type="radio"
      value="selected"
    />
    <div
      class="fui_radio-button emotion-2"
    />
    <span
      class="emotion-3"
    >
      radio button
    </span>
  </label>
</DocumentFragment>
`;

exports[`<Radio /> snapshots renders default state correctly BananaRepublic 1`] = `
<DocumentFragment>
  .emotion-0 {
  margin: 0 0 1.5rem;
}

.emotion-0:hover {
  -webkit-text-decoration: underline;
  text-decoration: underline;
}

.emotion-1 {
  opacity: 0;
  position: absolute;
}

.emotion-1[data-focus-visible-added]~.fui_radio-button {
  outline: 0;
  box-shadow: 0px 0px 0 3px #CCC;
}

.emotion-2 {
  display: block;
  position: relative;
  float: left;
  margin-right: 0.625rem;
  width: 1.5rem;
  height: 1.5rem;
  border: 1px solid #666;
  border-radius: 100%;
}

.emotion-2::after {
  content: "";
  position: absolute;
  width: 0;
  height: 0;
}

.emotion-2::before {
  content: "";
  position: absolute;
  display: block;
  width: calc(100% + 1px);
  height: calc(100% + 1px);
  border-radius: 100%;
  left: -1px;
  top: -1px;
}

.emotion-3 {
  display: inline-block;
  vertical-align: middle;
  line-height: 1.5;
  color: #666;
  max-width: calc(100% - 2.3025rem);
  font-size: 1rem;
  font-family: 'Lynstone',Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
}

<label
    class=" emotion-0"
  >
    <input
      class="emotion-1"
      id="radio-button"
      type="radio"
      value="selected"
    />
    <div
      class="fui_radio-button emotion-2"
    />
    <span
      class="emotion-3"
    >
      radio button
    </span>
  </label>
</DocumentFragment>
`;

exports[`<Radio /> snapshots renders default state correctly BananaRepublic in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  margin: 0 0 1.5rem;
}

.emotion-0:hover {
  -webkit-text-decoration: underline;
  text-decoration: underline;
}

.emotion-1 {
  opacity: 0;
  position: absolute;
}

.emotion-1[data-focus-visible-added]~.fui_radio-button {
  outline: 0;
  box-shadow: 0px 0px 0 3px #5CABF7;
}

.emotion-2 {
  display: block;
  position: relative;
  float: left;
  margin-right: 0.625rem;
  width: 1.5rem;
  height: 1.5rem;
  border: 1px solid #666;
  border-radius: 100%;
}

.emotion-2::after {
  content: "";
  position: absolute;
  width: 0;
  height: 0;
}

.emotion-2::before {
  content: "";
  position: absolute;
  display: block;
  width: calc(100% + 1px);
  height: calc(100% + 1px);
  border-radius: 100%;
  left: -1px;
  top: -1px;
}

.emotion-3 {
  display: inline-block;
  vertical-align: middle;
  line-height: 1.5;
  color: #666;
  max-width: calc(100% - 2.3025rem);
  font-size: 1.1rem;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
}

<label
    class=" emotion-0"
  >
    <input
      class="emotion-1"
      id="radio-button"
      type="radio"
      value="selected"
    />
    <div
      class="fui_radio-button emotion-2"
    />
    <span
      class="emotion-3"
    >
      radio button
    </span>
  </label>
</DocumentFragment>
`;

exports[`<Radio /> snapshots renders default state correctly BananaRepublicFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  margin: 0 0 1.5rem;
}

.emotion-0:hover {
  -webkit-text-decoration: underline;
  text-decoration: underline;
}

.emotion-1 {
  opacity: 0;
  position: absolute;
}

.emotion-1[data-focus-visible-added]~.fui_radio-button {
  outline: 0;
  box-shadow: 0px 0px 0 3px #CCC;
}

.emotion-2 {
  display: block;
  position: relative;
  float: left;
  margin-right: 0.625rem;
  width: 1.5rem;
  height: 1.5rem;
  border: 1px solid #666;
  border-radius: 100%;
}

.emotion-2::after {
  content: "";
  position: absolute;
  width: 0;
  height: 0;
}

.emotion-2::before {
  content: "";
  position: absolute;
  display: block;
  width: calc(100% + 1px);
  height: calc(100% + 1px);
  border-radius: 100%;
  left: -1px;
  top: -1px;
}

.emotion-3 {
  display: inline-block;
  vertical-align: middle;
  line-height: 1.5;
  color: #666;
  max-width: calc(100% - 2.3025rem);
  font-size: 1rem;
  font-family: 'Lynstone',Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
}

<label
    class=" emotion-0"
  >
    <input
      class="emotion-1"
      id="radio-button"
      type="radio"
      value="selected"
    />
    <div
      class="fui_radio-button emotion-2"
    />
    <span
      class="emotion-3"
    >
      radio button
    </span>
  </label>
</DocumentFragment>
`;

exports[`<Radio /> snapshots renders default state correctly BananaRepublicFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  margin: 0 0 1.5rem;
}

.emotion-0:hover {
  -webkit-text-decoration: underline;
  text-decoration: underline;
}

.emotion-1 {
  opacity: 0;
  position: absolute;
}

.emotion-1[data-focus-visible-added]~.fui_radio-button {
  outline: 0;
  box-shadow: 0px 0px 0 3px #5CABF7;
}

.emotion-2 {
  display: block;
  position: relative;
  float: left;
  margin-right: 0.625rem;
  width: 1.5rem;
  height: 1.5rem;
  border: 1px solid #666;
  border-radius: 100%;
}

.emotion-2::after {
  content: "";
  position: absolute;
  width: 0;
  height: 0;
}

.emotion-2::before {
  content: "";
  position: absolute;
  display: block;
  width: calc(100% + 1px);
  height: calc(100% + 1px);
  border-radius: 100%;
  left: -1px;
  top: -1px;
}

.emotion-3 {
  display: inline-block;
  vertical-align: middle;
  line-height: 1.5;
  color: #666;
  max-width: calc(100% - 2.3025rem);
  font-size: 1.1rem;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
}

<label
    class=" emotion-0"
  >
    <input
      class="emotion-1"
      id="radio-button"
      type="radio"
      value="selected"
    />
    <div
      class="fui_radio-button emotion-2"
    />
    <span
      class="emotion-3"
    >
      radio button
    </span>
  </label>
</DocumentFragment>
`;

exports[`<Radio /> snapshots renders default state correctly Gap 1`] = `
<DocumentFragment>
  .emotion-0 {
  margin: 0 0 1.5rem;
}

.emotion-0:hover {
  -webkit-text-decoration: underline;
  text-decoration: underline;
}

.emotion-1 {
  opacity: 0;
  position: absolute;
}

.emotion-1[data-focus-visible-added]~.fui_radio-button {
  outline: 0;
  box-shadow: 0px 0px 0 3px #5CABF7;
}

.emotion-2 {
  display: block;
  position: relative;
  float: left;
  margin-right: 0.625rem;
  width: 1.5rem;
  height: 1.5rem;
  border: 1px solid #666;
  border-radius: 100%;
}

.emotion-2::after {
  content: "";
  position: absolute;
  width: 0;
  height: 0;
}

.emotion-2::before {
  content: "";
  position: absolute;
  display: block;
  width: calc(100% + 1px);
  height: calc(100% + 1px);
  border-radius: 100%;
  left: -1px;
  top: -1px;
}

.emotion-3 {
  display: inline-block;
  vertical-align: middle;
  line-height: 1.5;
  color: #666;
  max-width: calc(100% - 2.3025rem);
  font-size: 1.063rem;
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
}

<label
    class=" emotion-0"
  >
    <input
      class="emotion-1"
      id="radio-button"
      type="radio"
      value="selected"
    />
    <div
      class="fui_radio-button emotion-2"
    />
    <span
      class="emotion-3"
    >
      radio button
    </span>
  </label>
</DocumentFragment>
`;

exports[`<Radio /> snapshots renders default state correctly Gap in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  margin: 0 0 1.5rem;
}

.emotion-0:hover {
  -webkit-text-decoration: underline;
  text-decoration: underline;
}

.emotion-1 {
  opacity: 0;
  position: absolute;
}

.emotion-1[data-focus-visible-added]~.fui_radio-button {
  outline: 0;
  box-shadow: 0px 0px 0 3px #5CABF7;
}

.emotion-2 {
  display: block;
  position: relative;
  float: left;
  margin-right: 0.625rem;
  width: 1.5rem;
  height: 1.5rem;
  border: 1px solid #666;
  border-radius: 100%;
}

.emotion-2::after {
  content: "";
  position: absolute;
  width: 0;
  height: 0;
}

.emotion-2::before {
  content: "";
  position: absolute;
  display: block;
  width: calc(100% + 1px);
  height: calc(100% + 1px);
  border-radius: 100%;
  left: -1px;
  top: -1px;
}

.emotion-3 {
  display: inline-block;
  vertical-align: middle;
  line-height: 1.5;
  color: #666;
  max-width: calc(100% - 2.3025rem);
  font-size: 1.1rem;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
}

<label
    class=" emotion-0"
  >
    <input
      class="emotion-1"
      id="radio-button"
      type="radio"
      value="selected"
    />
    <div
      class="fui_radio-button emotion-2"
    />
    <span
      class="emotion-3"
    >
      radio button
    </span>
  </label>
</DocumentFragment>
`;

exports[`<Radio /> snapshots renders default state correctly GapFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  margin: 0 0 1.5rem;
}

.emotion-0:hover {
  -webkit-text-decoration: underline;
  text-decoration: underline;
}

.emotion-1 {
  opacity: 0;
  position: absolute;
}

.emotion-1[data-focus-visible-added]~.fui_radio-button {
  outline: 0;
  box-shadow: 0px 0px 0 3px #5CABF7;
}

.emotion-2 {
  display: block;
  position: relative;
  float: left;
  margin-right: 0.625rem;
  width: 1.5rem;
  height: 1.5rem;
  border: 1px solid #666;
  border-radius: 100%;
}

.emotion-2::after {
  content: "";
  position: absolute;
  width: 0;
  height: 0;
}

.emotion-2::before {
  content: "";
  position: absolute;
  display: block;
  width: calc(100% + 1px);
  height: calc(100% + 1px);
  border-radius: 100%;
  left: -1px;
  top: -1px;
}

.emotion-3 {
  display: inline-block;
  vertical-align: middle;
  line-height: 1.5;
  color: #666;
  max-width: calc(100% - 2.3025rem);
  font-size: 1.063rem;
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
}

<label
    class=" emotion-0"
  >
    <input
      class="emotion-1"
      id="radio-button"
      type="radio"
      value="selected"
    />
    <div
      class="fui_radio-button emotion-2"
    />
    <span
      class="emotion-3"
    >
      radio button
    </span>
  </label>
</DocumentFragment>
`;

exports[`<Radio /> snapshots renders default state correctly GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  margin: 0 0 1.5rem;
}

.emotion-0:hover {
  -webkit-text-decoration: underline;
  text-decoration: underline;
}

.emotion-1 {
  opacity: 0;
  position: absolute;
}

.emotion-1[data-focus-visible-added]~.fui_radio-button {
  outline: 0;
  box-shadow: 0px 0px 0 3px #5CABF7;
}

.emotion-2 {
  display: block;
  position: relative;
  float: left;
  margin-right: 0.625rem;
  width: 1.5rem;
  height: 1.5rem;
  border: 1px solid #666;
  border-radius: 100%;
}

.emotion-2::after {
  content: "";
  position: absolute;
  width: 0;
  height: 0;
}

.emotion-2::before {
  content: "";
  position: absolute;
  display: block;
  width: calc(100% + 1px);
  height: calc(100% + 1px);
  border-radius: 100%;
  left: -1px;
  top: -1px;
}

.emotion-3 {
  display: inline-block;
  vertical-align: middle;
  line-height: 1.5;
  color: #666;
  max-width: calc(100% - 2.3025rem);
  font-size: 1.1rem;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
}

<label
    class=" emotion-0"
  >
    <input
      class="emotion-1"
      id="radio-button"
      type="radio"
      value="selected"
    />
    <div
      class="fui_radio-button emotion-2"
    />
    <span
      class="emotion-3"
    >
      radio button
    </span>
  </label>
</DocumentFragment>
`;

exports[`<Radio /> snapshots renders default state correctly OldNavy 1`] = `
<DocumentFragment>
  .emotion-0 {
  margin: 0 0 1.5rem;
}

.emotion-0:hover {
  -webkit-text-decoration: underline;
  text-decoration: underline;
}

.emotion-1 {
  opacity: 0;
  position: absolute;
}

.emotion-1[data-focus-visible-added]~.fui_radio-button {
  outline: 0;
  box-shadow: 0px 0px 0 3px #5CABF7;
}

.emotion-2 {
  display: block;
  position: relative;
  float: left;
  margin-right: 0.625rem;
  width: 1.5rem;
  height: 1.5rem;
  border: 1px solid #666;
  border-radius: 100%;
}

.emotion-2::after {
  content: "";
  position: absolute;
  width: 0;
  height: 0;
}

.emotion-2::before {
  content: "";
  position: absolute;
  display: block;
  width: calc(100% + 1px);
  height: calc(100% + 1px);
  border-radius: 100%;
  left: -1px;
  top: -1px;
}

.emotion-3 {
  display: inline-block;
  vertical-align: middle;
  line-height: 1.5;
  color: #666;
  max-width: calc(100% - 2.3025rem);
  font-size: 1rem;
  font-family: 'Gotham','Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
}

<label
    class=" emotion-0"
  >
    <input
      class="emotion-1"
      id="radio-button"
      type="radio"
      value="selected"
    />
    <div
      class="fui_radio-button emotion-2"
    />
    <span
      class="emotion-3"
    >
      radio button
    </span>
  </label>
</DocumentFragment>
`;

exports[`<Radio /> snapshots renders default state correctly OldNavy in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  margin: 0 0 1.5rem;
}

.emotion-0:hover {
  -webkit-text-decoration: underline;
  text-decoration: underline;
}

.emotion-1 {
  opacity: 0;
  position: absolute;
}

.emotion-1[data-focus-visible-added]~.fui_radio-button {
  outline: 0;
  box-shadow: 0px 0px 0 3px #5CABF7;
}

.emotion-2 {
  display: block;
  position: relative;
  float: left;
  margin-right: 0.625rem;
  width: 1.5rem;
  height: 1.5rem;
  border: 1px solid #666;
  border-radius: 100%;
}

.emotion-2::after {
  content: "";
  position: absolute;
  width: 0;
  height: 0;
}

.emotion-2::before {
  content: "";
  position: absolute;
  display: block;
  width: calc(100% + 1px);
  height: calc(100% + 1px);
  border-radius: 100%;
  left: -1px;
  top: -1px;
}

.emotion-3 {
  display: inline-block;
  vertical-align: middle;
  line-height: 1.5;
  color: #666;
  max-width: calc(100% - 2.3025rem);
  font-size: 1.1rem;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
}

<label
    class=" emotion-0"
  >
    <input
      class="emotion-1"
      id="radio-button"
      type="radio"
      value="selected"
    />
    <div
      class="fui_radio-button emotion-2"
    />
    <span
      class="emotion-3"
    >
      radio button
    </span>
  </label>
</DocumentFragment>
`;

exports[`<Radio /> snapshots renders disabled state correctly Athleta 1`] = `
<DocumentFragment>
  .emotion-0 {
  margin: 0 0 1.5rem;
}

.emotion-0:hover {
  -webkit-text-decoration: underline;
  text-decoration: underline;
}

.emotion-1 {
  opacity: 0;
  position: absolute;
}

.emotion-1[data-focus-visible-added]~.fui_radio-button {
  outline: 0;
  box-shadow: 0px 0px 0 3px #5CABF7;
}

.emotion-2 {
  display: block;
  position: relative;
  float: left;
  margin-right: 0.625rem;
  width: 1.5rem;
  height: 1.5rem;
  border: 1px solid #CCC;
  border-radius: 100%;
}

.emotion-2::after {
  content: "";
  position: absolute;
  width: 0;
  height: 0;
}

.emotion-2::before {
  content: "";
  position: absolute;
  display: block;
  width: calc(100% + 1px);
  height: calc(100% + 1px);
  border-radius: 100%;
  left: -1px;
  top: -1px;
}

.emotion-3 {
  display: inline-block;
  vertical-align: middle;
  line-height: 1.5;
  color: #CCC;
  max-width: calc(100% - 2.3025rem);
  font-size: 1.063rem;
  font-family: 'Avenir Next','Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
}

<label
    class=" emotion-0"
    disabled=""
  >
    <input
      class="emotion-1"
      disabled=""
      id="radio-button"
      type="radio"
      value="selected"
    />
    <div
      class="fui_radio-button emotion-2"
      disabled=""
    />
    <span
      class="emotion-3"
    >
      radio button
    </span>
  </label>
</DocumentFragment>
`;

exports[`<Radio /> snapshots renders disabled state correctly Athleta in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  margin: 0 0 1.5rem;
}

.emotion-0:hover {
  -webkit-text-decoration: underline;
  text-decoration: underline;
}

.emotion-1 {
  opacity: 0;
  position: absolute;
}

.emotion-1[data-focus-visible-added]~.fui_radio-button {
  outline: 0;
  box-shadow: 0px 0px 0 3px #5CABF7;
}

.emotion-2 {
  display: block;
  position: relative;
  float: left;
  margin-right: 0.625rem;
  width: 1.5rem;
  height: 1.5rem;
  border: 1px solid #CCC;
  border-radius: 100%;
}

.emotion-2::after {
  content: "";
  position: absolute;
  width: 0;
  height: 0;
}

.emotion-2::before {
  content: "";
  position: absolute;
  display: block;
  width: calc(100% + 1px);
  height: calc(100% + 1px);
  border-radius: 100%;
  left: -1px;
  top: -1px;
}

.emotion-3 {
  display: inline-block;
  vertical-align: middle;
  line-height: 1.5;
  color: #CCC;
  max-width: calc(100% - 2.3025rem);
  font-size: 1.1rem;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
}

<label
    class=" emotion-0"
    disabled=""
  >
    <input
      class="emotion-1"
      disabled=""
      id="radio-button"
      type="radio"
      value="selected"
    />
    <div
      class="fui_radio-button emotion-2"
      disabled=""
    />
    <span
      class="emotion-3"
    >
      radio button
    </span>
  </label>
</DocumentFragment>
`;

exports[`<Radio /> snapshots renders disabled state correctly BananaRepublic 1`] = `
<DocumentFragment>
  .emotion-0 {
  margin: 0 0 1.5rem;
}

.emotion-0:hover {
  -webkit-text-decoration: underline;
  text-decoration: underline;
}

.emotion-1 {
  opacity: 0;
  position: absolute;
}

.emotion-1[data-focus-visible-added]~.fui_radio-button {
  outline: 0;
  box-shadow: 0px 0px 0 3px #CCC;
}

.emotion-2 {
  display: block;
  position: relative;
  float: left;
  margin-right: 0.625rem;
  width: 1.5rem;
  height: 1.5rem;
  border: 1px solid #CCC;
  border-radius: 100%;
}

.emotion-2::after {
  content: "";
  position: absolute;
  width: 0;
  height: 0;
}

.emotion-2::before {
  content: "";
  position: absolute;
  display: block;
  width: calc(100% + 1px);
  height: calc(100% + 1px);
  border-radius: 100%;
  left: -1px;
  top: -1px;
}

.emotion-3 {
  display: inline-block;
  vertical-align: middle;
  line-height: 1.5;
  color: #CCC;
  max-width: calc(100% - 2.3025rem);
  font-size: 1rem;
  font-family: 'Lynstone',Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
}

<label
    class=" emotion-0"
    disabled=""
  >
    <input
      class="emotion-1"
      disabled=""
      id="radio-button"
      type="radio"
      value="selected"
    />
    <div
      class="fui_radio-button emotion-2"
      disabled=""
    />
    <span
      class="emotion-3"
    >
      radio button
    </span>
  </label>
</DocumentFragment>
`;

exports[`<Radio /> snapshots renders disabled state correctly BananaRepublic in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  margin: 0 0 1.5rem;
}

.emotion-0:hover {
  -webkit-text-decoration: underline;
  text-decoration: underline;
}

.emotion-1 {
  opacity: 0;
  position: absolute;
}

.emotion-1[data-focus-visible-added]~.fui_radio-button {
  outline: 0;
  box-shadow: 0px 0px 0 3px #5CABF7;
}

.emotion-2 {
  display: block;
  position: relative;
  float: left;
  margin-right: 0.625rem;
  width: 1.5rem;
  height: 1.5rem;
  border: 1px solid #CCC;
  border-radius: 100%;
}

.emotion-2::after {
  content: "";
  position: absolute;
  width: 0;
  height: 0;
}

.emotion-2::before {
  content: "";
  position: absolute;
  display: block;
  width: calc(100% + 1px);
  height: calc(100% + 1px);
  border-radius: 100%;
  left: -1px;
  top: -1px;
}

.emotion-3 {
  display: inline-block;
  vertical-align: middle;
  line-height: 1.5;
  color: #CCC;
  max-width: calc(100% - 2.3025rem);
  font-size: 1.1rem;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
}

<label
    class=" emotion-0"
    disabled=""
  >
    <input
      class="emotion-1"
      disabled=""
      id="radio-button"
      type="radio"
      value="selected"
    />
    <div
      class="fui_radio-button emotion-2"
      disabled=""
    />
    <span
      class="emotion-3"
    >
      radio button
    </span>
  </label>
</DocumentFragment>
`;

exports[`<Radio /> snapshots renders disabled state correctly BananaRepublicFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  margin: 0 0 1.5rem;
}

.emotion-0:hover {
  -webkit-text-decoration: underline;
  text-decoration: underline;
}

.emotion-1 {
  opacity: 0;
  position: absolute;
}

.emotion-1[data-focus-visible-added]~.fui_radio-button {
  outline: 0;
  box-shadow: 0px 0px 0 3px #CCC;
}

.emotion-2 {
  display: block;
  position: relative;
  float: left;
  margin-right: 0.625rem;
  width: 1.5rem;
  height: 1.5rem;
  border: 1px solid #CCC;
  border-radius: 100%;
}

.emotion-2::after {
  content: "";
  position: absolute;
  width: 0;
  height: 0;
}

.emotion-2::before {
  content: "";
  position: absolute;
  display: block;
  width: calc(100% + 1px);
  height: calc(100% + 1px);
  border-radius: 100%;
  left: -1px;
  top: -1px;
}

.emotion-3 {
  display: inline-block;
  vertical-align: middle;
  line-height: 1.5;
  color: #CCC;
  max-width: calc(100% - 2.3025rem);
  font-size: 1rem;
  font-family: 'Lynstone',Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
}

<label
    class=" emotion-0"
    disabled=""
  >
    <input
      class="emotion-1"
      disabled=""
      id="radio-button"
      type="radio"
      value="selected"
    />
    <div
      class="fui_radio-button emotion-2"
      disabled=""
    />
    <span
      class="emotion-3"
    >
      radio button
    </span>
  </label>
</DocumentFragment>
`;

exports[`<Radio /> snapshots renders disabled state correctly BananaRepublicFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  margin: 0 0 1.5rem;
}

.emotion-0:hover {
  -webkit-text-decoration: underline;
  text-decoration: underline;
}

.emotion-1 {
  opacity: 0;
  position: absolute;
}

.emotion-1[data-focus-visible-added]~.fui_radio-button {
  outline: 0;
  box-shadow: 0px 0px 0 3px #5CABF7;
}

.emotion-2 {
  display: block;
  position: relative;
  float: left;
  margin-right: 0.625rem;
  width: 1.5rem;
  height: 1.5rem;
  border: 1px solid #CCC;
  border-radius: 100%;
}

.emotion-2::after {
  content: "";
  position: absolute;
  width: 0;
  height: 0;
}

.emotion-2::before {
  content: "";
  position: absolute;
  display: block;
  width: calc(100% + 1px);
  height: calc(100% + 1px);
  border-radius: 100%;
  left: -1px;
  top: -1px;
}

.emotion-3 {
  display: inline-block;
  vertical-align: middle;
  line-height: 1.5;
  color: #CCC;
  max-width: calc(100% - 2.3025rem);
  font-size: 1.1rem;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
}

<label
    class=" emotion-0"
    disabled=""
  >
    <input
      class="emotion-1"
      disabled=""
      id="radio-button"
      type="radio"
      value="selected"
    />
    <div
      class="fui_radio-button emotion-2"
      disabled=""
    />
    <span
      class="emotion-3"
    >
      radio button
    </span>
  </label>
</DocumentFragment>
`;

exports[`<Radio /> snapshots renders disabled state correctly Gap 1`] = `
<DocumentFragment>
  .emotion-0 {
  margin: 0 0 1.5rem;
}

.emotion-0:hover {
  -webkit-text-decoration: underline;
  text-decoration: underline;
}

.emotion-1 {
  opacity: 0;
  position: absolute;
}

.emotion-1[data-focus-visible-added]~.fui_radio-button {
  outline: 0;
  box-shadow: 0px 0px 0 3px #5CABF7;
}

.emotion-2 {
  display: block;
  position: relative;
  float: left;
  margin-right: 0.625rem;
  width: 1.5rem;
  height: 1.5rem;
  border: 1px solid #CCC;
  border-radius: 100%;
}

.emotion-2::after {
  content: "";
  position: absolute;
  width: 0;
  height: 0;
}

.emotion-2::before {
  content: "";
  position: absolute;
  display: block;
  width: calc(100% + 1px);
  height: calc(100% + 1px);
  border-radius: 100%;
  left: -1px;
  top: -1px;
}

.emotion-3 {
  display: inline-block;
  vertical-align: middle;
  line-height: 1.5;
  color: #CCC;
  max-width: calc(100% - 2.3025rem);
  font-size: 1.063rem;
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
}

<label
    class=" emotion-0"
    disabled=""
  >
    <input
      class="emotion-1"
      disabled=""
      id="radio-button"
      type="radio"
      value="selected"
    />
    <div
      class="fui_radio-button emotion-2"
      disabled=""
    />
    <span
      class="emotion-3"
    >
      radio button
    </span>
  </label>
</DocumentFragment>
`;

exports[`<Radio /> snapshots renders disabled state correctly Gap in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  margin: 0 0 1.5rem;
}

.emotion-0:hover {
  -webkit-text-decoration: underline;
  text-decoration: underline;
}

.emotion-1 {
  opacity: 0;
  position: absolute;
}

.emotion-1[data-focus-visible-added]~.fui_radio-button {
  outline: 0;
  box-shadow: 0px 0px 0 3px #5CABF7;
}

.emotion-2 {
  display: block;
  position: relative;
  float: left;
  margin-right: 0.625rem;
  width: 1.5rem;
  height: 1.5rem;
  border: 1px solid #CCC;
  border-radius: 100%;
}

.emotion-2::after {
  content: "";
  position: absolute;
  width: 0;
  height: 0;
}

.emotion-2::before {
  content: "";
  position: absolute;
  display: block;
  width: calc(100% + 1px);
  height: calc(100% + 1px);
  border-radius: 100%;
  left: -1px;
  top: -1px;
}

.emotion-3 {
  display: inline-block;
  vertical-align: middle;
  line-height: 1.5;
  color: #CCC;
  max-width: calc(100% - 2.3025rem);
  font-size: 1.1rem;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
}

<label
    class=" emotion-0"
    disabled=""
  >
    <input
      class="emotion-1"
      disabled=""
      id="radio-button"
      type="radio"
      value="selected"
    />
    <div
      class="fui_radio-button emotion-2"
      disabled=""
    />
    <span
      class="emotion-3"
    >
      radio button
    </span>
  </label>
</DocumentFragment>
`;

exports[`<Radio /> snapshots renders disabled state correctly GapFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  margin: 0 0 1.5rem;
}

.emotion-0:hover {
  -webkit-text-decoration: underline;
  text-decoration: underline;
}

.emotion-1 {
  opacity: 0;
  position: absolute;
}

.emotion-1[data-focus-visible-added]~.fui_radio-button {
  outline: 0;
  box-shadow: 0px 0px 0 3px #5CABF7;
}

.emotion-2 {
  display: block;
  position: relative;
  float: left;
  margin-right: 0.625rem;
  width: 1.5rem;
  height: 1.5rem;
  border: 1px solid #CCC;
  border-radius: 100%;
}

.emotion-2::after {
  content: "";
  position: absolute;
  width: 0;
  height: 0;
}

.emotion-2::before {
  content: "";
  position: absolute;
  display: block;
  width: calc(100% + 1px);
  height: calc(100% + 1px);
  border-radius: 100%;
  left: -1px;
  top: -1px;
}

.emotion-3 {
  display: inline-block;
  vertical-align: middle;
  line-height: 1.5;
  color: #CCC;
  max-width: calc(100% - 2.3025rem);
  font-size: 1.063rem;
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
}

<label
    class=" emotion-0"
    disabled=""
  >
    <input
      class="emotion-1"
      disabled=""
      id="radio-button"
      type="radio"
      value="selected"
    />
    <div
      class="fui_radio-button emotion-2"
      disabled=""
    />
    <span
      class="emotion-3"
    >
      radio button
    </span>
  </label>
</DocumentFragment>
`;

exports[`<Radio /> snapshots renders disabled state correctly GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  margin: 0 0 1.5rem;
}

.emotion-0:hover {
  -webkit-text-decoration: underline;
  text-decoration: underline;
}

.emotion-1 {
  opacity: 0;
  position: absolute;
}

.emotion-1[data-focus-visible-added]~.fui_radio-button {
  outline: 0;
  box-shadow: 0px 0px 0 3px #5CABF7;
}

.emotion-2 {
  display: block;
  position: relative;
  float: left;
  margin-right: 0.625rem;
  width: 1.5rem;
  height: 1.5rem;
  border: 1px solid #CCC;
  border-radius: 100%;
}

.emotion-2::after {
  content: "";
  position: absolute;
  width: 0;
  height: 0;
}

.emotion-2::before {
  content: "";
  position: absolute;
  display: block;
  width: calc(100% + 1px);
  height: calc(100% + 1px);
  border-radius: 100%;
  left: -1px;
  top: -1px;
}

.emotion-3 {
  display: inline-block;
  vertical-align: middle;
  line-height: 1.5;
  color: #CCC;
  max-width: calc(100% - 2.3025rem);
  font-size: 1.1rem;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
}

<label
    class=" emotion-0"
    disabled=""
  >
    <input
      class="emotion-1"
      disabled=""
      id="radio-button"
      type="radio"
      value="selected"
    />
    <div
      class="fui_radio-button emotion-2"
      disabled=""
    />
    <span
      class="emotion-3"
    >
      radio button
    </span>
  </label>
</DocumentFragment>
`;

exports[`<Radio /> snapshots renders disabled state correctly OldNavy 1`] = `
<DocumentFragment>
  .emotion-0 {
  margin: 0 0 1.5rem;
}

.emotion-0:hover {
  -webkit-text-decoration: underline;
  text-decoration: underline;
}

.emotion-1 {
  opacity: 0;
  position: absolute;
}

.emotion-1[data-focus-visible-added]~.fui_radio-button {
  outline: 0;
  box-shadow: 0px 0px 0 3px #5CABF7;
}

.emotion-2 {
  display: block;
  position: relative;
  float: left;
  margin-right: 0.625rem;
  width: 1.5rem;
  height: 1.5rem;
  border: 1px solid #CCC;
  border-radius: 100%;
}

.emotion-2::after {
  content: "";
  position: absolute;
  width: 0;
  height: 0;
}

.emotion-2::before {
  content: "";
  position: absolute;
  display: block;
  width: calc(100% + 1px);
  height: calc(100% + 1px);
  border-radius: 100%;
  left: -1px;
  top: -1px;
}

.emotion-3 {
  display: inline-block;
  vertical-align: middle;
  line-height: 1.5;
  color: #CCC;
  max-width: calc(100% - 2.3025rem);
  font-size: 1rem;
  font-family: 'Gotham','Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
}

<label
    class=" emotion-0"
    disabled=""
  >
    <input
      class="emotion-1"
      disabled=""
      id="radio-button"
      type="radio"
      value="selected"
    />
    <div
      class="fui_radio-button emotion-2"
      disabled=""
    />
    <span
      class="emotion-3"
    >
      radio button
    </span>
  </label>
</DocumentFragment>
`;

exports[`<Radio /> snapshots renders disabled state correctly OldNavy in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  margin: 0 0 1.5rem;
}

.emotion-0:hover {
  -webkit-text-decoration: underline;
  text-decoration: underline;
}

.emotion-1 {
  opacity: 0;
  position: absolute;
}

.emotion-1[data-focus-visible-added]~.fui_radio-button {
  outline: 0;
  box-shadow: 0px 0px 0 3px #5CABF7;
}

.emotion-2 {
  display: block;
  position: relative;
  float: left;
  margin-right: 0.625rem;
  width: 1.5rem;
  height: 1.5rem;
  border: 1px solid #CCC;
  border-radius: 100%;
}

.emotion-2::after {
  content: "";
  position: absolute;
  width: 0;
  height: 0;
}

.emotion-2::before {
  content: "";
  position: absolute;
  display: block;
  width: calc(100% + 1px);
  height: calc(100% + 1px);
  border-radius: 100%;
  left: -1px;
  top: -1px;
}

.emotion-3 {
  display: inline-block;
  vertical-align: middle;
  line-height: 1.5;
  color: #CCC;
  max-width: calc(100% - 2.3025rem);
  font-size: 1.1rem;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
}

<label
    class=" emotion-0"
    disabled=""
  >
    <input
      class="emotion-1"
      disabled=""
      id="radio-button"
      type="radio"
      value="selected"
    />
    <div
      class="fui_radio-button emotion-2"
      disabled=""
    />
    <span
      class="emotion-3"
    >
      radio button
    </span>
  </label>
</DocumentFragment>
`;

exports[`<Radio /> snapshots renders with help text state correctly Athleta 1`] = `
<DocumentFragment>
  .emotion-0 {
  margin: 0 0 1.5rem;
}

.emotion-0:hover {
  -webkit-text-decoration: underline;
  text-decoration: underline;
}

.emotion-1 {
  opacity: 0;
  position: absolute;
}

.emotion-1[data-focus-visible-added]~.fui_radio-button {
  outline: 0;
  box-shadow: 0px 0px 0 3px #5CABF7;
}

.emotion-2 {
  display: block;
  position: relative;
  float: left;
  margin-right: 0.625rem;
  width: 1.5rem;
  height: 1.5rem;
  border: 1px solid #666;
  border-radius: 100%;
}

.emotion-2::after {
  content: "";
  position: absolute;
  width: 0;
  height: 0;
}

.emotion-2::before {
  content: "";
  position: absolute;
  display: block;
  width: calc(100% + 1px);
  height: calc(100% + 1px);
  border-radius: 100%;
  left: -1px;
  top: -1px;
}

.emotion-3 {
  display: inline-block;
  vertical-align: middle;
  line-height: 1.5;
  color: #666;
  max-width: calc(100% - 2.3025rem);
  font-size: 1.063rem;
  font-family: 'Avenir Next','Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
}

.emotion-4 {
  display: block;
  font-size: 0.8125rem;
  line-height: 1.2;
  margin-top: 0.25rem;
  color: #333;
  font-family: 'Avenir Next','Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
}

<label
    class=" emotion-0"
  >
    <input
      class="emotion-1"
      id="radio-button"
      type="radio"
      value="selected"
    />
    <div
      class="fui_radio-button emotion-2"
    />
    <span
      class="emotion-3"
    >
      radio button
      <div
        class="emotion-4 emotion-5"
      >
        Help text
      </div>
    </span>
  </label>
</DocumentFragment>
`;

exports[`<Radio /> snapshots renders with help text state correctly Athleta in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  margin: 0 0 1.5rem;
}

.emotion-0:hover {
  -webkit-text-decoration: underline;
  text-decoration: underline;
}

.emotion-1 {
  opacity: 0;
  position: absolute;
}

.emotion-1[data-focus-visible-added]~.fui_radio-button {
  outline: 0;
  box-shadow: 0px 0px 0 3px #5CABF7;
}

.emotion-2 {
  display: block;
  position: relative;
  float: left;
  margin-right: 0.625rem;
  width: 1.5rem;
  height: 1.5rem;
  border: 1px solid #666;
  border-radius: 100%;
}

.emotion-2::after {
  content: "";
  position: absolute;
  width: 0;
  height: 0;
}

.emotion-2::before {
  content: "";
  position: absolute;
  display: block;
  width: calc(100% + 1px);
  height: calc(100% + 1px);
  border-radius: 100%;
  left: -1px;
  top: -1px;
}

.emotion-3 {
  display: inline-block;
  vertical-align: middle;
  line-height: 1.5;
  color: #666;
  max-width: calc(100% - 2.3025rem);
  font-size: 1.1rem;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
}

.emotion-4 {
  display: block;
  font-size: 0.8125rem;
  line-height: 1.2;
  margin-top: 0.25rem;
  color: #333;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
}

<label
    class=" emotion-0"
  >
    <input
      class="emotion-1"
      id="radio-button"
      type="radio"
      value="selected"
    />
    <div
      class="fui_radio-button emotion-2"
    />
    <span
      class="emotion-3"
    >
      radio button
      <div
        class="emotion-4 emotion-5"
      >
        Help text
      </div>
    </span>
  </label>
</DocumentFragment>
`;

exports[`<Radio /> snapshots renders with help text state correctly BananaRepublic 1`] = `
<DocumentFragment>
  .emotion-0 {
  margin: 0 0 1.5rem;
}

.emotion-0:hover {
  -webkit-text-decoration: underline;
  text-decoration: underline;
}

.emotion-1 {
  opacity: 0;
  position: absolute;
}

.emotion-1[data-focus-visible-added]~.fui_radio-button {
  outline: 0;
  box-shadow: 0px 0px 0 3px #CCC;
}

.emotion-2 {
  display: block;
  position: relative;
  float: left;
  margin-right: 0.625rem;
  width: 1.5rem;
  height: 1.5rem;
  border: 1px solid #666;
  border-radius: 100%;
}

.emotion-2::after {
  content: "";
  position: absolute;
  width: 0;
  height: 0;
}

.emotion-2::before {
  content: "";
  position: absolute;
  display: block;
  width: calc(100% + 1px);
  height: calc(100% + 1px);
  border-radius: 100%;
  left: -1px;
  top: -1px;
}

.emotion-3 {
  display: inline-block;
  vertical-align: middle;
  line-height: 1.5;
  color: #666;
  max-width: calc(100% - 2.3025rem);
  font-size: 1rem;
  font-family: 'Lynstone',Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
}

.emotion-4 {
  display: block;
  font-size: 0.75rem;
  line-height: 1.2;
  margin-top: 0.25rem;
  color: #000;
  font-family: 'Lynstone',Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
}

<label
    class=" emotion-0"
  >
    <input
      class="emotion-1"
      id="radio-button"
      type="radio"
      value="selected"
    />
    <div
      class="fui_radio-button emotion-2"
    />
    <span
      class="emotion-3"
    >
      radio button
      <div
        class="emotion-4 emotion-5"
      >
        Help text
      </div>
    </span>
  </label>
</DocumentFragment>
`;

exports[`<Radio /> snapshots renders with help text state correctly BananaRepublic in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  margin: 0 0 1.5rem;
}

.emotion-0:hover {
  -webkit-text-decoration: underline;
  text-decoration: underline;
}

.emotion-1 {
  opacity: 0;
  position: absolute;
}

.emotion-1[data-focus-visible-added]~.fui_radio-button {
  outline: 0;
  box-shadow: 0px 0px 0 3px #5CABF7;
}

.emotion-2 {
  display: block;
  position: relative;
  float: left;
  margin-right: 0.625rem;
  width: 1.5rem;
  height: 1.5rem;
  border: 1px solid #666;
  border-radius: 100%;
}

.emotion-2::after {
  content: "";
  position: absolute;
  width: 0;
  height: 0;
}

.emotion-2::before {
  content: "";
  position: absolute;
  display: block;
  width: calc(100% + 1px);
  height: calc(100% + 1px);
  border-radius: 100%;
  left: -1px;
  top: -1px;
}

.emotion-3 {
  display: inline-block;
  vertical-align: middle;
  line-height: 1.5;
  color: #666;
  max-width: calc(100% - 2.3025rem);
  font-size: 1.1rem;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
}

.emotion-4 {
  display: block;
  font-size: 0.8125rem;
  line-height: 1.2;
  margin-top: 0.25rem;
  color: #333;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
}

<label
    class=" emotion-0"
  >
    <input
      class="emotion-1"
      id="radio-button"
      type="radio"
      value="selected"
    />
    <div
      class="fui_radio-button emotion-2"
    />
    <span
      class="emotion-3"
    >
      radio button
      <div
        class="emotion-4 emotion-5"
      >
        Help text
      </div>
    </span>
  </label>
</DocumentFragment>
`;

exports[`<Radio /> snapshots renders with help text state correctly BananaRepublicFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  margin: 0 0 1.5rem;
}

.emotion-0:hover {
  -webkit-text-decoration: underline;
  text-decoration: underline;
}

.emotion-1 {
  opacity: 0;
  position: absolute;
}

.emotion-1[data-focus-visible-added]~.fui_radio-button {
  outline: 0;
  box-shadow: 0px 0px 0 3px #CCC;
}

.emotion-2 {
  display: block;
  position: relative;
  float: left;
  margin-right: 0.625rem;
  width: 1.5rem;
  height: 1.5rem;
  border: 1px solid #666;
  border-radius: 100%;
}

.emotion-2::after {
  content: "";
  position: absolute;
  width: 0;
  height: 0;
}

.emotion-2::before {
  content: "";
  position: absolute;
  display: block;
  width: calc(100% + 1px);
  height: calc(100% + 1px);
  border-radius: 100%;
  left: -1px;
  top: -1px;
}

.emotion-3 {
  display: inline-block;
  vertical-align: middle;
  line-height: 1.5;
  color: #666;
  max-width: calc(100% - 2.3025rem);
  font-size: 1rem;
  font-family: 'Lynstone',Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
}

.emotion-4 {
  display: block;
  font-size: 0.75rem;
  line-height: 1.2;
  margin-top: 0.25rem;
  color: #333;
  font-family: 'Lynstone',Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
}

<label
    class=" emotion-0"
  >
    <input
      class="emotion-1"
      id="radio-button"
      type="radio"
      value="selected"
    />
    <div
      class="fui_radio-button emotion-2"
    />
    <span
      class="emotion-3"
    >
      radio button
      <div
        class="emotion-4 emotion-5"
      >
        Help text
      </div>
    </span>
  </label>
</DocumentFragment>
`;

exports[`<Radio /> snapshots renders with help text state correctly BananaRepublicFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  margin: 0 0 1.5rem;
}

.emotion-0:hover {
  -webkit-text-decoration: underline;
  text-decoration: underline;
}

.emotion-1 {
  opacity: 0;
  position: absolute;
}

.emotion-1[data-focus-visible-added]~.fui_radio-button {
  outline: 0;
  box-shadow: 0px 0px 0 3px #5CABF7;
}

.emotion-2 {
  display: block;
  position: relative;
  float: left;
  margin-right: 0.625rem;
  width: 1.5rem;
  height: 1.5rem;
  border: 1px solid #666;
  border-radius: 100%;
}

.emotion-2::after {
  content: "";
  position: absolute;
  width: 0;
  height: 0;
}

.emotion-2::before {
  content: "";
  position: absolute;
  display: block;
  width: calc(100% + 1px);
  height: calc(100% + 1px);
  border-radius: 100%;
  left: -1px;
  top: -1px;
}

.emotion-3 {
  display: inline-block;
  vertical-align: middle;
  line-height: 1.5;
  color: #666;
  max-width: calc(100% - 2.3025rem);
  font-size: 1.1rem;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
}

.emotion-4 {
  display: block;
  font-size: 0.8125rem;
  line-height: 1.2;
  margin-top: 0.25rem;
  color: #333;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
}

<label
    class=" emotion-0"
  >
    <input
      class="emotion-1"
      id="radio-button"
      type="radio"
      value="selected"
    />
    <div
      class="fui_radio-button emotion-2"
    />
    <span
      class="emotion-3"
    >
      radio button
      <div
        class="emotion-4 emotion-5"
      >
        Help text
      </div>
    </span>
  </label>
</DocumentFragment>
`;

exports[`<Radio /> snapshots renders with help text state correctly Gap 1`] = `
<DocumentFragment>
  .emotion-0 {
  margin: 0 0 1.5rem;
}

.emotion-0:hover {
  -webkit-text-decoration: underline;
  text-decoration: underline;
}

.emotion-1 {
  opacity: 0;
  position: absolute;
}

.emotion-1[data-focus-visible-added]~.fui_radio-button {
  outline: 0;
  box-shadow: 0px 0px 0 3px #5CABF7;
}

.emotion-2 {
  display: block;
  position: relative;
  float: left;
  margin-right: 0.625rem;
  width: 1.5rem;
  height: 1.5rem;
  border: 1px solid #666;
  border-radius: 100%;
}

.emotion-2::after {
  content: "";
  position: absolute;
  width: 0;
  height: 0;
}

.emotion-2::before {
  content: "";
  position: absolute;
  display: block;
  width: calc(100% + 1px);
  height: calc(100% + 1px);
  border-radius: 100%;
  left: -1px;
  top: -1px;
}

.emotion-3 {
  display: inline-block;
  vertical-align: middle;
  line-height: 1.5;
  color: #666;
  max-width: calc(100% - 2.3025rem);
  font-size: 1.063rem;
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
}

.emotion-4 {
  display: block;
  font-size: 0.75rem;
  line-height: 1.2;
  margin-top: 0.25rem;
  color: #333;
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
}

<label
    class=" emotion-0"
  >
    <input
      class="emotion-1"
      id="radio-button"
      type="radio"
      value="selected"
    />
    <div
      class="fui_radio-button emotion-2"
    />
    <span
      class="emotion-3"
    >
      radio button
      <div
        class="emotion-4 emotion-5"
      >
        Help text
      </div>
    </span>
  </label>
</DocumentFragment>
`;

exports[`<Radio /> snapshots renders with help text state correctly Gap in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  margin: 0 0 1.5rem;
}

.emotion-0:hover {
  -webkit-text-decoration: underline;
  text-decoration: underline;
}

.emotion-1 {
  opacity: 0;
  position: absolute;
}

.emotion-1[data-focus-visible-added]~.fui_radio-button {
  outline: 0;
  box-shadow: 0px 0px 0 3px #5CABF7;
}

.emotion-2 {
  display: block;
  position: relative;
  float: left;
  margin-right: 0.625rem;
  width: 1.5rem;
  height: 1.5rem;
  border: 1px solid #666;
  border-radius: 100%;
}

.emotion-2::after {
  content: "";
  position: absolute;
  width: 0;
  height: 0;
}

.emotion-2::before {
  content: "";
  position: absolute;
  display: block;
  width: calc(100% + 1px);
  height: calc(100% + 1px);
  border-radius: 100%;
  left: -1px;
  top: -1px;
}

.emotion-3 {
  display: inline-block;
  vertical-align: middle;
  line-height: 1.5;
  color: #666;
  max-width: calc(100% - 2.3025rem);
  font-size: 1.1rem;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
}

.emotion-4 {
  display: block;
  font-size: 0.8125rem;
  line-height: 1.2;
  margin-top: 0.25rem;
  color: #333;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
}

<label
    class=" emotion-0"
  >
    <input
      class="emotion-1"
      id="radio-button"
      type="radio"
      value="selected"
    />
    <div
      class="fui_radio-button emotion-2"
    />
    <span
      class="emotion-3"
    >
      radio button
      <div
        class="emotion-4 emotion-5"
      >
        Help text
      </div>
    </span>
  </label>
</DocumentFragment>
`;

exports[`<Radio /> snapshots renders with help text state correctly GapFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  margin: 0 0 1.5rem;
}

.emotion-0:hover {
  -webkit-text-decoration: underline;
  text-decoration: underline;
}

.emotion-1 {
  opacity: 0;
  position: absolute;
}

.emotion-1[data-focus-visible-added]~.fui_radio-button {
  outline: 0;
  box-shadow: 0px 0px 0 3px #5CABF7;
}

.emotion-2 {
  display: block;
  position: relative;
  float: left;
  margin-right: 0.625rem;
  width: 1.5rem;
  height: 1.5rem;
  border: 1px solid #666;
  border-radius: 100%;
}

.emotion-2::after {
  content: "";
  position: absolute;
  width: 0;
  height: 0;
}

.emotion-2::before {
  content: "";
  position: absolute;
  display: block;
  width: calc(100% + 1px);
  height: calc(100% + 1px);
  border-radius: 100%;
  left: -1px;
  top: -1px;
}

.emotion-3 {
  display: inline-block;
  vertical-align: middle;
  line-height: 1.5;
  color: #666;
  max-width: calc(100% - 2.3025rem);
  font-size: 1.063rem;
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
}

.emotion-4 {
  display: block;
  font-size: 0.75rem;
  line-height: 1.2;
  margin-top: 0.25rem;
  color: #333;
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
}

<label
    class=" emotion-0"
  >
    <input
      class="emotion-1"
      id="radio-button"
      type="radio"
      value="selected"
    />
    <div
      class="fui_radio-button emotion-2"
    />
    <span
      class="emotion-3"
    >
      radio button
      <div
        class="emotion-4 emotion-5"
      >
        Help text
      </div>
    </span>
  </label>
</DocumentFragment>
`;

exports[`<Radio /> snapshots renders with help text state correctly GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  margin: 0 0 1.5rem;
}

.emotion-0:hover {
  -webkit-text-decoration: underline;
  text-decoration: underline;
}

.emotion-1 {
  opacity: 0;
  position: absolute;
}

.emotion-1[data-focus-visible-added]~.fui_radio-button {
  outline: 0;
  box-shadow: 0px 0px 0 3px #5CABF7;
}

.emotion-2 {
  display: block;
  position: relative;
  float: left;
  margin-right: 0.625rem;
  width: 1.5rem;
  height: 1.5rem;
  border: 1px solid #666;
  border-radius: 100%;
}

.emotion-2::after {
  content: "";
  position: absolute;
  width: 0;
  height: 0;
}

.emotion-2::before {
  content: "";
  position: absolute;
  display: block;
  width: calc(100% + 1px);
  height: calc(100% + 1px);
  border-radius: 100%;
  left: -1px;
  top: -1px;
}

.emotion-3 {
  display: inline-block;
  vertical-align: middle;
  line-height: 1.5;
  color: #666;
  max-width: calc(100% - 2.3025rem);
  font-size: 1.1rem;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
}

.emotion-4 {
  display: block;
  font-size: 0.8125rem;
  line-height: 1.2;
  margin-top: 0.25rem;
  color: #333;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
}

<label
    class=" emotion-0"
  >
    <input
      class="emotion-1"
      id="radio-button"
      type="radio"
      value="selected"
    />
    <div
      class="fui_radio-button emotion-2"
    />
    <span
      class="emotion-3"
    >
      radio button
      <div
        class="emotion-4 emotion-5"
      >
        Help text
      </div>
    </span>
  </label>
</DocumentFragment>
`;

exports[`<Radio /> snapshots renders with help text state correctly OldNavy 1`] = `
<DocumentFragment>
  .emotion-0 {
  margin: 0 0 1.5rem;
}

.emotion-0:hover {
  -webkit-text-decoration: underline;
  text-decoration: underline;
}

.emotion-1 {
  opacity: 0;
  position: absolute;
}

.emotion-1[data-focus-visible-added]~.fui_radio-button {
  outline: 0;
  box-shadow: 0px 0px 0 3px #5CABF7;
}

.emotion-2 {
  display: block;
  position: relative;
  float: left;
  margin-right: 0.625rem;
  width: 1.5rem;
  height: 1.5rem;
  border: 1px solid #666;
  border-radius: 100%;
}

.emotion-2::after {
  content: "";
  position: absolute;
  width: 0;
  height: 0;
}

.emotion-2::before {
  content: "";
  position: absolute;
  display: block;
  width: calc(100% + 1px);
  height: calc(100% + 1px);
  border-radius: 100%;
  left: -1px;
  top: -1px;
}

.emotion-3 {
  display: inline-block;
  vertical-align: middle;
  line-height: 1.5;
  color: #666;
  max-width: calc(100% - 2.3025rem);
  font-size: 1rem;
  font-family: 'Gotham','Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
}

.emotion-4 {
  display: block;
  font-size: 0.8125rem;
  line-height: 1.2;
  margin-top: 0.25rem;
  color: #000;
  font-family: 'Gotham','Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
}

<label
    class=" emotion-0"
  >
    <input
      class="emotion-1"
      id="radio-button"
      type="radio"
      value="selected"
    />
    <div
      class="fui_radio-button emotion-2"
    />
    <span
      class="emotion-3"
    >
      radio button
      <div
        class="emotion-4 emotion-5"
      >
        Help text
      </div>
    </span>
  </label>
</DocumentFragment>
`;

exports[`<Radio /> snapshots renders with help text state correctly OldNavy in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  margin: 0 0 1.5rem;
}

.emotion-0:hover {
  -webkit-text-decoration: underline;
  text-decoration: underline;
}

.emotion-1 {
  opacity: 0;
  position: absolute;
}

.emotion-1[data-focus-visible-added]~.fui_radio-button {
  outline: 0;
  box-shadow: 0px 0px 0 3px #5CABF7;
}

.emotion-2 {
  display: block;
  position: relative;
  float: left;
  margin-right: 0.625rem;
  width: 1.5rem;
  height: 1.5rem;
  border: 1px solid #666;
  border-radius: 100%;
}

.emotion-2::after {
  content: "";
  position: absolute;
  width: 0;
  height: 0;
}

.emotion-2::before {
  content: "";
  position: absolute;
  display: block;
  width: calc(100% + 1px);
  height: calc(100% + 1px);
  border-radius: 100%;
  left: -1px;
  top: -1px;
}

.emotion-3 {
  display: inline-block;
  vertical-align: middle;
  line-height: 1.5;
  color: #666;
  max-width: calc(100% - 2.3025rem);
  font-size: 1.1rem;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
}

.emotion-4 {
  display: block;
  font-size: 0.8125rem;
  line-height: 1.2;
  margin-top: 0.25rem;
  color: #333;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
}

<label
    class=" emotion-0"
  >
    <input
      class="emotion-1"
      id="radio-button"
      type="radio"
      value="selected"
    />
    <div
      class="fui_radio-button emotion-2"
    />
    <span
      class="emotion-3"
    >
      radio button
      <div
        class="emotion-4 emotion-5"
      >
        Help text
      </div>
    </span>
  </label>
</DocumentFragment>
`;

exports[`<Radio /> snapshots renders with small background size state correctly Athleta 1`] = `
<DocumentFragment>
  .emotion-0 {
  margin: 0 0 1.5rem;
}

.emotion-0:hover {
  -webkit-text-decoration: underline;
  text-decoration: underline;
}

.emotion-1 {
  opacity: 0;
  position: absolute;
}

.emotion-1[data-focus-visible-added]~.fui_radio-button {
  outline: 0;
  box-shadow: 0px 0px 0 3px #5CABF7;
}

.emotion-2 {
  display: block;
  position: relative;
  float: left;
  margin-right: 0.625rem;
  width: 1.5rem;
  height: 1.5rem;
  border: 1px solid #666;
  border-radius: 100%;
}

.emotion-2::after {
  content: "";
  position: absolute;
  width: 0;
  height: 0;
}

.emotion-2::before {
  content: "";
  position: absolute;
  display: block;
  width: calc(100% - 10px);
  height: calc(100% - 10px);
  border-radius: 100%;
  left: 4px;
  top: 4px;
}

.emotion-3 {
  display: inline-block;
  vertical-align: middle;
  line-height: 1.5;
  color: #666;
  max-width: calc(100% - 2.3025rem);
  font-size: 1.063rem;
  font-family: 'Avenir Next','Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
}

<label
    class=" emotion-0"
  >
    <input
      class="emotion-1"
      id="radio-button"
      type="radio"
      value="selected"
    />
    <div
      class="fui_radio-button emotion-2"
    />
    <span
      class="emotion-3"
    >
      radio button
    </span>
  </label>
</DocumentFragment>
`;

exports[`<Radio /> snapshots renders with small background size state correctly Athleta in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  margin: 0 0 1.5rem;
}

.emotion-0:hover {
  -webkit-text-decoration: underline;
  text-decoration: underline;
}

.emotion-1 {
  opacity: 0;
  position: absolute;
}

.emotion-1[data-focus-visible-added]~.fui_radio-button {
  outline: 0;
  box-shadow: 0px 0px 0 3px #5CABF7;
}

.emotion-2 {
  display: block;
  position: relative;
  float: left;
  margin-right: 0.625rem;
  width: 1.5rem;
  height: 1.5rem;
  border: 1px solid #666;
  border-radius: 100%;
}

.emotion-2::after {
  content: "";
  position: absolute;
  width: 0;
  height: 0;
}

.emotion-2::before {
  content: "";
  position: absolute;
  display: block;
  width: calc(100% - 10px);
  height: calc(100% - 10px);
  border-radius: 100%;
  left: 4px;
  top: 4px;
}

.emotion-3 {
  display: inline-block;
  vertical-align: middle;
  line-height: 1.5;
  color: #666;
  max-width: calc(100% - 2.3025rem);
  font-size: 1.1rem;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
}

<label
    class=" emotion-0"
  >
    <input
      class="emotion-1"
      id="radio-button"
      type="radio"
      value="selected"
    />
    <div
      class="fui_radio-button emotion-2"
    />
    <span
      class="emotion-3"
    >
      radio button
    </span>
  </label>
</DocumentFragment>
`;

exports[`<Radio /> snapshots renders with small background size state correctly BananaRepublic 1`] = `
<DocumentFragment>
  .emotion-0 {
  margin: 0 0 1.5rem;
}

.emotion-0:hover {
  -webkit-text-decoration: underline;
  text-decoration: underline;
}

.emotion-1 {
  opacity: 0;
  position: absolute;
}

.emotion-1[data-focus-visible-added]~.fui_radio-button {
  outline: 0;
  box-shadow: 0px 0px 0 3px #CCC;
}

.emotion-2 {
  display: block;
  position: relative;
  float: left;
  margin-right: 0.625rem;
  width: 1.5rem;
  height: 1.5rem;
  border: 1px solid #666;
  border-radius: 100%;
}

.emotion-2::after {
  content: "";
  position: absolute;
  width: 0;
  height: 0;
}

.emotion-2::before {
  content: "";
  position: absolute;
  display: block;
  width: calc(100% - 10px);
  height: calc(100% - 10px);
  border-radius: 100%;
  left: 4px;
  top: 4px;
}

.emotion-3 {
  display: inline-block;
  vertical-align: middle;
  line-height: 1.5;
  color: #666;
  max-width: calc(100% - 2.3025rem);
  font-size: 1rem;
  font-family: 'Lynstone',Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
}

<label
    class=" emotion-0"
  >
    <input
      class="emotion-1"
      id="radio-button"
      type="radio"
      value="selected"
    />
    <div
      class="fui_radio-button emotion-2"
    />
    <span
      class="emotion-3"
    >
      radio button
    </span>
  </label>
</DocumentFragment>
`;

exports[`<Radio /> snapshots renders with small background size state correctly BananaRepublic in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  margin: 0 0 1.5rem;
}

.emotion-0:hover {
  -webkit-text-decoration: underline;
  text-decoration: underline;
}

.emotion-1 {
  opacity: 0;
  position: absolute;
}

.emotion-1[data-focus-visible-added]~.fui_radio-button {
  outline: 0;
  box-shadow: 0px 0px 0 3px #5CABF7;
}

.emotion-2 {
  display: block;
  position: relative;
  float: left;
  margin-right: 0.625rem;
  width: 1.5rem;
  height: 1.5rem;
  border: 1px solid #666;
  border-radius: 100%;
}

.emotion-2::after {
  content: "";
  position: absolute;
  width: 0;
  height: 0;
}

.emotion-2::before {
  content: "";
  position: absolute;
  display: block;
  width: calc(100% - 10px);
  height: calc(100% - 10px);
  border-radius: 100%;
  left: 4px;
  top: 4px;
}

.emotion-3 {
  display: inline-block;
  vertical-align: middle;
  line-height: 1.5;
  color: #666;
  max-width: calc(100% - 2.3025rem);
  font-size: 1.1rem;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
}

<label
    class=" emotion-0"
  >
    <input
      class="emotion-1"
      id="radio-button"
      type="radio"
      value="selected"
    />
    <div
      class="fui_radio-button emotion-2"
    />
    <span
      class="emotion-3"
    >
      radio button
    </span>
  </label>
</DocumentFragment>
`;

exports[`<Radio /> snapshots renders with small background size state correctly BananaRepublicFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  margin: 0 0 1.5rem;
}

.emotion-0:hover {
  -webkit-text-decoration: underline;
  text-decoration: underline;
}

.emotion-1 {
  opacity: 0;
  position: absolute;
}

.emotion-1[data-focus-visible-added]~.fui_radio-button {
  outline: 0;
  box-shadow: 0px 0px 0 3px #CCC;
}

.emotion-2 {
  display: block;
  position: relative;
  float: left;
  margin-right: 0.625rem;
  width: 1.5rem;
  height: 1.5rem;
  border: 1px solid #666;
  border-radius: 100%;
}

.emotion-2::after {
  content: "";
  position: absolute;
  width: 0;
  height: 0;
}

.emotion-2::before {
  content: "";
  position: absolute;
  display: block;
  width: calc(100% - 10px);
  height: calc(100% - 10px);
  border-radius: 100%;
  left: 4px;
  top: 4px;
}

.emotion-3 {
  display: inline-block;
  vertical-align: middle;
  line-height: 1.5;
  color: #666;
  max-width: calc(100% - 2.3025rem);
  font-size: 1rem;
  font-family: 'Lynstone',Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
}

<label
    class=" emotion-0"
  >
    <input
      class="emotion-1"
      id="radio-button"
      type="radio"
      value="selected"
    />
    <div
      class="fui_radio-button emotion-2"
    />
    <span
      class="emotion-3"
    >
      radio button
    </span>
  </label>
</DocumentFragment>
`;

exports[`<Radio /> snapshots renders with small background size state correctly BananaRepublicFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  margin: 0 0 1.5rem;
}

.emotion-0:hover {
  -webkit-text-decoration: underline;
  text-decoration: underline;
}

.emotion-1 {
  opacity: 0;
  position: absolute;
}

.emotion-1[data-focus-visible-added]~.fui_radio-button {
  outline: 0;
  box-shadow: 0px 0px 0 3px #5CABF7;
}

.emotion-2 {
  display: block;
  position: relative;
  float: left;
  margin-right: 0.625rem;
  width: 1.5rem;
  height: 1.5rem;
  border: 1px solid #666;
  border-radius: 100%;
}

.emotion-2::after {
  content: "";
  position: absolute;
  width: 0;
  height: 0;
}

.emotion-2::before {
  content: "";
  position: absolute;
  display: block;
  width: calc(100% - 10px);
  height: calc(100% - 10px);
  border-radius: 100%;
  left: 4px;
  top: 4px;
}

.emotion-3 {
  display: inline-block;
  vertical-align: middle;
  line-height: 1.5;
  color: #666;
  max-width: calc(100% - 2.3025rem);
  font-size: 1.1rem;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
}

<label
    class=" emotion-0"
  >
    <input
      class="emotion-1"
      id="radio-button"
      type="radio"
      value="selected"
    />
    <div
      class="fui_radio-button emotion-2"
    />
    <span
      class="emotion-3"
    >
      radio button
    </span>
  </label>
</DocumentFragment>
`;

exports[`<Radio /> snapshots renders with small background size state correctly Gap 1`] = `
<DocumentFragment>
  .emotion-0 {
  margin: 0 0 1.5rem;
}

.emotion-0:hover {
  -webkit-text-decoration: underline;
  text-decoration: underline;
}

.emotion-1 {
  opacity: 0;
  position: absolute;
}

.emotion-1[data-focus-visible-added]~.fui_radio-button {
  outline: 0;
  box-shadow: 0px 0px 0 3px #5CABF7;
}

.emotion-2 {
  display: block;
  position: relative;
  float: left;
  margin-right: 0.625rem;
  width: 1.5rem;
  height: 1.5rem;
  border: 1px solid #666;
  border-radius: 100%;
}

.emotion-2::after {
  content: "";
  position: absolute;
  width: 0;
  height: 0;
}

.emotion-2::before {
  content: "";
  position: absolute;
  display: block;
  width: calc(100% - 10px);
  height: calc(100% - 10px);
  border-radius: 100%;
  left: 4px;
  top: 4px;
}

.emotion-3 {
  display: inline-block;
  vertical-align: middle;
  line-height: 1.5;
  color: #666;
  max-width: calc(100% - 2.3025rem);
  font-size: 1.063rem;
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
}

<label
    class=" emotion-0"
  >
    <input
      class="emotion-1"
      id="radio-button"
      type="radio"
      value="selected"
    />
    <div
      class="fui_radio-button emotion-2"
    />
    <span
      class="emotion-3"
    >
      radio button
    </span>
  </label>
</DocumentFragment>
`;

exports[`<Radio /> snapshots renders with small background size state correctly Gap in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  margin: 0 0 1.5rem;
}

.emotion-0:hover {
  -webkit-text-decoration: underline;
  text-decoration: underline;
}

.emotion-1 {
  opacity: 0;
  position: absolute;
}

.emotion-1[data-focus-visible-added]~.fui_radio-button {
  outline: 0;
  box-shadow: 0px 0px 0 3px #5CABF7;
}

.emotion-2 {
  display: block;
  position: relative;
  float: left;
  margin-right: 0.625rem;
  width: 1.5rem;
  height: 1.5rem;
  border: 1px solid #666;
  border-radius: 100%;
}

.emotion-2::after {
  content: "";
  position: absolute;
  width: 0;
  height: 0;
}

.emotion-2::before {
  content: "";
  position: absolute;
  display: block;
  width: calc(100% - 10px);
  height: calc(100% - 10px);
  border-radius: 100%;
  left: 4px;
  top: 4px;
}

.emotion-3 {
  display: inline-block;
  vertical-align: middle;
  line-height: 1.5;
  color: #666;
  max-width: calc(100% - 2.3025rem);
  font-size: 1.1rem;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
}

<label
    class=" emotion-0"
  >
    <input
      class="emotion-1"
      id="radio-button"
      type="radio"
      value="selected"
    />
    <div
      class="fui_radio-button emotion-2"
    />
    <span
      class="emotion-3"
    >
      radio button
    </span>
  </label>
</DocumentFragment>
`;

exports[`<Radio /> snapshots renders with small background size state correctly GapFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  margin: 0 0 1.5rem;
}

.emotion-0:hover {
  -webkit-text-decoration: underline;
  text-decoration: underline;
}

.emotion-1 {
  opacity: 0;
  position: absolute;
}

.emotion-1[data-focus-visible-added]~.fui_radio-button {
  outline: 0;
  box-shadow: 0px 0px 0 3px #5CABF7;
}

.emotion-2 {
  display: block;
  position: relative;
  float: left;
  margin-right: 0.625rem;
  width: 1.5rem;
  height: 1.5rem;
  border: 1px solid #666;
  border-radius: 100%;
}

.emotion-2::after {
  content: "";
  position: absolute;
  width: 0;
  height: 0;
}

.emotion-2::before {
  content: "";
  position: absolute;
  display: block;
  width: calc(100% - 10px);
  height: calc(100% - 10px);
  border-radius: 100%;
  left: 4px;
  top: 4px;
}

.emotion-3 {
  display: inline-block;
  vertical-align: middle;
  line-height: 1.5;
  color: #666;
  max-width: calc(100% - 2.3025rem);
  font-size: 1.063rem;
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
}

<label
    class=" emotion-0"
  >
    <input
      class="emotion-1"
      id="radio-button"
      type="radio"
      value="selected"
    />
    <div
      class="fui_radio-button emotion-2"
    />
    <span
      class="emotion-3"
    >
      radio button
    </span>
  </label>
</DocumentFragment>
`;

exports[`<Radio /> snapshots renders with small background size state correctly GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  margin: 0 0 1.5rem;
}

.emotion-0:hover {
  -webkit-text-decoration: underline;
  text-decoration: underline;
}

.emotion-1 {
  opacity: 0;
  position: absolute;
}

.emotion-1[data-focus-visible-added]~.fui_radio-button {
  outline: 0;
  box-shadow: 0px 0px 0 3px #5CABF7;
}

.emotion-2 {
  display: block;
  position: relative;
  float: left;
  margin-right: 0.625rem;
  width: 1.5rem;
  height: 1.5rem;
  border: 1px solid #666;
  border-radius: 100%;
}

.emotion-2::after {
  content: "";
  position: absolute;
  width: 0;
  height: 0;
}

.emotion-2::before {
  content: "";
  position: absolute;
  display: block;
  width: calc(100% - 10px);
  height: calc(100% - 10px);
  border-radius: 100%;
  left: 4px;
  top: 4px;
}

.emotion-3 {
  display: inline-block;
  vertical-align: middle;
  line-height: 1.5;
  color: #666;
  max-width: calc(100% - 2.3025rem);
  font-size: 1.1rem;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
}

<label
    class=" emotion-0"
  >
    <input
      class="emotion-1"
      id="radio-button"
      type="radio"
      value="selected"
    />
    <div
      class="fui_radio-button emotion-2"
    />
    <span
      class="emotion-3"
    >
      radio button
    </span>
  </label>
</DocumentFragment>
`;

exports[`<Radio /> snapshots renders with small background size state correctly OldNavy 1`] = `
<DocumentFragment>
  .emotion-0 {
  margin: 0 0 1.5rem;
}

.emotion-0:hover {
  -webkit-text-decoration: underline;
  text-decoration: underline;
}

.emotion-1 {
  opacity: 0;
  position: absolute;
}

.emotion-1[data-focus-visible-added]~.fui_radio-button {
  outline: 0;
  box-shadow: 0px 0px 0 3px #5CABF7;
}

.emotion-2 {
  display: block;
  position: relative;
  float: left;
  margin-right: 0.625rem;
  width: 1.5rem;
  height: 1.5rem;
  border: 1px solid #666;
  border-radius: 100%;
}

.emotion-2::after {
  content: "";
  position: absolute;
  width: 0;
  height: 0;
}

.emotion-2::before {
  content: "";
  position: absolute;
  display: block;
  width: calc(100% - 10px);
  height: calc(100% - 10px);
  border-radius: 100%;
  left: 4px;
  top: 4px;
}

.emotion-3 {
  display: inline-block;
  vertical-align: middle;
  line-height: 1.5;
  color: #666;
  max-width: calc(100% - 2.3025rem);
  font-size: 1rem;
  font-family: 'Gotham','Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
}

<label
    class=" emotion-0"
  >
    <input
      class="emotion-1"
      id="radio-button"
      type="radio"
      value="selected"
    />
    <div
      class="fui_radio-button emotion-2"
    />
    <span
      class="emotion-3"
    >
      radio button
    </span>
  </label>
</DocumentFragment>
`;

exports[`<Radio /> snapshots renders with small background size state correctly OldNavy in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  margin: 0 0 1.5rem;
}

.emotion-0:hover {
  -webkit-text-decoration: underline;
  text-decoration: underline;
}

.emotion-1 {
  opacity: 0;
  position: absolute;
}

.emotion-1[data-focus-visible-added]~.fui_radio-button {
  outline: 0;
  box-shadow: 0px 0px 0 3px #5CABF7;
}

.emotion-2 {
  display: block;
  position: relative;
  float: left;
  margin-right: 0.625rem;
  width: 1.5rem;
  height: 1.5rem;
  border: 1px solid #666;
  border-radius: 100%;
}

.emotion-2::after {
  content: "";
  position: absolute;
  width: 0;
  height: 0;
}

.emotion-2::before {
  content: "";
  position: absolute;
  display: block;
  width: calc(100% - 10px);
  height: calc(100% - 10px);
  border-radius: 100%;
  left: 4px;
  top: 4px;
}

.emotion-3 {
  display: inline-block;
  vertical-align: middle;
  line-height: 1.5;
  color: #666;
  max-width: calc(100% - 2.3025rem);
  font-size: 1.1rem;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
}

<label
    class=" emotion-0"
  >
    <input
      class="emotion-1"
      id="radio-button"
      type="radio"
      value="selected"
    />
    <div
      class="fui_radio-button emotion-2"
    />
    <span
      class="emotion-3"
    >
      radio button
    </span>
  </label>
</DocumentFragment>
`;

exports[`<Radio /> snapshots renders with small label text state correctly Athleta 1`] = `
<DocumentFragment>
  .emotion-0 {
  margin: 0 0 1.5rem;
}

.emotion-0:hover {
  -webkit-text-decoration: underline;
  text-decoration: underline;
}

.emotion-1 {
  opacity: 0;
  position: absolute;
}

.emotion-1[data-focus-visible-added]~.fui_radio-button {
  outline: 0;
  box-shadow: 0px 0px 0 3px #5CABF7;
}

.emotion-2 {
  display: block;
  position: relative;
  float: left;
  margin-right: 0.625rem;
  width: 1rem;
  height: 1rem;
  bottom: -5px;
  border: 1px solid #666;
  border-radius: 100%;
}

.emotion-2::after {
  content: "";
  position: absolute;
  width: 0;
  height: 0;
}

.emotion-2::before {
  content: "";
  position: absolute;
  display: block;
  width: calc(100% + 1px);
  height: calc(100% + 1px);
  border-radius: 100%;
  left: -1px;
  top: -1px;
}

.emotion-3 {
  display: inline-block;
  vertical-align: middle;
  line-height: 1.5;
  color: #666;
  max-width: calc(100% - 2.3025rem);
  font-size: 0.875rem;
  font-family: 'Avenir Next','Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
}

<label
    class=" emotion-0"
  >
    <input
      class="emotion-1"
      id="radio-button"
      type="radio"
      value="selected"
    />
    <div
      class="fui_radio-button emotion-2"
    />
    <span
      class="emotion-3"
    >
      radio button
    </span>
  </label>
</DocumentFragment>
`;

exports[`<Radio /> snapshots renders with small label text state correctly Athleta in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  margin: 0 0 1.5rem;
}

.emotion-0:hover {
  -webkit-text-decoration: underline;
  text-decoration: underline;
}

.emotion-1 {
  opacity: 0;
  position: absolute;
}

.emotion-1[data-focus-visible-added]~.fui_radio-button {
  outline: 0;
  box-shadow: 0px 0px 0 3px #5CABF7;
}

.emotion-2 {
  display: block;
  position: relative;
  float: left;
  margin-right: 0.625rem;
  width: 1rem;
  height: 1rem;
  bottom: -5px;
  border: 1px solid #666;
  border-radius: 100%;
}

.emotion-2::after {
  content: "";
  position: absolute;
  width: 0;
  height: 0;
}

.emotion-2::before {
  content: "";
  position: absolute;
  display: block;
  width: calc(100% + 1px);
  height: calc(100% + 1px);
  border-radius: 100%;
  left: -1px;
  top: -1px;
}

.emotion-3 {
  display: inline-block;
  vertical-align: middle;
  line-height: 1.5;
  color: #666;
  max-width: calc(100% - 2.3025rem);
  font-size: 0.875rem;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
}

<label
    class=" emotion-0"
  >
    <input
      class="emotion-1"
      id="radio-button"
      type="radio"
      value="selected"
    />
    <div
      class="fui_radio-button emotion-2"
    />
    <span
      class="emotion-3"
    >
      radio button
    </span>
  </label>
</DocumentFragment>
`;

exports[`<Radio /> snapshots renders with small label text state correctly BananaRepublic 1`] = `
<DocumentFragment>
  .emotion-0 {
  margin: 0 0 1.5rem;
}

.emotion-0:hover {
  -webkit-text-decoration: underline;
  text-decoration: underline;
}

.emotion-1 {
  opacity: 0;
  position: absolute;
}

.emotion-1[data-focus-visible-added]~.fui_radio-button {
  outline: 0;
  box-shadow: 0px 0px 0 3px #CCC;
}

.emotion-2 {
  display: block;
  position: relative;
  float: left;
  margin-right: 0.625rem;
  width: 1rem;
  height: 1rem;
  bottom: -2px;
  border: 1px solid #666;
  border-radius: 100%;
}

.emotion-2::after {
  content: "";
  position: absolute;
  width: 0;
  height: 0;
}

.emotion-2::before {
  content: "";
  position: absolute;
  display: block;
  width: calc(100% + 1px);
  height: calc(100% + 1px);
  border-radius: 100%;
  left: -1px;
  top: -1px;
}

.emotion-3 {
  display: inline-block;
  vertical-align: middle;
  line-height: 1.5;
  color: #666;
  max-width: calc(100% - 2.3025rem);
  font-size: 0.875rem;
  font-family: 'Lynstone',Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
}

<label
    class=" emotion-0"
  >
    <input
      class="emotion-1"
      id="radio-button"
      type="radio"
      value="selected"
    />
    <div
      class="fui_radio-button emotion-2"
    />
    <span
      class="emotion-3"
    >
      radio button
    </span>
  </label>
</DocumentFragment>
`;

exports[`<Radio /> snapshots renders with small label text state correctly BananaRepublic in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  margin: 0 0 1.5rem;
}

.emotion-0:hover {
  -webkit-text-decoration: underline;
  text-decoration: underline;
}

.emotion-1 {
  opacity: 0;
  position: absolute;
}

.emotion-1[data-focus-visible-added]~.fui_radio-button {
  outline: 0;
  box-shadow: 0px 0px 0 3px #5CABF7;
}

.emotion-2 {
  display: block;
  position: relative;
  float: left;
  margin-right: 0.625rem;
  width: 1rem;
  height: 1rem;
  bottom: -2px;
  border: 1px solid #666;
  border-radius: 100%;
}

.emotion-2::after {
  content: "";
  position: absolute;
  width: 0;
  height: 0;
}

.emotion-2::before {
  content: "";
  position: absolute;
  display: block;
  width: calc(100% + 1px);
  height: calc(100% + 1px);
  border-radius: 100%;
  left: -1px;
  top: -1px;
}

.emotion-3 {
  display: inline-block;
  vertical-align: middle;
  line-height: 1.5;
  color: #666;
  max-width: calc(100% - 2.3025rem);
  font-size: 0.875rem;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
}

<label
    class=" emotion-0"
  >
    <input
      class="emotion-1"
      id="radio-button"
      type="radio"
      value="selected"
    />
    <div
      class="fui_radio-button emotion-2"
    />
    <span
      class="emotion-3"
    >
      radio button
    </span>
  </label>
</DocumentFragment>
`;

exports[`<Radio /> snapshots renders with small label text state correctly BananaRepublicFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  margin: 0 0 1.5rem;
}

.emotion-0:hover {
  -webkit-text-decoration: underline;
  text-decoration: underline;
}

.emotion-1 {
  opacity: 0;
  position: absolute;
}

.emotion-1[data-focus-visible-added]~.fui_radio-button {
  outline: 0;
  box-shadow: 0px 0px 0 3px #CCC;
}

.emotion-2 {
  display: block;
  position: relative;
  float: left;
  margin-right: 0.625rem;
  width: 1rem;
  height: 1rem;
  bottom: -2px;
  border: 1px solid #666;
  border-radius: 100%;
}

.emotion-2::after {
  content: "";
  position: absolute;
  width: 0;
  height: 0;
}

.emotion-2::before {
  content: "";
  position: absolute;
  display: block;
  width: calc(100% + 1px);
  height: calc(100% + 1px);
  border-radius: 100%;
  left: -1px;
  top: -1px;
}

.emotion-3 {
  display: inline-block;
  vertical-align: middle;
  line-height: 1.5;
  color: #666;
  max-width: calc(100% - 2.3025rem);
  font-size: 0.875rem;
  font-family: 'Lynstone',Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
}

<label
    class=" emotion-0"
  >
    <input
      class="emotion-1"
      id="radio-button"
      type="radio"
      value="selected"
    />
    <div
      class="fui_radio-button emotion-2"
    />
    <span
      class="emotion-3"
    >
      radio button
    </span>
  </label>
</DocumentFragment>
`;

exports[`<Radio /> snapshots renders with small label text state correctly BananaRepublicFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  margin: 0 0 1.5rem;
}

.emotion-0:hover {
  -webkit-text-decoration: underline;
  text-decoration: underline;
}

.emotion-1 {
  opacity: 0;
  position: absolute;
}

.emotion-1[data-focus-visible-added]~.fui_radio-button {
  outline: 0;
  box-shadow: 0px 0px 0 3px #5CABF7;
}

.emotion-2 {
  display: block;
  position: relative;
  float: left;
  margin-right: 0.625rem;
  width: 1rem;
  height: 1rem;
  bottom: -2px;
  border: 1px solid #666;
  border-radius: 100%;
}

.emotion-2::after {
  content: "";
  position: absolute;
  width: 0;
  height: 0;
}

.emotion-2::before {
  content: "";
  position: absolute;
  display: block;
  width: calc(100% + 1px);
  height: calc(100% + 1px);
  border-radius: 100%;
  left: -1px;
  top: -1px;
}

.emotion-3 {
  display: inline-block;
  vertical-align: middle;
  line-height: 1.5;
  color: #666;
  max-width: calc(100% - 2.3025rem);
  font-size: 0.875rem;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
}

<label
    class=" emotion-0"
  >
    <input
      class="emotion-1"
      id="radio-button"
      type="radio"
      value="selected"
    />
    <div
      class="fui_radio-button emotion-2"
    />
    <span
      class="emotion-3"
    >
      radio button
    </span>
  </label>
</DocumentFragment>
`;

exports[`<Radio /> snapshots renders with small label text state correctly Gap 1`] = `
<DocumentFragment>
  .emotion-0 {
  margin: 0 0 1.5rem;
}

.emotion-0:hover {
  -webkit-text-decoration: underline;
  text-decoration: underline;
}

.emotion-1 {
  opacity: 0;
  position: absolute;
}

.emotion-1[data-focus-visible-added]~.fui_radio-button {
  outline: 0;
  box-shadow: 0px 0px 0 3px #5CABF7;
}

.emotion-2 {
  display: block;
  position: relative;
  float: left;
  margin-right: 0.625rem;
  width: 1rem;
  height: 1rem;
  bottom: -2px;
  border: 1px solid #666;
  border-radius: 100%;
}

.emotion-2::after {
  content: "";
  position: absolute;
  width: 0;
  height: 0;
}

.emotion-2::before {
  content: "";
  position: absolute;
  display: block;
  width: calc(100% + 1px);
  height: calc(100% + 1px);
  border-radius: 100%;
  left: -1px;
  top: -1px;
}

.emotion-3 {
  display: inline-block;
  vertical-align: middle;
  line-height: 1.5;
  color: #666;
  max-width: calc(100% - 2.3025rem);
  font-size: 0.875rem;
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
}

<label
    class=" emotion-0"
  >
    <input
      class="emotion-1"
      id="radio-button"
      type="radio"
      value="selected"
    />
    <div
      class="fui_radio-button emotion-2"
    />
    <span
      class="emotion-3"
    >
      radio button
    </span>
  </label>
</DocumentFragment>
`;

exports[`<Radio /> snapshots renders with small label text state correctly Gap in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  margin: 0 0 1.5rem;
}

.emotion-0:hover {
  -webkit-text-decoration: underline;
  text-decoration: underline;
}

.emotion-1 {
  opacity: 0;
  position: absolute;
}

.emotion-1[data-focus-visible-added]~.fui_radio-button {
  outline: 0;
  box-shadow: 0px 0px 0 3px #5CABF7;
}

.emotion-2 {
  display: block;
  position: relative;
  float: left;
  margin-right: 0.625rem;
  width: 1rem;
  height: 1rem;
  bottom: -2px;
  border: 1px solid #666;
  border-radius: 100%;
}

.emotion-2::after {
  content: "";
  position: absolute;
  width: 0;
  height: 0;
}

.emotion-2::before {
  content: "";
  position: absolute;
  display: block;
  width: calc(100% + 1px);
  height: calc(100% + 1px);
  border-radius: 100%;
  left: -1px;
  top: -1px;
}

.emotion-3 {
  display: inline-block;
  vertical-align: middle;
  line-height: 1.5;
  color: #666;
  max-width: calc(100% - 2.3025rem);
  font-size: 0.875rem;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
}

<label
    class=" emotion-0"
  >
    <input
      class="emotion-1"
      id="radio-button"
      type="radio"
      value="selected"
    />
    <div
      class="fui_radio-button emotion-2"
    />
    <span
      class="emotion-3"
    >
      radio button
    </span>
  </label>
</DocumentFragment>
`;

exports[`<Radio /> snapshots renders with small label text state correctly GapFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  margin: 0 0 1.5rem;
}

.emotion-0:hover {
  -webkit-text-decoration: underline;
  text-decoration: underline;
}

.emotion-1 {
  opacity: 0;
  position: absolute;
}

.emotion-1[data-focus-visible-added]~.fui_radio-button {
  outline: 0;
  box-shadow: 0px 0px 0 3px #5CABF7;
}

.emotion-2 {
  display: block;
  position: relative;
  float: left;
  margin-right: 0.625rem;
  width: 1rem;
  height: 1rem;
  bottom: -2px;
  border: 1px solid #666;
  border-radius: 100%;
}

.emotion-2::after {
  content: "";
  position: absolute;
  width: 0;
  height: 0;
}

.emotion-2::before {
  content: "";
  position: absolute;
  display: block;
  width: calc(100% + 1px);
  height: calc(100% + 1px);
  border-radius: 100%;
  left: -1px;
  top: -1px;
}

.emotion-3 {
  display: inline-block;
  vertical-align: middle;
  line-height: 1.5;
  color: #666;
  max-width: calc(100% - 2.3025rem);
  font-size: 0.875rem;
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
}

<label
    class=" emotion-0"
  >
    <input
      class="emotion-1"
      id="radio-button"
      type="radio"
      value="selected"
    />
    <div
      class="fui_radio-button emotion-2"
    />
    <span
      class="emotion-3"
    >
      radio button
    </span>
  </label>
</DocumentFragment>
`;

exports[`<Radio /> snapshots renders with small label text state correctly GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  margin: 0 0 1.5rem;
}

.emotion-0:hover {
  -webkit-text-decoration: underline;
  text-decoration: underline;
}

.emotion-1 {
  opacity: 0;
  position: absolute;
}

.emotion-1[data-focus-visible-added]~.fui_radio-button {
  outline: 0;
  box-shadow: 0px 0px 0 3px #5CABF7;
}

.emotion-2 {
  display: block;
  position: relative;
  float: left;
  margin-right: 0.625rem;
  width: 1rem;
  height: 1rem;
  bottom: -2px;
  border: 1px solid #666;
  border-radius: 100%;
}

.emotion-2::after {
  content: "";
  position: absolute;
  width: 0;
  height: 0;
}

.emotion-2::before {
  content: "";
  position: absolute;
  display: block;
  width: calc(100% + 1px);
  height: calc(100% + 1px);
  border-radius: 100%;
  left: -1px;
  top: -1px;
}

.emotion-3 {
  display: inline-block;
  vertical-align: middle;
  line-height: 1.5;
  color: #666;
  max-width: calc(100% - 2.3025rem);
  font-size: 0.875rem;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
}

<label
    class=" emotion-0"
  >
    <input
      class="emotion-1"
      id="radio-button"
      type="radio"
      value="selected"
    />
    <div
      class="fui_radio-button emotion-2"
    />
    <span
      class="emotion-3"
    >
      radio button
    </span>
  </label>
</DocumentFragment>
`;

exports[`<Radio /> snapshots renders with small label text state correctly OldNavy 1`] = `
<DocumentFragment>
  .emotion-0 {
  margin: 0 0 1.5rem;
}

.emotion-0:hover {
  -webkit-text-decoration: underline;
  text-decoration: underline;
}

.emotion-1 {
  opacity: 0;
  position: absolute;
}

.emotion-1[data-focus-visible-added]~.fui_radio-button {
  outline: 0;
  box-shadow: 0px 0px 0 3px #5CABF7;
}

.emotion-2 {
  display: block;
  position: relative;
  float: left;
  margin-right: 0.625rem;
  width: 1rem;
  height: 1rem;
  bottom: -2px;
  border: 1px solid #666;
  border-radius: 100%;
}

.emotion-2::after {
  content: "";
  position: absolute;
  width: 0;
  height: 0;
}

.emotion-2::before {
  content: "";
  position: absolute;
  display: block;
  width: calc(100% + 1px);
  height: calc(100% + 1px);
  border-radius: 100%;
  left: -1px;
  top: -1px;
}

.emotion-3 {
  display: inline-block;
  vertical-align: middle;
  line-height: 1.5;
  color: #666;
  max-width: calc(100% - 2.3025rem);
  font-size: 0.875rem;
  font-family: 'Gotham','Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
}

<label
    class=" emotion-0"
  >
    <input
      class="emotion-1"
      id="radio-button"
      type="radio"
      value="selected"
    />
    <div
      class="fui_radio-button emotion-2"
    />
    <span
      class="emotion-3"
    >
      radio button
    </span>
  </label>
</DocumentFragment>
`;

exports[`<Radio /> snapshots renders with small label text state correctly OldNavy in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  margin: 0 0 1.5rem;
}

.emotion-0:hover {
  -webkit-text-decoration: underline;
  text-decoration: underline;
}

.emotion-1 {
  opacity: 0;
  position: absolute;
}

.emotion-1[data-focus-visible-added]~.fui_radio-button {
  outline: 0;
  box-shadow: 0px 0px 0 3px #5CABF7;
}

.emotion-2 {
  display: block;
  position: relative;
  float: left;
  margin-right: 0.625rem;
  width: 1rem;
  height: 1rem;
  bottom: -2px;
  border: 1px solid #666;
  border-radius: 100%;
}

.emotion-2::after {
  content: "";
  position: absolute;
  width: 0;
  height: 0;
}

.emotion-2::before {
  content: "";
  position: absolute;
  display: block;
  width: calc(100% + 1px);
  height: calc(100% + 1px);
  border-radius: 100%;
  left: -1px;
  top: -1px;
}

.emotion-3 {
  display: inline-block;
  vertical-align: middle;
  line-height: 1.5;
  color: #666;
  max-width: calc(100% - 2.3025rem);
  font-size: 0.875rem;
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
}

<label
    class=" emotion-0"
  >
    <input
      class="emotion-1"
      id="radio-button"
      type="radio"
      value="selected"
    />
    <div
      class="fui_radio-button emotion-2"
    />
    <span
      class="emotion-3"
    >
      radio button
    </span>
  </label>
</DocumentFragment>
`;
