// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`DismissButton snapshots renders default state correctly Athleta 1`] = `
<DocumentFragment>
  .emotion-0 {
  background: none;
  border: none;
  width: auto;
  margin: 0;
  padding: 16px;
  text-align: inherit;
  font: inherit;
  margin-left: 8px;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-1 {
  display: inline-block;
  height: 12px;
  width: 12px;
  min-height: 12px;
  min-width: 12px;
}

.emotion-1 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-1 svg path {
  fill: undefined;
}

.emotion-1 svg rect {
  fill: undefined;
}

<button
    aria-label="Close"
    class="emotion-0"
    type="button"
  >
    <span
      aria-hidden="true"
      class="emotion-1"
    >
      <svg
        fill="none"
        viewBox="0 0 22 22"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          clip-rule="evenodd"
          d="M1.391 1.55603c-.585787.58579-.585786 1.53554 0 2.12133L8.71364 11 1.391 18.3226c-.585787.5858-.585786 1.5356 0 2.1214l.16503.165c.58579.5858 1.53554.5858 2.12133 0L11 13.2864l7.3226 7.3226c.5858.5858 1.5356.5858 2.1214 0l.165-.165c.5858-.5858.5858-1.5356 0-2.1214L13.2864 11l7.3226-7.32264c.5858-.58579.5858-1.53554 0-2.12133l-.165-.16503c-.5858-.585787-1.5356-.585786-2.1214 0L11 8.71364 3.67736 1.391c-.58579-.585787-1.53554-.585786-2.12133 0l-.16503.16503z"
          fill="#666"
          fill-rule="evenodd"
        />
      </svg>
    </span>
  </button>
</DocumentFragment>
`;

exports[`DismissButton snapshots renders default state correctly Athleta in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  background: none;
  border: none;
  width: auto;
  margin: 0;
  padding: 16px;
  text-align: inherit;
  font: inherit;
  margin-left: 8px;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-1 {
  display: inline-block;
  height: 12px;
  width: 12px;
  min-height: 12px;
  min-width: 12px;
}

.emotion-1 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-1 svg path {
  fill: undefined;
}

.emotion-1 svg rect {
  fill: undefined;
}

<button
    aria-label="Close"
    class="emotion-0"
    type="button"
  >
    <span
      aria-hidden="true"
      class="emotion-1"
    >
      <svg
        fill="none"
        viewBox="0 0 22 22"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          clip-rule="evenodd"
          d="M1.391 1.55603c-.585787.58579-.585786 1.53554 0 2.12133L8.71364 11 1.391 18.3226c-.585787.5858-.585786 1.5356 0 2.1214l.16503.165c.58579.5858 1.53554.5858 2.12133 0L11 13.2864l7.3226 7.3226c.5858.5858 1.5356.5858 2.1214 0l.165-.165c.5858-.5858.5858-1.5356 0-2.1214L13.2864 11l7.3226-7.32264c.5858-.58579.5858-1.53554 0-2.12133l-.165-.16503c-.5858-.585787-1.5356-.585786-2.1214 0L11 8.71364 3.67736 1.391c-.58579-.585787-1.53554-.585786-2.12133 0l-.16503.16503z"
          fill="#666"
          fill-rule="evenodd"
        />
      </svg>
    </span>
  </button>
</DocumentFragment>
`;

exports[`DismissButton snapshots renders default state correctly BananaRepublic 1`] = `
<DocumentFragment>
  .emotion-0 {
  background: none;
  border: none;
  width: auto;
  margin: 0;
  padding: 16px;
  text-align: inherit;
  font: inherit;
  margin-left: 8px;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-1 {
  display: inline-block;
  height: 12px;
  width: 12px;
  min-height: 12px;
  min-width: 12px;
}

.emotion-1 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-1 svg path {
  fill: undefined;
}

.emotion-1 svg rect {
  fill: undefined;
}

<button
    aria-label="Close"
    class="emotion-0"
    type="button"
  >
    <span
      aria-hidden="true"
      class="emotion-1"
    >
      <svg
        fill="none"
        viewBox="0 0 22 22"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          clip-rule="evenodd"
          d="M.0340576 1.62505L1.62505.0340576 10.9058 9.31483 20.1866.0340576l1.591 1.5909924-9.2808 9.28075 9.2808 9.2808-1.591 1.591-9.2808-9.2808-9.28075 9.2808-1.5909911-1.591L9.31483 10.9058.0340576 1.62505z"
          fill="#000000"
          fill-rule="evenodd"
        />
      </svg>
    </span>
  </button>
</DocumentFragment>
`;

exports[`DismissButton snapshots renders default state correctly BananaRepublic in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  background: none;
  border: none;
  width: auto;
  margin: 0;
  padding: 16px;
  text-align: inherit;
  font: inherit;
  margin-left: 8px;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-1 {
  display: inline-block;
  height: 12px;
  width: 12px;
  min-height: 12px;
  min-width: 12px;
}

.emotion-1 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-1 svg path {
  fill: undefined;
}

.emotion-1 svg rect {
  fill: undefined;
}

<button
    aria-label="Close"
    class="emotion-0"
    type="button"
  >
    <span
      aria-hidden="true"
      class="emotion-1"
    >
      <svg
        fill="none"
        viewBox="0 0 22 22"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          clip-rule="evenodd"
          d="M.0340576 1.62505L1.62505.0340576 10.9058 9.31483 20.1866.0340576l1.591 1.5909924-9.2808 9.28075 9.2808 9.2808-1.591 1.591-9.2808-9.2808-9.28075 9.2808-1.5909911-1.591L9.31483 10.9058.0340576 1.62505z"
          fill="#000000"
          fill-rule="evenodd"
        />
      </svg>
    </span>
  </button>
</DocumentFragment>
`;

exports[`DismissButton snapshots renders default state correctly BananaRepublicFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  background: none;
  border: none;
  width: auto;
  margin: 0;
  padding: 16px;
  text-align: inherit;
  font: inherit;
  margin-left: 8px;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-1 {
  display: inline-block;
  height: 12px;
  width: 12px;
  min-height: 12px;
  min-width: 12px;
}

.emotion-1 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-1 svg path {
  fill: undefined;
}

.emotion-1 svg rect {
  fill: undefined;
}

<button
    aria-label="Close"
    class="emotion-0"
    type="button"
  >
    <span
      aria-hidden="true"
      class="emotion-1"
    >
      <svg
        fill="none"
        viewBox="0 0 22 22"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          clip-rule="evenodd"
          d="M.0340576 1.62505L1.62505.0340576 10.9058 9.31483 20.1866.0340576l1.591 1.5909924-9.2808 9.28075 9.2808 9.2808-1.591 1.591-9.2808-9.2808-9.28075 9.2808-1.5909911-1.591L9.31483 10.9058.0340576 1.62505z"
          fill="#000000"
          fill-rule="evenodd"
        />
      </svg>
    </span>
  </button>
</DocumentFragment>
`;

exports[`DismissButton snapshots renders default state correctly BananaRepublicFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  background: none;
  border: none;
  width: auto;
  margin: 0;
  padding: 16px;
  text-align: inherit;
  font: inherit;
  margin-left: 8px;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-1 {
  display: inline-block;
  height: 12px;
  width: 12px;
  min-height: 12px;
  min-width: 12px;
}

.emotion-1 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-1 svg path {
  fill: undefined;
}

.emotion-1 svg rect {
  fill: undefined;
}

<button
    aria-label="Close"
    class="emotion-0"
    type="button"
  >
    <span
      aria-hidden="true"
      class="emotion-1"
    >
      <svg
        fill="none"
        viewBox="0 0 22 22"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          clip-rule="evenodd"
          d="M.0340576 1.62505L1.62505.0340576 10.9058 9.31483 20.1866.0340576l1.591 1.5909924-9.2808 9.28075 9.2808 9.2808-1.591 1.591-9.2808-9.2808-9.28075 9.2808-1.5909911-1.591L9.31483 10.9058.0340576 1.62505z"
          fill="#000000"
          fill-rule="evenodd"
        />
      </svg>
    </span>
  </button>
</DocumentFragment>
`;

exports[`DismissButton snapshots renders default state correctly Gap 1`] = `
<DocumentFragment>
  .emotion-0 {
  background: none;
  border: none;
  width: auto;
  margin: 0;
  padding: 16px;
  text-align: inherit;
  font: inherit;
  margin-left: 8px;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-1 {
  display: inline-block;
  height: 12px;
  width: 12px;
  min-height: 12px;
  min-width: 12px;
}

.emotion-1 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-1 svg path {
  fill: undefined;
}

.emotion-1 svg rect {
  fill: undefined;
}

<button
    aria-label="Close"
    class="emotion-0"
    type="button"
  >
    <span
      aria-hidden="true"
      class="emotion-1"
    >
      <svg
        viewBox="0 0 10.126 10.313"
        xmlns="http://www.w3.org/2000/svg"
      >
        <g>
          <path
            d="m9.213.187.913.913-9.211 9.212-.913-.913z"
            fill="#002554"
          />
        </g>
        <g>
          <path
            d="m10.125 9.212-.912.913L0 .914.914 0z"
            fill="#002554"
          />
        </g>
      </svg>
    </span>
  </button>
</DocumentFragment>
`;

exports[`DismissButton snapshots renders default state correctly Gap in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  background: none;
  border: none;
  width: auto;
  margin: 0;
  padding: 16px;
  text-align: inherit;
  font: inherit;
  margin-left: 8px;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-1 {
  display: inline-block;
  height: 12px;
  width: 12px;
  min-height: 12px;
  min-width: 12px;
}

.emotion-1 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-1 svg path {
  fill: undefined;
}

.emotion-1 svg rect {
  fill: undefined;
}

<button
    aria-label="Close"
    class="emotion-0"
    type="button"
  >
    <span
      aria-hidden="true"
      class="emotion-1"
    >
      <svg
        viewBox="0 0 10.126 10.313"
        xmlns="http://www.w3.org/2000/svg"
      >
        <g>
          <path
            d="m9.213.187.913.913-9.211 9.212-.913-.913z"
            fill="#002554"
          />
        </g>
        <g>
          <path
            d="m10.125 9.212-.912.913L0 .914.914 0z"
            fill="#002554"
          />
        </g>
      </svg>
    </span>
  </button>
</DocumentFragment>
`;

exports[`DismissButton snapshots renders default state correctly GapFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  background: none;
  border: none;
  width: auto;
  margin: 0;
  padding: 16px;
  text-align: inherit;
  font: inherit;
  margin-left: 8px;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-1 {
  display: inline-block;
  height: 12px;
  width: 12px;
  min-height: 12px;
  min-width: 12px;
}

.emotion-1 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-1 svg path {
  fill: undefined;
}

.emotion-1 svg rect {
  fill: undefined;
}

<button
    aria-label="Close"
    class="emotion-0"
    type="button"
  >
    <span
      aria-hidden="true"
      class="emotion-1"
    >
      <svg
        viewBox="0 0 10.126 10.313"
        xmlns="http://www.w3.org/2000/svg"
      >
        <g>
          <path
            d="m9.213.187.913.913-9.211 9.212-.913-.913z"
            fill="#002554"
          />
        </g>
        <g>
          <path
            d="m10.125 9.212-.912.913L0 .914.914 0z"
            fill="#002554"
          />
        </g>
      </svg>
    </span>
  </button>
</DocumentFragment>
`;

exports[`DismissButton snapshots renders default state correctly GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  background: none;
  border: none;
  width: auto;
  margin: 0;
  padding: 16px;
  text-align: inherit;
  font: inherit;
  margin-left: 8px;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-1 {
  display: inline-block;
  height: 12px;
  width: 12px;
  min-height: 12px;
  min-width: 12px;
}

.emotion-1 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-1 svg path {
  fill: undefined;
}

.emotion-1 svg rect {
  fill: undefined;
}

<button
    aria-label="Close"
    class="emotion-0"
    type="button"
  >
    <span
      aria-hidden="true"
      class="emotion-1"
    >
      <svg
        viewBox="0 0 10.126 10.313"
        xmlns="http://www.w3.org/2000/svg"
      >
        <g>
          <path
            d="m9.213.187.913.913-9.211 9.212-.913-.913z"
            fill="#002554"
          />
        </g>
        <g>
          <path
            d="m10.125 9.212-.912.913L0 .914.914 0z"
            fill="#002554"
          />
        </g>
      </svg>
    </span>
  </button>
</DocumentFragment>
`;

exports[`DismissButton snapshots renders default state correctly OldNavy 1`] = `
<DocumentFragment>
  .emotion-0 {
  background: none;
  border: none;
  width: auto;
  margin: 0;
  padding: 16px;
  text-align: inherit;
  font: inherit;
  margin-left: 8px;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-1 {
  display: inline-block;
  height: 12px;
  width: 12px;
  min-height: 12px;
  min-width: 12px;
}

.emotion-1 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-1 svg path {
  fill: undefined;
}

.emotion-1 svg rect {
  fill: undefined;
}

<button
    aria-label="Close"
    class="emotion-0"
    type="button"
  >
    <span
      aria-hidden="true"
      class="emotion-1"
    >
      <svg
        fill="none"
        viewBox="0 0 22 22"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          clip-rule="evenodd"
          d="M.0339966 1.62505L1.62499.0340576 10.9058 9.31483 20.1865.0340576l1.591 1.5909924-9.2807 9.28075 9.2807 9.2808-1.591 1.591-9.2807-9.2808-9.28081 9.2808-1.5909922-1.591L9.31477 10.9058.0339966 1.62505z"
          fill="#043863"
          fill-rule="evenodd"
        />
      </svg>
    </span>
  </button>
</DocumentFragment>
`;

exports[`DismissButton snapshots renders default state correctly OldNavy in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  background: none;
  border: none;
  width: auto;
  margin: 0;
  padding: 16px;
  text-align: inherit;
  font: inherit;
  margin-left: 8px;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-1 {
  display: inline-block;
  height: 12px;
  width: 12px;
  min-height: 12px;
  min-width: 12px;
}

.emotion-1 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-1 svg path {
  fill: undefined;
}

.emotion-1 svg rect {
  fill: undefined;
}

<button
    aria-label="Close"
    class="emotion-0"
    type="button"
  >
    <span
      aria-hidden="true"
      class="emotion-1"
    >
      <svg
        fill="none"
        viewBox="0 0 22 22"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          clip-rule="evenodd"
          d="M.0339966 1.62505L1.62499.0340576 10.9058 9.31483 20.1865.0340576l1.591 1.5909924-9.2807 9.28075 9.2807 9.2808-1.591 1.591-9.2807-9.2808-9.28081 9.2808-1.5909922-1.591L9.31477 10.9058.0339966 1.62505z"
          fill="#043863"
          fill-rule="evenodd"
        />
      </svg>
    </span>
  </button>
</DocumentFragment>
`;
