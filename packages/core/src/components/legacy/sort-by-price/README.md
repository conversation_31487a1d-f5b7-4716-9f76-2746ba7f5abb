# Sort By Price
The <SortByPrice /> component is designed to provide our users with the ability to sort their results by price. Options include:
- Featured (Default)
- Low to High
- High to Low

The sort functionality lives in the backend due to the practical and performance requirement of product paging. The client does not know the full spectrum of products available to it and therefore cannot sort them effectively.

Technical flow for the <SortBy /> component:
## URL has no sort hash key set
1. User selects a sort value
2. Component publishes that action to the mediator
3. Mediator updates the list of selected facets
4. Mediator builds and sets query string based on the updated selected facet list
6. Mediator updates its own state with new selected facet list
7. Call to fetch additional products is made
8. Sort by data *is not returned from the API*
9. Mediator uses previous state to determine if there's a selected sort by and brings that previous sort by value into the current list of selected facets
10. Mediator passes that selected facet list to the sort by component to reconcile the dropdown text

## Business rules to be mindful of
- Clearing facets does not affect the sort value
- Switching department does not affect the sort value
- Sort value should not be represented by a facet tag

## Dropdown Text Direction

`dir="rtl"` is set on the select element to right align the selected option's text with the caret. `dir="ltr"` is set on the option elements to prevent the browser from treating certain text with terminal punctuation as right to left text e.g. `prix: $-$$$` as `$-$$$: prix`. See https://github.gapinc.com/ecomfrontend/core-ui/pull/2751.

## Props

useDefaultOptionAsPlaceholderText is a boolean value that is by default set to false.  If included as a prop and set true it sets the placeholder text to equal the first value in the options list. 

To view documentation about the props for `SortByPrice`, go [here](https://github.gapinc.com/ecomfrontend/core-ui/blob/main/packages/sort-by-price/types.js).
