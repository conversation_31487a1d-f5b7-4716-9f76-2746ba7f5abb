// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`<LinkSwatches /> snapshots for large breakpoint renders default state correctly Athleta 1`] = `
.emotion-0>div {
  margin-right: 7px;
}

.emotion-0 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
}

.emotion-1 {
  display: inline-block;
  position: relative;
}

@media (pointer: coarse) {
  .emotion-2:active label {
    -webkit-text-decoration: underline;
    text-decoration: underline;
  }
}

.emotion-2 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

@media (pointer: fine) {
  .emotion-2:hover label {
    -webkit-text-decoration: underline;
    text-decoration: underline;
  }
}

.emotion-3 .focus-visible+img {
  outline: 0;
  box-shadow: 0 0 0 0.1875rem #5CABF7;
  z-index: -1;
}

.emotion-3 {
  display: inline-block;
  position: relative;
  height: 1.875rem;
  width: 1.875rem;
  text-align: center;
  vertical-align: top;
  white-space: inherit;
  padding: 0.375rem;
}

.emotion-4 {
  opacity: 0;
  position: absolute;
  cursor: pointer;
  width: 100%;
  height: 100%;
  z-index: 1;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%,-50%);
  -moz-transform: translate(-50%,-50%);
  -ms-transform: translate(-50%,-50%);
  transform: translate(-50%,-50%);
  margin: 0;
}

.emotion-5 {
  width: 100%;
  height: 100%;
  margin: 0;
  border-radius: 50%;
  z-index: 1;
  box-shadow: ;
}

.emotion-16 {
  z-index: 2;
  position: absolute;
  width: 75%;
  height: 75%;
  top: 50%;
  left: 50%;
  margin: 0;
  -webkit-transform: translate(-50%,-50%);
  -moz-transform: translate(-50%,-50%);
  -ms-transform: translate(-50%,-50%);
  transform: translate(-50%,-50%);
  pointer-events: none;
}

.emotion-17 {
  -webkit-transform: translate(10%, 110%);
  -moz-transform: translate(10%, 110%);
  -ms-transform: translate(10%, 110%);
  transform: translate(10%, 110%);
}

.emotion-18 {
  -webkit-transform: rotate(-45deg);
  -moz-transform: rotate(-45deg);
  -ms-transform: rotate(-45deg);
  transform: rotate(-45deg);
  width: calc(100% + 0.25rem);
}

.emotion-19 {
  position: fixed;
  right: 100%;
  bottom: 100%;
}

.emotion-30 {
  font-family: 'Avenir Next','Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  color: #666;
  font-size: 0.875rem;
  margin-left: 4px;
  min-width: 22px;
  max-width: 28px;
  white-space: nowrap;
}

<div
  class="emotion-0"
>
  <div
    class="emotion-1"
  >
    <div
      class="emotion-2"
    >
      <div
        class="emotion-3"
      >
        <input
          aria-disabled="false"
          aria-label="carbon"
          class="emotion-4"
          data-product-image-alt-text="Split Neck Tee in Carbon"
          data-product-image-src="images/product-photos/old-navy/split-neck-tee/product--carbon.jpg"
          id="0000"
          name="color-radio"
          type="radio"
          value="0000"
        />
        <img
          alt="carbon"
          aria-hidden="true"
          class="emotion-5"
          src="images/swatches/old-navy/split-neck-tee/swatch--carbon.jpg"
        />
      </div>
    </div>
  </div>
  <div
    class="emotion-1"
  >
    <div
      class="emotion-2"
    >
      <div
        class="emotion-3"
      >
        <input
          aria-disabled="false"
          aria-label="coral retro stripe"
          class="emotion-4"
          data-product-image-alt-text="Split Neck Tee in Coral Retro Stripe"
          data-product-image-src="images/product-photos/old-navy/split-neck-tee/product--coral-retro-stripe.jpg"
          id="0001"
          name="color-radio"
          type="radio"
          value="0001"
        />
        <img
          alt="coral retro stripe"
          aria-hidden="true"
          class="emotion-5"
          src="images/swatches/old-navy/split-neck-tee/swatch--coral-retro-stripe.jpg"
        />
      </div>
    </div>
  </div>
  <div
    class="emotion-1"
  >
    <div
      class="emotion-2"
    >
      <div
        class="emotion-3"
      >
        <input
          aria-describedby="0002-outofstock"
          aria-disabled="true"
          aria-label="coral tropics"
          class="emotion-4"
          data-product-image-alt-text="Split Neck Tee in Coral Tropics"
          data-product-image-src="images/product-photos/old-navy/split-neck-tee/product--coral-tropics.jpg"
          id="0002"
          name="color-radio"
          type="radio"
          value="0002"
        />
        <img
          alt="coral tropics"
          aria-hidden="true"
          class="emotion-5"
          src="images/swatches/old-navy/split-neck-tee/swatch--coral-tropics.jpg"
        />
        <svg
          class="emotion-16"
          viewBox="0 0 37 23"
        >
          <g
            fill="none"
            fill-rule="evenodd"
            stroke="none"
            stroke-width="1"
          >
            <g
              class="emotion-17"
              fill="#000000"
              stroke="#FFFFFF"
            >
              <rect
                class="emotion-18"
                height="2"
              />
            </g>
          </g>
        </svg>
      </div>
      <span
        class="emotion-19"
        id="0002-outofstock"
      >
        fui.color_swatch.out_of_stock
      </span>
    </div>
  </div>
  <div
    class="emotion-1"
  >
    <div
      class="emotion-2"
    >
      <div
        class="emotion-3"
      >
        <input
          aria-disabled="false"
          aria-label="enchant mint"
          class="emotion-4"
          data-product-image-alt-text="Split Neck Tee in Enchant Mint"
          data-product-image-src="images/product-photos/old-navy/split-neck-tee/product--enchant-mint.jpg"
          id="0003"
          name="color-radio"
          type="radio"
          value="0003"
        />
        <img
          alt="enchant mint"
          aria-hidden="true"
          class="emotion-5"
          src="images/swatches/old-navy/split-neck-tee/swatch--enchant-mint.jpg"
        />
      </div>
    </div>
  </div>
  <div
    class="emotion-1"
  >
    <div
      class="emotion-2"
    >
      <div
        class="emotion-3"
      >
        <input
          aria-disabled="false"
          aria-label="goldenopportunity"
          class="emotion-4"
          data-product-image-alt-text="Split Neck Tee in Golden Opportunity"
          data-product-image-src="images/product-photos/old-navy/split-neck-tee/product--goldenopportunity.jpg"
          id="0004"
          name="color-radio"
          type="radio"
          value="0004"
        />
        <img
          alt="goldenopportunity"
          aria-hidden="true"
          class="emotion-5"
          src="images/swatches/old-navy/split-neck-tee/swatch--goldenopportunity.jpg"
        />
      </div>
    </div>
  </div>
  <a
    aria-label="fui.swatches.all_colors_link_aria_label"
    class="emotion-30"
  >
    + 3
  </a>
</div>
`;

exports[`<LinkSwatches /> snapshots for large breakpoint renders default state correctly Athleta in crossBrand 1`] = `
.emotion-0>div {
  margin-right: 7px;
}

.emotion-0 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
}

.emotion-1 {
  display: inline-block;
  position: relative;
}

@media (pointer: coarse) {
  .emotion-2:active label {
    -webkit-text-decoration: underline;
    text-decoration: underline;
  }
}

.emotion-2 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

@media (pointer: fine) {
  .emotion-2:hover label {
    -webkit-text-decoration: underline;
    text-decoration: underline;
  }
}

.emotion-3 .focus-visible+img {
  outline: 0;
  box-shadow: 0 0 0 0.1875rem #5CABF7;
  z-index: -1;
}

.emotion-3 {
  display: inline-block;
  position: relative;
  height: 1.875rem;
  width: 1.875rem;
  text-align: center;
  vertical-align: top;
  white-space: inherit;
  padding: 0.375rem;
}

.emotion-4 {
  opacity: 0;
  position: absolute;
  cursor: pointer;
  width: 100%;
  height: 100%;
  z-index: 1;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%,-50%);
  -moz-transform: translate(-50%,-50%);
  -ms-transform: translate(-50%,-50%);
  transform: translate(-50%,-50%);
  margin: 0;
}

.emotion-5 {
  width: 100%;
  height: 100%;
  margin: 0;
  border-radius: 50%;
  z-index: 1;
  box-shadow: ;
}

.emotion-16 {
  z-index: 2;
  position: absolute;
  width: 75%;
  height: 75%;
  top: 50%;
  left: 50%;
  margin: 0;
  -webkit-transform: translate(-50%,-50%);
  -moz-transform: translate(-50%,-50%);
  -ms-transform: translate(-50%,-50%);
  transform: translate(-50%,-50%);
  pointer-events: none;
}

.emotion-17 {
  -webkit-transform: translate(10%, 110%);
  -moz-transform: translate(10%, 110%);
  -ms-transform: translate(10%, 110%);
  transform: translate(10%, 110%);
}

.emotion-18 {
  -webkit-transform: rotate(-45deg);
  -moz-transform: rotate(-45deg);
  -ms-transform: rotate(-45deg);
  transform: rotate(-45deg);
  width: calc(100% + 0.25rem);
}

.emotion-19 {
  position: fixed;
  right: 100%;
  bottom: 100%;
}

.emotion-30 {
  font-family: 'Avenir Next','Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  color: #666;
  font-size: 0.875rem;
  margin-left: 4px;
  min-width: 22px;
  max-width: 28px;
  white-space: nowrap;
}

<div
  class="emotion-0"
>
  <div
    class="emotion-1"
  >
    <div
      class="emotion-2"
    >
      <div
        class="emotion-3"
      >
        <input
          aria-disabled="false"
          aria-label="carbon"
          class="emotion-4"
          data-product-image-alt-text="Split Neck Tee in Carbon"
          data-product-image-src="images/product-photos/old-navy/split-neck-tee/product--carbon.jpg"
          id="0000"
          name="color-radio"
          type="radio"
          value="0000"
        />
        <img
          alt="carbon"
          aria-hidden="true"
          class="emotion-5"
          src="images/swatches/old-navy/split-neck-tee/swatch--carbon.jpg"
        />
      </div>
    </div>
  </div>
  <div
    class="emotion-1"
  >
    <div
      class="emotion-2"
    >
      <div
        class="emotion-3"
      >
        <input
          aria-disabled="false"
          aria-label="coral retro stripe"
          class="emotion-4"
          data-product-image-alt-text="Split Neck Tee in Coral Retro Stripe"
          data-product-image-src="images/product-photos/old-navy/split-neck-tee/product--coral-retro-stripe.jpg"
          id="0001"
          name="color-radio"
          type="radio"
          value="0001"
        />
        <img
          alt="coral retro stripe"
          aria-hidden="true"
          class="emotion-5"
          src="images/swatches/old-navy/split-neck-tee/swatch--coral-retro-stripe.jpg"
        />
      </div>
    </div>
  </div>
  <div
    class="emotion-1"
  >
    <div
      class="emotion-2"
    >
      <div
        class="emotion-3"
      >
        <input
          aria-describedby="0002-outofstock"
          aria-disabled="true"
          aria-label="coral tropics"
          class="emotion-4"
          data-product-image-alt-text="Split Neck Tee in Coral Tropics"
          data-product-image-src="images/product-photos/old-navy/split-neck-tee/product--coral-tropics.jpg"
          id="0002"
          name="color-radio"
          type="radio"
          value="0002"
        />
        <img
          alt="coral tropics"
          aria-hidden="true"
          class="emotion-5"
          src="images/swatches/old-navy/split-neck-tee/swatch--coral-tropics.jpg"
        />
        <svg
          class="emotion-16"
          viewBox="0 0 37 23"
        >
          <g
            fill="none"
            fill-rule="evenodd"
            stroke="none"
            stroke-width="1"
          >
            <g
              class="emotion-17"
              fill="#000000"
              stroke="#FFFFFF"
            >
              <rect
                class="emotion-18"
                height="2"
              />
            </g>
          </g>
        </svg>
      </div>
      <span
        class="emotion-19"
        id="0002-outofstock"
      >
        fui.color_swatch.out_of_stock
      </span>
    </div>
  </div>
  <div
    class="emotion-1"
  >
    <div
      class="emotion-2"
    >
      <div
        class="emotion-3"
      >
        <input
          aria-disabled="false"
          aria-label="enchant mint"
          class="emotion-4"
          data-product-image-alt-text="Split Neck Tee in Enchant Mint"
          data-product-image-src="images/product-photos/old-navy/split-neck-tee/product--enchant-mint.jpg"
          id="0003"
          name="color-radio"
          type="radio"
          value="0003"
        />
        <img
          alt="enchant mint"
          aria-hidden="true"
          class="emotion-5"
          src="images/swatches/old-navy/split-neck-tee/swatch--enchant-mint.jpg"
        />
      </div>
    </div>
  </div>
  <div
    class="emotion-1"
  >
    <div
      class="emotion-2"
    >
      <div
        class="emotion-3"
      >
        <input
          aria-disabled="false"
          aria-label="goldenopportunity"
          class="emotion-4"
          data-product-image-alt-text="Split Neck Tee in Golden Opportunity"
          data-product-image-src="images/product-photos/old-navy/split-neck-tee/product--goldenopportunity.jpg"
          id="0004"
          name="color-radio"
          type="radio"
          value="0004"
        />
        <img
          alt="goldenopportunity"
          aria-hidden="true"
          class="emotion-5"
          src="images/swatches/old-navy/split-neck-tee/swatch--goldenopportunity.jpg"
        />
      </div>
    </div>
  </div>
  <a
    aria-label="fui.swatches.all_colors_link_aria_label"
    class="emotion-30"
  >
    + 3
  </a>
</div>
`;

exports[`<LinkSwatches /> snapshots for large breakpoint renders default state correctly BananaRepublic 1`] = `
.emotion-0>div {
  margin-right: 7px;
}

.emotion-0 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
}

.emotion-1 {
  display: inline-block;
  position: relative;
}

@media (pointer: coarse) {
  .emotion-2:active label {
    -webkit-text-decoration: underline;
    text-decoration: underline;
  }
}

.emotion-2 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

@media (pointer: fine) {
  .emotion-2:hover label {
    -webkit-text-decoration: underline;
    text-decoration: underline;
  }
}

.emotion-3 .focus-visible+img {
  outline: 0;
  box-shadow: 0 0 0 0.1875rem #5CABF7;
  z-index: -1;
}

.emotion-3 {
  display: inline-block;
  position: relative;
  height: 1.875rem;
  width: 1.875rem;
  text-align: center;
  vertical-align: top;
  white-space: inherit;
  padding: 0.375rem;
}

.emotion-4 {
  opacity: 0;
  position: absolute;
  cursor: pointer;
  width: 100%;
  height: 100%;
  z-index: 1;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%,-50%);
  -moz-transform: translate(-50%,-50%);
  -ms-transform: translate(-50%,-50%);
  transform: translate(-50%,-50%);
  margin: 0;
}

.emotion-5 {
  width: 100%;
  height: 100%;
  margin: 0;
  border-radius: 50%;
  z-index: 1;
  box-shadow: ;
}

.emotion-16 {
  z-index: 2;
  position: absolute;
  width: 75%;
  height: 75%;
  top: 50%;
  left: 50%;
  margin: 0;
  -webkit-transform: translate(-50%,-50%);
  -moz-transform: translate(-50%,-50%);
  -ms-transform: translate(-50%,-50%);
  transform: translate(-50%,-50%);
  pointer-events: none;
}

.emotion-17 {
  -webkit-transform: translate(10%, 110%);
  -moz-transform: translate(10%, 110%);
  -ms-transform: translate(10%, 110%);
  transform: translate(10%, 110%);
}

.emotion-18 {
  -webkit-transform: rotate(-45deg);
  -moz-transform: rotate(-45deg);
  -ms-transform: rotate(-45deg);
  transform: rotate(-45deg);
  width: calc(100% + 0.25rem);
}

.emotion-19 {
  position: fixed;
  right: 100%;
  bottom: 100%;
}

.emotion-30 {
  font-family: 'EuclidCircularB','Hiragino Kaku Gothic Pro',Helvetica,Arial,sans-serif;
  font-weight: 300;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  color: #666;
  font-size: 0.875rem;
  margin-left: 4px;
  min-width: 22px;
  max-width: 28px;
  white-space: nowrap;
}

<div
  class="emotion-0"
>
  <div
    class="emotion-1"
  >
    <div
      class="emotion-2"
    >
      <div
        class="emotion-3"
      >
        <input
          aria-disabled="false"
          aria-label="carbon"
          class="emotion-4"
          data-product-image-alt-text="Split Neck Tee in Carbon"
          data-product-image-src="images/product-photos/old-navy/split-neck-tee/product--carbon.jpg"
          id="0000"
          name="color-radio"
          type="radio"
          value="0000"
        />
        <img
          alt="carbon"
          aria-hidden="true"
          class="emotion-5"
          src="images/swatches/old-navy/split-neck-tee/swatch--carbon.jpg"
        />
      </div>
    </div>
  </div>
  <div
    class="emotion-1"
  >
    <div
      class="emotion-2"
    >
      <div
        class="emotion-3"
      >
        <input
          aria-disabled="false"
          aria-label="coral retro stripe"
          class="emotion-4"
          data-product-image-alt-text="Split Neck Tee in Coral Retro Stripe"
          data-product-image-src="images/product-photos/old-navy/split-neck-tee/product--coral-retro-stripe.jpg"
          id="0001"
          name="color-radio"
          type="radio"
          value="0001"
        />
        <img
          alt="coral retro stripe"
          aria-hidden="true"
          class="emotion-5"
          src="images/swatches/old-navy/split-neck-tee/swatch--coral-retro-stripe.jpg"
        />
      </div>
    </div>
  </div>
  <div
    class="emotion-1"
  >
    <div
      class="emotion-2"
    >
      <div
        class="emotion-3"
      >
        <input
          aria-describedby="0002-outofstock"
          aria-disabled="true"
          aria-label="coral tropics"
          class="emotion-4"
          data-product-image-alt-text="Split Neck Tee in Coral Tropics"
          data-product-image-src="images/product-photos/old-navy/split-neck-tee/product--coral-tropics.jpg"
          id="0002"
          name="color-radio"
          type="radio"
          value="0002"
        />
        <img
          alt="coral tropics"
          aria-hidden="true"
          class="emotion-5"
          src="images/swatches/old-navy/split-neck-tee/swatch--coral-tropics.jpg"
        />
        <svg
          class="emotion-16"
          viewBox="0 0 37 23"
        >
          <g
            fill="none"
            fill-rule="evenodd"
            stroke="none"
            stroke-width="1"
          >
            <g
              class="emotion-17"
              fill="#000000"
              stroke="#FFFFFF"
            >
              <rect
                class="emotion-18"
                height="2"
              />
            </g>
          </g>
        </svg>
      </div>
      <span
        class="emotion-19"
        id="0002-outofstock"
      >
        fui.color_swatch.out_of_stock
      </span>
    </div>
  </div>
  <div
    class="emotion-1"
  >
    <div
      class="emotion-2"
    >
      <div
        class="emotion-3"
      >
        <input
          aria-disabled="false"
          aria-label="enchant mint"
          class="emotion-4"
          data-product-image-alt-text="Split Neck Tee in Enchant Mint"
          data-product-image-src="images/product-photos/old-navy/split-neck-tee/product--enchant-mint.jpg"
          id="0003"
          name="color-radio"
          type="radio"
          value="0003"
        />
        <img
          alt="enchant mint"
          aria-hidden="true"
          class="emotion-5"
          src="images/swatches/old-navy/split-neck-tee/swatch--enchant-mint.jpg"
        />
      </div>
    </div>
  </div>
  <div
    class="emotion-1"
  >
    <div
      class="emotion-2"
    >
      <div
        class="emotion-3"
      >
        <input
          aria-disabled="false"
          aria-label="goldenopportunity"
          class="emotion-4"
          data-product-image-alt-text="Split Neck Tee in Golden Opportunity"
          data-product-image-src="images/product-photos/old-navy/split-neck-tee/product--goldenopportunity.jpg"
          id="0004"
          name="color-radio"
          type="radio"
          value="0004"
        />
        <img
          alt="goldenopportunity"
          aria-hidden="true"
          class="emotion-5"
          src="images/swatches/old-navy/split-neck-tee/swatch--goldenopportunity.jpg"
        />
      </div>
    </div>
  </div>
  <a
    aria-label="fui.swatches.all_colors_link_aria_label"
    class="emotion-30"
  >
    + 3
  </a>
</div>
`;

exports[`<LinkSwatches /> snapshots for large breakpoint renders default state correctly BananaRepublic in crossBrand 1`] = `
.emotion-0>div {
  margin-right: 7px;
}

.emotion-0 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
}

.emotion-1 {
  display: inline-block;
  position: relative;
}

@media (pointer: coarse) {
  .emotion-2:active label {
    -webkit-text-decoration: underline;
    text-decoration: underline;
  }
}

.emotion-2 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

@media (pointer: fine) {
  .emotion-2:hover label {
    -webkit-text-decoration: underline;
    text-decoration: underline;
  }
}

.emotion-3 .focus-visible+img {
  outline: 0;
  box-shadow: 0 0 0 0.1875rem #5CABF7;
  z-index: -1;
}

.emotion-3 {
  display: inline-block;
  position: relative;
  height: 1.875rem;
  width: 1.875rem;
  text-align: center;
  vertical-align: top;
  white-space: inherit;
  padding: 0.375rem;
}

.emotion-4 {
  opacity: 0;
  position: absolute;
  cursor: pointer;
  width: 100%;
  height: 100%;
  z-index: 1;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%,-50%);
  -moz-transform: translate(-50%,-50%);
  -ms-transform: translate(-50%,-50%);
  transform: translate(-50%,-50%);
  margin: 0;
}

.emotion-5 {
  width: 100%;
  height: 100%;
  margin: 0;
  border-radius: 50%;
  z-index: 1;
  box-shadow: ;
}

.emotion-16 {
  z-index: 2;
  position: absolute;
  width: 75%;
  height: 75%;
  top: 50%;
  left: 50%;
  margin: 0;
  -webkit-transform: translate(-50%,-50%);
  -moz-transform: translate(-50%,-50%);
  -ms-transform: translate(-50%,-50%);
  transform: translate(-50%,-50%);
  pointer-events: none;
}

.emotion-17 {
  -webkit-transform: translate(10%, 110%);
  -moz-transform: translate(10%, 110%);
  -ms-transform: translate(10%, 110%);
  transform: translate(10%, 110%);
}

.emotion-18 {
  -webkit-transform: rotate(-45deg);
  -moz-transform: rotate(-45deg);
  -ms-transform: rotate(-45deg);
  transform: rotate(-45deg);
  width: calc(100% + 0.25rem);
}

.emotion-19 {
  position: fixed;
  right: 100%;
  bottom: 100%;
}

.emotion-30 {
  font-family: 'EuclidCircularB','Hiragino Kaku Gothic Pro',Helvetica,Arial,sans-serif;
  font-weight: 300;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  color: #666;
  font-size: 0.875rem;
  margin-left: 4px;
  min-width: 22px;
  max-width: 28px;
  white-space: nowrap;
}

<div
  class="emotion-0"
>
  <div
    class="emotion-1"
  >
    <div
      class="emotion-2"
    >
      <div
        class="emotion-3"
      >
        <input
          aria-disabled="false"
          aria-label="carbon"
          class="emotion-4"
          data-product-image-alt-text="Split Neck Tee in Carbon"
          data-product-image-src="images/product-photos/old-navy/split-neck-tee/product--carbon.jpg"
          id="0000"
          name="color-radio"
          type="radio"
          value="0000"
        />
        <img
          alt="carbon"
          aria-hidden="true"
          class="emotion-5"
          src="images/swatches/old-navy/split-neck-tee/swatch--carbon.jpg"
        />
      </div>
    </div>
  </div>
  <div
    class="emotion-1"
  >
    <div
      class="emotion-2"
    >
      <div
        class="emotion-3"
      >
        <input
          aria-disabled="false"
          aria-label="coral retro stripe"
          class="emotion-4"
          data-product-image-alt-text="Split Neck Tee in Coral Retro Stripe"
          data-product-image-src="images/product-photos/old-navy/split-neck-tee/product--coral-retro-stripe.jpg"
          id="0001"
          name="color-radio"
          type="radio"
          value="0001"
        />
        <img
          alt="coral retro stripe"
          aria-hidden="true"
          class="emotion-5"
          src="images/swatches/old-navy/split-neck-tee/swatch--coral-retro-stripe.jpg"
        />
      </div>
    </div>
  </div>
  <div
    class="emotion-1"
  >
    <div
      class="emotion-2"
    >
      <div
        class="emotion-3"
      >
        <input
          aria-describedby="0002-outofstock"
          aria-disabled="true"
          aria-label="coral tropics"
          class="emotion-4"
          data-product-image-alt-text="Split Neck Tee in Coral Tropics"
          data-product-image-src="images/product-photos/old-navy/split-neck-tee/product--coral-tropics.jpg"
          id="0002"
          name="color-radio"
          type="radio"
          value="0002"
        />
        <img
          alt="coral tropics"
          aria-hidden="true"
          class="emotion-5"
          src="images/swatches/old-navy/split-neck-tee/swatch--coral-tropics.jpg"
        />
        <svg
          class="emotion-16"
          viewBox="0 0 37 23"
        >
          <g
            fill="none"
            fill-rule="evenodd"
            stroke="none"
            stroke-width="1"
          >
            <g
              class="emotion-17"
              fill="#000000"
              stroke="#FFFFFF"
            >
              <rect
                class="emotion-18"
                height="2"
              />
            </g>
          </g>
        </svg>
      </div>
      <span
        class="emotion-19"
        id="0002-outofstock"
      >
        fui.color_swatch.out_of_stock
      </span>
    </div>
  </div>
  <div
    class="emotion-1"
  >
    <div
      class="emotion-2"
    >
      <div
        class="emotion-3"
      >
        <input
          aria-disabled="false"
          aria-label="enchant mint"
          class="emotion-4"
          data-product-image-alt-text="Split Neck Tee in Enchant Mint"
          data-product-image-src="images/product-photos/old-navy/split-neck-tee/product--enchant-mint.jpg"
          id="0003"
          name="color-radio"
          type="radio"
          value="0003"
        />
        <img
          alt="enchant mint"
          aria-hidden="true"
          class="emotion-5"
          src="images/swatches/old-navy/split-neck-tee/swatch--enchant-mint.jpg"
        />
      </div>
    </div>
  </div>
  <div
    class="emotion-1"
  >
    <div
      class="emotion-2"
    >
      <div
        class="emotion-3"
      >
        <input
          aria-disabled="false"
          aria-label="goldenopportunity"
          class="emotion-4"
          data-product-image-alt-text="Split Neck Tee in Golden Opportunity"
          data-product-image-src="images/product-photos/old-navy/split-neck-tee/product--goldenopportunity.jpg"
          id="0004"
          name="color-radio"
          type="radio"
          value="0004"
        />
        <img
          alt="goldenopportunity"
          aria-hidden="true"
          class="emotion-5"
          src="images/swatches/old-navy/split-neck-tee/swatch--goldenopportunity.jpg"
        />
      </div>
    </div>
  </div>
  <a
    aria-label="fui.swatches.all_colors_link_aria_label"
    class="emotion-30"
  >
    + 3
  </a>
</div>
`;

exports[`<LinkSwatches /> snapshots for large breakpoint renders default state correctly BananaRepublicFactoryStore 1`] = `
.emotion-0>div {
  margin-right: 7px;
}

.emotion-0 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
}

.emotion-1 {
  display: inline-block;
  position: relative;
}

@media (pointer: coarse) {
  .emotion-2:active label {
    -webkit-text-decoration: underline;
    text-decoration: underline;
  }
}

.emotion-2 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

@media (pointer: fine) {
  .emotion-2:hover label {
    -webkit-text-decoration: underline;
    text-decoration: underline;
  }
}

.emotion-3 .focus-visible+img {
  outline: 0;
  box-shadow: 0 0 0 0.1875rem #5CABF7;
  z-index: -1;
}

.emotion-3 {
  display: inline-block;
  position: relative;
  height: 1.875rem;
  width: 1.875rem;
  text-align: center;
  vertical-align: top;
  white-space: inherit;
  padding: 0.375rem;
}

.emotion-4 {
  opacity: 0;
  position: absolute;
  cursor: pointer;
  width: 100%;
  height: 100%;
  z-index: 1;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%,-50%);
  -moz-transform: translate(-50%,-50%);
  -ms-transform: translate(-50%,-50%);
  transform: translate(-50%,-50%);
  margin: 0;
}

.emotion-5 {
  width: 100%;
  height: 100%;
  margin: 0;
  border-radius: 50%;
  z-index: 1;
  box-shadow: ;
}

.emotion-16 {
  z-index: 2;
  position: absolute;
  width: 75%;
  height: 75%;
  top: 50%;
  left: 50%;
  margin: 0;
  -webkit-transform: translate(-50%,-50%);
  -moz-transform: translate(-50%,-50%);
  -ms-transform: translate(-50%,-50%);
  transform: translate(-50%,-50%);
  pointer-events: none;
}

.emotion-17 {
  -webkit-transform: translate(10%, 110%);
  -moz-transform: translate(10%, 110%);
  -ms-transform: translate(10%, 110%);
  transform: translate(10%, 110%);
}

.emotion-18 {
  -webkit-transform: rotate(-45deg);
  -moz-transform: rotate(-45deg);
  -ms-transform: rotate(-45deg);
  transform: rotate(-45deg);
  width: calc(100% + 0.25rem);
}

.emotion-19 {
  position: fixed;
  right: 100%;
  bottom: 100%;
}

.emotion-30 {
  font-family: 'EuclidCircularB','Hiragino Kaku Gothic Pro',Helvetica,Arial,sans-serif;
  font-weight: 300;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  color: #666;
  font-size: 0.875rem;
  margin-left: 4px;
  min-width: 22px;
  max-width: 28px;
  white-space: nowrap;
}

<div
  class="emotion-0"
>
  <div
    class="emotion-1"
  >
    <div
      class="emotion-2"
    >
      <div
        class="emotion-3"
      >
        <input
          aria-disabled="false"
          aria-label="carbon"
          class="emotion-4"
          data-product-image-alt-text="Split Neck Tee in Carbon"
          data-product-image-src="images/product-photos/old-navy/split-neck-tee/product--carbon.jpg"
          id="0000"
          name="color-radio"
          type="radio"
          value="0000"
        />
        <img
          alt="carbon"
          aria-hidden="true"
          class="emotion-5"
          src="images/swatches/old-navy/split-neck-tee/swatch--carbon.jpg"
        />
      </div>
    </div>
  </div>
  <div
    class="emotion-1"
  >
    <div
      class="emotion-2"
    >
      <div
        class="emotion-3"
      >
        <input
          aria-disabled="false"
          aria-label="coral retro stripe"
          class="emotion-4"
          data-product-image-alt-text="Split Neck Tee in Coral Retro Stripe"
          data-product-image-src="images/product-photos/old-navy/split-neck-tee/product--coral-retro-stripe.jpg"
          id="0001"
          name="color-radio"
          type="radio"
          value="0001"
        />
        <img
          alt="coral retro stripe"
          aria-hidden="true"
          class="emotion-5"
          src="images/swatches/old-navy/split-neck-tee/swatch--coral-retro-stripe.jpg"
        />
      </div>
    </div>
  </div>
  <div
    class="emotion-1"
  >
    <div
      class="emotion-2"
    >
      <div
        class="emotion-3"
      >
        <input
          aria-describedby="0002-outofstock"
          aria-disabled="true"
          aria-label="coral tropics"
          class="emotion-4"
          data-product-image-alt-text="Split Neck Tee in Coral Tropics"
          data-product-image-src="images/product-photos/old-navy/split-neck-tee/product--coral-tropics.jpg"
          id="0002"
          name="color-radio"
          type="radio"
          value="0002"
        />
        <img
          alt="coral tropics"
          aria-hidden="true"
          class="emotion-5"
          src="images/swatches/old-navy/split-neck-tee/swatch--coral-tropics.jpg"
        />
        <svg
          class="emotion-16"
          viewBox="0 0 37 23"
        >
          <g
            fill="none"
            fill-rule="evenodd"
            stroke="none"
            stroke-width="1"
          >
            <g
              class="emotion-17"
              fill="#000000"
              stroke="#FFFFFF"
            >
              <rect
                class="emotion-18"
                height="2"
              />
            </g>
          </g>
        </svg>
      </div>
      <span
        class="emotion-19"
        id="0002-outofstock"
      >
        fui.color_swatch.out_of_stock
      </span>
    </div>
  </div>
  <div
    class="emotion-1"
  >
    <div
      class="emotion-2"
    >
      <div
        class="emotion-3"
      >
        <input
          aria-disabled="false"
          aria-label="enchant mint"
          class="emotion-4"
          data-product-image-alt-text="Split Neck Tee in Enchant Mint"
          data-product-image-src="images/product-photos/old-navy/split-neck-tee/product--enchant-mint.jpg"
          id="0003"
          name="color-radio"
          type="radio"
          value="0003"
        />
        <img
          alt="enchant mint"
          aria-hidden="true"
          class="emotion-5"
          src="images/swatches/old-navy/split-neck-tee/swatch--enchant-mint.jpg"
        />
      </div>
    </div>
  </div>
  <div
    class="emotion-1"
  >
    <div
      class="emotion-2"
    >
      <div
        class="emotion-3"
      >
        <input
          aria-disabled="false"
          aria-label="goldenopportunity"
          class="emotion-4"
          data-product-image-alt-text="Split Neck Tee in Golden Opportunity"
          data-product-image-src="images/product-photos/old-navy/split-neck-tee/product--goldenopportunity.jpg"
          id="0004"
          name="color-radio"
          type="radio"
          value="0004"
        />
        <img
          alt="goldenopportunity"
          aria-hidden="true"
          class="emotion-5"
          src="images/swatches/old-navy/split-neck-tee/swatch--goldenopportunity.jpg"
        />
      </div>
    </div>
  </div>
  <a
    aria-label="fui.swatches.all_colors_link_aria_label"
    class="emotion-30"
  >
    + 3
  </a>
</div>
`;

exports[`<LinkSwatches /> snapshots for large breakpoint renders default state correctly BananaRepublicFactoryStore in crossBrand 1`] = `
.emotion-0>div {
  margin-right: 7px;
}

.emotion-0 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
}

.emotion-1 {
  display: inline-block;
  position: relative;
}

@media (pointer: coarse) {
  .emotion-2:active label {
    -webkit-text-decoration: underline;
    text-decoration: underline;
  }
}

.emotion-2 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

@media (pointer: fine) {
  .emotion-2:hover label {
    -webkit-text-decoration: underline;
    text-decoration: underline;
  }
}

.emotion-3 .focus-visible+img {
  outline: 0;
  box-shadow: 0 0 0 0.1875rem #5CABF7;
  z-index: -1;
}

.emotion-3 {
  display: inline-block;
  position: relative;
  height: 1.875rem;
  width: 1.875rem;
  text-align: center;
  vertical-align: top;
  white-space: inherit;
  padding: 0.375rem;
}

.emotion-4 {
  opacity: 0;
  position: absolute;
  cursor: pointer;
  width: 100%;
  height: 100%;
  z-index: 1;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%,-50%);
  -moz-transform: translate(-50%,-50%);
  -ms-transform: translate(-50%,-50%);
  transform: translate(-50%,-50%);
  margin: 0;
}

.emotion-5 {
  width: 100%;
  height: 100%;
  margin: 0;
  border-radius: 50%;
  z-index: 1;
  box-shadow: ;
}

.emotion-16 {
  z-index: 2;
  position: absolute;
  width: 75%;
  height: 75%;
  top: 50%;
  left: 50%;
  margin: 0;
  -webkit-transform: translate(-50%,-50%);
  -moz-transform: translate(-50%,-50%);
  -ms-transform: translate(-50%,-50%);
  transform: translate(-50%,-50%);
  pointer-events: none;
}

.emotion-17 {
  -webkit-transform: translate(10%, 110%);
  -moz-transform: translate(10%, 110%);
  -ms-transform: translate(10%, 110%);
  transform: translate(10%, 110%);
}

.emotion-18 {
  -webkit-transform: rotate(-45deg);
  -moz-transform: rotate(-45deg);
  -ms-transform: rotate(-45deg);
  transform: rotate(-45deg);
  width: calc(100% + 0.25rem);
}

.emotion-19 {
  position: fixed;
  right: 100%;
  bottom: 100%;
}

.emotion-30 {
  font-family: 'EuclidCircularB','Hiragino Kaku Gothic Pro',Helvetica,Arial,sans-serif;
  font-weight: 300;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  color: #666;
  font-size: 0.875rem;
  margin-left: 4px;
  min-width: 22px;
  max-width: 28px;
  white-space: nowrap;
}

<div
  class="emotion-0"
>
  <div
    class="emotion-1"
  >
    <div
      class="emotion-2"
    >
      <div
        class="emotion-3"
      >
        <input
          aria-disabled="false"
          aria-label="carbon"
          class="emotion-4"
          data-product-image-alt-text="Split Neck Tee in Carbon"
          data-product-image-src="images/product-photos/old-navy/split-neck-tee/product--carbon.jpg"
          id="0000"
          name="color-radio"
          type="radio"
          value="0000"
        />
        <img
          alt="carbon"
          aria-hidden="true"
          class="emotion-5"
          src="images/swatches/old-navy/split-neck-tee/swatch--carbon.jpg"
        />
      </div>
    </div>
  </div>
  <div
    class="emotion-1"
  >
    <div
      class="emotion-2"
    >
      <div
        class="emotion-3"
      >
        <input
          aria-disabled="false"
          aria-label="coral retro stripe"
          class="emotion-4"
          data-product-image-alt-text="Split Neck Tee in Coral Retro Stripe"
          data-product-image-src="images/product-photos/old-navy/split-neck-tee/product--coral-retro-stripe.jpg"
          id="0001"
          name="color-radio"
          type="radio"
          value="0001"
        />
        <img
          alt="coral retro stripe"
          aria-hidden="true"
          class="emotion-5"
          src="images/swatches/old-navy/split-neck-tee/swatch--coral-retro-stripe.jpg"
        />
      </div>
    </div>
  </div>
  <div
    class="emotion-1"
  >
    <div
      class="emotion-2"
    >
      <div
        class="emotion-3"
      >
        <input
          aria-describedby="0002-outofstock"
          aria-disabled="true"
          aria-label="coral tropics"
          class="emotion-4"
          data-product-image-alt-text="Split Neck Tee in Coral Tropics"
          data-product-image-src="images/product-photos/old-navy/split-neck-tee/product--coral-tropics.jpg"
          id="0002"
          name="color-radio"
          type="radio"
          value="0002"
        />
        <img
          alt="coral tropics"
          aria-hidden="true"
          class="emotion-5"
          src="images/swatches/old-navy/split-neck-tee/swatch--coral-tropics.jpg"
        />
        <svg
          class="emotion-16"
          viewBox="0 0 37 23"
        >
          <g
            fill="none"
            fill-rule="evenodd"
            stroke="none"
            stroke-width="1"
          >
            <g
              class="emotion-17"
              fill="#000000"
              stroke="#FFFFFF"
            >
              <rect
                class="emotion-18"
                height="2"
              />
            </g>
          </g>
        </svg>
      </div>
      <span
        class="emotion-19"
        id="0002-outofstock"
      >
        fui.color_swatch.out_of_stock
      </span>
    </div>
  </div>
  <div
    class="emotion-1"
  >
    <div
      class="emotion-2"
    >
      <div
        class="emotion-3"
      >
        <input
          aria-disabled="false"
          aria-label="enchant mint"
          class="emotion-4"
          data-product-image-alt-text="Split Neck Tee in Enchant Mint"
          data-product-image-src="images/product-photos/old-navy/split-neck-tee/product--enchant-mint.jpg"
          id="0003"
          name="color-radio"
          type="radio"
          value="0003"
        />
        <img
          alt="enchant mint"
          aria-hidden="true"
          class="emotion-5"
          src="images/swatches/old-navy/split-neck-tee/swatch--enchant-mint.jpg"
        />
      </div>
    </div>
  </div>
  <div
    class="emotion-1"
  >
    <div
      class="emotion-2"
    >
      <div
        class="emotion-3"
      >
        <input
          aria-disabled="false"
          aria-label="goldenopportunity"
          class="emotion-4"
          data-product-image-alt-text="Split Neck Tee in Golden Opportunity"
          data-product-image-src="images/product-photos/old-navy/split-neck-tee/product--goldenopportunity.jpg"
          id="0004"
          name="color-radio"
          type="radio"
          value="0004"
        />
        <img
          alt="goldenopportunity"
          aria-hidden="true"
          class="emotion-5"
          src="images/swatches/old-navy/split-neck-tee/swatch--goldenopportunity.jpg"
        />
      </div>
    </div>
  </div>
  <a
    aria-label="fui.swatches.all_colors_link_aria_label"
    class="emotion-30"
  >
    + 3
  </a>
</div>
`;

exports[`<LinkSwatches /> snapshots for large breakpoint renders default state correctly Gap 1`] = `
.emotion-0>div {
  margin-right: 7px;
}

.emotion-0 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
}

.emotion-1 {
  display: inline-block;
  position: relative;
}

@media (pointer: coarse) {
  .emotion-2:active label {
    -webkit-text-decoration: underline;
    text-decoration: underline;
  }
}

.emotion-2 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

@media (pointer: fine) {
  .emotion-2:hover label {
    -webkit-text-decoration: underline;
    text-decoration: underline;
  }
}

.emotion-3 .focus-visible+img {
  outline: 0;
  box-shadow: 0 0 0 0.1875rem #5CABF7;
  z-index: -1;
}

.emotion-3 {
  display: inline-block;
  position: relative;
  height: 1.875rem;
  width: 1.875rem;
  text-align: center;
  vertical-align: top;
  white-space: inherit;
  padding: 0.375rem;
}

.emotion-4 {
  opacity: 0;
  position: absolute;
  cursor: pointer;
  width: 100%;
  height: 100%;
  z-index: 1;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%,-50%);
  -moz-transform: translate(-50%,-50%);
  -ms-transform: translate(-50%,-50%);
  transform: translate(-50%,-50%);
  margin: 0;
}

.emotion-5 {
  width: 100%;
  height: 100%;
  margin: 0;
  border-radius: 50%;
  z-index: 1;
  box-shadow: ;
}

.emotion-16 {
  z-index: 2;
  position: absolute;
  width: 75%;
  height: 75%;
  top: 50%;
  left: 50%;
  margin: 0;
  -webkit-transform: translate(-50%,-50%);
  -moz-transform: translate(-50%,-50%);
  -ms-transform: translate(-50%,-50%);
  transform: translate(-50%,-50%);
  pointer-events: none;
}

.emotion-17 {
  -webkit-transform: translate(10%, 110%);
  -moz-transform: translate(10%, 110%);
  -ms-transform: translate(10%, 110%);
  transform: translate(10%, 110%);
}

.emotion-18 {
  -webkit-transform: rotate(-45deg);
  -moz-transform: rotate(-45deg);
  -ms-transform: rotate(-45deg);
  transform: rotate(-45deg);
  width: calc(100% + 0.25rem);
}

.emotion-19 {
  position: fixed;
  right: 100%;
  bottom: 100%;
}

.emotion-30 {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  color: #666;
  font-size: 0.875rem;
  margin-left: 4px;
  min-width: 22px;
  max-width: 28px;
  white-space: nowrap;
}

<div
  class="emotion-0"
>
  <div
    class="emotion-1"
  >
    <div
      class="emotion-2"
    >
      <div
        class="emotion-3"
      >
        <input
          aria-disabled="false"
          aria-label="carbon"
          class="emotion-4"
          data-product-image-alt-text="Split Neck Tee in Carbon"
          data-product-image-src="images/product-photos/old-navy/split-neck-tee/product--carbon.jpg"
          id="0000"
          name="color-radio"
          type="radio"
          value="0000"
        />
        <img
          alt="carbon"
          aria-hidden="true"
          class="emotion-5"
          src="images/swatches/old-navy/split-neck-tee/swatch--carbon.jpg"
        />
      </div>
    </div>
  </div>
  <div
    class="emotion-1"
  >
    <div
      class="emotion-2"
    >
      <div
        class="emotion-3"
      >
        <input
          aria-disabled="false"
          aria-label="coral retro stripe"
          class="emotion-4"
          data-product-image-alt-text="Split Neck Tee in Coral Retro Stripe"
          data-product-image-src="images/product-photos/old-navy/split-neck-tee/product--coral-retro-stripe.jpg"
          id="0001"
          name="color-radio"
          type="radio"
          value="0001"
        />
        <img
          alt="coral retro stripe"
          aria-hidden="true"
          class="emotion-5"
          src="images/swatches/old-navy/split-neck-tee/swatch--coral-retro-stripe.jpg"
        />
      </div>
    </div>
  </div>
  <div
    class="emotion-1"
  >
    <div
      class="emotion-2"
    >
      <div
        class="emotion-3"
      >
        <input
          aria-describedby="0002-outofstock"
          aria-disabled="true"
          aria-label="coral tropics"
          class="emotion-4"
          data-product-image-alt-text="Split Neck Tee in Coral Tropics"
          data-product-image-src="images/product-photos/old-navy/split-neck-tee/product--coral-tropics.jpg"
          id="0002"
          name="color-radio"
          type="radio"
          value="0002"
        />
        <img
          alt="coral tropics"
          aria-hidden="true"
          class="emotion-5"
          src="images/swatches/old-navy/split-neck-tee/swatch--coral-tropics.jpg"
        />
        <svg
          class="emotion-16"
          viewBox="0 0 37 23"
        >
          <g
            fill="none"
            fill-rule="evenodd"
            stroke="none"
            stroke-width="1"
          >
            <g
              class="emotion-17"
              fill="#000000"
              stroke="#FFFFFF"
            >
              <rect
                class="emotion-18"
                height="2"
              />
            </g>
          </g>
        </svg>
      </div>
      <span
        class="emotion-19"
        id="0002-outofstock"
      >
        fui.color_swatch.out_of_stock
      </span>
    </div>
  </div>
  <div
    class="emotion-1"
  >
    <div
      class="emotion-2"
    >
      <div
        class="emotion-3"
      >
        <input
          aria-disabled="false"
          aria-label="enchant mint"
          class="emotion-4"
          data-product-image-alt-text="Split Neck Tee in Enchant Mint"
          data-product-image-src="images/product-photos/old-navy/split-neck-tee/product--enchant-mint.jpg"
          id="0003"
          name="color-radio"
          type="radio"
          value="0003"
        />
        <img
          alt="enchant mint"
          aria-hidden="true"
          class="emotion-5"
          src="images/swatches/old-navy/split-neck-tee/swatch--enchant-mint.jpg"
        />
      </div>
    </div>
  </div>
  <div
    class="emotion-1"
  >
    <div
      class="emotion-2"
    >
      <div
        class="emotion-3"
      >
        <input
          aria-disabled="false"
          aria-label="goldenopportunity"
          class="emotion-4"
          data-product-image-alt-text="Split Neck Tee in Golden Opportunity"
          data-product-image-src="images/product-photos/old-navy/split-neck-tee/product--goldenopportunity.jpg"
          id="0004"
          name="color-radio"
          type="radio"
          value="0004"
        />
        <img
          alt="goldenopportunity"
          aria-hidden="true"
          class="emotion-5"
          src="images/swatches/old-navy/split-neck-tee/swatch--goldenopportunity.jpg"
        />
      </div>
    </div>
  </div>
  <a
    aria-label="fui.swatches.all_colors_link_aria_label"
    class="emotion-30"
  >
    + 3
  </a>
</div>
`;

exports[`<LinkSwatches /> snapshots for large breakpoint renders default state correctly Gap in crossBrand 1`] = `
.emotion-0>div {
  margin-right: 7px;
}

.emotion-0 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
}

.emotion-1 {
  display: inline-block;
  position: relative;
}

@media (pointer: coarse) {
  .emotion-2:active label {
    -webkit-text-decoration: underline;
    text-decoration: underline;
  }
}

.emotion-2 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

@media (pointer: fine) {
  .emotion-2:hover label {
    -webkit-text-decoration: underline;
    text-decoration: underline;
  }
}

.emotion-3 .focus-visible+img {
  outline: 0;
  box-shadow: 0 0 0 0.1875rem #5CABF7;
  z-index: -1;
}

.emotion-3 {
  display: inline-block;
  position: relative;
  height: 1.875rem;
  width: 1.875rem;
  text-align: center;
  vertical-align: top;
  white-space: inherit;
  padding: 0.375rem;
}

.emotion-4 {
  opacity: 0;
  position: absolute;
  cursor: pointer;
  width: 100%;
  height: 100%;
  z-index: 1;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%,-50%);
  -moz-transform: translate(-50%,-50%);
  -ms-transform: translate(-50%,-50%);
  transform: translate(-50%,-50%);
  margin: 0;
}

.emotion-5 {
  width: 100%;
  height: 100%;
  margin: 0;
  border-radius: 50%;
  z-index: 1;
  box-shadow: ;
}

.emotion-16 {
  z-index: 2;
  position: absolute;
  width: 75%;
  height: 75%;
  top: 50%;
  left: 50%;
  margin: 0;
  -webkit-transform: translate(-50%,-50%);
  -moz-transform: translate(-50%,-50%);
  -ms-transform: translate(-50%,-50%);
  transform: translate(-50%,-50%);
  pointer-events: none;
}

.emotion-17 {
  -webkit-transform: translate(10%, 110%);
  -moz-transform: translate(10%, 110%);
  -ms-transform: translate(10%, 110%);
  transform: translate(10%, 110%);
}

.emotion-18 {
  -webkit-transform: rotate(-45deg);
  -moz-transform: rotate(-45deg);
  -ms-transform: rotate(-45deg);
  transform: rotate(-45deg);
  width: calc(100% + 0.25rem);
}

.emotion-19 {
  position: fixed;
  right: 100%;
  bottom: 100%;
}

.emotion-30 {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  color: #666;
  font-size: 0.875rem;
  margin-left: 4px;
  min-width: 22px;
  max-width: 28px;
  white-space: nowrap;
}

<div
  class="emotion-0"
>
  <div
    class="emotion-1"
  >
    <div
      class="emotion-2"
    >
      <div
        class="emotion-3"
      >
        <input
          aria-disabled="false"
          aria-label="carbon"
          class="emotion-4"
          data-product-image-alt-text="Split Neck Tee in Carbon"
          data-product-image-src="images/product-photos/old-navy/split-neck-tee/product--carbon.jpg"
          id="0000"
          name="color-radio"
          type="radio"
          value="0000"
        />
        <img
          alt="carbon"
          aria-hidden="true"
          class="emotion-5"
          src="images/swatches/old-navy/split-neck-tee/swatch--carbon.jpg"
        />
      </div>
    </div>
  </div>
  <div
    class="emotion-1"
  >
    <div
      class="emotion-2"
    >
      <div
        class="emotion-3"
      >
        <input
          aria-disabled="false"
          aria-label="coral retro stripe"
          class="emotion-4"
          data-product-image-alt-text="Split Neck Tee in Coral Retro Stripe"
          data-product-image-src="images/product-photos/old-navy/split-neck-tee/product--coral-retro-stripe.jpg"
          id="0001"
          name="color-radio"
          type="radio"
          value="0001"
        />
        <img
          alt="coral retro stripe"
          aria-hidden="true"
          class="emotion-5"
          src="images/swatches/old-navy/split-neck-tee/swatch--coral-retro-stripe.jpg"
        />
      </div>
    </div>
  </div>
  <div
    class="emotion-1"
  >
    <div
      class="emotion-2"
    >
      <div
        class="emotion-3"
      >
        <input
          aria-describedby="0002-outofstock"
          aria-disabled="true"
          aria-label="coral tropics"
          class="emotion-4"
          data-product-image-alt-text="Split Neck Tee in Coral Tropics"
          data-product-image-src="images/product-photos/old-navy/split-neck-tee/product--coral-tropics.jpg"
          id="0002"
          name="color-radio"
          type="radio"
          value="0002"
        />
        <img
          alt="coral tropics"
          aria-hidden="true"
          class="emotion-5"
          src="images/swatches/old-navy/split-neck-tee/swatch--coral-tropics.jpg"
        />
        <svg
          class="emotion-16"
          viewBox="0 0 37 23"
        >
          <g
            fill="none"
            fill-rule="evenodd"
            stroke="none"
            stroke-width="1"
          >
            <g
              class="emotion-17"
              fill="#000000"
              stroke="#FFFFFF"
            >
              <rect
                class="emotion-18"
                height="2"
              />
            </g>
          </g>
        </svg>
      </div>
      <span
        class="emotion-19"
        id="0002-outofstock"
      >
        fui.color_swatch.out_of_stock
      </span>
    </div>
  </div>
  <div
    class="emotion-1"
  >
    <div
      class="emotion-2"
    >
      <div
        class="emotion-3"
      >
        <input
          aria-disabled="false"
          aria-label="enchant mint"
          class="emotion-4"
          data-product-image-alt-text="Split Neck Tee in Enchant Mint"
          data-product-image-src="images/product-photos/old-navy/split-neck-tee/product--enchant-mint.jpg"
          id="0003"
          name="color-radio"
          type="radio"
          value="0003"
        />
        <img
          alt="enchant mint"
          aria-hidden="true"
          class="emotion-5"
          src="images/swatches/old-navy/split-neck-tee/swatch--enchant-mint.jpg"
        />
      </div>
    </div>
  </div>
  <div
    class="emotion-1"
  >
    <div
      class="emotion-2"
    >
      <div
        class="emotion-3"
      >
        <input
          aria-disabled="false"
          aria-label="goldenopportunity"
          class="emotion-4"
          data-product-image-alt-text="Split Neck Tee in Golden Opportunity"
          data-product-image-src="images/product-photos/old-navy/split-neck-tee/product--goldenopportunity.jpg"
          id="0004"
          name="color-radio"
          type="radio"
          value="0004"
        />
        <img
          alt="goldenopportunity"
          aria-hidden="true"
          class="emotion-5"
          src="images/swatches/old-navy/split-neck-tee/swatch--goldenopportunity.jpg"
        />
      </div>
    </div>
  </div>
  <a
    aria-label="fui.swatches.all_colors_link_aria_label"
    class="emotion-30"
  >
    + 3
  </a>
</div>
`;

exports[`<LinkSwatches /> snapshots for large breakpoint renders default state correctly GapFactoryStore 1`] = `
.emotion-0>div {
  margin-right: 7px;
}

.emotion-0 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
}

.emotion-1 {
  display: inline-block;
  position: relative;
}

@media (pointer: coarse) {
  .emotion-2:active label {
    -webkit-text-decoration: underline;
    text-decoration: underline;
  }
}

.emotion-2 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

@media (pointer: fine) {
  .emotion-2:hover label {
    -webkit-text-decoration: underline;
    text-decoration: underline;
  }
}

.emotion-3 .focus-visible+img {
  outline: 0;
  box-shadow: 0 0 0 0.1875rem #5CABF7;
  z-index: -1;
}

.emotion-3 {
  display: inline-block;
  position: relative;
  height: 1.875rem;
  width: 1.875rem;
  text-align: center;
  vertical-align: top;
  white-space: inherit;
  padding: 0.375rem;
}

.emotion-4 {
  opacity: 0;
  position: absolute;
  cursor: pointer;
  width: 100%;
  height: 100%;
  z-index: 1;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%,-50%);
  -moz-transform: translate(-50%,-50%);
  -ms-transform: translate(-50%,-50%);
  transform: translate(-50%,-50%);
  margin: 0;
}

.emotion-5 {
  width: 100%;
  height: 100%;
  margin: 0;
  border-radius: 50%;
  z-index: 1;
  box-shadow: ;
}

.emotion-16 {
  z-index: 2;
  position: absolute;
  width: 75%;
  height: 75%;
  top: 50%;
  left: 50%;
  margin: 0;
  -webkit-transform: translate(-50%,-50%);
  -moz-transform: translate(-50%,-50%);
  -ms-transform: translate(-50%,-50%);
  transform: translate(-50%,-50%);
  pointer-events: none;
}

.emotion-17 {
  -webkit-transform: translate(10%, 110%);
  -moz-transform: translate(10%, 110%);
  -ms-transform: translate(10%, 110%);
  transform: translate(10%, 110%);
}

.emotion-18 {
  -webkit-transform: rotate(-45deg);
  -moz-transform: rotate(-45deg);
  -ms-transform: rotate(-45deg);
  transform: rotate(-45deg);
  width: calc(100% + 0.25rem);
}

.emotion-19 {
  position: fixed;
  right: 100%;
  bottom: 100%;
}

.emotion-30 {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  color: #666;
  font-size: 0.875rem;
  margin-left: 4px;
  min-width: 22px;
  max-width: 28px;
  white-space: nowrap;
}

<div
  class="emotion-0"
>
  <div
    class="emotion-1"
  >
    <div
      class="emotion-2"
    >
      <div
        class="emotion-3"
      >
        <input
          aria-disabled="false"
          aria-label="carbon"
          class="emotion-4"
          data-product-image-alt-text="Split Neck Tee in Carbon"
          data-product-image-src="images/product-photos/old-navy/split-neck-tee/product--carbon.jpg"
          id="0000"
          name="color-radio"
          type="radio"
          value="0000"
        />
        <img
          alt="carbon"
          aria-hidden="true"
          class="emotion-5"
          src="images/swatches/old-navy/split-neck-tee/swatch--carbon.jpg"
        />
      </div>
    </div>
  </div>
  <div
    class="emotion-1"
  >
    <div
      class="emotion-2"
    >
      <div
        class="emotion-3"
      >
        <input
          aria-disabled="false"
          aria-label="coral retro stripe"
          class="emotion-4"
          data-product-image-alt-text="Split Neck Tee in Coral Retro Stripe"
          data-product-image-src="images/product-photos/old-navy/split-neck-tee/product--coral-retro-stripe.jpg"
          id="0001"
          name="color-radio"
          type="radio"
          value="0001"
        />
        <img
          alt="coral retro stripe"
          aria-hidden="true"
          class="emotion-5"
          src="images/swatches/old-navy/split-neck-tee/swatch--coral-retro-stripe.jpg"
        />
      </div>
    </div>
  </div>
  <div
    class="emotion-1"
  >
    <div
      class="emotion-2"
    >
      <div
        class="emotion-3"
      >
        <input
          aria-describedby="0002-outofstock"
          aria-disabled="true"
          aria-label="coral tropics"
          class="emotion-4"
          data-product-image-alt-text="Split Neck Tee in Coral Tropics"
          data-product-image-src="images/product-photos/old-navy/split-neck-tee/product--coral-tropics.jpg"
          id="0002"
          name="color-radio"
          type="radio"
          value="0002"
        />
        <img
          alt="coral tropics"
          aria-hidden="true"
          class="emotion-5"
          src="images/swatches/old-navy/split-neck-tee/swatch--coral-tropics.jpg"
        />
        <svg
          class="emotion-16"
          viewBox="0 0 37 23"
        >
          <g
            fill="none"
            fill-rule="evenodd"
            stroke="none"
            stroke-width="1"
          >
            <g
              class="emotion-17"
              fill="#000000"
              stroke="#FFFFFF"
            >
              <rect
                class="emotion-18"
                height="2"
              />
            </g>
          </g>
        </svg>
      </div>
      <span
        class="emotion-19"
        id="0002-outofstock"
      >
        fui.color_swatch.out_of_stock
      </span>
    </div>
  </div>
  <div
    class="emotion-1"
  >
    <div
      class="emotion-2"
    >
      <div
        class="emotion-3"
      >
        <input
          aria-disabled="false"
          aria-label="enchant mint"
          class="emotion-4"
          data-product-image-alt-text="Split Neck Tee in Enchant Mint"
          data-product-image-src="images/product-photos/old-navy/split-neck-tee/product--enchant-mint.jpg"
          id="0003"
          name="color-radio"
          type="radio"
          value="0003"
        />
        <img
          alt="enchant mint"
          aria-hidden="true"
          class="emotion-5"
          src="images/swatches/old-navy/split-neck-tee/swatch--enchant-mint.jpg"
        />
      </div>
    </div>
  </div>
  <div
    class="emotion-1"
  >
    <div
      class="emotion-2"
    >
      <div
        class="emotion-3"
      >
        <input
          aria-disabled="false"
          aria-label="goldenopportunity"
          class="emotion-4"
          data-product-image-alt-text="Split Neck Tee in Golden Opportunity"
          data-product-image-src="images/product-photos/old-navy/split-neck-tee/product--goldenopportunity.jpg"
          id="0004"
          name="color-radio"
          type="radio"
          value="0004"
        />
        <img
          alt="goldenopportunity"
          aria-hidden="true"
          class="emotion-5"
          src="images/swatches/old-navy/split-neck-tee/swatch--goldenopportunity.jpg"
        />
      </div>
    </div>
  </div>
  <a
    aria-label="fui.swatches.all_colors_link_aria_label"
    class="emotion-30"
  >
    + 3
  </a>
</div>
`;

exports[`<LinkSwatches /> snapshots for large breakpoint renders default state correctly GapFactoryStore in crossBrand 1`] = `
.emotion-0>div {
  margin-right: 7px;
}

.emotion-0 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
}

.emotion-1 {
  display: inline-block;
  position: relative;
}

@media (pointer: coarse) {
  .emotion-2:active label {
    -webkit-text-decoration: underline;
    text-decoration: underline;
  }
}

.emotion-2 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

@media (pointer: fine) {
  .emotion-2:hover label {
    -webkit-text-decoration: underline;
    text-decoration: underline;
  }
}

.emotion-3 .focus-visible+img {
  outline: 0;
  box-shadow: 0 0 0 0.1875rem #5CABF7;
  z-index: -1;
}

.emotion-3 {
  display: inline-block;
  position: relative;
  height: 1.875rem;
  width: 1.875rem;
  text-align: center;
  vertical-align: top;
  white-space: inherit;
  padding: 0.375rem;
}

.emotion-4 {
  opacity: 0;
  position: absolute;
  cursor: pointer;
  width: 100%;
  height: 100%;
  z-index: 1;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%,-50%);
  -moz-transform: translate(-50%,-50%);
  -ms-transform: translate(-50%,-50%);
  transform: translate(-50%,-50%);
  margin: 0;
}

.emotion-5 {
  width: 100%;
  height: 100%;
  margin: 0;
  border-radius: 50%;
  z-index: 1;
  box-shadow: ;
}

.emotion-16 {
  z-index: 2;
  position: absolute;
  width: 75%;
  height: 75%;
  top: 50%;
  left: 50%;
  margin: 0;
  -webkit-transform: translate(-50%,-50%);
  -moz-transform: translate(-50%,-50%);
  -ms-transform: translate(-50%,-50%);
  transform: translate(-50%,-50%);
  pointer-events: none;
}

.emotion-17 {
  -webkit-transform: translate(10%, 110%);
  -moz-transform: translate(10%, 110%);
  -ms-transform: translate(10%, 110%);
  transform: translate(10%, 110%);
}

.emotion-18 {
  -webkit-transform: rotate(-45deg);
  -moz-transform: rotate(-45deg);
  -ms-transform: rotate(-45deg);
  transform: rotate(-45deg);
  width: calc(100% + 0.25rem);
}

.emotion-19 {
  position: fixed;
  right: 100%;
  bottom: 100%;
}

.emotion-30 {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  color: #666;
  font-size: 0.875rem;
  margin-left: 4px;
  min-width: 22px;
  max-width: 28px;
  white-space: nowrap;
}

<div
  class="emotion-0"
>
  <div
    class="emotion-1"
  >
    <div
      class="emotion-2"
    >
      <div
        class="emotion-3"
      >
        <input
          aria-disabled="false"
          aria-label="carbon"
          class="emotion-4"
          data-product-image-alt-text="Split Neck Tee in Carbon"
          data-product-image-src="images/product-photos/old-navy/split-neck-tee/product--carbon.jpg"
          id="0000"
          name="color-radio"
          type="radio"
          value="0000"
        />
        <img
          alt="carbon"
          aria-hidden="true"
          class="emotion-5"
          src="images/swatches/old-navy/split-neck-tee/swatch--carbon.jpg"
        />
      </div>
    </div>
  </div>
  <div
    class="emotion-1"
  >
    <div
      class="emotion-2"
    >
      <div
        class="emotion-3"
      >
        <input
          aria-disabled="false"
          aria-label="coral retro stripe"
          class="emotion-4"
          data-product-image-alt-text="Split Neck Tee in Coral Retro Stripe"
          data-product-image-src="images/product-photos/old-navy/split-neck-tee/product--coral-retro-stripe.jpg"
          id="0001"
          name="color-radio"
          type="radio"
          value="0001"
        />
        <img
          alt="coral retro stripe"
          aria-hidden="true"
          class="emotion-5"
          src="images/swatches/old-navy/split-neck-tee/swatch--coral-retro-stripe.jpg"
        />
      </div>
    </div>
  </div>
  <div
    class="emotion-1"
  >
    <div
      class="emotion-2"
    >
      <div
        class="emotion-3"
      >
        <input
          aria-describedby="0002-outofstock"
          aria-disabled="true"
          aria-label="coral tropics"
          class="emotion-4"
          data-product-image-alt-text="Split Neck Tee in Coral Tropics"
          data-product-image-src="images/product-photos/old-navy/split-neck-tee/product--coral-tropics.jpg"
          id="0002"
          name="color-radio"
          type="radio"
          value="0002"
        />
        <img
          alt="coral tropics"
          aria-hidden="true"
          class="emotion-5"
          src="images/swatches/old-navy/split-neck-tee/swatch--coral-tropics.jpg"
        />
        <svg
          class="emotion-16"
          viewBox="0 0 37 23"
        >
          <g
            fill="none"
            fill-rule="evenodd"
            stroke="none"
            stroke-width="1"
          >
            <g
              class="emotion-17"
              fill="#000000"
              stroke="#FFFFFF"
            >
              <rect
                class="emotion-18"
                height="2"
              />
            </g>
          </g>
        </svg>
      </div>
      <span
        class="emotion-19"
        id="0002-outofstock"
      >
        fui.color_swatch.out_of_stock
      </span>
    </div>
  </div>
  <div
    class="emotion-1"
  >
    <div
      class="emotion-2"
    >
      <div
        class="emotion-3"
      >
        <input
          aria-disabled="false"
          aria-label="enchant mint"
          class="emotion-4"
          data-product-image-alt-text="Split Neck Tee in Enchant Mint"
          data-product-image-src="images/product-photos/old-navy/split-neck-tee/product--enchant-mint.jpg"
          id="0003"
          name="color-radio"
          type="radio"
          value="0003"
        />
        <img
          alt="enchant mint"
          aria-hidden="true"
          class="emotion-5"
          src="images/swatches/old-navy/split-neck-tee/swatch--enchant-mint.jpg"
        />
      </div>
    </div>
  </div>
  <div
    class="emotion-1"
  >
    <div
      class="emotion-2"
    >
      <div
        class="emotion-3"
      >
        <input
          aria-disabled="false"
          aria-label="goldenopportunity"
          class="emotion-4"
          data-product-image-alt-text="Split Neck Tee in Golden Opportunity"
          data-product-image-src="images/product-photos/old-navy/split-neck-tee/product--goldenopportunity.jpg"
          id="0004"
          name="color-radio"
          type="radio"
          value="0004"
        />
        <img
          alt="goldenopportunity"
          aria-hidden="true"
          class="emotion-5"
          src="images/swatches/old-navy/split-neck-tee/swatch--goldenopportunity.jpg"
        />
      </div>
    </div>
  </div>
  <a
    aria-label="fui.swatches.all_colors_link_aria_label"
    class="emotion-30"
  >
    + 3
  </a>
</div>
`;

exports[`<LinkSwatches /> snapshots for large breakpoint renders default state correctly OldNavy 1`] = `
.emotion-0>div {
  margin-right: 7px;
}

.emotion-0 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
}

.emotion-1 {
  display: inline-block;
  position: relative;
}

@media (pointer: coarse) {
  .emotion-2:active label {
    -webkit-text-decoration: underline;
    text-decoration: underline;
  }
}

.emotion-2 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

@media (pointer: fine) {
  .emotion-2:hover label {
    -webkit-text-decoration: underline;
    text-decoration: underline;
  }
}

.emotion-3 .focus-visible+img {
  outline: 0;
  box-shadow: 0 0 0 0.1875rem #5CABF7;
  z-index: -1;
}

.emotion-3 {
  display: inline-block;
  position: relative;
  height: 1.875rem;
  width: 1.875rem;
  text-align: center;
  vertical-align: top;
  white-space: inherit;
  padding: 0.375rem;
}

.emotion-4 {
  opacity: 0;
  position: absolute;
  cursor: pointer;
  width: 100%;
  height: 100%;
  z-index: 1;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%,-50%);
  -moz-transform: translate(-50%,-50%);
  -ms-transform: translate(-50%,-50%);
  transform: translate(-50%,-50%);
  margin: 0;
}

.emotion-5 {
  width: 100%;
  height: 100%;
  margin: 0;
  border-radius: 50%;
  z-index: 1;
  box-shadow: ;
}

.emotion-16 {
  z-index: 2;
  position: absolute;
  width: 75%;
  height: 75%;
  top: 50%;
  left: 50%;
  margin: 0;
  -webkit-transform: translate(-50%,-50%);
  -moz-transform: translate(-50%,-50%);
  -ms-transform: translate(-50%,-50%);
  transform: translate(-50%,-50%);
  pointer-events: none;
}

.emotion-17 {
  -webkit-transform: translate(10%, 110%);
  -moz-transform: translate(10%, 110%);
  -ms-transform: translate(10%, 110%);
  transform: translate(10%, 110%);
}

.emotion-18 {
  -webkit-transform: rotate(-45deg);
  -moz-transform: rotate(-45deg);
  -ms-transform: rotate(-45deg);
  transform: rotate(-45deg);
  width: calc(100% + 0.25rem);
}

.emotion-19 {
  position: fixed;
  right: 100%;
  bottom: 100%;
}

.emotion-30 {
  font-family: 'FranklinGothic','Open Sans','Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  color: #666;
  font-size: 0.875rem;
  margin-left: 4px;
  min-width: 22px;
  max-width: 28px;
  white-space: nowrap;
}

<div
  class="emotion-0"
>
  <div
    class="emotion-1"
  >
    <div
      class="emotion-2"
    >
      <div
        class="emotion-3"
      >
        <input
          aria-disabled="false"
          aria-label="carbon"
          class="emotion-4"
          data-product-image-alt-text="Split Neck Tee in Carbon"
          data-product-image-src="images/product-photos/old-navy/split-neck-tee/product--carbon.jpg"
          id="0000"
          name="color-radio"
          type="radio"
          value="0000"
        />
        <img
          alt="carbon"
          aria-hidden="true"
          class="emotion-5"
          src="images/swatches/old-navy/split-neck-tee/swatch--carbon.jpg"
        />
      </div>
    </div>
  </div>
  <div
    class="emotion-1"
  >
    <div
      class="emotion-2"
    >
      <div
        class="emotion-3"
      >
        <input
          aria-disabled="false"
          aria-label="coral retro stripe"
          class="emotion-4"
          data-product-image-alt-text="Split Neck Tee in Coral Retro Stripe"
          data-product-image-src="images/product-photos/old-navy/split-neck-tee/product--coral-retro-stripe.jpg"
          id="0001"
          name="color-radio"
          type="radio"
          value="0001"
        />
        <img
          alt="coral retro stripe"
          aria-hidden="true"
          class="emotion-5"
          src="images/swatches/old-navy/split-neck-tee/swatch--coral-retro-stripe.jpg"
        />
      </div>
    </div>
  </div>
  <div
    class="emotion-1"
  >
    <div
      class="emotion-2"
    >
      <div
        class="emotion-3"
      >
        <input
          aria-describedby="0002-outofstock"
          aria-disabled="true"
          aria-label="coral tropics"
          class="emotion-4"
          data-product-image-alt-text="Split Neck Tee in Coral Tropics"
          data-product-image-src="images/product-photos/old-navy/split-neck-tee/product--coral-tropics.jpg"
          id="0002"
          name="color-radio"
          type="radio"
          value="0002"
        />
        <img
          alt="coral tropics"
          aria-hidden="true"
          class="emotion-5"
          src="images/swatches/old-navy/split-neck-tee/swatch--coral-tropics.jpg"
        />
        <svg
          class="emotion-16"
          viewBox="0 0 37 23"
        >
          <g
            fill="none"
            fill-rule="evenodd"
            stroke="none"
            stroke-width="1"
          >
            <g
              class="emotion-17"
              fill="#000000"
              stroke="#FFFFFF"
            >
              <rect
                class="emotion-18"
                height="2"
              />
            </g>
          </g>
        </svg>
      </div>
      <span
        class="emotion-19"
        id="0002-outofstock"
      >
        fui.color_swatch.out_of_stock
      </span>
    </div>
  </div>
  <div
    class="emotion-1"
  >
    <div
      class="emotion-2"
    >
      <div
        class="emotion-3"
      >
        <input
          aria-disabled="false"
          aria-label="enchant mint"
          class="emotion-4"
          data-product-image-alt-text="Split Neck Tee in Enchant Mint"
          data-product-image-src="images/product-photos/old-navy/split-neck-tee/product--enchant-mint.jpg"
          id="0003"
          name="color-radio"
          type="radio"
          value="0003"
        />
        <img
          alt="enchant mint"
          aria-hidden="true"
          class="emotion-5"
          src="images/swatches/old-navy/split-neck-tee/swatch--enchant-mint.jpg"
        />
      </div>
    </div>
  </div>
  <div
    class="emotion-1"
  >
    <div
      class="emotion-2"
    >
      <div
        class="emotion-3"
      >
        <input
          aria-disabled="false"
          aria-label="goldenopportunity"
          class="emotion-4"
          data-product-image-alt-text="Split Neck Tee in Golden Opportunity"
          data-product-image-src="images/product-photos/old-navy/split-neck-tee/product--goldenopportunity.jpg"
          id="0004"
          name="color-radio"
          type="radio"
          value="0004"
        />
        <img
          alt="goldenopportunity"
          aria-hidden="true"
          class="emotion-5"
          src="images/swatches/old-navy/split-neck-tee/swatch--goldenopportunity.jpg"
        />
      </div>
    </div>
  </div>
  <a
    aria-label="fui.swatches.all_colors_link_aria_label"
    class="emotion-30"
  >
    + 3
  </a>
</div>
`;

exports[`<LinkSwatches /> snapshots for large breakpoint renders default state correctly OldNavy in crossBrand 1`] = `
.emotion-0>div {
  margin-right: 7px;
}

.emotion-0 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
}

.emotion-1 {
  display: inline-block;
  position: relative;
}

@media (pointer: coarse) {
  .emotion-2:active label {
    -webkit-text-decoration: underline;
    text-decoration: underline;
  }
}

.emotion-2 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

@media (pointer: fine) {
  .emotion-2:hover label {
    -webkit-text-decoration: underline;
    text-decoration: underline;
  }
}

.emotion-3 .focus-visible+img {
  outline: 0;
  box-shadow: 0 0 0 0.1875rem #5CABF7;
  z-index: -1;
}

.emotion-3 {
  display: inline-block;
  position: relative;
  height: 1.875rem;
  width: 1.875rem;
  text-align: center;
  vertical-align: top;
  white-space: inherit;
  padding: 0.375rem;
}

.emotion-4 {
  opacity: 0;
  position: absolute;
  cursor: pointer;
  width: 100%;
  height: 100%;
  z-index: 1;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%,-50%);
  -moz-transform: translate(-50%,-50%);
  -ms-transform: translate(-50%,-50%);
  transform: translate(-50%,-50%);
  margin: 0;
}

.emotion-5 {
  width: 100%;
  height: 100%;
  margin: 0;
  border-radius: 50%;
  z-index: 1;
  box-shadow: ;
}

.emotion-16 {
  z-index: 2;
  position: absolute;
  width: 75%;
  height: 75%;
  top: 50%;
  left: 50%;
  margin: 0;
  -webkit-transform: translate(-50%,-50%);
  -moz-transform: translate(-50%,-50%);
  -ms-transform: translate(-50%,-50%);
  transform: translate(-50%,-50%);
  pointer-events: none;
}

.emotion-17 {
  -webkit-transform: translate(10%, 110%);
  -moz-transform: translate(10%, 110%);
  -ms-transform: translate(10%, 110%);
  transform: translate(10%, 110%);
}

.emotion-18 {
  -webkit-transform: rotate(-45deg);
  -moz-transform: rotate(-45deg);
  -ms-transform: rotate(-45deg);
  transform: rotate(-45deg);
  width: calc(100% + 0.25rem);
}

.emotion-19 {
  position: fixed;
  right: 100%;
  bottom: 100%;
}

.emotion-30 {
  font-family: 'FranklinGothic','Open Sans','Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  color: #666;
  font-size: 0.875rem;
  margin-left: 4px;
  min-width: 22px;
  max-width: 28px;
  white-space: nowrap;
}

<div
  class="emotion-0"
>
  <div
    class="emotion-1"
  >
    <div
      class="emotion-2"
    >
      <div
        class="emotion-3"
      >
        <input
          aria-disabled="false"
          aria-label="carbon"
          class="emotion-4"
          data-product-image-alt-text="Split Neck Tee in Carbon"
          data-product-image-src="images/product-photos/old-navy/split-neck-tee/product--carbon.jpg"
          id="0000"
          name="color-radio"
          type="radio"
          value="0000"
        />
        <img
          alt="carbon"
          aria-hidden="true"
          class="emotion-5"
          src="images/swatches/old-navy/split-neck-tee/swatch--carbon.jpg"
        />
      </div>
    </div>
  </div>
  <div
    class="emotion-1"
  >
    <div
      class="emotion-2"
    >
      <div
        class="emotion-3"
      >
        <input
          aria-disabled="false"
          aria-label="coral retro stripe"
          class="emotion-4"
          data-product-image-alt-text="Split Neck Tee in Coral Retro Stripe"
          data-product-image-src="images/product-photos/old-navy/split-neck-tee/product--coral-retro-stripe.jpg"
          id="0001"
          name="color-radio"
          type="radio"
          value="0001"
        />
        <img
          alt="coral retro stripe"
          aria-hidden="true"
          class="emotion-5"
          src="images/swatches/old-navy/split-neck-tee/swatch--coral-retro-stripe.jpg"
        />
      </div>
    </div>
  </div>
  <div
    class="emotion-1"
  >
    <div
      class="emotion-2"
    >
      <div
        class="emotion-3"
      >
        <input
          aria-describedby="0002-outofstock"
          aria-disabled="true"
          aria-label="coral tropics"
          class="emotion-4"
          data-product-image-alt-text="Split Neck Tee in Coral Tropics"
          data-product-image-src="images/product-photos/old-navy/split-neck-tee/product--coral-tropics.jpg"
          id="0002"
          name="color-radio"
          type="radio"
          value="0002"
        />
        <img
          alt="coral tropics"
          aria-hidden="true"
          class="emotion-5"
          src="images/swatches/old-navy/split-neck-tee/swatch--coral-tropics.jpg"
        />
        <svg
          class="emotion-16"
          viewBox="0 0 37 23"
        >
          <g
            fill="none"
            fill-rule="evenodd"
            stroke="none"
            stroke-width="1"
          >
            <g
              class="emotion-17"
              fill="#000000"
              stroke="#FFFFFF"
            >
              <rect
                class="emotion-18"
                height="2"
              />
            </g>
          </g>
        </svg>
      </div>
      <span
        class="emotion-19"
        id="0002-outofstock"
      >
        fui.color_swatch.out_of_stock
      </span>
    </div>
  </div>
  <div
    class="emotion-1"
  >
    <div
      class="emotion-2"
    >
      <div
        class="emotion-3"
      >
        <input
          aria-disabled="false"
          aria-label="enchant mint"
          class="emotion-4"
          data-product-image-alt-text="Split Neck Tee in Enchant Mint"
          data-product-image-src="images/product-photos/old-navy/split-neck-tee/product--enchant-mint.jpg"
          id="0003"
          name="color-radio"
          type="radio"
          value="0003"
        />
        <img
          alt="enchant mint"
          aria-hidden="true"
          class="emotion-5"
          src="images/swatches/old-navy/split-neck-tee/swatch--enchant-mint.jpg"
        />
      </div>
    </div>
  </div>
  <div
    class="emotion-1"
  >
    <div
      class="emotion-2"
    >
      <div
        class="emotion-3"
      >
        <input
          aria-disabled="false"
          aria-label="goldenopportunity"
          class="emotion-4"
          data-product-image-alt-text="Split Neck Tee in Golden Opportunity"
          data-product-image-src="images/product-photos/old-navy/split-neck-tee/product--goldenopportunity.jpg"
          id="0004"
          name="color-radio"
          type="radio"
          value="0004"
        />
        <img
          alt="goldenopportunity"
          aria-hidden="true"
          class="emotion-5"
          src="images/swatches/old-navy/split-neck-tee/swatch--goldenopportunity.jpg"
        />
      </div>
    </div>
  </div>
  <a
    aria-label="fui.swatches.all_colors_link_aria_label"
    class="emotion-30"
  >
    + 3
  </a>
</div>
`;

exports[`<LinkSwatches /> snapshots for medium breakpoint renders default state correctly Athleta 1`] = `
.emotion-0 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
}

.emotion-0>div {
  margin-right: 2px;
}

.emotion-1 {
  display: inline-block;
  position: relative;
}

.emotion-2 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

@media (pointer: fine) {
  .emotion-2:hover label {
    -webkit-text-decoration: underline;
    text-decoration: underline;
  }
}

@media (pointer: coarse) {
  .emotion-2:active label {
    -webkit-text-decoration: underline;
    text-decoration: underline;
  }
}

.emotion-3 {
  display: inline-block;
  position: relative;
  height: 1.875rem;
  width: 1.875rem;
  text-align: center;
  vertical-align: top;
  white-space: inherit;
  padding: 0.375rem;
}

.emotion-3 .focus-visible+img {
  outline: 0;
  box-shadow: 0 0 0 0.1875rem #5CABF7;
  z-index: -1;
}

.emotion-4 {
  opacity: 0;
  position: absolute;
  cursor: pointer;
  width: 100%;
  height: 100%;
  z-index: 1;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%,-50%);
  -moz-transform: translate(-50%,-50%);
  -ms-transform: translate(-50%,-50%);
  transform: translate(-50%,-50%);
  margin: 0;
}

.emotion-5 {
  width: 100%;
  height: 100%;
  margin: 0;
  border-radius: 50%;
  z-index: 1;
  box-shadow: ;
}

.emotion-16 {
  z-index: 2;
  position: absolute;
  width: 75%;
  height: 75%;
  top: 50%;
  left: 50%;
  margin: 0;
  -webkit-transform: translate(-50%,-50%);
  -moz-transform: translate(-50%,-50%);
  -ms-transform: translate(-50%,-50%);
  transform: translate(-50%,-50%);
  pointer-events: none;
}

.emotion-17 {
  -webkit-transform: translate(10%, 110%);
  -moz-transform: translate(10%, 110%);
  -ms-transform: translate(10%, 110%);
  transform: translate(10%, 110%);
}

.emotion-18 {
  -webkit-transform: rotate(-45deg);
  -moz-transform: rotate(-45deg);
  -ms-transform: rotate(-45deg);
  transform: rotate(-45deg);
  width: calc(100% + 0.25rem);
}

.emotion-19 {
  position: fixed;
  right: 100%;
  bottom: 100%;
}

.emotion-30 {
  font-family: 'Avenir Next','Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  color: #666;
  font-size: 0.875rem;
  margin-left: 4px;
  min-width: 22px;
  max-width: 28px;
  white-space: nowrap;
}

<div
  class="emotion-0"
>
  <div
    class="emotion-1"
  >
    <div
      class="emotion-2"
    >
      <div
        class="emotion-3"
      >
        <input
          aria-disabled="false"
          aria-label="carbon"
          class="emotion-4"
          data-product-image-alt-text="Split Neck Tee in Carbon"
          data-product-image-src="images/product-photos/old-navy/split-neck-tee/product--carbon.jpg"
          id="0000"
          name="color-radio"
          type="radio"
          value="0000"
        />
        <img
          alt="carbon"
          aria-hidden="true"
          class="emotion-5"
          src="images/swatches/old-navy/split-neck-tee/swatch--carbon.jpg"
        />
      </div>
    </div>
  </div>
  <div
    class="emotion-1"
  >
    <div
      class="emotion-2"
    >
      <div
        class="emotion-3"
      >
        <input
          aria-disabled="false"
          aria-label="coral retro stripe"
          class="emotion-4"
          data-product-image-alt-text="Split Neck Tee in Coral Retro Stripe"
          data-product-image-src="images/product-photos/old-navy/split-neck-tee/product--coral-retro-stripe.jpg"
          id="0001"
          name="color-radio"
          type="radio"
          value="0001"
        />
        <img
          alt="coral retro stripe"
          aria-hidden="true"
          class="emotion-5"
          src="images/swatches/old-navy/split-neck-tee/swatch--coral-retro-stripe.jpg"
        />
      </div>
    </div>
  </div>
  <div
    class="emotion-1"
  >
    <div
      class="emotion-2"
    >
      <div
        class="emotion-3"
      >
        <input
          aria-describedby="0002-outofstock"
          aria-disabled="true"
          aria-label="coral tropics"
          class="emotion-4"
          data-product-image-alt-text="Split Neck Tee in Coral Tropics"
          data-product-image-src="images/product-photos/old-navy/split-neck-tee/product--coral-tropics.jpg"
          id="0002"
          name="color-radio"
          type="radio"
          value="0002"
        />
        <img
          alt="coral tropics"
          aria-hidden="true"
          class="emotion-5"
          src="images/swatches/old-navy/split-neck-tee/swatch--coral-tropics.jpg"
        />
        <svg
          class="emotion-16"
          viewBox="0 0 37 23"
        >
          <g
            fill="none"
            fill-rule="evenodd"
            stroke="none"
            stroke-width="1"
          >
            <g
              class="emotion-17"
              fill="#000000"
              stroke="#FFFFFF"
            >
              <rect
                class="emotion-18"
                height="2"
              />
            </g>
          </g>
        </svg>
      </div>
      <span
        class="emotion-19"
        id="0002-outofstock"
      >
        fui.color_swatch.out_of_stock
      </span>
    </div>
  </div>
  <div
    class="emotion-1"
  >
    <div
      class="emotion-2"
    >
      <div
        class="emotion-3"
      >
        <input
          aria-disabled="false"
          aria-label="enchant mint"
          class="emotion-4"
          data-product-image-alt-text="Split Neck Tee in Enchant Mint"
          data-product-image-src="images/product-photos/old-navy/split-neck-tee/product--enchant-mint.jpg"
          id="0003"
          name="color-radio"
          type="radio"
          value="0003"
        />
        <img
          alt="enchant mint"
          aria-hidden="true"
          class="emotion-5"
          src="images/swatches/old-navy/split-neck-tee/swatch--enchant-mint.jpg"
        />
      </div>
    </div>
  </div>
  <div
    class="emotion-1"
  >
    <div
      class="emotion-2"
    >
      <div
        class="emotion-3"
      >
        <input
          aria-disabled="false"
          aria-label="goldenopportunity"
          class="emotion-4"
          data-product-image-alt-text="Split Neck Tee in Golden Opportunity"
          data-product-image-src="images/product-photos/old-navy/split-neck-tee/product--goldenopportunity.jpg"
          id="0004"
          name="color-radio"
          type="radio"
          value="0004"
        />
        <img
          alt="goldenopportunity"
          aria-hidden="true"
          class="emotion-5"
          src="images/swatches/old-navy/split-neck-tee/swatch--goldenopportunity.jpg"
        />
      </div>
    </div>
  </div>
  <a
    aria-label="fui.swatches.all_colors_link_aria_label"
    class="emotion-30"
  >
    + 3
  </a>
</div>
`;

exports[`<LinkSwatches /> snapshots for medium breakpoint renders default state correctly Athleta in crossBrand 1`] = `
.emotion-0 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
}

.emotion-0>div {
  margin-right: 2px;
}

.emotion-1 {
  display: inline-block;
  position: relative;
}

.emotion-2 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

@media (pointer: fine) {
  .emotion-2:hover label {
    -webkit-text-decoration: underline;
    text-decoration: underline;
  }
}

@media (pointer: coarse) {
  .emotion-2:active label {
    -webkit-text-decoration: underline;
    text-decoration: underline;
  }
}

.emotion-3 {
  display: inline-block;
  position: relative;
  height: 1.875rem;
  width: 1.875rem;
  text-align: center;
  vertical-align: top;
  white-space: inherit;
  padding: 0.375rem;
}

.emotion-3 .focus-visible+img {
  outline: 0;
  box-shadow: 0 0 0 0.1875rem #5CABF7;
  z-index: -1;
}

.emotion-4 {
  opacity: 0;
  position: absolute;
  cursor: pointer;
  width: 100%;
  height: 100%;
  z-index: 1;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%,-50%);
  -moz-transform: translate(-50%,-50%);
  -ms-transform: translate(-50%,-50%);
  transform: translate(-50%,-50%);
  margin: 0;
}

.emotion-5 {
  width: 100%;
  height: 100%;
  margin: 0;
  border-radius: 50%;
  z-index: 1;
  box-shadow: ;
}

.emotion-16 {
  z-index: 2;
  position: absolute;
  width: 75%;
  height: 75%;
  top: 50%;
  left: 50%;
  margin: 0;
  -webkit-transform: translate(-50%,-50%);
  -moz-transform: translate(-50%,-50%);
  -ms-transform: translate(-50%,-50%);
  transform: translate(-50%,-50%);
  pointer-events: none;
}

.emotion-17 {
  -webkit-transform: translate(10%, 110%);
  -moz-transform: translate(10%, 110%);
  -ms-transform: translate(10%, 110%);
  transform: translate(10%, 110%);
}

.emotion-18 {
  -webkit-transform: rotate(-45deg);
  -moz-transform: rotate(-45deg);
  -ms-transform: rotate(-45deg);
  transform: rotate(-45deg);
  width: calc(100% + 0.25rem);
}

.emotion-19 {
  position: fixed;
  right: 100%;
  bottom: 100%;
}

.emotion-30 {
  font-family: 'Avenir Next','Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  color: #666;
  font-size: 0.875rem;
  margin-left: 4px;
  min-width: 22px;
  max-width: 28px;
  white-space: nowrap;
}

<div
  class="emotion-0"
>
  <div
    class="emotion-1"
  >
    <div
      class="emotion-2"
    >
      <div
        class="emotion-3"
      >
        <input
          aria-disabled="false"
          aria-label="carbon"
          class="emotion-4"
          data-product-image-alt-text="Split Neck Tee in Carbon"
          data-product-image-src="images/product-photos/old-navy/split-neck-tee/product--carbon.jpg"
          id="0000"
          name="color-radio"
          type="radio"
          value="0000"
        />
        <img
          alt="carbon"
          aria-hidden="true"
          class="emotion-5"
          src="images/swatches/old-navy/split-neck-tee/swatch--carbon.jpg"
        />
      </div>
    </div>
  </div>
  <div
    class="emotion-1"
  >
    <div
      class="emotion-2"
    >
      <div
        class="emotion-3"
      >
        <input
          aria-disabled="false"
          aria-label="coral retro stripe"
          class="emotion-4"
          data-product-image-alt-text="Split Neck Tee in Coral Retro Stripe"
          data-product-image-src="images/product-photos/old-navy/split-neck-tee/product--coral-retro-stripe.jpg"
          id="0001"
          name="color-radio"
          type="radio"
          value="0001"
        />
        <img
          alt="coral retro stripe"
          aria-hidden="true"
          class="emotion-5"
          src="images/swatches/old-navy/split-neck-tee/swatch--coral-retro-stripe.jpg"
        />
      </div>
    </div>
  </div>
  <div
    class="emotion-1"
  >
    <div
      class="emotion-2"
    >
      <div
        class="emotion-3"
      >
        <input
          aria-describedby="0002-outofstock"
          aria-disabled="true"
          aria-label="coral tropics"
          class="emotion-4"
          data-product-image-alt-text="Split Neck Tee in Coral Tropics"
          data-product-image-src="images/product-photos/old-navy/split-neck-tee/product--coral-tropics.jpg"
          id="0002"
          name="color-radio"
          type="radio"
          value="0002"
        />
        <img
          alt="coral tropics"
          aria-hidden="true"
          class="emotion-5"
          src="images/swatches/old-navy/split-neck-tee/swatch--coral-tropics.jpg"
        />
        <svg
          class="emotion-16"
          viewBox="0 0 37 23"
        >
          <g
            fill="none"
            fill-rule="evenodd"
            stroke="none"
            stroke-width="1"
          >
            <g
              class="emotion-17"
              fill="#000000"
              stroke="#FFFFFF"
            >
              <rect
                class="emotion-18"
                height="2"
              />
            </g>
          </g>
        </svg>
      </div>
      <span
        class="emotion-19"
        id="0002-outofstock"
      >
        fui.color_swatch.out_of_stock
      </span>
    </div>
  </div>
  <div
    class="emotion-1"
  >
    <div
      class="emotion-2"
    >
      <div
        class="emotion-3"
      >
        <input
          aria-disabled="false"
          aria-label="enchant mint"
          class="emotion-4"
          data-product-image-alt-text="Split Neck Tee in Enchant Mint"
          data-product-image-src="images/product-photos/old-navy/split-neck-tee/product--enchant-mint.jpg"
          id="0003"
          name="color-radio"
          type="radio"
          value="0003"
        />
        <img
          alt="enchant mint"
          aria-hidden="true"
          class="emotion-5"
          src="images/swatches/old-navy/split-neck-tee/swatch--enchant-mint.jpg"
        />
      </div>
    </div>
  </div>
  <div
    class="emotion-1"
  >
    <div
      class="emotion-2"
    >
      <div
        class="emotion-3"
      >
        <input
          aria-disabled="false"
          aria-label="goldenopportunity"
          class="emotion-4"
          data-product-image-alt-text="Split Neck Tee in Golden Opportunity"
          data-product-image-src="images/product-photos/old-navy/split-neck-tee/product--goldenopportunity.jpg"
          id="0004"
          name="color-radio"
          type="radio"
          value="0004"
        />
        <img
          alt="goldenopportunity"
          aria-hidden="true"
          class="emotion-5"
          src="images/swatches/old-navy/split-neck-tee/swatch--goldenopportunity.jpg"
        />
      </div>
    </div>
  </div>
  <a
    aria-label="fui.swatches.all_colors_link_aria_label"
    class="emotion-30"
  >
    + 3
  </a>
</div>
`;

exports[`<LinkSwatches /> snapshots for medium breakpoint renders default state correctly BananaRepublic 1`] = `
.emotion-0 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
}

.emotion-0>div {
  margin-right: 2px;
}

.emotion-1 {
  display: inline-block;
  position: relative;
}

.emotion-2 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

@media (pointer: fine) {
  .emotion-2:hover label {
    -webkit-text-decoration: underline;
    text-decoration: underline;
  }
}

@media (pointer: coarse) {
  .emotion-2:active label {
    -webkit-text-decoration: underline;
    text-decoration: underline;
  }
}

.emotion-3 {
  display: inline-block;
  position: relative;
  height: 1.875rem;
  width: 1.875rem;
  text-align: center;
  vertical-align: top;
  white-space: inherit;
  padding: 0.375rem;
}

.emotion-3 .focus-visible+img {
  outline: 0;
  box-shadow: 0 0 0 0.1875rem #5CABF7;
  z-index: -1;
}

.emotion-4 {
  opacity: 0;
  position: absolute;
  cursor: pointer;
  width: 100%;
  height: 100%;
  z-index: 1;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%,-50%);
  -moz-transform: translate(-50%,-50%);
  -ms-transform: translate(-50%,-50%);
  transform: translate(-50%,-50%);
  margin: 0;
}

.emotion-5 {
  width: 100%;
  height: 100%;
  margin: 0;
  border-radius: 50%;
  z-index: 1;
  box-shadow: ;
}

.emotion-16 {
  z-index: 2;
  position: absolute;
  width: 75%;
  height: 75%;
  top: 50%;
  left: 50%;
  margin: 0;
  -webkit-transform: translate(-50%,-50%);
  -moz-transform: translate(-50%,-50%);
  -ms-transform: translate(-50%,-50%);
  transform: translate(-50%,-50%);
  pointer-events: none;
}

.emotion-17 {
  -webkit-transform: translate(10%, 110%);
  -moz-transform: translate(10%, 110%);
  -ms-transform: translate(10%, 110%);
  transform: translate(10%, 110%);
}

.emotion-18 {
  -webkit-transform: rotate(-45deg);
  -moz-transform: rotate(-45deg);
  -ms-transform: rotate(-45deg);
  transform: rotate(-45deg);
  width: calc(100% + 0.25rem);
}

.emotion-19 {
  position: fixed;
  right: 100%;
  bottom: 100%;
}

.emotion-30 {
  font-family: 'EuclidCircularB','Hiragino Kaku Gothic Pro',Helvetica,Arial,sans-serif;
  font-weight: 300;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  color: #666;
  font-size: 0.875rem;
  margin-left: 4px;
  min-width: 22px;
  max-width: 28px;
  white-space: nowrap;
}

<div
  class="emotion-0"
>
  <div
    class="emotion-1"
  >
    <div
      class="emotion-2"
    >
      <div
        class="emotion-3"
      >
        <input
          aria-disabled="false"
          aria-label="carbon"
          class="emotion-4"
          data-product-image-alt-text="Split Neck Tee in Carbon"
          data-product-image-src="images/product-photos/old-navy/split-neck-tee/product--carbon.jpg"
          id="0000"
          name="color-radio"
          type="radio"
          value="0000"
        />
        <img
          alt="carbon"
          aria-hidden="true"
          class="emotion-5"
          src="images/swatches/old-navy/split-neck-tee/swatch--carbon.jpg"
        />
      </div>
    </div>
  </div>
  <div
    class="emotion-1"
  >
    <div
      class="emotion-2"
    >
      <div
        class="emotion-3"
      >
        <input
          aria-disabled="false"
          aria-label="coral retro stripe"
          class="emotion-4"
          data-product-image-alt-text="Split Neck Tee in Coral Retro Stripe"
          data-product-image-src="images/product-photos/old-navy/split-neck-tee/product--coral-retro-stripe.jpg"
          id="0001"
          name="color-radio"
          type="radio"
          value="0001"
        />
        <img
          alt="coral retro stripe"
          aria-hidden="true"
          class="emotion-5"
          src="images/swatches/old-navy/split-neck-tee/swatch--coral-retro-stripe.jpg"
        />
      </div>
    </div>
  </div>
  <div
    class="emotion-1"
  >
    <div
      class="emotion-2"
    >
      <div
        class="emotion-3"
      >
        <input
          aria-describedby="0002-outofstock"
          aria-disabled="true"
          aria-label="coral tropics"
          class="emotion-4"
          data-product-image-alt-text="Split Neck Tee in Coral Tropics"
          data-product-image-src="images/product-photos/old-navy/split-neck-tee/product--coral-tropics.jpg"
          id="0002"
          name="color-radio"
          type="radio"
          value="0002"
        />
        <img
          alt="coral tropics"
          aria-hidden="true"
          class="emotion-5"
          src="images/swatches/old-navy/split-neck-tee/swatch--coral-tropics.jpg"
        />
        <svg
          class="emotion-16"
          viewBox="0 0 37 23"
        >
          <g
            fill="none"
            fill-rule="evenodd"
            stroke="none"
            stroke-width="1"
          >
            <g
              class="emotion-17"
              fill="#000000"
              stroke="#FFFFFF"
            >
              <rect
                class="emotion-18"
                height="2"
              />
            </g>
          </g>
        </svg>
      </div>
      <span
        class="emotion-19"
        id="0002-outofstock"
      >
        fui.color_swatch.out_of_stock
      </span>
    </div>
  </div>
  <div
    class="emotion-1"
  >
    <div
      class="emotion-2"
    >
      <div
        class="emotion-3"
      >
        <input
          aria-disabled="false"
          aria-label="enchant mint"
          class="emotion-4"
          data-product-image-alt-text="Split Neck Tee in Enchant Mint"
          data-product-image-src="images/product-photos/old-navy/split-neck-tee/product--enchant-mint.jpg"
          id="0003"
          name="color-radio"
          type="radio"
          value="0003"
        />
        <img
          alt="enchant mint"
          aria-hidden="true"
          class="emotion-5"
          src="images/swatches/old-navy/split-neck-tee/swatch--enchant-mint.jpg"
        />
      </div>
    </div>
  </div>
  <div
    class="emotion-1"
  >
    <div
      class="emotion-2"
    >
      <div
        class="emotion-3"
      >
        <input
          aria-disabled="false"
          aria-label="goldenopportunity"
          class="emotion-4"
          data-product-image-alt-text="Split Neck Tee in Golden Opportunity"
          data-product-image-src="images/product-photos/old-navy/split-neck-tee/product--goldenopportunity.jpg"
          id="0004"
          name="color-radio"
          type="radio"
          value="0004"
        />
        <img
          alt="goldenopportunity"
          aria-hidden="true"
          class="emotion-5"
          src="images/swatches/old-navy/split-neck-tee/swatch--goldenopportunity.jpg"
        />
      </div>
    </div>
  </div>
  <a
    aria-label="fui.swatches.all_colors_link_aria_label"
    class="emotion-30"
  >
    + 3
  </a>
</div>
`;

exports[`<LinkSwatches /> snapshots for medium breakpoint renders default state correctly BananaRepublic in crossBrand 1`] = `
.emotion-0 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
}

.emotion-0>div {
  margin-right: 2px;
}

.emotion-1 {
  display: inline-block;
  position: relative;
}

.emotion-2 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

@media (pointer: fine) {
  .emotion-2:hover label {
    -webkit-text-decoration: underline;
    text-decoration: underline;
  }
}

@media (pointer: coarse) {
  .emotion-2:active label {
    -webkit-text-decoration: underline;
    text-decoration: underline;
  }
}

.emotion-3 {
  display: inline-block;
  position: relative;
  height: 1.875rem;
  width: 1.875rem;
  text-align: center;
  vertical-align: top;
  white-space: inherit;
  padding: 0.375rem;
}

.emotion-3 .focus-visible+img {
  outline: 0;
  box-shadow: 0 0 0 0.1875rem #5CABF7;
  z-index: -1;
}

.emotion-4 {
  opacity: 0;
  position: absolute;
  cursor: pointer;
  width: 100%;
  height: 100%;
  z-index: 1;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%,-50%);
  -moz-transform: translate(-50%,-50%);
  -ms-transform: translate(-50%,-50%);
  transform: translate(-50%,-50%);
  margin: 0;
}

.emotion-5 {
  width: 100%;
  height: 100%;
  margin: 0;
  border-radius: 50%;
  z-index: 1;
  box-shadow: ;
}

.emotion-16 {
  z-index: 2;
  position: absolute;
  width: 75%;
  height: 75%;
  top: 50%;
  left: 50%;
  margin: 0;
  -webkit-transform: translate(-50%,-50%);
  -moz-transform: translate(-50%,-50%);
  -ms-transform: translate(-50%,-50%);
  transform: translate(-50%,-50%);
  pointer-events: none;
}

.emotion-17 {
  -webkit-transform: translate(10%, 110%);
  -moz-transform: translate(10%, 110%);
  -ms-transform: translate(10%, 110%);
  transform: translate(10%, 110%);
}

.emotion-18 {
  -webkit-transform: rotate(-45deg);
  -moz-transform: rotate(-45deg);
  -ms-transform: rotate(-45deg);
  transform: rotate(-45deg);
  width: calc(100% + 0.25rem);
}

.emotion-19 {
  position: fixed;
  right: 100%;
  bottom: 100%;
}

.emotion-30 {
  font-family: 'EuclidCircularB','Hiragino Kaku Gothic Pro',Helvetica,Arial,sans-serif;
  font-weight: 300;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  color: #666;
  font-size: 0.875rem;
  margin-left: 4px;
  min-width: 22px;
  max-width: 28px;
  white-space: nowrap;
}

<div
  class="emotion-0"
>
  <div
    class="emotion-1"
  >
    <div
      class="emotion-2"
    >
      <div
        class="emotion-3"
      >
        <input
          aria-disabled="false"
          aria-label="carbon"
          class="emotion-4"
          data-product-image-alt-text="Split Neck Tee in Carbon"
          data-product-image-src="images/product-photos/old-navy/split-neck-tee/product--carbon.jpg"
          id="0000"
          name="color-radio"
          type="radio"
          value="0000"
        />
        <img
          alt="carbon"
          aria-hidden="true"
          class="emotion-5"
          src="images/swatches/old-navy/split-neck-tee/swatch--carbon.jpg"
        />
      </div>
    </div>
  </div>
  <div
    class="emotion-1"
  >
    <div
      class="emotion-2"
    >
      <div
        class="emotion-3"
      >
        <input
          aria-disabled="false"
          aria-label="coral retro stripe"
          class="emotion-4"
          data-product-image-alt-text="Split Neck Tee in Coral Retro Stripe"
          data-product-image-src="images/product-photos/old-navy/split-neck-tee/product--coral-retro-stripe.jpg"
          id="0001"
          name="color-radio"
          type="radio"
          value="0001"
        />
        <img
          alt="coral retro stripe"
          aria-hidden="true"
          class="emotion-5"
          src="images/swatches/old-navy/split-neck-tee/swatch--coral-retro-stripe.jpg"
        />
      </div>
    </div>
  </div>
  <div
    class="emotion-1"
  >
    <div
      class="emotion-2"
    >
      <div
        class="emotion-3"
      >
        <input
          aria-describedby="0002-outofstock"
          aria-disabled="true"
          aria-label="coral tropics"
          class="emotion-4"
          data-product-image-alt-text="Split Neck Tee in Coral Tropics"
          data-product-image-src="images/product-photos/old-navy/split-neck-tee/product--coral-tropics.jpg"
          id="0002"
          name="color-radio"
          type="radio"
          value="0002"
        />
        <img
          alt="coral tropics"
          aria-hidden="true"
          class="emotion-5"
          src="images/swatches/old-navy/split-neck-tee/swatch--coral-tropics.jpg"
        />
        <svg
          class="emotion-16"
          viewBox="0 0 37 23"
        >
          <g
            fill="none"
            fill-rule="evenodd"
            stroke="none"
            stroke-width="1"
          >
            <g
              class="emotion-17"
              fill="#000000"
              stroke="#FFFFFF"
            >
              <rect
                class="emotion-18"
                height="2"
              />
            </g>
          </g>
        </svg>
      </div>
      <span
        class="emotion-19"
        id="0002-outofstock"
      >
        fui.color_swatch.out_of_stock
      </span>
    </div>
  </div>
  <div
    class="emotion-1"
  >
    <div
      class="emotion-2"
    >
      <div
        class="emotion-3"
      >
        <input
          aria-disabled="false"
          aria-label="enchant mint"
          class="emotion-4"
          data-product-image-alt-text="Split Neck Tee in Enchant Mint"
          data-product-image-src="images/product-photos/old-navy/split-neck-tee/product--enchant-mint.jpg"
          id="0003"
          name="color-radio"
          type="radio"
          value="0003"
        />
        <img
          alt="enchant mint"
          aria-hidden="true"
          class="emotion-5"
          src="images/swatches/old-navy/split-neck-tee/swatch--enchant-mint.jpg"
        />
      </div>
    </div>
  </div>
  <div
    class="emotion-1"
  >
    <div
      class="emotion-2"
    >
      <div
        class="emotion-3"
      >
        <input
          aria-disabled="false"
          aria-label="goldenopportunity"
          class="emotion-4"
          data-product-image-alt-text="Split Neck Tee in Golden Opportunity"
          data-product-image-src="images/product-photos/old-navy/split-neck-tee/product--goldenopportunity.jpg"
          id="0004"
          name="color-radio"
          type="radio"
          value="0004"
        />
        <img
          alt="goldenopportunity"
          aria-hidden="true"
          class="emotion-5"
          src="images/swatches/old-navy/split-neck-tee/swatch--goldenopportunity.jpg"
        />
      </div>
    </div>
  </div>
  <a
    aria-label="fui.swatches.all_colors_link_aria_label"
    class="emotion-30"
  >
    + 3
  </a>
</div>
`;

exports[`<LinkSwatches /> snapshots for medium breakpoint renders default state correctly BananaRepublicFactoryStore 1`] = `
.emotion-0 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
}

.emotion-0>div {
  margin-right: 2px;
}

.emotion-1 {
  display: inline-block;
  position: relative;
}

.emotion-2 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

@media (pointer: fine) {
  .emotion-2:hover label {
    -webkit-text-decoration: underline;
    text-decoration: underline;
  }
}

@media (pointer: coarse) {
  .emotion-2:active label {
    -webkit-text-decoration: underline;
    text-decoration: underline;
  }
}

.emotion-3 {
  display: inline-block;
  position: relative;
  height: 1.875rem;
  width: 1.875rem;
  text-align: center;
  vertical-align: top;
  white-space: inherit;
  padding: 0.375rem;
}

.emotion-3 .focus-visible+img {
  outline: 0;
  box-shadow: 0 0 0 0.1875rem #5CABF7;
  z-index: -1;
}

.emotion-4 {
  opacity: 0;
  position: absolute;
  cursor: pointer;
  width: 100%;
  height: 100%;
  z-index: 1;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%,-50%);
  -moz-transform: translate(-50%,-50%);
  -ms-transform: translate(-50%,-50%);
  transform: translate(-50%,-50%);
  margin: 0;
}

.emotion-5 {
  width: 100%;
  height: 100%;
  margin: 0;
  border-radius: 50%;
  z-index: 1;
  box-shadow: ;
}

.emotion-16 {
  z-index: 2;
  position: absolute;
  width: 75%;
  height: 75%;
  top: 50%;
  left: 50%;
  margin: 0;
  -webkit-transform: translate(-50%,-50%);
  -moz-transform: translate(-50%,-50%);
  -ms-transform: translate(-50%,-50%);
  transform: translate(-50%,-50%);
  pointer-events: none;
}

.emotion-17 {
  -webkit-transform: translate(10%, 110%);
  -moz-transform: translate(10%, 110%);
  -ms-transform: translate(10%, 110%);
  transform: translate(10%, 110%);
}

.emotion-18 {
  -webkit-transform: rotate(-45deg);
  -moz-transform: rotate(-45deg);
  -ms-transform: rotate(-45deg);
  transform: rotate(-45deg);
  width: calc(100% + 0.25rem);
}

.emotion-19 {
  position: fixed;
  right: 100%;
  bottom: 100%;
}

.emotion-30 {
  font-family: 'EuclidCircularB','Hiragino Kaku Gothic Pro',Helvetica,Arial,sans-serif;
  font-weight: 300;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  color: #666;
  font-size: 0.875rem;
  margin-left: 4px;
  min-width: 22px;
  max-width: 28px;
  white-space: nowrap;
}

<div
  class="emotion-0"
>
  <div
    class="emotion-1"
  >
    <div
      class="emotion-2"
    >
      <div
        class="emotion-3"
      >
        <input
          aria-disabled="false"
          aria-label="carbon"
          class="emotion-4"
          data-product-image-alt-text="Split Neck Tee in Carbon"
          data-product-image-src="images/product-photos/old-navy/split-neck-tee/product--carbon.jpg"
          id="0000"
          name="color-radio"
          type="radio"
          value="0000"
        />
        <img
          alt="carbon"
          aria-hidden="true"
          class="emotion-5"
          src="images/swatches/old-navy/split-neck-tee/swatch--carbon.jpg"
        />
      </div>
    </div>
  </div>
  <div
    class="emotion-1"
  >
    <div
      class="emotion-2"
    >
      <div
        class="emotion-3"
      >
        <input
          aria-disabled="false"
          aria-label="coral retro stripe"
          class="emotion-4"
          data-product-image-alt-text="Split Neck Tee in Coral Retro Stripe"
          data-product-image-src="images/product-photos/old-navy/split-neck-tee/product--coral-retro-stripe.jpg"
          id="0001"
          name="color-radio"
          type="radio"
          value="0001"
        />
        <img
          alt="coral retro stripe"
          aria-hidden="true"
          class="emotion-5"
          src="images/swatches/old-navy/split-neck-tee/swatch--coral-retro-stripe.jpg"
        />
      </div>
    </div>
  </div>
  <div
    class="emotion-1"
  >
    <div
      class="emotion-2"
    >
      <div
        class="emotion-3"
      >
        <input
          aria-describedby="0002-outofstock"
          aria-disabled="true"
          aria-label="coral tropics"
          class="emotion-4"
          data-product-image-alt-text="Split Neck Tee in Coral Tropics"
          data-product-image-src="images/product-photos/old-navy/split-neck-tee/product--coral-tropics.jpg"
          id="0002"
          name="color-radio"
          type="radio"
          value="0002"
        />
        <img
          alt="coral tropics"
          aria-hidden="true"
          class="emotion-5"
          src="images/swatches/old-navy/split-neck-tee/swatch--coral-tropics.jpg"
        />
        <svg
          class="emotion-16"
          viewBox="0 0 37 23"
        >
          <g
            fill="none"
            fill-rule="evenodd"
            stroke="none"
            stroke-width="1"
          >
            <g
              class="emotion-17"
              fill="#000000"
              stroke="#FFFFFF"
            >
              <rect
                class="emotion-18"
                height="2"
              />
            </g>
          </g>
        </svg>
      </div>
      <span
        class="emotion-19"
        id="0002-outofstock"
      >
        fui.color_swatch.out_of_stock
      </span>
    </div>
  </div>
  <div
    class="emotion-1"
  >
    <div
      class="emotion-2"
    >
      <div
        class="emotion-3"
      >
        <input
          aria-disabled="false"
          aria-label="enchant mint"
          class="emotion-4"
          data-product-image-alt-text="Split Neck Tee in Enchant Mint"
          data-product-image-src="images/product-photos/old-navy/split-neck-tee/product--enchant-mint.jpg"
          id="0003"
          name="color-radio"
          type="radio"
          value="0003"
        />
        <img
          alt="enchant mint"
          aria-hidden="true"
          class="emotion-5"
          src="images/swatches/old-navy/split-neck-tee/swatch--enchant-mint.jpg"
        />
      </div>
    </div>
  </div>
  <div
    class="emotion-1"
  >
    <div
      class="emotion-2"
    >
      <div
        class="emotion-3"
      >
        <input
          aria-disabled="false"
          aria-label="goldenopportunity"
          class="emotion-4"
          data-product-image-alt-text="Split Neck Tee in Golden Opportunity"
          data-product-image-src="images/product-photos/old-navy/split-neck-tee/product--goldenopportunity.jpg"
          id="0004"
          name="color-radio"
          type="radio"
          value="0004"
        />
        <img
          alt="goldenopportunity"
          aria-hidden="true"
          class="emotion-5"
          src="images/swatches/old-navy/split-neck-tee/swatch--goldenopportunity.jpg"
        />
      </div>
    </div>
  </div>
  <a
    aria-label="fui.swatches.all_colors_link_aria_label"
    class="emotion-30"
  >
    + 3
  </a>
</div>
`;

exports[`<LinkSwatches /> snapshots for medium breakpoint renders default state correctly BananaRepublicFactoryStore in crossBrand 1`] = `
.emotion-0 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
}

.emotion-0>div {
  margin-right: 2px;
}

.emotion-1 {
  display: inline-block;
  position: relative;
}

.emotion-2 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

@media (pointer: fine) {
  .emotion-2:hover label {
    -webkit-text-decoration: underline;
    text-decoration: underline;
  }
}

@media (pointer: coarse) {
  .emotion-2:active label {
    -webkit-text-decoration: underline;
    text-decoration: underline;
  }
}

.emotion-3 {
  display: inline-block;
  position: relative;
  height: 1.875rem;
  width: 1.875rem;
  text-align: center;
  vertical-align: top;
  white-space: inherit;
  padding: 0.375rem;
}

.emotion-3 .focus-visible+img {
  outline: 0;
  box-shadow: 0 0 0 0.1875rem #5CABF7;
  z-index: -1;
}

.emotion-4 {
  opacity: 0;
  position: absolute;
  cursor: pointer;
  width: 100%;
  height: 100%;
  z-index: 1;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%,-50%);
  -moz-transform: translate(-50%,-50%);
  -ms-transform: translate(-50%,-50%);
  transform: translate(-50%,-50%);
  margin: 0;
}

.emotion-5 {
  width: 100%;
  height: 100%;
  margin: 0;
  border-radius: 50%;
  z-index: 1;
  box-shadow: ;
}

.emotion-16 {
  z-index: 2;
  position: absolute;
  width: 75%;
  height: 75%;
  top: 50%;
  left: 50%;
  margin: 0;
  -webkit-transform: translate(-50%,-50%);
  -moz-transform: translate(-50%,-50%);
  -ms-transform: translate(-50%,-50%);
  transform: translate(-50%,-50%);
  pointer-events: none;
}

.emotion-17 {
  -webkit-transform: translate(10%, 110%);
  -moz-transform: translate(10%, 110%);
  -ms-transform: translate(10%, 110%);
  transform: translate(10%, 110%);
}

.emotion-18 {
  -webkit-transform: rotate(-45deg);
  -moz-transform: rotate(-45deg);
  -ms-transform: rotate(-45deg);
  transform: rotate(-45deg);
  width: calc(100% + 0.25rem);
}

.emotion-19 {
  position: fixed;
  right: 100%;
  bottom: 100%;
}

.emotion-30 {
  font-family: 'EuclidCircularB','Hiragino Kaku Gothic Pro',Helvetica,Arial,sans-serif;
  font-weight: 300;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  color: #666;
  font-size: 0.875rem;
  margin-left: 4px;
  min-width: 22px;
  max-width: 28px;
  white-space: nowrap;
}

<div
  class="emotion-0"
>
  <div
    class="emotion-1"
  >
    <div
      class="emotion-2"
    >
      <div
        class="emotion-3"
      >
        <input
          aria-disabled="false"
          aria-label="carbon"
          class="emotion-4"
          data-product-image-alt-text="Split Neck Tee in Carbon"
          data-product-image-src="images/product-photos/old-navy/split-neck-tee/product--carbon.jpg"
          id="0000"
          name="color-radio"
          type="radio"
          value="0000"
        />
        <img
          alt="carbon"
          aria-hidden="true"
          class="emotion-5"
          src="images/swatches/old-navy/split-neck-tee/swatch--carbon.jpg"
        />
      </div>
    </div>
  </div>
  <div
    class="emotion-1"
  >
    <div
      class="emotion-2"
    >
      <div
        class="emotion-3"
      >
        <input
          aria-disabled="false"
          aria-label="coral retro stripe"
          class="emotion-4"
          data-product-image-alt-text="Split Neck Tee in Coral Retro Stripe"
          data-product-image-src="images/product-photos/old-navy/split-neck-tee/product--coral-retro-stripe.jpg"
          id="0001"
          name="color-radio"
          type="radio"
          value="0001"
        />
        <img
          alt="coral retro stripe"
          aria-hidden="true"
          class="emotion-5"
          src="images/swatches/old-navy/split-neck-tee/swatch--coral-retro-stripe.jpg"
        />
      </div>
    </div>
  </div>
  <div
    class="emotion-1"
  >
    <div
      class="emotion-2"
    >
      <div
        class="emotion-3"
      >
        <input
          aria-describedby="0002-outofstock"
          aria-disabled="true"
          aria-label="coral tropics"
          class="emotion-4"
          data-product-image-alt-text="Split Neck Tee in Coral Tropics"
          data-product-image-src="images/product-photos/old-navy/split-neck-tee/product--coral-tropics.jpg"
          id="0002"
          name="color-radio"
          type="radio"
          value="0002"
        />
        <img
          alt="coral tropics"
          aria-hidden="true"
          class="emotion-5"
          src="images/swatches/old-navy/split-neck-tee/swatch--coral-tropics.jpg"
        />
        <svg
          class="emotion-16"
          viewBox="0 0 37 23"
        >
          <g
            fill="none"
            fill-rule="evenodd"
            stroke="none"
            stroke-width="1"
          >
            <g
              class="emotion-17"
              fill="#000000"
              stroke="#FFFFFF"
            >
              <rect
                class="emotion-18"
                height="2"
              />
            </g>
          </g>
        </svg>
      </div>
      <span
        class="emotion-19"
        id="0002-outofstock"
      >
        fui.color_swatch.out_of_stock
      </span>
    </div>
  </div>
  <div
    class="emotion-1"
  >
    <div
      class="emotion-2"
    >
      <div
        class="emotion-3"
      >
        <input
          aria-disabled="false"
          aria-label="enchant mint"
          class="emotion-4"
          data-product-image-alt-text="Split Neck Tee in Enchant Mint"
          data-product-image-src="images/product-photos/old-navy/split-neck-tee/product--enchant-mint.jpg"
          id="0003"
          name="color-radio"
          type="radio"
          value="0003"
        />
        <img
          alt="enchant mint"
          aria-hidden="true"
          class="emotion-5"
          src="images/swatches/old-navy/split-neck-tee/swatch--enchant-mint.jpg"
        />
      </div>
    </div>
  </div>
  <div
    class="emotion-1"
  >
    <div
      class="emotion-2"
    >
      <div
        class="emotion-3"
      >
        <input
          aria-disabled="false"
          aria-label="goldenopportunity"
          class="emotion-4"
          data-product-image-alt-text="Split Neck Tee in Golden Opportunity"
          data-product-image-src="images/product-photos/old-navy/split-neck-tee/product--goldenopportunity.jpg"
          id="0004"
          name="color-radio"
          type="radio"
          value="0004"
        />
        <img
          alt="goldenopportunity"
          aria-hidden="true"
          class="emotion-5"
          src="images/swatches/old-navy/split-neck-tee/swatch--goldenopportunity.jpg"
        />
      </div>
    </div>
  </div>
  <a
    aria-label="fui.swatches.all_colors_link_aria_label"
    class="emotion-30"
  >
    + 3
  </a>
</div>
`;

exports[`<LinkSwatches /> snapshots for medium breakpoint renders default state correctly Gap 1`] = `
.emotion-0 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
}

.emotion-0>div {
  margin-right: 2px;
}

.emotion-1 {
  display: inline-block;
  position: relative;
}

.emotion-2 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

@media (pointer: fine) {
  .emotion-2:hover label {
    -webkit-text-decoration: underline;
    text-decoration: underline;
  }
}

@media (pointer: coarse) {
  .emotion-2:active label {
    -webkit-text-decoration: underline;
    text-decoration: underline;
  }
}

.emotion-3 {
  display: inline-block;
  position: relative;
  height: 1.875rem;
  width: 1.875rem;
  text-align: center;
  vertical-align: top;
  white-space: inherit;
  padding: 0.375rem;
}

.emotion-3 .focus-visible+img {
  outline: 0;
  box-shadow: 0 0 0 0.1875rem #5CABF7;
  z-index: -1;
}

.emotion-4 {
  opacity: 0;
  position: absolute;
  cursor: pointer;
  width: 100%;
  height: 100%;
  z-index: 1;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%,-50%);
  -moz-transform: translate(-50%,-50%);
  -ms-transform: translate(-50%,-50%);
  transform: translate(-50%,-50%);
  margin: 0;
}

.emotion-5 {
  width: 100%;
  height: 100%;
  margin: 0;
  border-radius: 50%;
  z-index: 1;
  box-shadow: ;
}

.emotion-16 {
  z-index: 2;
  position: absolute;
  width: 75%;
  height: 75%;
  top: 50%;
  left: 50%;
  margin: 0;
  -webkit-transform: translate(-50%,-50%);
  -moz-transform: translate(-50%,-50%);
  -ms-transform: translate(-50%,-50%);
  transform: translate(-50%,-50%);
  pointer-events: none;
}

.emotion-17 {
  -webkit-transform: translate(10%, 110%);
  -moz-transform: translate(10%, 110%);
  -ms-transform: translate(10%, 110%);
  transform: translate(10%, 110%);
}

.emotion-18 {
  -webkit-transform: rotate(-45deg);
  -moz-transform: rotate(-45deg);
  -ms-transform: rotate(-45deg);
  transform: rotate(-45deg);
  width: calc(100% + 0.25rem);
}

.emotion-19 {
  position: fixed;
  right: 100%;
  bottom: 100%;
}

.emotion-30 {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  color: #666;
  font-size: 0.875rem;
  margin-left: 4px;
  min-width: 22px;
  max-width: 28px;
  white-space: nowrap;
}

<div
  class="emotion-0"
>
  <div
    class="emotion-1"
  >
    <div
      class="emotion-2"
    >
      <div
        class="emotion-3"
      >
        <input
          aria-disabled="false"
          aria-label="carbon"
          class="emotion-4"
          data-product-image-alt-text="Split Neck Tee in Carbon"
          data-product-image-src="images/product-photos/old-navy/split-neck-tee/product--carbon.jpg"
          id="0000"
          name="color-radio"
          type="radio"
          value="0000"
        />
        <img
          alt="carbon"
          aria-hidden="true"
          class="emotion-5"
          src="images/swatches/old-navy/split-neck-tee/swatch--carbon.jpg"
        />
      </div>
    </div>
  </div>
  <div
    class="emotion-1"
  >
    <div
      class="emotion-2"
    >
      <div
        class="emotion-3"
      >
        <input
          aria-disabled="false"
          aria-label="coral retro stripe"
          class="emotion-4"
          data-product-image-alt-text="Split Neck Tee in Coral Retro Stripe"
          data-product-image-src="images/product-photos/old-navy/split-neck-tee/product--coral-retro-stripe.jpg"
          id="0001"
          name="color-radio"
          type="radio"
          value="0001"
        />
        <img
          alt="coral retro stripe"
          aria-hidden="true"
          class="emotion-5"
          src="images/swatches/old-navy/split-neck-tee/swatch--coral-retro-stripe.jpg"
        />
      </div>
    </div>
  </div>
  <div
    class="emotion-1"
  >
    <div
      class="emotion-2"
    >
      <div
        class="emotion-3"
      >
        <input
          aria-describedby="0002-outofstock"
          aria-disabled="true"
          aria-label="coral tropics"
          class="emotion-4"
          data-product-image-alt-text="Split Neck Tee in Coral Tropics"
          data-product-image-src="images/product-photos/old-navy/split-neck-tee/product--coral-tropics.jpg"
          id="0002"
          name="color-radio"
          type="radio"
          value="0002"
        />
        <img
          alt="coral tropics"
          aria-hidden="true"
          class="emotion-5"
          src="images/swatches/old-navy/split-neck-tee/swatch--coral-tropics.jpg"
        />
        <svg
          class="emotion-16"
          viewBox="0 0 37 23"
        >
          <g
            fill="none"
            fill-rule="evenodd"
            stroke="none"
            stroke-width="1"
          >
            <g
              class="emotion-17"
              fill="#000000"
              stroke="#FFFFFF"
            >
              <rect
                class="emotion-18"
                height="2"
              />
            </g>
          </g>
        </svg>
      </div>
      <span
        class="emotion-19"
        id="0002-outofstock"
      >
        fui.color_swatch.out_of_stock
      </span>
    </div>
  </div>
  <div
    class="emotion-1"
  >
    <div
      class="emotion-2"
    >
      <div
        class="emotion-3"
      >
        <input
          aria-disabled="false"
          aria-label="enchant mint"
          class="emotion-4"
          data-product-image-alt-text="Split Neck Tee in Enchant Mint"
          data-product-image-src="images/product-photos/old-navy/split-neck-tee/product--enchant-mint.jpg"
          id="0003"
          name="color-radio"
          type="radio"
          value="0003"
        />
        <img
          alt="enchant mint"
          aria-hidden="true"
          class="emotion-5"
          src="images/swatches/old-navy/split-neck-tee/swatch--enchant-mint.jpg"
        />
      </div>
    </div>
  </div>
  <div
    class="emotion-1"
  >
    <div
      class="emotion-2"
    >
      <div
        class="emotion-3"
      >
        <input
          aria-disabled="false"
          aria-label="goldenopportunity"
          class="emotion-4"
          data-product-image-alt-text="Split Neck Tee in Golden Opportunity"
          data-product-image-src="images/product-photos/old-navy/split-neck-tee/product--goldenopportunity.jpg"
          id="0004"
          name="color-radio"
          type="radio"
          value="0004"
        />
        <img
          alt="goldenopportunity"
          aria-hidden="true"
          class="emotion-5"
          src="images/swatches/old-navy/split-neck-tee/swatch--goldenopportunity.jpg"
        />
      </div>
    </div>
  </div>
  <a
    aria-label="fui.swatches.all_colors_link_aria_label"
    class="emotion-30"
  >
    + 3
  </a>
</div>
`;

exports[`<LinkSwatches /> snapshots for medium breakpoint renders default state correctly Gap in crossBrand 1`] = `
.emotion-0 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
}

.emotion-0>div {
  margin-right: 2px;
}

.emotion-1 {
  display: inline-block;
  position: relative;
}

.emotion-2 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

@media (pointer: fine) {
  .emotion-2:hover label {
    -webkit-text-decoration: underline;
    text-decoration: underline;
  }
}

@media (pointer: coarse) {
  .emotion-2:active label {
    -webkit-text-decoration: underline;
    text-decoration: underline;
  }
}

.emotion-3 {
  display: inline-block;
  position: relative;
  height: 1.875rem;
  width: 1.875rem;
  text-align: center;
  vertical-align: top;
  white-space: inherit;
  padding: 0.375rem;
}

.emotion-3 .focus-visible+img {
  outline: 0;
  box-shadow: 0 0 0 0.1875rem #5CABF7;
  z-index: -1;
}

.emotion-4 {
  opacity: 0;
  position: absolute;
  cursor: pointer;
  width: 100%;
  height: 100%;
  z-index: 1;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%,-50%);
  -moz-transform: translate(-50%,-50%);
  -ms-transform: translate(-50%,-50%);
  transform: translate(-50%,-50%);
  margin: 0;
}

.emotion-5 {
  width: 100%;
  height: 100%;
  margin: 0;
  border-radius: 50%;
  z-index: 1;
  box-shadow: ;
}

.emotion-16 {
  z-index: 2;
  position: absolute;
  width: 75%;
  height: 75%;
  top: 50%;
  left: 50%;
  margin: 0;
  -webkit-transform: translate(-50%,-50%);
  -moz-transform: translate(-50%,-50%);
  -ms-transform: translate(-50%,-50%);
  transform: translate(-50%,-50%);
  pointer-events: none;
}

.emotion-17 {
  -webkit-transform: translate(10%, 110%);
  -moz-transform: translate(10%, 110%);
  -ms-transform: translate(10%, 110%);
  transform: translate(10%, 110%);
}

.emotion-18 {
  -webkit-transform: rotate(-45deg);
  -moz-transform: rotate(-45deg);
  -ms-transform: rotate(-45deg);
  transform: rotate(-45deg);
  width: calc(100% + 0.25rem);
}

.emotion-19 {
  position: fixed;
  right: 100%;
  bottom: 100%;
}

.emotion-30 {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  color: #666;
  font-size: 0.875rem;
  margin-left: 4px;
  min-width: 22px;
  max-width: 28px;
  white-space: nowrap;
}

<div
  class="emotion-0"
>
  <div
    class="emotion-1"
  >
    <div
      class="emotion-2"
    >
      <div
        class="emotion-3"
      >
        <input
          aria-disabled="false"
          aria-label="carbon"
          class="emotion-4"
          data-product-image-alt-text="Split Neck Tee in Carbon"
          data-product-image-src="images/product-photos/old-navy/split-neck-tee/product--carbon.jpg"
          id="0000"
          name="color-radio"
          type="radio"
          value="0000"
        />
        <img
          alt="carbon"
          aria-hidden="true"
          class="emotion-5"
          src="images/swatches/old-navy/split-neck-tee/swatch--carbon.jpg"
        />
      </div>
    </div>
  </div>
  <div
    class="emotion-1"
  >
    <div
      class="emotion-2"
    >
      <div
        class="emotion-3"
      >
        <input
          aria-disabled="false"
          aria-label="coral retro stripe"
          class="emotion-4"
          data-product-image-alt-text="Split Neck Tee in Coral Retro Stripe"
          data-product-image-src="images/product-photos/old-navy/split-neck-tee/product--coral-retro-stripe.jpg"
          id="0001"
          name="color-radio"
          type="radio"
          value="0001"
        />
        <img
          alt="coral retro stripe"
          aria-hidden="true"
          class="emotion-5"
          src="images/swatches/old-navy/split-neck-tee/swatch--coral-retro-stripe.jpg"
        />
      </div>
    </div>
  </div>
  <div
    class="emotion-1"
  >
    <div
      class="emotion-2"
    >
      <div
        class="emotion-3"
      >
        <input
          aria-describedby="0002-outofstock"
          aria-disabled="true"
          aria-label="coral tropics"
          class="emotion-4"
          data-product-image-alt-text="Split Neck Tee in Coral Tropics"
          data-product-image-src="images/product-photos/old-navy/split-neck-tee/product--coral-tropics.jpg"
          id="0002"
          name="color-radio"
          type="radio"
          value="0002"
        />
        <img
          alt="coral tropics"
          aria-hidden="true"
          class="emotion-5"
          src="images/swatches/old-navy/split-neck-tee/swatch--coral-tropics.jpg"
        />
        <svg
          class="emotion-16"
          viewBox="0 0 37 23"
        >
          <g
            fill="none"
            fill-rule="evenodd"
            stroke="none"
            stroke-width="1"
          >
            <g
              class="emotion-17"
              fill="#000000"
              stroke="#FFFFFF"
            >
              <rect
                class="emotion-18"
                height="2"
              />
            </g>
          </g>
        </svg>
      </div>
      <span
        class="emotion-19"
        id="0002-outofstock"
      >
        fui.color_swatch.out_of_stock
      </span>
    </div>
  </div>
  <div
    class="emotion-1"
  >
    <div
      class="emotion-2"
    >
      <div
        class="emotion-3"
      >
        <input
          aria-disabled="false"
          aria-label="enchant mint"
          class="emotion-4"
          data-product-image-alt-text="Split Neck Tee in Enchant Mint"
          data-product-image-src="images/product-photos/old-navy/split-neck-tee/product--enchant-mint.jpg"
          id="0003"
          name="color-radio"
          type="radio"
          value="0003"
        />
        <img
          alt="enchant mint"
          aria-hidden="true"
          class="emotion-5"
          src="images/swatches/old-navy/split-neck-tee/swatch--enchant-mint.jpg"
        />
      </div>
    </div>
  </div>
  <div
    class="emotion-1"
  >
    <div
      class="emotion-2"
    >
      <div
        class="emotion-3"
      >
        <input
          aria-disabled="false"
          aria-label="goldenopportunity"
          class="emotion-4"
          data-product-image-alt-text="Split Neck Tee in Golden Opportunity"
          data-product-image-src="images/product-photos/old-navy/split-neck-tee/product--goldenopportunity.jpg"
          id="0004"
          name="color-radio"
          type="radio"
          value="0004"
        />
        <img
          alt="goldenopportunity"
          aria-hidden="true"
          class="emotion-5"
          src="images/swatches/old-navy/split-neck-tee/swatch--goldenopportunity.jpg"
        />
      </div>
    </div>
  </div>
  <a
    aria-label="fui.swatches.all_colors_link_aria_label"
    class="emotion-30"
  >
    + 3
  </a>
</div>
`;

exports[`<LinkSwatches /> snapshots for medium breakpoint renders default state correctly GapFactoryStore 1`] = `
.emotion-0 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
}

.emotion-0>div {
  margin-right: 2px;
}

.emotion-1 {
  display: inline-block;
  position: relative;
}

.emotion-2 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

@media (pointer: fine) {
  .emotion-2:hover label {
    -webkit-text-decoration: underline;
    text-decoration: underline;
  }
}

@media (pointer: coarse) {
  .emotion-2:active label {
    -webkit-text-decoration: underline;
    text-decoration: underline;
  }
}

.emotion-3 {
  display: inline-block;
  position: relative;
  height: 1.875rem;
  width: 1.875rem;
  text-align: center;
  vertical-align: top;
  white-space: inherit;
  padding: 0.375rem;
}

.emotion-3 .focus-visible+img {
  outline: 0;
  box-shadow: 0 0 0 0.1875rem #5CABF7;
  z-index: -1;
}

.emotion-4 {
  opacity: 0;
  position: absolute;
  cursor: pointer;
  width: 100%;
  height: 100%;
  z-index: 1;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%,-50%);
  -moz-transform: translate(-50%,-50%);
  -ms-transform: translate(-50%,-50%);
  transform: translate(-50%,-50%);
  margin: 0;
}

.emotion-5 {
  width: 100%;
  height: 100%;
  margin: 0;
  border-radius: 50%;
  z-index: 1;
  box-shadow: ;
}

.emotion-16 {
  z-index: 2;
  position: absolute;
  width: 75%;
  height: 75%;
  top: 50%;
  left: 50%;
  margin: 0;
  -webkit-transform: translate(-50%,-50%);
  -moz-transform: translate(-50%,-50%);
  -ms-transform: translate(-50%,-50%);
  transform: translate(-50%,-50%);
  pointer-events: none;
}

.emotion-17 {
  -webkit-transform: translate(10%, 110%);
  -moz-transform: translate(10%, 110%);
  -ms-transform: translate(10%, 110%);
  transform: translate(10%, 110%);
}

.emotion-18 {
  -webkit-transform: rotate(-45deg);
  -moz-transform: rotate(-45deg);
  -ms-transform: rotate(-45deg);
  transform: rotate(-45deg);
  width: calc(100% + 0.25rem);
}

.emotion-19 {
  position: fixed;
  right: 100%;
  bottom: 100%;
}

.emotion-30 {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  color: #666;
  font-size: 0.875rem;
  margin-left: 4px;
  min-width: 22px;
  max-width: 28px;
  white-space: nowrap;
}

<div
  class="emotion-0"
>
  <div
    class="emotion-1"
  >
    <div
      class="emotion-2"
    >
      <div
        class="emotion-3"
      >
        <input
          aria-disabled="false"
          aria-label="carbon"
          class="emotion-4"
          data-product-image-alt-text="Split Neck Tee in Carbon"
          data-product-image-src="images/product-photos/old-navy/split-neck-tee/product--carbon.jpg"
          id="0000"
          name="color-radio"
          type="radio"
          value="0000"
        />
        <img
          alt="carbon"
          aria-hidden="true"
          class="emotion-5"
          src="images/swatches/old-navy/split-neck-tee/swatch--carbon.jpg"
        />
      </div>
    </div>
  </div>
  <div
    class="emotion-1"
  >
    <div
      class="emotion-2"
    >
      <div
        class="emotion-3"
      >
        <input
          aria-disabled="false"
          aria-label="coral retro stripe"
          class="emotion-4"
          data-product-image-alt-text="Split Neck Tee in Coral Retro Stripe"
          data-product-image-src="images/product-photos/old-navy/split-neck-tee/product--coral-retro-stripe.jpg"
          id="0001"
          name="color-radio"
          type="radio"
          value="0001"
        />
        <img
          alt="coral retro stripe"
          aria-hidden="true"
          class="emotion-5"
          src="images/swatches/old-navy/split-neck-tee/swatch--coral-retro-stripe.jpg"
        />
      </div>
    </div>
  </div>
  <div
    class="emotion-1"
  >
    <div
      class="emotion-2"
    >
      <div
        class="emotion-3"
      >
        <input
          aria-describedby="0002-outofstock"
          aria-disabled="true"
          aria-label="coral tropics"
          class="emotion-4"
          data-product-image-alt-text="Split Neck Tee in Coral Tropics"
          data-product-image-src="images/product-photos/old-navy/split-neck-tee/product--coral-tropics.jpg"
          id="0002"
          name="color-radio"
          type="radio"
          value="0002"
        />
        <img
          alt="coral tropics"
          aria-hidden="true"
          class="emotion-5"
          src="images/swatches/old-navy/split-neck-tee/swatch--coral-tropics.jpg"
        />
        <svg
          class="emotion-16"
          viewBox="0 0 37 23"
        >
          <g
            fill="none"
            fill-rule="evenodd"
            stroke="none"
            stroke-width="1"
          >
            <g
              class="emotion-17"
              fill="#000000"
              stroke="#FFFFFF"
            >
              <rect
                class="emotion-18"
                height="2"
              />
            </g>
          </g>
        </svg>
      </div>
      <span
        class="emotion-19"
        id="0002-outofstock"
      >
        fui.color_swatch.out_of_stock
      </span>
    </div>
  </div>
  <div
    class="emotion-1"
  >
    <div
      class="emotion-2"
    >
      <div
        class="emotion-3"
      >
        <input
          aria-disabled="false"
          aria-label="enchant mint"
          class="emotion-4"
          data-product-image-alt-text="Split Neck Tee in Enchant Mint"
          data-product-image-src="images/product-photos/old-navy/split-neck-tee/product--enchant-mint.jpg"
          id="0003"
          name="color-radio"
          type="radio"
          value="0003"
        />
        <img
          alt="enchant mint"
          aria-hidden="true"
          class="emotion-5"
          src="images/swatches/old-navy/split-neck-tee/swatch--enchant-mint.jpg"
        />
      </div>
    </div>
  </div>
  <div
    class="emotion-1"
  >
    <div
      class="emotion-2"
    >
      <div
        class="emotion-3"
      >
        <input
          aria-disabled="false"
          aria-label="goldenopportunity"
          class="emotion-4"
          data-product-image-alt-text="Split Neck Tee in Golden Opportunity"
          data-product-image-src="images/product-photos/old-navy/split-neck-tee/product--goldenopportunity.jpg"
          id="0004"
          name="color-radio"
          type="radio"
          value="0004"
        />
        <img
          alt="goldenopportunity"
          aria-hidden="true"
          class="emotion-5"
          src="images/swatches/old-navy/split-neck-tee/swatch--goldenopportunity.jpg"
        />
      </div>
    </div>
  </div>
  <a
    aria-label="fui.swatches.all_colors_link_aria_label"
    class="emotion-30"
  >
    + 3
  </a>
</div>
`;

exports[`<LinkSwatches /> snapshots for medium breakpoint renders default state correctly GapFactoryStore in crossBrand 1`] = `
.emotion-0 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
}

.emotion-0>div {
  margin-right: 2px;
}

.emotion-1 {
  display: inline-block;
  position: relative;
}

.emotion-2 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

@media (pointer: fine) {
  .emotion-2:hover label {
    -webkit-text-decoration: underline;
    text-decoration: underline;
  }
}

@media (pointer: coarse) {
  .emotion-2:active label {
    -webkit-text-decoration: underline;
    text-decoration: underline;
  }
}

.emotion-3 {
  display: inline-block;
  position: relative;
  height: 1.875rem;
  width: 1.875rem;
  text-align: center;
  vertical-align: top;
  white-space: inherit;
  padding: 0.375rem;
}

.emotion-3 .focus-visible+img {
  outline: 0;
  box-shadow: 0 0 0 0.1875rem #5CABF7;
  z-index: -1;
}

.emotion-4 {
  opacity: 0;
  position: absolute;
  cursor: pointer;
  width: 100%;
  height: 100%;
  z-index: 1;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%,-50%);
  -moz-transform: translate(-50%,-50%);
  -ms-transform: translate(-50%,-50%);
  transform: translate(-50%,-50%);
  margin: 0;
}

.emotion-5 {
  width: 100%;
  height: 100%;
  margin: 0;
  border-radius: 50%;
  z-index: 1;
  box-shadow: ;
}

.emotion-16 {
  z-index: 2;
  position: absolute;
  width: 75%;
  height: 75%;
  top: 50%;
  left: 50%;
  margin: 0;
  -webkit-transform: translate(-50%,-50%);
  -moz-transform: translate(-50%,-50%);
  -ms-transform: translate(-50%,-50%);
  transform: translate(-50%,-50%);
  pointer-events: none;
}

.emotion-17 {
  -webkit-transform: translate(10%, 110%);
  -moz-transform: translate(10%, 110%);
  -ms-transform: translate(10%, 110%);
  transform: translate(10%, 110%);
}

.emotion-18 {
  -webkit-transform: rotate(-45deg);
  -moz-transform: rotate(-45deg);
  -ms-transform: rotate(-45deg);
  transform: rotate(-45deg);
  width: calc(100% + 0.25rem);
}

.emotion-19 {
  position: fixed;
  right: 100%;
  bottom: 100%;
}

.emotion-30 {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  color: #666;
  font-size: 0.875rem;
  margin-left: 4px;
  min-width: 22px;
  max-width: 28px;
  white-space: nowrap;
}

<div
  class="emotion-0"
>
  <div
    class="emotion-1"
  >
    <div
      class="emotion-2"
    >
      <div
        class="emotion-3"
      >
        <input
          aria-disabled="false"
          aria-label="carbon"
          class="emotion-4"
          data-product-image-alt-text="Split Neck Tee in Carbon"
          data-product-image-src="images/product-photos/old-navy/split-neck-tee/product--carbon.jpg"
          id="0000"
          name="color-radio"
          type="radio"
          value="0000"
        />
        <img
          alt="carbon"
          aria-hidden="true"
          class="emotion-5"
          src="images/swatches/old-navy/split-neck-tee/swatch--carbon.jpg"
        />
      </div>
    </div>
  </div>
  <div
    class="emotion-1"
  >
    <div
      class="emotion-2"
    >
      <div
        class="emotion-3"
      >
        <input
          aria-disabled="false"
          aria-label="coral retro stripe"
          class="emotion-4"
          data-product-image-alt-text="Split Neck Tee in Coral Retro Stripe"
          data-product-image-src="images/product-photos/old-navy/split-neck-tee/product--coral-retro-stripe.jpg"
          id="0001"
          name="color-radio"
          type="radio"
          value="0001"
        />
        <img
          alt="coral retro stripe"
          aria-hidden="true"
          class="emotion-5"
          src="images/swatches/old-navy/split-neck-tee/swatch--coral-retro-stripe.jpg"
        />
      </div>
    </div>
  </div>
  <div
    class="emotion-1"
  >
    <div
      class="emotion-2"
    >
      <div
        class="emotion-3"
      >
        <input
          aria-describedby="0002-outofstock"
          aria-disabled="true"
          aria-label="coral tropics"
          class="emotion-4"
          data-product-image-alt-text="Split Neck Tee in Coral Tropics"
          data-product-image-src="images/product-photos/old-navy/split-neck-tee/product--coral-tropics.jpg"
          id="0002"
          name="color-radio"
          type="radio"
          value="0002"
        />
        <img
          alt="coral tropics"
          aria-hidden="true"
          class="emotion-5"
          src="images/swatches/old-navy/split-neck-tee/swatch--coral-tropics.jpg"
        />
        <svg
          class="emotion-16"
          viewBox="0 0 37 23"
        >
          <g
            fill="none"
            fill-rule="evenodd"
            stroke="none"
            stroke-width="1"
          >
            <g
              class="emotion-17"
              fill="#000000"
              stroke="#FFFFFF"
            >
              <rect
                class="emotion-18"
                height="2"
              />
            </g>
          </g>
        </svg>
      </div>
      <span
        class="emotion-19"
        id="0002-outofstock"
      >
        fui.color_swatch.out_of_stock
      </span>
    </div>
  </div>
  <div
    class="emotion-1"
  >
    <div
      class="emotion-2"
    >
      <div
        class="emotion-3"
      >
        <input
          aria-disabled="false"
          aria-label="enchant mint"
          class="emotion-4"
          data-product-image-alt-text="Split Neck Tee in Enchant Mint"
          data-product-image-src="images/product-photos/old-navy/split-neck-tee/product--enchant-mint.jpg"
          id="0003"
          name="color-radio"
          type="radio"
          value="0003"
        />
        <img
          alt="enchant mint"
          aria-hidden="true"
          class="emotion-5"
          src="images/swatches/old-navy/split-neck-tee/swatch--enchant-mint.jpg"
        />
      </div>
    </div>
  </div>
  <div
    class="emotion-1"
  >
    <div
      class="emotion-2"
    >
      <div
        class="emotion-3"
      >
        <input
          aria-disabled="false"
          aria-label="goldenopportunity"
          class="emotion-4"
          data-product-image-alt-text="Split Neck Tee in Golden Opportunity"
          data-product-image-src="images/product-photos/old-navy/split-neck-tee/product--goldenopportunity.jpg"
          id="0004"
          name="color-radio"
          type="radio"
          value="0004"
        />
        <img
          alt="goldenopportunity"
          aria-hidden="true"
          class="emotion-5"
          src="images/swatches/old-navy/split-neck-tee/swatch--goldenopportunity.jpg"
        />
      </div>
    </div>
  </div>
  <a
    aria-label="fui.swatches.all_colors_link_aria_label"
    class="emotion-30"
  >
    + 3
  </a>
</div>
`;

exports[`<LinkSwatches /> snapshots for medium breakpoint renders default state correctly OldNavy 1`] = `
.emotion-0 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
}

.emotion-0>div {
  margin-right: 2px;
}

.emotion-1 {
  display: inline-block;
  position: relative;
}

.emotion-2 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

@media (pointer: fine) {
  .emotion-2:hover label {
    -webkit-text-decoration: underline;
    text-decoration: underline;
  }
}

@media (pointer: coarse) {
  .emotion-2:active label {
    -webkit-text-decoration: underline;
    text-decoration: underline;
  }
}

.emotion-3 {
  display: inline-block;
  position: relative;
  height: 1.875rem;
  width: 1.875rem;
  text-align: center;
  vertical-align: top;
  white-space: inherit;
  padding: 0.375rem;
}

.emotion-3 .focus-visible+img {
  outline: 0;
  box-shadow: 0 0 0 0.1875rem #5CABF7;
  z-index: -1;
}

.emotion-4 {
  opacity: 0;
  position: absolute;
  cursor: pointer;
  width: 100%;
  height: 100%;
  z-index: 1;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%,-50%);
  -moz-transform: translate(-50%,-50%);
  -ms-transform: translate(-50%,-50%);
  transform: translate(-50%,-50%);
  margin: 0;
}

.emotion-5 {
  width: 100%;
  height: 100%;
  margin: 0;
  border-radius: 50%;
  z-index: 1;
  box-shadow: ;
}

.emotion-16 {
  z-index: 2;
  position: absolute;
  width: 75%;
  height: 75%;
  top: 50%;
  left: 50%;
  margin: 0;
  -webkit-transform: translate(-50%,-50%);
  -moz-transform: translate(-50%,-50%);
  -ms-transform: translate(-50%,-50%);
  transform: translate(-50%,-50%);
  pointer-events: none;
}

.emotion-17 {
  -webkit-transform: translate(10%, 110%);
  -moz-transform: translate(10%, 110%);
  -ms-transform: translate(10%, 110%);
  transform: translate(10%, 110%);
}

.emotion-18 {
  -webkit-transform: rotate(-45deg);
  -moz-transform: rotate(-45deg);
  -ms-transform: rotate(-45deg);
  transform: rotate(-45deg);
  width: calc(100% + 0.25rem);
}

.emotion-19 {
  position: fixed;
  right: 100%;
  bottom: 100%;
}

.emotion-30 {
  font-family: 'FranklinGothic','Open Sans','Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  color: #666;
  font-size: 0.875rem;
  margin-left: 4px;
  min-width: 22px;
  max-width: 28px;
  white-space: nowrap;
}

<div
  class="emotion-0"
>
  <div
    class="emotion-1"
  >
    <div
      class="emotion-2"
    >
      <div
        class="emotion-3"
      >
        <input
          aria-disabled="false"
          aria-label="carbon"
          class="emotion-4"
          data-product-image-alt-text="Split Neck Tee in Carbon"
          data-product-image-src="images/product-photos/old-navy/split-neck-tee/product--carbon.jpg"
          id="0000"
          name="color-radio"
          type="radio"
          value="0000"
        />
        <img
          alt="carbon"
          aria-hidden="true"
          class="emotion-5"
          src="images/swatches/old-navy/split-neck-tee/swatch--carbon.jpg"
        />
      </div>
    </div>
  </div>
  <div
    class="emotion-1"
  >
    <div
      class="emotion-2"
    >
      <div
        class="emotion-3"
      >
        <input
          aria-disabled="false"
          aria-label="coral retro stripe"
          class="emotion-4"
          data-product-image-alt-text="Split Neck Tee in Coral Retro Stripe"
          data-product-image-src="images/product-photos/old-navy/split-neck-tee/product--coral-retro-stripe.jpg"
          id="0001"
          name="color-radio"
          type="radio"
          value="0001"
        />
        <img
          alt="coral retro stripe"
          aria-hidden="true"
          class="emotion-5"
          src="images/swatches/old-navy/split-neck-tee/swatch--coral-retro-stripe.jpg"
        />
      </div>
    </div>
  </div>
  <div
    class="emotion-1"
  >
    <div
      class="emotion-2"
    >
      <div
        class="emotion-3"
      >
        <input
          aria-describedby="0002-outofstock"
          aria-disabled="true"
          aria-label="coral tropics"
          class="emotion-4"
          data-product-image-alt-text="Split Neck Tee in Coral Tropics"
          data-product-image-src="images/product-photos/old-navy/split-neck-tee/product--coral-tropics.jpg"
          id="0002"
          name="color-radio"
          type="radio"
          value="0002"
        />
        <img
          alt="coral tropics"
          aria-hidden="true"
          class="emotion-5"
          src="images/swatches/old-navy/split-neck-tee/swatch--coral-tropics.jpg"
        />
        <svg
          class="emotion-16"
          viewBox="0 0 37 23"
        >
          <g
            fill="none"
            fill-rule="evenodd"
            stroke="none"
            stroke-width="1"
          >
            <g
              class="emotion-17"
              fill="#000000"
              stroke="#FFFFFF"
            >
              <rect
                class="emotion-18"
                height="2"
              />
            </g>
          </g>
        </svg>
      </div>
      <span
        class="emotion-19"
        id="0002-outofstock"
      >
        fui.color_swatch.out_of_stock
      </span>
    </div>
  </div>
  <div
    class="emotion-1"
  >
    <div
      class="emotion-2"
    >
      <div
        class="emotion-3"
      >
        <input
          aria-disabled="false"
          aria-label="enchant mint"
          class="emotion-4"
          data-product-image-alt-text="Split Neck Tee in Enchant Mint"
          data-product-image-src="images/product-photos/old-navy/split-neck-tee/product--enchant-mint.jpg"
          id="0003"
          name="color-radio"
          type="radio"
          value="0003"
        />
        <img
          alt="enchant mint"
          aria-hidden="true"
          class="emotion-5"
          src="images/swatches/old-navy/split-neck-tee/swatch--enchant-mint.jpg"
        />
      </div>
    </div>
  </div>
  <div
    class="emotion-1"
  >
    <div
      class="emotion-2"
    >
      <div
        class="emotion-3"
      >
        <input
          aria-disabled="false"
          aria-label="goldenopportunity"
          class="emotion-4"
          data-product-image-alt-text="Split Neck Tee in Golden Opportunity"
          data-product-image-src="images/product-photos/old-navy/split-neck-tee/product--goldenopportunity.jpg"
          id="0004"
          name="color-radio"
          type="radio"
          value="0004"
        />
        <img
          alt="goldenopportunity"
          aria-hidden="true"
          class="emotion-5"
          src="images/swatches/old-navy/split-neck-tee/swatch--goldenopportunity.jpg"
        />
      </div>
    </div>
  </div>
  <a
    aria-label="fui.swatches.all_colors_link_aria_label"
    class="emotion-30"
  >
    + 3
  </a>
</div>
`;

exports[`<LinkSwatches /> snapshots for medium breakpoint renders default state correctly OldNavy in crossBrand 1`] = `
.emotion-0 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
}

.emotion-0>div {
  margin-right: 2px;
}

.emotion-1 {
  display: inline-block;
  position: relative;
}

.emotion-2 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

@media (pointer: fine) {
  .emotion-2:hover label {
    -webkit-text-decoration: underline;
    text-decoration: underline;
  }
}

@media (pointer: coarse) {
  .emotion-2:active label {
    -webkit-text-decoration: underline;
    text-decoration: underline;
  }
}

.emotion-3 {
  display: inline-block;
  position: relative;
  height: 1.875rem;
  width: 1.875rem;
  text-align: center;
  vertical-align: top;
  white-space: inherit;
  padding: 0.375rem;
}

.emotion-3 .focus-visible+img {
  outline: 0;
  box-shadow: 0 0 0 0.1875rem #5CABF7;
  z-index: -1;
}

.emotion-4 {
  opacity: 0;
  position: absolute;
  cursor: pointer;
  width: 100%;
  height: 100%;
  z-index: 1;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%,-50%);
  -moz-transform: translate(-50%,-50%);
  -ms-transform: translate(-50%,-50%);
  transform: translate(-50%,-50%);
  margin: 0;
}

.emotion-5 {
  width: 100%;
  height: 100%;
  margin: 0;
  border-radius: 50%;
  z-index: 1;
  box-shadow: ;
}

.emotion-16 {
  z-index: 2;
  position: absolute;
  width: 75%;
  height: 75%;
  top: 50%;
  left: 50%;
  margin: 0;
  -webkit-transform: translate(-50%,-50%);
  -moz-transform: translate(-50%,-50%);
  -ms-transform: translate(-50%,-50%);
  transform: translate(-50%,-50%);
  pointer-events: none;
}

.emotion-17 {
  -webkit-transform: translate(10%, 110%);
  -moz-transform: translate(10%, 110%);
  -ms-transform: translate(10%, 110%);
  transform: translate(10%, 110%);
}

.emotion-18 {
  -webkit-transform: rotate(-45deg);
  -moz-transform: rotate(-45deg);
  -ms-transform: rotate(-45deg);
  transform: rotate(-45deg);
  width: calc(100% + 0.25rem);
}

.emotion-19 {
  position: fixed;
  right: 100%;
  bottom: 100%;
}

.emotion-30 {
  font-family: 'FranklinGothic','Open Sans','Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  color: #666;
  font-size: 0.875rem;
  margin-left: 4px;
  min-width: 22px;
  max-width: 28px;
  white-space: nowrap;
}

<div
  class="emotion-0"
>
  <div
    class="emotion-1"
  >
    <div
      class="emotion-2"
    >
      <div
        class="emotion-3"
      >
        <input
          aria-disabled="false"
          aria-label="carbon"
          class="emotion-4"
          data-product-image-alt-text="Split Neck Tee in Carbon"
          data-product-image-src="images/product-photos/old-navy/split-neck-tee/product--carbon.jpg"
          id="0000"
          name="color-radio"
          type="radio"
          value="0000"
        />
        <img
          alt="carbon"
          aria-hidden="true"
          class="emotion-5"
          src="images/swatches/old-navy/split-neck-tee/swatch--carbon.jpg"
        />
      </div>
    </div>
  </div>
  <div
    class="emotion-1"
  >
    <div
      class="emotion-2"
    >
      <div
        class="emotion-3"
      >
        <input
          aria-disabled="false"
          aria-label="coral retro stripe"
          class="emotion-4"
          data-product-image-alt-text="Split Neck Tee in Coral Retro Stripe"
          data-product-image-src="images/product-photos/old-navy/split-neck-tee/product--coral-retro-stripe.jpg"
          id="0001"
          name="color-radio"
          type="radio"
          value="0001"
        />
        <img
          alt="coral retro stripe"
          aria-hidden="true"
          class="emotion-5"
          src="images/swatches/old-navy/split-neck-tee/swatch--coral-retro-stripe.jpg"
        />
      </div>
    </div>
  </div>
  <div
    class="emotion-1"
  >
    <div
      class="emotion-2"
    >
      <div
        class="emotion-3"
      >
        <input
          aria-describedby="0002-outofstock"
          aria-disabled="true"
          aria-label="coral tropics"
          class="emotion-4"
          data-product-image-alt-text="Split Neck Tee in Coral Tropics"
          data-product-image-src="images/product-photos/old-navy/split-neck-tee/product--coral-tropics.jpg"
          id="0002"
          name="color-radio"
          type="radio"
          value="0002"
        />
        <img
          alt="coral tropics"
          aria-hidden="true"
          class="emotion-5"
          src="images/swatches/old-navy/split-neck-tee/swatch--coral-tropics.jpg"
        />
        <svg
          class="emotion-16"
          viewBox="0 0 37 23"
        >
          <g
            fill="none"
            fill-rule="evenodd"
            stroke="none"
            stroke-width="1"
          >
            <g
              class="emotion-17"
              fill="#000000"
              stroke="#FFFFFF"
            >
              <rect
                class="emotion-18"
                height="2"
              />
            </g>
          </g>
        </svg>
      </div>
      <span
        class="emotion-19"
        id="0002-outofstock"
      >
        fui.color_swatch.out_of_stock
      </span>
    </div>
  </div>
  <div
    class="emotion-1"
  >
    <div
      class="emotion-2"
    >
      <div
        class="emotion-3"
      >
        <input
          aria-disabled="false"
          aria-label="enchant mint"
          class="emotion-4"
          data-product-image-alt-text="Split Neck Tee in Enchant Mint"
          data-product-image-src="images/product-photos/old-navy/split-neck-tee/product--enchant-mint.jpg"
          id="0003"
          name="color-radio"
          type="radio"
          value="0003"
        />
        <img
          alt="enchant mint"
          aria-hidden="true"
          class="emotion-5"
          src="images/swatches/old-navy/split-neck-tee/swatch--enchant-mint.jpg"
        />
      </div>
    </div>
  </div>
  <div
    class="emotion-1"
  >
    <div
      class="emotion-2"
    >
      <div
        class="emotion-3"
      >
        <input
          aria-disabled="false"
          aria-label="goldenopportunity"
          class="emotion-4"
          data-product-image-alt-text="Split Neck Tee in Golden Opportunity"
          data-product-image-src="images/product-photos/old-navy/split-neck-tee/product--goldenopportunity.jpg"
          id="0004"
          name="color-radio"
          type="radio"
          value="0004"
        />
        <img
          alt="goldenopportunity"
          aria-hidden="true"
          class="emotion-5"
          src="images/swatches/old-navy/split-neck-tee/swatch--goldenopportunity.jpg"
        />
      </div>
    </div>
  </div>
  <a
    aria-label="fui.swatches.all_colors_link_aria_label"
    class="emotion-30"
  >
    + 3
  </a>
</div>
`;
