// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`<TagLinkGroup /> for inverse styles renders default state correctly Athleta 1`] = `
<DocumentFragment>
  .emotion-1 {
  font-family: 'Avenir Next','Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 600;
  letter-spacing: 0.5px;
  font-size: 1rem;
  color: #FFFFFF;
}

.emotion-2 {
  width: 100%;
  display: block;
  margin-top: 1rem;
  overflow: hidden;
  height: 1.75rem;
}

.emotion-3 {
  display: inline-block;
  margin-right: 0.5rem;
  margin-bottom: 0.75rem;
}

.emotion-4 {
  font-family: 'Avenir Next','Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  display: inline-block;
  border-radius: 1rem;
  padding: 0.25rem 0.5rem;
  font-size: 0.875rem;
  line-height: 0.75rem;
  text-transform: capitalize;
  outline: none;
  white-space: nowrap;
  color: #FFFFFF;
  background-color: #555555;
}

.emotion-4.focus-visible,
.emotion-4:hover {
  background-color: #999999;
}

.emotion-27 {
  background: none;
  border: none;
  width: auto;
  margin: 0;
  padding: 0;
  text-align: inherit;
  font: inherit;
  cursor: pointer;
  text-decoration-skip-ink: auto;
  -webkit-text-decoration: underline;
  text-decoration: underline;
  letter-spacing: 0.5px;
  color: #FFFFFF;
  font-size: 0.875rem;
}

<div
    class="emotion-0"
    data-testid="tag-link-group"
  >
    <h2
      class="emotion-1"
      data-testid="tag-link-group-heading"
    >
      tag_link_group.related_categories_heading
    </h2>
    <ul
      class="emotion-2"
    >
      <li
        class="emotion-3"
      >
        <a
          aria-label="Shorts"
          class="tag-link-group__text emotion-4"
          data-testid="tag-link"
          href="/browse/category.do?cid=1025764&nvt=Left%20Nav&nvt=GapMaternity&nvt=Categories&nvt=Shorts"
        >
          Shorts
        </a>
      </li>
      <li
        class="emotion-3"
      >
        <a
          aria-label="Shirts & Tops"
          class="tag-link-group__text emotion-4"
          data-testid="tag-link"
          href="/browse/category.do?cid=1014424&nvt=Left%20Nav&nvt=GapMaternity&nvt=Categories&nvt=Shirts%20%26%20Tops"
        >
          Shirts & Tops
        </a>
      </li>
      <li
        class="emotion-3"
      >
        <a
          aria-label="T-Shirts "
          class="tag-link-group__text emotion-4"
          data-testid="tag-link"
          href="/browse/category.do?cid=6049&nvt=Left%20Nav&nvt=GapMaternity&nvt=Categories&nvt=T-Shirts%20"
        >
          T-Shirts 
        </a>
      </li>
      <li
        class="emotion-3"
      >
        <a
          aria-label="Sweaters & Sweatshirts"
          class="tag-link-group__text emotion-4"
          data-testid="tag-link"
          href="/browse/category.do?cid=1019160&nvt=Left%20Nav&nvt=GapMaternity&nvt=Categories&nvt=Sweaters%20%26%20Sweatshirts"
        >
          Sweaters & Sweatshirts
        </a>
      </li>
      <li
        class="emotion-3"
      >
        <a
          aria-label="Swim"
          class="tag-link-group__text emotion-4"
          data-testid="tag-link"
          href="/browse/category.do?cid=345345&nvt=Left%20Nav&nvt=GapMaternity&nvt=Categories&nvt=Swim"
        >
          Swim
        </a>
      </li>
      <li
        class="emotion-3"
      >
        <a
          aria-label="Toddler Girl"
          class="tag-link-group__text emotion-4"
          data-testid="tag-link"
          href="/browse/category.do?cid=345345&nvt=Left%20Nav&nvt=GapMaternity&nvt=Categories&nvt=toddler"
        >
          Toddler Girl
        </a>
      </li>
      <li
        class="emotion-3"
      >
        <a
          aria-label="Toddler Boys"
          class="tag-link-group__text emotion-4"
          data-testid="tag-link"
          href="/browse/category.do?cid=345345&nvt=Left%20Nav&nvt=GapMaternity&nvt=Categories&nvt=toddler"
        >
          Toddler Boys
        </a>
      </li>
      <li
        class="emotion-3"
      >
        <a
          aria-label="Sports"
          class="tag-link-group__text emotion-4"
          data-testid="tag-link"
          href="/browse/category.do?cid=345345&nvt=Left%20Nav&nvt=GapMaternity&nvt=Categories&nvt=Sports"
        >
          Sports
        </a>
      </li>
      <li
        class="emotion-3"
      >
        <a
          aria-label="Socks & Tights"
          class="tag-link-group__text emotion-4"
          data-testid="tag-link"
          href="/browse/category.do?cid=345345&nvt=Left%20Nav&nvt=GapMaternity&nvt=Categories&nvt=Socks"
        >
          Socks & Tights
        </a>
      </li>
      <li
        class="emotion-3"
      >
        <a
          aria-label="Jackets"
          class="tag-link-group__text emotion-4"
          data-testid="tag-link"
          href="/browse/category.do?cid=345345&nvt=Left%20Nav&nvt=GapMaternity&nvt=Categories&nvt=Jackets"
        >
          Jackets
        </a>
      </li>
      <li
        class="emotion-3"
      >
        <a
          aria-label="Dresses & Skirts"
          class="tag-link-group__text emotion-4"
          data-testid="tag-link"
          href="/browse/category.do?cid=345345&nvt=Left%20Nav&nvt=GapMaternity&nvt=Categories&nvt=Skirts"
        >
          Dresses & Skirts
        </a>
      </li>
      <li
        class="emotion-3"
      >
        <a
          aria-label="Gap Factory"
          class="tag-link-group__text emotion-4"
          data-testid="tag-link"
          href="https://www.gapfactory.com/"
        >
          Gap Factory
        </a>
      </li>
    </ul>
    <div
      data-testid="tag-link-group-footer"
    >
      <button
        class="emotion-27"
        type="button"
      >
        tag_link_group.see_more_related_categories_text
      </button>
    </div>
  </div>
</DocumentFragment>
`;

exports[`<TagLinkGroup /> for inverse styles renders default state correctly Athleta in crossBrand 1`] = `
<DocumentFragment>
  .emotion-1 {
  font-family: 'Avenir Next','Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 600;
  letter-spacing: 0.5px;
  font-size: 1rem;
  color: #FFFFFF;
}

.emotion-2 {
  width: 100%;
  display: block;
  margin-top: 1rem;
  overflow: hidden;
  height: 1.75rem;
}

.emotion-3 {
  display: inline-block;
  margin-right: 0.5rem;
  margin-bottom: 0.75rem;
}

.emotion-4 {
  font-family: 'Avenir Next','Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  display: inline-block;
  border-radius: 1rem;
  padding: 0.25rem 0.5rem;
  font-size: 0.875rem;
  line-height: 0.75rem;
  text-transform: capitalize;
  outline: none;
  white-space: nowrap;
  color: #FFFFFF;
  background-color: #555555;
}

.emotion-4.focus-visible,
.emotion-4:hover {
  background-color: #999999;
}

.emotion-27 {
  background: none;
  border: none;
  width: auto;
  margin: 0;
  padding: 0;
  text-align: inherit;
  font: inherit;
  cursor: pointer;
  -webkit-text-decoration: none;
  text-decoration: none;
  color: #FFFFFF;
  font-size: 0.875rem;
}

<div
    class="emotion-0"
    data-testid="tag-link-group"
  >
    <h2
      class="emotion-1"
      data-testid="tag-link-group-heading"
    >
      tag_link_group.related_categories_heading
    </h2>
    <ul
      class="emotion-2"
    >
      <li
        class="emotion-3"
      >
        <a
          aria-label="Shorts"
          class="tag-link-group__text emotion-4"
          data-testid="tag-link"
          href="/browse/category.do?cid=1025764&nvt=Left%20Nav&nvt=GapMaternity&nvt=Categories&nvt=Shorts"
        >
          Shorts
        </a>
      </li>
      <li
        class="emotion-3"
      >
        <a
          aria-label="Shirts & Tops"
          class="tag-link-group__text emotion-4"
          data-testid="tag-link"
          href="/browse/category.do?cid=1014424&nvt=Left%20Nav&nvt=GapMaternity&nvt=Categories&nvt=Shirts%20%26%20Tops"
        >
          Shirts & Tops
        </a>
      </li>
      <li
        class="emotion-3"
      >
        <a
          aria-label="T-Shirts "
          class="tag-link-group__text emotion-4"
          data-testid="tag-link"
          href="/browse/category.do?cid=6049&nvt=Left%20Nav&nvt=GapMaternity&nvt=Categories&nvt=T-Shirts%20"
        >
          T-Shirts 
        </a>
      </li>
      <li
        class="emotion-3"
      >
        <a
          aria-label="Sweaters & Sweatshirts"
          class="tag-link-group__text emotion-4"
          data-testid="tag-link"
          href="/browse/category.do?cid=1019160&nvt=Left%20Nav&nvt=GapMaternity&nvt=Categories&nvt=Sweaters%20%26%20Sweatshirts"
        >
          Sweaters & Sweatshirts
        </a>
      </li>
      <li
        class="emotion-3"
      >
        <a
          aria-label="Swim"
          class="tag-link-group__text emotion-4"
          data-testid="tag-link"
          href="/browse/category.do?cid=345345&nvt=Left%20Nav&nvt=GapMaternity&nvt=Categories&nvt=Swim"
        >
          Swim
        </a>
      </li>
      <li
        class="emotion-3"
      >
        <a
          aria-label="Toddler Girl"
          class="tag-link-group__text emotion-4"
          data-testid="tag-link"
          href="/browse/category.do?cid=345345&nvt=Left%20Nav&nvt=GapMaternity&nvt=Categories&nvt=toddler"
        >
          Toddler Girl
        </a>
      </li>
      <li
        class="emotion-3"
      >
        <a
          aria-label="Toddler Boys"
          class="tag-link-group__text emotion-4"
          data-testid="tag-link"
          href="/browse/category.do?cid=345345&nvt=Left%20Nav&nvt=GapMaternity&nvt=Categories&nvt=toddler"
        >
          Toddler Boys
        </a>
      </li>
      <li
        class="emotion-3"
      >
        <a
          aria-label="Sports"
          class="tag-link-group__text emotion-4"
          data-testid="tag-link"
          href="/browse/category.do?cid=345345&nvt=Left%20Nav&nvt=GapMaternity&nvt=Categories&nvt=Sports"
        >
          Sports
        </a>
      </li>
      <li
        class="emotion-3"
      >
        <a
          aria-label="Socks & Tights"
          class="tag-link-group__text emotion-4"
          data-testid="tag-link"
          href="/browse/category.do?cid=345345&nvt=Left%20Nav&nvt=GapMaternity&nvt=Categories&nvt=Socks"
        >
          Socks & Tights
        </a>
      </li>
      <li
        class="emotion-3"
      >
        <a
          aria-label="Jackets"
          class="tag-link-group__text emotion-4"
          data-testid="tag-link"
          href="/browse/category.do?cid=345345&nvt=Left%20Nav&nvt=GapMaternity&nvt=Categories&nvt=Jackets"
        >
          Jackets
        </a>
      </li>
      <li
        class="emotion-3"
      >
        <a
          aria-label="Dresses & Skirts"
          class="tag-link-group__text emotion-4"
          data-testid="tag-link"
          href="/browse/category.do?cid=345345&nvt=Left%20Nav&nvt=GapMaternity&nvt=Categories&nvt=Skirts"
        >
          Dresses & Skirts
        </a>
      </li>
      <li
        class="emotion-3"
      >
        <a
          aria-label="Gap Factory"
          class="tag-link-group__text emotion-4"
          data-testid="tag-link"
          href="https://www.gapfactory.com/"
        >
          Gap Factory
        </a>
      </li>
    </ul>
    <div
      data-testid="tag-link-group-footer"
    >
      <button
        class="emotion-27"
        type="button"
      >
        tag_link_group.see_more_related_categories_text
      </button>
    </div>
  </div>
</DocumentFragment>
`;

exports[`<TagLinkGroup /> for inverse styles renders default state correctly BananaRepublic 1`] = `
<DocumentFragment>
  .emotion-1 {
  font-family: 'Lynstone',Helvetica,Arial,sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-family: 'BananaSerif','Times New Roman','serif';
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 1rem;
  color: #F6F4EB;
}

.emotion-2 {
  width: 100%;
  display: block;
  margin-top: 1rem;
  overflow: hidden;
  height: 1.75rem;
}

.emotion-3 {
  display: inline-block;
  margin-right: 0.5rem;
  margin-bottom: 0.75rem;
}

.emotion-4 {
  font-family: 'Lynstone',Helvetica,Arial,sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  display: inline-block;
  border-radius: 1rem;
  padding: 0.25rem 0.5rem;
  font-size: 0.875rem;
  line-height: 0.75rem;
  text-transform: capitalize;
  outline: none;
  white-space: nowrap;
  color: #F6F4EB;
  background-color: #54514C;
}

.emotion-4.focus-visible,
.emotion-4:hover {
  background-color: #918E87;
}

.emotion-27 {
  background: none;
  border: none;
  width: auto;
  margin: 0;
  padding: 0;
  text-align: inherit;
  font: inherit;
  cursor: pointer;
  text-decoration-skip-ink: auto;
  -webkit-text-decoration: underline;
  text-decoration: underline;
  color: #F6F4EB;
  font-size: 0.875rem;
}

<div
    class="emotion-0"
    data-testid="tag-link-group"
  >
    <h2
      class="emotion-1"
      data-testid="tag-link-group-heading"
    >
      tag_link_group.related_categories_heading
    </h2>
    <ul
      class="emotion-2"
    >
      <li
        class="emotion-3"
      >
        <a
          aria-label="Shorts"
          class="tag-link-group__text emotion-4"
          data-testid="tag-link"
          href="/browse/category.do?cid=1025764&nvt=Left%20Nav&nvt=GapMaternity&nvt=Categories&nvt=Shorts"
        >
          Shorts
        </a>
      </li>
      <li
        class="emotion-3"
      >
        <a
          aria-label="Shirts & Tops"
          class="tag-link-group__text emotion-4"
          data-testid="tag-link"
          href="/browse/category.do?cid=1014424&nvt=Left%20Nav&nvt=GapMaternity&nvt=Categories&nvt=Shirts%20%26%20Tops"
        >
          Shirts & Tops
        </a>
      </li>
      <li
        class="emotion-3"
      >
        <a
          aria-label="T-Shirts "
          class="tag-link-group__text emotion-4"
          data-testid="tag-link"
          href="/browse/category.do?cid=6049&nvt=Left%20Nav&nvt=GapMaternity&nvt=Categories&nvt=T-Shirts%20"
        >
          T-Shirts 
        </a>
      </li>
      <li
        class="emotion-3"
      >
        <a
          aria-label="Sweaters & Sweatshirts"
          class="tag-link-group__text emotion-4"
          data-testid="tag-link"
          href="/browse/category.do?cid=1019160&nvt=Left%20Nav&nvt=GapMaternity&nvt=Categories&nvt=Sweaters%20%26%20Sweatshirts"
        >
          Sweaters & Sweatshirts
        </a>
      </li>
      <li
        class="emotion-3"
      >
        <a
          aria-label="Swim"
          class="tag-link-group__text emotion-4"
          data-testid="tag-link"
          href="/browse/category.do?cid=345345&nvt=Left%20Nav&nvt=GapMaternity&nvt=Categories&nvt=Swim"
        >
          Swim
        </a>
      </li>
      <li
        class="emotion-3"
      >
        <a
          aria-label="Toddler Girl"
          class="tag-link-group__text emotion-4"
          data-testid="tag-link"
          href="/browse/category.do?cid=345345&nvt=Left%20Nav&nvt=GapMaternity&nvt=Categories&nvt=toddler"
        >
          Toddler Girl
        </a>
      </li>
      <li
        class="emotion-3"
      >
        <a
          aria-label="Toddler Boys"
          class="tag-link-group__text emotion-4"
          data-testid="tag-link"
          href="/browse/category.do?cid=345345&nvt=Left%20Nav&nvt=GapMaternity&nvt=Categories&nvt=toddler"
        >
          Toddler Boys
        </a>
      </li>
      <li
        class="emotion-3"
      >
        <a
          aria-label="Sports"
          class="tag-link-group__text emotion-4"
          data-testid="tag-link"
          href="/browse/category.do?cid=345345&nvt=Left%20Nav&nvt=GapMaternity&nvt=Categories&nvt=Sports"
        >
          Sports
        </a>
      </li>
      <li
        class="emotion-3"
      >
        <a
          aria-label="Socks & Tights"
          class="tag-link-group__text emotion-4"
          data-testid="tag-link"
          href="/browse/category.do?cid=345345&nvt=Left%20Nav&nvt=GapMaternity&nvt=Categories&nvt=Socks"
        >
          Socks & Tights
        </a>
      </li>
      <li
        class="emotion-3"
      >
        <a
          aria-label="Jackets"
          class="tag-link-group__text emotion-4"
          data-testid="tag-link"
          href="/browse/category.do?cid=345345&nvt=Left%20Nav&nvt=GapMaternity&nvt=Categories&nvt=Jackets"
        >
          Jackets
        </a>
      </li>
      <li
        class="emotion-3"
      >
        <a
          aria-label="Dresses & Skirts"
          class="tag-link-group__text emotion-4"
          data-testid="tag-link"
          href="/browse/category.do?cid=345345&nvt=Left%20Nav&nvt=GapMaternity&nvt=Categories&nvt=Skirts"
        >
          Dresses & Skirts
        </a>
      </li>
      <li
        class="emotion-3"
      >
        <a
          aria-label="Gap Factory"
          class="tag-link-group__text emotion-4"
          data-testid="tag-link"
          href="https://www.gapfactory.com/"
        >
          Gap Factory
        </a>
      </li>
    </ul>
    <div
      data-testid="tag-link-group-footer"
    >
      <button
        class="emotion-27"
        type="button"
      >
        tag_link_group.see_more_related_categories_text
      </button>
    </div>
  </div>
</DocumentFragment>
`;

exports[`<TagLinkGroup /> for inverse styles renders default state correctly BananaRepublic in crossBrand 1`] = `
<DocumentFragment>
  .emotion-1 {
  font-family: 'Lynstone',Helvetica,Arial,sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-family: 'BananaSerif','Times New Roman','serif';
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 1rem;
  color: #F6F4EB;
}

.emotion-2 {
  width: 100%;
  display: block;
  margin-top: 1rem;
  overflow: hidden;
  height: 1.75rem;
}

.emotion-3 {
  display: inline-block;
  margin-right: 0.5rem;
  margin-bottom: 0.75rem;
}

.emotion-4 {
  font-family: 'Lynstone',Helvetica,Arial,sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  display: inline-block;
  border-radius: 1rem;
  padding: 0.25rem 0.5rem;
  font-size: 0.875rem;
  line-height: 0.75rem;
  text-transform: capitalize;
  outline: none;
  white-space: nowrap;
  color: #F6F4EB;
  background-color: #54514C;
}

.emotion-4.focus-visible,
.emotion-4:hover {
  background-color: #918E87;
}

.emotion-27 {
  background: none;
  border: none;
  width: auto;
  margin: 0;
  padding: 0;
  text-align: inherit;
  font: inherit;
  cursor: pointer;
  -webkit-text-decoration: none;
  text-decoration: none;
  color: #FFFFFF;
  font-size: 0.875rem;
}

<div
    class="emotion-0"
    data-testid="tag-link-group"
  >
    <h2
      class="emotion-1"
      data-testid="tag-link-group-heading"
    >
      tag_link_group.related_categories_heading
    </h2>
    <ul
      class="emotion-2"
    >
      <li
        class="emotion-3"
      >
        <a
          aria-label="Shorts"
          class="tag-link-group__text emotion-4"
          data-testid="tag-link"
          href="/browse/category.do?cid=1025764&nvt=Left%20Nav&nvt=GapMaternity&nvt=Categories&nvt=Shorts"
        >
          Shorts
        </a>
      </li>
      <li
        class="emotion-3"
      >
        <a
          aria-label="Shirts & Tops"
          class="tag-link-group__text emotion-4"
          data-testid="tag-link"
          href="/browse/category.do?cid=1014424&nvt=Left%20Nav&nvt=GapMaternity&nvt=Categories&nvt=Shirts%20%26%20Tops"
        >
          Shirts & Tops
        </a>
      </li>
      <li
        class="emotion-3"
      >
        <a
          aria-label="T-Shirts "
          class="tag-link-group__text emotion-4"
          data-testid="tag-link"
          href="/browse/category.do?cid=6049&nvt=Left%20Nav&nvt=GapMaternity&nvt=Categories&nvt=T-Shirts%20"
        >
          T-Shirts 
        </a>
      </li>
      <li
        class="emotion-3"
      >
        <a
          aria-label="Sweaters & Sweatshirts"
          class="tag-link-group__text emotion-4"
          data-testid="tag-link"
          href="/browse/category.do?cid=1019160&nvt=Left%20Nav&nvt=GapMaternity&nvt=Categories&nvt=Sweaters%20%26%20Sweatshirts"
        >
          Sweaters & Sweatshirts
        </a>
      </li>
      <li
        class="emotion-3"
      >
        <a
          aria-label="Swim"
          class="tag-link-group__text emotion-4"
          data-testid="tag-link"
          href="/browse/category.do?cid=345345&nvt=Left%20Nav&nvt=GapMaternity&nvt=Categories&nvt=Swim"
        >
          Swim
        </a>
      </li>
      <li
        class="emotion-3"
      >
        <a
          aria-label="Toddler Girl"
          class="tag-link-group__text emotion-4"
          data-testid="tag-link"
          href="/browse/category.do?cid=345345&nvt=Left%20Nav&nvt=GapMaternity&nvt=Categories&nvt=toddler"
        >
          Toddler Girl
        </a>
      </li>
      <li
        class="emotion-3"
      >
        <a
          aria-label="Toddler Boys"
          class="tag-link-group__text emotion-4"
          data-testid="tag-link"
          href="/browse/category.do?cid=345345&nvt=Left%20Nav&nvt=GapMaternity&nvt=Categories&nvt=toddler"
        >
          Toddler Boys
        </a>
      </li>
      <li
        class="emotion-3"
      >
        <a
          aria-label="Sports"
          class="tag-link-group__text emotion-4"
          data-testid="tag-link"
          href="/browse/category.do?cid=345345&nvt=Left%20Nav&nvt=GapMaternity&nvt=Categories&nvt=Sports"
        >
          Sports
        </a>
      </li>
      <li
        class="emotion-3"
      >
        <a
          aria-label="Socks & Tights"
          class="tag-link-group__text emotion-4"
          data-testid="tag-link"
          href="/browse/category.do?cid=345345&nvt=Left%20Nav&nvt=GapMaternity&nvt=Categories&nvt=Socks"
        >
          Socks & Tights
        </a>
      </li>
      <li
        class="emotion-3"
      >
        <a
          aria-label="Jackets"
          class="tag-link-group__text emotion-4"
          data-testid="tag-link"
          href="/browse/category.do?cid=345345&nvt=Left%20Nav&nvt=GapMaternity&nvt=Categories&nvt=Jackets"
        >
          Jackets
        </a>
      </li>
      <li
        class="emotion-3"
      >
        <a
          aria-label="Dresses & Skirts"
          class="tag-link-group__text emotion-4"
          data-testid="tag-link"
          href="/browse/category.do?cid=345345&nvt=Left%20Nav&nvt=GapMaternity&nvt=Categories&nvt=Skirts"
        >
          Dresses & Skirts
        </a>
      </li>
      <li
        class="emotion-3"
      >
        <a
          aria-label="Gap Factory"
          class="tag-link-group__text emotion-4"
          data-testid="tag-link"
          href="https://www.gapfactory.com/"
        >
          Gap Factory
        </a>
      </li>
    </ul>
    <div
      data-testid="tag-link-group-footer"
    >
      <button
        class="emotion-27"
        type="button"
      >
        tag_link_group.see_more_related_categories_text
      </button>
    </div>
  </div>
</DocumentFragment>
`;

exports[`<TagLinkGroup /> for inverse styles renders default state correctly BananaRepublicFactoryStore 1`] = `
<DocumentFragment>
  .emotion-1 {
  font-family: 'Lynstone',Helvetica,Arial,sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-family: 'BananaSerif','Times New Roman','serif';
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 1rem;
  color: #F6F4EB;
}

.emotion-2 {
  width: 100%;
  display: block;
  margin-top: 1rem;
  overflow: hidden;
  height: 1.75rem;
}

.emotion-3 {
  display: inline-block;
  margin-right: 0.5rem;
  margin-bottom: 0.75rem;
}

.emotion-4 {
  font-family: 'Lynstone',Helvetica,Arial,sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  display: inline-block;
  border-radius: 1rem;
  padding: 0.25rem 0.5rem;
  font-size: 0.875rem;
  line-height: 0.75rem;
  text-transform: capitalize;
  outline: none;
  white-space: nowrap;
  color: #F6F4EB;
  background-color: #54514C;
}

.emotion-4.focus-visible,
.emotion-4:hover {
  background-color: #918E87;
}

.emotion-27 {
  background: none;
  border: none;
  width: auto;
  margin: 0;
  padding: 0;
  text-align: inherit;
  font: inherit;
  cursor: pointer;
  text-decoration-skip-ink: auto;
  -webkit-text-decoration: underline;
  text-decoration: underline;
  color: #F6F4EB;
  font-size: 0.875rem;
}

<div
    class="emotion-0"
    data-testid="tag-link-group"
  >
    <h2
      class="emotion-1"
      data-testid="tag-link-group-heading"
    >
      tag_link_group.related_categories_heading
    </h2>
    <ul
      class="emotion-2"
    >
      <li
        class="emotion-3"
      >
        <a
          aria-label="Shorts"
          class="tag-link-group__text emotion-4"
          data-testid="tag-link"
          href="/browse/category.do?cid=1025764&nvt=Left%20Nav&nvt=GapMaternity&nvt=Categories&nvt=Shorts"
        >
          Shorts
        </a>
      </li>
      <li
        class="emotion-3"
      >
        <a
          aria-label="Shirts & Tops"
          class="tag-link-group__text emotion-4"
          data-testid="tag-link"
          href="/browse/category.do?cid=1014424&nvt=Left%20Nav&nvt=GapMaternity&nvt=Categories&nvt=Shirts%20%26%20Tops"
        >
          Shirts & Tops
        </a>
      </li>
      <li
        class="emotion-3"
      >
        <a
          aria-label="T-Shirts "
          class="tag-link-group__text emotion-4"
          data-testid="tag-link"
          href="/browse/category.do?cid=6049&nvt=Left%20Nav&nvt=GapMaternity&nvt=Categories&nvt=T-Shirts%20"
        >
          T-Shirts 
        </a>
      </li>
      <li
        class="emotion-3"
      >
        <a
          aria-label="Sweaters & Sweatshirts"
          class="tag-link-group__text emotion-4"
          data-testid="tag-link"
          href="/browse/category.do?cid=1019160&nvt=Left%20Nav&nvt=GapMaternity&nvt=Categories&nvt=Sweaters%20%26%20Sweatshirts"
        >
          Sweaters & Sweatshirts
        </a>
      </li>
      <li
        class="emotion-3"
      >
        <a
          aria-label="Swim"
          class="tag-link-group__text emotion-4"
          data-testid="tag-link"
          href="/browse/category.do?cid=345345&nvt=Left%20Nav&nvt=GapMaternity&nvt=Categories&nvt=Swim"
        >
          Swim
        </a>
      </li>
      <li
        class="emotion-3"
      >
        <a
          aria-label="Toddler Girl"
          class="tag-link-group__text emotion-4"
          data-testid="tag-link"
          href="/browse/category.do?cid=345345&nvt=Left%20Nav&nvt=GapMaternity&nvt=Categories&nvt=toddler"
        >
          Toddler Girl
        </a>
      </li>
      <li
        class="emotion-3"
      >
        <a
          aria-label="Toddler Boys"
          class="tag-link-group__text emotion-4"
          data-testid="tag-link"
          href="/browse/category.do?cid=345345&nvt=Left%20Nav&nvt=GapMaternity&nvt=Categories&nvt=toddler"
        >
          Toddler Boys
        </a>
      </li>
      <li
        class="emotion-3"
      >
        <a
          aria-label="Sports"
          class="tag-link-group__text emotion-4"
          data-testid="tag-link"
          href="/browse/category.do?cid=345345&nvt=Left%20Nav&nvt=GapMaternity&nvt=Categories&nvt=Sports"
        >
          Sports
        </a>
      </li>
      <li
        class="emotion-3"
      >
        <a
          aria-label="Socks & Tights"
          class="tag-link-group__text emotion-4"
          data-testid="tag-link"
          href="/browse/category.do?cid=345345&nvt=Left%20Nav&nvt=GapMaternity&nvt=Categories&nvt=Socks"
        >
          Socks & Tights
        </a>
      </li>
      <li
        class="emotion-3"
      >
        <a
          aria-label="Jackets"
          class="tag-link-group__text emotion-4"
          data-testid="tag-link"
          href="/browse/category.do?cid=345345&nvt=Left%20Nav&nvt=GapMaternity&nvt=Categories&nvt=Jackets"
        >
          Jackets
        </a>
      </li>
      <li
        class="emotion-3"
      >
        <a
          aria-label="Dresses & Skirts"
          class="tag-link-group__text emotion-4"
          data-testid="tag-link"
          href="/browse/category.do?cid=345345&nvt=Left%20Nav&nvt=GapMaternity&nvt=Categories&nvt=Skirts"
        >
          Dresses & Skirts
        </a>
      </li>
      <li
        class="emotion-3"
      >
        <a
          aria-label="Gap Factory"
          class="tag-link-group__text emotion-4"
          data-testid="tag-link"
          href="https://www.gapfactory.com/"
        >
          Gap Factory
        </a>
      </li>
    </ul>
    <div
      data-testid="tag-link-group-footer"
    >
      <button
        class="emotion-27"
        type="button"
      >
        tag_link_group.see_more_related_categories_text
      </button>
    </div>
  </div>
</DocumentFragment>
`;

exports[`<TagLinkGroup /> for inverse styles renders default state correctly BananaRepublicFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-1 {
  font-family: 'Lynstone',Helvetica,Arial,sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-family: 'BananaSerif','Times New Roman','serif';
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 1rem;
  color: #F6F4EB;
}

.emotion-2 {
  width: 100%;
  display: block;
  margin-top: 1rem;
  overflow: hidden;
  height: 1.75rem;
}

.emotion-3 {
  display: inline-block;
  margin-right: 0.5rem;
  margin-bottom: 0.75rem;
}

.emotion-4 {
  font-family: 'Lynstone',Helvetica,Arial,sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  display: inline-block;
  border-radius: 1rem;
  padding: 0.25rem 0.5rem;
  font-size: 0.875rem;
  line-height: 0.75rem;
  text-transform: capitalize;
  outline: none;
  white-space: nowrap;
  color: #F6F4EB;
  background-color: #54514C;
}

.emotion-4.focus-visible,
.emotion-4:hover {
  background-color: #918E87;
}

.emotion-27 {
  background: none;
  border: none;
  width: auto;
  margin: 0;
  padding: 0;
  text-align: inherit;
  font: inherit;
  cursor: pointer;
  -webkit-text-decoration: none;
  text-decoration: none;
  color: #FFFFFF;
  font-size: 0.875rem;
}

<div
    class="emotion-0"
    data-testid="tag-link-group"
  >
    <h2
      class="emotion-1"
      data-testid="tag-link-group-heading"
    >
      tag_link_group.related_categories_heading
    </h2>
    <ul
      class="emotion-2"
    >
      <li
        class="emotion-3"
      >
        <a
          aria-label="Shorts"
          class="tag-link-group__text emotion-4"
          data-testid="tag-link"
          href="/browse/category.do?cid=1025764&nvt=Left%20Nav&nvt=GapMaternity&nvt=Categories&nvt=Shorts"
        >
          Shorts
        </a>
      </li>
      <li
        class="emotion-3"
      >
        <a
          aria-label="Shirts & Tops"
          class="tag-link-group__text emotion-4"
          data-testid="tag-link"
          href="/browse/category.do?cid=1014424&nvt=Left%20Nav&nvt=GapMaternity&nvt=Categories&nvt=Shirts%20%26%20Tops"
        >
          Shirts & Tops
        </a>
      </li>
      <li
        class="emotion-3"
      >
        <a
          aria-label="T-Shirts "
          class="tag-link-group__text emotion-4"
          data-testid="tag-link"
          href="/browse/category.do?cid=6049&nvt=Left%20Nav&nvt=GapMaternity&nvt=Categories&nvt=T-Shirts%20"
        >
          T-Shirts 
        </a>
      </li>
      <li
        class="emotion-3"
      >
        <a
          aria-label="Sweaters & Sweatshirts"
          class="tag-link-group__text emotion-4"
          data-testid="tag-link"
          href="/browse/category.do?cid=1019160&nvt=Left%20Nav&nvt=GapMaternity&nvt=Categories&nvt=Sweaters%20%26%20Sweatshirts"
        >
          Sweaters & Sweatshirts
        </a>
      </li>
      <li
        class="emotion-3"
      >
        <a
          aria-label="Swim"
          class="tag-link-group__text emotion-4"
          data-testid="tag-link"
          href="/browse/category.do?cid=345345&nvt=Left%20Nav&nvt=GapMaternity&nvt=Categories&nvt=Swim"
        >
          Swim
        </a>
      </li>
      <li
        class="emotion-3"
      >
        <a
          aria-label="Toddler Girl"
          class="tag-link-group__text emotion-4"
          data-testid="tag-link"
          href="/browse/category.do?cid=345345&nvt=Left%20Nav&nvt=GapMaternity&nvt=Categories&nvt=toddler"
        >
          Toddler Girl
        </a>
      </li>
      <li
        class="emotion-3"
      >
        <a
          aria-label="Toddler Boys"
          class="tag-link-group__text emotion-4"
          data-testid="tag-link"
          href="/browse/category.do?cid=345345&nvt=Left%20Nav&nvt=GapMaternity&nvt=Categories&nvt=toddler"
        >
          Toddler Boys
        </a>
      </li>
      <li
        class="emotion-3"
      >
        <a
          aria-label="Sports"
          class="tag-link-group__text emotion-4"
          data-testid="tag-link"
          href="/browse/category.do?cid=345345&nvt=Left%20Nav&nvt=GapMaternity&nvt=Categories&nvt=Sports"
        >
          Sports
        </a>
      </li>
      <li
        class="emotion-3"
      >
        <a
          aria-label="Socks & Tights"
          class="tag-link-group__text emotion-4"
          data-testid="tag-link"
          href="/browse/category.do?cid=345345&nvt=Left%20Nav&nvt=GapMaternity&nvt=Categories&nvt=Socks"
        >
          Socks & Tights
        </a>
      </li>
      <li
        class="emotion-3"
      >
        <a
          aria-label="Jackets"
          class="tag-link-group__text emotion-4"
          data-testid="tag-link"
          href="/browse/category.do?cid=345345&nvt=Left%20Nav&nvt=GapMaternity&nvt=Categories&nvt=Jackets"
        >
          Jackets
        </a>
      </li>
      <li
        class="emotion-3"
      >
        <a
          aria-label="Dresses & Skirts"
          class="tag-link-group__text emotion-4"
          data-testid="tag-link"
          href="/browse/category.do?cid=345345&nvt=Left%20Nav&nvt=GapMaternity&nvt=Categories&nvt=Skirts"
        >
          Dresses & Skirts
        </a>
      </li>
      <li
        class="emotion-3"
      >
        <a
          aria-label="Gap Factory"
          class="tag-link-group__text emotion-4"
          data-testid="tag-link"
          href="https://www.gapfactory.com/"
        >
          Gap Factory
        </a>
      </li>
    </ul>
    <div
      data-testid="tag-link-group-footer"
    >
      <button
        class="emotion-27"
        type="button"
      >
        tag_link_group.see_more_related_categories_text
      </button>
    </div>
  </div>
</DocumentFragment>
`;

exports[`<TagLinkGroup /> for inverse styles renders default state correctly Gap 1`] = `
<DocumentFragment>
  .emotion-1 {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 1.067rem;
  font-size: 1rem;
  color: #FFFFFF;
}

.emotion-2 {
  width: 100%;
  display: block;
  margin-top: 1rem;
  overflow: hidden;
  height: 1.75rem;
}

.emotion-3 {
  display: inline-block;
  margin-right: 0.5rem;
  margin-bottom: 0.75rem;
}

.emotion-4 {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  display: inline-block;
  border-radius: 1rem;
  padding: 0.25rem 0.5rem;
  font-size: 0.875rem;
  line-height: 0.75rem;
  text-transform: capitalize;
  outline: none;
  white-space: nowrap;
  color: #FFFFFF;
  background-color: #555555;
}

.emotion-4.focus-visible,
.emotion-4:hover {
  background-color: #999999;
}

.emotion-27 {
  background: none;
  border: none;
  width: auto;
  margin: 0;
  padding: 0;
  text-align: inherit;
  font: inherit;
  cursor: pointer;
  -webkit-text-decoration: none;
  text-decoration: none;
  color: #FFFFFF;
  font-size: 0.875rem;
}

<div
    class="emotion-0"
    data-testid="tag-link-group"
  >
    <h2
      class="emotion-1"
      data-testid="tag-link-group-heading"
    >
      tag_link_group.related_categories_heading
    </h2>
    <ul
      class="emotion-2"
    >
      <li
        class="emotion-3"
      >
        <a
          aria-label="Shorts"
          class="tag-link-group__text emotion-4"
          data-testid="tag-link"
          href="/browse/category.do?cid=1025764&nvt=Left%20Nav&nvt=GapMaternity&nvt=Categories&nvt=Shorts"
        >
          Shorts
        </a>
      </li>
      <li
        class="emotion-3"
      >
        <a
          aria-label="Shirts & Tops"
          class="tag-link-group__text emotion-4"
          data-testid="tag-link"
          href="/browse/category.do?cid=1014424&nvt=Left%20Nav&nvt=GapMaternity&nvt=Categories&nvt=Shirts%20%26%20Tops"
        >
          Shirts & Tops
        </a>
      </li>
      <li
        class="emotion-3"
      >
        <a
          aria-label="T-Shirts "
          class="tag-link-group__text emotion-4"
          data-testid="tag-link"
          href="/browse/category.do?cid=6049&nvt=Left%20Nav&nvt=GapMaternity&nvt=Categories&nvt=T-Shirts%20"
        >
          T-Shirts 
        </a>
      </li>
      <li
        class="emotion-3"
      >
        <a
          aria-label="Sweaters & Sweatshirts"
          class="tag-link-group__text emotion-4"
          data-testid="tag-link"
          href="/browse/category.do?cid=1019160&nvt=Left%20Nav&nvt=GapMaternity&nvt=Categories&nvt=Sweaters%20%26%20Sweatshirts"
        >
          Sweaters & Sweatshirts
        </a>
      </li>
      <li
        class="emotion-3"
      >
        <a
          aria-label="Swim"
          class="tag-link-group__text emotion-4"
          data-testid="tag-link"
          href="/browse/category.do?cid=345345&nvt=Left%20Nav&nvt=GapMaternity&nvt=Categories&nvt=Swim"
        >
          Swim
        </a>
      </li>
      <li
        class="emotion-3"
      >
        <a
          aria-label="Toddler Girl"
          class="tag-link-group__text emotion-4"
          data-testid="tag-link"
          href="/browse/category.do?cid=345345&nvt=Left%20Nav&nvt=GapMaternity&nvt=Categories&nvt=toddler"
        >
          Toddler Girl
        </a>
      </li>
      <li
        class="emotion-3"
      >
        <a
          aria-label="Toddler Boys"
          class="tag-link-group__text emotion-4"
          data-testid="tag-link"
          href="/browse/category.do?cid=345345&nvt=Left%20Nav&nvt=GapMaternity&nvt=Categories&nvt=toddler"
        >
          Toddler Boys
        </a>
      </li>
      <li
        class="emotion-3"
      >
        <a
          aria-label="Sports"
          class="tag-link-group__text emotion-4"
          data-testid="tag-link"
          href="/browse/category.do?cid=345345&nvt=Left%20Nav&nvt=GapMaternity&nvt=Categories&nvt=Sports"
        >
          Sports
        </a>
      </li>
      <li
        class="emotion-3"
      >
        <a
          aria-label="Socks & Tights"
          class="tag-link-group__text emotion-4"
          data-testid="tag-link"
          href="/browse/category.do?cid=345345&nvt=Left%20Nav&nvt=GapMaternity&nvt=Categories&nvt=Socks"
        >
          Socks & Tights
        </a>
      </li>
      <li
        class="emotion-3"
      >
        <a
          aria-label="Jackets"
          class="tag-link-group__text emotion-4"
          data-testid="tag-link"
          href="/browse/category.do?cid=345345&nvt=Left%20Nav&nvt=GapMaternity&nvt=Categories&nvt=Jackets"
        >
          Jackets
        </a>
      </li>
      <li
        class="emotion-3"
      >
        <a
          aria-label="Dresses & Skirts"
          class="tag-link-group__text emotion-4"
          data-testid="tag-link"
          href="/browse/category.do?cid=345345&nvt=Left%20Nav&nvt=GapMaternity&nvt=Categories&nvt=Skirts"
        >
          Dresses & Skirts
        </a>
      </li>
      <li
        class="emotion-3"
      >
        <a
          aria-label="Gap Factory"
          class="tag-link-group__text emotion-4"
          data-testid="tag-link"
          href="https://www.gapfactory.com/"
        >
          Gap Factory
        </a>
      </li>
    </ul>
    <div
      data-testid="tag-link-group-footer"
    >
      <button
        class="emotion-27"
        type="button"
      >
        tag_link_group.see_more_related_categories_text
      </button>
    </div>
  </div>
</DocumentFragment>
`;

exports[`<TagLinkGroup /> for inverse styles renders default state correctly Gap in crossBrand 1`] = `
<DocumentFragment>
  .emotion-1 {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 1.067rem;
  font-size: 1rem;
  color: #FFFFFF;
}

.emotion-2 {
  width: 100%;
  display: block;
  margin-top: 1rem;
  overflow: hidden;
  height: 1.75rem;
}

.emotion-3 {
  display: inline-block;
  margin-right: 0.5rem;
  margin-bottom: 0.75rem;
}

.emotion-4 {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  display: inline-block;
  border-radius: 1rem;
  padding: 0.25rem 0.5rem;
  font-size: 0.875rem;
  line-height: 0.75rem;
  text-transform: capitalize;
  outline: none;
  white-space: nowrap;
  color: #FFFFFF;
  background-color: #555555;
}

.emotion-4.focus-visible,
.emotion-4:hover {
  background-color: #999999;
}

.emotion-27 {
  background: none;
  border: none;
  width: auto;
  margin: 0;
  padding: 0;
  text-align: inherit;
  font: inherit;
  cursor: pointer;
  -webkit-text-decoration: none;
  text-decoration: none;
  color: #FFFFFF;
  font-size: 0.875rem;
}

<div
    class="emotion-0"
    data-testid="tag-link-group"
  >
    <h2
      class="emotion-1"
      data-testid="tag-link-group-heading"
    >
      tag_link_group.related_categories_heading
    </h2>
    <ul
      class="emotion-2"
    >
      <li
        class="emotion-3"
      >
        <a
          aria-label="Shorts"
          class="tag-link-group__text emotion-4"
          data-testid="tag-link"
          href="/browse/category.do?cid=1025764&nvt=Left%20Nav&nvt=GapMaternity&nvt=Categories&nvt=Shorts"
        >
          Shorts
        </a>
      </li>
      <li
        class="emotion-3"
      >
        <a
          aria-label="Shirts & Tops"
          class="tag-link-group__text emotion-4"
          data-testid="tag-link"
          href="/browse/category.do?cid=1014424&nvt=Left%20Nav&nvt=GapMaternity&nvt=Categories&nvt=Shirts%20%26%20Tops"
        >
          Shirts & Tops
        </a>
      </li>
      <li
        class="emotion-3"
      >
        <a
          aria-label="T-Shirts "
          class="tag-link-group__text emotion-4"
          data-testid="tag-link"
          href="/browse/category.do?cid=6049&nvt=Left%20Nav&nvt=GapMaternity&nvt=Categories&nvt=T-Shirts%20"
        >
          T-Shirts 
        </a>
      </li>
      <li
        class="emotion-3"
      >
        <a
          aria-label="Sweaters & Sweatshirts"
          class="tag-link-group__text emotion-4"
          data-testid="tag-link"
          href="/browse/category.do?cid=1019160&nvt=Left%20Nav&nvt=GapMaternity&nvt=Categories&nvt=Sweaters%20%26%20Sweatshirts"
        >
          Sweaters & Sweatshirts
        </a>
      </li>
      <li
        class="emotion-3"
      >
        <a
          aria-label="Swim"
          class="tag-link-group__text emotion-4"
          data-testid="tag-link"
          href="/browse/category.do?cid=345345&nvt=Left%20Nav&nvt=GapMaternity&nvt=Categories&nvt=Swim"
        >
          Swim
        </a>
      </li>
      <li
        class="emotion-3"
      >
        <a
          aria-label="Toddler Girl"
          class="tag-link-group__text emotion-4"
          data-testid="tag-link"
          href="/browse/category.do?cid=345345&nvt=Left%20Nav&nvt=GapMaternity&nvt=Categories&nvt=toddler"
        >
          Toddler Girl
        </a>
      </li>
      <li
        class="emotion-3"
      >
        <a
          aria-label="Toddler Boys"
          class="tag-link-group__text emotion-4"
          data-testid="tag-link"
          href="/browse/category.do?cid=345345&nvt=Left%20Nav&nvt=GapMaternity&nvt=Categories&nvt=toddler"
        >
          Toddler Boys
        </a>
      </li>
      <li
        class="emotion-3"
      >
        <a
          aria-label="Sports"
          class="tag-link-group__text emotion-4"
          data-testid="tag-link"
          href="/browse/category.do?cid=345345&nvt=Left%20Nav&nvt=GapMaternity&nvt=Categories&nvt=Sports"
        >
          Sports
        </a>
      </li>
      <li
        class="emotion-3"
      >
        <a
          aria-label="Socks & Tights"
          class="tag-link-group__text emotion-4"
          data-testid="tag-link"
          href="/browse/category.do?cid=345345&nvt=Left%20Nav&nvt=GapMaternity&nvt=Categories&nvt=Socks"
        >
          Socks & Tights
        </a>
      </li>
      <li
        class="emotion-3"
      >
        <a
          aria-label="Jackets"
          class="tag-link-group__text emotion-4"
          data-testid="tag-link"
          href="/browse/category.do?cid=345345&nvt=Left%20Nav&nvt=GapMaternity&nvt=Categories&nvt=Jackets"
        >
          Jackets
        </a>
      </li>
      <li
        class="emotion-3"
      >
        <a
          aria-label="Dresses & Skirts"
          class="tag-link-group__text emotion-4"
          data-testid="tag-link"
          href="/browse/category.do?cid=345345&nvt=Left%20Nav&nvt=GapMaternity&nvt=Categories&nvt=Skirts"
        >
          Dresses & Skirts
        </a>
      </li>
      <li
        class="emotion-3"
      >
        <a
          aria-label="Gap Factory"
          class="tag-link-group__text emotion-4"
          data-testid="tag-link"
          href="https://www.gapfactory.com/"
        >
          Gap Factory
        </a>
      </li>
    </ul>
    <div
      data-testid="tag-link-group-footer"
    >
      <button
        class="emotion-27"
        type="button"
      >
        tag_link_group.see_more_related_categories_text
      </button>
    </div>
  </div>
</DocumentFragment>
`;

exports[`<TagLinkGroup /> for inverse styles renders default state correctly GapFactoryStore 1`] = `
<DocumentFragment>
  .emotion-1 {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 1.067rem;
  font-size: 1rem;
  color: #FFFFFF;
}

.emotion-2 {
  width: 100%;
  display: block;
  margin-top: 1rem;
  overflow: hidden;
  height: 1.75rem;
}

.emotion-3 {
  display: inline-block;
  margin-right: 0.5rem;
  margin-bottom: 0.75rem;
}

.emotion-4 {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  display: inline-block;
  border-radius: 1rem;
  padding: 0.25rem 0.5rem;
  font-size: 0.875rem;
  line-height: 0.75rem;
  text-transform: capitalize;
  outline: none;
  white-space: nowrap;
  color: #FFFFFF;
  background-color: #555555;
}

.emotion-4.focus-visible,
.emotion-4:hover {
  background-color: #999999;
}

.emotion-27 {
  background: none;
  border: none;
  width: auto;
  margin: 0;
  padding: 0;
  text-align: inherit;
  font: inherit;
  cursor: pointer;
  -webkit-text-decoration: none;
  text-decoration: none;
  color: #FFFFFF;
  font-size: 0.875rem;
}

<div
    class="emotion-0"
    data-testid="tag-link-group"
  >
    <h2
      class="emotion-1"
      data-testid="tag-link-group-heading"
    >
      tag_link_group.related_categories_heading
    </h2>
    <ul
      class="emotion-2"
    >
      <li
        class="emotion-3"
      >
        <a
          aria-label="Shorts"
          class="tag-link-group__text emotion-4"
          data-testid="tag-link"
          href="/browse/category.do?cid=1025764&nvt=Left%20Nav&nvt=GapMaternity&nvt=Categories&nvt=Shorts"
        >
          Shorts
        </a>
      </li>
      <li
        class="emotion-3"
      >
        <a
          aria-label="Shirts & Tops"
          class="tag-link-group__text emotion-4"
          data-testid="tag-link"
          href="/browse/category.do?cid=1014424&nvt=Left%20Nav&nvt=GapMaternity&nvt=Categories&nvt=Shirts%20%26%20Tops"
        >
          Shirts & Tops
        </a>
      </li>
      <li
        class="emotion-3"
      >
        <a
          aria-label="T-Shirts "
          class="tag-link-group__text emotion-4"
          data-testid="tag-link"
          href="/browse/category.do?cid=6049&nvt=Left%20Nav&nvt=GapMaternity&nvt=Categories&nvt=T-Shirts%20"
        >
          T-Shirts 
        </a>
      </li>
      <li
        class="emotion-3"
      >
        <a
          aria-label="Sweaters & Sweatshirts"
          class="tag-link-group__text emotion-4"
          data-testid="tag-link"
          href="/browse/category.do?cid=1019160&nvt=Left%20Nav&nvt=GapMaternity&nvt=Categories&nvt=Sweaters%20%26%20Sweatshirts"
        >
          Sweaters & Sweatshirts
        </a>
      </li>
      <li
        class="emotion-3"
      >
        <a
          aria-label="Swim"
          class="tag-link-group__text emotion-4"
          data-testid="tag-link"
          href="/browse/category.do?cid=345345&nvt=Left%20Nav&nvt=GapMaternity&nvt=Categories&nvt=Swim"
        >
          Swim
        </a>
      </li>
      <li
        class="emotion-3"
      >
        <a
          aria-label="Toddler Girl"
          class="tag-link-group__text emotion-4"
          data-testid="tag-link"
          href="/browse/category.do?cid=345345&nvt=Left%20Nav&nvt=GapMaternity&nvt=Categories&nvt=toddler"
        >
          Toddler Girl
        </a>
      </li>
      <li
        class="emotion-3"
      >
        <a
          aria-label="Toddler Boys"
          class="tag-link-group__text emotion-4"
          data-testid="tag-link"
          href="/browse/category.do?cid=345345&nvt=Left%20Nav&nvt=GapMaternity&nvt=Categories&nvt=toddler"
        >
          Toddler Boys
        </a>
      </li>
      <li
        class="emotion-3"
      >
        <a
          aria-label="Sports"
          class="tag-link-group__text emotion-4"
          data-testid="tag-link"
          href="/browse/category.do?cid=345345&nvt=Left%20Nav&nvt=GapMaternity&nvt=Categories&nvt=Sports"
        >
          Sports
        </a>
      </li>
      <li
        class="emotion-3"
      >
        <a
          aria-label="Socks & Tights"
          class="tag-link-group__text emotion-4"
          data-testid="tag-link"
          href="/browse/category.do?cid=345345&nvt=Left%20Nav&nvt=GapMaternity&nvt=Categories&nvt=Socks"
        >
          Socks & Tights
        </a>
      </li>
      <li
        class="emotion-3"
      >
        <a
          aria-label="Jackets"
          class="tag-link-group__text emotion-4"
          data-testid="tag-link"
          href="/browse/category.do?cid=345345&nvt=Left%20Nav&nvt=GapMaternity&nvt=Categories&nvt=Jackets"
        >
          Jackets
        </a>
      </li>
      <li
        class="emotion-3"
      >
        <a
          aria-label="Dresses & Skirts"
          class="tag-link-group__text emotion-4"
          data-testid="tag-link"
          href="/browse/category.do?cid=345345&nvt=Left%20Nav&nvt=GapMaternity&nvt=Categories&nvt=Skirts"
        >
          Dresses & Skirts
        </a>
      </li>
      <li
        class="emotion-3"
      >
        <a
          aria-label="Gap Factory"
          class="tag-link-group__text emotion-4"
          data-testid="tag-link"
          href="https://www.gapfactory.com/"
        >
          Gap Factory
        </a>
      </li>
    </ul>
    <div
      data-testid="tag-link-group-footer"
    >
      <button
        class="emotion-27"
        type="button"
      >
        tag_link_group.see_more_related_categories_text
      </button>
    </div>
  </div>
</DocumentFragment>
`;

exports[`<TagLinkGroup /> for inverse styles renders default state correctly GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-1 {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 1.067rem;
  font-size: 1rem;
  color: #FFFFFF;
}

.emotion-2 {
  width: 100%;
  display: block;
  margin-top: 1rem;
  overflow: hidden;
  height: 1.75rem;
}

.emotion-3 {
  display: inline-block;
  margin-right: 0.5rem;
  margin-bottom: 0.75rem;
}

.emotion-4 {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  display: inline-block;
  border-radius: 1rem;
  padding: 0.25rem 0.5rem;
  font-size: 0.875rem;
  line-height: 0.75rem;
  text-transform: capitalize;
  outline: none;
  white-space: nowrap;
  color: #FFFFFF;
  background-color: #555555;
}

.emotion-4.focus-visible,
.emotion-4:hover {
  background-color: #999999;
}

.emotion-27 {
  background: none;
  border: none;
  width: auto;
  margin: 0;
  padding: 0;
  text-align: inherit;
  font: inherit;
  cursor: pointer;
  -webkit-text-decoration: none;
  text-decoration: none;
  color: #FFFFFF;
  font-size: 0.875rem;
}

<div
    class="emotion-0"
    data-testid="tag-link-group"
  >
    <h2
      class="emotion-1"
      data-testid="tag-link-group-heading"
    >
      tag_link_group.related_categories_heading
    </h2>
    <ul
      class="emotion-2"
    >
      <li
        class="emotion-3"
      >
        <a
          aria-label="Shorts"
          class="tag-link-group__text emotion-4"
          data-testid="tag-link"
          href="/browse/category.do?cid=1025764&nvt=Left%20Nav&nvt=GapMaternity&nvt=Categories&nvt=Shorts"
        >
          Shorts
        </a>
      </li>
      <li
        class="emotion-3"
      >
        <a
          aria-label="Shirts & Tops"
          class="tag-link-group__text emotion-4"
          data-testid="tag-link"
          href="/browse/category.do?cid=1014424&nvt=Left%20Nav&nvt=GapMaternity&nvt=Categories&nvt=Shirts%20%26%20Tops"
        >
          Shirts & Tops
        </a>
      </li>
      <li
        class="emotion-3"
      >
        <a
          aria-label="T-Shirts "
          class="tag-link-group__text emotion-4"
          data-testid="tag-link"
          href="/browse/category.do?cid=6049&nvt=Left%20Nav&nvt=GapMaternity&nvt=Categories&nvt=T-Shirts%20"
        >
          T-Shirts 
        </a>
      </li>
      <li
        class="emotion-3"
      >
        <a
          aria-label="Sweaters & Sweatshirts"
          class="tag-link-group__text emotion-4"
          data-testid="tag-link"
          href="/browse/category.do?cid=1019160&nvt=Left%20Nav&nvt=GapMaternity&nvt=Categories&nvt=Sweaters%20%26%20Sweatshirts"
        >
          Sweaters & Sweatshirts
        </a>
      </li>
      <li
        class="emotion-3"
      >
        <a
          aria-label="Swim"
          class="tag-link-group__text emotion-4"
          data-testid="tag-link"
          href="/browse/category.do?cid=345345&nvt=Left%20Nav&nvt=GapMaternity&nvt=Categories&nvt=Swim"
        >
          Swim
        </a>
      </li>
      <li
        class="emotion-3"
      >
        <a
          aria-label="Toddler Girl"
          class="tag-link-group__text emotion-4"
          data-testid="tag-link"
          href="/browse/category.do?cid=345345&nvt=Left%20Nav&nvt=GapMaternity&nvt=Categories&nvt=toddler"
        >
          Toddler Girl
        </a>
      </li>
      <li
        class="emotion-3"
      >
        <a
          aria-label="Toddler Boys"
          class="tag-link-group__text emotion-4"
          data-testid="tag-link"
          href="/browse/category.do?cid=345345&nvt=Left%20Nav&nvt=GapMaternity&nvt=Categories&nvt=toddler"
        >
          Toddler Boys
        </a>
      </li>
      <li
        class="emotion-3"
      >
        <a
          aria-label="Sports"
          class="tag-link-group__text emotion-4"
          data-testid="tag-link"
          href="/browse/category.do?cid=345345&nvt=Left%20Nav&nvt=GapMaternity&nvt=Categories&nvt=Sports"
        >
          Sports
        </a>
      </li>
      <li
        class="emotion-3"
      >
        <a
          aria-label="Socks & Tights"
          class="tag-link-group__text emotion-4"
          data-testid="tag-link"
          href="/browse/category.do?cid=345345&nvt=Left%20Nav&nvt=GapMaternity&nvt=Categories&nvt=Socks"
        >
          Socks & Tights
        </a>
      </li>
      <li
        class="emotion-3"
      >
        <a
          aria-label="Jackets"
          class="tag-link-group__text emotion-4"
          data-testid="tag-link"
          href="/browse/category.do?cid=345345&nvt=Left%20Nav&nvt=GapMaternity&nvt=Categories&nvt=Jackets"
        >
          Jackets
        </a>
      </li>
      <li
        class="emotion-3"
      >
        <a
          aria-label="Dresses & Skirts"
          class="tag-link-group__text emotion-4"
          data-testid="tag-link"
          href="/browse/category.do?cid=345345&nvt=Left%20Nav&nvt=GapMaternity&nvt=Categories&nvt=Skirts"
        >
          Dresses & Skirts
        </a>
      </li>
      <li
        class="emotion-3"
      >
        <a
          aria-label="Gap Factory"
          class="tag-link-group__text emotion-4"
          data-testid="tag-link"
          href="https://www.gapfactory.com/"
        >
          Gap Factory
        </a>
      </li>
    </ul>
    <div
      data-testid="tag-link-group-footer"
    >
      <button
        class="emotion-27"
        type="button"
      >
        tag_link_group.see_more_related_categories_text
      </button>
    </div>
  </div>
</DocumentFragment>
`;

exports[`<TagLinkGroup /> for inverse styles renders default state correctly OldNavy 1`] = `
<DocumentFragment>
  .emotion-1 {
  font-family: 'Gotham','Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 700;
  font-size: 1.067rem;
  font-size: 1rem;
  color: #FFFFFF;
}

.emotion-2 {
  width: 100%;
  display: block;
  margin-top: 1rem;
  overflow: hidden;
  height: 1.75rem;
}

.emotion-3 {
  display: inline-block;
  margin-right: 0.5rem;
  margin-bottom: 0.75rem;
}

.emotion-4 {
  font-family: 'Gotham','Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  display: inline-block;
  border-radius: 1rem;
  padding: 0.25rem 0.5rem;
  font-size: 0.875rem;
  line-height: 0.75rem;
  text-transform: capitalize;
  outline: none;
  white-space: nowrap;
  color: #FFFFFF;
  background-color: #555555;
}

.emotion-4.focus-visible,
.emotion-4:hover {
  background-color: #999999;
}

.emotion-27 {
  background: none;
  border: none;
  width: auto;
  margin: 0;
  padding: 0;
  text-align: inherit;
  font: inherit;
  cursor: pointer;
  -webkit-text-decoration: none;
  text-decoration: none;
  color: #FFFFFF;
  font-size: 0.875rem;
}

<div
    class="emotion-0"
    data-testid="tag-link-group"
  >
    <h2
      class="emotion-1"
      data-testid="tag-link-group-heading"
    >
      tag_link_group.related_categories_heading
    </h2>
    <ul
      class="emotion-2"
    >
      <li
        class="emotion-3"
      >
        <a
          aria-label="Shorts"
          class="tag-link-group__text emotion-4"
          data-testid="tag-link"
          href="/browse/category.do?cid=1025764&nvt=Left%20Nav&nvt=GapMaternity&nvt=Categories&nvt=Shorts"
        >
          Shorts
        </a>
      </li>
      <li
        class="emotion-3"
      >
        <a
          aria-label="Shirts & Tops"
          class="tag-link-group__text emotion-4"
          data-testid="tag-link"
          href="/browse/category.do?cid=1014424&nvt=Left%20Nav&nvt=GapMaternity&nvt=Categories&nvt=Shirts%20%26%20Tops"
        >
          Shirts & Tops
        </a>
      </li>
      <li
        class="emotion-3"
      >
        <a
          aria-label="T-Shirts "
          class="tag-link-group__text emotion-4"
          data-testid="tag-link"
          href="/browse/category.do?cid=6049&nvt=Left%20Nav&nvt=GapMaternity&nvt=Categories&nvt=T-Shirts%20"
        >
          T-Shirts 
        </a>
      </li>
      <li
        class="emotion-3"
      >
        <a
          aria-label="Sweaters & Sweatshirts"
          class="tag-link-group__text emotion-4"
          data-testid="tag-link"
          href="/browse/category.do?cid=1019160&nvt=Left%20Nav&nvt=GapMaternity&nvt=Categories&nvt=Sweaters%20%26%20Sweatshirts"
        >
          Sweaters & Sweatshirts
        </a>
      </li>
      <li
        class="emotion-3"
      >
        <a
          aria-label="Swim"
          class="tag-link-group__text emotion-4"
          data-testid="tag-link"
          href="/browse/category.do?cid=345345&nvt=Left%20Nav&nvt=GapMaternity&nvt=Categories&nvt=Swim"
        >
          Swim
        </a>
      </li>
      <li
        class="emotion-3"
      >
        <a
          aria-label="Toddler Girl"
          class="tag-link-group__text emotion-4"
          data-testid="tag-link"
          href="/browse/category.do?cid=345345&nvt=Left%20Nav&nvt=GapMaternity&nvt=Categories&nvt=toddler"
        >
          Toddler Girl
        </a>
      </li>
      <li
        class="emotion-3"
      >
        <a
          aria-label="Toddler Boys"
          class="tag-link-group__text emotion-4"
          data-testid="tag-link"
          href="/browse/category.do?cid=345345&nvt=Left%20Nav&nvt=GapMaternity&nvt=Categories&nvt=toddler"
        >
          Toddler Boys
        </a>
      </li>
      <li
        class="emotion-3"
      >
        <a
          aria-label="Sports"
          class="tag-link-group__text emotion-4"
          data-testid="tag-link"
          href="/browse/category.do?cid=345345&nvt=Left%20Nav&nvt=GapMaternity&nvt=Categories&nvt=Sports"
        >
          Sports
        </a>
      </li>
      <li
        class="emotion-3"
      >
        <a
          aria-label="Socks & Tights"
          class="tag-link-group__text emotion-4"
          data-testid="tag-link"
          href="/browse/category.do?cid=345345&nvt=Left%20Nav&nvt=GapMaternity&nvt=Categories&nvt=Socks"
        >
          Socks & Tights
        </a>
      </li>
      <li
        class="emotion-3"
      >
        <a
          aria-label="Jackets"
          class="tag-link-group__text emotion-4"
          data-testid="tag-link"
          href="/browse/category.do?cid=345345&nvt=Left%20Nav&nvt=GapMaternity&nvt=Categories&nvt=Jackets"
        >
          Jackets
        </a>
      </li>
      <li
        class="emotion-3"
      >
        <a
          aria-label="Dresses & Skirts"
          class="tag-link-group__text emotion-4"
          data-testid="tag-link"
          href="/browse/category.do?cid=345345&nvt=Left%20Nav&nvt=GapMaternity&nvt=Categories&nvt=Skirts"
        >
          Dresses & Skirts
        </a>
      </li>
      <li
        class="emotion-3"
      >
        <a
          aria-label="Gap Factory"
          class="tag-link-group__text emotion-4"
          data-testid="tag-link"
          href="https://www.gapfactory.com/"
        >
          Gap Factory
        </a>
      </li>
    </ul>
    <div
      data-testid="tag-link-group-footer"
    >
      <button
        class="emotion-27"
        type="button"
      >
        tag_link_group.see_more_related_categories_text
      </button>
    </div>
  </div>
</DocumentFragment>
`;

exports[`<TagLinkGroup /> for inverse styles renders default state correctly OldNavy in crossBrand 1`] = `
<DocumentFragment>
  .emotion-1 {
  font-family: 'Gotham','Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 700;
  font-size: 1.067rem;
  font-size: 1rem;
  color: #FFFFFF;
}

.emotion-2 {
  width: 100%;
  display: block;
  margin-top: 1rem;
  overflow: hidden;
  height: 1.75rem;
}

.emotion-3 {
  display: inline-block;
  margin-right: 0.5rem;
  margin-bottom: 0.75rem;
}

.emotion-4 {
  font-family: 'Gotham','Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  display: inline-block;
  border-radius: 1rem;
  padding: 0.25rem 0.5rem;
  font-size: 0.875rem;
  line-height: 0.75rem;
  text-transform: capitalize;
  outline: none;
  white-space: nowrap;
  color: #FFFFFF;
  background-color: #555555;
}

.emotion-4.focus-visible,
.emotion-4:hover {
  background-color: #999999;
}

.emotion-27 {
  background: none;
  border: none;
  width: auto;
  margin: 0;
  padding: 0;
  text-align: inherit;
  font: inherit;
  cursor: pointer;
  -webkit-text-decoration: none;
  text-decoration: none;
  color: #FFFFFF;
  font-size: 0.875rem;
}

<div
    class="emotion-0"
    data-testid="tag-link-group"
  >
    <h2
      class="emotion-1"
      data-testid="tag-link-group-heading"
    >
      tag_link_group.related_categories_heading
    </h2>
    <ul
      class="emotion-2"
    >
      <li
        class="emotion-3"
      >
        <a
          aria-label="Shorts"
          class="tag-link-group__text emotion-4"
          data-testid="tag-link"
          href="/browse/category.do?cid=1025764&nvt=Left%20Nav&nvt=GapMaternity&nvt=Categories&nvt=Shorts"
        >
          Shorts
        </a>
      </li>
      <li
        class="emotion-3"
      >
        <a
          aria-label="Shirts & Tops"
          class="tag-link-group__text emotion-4"
          data-testid="tag-link"
          href="/browse/category.do?cid=1014424&nvt=Left%20Nav&nvt=GapMaternity&nvt=Categories&nvt=Shirts%20%26%20Tops"
        >
          Shirts & Tops
        </a>
      </li>
      <li
        class="emotion-3"
      >
        <a
          aria-label="T-Shirts "
          class="tag-link-group__text emotion-4"
          data-testid="tag-link"
          href="/browse/category.do?cid=6049&nvt=Left%20Nav&nvt=GapMaternity&nvt=Categories&nvt=T-Shirts%20"
        >
          T-Shirts 
        </a>
      </li>
      <li
        class="emotion-3"
      >
        <a
          aria-label="Sweaters & Sweatshirts"
          class="tag-link-group__text emotion-4"
          data-testid="tag-link"
          href="/browse/category.do?cid=1019160&nvt=Left%20Nav&nvt=GapMaternity&nvt=Categories&nvt=Sweaters%20%26%20Sweatshirts"
        >
          Sweaters & Sweatshirts
        </a>
      </li>
      <li
        class="emotion-3"
      >
        <a
          aria-label="Swim"
          class="tag-link-group__text emotion-4"
          data-testid="tag-link"
          href="/browse/category.do?cid=345345&nvt=Left%20Nav&nvt=GapMaternity&nvt=Categories&nvt=Swim"
        >
          Swim
        </a>
      </li>
      <li
        class="emotion-3"
      >
        <a
          aria-label="Toddler Girl"
          class="tag-link-group__text emotion-4"
          data-testid="tag-link"
          href="/browse/category.do?cid=345345&nvt=Left%20Nav&nvt=GapMaternity&nvt=Categories&nvt=toddler"
        >
          Toddler Girl
        </a>
      </li>
      <li
        class="emotion-3"
      >
        <a
          aria-label="Toddler Boys"
          class="tag-link-group__text emotion-4"
          data-testid="tag-link"
          href="/browse/category.do?cid=345345&nvt=Left%20Nav&nvt=GapMaternity&nvt=Categories&nvt=toddler"
        >
          Toddler Boys
        </a>
      </li>
      <li
        class="emotion-3"
      >
        <a
          aria-label="Sports"
          class="tag-link-group__text emotion-4"
          data-testid="tag-link"
          href="/browse/category.do?cid=345345&nvt=Left%20Nav&nvt=GapMaternity&nvt=Categories&nvt=Sports"
        >
          Sports
        </a>
      </li>
      <li
        class="emotion-3"
      >
        <a
          aria-label="Socks & Tights"
          class="tag-link-group__text emotion-4"
          data-testid="tag-link"
          href="/browse/category.do?cid=345345&nvt=Left%20Nav&nvt=GapMaternity&nvt=Categories&nvt=Socks"
        >
          Socks & Tights
        </a>
      </li>
      <li
        class="emotion-3"
      >
        <a
          aria-label="Jackets"
          class="tag-link-group__text emotion-4"
          data-testid="tag-link"
          href="/browse/category.do?cid=345345&nvt=Left%20Nav&nvt=GapMaternity&nvt=Categories&nvt=Jackets"
        >
          Jackets
        </a>
      </li>
      <li
        class="emotion-3"
      >
        <a
          aria-label="Dresses & Skirts"
          class="tag-link-group__text emotion-4"
          data-testid="tag-link"
          href="/browse/category.do?cid=345345&nvt=Left%20Nav&nvt=GapMaternity&nvt=Categories&nvt=Skirts"
        >
          Dresses & Skirts
        </a>
      </li>
      <li
        class="emotion-3"
      >
        <a
          aria-label="Gap Factory"
          class="tag-link-group__text emotion-4"
          data-testid="tag-link"
          href="https://www.gapfactory.com/"
        >
          Gap Factory
        </a>
      </li>
    </ul>
    <div
      data-testid="tag-link-group-footer"
    >
      <button
        class="emotion-27"
        type="button"
      >
        tag_link_group.see_more_related_categories_text
      </button>
    </div>
  </div>
</DocumentFragment>
`;
