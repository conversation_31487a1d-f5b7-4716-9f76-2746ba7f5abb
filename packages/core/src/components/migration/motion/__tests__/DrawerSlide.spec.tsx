import React, { useState } from 'react';
import { fireEvent, render, screen } from 'test-utils';
import { DrawerSlide } from '../DrawerSlide';
import { DrawerSlidePosition, DrawerSlideProps } from '../types';

const SlideComponent = ({ in: slideIn = false, position, slideCSS }: Partial<DrawerSlideProps>): JSX.Element => {
  const [isOpen, setIsOpen] = useState(slideIn);
  return (
    <>
      <button onClick={() => setIsOpen(!isOpen)} type='button'>
        trigger slide effect
      </button>
      <DrawerSlide in={isOpen} position={position} slideCSS={slideCSS}>
        {transitionStyles => <span className={`${transitionStyles.defaultStyles} ${transitionStyles.drawerStyles}`}>DrawerSlide Test</span>}
      </DrawerSlide>
    </>
  );
};

const getChild = (): HTMLElement => screen.getByText('DrawerSlide Test');
const queryChild = (): HTMLElement | null => screen.queryByText('DrawerSlide Test');
const triggerSlideTransition = (): void => {
  fireEvent.click(screen.getByText('trigger slide effect'));
};

describe('DrawerSlide', () => {
  it('doesnt render child `in` prop is `false`', () => {
    render(<SlideComponent in={false} />);
    expect(queryChild()).not.toBeInTheDocument();
  });

  it('renders child when `in` prop is `true`', () => {
    render(<SlideComponent in />);
    expect(getChild()).toBeInTheDocument();
  });

  describe('positions', () => {
    test('defaults to enter from left', () => {
      const { container } = render(<SlideComponent in position={DrawerSlidePosition.left} />);
      triggerSlideTransition();
      expect(container.getElementsByClassName('left-0').length).toBe(1);
    });
    test('renders enter from top', () => {
      const { container } = render(<SlideComponent position={DrawerSlidePosition.top} />);
      triggerSlideTransition();
      expect(container.getElementsByClassName('top-0').length).toBe(1);
    });
    test('renders enter from right', () => {
      const { container } = render(<SlideComponent position={DrawerSlidePosition.right} />);
      triggerSlideTransition();
      expect(container.getElementsByClassName('right-0').length).toBe(1);
    });
    test('renders enter from bottom', () => {
      const { container } = render(<SlideComponent position={DrawerSlidePosition.bottom} />);
      triggerSlideTransition();
      expect(container.getElementsByClassName('bottom-0').length).toBe(1);
    });
  });

  describe('transitions', () => {
    test('renders enter transition correctly', () => {
      const { container } = render(<SlideComponent in={false} />);
      triggerSlideTransition();

      expect(container.getElementsByClassName('duration-300 ease-in').length).toBe(1);
    });

    test('renders exit transition correctly', () => {
      const { container } = render(<SlideComponent in />);
      triggerSlideTransition();

      expect(container.getElementsByClassName('duration-150 ease-out').length).toBe(1);
    });
  });

  describe('custom css', () => {
    test('renders custom css that overwrites defaults', () => {
      const customCSS = 'h-[100px]';
      const { container } = render(<SlideComponent in slideCSS={customCSS} />);
      triggerSlideTransition();
      expect(container.getElementsByClassName('h-[100px]').length).toBe(1);
    });
  });
});
