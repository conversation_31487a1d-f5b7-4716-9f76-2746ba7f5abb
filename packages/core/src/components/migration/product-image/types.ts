import { Brand } from '@ecom-next/utils/server';

// TODO: This is a fix to include gp and gpfs in the Brand type and to be removed by checkout team in the future
export type ItemBrand = Brand | 'gp' | 'gpfs';
export type BrandKey = "AT" | "BRFS" | "BR" | "GAP" | "GAPFS" | "ON";


export type ProductImageProps = React.ComponentProps<'img'> & {
  /**
   * The alternative text for accessibility which should describe the content.
   */
  alt: string;
  /**
   Turn on or off aria label hidden
  */
  'aria-hidden'?: boolean;
  /**
   Specify which brand
  */
  itemBrand?: ItemBrand;
  /**
   * The alternative text of the hover image for accessibility which should describe the content.
   */

  hoverAlt?: string;
  /**
   * The address of the hover image.
   */
  hoverSrc?: string;
  /**
   * Function to be run when product image is clicked but before redirecting to product page
   */
  onClick?: React.MouseEventHandler<HTMLAnchorElement>;
  /**
   * The address of the product.
   */
  productUrl?: string;
  /**
   * The address of an image.
   */
  src: string;
  /**
   * When using HoverImageSwap, determines if should use default ratio to size image.
   */
  useRatio?: boolean;
};
export type LogoProps = {
  itemBrand: ItemBrand;
  overwriteBrand?: boolean;
};
