{"accountMenu": {"component-global-header-account-menu-sign-in-cta": "Sign in"}, "addToBag": {"component-add-to-bag-button-label": "Add to bag", "component-add-to-bag-out-of-stock": "Out of stock"}, "bold": {"font-weight-bold-string-test": "Regular"}, "borderRadius": {"component-add-to-bag-border-radius": "var(--border-radius-000)", "component-bopis-bopis-filter-border-radius": "var(--border-radius-000)", "component-bopis-border-radius": "var(--border-radius-000)", "component-button-border-radius": "var(--border-radius-000)", "component-chips-border-radius": "var(--border-radius-000)", "component-critical-button-border-radius": "var(--border-radius-000)", "component-drawer-mobile-border-radius": "24px", "component-selector-available-focused-border-radius": "var(--border-radius-000)", "component-selector-border-radius": "var(--border-radius-000)", "component-selector-swatch-swatch-border-radius": "var(--border-radius-000)", "global-border-radius-0": "var(--border-radius-000)", "global-border-radius-10": "var(--border-radius-125)", "global-border-radius-16": "var(--border-radius-200)", "global-border-radius-2": "var(--border-radius-025)", "global-border-radius-24": "var(--border-radius-300)", "global-border-radius-4": "var(--border-radius-050)", "global-border-radius-6": "var(--border-radius-075)", "global-border-radius-8": "var(--border-radius-100)", "global-border-radius-999": "var(--border-radius-9999)", "interactive-border-radius": "var(--border-radius-000)"}, "borderWidth": {"component-bopis-selected-border-width": "var(--border-width-20)", "component-bopis-unselected-border-width": "var(--border-width-10)", "component-button-border-width": "var(--border-width-10)", "component-button-primary-hover-border-width": "var(--border-width-0)", "component-button-secondary-default-border-width": "var(--border-width-10)", "component-button-secondary-hover-border-width": "var(--border-width-10)", "component-color-swatch-color-swatch-cross-out-border-width": "var(--border-width-10)", "component-critical-button-border-width": "var(--border-width-0)", "component-selector-available-focused-border-width": "var(--border-width-30)", "component-selector-border-width": "var(--border-width-20)", "component-selector-swatch-grid---border-width-(divider)--": "var(--border-width-0)", "component-selector-swatch-swatch-border-width": "var(--border-width-20)", "component-selector-swatch-swatch-focus-border-width": "var(--border-width-30)", "component-tab-border-width": "var(--border-width-0)", "component-tab-selected-border-width": "var(--border-width-10)", "global-border-width-0": "var(--border-width-0)", "global-border-width-05": "var(--border-width-05)", "global-border-width-1": "var(--border-width-10)", "global-border-width-2": "var(--border-width-20)", "global-border-width-3": "var(--border-width-30)", "global-border-width-4": "var(--border-width-40)", "global-border-width-5": "var(--border-width-50)", "global-border-width-6": "var(--border-width-60)", "interactive-border-width": "var(--border-width-10)"}, "button": {"component-button-text-decoration-line": "None", "interactive-button-type": "<PERSON><PERSON><PERSON>"}, "checkboxAndRadio": {"interactive-checkbox-and-radio-icon": "<PERSON><PERSON><PERSON>"}, "colors": {"color-border-black": "rgb(var(--color-black-and-white-black) / <alpha-value>)", "color-border-bold": "rgb(var(--color-black-and-white-black) / <alpha-value>)", "color-border-default": "rgb(var(--color-neutrals-600) / <alpha-value>)", "color-border-inactive": "rgb(var(--color-neutrals-300) / <alpha-value>)", "color-border-informational": "rgb(var(--color-blue-500) / <alpha-value>)", "color-border-inverse": "rgb(var(--color-black-and-white-white) / <alpha-value>)", "color-border-negative": "rgb(var(--color-red-500) / <alpha-value>)", "color-border-positive": "rgb(var(--color-green-500) / <alpha-value>)", "color-border-subtle": "rgb(var(--color-neutrals-500) / <alpha-value>)", "color-border-warning": "rgb(var(--color-orange-500) / <alpha-value>)", "color-brand-1": "rgb(var(--color-brand-1) / <alpha-value>)", "color-brand-2": "rgb(var(--color-brand-2) / <alpha-value>)", "color-brand-3": "rgb(var(--color-brand-3) / <alpha-value>)", "color-brand-4": "rgb(var(--color-brand-4) / <alpha-value>)", "color-brand-5": "rgb(var(--color-brand-5) / <alpha-value>)", "color-brand-6": "rgb(var(--color-placeholder) / <alpha-value>)", "color-fill-dark": "rgb(var(--color-black-and-white-black) / <alpha-value>)", "color-fill-inactive": "rgb(var(--color-neutrals-300) / <alpha-value>)", "color-fill-informational": "rgb(var(--color-blue-500) / <alpha-value>)", "color-fill-negative": "rgb(var(--color-red-500) / <alpha-value>)", "color-fill-subtle": "rgb(var(--color-neutrals-500) / <alpha-value>)", "color-fill-white": "rgb(var(--color-black-and-white-white) / <alpha-value>)", "color-font-default": "rgb(var(--color-black-and-white-black) / <alpha-value>)", "color-font-inactive": "rgb(var(--color-neutrals-300) / <alpha-value>)", "color-font-info-bold": "rgb(var(--color-black-and-white-black) / <alpha-value>)", "color-font-info-default": "rgb(var(--color-black-and-white-black) / <alpha-value>)", "color-font-inverse": "rgb(var(--color-black-and-white-white) / <alpha-value>)", "color-font-link-critical": "rgb(var(--color-black-and-white-black) / <alpha-value>)", "color-font-link-critical-hover": "rgb(var(--color-black-and-white-black) / <alpha-value>)", "color-font-link-default": "rgb(var(--color-black-and-white-black) / <alpha-value>)", "color-font-link-hover": "rgb(var(--color-black-and-white-black) / <alpha-value>)", "color-font-link-inactive": "rgb(var(--color-neutrals-300) / <alpha-value>)", "color-font-link-inverse": "rgb(var(--color-black-and-white-white) / <alpha-value>)", "color-font-link-visited": "rgb(var(--color-black-and-white-black) / <alpha-value>)", "color-font-negative-bold": "rgb(var(--color-red-700) / <alpha-value>)", "color-font-negative-default": "rgb(var(--color-red-500) / <alpha-value>)", "color-font-positive-bold": "rgb(var(--color-green-700) / <alpha-value>)", "color-font-positive-default": "rgb(var(--color-green-500) / <alpha-value>)", "color-font-sale-bold": "rgb(var(--color-red-700) / <alpha-value>)", "color-font-sale-default": "rgb(var(--color-red-500) / <alpha-value>)", "color-font-subtle": "rgb(var(--color-neutrals-700) / <alpha-value>)", "color-font-warning-bold": "rgb(var(--color-orange-700) / <alpha-value>)", "color-font-warning-default": "rgb(var(--color-orange-500) / <alpha-value>)", "color-icon-default": "rgb(var(--color-black-and-white-black) / <alpha-value>)", "color-icon-inactive": "rgb(var(--color-neutrals-300) / <alpha-value>)", "color-icon-info": "rgb(var(--color-black-and-white-black) / <alpha-value>)", "color-icon-inverse": "rgb(var(--color-black-and-white-white) / <alpha-value>)", "color-icon-negative": "rgb(var(--color-red-500) / <alpha-value>)", "color-icon-positive": "rgb(var(--color-green-500) / <alpha-value>)", "color-icon-warning": "rgb(var(--color-orange-cb-500) / <alpha-value>)", "color-overlay-black-background": "rgb(var(--color-black-and-white-black-30) / <alpha-value>)", "color-overlay-white-background": "rgb(var(--color-black-and-white-white-30) / <alpha-value>)", "color-page-background-clear": "rgb(var(--color-black-and-white-white) / <alpha-value>)", "color-page-background-hazy": "rgb(var(--color-neutrals-200) / <alpha-value>)", "color-surface-dark": "rgb(var(--color-black-and-white-black) / <alpha-value>)", "color-surface-default": "rgb(var(--color-black-and-white-white) / <alpha-value>)", "color-surface-hover": "rgb(var(--color-neutrals-300) / <alpha-value>)", "color-surface-inactive": "rgb(var(--color-neutrals-200) / <alpha-value>)", "color-surface-info": "rgb(var(--color-neutrals-200) / <alpha-value>)", "color-surface-negative": "rgb(var(--color-red-100) / <alpha-value>)", "color-surface-positive": "rgb(var(--color-green-100) / <alpha-value>)", "color-surface-secondary-hover": "rgb(var(--color-neutrals-700) / <alpha-value>)", "color-surface-subtle": "rgb(var(--color-neutrals-500) / <alpha-value>)", "color-surface-warning": "rgb(var(--color-orange-100) / <alpha-value>)", "component-accordion-font-color": "rgb(var(--color-black-and-white-black) / <alpha-value>)", "component-button-critical-caution-font-color": "rgb(var(--color-black-and-white-white) / <alpha-value>)", "component-button-critical-caution-hover-font-color": "rgb(var(--color-black-and-white-white) / <alpha-value>)", "component-button-critical-caution-hover-surface-color": "rgb(var(--color-neutrals-900) / <alpha-value>)", "component-button-critical-caution-surface-color": "rgb(var(--color-neutrals-700) / <alpha-value>)", "component-button-critical-default-font-color": "rgb(var(--color-black-and-white-white) / <alpha-value>)", "component-button-critical-default-icon-color": "rgb(var(--color-black-and-white-white) / <alpha-value>)", "component-button-critical-default-surface-color": "rgb(var(--color-brand-5) / <alpha-value>)", "component-button-critical-disabled-font-color": "rgb(var(--color-neutrals-600) / <alpha-value>)", "component-button-critical-disabled-surface-color": "rgb(var(--color-neutrals-400) / <alpha-value>)", "component-button-critical-hover-font-color": "rgb(var(--color-black-and-white-white) / <alpha-value>)", "component-button-critical-hover-surface-color": "rgb(var(--color-brand-3) / <alpha-value>)", "component-button-critical-pressed-font-color": "rgb(var(--color-black-and-white-white) / <alpha-value>)", "component-button-critical-pressed-surface-color": "rgb(var(--color-brand-5) / <alpha-value>)", "component-button-primary-default-font-color": "rgb(var(--color-black-and-white-white) / <alpha-value>)", "component-button-primary-default-icon-color": "rgb(var(--color-black-and-white-white) / <alpha-value>)", "component-button-primary-default-surface-color": "rgb(var(--color-black-and-white-black) / <alpha-value>)", "component-button-primary-disabled-font-color": "rgb(var(--color-neutrals-600) / <alpha-value>)", "component-button-primary-disabled-surface-color": "rgb(var(--color-neutrals-400) / <alpha-value>)", "component-button-primary-hover-font-color": "rgb(var(--color-black-and-white-white) / <alpha-value>)", "component-button-primary-hover-surface-color": "rgb(var(--color-neutrals-700) / <alpha-value>)", "component-button-primary-pressed-font-color": "rgb(var(--color-black-and-white-white) / <alpha-value>)", "component-button-primary-pressed-surface-color": "rgb(var(--color-black-and-white-black) / <alpha-value>)", "component-button-secondary-default-border-color": "rgb(var(--color-black-and-white-black) / <alpha-value>)", "component-button-secondary-default-font-color": "rgb(var(--color-black-and-white-black) / <alpha-value>)", "component-button-secondary-default-surface-color": "rgb(var(--color-black-and-white-white) / <alpha-value>)", "component-button-secondary-disabled-border-color": "rgb(var(--color-neutrals-500) / <alpha-value>)", "component-button-secondary-disabled-font-color": "rgb(var(--color-neutrals-600) / <alpha-value>)", "component-button-secondary-disabled-surface-color": "rgb(var(--color-black-and-white-white) / <alpha-value>)", "component-button-secondary-hover-border-color": "rgb(var(--color-black-and-white-black) / <alpha-value>)", "component-button-secondary-hover-font-color": "rgb(var(--color-black-and-white-white) / <alpha-value>)", "component-button-secondary-hover-surface-color": "rgb(var(--color-black-and-white-black) / <alpha-value>)", "component-button-secondary-pressed-border-color": "rgb(var(--color-black-and-white-black) / <alpha-value>)", "component-button-secondary-pressed-font-color": "rgb(var(--color-black-and-white-black) / <alpha-value>)", "component-button-secondary-pressed-surface-color": "rgb(var(--color-black-and-white-white) / <alpha-value>)", "component-chips-input-chips-active": "rgb(var(--color-neutrals-600) / <alpha-value>)", "component-chips-input-chips-default": "rgb(var(--color-neutrals-300) / <alpha-value>)", "component-chips-input-chips-focused": "rgb(var(--color-neutrals-500) / <alpha-value>)", "component-drawer-surface": "rgb(var(--color-black-and-white-white) / <alpha-value>)", "component-global-header-account-menu-font-color": "rgb(var(--color-black-and-white-white) / <alpha-value>)", "component-global-header-account-menu-icon-color": "rgb(var(--color-black-and-white-white) / <alpha-value>)", "component-global-header-bag-desktop-fill-color": "rgb(var(--color-black-and-white-white) / <alpha-value>)", "component-global-header-bag-desktop-font-color": "rgb(var(--color-black-and-white-black) / <alpha-value>)", "component-global-header-everyday-free-shipping-desktop-font-color": "rgb(var(--color-black-and-white-white) / <alpha-value>)", "component-global-header-everyday-free-shipping-mobile-background-color": "rgb(var(--color-brand-3) / <alpha-value>)", "component-global-header-everyday-free-shipping-mobile-font-color": "rgb(var(--color-black-and-white-white) / <alpha-value>)", "component-global-header-sister-brand-bar-default-background-color": "rgb(var(--color-brand-3) / <alpha-value>)", "component-global-header-sister-brand-bar-default-font-color": "rgb(var(--color-grayscale-gray-04) / <alpha-value>)", "component-global-header-sister-brand-bar-hover-background-color": "rgb(var(--color-brand-3) / <alpha-value>)", "component-global-header-sister-brand-bar-hover-font-color": "rgb(var(--color-black-and-white-white) / <alpha-value>)", "component-global-header-sister-brand-bar-selected-font-color": "rgb(var(--color-black-and-white-white) / <alpha-value>)", "component-modal-default-header-color": "rgb(var(--color-black-and-white-white) / <alpha-value>)", "component-modal-dialog-background-color": "rgb(var(--color-black-and-white-white) / <alpha-value>)", "component-modal-secondary-header-color": "rgb(var(--color-black-and-white-white) / <alpha-value>)", "component-modal-skinny-header-color": "rgb(var(--color-grayscale-gray-15) / <alpha-value>)", "component-modal-skinny-header-font-color": "rgb(var(--color-grayscale-gray-09) / <alpha-value>)", "component-modal-skinny-header-surface-color": "rgb(var(--color-black-and-white-white) / <alpha-value>)", "component-pdp-header-marketing-message-font-color": "rgb(var(--color-black-and-white-black) / <alpha-value>)", "component-pdp-header-price-discount-%-off": "rgb(var(--color-black-and-white-black) / <alpha-value>)", "component-pdp-header-price-original-price-font-color": "rgb(var(--color-black-and-white-black) / <alpha-value>)", "component-pdp-header-style-flag-font-color": "rgb(var(--color-neutrals-700) / <alpha-value>)", "component-selector-available-focused-border-color": "rgb(var(--color-blue-1000) / <alpha-value>)", "component-selector-available-hover-border-color": "rgb(var(--color-blue-1000) / <alpha-value>)", "component-selector-available-hover-fill": "rgb(var(--color-blue-1000) / <alpha-value>)", "component-selector-available-hover-font-color": "rgb(var(--color-black-and-white-white) / <alpha-value>)", "component-selector-available-selected-border-color": "rgb(var(--color-black-and-white-black) / <alpha-value>)", "component-selector-available-selected-fill": "rgb(var(--color-black-and-white-white) / <alpha-value>)", "component-selector-available-selected-font-color": "rgb(var(--color-black-and-white-black) / <alpha-value>)", "component-selector-border-color": "rgb(var(--color-neutrals-300) / <alpha-value>)", "component-selector-disabled-border-color": "rgb(var(--color-neutrals-300) / <alpha-value>)", "component-selector-disabled-fill": "rgb(var(--color-neutrals-300) / <alpha-value>)", "component-selector-disabled-font-color": "rgb(var(--color-neutrals-700) / <alpha-value>)", "component-selector-fill": "rgb(var(--color-black-and-white-white) / <alpha-value>)", "component-selector-font-color": "rgb(var(--color-black-and-white-black) / <alpha-value>)", "component-selector-link": "rgb(var(--color-brand-2) / <alpha-value>)", "component-selector-swatch-grid-border-color": "rgb(var(--color-neutrals-900) / <alpha-value>)", "component-selector-swatch-module-group-price-discount-font-color": "rgb(var(--color-black-and-white-black) / <alpha-value>)", "component-selector-swatch-module-selection-name-font-color": "rgb(var(--color-neutrals-700) / <alpha-value>)", "component-selector-swatch-swatch-border-color": "rgb(var(--color-neutrals-600) / <alpha-value>)", "component-selector-swatch-swatch-font-color": "rgb(var(--color-black-and-white-black) / <alpha-value>)", "component-selector-unavailable-border-color": "rgb(var(--color-neutrals-300) / <alpha-value>)", "component-selector-unavailable-fill": "rgb(var(--color-black-and-white-white) / <alpha-value>)", "component-selector-unavailable-font-color": "rgb(var(--color-neutrals-800) / <alpha-value>)", "component-selector-unavailable-hover-border-color": "rgb(var(--color-blue-1000) / <alpha-value>)", "component-selector-unavailable-hover-fill": "rgb(var(--color-blue-1000) / <alpha-value>)", "component-selector-unavailable-hover-font-color": "rgb(var(--color-black-and-white-white) / <alpha-value>)", "component-selector-unavailable-hover-strikethrough": "rgb(var(--color-black-and-white-white) / <alpha-value>)", "component-selector-unavailable-selected-border-fill": "rgb(var(--color-black-and-white-black) / <alpha-value>)", "component-selector-unavailable-selected-fill": "rgb(var(--color-black-and-white-white) / <alpha-value>)", "component-selector-unavailable-selected-font-color": "rgb(var(--color-neutrals-800) / <alpha-value>)", "component-selector-unavailable-selected-strikethrough": "rgb(var(--color-black-and-white-black) / <alpha-value>)", "component-selector-unavailable-strikethrough": "rgb(var(--color-neutrals-300) / <alpha-value>)", "component-switch-off-fill": "rgb(var(--color-neutrals-300) / <alpha-value>)", "component-switch-off-font": "rgb(var(--color-neutrals-900) / <alpha-value>)", "component-switch-on-fill": "rgb(var(--color-neutrals-900) / <alpha-value>)", "component-switch-on-font-color": "rgb(var(--color-neutrals-200) / <alpha-value>)", "component-tab-border-color": "rgb(var(--color-neutrals-300) / <alpha-value>)", "component-tab-fill": "rgb(var(--color-black-and-white-white) / <alpha-value>)", "component-tab-font-color": "rgb(var(--color-neutrals-700) / <alpha-value>)", "component-tab-selected-border-color": "rgb(var(--color-neutrals-600) / <alpha-value>)", "global-color-black-and-white-black": "rgb(var(--color-black-and-white-black) / <alpha-value>)", "global-color-black-and-white-black-30-opacity": "rgb(var(--color-black-and-white-black-30) / <alpha-value>)", "global-color-black-and-white-black-50-opacity": "rgb(var(--color-black-and-white-black-50) / <alpha-value>)", "global-color-black-and-white-white": "rgb(var(--color-black-and-white-white) / <alpha-value>)", "global-color-black-and-white-white-30-opacity": "rgb(var(--color-black-and-white-white-30) / <alpha-value>)", "global-color-black-and-white-white-50-opacity": "rgb(var(--color-black-and-white-white-50) / <alpha-value>)", "global-color-grayscale-gray-01": "rgb(var(--color-grayscale-gray-01) / <alpha-value>)", "global-color-grayscale-gray-02": "rgb(var(--color-grayscale-gray-02) / <alpha-value>)", "global-color-grayscale-gray-03": "rgb(var(--color-grayscale-gray-03) / <alpha-value>)", "global-color-grayscale-gray-04": "rgb(var(--color-grayscale-gray-04) / <alpha-value>)", "global-color-grayscale-gray-05": "rgb(var(--color-grayscale-gray-05) / <alpha-value>)", "global-color-grayscale-gray-06": "rgb(var(--color-grayscale-gray-06) / <alpha-value>)", "global-color-grayscale-gray-07": "rgb(var(--color-grayscale-gray-07) / <alpha-value>)", "global-color-grayscale-gray-08": "rgb(var(--color-grayscale-gray-08) / <alpha-value>)", "global-color-grayscale-gray-09": "rgb(var(--color-grayscale-gray-09) / <alpha-value>)", "global-color-grayscale-gray-10": "rgb(var(--color-grayscale-gray-10) / <alpha-value>)", "global-color-grayscale-gray-11": "rgb(var(--color-grayscale-gray-11) / <alpha-value>)", "global-color-grayscale-gray-12": "rgb(var(--color-grayscale-gray-12) / <alpha-value>)", "global-color-grayscale-gray-13": "rgb(var(--color-grayscale-gray-13) / <alpha-value>)", "global-color-grayscale-gray-14": "rgb(var(--color-grayscale-gray-14) / <alpha-value>)", "global-color-grayscale-gray-15": "rgb(var(--color-grayscale-gray-15) / <alpha-value>)", "global-color-grayscale-gray-16": "rgb(var(--color-grayscale-gray-16) / <alpha-value>)", "global-color-neutral-neutral-01": "rgb(var(--color-neutral-neutral-01) / <alpha-value>)", "global-color-neutral-neutral-02": "rgb(var(--color-neutral-neutral-02) / <alpha-value>)", "global-drop-shadow-color": "rgb(var(--color-black-and-white-white) / <alpha-value>)"}, "critical": {"component-button-critical-case": "Button label"}, "criticalButton": {"component-critical-button-text-decoration-line": "None"}, "discount": {"component-pdp-header-price-discount-%-case": " off", "component-selector-swatch-module-group-price-discount-text": "% off"}, "displayLogic": {"component-global-header-sister-brand-bar-display-logic-athleta": true, "component-global-header-sister-brand-bar-display-logic-athleta-link-state": "<PERSON><PERSON><PERSON>", "component-global-header-sister-brand-bar-display-logic-banana-republic": true, "component-global-header-sister-brand-bar-display-logic-banana-republic-link-state": "<PERSON><PERSON><PERSON>", "component-global-header-sister-brand-bar-display-logic-case": "Title", "component-global-header-sister-brand-bar-display-logic-current-site": "Gap", "component-global-header-sister-brand-bar-display-logic-gap-factory": true, "component-global-header-sister-brand-bar-display-logic-gap-link-state": "Selected", "component-global-header-sister-brand-bar-display-logic-old-navy": true, "component-global-header-sister-brand-bar-display-logic-old-navy-link-state": "<PERSON><PERSON><PERSON>"}, "everydayFreeShipping": {"component-global-header-everyday-free-shipping-details-link": "Details", "component-global-header-everyday-free-shipping-message": "Free shipping on $50+ for rewards members", "component-global-header-everyday-free-shipping-sign-in-link": "Sign in or join"}, "fontFamily": {"font-family-base": "var(--font-family-font1)", "font-family-display": "var(--font-family-font2)", "font-family-family": "-"}, "fontSize": {"component-global-header-account-menu-link-font-size": "var(--font-size-12)", "component-global-header-account-menu-name-font-size": "var(--font-size-12)", "component-global-header-account-menu-rewards-font-size": "var(--font-size-12)", "component-global-header-bag-desktop-font-size": "var(--font-size-12)", "component-global-header-bag-mobile-font-size": "var(--font-size-9)", "component-global-header-everyday-free-shipping-desktop-font-size": "var(--font-size-12)", "component-global-header-everyday-free-shipping-mobile-font-size": "var(--font-size-11)", "component-global-header-sister-brand-bar-desktop-font-size": "var(--font-size-12)", "component-global-header-sister-brand-bar-mobile-font-size": "var(--font-size-11)", "component-pdp-header-marketing-message-font-size": "var(--font-size-12)", "component-pdp-header-price-original-price-font-size": "var(--font-size-14)", "component-pdp-header-price-purchase-price-font-size": "var(--font-size-14)", "component-pdp-header-product-title-font-size": "var(--font-size-14)", "component-selector-font-size": "var(--font-size-14)", "component-selector-swatch-module-group-price-discount-font-size": "var(--font-size-12)", "component-selector-swatch-module-group-price-previous-font-size": "var(--font-size-12)", "component-selector-swatch-module-group-price-purchase-font-size": "var(--font-size-16)", "component-switch-font-size": "var(--font-size-10)", "component-tab-font-size": "var(--font-size-16)", "font-size-b-2xl-font-size": "var(--font-size-34)", "font-size-b-l-font-size": "var(--font-size-16)", "font-size-b-m-font-size": "var(--font-size-14)", "font-size-b-s-font-size": "var(--font-size-12)", "font-size-b-xl-font-size": "var(--font-size-20)", "font-size-b-xs-font-size": "var(--font-size-11)", "font-size-d-l-font-size": "var(--font-size-60)", "font-size-d-m-font-size": "var(--font-size-40)", "font-size-d-s-font-size": "var(--font-size-28)", "font-size-d-xl-font-size": "var(--font-size-72)", "font-size-d-xs-font-size": "var(--font-size-18)"}, "fontWeight": {"component-global-header-account-menu-link-font-weight": "var(--font-weight-400)", "component-global-header-account-menu-name-font-weight": "var(--font-weight-400)", "component-global-header-account-menu-rewards-font-weight": "var(--font-weight-400)", "component-global-header-bag-desktop-font-weight": "var(--font-weight-400)", "component-global-header-bag-mobile-font-weight": "var(--font-weight-400)", "component-global-header-everyday-free-shipping-desktop-font-weight": "var(--font-weight-400)", "component-global-header-everyday-free-shipping-mobile-font-weight": "var(--font-weight-400)", "component-global-header-sister-brand-bar-default-font-weight": "var(--font-weight-350)", "component-global-header-sister-brand-bar-selected-font-weight": "var(--font-weight-350)", "component-pdp-header-marketing-message-font-weight": "var(--font-weight-325)", "component-pdp-header-price-original-price-font-weight": "var(--font-weight-400)", "component-pdp-header-price-purchase-price-font-weight": "var(--font-weight-400)", "component-pdp-header-product-title-font-weight": "var(--font-weight-325)", "component-pdp-header-style-flag-font-weight": "var(--font-weight-400)", "component-selector-available-selected-font-weight": "var(--font-weight-500)", "component-selector-font-weight": "var(--font-weight-500)", "component-selector-swatch-module-group-price-discount-font-weight": "var(--font-weight-400)", "component-selector-swatch-module-group-price-previous-font-weight": "var(--font-weight-400)", "component-selector-swatch-module-group-price-purchase-font-weight": "var(--font-weight-400)", "component-selector-swatch-module-label-font-weight": "var(--font-weight-400)", "component-selector-swatch-module-selection-name-font-weight": "var(--font-weight-400)", "component-switch-font-weight": "var(--font-weight-400)", "component-tab-selected-font-weight": "var(--font-weight-400)", "component-tab-unselected-font-weight": "var(--font-weight-400)", "font-weight-bold-font-weight": "var(--font-weight-400)", "font-weight-bolder-font-weight": "var(--font-weight-400)", "font-weight-regular-font-weight": "var(--font-weight-400)"}, "gap": {"component-bopis-gap": "var(--spacing-100)", "component-checkbox-gap": "var(--spacing-100)", "component-chips-gap": "var(--spacing-100)", "component-global-header-sister-brand-bar-display-logic-gap": true, "component-selector-swatch-grid-with-labels-gap": "var(--spacing-400)", "component-selector-swatch-grid-without-labels-gap": "var(--spacing-050)", "component-selector-swatch-module-gap": "var(--spacing-200)", "component-selector-swatch-module-group-price-gap": "var(--spacing-100)"}, "label": {"component-selector-swatch-module-label-string": "Color"}, "letterSpacing": {"font-tracking-b-regular-letter-spacing": "var(--font-letter-spacing-00)", "font-tracking-b-wide-letter-spacing": "var(--font-letter-spacing-08)", "font-tracking-b-wider-letter-spacing": "var(--font-letter-spacing-10)", "font-tracking-d-regular-letter-spacing": "var(--font-letter-spacing-00)", "font-tracking-d-wide-letter-spacing": "var(--font-letter-spacing-10)", "font-tracking-d-wider-letter-spacing": "var(--font-letter-spacing-10)"}, "lineHeight": {"component-pdp-header-marketing-message-line-height": "var(--font-line-height-14)", "component-pdp-header-price-purchase-price-line-height": "var(--font-line-height-18)", "component-pdp-header-product-title-line-height": "var(--font-line-height-18)", "font-size-b-l-line-height": "var(--font-line-height-20)", "font-size-b-m-line-height": "var(--font-line-height-18)", "font-size-b-s-line-height": "var(--font-line-height-14)", "font-size-b-xl-line-height": "var(--font-line-height-22)", "font-size-d-l-line-height": "var(--font-line-height-60)", "font-size-d-m-line-height": "var(--font-line-height-42)", "font-size-d-s-line-height": "var(--font-line-height-32)", "font-size-d-xl-line-height": "var(--font-line-height-72)", "font-size-d-xs-line-height": "var(--font-line-height-32)"}, "link": {"component-global-header-account-menu-link-find-a-store": "Find a store", "component-global-header-account-menu-link-gift-cards": "Gift cards", "component-global-header-account-menu-link-international-shipping": "International shipping", "component-global-header-account-menu-link-orders-and-returns": "Orders & returns", "component-global-header-account-menu-link-points-and-rewards": "Points & rewards", "component-global-header-account-menu-link-rewards-member": "Rewards member", "component-global-header-account-menu-link-sign-out": "Sign out"}, "marketingMessage": {"component-pdp-header-marketing-message-case": "BEST SELLER"}, "maxHeight": {"component-global-header-everyday-free-shipping-mobile-max-height": "var(--spacing-750)"}, "minHeight": {"component-add-to-bag-min-height": "var(--spacing-550)", "component-button-min-height": "var(--spacing-550)", "component-global-header-everyday-free-shipping-mobile-min-height": "var(--spacing-500)", "interactive-min-height": "var(--sizing-550)"}, "mobile": {"component-global-header-everyday-free-shipping-mobile-lower-divider": true}, "padding": {"component-button-padding-horizontal": "var(--spacing-600)", "component-critical-button-padding-horizontal": "var(--spacing-600)", "component-global-header-sister-brand-bar-desktop-padding": "var(--spacing-200)", "component-global-header-sister-brand-bar-mobile-padding": "var(--spacing-125)", "component-pdp-header-price-stack-padding": "var(--spacing-050)", "component-selector-available-focused-padding": "var(--spacing-100)", "component-selector-swatch-grid-with-labels-padding": "var(--spacing-200)", "component-selector-swatch-swatch-focus-padding": "var(--spacing-075)", "component-switch-padding": "var(--spacing-025)", "component-tab-padding-horizontal": "var(--spacing-250)", "component-tab-padding-vertical": "var(--spacing-0)", "component-tabs-tab-padding-horizontal": "var(--spacing-0)", "padding-inline-2x-small": "var(--spacing-025)", "padding-inline-large": "var(--spacing-300)", "padding-inline-medium": "var(--spacing-200)", "padding-inline-small": "var(--spacing-100)", "padding-inline-x-small": "var(--spacing-050)", "padding-stack-2x-small": "var(--spacing-025)", "padding-stack-large": "var(--spacing-300)", "padding-stack-medium": "var(--spacing-200)", "padding-stack-small": "var(--spacing-100)", "padding-stack-x-small": "var(--spacing-050)"}, "primary": {"component-button-primary-case": "Button label"}, "productTitle": {"component-pdp-header-product-title-title-case": "Product Name"}, "rewards": {"component-global-header-account-menu-rewards-invitation": "Join <PERSON> Rewards"}, "secondary": {"component-button-secondary-case": "Button label"}, "selectionName": {"component-selector-swatch-module-selection-name-string": "Selection name"}, "size": {"global-drop-shadow-spread-size": "var(--drop-shadow-spread-size)"}, "sizing": {"global-sizing-0": "var(--sizing-0)", "global-sizing-0125": "var(--sizing-0125)", "global-sizing-025": "var(--sizing-025)", "global-sizing-050": "var(--sizing-050)", "global-sizing-075": "var(--sizing-075)", "global-sizing-100": "var(--sizing-100)", "global-sizing-1000": "var(--sizing-1000)", "global-sizing-150": "var(--sizing-150)", "global-sizing-175": "var(--sizing-175)", "global-sizing-200": "var(--sizing-200)", "global-sizing-225": "var(--sizing-225)", "global-sizing-250": "var(--sizing-250)", "global-sizing-275": "var(--sizing-275)", "global-sizing-400": "var(--sizing-400)", "global-sizing-500": "var(--sizing-500)", "global-sizing-550": "var(--sizing-550)", "global-sizing-600": "var(--sizing-600)", "global-sizing-650": "var(--sizing-650)", "global-sizing-800": "var(--sizing-800)"}, "spacing": {"global-spacing-0": "var(--spacing-0)", "global-spacing-0125": "var(--spacing-0125)", "global-spacing-025": "var(--spacing-025)", "global-spacing-050": "var(--spacing-050)", "global-spacing-075": "var(--spacing-075)", "global-spacing-100": "var(--spacing-100)", "global-spacing-1000": "var(--spacing-1000)", "global-spacing-150": "var(--spacing-150)", "global-spacing-175": "var(--spacing-175)", "global-spacing-200": "var(--spacing-200)", "global-spacing-250": "var(--spacing-250)", "global-spacing-300": "var(--spacing-300)", "global-spacing-400": "var(--spacing-400)", "global-spacing-450": "var(--spacing-450)", "global-spacing-500": "var(--spacing-500)", "global-spacing-550": "var(--spacing-550)", "global-spacing-600": "var(--spacing-600)", "global-spacing-650": "var(--spacing-650)", "global-spacing-750": "var(--spacing-750)", "global-spacing-800": "var(--spacing-800)"}, "textInputs": {"interactive-text-inputs-icon": "Gap"}}