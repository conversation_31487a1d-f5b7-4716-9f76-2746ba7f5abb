@import 'slick-carousel/slick/slick.css';

.SDS_VERSION::after {
  content: '13.2.0';
}
@-webkit-keyframes backgroundBounce {
  0% {
    -webkit-transform: scale(0);
    transform: scale(0);
  }
  90% {
    -webkit-transform: scale(1.1);
    transform: scale(1.1);
  }
  100% {
    -webkit-transform: scale(1);
    transform: scale(1);
  }
}
@keyframes backgroundBounce {
  0% {
    -webkit-transform: scale(0);
    transform: scale(0);
  }
  90% {
    -webkit-transform: scale(1.1);
    transform: scale(1.1);
  }
  100% {
    -webkit-transform: scale(1);
    transform: scale(1);
  }
}
@-webkit-keyframes moving-bar {
  0% {
    background-size: 1px 1px;
  }
  100% {
    background-size: 100% 1px;
  }
}
@keyframes moving-bar {
  0% {
    background-size: 1px 1px;
  }
  100% {
    background-size: 100% 1px;
  }
}
@-webkit-keyframes opacity-transition {
  0% {
    opacity: 1;
  }
  100% {
    opacity: 0;
  }
}
@keyframes opacity-transition {
  0% {
    opacity: 1;
  }
  100% {
    opacity: 0;
  }
}
@-webkit-keyframes loseDimensions {
  0% {
    height: auto;
    width: auto;
    padding: 0.625rem 2rem 0.625rem 2.2rem;
    margin-bottom: 1.125rem;
  }
  100% {
    height: 0;
    width: 0;
    padding: 0;
    margin-bottom: 0;
  }
}
@keyframes loseDimensions {
  0% {
    height: auto;
    width: auto;
    padding: 0.625rem 2rem 0.625rem 2.2rem;
    margin-bottom: 1.125rem;
  }
  100% {
    height: 0;
    width: 0;
    padding: 0;
    margin-bottom: 0;
  }
}
@-ms-viewport {
  width: device-width;
}
html {
  font-size: 100%;
}
h1,
h2,
h3,
h4,
h5,
h6 {
  font-size: 1em;
  font-weight: inherit;
  margin: 0;
}
body {
  margin: 0;
  padding: 0;
  font-family: Helvetica, Arial, sans-serif;
  -webkit-text-size-adjust: 100%;
  -moz-text-size-adjust: 100%;
  -ms-text-size-adjust: 100%;
  text-size-adjust: 100%;
}
a {
  text-decoration: none;
  border-bottom: 0;
  color: inherit;
}
img {
  max-width: 100%;
}
a img {
  border: 0;
}
ol,
ul {
  margin: 0;
  padding: 0;
  list-style: none;
}
li {
  margin: 0;
  padding: 0;
}
label {
  cursor: pointer;
  display: block;
}
button,
input,
select {
  font-size: 1em;
}
button {
  cursor: pointer;
}
input[type='submit'] {
  margin: 0;
  padding: 0;
  border: 0;
}
dd,
dl,
dt {
  margin: 0;
  padding: 0;
}
hr {
  height: 2px;
  background: #cbcaca;
  margin: 0;
  padding: 0;
  border: 0;
}
p {
  margin: 0;
}
.sds-cb_font--primary {
  font-family: 'Source Sans Pro', Helvetica, Arial, Roboto, sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
.sds-cb_font--secondary {
  font-family: 'Source Sans Pro', Helvetica, Arial, Roboto, sans-serif;
  font-weight: 600;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
.sds-cb_font--seasonal {
  font-family: 'Source Sans Pro', Helvetica, Arial, Roboto, sans-serif;
  font-weight: 300;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
.sds-cb_font--helvetica {
  font-weight: 400;
  font-family: 'Gap Sans', Helvetica, Arial, Roboto, sans-serif;
}
.sds-cb_font--helvetica-bold {
  font-weight: 600;
  font-family: 'Gap Sans', Helvetica, Arial, Roboto, sans-serif;
}
.sds-cb_font--source-pro-light {
  font-weight: 300;
  font-family: 'Source Sans Pro', Helvetica, Arial, Roboto, sans-serif;
}
.sds-cb_font--source-pro {
  font-weight: 400;
  font-family: 'Source Sans Pro', Helvetica, Arial, Roboto, sans-serif;
}
.sds-cb_font--source-pro-bold {
  font-weight: 600;
  font-family: 'Source Sans Pro', Helvetica, Arial, Roboto, sans-serif;
}
.modal--base-btn,
.sds_font--primary {
  font-family: 'Avenir Next', 'Gap Sans', Helvetica, Arial, Roboto, sans-serif;
  font-weight: 300;
  -webkit-font-variant-ligatures: none;
  font-variant-ligatures: none;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
.sds_font--secondary {
  font-family: 'Avenir Next', 'Gap Sans', Helvetica, Arial, Roboto, sans-serif;
  font-weight: 600;
  -webkit-font-variant-ligatures: none;
  font-variant-ligatures: none;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
.sds_font--seasonal {
  font-family: DINNextLTPro-Condensed, sofia-webfont, Helvetica, Arial, Roboto, sans-serif;
  -webkit-font-variant-ligatures: none;
  font-variant-ligatures: none;
}
.sds_display-a {
  font-size: 1.5rem;
  line-height: 1.25;
  color: #333;
  font-family: sofia-webfont, Helvetica, Arial, Roboto, sans-serif;
  font-weight: 600;
  -webkit-font-variant-ligatures: none;
  font-variant-ligatures: none;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-transform: uppercase;
}
@media (min-width: 768px) {
  .sds_display-a {
    font-size: 2rem;
  }
}
.sds_display-b {
  line-height: 1;
  text-transform: uppercase;
  color: #666;
  font-family: DINNextLTPro-Condensed, sofia-webfont, Helvetica, Arial, Roboto, sans-serif;
  -webkit-font-variant-ligatures: none;
  font-variant-ligatures: none;
  font-size: 3rem;
  letter-spacing: 2px;
}
@media (min-width: 768px) {
  .sds_display-b {
    font-size: 4.625rem;
  }
}
.heading-a,
.sds_heading-a {
  font-weight: 600;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  color: #333;
  font-family: sofia-webfont, Helvetica, Arial, Roboto, sans-serif;
  -webkit-font-variant-ligatures: none;
  font-variant-ligatures: none;
  font-size: 1.5rem;
}
@media (min-width: 768px) {
  .heading-a,
  .sds_heading-a {
    font-size: 2rem;
  }
}
.heading-b,
.sds_heading-b {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  color: #666;
  font-family: sofia-webfont, Helvetica, Arial, Roboto, sans-serif;
  -webkit-font-variant-ligatures: none;
  font-variant-ligatures: none;
  font-size: 1.5rem;
}
@media (min-width: 768px) {
  .heading-b,
  .sds_heading-b {
    font-size: 2rem;
  }
}
.heading-c,
.sds_heading-c {
  text-transform: uppercase;
  letter-spacing: 0.1em;
  font-weight: 600;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  color: #a7a9ac;
  font-family: sofia-webfont, Helvetica, Arial, Roboto, sans-serif;
  -webkit-font-variant-ligatures: none;
  font-variant-ligatures: none;
  font-size: 1.2rem;
}
.heading-d,
.sds_heading-d {
  font-weight: 600;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-variant-ligatures: none;
  font-variant-ligatures: none;
  font-family: sofia-webfont, Helvetica, Arial, Roboto, sans-serif;
  font-size: 1.067rem;
  color: #333;
}
.heading-e,
.sds_heading-e {
  text-transform: uppercase;
  font-weight: 300;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  color: #333;
  font-family: sofia-webfont, Helvetica, Arial, Roboto, sans-serif;
  -webkit-font-variant-ligatures: none;
  font-variant-ligatures: none;
  font-size: 1.067rem;
  text-transform: none;
}
.body-a,
.sds_body-a {
  line-height: 1.43;
  font-weight: 300;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  color: #666;
  font-family: sofia-webfont, Helvetica, Arial, Roboto, sans-serif;
  -webkit-font-variant-ligatures: none;
  font-variant-ligatures: none;
  font-size: 0.933rem;
}
.sds_body-b {
  line-height: 1.4;
  font-weight: 300;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  color: #666;
  font-family: sofia-webfont, Helvetica, Arial, Roboto, sans-serif;
  -webkit-font-variant-ligatures: none;
  font-variant-ligatures: none;
  font-size: 0.75rem;
}
.sds_link,
.sds_link-a {
  cursor: pointer;
  color: #333;
  text-decoration: underline;
}
.sds_link-a:focus,
.sds_link-a:hover,
.sds_link:focus,
.sds_link:hover {
  text-decoration: none;
}
.label-a,
.sds_label-a {
  text-transform: uppercase;
  font-weight: 600;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  color: #333;
  font-family: sofia-webfont, Helvetica, Arial, Roboto, sans-serif;
  -webkit-font-variant-ligatures: none;
  font-variant-ligatures: none;
  font-size: 0.84rem;
  letter-spacing: 0.06em;
}
.label-b,
.sds_label-b {
  text-transform: uppercase;
  font-weight: 600;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  color: #333;
  font-family: sofia-webfont, Helvetica, Arial, Roboto, sans-serif;
  -webkit-font-variant-ligatures: none;
  font-variant-ligatures: none;
  font-size: 0.5625rem;
  letter-spacing: 0.06em;
}
.sds_label-c {
  font-weight: 300;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  color: #666;
  font-family: sofia-webfont, Helvetica, Arial, Roboto, sans-serif;
  -webkit-font-variant-ligatures: none;
  font-variant-ligatures: none;
  font-size: 0.8125rem;
}
.sds_list-a {
  line-height: 1.25;
  font-weight: 300;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  color: #666;
  font-family: sofia-webfont, Helvetica, Arial, Roboto, sans-serif;
  -webkit-font-variant-ligatures: none;
  font-variant-ligatures: none;
  font-size: 0.875rem;
  padding-left: 0.7rem;
}
.sds_list-a li {
  margin-bottom: 0.3rem;
  position: relative;
  list-style: none inside;
}
.sds_list-a li::before {
  content: '-';
  position: absolute;
  left: -0.7rem;
}
.sds_list-a li:last-of-type {
  margin-bottom: 0;
}
.sds-cb_display-a {
  font-family: 'Source Sans Pro', Helvetica, Arial, Roboto, sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 1.75rem;
  line-height: 1.25;
  color: #333;
}
@media (min-width: 768px) {
  .sds-cb_display-a {
    font-size: 2.625rem;
  }
}
.sds-cb_display-b {
  font-family: 'Source Sans Pro', Helvetica, Arial, Roboto, sans-serif;
  font-weight: 600;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 1.25rem;
  line-height: 1.25;
  letter-spacing: 2px;
  text-transform: uppercase;
  color: #333;
}
.heading-a_universal,
.sds-cb_heading-a,
.sds_heading-a_universal {
  font-family: 'Source Sans Pro', Helvetica, Arial, Roboto, sans-serif;
  font-weight: 300;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 2rem;
  line-height: 1.25;
  color: #333;
}
@media (min-width: 768px) {
  .heading-a_universal,
  .sds-cb_heading-a,
  .sds_heading-a_universal {
    font-size: 3rem;
  }
}
.heading-b_universal,
.sds-cb_heading-b,
.sds_heading-b_universal {
  font-family: 'Source Sans Pro', Helvetica, Arial, Roboto, sans-serif;
  font-weight: 300;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 1.5rem;
  line-height: 1.25;
  color: #666;
}
@media (min-width: 768px) {
  .heading-b_universal,
  .sds-cb_heading-b,
  .sds_heading-b_universal {
    font-size: 1.875rem;
  }
}
.heading-c_universal,
.sds-cb_heading-c,
.sds_heading-c_universal {
  font-family: 'Source Sans Pro', Helvetica, Arial, Roboto, sans-serif;
  font-weight: 300;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 1.375rem;
  line-height: 1.4;
  color: #333;
}
@media (min-width: 768px) {
  .heading-c_universal,
  .sds-cb_heading-c,
  .sds_heading-c_universal {
    font-size: 1.625rem;
  }
}
.heading-d_universal,
.sds-cb_heading-d,
.sds_heading-d_universal {
  font-family: 'Source Sans Pro', Helvetica, Arial, Roboto, sans-serif;
  font-weight: 300;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 1.25rem;
  line-height: 1.4;
  color: #333;
}
.heading-e_universal,
.sds-cb_heading-e,
.sds_heading-e_universal {
  font-family: 'Source Sans Pro', Helvetica, Arial, Roboto, sans-serif;
  font-weight: 600;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 1rem;
  line-height: 1.5;
  color: #333;
}
.body-a_universal,
.sds-cb_body-a,
.sds_body-a_universal {
  font-family: 'Source Sans Pro', Helvetica, Arial, Roboto, sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 0.9375rem;
  line-height: 1.5;
  color: #666;
}
.sds-cb_body-b {
  font-family: 'Source Sans Pro', Helvetica, Arial, Roboto, sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 0.75rem;
  line-height: 1.4;
  color: #666;
}
.sds-cb_link,
.sds-cb_link-a {
  color: #0466ca;
  text-decoration: none;
  cursor: pointer;
}
.sds-cb_link-a:hover,
.sds-cb_link:hover {
  text-decoration: underline;
}
.label-a_universal,
.sds-cb_label-a,
.sds_label-a_universal {
  font-family: 'Source Sans Pro', Helvetica, Arial, Roboto, sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 0.875rem;
  line-height: 1;
  color: #333;
}
.label-b_universal,
.sds-cb_label-b,
.sds_label-b_universal {
  font-family: 'Source Sans Pro', Helvetica, Arial, Roboto, sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 0.75rem;
  line-height: 1;
  color: #666;
}
.label-c_universal,
.sds-cb_label-c,
.sds_label-c_universal {
  font-family: 'Source Sans Pro', Helvetica, Arial, Roboto, sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 0.8125rem;
  color: #666;
}
.sds-cb_list-a {
  font-family: 'Source Sans Pro', Helvetica, Arial, Roboto, sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 0.9375rem;
  line-height: 1.5;
  color: #666;
  padding-left: 0.7rem;
}
.sds-cb_list-a li {
  position: relative;
  list-style: none inside;
  margin-bottom: 0.3rem;
}
.sds-cb_list-a li:last-of-type {
  margin-bottom: 0;
}
.sds-cb_list-a li::before {
  content: '-';
  position: absolute;
  left: -0.7rem;
}
.grid-root,
.sds_grid-root {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
  -ms-flex-flow: row wrap;
  flex-flow: row wrap;
}
.grid-root--negate-outer-gutter,
.sds_grid-root--negate-outer-gutter {
  margin-left: -0.5rem;
  margin-right: -0.5rem;
}
.g,
.sds_g {
  display: inline-block;
  zoom: 1;
  vertical-align: top;
  width: 100%;
}
.g-inner,
.sds_g-inner {
  padding: 0 0.5em;
}
.g-1,
.g-1-1,
.g-1-12,
.g-1-2,
.g-1-24,
.g-1-3,
.g-1-4,
.g-1-5,
.g-1-6,
.g-1-8,
.g-10-24,
.g-11-12,
.g-11-24,
.g-12-24,
.g-13-24,
.g-14-24,
.g-15-24,
.g-16-24,
.g-17-24,
.g-18-24,
.g-19-24,
.g-2-24,
.g-2-3,
.g-2-5,
.g-20-24,
.g-21-24,
.g-22-24,
.g-23-24,
.g-24-24,
.g-3-24,
.g-3-4,
.g-3-5,
.g-3-8,
.g-4-24,
.g-4-5,
.g-5-12,
.g-5-24,
.g-5-5,
.g-5-6,
.g-5-8,
.g-6-24,
.g-7-12,
.g-7-24,
.g-7-8,
.g-8-24,
.g-9-24 {
  display: inline-block;
  zoom: 1;
  vertical-align: top;
}
.g-1-24 {
  width: 4.1667%;
}
.g-1-12,
.g-2-24 {
  width: 8.3333%;
}
.g-1-8,
.g-3-24 {
  width: 12.5%;
}
.g-1-6,
.g-4-24 {
  width: 16.6667%;
}
.g-1-5 {
  width: 20%;
}
.g-5-24 {
  width: 20.8333%;
}
.g-1-4,
.g-6-24 {
  width: 25%;
}
.g-7-24 {
  width: 29.1667%;
}
.g-1-3,
.g-8-24 {
  width: 33.3333%;
}
.g-3-8,
.g-9-24 {
  width: 37.5%;
}
.g-2-5 {
  width: 40%;
}
.g-10-24,
.g-5-12 {
  width: 41.6667%;
}
.g-11-24 {
  width: 45.8333%;
}
.g-1-2,
.g-12-24 {
  width: 50%;
}
.g-13-24 {
  width: 54.1667%;
}
.g-14-24,
.g-7-12 {
  width: 58.3333%;
}
.g-3-5 {
  width: 60%;
}
.g-15-24,
.g-5-8 {
  width: 62.5%;
}
.g-16-24,
.g-2-3 {
  width: 66.6667%;
}
.g-17-24 {
  width: 70.8333%;
}
.g-18-24,
.g-3-4 {
  width: 75%;
}
.g-19-24 {
  width: 79.1667%;
}
.g-4-5 {
  width: 80%;
}
.g-20-24,
.g-5-6 {
  width: 83.3333%;
}
.g-21-24,
.g-7-8 {
  width: 87.5%;
}
.g-11-12,
.g-22-24 {
  width: 91.6667%;
}
.g-23-24 {
  width: 95.8333%;
}
.g-1,
.g-1-1,
.g-24-24,
.g-5-5 {
  width: 100%;
}
.sds_g-1,
.sds_g-1-1,
.sds_g-1-10,
.sds_g-1-11,
.sds_g-1-12,
.sds_g-1-2,
.sds_g-1-24,
.sds_g-1-3,
.sds_g-1-4,
.sds_g-1-5,
.sds_g-1-6,
.sds_g-1-7,
.sds_g-1-8,
.sds_g-1-9,
.sds_g-10-24,
.sds_g-11-12,
.sds_g-11-24,
.sds_g-12-24,
.sds_g-13-24,
.sds_g-14-24,
.sds_g-15-24,
.sds_g-16-24,
.sds_g-17-24,
.sds_g-18-24,
.sds_g-19-24,
.sds_g-2-24,
.sds_g-2-3,
.sds_g-2-5,
.sds_g-2-7,
.sds_g-2-9,
.sds_g-20-24,
.sds_g-21-24,
.sds_g-22-24,
.sds_g-23-24,
.sds_g-24-24,
.sds_g-3-24,
.sds_g-3-4,
.sds_g-3-5,
.sds_g-3-7,
.sds_g-3-8,
.sds_g-4-24,
.sds_g-4-5,
.sds_g-4-7,
.sds_g-4-9,
.sds_g-5-12,
.sds_g-5-24,
.sds_g-5-5,
.sds_g-5-6,
.sds_g-5-7,
.sds_g-5-8,
.sds_g-5-9,
.sds_g-6-24,
.sds_g-6-7,
.sds_g-7-12,
.sds_g-7-24,
.sds_g-7-8,
.sds_g-7-9,
.sds_g-8-24,
.sds_g-8-9,
.sds_g-9-24 {
  display: inline-block;
  zoom: 1;
  vertical-align: top;
}
.sds_g-1-24 {
  width: 4.1667%;
}
.sds_g-1-12,
.sds_g-2-24 {
  width: 8.3333%;
}
.sds_g-1-11 {
  width: 9.0909%;
}
.sds_g-1-10 {
  width: 10%;
}
.sds_g-1-9 {
  width: 11.1111%;
}
.sds_g-1-8,
.sds_g-3-24 {
  width: 12.5%;
}
.sds_g-1-7 {
  width: 14.2857%;
}
.sds_g-1-6,
.sds_g-4-24 {
  width: 16.6667%;
}
.sds_g-1-5 {
  width: 20%;
}
.sds_g-5-24 {
  width: 20.8333%;
}
.sds_g-2-9 {
  width: 22.2222%;
}
.sds_g-1-4,
.sds_g-6-24 {
  width: 25%;
}
.sds_g-2-7 {
  width: 28.5714%;
}
.sds_g-7-24 {
  width: 29.1667%;
}
.sds_g-1-3,
.sds_g-8-24 {
  width: 33.3333%;
}
.sds_g-3-8,
.sds_g-9-24 {
  width: 37.5%;
}
.sds_g-2-5 {
  width: 40%;
}
.sds_g-10-24,
.sds_g-5-12 {
  width: 41.6667%;
}
.sds_g-3-7 {
  width: 42.8571%;
}
.sds_g-4-9 {
  width: 44.4444%;
}
.sds_g-11-24 {
  width: 45.8333%;
}
.sds_g-1-2,
.sds_g-12-24 {
  width: 50%;
}
.sds_g-13-24 {
  width: 54.1667%;
}
.sds_g-5-9 {
  width: 55.5556%;
}
.sds_g-4-7 {
  width: 57.1429%;
}
.sds_g-14-24,
.sds_g-7-12 {
  width: 58.3333%;
}
.sds_g-3-5 {
  width: 60%;
}
.sds_g-15-24,
.sds_g-5-8 {
  width: 62.5%;
}
.sds_g-16-24,
.sds_g-2-3 {
  width: 66.6667%;
}
.sds_g-17-24 {
  width: 70.8333%;
}
.sds_g-5-7 {
  width: 71.4286%;
}
.sds_g-18-24,
.sds_g-3-4 {
  width: 75%;
}
.sds_g-7-9 {
  width: 77.7778%;
}
.sds_g-19-24 {
  width: 79.1667%;
}
.sds_g-4-5 {
  width: 80%;
}
.sds_g-20-24,
.sds_g-5-6 {
  width: 83.3333%;
}
.sds_g-6-7 {
  width: 85.7143%;
}
.sds_g-21-24,
.sds_g-7-8 {
  width: 87.5%;
}
.sds_g-8-9 {
  width: 88.8889%;
}
.sds_g-11-12,
.sds_g-22-24 {
  width: 91.6667%;
}
.sds_g-23-24 {
  width: 95.8333%;
}
.sds_g-1,
.sds_g-1-1,
.sds_g-24-24,
.sds_g-5-5 {
  width: 100%;
}
.g-md-1,
.g-md-1-1,
.g-md-1-12,
.g-md-1-2,
.g-md-1-24,
.g-md-1-3,
.g-md-1-4,
.g-md-1-5,
.g-md-1-6,
.g-md-1-8,
.g-md-10-24,
.g-md-11-12,
.g-md-11-24,
.g-md-12-24,
.g-md-13-24,
.g-md-14-24,
.g-md-15-24,
.g-md-16-24,
.g-md-17-24,
.g-md-18-24,
.g-md-19-24,
.g-md-2-24,
.g-md-2-3,
.g-md-2-5,
.g-md-20-24,
.g-md-21-24,
.g-md-22-24,
.g-md-23-24,
.g-md-24-24,
.g-md-3-24,
.g-md-3-4,
.g-md-3-5,
.g-md-3-8,
.g-md-4-24,
.g-md-4-5,
.g-md-5-12,
.g-md-5-24,
.g-md-5-5,
.g-md-5-6,
.g-md-5-8,
.g-md-6-24,
.g-md-7-12,
.g-md-7-24,
.g-md-7-8,
.g-md-8-24,
.g-md-9-24 {
  display: inline-block;
  zoom: 1;
  vertical-align: top;
}
@media (min-width: 569px) {
  .g-md-1-24 {
    width: 4.1667%;
  }
  .g-md-1-12,
  .g-md-2-24 {
    width: 8.3333%;
  }
  .g-md-1-8,
  .g-md-3-24 {
    width: 12.5%;
  }
  .g-md-1-6,
  .g-md-4-24 {
    width: 16.6667%;
  }
  .g-md-1-5 {
    width: 20%;
  }
  .g-md-5-24 {
    width: 20.8333%;
  }
  .g-md-1-4,
  .g-md-6-24 {
    width: 25%;
  }
  .g-md-7-24 {
    width: 29.1667%;
  }
  .g-md-1-3,
  .g-md-8-24 {
    width: 33.3333%;
  }
  .g-md-3-8,
  .g-md-9-24 {
    width: 37.5%;
  }
  .g-md-2-5 {
    width: 40%;
  }
  .g-md-10-24,
  .g-md-5-12 {
    width: 41.6667%;
  }
  .g-md-11-24 {
    width: 45.8333%;
  }
  .g-md-1-2,
  .g-md-12-24 {
    width: 50%;
  }
  .g-md-13-24 {
    width: 54.1667%;
  }
  .g-md-14-24,
  .g-md-7-12 {
    width: 58.3333%;
  }
  .g-md-3-5 {
    width: 60%;
  }
  .g-md-15-24,
  .g-md-5-8 {
    width: 62.5%;
  }
  .g-md-16-24,
  .g-md-2-3 {
    width: 66.6667%;
  }
  .g-md-17-24 {
    width: 70.8333%;
  }
  .g-md-18-24,
  .g-md-3-4 {
    width: 75%;
  }
  .g-md-19-24 {
    width: 79.1667%;
  }
  .g-md-4-5 {
    width: 80%;
  }
  .g-md-20-24,
  .g-md-5-6 {
    width: 83.3333%;
  }
  .g-md-21-24,
  .g-md-7-8 {
    width: 87.5%;
  }
  .g-md-11-12,
  .g-md-22-24 {
    width: 91.6667%;
  }
  .g-md-23-24 {
    width: 95.8333%;
  }
  .g-md-1,
  .g-md-1-1,
  .g-md-24-24,
  .g-md-5-5 {
    width: 100%;
  }
}
.g-lg-1,
.g-lg-1-1,
.g-lg-1-12,
.g-lg-1-2,
.g-lg-1-24,
.g-lg-1-3,
.g-lg-1-4,
.g-lg-1-5,
.g-lg-1-6,
.g-lg-1-8,
.g-lg-10-24,
.g-lg-11-12,
.g-lg-11-24,
.g-lg-12-24,
.g-lg-13-24,
.g-lg-14-24,
.g-lg-15-24,
.g-lg-16-24,
.g-lg-17-24,
.g-lg-18-24,
.g-lg-19-24,
.g-lg-2-24,
.g-lg-2-3,
.g-lg-2-5,
.g-lg-20-24,
.g-lg-21-24,
.g-lg-22-24,
.g-lg-23-24,
.g-lg-24-24,
.g-lg-3-24,
.g-lg-3-4,
.g-lg-3-5,
.g-lg-3-8,
.g-lg-4-24,
.g-lg-4-5,
.g-lg-5-12,
.g-lg-5-24,
.g-lg-5-5,
.g-lg-5-6,
.g-lg-5-8,
.g-lg-6-24,
.g-lg-7-12,
.g-lg-7-24,
.g-lg-7-8,
.g-lg-8-24,
.g-lg-9-24 {
  display: inline-block;
  zoom: 1;
  vertical-align: top;
}
@media (min-width: 768px) {
  .g-lg-1-24 {
    width: 4.1667%;
  }
  .g-lg-1-12,
  .g-lg-2-24 {
    width: 8.3333%;
  }
  .g-lg-1-8,
  .g-lg-3-24 {
    width: 12.5%;
  }
  .g-lg-1-6,
  .g-lg-4-24 {
    width: 16.6667%;
  }
  .g-lg-1-5 {
    width: 20%;
  }
  .g-lg-5-24 {
    width: 20.8333%;
  }
  .g-lg-1-4,
  .g-lg-6-24 {
    width: 25%;
  }
  .g-lg-7-24 {
    width: 29.1667%;
  }
  .g-lg-1-3,
  .g-lg-8-24 {
    width: 33.3333%;
  }
  .g-lg-3-8,
  .g-lg-9-24 {
    width: 37.5%;
  }
  .g-lg-2-5 {
    width: 40%;
  }
  .g-lg-10-24,
  .g-lg-5-12 {
    width: 41.6667%;
  }
  .g-lg-11-24 {
    width: 45.8333%;
  }
  .g-lg-1-2,
  .g-lg-12-24 {
    width: 50%;
  }
  .g-lg-13-24 {
    width: 54.1667%;
  }
  .g-lg-14-24,
  .g-lg-7-12 {
    width: 58.3333%;
  }
  .g-lg-3-5 {
    width: 60%;
  }
  .g-lg-15-24,
  .g-lg-5-8 {
    width: 62.5%;
  }
  .g-lg-16-24,
  .g-lg-2-3 {
    width: 66.6667%;
  }
  .g-lg-17-24 {
    width: 70.8333%;
  }
  .g-lg-18-24,
  .g-lg-3-4 {
    width: 75%;
  }
  .g-lg-19-24 {
    width: 79.1667%;
  }
  .g-lg-4-5 {
    width: 80%;
  }
  .g-lg-20-24,
  .g-lg-5-6 {
    width: 83.3333%;
  }
  .g-lg-21-24,
  .g-lg-7-8 {
    width: 87.5%;
  }
  .g-lg-11-12,
  .g-lg-22-24 {
    width: 91.6667%;
  }
  .g-lg-23-24 {
    width: 95.8333%;
  }
  .g-lg-1,
  .g-lg-1-1,
  .g-lg-24-24,
  .g-lg-5-5 {
    width: 100%;
  }
}
.g-xl-1,
.g-xl-1-1,
.g-xl-1-12,
.g-xl-1-2,
.g-xl-1-24,
.g-xl-1-3,
.g-xl-1-4,
.g-xl-1-5,
.g-xl-1-6,
.g-xl-1-8,
.g-xl-10-24,
.g-xl-11-12,
.g-xl-11-24,
.g-xl-12-24,
.g-xl-13-24,
.g-xl-14-24,
.g-xl-15-24,
.g-xl-16-24,
.g-xl-17-24,
.g-xl-18-24,
.g-xl-19-24,
.g-xl-2-24,
.g-xl-2-3,
.g-xl-2-5,
.g-xl-20-24,
.g-xl-21-24,
.g-xl-22-24,
.g-xl-23-24,
.g-xl-24-24,
.g-xl-3-24,
.g-xl-3-4,
.g-xl-3-5,
.g-xl-3-8,
.g-xl-4-24,
.g-xl-4-5,
.g-xl-5-12,
.g-xl-5-24,
.g-xl-5-5,
.g-xl-5-6,
.g-xl-5-8,
.g-xl-6-24,
.g-xl-7-12,
.g-xl-7-24,
.g-xl-7-8,
.g-xl-8-24,
.g-xl-9-24 {
  display: inline-block;
  zoom: 1;
  vertical-align: top;
}
@media (min-width: 1024px) {
  .g-xl-1-24 {
    width: 4.1667%;
  }
  .g-xl-1-12,
  .g-xl-2-24 {
    width: 8.3333%;
  }
  .g-xl-1-8,
  .g-xl-3-24 {
    width: 12.5%;
  }
  .g-xl-1-6,
  .g-xl-4-24 {
    width: 16.6667%;
  }
  .g-xl-1-5 {
    width: 20%;
  }
  .g-xl-5-24 {
    width: 20.8333%;
  }
  .g-xl-1-4,
  .g-xl-6-24 {
    width: 25%;
  }
  .g-xl-7-24 {
    width: 29.1667%;
  }
  .g-xl-1-3,
  .g-xl-8-24 {
    width: 33.3333%;
  }
  .g-xl-3-8,
  .g-xl-9-24 {
    width: 37.5%;
  }
  .g-xl-2-5 {
    width: 40%;
  }
  .g-xl-10-24,
  .g-xl-5-12 {
    width: 41.6667%;
  }
  .g-xl-11-24 {
    width: 45.8333%;
  }
  .g-xl-1-2,
  .g-xl-12-24 {
    width: 50%;
  }
  .g-xl-13-24 {
    width: 54.1667%;
  }
  .g-xl-14-24,
  .g-xl-7-12 {
    width: 58.3333%;
  }
  .g-xl-3-5 {
    width: 60%;
  }
  .g-xl-15-24,
  .g-xl-5-8 {
    width: 62.5%;
  }
  .g-xl-16-24,
  .g-xl-2-3 {
    width: 66.6667%;
  }
  .g-xl-17-24 {
    width: 70.8333%;
  }
  .g-xl-18-24,
  .g-xl-3-4 {
    width: 75%;
  }
  .g-xl-19-24 {
    width: 79.1667%;
  }
  .g-xl-4-5 {
    width: 80%;
  }
  .g-xl-20-24,
  .g-xl-5-6 {
    width: 83.3333%;
  }
  .g-xl-21-24,
  .g-xl-7-8 {
    width: 87.5%;
  }
  .g-xl-11-12,
  .g-xl-22-24 {
    width: 91.6667%;
  }
  .g-xl-23-24 {
    width: 95.8333%;
  }
  .g-xl-1,
  .g-xl-1-1,
  .g-xl-24-24,
  .g-xl-5-5 {
    width: 100%;
  }
}
.g-1280-1,
.g-1280-1-1,
.g-1280-1-12,
.g-1280-1-2,
.g-1280-1-24,
.g-1280-1-3,
.g-1280-1-4,
.g-1280-1-5,
.g-1280-1-6,
.g-1280-1-8,
.g-1280-10-24,
.g-1280-11-12,
.g-1280-11-24,
.g-1280-12-24,
.g-1280-13-24,
.g-1280-14-24,
.g-1280-15-24,
.g-1280-16-24,
.g-1280-17-24,
.g-1280-18-24,
.g-1280-19-24,
.g-1280-2-24,
.g-1280-2-3,
.g-1280-2-5,
.g-1280-20-24,
.g-1280-21-24,
.g-1280-22-24,
.g-1280-23-24,
.g-1280-24-24,
.g-1280-3-24,
.g-1280-3-4,
.g-1280-3-5,
.g-1280-3-8,
.g-1280-4-24,
.g-1280-4-5,
.g-1280-5-12,
.g-1280-5-24,
.g-1280-5-5,
.g-1280-5-6,
.g-1280-5-8,
.g-1280-6-24,
.g-1280-7-12,
.g-1280-7-24,
.g-1280-7-8,
.g-1280-8-24,
.g-1280-9-24 {
  display: inline-block;
  zoom: 1;
  vertical-align: top;
}
@media (min-width: 1280px) {
  .g-1280-1-24 {
    width: 4.1667%;
  }
  .g-1280-1-12,
  .g-1280-2-24 {
    width: 8.3333%;
  }
  .g-1280-1-8,
  .g-1280-3-24 {
    width: 12.5%;
  }
  .g-1280-1-6,
  .g-1280-4-24 {
    width: 16.6667%;
  }
  .g-1280-1-5 {
    width: 20%;
  }
  .g-1280-5-24 {
    width: 20.8333%;
  }
  .g-1280-1-4,
  .g-1280-6-24 {
    width: 25%;
  }
  .g-1280-7-24 {
    width: 29.1667%;
  }
  .g-1280-1-3,
  .g-1280-8-24 {
    width: 33.3333%;
  }
  .g-1280-3-8,
  .g-1280-9-24 {
    width: 37.5%;
  }
  .g-1280-2-5 {
    width: 40%;
  }
  .g-1280-10-24,
  .g-1280-5-12 {
    width: 41.6667%;
  }
  .g-1280-11-24 {
    width: 45.8333%;
  }
  .g-1280-1-2,
  .g-1280-12-24 {
    width: 50%;
  }
  .g-1280-13-24 {
    width: 54.1667%;
  }
  .g-1280-14-24,
  .g-1280-7-12 {
    width: 58.3333%;
  }
  .g-1280-3-5 {
    width: 60%;
  }
  .g-1280-15-24,
  .g-1280-5-8 {
    width: 62.5%;
  }
  .g-1280-16-24,
  .g-1280-2-3 {
    width: 66.6667%;
  }
  .g-1280-17-24 {
    width: 70.8333%;
  }
  .g-1280-18-24,
  .g-1280-3-4 {
    width: 75%;
  }
  .g-1280-19-24 {
    width: 79.1667%;
  }
  .g-1280-4-5 {
    width: 80%;
  }
  .g-1280-20-24,
  .g-1280-5-6 {
    width: 83.3333%;
  }
  .g-1280-21-24,
  .g-1280-7-8 {
    width: 87.5%;
  }
  .g-1280-11-12,
  .g-1280-22-24 {
    width: 91.6667%;
  }
  .g-1280-23-24 {
    width: 95.8333%;
  }
  .g-1280-1,
  .g-1280-1-1,
  .g-1280-24-24,
  .g-1280-5-5 {
    width: 100%;
  }
}
.g-1440-1,
.g-1440-1-1,
.g-1440-1-12,
.g-1440-1-2,
.g-1440-1-24,
.g-1440-1-3,
.g-1440-1-4,
.g-1440-1-5,
.g-1440-1-6,
.g-1440-1-8,
.g-1440-10-24,
.g-1440-11-12,
.g-1440-11-24,
.g-1440-12-24,
.g-1440-13-24,
.g-1440-14-24,
.g-1440-15-24,
.g-1440-16-24,
.g-1440-17-24,
.g-1440-18-24,
.g-1440-19-24,
.g-1440-2-24,
.g-1440-2-3,
.g-1440-2-5,
.g-1440-20-24,
.g-1440-21-24,
.g-1440-22-24,
.g-1440-23-24,
.g-1440-24-24,
.g-1440-3-24,
.g-1440-3-4,
.g-1440-3-5,
.g-1440-3-8,
.g-1440-4-24,
.g-1440-4-5,
.g-1440-5-12,
.g-1440-5-24,
.g-1440-5-5,
.g-1440-5-6,
.g-1440-5-8,
.g-1440-6-24,
.g-1440-7-12,
.g-1440-7-24,
.g-1440-7-8,
.g-1440-8-24,
.g-1440-9-24 {
  display: inline-block;
  zoom: 1;
  vertical-align: top;
}
@media (min-width: 1440px) {
  .g-1440-1-24 {
    width: 4.1667%;
  }
  .g-1440-1-12,
  .g-1440-2-24 {
    width: 8.3333%;
  }
  .g-1440-1-8,
  .g-1440-3-24 {
    width: 12.5%;
  }
  .g-1440-1-6,
  .g-1440-4-24 {
    width: 16.6667%;
  }
  .g-1440-1-5 {
    width: 20%;
  }
  .g-1440-5-24 {
    width: 20.8333%;
  }
  .g-1440-1-4,
  .g-1440-6-24 {
    width: 25%;
  }
  .g-1440-7-24 {
    width: 29.1667%;
  }
  .g-1440-1-3,
  .g-1440-8-24 {
    width: 33.3333%;
  }
  .g-1440-3-8,
  .g-1440-9-24 {
    width: 37.5%;
  }
  .g-1440-2-5 {
    width: 40%;
  }
  .g-1440-10-24,
  .g-1440-5-12 {
    width: 41.6667%;
  }
  .g-1440-11-24 {
    width: 45.8333%;
  }
  .g-1440-1-2,
  .g-1440-12-24 {
    width: 50%;
  }
  .g-1440-13-24 {
    width: 54.1667%;
  }
  .g-1440-14-24,
  .g-1440-7-12 {
    width: 58.3333%;
  }
  .g-1440-3-5 {
    width: 60%;
  }
  .g-1440-15-24,
  .g-1440-5-8 {
    width: 62.5%;
  }
  .g-1440-16-24,
  .g-1440-2-3 {
    width: 66.6667%;
  }
  .g-1440-17-24 {
    width: 70.8333%;
  }
  .g-1440-18-24,
  .g-1440-3-4 {
    width: 75%;
  }
  .g-1440-19-24 {
    width: 79.1667%;
  }
  .g-1440-4-5 {
    width: 80%;
  }
  .g-1440-20-24,
  .g-1440-5-6 {
    width: 83.3333%;
  }
  .g-1440-21-24,
  .g-1440-7-8 {
    width: 87.5%;
  }
  .g-1440-11-12,
  .g-1440-22-24 {
    width: 91.6667%;
  }
  .g-1440-23-24 {
    width: 95.8333%;
  }
  .g-1440-1,
  .g-1440-1-1,
  .g-1440-24-24,
  .g-1440-5-5 {
    width: 100%;
  }
}
.g-landscape-1,
.g-landscape-1-1,
.g-landscape-1-12,
.g-landscape-1-2,
.g-landscape-1-24,
.g-landscape-1-3,
.g-landscape-1-4,
.g-landscape-1-5,
.g-landscape-1-6,
.g-landscape-1-8,
.g-landscape-10-24,
.g-landscape-11-12,
.g-landscape-11-24,
.g-landscape-12-24,
.g-landscape-13-24,
.g-landscape-14-24,
.g-landscape-15-24,
.g-landscape-16-24,
.g-landscape-17-24,
.g-landscape-18-24,
.g-landscape-19-24,
.g-landscape-2-24,
.g-landscape-2-3,
.g-landscape-2-5,
.g-landscape-20-24,
.g-landscape-21-24,
.g-landscape-22-24,
.g-landscape-23-24,
.g-landscape-24-24,
.g-landscape-3-24,
.g-landscape-3-4,
.g-landscape-3-5,
.g-landscape-3-8,
.g-landscape-4-24,
.g-landscape-4-5,
.g-landscape-5-12,
.g-landscape-5-24,
.g-landscape-5-5,
.g-landscape-5-6,
.g-landscape-5-8,
.g-landscape-6-24,
.g-landscape-7-12,
.g-landscape-7-24,
.g-landscape-7-8,
.g-landscape-8-24,
.g-landscape-9-24 {
  display: inline-block;
  zoom: 1;
  vertical-align: top;
}
@media (min-width: 300px) and (min-aspect-ratio: 1 / 1), (min-width: 1024px) {
  .g-landscape-1-24 {
    width: 4.1667%;
  }
  .g-landscape-1-12,
  .g-landscape-2-24 {
    width: 8.3333%;
  }
  .g-landscape-1-8,
  .g-landscape-3-24 {
    width: 12.5%;
  }
  .g-landscape-1-6,
  .g-landscape-4-24 {
    width: 16.6667%;
  }
  .g-landscape-1-5 {
    width: 20%;
  }
  .g-landscape-5-24 {
    width: 20.8333%;
  }
  .g-landscape-1-4,
  .g-landscape-6-24 {
    width: 25%;
  }
  .g-landscape-7-24 {
    width: 29.1667%;
  }
  .g-landscape-1-3,
  .g-landscape-8-24 {
    width: 33.3333%;
  }
  .g-landscape-3-8,
  .g-landscape-9-24 {
    width: 37.5%;
  }
  .g-landscape-2-5 {
    width: 40%;
  }
  .g-landscape-10-24,
  .g-landscape-5-12 {
    width: 41.6667%;
  }
  .g-landscape-11-24 {
    width: 45.8333%;
  }
  .g-landscape-1-2,
  .g-landscape-12-24 {
    width: 50%;
  }
  .g-landscape-13-24 {
    width: 54.1667%;
  }
  .g-landscape-14-24,
  .g-landscape-7-12 {
    width: 58.3333%;
  }
  .g-landscape-3-5 {
    width: 60%;
  }
  .g-landscape-15-24,
  .g-landscape-5-8 {
    width: 62.5%;
  }
  .g-landscape-16-24,
  .g-landscape-2-3 {
    width: 66.6667%;
  }
  .g-landscape-17-24 {
    width: 70.8333%;
  }
  .g-landscape-18-24,
  .g-landscape-3-4 {
    width: 75%;
  }
  .g-landscape-19-24 {
    width: 79.1667%;
  }
  .g-landscape-4-5 {
    width: 80%;
  }
  .g-landscape-20-24,
  .g-landscape-5-6 {
    width: 83.3333%;
  }
  .g-landscape-21-24,
  .g-landscape-7-8 {
    width: 87.5%;
  }
  .g-landscape-11-12,
  .g-landscape-22-24 {
    width: 91.6667%;
  }
  .g-landscape-23-24 {
    width: 95.8333%;
  }
  .g-landscape-1,
  .g-landscape-1-1,
  .g-landscape-24-24,
  .g-landscape-5-5 {
    width: 100%;
  }
}
.g-landscape-lg-1,
.g-landscape-lg-1-1,
.g-landscape-lg-1-12,
.g-landscape-lg-1-2,
.g-landscape-lg-1-24,
.g-landscape-lg-1-3,
.g-landscape-lg-1-4,
.g-landscape-lg-1-5,
.g-landscape-lg-1-6,
.g-landscape-lg-1-8,
.g-landscape-lg-10-24,
.g-landscape-lg-11-12,
.g-landscape-lg-11-24,
.g-landscape-lg-12-24,
.g-landscape-lg-13-24,
.g-landscape-lg-14-24,
.g-landscape-lg-15-24,
.g-landscape-lg-16-24,
.g-landscape-lg-17-24,
.g-landscape-lg-18-24,
.g-landscape-lg-19-24,
.g-landscape-lg-2-24,
.g-landscape-lg-2-3,
.g-landscape-lg-2-5,
.g-landscape-lg-20-24,
.g-landscape-lg-21-24,
.g-landscape-lg-22-24,
.g-landscape-lg-23-24,
.g-landscape-lg-24-24,
.g-landscape-lg-3-24,
.g-landscape-lg-3-4,
.g-landscape-lg-3-5,
.g-landscape-lg-3-8,
.g-landscape-lg-4-24,
.g-landscape-lg-4-5,
.g-landscape-lg-5-12,
.g-landscape-lg-5-24,
.g-landscape-lg-5-5,
.g-landscape-lg-5-6,
.g-landscape-lg-5-8,
.g-landscape-lg-6-24,
.g-landscape-lg-7-12,
.g-landscape-lg-7-24,
.g-landscape-lg-7-8,
.g-landscape-lg-8-24,
.g-landscape-lg-9-24 {
  display: inline-block;
  zoom: 1;
  vertical-align: top;
}
@media (min-width: 768px) and (min-aspect-ratio: 1 / 1), (min-width: 1024px) {
  .g-landscape-lg-1-24 {
    width: 4.1667%;
  }
  .g-landscape-lg-1-12,
  .g-landscape-lg-2-24 {
    width: 8.3333%;
  }
  .g-landscape-lg-1-8,
  .g-landscape-lg-3-24 {
    width: 12.5%;
  }
  .g-landscape-lg-1-6,
  .g-landscape-lg-4-24 {
    width: 16.6667%;
  }
  .g-landscape-lg-1-5 {
    width: 20%;
  }
  .g-landscape-lg-5-24 {
    width: 20.8333%;
  }
  .g-landscape-lg-1-4,
  .g-landscape-lg-6-24 {
    width: 25%;
  }
  .g-landscape-lg-7-24 {
    width: 29.1667%;
  }
  .g-landscape-lg-1-3,
  .g-landscape-lg-8-24 {
    width: 33.3333%;
  }
  .g-landscape-lg-3-8,
  .g-landscape-lg-9-24 {
    width: 37.5%;
  }
  .g-landscape-lg-2-5 {
    width: 40%;
  }
  .g-landscape-lg-10-24,
  .g-landscape-lg-5-12 {
    width: 41.6667%;
  }
  .g-landscape-lg-11-24 {
    width: 45.8333%;
  }
  .g-landscape-lg-1-2,
  .g-landscape-lg-12-24 {
    width: 50%;
  }
  .g-landscape-lg-13-24 {
    width: 54.1667%;
  }
  .g-landscape-lg-14-24,
  .g-landscape-lg-7-12 {
    width: 58.3333%;
  }
  .g-landscape-lg-3-5 {
    width: 60%;
  }
  .g-landscape-lg-15-24,
  .g-landscape-lg-5-8 {
    width: 62.5%;
  }
  .g-landscape-lg-16-24,
  .g-landscape-lg-2-3 {
    width: 66.6667%;
  }
  .g-landscape-lg-17-24 {
    width: 70.8333%;
  }
  .g-landscape-lg-18-24,
  .g-landscape-lg-3-4 {
    width: 75%;
  }
  .g-landscape-lg-19-24 {
    width: 79.1667%;
  }
  .g-landscape-lg-4-5 {
    width: 80%;
  }
  .g-landscape-lg-20-24,
  .g-landscape-lg-5-6 {
    width: 83.3333%;
  }
  .g-landscape-lg-21-24,
  .g-landscape-lg-7-8 {
    width: 87.5%;
  }
  .g-landscape-lg-11-12,
  .g-landscape-lg-22-24 {
    width: 91.6667%;
  }
  .g-landscape-lg-23-24 {
    width: 95.8333%;
  }
  .g-landscape-lg-1,
  .g-landscape-lg-1-1,
  .g-landscape-lg-24-24,
  .g-landscape-lg-5-5 {
    width: 100%;
  }
}
.sds_g-md-1,
.sds_g-md-1-1,
.sds_g-md-1-10,
.sds_g-md-1-11,
.sds_g-md-1-12,
.sds_g-md-1-2,
.sds_g-md-1-24,
.sds_g-md-1-3,
.sds_g-md-1-4,
.sds_g-md-1-5,
.sds_g-md-1-6,
.sds_g-md-1-7,
.sds_g-md-1-8,
.sds_g-md-1-9,
.sds_g-md-10-24,
.sds_g-md-11-12,
.sds_g-md-11-24,
.sds_g-md-12-24,
.sds_g-md-13-24,
.sds_g-md-14-24,
.sds_g-md-15-24,
.sds_g-md-16-24,
.sds_g-md-17-24,
.sds_g-md-18-24,
.sds_g-md-19-24,
.sds_g-md-2-24,
.sds_g-md-2-3,
.sds_g-md-2-5,
.sds_g-md-2-7,
.sds_g-md-2-9,
.sds_g-md-20-24,
.sds_g-md-21-24,
.sds_g-md-22-24,
.sds_g-md-23-24,
.sds_g-md-24-24,
.sds_g-md-3-24,
.sds_g-md-3-4,
.sds_g-md-3-5,
.sds_g-md-3-7,
.sds_g-md-3-8,
.sds_g-md-4-24,
.sds_g-md-4-5,
.sds_g-md-4-7,
.sds_g-md-4-9,
.sds_g-md-5-12,
.sds_g-md-5-24,
.sds_g-md-5-5,
.sds_g-md-5-6,
.sds_g-md-5-7,
.sds_g-md-5-8,
.sds_g-md-5-9,
.sds_g-md-6-24,
.sds_g-md-6-7,
.sds_g-md-7-12,
.sds_g-md-7-24,
.sds_g-md-7-8,
.sds_g-md-7-9,
.sds_g-md-8-24,
.sds_g-md-8-9,
.sds_g-md-9-24 {
  display: inline-block;
  zoom: 1;
  vertical-align: top;
}
@media (min-width: 569px) {
  .sds_g-md-1-24 {
    width: 4.1667%;
  }
  .sds_g-md-1-12,
  .sds_g-md-2-24 {
    width: 8.3333%;
  }
  .sds_g-md-1-11 {
    width: 9.0909%;
  }
  .sds_g-md-1-10 {
    width: 10%;
  }
  .sds_g-md-1-9 {
    width: 11.1111%;
  }
  .sds_g-md-1-8,
  .sds_g-md-3-24 {
    width: 12.5%;
  }
  .sds_g-md-1-7 {
    width: 14.2857%;
  }
  .sds_g-md-1-6,
  .sds_g-md-4-24 {
    width: 16.6667%;
  }
  .sds_g-md-1-5 {
    width: 20%;
  }
  .sds_g-md-5-24 {
    width: 20.8333%;
  }
  .sds_g-md-2-9 {
    width: 22.2222%;
  }
  .sds_g-md-1-4,
  .sds_g-md-6-24 {
    width: 25%;
  }
  .sds_g-md-2-7 {
    width: 28.5714%;
  }
  .sds_g-md-7-24 {
    width: 29.1667%;
  }
  .sds_g-md-1-3,
  .sds_g-md-8-24 {
    width: 33.3333%;
  }
  .sds_g-md-3-8,
  .sds_g-md-9-24 {
    width: 37.5%;
  }
  .sds_g-md-2-5 {
    width: 40%;
  }
  .sds_g-md-10-24,
  .sds_g-md-5-12 {
    width: 41.6667%;
  }
  .sds_g-md-3-7 {
    width: 42.8571%;
  }
  .sds_g-md-4-9 {
    width: 44.4444%;
  }
  .sds_g-md-11-24 {
    width: 45.8333%;
  }
  .sds_g-md-1-2,
  .sds_g-md-12-24 {
    width: 50%;
  }
  .sds_g-md-13-24 {
    width: 54.1667%;
  }
  .sds_g-md-5-9 {
    width: 55.5556%;
  }
  .sds_g-md-4-7 {
    width: 57.1429%;
  }
  .sds_g-md-14-24,
  .sds_g-md-7-12 {
    width: 58.3333%;
  }
  .sds_g-md-3-5 {
    width: 60%;
  }
  .sds_g-md-15-24,
  .sds_g-md-5-8 {
    width: 62.5%;
  }
  .sds_g-md-16-24,
  .sds_g-md-2-3 {
    width: 66.6667%;
  }
  .sds_g-md-17-24 {
    width: 70.8333%;
  }
  .sds_g-md-5-7 {
    width: 71.4286%;
  }
  .sds_g-md-18-24,
  .sds_g-md-3-4 {
    width: 75%;
  }
  .sds_g-md-7-9 {
    width: 77.7778%;
  }
  .sds_g-md-19-24 {
    width: 79.1667%;
  }
  .sds_g-md-4-5 {
    width: 80%;
  }
  .sds_g-md-20-24,
  .sds_g-md-5-6 {
    width: 83.3333%;
  }
  .sds_g-md-6-7 {
    width: 85.7143%;
  }
  .sds_g-md-21-24,
  .sds_g-md-7-8 {
    width: 87.5%;
  }
  .sds_g-md-8-9 {
    width: 88.8889%;
  }
  .sds_g-md-11-12,
  .sds_g-md-22-24 {
    width: 91.6667%;
  }
  .sds_g-md-23-24 {
    width: 95.8333%;
  }
  .sds_g-md-1,
  .sds_g-md-1-1,
  .sds_g-md-24-24,
  .sds_g-md-5-5 {
    width: 100%;
  }
}
.sds_g-lg-1,
.sds_g-lg-1-1,
.sds_g-lg-1-10,
.sds_g-lg-1-11,
.sds_g-lg-1-12,
.sds_g-lg-1-2,
.sds_g-lg-1-24,
.sds_g-lg-1-3,
.sds_g-lg-1-4,
.sds_g-lg-1-5,
.sds_g-lg-1-6,
.sds_g-lg-1-7,
.sds_g-lg-1-8,
.sds_g-lg-1-9,
.sds_g-lg-10-24,
.sds_g-lg-11-12,
.sds_g-lg-11-24,
.sds_g-lg-12-24,
.sds_g-lg-13-24,
.sds_g-lg-14-24,
.sds_g-lg-15-24,
.sds_g-lg-16-24,
.sds_g-lg-17-24,
.sds_g-lg-18-24,
.sds_g-lg-19-24,
.sds_g-lg-2-24,
.sds_g-lg-2-3,
.sds_g-lg-2-5,
.sds_g-lg-2-7,
.sds_g-lg-2-9,
.sds_g-lg-20-24,
.sds_g-lg-21-24,
.sds_g-lg-22-24,
.sds_g-lg-23-24,
.sds_g-lg-24-24,
.sds_g-lg-3-24,
.sds_g-lg-3-4,
.sds_g-lg-3-5,
.sds_g-lg-3-7,
.sds_g-lg-3-8,
.sds_g-lg-4-24,
.sds_g-lg-4-5,
.sds_g-lg-4-7,
.sds_g-lg-4-9,
.sds_g-lg-5-12,
.sds_g-lg-5-24,
.sds_g-lg-5-5,
.sds_g-lg-5-6,
.sds_g-lg-5-7,
.sds_g-lg-5-8,
.sds_g-lg-5-9,
.sds_g-lg-6-24,
.sds_g-lg-6-7,
.sds_g-lg-7-12,
.sds_g-lg-7-24,
.sds_g-lg-7-8,
.sds_g-lg-7-9,
.sds_g-lg-8-24,
.sds_g-lg-8-9,
.sds_g-lg-9-24 {
  display: inline-block;
  zoom: 1;
  vertical-align: top;
}
@media (min-width: 768px) {
  .sds_g-lg-1-24 {
    width: 4.1667%;
  }
  .sds_g-lg-1-12,
  .sds_g-lg-2-24 {
    width: 8.3333%;
  }
  .sds_g-lg-1-11 {
    width: 9.0909%;
  }
  .sds_g-lg-1-10 {
    width: 10%;
  }
  .sds_g-lg-1-9 {
    width: 11.1111%;
  }
  .sds_g-lg-1-8,
  .sds_g-lg-3-24 {
    width: 12.5%;
  }
  .sds_g-lg-1-7 {
    width: 14.2857%;
  }
  .sds_g-lg-1-6,
  .sds_g-lg-4-24 {
    width: 16.6667%;
  }
  .sds_g-lg-1-5 {
    width: 20%;
  }
  .sds_g-lg-5-24 {
    width: 20.8333%;
  }
  .sds_g-lg-2-9 {
    width: 22.2222%;
  }
  .sds_g-lg-1-4,
  .sds_g-lg-6-24 {
    width: 25%;
  }
  .sds_g-lg-2-7 {
    width: 28.5714%;
  }
  .sds_g-lg-7-24 {
    width: 29.1667%;
  }
  .sds_g-lg-1-3,
  .sds_g-lg-8-24 {
    width: 33.3333%;
  }
  .sds_g-lg-3-8,
  .sds_g-lg-9-24 {
    width: 37.5%;
  }
  .sds_g-lg-2-5 {
    width: 40%;
  }
  .sds_g-lg-10-24,
  .sds_g-lg-5-12 {
    width: 41.6667%;
  }
  .sds_g-lg-3-7 {
    width: 42.8571%;
  }
  .sds_g-lg-4-9 {
    width: 44.4444%;
  }
  .sds_g-lg-11-24 {
    width: 45.8333%;
  }
  .sds_g-lg-1-2,
  .sds_g-lg-12-24 {
    width: 50%;
  }
  .sds_g-lg-13-24 {
    width: 54.1667%;
  }
  .sds_g-lg-5-9 {
    width: 55.5556%;
  }
  .sds_g-lg-4-7 {
    width: 57.1429%;
  }
  .sds_g-lg-14-24,
  .sds_g-lg-7-12 {
    width: 58.3333%;
  }
  .sds_g-lg-3-5 {
    width: 60%;
  }
  .sds_g-lg-15-24,
  .sds_g-lg-5-8 {
    width: 62.5%;
  }
  .sds_g-lg-16-24,
  .sds_g-lg-2-3 {
    width: 66.6667%;
  }
  .sds_g-lg-17-24 {
    width: 70.8333%;
  }
  .sds_g-lg-5-7 {
    width: 71.4286%;
  }
  .sds_g-lg-18-24,
  .sds_g-lg-3-4 {
    width: 75%;
  }
  .sds_g-lg-7-9 {
    width: 77.7778%;
  }
  .sds_g-lg-19-24 {
    width: 79.1667%;
  }
  .sds_g-lg-4-5 {
    width: 80%;
  }
  .sds_g-lg-20-24,
  .sds_g-lg-5-6 {
    width: 83.3333%;
  }
  .sds_g-lg-6-7 {
    width: 85.7143%;
  }
  .sds_g-lg-21-24,
  .sds_g-lg-7-8 {
    width: 87.5%;
  }
  .sds_g-lg-8-9 {
    width: 88.8889%;
  }
  .sds_g-lg-11-12,
  .sds_g-lg-22-24 {
    width: 91.6667%;
  }
  .sds_g-lg-23-24 {
    width: 95.8333%;
  }
  .sds_g-lg-1,
  .sds_g-lg-1-1,
  .sds_g-lg-24-24,
  .sds_g-lg-5-5 {
    width: 100%;
  }
}
.sds_g-xl-1,
.sds_g-xl-1-1,
.sds_g-xl-1-10,
.sds_g-xl-1-11,
.sds_g-xl-1-12,
.sds_g-xl-1-2,
.sds_g-xl-1-24,
.sds_g-xl-1-3,
.sds_g-xl-1-4,
.sds_g-xl-1-5,
.sds_g-xl-1-6,
.sds_g-xl-1-7,
.sds_g-xl-1-8,
.sds_g-xl-1-9,
.sds_g-xl-10-24,
.sds_g-xl-11-12,
.sds_g-xl-11-24,
.sds_g-xl-12-24,
.sds_g-xl-13-24,
.sds_g-xl-14-24,
.sds_g-xl-15-24,
.sds_g-xl-16-24,
.sds_g-xl-17-24,
.sds_g-xl-18-24,
.sds_g-xl-19-24,
.sds_g-xl-2-24,
.sds_g-xl-2-3,
.sds_g-xl-2-5,
.sds_g-xl-2-7,
.sds_g-xl-2-9,
.sds_g-xl-20-24,
.sds_g-xl-21-24,
.sds_g-xl-22-24,
.sds_g-xl-23-24,
.sds_g-xl-24-24,
.sds_g-xl-3-24,
.sds_g-xl-3-4,
.sds_g-xl-3-5,
.sds_g-xl-3-7,
.sds_g-xl-3-8,
.sds_g-xl-4-24,
.sds_g-xl-4-5,
.sds_g-xl-4-7,
.sds_g-xl-4-9,
.sds_g-xl-5-12,
.sds_g-xl-5-24,
.sds_g-xl-5-5,
.sds_g-xl-5-6,
.sds_g-xl-5-7,
.sds_g-xl-5-8,
.sds_g-xl-5-9,
.sds_g-xl-6-24,
.sds_g-xl-6-7,
.sds_g-xl-7-12,
.sds_g-xl-7-24,
.sds_g-xl-7-8,
.sds_g-xl-7-9,
.sds_g-xl-8-24,
.sds_g-xl-8-9,
.sds_g-xl-9-24 {
  display: inline-block;
  zoom: 1;
  vertical-align: top;
}
@media (min-width: 1024px) {
  .sds_g-xl-1-24 {
    width: 4.1667%;
  }
  .sds_g-xl-1-12,
  .sds_g-xl-2-24 {
    width: 8.3333%;
  }
  .sds_g-xl-1-11 {
    width: 9.0909%;
  }
  .sds_g-xl-1-10 {
    width: 10%;
  }
  .sds_g-xl-1-9 {
    width: 11.1111%;
  }
  .sds_g-xl-1-8,
  .sds_g-xl-3-24 {
    width: 12.5%;
  }
  .sds_g-xl-1-7 {
    width: 14.2857%;
  }
  .sds_g-xl-1-6,
  .sds_g-xl-4-24 {
    width: 16.6667%;
  }
  .sds_g-xl-1-5 {
    width: 20%;
  }
  .sds_g-xl-5-24 {
    width: 20.8333%;
  }
  .sds_g-xl-2-9 {
    width: 22.2222%;
  }
  .sds_g-xl-1-4,
  .sds_g-xl-6-24 {
    width: 25%;
  }
  .sds_g-xl-2-7 {
    width: 28.5714%;
  }
  .sds_g-xl-7-24 {
    width: 29.1667%;
  }
  .sds_g-xl-1-3,
  .sds_g-xl-8-24 {
    width: 33.3333%;
  }
  .sds_g-xl-3-8,
  .sds_g-xl-9-24 {
    width: 37.5%;
  }
  .sds_g-xl-2-5 {
    width: 40%;
  }
  .sds_g-xl-10-24,
  .sds_g-xl-5-12 {
    width: 41.6667%;
  }
  .sds_g-xl-3-7 {
    width: 42.8571%;
  }
  .sds_g-xl-4-9 {
    width: 44.4444%;
  }
  .sds_g-xl-11-24 {
    width: 45.8333%;
  }
  .sds_g-xl-1-2,
  .sds_g-xl-12-24 {
    width: 50%;
  }
  .sds_g-xl-13-24 {
    width: 54.1667%;
  }
  .sds_g-xl-5-9 {
    width: 55.5556%;
  }
  .sds_g-xl-4-7 {
    width: 57.1429%;
  }
  .sds_g-xl-14-24,
  .sds_g-xl-7-12 {
    width: 58.3333%;
  }
  .sds_g-xl-3-5 {
    width: 60%;
  }
  .sds_g-xl-15-24,
  .sds_g-xl-5-8 {
    width: 62.5%;
  }
  .sds_g-xl-16-24,
  .sds_g-xl-2-3 {
    width: 66.6667%;
  }
  .sds_g-xl-17-24 {
    width: 70.8333%;
  }
  .sds_g-xl-5-7 {
    width: 71.4286%;
  }
  .sds_g-xl-18-24,
  .sds_g-xl-3-4 {
    width: 75%;
  }
  .sds_g-xl-7-9 {
    width: 77.7778%;
  }
  .sds_g-xl-19-24 {
    width: 79.1667%;
  }
  .sds_g-xl-4-5 {
    width: 80%;
  }
  .sds_g-xl-20-24,
  .sds_g-xl-5-6 {
    width: 83.3333%;
  }
  .sds_g-xl-6-7 {
    width: 85.7143%;
  }
  .sds_g-xl-21-24,
  .sds_g-xl-7-8 {
    width: 87.5%;
  }
  .sds_g-xl-8-9 {
    width: 88.8889%;
  }
  .sds_g-xl-11-12,
  .sds_g-xl-22-24 {
    width: 91.6667%;
  }
  .sds_g-xl-23-24 {
    width: 95.8333%;
  }
  .sds_g-xl-1,
  .sds_g-xl-1-1,
  .sds_g-xl-24-24,
  .sds_g-xl-5-5 {
    width: 100%;
  }
}
.sds_g-1280-1,
.sds_g-1280-1-1,
.sds_g-1280-1-10,
.sds_g-1280-1-11,
.sds_g-1280-1-12,
.sds_g-1280-1-2,
.sds_g-1280-1-24,
.sds_g-1280-1-3,
.sds_g-1280-1-4,
.sds_g-1280-1-5,
.sds_g-1280-1-6,
.sds_g-1280-1-7,
.sds_g-1280-1-8,
.sds_g-1280-1-9,
.sds_g-1280-10-24,
.sds_g-1280-11-12,
.sds_g-1280-11-24,
.sds_g-1280-12-24,
.sds_g-1280-13-24,
.sds_g-1280-14-24,
.sds_g-1280-15-24,
.sds_g-1280-16-24,
.sds_g-1280-17-24,
.sds_g-1280-18-24,
.sds_g-1280-19-24,
.sds_g-1280-2-24,
.sds_g-1280-2-3,
.sds_g-1280-2-5,
.sds_g-1280-2-7,
.sds_g-1280-2-9,
.sds_g-1280-20-24,
.sds_g-1280-21-24,
.sds_g-1280-22-24,
.sds_g-1280-23-24,
.sds_g-1280-24-24,
.sds_g-1280-3-24,
.sds_g-1280-3-4,
.sds_g-1280-3-5,
.sds_g-1280-3-7,
.sds_g-1280-3-8,
.sds_g-1280-4-24,
.sds_g-1280-4-5,
.sds_g-1280-4-7,
.sds_g-1280-4-9,
.sds_g-1280-5-12,
.sds_g-1280-5-24,
.sds_g-1280-5-5,
.sds_g-1280-5-6,
.sds_g-1280-5-7,
.sds_g-1280-5-8,
.sds_g-1280-5-9,
.sds_g-1280-6-24,
.sds_g-1280-6-7,
.sds_g-1280-7-12,
.sds_g-1280-7-24,
.sds_g-1280-7-8,
.sds_g-1280-7-9,
.sds_g-1280-8-24,
.sds_g-1280-8-9,
.sds_g-1280-9-24 {
  display: inline-block;
  zoom: 1;
  vertical-align: top;
}
@media (min-width: 1280px) {
  .sds_g-1280-1-24 {
    width: 4.1667%;
  }
  .sds_g-1280-1-12,
  .sds_g-1280-2-24 {
    width: 8.3333%;
  }
  .sds_g-1280-1-11 {
    width: 9.0909%;
  }
  .sds_g-1280-1-10 {
    width: 10%;
  }
  .sds_g-1280-1-9 {
    width: 11.1111%;
  }
  .sds_g-1280-1-8,
  .sds_g-1280-3-24 {
    width: 12.5%;
  }
  .sds_g-1280-1-7 {
    width: 14.2857%;
  }
  .sds_g-1280-1-6,
  .sds_g-1280-4-24 {
    width: 16.6667%;
  }
  .sds_g-1280-1-5 {
    width: 20%;
  }
  .sds_g-1280-5-24 {
    width: 20.8333%;
  }
  .sds_g-1280-2-9 {
    width: 22.2222%;
  }
  .sds_g-1280-1-4,
  .sds_g-1280-6-24 {
    width: 25%;
  }
  .sds_g-1280-2-7 {
    width: 28.5714%;
  }
  .sds_g-1280-7-24 {
    width: 29.1667%;
  }
  .sds_g-1280-1-3,
  .sds_g-1280-8-24 {
    width: 33.3333%;
  }
  .sds_g-1280-3-8,
  .sds_g-1280-9-24 {
    width: 37.5%;
  }
  .sds_g-1280-2-5 {
    width: 40%;
  }
  .sds_g-1280-10-24,
  .sds_g-1280-5-12 {
    width: 41.6667%;
  }
  .sds_g-1280-3-7 {
    width: 42.8571%;
  }
  .sds_g-1280-4-9 {
    width: 44.4444%;
  }
  .sds_g-1280-11-24 {
    width: 45.8333%;
  }
  .sds_g-1280-1-2,
  .sds_g-1280-12-24 {
    width: 50%;
  }
  .sds_g-1280-13-24 {
    width: 54.1667%;
  }
  .sds_g-1280-5-9 {
    width: 55.5556%;
  }
  .sds_g-1280-4-7 {
    width: 57.1429%;
  }
  .sds_g-1280-14-24,
  .sds_g-1280-7-12 {
    width: 58.3333%;
  }
  .sds_g-1280-3-5 {
    width: 60%;
  }
  .sds_g-1280-15-24,
  .sds_g-1280-5-8 {
    width: 62.5%;
  }
  .sds_g-1280-16-24,
  .sds_g-1280-2-3 {
    width: 66.6667%;
  }
  .sds_g-1280-17-24 {
    width: 70.8333%;
  }
  .sds_g-1280-5-7 {
    width: 71.4286%;
  }
  .sds_g-1280-18-24,
  .sds_g-1280-3-4 {
    width: 75%;
  }
  .sds_g-1280-7-9 {
    width: 77.7778%;
  }
  .sds_g-1280-19-24 {
    width: 79.1667%;
  }
  .sds_g-1280-4-5 {
    width: 80%;
  }
  .sds_g-1280-20-24,
  .sds_g-1280-5-6 {
    width: 83.3333%;
  }
  .sds_g-1280-6-7 {
    width: 85.7143%;
  }
  .sds_g-1280-21-24,
  .sds_g-1280-7-8 {
    width: 87.5%;
  }
  .sds_g-1280-8-9 {
    width: 88.8889%;
  }
  .sds_g-1280-11-12,
  .sds_g-1280-22-24 {
    width: 91.6667%;
  }
  .sds_g-1280-23-24 {
    width: 95.8333%;
  }
  .sds_g-1280-1,
  .sds_g-1280-1-1,
  .sds_g-1280-24-24,
  .sds_g-1280-5-5 {
    width: 100%;
  }
}
.sds_g-1440-1,
.sds_g-1440-1-1,
.sds_g-1440-1-10,
.sds_g-1440-1-11,
.sds_g-1440-1-12,
.sds_g-1440-1-2,
.sds_g-1440-1-24,
.sds_g-1440-1-3,
.sds_g-1440-1-4,
.sds_g-1440-1-5,
.sds_g-1440-1-6,
.sds_g-1440-1-7,
.sds_g-1440-1-8,
.sds_g-1440-1-9,
.sds_g-1440-10-24,
.sds_g-1440-11-12,
.sds_g-1440-11-24,
.sds_g-1440-12-24,
.sds_g-1440-13-24,
.sds_g-1440-14-24,
.sds_g-1440-15-24,
.sds_g-1440-16-24,
.sds_g-1440-17-24,
.sds_g-1440-18-24,
.sds_g-1440-19-24,
.sds_g-1440-2-24,
.sds_g-1440-2-3,
.sds_g-1440-2-5,
.sds_g-1440-2-7,
.sds_g-1440-2-9,
.sds_g-1440-20-24,
.sds_g-1440-21-24,
.sds_g-1440-22-24,
.sds_g-1440-23-24,
.sds_g-1440-24-24,
.sds_g-1440-3-24,
.sds_g-1440-3-4,
.sds_g-1440-3-5,
.sds_g-1440-3-7,
.sds_g-1440-3-8,
.sds_g-1440-4-24,
.sds_g-1440-4-5,
.sds_g-1440-4-7,
.sds_g-1440-4-9,
.sds_g-1440-5-12,
.sds_g-1440-5-24,
.sds_g-1440-5-5,
.sds_g-1440-5-6,
.sds_g-1440-5-7,
.sds_g-1440-5-8,
.sds_g-1440-5-9,
.sds_g-1440-6-24,
.sds_g-1440-6-7,
.sds_g-1440-7-12,
.sds_g-1440-7-24,
.sds_g-1440-7-8,
.sds_g-1440-7-9,
.sds_g-1440-8-24,
.sds_g-1440-8-9,
.sds_g-1440-9-24 {
  display: inline-block;
  zoom: 1;
  vertical-align: top;
}
@media (min-width: 1440px) {
  .sds_g-1440-1-24 {
    width: 4.1667%;
  }
  .sds_g-1440-1-12,
  .sds_g-1440-2-24 {
    width: 8.3333%;
  }
  .sds_g-1440-1-11 {
    width: 9.0909%;
  }
  .sds_g-1440-1-10 {
    width: 10%;
  }
  .sds_g-1440-1-9 {
    width: 11.1111%;
  }
  .sds_g-1440-1-8,
  .sds_g-1440-3-24 {
    width: 12.5%;
  }
  .sds_g-1440-1-7 {
    width: 14.2857%;
  }
  .sds_g-1440-1-6,
  .sds_g-1440-4-24 {
    width: 16.6667%;
  }
  .sds_g-1440-1-5 {
    width: 20%;
  }
  .sds_g-1440-5-24 {
    width: 20.8333%;
  }
  .sds_g-1440-2-9 {
    width: 22.2222%;
  }
  .sds_g-1440-1-4,
  .sds_g-1440-6-24 {
    width: 25%;
  }
  .sds_g-1440-2-7 {
    width: 28.5714%;
  }
  .sds_g-1440-7-24 {
    width: 29.1667%;
  }
  .sds_g-1440-1-3,
  .sds_g-1440-8-24 {
    width: 33.3333%;
  }
  .sds_g-1440-3-8,
  .sds_g-1440-9-24 {
    width: 37.5%;
  }
  .sds_g-1440-2-5 {
    width: 40%;
  }
  .sds_g-1440-10-24,
  .sds_g-1440-5-12 {
    width: 41.6667%;
  }
  .sds_g-1440-3-7 {
    width: 42.8571%;
  }
  .sds_g-1440-4-9 {
    width: 44.4444%;
  }
  .sds_g-1440-11-24 {
    width: 45.8333%;
  }
  .sds_g-1440-1-2,
  .sds_g-1440-12-24 {
    width: 50%;
  }
  .sds_g-1440-13-24 {
    width: 54.1667%;
  }
  .sds_g-1440-5-9 {
    width: 55.5556%;
  }
  .sds_g-1440-4-7 {
    width: 57.1429%;
  }
  .sds_g-1440-14-24,
  .sds_g-1440-7-12 {
    width: 58.3333%;
  }
  .sds_g-1440-3-5 {
    width: 60%;
  }
  .sds_g-1440-15-24,
  .sds_g-1440-5-8 {
    width: 62.5%;
  }
  .sds_g-1440-16-24,
  .sds_g-1440-2-3 {
    width: 66.6667%;
  }
  .sds_g-1440-17-24 {
    width: 70.8333%;
  }
  .sds_g-1440-5-7 {
    width: 71.4286%;
  }
  .sds_g-1440-18-24,
  .sds_g-1440-3-4 {
    width: 75%;
  }
  .sds_g-1440-7-9 {
    width: 77.7778%;
  }
  .sds_g-1440-19-24 {
    width: 79.1667%;
  }
  .sds_g-1440-4-5 {
    width: 80%;
  }
  .sds_g-1440-20-24,
  .sds_g-1440-5-6 {
    width: 83.3333%;
  }
  .sds_g-1440-6-7 {
    width: 85.7143%;
  }
  .sds_g-1440-21-24,
  .sds_g-1440-7-8 {
    width: 87.5%;
  }
  .sds_g-1440-8-9 {
    width: 88.8889%;
  }
  .sds_g-1440-11-12,
  .sds_g-1440-22-24 {
    width: 91.6667%;
  }
  .sds_g-1440-23-24 {
    width: 95.8333%;
  }
  .sds_g-1440-1,
  .sds_g-1440-1-1,
  .sds_g-1440-24-24,
  .sds_g-1440-5-5 {
    width: 100%;
  }
}
.sds_g-landscape-1,
.sds_g-landscape-1-1,
.sds_g-landscape-1-10,
.sds_g-landscape-1-11,
.sds_g-landscape-1-12,
.sds_g-landscape-1-2,
.sds_g-landscape-1-24,
.sds_g-landscape-1-3,
.sds_g-landscape-1-4,
.sds_g-landscape-1-5,
.sds_g-landscape-1-6,
.sds_g-landscape-1-7,
.sds_g-landscape-1-8,
.sds_g-landscape-1-9,
.sds_g-landscape-10-24,
.sds_g-landscape-11-12,
.sds_g-landscape-11-24,
.sds_g-landscape-12-24,
.sds_g-landscape-13-24,
.sds_g-landscape-14-24,
.sds_g-landscape-15-24,
.sds_g-landscape-16-24,
.sds_g-landscape-17-24,
.sds_g-landscape-18-24,
.sds_g-landscape-19-24,
.sds_g-landscape-2-24,
.sds_g-landscape-2-3,
.sds_g-landscape-2-5,
.sds_g-landscape-2-7,
.sds_g-landscape-2-9,
.sds_g-landscape-20-24,
.sds_g-landscape-21-24,
.sds_g-landscape-22-24,
.sds_g-landscape-23-24,
.sds_g-landscape-24-24,
.sds_g-landscape-3-24,
.sds_g-landscape-3-4,
.sds_g-landscape-3-5,
.sds_g-landscape-3-7,
.sds_g-landscape-3-8,
.sds_g-landscape-4-24,
.sds_g-landscape-4-5,
.sds_g-landscape-4-7,
.sds_g-landscape-4-9,
.sds_g-landscape-5-12,
.sds_g-landscape-5-24,
.sds_g-landscape-5-5,
.sds_g-landscape-5-6,
.sds_g-landscape-5-7,
.sds_g-landscape-5-8,
.sds_g-landscape-5-9,
.sds_g-landscape-6-24,
.sds_g-landscape-6-7,
.sds_g-landscape-7-12,
.sds_g-landscape-7-24,
.sds_g-landscape-7-8,
.sds_g-landscape-7-9,
.sds_g-landscape-8-24,
.sds_g-landscape-8-9,
.sds_g-landscape-9-24 {
  display: inline-block;
  zoom: 1;
  vertical-align: top;
}
@media (min-width: 300px) and (min-aspect-ratio: 1 / 1), (min-width: 1024px) {
  .sds_g-landscape-1-24 {
    width: 4.1667%;
  }
  .sds_g-landscape-1-12,
  .sds_g-landscape-2-24 {
    width: 8.3333%;
  }
  .sds_g-landscape-1-11 {
    width: 9.0909%;
  }
  .sds_g-landscape-1-10 {
    width: 10%;
  }
  .sds_g-landscape-1-9 {
    width: 11.1111%;
  }
  .sds_g-landscape-1-8,
  .sds_g-landscape-3-24 {
    width: 12.5%;
  }
  .sds_g-landscape-1-7 {
    width: 14.2857%;
  }
  .sds_g-landscape-1-6,
  .sds_g-landscape-4-24 {
    width: 16.6667%;
  }
  .sds_g-landscape-1-5 {
    width: 20%;
  }
  .sds_g-landscape-5-24 {
    width: 20.8333%;
  }
  .sds_g-landscape-2-9 {
    width: 22.2222%;
  }
  .sds_g-landscape-1-4,
  .sds_g-landscape-6-24 {
    width: 25%;
  }
  .sds_g-landscape-2-7 {
    width: 28.5714%;
  }
  .sds_g-landscape-7-24 {
    width: 29.1667%;
  }
  .sds_g-landscape-1-3,
  .sds_g-landscape-8-24 {
    width: 33.3333%;
  }
  .sds_g-landscape-3-8,
  .sds_g-landscape-9-24 {
    width: 37.5%;
  }
  .sds_g-landscape-2-5 {
    width: 40%;
  }
  .sds_g-landscape-10-24,
  .sds_g-landscape-5-12 {
    width: 41.6667%;
  }
  .sds_g-landscape-3-7 {
    width: 42.8571%;
  }
  .sds_g-landscape-4-9 {
    width: 44.4444%;
  }
  .sds_g-landscape-11-24 {
    width: 45.8333%;
  }
  .sds_g-landscape-1-2,
  .sds_g-landscape-12-24 {
    width: 50%;
  }
  .sds_g-landscape-13-24 {
    width: 54.1667%;
  }
  .sds_g-landscape-5-9 {
    width: 55.5556%;
  }
  .sds_g-landscape-4-7 {
    width: 57.1429%;
  }
  .sds_g-landscape-14-24,
  .sds_g-landscape-7-12 {
    width: 58.3333%;
  }
  .sds_g-landscape-3-5 {
    width: 60%;
  }
  .sds_g-landscape-15-24,
  .sds_g-landscape-5-8 {
    width: 62.5%;
  }
  .sds_g-landscape-16-24,
  .sds_g-landscape-2-3 {
    width: 66.6667%;
  }
  .sds_g-landscape-17-24 {
    width: 70.8333%;
  }
  .sds_g-landscape-5-7 {
    width: 71.4286%;
  }
  .sds_g-landscape-18-24,
  .sds_g-landscape-3-4 {
    width: 75%;
  }
  .sds_g-landscape-7-9 {
    width: 77.7778%;
  }
  .sds_g-landscape-19-24 {
    width: 79.1667%;
  }
  .sds_g-landscape-4-5 {
    width: 80%;
  }
  .sds_g-landscape-20-24,
  .sds_g-landscape-5-6 {
    width: 83.3333%;
  }
  .sds_g-landscape-6-7 {
    width: 85.7143%;
  }
  .sds_g-landscape-21-24,
  .sds_g-landscape-7-8 {
    width: 87.5%;
  }
  .sds_g-landscape-8-9 {
    width: 88.8889%;
  }
  .sds_g-landscape-11-12,
  .sds_g-landscape-22-24 {
    width: 91.6667%;
  }
  .sds_g-landscape-23-24 {
    width: 95.8333%;
  }
  .sds_g-landscape-1,
  .sds_g-landscape-1-1,
  .sds_g-landscape-24-24,
  .sds_g-landscape-5-5 {
    width: 100%;
  }
}
.sds_g-landscape-lg-1,
.sds_g-landscape-lg-1-1,
.sds_g-landscape-lg-1-10,
.sds_g-landscape-lg-1-11,
.sds_g-landscape-lg-1-12,
.sds_g-landscape-lg-1-2,
.sds_g-landscape-lg-1-24,
.sds_g-landscape-lg-1-3,
.sds_g-landscape-lg-1-4,
.sds_g-landscape-lg-1-5,
.sds_g-landscape-lg-1-6,
.sds_g-landscape-lg-1-7,
.sds_g-landscape-lg-1-8,
.sds_g-landscape-lg-1-9,
.sds_g-landscape-lg-10-24,
.sds_g-landscape-lg-11-12,
.sds_g-landscape-lg-11-24,
.sds_g-landscape-lg-12-24,
.sds_g-landscape-lg-13-24,
.sds_g-landscape-lg-14-24,
.sds_g-landscape-lg-15-24,
.sds_g-landscape-lg-16-24,
.sds_g-landscape-lg-17-24,
.sds_g-landscape-lg-18-24,
.sds_g-landscape-lg-19-24,
.sds_g-landscape-lg-2-24,
.sds_g-landscape-lg-2-3,
.sds_g-landscape-lg-2-5,
.sds_g-landscape-lg-2-7,
.sds_g-landscape-lg-2-9,
.sds_g-landscape-lg-20-24,
.sds_g-landscape-lg-21-24,
.sds_g-landscape-lg-22-24,
.sds_g-landscape-lg-23-24,
.sds_g-landscape-lg-24-24,
.sds_g-landscape-lg-3-24,
.sds_g-landscape-lg-3-4,
.sds_g-landscape-lg-3-5,
.sds_g-landscape-lg-3-7,
.sds_g-landscape-lg-3-8,
.sds_g-landscape-lg-4-24,
.sds_g-landscape-lg-4-5,
.sds_g-landscape-lg-4-7,
.sds_g-landscape-lg-4-9,
.sds_g-landscape-lg-5-12,
.sds_g-landscape-lg-5-24,
.sds_g-landscape-lg-5-5,
.sds_g-landscape-lg-5-6,
.sds_g-landscape-lg-5-7,
.sds_g-landscape-lg-5-8,
.sds_g-landscape-lg-5-9,
.sds_g-landscape-lg-6-24,
.sds_g-landscape-lg-6-7,
.sds_g-landscape-lg-7-12,
.sds_g-landscape-lg-7-24,
.sds_g-landscape-lg-7-8,
.sds_g-landscape-lg-7-9,
.sds_g-landscape-lg-8-24,
.sds_g-landscape-lg-8-9,
.sds_g-landscape-lg-9-24 {
  display: inline-block;
  zoom: 1;
  vertical-align: top;
}
@media (min-width: 768px) and (min-aspect-ratio: 1 / 1), (min-width: 1024px) {
  .sds_g-landscape-lg-1-24 {
    width: 4.1667%;
  }
  .sds_g-landscape-lg-1-12,
  .sds_g-landscape-lg-2-24 {
    width: 8.3333%;
  }
  .sds_g-landscape-lg-1-11 {
    width: 9.0909%;
  }
  .sds_g-landscape-lg-1-10 {
    width: 10%;
  }
  .sds_g-landscape-lg-1-9 {
    width: 11.1111%;
  }
  .sds_g-landscape-lg-1-8,
  .sds_g-landscape-lg-3-24 {
    width: 12.5%;
  }
  .sds_g-landscape-lg-1-7 {
    width: 14.2857%;
  }
  .sds_g-landscape-lg-1-6,
  .sds_g-landscape-lg-4-24 {
    width: 16.6667%;
  }
  .sds_g-landscape-lg-1-5 {
    width: 20%;
  }
  .sds_g-landscape-lg-5-24 {
    width: 20.8333%;
  }
  .sds_g-landscape-lg-2-9 {
    width: 22.2222%;
  }
  .sds_g-landscape-lg-1-4,
  .sds_g-landscape-lg-6-24 {
    width: 25%;
  }
  .sds_g-landscape-lg-2-7 {
    width: 28.5714%;
  }
  .sds_g-landscape-lg-7-24 {
    width: 29.1667%;
  }
  .sds_g-landscape-lg-1-3,
  .sds_g-landscape-lg-8-24 {
    width: 33.3333%;
  }
  .sds_g-landscape-lg-3-8,
  .sds_g-landscape-lg-9-24 {
    width: 37.5%;
  }
  .sds_g-landscape-lg-2-5 {
    width: 40%;
  }
  .sds_g-landscape-lg-10-24,
  .sds_g-landscape-lg-5-12 {
    width: 41.6667%;
  }
  .sds_g-landscape-lg-3-7 {
    width: 42.8571%;
  }
  .sds_g-landscape-lg-4-9 {
    width: 44.4444%;
  }
  .sds_g-landscape-lg-11-24 {
    width: 45.8333%;
  }
  .sds_g-landscape-lg-1-2,
  .sds_g-landscape-lg-12-24 {
    width: 50%;
  }
  .sds_g-landscape-lg-13-24 {
    width: 54.1667%;
  }
  .sds_g-landscape-lg-5-9 {
    width: 55.5556%;
  }
  .sds_g-landscape-lg-4-7 {
    width: 57.1429%;
  }
  .sds_g-landscape-lg-14-24,
  .sds_g-landscape-lg-7-12 {
    width: 58.3333%;
  }
  .sds_g-landscape-lg-3-5 {
    width: 60%;
  }
  .sds_g-landscape-lg-15-24,
  .sds_g-landscape-lg-5-8 {
    width: 62.5%;
  }
  .sds_g-landscape-lg-16-24,
  .sds_g-landscape-lg-2-3 {
    width: 66.6667%;
  }
  .sds_g-landscape-lg-17-24 {
    width: 70.8333%;
  }
  .sds_g-landscape-lg-5-7 {
    width: 71.4286%;
  }
  .sds_g-landscape-lg-18-24,
  .sds_g-landscape-lg-3-4 {
    width: 75%;
  }
  .sds_g-landscape-lg-7-9 {
    width: 77.7778%;
  }
  .sds_g-landscape-lg-19-24 {
    width: 79.1667%;
  }
  .sds_g-landscape-lg-4-5 {
    width: 80%;
  }
  .sds_g-landscape-lg-20-24,
  .sds_g-landscape-lg-5-6 {
    width: 83.3333%;
  }
  .sds_g-landscape-lg-6-7 {
    width: 85.7143%;
  }
  .sds_g-landscape-lg-21-24,
  .sds_g-landscape-lg-7-8 {
    width: 87.5%;
  }
  .sds_g-landscape-lg-8-9 {
    width: 88.8889%;
  }
  .sds_g-landscape-lg-11-12,
  .sds_g-landscape-lg-22-24 {
    width: 91.6667%;
  }
  .sds_g-landscape-lg-23-24 {
    width: 95.8333%;
  }
  .sds_g-landscape-lg-1,
  .sds_g-landscape-lg-1-1,
  .sds_g-landscape-lg-24-24,
  .sds_g-landscape-lg-5-5 {
    width: 100%;
  }
}
.button-base,
.button_primary,
.button_primary_sm,
.button_secondary,
.button_secondary_sm,
.button_tertiary,
.sds_button-base,
.sds_button_primary,
.sds_button_primary_sm,
.sds_button_secondary,
.sds_button_secondary--flat,
.sds_button_secondary_sm,
.sds_button_tertiary,
.sds_button_tertiary--flat {
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  border-radius: 0;
  width: 100%;
  display: block;
  border: 0;
  text-align: center;
  cursor: pointer;
  text-transform: uppercase;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  -webkit-transition: all 0.2s ease;
  transition: all 0.2s ease;
}
.button_primary,
.button_primary_sm,
.button_secondary_sm,
.sds_button_primary,
.sds_button_primary_sm,
.sds_button_secondary_sm {
  background-color: #333;
}
.button_primary:disabled,
.button_primary_sm:disabled,
.button_secondary_sm:disabled,
.disabled.button_primary,
.disabled.button_primary_sm,
.disabled.button_secondary_sm,
.disabled.sds_button_primary,
.disabled.sds_button_primary_sm,
.disabled.sds_button_secondary_sm,
.sds_button_primary:disabled,
.sds_button_primary_sm:disabled,
.sds_button_secondary_sm:disabled {
  background-color: #c2c2c2;
  background-color: rgba(51, 51, 51, 0.3);
}
.button_inline,
.sds_button_inline {
  display: inline;
  width: auto;
}
.button_primary,
.sds_button_primary {
  text-transform: uppercase;
  letter-spacing: 0.1em;
  font-weight: 600;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  color: #fff;
  font-family: sofia-webfont, Helvetica, Arial, Roboto, sans-serif;
  -webkit-font-variant-ligatures: none;
  font-variant-ligatures: none;
  font-size: 1.2rem;
  padding: 0.52em;
}
.button_primary:hover,
.sds_button_primary:hover {
  background-color: #666;
}
.button_primary.disabled,
.disabled.add-to-bag,
.sds_button_primary.disabled {
  cursor: default;
}
.button_secondary,
.sds_button_secondary {
  text-transform: uppercase;
  letter-spacing: 0.1em;
  font-weight: 600;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  color: #fff;
  font-family: sofia-webfont, Helvetica, Arial, Roboto, sans-serif;
  -webkit-font-variant-ligatures: none;
  font-variant-ligatures: none;
  font-size: 1.2rem;
  background-color: #666;
  padding: 0.52em;
}
.button_secondary:hover,
.sds_button_secondary:hover {
  background-color: #8c8c8c;
}
.button_tertiary,
.sds_button_tertiary {
  padding-left: 1rem;
  padding-right: 1rem;
  font-family: sofia-webfont, Helvetica, Arial, Roboto, sans-serif;
  font-weight: 600;
  -webkit-font-variant-ligatures: none;
  font-variant-ligatures: none;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 81.25%;
  padding-top: 0.655em;
  padding-bottom: 0.655em;
  color: #333;
  background-color: #e0e0e0;
}
.button_tertiary:hover,
.sds_button_tertiary:hover {
  color: #666;
  background-color: #e8e8e8;
}
.button_primary_sm,
.sds_button_primary_sm {
  line-height: 1.43;
  font-weight: 300;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  color: #fff;
  font-family: sofia-webfont, Helvetica, Arial, Roboto, sans-serif;
  -webkit-font-variant-ligatures: none;
  font-variant-ligatures: none;
  font-size: 0.933rem;
  font-family: sofia-webfont, Helvetica, Arial, Roboto, sans-serif;
  font-weight: 600;
  font-variant-ligatures: none;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  padding: 0.5em;
}
@media (min-width: 768px) {
  .button_primary_sm,
  .sds_button_primary_sm {
    font-weight: 600;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    -webkit-font-variant-ligatures: none;
    font-variant-ligatures: none;
    font-family: sofia-webfont, Helvetica, Arial, Roboto, sans-serif;
    font-size: 1.067rem;
    color: #fff;
    font-family: sofia-webfont, Helvetica, Arial, Roboto, sans-serif;
    font-weight: 600;
    font-variant-ligatures: none;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }
}
.button_secondary_sm,
.sds_button_secondary_sm {
  line-height: 1.43;
  font-weight: 300;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  color: #fff;
  font-family: sofia-webfont, Helvetica, Arial, Roboto, sans-serif;
  -webkit-font-variant-ligatures: none;
  font-variant-ligatures: none;
  font-size: 0.933rem;
  font-family: sofia-webfont, Helvetica, Arial, Roboto, sans-serif;
  font-weight: 600;
  font-variant-ligatures: none;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: #666;
  padding: 0.5em;
}
.sds_button_secondary--flat {
  font-size: 81.25%;
  background-color: transparent;
  text-transform: uppercase;
  letter-spacing: 0.1em;
  font-weight: 600;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  color: #333;
  font-family: sofia-webfont, Helvetica, Arial, Roboto, sans-serif;
  -webkit-font-variant-ligatures: none;
  font-variant-ligatures: none;
  font-size: 1.2rem;
  padding: 0.52em;
}
.sds_button_secondary--flat:hover {
  color: #666;
}
.sds_button_tertiary--flat {
  padding: 1.2em 1em;
  font-size: 81.25%;
  background-color: transparent;
  font-family: sofia-webfont, Helvetica, Arial, Roboto, sans-serif;
  font-weight: 600;
  -webkit-font-variant-ligatures: none;
  font-variant-ligatures: none;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  color: #333;
}
.sds_button_tertiary--flat:hover {
  color: #666;
}
.sds_button_cat-page-filter {
  width: 70%;
  margin: 0 auto;
}
@media (min-width: 430px) {
  .button_cat-page-filter,
  .sds_button_cat-page-filter {
    width: 50%;
  }
}
.sds-cb_button-primary,
.sds-cb_button-secondary,
.sds-cb_button-secondary--flat,
.sds-cb_button-secondary--outline,
.sds-cb_button-tertiary,
.sds-cb_button-tertiary--flat,
.sds-cb_button-tertiary--outline {
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  border-radius: 0;
  width: 100%;
  display: block;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  text-transform: uppercase;
  text-align: center;
  cursor: pointer;
  -webkit-transition: all 0.2s ease;
  transition: all 0.2s ease;
  padding: 0.5rem;
  font-family: 'Source Sans Pro', Helvetica, Arial, Roboto, sans-serif;
  font-weight: 600;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 1rem;
  line-height: 1.5;
  color: #333;
}
.sds-cb_button-primary {
  background-color: #f43d00;
  border: 2px solid transparent;
  color: #fff;
}
.sds-cb_button-primary:hover {
  background-color: #f76e40;
  color: #fff;
}
.sds-cb_button-secondary {
  background-color: #333;
  border: 2px solid transparent;
  color: #fff;
}
.sds-cb_button-secondary:hover {
  background-color: #666;
  color: #fff;
}
.sds-cb_button-secondary--outline {
  background-color: transparent;
  border: 2px solid #333;
  color: #333;
}
.sds-cb_button-secondary--outline:hover {
  background-color: rgba(255, 255, 255, 0.25);
  color: #666;
}
.sds-cb_button-secondary--outline:hover {
  border: 2px solid #666;
}
.sds-cb_button-secondary--flat {
  background-color: transparent;
  border: 2px solid transparent;
  color: #0466ca;
}
.sds-cb_button-secondary--flat:hover {
  background-color: rgba(255, 255, 255, 0.25);
  color: #438cd7;
}
.sds-cb_button-tertiary {
  background-color: #333;
  border: 2px solid transparent;
  color: #fff;
}
.sds-cb_button-tertiary:hover {
  background-color: #666;
  color: #fff;
}
.sds-cb_button-tertiary--outline {
  background-color: transparent;
  border: 2px solid #333;
  color: #333;
}
.sds-cb_button-tertiary--outline:hover {
  background-color: rgba(255, 255, 255, 0.25);
  color: #666;
}
.sds-cb_button-tertiary--outline:hover {
  border: 2px solid #666;
}
.sds-cb_button-tertiary--flat {
  background-color: transparent;
  border: 2px solid transparent;
  color: #0466ca;
  font-family: 'Source Sans Pro', Helvetica, Arial, Roboto, sans-serif;
  font-weight: 600;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
.sds-cb_button-tertiary--flat:hover {
  background-color: rgba(255, 255, 255, 0.25);
  color: #438cd7;
}
.sds-cb_button_primary_a {
  font-family: 'Source Sans Pro', Helvetica, Arial, Roboto, sans-serif;
  font-weight: 600;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  font-size: 100%;
  line-height: 1;
  letter-spacing: 0.05em;
  text-align: center;
  text-transform: uppercase;
  color: #fff;
  background: #f43d00;
  padding: 1em;
  border: 0;
  display: block;
  width: 100%;
  outline: 0;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
}
.sds-cb_button_primary_a:active,
.sds-cb_button_primary_a:hover {
  background: #c82828;
}
.sds-cb_button_primary_a:disabled {
  cursor: default;
  opacity: 0.25;
}
.sds-cb_button_flat {
  font-family: 'Source Sans Pro', Helvetica, Arial, Roboto, sans-serif;
  font-weight: 600;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  letter-spacing: 0.05em;
  text-align: center;
  text-decoration: none;
  text-transform: uppercase;
  color: #0466ca;
  background: 0;
  padding: 1em;
  border: 0;
  display: block;
  width: 100%;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
}
.sds-cb_button_flat:active,
.sds-cb_button_flat:focus,
.sds-cb_button_flat:hover {
  color: shade(#0466ca, 30%);
}
.sds-cb_button_flat:disabled {
  cursor: default;
  opacity: 0.25;
}
.button-group,
.sds_button-group {
  margin: 0 -0.5em;
  padding: 0 0.5em;
}
.button-group [class*='button'],
.button-group [class*='sds_button'],
.sds_button-group [class*='button'],
.sds_button-group [class*='sds_button'] {
  margin: 0.5em 0.5em 0.5em 0;
}
.button-group--pair [class*='button'],
.sds_button-group--pair [class*='button'] {
  display: inline-block;
  width: 48%;
  width: calc(50% - 0.5rem);
}
.button-group--pair [class*='button']:last-child,
.sds_button-group--pair [class*='button']:last-child {
  margin-right: 0;
}
.button_inline,
.sds_button_inline {
  display: inline;
  width: auto;
}
.categoryNav .cat-page--header .button_cat-page-filter,
.sds-cb_btn,
.sds_btn {
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  border-radius: 0;
  display: inline-block;
  font-size: 1rem;
  line-height: normal;
  text-align: center;
  text-transform: uppercase;
  padding: 0.5em 0.8em;
  cursor: pointer;
  white-space: nowrap;
  vertical-align: middle;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  border: 2px solid transparent;
  font-family: sofia-webfont, Helvetica, Arial, Roboto, sans-serif;
  font-weight: 600;
  -webkit-font-variant-ligatures: none;
  font-variant-ligatures: none;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  color: #fff;
  background-color: #000;
}
.sds-cb_btn--color-wh,
.sds_btn--color-wh {
  color: #000;
  background-color: #fff;
}
.sds-cb_btn--outline,
.sds_btn--outline {
  color: #000;
  background-color: transparent;
  border-color: #000;
}
.sds-cb_btn--outline.sds-cb_btn--color-wh,
.sds-cb_btn--outline.sds_btn--color-wh,
.sds_btn--outline.sds-cb_btn--color-wh,
.sds_btn--outline.sds_btn--color-wh {
  color: #fff;
  background-color: transparent;
  border-color: #fff;
}
.categoryNav .cat-page--header .button_cat-page-filter,
.sds-cb_btn--border,
.sds_btn--border {
  color: #000;
  background-color: #fff;
  border-color: #000;
}
.categoryNav .cat-page--header .sds-cb_btn--color-wh.button_cat-page-filter,
.categoryNav .cat-page--header .sds_btn--color-wh.button_cat-page-filter,
.sds-cb_btn--border.sds-cb_btn--color-wh,
.sds-cb_btn--border.sds_btn--color-wh,
.sds_btn--border.sds-cb_btn--color-wh,
.sds_btn--border.sds_btn--color-wh {
  color: #fff;
  background-color: #000;
  border-color: #fff;
}
.sds-cb_btn--underline,
.sds_btn--underline {
  padding-left: 0;
  padding-right: 0;
  border-left: none;
  border-right: 0;
  background-color: transparent;
  border-bottom: 2px solid currentColor;
  color: #000;
}
.sds-cb_btn--underline.sds-cb_btn--color-wh,
.sds-cb_btn--underline.sds_btn--color-wh,
.sds_btn--underline.sds-cb_btn--color-wh,
.sds_btn--underline.sds_btn--color-wh {
  color: #fff;
  background-color: transparent;
}
.sds-cb_btn--flat,
.sds_btn--flat {
  position: relative;
  border: 0;
  background-color: transparent;
  color: #000;
}
.sds-cb_btn--flat::after,
.sds_btn--flat::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 0.8em;
  right: 0.8em;
  background-color: transparent;
  display: block;
  height: 2px;
}
.sds-cb_btn--flat.sds-cb_btn--color-wh,
.sds-cb_btn--flat.sds_btn--color-wh,
.sds_btn--flat.sds-cb_btn--color-wh,
.sds_btn--flat.sds_btn--color-wh {
  color: #fff;
  background-color: transparent;
}
.sds-cb_btn--border-thin,
.sds_btn--border-thin {
  border-width: 1px;
}
.sds-cb_btn--border-thin::after,
.sds_btn--border-thin::after {
  height: 1px;
  bottom: -1px;
}
.sds-cb_btn--border-thick,
.sds_btn--border-thick {
  border-width: 3px;
}
.sds-cb_btn--border-thick::after,
.sds_btn--border-thick::after {
  height: 3px;
  bottom: -3px;
}
.sds-cb_btn--sm,
.sds_btn--sm {
  font-size: 0.8rem;
}
.sds-cb_btn--lg,
.sds_btn--lg {
  font-size: 1.2rem;
}
.sds-cb_btn--xl,
.sds_btn--xl {
  font-size: 1.5rem;
}
.sds-cb_btn--full-width,
.sds_btn--full-width {
  display: block;
  width: 100%;
}
.sds-cb_btn {
  font-family: 'Source Sans Pro', Helvetica, Arial, Roboto, sans-serif;
  font-weight: 600;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
.sds_btn--color-primary {
  color: #fff;
  background-color: #00aebc;
}
.sds_btn--outline.sds-cb_btn--color-primary,
.sds_btn--outline.sds_btn--color-primary {
  color: #00aebc;
  background-color: transparent;
  border-color: #00aebc;
}
.categoryNav .cat-page--header .sds-cb_btn--color-primary.button_cat-page-filter,
.categoryNav .cat-page--header .sds_btn--color-primary.button_cat-page-filter,
.sds_btn--border.sds-cb_btn--color-primary,
.sds_btn--border.sds_btn--color-primary {
  color: #00aebc;
  background-color: #fff;
  border-color: #00aebc;
}
.sds_btn--underline.sds-cb_btn--color-primary,
.sds_btn--underline.sds_btn--color-primary {
  color: #00aebc;
  background-color: transparent;
}
.sds_btn--flat.sds-cb_btn--color-primary,
.sds_btn--flat.sds_btn--color-primary {
  color: #00aebc;
  background-color: transparent;
}
.sds-cb_btn--color-primary {
  color: #fff;
  background-color: #333;
}
.sds-cb_btn--outline.sds-cb_btn--color-primary,
.sds-cb_btn--outline.sds_btn--color-primary {
  color: #333;
  background-color: transparent;
  border-color: #333;
}
.sds-cb_btn--border.sds-cb_btn--color-primary,
.sds-cb_btn--border.sds_btn--color-primary {
  color: #333;
  background-color: #fff;
  border-color: #333;
}
.sds-cb_btn--underline.sds-cb_btn--color-primary,
.sds-cb_btn--underline.sds_btn--color-primary {
  color: #333;
  background-color: transparent;
}
.sds-cb_btn--flat.sds-cb_btn--color-primary,
.sds-cb_btn--flat.sds_btn--color-primary {
  color: #333;
  background-color: transparent;
}
.sds_deprecated.sds_btn--no-hover {
  visibility: visible;
}
.sds_deprecated.sds-cb_btn--no-hover {
  visibility: visible;
}
.cta-primary,
.sds_cta-primary {
  font-family: sofia-webfont, Helvetica, Arial, Roboto, sans-serif;
  font-weight: 600;
  -webkit-font-variant-ligatures: none;
  font-variant-ligatures: none;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  color: #000;
  background: 0;
  border: 1px solid #000;
  text-transform: uppercase;
  padding: 1em;
  font-size: 1em;
}
@media (min-width: 569px) {
  .cta-primary,
  .sds_cta-primary {
    font-size: 87.5%;
  }
}
.cta-primary:focus,
.sds_cta-primary:focus {
  outline: 0;
}
.cta-secondary,
.cta-tertiary,
.sds_cta-secondary,
.sds_cta-tertiary {
  font-family: sofia-webfont, Helvetica, Arial, Roboto, sans-serif;
  font-weight: 600;
  -webkit-font-variant-ligatures: none;
  font-variant-ligatures: none;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  color: #000;
  background: 0;
  border: 0;
  text-transform: uppercase;
  font-size: 1.125em;
}
.cta-secondary::after,
.cta-tertiary::after,
.sds_cta-secondary::after,
.sds_cta-tertiary::after {
  content: '';
  display: inline-block;
  height: 0;
  width: 0;
  border: 0.4em solid transparent;
  border-left-color: currentColor;
  margin-left: 0.35em;
}
.cta-tertiary,
.sds_cta-tertiary {
  font-size: 0.875em;
}
.cta-category,
.sds_cta-category {
  font-family: DINNextLTPro-Condensed, sofia-webfont, Helvetica, Arial, Roboto, sans-serif;
  color: #76777b;
  background: 0;
  border: 0;
  text-transform: uppercase;
  font-size: 1.5em;
}
.button_universal_primary_a,
.sds_button_universal_primary_a {
  font-family: 'Source Sans Pro', Helvetica, Arial, Roboto, sans-serif;
  font-weight: 600;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  font-size: 100%;
  line-height: 1;
  letter-spacing: 0.05em;
  text-align: center;
  text-transform: uppercase;
  color: #fff;
  background: #f43d00;
  padding: 1em;
  border: 0;
  display: block;
  width: 100%;
  outline: 0;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
}
.button_universal_primary_a:active,
.button_universal_primary_a:hover,
.sds_button_universal_primary_a:active,
.sds_button_universal_primary_a:hover {
  background: #c82828;
}
.button_universal_primary_a:disabled,
.sds_button_universal_primary_a:disabled {
  cursor: default;
  opacity: 0.25;
}
.button_universal_flat,
.sds_button_universal_flat {
  font-family: 'Source Sans Pro', Helvetica, Arial, Roboto, sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 0.75rem;
  line-height: 1;
  color: #666;
  font-family: 'Source Sans Pro', Helvetica, Arial, Roboto, sans-serif;
  font-weight: 600;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  letter-spacing: 0.05em;
  text-align: center;
  text-decoration: none;
  text-transform: uppercase;
  color: #0466ca;
  background: 0;
  padding: 1em;
  border: 0;
  display: block;
  width: 100%;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
}
.button_universal_flat:active,
.button_universal_flat:focus,
.button_universal_flat:hover,
.sds_button_universal_flat:active,
.sds_button_universal_flat:focus,
.sds_button_universal_flat:hover {
  color: #011a34;
}
.button_universal_flat:disabled,
.sds_button_universal_flat:disabled {
  cursor: default;
  opacity: 0.25;
}
.sds_field,
.sds_form,
.text-input__field {
  font-family: sofia-webfont, Helvetica, Arial, Roboto, sans-serif;
  font-weight: 300;
  -webkit-font-variant-ligatures: none;
  font-variant-ligatures: none;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
.sds_form {
  margin-bottom: 1rem;
}
.sds_field,
.text-input__field {
  position: relative;
  margin: 0;
  display: block;
}
.sds_field__label,
.sds_field__placeholder,
.text-input__label {
  position: absolute;
  -webkit-transition: all 0.2s ease;
  transition: all 0.2s ease;
}
.sds_field__label,
.text-input__label {
  color: #666;
}
.sds_field__placeholder {
  color: #ccc;
  right: 0;
}
.sds_field__error,
.sds_field__help,
.text-input__field__error {
  display: block;
  font-size: 0.8125rem;
  line-height: 1.2;
  margin-top: 0.25rem;
}
.sds_field__error,
.text-input__field__error {
  color: #d00000;
}
.sds_field__help {
  color: #333;
}
.sds-cb_field,
.sds-cb_form {
  font-family: 'Source Sans Pro', Helvetica, Arial, Roboto, sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
.sds-cb_form {
  margin-bottom: 1rem;
}
.sds-cb_field {
  position: relative;
  margin: 0;
  display: block;
}
.sds-cb_field__label,
.sds-cb_field__placeholder {
  position: absolute;
  -webkit-transition: all 0.2s ease;
  transition: all 0.2s ease;
}
.sds-cb_field__label {
  color: #666;
}
.sds-cb_field__placeholder {
  color: #ccc;
  right: 0;
}
.sds-cb_field__error,
.sds-cb_field__help {
  display: block;
  font-size: 0.8125rem;
  line-height: 1.2;
  margin-top: 0.25rem;
}
.sds-cb_field__error {
  color: #d00000;
}
.sds-cb_field__help {
  color: #333;
}
.sds_fieldset {
  display: block;
  border: 0;
  padding: 0;
  margin: 0;
}
.sds-cb_fieldset {
  display: block;
  border: 0;
  padding: 0;
  margin: 0;
}
.sds_input-a,
.text-input__default {
  font-family: sofia-webfont, Helvetica, Arial, Roboto, sans-serif;
  font-weight: 300;
  -webkit-font-variant-ligatures: none;
  font-variant-ligatures: none;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  padding: 0.375rem 0;
  display: block;
  color: #333;
  width: 100%;
  height: 2.25rem;
  border: 0;
  border-radius: 0;
  border-bottom: 1px solid #666;
  background-repeat: no-repeat;
  background-size: 0;
  background-image: -webkit-gradient(linear, right top, left top, from(transparent), color-stop(0, #333), color-stop(100%, #333), to(transparent));
  background-image: linear-gradient(to left, transparent 0, #333 0, #333 100%, transparent 100%);
  background-position: bottom center;
  font-size: 1.0625rem;
  font-family: sofia-webfont, Helvetica, Arial, Roboto, sans-serif;
  font-weight: 300;
  font-variant-ligatures: none;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  margin-top: 2.125rem;
}
.sds_input-a[type='number'],
.text-input__default[type='number'] {
  -moz-appearance: textfield;
}
.sds_input-a[type='number']::-webkit-inner-spin-button,
.sds_input-a[type='number']::-webkit-outer-spin-button,
.text-input__default[type='number']::-webkit-inner-spin-button,
.text-input__default[type='number']::-webkit-outer-spin-button {
  -webkit-appearance: none;
  margin: 0;
}
.sds_input-a:focus,
.text-input__default:focus {
  outline: 0;
  border-bottom: 1px solid #333;
  -webkit-animation: moving-bar 250ms linear forwards;
  animation: moving-bar 250ms linear forwards;
}
.sds-js_enabled.text-input__default ~ .sds_field__label,
.sds-js_enabled.text-input__default ~ .sds_field__placeholder,
.sds-js_enabled.text-input__default ~ .text-input__label,
.sds_input-a.sds-js_enabled ~ .sds_field__label,
.sds_input-a.sds-js_enabled ~ .sds_field__placeholder,
.sds_input-a.sds-js_enabled ~ .text-input__label {
  top: 0.375rem;
  text-transform: none;
  font-size: 1.0625rem;
}
.sds_has-error.text-input__default ~ .sds_field__label,
.sds_has-error.text-input__default ~ .sds_field__placeholder,
.sds_has-error.text-input__default ~ .text-input__label,
.sds_has-value.text-input__default ~ .sds_field__label,
.sds_has-value.text-input__default ~ .sds_field__placeholder,
.sds_has-value.text-input__default ~ .text-input__label,
.sds_input-a.sds_has-error ~ .sds_field__label,
.sds_input-a.sds_has-error ~ .sds_field__placeholder,
.sds_input-a.sds_has-error ~ .text-input__label,
.sds_input-a.sds_has-value ~ .sds_field__label,
.sds_input-a.sds_has-value ~ .sds_field__placeholder,
.sds_input-a.sds_has-value ~ .text-input__label,
.sds_input-a:focus ~ .sds_field__label,
.sds_input-a:focus ~ .sds_field__placeholder,
.sds_input-a:focus ~ .text-input__label,
.sds_input-a:not(.sds-js_enabled) ~ .sds_field__label,
.sds_input-a:not(.sds-js_enabled) ~ .sds_field__placeholder,
.sds_input-a:not(.sds-js_enabled) ~ .text-input__label,
.text-input__default:focus ~ .sds_field__label,
.text-input__default:focus ~ .sds_field__placeholder,
.text-input__default:focus ~ .text-input__label,
.text-input__default:not(.sds-js_enabled) ~ .sds_field__label,
.text-input__default:not(.sds-js_enabled) ~ .sds_field__placeholder,
.text-input__default:not(.sds-js_enabled) ~ .text-input__label {
  top: -1.25rem;
  font-size: 0.75rem;
  text-transform: uppercase;
  cursor: pointer;
  font-size: 0.6875rem;
}
.sds_has-value.text-input__default ~ .sds_field__label,
.sds_has-value.text-input__default ~ .text-input__label,
.sds_input-a.sds_has-value ~ .sds_field__label,
.sds_input-a.sds_has-value ~ .text-input__label,
.sds_input-a ~ .sds_field__label,
.sds_input-a ~ .text-input__label,
.text-input__default ~ .sds_field__label,
.text-input__default ~ .text-input__label {
  color: #666;
  cursor: text;
}
.sds_input-a:focus ~ .sds_field__label,
.sds_input-a:focus ~ .text-input__label,
.text-input__default:focus ~ .sds_field__label,
.text-input__default:focus ~ .text-input__label {
  color: #333;
}
.sds_has-error.text-input__default,
.sds_input-a.sds_has-error {
  border: 1px solid #d00000;
  padding-left: 0.5rem;
  background-image: -webkit-gradient(linear, right top, left top, from(transparent), color-stop(0, #d00000), color-stop(100%, #d00000), to(transparent));
  background-image: linear-gradient(to left, transparent 0, #d00000 0, #d00000 100%, transparent 100%);
}
.sds_has-error.text-input__default:focus,
.sds_input-a.sds_has-error:focus {
  border-color: #d00000;
}
.sds_has-error.text-input__default:focus ~ .sds_field__label,
.sds_has-error.text-input__default:focus ~ .text-input__label,
.sds_has-error.text-input__default ~ .sds_field__label,
.sds_has-error.text-input__default ~ .text-input__label,
.sds_input-a.sds_has-error:focus ~ .sds_field__label,
.sds_input-a.sds_has-error:focus ~ .text-input__label,
.sds_input-a.sds_has-error ~ .sds_field__label,
.sds_input-a.sds_has-error ~ .text-input__label {
  color: #d00000;
}
.sds_input-a--footnote ~ .sds_field__label::after,
.sds_input-a--footnote ~ .text-input__label::after {
  content: '\271D\FE0E';
  position: absolute;
  font-size: 0.5rem;
  top: -0.0125rem;
  right: -0.5rem;
}
.sds_input-a:disabled,
.text-input__default:disabled {
  color: #ccc;
  border-color: #ccc;
  background-color: #fff;
}
.sds_input-a:disabled ~ .sds_field__label,
.sds_input-a:disabled ~ .sds_field__placeholder,
.sds_input-a:disabled ~ .text-input__label,
.text-input__default:disabled ~ .sds_field__label,
.text-input__default:disabled ~ .sds_field__placeholder,
.text-input__default:disabled ~ .text-input__label {
  color: #ccc;
  border-color: #ccc;
  pointer-events: none;
  cursor: default;
}
.sds_text-input_basic,
.text-input_basic {
  margin-bottom: 1rem;
}
.sds_text-input_basic--label-text,
.text-input_basic--label-text {
  text-transform: uppercase;
  font-weight: 600;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  color: #666;
  font-family: sofia-webfont, Helvetica, Arial, Roboto, sans-serif;
  -webkit-font-variant-ligatures: none;
  font-variant-ligatures: none;
  font-size: 0.84rem;
  letter-spacing: 0.06em;
  font-family: sofia-webfont, Helvetica, Arial, Roboto, sans-serif;
  font-weight: 300;
  font-variant-ligatures: none;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 69%;
}
.sds_text-input_basic--optional-flag,
.text-input_basic--optional-flag {
  float: right;
  text-transform: uppercase;
  font-weight: 600;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  color: #666;
  font-family: sofia-webfont, Helvetica, Arial, Roboto, sans-serif;
  -webkit-font-variant-ligatures: none;
  font-variant-ligatures: none;
  font-size: 0.84rem;
  letter-spacing: 0.06em;
  font-family: sofia-webfont, Helvetica, Arial, Roboto, sans-serif;
  font-weight: 300;
  font-variant-ligatures: none;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 69%;
  margin-top: 0.3em;
}
.sds_text-input_basic--input,
.text-input_basic--input {
  outline: 0;
  padding: 0.5em;
  border: 1px solid #666;
  width: 100%;
  border: 1px solid #a7a9ac;
  line-height: 1.43;
  font-weight: 300;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  color: #666;
  font-family: sofia-webfont, Helvetica, Arial, Roboto, sans-serif;
  -webkit-font-variant-ligatures: none;
  font-variant-ligatures: none;
  font-size: 0.933rem;
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  border-radius: 0;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
}
.sds_text-input_basic--input:active,
.sds_text-input_basic--input:focus,
.text-input_basic--input:active,
.text-input_basic--input:focus {
  border-color: #333;
  -webkit-box-shadow: 0 0 0 1px #333;
  box-shadow: 0 0 0 1px #333;
}
.input-note,
.sds_input-note {
  margin-bottom: 2rem;
}
.input-note--text,
.sds_input-note--text {
  text-transform: uppercase;
  font-weight: 600;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  color: #666;
  font-family: sofia-webfont, Helvetica, Arial, Roboto, sans-serif;
  -webkit-font-variant-ligatures: none;
  font-variant-ligatures: none;
  font-size: 0.84rem;
  letter-spacing: 0.06em;
  font-family: sofia-webfont, Helvetica, Arial, Roboto, sans-serif;
  font-weight: 300;
  font-variant-ligatures: none;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-transform: none;
  letter-spacing: normal;
}
.sds-cb_input-a {
  font-family: sofia-webfont, Helvetica, Arial, Roboto, sans-serif;
  font-weight: 300;
  -webkit-font-variant-ligatures: none;
  font-variant-ligatures: none;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  padding: 0.375rem 0;
  display: block;
  color: #333;
  width: 100%;
  height: 2.25rem;
  border: 0;
  border-radius: 0;
  border-bottom: 1px solid #666;
  background-repeat: no-repeat;
  background-size: 0;
  background-image: -webkit-gradient(linear, right top, left top, from(transparent), color-stop(0, #333), color-stop(100%, #333), to(transparent));
  background-image: linear-gradient(to left, transparent 0, #333 0, #333 100%, transparent 100%);
  background-position: bottom center;
  font-size: 1.0625rem;
  font-family: 'Source Sans Pro', Helvetica, Arial, Roboto, sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  margin-top: 2.375rem;
  font-size: 1.25rem;
}
.sds-cb_input-a[type='number'] {
  -moz-appearance: textfield;
}
.sds-cb_input-a[type='number']::-webkit-inner-spin-button,
.sds-cb_input-a[type='number']::-webkit-outer-spin-button {
  -webkit-appearance: none;
  margin: 0;
}
.sds-cb_input-a:focus {
  outline: 0;
  border-color: #0466ca;
  -webkit-animation: moving-bar 250ms linear forwards;
  animation: moving-bar 250ms linear forwards;
}
.sds-cb_input-a.sds-js_enabled ~ .sds-cb_field__label,
.sds-cb_input-a.sds-js_enabled ~ .sds-cb_field__placeholder {
  top: 0.375rem;
  text-transform: none;
  font-size: 1.0625rem;
  cursor: text;
}
.sds-cb_input-a.sds_has-error ~ .sds-cb_field__label,
.sds-cb_input-a.sds_has-error ~ .sds-cb_field__placeholder,
.sds-cb_input-a.sds_has-value ~ .sds-cb_field__label,
.sds-cb_input-a.sds_has-value ~ .sds-cb_field__placeholder,
.sds-cb_input-a:focus ~ .sds-cb_field__label,
.sds-cb_input-a:focus ~ .sds-cb_field__placeholder,
.sds-cb_input-a:not(.sds-js_enabled) ~ .sds-cb_field__label,
.sds-cb_input-a:not(.sds-js_enabled) ~ .sds-cb_field__placeholder {
  top: -1.25rem;
  font-size: 0.75rem;
  text-transform: uppercase;
  cursor: pointer;
}
.sds-cb_input-a.sds_has-value ~ .sds-cb_field__label {
  color: #666;
}
.sds-cb_input-a:focus ~ .sds-cb_field__label {
  color: #0466ca;
}
.sds-cb_input-a.sds_has-error {
  border: 1px solid #d00000;
  padding-left: 0.5rem;
  background-image: -webkit-gradient(linear, right top, left top, from(transparent), color-stop(0, #d00000), color-stop(100%, #d00000), to(transparent));
  background-image: linear-gradient(to left, transparent 0, #d00000 0, #d00000 100%, transparent 100%);
}
.sds-cb_input-a.sds_has-error:focus {
  border-color: #d00000;
}
.sds-cb_input-a.sds_has-error:focus ~ .sds-cb_field__label,
.sds-cb_input-a.sds_has-error ~ .sds-cb_field__label {
  color: #d00000;
}
.sds-cb_input-a--footnote ~ .sds-cb_field__label::after {
  content: '\271D\FE0E';
  position: absolute;
  font-size: 0.5rem;
  top: -0.0125rem;
  right: -0.5rem;
}
.sds-cb_input-a:disabled {
  color: #ccc;
  border-color: #ccc;
  background-color: #fff;
}
.sds-cb_input-a:disabled ~ .sds-cb_field__label,
.sds-cb_input-a:disabled ~ .sds-cb_field__placeholder {
  color: #ccc;
  border-color: #ccc;
  pointer-events: none;
}
.sds_form {
  margin-bottom: 1rem;
  font-family: sofia-webfont, Helvetica, Arial, Roboto, sans-serif;
  font-weight: 300;
  -webkit-font-variant-ligatures: none;
  font-variant-ligatures: none;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
.sds_form__field-container {
  width: 100%;
  margin: 1.5rem 0;
}
.sds_form__field-container--invalid .sds_form__label {
  color: #d00000;
}
.sds_form__field-container--invalid .sds_form__input,
.sds_form__field-container--invalid .sds_form__input:focus {
  border: 2px solid #d00000;
  margin: -1px 0;
}
.sds_form__flag,
.sds_form__label {
  font-family: sofia-webfont, Helvetica, Arial, Roboto, sans-serif;
  font-weight: 300;
  -webkit-font-variant-ligatures: none;
  font-variant-ligatures: none;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-transform: uppercase;
  font-size: 69%;
  display: inline-block;
}
.sds_form__label {
  width: 66%;
  text-align: left;
}
.sds_form__flag {
  width: 32%;
  text-align: right;
}
.sds_form__input {
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  padding: 0.5rem;
  border: 1px solid #ccc;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  color: #666;
  width: 100%;
}
.sds_form__input:focus {
  outline: 0;
  border: 2px solid #0466ca;
  margin: -1px 0;
}
.sds_form__note {
  margin-top: -1rem;
}
.sds_form__note__text {
  font-family: sofia-webfont, Helvetica, Arial, Roboto, sans-serif;
  font-weight: 300;
  -webkit-font-variant-ligatures: none;
  font-variant-ligatures: none;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 0.8rem;
}
.sds_textarea-a {
  font-family: sofia-webfont, Helvetica, Arial, Roboto, sans-serif;
  font-weight: 300;
  -webkit-font-variant-ligatures: none;
  font-variant-ligatures: none;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  position: relative;
  overflow: hidden;
  overflow-y: scroll;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  padding: 2rem 0.5rem 1rem;
  width: 100%;
  border: solid 1px #999;
  color: #000;
  background-color: #fff;
  resize: vertical;
  min-height: 5rem;
  margin-top: 1rem;
  font-size: 1.0625rem;
}
.sds_textarea-a ~ .sds_field__label,
.sds_textarea-a ~ .sds_field__placeholder,
.sds_textarea-a ~ .text-input__label {
  padding: 0.5rem;
  position: absolute;
  top: 1.0625rem;
  text-transform: none;
  font-size: 1.0625rem;
}
.sds_textarea-a.sds_has-value ~ .sds_field__label,
.sds_textarea-a.sds_has-value ~ .text-input__label,
.sds_textarea-a ~ .sds_field__label,
.sds_textarea-a ~ .text-input__label {
  color: #666;
  background-color: #fff;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  width: calc(100% - 4px);
  left: 0.125rem;
}
.sds_textarea-a::-webkit-input-placeholder {
  color: transparent;
  -webkit-transition: all 0.3s ease;
  transition: all 0.3s ease;
}
.sds_textarea-a::-ms-input-placeholder {
  color: transparent;
  -webkit-transition: all 0.3s ease;
  transition: all 0.3s ease;
}
.sds_textarea-a::placeholder {
  color: transparent;
  -webkit-transition: all 0.3s ease;
  transition: all 0.3s ease;
}
.sds_textarea-a.sds_has-error ~ .sds_field__label,
.sds_textarea-a.sds_has-error ~ .sds_field__placeholder,
.sds_textarea-a.sds_has-error ~ .text-input__label,
.sds_textarea-a.sds_has-value ~ .sds_field__label,
.sds_textarea-a.sds_has-value ~ .sds_field__placeholder,
.sds_textarea-a.sds_has-value ~ .text-input__label,
.sds_textarea-a:focus ~ .sds_field__label,
.sds_textarea-a:focus ~ .sds_field__placeholder,
.sds_textarea-a:focus ~ .text-input__label,
.sds_textarea-a:not(.sds-js_enabled) ~ .sds_field__label,
.sds_textarea-a:not(.sds-js_enabled) ~ .sds_field__placeholder,
.sds_textarea-a:not(.sds-js_enabled) ~ .text-input__label {
  text-transform: uppercase;
  font-size: 0.75rem;
  font-size: 0.6875rem;
}
.sds_textarea-a:focus {
  outline: 0;
  border-color: #333;
  -webkit-box-shadow: 0 0 0 1px #333;
  box-shadow: 0 0 0 1px #333;
}
.sds_textarea-a:focus ~ .sds_field__label,
.sds_textarea-a:focus ~ .text-input__label {
  color: #333;
}
.sds_textarea-a:focus::-webkit-input-placeholder {
  color: #ccc;
}
.sds_textarea-a:focus::-ms-input-placeholder {
  color: #ccc;
}
.sds_textarea-a:focus::placeholder {
  color: #ccc;
}
.sds_textarea-a.sds_has-error {
  border-color: transparent;
  -webkit-box-shadow: 0 0 0 2px #d00000;
  box-shadow: 0 0 0 2px #d00000;
  -webkit-appearance: none;
}
.sds_textarea-a.sds_has-error:focus ~ .sds_field__label,
.sds_textarea-a.sds_has-error:focus ~ .text-input__label,
.sds_textarea-a.sds_has-error ~ .sds_field__label,
.sds_textarea-a.sds_has-error ~ .text-input__label {
  color: #d00000;
}
.sds_textarea-a:disabled {
  color: #ccc;
  border-color: #ccc;
  background-color: #fff;
}
.sds_textarea-a:disabled ~ .sds_field__help,
.sds_textarea-a:disabled ~ .sds_field__label,
.sds_textarea-a:disabled ~ .sds_field__placeholder,
.sds_textarea-a:disabled ~ .text-input__label {
  color: #ccc;
  border-color: #ccc;
  pointer-events: none;
  cursor: default;
}
.sds_textarea-a:disabled::-webkit-input-placeholder {
  color: transparent;
}
.sds_textarea-a:disabled::-ms-input-placeholder {
  color: transparent;
}
.sds_textarea-a:disabled::placeholder {
  color: transparent;
}
.sds-cb_textarea-a {
  font-family: 'Source Sans Pro', Helvetica, Arial, Roboto, sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  position: relative;
  overflow: auto;
  overflow-y: scroll;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  padding: 2rem 0.5rem 1rem;
  width: 100%;
  font-size: 1rem;
  border: solid 1px #999;
  color: #000;
  background-color: #fff;
  resize: vertical;
  min-height: 5rem;
  margin-top: 1rem;
}
.sds-cb_textarea-a ~ .sds-cb_field__label,
.sds-cb_textarea-a ~ .sds-cb_field__placeholder {
  padding: 0.5rem;
  position: absolute;
  top: 1.0625rem;
}
.sds-cb_textarea-a ~ .sds-cb_field__placeholder {
  right: 1rem;
}
.sds-cb_textarea-a.sds_has-value ~ .sds-cb_field__label,
.sds-cb_textarea-a ~ .sds-cb_field__label {
  color: #666;
  background-color: #fff;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  width: calc(100% - 6px);
  left: 0.125rem;
}
.sds-cb_textarea-a::-webkit-input-placeholder {
  color: transparent;
  -webkit-transition: all 0.3s ease;
  transition: all 0.3s ease;
}
.sds-cb_textarea-a::-ms-input-placeholder {
  color: transparent;
  -webkit-transition: all 0.3s ease;
  transition: all 0.3s ease;
}
.sds-cb_textarea-a::placeholder {
  color: transparent;
  -webkit-transition: all 0.3s ease;
  transition: all 0.3s ease;
}
.sds-cb_textarea-a.sds_has-error ~ .sds-cb_field__label,
.sds-cb_textarea-a.sds_has-error ~ .sds-cb_field__placeholder,
.sds-cb_textarea-a.sds_has-value ~ .sds-cb_field__label,
.sds-cb_textarea-a.sds_has-value ~ .sds-cb_field__placeholder,
.sds-cb_textarea-a:focus ~ .sds-cb_field__label,
.sds-cb_textarea-a:focus ~ .sds-cb_field__placeholder,
.sds-cb_textarea-a:not(.sds-js_enabled) ~ .sds-cb_field__label,
.sds-cb_textarea-a:not(.sds-js_enabled) ~ .sds-cb_field__placeholder {
  text-transform: uppercase;
  font-size: 0.75rem;
  background-color: #fff;
}
.sds-cb_textarea-a:focus {
  outline: 0;
  border-color: transparent;
  -webkit-box-shadow: 0 0 0 2px #0466ca;
  box-shadow: 0 0 0 2px #0466ca;
}
.sds-cb_textarea-a:focus ~ .sds-cb_field__label {
  color: #0466ca;
}
.sds-cb_textarea-a:focus::-webkit-input-placeholder {
  color: #ccc;
}
.sds-cb_textarea-a:focus::-ms-input-placeholder {
  color: #ccc;
}
.sds-cb_textarea-a:focus::placeholder {
  color: #ccc;
}
.sds-cb_textarea-a.sds_has-error {
  border-color: transparent;
  -webkit-box-shadow: 0 0 0 2px #d00000;
  box-shadow: 0 0 0 2px #d00000;
  -webkit-appearance: none;
}
.sds-cb_textarea-a.sds_has-error:focus ~ .sds-cb_field__label,
.sds-cb_textarea-a.sds_has-error ~ .sds-cb_field__label {
  color: #d00000;
}
.sds-cb_textarea-a:disabled {
  color: #ccc;
  border-color: #ccc;
  background-color: #fff;
}
.sds-cb_textarea-a:disabled ~ .sds-cb_field__help,
.sds-cb_textarea-a:disabled ~ .sds-cb_field__label,
.sds-cb_textarea-a:disabled ~ .sds-cb_field__placeholder {
  color: #ccc;
  border-color: #ccc;
  pointer-events: none;
  cursor: default;
}
.sds-cb_textarea-a:disabled::-webkit-input-placeholder {
  color: transparent;
}
.sds-cb_textarea-a:disabled::-ms-input-placeholder {
  color: transparent;
}
.sds-cb_textarea-a:disabled::placeholder {
  color: transparent;
}
.sds-cb_dropdown-a {
  font-family: 'Source Sans Pro', Helvetica, Arial, Roboto, sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  width: 100%;
  position: relative;
  margin-top: 2.375rem;
}
.sds-cb_dropdown-a .sds_dropdown-a__button {
  font-family: sofia-webfont, Helvetica, Arial, Roboto, sans-serif;
  font-weight: 300;
  -webkit-font-variant-ligatures: none;
  font-variant-ligatures: none;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  border: 0;
  position: relative;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  padding: 0.375rem 0;
  text-align: left;
  width: 100%;
  height: 2.25rem;
  color: #000;
  background-color: #fff;
  font-size: 1.0625rem;
  margin-bottom: 0;
  white-space: nowrap;
  overflow: hidden;
  color: #333;
  border-bottom: 1px solid #666;
  background-repeat: no-repeat;
  background-size: 0;
  background-image: -webkit-gradient(linear, right top, left top, from(transparent), color-stop(0, #333), color-stop(100%, #333), to(transparent));
  background-image: linear-gradient(to left, transparent 0, #333 0, #333 100%, transparent 100%);
  background-position: bottom center;
}
.sds-cb_dropdown-a .sds_dropdown-a__button[aria-disabled] {
  outline: 0;
}
.sds-cb_dropdown-a .sds_dropdown-a__button.sds_remove-focus:focus,
.sds-cb_dropdown-a .sds_dropdown-a__button.sds_remove-focus[aria-expanded='true'] {
  outline: 0;
  -webkit-animation: moving-bar 250ms linear forwards;
  animation: moving-bar 250ms linear forwards;
}
.sds-cb_dropdown-a .sds_dropdown-a__button.sds_remove-focus:focus:not([aria-disabled]),
.sds-cb_dropdown-a .sds_dropdown-a__button.sds_remove-focus[aria-expanded='true']:not([aria-disabled]) {
  border-color: #000;
}
.sds-cb_dropdown-a .sds_dropdown-a__button::before {
  content: '';
  background: url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='9' height='6' viewBox='0 0 9 6'%3E%3Cpath d='M.003 1.533L1.118.529l3.385 3.047L7.89.529l1.114 1.004-4.5 4.05z' style='fill:%23666;fill-rule:evenodd' /%3E%3C/svg%3E")
    no-repeat;
  width: 1.5rem;
  height: 2rem;
  display: block;
  position: absolute;
  right: 1px;
  top: 1px;
  background-position-x: 8px;
  background-position-y: 14px;
  background-color: #fff;
}
.sds-cb_dropdown-a .sds_dropdown-a__button.sds_init-state {
  color: #666;
}
.sds-cb_dropdown-a .sds_dropdown-a__list {
  -webkit-box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
  overflow-y: auto;
  height: auto;
  max-height: 200px;
  position: absolute;
  width: 100%;
  z-index: 999;
  -webkit-transition: max-height 0.25s ease-in-out;
  transition: max-height 0.25s ease-in-out;
  background-color: #fff;
}
.sds-cb_dropdown-a .sds_dropdown-a__list.sds_is-collapsed {
  height: 0;
  max-height: 0;
  z-index: 888;
}
.sds-cb_dropdown-a .sds_dropdown-a__list:focus {
  outline: 0;
}
.sds-cb_dropdown-a .sds_dropdown-a__list-item {
  cursor: pointer;
  padding: 0.5rem;
  color: #333;
  font-size: 1.0625rem;
}
.sds-cb_dropdown-a .sds_dropdown-a__list-item[aria-selected='true'] {
  background-color: #e5e5e5;
}
.sds-cb_dropdown-a .sds_dropdown-a__list-item.sds_is-focused:not([aria-selected='true']),
.sds-cb_dropdown-a .sds_dropdown-a__list-item:hover:not([aria-selected='true']) {
  background-color: #f2f2f2;
}
.sds-cb_dropdown-a .sds_dropdown-a__list-item:focus {
  outline: 0;
}
.sds-cb_dropdown-a .sds-cb_field__label {
  display: block;
  top: -1.25rem;
  font-size: 0.75rem;
  text-transform: uppercase;
}
.sds-cb_dropdown-a:not(.sds-js_enabled) .sds-cb_field__label {
  position: relative;
  height: 0;
}
.sds-cb_dropdown-a:not(.sds-js_enabled)::after {
  content: '';
  background: url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='9' height='6' viewBox='0 0 9 6'%3E%3Cpath d='M.003 1.533L1.118.529l3.385 3.047L7.89.529l1.114 1.004-4.5 4.05z' style='fill:%23666;fill-rule:evenodd' /%3E%3C/svg%3E")
    no-repeat;
  width: 1.5rem;
  height: 2rem;
  display: block;
  position: absolute;
  right: 1px;
  top: 1px;
  background-position-x: 8px;
  background-position-y: 14px;
  background-color: #fff;
}
.sds-cb_dropdown-a[aria-disabled]:not(.sds-js_enabled)::after {
  background: url("data:image/svg+xml;charset=utf8,%3Csvgxmlns='http://www.w3.org/2000/svg'width='9'height='6'viewBox='0096'%3E%3Cpathd='M.0031.533L1.118.529l3.3853.047L7.89.529l1.1141.004-4.54.05z'style='fill:%23CCC;fill-rule:evenodd'/%3E%3C/svg%3E")
    no-repeat;
  top: 0.85rem;
  right: 0.5rem;
  background-position: 0 0;
  height: 6px;
  width: 9px;
}
.sds-cb_dropdown-a .sds_dropdown-a__button {
  font-family: 'Source Sans Pro', Helvetica, Arial, Roboto, sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 1.125rem;
  background-image: -webkit-gradient(linear, right top, left top, from(transparent), color-stop(0, #0466ca), color-stop(100%, #0466ca), to(transparent));
  background-image: linear-gradient(to left, transparent 0, #0466ca 0, #0466ca 100%, transparent 100%);
}
.sds-cb_dropdown-a .sds_dropdown-a__button.sds_remove-focus:focus,
.sds-cb_dropdown-a .sds_dropdown-a__button.sds_remove-focus[aria-expanded='true'] {
  border-color: #0466ca;
}
.sds-cb_dropdown-a.sds_has-error .sds_dropdown-a__button,
.sds-cb_dropdown-a.sds_has-error select {
  padding-left: 0.25rem;
  bottom: 0;
  border: 1px solid #d00000;
  background-image: -webkit-gradient(linear, right top, left top, from(transparent), color-stop(0, #d00000), color-stop(100%, #d00000), to(transparent));
  background-image: linear-gradient(to left, transparent 0, #d00000 0, #d00000 100%, transparent 100%);
}
.sds-cb_dropdown-a.sds_has-error .sds_dropdown-a__button.sds_remove-focus:focus,
.sds-cb_dropdown-a.sds_has-error .sds_dropdown-a__button.sds_remove-focus[aria-expanded='true'],
.sds-cb_dropdown-a.sds_has-error select.sds_remove-focus:focus,
.sds-cb_dropdown-a.sds_has-error select.sds_remove-focus[aria-expanded='true'] {
  border-color: #d00000;
}
.sds-cb_dropdown-a.sds_has-error .sds-cb_field__label {
  color: #d00000;
}
.sds-cb_dropdown-a.sds_is-disabled .sds_dropdown-a__button {
  color: #ccc;
  border-color: #ccc;
  background-image: none;
}
.sds-cb_dropdown-a.sds_is-disabled .sds_dropdown-a__button:focus {
  outline: 0;
  border-color: initial;
}
.sds-cb_dropdown-a.sds_is-disabled .sds_dropdown-a__button::before {
  background: url("data:image/svg+xml;charset=utf8,%3Csvgxmlns='http://www.w3.org/2000/svg'width='9'height='6'viewBox='0096'%3E%3Cpathd='M.0031.533L1.118.529l3.3853.047L7.89.529l1.1141.004-4.54.05z'style='fill:%23CCC;fill-rule:evenodd'/%3E%3C/svg%3E")
    no-repeat;
  top: 0.85rem;
  right: 0.5rem;
  background-position: 0 0;
  height: 6px;
  width: 9px;
}
.sds-cb_dropdown-a select {
  font-family: sofia-webfont, Helvetica, Arial, Roboto, sans-serif;
  font-weight: 300;
  -webkit-font-variant-ligatures: none;
  font-variant-ligatures: none;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  appearance: none;
  border: 0;
  position: relative;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  padding: 0.375rem 0;
  text-align: left;
  width: 100%;
  height: 2.25rem;
  color: #000;
  background-color: #fff;
  font-size: 1.0625rem;
  margin-bottom: 0;
  white-space: nowrap;
  overflow: hidden;
  color: #333;
  border-bottom: 1px solid #666;
  background-repeat: no-repeat;
  background-size: 0;
  background-image: -webkit-gradient(linear, right top, left top, from(transparent), color-stop(0, #333), color-stop(100%, #333), to(transparent));
  background-image: linear-gradient(to left, transparent 0, #333 0, #333 100%, transparent 100%);
  background-position: bottom center;
  border-radius: 0;
  -webkit-appearance: none;
  -moz-appearance: none;
}
.sds-cb_dropdown-a select[aria-disabled] {
  outline: 0;
}
.sds-cb_dropdown-a select[disabled] {
  color: #ccc;
  border-color: #ccc;
  background-image: none;
}
.sds-cb_dropdown-a select[disabled]:focus {
  outline: 0;
  border-color: initial;
}
.sds_dropdown-a {
  font-family: sofia-webfont, Helvetica, Arial, Roboto, sans-serif;
  font-weight: 300;
  -webkit-font-variant-ligatures: none;
  font-variant-ligatures: none;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  width: 100%;
  position: relative;
  margin-top: 2.125rem;
}
.sds_dropdown-a .sds_dropdown-a__button {
  font-family: sofia-webfont, Helvetica, Arial, Roboto, sans-serif;
  font-weight: 300;
  -webkit-font-variant-ligatures: none;
  font-variant-ligatures: none;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  border: 0;
  position: relative;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  padding: 0.375rem 0;
  text-align: left;
  width: 100%;
  height: 2.25rem;
  color: #000;
  background-color: #fff;
  font-size: 1.0625rem;
  margin-bottom: 0;
  white-space: nowrap;
  overflow: hidden;
  color: #333;
  border-bottom: 1px solid #666;
  background-repeat: no-repeat;
  background-size: 0;
  background-image: -webkit-gradient(linear, right top, left top, from(transparent), color-stop(0, #333), color-stop(100%, #333), to(transparent));
  background-image: linear-gradient(to left, transparent 0, #333 0, #333 100%, transparent 100%);
  background-position: bottom center;
}
.sds_dropdown-a .sds_dropdown-a__button[aria-disabled] {
  outline: 0;
}
.sds_dropdown-a .sds_dropdown-a__button.sds_remove-focus:focus,
.sds_dropdown-a .sds_dropdown-a__button.sds_remove-focus[aria-expanded='true'] {
  outline: 0;
  -webkit-animation: moving-bar 250ms linear forwards;
  animation: moving-bar 250ms linear forwards;
}
.sds_dropdown-a .sds_dropdown-a__button.sds_remove-focus:focus:not([aria-disabled]),
.sds_dropdown-a .sds_dropdown-a__button.sds_remove-focus[aria-expanded='true']:not([aria-disabled]) {
  border-color: #000;
}
.sds_dropdown-a .sds_dropdown-a__button::before {
  content: '';
  background: url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='9' height='6' viewBox='0 0 9 6'%3E%3Cpath d='M.003 1.533L1.118.529l3.385 3.047L7.89.529l1.114 1.004-4.5 4.05z' style='fill:%23666;fill-rule:evenodd' /%3E%3C/svg%3E")
    no-repeat;
  width: 1.5rem;
  height: 2rem;
  display: block;
  position: absolute;
  right: 1px;
  top: 1px;
  background-position-x: 8px;
  background-position-y: 14px;
  background-color: #fff;
}
.sds_dropdown-a .sds_dropdown-a__button.sds_init-state {
  color: #666;
}
.sds_dropdown-a .sds_dropdown-a__list {
  -webkit-box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
  overflow-y: auto;
  height: auto;
  max-height: 200px;
  position: absolute;
  width: 100%;
  z-index: 999;
  -webkit-transition: max-height 0.25s ease-in-out;
  transition: max-height 0.25s ease-in-out;
  background-color: #fff;
}
.sds_dropdown-a .sds_dropdown-a__list.sds_is-collapsed {
  height: 0;
  max-height: 0;
  z-index: 888;
}
.sds_dropdown-a .sds_dropdown-a__list:focus {
  outline: 0;
}
.sds_dropdown-a .sds_dropdown-a__list-item {
  cursor: pointer;
  padding: 0.5rem;
  color: #333;
  font-size: 1.0625rem;
}
.sds_dropdown-a .sds_dropdown-a__list-item[aria-selected='true'] {
  background-color: #e5e5e5;
}
.sds_dropdown-a .sds_dropdown-a__list-item.sds_is-focused:not([aria-selected='true']),
.sds_dropdown-a .sds_dropdown-a__list-item:hover:not([aria-selected='true']) {
  background-color: #f2f2f2;
}
.sds_dropdown-a .sds_dropdown-a__list-item:focus {
  outline: 0;
}
.sds_dropdown-a .sds_field__label,
.sds_dropdown-a .text-input__label {
  display: block;
  top: -1.25rem;
  font-size: 0.75rem;
  text-transform: uppercase;
  font-size: 0.6875rem;
}
.sds_dropdown-a:not(.sds-js_enabled) .sds_field__label,
.sds_dropdown-a:not(.sds-js_enabled) .text-input__label {
  position: relative;
  height: 0;
}
.sds_dropdown-a:not(.sds-js_enabled)::after {
  content: '';
  background: url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='9' height='6' viewBox='0 0 9 6'%3E%3Cpath d='M.003 1.533L1.118.529l3.385 3.047L7.89.529l1.114 1.004-4.5 4.05z' style='fill:%23666;fill-rule:evenodd' /%3E%3C/svg%3E")
    no-repeat;
  width: 1.5rem;
  height: 2rem;
  display: block;
  position: absolute;
  right: 1px;
  top: 1px;
  background-position-x: 8px;
  background-position-y: 14px;
  background-color: #fff;
}
.sds_dropdown-a[aria-disabled]:not(.sds-js_enabled)::after {
  background: url("data:image/svg+xml;charset=utf8,%3Csvgxmlns='http://www.w3.org/2000/svg'width='9'height='6'viewBox='0096'%3E%3Cpathd='M.0031.533L1.118.529l3.3853.047L7.89.529l1.1141.004-4.54.05z'style='fill:%23CCC;fill-rule:evenodd'/%3E%3C/svg%3E")
    no-repeat;
  top: 0.85rem;
  right: 0.5rem;
  background-position: 0 0;
  height: 6px;
  width: 9px;
}
.sds_dropdown-a .sds_dropdown-a__button:not([aria-disabled]) {
  border-color: #333;
}
.sds_dropdown-a.sds_has-error .sds_dropdown-a__button,
.sds_dropdown-a.sds_has-error select {
  padding-left: 0.25rem;
  bottom: 0;
  border: 1px solid #d00000;
  background-image: -webkit-gradient(linear, right top, left top, from(transparent), color-stop(0, #d00000), color-stop(100%, #d00000), to(transparent));
  background-image: linear-gradient(to left, transparent 0, #d00000 0, #d00000 100%, transparent 100%);
}
.sds_dropdown-a.sds_has-error .sds_dropdown-a__button.sds_remove-focus:focus,
.sds_dropdown-a.sds_has-error .sds_dropdown-a__button.sds_remove-focus[aria-expanded='true'],
.sds_dropdown-a.sds_has-error select.sds_remove-focus:focus,
.sds_dropdown-a.sds_has-error select.sds_remove-focus[aria-expanded='true'] {
  border-color: #d00000;
}
.sds_dropdown-a.sds_has-error .sds_field__label,
.sds_dropdown-a.sds_has-error .text-input__label {
  color: #d00000;
}
.sds_dropdown-a.sds_is-disabled .sds_dropdown-a__button {
  color: #ccc;
  border-color: #ccc;
  background-image: none;
}
.sds_dropdown-a.sds_is-disabled .sds_dropdown-a__button:focus {
  outline: 0;
  border-color: initial;
}
.sds_dropdown-a.sds_is-disabled .sds_dropdown-a__button::before {
  background: url("data:image/svg+xml;charset=utf8,%3Csvgxmlns='http://www.w3.org/2000/svg'width='9'height='6'viewBox='0096'%3E%3Cpathd='M.0031.533L1.118.529l3.3853.047L7.89.529l1.1141.004-4.54.05z'style='fill:%23CCC;fill-rule:evenodd'/%3E%3C/svg%3E")
    no-repeat;
  top: 0.85rem;
  right: 0.5rem;
  background-position: 0 0;
  height: 6px;
  width: 9px;
}
.sds_dropdown-a select {
  font-family: sofia-webfont, Helvetica, Arial, Roboto, sans-serif;
  font-weight: 300;
  -webkit-font-variant-ligatures: none;
  font-variant-ligatures: none;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  appearance: none;
  border: 0;
  position: relative;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  padding: 0.375rem 0;
  text-align: left;
  width: 100%;
  height: 2.25rem;
  color: #000;
  background-color: #fff;
  font-size: 1.0625rem;
  margin-bottom: 0;
  white-space: nowrap;
  overflow: hidden;
  color: #333;
  border-bottom: 1px solid #666;
  background-repeat: no-repeat;
  background-size: 0;
  background-image: -webkit-gradient(linear, right top, left top, from(transparent), color-stop(0, #333), color-stop(100%, #333), to(transparent));
  background-image: linear-gradient(to left, transparent 0, #333 0, #333 100%, transparent 100%);
  background-position: bottom center;
  border-radius: 0;
  -webkit-appearance: none;
  -moz-appearance: none;
}
.sds_dropdown-a select[aria-disabled] {
  outline: 0;
}
.sds_dropdown-a select[disabled] {
  color: #ccc;
  border-color: #ccc;
  background-image: none;
}
.sds_dropdown-a select[disabled]:focus {
  outline: 0;
  border-color: initial;
}
.sds_checkbox-a {
  font-family: sofia-webfont, Helvetica, Arial, Roboto, sans-serif;
  font-weight: 300;
  -webkit-font-variant-ligatures: none;
  font-variant-ligatures: none;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  vertical-align: middle;
  margin: 0 0 1.5rem;
  font-size: 1.063rem;
}
.sds_checkbox-a__input {
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  padding: 0;
  opacity: 0;
  position: absolute;
}
.sds_checkbox-a__input:focus.focus-ring ~ .sds_checkbox-a__box,
.sds_checkbox-a__input:focus.focus-visible ~ .sds_checkbox-a__box {
  outline: 0;
  -webkit-box-shadow: 0 0 0 3px #5cabf7 !important;
  box-shadow: 0 0 0 3px #5cabf7 !important;
}
.sds_checkbox-a__input:checked ~ .sds_checkbox-a__box {
  border: 1px solid #000;
}
.sds_checkbox-a__input:checked ~ .sds_checkbox-a__box::before {
  -webkit-animation: backgroundBounce 250ms cubic-bezier(0.175, 0.885, 0.32, 1.275);
  animation: backgroundBounce 250ms cubic-bezier(0.175, 0.885, 0.32, 1.275);
  -webkit-transform: scale(1);
  transform: scale(1);
}
.sds_checkbox-a__input:checked ~ .sds_checkbox-a__box::after {
  width: 0.8125rem;
  height: 0.5rem;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -75%) rotate(-45deg);
  transform: translate(-50%, -75%) rotate(-45deg);
}
.sds_checkbox-a__input.sds_has-error:not(:checked):focus ~ .sds_checkbox-a__box,
.sds_checkbox-a__input.sds_has-error:not(:checked):not(:focus) ~ .sds_checkbox-a__box {
  border: 1px solid transparent;
  -webkit-box-shadow: 0 0 0 2px #d00000;
  box-shadow: 0 0 0 2px #d00000;
}
.sds_checkbox-a__input:disabled ~ * {
  cursor: default;
}
.sds_checkbox-a__input:disabled ~ .sds_checkbox-a__label {
  color: #ccc;
}
.sds_checkbox-a__input:disabled ~ .sds_checkbox-a__box {
  border-color: #ccc;
}
.sds_checkbox-a__box {
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  position: relative;
  display: block;
  float: left;
  margin-right: 0.625rem;
  width: 1.375rem;
  height: 1.375rem;
  border: 1px solid #666;
}
.sds_checkbox-a__box::before {
  content: '';
  display: block;
  position: absolute;
  width: calc(100% + 1px);
  height: calc(100% + 1px);
  -webkit-transform: scale(0);
  transform: scale(0);
  background-color: #000;
  background-color: #333;
}
.sds_checkbox-a__box::after {
  content: '';
  display: block;
  width: 0;
  height: 0;
  background: 0;
  position: absolute;
  border: 2px solid #fff;
  border-right: 0;
  border-top: 0;
}
.sds_checkbox-a__label {
  display: inline-block;
  color: #666;
  max-width: calc(100% - 2.3025rem);
  line-height: 1.5;
  vertical-align: middle;
  line-height: normal;
  vertical-align: text-top;
}
.sds_checkbox-a__error,
.sds_checkbox-a__help {
  display: block;
  font-size: 0.8125rem;
  line-height: 1.2;
  margin-top: 0.25rem;
}
.sds_checkbox-a__error {
  color: #d00000;
}
.sds_checkbox-a__help {
  color: #333;
}
.sds-cb_checkbox-a {
  font-family: sofia-webfont, Helvetica, Arial, Roboto, sans-serif;
  font-weight: 300;
  -webkit-font-variant-ligatures: none;
  font-variant-ligatures: none;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  vertical-align: middle;
  margin: 0 0 1.5rem;
  font-size: 1.063rem;
  font-family: 'Source Sans Pro', Helvetica, Arial, Roboto, sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 1.063rem;
}
.sds-cb_checkbox-a__input {
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  padding: 0;
  opacity: 0;
  position: absolute;
}
.sds-cb_checkbox-a__input:focus.focus-visible ~ .sds-cb_checkbox-a__box {
  outline: 0;
  -webkit-box-shadow: 0 0 0 2px #5cabf7 !important;
  box-shadow: 0 0 0 2px #5cabf7 !important;
}
.sds-cb_checkbox-a__input:checked ~ .sds-cb_checkbox-a__box {
  border: 1px solid #0466ca;
}
.sds-cb_checkbox-a__input:checked ~ .sds-cb_checkbox-a__box::before {
  -webkit-animation: backgroundBounce 250ms cubic-bezier(0.175, 0.885, 0.32, 1.275);
  animation: backgroundBounce 250ms cubic-bezier(0.175, 0.885, 0.32, 1.275);
  -webkit-transform: scale(1);
  transform: scale(1);
}
.sds-cb_checkbox-a__input:checked ~ .sds-cb_checkbox-a__box::after {
  width: 0.8125rem;
  height: 0.5rem;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -75%) rotate(-45deg);
  transform: translate(-50%, -75%) rotate(-45deg);
}
.sds-cb_checkbox-a__input.sds_has-error:not(:checked):focus ~ .sds-cb_checkbox-a__box,
.sds-cb_checkbox-a__input.sds_has-error:not(:checked):not(:focus) ~ .sds-cb_checkbox-a__box {
  border: 1px solid transparent;
  -webkit-box-shadow: 0 0 0 2px #d00000;
  box-shadow: 0 0 0 2px #d00000;
}
.sds-cb_checkbox-a__input:disabled ~ * {
  cursor: default;
}
.sds-cb_checkbox-a__input:disabled ~ .sds-cb_checkbox-a__label {
  color: #ccc;
}
.sds-cb_checkbox-a__input:disabled ~ .sds-cb_checkbox-a__box {
  border-color: #ccc;
}
.sds-cb_checkbox-a__box {
  position: relative;
  display: block;
  float: left;
  margin-right: 0.625rem;
  width: 1.375rem;
  height: 1.375rem;
  border: 1px solid #666;
}
.sds-cb_checkbox-a__box::before {
  content: '';
  display: block;
  position: absolute;
  width: calc(100% + 1px);
  height: calc(100% + 1px);
  -webkit-transform: scale(0);
  transform: scale(0);
  background-color: #0466ca;
}
.sds-cb_checkbox-a__box::after {
  content: '';
  display: block;
  width: 0;
  height: 0;
  background: 0;
  position: absolute;
  border: 2px solid #fff;
  border-right: 0;
  border-top: 0;
}
.sds-cb_checkbox-a__label {
  display: inline-block;
  font-size: 1.1rem;
  line-height: 1.5;
  vertical-align: middle;
  color: #666;
  max-width: calc(100% - 2.3025rem);
}
.sds-cb_checkbox-a__error,
.sds-cb_checkbox-a__help {
  display: block;
  font-size: 0.8125rem;
  line-height: 1.2;
  margin-top: 0.25rem;
}
.sds-cb_checkbox-a__error {
  color: #d00000;
}
.sds-cb_checkbox-a__help {
  color: #333;
}
.sds_form__checkbox {
  margin: 1.5rem 0;
}
.sds_form__checkbox__input {
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  padding: 0;
  opacity: 0;
  position: absolute;
}
.sds_form__checkbox__input:checked + .sds_form__checkbox__box {
  background-color: #0466ca;
  border: 1px solid #0466ca;
}
.sds_form__checkbox__input:checked + .sds_form__checkbox__box .sds_form__checkbox__mark {
  opacity: 1;
}
.sds_form__checkbox__input:checked + .sds_form__checkbox__box .sds_form__checkbox--fancy__mark {
  stroke-dashoffset: 0;
}
.sds_form__checkbox__input:focus + span {
  outline: #5cabf7 solid 0.125rem;
}
.sds_form__checkbox__box {
  display: block;
  float: left;
  margin-right: 0.5rem;
  width: 24px;
  height: 24px;
  border: 1px solid #666;
  -webkit-transition: background 0.3s ease;
  transition: background 0.3s ease;
}
.sds_form__checkbox__mark {
  opacity: 0;
  -webkit-transition: opacity 0.3s ease;
  transition: opacity 0.3s ease;
}
.sds_form__checkbox__mark::after {
  content: '';
  width: 13px;
  height: 8px;
  border: 2px solid #fff;
  border-top: 0;
  border-right: 0;
  background: 0;
  position: absolute;
  margin-top: 4px;
  margin-left: 5px;
  -webkit-transform: rotate(-45deg);
  transform: rotate(-45deg);
}
.sds_form__checkbox--fancy__svg {
  width: 19px;
  height: 13px;
  margin-left: 2px;
  margin-top: 6px;
}
.sds_form__checkbox--fancy__mark {
  fill: none;
  stroke: #fff;
  stroke-width: 2;
  stroke-dasharray: 23;
  stroke-dashoffset: 23;
  -webkit-transition: all 0.1s ease-in-out;
  transition: all 0.1s ease-in-out;
}
.sds_radio-button-a {
  font-family: sofia-webfont, Helvetica, Arial, Roboto, sans-serif;
  font-weight: 300;
  -webkit-font-variant-ligatures: none;
  font-variant-ligatures: none;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  margin: 0 0 1.5rem;
  font-size: 1.063rem;
}
.sds_radio-button-a input[type='radio'] {
  opacity: 0;
  position: absolute;
}
.sds_radio-button-a input[type='radio']:disabled ~ * {
  cursor: default;
}
.sds_radio-button-a__input:focus.focus-visible ~ .sds_radio-button-a__button {
  outline: 0;
  -webkit-box-shadow: 0 0 0 3px #5cabf7;
  box-shadow: 0 0 0 3px #5cabf7;
}
.sds_radio-button-a__input:focus.focus-visible:checked ~ .sds_radio-button-a__button {
  -webkit-box-shadow: 0.5px 0.5px 0 3px #5cabf7;
  box-shadow: 0.5px 0.5px 0 3px #5cabf7;
}
.sds_radio-button-a__input:checked ~ .sds_radio-button-a__button::before {
  -webkit-animation: backgroundBounce 250ms cubic-bezier(0.175, 0.885, 0.32, 1.275);
  animation: backgroundBounce 250ms cubic-bezier(0.175, 0.885, 0.32, 1.275);
  border: 1px solid #000;
  background-color: #000;
  background-color: #333;
  border: 1px solid #333;
}
.sds_radio-button-a__input:checked ~ .sds_radio-button-a__button::after {
  width: 0.7rem;
  height: 0.4rem;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-43%, -65%) rotate(-45deg);
  transform: translate(-43%, -65%) rotate(-45deg);
  border: 2px solid #fff;
  border-top: 0;
  border-right: 0;
}
.sds_radio-button-a__input:disabled ~ .sds_radio-button-a__label {
  color: #ccc;
}
.sds_radio-button-a__input:disabled ~ .sds_radio-button-a__button {
  border: 1px solid #ccc;
}
.sds_radio-button-a__button {
  display: block;
  position: relative;
  float: left;
  margin-right: 0.625rem;
  width: 1.5rem;
  height: 1.5rem;
  border: 1px solid #666;
  border-radius: 100%;
}
.sds_radio-button-a__button::after,
.sds_radio-button-a__button::before {
  content: '';
  position: absolute;
}
.sds_radio-button-a__button::after {
  width: 0;
  height: 0;
}
.sds_radio-button-a__button::before {
  display: block;
  width: calc(100% + 1px);
  height: calc(100% + 1px);
  border-radius: 100%;
  left: -1px;
  top: -1px;
}
.sds_radio-button-a__label {
  display: inline-block;
  vertical-align: middle;
  line-height: 1.5;
  color: #666;
  max-width: calc(100% - 2.3025rem);
}
.sds_radio-button-a__error,
.sds_radio-button-a__help {
  display: block;
  font-size: 0.8125rem;
  line-height: 1.2;
  margin-top: 0.25rem;
}
.sds_radio-button-a__error {
  color: #d00000;
}
.sds_radio-button-a__help {
  color: #333;
}
.sds-cb_radio-button-a {
  font-family: sofia-webfont, Helvetica, Arial, Roboto, sans-serif;
  font-weight: 300;
  -webkit-font-variant-ligatures: none;
  font-variant-ligatures: none;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  margin: 0 0 1.5rem;
  font-size: 1.063rem;
  font-family: 'Source Sans Pro', Helvetica, Arial, Roboto, sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 1.063rem;
}
.sds-cb_radio-button-a input[type='radio'] {
  opacity: 0;
  position: absolute;
}
.sds-cb_radio-button-a input[type='radio']:disabled ~ * {
  cursor: default;
}
.sds-cb_radio-button-a__input:focus.focus-visible ~ .sds-cb_radio-button-a__button {
  outline: 0;
  -webkit-box-shadow: 0 0 0 3px #5cabf7;
  box-shadow: 0 0 0 3px #5cabf7;
}
.sds-cb_radio-button-a__input:focus.focus-visible:checked ~ .sds-cb_radio-button-a__button {
  -webkit-box-shadow: 0.5px 0.5px 0 3px #5cabf7;
  box-shadow: 0.5px 0.5px 0 3px #5cabf7;
}
.sds-cb_radio-button-a__input:checked ~ .sds-cb_radio-button-a__button::before {
  -webkit-animation: backgroundBounce 250ms cubic-bezier(0.175, 0.885, 0.32, 1.275);
  animation: backgroundBounce 250ms cubic-bezier(0.175, 0.885, 0.32, 1.275);
  background-color: #0466ca;
  border: 1px solid #0466ca;
}
.sds-cb_radio-button-a__input:checked ~ .sds-cb_radio-button-a__button::after {
  width: 0.7rem;
  height: 0.4rem;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-43%, -65%) rotate(-45deg);
  transform: translate(-43%, -65%) rotate(-45deg);
  border: 2px solid #fff;
  border-top: 0;
  border-right: 0;
}
.sds-cb_radio-button-a__input:disabled ~ .sds-cb_radio-button-a__label {
  color: #ccc;
}
.sds-cb_radio-button-a__input:disabled ~ .sds-cb_radio-button-a__button {
  border: 1px solid #ccc;
}
.sds-cb_radio-button-a__button {
  display: block;
  position: relative;
  float: left;
  margin-right: 0.625rem;
  width: 1.5rem;
  height: 1.5rem;
  border: 1px solid #666;
  border-radius: 100%;
}
.sds-cb_radio-button-a__button::after {
  position: absolute;
  content: '';
  width: 0;
  height: 0;
}
.sds-cb_radio-button-a__button::before {
  content: '';
  display: block;
  position: absolute;
  width: calc(100% + 1px);
  height: calc(100% + 1px);
  border-radius: 100%;
  left: -1px;
  top: -1px;
}
.sds-cb_radio-button-a__label {
  display: inline-block;
  vertical-align: middle;
  line-height: 1.5;
  font-size: 1.1rem;
  color: #666;
  max-width: calc(100% - 2.3025rem);
}
.sds-cb_radio-button-a__error,
.sds-cb_radio-button-a__help {
  display: block;
  font-size: 0.8125rem;
  line-height: 1.2;
  margin-top: 0.25rem;
}
.sds-cb_radio-button-a__error {
  color: #d00000;
}
.sds-cb_radio-button-a__help {
  color: #333;
}
.notification,
.sds_messaging-notification {
  font-family: sofia-webfont, Helvetica, Arial, Roboto, sans-serif;
  font-weight: 300;
  -webkit-font-variant-ligatures: none;
  font-variant-ligatures: none;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  line-height: 1.25;
  padding: 0.625rem 2rem 0.625rem 2.2rem;
  margin-bottom: 1.125rem;
  border-width: 2px 2px 2px 8px;
  border-style: solid;
  position: relative;
  font-size: 0.875rem;
  color: #333;
}
.notification::before,
.sds_messaging-notification::before {
  position: absolute;
  top: 50%;
  left: 0.5rem;
  -webkit-transform: translateY(-50%);
  transform: translateY(-50%);
  content: '';
  height: 1.25rem;
  width: 1.25rem;
}
.notification__error,
.sds_messaging-notification--error {
  border-color: #d00000;
}
.add-to-bag-error-messaging::before,
.notification__error::before,
.sds_messaging-notification--error::before {
  background: url(data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAyMCAyMCI+PGcgZmlsbD0ibm9uZSIgZmlsbC1ydWxlPSJldmVub2RkIj48Y2lyY2xlIGN4PSIxMCIgY3k9IjEwIiByPSIxMCIgZmlsbD0iI0QwMDAwMCIvPjxwYXRoIGZpbGw9IiNGRkYiIGZpbGwtcnVsZT0ibm9uemVybyIgZD0iTTExLjU3OSAxNC4zNGMwIC44MjgtLjY3MiAxLjQ1LTEuNTc1IDEuNDUtLjkwNCAwLTEuNTgzLS42MjItMS41ODMtMS40NSAwLS44MzIuNjc3LTEuNDU2IDEuNTgzLTEuNDU2LjkwNSAwIDEuNTc1LjYyMyAxLjU3NSAxLjQ1NnpNOC42ODUgNC4yMWgyLjYzdjcuMjU3aC0yLjYzVjQuMjExeiIvPjwvZz48L3N2Zz4=)
    no-repeat;
}
.notification__warning,
.sds_messaging-notification--warning {
  border-color: #f0b00b;
}
.notification__warning::before,
.sds_messaging-notification--warning::before {
  background: url(data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyMCIgaGVpZ2h0PSIyMCIgdmlld0JveD0iMCAwIDIwIDIwIj48ZyBmaWxsPSJub25lIiBmaWxsLXJ1bGU9ImV2ZW5vZGQiPjxwYXRoIGZpbGw9IiNGMEIwMEIiIGQ9Ik0xMS43ODkgMS4zNDVsNy40MDMgMTQuODA4YTIgMiAwIDAgMS0xLjc4OCAyLjg5NUgyLjU5NmEyIDIgMCAwIDEtMS43ODktMi44OTVMOC4yMTEgMS4zNDVhMiAyIDAgMCAxIDMuNTc4IDB6Ii8+PHBhdGggZmlsbD0iI0ZGRiIgZmlsbC1ydWxlPSJub256ZXJvIiBkPSJNMTEuNDI5IDE1LjQ5OGMwIC44MTctLjYwOCAxLjQzLTEuNDI2IDEuNDMtLjgxNyAwLTEuNDMyLS42MTMtMS40MzItMS40MyAwLS44MjIuNjEzLTEuNDM3IDEuNDMyLTEuNDM3LjgyIDAgMS40MjYuNjE1IDEuNDI2IDEuNDM3ek04LjgxIDUuNWgyLjM4djcuMTYySDguODFWNS41eiIvPjwvZz48L3N2Zz4=)
    no-repeat;
}
.notification__information,
.sds_messaging-notification--information {
  border-color: #5cabf7;
}
.notification__information::before,
.sds_messaging-notification--information::before {
  background: url(data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyMCIgaGVpZ2h0PSIyMCIgdmlld0JveD0iMCAwIDIwIDIwIj48ZyBmaWxsPSJub25lIiBmaWxsLXJ1bGU9ImV2ZW5vZGQiPjxjaXJjbGUgY3g9IjEwIiBjeT0iMTAiIHI9IjEwIiBmaWxsPSIjNUNBQkY3Ii8+PHBhdGggZmlsbD0iI0ZGRiIgZmlsbC1ydWxlPSJub256ZXJvIiBkPSJNOC40MjEgNS42NmMwLS44MjguNjcyLTEuNDUgMS41NzUtMS40NS45MDQgMCAxLjU4My42MjIgMS41ODMgMS40NSAwIC44MzItLjY3NyAxLjQ1Ni0xLjU4MyAxLjQ1Ni0uOTA1IDAtMS41NzUtLjYyMy0xLjU3NS0xLjQ1NnptMi44OTQgMTAuMTNoLTIuNjNWOC41MzJoMi42M3Y3LjI1NnoiLz48L2c+PC9zdmc+)
    no-repeat;
}
.sds_messaging-notification__dismiss-button {
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  position: absolute;
  top: 0.75rem;
  right: 0.75rem;
  border: 0 none;
  background: url(data:image/svg+xml;base64,PHN2ZyB4bWxucz0naHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmcnIHdpZHRoPScxMicgaGVpZ2h0PScxMic+PHBhdGggZmlsbD0nIzk4OTg5OCcgZD0nTTEwLjUxMiAwbC00LjUyIDQuNTMxTDEuNDc3LjAyMyAwIDEuNTAxbDQuNTIgNC41MDctNC41MDYgNC41MTcgMS40NzIgMS40NzQgNC41MDktNC41MThMMTAuNTIzIDEyIDEyIDEwLjUyMiA3LjQ3MyA2LjAwM2w0LjUxNS00LjUyOHonLz48L3N2Zz4=)
    no-repeat;
  width: 0.75rem;
  height: 0.75rem;
}
.sds_messaging-notification__dismiss-button:focus,
.sds_messaging-notification__dismiss-button:hover {
  background: url(data:image/svg+xml;base64,PHN2ZyB4bWxucz0naHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmcnIHdpZHRoPScxMicgaGVpZ2h0PScxMic+PHBhdGggZmlsbD0nIzc1NzU3NScgZD0nTTEwLjUxMiAwbC00LjUyIDQuNTMxTDEuNDc3LjAyMyAwIDEuNTAxbDQuNTIgNC41MDctNC41MDYgNC41MTcgMS40NzIgMS40NzQgNC41MDktNC41MThMMTAuNTIzIDEyIDEyIDEwLjUyMiA3LjQ3MyA2LjAwM2w0LjUxNS00LjUyOHonLz48L3N2Zz4=)
    no-repeat;
}
.sds_is-dismissed.add-to-bag-error-messaging,
.sds_is-dismissed.notification,
.sds_messaging-notification.sds_is-dismissed {
  -webkit-animation:
    opacity-transition 550ms ease-out forwards,
    loseDimensions 0s ease-out 550ms forwards;
  animation:
    opacity-transition 550ms ease-out forwards,
    loseDimensions 0s ease-out 550ms forwards;
}
.sds-cb_messaging-notification {
  font-family: 'Source Sans Pro', Helvetica, Arial, Roboto, sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  line-height: 1.25;
  position: relative;
  padding: 0.625rem 2rem 0.625rem 2.2rem;
  margin-bottom: 1.125rem;
  border-width: 2px 2px 2px 8px;
  border-style: solid;
  color: #333;
  font-size: 0.875rem;
}
.sds-cb_messaging-notification::before {
  position: absolute;
  top: 50%;
  left: 0.5rem;
  -webkit-transform: translateY(-50%);
  transform: translateY(-50%);
  content: '';
  height: 1.25rem;
  width: 1.25rem;
}
.sds-cb_messaging-notification--error {
  border-color: #d00000;
}
.sds-cb_messaging-notification--error::before {
  background: url(data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAyMCAyMCI+PGcgZmlsbD0ibm9uZSIgZmlsbC1ydWxlPSJldmVub2RkIj48Y2lyY2xlIGN4PSIxMCIgY3k9IjEwIiByPSIxMCIgZmlsbD0iI0QwMDAwMCIvPjxwYXRoIGZpbGw9IiNGRkYiIGZpbGwtcnVsZT0ibm9uemVybyIgZD0iTTExLjU3OSAxNC4zNGMwIC44MjgtLjY3MiAxLjQ1LTEuNTc1IDEuNDUtLjkwNCAwLTEuNTgzLS42MjItMS41ODMtMS40NSAwLS44MzIuNjc3LTEuNDU2IDEuNTgzLTEuNDU2LjkwNSAwIDEuNTc1LjYyMyAxLjU3NSAxLjQ1NnpNOC42ODUgNC4yMWgyLjYzdjcuMjU3aC0yLjYzVjQuMjExeiIvPjwvZz48L3N2Zz4=)
    no-repeat;
}
.sds-cb_messaging-notification--warning {
  border-color: #f0b00b;
}
.sds-cb_messaging-notification--warning::before {
  background: url(data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyMCIgaGVpZ2h0PSIyMCIgdmlld0JveD0iMCAwIDIwIDIwIj48ZyBmaWxsPSJub25lIiBmaWxsLXJ1bGU9ImV2ZW5vZGQiPjxwYXRoIGZpbGw9IiNGMEIwMEIiIGQ9Ik0xMS43ODkgMS4zNDVsNy40MDMgMTQuODA4YTIgMiAwIDAgMS0xLjc4OCAyLjg5NUgyLjU5NmEyIDIgMCAwIDEtMS43ODktMi44OTVMOC4yMTEgMS4zNDVhMiAyIDAgMCAxIDMuNTc4IDB6Ii8+PHBhdGggZmlsbD0iI0ZGRiIgZmlsbC1ydWxlPSJub256ZXJvIiBkPSJNMTEuNDI5IDE1LjQ5OGMwIC44MTctLjYwOCAxLjQzLTEuNDI2IDEuNDMtLjgxNyAwLTEuNDMyLS42MTMtMS40MzItMS40MyAwLS44MjIuNjEzLTEuNDM3IDEuNDMyLTEuNDM3LjgyIDAgMS40MjYuNjE1IDEuNDI2IDEuNDM3ek04LjgxIDUuNWgyLjM4djcuMTYySDguODFWNS41eiIvPjwvZz48L3N2Zz4=)
    no-repeat;
}
.sds-cb_messaging-notification--information {
  border-color: #5cabf7;
}
.sds-cb_messaging-notification--information::before {
  background: url(data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyMCIgaGVpZ2h0PSIyMCIgdmlld0JveD0iMCAwIDIwIDIwIj48ZyBmaWxsPSJub25lIiBmaWxsLXJ1bGU9ImV2ZW5vZGQiPjxjaXJjbGUgY3g9IjEwIiBjeT0iMTAiIHI9IjEwIiBmaWxsPSIjNUNBQkY3Ii8+PHBhdGggZmlsbD0iI0ZGRiIgZmlsbC1ydWxlPSJub256ZXJvIiBkPSJNOC40MjEgNS42NmMwLS44MjguNjcyLTEuNDUgMS41NzUtMS40NS45MDQgMCAxLjU4My42MjIgMS41ODMgMS40NSAwIC44MzItLjY3NyAxLjQ1Ni0xLjU4MyAxLjQ1Ni0uOTA1IDAtMS41NzUtLjYyMy0xLjU3NS0xLjQ1NnptMi44OTQgMTAuMTNoLTIuNjNWOC41MzJoMi42M3Y3LjI1NnoiLz48L2c+PC9zdmc+)
    no-repeat;
}
.sds-cb_messaging-notification__dismiss-button {
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  position: absolute;
  top: 0.75rem;
  right: 0.75rem;
  border: 0 none;
  background: url(data:image/svg+xml;base64,PHN2ZyB4bWxucz0naHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmcnIHdpZHRoPScxMicgaGVpZ2h0PScxMic+PHBhdGggZmlsbD0nIzk4OTg5OCcgZD0nTTEwLjUxMiAwbC00LjUyIDQuNTMxTDEuNDc3LjAyMyAwIDEuNTAxbDQuNTIgNC41MDctNC41MDYgNC41MTcgMS40NzIgMS40NzQgNC41MDktNC41MThMMTAuNTIzIDEyIDEyIDEwLjUyMiA3LjQ3MyA2LjAwM2w0LjUxNS00LjUyOHonLz48L3N2Zz4=)
    no-repeat;
  width: 0.75rem;
  height: 0.75rem;
}
.sds-cb_messaging-notification__dismiss-button:focus,
.sds-cb_messaging-notification__dismiss-button:hover {
  background: url(data:image/svg+xml;base64,PHN2ZyB4bWxucz0naHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmcnIHdpZHRoPScxMicgaGVpZ2h0PScxMic+PHBhdGggZmlsbD0nIzc1NzU3NScgZD0nTTEwLjUxMiAwbC00LjUyIDQuNTMxTDEuNDc3LjAyMyAwIDEuNTAxbDQuNTIgNC41MDctNC41MDYgNC41MTcgMS40NzIgMS40NzQgNC41MDktNC41MThMMTAuNTIzIDEyIDEyIDEwLjUyMiA3LjQ3MyA2LjAwM2w0LjUxNS00LjUyOHonLz48L3N2Zz4=)
    no-repeat;
}
.sds-cb_messaging-notification.sds_is-dismissed {
  -webkit-animation:
    opacity-transition 550ms ease-out forwards,
    loseDimensions 0s ease-out 550ms forwards;
  animation:
    opacity-transition 550ms ease-out forwards,
    loseDimensions 0s ease-out 550ms forwards;
}
.sds_sp_2xs {
  margin-bottom: 0.25rem;
}
.sds_sp_xs {
  margin-bottom: 0.3rem;
}
.sds_sp_sm {
  margin-bottom: 0.5rem;
}
.sds_sp_lg {
  margin-bottom: 1.25rem;
}
.sds_sp_xl {
  margin-bottom: 2rem;
}
.sds_sp_2xl {
  margin-bottom: 3rem;
}
.sds_sp_3xl {
  margin-bottom: 4rem;
}
.sds_sp {
  margin-bottom: 1rem;
}
.sds_sp_top_2xs {
  margin-top: 0.25rem;
}
.sds_sp_top_xs {
  margin-top: 0.3rem;
}
.sds_sp_top_sm {
  margin-top: 0.5rem;
}
.sds_sp_top_lg {
  margin-top: 1.25rem;
}
.sds_sp_top_xl {
  margin-top: 2rem;
}
.sds_sp_top_2xl {
  margin-top: 3rem;
}
.sds_sp_top_3xl {
  margin-top: 4rem;
}
.sds_sp_top {
  margin-top: 1rem;
}
.sds_sp_left_2xs {
  margin-left: 0.25rem;
}
.sds_sp_left_xs {
  margin-left: 0.3rem;
}
.sds_sp_left_sm {
  margin-left: 0.5rem;
}
.sds_sp_left_lg {
  margin-left: 1.25rem;
}
.sds_sp_left_xl {
  margin-left: 2rem;
}
.sds_sp_left_2xl {
  margin-left: 3rem;
}
.sds_sp_left_3xl {
  margin-left: 4rem;
}
.sds_sp_left {
  margin-left: 1rem;
}
.sds_sp_right_2xs {
  margin-right: 0.25rem;
}
.sds_sp_right_xs {
  margin-right: 0.3rem;
}
.sds_sp_right_sm {
  margin-right: 0.5rem;
}
.sds_sp_right_lg {
  margin-right: 1.25rem;
}
.sds_sp_right_xl {
  margin-right: 2rem;
}
.sds_sp_right_2xl {
  margin-right: 3rem;
}
.sds_sp_right_3xl {
  margin-right: 4rem;
}
.sds_sp_right {
  margin-right: 1rem;
}
.sds_sp_all_2xs {
  margin: 0.25rem;
}
.sds_sp_all_xs {
  margin: 0.3rem;
}
.sds_sp_all_sm {
  margin: 0.5rem;
}
.sds_sp_all_lg {
  margin: 1.25rem;
}
.sds_sp_all_xl {
  margin: 2rem;
}
.sds_sp_all_2xl {
  margin: 3rem;
}
.sds_sp_all_3xl {
  margin: 4rem;
}
.sds_sp_all {
  margin: 1rem;
}
.sds_sp_horizontal_2xs {
  margin-left: 0.25rem;
  margin-right: 0.25rem;
}
.sds_sp_horizontal_xs {
  margin-left: 0.3rem;
  margin-right: 0.3rem;
}
.sds_sp_horizontal_sm {
  margin-left: 0.5rem;
  margin-right: 0.5rem;
}
.sds_sp_horizontal_lg {
  margin-left: 1.25rem;
  margin-right: 1.25rem;
}
.sds_sp_horizontal_xl {
  margin-left: 2rem;
  margin-right: 2rem;
}
.sds_sp_horizontal_2xl {
  margin-left: 3rem;
  margin-right: 3rem;
}
.sds_sp_horizontal_3xl {
  margin-left: 4rem;
  margin-right: 4rem;
}
.sds_sp_horizontal {
  margin-left: 1rem;
  margin-right: 1rem;
}
.sds_sp_vertical_2xs {
  margin-top: 0.25rem;
  margin-bottom: 0.25rem;
}
.sds_sp_vertical_xs {
  margin-top: 0.3rem;
  margin-bottom: 0.3rem;
}
.sds_sp_vertical_sm {
  margin-top: 0.5rem;
  margin-bottom: 0.5rem;
}
.sds_sp_vertical_lg {
  margin-top: 1.25rem;
  margin-bottom: 1.25rem;
}
.sds_sp_vertical_xl {
  margin-top: 2rem;
  margin-bottom: 2rem;
}
.sds_sp_vertical_2xl {
  margin-top: 3rem;
  margin-bottom: 3rem;
}
.sds_sp_vertical_3xl {
  margin-top: 4rem;
  margin-bottom: 4rem;
}
.sds_sp_vertical {
  margin-top: 1rem;
  margin-bottom: 1rem;
}
.sds_pd_2xs {
  padding: 0.25rem;
}
.sds_pd_xs {
  padding: 0.3rem;
}
.sds_pd_sm {
  padding: 0.5rem;
}
.sds_pd_lg {
  padding: 1.25rem;
}
.sds_pd_xl {
  padding: 2rem;
}
.sds_pd_2xl {
  padding: 3rem;
}
.sds_pd_3xl {
  padding: 4rem;
}
.sds_pd {
  padding: 1rem;
}
.sds_pd_left_2xs {
  padding-left: 0.25rem;
}
.sds_pd_left_xs {
  padding-left: 0.3rem;
}
.sds_pd_left_sm {
  padding-left: 0.5rem;
}
.sds_pd_left_lg {
  padding-left: 1.25rem;
}
.sds_pd_left_xl {
  padding-left: 2rem;
}
.sds_pd_left_2xl {
  padding-left: 3rem;
}
.sds_pd_left_3xl {
  padding-left: 4rem;
}
.sds_pd_left {
  padding-left: 1rem;
}
.sds_pd_right_2xs {
  padding-right: 0.25rem;
}
.sds_pd_right_xs {
  padding-right: 0.3rem;
}
.sds_pd_right_sm {
  padding-right: 0.5rem;
}
.sds_pd_right_lg {
  padding-right: 1.25rem;
}
.sds_pd_right_xl {
  padding-right: 2rem;
}
.sds_pd_right_2xl {
  padding-right: 3rem;
}
.sds_pd_right_3xl {
  padding-right: 4rem;
}
.sds_pd_right {
  padding-right: 1rem;
}
.sds_pd_bottom_2xs {
  padding-bottom: 0.25rem;
}
.sds_pd_bottom_xs {
  padding-bottom: 0.3rem;
}
.sds_pd_bottom_sm {
  padding-bottom: 0.5rem;
}
.sds_pd_bottom_lg {
  padding-bottom: 1.25rem;
}
.sds_pd_bottom_xl {
  padding-bottom: 2rem;
}
.sds_pd_bottom_2xl {
  padding-bottom: 3rem;
}
.sds_pd_bottom_3xl {
  padding-bottom: 4rem;
}
.sds_pd_bottom {
  padding-bottom: 1rem;
}
.sds_pd_top_2xs {
  padding-top: 0.25rem;
}
.sds_pd_top_xs {
  padding-top: 0.3rem;
}
.sds_pd_top_sm {
  padding-top: 0.5rem;
}
.sds_pd_top_lg {
  padding-top: 1.25rem;
}
.sds_pd_top_xl {
  padding-top: 2rem;
}
.sds_pd_top_2xl {
  padding-top: 3rem;
}
.sds_pd_top_3xl {
  padding-top: 4rem;
}
.sds_pd_top {
  padding-top: 1rem;
}
.sds_pd_horizontal_2xs {
  padding-left: 0.25rem;
  padding-right: 0.25rem;
}
.sds_pd_horizontal_xs {
  padding-left: 0.3rem;
  padding-right: 0.3rem;
}
.sds_pd_horizontal_sm {
  padding-left: 0.5rem;
  padding-right: 0.5rem;
}
.sds_pd_horizontal_lg {
  padding-left: 1.25rem;
  padding-right: 1.25rem;
}
.sds_pd_horizontal_xl {
  padding-left: 2rem;
  padding-right: 2rem;
}
.sds_pd_horizontal_2xl {
  padding-left: 3rem;
  padding-right: 3rem;
}
.sds_pd_horizontal_3xl {
  padding-left: 4rem;
  padding-right: 4rem;
}
.sds_pd_horizontal {
  padding-left: 1rem;
  padding-right: 1rem;
}
.sds_pd_vertical_2xs {
  padding-top: 0.25rem;
  padding-bottom: 0.25rem;
}
.sds_pd_vertical_xs {
  padding-top: 0.3rem;
  padding-bottom: 0.3rem;
}
.sds_pd_vertical_sm {
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
}
.sds_pd_vertical_lg {
  padding-top: 1.25rem;
  padding-bottom: 1.25rem;
}
.sds_pd_vertical_xl {
  padding-top: 2rem;
  padding-bottom: 2rem;
}
.sds_pd_vertical_2xl {
  padding-top: 3rem;
  padding-bottom: 3rem;
}
.sds_pd_vertical_3xl {
  padding-top: 4rem;
  padding-bottom: 4rem;
}
.sds_pd_vertical {
  padding-top: 1rem;
  padding-bottom: 1rem;
}
.sp {
  margin-bottom: 1em;
}
.sp_xs {
  margin-bottom: 0.3em;
}
.sp_sm {
  margin-bottom: 0.5em;
}
.sp_lg {
  margin-bottom: 1.25em;
}
.sp_xl {
  margin-bottom: 2em;
}
.sp_top_xs {
  margin-top: 0.3em;
}
.sp_top_sm {
  margin-top: 0.5em;
}
.sp_top,
.sp_top_md {
  margin-top: 1em;
}
.sp_top_lg {
  margin-top: 1.25em;
}
.sp_top_xl {
  margin-top: 2em;
}
.sp_left {
  margin-left: 1em;
}
.sp_right_xs {
  margin-right: 0.3em;
}
.sp_right_sm {
  margin-right: 0.5em;
}
.sp_right {
  margin-right: 1em;
}
.sp_all {
  margin: 1em;
}
.sds_sp_bottom,
.sp_bottom {
  margin-bottom: 1em;
}
.sp_horizontal {
  margin-left: 1em;
  margin-right: 1em;
}
.sp_vertical {
  margin-left: 1em;
  margin-right: 1em;
}
.sds_sp_1-125,
.sp_1-125 {
  margin-bottom: 1.125em;
}
.sds_sp_0-875,
.sp_0-875 {
  margin-bottom: 0.875em;
}
.sds_sp_0-625,
.sp_0-625 {
  margin-bottom: 0.625em;
}
.sds_sp_0-375,
.sp_0-375 {
  margin-bottom: 0.375em;
}
.sds_sp_0-45,
.sp_0-45 {
  margin-bottom: 0.45em;
}
.sds_sp_top_0-11,
.sp_top_0-11 {
  margin-top: 0.11em;
}
.sds_sp_top_0-2,
.sp_top_0-2 {
  margin-top: 0.2em;
}
.sds_sp_top_0-7,
.sp_top_0-7 {
  margin-top: 0.7em;
}
.sds_sp_0-75,
.sp_0-75 {
  margin-bottom: 0.75em;
}
.sds_sp_top_0-675,
.sp_top_0-675 {
  margin-top: 0.675em;
}
.sds_sp_top_0-75,
.sp_top_0-75 {
  margin-top: 0.75em;
}
.pd_sm {
  padding-bottom: 0.5em;
}
.pd {
  padding-bottom: 1em;
}
.pd_all_sm {
  padding: 0.5em;
}
.pd_all,
.sds_pd_all {
  padding: 1em;
}
.pd_left_sm {
  padding-left: 0.5em;
}
.pd_left {
  padding-left: 1em;
}
.pd_right_sm {
  padding-right: 0.5em;
}
.pd_right {
  padding-right: 1em;
}
.pd_sm {
  padding-bottom: 0.5em;
}
.pd {
  padding-bottom: 1em;
}
.pd_lg {
  padding-bottom: 1.25em;
}
.pd_top_xs {
  padding-top: 0.3em;
}
.pd_top_sm {
  padding-top: 0.5em;
}
.pd_top {
  padding-top: 1em;
}
.pd_top_lg {
  padding-top: 1.25em;
}
.pd_horizontal_sm {
  padding-left: 0.5em;
  padding-right: 0.5em;
}
.pd_horizontal {
  padding-left: 1em;
  padding-right: 1em;
}
.pd_vertical_sm {
  padding-top: 0.5em;
  padding-bottom: 0.5em;
}
.pd_vertical {
  padding-top: 1em;
  padding-bottom: 1em;
}
.pd_0-75,
.pd_vertical_0-75,
.sds_pd_0-75,
.sds_pd_vertical_0-75 {
  padding-bottom: 0.75em;
}
.pd_top_0-75,
.pd_vertical_0-75,
.sds_pd_top_0-75,
.sds_pd_vertical_0-75 {
  padding-top: 0.75em;
}
.pd_right_0-75,
.sds_pd_right_0-75 {
  padding-right: 0.75em;
}
.pd_left_0-75,
.sds_pd_left_0-75 {
  padding-left: 0.75em;
}
.pd_0-65,
.pd_vertical_0-65,
.sds_pd_0-65,
.sds_pd_vertical_0-65 {
  padding-bottom: 0.65em;
}
.pd_top_0-65,
.pd_vertical_0-65,
.sds_pd_top_0-65,
.sds_pd_vertical_0-65 {
  padding-top: 0.65em;
}
.drop-shadow,
.sds_drop-shadow,
.universal-modal {
  -webkit-box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
}
.full,
.sds_full {
  width: 100%;
}
.sds_visually-hidden,
.visually-hidden {
  position: absolute;
  left: -999em;
  top: -999em;
}
.sds_visually-hidden-cancel,
.visually-hidden-cancel {
  position: static;
  left: auto;
  top: auto;
}
.focusable-hide,
.sds_focusable-hide {
  opacity: 0;
  position: absolute;
  height: 0;
  width: 0;
}
.sds_visible-at-sm,
.visible-at-sm {
  display: block;
}
@media (min-width: 569px) {
  .sds_visible-at-sm,
  .visible-at-sm {
    display: none !important;
  }
}
.sds_visible-at-md,
.visible-at-md {
  display: block;
}
@media (min-width: 768px) {
  .sds_visible-at-md,
  .visible-at-md {
    display: none !important;
  }
}
.hide-at-md,
.sds_hide-at-md {
  display: none;
}
@media (min-width: 768px) {
  .hide-at-md,
  .sds_hide-at-md {
    display: block;
  }
}
@media (min-width: 768px) {
  .hide-at-lg,
  .sds_hide-at-lg {
    display: none !important;
  }
}
@media (min-width: 768px) and (min-aspect-ratio: 1 / 1), (min-width: 1024px) {
  .hide-at-lg-landscape,
  .sds_hide-at-lg-landscape {
    display: none !important;
  }
}
.sds_show-at-md,
.show-at-md {
  display: none;
}
@media (min-width: 569px) {
  .sds_show-at-md,
  .show-at-md {
    display: block;
  }
}
.sds_show-at-lg-landscape,
.show-at-lg-landscape {
  display: none;
}
@media (min-width: 768px) and (min-aspect-ratio: 1 / 1), (min-width: 1024px) {
  .sds_show-at-lg-landscape,
  .show-at-lg-landscape {
    display: block;
  }
}
.sds_show-at-lg,
.show-at-lg {
  display: none;
}
@media (min-width: 768px) {
  .sds_show-at-lg,
  .show-at-lg {
    display: block;
  }
}
.sds_show-at-lg-inline,
.show-at-lg-inline {
  display: none;
}
@media (min-width: 768px) {
  .sds_show-at-lg-inline,
  .show-at-lg-inline {
    display: inline-block;
  }
}
@media (min-width: 1024px) {
  .hide-at-xl,
  .sds_hide-at-xl {
    display: none !important;
  }
}
.sds_show-at-xl,
.show-at-xl {
  display: none;
}
@media (min-width: 1024px) {
  .sds_show-at-xl,
  .show-at-xl {
    display: block;
  }
}
.sds_show-at-xl-inline {
  display: none;
}
@media (min-width: 1024px) {
  .sds_show-at-xl-inline {
    display: inline;
  }
}
.left,
.sds_left {
  float: left;
}
.right,
.sds_right {
  float: right;
}
.sds_sp_horizontal_auto {
  margin-left: auto;
  margin-right: auto;
}
.sds_absolute-centered--horizontal {
  position: absolute;
  left: 50%;
  -webkit-transform: translateX(-50%);
  transform: translateX(-50%);
}
.sds_absolute-centered--vertical {
  position: absolute;
  top: 50%;
  -webkit-transform: translateY(-50%);
  transform: translateY(-50%);
}
.sds_absolute-centered {
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translateX(-50%) translateY(-50%);
  transform: translateX(-50%) translateY(-50%);
}
.sds_hr,
hr {
  width: 100%;
  margin: 0.75em 0;
  color: #c9c9c9;
  height: 1px;
}
.relative,
.sds_relative {
  position: relative;
}
.absolute,
.sds_absolute {
  position: absolute;
}
.cursor-pointer,
.modal--base-btn,
.sds_cursor-pointer {
  cursor: pointer;
}
.disable-text-select,
.sds_disable-text-select {
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}
.border-box,
.sds_border-box {
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
}
.block,
.sds_block {
  display: block;
}
.inline,
.sds_inline {
  display: inline;
}
.inline-block,
.sds_inline-block {
  display: inline-block;
  vertical-align: middle;
}
.modal--close-button,
.sds_unbuttonize,
.unbuttonize {
  background: 0;
  border: 0;
  width: auto;
  margin: 0;
  padding: 0;
  text-align: inherit;
  font: inherit;
}
.full-height,
.sds_full-height {
  height: 100%;
  overflow: auto;
}
.has-open-component,
.sds_has-open-component {
  position: fixed;
}
.clearfix::after,
.sds_clearfix::after {
  clear: both;
  content: '';
  display: table;
}
.capitalize,
.modal--title,
.sds_capitalize {
  text-transform: capitalize;
}
.sds_uppercase,
.uppercase {
  text-transform: uppercase;
}
.lowercase,
.sds_lowercase {
  text-transform: lowercase;
}
.sds_normalcase {
  text-transform: none;
}
.nowrap,
.sds_nowrap {
  white-space: nowrap;
}
.sds_tx_left,
.tx_left {
  text-align: left;
}
.sds_tx_center,
.tx_center {
  text-align: center;
}
.sds_tx_right,
.tx_right {
  text-align: right;
}
.sds_overflow-ellipsis {
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
}
.sds_border--thick {
  border-width: 0.125rem !important;
}
.sds_font-size--74 {
  font-size: 4.625rem;
}
.sds_font-size--64 {
  font-size: 4rem;
}
.sds_font-size--56 {
  font-size: 3.5rem;
}
.sds_font-size--50 {
  font-size: 3.125rem;
}
.sds_font-size--48 {
  font-size: 3rem;
}
.sds_font-size--42 {
  font-size: 2.625rem;
}
.sds_font-size--36 {
  font-size: 2.25rem;
}
.sds_font-size--32 {
  font-size: 2rem;
}
.sds_font-size--30 {
  font-size: 1.875rem;
}
.sds_font-size--28 {
  font-size: 1.75rem;
}
.sds_font-size--24 {
  font-size: 1.5rem;
}
.sds_font-size--22 {
  font-size: 1.4rem;
}
.sds_font-size--20 {
  font-size: 1.25rem;
}
.sds_font-size--18 {
  font-size: 1.125rem;
}
.sds_font-size--17 {
  font-size: 1.0625rem;
}
.sds_font-size--16 {
  font-size: 1rem;
}
.modal--title,
.sds_font-size--15 {
  font-size: 0.9375rem;
}
.sds_font-size--14 {
  font-size: 0.875rem;
}
.sds-cb_button-tertiary,
.sds-cb_button-tertiary--flat,
.sds-cb_button-tertiary--outline,
.sds_font-size--13 {
  font-size: 0.8125rem;
}
.modal--base-btn,
.sds_font-size--12 {
  font-size: 0.75rem;
}
.sds_font-size--11 {
  font-size: 0.6875rem;
}
.sds_font-size--10 {
  font-size: 0.625rem;
}
.sds_letter-spacing---1-0 {
  letter-spacing: -1px;
}
.sds_letter-spacing---0-5 {
  letter-spacing: -0.5px;
}
.sds_letter-spacing--0-0 {
  letter-spacing: 0;
}
.sds_letter-spacing--0-5 {
  letter-spacing: 0.5px;
}
.sds_letter-spacing--1-0 {
  letter-spacing: 1px;
}
.sds_letter-spacing--1-5 {
  letter-spacing: 1.5px;
}
.sds_letter-spacing--2-0 {
  letter-spacing: 2px;
}
.sds_letter-spacing--2-5 {
  letter-spacing: 2.5px;
}
.sds_letter-spacing--3-0 {
  letter-spacing: 3px;
}
.sds_line-height--1-0 {
  line-height: 1;
}
.sds_line-height--1-25 {
  line-height: 1.25;
}
.sds_line-height--1-4 {
  line-height: 1.43;
}
.sds_line-height--1-65 {
  line-height: 1.65;
}
.sds_strike-through {
  text-decoration: line-through;
}
.s-body-a,
.sds_s-body-a {
  font-family: sofia-webfont, Helvetica, Arial, Roboto, sans-serif;
  font-size: 1.125rem;
  color: #333;
}
.s-eyebrow-1,
.sds_s-eyebrow-1 {
  font-family: sofia-webfont, Helvetica, Arial, Roboto, sans-serif;
  font-weight: 300;
  font-size: 2rem;
  letter-spacing: 1px;
  line-height: 1;
  color: #333;
}
.s-eyebrow-2,
.sds_s-eyebrow-2 {
  font-family: sofia-webfont, Helvetica, Arial, Roboto, sans-serif;
  font-size: 1.5rem;
  letter-spacing: 1px;
  line-height: 1;
  color: #333;
}
.s-headline-1a,
.sds_s-headline-1a {
  font-family: sofia-webfont, Helvetica, Arial, Roboto, sans-serif;
  font-weight: 300;
  font-size: 4.69rem;
  line-height: 1em;
  color: #333;
}
.s-headline-2a,
.sds_s-headline-2a {
  font-family: DINNextLTPro-Condensed, sofia-webfont, Helvetica, Arial, Roboto, sans-serif;
  font-size: 6.25rem;
  line-height: 1em;
  color: #333;
}
.s-headline-3a,
.sds_s-headline-3a {
  font-family: DINNextLTPro-Condensed, sofia-webfont, Helvetica, Arial, Roboto, sans-serif;
  font-size: 3.75rem;
  line-height: 1em;
  color: #333;
}
.s-headline-4a,
.sds_s-headline-4a {
  font-family: sofia-webfont, Helvetica, Arial, Roboto, sans-serif;
  font-weight: 600;
  font-size: 1.5rem;
  line-height: 1em;
  color: #333;
}
.s-category,
.sds_s-category {
  font-family: DINNextLTPro-Condensed, sofia-webfont, Helvetica, Arial, Roboto, sans-serif;
  font-size: 1.75rem;
  line-height: 1em;
  letter-spacing: 3px;
  color: #fff;
}
@media (min-width: 569px) {
  .s-category,
  .sds_s-category {
    font-size: 3.5em;
  }
}
@media (min-width: 768px) {
  .s-category,
  .sds_s-category {
    font-size: 4.375em;
  }
}
.sds-cb_color--b1 {
  color: #0466ca;
}
.sds-cb_color-border--b1 {
  border-color: #0466ca;
}
.sds-cb_color-background--b1 {
  background-color: #0466ca;
}
.sds-cb_color--b2 {
  color: #5cabf7;
}
.sds-cb_color-border--b2 {
  border-color: #5cabf7;
}
.sds-cb_color-background--b2 {
  background-color: #5cabf7;
}
.sds-cb_color--g1 {
  color: #333;
}
.sds-cb_color-border--g1 {
  border-color: #333;
}
.sds-cb_color-background--g1 {
  background-color: #333;
}
.sds-cb_color--g2 {
  color: #666;
}
.sds-cb_color-border--g2 {
  border-color: #666;
}
.sds-cb_color-background--g2 {
  background-color: #666;
}
.sds-cb_color--g3 {
  color: #999;
}
.sds-cb_color-border--g3 {
  border-color: #999;
}
.sds-cb_color-background--g3 {
  background-color: #999;
}
#hamburger-nav-footer .footer-head,
.sds-cb_color--g4 {
  color: #ccc;
}
.sds-cb_color-border--g4 {
  border-color: #ccc;
}
.sds-cb_color-background--g4 {
  background-color: #ccc;
}
.sds-cb_color--g5 {
  color: #f2f2f2;
}
.sds-cb_color-border--g5 {
  border-color: #f2f2f2;
}
.sds-cb_color-background--g5 {
  background-color: #f2f2f2;
}
.sds-cb_color--g6 {
  color: #eee;
}
.sds-cb_color-border--g6 {
  border-color: #eee;
}
.sds-cb_color-background--g6 {
  background-color: #eee;
}
.sds-cb_color--wh {
  color: #fff;
}
.sds-cb_color-border--wh {
  border-color: #fff;
}
.sds-cb_color-background--wh {
  background-color: #fff;
}
.sds-cb_color--bk {
  color: #000;
}
.sds-cb_color-border--bk {
  border-color: #000;
}
#hamburger-nav-footer,
.sds-cb_color-background--bk {
  background-color: #000;
}
.sds-cb_color--r1 {
  color: #d00000;
}
.sds-cb_color-border--r1 {
  border-color: #d00000;
}
.sds-cb_color-background--r1 {
  background-color: #d00000;
}
.sds-cb_color--r2 {
  color: #c82828;
}
.sds-cb_color-border--r2 {
  border-color: #c82828;
}
.sds-cb_color-background--r2 {
  background-color: #c82828;
}
.sds-cb_color--r3 {
  color: #f43d00;
}
.sds-cb_color-border--r3 {
  border-color: #f43d00;
}
.sds-cb_color-background--r3 {
  background-color: #f43d00;
}
.sds-cb_color--err1 {
  color: #d00000;
}
.sds-cb_color-border--err1 {
  border-color: #d00000;
}
.sds-cb_color-background--err1 {
  background-color: #d00000;
}
.sds-cb_color--inf {
  color: #ff7807;
}
.sds-cb_color-border--inf {
  border-color: #ff7807;
}
.sds-cb_color-background--inf {
  background-color: #ff7807;
}
.sds-cb_color--inf1 {
  color: #5cabf7;
}
.sds-cb_color-border--inf1 {
  border-color: #5cabf7;
}
.sds-cb_color-background--inf1 {
  background-color: #5cabf7;
}
.sds-cb_color--wrn1 {
  color: #f0b00b;
}
.sds-cb_color-border--wrn1 {
  border-color: #f0b00b;
}
.sds-cb_color-background--wrn1 {
  background-color: #f0b00b;
}
.sds-cb_color--s1 {
  color: #16a816;
}
.sds-cb_color-border--s1 {
  border-color: #16a816;
}
.sds-cb_color-background--s1 {
  background-color: #16a816;
}
.sds-cb_color--s2 {
  color: #f0b00b;
}
.sds-cb_color-border--s2 {
  border-color: #f0b00b;
}
.sds-cb_color-background--s2 {
  background-color: #f0b00b;
}
.sds-cb_color--s3 {
  color: #d00000;
}
.sds-cb_color-border--s3 {
  border-color: #d00000;
}
.sds-cb_color-background--s3 {
  background-color: #d00000;
}
.sds-cb_color--alpha00 {
  color: transparent;
}
.sds-cb_color-border--alpha00 {
  border-color: transparent;
}
.sds-cb_color-background--alpha00 {
  background-color: transparent;
}
.sds_color--b1_universal {
  color: #0466ca;
}
.sds_color-border--b1_universal {
  border-color: #0466ca;
}
.sds_color-background--b1_universal {
  background-color: #0466ca;
}
.sds_color--b2_universal {
  color: #5cabf7;
}
.sds_color-border--b2_universal {
  border-color: #5cabf7;
}
.sds_color-background--b2_universal {
  background-color: #5cabf7;
}
.sds_color--g1_universal {
  color: #333;
}
.sds_color-border--g1_universal {
  border-color: #333;
}
.sds_color-background--g1_universal {
  background-color: #333;
}
.sds_color--g2_universal {
  color: #666;
}
.sds_color-border--g2_universal {
  border-color: #666;
}
.sds_color-background--g2_universal {
  background-color: #666;
}
.sds_color--g3_universal {
  color: #999;
}
.sds_color-border--g3_universal {
  border-color: #999;
}
.sds_color-background--g3_universal {
  background-color: #999;
}
.sds_color--g4_universal {
  color: #ccc;
}
.sds_color-border--g4_universal {
  border-color: #ccc;
}
.sds_color-background--g4_universal {
  background-color: #ccc;
}
.sds_color--g5_universal {
  color: #f2f2f2;
}
.sds_color-border--g5_universal {
  border-color: #f2f2f2;
}
.sds_color-background--g5_universal {
  background-color: #f2f2f2;
}
.sds_color--g6_universal {
  color: #eee;
}
.sds_color-border--g6_universal {
  border-color: #eee;
}
.sds_color-background--g6_universal {
  background-color: #eee;
}
.sds_color--wh_universal {
  color: #fff;
}
.sds_color-border--wh_universal {
  border-color: #fff;
}
.sds_color-background--wh_universal {
  background-color: #fff;
}
.sds_color--bk_universal {
  color: #000;
}
.sds_color-border--bk_universal {
  border-color: #000;
}
.sds_color-background--bk_universal {
  background-color: #000;
}
.sds_color--r1_universal {
  color: #d00000;
}
.sds_color-border--r1_universal {
  border-color: #d00000;
}
.sds_color-background--r1_universal {
  background-color: #d00000;
}
.sds_color--r2_universal {
  color: #c82828;
}
.sds_color-border--r2_universal {
  border-color: #c82828;
}
.sds_color-background--r2_universal {
  background-color: #c82828;
}
.sds_color--r3_universal {
  color: #f43d00;
}
.sds_color-border--r3_universal {
  border-color: #f43d00;
}
.sds_color-background--r3_universal {
  background-color: #f43d00;
}
.sds_color--err1_universal {
  color: #d00000;
}
.sds_color-border--err1_universal {
  border-color: #d00000;
}
.sds_color-background--err1_universal {
  background-color: #d00000;
}
.sds_color--inf_universal {
  color: #ff7807;
}
.sds_color-border--inf_universal {
  border-color: #ff7807;
}
.sds_color-background--inf_universal {
  background-color: #ff7807;
}
.sds_color--inf1_universal {
  color: #5cabf7;
}
.sds_color-border--inf1_universal {
  border-color: #5cabf7;
}
.sds_color-background--inf1_universal {
  background-color: #5cabf7;
}
.sds_color--wrn1_universal {
  color: #f0b00b;
}
.sds_color-border--wrn1_universal {
  border-color: #f0b00b;
}
.sds_color-background--wrn1_universal {
  background-color: #f0b00b;
}
.sds_color--s1_universal {
  color: #16a816;
}
.sds_color-border--s1_universal {
  border-color: #16a816;
}
.sds_color-background--s1_universal {
  background-color: #16a816;
}
.sds_color--s2_universal {
  color: #f0b00b;
}
.sds_color-border--s2_universal {
  border-color: #f0b00b;
}
.sds_color-background--s2_universal {
  background-color: #f0b00b;
}
.sds_color--s3_universal {
  color: #d00000;
}
.sds_color-border--s3_universal {
  border-color: #d00000;
}
.sds_color-background--s3_universal {
  background-color: #d00000;
}
.sds_color--alpha00_universal {
  color: transparent;
}
.sds_color-border--alpha00_universal {
  border-color: transparent;
}
.sds_color-background--alpha00_universal {
  background-color: transparent;
}
.sds_color--b1 {
  color: #333;
}
.sds_color-border--b1 {
  border-color: #333;
}
.sds_color-background--b1 {
  background-color: #333;
}
.sds_color--b2 {
  color: #92278f;
}
.sds_color-border--b2 {
  border-color: #92278f;
}
.sds_color-background--b2 {
  background-color: #92278f;
}
.sds_color--b3 {
  color: #00aebc;
}
.sds_color-border--b3 {
  border-color: #00aebc;
}
.sds_color-background--b3 {
  background-color: #00aebc;
}
.sds_color--g1 {
  color: #333;
}
.sds_color-border--g1 {
  border-color: #333;
}
.sds_color-background--g1 {
  background-color: #333;
}
.sds_color--g2 {
  color: #666;
}
.sds_color-border--g2 {
  border-color: #666;
}
.sds_color-background--g2 {
  background-color: #666;
}
.sds_color--g3 {
  color: #a7a9ac;
}
.sds_color-border--g3 {
  border-color: #a7a9ac;
}
.sds_color-background--g3 {
  background-color: #a7a9ac;
}
.sds_color--g4 {
  color: #c9c9c9;
}
.sds_color-border--g4 {
  border-color: #c9c9c9;
}
.sds_color-background--g4 {
  background-color: #c9c9c9;
}
.sds_color--g5 {
  color: #e0e0e0;
}
.sds_color-border--g5 {
  border-color: #e0e0e0;
}
.sds_color-background--g5 {
  background-color: #e0e0e0;
}
.sds_color--g6 {
  color: #f2f2f2;
}
.sds_color-border--g6 {
  border-color: #f2f2f2;
}
.sds_color-background--g6 {
  background-color: #f2f2f2;
}
.sds_color--wh {
  color: #fff;
}
.sds_color-border--wh {
  border-color: #fff;
}
.sds_color-background--wh {
  background-color: #fff;
}
.sds_color--bk {
  color: #000;
}
.sds_color-border--bk {
  border-color: #000;
}
.sds_color-background--bk {
  background-color: #000;
}
.sds_color--r1 {
  color: #d00000;
}
.sds_color-border--r1 {
  border-color: #d00000;
}
.sds_color-background--r1 {
  background-color: #d00000;
}
.sds_color--err1 {
  color: #d00000;
}
.sds_color-border--err1 {
  border-color: #d00000;
}
.sds_color-background--err1 {
  background-color: #d00000;
}
.sds_color--inf {
  color: #ff7807;
}
.sds_color-border--inf {
  border-color: #ff7807;
}
.sds_color-background--inf {
  background-color: #ff7807;
}
.sds_color--alpha00 {
  color: transparent;
}
.sds_color-border--alpha00 {
  border-color: transparent;
}
.sds_color-background--alpha00 {
  background-color: transparent;
}
.sds_color--s1 {
  color: #fb4b57;
}
.sds_color-border--s1 {
  border-color: #fb4b57;
}
.sds_color-background--s1 {
  background-color: #fb4b57;
}
.sds_color--s2 {
  color: #76777b;
}
.sds_color-border--s2 {
  border-color: #76777b;
}
.sds_color-background--s2 {
  background-color: #76777b;
}
.sds_color--s3 {
  color: #f25e21;
}
.sds_color-border--s3 {
  border-color: #f25e21;
}
.sds_color-background--s3 {
  background-color: #f25e21;
}
.sds_color--s4 {
  color: rgba(0, 0, 0, 0.5);
}
.sds_color-border--s4 {
  border-color: rgba(0, 0, 0, 0.5);
}
.sds_color-background--s4 {
  background-color: rgba(0, 0, 0, 0.5);
}
.sds_color--s5 {
  color: rgba(255, 255, 255, 0.5);
}
.sds_color-border--s5 {
  border-color: rgba(255, 255, 255, 0.5);
}
.sds_color-background--s5 {
  background-color: rgba(255, 255, 255, 0.5);
}
.sds_color--gray90 {
  color: #191919;
}
.sds_color-border--gray90 {
  border-color: #191919;
}
.sds_color-background--gray90 {
  background-color: #191919;
}
.sds_color--gray80 {
  color: #333;
}
.sds_color-border--gray80 {
  border-color: #333;
}
.sds_color-background--gray80 {
  background-color: #333;
}
.sds_color--gray70 {
  color: #4c4c4c;
}
.sds_color-border--gray70 {
  border-color: #4c4c4c;
}
.sds_color-background--gray70 {
  background-color: #4c4c4c;
}
.sds_color--gray60 {
  color: #666;
}
.sds_color-border--gray60 {
  border-color: #666;
}
.sds_color-background--gray60 {
  background-color: #666;
}
.sds_color--gray54 {
  color: #757575;
}
.sds_color-border--gray54 {
  border-color: #757575;
}
.sds_color-background--gray54 {
  background-color: #757575;
}
.sds_color--gray50 {
  color: #7f7f7f;
}
.sds_color-border--gray50 {
  border-color: #7f7f7f;
}
.sds_color-background--gray50 {
  background-color: #7f7f7f;
}
.sds_color--gray40 {
  color: #999;
}
.sds_color-border--gray40 {
  border-color: #999;
}
.sds_color-background--gray40 {
  background-color: #999;
}
.sds_color--gray30 {
  color: #b2b2b2;
}
.sds_color-border--gray30 {
  border-color: #b2b2b2;
}
.sds_color-background--gray30 {
  background-color: #b2b2b2;
}
.sds_color--gray20 {
  color: #ccc;
}
.sds_color-border--gray20 {
  border-color: #ccc;
}
.sds_color-background--gray20 {
  background-color: #ccc;
}
.sds_color--gray10 {
  color: #e5e5e5;
}
.sds_color-border--gray10 {
  border-color: #e5e5e5;
}
.sds_color-background--gray10 {
  background-color: #e5e5e5;
}
.sds_color--gray05 {
  color: #f2f2f2;
}
.sds_color-border--gray05 {
  border-color: #f2f2f2;
}
.sds_color-background--gray05 {
  background-color: #f2f2f2;
}
.sds_color--wh {
  color: #fff;
}
.sds_color-border--wh {
  border-color: #fff;
}
.sds_color-background--wh {
  background-color: #fff;
}
.sds_color--bk {
  color: #000;
}
.sds_color-border--bk {
  border-color: #000;
}
.sds_color-background--bk {
  background-color: #000;
}
.sds_color--bk-alpha25 {
  color: rgba(0, 0, 0, 0.25);
}
.sds_color-border--bk-alpha25 {
  border-color: rgba(0, 0, 0, 0.25);
}
.sds_color-background--bk-alpha25 {
  background-color: rgba(0, 0, 0, 0.25);
}
.sds_color--bk-alpha50 {
  color: rgba(0, 0, 0, 0.5);
}
.sds_color-border--bk-alpha50 {
  border-color: rgba(0, 0, 0, 0.5);
}
.sds_color-background--bk-alpha50 {
  background-color: rgba(0, 0, 0, 0.5);
}
.sds_color--bk-alpha75 {
  color: rgba(0, 0, 0, 0.75);
}
.sds_color-border--bk-alpha75 {
  border-color: rgba(0, 0, 0, 0.75);
}
.sds_color-background--bk-alpha75 {
  background-color: rgba(0, 0, 0, 0.75);
}
.sds_color--wh-alpha25 {
  color: rgba(255, 255, 255, 0.25);
}
.sds_color-border--wh-alpha25 {
  border-color: rgba(255, 255, 255, 0.25);
}
.sds_color-background--wh-alpha25 {
  background-color: rgba(255, 255, 255, 0.25);
}
.sds_color--wh-alpha50 {
  color: rgba(255, 255, 255, 0.5);
}
.sds_color-border--wh-alpha50 {
  border-color: rgba(255, 255, 255, 0.5);
}
.sds_color-background--wh-alpha50 {
  background-color: rgba(255, 255, 255, 0.5);
}
.sds_color--wh-alpha75 {
  color: rgba(255, 255, 255, 0.75);
}
.sds_color-border--wh-alpha75 {
  border-color: rgba(255, 255, 255, 0.75);
}
.sds_color-background--wh-alpha75 {
  background-color: rgba(255, 255, 255, 0.75);
}
.tx_sm {
  font-size: 0.8em;
}
.tx_md {
  font-size: 1em;
}
.tx_lg {
  font-size: 1.3em;
}
.tx_xl {
  font-size: 1.7em;
}
.tx_1-25 {
  font-size: 1.25em;
}
.tx_2-00 {
  font-size: 2em;
}
.tx_light {
  font-family: 'helvetica light', sans-serif;
  font-weight: 400;
}
.tx_normal {
  font-weight: 400;
}
.tx_bold {
  font-weight: 700;
}
.tx_uppercase {
  text-transform: uppercase;
}
.tx_subtle {
  opacity: 0.6;
}
.tx_center {
  text-align: center;
}
.tx_right {
  text-align: right;
}
.tx_left {
  text-align: left;
}
.overflow-ellipses {
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
}
.onesite {
  font-size: 16px;
  line-height: 1;
  font-family: Helvetica, Arial, sans-serif;
}
.no-btn-style {
  border: 0;
  background-color: transparent;
}
.no-scroll {
  overflow: hidden;
  position: fixed;
}
.disable-hover {
  pointer-events: none;
}
@media (max-width: 767px) {
  .nav-shiftee {
    background: #fff;
    -webkit-transition: -webkit-transform 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
    transition: -webkit-transform 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
    transition: transform 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
    transition:
      transform 0.3s cubic-bezier(0.645, 0.045, 0.355, 1),
      -webkit-transform 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
  }
}
.center-align {
  text-align: center;
}
.universal-modal--is-open {
  overflow: hidden;
}
.universal-modal {
  display: none;
  width: 100%;
  right: 0;
  left: 0;
  top: 12%;
  margin: 0 auto;
  position: relative;
  z-index: 800;
  background-color: #fff;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  border-radius: 6px;
  overflow: hidden;
}
.universal-modal.show-modal {
  display: block;
}
.universal-modal_natural-height {
  margin-top: 2em;
  margin-bottom: 2em;
  top: 0;
}
.universal-modal__role-wrapper {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: #fff;
  height: 100%;
  max-height: initial;
}
@media (min-width: 569px) {
  .universal-modal__role-wrapper {
    position: static;
  }
}
.modal--header {
  position: relative;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  min-height: 38px;
  padding: 0.7em 2.5em 0.7em 1em;
  line-height: 1.43;
  font-weight: 600;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  color: #666;
  font-family: sofia-webfont, Helvetica, Arial, Roboto, sans-serif;
  -webkit-font-variant-ligatures: none;
  font-variant-ligatures: none;
  font-size: 0.933rem;
  background-color: #f2f2f2;
  text-align: left;
}
.modal--header_no-background {
  background-color: transparent;
}
.modal--footer {
  padding: 0.6em 1em;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
}
.modal--content {
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  overflow: auto;
  -webkit-overflow-scrolling: touch;
  height: 100%;
}
.content--scrollable {
  padding: 1em 1em;
}
.modal--close-button {
  padding: 0;
  position: absolute;
  right: 0;
  width: 2.5em;
  height: 2.5em;
  top: 0;
  -webkit-box-sizing: content-box;
  box-sizing: content-box;
  z-index: 1;
}
.modal--close-button:focus {
  border: solid 2px #5cabf7;
  outline: 0;
}
.inlinesvg .modal--close-icon.icon-x {
  background-image: none;
}
.modal--close-icon--svg {
  width: 1.5em;
  height: 1.5em;
  stroke: gray;
  padding: 0.5em;
  -webkit-box-sizing: content-box;
  box-sizing: content-box;
}
.universal-modal--backdrop {
  display: none;
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  width: 100%;
  background-color: rgba(51, 51, 51, 0.75);
  z-index: 1040;
  overflow: auto;
}
.universal-modal--backdrop.show-modal {
  display: block;
}
.modal_mini {
  max-width: 250px;
  max-height: 180px;
}
.modal_standard {
  max-width: 300px;
  max-height: 500px;
}
@media (min-width: 569px) {
  .modal_standard {
    max-width: 400px;
    max-height: 600px;
  }
}
@media (min-width: 768px) {
  .modal_standard {
    max-width: 500px;
    max-height: 750px;
  }
}
@media (min-width: 1024px) {
  .modal_standard {
    max-width: 600px;
    max-height: 800px;
  }
}
.modal_max {
  max-width: 400px;
  max-height: 850px;
}
@media (min-width: 569px) {
  .modal_max {
    max-width: 582px;
    max-height: 1200px;
  }
}
@media (min-width: 768px) {
  .modal_max {
    max-width: 760px;
    max-height: 1250px;
  }
}
@media (min-width: 1024px) {
  .modal_max {
    max-width: 1000px;
    max-height: 1300px;
  }
}
.modal_arbitrary {
  width: 90%;
  margin-left: auto;
  margin-right: auto;
}
.modal-action-sheet {
  width: 100%;
  height: 100%;
  max-width: none;
  max-height: initial;
  position: fixed;
  top: 0;
  border-radius: 0;
  margin: auto;
}
.modal-action-sheet .modal--content {
  height: calc(100% - 38px);
}
#iframe {
  width: 100%;
  border: 0;
}
@media (min-width: 768px) {
  .universal-modal_except-at-lg {
    display: block;
    position: relative;
    max-width: none;
    max-height: none;
    width: auto;
    -webkit-box-shadow: none;
    box-shadow: none;
    background: 0;
    height: auto !important;
    overflow: visible;
  }
  .universal-modal_except-at-lg .modal--content {
    overflow: visible;
    height: auto !important;
  }
  .universal-modal_except-at-lg .modal--header {
    display: none;
  }
}
.subheading {
  overflow: hidden;
  font-weight: 300;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-variant-ligatures: none;
  font-variant-ligatures: none;
  font-family: sofia-webfont, Helvetica, Arial, Roboto, sans-serif;
  font-size: 1.067rem;
  color: #666;
  background: #f2f2f2;
  padding: 0.3em 0.5em;
  margin-left: 0.3em;
  margin-bottom: 1.25em;
}
@media (min-width: 569px) {
  .subheading {
    margin-right: 0.3em;
  }
}
.subheading--text {
  text-transform: uppercase;
  letter-spacing: 0.15em;
  display: inline-block;
  position: relative;
}
.ruled--header {
  padding: 0.3em 0;
  text-align: center;
  overflow: hidden;
  background: -webkit-gradient(linear, left top, left bottom, from(rgba(255, 255, 255, 0.75)), to(rgba(255, 255, 255, 1)));
  background: linear-gradient(rgba(255, 255, 255, 0.75), rgba(255, 255, 255, 1));
  position: relative;
}
@media (min-width: 768px) {
  .ruled--header {
    padding: 0.5em 0;
  }
}
.ruled--header--text {
  text-transform: uppercase;
  display: inline-block;
  padding: 0 0.5em;
  position: relative;
  z-index: 200;
  font-weight: 300;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-variant-ligatures: none;
  font-variant-ligatures: none;
  font-family: sofia-webfont, Helvetica, Arial, Roboto, sans-serif;
  font-size: 1.067rem;
  color: #333;
  letter-spacing: 0.15em;
}
.ruled--header--text.sds-cb_color--g1 {
  color: #333;
}
@media (min-width: 768px) {
  .ruled--header--text {
    text-transform: uppercase;
    letter-spacing: 0.1em;
    font-weight: 300;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    color: #333;
    font-family: sofia-webfont, Helvetica, Arial, Roboto, sans-serif;
    -webkit-font-variant-ligatures: none;
    font-variant-ligatures: none;
    font-size: 1.2rem;
    letter-spacing: 0.15em;
  }
}
.ruled--header--text:after,
.ruled--header--text:before {
  display: block;
  content: '';
  border-top: 1px solid #c9c9c9;
  position: absolute;
  top: 50%;
  margin-top: -1px;
  width: 70em;
}
.ruled--header--text:before {
  right: 100%;
}
.ruled--header--text:after {
  left: 100%;
}
#modalWindow {
  opacity: 0;
  background: rgba(0, 0, 0, 0.5);
}
.modal-open {
  overflow: hidden;
}
.modal {
  display: none;
  overflow: hidden;
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 1050;
  -webkit-overflow-scrolling: touch;
  outline: 0;
  height: 100%;
}
.modal-open .modal {
  overflow-x: hidden;
  overflow-y: auto;
}
.modal-dialog {
  position: relative;
}
.modal-content {
  position: relative;
  background-color: #fff;
  border: 1px solid #999;
  border: 1px solid rgba(0, 0, 0, 0.2);
  border-radius: 6px;
  -webkit-box-shadow: 0 3px 9px rgba(0, 0, 0, 0.5);
  box-shadow: 0 3px 9px rgba(0, 0, 0, 0.5);
  background-clip: padding-box;
  outline: 0;
}
.modal-backdrop {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 1040;
  background-color: #000;
}
.modal-backdrop.fade {
  -webkit-opacity: 0;
  -moz-opacity: 0;
  -o-opacity: 0;
  opacity: 0;
}
.modal-backdrop.in {
  -webkit-opacity: 0.5;
  -moz-opacity: 0.5;
  -o-opacity: 0.5;
  opacity: 0.5;
}
.modal-header {
  border-bottom: 1px solid #e5e5e5;
  outline: 0;
}
.modal-title {
  margin: 0;
}
.modal-body {
  position: relative;
}
.modal-footer {
  text-align: right;
}
.modal-footer .btn + .btn {
  margin-left: 5px;
  margin-bottom: 0;
}
.modal-footer .btn-group .btn + .btn {
  margin-left: -1px;
}
.modal-footer .btn-block + .btn-block {
  margin-left: 0;
}
.modal-scrollbar-measure {
  position: absolute;
  top: -9999px;
  width: 50px;
  height: 50px;
  overflow: scroll;
}
@media (min-width: 768px) {
  .modal-content {
    -webkit-box-shadow: 0 5px 15px rgba(0, 0, 0, 0.5);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.5);
  }
  .modal-sm {
    width: 300px;
  }
}
@media (min-width: 992px) {
  .modal-lg {
    width: 900px;
  }
}
.modal-footer:after,
.modal-footer:before {
  content: ' ';
  display: table;
}
.modal-footer:after {
  clear: both;
}
.center-block {
  display: block;
  margin-left: auto;
  margin-right: auto;
}
.pull-right {
  float: right !important;
}
.pull-left {
  float: left !important;
}
.show {
  display: block !important;
}
.invisible {
  visibility: hidden;
}
.text-hide {
  font: 0/0 a;
  color: transparent;
  text-shadow: none;
  background-color: transparent;
  border: 0;
}
.hidden {
  display: none !important;
  visibility: hidden !important;
}
.affix {
  position: fixed;
  -webkit-transform: translate3d(0, 0, 0);
  transform: translate3d(0, 0, 0);
}
.link,
.modal--base-btn {
  color: #333;
  text-decoration: underline;
}
.link_default {
  color: #339;
}
.link_default:active,
.link_default:hover,
.link_default:link,
.link_default:visited {
  color: #339;
}
.link_default:focus,
.link_default:hover {
  text-decoration: underline;
}
.link_default_universal {
  font-family: sofia-webfont, Helvetica, Arial, Roboto, sans-serif;
  font-weight: 600;
  -webkit-font-variant-ligatures: none;
  font-variant-ligatures: none;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  color: #0073c8;
}
.link_default_universal:active,
.link_default_universal:hover,
.link_default_universal:link,
.link_default_universal:visited {
  color: #0073c8;
}
.link_default_universal:focus,
.link_default_universal:hover {
  text-decoration: underline;
}
.limit-width {
  max-width: 87.5em;
  margin-left: auto;
  margin-right: auto;
}
.formBottomButtons_responsive_modal {
  font-size: 16px;
  font-family: Arial, Verdana, Helvetica;
}
.universalSectionContainer_responsive_modal {
  padding: 0;
  margin: 0;
  width: 100%;
  font-size: inherit;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  padding: 0 24px;
}
.check_another_giftcard_button {
  font-size: 16px;
  text-decoration: none;
}
#giftCardBalanceList_responsive_modal {
  padding: 1px 0 0 2px;
  font-size: 16px;
}
.check_balance_modal_padding {
  margin-bottom: 24px;
}
.giftcard_modal_header {
  margin-top: 16px;
  margin-bottom: 16px;
}
.responsive_modal_balance {
  margin-bottom: 24px !important;
}
.check_another_giftcard_link {
  text-decoration: none;
}
.modal-content {
  border-radius: 10px;
  border: 0;
}
.modal-content .alert-red {
  color: #ca3c3e;
}
.modal-content .black {
  color: #333;
}
.modal-content .grey {
  color: #666;
}
.modal-content .grey-bg {
  background: #666;
}
.modal-content .light-grey {
  color: #999;
}
.modal-content .gap-inc-orange {
  color: #f34d00;
}
.modal-content .gap-inc-orange-bg {
  background: #f34d00;
}
.modal-dialog {
  -webkit-transform: translateY(-50%);
  transform: translateY(-50%);
  top: 50%;
  margin-left: auto;
  margin-right: auto;
  width: 95%;
  max-width: 650px;
  font-size: 14px;
  max-height: 80%;
  font-family: 'Open Sans', arial;
}
@media (min-width: 768px) {
  .modal-dialog {
    width: 80%;
    font-size: 15px;
  }
}
.modal-dialog button {
  font-family: 'Open Sans', arial;
}
.modal-header {
  background: #f2f2f2;
  border-top-left-radius: 10px;
  border-top-right-radius: 10px;
}
.modal-header .close {
  color: #a8a8a8;
  background: #f2f2f2;
  padding: 0;
  position: relative;
  top: -0.3em;
  border: 0;
}
.modal-footer button {
  width: 100%;
  border: 0;
  border-radius: 3px;
  color: #fff;
  background: #320066;
  padding: 1em;
  text-transform: uppercase;
  cursor: pointer;
}
.modal-footer button:active,
.modal-footer button:hover {
  opacity: 0.8;
}
.modal-footer button.sds-cb_button-primary,
.modal-footer button.sds-cb_button-secondary--outline {
  font-family: 'Source Sans Pro', Helvetica, Arial, Roboto, sans-serif;
  border-radius: 0;
  padding: 0.5rem;
}
.modal-footer button.sds-cb_button-primary {
  background-color: #f43d00;
  border: 2px solid transparent;
}
.modal-footer button.sds-cb_button-secondary--outline {
  background-color: transparent;
  border: 2px solid #333;
  color: #333;
}
.cssHide {
  position: absolute;
  top: -100em;
  width: 1px;
  height: 1px;
  overflow: hidden;
}
.cssHide2 {
  position: absolute;
  top: 0;
  left: 0;
  visibility: hidden;
}
.srs_color--blue-4b81ae {
  color: #4b81ae;
}
.srs_color--gray-787878 {
  color: #787878;
}
.srs_color-background--blue-a1cceb {
  background-color: #a1cceb;
}
.srs_color-background--blue-2f3133 {
  background-color: #2f3133;
}
.srs_color-background--gray-EEE {
  background-color: #eee;
}
.srs_color-background--purple-26245f {
  background-color: #26245f;
}
.srs_color-border--purple-26245f {
  border-color: #26245f;
}
.srs_color-border--blue-2f3133 {
  border-color: #2f3133;
}
.srs_color-border--blue-4780ab {
  border-color: #4780ab;
}
.bottom-border {
  text-decoration: underline;
  background-color: transparent;
}
.modal_standard {
  max-width: 650px;
}
.modal--content {
  padding: 0;
}
.modal-dialog {
  top: 50%;
  margin-left: auto;
  margin-right: auto;
  width: 95%;
  max-width: 650px;
  font-size: 14px;
  max-height: 80%;
  font-family: 'Open Sans', arial, sans-serif;
}
@media (min-width: 768px) {
  .modal-dialog {
    width: 80%;
    font-size: 15px;
  }
}
.modal-dialog button {
  font-family: 'Open Sans', arial, sans-serif;
}
.modal-footer button {
  width: 100%;
  border: 0;
  border-radius: 3px;
  color: #fff;
  background: #320066;
  padding: 1em;
  text-transform: uppercase;
  cursor: pointer;
}
.modal-footer button.sds-cb_button-primary,
.modal-footer button.sds-cb_button-secondary--outline {
  font-family: 'Source Sans Pro', Helvetica, Arial, Roboto, sans-serif;
  border-radius: 0;
  padding: 0.5rem;
}
.modal-footer button.sds-cb_button-primary {
  background-color: #f43d00;
  border: 2px solid transparent;
}
.modal-footer button.sds-cb_button-secondary--outline {
  background-color: transparent;
  border: 2px solid #333;
  color: #333;
}
.modal-footer button:active,
.modal-footer button:hover {
  opacity: 0.8;
}
.modal-footer::after,
.modal-footer::before {
  content: ' ';
  display: table;
}
.modal-footer::after {
  clear: both;
}
.alert-red {
  color: #ca3c3e;
}
.black {
  color: #333;
}
.grey {
  color: #666;
}
.grey-bg {
  background: #666;
}
.light-grey {
  color: #999;
}
.gap-inc-orange {
  color: #f34d00;
}
.gap-inc-orange-bg {
  background: #f34d00;
}
.modal-header {
  background: #f2f2f2;
  border-top-left-radius: 10px;
  border-top-right-radius: 10px;
}
.modal-header .close {
  color: #a8a8a8;
  background: #f2f2f2;
  padding: 0;
  position: relative;
  top: -0.3em;
  border: 0;
}
.tx_bold {
  font-weight: 700;
}
.tx_sm {
  font-size: 0.8em;
}
.hide {
  display: none !important;
}
.universal-modal--is-open {
  overflow: hidden;
}

#main_head {
  height: unset !important;
}

img.note1,
img.note2 {
  display: inline !important;
  margin-top: -2px !important;
}

#wcdChat {
  display: none !important;
}

p > a > img,
p > a > img,
p > a > img,
p > a > img {
  display: inline !important;
}

/** undo tailwind resets, remove this when components are re-rewritten*/
html,
:host {
  line-height: normal;
}
