import '@testing-library/jest-dom';
import { render, CmsMarketing, screen } from 'test-utils';
import PageMarketing from '../PageMarketing';
import { MarketingContent } from '@mui/fetchMarketing';
import { DynamicMarketing } from '@mui/components/json-marketing.client';
import contentData from './fixtures/categoryData';
import { CmsProps } from '@mui/components/legacy-mui-entry';

describe('A Category Slot', () => {
  (DynamicMarketing as jest.Mock).mockRestore();
  (CmsMarketing as jest.Mock).mockImplementation((props: CmsProps) => {
    const { schema } = props;
    return <h1>Marketing content: {schema}</h1>;
  });

  it('should render ebb', async () => {
    render(<PageMarketing cid='1006482' position='top' marketingType='category-banner' />, {
      customRenderProps: {
        content: contentData as MarketingContent<'category'>,
      },
    });
    expect(await screen.findByText('Marketing content: https://cms.gap.com/schema/content/v2/category-banner.json')).toBeInTheDocument();
  });

  it('should render ism', async () => {
    const ismData = contentData['1182084/ism'].contentItems[0];

    render(<PageMarketing cid='1006482' subcatCid='1182084' position='top' marketingType='ism' pageData={{ ...ismData, schema: ismData._meta.schema }} />, {
      customRenderProps: {
        content: contentData as MarketingContent<'category'>,
      },
    });
    expect(await screen.findByText('Marketing content: https://cms.gap.com/schema/content/v1/ism-double-partial-image.json')).toBeInTheDocument();
  });

  afterAll(() => {
    jest.clearAllMocks();
  });
});
