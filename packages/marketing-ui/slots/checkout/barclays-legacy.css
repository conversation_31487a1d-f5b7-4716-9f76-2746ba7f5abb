.credit-card-offer {
  width: unset !important;
}
.sds_visually-hidden,
.visually-hidden {
  position: absolute;
  left: -999em;
  top: -999em;
}
.sds_visually-hidden-cancel,
.visually-hidden-cancel {
  position: static;
  left: auto;
  top: auto;
}
.sds-cb_font--primary {
  font-family: 'Source Sans Pro', Helvetica, Arial, Roboto, sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
.sds_hr,
hr {
  width: 100%;
  margin: 0.75em 0;
  color: #c9c9c9;
  height: 1px;
}
hr {
  height: 2px;
  background: #cbcaca;
  margin: 0;
  padding: 0;
  border: 0;
}
.sds_pd {
  padding: 1rem;
}
.sds_font-size--13 {
  font-size: 0.8125rem;
}