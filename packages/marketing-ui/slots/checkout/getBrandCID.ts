import { type Brand } from '@ecom-next/utils/server';
import logger from '@ecom-next/app/logger';
import { type BrandCIDMap, type BrandCIDValue } from '.';

/**
 * Tries to retrieve the CID for a given brand.
 *
 * The main purpose of this function is that it logs an error if the
 * brand cannot be mapped to the CID lookup table so that we have
 * something to debug on instead of silently failing.
 */
function getBrandCID(cidMap: BrandCIDMap, brand: Brand): BrandCIDValue | undefined {
  const cid = cidMap[brand];

  if (!cid) {
    logger.error(`getCID(): Could not match brand "${brand}" to the provided CIDs.`);
    return undefined;
  }

  return cid;
}

export { getBrandCID };
