'use client';

import { type ReactNode, Fragment, useContext } from 'react';
import Marketing from '../../src';
import { usePageContext } from '@ecom-next/sitewide/hooks/usePageContext';
import { brandCIDMap, getBrandCID } from '.';
import { useActiveBagContext } from '@ecom-next/shopping-bag/contexts/ActiveBagProvider';
import { type CreditCardOfferProps } from '@mui/components/legacy/components/CreditCardOffer';
import './barclays-legacy.css';

// Extend CreditCardOfferProps with the new returnUrl and currencySymbol properties
type ExtendedCreditCardOfferProps = CreditCardOfferProps & {
  returnUrl: string;
  currencySymbol: string;
  isFeatureEnabled: boolean;
};

export function CreditCardOffer(): ReactNode {
  const { market, locale, brand, ecomApiBaseUrl, brandAbbr } = usePageContext();
  const cid = getBrandCID(brandCIDMap, brand) || 'error';
  const slot = `${cid}/cardpromo` as const;

  const bagContext = useActiveBagContext();

  if (!bagContext.bagState.data) {
    return <></>;
  }

  const {
    bagState: {
      data: {
        summaryOfCharges: { myTotal },
        cardSavingsCalcTotal,
        currencySymbol,
        bagAttributes: { userStatus },
      },
    },
  } = bagContext;

  let recognition_status = 'unrecognized';
  if (userStatus === 'RECOGNIZED') recognition_status = 'recognized';
  if (userStatus === 'AUTHENTICATED') recognition_status = 'authenticated';

  const extendedProps = {
    datalayerValues: {
      channel: `${brandAbbr.toLowerCase()}:buy`,
      page_name: `${brandAbbr.toLowerCase()}:buy:ShoppingBag`,
      page_type: 'ShoppingBag',
      recognition_status,
    },
    isFeatureEnabled: true,
  };

  const validBrand = new Map([
    ['gap', 'GP'],
    ['gapfs', 'GPFS'],
    ['on', 'ON'],
    ['br', 'BR'],
    ['at', 'AT'],
    ['brfs', 'BRFS'],
  ]);
  const url = typeof window !== 'undefined' && window.location ? window.location.href : '';
  const creditCardPromoProps: Partial<ExtendedCreditCardOfferProps> = {
    shoppingBagTotal: parseFloat(myTotal),
    itemsTotalForBrand: cardSavingsCalcTotal,
    currencySymbol: currencySymbol,
    prescreenApiHost: ecomApiBaseUrl,
    // @ts-ignore
    brandAbbr: validBrand.get(brand),
    returnUrl: url,
  };

  return (
    <Marketing
      brand={brand}
      market={market}
      locale={locale}
      cid={cid}
      pageType='shoppingbag'
      slot={slot}
      defaultMarketing={<Fragment />}
      {...creditCardPromoProps}
      {...extendedProps}
    />
  );
}
