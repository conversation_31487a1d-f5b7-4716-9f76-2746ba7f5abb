import React from 'react';
import { render } from '@testing-library/react';
import { CreditCardOffer } from '../CreditCardOffer';
import { usePageContext } from '@ecom-next/sitewide/hooks/usePageContext';
import { useActiveBagContext } from '@ecom-next/shopping-bag/contexts/ActiveBagProvider';
import Marketing from '../../../src';

jest.mock('../../../src', () => {
  return jest.fn(() => <div>Marketing Component</div>);
});

// Mock the hooks
jest.mock('@ecom-next/sitewide/hooks/usePageContext', () => ({
  usePageContext: jest.fn(),
}));

jest.mock('@ecom-next/shopping-bag/contexts/ActiveBagProvider', () => ({
  useActiveBagContext: jest.fn(),
}));

describe('CreditCardOffer', () => {
  const mockUsePageContext = usePageContext as jest.Mock;
  const mockUseActiveBagContext = useActiveBagContext as jest.Mock;

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders Marketing component when bag data is present', () => {
    mockUsePageContext.mockReturnValue({
      market: 'US',
      locale: 'en-US',
      brand: 'gap',
      ecomApiBaseUrl: 'https://api.example.com',
      pageType: 'shoppingbag',
      brandAbbr: 'gp',
    });

    mockUseActiveBagContext.mockReturnValue({
      bagState: {
        data: {
          summaryOfCharges: { myTotal: '100.00' },
          cardSavingsCalcTotal: 19.9,
          currencySymbol: '$',
          bagAttributes: {
            userStatus: 'RECOGNIZED',
          },
        },
      },
    });

    const { getByText } = render(<CreditCardOffer />);

    expect(getByText('Marketing Component')).toBeInTheDocument();
    expect(Marketing).toHaveBeenCalledWith(
      expect.objectContaining({
        shoppingBagTotal: 100.0,
        itemsTotalForBrand: 19.9,
        currencySymbol: '$',
        returnUrl: expect.any(String),
        brandAbbr: 'GP', // based on the mapping
        market: 'US',
      }),
      {}
    );
  });

  it('does not render anything when bag data is not available', () => {
    mockUsePageContext.mockReturnValue({
      market: 'US',
      locale: 'en-US',
      brand: 'gap',
      ecomApiBaseUrl: 'https://api.example.com',
      pageType: 'shoppingbag',
      brandAbbr: 'gp',
    });

    mockUseActiveBagContext.mockReturnValue({
      bagState: {
        data: null,
      },
    });

    const { container } = render(<CreditCardOffer />);

    expect(container.firstChild).toBeNull(); // No render
  });
});
