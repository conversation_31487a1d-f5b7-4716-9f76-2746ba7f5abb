import React from 'react';
import { render, act } from '@testing-library/react';
import { renderHook } from '@testing-library/react-hooks';
import { Feature, logNewRelicError } from '@ecom-next/shopping-bag/utils';
import { ActiveBagProvider } from '@ecom-next/shopping-bag/contexts/ActiveBagProvider';
import { usePageContext } from '@sitewide/hooks/usePageContext';
import { useProductRecommendations } from '@ecom-next/sitewide/product-recs-provider';
import { useShoppingBagFeatures } from '@ecom-next/shopping-bag/utils';
import Marketing from '@ecom-next/marketing-ui';

import * as getBrandCIDModule from '../getBrandCID';
import { ProductRecommendations } from '../ProductRecommendations';

jest.mock('@sitewide/hooks/usePageContext', () => ({
  usePageContext: jest.fn().mockReturnValue({
    brand: 'gap',
    market: 'us',
    locale: 'en_US',
  }),
}));

jest.mock('@ecom-next/marketing-ui', () => {
  return {
    __esModule: true,
    default: jest.fn(),
  };
});

jest.mock('@ecom-next/sitewide/product-recs-provider', () => ({
  useProductRecommendations: jest.fn(),
}));

jest.mock('@ecom-next/shopping-bag/utils', () => ({
  useShoppingBagFeatures: jest.fn(),
  logNewRelicError: jest.fn(),
  Feature: { PRODUCT_RECOMMENDATION: 'PRODUCT_RECOMMENDATION' },
}));

jest.mock('../getBrandCID');
jest.mock('@ecom-next/shopping-bag/utils');

describe('<ProductRecommendations />', () => {
  const mockBagState = {
    loading: 'SUCCESS',
    data: {
      productList: [{ sku: '123' }, { sku: '456' }],
    },
    error: null,
  };
  afterAll(() => {
    jest.restoreAllMocks();
  });

  it('should pass error CID if cannot map brand to CID', () => {
    const getBrandCIDMock = jest.spyOn(getBrandCIDModule, 'getBrandCID').mockImplementation(() => undefined);

    (usePageContext as jest.Mock).mockReturnValue({
      market: 'us',
      locale: 'en_US',
      brand: 'gap',
    });

    (useProductRecommendations as jest.Mock).mockReturnValue({
      getAiRecommendations: jest.fn(),
    });

    (useShoppingBagFeatures as jest.Mock).mockReturnValue({
      isAiRecsEnabled: true,
    });

    render(
      <ActiveBagProvider bagState={mockBagState} asyncDispatch={jest.fn()}>
        <ProductRecommendations />
      </ActiveBagProvider>
    );

    expect(Marketing).toHaveBeenCalledWith(
      expect.objectContaining({
        brand: 'gap',
        market: 'us',
        locale: 'en_US',
        cid: 'error',
        pageType: 'shoppingbag',
        slot: 'error/productrecommendations',
        onRender: expect.any(Function),
        onError: expect.any(Function),
        defaultMarketing: expect.anything(),
        errorCallback: expect.any(Function),
      }),
      {}
    );

    getBrandCIDMock.mockRestore();
  });

  it('should pass correct props based on context props', () => {
    const getBrandCIDMock = jest.spyOn(getBrandCIDModule, 'getBrandCID').mockImplementation(() => '2078');
    (usePageContext as jest.Mock).mockReturnValue({
      market: 'us',
      locale: 'en_US',
      brand: 'gap',
    });

    (useProductRecommendations as jest.Mock).mockReturnValue({
      getAiRecommendations: jest.fn(),
    });

    (useShoppingBagFeatures as jest.Mock).mockReturnValue({
      isAiRecsEnabled: true,
    });

    render(
      <ActiveBagProvider bagState={mockBagState} asyncDispatch={jest.fn()}>
        <ProductRecommendations />
      </ActiveBagProvider>
    );

    expect(Marketing).toHaveBeenCalledWith(
      expect.objectContaining({
        brand: 'gap',
        market: 'us',
        locale: 'en_US',
        cid: '2078',
        pageType: 'shoppingbag',
        slot: '2078/productrecommendations',
        onRender: expect.any(Function),
        onError: expect.any(Function),
        defaultMarketing: expect.anything(),
        errorCallback: expect.any(Function),
      }),
      {}
    );

    getBrandCIDMock.mockRestore();
  });

  it('should call onError and set hasError to true when handleError is called', () => {
    const onErrorMock = jest.fn();
    const { result } = renderHook(() => {
      const [hasError, setHasError] = React.useState(false);
      const handleError = (error: Error) => {
        setHasError(true);
        onErrorMock(error);
      };
      return { handleError, hasError };
    });

    act(() => {
      result.current.handleError(new Error('Test Error'));
    });

    expect(result.current.hasError).toBe(true);
    expect(onErrorMock).toHaveBeenCalledWith(new Error('Test Error'));
  });

  it('should call onRender when Marketing component is rendered', () => {
    const onRenderMock = jest.fn();

    (usePageContext as jest.Mock).mockReturnValue({
      market: 'us',
      locale: 'en_US',
      brand: 'gap',
    });

    (useProductRecommendations as jest.Mock).mockReturnValue({
      getAiRecommendations: jest.fn(),
    });

    (useShoppingBagFeatures as jest.Mock).mockReturnValue({
      isAiRecsEnabled: true,
    });

    render(
      <ActiveBagProvider bagState={mockBagState} asyncDispatch={jest.fn()}>
        <ProductRecommendations />
      </ActiveBagProvider>
    );

    expect(Marketing).toHaveBeenCalledWith(
      expect.objectContaining({
        onRender: expect.any(Function),
      }),
      {}
    );
  });

  it('should render without crashing', () => {
    (usePageContext as jest.Mock).mockReturnValue({
      market: 'us',
      locale: 'en_US',
      brand: 'gap',
    });

    (useProductRecommendations as jest.Mock).mockReturnValue({
      getAiRecommendations: jest.fn(),
    });

    (useShoppingBagFeatures as jest.Mock).mockReturnValue({
      isAiRecsEnabled: true,
    });
    render(
      <ActiveBagProvider bagState={mockBagState} asyncDispatch={jest.fn()}>
        <ProductRecommendations />
      </ActiveBagProvider>
    );
    expect(Marketing).toHaveBeenCalled();
  });

  it('should call logNewRelicError when errorFallback is called', () => {
    (logNewRelicError as jest.Mock).mockImplementation(() => {});

    // Mock `getBrandCID` to return undefined (brand not found)
    jest.spyOn(getBrandCIDModule, 'getBrandCID').mockImplementation(() => undefined);

    (usePageContext as jest.Mock).mockReturnValue({
      market: 'us',
      locale: 'en_US',
      brand: 'gap',
    });

    (useProductRecommendations as jest.Mock).mockReturnValue({
      getAiRecommendations: jest.fn(),
    });

    (useShoppingBagFeatures as jest.Mock).mockReturnValue({
      isAiRecsEnabled: true,
    });

    const onErrorMock = jest.fn(() => {
      logNewRelicError(new Error('Error loading Product Recommendations'), {
        caller: 'ProductRecommendations',
        feature: Feature.PRODUCT_RECOMMENDATION,
        message: 'Failed to load Product Recommendations',
      });
    });

    render(
      <ActiveBagProvider bagState={mockBagState} asyncDispatch={jest.fn()}>
        <ProductRecommendations onError={onErrorMock} />
      </ActiveBagProvider>
    );

    // Simulate an error to trigger the onError callback
    act(() => {
      onErrorMock();
    });

    // Assert that `logNewRelicError` was called with the expected arguments
    expect(logNewRelicError).toHaveBeenCalledWith(
      expect.any(Error),
      expect.objectContaining({
        caller: 'ProductRecommendations',
        feature: 'PRODUCT_RECOMMENDATION',
        message: 'Failed to load Product Recommendations',
      })
    );
  });

  it('calls handleError when Marketing onError is triggered, calls onError prop, and sets hasError', () => {
    const onErrorMock = jest.fn();

    render(
      <ActiveBagProvider bagState={mockBagState} asyncDispatch={jest.fn()}>
        <ProductRecommendations onError={onErrorMock} />
      </ActiveBagProvider>
    );

    const marketingProps = (Marketing as jest.Mock).mock.calls[0][0];
    const onErrorFunc = marketingProps.onError;
    const testError = new Error('Test error');
    const fallbackElement = onErrorFunc(testError);

    expect(onErrorMock).toHaveBeenCalledWith(testError);
    expect(React.isValidElement(fallbackElement)).toBe(true);
    expect(fallbackElement.type).toBe(React.Fragment);
    expect(logNewRelicError).toHaveBeenCalledWith(
      expect.any(Error),
      expect.objectContaining({
        caller: 'ProductRecommendations',
        feature: Feature.PRODUCT_RECOMMENDATION,
        message: 'Failed to load Product Recommendations',
      })
    );
  });

  it('renders errorFallback when hasError state is true', () => {
    const { rerender, container } = render(
      <ActiveBagProvider bagState={mockBagState} asyncDispatch={jest.fn()}>
        <ProductRecommendations />
      </ActiveBagProvider>
    );

    const marketingProps = (Marketing as jest.Mock).mock.calls[0][0];
    const onErrorFunc = marketingProps.onError;

    act(() => {
      onErrorFunc(new Error('Trigger error'));
    });

    rerender(
      <ActiveBagProvider bagState={mockBagState} asyncDispatch={jest.fn()}>
        <ProductRecommendations />
      </ActiveBagProvider>
    );

    expect(logNewRelicError).toHaveBeenCalledWith(
      expect.any(Error),
      expect.objectContaining({
        caller: 'ProductRecommendations',
        feature: Feature.PRODUCT_RECOMMENDATION,
        message: 'Failed to load Product Recommendations',
      })
    );
  });

  it('calls getAiRecommendations with correct anchor_ids when productList has SKUs', async () => {
    const getAiRecommendationsMock = jest.fn();

    (usePageContext as jest.Mock).mockReturnValue({
      market: 'us',
      locale: 'en_US',
      brand: 'gap',
    });

    (useProductRecommendations as jest.Mock).mockReturnValue({
      getAiRecommendations: getAiRecommendationsMock,
    });

    (useShoppingBagFeatures as jest.Mock).mockReturnValue({
      isAiRecsEnabled: true,
    });

    const mockBagState = {
      loading: 'SUCCESS',
      data: {
        productList: [{ sku: 'abc123' }, { sku: 'def456' }],
      },
      error: null,
    };

    render(
      <ActiveBagProvider bagState={mockBagState} asyncDispatch={jest.fn()}>
        <ProductRecommendations />
      </ActiveBagProvider>
    );

    expect(getAiRecommendationsMock).toHaveBeenCalledWith({
      anchor_ids: 'abc123,def456',
      anchor_type: 'sku',
      page_type: 'CART',
    });
  });

  it('calls getAiRecommendations with empty anchor_ids when productList is missing', () => {
    const getAiRecommendationsMock = jest.fn();

    (usePageContext as jest.Mock).mockReturnValue({
      market: 'us',
      locale: 'en_US',
      brand: 'gap',
    });

    (useProductRecommendations as jest.Mock).mockReturnValue({
      getAiRecommendations: getAiRecommendationsMock,
    });

    (useShoppingBagFeatures as jest.Mock).mockReturnValue({
      isAiRecsEnabled: true,
    });

    const mockBagState = {
      loading: 'SUCCESS',
      data: {
        productList: [],
      },
      error: null,
    };

    render(
      <ActiveBagProvider bagState={mockBagState} asyncDispatch={jest.fn()}>
        <ProductRecommendations />
      </ActiveBagProvider>
    );

    expect(getAiRecommendationsMock).toHaveBeenCalledWith({
      anchor_ids: '',
      anchor_type: 'sku',
      page_type: 'CART',
    });
  });
});
