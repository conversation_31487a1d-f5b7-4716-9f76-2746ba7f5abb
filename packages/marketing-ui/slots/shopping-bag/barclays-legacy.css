iframe {
  min-height: auto !important;
}

.sds_visually-hidden,
.visually-hidden {
  display: none;
}

.sds-cb_font--primary {
  font-family: 'Source Sans Pro', Helvetica, Arial, Roboto, sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

:root {
  --color-black-1300: 17 17 17;
}

button.quick-add.add-to-bag.add-to-bag--disabled {
  color: rgb(255, 255, 255);
  background-color: rgb(51, 51, 51);
}

/** Color vars defined by pdp for edit-bag experience **/
#product-edit-detail {
  --color-tan-200: 237 232 224;
  --color-tan-200-disabled: 248 246 243;
  --color-blue-100: 3 27 161;
  --color-gray-100: 136 136 136;
  --color-gray-200: 117 117 117;
  --color-gray-300: 238 238 238;
  --color-gray-400: 212 212 212;
  --color-gray-1000: 94 92 90;
  --color-gray-1100: 89 89 89;
  --color-blue-link: 42 113 212;
  --color-red-1000: 208 2 27;
  --color-white-1000: 251 251 250;
  --color-black-1000: 42 42 42;
  --color-black-1100: 43 43 43;
  --color-black-1200: 48 53 51;
  --color-black-1400: 44 40 36;
}
