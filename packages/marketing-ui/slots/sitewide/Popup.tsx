'use client';
import Marketing from '@ecom-next/marketing-ui';
import { useAppState } from '@sitewide/providers/PageContextProvider';

export const Popup = () => {
  const { brand, market, locale } = useAppState();

  // cannot use jsx here because Promise() is not compatible with yet
  const component = Marketing({
    brand,
    cid: 'sitewide',
    locale,
    pageType: 'sitewide',
    slot: 'sitewide/popup',
    market,
    defaultMarketing: null,
  });
  return component;
};
