/* eslint-disable no-undef */
/* eslint-disable react/no-unknown-property */
import React, { ReactNode, ErrorInfo } from 'react';
import { styled } from '@ecom-next/core/react-stitch';

const ListItem = styled.li`
  list-style-type: circle;
`;

interface ErrorBoundaryState {
  error: null | Error;
  errorInfo: null | ErrorInfo;
  hasError: boolean;
}

export class StoryErrorBoundary extends React.Component<{ children: ReactNode }, ErrorBoundaryState> {
  constructor(props) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
    };
  }

  static getDerivedStateFromError(__: Error): Pick<ErrorBoundaryState, 'hasError'> {
    return { hasError: true };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    // You can also log the error to an error reporting service
    // eslint-disable-next-line no-console
    console.log('error caught in ErrorBoundary', error, errorInfo);
    this.setState({
      error,
      errorInfo,
    });
  }

  forceRefresh() {
    this.setState({ error: null, errorInfo: null });
  }

  render() {
    if (this.state.hasError) {
      return (
        <>
          <div css={{ marginTop: '50px', display: 'flex', justifyContent: 'center' }} data-testid='error-boundary'>
            <div css={{ textAlign: 'center', width: '80%' }}>
              <h1>We are unable to complete your visualization. Please continue authoring the content type until all required fields are filled.</h1>
              <br />
              <h1>A couple of things to look out for:</h1>
              <br />
              <ul>
                <ListItem>Background images are populated</ListItem>
                <ListItem>
                  <em>alt text</em> is authored for all images
                </ListItem>
                <ListItem>All CTAs have URLs</ListItem>
                <ListItem>All required icons are added</ListItem>
              </ul>
            </div>
          </div>
        </>
      );
    }

    return this.props.children;
  }
}
