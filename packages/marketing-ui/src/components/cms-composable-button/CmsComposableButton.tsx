import { Props as ButtonProps, ComposableButton } from '../composable-button/ComposableButton';
import { useCallback } from 'react';
import { useRouter } from 'next/navigation';
interface Props {
  buttonStyle: {
    buttonStyle: ButtonProps['variant'];
    buttonColor: ButtonProps['color'];
  };
  cta: {
    label: ButtonProps['label'];
    value: string;
  };
}

export const CmsComposableButton = ({ cta, buttonStyle }: Props) => {
  const { push } = useRouter();
  const onClick = useCallback(() => {
    if (cta.value) push(cta.value);
  }, [cta.value]);

  return <ComposableButton label={cta.label} onClick={onClick} variant={buttonStyle.buttonStyle} color={buttonStyle.buttonColor} />;
};
