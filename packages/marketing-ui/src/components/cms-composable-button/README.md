# CMS Composable Button

## Overview

The `CmsComposableButton` works as a simple mapper between CMS and the `ComposableButton` to re-map properties into the necessary properties used by the raw component.

## Usage

```tsx
import { CmsComposableButton } from '@next/marketing-ui';

const MyComponent = () => {
  const [buttonProps, setButtonProps] = useState();

  useEffect(() => {
    fetch('...some cms url...')
      .then(resp => resp.json())
      .then(data => setButtonProps(data));
  }, []);

  return <CmsComposableButton {...buttonProps} />;
};
```

## Note

Based on the response payload of CMS, we might have to adjust some mapping scenarios in the button.

Therefore, this is a continuous work in progress component.
