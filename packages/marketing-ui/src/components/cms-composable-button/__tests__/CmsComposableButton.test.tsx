import { fireEvent, render } from '@testing-library/react';
import { CmsComposableButton } from '../CmsComposableButton';

jest.mock('next/navigation', () => ({
  useRouter: () => ({
    prefetch: () => null,
  }),
}));
describe('CmsComposableButton', () => {
  it('should render', () => {
    const cta = { value: 'google.com', label: 'foo' };
    const { container, getByRole } = render(<CmsComposableButton buttonStyle={{ buttonStyle: 'solid', buttonColor: 'primary' }} cta={cta} />);
    expect(container).toBeDefined();
    expect(getByRole('button').textContent).toEqual(cta.label);
  });

  it('should click the button and call the handler', () => {
    const cta = { value: 'google.com', label: 'foo' };
    const spy = jest.fn();

    jest.spyOn(require('next/navigation'), 'useRouter').mockImplementation(() => ({
      push: spy,
    }));

    const { getByRole } = render(<CmsComposableButton buttonStyle={{ buttonStyle: 'solid', buttonColor: 'primary' }} cta={cta} />);
    fireEvent.click(getByRole('button'));
    expect(spy).toHaveBeenCalledWith(cta.value);
  });
});
