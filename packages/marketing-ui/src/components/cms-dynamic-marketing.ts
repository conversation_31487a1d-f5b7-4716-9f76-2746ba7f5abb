// @ts-nocheck
'use client';
// AUTO-GENERATED, DO NOT MODIFIED!!! generated using generate-components-map.js.

import { LazyExoticComponent, PropsWithChildren, lazy } from 'react';
import SpotlightVariableHeight from './legacy/CMS/content-types/SpotlightVariableHeight/index';
import CTADropdown from './legacy/CMS/content-types/CTADropdown/index';
export const CmsDynamicMarketingComponents = {
  'https://cms.gap.com/schema/content/v1/cta-or-dropdown.json': CTADropdown as LazyExoticComponent<PropsWithChildren>,
  'https://cms.gap.com/schema/content/v2/category-banner.json': lazy<React.PropsWithChildren>(() => import('./legacy/CMS/content-types/CategoryBanner/index')),
  'https://cms.gap.com/schema/content/v2/category-banner-shop-by-size.json': lazy<React.PropsWithChildren>(
    () => import('./legacy/CMS/content-types/CategoryBanner/index')
  ),
  'https://cms.gap.com/schema/v1/content/category-banner-price-cards.json': lazy<React.PropsWithChildren>(
    () => import('./legacy/CMS/content-types/CategoryBannerPriceCards/index')
  ),
  'https://cms.gap.com/schema/v1/content/category-banner-price-cards-carousel.json': lazy<React.PropsWithChildren>(
    () => import('./legacy/CMS/content-types/CategoryBannerPriceCardsCarousel/index')
  ),
  'https://cms.gap.com/schema/content/v1/category-banner.json': lazy<React.PropsWithChildren>(
    () => import('./legacy/CMS/content-types/CategoryBannerV1/index')
  ),
  'https://cms.gap.com/schema/content/v1/category-banner-shop-by-size.json': lazy<React.PropsWithChildren>(
    () => import('./legacy/CMS/content-types/CategoryBannerV1/index')
  ),
  'https://cms.gap.com/schema/content/v1/category-banner-variable-height-carousel.json': lazy<React.PropsWithChildren>(
    () => import('./legacy/CMS/content-types/CategoryBannerVariableHeightCarousel/index')
  ),
  'https://cms.gap.com/schema/content/v1/category-card-variable-grid.json': lazy<React.PropsWithChildren>(
    () => import('./legacy/CMS/content-types/CategoryCardVariableGrid/index')
  ),
  'https://cms.gap.com/schema/content/v1/category-cards.json': lazy<React.PropsWithChildren>(() => import('./legacy/CMS/content-types/CategoryCards/index')),
  'https://cms.gap.com/schema/content/v1/circle-navigation.json': lazy<React.PropsWithChildren>(
    () => import('./legacy/CMS/content-types/CircleNavigation/index')
  ),
  'https://cms.gap.com/schema/content/v1/community-feed.json': lazy<React.PropsWithChildren>(() => import('./legacy/CMS/content-types/CommunityFeed/index')),
  'https://cms.gap.com/schema/content/v1/community-spotlight.json': lazy<React.PropsWithChildren>(
    () => import('./legacy/CMS/content-types/CommunitySpotlight/index')
  ),
  'https://cms.gap.com/schema/content/v1/edfs.json': lazy<React.PropsWithChildren>(() => import('./legacy/CMS/content-types/EveryDayFreeShipping/index')),
  'https://cms.gap.com/schema/content/v1/featured-categories.json': lazy<React.PropsWithChildren>(
    () => import('./legacy/CMS/content-types/FeaturedCategories/index')
  ),
  'https://cms.gap.com/schema/content/v1/flexible-banner-hp.json': lazy<React.PropsWithChildren>(
    () => import('./legacy/CMS/content-types/FlexibleBannerHP/index')
  ),
  'https://cms.gap.com/schema/content/v1/headline.json': lazy<React.PropsWithChildren>(() => import('./legacy/CMS/content-types/Headline/index')),
  'https://cms.gap.com/schema/content/v1/ism-double-full-image.json': lazy<React.PropsWithChildren>(
    () => import('./legacy/CMS/content-types/ISMBannerFullImage/index')
  ),
  'https://cms.gap.com/schema/content/v1/ism-single-full-image.json': lazy<React.PropsWithChildren>(
    () => import('./legacy/CMS/content-types/ISMBannerFullImage/index')
  ),
  'https://cms.gap.com/schema/content/v1/ism-double-full-video.json': lazy<React.PropsWithChildren>(
    () => import('./legacy/CMS/content-types/ISMBannerFullVideo/index')
  ),
  'https://cms.gap.com/schema/content/v1/ism-single-full-video.json': lazy<React.PropsWithChildren>(
    () => import('./legacy/CMS/content-types/ISMBannerFullVideo/index')
  ),
  'https://cms.gap.com/schema/content/v1/ism-double-with-icons.json': lazy<React.PropsWithChildren>(
    () => import('./legacy/CMS/content-types/ISMBannerPartialIcons/index')
  ),
  'https://cms.gap.com/schema/content/v1/ism-double-partial-image.json': lazy<React.PropsWithChildren>(
    () => import('./legacy/CMS/content-types/ISMBannerPartialImage/index')
  ),
  'https://cms.gap.com/schema/content/v1/ism-single-partial-image.json': lazy<React.PropsWithChildren>(
    () => import('./legacy/CMS/content-types/ISMBannerPartialImage/index')
  ),
  'https://cms.gap.com/schema/content/v1/ism-double-partial-video.json': lazy<React.PropsWithChildren>(
    () => import('./legacy/CMS/content-types/ISMBannerPartialVideo/index')
  ),
  'https://cms.gap.com/schema/content/v1/ism-single-partial-video.json': lazy<React.PropsWithChildren>(
    () => import('./legacy/CMS/content-types/ISMBannerPartialVideo/index')
  ),
  'https://cms.gap.com/schema/content/v1/ism-double-partial-image-carousel.json': lazy<React.PropsWithChildren>(
    () => import('./legacy/CMS/content-types/ISMCarouselDoublePartialImage/index')
  ),
  'https://cms.gap.com/schema/content/v1/ism-single-full-image-carousel.json': lazy<React.PropsWithChildren>(
    () => import('./legacy/CMS/content-types/ISMCarouselFullImage/index')
  ),
  'https://cms.gap.com/schema/content/v1/ism-double-full-image-carousel.json': lazy<React.PropsWithChildren>(
    () => import('./legacy/CMS/content-types/ISMCarouselFullImage/index')
  ),
  'https://cms.gap.com/schema/content/v1/ism-single-partial-image-carousel.json': lazy<React.PropsWithChildren>(
    () => import('./legacy/CMS/content-types/ISMCarouselSinglePartialImage/index')
  ),
  'https://cms.gap.com/schema/content/v1/left-nav-tile.json': lazy<React.PropsWithChildren>(() => import('./legacy/CMS/content-types/LeftNavTile/index')),
  'https://cms.gap.com/schema/content/v1/overlapping-2-image-banner.json': lazy<React.PropsWithChildren>(
    () => import('./legacy/CMS/content-types/Overlapping2ImageBanner/index')
  ),
  'https://cms.gap.com/schema/content/v1/overlapping-3-image-banner.json': lazy<React.PropsWithChildren>(
    () => import('./legacy/CMS/content-types/Overlapping3ImageBanner/index')
  ),
  'https://cms.gap.com/schema/content/v1/partnerships-brand-stories.json': lazy<React.PropsWithChildren>(
    () => import('./legacy/CMS/content-types/PartnershipsAndBrandStories/index')
  ),
  'https://cms.gap.com/schema/content/v1/partnerships-brand-stories-video.json': lazy<React.PropsWithChildren>(
    () => import('./legacy/CMS/content-types/PartnershipsAndBrandStoriesVideo/index')
  ),
  'https://cms.gap.com/schema/content/v1/product-detail-page.json': lazy<React.PropsWithChildren>(
    () => import('./legacy/CMS/content-types/ProductDetailsPage/index')
  ),
  'https://cms.gap.com/schema/content/v1/promo-drawer.json': lazy<React.PropsWithChildren>(() => import('./legacy/CMS/content-types/PromoDrawer/index')),
  'https://cms.gap.com/schema/content/v1/promo-sticker.json': lazy<React.PropsWithChildren>(() => import('./legacy/CMS/content-types/PromoSticker/index')),
  'https://cms.gap.com/schema/content/v1/rich-text.json': lazy<React.PropsWithChildren>(() => import('./legacy/CMS/content-types/RichTextBanner/index')),
  'https://cms.gap.com/schema/content/v1/seo-rich-text.json': lazy<React.PropsWithChildren>(() => import('./legacy/CMS/content-types/SEORichText/index')),
  'https://cms.gap.com/schema/content/v1/shopping-bag.json': lazy<React.PropsWithChildren>(() => import('./legacy/CMS/content-types/ShoppingBag/index')),
  'https://cms.gap.com/schema/content/v1/sitewide-banner.json': lazy<React.PropsWithChildren>(() => import('./legacy/CMS/content-types/SitewideBanner/index')),
  'https://cms.gap.com/schema/content/v1/sitewide-banner-carousel.json': lazy<React.PropsWithChildren>(
    () => import('./legacy/CMS/content-types/SitewideBannerCarousel/index')
  ),
  'https://cms.gap.com/schema/content/v2/sitewide-banner-countdown-clock.json': lazy<React.PropsWithChildren>(
    () => import('./legacy/CMS/content-types/SitewideBannerCountdownClock/index')
  ),
  'https://cms.gap.com/schema/content/v1/spotlight-carousel.json': lazy<React.PropsWithChildren>(
    () => import('./legacy/CMS/content-types/SpotlightCarousel/index')
  ),
  'https://cms.gap.com/schema/content/v1/spotlight.json': lazy<React.PropsWithChildren>(() => import('./legacy/CMS/content-types/SpotlightImage/index')),
  'https://cms.gap.com/schema/content/v1/spotlight-split-view.json': lazy<React.PropsWithChildren>(
    () => import('./legacy/CMS/content-types/SpotlightSplitView/index')
  ),
  'https://cms.gap.com/schema/content/v1/spotlight-variable-height.json': SpotlightVariableHeight as LazyExoticComponent<PropsWithChildren>,
  'https://cms.gap.com/schema/content/v1/spotlight-variable-height-video.json': lazy<React.PropsWithChildren>(
    () => import('./legacy/CMS/content-types/SpotlightVariableHeightVideo/index')
  ),
  'https://cms.gap.com/schema/content/v1/spotlight-video.json': lazy<React.PropsWithChildren>(() => import('./legacy/CMS/content-types/SpotlightVideo/index')),
  'https://cms.gap.com/schema/content/v1/storytelling-product-collection.json': lazy<React.PropsWithChildren>(
    () => import('./legacy/CMS/content-types/StorytellingAndProductCollection/index')
  ),
  'https://cms.gap.com/schema/content/v1/storytelling-product-collection-video.json': lazy<React.PropsWithChildren>(
    () => import('./legacy/CMS/content-types/StorytellingAndProductCollectionVideo/index')
  ),
  'https://cms.gap.com/schema/content/v1/storytelling-product-rating.json': lazy<React.PropsWithChildren>(
    () => import('./legacy/CMS/content-types/StorytellingAndProductRating/index')
  ),
  'https://cms.gap.com/schema/content/v1/storytelling-product-rating-video.json': lazy<React.PropsWithChildren>(
    () => import('./legacy/CMS/content-types/StorytellingAndProductRatingVideo/index')
  ),
  'https://cms.gap.com/schema/content/v1/sub-category-banner-image-with-text.json': lazy<React.PropsWithChildren>(
    () => import('./legacy/CMS/content-types/SubCategoryBanner/index')
  ),
  'https://cms.gap.com/schema/content/v1/sub-category-banner-variable-height-carousel.json': lazy<React.PropsWithChildren>(
    () => import('./legacy/CMS/content-types/SubCategoryBannerVariableHeightCarousel/index')
  ),
  'https://cms.gap.com/schema/content/v1/sub-category-banner-with-icons.json': lazy<React.PropsWithChildren>(
    () => import('./legacy/CMS/content-types/SubCategoryBannerWithIcons/index')
  ),
  'https://cms.gap.com/schema/content/v1/text-navigation-carousel.json': lazy<React.PropsWithChildren>(
    () => import('./legacy/CMS/content-types/TextNavigation/index')
  ),
  'https://cms.gap.com/schema/content/v1/text-navigation-exposed.json': lazy<React.PropsWithChildren>(
    () => import('./legacy/CMS/content-types/TextNavigation/index')
  ),
  'https://cms.gap.com/schema/content/v1/text-navigation-dropdown.json': lazy<React.PropsWithChildren>(
    () => import('./legacy/CMS/content-types/TextNavigation/index')
  ),
  'https://cms.gap.com/schema/content/v1/vertical-spacer.json': lazy<React.PropsWithChildren>(() => import('./legacy/CMS/content-types/VerticalSpacer/index')),
  'https://cms.gap.com/schema/content/v2/visual-navigation-carousel-v2.json': lazy<React.PropsWithChildren>(
    () => import('./legacy/CMS/content-types/VisualNavigationCarouselV2/index')
  ),
  'https://cms.gap.com/schema/content/v1/wayfinding-and-product-cards.json': lazy<React.PropsWithChildren>(
    () => import('./legacy/CMS/content-types/WayfindingAndProductCards/index')
  ),
  'https://cms.gap.com/schema/content/v1/visual-navigation.json': lazy<React.PropsWithChildren>(
    () => import('./legacy/CMS/content-types/VisualNavigation/VisualNavigation/index')
  ),
  'https://cms.gap.com/schema/content/v1/visual-navigation-size-toggle.json': lazy<React.PropsWithChildren>(
    () => import('./legacy/CMS/content-types/VisualNavigation/VisualNavigationSizeToggle/index')
  ),
  'https://cms.gap.com/schema/content/v1/visual-navigation-size-toggle-carousel.json': lazy<React.PropsWithChildren>(
    () => import('./legacy/CMS/content-types/VisualNavigationCarousel/SizeToggle/index')
  ),
  'https://cms.gap.com/schema/content/v1/visual-navigation-carousel.json': lazy<React.PropsWithChildren>(
    () => import('./legacy/CMS/content-types/VisualNavigationCarousel/VisualNavigationCarousel/index')
  ),
  'https://cms.gap.com/schema/content/v1/visual-navigation-carousel-with-price.json': lazy<React.PropsWithChildren>(
    () => import('./legacy/CMS/content-types/VisualNavigationCarousel/WithPrice/index')
  ),
  'https://cms.gap.com/schema/content/v1/category-banner-more-to-shop.json': lazy<React.PropsWithChildren>(
    () => import('./legacy/CMS/components/CategoryBannerMoreToShop/index')
  ),
  'https://cms.gap.com/schema/content/v1/category-banner-variable-height.json': lazy<React.PropsWithChildren>(
    () => import('./legacy/CMS/components/CategoryBannerVariableHeight/index')
  ),
  'https://cms.gap.com/schema/content/v1/sub-category-banner-variable-height.json': lazy<React.PropsWithChildren>(
    () => import('./legacy/CMS/components/CategoryBannerVariableHeight/index')
  ),
  'https://cms.gap.com/schema/content/v1/category-link-ribbon-banner.json': lazy<React.PropsWithChildren>(
    () => import('./legacy/CMS/components/CategoryLinkRibbonBanner/index')
  ),
  'https://cms.gap.com/schema/content/v1/seo-category-description.json': lazy<React.PropsWithChildren>(
    () => import('./legacy/CMS/components/CategorySeoDescription/index')
  ),
  'https://cms.gap.com/schema/content/v1/mobileapp-image-tile.json': lazy<React.PropsWithChildren>(() => import('./rapid-authoring/NewImageTile/index')),
  'https://cms.gap.com/schema/content/v1/mobileapp-video-tile.json': lazy<React.PropsWithChildren>(() => import('./rapid-authoring/NewVideoTile/index')),
  'https://cms.gap.com/schema/content/v1/visual-navigation-with-price.json': lazy<React.PropsWithChildren>(
    () => import('./legacy/CMS/components/VisualNavigation/index')
  ),
  'https://cms.gap.com/schema/content/v1/mobileapp-category-carousel.json': lazy<React.PropsWithChildren>(
    () => import('./rapid-authoring/FeaturedCategories/index')
  ),
  'https://cms.gap.com/schema/content/v1/mobileapp-product-carousel.json': lazy<React.PropsWithChildren>(() => import('./rapid-authoring/ProductFeed/index')),
  'https://cms.gap.com/schema/content/v1/mobileapp-image-tile.json': lazy<React.PropsWithChildren>(() => import('./rapid-authoring/NewImageTile/index')),
  'https://cms.gap.com/schema/content/v1/mobileapp-video-tile.json': lazy<React.PropsWithChildren>(() => import('./rapid-authoring/NewVideoTile/index')),
  'https://cms.gap.com/schema/content/v1/json-marketing.json': (props: React.PropsWithChildren) => props.children,
};
export type CmsMarketingComponents = typeof CmsDynamicMarketingComponents;
