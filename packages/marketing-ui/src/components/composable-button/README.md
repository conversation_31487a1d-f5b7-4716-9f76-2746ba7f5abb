# Composable Button

## Overview

The `ComposableButton` provides a quick implementation of a business-defined button or CTA (call-to-action). It's a centralized place where a button will follow the look and feel of the branded buttons and link-like buttons.

## Usage

```tsx
import { ComposableButton } from '@next/marketing-ui';

const MyComponent = () => {
  return (
    <>
      <div>{'Hello World'}</div>
      <ComposableButton label='Continue' onClick={() => doSomething()} color='primary' variant='solid' />
    </>
  );
};
```

## Branding

The branding of the button already listens to the general provider, so when the user navigates from brand to brand, the button will follow the necessary look and feel.
