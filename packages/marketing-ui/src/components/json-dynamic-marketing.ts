// @ts-nocheck
'use client';
// AUTO-GENERATED, DO NOT MODIFIED!!! generated using generate-components-map.js.

import { lazy } from 'react';
import LayeredContentModule from './legacy/components/LayeredContentModule';
import ComposableButton from './legacy/components/ComposableButton';
import LinkWithModal from './legacy/components/LinkWithModal';
import SVGImage from './legacy/components/SVGImage';
import SVGOverlay from './legacy/components/SVGOverlay';
import TextHeadline from './legacy/components/TextHeadline';
import TextOverlay from './legacy/components/TextOverlay';
import { JsonProps } from './json-marketing.client';
export type JsonMarketingComponentNames =
  | 'Animation'
  | 'ButtonDropdown'
  | 'ButtonList'
  | 'CSSAnimation'
  | 'CTALinksContainer'
  | 'Carousel'
  | 'ComposableButton'
  | 'ComposableButtonBR'
  | 'CreditCardOffer'
  | 'DetailsLink'
  | 'DetailsLinkWithPrefix'
  | 'Dismissible'
  | 'EmbeddedVideo'
  | 'HoverImage'
  | 'HoverImageWithTransition'
  | 'InSortMarketingBanner'
  | 'JumpLink'
  | 'LayeredContentModule'
  | 'LinkWithModal'
  | 'MarketingBanner'
  | 'ModalTrigger'
  | 'PixleeModule'
  | 'Recommendations'
  | 'RedpointPlaceholder'
  | 'SVGImage'
  | 'SVGOverlay'
  | 'SignInCta'
  | 'Tabs'
  | 'TapToApply'
  | 'TextHeadline'
  | 'TextOverlay'
  | 'TriggerableModal'
  | 'VideoComponent';
export const DynamicMarketingComponents = {
  Animation: lazy<JsonProps>(() => import('./legacy/components/Animation')),
  ButtonDropdown: lazy<JsonProps>(() => import('./legacy/components/ButtonDropdown')),
  ButtonList: lazy<JsonProps>(() => import('./legacy/components/ButtonList')),
  CSSAnimation: lazy<JsonProps>(() => import('./legacy/components/CSSAnimation')),
  CTALinksContainer: lazy<JsonProps>(() => import('./legacy/components/CTALinksContainer')),
  Carousel: lazy<JsonProps>(() => import('./legacy/components/Carousel')),
  ComposableButton,
  ComposableButtonBR: lazy<JsonProps>(() => import('./legacy/components/ComposableButtonBR')),
  CreditCardOffer: lazy<JsonProps>(() => import('./legacy/components/CreditCardOffer')),
  DetailsLink: lazy<JsonProps>(() => import('./legacy/components/DetailsLink')),
  DetailsLinkWithPrefix: lazy<JsonProps>(() => import('./legacy/components/DetailsLinkWithPrefix')),
  Dismissible: lazy<JsonProps>(() => import('./legacy/components/Dismissible')),
  EmbeddedVideo: lazy<JsonProps>(() => import('./legacy/components/EmbeddedVideo')),
  HoverImage: lazy<JsonProps>(() => import('./legacy/components/HoverImage')),
  HoverImageWithTransition: lazy<JsonProps>(() => import('./legacy/components/HoverImageWithTransition')),
  InSortMarketingBanner: lazy<JsonProps>(() => import('./legacy/components/InSortMarketingBanner')),
  JumpLink: lazy<JsonProps>(() => import('./legacy/components/JumpLink')),
  LayeredContentModule,
  LinkWithModal,
  MarketingBanner: lazy<JsonProps>(() => import('./legacy/components/MarketingBanner')),
  ModalTrigger: lazy<JsonProps>(() => import('./legacy/components/ModalTrigger')),
  PixleeModule: lazy<JsonProps>(() => import('./legacy/components/PixleeModule')),
  Recommendations: lazy<JsonProps>(() => import('./legacy/components/Recommendations')),
  RedpointPlaceholder: lazy<JsonProps>(() => import('./legacy/components/RedpointPlaceholder')),
  SVGImage,
  SVGOverlay,
  SignInCta: lazy<JsonProps>(() => import('./legacy/components/SignInCta')),
  Tabs: lazy<JsonProps>(() => import('./legacy/components/Tabs')),
  TapToApply: lazy<JsonProps>(() => import('./legacy/components/TapToApply')),
  TextHeadline,
  TextOverlay,
  TriggerableModal: lazy<JsonProps>(() => import('./legacy/components/TriggerableModal')),
  VideoComponent: lazy<JsonProps>(() => import('./legacy/components/VideoComponent')),
};
export type JsonMarketingComponents = typeof DynamicMarketingComponents;
