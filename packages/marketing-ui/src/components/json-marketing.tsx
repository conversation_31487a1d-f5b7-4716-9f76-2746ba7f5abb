'use client';
import React, { lazy } from 'react';
import dynamic from 'next/dynamic';
import { CSSObject } from '@emotion/react';
import { AnimatedHeadline } from '@sitewide/components/animated-headline';
import { DynamicModal } from '@sitewide/components/legacy/dynamic-modal';
import Headline from '@sitewide/components/headline';
import { FlexHeadLineHUIRewrite as FlexHeadline } from '@sitewide/components/flex-headline';
import { Clienteling } from '@sitewide/components/legacy/clienteling';
import { AcquisitionBanner } from '@sitewide/components/acquisition/acquisition-banner';
import AcquisitionRover from '@sitewide/components/legacy/acquisition/acquisition-rover';
import { AcquisitionModal } from '@sitewide/components/legacy/acquisition/acquisition-modal';
import { BrandLogo } from '@sitewide/components/legacy/brand-logo';
import { SisterBrandsWrapper } from '@sitewide/components/legacy/navigation/sister-brands-bar';
import HamburgerNav from '@sitewide/components/ham-nav';
import UtilityLinks from '@sitewide/components/legacy/brand-bar/UtilityLinks';
import { EdfsFooter } from '@sitewide/components/legacy/edfs';
import { EdfsLarge, EdfsSmall } from '@sitewide/components/edfs';
import { EmailPopup, EmailRegistrationForm, MaternityForm, SmsForm } from '@sitewide/components/legacy/customer-aquisition';
import { Page } from '@ecom-next/utils/server';
import { Footer } from '@sitewide/components/legacy/footer/entry';
import type { JsonComponentsMap } from '../../cms-component-types';
import BuiltIn from './legacy/components/BuiltIn';
import HTMLInjectionComponent from './legacy/components/HTMLInjectionComponent';
import LayoutComponent from './legacy/components/LayoutComponent';
import Image from './legacy/components/Image';

import Link from './legacy/components/Link';

import OptimizelyPlaceholder from './legacy/components/OptimizelyPlaceholder';
// import MegaNav from '@sitewide/components/legacy/navigation/mega-nav';
import { DynamicMarketing as JsonOrCmsMarketing } from './json-marketing.client';
import { DynamicMarketingComponents } from './json-dynamic-marketing';
import { useEnabledFeatures } from '@ecom-next/core/react-stitch';

export type JsonProps<T = unknown> = JsonBaseProps & ContentResolverProps & T;

const MegaNav = lazy(() => import('@sitewide/components/legacy/navigation/mega-nav/MegaNav'));
const Countdown = dynamic(() => import('@sitewide/components/countdown'), { ssr: false });

const PromoDrawer = dynamic(() => import('@sitewide/components/legacy/promotions/promo-drawer'), {
  ssr: false,
});
const PromoSticker = dynamic(() => import('@sitewide/components/legacy/promo-sticker/PromoSticker'), { ssr: false });

const UserGreeting = dynamic(() => import('@sitewide/components/acquisition/acquisition-banner/UserGreeting'), { ssr: false });

function MultiComponent(props: JsonProps) {
  const { components = [] } = props;
  const contents = components.map((data, index) => <JsonOrCmsMarketing key={`multi-content-${index}`} {...data} />);

  return contents;
}

const COMPONENTS_MAP = {
  AcquisitionBanner: AcquisitionBanner,
  AcquisitionRover: AcquisitionRover,
  AcquisitionModal: AcquisitionModal,
  AnimatedHeadline: AnimatedHeadline,
  Countdown: Countdown,
  Clienteling: Clienteling,
  DynamicModal: DynamicModal,
  EdfsFooter: EdfsFooter,
  EdfsLarge: EdfsLarge,
  EdfsSmall: EdfsSmall,
  EmailPopUp: EmailPopup,
  EmailRegistrationForm: EmailRegistrationForm,
  FlexHeadline: FlexHeadline,
  // "Footer": "Footer",
  HamburgerNav,
  Headline,
  Logo: BrandLogo,
  MaternityForm: MaternityForm,
  MegaNav,
  FooterMulti: MultiComponent,
  Footer,
  MktComponentEdfsFooter: EdfsFooter,
  MktEdfsLarge: EdfsLarge,
  MktEdfsSmall: EdfsSmall,
  MktSticker: PromoSticker,
  PromoDrawer: PromoDrawer,
  PromoDrawerComponentV2: PromoDrawer,
  PromoSticker: PromoSticker,
  SmsForm: SmsForm,
  SMSForm: SmsForm,
  UtilityLinks,
  SisterBrandsBar: SisterBrandsWrapper,
  UserGreeting: UserGreeting,
  BuiltIn,
  HTMLInjectionComponent,
  LayoutComponent,
  Image,
  Link,
  HomeSVGOverlay: DynamicMarketingComponents.SVGOverlay,
  OptimizelyPlaceholder,
  MultiComponent,
  HomeMultiSimple: MultiComponent,
};

export interface DefaultHeight {
  large: number | string;
  small: number | string;
}

export interface FixedHeight {
  desktop: number | string;
  mobile: number | string;
}

type DynamicMarketingComponentNames = keyof JsonComponentsMap;
type SitewideMarketingComponentNames = keyof typeof COMPONENTS_MAP;
interface JsonBaseProps {
  children?: JSX.Element;
  components?: JsonBaseProps[];
  data?: JsonBaseProps;
  defaultHeight?: DefaultHeight;
  experimentRunning?: boolean;
  fixedHeight?: FixedHeight;
  instanceName?: string;
  isDesktop?: boolean;
  lazy?: boolean;
  name: DynamicMarketingComponentNames | SitewideMarketingComponentNames;
  placeholderSettings?: {
    desktop: Record<string, string>;
    mobile: Record<string, string>;
  };
  shouldDisplay?: boolean;
  style?: CSSObject;
  type: string;

  useGreyLoadingEffect?: boolean;
}

interface ContentResolverProps {
  marketing?: JsonBaseProps;
  marketingType?: string;
  pageType?: Page;
}

const errorLogger =
  typeof window !== 'undefined'
    ? window.newrelic?.noticeError
    : // eslint-disable-next-line no-console
      console.error;

function JsonDynamicMarketing(props: JsonProps) {
  const { name, type, ...rest } = props;

  if (!name) return null;

  const normalizedName = type === 'builtin' ? 'BuiltIn' : name;

  const Component =
    DynamicMarketingComponents[normalizedName as DynamicMarketingComponentNames] || COMPONENTS_MAP[normalizedName as SitewideMarketingComponentNames];

  if (!Component) return null;

  return <Component name={name} {...rest} errorLogger={errorLogger} />;
}

export default JsonDynamicMarketing;

export const DynamicMarketing = JsonDynamicMarketing;

export type JsonMarketing = typeof JsonDynamicMarketing;
