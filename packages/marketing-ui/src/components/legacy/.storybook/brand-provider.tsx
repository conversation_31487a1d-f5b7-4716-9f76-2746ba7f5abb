// @ts-nocheck
'use client';
import React from 'react';
import { Brands } from '@ecom-next/core/react-stitch';

export const BrandContext = React.createContext<Brands>(Brands.Gap);

type Props = {
  brand: Brands;
  children: React.ReactNode;
};

const BrandProvider = ({ brand, children }: Props): JSX.Element => {
  return <BrandContext.Provider value={brand}>{children}</BrandContext.Provider>;
};

export default BrandProvider;
