// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`CategoryBannerMoreToShop Component should match snapshots in desktop view 1`] = `
.emotion-0 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  margin-bottom: 1.5rem;
  max-width: 768px;
  margin-left: auto;
  margin-right: 1rem;
}

@media (min-width: 1024px) {
  .emotion-0 {
    max-width: 1280px;
    padding-right: 1rem;
    padding-left: 15rem;
    margin-left: auto;
    margin-right: auto;
  }
}

.emotion-1 {
  width: 100%;
  height: auto;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
}

.emotion-2 {
  width: 100%;
  padding: 15px;
  box-sizing: border-box;
  border-top: 1px #2B2B2B;
  border-bottom: 1px #2B2B2B;
  border-style: solid none;
}

.emotion-3 .amp-cms--p {
  padding: 0;
  margin: 0;
  line-height: 0;
}

.emotion-3 .amp-cms--p>span {
  display: inline;
  white-space: break-spaces;
}

.emotion-3 .amp-cms--p a {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  position: relative;
  z-index: 2;
  pointer-events: auto;
}

.emotion-3 .amp-cms--p a:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-3 .amp-cms--legal-copy {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(0.9375vw, 12px));
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-3 sup {
  vertical-align: top;
  display: inline-block;
  margin-top: -0.25ex;
}

.emotion-3 sub {
  vertical-align: bottom;
  display: inline-block;
  margin-bottom: -0.25ex;
}

.emotion-3 .amp-cms--f-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.9375vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--f-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.9375vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--f-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.9375vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--fn-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.9375vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--body-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(1.5625vw, 20px));
  line-height: 1.3;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--body-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(1.40625vw, 18px));
  line-height: 1.2777777777777777;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--body-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(1.25vw, 16px));
  line-height: 1.25;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--body-4 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(1.09375vw, 14px));
  line-height: 1.2857142857142858;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--body-5 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(0.9375vw, 12px));
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--eyebrow-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(35px, min(3.90625vw, 50px));
  line-height: 0.92;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--eyebrow-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(35px, min(3.125vw, 40px));
  line-height: 1;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--eyebrow-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(35px, min(2.734375vw, 35px));
  line-height: 1;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--headline-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(9.375vw, 120px));
  line-height: 0.8333333333333334;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--headline-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(8.59375vw, 110px));
  line-height: 0.8636363636363636;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--headline-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(7.8125vw, 100px));
  line-height: 0.85;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--headline-4 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(7.8125vw, 100px));
  line-height: 1.05;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--headline-5 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(6.25vw, 80px));
  line-height: 0.9375;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--headline-6 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(5.46875vw, 70px));
  line-height: 0.9285714285714286;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--headline-7 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(4.6875vw, 60px));
  line-height: 0.9166666666666666;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--headlineAlt-1 {
  color: #2B2B2B;
  font-size: max(14px, min(8.90625vw, 114px));
  line-height: 1;
  letter-spacing: min(-0.03125vw, -0.4px);
  font-weight: 400;
}

.emotion-3 .amp-cms--headlineAlt-2 {
  color: #2B2B2B;
  font-size: max(14px, min(6.5625vw, 84px));
  line-height: 1;
  letter-spacing: min(-0.03125vw, -0.4px);
  font-weight: 400;
}

.emotion-3 .amp-cms--headlineAlt-3 {
  color: #2B2B2B;
  font-size: max(14px, min(5vw, 64px));
  line-height: 1;
  letter-spacing: min(-0.03125vw, -0.4px);
  font-weight: 400;
}

.emotion-3 .amp-cms--headlineAlt-4 {
  color: #2B2B2B;
  font-size: max(14px, min(2.65625vw, 34px));
  line-height: 1;
  letter-spacing: min(-0.03125vw, -0.4px);
  font-weight: 400;
}

.emotion-3 .amp-cms--headlineAlt-5 {
  color: #2B2B2B;
  font-size: min(1.25vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-3 .amp-cms--headlineAlt-6 {
  color: #2B2B2B;
  font-size: min(1.25vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-3 .amp-cms--headlineAlt-7 {
  color: #2B2B2B;
  font-size: min(1.25vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-3 .amp-cms--promo-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(7.8125vw, 100px));
  line-height: 1;
  letter-spacing: min(0vw, 0px);
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-3 .amp-cms--promo-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(6.25vw, 80px));
  line-height: 1;
  letter-spacing: min(0vw, 0px);
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-3 .amp-cms--promoAlt-1 {
  color: #2B2B2B;
  font-size: min(1.25vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 600;
}

.emotion-3 .amp-cms--subhead-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(2.34375vw, 30px));
  line-height: 1;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--subhead-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(1.953125vw, 25px));
  line-height: 1;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--subhead-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(1.5625vw, 20px));
  line-height: 1.1;
  letter-spacing: 0;
}

.emotion-4 {
  display: grid;
  grid-template-columns: 1fr;
  grid-gap: 1vw;
  margin-top: 0;
}

.emotion-5 {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr 1fr;
  grid-template-rows: 1fr;
  grid-gap: 0;
  position: relative;
}

.emotion-7 {
  position: relative;
  display: block;
  overflow: hidden;
  padding-bottom: 150%;
}

.emotion-9 {
  position: absolute;
  height: 100%;
  width: 100%;
  z-index: 1;
}

.emotion-11 {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: url(https://2hmc8flz4ofg1dy89cc38f2xm.staging.bigcontent.io/i/athleta/MessagingCardsC?fmt=auto&h=1640) no-repeat center center;
  -webkit-background-size: cover;
  background-size: cover;
}

.emotion-17 {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: url(https://2hmc8flz4ofg1dy89cc38f2xm.staging.bigcontent.io/i/athleta/MessagingCardsA?fmt=auto&h=1640) no-repeat center center;
  -webkit-background-size: cover;
  background-size: cover;
}

.emotion-23 {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: url(https://2hmc8flz4ofg1dy89cc38f2xm.staging.bigcontent.io/i/athleta/MessagingCardsB?fmt=auto&h=1640) no-repeat center center;
  -webkit-background-size: cover;
  background-size: cover;
}

.emotion-33 {
  width: 100%;
}

.emotion-37 {
  width: 90%;
  height: auto;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  text-align: left;
  color: #2B2B2B;
  margin: 6px auto 0 0;
  padding-left: 0;
  margin-bottom: 6px;
}

.emotion-38 {
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(1.25vw, 16px));
  line-height: 1.25;
  letter-spacing: 0;
}

.emotion-59 {
  position: relative;
  bottom: 0;
  min-height: 40px;
  text-align: left;
  width: 100%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  margin-right: auto;
  padding-bottom: 16px;
  max-width: 100%;
  -webkit-box-flex-wrap: wrap;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
}

.emotion-60 {
  z-index: 2;
  width: 100%;
  display: inline-block;
}

.emotion-61 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  display: inline-block;
  text-transform: none;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 1px solid transparent;
  border-radius: 0;
  max-width: 300px;
  font-size: 12px;
  font-weight: 500;
  letter-spacing: 2%;
  min-height: auto;
  max-height: auto;
  line-height: 12px;
  padding: 0;
  width: auto;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: left;
  border-left: none;
  border-right: none;
  border-radius: 0;
  background-color: transparent;
  border-bottom: 0;
  -webkit-box-pack: left;
  -ms-flex-pack: left;
  -webkit-justify-content: left;
  justify-content: left;
  color: #000000;
  height: auto;
  -webkit-text-decoration: underline;
  text-decoration: underline;
  text-underline-offset: 3.3px;
  text-decoration-thickness: 1.6px;
  -webkit-align-self: flex-start;
  -ms-flex-item-align: flex-start;
  align-self: flex-start;
  padding: 7px;
  white-space: normal;
  text-transform: none;
  white-space: initial;
  width: 100%;
  padding: 0;
  margin: 0;
  display: inline;
  line-height: 1.5em;
  text-align: left;
  text-underline-offset: 1px;
}

.emotion-61:focus {
  outline: none;
}

.emotion-61>span {
  padding: 1px 0;
}

.emotion-61:hover,
.emotion-61:focus {
  -webkit-text-decoration: unset;
  text-decoration: unset;
}

.emotion-61:active {
  -webkit-text-decoration: unset;
  text-decoration: unset;
}

.emotion-62 {
  box-sizing: border-box;
}

<div>
  <div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      class="emotion-0"
    >
      <div
        class="emotion-1"
      >
        <div
          class="emotion-2"
        >
          <div
            class="emotion-3"
          >
            <div>
              <p
                class="amp-cms--p amp-cms--text-center"
              >
                <span
                  class="amp-cms--headline-7"
                >
                  MORE TO SHOP. MORE TO LOVE.
                </span>
              </p>
            </div>
          </div>
        </div>
        <div
          class="emotion-4"
          role="group"
        >
          <div
            class="emotion-5 emotion-6"
          >
            <div
              class="emotion-7 emotion-8"
            >
              <a
                class="emotion-9 emotion-10"
                href="test 1"
                target="_self"
                title="test 1"
              />
              <div
                aria-label="image1"
                class="emotion-11 emotion-12"
                data-testid="cardImageTestId"
                role="img"
              />
            </div>
            <div
              class="emotion-7 emotion-8"
            >
              <a
                class="emotion-9 emotion-10"
                href="test 2"
                target="_self"
                title="test 2"
              />
              <div
                aria-label="Joggers"
                class="emotion-17 emotion-12"
                data-testid="cardImageTestId"
                role="img"
              />
            </div>
            <div
              class="emotion-7 emotion-8"
            >
              <a
                class="emotion-9 emotion-10"
                href="test 3"
                target="_self"
                title="test 3"
              />
              <div
                aria-label="Test3"
                class="emotion-23 emotion-12"
                data-testid="cardImageTestId"
                role="img"
              />
            </div>
            <div
              class="emotion-7 emotion-8"
            >
              <a
                class="emotion-9 emotion-10"
                href="test 4"
                target="_self"
                title="test 4"
              />
              <div
                aria-label="IMAGE4"
                class="emotion-17 emotion-12"
                data-testid="cardImageTestId"
                role="img"
              />
            </div>
          </div>
          <div
            class="emotion-5 emotion-6"
          >
            <div
              class="emotion-33 emotion-34"
            >
              <a
                class="emotion-9 emotion-10"
                href="test 1"
                target="_self"
                title="test 1"
              />
              <div
                class="emotion-37"
              >
                <span
                  class="emotion-38"
                >
                  Ultra High Rise Tight
                </span>
              </div>
            </div>
            <div
              class="emotion-33 emotion-34"
            >
              <a
                class="emotion-9 emotion-10"
                href="test 2"
                target="_self"
                title="test 2"
              />
              <div
                class="emotion-37"
              >
                <span
                  class="emotion-38"
                >
                  Ultra High Rise.
                </span>
              </div>
            </div>
            <div
              class="emotion-33 emotion-34"
            >
              <a
                class="emotion-9 emotion-10"
                href="test 3"
                target="_self"
                title="test 3"
              />
              <div
                class="emotion-37"
              >
                <span
                  class="emotion-38"
                >
                  Ultra High Rise Elation tight
                </span>
              </div>
            </div>
            <div
              class="emotion-33 emotion-34"
            >
              <a
                class="emotion-9 emotion-10"
                href="test 4"
                target="_self"
                title="test 4"
              />
              <div
                class="emotion-37"
              >
                <span
                  class="emotion-38"
                />
              </div>
            </div>
          </div>
          <div
            class="emotion-5 emotion-6"
          >
            <div
              class="emotion-59"
            >
              <div
                class="emotion-60"
              >
                <a
                  class="emotion-61"
                  color="dark"
                  href="/CTALink1"
                >
                  <span
                    class="emotion-62"
                  >
                    CTA Link 1
                  </span>
                </a>
              </div>
              <div
                class="emotion-60"
              >
                <a
                  class="emotion-61"
                  color="dark"
                  href="/CTALink2"
                >
                  <span
                    class="emotion-62"
                  >
                    CTA Link 2
                  </span>
                </a>
              </div>
            </div>
            <div
              class="emotion-59"
            >
              <div
                class="emotion-60"
              >
                <a
                  class="emotion-61"
                  color="dark"
                  href="/CTALink1"
                >
                  <span
                    class="emotion-62"
                  >
                    CTA Link 3
                  </span>
                </a>
              </div>
              <div
                class="emotion-60"
              >
                <a
                  class="emotion-61"
                  color="dark"
                  href="/CTALink2"
                >
                  <span
                    class="emotion-62"
                  >
                    CTA Link 4
                  </span>
                </a>
              </div>
            </div>
            <div
              class="emotion-59"
            />
            <div
              class="emotion-59"
            >
              <div
                class="emotion-60"
              >
                <a
                  class="emotion-61"
                  color="dark"
                  href="/CTALink1"
                >
                  <span
                    class="emotion-62"
                  >
                    CTA Link 7
                  </span>
                </a>
              </div>
              <div
                class="emotion-60"
              >
                <a
                  class="emotion-61"
                  color="dark"
                  href="/CTALink2"
                >
                  <span
                    class="emotion-62"
                  >
                    CTA Link 8
                  </span>
                </a>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`CategoryBannerMoreToShop Component should match snapshots in desktop view for Gap Factory brand 1`] = `
.emotion-0 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  margin-bottom: 1.5rem;
  max-width: 768px;
  margin-left: auto;
  margin-right: 1rem;
}

@media (min-width: 1024px) {
  .emotion-0 {
    max-width: 1280px;
    padding-right: 1rem;
    padding-left: 15rem;
    margin-left: auto;
    margin-right: auto;
  }
}

.emotion-1 {
  width: 100%;
  height: auto;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
}

.emotion-2 {
  width: 100%;
  padding: 15px;
  box-sizing: border-box;
  border-top: 1px #2B2B2B;
  border-bottom: 1px #2B2B2B;
  border-style: solid none;
}

.emotion-3 .amp-cms--p {
  padding: 0;
  margin: 0;
  line-height: 0;
}

.emotion-3 .amp-cms--p>span {
  display: inline;
  white-space: break-spaces;
}

.emotion-3 .amp-cms--p a {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  position: relative;
  z-index: 2;
  pointer-events: auto;
}

.emotion-3 .amp-cms--p a:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-3 .amp-cms--legal-copy {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(0.9375vw, 12px));
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-3 sup {
  vertical-align: top;
  display: inline-block;
  margin-top: -0.25ex;
}

.emotion-3 sub {
  vertical-align: bottom;
  display: inline-block;
  margin-bottom: -0.25ex;
}

.emotion-3 .amp-cms--f-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.9375vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--f-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.9375vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--f-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.9375vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--fn-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.9375vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--body-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(1.5625vw, 20px));
  line-height: 1.3;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--body-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(1.40625vw, 18px));
  line-height: 1.2777777777777777;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--body-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(1.25vw, 16px));
  line-height: 1.25;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--body-4 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(1.09375vw, 14px));
  line-height: 1.2857142857142858;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--body-5 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(0.9375vw, 12px));
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--eyebrow-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(35px, min(3.90625vw, 50px));
  line-height: 0.92;
  letter-spacing: 0;
  font-weight: 500;
}

.emotion-3 .amp-cms--eyebrow-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(35px, min(3.125vw, 40px));
  line-height: 1;
  letter-spacing: 0;
  font-weight: 500;
}

.emotion-3 .amp-cms--eyebrow-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(35px, min(2.734375vw, 35px));
  line-height: 1;
  letter-spacing: 0;
  font-weight: 500;
}

.emotion-3 .amp-cms--headline-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(9.375vw, 120px));
  line-height: 0.8333333333333334;
  letter-spacing: 0;
  font-weight: 500;
}

.emotion-3 .amp-cms--headline-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(8.59375vw, 110px));
  line-height: 0.8636363636363636;
  letter-spacing: 0;
  font-weight: 500;
}

.emotion-3 .amp-cms--headline-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(7.8125vw, 100px));
  line-height: 0.85;
  letter-spacing: 0;
  font-weight: 500;
}

.emotion-3 .amp-cms--headline-4 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(7.8125vw, 100px));
  line-height: 1.05;
  letter-spacing: 0;
  font-weight: 500;
}

.emotion-3 .amp-cms--headline-5 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(6.25vw, 80px));
  line-height: 0.9375;
  letter-spacing: 0;
  font-weight: 500;
}

.emotion-3 .amp-cms--headline-6 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(5.46875vw, 70px));
  line-height: 0.9285714285714286;
  letter-spacing: 0;
  font-weight: 500;
}

.emotion-3 .amp-cms--headline-7 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(4.6875vw, 60px));
  line-height: 0.9166666666666666;
  letter-spacing: 0;
  font-weight: 500;
}

.emotion-3 .amp-cms--headlineAlt-1 {
  color: #2B2B2B;
  font-size: max(14px, min(8.90625vw, 114px));
  line-height: 1;
  letter-spacing: min(-0.03125vw, -0.4px);
  font-weight: 400;
}

.emotion-3 .amp-cms--headlineAlt-2 {
  color: #2B2B2B;
  font-size: max(14px, min(6.5625vw, 84px));
  line-height: 1;
  letter-spacing: min(-0.03125vw, -0.4px);
  font-weight: 400;
}

.emotion-3 .amp-cms--headlineAlt-3 {
  color: #2B2B2B;
  font-size: max(14px, min(5vw, 64px));
  line-height: 1;
  letter-spacing: min(-0.03125vw, -0.4px);
  font-weight: 400;
}

.emotion-3 .amp-cms--headlineAlt-4 {
  color: #2B2B2B;
  font-size: max(14px, min(2.65625vw, 34px));
  line-height: 1;
  letter-spacing: min(-0.03125vw, -0.4px);
  font-weight: 400;
}

.emotion-3 .amp-cms--headlineAlt-5 {
  color: #2B2B2B;
  font-size: min(1.25vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-3 .amp-cms--headlineAlt-6 {
  color: #2B2B2B;
  font-size: min(1.25vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-3 .amp-cms--headlineAlt-7 {
  color: #2B2B2B;
  font-size: min(1.25vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-3 .amp-cms--promo-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(7.8125vw, 100px));
  line-height: 1;
  letter-spacing: min(0vw, 0px);
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-3 .amp-cms--promo-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(6.25vw, 80px));
  line-height: 1;
  letter-spacing: min(0vw, 0px);
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-3 .amp-cms--promoAlt-1 {
  color: #2B2B2B;
  font-size: min(1.25vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 600;
}

.emotion-3 .amp-cms--subhead-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(2.34375vw, 30px));
  line-height: 1;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--subhead-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(1.953125vw, 25px));
  line-height: 1;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--subhead-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(1.5625vw, 20px));
  line-height: 1.1;
  letter-spacing: 0;
}

.emotion-4 {
  display: grid;
  grid-template-columns: 1fr;
  grid-gap: 1vw;
  margin-top: 0;
}

.emotion-5 {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr 1fr;
  grid-template-rows: 1fr;
  grid-gap: 0;
  position: relative;
}

.emotion-7 {
  position: relative;
  display: block;
  overflow: hidden;
  padding-bottom: 150%;
}

.emotion-9 {
  position: absolute;
  height: 100%;
  width: 100%;
  z-index: 1;
}

.emotion-11 {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: url(https://2hmc8flz4ofg1dy89cc38f2xm.staging.bigcontent.io/i/athleta/MessagingCardsC?fmt=auto&h=1640) no-repeat center center;
  -webkit-background-size: cover;
  background-size: cover;
}

.emotion-17 {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: url(https://2hmc8flz4ofg1dy89cc38f2xm.staging.bigcontent.io/i/athleta/MessagingCardsA?fmt=auto&h=1640) no-repeat center center;
  -webkit-background-size: cover;
  background-size: cover;
}

.emotion-23 {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: url(https://2hmc8flz4ofg1dy89cc38f2xm.staging.bigcontent.io/i/athleta/MessagingCardsB?fmt=auto&h=1640) no-repeat center center;
  -webkit-background-size: cover;
  background-size: cover;
}

.emotion-33 {
  width: 100%;
}

.emotion-37 {
  width: 90%;
  height: auto;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  text-align: left;
  color: #2B2B2B;
  margin: 6px auto 0 0;
  padding-left: 0;
  margin-bottom: 6px;
}

.emotion-38 {
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(1.25vw, 16px));
  line-height: 1.25;
  letter-spacing: 0;
}

.emotion-59 {
  position: relative;
  bottom: 0;
  min-height: 40px;
  text-align: left;
  width: 100%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  margin-right: auto;
  padding-bottom: 16px;
  max-width: 100%;
  -webkit-box-flex-wrap: wrap;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
}

.emotion-60 {
  z-index: 2;
  width: 100%;
  display: inline-block;
}

.emotion-61 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  display: inline-block;
  text-transform: none;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 1px solid transparent;
  border-radius: 0;
  max-width: 300px;
  font-size: 12px;
  font-weight: 500;
  letter-spacing: 2%;
  min-height: auto;
  max-height: auto;
  line-height: 12px;
  padding: 0;
  width: auto;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: left;
  border-left: none;
  border-right: none;
  border-radius: 0;
  background-color: transparent;
  border-bottom: 0;
  -webkit-box-pack: left;
  -ms-flex-pack: left;
  -webkit-justify-content: left;
  justify-content: left;
  color: #000000;
  height: auto;
  -webkit-text-decoration: underline;
  text-decoration: underline;
  text-underline-offset: 3.3px;
  text-decoration-thickness: 1.6px;
  -webkit-align-self: flex-start;
  -ms-flex-item-align: flex-start;
  align-self: flex-start;
  padding: 7px;
  white-space: normal;
  text-transform: none;
  white-space: initial;
  width: 100%;
  padding: 0;
  margin: 0;
  display: inline;
  line-height: 1.5em;
  text-align: left;
  text-underline-offset: 1px;
}

.emotion-61:focus {
  outline: none;
}

.emotion-61>span {
  padding: 1px 0;
}

.emotion-61:hover,
.emotion-61:focus {
  -webkit-text-decoration: unset;
  text-decoration: unset;
}

.emotion-61:active {
  -webkit-text-decoration: unset;
  text-decoration: unset;
}

.emotion-62 {
  box-sizing: border-box;
}

<div>
  <div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <div
        class="emotion-0"
      >
        <div
          class="emotion-1"
        >
          <div
            class="emotion-2"
          >
            <div
              class="emotion-3"
            >
              <div>
                <p
                  class="amp-cms--p amp-cms--text-center"
                >
                  <span
                    class="amp-cms--headline-7"
                  >
                    MORE TO SHOP. MORE TO LOVE.
                  </span>
                </p>
              </div>
            </div>
          </div>
          <div
            class="emotion-4"
            role="group"
          >
            <div
              class="emotion-5 emotion-6"
            >
              <div
                class="emotion-7 emotion-8"
              >
                <a
                  class="emotion-9 emotion-10"
                  href="test 1"
                  target="_self"
                  title="test 1"
                />
                <div
                  aria-label="image1"
                  class="emotion-11 emotion-12"
                  data-testid="cardImageTestId"
                  role="img"
                />
              </div>
              <div
                class="emotion-7 emotion-8"
              >
                <a
                  class="emotion-9 emotion-10"
                  href="test 2"
                  target="_self"
                  title="test 2"
                />
                <div
                  aria-label="Joggers"
                  class="emotion-17 emotion-12"
                  data-testid="cardImageTestId"
                  role="img"
                />
              </div>
              <div
                class="emotion-7 emotion-8"
              >
                <a
                  class="emotion-9 emotion-10"
                  href="test 3"
                  target="_self"
                  title="test 3"
                />
                <div
                  aria-label="Test3"
                  class="emotion-23 emotion-12"
                  data-testid="cardImageTestId"
                  role="img"
                />
              </div>
              <div
                class="emotion-7 emotion-8"
              >
                <a
                  class="emotion-9 emotion-10"
                  href="test 4"
                  target="_self"
                  title="test 4"
                />
                <div
                  aria-label="IMAGE4"
                  class="emotion-17 emotion-12"
                  data-testid="cardImageTestId"
                  role="img"
                />
              </div>
            </div>
            <div
              class="emotion-5 emotion-6"
            >
              <div
                class="emotion-33 emotion-34"
              >
                <a
                  class="emotion-9 emotion-10"
                  href="test 1"
                  target="_self"
                  title="test 1"
                />
                <div
                  class="emotion-37"
                >
                  <span
                    class="emotion-38"
                  >
                    Ultra High Rise Tight
                  </span>
                </div>
              </div>
              <div
                class="emotion-33 emotion-34"
              >
                <a
                  class="emotion-9 emotion-10"
                  href="test 2"
                  target="_self"
                  title="test 2"
                />
                <div
                  class="emotion-37"
                >
                  <span
                    class="emotion-38"
                  >
                    Ultra High Rise.
                  </span>
                </div>
              </div>
              <div
                class="emotion-33 emotion-34"
              >
                <a
                  class="emotion-9 emotion-10"
                  href="test 3"
                  target="_self"
                  title="test 3"
                />
                <div
                  class="emotion-37"
                >
                  <span
                    class="emotion-38"
                  >
                    Ultra High Rise Elation tight
                  </span>
                </div>
              </div>
              <div
                class="emotion-33 emotion-34"
              >
                <a
                  class="emotion-9 emotion-10"
                  href="test 4"
                  target="_self"
                  title="test 4"
                />
                <div
                  class="emotion-37"
                >
                  <span
                    class="emotion-38"
                  />
                </div>
              </div>
            </div>
            <div
              class="emotion-5 emotion-6"
            >
              <div
                class="emotion-59"
              >
                <div
                  class="emotion-60"
                >
                  <a
                    class="emotion-61"
                    color="dark"
                    href="/CTALink1"
                  >
                    <span
                      class="emotion-62"
                    >
                      CTA Link 1
                    </span>
                  </a>
                </div>
                <div
                  class="emotion-60"
                >
                  <a
                    class="emotion-61"
                    color="dark"
                    href="/CTALink2"
                  >
                    <span
                      class="emotion-62"
                    >
                      CTA Link 2
                    </span>
                  </a>
                </div>
              </div>
              <div
                class="emotion-59"
              >
                <div
                  class="emotion-60"
                >
                  <a
                    class="emotion-61"
                    color="dark"
                    href="/CTALink1"
                  >
                    <span
                      class="emotion-62"
                    >
                      CTA Link 3
                    </span>
                  </a>
                </div>
                <div
                  class="emotion-60"
                >
                  <a
                    class="emotion-61"
                    color="dark"
                    href="/CTALink2"
                  >
                    <span
                      class="emotion-62"
                    >
                      CTA Link 4
                    </span>
                  </a>
                </div>
              </div>
              <div
                class="emotion-59"
              />
              <div
                class="emotion-59"
              >
                <div
                  class="emotion-60"
                >
                  <a
                    class="emotion-61"
                    color="dark"
                    href="/CTALink1"
                  >
                    <span
                      class="emotion-62"
                    >
                      CTA Link 7
                    </span>
                  </a>
                </div>
                <div
                  class="emotion-60"
                >
                  <a
                    class="emotion-61"
                    color="dark"
                    href="/CTALink2"
                  >
                    <span
                      class="emotion-62"
                    >
                      CTA Link 8
                    </span>
                  </a>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`CategoryBannerMoreToShop Component should match snapshots in desktop view for Gap brand 1`] = `
.emotion-0 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  margin-bottom: 1.5rem;
  max-width: 768px;
  margin-left: auto;
  margin-right: 1rem;
}

@media (min-width: 1024px) {
  .emotion-0 {
    max-width: 1280px;
    padding-right: 1rem;
    padding-left: 15rem;
    margin-left: auto;
    margin-right: auto;
  }
}

.emotion-1 {
  width: 100%;
  height: auto;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
}

.emotion-2 {
  width: 100%;
  padding: 15px;
  box-sizing: border-box;
  border-top: 1px #2B2B2B;
  border-bottom: 1px #2B2B2B;
  border-style: solid none;
}

.emotion-3 .amp-cms--p {
  padding: 0;
  margin: 0;
  line-height: 0;
}

.emotion-3 .amp-cms--p>span {
  display: inline;
  white-space: break-spaces;
}

.emotion-3 .amp-cms--p a {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  position: relative;
  z-index: 2;
  pointer-events: auto;
}

.emotion-3 .amp-cms--p a:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-3 .amp-cms--legal-copy {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(0.9375vw, 12px));
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-3 sup {
  vertical-align: top;
  display: inline-block;
  margin-top: -0.25ex;
}

.emotion-3 sub {
  vertical-align: bottom;
  display: inline-block;
  margin-bottom: -0.25ex;
}

.emotion-3 .amp-cms--f-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.9375vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--f-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.9375vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--f-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.9375vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--fn-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.9375vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--body-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(1.5625vw, 20px));
  line-height: 1.3;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--body-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(1.40625vw, 18px));
  line-height: 1.2777777777777777;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--body-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(1.25vw, 16px));
  line-height: 1.25;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--body-4 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(1.09375vw, 14px));
  line-height: 1.2857142857142858;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--body-5 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(0.9375vw, 12px));
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--eyebrow-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(35px, min(3.90625vw, 50px));
  line-height: 0.92;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--eyebrow-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(35px, min(3.125vw, 40px));
  line-height: 1;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--eyebrow-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(35px, min(2.734375vw, 35px));
  line-height: 1;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--headline-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(9.375vw, 120px));
  line-height: 0.8333333333333334;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--headline-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(8.59375vw, 110px));
  line-height: 0.8636363636363636;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--headline-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(7.8125vw, 100px));
  line-height: 0.85;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--headline-4 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(7.8125vw, 100px));
  line-height: 1.05;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--headline-5 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(6.25vw, 80px));
  line-height: 0.9375;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--headline-6 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(5.46875vw, 70px));
  line-height: 0.9285714285714286;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--headline-7 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(4.6875vw, 60px));
  line-height: 0.9166666666666666;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--headlineAlt-1 {
  color: #2B2B2B;
  font-size: max(14px, min(8.90625vw, 114px));
  line-height: 1;
  letter-spacing: min(-0.03125vw, -0.4px);
  font-weight: 400;
}

.emotion-3 .amp-cms--headlineAlt-2 {
  color: #2B2B2B;
  font-size: max(14px, min(6.5625vw, 84px));
  line-height: 1;
  letter-spacing: min(-0.03125vw, -0.4px);
  font-weight: 400;
}

.emotion-3 .amp-cms--headlineAlt-3 {
  color: #2B2B2B;
  font-size: max(14px, min(5vw, 64px));
  line-height: 1;
  letter-spacing: min(-0.03125vw, -0.4px);
  font-weight: 400;
}

.emotion-3 .amp-cms--headlineAlt-4 {
  color: #2B2B2B;
  font-size: max(14px, min(2.65625vw, 34px));
  line-height: 1;
  letter-spacing: min(-0.03125vw, -0.4px);
  font-weight: 400;
}

.emotion-3 .amp-cms--headlineAlt-5 {
  color: #2B2B2B;
  font-size: min(1.25vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-3 .amp-cms--headlineAlt-6 {
  color: #2B2B2B;
  font-size: min(1.25vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-3 .amp-cms--headlineAlt-7 {
  color: #2B2B2B;
  font-size: min(1.25vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-3 .amp-cms--promo-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(7.8125vw, 100px));
  line-height: 1;
  letter-spacing: min(0vw, 0px);
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-3 .amp-cms--promo-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(6.25vw, 80px));
  line-height: 1;
  letter-spacing: min(0vw, 0px);
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-3 .amp-cms--promoAlt-1 {
  color: #2B2B2B;
  font-size: min(1.25vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 600;
}

.emotion-3 .amp-cms--subhead-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(2.34375vw, 30px));
  line-height: 1;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--subhead-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(1.953125vw, 25px));
  line-height: 1;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--subhead-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(1.5625vw, 20px));
  line-height: 1.1;
  letter-spacing: 0;
}

.emotion-4 {
  display: grid;
  grid-template-columns: 1fr;
  grid-gap: 1vw;
  margin-top: 0;
}

.emotion-5 {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr 1fr;
  grid-template-rows: 1fr;
  grid-gap: 0;
  position: relative;
}

.emotion-7 {
  position: relative;
  display: block;
  overflow: hidden;
  padding-bottom: 150%;
}

.emotion-9 {
  position: absolute;
  height: 100%;
  width: 100%;
  z-index: 1;
}

.emotion-11 {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: url(https://2hmc8flz4ofg1dy89cc38f2xm.staging.bigcontent.io/i/athleta/MessagingCardsC?fmt=auto&h=1640) no-repeat center center;
  -webkit-background-size: cover;
  background-size: cover;
}

.emotion-17 {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: url(https://2hmc8flz4ofg1dy89cc38f2xm.staging.bigcontent.io/i/athleta/MessagingCardsA?fmt=auto&h=1640) no-repeat center center;
  -webkit-background-size: cover;
  background-size: cover;
}

.emotion-23 {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: url(https://2hmc8flz4ofg1dy89cc38f2xm.staging.bigcontent.io/i/athleta/MessagingCardsB?fmt=auto&h=1640) no-repeat center center;
  -webkit-background-size: cover;
  background-size: cover;
}

.emotion-33 {
  width: 100%;
}

.emotion-37 {
  width: 90%;
  height: auto;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  text-align: left;
  color: #2B2B2B;
  margin: 6px auto 0 0;
  padding-left: 0;
  margin-bottom: 6px;
}

.emotion-38 {
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(1.25vw, 16px));
  line-height: 1.25;
  letter-spacing: 0;
}

.emotion-59 {
  position: relative;
  bottom: 0;
  min-height: 40px;
  text-align: left;
  width: 100%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  margin-right: auto;
  padding-bottom: 16px;
  max-width: 100%;
  -webkit-box-flex-wrap: wrap;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
}

.emotion-60 {
  z-index: 2;
  width: 100%;
  display: inline-block;
}

.emotion-61 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  display: inline-block;
  text-transform: none;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 1px solid transparent;
  border-radius: 0;
  max-width: 300px;
  font-size: 12px;
  font-weight: 500;
  letter-spacing: 2%;
  min-height: auto;
  max-height: auto;
  line-height: 12px;
  padding: 0;
  width: auto;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: left;
  border-left: none;
  border-right: none;
  border-radius: 0;
  background-color: transparent;
  border-bottom: 0;
  -webkit-box-pack: left;
  -ms-flex-pack: left;
  -webkit-justify-content: left;
  justify-content: left;
  color: #000000;
  height: auto;
  -webkit-text-decoration: underline;
  text-decoration: underline;
  text-underline-offset: 3.3px;
  text-decoration-thickness: 1.6px;
  -webkit-align-self: flex-start;
  -ms-flex-item-align: flex-start;
  align-self: flex-start;
  padding: 7px;
  white-space: normal;
  text-transform: none;
  white-space: initial;
  width: 100%;
  padding: 0;
  margin: 0;
  display: inline;
  line-height: 1.5em;
  text-align: left;
  text-underline-offset: 1px;
}

.emotion-61:focus {
  outline: none;
}

.emotion-61>span {
  padding: 1px 0;
}

.emotion-61:hover,
.emotion-61:focus {
  -webkit-text-decoration: unset;
  text-decoration: unset;
}

.emotion-61:active {
  -webkit-text-decoration: unset;
  text-decoration: unset;
}

.emotion-62 {
  box-sizing: border-box;
}

<div>
  <div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <div
        class="emotion-0"
      >
        <div
          class="emotion-1"
        >
          <div
            class="emotion-2"
          >
            <div
              class="emotion-3"
            >
              <div>
                <p
                  class="amp-cms--p amp-cms--text-center"
                >
                  <span
                    class="amp-cms--headline-7"
                  >
                    MORE TO SHOP. MORE TO LOVE.
                  </span>
                </p>
              </div>
            </div>
          </div>
          <div
            class="emotion-4"
            role="group"
          >
            <div
              class="emotion-5 emotion-6"
            >
              <div
                class="emotion-7 emotion-8"
              >
                <a
                  class="emotion-9 emotion-10"
                  href="test 1"
                  target="_self"
                  title="test 1"
                />
                <div
                  aria-label="image1"
                  class="emotion-11 emotion-12"
                  data-testid="cardImageTestId"
                  role="img"
                />
              </div>
              <div
                class="emotion-7 emotion-8"
              >
                <a
                  class="emotion-9 emotion-10"
                  href="test 2"
                  target="_self"
                  title="test 2"
                />
                <div
                  aria-label="Joggers"
                  class="emotion-17 emotion-12"
                  data-testid="cardImageTestId"
                  role="img"
                />
              </div>
              <div
                class="emotion-7 emotion-8"
              >
                <a
                  class="emotion-9 emotion-10"
                  href="test 3"
                  target="_self"
                  title="test 3"
                />
                <div
                  aria-label="Test3"
                  class="emotion-23 emotion-12"
                  data-testid="cardImageTestId"
                  role="img"
                />
              </div>
              <div
                class="emotion-7 emotion-8"
              >
                <a
                  class="emotion-9 emotion-10"
                  href="test 4"
                  target="_self"
                  title="test 4"
                />
                <div
                  aria-label="IMAGE4"
                  class="emotion-17 emotion-12"
                  data-testid="cardImageTestId"
                  role="img"
                />
              </div>
            </div>
            <div
              class="emotion-5 emotion-6"
            >
              <div
                class="emotion-33 emotion-34"
              >
                <a
                  class="emotion-9 emotion-10"
                  href="test 1"
                  target="_self"
                  title="test 1"
                />
                <div
                  class="emotion-37"
                >
                  <span
                    class="emotion-38"
                  >
                    Ultra High Rise Tight
                  </span>
                </div>
              </div>
              <div
                class="emotion-33 emotion-34"
              >
                <a
                  class="emotion-9 emotion-10"
                  href="test 2"
                  target="_self"
                  title="test 2"
                />
                <div
                  class="emotion-37"
                >
                  <span
                    class="emotion-38"
                  >
                    Ultra High Rise.
                  </span>
                </div>
              </div>
              <div
                class="emotion-33 emotion-34"
              >
                <a
                  class="emotion-9 emotion-10"
                  href="test 3"
                  target="_self"
                  title="test 3"
                />
                <div
                  class="emotion-37"
                >
                  <span
                    class="emotion-38"
                  >
                    Ultra High Rise Elation tight
                  </span>
                </div>
              </div>
              <div
                class="emotion-33 emotion-34"
              >
                <a
                  class="emotion-9 emotion-10"
                  href="test 4"
                  target="_self"
                  title="test 4"
                />
                <div
                  class="emotion-37"
                >
                  <span
                    class="emotion-38"
                  />
                </div>
              </div>
            </div>
            <div
              class="emotion-5 emotion-6"
            >
              <div
                class="emotion-59"
              >
                <div
                  class="emotion-60"
                >
                  <a
                    class="emotion-61"
                    color="dark"
                    href="/CTALink1"
                  >
                    <span
                      class="emotion-62"
                    >
                      CTA Link 1
                    </span>
                  </a>
                </div>
                <div
                  class="emotion-60"
                >
                  <a
                    class="emotion-61"
                    color="dark"
                    href="/CTALink2"
                  >
                    <span
                      class="emotion-62"
                    >
                      CTA Link 2
                    </span>
                  </a>
                </div>
              </div>
              <div
                class="emotion-59"
              >
                <div
                  class="emotion-60"
                >
                  <a
                    class="emotion-61"
                    color="dark"
                    href="/CTALink1"
                  >
                    <span
                      class="emotion-62"
                    >
                      CTA Link 3
                    </span>
                  </a>
                </div>
                <div
                  class="emotion-60"
                >
                  <a
                    class="emotion-61"
                    color="dark"
                    href="/CTALink2"
                  >
                    <span
                      class="emotion-62"
                    >
                      CTA Link 4
                    </span>
                  </a>
                </div>
              </div>
              <div
                class="emotion-59"
              />
              <div
                class="emotion-59"
              >
                <div
                  class="emotion-60"
                >
                  <a
                    class="emotion-61"
                    color="dark"
                    href="/CTALink1"
                  >
                    <span
                      class="emotion-62"
                    >
                      CTA Link 7
                    </span>
                  </a>
                </div>
                <div
                  class="emotion-60"
                >
                  <a
                    class="emotion-61"
                    color="dark"
                    href="/CTALink2"
                  >
                    <span
                      class="emotion-62"
                    >
                      CTA Link 8
                    </span>
                  </a>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`CategoryBannerMoreToShop Component should match snapshots in mobile view 1`] = `
.emotion-0 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  margin-bottom: 1.5rem;
  max-width: 768px;
  margin-left: auto;
  padding: 0;
  margin-right: auto;
}

@media (min-width: 1024px) {
  .emotion-0 {
    max-width: 1280px;
    padding-right: 1rem;
    padding-left: 15rem;
    margin-left: auto;
    margin-right: auto;
  }
}

.emotion-1 {
  width: 100%;
  height: auto;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
}

.emotion-2 {
  width: 100%;
  padding: 15px;
  box-sizing: border-box;
  border-top: 1px #2B2B2B;
  border-bottom: 1px #2B2B2B;
  border-style: solid none;
}

.emotion-3 .amp-cms--p {
  padding: 0;
  margin: 0;
  line-height: 0;
}

.emotion-3 .amp-cms--p>span {
  display: inline;
  white-space: break-spaces;
}

.emotion-3 .amp-cms--p a {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  position: relative;
  z-index: 2;
  pointer-events: auto;
}

.emotion-3 .amp-cms--p a:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-3 .amp-cms--legal-copy {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(2.666666666666667vw, 10px));
  line-height: 1.4;
  letter-spacing: 0;
}

.emotion-3 sup {
  vertical-align: top;
  display: inline-block;
  margin-top: -0.25ex;
}

.emotion-3 sub {
  vertical-align: bottom;
  display: inline-block;
  margin-bottom: -0.25ex;
}

.emotion-3 .amp-cms--f-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(2.933333333333333vw, 11px);
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--f-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(2.933333333333333vw, 11px);
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--f-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(2.933333333333333vw, 11px);
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--fn-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(2.933333333333333vw, 11px);
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--body-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(4.8vw, 18px));
  line-height: 1.2777777777777777;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--body-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(4.266666666666667vw, 16px));
  line-height: 1.25;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--body-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(3.733333333333334vw, 14px));
  line-height: 1.2857142857142858;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--body-4 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(3.2vw, 12px));
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--body-5 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(2.666666666666667vw, 10px));
  line-height: 1.4;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--eyebrow-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(8vw, 30px));
  line-height: 1;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--eyebrow-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(6.933333333333333vw, 26px));
  line-height: 1;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--eyebrow-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(6.4vw, 24px));
  line-height: 1;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--headline-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, min(16vw, 60px));
  line-height: 1;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--headline-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, min(14.666666666666666vw, 55px));
  line-height: 1;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--headline-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, min(14.666666666666666vw, 55px));
  line-height: 1;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--headline-4 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, min(13.333333333333334vw, 50px));
  line-height: 1.1;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--headline-5 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, min(12vw, 45px));
  line-height: 1;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--headline-6 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, min(10.666666666666668vw, 40px));
  line-height: 1;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--headline-7 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, min(9.333333333333334vw, 35px));
  line-height: 1;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--headlineAlt-1 {
  color: #2B2B2B;
  font-size: max(13px, min(17.066666666666666vw, 64px));
  line-height: 1;
  letter-spacing: min(-0.07999999999999999vw, -0.3px);
  font-weight: 400;
}

.emotion-3 .amp-cms--headlineAlt-2 {
  color: #2B2B2B;
  font-size: max(13px, min(14.399999999999999vw, 54px));
  line-height: 1;
  letter-spacing: min(-0.07999999999999999vw, -0.3px);
  font-weight: 400;
}

.emotion-3 .amp-cms--headlineAlt-3 {
  color: #2B2B2B;
  font-size: max(13px, min(9.066666666666666vw, 34px));
  line-height: 1;
  letter-spacing: min(-0.07999999999999999vw, -0.3px);
  font-weight: 400;
}

.emotion-3 .amp-cms--headlineAlt-4 {
  color: #2B2B2B;
  font-size: max(13px, min(6.4vw, 24px));
  line-height: 1.4166666666666667;
  letter-spacing: min(-0.07999999999999999vw, -0.3px);
  font-weight: 400;
}

.emotion-3 .amp-cms--headlineAlt-5 {
  color: #2B2B2B;
  font-size: min(3.733333333333334vw, 14px);
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-3 .amp-cms--headlineAlt-6 {
  color: #2B2B2B;
  font-size: min(3.733333333333334vw, 14px);
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-3 .amp-cms--headlineAlt-7 {
  color: #2B2B2B;
  font-size: min(3.733333333333334vw, 14px);
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-3 .amp-cms--promo-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(13px, min(16vw, 60px));
  line-height: 1;
  letter-spacing: min(0vw, 0px);
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-3 .amp-cms--promo-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(13px, min(10.666666666666668vw, 40px));
  line-height: 1;
  letter-spacing: min(0vw, 0px);
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-3 .amp-cms--promoAlt-1 {
  color: #2B2B2B;
  font-size: min(3.733333333333334vw, 14px);
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 600;
}

.emotion-3 .amp-cms--subhead-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, min(5.866666666666666vw, 22px));
  line-height: 1.0909090909090908;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--subhead-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, min(5.333333333333334vw, 20px));
  line-height: 1.1;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--subhead-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, min(4.8vw, 18px));
  line-height: 1.1111111111111112;
  letter-spacing: 0;
}

.emotion-4 {
  width: 100%;
  margin-top: 0;
}

.emotion-5 {
  display: grid;
  grid-template-rows: 1fr;
  grid-template-columns: 1fr 1fr;
  grid-gap: 0;
  margin-bottom: 3vw;
}

.emotion-6 {
  display: grid;
  grid-template-columns: 1fr;
  grid-template-rows: 1fr;
  grid-gap: 0;
  position: relative;
}

.emotion-8 {
  position: relative;
  display: block;
  overflow: hidden;
  padding-bottom: 133.33333333333331%;
}

.emotion-10 {
  position: absolute;
  height: 100%;
  width: 100%;
  z-index: 1;
}

.emotion-12 {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: url(https://2hmc8flz4ofg1dy89cc38f2xm.staging.bigcontent.io/i/athleta/MessagingCardsC?fmt=auto&h=1640) no-repeat center center;
  -webkit-background-size: cover;
  background-size: cover;
}

.emotion-20 {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: url(https://2hmc8flz4ofg1dy89cc38f2xm.staging.bigcontent.io/i/athleta/MessagingCardsA?fmt=auto&h=1640) no-repeat center center;
  -webkit-background-size: cover;
  background-size: cover;
}

.emotion-24 {
  width: 100%;
}

.emotion-28 {
  width: 90%;
  height: auto;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  text-align: left;
  color: #2B2B2B;
  margin: 6px auto 0 0;
  padding-left: 16px;
  margin-bottom: 20px;
}

.emotion-29 {
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(3.733333333333334vw, 14px));
  line-height: 1.2857142857142858;
  letter-spacing: 0;
}

.emotion-40 {
  position: relative;
  bottom: 0;
  min-height: 75px;
  text-align: left;
  width: 100%;
  display: grid;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  margin-right: auto;
  padding-bottom: 16px;
  max-width: 100%;
  -webkit-box-flex-wrap: wrap;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
}

.emotion-41 {
  z-index: 2;
  width: 100%;
  display: inline-block;
  margin-bottom: 16px;
  padding-left: 16px;
}

.emotion-42 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  display: inline-block;
  text-transform: none;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 1px solid transparent;
  border-radius: 0;
  font-size: 12px;
  font-weight: 500;
  letter-spacing: 2%;
  min-height: auto;
  max-height: auto;
  line-height: 1.125;
  padding: 0;
  width: auto;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: left;
  border-left: none;
  border-right: none;
  border-radius: 0;
  background-color: transparent;
  border-bottom: 0;
  -webkit-box-pack: left;
  -ms-flex-pack: left;
  -webkit-justify-content: left;
  justify-content: left;
  color: #000000;
  height: auto;
  -webkit-text-decoration: underline;
  text-decoration: underline;
  text-underline-offset: 3.3px;
  text-decoration-thickness: 1.6px;
  -webkit-align-self: flex-start;
  -ms-flex-item-align: flex-start;
  align-self: flex-start;
  padding: 7px;
  white-space: normal;
  text-transform: none;
  white-space: initial;
  width: 100%;
  padding: 0;
  margin: 0;
  display: inline;
  line-height: 1.5em;
  text-align: left;
  text-underline-offset: 1px;
}

.emotion-42:focus {
  outline: none;
}

.emotion-42>span {
  padding: 1px 0;
}

.emotion-42:hover,
.emotion-42:focus {
  -webkit-text-decoration: unset;
  text-decoration: unset;
}

.emotion-42:active {
  -webkit-text-decoration: unset;
  text-decoration: unset;
}

.emotion-43 {
  box-sizing: border-box;
}

.emotion-63 {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: url(https://2hmc8flz4ofg1dy89cc38f2xm.staging.bigcontent.io/i/athleta/MessagingCardsB?fmt=auto&h=1640) no-repeat center center;
  -webkit-background-size: cover;
  background-size: cover;
}

<div>
  <div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      class="emotion-0"
    >
      <div
        class="emotion-1"
      >
        <div
          class="emotion-2"
        >
          <div
            class="emotion-3"
          >
            <div>
              <p
                class="amp-cms--p amp-cms--text-center"
              >
                <span
                  class="amp-cms--headline-7"
                >
                  MORE TO SHOP. MORE TO LOVE.
                </span>
              </p>
            </div>
          </div>
        </div>
        <div
          class="emotion-4"
          role="group"
        >
          <div
            class="emotion-5"
          >
            <div
              class="emotion-6 emotion-7"
            >
              <div
                class="emotion-8 emotion-9"
              >
                <a
                  class="emotion-10 emotion-11"
                  href="test 1"
                  target="_self"
                  title="test 1"
                />
                <div
                  aria-label="image1"
                  class="emotion-12 emotion-13"
                  data-testid="cardImageTestId"
                  role="img"
                />
              </div>
            </div>
            <div
              class="emotion-6 emotion-7"
            >
              <div
                class="emotion-8 emotion-9"
              >
                <a
                  class="emotion-10 emotion-11"
                  href="test 2"
                  target="_self"
                  title="test 2"
                />
                <div
                  aria-label="Joggers"
                  class="emotion-20 emotion-13"
                  data-testid="cardImageTestId"
                  role="img"
                />
              </div>
            </div>
            <div
              class="emotion-6 emotion-7"
            >
              <div
                class="emotion-24 emotion-25"
              >
                <a
                  class="emotion-10 emotion-11"
                  href="test 1"
                  target="_self"
                  title="test 1"
                />
                <div
                  class="emotion-28"
                >
                  <span
                    class="emotion-29"
                  >
                    Ultra High Rise Tight
                  </span>
                </div>
              </div>
            </div>
            <div
              class="emotion-6 emotion-7"
            >
              <div
                class="emotion-24 emotion-25"
              >
                <a
                  class="emotion-10 emotion-11"
                  href="test 2"
                  target="_self"
                  title="test 2"
                />
                <div
                  class="emotion-28"
                >
                  <span
                    class="emotion-29"
                  >
                    Ultra High Rise.
                  </span>
                </div>
              </div>
            </div>
            <div
              class="emotion-6 emotion-7"
            >
              <div
                class="emotion-40"
              >
                <div
                  class="emotion-41"
                >
                  <a
                    class="emotion-42"
                    color="dark"
                    href="/CTALink1"
                  >
                    <span
                      class="emotion-43"
                    >
                      CTA Link 1
                    </span>
                  </a>
                </div>
                <div
                  class="emotion-41"
                >
                  <a
                    class="emotion-42"
                    color="dark"
                    href="/CTALink2"
                  >
                    <span
                      class="emotion-43"
                    >
                      CTA Link 2
                    </span>
                  </a>
                </div>
              </div>
            </div>
            <div
              class="emotion-6 emotion-7"
            >
              <div
                class="emotion-40"
              >
                <div
                  class="emotion-41"
                >
                  <a
                    class="emotion-42"
                    color="dark"
                    href="/CTALink1"
                  >
                    <span
                      class="emotion-43"
                    >
                      CTA Link 3
                    </span>
                  </a>
                </div>
                <div
                  class="emotion-41"
                >
                  <a
                    class="emotion-42"
                    color="dark"
                    href="/CTALink2"
                  >
                    <span
                      class="emotion-43"
                    >
                      CTA Link 4
                    </span>
                  </a>
                </div>
              </div>
            </div>
          </div>
          <div
            class="emotion-5"
          >
            <div
              class="emotion-6 emotion-7"
            >
              <div
                class="emotion-8 emotion-9"
              >
                <a
                  class="emotion-10 emotion-11"
                  href="test 3"
                  target="_self"
                  title="test 3"
                />
                <div
                  aria-label="Test3"
                  class="emotion-63 emotion-13"
                  data-testid="cardImageTestId"
                  role="img"
                />
              </div>
            </div>
            <div
              class="emotion-6 emotion-7"
            >
              <div
                class="emotion-8 emotion-9"
              >
                <a
                  class="emotion-10 emotion-11"
                  href="test 4"
                  target="_self"
                  title="test 4"
                />
                <div
                  aria-label="IMAGE4"
                  class="emotion-20 emotion-13"
                  data-testid="cardImageTestId"
                  role="img"
                />
              </div>
            </div>
            <div
              class="emotion-6 emotion-7"
            >
              <div
                class="emotion-24 emotion-25"
              >
                <a
                  class="emotion-10 emotion-11"
                  href="test 3"
                  target="_self"
                  title="test 3"
                />
                <div
                  class="emotion-28"
                >
                  <span
                    class="emotion-29"
                  >
                    Ultra High Rise Elation tight
                  </span>
                </div>
              </div>
            </div>
            <div
              class="emotion-6 emotion-7"
            >
              <div
                class="emotion-24 emotion-25"
              >
                <a
                  class="emotion-10 emotion-11"
                  href="test 4"
                  target="_self"
                  title="test 4"
                />
                <div
                  class="emotion-28"
                >
                  <span
                    class="emotion-29"
                  />
                </div>
              </div>
            </div>
            <div
              class="emotion-6 emotion-7"
            >
              <div
                class="emotion-40"
              />
            </div>
            <div
              class="emotion-6 emotion-7"
            >
              <div
                class="emotion-40"
              >
                <div
                  class="emotion-41"
                >
                  <a
                    class="emotion-42"
                    color="dark"
                    href="/CTALink1"
                  >
                    <span
                      class="emotion-43"
                    >
                      CTA Link 7
                    </span>
                  </a>
                </div>
                <div
                  class="emotion-41"
                >
                  <a
                    class="emotion-42"
                    color="dark"
                    href="/CTALink2"
                  >
                    <span
                      class="emotion-43"
                    >
                      CTA Link 8
                    </span>
                  </a>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`CategoryBannerMoreToShop Component should match snapshots in mobile view for Gap Factory brand 1`] = `
.emotion-0 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  margin-bottom: 1.5rem;
  max-width: 768px;
  margin-left: auto;
  padding: 0;
  margin-right: auto;
}

@media (min-width: 1024px) {
  .emotion-0 {
    max-width: 1280px;
    padding-right: 1rem;
    padding-left: 15rem;
    margin-left: auto;
    margin-right: auto;
  }
}

.emotion-1 {
  width: 100%;
  height: auto;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
}

.emotion-2 {
  width: 100%;
  padding: 15px;
  box-sizing: border-box;
  border-top: 1px #2B2B2B;
  border-bottom: 1px #2B2B2B;
  border-style: solid none;
}

.emotion-3 .amp-cms--p {
  padding: 0;
  margin: 0;
  line-height: 0;
}

.emotion-3 .amp-cms--p>span {
  display: inline;
  white-space: break-spaces;
}

.emotion-3 .amp-cms--p a {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  position: relative;
  z-index: 2;
  pointer-events: auto;
}

.emotion-3 .amp-cms--p a:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-3 .amp-cms--legal-copy {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(3.2vw, 12px));
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-3 sup {
  vertical-align: top;
  display: inline-block;
  margin-top: -0.25ex;
}

.emotion-3 sub {
  vertical-align: bottom;
  display: inline-block;
  margin-bottom: -0.25ex;
}

.emotion-3 .amp-cms--f-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(2.933333333333333vw, 11px);
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--f-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(2.933333333333333vw, 11px);
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--f-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(2.933333333333333vw, 11px);
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--fn-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(2.933333333333333vw, 11px);
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--body-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(4.8vw, 18px));
  line-height: 1.2777777777777777;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--body-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(4.266666666666667vw, 16px));
  line-height: 1.25;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--body-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(3.733333333333334vw, 14px));
  line-height: 1.2857142857142858;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--body-4 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(3.2vw, 12px));
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--body-5 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(3.2vw, 12px));
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--eyebrow-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(8vw, 30px));
  line-height: 1;
  letter-spacing: 0;
  font-weight: 500;
}

.emotion-3 .amp-cms--eyebrow-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(6.933333333333333vw, 26px));
  line-height: 1;
  letter-spacing: 0;
  font-weight: 500;
}

.emotion-3 .amp-cms--eyebrow-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(6.4vw, 24px));
  line-height: 1;
  letter-spacing: 0;
  font-weight: 500;
}

.emotion-3 .amp-cms--headline-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, min(16vw, 60px));
  line-height: 1;
  letter-spacing: 0;
  font-weight: 500;
}

.emotion-3 .amp-cms--headline-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, min(14.666666666666666vw, 55px));
  line-height: 1;
  letter-spacing: 0;
  font-weight: 500;
}

.emotion-3 .amp-cms--headline-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, min(14.666666666666666vw, 55px));
  line-height: 1;
  letter-spacing: 0;
  font-weight: 500;
}

.emotion-3 .amp-cms--headline-4 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, min(13.333333333333334vw, 50px));
  line-height: 1.1;
  letter-spacing: 0;
  font-weight: 500;
}

.emotion-3 .amp-cms--headline-5 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, min(12vw, 45px));
  line-height: 1;
  letter-spacing: 0;
  font-weight: 500;
}

.emotion-3 .amp-cms--headline-6 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, min(10.666666666666668vw, 40px));
  line-height: 1;
  letter-spacing: 0;
  font-weight: 500;
}

.emotion-3 .amp-cms--headline-7 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, min(9.333333333333334vw, 35px));
  line-height: 1;
  letter-spacing: 0;
  font-weight: 500;
}

.emotion-3 .amp-cms--headlineAlt-1 {
  color: #2B2B2B;
  font-size: max(13px, min(17.066666666666666vw, 64px));
  line-height: 1;
  letter-spacing: min(-0.07999999999999999vw, -0.3px);
  font-weight: 400;
}

.emotion-3 .amp-cms--headlineAlt-2 {
  color: #2B2B2B;
  font-size: max(13px, min(14.399999999999999vw, 54px));
  line-height: 1;
  letter-spacing: min(-0.07999999999999999vw, -0.3px);
  font-weight: 400;
}

.emotion-3 .amp-cms--headlineAlt-3 {
  color: #2B2B2B;
  font-size: max(13px, min(9.066666666666666vw, 34px));
  line-height: 1;
  letter-spacing: min(-0.07999999999999999vw, -0.3px);
  font-weight: 400;
}

.emotion-3 .amp-cms--headlineAlt-4 {
  color: #2B2B2B;
  font-size: max(13px, min(6.4vw, 24px));
  line-height: 1.4166666666666667;
  letter-spacing: min(-0.07999999999999999vw, -0.3px);
  font-weight: 400;
}

.emotion-3 .amp-cms--headlineAlt-5 {
  color: #2B2B2B;
  font-size: min(3.733333333333334vw, 14px);
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-3 .amp-cms--headlineAlt-6 {
  color: #2B2B2B;
  font-size: min(3.733333333333334vw, 14px);
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-3 .amp-cms--headlineAlt-7 {
  color: #2B2B2B;
  font-size: min(3.733333333333334vw, 14px);
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-3 .amp-cms--promo-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(13px, min(16vw, 60px));
  line-height: 1;
  letter-spacing: min(0vw, 0px);
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-3 .amp-cms--promo-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(13px, min(10.666666666666668vw, 40px));
  line-height: 1;
  letter-spacing: min(0vw, 0px);
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-3 .amp-cms--promoAlt-1 {
  color: #2B2B2B;
  font-size: min(3.733333333333334vw, 14px);
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 600;
}

.emotion-3 .amp-cms--subhead-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, min(5.866666666666666vw, 22px));
  line-height: 1.0909090909090908;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--subhead-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, min(5.333333333333334vw, 20px));
  line-height: 1.1;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--subhead-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, min(4.8vw, 18px));
  line-height: 1.1111111111111112;
  letter-spacing: 0;
}

.emotion-4 {
  width: 100%;
  margin-top: 0;
}

.emotion-5 {
  display: grid;
  grid-template-rows: 1fr;
  grid-template-columns: 1fr 1fr;
  grid-gap: 0;
  margin-bottom: 3vw;
}

.emotion-6 {
  display: grid;
  grid-template-columns: 1fr;
  grid-template-rows: 1fr;
  grid-gap: 0;
  position: relative;
}

.emotion-8 {
  position: relative;
  display: block;
  overflow: hidden;
  padding-bottom: 133.33333333333331%;
}

.emotion-10 {
  position: absolute;
  height: 100%;
  width: 100%;
  z-index: 1;
}

.emotion-12 {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: url(https://2hmc8flz4ofg1dy89cc38f2xm.staging.bigcontent.io/i/athleta/MessagingCardsC?fmt=auto&h=1640) no-repeat center center;
  -webkit-background-size: cover;
  background-size: cover;
}

.emotion-20 {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: url(https://2hmc8flz4ofg1dy89cc38f2xm.staging.bigcontent.io/i/athleta/MessagingCardsA?fmt=auto&h=1640) no-repeat center center;
  -webkit-background-size: cover;
  background-size: cover;
}

.emotion-24 {
  width: 100%;
}

.emotion-28 {
  width: 90%;
  height: auto;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  text-align: left;
  color: #2B2B2B;
  margin: 6px auto 0 0;
  padding-left: 16px;
  margin-bottom: 20px;
}

.emotion-29 {
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(3.733333333333334vw, 14px));
  line-height: 1.2857142857142858;
  letter-spacing: 0;
}

.emotion-40 {
  position: relative;
  bottom: 0;
  min-height: 75px;
  text-align: left;
  width: 100%;
  display: grid;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  margin-right: auto;
  padding-bottom: 16px;
  max-width: 100%;
  -webkit-box-flex-wrap: wrap;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
}

.emotion-41 {
  z-index: 2;
  width: 100%;
  display: inline-block;
  margin-bottom: 16px;
  padding-left: 16px;
}

.emotion-42 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  display: inline-block;
  text-transform: none;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 1px solid transparent;
  border-radius: 0;
  font-size: 12px;
  font-weight: 500;
  letter-spacing: 2%;
  min-height: auto;
  max-height: auto;
  line-height: 1.125;
  padding: 0;
  width: auto;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: left;
  border-left: none;
  border-right: none;
  border-radius: 0;
  background-color: transparent;
  border-bottom: 0;
  -webkit-box-pack: left;
  -ms-flex-pack: left;
  -webkit-justify-content: left;
  justify-content: left;
  color: #000000;
  height: auto;
  -webkit-text-decoration: underline;
  text-decoration: underline;
  text-underline-offset: 3.3px;
  text-decoration-thickness: 1.6px;
  -webkit-align-self: flex-start;
  -ms-flex-item-align: flex-start;
  align-self: flex-start;
  padding: 7px;
  white-space: normal;
  text-transform: none;
  white-space: initial;
  width: 100%;
  padding: 0;
  margin: 0;
  display: inline;
  line-height: 1.5em;
  text-align: left;
  text-underline-offset: 1px;
}

.emotion-42:focus {
  outline: none;
}

.emotion-42>span {
  padding: 1px 0;
}

.emotion-42:hover,
.emotion-42:focus {
  -webkit-text-decoration: unset;
  text-decoration: unset;
}

.emotion-42:active {
  -webkit-text-decoration: unset;
  text-decoration: unset;
}

.emotion-43 {
  box-sizing: border-box;
}

.emotion-63 {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: url(https://2hmc8flz4ofg1dy89cc38f2xm.staging.bigcontent.io/i/athleta/MessagingCardsB?fmt=auto&h=1640) no-repeat center center;
  -webkit-background-size: cover;
  background-size: cover;
}

<div>
  <div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <div
        class="emotion-0"
      >
        <div
          class="emotion-1"
        >
          <div
            class="emotion-2"
          >
            <div
              class="emotion-3"
            >
              <div>
                <p
                  class="amp-cms--p amp-cms--text-center"
                >
                  <span
                    class="amp-cms--headline-7"
                  >
                    MORE TO SHOP. MORE TO LOVE.
                  </span>
                </p>
              </div>
            </div>
          </div>
          <div
            class="emotion-4"
            role="group"
          >
            <div
              class="emotion-5"
            >
              <div
                class="emotion-6 emotion-7"
              >
                <div
                  class="emotion-8 emotion-9"
                >
                  <a
                    class="emotion-10 emotion-11"
                    href="test 1"
                    target="_self"
                    title="test 1"
                  />
                  <div
                    aria-label="image1"
                    class="emotion-12 emotion-13"
                    data-testid="cardImageTestId"
                    role="img"
                  />
                </div>
              </div>
              <div
                class="emotion-6 emotion-7"
              >
                <div
                  class="emotion-8 emotion-9"
                >
                  <a
                    class="emotion-10 emotion-11"
                    href="test 2"
                    target="_self"
                    title="test 2"
                  />
                  <div
                    aria-label="Joggers"
                    class="emotion-20 emotion-13"
                    data-testid="cardImageTestId"
                    role="img"
                  />
                </div>
              </div>
              <div
                class="emotion-6 emotion-7"
              >
                <div
                  class="emotion-24 emotion-25"
                >
                  <a
                    class="emotion-10 emotion-11"
                    href="test 1"
                    target="_self"
                    title="test 1"
                  />
                  <div
                    class="emotion-28"
                  >
                    <span
                      class="emotion-29"
                    >
                      Ultra High Rise Tight
                    </span>
                  </div>
                </div>
              </div>
              <div
                class="emotion-6 emotion-7"
              >
                <div
                  class="emotion-24 emotion-25"
                >
                  <a
                    class="emotion-10 emotion-11"
                    href="test 2"
                    target="_self"
                    title="test 2"
                  />
                  <div
                    class="emotion-28"
                  >
                    <span
                      class="emotion-29"
                    >
                      Ultra High Rise.
                    </span>
                  </div>
                </div>
              </div>
              <div
                class="emotion-6 emotion-7"
              >
                <div
                  class="emotion-40"
                >
                  <div
                    class="emotion-41"
                  >
                    <a
                      class="emotion-42"
                      color="dark"
                      href="/CTALink1"
                    >
                      <span
                        class="emotion-43"
                      >
                        CTA Link 1
                      </span>
                    </a>
                  </div>
                  <div
                    class="emotion-41"
                  >
                    <a
                      class="emotion-42"
                      color="dark"
                      href="/CTALink2"
                    >
                      <span
                        class="emotion-43"
                      >
                        CTA Link 2
                      </span>
                    </a>
                  </div>
                </div>
              </div>
              <div
                class="emotion-6 emotion-7"
              >
                <div
                  class="emotion-40"
                >
                  <div
                    class="emotion-41"
                  >
                    <a
                      class="emotion-42"
                      color="dark"
                      href="/CTALink1"
                    >
                      <span
                        class="emotion-43"
                      >
                        CTA Link 3
                      </span>
                    </a>
                  </div>
                  <div
                    class="emotion-41"
                  >
                    <a
                      class="emotion-42"
                      color="dark"
                      href="/CTALink2"
                    >
                      <span
                        class="emotion-43"
                      >
                        CTA Link 4
                      </span>
                    </a>
                  </div>
                </div>
              </div>
            </div>
            <div
              class="emotion-5"
            >
              <div
                class="emotion-6 emotion-7"
              >
                <div
                  class="emotion-8 emotion-9"
                >
                  <a
                    class="emotion-10 emotion-11"
                    href="test 3"
                    target="_self"
                    title="test 3"
                  />
                  <div
                    aria-label="Test3"
                    class="emotion-63 emotion-13"
                    data-testid="cardImageTestId"
                    role="img"
                  />
                </div>
              </div>
              <div
                class="emotion-6 emotion-7"
              >
                <div
                  class="emotion-8 emotion-9"
                >
                  <a
                    class="emotion-10 emotion-11"
                    href="test 4"
                    target="_self"
                    title="test 4"
                  />
                  <div
                    aria-label="IMAGE4"
                    class="emotion-20 emotion-13"
                    data-testid="cardImageTestId"
                    role="img"
                  />
                </div>
              </div>
              <div
                class="emotion-6 emotion-7"
              >
                <div
                  class="emotion-24 emotion-25"
                >
                  <a
                    class="emotion-10 emotion-11"
                    href="test 3"
                    target="_self"
                    title="test 3"
                  />
                  <div
                    class="emotion-28"
                  >
                    <span
                      class="emotion-29"
                    >
                      Ultra High Rise Elation tight
                    </span>
                  </div>
                </div>
              </div>
              <div
                class="emotion-6 emotion-7"
              >
                <div
                  class="emotion-24 emotion-25"
                >
                  <a
                    class="emotion-10 emotion-11"
                    href="test 4"
                    target="_self"
                    title="test 4"
                  />
                  <div
                    class="emotion-28"
                  >
                    <span
                      class="emotion-29"
                    />
                  </div>
                </div>
              </div>
              <div
                class="emotion-6 emotion-7"
              >
                <div
                  class="emotion-40"
                />
              </div>
              <div
                class="emotion-6 emotion-7"
              >
                <div
                  class="emotion-40"
                >
                  <div
                    class="emotion-41"
                  >
                    <a
                      class="emotion-42"
                      color="dark"
                      href="/CTALink1"
                    >
                      <span
                        class="emotion-43"
                      >
                        CTA Link 7
                      </span>
                    </a>
                  </div>
                  <div
                    class="emotion-41"
                  >
                    <a
                      class="emotion-42"
                      color="dark"
                      href="/CTALink2"
                    >
                      <span
                        class="emotion-43"
                      >
                        CTA Link 8
                      </span>
                    </a>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`CategoryBannerMoreToShop Component should match snapshots in mobile view for Gap brand 1`] = `
.emotion-0 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  margin-bottom: 1.5rem;
  max-width: 768px;
  margin-left: auto;
  padding: 0;
  margin-right: auto;
}

@media (min-width: 1024px) {
  .emotion-0 {
    max-width: 1280px;
    padding-right: 1rem;
    padding-left: 15rem;
    margin-left: auto;
    margin-right: auto;
  }
}

.emotion-1 {
  width: 100%;
  height: auto;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
}

.emotion-2 {
  width: 100%;
  padding: 15px;
  box-sizing: border-box;
  border-top: 1px #2B2B2B;
  border-bottom: 1px #2B2B2B;
  border-style: solid none;
}

.emotion-3 .amp-cms--p {
  padding: 0;
  margin: 0;
  line-height: 0;
}

.emotion-3 .amp-cms--p>span {
  display: inline;
  white-space: break-spaces;
}

.emotion-3 .amp-cms--p a {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  position: relative;
  z-index: 2;
  pointer-events: auto;
}

.emotion-3 .amp-cms--p a:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-3 .amp-cms--legal-copy {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(2.666666666666667vw, 10px));
  line-height: 1.4;
  letter-spacing: 0;
}

.emotion-3 sup {
  vertical-align: top;
  display: inline-block;
  margin-top: -0.25ex;
}

.emotion-3 sub {
  vertical-align: bottom;
  display: inline-block;
  margin-bottom: -0.25ex;
}

.emotion-3 .amp-cms--f-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(2.933333333333333vw, 11px);
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--f-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(2.933333333333333vw, 11px);
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--f-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(2.933333333333333vw, 11px);
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--fn-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(2.933333333333333vw, 11px);
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--body-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(4.8vw, 18px));
  line-height: 1.2777777777777777;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--body-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(4.266666666666667vw, 16px));
  line-height: 1.25;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--body-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(3.733333333333334vw, 14px));
  line-height: 1.2857142857142858;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--body-4 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(3.2vw, 12px));
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--body-5 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(2.666666666666667vw, 10px));
  line-height: 1.4;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--eyebrow-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(8vw, 30px));
  line-height: 1;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--eyebrow-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(6.933333333333333vw, 26px));
  line-height: 1;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--eyebrow-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(6.4vw, 24px));
  line-height: 1;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--headline-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, min(16vw, 60px));
  line-height: 1;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--headline-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, min(14.666666666666666vw, 55px));
  line-height: 1;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--headline-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, min(14.666666666666666vw, 55px));
  line-height: 1;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--headline-4 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, min(13.333333333333334vw, 50px));
  line-height: 1.1;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--headline-5 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, min(12vw, 45px));
  line-height: 1;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--headline-6 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, min(10.666666666666668vw, 40px));
  line-height: 1;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--headline-7 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, min(9.333333333333334vw, 35px));
  line-height: 1;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--headlineAlt-1 {
  color: #2B2B2B;
  font-size: max(13px, min(17.066666666666666vw, 64px));
  line-height: 1;
  letter-spacing: min(-0.07999999999999999vw, -0.3px);
  font-weight: 400;
}

.emotion-3 .amp-cms--headlineAlt-2 {
  color: #2B2B2B;
  font-size: max(13px, min(14.399999999999999vw, 54px));
  line-height: 1;
  letter-spacing: min(-0.07999999999999999vw, -0.3px);
  font-weight: 400;
}

.emotion-3 .amp-cms--headlineAlt-3 {
  color: #2B2B2B;
  font-size: max(13px, min(9.066666666666666vw, 34px));
  line-height: 1;
  letter-spacing: min(-0.07999999999999999vw, -0.3px);
  font-weight: 400;
}

.emotion-3 .amp-cms--headlineAlt-4 {
  color: #2B2B2B;
  font-size: max(13px, min(6.4vw, 24px));
  line-height: 1.4166666666666667;
  letter-spacing: min(-0.07999999999999999vw, -0.3px);
  font-weight: 400;
}

.emotion-3 .amp-cms--headlineAlt-5 {
  color: #2B2B2B;
  font-size: min(3.733333333333334vw, 14px);
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-3 .amp-cms--headlineAlt-6 {
  color: #2B2B2B;
  font-size: min(3.733333333333334vw, 14px);
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-3 .amp-cms--headlineAlt-7 {
  color: #2B2B2B;
  font-size: min(3.733333333333334vw, 14px);
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-3 .amp-cms--promo-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(13px, min(16vw, 60px));
  line-height: 1;
  letter-spacing: min(0vw, 0px);
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-3 .amp-cms--promo-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(13px, min(10.666666666666668vw, 40px));
  line-height: 1;
  letter-spacing: min(0vw, 0px);
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-3 .amp-cms--promoAlt-1 {
  color: #2B2B2B;
  font-size: min(3.733333333333334vw, 14px);
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 600;
}

.emotion-3 .amp-cms--subhead-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, min(5.866666666666666vw, 22px));
  line-height: 1.0909090909090908;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--subhead-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, min(5.333333333333334vw, 20px));
  line-height: 1.1;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--subhead-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, min(4.8vw, 18px));
  line-height: 1.1111111111111112;
  letter-spacing: 0;
}

.emotion-4 {
  width: 100%;
  margin-top: 0;
}

.emotion-5 {
  display: grid;
  grid-template-rows: 1fr;
  grid-template-columns: 1fr 1fr;
  grid-gap: 0;
  margin-bottom: 3vw;
}

.emotion-6 {
  display: grid;
  grid-template-columns: 1fr;
  grid-template-rows: 1fr;
  grid-gap: 0;
  position: relative;
}

.emotion-8 {
  position: relative;
  display: block;
  overflow: hidden;
  padding-bottom: 133.33333333333331%;
}

.emotion-10 {
  position: absolute;
  height: 100%;
  width: 100%;
  z-index: 1;
}

.emotion-12 {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: url(https://2hmc8flz4ofg1dy89cc38f2xm.staging.bigcontent.io/i/athleta/MessagingCardsC?fmt=auto&h=1640) no-repeat center center;
  -webkit-background-size: cover;
  background-size: cover;
}

.emotion-20 {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: url(https://2hmc8flz4ofg1dy89cc38f2xm.staging.bigcontent.io/i/athleta/MessagingCardsA?fmt=auto&h=1640) no-repeat center center;
  -webkit-background-size: cover;
  background-size: cover;
}

.emotion-24 {
  width: 100%;
}

.emotion-28 {
  width: 90%;
  height: auto;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  text-align: left;
  color: #2B2B2B;
  margin: 6px auto 0 0;
  padding-left: 16px;
  margin-bottom: 20px;
}

.emotion-29 {
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(3.733333333333334vw, 14px));
  line-height: 1.2857142857142858;
  letter-spacing: 0;
}

.emotion-40 {
  position: relative;
  bottom: 0;
  min-height: 75px;
  text-align: left;
  width: 100%;
  display: grid;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  margin-right: auto;
  padding-bottom: 16px;
  max-width: 100%;
  -webkit-box-flex-wrap: wrap;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
}

.emotion-41 {
  z-index: 2;
  width: 100%;
  display: inline-block;
  margin-bottom: 16px;
  padding-left: 16px;
}

.emotion-42 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  display: inline-block;
  text-transform: none;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 1px solid transparent;
  border-radius: 0;
  font-size: 12px;
  font-weight: 500;
  letter-spacing: 2%;
  min-height: auto;
  max-height: auto;
  line-height: 1.125;
  padding: 0;
  width: auto;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: left;
  border-left: none;
  border-right: none;
  border-radius: 0;
  background-color: transparent;
  border-bottom: 0;
  -webkit-box-pack: left;
  -ms-flex-pack: left;
  -webkit-justify-content: left;
  justify-content: left;
  color: #000000;
  height: auto;
  -webkit-text-decoration: underline;
  text-decoration: underline;
  text-underline-offset: 3.3px;
  text-decoration-thickness: 1.6px;
  -webkit-align-self: flex-start;
  -ms-flex-item-align: flex-start;
  align-self: flex-start;
  padding: 7px;
  white-space: normal;
  text-transform: none;
  white-space: initial;
  width: 100%;
  padding: 0;
  margin: 0;
  display: inline;
  line-height: 1.5em;
  text-align: left;
  text-underline-offset: 1px;
}

.emotion-42:focus {
  outline: none;
}

.emotion-42>span {
  padding: 1px 0;
}

.emotion-42:hover,
.emotion-42:focus {
  -webkit-text-decoration: unset;
  text-decoration: unset;
}

.emotion-42:active {
  -webkit-text-decoration: unset;
  text-decoration: unset;
}

.emotion-43 {
  box-sizing: border-box;
}

.emotion-63 {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: url(https://2hmc8flz4ofg1dy89cc38f2xm.staging.bigcontent.io/i/athleta/MessagingCardsB?fmt=auto&h=1640) no-repeat center center;
  -webkit-background-size: cover;
  background-size: cover;
}

<div>
  <div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <div
        class="emotion-0"
      >
        <div
          class="emotion-1"
        >
          <div
            class="emotion-2"
          >
            <div
              class="emotion-3"
            >
              <div>
                <p
                  class="amp-cms--p amp-cms--text-center"
                >
                  <span
                    class="amp-cms--headline-7"
                  >
                    MORE TO SHOP. MORE TO LOVE.
                  </span>
                </p>
              </div>
            </div>
          </div>
          <div
            class="emotion-4"
            role="group"
          >
            <div
              class="emotion-5"
            >
              <div
                class="emotion-6 emotion-7"
              >
                <div
                  class="emotion-8 emotion-9"
                >
                  <a
                    class="emotion-10 emotion-11"
                    href="test 1"
                    target="_self"
                    title="test 1"
                  />
                  <div
                    aria-label="image1"
                    class="emotion-12 emotion-13"
                    data-testid="cardImageTestId"
                    role="img"
                  />
                </div>
              </div>
              <div
                class="emotion-6 emotion-7"
              >
                <div
                  class="emotion-8 emotion-9"
                >
                  <a
                    class="emotion-10 emotion-11"
                    href="test 2"
                    target="_self"
                    title="test 2"
                  />
                  <div
                    aria-label="Joggers"
                    class="emotion-20 emotion-13"
                    data-testid="cardImageTestId"
                    role="img"
                  />
                </div>
              </div>
              <div
                class="emotion-6 emotion-7"
              >
                <div
                  class="emotion-24 emotion-25"
                >
                  <a
                    class="emotion-10 emotion-11"
                    href="test 1"
                    target="_self"
                    title="test 1"
                  />
                  <div
                    class="emotion-28"
                  >
                    <span
                      class="emotion-29"
                    >
                      Ultra High Rise Tight
                    </span>
                  </div>
                </div>
              </div>
              <div
                class="emotion-6 emotion-7"
              >
                <div
                  class="emotion-24 emotion-25"
                >
                  <a
                    class="emotion-10 emotion-11"
                    href="test 2"
                    target="_self"
                    title="test 2"
                  />
                  <div
                    class="emotion-28"
                  >
                    <span
                      class="emotion-29"
                    >
                      Ultra High Rise.
                    </span>
                  </div>
                </div>
              </div>
              <div
                class="emotion-6 emotion-7"
              >
                <div
                  class="emotion-40"
                >
                  <div
                    class="emotion-41"
                  >
                    <a
                      class="emotion-42"
                      color="dark"
                      href="/CTALink1"
                    >
                      <span
                        class="emotion-43"
                      >
                        CTA Link 1
                      </span>
                    </a>
                  </div>
                  <div
                    class="emotion-41"
                  >
                    <a
                      class="emotion-42"
                      color="dark"
                      href="/CTALink2"
                    >
                      <span
                        class="emotion-43"
                      >
                        CTA Link 2
                      </span>
                    </a>
                  </div>
                </div>
              </div>
              <div
                class="emotion-6 emotion-7"
              >
                <div
                  class="emotion-40"
                >
                  <div
                    class="emotion-41"
                  >
                    <a
                      class="emotion-42"
                      color="dark"
                      href="/CTALink1"
                    >
                      <span
                        class="emotion-43"
                      >
                        CTA Link 3
                      </span>
                    </a>
                  </div>
                  <div
                    class="emotion-41"
                  >
                    <a
                      class="emotion-42"
                      color="dark"
                      href="/CTALink2"
                    >
                      <span
                        class="emotion-43"
                      >
                        CTA Link 4
                      </span>
                    </a>
                  </div>
                </div>
              </div>
            </div>
            <div
              class="emotion-5"
            >
              <div
                class="emotion-6 emotion-7"
              >
                <div
                  class="emotion-8 emotion-9"
                >
                  <a
                    class="emotion-10 emotion-11"
                    href="test 3"
                    target="_self"
                    title="test 3"
                  />
                  <div
                    aria-label="Test3"
                    class="emotion-63 emotion-13"
                    data-testid="cardImageTestId"
                    role="img"
                  />
                </div>
              </div>
              <div
                class="emotion-6 emotion-7"
              >
                <div
                  class="emotion-8 emotion-9"
                >
                  <a
                    class="emotion-10 emotion-11"
                    href="test 4"
                    target="_self"
                    title="test 4"
                  />
                  <div
                    aria-label="IMAGE4"
                    class="emotion-20 emotion-13"
                    data-testid="cardImageTestId"
                    role="img"
                  />
                </div>
              </div>
              <div
                class="emotion-6 emotion-7"
              >
                <div
                  class="emotion-24 emotion-25"
                >
                  <a
                    class="emotion-10 emotion-11"
                    href="test 3"
                    target="_self"
                    title="test 3"
                  />
                  <div
                    class="emotion-28"
                  >
                    <span
                      class="emotion-29"
                    >
                      Ultra High Rise Elation tight
                    </span>
                  </div>
                </div>
              </div>
              <div
                class="emotion-6 emotion-7"
              >
                <div
                  class="emotion-24 emotion-25"
                >
                  <a
                    class="emotion-10 emotion-11"
                    href="test 4"
                    target="_self"
                    title="test 4"
                  />
                  <div
                    class="emotion-28"
                  >
                    <span
                      class="emotion-29"
                    />
                  </div>
                </div>
              </div>
              <div
                class="emotion-6 emotion-7"
              >
                <div
                  class="emotion-40"
                />
              </div>
              <div
                class="emotion-6 emotion-7"
              >
                <div
                  class="emotion-40"
                >
                  <div
                    class="emotion-41"
                  >
                    <a
                      class="emotion-42"
                      color="dark"
                      href="/CTALink1"
                    >
                      <span
                        class="emotion-43"
                      >
                        CTA Link 7
                      </span>
                    </a>
                  </div>
                  <div
                    class="emotion-41"
                  >
                    <a
                      class="emotion-42"
                      color="dark"
                      href="/CTALink2"
                    >
                      <span
                        class="emotion-43"
                      >
                        CTA Link 8
                      </span>
                    </a>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;
