// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`FlexContainerWithTextAndCta Snapshot should match mobile snapshot 1`] = `
.emotion-0 {
  width: 100%;
  height: 100%;
  padding: 15px 15px 15px 15px;
  box-sizing: border-box;
  position: absolute;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  grid-template-columns: repeat(3, minmax(20%,max-content));
  -webkit-align-content: center;
  -ms-flex-line-pack: center;
  align-content: center;
  grid-template-rows: repeat(3, minmax(0, max-content));
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
}

.emotion-0 .rteWrapperDivs {
  grid-column: 2;
  grid-row: 2;
}

.emotion-0 .ctaWrapperDivs {
  grid-column: 2;
  grid-row: 2;
}

.emotion-1 {
  box-sizing: content-box;
  text-align: center;
}

.emotion-2 {
  text-align: center;
  display: inline-block;
}

.emotion-2 .amp-cms--p {
  padding: 0;
  margin: 0;
  line-height: 0;
}

.emotion-2 .amp-cms--p>span {
  display: inline;
  white-space: break-spaces;
}

.emotion-2 .amp-cms--p a {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  position: relative;
  z-index: 2;
  pointer-events: auto;
}

.emotion-2 .amp-cms--p a:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-2 .amp-cms--legal-copy {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 10px;
  line-height: 1.4;
  letter-spacing: 0;
}

.emotion-2 sup {
  vertical-align: top;
  display: inline-block;
  margin-top: -0.25ex;
}

.emotion-2 sub {
  vertical-align: bottom;
  display: inline-block;
  margin-bottom: -0.25ex;
}

.emotion-2 .amp-cms--f-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 11px;
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-2 .amp-cms--f-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 11px;
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-2 .amp-cms--f-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 11px;
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-2 .amp-cms--fn-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 11px;
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-2 .amp-cms--body-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 18px;
  line-height: 1.2777777777777777;
  letter-spacing: 0;
}

.emotion-2 .amp-cms--body-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 16px;
  line-height: 1.25;
  letter-spacing: 0;
}

.emotion-2 .amp-cms--body-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 14px;
  line-height: 1.2857142857142858;
  letter-spacing: 0;
}

.emotion-2 .amp-cms--body-4 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-2 .amp-cms--body-5 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 10px;
  line-height: 1.4;
  letter-spacing: 0;
}

.emotion-2 .amp-cms--eyebrow-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 30px;
  line-height: 1;
  letter-spacing: 0;
}

.emotion-2 .amp-cms--eyebrow-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 26px;
  line-height: 1;
  letter-spacing: 0;
}

.emotion-2 .amp-cms--eyebrow-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 24px;
  line-height: 1;
  letter-spacing: 0;
}

.emotion-2 .amp-cms--headline-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 60px;
  line-height: 1;
  letter-spacing: 0;
}

.emotion-2 .amp-cms--headline-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 55px;
  line-height: 1;
  letter-spacing: 0;
}

.emotion-2 .amp-cms--headline-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 55px;
  line-height: 1;
  letter-spacing: 0;
}

.emotion-2 .amp-cms--headline-4 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 50px;
  line-height: 1.1;
  letter-spacing: 0;
}

.emotion-2 .amp-cms--headline-5 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 45px;
  line-height: 1;
  letter-spacing: 0;
}

.emotion-2 .amp-cms--headline-6 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 40px;
  line-height: 1;
  letter-spacing: 0;
}

.emotion-2 .amp-cms--headline-7 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 35px;
  line-height: 1;
  letter-spacing: 0;
}

.emotion-2 .amp-cms--headlineAlt-1 {
  color: #2B2B2B;
  font-size: 64px;
  line-height: 1;
  letter-spacing: -0.3px;
  font-weight: 400;
}

.emotion-2 .amp-cms--headlineAlt-2 {
  color: #2B2B2B;
  font-size: 54px;
  line-height: 1;
  letter-spacing: -0.3px;
  font-weight: 400;
}

.emotion-2 .amp-cms--headlineAlt-3 {
  color: #2B2B2B;
  font-size: 34px;
  line-height: 1;
  letter-spacing: -0.3px;
  font-weight: 400;
}

.emotion-2 .amp-cms--headlineAlt-4 {
  color: #2B2B2B;
  font-size: 24px;
  line-height: 1.4166666666666667;
  letter-spacing: -0.3px;
  font-weight: 400;
}

.emotion-2 .amp-cms--headlineAlt-5 {
  color: #2B2B2B;
  font-size: 14px;
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-2 .amp-cms--headlineAlt-6 {
  color: #2B2B2B;
  font-size: 14px;
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-2 .amp-cms--headlineAlt-7 {
  color: #2B2B2B;
  font-size: 14px;
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-2 .amp-cms--promo-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 60px;
  line-height: 1;
  letter-spacing: 0px;
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-2 .amp-cms--promo-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 40px;
  line-height: 1;
  letter-spacing: 0px;
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-2 .amp-cms--promoAlt-1 {
  color: #2B2B2B;
  font-size: 14px;
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 600;
}

.emotion-2 .amp-cms--subhead-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 22px;
  line-height: 1.0909090909090908;
  letter-spacing: 0;
}

.emotion-2 .amp-cms--subhead-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 20px;
  line-height: 1.1;
  letter-spacing: 0;
}

.emotion-2 .amp-cms--subhead-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 18px;
  line-height: 1.1111111111111112;
  letter-spacing: 0;
}

.emotion-3 {
  display: block;
  width: 100%;
  position: relative;
  z-index: 2;
  padding-top: 15px;
  text-align: center;
}

.emotion-4 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  display: inline-block;
  text-transform: none;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 1px solid transparent;
  border-radius: 0;
  font-size: 12px;
  font-weight: 500;
  letter-spacing: 2%;
  min-height: unset;
  max-height: auto;
  line-height: 1.125;
  padding: 12px;
  width: auto;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  background-color: #FFFFFF;
  color: #000000;
  border-color: #000000;
  -webkit-align-self: flex-start;
  -ms-flex-item-align: flex-start;
  align-self: flex-start;
  white-space: normal;
  text-transform: none;
}

.emotion-4:focus {
  outline: none;
}

.emotion-4>span {
  padding: 1px 0;
}

.emotion-4:hover,
.emotion-4:focus {
  color: #FFFFFF;
  background-color: #000000;
  border-color: #000000;
}

.emotion-4:active {
  color: #FFFFFF;
  background-color: #000000;
  border-color: #000000;
}

<div>
  <div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <div
        class="emotion-0"
      >
        <div
          class="rteWrapperDivs emotion-1"
        >
          <div
            class="emotion-2"
          >
            <div>
              <p
                class="amp-cms--p"
              >
                <span
                  class="amp-cms--subhead-1"
                  style="color:#00FF00;font-weight:800"
                >
                  Body font 1
                </span>
              </p>
              <p
                class="amp-cms--p"
              >
                <span
                  class="amp-cms--subhead-2"
                  style="color:#10a6a6;font-style:italic"
                >
                  GOOD EARTH
                </span>
              </p>
            </div>
          </div>
        </div>
        <div
          class="ctaWrapperDivs emotion-3"
        >
          <a
            class="emotion-4"
            color="dark"
            href="/ss"
          >
            Get a life
          </a>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`FlexContainerWithTextAndCta Snapshot should match snapshot 1`] = `
.emotion-0 {
  width: 100%;
  height: 100%;
  padding: 50px;
  box-sizing: border-box;
  position: absolute;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  display: grid;
  grid-template-columns: repeat(3, minmax(20%,max-content));
  -webkit-align-content: center;
  -ms-flex-line-pack: center;
  align-content: center;
  grid-template-rows: 1fr max-content 1fr;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  justify-content: space-between;
}

.emotion-0 .rteWrapperDivs {
  grid-column: 2;
  grid-row: 2;
}

.emotion-0 .ctaWrapperDivs {
  grid-column: 2;
  grid-row: 3;
  text-align: center;
}

.emotion-1 {
  box-sizing: content-box;
  text-align: center;
}

.emotion-2 {
  text-align: start;
  display: inline-block;
}

.emotion-2 .amp-cms--p {
  padding: 0;
  margin: 0;
  line-height: 0;
}

.emotion-2 .amp-cms--p>span {
  display: inline;
  white-space: break-spaces;
}

.emotion-2 .amp-cms--p a {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  position: relative;
  z-index: 2;
  pointer-events: auto;
}

.emotion-2 .amp-cms--p a:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-2 .amp-cms--legal-copy {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-2 sup {
  vertical-align: top;
  display: inline-block;
  margin-top: -0.25ex;
}

.emotion-2 sub {
  vertical-align: bottom;
  display: inline-block;
  margin-bottom: -0.25ex;
}

.emotion-2 .amp-cms--f-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-2 .amp-cms--f-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-2 .amp-cms--f-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-2 .amp-cms--fn-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-2 .amp-cms--body-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 20px;
  line-height: 1.3;
  letter-spacing: 0;
}

.emotion-2 .amp-cms--body-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 18px;
  line-height: 1.2777777777777777;
  letter-spacing: 0;
}

.emotion-2 .amp-cms--body-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 16px;
  line-height: 1.25;
  letter-spacing: 0;
}

.emotion-2 .amp-cms--body-4 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 14px;
  line-height: 1.2857142857142858;
  letter-spacing: 0;
}

.emotion-2 .amp-cms--body-5 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-2 .amp-cms--eyebrow-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 50px;
  line-height: 0.92;
  letter-spacing: 0;
}

.emotion-2 .amp-cms--eyebrow-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 40px;
  line-height: 1;
  letter-spacing: 0;
}

.emotion-2 .amp-cms--eyebrow-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 35px;
  line-height: 1;
  letter-spacing: 0;
}

.emotion-2 .amp-cms--headline-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 120px;
  line-height: 0.8333333333333334;
  letter-spacing: 0;
}

.emotion-2 .amp-cms--headline-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 110px;
  line-height: 0.8636363636363636;
  letter-spacing: 0;
}

.emotion-2 .amp-cms--headline-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 100px;
  line-height: 0.85;
  letter-spacing: 0;
}

.emotion-2 .amp-cms--headline-4 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 100px;
  line-height: 1.05;
  letter-spacing: 0;
}

.emotion-2 .amp-cms--headline-5 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 80px;
  line-height: 0.9375;
  letter-spacing: 0;
}

.emotion-2 .amp-cms--headline-6 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 70px;
  line-height: 0.9285714285714286;
  letter-spacing: 0;
}

.emotion-2 .amp-cms--headline-7 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 60px;
  line-height: 0.9166666666666666;
  letter-spacing: 0;
}

.emotion-2 .amp-cms--headlineAlt-1 {
  color: #2B2B2B;
  font-size: 114px;
  line-height: 1;
  letter-spacing: -0.4px;
  font-weight: 400;
}

.emotion-2 .amp-cms--headlineAlt-2 {
  color: #2B2B2B;
  font-size: 84px;
  line-height: 1;
  letter-spacing: -0.4px;
  font-weight: 400;
}

.emotion-2 .amp-cms--headlineAlt-3 {
  color: #2B2B2B;
  font-size: 64px;
  line-height: 1;
  letter-spacing: -0.4px;
  font-weight: 400;
}

.emotion-2 .amp-cms--headlineAlt-4 {
  color: #2B2B2B;
  font-size: 34px;
  line-height: 1;
  letter-spacing: -0.4px;
  font-weight: 400;
}

.emotion-2 .amp-cms--headlineAlt-5 {
  color: #2B2B2B;
  font-size: 16px;
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-2 .amp-cms--headlineAlt-6 {
  color: #2B2B2B;
  font-size: 16px;
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-2 .amp-cms--headlineAlt-7 {
  color: #2B2B2B;
  font-size: 16px;
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-2 .amp-cms--promo-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 100px;
  line-height: 1;
  letter-spacing: 0px;
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-2 .amp-cms--promo-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 80px;
  line-height: 1;
  letter-spacing: 0px;
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-2 .amp-cms--promoAlt-1 {
  color: #2B2B2B;
  font-size: 16px;
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 600;
}

.emotion-2 .amp-cms--subhead-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 30px;
  line-height: 1;
  letter-spacing: 0;
}

.emotion-2 .amp-cms--subhead-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 25px;
  line-height: 1;
  letter-spacing: 0;
}

.emotion-2 .amp-cms--subhead-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 20px;
  line-height: 1.1;
  letter-spacing: 0;
}

.emotion-3 {
  display: block;
  width: 100%;
  position: relative;
  z-index: 2;
  padding-top: 15px;
  text-align: center;
}

.emotion-4 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  display: inline-block;
  text-transform: none;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 1px solid transparent;
  border-radius: 0;
  max-width: 300px;
  font-size: 12px;
  font-weight: 500;
  letter-spacing: 2%;
  min-height: unset;
  max-height: auto;
  line-height: 12px;
  padding: 12px;
  width: auto;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  background-color: #FFFFFF;
  color: #000000;
  border-color: #000000;
  -webkit-align-self: flex-start;
  -ms-flex-item-align: flex-start;
  align-self: flex-start;
  white-space: normal;
  text-transform: none;
}

.emotion-4:focus {
  outline: none;
}

.emotion-4>span {
  padding: 1px 0;
}

.emotion-4:hover,
.emotion-4:focus {
  color: #FFFFFF;
  background-color: #000000;
  border-color: #000000;
}

.emotion-4:active {
  color: #FFFFFF;
  background-color: #000000;
  border-color: #000000;
}

<div>
  <div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <div
        class="emotion-0"
      >
        <div
          class="rteWrapperDivs emotion-1"
        >
          <div
            class="emotion-2"
          >
            <div>
              <p
                class="amp-cms--p"
              >
                <span
                  class="amp-cms--subhead-1"
                  style="color:#00FF00;font-weight:800"
                >
                  Body font 1
                </span>
              </p>
              <p
                class="amp-cms--p"
              >
                <span
                  class="amp-cms--subhead-2"
                  style="color:#10a6a6;font-style:italic"
                >
                  GOOD EARTH
                </span>
              </p>
            </div>
          </div>
        </div>
        <div
          class="ctaWrapperDivs emotion-3"
        >
          <a
            class="emotion-4"
            color="dark"
            href="/ss"
          >
            Get a life
          </a>
        </div>
      </div>
    </div>
  </div>
</div>
`;
