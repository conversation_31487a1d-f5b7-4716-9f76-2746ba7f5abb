// @ts-nocheck
'use client';
import React, { useEffect, useRef, useState } from 'react';
import { useLocalize } from '@ecom-next/sitewide/localization-provider';
import { useTheme } from '@ecom-next/core/react-stitch';
import { Carousel } from '@ecom-next/core/legacy/carousel';
import { ColorTheme } from '@ecom-next/core/legacy/icons';
import { safeWindow } from '../../../../../helper/safeWindow';
import { slidesToScroll, visualNavDefaultSlidesToShow } from '../../../../subcomponents/CMSMarketingCarousel/helpers';
import { IntroCard } from '../../components/Cards';
import { CardImage, CardText } from '../../components';
import { RichText } from '../../../../subcomponents/RichText';
import { VisualNavigationProps, CarouselCardProps } from '../../types';
import { encodeAdvanceImage } from '../../../../helpers/ImageEncoder';
import { useCardPercentHeight } from '../../helpers';
import { useViewportIsMobile } from '../../../../../hooks/useViewportIsMobile';
import { AT_ON_MAX_WIDTH_PX } from '../../../../subcomponents/ComponentMaxWidth';
import { CustomArrow, TitleWrapper, HeadlineWrapper, Link, NavigationWrapper, CardWrapper } from '..';
import debounce from '../../../../../helper/debounce';
import { getCardImageDimensions } from '../../helpers/getCardImageDimensions';
import { PRODUCT_GRID_CLASSNAME } from '../../../../constants';
import { jumpToSection } from '../../../../../helper/jumpToSection';

export const CarouselDesktop: React.FC<VisualNavigationProps> = props => {
  const { headline, categoryCards, introCards, webAppearance } = props;
  const theme = useTheme();
  const checkMobile = useViewportIsMobile();
  const navWrapperRef = useRef<HTMLDivElement>(null);
  const navRef = useRef<HTMLDivElement>(null);
  const cardImageRef = useRef<HTMLDivElement | null>(null);
  const [sliderVP, setSliderVP] = useState<number | undefined>(undefined);
  const [navHeight, setNavHeight] = useState('');
  const [textHeight, setTextHeight] = useState('100%');
  const [arrowPosition, setArrowPosition] = useState(0);
  const debounceRef = useRef(
    debounce(() => {
      setSliderVP(undefined);
    }, 250)
  );
  const { localize } = useLocalize();
  const sWindow = safeWindow();

  useEffect(() => {
    if (!sWindow) return;
    sWindow.addEventListener('resize', debounceRef.current);
    // eslint-disable-next-line consistent-return
    return () => {
      // eslint-disable-next-line react-hooks/exhaustive-deps
      sWindow.removeEventListener('resize', debounceRef.current);
    };
  }, [sWindow]);

  useEffect(() => {
    if (navWrapperRef.current) {
      const element = navWrapperRef.current.querySelector('.slick-list');
      if (element?.clientWidth !== sliderVP) {
        setSliderVP(element?.clientWidth);
      }
    }

    cardImageRef?.current && setArrowPosition(cardImageRef.current.offsetHeight / 2);

    let maxHeight = 0;
    const array = document.getElementsByClassName('cardTextWrapper');
    Object.keys(array).forEach((key, i) => {
      if (maxHeight < array[i].getBoundingClientRect().height) {
        maxHeight = array[i].getBoundingClientRect().height;
      }
    });
    setTextHeight(maxHeight.toString());
    navRef?.current && setNavHeight((navRef?.current?.offsetHeight + maxHeight).toString());
  }, [sliderVP]);

  let flexStr = '0 0 50%';
  const introCardData = introCards && introCards[0];
  const cardsCopy = introCards ? [introCardData, ...categoryCards] : [...categoryCards];
  if (cardsCopy.length >= 3) flexStr = '0 0 40%';
  const flexStyles = (checkMobile || cardsCopy.length < 3) && {
    flex: flexStr,
  };

  const isAthleta = theme.brand === 'at';
  const isGapOrGapFactory = theme.brand === 'gap' || theme.brand === 'gapfs';
  const chevColor = webAppearance?.chevronColor as ColorTheme;
  const isCarousel = true;
  const percentHeight = useCardPercentHeight(categoryCards.length, isCarousel);
  const cardImageDimensions = getCardImageDimensions(theme.brand, 'desktop');

  const [hoverState, setHoverState] = useState(-1);

  const setMouseOver = (index: number) => {
    !checkMobile && setHoverState(index);
  };
  const setMouseLeave = () => {
    !checkMobile && setHoverState(-1);
  };

  return (
    <NavigationWrapper ref={navWrapperRef} arrowPosition={arrowPosition} sliderVP={sliderVP} webAppearance={webAppearance}>
      {headline && (
        <HeadlineWrapper>
          <RichText isDesktop={!checkMobile} scalableText={{ parentMaxWidthPx: AT_ON_MAX_WIDTH_PX, enable: true }} text={headline} />
        </HeadlineWrapper>
      )}
      <Carousel
        arrowPosition='0'
        css={
          isGapOrGapFactory && {
            borderTop: `solid 1px ${theme.color.b1}`,
            width: 'calc(100% - 1px)',
          }
        }
        nextArrow={<CustomArrow arrowType='next' chevronColor={chevColor} />}
        nextArrowAlt={localize('cms.carousel.next')}
        prevArrow={<CustomArrow arrowType='prev' chevronColor={chevColor} />}
        prevArrowAlt={localize('cms.carousel.previous')}
        slidesToScroll={slidesToScroll(visualNavDefaultSlidesToShow)}
        slidesToShow={visualNavDefaultSlidesToShow}
        variableWidth
      >
        {cardsCopy.map((card: CarouselCardProps, index: number) => {
          const hoverOption = card?.hoverOptions && card?.hoverOptions[0];
          const zoom = hoverOption?.zoom;
          const color = hoverOption?.color;
          const transparency = hoverOption?.transparency;
          const hoverImage = hoverOption?.image;
          const hoverAdvImageData = hoverImage && hoverImage[0];
          let imageAspect = checkMobile ? '1:2' : '1.5:1';
          if (cardsCopy.length > 2) {
            imageAspect = checkMobile ? '72:65' : '';
          }
          const opts = {
            aspect: imageAspect,
            height: 820,
          };
          const viewportSize = checkMobile ? 'mobile' : 'desktop';

          const hoverimageUrl = hoverAdvImageData?.image && encodeAdvanceImage(hoverAdvImageData, viewportSize, opts);
          const likeIndex = index;

          if (introCards && index === 0) {
            return (
              <IntroCard
                key={card?.backgroundImage && card?.backgroundImage[0].image?.name}
                cardCount={cardsCopy.length}
                flexStyles={flexStyles}
                height={Number(navHeight)}
                introCardData={introCardData}
                isCarousel
                isMobile={checkMobile}
                webAppearance={webAppearance}
              />
            );
          }
          return (
            <CardWrapper
              key={`${card?.description}-${card?.heading}-${likeIndex}-${card?.description}`}
              categoryCards={categoryCards}
              data-testid='vis-nav-card-content'
              fontColor={color}
              hoverimageUrl={hoverimageUrl}
              introCards={introCards}
              onFocus={() => setMouseOver(index)}
              onMouseLeave={setMouseLeave}
              onMouseOver={() => setMouseOver(index)}
              transparency={transparency}
              zoom={zoom}
            >
              <Link
                href={card?.url?.value}
                onClick={() => {
                  if (card?.url?.value.startsWith('#')) {
                    jumpToSection(true, PRODUCT_GRID_CLASSNAME);
                  }
                }}
              >
                <div ref={navRef}>
                  {isAthleta && (
                    <TitleWrapper className='vis-nav-title-wrapper'>
                      <RichText
                        isDesktop={!checkMobile}
                        scalableText={{
                          disableInfiniteScaling: true,
                          enable: true,
                        }}
                        text={card?.heading || ''}
                      />
                    </TitleWrapper>
                  )}
                  <CardImage
                    ref={cardImageRef}
                    aspectRatioHeight={cardImageDimensions.height || percentHeight}
                    cardCount={cardsCopy.length}
                    height={cardImageDimensions.height}
                    hoverData={card?.hoverOptions}
                    image={card?.image}
                    index={index}
                  />
                </div>
                <CardText
                  description={card?.description}
                  hasIntroCard={!!introCardData}
                  heading={card?.heading}
                  hoverState={hoverState === index}
                  index={index}
                  isCarousel
                  price={card?.price}
                  textHeight={textHeight}
                />
              </Link>
            </CardWrapper>
          );
        })}
      </Carousel>
    </NavigationWrapper>
  );
};
