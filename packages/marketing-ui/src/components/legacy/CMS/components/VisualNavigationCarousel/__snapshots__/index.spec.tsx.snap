// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`VisualNavigationCarousel on desktop should match snapshot 1`] = `
.emotion-0 {
  overflow: hidden;
}

.emotion-0 div.slick-track {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  gap: min(0vw, 0px);
}

.emotion-0 div.slick-track:before {
  display: none;
}

.emotion-0 div.slick-track:after {
  display: none;
}

.emotion-0 div.slick-slide {
  height: auto;
}

.emotion-0 div.slick-slide>div {
  height: 100%;
}

.emotion-1 {
  position: relative;
}

.emotion-1 .slick-slider {
  position: relative;
  display: block;
  box-sizing: border-box;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  -webkit-touch-callout: none;
  touch-action: pan-y;
  -webkit-tap-highlight-color: transparent;
  scroll-behavior: smooth;
}

.emotion-1 .slick-slider .slick-track,
.emotion-1 .slick-slider .slick-list {
  -webkit-transform: translate3d(0, 0, 0);
  -moz-transform: translate3d(0, 0, 0);
  -ms-transform: translate3d(0, 0, 0);
  transform: translate3d(0, 0, 0);
}

.emotion-1 .slick-list {
  position: relative;
  display: block;
  overflow-x: hidden;
  overflow-y: hidden;
  margin: 0;
  padding: 0;
}

.emotion-1 .slick-list:focus {
  outline: none;
}

.emotion-1 .slick-list.dragging {
  cursor: pointer;
  cursor: hand;
}

.emotion-1 .slick-track {
  position: relative;
  top: 0;
  left: 0;
  display: block;
  margin-left: auto;
  margin-right: auto;
}

.emotion-1 .slick-track:before,
.emotion-1 .slick-track:after {
  display: table;
  content: "";
}

.emotion-1 .slick-track:after {
  clear: both;
}

.emotion-1 .slick-slide {
  display: none;
  float: left;
  height: 100%;
  min-height: 1px;
}

.emotion-1 .slick-slide img {
  display: block;
}

.emotion-1 .slick-slide.slick-loading img {
  display: none;
}

.emotion-1 .slick-slide.dragging img {
  pointer-events: none;
}

.emotion-1 [dir="rtl"] .slick-slide {
  float: right;
}

.emotion-1 .slick-initialized .slick-slide,
.emotion-1 .slick-vertical .slick-slide {
  display: block;
}

.emotion-1 .slick-vertical .slick-slide {
  border: 1px solid transparent;
  height: auto;
}

.emotion-1 .slick-loading .slick-track,
.emotion-1 .slick-loading .slick-slide {
  visibility: hidden;
}

.emotion-1 .slick-loading .slick-list {
  background: #fff url("./ajax-loader.gif") center center no-repeat;
}

.emotion-1 .slick-arrow.slick-hidden {
  display: none;
}

.emotion-1 .slick-prev,
.emotion-1 .slick-next {
  font-size: 0;
  line-height: 0;
  position: absolute;
  top: 50%;
  display: block;
  padding: 0;
  cursor: pointer;
  color: transparent;
  border: none;
  outline: none;
  background: transparent;
}

.emotion-1 .slick-prev:hover,
.emotion-1 .slick-next:hover,
.emotion-1 .slick-prev:focus,
.emotion-1 .slick-next:focus {
  color: transparent;
  outline: none;
  background: transparent;
  opacity: 1;
}

.emotion-1 .slick-prev.slick-disabled,
.emotion-1 .slick-next.slick-disabled {
  opacity: 0.25;
}

.emotion-1 .slick-prev {
  left: -0px;
  -webkit-transform: translate(0, -50%) rotate(90deg);
  -moz-transform: translate(0, -50%) rotate(90deg);
  -ms-transform: translate(0, -50%) rotate(90deg);
  transform: translate(0, -50%) rotate(90deg);
}

.emotion-1 .slick-prev span {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-1 [dir="rtl"] .slick-prev {
  right: -0px;
  left: auto;
}

.emotion-1 .slick-next {
  right: -0px;
  -webkit-transform: translate(0, -50%) rotate(-90deg);
  -moz-transform: translate(0, -50%) rotate(-90deg);
  -ms-transform: translate(0, -50%) rotate(-90deg);
  transform: translate(0, -50%) rotate(-90deg);
}

.emotion-1 .slick-next span {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-1 [dir="rtl"] .slick-next {
  right: auto;
  left: -0px;
}

.emotion-1 .slick-dotted.slick-slider {
  margin-bottom: 30px;
}

.emotion-1 .slick-dots {
  position: absolute;
  bottom: -25px;
  display: block;
  width: 100%;
  padding: 0;
  margin: 0;
  list-style: none;
  text-align: center;
}

.emotion-1 .slick-dots li {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: 20px;
  height: 20px;
  margin: 0 5px;
  padding: 0;
  cursor: pointer;
}

.emotion-1 .slick-dots li.slick-active button:before {
  opacity: 0.75;
  color: black;
}

.emotion-1 .slick-dots li button {
  font-size: 0;
  line-height: 0;
  display: block;
  padding: 5px;
  cursor: pointer;
  color: transparent;
  border: 0;
  outline: none;
  background: transparent;
}

.emotion-1 .slick-dots li button:hover,
.emotion-1 .slick-dots li button:focus {
  outline: none;
}

.emotion-1 .slick-dots li button:hover:before,
.emotion-1 .slick-dots li button:focus:before,
.emotion-1 .slick-dots li button:hover:before,
.emotion-1 .slick-dots li button:focus:before {
  opacity: 1;
}

.emotion-1 .slick-dots li button:before {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 5px;
  height: 5px;
  content: "";
  opacity: 0.25;
  background-color: black;
  border-radius: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-2.slick-prev.slick-arrow {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: min(3.4375000000000004vw, 44px);
  height: min(3.4375000000000004vw, 44px);
  z-index: 2;
}

.emotion-2.slick-prev.slick-arrow.slick-disabled {
  display: none;
}

.emotion-2.slick-prev.slick-arrow>span {
  margin: auto;
  height: auto;
  width: auto;
  min-height: auto;
  min-width: auto;
}

.emotion-2.slick-prev.slick-arrow svg {
  margin: auto;
  height: min(1.953125vw, 25px);
  width: min(1.40625vw, 18px);
}

.emotion-3 {
  display: inline-block;
  height: 2rem;
  width: 2rem;
  min-height: 2rem;
  min-width: 2rem;
}

.emotion-3 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-4.slick-next.slick-arrow {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: min(3.4375000000000004vw, 44px);
  height: min(3.4375000000000004vw, 44px);
  z-index: 2;
}

.emotion-4.slick-next.slick-arrow.slick-disabled {
  display: none;
}

.emotion-4.slick-next.slick-arrow>span {
  margin: auto;
  height: auto;
  width: auto;
  min-height: auto;
  min-width: auto;
}

.emotion-4.slick-next.slick-arrow svg {
  margin: auto;
  height: min(1.953125vw, 25px);
  width: min(1.40625vw, 18px);
}

<div>
  <div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      class="emotion-0"
    >
      <div
        class="emotion-1"
      >
        <div
          class="slick-slider slick-initialized"
          dir="ltr"
        >
          <button
            aria-label="Previous"
            class="slick-prev slick-arrow slick-prev emotion-2"
          >
            <span
              aria-hidden="true"
              class="emotion-3"
            >
              <svg
                viewBox="0 0 18 7.742"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M0,.839,6.44,9,0,17.162.818,18,7.743,9.225,7.522,9l.22-.225L.818,0Z"
                  fill="#FFFFFF"
                  transform="translate(18) rotate(90)"
                />
              </svg>
            </span>
          </button>
          <div
            class="slick-list"
          >
            <div
              class="slick-track"
              style="opacity: 1; transform: translate3d(0px, 0px, 0px);"
            >
              <div
                aria-hidden="true"
                class="slick-slide slick-cloned"
                data-index="-5"
                style="width: 0px;"
                tabindex="-1"
              >
                <div>
                  <div
                    class="slide"
                    style="width: 100%; display: inline-block;"
                    tabindex="-1"
                  />
                </div>
              </div>
              <div
                aria-hidden="true"
                class="slick-slide slick-cloned"
                data-index="-4"
                style="width: 0px;"
                tabindex="-1"
              >
                <div>
                  <div
                    class="slide"
                    style="width: 100%; display: inline-block;"
                    tabindex="-1"
                  />
                </div>
              </div>
              <div
                aria-hidden="true"
                class="slick-slide slick-cloned"
                data-index="-3"
                style="width: 0px;"
                tabindex="-1"
              >
                <div>
                  <div
                    class="slide"
                    style="width: 100%; display: inline-block;"
                    tabindex="-1"
                  />
                </div>
              </div>
              <div
                aria-hidden="true"
                class="slick-slide slick-cloned"
                data-index="-2"
                style="width: 0px;"
                tabindex="-1"
              >
                <div>
                  <div
                    class="slide"
                    style="width: 100%; display: inline-block;"
                    tabindex="-1"
                  />
                </div>
              </div>
              <div
                aria-hidden="true"
                class="slick-slide slick-cloned"
                data-index="-1"
                style="width: 0px;"
                tabindex="-1"
              >
                <div>
                  <div
                    class="slide"
                    style="width: 100%; display: inline-block;"
                    tabindex="-1"
                  />
                </div>
              </div>
              <div
                aria-hidden="false"
                class="slick-slide slick-active slick-current"
                data-index="0"
                style="outline: none; width: 0px;"
                tabindex="-1"
              >
                <div>
                  <div
                    class="slide"
                    style="width: 100%; display: inline-block;"
                    tabindex="-1"
                  />
                </div>
              </div>
              <div
                aria-hidden="false"
                class="slick-slide slick-active"
                data-index="1"
                style="outline: none; width: 0px;"
                tabindex="-1"
              >
                <div>
                  <div
                    class="slide"
                    style="width: 100%; display: inline-block;"
                    tabindex="-1"
                  />
                </div>
              </div>
              <div
                aria-hidden="false"
                class="slick-slide slick-active"
                data-index="2"
                style="outline: none; width: 0px;"
                tabindex="-1"
              >
                <div>
                  <div
                    class="slide"
                    style="width: 100%; display: inline-block;"
                    tabindex="-1"
                  />
                </div>
              </div>
              <div
                aria-hidden="false"
                class="slick-slide slick-active"
                data-index="3"
                style="outline: none; width: 0px;"
                tabindex="-1"
              >
                <div>
                  <div
                    class="slide"
                    style="width: 100%; display: inline-block;"
                    tabindex="-1"
                  />
                </div>
              </div>
              <div
                aria-hidden="false"
                class="slick-slide slick-active"
                data-index="4"
                style="outline: none; width: 0px;"
                tabindex="-1"
              >
                <div>
                  <div
                    class="slide"
                    style="width: 100%; display: inline-block;"
                    tabindex="-1"
                  />
                </div>
              </div>
              <div
                aria-hidden="false"
                class="slick-slide slick-active"
                data-index="5"
                style="outline: none; width: 0px;"
                tabindex="-1"
              >
                <div>
                  <div
                    class="slide"
                    style="width: 100%; display: inline-block;"
                    tabindex="-1"
                  />
                </div>
              </div>
              <div
                aria-hidden="true"
                class="slick-slide"
                data-index="6"
                style="outline: none; width: 0px;"
                tabindex="-1"
              >
                <div>
                  <div
                    class="slide"
                    style="width: 100%; display: inline-block;"
                    tabindex="-1"
                  />
                </div>
              </div>
              <div
                aria-hidden="true"
                class="slick-slide"
                data-index="7"
                style="outline: none; width: 0px;"
                tabindex="-1"
              >
                <div>
                  <div
                    class="slide"
                    style="width: 100%; display: inline-block;"
                    tabindex="-1"
                  />
                </div>
              </div>
              <div
                aria-hidden="true"
                class="slick-slide"
                data-index="8"
                style="outline: none; width: 0px;"
                tabindex="-1"
              >
                <div>
                  <div
                    class="slide"
                    style="width: 100%; display: inline-block;"
                    tabindex="-1"
                  />
                </div>
              </div>
              <div
                aria-hidden="true"
                class="slick-slide"
                data-index="9"
                style="outline: none; width: 0px;"
                tabindex="-1"
              >
                <div>
                  <div
                    class="slide"
                    style="width: 100%; display: inline-block;"
                    tabindex="-1"
                  />
                </div>
              </div>
              <div
                aria-hidden="true"
                class="slick-slide slick-cloned"
                data-index="10"
                style="width: 0px;"
                tabindex="-1"
              >
                <div>
                  <div
                    class="slide"
                    style="width: 100%; display: inline-block;"
                    tabindex="-1"
                  />
                </div>
              </div>
              <div
                aria-hidden="true"
                class="slick-slide slick-cloned"
                data-index="11"
                style="width: 0px;"
                tabindex="-1"
              >
                <div>
                  <div
                    class="slide"
                    style="width: 100%; display: inline-block;"
                    tabindex="-1"
                  />
                </div>
              </div>
              <div
                aria-hidden="true"
                class="slick-slide slick-cloned"
                data-index="12"
                style="width: 0px;"
                tabindex="-1"
              >
                <div>
                  <div
                    class="slide"
                    style="width: 100%; display: inline-block;"
                    tabindex="-1"
                  />
                </div>
              </div>
              <div
                aria-hidden="true"
                class="slick-slide slick-cloned"
                data-index="13"
                style="width: 0px;"
                tabindex="-1"
              >
                <div>
                  <div
                    class="slide"
                    style="width: 100%; display: inline-block;"
                    tabindex="-1"
                  />
                </div>
              </div>
              <div
                aria-hidden="true"
                class="slick-slide slick-cloned"
                data-index="14"
                style="width: 0px;"
                tabindex="-1"
              >
                <div>
                  <div
                    class="slide"
                    style="width: 100%; display: inline-block;"
                    tabindex="-1"
                  />
                </div>
              </div>
              <div
                aria-hidden="true"
                class="slick-slide slick-cloned"
                data-index="15"
                style="width: 0px;"
                tabindex="-1"
              >
                <div>
                  <div
                    class="slide"
                    style="width: 100%; display: inline-block;"
                    tabindex="-1"
                  />
                </div>
              </div>
              <div
                aria-hidden="true"
                class="slick-slide slick-cloned"
                data-index="16"
                style="width: 0px;"
                tabindex="-1"
              >
                <div>
                  <div
                    class="slide"
                    style="width: 100%; display: inline-block;"
                    tabindex="-1"
                  />
                </div>
              </div>
              <div
                aria-hidden="true"
                class="slick-slide slick-cloned"
                data-index="17"
                style="width: 0px;"
                tabindex="-1"
              >
                <div>
                  <div
                    class="slide"
                    style="width: 100%; display: inline-block;"
                    tabindex="-1"
                  />
                </div>
              </div>
              <div
                aria-hidden="true"
                class="slick-slide slick-cloned"
                data-index="18"
                style="width: 0px;"
                tabindex="-1"
              >
                <div>
                  <div
                    class="slide"
                    style="width: 100%; display: inline-block;"
                    tabindex="-1"
                  />
                </div>
              </div>
              <div
                aria-hidden="true"
                class="slick-slide slick-cloned"
                data-index="19"
                style="width: 0px;"
                tabindex="-1"
              >
                <div>
                  <div
                    class="slide"
                    style="width: 100%; display: inline-block;"
                    tabindex="-1"
                  />
                </div>
              </div>
            </div>
          </div>
          <button
            aria-label="Next"
            class="slick-next slick-arrow slick-next emotion-4"
          >
            <span
              aria-hidden="true"
              class="emotion-3"
            >
              <svg
                viewBox="0 0 18 7.742"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M0,.839,6.44,9,0,17.162.818,18,7.743,9.225,7.522,9l.22-.225L.818,0Z"
                  fill="#FFFFFF"
                  transform="translate(18) rotate(90)"
                />
              </svg>
            </span>
          </button>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`VisualNavigationCarousel on mobile should match snapshot 1`] = `
.emotion-0 {
  overflow: hidden;
}

.emotion-0 div.slick-track {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  gap: min(0vw, 0px);
}

.emotion-0 div.slick-track:before {
  display: none;
}

.emotion-0 div.slick-track:after {
  display: none;
}

.emotion-0 div.slick-slide {
  height: auto;
}

.emotion-0 div.slick-slide>div {
  height: 100%;
}

.emotion-1 {
  position: relative;
}

.emotion-1 .slick-slider {
  position: relative;
  display: block;
  box-sizing: border-box;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  -webkit-touch-callout: none;
  touch-action: pan-y;
  -webkit-tap-highlight-color: transparent;
  scroll-behavior: smooth;
}

.emotion-1 .slick-slider .slick-track,
.emotion-1 .slick-slider .slick-list {
  -webkit-transform: translate3d(0, 0, 0);
  -moz-transform: translate3d(0, 0, 0);
  -ms-transform: translate3d(0, 0, 0);
  transform: translate3d(0, 0, 0);
}

.emotion-1 .slick-list {
  position: relative;
  display: block;
  overflow-x: hidden;
  overflow-y: hidden;
  margin: 0;
  padding: 0;
}

.emotion-1 .slick-list:focus {
  outline: none;
}

.emotion-1 .slick-list.dragging {
  cursor: pointer;
  cursor: hand;
}

.emotion-1 .slick-track {
  position: relative;
  top: 0;
  left: 0;
  display: block;
  margin-left: auto;
  margin-right: auto;
}

.emotion-1 .slick-track:before,
.emotion-1 .slick-track:after {
  display: table;
  content: "";
}

.emotion-1 .slick-track:after {
  clear: both;
}

.emotion-1 .slick-slide {
  display: none;
  float: left;
  height: 100%;
  min-height: 1px;
}

.emotion-1 .slick-slide img {
  display: block;
}

.emotion-1 .slick-slide.slick-loading img {
  display: none;
}

.emotion-1 .slick-slide.dragging img {
  pointer-events: none;
}

.emotion-1 [dir="rtl"] .slick-slide {
  float: right;
}

.emotion-1 .slick-initialized .slick-slide,
.emotion-1 .slick-vertical .slick-slide {
  display: block;
}

.emotion-1 .slick-vertical .slick-slide {
  border: 1px solid transparent;
  height: auto;
}

.emotion-1 .slick-loading .slick-track,
.emotion-1 .slick-loading .slick-slide {
  visibility: hidden;
}

.emotion-1 .slick-loading .slick-list {
  background: #fff url("./ajax-loader.gif") center center no-repeat;
}

.emotion-1 .slick-arrow.slick-hidden {
  display: none;
}

.emotion-1 .slick-prev,
.emotion-1 .slick-next {
  font-size: 0;
  line-height: 0;
  position: absolute;
  top: 50%;
  display: block;
  padding: 0;
  cursor: pointer;
  color: transparent;
  border: none;
  outline: none;
  background: transparent;
}

.emotion-1 .slick-prev:hover,
.emotion-1 .slick-next:hover,
.emotion-1 .slick-prev:focus,
.emotion-1 .slick-next:focus {
  color: transparent;
  outline: none;
  background: transparent;
  opacity: 1;
}

.emotion-1 .slick-prev.slick-disabled,
.emotion-1 .slick-next.slick-disabled {
  opacity: 0.25;
}

.emotion-1 .slick-prev {
  left: -0px;
  -webkit-transform: translate(0, -50%) rotate(90deg);
  -moz-transform: translate(0, -50%) rotate(90deg);
  -ms-transform: translate(0, -50%) rotate(90deg);
  transform: translate(0, -50%) rotate(90deg);
}

.emotion-1 .slick-prev span {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-1 [dir="rtl"] .slick-prev {
  right: -0px;
  left: auto;
}

.emotion-1 .slick-next {
  right: -0px;
  -webkit-transform: translate(0, -50%) rotate(-90deg);
  -moz-transform: translate(0, -50%) rotate(-90deg);
  -ms-transform: translate(0, -50%) rotate(-90deg);
  transform: translate(0, -50%) rotate(-90deg);
}

.emotion-1 .slick-next span {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-1 [dir="rtl"] .slick-next {
  right: auto;
  left: -0px;
}

.emotion-1 .slick-dotted.slick-slider {
  margin-bottom: 30px;
}

.emotion-1 .slick-dots {
  position: absolute;
  bottom: -25px;
  display: block;
  width: 100%;
  padding: 0;
  margin: 0;
  list-style: none;
  text-align: center;
}

.emotion-1 .slick-dots li {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: 20px;
  height: 20px;
  margin: 0 5px;
  padding: 0;
  cursor: pointer;
}

.emotion-1 .slick-dots li.slick-active button:before {
  opacity: 0.75;
  color: black;
}

.emotion-1 .slick-dots li button {
  font-size: 0;
  line-height: 0;
  display: block;
  padding: 5px;
  cursor: pointer;
  color: transparent;
  border: 0;
  outline: none;
  background: transparent;
}

.emotion-1 .slick-dots li button:hover,
.emotion-1 .slick-dots li button:focus {
  outline: none;
}

.emotion-1 .slick-dots li button:hover:before,
.emotion-1 .slick-dots li button:focus:before,
.emotion-1 .slick-dots li button:hover:before,
.emotion-1 .slick-dots li button:focus:before {
  opacity: 1;
}

.emotion-1 .slick-dots li button:before {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 5px;
  height: 5px;
  content: "";
  opacity: 0.25;
  background-color: black;
  border-radius: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

<div>
  <div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      class="emotion-0"
    >
      <div
        class="emotion-1"
      >
        <div
          class="slick-slider slick-initialized"
          dir="ltr"
        >
          <div
            class="slick-list"
          >
            <div
              class="slick-track"
              style="opacity: 1; transform: translate3d(0px, 0px, 0px);"
            >
              <div
                aria-hidden="false"
                class="slick-slide slick-active slick-current"
                data-index="0"
                style="outline: none; width: 0px;"
                tabindex="-1"
              >
                <div>
                  <div
                    class="slide"
                    style="width: 100%; display: inline-block;"
                    tabindex="-1"
                  />
                </div>
              </div>
              <div
                aria-hidden="true"
                class="slick-slide"
                data-index="1"
                style="outline: none; width: 0px;"
                tabindex="-1"
              >
                <div>
                  <div
                    class="slide"
                    style="width: 100%; display: inline-block;"
                    tabindex="-1"
                  />
                </div>
              </div>
              <div
                aria-hidden="true"
                class="slick-slide"
                data-index="2"
                style="outline: none; width: 0px;"
                tabindex="-1"
              >
                <div>
                  <div
                    class="slide"
                    style="width: 100%; display: inline-block;"
                    tabindex="-1"
                  />
                </div>
              </div>
              <div
                aria-hidden="true"
                class="slick-slide"
                data-index="3"
                style="outline: none; width: 0px;"
                tabindex="-1"
              >
                <div>
                  <div
                    class="slide"
                    style="width: 100%; display: inline-block;"
                    tabindex="-1"
                  />
                </div>
              </div>
              <div
                aria-hidden="true"
                class="slick-slide"
                data-index="4"
                style="outline: none; width: 0px;"
                tabindex="-1"
              >
                <div>
                  <div
                    class="slide"
                    style="width: 100%; display: inline-block;"
                    tabindex="-1"
                  />
                </div>
              </div>
              <div
                aria-hidden="true"
                class="slick-slide"
                data-index="5"
                style="outline: none; width: 0px;"
                tabindex="-1"
              >
                <div>
                  <div
                    class="slide"
                    style="width: 100%; display: inline-block;"
                    tabindex="-1"
                  />
                </div>
              </div>
              <div
                aria-hidden="true"
                class="slick-slide"
                data-index="6"
                style="outline: none; width: 0px;"
                tabindex="-1"
              >
                <div>
                  <div
                    class="slide"
                    style="width: 100%; display: inline-block;"
                    tabindex="-1"
                  />
                </div>
              </div>
              <div
                aria-hidden="true"
                class="slick-slide"
                data-index="7"
                style="outline: none; width: 0px;"
                tabindex="-1"
              >
                <div>
                  <div
                    class="slide"
                    style="width: 100%; display: inline-block;"
                    tabindex="-1"
                  />
                </div>
              </div>
              <div
                aria-hidden="true"
                class="slick-slide"
                data-index="8"
                style="outline: none; width: 0px;"
                tabindex="-1"
              >
                <div>
                  <div
                    class="slide"
                    style="width: 100%; display: inline-block;"
                    tabindex="-1"
                  />
                </div>
              </div>
              <div
                aria-hidden="true"
                class="slick-slide"
                data-index="9"
                style="outline: none; width: 0px;"
                tabindex="-1"
              >
                <div>
                  <div
                    class="slide"
                    style="width: 100%; display: inline-block;"
                    tabindex="-1"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;
