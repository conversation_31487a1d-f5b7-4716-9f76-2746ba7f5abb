// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`<BodyOverlayContent /> with hero image data should match the snapshot for default test data 1`] = `
.emotion-0 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  width: 80.83333333333333%;
  padding: min(32px, 2.2222222222222223vw) min(147px, 10.208333333333334vw);
  gap: min(24px, 1.6666666666666667vw);
  pointer-events: none;
}

.emotion-1 {
  text-align: center;
}

.emotion-1 .amp-cms--p {
  padding: 0;
  margin: 0;
  line-height: 0;
}

.emotion-1 .amp-cms--p>span {
  display: inline;
  white-space: break-spaces;
}

.emotion-1 .amp-cms--p a {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  position: relative;
  z-index: 2;
  pointer-events: auto;
}

.emotion-1 .amp-cms--p a:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-1 .amp-cms--legal-copy {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(0.8333333333333334vw, 12px));
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-1 sup {
  vertical-align: top;
  display: inline-block;
  margin-top: -0.25ex;
}

.emotion-1 sub {
  vertical-align: bottom;
  display: inline-block;
  margin-bottom: -0.25ex;
}

.emotion-1 .amp-cms--f-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.8333333333333334vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--f-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.8333333333333334vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--f-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.8333333333333334vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--fn-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.8333333333333334vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--body-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(1.3888888888888888vw, 20px));
  line-height: 1.3;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--body-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(1.25vw, 18px));
  line-height: 1.2777777777777777;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--body-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(1.1111111111111112vw, 16px));
  line-height: 1.25;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--body-4 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(0.9722222222222222vw, 14px));
  line-height: 1.2857142857142858;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--body-5 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(0.8333333333333334vw, 12px));
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--eyebrow-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(35px, min(3.4722222222222223vw, 50px));
  line-height: 0.92;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--eyebrow-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(35px, min(2.7777777777777777vw, 40px));
  line-height: 1;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--eyebrow-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(35px, min(2.430555555555556vw, 35px));
  line-height: 1;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--headline-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(8.333333333333332vw, 120px));
  line-height: 0.8333333333333334;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--headline-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(7.638888888888889vw, 110px));
  line-height: 0.8636363636363636;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--headline-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(6.944444444444445vw, 100px));
  line-height: 0.85;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--headline-4 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(6.944444444444445vw, 100px));
  line-height: 1.05;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--headline-5 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(5.555555555555555vw, 80px));
  line-height: 0.9375;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--headline-6 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(4.861111111111112vw, 70px));
  line-height: 0.9285714285714286;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--headline-7 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(4.166666666666666vw, 60px));
  line-height: 0.9166666666666666;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--headlineAlt-1 {
  color: #2B2B2B;
  font-size: max(14px, min(7.916666666666666vw, 114px));
  line-height: 1;
  letter-spacing: min(-0.027777777777777776vw, -0.4px);
  font-weight: 400;
}

.emotion-1 .amp-cms--headlineAlt-2 {
  color: #2B2B2B;
  font-size: max(14px, min(5.833333333333333vw, 84px));
  line-height: 1;
  letter-spacing: min(-0.027777777777777776vw, -0.4px);
  font-weight: 400;
}

.emotion-1 .amp-cms--headlineAlt-3 {
  color: #2B2B2B;
  font-size: max(14px, min(4.444444444444445vw, 64px));
  line-height: 1;
  letter-spacing: min(-0.027777777777777776vw, -0.4px);
  font-weight: 400;
}

.emotion-1 .amp-cms--headlineAlt-4 {
  color: #2B2B2B;
  font-size: max(14px, min(2.361111111111111vw, 34px));
  line-height: 1;
  letter-spacing: min(-0.027777777777777776vw, -0.4px);
  font-weight: 400;
}

.emotion-1 .amp-cms--headlineAlt-5 {
  color: #2B2B2B;
  font-size: min(1.1111111111111112vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-1 .amp-cms--headlineAlt-6 {
  color: #2B2B2B;
  font-size: min(1.1111111111111112vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-1 .amp-cms--headlineAlt-7 {
  color: #2B2B2B;
  font-size: min(1.1111111111111112vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-1 .amp-cms--promo-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(6.944444444444445vw, 100px));
  line-height: 1;
  letter-spacing: min(0vw, 0px);
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-1 .amp-cms--promo-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(5.555555555555555vw, 80px));
  line-height: 1;
  letter-spacing: min(0vw, 0px);
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-1 .amp-cms--promoAlt-1 {
  color: #2B2B2B;
  font-size: min(1.1111111111111112vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 600;
}

.emotion-1 .amp-cms--subhead-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(2.083333333333333vw, 30px));
  line-height: 1;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--subhead-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(1.7361111111111112vw, 25px));
  line-height: 1;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--subhead-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(1.3888888888888888vw, 20px));
  line-height: 1.1;
  letter-spacing: 0;
}

.emotion-2 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  height: auto;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  gap: 60px;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

.emotion-3 {
  position: relative;
}

.emotion-4 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  display: inline-block;
  text-transform: none;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 1px solid transparent;
  border-radius: 0;
  max-width: 380px;
  font-size: 18px;
  font-weight: 500;
  letter-spacing: 2%;
  min-height: auto;
  max-height: auto;
  line-height: 18px;
  padding: 0;
  width: 100%;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: left;
  border-left: none;
  border-right: none;
  border-radius: 0;
  background-color: transparent;
  border-bottom: 0;
  -webkit-box-pack: left;
  -ms-flex-pack: left;
  -webkit-justify-content: left;
  justify-content: left;
  color: #000000;
  height: auto;
  -webkit-text-decoration: underline;
  text-decoration: underline;
  text-underline-offset: 5px;
  text-decoration-thickness: 2.3px;
  position: relative;
  z-index: 2;
  pointer-events: auto;
  text-align: left;
  line-height: 1;
  padding: 0;
  border: 0;
  text-underline-offset: 5px;
}

.emotion-4:focus {
  outline: none;
}

.emotion-4>span {
  padding: 1px 0;
}

.emotion-4:hover,
.emotion-4:focus {
  -webkit-text-decoration: unset;
  text-decoration: unset;
}

.emotion-4:active {
  -webkit-text-decoration: unset;
  text-decoration: unset;
}

.emotion-4>* {
  width: inherit;
  -webkit-text-decoration: inherit;
  text-decoration: inherit;
}

.emotion-4>span {
  padding: 0;
}

.emotion-5 {
  box-sizing: border-box;
}

.emotion-6 {
  display: inline-block;
  text-transform: none;
  position: relative;
  width: 100%;
  -webkit-text-decoration: inherit;
  text-decoration: inherit;
}

.emotion-7 {
  display: inline-block;
  height: 10px;
  width: 10px;
  min-height: 10px;
  min-width: 10px;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  position: absolute;
  right: 0;
  top: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  margin-left: 8px;
}

.emotion-7 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-7 svg path {
  fill: currentColor;
}

.emotion-7 svg rect {
  fill: currentColor;
}

.emotion-9 {
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
  background: #FFFFFF;
  box-sizing: border-box;
  list-style-type: none;
  min-width: 100%;
  overflow: hidden;
  position: relative;
  z-index: 390;
  border-width: 0;
  border-style: solid;
  text-align: left;
  letter-spacing: 0;
  pointer-events: auto;
  padding: 0px;
  position: absolute;
  max-height: 0;
  width: 100%;
  -webkit-transition: max-height .5s ease-in-out,visibility .5s;
  transition: max-height .5s ease-in-out,visibility .5s;
  visibility: hidden;
  margin-top: 12px;
  color: #000000;
  background-color: #FFFFFF;
}

.emotion-9 :hover {
  color: #FFFFFF;
  background-color: #000000;
}

.emotion-10 {
  box-sizing: border-box;
  width: 100%;
  border-bottom: none;
  letter-spacing: 1px;
  margin-left: 0;
  text-transform: none;
  padding: 12px;
  border-color: #000000;
}

.emotion-10:last-child {
  border: none;
}

.emotion-11 {
  cursor: pointer;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  padding: 0px;
  -webkit-text-decoration: none;
  text-decoration: none;
  -webkit-transition: color 0.3s ease;
  transition: color 0.3s ease;
  white-space: nowrap;
  font-size: 18px;
  text-align: center;
  color: inherit;
  -webkit-box-pack: start;
  -ms-flex-pack: start;
  -webkit-justify-content: start;
  justify-content: start;
  background-color: inherit;
  font-weight: 400;
  letter-spacing: 1px;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

<div>
  <div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      class="emotion-0"
    >
      <div
        class="emotion-1"
      >
        <div>
          <p
            class="amp-cms--p"
          >
            <span
              class="amp-cms--headline-3"
              style="color:#FFFFFF"
            >
              Large Headline Goes Here.
            </span>
          </p>
          <p
            class="amp-cms--p"
          >
            <span
              class="amp-cms--subhead-1"
              style="color:#FFFFFF"
            >
              Subheading goes here.
            </span>
          </p>
        </div>
      </div>
      <div
        class="emotion-2"
      >
        <div
          class="emotion-3"
          data-testid="ctaDropdownWrapper"
        >
          <button
            aria-expanded="false"
            class="emotion-4"
            color="dark"
          >
            <span
              class="emotion-5"
            >
              <span
                class="emotion-6"
                data-id="cta-dropdown-label"
              >
                Shop All
                <span
                  aria-hidden="true"
                  class="emotion-7"
                >
                  <svg
                    fill="none"
                    viewBox="0 0 12 12"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      clip-rule="evenodd"
                      d="M7 0H5v5H0v2h5v5h2V7h5V5H7V0z"
                      fill="#000000"
                      fill-rule="evenodd"
                    />
                  </svg>
                </span>
              </span>
            </span>
          </button>
          <div
            class="emotion-8"
          >
            <ul
              aria-hidden="true"
              class="emotion-9"
            >
              <li
                class="emotion-10"
              >
                <a
                  breakpoint="desktop"
                  class="emotion-11"
                  href="/Women"
                  target="_self"
                >
                  Women
                </a>
              </li>
              <li
                class="emotion-10"
              >
                <a
                  breakpoint="desktop"
                  class="emotion-11"
                  href="/Men"
                  target="_self"
                >
                  Men
                </a>
              </li>
              <li
                class="emotion-10"
              >
                <a
                  breakpoint="desktop"
                  class="emotion-11"
                  href="/Girls"
                  target="_self"
                >
                  Girls
                </a>
              </li>
              <li
                class="emotion-10"
              >
                <a
                  breakpoint="desktop"
                  class="emotion-11"
                  href="/Boys"
                  target="_self"
                >
                  Boys
                </a>
              </li>
              <li
                class="emotion-10"
              >
                <a
                  breakpoint="desktop"
                  class="emotion-11"
                  href="/Toddler Girls"
                  target="_self"
                >
                  Toddler Girls
                </a>
              </li>
              <li
                class="emotion-10"
              >
                <a
                  breakpoint="desktop"
                  class="emotion-11"
                  href="/Toddler Boys"
                  target="_self"
                >
                  Toddler Boys
                </a>
              </li>
              <li
                class="emotion-10"
              >
                <a
                  breakpoint="desktop"
                  class="emotion-11"
                  href="/Baby Girls"
                  target="_self"
                >
                  Baby Girls
                </a>
              </li>
              <li
                class="emotion-10"
              >
                <a
                  breakpoint="desktop"
                  class="emotion-11"
                  href="/Baby Boys"
                  target="_self"
                >
                  Baby Boys
                </a>
              </li>
            </ul>
          </div>
        </div>
        <div
          class="emotion-3"
          data-testid="ctaDropdownWrapper"
        >
          <button
            aria-expanded="false"
            class="emotion-4"
            color="dark"
          >
            <span
              class="emotion-5"
            >
              <span
                class="emotion-6"
                data-id="cta-dropdown-label"
              >
                Shop Pants
                <span
                  aria-hidden="true"
                  class="emotion-7"
                >
                  <svg
                    fill="none"
                    viewBox="0 0 12 12"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      clip-rule="evenodd"
                      d="M7 0H5v5H0v2h5v5h2V7h5V5H7V0z"
                      fill="#000000"
                      fill-rule="evenodd"
                    />
                  </svg>
                </span>
              </span>
            </span>
          </button>
          <div
            class="emotion-8"
          >
            <ul
              aria-hidden="true"
              class="emotion-9"
            >
              <li
                class="emotion-10"
              >
                <a
                  breakpoint="desktop"
                  class="emotion-11"
                  href="/jeans"
                  target="_self"
                >
                  Jeans
                </a>
              </li>
              <li
                class="emotion-10"
              >
                <a
                  breakpoint="desktop"
                  class="emotion-11"
                  href="/khakis"
                  target="_self"
                >
                  Khakis
                </a>
              </li>
              <li
                class="emotion-10"
              >
                <a
                  breakpoint="desktop"
                  class="emotion-11"
                  href="/sweatpants"
                  target="_self"
                >
                  Sweatpants Sweatpants Sweatpants Sweatpants Sweatpants Sweatpants Sweatpants
                </a>
              </li>
            </ul>
          </div>
        </div>
        <div
          class="emotion-3"
          data-testid="ctaDropdownWrapper"
        >
          <button
            aria-expanded="false"
            class="emotion-4"
            color="dark"
          >
            <span
              class="emotion-5"
            >
              <span
                class="emotion-6"
                data-id="cta-dropdown-label"
              >
                Shop New
                <span
                  aria-hidden="true"
                  class="emotion-7"
                >
                  <svg
                    fill="none"
                    viewBox="0 0 12 12"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      clip-rule="evenodd"
                      d="M7 0H5v5H0v2h5v5h2V7h5V5H7V0z"
                      fill="#000000"
                      fill-rule="evenodd"
                    />
                  </svg>
                </span>
              </span>
            </span>
          </button>
          <div
            class="emotion-8"
          >
            <ul
              aria-hidden="true"
              class="emotion-9"
            >
              <li
                class="emotion-10"
              >
                <a
                  breakpoint="desktop"
                  class="emotion-11"
                  href="/Women"
                  target="_self"
                >
                  Women
                </a>
              </li>
              <li
                class="emotion-10"
              >
                <a
                  breakpoint="desktop"
                  class="emotion-11"
                  href="/Men"
                  target="_self"
                >
                  Men
                </a>
              </li>
              <li
                class="emotion-10"
              >
                <a
                  breakpoint="desktop"
                  class="emotion-11"
                  href="/Girls"
                  target="_self"
                >
                  Girls
                </a>
              </li>
              <li
                class="emotion-10"
              >
                <a
                  breakpoint="desktop"
                  class="emotion-11"
                  href="/Boys"
                  target="_self"
                >
                  Boys
                </a>
              </li>
              <li
                class="emotion-10"
              >
                <a
                  breakpoint="desktop"
                  class="emotion-11"
                  href="/Toddler Girls"
                  target="_self"
                >
                  Toddler Girls
                </a>
              </li>
              <li
                class="emotion-10"
              >
                <a
                  breakpoint="desktop"
                  class="emotion-11"
                  href="/Toddler Boys"
                  target="_self"
                >
                  Toddler Boys
                </a>
              </li>
              <li
                class="emotion-10"
              >
                <a
                  breakpoint="desktop"
                  class="emotion-11"
                  href="/Baby Girls"
                  target="_self"
                >
                  Baby Girls
                </a>
              </li>
              <li
                class="emotion-10"
              >
                <a
                  breakpoint="desktop"
                  class="emotion-11"
                  href="/Maternity"
                  target="_self"
                >
                  Maternity
                </a>
              </li>
              <li
                class="emotion-10"
              >
                <a
                  breakpoint="desktop"
                  class="emotion-11"
                  href="/Shop for the Fam Fam"
                  target="_self"
                >
                  Shop for the Fam Fam
                </a>
              </li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`<BodyOverlayContent /> with hero image data should match the snapshot for device: desktop with a content-justification of center 1`] = `
.emotion-0 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  width: 80.83333333333333%;
  padding: min(32px, 2.2222222222222223vw) min(147px, 10.208333333333334vw);
  gap: min(24px, 1.6666666666666667vw);
  pointer-events: none;
}

.emotion-1 {
  text-align: center;
}

.emotion-1 .amp-cms--p {
  padding: 0;
  margin: 0;
  line-height: 0;
}

.emotion-1 .amp-cms--p>span {
  display: inline;
  white-space: break-spaces;
}

.emotion-1 .amp-cms--p a {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  position: relative;
  z-index: 2;
  pointer-events: auto;
}

.emotion-1 .amp-cms--p a:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-1 .amp-cms--legal-copy {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(0.8333333333333334vw, 12px));
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-1 sup {
  vertical-align: top;
  display: inline-block;
  margin-top: -0.25ex;
}

.emotion-1 sub {
  vertical-align: bottom;
  display: inline-block;
  margin-bottom: -0.25ex;
}

.emotion-1 .amp-cms--f-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.8333333333333334vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--f-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.8333333333333334vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--f-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.8333333333333334vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--fn-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.8333333333333334vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--body-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(1.3888888888888888vw, 20px));
  line-height: 1.3;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--body-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(1.25vw, 18px));
  line-height: 1.2777777777777777;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--body-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(1.1111111111111112vw, 16px));
  line-height: 1.25;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--body-4 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(0.9722222222222222vw, 14px));
  line-height: 1.2857142857142858;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--body-5 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(0.8333333333333334vw, 12px));
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--eyebrow-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(35px, min(3.4722222222222223vw, 50px));
  line-height: 0.92;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--eyebrow-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(35px, min(2.7777777777777777vw, 40px));
  line-height: 1;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--eyebrow-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(35px, min(2.430555555555556vw, 35px));
  line-height: 1;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--headline-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(8.333333333333332vw, 120px));
  line-height: 0.8333333333333334;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--headline-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(7.638888888888889vw, 110px));
  line-height: 0.8636363636363636;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--headline-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(6.944444444444445vw, 100px));
  line-height: 0.85;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--headline-4 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(6.944444444444445vw, 100px));
  line-height: 1.05;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--headline-5 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(5.555555555555555vw, 80px));
  line-height: 0.9375;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--headline-6 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(4.861111111111112vw, 70px));
  line-height: 0.9285714285714286;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--headline-7 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(4.166666666666666vw, 60px));
  line-height: 0.9166666666666666;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--headlineAlt-1 {
  color: #2B2B2B;
  font-size: max(14px, min(7.916666666666666vw, 114px));
  line-height: 1;
  letter-spacing: min(-0.027777777777777776vw, -0.4px);
  font-weight: 400;
}

.emotion-1 .amp-cms--headlineAlt-2 {
  color: #2B2B2B;
  font-size: max(14px, min(5.833333333333333vw, 84px));
  line-height: 1;
  letter-spacing: min(-0.027777777777777776vw, -0.4px);
  font-weight: 400;
}

.emotion-1 .amp-cms--headlineAlt-3 {
  color: #2B2B2B;
  font-size: max(14px, min(4.444444444444445vw, 64px));
  line-height: 1;
  letter-spacing: min(-0.027777777777777776vw, -0.4px);
  font-weight: 400;
}

.emotion-1 .amp-cms--headlineAlt-4 {
  color: #2B2B2B;
  font-size: max(14px, min(2.361111111111111vw, 34px));
  line-height: 1;
  letter-spacing: min(-0.027777777777777776vw, -0.4px);
  font-weight: 400;
}

.emotion-1 .amp-cms--headlineAlt-5 {
  color: #2B2B2B;
  font-size: min(1.1111111111111112vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-1 .amp-cms--headlineAlt-6 {
  color: #2B2B2B;
  font-size: min(1.1111111111111112vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-1 .amp-cms--headlineAlt-7 {
  color: #2B2B2B;
  font-size: min(1.1111111111111112vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-1 .amp-cms--promo-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(6.944444444444445vw, 100px));
  line-height: 1;
  letter-spacing: min(0vw, 0px);
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-1 .amp-cms--promo-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(5.555555555555555vw, 80px));
  line-height: 1;
  letter-spacing: min(0vw, 0px);
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-1 .amp-cms--promoAlt-1 {
  color: #2B2B2B;
  font-size: min(1.1111111111111112vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 600;
}

.emotion-1 .amp-cms--subhead-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(2.083333333333333vw, 30px));
  line-height: 1;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--subhead-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(1.7361111111111112vw, 25px));
  line-height: 1;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--subhead-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(1.3888888888888888vw, 20px));
  line-height: 1.1;
  letter-spacing: 0;
}

.emotion-2 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  height: auto;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  gap: 60px;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

.emotion-3 {
  position: relative;
}

.emotion-4 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  display: inline-block;
  text-transform: none;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 1px solid transparent;
  border-radius: 0;
  max-width: 380px;
  font-size: 18px;
  font-weight: 500;
  letter-spacing: 2%;
  min-height: auto;
  max-height: auto;
  line-height: 18px;
  padding: 0;
  width: 100%;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: left;
  border-left: none;
  border-right: none;
  border-radius: 0;
  background-color: transparent;
  border-bottom: 0;
  -webkit-box-pack: left;
  -ms-flex-pack: left;
  -webkit-justify-content: left;
  justify-content: left;
  color: #000000;
  height: auto;
  -webkit-text-decoration: underline;
  text-decoration: underline;
  text-underline-offset: 5px;
  text-decoration-thickness: 2.3px;
  position: relative;
  z-index: 2;
  pointer-events: auto;
  text-align: left;
  line-height: 1;
  padding: 0;
  border: 0;
  text-underline-offset: 5px;
}

.emotion-4:focus {
  outline: none;
}

.emotion-4>span {
  padding: 1px 0;
}

.emotion-4:hover,
.emotion-4:focus {
  -webkit-text-decoration: unset;
  text-decoration: unset;
}

.emotion-4:active {
  -webkit-text-decoration: unset;
  text-decoration: unset;
}

.emotion-4>* {
  width: inherit;
  -webkit-text-decoration: inherit;
  text-decoration: inherit;
}

.emotion-4>span {
  padding: 0;
}

.emotion-5 {
  box-sizing: border-box;
}

.emotion-6 {
  display: inline-block;
  text-transform: none;
  position: relative;
  width: 100%;
  -webkit-text-decoration: inherit;
  text-decoration: inherit;
}

.emotion-7 {
  display: inline-block;
  height: 10px;
  width: 10px;
  min-height: 10px;
  min-width: 10px;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  position: absolute;
  right: 0;
  top: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  margin-left: 8px;
}

.emotion-7 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-7 svg path {
  fill: currentColor;
}

.emotion-7 svg rect {
  fill: currentColor;
}

.emotion-9 {
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
  background: #FFFFFF;
  box-sizing: border-box;
  list-style-type: none;
  min-width: 100%;
  overflow: hidden;
  position: relative;
  z-index: 390;
  border-width: 0;
  border-style: solid;
  text-align: left;
  letter-spacing: 0;
  pointer-events: auto;
  padding: 0px;
  position: absolute;
  max-height: 0;
  width: 100%;
  -webkit-transition: max-height .5s ease-in-out,visibility .5s;
  transition: max-height .5s ease-in-out,visibility .5s;
  visibility: hidden;
  margin-top: 12px;
  color: #000000;
  background-color: #FFFFFF;
}

.emotion-9 :hover {
  color: #FFFFFF;
  background-color: #000000;
}

.emotion-10 {
  box-sizing: border-box;
  width: 100%;
  border-bottom: none;
  letter-spacing: 1px;
  margin-left: 0;
  text-transform: none;
  padding: 12px;
  border-color: #000000;
}

.emotion-10:last-child {
  border: none;
}

.emotion-11 {
  cursor: pointer;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  padding: 0px;
  -webkit-text-decoration: none;
  text-decoration: none;
  -webkit-transition: color 0.3s ease;
  transition: color 0.3s ease;
  white-space: nowrap;
  font-size: 18px;
  text-align: center;
  color: inherit;
  -webkit-box-pack: start;
  -ms-flex-pack: start;
  -webkit-justify-content: start;
  justify-content: start;
  background-color: inherit;
  font-weight: 400;
  letter-spacing: 1px;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

<div>
  <div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      class="emotion-0"
    >
      <div
        class="emotion-1"
      >
        <div>
          <p
            class="amp-cms--p"
          >
            <span
              class="amp-cms--headline-3"
              style="color:#FFFFFF"
            >
              Large Headline Goes Here.
            </span>
          </p>
          <p
            class="amp-cms--p"
          >
            <span
              class="amp-cms--subhead-1"
              style="color:#FFFFFF"
            >
              Subheading goes here.
            </span>
          </p>
        </div>
      </div>
      <div
        class="emotion-2"
      >
        <div
          class="emotion-3"
          data-testid="ctaDropdownWrapper"
        >
          <button
            aria-expanded="false"
            class="emotion-4"
            color="dark"
          >
            <span
              class="emotion-5"
            >
              <span
                class="emotion-6"
                data-id="cta-dropdown-label"
              >
                Shop All
                <span
                  aria-hidden="true"
                  class="emotion-7"
                >
                  <svg
                    fill="none"
                    viewBox="0 0 12 12"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      clip-rule="evenodd"
                      d="M7 0H5v5H0v2h5v5h2V7h5V5H7V0z"
                      fill="#000000"
                      fill-rule="evenodd"
                    />
                  </svg>
                </span>
              </span>
            </span>
          </button>
          <div
            class="emotion-8"
          >
            <ul
              aria-hidden="true"
              class="emotion-9"
            >
              <li
                class="emotion-10"
              >
                <a
                  breakpoint="desktop"
                  class="emotion-11"
                  href="/Women"
                  target="_self"
                >
                  Women
                </a>
              </li>
              <li
                class="emotion-10"
              >
                <a
                  breakpoint="desktop"
                  class="emotion-11"
                  href="/Men"
                  target="_self"
                >
                  Men
                </a>
              </li>
              <li
                class="emotion-10"
              >
                <a
                  breakpoint="desktop"
                  class="emotion-11"
                  href="/Girls"
                  target="_self"
                >
                  Girls
                </a>
              </li>
              <li
                class="emotion-10"
              >
                <a
                  breakpoint="desktop"
                  class="emotion-11"
                  href="/Boys"
                  target="_self"
                >
                  Boys
                </a>
              </li>
              <li
                class="emotion-10"
              >
                <a
                  breakpoint="desktop"
                  class="emotion-11"
                  href="/Toddler Girls"
                  target="_self"
                >
                  Toddler Girls
                </a>
              </li>
              <li
                class="emotion-10"
              >
                <a
                  breakpoint="desktop"
                  class="emotion-11"
                  href="/Toddler Boys"
                  target="_self"
                >
                  Toddler Boys
                </a>
              </li>
              <li
                class="emotion-10"
              >
                <a
                  breakpoint="desktop"
                  class="emotion-11"
                  href="/Baby Girls"
                  target="_self"
                >
                  Baby Girls
                </a>
              </li>
              <li
                class="emotion-10"
              >
                <a
                  breakpoint="desktop"
                  class="emotion-11"
                  href="/Baby Boys"
                  target="_self"
                >
                  Baby Boys
                </a>
              </li>
            </ul>
          </div>
        </div>
        <div
          class="emotion-3"
          data-testid="ctaDropdownWrapper"
        >
          <button
            aria-expanded="false"
            class="emotion-4"
            color="dark"
          >
            <span
              class="emotion-5"
            >
              <span
                class="emotion-6"
                data-id="cta-dropdown-label"
              >
                Shop Pants
                <span
                  aria-hidden="true"
                  class="emotion-7"
                >
                  <svg
                    fill="none"
                    viewBox="0 0 12 12"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      clip-rule="evenodd"
                      d="M7 0H5v5H0v2h5v5h2V7h5V5H7V0z"
                      fill="#000000"
                      fill-rule="evenodd"
                    />
                  </svg>
                </span>
              </span>
            </span>
          </button>
          <div
            class="emotion-8"
          >
            <ul
              aria-hidden="true"
              class="emotion-9"
            >
              <li
                class="emotion-10"
              >
                <a
                  breakpoint="desktop"
                  class="emotion-11"
                  href="/jeans"
                  target="_self"
                >
                  Jeans
                </a>
              </li>
              <li
                class="emotion-10"
              >
                <a
                  breakpoint="desktop"
                  class="emotion-11"
                  href="/khakis"
                  target="_self"
                >
                  Khakis
                </a>
              </li>
              <li
                class="emotion-10"
              >
                <a
                  breakpoint="desktop"
                  class="emotion-11"
                  href="/sweatpants"
                  target="_self"
                >
                  Sweatpants Sweatpants Sweatpants Sweatpants Sweatpants Sweatpants Sweatpants
                </a>
              </li>
            </ul>
          </div>
        </div>
        <div
          class="emotion-3"
          data-testid="ctaDropdownWrapper"
        >
          <button
            aria-expanded="false"
            class="emotion-4"
            color="dark"
          >
            <span
              class="emotion-5"
            >
              <span
                class="emotion-6"
                data-id="cta-dropdown-label"
              >
                Shop New
                <span
                  aria-hidden="true"
                  class="emotion-7"
                >
                  <svg
                    fill="none"
                    viewBox="0 0 12 12"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      clip-rule="evenodd"
                      d="M7 0H5v5H0v2h5v5h2V7h5V5H7V0z"
                      fill="#000000"
                      fill-rule="evenodd"
                    />
                  </svg>
                </span>
              </span>
            </span>
          </button>
          <div
            class="emotion-8"
          >
            <ul
              aria-hidden="true"
              class="emotion-9"
            >
              <li
                class="emotion-10"
              >
                <a
                  breakpoint="desktop"
                  class="emotion-11"
                  href="/Women"
                  target="_self"
                >
                  Women
                </a>
              </li>
              <li
                class="emotion-10"
              >
                <a
                  breakpoint="desktop"
                  class="emotion-11"
                  href="/Men"
                  target="_self"
                >
                  Men
                </a>
              </li>
              <li
                class="emotion-10"
              >
                <a
                  breakpoint="desktop"
                  class="emotion-11"
                  href="/Girls"
                  target="_self"
                >
                  Girls
                </a>
              </li>
              <li
                class="emotion-10"
              >
                <a
                  breakpoint="desktop"
                  class="emotion-11"
                  href="/Boys"
                  target="_self"
                >
                  Boys
                </a>
              </li>
              <li
                class="emotion-10"
              >
                <a
                  breakpoint="desktop"
                  class="emotion-11"
                  href="/Toddler Girls"
                  target="_self"
                >
                  Toddler Girls
                </a>
              </li>
              <li
                class="emotion-10"
              >
                <a
                  breakpoint="desktop"
                  class="emotion-11"
                  href="/Toddler Boys"
                  target="_self"
                >
                  Toddler Boys
                </a>
              </li>
              <li
                class="emotion-10"
              >
                <a
                  breakpoint="desktop"
                  class="emotion-11"
                  href="/Baby Girls"
                  target="_self"
                >
                  Baby Girls
                </a>
              </li>
              <li
                class="emotion-10"
              >
                <a
                  breakpoint="desktop"
                  class="emotion-11"
                  href="/Maternity"
                  target="_self"
                >
                  Maternity
                </a>
              </li>
              <li
                class="emotion-10"
              >
                <a
                  breakpoint="desktop"
                  class="emotion-11"
                  href="/Shop for the Fam Fam"
                  target="_self"
                >
                  Shop for the Fam Fam
                </a>
              </li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`<BodyOverlayContent /> with hero image data should match the snapshot for device: desktop with a content-justification of left 1`] = `
.emotion-0 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  width: 80.83333333333333%;
  padding: min(32px, 2.2222222222222223vw) min(147px, 10.208333333333334vw);
  gap: min(24px, 1.6666666666666667vw);
  pointer-events: none;
}

.emotion-1 {
  text-align: center;
}

.emotion-1 .amp-cms--p {
  padding: 0;
  margin: 0;
  line-height: 0;
}

.emotion-1 .amp-cms--p>span {
  display: inline;
  white-space: break-spaces;
}

.emotion-1 .amp-cms--p a {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  position: relative;
  z-index: 2;
  pointer-events: auto;
}

.emotion-1 .amp-cms--p a:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-1 .amp-cms--legal-copy {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(0.8333333333333334vw, 12px));
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-1 sup {
  vertical-align: top;
  display: inline-block;
  margin-top: -0.25ex;
}

.emotion-1 sub {
  vertical-align: bottom;
  display: inline-block;
  margin-bottom: -0.25ex;
}

.emotion-1 .amp-cms--f-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.8333333333333334vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--f-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.8333333333333334vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--f-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.8333333333333334vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--fn-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.8333333333333334vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--body-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(1.3888888888888888vw, 20px));
  line-height: 1.3;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--body-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(1.25vw, 18px));
  line-height: 1.2777777777777777;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--body-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(1.1111111111111112vw, 16px));
  line-height: 1.25;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--body-4 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(0.9722222222222222vw, 14px));
  line-height: 1.2857142857142858;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--body-5 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(0.8333333333333334vw, 12px));
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--eyebrow-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(35px, min(3.4722222222222223vw, 50px));
  line-height: 0.92;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--eyebrow-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(35px, min(2.7777777777777777vw, 40px));
  line-height: 1;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--eyebrow-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(35px, min(2.430555555555556vw, 35px));
  line-height: 1;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--headline-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(8.333333333333332vw, 120px));
  line-height: 0.8333333333333334;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--headline-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(7.638888888888889vw, 110px));
  line-height: 0.8636363636363636;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--headline-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(6.944444444444445vw, 100px));
  line-height: 0.85;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--headline-4 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(6.944444444444445vw, 100px));
  line-height: 1.05;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--headline-5 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(5.555555555555555vw, 80px));
  line-height: 0.9375;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--headline-6 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(4.861111111111112vw, 70px));
  line-height: 0.9285714285714286;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--headline-7 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(4.166666666666666vw, 60px));
  line-height: 0.9166666666666666;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--headlineAlt-1 {
  color: #2B2B2B;
  font-size: max(14px, min(7.916666666666666vw, 114px));
  line-height: 1;
  letter-spacing: min(-0.027777777777777776vw, -0.4px);
  font-weight: 400;
}

.emotion-1 .amp-cms--headlineAlt-2 {
  color: #2B2B2B;
  font-size: max(14px, min(5.833333333333333vw, 84px));
  line-height: 1;
  letter-spacing: min(-0.027777777777777776vw, -0.4px);
  font-weight: 400;
}

.emotion-1 .amp-cms--headlineAlt-3 {
  color: #2B2B2B;
  font-size: max(14px, min(4.444444444444445vw, 64px));
  line-height: 1;
  letter-spacing: min(-0.027777777777777776vw, -0.4px);
  font-weight: 400;
}

.emotion-1 .amp-cms--headlineAlt-4 {
  color: #2B2B2B;
  font-size: max(14px, min(2.361111111111111vw, 34px));
  line-height: 1;
  letter-spacing: min(-0.027777777777777776vw, -0.4px);
  font-weight: 400;
}

.emotion-1 .amp-cms--headlineAlt-5 {
  color: #2B2B2B;
  font-size: min(1.1111111111111112vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-1 .amp-cms--headlineAlt-6 {
  color: #2B2B2B;
  font-size: min(1.1111111111111112vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-1 .amp-cms--headlineAlt-7 {
  color: #2B2B2B;
  font-size: min(1.1111111111111112vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-1 .amp-cms--promo-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(6.944444444444445vw, 100px));
  line-height: 1;
  letter-spacing: min(0vw, 0px);
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-1 .amp-cms--promo-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(5.555555555555555vw, 80px));
  line-height: 1;
  letter-spacing: min(0vw, 0px);
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-1 .amp-cms--promoAlt-1 {
  color: #2B2B2B;
  font-size: min(1.1111111111111112vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 600;
}

.emotion-1 .amp-cms--subhead-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(2.083333333333333vw, 30px));
  line-height: 1;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--subhead-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(1.7361111111111112vw, 25px));
  line-height: 1;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--subhead-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(1.3888888888888888vw, 20px));
  line-height: 1.1;
  letter-spacing: 0;
}

.emotion-2 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  height: auto;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  gap: 60px;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

.emotion-3 {
  position: relative;
}

.emotion-4 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  display: inline-block;
  text-transform: none;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 1px solid transparent;
  border-radius: 0;
  max-width: 380px;
  font-size: 18px;
  font-weight: 500;
  letter-spacing: 2%;
  min-height: auto;
  max-height: auto;
  line-height: 18px;
  padding: 0;
  width: 100%;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: left;
  border-left: none;
  border-right: none;
  border-radius: 0;
  background-color: transparent;
  border-bottom: 0;
  -webkit-box-pack: left;
  -ms-flex-pack: left;
  -webkit-justify-content: left;
  justify-content: left;
  color: #000000;
  height: auto;
  -webkit-text-decoration: underline;
  text-decoration: underline;
  text-underline-offset: 5px;
  text-decoration-thickness: 2.3px;
  position: relative;
  z-index: 2;
  pointer-events: auto;
  text-align: left;
  line-height: 1;
  padding: 0;
  border: 0;
  text-underline-offset: 5px;
}

.emotion-4:focus {
  outline: none;
}

.emotion-4>span {
  padding: 1px 0;
}

.emotion-4:hover,
.emotion-4:focus {
  -webkit-text-decoration: unset;
  text-decoration: unset;
}

.emotion-4:active {
  -webkit-text-decoration: unset;
  text-decoration: unset;
}

.emotion-4>* {
  width: inherit;
  -webkit-text-decoration: inherit;
  text-decoration: inherit;
}

.emotion-4>span {
  padding: 0;
}

.emotion-5 {
  box-sizing: border-box;
}

.emotion-6 {
  display: inline-block;
  text-transform: none;
  position: relative;
  width: 100%;
  -webkit-text-decoration: inherit;
  text-decoration: inherit;
}

.emotion-7 {
  display: inline-block;
  height: 10px;
  width: 10px;
  min-height: 10px;
  min-width: 10px;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  position: absolute;
  right: 0;
  top: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  margin-left: 8px;
}

.emotion-7 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-7 svg path {
  fill: currentColor;
}

.emotion-7 svg rect {
  fill: currentColor;
}

.emotion-9 {
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
  background: #FFFFFF;
  box-sizing: border-box;
  list-style-type: none;
  min-width: 100%;
  overflow: hidden;
  position: relative;
  z-index: 390;
  border-width: 0;
  border-style: solid;
  text-align: left;
  letter-spacing: 0;
  pointer-events: auto;
  padding: 0px;
  position: absolute;
  max-height: 0;
  width: 100%;
  -webkit-transition: max-height .5s ease-in-out,visibility .5s;
  transition: max-height .5s ease-in-out,visibility .5s;
  visibility: hidden;
  margin-top: 12px;
  color: #000000;
  background-color: #FFFFFF;
}

.emotion-9 :hover {
  color: #FFFFFF;
  background-color: #000000;
}

.emotion-10 {
  box-sizing: border-box;
  width: 100%;
  border-bottom: none;
  letter-spacing: 1px;
  margin-left: 0;
  text-transform: none;
  padding: 12px;
  border-color: #000000;
}

.emotion-10:last-child {
  border: none;
}

.emotion-11 {
  cursor: pointer;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  padding: 0px;
  -webkit-text-decoration: none;
  text-decoration: none;
  -webkit-transition: color 0.3s ease;
  transition: color 0.3s ease;
  white-space: nowrap;
  font-size: 18px;
  text-align: center;
  color: inherit;
  -webkit-box-pack: start;
  -ms-flex-pack: start;
  -webkit-justify-content: start;
  justify-content: start;
  background-color: inherit;
  font-weight: 400;
  letter-spacing: 1px;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

<div>
  <div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      class="emotion-0"
    >
      <div
        class="emotion-1"
      >
        <div>
          <p
            class="amp-cms--p"
          >
            <span
              class="amp-cms--headline-3"
              style="color:#FFFFFF"
            >
              Large Headline Goes Here.
            </span>
          </p>
          <p
            class="amp-cms--p"
          >
            <span
              class="amp-cms--subhead-1"
              style="color:#FFFFFF"
            >
              Subheading goes here.
            </span>
          </p>
        </div>
      </div>
      <div
        class="emotion-2"
      >
        <div
          class="emotion-3"
          data-testid="ctaDropdownWrapper"
        >
          <button
            aria-expanded="false"
            class="emotion-4"
            color="dark"
          >
            <span
              class="emotion-5"
            >
              <span
                class="emotion-6"
                data-id="cta-dropdown-label"
              >
                Shop All
                <span
                  aria-hidden="true"
                  class="emotion-7"
                >
                  <svg
                    fill="none"
                    viewBox="0 0 12 12"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      clip-rule="evenodd"
                      d="M7 0H5v5H0v2h5v5h2V7h5V5H7V0z"
                      fill="#000000"
                      fill-rule="evenodd"
                    />
                  </svg>
                </span>
              </span>
            </span>
          </button>
          <div
            class="emotion-8"
          >
            <ul
              aria-hidden="true"
              class="emotion-9"
            >
              <li
                class="emotion-10"
              >
                <a
                  breakpoint="desktop"
                  class="emotion-11"
                  href="/Women"
                  target="_self"
                >
                  Women
                </a>
              </li>
              <li
                class="emotion-10"
              >
                <a
                  breakpoint="desktop"
                  class="emotion-11"
                  href="/Men"
                  target="_self"
                >
                  Men
                </a>
              </li>
              <li
                class="emotion-10"
              >
                <a
                  breakpoint="desktop"
                  class="emotion-11"
                  href="/Girls"
                  target="_self"
                >
                  Girls
                </a>
              </li>
              <li
                class="emotion-10"
              >
                <a
                  breakpoint="desktop"
                  class="emotion-11"
                  href="/Boys"
                  target="_self"
                >
                  Boys
                </a>
              </li>
              <li
                class="emotion-10"
              >
                <a
                  breakpoint="desktop"
                  class="emotion-11"
                  href="/Toddler Girls"
                  target="_self"
                >
                  Toddler Girls
                </a>
              </li>
              <li
                class="emotion-10"
              >
                <a
                  breakpoint="desktop"
                  class="emotion-11"
                  href="/Toddler Boys"
                  target="_self"
                >
                  Toddler Boys
                </a>
              </li>
              <li
                class="emotion-10"
              >
                <a
                  breakpoint="desktop"
                  class="emotion-11"
                  href="/Baby Girls"
                  target="_self"
                >
                  Baby Girls
                </a>
              </li>
              <li
                class="emotion-10"
              >
                <a
                  breakpoint="desktop"
                  class="emotion-11"
                  href="/Baby Boys"
                  target="_self"
                >
                  Baby Boys
                </a>
              </li>
            </ul>
          </div>
        </div>
        <div
          class="emotion-3"
          data-testid="ctaDropdownWrapper"
        >
          <button
            aria-expanded="false"
            class="emotion-4"
            color="dark"
          >
            <span
              class="emotion-5"
            >
              <span
                class="emotion-6"
                data-id="cta-dropdown-label"
              >
                Shop Pants
                <span
                  aria-hidden="true"
                  class="emotion-7"
                >
                  <svg
                    fill="none"
                    viewBox="0 0 12 12"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      clip-rule="evenodd"
                      d="M7 0H5v5H0v2h5v5h2V7h5V5H7V0z"
                      fill="#000000"
                      fill-rule="evenodd"
                    />
                  </svg>
                </span>
              </span>
            </span>
          </button>
          <div
            class="emotion-8"
          >
            <ul
              aria-hidden="true"
              class="emotion-9"
            >
              <li
                class="emotion-10"
              >
                <a
                  breakpoint="desktop"
                  class="emotion-11"
                  href="/jeans"
                  target="_self"
                >
                  Jeans
                </a>
              </li>
              <li
                class="emotion-10"
              >
                <a
                  breakpoint="desktop"
                  class="emotion-11"
                  href="/khakis"
                  target="_self"
                >
                  Khakis
                </a>
              </li>
              <li
                class="emotion-10"
              >
                <a
                  breakpoint="desktop"
                  class="emotion-11"
                  href="/sweatpants"
                  target="_self"
                >
                  Sweatpants Sweatpants Sweatpants Sweatpants Sweatpants Sweatpants Sweatpants
                </a>
              </li>
            </ul>
          </div>
        </div>
        <div
          class="emotion-3"
          data-testid="ctaDropdownWrapper"
        >
          <button
            aria-expanded="false"
            class="emotion-4"
            color="dark"
          >
            <span
              class="emotion-5"
            >
              <span
                class="emotion-6"
                data-id="cta-dropdown-label"
              >
                Shop New
                <span
                  aria-hidden="true"
                  class="emotion-7"
                >
                  <svg
                    fill="none"
                    viewBox="0 0 12 12"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      clip-rule="evenodd"
                      d="M7 0H5v5H0v2h5v5h2V7h5V5H7V0z"
                      fill="#000000"
                      fill-rule="evenodd"
                    />
                  </svg>
                </span>
              </span>
            </span>
          </button>
          <div
            class="emotion-8"
          >
            <ul
              aria-hidden="true"
              class="emotion-9"
            >
              <li
                class="emotion-10"
              >
                <a
                  breakpoint="desktop"
                  class="emotion-11"
                  href="/Women"
                  target="_self"
                >
                  Women
                </a>
              </li>
              <li
                class="emotion-10"
              >
                <a
                  breakpoint="desktop"
                  class="emotion-11"
                  href="/Men"
                  target="_self"
                >
                  Men
                </a>
              </li>
              <li
                class="emotion-10"
              >
                <a
                  breakpoint="desktop"
                  class="emotion-11"
                  href="/Girls"
                  target="_self"
                >
                  Girls
                </a>
              </li>
              <li
                class="emotion-10"
              >
                <a
                  breakpoint="desktop"
                  class="emotion-11"
                  href="/Boys"
                  target="_self"
                >
                  Boys
                </a>
              </li>
              <li
                class="emotion-10"
              >
                <a
                  breakpoint="desktop"
                  class="emotion-11"
                  href="/Toddler Girls"
                  target="_self"
                >
                  Toddler Girls
                </a>
              </li>
              <li
                class="emotion-10"
              >
                <a
                  breakpoint="desktop"
                  class="emotion-11"
                  href="/Toddler Boys"
                  target="_self"
                >
                  Toddler Boys
                </a>
              </li>
              <li
                class="emotion-10"
              >
                <a
                  breakpoint="desktop"
                  class="emotion-11"
                  href="/Baby Girls"
                  target="_self"
                >
                  Baby Girls
                </a>
              </li>
              <li
                class="emotion-10"
              >
                <a
                  breakpoint="desktop"
                  class="emotion-11"
                  href="/Maternity"
                  target="_self"
                >
                  Maternity
                </a>
              </li>
              <li
                class="emotion-10"
              >
                <a
                  breakpoint="desktop"
                  class="emotion-11"
                  href="/Shop for the Fam Fam"
                  target="_self"
                >
                  Shop for the Fam Fam
                </a>
              </li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`<BodyOverlayContent /> with hero image data should match the snapshot for device: desktop with a content-justification of right 1`] = `
.emotion-0 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  width: 80.83333333333333%;
  padding: min(32px, 2.2222222222222223vw) min(147px, 10.208333333333334vw);
  gap: min(24px, 1.6666666666666667vw);
  pointer-events: none;
}

.emotion-1 {
  text-align: center;
}

.emotion-1 .amp-cms--p {
  padding: 0;
  margin: 0;
  line-height: 0;
}

.emotion-1 .amp-cms--p>span {
  display: inline;
  white-space: break-spaces;
}

.emotion-1 .amp-cms--p a {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  position: relative;
  z-index: 2;
  pointer-events: auto;
}

.emotion-1 .amp-cms--p a:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-1 .amp-cms--legal-copy {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(0.8333333333333334vw, 12px));
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-1 sup {
  vertical-align: top;
  display: inline-block;
  margin-top: -0.25ex;
}

.emotion-1 sub {
  vertical-align: bottom;
  display: inline-block;
  margin-bottom: -0.25ex;
}

.emotion-1 .amp-cms--f-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.8333333333333334vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--f-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.8333333333333334vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--f-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.8333333333333334vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--fn-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.8333333333333334vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--body-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(1.3888888888888888vw, 20px));
  line-height: 1.3;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--body-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(1.25vw, 18px));
  line-height: 1.2777777777777777;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--body-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(1.1111111111111112vw, 16px));
  line-height: 1.25;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--body-4 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(0.9722222222222222vw, 14px));
  line-height: 1.2857142857142858;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--body-5 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(0.8333333333333334vw, 12px));
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--eyebrow-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(35px, min(3.4722222222222223vw, 50px));
  line-height: 0.92;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--eyebrow-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(35px, min(2.7777777777777777vw, 40px));
  line-height: 1;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--eyebrow-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(35px, min(2.430555555555556vw, 35px));
  line-height: 1;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--headline-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(8.333333333333332vw, 120px));
  line-height: 0.8333333333333334;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--headline-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(7.638888888888889vw, 110px));
  line-height: 0.8636363636363636;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--headline-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(6.944444444444445vw, 100px));
  line-height: 0.85;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--headline-4 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(6.944444444444445vw, 100px));
  line-height: 1.05;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--headline-5 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(5.555555555555555vw, 80px));
  line-height: 0.9375;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--headline-6 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(4.861111111111112vw, 70px));
  line-height: 0.9285714285714286;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--headline-7 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(4.166666666666666vw, 60px));
  line-height: 0.9166666666666666;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--headlineAlt-1 {
  color: #2B2B2B;
  font-size: max(14px, min(7.916666666666666vw, 114px));
  line-height: 1;
  letter-spacing: min(-0.027777777777777776vw, -0.4px);
  font-weight: 400;
}

.emotion-1 .amp-cms--headlineAlt-2 {
  color: #2B2B2B;
  font-size: max(14px, min(5.833333333333333vw, 84px));
  line-height: 1;
  letter-spacing: min(-0.027777777777777776vw, -0.4px);
  font-weight: 400;
}

.emotion-1 .amp-cms--headlineAlt-3 {
  color: #2B2B2B;
  font-size: max(14px, min(4.444444444444445vw, 64px));
  line-height: 1;
  letter-spacing: min(-0.027777777777777776vw, -0.4px);
  font-weight: 400;
}

.emotion-1 .amp-cms--headlineAlt-4 {
  color: #2B2B2B;
  font-size: max(14px, min(2.361111111111111vw, 34px));
  line-height: 1;
  letter-spacing: min(-0.027777777777777776vw, -0.4px);
  font-weight: 400;
}

.emotion-1 .amp-cms--headlineAlt-5 {
  color: #2B2B2B;
  font-size: min(1.1111111111111112vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-1 .amp-cms--headlineAlt-6 {
  color: #2B2B2B;
  font-size: min(1.1111111111111112vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-1 .amp-cms--headlineAlt-7 {
  color: #2B2B2B;
  font-size: min(1.1111111111111112vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-1 .amp-cms--promo-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(6.944444444444445vw, 100px));
  line-height: 1;
  letter-spacing: min(0vw, 0px);
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-1 .amp-cms--promo-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(5.555555555555555vw, 80px));
  line-height: 1;
  letter-spacing: min(0vw, 0px);
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-1 .amp-cms--promoAlt-1 {
  color: #2B2B2B;
  font-size: min(1.1111111111111112vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 600;
}

.emotion-1 .amp-cms--subhead-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(2.083333333333333vw, 30px));
  line-height: 1;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--subhead-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(1.7361111111111112vw, 25px));
  line-height: 1;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--subhead-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(1.3888888888888888vw, 20px));
  line-height: 1.1;
  letter-spacing: 0;
}

.emotion-2 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  height: auto;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  gap: 60px;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

.emotion-3 {
  position: relative;
}

.emotion-4 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  display: inline-block;
  text-transform: none;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 1px solid transparent;
  border-radius: 0;
  max-width: 380px;
  font-size: 18px;
  font-weight: 500;
  letter-spacing: 2%;
  min-height: auto;
  max-height: auto;
  line-height: 18px;
  padding: 0;
  width: 100%;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: left;
  border-left: none;
  border-right: none;
  border-radius: 0;
  background-color: transparent;
  border-bottom: 0;
  -webkit-box-pack: left;
  -ms-flex-pack: left;
  -webkit-justify-content: left;
  justify-content: left;
  color: #000000;
  height: auto;
  -webkit-text-decoration: underline;
  text-decoration: underline;
  text-underline-offset: 5px;
  text-decoration-thickness: 2.3px;
  position: relative;
  z-index: 2;
  pointer-events: auto;
  text-align: left;
  line-height: 1;
  padding: 0;
  border: 0;
  text-underline-offset: 5px;
}

.emotion-4:focus {
  outline: none;
}

.emotion-4>span {
  padding: 1px 0;
}

.emotion-4:hover,
.emotion-4:focus {
  -webkit-text-decoration: unset;
  text-decoration: unset;
}

.emotion-4:active {
  -webkit-text-decoration: unset;
  text-decoration: unset;
}

.emotion-4>* {
  width: inherit;
  -webkit-text-decoration: inherit;
  text-decoration: inherit;
}

.emotion-4>span {
  padding: 0;
}

.emotion-5 {
  box-sizing: border-box;
}

.emotion-6 {
  display: inline-block;
  text-transform: none;
  position: relative;
  width: 100%;
  -webkit-text-decoration: inherit;
  text-decoration: inherit;
}

.emotion-7 {
  display: inline-block;
  height: 10px;
  width: 10px;
  min-height: 10px;
  min-width: 10px;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  position: absolute;
  right: 0;
  top: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  margin-left: 8px;
}

.emotion-7 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-7 svg path {
  fill: currentColor;
}

.emotion-7 svg rect {
  fill: currentColor;
}

.emotion-9 {
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
  background: #FFFFFF;
  box-sizing: border-box;
  list-style-type: none;
  min-width: 100%;
  overflow: hidden;
  position: relative;
  z-index: 390;
  border-width: 0;
  border-style: solid;
  text-align: left;
  letter-spacing: 0;
  pointer-events: auto;
  padding: 0px;
  position: absolute;
  max-height: 0;
  width: 100%;
  -webkit-transition: max-height .5s ease-in-out,visibility .5s;
  transition: max-height .5s ease-in-out,visibility .5s;
  visibility: hidden;
  margin-top: 12px;
  color: #000000;
  background-color: #FFFFFF;
}

.emotion-9 :hover {
  color: #FFFFFF;
  background-color: #000000;
}

.emotion-10 {
  box-sizing: border-box;
  width: 100%;
  border-bottom: none;
  letter-spacing: 1px;
  margin-left: 0;
  text-transform: none;
  padding: 12px;
  border-color: #000000;
}

.emotion-10:last-child {
  border: none;
}

.emotion-11 {
  cursor: pointer;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  padding: 0px;
  -webkit-text-decoration: none;
  text-decoration: none;
  -webkit-transition: color 0.3s ease;
  transition: color 0.3s ease;
  white-space: nowrap;
  font-size: 18px;
  text-align: center;
  color: inherit;
  -webkit-box-pack: start;
  -ms-flex-pack: start;
  -webkit-justify-content: start;
  justify-content: start;
  background-color: inherit;
  font-weight: 400;
  letter-spacing: 1px;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

<div>
  <div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      class="emotion-0"
    >
      <div
        class="emotion-1"
      >
        <div>
          <p
            class="amp-cms--p"
          >
            <span
              class="amp-cms--headline-3"
              style="color:#FFFFFF"
            >
              Large Headline Goes Here.
            </span>
          </p>
          <p
            class="amp-cms--p"
          >
            <span
              class="amp-cms--subhead-1"
              style="color:#FFFFFF"
            >
              Subheading goes here.
            </span>
          </p>
        </div>
      </div>
      <div
        class="emotion-2"
      >
        <div
          class="emotion-3"
          data-testid="ctaDropdownWrapper"
        >
          <button
            aria-expanded="false"
            class="emotion-4"
            color="dark"
          >
            <span
              class="emotion-5"
            >
              <span
                class="emotion-6"
                data-id="cta-dropdown-label"
              >
                Shop All
                <span
                  aria-hidden="true"
                  class="emotion-7"
                >
                  <svg
                    fill="none"
                    viewBox="0 0 12 12"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      clip-rule="evenodd"
                      d="M7 0H5v5H0v2h5v5h2V7h5V5H7V0z"
                      fill="#000000"
                      fill-rule="evenodd"
                    />
                  </svg>
                </span>
              </span>
            </span>
          </button>
          <div
            class="emotion-8"
          >
            <ul
              aria-hidden="true"
              class="emotion-9"
            >
              <li
                class="emotion-10"
              >
                <a
                  breakpoint="desktop"
                  class="emotion-11"
                  href="/Women"
                  target="_self"
                >
                  Women
                </a>
              </li>
              <li
                class="emotion-10"
              >
                <a
                  breakpoint="desktop"
                  class="emotion-11"
                  href="/Men"
                  target="_self"
                >
                  Men
                </a>
              </li>
              <li
                class="emotion-10"
              >
                <a
                  breakpoint="desktop"
                  class="emotion-11"
                  href="/Girls"
                  target="_self"
                >
                  Girls
                </a>
              </li>
              <li
                class="emotion-10"
              >
                <a
                  breakpoint="desktop"
                  class="emotion-11"
                  href="/Boys"
                  target="_self"
                >
                  Boys
                </a>
              </li>
              <li
                class="emotion-10"
              >
                <a
                  breakpoint="desktop"
                  class="emotion-11"
                  href="/Toddler Girls"
                  target="_self"
                >
                  Toddler Girls
                </a>
              </li>
              <li
                class="emotion-10"
              >
                <a
                  breakpoint="desktop"
                  class="emotion-11"
                  href="/Toddler Boys"
                  target="_self"
                >
                  Toddler Boys
                </a>
              </li>
              <li
                class="emotion-10"
              >
                <a
                  breakpoint="desktop"
                  class="emotion-11"
                  href="/Baby Girls"
                  target="_self"
                >
                  Baby Girls
                </a>
              </li>
              <li
                class="emotion-10"
              >
                <a
                  breakpoint="desktop"
                  class="emotion-11"
                  href="/Baby Boys"
                  target="_self"
                >
                  Baby Boys
                </a>
              </li>
            </ul>
          </div>
        </div>
        <div
          class="emotion-3"
          data-testid="ctaDropdownWrapper"
        >
          <button
            aria-expanded="false"
            class="emotion-4"
            color="dark"
          >
            <span
              class="emotion-5"
            >
              <span
                class="emotion-6"
                data-id="cta-dropdown-label"
              >
                Shop Pants
                <span
                  aria-hidden="true"
                  class="emotion-7"
                >
                  <svg
                    fill="none"
                    viewBox="0 0 12 12"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      clip-rule="evenodd"
                      d="M7 0H5v5H0v2h5v5h2V7h5V5H7V0z"
                      fill="#000000"
                      fill-rule="evenodd"
                    />
                  </svg>
                </span>
              </span>
            </span>
          </button>
          <div
            class="emotion-8"
          >
            <ul
              aria-hidden="true"
              class="emotion-9"
            >
              <li
                class="emotion-10"
              >
                <a
                  breakpoint="desktop"
                  class="emotion-11"
                  href="/jeans"
                  target="_self"
                >
                  Jeans
                </a>
              </li>
              <li
                class="emotion-10"
              >
                <a
                  breakpoint="desktop"
                  class="emotion-11"
                  href="/khakis"
                  target="_self"
                >
                  Khakis
                </a>
              </li>
              <li
                class="emotion-10"
              >
                <a
                  breakpoint="desktop"
                  class="emotion-11"
                  href="/sweatpants"
                  target="_self"
                >
                  Sweatpants Sweatpants Sweatpants Sweatpants Sweatpants Sweatpants Sweatpants
                </a>
              </li>
            </ul>
          </div>
        </div>
        <div
          class="emotion-3"
          data-testid="ctaDropdownWrapper"
        >
          <button
            aria-expanded="false"
            class="emotion-4"
            color="dark"
          >
            <span
              class="emotion-5"
            >
              <span
                class="emotion-6"
                data-id="cta-dropdown-label"
              >
                Shop New
                <span
                  aria-hidden="true"
                  class="emotion-7"
                >
                  <svg
                    fill="none"
                    viewBox="0 0 12 12"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      clip-rule="evenodd"
                      d="M7 0H5v5H0v2h5v5h2V7h5V5H7V0z"
                      fill="#000000"
                      fill-rule="evenodd"
                    />
                  </svg>
                </span>
              </span>
            </span>
          </button>
          <div
            class="emotion-8"
          >
            <ul
              aria-hidden="true"
              class="emotion-9"
            >
              <li
                class="emotion-10"
              >
                <a
                  breakpoint="desktop"
                  class="emotion-11"
                  href="/Women"
                  target="_self"
                >
                  Women
                </a>
              </li>
              <li
                class="emotion-10"
              >
                <a
                  breakpoint="desktop"
                  class="emotion-11"
                  href="/Men"
                  target="_self"
                >
                  Men
                </a>
              </li>
              <li
                class="emotion-10"
              >
                <a
                  breakpoint="desktop"
                  class="emotion-11"
                  href="/Girls"
                  target="_self"
                >
                  Girls
                </a>
              </li>
              <li
                class="emotion-10"
              >
                <a
                  breakpoint="desktop"
                  class="emotion-11"
                  href="/Boys"
                  target="_self"
                >
                  Boys
                </a>
              </li>
              <li
                class="emotion-10"
              >
                <a
                  breakpoint="desktop"
                  class="emotion-11"
                  href="/Toddler Girls"
                  target="_self"
                >
                  Toddler Girls
                </a>
              </li>
              <li
                class="emotion-10"
              >
                <a
                  breakpoint="desktop"
                  class="emotion-11"
                  href="/Toddler Boys"
                  target="_self"
                >
                  Toddler Boys
                </a>
              </li>
              <li
                class="emotion-10"
              >
                <a
                  breakpoint="desktop"
                  class="emotion-11"
                  href="/Baby Girls"
                  target="_self"
                >
                  Baby Girls
                </a>
              </li>
              <li
                class="emotion-10"
              >
                <a
                  breakpoint="desktop"
                  class="emotion-11"
                  href="/Maternity"
                  target="_self"
                >
                  Maternity
                </a>
              </li>
              <li
                class="emotion-10"
              >
                <a
                  breakpoint="desktop"
                  class="emotion-11"
                  href="/Shop for the Fam Fam"
                  target="_self"
                >
                  Shop for the Fam Fam
                </a>
              </li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`<BodyOverlayContent /> with hero image data should match the snapshot for device: mobile with a content-justification of center 1`] = `
.emotion-0 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  width: 80.83333333333333%;
  padding: min(32px, 2.2222222222222223vw) min(147px, 10.208333333333334vw);
  gap: min(24px, 1.6666666666666667vw);
  pointer-events: none;
}

.emotion-1 {
  text-align: center;
}

.emotion-1 .amp-cms--p {
  padding: 0;
  margin: 0;
  line-height: 0;
}

.emotion-1 .amp-cms--p>span {
  display: inline;
  white-space: break-spaces;
}

.emotion-1 .amp-cms--p a {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  position: relative;
  z-index: 2;
  pointer-events: auto;
}

.emotion-1 .amp-cms--p a:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-1 .amp-cms--legal-copy {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(0.8333333333333334vw, 12px));
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-1 sup {
  vertical-align: top;
  display: inline-block;
  margin-top: -0.25ex;
}

.emotion-1 sub {
  vertical-align: bottom;
  display: inline-block;
  margin-bottom: -0.25ex;
}

.emotion-1 .amp-cms--f-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.8333333333333334vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--f-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.8333333333333334vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--f-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.8333333333333334vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--fn-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.8333333333333334vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--body-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(1.3888888888888888vw, 20px));
  line-height: 1.3;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--body-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(1.25vw, 18px));
  line-height: 1.2777777777777777;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--body-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(1.1111111111111112vw, 16px));
  line-height: 1.25;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--body-4 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(0.9722222222222222vw, 14px));
  line-height: 1.2857142857142858;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--body-5 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(0.8333333333333334vw, 12px));
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--eyebrow-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(35px, min(3.4722222222222223vw, 50px));
  line-height: 0.92;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--eyebrow-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(35px, min(2.7777777777777777vw, 40px));
  line-height: 1;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--eyebrow-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(35px, min(2.430555555555556vw, 35px));
  line-height: 1;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--headline-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(8.333333333333332vw, 120px));
  line-height: 0.8333333333333334;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--headline-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(7.638888888888889vw, 110px));
  line-height: 0.8636363636363636;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--headline-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(6.944444444444445vw, 100px));
  line-height: 0.85;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--headline-4 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(6.944444444444445vw, 100px));
  line-height: 1.05;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--headline-5 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(5.555555555555555vw, 80px));
  line-height: 0.9375;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--headline-6 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(4.861111111111112vw, 70px));
  line-height: 0.9285714285714286;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--headline-7 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(4.166666666666666vw, 60px));
  line-height: 0.9166666666666666;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--headlineAlt-1 {
  color: #2B2B2B;
  font-size: max(14px, min(7.916666666666666vw, 114px));
  line-height: 1;
  letter-spacing: min(-0.027777777777777776vw, -0.4px);
  font-weight: 400;
}

.emotion-1 .amp-cms--headlineAlt-2 {
  color: #2B2B2B;
  font-size: max(14px, min(5.833333333333333vw, 84px));
  line-height: 1;
  letter-spacing: min(-0.027777777777777776vw, -0.4px);
  font-weight: 400;
}

.emotion-1 .amp-cms--headlineAlt-3 {
  color: #2B2B2B;
  font-size: max(14px, min(4.444444444444445vw, 64px));
  line-height: 1;
  letter-spacing: min(-0.027777777777777776vw, -0.4px);
  font-weight: 400;
}

.emotion-1 .amp-cms--headlineAlt-4 {
  color: #2B2B2B;
  font-size: max(14px, min(2.361111111111111vw, 34px));
  line-height: 1;
  letter-spacing: min(-0.027777777777777776vw, -0.4px);
  font-weight: 400;
}

.emotion-1 .amp-cms--headlineAlt-5 {
  color: #2B2B2B;
  font-size: min(1.1111111111111112vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-1 .amp-cms--headlineAlt-6 {
  color: #2B2B2B;
  font-size: min(1.1111111111111112vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-1 .amp-cms--headlineAlt-7 {
  color: #2B2B2B;
  font-size: min(1.1111111111111112vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-1 .amp-cms--promo-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(6.944444444444445vw, 100px));
  line-height: 1;
  letter-spacing: min(0vw, 0px);
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-1 .amp-cms--promo-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(5.555555555555555vw, 80px));
  line-height: 1;
  letter-spacing: min(0vw, 0px);
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-1 .amp-cms--promoAlt-1 {
  color: #2B2B2B;
  font-size: min(1.1111111111111112vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 600;
}

.emotion-1 .amp-cms--subhead-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(2.083333333333333vw, 30px));
  line-height: 1;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--subhead-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(1.7361111111111112vw, 25px));
  line-height: 1;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--subhead-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(1.3888888888888888vw, 20px));
  line-height: 1.1;
  letter-spacing: 0;
}

.emotion-2 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  height: auto;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  gap: 60px;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

.emotion-3 {
  position: relative;
}

.emotion-4 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  display: inline-block;
  text-transform: none;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 1px solid transparent;
  border-radius: 0;
  max-width: 380px;
  font-size: 18px;
  font-weight: 500;
  letter-spacing: 2%;
  min-height: auto;
  max-height: auto;
  line-height: 18px;
  padding: 0;
  width: 100%;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: left;
  border-left: none;
  border-right: none;
  border-radius: 0;
  background-color: transparent;
  border-bottom: 0;
  -webkit-box-pack: left;
  -ms-flex-pack: left;
  -webkit-justify-content: left;
  justify-content: left;
  color: #000000;
  height: auto;
  -webkit-text-decoration: underline;
  text-decoration: underline;
  text-underline-offset: 5px;
  text-decoration-thickness: 2.3px;
  position: relative;
  z-index: 2;
  pointer-events: auto;
  text-align: left;
  line-height: 1;
  padding: 0;
  border: 0;
  text-underline-offset: 5px;
}

.emotion-4:focus {
  outline: none;
}

.emotion-4>span {
  padding: 1px 0;
}

.emotion-4:hover,
.emotion-4:focus {
  -webkit-text-decoration: unset;
  text-decoration: unset;
}

.emotion-4:active {
  -webkit-text-decoration: unset;
  text-decoration: unset;
}

.emotion-4>* {
  width: inherit;
  -webkit-text-decoration: inherit;
  text-decoration: inherit;
}

.emotion-4>span {
  padding: 0;
}

.emotion-5 {
  box-sizing: border-box;
}

.emotion-6 {
  display: inline-block;
  text-transform: none;
  position: relative;
  width: 100%;
  -webkit-text-decoration: inherit;
  text-decoration: inherit;
}

.emotion-7 {
  display: inline-block;
  height: 10px;
  width: 10px;
  min-height: 10px;
  min-width: 10px;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  position: absolute;
  right: 0;
  top: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  margin-left: 8px;
}

.emotion-7 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-7 svg path {
  fill: currentColor;
}

.emotion-7 svg rect {
  fill: currentColor;
}

.emotion-9 {
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
  background: #FFFFFF;
  box-sizing: border-box;
  list-style-type: none;
  min-width: 100%;
  overflow: hidden;
  position: relative;
  z-index: 390;
  border-width: 0;
  border-style: solid;
  text-align: left;
  letter-spacing: 0;
  pointer-events: auto;
  padding: 0px;
  position: absolute;
  max-height: 0;
  width: 100%;
  -webkit-transition: max-height .5s ease-in-out,visibility .5s;
  transition: max-height .5s ease-in-out,visibility .5s;
  visibility: hidden;
  margin-top: 12px;
  color: #000000;
  background-color: #FFFFFF;
}

.emotion-9 :hover {
  color: #FFFFFF;
  background-color: #000000;
}

.emotion-10 {
  box-sizing: border-box;
  width: 100%;
  border-bottom: none;
  letter-spacing: 1px;
  margin-left: 0;
  text-transform: none;
  padding: 12px;
  border-color: #000000;
}

.emotion-10:last-child {
  border: none;
}

.emotion-11 {
  cursor: pointer;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  padding: 0px;
  -webkit-text-decoration: none;
  text-decoration: none;
  -webkit-transition: color 0.3s ease;
  transition: color 0.3s ease;
  white-space: nowrap;
  font-size: 18px;
  text-align: center;
  color: inherit;
  -webkit-box-pack: start;
  -ms-flex-pack: start;
  -webkit-justify-content: start;
  justify-content: start;
  background-color: inherit;
  font-weight: 400;
  letter-spacing: 1px;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

<div>
  <div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      class="emotion-0"
    >
      <div
        class="emotion-1"
      >
        <div>
          <p
            class="amp-cms--p"
          >
            <span
              class="amp-cms--headline-3"
              style="color:#FFFFFF"
            >
              Large Headline Goes Here.
            </span>
          </p>
          <p
            class="amp-cms--p"
          >
            <span
              class="amp-cms--subhead-1"
              style="color:#FFFFFF"
            >
              Subheading goes here.
            </span>
          </p>
        </div>
      </div>
      <div
        class="emotion-2"
      >
        <div
          class="emotion-3"
          data-testid="ctaDropdownWrapper"
        >
          <button
            aria-expanded="false"
            class="emotion-4"
            color="dark"
          >
            <span
              class="emotion-5"
            >
              <span
                class="emotion-6"
                data-id="cta-dropdown-label"
              >
                Shop All
                <span
                  aria-hidden="true"
                  class="emotion-7"
                >
                  <svg
                    fill="none"
                    viewBox="0 0 12 12"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      clip-rule="evenodd"
                      d="M7 0H5v5H0v2h5v5h2V7h5V5H7V0z"
                      fill="#000000"
                      fill-rule="evenodd"
                    />
                  </svg>
                </span>
              </span>
            </span>
          </button>
          <div
            class="emotion-8"
          >
            <ul
              aria-hidden="true"
              class="emotion-9"
            >
              <li
                class="emotion-10"
              >
                <a
                  breakpoint="desktop"
                  class="emotion-11"
                  href="/Women"
                  target="_self"
                >
                  Women
                </a>
              </li>
              <li
                class="emotion-10"
              >
                <a
                  breakpoint="desktop"
                  class="emotion-11"
                  href="/Men"
                  target="_self"
                >
                  Men
                </a>
              </li>
              <li
                class="emotion-10"
              >
                <a
                  breakpoint="desktop"
                  class="emotion-11"
                  href="/Girls"
                  target="_self"
                >
                  Girls
                </a>
              </li>
              <li
                class="emotion-10"
              >
                <a
                  breakpoint="desktop"
                  class="emotion-11"
                  href="/Boys"
                  target="_self"
                >
                  Boys
                </a>
              </li>
              <li
                class="emotion-10"
              >
                <a
                  breakpoint="desktop"
                  class="emotion-11"
                  href="/Toddler Girls"
                  target="_self"
                >
                  Toddler Girls
                </a>
              </li>
              <li
                class="emotion-10"
              >
                <a
                  breakpoint="desktop"
                  class="emotion-11"
                  href="/Toddler Boys"
                  target="_self"
                >
                  Toddler Boys
                </a>
              </li>
              <li
                class="emotion-10"
              >
                <a
                  breakpoint="desktop"
                  class="emotion-11"
                  href="/Baby Girls"
                  target="_self"
                >
                  Baby Girls
                </a>
              </li>
              <li
                class="emotion-10"
              >
                <a
                  breakpoint="desktop"
                  class="emotion-11"
                  href="/Baby Boys"
                  target="_self"
                >
                  Baby Boys
                </a>
              </li>
            </ul>
          </div>
        </div>
        <div
          class="emotion-3"
          data-testid="ctaDropdownWrapper"
        >
          <button
            aria-expanded="false"
            class="emotion-4"
            color="dark"
          >
            <span
              class="emotion-5"
            >
              <span
                class="emotion-6"
                data-id="cta-dropdown-label"
              >
                Shop Pants
                <span
                  aria-hidden="true"
                  class="emotion-7"
                >
                  <svg
                    fill="none"
                    viewBox="0 0 12 12"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      clip-rule="evenodd"
                      d="M7 0H5v5H0v2h5v5h2V7h5V5H7V0z"
                      fill="#000000"
                      fill-rule="evenodd"
                    />
                  </svg>
                </span>
              </span>
            </span>
          </button>
          <div
            class="emotion-8"
          >
            <ul
              aria-hidden="true"
              class="emotion-9"
            >
              <li
                class="emotion-10"
              >
                <a
                  breakpoint="desktop"
                  class="emotion-11"
                  href="/jeans"
                  target="_self"
                >
                  Jeans
                </a>
              </li>
              <li
                class="emotion-10"
              >
                <a
                  breakpoint="desktop"
                  class="emotion-11"
                  href="/khakis"
                  target="_self"
                >
                  Khakis
                </a>
              </li>
              <li
                class="emotion-10"
              >
                <a
                  breakpoint="desktop"
                  class="emotion-11"
                  href="/sweatpants"
                  target="_self"
                >
                  Sweatpants Sweatpants Sweatpants Sweatpants Sweatpants Sweatpants Sweatpants
                </a>
              </li>
            </ul>
          </div>
        </div>
        <div
          class="emotion-3"
          data-testid="ctaDropdownWrapper"
        >
          <button
            aria-expanded="false"
            class="emotion-4"
            color="dark"
          >
            <span
              class="emotion-5"
            >
              <span
                class="emotion-6"
                data-id="cta-dropdown-label"
              >
                Shop New
                <span
                  aria-hidden="true"
                  class="emotion-7"
                >
                  <svg
                    fill="none"
                    viewBox="0 0 12 12"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      clip-rule="evenodd"
                      d="M7 0H5v5H0v2h5v5h2V7h5V5H7V0z"
                      fill="#000000"
                      fill-rule="evenodd"
                    />
                  </svg>
                </span>
              </span>
            </span>
          </button>
          <div
            class="emotion-8"
          >
            <ul
              aria-hidden="true"
              class="emotion-9"
            >
              <li
                class="emotion-10"
              >
                <a
                  breakpoint="desktop"
                  class="emotion-11"
                  href="/Women"
                  target="_self"
                >
                  Women
                </a>
              </li>
              <li
                class="emotion-10"
              >
                <a
                  breakpoint="desktop"
                  class="emotion-11"
                  href="/Men"
                  target="_self"
                >
                  Men
                </a>
              </li>
              <li
                class="emotion-10"
              >
                <a
                  breakpoint="desktop"
                  class="emotion-11"
                  href="/Girls"
                  target="_self"
                >
                  Girls
                </a>
              </li>
              <li
                class="emotion-10"
              >
                <a
                  breakpoint="desktop"
                  class="emotion-11"
                  href="/Boys"
                  target="_self"
                >
                  Boys
                </a>
              </li>
              <li
                class="emotion-10"
              >
                <a
                  breakpoint="desktop"
                  class="emotion-11"
                  href="/Toddler Girls"
                  target="_self"
                >
                  Toddler Girls
                </a>
              </li>
              <li
                class="emotion-10"
              >
                <a
                  breakpoint="desktop"
                  class="emotion-11"
                  href="/Toddler Boys"
                  target="_self"
                >
                  Toddler Boys
                </a>
              </li>
              <li
                class="emotion-10"
              >
                <a
                  breakpoint="desktop"
                  class="emotion-11"
                  href="/Baby Girls"
                  target="_self"
                >
                  Baby Girls
                </a>
              </li>
              <li
                class="emotion-10"
              >
                <a
                  breakpoint="desktop"
                  class="emotion-11"
                  href="/Maternity"
                  target="_self"
                >
                  Maternity
                </a>
              </li>
              <li
                class="emotion-10"
              >
                <a
                  breakpoint="desktop"
                  class="emotion-11"
                  href="/Shop for the Fam Fam"
                  target="_self"
                >
                  Shop for the Fam Fam
                </a>
              </li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`<BodyOverlayContent /> with hero image data should match the snapshot for device: mobile with a content-justification of left 1`] = `
.emotion-0 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  width: 80.83333333333333%;
  padding: min(32px, 2.2222222222222223vw) min(147px, 10.208333333333334vw);
  gap: min(24px, 1.6666666666666667vw);
  pointer-events: none;
}

.emotion-1 {
  text-align: center;
}

.emotion-1 .amp-cms--p {
  padding: 0;
  margin: 0;
  line-height: 0;
}

.emotion-1 .amp-cms--p>span {
  display: inline;
  white-space: break-spaces;
}

.emotion-1 .amp-cms--p a {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  position: relative;
  z-index: 2;
  pointer-events: auto;
}

.emotion-1 .amp-cms--p a:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-1 .amp-cms--legal-copy {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(0.8333333333333334vw, 12px));
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-1 sup {
  vertical-align: top;
  display: inline-block;
  margin-top: -0.25ex;
}

.emotion-1 sub {
  vertical-align: bottom;
  display: inline-block;
  margin-bottom: -0.25ex;
}

.emotion-1 .amp-cms--f-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.8333333333333334vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--f-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.8333333333333334vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--f-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.8333333333333334vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--fn-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.8333333333333334vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--body-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(1.3888888888888888vw, 20px));
  line-height: 1.3;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--body-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(1.25vw, 18px));
  line-height: 1.2777777777777777;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--body-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(1.1111111111111112vw, 16px));
  line-height: 1.25;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--body-4 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(0.9722222222222222vw, 14px));
  line-height: 1.2857142857142858;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--body-5 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(0.8333333333333334vw, 12px));
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--eyebrow-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(35px, min(3.4722222222222223vw, 50px));
  line-height: 0.92;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--eyebrow-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(35px, min(2.7777777777777777vw, 40px));
  line-height: 1;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--eyebrow-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(35px, min(2.430555555555556vw, 35px));
  line-height: 1;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--headline-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(8.333333333333332vw, 120px));
  line-height: 0.8333333333333334;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--headline-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(7.638888888888889vw, 110px));
  line-height: 0.8636363636363636;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--headline-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(6.944444444444445vw, 100px));
  line-height: 0.85;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--headline-4 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(6.944444444444445vw, 100px));
  line-height: 1.05;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--headline-5 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(5.555555555555555vw, 80px));
  line-height: 0.9375;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--headline-6 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(4.861111111111112vw, 70px));
  line-height: 0.9285714285714286;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--headline-7 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(4.166666666666666vw, 60px));
  line-height: 0.9166666666666666;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--headlineAlt-1 {
  color: #2B2B2B;
  font-size: max(14px, min(7.916666666666666vw, 114px));
  line-height: 1;
  letter-spacing: min(-0.027777777777777776vw, -0.4px);
  font-weight: 400;
}

.emotion-1 .amp-cms--headlineAlt-2 {
  color: #2B2B2B;
  font-size: max(14px, min(5.833333333333333vw, 84px));
  line-height: 1;
  letter-spacing: min(-0.027777777777777776vw, -0.4px);
  font-weight: 400;
}

.emotion-1 .amp-cms--headlineAlt-3 {
  color: #2B2B2B;
  font-size: max(14px, min(4.444444444444445vw, 64px));
  line-height: 1;
  letter-spacing: min(-0.027777777777777776vw, -0.4px);
  font-weight: 400;
}

.emotion-1 .amp-cms--headlineAlt-4 {
  color: #2B2B2B;
  font-size: max(14px, min(2.361111111111111vw, 34px));
  line-height: 1;
  letter-spacing: min(-0.027777777777777776vw, -0.4px);
  font-weight: 400;
}

.emotion-1 .amp-cms--headlineAlt-5 {
  color: #2B2B2B;
  font-size: min(1.1111111111111112vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-1 .amp-cms--headlineAlt-6 {
  color: #2B2B2B;
  font-size: min(1.1111111111111112vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-1 .amp-cms--headlineAlt-7 {
  color: #2B2B2B;
  font-size: min(1.1111111111111112vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-1 .amp-cms--promo-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(6.944444444444445vw, 100px));
  line-height: 1;
  letter-spacing: min(0vw, 0px);
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-1 .amp-cms--promo-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(5.555555555555555vw, 80px));
  line-height: 1;
  letter-spacing: min(0vw, 0px);
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-1 .amp-cms--promoAlt-1 {
  color: #2B2B2B;
  font-size: min(1.1111111111111112vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 600;
}

.emotion-1 .amp-cms--subhead-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(2.083333333333333vw, 30px));
  line-height: 1;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--subhead-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(1.7361111111111112vw, 25px));
  line-height: 1;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--subhead-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(1.3888888888888888vw, 20px));
  line-height: 1.1;
  letter-spacing: 0;
}

.emotion-2 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  height: auto;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  gap: 60px;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

.emotion-3 {
  position: relative;
}

.emotion-4 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  display: inline-block;
  text-transform: none;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 1px solid transparent;
  border-radius: 0;
  max-width: 380px;
  font-size: 18px;
  font-weight: 500;
  letter-spacing: 2%;
  min-height: auto;
  max-height: auto;
  line-height: 18px;
  padding: 0;
  width: 100%;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: left;
  border-left: none;
  border-right: none;
  border-radius: 0;
  background-color: transparent;
  border-bottom: 0;
  -webkit-box-pack: left;
  -ms-flex-pack: left;
  -webkit-justify-content: left;
  justify-content: left;
  color: #000000;
  height: auto;
  -webkit-text-decoration: underline;
  text-decoration: underline;
  text-underline-offset: 5px;
  text-decoration-thickness: 2.3px;
  position: relative;
  z-index: 2;
  pointer-events: auto;
  text-align: left;
  line-height: 1;
  padding: 0;
  border: 0;
  text-underline-offset: 5px;
}

.emotion-4:focus {
  outline: none;
}

.emotion-4>span {
  padding: 1px 0;
}

.emotion-4:hover,
.emotion-4:focus {
  -webkit-text-decoration: unset;
  text-decoration: unset;
}

.emotion-4:active {
  -webkit-text-decoration: unset;
  text-decoration: unset;
}

.emotion-4>* {
  width: inherit;
  -webkit-text-decoration: inherit;
  text-decoration: inherit;
}

.emotion-4>span {
  padding: 0;
}

.emotion-5 {
  box-sizing: border-box;
}

.emotion-6 {
  display: inline-block;
  text-transform: none;
  position: relative;
  width: 100%;
  -webkit-text-decoration: inherit;
  text-decoration: inherit;
}

.emotion-7 {
  display: inline-block;
  height: 10px;
  width: 10px;
  min-height: 10px;
  min-width: 10px;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  position: absolute;
  right: 0;
  top: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  margin-left: 8px;
}

.emotion-7 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-7 svg path {
  fill: currentColor;
}

.emotion-7 svg rect {
  fill: currentColor;
}

.emotion-9 {
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
  background: #FFFFFF;
  box-sizing: border-box;
  list-style-type: none;
  min-width: 100%;
  overflow: hidden;
  position: relative;
  z-index: 390;
  border-width: 0;
  border-style: solid;
  text-align: left;
  letter-spacing: 0;
  pointer-events: auto;
  padding: 0px;
  position: absolute;
  max-height: 0;
  width: 100%;
  -webkit-transition: max-height .5s ease-in-out,visibility .5s;
  transition: max-height .5s ease-in-out,visibility .5s;
  visibility: hidden;
  margin-top: 12px;
  color: #000000;
  background-color: #FFFFFF;
}

.emotion-9 :hover {
  color: #FFFFFF;
  background-color: #000000;
}

.emotion-10 {
  box-sizing: border-box;
  width: 100%;
  border-bottom: none;
  letter-spacing: 1px;
  margin-left: 0;
  text-transform: none;
  padding: 12px;
  border-color: #000000;
}

.emotion-10:last-child {
  border: none;
}

.emotion-11 {
  cursor: pointer;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  padding: 0px;
  -webkit-text-decoration: none;
  text-decoration: none;
  -webkit-transition: color 0.3s ease;
  transition: color 0.3s ease;
  white-space: nowrap;
  font-size: 18px;
  text-align: center;
  color: inherit;
  -webkit-box-pack: start;
  -ms-flex-pack: start;
  -webkit-justify-content: start;
  justify-content: start;
  background-color: inherit;
  font-weight: 400;
  letter-spacing: 1px;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

<div>
  <div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      class="emotion-0"
    >
      <div
        class="emotion-1"
      >
        <div>
          <p
            class="amp-cms--p"
          >
            <span
              class="amp-cms--headline-3"
              style="color:#FFFFFF"
            >
              Large Headline Goes Here.
            </span>
          </p>
          <p
            class="amp-cms--p"
          >
            <span
              class="amp-cms--subhead-1"
              style="color:#FFFFFF"
            >
              Subheading goes here.
            </span>
          </p>
        </div>
      </div>
      <div
        class="emotion-2"
      >
        <div
          class="emotion-3"
          data-testid="ctaDropdownWrapper"
        >
          <button
            aria-expanded="false"
            class="emotion-4"
            color="dark"
          >
            <span
              class="emotion-5"
            >
              <span
                class="emotion-6"
                data-id="cta-dropdown-label"
              >
                Shop All
                <span
                  aria-hidden="true"
                  class="emotion-7"
                >
                  <svg
                    fill="none"
                    viewBox="0 0 12 12"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      clip-rule="evenodd"
                      d="M7 0H5v5H0v2h5v5h2V7h5V5H7V0z"
                      fill="#000000"
                      fill-rule="evenodd"
                    />
                  </svg>
                </span>
              </span>
            </span>
          </button>
          <div
            class="emotion-8"
          >
            <ul
              aria-hidden="true"
              class="emotion-9"
            >
              <li
                class="emotion-10"
              >
                <a
                  breakpoint="desktop"
                  class="emotion-11"
                  href="/Women"
                  target="_self"
                >
                  Women
                </a>
              </li>
              <li
                class="emotion-10"
              >
                <a
                  breakpoint="desktop"
                  class="emotion-11"
                  href="/Men"
                  target="_self"
                >
                  Men
                </a>
              </li>
              <li
                class="emotion-10"
              >
                <a
                  breakpoint="desktop"
                  class="emotion-11"
                  href="/Girls"
                  target="_self"
                >
                  Girls
                </a>
              </li>
              <li
                class="emotion-10"
              >
                <a
                  breakpoint="desktop"
                  class="emotion-11"
                  href="/Boys"
                  target="_self"
                >
                  Boys
                </a>
              </li>
              <li
                class="emotion-10"
              >
                <a
                  breakpoint="desktop"
                  class="emotion-11"
                  href="/Toddler Girls"
                  target="_self"
                >
                  Toddler Girls
                </a>
              </li>
              <li
                class="emotion-10"
              >
                <a
                  breakpoint="desktop"
                  class="emotion-11"
                  href="/Toddler Boys"
                  target="_self"
                >
                  Toddler Boys
                </a>
              </li>
              <li
                class="emotion-10"
              >
                <a
                  breakpoint="desktop"
                  class="emotion-11"
                  href="/Baby Girls"
                  target="_self"
                >
                  Baby Girls
                </a>
              </li>
              <li
                class="emotion-10"
              >
                <a
                  breakpoint="desktop"
                  class="emotion-11"
                  href="/Baby Boys"
                  target="_self"
                >
                  Baby Boys
                </a>
              </li>
            </ul>
          </div>
        </div>
        <div
          class="emotion-3"
          data-testid="ctaDropdownWrapper"
        >
          <button
            aria-expanded="false"
            class="emotion-4"
            color="dark"
          >
            <span
              class="emotion-5"
            >
              <span
                class="emotion-6"
                data-id="cta-dropdown-label"
              >
                Shop Pants
                <span
                  aria-hidden="true"
                  class="emotion-7"
                >
                  <svg
                    fill="none"
                    viewBox="0 0 12 12"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      clip-rule="evenodd"
                      d="M7 0H5v5H0v2h5v5h2V7h5V5H7V0z"
                      fill="#000000"
                      fill-rule="evenodd"
                    />
                  </svg>
                </span>
              </span>
            </span>
          </button>
          <div
            class="emotion-8"
          >
            <ul
              aria-hidden="true"
              class="emotion-9"
            >
              <li
                class="emotion-10"
              >
                <a
                  breakpoint="desktop"
                  class="emotion-11"
                  href="/jeans"
                  target="_self"
                >
                  Jeans
                </a>
              </li>
              <li
                class="emotion-10"
              >
                <a
                  breakpoint="desktop"
                  class="emotion-11"
                  href="/khakis"
                  target="_self"
                >
                  Khakis
                </a>
              </li>
              <li
                class="emotion-10"
              >
                <a
                  breakpoint="desktop"
                  class="emotion-11"
                  href="/sweatpants"
                  target="_self"
                >
                  Sweatpants Sweatpants Sweatpants Sweatpants Sweatpants Sweatpants Sweatpants
                </a>
              </li>
            </ul>
          </div>
        </div>
        <div
          class="emotion-3"
          data-testid="ctaDropdownWrapper"
        >
          <button
            aria-expanded="false"
            class="emotion-4"
            color="dark"
          >
            <span
              class="emotion-5"
            >
              <span
                class="emotion-6"
                data-id="cta-dropdown-label"
              >
                Shop New
                <span
                  aria-hidden="true"
                  class="emotion-7"
                >
                  <svg
                    fill="none"
                    viewBox="0 0 12 12"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      clip-rule="evenodd"
                      d="M7 0H5v5H0v2h5v5h2V7h5V5H7V0z"
                      fill="#000000"
                      fill-rule="evenodd"
                    />
                  </svg>
                </span>
              </span>
            </span>
          </button>
          <div
            class="emotion-8"
          >
            <ul
              aria-hidden="true"
              class="emotion-9"
            >
              <li
                class="emotion-10"
              >
                <a
                  breakpoint="desktop"
                  class="emotion-11"
                  href="/Women"
                  target="_self"
                >
                  Women
                </a>
              </li>
              <li
                class="emotion-10"
              >
                <a
                  breakpoint="desktop"
                  class="emotion-11"
                  href="/Men"
                  target="_self"
                >
                  Men
                </a>
              </li>
              <li
                class="emotion-10"
              >
                <a
                  breakpoint="desktop"
                  class="emotion-11"
                  href="/Girls"
                  target="_self"
                >
                  Girls
                </a>
              </li>
              <li
                class="emotion-10"
              >
                <a
                  breakpoint="desktop"
                  class="emotion-11"
                  href="/Boys"
                  target="_self"
                >
                  Boys
                </a>
              </li>
              <li
                class="emotion-10"
              >
                <a
                  breakpoint="desktop"
                  class="emotion-11"
                  href="/Toddler Girls"
                  target="_self"
                >
                  Toddler Girls
                </a>
              </li>
              <li
                class="emotion-10"
              >
                <a
                  breakpoint="desktop"
                  class="emotion-11"
                  href="/Toddler Boys"
                  target="_self"
                >
                  Toddler Boys
                </a>
              </li>
              <li
                class="emotion-10"
              >
                <a
                  breakpoint="desktop"
                  class="emotion-11"
                  href="/Baby Girls"
                  target="_self"
                >
                  Baby Girls
                </a>
              </li>
              <li
                class="emotion-10"
              >
                <a
                  breakpoint="desktop"
                  class="emotion-11"
                  href="/Maternity"
                  target="_self"
                >
                  Maternity
                </a>
              </li>
              <li
                class="emotion-10"
              >
                <a
                  breakpoint="desktop"
                  class="emotion-11"
                  href="/Shop for the Fam Fam"
                  target="_self"
                >
                  Shop for the Fam Fam
                </a>
              </li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`<BodyOverlayContent /> with hero image data should match the snapshot for device: mobile with a content-justification of right 1`] = `
.emotion-0 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  width: 80.83333333333333%;
  padding: min(32px, 2.2222222222222223vw) min(147px, 10.208333333333334vw);
  gap: min(24px, 1.6666666666666667vw);
  pointer-events: none;
}

.emotion-1 {
  text-align: center;
}

.emotion-1 .amp-cms--p {
  padding: 0;
  margin: 0;
  line-height: 0;
}

.emotion-1 .amp-cms--p>span {
  display: inline;
  white-space: break-spaces;
}

.emotion-1 .amp-cms--p a {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  position: relative;
  z-index: 2;
  pointer-events: auto;
}

.emotion-1 .amp-cms--p a:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-1 .amp-cms--legal-copy {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(0.8333333333333334vw, 12px));
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-1 sup {
  vertical-align: top;
  display: inline-block;
  margin-top: -0.25ex;
}

.emotion-1 sub {
  vertical-align: bottom;
  display: inline-block;
  margin-bottom: -0.25ex;
}

.emotion-1 .amp-cms--f-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.8333333333333334vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--f-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.8333333333333334vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--f-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.8333333333333334vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--fn-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.8333333333333334vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--body-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(1.3888888888888888vw, 20px));
  line-height: 1.3;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--body-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(1.25vw, 18px));
  line-height: 1.2777777777777777;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--body-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(1.1111111111111112vw, 16px));
  line-height: 1.25;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--body-4 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(0.9722222222222222vw, 14px));
  line-height: 1.2857142857142858;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--body-5 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(0.8333333333333334vw, 12px));
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--eyebrow-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(35px, min(3.4722222222222223vw, 50px));
  line-height: 0.92;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--eyebrow-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(35px, min(2.7777777777777777vw, 40px));
  line-height: 1;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--eyebrow-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(35px, min(2.430555555555556vw, 35px));
  line-height: 1;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--headline-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(8.333333333333332vw, 120px));
  line-height: 0.8333333333333334;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--headline-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(7.638888888888889vw, 110px));
  line-height: 0.8636363636363636;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--headline-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(6.944444444444445vw, 100px));
  line-height: 0.85;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--headline-4 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(6.944444444444445vw, 100px));
  line-height: 1.05;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--headline-5 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(5.555555555555555vw, 80px));
  line-height: 0.9375;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--headline-6 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(4.861111111111112vw, 70px));
  line-height: 0.9285714285714286;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--headline-7 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(4.166666666666666vw, 60px));
  line-height: 0.9166666666666666;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--headlineAlt-1 {
  color: #2B2B2B;
  font-size: max(14px, min(7.916666666666666vw, 114px));
  line-height: 1;
  letter-spacing: min(-0.027777777777777776vw, -0.4px);
  font-weight: 400;
}

.emotion-1 .amp-cms--headlineAlt-2 {
  color: #2B2B2B;
  font-size: max(14px, min(5.833333333333333vw, 84px));
  line-height: 1;
  letter-spacing: min(-0.027777777777777776vw, -0.4px);
  font-weight: 400;
}

.emotion-1 .amp-cms--headlineAlt-3 {
  color: #2B2B2B;
  font-size: max(14px, min(4.444444444444445vw, 64px));
  line-height: 1;
  letter-spacing: min(-0.027777777777777776vw, -0.4px);
  font-weight: 400;
}

.emotion-1 .amp-cms--headlineAlt-4 {
  color: #2B2B2B;
  font-size: max(14px, min(2.361111111111111vw, 34px));
  line-height: 1;
  letter-spacing: min(-0.027777777777777776vw, -0.4px);
  font-weight: 400;
}

.emotion-1 .amp-cms--headlineAlt-5 {
  color: #2B2B2B;
  font-size: min(1.1111111111111112vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-1 .amp-cms--headlineAlt-6 {
  color: #2B2B2B;
  font-size: min(1.1111111111111112vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-1 .amp-cms--headlineAlt-7 {
  color: #2B2B2B;
  font-size: min(1.1111111111111112vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-1 .amp-cms--promo-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(6.944444444444445vw, 100px));
  line-height: 1;
  letter-spacing: min(0vw, 0px);
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-1 .amp-cms--promo-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(5.555555555555555vw, 80px));
  line-height: 1;
  letter-spacing: min(0vw, 0px);
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-1 .amp-cms--promoAlt-1 {
  color: #2B2B2B;
  font-size: min(1.1111111111111112vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 600;
}

.emotion-1 .amp-cms--subhead-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(2.083333333333333vw, 30px));
  line-height: 1;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--subhead-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(1.7361111111111112vw, 25px));
  line-height: 1;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--subhead-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(1.3888888888888888vw, 20px));
  line-height: 1.1;
  letter-spacing: 0;
}

.emotion-2 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  height: auto;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  gap: 60px;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

.emotion-3 {
  position: relative;
}

.emotion-4 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  display: inline-block;
  text-transform: none;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 1px solid transparent;
  border-radius: 0;
  max-width: 380px;
  font-size: 18px;
  font-weight: 500;
  letter-spacing: 2%;
  min-height: auto;
  max-height: auto;
  line-height: 18px;
  padding: 0;
  width: 100%;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: left;
  border-left: none;
  border-right: none;
  border-radius: 0;
  background-color: transparent;
  border-bottom: 0;
  -webkit-box-pack: left;
  -ms-flex-pack: left;
  -webkit-justify-content: left;
  justify-content: left;
  color: #000000;
  height: auto;
  -webkit-text-decoration: underline;
  text-decoration: underline;
  text-underline-offset: 5px;
  text-decoration-thickness: 2.3px;
  position: relative;
  z-index: 2;
  pointer-events: auto;
  text-align: left;
  line-height: 1;
  padding: 0;
  border: 0;
  text-underline-offset: 5px;
}

.emotion-4:focus {
  outline: none;
}

.emotion-4>span {
  padding: 1px 0;
}

.emotion-4:hover,
.emotion-4:focus {
  -webkit-text-decoration: unset;
  text-decoration: unset;
}

.emotion-4:active {
  -webkit-text-decoration: unset;
  text-decoration: unset;
}

.emotion-4>* {
  width: inherit;
  -webkit-text-decoration: inherit;
  text-decoration: inherit;
}

.emotion-4>span {
  padding: 0;
}

.emotion-5 {
  box-sizing: border-box;
}

.emotion-6 {
  display: inline-block;
  text-transform: none;
  position: relative;
  width: 100%;
  -webkit-text-decoration: inherit;
  text-decoration: inherit;
}

.emotion-7 {
  display: inline-block;
  height: 10px;
  width: 10px;
  min-height: 10px;
  min-width: 10px;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  position: absolute;
  right: 0;
  top: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  margin-left: 8px;
}

.emotion-7 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-7 svg path {
  fill: currentColor;
}

.emotion-7 svg rect {
  fill: currentColor;
}

.emotion-9 {
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
  background: #FFFFFF;
  box-sizing: border-box;
  list-style-type: none;
  min-width: 100%;
  overflow: hidden;
  position: relative;
  z-index: 390;
  border-width: 0;
  border-style: solid;
  text-align: left;
  letter-spacing: 0;
  pointer-events: auto;
  padding: 0px;
  position: absolute;
  max-height: 0;
  width: 100%;
  -webkit-transition: max-height .5s ease-in-out,visibility .5s;
  transition: max-height .5s ease-in-out,visibility .5s;
  visibility: hidden;
  margin-top: 12px;
  color: #000000;
  background-color: #FFFFFF;
}

.emotion-9 :hover {
  color: #FFFFFF;
  background-color: #000000;
}

.emotion-10 {
  box-sizing: border-box;
  width: 100%;
  border-bottom: none;
  letter-spacing: 1px;
  margin-left: 0;
  text-transform: none;
  padding: 12px;
  border-color: #000000;
}

.emotion-10:last-child {
  border: none;
}

.emotion-11 {
  cursor: pointer;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  padding: 0px;
  -webkit-text-decoration: none;
  text-decoration: none;
  -webkit-transition: color 0.3s ease;
  transition: color 0.3s ease;
  white-space: nowrap;
  font-size: 18px;
  text-align: center;
  color: inherit;
  -webkit-box-pack: start;
  -ms-flex-pack: start;
  -webkit-justify-content: start;
  justify-content: start;
  background-color: inherit;
  font-weight: 400;
  letter-spacing: 1px;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

<div>
  <div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      class="emotion-0"
    >
      <div
        class="emotion-1"
      >
        <div>
          <p
            class="amp-cms--p"
          >
            <span
              class="amp-cms--headline-3"
              style="color:#FFFFFF"
            >
              Large Headline Goes Here.
            </span>
          </p>
          <p
            class="amp-cms--p"
          >
            <span
              class="amp-cms--subhead-1"
              style="color:#FFFFFF"
            >
              Subheading goes here.
            </span>
          </p>
        </div>
      </div>
      <div
        class="emotion-2"
      >
        <div
          class="emotion-3"
          data-testid="ctaDropdownWrapper"
        >
          <button
            aria-expanded="false"
            class="emotion-4"
            color="dark"
          >
            <span
              class="emotion-5"
            >
              <span
                class="emotion-6"
                data-id="cta-dropdown-label"
              >
                Shop All
                <span
                  aria-hidden="true"
                  class="emotion-7"
                >
                  <svg
                    fill="none"
                    viewBox="0 0 12 12"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      clip-rule="evenodd"
                      d="M7 0H5v5H0v2h5v5h2V7h5V5H7V0z"
                      fill="#000000"
                      fill-rule="evenodd"
                    />
                  </svg>
                </span>
              </span>
            </span>
          </button>
          <div
            class="emotion-8"
          >
            <ul
              aria-hidden="true"
              class="emotion-9"
            >
              <li
                class="emotion-10"
              >
                <a
                  breakpoint="desktop"
                  class="emotion-11"
                  href="/Women"
                  target="_self"
                >
                  Women
                </a>
              </li>
              <li
                class="emotion-10"
              >
                <a
                  breakpoint="desktop"
                  class="emotion-11"
                  href="/Men"
                  target="_self"
                >
                  Men
                </a>
              </li>
              <li
                class="emotion-10"
              >
                <a
                  breakpoint="desktop"
                  class="emotion-11"
                  href="/Girls"
                  target="_self"
                >
                  Girls
                </a>
              </li>
              <li
                class="emotion-10"
              >
                <a
                  breakpoint="desktop"
                  class="emotion-11"
                  href="/Boys"
                  target="_self"
                >
                  Boys
                </a>
              </li>
              <li
                class="emotion-10"
              >
                <a
                  breakpoint="desktop"
                  class="emotion-11"
                  href="/Toddler Girls"
                  target="_self"
                >
                  Toddler Girls
                </a>
              </li>
              <li
                class="emotion-10"
              >
                <a
                  breakpoint="desktop"
                  class="emotion-11"
                  href="/Toddler Boys"
                  target="_self"
                >
                  Toddler Boys
                </a>
              </li>
              <li
                class="emotion-10"
              >
                <a
                  breakpoint="desktop"
                  class="emotion-11"
                  href="/Baby Girls"
                  target="_self"
                >
                  Baby Girls
                </a>
              </li>
              <li
                class="emotion-10"
              >
                <a
                  breakpoint="desktop"
                  class="emotion-11"
                  href="/Baby Boys"
                  target="_self"
                >
                  Baby Boys
                </a>
              </li>
            </ul>
          </div>
        </div>
        <div
          class="emotion-3"
          data-testid="ctaDropdownWrapper"
        >
          <button
            aria-expanded="false"
            class="emotion-4"
            color="dark"
          >
            <span
              class="emotion-5"
            >
              <span
                class="emotion-6"
                data-id="cta-dropdown-label"
              >
                Shop Pants
                <span
                  aria-hidden="true"
                  class="emotion-7"
                >
                  <svg
                    fill="none"
                    viewBox="0 0 12 12"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      clip-rule="evenodd"
                      d="M7 0H5v5H0v2h5v5h2V7h5V5H7V0z"
                      fill="#000000"
                      fill-rule="evenodd"
                    />
                  </svg>
                </span>
              </span>
            </span>
          </button>
          <div
            class="emotion-8"
          >
            <ul
              aria-hidden="true"
              class="emotion-9"
            >
              <li
                class="emotion-10"
              >
                <a
                  breakpoint="desktop"
                  class="emotion-11"
                  href="/jeans"
                  target="_self"
                >
                  Jeans
                </a>
              </li>
              <li
                class="emotion-10"
              >
                <a
                  breakpoint="desktop"
                  class="emotion-11"
                  href="/khakis"
                  target="_self"
                >
                  Khakis
                </a>
              </li>
              <li
                class="emotion-10"
              >
                <a
                  breakpoint="desktop"
                  class="emotion-11"
                  href="/sweatpants"
                  target="_self"
                >
                  Sweatpants Sweatpants Sweatpants Sweatpants Sweatpants Sweatpants Sweatpants
                </a>
              </li>
            </ul>
          </div>
        </div>
        <div
          class="emotion-3"
          data-testid="ctaDropdownWrapper"
        >
          <button
            aria-expanded="false"
            class="emotion-4"
            color="dark"
          >
            <span
              class="emotion-5"
            >
              <span
                class="emotion-6"
                data-id="cta-dropdown-label"
              >
                Shop New
                <span
                  aria-hidden="true"
                  class="emotion-7"
                >
                  <svg
                    fill="none"
                    viewBox="0 0 12 12"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      clip-rule="evenodd"
                      d="M7 0H5v5H0v2h5v5h2V7h5V5H7V0z"
                      fill="#000000"
                      fill-rule="evenodd"
                    />
                  </svg>
                </span>
              </span>
            </span>
          </button>
          <div
            class="emotion-8"
          >
            <ul
              aria-hidden="true"
              class="emotion-9"
            >
              <li
                class="emotion-10"
              >
                <a
                  breakpoint="desktop"
                  class="emotion-11"
                  href="/Women"
                  target="_self"
                >
                  Women
                </a>
              </li>
              <li
                class="emotion-10"
              >
                <a
                  breakpoint="desktop"
                  class="emotion-11"
                  href="/Men"
                  target="_self"
                >
                  Men
                </a>
              </li>
              <li
                class="emotion-10"
              >
                <a
                  breakpoint="desktop"
                  class="emotion-11"
                  href="/Girls"
                  target="_self"
                >
                  Girls
                </a>
              </li>
              <li
                class="emotion-10"
              >
                <a
                  breakpoint="desktop"
                  class="emotion-11"
                  href="/Boys"
                  target="_self"
                >
                  Boys
                </a>
              </li>
              <li
                class="emotion-10"
              >
                <a
                  breakpoint="desktop"
                  class="emotion-11"
                  href="/Toddler Girls"
                  target="_self"
                >
                  Toddler Girls
                </a>
              </li>
              <li
                class="emotion-10"
              >
                <a
                  breakpoint="desktop"
                  class="emotion-11"
                  href="/Toddler Boys"
                  target="_self"
                >
                  Toddler Boys
                </a>
              </li>
              <li
                class="emotion-10"
              >
                <a
                  breakpoint="desktop"
                  class="emotion-11"
                  href="/Baby Girls"
                  target="_self"
                >
                  Baby Girls
                </a>
              </li>
              <li
                class="emotion-10"
              >
                <a
                  breakpoint="desktop"
                  class="emotion-11"
                  href="/Maternity"
                  target="_self"
                >
                  Maternity
                </a>
              </li>
              <li
                class="emotion-10"
              >
                <a
                  breakpoint="desktop"
                  class="emotion-11"
                  href="/Shop for the Fam Fam"
                  target="_self"
                >
                  Shop for the Fam Fam
                </a>
              </li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`<BodyOverlayContent /> with video data should match the snapshot 1`] = `
.emotion-0 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  width: 80.83333333333333%;
  padding: min(32px, 2.2222222222222223vw) min(147px, 10.208333333333334vw);
  gap: min(24px, 1.6666666666666667vw);
  pointer-events: none;
}

.emotion-1 {
  text-align: right;
  z-index: 1;
}

.emotion-1 .amp-cms--p {
  padding: 0;
  margin: 0;
  line-height: 0;
}

.emotion-1 .amp-cms--p>span {
  display: inline;
  white-space: break-spaces;
}

.emotion-1 .amp-cms--p a {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  position: relative;
  z-index: 2;
  pointer-events: auto;
}

.emotion-1 .amp-cms--p a:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-1 .amp-cms--legal-copy {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(0.8333333333333334vw, 12px));
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-1 sup {
  vertical-align: top;
  display: inline-block;
  margin-top: -0.25ex;
}

.emotion-1 sub {
  vertical-align: bottom;
  display: inline-block;
  margin-bottom: -0.25ex;
}

.emotion-1 .amp-cms--f-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.8333333333333334vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--f-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.8333333333333334vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--f-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.8333333333333334vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--fn-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.8333333333333334vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--body-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(1.3888888888888888vw, 20px));
  line-height: 1.3;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--body-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(1.25vw, 18px));
  line-height: 1.2777777777777777;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--body-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(1.1111111111111112vw, 16px));
  line-height: 1.25;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--body-4 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(0.9722222222222222vw, 14px));
  line-height: 1.2857142857142858;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--body-5 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(0.8333333333333334vw, 12px));
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--eyebrow-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(35px, min(3.4722222222222223vw, 50px));
  line-height: 0.92;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--eyebrow-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(35px, min(2.7777777777777777vw, 40px));
  line-height: 1;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--eyebrow-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(35px, min(2.430555555555556vw, 35px));
  line-height: 1;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--headline-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(8.333333333333332vw, 120px));
  line-height: 0.8333333333333334;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--headline-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(7.638888888888889vw, 110px));
  line-height: 0.8636363636363636;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--headline-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(6.944444444444445vw, 100px));
  line-height: 0.85;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--headline-4 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(6.944444444444445vw, 100px));
  line-height: 1.05;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--headline-5 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(5.555555555555555vw, 80px));
  line-height: 0.9375;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--headline-6 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(4.861111111111112vw, 70px));
  line-height: 0.9285714285714286;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--headline-7 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(4.166666666666666vw, 60px));
  line-height: 0.9166666666666666;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--headlineAlt-1 {
  color: #2B2B2B;
  font-size: max(14px, min(7.916666666666666vw, 114px));
  line-height: 1;
  letter-spacing: min(-0.027777777777777776vw, -0.4px);
  font-weight: 400;
}

.emotion-1 .amp-cms--headlineAlt-2 {
  color: #2B2B2B;
  font-size: max(14px, min(5.833333333333333vw, 84px));
  line-height: 1;
  letter-spacing: min(-0.027777777777777776vw, -0.4px);
  font-weight: 400;
}

.emotion-1 .amp-cms--headlineAlt-3 {
  color: #2B2B2B;
  font-size: max(14px, min(4.444444444444445vw, 64px));
  line-height: 1;
  letter-spacing: min(-0.027777777777777776vw, -0.4px);
  font-weight: 400;
}

.emotion-1 .amp-cms--headlineAlt-4 {
  color: #2B2B2B;
  font-size: max(14px, min(2.361111111111111vw, 34px));
  line-height: 1;
  letter-spacing: min(-0.027777777777777776vw, -0.4px);
  font-weight: 400;
}

.emotion-1 .amp-cms--headlineAlt-5 {
  color: #2B2B2B;
  font-size: min(1.1111111111111112vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-1 .amp-cms--headlineAlt-6 {
  color: #2B2B2B;
  font-size: min(1.1111111111111112vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-1 .amp-cms--headlineAlt-7 {
  color: #2B2B2B;
  font-size: min(1.1111111111111112vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-1 .amp-cms--promo-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(6.944444444444445vw, 100px));
  line-height: 1;
  letter-spacing: min(0vw, 0px);
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-1 .amp-cms--promo-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(5.555555555555555vw, 80px));
  line-height: 1;
  letter-spacing: min(0vw, 0px);
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-1 .amp-cms--promoAlt-1 {
  color: #2B2B2B;
  font-size: min(1.1111111111111112vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 600;
}

.emotion-1 .amp-cms--subhead-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(2.083333333333333vw, 30px));
  line-height: 1;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--subhead-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(1.7361111111111112vw, 25px));
  line-height: 1;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--subhead-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(1.3888888888888888vw, 20px));
  line-height: 1.1;
  letter-spacing: 0;
}

.emotion-2 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  height: auto;
  -webkit-box-pack: end;
  -ms-flex-pack: end;
  -webkit-justify-content: flex-end;
  justify-content: flex-end;
  gap: 60px;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

.emotion-3 {
  position: relative;
}

.emotion-4 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  display: inline-block;
  text-transform: none;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 1px solid transparent;
  border-radius: 0;
  max-width: 380px;
  font-size: 18px;
  font-weight: 500;
  letter-spacing: 2%;
  min-height: auto;
  max-height: auto;
  line-height: 18px;
  padding: 0;
  width: 100%;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: left;
  border-left: none;
  border-right: none;
  border-radius: 0;
  background-color: transparent;
  border-bottom: 0;
  -webkit-box-pack: left;
  -ms-flex-pack: left;
  -webkit-justify-content: left;
  justify-content: left;
  color: #000000;
  height: auto;
  -webkit-text-decoration: underline;
  text-decoration: underline;
  text-underline-offset: 5px;
  text-decoration-thickness: 2.3px;
  position: relative;
  z-index: 2;
  pointer-events: auto;
  text-align: left;
  line-height: 1;
  padding: 0;
  border: 0;
  text-underline-offset: 5px;
}

.emotion-4:focus {
  outline: none;
}

.emotion-4>span {
  padding: 1px 0;
}

.emotion-4:hover,
.emotion-4:focus {
  -webkit-text-decoration: unset;
  text-decoration: unset;
}

.emotion-4:active {
  -webkit-text-decoration: unset;
  text-decoration: unset;
}

.emotion-4>* {
  width: inherit;
  -webkit-text-decoration: inherit;
  text-decoration: inherit;
}

.emotion-4>span {
  padding: 0;
}

.emotion-5 {
  box-sizing: border-box;
}

.emotion-6 {
  display: inline-block;
  text-transform: none;
  position: relative;
  width: 100%;
  -webkit-text-decoration: inherit;
  text-decoration: inherit;
}

.emotion-7 {
  display: inline-block;
  height: 10px;
  width: 10px;
  min-height: 10px;
  min-width: 10px;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  position: absolute;
  right: 0;
  top: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  margin-left: 8px;
}

.emotion-7 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-7 svg path {
  fill: currentColor;
}

.emotion-7 svg rect {
  fill: currentColor;
}

.emotion-9 {
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
  background: #FFFFFF;
  box-sizing: border-box;
  list-style-type: none;
  min-width: 100%;
  overflow: hidden;
  position: relative;
  z-index: 390;
  border-width: 0;
  border-style: solid;
  text-align: left;
  letter-spacing: 0;
  pointer-events: auto;
  padding: 0px;
  position: absolute;
  max-height: 0;
  width: 100%;
  -webkit-transition: max-height .5s ease-in-out,visibility .5s;
  transition: max-height .5s ease-in-out,visibility .5s;
  visibility: hidden;
  margin-top: 12px;
  color: #000000;
  background-color: #FFFFFF;
}

.emotion-9 :hover {
  color: #FFFFFF;
  background-color: #000000;
}

.emotion-10 {
  box-sizing: border-box;
  width: 100%;
  border-bottom: none;
  letter-spacing: 1px;
  margin-left: 0;
  text-transform: none;
  padding: 12px;
  border-color: #000000;
}

.emotion-10:last-child {
  border: none;
}

.emotion-11 {
  cursor: pointer;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  padding: 0px;
  -webkit-text-decoration: none;
  text-decoration: none;
  -webkit-transition: color 0.3s ease;
  transition: color 0.3s ease;
  white-space: nowrap;
  font-size: 18px;
  text-align: center;
  color: inherit;
  -webkit-box-pack: start;
  -ms-flex-pack: start;
  -webkit-justify-content: start;
  justify-content: start;
  background-color: inherit;
  font-weight: 400;
  letter-spacing: 1px;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

<div>
  <div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      class="emotion-0"
    >
      <div
        class="emotion-1"
      >
        <div>
          <p
            class="amp-cms--p"
          >
            <span
              class="amp-cms--headline-4"
              style="color:#FFFFFF"
            >
              Video Text
            </span>
          </p>
        </div>
      </div>
      <div
        class="emotion-2"
      >
        <div
          class="emotion-3"
          data-testid="ctaDropdownWrapper"
        >
          <button
            aria-expanded="false"
            class="emotion-4"
            color="dark"
          >
            <span
              class="emotion-5"
            >
              <span
                class="emotion-6"
                data-id="cta-dropdown-label"
              >
                Shop All
                <span
                  aria-hidden="true"
                  class="emotion-7"
                >
                  <svg
                    fill="none"
                    viewBox="0 0 12 12"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      clip-rule="evenodd"
                      d="M7 0H5v5H0v2h5v5h2V7h5V5H7V0z"
                      fill="#000000"
                      fill-rule="evenodd"
                    />
                  </svg>
                </span>
              </span>
            </span>
          </button>
          <div
            class="emotion-8"
          >
            <ul
              aria-hidden="true"
              class="emotion-9"
            >
              <li
                class="emotion-10"
              >
                <a
                  breakpoint="desktop"
                  class="emotion-11"
                  href="/Women"
                  target="_self"
                >
                  Women
                </a>
              </li>
              <li
                class="emotion-10"
              >
                <a
                  breakpoint="desktop"
                  class="emotion-11"
                  href="/Men"
                  target="_self"
                >
                  Men
                </a>
              </li>
              <li
                class="emotion-10"
              >
                <a
                  breakpoint="desktop"
                  class="emotion-11"
                  href="/Girls"
                  target="_self"
                >
                  Girls
                </a>
              </li>
              <li
                class="emotion-10"
              >
                <a
                  breakpoint="desktop"
                  class="emotion-11"
                  href="/Boys"
                  target="_self"
                >
                  Boys
                </a>
              </li>
              <li
                class="emotion-10"
              >
                <a
                  breakpoint="desktop"
                  class="emotion-11"
                  href="/Toddler Girls"
                  target="_self"
                >
                  Toddler Girls
                </a>
              </li>
              <li
                class="emotion-10"
              >
                <a
                  breakpoint="desktop"
                  class="emotion-11"
                  href="/Toddler Boys"
                  target="_self"
                >
                  Toddler Boys
                </a>
              </li>
              <li
                class="emotion-10"
              >
                <a
                  breakpoint="desktop"
                  class="emotion-11"
                  href="/Baby Girls"
                  target="_self"
                >
                  Baby Girls
                </a>
              </li>
              <li
                class="emotion-10"
              >
                <a
                  breakpoint="desktop"
                  class="emotion-11"
                  href="/Baby Boys"
                  target="_self"
                >
                  Baby Boys
                </a>
              </li>
            </ul>
          </div>
        </div>
        <div
          class="emotion-3"
          data-testid="ctaDropdownWrapper"
        >
          <button
            aria-expanded="false"
            class="emotion-4"
            color="dark"
          >
            <span
              class="emotion-5"
            >
              <span
                class="emotion-6"
                data-id="cta-dropdown-label"
              >
                Shop Pants
                <span
                  aria-hidden="true"
                  class="emotion-7"
                >
                  <svg
                    fill="none"
                    viewBox="0 0 12 12"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      clip-rule="evenodd"
                      d="M7 0H5v5H0v2h5v5h2V7h5V5H7V0z"
                      fill="#000000"
                      fill-rule="evenodd"
                    />
                  </svg>
                </span>
              </span>
            </span>
          </button>
          <div
            class="emotion-8"
          >
            <ul
              aria-hidden="true"
              class="emotion-9"
            >
              <li
                class="emotion-10"
              >
                <a
                  breakpoint="desktop"
                  class="emotion-11"
                  href="/jeans"
                  target="_self"
                >
                  Jeans
                </a>
              </li>
              <li
                class="emotion-10"
              >
                <a
                  breakpoint="desktop"
                  class="emotion-11"
                  href="/khakis"
                  target="_self"
                >
                  Khakis
                </a>
              </li>
              <li
                class="emotion-10"
              >
                <a
                  breakpoint="desktop"
                  class="emotion-11"
                  href="/sweatpants"
                  target="_self"
                >
                  Sweatpants Sweatpants Sweatpants Sweatpants Sweatpants Sweatpants Sweatpants
                </a>
              </li>
            </ul>
          </div>
        </div>
        <div
          class="emotion-3"
          data-testid="ctaDropdownWrapper"
        >
          <button
            aria-expanded="false"
            class="emotion-4"
            color="dark"
          >
            <span
              class="emotion-5"
            >
              <span
                class="emotion-6"
                data-id="cta-dropdown-label"
              >
                Shop New
                <span
                  aria-hidden="true"
                  class="emotion-7"
                >
                  <svg
                    fill="none"
                    viewBox="0 0 12 12"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      clip-rule="evenodd"
                      d="M7 0H5v5H0v2h5v5h2V7h5V5H7V0z"
                      fill="#000000"
                      fill-rule="evenodd"
                    />
                  </svg>
                </span>
              </span>
            </span>
          </button>
          <div
            class="emotion-8"
          >
            <ul
              aria-hidden="true"
              class="emotion-9"
            >
              <li
                class="emotion-10"
              >
                <a
                  breakpoint="desktop"
                  class="emotion-11"
                  href="/Women"
                  target="_self"
                >
                  Women
                </a>
              </li>
              <li
                class="emotion-10"
              >
                <a
                  breakpoint="desktop"
                  class="emotion-11"
                  href="/Men"
                  target="_self"
                >
                  Men
                </a>
              </li>
              <li
                class="emotion-10"
              >
                <a
                  breakpoint="desktop"
                  class="emotion-11"
                  href="/Girls"
                  target="_self"
                >
                  Girls
                </a>
              </li>
              <li
                class="emotion-10"
              >
                <a
                  breakpoint="desktop"
                  class="emotion-11"
                  href="/Boys"
                  target="_self"
                >
                  Boys
                </a>
              </li>
              <li
                class="emotion-10"
              >
                <a
                  breakpoint="desktop"
                  class="emotion-11"
                  href="/Toddler Girls"
                  target="_self"
                >
                  Toddler Girls
                </a>
              </li>
              <li
                class="emotion-10"
              >
                <a
                  breakpoint="desktop"
                  class="emotion-11"
                  href="/Toddler Boys"
                  target="_self"
                >
                  Toddler Boys
                </a>
              </li>
              <li
                class="emotion-10"
              >
                <a
                  breakpoint="desktop"
                  class="emotion-11"
                  href="/Baby Girls"
                  target="_self"
                >
                  Baby Girls
                </a>
              </li>
              <li
                class="emotion-10"
              >
                <a
                  breakpoint="desktop"
                  class="emotion-11"
                  href="/Maternity"
                  target="_self"
                >
                  Maternity
                </a>
              </li>
              <li
                class="emotion-10"
              >
                <a
                  breakpoint="desktop"
                  class="emotion-11"
                  href="/Shop for the Fam Fam"
                  target="_self"
                >
                  Shop for the Fam Fam
                </a>
              </li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;
