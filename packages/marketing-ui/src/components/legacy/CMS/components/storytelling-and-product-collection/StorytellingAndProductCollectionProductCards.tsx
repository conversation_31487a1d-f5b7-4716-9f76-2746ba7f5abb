// @ts-nocheck
'use client';
import React from 'react';
import { CSSObject } from '@ecom-next/core/react-stitch';
import Link from '../../../components/Link';
import BackgroundTypeContainer from '../../subcomponents/BackgroundTypeContainer';
import { RichText } from '../../subcomponents/RichText';
import { BackgroundTypeVariant } from '../../types/amplience';
import { StorytellingProductCardsWrapper } from './styles';
import { StorytellingAndProductCollectionGenericComponentType, ProductCardsBaseProps } from './types';

type ProductCardsType = Omit<StorytellingAndProductCollectionGenericComponentType, 'mainImage' | 'richTextCss' | 'scalableText' | 'content'>;

export interface StorytellingAndProductCollectionProductCardsProps extends ProductCardsType {
  productCards: StorytellingAndProductCollectionGenericComponentType['content']['productCards'];
}

const ProductCardsBase = ({ productCards, imageStyles, isDesktop, scalableText }: ProductCardsBaseProps): JSX.Element => (
  <>
    {productCards &&
      productCards?.map((productCard, count) => {
        const { cards } = productCard;
        const { image, text } = cards;
        const productImage = image[0];
        const imageContainerProps = {
          'aria-labelledby': `storytelling-product-card-${count}`,
          key: `product-card-image-${productImage.altText}`,
          background: {
            images: image,
            type: 'image' as BackgroundTypeVariant,
          },
          css: {
            ...imageStyles,
            alignSelf: 'stretch',
            aspectRatio: '3 / 4',
            display: 'flex',
            justifySelf: 'stretch',
            objectFit: 'cover',
            overflow: 'hidden',
            width: '100%',
            height: '100%',
          } as CSSObject,
          isDesktop,
          role: 'img',
          ...(text && { altText: productImage.altText }),
        };

        const richTextWrapperStyles = {
          display: 'grid',
          marginTop: 10,
        };

        const ariaLabel =
          productCard.cards.url?.label && productCard.cards.url?.value
            ? {
                'aria-label': productCard.cards.url.label,
              }
            : {};

        const linkData = {
          'aria-label': productCard.cards.url?.label,
          css: richTextWrapperStyles,
          href: productCard.cards.url?.value,
          id: `storytelling-product-card-${count}`,
        };

        const wrappedRichText = productCard.cards.url?.value ? (
          <Link data={linkData}>
            <RichText scalableText={scalableText} text={text} />
          </Link>
        ) : (
          <div css={richTextWrapperStyles} id={`storytelling-product-card-${count}`}>
            <RichText scalableText={scalableText} text={text} />
          </div>
        );

        return (
          <div key={`storytelling-product-card-${productImage.altText}`}>
            {productCard.cards.url?.value ? (
              <a {...ariaLabel} href={productCard.cards.url?.value}>
                <BackgroundTypeContainer {...imageContainerProps} />
              </a>
            ) : (
              <BackgroundTypeContainer {...imageContainerProps} />
            )}
            {!isDesktop && text && wrappedRichText}
          </div>
        );
      })}
  </>
);

export const StorytellingAndProductCollectionProductCards = ({ imageCss, isDesktop, productCards }: StorytellingAndProductCollectionProductCardsProps) => (
  <StorytellingProductCardsWrapper isDesktop={isDesktop}>
    <ProductCardsBase imageStyles={imageCss} isDesktop={isDesktop} productCards={productCards} />
  </StorytellingProductCardsWrapper>
);
