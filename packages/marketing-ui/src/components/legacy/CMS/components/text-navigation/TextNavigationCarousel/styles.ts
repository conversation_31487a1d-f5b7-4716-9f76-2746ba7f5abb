// @ts-nocheck
'use client';
import { CSSObject, Theme } from '@ecom-next/core/react-stitch';

const SLICK_TRACK = '.slick-track';
const SLICK_SLIDE = '.slick-slide';
const SLICK_SLIDE_CONTAINER = `${SLICK_SLIDE} > div`;

export type ColorProps = string | undefined;

export type brandSpecificTextNavStyles = {
  slickTrackStyles?: CSSObject;
};

export const getCarouselStyles = ({
  firstSlideIndex = 0,
  theme,
  slideGap = 0,
  sliderWidth = 0,
  slidesToShow,
  minHeight = 'auto',
  brandSpecificStyles = {},
}: {
  firstSlideIndex?: number;
  minHeight?: number | string;
  theme: Theme;
  slideGap?: number;
  sliderWidth?: number;
  slidesToShow?: number;
  brandSpecificStyles?: brandSpecificTextNavStyles;
}): CSSObject => {
  const collapsed = !slideGap;
  const getArrowPadding = theme.brand === 'at' ? '16px 13px' : '15px 11px';
  const BUTTON_SIZE = '44px';
  const { slickTrackStyles } = brandSpecificStyles;

  const slideStyles: CSSObject = {
    letterSpacing: 0,
  };

  const firstSlideStyles: CSSObject = {
    [`${SLICK_SLIDE}:nth-of-type(${firstSlideIndex + 1})`]: {
      'a, button': {
        borderBottomRightRadius: 0,
        borderTopRightRadius: 0,
        ...(collapsed ? { borderRight: 0 } : {}),
      },
    },
  };

  const lastSlideStyles: CSSObject = {
    [`${SLICK_SLIDE}:last-of-type`]: {
      'a, button': {
        borderBottomLeftRadius: 0,
        borderTopLeftRadius: 0,
      },
    },
  };

  const styles: CSSObject = {
    [SLICK_TRACK]: {
      width: 'max-content !important',
      display: 'flex',
      ...slickTrackStyles,
    },
    [SLICK_SLIDE]: {
      // !important is needed because slick attaches an inline style (width)
      width: 'auto !important',
      height: 'auto',
      float: 'none',
      paddingRight: `${slideGap}px`,
      boxSizing: 'border-box',
      ...(sliderWidth && !!slidesToShow
        ? {
            maxWidth: `calc(${sliderWidth}px - 44px)`,
            minWidth: `calc(${sliderWidth}px / ${slidesToShow})`,
          }
        : {}),
    },
    [`${SLICK_SLIDE_CONTAINER}, ${SLICK_SLIDE_CONTAINER} > *`]: {
      height: '100%',
      width: '100%',
      display: 'flex',
      alignItems: 'center',
      minHeight,
    },

    '.slick-arrow span': {
      boxSizing: 'border-box',
      height: BUTTON_SIZE,
      width: BUTTON_SIZE,
      padding: getArrowPadding, // shrinks the icon to the correct size
    },
    '.slick-prev': {
      left: '-5px',
    },
    '.slick-next': {
      right: '-5px',
    },
    '.slick-list': {
      margin: `0 ${BUTTON_SIZE}`,
    },
    '.slick-disabled': {
      visibility: 'hidden',
    },
    '& *.focus-visible': {
      boxShadow: '0 0 0 3px #5cabf7',
    },
    [`${SLICK_SLIDE}:not(:first-of-type):not(:nth-of-type(${firstSlideIndex + 1})):not(:last-of-type)`]: {
      'a, button': {
        ...(collapsed ? { borderRight: 0 } : {}),
        borderRadius: 0,
      },
    },
    ...slideStyles,
    ...firstSlideStyles,
    ...lastSlideStyles,
  };

  return styles;
};
