// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`CTADropdownON on desktop should match snapshot 1`] = `
.emotion-0 {
  margin-left: auto;
  margin-right: auto;
  max-width: 1280px;
}

.emotion-1 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  padding: 40px;
}

.emotion-2 {
  margin-bottom: 24px;
}

.emotion-2 .amp-cms--p {
  padding: 0;
  margin: 0;
  line-height: 0;
}

.emotion-2 .amp-cms--p>span {
  display: inline;
  white-space: break-spaces;
}

.emotion-2 .amp-cms--p a {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  position: relative;
  z-index: 2;
  pointer-events: auto;
}

.emotion-2 .amp-cms--p a:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-2 .amp-cms--legal-copy {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(0.78125vw, 10px));
  line-height: 1.5;
  letter-spacing: 0;
}

.emotion-2 sup {
  vertical-align: top;
  display: inline-block;
  margin-top: -0.20ex;
}

.emotion-2 sub {
  vertical-align: bottom;
  display: inline-block;
  margin-bottom: -0.20ex;
}

.emotion-2 .amp-cms--f-0 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.9375vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-2 .amp-cms--f-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.9375vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-2 .amp-cms--f-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.9375vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-2 .amp-cms--fn-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.9375vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-2 .amp-cms--body-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(1.40625vw, 18px));
  line-height: 1.5;
  letter-spacing: min(0.056249999999999994vw, 0.72px);
  font-weight: 500;
}

.emotion-2 .amp-cms--body-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(1.25vw, 16px));
  line-height: 1.5;
  letter-spacing: min(0.05vw, 0.64px);
  font-weight: 500;
}

.emotion-2 .amp-cms--body-3 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(1.09375vw, 14px));
  line-height: 1.5;
  letter-spacing: min(0.043750000000000004vw, 0.56px);
}

.emotion-2 .amp-cms--body-4 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(0.9375vw, 12px));
  line-height: 1.5;
  letter-spacing: 0;
}

.emotion-2 .amp-cms--body-5 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(0.78125vw, 10px));
  line-height: 1.5;
  letter-spacing: 0;
}

.emotion-2 .amp-cms--eyebrow-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(2.1875vw, 28px));
  line-height: 1;
  letter-spacing: min(0.08750000000000001vw, 1.12px);
  font-weight: 500;
}

.emotion-2 .amp-cms--eyebrow-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(1.7968749999999998vw, 23px));
  line-height: 1;
  letter-spacing: min(0.071875vw, 0.92px);
  font-weight: 500;
}

.emotion-2 .amp-cms--eyebrow-3 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(1.328125vw, 17px));
  line-height: 1;
  letter-spacing: min(0.053125000000000006vw, 0.68px);
  font-weight: 500;
}

.emotion-2 .amp-cms--headline-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(9.21875vw, 118px));
  line-height: 1;
  letter-spacing: 0;
  font-weight: 700;
}

.emotion-2 .amp-cms--headline-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(6.25vw, 80px));
  line-height: 1;
  letter-spacing: 0;
  font-weight: 700;
}

.emotion-2 .amp-cms--headline-3 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(5.625vw, 72px));
  line-height: 1;
  letter-spacing: 0;
  font-weight: 700;
}

.emotion-2 .amp-cms--headline-4 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(5vw, 64px));
  line-height: 1;
  letter-spacing: min(0.1vw, 1.28px);
  font-weight: 700;
}

.emotion-2 .amp-cms--headline-5 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(3.75vw, 48px));
  line-height: 1;
  letter-spacing: min(0.22499999999999998vw, 2.88px);
  font-weight: 700;
}

.emotion-2 .amp-cms--headline-6 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(3.125vw, 40px));
  line-height: 1.1;
  letter-spacing: min(0.1875vw, 2.4px);
  font-weight: 700;
}

.emotion-2 .amp-cms--headline-7 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(2.5vw, 32px));
  line-height: 1;
  letter-spacing: min(0.1vw, 1.28px);
  font-weight: 700;
}

.emotion-2 .amp-cms--headlineAlt-1 {
  color: #003764;
  font-size: max(14px, min(8.90625vw, 114px));
  line-height: 1;
  letter-spacing: min(-0.03125vw, -0.4px);
  font-weight: 400;
}

.emotion-2 .amp-cms--headlineAlt-2 {
  color: #003764;
  font-size: max(14px, min(6.5625vw, 84px));
  line-height: 1;
  letter-spacing: min(-0.03125vw, -0.4px);
  font-weight: 400;
}

.emotion-2 .amp-cms--headlineAlt-3 {
  color: #003764;
  font-size: max(14px, min(5vw, 64px));
  line-height: 1;
  letter-spacing: min(-0.03125vw, -0.4px);
  font-weight: 400;
}

.emotion-2 .amp-cms--headlineAlt-4 {
  color: #003764;
  font-size: max(14px, min(2.65625vw, 34px));
  line-height: 1;
  letter-spacing: min(-0.03125vw, -0.4px);
  font-weight: 400;
}

.emotion-2 .amp-cms--headlineAlt-5 {
  color: #003764;
  font-size: min(1.25vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-2 .amp-cms--headlineAlt-6 {
  color: #003764;
  font-size: min(1.25vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-2 .amp-cms--headlineAlt-7 {
  color: #003764;
  font-size: min(1.25vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-2 .amp-cms--promo-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(7.8125vw, 100px));
  line-height: 1;
  letter-spacing: min(0vw, 0px);
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-2 .amp-cms--promo-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(6.25vw, 80px));
  line-height: 1;
  letter-spacing: min(0vw, 0px);
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-2 .amp-cms--promoAlt-1 {
  color: #003764;
  font-size: min(1.25vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 600;
}

.emotion-2 .amp-cms--subhead-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(1.875vw, 24px));
  line-height: 1.5;
  letter-spacing: 0;
  font-weight: 500;
}

.emotion-2 .amp-cms--subhead-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(1.7187500000000002vw, 22px));
  line-height: 1.5;
  letter-spacing: 0;
  font-weight: 500;
}

.emotion-2 .amp-cms--subhead-3 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(1.5625vw, 20px));
  line-height: 1.5;
  letter-spacing: 0;
  font-weight: 500;
}

.emotion-3 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  height: auto;
  gap: 56px;
}

.emotion-4 {
  position: relative;
  width: 22%;
  -webkit-box-flex: 1;
  -webkit-flex-grow: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
  max-width: 264px;
}

.emotion-5 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 700;
  letter-spacing: 1.6800000000000002px;
  min-height: 48px;
  max-height: auto;
  line-height: 1.1428571428571428;
  padding: 14px 22px;
  width: auto;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #22BB22;
  background-color: #BB22BB;
  border-color: #BB22BB;
  -webkit-align-self: center;
  -ms-flex-item-align: center;
  align-self: center;
  white-space: normal;
  width: 100%;
  z-index: 2;
  position: relative;
  pointer-events: auto;
  height: 50px;
  padding-inline: 15px;
}

.emotion-5:focus {
  outline: none;
}

.emotion-5>span {
  padding: 1px 0;
}

.emotion-5:hover,
.emotion-5:focus {
  color: #BB22BB;
  background-color: #22BB22;
  border-color: #BB22BB;
}

.emotion-5:active {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  color: #22BB22;
  background-color: #000000;
  border-color: #000000;
}

.emotion-5>* {
  width: inherit;
  -webkit-text-decoration: inherit;
  text-decoration: inherit;
}

.emotion-5>span>span {
  position: relative;
  -webkit-transform: translateX(5px);
  -moz-transform: translateX(5px);
  -ms-transform: translateX(5px);
  transform: translateX(5px);
}

.emotion-7 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 700;
  letter-spacing: 1.6800000000000002px;
  min-height: 48px;
  max-height: auto;
  line-height: 1.1428571428571428;
  padding: 14px 22px;
  width: 100%;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #22BB22;
  background-color: #BB22BB;
  border-color: #BB22BB;
  position: relative;
  z-index: 2;
  pointer-events: auto;
  height: 50px;
  padding-inline: 15px;
}

.emotion-7:focus {
  outline: none;
}

.emotion-7>span {
  padding: 1px 0;
}

.emotion-7:hover,
.emotion-7:focus {
  color: #BB22BB;
  background-color: #22BB22;
  border-color: #BB22BB;
}

.emotion-7:active {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  color: #22BB22;
  background-color: #000000;
  border-color: #000000;
}

.emotion-7>* {
  width: inherit;
  -webkit-text-decoration: inherit;
  text-decoration: inherit;
}

.emotion-7>span>span {
  position: relative;
  -webkit-transform: translateX(5px);
  -moz-transform: translateX(5px);
  -ms-transform: translateX(5px);
  transform: translateX(5px);
}

.emotion-8 {
  display: inline-block;
  position: relative;
  width: 100%;
  -webkit-text-decoration: inherit;
  text-decoration: inherit;
}

.emotion-9 {
  display: inline-block;
  height: 10px;
  width: 10px;
  min-height: 10px;
  min-width: 10px;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  position: absolute;
  right: 0;
  top: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
}

.emotion-9 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-9 svg path {
  fill: currentColor;
}

.emotion-9 svg rect {
  fill: currentColor;
}

.emotion-11 {
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
  background: #FFFFFF;
  box-sizing: border-box;
  list-style-type: none;
  min-width: 100%;
  overflow: hidden;
  position: relative;
  z-index: 390;
  border-width: 0;
  border-style: solid;
  text-align: left;
  letter-spacing: 0;
  pointer-events: auto;
  padding: 0 1rem;
  position: absolute;
  max-height: 0;
  width: 100%;
  -webkit-transition: max-height .5s ease-in-out,visibility .5s;
  transition: max-height .5s ease-in-out,visibility .5s;
  visibility: hidden;
  color: #BB22BB;
}

.emotion-12 {
  box-sizing: border-box;
  width: 100%;
  border-bottom: 1px solid #000000;
  border-color: #BB22BB;
}

.emotion-12:last-child {
  border: none;
}

.emotion-13 {
  cursor: pointer;
  display: block;
  padding: 0.5rem 0;
  -webkit-text-decoration: none;
  text-decoration: none;
  -webkit-transition: color 0.3s ease;
  transition: color 0.3s ease;
  white-space: nowrap;
  font-size: inherit;
  text-align: center;
  color: #000000;
  font-family: var(--font-family-font1),sans-serif;
  font-weight: 700;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

<div>
  <div
    style="font-family: var(--font-family-font1),sans-serif;"
  >
    <div
      class="emotion-0"
    >
      <div
        class="emotion-1"
      >
        <div
          class="emotion-2"
        >
          <div>
            <p
              class="amp-cms--p"
            >
              <span
                class="amp-cms--headline-4"
                style="font-weight:bold"
              >
                Go
              </span>
               Perferendis temporib
            </p>
          </div>
        </div>
        <div
          class="emotion-3"
        >
          <div
            class="emotion-4"
          >
            <a
              class="cta-label emotion-5"
              color="custom"
              href="#girls"
              target="_self"
            >
              Girls
            </a>
          </div>
          <div
            class="emotion-4"
            data-testid="ctaDropdownWrapper"
          >
            <button
              aria-expanded="false"
              class="emotion-7"
              color="custom"
            >
              <span
                class="emotion-8"
                data-id="cta-dropdown-label"
              >
                Dropdown Label
                <span
                  aria-hidden="true"
                  class="emotion-9"
                >
                  <svg
                    viewBox="0 0 10.5 10.5"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M9.75 4.125H6.375V.75a.75.75 0 00-.75-.75h-.75a.75.75 0 00-.75.75v3.375H.75a.75.75 0 00-.75.75v.75a.75.75 0 00.75.75h3.375V9.75a.75.75 0 00.75.75h.75a.75.75 0 00.75-.75V6.375H9.75a.75.75 0 00.75-.75v-.75a.75.75 0 00-.75-.75z"
                      fill="#003764"
                    />
                  </svg>
                </span>
              </span>
            </button>
            <div
              class="emotion-10"
            >
              <ul
                aria-hidden="true"
                class="emotion-11"
              >
                <li
                  class="emotion-12"
                >
                  <a
                    breakpoint="desktop"
                    class="emotion-13"
                    href="#women"
                    target="_self"
                  >
                    Women
                  </a>
                </li>
                <li
                  class="emotion-12"
                >
                  <a
                    breakpoint="desktop"
                    class="emotion-13"
                    href="#boys"
                    target="_self"
                  >
                    Boys
                  </a>
                </li>
                <li
                  class="emotion-12"
                >
                  <a
                    breakpoint="desktop"
                    class="emotion-13"
                    href="#temporib"
                    target="_self"
                  >
                    Perferendis temporib
                  </a>
                </li>
                <li
                  class="emotion-12"
                >
                  <a
                    breakpoint="desktop"
                    class="emotion-13"
                    href="#corporis"
                    target="_self"
                  >
                    Id in alias corporis
                  </a>
                </li>
                <li
                  class="emotion-12"
                >
                  <a
                    breakpoint="desktop"
                    class="emotion-13"
                    href="#possimus"
                    target="_self"
                  >
                    Similique possimus m
                  </a>
                </li>
                <li
                  class="emotion-12"
                >
                  <a
                    breakpoint="desktop"
                    class="emotion-13"
                    href="#sunt"
                    target="_self"
                  >
                    Dolor dicta sunt non
                  </a>
                </li>
                <li
                  class="emotion-12"
                >
                  <a
                    breakpoint="desktop"
                    class="emotion-13"
                    href="#voluptatibus"
                    target="_self"
                  >
                    Quia voluptatibus of
                  </a>
                </li>
                <li
                  class="emotion-12"
                >
                  <a
                    breakpoint="desktop"
                    class="emotion-13"
                    href="#nemo"
                    target="_self"
                  >
                    Ratione qui nemo nes
                  </a>
                </li>
                <li
                  class="emotion-12"
                >
                  <a
                    breakpoint="desktop"
                    class="emotion-13"
                    href="#earum"
                    target="_self"
                  >
                    Ut et earum. Soluta
                  </a>
                </li>
                <li
                  class="emotion-12"
                >
                  <a
                    breakpoint="desktop"
                    class="emotion-13"
                    href="#fugiat"
                    target="_self"
                  >
                    Non fugiat ut evenie
                  </a>
                </li>
                <li
                  class="emotion-12"
                >
                  <a
                    breakpoint="desktop"
                    class="emotion-13"
                    href="#numquam"
                    target="_self"
                  >
                    Ex ut porro numquam
                  </a>
                </li>
              </ul>
            </div>
          </div>
          <div
            class="emotion-4"
            data-testid="ctaDropdownWrapper"
          >
            <button
              aria-expanded="false"
              class="emotion-7"
              color="custom"
            >
              <span
                class="emotion-8"
                data-id="cta-dropdown-label"
              >
                dummy
                <span
                  aria-hidden="true"
                  class="emotion-9"
                >
                  <svg
                    viewBox="0 0 10.5 10.5"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M9.75 4.125H6.375V.75a.75.75 0 00-.75-.75h-.75a.75.75 0 00-.75.75v3.375H.75a.75.75 0 00-.75.75v.75a.75.75 0 00.75.75h3.375V9.75a.75.75 0 00.75.75h.75a.75.75 0 00.75-.75V6.375H9.75a.75.75 0 00.75-.75v-.75a.75.75 0 00-.75-.75z"
                      fill="#003764"
                    />
                  </svg>
                </span>
              </span>
            </button>
            <div
              class="emotion-10"
            >
              <ul
                aria-hidden="true"
                class="emotion-11"
              >
                <li
                  class="emotion-12"
                >
                  <a
                    breakpoint="desktop"
                    class="emotion-13"
                    href="#corporis"
                    target="_self"
                  >
                    Id in alias corporis
                  </a>
                </li>
                <li
                  class="emotion-12"
                >
                  <a
                    breakpoint="desktop"
                    class="emotion-13"
                    href="#possimus"
                    target="_self"
                  >
                    Similique possimus m
                  </a>
                </li>
                <li
                  class="emotion-12"
                >
                  <a
                    breakpoint="desktop"
                    class="emotion-13"
                    href="#sunt"
                    target="_self"
                  >
                    Dolor dicta sunt non
                  </a>
                </li>
                <li
                  class="emotion-12"
                >
                  <a
                    breakpoint="desktop"
                    class="emotion-13"
                    href="#voluptatibus"
                    target="_self"
                  >
                    Quia voluptatibus of
                  </a>
                </li>
                <li
                  class="emotion-12"
                >
                  <a
                    breakpoint="desktop"
                    class="emotion-13"
                    href="#nemo"
                    target="_self"
                  >
                    Ratione qui nemo nes
                  </a>
                </li>
              </ul>
            </div>
          </div>
          <div
            class="emotion-4"
          >
            <a
              class="cta-label emotion-5"
              color="custom"
              href="#omnis"
              target="_self"
            >
              Odit omnis eaque rei
            </a>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`CTADropdownON on desktop should match snapshot when both on-cta-redesign-2024 feature flag and OnCtaRedesign2024Context are enabled 1`] = `
.emotion-0 {
  margin-left: auto;
  margin-right: auto;
  max-width: 1280px;
}

.emotion-1 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  padding: 40px;
}

.emotion-2 {
  margin-bottom: 24px;
}

.emotion-2 .amp-cms--p {
  padding: 0;
  margin: 0;
  line-height: 0;
}

.emotion-2 .amp-cms--p>span {
  display: inline;
  white-space: break-spaces;
}

.emotion-2 .amp-cms--p a {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  position: relative;
  z-index: 2;
  pointer-events: auto;
}

.emotion-2 .amp-cms--p a:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-2 .amp-cms--legal-copy {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(0.78125vw, 10px));
  line-height: 1.5;
  letter-spacing: 0;
}

.emotion-2 sup {
  vertical-align: top;
  display: inline-block;
  margin-top: -0.20ex;
}

.emotion-2 sub {
  vertical-align: bottom;
  display: inline-block;
  margin-bottom: -0.20ex;
}

.emotion-2 .amp-cms--f-0 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.9375vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-2 .amp-cms--f-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.9375vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-2 .amp-cms--f-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.9375vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-2 .amp-cms--fn-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.9375vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-2 .amp-cms--body-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(1.40625vw, 18px));
  line-height: 1.5;
  letter-spacing: min(0.056249999999999994vw, 0.72px);
  font-weight: 500;
}

.emotion-2 .amp-cms--body-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(1.25vw, 16px));
  line-height: 1.5;
  letter-spacing: min(0.05vw, 0.64px);
  font-weight: 500;
}

.emotion-2 .amp-cms--body-3 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(1.09375vw, 14px));
  line-height: 1.5;
  letter-spacing: min(0.043750000000000004vw, 0.56px);
}

.emotion-2 .amp-cms--body-4 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(0.9375vw, 12px));
  line-height: 1.5;
  letter-spacing: 0;
}

.emotion-2 .amp-cms--body-5 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(0.78125vw, 10px));
  line-height: 1.5;
  letter-spacing: 0;
}

.emotion-2 .amp-cms--eyebrow-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(2.1875vw, 28px));
  line-height: 1;
  letter-spacing: min(0.08750000000000001vw, 1.12px);
  font-weight: 500;
}

.emotion-2 .amp-cms--eyebrow-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(1.7968749999999998vw, 23px));
  line-height: 1;
  letter-spacing: min(0.071875vw, 0.92px);
  font-weight: 500;
}

.emotion-2 .amp-cms--eyebrow-3 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(1.328125vw, 17px));
  line-height: 1;
  letter-spacing: min(0.053125000000000006vw, 0.68px);
  font-weight: 500;
}

.emotion-2 .amp-cms--headline-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(9.21875vw, 118px));
  line-height: 1;
  letter-spacing: 0;
  font-weight: 700;
}

.emotion-2 .amp-cms--headline-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(6.25vw, 80px));
  line-height: 1;
  letter-spacing: 0;
  font-weight: 700;
}

.emotion-2 .amp-cms--headline-3 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(5.625vw, 72px));
  line-height: 1;
  letter-spacing: 0;
  font-weight: 700;
}

.emotion-2 .amp-cms--headline-4 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(5vw, 64px));
  line-height: 1;
  letter-spacing: min(0.1vw, 1.28px);
  font-weight: 700;
}

.emotion-2 .amp-cms--headline-5 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(3.75vw, 48px));
  line-height: 1;
  letter-spacing: min(0.22499999999999998vw, 2.88px);
  font-weight: 700;
}

.emotion-2 .amp-cms--headline-6 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(3.125vw, 40px));
  line-height: 1.1;
  letter-spacing: min(0.1875vw, 2.4px);
  font-weight: 700;
}

.emotion-2 .amp-cms--headline-7 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(2.5vw, 32px));
  line-height: 1;
  letter-spacing: min(0.1vw, 1.28px);
  font-weight: 700;
}

.emotion-2 .amp-cms--headlineAlt-1 {
  color: #003764;
  font-size: max(14px, min(8.90625vw, 114px));
  line-height: 1;
  letter-spacing: min(-0.03125vw, -0.4px);
  font-weight: 400;
}

.emotion-2 .amp-cms--headlineAlt-2 {
  color: #003764;
  font-size: max(14px, min(6.5625vw, 84px));
  line-height: 1;
  letter-spacing: min(-0.03125vw, -0.4px);
  font-weight: 400;
}

.emotion-2 .amp-cms--headlineAlt-3 {
  color: #003764;
  font-size: max(14px, min(5vw, 64px));
  line-height: 1;
  letter-spacing: min(-0.03125vw, -0.4px);
  font-weight: 400;
}

.emotion-2 .amp-cms--headlineAlt-4 {
  color: #003764;
  font-size: max(14px, min(2.65625vw, 34px));
  line-height: 1;
  letter-spacing: min(-0.03125vw, -0.4px);
  font-weight: 400;
}

.emotion-2 .amp-cms--headlineAlt-5 {
  color: #003764;
  font-size: min(1.25vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-2 .amp-cms--headlineAlt-6 {
  color: #003764;
  font-size: min(1.25vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-2 .amp-cms--headlineAlt-7 {
  color: #003764;
  font-size: min(1.25vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-2 .amp-cms--promo-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(7.8125vw, 100px));
  line-height: 1;
  letter-spacing: min(0vw, 0px);
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-2 .amp-cms--promo-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(6.25vw, 80px));
  line-height: 1;
  letter-spacing: min(0vw, 0px);
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-2 .amp-cms--promoAlt-1 {
  color: #003764;
  font-size: min(1.25vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 600;
}

.emotion-2 .amp-cms--subhead-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(1.875vw, 24px));
  line-height: 1.5;
  letter-spacing: 0;
  font-weight: 500;
}

.emotion-2 .amp-cms--subhead-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(1.7187500000000002vw, 22px));
  line-height: 1.5;
  letter-spacing: 0;
  font-weight: 500;
}

.emotion-2 .amp-cms--subhead-3 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(1.5625vw, 20px));
  line-height: 1.5;
  letter-spacing: 0;
  font-weight: 500;
}

.emotion-3 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  height: auto;
  gap: 56px;
}

.emotion-4 {
  position: relative;
  width: 22%;
  -webkit-box-flex: 1;
  -webkit-flex-grow: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
  max-width: 264px;
}

.emotion-5 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 700;
  letter-spacing: 1.6800000000000002px;
  min-height: 48px;
  max-height: auto;
  line-height: 1.1428571428571428;
  padding: 14px 22px;
  width: auto;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #22BB22;
  background-color: #BB22BB;
  border-color: #BB22BB;
  -webkit-align-self: center;
  -ms-flex-item-align: center;
  align-self: center;
  white-space: normal;
  width: 100%;
  z-index: 2;
  position: relative;
  pointer-events: auto;
  height: 50px;
  padding-inline: 15px;
}

.emotion-5:focus {
  outline: none;
}

.emotion-5>span {
  padding: 1px 0;
}

.emotion-5:hover,
.emotion-5:focus {
  color: #BB22BB;
  background-color: #22BB22;
  border-color: #BB22BB;
}

.emotion-5:active {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  color: #22BB22;
  background-color: #000000;
  border-color: #000000;
}

.emotion-5>* {
  width: inherit;
  -webkit-text-decoration: inherit;
  text-decoration: inherit;
}

.emotion-5>span>span {
  position: relative;
  -webkit-transform: translateX(5px);
  -moz-transform: translateX(5px);
  -ms-transform: translateX(5px);
  transform: translateX(5px);
}

.emotion-7 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 700;
  letter-spacing: 1.6800000000000002px;
  min-height: 48px;
  max-height: auto;
  line-height: 1.1428571428571428;
  padding: 14px 22px;
  width: 100%;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #22BB22;
  background-color: #BB22BB;
  border-color: #BB22BB;
  position: relative;
  z-index: 2;
  pointer-events: auto;
  height: 50px;
  padding-inline: 15px;
}

.emotion-7:focus {
  outline: none;
}

.emotion-7>span {
  padding: 1px 0;
}

.emotion-7:hover,
.emotion-7:focus {
  color: #BB22BB;
  background-color: #22BB22;
  border-color: #BB22BB;
}

.emotion-7:active {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  color: #22BB22;
  background-color: #000000;
  border-color: #000000;
}

.emotion-7>* {
  width: inherit;
  -webkit-text-decoration: inherit;
  text-decoration: inherit;
}

.emotion-7>span>span {
  position: relative;
  -webkit-transform: translateX(5px);
  -moz-transform: translateX(5px);
  -ms-transform: translateX(5px);
  transform: translateX(5px);
}

.emotion-8 {
  display: inline-block;
  position: relative;
  width: 100%;
  -webkit-text-decoration: inherit;
  text-decoration: inherit;
}

.emotion-9 {
  display: inline-block;
  height: 10px;
  width: 10px;
  min-height: 10px;
  min-width: 10px;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  position: absolute;
  right: 0;
  top: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
}

.emotion-9 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-9 svg path {
  fill: currentColor;
}

.emotion-9 svg rect {
  fill: currentColor;
}

.emotion-11 {
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
  background: #FFFFFF;
  box-sizing: border-box;
  list-style-type: none;
  min-width: 100%;
  overflow: hidden;
  position: relative;
  z-index: 390;
  border-width: 0;
  border-style: solid;
  text-align: left;
  letter-spacing: 0;
  pointer-events: auto;
  padding: 0 1rem;
  position: absolute;
  max-height: 0;
  width: 100%;
  -webkit-transition: max-height .5s ease-in-out,visibility .5s;
  transition: max-height .5s ease-in-out,visibility .5s;
  visibility: hidden;
  color: #BB22BB;
}

.emotion-12 {
  box-sizing: border-box;
  width: 100%;
  border-bottom: 1px solid #000000;
  border-color: #BB22BB;
}

.emotion-12:last-child {
  border: none;
}

.emotion-13 {
  cursor: pointer;
  display: block;
  padding: 0.5rem 0;
  -webkit-text-decoration: none;
  text-decoration: none;
  -webkit-transition: color 0.3s ease;
  transition: color 0.3s ease;
  white-space: nowrap;
  font-size: inherit;
  text-align: center;
  color: #000000;
  font-family: var(--font-family-font1),sans-serif;
  font-weight: 700;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

<div>
  <div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-family-font1),sans-serif;"
    >
      <div
        class="emotion-0"
      >
        <div
          class="emotion-1"
        >
          <div
            class="emotion-2"
          >
            <div>
              <p
                class="amp-cms--p"
              >
                <span
                  class="amp-cms--headline-4"
                  style="font-weight:bold"
                >
                  Go
                </span>
                 Perferendis temporib
              </p>
            </div>
          </div>
          <div
            class="emotion-3"
          >
            <div
              class="emotion-4"
            >
              <a
                class="cta-label emotion-5"
                color="custom"
                href="#girls"
                target="_self"
              >
                Girls
              </a>
            </div>
            <div
              class="emotion-4"
              data-testid="ctaDropdownWrapper"
            >
              <button
                aria-expanded="false"
                class="emotion-7"
                color="custom"
              >
                <span
                  class="emotion-8"
                  data-id="cta-dropdown-label"
                >
                  Dropdown Label
                  <span
                    aria-hidden="true"
                    class="emotion-9"
                  >
                    <svg
                      fill="none"
                      viewBox="0 0 12 12"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        clip-rule="evenodd"
                        d="M7 0H5v5H0v2h5v5h2V7h5V5H7V0z"
                        fill="#000000"
                        fill-rule="evenodd"
                      />
                    </svg>
                  </span>
                </span>
              </button>
              <div
                class="emotion-10"
              >
                <ul
                  aria-hidden="true"
                  class="emotion-11"
                >
                  <li
                    class="emotion-12"
                  >
                    <a
                      breakpoint="desktop"
                      class="emotion-13"
                      href="#women"
                      target="_self"
                    >
                      Women
                    </a>
                  </li>
                  <li
                    class="emotion-12"
                  >
                    <a
                      breakpoint="desktop"
                      class="emotion-13"
                      href="#boys"
                      target="_self"
                    >
                      Boys
                    </a>
                  </li>
                  <li
                    class="emotion-12"
                  >
                    <a
                      breakpoint="desktop"
                      class="emotion-13"
                      href="#temporib"
                      target="_self"
                    >
                      Perferendis temporib
                    </a>
                  </li>
                  <li
                    class="emotion-12"
                  >
                    <a
                      breakpoint="desktop"
                      class="emotion-13"
                      href="#corporis"
                      target="_self"
                    >
                      Id in alias corporis
                    </a>
                  </li>
                  <li
                    class="emotion-12"
                  >
                    <a
                      breakpoint="desktop"
                      class="emotion-13"
                      href="#possimus"
                      target="_self"
                    >
                      Similique possimus m
                    </a>
                  </li>
                  <li
                    class="emotion-12"
                  >
                    <a
                      breakpoint="desktop"
                      class="emotion-13"
                      href="#sunt"
                      target="_self"
                    >
                      Dolor dicta sunt non
                    </a>
                  </li>
                  <li
                    class="emotion-12"
                  >
                    <a
                      breakpoint="desktop"
                      class="emotion-13"
                      href="#voluptatibus"
                      target="_self"
                    >
                      Quia voluptatibus of
                    </a>
                  </li>
                  <li
                    class="emotion-12"
                  >
                    <a
                      breakpoint="desktop"
                      class="emotion-13"
                      href="#nemo"
                      target="_self"
                    >
                      Ratione qui nemo nes
                    </a>
                  </li>
                  <li
                    class="emotion-12"
                  >
                    <a
                      breakpoint="desktop"
                      class="emotion-13"
                      href="#earum"
                      target="_self"
                    >
                      Ut et earum. Soluta
                    </a>
                  </li>
                  <li
                    class="emotion-12"
                  >
                    <a
                      breakpoint="desktop"
                      class="emotion-13"
                      href="#fugiat"
                      target="_self"
                    >
                      Non fugiat ut evenie
                    </a>
                  </li>
                  <li
                    class="emotion-12"
                  >
                    <a
                      breakpoint="desktop"
                      class="emotion-13"
                      href="#numquam"
                      target="_self"
                    >
                      Ex ut porro numquam
                    </a>
                  </li>
                </ul>
              </div>
            </div>
            <div
              class="emotion-4"
              data-testid="ctaDropdownWrapper"
            >
              <button
                aria-expanded="false"
                class="emotion-7"
                color="custom"
              >
                <span
                  class="emotion-8"
                  data-id="cta-dropdown-label"
                >
                  dummy
                  <span
                    aria-hidden="true"
                    class="emotion-9"
                  >
                    <svg
                      fill="none"
                      viewBox="0 0 12 12"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        clip-rule="evenodd"
                        d="M7 0H5v5H0v2h5v5h2V7h5V5H7V0z"
                        fill="#000000"
                        fill-rule="evenodd"
                      />
                    </svg>
                  </span>
                </span>
              </button>
              <div
                class="emotion-10"
              >
                <ul
                  aria-hidden="true"
                  class="emotion-11"
                >
                  <li
                    class="emotion-12"
                  >
                    <a
                      breakpoint="desktop"
                      class="emotion-13"
                      href="#corporis"
                      target="_self"
                    >
                      Id in alias corporis
                    </a>
                  </li>
                  <li
                    class="emotion-12"
                  >
                    <a
                      breakpoint="desktop"
                      class="emotion-13"
                      href="#possimus"
                      target="_self"
                    >
                      Similique possimus m
                    </a>
                  </li>
                  <li
                    class="emotion-12"
                  >
                    <a
                      breakpoint="desktop"
                      class="emotion-13"
                      href="#sunt"
                      target="_self"
                    >
                      Dolor dicta sunt non
                    </a>
                  </li>
                  <li
                    class="emotion-12"
                  >
                    <a
                      breakpoint="desktop"
                      class="emotion-13"
                      href="#voluptatibus"
                      target="_self"
                    >
                      Quia voluptatibus of
                    </a>
                  </li>
                  <li
                    class="emotion-12"
                  >
                    <a
                      breakpoint="desktop"
                      class="emotion-13"
                      href="#nemo"
                      target="_self"
                    >
                      Ratione qui nemo nes
                    </a>
                  </li>
                </ul>
              </div>
            </div>
            <div
              class="emotion-4"
            >
              <a
                class="cta-label emotion-5"
                color="custom"
                href="#omnis"
                target="_self"
              >
                Odit omnis eaque rei
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`CTADropdownON on mobile should match snapshot 1`] = `
.emotion-0 {
  margin-left: auto;
  margin-right: auto;
  max-width: 1280px;
}

.emotion-1 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  padding: 6.4vw 2.1333333333333333vw;
}

.emotion-2 {
  margin-bottom: 24px;
}

.emotion-2 .amp-cms--p {
  padding: 0;
  margin: 0;
  line-height: 0;
}

.emotion-2 .amp-cms--p>span {
  display: inline;
  white-space: break-spaces;
}

.emotion-2 .amp-cms--p a {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  position: relative;
  z-index: 2;
  pointer-events: auto;
}

.emotion-2 .amp-cms--p a:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-2 .amp-cms--legal-copy {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(2.666666666666667vw, 10px));
  line-height: 1.5;
  letter-spacing: 0;
}

.emotion-2 sup {
  vertical-align: top;
  display: inline-block;
  margin-top: -0.20ex;
}

.emotion-2 sub {
  vertical-align: bottom;
  display: inline-block;
  margin-bottom: -0.20ex;
}

.emotion-2 .amp-cms--f-0 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(2.933333333333333vw, 11px);
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-2 .amp-cms--f-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(2.933333333333333vw, 11px);
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-2 .amp-cms--f-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(2.933333333333333vw, 11px);
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-2 .amp-cms--fn-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(2.933333333333333vw, 11px);
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-2 .amp-cms--body-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(3.733333333333334vw, 14px));
  line-height: 1.5;
  letter-spacing: min(0.08960000000000001vw, 0.336px);
  font-weight: 500;
}

.emotion-2 .amp-cms--body-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(3.2vw, 12px));
  line-height: 1.5;
  letter-spacing: min(0.0768vw, 0.288px);
  font-weight: 500;
}

.emotion-2 .amp-cms--body-3 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(3.2vw, 12px));
  line-height: 1.5;
  letter-spacing: min(0.0768vw, 0.288px);
}

.emotion-2 .amp-cms--body-4 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(2.666666666666667vw, 10px));
  line-height: 1.5;
  letter-spacing: 0;
}

.emotion-2 .amp-cms--body-5 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(2.666666666666667vw, 10px));
  line-height: 1.5;
  letter-spacing: 0;
}

.emotion-2 .amp-cms--eyebrow-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(6.133333333333333vw, 23px));
  line-height: 1;
  letter-spacing: min(0.24533333333333335vw, 0.92px);
  font-weight: 500;
}

.emotion-2 .amp-cms--eyebrow-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(5.066666666666666vw, 19px));
  line-height: 1;
  letter-spacing: min(0.20266666666666666vw, 0.76px);
  font-weight: 500;
}

.emotion-2 .amp-cms--eyebrow-3 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(3.733333333333334vw, 14px));
  line-height: 1.0714285714285714;
  letter-spacing: min(0.14933333333333335vw, 0.56px);
  font-weight: 500;
}

.emotion-2 .amp-cms--headline-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(18.133333333333333vw, 68px));
  line-height: 1;
  letter-spacing: 0;
  font-weight: 700;
}

.emotion-2 .amp-cms--headline-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(13.333333333333334vw, 50px));
  line-height: 1;
  letter-spacing: 0;
  font-weight: 700;
}

.emotion-2 .amp-cms--headline-3 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(11.466666666666667vw, 43px));
  line-height: 1;
  letter-spacing: 0;
  font-weight: 700;
}

.emotion-2 .amp-cms--headline-4 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(9.6vw, 36px));
  line-height: 1;
  letter-spacing: min(0.19199999999999998vw, 0.72px);
  font-weight: 700;
}

.emotion-2 .amp-cms--headline-5 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(7.466666666666668vw, 28px));
  line-height: 1;
  letter-spacing: min(0.2986666666666667vw, 1.12px);
  font-weight: 700;
}

.emotion-2 .amp-cms--headline-6 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(6.4vw, 24px));
  line-height: 1.0833333333333333;
  letter-spacing: min(0.256vw, 0.96px);
  font-weight: 700;
}

.emotion-2 .amp-cms--headline-7 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(5.333333333333334vw, 20px));
  line-height: 1;
  letter-spacing: min(0.128vw, 0.48px);
  font-weight: 700;
}

.emotion-2 .amp-cms--headlineAlt-1 {
  color: #003764;
  font-size: max(13px, min(17.066666666666666vw, 64px));
  line-height: 1;
  letter-spacing: min(-0.07999999999999999vw, -0.3px);
  font-weight: 400;
}

.emotion-2 .amp-cms--headlineAlt-2 {
  color: #003764;
  font-size: max(13px, min(14.399999999999999vw, 54px));
  line-height: 1;
  letter-spacing: min(-0.07999999999999999vw, -0.3px);
  font-weight: 400;
}

.emotion-2 .amp-cms--headlineAlt-3 {
  color: #003764;
  font-size: max(13px, min(9.066666666666666vw, 34px));
  line-height: 1;
  letter-spacing: min(-0.07999999999999999vw, -0.3px);
  font-weight: 400;
}

.emotion-2 .amp-cms--headlineAlt-4 {
  color: #003764;
  font-size: max(13px, min(6.4vw, 24px));
  line-height: 1.4166666666666667;
  letter-spacing: min(-0.07999999999999999vw, -0.3px);
  font-weight: 400;
}

.emotion-2 .amp-cms--headlineAlt-5 {
  color: #003764;
  font-size: min(3.733333333333334vw, 14px);
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-2 .amp-cms--headlineAlt-6 {
  color: #003764;
  font-size: min(3.733333333333334vw, 14px);
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-2 .amp-cms--headlineAlt-7 {
  color: #003764;
  font-size: min(3.733333333333334vw, 14px);
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-2 .amp-cms--promo-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(13px, min(16vw, 60px));
  line-height: 1;
  letter-spacing: min(0vw, 0px);
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-2 .amp-cms--promo-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(13px, min(10.666666666666668vw, 40px));
  line-height: 1;
  letter-spacing: min(0vw, 0px);
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-2 .amp-cms--promoAlt-1 {
  color: #003764;
  font-size: min(3.733333333333334vw, 14px);
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 600;
}

.emotion-2 .amp-cms--subhead-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(16px, min(5.333333333333334vw, 20px));
  line-height: 1.5;
  letter-spacing: 0;
  font-weight: 500;
}

.emotion-2 .amp-cms--subhead-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(16px, min(4.8vw, 18px));
  line-height: 1.5;
  letter-spacing: 0;
  font-weight: 500;
}

.emotion-2 .amp-cms--subhead-3 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(16px, min(4.266666666666667vw, 16px));
  line-height: 1.5;
  letter-spacing: 0;
  font-weight: 500;
}

.emotion-3 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  height: auto;
  gap: 12px;
}

.emotion-4 {
  position: relative;
  width: 100%;
}

.emotion-5 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 700;
  letter-spacing: 1.6800000000000002px;
  min-height: 44px;
  max-height: auto;
  line-height: 1.1428571428571428;
  padding: 12px 22px;
  width: auto;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #22BB22;
  background-color: #BB22BB;
  border-color: #BB22BB;
  -webkit-align-self: center;
  -ms-flex-item-align: center;
  align-self: center;
  white-space: normal;
  width: 100%;
  z-index: 2;
  position: relative;
  pointer-events: auto;
  height: 45px;
  padding-inline: 15px;
}

.emotion-5:focus {
  outline: none;
}

.emotion-5>span {
  padding: 1px 0;
}

.emotion-5:hover,
.emotion-5:focus {
  color: #BB22BB;
  background-color: #22BB22;
  border-color: #BB22BB;
}

.emotion-5:active {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  color: #22BB22;
  background-color: #000000;
  border-color: #000000;
}

.emotion-5>* {
  width: inherit;
  -webkit-text-decoration: inherit;
  text-decoration: inherit;
}

.emotion-5>span>span {
  position: relative;
  -webkit-transform: translateX(5px);
  -moz-transform: translateX(5px);
  -ms-transform: translateX(5px);
  transform: translateX(5px);
}

.emotion-7 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 700;
  letter-spacing: 1.6800000000000002px;
  min-height: 44px;
  max-height: auto;
  line-height: 1.1428571428571428;
  padding: 12px 22px;
  width: 100%;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #22BB22;
  background-color: #BB22BB;
  border-color: #BB22BB;
  position: relative;
  z-index: 2;
  pointer-events: auto;
  height: 45px;
  padding-inline: 15px;
}

.emotion-7:focus {
  outline: none;
}

.emotion-7>span {
  padding: 1px 0;
}

.emotion-7>* {
  width: inherit;
  -webkit-text-decoration: inherit;
  text-decoration: inherit;
}

.emotion-7>span>span {
  position: relative;
  -webkit-transform: translateX(5px);
  -moz-transform: translateX(5px);
  -ms-transform: translateX(5px);
  transform: translateX(5px);
}

.emotion-8 {
  display: inline-block;
  position: relative;
  width: 100%;
  -webkit-text-decoration: inherit;
  text-decoration: inherit;
}

.emotion-9 {
  display: inline-block;
  height: 10px;
  width: 10px;
  min-height: 10px;
  min-width: 10px;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  position: absolute;
  right: 0;
  top: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
}

.emotion-9 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-9 svg path {
  fill: currentColor;
}

.emotion-9 svg rect {
  fill: currentColor;
}

.emotion-11 {
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
  background: #FFFFFF;
  box-sizing: border-box;
  list-style-type: none;
  min-width: 100%;
  overflow: hidden;
  position: relative;
  z-index: 390;
  border-width: 0;
  border-style: solid;
  text-align: left;
  letter-spacing: 0;
  pointer-events: auto;
  padding: inherit;
  max-height: 0;
  width: 100%;
  -webkit-transition: max-height .5s ease-in-out,visibility .5s;
  transition: max-height .5s ease-in-out,visibility .5s;
  visibility: hidden;
  color: #BB22BB;
}

.emotion-12 {
  box-sizing: border-box;
  width: auto;
  border-bottom: 1px solid #000000;
  border-color: #BB22BB;
}

.emotion-12:last-child {
  border: none;
}

.emotion-13 {
  cursor: pointer;
  display: block;
  padding: 0.75rem 0;
  -webkit-text-decoration: none;
  text-decoration: none;
  -webkit-transition: color 0.3s ease;
  transition: color 0.3s ease;
  white-space: nowrap;
  font-size: inherit;
  text-align: center;
  color: #000000;
  font-family: var(--font-family-font1),sans-serif;
  font-weight: 700;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

<div>
  <div
    style="font-family: var(--font-family-font1),sans-serif;"
  >
    <div
      class="emotion-0"
    >
      <div
        class="emotion-1"
      >
        <div
          class="emotion-2"
        >
          <div>
            <p
              class="amp-cms--p"
            >
              <span
                class="amp-cms--headline-4"
                style="font-weight:bold"
              >
                Go
              </span>
               Perferendis temporib
            </p>
          </div>
        </div>
        <div
          class="emotion-3"
        >
          <div
            class="emotion-4"
          >
            <a
              class="cta-label emotion-5"
              color="custom"
              href="#girls"
              target="_self"
            >
              Girls
            </a>
          </div>
          <div
            class="emotion-4"
            data-testid="ctaDropdownWrapper"
          >
            <button
              aria-expanded="false"
              class="emotion-7"
              color="custom"
            >
              <span
                class="emotion-8"
                data-id="cta-dropdown-label"
              >
                Dropdown Label
                <span
                  aria-hidden="true"
                  class="emotion-9"
                >
                  <svg
                    viewBox="0 0 10.5 10.5"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M9.75 4.125H6.375V.75a.75.75 0 00-.75-.75h-.75a.75.75 0 00-.75.75v3.375H.75a.75.75 0 00-.75.75v.75a.75.75 0 00.75.75h3.375V9.75a.75.75 0 00.75.75h.75a.75.75 0 00.75-.75V6.375H9.75a.75.75 0 00.75-.75v-.75a.75.75 0 00-.75-.75z"
                      fill="#003764"
                    />
                  </svg>
                </span>
              </span>
            </button>
            <div
              class="emotion-10"
            >
              <ul
                aria-hidden="true"
                class="emotion-11"
              >
                <li
                  class="emotion-12"
                >
                  <a
                    breakpoint="mobile"
                    class="emotion-13"
                    href="#women"
                    target="_self"
                  >
                    Women
                  </a>
                </li>
                <li
                  class="emotion-12"
                >
                  <a
                    breakpoint="mobile"
                    class="emotion-13"
                    href="#boys"
                    target="_self"
                  >
                    Boys
                  </a>
                </li>
                <li
                  class="emotion-12"
                >
                  <a
                    breakpoint="mobile"
                    class="emotion-13"
                    href="#temporib"
                    target="_self"
                  >
                    Perferendis temporib
                  </a>
                </li>
                <li
                  class="emotion-12"
                >
                  <a
                    breakpoint="mobile"
                    class="emotion-13"
                    href="#corporis"
                    target="_self"
                  >
                    Id in alias corporis
                  </a>
                </li>
                <li
                  class="emotion-12"
                >
                  <a
                    breakpoint="mobile"
                    class="emotion-13"
                    href="#possimus"
                    target="_self"
                  >
                    Similique possimus m
                  </a>
                </li>
                <li
                  class="emotion-12"
                >
                  <a
                    breakpoint="mobile"
                    class="emotion-13"
                    href="#sunt"
                    target="_self"
                  >
                    Dolor dicta sunt non
                  </a>
                </li>
                <li
                  class="emotion-12"
                >
                  <a
                    breakpoint="mobile"
                    class="emotion-13"
                    href="#voluptatibus"
                    target="_self"
                  >
                    Quia voluptatibus of
                  </a>
                </li>
                <li
                  class="emotion-12"
                >
                  <a
                    breakpoint="mobile"
                    class="emotion-13"
                    href="#nemo"
                    target="_self"
                  >
                    Ratione qui nemo nes
                  </a>
                </li>
                <li
                  class="emotion-12"
                >
                  <a
                    breakpoint="mobile"
                    class="emotion-13"
                    href="#earum"
                    target="_self"
                  >
                    Ut et earum. Soluta
                  </a>
                </li>
                <li
                  class="emotion-12"
                >
                  <a
                    breakpoint="mobile"
                    class="emotion-13"
                    href="#fugiat"
                    target="_self"
                  >
                    Non fugiat ut evenie
                  </a>
                </li>
                <li
                  class="emotion-12"
                >
                  <a
                    breakpoint="mobile"
                    class="emotion-13"
                    href="#numquam"
                    target="_self"
                  >
                    Ex ut porro numquam
                  </a>
                </li>
              </ul>
            </div>
          </div>
          <div
            class="emotion-4"
            data-testid="ctaDropdownWrapper"
          >
            <button
              aria-expanded="false"
              class="emotion-7"
              color="custom"
            >
              <span
                class="emotion-8"
                data-id="cta-dropdown-label"
              >
                dummy
                <span
                  aria-hidden="true"
                  class="emotion-9"
                >
                  <svg
                    viewBox="0 0 10.5 10.5"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M9.75 4.125H6.375V.75a.75.75 0 00-.75-.75h-.75a.75.75 0 00-.75.75v3.375H.75a.75.75 0 00-.75.75v.75a.75.75 0 00.75.75h3.375V9.75a.75.75 0 00.75.75h.75a.75.75 0 00.75-.75V6.375H9.75a.75.75 0 00.75-.75v-.75a.75.75 0 00-.75-.75z"
                      fill="#003764"
                    />
                  </svg>
                </span>
              </span>
            </button>
            <div
              class="emotion-10"
            >
              <ul
                aria-hidden="true"
                class="emotion-11"
              >
                <li
                  class="emotion-12"
                >
                  <a
                    breakpoint="mobile"
                    class="emotion-13"
                    href="#corporis"
                    target="_self"
                  >
                    Id in alias corporis
                  </a>
                </li>
                <li
                  class="emotion-12"
                >
                  <a
                    breakpoint="mobile"
                    class="emotion-13"
                    href="#possimus"
                    target="_self"
                  >
                    Similique possimus m
                  </a>
                </li>
                <li
                  class="emotion-12"
                >
                  <a
                    breakpoint="mobile"
                    class="emotion-13"
                    href="#sunt"
                    target="_self"
                  >
                    Dolor dicta sunt non
                  </a>
                </li>
                <li
                  class="emotion-12"
                >
                  <a
                    breakpoint="mobile"
                    class="emotion-13"
                    href="#voluptatibus"
                    target="_self"
                  >
                    Quia voluptatibus of
                  </a>
                </li>
                <li
                  class="emotion-12"
                >
                  <a
                    breakpoint="mobile"
                    class="emotion-13"
                    href="#nemo"
                    target="_self"
                  >
                    Ratione qui nemo nes
                  </a>
                </li>
              </ul>
            </div>
          </div>
          <div
            class="emotion-4"
          >
            <a
              class="cta-label emotion-5"
              color="custom"
              href="#omnis"
              target="_self"
            >
              Odit omnis eaque rei
            </a>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;
