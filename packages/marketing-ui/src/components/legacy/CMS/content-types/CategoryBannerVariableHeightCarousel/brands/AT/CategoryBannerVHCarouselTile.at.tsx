// @ts-nocheck
'use client';
import React, { useEffect, useRef } from 'react';
import { Brands } from '@ecom-next/core/react-stitch';
import FlexContainerWithTextAndCta from './FlexContainerWithTextAndCta.at';
import { useViewportIsXLarge } from '../../../../../hooks/useViewportIsLarge';
import AspectRatioContainer from '../../../../subcomponents/AspectRatioContainer';
import { heightMap, widthMap } from '../../../../components/CategoryBannerVariableHeight/styles';
import {
  FlexWrapper,
  SimpleWrapperContainer,
  Tile,
  AbsoluteWrapper,
  CategoryBannerVHCarouselTileProps,
  LinkBanner,
} from '../../../../components/CategoryBannerVHCarousel';

const CBVHCarouselTile = ({
  tiles,
  showContent,
  desktopBannerSize,
  mobileBannerSize,
  mobileTextTreatment,
  onImageHeightChange,
}: CategoryBannerVHCarouselTileProps & {
  onImageHeightChange?: (height: number) => void;
}): JSX.Element => {
  const isDesktop = useViewportIsXLarge();
  const brandName = Brands.Athleta;
  const viewPort = isDesktop ? 'desktop' : 'mobile';
  const bannerSizeDevice = isDesktop ? desktopBannerSize : mobileBannerSize;
  const bannerSize = ['medium', 'large'].includes(bannerSizeDevice) ? bannerSizeDevice : 'medium';
  const HEIGHT_MAX_SIZE: number = heightMap.categoryBanner[viewPort][bannerSize][brandName];
  const WIDTH_MAX_SIZE: number = widthMap.categoryBanner[viewPort][brandName];

  const elementRef = useRef<HTMLDivElement | null>(null);

  useEffect(() => {
    onImageHeightChange && onImageHeightChange(elementRef.current?.clientHeight || HEIGHT_MAX_SIZE);
  }, [elementRef.current?.clientHeight]);

  const carouselTileItems = tiles.map((tile: Tile, index: number) => {
    const { backgroundImage = [], mobileBackgroundImage = [], bannerLink, ctaButton, text, webAppearance } = tile;
    const numberOfTiles = tiles.length;
    const backgroundImages = !isDesktop && Boolean(mobileBackgroundImage?.length) ? mobileBackgroundImage : backgroundImage;
    const tileKey = `tile_${backgroundImages[0]?.image?.id || bannerLink?.value || text}_${index}`;
    const { desktop, mobile } = webAppearance;

    return (
      <SimpleWrapperContainer key={tileKey} css={{ position: 'relative' }}>
        <AspectRatioContainer
          ref={elementRef}
          background={{
            type: 'image',
            images: backgroundImages,
          }}
          height={HEIGHT_MAX_SIZE}
          width={WIDTH_MAX_SIZE / numberOfTiles}
        />
        {bannerLink && <LinkBanner bannerLink={bannerLink} />}
        {showContent && (
          <AbsoluteWrapper
            css={{
              position: mobileTextTreatment === 'on' || isDesktop ? 'absolute' : 'unset',
              height: mobileTextTreatment === 'below' && !isDesktop ? 'auto' : '100%',
            }}
            data-testid='absolute-wrapper'
          >
            <FlexContainerWithTextAndCta ctaButton={ctaButton} desktop={desktop} mobile={mobile} mobileTextTreatment={mobileTextTreatment} text={text} />
          </AbsoluteWrapper>
        )}
      </SimpleWrapperContainer>
    );
  });

  return <FlexWrapper>{carouselTileItems}</FlexWrapper>;
};

export default CBVHCarouselTile;
