// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`AbsoluteWrapper should render tile content below image 1`] = `
.emotion-0 {
  margin-left: auto;
  margin-right: auto;
  max-width: 1280px;
}

.emotion-2 {
  position: relative;
}

.emotion-3 {
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-3 .slick-list {
  overflow: hidden;
}

.emotion-3 .slick-track {
  width: -webkit-max-content!important;
  width: -moz-max-content!important;
  width: max-content!important;
}

.emotion-3 button.slick-next.slick-arrow.slick-next,
.emotion-3 button.slick-prev.slick-arrow.slick-prev {
  z-index: 2;
  top: min(53.333333333333336vw, 50%);
  background: #00000080;
  opacity: 60%;
  height: 44px;
  width: 44px;
}

.emotion-3 button.slick-next.slick-arrow.slick-next span svg path,
.emotion-3 button.slick-prev.slick-arrow.slick-prev span svg path {
  fill: #FFFFFF80;
}

.emotion-3 button.slick-next.slick-arrow.slick-next:hover,
.emotion-3 button.slick-prev.slick-arrow.slick-prev:hover {
  background: #00000080;
}

.emotion-3 button.slick-next.slick-arrow.slick-next:focus,
.emotion-3 button.slick-prev.slick-arrow.slick-prev:focus {
  background: #00000080;
}

.emotion-3 button.slick-next.slick-arrow.slick-next>span,
.emotion-3 button.slick-prev.slick-arrow.slick-prev>span {
  margin: auto;
}

.emotion-3 button.slick-next.slick-arrow.slick-next svg,
.emotion-3 button.slick-prev.slick-arrow.slick-prev svg {
  margin: auto;
  height: 25px;
  width: 14px;
}

.emotion-3 button.slick-next.slick-arrow.slick-next:focus-visible,
.emotion-3 button.slick-prev.slick-arrow.slick-prev:focus-visible {
  outline: auto;
}

.emotion-3 .slick-slide {
  margin: 0;
  width: auto;
}

.emotion-3 .slick-disabled {
  display: none!important;
}

.emotion-3 .slick-next {
  left: calc(100% - 44px);
}

.emotion-3 .slick-slide:first-of-type a>div:nth-of-type(2)>div>div:nth-of-type(2) {
  border-left: none;
}

.emotion-3 .slick-slide>div:first-of-type {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-3 .slick-slide>div:first-of-type>div:first-of-type {
  width: 100%;
}

.emotion-4 {
  position: relative;
}

.emotion-4 .slick-slider {
  position: relative;
  display: block;
  box-sizing: border-box;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  -webkit-touch-callout: none;
  touch-action: pan-y;
  -webkit-tap-highlight-color: transparent;
  scroll-behavior: smooth;
}

.emotion-4 .slick-slider .slick-track,
.emotion-4 .slick-slider .slick-list {
  -webkit-transform: translate3d(0, 0, 0);
  -moz-transform: translate3d(0, 0, 0);
  -ms-transform: translate3d(0, 0, 0);
  transform: translate3d(0, 0, 0);
}

.emotion-4 .slick-list {
  position: relative;
  display: block;
  overflow-x: hidden;
  overflow-y: hidden;
  margin: 0;
  padding: 0;
}

.emotion-4 .slick-list:focus {
  outline: none;
}

.emotion-4 .slick-list.dragging {
  cursor: pointer;
  cursor: hand;
}

.emotion-4 .slick-track {
  position: relative;
  top: 0;
  left: 0;
  display: block;
  margin-left: auto;
  margin-right: auto;
}

.emotion-4 .slick-track:before,
.emotion-4 .slick-track:after {
  display: table;
  content: "";
}

.emotion-4 .slick-track:after {
  clear: both;
}

.emotion-4 .slick-slide {
  display: none;
  float: left;
  height: 100%;
  min-height: 1px;
}

.emotion-4 .slick-slide img {
  display: block;
}

.emotion-4 .slick-slide.slick-loading img {
  display: none;
}

.emotion-4 .slick-slide.dragging img {
  pointer-events: none;
}

.emotion-4 [dir="rtl"] .slick-slide {
  float: right;
}

.emotion-4 .slick-initialized .slick-slide,
.emotion-4 .slick-vertical .slick-slide {
  display: block;
}

.emotion-4 .slick-vertical .slick-slide {
  border: 1px solid transparent;
  height: auto;
}

.emotion-4 .slick-loading .slick-track,
.emotion-4 .slick-loading .slick-slide {
  visibility: hidden;
}

.emotion-4 .slick-loading .slick-list {
  background: #fff url("./ajax-loader.gif") center center no-repeat;
}

.emotion-4 .slick-arrow.slick-hidden {
  display: none;
}

.emotion-4 .slick-prev,
.emotion-4 .slick-next {
  font-size: 0;
  line-height: 0;
  position: absolute;
  top: 50%;
  display: block;
  padding: 0;
  cursor: pointer;
  color: transparent;
  border: none;
  outline: none;
  background: transparent;
}

.emotion-4 .slick-prev:hover,
.emotion-4 .slick-next:hover,
.emotion-4 .slick-prev:focus,
.emotion-4 .slick-next:focus {
  color: transparent;
  outline: none;
  background: transparent;
  opacity: 1;
}

.emotion-4 .slick-prev.slick-disabled,
.emotion-4 .slick-next.slick-disabled {
  opacity: 0.25;
}

.emotion-4 .slick-prev {
  left: -0;
  -webkit-transform: translate(0, -50%) rotate(90deg);
  -moz-transform: translate(0, -50%) rotate(90deg);
  -ms-transform: translate(0, -50%) rotate(90deg);
  transform: translate(0, -50%) rotate(90deg);
}

.emotion-4 .slick-prev span {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-4 [dir="rtl"] .slick-prev {
  right: -0;
  left: auto;
}

.emotion-4 .slick-next {
  right: -0;
  -webkit-transform: translate(0, -50%) rotate(-90deg);
  -moz-transform: translate(0, -50%) rotate(-90deg);
  -ms-transform: translate(0, -50%) rotate(-90deg);
  transform: translate(0, -50%) rotate(-90deg);
}

.emotion-4 .slick-next span {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-4 [dir="rtl"] .slick-next {
  right: auto;
  left: -0;
}

.emotion-4 .slick-dotted.slick-slider {
  margin-bottom: 30px;
}

.emotion-4 .slick-dots {
  position: absolute;
  bottom: -25px;
  display: block;
  width: 100%;
  padding: 0;
  margin: 0;
  list-style: none;
  text-align: center;
}

.emotion-4 .slick-dots li {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: 20px;
  height: 20px;
  margin: 0 5px;
  padding: 0;
  cursor: pointer;
}

.emotion-4 .slick-dots li.slick-active button:before {
  opacity: 0.75;
  color: black;
}

.emotion-4 .slick-dots li button {
  font-size: 0;
  line-height: 0;
  display: block;
  padding: 5px;
  cursor: pointer;
  color: transparent;
  border: 0;
  outline: none;
  background: transparent;
}

.emotion-4 .slick-dots li button:hover,
.emotion-4 .slick-dots li button:focus {
  outline: none;
}

.emotion-4 .slick-dots li button:hover:before,
.emotion-4 .slick-dots li button:focus:before,
.emotion-4 .slick-dots li button:hover:before,
.emotion-4 .slick-dots li button:focus:before {
  opacity: 1;
}

.emotion-4 .slick-dots li button:before {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 5px;
  height: 5px;
  content: "";
  opacity: 0.25;
  background-color: black;
  border-radius: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-5 {
  display: inline-block;
  height: 2rem;
  width: 2rem;
  min-height: 2rem;
  min-width: 2rem;
}

.emotion-5 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-6 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-7 {
  width: 100%;
  position: relative;
}

.emotion-8 {
  overflow: hidden;
  position: relative;
  box-sizing: border-box;
  aspect-ratio: 375/400;
}

.emotion-9 {
  -webkit-background-size: cover;
  background-size: cover;
  aspect-ratio: 375/400;
  overflow: hidden;
}

.emotion-10 {
  width: 100%;
  aspect-ratio: 375/400;
  object-fit: cover;
}

.emotion-11 {
  width: 100%;
  height: 100%;
  position: absolute;
  left: 0;
  top: 0;
  z-index: 1;
}

.emotion-12 {
  height: 100%;
  width: 100%;
  position: absolute;
  top: 0;
  left: 0;
  position: unset;
  height: auto;
}

.emotion-13 {
  width: 100%;
  height: 100%;
  padding: 30px 16px;
  box-sizing: border-box;
  position: static;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  grid-template-columns: repeat(3, minmax(20%,max-content));
  -webkit-align-content: center;
  -ms-flex-line-pack: center;
  align-content: center;
  grid-template-rows: repeat(3, minmax(0, max-content));
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
}

.emotion-13 .rteWrapperDivs {
  grid-column: 2;
  grid-row: 2;
}

.emotion-13 .ctaWrapperDivs {
  grid-column: 2;
  grid-row: 2;
}

.emotion-14 {
  box-sizing: content-box;
  text-align: right;
}

.emotion-15 {
  text-align: right;
  display: inline-block;
}

.emotion-15 .amp-cms--p {
  padding: 0;
  margin: 0;
  line-height: 0;
}

.emotion-15 .amp-cms--p>span {
  display: inline;
  white-space: break-spaces;
}

.emotion-15 .amp-cms--p a {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  position: relative;
  z-index: 2;
  pointer-events: auto;
}

.emotion-15 .amp-cms--p a:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-15 .amp-cms--legal-copy {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(2.666666666666667vw, 34.13333333333333px));
  line-height: 1.5;
  letter-spacing: min(0.13333333333333333vw, 1.7066666666666668px);
  font-weight: 500;
}

.emotion-15 sup {
  vertical-align: top;
  display: inline-block;
  margin-top: -0.25ex;
}

.emotion-15 sub {
  vertical-align: bottom;
  display: inline-block;
  margin-bottom: -0.25ex;
}

.emotion-15 .amp-cms--f-0 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(2.933333333333333vw, 37.54666666666667px);
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-15 .amp-cms--f-1 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(2.933333333333333vw, 37.54666666666667px);
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-15 .amp-cms--f-2 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(2.933333333333333vw, 37.54666666666667px);
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-15 .amp-cms--fn-1 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(2.933333333333333vw, 37.54666666666667px);
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-15 .amp-cms--body-1 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(4.266666666666667vw, 54.61333333333334px));
  line-height: 1.625;
  letter-spacing: min(0.21333333333333335vw, 2.730666666666667px);
  font-weight: 500;
}

.emotion-15 .amp-cms--body-2 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(3.733333333333334vw, 47.78666666666667px));
  line-height: 1.4285714285714286;
  letter-spacing: min(0vw, 0px);
  font-weight: 500;
}

.emotion-15 .amp-cms--body-3 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(3.2vw, 40.96px));
  line-height: 1.5;
  letter-spacing: min(0.15999999999999998vw, 2.048px);
  font-weight: 500;
}

.emotion-15 .amp-cms--body-4 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(2.666666666666667vw, 34.13333333333333px));
  line-height: 1.6;
  letter-spacing: min(0vw, 0px);
  font-weight: 500;
}

.emotion-15 .amp-cms--body-5 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(2.666666666666667vw, 34.13333333333333px));
  line-height: 1.5;
  letter-spacing: min(0.13333333333333333vw, 1.7066666666666668px);
  font-weight: 500;
}

.emotion-15 .amp-cms--eyebrow-1 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(3.2vw, 40.96px));
  line-height: 2;
  letter-spacing: min(0.31999999999999995vw, 4.096px);
  font-weight: 700;
  text-transform: uppercase;
}

.emotion-15 .amp-cms--eyebrow-2 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(11px, min(2.933333333333333vw, 37.54666666666667px));
  line-height: 2;
  letter-spacing: min(0.29333333333333333vw, 3.754666666666667px);
  font-weight: 700;
  text-transform: uppercase;
}

.emotion-15 .amp-cms--eyebrow-3 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(2.666666666666667vw, 34.13333333333333px));
  line-height: 1.5;
  letter-spacing: min(0.18133333333333335vw, 2.321066666666667px);
  font-weight: 700;
  text-transform: uppercase;
}

.emotion-15 .amp-cms--headline-1 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(13px, min(9.6vw, 122.88000000000001px));
  line-height: 1;
  letter-spacing: min(0.36533333333333334vw, 4.676266666666668px);
  text-transform: uppercase;
  font-weight: 500;
}

.emotion-15 .amp-cms--headline-2 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(13px, min(9.066666666666666vw, 116.05333333333334px));
  line-height: 1;
  letter-spacing: min(0.45333333333333325vw, 5.802666666666667px);
  text-transform: uppercase;
  font-weight: 500;
}

.emotion-15 .amp-cms--headline-3 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(13px, min(8vw, 102.4px));
  line-height: 1;
  letter-spacing: min(0.07999999999999999vw, 1.024px);
  text-transform: uppercase;
  font-weight: 500;
}

.emotion-15 .amp-cms--headline-4 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(13px, min(6.4vw, 81.92px));
  line-height: 1;
  letter-spacing: min(0.48000000000000004vw, 6.144px);
  text-transform: uppercase;
  font-weight: 500;
}

.emotion-15 .amp-cms--headline-5 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(13px, min(5.866666666666666vw, 75.09333333333333px));
  line-height: 1.1818181818181819;
  letter-spacing: min(0.5866666666666667vw, 7.509333333333334px);
  text-transform: uppercase;
  font-weight: 600;
}

.emotion-15 .amp-cms--headline-6 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(13px, min(4.266666666666667vw, 54.61333333333334px));
  line-height: 1.375;
  letter-spacing: min(0.21333333333333335vw, 2.730666666666667px);
  text-transform: none;
  font-weight: 600;
}

.emotion-15 .amp-cms--headline-7 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(13px, min(3.733333333333334vw, 47.78666666666667px));
  line-height: 1.4285714285714286;
  letter-spacing: min(0.02666666666666667vw, 0.3413333333333334px);
  text-transform: uppercase;
  font-weight: 700;
}

.emotion-15 .amp-cms--headlineAlt-1 {
  color: #000000;
  font-family: var(--font-soehne),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(13px, min(17.066666666666666vw, 218.45333333333335px));
  line-height: 1;
  letter-spacing: min(-0.07999999999999999vw, -1.024px);
  font-weight: 400;
}

.emotion-15 .amp-cms--headlineAlt-2 {
  color: #000000;
  font-family: var(--font-soehne),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(13px, min(14.399999999999999vw, 184.32000000000002px));
  line-height: 1;
  letter-spacing: min(-0.07999999999999999vw, -1.024px);
  font-weight: 400;
}

.emotion-15 .amp-cms--headlineAlt-3 {
  color: #000000;
  font-family: var(--font-soehne),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(13px, min(9.066666666666666vw, 116.05333333333334px));
  line-height: 1;
  letter-spacing: min(-0.07999999999999999vw, -1.024px);
  font-weight: 400;
}

.emotion-15 .amp-cms--headlineAlt-4 {
  color: #000000;
  font-family: var(--font-soehne),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(13px, min(6.4vw, 81.92px));
  line-height: 1.4166666666666667;
  letter-spacing: min(-0.07999999999999999vw, -1.024px);
  font-weight: 400;
}

.emotion-15 .amp-cms--headlineAlt-5 {
  color: #000000;
  font-family: var(--font-soehne),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(3.733333333333334vw, 47.78666666666667px);
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-15 .amp-cms--headlineAlt-6 {
  color: #000000;
  font-family: var(--font-soehne),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(3.733333333333334vw, 47.78666666666667px);
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-15 .amp-cms--headlineAlt-7 {
  color: #000000;
  font-family: var(--font-soehne),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(3.733333333333334vw, 47.78666666666667px);
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-15 .amp-cms--promo-1 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(13px, min(16vw, 204.8px));
  line-height: 1;
  letter-spacing: min(0vw, 0px);
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-15 .amp-cms--promo-2 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(13px, min(10.666666666666668vw, 136.53333333333333px));
  line-height: 1;
  letter-spacing: min(0vw, 0px);
  font-weight: 600;
}

.emotion-15 .amp-cms--promoAlt-1 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(3.733333333333334vw, 47.78666666666667px);
  line-height: 0.07142857142857142;
  letter-spacing: 0;
}

.emotion-15 .amp-cms--subhead-1 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(13px, min(4.266666666666667vw, 54.61333333333334px));
  line-height: 1.75;
  letter-spacing: min(0.08533333333333333vw, 1.0922666666666667px);
  font-weight: 400;
  text-transform: uppercase;
}

.emotion-15 .amp-cms--subhead-2 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(13px, min(3.733333333333334vw, 47.78666666666667px));
  line-height: 1.5714285714285714;
  letter-spacing: min(0.18666666666666668vw, 2.3893333333333335px);
  font-weight: 500;
  text-transform: uppercase;
}

.emotion-15 .amp-cms--subhead-3 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(13px, min(3.4666666666666663vw, 44.373333333333335px));
  line-height: 1;
  letter-spacing: min(0.25866666666666666vw, 3.3109333333333333px);
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-16 {
  display: block;
  width: 100%;
  position: relative;
  z-index: 2;
  padding-top: 24px;
  text-align: start;
}

.emotion-17 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: none;
  transition: none;
  box-sizing: border-box;
  border: 1.5px solid transparent;
  border-radius: 0;
  font-size: 18px;
  font-weight: 500;
  letter-spacing: 0px;
  min-height: 44px;
  max-height: auto;
  line-height: 1.6666666666666667;
  padding: 0;
  width: auto;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: left;
  border-left: none;
  border-right: none;
  border-radius: 0;
  background-color: transparent;
  border-bottom: 0;
  -webkit-box-pack: left;
  -ms-flex-pack: left;
  -webkit-justify-content: left;
  justify-content: left;
  color: #000000;
  height: auto;
  -webkit-text-decoration: underline;
  text-decoration: underline;
  text-underline-offset: 5px;
  text-decoration-thickness: 1px;
  -webkit-align-self: flex-start;
  -ms-flex-item-align: flex-start;
  align-self: flex-start;
  white-space: normal;
}

.emotion-17:focus {
  outline: none;
}

.emotion-17>span {
  padding: 1px 0;
}

.emotion-17:hover,
.emotion-17:focus {
  text-shadow: 0 0 2px currentColor;
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-17:active {
  text-transform: uppercase;
  text-shadow: none;
  -webkit-text-decoration: underline;
  text-decoration: underline;
}

.emotion-18 {
  box-sizing: border-box;
}

.emotion-25 {
  width: 100%;
  height: 100%;
  padding: 30px 16px;
  box-sizing: border-box;
  position: static;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  grid-template-columns: repeat(3, minmax(20%,max-content));
  -webkit-align-content: center;
  -ms-flex-line-pack: center;
  align-content: center;
  grid-template-rows: repeat(3, minmax(0, max-content));
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
}

.emotion-25 .rteWrapperDivs {
  grid-column: 3;
  grid-row: 3;
}

.emotion-25 .ctaWrapperDivs {
  grid-column: 1;
  grid-row: 3;
}

.emotion-26 {
  box-sizing: content-box;
  text-align: center;
}

.emotion-27 {
  text-align: center;
  display: inline-block;
}

.emotion-27 .amp-cms--p {
  padding: 0;
  margin: 0;
  line-height: 0;
}

.emotion-27 .amp-cms--p>span {
  display: inline;
  white-space: break-spaces;
}

.emotion-27 .amp-cms--p a {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  position: relative;
  z-index: 2;
  pointer-events: auto;
}

.emotion-27 .amp-cms--p a:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-27 .amp-cms--legal-copy {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(2.666666666666667vw, 34.13333333333333px));
  line-height: 1.5;
  letter-spacing: min(0.13333333333333333vw, 1.7066666666666668px);
  font-weight: 500;
}

.emotion-27 sup {
  vertical-align: top;
  display: inline-block;
  margin-top: -0.25ex;
}

.emotion-27 sub {
  vertical-align: bottom;
  display: inline-block;
  margin-bottom: -0.25ex;
}

.emotion-27 .amp-cms--f-0 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(2.933333333333333vw, 37.54666666666667px);
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-27 .amp-cms--f-1 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(2.933333333333333vw, 37.54666666666667px);
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-27 .amp-cms--f-2 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(2.933333333333333vw, 37.54666666666667px);
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-27 .amp-cms--fn-1 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(2.933333333333333vw, 37.54666666666667px);
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-27 .amp-cms--body-1 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(4.266666666666667vw, 54.61333333333334px));
  line-height: 1.625;
  letter-spacing: min(0.21333333333333335vw, 2.730666666666667px);
  font-weight: 500;
}

.emotion-27 .amp-cms--body-2 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(3.733333333333334vw, 47.78666666666667px));
  line-height: 1.4285714285714286;
  letter-spacing: min(0vw, 0px);
  font-weight: 500;
}

.emotion-27 .amp-cms--body-3 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(3.2vw, 40.96px));
  line-height: 1.5;
  letter-spacing: min(0.15999999999999998vw, 2.048px);
  font-weight: 500;
}

.emotion-27 .amp-cms--body-4 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(2.666666666666667vw, 34.13333333333333px));
  line-height: 1.6;
  letter-spacing: min(0vw, 0px);
  font-weight: 500;
}

.emotion-27 .amp-cms--body-5 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(2.666666666666667vw, 34.13333333333333px));
  line-height: 1.5;
  letter-spacing: min(0.13333333333333333vw, 1.7066666666666668px);
  font-weight: 500;
}

.emotion-27 .amp-cms--eyebrow-1 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(3.2vw, 40.96px));
  line-height: 2;
  letter-spacing: min(0.31999999999999995vw, 4.096px);
  font-weight: 700;
  text-transform: uppercase;
}

.emotion-27 .amp-cms--eyebrow-2 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(11px, min(2.933333333333333vw, 37.54666666666667px));
  line-height: 2;
  letter-spacing: min(0.29333333333333333vw, 3.754666666666667px);
  font-weight: 700;
  text-transform: uppercase;
}

.emotion-27 .amp-cms--eyebrow-3 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(2.666666666666667vw, 34.13333333333333px));
  line-height: 1.5;
  letter-spacing: min(0.18133333333333335vw, 2.321066666666667px);
  font-weight: 700;
  text-transform: uppercase;
}

.emotion-27 .amp-cms--headline-1 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(13px, min(9.6vw, 122.88000000000001px));
  line-height: 1;
  letter-spacing: min(0.36533333333333334vw, 4.676266666666668px);
  text-transform: uppercase;
  font-weight: 500;
}

.emotion-27 .amp-cms--headline-2 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(13px, min(9.066666666666666vw, 116.05333333333334px));
  line-height: 1;
  letter-spacing: min(0.45333333333333325vw, 5.802666666666667px);
  text-transform: uppercase;
  font-weight: 500;
}

.emotion-27 .amp-cms--headline-3 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(13px, min(8vw, 102.4px));
  line-height: 1;
  letter-spacing: min(0.07999999999999999vw, 1.024px);
  text-transform: uppercase;
  font-weight: 500;
}

.emotion-27 .amp-cms--headline-4 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(13px, min(6.4vw, 81.92px));
  line-height: 1;
  letter-spacing: min(0.48000000000000004vw, 6.144px);
  text-transform: uppercase;
  font-weight: 500;
}

.emotion-27 .amp-cms--headline-5 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(13px, min(5.866666666666666vw, 75.09333333333333px));
  line-height: 1.1818181818181819;
  letter-spacing: min(0.5866666666666667vw, 7.509333333333334px);
  text-transform: uppercase;
  font-weight: 600;
}

.emotion-27 .amp-cms--headline-6 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(13px, min(4.266666666666667vw, 54.61333333333334px));
  line-height: 1.375;
  letter-spacing: min(0.21333333333333335vw, 2.730666666666667px);
  text-transform: none;
  font-weight: 600;
}

.emotion-27 .amp-cms--headline-7 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(13px, min(3.733333333333334vw, 47.78666666666667px));
  line-height: 1.4285714285714286;
  letter-spacing: min(0.02666666666666667vw, 0.3413333333333334px);
  text-transform: uppercase;
  font-weight: 700;
}

.emotion-27 .amp-cms--headlineAlt-1 {
  color: #000000;
  font-family: var(--font-soehne),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(13px, min(17.066666666666666vw, 218.45333333333335px));
  line-height: 1;
  letter-spacing: min(-0.07999999999999999vw, -1.024px);
  font-weight: 400;
}

.emotion-27 .amp-cms--headlineAlt-2 {
  color: #000000;
  font-family: var(--font-soehne),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(13px, min(14.399999999999999vw, 184.32000000000002px));
  line-height: 1;
  letter-spacing: min(-0.07999999999999999vw, -1.024px);
  font-weight: 400;
}

.emotion-27 .amp-cms--headlineAlt-3 {
  color: #000000;
  font-family: var(--font-soehne),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(13px, min(9.066666666666666vw, 116.05333333333334px));
  line-height: 1;
  letter-spacing: min(-0.07999999999999999vw, -1.024px);
  font-weight: 400;
}

.emotion-27 .amp-cms--headlineAlt-4 {
  color: #000000;
  font-family: var(--font-soehne),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(13px, min(6.4vw, 81.92px));
  line-height: 1.4166666666666667;
  letter-spacing: min(-0.07999999999999999vw, -1.024px);
  font-weight: 400;
}

.emotion-27 .amp-cms--headlineAlt-5 {
  color: #000000;
  font-family: var(--font-soehne),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(3.733333333333334vw, 47.78666666666667px);
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-27 .amp-cms--headlineAlt-6 {
  color: #000000;
  font-family: var(--font-soehne),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(3.733333333333334vw, 47.78666666666667px);
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-27 .amp-cms--headlineAlt-7 {
  color: #000000;
  font-family: var(--font-soehne),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(3.733333333333334vw, 47.78666666666667px);
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-27 .amp-cms--promo-1 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(13px, min(16vw, 204.8px));
  line-height: 1;
  letter-spacing: min(0vw, 0px);
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-27 .amp-cms--promo-2 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(13px, min(10.666666666666668vw, 136.53333333333333px));
  line-height: 1;
  letter-spacing: min(0vw, 0px);
  font-weight: 600;
}

.emotion-27 .amp-cms--promoAlt-1 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(3.733333333333334vw, 47.78666666666667px);
  line-height: 0.07142857142857142;
  letter-spacing: 0;
}

.emotion-27 .amp-cms--subhead-1 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(13px, min(4.266666666666667vw, 54.61333333333334px));
  line-height: 1.75;
  letter-spacing: min(0.08533333333333333vw, 1.0922666666666667px);
  font-weight: 400;
  text-transform: uppercase;
}

.emotion-27 .amp-cms--subhead-2 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(13px, min(3.733333333333334vw, 47.78666666666667px));
  line-height: 1.5714285714285714;
  letter-spacing: min(0.18666666666666668vw, 2.3893333333333335px);
  font-weight: 500;
  text-transform: uppercase;
}

.emotion-27 .amp-cms--subhead-3 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(13px, min(3.4666666666666663vw, 44.373333333333335px));
  line-height: 1;
  letter-spacing: min(0.25866666666666666vw, 3.3109333333333333px);
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-28 {
  display: block;
  width: 100%;
  position: relative;
  z-index: 2;
  padding-top: 24px;
  text-align: center;
}

.emotion-29 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: none;
  transition: none;
  box-sizing: border-box;
  border: 1.5px solid transparent;
  border-radius: 0;
  font-size: 18px;
  font-weight: 500;
  letter-spacing: 0px;
  min-height: auto;
  max-height: 64px;
  line-height: 1.1111111111111112;
  padding: 23px 40px;
  width: auto;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  background-color: #FFFFFF;
  color: #000000;
  border-color: #000000;
  -webkit-align-self: flex-start;
  -ms-flex-item-align: flex-start;
  align-self: flex-start;
  white-space: normal;
}

.emotion-29:focus {
  outline: none;
}

.emotion-29>span {
  padding: 1px 0;
}

.emotion-29:hover,
.emotion-29:focus {
  text-shadow: 0 0 2px currentColor;
}

.emotion-29:active {
  color: #FFFFFF;
  background-color: #000000;
  border-color: #000000;
  -webkit-text-decoration: none;
  text-decoration: none;
  text-shadow: none;
}

.emotion-47 {
  width: 100%;
  height: 100%;
  padding: 30px 16px;
  box-sizing: border-box;
  position: static;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  grid-template-columns: repeat(3, minmax(20%,max-content));
  -webkit-align-content: center;
  -ms-flex-line-pack: center;
  align-content: center;
  grid-template-rows: repeat(3, minmax(0, max-content));
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
}

.emotion-47 .rteWrapperDivs {
  grid-column: 1;
  grid-row: 1;
}

.emotion-47 .ctaWrapperDivs {
  grid-column: 1;
  grid-row: 1;
}

<div>
  <div
    style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      class="emotion-0"
    >
      <section
        class="emotion-1"
      >
        <div
          class="emotion-2"
        >
          <nav
            class="emotion-3"
          >
            <div
              class="emotion-4"
            >
              <div
                class="slick-slider slick-initialized"
                dir="ltr"
              >
                <button
                  aria-label="Previous"
                  class="slick-prev slick-arrow slick-prev slick-disabled"
                  data-role="none"
                  disabled=""
                >
                  <span
                    aria-hidden="true"
                    class="emotion-5"
                  >
                    <svg
                      viewBox="0 0 26.78 17.63"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M0 1.95l13 15.68L26.79 1.98 24.53 0 13.08 13.01 2.3.03z"
                        fill="#000000"
                      />
                    </svg>
                  </span>
                </button>
                <div
                  class="slick-list"
                >
                  <div
                    class="slick-track"
                    style="opacity: 1; transform: translate3d(0px, 0px, 0px);"
                  >
                    <div
                      aria-hidden="false"
                      class="slick-slide slick-active slick-current"
                      data-index="0"
                      style="outline: none; width: 0px;"
                      tabindex="-1"
                    >
                      <div>
                        <div
                          class="emotion-6"
                        >
                          <div
                            class="emotion-7"
                          >
                            <div
                              class="emotion-8"
                              height="400"
                              width="375"
                            >
                              <div
                                class="emotion-9"
                                data-testid="product-card-image"
                              >
                                <img
                                  alt="Kid Photo"
                                  class="emotion-10"
                                  src="https://1puomodm9vwdc1sabswsbq9607.staging.bigcontent.io/i/oldnavy/G32977_TG_MOB@2x?fmt=webp"
                                />
                              </div>
                            </div>
                            <a
                              class="emotion-11"
                              href="/linkToNewArrivals"
                              tabindex="-1"
                              target="_self"
                              title=""
                            />
                            <div
                              class="emotion-12"
                              data-testid="absolute-wrapper"
                            >
                              <div
                                class="emotion-13"
                              >
                                <div
                                  class="rteWrapperDivs emotion-14"
                                >
                                  <div
                                    class="emotion-15"
                                  >
                                    <div>
                                      <p
                                        class="amp-cms--p"
                                      >
                                        <span
                                          class="amp-cms--subhead-1"
                                        >
                                          Lorem Ipsum
                                        </span>
                                      </p>
                                      <p
                                        class="amp-cms--p"
                                      >
                                        <span
                                          class="amp-cms--body-1"
                                        >
                                          dolor sit amet, consectetur adipiscing elit.
                                        </span>
                                      </p>
                                    </div>
                                  </div>
                                </div>
                                <div
                                  class="ctaWrapperDivs emotion-16"
                                >
                                  <a
                                    class="emotion-17"
                                    color="dark"
                                    href="/buyIt"
                                  >
                                    <span
                                      class="emotion-18"
                                    >
                                      new arrivals
                                    </span>
                                  </a>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div
                      aria-hidden="true"
                      class="slick-slide"
                      data-index="1"
                      style="outline: none; width: 0px;"
                      tabindex="-1"
                    >
                      <div>
                        <div
                          class="emotion-6"
                        >
                          <div
                            class="emotion-7"
                          >
                            <div
                              class="emotion-8"
                              height="400"
                              width="375"
                            >
                              <div
                                class="emotion-9"
                                data-testid="product-card-image"
                              >
                                <img
                                  alt=""
                                  class="emotion-10"
                                  src="https://1puomodm9vwdc1sabswsbq9607.staging.bigcontent.io/i/oldnavy/faded-blue-bg-1000x115?fmt=webp"
                                />
                              </div>
                            </div>
                            <div
                              class="emotion-12"
                              data-testid="absolute-wrapper"
                            >
                              <div
                                class="emotion-25"
                              >
                                <div
                                  class="rteWrapperDivs emotion-26"
                                >
                                  <div
                                    class="emotion-27"
                                  >
                                    <div>
                                      <h2
                                        class="amp-cms--p"
                                      >
                                        <span
                                          class="amp-cms--body-1"
                                        >
                                          OUR MOST LOVED STYLES
                                        </span>
                                      </h2>
                                    </div>
                                  </div>
                                </div>
                                <div
                                  class="ctaWrapperDivs emotion-28"
                                >
                                  <a
                                    class="emotion-29"
                                    color="dark"
                                    href="/buyIt"
                                  >
                                    new arrivals
                                  </a>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div
                      aria-hidden="true"
                      class="slick-slide"
                      data-index="2"
                      style="outline: none; width: 0px;"
                      tabindex="-1"
                    >
                      <div>
                        <div
                          class="emotion-6"
                        >
                          <div
                            class="emotion-7"
                          >
                            <div
                              class="emotion-8"
                              height="400"
                              width="375"
                            >
                              <div
                                class="emotion-9"
                                data-testid="product-card-image"
                              >
                                <img
                                  alt=""
                                  class="emotion-10"
                                  src="https://2hmc8flz4ofg1dy89cc38f2xm.staging.bigcontent.io/i/athleta/annie-spratt-h-LcVG8W1XY-unsplash?fmt=webp"
                                />
                              </div>
                            </div>
                            <a
                              class="emotion-11"
                              href="/productsWeLove"
                              tabindex="-1"
                              target="_self"
                              title="Middle link"
                            />
                            <div
                              class="emotion-12"
                              data-testid="absolute-wrapper"
                            >
                              <div
                                class="emotion-13"
                              >
                                <div
                                  class="ctaWrapperDivs emotion-28"
                                >
                                  <a
                                    class="emotion-29"
                                    color="dark"
                                    href="/ss"
                                  >
                                    Get a life
                                  </a>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div
                      aria-hidden="true"
                      class="slick-slide"
                      data-index="3"
                      style="outline: none; width: 0px;"
                      tabindex="-1"
                    >
                      <div>
                        <div
                          class="emotion-6"
                        >
                          <div
                            class="emotion-7"
                          >
                            <div
                              class="emotion-8"
                              height="400"
                              width="375"
                            >
                              <div
                                class="emotion-9"
                                data-testid="product-card-image"
                              >
                                <img
                                  alt=""
                                  class="emotion-10"
                                  src="https://2hmc8flz4ofg1dy89cc38f2xm.staging.bigcontent.io/i/athleta/annie-spratt-h-LcVG8W1XY-unsplash?fmt=webp"
                                />
                              </div>
                            </div>
                            <a
                              class="emotion-11"
                              href="/lastLink"
                              tabindex="-1"
                              target="_self"
                              title="lastLink"
                            />
                            <div
                              class="emotion-12"
                              data-testid="absolute-wrapper"
                            >
                              <div
                                class="emotion-47"
                              >
                                <div
                                  class="rteWrapperDivs emotion-26"
                                >
                                  <div
                                    class="emotion-27"
                                  >
                                    <div>
                                      <p
                                        class="amp-cms--p"
                                      >
                                        <span
                                          class="amp-cms--headline-6"
                                        >
                                          Lorem Ipsum Dolor Sit Amet,
                                        </span>
                                      </p>
                                      <p
                                        class="amp-cms--p"
                                      >
                                        <span
                                          class="amp-cms--body-1"
                                        >
                                          consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam.
                                        </span>
                                      </p>
                                    </div>
                                  </div>
                                </div>
                                <div
                                  class="ctaWrapperDivs emotion-28"
                                >
                                  <a
                                    class="emotion-29"
                                    color="dark"
                                    href="/ss"
                                  >
                                    Get a life
                                  </a>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <button
                  aria-label="Next"
                  class="slick-next slick-arrow slick-next"
                  data-role="none"
                >
                  <span
                    aria-hidden="true"
                    class="emotion-5"
                  >
                    <svg
                      viewBox="0 0 26.78 17.63"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M0 1.95l13 15.68L26.79 1.98 24.53 0 13.08 13.01 2.3.03z"
                        fill="#000000"
                      />
                    </svg>
                  </span>
                </button>
                <ul
                  class="slick-dots"
                  style="display: block;"
                >
                  <li
                    class="slick-active"
                  >
                    <button>
                      1
                    </button>
                  </li>
                  <li
                    class=""
                  >
                    <button>
                      2
                    </button>
                  </li>
                  <li
                    class=""
                  >
                    <button>
                      3
                    </button>
                  </li>
                  <li
                    class=""
                  >
                    <button>
                      4
                    </button>
                  </li>
                </ul>
              </div>
            </div>
          </nav>
        </div>
      </section>
    </div>
  </div>
</div>
`;

exports[`CategoryBannerVariableHeightCarousel Athleta Content Type should match snapshot for Brands.Athleta 1`] = `
.emotion-0 {
  margin-left: auto;
  margin-right: auto;
  max-width: 1280px;
}

.emotion-2 {
  position: relative;
}

.emotion-3 {
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-3 .slick-list {
  overflow: hidden;
}

.emotion-3 .slick-track {
  width: -webkit-max-content!important;
  width: -moz-max-content!important;
  width: max-content!important;
}

.emotion-3 button.slick-next.slick-arrow.slick-next,
.emotion-3 button.slick-prev.slick-arrow.slick-prev {
  z-index: 2;
  top: min(12.5vw, 50%);
  background: #00000080;
  opacity: 60%;
  height: 44px;
  width: 44px;
}

.emotion-3 button.slick-next.slick-arrow.slick-next span svg path,
.emotion-3 button.slick-prev.slick-arrow.slick-prev span svg path {
  fill: #FFFFFF80;
}

.emotion-3 button.slick-next.slick-arrow.slick-next:hover,
.emotion-3 button.slick-prev.slick-arrow.slick-prev:hover {
  background: #00000080;
}

.emotion-3 button.slick-next.slick-arrow.slick-next:focus,
.emotion-3 button.slick-prev.slick-arrow.slick-prev:focus {
  background: #00000080;
}

.emotion-3 button.slick-next.slick-arrow.slick-next>span,
.emotion-3 button.slick-prev.slick-arrow.slick-prev>span {
  margin: auto;
}

.emotion-3 button.slick-next.slick-arrow.slick-next svg,
.emotion-3 button.slick-prev.slick-arrow.slick-prev svg {
  margin: auto;
  height: 25px;
  width: 14px;
}

.emotion-3 button.slick-next.slick-arrow.slick-next:focus-visible,
.emotion-3 button.slick-prev.slick-arrow.slick-prev:focus-visible {
  outline: auto;
}

.emotion-3 .slick-slide {
  margin: 0;
  width: auto;
}

.emotion-3 .slick-disabled {
  display: none!important;
}

.emotion-3 .slick-next {
  left: calc(100% - 44px);
}

.emotion-3 .slick-slide:first-of-type a>div:nth-of-type(2)>div>div:nth-of-type(2) {
  border-left: none;
}

.emotion-3 .slick-slide>div:first-of-type {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-3 .slick-slide>div:first-of-type>div:first-of-type {
  width: 100%;
}

.emotion-4 {
  position: relative;
}

.emotion-4 .slick-slider {
  position: relative;
  display: block;
  box-sizing: border-box;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  -webkit-touch-callout: none;
  touch-action: pan-y;
  -webkit-tap-highlight-color: transparent;
  scroll-behavior: smooth;
}

.emotion-4 .slick-slider .slick-track,
.emotion-4 .slick-slider .slick-list {
  -webkit-transform: translate3d(0, 0, 0);
  -moz-transform: translate3d(0, 0, 0);
  -ms-transform: translate3d(0, 0, 0);
  transform: translate3d(0, 0, 0);
}

.emotion-4 .slick-list {
  position: relative;
  display: block;
  overflow-x: hidden;
  overflow-y: hidden;
  margin: 0;
  padding: 0;
}

.emotion-4 .slick-list:focus {
  outline: none;
}

.emotion-4 .slick-list.dragging {
  cursor: pointer;
  cursor: hand;
}

.emotion-4 .slick-track {
  position: relative;
  top: 0;
  left: 0;
  display: block;
  margin-left: auto;
  margin-right: auto;
}

.emotion-4 .slick-track:before,
.emotion-4 .slick-track:after {
  display: table;
  content: "";
}

.emotion-4 .slick-track:after {
  clear: both;
}

.emotion-4 .slick-slide {
  display: none;
  float: left;
  height: 100%;
  min-height: 1px;
}

.emotion-4 .slick-slide img {
  display: block;
}

.emotion-4 .slick-slide.slick-loading img {
  display: none;
}

.emotion-4 .slick-slide.dragging img {
  pointer-events: none;
}

.emotion-4 [dir="rtl"] .slick-slide {
  float: right;
}

.emotion-4 .slick-initialized .slick-slide,
.emotion-4 .slick-vertical .slick-slide {
  display: block;
}

.emotion-4 .slick-vertical .slick-slide {
  border: 1px solid transparent;
  height: auto;
}

.emotion-4 .slick-loading .slick-track,
.emotion-4 .slick-loading .slick-slide {
  visibility: hidden;
}

.emotion-4 .slick-loading .slick-list {
  background: #fff url("./ajax-loader.gif") center center no-repeat;
}

.emotion-4 .slick-arrow.slick-hidden {
  display: none;
}

.emotion-4 .slick-prev,
.emotion-4 .slick-next {
  font-size: 0;
  line-height: 0;
  position: absolute;
  top: 50%;
  display: block;
  padding: 0;
  cursor: pointer;
  color: transparent;
  border: none;
  outline: none;
  background: transparent;
}

.emotion-4 .slick-prev:hover,
.emotion-4 .slick-next:hover,
.emotion-4 .slick-prev:focus,
.emotion-4 .slick-next:focus {
  color: transparent;
  outline: none;
  background: transparent;
  opacity: 1;
}

.emotion-4 .slick-prev.slick-disabled,
.emotion-4 .slick-next.slick-disabled {
  opacity: 0.25;
}

.emotion-4 .slick-prev {
  left: -0;
  -webkit-transform: translate(0, -50%) rotate(90deg);
  -moz-transform: translate(0, -50%) rotate(90deg);
  -ms-transform: translate(0, -50%) rotate(90deg);
  transform: translate(0, -50%) rotate(90deg);
}

.emotion-4 .slick-prev span {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-4 [dir="rtl"] .slick-prev {
  right: -0;
  left: auto;
}

.emotion-4 .slick-next {
  right: -0;
  -webkit-transform: translate(0, -50%) rotate(-90deg);
  -moz-transform: translate(0, -50%) rotate(-90deg);
  -ms-transform: translate(0, -50%) rotate(-90deg);
  transform: translate(0, -50%) rotate(-90deg);
}

.emotion-4 .slick-next span {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-4 [dir="rtl"] .slick-next {
  right: auto;
  left: -0;
}

.emotion-4 .slick-dotted.slick-slider {
  margin-bottom: 30px;
}

.emotion-4 .slick-dots {
  position: absolute;
  bottom: -25px;
  display: block;
  width: 100%;
  padding: 0;
  margin: 0;
  list-style: none;
  text-align: center;
}

.emotion-4 .slick-dots li {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: 20px;
  height: 20px;
  margin: 0 5px;
  padding: 0;
  cursor: pointer;
}

.emotion-4 .slick-dots li.slick-active button:before {
  opacity: 0.75;
  color: black;
}

.emotion-4 .slick-dots li button {
  font-size: 0;
  line-height: 0;
  display: block;
  padding: 5px;
  cursor: pointer;
  color: transparent;
  border: 0;
  outline: none;
  background: transparent;
}

.emotion-4 .slick-dots li button:hover,
.emotion-4 .slick-dots li button:focus {
  outline: none;
}

.emotion-4 .slick-dots li button:hover:before,
.emotion-4 .slick-dots li button:focus:before,
.emotion-4 .slick-dots li button:hover:before,
.emotion-4 .slick-dots li button:focus:before {
  opacity: 1;
}

.emotion-4 .slick-dots li button:before {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 5px;
  height: 5px;
  content: "";
  opacity: 0.25;
  background-color: black;
  border-radius: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-5 {
  display: inline-block;
  height: 2rem;
  width: 2rem;
  min-height: 2rem;
  min-width: 2rem;
}

.emotion-5 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-6 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-7 {
  width: 100%;
  position: relative;
}

.emotion-8 {
  overflow: hidden;
  position: relative;
  box-sizing: border-box;
  aspect-ratio: 640/320;
}

.emotion-9 {
  -webkit-background-size: cover;
  background-size: cover;
  aspect-ratio: 640/320;
  overflow: hidden;
}

.emotion-10 {
  width: 100%;
  aspect-ratio: 640/320;
  object-fit: cover;
}

.emotion-11 {
  width: 100%;
  height: 100%;
  position: absolute;
  left: 0;
  top: 0;
  z-index: 1;
}

.emotion-12 {
  height: 100%;
  width: 100%;
  position: absolute;
  top: 0;
  left: 0;
  position: absolute;
  height: 100%;
}

.emotion-13 {
  width: 100%;
  height: 100%;
  padding: 42px 70px;
  box-sizing: border-box;
  position: absolute;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  display: grid;
  grid-template-columns: repeat(3, minmax(min-content,max-content));
  -webkit-align-content: center;
  -ms-flex-line-pack: center;
  align-content: center;
  grid-template-rows: 1fr max-content 1fr;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  justify-content: space-between;
}

.emotion-13 .rteWrapperDivs {
  grid-column: 2;
  grid-row: 2;
}

.emotion-13 .ctaWrapperDivs {
  grid-column: 2;
  grid-row: 3;
  text-align: center;
}

.emotion-14 {
  box-sizing: content-box;
  text-align: center;
  -webkit-align-self: center;
  -ms-flex-item-align: center;
  align-self: center;
  justify-self: center;
}

.emotion-15 {
  text-align: center;
  display: inline-block;
}

.emotion-15 .amp-cms--p {
  padding: 0;
  margin: 0;
  line-height: 0;
}

.emotion-15 .amp-cms--p>span {
  display: inline;
  white-space: break-spaces;
}

.emotion-15 .amp-cms--p a {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  position: relative;
  z-index: 2;
  pointer-events: auto;
}

.emotion-15 .amp-cms--p a:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-15 .amp-cms--legal-copy {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(0.78125vw, 10px));
  line-height: 1.5;
  letter-spacing: min(0.0390625vw, 0.5px);
  font-weight: 500;
}

.emotion-15 sup {
  vertical-align: top;
  display: inline-block;
  margin-top: -0.25ex;
}

.emotion-15 sub {
  vertical-align: bottom;
  display: inline-block;
  margin-bottom: -0.25ex;
}

.emotion-15 .amp-cms--f-0 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.9375vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-15 .amp-cms--f-1 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.9375vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-15 .amp-cms--f-2 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.9375vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-15 .amp-cms--fn-1 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.9375vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-15 .amp-cms--body-1 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(1.40625vw, 18px));
  line-height: 1.5555555555555556;
  letter-spacing: min(0.0703125vw, 0.9px);
  font-weight: 500;
}

.emotion-15 .amp-cms--body-2 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(1.25vw, 16px));
  line-height: 1.25;
  letter-spacing: min(0vw, 0px);
  font-weight: 500;
}

.emotion-15 .amp-cms--body-3 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(1.09375vw, 14px));
  line-height: 1.5714285714285714;
  letter-spacing: min(0.05468749999999999vw, 0.7px);
  font-weight: 500;
}

.emotion-15 .amp-cms--body-4 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(0.9375vw, 12px));
  line-height: 1.5;
  letter-spacing: min(0vw, 0px);
  font-weight: 500;
}

.emotion-15 .amp-cms--body-5 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(0.78125vw, 10px));
  line-height: 1.5;
  letter-spacing: min(0.0390625vw, 0.5px);
  font-weight: 500;
}

.emotion-15 .amp-cms--eyebrow-1 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(1.328125vw, 17px));
  line-height: 1.588235294117647;
  letter-spacing: min(0.1328125vw, 1.7px);
  font-weight: 700;
  text-transform: uppercase;
}

.emotion-15 .amp-cms--eyebrow-2 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(11px, min(1.25vw, 16px));
  line-height: 1.75;
  letter-spacing: min(0.125vw, 1.6px);
  font-weight: 700;
  text-transform: uppercase;
}

.emotion-15 .amp-cms--eyebrow-3 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(0.9375vw, 12px));
  line-height: 1.8333333333333333;
  letter-spacing: min(0.0640625vw, 0.82px);
  font-weight: 700;
  text-transform: uppercase;
}

.emotion-15 .amp-cms--headline-1 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(4.6875vw, 60px));
  line-height: 1;
  letter-spacing: min(0.1875vw, 2.4px);
  text-transform: uppercase;
  font-weight: 500;
}

.emotion-15 .amp-cms--headline-2 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(4.375vw, 56px));
  line-height: 1;
  letter-spacing: min(0.21874999999999997vw, 2.8px);
  text-transform: uppercase;
  font-weight: 500;
}

.emotion-15 .amp-cms--headline-3 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(3.90625vw, 50px));
  line-height: 1;
  letter-spacing: min(0.1953125vw, 2.5px);
  text-transform: uppercase;
  font-weight: 500;
}

.emotion-15 .amp-cms--headline-4 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(3.125vw, 40px));
  line-height: 1;
  letter-spacing: min(0.21874999999999997vw, 2.8px);
  text-transform: uppercase;
  font-weight: 500;
}

.emotion-15 .amp-cms--headline-5 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(2.8125vw, 36px));
  line-height: 1;
  letter-spacing: min(0.28125vw, 3.6px);
  text-transform: uppercase;
  font-weight: 600;
}

.emotion-15 .amp-cms--headline-6 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(2.1875vw, 28px));
  line-height: 1.1428571428571428;
  letter-spacing: min(0.11796875vw, 1.51px);
  text-transform: none;
  font-weight: 600;
}

.emotion-15 .amp-cms--headline-7 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(1.5625vw, 20px));
  line-height: 1.2;
  letter-spacing: min(0.1015625vw, 1.3px);
  text-transform: uppercase;
  font-weight: 700;
}

.emotion-15 .amp-cms--headlineAlt-1 {
  color: #000000;
  font-family: var(--font-soehne),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(9.6875vw, 124px));
  line-height: 1;
  letter-spacing: min(-0.03125vw, -0.4px);
  font-weight: 400;
}

.emotion-15 .amp-cms--headlineAlt-2 {
  color: #000000;
  font-family: var(--font-soehne),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(6.5625vw, 84px));
  line-height: 1;
  letter-spacing: min(-0.03125vw, -0.4px);
  font-weight: 400;
}

.emotion-15 .amp-cms--headlineAlt-3 {
  color: #000000;
  font-family: var(--font-soehne),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(5vw, 64px));
  line-height: 1;
  letter-spacing: min(-0.03125vw, -0.4px);
  font-weight: 400;
}

.emotion-15 .amp-cms--headlineAlt-4 {
  color: #000000;
  font-family: var(--font-soehne),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(2.65625vw, 34px));
  line-height: 1;
  letter-spacing: min(-0.03125vw, -0.4px);
  font-weight: 400;
}

.emotion-15 .amp-cms--headlineAlt-5 {
  color: #000000;
  font-family: var(--font-soehne),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(1.25vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-15 .amp-cms--headlineAlt-6 {
  color: #000000;
  font-family: var(--font-soehne),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(1.25vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-15 .amp-cms--headlineAlt-7 {
  color: #000000;
  font-family: var(--font-soehne),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(1.25vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-15 .amp-cms--promo-1 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(7.8125vw, 100px));
  line-height: 1;
  letter-spacing: min(0vw, 0px);
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-15 .amp-cms--promo-2 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(6.25vw, 80px));
  line-height: 1;
  letter-spacing: min(0vw, 0px);
  font-weight: 600;
}

.emotion-15 .amp-cms--promoAlt-1 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(1.25vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
}

.emotion-15 .amp-cms--subhead-1 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(1.875vw, 24px));
  line-height: 1.5;
  letter-spacing: min(0.09375vw, 1.2px);
  font-weight: 400;
  text-transform: uppercase;
}

.emotion-15 .amp-cms--subhead-2 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(1.40625vw, 18px));
  line-height: 1.5555555555555556;
  letter-spacing: min(0.028124999999999997vw, 0.36px);
  font-weight: 500;
  text-transform: uppercase;
}

.emotion-15 .amp-cms--subhead-3 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(1.09375vw, 14px));
  line-height: 1.1428571428571428;
  letter-spacing: min(0.05468749999999999vw, 0.7px);
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-16 {
  display: block;
  width: 100%;
  position: relative;
  z-index: 2;
  padding-top: 24px;
  text-align: center;
  -webkit-align-self: center;
  -ms-flex-item-align: center;
  align-self: center;
  justify-self: center;
}

.emotion-17 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: none;
  transition: none;
  box-sizing: border-box;
  border: 1.5px solid transparent;
  border-radius: 0;
  font-size: 18px;
  font-weight: 500;
  letter-spacing: 0px;
  min-height: auto;
  max-height: auto;
  line-height: 1.6666666666666667;
  padding: 0;
  width: auto;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: left;
  border-left: none;
  border-right: none;
  border-radius: 0;
  background-color: transparent;
  border-bottom: 0;
  -webkit-box-pack: left;
  -ms-flex-pack: left;
  -webkit-justify-content: left;
  justify-content: left;
  color: #000000;
  height: auto;
  -webkit-text-decoration: underline;
  text-decoration: underline;
  text-underline-offset: 5px;
  text-decoration-thickness: 1px;
  -webkit-align-self: flex-start;
  -ms-flex-item-align: flex-start;
  align-self: flex-start;
  white-space: normal;
}

.emotion-17:focus {
  outline: none;
}

.emotion-17>span {
  padding: 1px 0;
}

.emotion-17:hover,
.emotion-17:focus {
  text-shadow: 0 0 2px currentColor;
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-17:active {
  text-transform: uppercase;
  text-shadow: none;
  -webkit-text-decoration: underline;
  text-decoration: underline;
}

.emotion-18 {
  box-sizing: border-box;
}

.emotion-24 {
  width: 100%;
  height: 100%;
  padding: 42px 70px;
  box-sizing: border-box;
  position: absolute;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  display: grid;
  grid-template-columns: repeat(3, minmax(min-content,max-content));
  -webkit-align-content: center;
  -ms-flex-line-pack: center;
  align-content: center;
  grid-template-rows: repeat(3, minmax(0, max-content));
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  justify-content: space-between;
}

.emotion-24 .rteWrapperDivs {
  grid-column: 3;
  grid-row: 3;
}

.emotion-24 .ctaWrapperDivs {
  grid-column: 1;
  grid-row: 3;
  text-align: end;
}

.emotion-25 {
  box-sizing: content-box;
  text-align: end;
  -webkit-align-self: end;
  -ms-flex-item-align: end;
  align-self: end;
  justify-self: end;
}

.emotion-26 {
  text-align: end;
  display: inline-block;
}

.emotion-26 .amp-cms--p {
  padding: 0;
  margin: 0;
  line-height: 0;
}

.emotion-26 .amp-cms--p>span {
  display: inline;
  white-space: break-spaces;
}

.emotion-26 .amp-cms--p a {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  position: relative;
  z-index: 2;
  pointer-events: auto;
}

.emotion-26 .amp-cms--p a:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-26 .amp-cms--legal-copy {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(0.78125vw, 10px));
  line-height: 1.5;
  letter-spacing: min(0.0390625vw, 0.5px);
  font-weight: 500;
}

.emotion-26 sup {
  vertical-align: top;
  display: inline-block;
  margin-top: -0.25ex;
}

.emotion-26 sub {
  vertical-align: bottom;
  display: inline-block;
  margin-bottom: -0.25ex;
}

.emotion-26 .amp-cms--f-0 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.9375vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-26 .amp-cms--f-1 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.9375vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-26 .amp-cms--f-2 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.9375vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-26 .amp-cms--fn-1 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.9375vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-26 .amp-cms--body-1 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(1.40625vw, 18px));
  line-height: 1.5555555555555556;
  letter-spacing: min(0.0703125vw, 0.9px);
  font-weight: 500;
}

.emotion-26 .amp-cms--body-2 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(1.25vw, 16px));
  line-height: 1.25;
  letter-spacing: min(0vw, 0px);
  font-weight: 500;
}

.emotion-26 .amp-cms--body-3 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(1.09375vw, 14px));
  line-height: 1.5714285714285714;
  letter-spacing: min(0.05468749999999999vw, 0.7px);
  font-weight: 500;
}

.emotion-26 .amp-cms--body-4 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(0.9375vw, 12px));
  line-height: 1.5;
  letter-spacing: min(0vw, 0px);
  font-weight: 500;
}

.emotion-26 .amp-cms--body-5 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(0.78125vw, 10px));
  line-height: 1.5;
  letter-spacing: min(0.0390625vw, 0.5px);
  font-weight: 500;
}

.emotion-26 .amp-cms--eyebrow-1 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(1.328125vw, 17px));
  line-height: 1.588235294117647;
  letter-spacing: min(0.1328125vw, 1.7px);
  font-weight: 700;
  text-transform: uppercase;
}

.emotion-26 .amp-cms--eyebrow-2 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(11px, min(1.25vw, 16px));
  line-height: 1.75;
  letter-spacing: min(0.125vw, 1.6px);
  font-weight: 700;
  text-transform: uppercase;
}

.emotion-26 .amp-cms--eyebrow-3 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(0.9375vw, 12px));
  line-height: 1.8333333333333333;
  letter-spacing: min(0.0640625vw, 0.82px);
  font-weight: 700;
  text-transform: uppercase;
}

.emotion-26 .amp-cms--headline-1 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(4.6875vw, 60px));
  line-height: 1;
  letter-spacing: min(0.1875vw, 2.4px);
  text-transform: uppercase;
  font-weight: 500;
}

.emotion-26 .amp-cms--headline-2 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(4.375vw, 56px));
  line-height: 1;
  letter-spacing: min(0.21874999999999997vw, 2.8px);
  text-transform: uppercase;
  font-weight: 500;
}

.emotion-26 .amp-cms--headline-3 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(3.90625vw, 50px));
  line-height: 1;
  letter-spacing: min(0.1953125vw, 2.5px);
  text-transform: uppercase;
  font-weight: 500;
}

.emotion-26 .amp-cms--headline-4 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(3.125vw, 40px));
  line-height: 1;
  letter-spacing: min(0.21874999999999997vw, 2.8px);
  text-transform: uppercase;
  font-weight: 500;
}

.emotion-26 .amp-cms--headline-5 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(2.8125vw, 36px));
  line-height: 1;
  letter-spacing: min(0.28125vw, 3.6px);
  text-transform: uppercase;
  font-weight: 600;
}

.emotion-26 .amp-cms--headline-6 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(2.1875vw, 28px));
  line-height: 1.1428571428571428;
  letter-spacing: min(0.11796875vw, 1.51px);
  text-transform: none;
  font-weight: 600;
}

.emotion-26 .amp-cms--headline-7 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(1.5625vw, 20px));
  line-height: 1.2;
  letter-spacing: min(0.1015625vw, 1.3px);
  text-transform: uppercase;
  font-weight: 700;
}

.emotion-26 .amp-cms--headlineAlt-1 {
  color: #000000;
  font-family: var(--font-soehne),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(9.6875vw, 124px));
  line-height: 1;
  letter-spacing: min(-0.03125vw, -0.4px);
  font-weight: 400;
}

.emotion-26 .amp-cms--headlineAlt-2 {
  color: #000000;
  font-family: var(--font-soehne),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(6.5625vw, 84px));
  line-height: 1;
  letter-spacing: min(-0.03125vw, -0.4px);
  font-weight: 400;
}

.emotion-26 .amp-cms--headlineAlt-3 {
  color: #000000;
  font-family: var(--font-soehne),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(5vw, 64px));
  line-height: 1;
  letter-spacing: min(-0.03125vw, -0.4px);
  font-weight: 400;
}

.emotion-26 .amp-cms--headlineAlt-4 {
  color: #000000;
  font-family: var(--font-soehne),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(2.65625vw, 34px));
  line-height: 1;
  letter-spacing: min(-0.03125vw, -0.4px);
  font-weight: 400;
}

.emotion-26 .amp-cms--headlineAlt-5 {
  color: #000000;
  font-family: var(--font-soehne),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(1.25vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-26 .amp-cms--headlineAlt-6 {
  color: #000000;
  font-family: var(--font-soehne),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(1.25vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-26 .amp-cms--headlineAlt-7 {
  color: #000000;
  font-family: var(--font-soehne),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(1.25vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-26 .amp-cms--promo-1 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(7.8125vw, 100px));
  line-height: 1;
  letter-spacing: min(0vw, 0px);
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-26 .amp-cms--promo-2 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(6.25vw, 80px));
  line-height: 1;
  letter-spacing: min(0vw, 0px);
  font-weight: 600;
}

.emotion-26 .amp-cms--promoAlt-1 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(1.25vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
}

.emotion-26 .amp-cms--subhead-1 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(1.875vw, 24px));
  line-height: 1.5;
  letter-spacing: min(0.09375vw, 1.2px);
  font-weight: 400;
  text-transform: uppercase;
}

.emotion-26 .amp-cms--subhead-2 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(1.40625vw, 18px));
  line-height: 1.5555555555555556;
  letter-spacing: min(0.028124999999999997vw, 0.36px);
  font-weight: 500;
  text-transform: uppercase;
}

.emotion-26 .amp-cms--subhead-3 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(1.09375vw, 14px));
  line-height: 1.1428571428571428;
  letter-spacing: min(0.05468749999999999vw, 0.7px);
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-27 {
  display: block;
  width: 100%;
  position: relative;
  z-index: 2;
  padding-top: 24px;
  text-align: left;
  -webkit-align-self: end;
  -ms-flex-item-align: end;
  align-self: end;
  justify-self: end;
}

.emotion-28 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: none;
  transition: none;
  box-sizing: border-box;
  border: 1.5px solid transparent;
  border-radius: 0;
  font-size: 18px;
  font-weight: 500;
  letter-spacing: 0px;
  min-height: auto;
  max-height: 64px;
  line-height: 1.1111111111111112;
  padding: 23px 40px;
  width: auto;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  background-color: #FFFFFF;
  color: #000000;
  border-color: #000000;
  -webkit-align-self: flex-start;
  -ms-flex-item-align: flex-start;
  align-self: flex-start;
  white-space: normal;
}

.emotion-28:focus {
  outline: none;
}

.emotion-28>span {
  padding: 1px 0;
}

.emotion-28:hover,
.emotion-28:focus {
  text-shadow: 0 0 2px currentColor;
}

.emotion-28:active {
  color: #FFFFFF;
  background-color: #000000;
  border-color: #000000;
  -webkit-text-decoration: none;
  text-decoration: none;
  text-shadow: none;
}

.emotion-31 {
  overflow: hidden;
  position: relative;
  box-sizing: border-box;
  aspect-ratio: 1280/320;
}

.emotion-32 {
  -webkit-background-size: cover;
  background-size: cover;
  aspect-ratio: 1280/320;
  overflow: hidden;
}

.emotion-33 {
  width: 100%;
  aspect-ratio: 1280/320;
  object-fit: cover;
}

.emotion-36 {
  width: 100%;
  height: 100%;
  padding: 42px 70px;
  box-sizing: border-box;
  position: absolute;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  display: grid;
  grid-template-columns: repeat(3, minmax(min-content,max-content));
  -webkit-align-content: center;
  -ms-flex-line-pack: center;
  align-content: center;
  grid-template-rows: repeat(3, minmax(0, max-content));
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  justify-content: space-between;
}

.emotion-36 .rteWrapperDivs {
  grid-column: 2;
  grid-row: 2;
}

.emotion-36 .ctaWrapperDivs {
  grid-column: 2;
  grid-row: 2;
  text-align: start;
}

.emotion-46 {
  width: 100%;
  height: 100%;
  padding: 42px 70px;
  box-sizing: border-box;
  position: absolute;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  display: grid;
  grid-template-columns: repeat(3, minmax(min-content,max-content));
  -webkit-align-content: center;
  -ms-flex-line-pack: center;
  align-content: center;
  grid-template-rows: max-content max-content 1fr;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  justify-content: space-between;
}

.emotion-46 .rteWrapperDivs {
  grid-column: 1;
  grid-row: 1;
}

.emotion-46 .ctaWrapperDivs {
  grid-column: 1;
  grid-row: 2;
  text-align: start;
}

.emotion-47 {
  box-sizing: content-box;
  text-align: start;
  -webkit-align-self: start;
  -ms-flex-item-align: start;
  align-self: start;
  justify-self: start;
}

.emotion-48 {
  text-align: start;
  display: inline-block;
}

.emotion-48 .amp-cms--p {
  padding: 0;
  margin: 0;
  line-height: 0;
}

.emotion-48 .amp-cms--p>span {
  display: inline;
  white-space: break-spaces;
}

.emotion-48 .amp-cms--p a {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  position: relative;
  z-index: 2;
  pointer-events: auto;
}

.emotion-48 .amp-cms--p a:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-48 .amp-cms--legal-copy {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(0.78125vw, 10px));
  line-height: 1.5;
  letter-spacing: min(0.0390625vw, 0.5px);
  font-weight: 500;
}

.emotion-48 sup {
  vertical-align: top;
  display: inline-block;
  margin-top: -0.25ex;
}

.emotion-48 sub {
  vertical-align: bottom;
  display: inline-block;
  margin-bottom: -0.25ex;
}

.emotion-48 .amp-cms--f-0 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.9375vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-48 .amp-cms--f-1 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.9375vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-48 .amp-cms--f-2 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.9375vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-48 .amp-cms--fn-1 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.9375vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-48 .amp-cms--body-1 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(1.40625vw, 18px));
  line-height: 1.5555555555555556;
  letter-spacing: min(0.0703125vw, 0.9px);
  font-weight: 500;
}

.emotion-48 .amp-cms--body-2 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(1.25vw, 16px));
  line-height: 1.25;
  letter-spacing: min(0vw, 0px);
  font-weight: 500;
}

.emotion-48 .amp-cms--body-3 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(1.09375vw, 14px));
  line-height: 1.5714285714285714;
  letter-spacing: min(0.05468749999999999vw, 0.7px);
  font-weight: 500;
}

.emotion-48 .amp-cms--body-4 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(0.9375vw, 12px));
  line-height: 1.5;
  letter-spacing: min(0vw, 0px);
  font-weight: 500;
}

.emotion-48 .amp-cms--body-5 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(0.78125vw, 10px));
  line-height: 1.5;
  letter-spacing: min(0.0390625vw, 0.5px);
  font-weight: 500;
}

.emotion-48 .amp-cms--eyebrow-1 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(1.328125vw, 17px));
  line-height: 1.588235294117647;
  letter-spacing: min(0.1328125vw, 1.7px);
  font-weight: 700;
  text-transform: uppercase;
}

.emotion-48 .amp-cms--eyebrow-2 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(11px, min(1.25vw, 16px));
  line-height: 1.75;
  letter-spacing: min(0.125vw, 1.6px);
  font-weight: 700;
  text-transform: uppercase;
}

.emotion-48 .amp-cms--eyebrow-3 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(0.9375vw, 12px));
  line-height: 1.8333333333333333;
  letter-spacing: min(0.0640625vw, 0.82px);
  font-weight: 700;
  text-transform: uppercase;
}

.emotion-48 .amp-cms--headline-1 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(4.6875vw, 60px));
  line-height: 1;
  letter-spacing: min(0.1875vw, 2.4px);
  text-transform: uppercase;
  font-weight: 500;
}

.emotion-48 .amp-cms--headline-2 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(4.375vw, 56px));
  line-height: 1;
  letter-spacing: min(0.21874999999999997vw, 2.8px);
  text-transform: uppercase;
  font-weight: 500;
}

.emotion-48 .amp-cms--headline-3 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(3.90625vw, 50px));
  line-height: 1;
  letter-spacing: min(0.1953125vw, 2.5px);
  text-transform: uppercase;
  font-weight: 500;
}

.emotion-48 .amp-cms--headline-4 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(3.125vw, 40px));
  line-height: 1;
  letter-spacing: min(0.21874999999999997vw, 2.8px);
  text-transform: uppercase;
  font-weight: 500;
}

.emotion-48 .amp-cms--headline-5 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(2.8125vw, 36px));
  line-height: 1;
  letter-spacing: min(0.28125vw, 3.6px);
  text-transform: uppercase;
  font-weight: 600;
}

.emotion-48 .amp-cms--headline-6 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(2.1875vw, 28px));
  line-height: 1.1428571428571428;
  letter-spacing: min(0.11796875vw, 1.51px);
  text-transform: none;
  font-weight: 600;
}

.emotion-48 .amp-cms--headline-7 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(1.5625vw, 20px));
  line-height: 1.2;
  letter-spacing: min(0.1015625vw, 1.3px);
  text-transform: uppercase;
  font-weight: 700;
}

.emotion-48 .amp-cms--headlineAlt-1 {
  color: #000000;
  font-family: var(--font-soehne),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(9.6875vw, 124px));
  line-height: 1;
  letter-spacing: min(-0.03125vw, -0.4px);
  font-weight: 400;
}

.emotion-48 .amp-cms--headlineAlt-2 {
  color: #000000;
  font-family: var(--font-soehne),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(6.5625vw, 84px));
  line-height: 1;
  letter-spacing: min(-0.03125vw, -0.4px);
  font-weight: 400;
}

.emotion-48 .amp-cms--headlineAlt-3 {
  color: #000000;
  font-family: var(--font-soehne),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(5vw, 64px));
  line-height: 1;
  letter-spacing: min(-0.03125vw, -0.4px);
  font-weight: 400;
}

.emotion-48 .amp-cms--headlineAlt-4 {
  color: #000000;
  font-family: var(--font-soehne),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(2.65625vw, 34px));
  line-height: 1;
  letter-spacing: min(-0.03125vw, -0.4px);
  font-weight: 400;
}

.emotion-48 .amp-cms--headlineAlt-5 {
  color: #000000;
  font-family: var(--font-soehne),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(1.25vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-48 .amp-cms--headlineAlt-6 {
  color: #000000;
  font-family: var(--font-soehne),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(1.25vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-48 .amp-cms--headlineAlt-7 {
  color: #000000;
  font-family: var(--font-soehne),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(1.25vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-48 .amp-cms--promo-1 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(7.8125vw, 100px));
  line-height: 1;
  letter-spacing: min(0vw, 0px);
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-48 .amp-cms--promo-2 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(6.25vw, 80px));
  line-height: 1;
  letter-spacing: min(0vw, 0px);
  font-weight: 600;
}

.emotion-48 .amp-cms--promoAlt-1 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(1.25vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
}

.emotion-48 .amp-cms--subhead-1 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(1.875vw, 24px));
  line-height: 1.5;
  letter-spacing: min(0.09375vw, 1.2px);
  font-weight: 400;
  text-transform: uppercase;
}

.emotion-48 .amp-cms--subhead-2 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(1.40625vw, 18px));
  line-height: 1.5555555555555556;
  letter-spacing: min(0.028124999999999997vw, 0.36px);
  font-weight: 500;
  text-transform: uppercase;
}

.emotion-48 .amp-cms--subhead-3 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(1.09375vw, 14px));
  line-height: 1.1428571428571428;
  letter-spacing: min(0.05468749999999999vw, 0.7px);
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-49 {
  display: block;
  width: 100%;
  position: relative;
  z-index: 2;
  padding-top: 24px;
  text-align: left;
  -webkit-align-self: start;
  -ms-flex-item-align: start;
  align-self: start;
  justify-self: start;
}

<div>
  <div
    style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      class="emotion-0"
    >
      <section
        class="emotion-1"
      >
        <div
          class="emotion-2"
        >
          <nav
            class="emotion-3"
          >
            <div
              class="emotion-4"
            >
              <div
                class="slick-slider slick-initialized"
                dir="ltr"
              >
                <button
                  aria-label="Previous"
                  class="slick-prev slick-arrow slick-prev slick-disabled"
                  data-role="none"
                  disabled=""
                >
                  <span
                    aria-hidden="true"
                    class="emotion-5"
                  >
                    <svg
                      viewBox="0 0 26.78 17.63"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M0 1.95l13 15.68L26.79 1.98 24.53 0 13.08 13.01 2.3.03z"
                        fill="#000000"
                      />
                    </svg>
                  </span>
                </button>
                <div
                  class="slick-list"
                >
                  <div
                    class="slick-track"
                    style="opacity: 1; transform: translate3d(0px, 0px, 0px);"
                  >
                    <div
                      aria-hidden="false"
                      class="slick-slide slick-active slick-current"
                      data-index="0"
                      style="outline: none; width: 0px;"
                      tabindex="-1"
                    >
                      <div>
                        <div
                          class="emotion-6"
                        >
                          <div
                            class="emotion-7"
                          >
                            <div
                              class="emotion-8"
                              height="320"
                              width="640"
                            >
                              <div
                                class="emotion-9"
                                data-testid="product-card-image"
                              >
                                <img
                                  alt="hello lady"
                                  class="emotion-10"
                                  src="https://2hmc8flz4ofg1dy89cc38f2xm.staging.bigcontent.io/i/athleta/530895_012_VIPW_AT_WMN_LS_150_SP20_SW_3_0882?fmt=webp"
                                />
                              </div>
                            </div>
                            <a
                              class="emotion-11"
                              href="/linkToNewArrivals"
                              tabindex="-1"
                              target="_self"
                              title=""
                            />
                            <div
                              class="emotion-12"
                              data-testid="absolute-wrapper"
                            >
                              <div
                                class="emotion-13"
                              >
                                <div
                                  class="rteWrapperDivs emotion-14"
                                >
                                  <div
                                    class="emotion-15"
                                  >
                                    <div>
                                      <p
                                        class="amp-cms--p"
                                      >
                                        <span
                                          class="amp-cms--subhead-1"
                                        >
                                          Lorem Ipsum
                                        </span>
                                      </p>
                                      <p
                                        class="amp-cms--p"
                                      >
                                        <span
                                          class="amp-cms--body-1"
                                        >
                                          dolor sit amet, consectetur adipiscing elit.
                                        </span>
                                      </p>
                                    </div>
                                  </div>
                                </div>
                                <div
                                  class="ctaWrapperDivs emotion-16"
                                >
                                  <a
                                    class="emotion-17"
                                    color="dark"
                                    href="/buyIt"
                                  >
                                    <span
                                      class="emotion-18"
                                    >
                                      new arrivals
                                    </span>
                                  </a>
                                </div>
                              </div>
                            </div>
                          </div>
                          <div
                            class="emotion-7"
                          >
                            <div
                              class="emotion-8"
                              height="320"
                              width="640"
                            >
                              <div
                                class="emotion-9"
                                data-testid="product-card-image"
                              >
                                <img
                                  alt="hello lady"
                                  class="emotion-10"
                                  src="https://2hmc8flz4ofg1dy89cc38f2xm.staging.bigcontent.io/i/athleta/woman-black_hat?fmt=webp"
                                />
                              </div>
                            </div>
                            <div
                              class="emotion-12"
                              data-testid="absolute-wrapper"
                            >
                              <div
                                class="emotion-24"
                              >
                                <div
                                  class="rteWrapperDivs emotion-25"
                                >
                                  <div
                                    class="emotion-26"
                                  >
                                    <div>
                                      <h2
                                        class="amp-cms--p"
                                      >
                                        <span
                                          class="amp-cms--body-1"
                                        >
                                          OUR MOST LOVED STYLES
                                        </span>
                                      </h2>
                                    </div>
                                  </div>
                                </div>
                                <div
                                  class="ctaWrapperDivs emotion-27"
                                >
                                  <a
                                    class="emotion-28"
                                    color="dark"
                                    href="/buyIt"
                                  >
                                    new arrivals
                                  </a>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div
                      aria-hidden="true"
                      class="slick-slide"
                      data-index="1"
                      style="outline: none; width: 0px;"
                      tabindex="-1"
                    >
                      <div>
                        <div
                          class="emotion-6"
                        >
                          <div
                            class="emotion-7"
                          >
                            <div
                              class="emotion-31"
                              height="320"
                              width="1280"
                            >
                              <div
                                class="emotion-32"
                                data-testid="product-card-image"
                              >
                                <img
                                  alt="image only"
                                  class="emotion-33"
                                  src="https://2hmc8flz4ofg1dy89cc38f2xm.staging.bigcontent.io/i/athleta/HOL2_NA_GiftShop_ISM_XL@2x?fmt=webp"
                                />
                              </div>
                            </div>
                            <a
                              class="emotion-11"
                              href="/productsWeLove"
                              tabindex="-1"
                              target="_self"
                              title="Middle link"
                            />
                            <div
                              class="emotion-12"
                              data-testid="absolute-wrapper"
                            >
                              <div
                                class="emotion-36"
                              >
                                <div
                                  class="ctaWrapperDivs emotion-16"
                                >
                                  <a
                                    class="emotion-28"
                                    color="dark"
                                    href="/ss"
                                  >
                                    Get a life
                                  </a>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div
                      aria-hidden="true"
                      class="slick-slide"
                      data-index="2"
                      style="outline: none; width: 0px;"
                      tabindex="-1"
                    >
                      <div>
                        <div
                          class="emotion-6"
                        >
                          <div
                            class="emotion-7"
                          >
                            <div
                              class="emotion-31"
                              height="320"
                              width="1280"
                            >
                              <div
                                class="emotion-32"
                                data-testid="product-card-image"
                              >
                                <img
                                  alt="hi"
                                  class="emotion-33"
                                  src="https://2hmc8flz4ofg1dy89cc38f2xm.staging.bigcontent.io/i/athleta/985360_032_SFMG_AT_WMN_GS_70_HO21_MU_1_8109copy?fmt=webp"
                                />
                              </div>
                            </div>
                            <a
                              class="emotion-11"
                              href="/lastLink"
                              tabindex="-1"
                              target="_self"
                              title="lastLink"
                            />
                            <div
                              class="emotion-12"
                              data-testid="absolute-wrapper"
                            >
                              <div
                                class="emotion-46"
                              >
                                <div
                                  class="rteWrapperDivs emotion-47"
                                >
                                  <div
                                    class="emotion-48"
                                  >
                                    <div>
                                      <p
                                        class="amp-cms--p"
                                      >
                                        <span
                                          class="amp-cms--headline-6"
                                        >
                                          Lorem Ipsum Dolor Sit Amet,
                                        </span>
                                      </p>
                                      <p
                                        class="amp-cms--p"
                                      >
                                        <span
                                          class="amp-cms--body-1"
                                        >
                                          consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam.
                                        </span>
                                      </p>
                                    </div>
                                  </div>
                                </div>
                                <div
                                  class="ctaWrapperDivs emotion-49"
                                >
                                  <a
                                    class="emotion-28"
                                    color="dark"
                                    href="/ss"
                                  >
                                    Get a life
                                  </a>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <button
                  aria-label="Next"
                  class="slick-next slick-arrow slick-next"
                  data-role="none"
                >
                  <span
                    aria-hidden="true"
                    class="emotion-5"
                  >
                    <svg
                      viewBox="0 0 26.78 17.63"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M0 1.95l13 15.68L26.79 1.98 24.53 0 13.08 13.01 2.3.03z"
                        fill="#000000"
                      />
                    </svg>
                  </span>
                </button>
                <ul
                  class="slick-dots"
                  style="display: block;"
                >
                  <li
                    class="slick-active"
                  >
                    <button>
                      1
                    </button>
                  </li>
                  <li
                    class=""
                  >
                    <button>
                      2
                    </button>
                  </li>
                  <li
                    class=""
                  >
                    <button>
                      3
                    </button>
                  </li>
                </ul>
              </div>
            </div>
          </nav>
        </div>
      </section>
    </div>
  </div>
</div>
`;

exports[`CategoryBannerVariableHeightCarousel Athleta Content Type should render isPersistentContent as true 1`] = `
.emotion-0 {
  margin-left: auto;
  margin-right: auto;
  max-width: 1280px;
}

.emotion-2 {
  position: relative;
}

.emotion-3 {
  height: 100%;
  width: 100%;
  position: absolute;
  top: 0;
  left: 0;
}

.emotion-4 {
  width: 100%;
  height: 100%;
  padding: 42px 70px;
  box-sizing: border-box;
  position: absolute;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  display: grid;
  grid-template-columns: repeat(3, minmax(min-content,max-content));
  -webkit-align-content: center;
  -ms-flex-line-pack: center;
  align-content: center;
  grid-template-rows: 1fr max-content 1fr;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  justify-content: space-between;
}

.emotion-4 .rteWrapperDivs {
  grid-column: 2;
  grid-row: 2;
}

.emotion-4 .ctaWrapperDivs {
  grid-column: 2;
  grid-row: 3;
  text-align: center;
}

.emotion-5 {
  box-sizing: content-box;
  text-align: center;
}

.emotion-6 {
  text-align: center;
  display: inline-block;
}

.emotion-6 .amp-cms--p {
  padding: 0;
  margin: 0;
  line-height: 0;
}

.emotion-6 .amp-cms--p>span {
  display: inline;
  white-space: break-spaces;
}

.emotion-6 .amp-cms--p a {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  position: relative;
  z-index: 2;
  pointer-events: auto;
}

.emotion-6 .amp-cms--p a:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-6 .amp-cms--legal-copy {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 10px;
  line-height: 1.5;
  letter-spacing: 0.5px;
  font-weight: 500;
}

.emotion-6 sup {
  vertical-align: top;
  display: inline-block;
  margin-top: -0.25ex;
}

.emotion-6 sub {
  vertical-align: bottom;
  display: inline-block;
  margin-bottom: -0.25ex;
}

.emotion-6 .amp-cms--f-0 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-6 .amp-cms--f-1 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-6 .amp-cms--f-2 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-6 .amp-cms--fn-1 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-6 .amp-cms--body-1 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 18px;
  line-height: 1.5555555555555556;
  letter-spacing: 0.9px;
  font-weight: 500;
}

.emotion-6 .amp-cms--body-2 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 16px;
  line-height: 1.25;
  letter-spacing: 0px;
  font-weight: 500;
}

.emotion-6 .amp-cms--body-3 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 14px;
  line-height: 1.5714285714285714;
  letter-spacing: 0.7px;
  font-weight: 500;
}

.emotion-6 .amp-cms--body-4 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.5;
  letter-spacing: 0px;
  font-weight: 500;
}

.emotion-6 .amp-cms--body-5 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 10px;
  line-height: 1.5;
  letter-spacing: 0.5px;
  font-weight: 500;
}

.emotion-6 .amp-cms--eyebrow-1 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 17px;
  line-height: 1.588235294117647;
  letter-spacing: 1.7px;
  font-weight: 700;
  text-transform: uppercase;
}

.emotion-6 .amp-cms--eyebrow-2 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 16px;
  line-height: 1.75;
  letter-spacing: 1.6px;
  font-weight: 700;
  text-transform: uppercase;
}

.emotion-6 .amp-cms--eyebrow-3 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.8333333333333333;
  letter-spacing: 0.82px;
  font-weight: 700;
  text-transform: uppercase;
}

.emotion-6 .amp-cms--headline-1 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 60px;
  line-height: 1;
  letter-spacing: 2.4px;
  text-transform: uppercase;
  font-weight: 500;
}

.emotion-6 .amp-cms--headline-2 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 56px;
  line-height: 1;
  letter-spacing: 2.8px;
  text-transform: uppercase;
  font-weight: 500;
}

.emotion-6 .amp-cms--headline-3 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 50px;
  line-height: 1;
  letter-spacing: 2.5px;
  text-transform: uppercase;
  font-weight: 500;
}

.emotion-6 .amp-cms--headline-4 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 40px;
  line-height: 1;
  letter-spacing: 2.8px;
  text-transform: uppercase;
  font-weight: 500;
}

.emotion-6 .amp-cms--headline-5 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 36px;
  line-height: 1;
  letter-spacing: 3.6px;
  text-transform: uppercase;
  font-weight: 600;
}

.emotion-6 .amp-cms--headline-6 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 28px;
  line-height: 1.1428571428571428;
  letter-spacing: 1.51px;
  text-transform: none;
  font-weight: 600;
}

.emotion-6 .amp-cms--headline-7 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 20px;
  line-height: 1.2;
  letter-spacing: 1.3px;
  text-transform: uppercase;
  font-weight: 700;
}

.emotion-6 .amp-cms--headlineAlt-1 {
  color: #000000;
  font-family: var(--font-soehne),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 124px;
  line-height: 1;
  letter-spacing: -0.4px;
  font-weight: 400;
}

.emotion-6 .amp-cms--headlineAlt-2 {
  color: #000000;
  font-family: var(--font-soehne),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 84px;
  line-height: 1;
  letter-spacing: -0.4px;
  font-weight: 400;
}

.emotion-6 .amp-cms--headlineAlt-3 {
  color: #000000;
  font-family: var(--font-soehne),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 64px;
  line-height: 1;
  letter-spacing: -0.4px;
  font-weight: 400;
}

.emotion-6 .amp-cms--headlineAlt-4 {
  color: #000000;
  font-family: var(--font-soehne),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 34px;
  line-height: 1;
  letter-spacing: -0.4px;
  font-weight: 400;
}

.emotion-6 .amp-cms--headlineAlt-5 {
  color: #000000;
  font-family: var(--font-soehne),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 16px;
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-6 .amp-cms--headlineAlt-6 {
  color: #000000;
  font-family: var(--font-soehne),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 16px;
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-6 .amp-cms--headlineAlt-7 {
  color: #000000;
  font-family: var(--font-soehne),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 16px;
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-6 .amp-cms--promo-1 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 100px;
  line-height: 1;
  letter-spacing: 0px;
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-6 .amp-cms--promo-2 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 80px;
  line-height: 1;
  letter-spacing: 0px;
  font-weight: 600;
}

.emotion-6 .amp-cms--promoAlt-1 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 16px;
  line-height: 0.0625;
  letter-spacing: 0;
}

.emotion-6 .amp-cms--subhead-1 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 24px;
  line-height: 1.5;
  letter-spacing: 1.2px;
  font-weight: 400;
  text-transform: uppercase;
}

.emotion-6 .amp-cms--subhead-2 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 18px;
  line-height: 1.5555555555555556;
  letter-spacing: 0.36px;
  font-weight: 500;
  text-transform: uppercase;
}

.emotion-6 .amp-cms--subhead-3 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 14px;
  line-height: 1.1428571428571428;
  letter-spacing: 0.7px;
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-7 {
  display: block;
  width: 100%;
  position: relative;
  z-index: 2;
  padding-top: 24px;
  text-align: center;
}

.emotion-8 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: none;
  transition: none;
  box-sizing: border-box;
  border: 1.5px solid transparent;
  border-radius: 0;
  font-size: 18px;
  font-weight: 500;
  letter-spacing: 0px;
  min-height: auto;
  max-height: auto;
  line-height: 1.6666666666666667;
  padding: 0;
  width: auto;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: left;
  border-left: none;
  border-right: none;
  border-radius: 0;
  background-color: transparent;
  border-bottom: 0;
  -webkit-box-pack: left;
  -ms-flex-pack: left;
  -webkit-justify-content: left;
  justify-content: left;
  color: #000000;
  height: auto;
  -webkit-text-decoration: underline;
  text-decoration: underline;
  text-underline-offset: 5px;
  text-decoration-thickness: 1px;
  -webkit-align-self: flex-start;
  -ms-flex-item-align: flex-start;
  align-self: flex-start;
  white-space: normal;
}

.emotion-8:focus {
  outline: none;
}

.emotion-8>span {
  padding: 1px 0;
}

.emotion-8:hover,
.emotion-8:focus {
  text-shadow: 0 0 2px currentColor;
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-8:active {
  text-transform: uppercase;
  text-shadow: none;
  -webkit-text-decoration: underline;
  text-decoration: underline;
}

.emotion-9 {
  box-sizing: border-box;
}

.emotion-10 {
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-10 .slick-list {
  overflow: hidden;
}

.emotion-10 .slick-track {
  width: -webkit-max-content!important;
  width: -moz-max-content!important;
  width: max-content!important;
}

.emotion-10 button.slick-next.slick-arrow.slick-next,
.emotion-10 button.slick-prev.slick-arrow.slick-prev {
  z-index: 2;
  top: min(12.5vw, 50%);
  background: #00000080;
  opacity: 60%;
  height: 44px;
  width: 44px;
}

.emotion-10 button.slick-next.slick-arrow.slick-next span svg path,
.emotion-10 button.slick-prev.slick-arrow.slick-prev span svg path {
  fill: #FFFFFF80;
}

.emotion-10 button.slick-next.slick-arrow.slick-next:hover,
.emotion-10 button.slick-prev.slick-arrow.slick-prev:hover {
  background: #00000080;
}

.emotion-10 button.slick-next.slick-arrow.slick-next:focus,
.emotion-10 button.slick-prev.slick-arrow.slick-prev:focus {
  background: #00000080;
}

.emotion-10 button.slick-next.slick-arrow.slick-next>span,
.emotion-10 button.slick-prev.slick-arrow.slick-prev>span {
  margin: auto;
}

.emotion-10 button.slick-next.slick-arrow.slick-next svg,
.emotion-10 button.slick-prev.slick-arrow.slick-prev svg {
  margin: auto;
  height: 25px;
  width: 14px;
}

.emotion-10 button.slick-next.slick-arrow.slick-next:focus-visible,
.emotion-10 button.slick-prev.slick-arrow.slick-prev:focus-visible {
  outline: auto;
}

.emotion-10 .slick-slide {
  margin: 0;
  width: auto;
}

.emotion-10 .slick-disabled {
  display: none!important;
}

.emotion-10 .slick-next {
  left: calc(100% - 44px);
}

.emotion-10 .slick-slide:first-of-type a>div:nth-of-type(2)>div>div:nth-of-type(2) {
  border-left: none;
}

.emotion-10 .slick-slide>div:first-of-type {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-10 .slick-slide>div:first-of-type>div:first-of-type {
  width: 100%;
}

.emotion-11 {
  position: relative;
}

.emotion-11 .slick-slider {
  position: relative;
  display: block;
  box-sizing: border-box;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  -webkit-touch-callout: none;
  touch-action: pan-y;
  -webkit-tap-highlight-color: transparent;
  scroll-behavior: smooth;
}

.emotion-11 .slick-slider .slick-track,
.emotion-11 .slick-slider .slick-list {
  -webkit-transform: translate3d(0, 0, 0);
  -moz-transform: translate3d(0, 0, 0);
  -ms-transform: translate3d(0, 0, 0);
  transform: translate3d(0, 0, 0);
}

.emotion-11 .slick-list {
  position: relative;
  display: block;
  overflow-x: hidden;
  overflow-y: hidden;
  margin: 0;
  padding: 0;
}

.emotion-11 .slick-list:focus {
  outline: none;
}

.emotion-11 .slick-list.dragging {
  cursor: pointer;
  cursor: hand;
}

.emotion-11 .slick-track {
  position: relative;
  top: 0;
  left: 0;
  display: block;
  margin-left: auto;
  margin-right: auto;
}

.emotion-11 .slick-track:before,
.emotion-11 .slick-track:after {
  display: table;
  content: "";
}

.emotion-11 .slick-track:after {
  clear: both;
}

.emotion-11 .slick-slide {
  display: none;
  float: left;
  height: 100%;
  min-height: 1px;
}

.emotion-11 .slick-slide img {
  display: block;
}

.emotion-11 .slick-slide.slick-loading img {
  display: none;
}

.emotion-11 .slick-slide.dragging img {
  pointer-events: none;
}

.emotion-11 [dir="rtl"] .slick-slide {
  float: right;
}

.emotion-11 .slick-initialized .slick-slide,
.emotion-11 .slick-vertical .slick-slide {
  display: block;
}

.emotion-11 .slick-vertical .slick-slide {
  border: 1px solid transparent;
  height: auto;
}

.emotion-11 .slick-loading .slick-track,
.emotion-11 .slick-loading .slick-slide {
  visibility: hidden;
}

.emotion-11 .slick-loading .slick-list {
  background: #fff url("./ajax-loader.gif") center center no-repeat;
}

.emotion-11 .slick-arrow.slick-hidden {
  display: none;
}

.emotion-11 .slick-prev,
.emotion-11 .slick-next {
  font-size: 0;
  line-height: 0;
  position: absolute;
  top: 50%;
  display: block;
  padding: 0;
  cursor: pointer;
  color: transparent;
  border: none;
  outline: none;
  background: transparent;
}

.emotion-11 .slick-prev:hover,
.emotion-11 .slick-next:hover,
.emotion-11 .slick-prev:focus,
.emotion-11 .slick-next:focus {
  color: transparent;
  outline: none;
  background: transparent;
  opacity: 1;
}

.emotion-11 .slick-prev.slick-disabled,
.emotion-11 .slick-next.slick-disabled {
  opacity: 0.25;
}

.emotion-11 .slick-prev {
  left: -0;
  -webkit-transform: translate(0, -50%) rotate(90deg);
  -moz-transform: translate(0, -50%) rotate(90deg);
  -ms-transform: translate(0, -50%) rotate(90deg);
  transform: translate(0, -50%) rotate(90deg);
}

.emotion-11 .slick-prev span {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-11 [dir="rtl"] .slick-prev {
  right: -0;
  left: auto;
}

.emotion-11 .slick-next {
  right: -0;
  -webkit-transform: translate(0, -50%) rotate(-90deg);
  -moz-transform: translate(0, -50%) rotate(-90deg);
  -ms-transform: translate(0, -50%) rotate(-90deg);
  transform: translate(0, -50%) rotate(-90deg);
}

.emotion-11 .slick-next span {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-11 [dir="rtl"] .slick-next {
  right: auto;
  left: -0;
}

.emotion-11 .slick-dotted.slick-slider {
  margin-bottom: 30px;
}

.emotion-11 .slick-dots {
  position: absolute;
  bottom: -25px;
  display: block;
  width: 100%;
  padding: 0;
  margin: 0;
  list-style: none;
  text-align: center;
}

.emotion-11 .slick-dots li {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: 20px;
  height: 20px;
  margin: 0 5px;
  padding: 0;
  cursor: pointer;
}

.emotion-11 .slick-dots li.slick-active button:before {
  opacity: 0.75;
  color: black;
}

.emotion-11 .slick-dots li button {
  font-size: 0;
  line-height: 0;
  display: block;
  padding: 5px;
  cursor: pointer;
  color: transparent;
  border: 0;
  outline: none;
  background: transparent;
}

.emotion-11 .slick-dots li button:hover,
.emotion-11 .slick-dots li button:focus {
  outline: none;
}

.emotion-11 .slick-dots li button:hover:before,
.emotion-11 .slick-dots li button:focus:before,
.emotion-11 .slick-dots li button:hover:before,
.emotion-11 .slick-dots li button:focus:before {
  opacity: 1;
}

.emotion-11 .slick-dots li button:before {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 5px;
  height: 5px;
  content: "";
  opacity: 0.25;
  background-color: black;
  border-radius: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-12 {
  display: inline-block;
  height: 2rem;
  width: 2rem;
  min-height: 2rem;
  min-width: 2rem;
}

.emotion-12 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-13 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-14 {
  width: 100%;
  position: relative;
}

.emotion-15 {
  overflow: hidden;
  position: relative;
  box-sizing: border-box;
  aspect-ratio: 640/320;
}

.emotion-16 {
  -webkit-background-size: cover;
  background-size: cover;
  aspect-ratio: 640/320;
  overflow: hidden;
}

.emotion-17 {
  width: 100%;
  aspect-ratio: 640/320;
  object-fit: cover;
}

.emotion-18 {
  width: 100%;
  height: 100%;
  position: absolute;
  left: 0;
  top: 0;
  z-index: 1;
}

.emotion-25 {
  overflow: hidden;
  position: relative;
  box-sizing: border-box;
  aspect-ratio: 1280/320;
}

.emotion-26 {
  -webkit-background-size: cover;
  background-size: cover;
  aspect-ratio: 1280/320;
  overflow: hidden;
}

.emotion-27 {
  width: 100%;
  aspect-ratio: 1280/320;
  object-fit: cover;
}

<div>
  <div
    style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      class="emotion-0"
    >
      <section
        class="emotion-1"
      >
        <div
          class="emotion-2"
        >
          <div
            class="persistentDiv"
          >
            <div
              class="emotion-3"
            >
              <div
                class="emotion-4"
              >
                <div
                  class="rteWrapperDivs emotion-5"
                >
                  <div
                    class="emotion-6"
                  >
                    <div>
                      <p
                        class="amp-cms--p"
                      >
                        <span
                          class="amp-cms--subhead-1"
                        >
                          Lorem Ipsum
                        </span>
                      </p>
                      <p
                        class="amp-cms--p"
                      >
                        <span
                          class="amp-cms--body-1"
                        >
                          dolor sit amet, consectetur adipiscing elit.
                        </span>
                      </p>
                    </div>
                  </div>
                </div>
                <div
                  class="ctaWrapperDivs emotion-7"
                >
                  <a
                    class="emotion-8"
                    color="dark"
                    href="/buyIt"
                  >
                    <span
                      class="emotion-9"
                    >
                      new arrivals
                    </span>
                  </a>
                </div>
              </div>
            </div>
          </div>
          <nav
            class="emotion-10"
          >
            <div
              class="emotion-11"
            >
              <div
                class="slick-slider slick-initialized"
                dir="ltr"
              >
                <button
                  aria-label="Previous"
                  class="slick-prev slick-arrow slick-prev slick-disabled"
                  data-role="none"
                  disabled=""
                >
                  <span
                    aria-hidden="true"
                    class="emotion-12"
                  >
                    <svg
                      viewBox="0 0 26.78 17.63"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M0 1.95l13 15.68L26.79 1.98 24.53 0 13.08 13.01 2.3.03z"
                        fill="#000000"
                      />
                    </svg>
                  </span>
                </button>
                <div
                  class="slick-list"
                >
                  <div
                    class="slick-track"
                    style="opacity: 1; transform: translate3d(0px, 0px, 0px);"
                  >
                    <div
                      aria-hidden="false"
                      class="slick-slide slick-active slick-current"
                      data-index="0"
                      style="outline: none; width: 0px;"
                      tabindex="-1"
                    >
                      <div>
                        <div
                          class="emotion-13"
                        >
                          <div
                            class="emotion-14"
                          >
                            <div
                              class="emotion-15"
                              height="320"
                              width="640"
                            >
                              <div
                                class="emotion-16"
                                data-testid="product-card-image"
                              >
                                <img
                                  alt="hello lady"
                                  class="emotion-17"
                                  src="https://2hmc8flz4ofg1dy89cc38f2xm.staging.bigcontent.io/i/athleta/530895_012_VIPW_AT_WMN_LS_150_SP20_SW_3_0882?fmt=webp"
                                />
                              </div>
                            </div>
                            <a
                              class="emotion-18"
                              href="/linkToNewArrivals"
                              tabindex="-1"
                              target="_self"
                              title=""
                            />
                          </div>
                          <div
                            class="emotion-14"
                          >
                            <div
                              class="emotion-15"
                              height="320"
                              width="640"
                            >
                              <div
                                class="emotion-16"
                                data-testid="product-card-image"
                              >
                                <img
                                  alt="hello lady"
                                  class="emotion-17"
                                  src="https://2hmc8flz4ofg1dy89cc38f2xm.staging.bigcontent.io/i/athleta/woman-black_hat?fmt=webp"
                                />
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div
                      aria-hidden="true"
                      class="slick-slide"
                      data-index="1"
                      style="outline: none; width: 0px;"
                      tabindex="-1"
                    >
                      <div>
                        <div
                          class="emotion-13"
                        >
                          <div
                            class="emotion-14"
                          >
                            <div
                              class="emotion-25"
                              height="320"
                              width="1280"
                            >
                              <div
                                class="emotion-26"
                                data-testid="product-card-image"
                              >
                                <img
                                  alt="image only"
                                  class="emotion-27"
                                  src="https://2hmc8flz4ofg1dy89cc38f2xm.staging.bigcontent.io/i/athleta/HOL2_NA_GiftShop_ISM_XL@2x?fmt=webp"
                                />
                              </div>
                            </div>
                            <a
                              class="emotion-18"
                              href="/productsWeLove"
                              tabindex="-1"
                              target="_self"
                              title="Middle link"
                            />
                          </div>
                        </div>
                      </div>
                    </div>
                    <div
                      aria-hidden="true"
                      class="slick-slide"
                      data-index="2"
                      style="outline: none; width: 0px;"
                      tabindex="-1"
                    >
                      <div>
                        <div
                          class="emotion-13"
                        >
                          <div
                            class="emotion-14"
                          >
                            <div
                              class="emotion-25"
                              height="320"
                              width="1280"
                            >
                              <div
                                class="emotion-26"
                                data-testid="product-card-image"
                              >
                                <img
                                  alt="hi"
                                  class="emotion-27"
                                  src="https://2hmc8flz4ofg1dy89cc38f2xm.staging.bigcontent.io/i/athleta/985360_032_SFMG_AT_WMN_GS_70_HO21_MU_1_8109copy?fmt=webp"
                                />
                              </div>
                            </div>
                            <a
                              class="emotion-18"
                              href="/lastLink"
                              tabindex="-1"
                              target="_self"
                              title="lastLink"
                            />
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <button
                  aria-label="Next"
                  class="slick-next slick-arrow slick-next"
                  data-role="none"
                >
                  <span
                    aria-hidden="true"
                    class="emotion-12"
                  >
                    <svg
                      viewBox="0 0 26.78 17.63"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M0 1.95l13 15.68L26.79 1.98 24.53 0 13.08 13.01 2.3.03z"
                        fill="#000000"
                      />
                    </svg>
                  </span>
                </button>
                <ul
                  class="slick-dots"
                  style="display: block;"
                >
                  <li
                    class="slick-active"
                  >
                    <button>
                      1
                    </button>
                  </li>
                  <li
                    class=""
                  >
                    <button>
                      2
                    </button>
                  </li>
                  <li
                    class=""
                  >
                    <button>
                      3
                    </button>
                  </li>
                </ul>
              </div>
            </div>
          </nav>
        </div>
      </section>
    </div>
  </div>
</div>
`;
