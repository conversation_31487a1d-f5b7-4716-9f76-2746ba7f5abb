import React from 'react';
import { render } from 'test-utils';
// @ts-ignore
import { Size, SMALL, XLARGE } from '@ecom-next/core/breakpoint-provider';
// @ts-ignore
import { Brands } from '@ecom-next/core/react-stitch';
import { LocalizationTestWrapper } from '../../../../subcomponents/LocalizationTestWrapper/index';
import { cbvhCarouselBaseDataContentData as defaultProps } from '../../__fixtures__/test-data';
import { CategoryBannerVHCarouselProps } from '../../../../components/CategoryBannerVHCarousel/index';
import { AT_ON_MAX_WIDTH_PX } from '../../../../subcomponents/ComponentMaxWidth/index';
import CategoryBannerVHCarousel from './CategoryBannerVariableHeightCarousel.at';

const getParentContainer = (container: HTMLElement): HTMLElement => (container?.firstChild?.firstChild as HTMLElement) || null;

interface RenderComponentOptions {
  brand?: Brands;
  breakpoint?: Size;
  enabledFeatures?: Record<string, boolean>;
  props?: Partial<CategoryBannerVHCarouselProps>;
}

const renderComponent = (
  props: RenderComponentOptions['props'] = {},
  brand: RenderComponentOptions['brand'] = Brands.Athleta,
  breakpoint: RenderComponentOptions['breakpoint'] = XLARGE,
  enabledFeatures?: RenderComponentOptions['enabledFeatures']
) => {
  const result = render(
    <LocalizationTestWrapper>
      <CategoryBannerVHCarousel {...defaultProps} {...props} />
    </LocalizationTestWrapper>,
    {
      // @ts-ignore
      enabledFeatures,
      breakpoint,
      appState: {
        brandName: brand,
      } as any,
    }
  );

  return result;
};

describe('AbsoluteWrapper', () => {
  it('should render tile content on image', () => {
    const { getAllByTestId } = renderComponent({ mobileTextTreatment: 'on' }, Brands.Athleta, SMALL);
    const absoluteWrapper = getAllByTestId('absolute-wrapper')[0];
    expect(absoluteWrapper).toHaveStyleRules({
      position: 'absolute',
      height: '100%',
    });
  });

  it('should render tile content below image', () => {
    const { container } = renderComponent({ mobileTextTreatment: 'below' }, Brands.Athleta, SMALL);
    expect(container).toMatchSnapshot();
  });
});

describe('CategoryBannerVariableHeightCarousel Athleta Content Type', () => {
  it('should match snapshot for Brands.Athleta', () => {
    const { container } = renderComponent();
    expect(container).toMatchSnapshot();
  });

  it(`should have a max-width of ${AT_ON_MAX_WIDTH_PX} on parent carousel wrapper`, () => {
    const { container } = renderComponent();
    expect(getParentContainer(container)).toHaveStyleRules({
      'max-width': `${AT_ON_MAX_WIDTH_PX}px`,
    });
  });
  it(`should render  with ComponentMaxWidth when newPlpGridFeatureFlag is false`, () => {
    const { container } = renderComponent({ mobileTextTreatment: 'on' }, Brands.Athleta, SMALL, { 'mui-new-plp-grid-2025': false });
    expect(getParentContainer(container)).toHaveStyleRules({
      'max-width': `${AT_ON_MAX_WIDTH_PX}px`,
    });
  });
  it(`should render without ComponentMaxWidth when newPlpGridFeatureFlag is true`, () => {
    const { container } = renderComponent({ mobileTextTreatment: 'on' }, Brands.Athleta, SMALL, { 'mui-new-plp-grid-2025': true });
    expect(getParentContainer(container)).not.toHaveStyleRules({
      'max-width': `${AT_ON_MAX_WIDTH_PX}px`,
    });
  });

  it('should render a child components per tile', () => {
    const { container } = renderComponent(defaultProps, Brands.Athleta, XLARGE);
    const flexWrapper = container.querySelectorAll('.slick-active')[0]?.firstChild?.firstChild as HTMLElement;
    expect(flexWrapper.children?.length).toBe(defaultProps.frames[0].tiles.length);
  });

  it('should render null', () => {
    const { container } = renderComponent({ frames: [] }, Brands.Athleta, XLARGE);
    expect(container?.querySelectorAll('nav').length).toBe(0);
  });

  it('should render isPersistentContent as true', () => {
    const { container } = renderComponent({ contentConfiguration: true }, Brands.Athleta, XLARGE);
    expect(container).toMatchSnapshot();
  });
});
