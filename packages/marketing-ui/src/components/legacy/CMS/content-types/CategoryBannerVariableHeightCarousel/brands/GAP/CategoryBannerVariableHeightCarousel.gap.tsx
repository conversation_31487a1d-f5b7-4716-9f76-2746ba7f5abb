'use client';
import React, { useState } from 'react';
// @ts-ignore
import { Brands, styled, useTheme, useEnabledFeatures } from '@ecom-next/core/react-stitch';

import { useViewportIsLarge } from '../../../../../hooks/useViewportIsLarge/index';
import { useShowHideBasedOnScreenSize } from '../../../../../hooks/useShowHideBasedOnScreenSize/index';
import { CategoryBannerVHCarouselPropsGap } from '../../types';

import { useCBVHCenterPosition } from '../../../../components/CategoryBannerVHCarousel/hooks/useCBVHCenterPosition/index';
import { StaticTile, CBVHCarouselWrapper, Tile } from '../../../../components/CategoryBannerVHCarousel/index';
import { heightMap } from '../../../../components/CategoryBannerVariableHeight/styles';
import useMinScale from '../../../../components/VisualNavigationCarousel/hooks/useMinScale';

import { CMSMarketingCarousel } from '../../../../subcomponents/CMSMarketingCarousel/index';
import { ComponentMaxWidth } from '../../../../subcomponents/ComponentMaxWidth/index';
import CustomPlayPauseButton from '../../../../subcomponents/PlayPauseButton/index';

import CategoryBannerVHCarouselTile from './CategoryBannerVHCarouselTile.gap';

interface PaginationWrapperProps {
  controlsIconsColor: CategoryBannerVHCarouselPropsGap['carouselSettings']['styling']['controlsIconsColor'];
  desktopBannerSize: CategoryBannerVHCarouselPropsGap['desktopBannerSize'];
  isPersistentContent?: boolean;
  mobileBannerSize: CategoryBannerVHCarouselPropsGap['mobileBannerSize'];
  mobileTextTreatment: CategoryBannerVHCarouselPropsGap['mobileTextTreatment'];
}

const PLAY_PAUSE_ICON_HEIGHT_PX = 44;
const PLAY_PAUSE_ICON_WIDTH_PX = 44;

const PaginationWrapper = styled.div<PaginationWrapperProps>(
  ({ desktopBannerSize, isPersistentContent, mobileTextTreatment, mobileBannerSize, controlsIconsColor }) => {
    const isDesktop: boolean = useViewportIsLarge();
    const brandName = Brands.Gap || Brands.GapFactoryStore;
    const viewport = isDesktop ? 'desktop' : 'mobile';
    const bannerSize = isDesktop ? desktopBannerSize : mobileBannerSize;
    const HEIGHT_MAX_SIZE: number = heightMap.categoryBanner[viewport][bannerSize][brandName];
    const isBelow: boolean = mobileTextTreatment.includes('below');
    const isPrimary: boolean = controlsIconsColor?.includes('primary') ?? false;
    const positionCompensationOnMobilePagination: number = (!isDesktop && isBelow && !isPersistentContent && -5) || 0;

    const bottomValue = useMinScale(HEIGHT_MAX_SIZE - positionCompensationOnMobilePagination - PLAY_PAUSE_ICON_HEIGHT_PX);
    const bottomValuePlayPauseBtn = useMinScale(HEIGHT_MAX_SIZE - PLAY_PAUSE_ICON_HEIGHT_PX);

    const topPosition = {
      top: !isDesktop && isBelow && !isPersistentContent ? bottomValue : 'inherit',
    };
    const topPositionPlayPause = {
      top: !isDesktop && isBelow && !isPersistentContent ? bottomValuePlayPauseBtn : 'inherit',
    };

    const bottomPosition = {
      bottom: !isDesktop && isBelow && !isPersistentContent ? 'inherit' : 0,
    };

    const theme = useTheme();
    return {
      'ul.slick-dots': {
        marginBlockStart: 0,
        marginBlockEnd: 0,
        paddingInlineStart: 0,
        textAlign: 'right',
        ...topPosition,
        ...bottomPosition,
        zIndex: 2,
        width: '80%',
        right: 0,
        marginRight: isDesktop ? '20px' : '15px',
        marginBottom: isDesktop ? '20px' : '15px',
        li: {
          width: 'auto',
          height: 'auto',
          margin: 0,
        },
        'li:not(li:first-child)': {
          marginLeft: '10px',
        },
        'li button:before': {
          color: isPrimary ? theme.color.b1 : theme.color.wh,
          width: '10px',
          height: '10px',
          opacity: '1',
          border: `1px solid ${isPrimary ? theme.color.b1 : theme.color.wh}`,
          borderRadius: '50%',
          content: '""',
        },
        'li.slick-active button:before': {
          color: isPrimary ? theme.color.b1 : theme.color.wh,
          backgroundColor: isPrimary ? theme.color.b1 : theme.color.wh,
        },
      },
      'button.pausePlayGapBtn': {
        ...topPositionPlayPause,
        ...bottomPosition,
      },
    };
  }
);

const CategoryBannerVHCarousel = (props: CategoryBannerVHCarouselPropsGap): JSX.Element | null => {
  const { frames, contentConfiguration, desktopBannerSize, mobileBannerSize, webAppearance, mobileTextTreatment, carouselSettings } = props;

  const { showHideBasedOnScreenSize } = webAppearance;
  const numberOfCarouselFrames = frames.length;
  const isDesktop: boolean = useViewportIsLarge();
  const isPersistentContent = contentConfiguration;
  const showCarouselOnViewPort = useShowHideBasedOnScreenSize(showHideBasedOnScreenSize);
  const arrowPosition = useCBVHCenterPosition(desktopBannerSize, mobileBannerSize);

  const getFramesTiles: Tile[][] = frames.reduce(
    (frameTiles: Tile[][], { tiles }) => [...frameTiles, ...(!isDesktop ? tiles.map(tile => [tile]) : [tiles])],
    []
  );
  const arrowPositionValue = `min(${arrowPosition}vw, 50%)`;

  const [hover, setHover] = useState(false);
  const [top, setTop] = useState<number | undefined>(undefined);
  const handleHover = (): void => setHover(!hover);
  const imageHeightHandler = (height: number) => {
    if (mobileTextTreatment === 'below') {
      setTop(height - PLAY_PAUSE_ICON_HEIGHT_PX);
    } else {
      setTop(undefined);
    }
  };
  const isMobilePersistentAndBelow = mobileTextTreatment.includes('below') && isPersistentContent && !isDesktop;

  if (!showCarouselOnViewPort || numberOfCarouselFrames < 2) return null;

  return (
    <CBVHCarouselWrapper>
      {isPersistentContent && !isMobilePersistentAndBelow && (
        <div onMouseEnter={handleHover} onMouseLeave={handleHover}>
          <StaticTile tile={frames[0].tiles[0]} />
        </div>
      )}
      <CMSMarketingCarousel
        arrowPosition={arrowPositionValue}
        carouselSettings={carouselSettings}
        customPlayPauseButton={
          <CustomPlayPauseButton
            className='pausePlayGapBtn'
            css={{
              height: PLAY_PAUSE_ICON_HEIGHT_PX,
              width: PLAY_PAUSE_ICON_WIDTH_PX,
              '>span': {
                display: 'inline-block',
              },
            }}
            variant={carouselSettings.styling.controlsIconsColor}
          />
        }
        forceHover={hover}
      >
        {getFramesTiles.map((tiles: Tile[], index: number) => {
          const key = `${index}_${tiles.map(tile => tile.bannerLink?.value || tile.text).join(':')}`;
          return (
            <CategoryBannerVHCarouselTile
              key={key}
              desktopBannerSize={desktopBannerSize}
              mobileBannerSize={mobileBannerSize}
              mobileTextTreatment={mobileTextTreatment}
              onImageHeightChange={imageHeightHandler}
              showContent={!isPersistentContent}
              tiles={tiles}
            />
          );
        })}
      </CMSMarketingCarousel>
      {isMobilePersistentAndBelow && (
        <div onMouseEnter={handleHover} onMouseLeave={handleHover}>
          <StaticTile isMobilePersistentAndBelow={isMobilePersistentAndBelow} tile={frames[0].tiles[0]} />
        </div>
      )}
    </CBVHCarouselWrapper>
  );
};

const CategoryBannerVHCarouselWithMaxWidth = (props: CategoryBannerVHCarouselPropsGap): JSX.Element | null => {
  const { carouselSettings, contentConfiguration } = props;
  const { controlsIconsColor } = carouselSettings.styling;
  const { mobileTextTreatment, mobileBannerSize, desktopBannerSize } = props;

  const enabledFeatures = useEnabledFeatures();
  const newPlpGridFeatureFlag = !!enabledFeatures?.['mui-new-plp-grid-2025'];

  return (
    <PaginationWrapper
      controlsIconsColor={controlsIconsColor}
      desktopBannerSize={desktopBannerSize}
      isPersistentContent={contentConfiguration}
      mobileBannerSize={mobileBannerSize}
      mobileTextTreatment={mobileTextTreatment}
    >
      {newPlpGridFeatureFlag ? (
        <CategoryBannerVHCarousel {...props} />
      ) : (
        <ComponentMaxWidth>
          <CategoryBannerVHCarousel {...props} />
        </ComponentMaxWidth>
      )}
    </PaginationWrapper>
  );
};

export default CategoryBannerVHCarouselWithMaxWidth;
