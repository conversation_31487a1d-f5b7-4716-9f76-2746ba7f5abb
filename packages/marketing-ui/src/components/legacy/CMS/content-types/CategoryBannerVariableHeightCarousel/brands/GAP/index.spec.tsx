import React from 'react';
import { render, screen, cleanup } from 'test-utils';
// @ts-ignore
import { Size, SMALL, LARGE, XLARGE } from '@ecom-next/core/breakpoint-provider';
// @ts-ignore
import { Brands } from '@ecom-next/core/react-stitch';
import { LocalizationTestWrapper } from '../../../../subcomponents/LocalizationTestWrapper/index';
import { cbvhCarouselBaseDataContentData as defaultProps } from '../../__fixtures__/test-data';
import { CategoryBannerVHCarouselPropsGap } from '../../types';
import CategoryBannerVHCarousel from './CategoryBannerVariableHeightCarousel.gap';

interface RenderComponentOptions {
  brand?: Brands;
  breakpoint?: Size;
  enabledFeatures?: Record<string, boolean | object>;
  props?: Partial<CategoryBannerVHCarouselPropsGap>;
}

const renderComponent = (
  props: RenderComponentOptions['props'] = {},
  brand: RenderComponentOptions['brand'] = Brands.Gap,
  breakpoint: RenderComponentOptions['breakpoint'] = XLARGE,
  enabledFeatures?: RenderComponentOptions['enabledFeatures']
) => {
  const result = render(
    <LocalizationTestWrapper>
      <CategoryBannerVHCarousel {...defaultProps} {...props} />
    </LocalizationTestWrapper>,
    {
      // @ts-ignore
      enabledFeatures,
      breakpoint,
      appState: {
        brandName: brand,
      } as any,
    }
  );

  return result;
};

const getAbsoluteWrapper = (container: HTMLElement) => container.querySelectorAll('.slick-slide div div div div div')[0];

describe('AbsoluteWrapper', () => {
  it('should render tile content on image', () => {
    renderComponent({ mobileTextTreatment: 'on', contentConfiguration: true }, Brands.Gap, SMALL);
    const ctaButtonName = defaultProps.frames[0].tiles[0].ctaButton?.label;
    const absoluteWrapper = screen.getByRole('link', { name: ctaButtonName })!.parentElement!.parentElement!.parentElement!;
    expect(absoluteWrapper).toHaveStyleRules({
      position: 'absolute',
      height: '100%',
    });
  });

  it('should render tile content below image', () => {
    const { container } = renderComponent({ mobileTextTreatment: 'below' }, Brands.Gap, SMALL);
    expect(container).toMatchSnapshot();
  });
});

describe('CategoryBannerVariableHeightCarousel Gap Content Type', () => {
  it('should render a child components per tile', () => {
    const { container } = renderComponent({}, Brands.Gap, XLARGE);
    const flexWrapper = container.querySelectorAll('.slick-active')[0]?.firstChild?.firstChild as HTMLElement;
    expect(flexWrapper.children?.length).toBe(defaultProps.frames[0].tiles.length);
  });

  it('should render null', () => {
    const { container } = renderComponent({ frames: [] }, Brands.Gap, XLARGE);
    expect(container?.querySelectorAll('nav').length).toBe(0);
  });

  it('should render isPersistentContent as true', () => {
    const { container } = renderComponent({ contentConfiguration: true }, Brands.Gap, XLARGE);
    expect(container).toMatchSnapshot();
  });
});

describe('On mobile', () => {
  it('should match snapshot', () => {
    const { container } = renderComponent({}, Brands.Gap, SMALL);
    expect(container).toMatchSnapshot();
  });

  it('should render tiles on mobile', () => {
    const { container } = renderComponent({ contentConfiguration: true }, Brands.Gap, SMALL);
    expect(container).toMatchSnapshot();
  });

  it('should render component if persistent', () => {
    const { container } = renderComponent({ contentConfiguration: true }, Brands.Gap, SMALL);
    expect(container).toMatchSnapshot();
  });
});

describe('CategoryBannerVHCarousel Snaps', () => {
  it('should render a default CategoryBannerVHCarousel', () => {
    const result = renderComponent({}, Brands.Gap, LARGE);
    expect(result).toMatchSnapshot();
  });

  it('should render secondary color for carousel desktop', () => {
    const { asFragment } = renderComponent(
      {
        carouselSettings: {
          transition: 'slide',
          type: 'clickThrough',
          continuousLoop: false,
          autoplay: {
            delay: 3000,
            pauseOnHover: false,
          },
          animation: {
            speed: 500,
            ease: false,
          },
          styling: {
            controlsIconsColor: 'secondary',
            pagination: 'desktop',
            hideChevrons: false,
          },
        },
      },
      Brands.Gap,
      LARGE
    );
    expect(asFragment()).toMatchSnapshot();
  });

  it('should match snapshot for persistent below on mobile', () => {
    const { asFragment } = renderComponent({ contentConfiguration: true, mobileTextTreatment: 'below' }, Brands.Gap, SMALL);
    expect(asFragment()).toMatchSnapshot();
  });

  it('should match snapshot for persistent below on mobile with pagination and play btn', () => {
    const { asFragment, container } = renderComponent(
      {
        contentConfiguration: true,
        mobileTextTreatment: 'below',
        carouselSettings: {
          transition: 'slide',
          type: 'autoplay',
          continuousLoop: false,
          autoplay: {
            delay: 3000,
            pauseOnHover: false,
          },
          animation: {
            speed: 500,
            ease: false,
          },
          styling: {
            controlsIconsColor: 'secondary',
            pagination: 'desktopAndMobile',
            hideChevrons: false,
          },
        },
      },
      Brands.Gap,
      SMALL
    );
    expect(asFragment()).toMatchSnapshot();
    const pausePlayGapBtn = screen.getByLabelText('pause');
    expect(pausePlayGapBtn).toHaveStyleRules({ height: '44px', width: '44px' });
  });
});

describe('Banners', () => {
  describe('Banner size tile desktop', () => {
    const firstImgAltText = defaultProps.frames[0].tiles[0].backgroundImage![0].altText!;
    it('should render small tile banner size with height of 365px', () => {
      renderComponent({ desktopBannerSize: 'small' }, Brands.Gap, LARGE);
      const image = screen.getAllByAltText(firstImgAltText);
      expect(image[0]).toHaveStyleRule('aspect-ratio', '640/365');
      expect(image[0]).toMatchSnapshot();
    });

    it('should render medium tile banner size with height of 400px', () => {
      renderComponent({ desktopBannerSize: 'medium' }, Brands.Gap, LARGE);

      const image = screen.getAllByAltText(firstImgAltText);
      expect(image[0]).toHaveStyleRule('aspect-ratio', '640/400');
      expect(image[0]).toMatchSnapshot();
    });

    it('should render large tile banner size with height of 575px', () => {
      renderComponent({ desktopBannerSize: 'large' }, Brands.Gap, LARGE);

      const image = screen.getAllByAltText(firstImgAltText);
      expect(image[0]).toHaveStyleRule('aspect-ratio', '640/575');
      expect(image[0]).toMatchSnapshot();
    });
  });

  describe('Banner size tile mobile', () => {
    it('should render small tile banner size with height of 200px', () => {
      renderComponent({ mobileBannerSize: 'small' }, Brands.Gap, SMALL);
      const image = screen.getByRole('img');
      expect(image).toHaveStyleRule('aspect-ratio', '375/200');
      expect(image).toMatchSnapshot();
    });

    it('should render medium tile banner size with height of 235px', () => {
      renderComponent({ mobileBannerSize: 'medium' }, Brands.Gap, SMALL);
      const image = screen.getByRole('img');
      expect(image).toHaveStyleRule('aspect-ratio', '375/235');
      expect(image).toMatchSnapshot();
    });

    it('should render large tile banner size with height of 450px', () => {
      renderComponent({ mobileBannerSize: 'large' }, Brands.Gap, SMALL);
      const image = screen.getByRole('img');
      expect(image).toHaveStyleRule('aspect-ratio', '375/450');
      expect(image).toMatchSnapshot();
    });
  });

  describe('Snapshots', () => {
    cleanup();
    it('should match desktop snapshots', () => {
      const { container } = renderComponent({}, Brands.Gap, XLARGE);
      expect(container).toMatchSnapshot();
    });
    it('should match mobile snapshots', () => {
      const { container } = renderComponent({}, Brands.Gap, SMALL);
      expect(container).toMatchSnapshot();
    });
  });
  describe('CategoryBannerVariableHeight - Feature Flag: newPlpGridFeatureFlag', () => {
    it('should match snapshot when newPlpGridFeatureFlag is enabled', () => {
      const enabledFeatures = { 'mui-new-plp-grid-2025': true };
      const { container } = renderComponent({}, Brands.Gap, XLARGE, { enabledFeatures });
      expect(container).toMatchSnapshot();
    });

    it('should match snapshot when newPlpGridFeatureFlag is disabled', () => {
      const enabledFeatures = { 'mui-new-plp-grid-2025': false };
      const { container } = renderComponent({}, Brands.Gap, XLARGE, { enabledFeatures });
      expect(container).toMatchSnapshot();
    });
  });
});
