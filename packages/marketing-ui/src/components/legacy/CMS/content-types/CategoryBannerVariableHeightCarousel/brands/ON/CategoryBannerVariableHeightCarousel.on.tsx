'use client';
import React, { useState } from 'react';
// @ts-ignore
import { useEnabledFeatures } from '@ecom-next/core/react-stitch';
import { useViewportIsLarge } from '../../../../../hooks/useViewportIsLarge/index';
import { useCBVHCenterPosition } from '../../../../components/CategoryBannerVHCarousel/hooks/useCBVHCenterPosition/index';
import { CMSMarketingCarousel } from '../../../../subcomponents/CMSMarketingCarousel/index';
import { StaticTile, CBVHCarouselWrapper, Tile, CategoryBannerVHCarouselProps } from '../../../../components/CategoryBannerVHCarousel/index';
import CustomPlayPauseButton from '../../../../subcomponents/PlayPauseButton/index';
import { useShowHideBasedOnScreenSize } from '../../../../../hooks/useShowHideBasedOnScreenSize/index';
import { ComponentMaxWidth } from '../../../../subcomponents/ComponentMaxWidth/index';
import { ConditionalPaginationTargetWrapperSection } from '../../../../components/CategoryBannerVHCarousel/styles';
import CategoryBannerVHCarouselTile from './CategoryBannerVHCarouselTile.on';

const CategoryBannerVHCarousel = (props: CategoryBannerVHCarouselProps): JSX.Element | null => {
  const { frames, contentConfiguration, desktopBannerSize, mobileBannerSize, webAppearance, mobileTextTreatment, carouselSettings } = props;
  const { showHideBasedOnScreenSize } = webAppearance;
  const numberOfCarouselFrames = frames.length;
  const isDesktop: boolean = useViewportIsLarge();
  const isPersistentContent = contentConfiguration;
  const showCarouselOnViewPort = useShowHideBasedOnScreenSize(showHideBasedOnScreenSize);
  const arrowPosition = useCBVHCenterPosition(desktopBannerSize, mobileBannerSize);
  const arrowPositionValue = `min(${arrowPosition}vw, 50%)`;
  const getFramesTiles: Tile[][] = frames.reduce(
    (frameTiles: Tile[][], { tiles }) => [...frameTiles, ...(!isDesktop ? tiles.map(tile => [tile]) : [tiles])],
    []
  );

  const [hover, setHover] = useState(false);
  const handleHover = (): void => setHover(!hover);

  const PLAY_PAUSE_ICON_SIZE_PX = isDesktop ? 50 : 44;

  const [top, setTop] = useState<number | undefined>(undefined);

  const imageHeightHandler = (height: number) => {
    if (mobileTextTreatment === 'below') {
      setTop(height - PLAY_PAUSE_ICON_SIZE_PX);
    } else {
      setTop(undefined);
    }
  };
  const isMobilePersistentAndBelow = mobileTextTreatment.includes('below') && isPersistentContent && !isDesktop;

  if (!showCarouselOnViewPort || numberOfCarouselFrames < 2) return null;
  return (
    <CBVHCarouselWrapper>
      {isPersistentContent && !isMobilePersistentAndBelow && (
        <div onMouseEnter={handleHover} onMouseLeave={handleHover}>
          <StaticTile tile={frames[0].tiles[0]} />
        </div>
      )}
      <CMSMarketingCarousel
        arrowPosition={arrowPositionValue}
        carouselSettings={carouselSettings}
        customPlayPauseButton={
          <CustomPlayPauseButton
            css={{
              bottom: 0,
              maxHeight: PLAY_PAUSE_ICON_SIZE_PX,
              top,
            }}
            size={PLAY_PAUSE_ICON_SIZE_PX}
            variant={carouselSettings.styling.controlsIconsColor}
          />
        }
        forceHover={hover}
      >
        {getFramesTiles.map((tiles: Tile[], index: number) => {
          const key = `${index}_${tiles.map(tile => tile.bannerLink?.value || tile.text).join(':')}`;
          return (
            <CategoryBannerVHCarouselTile
              key={key}
              desktopBannerSize={desktopBannerSize}
              mobileBannerSize={mobileBannerSize}
              mobileTextTreatment={mobileTextTreatment}
              onImageHeightChange={imageHeightHandler}
              showContent={!isPersistentContent}
              tiles={tiles}
            />
          );
        })}
      </CMSMarketingCarousel>
      {isMobilePersistentAndBelow && (
        <div onMouseEnter={handleHover} onMouseLeave={handleHover}>
          <StaticTile isMobilePersistentAndBelow={isMobilePersistentAndBelow} tile={frames[0].tiles[0]} />
        </div>
      )}
    </CBVHCarouselWrapper>
  );
};

const CategoryBannerVHCarouselWithMaxWidth = (props: CategoryBannerVHCarouselProps): JSX.Element | null => {
  const { mobileTextTreatment, contentConfiguration } = props;
  const isDesktop: boolean = useViewportIsLarge();
  const isPersistentContent = contentConfiguration;
  const isMobilePersistentAndBelow = mobileTextTreatment.includes('below') && isPersistentContent && !isDesktop;
  const enabledFeatures = useEnabledFeatures();
  const newPlpGridFeatureFlag = !!enabledFeatures?.['mui-new-plp-grid-2025'];
  return newPlpGridFeatureFlag ? (
    <ConditionalPaginationTargetWrapperSection isMobilePersistentAndBelow={isMobilePersistentAndBelow}>
      <CategoryBannerVHCarousel {...props} />
    </ConditionalPaginationTargetWrapperSection>
  ) : (
    <ComponentMaxWidth>
      <ConditionalPaginationTargetWrapperSection isMobilePersistentAndBelow={isMobilePersistentAndBelow}>
        <CategoryBannerVHCarousel {...props} />
      </ConditionalPaginationTargetWrapperSection>
    </ComponentMaxWidth>
  );
};

export default CategoryBannerVHCarouselWithMaxWidth;
