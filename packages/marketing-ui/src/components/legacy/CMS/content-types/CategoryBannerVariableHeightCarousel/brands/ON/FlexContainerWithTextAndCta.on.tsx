// @ts-nocheck
'use client';
import React from 'react';
import { Brands, CSSObject } from '@ecom-next/core/react-stitch';
import { useViewportIsLarge } from '../../../../../hooks/useViewportIsLarge';
import { CtaButton } from '../../../../subcomponents/CTAButton';
import { RichText } from '../../../../subcomponents/RichText';
import { CTAButtonWrapperDivWithStyles, FlexContainer, ctaSizeSet, RTEWrapperDivWithStyles } from '../../../../components/CategoryBannerVHCarousel';
import { FlexContainerWithTextAndCtaPropsContentTypes } from '../../types';

const FlexContainerWithTextAndCta = ({
  desktop,
  mobile,
  text,
  ctaButton,
  mobileTextTreatment,
  isSplitTile,
}: FlexContainerWithTextAndCtaPropsContentTypes): JSX.Element => {
  const { ctaButtonStyling, verticalPlacement, horizontalPlacement, ctaVerticalPlacement, ctaHorizontalPlacement, ctaJustification } = desktop;
  const { ctaPlacement } = mobile;
  const brandName: Brands = Brands.OldNavy;
  const ctaSize = ctaSizeSet(brandName);
  const OLDNAVY_SCALABLE_TEXT_PROPS = {
    enable: true,
    parentMaxWidthPx: 1280,
  };
  const isDesktop = useViewportIsLarge();

  const rteWidth: CSSObject = isSplitTile ? { maxWidth: `min(${(864 / 1280) * 100}vw, 864px)` } : { maxWidth: 864, width: 'calc(min(100vw, 1280px) * 0.675)' };

  return (
    <FlexContainer
      ctaButton={ctaButton}
      ctaHorizontalPlacement={ctaHorizontalPlacement}
      ctaJustification={ctaJustification}
      ctaVerticalPlacement={ctaVerticalPlacement}
      horizontalPlacement={horizontalPlacement}
      mobileTextTreatment={mobileTextTreatment}
      text={text}
      verticalPlacement={verticalPlacement}
    >
      {text && (
        <RTEWrapperDivWithStyles className='rteWrapperDivs' horizontalPlacement={horizontalPlacement} textJustificationMobile={mobile.textJustification}>
          {/* UX note: for Old Navy, the RTE width should be 864px at slot size 1280px and should be scalable.
           * So maxWidth should be 864px for viewport >= 1280px and scalable for viewport < 1280px.
           */}
          <RichText
            css={{
              textAlign: isDesktop ? desktop.textJustification : mobile.textJustification,
              display: 'inline-block',
              ...rteWidth,
            }}
            disableTextAlign
            scalableText={OLDNAVY_SCALABLE_TEXT_PROPS}
            text={text}
          />
        </RTEWrapperDivWithStyles>
      )}

      {ctaButton && (
        <CTAButtonWrapperDivWithStyles className='ctaWrapperDivs' ctaHorizontalPlacement={ctaHorizontalPlacement} ctaPlacement={ctaPlacement}>
          <CtaButton ctaButton={ctaButton} ctaButtonStyling={ctaButtonStyling} ctaSize={ctaSize} fixedWidth />
        </CTAButtonWrapperDivWithStyles>
      )}
    </FlexContainer>
  );
};

export default FlexContainerWithTextAndCta;
