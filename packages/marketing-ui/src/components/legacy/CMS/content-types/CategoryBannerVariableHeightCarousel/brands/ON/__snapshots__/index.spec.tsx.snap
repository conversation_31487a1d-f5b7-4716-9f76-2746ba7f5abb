// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`AbsoluteWrapper should render tile content below image 1`] = `
.emotion-0 {
  margin-left: auto;
  margin-right: auto;
  max-width: 1280px;
}

.emotion-2 {
  position: relative;
}

.emotion-3 {
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-3 .slick-list {
  overflow: hidden;
}

.emotion-3 .slick-track {
  width: -webkit-max-content!important;
  width: -moz-max-content!important;
  width: max-content!important;
}

.emotion-3 button.slick-next.slick-arrow.slick-next,
.emotion-3 button.slick-prev.slick-arrow.slick-prev {
  z-index: 2;
  top: min(42.66666666666667vw, 50%);
  height: 44px;
  width: 44px;
  background: #003764;
  opacity: 75%;
}

.emotion-3 button.slick-next.slick-arrow.slick-next>span,
.emotion-3 button.slick-prev.slick-arrow.slick-prev>span {
  margin: auto;
}

.emotion-3 button.slick-next.slick-arrow.slick-next svg,
.emotion-3 button.slick-prev.slick-arrow.slick-prev svg {
  margin: auto;
  height: 25px;
  width: 14px;
}

.emotion-3 button.slick-next.slick-arrow.slick-next span svg path,
.emotion-3 button.slick-prev.slick-arrow.slick-prev span svg path {
  fill: #ffffff;
}

.emotion-3 button.slick-next.slick-arrow.slick-next:hover,
.emotion-3 button.slick-prev.slick-arrow.slick-prev:hover {
  opacity: 100%;
}

.emotion-3 button.slick-next.slick-arrow.slick-next:focus,
.emotion-3 button.slick-prev.slick-arrow.slick-prev:focus {
  background: #003764;
}

.emotion-3 button.slick-next.slick-arrow.slick-next:focus-visible,
.emotion-3 button.slick-prev.slick-arrow.slick-prev:focus-visible {
  outline: auto;
}

.emotion-3 .slick-slide {
  margin: 0;
  width: auto;
}

.emotion-3 .slick-disabled {
  display: none!important;
}

.emotion-3 .slick-next {
  left: calc(100% - 44px);
}

.emotion-3 .slick-slide:first-of-type a>div:nth-of-type(2)>div>div:nth-of-type(2) {
  border-left: none;
}

.emotion-3 .slick-slide>div:first-of-type {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-3 .slick-slide>div:first-of-type>div:first-of-type {
  width: 100%;
}

.emotion-3 .slick-slider ul.slick-dots {
  bottom: 4px;
  width: unset;
  height: 44px;
  margin-left: 50%;
  -webkit-transform: translateX(-50%);
  -moz-transform: translateX(-50%);
  -ms-transform: translateX(-50%);
  transform: translateX(-50%);
  -webkit-align-content: center;
  -ms-flex-line-pack: center;
  align-content: center;
}

.emotion-3 .slick-slider .slick-dots li.slick-active button:before {
  background-color: #003764;
}

.emotion-3 .slick-slider ul.slick-dots button:before {
  width: 10px;
  height: 10px;
  opacity: 1;
  background-color: #B2B2B2;
}

.emotion-4 {
  position: relative;
}

.emotion-4 .slick-slider {
  position: relative;
  display: block;
  box-sizing: border-box;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  -webkit-touch-callout: none;
  touch-action: pan-y;
  -webkit-tap-highlight-color: transparent;
  scroll-behavior: smooth;
}

.emotion-4 .slick-slider .slick-track,
.emotion-4 .slick-slider .slick-list {
  -webkit-transform: translate3d(0, 0, 0);
  -moz-transform: translate3d(0, 0, 0);
  -ms-transform: translate3d(0, 0, 0);
  transform: translate3d(0, 0, 0);
}

.emotion-4 .slick-list {
  position: relative;
  display: block;
  overflow-x: hidden;
  overflow-y: hidden;
  margin: 0;
  padding: 0;
}

.emotion-4 .slick-list:focus {
  outline: none;
}

.emotion-4 .slick-list.dragging {
  cursor: pointer;
  cursor: hand;
}

.emotion-4 .slick-track {
  position: relative;
  top: 0;
  left: 0;
  display: block;
  margin-left: auto;
  margin-right: auto;
}

.emotion-4 .slick-track:before,
.emotion-4 .slick-track:after {
  display: table;
  content: "";
}

.emotion-4 .slick-track:after {
  clear: both;
}

.emotion-4 .slick-slide {
  display: none;
  float: left;
  height: 100%;
  min-height: 1px;
}

.emotion-4 .slick-slide img {
  display: block;
}

.emotion-4 .slick-slide.slick-loading img {
  display: none;
}

.emotion-4 .slick-slide.dragging img {
  pointer-events: none;
}

.emotion-4 [dir="rtl"] .slick-slide {
  float: right;
}

.emotion-4 .slick-initialized .slick-slide,
.emotion-4 .slick-vertical .slick-slide {
  display: block;
}

.emotion-4 .slick-vertical .slick-slide {
  border: 1px solid transparent;
  height: auto;
}

.emotion-4 .slick-loading .slick-track,
.emotion-4 .slick-loading .slick-slide {
  visibility: hidden;
}

.emotion-4 .slick-loading .slick-list {
  background: #fff url("./ajax-loader.gif") center center no-repeat;
}

.emotion-4 .slick-arrow.slick-hidden {
  display: none;
}

.emotion-4 .slick-prev,
.emotion-4 .slick-next {
  font-size: 0;
  line-height: 0;
  position: absolute;
  top: 50%;
  display: block;
  padding: 0;
  cursor: pointer;
  color: transparent;
  border: none;
  outline: none;
  background: transparent;
}

.emotion-4 .slick-prev:hover,
.emotion-4 .slick-next:hover,
.emotion-4 .slick-prev:focus,
.emotion-4 .slick-next:focus {
  color: transparent;
  outline: none;
  background: transparent;
  opacity: 1;
}

.emotion-4 .slick-prev.slick-disabled,
.emotion-4 .slick-next.slick-disabled {
  opacity: 0.25;
}

.emotion-4 .slick-prev {
  left: -0;
  -webkit-transform: translate(0, -50%) rotate(90deg);
  -moz-transform: translate(0, -50%) rotate(90deg);
  -ms-transform: translate(0, -50%) rotate(90deg);
  transform: translate(0, -50%) rotate(90deg);
}

.emotion-4 .slick-prev span {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-4 [dir="rtl"] .slick-prev {
  right: -0;
  left: auto;
}

.emotion-4 .slick-next {
  right: -0;
  -webkit-transform: translate(0, -50%) rotate(-90deg);
  -moz-transform: translate(0, -50%) rotate(-90deg);
  -ms-transform: translate(0, -50%) rotate(-90deg);
  transform: translate(0, -50%) rotate(-90deg);
}

.emotion-4 .slick-next span {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-4 [dir="rtl"] .slick-next {
  right: auto;
  left: -0;
}

.emotion-4 .slick-dotted.slick-slider {
  margin-bottom: 30px;
}

.emotion-4 .slick-dots {
  position: absolute;
  bottom: -25px;
  display: block;
  width: 100%;
  padding: 0;
  margin: 0;
  list-style: none;
  text-align: center;
}

.emotion-4 .slick-dots li {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: 20px;
  height: 20px;
  margin: 0 5px;
  padding: 0;
  cursor: pointer;
}

.emotion-4 .slick-dots li.slick-active button:before {
  opacity: 0.75;
  color: black;
}

.emotion-4 .slick-dots li button {
  font-size: 0;
  line-height: 0;
  display: block;
  padding: 5px;
  cursor: pointer;
  color: transparent;
  border: 0;
  outline: none;
  background: transparent;
}

.emotion-4 .slick-dots li button:hover,
.emotion-4 .slick-dots li button:focus {
  outline: none;
}

.emotion-4 .slick-dots li button:hover:before,
.emotion-4 .slick-dots li button:focus:before,
.emotion-4 .slick-dots li button:hover:before,
.emotion-4 .slick-dots li button:focus:before {
  opacity: 1;
}

.emotion-4 .slick-dots li button:before {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 5px;
  height: 5px;
  content: "";
  opacity: 0.25;
  background-color: black;
  border-radius: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-5 {
  display: inline-block;
  height: 2rem;
  width: 2rem;
  min-height: 2rem;
  min-width: 2rem;
}

.emotion-5 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-6 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-7 {
  width: 100%;
  position: relative;
}

.emotion-8 {
  overflow: hidden;
  position: relative;
  box-sizing: border-box;
  aspect-ratio: 375/320;
}

.emotion-9 {
  -webkit-background-size: cover;
  background-size: cover;
  aspect-ratio: 375/320;
  overflow: hidden;
}

.emotion-10 {
  width: 100%;
  aspect-ratio: 375/320;
  object-fit: cover;
}

.emotion-11 {
  height: 100%;
  width: 100%;
  position: absolute;
  top: 0;
  left: 0;
  position: unset;
  height: auto;
}

.emotion-12 {
  width: 100%;
  height: 100%;
  padding: 30px 16px;
  box-sizing: border-box;
  position: static;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  grid-template-columns: repeat(3, minmax(20%,max-content));
  -webkit-align-content: center;
  -ms-flex-line-pack: center;
  align-content: center;
  grid-template-rows: repeat(3, minmax(0, max-content));
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
}

.emotion-12 .rteWrapperDivs {
  grid-column: 3;
  grid-row: 3;
}

.emotion-12 .ctaWrapperDivs {
  grid-column: 1;
  grid-row: 3;
}

.emotion-13 {
  box-sizing: content-box;
  text-align: center;
}

.emotion-14 {
  text-align: center;
  display: inline-block;
  max-width: 864px;
  width: calc(min(100vw, 1280px) * 0.675);
}

.emotion-14 .amp-cms--p {
  padding: 0;
  margin: 0;
  line-height: 0;
}

.emotion-14 .amp-cms--p>span {
  display: inline;
  white-space: break-spaces;
}

.emotion-14 .amp-cms--p a {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  position: relative;
  z-index: 2;
  pointer-events: auto;
}

.emotion-14 .amp-cms--p a:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-14 .amp-cms--legal-copy {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(2.666666666666667vw, 34.13333333333333px));
  line-height: 1.5;
  letter-spacing: 0;
}

.emotion-14 sup {
  vertical-align: top;
  display: inline-block;
  margin-top: -0.20ex;
}

.emotion-14 sub {
  vertical-align: bottom;
  display: inline-block;
  margin-bottom: -0.20ex;
}

.emotion-14 .amp-cms--f-0 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(2.933333333333333vw, 37.54666666666667px);
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-14 .amp-cms--f-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(2.933333333333333vw, 37.54666666666667px);
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-14 .amp-cms--f-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(2.933333333333333vw, 37.54666666666667px);
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-14 .amp-cms--fn-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(2.933333333333333vw, 37.54666666666667px);
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-14 .amp-cms--body-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(3.733333333333334vw, 47.78666666666667px));
  line-height: 1.5;
  letter-spacing: min(0.08960000000000001vw, 1.1468800000000001px);
  font-weight: 500;
}

.emotion-14 .amp-cms--body-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(3.2vw, 40.96px));
  line-height: 1.5;
  letter-spacing: min(0.0768vw, 0.98304px);
  font-weight: 500;
}

.emotion-14 .amp-cms--body-3 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(3.2vw, 40.96px));
  line-height: 1.5;
  letter-spacing: min(0.0768vw, 0.98304px);
}

.emotion-14 .amp-cms--body-4 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(2.666666666666667vw, 34.13333333333333px));
  line-height: 1.5;
  letter-spacing: 0;
}

.emotion-14 .amp-cms--body-5 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(2.666666666666667vw, 34.13333333333333px));
  line-height: 1.5;
  letter-spacing: 0;
}

.emotion-14 .amp-cms--eyebrow-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(6.133333333333333vw, 78.50666666666667px));
  line-height: 1;
  letter-spacing: min(0.24533333333333335vw, 3.140266666666667px);
  font-weight: 500;
}

.emotion-14 .amp-cms--eyebrow-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(5.066666666666666vw, 64.85333333333334px));
  line-height: 1;
  letter-spacing: min(0.20266666666666666vw, 2.5941333333333336px);
  font-weight: 500;
}

.emotion-14 .amp-cms--eyebrow-3 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(3.733333333333334vw, 47.78666666666667px));
  line-height: 1.0714285714285714;
  letter-spacing: min(0.14933333333333335vw, 1.9114666666666669px);
  font-weight: 500;
}

.emotion-14 .amp-cms--headline-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(18.133333333333333vw, 232.10666666666668px));
  line-height: 1;
  letter-spacing: 0;
  font-weight: 700;
}

.emotion-14 .amp-cms--headline-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(13.333333333333334vw, 170.66666666666669px));
  line-height: 1;
  letter-spacing: 0;
  font-weight: 700;
}

.emotion-14 .amp-cms--headline-3 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(11.466666666666667vw, 146.77333333333334px));
  line-height: 1;
  letter-spacing: 0;
  font-weight: 700;
}

.emotion-14 .amp-cms--headline-4 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(9.6vw, 122.88000000000001px));
  line-height: 1;
  letter-spacing: min(0.19199999999999998vw, 2.4576000000000002px);
  font-weight: 700;
}

.emotion-14 .amp-cms--headline-5 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(7.466666666666668vw, 95.57333333333334px));
  line-height: 1;
  letter-spacing: min(0.2986666666666667vw, 3.8229333333333337px);
  font-weight: 700;
}

.emotion-14 .amp-cms--headline-6 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(6.4vw, 81.92px));
  line-height: 1.0833333333333333;
  letter-spacing: min(0.256vw, 3.2768px);
  font-weight: 700;
}

.emotion-14 .amp-cms--headline-7 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(5.333333333333334vw, 68.26666666666667px));
  line-height: 1;
  letter-spacing: min(0.128vw, 1.6384px);
  font-weight: 700;
}

.emotion-14 .amp-cms--headlineAlt-1 {
  color: #003764;
  font-size: max(13px, min(17.066666666666666vw, 218.45333333333335px));
  line-height: 1;
  letter-spacing: min(-0.07999999999999999vw, -1.024px);
  font-weight: 400;
}

.emotion-14 .amp-cms--headlineAlt-2 {
  color: #003764;
  font-size: max(13px, min(14.399999999999999vw, 184.32000000000002px));
  line-height: 1;
  letter-spacing: min(-0.07999999999999999vw, -1.024px);
  font-weight: 400;
}

.emotion-14 .amp-cms--headlineAlt-3 {
  color: #003764;
  font-size: max(13px, min(9.066666666666666vw, 116.05333333333334px));
  line-height: 1;
  letter-spacing: min(-0.07999999999999999vw, -1.024px);
  font-weight: 400;
}

.emotion-14 .amp-cms--headlineAlt-4 {
  color: #003764;
  font-size: max(13px, min(6.4vw, 81.92px));
  line-height: 1.4166666666666667;
  letter-spacing: min(-0.07999999999999999vw, -1.024px);
  font-weight: 400;
}

.emotion-14 .amp-cms--headlineAlt-5 {
  color: #003764;
  font-size: min(3.733333333333334vw, 47.78666666666667px);
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-14 .amp-cms--headlineAlt-6 {
  color: #003764;
  font-size: min(3.733333333333334vw, 47.78666666666667px);
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-14 .amp-cms--headlineAlt-7 {
  color: #003764;
  font-size: min(3.733333333333334vw, 47.78666666666667px);
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-14 .amp-cms--promo-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(13px, min(16vw, 204.8px));
  line-height: 1;
  letter-spacing: min(0vw, 0px);
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-14 .amp-cms--promo-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(13px, min(10.666666666666668vw, 136.53333333333333px));
  line-height: 1;
  letter-spacing: min(0vw, 0px);
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-14 .amp-cms--promoAlt-1 {
  color: #003764;
  font-size: min(3.733333333333334vw, 47.78666666666667px);
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 600;
}

.emotion-14 .amp-cms--subhead-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(16px, min(5.333333333333334vw, 68.26666666666667px));
  line-height: 1.5;
  letter-spacing: 0;
  font-weight: 500;
}

.emotion-14 .amp-cms--subhead-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(16px, min(4.8vw, 61.440000000000005px));
  line-height: 1.5;
  letter-spacing: 0;
  font-weight: 500;
}

.emotion-14 .amp-cms--subhead-3 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(16px, min(4.266666666666667vw, 54.61333333333334px));
  line-height: 1.5;
  letter-spacing: 0;
  font-weight: 500;
}

.emotion-15 {
  display: block;
  width: 100%;
  position: relative;
  z-index: 2;
  padding-top: 18px;
  text-align: center;
}

.emotion-16 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 700;
  letter-spacing: 1.6800000000000002px;
  min-height: 44px;
  max-height: auto;
  line-height: 1.1428571428571428;
  padding: 12px 22px;
  width: auto;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  background-color: #FFFFFF;
  color: #003764;
  border-color: #003764;
  -webkit-align-self: flex-start;
  -ms-flex-item-align: flex-start;
  align-self: flex-start;
  white-space: normal;
  width: 280px;
}

.emotion-16:focus {
  outline: none;
}

.emotion-16>span {
  padding: 1px 0;
}

.emotion-16:hover,
.emotion-16:focus {
  color: #FFFFFF;
  background-color: #003764;
  border-color: #003764;
}

.emotion-16:active {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  color: #FFFFFF;
  background-color: #000000;
  border-color: #000000;
}

.emotion-22 {
  width: 100%;
  height: 100%;
  position: absolute;
  left: 0;
  top: 0;
  z-index: 1;
}

.emotion-24 {
  width: 100%;
  height: 100%;
  padding: 30px 16px;
  box-sizing: border-box;
  position: static;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  grid-template-columns: repeat(3, minmax(20%,max-content));
  -webkit-align-content: center;
  -ms-flex-line-pack: center;
  align-content: center;
  grid-template-rows: repeat(3, minmax(0, max-content));
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
}

.emotion-24 .rteWrapperDivs {
  grid-column: 2;
  grid-row: 3;
}

.emotion-24 .ctaWrapperDivs {
  grid-column: 3;
  grid-row: 1;
}

.emotion-25 {
  box-sizing: content-box;
  text-align: right;
}

.emotion-26 {
  text-align: right;
  display: inline-block;
  max-width: 864px;
  width: calc(min(100vw, 1280px) * 0.675);
}

.emotion-26 .amp-cms--p {
  padding: 0;
  margin: 0;
  line-height: 0;
}

.emotion-26 .amp-cms--p>span {
  display: inline;
  white-space: break-spaces;
}

.emotion-26 .amp-cms--p a {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  position: relative;
  z-index: 2;
  pointer-events: auto;
}

.emotion-26 .amp-cms--p a:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-26 .amp-cms--legal-copy {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(2.666666666666667vw, 34.13333333333333px));
  line-height: 1.5;
  letter-spacing: 0;
}

.emotion-26 sup {
  vertical-align: top;
  display: inline-block;
  margin-top: -0.20ex;
}

.emotion-26 sub {
  vertical-align: bottom;
  display: inline-block;
  margin-bottom: -0.20ex;
}

.emotion-26 .amp-cms--f-0 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(2.933333333333333vw, 37.54666666666667px);
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-26 .amp-cms--f-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(2.933333333333333vw, 37.54666666666667px);
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-26 .amp-cms--f-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(2.933333333333333vw, 37.54666666666667px);
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-26 .amp-cms--fn-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(2.933333333333333vw, 37.54666666666667px);
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-26 .amp-cms--body-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(3.733333333333334vw, 47.78666666666667px));
  line-height: 1.5;
  letter-spacing: min(0.08960000000000001vw, 1.1468800000000001px);
  font-weight: 500;
}

.emotion-26 .amp-cms--body-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(3.2vw, 40.96px));
  line-height: 1.5;
  letter-spacing: min(0.0768vw, 0.98304px);
  font-weight: 500;
}

.emotion-26 .amp-cms--body-3 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(3.2vw, 40.96px));
  line-height: 1.5;
  letter-spacing: min(0.0768vw, 0.98304px);
}

.emotion-26 .amp-cms--body-4 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(2.666666666666667vw, 34.13333333333333px));
  line-height: 1.5;
  letter-spacing: 0;
}

.emotion-26 .amp-cms--body-5 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(2.666666666666667vw, 34.13333333333333px));
  line-height: 1.5;
  letter-spacing: 0;
}

.emotion-26 .amp-cms--eyebrow-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(6.133333333333333vw, 78.50666666666667px));
  line-height: 1;
  letter-spacing: min(0.24533333333333335vw, 3.140266666666667px);
  font-weight: 500;
}

.emotion-26 .amp-cms--eyebrow-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(5.066666666666666vw, 64.85333333333334px));
  line-height: 1;
  letter-spacing: min(0.20266666666666666vw, 2.5941333333333336px);
  font-weight: 500;
}

.emotion-26 .amp-cms--eyebrow-3 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(3.733333333333334vw, 47.78666666666667px));
  line-height: 1.0714285714285714;
  letter-spacing: min(0.14933333333333335vw, 1.9114666666666669px);
  font-weight: 500;
}

.emotion-26 .amp-cms--headline-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(18.133333333333333vw, 232.10666666666668px));
  line-height: 1;
  letter-spacing: 0;
  font-weight: 700;
}

.emotion-26 .amp-cms--headline-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(13.333333333333334vw, 170.66666666666669px));
  line-height: 1;
  letter-spacing: 0;
  font-weight: 700;
}

.emotion-26 .amp-cms--headline-3 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(11.466666666666667vw, 146.77333333333334px));
  line-height: 1;
  letter-spacing: 0;
  font-weight: 700;
}

.emotion-26 .amp-cms--headline-4 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(9.6vw, 122.88000000000001px));
  line-height: 1;
  letter-spacing: min(0.19199999999999998vw, 2.4576000000000002px);
  font-weight: 700;
}

.emotion-26 .amp-cms--headline-5 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(7.466666666666668vw, 95.57333333333334px));
  line-height: 1;
  letter-spacing: min(0.2986666666666667vw, 3.8229333333333337px);
  font-weight: 700;
}

.emotion-26 .amp-cms--headline-6 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(6.4vw, 81.92px));
  line-height: 1.0833333333333333;
  letter-spacing: min(0.256vw, 3.2768px);
  font-weight: 700;
}

.emotion-26 .amp-cms--headline-7 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(5.333333333333334vw, 68.26666666666667px));
  line-height: 1;
  letter-spacing: min(0.128vw, 1.6384px);
  font-weight: 700;
}

.emotion-26 .amp-cms--headlineAlt-1 {
  color: #003764;
  font-size: max(13px, min(17.066666666666666vw, 218.45333333333335px));
  line-height: 1;
  letter-spacing: min(-0.07999999999999999vw, -1.024px);
  font-weight: 400;
}

.emotion-26 .amp-cms--headlineAlt-2 {
  color: #003764;
  font-size: max(13px, min(14.399999999999999vw, 184.32000000000002px));
  line-height: 1;
  letter-spacing: min(-0.07999999999999999vw, -1.024px);
  font-weight: 400;
}

.emotion-26 .amp-cms--headlineAlt-3 {
  color: #003764;
  font-size: max(13px, min(9.066666666666666vw, 116.05333333333334px));
  line-height: 1;
  letter-spacing: min(-0.07999999999999999vw, -1.024px);
  font-weight: 400;
}

.emotion-26 .amp-cms--headlineAlt-4 {
  color: #003764;
  font-size: max(13px, min(6.4vw, 81.92px));
  line-height: 1.4166666666666667;
  letter-spacing: min(-0.07999999999999999vw, -1.024px);
  font-weight: 400;
}

.emotion-26 .amp-cms--headlineAlt-5 {
  color: #003764;
  font-size: min(3.733333333333334vw, 47.78666666666667px);
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-26 .amp-cms--headlineAlt-6 {
  color: #003764;
  font-size: min(3.733333333333334vw, 47.78666666666667px);
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-26 .amp-cms--headlineAlt-7 {
  color: #003764;
  font-size: min(3.733333333333334vw, 47.78666666666667px);
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-26 .amp-cms--promo-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(13px, min(16vw, 204.8px));
  line-height: 1;
  letter-spacing: min(0vw, 0px);
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-26 .amp-cms--promo-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(13px, min(10.666666666666668vw, 136.53333333333333px));
  line-height: 1;
  letter-spacing: min(0vw, 0px);
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-26 .amp-cms--promoAlt-1 {
  color: #003764;
  font-size: min(3.733333333333334vw, 47.78666666666667px);
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 600;
}

.emotion-26 .amp-cms--subhead-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(16px, min(5.333333333333334vw, 68.26666666666667px));
  line-height: 1.5;
  letter-spacing: 0;
  font-weight: 500;
}

.emotion-26 .amp-cms--subhead-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(16px, min(4.8vw, 61.440000000000005px));
  line-height: 1.5;
  letter-spacing: 0;
  font-weight: 500;
}

.emotion-26 .amp-cms--subhead-3 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(16px, min(4.266666666666667vw, 54.61333333333334px));
  line-height: 1.5;
  letter-spacing: 0;
  font-weight: 500;
}

.emotion-27 {
  display: block;
  width: 100%;
  position: relative;
  z-index: 2;
  padding-top: 18px;
  text-align: start;
}

.emotion-28 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 700;
  letter-spacing: 1.05px;
  min-height: 44px;
  max-height: auto;
  line-height: 1.8571428571428572;
  padding: 0;
  width: auto;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: left;
  border-left: none;
  border-right: none;
  border-radius: 0;
  background-color: transparent;
  border-bottom: 0;
  -webkit-box-pack: left;
  -ms-flex-pack: left;
  -webkit-justify-content: left;
  justify-content: left;
  color: #003764;
  height: auto;
  -webkit-text-decoration: underline;
  text-decoration: underline;
  text-underline-offset: 3.5px;
  text-decoration-thickness: 3px;
  -webkit-align-self: flex-start;
  -ms-flex-item-align: flex-start;
  align-self: flex-start;
  white-space: normal;
}

.emotion-28:focus {
  outline: none;
}

.emotion-28>span {
  padding: 1px 0;
}

.emotion-28:hover,
.emotion-28:focus {
  text-decoration-color: #003764;
}

.emotion-28:hover span,
.emotion-28:focus span {
  color: #FFFFFF;
  background-color: #003764;
}

.emotion-28:active {
  text-decoration-color: #000000;
}

.emotion-28:active>span {
  color: #FFFFFF;
  background-color: #000000;
}

.emotion-29 {
  box-sizing: border-box;
}

<div>
  <div
    style="font-family: var(--font-family-font1),sans-serif;"
  >
    <div
      class="emotion-0"
    >
      <section
        class="emotion-1"
      >
        <div
          class="emotion-2"
        >
          <nav
            class="emotion-3"
          >
            <div
              class="emotion-4"
            >
              <div
                class="slick-slider slick-initialized"
                dir="ltr"
              >
                <button
                  aria-label="Previous"
                  class="slick-prev slick-arrow slick-prev slick-disabled"
                  data-role="none"
                  disabled=""
                >
                  <span
                    aria-hidden="true"
                    class="emotion-5"
                  >
                    <svg
                      viewBox="0 0 13.29 8.07"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M6.13 7.86a.73.73 0 001.04 0l5.92-5.92a.73.73 0 000-1.03l-.7-.7a.73.73 0 00-1.03 0l-4.71 4.7L1.94.2A.73.73 0 00.9.21l-.69.7a.73.73 0 000 1.03z"
                        fill="#003764"
                      />
                    </svg>
                  </span>
                </button>
                <div
                  class="slick-list"
                >
                  <div
                    class="slick-track"
                    style="opacity: 1; transform: translate3d(0px, 0px, 0px);"
                  >
                    <div
                      aria-hidden="false"
                      class="slick-slide slick-active slick-current"
                      data-index="0"
                      style="outline: none; width: 0px;"
                      tabindex="-1"
                    >
                      <div>
                        <div
                          class="emotion-6"
                        >
                          <div
                            class="emotion-7"
                          >
                            <div
                              class="emotion-8"
                              height="320"
                              width="375"
                            >
                              <div
                                class="emotion-9"
                                data-testid="product-card-image"
                              >
                                <img
                                  alt="colorful leggings"
                                  class="emotion-10"
                                  src="https://1puomodm9vwdc1sabswsbq9607.staging.bigcontent.io/i/oldnavy/faded-blue-bg-1000x115?fmt=webp"
                                />
                              </div>
                            </div>
                            <div
                              class="emotion-11"
                              data-testid="absolute-wrapper"
                            >
                              <div
                                class="emotion-12"
                              >
                                <div
                                  class="rteWrapperDivs emotion-13"
                                >
                                  <div
                                    class="emotion-14"
                                  >
                                    <div>
                                      <h2
                                        class="amp-cms--p"
                                      >
                                        <span
                                          class="amp-cms--body-1"
                                        >
                                          OUT MOST LOVED STYLES
                                        </span>
                                      </h2>
                                    </div>
                                  </div>
                                </div>
                                <div
                                  class="ctaWrapperDivs emotion-15"
                                >
                                  <a
                                    class="emotion-16"
                                    color="dark"
                                    href="/buyIt"
                                  >
                                    new arrivals
                                  </a>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div
                      aria-hidden="true"
                      class="slick-slide"
                      data-index="1"
                      style="outline: none; width: 0px;"
                      tabindex="-1"
                    >
                      <div>
                        <div
                          class="emotion-6"
                        >
                          <div
                            class="emotion-7"
                          >
                            <div
                              class="emotion-8"
                              height="320"
                              width="375"
                            >
                              <div
                                class="emotion-9"
                                data-testid="product-card-image"
                              >
                                <img
                                  alt="Kid Photo"
                                  class="emotion-10"
                                  src="https://1puomodm9vwdc1sabswsbq9607.staging.bigcontent.io/i/oldnavy/G32977_TG_MOB@2x?fmt=webp"
                                />
                              </div>
                            </div>
                            <a
                              class="emotion-22"
                              href="/linkToNewArrivals"
                              tabindex="-1"
                              target="_self"
                              title="link to somewhere"
                            />
                            <div
                              class="emotion-11"
                              data-testid="absolute-wrapper"
                            >
                              <div
                                class="emotion-24"
                              >
                                <div
                                  class="rteWrapperDivs emotion-25"
                                >
                                  <div
                                    class="emotion-26"
                                  >
                                    <div>
                                      <p
                                        class="amp-cms--p"
                                      >
                                        <span
                                          class="amp-cms--subhead-1"
                                          style="color:#00FF00;font-weight:800"
                                        >
                                          Body font 1
                                        </span>
                                      </p>
                                      <p
                                        class="amp-cms--p"
                                      >
                                        <span
                                          class="amp-cms--subhead-2"
                                          style="color:#10a6a6;font-style:italic"
                                        >
                                          GOOD EARTH
                                        </span>
                                      </p>
                                    </div>
                                  </div>
                                </div>
                                <div
                                  class="ctaWrapperDivs emotion-27"
                                >
                                  <a
                                    class="emotion-28"
                                    color="dark"
                                    href="/buyIt"
                                  >
                                    <span
                                      class="emotion-29"
                                    >
                                      new arrivals
                                    </span>
                                  </a>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div
                      aria-hidden="true"
                      class="slick-slide"
                      data-index="2"
                      style="outline: none; width: 0px;"
                      tabindex="-1"
                    >
                      <div>
                        <div
                          class="emotion-6"
                        >
                          <div
                            class="emotion-7"
                          >
                            <div
                              class="emotion-8"
                              height="320"
                              width="375"
                            >
                              <div
                                class="emotion-9"
                                data-testid="product-card-image"
                              >
                                <img
                                  alt=""
                                  class="emotion-10"
                                  src="https://1puomodm9vwdc1sabswsbq9607.staging.bigcontent.io/i/oldnavy/faded-blue-bg-1000x115?fmt=webp"
                                />
                              </div>
                            </div>
                            <div
                              class="emotion-11"
                              data-testid="absolute-wrapper"
                            >
                              <div
                                class="emotion-12"
                              >
                                <div
                                  class="rteWrapperDivs emotion-13"
                                >
                                  <div
                                    class="emotion-14"
                                  >
                                    <div>
                                      <h2
                                        class="amp-cms--p"
                                      >
                                        <span
                                          class="amp-cms--body-1"
                                        >
                                          OUT MOST LOVED STYLES
                                        </span>
                                      </h2>
                                    </div>
                                  </div>
                                </div>
                                <div
                                  class="ctaWrapperDivs emotion-15"
                                >
                                  <a
                                    class="emotion-16"
                                    color="dark"
                                    href="/buyIt"
                                  >
                                    new arrivals
                                  </a>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div
                      aria-hidden="true"
                      class="slick-slide"
                      data-index="3"
                      style="outline: none; width: 0px;"
                      tabindex="-1"
                    >
                      <div>
                        <div
                          class="emotion-6"
                        >
                          <div
                            class="emotion-7"
                          >
                            <div
                              class="emotion-8"
                              height="320"
                              width="375"
                            >
                              <div
                                class="emotion-9"
                                data-testid="product-card-image"
                              >
                                <img
                                  alt="Kid Photo"
                                  class="emotion-10"
                                  src="https://1puomodm9vwdc1sabswsbq9607.staging.bigcontent.io/i/oldnavy/G32977_TG_MOB@2x?fmt=webp"
                                />
                              </div>
                            </div>
                            <a
                              class="emotion-22"
                              href="/linkToNewArrivals"
                              tabindex="-1"
                              target="_self"
                              title="link to somewhere"
                            />
                            <div
                              class="emotion-11"
                              data-testid="absolute-wrapper"
                            >
                              <div
                                class="emotion-24"
                              >
                                <div
                                  class="rteWrapperDivs emotion-25"
                                >
                                  <div
                                    class="emotion-26"
                                  >
                                    <div>
                                      <p
                                        class="amp-cms--p"
                                      >
                                        <span
                                          class="amp-cms--subhead-1"
                                          style="color:#00FF00;font-weight:800"
                                        >
                                          Body font 1
                                        </span>
                                      </p>
                                      <p
                                        class="amp-cms--p"
                                      >
                                        <span
                                          class="amp-cms--subhead-2"
                                          style="color:#10a6a6;font-style:italic"
                                        >
                                          GOOD EARTH
                                        </span>
                                      </p>
                                    </div>
                                  </div>
                                </div>
                                <div
                                  class="ctaWrapperDivs emotion-27"
                                >
                                  <a
                                    class="emotion-28"
                                    color="dark"
                                    href="/buyIt"
                                  >
                                    <span
                                      class="emotion-29"
                                    >
                                      new arrivals
                                    </span>
                                  </a>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <button
                  aria-label="Next"
                  class="slick-next slick-arrow slick-next"
                  data-role="none"
                >
                  <span
                    aria-hidden="true"
                    class="emotion-5"
                  >
                    <svg
                      viewBox="0 0 13.29 8.07"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M6.13 7.86a.73.73 0 001.04 0l5.92-5.92a.73.73 0 000-1.03l-.7-.7a.73.73 0 00-1.03 0l-4.71 4.7L1.94.2A.73.73 0 00.9.21l-.69.7a.73.73 0 000 1.03z"
                        fill="#003764"
                      />
                    </svg>
                  </span>
                </button>
                <ul
                  class="slick-dots"
                  style="display: block;"
                >
                  <li
                    class="slick-active"
                  >
                    <button>
                      1
                    </button>
                  </li>
                  <li
                    class=""
                  >
                    <button>
                      2
                    </button>
                  </li>
                  <li
                    class=""
                  >
                    <button>
                      3
                    </button>
                  </li>
                  <li
                    class=""
                  >
                    <button>
                      4
                    </button>
                  </li>
                </ul>
              </div>
            </div>
          </nav>
        </div>
      </section>
    </div>
  </div>
</div>
`;

exports[`CategoryBannerVariableHeightCarousel OldNavy Content Type should match snapshot for Brands.OldNavy 1`] = `
.emotion-0 {
  margin-left: auto;
  margin-right: auto;
  max-width: 1280px;
}

.emotion-2 {
  position: relative;
}

.emotion-3 {
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-3 .slick-list {
  overflow: hidden;
}

.emotion-3 .slick-track {
  width: -webkit-max-content!important;
  width: -moz-max-content!important;
  width: max-content!important;
}

.emotion-3 button.slick-next.slick-arrow.slick-next,
.emotion-3 button.slick-prev.slick-arrow.slick-prev {
  z-index: 2;
  top: min(15.625vw, 50%);
  height: 44px;
  width: 44px;
  background: #ffffff;
  opacity: 75%;
}

.emotion-3 button.slick-next.slick-arrow.slick-next>span,
.emotion-3 button.slick-prev.slick-arrow.slick-prev>span {
  margin: auto;
}

.emotion-3 button.slick-next.slick-arrow.slick-next svg,
.emotion-3 button.slick-prev.slick-arrow.slick-prev svg {
  margin: auto;
  height: 25px;
  width: 14px;
}

.emotion-3 button.slick-next.slick-arrow.slick-next span svg path,
.emotion-3 button.slick-prev.slick-arrow.slick-prev span svg path {
  fill: #003764;
}

.emotion-3 button.slick-next.slick-arrow.slick-next:hover,
.emotion-3 button.slick-prev.slick-arrow.slick-prev:hover {
  opacity: 100%;
}

.emotion-3 button.slick-next.slick-arrow.slick-next:focus,
.emotion-3 button.slick-prev.slick-arrow.slick-prev:focus {
  background: #ffffff;
}

.emotion-3 button.slick-next.slick-arrow.slick-next:focus-visible,
.emotion-3 button.slick-prev.slick-arrow.slick-prev:focus-visible {
  outline: auto;
}

.emotion-3 .slick-slide {
  margin: 0;
  width: auto;
}

.emotion-3 .slick-disabled {
  display: none!important;
}

.emotion-3 .slick-next {
  left: calc(100% - 44px);
}

.emotion-3 .slick-slide:first-of-type a>div:nth-of-type(2)>div>div:nth-of-type(2) {
  border-left: none;
}

.emotion-3 .slick-slide>div:first-of-type {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-3 .slick-slide>div:first-of-type>div:first-of-type {
  width: 100%;
}

.emotion-3 .slick-slider ul.slick-dots {
  bottom: 8px;
  width: unset;
  height: 44px;
  margin-left: 50%;
  -webkit-transform: translateX(-50%);
  -moz-transform: translateX(-50%);
  -ms-transform: translateX(-50%);
  transform: translateX(-50%);
  -webkit-align-content: center;
  -ms-flex-line-pack: center;
  align-content: center;
}

.emotion-3 .slick-slider .slick-dots li.slick-active button:before {
  background-color: #FFFFFF;
}

.emotion-3 .slick-slider ul.slick-dots button:before {
  width: 10px;
  height: 10px;
  opacity: 1;
  border: 1px solid #FFFFFF;
  background-color: transparent;
}

.emotion-4 {
  position: relative;
}

.emotion-4 .slick-slider {
  position: relative;
  display: block;
  box-sizing: border-box;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  -webkit-touch-callout: none;
  touch-action: pan-y;
  -webkit-tap-highlight-color: transparent;
  scroll-behavior: smooth;
}

.emotion-4 .slick-slider .slick-track,
.emotion-4 .slick-slider .slick-list {
  -webkit-transform: translate3d(0, 0, 0);
  -moz-transform: translate3d(0, 0, 0);
  -ms-transform: translate3d(0, 0, 0);
  transform: translate3d(0, 0, 0);
}

.emotion-4 .slick-list {
  position: relative;
  display: block;
  overflow-x: hidden;
  overflow-y: hidden;
  margin: 0;
  padding: 0;
}

.emotion-4 .slick-list:focus {
  outline: none;
}

.emotion-4 .slick-list.dragging {
  cursor: pointer;
  cursor: hand;
}

.emotion-4 .slick-track {
  position: relative;
  top: 0;
  left: 0;
  display: block;
  margin-left: auto;
  margin-right: auto;
}

.emotion-4 .slick-track:before,
.emotion-4 .slick-track:after {
  display: table;
  content: "";
}

.emotion-4 .slick-track:after {
  clear: both;
}

.emotion-4 .slick-slide {
  display: none;
  float: left;
  height: 100%;
  min-height: 1px;
}

.emotion-4 .slick-slide img {
  display: block;
}

.emotion-4 .slick-slide.slick-loading img {
  display: none;
}

.emotion-4 .slick-slide.dragging img {
  pointer-events: none;
}

.emotion-4 [dir="rtl"] .slick-slide {
  float: right;
}

.emotion-4 .slick-initialized .slick-slide,
.emotion-4 .slick-vertical .slick-slide {
  display: block;
}

.emotion-4 .slick-vertical .slick-slide {
  border: 1px solid transparent;
  height: auto;
}

.emotion-4 .slick-loading .slick-track,
.emotion-4 .slick-loading .slick-slide {
  visibility: hidden;
}

.emotion-4 .slick-loading .slick-list {
  background: #fff url("./ajax-loader.gif") center center no-repeat;
}

.emotion-4 .slick-arrow.slick-hidden {
  display: none;
}

.emotion-4 .slick-prev,
.emotion-4 .slick-next {
  font-size: 0;
  line-height: 0;
  position: absolute;
  top: 50%;
  display: block;
  padding: 0;
  cursor: pointer;
  color: transparent;
  border: none;
  outline: none;
  background: transparent;
}

.emotion-4 .slick-prev:hover,
.emotion-4 .slick-next:hover,
.emotion-4 .slick-prev:focus,
.emotion-4 .slick-next:focus {
  color: transparent;
  outline: none;
  background: transparent;
  opacity: 1;
}

.emotion-4 .slick-prev.slick-disabled,
.emotion-4 .slick-next.slick-disabled {
  opacity: 0.25;
}

.emotion-4 .slick-prev {
  left: -0;
  -webkit-transform: translate(0, -50%) rotate(90deg);
  -moz-transform: translate(0, -50%) rotate(90deg);
  -ms-transform: translate(0, -50%) rotate(90deg);
  transform: translate(0, -50%) rotate(90deg);
}

.emotion-4 .slick-prev span {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-4 [dir="rtl"] .slick-prev {
  right: -0;
  left: auto;
}

.emotion-4 .slick-next {
  right: -0;
  -webkit-transform: translate(0, -50%) rotate(-90deg);
  -moz-transform: translate(0, -50%) rotate(-90deg);
  -ms-transform: translate(0, -50%) rotate(-90deg);
  transform: translate(0, -50%) rotate(-90deg);
}

.emotion-4 .slick-next span {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-4 [dir="rtl"] .slick-next {
  right: auto;
  left: -0;
}

.emotion-4 .slick-dotted.slick-slider {
  margin-bottom: 30px;
}

.emotion-4 .slick-dots {
  position: absolute;
  bottom: -25px;
  display: block;
  width: 100%;
  padding: 0;
  margin: 0;
  list-style: none;
  text-align: center;
}

.emotion-4 .slick-dots li {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: 20px;
  height: 20px;
  margin: 0 5px;
  padding: 0;
  cursor: pointer;
}

.emotion-4 .slick-dots li.slick-active button:before {
  opacity: 0.75;
  color: black;
}

.emotion-4 .slick-dots li button {
  font-size: 0;
  line-height: 0;
  display: block;
  padding: 5px;
  cursor: pointer;
  color: transparent;
  border: 0;
  outline: none;
  background: transparent;
}

.emotion-4 .slick-dots li button:hover,
.emotion-4 .slick-dots li button:focus {
  outline: none;
}

.emotion-4 .slick-dots li button:hover:before,
.emotion-4 .slick-dots li button:focus:before,
.emotion-4 .slick-dots li button:hover:before,
.emotion-4 .slick-dots li button:focus:before {
  opacity: 1;
}

.emotion-4 .slick-dots li button:before {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 5px;
  height: 5px;
  content: "";
  opacity: 0.25;
  background-color: black;
  border-radius: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-5 {
  display: inline-block;
  height: 2rem;
  width: 2rem;
  min-height: 2rem;
  min-width: 2rem;
}

.emotion-5 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-6 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-7 {
  width: 100%;
  position: relative;
}

.emotion-8 {
  overflow: hidden;
  position: relative;
  box-sizing: border-box;
  aspect-ratio: 640/400;
}

.emotion-9 {
  -webkit-background-size: cover;
  background-size: cover;
  aspect-ratio: 640/400;
  overflow: hidden;
}

.emotion-10 {
  width: 100%;
  aspect-ratio: 640/400;
  object-fit: cover;
}

.emotion-11 {
  width: 100%;
  height: 100%;
  position: absolute;
  left: 0;
  top: 0;
  z-index: 1;
}

.emotion-12 {
  height: 100%;
  width: 100%;
  position: absolute;
  top: 0;
  left: 0;
  position: absolute;
  height: 100%;
}

.emotion-13 {
  width: 100%;
  height: 100%;
  padding: 50px 60px;
  box-sizing: border-box;
  position: absolute;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  display: grid;
  grid-template-columns: repeat(3, minmax(min-content,max-content));
  -webkit-align-content: center;
  -ms-flex-line-pack: center;
  align-content: center;
  grid-template-rows: 1fr max-content 1fr;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  justify-content: space-between;
}

.emotion-13 .rteWrapperDivs {
  grid-column: 2;
  grid-row: 2;
}

.emotion-13 .ctaWrapperDivs {
  grid-column: 2;
  grid-row: 3;
  text-align: center;
}

.emotion-14 {
  box-sizing: content-box;
  text-align: center;
}

.emotion-15 {
  text-align: center;
  display: inline-block;
  max-width: min(67.5vw, 864px);
}

.emotion-15 .amp-cms--p {
  padding: 0;
  margin: 0;
  line-height: 0;
}

.emotion-15 .amp-cms--p>span {
  display: inline;
  white-space: break-spaces;
}

.emotion-15 .amp-cms--p a {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  position: relative;
  z-index: 2;
  pointer-events: auto;
}

.emotion-15 .amp-cms--p a:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-15 .amp-cms--legal-copy {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(0.78125vw, 10px));
  line-height: 1.5;
  letter-spacing: 0;
}

.emotion-15 sup {
  vertical-align: top;
  display: inline-block;
  margin-top: -0.20ex;
}

.emotion-15 sub {
  vertical-align: bottom;
  display: inline-block;
  margin-bottom: -0.20ex;
}

.emotion-15 .amp-cms--f-0 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.9375vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-15 .amp-cms--f-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.9375vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-15 .amp-cms--f-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.9375vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-15 .amp-cms--fn-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.9375vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-15 .amp-cms--body-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(1.40625vw, 18px));
  line-height: 1.5;
  letter-spacing: min(0.056249999999999994vw, 0.72px);
  font-weight: 500;
}

.emotion-15 .amp-cms--body-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(1.25vw, 16px));
  line-height: 1.5;
  letter-spacing: min(0.05vw, 0.64px);
  font-weight: 500;
}

.emotion-15 .amp-cms--body-3 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(1.09375vw, 14px));
  line-height: 1.5;
  letter-spacing: min(0.043750000000000004vw, 0.56px);
}

.emotion-15 .amp-cms--body-4 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(0.9375vw, 12px));
  line-height: 1.5;
  letter-spacing: 0;
}

.emotion-15 .amp-cms--body-5 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(0.78125vw, 10px));
  line-height: 1.5;
  letter-spacing: 0;
}

.emotion-15 .amp-cms--eyebrow-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(2.1875vw, 28px));
  line-height: 1;
  letter-spacing: min(0.08750000000000001vw, 1.12px);
  font-weight: 500;
}

.emotion-15 .amp-cms--eyebrow-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(1.7968749999999998vw, 23px));
  line-height: 1;
  letter-spacing: min(0.071875vw, 0.92px);
  font-weight: 500;
}

.emotion-15 .amp-cms--eyebrow-3 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(1.328125vw, 17px));
  line-height: 1;
  letter-spacing: min(0.053125000000000006vw, 0.68px);
  font-weight: 500;
}

.emotion-15 .amp-cms--headline-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(9.21875vw, 118px));
  line-height: 1;
  letter-spacing: 0;
  font-weight: 700;
}

.emotion-15 .amp-cms--headline-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(6.25vw, 80px));
  line-height: 1;
  letter-spacing: 0;
  font-weight: 700;
}

.emotion-15 .amp-cms--headline-3 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(5.625vw, 72px));
  line-height: 1;
  letter-spacing: 0;
  font-weight: 700;
}

.emotion-15 .amp-cms--headline-4 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(5vw, 64px));
  line-height: 1;
  letter-spacing: min(0.1vw, 1.28px);
  font-weight: 700;
}

.emotion-15 .amp-cms--headline-5 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(3.75vw, 48px));
  line-height: 1;
  letter-spacing: min(0.22499999999999998vw, 2.88px);
  font-weight: 700;
}

.emotion-15 .amp-cms--headline-6 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(3.125vw, 40px));
  line-height: 1.1;
  letter-spacing: min(0.1875vw, 2.4px);
  font-weight: 700;
}

.emotion-15 .amp-cms--headline-7 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(2.5vw, 32px));
  line-height: 1;
  letter-spacing: min(0.1vw, 1.28px);
  font-weight: 700;
}

.emotion-15 .amp-cms--headlineAlt-1 {
  color: #003764;
  font-size: max(14px, min(8.90625vw, 114px));
  line-height: 1;
  letter-spacing: min(-0.03125vw, -0.4px);
  font-weight: 400;
}

.emotion-15 .amp-cms--headlineAlt-2 {
  color: #003764;
  font-size: max(14px, min(6.5625vw, 84px));
  line-height: 1;
  letter-spacing: min(-0.03125vw, -0.4px);
  font-weight: 400;
}

.emotion-15 .amp-cms--headlineAlt-3 {
  color: #003764;
  font-size: max(14px, min(5vw, 64px));
  line-height: 1;
  letter-spacing: min(-0.03125vw, -0.4px);
  font-weight: 400;
}

.emotion-15 .amp-cms--headlineAlt-4 {
  color: #003764;
  font-size: max(14px, min(2.65625vw, 34px));
  line-height: 1;
  letter-spacing: min(-0.03125vw, -0.4px);
  font-weight: 400;
}

.emotion-15 .amp-cms--headlineAlt-5 {
  color: #003764;
  font-size: min(1.25vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-15 .amp-cms--headlineAlt-6 {
  color: #003764;
  font-size: min(1.25vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-15 .amp-cms--headlineAlt-7 {
  color: #003764;
  font-size: min(1.25vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-15 .amp-cms--promo-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(7.8125vw, 100px));
  line-height: 1;
  letter-spacing: min(0vw, 0px);
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-15 .amp-cms--promo-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(6.25vw, 80px));
  line-height: 1;
  letter-spacing: min(0vw, 0px);
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-15 .amp-cms--promoAlt-1 {
  color: #003764;
  font-size: min(1.25vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 600;
}

.emotion-15 .amp-cms--subhead-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(1.875vw, 24px));
  line-height: 1.5;
  letter-spacing: 0;
  font-weight: 500;
}

.emotion-15 .amp-cms--subhead-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(1.7187500000000002vw, 22px));
  line-height: 1.5;
  letter-spacing: 0;
  font-weight: 500;
}

.emotion-15 .amp-cms--subhead-3 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(1.5625vw, 20px));
  line-height: 1.5;
  letter-spacing: 0;
  font-weight: 500;
}

.emotion-16 {
  display: block;
  width: 100%;
  position: relative;
  z-index: 2;
  padding-top: 18px;
  text-align: center;
}

.emotion-17 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 700;
  letter-spacing: 1.05px;
  min-height: auto;
  max-height: auto;
  line-height: 1.8571428571428572;
  padding: 0;
  width: auto;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: left;
  border-left: none;
  border-right: none;
  border-radius: 0;
  background-color: transparent;
  border-bottom: 0;
  -webkit-box-pack: left;
  -ms-flex-pack: left;
  -webkit-justify-content: left;
  justify-content: left;
  color: #003764;
  height: auto;
  -webkit-text-decoration: underline;
  text-decoration: underline;
  text-underline-offset: 3.5px;
  text-decoration-thickness: 3px;
  -webkit-align-self: flex-start;
  -ms-flex-item-align: flex-start;
  align-self: flex-start;
  white-space: normal;
}

.emotion-17:focus {
  outline: none;
}

.emotion-17>span {
  padding: 1px 0;
}

.emotion-17:hover,
.emotion-17:focus {
  text-decoration-color: #003764;
}

.emotion-17:hover span,
.emotion-17:focus span {
  color: #FFFFFF;
  background-color: #003764;
}

.emotion-17:active {
  text-decoration-color: #000000;
}

.emotion-17:active>span {
  color: #FFFFFF;
  background-color: #000000;
}

.emotion-18 {
  box-sizing: border-box;
}

.emotion-24 {
  width: 100%;
  height: 100%;
  padding: 50px 60px;
  box-sizing: border-box;
  position: absolute;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  display: grid;
  grid-template-columns: repeat(3, minmax(min-content,max-content));
  -webkit-align-content: center;
  -ms-flex-line-pack: center;
  align-content: center;
  grid-template-rows: repeat(3, minmax(0, max-content));
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  justify-content: space-between;
}

.emotion-24 .rteWrapperDivs {
  grid-column: 3;
  grid-row: 3;
}

.emotion-24 .ctaWrapperDivs {
  grid-column: 1;
  grid-row: 3;
  text-align: end;
}

.emotion-25 {
  box-sizing: content-box;
  text-align: end;
}

.emotion-26 {
  text-align: end;
  display: inline-block;
  max-width: min(67.5vw, 864px);
}

.emotion-26 .amp-cms--p {
  padding: 0;
  margin: 0;
  line-height: 0;
}

.emotion-26 .amp-cms--p>span {
  display: inline;
  white-space: break-spaces;
}

.emotion-26 .amp-cms--p a {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  position: relative;
  z-index: 2;
  pointer-events: auto;
}

.emotion-26 .amp-cms--p a:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-26 .amp-cms--legal-copy {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(0.78125vw, 10px));
  line-height: 1.5;
  letter-spacing: 0;
}

.emotion-26 sup {
  vertical-align: top;
  display: inline-block;
  margin-top: -0.20ex;
}

.emotion-26 sub {
  vertical-align: bottom;
  display: inline-block;
  margin-bottom: -0.20ex;
}

.emotion-26 .amp-cms--f-0 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.9375vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-26 .amp-cms--f-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.9375vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-26 .amp-cms--f-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.9375vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-26 .amp-cms--fn-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.9375vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-26 .amp-cms--body-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(1.40625vw, 18px));
  line-height: 1.5;
  letter-spacing: min(0.056249999999999994vw, 0.72px);
  font-weight: 500;
}

.emotion-26 .amp-cms--body-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(1.25vw, 16px));
  line-height: 1.5;
  letter-spacing: min(0.05vw, 0.64px);
  font-weight: 500;
}

.emotion-26 .amp-cms--body-3 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(1.09375vw, 14px));
  line-height: 1.5;
  letter-spacing: min(0.043750000000000004vw, 0.56px);
}

.emotion-26 .amp-cms--body-4 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(0.9375vw, 12px));
  line-height: 1.5;
  letter-spacing: 0;
}

.emotion-26 .amp-cms--body-5 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(0.78125vw, 10px));
  line-height: 1.5;
  letter-spacing: 0;
}

.emotion-26 .amp-cms--eyebrow-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(2.1875vw, 28px));
  line-height: 1;
  letter-spacing: min(0.08750000000000001vw, 1.12px);
  font-weight: 500;
}

.emotion-26 .amp-cms--eyebrow-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(1.7968749999999998vw, 23px));
  line-height: 1;
  letter-spacing: min(0.071875vw, 0.92px);
  font-weight: 500;
}

.emotion-26 .amp-cms--eyebrow-3 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(1.328125vw, 17px));
  line-height: 1;
  letter-spacing: min(0.053125000000000006vw, 0.68px);
  font-weight: 500;
}

.emotion-26 .amp-cms--headline-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(9.21875vw, 118px));
  line-height: 1;
  letter-spacing: 0;
  font-weight: 700;
}

.emotion-26 .amp-cms--headline-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(6.25vw, 80px));
  line-height: 1;
  letter-spacing: 0;
  font-weight: 700;
}

.emotion-26 .amp-cms--headline-3 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(5.625vw, 72px));
  line-height: 1;
  letter-spacing: 0;
  font-weight: 700;
}

.emotion-26 .amp-cms--headline-4 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(5vw, 64px));
  line-height: 1;
  letter-spacing: min(0.1vw, 1.28px);
  font-weight: 700;
}

.emotion-26 .amp-cms--headline-5 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(3.75vw, 48px));
  line-height: 1;
  letter-spacing: min(0.22499999999999998vw, 2.88px);
  font-weight: 700;
}

.emotion-26 .amp-cms--headline-6 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(3.125vw, 40px));
  line-height: 1.1;
  letter-spacing: min(0.1875vw, 2.4px);
  font-weight: 700;
}

.emotion-26 .amp-cms--headline-7 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(2.5vw, 32px));
  line-height: 1;
  letter-spacing: min(0.1vw, 1.28px);
  font-weight: 700;
}

.emotion-26 .amp-cms--headlineAlt-1 {
  color: #003764;
  font-size: max(14px, min(8.90625vw, 114px));
  line-height: 1;
  letter-spacing: min(-0.03125vw, -0.4px);
  font-weight: 400;
}

.emotion-26 .amp-cms--headlineAlt-2 {
  color: #003764;
  font-size: max(14px, min(6.5625vw, 84px));
  line-height: 1;
  letter-spacing: min(-0.03125vw, -0.4px);
  font-weight: 400;
}

.emotion-26 .amp-cms--headlineAlt-3 {
  color: #003764;
  font-size: max(14px, min(5vw, 64px));
  line-height: 1;
  letter-spacing: min(-0.03125vw, -0.4px);
  font-weight: 400;
}

.emotion-26 .amp-cms--headlineAlt-4 {
  color: #003764;
  font-size: max(14px, min(2.65625vw, 34px));
  line-height: 1;
  letter-spacing: min(-0.03125vw, -0.4px);
  font-weight: 400;
}

.emotion-26 .amp-cms--headlineAlt-5 {
  color: #003764;
  font-size: min(1.25vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-26 .amp-cms--headlineAlt-6 {
  color: #003764;
  font-size: min(1.25vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-26 .amp-cms--headlineAlt-7 {
  color: #003764;
  font-size: min(1.25vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-26 .amp-cms--promo-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(7.8125vw, 100px));
  line-height: 1;
  letter-spacing: min(0vw, 0px);
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-26 .amp-cms--promo-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(6.25vw, 80px));
  line-height: 1;
  letter-spacing: min(0vw, 0px);
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-26 .amp-cms--promoAlt-1 {
  color: #003764;
  font-size: min(1.25vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 600;
}

.emotion-26 .amp-cms--subhead-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(1.875vw, 24px));
  line-height: 1.5;
  letter-spacing: 0;
  font-weight: 500;
}

.emotion-26 .amp-cms--subhead-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(1.7187500000000002vw, 22px));
  line-height: 1.5;
  letter-spacing: 0;
  font-weight: 500;
}

.emotion-26 .amp-cms--subhead-3 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(1.5625vw, 20px));
  line-height: 1.5;
  letter-spacing: 0;
  font-weight: 500;
}

.emotion-27 {
  display: block;
  width: 100%;
  position: relative;
  z-index: 2;
  padding-top: 18px;
  text-align: left;
}

.emotion-28 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 700;
  letter-spacing: 1.6800000000000002px;
  min-height: 48px;
  max-height: auto;
  line-height: 1.1428571428571428;
  padding: 14px 22px;
  width: auto;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  background-color: #FFFFFF;
  color: #003764;
  border-color: #003764;
  -webkit-align-self: flex-start;
  -ms-flex-item-align: flex-start;
  align-self: flex-start;
  white-space: normal;
  width: 335px;
}

.emotion-28:focus {
  outline: none;
}

.emotion-28>span {
  padding: 1px 0;
}

.emotion-28:hover,
.emotion-28:focus {
  color: #FFFFFF;
  background-color: #003764;
  border-color: #003764;
}

.emotion-28:active {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  color: #FFFFFF;
  background-color: #000000;
  border-color: #000000;
}

.emotion-31 {
  overflow: hidden;
  position: relative;
  box-sizing: border-box;
  aspect-ratio: 1280/400;
}

.emotion-32 {
  -webkit-background-size: cover;
  background-size: cover;
  aspect-ratio: 1280/400;
  overflow: hidden;
}

.emotion-33 {
  width: 100%;
  aspect-ratio: 1280/400;
  object-fit: cover;
}

.emotion-36 {
  width: 100%;
  height: 100%;
  padding: 50px 60px;
  box-sizing: border-box;
  position: absolute;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  display: grid;
  grid-template-columns: repeat(3, minmax(min-content,max-content));
  -webkit-align-content: center;
  -ms-flex-line-pack: center;
  align-content: center;
  grid-template-rows: repeat(3, minmax(0, max-content));
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  justify-content: space-between;
}

.emotion-36 .rteWrapperDivs {
  grid-column: 2;
  grid-row: 2;
}

.emotion-36 .ctaWrapperDivs {
  grid-column: 2;
  grid-row: 2;
  text-align: start;
}

.emotion-46 {
  width: 100%;
  height: 100%;
  padding: 50px 60px;
  box-sizing: border-box;
  position: absolute;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  display: grid;
  grid-template-columns: repeat(3, minmax(min-content,max-content));
  -webkit-align-content: center;
  -ms-flex-line-pack: center;
  align-content: center;
  grid-template-rows: max-content max-content 1fr;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  justify-content: space-between;
}

.emotion-46 .rteWrapperDivs {
  grid-column: 1;
  grid-row: 1;
}

.emotion-46 .ctaWrapperDivs {
  grid-column: 1;
  grid-row: 2;
  text-align: start;
}

.emotion-47 {
  box-sizing: content-box;
  text-align: start;
}

.emotion-48 {
  text-align: start;
  display: inline-block;
  max-width: 864px;
  width: calc(min(100vw, 1280px) * 0.675);
}

.emotion-48 .amp-cms--p {
  padding: 0;
  margin: 0;
  line-height: 0;
}

.emotion-48 .amp-cms--p>span {
  display: inline;
  white-space: break-spaces;
}

.emotion-48 .amp-cms--p a {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  position: relative;
  z-index: 2;
  pointer-events: auto;
}

.emotion-48 .amp-cms--p a:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-48 .amp-cms--legal-copy {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(0.78125vw, 10px));
  line-height: 1.5;
  letter-spacing: 0;
}

.emotion-48 sup {
  vertical-align: top;
  display: inline-block;
  margin-top: -0.20ex;
}

.emotion-48 sub {
  vertical-align: bottom;
  display: inline-block;
  margin-bottom: -0.20ex;
}

.emotion-48 .amp-cms--f-0 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.9375vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-48 .amp-cms--f-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.9375vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-48 .amp-cms--f-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.9375vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-48 .amp-cms--fn-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.9375vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-48 .amp-cms--body-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(1.40625vw, 18px));
  line-height: 1.5;
  letter-spacing: min(0.056249999999999994vw, 0.72px);
  font-weight: 500;
}

.emotion-48 .amp-cms--body-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(1.25vw, 16px));
  line-height: 1.5;
  letter-spacing: min(0.05vw, 0.64px);
  font-weight: 500;
}

.emotion-48 .amp-cms--body-3 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(1.09375vw, 14px));
  line-height: 1.5;
  letter-spacing: min(0.043750000000000004vw, 0.56px);
}

.emotion-48 .amp-cms--body-4 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(0.9375vw, 12px));
  line-height: 1.5;
  letter-spacing: 0;
}

.emotion-48 .amp-cms--body-5 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(0.78125vw, 10px));
  line-height: 1.5;
  letter-spacing: 0;
}

.emotion-48 .amp-cms--eyebrow-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(2.1875vw, 28px));
  line-height: 1;
  letter-spacing: min(0.08750000000000001vw, 1.12px);
  font-weight: 500;
}

.emotion-48 .amp-cms--eyebrow-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(1.7968749999999998vw, 23px));
  line-height: 1;
  letter-spacing: min(0.071875vw, 0.92px);
  font-weight: 500;
}

.emotion-48 .amp-cms--eyebrow-3 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(1.328125vw, 17px));
  line-height: 1;
  letter-spacing: min(0.053125000000000006vw, 0.68px);
  font-weight: 500;
}

.emotion-48 .amp-cms--headline-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(9.21875vw, 118px));
  line-height: 1;
  letter-spacing: 0;
  font-weight: 700;
}

.emotion-48 .amp-cms--headline-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(6.25vw, 80px));
  line-height: 1;
  letter-spacing: 0;
  font-weight: 700;
}

.emotion-48 .amp-cms--headline-3 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(5.625vw, 72px));
  line-height: 1;
  letter-spacing: 0;
  font-weight: 700;
}

.emotion-48 .amp-cms--headline-4 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(5vw, 64px));
  line-height: 1;
  letter-spacing: min(0.1vw, 1.28px);
  font-weight: 700;
}

.emotion-48 .amp-cms--headline-5 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(3.75vw, 48px));
  line-height: 1;
  letter-spacing: min(0.22499999999999998vw, 2.88px);
  font-weight: 700;
}

.emotion-48 .amp-cms--headline-6 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(3.125vw, 40px));
  line-height: 1.1;
  letter-spacing: min(0.1875vw, 2.4px);
  font-weight: 700;
}

.emotion-48 .amp-cms--headline-7 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(2.5vw, 32px));
  line-height: 1;
  letter-spacing: min(0.1vw, 1.28px);
  font-weight: 700;
}

.emotion-48 .amp-cms--headlineAlt-1 {
  color: #003764;
  font-size: max(14px, min(8.90625vw, 114px));
  line-height: 1;
  letter-spacing: min(-0.03125vw, -0.4px);
  font-weight: 400;
}

.emotion-48 .amp-cms--headlineAlt-2 {
  color: #003764;
  font-size: max(14px, min(6.5625vw, 84px));
  line-height: 1;
  letter-spacing: min(-0.03125vw, -0.4px);
  font-weight: 400;
}

.emotion-48 .amp-cms--headlineAlt-3 {
  color: #003764;
  font-size: max(14px, min(5vw, 64px));
  line-height: 1;
  letter-spacing: min(-0.03125vw, -0.4px);
  font-weight: 400;
}

.emotion-48 .amp-cms--headlineAlt-4 {
  color: #003764;
  font-size: max(14px, min(2.65625vw, 34px));
  line-height: 1;
  letter-spacing: min(-0.03125vw, -0.4px);
  font-weight: 400;
}

.emotion-48 .amp-cms--headlineAlt-5 {
  color: #003764;
  font-size: min(1.25vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-48 .amp-cms--headlineAlt-6 {
  color: #003764;
  font-size: min(1.25vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-48 .amp-cms--headlineAlt-7 {
  color: #003764;
  font-size: min(1.25vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-48 .amp-cms--promo-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(7.8125vw, 100px));
  line-height: 1;
  letter-spacing: min(0vw, 0px);
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-48 .amp-cms--promo-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(6.25vw, 80px));
  line-height: 1;
  letter-spacing: min(0vw, 0px);
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-48 .amp-cms--promoAlt-1 {
  color: #003764;
  font-size: min(1.25vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 600;
}

.emotion-48 .amp-cms--subhead-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(1.875vw, 24px));
  line-height: 1.5;
  letter-spacing: 0;
  font-weight: 500;
}

.emotion-48 .amp-cms--subhead-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(1.7187500000000002vw, 22px));
  line-height: 1.5;
  letter-spacing: 0;
  font-weight: 500;
}

.emotion-48 .amp-cms--subhead-3 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(1.5625vw, 20px));
  line-height: 1.5;
  letter-spacing: 0;
  font-weight: 500;
}

<div>
  <div
    style="font-family: var(--font-family-font1),sans-serif;"
  >
    <div
      class="emotion-0"
    >
      <section
        class="emotion-1"
      >
        <div
          class="emotion-2"
        >
          <nav
            class="emotion-3"
          >
            <div
              class="emotion-4"
            >
              <div
                class="slick-slider slick-initialized"
                dir="ltr"
              >
                <button
                  aria-label="Previous"
                  class="slick-prev slick-arrow slick-prev slick-disabled"
                  data-role="none"
                  disabled=""
                >
                  <span
                    aria-hidden="true"
                    class="emotion-5"
                  >
                    <svg
                      viewBox="0 0 13.29 8.07"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M6.13 7.86a.73.73 0 001.04 0l5.92-5.92a.73.73 0 000-1.03l-.7-.7a.73.73 0 00-1.03 0l-4.71 4.7L1.94.2A.73.73 0 00.9.21l-.69.7a.73.73 0 000 1.03z"
                        fill="#FFFFFF"
                      />
                    </svg>
                  </span>
                </button>
                <div
                  class="slick-list"
                >
                  <div
                    class="slick-track"
                    style="opacity: 1; transform: translate3d(0px, 0px, 0px);"
                  >
                    <div
                      aria-hidden="false"
                      class="slick-slide slick-active slick-current"
                      data-index="0"
                      style="outline: none; width: 0px;"
                      tabindex="-1"
                    >
                      <div>
                        <div
                          class="emotion-6"
                        >
                          <div
                            class="emotion-7"
                          >
                            <div
                              class="emotion-8"
                              height="400"
                              width="640"
                            >
                              <div
                                class="emotion-9"
                                data-testid="product-card-image"
                              >
                                <img
                                  alt="hello lady"
                                  class="emotion-10"
                                  src="https://2hmc8flz4ofg1dy89cc38f2xm.staging.bigcontent.io/i/athleta/530895_012_VIPW_AT_WMN_LS_150_SP20_SW_3_0882?fmt=webp"
                                />
                              </div>
                            </div>
                            <a
                              class="emotion-11"
                              href="/linkToNewArrivals"
                              tabindex="-1"
                              target="_self"
                              title=""
                            />
                            <div
                              class="emotion-12"
                              data-testid="absolute-wrapper"
                            >
                              <div
                                class="emotion-13"
                              >
                                <div
                                  class="rteWrapperDivs emotion-14"
                                >
                                  <div
                                    class="emotion-15"
                                  >
                                    <div>
                                      <p
                                        class="amp-cms--p"
                                      >
                                        <span
                                          class="amp-cms--subhead-1"
                                        >
                                          Lorem Ipsum
                                        </span>
                                      </p>
                                      <p
                                        class="amp-cms--p"
                                      >
                                        <span
                                          class="amp-cms--body-1"
                                        >
                                          dolor sit amet, consectetur adipiscing elit.
                                        </span>
                                      </p>
                                    </div>
                                  </div>
                                </div>
                                <div
                                  class="ctaWrapperDivs emotion-16"
                                >
                                  <a
                                    class="emotion-17"
                                    color="dark"
                                    href="/buyIt"
                                  >
                                    <span
                                      class="emotion-18"
                                    >
                                      new arrivals
                                    </span>
                                  </a>
                                </div>
                              </div>
                            </div>
                          </div>
                          <div
                            class="emotion-7"
                          >
                            <div
                              class="emotion-8"
                              height="400"
                              width="640"
                            >
                              <div
                                class="emotion-9"
                                data-testid="product-card-image"
                              >
                                <img
                                  alt="hello lady"
                                  class="emotion-10"
                                  src="https://2hmc8flz4ofg1dy89cc38f2xm.staging.bigcontent.io/i/athleta/woman-black_hat?fmt=webp"
                                />
                              </div>
                            </div>
                            <div
                              class="emotion-12"
                              data-testid="absolute-wrapper"
                            >
                              <div
                                class="emotion-24"
                              >
                                <div
                                  class="rteWrapperDivs emotion-25"
                                >
                                  <div
                                    class="emotion-26"
                                  >
                                    <div>
                                      <h2
                                        class="amp-cms--p"
                                      >
                                        <span
                                          class="amp-cms--body-1"
                                        >
                                          OUR MOST LOVED STYLES
                                        </span>
                                      </h2>
                                    </div>
                                  </div>
                                </div>
                                <div
                                  class="ctaWrapperDivs emotion-27"
                                >
                                  <a
                                    class="emotion-28"
                                    color="dark"
                                    href="/buyIt"
                                  >
                                    new arrivals
                                  </a>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div
                      aria-hidden="true"
                      class="slick-slide"
                      data-index="1"
                      style="outline: none; width: 0px;"
                      tabindex="-1"
                    >
                      <div>
                        <div
                          class="emotion-6"
                        >
                          <div
                            class="emotion-7"
                          >
                            <div
                              class="emotion-31"
                              height="400"
                              width="1280"
                            >
                              <div
                                class="emotion-32"
                                data-testid="product-card-image"
                              >
                                <img
                                  alt="image only"
                                  class="emotion-33"
                                  src="https://2hmc8flz4ofg1dy89cc38f2xm.staging.bigcontent.io/i/athleta/HOL2_NA_GiftShop_ISM_XL@2x?fmt=webp"
                                />
                              </div>
                            </div>
                            <a
                              class="emotion-11"
                              href="/productsWeLove"
                              tabindex="-1"
                              target="_self"
                              title="Middle link"
                            />
                            <div
                              class="emotion-12"
                              data-testid="absolute-wrapper"
                            >
                              <div
                                class="emotion-36"
                              >
                                <div
                                  class="ctaWrapperDivs emotion-16"
                                >
                                  <a
                                    class="emotion-28"
                                    color="dark"
                                    href="/ss"
                                  >
                                    Get a life
                                  </a>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div
                      aria-hidden="true"
                      class="slick-slide"
                      data-index="2"
                      style="outline: none; width: 0px;"
                      tabindex="-1"
                    >
                      <div>
                        <div
                          class="emotion-6"
                        >
                          <div
                            class="emotion-7"
                          >
                            <div
                              class="emotion-31"
                              height="400"
                              width="1280"
                            >
                              <div
                                class="emotion-32"
                                data-testid="product-card-image"
                              >
                                <img
                                  alt="hi"
                                  class="emotion-33"
                                  src="https://2hmc8flz4ofg1dy89cc38f2xm.staging.bigcontent.io/i/athleta/985360_032_SFMG_AT_WMN_GS_70_HO21_MU_1_8109copy?fmt=webp"
                                />
                              </div>
                            </div>
                            <a
                              class="emotion-11"
                              href="/lastLink"
                              tabindex="-1"
                              target="_self"
                              title="lastLink"
                            />
                            <div
                              class="emotion-12"
                              data-testid="absolute-wrapper"
                            >
                              <div
                                class="emotion-46"
                              >
                                <div
                                  class="rteWrapperDivs emotion-47"
                                >
                                  <div
                                    class="emotion-48"
                                  >
                                    <div>
                                      <p
                                        class="amp-cms--p"
                                      >
                                        <span
                                          class="amp-cms--headline-6"
                                        >
                                          Lorem Ipsum Dolor Sit Amet,
                                        </span>
                                      </p>
                                      <p
                                        class="amp-cms--p"
                                      >
                                        <span
                                          class="amp-cms--body-1"
                                        >
                                          consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam.
                                        </span>
                                      </p>
                                    </div>
                                  </div>
                                </div>
                                <div
                                  class="ctaWrapperDivs emotion-27"
                                >
                                  <a
                                    class="emotion-28"
                                    color="dark"
                                    href="/ss"
                                  >
                                    Get a life
                                  </a>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <button
                  aria-label="Next"
                  class="slick-next slick-arrow slick-next"
                  data-role="none"
                >
                  <span
                    aria-hidden="true"
                    class="emotion-5"
                  >
                    <svg
                      viewBox="0 0 13.29 8.07"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M6.13 7.86a.73.73 0 001.04 0l5.92-5.92a.73.73 0 000-1.03l-.7-.7a.73.73 0 00-1.03 0l-4.71 4.7L1.94.2A.73.73 0 00.9.21l-.69.7a.73.73 0 000 1.03z"
                        fill="#FFFFFF"
                      />
                    </svg>
                  </span>
                </button>
                <ul
                  class="slick-dots"
                  style="display: block;"
                >
                  <li
                    class="slick-active"
                  >
                    <button>
                      1
                    </button>
                  </li>
                  <li
                    class=""
                  >
                    <button>
                      2
                    </button>
                  </li>
                  <li
                    class=""
                  >
                    <button>
                      3
                    </button>
                  </li>
                </ul>
              </div>
            </div>
          </nav>
        </div>
      </section>
    </div>
  </div>
</div>
`;

exports[`CategoryBannerVariableHeightCarousel OldNavy Content Type should render isPersistentContent as true 1`] = `
.emotion-0 {
  margin-left: auto;
  margin-right: auto;
  max-width: 1280px;
}

.emotion-2 {
  position: relative;
}

.emotion-3 {
  height: 100%;
  width: 100%;
  position: absolute;
  top: 0;
  left: 0;
}

.emotion-4 {
  width: 100%;
  height: 100%;
  padding: 50px 60px;
  box-sizing: border-box;
  position: absolute;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  display: grid;
  grid-template-columns: repeat(3, minmax(min-content,max-content));
  -webkit-align-content: center;
  -ms-flex-line-pack: center;
  align-content: center;
  grid-template-rows: 1fr max-content 1fr;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  justify-content: space-between;
}

.emotion-4 .rteWrapperDivs {
  grid-column: 2;
  grid-row: 2;
}

.emotion-4 .ctaWrapperDivs {
  grid-column: 2;
  grid-row: 3;
  text-align: center;
}

.emotion-5 {
  box-sizing: content-box;
  text-align: center;
}

.emotion-6 {
  text-align: center;
  display: inline-block;
}

.emotion-6 .amp-cms--p {
  padding: 0;
  margin: 0;
  line-height: 0;
}

.emotion-6 .amp-cms--p>span {
  display: inline;
  white-space: break-spaces;
}

.emotion-6 .amp-cms--p a {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  position: relative;
  z-index: 2;
  pointer-events: auto;
}

.emotion-6 .amp-cms--p a:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-6 .amp-cms--legal-copy {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 10px;
  line-height: 1.5;
  letter-spacing: 0;
}

.emotion-6 sup {
  vertical-align: top;
  display: inline-block;
  margin-top: -0.20ex;
}

.emotion-6 sub {
  vertical-align: bottom;
  display: inline-block;
  margin-bottom: -0.20ex;
}

.emotion-6 .amp-cms--f-0 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-6 .amp-cms--f-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-6 .amp-cms--f-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-6 .amp-cms--fn-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-6 .amp-cms--body-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 18px;
  line-height: 1.5;
  letter-spacing: 0.72px;
  font-weight: 500;
}

.emotion-6 .amp-cms--body-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 16px;
  line-height: 1.5;
  letter-spacing: 0.64px;
  font-weight: 500;
}

.emotion-6 .amp-cms--body-3 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 14px;
  line-height: 1.5;
  letter-spacing: 0.56px;
}

.emotion-6 .amp-cms--body-4 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.5;
  letter-spacing: 0;
}

.emotion-6 .amp-cms--body-5 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 10px;
  line-height: 1.5;
  letter-spacing: 0;
}

.emotion-6 .amp-cms--eyebrow-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 28px;
  line-height: 1;
  letter-spacing: 1.12px;
  font-weight: 500;
}

.emotion-6 .amp-cms--eyebrow-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 23px;
  line-height: 1;
  letter-spacing: 0.92px;
  font-weight: 500;
}

.emotion-6 .amp-cms--eyebrow-3 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 17px;
  line-height: 1;
  letter-spacing: 0.68px;
  font-weight: 500;
}

.emotion-6 .amp-cms--headline-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 118px;
  line-height: 1;
  letter-spacing: 0;
  font-weight: 700;
}

.emotion-6 .amp-cms--headline-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 80px;
  line-height: 1;
  letter-spacing: 0;
  font-weight: 700;
}

.emotion-6 .amp-cms--headline-3 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 72px;
  line-height: 1;
  letter-spacing: 0;
  font-weight: 700;
}

.emotion-6 .amp-cms--headline-4 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 64px;
  line-height: 1;
  letter-spacing: 1.28px;
  font-weight: 700;
}

.emotion-6 .amp-cms--headline-5 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 48px;
  line-height: 1;
  letter-spacing: 2.88px;
  font-weight: 700;
}

.emotion-6 .amp-cms--headline-6 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 40px;
  line-height: 1.1;
  letter-spacing: 2.4px;
  font-weight: 700;
}

.emotion-6 .amp-cms--headline-7 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 32px;
  line-height: 1;
  letter-spacing: 1.28px;
  font-weight: 700;
}

.emotion-6 .amp-cms--headlineAlt-1 {
  color: #003764;
  font-size: 114px;
  line-height: 1;
  letter-spacing: -0.4px;
  font-weight: 400;
}

.emotion-6 .amp-cms--headlineAlt-2 {
  color: #003764;
  font-size: 84px;
  line-height: 1;
  letter-spacing: -0.4px;
  font-weight: 400;
}

.emotion-6 .amp-cms--headlineAlt-3 {
  color: #003764;
  font-size: 64px;
  line-height: 1;
  letter-spacing: -0.4px;
  font-weight: 400;
}

.emotion-6 .amp-cms--headlineAlt-4 {
  color: #003764;
  font-size: 34px;
  line-height: 1;
  letter-spacing: -0.4px;
  font-weight: 400;
}

.emotion-6 .amp-cms--headlineAlt-5 {
  color: #003764;
  font-size: 16px;
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-6 .amp-cms--headlineAlt-6 {
  color: #003764;
  font-size: 16px;
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-6 .amp-cms--headlineAlt-7 {
  color: #003764;
  font-size: 16px;
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-6 .amp-cms--promo-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 100px;
  line-height: 1;
  letter-spacing: 0px;
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-6 .amp-cms--promo-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 80px;
  line-height: 1;
  letter-spacing: 0px;
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-6 .amp-cms--promoAlt-1 {
  color: #003764;
  font-size: 16px;
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 600;
}

.emotion-6 .amp-cms--subhead-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 24px;
  line-height: 1.5;
  letter-spacing: 0;
  font-weight: 500;
}

.emotion-6 .amp-cms--subhead-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 22px;
  line-height: 1.5;
  letter-spacing: 0;
  font-weight: 500;
}

.emotion-6 .amp-cms--subhead-3 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 20px;
  line-height: 1.5;
  letter-spacing: 0;
  font-weight: 500;
}

.emotion-7 {
  display: block;
  width: 100%;
  position: relative;
  z-index: 2;
  padding-top: 18px;
  text-align: center;
}

.emotion-8 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 700;
  letter-spacing: 1.05px;
  min-height: auto;
  max-height: auto;
  line-height: 1.8571428571428572;
  padding: 0;
  width: auto;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: left;
  border-left: none;
  border-right: none;
  border-radius: 0;
  background-color: transparent;
  border-bottom: 0;
  -webkit-box-pack: left;
  -ms-flex-pack: left;
  -webkit-justify-content: left;
  justify-content: left;
  color: #003764;
  height: auto;
  -webkit-text-decoration: underline;
  text-decoration: underline;
  text-underline-offset: 3.5px;
  text-decoration-thickness: 3px;
  -webkit-align-self: flex-start;
  -ms-flex-item-align: flex-start;
  align-self: flex-start;
  white-space: normal;
}

.emotion-8:focus {
  outline: none;
}

.emotion-8>span {
  padding: 1px 0;
}

.emotion-8:hover,
.emotion-8:focus {
  text-decoration-color: #003764;
}

.emotion-8:hover span,
.emotion-8:focus span {
  color: #FFFFFF;
  background-color: #003764;
}

.emotion-8:active {
  text-decoration-color: #000000;
}

.emotion-8:active>span {
  color: #FFFFFF;
  background-color: #000000;
}

.emotion-9 {
  box-sizing: border-box;
}

.emotion-10 {
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-10 .slick-list {
  overflow: hidden;
}

.emotion-10 .slick-track {
  width: -webkit-max-content!important;
  width: -moz-max-content!important;
  width: max-content!important;
}

.emotion-10 button.slick-next.slick-arrow.slick-next,
.emotion-10 button.slick-prev.slick-arrow.slick-prev {
  z-index: 2;
  top: min(15.625vw, 50%);
  height: 44px;
  width: 44px;
  background: #003764;
  opacity: 75%;
}

.emotion-10 button.slick-next.slick-arrow.slick-next>span,
.emotion-10 button.slick-prev.slick-arrow.slick-prev>span {
  margin: auto;
}

.emotion-10 button.slick-next.slick-arrow.slick-next svg,
.emotion-10 button.slick-prev.slick-arrow.slick-prev svg {
  margin: auto;
  height: 25px;
  width: 14px;
}

.emotion-10 button.slick-next.slick-arrow.slick-next span svg path,
.emotion-10 button.slick-prev.slick-arrow.slick-prev span svg path {
  fill: #ffffff;
}

.emotion-10 button.slick-next.slick-arrow.slick-next:hover,
.emotion-10 button.slick-prev.slick-arrow.slick-prev:hover {
  opacity: 100%;
}

.emotion-10 button.slick-next.slick-arrow.slick-next:focus,
.emotion-10 button.slick-prev.slick-arrow.slick-prev:focus {
  background: #003764;
}

.emotion-10 button.slick-next.slick-arrow.slick-next:focus-visible,
.emotion-10 button.slick-prev.slick-arrow.slick-prev:focus-visible {
  outline: auto;
}

.emotion-10 .slick-slide {
  margin: 0;
  width: auto;
}

.emotion-10 .slick-disabled {
  display: none!important;
}

.emotion-10 .slick-next {
  left: calc(100% - 44px);
}

.emotion-10 .slick-slide:first-of-type a>div:nth-of-type(2)>div>div:nth-of-type(2) {
  border-left: none;
}

.emotion-10 .slick-slide>div:first-of-type {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-10 .slick-slide>div:first-of-type>div:first-of-type {
  width: 100%;
}

.emotion-10 .slick-slider ul.slick-dots {
  bottom: 8px;
  width: unset;
  height: 44px;
  margin-left: 50%;
  -webkit-transform: translateX(-50%);
  -moz-transform: translateX(-50%);
  -ms-transform: translateX(-50%);
  transform: translateX(-50%);
  -webkit-align-content: center;
  -ms-flex-line-pack: center;
  align-content: center;
}

.emotion-10 .slick-slider .slick-dots li.slick-active button:before {
  background-color: #003764;
}

.emotion-10 .slick-slider ul.slick-dots button:before {
  width: 10px;
  height: 10px;
  opacity: 1;
  background-color: #B2B2B2;
}

.emotion-11 {
  position: relative;
}

.emotion-11 .slick-slider {
  position: relative;
  display: block;
  box-sizing: border-box;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  -webkit-touch-callout: none;
  touch-action: pan-y;
  -webkit-tap-highlight-color: transparent;
  scroll-behavior: smooth;
}

.emotion-11 .slick-slider .slick-track,
.emotion-11 .slick-slider .slick-list {
  -webkit-transform: translate3d(0, 0, 0);
  -moz-transform: translate3d(0, 0, 0);
  -ms-transform: translate3d(0, 0, 0);
  transform: translate3d(0, 0, 0);
}

.emotion-11 .slick-list {
  position: relative;
  display: block;
  overflow-x: hidden;
  overflow-y: hidden;
  margin: 0;
  padding: 0;
}

.emotion-11 .slick-list:focus {
  outline: none;
}

.emotion-11 .slick-list.dragging {
  cursor: pointer;
  cursor: hand;
}

.emotion-11 .slick-track {
  position: relative;
  top: 0;
  left: 0;
  display: block;
  margin-left: auto;
  margin-right: auto;
}

.emotion-11 .slick-track:before,
.emotion-11 .slick-track:after {
  display: table;
  content: "";
}

.emotion-11 .slick-track:after {
  clear: both;
}

.emotion-11 .slick-slide {
  display: none;
  float: left;
  height: 100%;
  min-height: 1px;
}

.emotion-11 .slick-slide img {
  display: block;
}

.emotion-11 .slick-slide.slick-loading img {
  display: none;
}

.emotion-11 .slick-slide.dragging img {
  pointer-events: none;
}

.emotion-11 [dir="rtl"] .slick-slide {
  float: right;
}

.emotion-11 .slick-initialized .slick-slide,
.emotion-11 .slick-vertical .slick-slide {
  display: block;
}

.emotion-11 .slick-vertical .slick-slide {
  border: 1px solid transparent;
  height: auto;
}

.emotion-11 .slick-loading .slick-track,
.emotion-11 .slick-loading .slick-slide {
  visibility: hidden;
}

.emotion-11 .slick-loading .slick-list {
  background: #fff url("./ajax-loader.gif") center center no-repeat;
}

.emotion-11 .slick-arrow.slick-hidden {
  display: none;
}

.emotion-11 .slick-prev,
.emotion-11 .slick-next {
  font-size: 0;
  line-height: 0;
  position: absolute;
  top: 50%;
  display: block;
  padding: 0;
  cursor: pointer;
  color: transparent;
  border: none;
  outline: none;
  background: transparent;
}

.emotion-11 .slick-prev:hover,
.emotion-11 .slick-next:hover,
.emotion-11 .slick-prev:focus,
.emotion-11 .slick-next:focus {
  color: transparent;
  outline: none;
  background: transparent;
  opacity: 1;
}

.emotion-11 .slick-prev.slick-disabled,
.emotion-11 .slick-next.slick-disabled {
  opacity: 0.25;
}

.emotion-11 .slick-prev {
  left: -0;
  -webkit-transform: translate(0, -50%) rotate(90deg);
  -moz-transform: translate(0, -50%) rotate(90deg);
  -ms-transform: translate(0, -50%) rotate(90deg);
  transform: translate(0, -50%) rotate(90deg);
}

.emotion-11 .slick-prev span {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-11 [dir="rtl"] .slick-prev {
  right: -0;
  left: auto;
}

.emotion-11 .slick-next {
  right: -0;
  -webkit-transform: translate(0, -50%) rotate(-90deg);
  -moz-transform: translate(0, -50%) rotate(-90deg);
  -ms-transform: translate(0, -50%) rotate(-90deg);
  transform: translate(0, -50%) rotate(-90deg);
}

.emotion-11 .slick-next span {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-11 [dir="rtl"] .slick-next {
  right: auto;
  left: -0;
}

.emotion-11 .slick-dotted.slick-slider {
  margin-bottom: 30px;
}

.emotion-11 .slick-dots {
  position: absolute;
  bottom: -25px;
  display: block;
  width: 100%;
  padding: 0;
  margin: 0;
  list-style: none;
  text-align: center;
}

.emotion-11 .slick-dots li {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: 20px;
  height: 20px;
  margin: 0 5px;
  padding: 0;
  cursor: pointer;
}

.emotion-11 .slick-dots li.slick-active button:before {
  opacity: 0.75;
  color: black;
}

.emotion-11 .slick-dots li button {
  font-size: 0;
  line-height: 0;
  display: block;
  padding: 5px;
  cursor: pointer;
  color: transparent;
  border: 0;
  outline: none;
  background: transparent;
}

.emotion-11 .slick-dots li button:hover,
.emotion-11 .slick-dots li button:focus {
  outline: none;
}

.emotion-11 .slick-dots li button:hover:before,
.emotion-11 .slick-dots li button:focus:before,
.emotion-11 .slick-dots li button:hover:before,
.emotion-11 .slick-dots li button:focus:before {
  opacity: 1;
}

.emotion-11 .slick-dots li button:before {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 5px;
  height: 5px;
  content: "";
  opacity: 0.25;
  background-color: black;
  border-radius: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-12 {
  display: inline-block;
  height: 2rem;
  width: 2rem;
  min-height: 2rem;
  min-width: 2rem;
}

.emotion-12 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-13 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-14 {
  width: 100%;
  position: relative;
}

.emotion-15 {
  overflow: hidden;
  position: relative;
  box-sizing: border-box;
  aspect-ratio: 640/400;
}

.emotion-16 {
  -webkit-background-size: cover;
  background-size: cover;
  aspect-ratio: 640/400;
  overflow: hidden;
}

.emotion-17 {
  width: 100%;
  aspect-ratio: 640/400;
  object-fit: cover;
}

.emotion-18 {
  width: 100%;
  height: 100%;
  position: absolute;
  left: 0;
  top: 0;
  z-index: 1;
}

.emotion-25 {
  overflow: hidden;
  position: relative;
  box-sizing: border-box;
  aspect-ratio: 1280/400;
}

.emotion-26 {
  -webkit-background-size: cover;
  background-size: cover;
  aspect-ratio: 1280/400;
  overflow: hidden;
}

.emotion-27 {
  width: 100%;
  aspect-ratio: 1280/400;
  object-fit: cover;
}

<div>
  <div
    style="font-family: var(--font-family-font1),sans-serif;"
  >
    <div
      class="emotion-0"
    >
      <section
        class="emotion-1"
      >
        <div
          class="emotion-2"
        >
          <div>
            <div
              class="emotion-3"
            >
              <div
                class="emotion-4"
              >
                <div
                  class="rteWrapperDivs emotion-5"
                >
                  <div
                    class="emotion-6"
                  >
                    <div>
                      <p
                        class="amp-cms--p"
                      >
                        <span
                          class="amp-cms--subhead-1"
                        >
                          Lorem Ipsum
                        </span>
                      </p>
                      <p
                        class="amp-cms--p"
                      >
                        <span
                          class="amp-cms--body-1"
                        >
                          dolor sit amet, consectetur adipiscing elit.
                        </span>
                      </p>
                    </div>
                  </div>
                </div>
                <div
                  class="ctaWrapperDivs emotion-7"
                >
                  <a
                    class="emotion-8"
                    color="dark"
                    href="/buyIt"
                  >
                    <span
                      class="emotion-9"
                    >
                      new arrivals
                    </span>
                  </a>
                </div>
              </div>
            </div>
          </div>
          <nav
            class="emotion-10"
          >
            <div
              class="emotion-11"
            >
              <div
                class="slick-slider slick-initialized"
                dir="ltr"
              >
                <button
                  aria-label="Previous"
                  class="slick-prev slick-arrow slick-prev slick-disabled"
                  data-role="none"
                  disabled=""
                >
                  <span
                    aria-hidden="true"
                    class="emotion-12"
                  >
                    <svg
                      viewBox="0 0 13.29 8.07"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M6.13 7.86a.73.73 0 001.04 0l5.92-5.92a.73.73 0 000-1.03l-.7-.7a.73.73 0 00-1.03 0l-4.71 4.7L1.94.2A.73.73 0 00.9.21l-.69.7a.73.73 0 000 1.03z"
                        fill="#003764"
                      />
                    </svg>
                  </span>
                </button>
                <div
                  class="slick-list"
                >
                  <div
                    class="slick-track"
                    style="opacity: 1; transform: translate3d(0px, 0px, 0px);"
                  >
                    <div
                      aria-hidden="false"
                      class="slick-slide slick-active slick-current"
                      data-index="0"
                      style="outline: none; width: 0px;"
                      tabindex="-1"
                    >
                      <div>
                        <div
                          class="emotion-13"
                        >
                          <div
                            class="emotion-14"
                          >
                            <div
                              class="emotion-15"
                              height="400"
                              width="640"
                            >
                              <div
                                class="emotion-16"
                                data-testid="product-card-image"
                              >
                                <img
                                  alt="hello lady"
                                  class="emotion-17"
                                  src="https://2hmc8flz4ofg1dy89cc38f2xm.staging.bigcontent.io/i/athleta/530895_012_VIPW_AT_WMN_LS_150_SP20_SW_3_0882?fmt=webp"
                                />
                              </div>
                            </div>
                            <a
                              class="emotion-18"
                              href="/linkToNewArrivals"
                              tabindex="-1"
                              target="_self"
                              title=""
                            />
                          </div>
                          <div
                            class="emotion-14"
                          >
                            <div
                              class="emotion-15"
                              height="400"
                              width="640"
                            >
                              <div
                                class="emotion-16"
                                data-testid="product-card-image"
                              >
                                <img
                                  alt="hello lady"
                                  class="emotion-17"
                                  src="https://2hmc8flz4ofg1dy89cc38f2xm.staging.bigcontent.io/i/athleta/woman-black_hat?fmt=webp"
                                />
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div
                      aria-hidden="true"
                      class="slick-slide"
                      data-index="1"
                      style="outline: none; width: 0px;"
                      tabindex="-1"
                    >
                      <div>
                        <div
                          class="emotion-13"
                        >
                          <div
                            class="emotion-14"
                          >
                            <div
                              class="emotion-25"
                              height="400"
                              width="1280"
                            >
                              <div
                                class="emotion-26"
                                data-testid="product-card-image"
                              >
                                <img
                                  alt="image only"
                                  class="emotion-27"
                                  src="https://2hmc8flz4ofg1dy89cc38f2xm.staging.bigcontent.io/i/athleta/HOL2_NA_GiftShop_ISM_XL@2x?fmt=webp"
                                />
                              </div>
                            </div>
                            <a
                              class="emotion-18"
                              href="/productsWeLove"
                              tabindex="-1"
                              target="_self"
                              title="Middle link"
                            />
                          </div>
                        </div>
                      </div>
                    </div>
                    <div
                      aria-hidden="true"
                      class="slick-slide"
                      data-index="2"
                      style="outline: none; width: 0px;"
                      tabindex="-1"
                    >
                      <div>
                        <div
                          class="emotion-13"
                        >
                          <div
                            class="emotion-14"
                          >
                            <div
                              class="emotion-25"
                              height="400"
                              width="1280"
                            >
                              <div
                                class="emotion-26"
                                data-testid="product-card-image"
                              >
                                <img
                                  alt="hi"
                                  class="emotion-27"
                                  src="https://2hmc8flz4ofg1dy89cc38f2xm.staging.bigcontent.io/i/athleta/985360_032_SFMG_AT_WMN_GS_70_HO21_MU_1_8109copy?fmt=webp"
                                />
                              </div>
                            </div>
                            <a
                              class="emotion-18"
                              href="/lastLink"
                              tabindex="-1"
                              target="_self"
                              title="lastLink"
                            />
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <button
                  aria-label="Next"
                  class="slick-next slick-arrow slick-next"
                  data-role="none"
                >
                  <span
                    aria-hidden="true"
                    class="emotion-12"
                  >
                    <svg
                      viewBox="0 0 13.29 8.07"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M6.13 7.86a.73.73 0 001.04 0l5.92-5.92a.73.73 0 000-1.03l-.7-.7a.73.73 0 00-1.03 0l-4.71 4.7L1.94.2A.73.73 0 00.9.21l-.69.7a.73.73 0 000 1.03z"
                        fill="#003764"
                      />
                    </svg>
                  </span>
                </button>
                <ul
                  class="slick-dots"
                  style="display: block;"
                >
                  <li
                    class="slick-active"
                  >
                    <button>
                      1
                    </button>
                  </li>
                  <li
                    class=""
                  >
                    <button>
                      2
                    </button>
                  </li>
                  <li
                    class=""
                  >
                    <button>
                      3
                    </button>
                  </li>
                </ul>
              </div>
            </div>
          </nav>
        </div>
      </section>
    </div>
  </div>
</div>
`;

exports[`Snapshots should match desktop snapshots 1`] = `
.emotion-0 {
  margin-left: auto;
  margin-right: auto;
  max-width: 1280px;
}

.emotion-2 {
  position: relative;
}

.emotion-3 {
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-3 .slick-list {
  overflow: hidden;
}

.emotion-3 .slick-track {
  width: -webkit-max-content!important;
  width: -moz-max-content!important;
  width: max-content!important;
}

.emotion-3 button.slick-next.slick-arrow.slick-next,
.emotion-3 button.slick-prev.slick-arrow.slick-prev {
  z-index: 2;
  top: min(15.625vw, 50%);
  height: 44px;
  width: 44px;
  background: #003764;
  opacity: 75%;
}

.emotion-3 button.slick-next.slick-arrow.slick-next>span,
.emotion-3 button.slick-prev.slick-arrow.slick-prev>span {
  margin: auto;
}

.emotion-3 button.slick-next.slick-arrow.slick-next svg,
.emotion-3 button.slick-prev.slick-arrow.slick-prev svg {
  margin: auto;
  height: 25px;
  width: 14px;
}

.emotion-3 button.slick-next.slick-arrow.slick-next span svg path,
.emotion-3 button.slick-prev.slick-arrow.slick-prev span svg path {
  fill: #ffffff;
}

.emotion-3 button.slick-next.slick-arrow.slick-next:hover,
.emotion-3 button.slick-prev.slick-arrow.slick-prev:hover {
  opacity: 100%;
}

.emotion-3 button.slick-next.slick-arrow.slick-next:focus,
.emotion-3 button.slick-prev.slick-arrow.slick-prev:focus {
  background: #003764;
}

.emotion-3 button.slick-next.slick-arrow.slick-next:focus-visible,
.emotion-3 button.slick-prev.slick-arrow.slick-prev:focus-visible {
  outline: auto;
}

.emotion-3 .slick-slide {
  margin: 0;
  width: auto;
}

.emotion-3 .slick-disabled {
  display: none!important;
}

.emotion-3 .slick-next {
  left: calc(100% - 44px);
}

.emotion-3 .slick-slide:first-of-type a>div:nth-of-type(2)>div>div:nth-of-type(2) {
  border-left: none;
}

.emotion-3 .slick-slide>div:first-of-type {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-3 .slick-slide>div:first-of-type>div:first-of-type {
  width: 100%;
}

.emotion-3 .slick-slider ul.slick-dots {
  bottom: 8px;
  width: unset;
  height: 44px;
  margin-left: 50%;
  -webkit-transform: translateX(-50%);
  -moz-transform: translateX(-50%);
  -ms-transform: translateX(-50%);
  transform: translateX(-50%);
  -webkit-align-content: center;
  -ms-flex-line-pack: center;
  align-content: center;
}

.emotion-3 .slick-slider .slick-dots li.slick-active button:before {
  background-color: #003764;
}

.emotion-3 .slick-slider ul.slick-dots button:before {
  width: 10px;
  height: 10px;
  opacity: 1;
  background-color: #B2B2B2;
}

.emotion-4 {
  position: relative;
}

.emotion-4 .slick-slider {
  position: relative;
  display: block;
  box-sizing: border-box;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  -webkit-touch-callout: none;
  touch-action: pan-y;
  -webkit-tap-highlight-color: transparent;
  scroll-behavior: smooth;
}

.emotion-4 .slick-slider .slick-track,
.emotion-4 .slick-slider .slick-list {
  -webkit-transform: translate3d(0, 0, 0);
  -moz-transform: translate3d(0, 0, 0);
  -ms-transform: translate3d(0, 0, 0);
  transform: translate3d(0, 0, 0);
}

.emotion-4 .slick-list {
  position: relative;
  display: block;
  overflow-x: hidden;
  overflow-y: hidden;
  margin: 0;
  padding: 0;
}

.emotion-4 .slick-list:focus {
  outline: none;
}

.emotion-4 .slick-list.dragging {
  cursor: pointer;
  cursor: hand;
}

.emotion-4 .slick-track {
  position: relative;
  top: 0;
  left: 0;
  display: block;
  margin-left: auto;
  margin-right: auto;
}

.emotion-4 .slick-track:before,
.emotion-4 .slick-track:after {
  display: table;
  content: "";
}

.emotion-4 .slick-track:after {
  clear: both;
}

.emotion-4 .slick-slide {
  display: none;
  float: left;
  height: 100%;
  min-height: 1px;
}

.emotion-4 .slick-slide img {
  display: block;
}

.emotion-4 .slick-slide.slick-loading img {
  display: none;
}

.emotion-4 .slick-slide.dragging img {
  pointer-events: none;
}

.emotion-4 [dir="rtl"] .slick-slide {
  float: right;
}

.emotion-4 .slick-initialized .slick-slide,
.emotion-4 .slick-vertical .slick-slide {
  display: block;
}

.emotion-4 .slick-vertical .slick-slide {
  border: 1px solid transparent;
  height: auto;
}

.emotion-4 .slick-loading .slick-track,
.emotion-4 .slick-loading .slick-slide {
  visibility: hidden;
}

.emotion-4 .slick-loading .slick-list {
  background: #fff url("./ajax-loader.gif") center center no-repeat;
}

.emotion-4 .slick-arrow.slick-hidden {
  display: none;
}

.emotion-4 .slick-prev,
.emotion-4 .slick-next {
  font-size: 0;
  line-height: 0;
  position: absolute;
  top: 50%;
  display: block;
  padding: 0;
  cursor: pointer;
  color: transparent;
  border: none;
  outline: none;
  background: transparent;
}

.emotion-4 .slick-prev:hover,
.emotion-4 .slick-next:hover,
.emotion-4 .slick-prev:focus,
.emotion-4 .slick-next:focus {
  color: transparent;
  outline: none;
  background: transparent;
  opacity: 1;
}

.emotion-4 .slick-prev.slick-disabled,
.emotion-4 .slick-next.slick-disabled {
  opacity: 0.25;
}

.emotion-4 .slick-prev {
  left: -0;
  -webkit-transform: translate(0, -50%) rotate(90deg);
  -moz-transform: translate(0, -50%) rotate(90deg);
  -ms-transform: translate(0, -50%) rotate(90deg);
  transform: translate(0, -50%) rotate(90deg);
}

.emotion-4 .slick-prev span {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-4 [dir="rtl"] .slick-prev {
  right: -0;
  left: auto;
}

.emotion-4 .slick-next {
  right: -0;
  -webkit-transform: translate(0, -50%) rotate(-90deg);
  -moz-transform: translate(0, -50%) rotate(-90deg);
  -ms-transform: translate(0, -50%) rotate(-90deg);
  transform: translate(0, -50%) rotate(-90deg);
}

.emotion-4 .slick-next span {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-4 [dir="rtl"] .slick-next {
  right: auto;
  left: -0;
}

.emotion-4 .slick-dotted.slick-slider {
  margin-bottom: 30px;
}

.emotion-4 .slick-dots {
  position: absolute;
  bottom: -25px;
  display: block;
  width: 100%;
  padding: 0;
  margin: 0;
  list-style: none;
  text-align: center;
}

.emotion-4 .slick-dots li {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: 20px;
  height: 20px;
  margin: 0 5px;
  padding: 0;
  cursor: pointer;
}

.emotion-4 .slick-dots li.slick-active button:before {
  opacity: 0.75;
  color: black;
}

.emotion-4 .slick-dots li button {
  font-size: 0;
  line-height: 0;
  display: block;
  padding: 5px;
  cursor: pointer;
  color: transparent;
  border: 0;
  outline: none;
  background: transparent;
}

.emotion-4 .slick-dots li button:hover,
.emotion-4 .slick-dots li button:focus {
  outline: none;
}

.emotion-4 .slick-dots li button:hover:before,
.emotion-4 .slick-dots li button:focus:before,
.emotion-4 .slick-dots li button:hover:before,
.emotion-4 .slick-dots li button:focus:before {
  opacity: 1;
}

.emotion-4 .slick-dots li button:before {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 5px;
  height: 5px;
  content: "";
  opacity: 0.25;
  background-color: black;
  border-radius: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-5 {
  display: inline-block;
  height: 2rem;
  width: 2rem;
  min-height: 2rem;
  min-width: 2rem;
}

.emotion-5 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-6 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-7 {
  width: 100%;
  position: relative;
}

.emotion-8 {
  overflow: hidden;
  position: relative;
  box-sizing: border-box;
  aspect-ratio: 640/400;
}

.emotion-9 {
  -webkit-background-size: cover;
  background-size: cover;
  aspect-ratio: 640/400;
  overflow: hidden;
}

.emotion-10 {
  width: 100%;
  aspect-ratio: 640/400;
  object-fit: cover;
}

.emotion-11 {
  width: 100%;
  height: 100%;
  position: absolute;
  left: 0;
  top: 0;
  z-index: 1;
}

.emotion-12 {
  height: 100%;
  width: 100%;
  position: absolute;
  top: 0;
  left: 0;
  position: absolute;
  height: 100%;
}

.emotion-13 {
  width: 100%;
  height: 100%;
  padding: 50px 60px;
  box-sizing: border-box;
  position: absolute;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  display: grid;
  grid-template-columns: repeat(3, minmax(min-content,max-content));
  -webkit-align-content: center;
  -ms-flex-line-pack: center;
  align-content: center;
  grid-template-rows: 1fr max-content 1fr;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  justify-content: space-between;
}

.emotion-13 .rteWrapperDivs {
  grid-column: 2;
  grid-row: 2;
}

.emotion-13 .ctaWrapperDivs {
  grid-column: 2;
  grid-row: 3;
  text-align: center;
}

.emotion-14 {
  box-sizing: content-box;
  text-align: center;
}

.emotion-15 {
  text-align: center;
  display: inline-block;
  max-width: min(67.5vw, 864px);
}

.emotion-15 .amp-cms--p {
  padding: 0;
  margin: 0;
  line-height: 0;
}

.emotion-15 .amp-cms--p>span {
  display: inline;
  white-space: break-spaces;
}

.emotion-15 .amp-cms--p a {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  position: relative;
  z-index: 2;
  pointer-events: auto;
}

.emotion-15 .amp-cms--p a:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-15 .amp-cms--legal-copy {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(0.78125vw, 10px));
  line-height: 1.5;
  letter-spacing: 0;
}

.emotion-15 sup {
  vertical-align: top;
  display: inline-block;
  margin-top: -0.20ex;
}

.emotion-15 sub {
  vertical-align: bottom;
  display: inline-block;
  margin-bottom: -0.20ex;
}

.emotion-15 .amp-cms--f-0 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.9375vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-15 .amp-cms--f-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.9375vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-15 .amp-cms--f-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.9375vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-15 .amp-cms--fn-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.9375vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-15 .amp-cms--body-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(1.40625vw, 18px));
  line-height: 1.5;
  letter-spacing: min(0.056249999999999994vw, 0.72px);
  font-weight: 500;
}

.emotion-15 .amp-cms--body-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(1.25vw, 16px));
  line-height: 1.5;
  letter-spacing: min(0.05vw, 0.64px);
  font-weight: 500;
}

.emotion-15 .amp-cms--body-3 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(1.09375vw, 14px));
  line-height: 1.5;
  letter-spacing: min(0.043750000000000004vw, 0.56px);
}

.emotion-15 .amp-cms--body-4 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(0.9375vw, 12px));
  line-height: 1.5;
  letter-spacing: 0;
}

.emotion-15 .amp-cms--body-5 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(0.78125vw, 10px));
  line-height: 1.5;
  letter-spacing: 0;
}

.emotion-15 .amp-cms--eyebrow-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(2.1875vw, 28px));
  line-height: 1;
  letter-spacing: min(0.08750000000000001vw, 1.12px);
  font-weight: 500;
}

.emotion-15 .amp-cms--eyebrow-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(1.7968749999999998vw, 23px));
  line-height: 1;
  letter-spacing: min(0.071875vw, 0.92px);
  font-weight: 500;
}

.emotion-15 .amp-cms--eyebrow-3 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(1.328125vw, 17px));
  line-height: 1;
  letter-spacing: min(0.053125000000000006vw, 0.68px);
  font-weight: 500;
}

.emotion-15 .amp-cms--headline-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(9.21875vw, 118px));
  line-height: 1;
  letter-spacing: 0;
  font-weight: 700;
}

.emotion-15 .amp-cms--headline-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(6.25vw, 80px));
  line-height: 1;
  letter-spacing: 0;
  font-weight: 700;
}

.emotion-15 .amp-cms--headline-3 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(5.625vw, 72px));
  line-height: 1;
  letter-spacing: 0;
  font-weight: 700;
}

.emotion-15 .amp-cms--headline-4 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(5vw, 64px));
  line-height: 1;
  letter-spacing: min(0.1vw, 1.28px);
  font-weight: 700;
}

.emotion-15 .amp-cms--headline-5 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(3.75vw, 48px));
  line-height: 1;
  letter-spacing: min(0.22499999999999998vw, 2.88px);
  font-weight: 700;
}

.emotion-15 .amp-cms--headline-6 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(3.125vw, 40px));
  line-height: 1.1;
  letter-spacing: min(0.1875vw, 2.4px);
  font-weight: 700;
}

.emotion-15 .amp-cms--headline-7 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(2.5vw, 32px));
  line-height: 1;
  letter-spacing: min(0.1vw, 1.28px);
  font-weight: 700;
}

.emotion-15 .amp-cms--headlineAlt-1 {
  color: #003764;
  font-size: max(14px, min(8.90625vw, 114px));
  line-height: 1;
  letter-spacing: min(-0.03125vw, -0.4px);
  font-weight: 400;
}

.emotion-15 .amp-cms--headlineAlt-2 {
  color: #003764;
  font-size: max(14px, min(6.5625vw, 84px));
  line-height: 1;
  letter-spacing: min(-0.03125vw, -0.4px);
  font-weight: 400;
}

.emotion-15 .amp-cms--headlineAlt-3 {
  color: #003764;
  font-size: max(14px, min(5vw, 64px));
  line-height: 1;
  letter-spacing: min(-0.03125vw, -0.4px);
  font-weight: 400;
}

.emotion-15 .amp-cms--headlineAlt-4 {
  color: #003764;
  font-size: max(14px, min(2.65625vw, 34px));
  line-height: 1;
  letter-spacing: min(-0.03125vw, -0.4px);
  font-weight: 400;
}

.emotion-15 .amp-cms--headlineAlt-5 {
  color: #003764;
  font-size: min(1.25vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-15 .amp-cms--headlineAlt-6 {
  color: #003764;
  font-size: min(1.25vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-15 .amp-cms--headlineAlt-7 {
  color: #003764;
  font-size: min(1.25vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-15 .amp-cms--promo-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(7.8125vw, 100px));
  line-height: 1;
  letter-spacing: min(0vw, 0px);
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-15 .amp-cms--promo-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(6.25vw, 80px));
  line-height: 1;
  letter-spacing: min(0vw, 0px);
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-15 .amp-cms--promoAlt-1 {
  color: #003764;
  font-size: min(1.25vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 600;
}

.emotion-15 .amp-cms--subhead-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(1.875vw, 24px));
  line-height: 1.5;
  letter-spacing: 0;
  font-weight: 500;
}

.emotion-15 .amp-cms--subhead-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(1.7187500000000002vw, 22px));
  line-height: 1.5;
  letter-spacing: 0;
  font-weight: 500;
}

.emotion-15 .amp-cms--subhead-3 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(1.5625vw, 20px));
  line-height: 1.5;
  letter-spacing: 0;
  font-weight: 500;
}

.emotion-16 {
  display: block;
  width: 100%;
  position: relative;
  z-index: 2;
  padding-top: 18px;
  text-align: center;
}

.emotion-17 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 700;
  letter-spacing: 1.05px;
  min-height: auto;
  max-height: auto;
  line-height: 1.8571428571428572;
  padding: 0;
  width: auto;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: left;
  border-left: none;
  border-right: none;
  border-radius: 0;
  background-color: transparent;
  border-bottom: 0;
  -webkit-box-pack: left;
  -ms-flex-pack: left;
  -webkit-justify-content: left;
  justify-content: left;
  color: #003764;
  height: auto;
  -webkit-text-decoration: underline;
  text-decoration: underline;
  text-underline-offset: 3.5px;
  text-decoration-thickness: 3px;
  -webkit-align-self: flex-start;
  -ms-flex-item-align: flex-start;
  align-self: flex-start;
  white-space: normal;
}

.emotion-17:focus {
  outline: none;
}

.emotion-17>span {
  padding: 1px 0;
}

.emotion-17:hover,
.emotion-17:focus {
  text-decoration-color: #003764;
}

.emotion-17:hover span,
.emotion-17:focus span {
  color: #FFFFFF;
  background-color: #003764;
}

.emotion-17:active {
  text-decoration-color: #000000;
}

.emotion-17:active>span {
  color: #FFFFFF;
  background-color: #000000;
}

.emotion-18 {
  box-sizing: border-box;
}

.emotion-24 {
  width: 100%;
  height: 100%;
  padding: 50px 60px;
  box-sizing: border-box;
  position: absolute;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  display: grid;
  grid-template-columns: repeat(3, minmax(min-content,max-content));
  -webkit-align-content: center;
  -ms-flex-line-pack: center;
  align-content: center;
  grid-template-rows: repeat(3, minmax(0, max-content));
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  justify-content: space-between;
}

.emotion-24 .rteWrapperDivs {
  grid-column: 3;
  grid-row: 3;
}

.emotion-24 .ctaWrapperDivs {
  grid-column: 1;
  grid-row: 3;
  text-align: end;
}

.emotion-25 {
  box-sizing: content-box;
  text-align: end;
}

.emotion-26 {
  text-align: end;
  display: inline-block;
  max-width: min(67.5vw, 864px);
}

.emotion-26 .amp-cms--p {
  padding: 0;
  margin: 0;
  line-height: 0;
}

.emotion-26 .amp-cms--p>span {
  display: inline;
  white-space: break-spaces;
}

.emotion-26 .amp-cms--p a {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  position: relative;
  z-index: 2;
  pointer-events: auto;
}

.emotion-26 .amp-cms--p a:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-26 .amp-cms--legal-copy {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(0.78125vw, 10px));
  line-height: 1.5;
  letter-spacing: 0;
}

.emotion-26 sup {
  vertical-align: top;
  display: inline-block;
  margin-top: -0.20ex;
}

.emotion-26 sub {
  vertical-align: bottom;
  display: inline-block;
  margin-bottom: -0.20ex;
}

.emotion-26 .amp-cms--f-0 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.9375vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-26 .amp-cms--f-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.9375vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-26 .amp-cms--f-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.9375vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-26 .amp-cms--fn-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.9375vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-26 .amp-cms--body-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(1.40625vw, 18px));
  line-height: 1.5;
  letter-spacing: min(0.056249999999999994vw, 0.72px);
  font-weight: 500;
}

.emotion-26 .amp-cms--body-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(1.25vw, 16px));
  line-height: 1.5;
  letter-spacing: min(0.05vw, 0.64px);
  font-weight: 500;
}

.emotion-26 .amp-cms--body-3 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(1.09375vw, 14px));
  line-height: 1.5;
  letter-spacing: min(0.043750000000000004vw, 0.56px);
}

.emotion-26 .amp-cms--body-4 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(0.9375vw, 12px));
  line-height: 1.5;
  letter-spacing: 0;
}

.emotion-26 .amp-cms--body-5 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(0.78125vw, 10px));
  line-height: 1.5;
  letter-spacing: 0;
}

.emotion-26 .amp-cms--eyebrow-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(2.1875vw, 28px));
  line-height: 1;
  letter-spacing: min(0.08750000000000001vw, 1.12px);
  font-weight: 500;
}

.emotion-26 .amp-cms--eyebrow-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(1.7968749999999998vw, 23px));
  line-height: 1;
  letter-spacing: min(0.071875vw, 0.92px);
  font-weight: 500;
}

.emotion-26 .amp-cms--eyebrow-3 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(1.328125vw, 17px));
  line-height: 1;
  letter-spacing: min(0.053125000000000006vw, 0.68px);
  font-weight: 500;
}

.emotion-26 .amp-cms--headline-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(9.21875vw, 118px));
  line-height: 1;
  letter-spacing: 0;
  font-weight: 700;
}

.emotion-26 .amp-cms--headline-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(6.25vw, 80px));
  line-height: 1;
  letter-spacing: 0;
  font-weight: 700;
}

.emotion-26 .amp-cms--headline-3 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(5.625vw, 72px));
  line-height: 1;
  letter-spacing: 0;
  font-weight: 700;
}

.emotion-26 .amp-cms--headline-4 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(5vw, 64px));
  line-height: 1;
  letter-spacing: min(0.1vw, 1.28px);
  font-weight: 700;
}

.emotion-26 .amp-cms--headline-5 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(3.75vw, 48px));
  line-height: 1;
  letter-spacing: min(0.22499999999999998vw, 2.88px);
  font-weight: 700;
}

.emotion-26 .amp-cms--headline-6 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(3.125vw, 40px));
  line-height: 1.1;
  letter-spacing: min(0.1875vw, 2.4px);
  font-weight: 700;
}

.emotion-26 .amp-cms--headline-7 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(2.5vw, 32px));
  line-height: 1;
  letter-spacing: min(0.1vw, 1.28px);
  font-weight: 700;
}

.emotion-26 .amp-cms--headlineAlt-1 {
  color: #003764;
  font-size: max(14px, min(8.90625vw, 114px));
  line-height: 1;
  letter-spacing: min(-0.03125vw, -0.4px);
  font-weight: 400;
}

.emotion-26 .amp-cms--headlineAlt-2 {
  color: #003764;
  font-size: max(14px, min(6.5625vw, 84px));
  line-height: 1;
  letter-spacing: min(-0.03125vw, -0.4px);
  font-weight: 400;
}

.emotion-26 .amp-cms--headlineAlt-3 {
  color: #003764;
  font-size: max(14px, min(5vw, 64px));
  line-height: 1;
  letter-spacing: min(-0.03125vw, -0.4px);
  font-weight: 400;
}

.emotion-26 .amp-cms--headlineAlt-4 {
  color: #003764;
  font-size: max(14px, min(2.65625vw, 34px));
  line-height: 1;
  letter-spacing: min(-0.03125vw, -0.4px);
  font-weight: 400;
}

.emotion-26 .amp-cms--headlineAlt-5 {
  color: #003764;
  font-size: min(1.25vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-26 .amp-cms--headlineAlt-6 {
  color: #003764;
  font-size: min(1.25vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-26 .amp-cms--headlineAlt-7 {
  color: #003764;
  font-size: min(1.25vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-26 .amp-cms--promo-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(7.8125vw, 100px));
  line-height: 1;
  letter-spacing: min(0vw, 0px);
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-26 .amp-cms--promo-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(6.25vw, 80px));
  line-height: 1;
  letter-spacing: min(0vw, 0px);
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-26 .amp-cms--promoAlt-1 {
  color: #003764;
  font-size: min(1.25vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 600;
}

.emotion-26 .amp-cms--subhead-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(1.875vw, 24px));
  line-height: 1.5;
  letter-spacing: 0;
  font-weight: 500;
}

.emotion-26 .amp-cms--subhead-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(1.7187500000000002vw, 22px));
  line-height: 1.5;
  letter-spacing: 0;
  font-weight: 500;
}

.emotion-26 .amp-cms--subhead-3 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(1.5625vw, 20px));
  line-height: 1.5;
  letter-spacing: 0;
  font-weight: 500;
}

.emotion-27 {
  display: block;
  width: 100%;
  position: relative;
  z-index: 2;
  padding-top: 18px;
  text-align: left;
}

.emotion-28 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 700;
  letter-spacing: 1.6800000000000002px;
  min-height: 48px;
  max-height: auto;
  line-height: 1.1428571428571428;
  padding: 14px 22px;
  width: auto;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  background-color: #FFFFFF;
  color: #003764;
  border-color: #003764;
  -webkit-align-self: flex-start;
  -ms-flex-item-align: flex-start;
  align-self: flex-start;
  white-space: normal;
  width: 335px;
}

.emotion-28:focus {
  outline: none;
}

.emotion-28>span {
  padding: 1px 0;
}

.emotion-28:hover,
.emotion-28:focus {
  color: #FFFFFF;
  background-color: #003764;
  border-color: #003764;
}

.emotion-28:active {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  color: #FFFFFF;
  background-color: #000000;
  border-color: #000000;
}

.emotion-31 {
  overflow: hidden;
  position: relative;
  box-sizing: border-box;
  aspect-ratio: 1280/400;
}

.emotion-32 {
  -webkit-background-size: cover;
  background-size: cover;
  aspect-ratio: 1280/400;
  overflow: hidden;
}

.emotion-33 {
  width: 100%;
  aspect-ratio: 1280/400;
  object-fit: cover;
}

.emotion-36 {
  width: 100%;
  height: 100%;
  padding: 50px 60px;
  box-sizing: border-box;
  position: absolute;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  display: grid;
  grid-template-columns: repeat(3, minmax(min-content,max-content));
  -webkit-align-content: center;
  -ms-flex-line-pack: center;
  align-content: center;
  grid-template-rows: repeat(3, minmax(0, max-content));
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  justify-content: space-between;
}

.emotion-36 .rteWrapperDivs {
  grid-column: 2;
  grid-row: 2;
}

.emotion-36 .ctaWrapperDivs {
  grid-column: 2;
  grid-row: 2;
  text-align: start;
}

.emotion-46 {
  width: 100%;
  height: 100%;
  padding: 50px 60px;
  box-sizing: border-box;
  position: absolute;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  display: grid;
  grid-template-columns: repeat(3, minmax(min-content,max-content));
  -webkit-align-content: center;
  -ms-flex-line-pack: center;
  align-content: center;
  grid-template-rows: max-content max-content 1fr;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  justify-content: space-between;
}

.emotion-46 .rteWrapperDivs {
  grid-column: 1;
  grid-row: 1;
}

.emotion-46 .ctaWrapperDivs {
  grid-column: 1;
  grid-row: 2;
  text-align: start;
}

.emotion-47 {
  box-sizing: content-box;
  text-align: start;
}

.emotion-48 {
  text-align: start;
  display: inline-block;
  max-width: 864px;
  width: calc(min(100vw, 1280px) * 0.675);
}

.emotion-48 .amp-cms--p {
  padding: 0;
  margin: 0;
  line-height: 0;
}

.emotion-48 .amp-cms--p>span {
  display: inline;
  white-space: break-spaces;
}

.emotion-48 .amp-cms--p a {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  position: relative;
  z-index: 2;
  pointer-events: auto;
}

.emotion-48 .amp-cms--p a:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-48 .amp-cms--legal-copy {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(0.78125vw, 10px));
  line-height: 1.5;
  letter-spacing: 0;
}

.emotion-48 sup {
  vertical-align: top;
  display: inline-block;
  margin-top: -0.20ex;
}

.emotion-48 sub {
  vertical-align: bottom;
  display: inline-block;
  margin-bottom: -0.20ex;
}

.emotion-48 .amp-cms--f-0 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.9375vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-48 .amp-cms--f-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.9375vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-48 .amp-cms--f-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.9375vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-48 .amp-cms--fn-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.9375vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-48 .amp-cms--body-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(1.40625vw, 18px));
  line-height: 1.5;
  letter-spacing: min(0.056249999999999994vw, 0.72px);
  font-weight: 500;
}

.emotion-48 .amp-cms--body-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(1.25vw, 16px));
  line-height: 1.5;
  letter-spacing: min(0.05vw, 0.64px);
  font-weight: 500;
}

.emotion-48 .amp-cms--body-3 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(1.09375vw, 14px));
  line-height: 1.5;
  letter-spacing: min(0.043750000000000004vw, 0.56px);
}

.emotion-48 .amp-cms--body-4 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(0.9375vw, 12px));
  line-height: 1.5;
  letter-spacing: 0;
}

.emotion-48 .amp-cms--body-5 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(0.78125vw, 10px));
  line-height: 1.5;
  letter-spacing: 0;
}

.emotion-48 .amp-cms--eyebrow-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(2.1875vw, 28px));
  line-height: 1;
  letter-spacing: min(0.08750000000000001vw, 1.12px);
  font-weight: 500;
}

.emotion-48 .amp-cms--eyebrow-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(1.7968749999999998vw, 23px));
  line-height: 1;
  letter-spacing: min(0.071875vw, 0.92px);
  font-weight: 500;
}

.emotion-48 .amp-cms--eyebrow-3 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(1.328125vw, 17px));
  line-height: 1;
  letter-spacing: min(0.053125000000000006vw, 0.68px);
  font-weight: 500;
}

.emotion-48 .amp-cms--headline-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(9.21875vw, 118px));
  line-height: 1;
  letter-spacing: 0;
  font-weight: 700;
}

.emotion-48 .amp-cms--headline-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(6.25vw, 80px));
  line-height: 1;
  letter-spacing: 0;
  font-weight: 700;
}

.emotion-48 .amp-cms--headline-3 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(5.625vw, 72px));
  line-height: 1;
  letter-spacing: 0;
  font-weight: 700;
}

.emotion-48 .amp-cms--headline-4 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(5vw, 64px));
  line-height: 1;
  letter-spacing: min(0.1vw, 1.28px);
  font-weight: 700;
}

.emotion-48 .amp-cms--headline-5 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(3.75vw, 48px));
  line-height: 1;
  letter-spacing: min(0.22499999999999998vw, 2.88px);
  font-weight: 700;
}

.emotion-48 .amp-cms--headline-6 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(3.125vw, 40px));
  line-height: 1.1;
  letter-spacing: min(0.1875vw, 2.4px);
  font-weight: 700;
}

.emotion-48 .amp-cms--headline-7 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(2.5vw, 32px));
  line-height: 1;
  letter-spacing: min(0.1vw, 1.28px);
  font-weight: 700;
}

.emotion-48 .amp-cms--headlineAlt-1 {
  color: #003764;
  font-size: max(14px, min(8.90625vw, 114px));
  line-height: 1;
  letter-spacing: min(-0.03125vw, -0.4px);
  font-weight: 400;
}

.emotion-48 .amp-cms--headlineAlt-2 {
  color: #003764;
  font-size: max(14px, min(6.5625vw, 84px));
  line-height: 1;
  letter-spacing: min(-0.03125vw, -0.4px);
  font-weight: 400;
}

.emotion-48 .amp-cms--headlineAlt-3 {
  color: #003764;
  font-size: max(14px, min(5vw, 64px));
  line-height: 1;
  letter-spacing: min(-0.03125vw, -0.4px);
  font-weight: 400;
}

.emotion-48 .amp-cms--headlineAlt-4 {
  color: #003764;
  font-size: max(14px, min(2.65625vw, 34px));
  line-height: 1;
  letter-spacing: min(-0.03125vw, -0.4px);
  font-weight: 400;
}

.emotion-48 .amp-cms--headlineAlt-5 {
  color: #003764;
  font-size: min(1.25vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-48 .amp-cms--headlineAlt-6 {
  color: #003764;
  font-size: min(1.25vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-48 .amp-cms--headlineAlt-7 {
  color: #003764;
  font-size: min(1.25vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-48 .amp-cms--promo-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(7.8125vw, 100px));
  line-height: 1;
  letter-spacing: min(0vw, 0px);
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-48 .amp-cms--promo-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(6.25vw, 80px));
  line-height: 1;
  letter-spacing: min(0vw, 0px);
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-48 .amp-cms--promoAlt-1 {
  color: #003764;
  font-size: min(1.25vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 600;
}

.emotion-48 .amp-cms--subhead-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(1.875vw, 24px));
  line-height: 1.5;
  letter-spacing: 0;
  font-weight: 500;
}

.emotion-48 .amp-cms--subhead-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(1.7187500000000002vw, 22px));
  line-height: 1.5;
  letter-spacing: 0;
  font-weight: 500;
}

.emotion-48 .amp-cms--subhead-3 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(1.5625vw, 20px));
  line-height: 1.5;
  letter-spacing: 0;
  font-weight: 500;
}

<div>
  <div
    style="font-family: var(--font-family-font1),sans-serif;"
  >
    <div
      class="emotion-0"
    >
      <section
        class="emotion-1"
      >
        <div
          class="emotion-2"
        >
          <nav
            class="emotion-3"
          >
            <div
              class="emotion-4"
            >
              <div
                class="slick-slider slick-initialized"
                dir="ltr"
              >
                <button
                  aria-label="Previous"
                  class="slick-prev slick-arrow slick-prev slick-disabled"
                  data-role="none"
                  disabled=""
                >
                  <span
                    aria-hidden="true"
                    class="emotion-5"
                  >
                    <svg
                      viewBox="0 0 13.29 8.07"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M6.13 7.86a.73.73 0 001.04 0l5.92-5.92a.73.73 0 000-1.03l-.7-.7a.73.73 0 00-1.03 0l-4.71 4.7L1.94.2A.73.73 0 00.9.21l-.69.7a.73.73 0 000 1.03z"
                        fill="#003764"
                      />
                    </svg>
                  </span>
                </button>
                <div
                  class="slick-list"
                >
                  <div
                    class="slick-track"
                    style="opacity: 1; transform: translate3d(0px, 0px, 0px);"
                  >
                    <div
                      aria-hidden="false"
                      class="slick-slide slick-active slick-current"
                      data-index="0"
                      style="outline: none; width: 0px;"
                      tabindex="-1"
                    >
                      <div>
                        <div
                          class="emotion-6"
                        >
                          <div
                            class="emotion-7"
                          >
                            <div
                              class="emotion-8"
                              height="400"
                              width="640"
                            >
                              <div
                                class="emotion-9"
                                data-testid="product-card-image"
                              >
                                <img
                                  alt="hello lady"
                                  class="emotion-10"
                                  src="https://2hmc8flz4ofg1dy89cc38f2xm.staging.bigcontent.io/i/athleta/530895_012_VIPW_AT_WMN_LS_150_SP20_SW_3_0882?fmt=webp"
                                />
                              </div>
                            </div>
                            <a
                              class="emotion-11"
                              href="/linkToNewArrivals"
                              tabindex="-1"
                              target="_self"
                              title=""
                            />
                            <div
                              class="emotion-12"
                              data-testid="absolute-wrapper"
                            >
                              <div
                                class="emotion-13"
                              >
                                <div
                                  class="rteWrapperDivs emotion-14"
                                >
                                  <div
                                    class="emotion-15"
                                  >
                                    <div>
                                      <p
                                        class="amp-cms--p"
                                      >
                                        <span
                                          class="amp-cms--subhead-1"
                                        >
                                          Lorem Ipsum
                                        </span>
                                      </p>
                                      <p
                                        class="amp-cms--p"
                                      >
                                        <span
                                          class="amp-cms--body-1"
                                        >
                                          dolor sit amet, consectetur adipiscing elit.
                                        </span>
                                      </p>
                                    </div>
                                  </div>
                                </div>
                                <div
                                  class="ctaWrapperDivs emotion-16"
                                >
                                  <a
                                    class="emotion-17"
                                    color="dark"
                                    href="/buyIt"
                                  >
                                    <span
                                      class="emotion-18"
                                    >
                                      new arrivals
                                    </span>
                                  </a>
                                </div>
                              </div>
                            </div>
                          </div>
                          <div
                            class="emotion-7"
                          >
                            <div
                              class="emotion-8"
                              height="400"
                              width="640"
                            >
                              <div
                                class="emotion-9"
                                data-testid="product-card-image"
                              >
                                <img
                                  alt="hello lady"
                                  class="emotion-10"
                                  src="https://2hmc8flz4ofg1dy89cc38f2xm.staging.bigcontent.io/i/athleta/woman-black_hat?fmt=webp"
                                />
                              </div>
                            </div>
                            <div
                              class="emotion-12"
                              data-testid="absolute-wrapper"
                            >
                              <div
                                class="emotion-24"
                              >
                                <div
                                  class="rteWrapperDivs emotion-25"
                                >
                                  <div
                                    class="emotion-26"
                                  >
                                    <div>
                                      <h2
                                        class="amp-cms--p"
                                      >
                                        <span
                                          class="amp-cms--body-1"
                                        >
                                          OUR MOST LOVED STYLES
                                        </span>
                                      </h2>
                                    </div>
                                  </div>
                                </div>
                                <div
                                  class="ctaWrapperDivs emotion-27"
                                >
                                  <a
                                    class="emotion-28"
                                    color="dark"
                                    href="/buyIt"
                                  >
                                    new arrivals
                                  </a>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div
                      aria-hidden="true"
                      class="slick-slide"
                      data-index="1"
                      style="outline: none; width: 0px;"
                      tabindex="-1"
                    >
                      <div>
                        <div
                          class="emotion-6"
                        >
                          <div
                            class="emotion-7"
                          >
                            <div
                              class="emotion-31"
                              height="400"
                              width="1280"
                            >
                              <div
                                class="emotion-32"
                                data-testid="product-card-image"
                              >
                                <img
                                  alt="image only"
                                  class="emotion-33"
                                  src="https://2hmc8flz4ofg1dy89cc38f2xm.staging.bigcontent.io/i/athleta/HOL2_NA_GiftShop_ISM_XL@2x?fmt=webp"
                                />
                              </div>
                            </div>
                            <a
                              class="emotion-11"
                              href="/productsWeLove"
                              tabindex="-1"
                              target="_self"
                              title="Middle link"
                            />
                            <div
                              class="emotion-12"
                              data-testid="absolute-wrapper"
                            >
                              <div
                                class="emotion-36"
                              >
                                <div
                                  class="ctaWrapperDivs emotion-16"
                                >
                                  <a
                                    class="emotion-28"
                                    color="dark"
                                    href="/ss"
                                  >
                                    Get a life
                                  </a>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div
                      aria-hidden="true"
                      class="slick-slide"
                      data-index="2"
                      style="outline: none; width: 0px;"
                      tabindex="-1"
                    >
                      <div>
                        <div
                          class="emotion-6"
                        >
                          <div
                            class="emotion-7"
                          >
                            <div
                              class="emotion-31"
                              height="400"
                              width="1280"
                            >
                              <div
                                class="emotion-32"
                                data-testid="product-card-image"
                              >
                                <img
                                  alt="hi"
                                  class="emotion-33"
                                  src="https://2hmc8flz4ofg1dy89cc38f2xm.staging.bigcontent.io/i/athleta/985360_032_SFMG_AT_WMN_GS_70_HO21_MU_1_8109copy?fmt=webp"
                                />
                              </div>
                            </div>
                            <a
                              class="emotion-11"
                              href="/lastLink"
                              tabindex="-1"
                              target="_self"
                              title="lastLink"
                            />
                            <div
                              class="emotion-12"
                              data-testid="absolute-wrapper"
                            >
                              <div
                                class="emotion-46"
                              >
                                <div
                                  class="rteWrapperDivs emotion-47"
                                >
                                  <div
                                    class="emotion-48"
                                  >
                                    <div>
                                      <p
                                        class="amp-cms--p"
                                      >
                                        <span
                                          class="amp-cms--headline-6"
                                        >
                                          Lorem Ipsum Dolor Sit Amet,
                                        </span>
                                      </p>
                                      <p
                                        class="amp-cms--p"
                                      >
                                        <span
                                          class="amp-cms--body-1"
                                        >
                                          consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam.
                                        </span>
                                      </p>
                                    </div>
                                  </div>
                                </div>
                                <div
                                  class="ctaWrapperDivs emotion-27"
                                >
                                  <a
                                    class="emotion-28"
                                    color="dark"
                                    href="/ss"
                                  >
                                    Get a life
                                  </a>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <button
                  aria-label="Next"
                  class="slick-next slick-arrow slick-next"
                  data-role="none"
                >
                  <span
                    aria-hidden="true"
                    class="emotion-5"
                  >
                    <svg
                      viewBox="0 0 13.29 8.07"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M6.13 7.86a.73.73 0 001.04 0l5.92-5.92a.73.73 0 000-1.03l-.7-.7a.73.73 0 00-1.03 0l-4.71 4.7L1.94.2A.73.73 0 00.9.21l-.69.7a.73.73 0 000 1.03z"
                        fill="#003764"
                      />
                    </svg>
                  </span>
                </button>
                <ul
                  class="slick-dots"
                  style="display: block;"
                >
                  <li
                    class="slick-active"
                  >
                    <button>
                      1
                    </button>
                  </li>
                  <li
                    class=""
                  >
                    <button>
                      2
                    </button>
                  </li>
                  <li
                    class=""
                  >
                    <button>
                      3
                    </button>
                  </li>
                </ul>
              </div>
            </div>
          </nav>
        </div>
      </section>
    </div>
  </div>
</div>
`;

exports[`Snapshots should match mobile snapshots 1`] = `
.emotion-0 {
  margin-left: auto;
  margin-right: auto;
  max-width: 1280px;
}

.emotion-2 {
  position: relative;
}

.emotion-3 {
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-3 .slick-list {
  overflow: hidden;
}

.emotion-3 .slick-track {
  width: -webkit-max-content!important;
  width: -moz-max-content!important;
  width: max-content!important;
}

.emotion-3 button.slick-next.slick-arrow.slick-next,
.emotion-3 button.slick-prev.slick-arrow.slick-prev {
  z-index: 2;
  top: min(42.66666666666667vw, 50%);
  height: 44px;
  width: 44px;
  background: #003764;
  opacity: 75%;
}

.emotion-3 button.slick-next.slick-arrow.slick-next>span,
.emotion-3 button.slick-prev.slick-arrow.slick-prev>span {
  margin: auto;
}

.emotion-3 button.slick-next.slick-arrow.slick-next svg,
.emotion-3 button.slick-prev.slick-arrow.slick-prev svg {
  margin: auto;
  height: 25px;
  width: 14px;
}

.emotion-3 button.slick-next.slick-arrow.slick-next span svg path,
.emotion-3 button.slick-prev.slick-arrow.slick-prev span svg path {
  fill: #ffffff;
}

.emotion-3 button.slick-next.slick-arrow.slick-next:hover,
.emotion-3 button.slick-prev.slick-arrow.slick-prev:hover {
  opacity: 100%;
}

.emotion-3 button.slick-next.slick-arrow.slick-next:focus,
.emotion-3 button.slick-prev.slick-arrow.slick-prev:focus {
  background: #003764;
}

.emotion-3 button.slick-next.slick-arrow.slick-next:focus-visible,
.emotion-3 button.slick-prev.slick-arrow.slick-prev:focus-visible {
  outline: auto;
}

.emotion-3 .slick-slide {
  margin: 0;
  width: auto;
}

.emotion-3 .slick-disabled {
  display: none!important;
}

.emotion-3 .slick-next {
  left: calc(100% - 44px);
}

.emotion-3 .slick-slide:first-of-type a>div:nth-of-type(2)>div>div:nth-of-type(2) {
  border-left: none;
}

.emotion-3 .slick-slide>div:first-of-type {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-3 .slick-slide>div:first-of-type>div:first-of-type {
  width: 100%;
}

.emotion-3 .slick-slider ul.slick-dots {
  bottom: 4px;
  width: unset;
  height: 44px;
  margin-left: 50%;
  -webkit-transform: translateX(-50%);
  -moz-transform: translateX(-50%);
  -ms-transform: translateX(-50%);
  transform: translateX(-50%);
  -webkit-align-content: center;
  -ms-flex-line-pack: center;
  align-content: center;
}

.emotion-3 .slick-slider .slick-dots li.slick-active button:before {
  background-color: #003764;
}

.emotion-3 .slick-slider ul.slick-dots button:before {
  width: 10px;
  height: 10px;
  opacity: 1;
  background-color: #B2B2B2;
}

.emotion-4 {
  position: relative;
}

.emotion-4 .slick-slider {
  position: relative;
  display: block;
  box-sizing: border-box;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  -webkit-touch-callout: none;
  touch-action: pan-y;
  -webkit-tap-highlight-color: transparent;
  scroll-behavior: smooth;
}

.emotion-4 .slick-slider .slick-track,
.emotion-4 .slick-slider .slick-list {
  -webkit-transform: translate3d(0, 0, 0);
  -moz-transform: translate3d(0, 0, 0);
  -ms-transform: translate3d(0, 0, 0);
  transform: translate3d(0, 0, 0);
}

.emotion-4 .slick-list {
  position: relative;
  display: block;
  overflow-x: hidden;
  overflow-y: hidden;
  margin: 0;
  padding: 0;
}

.emotion-4 .slick-list:focus {
  outline: none;
}

.emotion-4 .slick-list.dragging {
  cursor: pointer;
  cursor: hand;
}

.emotion-4 .slick-track {
  position: relative;
  top: 0;
  left: 0;
  display: block;
  margin-left: auto;
  margin-right: auto;
}

.emotion-4 .slick-track:before,
.emotion-4 .slick-track:after {
  display: table;
  content: "";
}

.emotion-4 .slick-track:after {
  clear: both;
}

.emotion-4 .slick-slide {
  display: none;
  float: left;
  height: 100%;
  min-height: 1px;
}

.emotion-4 .slick-slide img {
  display: block;
}

.emotion-4 .slick-slide.slick-loading img {
  display: none;
}

.emotion-4 .slick-slide.dragging img {
  pointer-events: none;
}

.emotion-4 [dir="rtl"] .slick-slide {
  float: right;
}

.emotion-4 .slick-initialized .slick-slide,
.emotion-4 .slick-vertical .slick-slide {
  display: block;
}

.emotion-4 .slick-vertical .slick-slide {
  border: 1px solid transparent;
  height: auto;
}

.emotion-4 .slick-loading .slick-track,
.emotion-4 .slick-loading .slick-slide {
  visibility: hidden;
}

.emotion-4 .slick-loading .slick-list {
  background: #fff url("./ajax-loader.gif") center center no-repeat;
}

.emotion-4 .slick-arrow.slick-hidden {
  display: none;
}

.emotion-4 .slick-prev,
.emotion-4 .slick-next {
  font-size: 0;
  line-height: 0;
  position: absolute;
  top: 50%;
  display: block;
  padding: 0;
  cursor: pointer;
  color: transparent;
  border: none;
  outline: none;
  background: transparent;
}

.emotion-4 .slick-prev:hover,
.emotion-4 .slick-next:hover,
.emotion-4 .slick-prev:focus,
.emotion-4 .slick-next:focus {
  color: transparent;
  outline: none;
  background: transparent;
  opacity: 1;
}

.emotion-4 .slick-prev.slick-disabled,
.emotion-4 .slick-next.slick-disabled {
  opacity: 0.25;
}

.emotion-4 .slick-prev {
  left: -0;
  -webkit-transform: translate(0, -50%) rotate(90deg);
  -moz-transform: translate(0, -50%) rotate(90deg);
  -ms-transform: translate(0, -50%) rotate(90deg);
  transform: translate(0, -50%) rotate(90deg);
}

.emotion-4 .slick-prev span {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-4 [dir="rtl"] .slick-prev {
  right: -0;
  left: auto;
}

.emotion-4 .slick-next {
  right: -0;
  -webkit-transform: translate(0, -50%) rotate(-90deg);
  -moz-transform: translate(0, -50%) rotate(-90deg);
  -ms-transform: translate(0, -50%) rotate(-90deg);
  transform: translate(0, -50%) rotate(-90deg);
}

.emotion-4 .slick-next span {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-4 [dir="rtl"] .slick-next {
  right: auto;
  left: -0;
}

.emotion-4 .slick-dotted.slick-slider {
  margin-bottom: 30px;
}

.emotion-4 .slick-dots {
  position: absolute;
  bottom: -25px;
  display: block;
  width: 100%;
  padding: 0;
  margin: 0;
  list-style: none;
  text-align: center;
}

.emotion-4 .slick-dots li {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: 20px;
  height: 20px;
  margin: 0 5px;
  padding: 0;
  cursor: pointer;
}

.emotion-4 .slick-dots li.slick-active button:before {
  opacity: 0.75;
  color: black;
}

.emotion-4 .slick-dots li button {
  font-size: 0;
  line-height: 0;
  display: block;
  padding: 5px;
  cursor: pointer;
  color: transparent;
  border: 0;
  outline: none;
  background: transparent;
}

.emotion-4 .slick-dots li button:hover,
.emotion-4 .slick-dots li button:focus {
  outline: none;
}

.emotion-4 .slick-dots li button:hover:before,
.emotion-4 .slick-dots li button:focus:before,
.emotion-4 .slick-dots li button:hover:before,
.emotion-4 .slick-dots li button:focus:before {
  opacity: 1;
}

.emotion-4 .slick-dots li button:before {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 5px;
  height: 5px;
  content: "";
  opacity: 0.25;
  background-color: black;
  border-radius: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-5 {
  display: inline-block;
  height: 2rem;
  width: 2rem;
  min-height: 2rem;
  min-width: 2rem;
}

.emotion-5 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-6 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-7 {
  width: 100%;
  position: relative;
}

.emotion-8 {
  overflow: hidden;
  position: relative;
  box-sizing: border-box;
  aspect-ratio: 375/320;
}

.emotion-9 {
  -webkit-background-size: cover;
  background-size: cover;
  aspect-ratio: 375/320;
  overflow: hidden;
}

.emotion-10 {
  width: 100%;
  aspect-ratio: 375/320;
  object-fit: cover;
}

.emotion-11 {
  width: 100%;
  height: 100%;
  position: absolute;
  left: 0;
  top: 0;
  z-index: 1;
}

.emotion-12 {
  height: 100%;
  width: 100%;
  position: absolute;
  top: 0;
  left: 0;
  position: absolute;
  height: 100%;
}

.emotion-13 {
  width: 100%;
  height: 100%;
  padding: 30px 16px;
  box-sizing: border-box;
  position: absolute;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  grid-template-columns: repeat(3, minmax(20%,max-content));
  -webkit-align-content: center;
  -ms-flex-line-pack: center;
  align-content: center;
  grid-template-rows: repeat(3, minmax(0, max-content));
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
}

.emotion-13 .rteWrapperDivs {
  grid-column: 2;
  grid-row: 2;
}

.emotion-13 .ctaWrapperDivs {
  grid-column: 2;
  grid-row: 2;
}

.emotion-14 {
  box-sizing: content-box;
  text-align: right;
}

.emotion-15 {
  text-align: right;
  display: inline-block;
  max-width: 864px;
  width: calc(min(100vw, 1280px) * 0.675);
}

.emotion-15 .amp-cms--p {
  padding: 0;
  margin: 0;
  line-height: 0;
}

.emotion-15 .amp-cms--p>span {
  display: inline;
  white-space: break-spaces;
}

.emotion-15 .amp-cms--p a {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  position: relative;
  z-index: 2;
  pointer-events: auto;
}

.emotion-15 .amp-cms--p a:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-15 .amp-cms--legal-copy {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(2.666666666666667vw, 34.13333333333333px));
  line-height: 1.5;
  letter-spacing: 0;
}

.emotion-15 sup {
  vertical-align: top;
  display: inline-block;
  margin-top: -0.20ex;
}

.emotion-15 sub {
  vertical-align: bottom;
  display: inline-block;
  margin-bottom: -0.20ex;
}

.emotion-15 .amp-cms--f-0 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(2.933333333333333vw, 37.54666666666667px);
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-15 .amp-cms--f-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(2.933333333333333vw, 37.54666666666667px);
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-15 .amp-cms--f-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(2.933333333333333vw, 37.54666666666667px);
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-15 .amp-cms--fn-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(2.933333333333333vw, 37.54666666666667px);
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-15 .amp-cms--body-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(3.733333333333334vw, 47.78666666666667px));
  line-height: 1.5;
  letter-spacing: min(0.08960000000000001vw, 1.1468800000000001px);
  font-weight: 500;
}

.emotion-15 .amp-cms--body-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(3.2vw, 40.96px));
  line-height: 1.5;
  letter-spacing: min(0.0768vw, 0.98304px);
  font-weight: 500;
}

.emotion-15 .amp-cms--body-3 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(3.2vw, 40.96px));
  line-height: 1.5;
  letter-spacing: min(0.0768vw, 0.98304px);
}

.emotion-15 .amp-cms--body-4 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(2.666666666666667vw, 34.13333333333333px));
  line-height: 1.5;
  letter-spacing: 0;
}

.emotion-15 .amp-cms--body-5 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(2.666666666666667vw, 34.13333333333333px));
  line-height: 1.5;
  letter-spacing: 0;
}

.emotion-15 .amp-cms--eyebrow-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(6.133333333333333vw, 78.50666666666667px));
  line-height: 1;
  letter-spacing: min(0.24533333333333335vw, 3.140266666666667px);
  font-weight: 500;
}

.emotion-15 .amp-cms--eyebrow-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(5.066666666666666vw, 64.85333333333334px));
  line-height: 1;
  letter-spacing: min(0.20266666666666666vw, 2.5941333333333336px);
  font-weight: 500;
}

.emotion-15 .amp-cms--eyebrow-3 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(3.733333333333334vw, 47.78666666666667px));
  line-height: 1.0714285714285714;
  letter-spacing: min(0.14933333333333335vw, 1.9114666666666669px);
  font-weight: 500;
}

.emotion-15 .amp-cms--headline-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(18.133333333333333vw, 232.10666666666668px));
  line-height: 1;
  letter-spacing: 0;
  font-weight: 700;
}

.emotion-15 .amp-cms--headline-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(13.333333333333334vw, 170.66666666666669px));
  line-height: 1;
  letter-spacing: 0;
  font-weight: 700;
}

.emotion-15 .amp-cms--headline-3 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(11.466666666666667vw, 146.77333333333334px));
  line-height: 1;
  letter-spacing: 0;
  font-weight: 700;
}

.emotion-15 .amp-cms--headline-4 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(9.6vw, 122.88000000000001px));
  line-height: 1;
  letter-spacing: min(0.19199999999999998vw, 2.4576000000000002px);
  font-weight: 700;
}

.emotion-15 .amp-cms--headline-5 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(7.466666666666668vw, 95.57333333333334px));
  line-height: 1;
  letter-spacing: min(0.2986666666666667vw, 3.8229333333333337px);
  font-weight: 700;
}

.emotion-15 .amp-cms--headline-6 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(6.4vw, 81.92px));
  line-height: 1.0833333333333333;
  letter-spacing: min(0.256vw, 3.2768px);
  font-weight: 700;
}

.emotion-15 .amp-cms--headline-7 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(5.333333333333334vw, 68.26666666666667px));
  line-height: 1;
  letter-spacing: min(0.128vw, 1.6384px);
  font-weight: 700;
}

.emotion-15 .amp-cms--headlineAlt-1 {
  color: #003764;
  font-size: max(13px, min(17.066666666666666vw, 218.45333333333335px));
  line-height: 1;
  letter-spacing: min(-0.07999999999999999vw, -1.024px);
  font-weight: 400;
}

.emotion-15 .amp-cms--headlineAlt-2 {
  color: #003764;
  font-size: max(13px, min(14.399999999999999vw, 184.32000000000002px));
  line-height: 1;
  letter-spacing: min(-0.07999999999999999vw, -1.024px);
  font-weight: 400;
}

.emotion-15 .amp-cms--headlineAlt-3 {
  color: #003764;
  font-size: max(13px, min(9.066666666666666vw, 116.05333333333334px));
  line-height: 1;
  letter-spacing: min(-0.07999999999999999vw, -1.024px);
  font-weight: 400;
}

.emotion-15 .amp-cms--headlineAlt-4 {
  color: #003764;
  font-size: max(13px, min(6.4vw, 81.92px));
  line-height: 1.4166666666666667;
  letter-spacing: min(-0.07999999999999999vw, -1.024px);
  font-weight: 400;
}

.emotion-15 .amp-cms--headlineAlt-5 {
  color: #003764;
  font-size: min(3.733333333333334vw, 47.78666666666667px);
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-15 .amp-cms--headlineAlt-6 {
  color: #003764;
  font-size: min(3.733333333333334vw, 47.78666666666667px);
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-15 .amp-cms--headlineAlt-7 {
  color: #003764;
  font-size: min(3.733333333333334vw, 47.78666666666667px);
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-15 .amp-cms--promo-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(13px, min(16vw, 204.8px));
  line-height: 1;
  letter-spacing: min(0vw, 0px);
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-15 .amp-cms--promo-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(13px, min(10.666666666666668vw, 136.53333333333333px));
  line-height: 1;
  letter-spacing: min(0vw, 0px);
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-15 .amp-cms--promoAlt-1 {
  color: #003764;
  font-size: min(3.733333333333334vw, 47.78666666666667px);
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 600;
}

.emotion-15 .amp-cms--subhead-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(16px, min(5.333333333333334vw, 68.26666666666667px));
  line-height: 1.5;
  letter-spacing: 0;
  font-weight: 500;
}

.emotion-15 .amp-cms--subhead-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(16px, min(4.8vw, 61.440000000000005px));
  line-height: 1.5;
  letter-spacing: 0;
  font-weight: 500;
}

.emotion-15 .amp-cms--subhead-3 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(16px, min(4.266666666666667vw, 54.61333333333334px));
  line-height: 1.5;
  letter-spacing: 0;
  font-weight: 500;
}

.emotion-16 {
  display: block;
  width: 100%;
  position: relative;
  z-index: 2;
  padding-top: 18px;
  text-align: start;
}

.emotion-17 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 700;
  letter-spacing: 1.05px;
  min-height: 44px;
  max-height: auto;
  line-height: 1.8571428571428572;
  padding: 0;
  width: auto;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: left;
  border-left: none;
  border-right: none;
  border-radius: 0;
  background-color: transparent;
  border-bottom: 0;
  -webkit-box-pack: left;
  -ms-flex-pack: left;
  -webkit-justify-content: left;
  justify-content: left;
  color: #003764;
  height: auto;
  -webkit-text-decoration: underline;
  text-decoration: underline;
  text-underline-offset: 3.5px;
  text-decoration-thickness: 3px;
  -webkit-align-self: flex-start;
  -ms-flex-item-align: flex-start;
  align-self: flex-start;
  white-space: normal;
}

.emotion-17:focus {
  outline: none;
}

.emotion-17>span {
  padding: 1px 0;
}

.emotion-17:hover,
.emotion-17:focus {
  text-decoration-color: #003764;
}

.emotion-17:hover span,
.emotion-17:focus span {
  color: #FFFFFF;
  background-color: #003764;
}

.emotion-17:active {
  text-decoration-color: #000000;
}

.emotion-17:active>span {
  color: #FFFFFF;
  background-color: #000000;
}

.emotion-18 {
  box-sizing: border-box;
}

.emotion-25 {
  width: 100%;
  height: 100%;
  padding: 30px 16px;
  box-sizing: border-box;
  position: absolute;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  grid-template-columns: repeat(3, minmax(20%,max-content));
  -webkit-align-content: center;
  -ms-flex-line-pack: center;
  align-content: center;
  grid-template-rows: repeat(3, minmax(0, max-content));
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
}

.emotion-25 .rteWrapperDivs {
  grid-column: 3;
  grid-row: 3;
}

.emotion-25 .ctaWrapperDivs {
  grid-column: 1;
  grid-row: 3;
}

.emotion-26 {
  box-sizing: content-box;
  text-align: center;
}

.emotion-27 {
  text-align: center;
  display: inline-block;
  max-width: 864px;
  width: calc(min(100vw, 1280px) * 0.675);
}

.emotion-27 .amp-cms--p {
  padding: 0;
  margin: 0;
  line-height: 0;
}

.emotion-27 .amp-cms--p>span {
  display: inline;
  white-space: break-spaces;
}

.emotion-27 .amp-cms--p a {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  position: relative;
  z-index: 2;
  pointer-events: auto;
}

.emotion-27 .amp-cms--p a:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-27 .amp-cms--legal-copy {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(2.666666666666667vw, 34.13333333333333px));
  line-height: 1.5;
  letter-spacing: 0;
}

.emotion-27 sup {
  vertical-align: top;
  display: inline-block;
  margin-top: -0.20ex;
}

.emotion-27 sub {
  vertical-align: bottom;
  display: inline-block;
  margin-bottom: -0.20ex;
}

.emotion-27 .amp-cms--f-0 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(2.933333333333333vw, 37.54666666666667px);
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-27 .amp-cms--f-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(2.933333333333333vw, 37.54666666666667px);
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-27 .amp-cms--f-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(2.933333333333333vw, 37.54666666666667px);
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-27 .amp-cms--fn-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(2.933333333333333vw, 37.54666666666667px);
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-27 .amp-cms--body-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(3.733333333333334vw, 47.78666666666667px));
  line-height: 1.5;
  letter-spacing: min(0.08960000000000001vw, 1.1468800000000001px);
  font-weight: 500;
}

.emotion-27 .amp-cms--body-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(3.2vw, 40.96px));
  line-height: 1.5;
  letter-spacing: min(0.0768vw, 0.98304px);
  font-weight: 500;
}

.emotion-27 .amp-cms--body-3 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(3.2vw, 40.96px));
  line-height: 1.5;
  letter-spacing: min(0.0768vw, 0.98304px);
}

.emotion-27 .amp-cms--body-4 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(2.666666666666667vw, 34.13333333333333px));
  line-height: 1.5;
  letter-spacing: 0;
}

.emotion-27 .amp-cms--body-5 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(2.666666666666667vw, 34.13333333333333px));
  line-height: 1.5;
  letter-spacing: 0;
}

.emotion-27 .amp-cms--eyebrow-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(6.133333333333333vw, 78.50666666666667px));
  line-height: 1;
  letter-spacing: min(0.24533333333333335vw, 3.140266666666667px);
  font-weight: 500;
}

.emotion-27 .amp-cms--eyebrow-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(5.066666666666666vw, 64.85333333333334px));
  line-height: 1;
  letter-spacing: min(0.20266666666666666vw, 2.5941333333333336px);
  font-weight: 500;
}

.emotion-27 .amp-cms--eyebrow-3 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(3.733333333333334vw, 47.78666666666667px));
  line-height: 1.0714285714285714;
  letter-spacing: min(0.14933333333333335vw, 1.9114666666666669px);
  font-weight: 500;
}

.emotion-27 .amp-cms--headline-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(18.133333333333333vw, 232.10666666666668px));
  line-height: 1;
  letter-spacing: 0;
  font-weight: 700;
}

.emotion-27 .amp-cms--headline-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(13.333333333333334vw, 170.66666666666669px));
  line-height: 1;
  letter-spacing: 0;
  font-weight: 700;
}

.emotion-27 .amp-cms--headline-3 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(11.466666666666667vw, 146.77333333333334px));
  line-height: 1;
  letter-spacing: 0;
  font-weight: 700;
}

.emotion-27 .amp-cms--headline-4 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(9.6vw, 122.88000000000001px));
  line-height: 1;
  letter-spacing: min(0.19199999999999998vw, 2.4576000000000002px);
  font-weight: 700;
}

.emotion-27 .amp-cms--headline-5 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(7.466666666666668vw, 95.57333333333334px));
  line-height: 1;
  letter-spacing: min(0.2986666666666667vw, 3.8229333333333337px);
  font-weight: 700;
}

.emotion-27 .amp-cms--headline-6 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(6.4vw, 81.92px));
  line-height: 1.0833333333333333;
  letter-spacing: min(0.256vw, 3.2768px);
  font-weight: 700;
}

.emotion-27 .amp-cms--headline-7 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(5.333333333333334vw, 68.26666666666667px));
  line-height: 1;
  letter-spacing: min(0.128vw, 1.6384px);
  font-weight: 700;
}

.emotion-27 .amp-cms--headlineAlt-1 {
  color: #003764;
  font-size: max(13px, min(17.066666666666666vw, 218.45333333333335px));
  line-height: 1;
  letter-spacing: min(-0.07999999999999999vw, -1.024px);
  font-weight: 400;
}

.emotion-27 .amp-cms--headlineAlt-2 {
  color: #003764;
  font-size: max(13px, min(14.399999999999999vw, 184.32000000000002px));
  line-height: 1;
  letter-spacing: min(-0.07999999999999999vw, -1.024px);
  font-weight: 400;
}

.emotion-27 .amp-cms--headlineAlt-3 {
  color: #003764;
  font-size: max(13px, min(9.066666666666666vw, 116.05333333333334px));
  line-height: 1;
  letter-spacing: min(-0.07999999999999999vw, -1.024px);
  font-weight: 400;
}

.emotion-27 .amp-cms--headlineAlt-4 {
  color: #003764;
  font-size: max(13px, min(6.4vw, 81.92px));
  line-height: 1.4166666666666667;
  letter-spacing: min(-0.07999999999999999vw, -1.024px);
  font-weight: 400;
}

.emotion-27 .amp-cms--headlineAlt-5 {
  color: #003764;
  font-size: min(3.733333333333334vw, 47.78666666666667px);
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-27 .amp-cms--headlineAlt-6 {
  color: #003764;
  font-size: min(3.733333333333334vw, 47.78666666666667px);
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-27 .amp-cms--headlineAlt-7 {
  color: #003764;
  font-size: min(3.733333333333334vw, 47.78666666666667px);
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-27 .amp-cms--promo-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(13px, min(16vw, 204.8px));
  line-height: 1;
  letter-spacing: min(0vw, 0px);
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-27 .amp-cms--promo-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(13px, min(10.666666666666668vw, 136.53333333333333px));
  line-height: 1;
  letter-spacing: min(0vw, 0px);
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-27 .amp-cms--promoAlt-1 {
  color: #003764;
  font-size: min(3.733333333333334vw, 47.78666666666667px);
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 600;
}

.emotion-27 .amp-cms--subhead-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(16px, min(5.333333333333334vw, 68.26666666666667px));
  line-height: 1.5;
  letter-spacing: 0;
  font-weight: 500;
}

.emotion-27 .amp-cms--subhead-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(16px, min(4.8vw, 61.440000000000005px));
  line-height: 1.5;
  letter-spacing: 0;
  font-weight: 500;
}

.emotion-27 .amp-cms--subhead-3 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(16px, min(4.266666666666667vw, 54.61333333333334px));
  line-height: 1.5;
  letter-spacing: 0;
  font-weight: 500;
}

.emotion-28 {
  display: block;
  width: 100%;
  position: relative;
  z-index: 2;
  padding-top: 18px;
  text-align: center;
}

.emotion-29 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 700;
  letter-spacing: 1.6800000000000002px;
  min-height: 44px;
  max-height: auto;
  line-height: 1.1428571428571428;
  padding: 12px 22px;
  width: auto;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  background-color: #FFFFFF;
  color: #003764;
  border-color: #003764;
  -webkit-align-self: flex-start;
  -ms-flex-item-align: flex-start;
  align-self: flex-start;
  white-space: normal;
  width: 280px;
}

.emotion-29:focus {
  outline: none;
}

.emotion-29>span {
  padding: 1px 0;
}

.emotion-29:hover,
.emotion-29:focus {
  color: #FFFFFF;
  background-color: #003764;
  border-color: #003764;
}

.emotion-29:active {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  color: #FFFFFF;
  background-color: #000000;
  border-color: #000000;
}

.emotion-47 {
  width: 100%;
  height: 100%;
  padding: 30px 16px;
  box-sizing: border-box;
  position: absolute;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  grid-template-columns: repeat(3, minmax(20%,max-content));
  -webkit-align-content: center;
  -ms-flex-line-pack: center;
  align-content: center;
  grid-template-rows: repeat(3, minmax(0, max-content));
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
}

.emotion-47 .rteWrapperDivs {
  grid-column: 1;
  grid-row: 1;
}

.emotion-47 .ctaWrapperDivs {
  grid-column: 1;
  grid-row: 1;
}

<div>
  <div
    style="font-family: var(--font-family-font1),sans-serif;"
  >
    <div
      class="emotion-0"
    >
      <section
        class="emotion-1"
      >
        <div
          class="emotion-2"
        >
          <nav
            class="emotion-3"
          >
            <div
              class="emotion-4"
            >
              <div
                class="slick-slider slick-initialized"
                dir="ltr"
              >
                <button
                  aria-label="Previous"
                  class="slick-prev slick-arrow slick-prev slick-disabled"
                  data-role="none"
                  disabled=""
                >
                  <span
                    aria-hidden="true"
                    class="emotion-5"
                  >
                    <svg
                      viewBox="0 0 13.29 8.07"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M6.13 7.86a.73.73 0 001.04 0l5.92-5.92a.73.73 0 000-1.03l-.7-.7a.73.73 0 00-1.03 0l-4.71 4.7L1.94.2A.73.73 0 00.9.21l-.69.7a.73.73 0 000 1.03z"
                        fill="#003764"
                      />
                    </svg>
                  </span>
                </button>
                <div
                  class="slick-list"
                >
                  <div
                    class="slick-track"
                    style="opacity: 1; transform: translate3d(0px, 0px, 0px);"
                  >
                    <div
                      aria-hidden="false"
                      class="slick-slide slick-active slick-current"
                      data-index="0"
                      style="outline: none; width: 0px;"
                      tabindex="-1"
                    >
                      <div>
                        <div
                          class="emotion-6"
                        >
                          <div
                            class="emotion-7"
                          >
                            <div
                              class="emotion-8"
                              height="320"
                              width="375"
                            >
                              <div
                                class="emotion-9"
                                data-testid="product-card-image"
                              >
                                <img
                                  alt="Kid Photo"
                                  class="emotion-10"
                                  src="https://1puomodm9vwdc1sabswsbq9607.staging.bigcontent.io/i/oldnavy/G32977_TG_MOB@2x?fmt=webp"
                                />
                              </div>
                            </div>
                            <a
                              class="emotion-11"
                              href="/linkToNewArrivals"
                              tabindex="-1"
                              target="_self"
                              title=""
                            />
                            <div
                              class="emotion-12"
                              data-testid="absolute-wrapper"
                            >
                              <div
                                class="emotion-13"
                              >
                                <div
                                  class="rteWrapperDivs emotion-14"
                                >
                                  <div
                                    class="emotion-15"
                                  >
                                    <div>
                                      <p
                                        class="amp-cms--p"
                                      >
                                        <span
                                          class="amp-cms--subhead-1"
                                        >
                                          Lorem Ipsum
                                        </span>
                                      </p>
                                      <p
                                        class="amp-cms--p"
                                      >
                                        <span
                                          class="amp-cms--body-1"
                                        >
                                          dolor sit amet, consectetur adipiscing elit.
                                        </span>
                                      </p>
                                    </div>
                                  </div>
                                </div>
                                <div
                                  class="ctaWrapperDivs emotion-16"
                                >
                                  <a
                                    class="emotion-17"
                                    color="dark"
                                    href="/buyIt"
                                  >
                                    <span
                                      class="emotion-18"
                                    >
                                      new arrivals
                                    </span>
                                  </a>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div
                      aria-hidden="true"
                      class="slick-slide"
                      data-index="1"
                      style="outline: none; width: 0px;"
                      tabindex="-1"
                    >
                      <div>
                        <div
                          class="emotion-6"
                        >
                          <div
                            class="emotion-7"
                          >
                            <div
                              class="emotion-8"
                              height="320"
                              width="375"
                            >
                              <div
                                class="emotion-9"
                                data-testid="product-card-image"
                              >
                                <img
                                  alt=""
                                  class="emotion-10"
                                  src="https://1puomodm9vwdc1sabswsbq9607.staging.bigcontent.io/i/oldnavy/faded-blue-bg-1000x115?fmt=webp"
                                />
                              </div>
                            </div>
                            <div
                              class="emotion-12"
                              data-testid="absolute-wrapper"
                            >
                              <div
                                class="emotion-25"
                              >
                                <div
                                  class="rteWrapperDivs emotion-26"
                                >
                                  <div
                                    class="emotion-27"
                                  >
                                    <div>
                                      <h2
                                        class="amp-cms--p"
                                      >
                                        <span
                                          class="amp-cms--body-1"
                                        >
                                          OUR MOST LOVED STYLES
                                        </span>
                                      </h2>
                                    </div>
                                  </div>
                                </div>
                                <div
                                  class="ctaWrapperDivs emotion-28"
                                >
                                  <a
                                    class="emotion-29"
                                    color="dark"
                                    href="/buyIt"
                                  >
                                    new arrivals
                                  </a>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div
                      aria-hidden="true"
                      class="slick-slide"
                      data-index="2"
                      style="outline: none; width: 0px;"
                      tabindex="-1"
                    >
                      <div>
                        <div
                          class="emotion-6"
                        >
                          <div
                            class="emotion-7"
                          >
                            <div
                              class="emotion-8"
                              height="320"
                              width="375"
                            >
                              <div
                                class="emotion-9"
                                data-testid="product-card-image"
                              >
                                <img
                                  alt=""
                                  class="emotion-10"
                                  src="https://2hmc8flz4ofg1dy89cc38f2xm.staging.bigcontent.io/i/athleta/annie-spratt-h-LcVG8W1XY-unsplash?fmt=webp"
                                />
                              </div>
                            </div>
                            <a
                              class="emotion-11"
                              href="/productsWeLove"
                              tabindex="-1"
                              target="_self"
                              title="Middle link"
                            />
                            <div
                              class="emotion-12"
                              data-testid="absolute-wrapper"
                            >
                              <div
                                class="emotion-13"
                              >
                                <div
                                  class="ctaWrapperDivs emotion-28"
                                >
                                  <a
                                    class="emotion-29"
                                    color="dark"
                                    href="/ss"
                                  >
                                    Get a life
                                  </a>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div
                      aria-hidden="true"
                      class="slick-slide"
                      data-index="3"
                      style="outline: none; width: 0px;"
                      tabindex="-1"
                    >
                      <div>
                        <div
                          class="emotion-6"
                        >
                          <div
                            class="emotion-7"
                          >
                            <div
                              class="emotion-8"
                              height="320"
                              width="375"
                            >
                              <div
                                class="emotion-9"
                                data-testid="product-card-image"
                              >
                                <img
                                  alt=""
                                  class="emotion-10"
                                  src="https://2hmc8flz4ofg1dy89cc38f2xm.staging.bigcontent.io/i/athleta/annie-spratt-h-LcVG8W1XY-unsplash?fmt=webp"
                                />
                              </div>
                            </div>
                            <a
                              class="emotion-11"
                              href="/lastLink"
                              tabindex="-1"
                              target="_self"
                              title="lastLink"
                            />
                            <div
                              class="emotion-12"
                              data-testid="absolute-wrapper"
                            >
                              <div
                                class="emotion-47"
                              >
                                <div
                                  class="rteWrapperDivs emotion-26"
                                >
                                  <div
                                    class="emotion-27"
                                  >
                                    <div>
                                      <p
                                        class="amp-cms--p"
                                      >
                                        <span
                                          class="amp-cms--headline-6"
                                        >
                                          Lorem Ipsum Dolor Sit Amet,
                                        </span>
                                      </p>
                                      <p
                                        class="amp-cms--p"
                                      >
                                        <span
                                          class="amp-cms--body-1"
                                        >
                                          consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam.
                                        </span>
                                      </p>
                                    </div>
                                  </div>
                                </div>
                                <div
                                  class="ctaWrapperDivs emotion-28"
                                >
                                  <a
                                    class="emotion-29"
                                    color="dark"
                                    href="/ss"
                                  >
                                    Get a life
                                  </a>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <button
                  aria-label="Next"
                  class="slick-next slick-arrow slick-next"
                  data-role="none"
                >
                  <span
                    aria-hidden="true"
                    class="emotion-5"
                  >
                    <svg
                      viewBox="0 0 13.29 8.07"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M6.13 7.86a.73.73 0 001.04 0l5.92-5.92a.73.73 0 000-1.03l-.7-.7a.73.73 0 00-1.03 0l-4.71 4.7L1.94.2A.73.73 0 00.9.21l-.69.7a.73.73 0 000 1.03z"
                        fill="#003764"
                      />
                    </svg>
                  </span>
                </button>
                <ul
                  class="slick-dots"
                  style="display: block;"
                >
                  <li
                    class="slick-active"
                  >
                    <button>
                      1
                    </button>
                  </li>
                  <li
                    class=""
                  >
                    <button>
                      2
                    </button>
                  </li>
                  <li
                    class=""
                  >
                    <button>
                      3
                    </button>
                  </li>
                  <li
                    class=""
                  >
                    <button>
                      4
                    </button>
                  </li>
                </ul>
              </div>
            </div>
          </nav>
        </div>
      </section>
    </div>
  </div>
</div>
`;

exports[`chevron focus, hover should have primary color snaps 1`] = `
.emotion-0 {
  display: inline-block;
  height: 2rem;
  width: 2rem;
  min-height: 2rem;
  min-width: 2rem;
}

.emotion-0 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

<button
  aria-label="Next"
  class="slick-next slick-arrow slick-next"
  data-role="none"
>
  <span
    aria-hidden="true"
    class="emotion-0"
  >
    <svg
      viewBox="0 0 13.29 8.07"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M6.13 7.86a.73.73 0 001.04 0l5.92-5.92a.73.73 0 000-1.03l-.7-.7a.73.73 0 00-1.03 0l-4.71 4.7L1.94.2A.73.73 0 00.9.21l-.69.7a.73.73 0 000 1.03z"
        fill="#003764"
      />
    </svg>
  </span>
</button>
`;

exports[`chevron focus, hover should have secondary color snaps 1`] = `
.emotion-0 {
  display: inline-block;
  height: 2rem;
  width: 2rem;
  min-height: 2rem;
  min-width: 2rem;
}

.emotion-0 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

<button
  aria-label="Next"
  class="slick-next slick-arrow slick-next"
  data-role="none"
>
  <span
    aria-hidden="true"
    class="emotion-0"
  >
    <svg
      viewBox="0 0 13.29 8.07"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M6.13 7.86a.73.73 0 001.04 0l5.92-5.92a.73.73 0 000-1.03l-.7-.7a.73.73 0 00-1.03 0l-4.71 4.7L1.94.2A.73.73 0 00.9.21l-.69.7a.73.73 0 000 1.03z"
        fill="#FFFFFF"
      />
    </svg>
  </span>
</button>
`;

exports[`on mobile should have 4 slides 1`] = `
.emotion-0 {
  margin-left: auto;
  margin-right: auto;
  max-width: 1280px;
}

.emotion-2 {
  position: relative;
}

.emotion-3 {
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-3 .slick-list {
  overflow: hidden;
}

.emotion-3 .slick-track {
  width: -webkit-max-content!important;
  width: -moz-max-content!important;
  width: max-content!important;
}

.emotion-3 button.slick-next.slick-arrow.slick-next,
.emotion-3 button.slick-prev.slick-arrow.slick-prev {
  z-index: 2;
  top: min(42.66666666666667vw, 50%);
  height: 44px;
  width: 44px;
  background: #003764;
  opacity: 75%;
}

.emotion-3 button.slick-next.slick-arrow.slick-next>span,
.emotion-3 button.slick-prev.slick-arrow.slick-prev>span {
  margin: auto;
}

.emotion-3 button.slick-next.slick-arrow.slick-next svg,
.emotion-3 button.slick-prev.slick-arrow.slick-prev svg {
  margin: auto;
  height: 25px;
  width: 14px;
}

.emotion-3 button.slick-next.slick-arrow.slick-next span svg path,
.emotion-3 button.slick-prev.slick-arrow.slick-prev span svg path {
  fill: #ffffff;
}

.emotion-3 button.slick-next.slick-arrow.slick-next:hover,
.emotion-3 button.slick-prev.slick-arrow.slick-prev:hover {
  opacity: 100%;
}

.emotion-3 button.slick-next.slick-arrow.slick-next:focus,
.emotion-3 button.slick-prev.slick-arrow.slick-prev:focus {
  background: #003764;
}

.emotion-3 button.slick-next.slick-arrow.slick-next:focus-visible,
.emotion-3 button.slick-prev.slick-arrow.slick-prev:focus-visible {
  outline: auto;
}

.emotion-3 .slick-slide {
  margin: 0;
  width: auto;
}

.emotion-3 .slick-disabled {
  display: none!important;
}

.emotion-3 .slick-next {
  left: calc(100% - 44px);
}

.emotion-3 .slick-slide:first-of-type a>div:nth-of-type(2)>div>div:nth-of-type(2) {
  border-left: none;
}

.emotion-3 .slick-slide>div:first-of-type {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-3 .slick-slide>div:first-of-type>div:first-of-type {
  width: 100%;
}

.emotion-3 .slick-slider ul.slick-dots {
  bottom: 4px;
  width: unset;
  height: 44px;
  margin-left: 50%;
  -webkit-transform: translateX(-50%);
  -moz-transform: translateX(-50%);
  -ms-transform: translateX(-50%);
  transform: translateX(-50%);
  -webkit-align-content: center;
  -ms-flex-line-pack: center;
  align-content: center;
}

.emotion-3 .slick-slider .slick-dots li.slick-active button:before {
  background-color: #003764;
}

.emotion-3 .slick-slider ul.slick-dots button:before {
  width: 10px;
  height: 10px;
  opacity: 1;
  background-color: #B2B2B2;
}

.emotion-4 {
  position: relative;
}

.emotion-4 .slick-slider {
  position: relative;
  display: block;
  box-sizing: border-box;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  -webkit-touch-callout: none;
  touch-action: pan-y;
  -webkit-tap-highlight-color: transparent;
  scroll-behavior: smooth;
}

.emotion-4 .slick-slider .slick-track,
.emotion-4 .slick-slider .slick-list {
  -webkit-transform: translate3d(0, 0, 0);
  -moz-transform: translate3d(0, 0, 0);
  -ms-transform: translate3d(0, 0, 0);
  transform: translate3d(0, 0, 0);
}

.emotion-4 .slick-list {
  position: relative;
  display: block;
  overflow-x: hidden;
  overflow-y: hidden;
  margin: 0;
  padding: 0;
}

.emotion-4 .slick-list:focus {
  outline: none;
}

.emotion-4 .slick-list.dragging {
  cursor: pointer;
  cursor: hand;
}

.emotion-4 .slick-track {
  position: relative;
  top: 0;
  left: 0;
  display: block;
  margin-left: auto;
  margin-right: auto;
}

.emotion-4 .slick-track:before,
.emotion-4 .slick-track:after {
  display: table;
  content: "";
}

.emotion-4 .slick-track:after {
  clear: both;
}

.emotion-4 .slick-slide {
  display: none;
  float: left;
  height: 100%;
  min-height: 1px;
}

.emotion-4 .slick-slide img {
  display: block;
}

.emotion-4 .slick-slide.slick-loading img {
  display: none;
}

.emotion-4 .slick-slide.dragging img {
  pointer-events: none;
}

.emotion-4 [dir="rtl"] .slick-slide {
  float: right;
}

.emotion-4 .slick-initialized .slick-slide,
.emotion-4 .slick-vertical .slick-slide {
  display: block;
}

.emotion-4 .slick-vertical .slick-slide {
  border: 1px solid transparent;
  height: auto;
}

.emotion-4 .slick-loading .slick-track,
.emotion-4 .slick-loading .slick-slide {
  visibility: hidden;
}

.emotion-4 .slick-loading .slick-list {
  background: #fff url("./ajax-loader.gif") center center no-repeat;
}

.emotion-4 .slick-arrow.slick-hidden {
  display: none;
}

.emotion-4 .slick-prev,
.emotion-4 .slick-next {
  font-size: 0;
  line-height: 0;
  position: absolute;
  top: 50%;
  display: block;
  padding: 0;
  cursor: pointer;
  color: transparent;
  border: none;
  outline: none;
  background: transparent;
}

.emotion-4 .slick-prev:hover,
.emotion-4 .slick-next:hover,
.emotion-4 .slick-prev:focus,
.emotion-4 .slick-next:focus {
  color: transparent;
  outline: none;
  background: transparent;
  opacity: 1;
}

.emotion-4 .slick-prev.slick-disabled,
.emotion-4 .slick-next.slick-disabled {
  opacity: 0.25;
}

.emotion-4 .slick-prev {
  left: -0;
  -webkit-transform: translate(0, -50%) rotate(90deg);
  -moz-transform: translate(0, -50%) rotate(90deg);
  -ms-transform: translate(0, -50%) rotate(90deg);
  transform: translate(0, -50%) rotate(90deg);
}

.emotion-4 .slick-prev span {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-4 [dir="rtl"] .slick-prev {
  right: -0;
  left: auto;
}

.emotion-4 .slick-next {
  right: -0;
  -webkit-transform: translate(0, -50%) rotate(-90deg);
  -moz-transform: translate(0, -50%) rotate(-90deg);
  -ms-transform: translate(0, -50%) rotate(-90deg);
  transform: translate(0, -50%) rotate(-90deg);
}

.emotion-4 .slick-next span {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-4 [dir="rtl"] .slick-next {
  right: auto;
  left: -0;
}

.emotion-4 .slick-dotted.slick-slider {
  margin-bottom: 30px;
}

.emotion-4 .slick-dots {
  position: absolute;
  bottom: -25px;
  display: block;
  width: 100%;
  padding: 0;
  margin: 0;
  list-style: none;
  text-align: center;
}

.emotion-4 .slick-dots li {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: 20px;
  height: 20px;
  margin: 0 5px;
  padding: 0;
  cursor: pointer;
}

.emotion-4 .slick-dots li.slick-active button:before {
  opacity: 0.75;
  color: black;
}

.emotion-4 .slick-dots li button {
  font-size: 0;
  line-height: 0;
  display: block;
  padding: 5px;
  cursor: pointer;
  color: transparent;
  border: 0;
  outline: none;
  background: transparent;
}

.emotion-4 .slick-dots li button:hover,
.emotion-4 .slick-dots li button:focus {
  outline: none;
}

.emotion-4 .slick-dots li button:hover:before,
.emotion-4 .slick-dots li button:focus:before,
.emotion-4 .slick-dots li button:hover:before,
.emotion-4 .slick-dots li button:focus:before {
  opacity: 1;
}

.emotion-4 .slick-dots li button:before {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 5px;
  height: 5px;
  content: "";
  opacity: 0.25;
  background-color: black;
  border-radius: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-5 {
  display: inline-block;
  height: 2rem;
  width: 2rem;
  min-height: 2rem;
  min-width: 2rem;
}

.emotion-5 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-6 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-7 {
  width: 100%;
  position: relative;
}

.emotion-8 {
  overflow: hidden;
  position: relative;
  box-sizing: border-box;
  aspect-ratio: 375/320;
}

.emotion-9 {
  -webkit-background-size: cover;
  background-size: cover;
  aspect-ratio: 375/320;
  overflow: hidden;
}

.emotion-10 {
  width: 100%;
  aspect-ratio: 375/320;
  object-fit: cover;
}

.emotion-11 {
  width: 100%;
  height: 100%;
  position: absolute;
  left: 0;
  top: 0;
  z-index: 1;
}

.emotion-12 {
  height: 100%;
  width: 100%;
  position: absolute;
  top: 0;
  left: 0;
  position: absolute;
  height: 100%;
}

.emotion-13 {
  width: 100%;
  height: 100%;
  padding: 30px 16px;
  box-sizing: border-box;
  position: absolute;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  grid-template-columns: repeat(3, minmax(20%,max-content));
  -webkit-align-content: center;
  -ms-flex-line-pack: center;
  align-content: center;
  grid-template-rows: repeat(3, minmax(0, max-content));
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
}

.emotion-13 .rteWrapperDivs {
  grid-column: 2;
  grid-row: 2;
}

.emotion-13 .ctaWrapperDivs {
  grid-column: 2;
  grid-row: 2;
}

.emotion-14 {
  box-sizing: content-box;
  text-align: right;
}

.emotion-15 {
  text-align: right;
  display: inline-block;
  max-width: 864px;
  width: calc(min(100vw, 1280px) * 0.675);
}

.emotion-15 .amp-cms--p {
  padding: 0;
  margin: 0;
  line-height: 0;
}

.emotion-15 .amp-cms--p>span {
  display: inline;
  white-space: break-spaces;
}

.emotion-15 .amp-cms--p a {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  position: relative;
  z-index: 2;
  pointer-events: auto;
}

.emotion-15 .amp-cms--p a:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-15 .amp-cms--legal-copy {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(2.666666666666667vw, 34.13333333333333px));
  line-height: 1.5;
  letter-spacing: 0;
}

.emotion-15 sup {
  vertical-align: top;
  display: inline-block;
  margin-top: -0.20ex;
}

.emotion-15 sub {
  vertical-align: bottom;
  display: inline-block;
  margin-bottom: -0.20ex;
}

.emotion-15 .amp-cms--f-0 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(2.933333333333333vw, 37.54666666666667px);
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-15 .amp-cms--f-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(2.933333333333333vw, 37.54666666666667px);
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-15 .amp-cms--f-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(2.933333333333333vw, 37.54666666666667px);
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-15 .amp-cms--fn-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(2.933333333333333vw, 37.54666666666667px);
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-15 .amp-cms--body-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(3.733333333333334vw, 47.78666666666667px));
  line-height: 1.5;
  letter-spacing: min(0.08960000000000001vw, 1.1468800000000001px);
  font-weight: 500;
}

.emotion-15 .amp-cms--body-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(3.2vw, 40.96px));
  line-height: 1.5;
  letter-spacing: min(0.0768vw, 0.98304px);
  font-weight: 500;
}

.emotion-15 .amp-cms--body-3 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(3.2vw, 40.96px));
  line-height: 1.5;
  letter-spacing: min(0.0768vw, 0.98304px);
}

.emotion-15 .amp-cms--body-4 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(2.666666666666667vw, 34.13333333333333px));
  line-height: 1.5;
  letter-spacing: 0;
}

.emotion-15 .amp-cms--body-5 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(2.666666666666667vw, 34.13333333333333px));
  line-height: 1.5;
  letter-spacing: 0;
}

.emotion-15 .amp-cms--eyebrow-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(6.133333333333333vw, 78.50666666666667px));
  line-height: 1;
  letter-spacing: min(0.24533333333333335vw, 3.140266666666667px);
  font-weight: 500;
}

.emotion-15 .amp-cms--eyebrow-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(5.066666666666666vw, 64.85333333333334px));
  line-height: 1;
  letter-spacing: min(0.20266666666666666vw, 2.5941333333333336px);
  font-weight: 500;
}

.emotion-15 .amp-cms--eyebrow-3 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(3.733333333333334vw, 47.78666666666667px));
  line-height: 1.0714285714285714;
  letter-spacing: min(0.14933333333333335vw, 1.9114666666666669px);
  font-weight: 500;
}

.emotion-15 .amp-cms--headline-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(18.133333333333333vw, 232.10666666666668px));
  line-height: 1;
  letter-spacing: 0;
  font-weight: 700;
}

.emotion-15 .amp-cms--headline-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(13.333333333333334vw, 170.66666666666669px));
  line-height: 1;
  letter-spacing: 0;
  font-weight: 700;
}

.emotion-15 .amp-cms--headline-3 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(11.466666666666667vw, 146.77333333333334px));
  line-height: 1;
  letter-spacing: 0;
  font-weight: 700;
}

.emotion-15 .amp-cms--headline-4 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(9.6vw, 122.88000000000001px));
  line-height: 1;
  letter-spacing: min(0.19199999999999998vw, 2.4576000000000002px);
  font-weight: 700;
}

.emotion-15 .amp-cms--headline-5 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(7.466666666666668vw, 95.57333333333334px));
  line-height: 1;
  letter-spacing: min(0.2986666666666667vw, 3.8229333333333337px);
  font-weight: 700;
}

.emotion-15 .amp-cms--headline-6 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(6.4vw, 81.92px));
  line-height: 1.0833333333333333;
  letter-spacing: min(0.256vw, 3.2768px);
  font-weight: 700;
}

.emotion-15 .amp-cms--headline-7 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(5.333333333333334vw, 68.26666666666667px));
  line-height: 1;
  letter-spacing: min(0.128vw, 1.6384px);
  font-weight: 700;
}

.emotion-15 .amp-cms--headlineAlt-1 {
  color: #003764;
  font-size: max(13px, min(17.066666666666666vw, 218.45333333333335px));
  line-height: 1;
  letter-spacing: min(-0.07999999999999999vw, -1.024px);
  font-weight: 400;
}

.emotion-15 .amp-cms--headlineAlt-2 {
  color: #003764;
  font-size: max(13px, min(14.399999999999999vw, 184.32000000000002px));
  line-height: 1;
  letter-spacing: min(-0.07999999999999999vw, -1.024px);
  font-weight: 400;
}

.emotion-15 .amp-cms--headlineAlt-3 {
  color: #003764;
  font-size: max(13px, min(9.066666666666666vw, 116.05333333333334px));
  line-height: 1;
  letter-spacing: min(-0.07999999999999999vw, -1.024px);
  font-weight: 400;
}

.emotion-15 .amp-cms--headlineAlt-4 {
  color: #003764;
  font-size: max(13px, min(6.4vw, 81.92px));
  line-height: 1.4166666666666667;
  letter-spacing: min(-0.07999999999999999vw, -1.024px);
  font-weight: 400;
}

.emotion-15 .amp-cms--headlineAlt-5 {
  color: #003764;
  font-size: min(3.733333333333334vw, 47.78666666666667px);
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-15 .amp-cms--headlineAlt-6 {
  color: #003764;
  font-size: min(3.733333333333334vw, 47.78666666666667px);
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-15 .amp-cms--headlineAlt-7 {
  color: #003764;
  font-size: min(3.733333333333334vw, 47.78666666666667px);
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-15 .amp-cms--promo-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(13px, min(16vw, 204.8px));
  line-height: 1;
  letter-spacing: min(0vw, 0px);
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-15 .amp-cms--promo-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(13px, min(10.666666666666668vw, 136.53333333333333px));
  line-height: 1;
  letter-spacing: min(0vw, 0px);
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-15 .amp-cms--promoAlt-1 {
  color: #003764;
  font-size: min(3.733333333333334vw, 47.78666666666667px);
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 600;
}

.emotion-15 .amp-cms--subhead-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(16px, min(5.333333333333334vw, 68.26666666666667px));
  line-height: 1.5;
  letter-spacing: 0;
  font-weight: 500;
}

.emotion-15 .amp-cms--subhead-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(16px, min(4.8vw, 61.440000000000005px));
  line-height: 1.5;
  letter-spacing: 0;
  font-weight: 500;
}

.emotion-15 .amp-cms--subhead-3 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(16px, min(4.266666666666667vw, 54.61333333333334px));
  line-height: 1.5;
  letter-spacing: 0;
  font-weight: 500;
}

.emotion-16 {
  display: block;
  width: 100%;
  position: relative;
  z-index: 2;
  padding-top: 18px;
  text-align: start;
}

.emotion-17 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 700;
  letter-spacing: 1.05px;
  min-height: 44px;
  max-height: auto;
  line-height: 1.8571428571428572;
  padding: 0;
  width: auto;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: left;
  border-left: none;
  border-right: none;
  border-radius: 0;
  background-color: transparent;
  border-bottom: 0;
  -webkit-box-pack: left;
  -ms-flex-pack: left;
  -webkit-justify-content: left;
  justify-content: left;
  color: #003764;
  height: auto;
  -webkit-text-decoration: underline;
  text-decoration: underline;
  text-underline-offset: 3.5px;
  text-decoration-thickness: 3px;
  -webkit-align-self: flex-start;
  -ms-flex-item-align: flex-start;
  align-self: flex-start;
  white-space: normal;
}

.emotion-17:focus {
  outline: none;
}

.emotion-17>span {
  padding: 1px 0;
}

.emotion-17:hover,
.emotion-17:focus {
  text-decoration-color: #003764;
}

.emotion-17:hover span,
.emotion-17:focus span {
  color: #FFFFFF;
  background-color: #003764;
}

.emotion-17:active {
  text-decoration-color: #000000;
}

.emotion-17:active>span {
  color: #FFFFFF;
  background-color: #000000;
}

.emotion-18 {
  box-sizing: border-box;
}

.emotion-25 {
  width: 100%;
  height: 100%;
  padding: 30px 16px;
  box-sizing: border-box;
  position: absolute;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  grid-template-columns: repeat(3, minmax(20%,max-content));
  -webkit-align-content: center;
  -ms-flex-line-pack: center;
  align-content: center;
  grid-template-rows: repeat(3, minmax(0, max-content));
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
}

.emotion-25 .rteWrapperDivs {
  grid-column: 3;
  grid-row: 3;
}

.emotion-25 .ctaWrapperDivs {
  grid-column: 1;
  grid-row: 3;
}

.emotion-26 {
  box-sizing: content-box;
  text-align: center;
}

.emotion-27 {
  text-align: center;
  display: inline-block;
  max-width: 864px;
  width: calc(min(100vw, 1280px) * 0.675);
}

.emotion-27 .amp-cms--p {
  padding: 0;
  margin: 0;
  line-height: 0;
}

.emotion-27 .amp-cms--p>span {
  display: inline;
  white-space: break-spaces;
}

.emotion-27 .amp-cms--p a {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  position: relative;
  z-index: 2;
  pointer-events: auto;
}

.emotion-27 .amp-cms--p a:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-27 .amp-cms--legal-copy {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(2.666666666666667vw, 34.13333333333333px));
  line-height: 1.5;
  letter-spacing: 0;
}

.emotion-27 sup {
  vertical-align: top;
  display: inline-block;
  margin-top: -0.20ex;
}

.emotion-27 sub {
  vertical-align: bottom;
  display: inline-block;
  margin-bottom: -0.20ex;
}

.emotion-27 .amp-cms--f-0 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(2.933333333333333vw, 37.54666666666667px);
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-27 .amp-cms--f-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(2.933333333333333vw, 37.54666666666667px);
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-27 .amp-cms--f-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(2.933333333333333vw, 37.54666666666667px);
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-27 .amp-cms--fn-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(2.933333333333333vw, 37.54666666666667px);
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-27 .amp-cms--body-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(3.733333333333334vw, 47.78666666666667px));
  line-height: 1.5;
  letter-spacing: min(0.08960000000000001vw, 1.1468800000000001px);
  font-weight: 500;
}

.emotion-27 .amp-cms--body-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(3.2vw, 40.96px));
  line-height: 1.5;
  letter-spacing: min(0.0768vw, 0.98304px);
  font-weight: 500;
}

.emotion-27 .amp-cms--body-3 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(3.2vw, 40.96px));
  line-height: 1.5;
  letter-spacing: min(0.0768vw, 0.98304px);
}

.emotion-27 .amp-cms--body-4 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(2.666666666666667vw, 34.13333333333333px));
  line-height: 1.5;
  letter-spacing: 0;
}

.emotion-27 .amp-cms--body-5 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(2.666666666666667vw, 34.13333333333333px));
  line-height: 1.5;
  letter-spacing: 0;
}

.emotion-27 .amp-cms--eyebrow-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(6.133333333333333vw, 78.50666666666667px));
  line-height: 1;
  letter-spacing: min(0.24533333333333335vw, 3.140266666666667px);
  font-weight: 500;
}

.emotion-27 .amp-cms--eyebrow-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(5.066666666666666vw, 64.85333333333334px));
  line-height: 1;
  letter-spacing: min(0.20266666666666666vw, 2.5941333333333336px);
  font-weight: 500;
}

.emotion-27 .amp-cms--eyebrow-3 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(3.733333333333334vw, 47.78666666666667px));
  line-height: 1.0714285714285714;
  letter-spacing: min(0.14933333333333335vw, 1.9114666666666669px);
  font-weight: 500;
}

.emotion-27 .amp-cms--headline-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(18.133333333333333vw, 232.10666666666668px));
  line-height: 1;
  letter-spacing: 0;
  font-weight: 700;
}

.emotion-27 .amp-cms--headline-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(13.333333333333334vw, 170.66666666666669px));
  line-height: 1;
  letter-spacing: 0;
  font-weight: 700;
}

.emotion-27 .amp-cms--headline-3 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(11.466666666666667vw, 146.77333333333334px));
  line-height: 1;
  letter-spacing: 0;
  font-weight: 700;
}

.emotion-27 .amp-cms--headline-4 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(9.6vw, 122.88000000000001px));
  line-height: 1;
  letter-spacing: min(0.19199999999999998vw, 2.4576000000000002px);
  font-weight: 700;
}

.emotion-27 .amp-cms--headline-5 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(7.466666666666668vw, 95.57333333333334px));
  line-height: 1;
  letter-spacing: min(0.2986666666666667vw, 3.8229333333333337px);
  font-weight: 700;
}

.emotion-27 .amp-cms--headline-6 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(6.4vw, 81.92px));
  line-height: 1.0833333333333333;
  letter-spacing: min(0.256vw, 3.2768px);
  font-weight: 700;
}

.emotion-27 .amp-cms--headline-7 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(5.333333333333334vw, 68.26666666666667px));
  line-height: 1;
  letter-spacing: min(0.128vw, 1.6384px);
  font-weight: 700;
}

.emotion-27 .amp-cms--headlineAlt-1 {
  color: #003764;
  font-size: max(13px, min(17.066666666666666vw, 218.45333333333335px));
  line-height: 1;
  letter-spacing: min(-0.07999999999999999vw, -1.024px);
  font-weight: 400;
}

.emotion-27 .amp-cms--headlineAlt-2 {
  color: #003764;
  font-size: max(13px, min(14.399999999999999vw, 184.32000000000002px));
  line-height: 1;
  letter-spacing: min(-0.07999999999999999vw, -1.024px);
  font-weight: 400;
}

.emotion-27 .amp-cms--headlineAlt-3 {
  color: #003764;
  font-size: max(13px, min(9.066666666666666vw, 116.05333333333334px));
  line-height: 1;
  letter-spacing: min(-0.07999999999999999vw, -1.024px);
  font-weight: 400;
}

.emotion-27 .amp-cms--headlineAlt-4 {
  color: #003764;
  font-size: max(13px, min(6.4vw, 81.92px));
  line-height: 1.4166666666666667;
  letter-spacing: min(-0.07999999999999999vw, -1.024px);
  font-weight: 400;
}

.emotion-27 .amp-cms--headlineAlt-5 {
  color: #003764;
  font-size: min(3.733333333333334vw, 47.78666666666667px);
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-27 .amp-cms--headlineAlt-6 {
  color: #003764;
  font-size: min(3.733333333333334vw, 47.78666666666667px);
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-27 .amp-cms--headlineAlt-7 {
  color: #003764;
  font-size: min(3.733333333333334vw, 47.78666666666667px);
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-27 .amp-cms--promo-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(13px, min(16vw, 204.8px));
  line-height: 1;
  letter-spacing: min(0vw, 0px);
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-27 .amp-cms--promo-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(13px, min(10.666666666666668vw, 136.53333333333333px));
  line-height: 1;
  letter-spacing: min(0vw, 0px);
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-27 .amp-cms--promoAlt-1 {
  color: #003764;
  font-size: min(3.733333333333334vw, 47.78666666666667px);
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 600;
}

.emotion-27 .amp-cms--subhead-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(16px, min(5.333333333333334vw, 68.26666666666667px));
  line-height: 1.5;
  letter-spacing: 0;
  font-weight: 500;
}

.emotion-27 .amp-cms--subhead-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(16px, min(4.8vw, 61.440000000000005px));
  line-height: 1.5;
  letter-spacing: 0;
  font-weight: 500;
}

.emotion-27 .amp-cms--subhead-3 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(16px, min(4.266666666666667vw, 54.61333333333334px));
  line-height: 1.5;
  letter-spacing: 0;
  font-weight: 500;
}

.emotion-28 {
  display: block;
  width: 100%;
  position: relative;
  z-index: 2;
  padding-top: 18px;
  text-align: center;
}

.emotion-29 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 700;
  letter-spacing: 1.6800000000000002px;
  min-height: 44px;
  max-height: auto;
  line-height: 1.1428571428571428;
  padding: 12px 22px;
  width: auto;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  background-color: #FFFFFF;
  color: #003764;
  border-color: #003764;
  -webkit-align-self: flex-start;
  -ms-flex-item-align: flex-start;
  align-self: flex-start;
  white-space: normal;
  width: 280px;
}

.emotion-29:focus {
  outline: none;
}

.emotion-29>span {
  padding: 1px 0;
}

.emotion-29:hover,
.emotion-29:focus {
  color: #FFFFFF;
  background-color: #003764;
  border-color: #003764;
}

.emotion-29:active {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  color: #FFFFFF;
  background-color: #000000;
  border-color: #000000;
}

.emotion-47 {
  width: 100%;
  height: 100%;
  padding: 30px 16px;
  box-sizing: border-box;
  position: absolute;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  grid-template-columns: repeat(3, minmax(20%,max-content));
  -webkit-align-content: center;
  -ms-flex-line-pack: center;
  align-content: center;
  grid-template-rows: repeat(3, minmax(0, max-content));
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
}

.emotion-47 .rteWrapperDivs {
  grid-column: 1;
  grid-row: 1;
}

.emotion-47 .ctaWrapperDivs {
  grid-column: 1;
  grid-row: 1;
}

<div>
  <div
    style="font-family: var(--font-family-font1),sans-serif;"
  >
    <div
      class="emotion-0"
    >
      <section
        class="emotion-1"
      >
        <div
          class="emotion-2"
        >
          <nav
            class="emotion-3"
          >
            <div
              class="emotion-4"
            >
              <div
                class="slick-slider slick-initialized"
                dir="ltr"
              >
                <button
                  aria-label="Previous"
                  class="slick-prev slick-arrow slick-prev slick-disabled"
                  data-role="none"
                  disabled=""
                >
                  <span
                    aria-hidden="true"
                    class="emotion-5"
                  >
                    <svg
                      viewBox="0 0 13.29 8.07"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M6.13 7.86a.73.73 0 001.04 0l5.92-5.92a.73.73 0 000-1.03l-.7-.7a.73.73 0 00-1.03 0l-4.71 4.7L1.94.2A.73.73 0 00.9.21l-.69.7a.73.73 0 000 1.03z"
                        fill="#003764"
                      />
                    </svg>
                  </span>
                </button>
                <div
                  class="slick-list"
                >
                  <div
                    class="slick-track"
                    style="opacity: 1; transform: translate3d(0px, 0px, 0px);"
                  >
                    <div
                      aria-hidden="false"
                      class="slick-slide slick-active slick-current"
                      data-index="0"
                      style="outline: none; width: 0px;"
                      tabindex="-1"
                    >
                      <div>
                        <div
                          class="emotion-6"
                        >
                          <div
                            class="emotion-7"
                          >
                            <div
                              class="emotion-8"
                              height="320"
                              width="375"
                            >
                              <div
                                class="emotion-9"
                                data-testid="product-card-image"
                              >
                                <img
                                  alt="Kid Photo"
                                  class="emotion-10"
                                  src="https://1puomodm9vwdc1sabswsbq9607.staging.bigcontent.io/i/oldnavy/G32977_TG_MOB@2x?fmt=webp"
                                />
                              </div>
                            </div>
                            <a
                              class="emotion-11"
                              href="/linkToNewArrivals"
                              tabindex="-1"
                              target="_self"
                              title=""
                            />
                            <div
                              class="emotion-12"
                              data-testid="absolute-wrapper"
                            >
                              <div
                                class="emotion-13"
                              >
                                <div
                                  class="rteWrapperDivs emotion-14"
                                >
                                  <div
                                    class="emotion-15"
                                  >
                                    <div>
                                      <p
                                        class="amp-cms--p"
                                      >
                                        <span
                                          class="amp-cms--subhead-1"
                                        >
                                          Lorem Ipsum
                                        </span>
                                      </p>
                                      <p
                                        class="amp-cms--p"
                                      >
                                        <span
                                          class="amp-cms--body-1"
                                        >
                                          dolor sit amet, consectetur adipiscing elit.
                                        </span>
                                      </p>
                                    </div>
                                  </div>
                                </div>
                                <div
                                  class="ctaWrapperDivs emotion-16"
                                >
                                  <a
                                    class="emotion-17"
                                    color="dark"
                                    href="/buyIt"
                                  >
                                    <span
                                      class="emotion-18"
                                    >
                                      new arrivals
                                    </span>
                                  </a>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div
                      aria-hidden="true"
                      class="slick-slide"
                      data-index="1"
                      style="outline: none; width: 0px;"
                      tabindex="-1"
                    >
                      <div>
                        <div
                          class="emotion-6"
                        >
                          <div
                            class="emotion-7"
                          >
                            <div
                              class="emotion-8"
                              height="320"
                              width="375"
                            >
                              <div
                                class="emotion-9"
                                data-testid="product-card-image"
                              >
                                <img
                                  alt=""
                                  class="emotion-10"
                                  src="https://1puomodm9vwdc1sabswsbq9607.staging.bigcontent.io/i/oldnavy/faded-blue-bg-1000x115?fmt=webp"
                                />
                              </div>
                            </div>
                            <div
                              class="emotion-12"
                              data-testid="absolute-wrapper"
                            >
                              <div
                                class="emotion-25"
                              >
                                <div
                                  class="rteWrapperDivs emotion-26"
                                >
                                  <div
                                    class="emotion-27"
                                  >
                                    <div>
                                      <h2
                                        class="amp-cms--p"
                                      >
                                        <span
                                          class="amp-cms--body-1"
                                        >
                                          OUR MOST LOVED STYLES
                                        </span>
                                      </h2>
                                    </div>
                                  </div>
                                </div>
                                <div
                                  class="ctaWrapperDivs emotion-28"
                                >
                                  <a
                                    class="emotion-29"
                                    color="dark"
                                    href="/buyIt"
                                  >
                                    new arrivals
                                  </a>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div
                      aria-hidden="true"
                      class="slick-slide"
                      data-index="2"
                      style="outline: none; width: 0px;"
                      tabindex="-1"
                    >
                      <div>
                        <div
                          class="emotion-6"
                        >
                          <div
                            class="emotion-7"
                          >
                            <div
                              class="emotion-8"
                              height="320"
                              width="375"
                            >
                              <div
                                class="emotion-9"
                                data-testid="product-card-image"
                              >
                                <img
                                  alt=""
                                  class="emotion-10"
                                  src="https://2hmc8flz4ofg1dy89cc38f2xm.staging.bigcontent.io/i/athleta/annie-spratt-h-LcVG8W1XY-unsplash?fmt=webp"
                                />
                              </div>
                            </div>
                            <a
                              class="emotion-11"
                              href="/productsWeLove"
                              tabindex="-1"
                              target="_self"
                              title="Middle link"
                            />
                            <div
                              class="emotion-12"
                              data-testid="absolute-wrapper"
                            >
                              <div
                                class="emotion-13"
                              >
                                <div
                                  class="ctaWrapperDivs emotion-28"
                                >
                                  <a
                                    class="emotion-29"
                                    color="dark"
                                    href="/ss"
                                  >
                                    Get a life
                                  </a>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div
                      aria-hidden="true"
                      class="slick-slide"
                      data-index="3"
                      style="outline: none; width: 0px;"
                      tabindex="-1"
                    >
                      <div>
                        <div
                          class="emotion-6"
                        >
                          <div
                            class="emotion-7"
                          >
                            <div
                              class="emotion-8"
                              height="320"
                              width="375"
                            >
                              <div
                                class="emotion-9"
                                data-testid="product-card-image"
                              >
                                <img
                                  alt=""
                                  class="emotion-10"
                                  src="https://2hmc8flz4ofg1dy89cc38f2xm.staging.bigcontent.io/i/athleta/annie-spratt-h-LcVG8W1XY-unsplash?fmt=webp"
                                />
                              </div>
                            </div>
                            <a
                              class="emotion-11"
                              href="/lastLink"
                              tabindex="-1"
                              target="_self"
                              title="lastLink"
                            />
                            <div
                              class="emotion-12"
                              data-testid="absolute-wrapper"
                            >
                              <div
                                class="emotion-47"
                              >
                                <div
                                  class="rteWrapperDivs emotion-26"
                                >
                                  <div
                                    class="emotion-27"
                                  >
                                    <div>
                                      <p
                                        class="amp-cms--p"
                                      >
                                        <span
                                          class="amp-cms--headline-6"
                                        >
                                          Lorem Ipsum Dolor Sit Amet,
                                        </span>
                                      </p>
                                      <p
                                        class="amp-cms--p"
                                      >
                                        <span
                                          class="amp-cms--body-1"
                                        >
                                          consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam.
                                        </span>
                                      </p>
                                    </div>
                                  </div>
                                </div>
                                <div
                                  class="ctaWrapperDivs emotion-28"
                                >
                                  <a
                                    class="emotion-29"
                                    color="dark"
                                    href="/ss"
                                  >
                                    Get a life
                                  </a>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <button
                  aria-label="Next"
                  class="slick-next slick-arrow slick-next"
                  data-role="none"
                >
                  <span
                    aria-hidden="true"
                    class="emotion-5"
                  >
                    <svg
                      viewBox="0 0 13.29 8.07"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M6.13 7.86a.73.73 0 001.04 0l5.92-5.92a.73.73 0 000-1.03l-.7-.7a.73.73 0 00-1.03 0l-4.71 4.7L1.94.2A.73.73 0 00.9.21l-.69.7a.73.73 0 000 1.03z"
                        fill="#003764"
                      />
                    </svg>
                  </span>
                </button>
                <ul
                  class="slick-dots"
                  style="display: block;"
                >
                  <li
                    class="slick-active"
                  >
                    <button>
                      1
                    </button>
                  </li>
                  <li
                    class=""
                  >
                    <button>
                      2
                    </button>
                  </li>
                  <li
                    class=""
                  >
                    <button>
                      3
                    </button>
                  </li>
                  <li
                    class=""
                  >
                    <button>
                      4
                    </button>
                  </li>
                </ul>
              </div>
            </div>
          </nav>
        </div>
      </section>
    </div>
  </div>
</div>
`;

exports[`on mobile should match snapshot for persistent below on mobile 1`] = `
<DocumentFragment>
  .emotion-0 {
  margin-left: auto;
  margin-right: auto;
  max-width: 1280px;
}

.emotion-1 nav .slick-slider ul.slick-dots {
  bottom: -185px;
}

.emotion-2 {
  position: relative;
}

.emotion-3 {
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-3 .slick-list {
  overflow: hidden;
}

.emotion-3 .slick-track {
  width: -webkit-max-content!important;
  width: -moz-max-content!important;
  width: max-content!important;
}

.emotion-3 button.slick-next.slick-arrow.slick-next,
.emotion-3 button.slick-prev.slick-arrow.slick-prev {
  z-index: 2;
  top: min(42.66666666666667vw, 50%);
  height: 44px;
  width: 44px;
  background: #003764;
  opacity: 75%;
}

.emotion-3 button.slick-next.slick-arrow.slick-next>span,
.emotion-3 button.slick-prev.slick-arrow.slick-prev>span {
  margin: auto;
}

.emotion-3 button.slick-next.slick-arrow.slick-next svg,
.emotion-3 button.slick-prev.slick-arrow.slick-prev svg {
  margin: auto;
  height: 25px;
  width: 14px;
}

.emotion-3 button.slick-next.slick-arrow.slick-next span svg path,
.emotion-3 button.slick-prev.slick-arrow.slick-prev span svg path {
  fill: #ffffff;
}

.emotion-3 button.slick-next.slick-arrow.slick-next:hover,
.emotion-3 button.slick-prev.slick-arrow.slick-prev:hover {
  opacity: 100%;
}

.emotion-3 button.slick-next.slick-arrow.slick-next:focus,
.emotion-3 button.slick-prev.slick-arrow.slick-prev:focus {
  background: #003764;
}

.emotion-3 button.slick-next.slick-arrow.slick-next:focus-visible,
.emotion-3 button.slick-prev.slick-arrow.slick-prev:focus-visible {
  outline: auto;
}

.emotion-3 .slick-slide {
  margin: 0;
  width: auto;
}

.emotion-3 .slick-disabled {
  display: none!important;
}

.emotion-3 .slick-next {
  left: calc(100% - 44px);
}

.emotion-3 .slick-slide:first-of-type a>div:nth-of-type(2)>div>div:nth-of-type(2) {
  border-left: none;
}

.emotion-3 .slick-slide>div:first-of-type {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-3 .slick-slide>div:first-of-type>div:first-of-type {
  width: 100%;
}

.emotion-3 .slick-slider ul.slick-dots {
  bottom: 4px;
  width: unset;
  height: 44px;
  margin-left: 50%;
  -webkit-transform: translateX(-50%);
  -moz-transform: translateX(-50%);
  -ms-transform: translateX(-50%);
  transform: translateX(-50%);
  -webkit-align-content: center;
  -ms-flex-line-pack: center;
  align-content: center;
}

.emotion-3 .slick-slider .slick-dots li.slick-active button:before {
  background-color: #003764;
}

.emotion-3 .slick-slider ul.slick-dots button:before {
  width: 10px;
  height: 10px;
  opacity: 1;
  background-color: #B2B2B2;
}

.emotion-4 {
  position: relative;
}

.emotion-4 .slick-slider {
  position: relative;
  display: block;
  box-sizing: border-box;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  -webkit-touch-callout: none;
  touch-action: pan-y;
  -webkit-tap-highlight-color: transparent;
  scroll-behavior: smooth;
}

.emotion-4 .slick-slider .slick-track,
.emotion-4 .slick-slider .slick-list {
  -webkit-transform: translate3d(0, 0, 0);
  -moz-transform: translate3d(0, 0, 0);
  -ms-transform: translate3d(0, 0, 0);
  transform: translate3d(0, 0, 0);
}

.emotion-4 .slick-list {
  position: relative;
  display: block;
  overflow-x: hidden;
  overflow-y: hidden;
  margin: 0;
  padding: 0;
}

.emotion-4 .slick-list:focus {
  outline: none;
}

.emotion-4 .slick-list.dragging {
  cursor: pointer;
  cursor: hand;
}

.emotion-4 .slick-track {
  position: relative;
  top: 0;
  left: 0;
  display: block;
  margin-left: auto;
  margin-right: auto;
}

.emotion-4 .slick-track:before,
.emotion-4 .slick-track:after {
  display: table;
  content: "";
}

.emotion-4 .slick-track:after {
  clear: both;
}

.emotion-4 .slick-slide {
  display: none;
  float: left;
  height: 100%;
  min-height: 1px;
}

.emotion-4 .slick-slide img {
  display: block;
}

.emotion-4 .slick-slide.slick-loading img {
  display: none;
}

.emotion-4 .slick-slide.dragging img {
  pointer-events: none;
}

.emotion-4 [dir="rtl"] .slick-slide {
  float: right;
}

.emotion-4 .slick-initialized .slick-slide,
.emotion-4 .slick-vertical .slick-slide {
  display: block;
}

.emotion-4 .slick-vertical .slick-slide {
  border: 1px solid transparent;
  height: auto;
}

.emotion-4 .slick-loading .slick-track,
.emotion-4 .slick-loading .slick-slide {
  visibility: hidden;
}

.emotion-4 .slick-loading .slick-list {
  background: #fff url("./ajax-loader.gif") center center no-repeat;
}

.emotion-4 .slick-arrow.slick-hidden {
  display: none;
}

.emotion-4 .slick-prev,
.emotion-4 .slick-next {
  font-size: 0;
  line-height: 0;
  position: absolute;
  top: 50%;
  display: block;
  padding: 0;
  cursor: pointer;
  color: transparent;
  border: none;
  outline: none;
  background: transparent;
}

.emotion-4 .slick-prev:hover,
.emotion-4 .slick-next:hover,
.emotion-4 .slick-prev:focus,
.emotion-4 .slick-next:focus {
  color: transparent;
  outline: none;
  background: transparent;
  opacity: 1;
}

.emotion-4 .slick-prev.slick-disabled,
.emotion-4 .slick-next.slick-disabled {
  opacity: 0.25;
}

.emotion-4 .slick-prev {
  left: -0;
  -webkit-transform: translate(0, -50%) rotate(90deg);
  -moz-transform: translate(0, -50%) rotate(90deg);
  -ms-transform: translate(0, -50%) rotate(90deg);
  transform: translate(0, -50%) rotate(90deg);
}

.emotion-4 .slick-prev span {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-4 [dir="rtl"] .slick-prev {
  right: -0;
  left: auto;
}

.emotion-4 .slick-next {
  right: -0;
  -webkit-transform: translate(0, -50%) rotate(-90deg);
  -moz-transform: translate(0, -50%) rotate(-90deg);
  -ms-transform: translate(0, -50%) rotate(-90deg);
  transform: translate(0, -50%) rotate(-90deg);
}

.emotion-4 .slick-next span {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-4 [dir="rtl"] .slick-next {
  right: auto;
  left: -0;
}

.emotion-4 .slick-dotted.slick-slider {
  margin-bottom: 30px;
}

.emotion-4 .slick-dots {
  position: absolute;
  bottom: -25px;
  display: block;
  width: 100%;
  padding: 0;
  margin: 0;
  list-style: none;
  text-align: center;
}

.emotion-4 .slick-dots li {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: 20px;
  height: 20px;
  margin: 0 5px;
  padding: 0;
  cursor: pointer;
}

.emotion-4 .slick-dots li.slick-active button:before {
  opacity: 0.75;
  color: black;
}

.emotion-4 .slick-dots li button {
  font-size: 0;
  line-height: 0;
  display: block;
  padding: 5px;
  cursor: pointer;
  color: transparent;
  border: 0;
  outline: none;
  background: transparent;
}

.emotion-4 .slick-dots li button:hover,
.emotion-4 .slick-dots li button:focus {
  outline: none;
}

.emotion-4 .slick-dots li button:hover:before,
.emotion-4 .slick-dots li button:focus:before,
.emotion-4 .slick-dots li button:hover:before,
.emotion-4 .slick-dots li button:focus:before {
  opacity: 1;
}

.emotion-4 .slick-dots li button:before {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 5px;
  height: 5px;
  content: "";
  opacity: 0.25;
  background-color: black;
  border-radius: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-5 {
  display: inline-block;
  height: 2rem;
  width: 2rem;
  min-height: 2rem;
  min-width: 2rem;
}

.emotion-5 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-6 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-7 {
  width: 100%;
  position: relative;
}

.emotion-8 {
  overflow: hidden;
  position: relative;
  box-sizing: border-box;
  aspect-ratio: 375/320;
}

.emotion-9 {
  -webkit-background-size: cover;
  background-size: cover;
  aspect-ratio: 375/320;
  overflow: hidden;
}

.emotion-10 {
  width: 100%;
  aspect-ratio: 375/320;
  object-fit: cover;
}

.emotion-11 {
  width: 100%;
  height: 100%;
  position: absolute;
  left: 0;
  top: 0;
  z-index: 1;
}

.emotion-30 {
  width: 100%;
  height: 100%;
  padding: 30px 16px;
  box-sizing: border-box;
  position: static;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  grid-template-columns: repeat(3, minmax(20%,max-content));
  -webkit-align-content: center;
  -ms-flex-line-pack: center;
  align-content: center;
  grid-template-rows: repeat(3, minmax(0, max-content));
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
}

.emotion-30 .rteWrapperDivs {
  grid-column: 2;
  grid-row: 2;
}

.emotion-30 .ctaWrapperDivs {
  grid-column: 2;
  grid-row: 2;
}

.emotion-31 {
  box-sizing: content-box;
  text-align: right;
}

.emotion-32 {
  text-align: right;
  display: inline-block;
}

.emotion-32 .amp-cms--p {
  padding: 0;
  margin: 0;
  line-height: 0;
}

.emotion-32 .amp-cms--p>span {
  display: inline;
  white-space: break-spaces;
}

.emotion-32 .amp-cms--p a {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  position: relative;
  z-index: 2;
  pointer-events: auto;
}

.emotion-32 .amp-cms--p a:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-32 .amp-cms--legal-copy {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 10px;
  line-height: 1.5;
  letter-spacing: 0;
}

.emotion-32 sup {
  vertical-align: top;
  display: inline-block;
  margin-top: -0.20ex;
}

.emotion-32 sub {
  vertical-align: bottom;
  display: inline-block;
  margin-bottom: -0.20ex;
}

.emotion-32 .amp-cms--f-0 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 11px;
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-32 .amp-cms--f-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 11px;
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-32 .amp-cms--f-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 11px;
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-32 .amp-cms--fn-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 11px;
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-32 .amp-cms--body-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 14px;
  line-height: 1.5;
  letter-spacing: 0.336px;
  font-weight: 500;
}

.emotion-32 .amp-cms--body-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.5;
  letter-spacing: 0.288px;
  font-weight: 500;
}

.emotion-32 .amp-cms--body-3 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.5;
  letter-spacing: 0.288px;
}

.emotion-32 .amp-cms--body-4 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 10px;
  line-height: 1.5;
  letter-spacing: 0;
}

.emotion-32 .amp-cms--body-5 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 10px;
  line-height: 1.5;
  letter-spacing: 0;
}

.emotion-32 .amp-cms--eyebrow-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 23px;
  line-height: 1;
  letter-spacing: 0.92px;
  font-weight: 500;
}

.emotion-32 .amp-cms--eyebrow-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 19px;
  line-height: 1;
  letter-spacing: 0.76px;
  font-weight: 500;
}

.emotion-32 .amp-cms--eyebrow-3 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 14px;
  line-height: 1.0714285714285714;
  letter-spacing: 0.56px;
  font-weight: 500;
}

.emotion-32 .amp-cms--headline-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 68px;
  line-height: 1;
  letter-spacing: 0;
  font-weight: 700;
}

.emotion-32 .amp-cms--headline-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 50px;
  line-height: 1;
  letter-spacing: 0;
  font-weight: 700;
}

.emotion-32 .amp-cms--headline-3 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 43px;
  line-height: 1;
  letter-spacing: 0;
  font-weight: 700;
}

.emotion-32 .amp-cms--headline-4 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 36px;
  line-height: 1;
  letter-spacing: 0.72px;
  font-weight: 700;
}

.emotion-32 .amp-cms--headline-5 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 28px;
  line-height: 1;
  letter-spacing: 1.12px;
  font-weight: 700;
}

.emotion-32 .amp-cms--headline-6 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 24px;
  line-height: 1.0833333333333333;
  letter-spacing: 0.96px;
  font-weight: 700;
}

.emotion-32 .amp-cms--headline-7 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 20px;
  line-height: 1;
  letter-spacing: 0.48px;
  font-weight: 700;
}

.emotion-32 .amp-cms--headlineAlt-1 {
  color: #003764;
  font-size: 64px;
  line-height: 1;
  letter-spacing: -0.3px;
  font-weight: 400;
}

.emotion-32 .amp-cms--headlineAlt-2 {
  color: #003764;
  font-size: 54px;
  line-height: 1;
  letter-spacing: -0.3px;
  font-weight: 400;
}

.emotion-32 .amp-cms--headlineAlt-3 {
  color: #003764;
  font-size: 34px;
  line-height: 1;
  letter-spacing: -0.3px;
  font-weight: 400;
}

.emotion-32 .amp-cms--headlineAlt-4 {
  color: #003764;
  font-size: 24px;
  line-height: 1.4166666666666667;
  letter-spacing: -0.3px;
  font-weight: 400;
}

.emotion-32 .amp-cms--headlineAlt-5 {
  color: #003764;
  font-size: 14px;
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-32 .amp-cms--headlineAlt-6 {
  color: #003764;
  font-size: 14px;
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-32 .amp-cms--headlineAlt-7 {
  color: #003764;
  font-size: 14px;
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-32 .amp-cms--promo-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 60px;
  line-height: 1;
  letter-spacing: 0px;
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-32 .amp-cms--promo-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 40px;
  line-height: 1;
  letter-spacing: 0px;
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-32 .amp-cms--promoAlt-1 {
  color: #003764;
  font-size: 14px;
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 600;
}

.emotion-32 .amp-cms--subhead-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 20px;
  line-height: 1.5;
  letter-spacing: 0;
  font-weight: 500;
}

.emotion-32 .amp-cms--subhead-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 18px;
  line-height: 1.5;
  letter-spacing: 0;
  font-weight: 500;
}

.emotion-32 .amp-cms--subhead-3 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 16px;
  line-height: 1.5;
  letter-spacing: 0;
  font-weight: 500;
}

.emotion-33 {
  display: block;
  width: 100%;
  position: relative;
  z-index: 2;
  padding-top: 18px;
  text-align: start;
}

.emotion-34 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 700;
  letter-spacing: 1.05px;
  min-height: 44px;
  max-height: auto;
  line-height: 1.8571428571428572;
  padding: 0;
  width: auto;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: left;
  border-left: none;
  border-right: none;
  border-radius: 0;
  background-color: transparent;
  border-bottom: 0;
  -webkit-box-pack: left;
  -ms-flex-pack: left;
  -webkit-justify-content: left;
  justify-content: left;
  color: #003764;
  height: auto;
  -webkit-text-decoration: underline;
  text-decoration: underline;
  text-underline-offset: 3.5px;
  text-decoration-thickness: 3px;
  -webkit-align-self: flex-start;
  -ms-flex-item-align: flex-start;
  align-self: flex-start;
  white-space: normal;
}

.emotion-34:focus {
  outline: none;
}

.emotion-34>span {
  padding: 1px 0;
}

.emotion-34:hover,
.emotion-34:focus {
  text-decoration-color: #003764;
}

.emotion-34:hover span,
.emotion-34:focus span {
  color: #FFFFFF;
  background-color: #003764;
}

.emotion-34:active {
  text-decoration-color: #000000;
}

.emotion-34:active>span {
  color: #FFFFFF;
  background-color: #000000;
}

.emotion-35 {
  box-sizing: border-box;
}

<div
    style="font-family: var(--font-family-font1),sans-serif;"
  >
    <div
      class="emotion-0"
    >
      <section
        class="emotion-1"
      >
        <div
          class="emotion-2"
        >
          <nav
            class="emotion-3"
          >
            <div
              class="emotion-4"
            >
              <div
                class="slick-slider slick-initialized"
                dir="ltr"
              >
                <button
                  aria-label="Previous"
                  class="slick-prev slick-arrow slick-prev slick-disabled"
                  data-role="none"
                  disabled=""
                >
                  <span
                    aria-hidden="true"
                    class="emotion-5"
                  >
                    <svg
                      viewBox="0 0 13.29 8.07"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M6.13 7.86a.73.73 0 001.04 0l5.92-5.92a.73.73 0 000-1.03l-.7-.7a.73.73 0 00-1.03 0l-4.71 4.7L1.94.2A.73.73 0 00.9.21l-.69.7a.73.73 0 000 1.03z"
                        fill="#003764"
                      />
                    </svg>
                  </span>
                </button>
                <div
                  class="slick-list"
                >
                  <div
                    class="slick-track"
                    style="opacity: 1; transform: translate3d(0px, 0px, 0px);"
                  >
                    <div
                      aria-hidden="false"
                      class="slick-slide slick-active slick-current"
                      data-index="0"
                      style="outline: none; width: 0px;"
                      tabindex="-1"
                    >
                      <div>
                        <div
                          class="emotion-6"
                        >
                          <div
                            class="emotion-7"
                          >
                            <div
                              class="emotion-8"
                              height="320"
                              width="375"
                            >
                              <div
                                class="emotion-9"
                                data-testid="product-card-image"
                              >
                                <img
                                  alt="Kid Photo"
                                  class="emotion-10"
                                  src="https://1puomodm9vwdc1sabswsbq9607.staging.bigcontent.io/i/oldnavy/G32977_TG_MOB@2x?fmt=webp"
                                />
                              </div>
                            </div>
                            <a
                              class="emotion-11"
                              href="/linkToNewArrivals"
                              tabindex="-1"
                              target="_self"
                              title=""
                            />
                          </div>
                        </div>
                      </div>
                    </div>
                    <div
                      aria-hidden="true"
                      class="slick-slide"
                      data-index="1"
                      style="outline: none; width: 0px;"
                      tabindex="-1"
                    >
                      <div>
                        <div
                          class="emotion-6"
                        >
                          <div
                            class="emotion-7"
                          >
                            <div
                              class="emotion-8"
                              height="320"
                              width="375"
                            >
                              <div
                                class="emotion-9"
                                data-testid="product-card-image"
                              >
                                <img
                                  alt=""
                                  class="emotion-10"
                                  src="https://1puomodm9vwdc1sabswsbq9607.staging.bigcontent.io/i/oldnavy/faded-blue-bg-1000x115?fmt=webp"
                                />
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div
                      aria-hidden="true"
                      class="slick-slide"
                      data-index="2"
                      style="outline: none; width: 0px;"
                      tabindex="-1"
                    >
                      <div>
                        <div
                          class="emotion-6"
                        >
                          <div
                            class="emotion-7"
                          >
                            <div
                              class="emotion-8"
                              height="320"
                              width="375"
                            >
                              <div
                                class="emotion-9"
                                data-testid="product-card-image"
                              >
                                <img
                                  alt=""
                                  class="emotion-10"
                                  src="https://2hmc8flz4ofg1dy89cc38f2xm.staging.bigcontent.io/i/athleta/annie-spratt-h-LcVG8W1XY-unsplash?fmt=webp"
                                />
                              </div>
                            </div>
                            <a
                              class="emotion-11"
                              href="/productsWeLove"
                              tabindex="-1"
                              target="_self"
                              title="Middle link"
                            />
                          </div>
                        </div>
                      </div>
                    </div>
                    <div
                      aria-hidden="true"
                      class="slick-slide"
                      data-index="3"
                      style="outline: none; width: 0px;"
                      tabindex="-1"
                    >
                      <div>
                        <div
                          class="emotion-6"
                        >
                          <div
                            class="emotion-7"
                          >
                            <div
                              class="emotion-8"
                              height="320"
                              width="375"
                            >
                              <div
                                class="emotion-9"
                                data-testid="product-card-image"
                              >
                                <img
                                  alt=""
                                  class="emotion-10"
                                  src="https://2hmc8flz4ofg1dy89cc38f2xm.staging.bigcontent.io/i/athleta/annie-spratt-h-LcVG8W1XY-unsplash?fmt=webp"
                                />
                              </div>
                            </div>
                            <a
                              class="emotion-11"
                              href="/lastLink"
                              tabindex="-1"
                              target="_self"
                              title="lastLink"
                            />
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <button
                  aria-label="Next"
                  class="slick-next slick-arrow slick-next"
                  data-role="none"
                >
                  <span
                    aria-hidden="true"
                    class="emotion-5"
                  >
                    <svg
                      viewBox="0 0 13.29 8.07"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M6.13 7.86a.73.73 0 001.04 0l5.92-5.92a.73.73 0 000-1.03l-.7-.7a.73.73 0 00-1.03 0l-4.71 4.7L1.94.2A.73.73 0 00.9.21l-.69.7a.73.73 0 000 1.03z"
                        fill="#003764"
                      />
                    </svg>
                  </span>
                </button>
                <ul
                  class="slick-dots"
                  style="display: block;"
                >
                  <li
                    class="slick-active"
                  >
                    <button>
                      1
                    </button>
                  </li>
                  <li
                    class=""
                  >
                    <button>
                      2
                    </button>
                  </li>
                  <li
                    class=""
                  >
                    <button>
                      3
                    </button>
                  </li>
                  <li
                    class=""
                  >
                    <button>
                      4
                    </button>
                  </li>
                </ul>
              </div>
            </div>
          </nav>
          <div>
            <div
              class="emotion-30"
            >
              <div
                class="rteWrapperDivs emotion-31"
              >
                <div
                  class="emotion-32"
                >
                  <div>
                    <p
                      class="amp-cms--p"
                    >
                      <span
                        class="amp-cms--subhead-1"
                      >
                        Lorem Ipsum
                      </span>
                    </p>
                    <p
                      class="amp-cms--p"
                    >
                      <span
                        class="amp-cms--body-1"
                      >
                        dolor sit amet, consectetur adipiscing elit.
                      </span>
                    </p>
                  </div>
                </div>
              </div>
              <div
                class="ctaWrapperDivs emotion-33"
              >
                <a
                  class="emotion-34"
                  color="dark"
                  href="/buyIt"
                >
                  <span
                    class="emotion-35"
                  >
                    new arrivals
                  </span>
                </a>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  </div>
</DocumentFragment>
`;

exports[`on mobile should render isPersistentContent as true 1`] = `
.emotion-0 {
  margin-left: auto;
  margin-right: auto;
  max-width: 1280px;
}

.emotion-2 {
  position: relative;
}

.emotion-3 {
  height: 100%;
  width: 100%;
  position: absolute;
  top: 0;
  left: 0;
}

.emotion-4 {
  width: 100%;
  height: 100%;
  padding: 30px 16px;
  box-sizing: border-box;
  position: static;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  grid-template-columns: repeat(3, minmax(20%,max-content));
  -webkit-align-content: center;
  -ms-flex-line-pack: center;
  align-content: center;
  grid-template-rows: repeat(3, minmax(0, max-content));
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
}

.emotion-4 .rteWrapperDivs {
  grid-column: 2;
  grid-row: 2;
}

.emotion-4 .ctaWrapperDivs {
  grid-column: 2;
  grid-row: 2;
}

.emotion-5 {
  box-sizing: content-box;
  text-align: right;
}

.emotion-6 {
  text-align: right;
  display: inline-block;
}

.emotion-6 .amp-cms--p {
  padding: 0;
  margin: 0;
  line-height: 0;
}

.emotion-6 .amp-cms--p>span {
  display: inline;
  white-space: break-spaces;
}

.emotion-6 .amp-cms--p a {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  position: relative;
  z-index: 2;
  pointer-events: auto;
}

.emotion-6 .amp-cms--p a:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-6 .amp-cms--legal-copy {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 10px;
  line-height: 1.5;
  letter-spacing: 0;
}

.emotion-6 sup {
  vertical-align: top;
  display: inline-block;
  margin-top: -0.20ex;
}

.emotion-6 sub {
  vertical-align: bottom;
  display: inline-block;
  margin-bottom: -0.20ex;
}

.emotion-6 .amp-cms--f-0 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 11px;
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-6 .amp-cms--f-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 11px;
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-6 .amp-cms--f-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 11px;
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-6 .amp-cms--fn-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 11px;
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-6 .amp-cms--body-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 14px;
  line-height: 1.5;
  letter-spacing: 0.336px;
  font-weight: 500;
}

.emotion-6 .amp-cms--body-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.5;
  letter-spacing: 0.288px;
  font-weight: 500;
}

.emotion-6 .amp-cms--body-3 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.5;
  letter-spacing: 0.288px;
}

.emotion-6 .amp-cms--body-4 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 10px;
  line-height: 1.5;
  letter-spacing: 0;
}

.emotion-6 .amp-cms--body-5 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 10px;
  line-height: 1.5;
  letter-spacing: 0;
}

.emotion-6 .amp-cms--eyebrow-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 23px;
  line-height: 1;
  letter-spacing: 0.92px;
  font-weight: 500;
}

.emotion-6 .amp-cms--eyebrow-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 19px;
  line-height: 1;
  letter-spacing: 0.76px;
  font-weight: 500;
}

.emotion-6 .amp-cms--eyebrow-3 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 14px;
  line-height: 1.0714285714285714;
  letter-spacing: 0.56px;
  font-weight: 500;
}

.emotion-6 .amp-cms--headline-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 68px;
  line-height: 1;
  letter-spacing: 0;
  font-weight: 700;
}

.emotion-6 .amp-cms--headline-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 50px;
  line-height: 1;
  letter-spacing: 0;
  font-weight: 700;
}

.emotion-6 .amp-cms--headline-3 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 43px;
  line-height: 1;
  letter-spacing: 0;
  font-weight: 700;
}

.emotion-6 .amp-cms--headline-4 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 36px;
  line-height: 1;
  letter-spacing: 0.72px;
  font-weight: 700;
}

.emotion-6 .amp-cms--headline-5 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 28px;
  line-height: 1;
  letter-spacing: 1.12px;
  font-weight: 700;
}

.emotion-6 .amp-cms--headline-6 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 24px;
  line-height: 1.0833333333333333;
  letter-spacing: 0.96px;
  font-weight: 700;
}

.emotion-6 .amp-cms--headline-7 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 20px;
  line-height: 1;
  letter-spacing: 0.48px;
  font-weight: 700;
}

.emotion-6 .amp-cms--headlineAlt-1 {
  color: #003764;
  font-size: 64px;
  line-height: 1;
  letter-spacing: -0.3px;
  font-weight: 400;
}

.emotion-6 .amp-cms--headlineAlt-2 {
  color: #003764;
  font-size: 54px;
  line-height: 1;
  letter-spacing: -0.3px;
  font-weight: 400;
}

.emotion-6 .amp-cms--headlineAlt-3 {
  color: #003764;
  font-size: 34px;
  line-height: 1;
  letter-spacing: -0.3px;
  font-weight: 400;
}

.emotion-6 .amp-cms--headlineAlt-4 {
  color: #003764;
  font-size: 24px;
  line-height: 1.4166666666666667;
  letter-spacing: -0.3px;
  font-weight: 400;
}

.emotion-6 .amp-cms--headlineAlt-5 {
  color: #003764;
  font-size: 14px;
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-6 .amp-cms--headlineAlt-6 {
  color: #003764;
  font-size: 14px;
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-6 .amp-cms--headlineAlt-7 {
  color: #003764;
  font-size: 14px;
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-6 .amp-cms--promo-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 60px;
  line-height: 1;
  letter-spacing: 0px;
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-6 .amp-cms--promo-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 40px;
  line-height: 1;
  letter-spacing: 0px;
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-6 .amp-cms--promoAlt-1 {
  color: #003764;
  font-size: 14px;
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 600;
}

.emotion-6 .amp-cms--subhead-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 20px;
  line-height: 1.5;
  letter-spacing: 0;
  font-weight: 500;
}

.emotion-6 .amp-cms--subhead-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 18px;
  line-height: 1.5;
  letter-spacing: 0;
  font-weight: 500;
}

.emotion-6 .amp-cms--subhead-3 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 16px;
  line-height: 1.5;
  letter-spacing: 0;
  font-weight: 500;
}

.emotion-7 {
  display: block;
  width: 100%;
  position: relative;
  z-index: 2;
  padding-top: 18px;
  text-align: start;
}

.emotion-8 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 700;
  letter-spacing: 1.05px;
  min-height: 44px;
  max-height: auto;
  line-height: 1.8571428571428572;
  padding: 0;
  width: auto;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: left;
  border-left: none;
  border-right: none;
  border-radius: 0;
  background-color: transparent;
  border-bottom: 0;
  -webkit-box-pack: left;
  -ms-flex-pack: left;
  -webkit-justify-content: left;
  justify-content: left;
  color: #003764;
  height: auto;
  -webkit-text-decoration: underline;
  text-decoration: underline;
  text-underline-offset: 3.5px;
  text-decoration-thickness: 3px;
  -webkit-align-self: flex-start;
  -ms-flex-item-align: flex-start;
  align-self: flex-start;
  white-space: normal;
}

.emotion-8:focus {
  outline: none;
}

.emotion-8>span {
  padding: 1px 0;
}

.emotion-8:hover,
.emotion-8:focus {
  text-decoration-color: #003764;
}

.emotion-8:hover span,
.emotion-8:focus span {
  color: #FFFFFF;
  background-color: #003764;
}

.emotion-8:active {
  text-decoration-color: #000000;
}

.emotion-8:active>span {
  color: #FFFFFF;
  background-color: #000000;
}

.emotion-9 {
  box-sizing: border-box;
}

.emotion-10 {
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-10 .slick-list {
  overflow: hidden;
}

.emotion-10 .slick-track {
  width: -webkit-max-content!important;
  width: -moz-max-content!important;
  width: max-content!important;
}

.emotion-10 button.slick-next.slick-arrow.slick-next,
.emotion-10 button.slick-prev.slick-arrow.slick-prev {
  z-index: 2;
  top: min(42.66666666666667vw, 50%);
  height: 44px;
  width: 44px;
  background: #003764;
  opacity: 75%;
}

.emotion-10 button.slick-next.slick-arrow.slick-next>span,
.emotion-10 button.slick-prev.slick-arrow.slick-prev>span {
  margin: auto;
}

.emotion-10 button.slick-next.slick-arrow.slick-next svg,
.emotion-10 button.slick-prev.slick-arrow.slick-prev svg {
  margin: auto;
  height: 25px;
  width: 14px;
}

.emotion-10 button.slick-next.slick-arrow.slick-next span svg path,
.emotion-10 button.slick-prev.slick-arrow.slick-prev span svg path {
  fill: #ffffff;
}

.emotion-10 button.slick-next.slick-arrow.slick-next:hover,
.emotion-10 button.slick-prev.slick-arrow.slick-prev:hover {
  opacity: 100%;
}

.emotion-10 button.slick-next.slick-arrow.slick-next:focus,
.emotion-10 button.slick-prev.slick-arrow.slick-prev:focus {
  background: #003764;
}

.emotion-10 button.slick-next.slick-arrow.slick-next:focus-visible,
.emotion-10 button.slick-prev.slick-arrow.slick-prev:focus-visible {
  outline: auto;
}

.emotion-10 .slick-slide {
  margin: 0;
  width: auto;
}

.emotion-10 .slick-disabled {
  display: none!important;
}

.emotion-10 .slick-next {
  left: calc(100% - 44px);
}

.emotion-10 .slick-slide:first-of-type a>div:nth-of-type(2)>div>div:nth-of-type(2) {
  border-left: none;
}

.emotion-10 .slick-slide>div:first-of-type {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-10 .slick-slide>div:first-of-type>div:first-of-type {
  width: 100%;
}

.emotion-10 .slick-slider ul.slick-dots {
  bottom: 4px;
  width: unset;
  height: 44px;
  margin-left: 50%;
  -webkit-transform: translateX(-50%);
  -moz-transform: translateX(-50%);
  -ms-transform: translateX(-50%);
  transform: translateX(-50%);
  -webkit-align-content: center;
  -ms-flex-line-pack: center;
  align-content: center;
}

.emotion-10 .slick-slider .slick-dots li.slick-active button:before {
  background-color: #003764;
}

.emotion-10 .slick-slider ul.slick-dots button:before {
  width: 10px;
  height: 10px;
  opacity: 1;
  background-color: #B2B2B2;
}

.emotion-11 {
  position: relative;
}

.emotion-11 .slick-slider {
  position: relative;
  display: block;
  box-sizing: border-box;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  -webkit-touch-callout: none;
  touch-action: pan-y;
  -webkit-tap-highlight-color: transparent;
  scroll-behavior: smooth;
}

.emotion-11 .slick-slider .slick-track,
.emotion-11 .slick-slider .slick-list {
  -webkit-transform: translate3d(0, 0, 0);
  -moz-transform: translate3d(0, 0, 0);
  -ms-transform: translate3d(0, 0, 0);
  transform: translate3d(0, 0, 0);
}

.emotion-11 .slick-list {
  position: relative;
  display: block;
  overflow-x: hidden;
  overflow-y: hidden;
  margin: 0;
  padding: 0;
}

.emotion-11 .slick-list:focus {
  outline: none;
}

.emotion-11 .slick-list.dragging {
  cursor: pointer;
  cursor: hand;
}

.emotion-11 .slick-track {
  position: relative;
  top: 0;
  left: 0;
  display: block;
  margin-left: auto;
  margin-right: auto;
}

.emotion-11 .slick-track:before,
.emotion-11 .slick-track:after {
  display: table;
  content: "";
}

.emotion-11 .slick-track:after {
  clear: both;
}

.emotion-11 .slick-slide {
  display: none;
  float: left;
  height: 100%;
  min-height: 1px;
}

.emotion-11 .slick-slide img {
  display: block;
}

.emotion-11 .slick-slide.slick-loading img {
  display: none;
}

.emotion-11 .slick-slide.dragging img {
  pointer-events: none;
}

.emotion-11 [dir="rtl"] .slick-slide {
  float: right;
}

.emotion-11 .slick-initialized .slick-slide,
.emotion-11 .slick-vertical .slick-slide {
  display: block;
}

.emotion-11 .slick-vertical .slick-slide {
  border: 1px solid transparent;
  height: auto;
}

.emotion-11 .slick-loading .slick-track,
.emotion-11 .slick-loading .slick-slide {
  visibility: hidden;
}

.emotion-11 .slick-loading .slick-list {
  background: #fff url("./ajax-loader.gif") center center no-repeat;
}

.emotion-11 .slick-arrow.slick-hidden {
  display: none;
}

.emotion-11 .slick-prev,
.emotion-11 .slick-next {
  font-size: 0;
  line-height: 0;
  position: absolute;
  top: 50%;
  display: block;
  padding: 0;
  cursor: pointer;
  color: transparent;
  border: none;
  outline: none;
  background: transparent;
}

.emotion-11 .slick-prev:hover,
.emotion-11 .slick-next:hover,
.emotion-11 .slick-prev:focus,
.emotion-11 .slick-next:focus {
  color: transparent;
  outline: none;
  background: transparent;
  opacity: 1;
}

.emotion-11 .slick-prev.slick-disabled,
.emotion-11 .slick-next.slick-disabled {
  opacity: 0.25;
}

.emotion-11 .slick-prev {
  left: -0;
  -webkit-transform: translate(0, -50%) rotate(90deg);
  -moz-transform: translate(0, -50%) rotate(90deg);
  -ms-transform: translate(0, -50%) rotate(90deg);
  transform: translate(0, -50%) rotate(90deg);
}

.emotion-11 .slick-prev span {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-11 [dir="rtl"] .slick-prev {
  right: -0;
  left: auto;
}

.emotion-11 .slick-next {
  right: -0;
  -webkit-transform: translate(0, -50%) rotate(-90deg);
  -moz-transform: translate(0, -50%) rotate(-90deg);
  -ms-transform: translate(0, -50%) rotate(-90deg);
  transform: translate(0, -50%) rotate(-90deg);
}

.emotion-11 .slick-next span {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-11 [dir="rtl"] .slick-next {
  right: auto;
  left: -0;
}

.emotion-11 .slick-dotted.slick-slider {
  margin-bottom: 30px;
}

.emotion-11 .slick-dots {
  position: absolute;
  bottom: -25px;
  display: block;
  width: 100%;
  padding: 0;
  margin: 0;
  list-style: none;
  text-align: center;
}

.emotion-11 .slick-dots li {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: 20px;
  height: 20px;
  margin: 0 5px;
  padding: 0;
  cursor: pointer;
}

.emotion-11 .slick-dots li.slick-active button:before {
  opacity: 0.75;
  color: black;
}

.emotion-11 .slick-dots li button {
  font-size: 0;
  line-height: 0;
  display: block;
  padding: 5px;
  cursor: pointer;
  color: transparent;
  border: 0;
  outline: none;
  background: transparent;
}

.emotion-11 .slick-dots li button:hover,
.emotion-11 .slick-dots li button:focus {
  outline: none;
}

.emotion-11 .slick-dots li button:hover:before,
.emotion-11 .slick-dots li button:focus:before,
.emotion-11 .slick-dots li button:hover:before,
.emotion-11 .slick-dots li button:focus:before {
  opacity: 1;
}

.emotion-11 .slick-dots li button:before {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 5px;
  height: 5px;
  content: "";
  opacity: 0.25;
  background-color: black;
  border-radius: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-12 {
  display: inline-block;
  height: 2rem;
  width: 2rem;
  min-height: 2rem;
  min-width: 2rem;
}

.emotion-12 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-13 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-14 {
  width: 100%;
  position: relative;
}

.emotion-15 {
  overflow: hidden;
  position: relative;
  box-sizing: border-box;
  aspect-ratio: 375/320;
}

.emotion-16 {
  -webkit-background-size: cover;
  background-size: cover;
  aspect-ratio: 375/320;
  overflow: hidden;
}

.emotion-17 {
  width: 100%;
  aspect-ratio: 375/320;
  object-fit: cover;
}

.emotion-18 {
  width: 100%;
  height: 100%;
  position: absolute;
  left: 0;
  top: 0;
  z-index: 1;
}

<div>
  <div
    style="font-family: var(--font-family-font1),sans-serif;"
  >
    <div
      class="emotion-0"
    >
      <section
        class="emotion-1"
      >
        <div
          class="emotion-2"
        >
          <div>
            <div
              class="emotion-3"
            >
              <div
                class="emotion-4"
              >
                <div
                  class="rteWrapperDivs emotion-5"
                >
                  <div
                    class="emotion-6"
                  >
                    <div>
                      <p
                        class="amp-cms--p"
                      >
                        <span
                          class="amp-cms--subhead-1"
                        >
                          Lorem Ipsum
                        </span>
                      </p>
                      <p
                        class="amp-cms--p"
                      >
                        <span
                          class="amp-cms--body-1"
                        >
                          dolor sit amet, consectetur adipiscing elit.
                        </span>
                      </p>
                    </div>
                  </div>
                </div>
                <div
                  class="ctaWrapperDivs emotion-7"
                >
                  <a
                    class="emotion-8"
                    color="dark"
                    href="/buyIt"
                  >
                    <span
                      class="emotion-9"
                    >
                      new arrivals
                    </span>
                  </a>
                </div>
              </div>
            </div>
          </div>
          <nav
            class="emotion-10"
          >
            <div
              class="emotion-11"
            >
              <div
                class="slick-slider slick-initialized"
                dir="ltr"
              >
                <button
                  aria-label="Previous"
                  class="slick-prev slick-arrow slick-prev slick-disabled"
                  data-role="none"
                  disabled=""
                >
                  <span
                    aria-hidden="true"
                    class="emotion-12"
                  >
                    <svg
                      viewBox="0 0 13.29 8.07"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M6.13 7.86a.73.73 0 001.04 0l5.92-5.92a.73.73 0 000-1.03l-.7-.7a.73.73 0 00-1.03 0l-4.71 4.7L1.94.2A.73.73 0 00.9.21l-.69.7a.73.73 0 000 1.03z"
                        fill="#003764"
                      />
                    </svg>
                  </span>
                </button>
                <div
                  class="slick-list"
                >
                  <div
                    class="slick-track"
                    style="opacity: 1; transform: translate3d(0px, 0px, 0px);"
                  >
                    <div
                      aria-hidden="false"
                      class="slick-slide slick-active slick-current"
                      data-index="0"
                      style="outline: none; width: 0px;"
                      tabindex="-1"
                    >
                      <div>
                        <div
                          class="emotion-13"
                        >
                          <div
                            class="emotion-14"
                          >
                            <div
                              class="emotion-15"
                              height="320"
                              width="375"
                            >
                              <div
                                class="emotion-16"
                                data-testid="product-card-image"
                              >
                                <img
                                  alt="Kid Photo"
                                  class="emotion-17"
                                  src="https://1puomodm9vwdc1sabswsbq9607.staging.bigcontent.io/i/oldnavy/G32977_TG_MOB@2x?fmt=webp"
                                />
                              </div>
                            </div>
                            <a
                              class="emotion-18"
                              href="/linkToNewArrivals"
                              tabindex="-1"
                              target="_self"
                              title=""
                            />
                          </div>
                        </div>
                      </div>
                    </div>
                    <div
                      aria-hidden="true"
                      class="slick-slide"
                      data-index="1"
                      style="outline: none; width: 0px;"
                      tabindex="-1"
                    >
                      <div>
                        <div
                          class="emotion-13"
                        >
                          <div
                            class="emotion-14"
                          >
                            <div
                              class="emotion-15"
                              height="320"
                              width="375"
                            >
                              <div
                                class="emotion-16"
                                data-testid="product-card-image"
                              >
                                <img
                                  alt=""
                                  class="emotion-17"
                                  src="https://1puomodm9vwdc1sabswsbq9607.staging.bigcontent.io/i/oldnavy/faded-blue-bg-1000x115?fmt=webp"
                                />
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div
                      aria-hidden="true"
                      class="slick-slide"
                      data-index="2"
                      style="outline: none; width: 0px;"
                      tabindex="-1"
                    >
                      <div>
                        <div
                          class="emotion-13"
                        >
                          <div
                            class="emotion-14"
                          >
                            <div
                              class="emotion-15"
                              height="320"
                              width="375"
                            >
                              <div
                                class="emotion-16"
                                data-testid="product-card-image"
                              >
                                <img
                                  alt=""
                                  class="emotion-17"
                                  src="https://2hmc8flz4ofg1dy89cc38f2xm.staging.bigcontent.io/i/athleta/annie-spratt-h-LcVG8W1XY-unsplash?fmt=webp"
                                />
                              </div>
                            </div>
                            <a
                              class="emotion-18"
                              href="/productsWeLove"
                              tabindex="-1"
                              target="_self"
                              title="Middle link"
                            />
                          </div>
                        </div>
                      </div>
                    </div>
                    <div
                      aria-hidden="true"
                      class="slick-slide"
                      data-index="3"
                      style="outline: none; width: 0px;"
                      tabindex="-1"
                    >
                      <div>
                        <div
                          class="emotion-13"
                        >
                          <div
                            class="emotion-14"
                          >
                            <div
                              class="emotion-15"
                              height="320"
                              width="375"
                            >
                              <div
                                class="emotion-16"
                                data-testid="product-card-image"
                              >
                                <img
                                  alt=""
                                  class="emotion-17"
                                  src="https://2hmc8flz4ofg1dy89cc38f2xm.staging.bigcontent.io/i/athleta/annie-spratt-h-LcVG8W1XY-unsplash?fmt=webp"
                                />
                              </div>
                            </div>
                            <a
                              class="emotion-18"
                              href="/lastLink"
                              tabindex="-1"
                              target="_self"
                              title="lastLink"
                            />
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <button
                  aria-label="Next"
                  class="slick-next slick-arrow slick-next"
                  data-role="none"
                >
                  <span
                    aria-hidden="true"
                    class="emotion-12"
                  >
                    <svg
                      viewBox="0 0 13.29 8.07"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M6.13 7.86a.73.73 0 001.04 0l5.92-5.92a.73.73 0 000-1.03l-.7-.7a.73.73 0 00-1.03 0l-4.71 4.7L1.94.2A.73.73 0 00.9.21l-.69.7a.73.73 0 000 1.03z"
                        fill="#003764"
                      />
                    </svg>
                  </span>
                </button>
                <ul
                  class="slick-dots"
                  style="display: block;"
                >
                  <li
                    class="slick-active"
                  >
                    <button>
                      1
                    </button>
                  </li>
                  <li
                    class=""
                  >
                    <button>
                      2
                    </button>
                  </li>
                  <li
                    class=""
                  >
                    <button>
                      3
                    </button>
                  </li>
                  <li
                    class=""
                  >
                    <button>
                      4
                    </button>
                  </li>
                </ul>
              </div>
            </div>
          </nav>
        </div>
      </section>
    </div>
  </div>
</div>
`;
