import React from 'react';
import { screen, render, cleanup, fireEvent } from 'test-utils';
// @ts-ignore
import { Si<PERSON>, SMALL, XLARGE } from '@ecom-next/core/breakpoint-provider';
// @ts-ignore
import { Brands } from '@ecom-next/core/react-stitch';
import { LocalizationTestWrapper } from '../../../../subcomponents/LocalizationTestWrapper/index';
import { cbvhCarouselBaseDataContentData as defaultProps, withSecondaryColorData } from '../../__fixtures__/test-data';
import { CategoryBannerVHCarouselProps } from '../../../../components/CategoryBannerVHCarousel/index';
import { CarouselSettings } from '../../../../subcomponents/CMSMarketingCarousel/types';
import CategoryBannerVHCarousel from './CategoryBannerVariableHeightCarousel.on';

const getParentContainer = (container: HTMLElement): HTMLElement | null => (container?.firstChild.firstChild as HTMLElement) || null;

interface RenderComponentProps {
  brand?: Brands;
  breakpoint?: Size;
  enabledFeatures?: Record<string, boolean | object>;
  props?: Partial<CategoryBannerVHCarouselProps | CarouselSettings['styling']>;
}

const renderComponent = (
  props: RenderComponentProps['props'] = {},
  brand: RenderComponentProps['brand'] = Brands.OldNavy,
  breakpoint: RenderComponentProps['breakpoint'] = XLARGE,
  enabledFeatures?: RenderComponentProps['enabledFeatures']
) => {
  const result = render(
    <LocalizationTestWrapper>
      <CategoryBannerVHCarousel {...defaultProps} {...props} />
    </LocalizationTestWrapper>,
    {
      // @ts-ignore
      enabledFeatures,
      breakpoint,
      appState: {
        brandName: brand,
      } as any,
    }
  );

  return result;
};

describe('AbsoluteWrapper', () => {
  it('should render tile content on image', () => {
    const { getAllByTestId } = renderComponent({ mobileTextTreatment: 'on' }, Brands.OldNavy, SMALL);
    const absoluteWrapper = getAllByTestId('absolute-wrapper')[0];
    expect(absoluteWrapper).toHaveStyleRules({
      position: 'absolute',
      height: '100%',
    });
  });

  it('should render tile content below image', () => {
    const { container } = renderComponent(
      {
        mobileTextTreatment: 'below',
        frames: [
          {
            tiles: [
              {
                backgroundImage: [
                  {
                    image: {
                      _meta: {
                        schema: 'http://bigcontent.io/cms/schema/v1/core#/definitions/image-link',
                      },
                      id: '706688cf-2808-4d97-b1b0-e8ad10b2ca3b',
                      name: 'woman-black_hat',
                      endpoint: 'athleta',
                      defaultHost: '2hmc8flz4ofg1dy89cc38f2xm.staging.bigcontent.io',
                    },
                    altText: 'hello lady',
                    variations: [
                      {
                        variation: 'desktop',
                      },
                      {
                        variation: 'mobile',
                      },
                    ],
                    fliph: false,
                    flipv: false,
                  },
                ],
                mobileBackgroundImage: [
                  {
                    image: {
                      _meta: {
                        schema: 'http://bigcontent.io/cms/schema/v1/core#/definitions/image-link',
                      },
                      id: '70e545d1-1155-45d4-b2ce-5705f535c88a',
                      name: 'faded-blue-bg-1000x115',
                      endpoint: 'oldnavy',
                      defaultHost: '1puomodm9vwdc1sabswsbq9607.staging.bigcontent.io',
                    },
                    altText: 'colorful leggings',
                    variations: [
                      {
                        variation: 'desktop',
                      },
                      {
                        variation: 'mobile',
                      },
                    ],
                    fliph: false,
                    flipv: false,
                  },
                ],
                webAppearance: {
                  desktop: {
                    verticalPlacement: 'end',
                    horizontalPlacement: 'end',
                    ctaVerticalPlacement: 'end',
                    ctaHorizontalPlacement: 'left',
                    textJustification: 'end',
                    ctaJustification: 'end',
                    ctaButtonStyling: {
                      buttonStyle: 'border',
                      buttonColor: 'dark',
                    },
                  },
                  mobile: {
                    textJustification: 'center',
                    ctaPlacement: 'center',
                  },
                },
                text: '<h2 class="amp-cms--p" style="text-align:left;"><span class="amp-cms--body-1">OUT MOST LOVED STYLES</span></h2>',
                ctaButton: {
                  label: 'new arrivals',
                  value: 'buyIt',
                },
              },
              {
                backgroundImage: [
                  {
                    image: {
                      _meta: {
                        schema: 'http://bigcontent.io/cms/schema/v1/core#/definitions/image-link',
                      },
                      id: 'e7e06d55-cc72-4e5e-b8fd-9e38c2e37a49',
                      name: '530895_012_VIPW_AT_WMN_LS_150_SP20_SW_3_0882',
                      endpoint: 'athleta',
                      defaultHost: '2hmc8flz4ofg1dy89cc38f2xm.staging.bigcontent.io',
                    },
                    altText: 'hello lady',
                    variations: [
                      {
                        variation: 'desktop',
                      },
                      {
                        variation: 'mobile',
                      },
                    ],
                    fliph: false,
                    flipv: false,
                  },
                ],
                mobileBackgroundImage: [
                  {
                    image: {
                      _meta: {
                        schema: 'http://bigcontent.io/cms/schema/v1/core#/definitions/image-link',
                      },
                      id: 'c70f2644-f8f4-4c22-96bd-a50b2cc39fa4',
                      name: 'G32977_TG_MOB@2x',
                      endpoint: 'oldnavy',
                      defaultHost: '1puomodm9vwdc1sabswsbq9607.staging.bigcontent.io',
                    },
                    altText: 'Kid Photo',
                    variations: [
                      {
                        variation: 'desktop',
                      },
                      {
                        variation: 'mobile',
                      },
                    ],
                    fliph: false,
                    flipv: false,
                  },
                ],
                webAppearance: {
                  desktop: {
                    verticalPlacement: 'end',
                    horizontalPlacement: 'center',
                    ctaVerticalPlacement: 'start',
                    ctaHorizontalPlacement: 'right',
                    textJustification: 'start',
                    ctaJustification: 'center',
                    ctaButtonStyling: {
                      buttonStyle: 'underline',
                      buttonColor: 'dark',
                    },
                  },
                  mobile: {
                    textJustification: 'right',
                    ctaPlacement: 'start',
                  },
                },
                ctaButton: {
                  label: 'new arrivals',
                  value: 'buyIt',
                },
                bannerLink: {
                  label: 'link to somewhere',
                  value: 'linkToNewArrivals',
                },
                text: '<p class="amp-cms--p" style="text-align:center;"><span class="amp-cms--subhead-1" style="color:#00FF00;font-weight:800">Body font 1</span></p><p class="amp-cms--p" style="text-align:center;"><span class="amp-cms--subhead-2" style="color:#10a6a6;font-style:italic">GOOD EARTH</span></p>',
              },
            ],
          },
          {
            tiles: [
              {
                backgroundImage: [
                  {
                    image: {
                      _meta: {
                        schema: 'http://bigcontent.io/cms/schema/v1/core#/definitions/image-link',
                      },
                      id: '706688cf-2808-4d97-b1b0-e8ad10b2ca3b',
                      name: 'woman-black_hat',
                      endpoint: 'athleta',
                      defaultHost: '2hmc8flz4ofg1dy89cc38f2xm.staging.bigcontent.io',
                    },
                    altText: 'hello lady',
                    variations: [
                      {
                        variation: 'desktop',
                      },
                      {
                        variation: 'mobile',
                      },
                    ],
                    fliph: false,
                    flipv: false,
                  },
                ],
                mobileBackgroundImage: [
                  {
                    image: {
                      _meta: {
                        schema: 'http://bigcontent.io/cms/schema/v1/core#/definitions/image-link',
                      },
                      id: '70e545d1-1155-45d4-b2ce-5705f535c88a',
                      name: 'faded-blue-bg-1000x115',
                      endpoint: 'oldnavy',
                      defaultHost: '1puomodm9vwdc1sabswsbq9607.staging.bigcontent.io',
                    },
                    altText: '',
                    variations: [
                      {
                        variation: 'desktop',
                      },
                      {
                        variation: 'mobile',
                      },
                    ],
                    fliph: false,
                    flipv: false,
                  },
                ],
                webAppearance: {
                  desktop: {
                    verticalPlacement: 'end',
                    horizontalPlacement: 'end',
                    ctaVerticalPlacement: 'end',
                    ctaHorizontalPlacement: 'left',
                    textJustification: 'end',
                    ctaJustification: 'end',
                    ctaButtonStyling: {
                      buttonStyle: 'border',
                      buttonColor: 'dark',
                    },
                  },
                  mobile: {
                    textJustification: 'center',
                    ctaPlacement: 'center',
                  },
                },
                text: '<h2 class="amp-cms--p" style="text-align:left;"><span class="amp-cms--body-1">OUT MOST LOVED STYLES</span></h2>',
                ctaButton: {
                  label: 'new arrivals',
                  value: 'buyIt',
                },
              },
              {
                backgroundImage: [
                  {
                    image: {
                      _meta: {
                        schema: 'http://bigcontent.io/cms/schema/v1/core#/definitions/image-link',
                      },
                      id: 'e7e06d55-cc72-4e5e-b8fd-9e38c2e37a49',
                      name: '530895_012_VIPW_AT_WMN_LS_150_SP20_SW_3_0882',
                      endpoint: 'athleta',
                      defaultHost: '2hmc8flz4ofg1dy89cc38f2xm.staging.bigcontent.io',
                    },
                    altText: 'hello lady',
                    variations: [
                      {
                        variation: 'desktop',
                      },
                      {
                        variation: 'mobile',
                      },
                    ],
                    fliph: false,
                    flipv: false,
                  },
                ],
                mobileBackgroundImage: [
                  {
                    image: {
                      _meta: {
                        schema: 'http://bigcontent.io/cms/schema/v1/core#/definitions/image-link',
                      },
                      id: 'c70f2644-f8f4-4c22-96bd-a50b2cc39fa4',
                      name: 'G32977_TG_MOB@2x',
                      endpoint: 'oldnavy',
                      defaultHost: '1puomodm9vwdc1sabswsbq9607.staging.bigcontent.io',
                    },
                    altText: 'Kid Photo',
                    variations: [
                      {
                        variation: 'desktop',
                      },
                      {
                        variation: 'mobile',
                      },
                    ],
                    fliph: false,
                    flipv: false,
                  },
                ],
                webAppearance: {
                  desktop: {
                    verticalPlacement: 'end',
                    horizontalPlacement: 'center',
                    ctaVerticalPlacement: 'start',
                    ctaHorizontalPlacement: 'right',
                    textJustification: 'start',
                    ctaJustification: 'center',
                    ctaButtonStyling: {
                      buttonStyle: 'underline',
                      buttonColor: 'dark',
                    },
                  },
                  mobile: {
                    textJustification: 'right',
                    ctaPlacement: 'start',
                  },
                },
                ctaButton: {
                  label: 'new arrivals',
                  value: 'buyIt',
                },
                bannerLink: {
                  label: 'link to somewhere',
                  value: 'linkToNewArrivals',
                },
                text: '<p class="amp-cms--p" style="text-align:center;"><span class="amp-cms--subhead-1" style="color:#00FF00;font-weight:800">Body font 1</span></p><p class="amp-cms--p" style="text-align:center;"><span class="amp-cms--subhead-2" style="color:#10a6a6;font-style:italic">GOOD EARTH</span></p>',
              },
            ],
          },
        ],
      },

      Brands.OldNavy,
      SMALL
    );

    expect(screen.getAllByTestId('absolute-wrapper')[0]).toHaveStyleRules({
      position: 'unset',
      height: 'auto',
    });
    expect(container).toMatchSnapshot();
  });
});

describe('CategoryBannerVariableHeightCarousel OldNavy Content Type', () => {
  it('should match snapshot for Brands.OldNavy', () => {
    const { container } = renderComponent(withSecondaryColorData);
    expect(container).toMatchSnapshot();
  });

  it('should have a max-width of 1280 on parent carousel wrapper', () => {
    const { container } = renderComponent();
    expect(getParentContainer(container)).toHaveStyleRule('max-width', '1280px');
  });
  it('should render with ConditionalPaginationTargetWrapperSection with ComponentMaxWidth when newPlpGridFeatureFlag is false', () => {
    const { container } = renderComponent({}, Brands.OldNavy, XLARGE, { 'mui-new-plp-grid-2025': false });
    expect(getParentContainer(container)).toHaveStyleRule('max-width', '1280px');
  });
  it('should render with ConditionalPaginationTargetWrapperSection without ComponentMaxWidth when newPlpGridFeatureFlag is true', () => {
    const { container } = renderComponent({}, Brands.OldNavy, XLARGE, { 'mui-new-plp-grid-2025': true });
    expect(getParentContainer(container)).not.toHaveStyleRule('max-width', '1280px');
  });
  it('should render a child components per tile', () => {
    const { container } = renderComponent({}, Brands.OldNavy, XLARGE);
    const flexWrapper = container.querySelectorAll('.slick-active')[0]?.firstChild?.firstChild as HTMLElement;
    expect(flexWrapper.children?.length).toBe(defaultProps.frames[0].tiles.length);
  });

  it('should render null', () => {
    const { container } = renderComponent({ frames: [] }, Brands.OldNavy, XLARGE);
    expect(container?.querySelectorAll('nav').length).toBe(0);
  });

  it('should render isPersistentContent as true', () => {
    const { container } = renderComponent({ contentConfiguration: true }, Brands.OldNavy, XLARGE);
    expect(container.firstChild?.firstChild?.firstChild?.firstChild?.firstChild?.firstChild).toHaveStyleRules({
      position: 'absolute',
      width: '100%',
      height: '100%',
    });
    expect(container).toMatchSnapshot();
  });
});

describe('on mobile', () => {
  cleanup();
  it('should have 4 slides', () => {
    const { container } = renderComponent({}, Brands.OldNavy, SMALL);
    expect(container?.querySelectorAll('.slick-slide').length).toBe(4);
    expect(container).toMatchSnapshot();
  });
  it('should render isPersistentContent as true', () => {
    const { container } = renderComponent({ contentConfiguration: true }, Brands.OldNavy, SMALL);
    expect(container).toMatchSnapshot();
  });

  it('should match snapshot for persistent below on mobile', () => {
    const { asFragment } = renderComponent({ contentConfiguration: true, mobileTextTreatment: 'below' }, Brands.OldNavy, SMALL);
    expect(asFragment()).toMatchSnapshot();
  });
});

describe('chevron focus, hover', () => {
  afterEach(cleanup);

  it('should have primary color snaps', () => {
    const { container } = renderComponent({}, Brands.OldNavy, XLARGE);
    const chevron: HTMLElement = container?.querySelector('button.slick-next') || container;

    fireEvent.mouseOver(screen.getByLabelText('Next'));
    let compStyles = {} as CSSStyleDeclaration;
    compStyles = window.getComputedStyle(chevron);

    expect(chevron).toMatchSnapshot();
    expect(compStyles.background).toBe('rgb(0, 55, 100)');
  });

  it('should have secondary color snaps', () => {
    const { container } = renderComponent(
      {
        carouselSettings: {
          ...defaultProps.carouselSettings,
          styling: {
            ...defaultProps.carouselSettings.styling,
            controlsIconsColor: 'secondary',
          },
        },
      },
      Brands.OldNavy,
      XLARGE
    );
    const chevron: HTMLElement = container?.querySelector('button.slick-next') || container;

    fireEvent.mouseOver(chevron);
    expect(chevron).toMatchSnapshot();
    let compStyles = {} as CSSStyleDeclaration;
    compStyles = window.getComputedStyle(chevron);

    fireEvent.mouseDown(chevron);
    expect(compStyles.background).toBe('rgb(255, 255, 255)');
  });

  it('should have secondary color on hover event for button.Next', () => {
    renderComponent(
      {
        carouselSettings: {
          ...defaultProps.carouselSettings,
          styling: {
            ...defaultProps.carouselSettings.styling,
            controlsIconsColor: 'secondary',
          },
        },
      },
      Brands.OldNavy,
      XLARGE
    );

    fireEvent.mouseOver(screen.getByLabelText('Next'));
    let compStyles = {} as CSSStyleDeclaration;
    compStyles = window.getComputedStyle(screen.getByLabelText('Next'));

    expect(compStyles.background).toBe('rgb(255, 255, 255)');
  });

  it('should have secondary color on focus event for button.Next', () => {
    const { container } = renderComponent(
      {
        carouselSettings: {
          ...defaultProps.carouselSettings,
          styling: {
            ...defaultProps.carouselSettings.styling,
            controlsIconsColor: 'secondary',
          },
        },
      },
      Brands.OldNavy,
      XLARGE
    );

    const chevron: HTMLElement = container?.querySelector('button.slick-next') || container;

    fireEvent.mouseDown(chevron);
    let compStyles = {} as CSSStyleDeclaration;
    compStyles = window.getComputedStyle(chevron);

    expect(compStyles.background).toBe('rgb(255, 255, 255)');
  });

  it('should have primary color on hover event for button.Next', () => {
    renderComponent({}, Brands.OldNavy, XLARGE);
    fireEvent.mouseOver(screen.getByLabelText('Next'));
    let compStyles = {} as CSSStyleDeclaration;
    compStyles = window.getComputedStyle(screen.getByLabelText('Next'));

    expect(compStyles.background).toBe('rgb(0, 55, 100)');
  });

  it('should have primary color on focus event for button.Next', () => {
    renderComponent({}, Brands.OldNavy, XLARGE);
    fireEvent.mouseDown(screen.getByLabelText('Next'));
    let compStyles = {} as CSSStyleDeclaration;
    compStyles = window.getComputedStyle(screen.getByLabelText('Next'));

    expect(compStyles.background).toBe('rgb(0, 55, 100)');
  });
});

describe('Snapshots', () => {
  cleanup();
  it('should match desktop snapshots', () => {
    const { container } = renderComponent({}, Brands.OldNavy, XLARGE);
    expect(container).toMatchSnapshot();
  });
  it('should match mobile snapshots', () => {
    const { container } = renderComponent({}, Brands.OldNavy, SMALL);
    expect(container).toMatchSnapshot();
  });
});
