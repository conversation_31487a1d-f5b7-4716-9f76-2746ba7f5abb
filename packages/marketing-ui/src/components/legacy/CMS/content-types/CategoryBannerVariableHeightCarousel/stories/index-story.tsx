// @ts-nocheck
'use client';
import React from 'react';
import { StoryFn } from '@storybook/react';
import { BreakpointProvider } from '@ecom-next/core/breakpoint-provider';
import { LocalizationProvider, normalizeLocale, Locale } from '@ecom-next/sitewide/localization-provider';
import { translations } from '../../../../helper/localTRO';
import { localizationPicker } from '../../../../stories/story-helpers';
import { CategoryBannerVHCarouselProps } from '../../../components/CategoryBannerVHCarousel';
import CategoryBannerVHCarousel from '..';
import { cbvhCarouselBaseDataContentData } from '../__fixtures__/test-data';
import README from '../README.mdx';

export default {
  title: 'Common/JSON Components (Marketing)/content-types/CategoryBannerVariableHeightCarousel',
  argTypes: {
    locale: localizationPicker,
  },
  parameters: {
    docs: { page: README },
    layout: 'fullscreen',
  },
  tags: ['exclude'],
  decorators: [
    (Story: React.FC) => (
      <BreakpointProvider>
        <Story />
      </BreakpointProvider>
    ),
  ],
};

type LocProps = {
  locale: Locale;
};

const NullJSX = ({ content, children, locale }: { content: JSX.Element; children: React.ReactNode; locale: Locale }): JSX.Element =>
  content && content.type() === null ? (
    <>{children}</>
  ) : (
    <LocalizationProvider locale={locale} translations={translations[normalizeLocale(locale)].translation}>
      {content}
    </LocalizationProvider>
  );

export const Playground: StoryFn<CategoryBannerVHCarouselProps & LocProps> = args => {
  const content = <CategoryBannerVHCarousel {...args} />;

  return (
    <NullJSX content={content} locale={args.locale ?? 'en-US'}>
      <p>Unsupported by brand</p>
    </NullJSX>
  );
};

Playground.argTypes = {
  desktopBannerSize: {
    control: {
      type: 'radio',
      options: ['medium', 'large'],
    },
  },
  mobileBannerSize: {
    control: { type: 'radio', options: ['medium', 'large'] },
  },
  contentConfiguration: {
    control: { active: { control: 'boolean' } },
  },
  mobileTextTreatment: {
    control: { type: 'radio', options: ['below', 'on'] },
  },
};

Playground.args = {
  ...cbvhCarouselBaseDataContentData,
};
