'use client';
import React from 'react';
// @ts-ignore
import { CSSObject, styled, useEnabledFeatures } from '@ecom-next/core/react-stitch';
import { ShowHideWrapper } from '../../subcomponents/ShowHideWrapper/index';
import { defaultComponentMaxWidths } from '../../subcomponents/ComponentMaxWidth/index';
import Hidden from '../../components/Hidden/index';
import { useViewportIsLarge } from '../../../hooks/useViewportIsLarge/index';
import { RichText } from '../../subcomponents/RichText/index';
import { CircleNavigationCarousel, CircleNavigationExposed } from '../../components/CircleNavigation/index';
import { TextContainer } from '../../components/CircleNavigation/styledComponents';
import BackgroundTypeContainer from '../../subcomponents/BackgroundTypeContainer/index';
import { CircleNavigationStylesType } from '../../components/CircleNavigation/types';
import { BackgroundTypeExtensionValue, BackgroundTypeVariant } from '../../types/amplience';
import { CircleNavigationContentType, CircleNavigationWebAppearance, SizePropertiesType } from './types';
import { exposedRowsByViewport } from './layouts/exposedRowsByViewport.on';
import { useRowsLayout } from './hooks/useRowsLayout';

// Utility function to conditionally add data-testid in non-production environments
const getTestId = (testId: string): { 'data-testid'?: string } => {
  // Only include data-testid in non-production environments
  const isNonProd = !(process.env.TARGET_ENV || '').includes('prod');
  return isNonProd ? { 'data-testid': testId } : {};
};

export type CircleNavigationProps = CircleNavigationContentType;

const LARGE_VIEWPORT = 1280;
const SMALL_VIEWPORT = 375;
const DESKTOP_SPEC_SCALE_POINT = 1440;
const DESKTOP_SPEC_CIRCLE_WIDTH = 120;
const DESKTOP_SPEC_CATEGORY_SIDE_PADDING = 8;
const DESKTOP_SPEC_CATEGORY_TOPBOTTOM_PADDING = 16;
const DESKTOP_SPEC_LABEL_WIDTH = 144;

const categoryStylesGroup = (webAppearance: CircleNavigationWebAppearance, isViewportLarge: boolean): CircleNavigationStylesType => {
  const {
    fontColor,
    mobileCategoriesTreatment = 'exposed', // May 2024: Old Navy wants mobile to be a slider in all cases
    imageSize = 'large',
  } = webAppearance;

  const desktopCategoryTotalWidth = DESKTOP_SPEC_LABEL_WIDTH + DESKTOP_SPEC_CATEGORY_SIDE_PADDING * 2;

  const labelStyles: SizePropertiesType = {
    default: {
      color: fontColor,
      width: `${(70 / SMALL_VIEWPORT) * 100}vw`,
      marginTop: !isViewportLarge ? `${(8 / SMALL_VIEWPORT) * 100}vw` : 0,
      fontSize: `${(10 / SMALL_VIEWPORT) * 100}vw`,
    },
    large: {
      boxSizing: 'border-box',
      color: fontColor,
      width: !isViewportLarge ? `min(${(136 / LARGE_VIEWPORT) * 100}vw, 136px)` : '100%',
      aspectRatio: '144 / 48',
      marginTop:
        imageSize === 'large'
          ? `${(DESKTOP_SPEC_CATEGORY_TOPBOTTOM_PADDING * 100) / (desktopCategoryTotalWidth - DESKTOP_SPEC_CATEGORY_SIDE_PADDING * 2)}%`
          : `min(${(10 / LARGE_VIEWPORT) * 100}vw, 10px)`,
    },
  };
  const largeLabelVariant = 'body2';

  const imageStyles: SizePropertiesType = {
    default: {
      width: '16vw',
      height: '16vw',
    },
    large:
      webAppearance.desktopLayout === 'slider'
        ? {
            width: imageSize === 'large' ? `${(DESKTOP_SPEC_CIRCLE_WIDTH / DESKTOP_SPEC_LABEL_WIDTH) * 100}%` : `min(${(102 / LARGE_VIEWPORT) * 100}vw, 102px)`,
            aspectRatio: '1/1',
          }
        : {
            width:
              imageSize === 'large'
                ? `min(${(DESKTOP_SPEC_CIRCLE_WIDTH / DESKTOP_SPEC_SCALE_POINT) * 100}vw, 120px)`
                : `min(${(102 / LARGE_VIEWPORT) * 100}vw, 102px)`,
            aspectRatio: '1/1',
          },
  };

  return {
    exposed: {
      circle: {
        width: `${((DESKTOP_SPEC_LABEL_WIDTH + DESKTOP_SPEC_CATEGORY_SIDE_PADDING * 2) / DESKTOP_SPEC_SCALE_POINT) * 100}%`,
      },
    },
    container: {
      boxSizing: 'border-box',
      width: !isViewportLarge ? '16vw' : '100%',
      margin: !isViewportLarge ? '0' : 'unset',
      padding: isViewportLarge
        ? `${(DESKTOP_SPEC_CATEGORY_TOPBOTTOM_PADDING * 100) / desktopCategoryTotalWidth}% ${
            (DESKTOP_SPEC_CATEGORY_SIDE_PADDING * 100) / desktopCategoryTotalWidth
          }%`
        : 'unset',
    },
    image: isViewportLarge ? imageStyles.large : imageStyles.default,
    label: isViewportLarge ? labelStyles.large : labelStyles.default,
    link: {
      color: fontColor,
      width: mobileCategoriesTreatment === 'exposed' ? '19vw' : '16vw',
    },
    labelVariant: isViewportLarge ? largeLabelVariant : 'body4',
  };
};

const OldNavyCircleNavigation = (props: CircleNavigationProps): JSX.Element | null => {
  const { background, mobileBackgroundImage, categoryImages = [], categoryInfo = [], webAppearance = { desktopLayout: 'slider' }, richText } = props;

  const enabledFeatures = useEnabledFeatures();
  const isNewPlpGrid2025 = !!enabledFeatures?.['mui-new-plp-grid-2025'];

  const isViewportLarge = useViewportIsLarge();
  // const {mobileCategoriesTreatment = "exposed"} = webAppearance;
  const isDesktopCarouselLayout = isViewportLarge && webAppearance?.desktopLayout === 'slider';
  const categoryCount = categoryImages.length;
  const minHeight: string = isViewportLarge ? '0' : '19vw';

  /*
   * July 2024: Previously, desktopMaxWidth changed based on the marketingType.
   * However, the new design has 1440 as the max width regardless of the slot being EBB or home.
   * This is because UX wants the width to match sitewide banners' carousels that the EBB may be directly under.
   * Also, widths on category pages have changed recently.
   *
   */

  const desktopMaxWidth = isNewPlpGrid2025 ? undefined : defaultComponentMaxWidths.homepage.on;

  const rows = useRowsLayout(exposedRowsByViewport, categoryCount, webAppearance.imageSize);
  const circleNavCategories = categoryImages.map((categoryImage, index) => ({
    image: categoryImage,
    info: categoryInfo[index],
    key: `${categoryImage.image?.id}+${index}`,
  }));

  const categoryStyles = categoryStylesGroup(webAppearance, isViewportLarge);

  function getTextContainerCSS(displayBackgroundContainer: boolean): CSSObject {
    const paddingBottomDesktop = 16;
    const paddingBottomMobile = displayBackgroundContainer ? 8 : 20;

    const desktopCarouselLayoutStyles: CSSObject = {
      margin: '0 148px',
      // this padding is calculated in order to scale based on figma showing 24px at a 1440px viewport width
      paddingBottom: `${(16 / DESKTOP_SPEC_SCALE_POINT) * 100}%`,
    };

    const exposedOrMobileStyles: CSSObject = {
      boxSizing: 'border-box',
      minHeight,
      paddingInline: !isViewportLarge ? `${(32 / SMALL_VIEWPORT) * 100}vw` : `min(${(60 / LARGE_VIEWPORT) * 100}vw, 60px)`,
      paddingTop: !isViewportLarge ? `${(8 / SMALL_VIEWPORT) * 100}vw` : `min(${(16 / LARGE_VIEWPORT) * 100}vw, 16px)`,
      paddingBottom: !isViewportLarge
        ? `${(paddingBottomMobile / SMALL_VIEWPORT) * 100}vw`
        : `min(${(paddingBottomDesktop / LARGE_VIEWPORT) * 100}vw, ${paddingBottomDesktop}px)`,
    };

    return isDesktopCarouselLayout ? desktopCarouselLayoutStyles : exposedOrMobileStyles;
  }

  const isCarousel = circleNavCategories.length > 4;
  const carouselMargins = {
    marginLeft: `${(18 / DESKTOP_SPEC_SCALE_POINT) * 100}%`,
    marginRight: `${(18 / DESKTOP_SPEC_SCALE_POINT) * 100}%`,
  };

  if (!isViewportLarge) {
    const leftRightMargin = isCarousel ? `${(8 / SMALL_VIEWPORT) * 100}%` : `${(12 / SMALL_VIEWPORT) * 100}%`;
    carouselMargins.marginLeft = leftRightMargin;
    carouselMargins.marginRight = leftRightMargin;
  }

  function getBackground(): BackgroundTypeExtensionValue {
    if (mobileBackgroundImage && !isViewportLarge) {
      const type: BackgroundTypeVariant = 'image' as BackgroundTypeVariant;
      return {
        type,
        images: mobileBackgroundImage,
      };
    }
    return background;
  }

  const [firstBackgroundImage] = getBackground()?.images || [];
  const displayBackgroundContainer = firstBackgroundImage?.image || getBackground()?.color;

  const mobileContent = () => {
    if (isViewportLarge) return null;
    // if (mobileCategoriesTreatment === "slider") {
    categoryStyles.link = { ...categoryStyles.link, width: '19vw' };
    return <CircleNavigationCarousel categories={circleNavCategories} desktopMaxWidth={desktopMaxWidth} styles={categoryStyles} />;
    // }
    // May 2024: Old Navy wants mobile to be a slider in all cases
    // if (mobileCategoriesTreatment === "exposed") {
    //   return (
    //     <CircleNavigationExposed
    //       categories={circleNavCategories}
    //       customHoverOpacity={0.75}
    //       rows={rows}
    //       styles={categoryStyles}
    //     />
    //   );
    // }

    // return null;
  };

  // this padding is calculated in order to scale based on figma showing 16px at a 1440px viewport width
  const containerPaddingDesktop = `${(16 / DESKTOP_SPEC_SCALE_POINT) * 100}%`;

  const RichTextArea: JSX.Element = (
    <div>
      <TextContainer css={getTextContainerCSS(displayBackgroundContainer !== undefined)}>
        <RichText
          isDesktop={isViewportLarge}
          scalableText={{
            parentMaxWidthPx: desktopMaxWidth,
            enable: true,
          }}
          text={richText}
        />
      </TextContainer>
    </div>
  );

  const BannerContainer = styled.div(
    isViewportLarge && {
      paddingBottom: 16,
      paddingTop: 16,
      ...(!isNewPlpGrid2025 && { maxWidth: `${desktopMaxWidth}px` }),
      margin: '0 auto',
    }
  );

  const isExposedLayout = webAppearance?.desktopLayout === 'exposed';

  return (
    <ShowHideWrapper breakpoint='large' showHideBasedOnScreenSize={webAppearance?.showHide}>
      <BannerContainer>
        {firstBackgroundImage?.image || !!richText ? (
          <BackgroundTypeContainer
            background={getBackground()}
            css={{
              width: '100%',
              height: '100%',
              minHeight,
              paddingTop: isViewportLarge && !!richText && !isExposedLayout ? containerPaddingDesktop : 0,
            }}
          >
            {!!richText && RichTextArea}
          </BackgroundTypeContainer>
        ) : (
          <>{!!richText && RichTextArea}</>
        )}
        <div css={carouselMargins}>
          <Hidden isHidden={!isViewportLarge}>
            {webAppearance?.desktopLayout === 'exposed' ? (
              <CircleNavigationExposed categories={circleNavCategories} desktopMaxWidth={desktopMaxWidth} rows={rows} styles={categoryStyles} />
            ) : (
              <CircleNavigationCarousel categories={circleNavCategories} desktopMaxWidth={desktopMaxWidth} styles={categoryStyles} />
            )}
          </Hidden>
          <Hidden isHidden={isViewportLarge}>{mobileContent()}</Hidden>
        </div>
      </BannerContainer>
    </ShowHideWrapper>
  );
};

export default OldNavyCircleNavigation;
