// @ts-nocheck
import { CircleNavigationContentType } from '../types';
const jeans = require('../../../../assets/boyfriend-jeans.jpg').default?.src;
const man = require('../../../../assets/man-on-casual-clothes.png').default?.src;
const man2 = require('../../../../assets/man-walking.png').default?.src;
const woman = require('../../../../assets/woman-cheering.png').default?.src;

export const categoryBannerCircleNavigationFour: CircleNavigationContentType = {
  _meta: {
    name: 'Category Banner Circle Navigation',
    schema: 'https://cms.gap.com/schema/content/v1/circle-navigation.json',
    deliveryId: '736fc556-cbef-4c48-bf43-7ffeed1152d7',
  },
  categoryImages: [
    {
      svgPath: jeans,
      altText: 'card1',
      variations: [
        {
          variation: 'desktop',
        },
        {
          variation: 'mobile',
        },
      ],
      fliph: false,
      flipv: false,
      enableChroma: false,
      chromaQuality: 80,
    },
    {
      svgPath: man,
      altText: 'card2',
      variations: [
        {
          variation: 'desktop',
        },
        {
          variation: 'mobile',
        },
      ],
      fliph: false,
      flipv: false,
      enableChroma: false,
      chromaQuality: 80,
    },
    {
      svgPath: man2,
      altText: 'card3',
      variations: [
        {
          variation: 'desktop',
        },
        {
          variation: 'mobile',
        },
      ],
      fliph: false,
      flipv: false,
      enableChroma: false,
      chromaQuality: 80,
    },
    {
      svgPath: woman,
      altText: 'card4',
      variations: [
        {
          variation: 'desktop',
        },
        {
          variation: 'mobile',
        },
      ],
      fliph: false,
      flipv: false,
      enableChroma: false,
      chromaQuality: 80,
    },
    {
      svgPath: jeans,
      altText: 'card5',
      variations: [
        {
          variation: 'desktop',
        },
        {
          variation: 'mobile',
        },
      ],
      fliph: false,
      flipv: false,
      enableChroma: false,
      chromaQuality: 80,
    },
    {
      svgPath: man,
      altText: 'card6',
      variations: [
        {
          variation: 'desktop',
        },
        {
          variation: 'mobile',
        },
      ],
      fliph: false,
      flipv: false,
      enableChroma: false,
      chromaQuality: 80,
    },
    {
      svgPath: man2,
      altText: 'card7',
      variations: [
        {
          variation: 'desktop',
        },
        {
          variation: 'mobile',
        },
      ],
      fliph: false,
      flipv: false,
      enableChroma: false,
      chromaQuality: 80,
    },
    {
      svgPath: woman,
      altText: 'card8',
      variations: [
        {
          variation: 'desktop',
        },
        {
          variation: 'mobile',
        },
      ],
      fliph: false,
      flipv: false,
      enableChroma: false,
      chromaQuality: 80,
    },
    {
      svgPath: jeans,
      altText: 'card9',
      variations: [
        {
          variation: 'desktop',
        },
        {
          variation: 'mobile',
        },
      ],
      fliph: false,
      flipv: false,
      enableChroma: false,
      chromaQuality: 80,
    },
    {
      svgPath: man,
      altText: 'card10',
      variations: [
        {
          variation: 'desktop',
        },
        {
          variation: 'mobile',
        },
      ],
      fliph: false,
      flipv: false,
      enableChroma: false,
      chromaQuality: 80,
    },
    {
      svgPath: man2,
      altText: 'card11',
      variations: [
        {
          variation: 'desktop',
        },
        {
          variation: 'mobile',
        },
      ],
      fliph: false,
      flipv: false,
      enableChroma: false,
      chromaQuality: 80,
    },
    {
      svgPath: woman,
      altText: 'card12',
      variations: [
        {
          variation: 'desktop',
        },
        {
          variation: 'mobile',
        },
      ],
      fliph: false,
      flipv: false,
      enableChroma: false,
      chromaQuality: 80,
    },
    {
      svgPath: jeans,
      altText: 'card13',
      variations: [
        {
          variation: 'desktop',
        },
        {
          variation: 'mobile',
        },
      ],
      fliph: false,
      flipv: false,
      enableChroma: false,
      chromaQuality: 80,
    },
    {
      svgPath: man,
      altText: 'card14',
      variations: [
        {
          variation: 'desktop',
        },
        {
          variation: 'mobile',
        },
      ],
      fliph: false,
      flipv: false,
      enableChroma: false,
      chromaQuality: 80,
    },
  ],
  background: {
    type: 'solid',
  },
  webAppearance: {
    imageSize: 'large',
    fontSize: 'large',
    mobileCategoriesTreatment: 'exposed',
    fontColor: '#000000',
    desktopLayout: 'exposed',
    showHide: 'alwaysShow',
  },
  categoryInfo: [
    {
      label: 'Women',
      value: '#',
    },
    {
      label: "W's Plus",
      value: '#',
    },
    {
      label: 'Men',
      value: '#',
    },
    {
      label: 'Girls',
      value: '#',
    },
    {
      label: 'Boys',
      value: '#',
    },
    {
      label: 'Toddler Girls',
      value: '#',
    },
    {
      label: 'Toddler Boys',
      value: '#',
    },
    {
      label: 'Baby Girls',
      value: '#',
    },
    {
      label: 'Baby Boys',
      value: '#',
    },
    {
      label: 'Maternity',
      value: '#',
    },
    {
      label: 'New Arrivals',
      value: '#',
    },
    {
      label: 'Clearance',
      value: '#',
    },
    {
      label: 'Dresses',
      value: '#',
    },
    {
      label: 'Swim Wear',
      value: '#',
    },
  ],
};
