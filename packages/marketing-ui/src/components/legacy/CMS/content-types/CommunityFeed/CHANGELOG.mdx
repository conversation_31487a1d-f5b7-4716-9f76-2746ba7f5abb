# Community Spotlight Changelog

## 2022-12

- Community Feed - Updates based on Schema Changes [PTMDS-472](https://gapinc.atlassian.net/browse/PTMDS-472)

  The partial for Community Feed CTAs changed from an object to an array. Also, the content field is now possibly undefined.

  - With CTAs Before

    ```
        {
            "content": {
                "cards" : {...},
                "cta": {
                    "cta": {
                        "label": "CTA 1",
                        "value": "cta1"
                    },
                    "buttonStyle": {
                        "buttonStyle": "chevron",
                        "buttonColor": "custom",
                        "primaryHex": "#CCC",
                        "secondaryHex": "#0f0"
                    }
                }
            }
        }
    ```

  - Without CTAs or other content Before

    ```
        {
            "content": {
                "cards" : {...},
                "cta": {
                    "buttonStyle": {
                        "buttonStyle": "border",
                        "buttonColor": "dark",
                    }
                }
            }
        }
    ```

### How to Resolve Breaking Changes

- With CTAs After Change

  ```
      {
          "content": {
              "cards" : {...},
              "cta": [
                  {
                      "buttonStyle": {
                          "buttonStyle": "border",
                          "buttonColor": "darl",
                          "primaryHex": "#CCC",
                          "secondaryHex": "#0f0"
                      },
                      "cta": {
                          "label": "",
                          "value": "#"
                      }
                  }
              ]
          }
      }
  ```

- Without CTAs or other content After Change

  ```
      {
          "content": {
            "cards": {...},
          }
      }
  ```
