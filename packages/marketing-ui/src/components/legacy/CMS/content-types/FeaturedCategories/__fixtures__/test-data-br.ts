import { BRButtonStyle } from '../../../global/types';
import { Frame, FeaturedCategoriesContentType } from '../types';
import { defaultFrame, defaultFrame2, defaultFrame3, defaultFrame4, general, _meta } from './test-data';

export const defaultTestDataBR: FeaturedCategoriesContentType = {
  _meta,
  general: general('inset', 'medium', 'carousel'),
  carouselSettings: {
    transition: 'slide',
    type: 'clickThrough',
    continuousLoop: false,
    autoplay: {
      delay: 3000,
      pauseOnHover: false,
    },
    animation: {
      speed: 500,
      ease: false,
    },
    styling: {
      controlsIconsColor: 'primary',
      pagination: 'hide',
      hideChevrons: false,
      paginationStyle: 'chevron',
      paginationAlignment: 'left',
    },
  },
  videoControlSettings: {
    controlsIconsColor: 'secondary',
    displayVideoControls: true,
    audioVideoControlLocation: 'right',
    autoplay: false,
    sequentialLoop: false,
    singleLoop: false,
    videoDisplaySettings: 'displayOnHover',
    videoSoundIcons: true,
  },
  frame: [defaultFrame, defaultFrame2, defaultFrame3, defaultFrame4],
};

export const defaultFourFramesFullBleed: FeaturedCategoriesContentType = {
  ...defaultTestDataBR,
  general: {
    ...defaultTestDataBR.general,
    layout: 'fullBleed',
    size: 'large',
  },
  frame: [
    {
      ...defaultFrame,
      contentJustification: 'center',
      verticalAlignment: 'bottom',
      spotlightText: {
        useGradientBackfill: false,
        defaultText: '<p class="amp-cms--p" style="text-align:left;"><span class="amp-cms--body-1" style="color:#FFFFFF">First Card</span></p>',
        mobileOverride: '<p class="amp-cms--p" style="text-align:left;"><span class="amp-cms--body-1 style="color:#FFFFFF"">First Mobile</span></p>',
      },
      ctaButtons: [
        {
          cta: {
            label: 'CTA ONE',
            value: 'www.somethingonc.com',
          },
          buttonStyle: {
            buttonStyle: 'solid',
            buttonSize: 'large',
            buttonColor: 'primary',
          },
        },
        {
          cta: {
            label: 'CTA TWO',
            value: 'www.somethingtwo.com',
          },
          buttonStyle: {
            buttonStyle: 'solid',
            buttonSize: 'large',
            buttonColor: 'secondary',
          },
        },
      ],
      imageOverlays: {
        handle: {
          placement: 'left',
        },
        detailsLink: {
          fontColor: 'primary',
          label: 'Details',
          prefixLabel: 'Exclutions Apply',
          pemoleCode: '1032197',
          htmlModalUrl: 'https://cdn.c1.amplience.net/c/oldnavyprod/rest_details_070823',
        },
        useGradientBackfill: false,
      },
      videoBackground: [
        {
          nonVimeoVideo: {
            desktop: {
              url: 'https://onol.wip.prod.gaptecholapps.com/Asset_Archive/videocampaign/SVH_Video_1440x800.mp4',
              fallbackImage: [
                {
                  image: {
                    _meta: {
                      schema: 'http://bigcontent.io/cms/schema/v1/core#/definitions/image-link',
                    },
                    id: '44de475f-9944-48a8-b20b-552aa16cfbbf',
                    name: 'gdpcat3',
                    endpoint: 'athleta',
                    defaultHost: '2hmc8flz4ofg1dy89cc38f2xm.staging.bigcontent.io',
                    mimeType: 'image/jpeg',
                  },
                  altText: 'nonvimeodesktopfallbackimg',
                  variations: [
                    {
                      variation: 'desktop',
                    },
                    {
                      variation: 'mobile',
                    },
                  ],
                  fliph: false,
                  flipv: false,
                  enableChroma: false,
                  chromaQuality: 80,
                },
              ],
            },
            mobile: {
              url: 'https://onol.wip.prod.gaptecholapps.com/Asset_Archive/videocampaign/SVH_Video_1440x800.mp4',
              fallbackImage: [
                {
                  image: {
                    _meta: {
                      schema: 'http://bigcontent.io/cms/schema/v1/core#/definitions/image-link',
                    },
                    id: 'd3c6a769-d9f8-44a7-816b-3702f272b62d',
                    name: 'Card 3 - MobileL',
                    endpoint: 'athleta',
                    defaultHost: '2hmc8flz4ofg1dy89cc38f2xm.staging.bigcontent.io',
                    mimeType: 'image/png',
                  },
                  altText: 'nonvimeomobilefallbackimg',
                  variations: [
                    {
                      variation: 'desktop',
                    },
                    {
                      variation: 'mobile',
                    },
                  ],
                  fliph: false,
                  flipv: false,
                  enableChroma: false,
                  chromaQuality: 80,
                },
              ],
            },
          },
        },
      ],
    },
    {
      ...defaultFrame,
      contentJustification: 'center',
      verticalAlignment: 'bottom',
      spotlightText: {
        useGradientBackfill: false,
        defaultText: '<p class="amp-cms--p" style="text-align:left;"><span class="amp-cms--body-1" style="color:#FFFFFF">Second Card</span></p>',
        mobileOverride: '<p class="amp-cms--p" style="text-align:left;"><span class="amp-cms--body-1 style="color:#FFFFFF"">Second Mobile</span></p>',
      },
      linkWrapper: {
        label: 'new arrivals',
        value: 'www.gap.com',
      },
      ctaButtons: [
        {
          cta: {
            label: 'CTA ONE',
            value: 'www.somethingonc.com',
          },
          buttonStyle: {
            buttonStyle: 'solid',
            buttonSize: 'small',
            buttonColor: 'primary',
          },
        },
        {
          cta: {
            label: 'CTA TWO',
            value: 'www.somethingtwo.com',
          },
          buttonStyle: {
            buttonStyle: 'solid',
            buttonSize: 'small',
            buttonColor: 'secondary',
          },
        },
      ],
      imageOverlays: {
        handle: {
          placement: 'left',
        },
        detailsLink: {
          fontColor: 'secondary',
          label: 'View Details',
          prefixLabel: 'Exclutions',
          pemoleCode: '1032197',
          htmlModalUrl: 'https://cdn.c1.amplience.net/c/oldnavyprod/rest_details_070823',
        },
        useGradientBackfill: false,
      },
    },
    {
      ...defaultFrame,
      contentJustification: 'right',
      verticalAlignment: 'bottom',
      spotlightText: {
        useGradientBackfill: false,
        defaultText: '<p class="amp-cms--p" style="text-align:left;"><span class="amp-cms--body-1" style="color:#FFFFFF">Third Card</span></p>',
        mobileOverride: '<p class="amp-cms--p" style="text-align:left;"><span class="amp-cms--body-1 style="color:#FFFFFF"">Third Mobile</span></p>',
      },
      ctaButtons: [
        {
          cta: {
            label: 'CTA ONE',
            value: '#',
          },
          buttonStyle: {
            buttonStyle: 'solid',
            buttonSize: 'large',
            buttonColor: 'primary',
          },
        },
        {
          cta: {
            label: 'CTA TWO',
            value: '#',
          },
          buttonStyle: {
            buttonStyle: 'solid',
            buttonSize: 'large',
            buttonColor: 'secondary',
          },
        },
      ],
      videoBackground: [
        {
          nonVimeoVideo: {
            desktop: {
              url: 'https://onol.wip.prod.gaptecholapps.com/Asset_Archive/videocampaign/SVH_Video_1440x800.mp4',
              fallbackImage: [
                {
                  image: {
                    _meta: {
                      schema: 'http://bigcontent.io/cms/schema/v1/core#/definitions/image-link',
                    },
                    id: '44de475f-9944-48a8-b20b-552aa16cfbbf',
                    name: 'gdpcat3',
                    endpoint: 'athleta',
                    defaultHost: '2hmc8flz4ofg1dy89cc38f2xm.staging.bigcontent.io',
                    mimeType: 'image/jpeg',
                  },
                  altText: 'nonvimeodesktopfallbackimg',
                  variations: [
                    {
                      variation: 'desktop',
                    },
                    {
                      variation: 'mobile',
                    },
                  ],
                  fliph: false,
                  flipv: false,
                  enableChroma: false,
                  chromaQuality: 80,
                },
              ],
            },
            mobile: {
              url: 'https://onol.wip.prod.gaptecholapps.com/Asset_Archive/videocampaign/SVH_Video_1440x800.mp4',
              fallbackImage: [
                {
                  image: {
                    _meta: {
                      schema: 'http://bigcontent.io/cms/schema/v1/core#/definitions/image-link',
                    },
                    id: 'd3c6a769-d9f8-44a7-816b-3702f272b62d',
                    name: 'Card 3 - MobileL',
                    endpoint: 'athleta',
                    defaultHost: '2hmc8flz4ofg1dy89cc38f2xm.staging.bigcontent.io',
                    mimeType: 'image/png',
                  },
                  altText: 'nonvimeomobilefallbackimg',
                  variations: [
                    {
                      variation: 'desktop',
                    },
                    {
                      variation: 'mobile',
                    },
                  ],
                  fliph: false,
                  flipv: false,
                  enableChroma: false,
                  chromaQuality: 80,
                },
              ],
            },
          },
        },
      ],
      imageOverlays: {
        handle: {
          placement: 'left',
        },
        detailsLink: {
          fontColor: 'primary',
          label: 'Details view',
          prefixLabel: 'Exclutions apply',
          pemoleCode: '1032197',
          htmlModalUrl: 'https://cdn.c1.amplience.net/c/oldnavyprod/rest_details_070823',
        },
        useGradientBackfill: false,
      },
    },
    {
      ...defaultFrame,
      contentJustification: 'center',
      verticalAlignment: 'bottom',
      spotlightText: {
        useGradientBackfill: false,
        defaultText: '<p class="amp-cms--p" style="text-align:left;"><span class="amp-cms--body-1" style="color:#FFFFFF">Fourth Card</span></p>',
        mobileOverride: '<p class="amp-cms--p" style="text-align:left;"><span class="amp-cms--body-1 style="color:#FFFFFF"">Fourth Mobile</span></p>',
      },
      ctaButtons: [
        {
          cta: {
            label: 'CTA ONE',
            value: '#',
          },
          buttonStyle: {
            buttonStyle: 'underline',
            buttonSize: 'large',
            buttonColor: 'secondary',
          },
        },
        {
          cta: {
            label: 'CTA TWO',
            value: '#',
          },
          buttonStyle: {
            buttonStyle: 'underline',
            buttonSize: 'large',
            buttonColor: 'secondary',
          },
        },
      ],
      imageOverlays: {
        handle: {
          placement: 'left',
        },
        detailsLink: {
          fontColor: 'secondary',
          label: 'details',
          prefixLabel: 'exclutions apply',
          pemoleCode: '1032197',
          htmlModalUrl: 'https://cdn.c1.amplience.net/c/oldnavyprod/rest_details_070823',
        },
        useGradientBackfill: false,
      },
    },
    {
      ...defaultFrame,
      contentJustification: 'center',
      verticalAlignment: 'bottom',
      spotlightText: {
        useGradientBackfill: false,
        defaultText: '<p class="amp-cms--p" style="text-align:left;"><span class="amp-cms--body-1" style="color:#FFFFFF">Fifth Card</span></p>',
        mobileOverride: '<p class="amp-cms--p" style="text-align:left;"><span class="amp-cms--body-1 style="color:#FFFFFF"">Fifth Mobile</span></p>',
      },
      ctaButtons: [
        {
          cta: {
            label: 'CTA ONE',
            value: '#',
          },
          buttonStyle: {
            buttonStyle: 'underline',
            buttonSize: 'large',
            buttonColor: 'secondary',
          },
        },
        {
          cta: {
            label: 'CTA TWO',
            value: '#',
          },
          buttonStyle: {
            buttonStyle: 'underline',
            buttonSize: 'large',
            buttonColor: 'secondary',
          },
        },
      ],
    },
    {
      ...defaultFrame,
      contentJustification: 'center',
      verticalAlignment: 'bottom',
      spotlightText: {
        useGradientBackfill: false,
        defaultText: '<p class="amp-cms--p" style="text-align:left;"><span class="amp-cms--body-1" style="color:#FFFFFF">Sixth Card</span></p>',
        mobileOverride: '<p class="amp-cms--p" style="text-align:left;"><span class="amp-cms--body-1 style="color:#FFFFFF"">Sixth Mobile</span></p>',
      },
      ctaButtons: [
        {
          cta: {
            label: 'CTA ONE',
            value: '#',
          },
          buttonStyle: {
            buttonStyle: 'underline',
            buttonSize: 'large',
            buttonColor: 'secondary',
          },
        },
        {
          cta: {
            label: 'CTA TWO',
            value: '#',
          },
          buttonStyle: {
            buttonStyle: 'underline',
            buttonSize: 'large',
            buttonColor: 'secondary',
          },
        },
      ],
    },
    {
      ...defaultFrame,
      contentJustification: 'center',
      verticalAlignment: 'bottom',
      spotlightText: {
        useGradientBackfill: false,
        defaultText: '<p class="amp-cms--p" style="text-align:left;"><span class="amp-cms--body-1" style="color:#FFFFFF">Seventh Card</span></p>',
        mobileOverride: '<p class="amp-cms--p" style="text-align:left;"><span class="amp-cms--body-1 style="color:#FFFFFF"">Seventh Mobile</span></p>',
      },
      ctaButtons: [
        {
          cta: {
            label: 'CTA ONE',
            value: 'www.somethingonc.com',
          },
          buttonStyle: {
            buttonStyle: 'solid',
            buttonSize: 'large',
            buttonColor: 'primary',
          },
        },
        {
          cta: {
            label: 'CTA TWO',
            value: 'www.somethingtwo.com',
          },
          buttonStyle: {
            buttonStyle: 'solid',
            buttonSize: 'large',
            buttonColor: 'secondary',
          },
        },
      ],
      imageOverlays: {
        handle: {
          placement: 'left',
        },
        detailsLink: {
          fontColor: 'primary',
          label: 'Details',
          prefixLabel: 'Exclutions Apply',
          pemoleCode: '1032197',
          htmlModalUrl: 'https://cdn.c1.amplience.net/c/oldnavyprod/rest_details_070823',
        },
        useGradientBackfill: false,
      },
      videoBackground: [
        {
          nonVimeoVideo: {
            desktop: {
              url: 'https://onol.wip.prod.gaptecholapps.com/Asset_Archive/videocampaign/SVH_Video_1440x800.mp4',
              fallbackImage: [
                {
                  image: {
                    _meta: {
                      schema: 'http://bigcontent.io/cms/schema/v1/core#/definitions/image-link',
                    },
                    id: '44de475f-9944-48a8-b20b-552aa16cfbbf',
                    name: 'gdpcat3',
                    endpoint: 'athleta',
                    defaultHost: '2hmc8flz4ofg1dy89cc38f2xm.staging.bigcontent.io',
                    mimeType: 'image/jpeg',
                  },
                  altText: 'nonvimeodesktopfallbackimg',
                  variations: [
                    {
                      variation: 'desktop',
                    },
                    {
                      variation: 'mobile',
                    },
                  ],
                  fliph: false,
                  flipv: false,
                  enableChroma: false,
                  chromaQuality: 80,
                },
              ],
            },
            mobile: {
              url: 'https://onol.wip.prod.gaptecholapps.com/Asset_Archive/videocampaign/SVH_Video_1440x800.mp4',
              fallbackImage: [
                {
                  image: {
                    _meta: {
                      schema: 'http://bigcontent.io/cms/schema/v1/core#/definitions/image-link',
                    },
                    id: 'd3c6a769-d9f8-44a7-816b-3702f272b62d',
                    name: 'Card 3 - MobileL',
                    endpoint: 'athleta',
                    defaultHost: '2hmc8flz4ofg1dy89cc38f2xm.staging.bigcontent.io',
                    mimeType: 'image/png',
                  },
                  altText: 'nonvimeomobilefallbackimg',
                  variations: [
                    {
                      variation: 'desktop',
                    },
                    {
                      variation: 'mobile',
                    },
                  ],
                  fliph: false,
                  flipv: false,
                  enableChroma: false,
                  chromaQuality: 80,
                },
              ],
            },
          },
        },
      ],
    },
    {
      ...defaultFrame,
      contentJustification: 'center',
      verticalAlignment: 'bottom',
      spotlightText: {
        useGradientBackfill: false,
        defaultText: '<p class="amp-cms--p" style="text-align:left;"><span class="amp-cms--body-1" style="color:#FFFFFF">Eigth Card</span></p>',
        mobileOverride: '<p class="amp-cms--p" style="text-align:left;"><span class="amp-cms--body-1 style="color:#FFFFFF"">Eigth Mobile</span></p>',
      },
      linkWrapper: {
        label: 'new arrivals',
        value: 'www.gap.com',
      },
      ctaButtons: [
        {
          cta: {
            label: 'CTA ONE',
            value: 'www.somethingonc.com',
          },
          buttonStyle: {
            buttonStyle: 'solid',
            buttonSize: 'small',
            buttonColor: 'primary',
          },
        },
        {
          cta: {
            label: 'CTA TWO',
            value: 'www.somethingtwo.com',
          },
          buttonStyle: {
            buttonStyle: 'solid',
            buttonSize: 'small',
            buttonColor: 'secondary',
          },
        },
      ],
      imageOverlays: {
        handle: {
          placement: 'left',
        },
        detailsLink: {
          fontColor: 'secondary',
          label: 'View Details',
          prefixLabel: 'Exclutions',
          pemoleCode: '1032197',
          htmlModalUrl: 'https://cdn.c1.amplience.net/c/oldnavyprod/rest_details_070823',
        },
        useGradientBackfill: false,
      },
    },
    {
      ...defaultFrame,
      contentJustification: 'right',
      verticalAlignment: 'bottom',
      spotlightText: {
        useGradientBackfill: false,
        defaultText: '<p class="amp-cms--p" style="text-align:left;"><span class="amp-cms--body-1" style="color:#FFFFFF">Ninth Card</span></p>',
        mobileOverride: '<p class="amp-cms--p" style="text-align:left;"><span class="amp-cms--body-1 style="color:#FFFFFF"">Ninth Mobile</span></p>',
      },
      ctaButtons: [
        {
          cta: {
            label: 'CTA ONE',
            value: '#',
          },
          buttonStyle: {
            buttonStyle: 'solid',
            buttonSize: 'large',
            buttonColor: 'primary',
          },
        },
        {
          cta: {
            label: 'CTA TWO',
            value: '#',
          },
          buttonStyle: {
            buttonStyle: 'solid',
            buttonSize: 'large',
            buttonColor: 'secondary',
          },
        },
      ],
      videoBackground: [
        {
          nonVimeoVideo: {
            desktop: {
              url: 'https://onol.wip.prod.gaptecholapps.com/Asset_Archive/videocampaign/SVH_Video_1440x800.mp4',
              fallbackImage: [
                {
                  image: {
                    _meta: {
                      schema: 'http://bigcontent.io/cms/schema/v1/core#/definitions/image-link',
                    },
                    id: '44de475f-9944-48a8-b20b-552aa16cfbbf',
                    name: 'gdpcat3',
                    endpoint: 'athleta',
                    defaultHost: '2hmc8flz4ofg1dy89cc38f2xm.staging.bigcontent.io',
                    mimeType: 'image/jpeg',
                  },
                  altText: 'nonvimeodesktopfallbackimg',
                  variations: [
                    {
                      variation: 'desktop',
                    },
                    {
                      variation: 'mobile',
                    },
                  ],
                  fliph: false,
                  flipv: false,
                  enableChroma: false,
                  chromaQuality: 80,
                },
              ],
            },
            mobile: {
              url: 'https://onol.wip.prod.gaptecholapps.com/Asset_Archive/videocampaign/SVH_Video_1440x800.mp4',
              fallbackImage: [
                {
                  image: {
                    _meta: {
                      schema: 'http://bigcontent.io/cms/schema/v1/core#/definitions/image-link',
                    },
                    id: 'd3c6a769-d9f8-44a7-816b-3702f272b62d',
                    name: 'Card 3 - MobileL',
                    endpoint: 'athleta',
                    defaultHost: '2hmc8flz4ofg1dy89cc38f2xm.staging.bigcontent.io',
                    mimeType: 'image/png',
                  },
                  altText: 'nonvimeomobilefallbackimg',
                  variations: [
                    {
                      variation: 'desktop',
                    },
                    {
                      variation: 'mobile',
                    },
                  ],
                  fliph: false,
                  flipv: false,
                  enableChroma: false,
                  chromaQuality: 80,
                },
              ],
            },
          },
        },
      ],
      imageOverlays: {
        handle: {
          placement: 'left',
        },
        detailsLink: {
          fontColor: 'primary',
          label: 'Details view',
          prefixLabel: 'Exclutions apply',
          pemoleCode: '1032197',
          htmlModalUrl: 'https://cdn.c1.amplience.net/c/oldnavyprod/rest_details_070823',
        },
        useGradientBackfill: false,
      },
    },
    {
      ...defaultFrame,
      contentJustification: 'center',
      verticalAlignment: 'bottom',
      spotlightText: {
        useGradientBackfill: false,
        defaultText: '<p class="amp-cms--p" style="text-align:left;"><span class="amp-cms--body-1" style="color:#FFFFFF">Tenth Card</span></p>',
        mobileOverride: '<p class="amp-cms--p" style="text-align:left;"><span class="amp-cms--body-1 style="color:#FFFFFF"">Tenth Mobile</span></p>',
      },
      ctaButtons: [
        {
          cta: {
            label: 'CTA ONE',
            value: '#',
          },
          buttonStyle: {
            buttonStyle: 'underline',
            buttonSize: 'large',
            buttonColor: 'secondary',
          },
        },
        {
          cta: {
            label: 'CTA TWO',
            value: '#',
          },
          buttonStyle: {
            buttonStyle: 'underline',
            buttonSize: 'large',
            buttonColor: 'secondary',
          },
        },
      ],
      imageOverlays: {
        handle: {
          placement: 'left',
        },
        detailsLink: {
          fontColor: 'secondary',
          label: 'details',
          prefixLabel: 'exclutions apply',
          pemoleCode: '1032197',
          htmlModalUrl: 'https://cdn.c1.amplience.net/c/oldnavyprod/rest_details_070823',
        },
        useGradientBackfill: false,
      },
    },
  ],
};

export const defaultFourFramesFullbleedPadded: FeaturedCategoriesContentType = {
  ...defaultFourFramesFullBleed,
  general: {
    ...defaultFourFramesFullBleed.general,
    layout: 'fullbleedPadded',
  },
};

export const defaultFourFramesInset: FeaturedCategoriesContentType = {
  ...defaultFourFramesFullBleed,
  general: { ...defaultFourFramesFullBleed.general, layout: 'inset' },
};

const underlineButtonStyle: BRButtonStyle = {
  buttonStyle: 'underline',
  buttonSize: 'large',
  buttonColor: 'secondary',
};

const underlineCtas: Frame['ctaButtons'] = [
  {
    cta: {
      label: 'Shop Women',
      value: 'https://bananarepublic.gap.com/',
    },
    buttonStyle: underlineButtonStyle,
  },
  {
    cta: {
      label: 'Shop Men',
      value: 'https://bananarepublic.gap.com/',
    },
    buttonStyle: underlineButtonStyle,
  },
];

export const smallMockExample1WithHeading: FeaturedCategoriesContentType = {
  ...defaultTestDataBR,
  general: {
    ...defaultTestDataBR.general,
    layout: 'fullBleed',
    size: 'small',
    sectionHeader: '<p class="amp-cms--p" style="text-align:left;"><span class="amp-cms--headline-6">First Look: August Favorites</span></p>',
  },
  frame: [
    {
      ...defaultFrame,
      contentJustification: 'center',
      verticalAlignment: 'bottom',
      spotlightText: {
        defaultText: '<p class="amp-cms--p" style="text-align:left;"><span class="amp-cms--headline-6" style="color:#FFF">Sweaters</span></p>',
      },
      ctaButtons: underlineCtas,
      image: {
        mainImage: [
          {
            image: {
              _meta: {
                schema: 'http://bigcontent.io/cms/schema/v1/core#/definitions/image-link',
              },
              id: '1d139eea-a505-419a-88c4-58f6e61273d1',
              name: '8f72408c0243a891e916f13ab23cf14a_1',
              endpoint: 'bananarepublic',
              defaultHost: '1oaaw8uqqaz0o1u33g36rq1glc.staging.bigcontent.io',
              mimeType: 'image/png',
            },
            variations: [
              {
                variation: 'desktop',
              },
              {
                variation: 'mobile',
              },
            ],
            fliph: false,
            flipv: false,
            enableChroma: false,
            chromaQuality: 80,
          },
        ],
      },
    },
    {
      ...defaultFrame,
      contentJustification: 'center',
      verticalAlignment: 'bottom',
      spotlightText: {
        defaultText: '<p class="amp-cms--p" style="text-align:left;"><span class="amp-cms--headline-6" style="color:#FFF">Tailoring</span></p>',
      },
      ctaButtons: underlineCtas,
      image: {
        mainImage: [
          {
            image: {
              _meta: {
                schema: 'http://bigcontent.io/cms/schema/v1/core#/definitions/image-link',
              },
              id: '5139df1a-c60e-4791-ab09-3c66360d62c6',
              name: '8f72408c0243a891e916f13ab23cf14a2',
              endpoint: 'bananarepublic',
              defaultHost: '1oaaw8uqqaz0o1u33g36rq1glc.staging.bigcontent.io',
              mimeType: 'image/png',
            },
            variations: [
              {
                variation: 'desktop',
              },
              {
                variation: 'mobile',
              },
            ],
            fliph: false,
            flipv: false,
            enableChroma: false,
            chromaQuality: 80,
          },
        ],
      },
    },
    {
      ...defaultFrame,
      contentJustification: 'center',
      verticalAlignment: 'bottom',
      spotlightText: {
        defaultText: '<p class="amp-cms--p" style="text-align:left;"><span class="amp-cms--headline-6" style="color:#FFF">Accessories</span></p>',
      },
      ctaButtons: underlineCtas,
      image: {
        mainImage: [
          {
            image: {
              _meta: {
                schema: 'http://bigcontent.io/cms/schema/v1/core#/definitions/image-link',
              },
              id: '6733aefe-043f-4628-810c-67bc4c35329f',
              name: '8f72408c0243a891e916f13ab23cf14a4',
              endpoint: 'bananarepublic',
              defaultHost: '1oaaw8uqqaz0o1u33g36rq1glc.staging.bigcontent.io',
              mimeType: 'image/png',
            },
            variations: [
              {
                variation: 'desktop',
              },
              {
                variation: 'mobile',
              },
            ],
            fliph: false,
            flipv: false,
            enableChroma: false,
            chromaQuality: 80,
          },
        ],
      },
    },
    {
      ...defaultFrame,
      contentJustification: 'center',
      verticalAlignment: 'bottom',
      spotlightText: {
        defaultText: '<p class="amp-cms--p" style="text-align:left;"><span class="amp-cms--headline-6" style="color:#FFF">Shirting</span></p>',
      },
      ctaButtons: underlineCtas,
      image: {
        mainImage: [
          {
            image: {
              _meta: {
                schema: 'http://bigcontent.io/cms/schema/v1/core#/definitions/image-link',
              },
              id: '8edd2d08-694d-474b-a240-ea87a3ea4fc3',
              name: '8f72408c0243a891e916f13ab23cf14a3',
              endpoint: 'bananarepublic',
              defaultHost: '1oaaw8uqqaz0o1u33g36rq1glc.staging.bigcontent.io',
              mimeType: 'image/png',
            },
            variations: [
              {
                variation: 'desktop',
              },
              {
                variation: 'mobile',
              },
            ],
            fliph: false,
            flipv: false,
            enableChroma: false,
            chromaQuality: 80,
          },
        ],
      },
    },
  ],
};

export const mediumMockExample1WithHeading: FeaturedCategoriesContentType = {
  ...smallMockExample1WithHeading,
  general: {
    ...smallMockExample1WithHeading.general,
    size: 'medium',
  },
};

export const largeMockExample1WithHeading: FeaturedCategoriesContentType = {
  ...smallMockExample1WithHeading,
  general: {
    ...smallMockExample1WithHeading.general,
    size: 'large',
  },
};

export const mobileCarouselExample: FeaturedCategoriesContentType = {
  _meta: {
    name: 'Featured Categories',
    schema: 'https://cms.gap.com/schema/content/v1/featured-categories.json',
    deliveryId: '7bf0f753-0cf1-481a-a2ec-a7df446c5d32',
  },
  general: {
    layout: 'fullBleed',
    mobileLayoutType: 'carousel',
    size: 'large',
  },
  frame: [defaultFrame, defaultFrame2, defaultFrame3, defaultFrame4],
  carouselSettings: {
    transition: 'slide',
    type: 'clickThrough',
    continuousLoop: false,
    autoplay: {
      delay: 3000,
      pauseOnHover: false,
    },
    animation: {
      speed: 500,
      ease: false,
    },
    styling: {
      controlsIconsColor: 'primary',
      pagination: 'hide',
      hideChevrons: false,
      paginationStyle: 'chevron',
      paginationAlignment: 'left',
    },
  },
  videoControlSettings: {
    displayVideoControls: true,
    audioVideoControlLocation: 'right',
    controlsIconsColor: 'primary',
    autoplay: false,
    singleLoop: true,
    sequentialLoop: false,
    videoDisplaySettings: 'displayOnHover',
    videoSoundIcons: false,
  },
};
