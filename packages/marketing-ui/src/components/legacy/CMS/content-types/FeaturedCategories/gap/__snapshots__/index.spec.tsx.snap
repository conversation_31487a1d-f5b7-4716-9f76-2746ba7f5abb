// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Gap FeaturedCategories full bleed desktop should match snapshots for primary video controls 1`] = `
.emotion-0 {
  height: 100%;
  padding: 0;
}

.emotion-1 {
  margin-bottom: unset;
  margin-left: unset;
  margin: 0 0 30px 35px;
}

.emotion-1 .amp-cms--p {
  padding: 0;
  margin: 0;
  line-height: 0;
}

.emotion-1 .amp-cms--p>span {
  display: inline;
  white-space: break-spaces;
}

.emotion-1 .amp-cms--p a {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  position: relative;
  z-index: 2;
  pointer-events: auto;
}

.emotion-1 .amp-cms--p a:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-1 .amp-cms--legal-copy {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-1 sup {
  vertical-align: top;
  display: inline-block;
  margin-top: -0.25ex;
}

.emotion-1 sub {
  vertical-align: bottom;
  display: inline-block;
  margin-bottom: -0.25ex;
}

.emotion-1 .amp-cms--f-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--f-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--f-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--fn-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--body-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 20px;
  line-height: 1.3;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--body-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 18px;
  line-height: 1.2777777777777777;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--body-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 16px;
  line-height: 1.25;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--body-4 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 14px;
  line-height: 1.2857142857142858;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--body-5 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--eyebrow-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 50px;
  line-height: 0.92;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--eyebrow-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 40px;
  line-height: 1;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--eyebrow-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 35px;
  line-height: 1;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--headline-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 120px;
  line-height: 0.8333333333333334;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--headline-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 110px;
  line-height: 0.8636363636363636;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--headline-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 100px;
  line-height: 0.85;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--headline-4 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 100px;
  line-height: 1.05;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--headline-5 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 80px;
  line-height: 0.9375;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--headline-6 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 70px;
  line-height: 0.9285714285714286;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--headline-7 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 60px;
  line-height: 0.9166666666666666;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--headlineAlt-1 {
  color: #2B2B2B;
  font-size: 114px;
  line-height: 1;
  letter-spacing: -0.4px;
  font-weight: 400;
}

.emotion-1 .amp-cms--headlineAlt-2 {
  color: #2B2B2B;
  font-size: 84px;
  line-height: 1;
  letter-spacing: -0.4px;
  font-weight: 400;
}

.emotion-1 .amp-cms--headlineAlt-3 {
  color: #2B2B2B;
  font-size: 64px;
  line-height: 1;
  letter-spacing: -0.4px;
  font-weight: 400;
}

.emotion-1 .amp-cms--headlineAlt-4 {
  color: #2B2B2B;
  font-size: 34px;
  line-height: 1;
  letter-spacing: -0.4px;
  font-weight: 400;
}

.emotion-1 .amp-cms--headlineAlt-5 {
  color: #2B2B2B;
  font-size: 16px;
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-1 .amp-cms--headlineAlt-6 {
  color: #2B2B2B;
  font-size: 16px;
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-1 .amp-cms--headlineAlt-7 {
  color: #2B2B2B;
  font-size: 16px;
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-1 .amp-cms--promo-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 100px;
  line-height: 1;
  letter-spacing: 0px;
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-1 .amp-cms--promo-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 80px;
  line-height: 1;
  letter-spacing: 0px;
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-1 .amp-cms--promoAlt-1 {
  color: #2B2B2B;
  font-size: 16px;
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 600;
}

.emotion-1 .amp-cms--subhead-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 30px;
  line-height: 1;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--subhead-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 25px;
  line-height: 1;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--subhead-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 20px;
  line-height: 1.1;
  letter-spacing: 0;
}

.emotion-2 {
  padding: 0;
}

.emotion-3 {
  width: 100%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-flex-wrap: nowrap;
  -webkit-flex-wrap: nowrap;
  -ms-flex-wrap: nowrap;
  flex-wrap: nowrap;
}

.emotion-4 {
  position: relative;
  width: 100%;
}

.emotion-5 {
  cursor: pointer;
  height: 100%;
  width: 100%;
}

.emotion-6 {
  position: relative;
  height: min(56.25vw, 810px)!important;
  pointer-events: auto;
  width: 100%!important;
  -webkit-background-size: cover;
  background-size: cover;
}

.emotion-6>div>img {
  height: min(56.25vw, 810px)!important;
}

.emotion-7 {
  width: -webkit-fit-content;
  width: -moz-fit-content;
  width: fit-content;
  position: relative;
  width: 100%;
}

.emotion-8 {
  display: none;
  gap: 15px;
  position: absolute;
  max-width: -webkit-max-content;
  max-width: -moz-max-content;
  max-width: max-content;
  max-height: -webkit-max-content;
  max-height: -moz-max-content;
  max-height: max-content;
  margin: 10px 15px;
  right: 0;
  bottom: 0;
  height: 30px;
}

.emotion-8>button {
  left: 0;
  bottom: 0;
  z-index: 4;
  position: relative;
  width: 30px;
  height: 30px;
}

.emotion-8>div>button {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  left: 0;
  bottom: 0;
  z-index: 4;
  position: relative;
}

.emotion-8>div>div {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  left: 0;
  z-index: 3;
  bottom: 0;
}

.emotion-8>div>div:before {
  left: 0;
  bottom: -30px;
  width: 30px;
  height: 30px;
  border-radius: 0;
}

.emotion-9 {
  background: none;
  border: none;
  padding: 0;
  height: 24px;
  margin: 0;
  position: absolute;
  bottom: 11px;
  right: 112px;
  width: 24px;
  height: 24px;
}

.emotion-9:is(:not(:focus, :active, :hover, :focus-within)) {
  opacity: 0.64;
}

.emotion-9:is(:hover) {
  opacity: 1;
}

@media (min-width: 767px) {
  .emotion-9 {
    right: 120px;
  }
}

.emotion-10 {
  display: inline-block;
  height: 30px;
  width: 30px;
  min-height: 30px;
  min-width: 30px;
}

.emotion-10 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-10 svg:hover rect {
  fill: #031BA1;
}

.emotion-10 svg:hover path {
  fill: #FFFFFF;
}

.emotion-11 {
  box-sizing: border-box;
  background: none;
  border: none;
  padding: 0;
  position: relative;
  width: 100%;
  height: 0px;
}

.emotion-11:focus div {
  opacity: 0.84;
  -webkit-transition: opacity 200ms;
  transition: opacity 200ms;
  border-radius: 20px 20px 0 0;
}

.emotion-11 div {
  border-radius: 20px 20px 0 0;
}

.emotion-11 .keepOpen,
.emotion-11 .staysOpen {
  opacity: 0.84;
  -webkit-transition: opacity 200ms;
  transition: opacity 200ms;
  z-index: 2;
}

.emotion-12 {
  background: none;
  border: none;
  padding: 0;
  height: 24px;
  margin: 0;
  position: absolute;
  bottom: 11px;
  right: 72px;
  z-index: 11;
}

.emotion-12:is(:not(:focus, :active, :hover, :focus-within)) {
  opacity: 0.64;
}

.emotion-12:is(:hover) {
  opacity: 1;
}

@media (min-width: 767px) {
  .emotion-12 {
    right: 80px;
  }
}

.emotion-14 {
  opacity: 0;
  -webkit-transition: opacity 200ms;
  transition: opacity 200ms;
  box-sizing: border-box;
  background: #ffffff;
  border: none;
  padding: 0;
  position: absolute;
  width: 30px;
  height: 60px;
  bottom: 35px;
  right: 80px;
  border-radius: 20px 20px 0 0;
}

.emotion-14:before {
  content: "";
  height: 24px;
  background: #ffffff;
  display: block;
  position: absolute;
  bottom: -24px;
  width: 24px;
  border-bottom-right-radius: 50%;
  border-bottom-left-radius: 50%;
}

.emotion-15 {
  position: absolute;
  z-index: 0;
  width: 100%!important;
  height: min(56.25vw, 810px)!important;
  top: 0;
  pointer-events: none;
  -webkit-background-size: cover;
  background-size: cover;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: end;
  -webkit-box-align: end;
  -ms-flex-align: end;
  align-items: end;
  box-sizing: border-box;
}

.emotion-16 {
  width: 100%!important;
  height: auto!important;
  -webkit-background-size: cover;
  background-size: cover;
  display: -webkit-box!important;
  display: -webkit-flex!important;
  display: -ms-flexbox!important;
  display: flex!important;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: end;
  -webkit-box-align: end;
  -ms-flex-align: end;
  align-items: end;
  box-sizing: border-box;
  padding: 30px 35px;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  gap: 20px;
}

.emotion-17 {
  box-sizing: border-box;
  height: auto!important;
  width: 100%!important;
  display: -webkit-box!important;
  display: -webkit-flex!important;
  display: -ms-flexbox!important;
  display: flex!important;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  gap: 15px;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
}

.emotion-18 .amp-cms--p {
  padding: 0;
  margin: 0;
  line-height: 0;
}

.emotion-18 .amp-cms--p>span {
  display: inline;
  white-space: break-spaces;
}

.emotion-18 .amp-cms--p a {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  position: relative;
  z-index: 2;
  pointer-events: auto;
}

.emotion-18 .amp-cms--p a:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-18 .amp-cms--legal-copy {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, 0.8333333333333334vw);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-18 sup {
  vertical-align: top;
  display: inline-block;
  margin-top: -0.25ex;
}

.emotion-18 sub {
  vertical-align: bottom;
  display: inline-block;
  margin-bottom: -0.25ex;
}

.emotion-18 .amp-cms--f-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 0.8333333333333334vw;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-18 .amp-cms--f-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 0.8333333333333334vw;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-18 .amp-cms--f-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 0.8333333333333334vw;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-18 .amp-cms--fn-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 0.8333333333333334vw;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-18 .amp-cms--body-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, 1.3888888888888888vw);
  line-height: 1.3;
  letter-spacing: 0;
}

.emotion-18 .amp-cms--body-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, 1.25vw);
  line-height: 1.2777777777777777;
  letter-spacing: 0;
}

.emotion-18 .amp-cms--body-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, 1.1111111111111112vw);
  line-height: 1.25;
  letter-spacing: 0;
}

.emotion-18 .amp-cms--body-4 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, 0.9722222222222222vw);
  line-height: 1.2857142857142858;
  letter-spacing: 0;
}

.emotion-18 .amp-cms--body-5 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, 0.8333333333333334vw);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-18 .amp-cms--eyebrow-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(35px, 3.4722222222222223vw);
  line-height: 0.92;
  letter-spacing: 0;
}

.emotion-18 .amp-cms--eyebrow-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(35px, 2.7777777777777777vw);
  line-height: 1;
  letter-spacing: 0;
}

.emotion-18 .amp-cms--eyebrow-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(35px, 2.430555555555556vw);
  line-height: 1;
  letter-spacing: 0;
}

.emotion-18 .amp-cms--headline-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, 8.333333333333332vw);
  line-height: 0.8333333333333334;
  letter-spacing: 0;
}

.emotion-18 .amp-cms--headline-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, 7.638888888888889vw);
  line-height: 0.8636363636363636;
  letter-spacing: 0;
}

.emotion-18 .amp-cms--headline-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, 6.944444444444445vw);
  line-height: 0.85;
  letter-spacing: 0;
}

.emotion-18 .amp-cms--headline-4 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, 6.944444444444445vw);
  line-height: 1.05;
  letter-spacing: 0;
}

.emotion-18 .amp-cms--headline-5 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, 5.555555555555555vw);
  line-height: 0.9375;
  letter-spacing: 0;
}

.emotion-18 .amp-cms--headline-6 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, 4.861111111111112vw);
  line-height: 0.9285714285714286;
  letter-spacing: 0;
}

.emotion-18 .amp-cms--headline-7 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, 4.166666666666666vw);
  line-height: 0.9166666666666666;
  letter-spacing: 0;
}

.emotion-18 .amp-cms--headlineAlt-1 {
  color: #2B2B2B;
  font-size: max(14px, 7.916666666666666vw);
  line-height: 1;
  letter-spacing: -0.027777777777777776vw;
  font-weight: 400;
}

.emotion-18 .amp-cms--headlineAlt-2 {
  color: #2B2B2B;
  font-size: max(14px, 5.833333333333333vw);
  line-height: 1;
  letter-spacing: -0.027777777777777776vw;
  font-weight: 400;
}

.emotion-18 .amp-cms--headlineAlt-3 {
  color: #2B2B2B;
  font-size: max(14px, 4.444444444444445vw);
  line-height: 1;
  letter-spacing: -0.027777777777777776vw;
  font-weight: 400;
}

.emotion-18 .amp-cms--headlineAlt-4 {
  color: #2B2B2B;
  font-size: max(14px, 2.361111111111111vw);
  line-height: 1;
  letter-spacing: -0.027777777777777776vw;
  font-weight: 400;
}

.emotion-18 .amp-cms--headlineAlt-5 {
  color: #2B2B2B;
  font-size: 1.1111111111111112vw;
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-18 .amp-cms--headlineAlt-6 {
  color: #2B2B2B;
  font-size: 1.1111111111111112vw;
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-18 .amp-cms--headlineAlt-7 {
  color: #2B2B2B;
  font-size: 1.1111111111111112vw;
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-18 .amp-cms--promo-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, 6.944444444444445vw);
  line-height: 1;
  letter-spacing: 0vw;
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-18 .amp-cms--promo-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, 5.555555555555555vw);
  line-height: 1;
  letter-spacing: 0vw;
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-18 .amp-cms--promoAlt-1 {
  color: #2B2B2B;
  font-size: 1.1111111111111112vw;
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 600;
}

.emotion-18 .amp-cms--subhead-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, 2.083333333333333vw);
  line-height: 1;
  letter-spacing: 0;
}

.emotion-18 .amp-cms--subhead-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, 1.7361111111111112vw);
  line-height: 1;
  letter-spacing: 0;
}

.emotion-18 .amp-cms--subhead-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, 1.3888888888888888vw);
  line-height: 1.1;
  letter-spacing: 0;
}

<div>
  <div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      class="emotion-0"
    >
      <div
        class="emotion-1"
      >
        <div>
          <p
            class="amp-cms--p"
            style="text-align:left;"
          >
            <span
              class="amp-cms--headline-3"
            >
              OPTIONAL HEADLINE
            </span>
          </p>
        </div>
      </div>
      <div
        class="emotion-2"
      >
        <div
          class="emotion-3"
        >
          <div
            class="emotion-4"
          >
            <a
              aria-label="new arrivals"
              class="emotion-5"
              data-testid="conditional-link"
              href="www.google.com"
              target="_self"
            >
              <div
                class="emotion-6"
              >
                <div
                  class="emotion-7"
                  data-testid="videocomponent-container"
                >
                  <div
                    style="position: relative;"
                  >
                    <img
                      fetchpriority="high"
                      src="https://2hmc8flz4ofg1dy89cc38f2xm.staging.bigcontent.io/i/athleta/gdpcat3?fmt=auto"
                      style="height: 100%; left: 0px; object-fit: cover; position: absolute; top: 0px; width: 100%;"
                    />
                    <h1
                      role="presentation"
                    >
                      ReactPlayer
                    </h1>
                  </div>
                  <div
                    class="player-custom-controls emotion-8"
                    data-testid="player-custom-controls"
                  >
                    <button
                      aria-label="Play"
                      aria-pressed="false"
                      class="emotion-9"
                    >
                      <span
                        aria-hidden="true"
                        class="emotion-10"
                      >
                        <svg
                          fill="none"
                          viewBox="0 0 30 30"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <g
                            clip-path="url(#clip0_8840_12265)"
                            filter="url(#filter0_b_8840_12265)"
                          >
                            <rect
                              fill="rgba(29, 29, 29, 0.3)"
                              height="30"
                              width="30"
                            />
                            <path
                              d="M19.5 15.0008L10.5 20.197V9.80469L19.5 15.0008Z"
                              fill="#FFFFFF"
                            />
                          </g>
                          <defs>
                            <filter
                              color-interpolation-filters="sRGB"
                              filterUnits="userSpaceOnUse"
                              height="106"
                              id="filter0_b_8840_12265"
                              width="106"
                              x="-38"
                              y="-38"
                            >
                              <feflood
                                flood-opacity="0"
                                result="BackgroundImageFix"
                              />
                              <fegaussianblur
                                in="BackgroundImageFix"
                                stdDeviation="19"
                              />
                              <fecomposite
                                in2="SourceAlpha"
                                operator="in"
                                result="effect1_backgroundBlur_8840_12265"
                              />
                              <feblend
                                in="SourceGraphic"
                                in2="effect1_backgroundBlur_8840_12265"
                                mode="normal"
                                result="shape"
                              />
                            </filter>
                            <clippath
                              id="clip0_8840_12265"
                            >
                              <rect
                                fill="#FFFFFF"
                                height="30"
                                width="30"
                              />
                            </clippath>
                          </defs>
                        </svg>
                      </span>
                    </button>
                    <div
                      class="emotion-11"
                      data-testid="styled-mute-controls"
                    >
                      <button
                        aria-label="Mute"
                        aria-pressed="false"
                        class="emotion-12"
                      >
                        <span
                          aria-hidden="true"
                          class="emotion-10"
                        >
                          <svg
                            fill="none"
                            viewBox="0 0 30 30"
                            xmlns="http://www.w3.org/2000/svg"
                          >
                            <g
                              filter="url(#filter0_b_8973_3420)"
                            >
                              <rect
                                fill="rgba(29, 29, 29, 0.3)"
                                height="30"
                                width="30"
                              />
                              <path
                                d="M21.2364 20.9121L7.01953 10.9904L7.77703 9.90515L21.9953 19.8263L21.2364 20.9121Z"
                                fill="#FFFFFF"
                              />
                              <path
                                d="M19.545 20.1809L18.5015 19.3666C19.2034 18.5002 19.6714 17.4682 19.8608 16.3693C19.9912 15.5096 19.9396 14.6322 19.7091 13.7938C19.47 12.8893 19.0628 12.0377 18.5084 11.2842L19.5381 10.4526C20.209 11.3506 20.7013 12.369 20.9883 13.4526C21.2665 14.4693 21.3274 15.5333 21.1675 16.5753C20.9443 17.8963 20.3857 19.1378 19.545 20.1809Z"
                                fill="#FFFFFF"
                              />
                              <path
                                d="M16.8456 19.085L15.7427 18.356C16.1805 17.7303 16.4799 17.019 16.6216 16.2686C16.7633 15.5182 16.7437 14.7459 16.5643 14.0037C16.4105 13.3412 16.1395 12.7116 15.7638 12.1446L16.8262 11.3553C17.3109 12.0657 17.6589 12.8604 17.8525 13.6982C18.0717 14.6165 18.0953 15.5706 17.9218 16.4987C17.7484 17.4267 17.3817 18.3079 16.8456 19.085Z"
                                fill="#FFFFFF"
                              />
                              <path
                                d="M9.89171 16.6854H7.40039V13.7084H9.89171L13.9448 9.98572V20.4076L9.89171 16.6854Z"
                                fill="#FFFFFF"
                              />
                              <path
                                d="M14.1638 20.9094L9.80411 16.9058H7.17822V13.4875H9.80411L14.1638 9.4834V20.9094ZM7.6194 16.4646H9.97572L13.7222 19.9058V10.4875L9.97572 13.9287H7.6194V16.4646Z"
                                fill="#FFFFFF"
                              />
                            </g>
                            <defs>
                              <filter
                                color-interpolation-filters="sRGB"
                                filterUnits="userSpaceOnUse"
                                height="106"
                                id="filter0_b_8973_3420"
                                width="106"
                                x="-38"
                                y="-38"
                              >
                                <feflood
                                  flood-opacity="0"
                                  result="BackgroundImageFix"
                                />
                                <fegaussianblur
                                  in="BackgroundImageFix"
                                  stdDeviation="19"
                                />
                                <fecomposite
                                  in2="SourceAlpha"
                                  operator="in"
                                  result="effect1_backgroundBlur_8973_3420"
                                />
                                <feblend
                                  in="SourceGraphic"
                                  in2="effect1_backgroundBlur_8973_3420"
                                  mode="normal"
                                  result="shape"
                                />
                              </filter>
                            </defs>
                          </svg>
                        </span>
                      </button>
                      <div
                        class="emotion-14"
                        data-testid="styled-input-volume"
                        style="background: rgb(255, 255, 255);"
                      >
                        <div
                          role="presentation"
                        />
                      </div>
                    </div>
                  </div>
                </div>
                <div
                  class="emotion-15"
                >
                  <div
                    class="emotion-16"
                  >
                    <div
                      class="emotion-17"
                    >
                      <div
                        class="emotion-18"
                      >
                        <div>
                          <p
                            class="amp-cms--p"
                            style="text-align:left;"
                          >
                            <span
                              class="amp-cms--subhead-1"
                              style="color:#FFFFFF"
                            >
                              Card
                            </span>
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </a>
          </div>
          <div
            class="emotion-4"
          >
            <a
              aria-label="new arrivals"
              class="emotion-5"
              data-testid="conditional-link"
              href="www.google.com"
              target="_self"
            >
              <div
                class="emotion-6"
              >
                <div
                  class="emotion-7"
                  data-testid="videocomponent-container"
                >
                  <div
                    style="position: relative;"
                  >
                    <img
                      fetchpriority="high"
                      src="https://2hmc8flz4ofg1dy89cc38f2xm.staging.bigcontent.io/i/athleta/gdpcat3?fmt=auto"
                      style="height: 100%; left: 0px; object-fit: cover; position: absolute; top: 0px; width: 100%;"
                    />
                    <h1
                      role="presentation"
                    >
                      ReactPlayer
                    </h1>
                  </div>
                  <div
                    class="player-custom-controls emotion-8"
                    data-testid="player-custom-controls"
                  >
                    <button
                      aria-label="Play"
                      aria-pressed="false"
                      class="emotion-9"
                    >
                      <span
                        aria-hidden="true"
                        class="emotion-10"
                      >
                        <svg
                          fill="none"
                          viewBox="0 0 30 30"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <g
                            clip-path="url(#clip0_8840_12265)"
                            filter="url(#filter0_b_8840_12265)"
                          >
                            <rect
                              fill="rgba(29, 29, 29, 0.3)"
                              height="30"
                              width="30"
                            />
                            <path
                              d="M19.5 15.0008L10.5 20.197V9.80469L19.5 15.0008Z"
                              fill="#FFFFFF"
                            />
                          </g>
                          <defs>
                            <filter
                              color-interpolation-filters="sRGB"
                              filterUnits="userSpaceOnUse"
                              height="106"
                              id="filter0_b_8840_12265"
                              width="106"
                              x="-38"
                              y="-38"
                            >
                              <feflood
                                flood-opacity="0"
                                result="BackgroundImageFix"
                              />
                              <fegaussianblur
                                in="BackgroundImageFix"
                                stdDeviation="19"
                              />
                              <fecomposite
                                in2="SourceAlpha"
                                operator="in"
                                result="effect1_backgroundBlur_8840_12265"
                              />
                              <feblend
                                in="SourceGraphic"
                                in2="effect1_backgroundBlur_8840_12265"
                                mode="normal"
                                result="shape"
                              />
                            </filter>
                            <clippath
                              id="clip0_8840_12265"
                            >
                              <rect
                                fill="#FFFFFF"
                                height="30"
                                width="30"
                              />
                            </clippath>
                          </defs>
                        </svg>
                      </span>
                    </button>
                    <div
                      class="emotion-11"
                      data-testid="styled-mute-controls"
                    >
                      <button
                        aria-label="Mute"
                        aria-pressed="false"
                        class="emotion-12"
                      >
                        <span
                          aria-hidden="true"
                          class="emotion-10"
                        >
                          <svg
                            fill="none"
                            viewBox="0 0 30 30"
                            xmlns="http://www.w3.org/2000/svg"
                          >
                            <g
                              filter="url(#filter0_b_8973_3420)"
                            >
                              <rect
                                fill="rgba(29, 29, 29, 0.3)"
                                height="30"
                                width="30"
                              />
                              <path
                                d="M21.2364 20.9121L7.01953 10.9904L7.77703 9.90515L21.9953 19.8263L21.2364 20.9121Z"
                                fill="#FFFFFF"
                              />
                              <path
                                d="M19.545 20.1809L18.5015 19.3666C19.2034 18.5002 19.6714 17.4682 19.8608 16.3693C19.9912 15.5096 19.9396 14.6322 19.7091 13.7938C19.47 12.8893 19.0628 12.0377 18.5084 11.2842L19.5381 10.4526C20.209 11.3506 20.7013 12.369 20.9883 13.4526C21.2665 14.4693 21.3274 15.5333 21.1675 16.5753C20.9443 17.8963 20.3857 19.1378 19.545 20.1809Z"
                                fill="#FFFFFF"
                              />
                              <path
                                d="M16.8456 19.085L15.7427 18.356C16.1805 17.7303 16.4799 17.019 16.6216 16.2686C16.7633 15.5182 16.7437 14.7459 16.5643 14.0037C16.4105 13.3412 16.1395 12.7116 15.7638 12.1446L16.8262 11.3553C17.3109 12.0657 17.6589 12.8604 17.8525 13.6982C18.0717 14.6165 18.0953 15.5706 17.9218 16.4987C17.7484 17.4267 17.3817 18.3079 16.8456 19.085Z"
                                fill="#FFFFFF"
                              />
                              <path
                                d="M9.89171 16.6854H7.40039V13.7084H9.89171L13.9448 9.98572V20.4076L9.89171 16.6854Z"
                                fill="#FFFFFF"
                              />
                              <path
                                d="M14.1638 20.9094L9.80411 16.9058H7.17822V13.4875H9.80411L14.1638 9.4834V20.9094ZM7.6194 16.4646H9.97572L13.7222 19.9058V10.4875L9.97572 13.9287H7.6194V16.4646Z"
                                fill="#FFFFFF"
                              />
                            </g>
                            <defs>
                              <filter
                                color-interpolation-filters="sRGB"
                                filterUnits="userSpaceOnUse"
                                height="106"
                                id="filter0_b_8973_3420"
                                width="106"
                                x="-38"
                                y="-38"
                              >
                                <feflood
                                  flood-opacity="0"
                                  result="BackgroundImageFix"
                                />
                                <fegaussianblur
                                  in="BackgroundImageFix"
                                  stdDeviation="19"
                                />
                                <fecomposite
                                  in2="SourceAlpha"
                                  operator="in"
                                  result="effect1_backgroundBlur_8973_3420"
                                />
                                <feblend
                                  in="SourceGraphic"
                                  in2="effect1_backgroundBlur_8973_3420"
                                  mode="normal"
                                  result="shape"
                                />
                              </filter>
                            </defs>
                          </svg>
                        </span>
                      </button>
                      <div
                        class="emotion-14"
                        data-testid="styled-input-volume"
                        style="background: rgb(255, 255, 255);"
                      >
                        <div
                          role="presentation"
                        />
                      </div>
                    </div>
                  </div>
                </div>
                <div
                  class="emotion-15"
                >
                  <div
                    class="emotion-16"
                  >
                    <div
                      class="emotion-17"
                    >
                      <div
                        class="emotion-18"
                      >
                        <div>
                          <p
                            class="amp-cms--p"
                            style="text-align:left;"
                          >
                            <span
                              class="amp-cms--subhead-1"
                              style="color:#FFFFFF"
                            >
                              Card
                            </span>
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </a>
          </div>
          <div
            class="emotion-4"
          >
            <a
              aria-label="new arrivals"
              class="emotion-5"
              data-testid="conditional-link"
              href="www.google.com"
              target="_self"
            >
              <div
                class="emotion-6"
              >
                <div
                  class="emotion-7"
                  data-testid="videocomponent-container"
                >
                  <div
                    style="position: relative;"
                  >
                    <img
                      fetchpriority="high"
                      src="https://2hmc8flz4ofg1dy89cc38f2xm.staging.bigcontent.io/i/athleta/gdpcat3?fmt=auto"
                      style="height: 100%; left: 0px; object-fit: cover; position: absolute; top: 0px; width: 100%;"
                    />
                    <h1
                      role="presentation"
                    >
                      ReactPlayer
                    </h1>
                  </div>
                  <div
                    class="player-custom-controls emotion-8"
                    data-testid="player-custom-controls"
                  >
                    <button
                      aria-label="Play"
                      aria-pressed="false"
                      class="emotion-9"
                    >
                      <span
                        aria-hidden="true"
                        class="emotion-10"
                      >
                        <svg
                          fill="none"
                          viewBox="0 0 30 30"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <g
                            clip-path="url(#clip0_8840_12265)"
                            filter="url(#filter0_b_8840_12265)"
                          >
                            <rect
                              fill="rgba(29, 29, 29, 0.3)"
                              height="30"
                              width="30"
                            />
                            <path
                              d="M19.5 15.0008L10.5 20.197V9.80469L19.5 15.0008Z"
                              fill="#FFFFFF"
                            />
                          </g>
                          <defs>
                            <filter
                              color-interpolation-filters="sRGB"
                              filterUnits="userSpaceOnUse"
                              height="106"
                              id="filter0_b_8840_12265"
                              width="106"
                              x="-38"
                              y="-38"
                            >
                              <feflood
                                flood-opacity="0"
                                result="BackgroundImageFix"
                              />
                              <fegaussianblur
                                in="BackgroundImageFix"
                                stdDeviation="19"
                              />
                              <fecomposite
                                in2="SourceAlpha"
                                operator="in"
                                result="effect1_backgroundBlur_8840_12265"
                              />
                              <feblend
                                in="SourceGraphic"
                                in2="effect1_backgroundBlur_8840_12265"
                                mode="normal"
                                result="shape"
                              />
                            </filter>
                            <clippath
                              id="clip0_8840_12265"
                            >
                              <rect
                                fill="#FFFFFF"
                                height="30"
                                width="30"
                              />
                            </clippath>
                          </defs>
                        </svg>
                      </span>
                    </button>
                    <div
                      class="emotion-11"
                      data-testid="styled-mute-controls"
                    >
                      <button
                        aria-label="Mute"
                        aria-pressed="false"
                        class="emotion-12"
                      >
                        <span
                          aria-hidden="true"
                          class="emotion-10"
                        >
                          <svg
                            fill="none"
                            viewBox="0 0 30 30"
                            xmlns="http://www.w3.org/2000/svg"
                          >
                            <g
                              filter="url(#filter0_b_8973_3420)"
                            >
                              <rect
                                fill="rgba(29, 29, 29, 0.3)"
                                height="30"
                                width="30"
                              />
                              <path
                                d="M21.2364 20.9121L7.01953 10.9904L7.77703 9.90515L21.9953 19.8263L21.2364 20.9121Z"
                                fill="#FFFFFF"
                              />
                              <path
                                d="M19.545 20.1809L18.5015 19.3666C19.2034 18.5002 19.6714 17.4682 19.8608 16.3693C19.9912 15.5096 19.9396 14.6322 19.7091 13.7938C19.47 12.8893 19.0628 12.0377 18.5084 11.2842L19.5381 10.4526C20.209 11.3506 20.7013 12.369 20.9883 13.4526C21.2665 14.4693 21.3274 15.5333 21.1675 16.5753C20.9443 17.8963 20.3857 19.1378 19.545 20.1809Z"
                                fill="#FFFFFF"
                              />
                              <path
                                d="M16.8456 19.085L15.7427 18.356C16.1805 17.7303 16.4799 17.019 16.6216 16.2686C16.7633 15.5182 16.7437 14.7459 16.5643 14.0037C16.4105 13.3412 16.1395 12.7116 15.7638 12.1446L16.8262 11.3553C17.3109 12.0657 17.6589 12.8604 17.8525 13.6982C18.0717 14.6165 18.0953 15.5706 17.9218 16.4987C17.7484 17.4267 17.3817 18.3079 16.8456 19.085Z"
                                fill="#FFFFFF"
                              />
                              <path
                                d="M9.89171 16.6854H7.40039V13.7084H9.89171L13.9448 9.98572V20.4076L9.89171 16.6854Z"
                                fill="#FFFFFF"
                              />
                              <path
                                d="M14.1638 20.9094L9.80411 16.9058H7.17822V13.4875H9.80411L14.1638 9.4834V20.9094ZM7.6194 16.4646H9.97572L13.7222 19.9058V10.4875L9.97572 13.9287H7.6194V16.4646Z"
                                fill="#FFFFFF"
                              />
                            </g>
                            <defs>
                              <filter
                                color-interpolation-filters="sRGB"
                                filterUnits="userSpaceOnUse"
                                height="106"
                                id="filter0_b_8973_3420"
                                width="106"
                                x="-38"
                                y="-38"
                              >
                                <feflood
                                  flood-opacity="0"
                                  result="BackgroundImageFix"
                                />
                                <fegaussianblur
                                  in="BackgroundImageFix"
                                  stdDeviation="19"
                                />
                                <fecomposite
                                  in2="SourceAlpha"
                                  operator="in"
                                  result="effect1_backgroundBlur_8973_3420"
                                />
                                <feblend
                                  in="SourceGraphic"
                                  in2="effect1_backgroundBlur_8973_3420"
                                  mode="normal"
                                  result="shape"
                                />
                              </filter>
                            </defs>
                          </svg>
                        </span>
                      </button>
                      <div
                        class="emotion-14"
                        data-testid="styled-input-volume"
                        style="background: rgb(255, 255, 255);"
                      >
                        <div
                          role="presentation"
                        />
                      </div>
                    </div>
                  </div>
                </div>
                <div
                  class="emotion-15"
                >
                  <div
                    class="emotion-16"
                  >
                    <div
                      class="emotion-17"
                    >
                      <div
                        class="emotion-18"
                      >
                        <div>
                          <p
                            class="amp-cms--p"
                            style="text-align:left;"
                          >
                            <span
                              class="amp-cms--subhead-1"
                              style="color:#FFFFFF"
                            >
                              Card
                            </span>
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </a>
          </div>
          <div
            class="emotion-4"
          >
            <a
              aria-label="new arrivals"
              class="emotion-5"
              data-testid="conditional-link"
              href="www.google.com"
              target="_self"
            >
              <div
                class="emotion-6"
              >
                <div
                  class="emotion-7"
                  data-testid="videocomponent-container"
                >
                  <div
                    style="position: relative;"
                  >
                    <img
                      fetchpriority="high"
                      src="https://2hmc8flz4ofg1dy89cc38f2xm.staging.bigcontent.io/i/athleta/gdpcat3?fmt=auto"
                      style="height: 100%; left: 0px; object-fit: cover; position: absolute; top: 0px; width: 100%;"
                    />
                    <h1
                      role="presentation"
                    >
                      ReactPlayer
                    </h1>
                  </div>
                  <div
                    class="player-custom-controls emotion-8"
                    data-testid="player-custom-controls"
                  >
                    <button
                      aria-label="Play"
                      aria-pressed="false"
                      class="emotion-9"
                    >
                      <span
                        aria-hidden="true"
                        class="emotion-10"
                      >
                        <svg
                          fill="none"
                          viewBox="0 0 30 30"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <g
                            clip-path="url(#clip0_8840_12265)"
                            filter="url(#filter0_b_8840_12265)"
                          >
                            <rect
                              fill="rgba(29, 29, 29, 0.3)"
                              height="30"
                              width="30"
                            />
                            <path
                              d="M19.5 15.0008L10.5 20.197V9.80469L19.5 15.0008Z"
                              fill="#FFFFFF"
                            />
                          </g>
                          <defs>
                            <filter
                              color-interpolation-filters="sRGB"
                              filterUnits="userSpaceOnUse"
                              height="106"
                              id="filter0_b_8840_12265"
                              width="106"
                              x="-38"
                              y="-38"
                            >
                              <feflood
                                flood-opacity="0"
                                result="BackgroundImageFix"
                              />
                              <fegaussianblur
                                in="BackgroundImageFix"
                                stdDeviation="19"
                              />
                              <fecomposite
                                in2="SourceAlpha"
                                operator="in"
                                result="effect1_backgroundBlur_8840_12265"
                              />
                              <feblend
                                in="SourceGraphic"
                                in2="effect1_backgroundBlur_8840_12265"
                                mode="normal"
                                result="shape"
                              />
                            </filter>
                            <clippath
                              id="clip0_8840_12265"
                            >
                              <rect
                                fill="#FFFFFF"
                                height="30"
                                width="30"
                              />
                            </clippath>
                          </defs>
                        </svg>
                      </span>
                    </button>
                    <div
                      class="emotion-11"
                      data-testid="styled-mute-controls"
                    >
                      <button
                        aria-label="Mute"
                        aria-pressed="false"
                        class="emotion-12"
                      >
                        <span
                          aria-hidden="true"
                          class="emotion-10"
                        >
                          <svg
                            fill="none"
                            viewBox="0 0 30 30"
                            xmlns="http://www.w3.org/2000/svg"
                          >
                            <g
                              filter="url(#filter0_b_8973_3420)"
                            >
                              <rect
                                fill="rgba(29, 29, 29, 0.3)"
                                height="30"
                                width="30"
                              />
                              <path
                                d="M21.2364 20.9121L7.01953 10.9904L7.77703 9.90515L21.9953 19.8263L21.2364 20.9121Z"
                                fill="#FFFFFF"
                              />
                              <path
                                d="M19.545 20.1809L18.5015 19.3666C19.2034 18.5002 19.6714 17.4682 19.8608 16.3693C19.9912 15.5096 19.9396 14.6322 19.7091 13.7938C19.47 12.8893 19.0628 12.0377 18.5084 11.2842L19.5381 10.4526C20.209 11.3506 20.7013 12.369 20.9883 13.4526C21.2665 14.4693 21.3274 15.5333 21.1675 16.5753C20.9443 17.8963 20.3857 19.1378 19.545 20.1809Z"
                                fill="#FFFFFF"
                              />
                              <path
                                d="M16.8456 19.085L15.7427 18.356C16.1805 17.7303 16.4799 17.019 16.6216 16.2686C16.7633 15.5182 16.7437 14.7459 16.5643 14.0037C16.4105 13.3412 16.1395 12.7116 15.7638 12.1446L16.8262 11.3553C17.3109 12.0657 17.6589 12.8604 17.8525 13.6982C18.0717 14.6165 18.0953 15.5706 17.9218 16.4987C17.7484 17.4267 17.3817 18.3079 16.8456 19.085Z"
                                fill="#FFFFFF"
                              />
                              <path
                                d="M9.89171 16.6854H7.40039V13.7084H9.89171L13.9448 9.98572V20.4076L9.89171 16.6854Z"
                                fill="#FFFFFF"
                              />
                              <path
                                d="M14.1638 20.9094L9.80411 16.9058H7.17822V13.4875H9.80411L14.1638 9.4834V20.9094ZM7.6194 16.4646H9.97572L13.7222 19.9058V10.4875L9.97572 13.9287H7.6194V16.4646Z"
                                fill="#FFFFFF"
                              />
                            </g>
                            <defs>
                              <filter
                                color-interpolation-filters="sRGB"
                                filterUnits="userSpaceOnUse"
                                height="106"
                                id="filter0_b_8973_3420"
                                width="106"
                                x="-38"
                                y="-38"
                              >
                                <feflood
                                  flood-opacity="0"
                                  result="BackgroundImageFix"
                                />
                                <fegaussianblur
                                  in="BackgroundImageFix"
                                  stdDeviation="19"
                                />
                                <fecomposite
                                  in2="SourceAlpha"
                                  operator="in"
                                  result="effect1_backgroundBlur_8973_3420"
                                />
                                <feblend
                                  in="SourceGraphic"
                                  in2="effect1_backgroundBlur_8973_3420"
                                  mode="normal"
                                  result="shape"
                                />
                              </filter>
                            </defs>
                          </svg>
                        </span>
                      </button>
                      <div
                        class="emotion-14"
                        data-testid="styled-input-volume"
                        style="background: rgb(255, 255, 255);"
                      >
                        <div
                          role="presentation"
                        />
                      </div>
                    </div>
                  </div>
                </div>
                <div
                  class="emotion-15"
                >
                  <div
                    class="emotion-16"
                  >
                    <div
                      class="emotion-17"
                    >
                      <div
                        class="emotion-18"
                      >
                        <div>
                          <p
                            class="amp-cms--p"
                            style="text-align:left;"
                          >
                            <span
                              class="amp-cms--subhead-1"
                              style="color:#FFFFFF"
                            >
                              Card
                            </span>
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </a>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`Gap FeaturedCategories full bleed desktop should match snapshots for secondary video controls 1`] = `
.emotion-0 {
  height: 100%;
  padding: 0;
}

.emotion-1 {
  margin-bottom: unset;
  margin-left: unset;
  margin: 0 0 30px 35px;
}

.emotion-1 .amp-cms--p {
  padding: 0;
  margin: 0;
  line-height: 0;
}

.emotion-1 .amp-cms--p>span {
  display: inline;
  white-space: break-spaces;
}

.emotion-1 .amp-cms--p a {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  position: relative;
  z-index: 2;
  pointer-events: auto;
}

.emotion-1 .amp-cms--p a:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-1 .amp-cms--legal-copy {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-1 sup {
  vertical-align: top;
  display: inline-block;
  margin-top: -0.25ex;
}

.emotion-1 sub {
  vertical-align: bottom;
  display: inline-block;
  margin-bottom: -0.25ex;
}

.emotion-1 .amp-cms--f-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--f-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--f-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--fn-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--body-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 20px;
  line-height: 1.3;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--body-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 18px;
  line-height: 1.2777777777777777;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--body-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 16px;
  line-height: 1.25;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--body-4 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 14px;
  line-height: 1.2857142857142858;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--body-5 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--eyebrow-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 50px;
  line-height: 0.92;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--eyebrow-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 40px;
  line-height: 1;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--eyebrow-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 35px;
  line-height: 1;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--headline-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 120px;
  line-height: 0.8333333333333334;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--headline-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 110px;
  line-height: 0.8636363636363636;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--headline-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 100px;
  line-height: 0.85;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--headline-4 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 100px;
  line-height: 1.05;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--headline-5 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 80px;
  line-height: 0.9375;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--headline-6 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 70px;
  line-height: 0.9285714285714286;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--headline-7 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 60px;
  line-height: 0.9166666666666666;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--headlineAlt-1 {
  color: #2B2B2B;
  font-size: 114px;
  line-height: 1;
  letter-spacing: -0.4px;
  font-weight: 400;
}

.emotion-1 .amp-cms--headlineAlt-2 {
  color: #2B2B2B;
  font-size: 84px;
  line-height: 1;
  letter-spacing: -0.4px;
  font-weight: 400;
}

.emotion-1 .amp-cms--headlineAlt-3 {
  color: #2B2B2B;
  font-size: 64px;
  line-height: 1;
  letter-spacing: -0.4px;
  font-weight: 400;
}

.emotion-1 .amp-cms--headlineAlt-4 {
  color: #2B2B2B;
  font-size: 34px;
  line-height: 1;
  letter-spacing: -0.4px;
  font-weight: 400;
}

.emotion-1 .amp-cms--headlineAlt-5 {
  color: #2B2B2B;
  font-size: 16px;
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-1 .amp-cms--headlineAlt-6 {
  color: #2B2B2B;
  font-size: 16px;
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-1 .amp-cms--headlineAlt-7 {
  color: #2B2B2B;
  font-size: 16px;
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-1 .amp-cms--promo-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 100px;
  line-height: 1;
  letter-spacing: 0px;
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-1 .amp-cms--promo-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 80px;
  line-height: 1;
  letter-spacing: 0px;
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-1 .amp-cms--promoAlt-1 {
  color: #2B2B2B;
  font-size: 16px;
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 600;
}

.emotion-1 .amp-cms--subhead-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 30px;
  line-height: 1;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--subhead-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 25px;
  line-height: 1;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--subhead-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 20px;
  line-height: 1.1;
  letter-spacing: 0;
}

.emotion-2 {
  padding: 0;
}

.emotion-3 {
  width: 100%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-flex-wrap: nowrap;
  -webkit-flex-wrap: nowrap;
  -ms-flex-wrap: nowrap;
  flex-wrap: nowrap;
}

.emotion-4 {
  position: relative;
  width: 100%;
}

.emotion-5 {
  cursor: pointer;
  height: 100%;
  width: 100%;
}

.emotion-6 {
  position: relative;
  height: min(56.25vw, 810px)!important;
  pointer-events: auto;
  width: 100%!important;
  -webkit-background-size: cover;
  background-size: cover;
}

.emotion-6>div>img {
  height: min(56.25vw, 810px)!important;
}

.emotion-7 {
  width: -webkit-fit-content;
  width: -moz-fit-content;
  width: fit-content;
  position: relative;
  width: 100%;
}

.emotion-8 {
  display: none;
  gap: 15px;
  position: absolute;
  max-width: -webkit-max-content;
  max-width: -moz-max-content;
  max-width: max-content;
  max-height: -webkit-max-content;
  max-height: -moz-max-content;
  max-height: max-content;
  margin: 10px 15px;
  right: 0;
  bottom: 0;
  height: 30px;
}

.emotion-8>button {
  left: 0;
  bottom: 0;
  z-index: 4;
  position: relative;
  width: 30px;
  height: 30px;
}

.emotion-8>div>button {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  left: 0;
  bottom: 0;
  z-index: 4;
  position: relative;
}

.emotion-8>div>div {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  left: 0;
  z-index: 3;
  bottom: 0;
}

.emotion-8>div>div:before {
  left: 0;
  bottom: -30px;
  width: 30px;
  height: 30px;
  border-radius: 0;
}

.emotion-9 {
  background: none;
  border: none;
  padding: 0;
  height: 24px;
  margin: 0;
  position: absolute;
  bottom: 11px;
  right: 112px;
  width: 24px;
  height: 24px;
}

.emotion-9:is(:not(:focus, :active, :hover, :focus-within)) {
  opacity: 0.64;
}

.emotion-9:is(:hover) {
  opacity: 1;
}

@media (min-width: 767px) {
  .emotion-9 {
    right: 120px;
  }
}

.emotion-10 {
  display: inline-block;
  height: 30px;
  width: 30px;
  min-height: 30px;
  min-width: 30px;
}

.emotion-10 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-10 svg:hover rect {
  fill: #031BA1;
}

.emotion-10 svg:hover path {
  fill: #FFFFFF;
}

.emotion-11 {
  box-sizing: border-box;
  background: none;
  border: none;
  padding: 0;
  position: relative;
  width: 100%;
  height: 0px;
}

.emotion-11:focus div {
  opacity: 0.84;
  -webkit-transition: opacity 200ms;
  transition: opacity 200ms;
  border-radius: 20px 20px 0 0;
}

.emotion-11 div {
  border-radius: 20px 20px 0 0;
}

.emotion-11 .keepOpen,
.emotion-11 .staysOpen {
  opacity: 0.84;
  -webkit-transition: opacity 200ms;
  transition: opacity 200ms;
  z-index: 2;
}

.emotion-12 {
  background: none;
  border: none;
  padding: 0;
  height: 24px;
  margin: 0;
  position: absolute;
  bottom: 11px;
  right: 72px;
  z-index: 11;
}

.emotion-12:is(:not(:focus, :active, :hover, :focus-within)) {
  opacity: 0.64;
}

.emotion-12:is(:hover) {
  opacity: 1;
}

@media (min-width: 767px) {
  .emotion-12 {
    right: 80px;
  }
}

.emotion-14 {
  opacity: 0;
  -webkit-transition: opacity 200ms;
  transition: opacity 200ms;
  box-sizing: border-box;
  background: #ffffff;
  border: none;
  padding: 0;
  position: absolute;
  width: 30px;
  height: 60px;
  bottom: 35px;
  right: 80px;
  border-radius: 20px 20px 0 0;
}

.emotion-14:before {
  content: "";
  height: 24px;
  background: #ffffff;
  display: block;
  position: absolute;
  bottom: -24px;
  width: 24px;
  border-bottom-right-radius: 50%;
  border-bottom-left-radius: 50%;
}

.emotion-15 {
  position: absolute;
  z-index: 0;
  width: 100%!important;
  height: min(56.25vw, 810px)!important;
  top: 0;
  pointer-events: none;
  -webkit-background-size: cover;
  background-size: cover;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: end;
  -webkit-box-align: end;
  -ms-flex-align: end;
  align-items: end;
  box-sizing: border-box;
}

.emotion-16 {
  width: 100%!important;
  height: auto!important;
  -webkit-background-size: cover;
  background-size: cover;
  display: -webkit-box!important;
  display: -webkit-flex!important;
  display: -ms-flexbox!important;
  display: flex!important;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: end;
  -webkit-box-align: end;
  -ms-flex-align: end;
  align-items: end;
  box-sizing: border-box;
  padding: 30px 35px;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  gap: 20px;
}

.emotion-17 {
  box-sizing: border-box;
  height: auto!important;
  width: 100%!important;
  display: -webkit-box!important;
  display: -webkit-flex!important;
  display: -ms-flexbox!important;
  display: flex!important;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  gap: 15px;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
}

.emotion-18 .amp-cms--p {
  padding: 0;
  margin: 0;
  line-height: 0;
}

.emotion-18 .amp-cms--p>span {
  display: inline;
  white-space: break-spaces;
}

.emotion-18 .amp-cms--p a {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  position: relative;
  z-index: 2;
  pointer-events: auto;
}

.emotion-18 .amp-cms--p a:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-18 .amp-cms--legal-copy {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, 0.8333333333333334vw);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-18 sup {
  vertical-align: top;
  display: inline-block;
  margin-top: -0.25ex;
}

.emotion-18 sub {
  vertical-align: bottom;
  display: inline-block;
  margin-bottom: -0.25ex;
}

.emotion-18 .amp-cms--f-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 0.8333333333333334vw;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-18 .amp-cms--f-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 0.8333333333333334vw;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-18 .amp-cms--f-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 0.8333333333333334vw;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-18 .amp-cms--fn-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 0.8333333333333334vw;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-18 .amp-cms--body-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, 1.3888888888888888vw);
  line-height: 1.3;
  letter-spacing: 0;
}

.emotion-18 .amp-cms--body-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, 1.25vw);
  line-height: 1.2777777777777777;
  letter-spacing: 0;
}

.emotion-18 .amp-cms--body-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, 1.1111111111111112vw);
  line-height: 1.25;
  letter-spacing: 0;
}

.emotion-18 .amp-cms--body-4 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, 0.9722222222222222vw);
  line-height: 1.2857142857142858;
  letter-spacing: 0;
}

.emotion-18 .amp-cms--body-5 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, 0.8333333333333334vw);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-18 .amp-cms--eyebrow-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(35px, 3.4722222222222223vw);
  line-height: 0.92;
  letter-spacing: 0;
}

.emotion-18 .amp-cms--eyebrow-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(35px, 2.7777777777777777vw);
  line-height: 1;
  letter-spacing: 0;
}

.emotion-18 .amp-cms--eyebrow-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(35px, 2.430555555555556vw);
  line-height: 1;
  letter-spacing: 0;
}

.emotion-18 .amp-cms--headline-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, 8.333333333333332vw);
  line-height: 0.8333333333333334;
  letter-spacing: 0;
}

.emotion-18 .amp-cms--headline-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, 7.638888888888889vw);
  line-height: 0.8636363636363636;
  letter-spacing: 0;
}

.emotion-18 .amp-cms--headline-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, 6.944444444444445vw);
  line-height: 0.85;
  letter-spacing: 0;
}

.emotion-18 .amp-cms--headline-4 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, 6.944444444444445vw);
  line-height: 1.05;
  letter-spacing: 0;
}

.emotion-18 .amp-cms--headline-5 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, 5.555555555555555vw);
  line-height: 0.9375;
  letter-spacing: 0;
}

.emotion-18 .amp-cms--headline-6 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, 4.861111111111112vw);
  line-height: 0.9285714285714286;
  letter-spacing: 0;
}

.emotion-18 .amp-cms--headline-7 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, 4.166666666666666vw);
  line-height: 0.9166666666666666;
  letter-spacing: 0;
}

.emotion-18 .amp-cms--headlineAlt-1 {
  color: #2B2B2B;
  font-size: max(14px, 7.916666666666666vw);
  line-height: 1;
  letter-spacing: -0.027777777777777776vw;
  font-weight: 400;
}

.emotion-18 .amp-cms--headlineAlt-2 {
  color: #2B2B2B;
  font-size: max(14px, 5.833333333333333vw);
  line-height: 1;
  letter-spacing: -0.027777777777777776vw;
  font-weight: 400;
}

.emotion-18 .amp-cms--headlineAlt-3 {
  color: #2B2B2B;
  font-size: max(14px, 4.444444444444445vw);
  line-height: 1;
  letter-spacing: -0.027777777777777776vw;
  font-weight: 400;
}

.emotion-18 .amp-cms--headlineAlt-4 {
  color: #2B2B2B;
  font-size: max(14px, 2.361111111111111vw);
  line-height: 1;
  letter-spacing: -0.027777777777777776vw;
  font-weight: 400;
}

.emotion-18 .amp-cms--headlineAlt-5 {
  color: #2B2B2B;
  font-size: 1.1111111111111112vw;
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-18 .amp-cms--headlineAlt-6 {
  color: #2B2B2B;
  font-size: 1.1111111111111112vw;
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-18 .amp-cms--headlineAlt-7 {
  color: #2B2B2B;
  font-size: 1.1111111111111112vw;
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-18 .amp-cms--promo-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, 6.944444444444445vw);
  line-height: 1;
  letter-spacing: 0vw;
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-18 .amp-cms--promo-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, 5.555555555555555vw);
  line-height: 1;
  letter-spacing: 0vw;
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-18 .amp-cms--promoAlt-1 {
  color: #2B2B2B;
  font-size: 1.1111111111111112vw;
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 600;
}

.emotion-18 .amp-cms--subhead-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, 2.083333333333333vw);
  line-height: 1;
  letter-spacing: 0;
}

.emotion-18 .amp-cms--subhead-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, 1.7361111111111112vw);
  line-height: 1;
  letter-spacing: 0;
}

.emotion-18 .amp-cms--subhead-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, 1.3888888888888888vw);
  line-height: 1.1;
  letter-spacing: 0;
}

<div>
  <div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      class="emotion-0"
    >
      <div
        class="emotion-1"
      >
        <div>
          <p
            class="amp-cms--p"
            style="text-align:left;"
          >
            <span
              class="amp-cms--headline-3"
            >
              OPTIONAL HEADLINE
            </span>
          </p>
        </div>
      </div>
      <div
        class="emotion-2"
      >
        <div
          class="emotion-3"
        >
          <div
            class="emotion-4"
          >
            <a
              aria-label="new arrivals"
              class="emotion-5"
              data-testid="conditional-link"
              href="www.google.com"
              target="_self"
            >
              <div
                class="emotion-6"
              >
                <div
                  class="emotion-7"
                  data-testid="videocomponent-container"
                >
                  <div
                    style="position: relative;"
                  >
                    <img
                      fetchpriority="high"
                      src="https://2hmc8flz4ofg1dy89cc38f2xm.staging.bigcontent.io/i/athleta/gdpcat3?fmt=auto"
                      style="height: 100%; left: 0px; object-fit: cover; position: absolute; top: 0px; width: 100%;"
                    />
                    <h1
                      role="presentation"
                    >
                      ReactPlayer
                    </h1>
                  </div>
                  <div
                    class="player-custom-controls emotion-8"
                    data-testid="player-custom-controls"
                  >
                    <button
                      aria-label="Play"
                      aria-pressed="false"
                      class="emotion-9"
                    >
                      <span
                        aria-hidden="true"
                        class="emotion-10"
                      >
                        <svg
                          fill="none"
                          viewBox="0 0 30 30"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <g
                            clip-path="url(#clip0_8840_12265)"
                            filter="url(#filter0_b_8840_12265)"
                          >
                            <rect
                              fill="#F7F7F7"
                              height="30"
                              width="30"
                            />
                            <path
                              d="M19.5 15.0008L10.5 20.197V9.80469L19.5 15.0008Z"
                              fill="#2B2B2B"
                            />
                          </g>
                          <defs>
                            <filter
                              color-interpolation-filters="sRGB"
                              filterUnits="userSpaceOnUse"
                              height="106"
                              id="filter0_b_8840_12265"
                              width="106"
                              x="-38"
                              y="-38"
                            >
                              <feflood
                                flood-opacity="0"
                                result="BackgroundImageFix"
                              />
                              <fegaussianblur
                                in="BackgroundImageFix"
                                stdDeviation="19"
                              />
                              <fecomposite
                                in2="SourceAlpha"
                                operator="in"
                                result="effect1_backgroundBlur_8840_12265"
                              />
                              <feblend
                                in="SourceGraphic"
                                in2="effect1_backgroundBlur_8840_12265"
                                mode="normal"
                                result="shape"
                              />
                            </filter>
                            <clippath
                              id="clip0_8840_12265"
                            >
                              <rect
                                fill="#FFFFFF"
                                height="30"
                                width="30"
                              />
                            </clippath>
                          </defs>
                        </svg>
                      </span>
                    </button>
                    <div
                      class="emotion-11"
                      data-testid="styled-mute-controls"
                    >
                      <button
                        aria-label="Mute"
                        aria-pressed="false"
                        class="emotion-12"
                      >
                        <span
                          aria-hidden="true"
                          class="emotion-10"
                        >
                          <svg
                            fill="none"
                            viewBox="0 0 30 30"
                            xmlns="http://www.w3.org/2000/svg"
                          >
                            <g
                              filter="url(#filter0_b_8973_3420)"
                            >
                              <rect
                                fill="#F7F7F7"
                                height="30"
                                width="30"
                              />
                              <path
                                d="M21.2364 20.9121L7.01953 10.9904L7.77703 9.90515L21.9953 19.8263L21.2364 20.9121Z"
                                fill="#2B2B2B"
                              />
                              <path
                                d="M19.545 20.1809L18.5015 19.3666C19.2034 18.5002 19.6714 17.4682 19.8608 16.3693C19.9912 15.5096 19.9396 14.6322 19.7091 13.7938C19.47 12.8893 19.0628 12.0377 18.5084 11.2842L19.5381 10.4526C20.209 11.3506 20.7013 12.369 20.9883 13.4526C21.2665 14.4693 21.3274 15.5333 21.1675 16.5753C20.9443 17.8963 20.3857 19.1378 19.545 20.1809Z"
                                fill="#2B2B2B"
                              />
                              <path
                                d="M16.8456 19.085L15.7427 18.356C16.1805 17.7303 16.4799 17.019 16.6216 16.2686C16.7633 15.5182 16.7437 14.7459 16.5643 14.0037C16.4105 13.3412 16.1395 12.7116 15.7638 12.1446L16.8262 11.3553C17.3109 12.0657 17.6589 12.8604 17.8525 13.6982C18.0717 14.6165 18.0953 15.5706 17.9218 16.4987C17.7484 17.4267 17.3817 18.3079 16.8456 19.085Z"
                                fill="#2B2B2B"
                              />
                              <path
                                d="M9.89171 16.6854H7.40039V13.7084H9.89171L13.9448 9.98572V20.4076L9.89171 16.6854Z"
                                fill="#2B2B2B"
                              />
                              <path
                                d="M14.1638 20.9094L9.80411 16.9058H7.17822V13.4875H9.80411L14.1638 9.4834V20.9094ZM7.6194 16.4646H9.97572L13.7222 19.9058V10.4875L9.97572 13.9287H7.6194V16.4646Z"
                                fill="#2B2B2B"
                              />
                            </g>
                            <defs>
                              <filter
                                color-interpolation-filters="sRGB"
                                filterUnits="userSpaceOnUse"
                                height="106"
                                id="filter0_b_8973_3420"
                                width="106"
                                x="-38"
                                y="-38"
                              >
                                <feflood
                                  flood-opacity="0"
                                  result="BackgroundImageFix"
                                />
                                <fegaussianblur
                                  in="BackgroundImageFix"
                                  stdDeviation="19"
                                />
                                <fecomposite
                                  in2="SourceAlpha"
                                  operator="in"
                                  result="effect1_backgroundBlur_8973_3420"
                                />
                                <feblend
                                  in="SourceGraphic"
                                  in2="effect1_backgroundBlur_8973_3420"
                                  mode="normal"
                                  result="shape"
                                />
                              </filter>
                            </defs>
                          </svg>
                        </span>
                      </button>
                      <div
                        class="emotion-14"
                        data-testid="styled-input-volume"
                        style="background: rgb(255, 255, 255);"
                      >
                        <div
                          role="presentation"
                        />
                      </div>
                    </div>
                  </div>
                </div>
                <div
                  class="emotion-15"
                >
                  <div
                    class="emotion-16"
                  >
                    <div
                      class="emotion-17"
                    >
                      <div
                        class="emotion-18"
                      >
                        <div>
                          <p
                            class="amp-cms--p"
                            style="text-align:left;"
                          >
                            <span
                              class="amp-cms--subhead-1"
                              style="color:#FFFFFF"
                            >
                              Card
                            </span>
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </a>
          </div>
          <div
            class="emotion-4"
          >
            <a
              aria-label="new arrivals"
              class="emotion-5"
              data-testid="conditional-link"
              href="www.google.com"
              target="_self"
            >
              <div
                class="emotion-6"
              >
                <div
                  class="emotion-7"
                  data-testid="videocomponent-container"
                >
                  <div
                    style="position: relative;"
                  >
                    <img
                      fetchpriority="high"
                      src="https://2hmc8flz4ofg1dy89cc38f2xm.staging.bigcontent.io/i/athleta/gdpcat3?fmt=auto"
                      style="height: 100%; left: 0px; object-fit: cover; position: absolute; top: 0px; width: 100%;"
                    />
                    <h1
                      role="presentation"
                    >
                      ReactPlayer
                    </h1>
                  </div>
                  <div
                    class="player-custom-controls emotion-8"
                    data-testid="player-custom-controls"
                  >
                    <button
                      aria-label="Play"
                      aria-pressed="false"
                      class="emotion-9"
                    >
                      <span
                        aria-hidden="true"
                        class="emotion-10"
                      >
                        <svg
                          fill="none"
                          viewBox="0 0 30 30"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <g
                            clip-path="url(#clip0_8840_12265)"
                            filter="url(#filter0_b_8840_12265)"
                          >
                            <rect
                              fill="#F7F7F7"
                              height="30"
                              width="30"
                            />
                            <path
                              d="M19.5 15.0008L10.5 20.197V9.80469L19.5 15.0008Z"
                              fill="#2B2B2B"
                            />
                          </g>
                          <defs>
                            <filter
                              color-interpolation-filters="sRGB"
                              filterUnits="userSpaceOnUse"
                              height="106"
                              id="filter0_b_8840_12265"
                              width="106"
                              x="-38"
                              y="-38"
                            >
                              <feflood
                                flood-opacity="0"
                                result="BackgroundImageFix"
                              />
                              <fegaussianblur
                                in="BackgroundImageFix"
                                stdDeviation="19"
                              />
                              <fecomposite
                                in2="SourceAlpha"
                                operator="in"
                                result="effect1_backgroundBlur_8840_12265"
                              />
                              <feblend
                                in="SourceGraphic"
                                in2="effect1_backgroundBlur_8840_12265"
                                mode="normal"
                                result="shape"
                              />
                            </filter>
                            <clippath
                              id="clip0_8840_12265"
                            >
                              <rect
                                fill="#FFFFFF"
                                height="30"
                                width="30"
                              />
                            </clippath>
                          </defs>
                        </svg>
                      </span>
                    </button>
                    <div
                      class="emotion-11"
                      data-testid="styled-mute-controls"
                    >
                      <button
                        aria-label="Mute"
                        aria-pressed="false"
                        class="emotion-12"
                      >
                        <span
                          aria-hidden="true"
                          class="emotion-10"
                        >
                          <svg
                            fill="none"
                            viewBox="0 0 30 30"
                            xmlns="http://www.w3.org/2000/svg"
                          >
                            <g
                              filter="url(#filter0_b_8973_3420)"
                            >
                              <rect
                                fill="#F7F7F7"
                                height="30"
                                width="30"
                              />
                              <path
                                d="M21.2364 20.9121L7.01953 10.9904L7.77703 9.90515L21.9953 19.8263L21.2364 20.9121Z"
                                fill="#2B2B2B"
                              />
                              <path
                                d="M19.545 20.1809L18.5015 19.3666C19.2034 18.5002 19.6714 17.4682 19.8608 16.3693C19.9912 15.5096 19.9396 14.6322 19.7091 13.7938C19.47 12.8893 19.0628 12.0377 18.5084 11.2842L19.5381 10.4526C20.209 11.3506 20.7013 12.369 20.9883 13.4526C21.2665 14.4693 21.3274 15.5333 21.1675 16.5753C20.9443 17.8963 20.3857 19.1378 19.545 20.1809Z"
                                fill="#2B2B2B"
                              />
                              <path
                                d="M16.8456 19.085L15.7427 18.356C16.1805 17.7303 16.4799 17.019 16.6216 16.2686C16.7633 15.5182 16.7437 14.7459 16.5643 14.0037C16.4105 13.3412 16.1395 12.7116 15.7638 12.1446L16.8262 11.3553C17.3109 12.0657 17.6589 12.8604 17.8525 13.6982C18.0717 14.6165 18.0953 15.5706 17.9218 16.4987C17.7484 17.4267 17.3817 18.3079 16.8456 19.085Z"
                                fill="#2B2B2B"
                              />
                              <path
                                d="M9.89171 16.6854H7.40039V13.7084H9.89171L13.9448 9.98572V20.4076L9.89171 16.6854Z"
                                fill="#2B2B2B"
                              />
                              <path
                                d="M14.1638 20.9094L9.80411 16.9058H7.17822V13.4875H9.80411L14.1638 9.4834V20.9094ZM7.6194 16.4646H9.97572L13.7222 19.9058V10.4875L9.97572 13.9287H7.6194V16.4646Z"
                                fill="#2B2B2B"
                              />
                            </g>
                            <defs>
                              <filter
                                color-interpolation-filters="sRGB"
                                filterUnits="userSpaceOnUse"
                                height="106"
                                id="filter0_b_8973_3420"
                                width="106"
                                x="-38"
                                y="-38"
                              >
                                <feflood
                                  flood-opacity="0"
                                  result="BackgroundImageFix"
                                />
                                <fegaussianblur
                                  in="BackgroundImageFix"
                                  stdDeviation="19"
                                />
                                <fecomposite
                                  in2="SourceAlpha"
                                  operator="in"
                                  result="effect1_backgroundBlur_8973_3420"
                                />
                                <feblend
                                  in="SourceGraphic"
                                  in2="effect1_backgroundBlur_8973_3420"
                                  mode="normal"
                                  result="shape"
                                />
                              </filter>
                            </defs>
                          </svg>
                        </span>
                      </button>
                      <div
                        class="emotion-14"
                        data-testid="styled-input-volume"
                        style="background: rgb(255, 255, 255);"
                      >
                        <div
                          role="presentation"
                        />
                      </div>
                    </div>
                  </div>
                </div>
                <div
                  class="emotion-15"
                >
                  <div
                    class="emotion-16"
                  >
                    <div
                      class="emotion-17"
                    >
                      <div
                        class="emotion-18"
                      >
                        <div>
                          <p
                            class="amp-cms--p"
                            style="text-align:left;"
                          >
                            <span
                              class="amp-cms--subhead-1"
                              style="color:#FFFFFF"
                            >
                              Card
                            </span>
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </a>
          </div>
          <div
            class="emotion-4"
          >
            <a
              aria-label="new arrivals"
              class="emotion-5"
              data-testid="conditional-link"
              href="www.google.com"
              target="_self"
            >
              <div
                class="emotion-6"
              >
                <div
                  class="emotion-7"
                  data-testid="videocomponent-container"
                >
                  <div
                    style="position: relative;"
                  >
                    <img
                      fetchpriority="high"
                      src="https://2hmc8flz4ofg1dy89cc38f2xm.staging.bigcontent.io/i/athleta/gdpcat3?fmt=auto"
                      style="height: 100%; left: 0px; object-fit: cover; position: absolute; top: 0px; width: 100%;"
                    />
                    <h1
                      role="presentation"
                    >
                      ReactPlayer
                    </h1>
                  </div>
                  <div
                    class="player-custom-controls emotion-8"
                    data-testid="player-custom-controls"
                  >
                    <button
                      aria-label="Play"
                      aria-pressed="false"
                      class="emotion-9"
                    >
                      <span
                        aria-hidden="true"
                        class="emotion-10"
                      >
                        <svg
                          fill="none"
                          viewBox="0 0 30 30"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <g
                            clip-path="url(#clip0_8840_12265)"
                            filter="url(#filter0_b_8840_12265)"
                          >
                            <rect
                              fill="#F7F7F7"
                              height="30"
                              width="30"
                            />
                            <path
                              d="M19.5 15.0008L10.5 20.197V9.80469L19.5 15.0008Z"
                              fill="#2B2B2B"
                            />
                          </g>
                          <defs>
                            <filter
                              color-interpolation-filters="sRGB"
                              filterUnits="userSpaceOnUse"
                              height="106"
                              id="filter0_b_8840_12265"
                              width="106"
                              x="-38"
                              y="-38"
                            >
                              <feflood
                                flood-opacity="0"
                                result="BackgroundImageFix"
                              />
                              <fegaussianblur
                                in="BackgroundImageFix"
                                stdDeviation="19"
                              />
                              <fecomposite
                                in2="SourceAlpha"
                                operator="in"
                                result="effect1_backgroundBlur_8840_12265"
                              />
                              <feblend
                                in="SourceGraphic"
                                in2="effect1_backgroundBlur_8840_12265"
                                mode="normal"
                                result="shape"
                              />
                            </filter>
                            <clippath
                              id="clip0_8840_12265"
                            >
                              <rect
                                fill="#FFFFFF"
                                height="30"
                                width="30"
                              />
                            </clippath>
                          </defs>
                        </svg>
                      </span>
                    </button>
                    <div
                      class="emotion-11"
                      data-testid="styled-mute-controls"
                    >
                      <button
                        aria-label="Mute"
                        aria-pressed="false"
                        class="emotion-12"
                      >
                        <span
                          aria-hidden="true"
                          class="emotion-10"
                        >
                          <svg
                            fill="none"
                            viewBox="0 0 30 30"
                            xmlns="http://www.w3.org/2000/svg"
                          >
                            <g
                              filter="url(#filter0_b_8973_3420)"
                            >
                              <rect
                                fill="#F7F7F7"
                                height="30"
                                width="30"
                              />
                              <path
                                d="M21.2364 20.9121L7.01953 10.9904L7.77703 9.90515L21.9953 19.8263L21.2364 20.9121Z"
                                fill="#2B2B2B"
                              />
                              <path
                                d="M19.545 20.1809L18.5015 19.3666C19.2034 18.5002 19.6714 17.4682 19.8608 16.3693C19.9912 15.5096 19.9396 14.6322 19.7091 13.7938C19.47 12.8893 19.0628 12.0377 18.5084 11.2842L19.5381 10.4526C20.209 11.3506 20.7013 12.369 20.9883 13.4526C21.2665 14.4693 21.3274 15.5333 21.1675 16.5753C20.9443 17.8963 20.3857 19.1378 19.545 20.1809Z"
                                fill="#2B2B2B"
                              />
                              <path
                                d="M16.8456 19.085L15.7427 18.356C16.1805 17.7303 16.4799 17.019 16.6216 16.2686C16.7633 15.5182 16.7437 14.7459 16.5643 14.0037C16.4105 13.3412 16.1395 12.7116 15.7638 12.1446L16.8262 11.3553C17.3109 12.0657 17.6589 12.8604 17.8525 13.6982C18.0717 14.6165 18.0953 15.5706 17.9218 16.4987C17.7484 17.4267 17.3817 18.3079 16.8456 19.085Z"
                                fill="#2B2B2B"
                              />
                              <path
                                d="M9.89171 16.6854H7.40039V13.7084H9.89171L13.9448 9.98572V20.4076L9.89171 16.6854Z"
                                fill="#2B2B2B"
                              />
                              <path
                                d="M14.1638 20.9094L9.80411 16.9058H7.17822V13.4875H9.80411L14.1638 9.4834V20.9094ZM7.6194 16.4646H9.97572L13.7222 19.9058V10.4875L9.97572 13.9287H7.6194V16.4646Z"
                                fill="#2B2B2B"
                              />
                            </g>
                            <defs>
                              <filter
                                color-interpolation-filters="sRGB"
                                filterUnits="userSpaceOnUse"
                                height="106"
                                id="filter0_b_8973_3420"
                                width="106"
                                x="-38"
                                y="-38"
                              >
                                <feflood
                                  flood-opacity="0"
                                  result="BackgroundImageFix"
                                />
                                <fegaussianblur
                                  in="BackgroundImageFix"
                                  stdDeviation="19"
                                />
                                <fecomposite
                                  in2="SourceAlpha"
                                  operator="in"
                                  result="effect1_backgroundBlur_8973_3420"
                                />
                                <feblend
                                  in="SourceGraphic"
                                  in2="effect1_backgroundBlur_8973_3420"
                                  mode="normal"
                                  result="shape"
                                />
                              </filter>
                            </defs>
                          </svg>
                        </span>
                      </button>
                      <div
                        class="emotion-14"
                        data-testid="styled-input-volume"
                        style="background: rgb(255, 255, 255);"
                      >
                        <div
                          role="presentation"
                        />
                      </div>
                    </div>
                  </div>
                </div>
                <div
                  class="emotion-15"
                >
                  <div
                    class="emotion-16"
                  >
                    <div
                      class="emotion-17"
                    >
                      <div
                        class="emotion-18"
                      >
                        <div>
                          <p
                            class="amp-cms--p"
                            style="text-align:left;"
                          >
                            <span
                              class="amp-cms--subhead-1"
                              style="color:#FFFFFF"
                            >
                              Card
                            </span>
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </a>
          </div>
          <div
            class="emotion-4"
          >
            <a
              aria-label="new arrivals"
              class="emotion-5"
              data-testid="conditional-link"
              href="www.google.com"
              target="_self"
            >
              <div
                class="emotion-6"
              >
                <div
                  class="emotion-7"
                  data-testid="videocomponent-container"
                >
                  <div
                    style="position: relative;"
                  >
                    <img
                      fetchpriority="high"
                      src="https://2hmc8flz4ofg1dy89cc38f2xm.staging.bigcontent.io/i/athleta/gdpcat3?fmt=auto"
                      style="height: 100%; left: 0px; object-fit: cover; position: absolute; top: 0px; width: 100%;"
                    />
                    <h1
                      role="presentation"
                    >
                      ReactPlayer
                    </h1>
                  </div>
                  <div
                    class="player-custom-controls emotion-8"
                    data-testid="player-custom-controls"
                  >
                    <button
                      aria-label="Play"
                      aria-pressed="false"
                      class="emotion-9"
                    >
                      <span
                        aria-hidden="true"
                        class="emotion-10"
                      >
                        <svg
                          fill="none"
                          viewBox="0 0 30 30"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <g
                            clip-path="url(#clip0_8840_12265)"
                            filter="url(#filter0_b_8840_12265)"
                          >
                            <rect
                              fill="#F7F7F7"
                              height="30"
                              width="30"
                            />
                            <path
                              d="M19.5 15.0008L10.5 20.197V9.80469L19.5 15.0008Z"
                              fill="#2B2B2B"
                            />
                          </g>
                          <defs>
                            <filter
                              color-interpolation-filters="sRGB"
                              filterUnits="userSpaceOnUse"
                              height="106"
                              id="filter0_b_8840_12265"
                              width="106"
                              x="-38"
                              y="-38"
                            >
                              <feflood
                                flood-opacity="0"
                                result="BackgroundImageFix"
                              />
                              <fegaussianblur
                                in="BackgroundImageFix"
                                stdDeviation="19"
                              />
                              <fecomposite
                                in2="SourceAlpha"
                                operator="in"
                                result="effect1_backgroundBlur_8840_12265"
                              />
                              <feblend
                                in="SourceGraphic"
                                in2="effect1_backgroundBlur_8840_12265"
                                mode="normal"
                                result="shape"
                              />
                            </filter>
                            <clippath
                              id="clip0_8840_12265"
                            >
                              <rect
                                fill="#FFFFFF"
                                height="30"
                                width="30"
                              />
                            </clippath>
                          </defs>
                        </svg>
                      </span>
                    </button>
                    <div
                      class="emotion-11"
                      data-testid="styled-mute-controls"
                    >
                      <button
                        aria-label="Mute"
                        aria-pressed="false"
                        class="emotion-12"
                      >
                        <span
                          aria-hidden="true"
                          class="emotion-10"
                        >
                          <svg
                            fill="none"
                            viewBox="0 0 30 30"
                            xmlns="http://www.w3.org/2000/svg"
                          >
                            <g
                              filter="url(#filter0_b_8973_3420)"
                            >
                              <rect
                                fill="#F7F7F7"
                                height="30"
                                width="30"
                              />
                              <path
                                d="M21.2364 20.9121L7.01953 10.9904L7.77703 9.90515L21.9953 19.8263L21.2364 20.9121Z"
                                fill="#2B2B2B"
                              />
                              <path
                                d="M19.545 20.1809L18.5015 19.3666C19.2034 18.5002 19.6714 17.4682 19.8608 16.3693C19.9912 15.5096 19.9396 14.6322 19.7091 13.7938C19.47 12.8893 19.0628 12.0377 18.5084 11.2842L19.5381 10.4526C20.209 11.3506 20.7013 12.369 20.9883 13.4526C21.2665 14.4693 21.3274 15.5333 21.1675 16.5753C20.9443 17.8963 20.3857 19.1378 19.545 20.1809Z"
                                fill="#2B2B2B"
                              />
                              <path
                                d="M16.8456 19.085L15.7427 18.356C16.1805 17.7303 16.4799 17.019 16.6216 16.2686C16.7633 15.5182 16.7437 14.7459 16.5643 14.0037C16.4105 13.3412 16.1395 12.7116 15.7638 12.1446L16.8262 11.3553C17.3109 12.0657 17.6589 12.8604 17.8525 13.6982C18.0717 14.6165 18.0953 15.5706 17.9218 16.4987C17.7484 17.4267 17.3817 18.3079 16.8456 19.085Z"
                                fill="#2B2B2B"
                              />
                              <path
                                d="M9.89171 16.6854H7.40039V13.7084H9.89171L13.9448 9.98572V20.4076L9.89171 16.6854Z"
                                fill="#2B2B2B"
                              />
                              <path
                                d="M14.1638 20.9094L9.80411 16.9058H7.17822V13.4875H9.80411L14.1638 9.4834V20.9094ZM7.6194 16.4646H9.97572L13.7222 19.9058V10.4875L9.97572 13.9287H7.6194V16.4646Z"
                                fill="#2B2B2B"
                              />
                            </g>
                            <defs>
                              <filter
                                color-interpolation-filters="sRGB"
                                filterUnits="userSpaceOnUse"
                                height="106"
                                id="filter0_b_8973_3420"
                                width="106"
                                x="-38"
                                y="-38"
                              >
                                <feflood
                                  flood-opacity="0"
                                  result="BackgroundImageFix"
                                />
                                <fegaussianblur
                                  in="BackgroundImageFix"
                                  stdDeviation="19"
                                />
                                <fecomposite
                                  in2="SourceAlpha"
                                  operator="in"
                                  result="effect1_backgroundBlur_8973_3420"
                                />
                                <feblend
                                  in="SourceGraphic"
                                  in2="effect1_backgroundBlur_8973_3420"
                                  mode="normal"
                                  result="shape"
                                />
                              </filter>
                            </defs>
                          </svg>
                        </span>
                      </button>
                      <div
                        class="emotion-14"
                        data-testid="styled-input-volume"
                        style="background: rgb(255, 255, 255);"
                      >
                        <div
                          role="presentation"
                        />
                      </div>
                    </div>
                  </div>
                </div>
                <div
                  class="emotion-15"
                >
                  <div
                    class="emotion-16"
                  >
                    <div
                      class="emotion-17"
                    >
                      <div
                        class="emotion-18"
                      >
                        <div>
                          <p
                            class="amp-cms--p"
                            style="text-align:left;"
                          >
                            <span
                              class="amp-cms--subhead-1"
                              style="color:#FFFFFF"
                            >
                              Card
                            </span>
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </a>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`Gap FeaturedCategories full bleed desktop should match snapshots for size large 1`] = `
.emotion-0 {
  height: 100%;
  padding: 0;
}

.emotion-1 {
  margin-bottom: unset;
  margin-left: unset;
  margin: 0 0 30px 35px;
}

.emotion-1 .amp-cms--p {
  padding: 0;
  margin: 0;
  line-height: 0;
}

.emotion-1 .amp-cms--p>span {
  display: inline;
  white-space: break-spaces;
}

.emotion-1 .amp-cms--p a {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  position: relative;
  z-index: 2;
  pointer-events: auto;
}

.emotion-1 .amp-cms--p a:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-1 .amp-cms--legal-copy {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-1 sup {
  vertical-align: top;
  display: inline-block;
  margin-top: -0.25ex;
}

.emotion-1 sub {
  vertical-align: bottom;
  display: inline-block;
  margin-bottom: -0.25ex;
}

.emotion-1 .amp-cms--f-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--f-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--f-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--fn-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--body-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 20px;
  line-height: 1.3;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--body-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 18px;
  line-height: 1.2777777777777777;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--body-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 16px;
  line-height: 1.25;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--body-4 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 14px;
  line-height: 1.2857142857142858;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--body-5 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--eyebrow-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 50px;
  line-height: 0.92;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--eyebrow-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 40px;
  line-height: 1;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--eyebrow-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 35px;
  line-height: 1;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--headline-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 120px;
  line-height: 0.8333333333333334;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--headline-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 110px;
  line-height: 0.8636363636363636;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--headline-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 100px;
  line-height: 0.85;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--headline-4 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 100px;
  line-height: 1.05;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--headline-5 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 80px;
  line-height: 0.9375;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--headline-6 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 70px;
  line-height: 0.9285714285714286;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--headline-7 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 60px;
  line-height: 0.9166666666666666;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--headlineAlt-1 {
  color: #2B2B2B;
  font-size: 114px;
  line-height: 1;
  letter-spacing: -0.4px;
  font-weight: 400;
}

.emotion-1 .amp-cms--headlineAlt-2 {
  color: #2B2B2B;
  font-size: 84px;
  line-height: 1;
  letter-spacing: -0.4px;
  font-weight: 400;
}

.emotion-1 .amp-cms--headlineAlt-3 {
  color: #2B2B2B;
  font-size: 64px;
  line-height: 1;
  letter-spacing: -0.4px;
  font-weight: 400;
}

.emotion-1 .amp-cms--headlineAlt-4 {
  color: #2B2B2B;
  font-size: 34px;
  line-height: 1;
  letter-spacing: -0.4px;
  font-weight: 400;
}

.emotion-1 .amp-cms--headlineAlt-5 {
  color: #2B2B2B;
  font-size: 16px;
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-1 .amp-cms--headlineAlt-6 {
  color: #2B2B2B;
  font-size: 16px;
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-1 .amp-cms--headlineAlt-7 {
  color: #2B2B2B;
  font-size: 16px;
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-1 .amp-cms--promo-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 100px;
  line-height: 1;
  letter-spacing: 0px;
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-1 .amp-cms--promo-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 80px;
  line-height: 1;
  letter-spacing: 0px;
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-1 .amp-cms--promoAlt-1 {
  color: #2B2B2B;
  font-size: 16px;
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 600;
}

.emotion-1 .amp-cms--subhead-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 30px;
  line-height: 1;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--subhead-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 25px;
  line-height: 1;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--subhead-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 20px;
  line-height: 1.1;
  letter-spacing: 0;
}

.emotion-2 {
  padding: 0;
}

.emotion-3 {
  width: 100%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-flex-wrap: nowrap;
  -webkit-flex-wrap: nowrap;
  -ms-flex-wrap: nowrap;
  flex-wrap: nowrap;
}

.emotion-4 {
  position: relative;
  width: 100%;
}

.emotion-5 {
  cursor: pointer;
  height: 100%;
  width: 100%;
}

.emotion-6 {
  background: transparent;
  width: 100%!important;
  height: min(56.25vw, 810px)!important;
  -webkit-background-size: cover;
  background-size: cover;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: end;
  -webkit-box-align: end;
  -ms-flex-align: end;
  align-items: end;
  box-sizing: border-box;
}

.emotion-7 {
  width: 100%!important;
  height: auto!important;
  -webkit-background-size: cover;
  background-size: cover;
  display: -webkit-box!important;
  display: -webkit-flex!important;
  display: -ms-flexbox!important;
  display: flex!important;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: end;
  -webkit-box-align: end;
  -ms-flex-align: end;
  align-items: end;
  box-sizing: border-box;
  padding: 30px 35px;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  gap: 20px;
}

.emotion-8 {
  box-sizing: border-box;
  height: auto!important;
  width: 100%!important;
  display: -webkit-box!important;
  display: -webkit-flex!important;
  display: -ms-flexbox!important;
  display: flex!important;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  gap: 15px;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
}

.emotion-9 .amp-cms--p {
  padding: 0;
  margin: 0;
  line-height: 0;
}

.emotion-9 .amp-cms--p>span {
  display: inline;
  white-space: break-spaces;
}

.emotion-9 .amp-cms--p a {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  position: relative;
  z-index: 2;
  pointer-events: auto;
}

.emotion-9 .amp-cms--p a:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-9 .amp-cms--legal-copy {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, 0.8333333333333334vw);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-9 sup {
  vertical-align: top;
  display: inline-block;
  margin-top: -0.25ex;
}

.emotion-9 sub {
  vertical-align: bottom;
  display: inline-block;
  margin-bottom: -0.25ex;
}

.emotion-9 .amp-cms--f-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 0.8333333333333334vw;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--f-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 0.8333333333333334vw;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--f-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 0.8333333333333334vw;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--fn-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 0.8333333333333334vw;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--body-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, 1.3888888888888888vw);
  line-height: 1.3;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--body-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, 1.25vw);
  line-height: 1.2777777777777777;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--body-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, 1.1111111111111112vw);
  line-height: 1.25;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--body-4 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, 0.9722222222222222vw);
  line-height: 1.2857142857142858;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--body-5 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, 0.8333333333333334vw);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--eyebrow-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(35px, 3.4722222222222223vw);
  line-height: 0.92;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--eyebrow-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(35px, 2.7777777777777777vw);
  line-height: 1;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--eyebrow-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(35px, 2.430555555555556vw);
  line-height: 1;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--headline-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, 8.333333333333332vw);
  line-height: 0.8333333333333334;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--headline-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, 7.638888888888889vw);
  line-height: 0.8636363636363636;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--headline-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, 6.944444444444445vw);
  line-height: 0.85;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--headline-4 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, 6.944444444444445vw);
  line-height: 1.05;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--headline-5 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, 5.555555555555555vw);
  line-height: 0.9375;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--headline-6 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, 4.861111111111112vw);
  line-height: 0.9285714285714286;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--headline-7 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, 4.166666666666666vw);
  line-height: 0.9166666666666666;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--headlineAlt-1 {
  color: #2B2B2B;
  font-size: max(14px, 7.916666666666666vw);
  line-height: 1;
  letter-spacing: -0.027777777777777776vw;
  font-weight: 400;
}

.emotion-9 .amp-cms--headlineAlt-2 {
  color: #2B2B2B;
  font-size: max(14px, 5.833333333333333vw);
  line-height: 1;
  letter-spacing: -0.027777777777777776vw;
  font-weight: 400;
}

.emotion-9 .amp-cms--headlineAlt-3 {
  color: #2B2B2B;
  font-size: max(14px, 4.444444444444445vw);
  line-height: 1;
  letter-spacing: -0.027777777777777776vw;
  font-weight: 400;
}

.emotion-9 .amp-cms--headlineAlt-4 {
  color: #2B2B2B;
  font-size: max(14px, 2.361111111111111vw);
  line-height: 1;
  letter-spacing: -0.027777777777777776vw;
  font-weight: 400;
}

.emotion-9 .amp-cms--headlineAlt-5 {
  color: #2B2B2B;
  font-size: 1.1111111111111112vw;
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-9 .amp-cms--headlineAlt-6 {
  color: #2B2B2B;
  font-size: 1.1111111111111112vw;
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-9 .amp-cms--headlineAlt-7 {
  color: #2B2B2B;
  font-size: 1.1111111111111112vw;
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-9 .amp-cms--promo-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, 6.944444444444445vw);
  line-height: 1;
  letter-spacing: 0vw;
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-9 .amp-cms--promo-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, 5.555555555555555vw);
  line-height: 1;
  letter-spacing: 0vw;
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-9 .amp-cms--promoAlt-1 {
  color: #2B2B2B;
  font-size: 1.1111111111111112vw;
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 600;
}

.emotion-9 .amp-cms--subhead-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, 2.083333333333333vw);
  line-height: 1;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--subhead-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, 1.7361111111111112vw);
  line-height: 1;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--subhead-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, 1.3888888888888888vw);
  line-height: 1.1;
  letter-spacing: 0;
}

<div>
  <div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      class="emotion-0"
    >
      <div
        class="emotion-1"
      >
        <div>
          <p
            class="amp-cms--p"
            style="text-align:left;"
          >
            <span
              class="amp-cms--headline-3"
            >
              OPTIONAL HEADLINE
            </span>
          </p>
        </div>
      </div>
      <div
        class="emotion-2"
      >
        <div
          class="emotion-3"
        >
          <div
            class="emotion-4"
          >
            <a
              aria-label="new arrivals"
              class="emotion-5"
              data-testid="conditional-link"
              href="www.google.com"
              target="_self"
            >
              <div
                aria-label="image alt text"
                class="emotion-6"
                height="0"
                role="img"
                width="0"
              >
                <div
                  class="emotion-7"
                >
                  <div
                    class="emotion-8"
                  >
                    <div
                      class="emotion-9"
                    >
                      <div>
                        <p
                          class="amp-cms--p"
                          style="text-align:left;"
                        >
                          <span
                            class="amp-cms--subhead-1"
                            style="color:#FFFFFF"
                          >
                            Card
                          </span>
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </a>
          </div>
          <div
            class="emotion-4"
          >
            <a
              aria-label="new arrivals"
              class="emotion-5"
              data-testid="conditional-link"
              href="www.google.com"
              target="_self"
            >
              <div
                aria-label="image alt text"
                class="emotion-6"
                height="0"
                role="img"
                width="0"
              >
                <div
                  class="emotion-7"
                >
                  <div
                    class="emotion-8"
                  >
                    <div
                      class="emotion-9"
                    >
                      <div>
                        <p
                          class="amp-cms--p"
                          style="text-align:left;"
                        >
                          <span
                            class="amp-cms--subhead-1"
                            style="color:#FFFFFF"
                          >
                            Card
                          </span>
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </a>
          </div>
          <div
            class="emotion-4"
          >
            <a
              aria-label="new arrivals"
              class="emotion-5"
              data-testid="conditional-link"
              href="www.google.com"
              target="_self"
            >
              <div
                aria-label="image alt text"
                class="emotion-6"
                height="0"
                role="img"
                width="0"
              >
                <div
                  class="emotion-7"
                >
                  <div
                    class="emotion-8"
                  >
                    <div
                      class="emotion-9"
                    >
                      <div>
                        <p
                          class="amp-cms--p"
                          style="text-align:left;"
                        >
                          <span
                            class="amp-cms--subhead-1"
                            style="color:#FFFFFF"
                          >
                            Card
                          </span>
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </a>
          </div>
          <div
            class="emotion-4"
          >
            <a
              aria-label="new arrivals"
              class="emotion-5"
              data-testid="conditional-link"
              href="www.google.com"
              target="_self"
            >
              <div
                aria-label="image alt text"
                class="emotion-6"
                height="0"
                role="img"
                width="0"
              >
                <div
                  class="emotion-7"
                >
                  <div
                    class="emotion-8"
                  >
                    <div
                      class="emotion-9"
                    >
                      <div>
                        <p
                          class="amp-cms--p"
                          style="text-align:left;"
                        >
                          <span
                            class="amp-cms--subhead-1"
                            style="color:#FFFFFF"
                          >
                            Card
                          </span>
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </a>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`Gap FeaturedCategories full bleed mobile should match snapshots for scroll layout size large and medium 1`] = `
.emotion-0 {
  height: 100%;
  padding: 0;
}

.emotion-1 {
  margin-bottom: 15px;
  margin-left: 15px;
  margin: 0 0 15px 16px;
}

.emotion-1 .amp-cms--p {
  padding: 0;
  margin: 0;
  line-height: 0;
}

.emotion-1 .amp-cms--p>span {
  display: inline;
  white-space: break-spaces;
}

.emotion-1 .amp-cms--p a {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  position: relative;
  z-index: 2;
  pointer-events: auto;
}

.emotion-1 .amp-cms--p a:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-1 .amp-cms--legal-copy {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 10px;
  line-height: 1.4;
  letter-spacing: 0;
}

.emotion-1 sup {
  vertical-align: top;
  display: inline-block;
  margin-top: -0.25ex;
}

.emotion-1 sub {
  vertical-align: bottom;
  display: inline-block;
  margin-bottom: -0.25ex;
}

.emotion-1 .amp-cms--f-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 11px;
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--f-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 11px;
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--f-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 11px;
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--fn-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 11px;
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--body-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 18px;
  line-height: 1.2777777777777777;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--body-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 16px;
  line-height: 1.25;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--body-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 14px;
  line-height: 1.2857142857142858;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--body-4 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--body-5 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 10px;
  line-height: 1.4;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--eyebrow-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 30px;
  line-height: 1;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--eyebrow-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 26px;
  line-height: 1;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--eyebrow-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 24px;
  line-height: 1;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--headline-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 60px;
  line-height: 1;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--headline-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 55px;
  line-height: 1;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--headline-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 55px;
  line-height: 1;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--headline-4 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 50px;
  line-height: 1.1;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--headline-5 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 45px;
  line-height: 1;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--headline-6 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 40px;
  line-height: 1;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--headline-7 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 35px;
  line-height: 1;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--headlineAlt-1 {
  color: #2B2B2B;
  font-size: 64px;
  line-height: 1;
  letter-spacing: -0.3px;
  font-weight: 400;
}

.emotion-1 .amp-cms--headlineAlt-2 {
  color: #2B2B2B;
  font-size: 54px;
  line-height: 1;
  letter-spacing: -0.3px;
  font-weight: 400;
}

.emotion-1 .amp-cms--headlineAlt-3 {
  color: #2B2B2B;
  font-size: 34px;
  line-height: 1;
  letter-spacing: -0.3px;
  font-weight: 400;
}

.emotion-1 .amp-cms--headlineAlt-4 {
  color: #2B2B2B;
  font-size: 24px;
  line-height: 1.4166666666666667;
  letter-spacing: -0.3px;
  font-weight: 400;
}

.emotion-1 .amp-cms--headlineAlt-5 {
  color: #2B2B2B;
  font-size: 14px;
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-1 .amp-cms--headlineAlt-6 {
  color: #2B2B2B;
  font-size: 14px;
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-1 .amp-cms--headlineAlt-7 {
  color: #2B2B2B;
  font-size: 14px;
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-1 .amp-cms--promo-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 60px;
  line-height: 1;
  letter-spacing: 0px;
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-1 .amp-cms--promo-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 40px;
  line-height: 1;
  letter-spacing: 0px;
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-1 .amp-cms--promoAlt-1 {
  color: #2B2B2B;
  font-size: 14px;
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 600;
}

.emotion-1 .amp-cms--subhead-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 22px;
  line-height: 1.0909090909090908;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--subhead-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 20px;
  line-height: 1.1;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--subhead-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 18px;
  line-height: 1.1111111111111112;
  letter-spacing: 0;
}

.emotion-2 {
  display: grid;
  grid-template-columns: repeat(4, 250px);
  overflow-x: scroll;
  scrollbar-width: none;
  padding: 0;
  -ms-overflow-style: none;
}

.emotion-2::-webkit-scrollbar {
  display: none;
}

.emotion-3 {
  position: relative;
  width: 100%;
}

.emotion-4 {
  cursor: pointer;
  height: 100%;
  width: 100%;
}

.emotion-5 {
  background: transparent;
  width: 100%!important;
  height: min(106.25vw, 340px)!important;
  -webkit-background-size: cover;
  background-size: cover;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: end;
  -webkit-box-align: end;
  -ms-flex-align: end;
  align-items: end;
  box-sizing: border-box;
}

.emotion-6 {
  width: 100%!important;
  height: auto!important;
  -webkit-background-size: cover;
  background-size: cover;
  display: -webkit-box!important;
  display: -webkit-flex!important;
  display: -ms-flexbox!important;
  display: flex!important;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: end;
  -webkit-box-align: end;
  -ms-flex-align: end;
  align-items: end;
  box-sizing: border-box;
  padding: 20px 15px;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  gap: 20px;
}

.emotion-7 {
  box-sizing: border-box;
  height: auto!important;
  width: 100%!important;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  gap: 15px;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
}

.emotion-8 .amp-cms--p {
  padding: 0;
  margin: 0;
  line-height: 0;
}

.emotion-8 .amp-cms--p>span {
  display: inline;
  white-space: break-spaces;
}

.emotion-8 .amp-cms--p a {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  position: relative;
  z-index: 2;
  pointer-events: auto;
}

.emotion-8 .amp-cms--p a:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-8 .amp-cms--legal-copy {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, 2.666666666666667vw);
  line-height: 1.4;
  letter-spacing: 0;
}

.emotion-8 sup {
  vertical-align: top;
  display: inline-block;
  margin-top: -0.25ex;
}

.emotion-8 sub {
  vertical-align: bottom;
  display: inline-block;
  margin-bottom: -0.25ex;
}

.emotion-8 .amp-cms--f-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 2.933333333333333vw;
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-8 .amp-cms--f-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 2.933333333333333vw;
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-8 .amp-cms--f-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 2.933333333333333vw;
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-8 .amp-cms--fn-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 2.933333333333333vw;
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-8 .amp-cms--body-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, 4.8vw);
  line-height: 1.2777777777777777;
  letter-spacing: 0;
}

.emotion-8 .amp-cms--body-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, 4.266666666666667vw);
  line-height: 1.25;
  letter-spacing: 0;
}

.emotion-8 .amp-cms--body-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, 3.733333333333334vw);
  line-height: 1.2857142857142858;
  letter-spacing: 0;
}

.emotion-8 .amp-cms--body-4 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, 3.2vw);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-8 .amp-cms--body-5 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, 2.666666666666667vw);
  line-height: 1.4;
  letter-spacing: 0;
}

.emotion-8 .amp-cms--eyebrow-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, 8vw);
  line-height: 1;
  letter-spacing: 0;
}

.emotion-8 .amp-cms--eyebrow-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, 6.933333333333333vw);
  line-height: 1;
  letter-spacing: 0;
}

.emotion-8 .amp-cms--eyebrow-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, 6.4vw);
  line-height: 1;
  letter-spacing: 0;
}

.emotion-8 .amp-cms--headline-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, 16vw);
  line-height: 1;
  letter-spacing: 0;
}

.emotion-8 .amp-cms--headline-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, 14.666666666666666vw);
  line-height: 1;
  letter-spacing: 0;
}

.emotion-8 .amp-cms--headline-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, 14.666666666666666vw);
  line-height: 1;
  letter-spacing: 0;
}

.emotion-8 .amp-cms--headline-4 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, 13.333333333333334vw);
  line-height: 1.1;
  letter-spacing: 0;
}

.emotion-8 .amp-cms--headline-5 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, 12vw);
  line-height: 1;
  letter-spacing: 0;
}

.emotion-8 .amp-cms--headline-6 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, 10.666666666666668vw);
  line-height: 1;
  letter-spacing: 0;
}

.emotion-8 .amp-cms--headline-7 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, 9.333333333333334vw);
  line-height: 1;
  letter-spacing: 0;
}

.emotion-8 .amp-cms--headlineAlt-1 {
  color: #2B2B2B;
  font-size: max(13px, 17.066666666666666vw);
  line-height: 1;
  letter-spacing: -0.07999999999999999vw;
  font-weight: 400;
}

.emotion-8 .amp-cms--headlineAlt-2 {
  color: #2B2B2B;
  font-size: max(13px, 14.399999999999999vw);
  line-height: 1;
  letter-spacing: -0.07999999999999999vw;
  font-weight: 400;
}

.emotion-8 .amp-cms--headlineAlt-3 {
  color: #2B2B2B;
  font-size: max(13px, 9.066666666666666vw);
  line-height: 1;
  letter-spacing: -0.07999999999999999vw;
  font-weight: 400;
}

.emotion-8 .amp-cms--headlineAlt-4 {
  color: #2B2B2B;
  font-size: max(13px, 6.4vw);
  line-height: 1.4166666666666667;
  letter-spacing: -0.07999999999999999vw;
  font-weight: 400;
}

.emotion-8 .amp-cms--headlineAlt-5 {
  color: #2B2B2B;
  font-size: 3.733333333333334vw;
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-8 .amp-cms--headlineAlt-6 {
  color: #2B2B2B;
  font-size: 3.733333333333334vw;
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-8 .amp-cms--headlineAlt-7 {
  color: #2B2B2B;
  font-size: 3.733333333333334vw;
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-8 .amp-cms--promo-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(13px, 16vw);
  line-height: 1;
  letter-spacing: 0vw;
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-8 .amp-cms--promo-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(13px, 10.666666666666668vw);
  line-height: 1;
  letter-spacing: 0vw;
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-8 .amp-cms--promoAlt-1 {
  color: #2B2B2B;
  font-size: 3.733333333333334vw;
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 600;
}

.emotion-8 .amp-cms--subhead-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, 5.866666666666666vw);
  line-height: 1.0909090909090908;
  letter-spacing: 0;
}

.emotion-8 .amp-cms--subhead-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, 5.333333333333334vw);
  line-height: 1.1;
  letter-spacing: 0;
}

.emotion-8 .amp-cms--subhead-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, 4.8vw);
  line-height: 1.1111111111111112;
  letter-spacing: 0;
}

.emotion-17 {
  position: relative;
  height: min(106.25vw, 340px)!important;
  pointer-events: auto;
  width: 100%!important;
  -webkit-background-size: cover;
  background-size: cover;
}

.emotion-17>div>img {
  height: min(106.25vw, 340px)!important;
}

.emotion-18 {
  width: -webkit-fit-content;
  width: -moz-fit-content;
  width: fit-content;
  position: relative;
  width: 100%;
}

.emotion-19 {
  display: none;
  gap: 15px;
  position: absolute;
  max-width: -webkit-max-content;
  max-width: -moz-max-content;
  max-width: max-content;
  max-height: -webkit-max-content;
  max-height: -moz-max-content;
  max-height: max-content;
  margin: 10px;
  right: 0;
  bottom: 0;
  height: 28px;
}

.emotion-19>button {
  left: 0;
  bottom: 0;
  z-index: 4;
  position: relative;
  width: 28px;
  height: 28px;
}

.emotion-19>div>button {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  left: 0;
  bottom: 0;
  z-index: 4;
  position: relative;
}

.emotion-19>div>div {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  left: 0;
  z-index: 3;
  bottom: 0;
}

.emotion-19>div>div:before {
  left: 0;
  bottom: -28px;
  width: 28px;
  height: 28px;
  border-radius: 0;
}

.emotion-20 {
  background: none;
  border: none;
  padding: 0;
  height: 24px;
  margin: 0;
  position: absolute;
  bottom: 11px;
  right: 112px;
  width: 24px;
  height: 24px;
}

.emotion-20:is(:not(:focus, :active, :hover, :focus-within)) {
  opacity: 0.64;
}

.emotion-20:is(:hover) {
  opacity: 1;
}

@media (min-width: 767px) {
  .emotion-20 {
    right: 120px;
  }
}

.emotion-21 {
  display: inline-block;
  height: 28px;
  width: 28px;
  min-height: 28px;
  min-width: 28px;
}

.emotion-21 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-21 svg:hover rect {
  fill: #031BA1;
}

.emotion-21 svg:hover path {
  fill: #FFFFFF;
}

.emotion-22 {
  box-sizing: border-box;
  background: none;
  border: none;
  padding: 0;
  position: relative;
  width: 100%;
  height: 0px;
}

.emotion-22:focus div {
  opacity: 0.84;
  -webkit-transition: opacity 200ms;
  transition: opacity 200ms;
  border-radius: 20px 20px 0 0;
}

.emotion-22 div {
  border-radius: 20px 20px 0 0;
}

.emotion-22 .keepOpen,
.emotion-22 .staysOpen {
  opacity: 0.84;
  -webkit-transition: opacity 200ms;
  transition: opacity 200ms;
  z-index: 2;
}

.emotion-23 {
  background: none;
  border: none;
  padding: 0;
  height: 24px;
  margin: 0;
  position: absolute;
  bottom: 11px;
  right: 72px;
  z-index: 11;
}

.emotion-23:is(:not(:focus, :active, :hover, :focus-within)) {
  opacity: 0.64;
}

.emotion-23:is(:hover) {
  opacity: 1;
}

@media (min-width: 767px) {
  .emotion-23 {
    right: 80px;
  }
}

.emotion-25 {
  opacity: 0;
  -webkit-transition: opacity 200ms;
  transition: opacity 200ms;
  box-sizing: border-box;
  background: #ffffff;
  border: none;
  padding: 0;
  position: absolute;
  width: 28px;
  height: 60px;
  bottom: 35px;
  right: 72px;
  border-radius: 20px 20px 0 0;
}

.emotion-25:before {
  content: "";
  height: 24px;
  background: #ffffff;
  display: block;
  position: absolute;
  bottom: -24px;
  width: 24px;
  border-bottom-right-radius: 50%;
  border-bottom-left-radius: 50%;
}

.emotion-26 {
  position: absolute;
  z-index: 0;
  width: 100%!important;
  height: min(106.25vw, 340px)!important;
  top: 0;
  pointer-events: none;
  -webkit-background-size: cover;
  background-size: cover;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: end;
  -webkit-box-align: end;
  -ms-flex-align: end;
  align-items: end;
  box-sizing: border-box;
}

<div>
  <div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      class="emotion-0"
    >
      <div
        class="emotion-1"
      >
        <div>
          <p
            class="amp-cms--p"
            style="text-align:left;"
          >
            <span
              class="amp-cms--headline-3"
            >
              OPTIONAL HEADLINE
            </span>
          </p>
        </div>
      </div>
      <div
        class="emotion-2"
      >
        <div
          class="emotion-3"
        >
          <a
            aria-label="new arrivals"
            class="emotion-4"
            data-testid="conditional-link"
            href="www.google.com"
            target="_self"
          >
            <div
              aria-label="mobile alt text"
              class="emotion-5"
              height="0"
              role="img"
              width="0"
            >
              <div
                class="emotion-6"
              >
                <div
                  class="emotion-7"
                >
                  <div
                    class="emotion-8"
                  >
                    <div>
                      <p
                        class="amp-cms--p"
                        style="text-align:left;"
                      >
                        <span
                          class="amp-cms--body-1"
                        >
                          Mobile Card
                        </span>
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </a>
        </div>
        <div
          class="emotion-3"
        >
          <a
            aria-label="new arrivals"
            class="emotion-4"
            data-testid="conditional-link"
            href="www.google.com"
            target="_self"
          >
            <div
              aria-label="mobile alt text"
              class="emotion-5"
              height="0"
              role="img"
              width="0"
            >
              <div
                class="emotion-6"
              >
                <div
                  class="emotion-7"
                >
                  <div
                    class="emotion-8"
                  >
                    <div>
                      <p
                        class="amp-cms--p"
                        style="text-align:left;"
                      >
                        <span
                          class="amp-cms--body-1"
                        >
                          Mobile Card
                        </span>
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </a>
        </div>
        <div
          class="emotion-3"
        >
          <a
            aria-label="new arrivals"
            class="emotion-4"
            data-testid="conditional-link"
            href="www.google.com"
            target="_self"
          >
            <div
              class="emotion-17"
            >
              <div
                class="emotion-18"
                data-testid="videocomponent-container"
              >
                <div
                  style="position: relative;"
                >
                  <img
                    fetchpriority="high"
                    src="https://2hmc8flz4ofg1dy89cc38f2xm.staging.bigcontent.io/i/athleta/Card 3 - MobileL?fmt=auto"
                    style="height: 100%; left: 0px; object-fit: cover; position: absolute; top: 0px; width: 100%;"
                  />
                  <h1
                    role="presentation"
                  >
                    ReactPlayer
                  </h1>
                </div>
                <div
                  class="player-custom-controls emotion-19"
                  data-testid="player-custom-controls"
                >
                  <button
                    aria-label="Play"
                    aria-pressed="false"
                    class="emotion-20"
                  >
                    <span
                      aria-hidden="true"
                      class="emotion-21"
                    >
                      <svg
                        fill="none"
                        viewBox="0 0 28 28"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <g
                          filter="url(#filter0_b_13041_121152)"
                        >
                          <rect
                            fill="#F7F7F7"
                            height="28"
                            width="28"
                          />
                          <path
                            d="M17.615 14.18L10.375 18.36V10L17.615 14.18Z"
                            fill="#2B2B2B"
                          />
                        </g>
                        <defs>
                          <filter
                            color-interpolation-filters="sRGB"
                            filterUnits="userSpaceOnUse"
                            height="104"
                            id="filter0_b_13041_121152"
                            width="104"
                            x="-38"
                            y="-38"
                          >
                            <feflood
                              flood-opacity="0"
                              result="BackgroundImageFix"
                            />
                            <fegaussianblur
                              in="BackgroundImageFix"
                              stdDeviation="19"
                            />
                            <fecomposite
                              in2="SourceAlpha"
                              operator="in"
                              result="effect1_backgroundBlur_13041_121152"
                            />
                            <feblend
                              in="SourceGraphic"
                              in2="effect1_backgroundBlur_13041_121152"
                              mode="normal"
                              result="shape"
                            />
                          </filter>
                        </defs>
                      </svg>
                    </span>
                  </button>
                  <div
                    class="emotion-22"
                    data-testid="styled-mute-controls"
                  >
                    <button
                      aria-label="Mute"
                      aria-pressed="false"
                      class="emotion-23"
                    >
                      <span
                        aria-hidden="true"
                        class="emotion-21"
                      >
                        <svg
                          fill="none"
                          viewBox="0 0 28 28"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <g
                            filter="url(#filter0_b_8973_3432)"
                          >
                            <rect
                              fill="#F7F7F7"
                              height="28"
                              width="28"
                            />
                            <path
                              d="M19.3892 18.3292L8.01562 10.3919L8.62162 9.52363L19.9962 17.4606L19.3892 18.3292Z"
                              fill="#2B2B2B"
                            />
                            <path
                              d="M18.0364 17.7442L17.2016 17.0927C17.7631 16.3996 18.1375 15.574 18.289 14.6949C18.3934 14.0071 18.3521 13.3052 18.1677 12.6344C17.9764 11.9108 17.6506 11.2296 17.2071 10.6267L18.0308 9.96152C18.5676 10.6799 18.9614 11.4946 19.191 12.3615C19.4136 13.1749 19.4623 14.0261 19.3344 14.8596C19.1558 15.9164 18.7089 16.9096 18.0364 17.7442Z"
                              fill="#2B2B2B"
                            />
                            <path
                              d="M15.8761 16.8675L14.9938 16.2843C15.344 15.7838 15.5835 15.2147 15.6969 14.6144C15.8102 14.0141 15.7946 13.3963 15.651 12.8025C15.528 12.2724 15.3112 11.7688 15.0106 11.3152L15.8606 10.6838C16.2483 11.2521 16.5268 11.8878 16.6816 12.5581C16.857 13.2927 16.8758 14.056 16.7371 14.7984C16.5983 15.5409 16.305 16.2458 15.8761 16.8675Z"
                              fill="#2B2B2B"
                            />
                            <path
                              d="M10.3134 14.9479H8.32031V12.5662H10.3134L13.5558 9.58809V17.9256L10.3134 14.9479Z"
                              fill="#2B2B2B"
                            />
                            <path
                              d="M13.7307 18.3273L10.2429 15.1244H8.14219V12.3898H10.2429L13.7307 9.18652V18.3273ZM8.49513 14.7715H10.3802L13.3774 17.5244V9.98982L10.3802 12.7428H8.49513V14.7715Z"
                              fill="#2B2B2B"
                            />
                          </g>
                          <defs>
                            <filter
                              color-interpolation-filters="sRGB"
                              filterUnits="userSpaceOnUse"
                              height="104"
                              id="filter0_b_8973_3432"
                              width="104"
                              x="-38"
                              y="-38"
                            >
                              <feflood
                                flood-opacity="0"
                                result="BackgroundImageFix"
                              />
                              <fegaussianblur
                                in="BackgroundImageFix"
                                stdDeviation="19"
                              />
                              <fecomposite
                                in2="SourceAlpha"
                                operator="in"
                                result="effect1_backgroundBlur_8973_3432"
                              />
                              <feblend
                                in="SourceGraphic"
                                in2="effect1_backgroundBlur_8973_3432"
                                mode="normal"
                                result="shape"
                              />
                            </filter>
                          </defs>
                        </svg>
                      </span>
                    </button>
                    <div
                      class="emotion-25"
                      data-testid="styled-input-volume"
                      style="background: rgb(255, 255, 255);"
                    >
                      <div
                        role="presentation"
                      />
                    </div>
                  </div>
                </div>
              </div>
              <div
                class="emotion-26"
              >
                <div
                  class="emotion-6"
                >
                  <div
                    class="emotion-7"
                  >
                    <div
                      class="emotion-8"
                    >
                      <div>
                        <p
                          class="amp-cms--p"
                          style="text-align:left;"
                        >
                          <span
                            class="amp-cms--body-1"
                          >
                            Mobile Card
                          </span>
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </a>
        </div>
        <div
          class="emotion-3"
        >
          <a
            aria-label="new arrivals"
            class="emotion-4"
            data-testid="conditional-link"
            href="www.google.com"
            target="_self"
          >
            <div
              class="emotion-17"
            >
              <div
                class="emotion-18"
                data-testid="videocomponent-container"
              >
                <div
                  style="position: relative;"
                >
                  <img
                    fetchpriority="high"
                    src="https://2hmc8flz4ofg1dy89cc38f2xm.staging.bigcontent.io/i/athleta/Card 3 - MobileL?fmt=auto"
                    style="height: 100%; left: 0px; object-fit: cover; position: absolute; top: 0px; width: 100%;"
                  />
                  <h1
                    role="presentation"
                  >
                    ReactPlayer
                  </h1>
                </div>
                <div
                  class="player-custom-controls emotion-19"
                  data-testid="player-custom-controls"
                >
                  <button
                    aria-label="Play"
                    aria-pressed="false"
                    class="emotion-20"
                  >
                    <span
                      aria-hidden="true"
                      class="emotion-21"
                    >
                      <svg
                        fill="none"
                        viewBox="0 0 28 28"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <g
                          filter="url(#filter0_b_13041_121152)"
                        >
                          <rect
                            fill="#F7F7F7"
                            height="28"
                            width="28"
                          />
                          <path
                            d="M17.615 14.18L10.375 18.36V10L17.615 14.18Z"
                            fill="#2B2B2B"
                          />
                        </g>
                        <defs>
                          <filter
                            color-interpolation-filters="sRGB"
                            filterUnits="userSpaceOnUse"
                            height="104"
                            id="filter0_b_13041_121152"
                            width="104"
                            x="-38"
                            y="-38"
                          >
                            <feflood
                              flood-opacity="0"
                              result="BackgroundImageFix"
                            />
                            <fegaussianblur
                              in="BackgroundImageFix"
                              stdDeviation="19"
                            />
                            <fecomposite
                              in2="SourceAlpha"
                              operator="in"
                              result="effect1_backgroundBlur_13041_121152"
                            />
                            <feblend
                              in="SourceGraphic"
                              in2="effect1_backgroundBlur_13041_121152"
                              mode="normal"
                              result="shape"
                            />
                          </filter>
                        </defs>
                      </svg>
                    </span>
                  </button>
                  <div
                    class="emotion-22"
                    data-testid="styled-mute-controls"
                  >
                    <button
                      aria-label="Mute"
                      aria-pressed="false"
                      class="emotion-23"
                    >
                      <span
                        aria-hidden="true"
                        class="emotion-21"
                      >
                        <svg
                          fill="none"
                          viewBox="0 0 28 28"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <g
                            filter="url(#filter0_b_8973_3432)"
                          >
                            <rect
                              fill="#F7F7F7"
                              height="28"
                              width="28"
                            />
                            <path
                              d="M19.3892 18.3292L8.01562 10.3919L8.62162 9.52363L19.9962 17.4606L19.3892 18.3292Z"
                              fill="#2B2B2B"
                            />
                            <path
                              d="M18.0364 17.7442L17.2016 17.0927C17.7631 16.3996 18.1375 15.574 18.289 14.6949C18.3934 14.0071 18.3521 13.3052 18.1677 12.6344C17.9764 11.9108 17.6506 11.2296 17.2071 10.6267L18.0308 9.96152C18.5676 10.6799 18.9614 11.4946 19.191 12.3615C19.4136 13.1749 19.4623 14.0261 19.3344 14.8596C19.1558 15.9164 18.7089 16.9096 18.0364 17.7442Z"
                              fill="#2B2B2B"
                            />
                            <path
                              d="M15.8761 16.8675L14.9938 16.2843C15.344 15.7838 15.5835 15.2147 15.6969 14.6144C15.8102 14.0141 15.7946 13.3963 15.651 12.8025C15.528 12.2724 15.3112 11.7688 15.0106 11.3152L15.8606 10.6838C16.2483 11.2521 16.5268 11.8878 16.6816 12.5581C16.857 13.2927 16.8758 14.056 16.7371 14.7984C16.5983 15.5409 16.305 16.2458 15.8761 16.8675Z"
                              fill="#2B2B2B"
                            />
                            <path
                              d="M10.3134 14.9479H8.32031V12.5662H10.3134L13.5558 9.58809V17.9256L10.3134 14.9479Z"
                              fill="#2B2B2B"
                            />
                            <path
                              d="M13.7307 18.3273L10.2429 15.1244H8.14219V12.3898H10.2429L13.7307 9.18652V18.3273ZM8.49513 14.7715H10.3802L13.3774 17.5244V9.98982L10.3802 12.7428H8.49513V14.7715Z"
                              fill="#2B2B2B"
                            />
                          </g>
                          <defs>
                            <filter
                              color-interpolation-filters="sRGB"
                              filterUnits="userSpaceOnUse"
                              height="104"
                              id="filter0_b_8973_3432"
                              width="104"
                              x="-38"
                              y="-38"
                            >
                              <feflood
                                flood-opacity="0"
                                result="BackgroundImageFix"
                              />
                              <fegaussianblur
                                in="BackgroundImageFix"
                                stdDeviation="19"
                              />
                              <fecomposite
                                in2="SourceAlpha"
                                operator="in"
                                result="effect1_backgroundBlur_8973_3432"
                              />
                              <feblend
                                in="SourceGraphic"
                                in2="effect1_backgroundBlur_8973_3432"
                                mode="normal"
                                result="shape"
                              />
                            </filter>
                          </defs>
                        </svg>
                      </span>
                    </button>
                    <div
                      class="emotion-25"
                      data-testid="styled-input-volume"
                      style="background: rgb(255, 255, 255);"
                    >
                      <div
                        role="presentation"
                      />
                    </div>
                  </div>
                </div>
              </div>
              <div
                class="emotion-26"
              >
                <div
                  class="emotion-6"
                >
                  <div
                    class="emotion-7"
                  >
                    <div
                      class="emotion-8"
                    >
                      <div>
                        <p
                          class="amp-cms--p"
                          style="text-align:left;"
                        >
                          <span
                            class="amp-cms--body-1"
                          >
                            Mobile Card
                          </span>
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </a>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`Gap FeaturedCategories full bleed mobile should match snapshots for size large 1`] = `
.emotion-0 {
  height: 100%;
  padding: 0;
}

.emotion-1 {
  margin-bottom: 15px;
  margin-left: 15px;
  margin: 0 0 15px 16px;
}

.emotion-1 .amp-cms--p {
  padding: 0;
  margin: 0;
  line-height: 0;
}

.emotion-1 .amp-cms--p>span {
  display: inline;
  white-space: break-spaces;
}

.emotion-1 .amp-cms--p a {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  position: relative;
  z-index: 2;
  pointer-events: auto;
}

.emotion-1 .amp-cms--p a:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-1 .amp-cms--legal-copy {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 10px;
  line-height: 1.4;
  letter-spacing: 0;
}

.emotion-1 sup {
  vertical-align: top;
  display: inline-block;
  margin-top: -0.25ex;
}

.emotion-1 sub {
  vertical-align: bottom;
  display: inline-block;
  margin-bottom: -0.25ex;
}

.emotion-1 .amp-cms--f-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 11px;
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--f-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 11px;
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--f-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 11px;
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--fn-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 11px;
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--body-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 18px;
  line-height: 1.2777777777777777;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--body-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 16px;
  line-height: 1.25;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--body-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 14px;
  line-height: 1.2857142857142858;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--body-4 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--body-5 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 10px;
  line-height: 1.4;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--eyebrow-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 30px;
  line-height: 1;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--eyebrow-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 26px;
  line-height: 1;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--eyebrow-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 24px;
  line-height: 1;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--headline-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 60px;
  line-height: 1;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--headline-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 55px;
  line-height: 1;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--headline-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 55px;
  line-height: 1;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--headline-4 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 50px;
  line-height: 1.1;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--headline-5 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 45px;
  line-height: 1;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--headline-6 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 40px;
  line-height: 1;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--headline-7 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 35px;
  line-height: 1;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--headlineAlt-1 {
  color: #2B2B2B;
  font-size: 64px;
  line-height: 1;
  letter-spacing: -0.3px;
  font-weight: 400;
}

.emotion-1 .amp-cms--headlineAlt-2 {
  color: #2B2B2B;
  font-size: 54px;
  line-height: 1;
  letter-spacing: -0.3px;
  font-weight: 400;
}

.emotion-1 .amp-cms--headlineAlt-3 {
  color: #2B2B2B;
  font-size: 34px;
  line-height: 1;
  letter-spacing: -0.3px;
  font-weight: 400;
}

.emotion-1 .amp-cms--headlineAlt-4 {
  color: #2B2B2B;
  font-size: 24px;
  line-height: 1.4166666666666667;
  letter-spacing: -0.3px;
  font-weight: 400;
}

.emotion-1 .amp-cms--headlineAlt-5 {
  color: #2B2B2B;
  font-size: 14px;
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-1 .amp-cms--headlineAlt-6 {
  color: #2B2B2B;
  font-size: 14px;
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-1 .amp-cms--headlineAlt-7 {
  color: #2B2B2B;
  font-size: 14px;
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-1 .amp-cms--promo-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 60px;
  line-height: 1;
  letter-spacing: 0px;
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-1 .amp-cms--promo-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 40px;
  line-height: 1;
  letter-spacing: 0px;
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-1 .amp-cms--promoAlt-1 {
  color: #2B2B2B;
  font-size: 14px;
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 600;
}

.emotion-1 .amp-cms--subhead-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 22px;
  line-height: 1.0909090909090908;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--subhead-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 20px;
  line-height: 1.1;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--subhead-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 18px;
  line-height: 1.1111111111111112;
  letter-spacing: 0;
}

.emotion-2 {
  display: grid;
  grid-template-columns: 1fr 1fr;
}

.emotion-3 {
  position: relative;
  width: 100%;
}

.emotion-4 {
  cursor: pointer;
  height: 100%;
  width: 100%;
}

.emotion-5 {
  background: transparent;
  width: 100%!important;
  height: min(106.25vw, 340px)!important;
  -webkit-background-size: cover;
  background-size: cover;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: end;
  -webkit-box-align: end;
  -ms-flex-align: end;
  align-items: end;
  box-sizing: border-box;
}

.emotion-6 {
  width: 100%!important;
  height: auto!important;
  -webkit-background-size: cover;
  background-size: cover;
  display: -webkit-box!important;
  display: -webkit-flex!important;
  display: -ms-flexbox!important;
  display: flex!important;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: end;
  -webkit-box-align: end;
  -ms-flex-align: end;
  align-items: end;
  box-sizing: border-box;
  padding: 20px 15px;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  gap: 20px;
}

.emotion-7 {
  box-sizing: border-box;
  height: auto!important;
  width: 100%!important;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  gap: 15px;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
}

.emotion-8 .amp-cms--p {
  padding: 0;
  margin: 0;
  line-height: 0;
}

.emotion-8 .amp-cms--p>span {
  display: inline;
  white-space: break-spaces;
}

.emotion-8 .amp-cms--p a {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  position: relative;
  z-index: 2;
  pointer-events: auto;
}

.emotion-8 .amp-cms--p a:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-8 .amp-cms--legal-copy {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, 2.666666666666667vw);
  line-height: 1.4;
  letter-spacing: 0;
}

.emotion-8 sup {
  vertical-align: top;
  display: inline-block;
  margin-top: -0.25ex;
}

.emotion-8 sub {
  vertical-align: bottom;
  display: inline-block;
  margin-bottom: -0.25ex;
}

.emotion-8 .amp-cms--f-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 2.933333333333333vw;
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-8 .amp-cms--f-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 2.933333333333333vw;
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-8 .amp-cms--f-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 2.933333333333333vw;
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-8 .amp-cms--fn-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 2.933333333333333vw;
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-8 .amp-cms--body-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, 4.8vw);
  line-height: 1.2777777777777777;
  letter-spacing: 0;
}

.emotion-8 .amp-cms--body-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, 4.266666666666667vw);
  line-height: 1.25;
  letter-spacing: 0;
}

.emotion-8 .amp-cms--body-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, 3.733333333333334vw);
  line-height: 1.2857142857142858;
  letter-spacing: 0;
}

.emotion-8 .amp-cms--body-4 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, 3.2vw);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-8 .amp-cms--body-5 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, 2.666666666666667vw);
  line-height: 1.4;
  letter-spacing: 0;
}

.emotion-8 .amp-cms--eyebrow-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, 8vw);
  line-height: 1;
  letter-spacing: 0;
}

.emotion-8 .amp-cms--eyebrow-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, 6.933333333333333vw);
  line-height: 1;
  letter-spacing: 0;
}

.emotion-8 .amp-cms--eyebrow-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, 6.4vw);
  line-height: 1;
  letter-spacing: 0;
}

.emotion-8 .amp-cms--headline-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, 16vw);
  line-height: 1;
  letter-spacing: 0;
}

.emotion-8 .amp-cms--headline-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, 14.666666666666666vw);
  line-height: 1;
  letter-spacing: 0;
}

.emotion-8 .amp-cms--headline-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, 14.666666666666666vw);
  line-height: 1;
  letter-spacing: 0;
}

.emotion-8 .amp-cms--headline-4 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, 13.333333333333334vw);
  line-height: 1.1;
  letter-spacing: 0;
}

.emotion-8 .amp-cms--headline-5 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, 12vw);
  line-height: 1;
  letter-spacing: 0;
}

.emotion-8 .amp-cms--headline-6 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, 10.666666666666668vw);
  line-height: 1;
  letter-spacing: 0;
}

.emotion-8 .amp-cms--headline-7 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, 9.333333333333334vw);
  line-height: 1;
  letter-spacing: 0;
}

.emotion-8 .amp-cms--headlineAlt-1 {
  color: #2B2B2B;
  font-size: max(13px, 17.066666666666666vw);
  line-height: 1;
  letter-spacing: -0.07999999999999999vw;
  font-weight: 400;
}

.emotion-8 .amp-cms--headlineAlt-2 {
  color: #2B2B2B;
  font-size: max(13px, 14.399999999999999vw);
  line-height: 1;
  letter-spacing: -0.07999999999999999vw;
  font-weight: 400;
}

.emotion-8 .amp-cms--headlineAlt-3 {
  color: #2B2B2B;
  font-size: max(13px, 9.066666666666666vw);
  line-height: 1;
  letter-spacing: -0.07999999999999999vw;
  font-weight: 400;
}

.emotion-8 .amp-cms--headlineAlt-4 {
  color: #2B2B2B;
  font-size: max(13px, 6.4vw);
  line-height: 1.4166666666666667;
  letter-spacing: -0.07999999999999999vw;
  font-weight: 400;
}

.emotion-8 .amp-cms--headlineAlt-5 {
  color: #2B2B2B;
  font-size: 3.733333333333334vw;
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-8 .amp-cms--headlineAlt-6 {
  color: #2B2B2B;
  font-size: 3.733333333333334vw;
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-8 .amp-cms--headlineAlt-7 {
  color: #2B2B2B;
  font-size: 3.733333333333334vw;
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-8 .amp-cms--promo-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(13px, 16vw);
  line-height: 1;
  letter-spacing: 0vw;
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-8 .amp-cms--promo-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(13px, 10.666666666666668vw);
  line-height: 1;
  letter-spacing: 0vw;
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-8 .amp-cms--promoAlt-1 {
  color: #2B2B2B;
  font-size: 3.733333333333334vw;
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 600;
}

.emotion-8 .amp-cms--subhead-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, 5.866666666666666vw);
  line-height: 1.0909090909090908;
  letter-spacing: 0;
}

.emotion-8 .amp-cms--subhead-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, 5.333333333333334vw);
  line-height: 1.1;
  letter-spacing: 0;
}

.emotion-8 .amp-cms--subhead-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, 4.8vw);
  line-height: 1.1111111111111112;
  letter-spacing: 0;
}

<div>
  <div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      class="emotion-0"
    >
      <div
        class="emotion-1"
      >
        <div>
          <p
            class="amp-cms--p"
            style="text-align:left;"
          >
            <span
              class="amp-cms--headline-3"
            >
              OPTIONAL HEADLINE
            </span>
          </p>
        </div>
      </div>
      <div
        class="emotion-2"
      >
        <div
          class="emotion-3"
        >
          <a
            aria-label="new arrivals"
            class="emotion-4"
            data-testid="conditional-link"
            href="www.google.com"
            target="_self"
          >
            <div
              aria-label="mobile alt text"
              class="emotion-5"
              height="0"
              role="img"
              width="0"
            >
              <div
                class="emotion-6"
              >
                <div
                  class="emotion-7"
                >
                  <div
                    class="emotion-8"
                  >
                    <div>
                      <p
                        class="amp-cms--p"
                        style="text-align:left;"
                      >
                        <span
                          class="amp-cms--body-1"
                        >
                          Mobile Card
                        </span>
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </a>
        </div>
        <div
          class="emotion-3"
        >
          <a
            aria-label="new arrivals"
            class="emotion-4"
            data-testid="conditional-link"
            href="www.google.com"
            target="_self"
          >
            <div
              aria-label="mobile alt text"
              class="emotion-5"
              height="0"
              role="img"
              width="0"
            >
              <div
                class="emotion-6"
              >
                <div
                  class="emotion-7"
                >
                  <div
                    class="emotion-8"
                  >
                    <div>
                      <p
                        class="amp-cms--p"
                        style="text-align:left;"
                      >
                        <span
                          class="amp-cms--body-1"
                        >
                          Mobile Card
                        </span>
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </a>
        </div>
        <div
          class="emotion-3"
        >
          <a
            aria-label="new arrivals"
            class="emotion-4"
            data-testid="conditional-link"
            href="www.google.com"
            target="_self"
          >
            <div
              aria-label="mobile alt text"
              class="emotion-5"
              height="0"
              role="img"
              width="0"
            >
              <div
                class="emotion-6"
              >
                <div
                  class="emotion-7"
                >
                  <div
                    class="emotion-8"
                  >
                    <div>
                      <p
                        class="amp-cms--p"
                        style="text-align:left;"
                      >
                        <span
                          class="amp-cms--body-1"
                        >
                          Mobile Card
                        </span>
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </a>
        </div>
        <div
          class="emotion-3"
        >
          <a
            aria-label="new arrivals"
            class="emotion-4"
            data-testid="conditional-link"
            href="www.google.com"
            target="_self"
          >
            <div
              aria-label="mobile alt text"
              class="emotion-5"
              height="0"
              role="img"
              width="0"
            >
              <div
                class="emotion-6"
              >
                <div
                  class="emotion-7"
                >
                  <div
                    class="emotion-8"
                  >
                    <div>
                      <p
                        class="amp-cms--p"
                        style="text-align:left;"
                      >
                        <span
                          class="amp-cms--body-1"
                        >
                          Mobile Card
                        </span>
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </a>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`Gap FeaturedCategories full bleed mobile should match snapshots for video 1`] = `
.emotion-0 {
  height: 100%;
  padding: 0;
}

.emotion-1 {
  margin-bottom: 15px;
  margin-left: 15px;
  margin: 0 0 15px 16px;
}

.emotion-1 .amp-cms--p {
  padding: 0;
  margin: 0;
  line-height: 0;
}

.emotion-1 .amp-cms--p>span {
  display: inline;
  white-space: break-spaces;
}

.emotion-1 .amp-cms--p a {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  position: relative;
  z-index: 2;
  pointer-events: auto;
}

.emotion-1 .amp-cms--p a:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-1 .amp-cms--legal-copy {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 10px;
  line-height: 1.4;
  letter-spacing: 0;
}

.emotion-1 sup {
  vertical-align: top;
  display: inline-block;
  margin-top: -0.25ex;
}

.emotion-1 sub {
  vertical-align: bottom;
  display: inline-block;
  margin-bottom: -0.25ex;
}

.emotion-1 .amp-cms--f-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 11px;
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--f-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 11px;
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--f-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 11px;
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--fn-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 11px;
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--body-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 18px;
  line-height: 1.2777777777777777;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--body-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 16px;
  line-height: 1.25;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--body-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 14px;
  line-height: 1.2857142857142858;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--body-4 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--body-5 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 10px;
  line-height: 1.4;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--eyebrow-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 30px;
  line-height: 1;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--eyebrow-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 26px;
  line-height: 1;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--eyebrow-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 24px;
  line-height: 1;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--headline-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 60px;
  line-height: 1;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--headline-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 55px;
  line-height: 1;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--headline-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 55px;
  line-height: 1;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--headline-4 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 50px;
  line-height: 1.1;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--headline-5 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 45px;
  line-height: 1;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--headline-6 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 40px;
  line-height: 1;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--headline-7 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 35px;
  line-height: 1;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--headlineAlt-1 {
  color: #2B2B2B;
  font-size: 64px;
  line-height: 1;
  letter-spacing: -0.3px;
  font-weight: 400;
}

.emotion-1 .amp-cms--headlineAlt-2 {
  color: #2B2B2B;
  font-size: 54px;
  line-height: 1;
  letter-spacing: -0.3px;
  font-weight: 400;
}

.emotion-1 .amp-cms--headlineAlt-3 {
  color: #2B2B2B;
  font-size: 34px;
  line-height: 1;
  letter-spacing: -0.3px;
  font-weight: 400;
}

.emotion-1 .amp-cms--headlineAlt-4 {
  color: #2B2B2B;
  font-size: 24px;
  line-height: 1.4166666666666667;
  letter-spacing: -0.3px;
  font-weight: 400;
}

.emotion-1 .amp-cms--headlineAlt-5 {
  color: #2B2B2B;
  font-size: 14px;
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-1 .amp-cms--headlineAlt-6 {
  color: #2B2B2B;
  font-size: 14px;
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-1 .amp-cms--headlineAlt-7 {
  color: #2B2B2B;
  font-size: 14px;
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-1 .amp-cms--promo-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 60px;
  line-height: 1;
  letter-spacing: 0px;
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-1 .amp-cms--promo-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 40px;
  line-height: 1;
  letter-spacing: 0px;
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-1 .amp-cms--promoAlt-1 {
  color: #2B2B2B;
  font-size: 14px;
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 600;
}

.emotion-1 .amp-cms--subhead-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 22px;
  line-height: 1.0909090909090908;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--subhead-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 20px;
  line-height: 1.1;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--subhead-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 18px;
  line-height: 1.1111111111111112;
  letter-spacing: 0;
}

.emotion-2 {
  display: grid;
  grid-template-columns: 1fr 1fr;
}

.emotion-3 {
  position: relative;
  width: 100%;
}

.emotion-4 {
  cursor: pointer;
  height: 100%;
  width: 100%;
}

.emotion-5 {
  position: relative;
  height: min(106.25vw, 340px)!important;
  pointer-events: auto;
  width: 100%!important;
  -webkit-background-size: cover;
  background-size: cover;
}

.emotion-5>div>img {
  height: min(106.25vw, 340px)!important;
}

.emotion-6 {
  width: -webkit-fit-content;
  width: -moz-fit-content;
  width: fit-content;
  position: relative;
  width: 100%;
}

.emotion-7 {
  display: none;
  gap: 15px;
  position: absolute;
  max-width: -webkit-max-content;
  max-width: -moz-max-content;
  max-width: max-content;
  max-height: -webkit-max-content;
  max-height: -moz-max-content;
  max-height: max-content;
  margin: 10px;
  right: 0;
  bottom: 0;
  height: 28px;
}

.emotion-7>button {
  left: 0;
  bottom: 0;
  z-index: 4;
  position: relative;
  width: 28px;
  height: 28px;
}

.emotion-7>div>button {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  left: 0;
  bottom: 0;
  z-index: 4;
  position: relative;
}

.emotion-7>div>div {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  left: 0;
  z-index: 3;
  bottom: 0;
}

.emotion-7>div>div:before {
  left: 0;
  bottom: -28px;
  width: 28px;
  height: 28px;
  border-radius: 0;
}

.emotion-8 {
  background: none;
  border: none;
  padding: 0;
  height: 24px;
  margin: 0;
  position: absolute;
  bottom: 11px;
  right: 112px;
  width: 24px;
  height: 24px;
}

.emotion-8:is(:not(:focus, :active, :hover, :focus-within)) {
  opacity: 0.64;
}

.emotion-8:is(:hover) {
  opacity: 1;
}

@media (min-width: 767px) {
  .emotion-8 {
    right: 120px;
  }
}

.emotion-9 {
  display: inline-block;
  height: 28px;
  width: 28px;
  min-height: 28px;
  min-width: 28px;
}

.emotion-9 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-9 svg:hover rect {
  fill: #031BA1;
}

.emotion-9 svg:hover path {
  fill: #FFFFFF;
}

.emotion-10 {
  box-sizing: border-box;
  background: none;
  border: none;
  padding: 0;
  position: relative;
  width: 100%;
  height: 0px;
}

.emotion-10:focus div {
  opacity: 0.84;
  -webkit-transition: opacity 200ms;
  transition: opacity 200ms;
  border-radius: 20px 20px 0 0;
}

.emotion-10 div {
  border-radius: 20px 20px 0 0;
}

.emotion-10 .keepOpen,
.emotion-10 .staysOpen {
  opacity: 0.84;
  -webkit-transition: opacity 200ms;
  transition: opacity 200ms;
  z-index: 2;
}

.emotion-11 {
  background: none;
  border: none;
  padding: 0;
  height: 24px;
  margin: 0;
  position: absolute;
  bottom: 11px;
  right: 72px;
  z-index: 11;
}

.emotion-11:is(:not(:focus, :active, :hover, :focus-within)) {
  opacity: 0.64;
}

.emotion-11:is(:hover) {
  opacity: 1;
}

@media (min-width: 767px) {
  .emotion-11 {
    right: 80px;
  }
}

.emotion-13 {
  opacity: 0;
  -webkit-transition: opacity 200ms;
  transition: opacity 200ms;
  box-sizing: border-box;
  background: #ffffff;
  border: none;
  padding: 0;
  position: absolute;
  width: 28px;
  height: 60px;
  bottom: 35px;
  right: 72px;
  border-radius: 20px 20px 0 0;
}

.emotion-13:before {
  content: "";
  height: 24px;
  background: #ffffff;
  display: block;
  position: absolute;
  bottom: -24px;
  width: 24px;
  border-bottom-right-radius: 50%;
  border-bottom-left-radius: 50%;
}

.emotion-14 {
  position: absolute;
  z-index: 0;
  width: 100%!important;
  height: min(106.25vw, 340px)!important;
  top: 0;
  pointer-events: none;
  -webkit-background-size: cover;
  background-size: cover;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: end;
  -webkit-box-align: end;
  -ms-flex-align: end;
  align-items: end;
  box-sizing: border-box;
}

.emotion-15 {
  width: 100%!important;
  height: auto!important;
  -webkit-background-size: cover;
  background-size: cover;
  display: -webkit-box!important;
  display: -webkit-flex!important;
  display: -ms-flexbox!important;
  display: flex!important;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: end;
  -webkit-box-align: end;
  -ms-flex-align: end;
  align-items: end;
  box-sizing: border-box;
  padding: 20px 15px;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  gap: 20px;
}

.emotion-16 {
  box-sizing: border-box;
  height: auto!important;
  width: 100%!important;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  gap: 15px;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
}

.emotion-17 .amp-cms--p {
  padding: 0;
  margin: 0;
  line-height: 0;
}

.emotion-17 .amp-cms--p>span {
  display: inline;
  white-space: break-spaces;
}

.emotion-17 .amp-cms--p a {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  position: relative;
  z-index: 2;
  pointer-events: auto;
}

.emotion-17 .amp-cms--p a:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-17 .amp-cms--legal-copy {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, 2.666666666666667vw);
  line-height: 1.4;
  letter-spacing: 0;
}

.emotion-17 sup {
  vertical-align: top;
  display: inline-block;
  margin-top: -0.25ex;
}

.emotion-17 sub {
  vertical-align: bottom;
  display: inline-block;
  margin-bottom: -0.25ex;
}

.emotion-17 .amp-cms--f-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 2.933333333333333vw;
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-17 .amp-cms--f-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 2.933333333333333vw;
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-17 .amp-cms--f-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 2.933333333333333vw;
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-17 .amp-cms--fn-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 2.933333333333333vw;
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-17 .amp-cms--body-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, 4.8vw);
  line-height: 1.2777777777777777;
  letter-spacing: 0;
}

.emotion-17 .amp-cms--body-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, 4.266666666666667vw);
  line-height: 1.25;
  letter-spacing: 0;
}

.emotion-17 .amp-cms--body-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, 3.733333333333334vw);
  line-height: 1.2857142857142858;
  letter-spacing: 0;
}

.emotion-17 .amp-cms--body-4 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, 3.2vw);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-17 .amp-cms--body-5 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, 2.666666666666667vw);
  line-height: 1.4;
  letter-spacing: 0;
}

.emotion-17 .amp-cms--eyebrow-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, 8vw);
  line-height: 1;
  letter-spacing: 0;
}

.emotion-17 .amp-cms--eyebrow-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, 6.933333333333333vw);
  line-height: 1;
  letter-spacing: 0;
}

.emotion-17 .amp-cms--eyebrow-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, 6.4vw);
  line-height: 1;
  letter-spacing: 0;
}

.emotion-17 .amp-cms--headline-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, 16vw);
  line-height: 1;
  letter-spacing: 0;
}

.emotion-17 .amp-cms--headline-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, 14.666666666666666vw);
  line-height: 1;
  letter-spacing: 0;
}

.emotion-17 .amp-cms--headline-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, 14.666666666666666vw);
  line-height: 1;
  letter-spacing: 0;
}

.emotion-17 .amp-cms--headline-4 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, 13.333333333333334vw);
  line-height: 1.1;
  letter-spacing: 0;
}

.emotion-17 .amp-cms--headline-5 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, 12vw);
  line-height: 1;
  letter-spacing: 0;
}

.emotion-17 .amp-cms--headline-6 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, 10.666666666666668vw);
  line-height: 1;
  letter-spacing: 0;
}

.emotion-17 .amp-cms--headline-7 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, 9.333333333333334vw);
  line-height: 1;
  letter-spacing: 0;
}

.emotion-17 .amp-cms--headlineAlt-1 {
  color: #2B2B2B;
  font-size: max(13px, 17.066666666666666vw);
  line-height: 1;
  letter-spacing: -0.07999999999999999vw;
  font-weight: 400;
}

.emotion-17 .amp-cms--headlineAlt-2 {
  color: #2B2B2B;
  font-size: max(13px, 14.399999999999999vw);
  line-height: 1;
  letter-spacing: -0.07999999999999999vw;
  font-weight: 400;
}

.emotion-17 .amp-cms--headlineAlt-3 {
  color: #2B2B2B;
  font-size: max(13px, 9.066666666666666vw);
  line-height: 1;
  letter-spacing: -0.07999999999999999vw;
  font-weight: 400;
}

.emotion-17 .amp-cms--headlineAlt-4 {
  color: #2B2B2B;
  font-size: max(13px, 6.4vw);
  line-height: 1.4166666666666667;
  letter-spacing: -0.07999999999999999vw;
  font-weight: 400;
}

.emotion-17 .amp-cms--headlineAlt-5 {
  color: #2B2B2B;
  font-size: 3.733333333333334vw;
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-17 .amp-cms--headlineAlt-6 {
  color: #2B2B2B;
  font-size: 3.733333333333334vw;
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-17 .amp-cms--headlineAlt-7 {
  color: #2B2B2B;
  font-size: 3.733333333333334vw;
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-17 .amp-cms--promo-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(13px, 16vw);
  line-height: 1;
  letter-spacing: 0vw;
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-17 .amp-cms--promo-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(13px, 10.666666666666668vw);
  line-height: 1;
  letter-spacing: 0vw;
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-17 .amp-cms--promoAlt-1 {
  color: #2B2B2B;
  font-size: 3.733333333333334vw;
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 600;
}

.emotion-17 .amp-cms--subhead-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, 5.866666666666666vw);
  line-height: 1.0909090909090908;
  letter-spacing: 0;
}

.emotion-17 .amp-cms--subhead-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, 5.333333333333334vw);
  line-height: 1.1;
  letter-spacing: 0;
}

.emotion-17 .amp-cms--subhead-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, 4.8vw);
  line-height: 1.1111111111111112;
  letter-spacing: 0;
}

<div>
  <div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      class="emotion-0"
    >
      <div
        class="emotion-1"
      >
        <div>
          <p
            class="amp-cms--p"
            style="text-align:left;"
          >
            <span
              class="amp-cms--headline-3"
            >
              OPTIONAL HEADLINE
            </span>
          </p>
        </div>
      </div>
      <div
        class="emotion-2"
      >
        <div
          class="emotion-3"
        >
          <a
            aria-label="new arrivals"
            class="emotion-4"
            data-testid="conditional-link"
            href="www.google.com"
            target="_self"
          >
            <div
              class="emotion-5"
            >
              <div
                class="emotion-6"
                data-testid="videocomponent-container"
              >
                <div
                  style="position: relative;"
                >
                  <img
                    fetchpriority="high"
                    src="https://2hmc8flz4ofg1dy89cc38f2xm.staging.bigcontent.io/i/athleta/Card 3 - MobileL?fmt=auto"
                    style="height: 100%; left: 0px; object-fit: cover; position: absolute; top: 0px; width: 100%;"
                  />
                  <h1
                    role="presentation"
                  >
                    ReactPlayer
                  </h1>
                </div>
                <div
                  class="player-custom-controls emotion-7"
                  data-testid="player-custom-controls"
                >
                  <button
                    aria-label="Play"
                    aria-pressed="false"
                    class="emotion-8"
                  >
                    <span
                      aria-hidden="true"
                      class="emotion-9"
                    >
                      <svg
                        fill="none"
                        viewBox="0 0 28 28"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <g
                          filter="url(#filter0_b_13041_121152)"
                        >
                          <rect
                            fill="#F7F7F7"
                            height="28"
                            width="28"
                          />
                          <path
                            d="M17.615 14.18L10.375 18.36V10L17.615 14.18Z"
                            fill="#2B2B2B"
                          />
                        </g>
                        <defs>
                          <filter
                            color-interpolation-filters="sRGB"
                            filterUnits="userSpaceOnUse"
                            height="104"
                            id="filter0_b_13041_121152"
                            width="104"
                            x="-38"
                            y="-38"
                          >
                            <feflood
                              flood-opacity="0"
                              result="BackgroundImageFix"
                            />
                            <fegaussianblur
                              in="BackgroundImageFix"
                              stdDeviation="19"
                            />
                            <fecomposite
                              in2="SourceAlpha"
                              operator="in"
                              result="effect1_backgroundBlur_13041_121152"
                            />
                            <feblend
                              in="SourceGraphic"
                              in2="effect1_backgroundBlur_13041_121152"
                              mode="normal"
                              result="shape"
                            />
                          </filter>
                        </defs>
                      </svg>
                    </span>
                  </button>
                  <div
                    class="emotion-10"
                    data-testid="styled-mute-controls"
                  >
                    <button
                      aria-label="Mute"
                      aria-pressed="false"
                      class="emotion-11"
                    >
                      <span
                        aria-hidden="true"
                        class="emotion-9"
                      >
                        <svg
                          fill="none"
                          viewBox="0 0 28 28"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <g
                            filter="url(#filter0_b_8973_3432)"
                          >
                            <rect
                              fill="#F7F7F7"
                              height="28"
                              width="28"
                            />
                            <path
                              d="M19.3892 18.3292L8.01562 10.3919L8.62162 9.52363L19.9962 17.4606L19.3892 18.3292Z"
                              fill="#2B2B2B"
                            />
                            <path
                              d="M18.0364 17.7442L17.2016 17.0927C17.7631 16.3996 18.1375 15.574 18.289 14.6949C18.3934 14.0071 18.3521 13.3052 18.1677 12.6344C17.9764 11.9108 17.6506 11.2296 17.2071 10.6267L18.0308 9.96152C18.5676 10.6799 18.9614 11.4946 19.191 12.3615C19.4136 13.1749 19.4623 14.0261 19.3344 14.8596C19.1558 15.9164 18.7089 16.9096 18.0364 17.7442Z"
                              fill="#2B2B2B"
                            />
                            <path
                              d="M15.8761 16.8675L14.9938 16.2843C15.344 15.7838 15.5835 15.2147 15.6969 14.6144C15.8102 14.0141 15.7946 13.3963 15.651 12.8025C15.528 12.2724 15.3112 11.7688 15.0106 11.3152L15.8606 10.6838C16.2483 11.2521 16.5268 11.8878 16.6816 12.5581C16.857 13.2927 16.8758 14.056 16.7371 14.7984C16.5983 15.5409 16.305 16.2458 15.8761 16.8675Z"
                              fill="#2B2B2B"
                            />
                            <path
                              d="M10.3134 14.9479H8.32031V12.5662H10.3134L13.5558 9.58809V17.9256L10.3134 14.9479Z"
                              fill="#2B2B2B"
                            />
                            <path
                              d="M13.7307 18.3273L10.2429 15.1244H8.14219V12.3898H10.2429L13.7307 9.18652V18.3273ZM8.49513 14.7715H10.3802L13.3774 17.5244V9.98982L10.3802 12.7428H8.49513V14.7715Z"
                              fill="#2B2B2B"
                            />
                          </g>
                          <defs>
                            <filter
                              color-interpolation-filters="sRGB"
                              filterUnits="userSpaceOnUse"
                              height="104"
                              id="filter0_b_8973_3432"
                              width="104"
                              x="-38"
                              y="-38"
                            >
                              <feflood
                                flood-opacity="0"
                                result="BackgroundImageFix"
                              />
                              <fegaussianblur
                                in="BackgroundImageFix"
                                stdDeviation="19"
                              />
                              <fecomposite
                                in2="SourceAlpha"
                                operator="in"
                                result="effect1_backgroundBlur_8973_3432"
                              />
                              <feblend
                                in="SourceGraphic"
                                in2="effect1_backgroundBlur_8973_3432"
                                mode="normal"
                                result="shape"
                              />
                            </filter>
                          </defs>
                        </svg>
                      </span>
                    </button>
                    <div
                      class="emotion-13"
                      data-testid="styled-input-volume"
                      style="background: rgb(255, 255, 255);"
                    >
                      <div
                        role="presentation"
                      />
                    </div>
                  </div>
                </div>
              </div>
              <div
                class="emotion-14"
              >
                <div
                  class="emotion-15"
                >
                  <div
                    class="emotion-16"
                  >
                    <div
                      class="emotion-17"
                    >
                      <div>
                        <p
                          class="amp-cms--p"
                          style="text-align:left;"
                        >
                          <span
                            class="amp-cms--body-1"
                          >
                            Mobile Card
                          </span>
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </a>
        </div>
        <div
          class="emotion-3"
        >
          <a
            aria-label="new arrivals"
            class="emotion-4"
            data-testid="conditional-link"
            href="www.google.com"
            target="_self"
          >
            <div
              class="emotion-5"
            >
              <div
                class="emotion-6"
                data-testid="videocomponent-container"
              >
                <div
                  style="position: relative;"
                >
                  <img
                    fetchpriority="high"
                    src="https://2hmc8flz4ofg1dy89cc38f2xm.staging.bigcontent.io/i/athleta/Card 3 - MobileL?fmt=auto"
                    style="height: 100%; left: 0px; object-fit: cover; position: absolute; top: 0px; width: 100%;"
                  />
                  <h1
                    role="presentation"
                  >
                    ReactPlayer
                  </h1>
                </div>
                <div
                  class="player-custom-controls emotion-7"
                  data-testid="player-custom-controls"
                >
                  <button
                    aria-label="Play"
                    aria-pressed="false"
                    class="emotion-8"
                  >
                    <span
                      aria-hidden="true"
                      class="emotion-9"
                    >
                      <svg
                        fill="none"
                        viewBox="0 0 28 28"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <g
                          filter="url(#filter0_b_13041_121152)"
                        >
                          <rect
                            fill="#F7F7F7"
                            height="28"
                            width="28"
                          />
                          <path
                            d="M17.615 14.18L10.375 18.36V10L17.615 14.18Z"
                            fill="#2B2B2B"
                          />
                        </g>
                        <defs>
                          <filter
                            color-interpolation-filters="sRGB"
                            filterUnits="userSpaceOnUse"
                            height="104"
                            id="filter0_b_13041_121152"
                            width="104"
                            x="-38"
                            y="-38"
                          >
                            <feflood
                              flood-opacity="0"
                              result="BackgroundImageFix"
                            />
                            <fegaussianblur
                              in="BackgroundImageFix"
                              stdDeviation="19"
                            />
                            <fecomposite
                              in2="SourceAlpha"
                              operator="in"
                              result="effect1_backgroundBlur_13041_121152"
                            />
                            <feblend
                              in="SourceGraphic"
                              in2="effect1_backgroundBlur_13041_121152"
                              mode="normal"
                              result="shape"
                            />
                          </filter>
                        </defs>
                      </svg>
                    </span>
                  </button>
                  <div
                    class="emotion-10"
                    data-testid="styled-mute-controls"
                  >
                    <button
                      aria-label="Mute"
                      aria-pressed="false"
                      class="emotion-11"
                    >
                      <span
                        aria-hidden="true"
                        class="emotion-9"
                      >
                        <svg
                          fill="none"
                          viewBox="0 0 28 28"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <g
                            filter="url(#filter0_b_8973_3432)"
                          >
                            <rect
                              fill="#F7F7F7"
                              height="28"
                              width="28"
                            />
                            <path
                              d="M19.3892 18.3292L8.01562 10.3919L8.62162 9.52363L19.9962 17.4606L19.3892 18.3292Z"
                              fill="#2B2B2B"
                            />
                            <path
                              d="M18.0364 17.7442L17.2016 17.0927C17.7631 16.3996 18.1375 15.574 18.289 14.6949C18.3934 14.0071 18.3521 13.3052 18.1677 12.6344C17.9764 11.9108 17.6506 11.2296 17.2071 10.6267L18.0308 9.96152C18.5676 10.6799 18.9614 11.4946 19.191 12.3615C19.4136 13.1749 19.4623 14.0261 19.3344 14.8596C19.1558 15.9164 18.7089 16.9096 18.0364 17.7442Z"
                              fill="#2B2B2B"
                            />
                            <path
                              d="M15.8761 16.8675L14.9938 16.2843C15.344 15.7838 15.5835 15.2147 15.6969 14.6144C15.8102 14.0141 15.7946 13.3963 15.651 12.8025C15.528 12.2724 15.3112 11.7688 15.0106 11.3152L15.8606 10.6838C16.2483 11.2521 16.5268 11.8878 16.6816 12.5581C16.857 13.2927 16.8758 14.056 16.7371 14.7984C16.5983 15.5409 16.305 16.2458 15.8761 16.8675Z"
                              fill="#2B2B2B"
                            />
                            <path
                              d="M10.3134 14.9479H8.32031V12.5662H10.3134L13.5558 9.58809V17.9256L10.3134 14.9479Z"
                              fill="#2B2B2B"
                            />
                            <path
                              d="M13.7307 18.3273L10.2429 15.1244H8.14219V12.3898H10.2429L13.7307 9.18652V18.3273ZM8.49513 14.7715H10.3802L13.3774 17.5244V9.98982L10.3802 12.7428H8.49513V14.7715Z"
                              fill="#2B2B2B"
                            />
                          </g>
                          <defs>
                            <filter
                              color-interpolation-filters="sRGB"
                              filterUnits="userSpaceOnUse"
                              height="104"
                              id="filter0_b_8973_3432"
                              width="104"
                              x="-38"
                              y="-38"
                            >
                              <feflood
                                flood-opacity="0"
                                result="BackgroundImageFix"
                              />
                              <fegaussianblur
                                in="BackgroundImageFix"
                                stdDeviation="19"
                              />
                              <fecomposite
                                in2="SourceAlpha"
                                operator="in"
                                result="effect1_backgroundBlur_8973_3432"
                              />
                              <feblend
                                in="SourceGraphic"
                                in2="effect1_backgroundBlur_8973_3432"
                                mode="normal"
                                result="shape"
                              />
                            </filter>
                          </defs>
                        </svg>
                      </span>
                    </button>
                    <div
                      class="emotion-13"
                      data-testid="styled-input-volume"
                      style="background: rgb(255, 255, 255);"
                    >
                      <div
                        role="presentation"
                      />
                    </div>
                  </div>
                </div>
              </div>
              <div
                class="emotion-14"
              >
                <div
                  class="emotion-15"
                >
                  <div
                    class="emotion-16"
                  >
                    <div
                      class="emotion-17"
                    >
                      <div>
                        <p
                          class="amp-cms--p"
                          style="text-align:left;"
                        >
                          <span
                            class="amp-cms--body-1"
                          >
                            Mobile Card
                          </span>
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </a>
        </div>
        <div
          class="emotion-3"
        >
          <a
            aria-label="new arrivals"
            class="emotion-4"
            data-testid="conditional-link"
            href="www.google.com"
            target="_self"
          >
            <div
              class="emotion-5"
            >
              <div
                class="emotion-6"
                data-testid="videocomponent-container"
              >
                <div
                  style="position: relative;"
                >
                  <img
                    fetchpriority="high"
                    src="https://2hmc8flz4ofg1dy89cc38f2xm.staging.bigcontent.io/i/athleta/Card 3 - MobileL?fmt=auto"
                    style="height: 100%; left: 0px; object-fit: cover; position: absolute; top: 0px; width: 100%;"
                  />
                  <h1
                    role="presentation"
                  >
                    ReactPlayer
                  </h1>
                </div>
                <div
                  class="player-custom-controls emotion-7"
                  data-testid="player-custom-controls"
                >
                  <button
                    aria-label="Play"
                    aria-pressed="false"
                    class="emotion-8"
                  >
                    <span
                      aria-hidden="true"
                      class="emotion-9"
                    >
                      <svg
                        fill="none"
                        viewBox="0 0 28 28"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <g
                          filter="url(#filter0_b_13041_121152)"
                        >
                          <rect
                            fill="#F7F7F7"
                            height="28"
                            width="28"
                          />
                          <path
                            d="M17.615 14.18L10.375 18.36V10L17.615 14.18Z"
                            fill="#2B2B2B"
                          />
                        </g>
                        <defs>
                          <filter
                            color-interpolation-filters="sRGB"
                            filterUnits="userSpaceOnUse"
                            height="104"
                            id="filter0_b_13041_121152"
                            width="104"
                            x="-38"
                            y="-38"
                          >
                            <feflood
                              flood-opacity="0"
                              result="BackgroundImageFix"
                            />
                            <fegaussianblur
                              in="BackgroundImageFix"
                              stdDeviation="19"
                            />
                            <fecomposite
                              in2="SourceAlpha"
                              operator="in"
                              result="effect1_backgroundBlur_13041_121152"
                            />
                            <feblend
                              in="SourceGraphic"
                              in2="effect1_backgroundBlur_13041_121152"
                              mode="normal"
                              result="shape"
                            />
                          </filter>
                        </defs>
                      </svg>
                    </span>
                  </button>
                  <div
                    class="emotion-10"
                    data-testid="styled-mute-controls"
                  >
                    <button
                      aria-label="Mute"
                      aria-pressed="false"
                      class="emotion-11"
                    >
                      <span
                        aria-hidden="true"
                        class="emotion-9"
                      >
                        <svg
                          fill="none"
                          viewBox="0 0 28 28"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <g
                            filter="url(#filter0_b_8973_3432)"
                          >
                            <rect
                              fill="#F7F7F7"
                              height="28"
                              width="28"
                            />
                            <path
                              d="M19.3892 18.3292L8.01562 10.3919L8.62162 9.52363L19.9962 17.4606L19.3892 18.3292Z"
                              fill="#2B2B2B"
                            />
                            <path
                              d="M18.0364 17.7442L17.2016 17.0927C17.7631 16.3996 18.1375 15.574 18.289 14.6949C18.3934 14.0071 18.3521 13.3052 18.1677 12.6344C17.9764 11.9108 17.6506 11.2296 17.2071 10.6267L18.0308 9.96152C18.5676 10.6799 18.9614 11.4946 19.191 12.3615C19.4136 13.1749 19.4623 14.0261 19.3344 14.8596C19.1558 15.9164 18.7089 16.9096 18.0364 17.7442Z"
                              fill="#2B2B2B"
                            />
                            <path
                              d="M15.8761 16.8675L14.9938 16.2843C15.344 15.7838 15.5835 15.2147 15.6969 14.6144C15.8102 14.0141 15.7946 13.3963 15.651 12.8025C15.528 12.2724 15.3112 11.7688 15.0106 11.3152L15.8606 10.6838C16.2483 11.2521 16.5268 11.8878 16.6816 12.5581C16.857 13.2927 16.8758 14.056 16.7371 14.7984C16.5983 15.5409 16.305 16.2458 15.8761 16.8675Z"
                              fill="#2B2B2B"
                            />
                            <path
                              d="M10.3134 14.9479H8.32031V12.5662H10.3134L13.5558 9.58809V17.9256L10.3134 14.9479Z"
                              fill="#2B2B2B"
                            />
                            <path
                              d="M13.7307 18.3273L10.2429 15.1244H8.14219V12.3898H10.2429L13.7307 9.18652V18.3273ZM8.49513 14.7715H10.3802L13.3774 17.5244V9.98982L10.3802 12.7428H8.49513V14.7715Z"
                              fill="#2B2B2B"
                            />
                          </g>
                          <defs>
                            <filter
                              color-interpolation-filters="sRGB"
                              filterUnits="userSpaceOnUse"
                              height="104"
                              id="filter0_b_8973_3432"
                              width="104"
                              x="-38"
                              y="-38"
                            >
                              <feflood
                                flood-opacity="0"
                                result="BackgroundImageFix"
                              />
                              <fegaussianblur
                                in="BackgroundImageFix"
                                stdDeviation="19"
                              />
                              <fecomposite
                                in2="SourceAlpha"
                                operator="in"
                                result="effect1_backgroundBlur_8973_3432"
                              />
                              <feblend
                                in="SourceGraphic"
                                in2="effect1_backgroundBlur_8973_3432"
                                mode="normal"
                                result="shape"
                              />
                            </filter>
                          </defs>
                        </svg>
                      </span>
                    </button>
                    <div
                      class="emotion-13"
                      data-testid="styled-input-volume"
                      style="background: rgb(255, 255, 255);"
                    >
                      <div
                        role="presentation"
                      />
                    </div>
                  </div>
                </div>
              </div>
              <div
                class="emotion-14"
              >
                <div
                  class="emotion-15"
                >
                  <div
                    class="emotion-16"
                  >
                    <div
                      class="emotion-17"
                    >
                      <div>
                        <p
                          class="amp-cms--p"
                          style="text-align:left;"
                        >
                          <span
                            class="amp-cms--body-1"
                          >
                            Mobile Card
                          </span>
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </a>
        </div>
        <div
          class="emotion-3"
        >
          <a
            aria-label="new arrivals"
            class="emotion-4"
            data-testid="conditional-link"
            href="www.google.com"
            target="_self"
          >
            <div
              class="emotion-5"
            >
              <div
                class="emotion-6"
                data-testid="videocomponent-container"
              >
                <div
                  style="position: relative;"
                >
                  <img
                    fetchpriority="high"
                    src="https://2hmc8flz4ofg1dy89cc38f2xm.staging.bigcontent.io/i/athleta/Card 3 - MobileL?fmt=auto"
                    style="height: 100%; left: 0px; object-fit: cover; position: absolute; top: 0px; width: 100%;"
                  />
                  <h1
                    role="presentation"
                  >
                    ReactPlayer
                  </h1>
                </div>
                <div
                  class="player-custom-controls emotion-7"
                  data-testid="player-custom-controls"
                >
                  <button
                    aria-label="Play"
                    aria-pressed="false"
                    class="emotion-8"
                  >
                    <span
                      aria-hidden="true"
                      class="emotion-9"
                    >
                      <svg
                        fill="none"
                        viewBox="0 0 28 28"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <g
                          filter="url(#filter0_b_13041_121152)"
                        >
                          <rect
                            fill="#F7F7F7"
                            height="28"
                            width="28"
                          />
                          <path
                            d="M17.615 14.18L10.375 18.36V10L17.615 14.18Z"
                            fill="#2B2B2B"
                          />
                        </g>
                        <defs>
                          <filter
                            color-interpolation-filters="sRGB"
                            filterUnits="userSpaceOnUse"
                            height="104"
                            id="filter0_b_13041_121152"
                            width="104"
                            x="-38"
                            y="-38"
                          >
                            <feflood
                              flood-opacity="0"
                              result="BackgroundImageFix"
                            />
                            <fegaussianblur
                              in="BackgroundImageFix"
                              stdDeviation="19"
                            />
                            <fecomposite
                              in2="SourceAlpha"
                              operator="in"
                              result="effect1_backgroundBlur_13041_121152"
                            />
                            <feblend
                              in="SourceGraphic"
                              in2="effect1_backgroundBlur_13041_121152"
                              mode="normal"
                              result="shape"
                            />
                          </filter>
                        </defs>
                      </svg>
                    </span>
                  </button>
                  <div
                    class="emotion-10"
                    data-testid="styled-mute-controls"
                  >
                    <button
                      aria-label="Mute"
                      aria-pressed="false"
                      class="emotion-11"
                    >
                      <span
                        aria-hidden="true"
                        class="emotion-9"
                      >
                        <svg
                          fill="none"
                          viewBox="0 0 28 28"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <g
                            filter="url(#filter0_b_8973_3432)"
                          >
                            <rect
                              fill="#F7F7F7"
                              height="28"
                              width="28"
                            />
                            <path
                              d="M19.3892 18.3292L8.01562 10.3919L8.62162 9.52363L19.9962 17.4606L19.3892 18.3292Z"
                              fill="#2B2B2B"
                            />
                            <path
                              d="M18.0364 17.7442L17.2016 17.0927C17.7631 16.3996 18.1375 15.574 18.289 14.6949C18.3934 14.0071 18.3521 13.3052 18.1677 12.6344C17.9764 11.9108 17.6506 11.2296 17.2071 10.6267L18.0308 9.96152C18.5676 10.6799 18.9614 11.4946 19.191 12.3615C19.4136 13.1749 19.4623 14.0261 19.3344 14.8596C19.1558 15.9164 18.7089 16.9096 18.0364 17.7442Z"
                              fill="#2B2B2B"
                            />
                            <path
                              d="M15.8761 16.8675L14.9938 16.2843C15.344 15.7838 15.5835 15.2147 15.6969 14.6144C15.8102 14.0141 15.7946 13.3963 15.651 12.8025C15.528 12.2724 15.3112 11.7688 15.0106 11.3152L15.8606 10.6838C16.2483 11.2521 16.5268 11.8878 16.6816 12.5581C16.857 13.2927 16.8758 14.056 16.7371 14.7984C16.5983 15.5409 16.305 16.2458 15.8761 16.8675Z"
                              fill="#2B2B2B"
                            />
                            <path
                              d="M10.3134 14.9479H8.32031V12.5662H10.3134L13.5558 9.58809V17.9256L10.3134 14.9479Z"
                              fill="#2B2B2B"
                            />
                            <path
                              d="M13.7307 18.3273L10.2429 15.1244H8.14219V12.3898H10.2429L13.7307 9.18652V18.3273ZM8.49513 14.7715H10.3802L13.3774 17.5244V9.98982L10.3802 12.7428H8.49513V14.7715Z"
                              fill="#2B2B2B"
                            />
                          </g>
                          <defs>
                            <filter
                              color-interpolation-filters="sRGB"
                              filterUnits="userSpaceOnUse"
                              height="104"
                              id="filter0_b_8973_3432"
                              width="104"
                              x="-38"
                              y="-38"
                            >
                              <feflood
                                flood-opacity="0"
                                result="BackgroundImageFix"
                              />
                              <fegaussianblur
                                in="BackgroundImageFix"
                                stdDeviation="19"
                              />
                              <fecomposite
                                in2="SourceAlpha"
                                operator="in"
                                result="effect1_backgroundBlur_8973_3432"
                              />
                              <feblend
                                in="SourceGraphic"
                                in2="effect1_backgroundBlur_8973_3432"
                                mode="normal"
                                result="shape"
                              />
                            </filter>
                          </defs>
                        </svg>
                      </span>
                    </button>
                    <div
                      class="emotion-13"
                      data-testid="styled-input-volume"
                      style="background: rgb(255, 255, 255);"
                    >
                      <div
                        role="presentation"
                      />
                    </div>
                  </div>
                </div>
              </div>
              <div
                class="emotion-14"
              >
                <div
                  class="emotion-15"
                >
                  <div
                    class="emotion-16"
                  >
                    <div
                      class="emotion-17"
                    >
                      <div>
                        <p
                          class="amp-cms--p"
                          style="text-align:left;"
                        >
                          <span
                            class="amp-cms--body-1"
                          >
                            Mobile Card
                          </span>
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </a>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`Gap FeaturedCategories inset desktop should match snapshots for size large 1`] = `
.emotion-0 {
  height: 100%;
  padding: 30px 20px;
}

.emotion-0>div>div {
  gap: 30px;
}

.emotion-1 {
  margin-bottom: unset;
  margin-left: unset;
  margin: 0 0 30px 15px;
}

.emotion-1 .amp-cms--p {
  padding: 0;
  margin: 0;
  line-height: 0;
}

.emotion-1 .amp-cms--p>span {
  display: inline;
  white-space: break-spaces;
}

.emotion-1 .amp-cms--p a {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  position: relative;
  z-index: 2;
  pointer-events: auto;
}

.emotion-1 .amp-cms--p a:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-1 .amp-cms--legal-copy {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-1 sup {
  vertical-align: top;
  display: inline-block;
  margin-top: -0.25ex;
}

.emotion-1 sub {
  vertical-align: bottom;
  display: inline-block;
  margin-bottom: -0.25ex;
}

.emotion-1 .amp-cms--f-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--f-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--f-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--fn-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--body-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 20px;
  line-height: 1.3;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--body-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 18px;
  line-height: 1.2777777777777777;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--body-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 16px;
  line-height: 1.25;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--body-4 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 14px;
  line-height: 1.2857142857142858;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--body-5 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--eyebrow-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 50px;
  line-height: 0.92;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--eyebrow-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 40px;
  line-height: 1;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--eyebrow-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 35px;
  line-height: 1;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--headline-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 120px;
  line-height: 0.8333333333333334;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--headline-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 110px;
  line-height: 0.8636363636363636;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--headline-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 100px;
  line-height: 0.85;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--headline-4 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 100px;
  line-height: 1.05;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--headline-5 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 80px;
  line-height: 0.9375;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--headline-6 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 70px;
  line-height: 0.9285714285714286;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--headline-7 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 60px;
  line-height: 0.9166666666666666;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--headlineAlt-1 {
  color: #2B2B2B;
  font-size: 114px;
  line-height: 1;
  letter-spacing: -0.4px;
  font-weight: 400;
}

.emotion-1 .amp-cms--headlineAlt-2 {
  color: #2B2B2B;
  font-size: 84px;
  line-height: 1;
  letter-spacing: -0.4px;
  font-weight: 400;
}

.emotion-1 .amp-cms--headlineAlt-3 {
  color: #2B2B2B;
  font-size: 64px;
  line-height: 1;
  letter-spacing: -0.4px;
  font-weight: 400;
}

.emotion-1 .amp-cms--headlineAlt-4 {
  color: #2B2B2B;
  font-size: 34px;
  line-height: 1;
  letter-spacing: -0.4px;
  font-weight: 400;
}

.emotion-1 .amp-cms--headlineAlt-5 {
  color: #2B2B2B;
  font-size: 16px;
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-1 .amp-cms--headlineAlt-6 {
  color: #2B2B2B;
  font-size: 16px;
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-1 .amp-cms--headlineAlt-7 {
  color: #2B2B2B;
  font-size: 16px;
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-1 .amp-cms--promo-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 100px;
  line-height: 1;
  letter-spacing: 0px;
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-1 .amp-cms--promo-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 80px;
  line-height: 1;
  letter-spacing: 0px;
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-1 .amp-cms--promoAlt-1 {
  color: #2B2B2B;
  font-size: 16px;
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 600;
}

.emotion-1 .amp-cms--subhead-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 30px;
  line-height: 1;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--subhead-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 25px;
  line-height: 1;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--subhead-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 20px;
  line-height: 1.1;
  letter-spacing: 0;
}

.emotion-2 {
  padding: 0 15px;
}

.emotion-3 {
  width: 100%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-flex-wrap: nowrap;
  -webkit-flex-wrap: nowrap;
  -ms-flex-wrap: nowrap;
  flex-wrap: nowrap;
}

.emotion-4 {
  position: relative;
  width: 100%;
}

.emotion-5 {
  cursor: pointer;
  height: 100%;
  width: 100%;
}

.emotion-6 {
  background: transparent;
  width: 100%!important;
  height: min(56.25vw, 810px)!important;
  -webkit-background-size: cover;
  background-size: cover;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: end;
  -webkit-box-align: end;
  -ms-flex-align: end;
  align-items: end;
  box-sizing: border-box;
}

.emotion-7 {
  width: 100%!important;
  height: auto!important;
  -webkit-background-size: cover;
  background-size: cover;
  display: -webkit-box!important;
  display: -webkit-flex!important;
  display: -ms-flexbox!important;
  display: flex!important;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: end;
  -webkit-box-align: end;
  -ms-flex-align: end;
  align-items: end;
  box-sizing: border-box;
  padding: 30px 20px;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  gap: 20px;
}

.emotion-8 {
  box-sizing: border-box;
  height: auto!important;
  width: 100%!important;
  display: -webkit-box!important;
  display: -webkit-flex!important;
  display: -ms-flexbox!important;
  display: flex!important;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  gap: 15px;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
}

.emotion-9 .amp-cms--p {
  padding: 0;
  margin: 0;
  line-height: 0;
}

.emotion-9 .amp-cms--p>span {
  display: inline;
  white-space: break-spaces;
}

.emotion-9 .amp-cms--p a {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  position: relative;
  z-index: 2;
  pointer-events: auto;
}

.emotion-9 .amp-cms--p a:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-9 .amp-cms--legal-copy {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, 0.8333333333333334vw);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-9 sup {
  vertical-align: top;
  display: inline-block;
  margin-top: -0.25ex;
}

.emotion-9 sub {
  vertical-align: bottom;
  display: inline-block;
  margin-bottom: -0.25ex;
}

.emotion-9 .amp-cms--f-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 0.8333333333333334vw;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--f-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 0.8333333333333334vw;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--f-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 0.8333333333333334vw;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--fn-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 0.8333333333333334vw;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--body-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, 1.3888888888888888vw);
  line-height: 1.3;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--body-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, 1.25vw);
  line-height: 1.2777777777777777;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--body-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, 1.1111111111111112vw);
  line-height: 1.25;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--body-4 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, 0.9722222222222222vw);
  line-height: 1.2857142857142858;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--body-5 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, 0.8333333333333334vw);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--eyebrow-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(35px, 3.4722222222222223vw);
  line-height: 0.92;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--eyebrow-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(35px, 2.7777777777777777vw);
  line-height: 1;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--eyebrow-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(35px, 2.430555555555556vw);
  line-height: 1;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--headline-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, 8.333333333333332vw);
  line-height: 0.8333333333333334;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--headline-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, 7.638888888888889vw);
  line-height: 0.8636363636363636;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--headline-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, 6.944444444444445vw);
  line-height: 0.85;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--headline-4 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, 6.944444444444445vw);
  line-height: 1.05;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--headline-5 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, 5.555555555555555vw);
  line-height: 0.9375;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--headline-6 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, 4.861111111111112vw);
  line-height: 0.9285714285714286;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--headline-7 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, 4.166666666666666vw);
  line-height: 0.9166666666666666;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--headlineAlt-1 {
  color: #2B2B2B;
  font-size: max(14px, 7.916666666666666vw);
  line-height: 1;
  letter-spacing: -0.027777777777777776vw;
  font-weight: 400;
}

.emotion-9 .amp-cms--headlineAlt-2 {
  color: #2B2B2B;
  font-size: max(14px, 5.833333333333333vw);
  line-height: 1;
  letter-spacing: -0.027777777777777776vw;
  font-weight: 400;
}

.emotion-9 .amp-cms--headlineAlt-3 {
  color: #2B2B2B;
  font-size: max(14px, 4.444444444444445vw);
  line-height: 1;
  letter-spacing: -0.027777777777777776vw;
  font-weight: 400;
}

.emotion-9 .amp-cms--headlineAlt-4 {
  color: #2B2B2B;
  font-size: max(14px, 2.361111111111111vw);
  line-height: 1;
  letter-spacing: -0.027777777777777776vw;
  font-weight: 400;
}

.emotion-9 .amp-cms--headlineAlt-5 {
  color: #2B2B2B;
  font-size: 1.1111111111111112vw;
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-9 .amp-cms--headlineAlt-6 {
  color: #2B2B2B;
  font-size: 1.1111111111111112vw;
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-9 .amp-cms--headlineAlt-7 {
  color: #2B2B2B;
  font-size: 1.1111111111111112vw;
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-9 .amp-cms--promo-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, 6.944444444444445vw);
  line-height: 1;
  letter-spacing: 0vw;
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-9 .amp-cms--promo-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, 5.555555555555555vw);
  line-height: 1;
  letter-spacing: 0vw;
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-9 .amp-cms--promoAlt-1 {
  color: #2B2B2B;
  font-size: 1.1111111111111112vw;
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 600;
}

.emotion-9 .amp-cms--subhead-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, 2.083333333333333vw);
  line-height: 1;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--subhead-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, 1.7361111111111112vw);
  line-height: 1;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--subhead-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, 1.3888888888888888vw);
  line-height: 1.1;
  letter-spacing: 0;
}

<div>
  <div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      class="emotion-0"
    >
      <div
        class="emotion-1"
      >
        <div>
          <p
            class="amp-cms--p"
            style="text-align:left;"
          >
            <span
              class="amp-cms--headline-3"
            >
              OPTIONAL HEADLINE
            </span>
          </p>
        </div>
      </div>
      <div
        class="emotion-2"
      >
        <div
          class="emotion-3"
        >
          <div
            class="emotion-4"
          >
            <a
              aria-label="new arrivals"
              class="emotion-5"
              data-testid="conditional-link"
              href="www.google.com"
              target="_self"
            >
              <div
                aria-label="image alt text"
                class="emotion-6"
                height="0"
                role="img"
                width="0"
              >
                <div
                  class="emotion-7"
                >
                  <div
                    class="emotion-8"
                  >
                    <div
                      class="emotion-9"
                    >
                      <div>
                        <p
                          class="amp-cms--p"
                          style="text-align:left;"
                        >
                          <span
                            class="amp-cms--subhead-1"
                            style="color:#FFFFFF"
                          >
                            Card
                          </span>
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </a>
          </div>
          <div
            class="emotion-4"
          >
            <a
              aria-label="new arrivals"
              class="emotion-5"
              data-testid="conditional-link"
              href="www.google.com"
              target="_self"
            >
              <div
                aria-label="image alt text"
                class="emotion-6"
                height="0"
                role="img"
                width="0"
              >
                <div
                  class="emotion-7"
                >
                  <div
                    class="emotion-8"
                  >
                    <div
                      class="emotion-9"
                    >
                      <div>
                        <p
                          class="amp-cms--p"
                          style="text-align:left;"
                        >
                          <span
                            class="amp-cms--subhead-1"
                            style="color:#FFFFFF"
                          >
                            Card
                          </span>
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </a>
          </div>
          <div
            class="emotion-4"
          >
            <a
              aria-label="new arrivals"
              class="emotion-5"
              data-testid="conditional-link"
              href="www.google.com"
              target="_self"
            >
              <div
                aria-label="image alt text"
                class="emotion-6"
                height="0"
                role="img"
                width="0"
              >
                <div
                  class="emotion-7"
                >
                  <div
                    class="emotion-8"
                  >
                    <div
                      class="emotion-9"
                    >
                      <div>
                        <p
                          class="amp-cms--p"
                          style="text-align:left;"
                        >
                          <span
                            class="amp-cms--subhead-1"
                            style="color:#FFFFFF"
                          >
                            Card
                          </span>
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </a>
          </div>
          <div
            class="emotion-4"
          >
            <a
              aria-label="new arrivals"
              class="emotion-5"
              data-testid="conditional-link"
              href="www.google.com"
              target="_self"
            >
              <div
                aria-label="image alt text"
                class="emotion-6"
                height="0"
                role="img"
                width="0"
              >
                <div
                  class="emotion-7"
                >
                  <div
                    class="emotion-8"
                  >
                    <div
                      class="emotion-9"
                    >
                      <div>
                        <p
                          class="amp-cms--p"
                          style="text-align:left;"
                        >
                          <span
                            class="amp-cms--subhead-1"
                            style="color:#FFFFFF"
                          >
                            Card
                          </span>
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </a>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`Gap FeaturedCategories inset mobile should match snapshots for scroll layout size medium 1`] = `
.emotion-0 {
  height: 100%;
  padding: 16px 0px;
}

.emotion-0>div {
  gap: 30px;
  display: grid;
  margin: 0 0 0 16px;
}

.emotion-1 {
  margin-bottom: 15px;
  margin-left: 15px;
  margin: 0 0 15px 16px;
}

.emotion-1 .amp-cms--p {
  padding: 0;
  margin: 0;
  line-height: 0;
}

.emotion-1 .amp-cms--p>span {
  display: inline;
  white-space: break-spaces;
}

.emotion-1 .amp-cms--p a {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  position: relative;
  z-index: 2;
  pointer-events: auto;
}

.emotion-1 .amp-cms--p a:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-1 .amp-cms--legal-copy {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 10px;
  line-height: 1.4;
  letter-spacing: 0;
}

.emotion-1 sup {
  vertical-align: top;
  display: inline-block;
  margin-top: -0.25ex;
}

.emotion-1 sub {
  vertical-align: bottom;
  display: inline-block;
  margin-bottom: -0.25ex;
}

.emotion-1 .amp-cms--f-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 11px;
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--f-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 11px;
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--f-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 11px;
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--fn-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 11px;
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--body-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 18px;
  line-height: 1.2777777777777777;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--body-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 16px;
  line-height: 1.25;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--body-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 14px;
  line-height: 1.2857142857142858;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--body-4 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--body-5 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 10px;
  line-height: 1.4;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--eyebrow-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 30px;
  line-height: 1;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--eyebrow-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 26px;
  line-height: 1;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--eyebrow-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 24px;
  line-height: 1;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--headline-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 60px;
  line-height: 1;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--headline-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 55px;
  line-height: 1;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--headline-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 55px;
  line-height: 1;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--headline-4 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 50px;
  line-height: 1.1;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--headline-5 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 45px;
  line-height: 1;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--headline-6 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 40px;
  line-height: 1;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--headline-7 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 35px;
  line-height: 1;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--headlineAlt-1 {
  color: #2B2B2B;
  font-size: 64px;
  line-height: 1;
  letter-spacing: -0.3px;
  font-weight: 400;
}

.emotion-1 .amp-cms--headlineAlt-2 {
  color: #2B2B2B;
  font-size: 54px;
  line-height: 1;
  letter-spacing: -0.3px;
  font-weight: 400;
}

.emotion-1 .amp-cms--headlineAlt-3 {
  color: #2B2B2B;
  font-size: 34px;
  line-height: 1;
  letter-spacing: -0.3px;
  font-weight: 400;
}

.emotion-1 .amp-cms--headlineAlt-4 {
  color: #2B2B2B;
  font-size: 24px;
  line-height: 1.4166666666666667;
  letter-spacing: -0.3px;
  font-weight: 400;
}

.emotion-1 .amp-cms--headlineAlt-5 {
  color: #2B2B2B;
  font-size: 14px;
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-1 .amp-cms--headlineAlt-6 {
  color: #2B2B2B;
  font-size: 14px;
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-1 .amp-cms--headlineAlt-7 {
  color: #2B2B2B;
  font-size: 14px;
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-1 .amp-cms--promo-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 60px;
  line-height: 1;
  letter-spacing: 0px;
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-1 .amp-cms--promo-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 40px;
  line-height: 1;
  letter-spacing: 0px;
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-1 .amp-cms--promoAlt-1 {
  color: #2B2B2B;
  font-size: 14px;
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 600;
}

.emotion-1 .amp-cms--subhead-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 22px;
  line-height: 1.0909090909090908;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--subhead-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 20px;
  line-height: 1.1;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--subhead-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 18px;
  line-height: 1.1111111111111112;
  letter-spacing: 0;
}

.emotion-2 {
  display: grid;
  grid-template-columns: repeat(4, 250px);
  overflow-x: scroll;
  scrollbar-width: none;
  padding: 0;
  -ms-overflow-style: none;
}

.emotion-2::-webkit-scrollbar {
  display: none;
}

.emotion-3 {
  position: relative;
  width: 100%;
}

.emotion-4 {
  cursor: pointer;
  height: 100%;
  width: 100%;
}

.emotion-5 {
  background: transparent;
  width: 100%!important;
  height: min(88.75vw, 284px)!important;
  -webkit-background-size: cover;
  background-size: cover;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: end;
  -webkit-box-align: end;
  -ms-flex-align: end;
  align-items: end;
  box-sizing: border-box;
}

.emotion-6 {
  width: 100%!important;
  height: auto!important;
  -webkit-background-size: cover;
  background-size: cover;
  display: -webkit-box!important;
  display: -webkit-flex!important;
  display: -ms-flexbox!important;
  display: flex!important;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: end;
  -webkit-box-align: end;
  -ms-flex-align: end;
  align-items: end;
  box-sizing: border-box;
  padding: 20px 15px;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  gap: 20px;
}

.emotion-7 {
  box-sizing: border-box;
  height: auto!important;
  width: 100%!important;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  gap: 15px;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
}

.emotion-8 .amp-cms--p {
  padding: 0;
  margin: 0;
  line-height: 0;
}

.emotion-8 .amp-cms--p>span {
  display: inline;
  white-space: break-spaces;
}

.emotion-8 .amp-cms--p a {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  position: relative;
  z-index: 2;
  pointer-events: auto;
}

.emotion-8 .amp-cms--p a:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-8 .amp-cms--legal-copy {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, 2.666666666666667vw);
  line-height: 1.4;
  letter-spacing: 0;
}

.emotion-8 sup {
  vertical-align: top;
  display: inline-block;
  margin-top: -0.25ex;
}

.emotion-8 sub {
  vertical-align: bottom;
  display: inline-block;
  margin-bottom: -0.25ex;
}

.emotion-8 .amp-cms--f-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 2.933333333333333vw;
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-8 .amp-cms--f-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 2.933333333333333vw;
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-8 .amp-cms--f-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 2.933333333333333vw;
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-8 .amp-cms--fn-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 2.933333333333333vw;
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-8 .amp-cms--body-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, 4.8vw);
  line-height: 1.2777777777777777;
  letter-spacing: 0;
}

.emotion-8 .amp-cms--body-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, 4.266666666666667vw);
  line-height: 1.25;
  letter-spacing: 0;
}

.emotion-8 .amp-cms--body-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, 3.733333333333334vw);
  line-height: 1.2857142857142858;
  letter-spacing: 0;
}

.emotion-8 .amp-cms--body-4 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, 3.2vw);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-8 .amp-cms--body-5 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, 2.666666666666667vw);
  line-height: 1.4;
  letter-spacing: 0;
}

.emotion-8 .amp-cms--eyebrow-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, 8vw);
  line-height: 1;
  letter-spacing: 0;
}

.emotion-8 .amp-cms--eyebrow-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, 6.933333333333333vw);
  line-height: 1;
  letter-spacing: 0;
}

.emotion-8 .amp-cms--eyebrow-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, 6.4vw);
  line-height: 1;
  letter-spacing: 0;
}

.emotion-8 .amp-cms--headline-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, 16vw);
  line-height: 1;
  letter-spacing: 0;
}

.emotion-8 .amp-cms--headline-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, 14.666666666666666vw);
  line-height: 1;
  letter-spacing: 0;
}

.emotion-8 .amp-cms--headline-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, 14.666666666666666vw);
  line-height: 1;
  letter-spacing: 0;
}

.emotion-8 .amp-cms--headline-4 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, 13.333333333333334vw);
  line-height: 1.1;
  letter-spacing: 0;
}

.emotion-8 .amp-cms--headline-5 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, 12vw);
  line-height: 1;
  letter-spacing: 0;
}

.emotion-8 .amp-cms--headline-6 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, 10.666666666666668vw);
  line-height: 1;
  letter-spacing: 0;
}

.emotion-8 .amp-cms--headline-7 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, 9.333333333333334vw);
  line-height: 1;
  letter-spacing: 0;
}

.emotion-8 .amp-cms--headlineAlt-1 {
  color: #2B2B2B;
  font-size: max(13px, 17.066666666666666vw);
  line-height: 1;
  letter-spacing: -0.07999999999999999vw;
  font-weight: 400;
}

.emotion-8 .amp-cms--headlineAlt-2 {
  color: #2B2B2B;
  font-size: max(13px, 14.399999999999999vw);
  line-height: 1;
  letter-spacing: -0.07999999999999999vw;
  font-weight: 400;
}

.emotion-8 .amp-cms--headlineAlt-3 {
  color: #2B2B2B;
  font-size: max(13px, 9.066666666666666vw);
  line-height: 1;
  letter-spacing: -0.07999999999999999vw;
  font-weight: 400;
}

.emotion-8 .amp-cms--headlineAlt-4 {
  color: #2B2B2B;
  font-size: max(13px, 6.4vw);
  line-height: 1.4166666666666667;
  letter-spacing: -0.07999999999999999vw;
  font-weight: 400;
}

.emotion-8 .amp-cms--headlineAlt-5 {
  color: #2B2B2B;
  font-size: 3.733333333333334vw;
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-8 .amp-cms--headlineAlt-6 {
  color: #2B2B2B;
  font-size: 3.733333333333334vw;
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-8 .amp-cms--headlineAlt-7 {
  color: #2B2B2B;
  font-size: 3.733333333333334vw;
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-8 .amp-cms--promo-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(13px, 16vw);
  line-height: 1;
  letter-spacing: 0vw;
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-8 .amp-cms--promo-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(13px, 10.666666666666668vw);
  line-height: 1;
  letter-spacing: 0vw;
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-8 .amp-cms--promoAlt-1 {
  color: #2B2B2B;
  font-size: 3.733333333333334vw;
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 600;
}

.emotion-8 .amp-cms--subhead-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, 5.866666666666666vw);
  line-height: 1.0909090909090908;
  letter-spacing: 0;
}

.emotion-8 .amp-cms--subhead-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, 5.333333333333334vw);
  line-height: 1.1;
  letter-spacing: 0;
}

.emotion-8 .amp-cms--subhead-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, 4.8vw);
  line-height: 1.1111111111111112;
  letter-spacing: 0;
}

.emotion-17 {
  position: relative;
  height: min(88.75vw, 284px)!important;
  pointer-events: auto;
  width: 100%!important;
  -webkit-background-size: cover;
  background-size: cover;
}

.emotion-17>div>img {
  height: min(88.75vw, 284px)!important;
}

.emotion-18 {
  width: -webkit-fit-content;
  width: -moz-fit-content;
  width: fit-content;
  position: relative;
  width: 100%;
}

.emotion-19 {
  display: none;
  gap: 15px;
  position: absolute;
  max-width: -webkit-max-content;
  max-width: -moz-max-content;
  max-width: max-content;
  max-height: -webkit-max-content;
  max-height: -moz-max-content;
  max-height: max-content;
  margin: 10px;
  right: 0;
  bottom: 0;
  height: 28px;
}

.emotion-19>button {
  left: 0;
  bottom: 0;
  z-index: 4;
  position: relative;
  width: 28px;
  height: 28px;
}

.emotion-19>div>button {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  left: 0;
  bottom: 0;
  z-index: 4;
  position: relative;
}

.emotion-19>div>div {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  left: 0;
  z-index: 3;
  bottom: 0;
}

.emotion-19>div>div:before {
  left: 0;
  bottom: -28px;
  width: 28px;
  height: 28px;
  border-radius: 0;
}

.emotion-20 {
  background: none;
  border: none;
  padding: 0;
  height: 24px;
  margin: 0;
  position: absolute;
  bottom: 11px;
  right: 112px;
  width: 24px;
  height: 24px;
}

.emotion-20:is(:not(:focus, :active, :hover, :focus-within)) {
  opacity: 0.64;
}

.emotion-20:is(:hover) {
  opacity: 1;
}

@media (min-width: 767px) {
  .emotion-20 {
    right: 120px;
  }
}

.emotion-21 {
  display: inline-block;
  height: 28px;
  width: 28px;
  min-height: 28px;
  min-width: 28px;
}

.emotion-21 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-21 svg:hover rect {
  fill: #031BA1;
}

.emotion-21 svg:hover path {
  fill: #FFFFFF;
}

.emotion-22 {
  box-sizing: border-box;
  background: none;
  border: none;
  padding: 0;
  position: relative;
  width: 100%;
  height: 0px;
}

.emotion-22:focus div {
  opacity: 0.84;
  -webkit-transition: opacity 200ms;
  transition: opacity 200ms;
  border-radius: 20px 20px 0 0;
}

.emotion-22 div {
  border-radius: 20px 20px 0 0;
}

.emotion-22 .keepOpen,
.emotion-22 .staysOpen {
  opacity: 0.84;
  -webkit-transition: opacity 200ms;
  transition: opacity 200ms;
  z-index: 2;
}

.emotion-23 {
  background: none;
  border: none;
  padding: 0;
  height: 24px;
  margin: 0;
  position: absolute;
  bottom: 11px;
  right: 72px;
  z-index: 11;
}

.emotion-23:is(:not(:focus, :active, :hover, :focus-within)) {
  opacity: 0.64;
}

.emotion-23:is(:hover) {
  opacity: 1;
}

@media (min-width: 767px) {
  .emotion-23 {
    right: 80px;
  }
}

.emotion-25 {
  opacity: 0;
  -webkit-transition: opacity 200ms;
  transition: opacity 200ms;
  box-sizing: border-box;
  background: #ffffff;
  border: none;
  padding: 0;
  position: absolute;
  width: 28px;
  height: 60px;
  bottom: 35px;
  right: 72px;
  border-radius: 20px 20px 0 0;
}

.emotion-25:before {
  content: "";
  height: 24px;
  background: #ffffff;
  display: block;
  position: absolute;
  bottom: -24px;
  width: 24px;
  border-bottom-right-radius: 50%;
  border-bottom-left-radius: 50%;
}

.emotion-26 {
  position: absolute;
  z-index: 0;
  width: 100%!important;
  height: min(88.75vw, 284px)!important;
  top: 0;
  pointer-events: none;
  -webkit-background-size: cover;
  background-size: cover;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: end;
  -webkit-box-align: end;
  -ms-flex-align: end;
  align-items: end;
  box-sizing: border-box;
}

<div>
  <div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      class="emotion-0"
    >
      <div
        class="emotion-1"
      >
        <div>
          <p
            class="amp-cms--p"
            style="text-align:left;"
          >
            <span
              class="amp-cms--headline-3"
            >
              OPTIONAL HEADLINE
            </span>
          </p>
        </div>
      </div>
      <div
        class="emotion-2"
      >
        <div
          class="emotion-3"
        >
          <a
            aria-label="new arrivals"
            class="emotion-4"
            data-testid="conditional-link"
            href="www.google.com"
            target="_self"
          >
            <div
              aria-label="mobile alt text"
              class="emotion-5"
              height="0"
              role="img"
              width="0"
            >
              <div
                class="emotion-6"
              >
                <div
                  class="emotion-7"
                >
                  <div
                    class="emotion-8"
                  >
                    <div>
                      <p
                        class="amp-cms--p"
                        style="text-align:left;"
                      >
                        <span
                          class="amp-cms--body-1"
                        >
                          Mobile Card
                        </span>
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </a>
        </div>
        <div
          class="emotion-3"
        >
          <a
            aria-label="new arrivals"
            class="emotion-4"
            data-testid="conditional-link"
            href="www.google.com"
            target="_self"
          >
            <div
              aria-label="mobile alt text"
              class="emotion-5"
              height="0"
              role="img"
              width="0"
            >
              <div
                class="emotion-6"
              >
                <div
                  class="emotion-7"
                >
                  <div
                    class="emotion-8"
                  >
                    <div>
                      <p
                        class="amp-cms--p"
                        style="text-align:left;"
                      >
                        <span
                          class="amp-cms--body-1"
                        >
                          Mobile Card
                        </span>
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </a>
        </div>
        <div
          class="emotion-3"
        >
          <a
            aria-label="new arrivals"
            class="emotion-4"
            data-testid="conditional-link"
            href="www.google.com"
            target="_self"
          >
            <div
              class="emotion-17"
            >
              <div
                class="emotion-18"
                data-testid="videocomponent-container"
              >
                <div
                  style="position: relative;"
                >
                  <img
                    fetchpriority="high"
                    src="https://2hmc8flz4ofg1dy89cc38f2xm.staging.bigcontent.io/i/athleta/Card 3 - MobileL?fmt=auto"
                    style="height: 100%; left: 0px; object-fit: cover; position: absolute; top: 0px; width: 100%;"
                  />
                  <h1
                    role="presentation"
                  >
                    ReactPlayer
                  </h1>
                </div>
                <div
                  class="player-custom-controls emotion-19"
                  data-testid="player-custom-controls"
                >
                  <button
                    aria-label="Play"
                    aria-pressed="false"
                    class="emotion-20"
                  >
                    <span
                      aria-hidden="true"
                      class="emotion-21"
                    >
                      <svg
                        fill="none"
                        viewBox="0 0 28 28"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <g
                          filter="url(#filter0_b_13041_121152)"
                        >
                          <rect
                            fill="#F7F7F7"
                            height="28"
                            width="28"
                          />
                          <path
                            d="M17.615 14.18L10.375 18.36V10L17.615 14.18Z"
                            fill="#2B2B2B"
                          />
                        </g>
                        <defs>
                          <filter
                            color-interpolation-filters="sRGB"
                            filterUnits="userSpaceOnUse"
                            height="104"
                            id="filter0_b_13041_121152"
                            width="104"
                            x="-38"
                            y="-38"
                          >
                            <feflood
                              flood-opacity="0"
                              result="BackgroundImageFix"
                            />
                            <fegaussianblur
                              in="BackgroundImageFix"
                              stdDeviation="19"
                            />
                            <fecomposite
                              in2="SourceAlpha"
                              operator="in"
                              result="effect1_backgroundBlur_13041_121152"
                            />
                            <feblend
                              in="SourceGraphic"
                              in2="effect1_backgroundBlur_13041_121152"
                              mode="normal"
                              result="shape"
                            />
                          </filter>
                        </defs>
                      </svg>
                    </span>
                  </button>
                  <div
                    class="emotion-22"
                    data-testid="styled-mute-controls"
                  >
                    <button
                      aria-label="Mute"
                      aria-pressed="false"
                      class="emotion-23"
                    >
                      <span
                        aria-hidden="true"
                        class="emotion-21"
                      >
                        <svg
                          fill="none"
                          viewBox="0 0 28 28"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <g
                            filter="url(#filter0_b_8973_3432)"
                          >
                            <rect
                              fill="#F7F7F7"
                              height="28"
                              width="28"
                            />
                            <path
                              d="M19.3892 18.3292L8.01562 10.3919L8.62162 9.52363L19.9962 17.4606L19.3892 18.3292Z"
                              fill="#2B2B2B"
                            />
                            <path
                              d="M18.0364 17.7442L17.2016 17.0927C17.7631 16.3996 18.1375 15.574 18.289 14.6949C18.3934 14.0071 18.3521 13.3052 18.1677 12.6344C17.9764 11.9108 17.6506 11.2296 17.2071 10.6267L18.0308 9.96152C18.5676 10.6799 18.9614 11.4946 19.191 12.3615C19.4136 13.1749 19.4623 14.0261 19.3344 14.8596C19.1558 15.9164 18.7089 16.9096 18.0364 17.7442Z"
                              fill="#2B2B2B"
                            />
                            <path
                              d="M15.8761 16.8675L14.9938 16.2843C15.344 15.7838 15.5835 15.2147 15.6969 14.6144C15.8102 14.0141 15.7946 13.3963 15.651 12.8025C15.528 12.2724 15.3112 11.7688 15.0106 11.3152L15.8606 10.6838C16.2483 11.2521 16.5268 11.8878 16.6816 12.5581C16.857 13.2927 16.8758 14.056 16.7371 14.7984C16.5983 15.5409 16.305 16.2458 15.8761 16.8675Z"
                              fill="#2B2B2B"
                            />
                            <path
                              d="M10.3134 14.9479H8.32031V12.5662H10.3134L13.5558 9.58809V17.9256L10.3134 14.9479Z"
                              fill="#2B2B2B"
                            />
                            <path
                              d="M13.7307 18.3273L10.2429 15.1244H8.14219V12.3898H10.2429L13.7307 9.18652V18.3273ZM8.49513 14.7715H10.3802L13.3774 17.5244V9.98982L10.3802 12.7428H8.49513V14.7715Z"
                              fill="#2B2B2B"
                            />
                          </g>
                          <defs>
                            <filter
                              color-interpolation-filters="sRGB"
                              filterUnits="userSpaceOnUse"
                              height="104"
                              id="filter0_b_8973_3432"
                              width="104"
                              x="-38"
                              y="-38"
                            >
                              <feflood
                                flood-opacity="0"
                                result="BackgroundImageFix"
                              />
                              <fegaussianblur
                                in="BackgroundImageFix"
                                stdDeviation="19"
                              />
                              <fecomposite
                                in2="SourceAlpha"
                                operator="in"
                                result="effect1_backgroundBlur_8973_3432"
                              />
                              <feblend
                                in="SourceGraphic"
                                in2="effect1_backgroundBlur_8973_3432"
                                mode="normal"
                                result="shape"
                              />
                            </filter>
                          </defs>
                        </svg>
                      </span>
                    </button>
                    <div
                      class="emotion-25"
                      data-testid="styled-input-volume"
                      style="background: rgb(255, 255, 255);"
                    >
                      <div
                        role="presentation"
                      />
                    </div>
                  </div>
                </div>
              </div>
              <div
                class="emotion-26"
              >
                <div
                  class="emotion-6"
                >
                  <div
                    class="emotion-7"
                  >
                    <div
                      class="emotion-8"
                    >
                      <div>
                        <p
                          class="amp-cms--p"
                          style="text-align:left;"
                        >
                          <span
                            class="amp-cms--body-1"
                          >
                            Mobile Card
                          </span>
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </a>
        </div>
        <div
          class="emotion-3"
        >
          <a
            aria-label="new arrivals"
            class="emotion-4"
            data-testid="conditional-link"
            href="www.google.com"
            target="_self"
          >
            <div
              class="emotion-17"
            >
              <div
                class="emotion-18"
                data-testid="videocomponent-container"
              >
                <div
                  style="position: relative;"
                >
                  <img
                    fetchpriority="high"
                    src="https://2hmc8flz4ofg1dy89cc38f2xm.staging.bigcontent.io/i/athleta/Card 3 - MobileL?fmt=auto"
                    style="height: 100%; left: 0px; object-fit: cover; position: absolute; top: 0px; width: 100%;"
                  />
                  <h1
                    role="presentation"
                  >
                    ReactPlayer
                  </h1>
                </div>
                <div
                  class="player-custom-controls emotion-19"
                  data-testid="player-custom-controls"
                >
                  <button
                    aria-label="Play"
                    aria-pressed="false"
                    class="emotion-20"
                  >
                    <span
                      aria-hidden="true"
                      class="emotion-21"
                    >
                      <svg
                        fill="none"
                        viewBox="0 0 28 28"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <g
                          filter="url(#filter0_b_13041_121152)"
                        >
                          <rect
                            fill="#F7F7F7"
                            height="28"
                            width="28"
                          />
                          <path
                            d="M17.615 14.18L10.375 18.36V10L17.615 14.18Z"
                            fill="#2B2B2B"
                          />
                        </g>
                        <defs>
                          <filter
                            color-interpolation-filters="sRGB"
                            filterUnits="userSpaceOnUse"
                            height="104"
                            id="filter0_b_13041_121152"
                            width="104"
                            x="-38"
                            y="-38"
                          >
                            <feflood
                              flood-opacity="0"
                              result="BackgroundImageFix"
                            />
                            <fegaussianblur
                              in="BackgroundImageFix"
                              stdDeviation="19"
                            />
                            <fecomposite
                              in2="SourceAlpha"
                              operator="in"
                              result="effect1_backgroundBlur_13041_121152"
                            />
                            <feblend
                              in="SourceGraphic"
                              in2="effect1_backgroundBlur_13041_121152"
                              mode="normal"
                              result="shape"
                            />
                          </filter>
                        </defs>
                      </svg>
                    </span>
                  </button>
                  <div
                    class="emotion-22"
                    data-testid="styled-mute-controls"
                  >
                    <button
                      aria-label="Mute"
                      aria-pressed="false"
                      class="emotion-23"
                    >
                      <span
                        aria-hidden="true"
                        class="emotion-21"
                      >
                        <svg
                          fill="none"
                          viewBox="0 0 28 28"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <g
                            filter="url(#filter0_b_8973_3432)"
                          >
                            <rect
                              fill="#F7F7F7"
                              height="28"
                              width="28"
                            />
                            <path
                              d="M19.3892 18.3292L8.01562 10.3919L8.62162 9.52363L19.9962 17.4606L19.3892 18.3292Z"
                              fill="#2B2B2B"
                            />
                            <path
                              d="M18.0364 17.7442L17.2016 17.0927C17.7631 16.3996 18.1375 15.574 18.289 14.6949C18.3934 14.0071 18.3521 13.3052 18.1677 12.6344C17.9764 11.9108 17.6506 11.2296 17.2071 10.6267L18.0308 9.96152C18.5676 10.6799 18.9614 11.4946 19.191 12.3615C19.4136 13.1749 19.4623 14.0261 19.3344 14.8596C19.1558 15.9164 18.7089 16.9096 18.0364 17.7442Z"
                              fill="#2B2B2B"
                            />
                            <path
                              d="M15.8761 16.8675L14.9938 16.2843C15.344 15.7838 15.5835 15.2147 15.6969 14.6144C15.8102 14.0141 15.7946 13.3963 15.651 12.8025C15.528 12.2724 15.3112 11.7688 15.0106 11.3152L15.8606 10.6838C16.2483 11.2521 16.5268 11.8878 16.6816 12.5581C16.857 13.2927 16.8758 14.056 16.7371 14.7984C16.5983 15.5409 16.305 16.2458 15.8761 16.8675Z"
                              fill="#2B2B2B"
                            />
                            <path
                              d="M10.3134 14.9479H8.32031V12.5662H10.3134L13.5558 9.58809V17.9256L10.3134 14.9479Z"
                              fill="#2B2B2B"
                            />
                            <path
                              d="M13.7307 18.3273L10.2429 15.1244H8.14219V12.3898H10.2429L13.7307 9.18652V18.3273ZM8.49513 14.7715H10.3802L13.3774 17.5244V9.98982L10.3802 12.7428H8.49513V14.7715Z"
                              fill="#2B2B2B"
                            />
                          </g>
                          <defs>
                            <filter
                              color-interpolation-filters="sRGB"
                              filterUnits="userSpaceOnUse"
                              height="104"
                              id="filter0_b_8973_3432"
                              width="104"
                              x="-38"
                              y="-38"
                            >
                              <feflood
                                flood-opacity="0"
                                result="BackgroundImageFix"
                              />
                              <fegaussianblur
                                in="BackgroundImageFix"
                                stdDeviation="19"
                              />
                              <fecomposite
                                in2="SourceAlpha"
                                operator="in"
                                result="effect1_backgroundBlur_8973_3432"
                              />
                              <feblend
                                in="SourceGraphic"
                                in2="effect1_backgroundBlur_8973_3432"
                                mode="normal"
                                result="shape"
                              />
                            </filter>
                          </defs>
                        </svg>
                      </span>
                    </button>
                    <div
                      class="emotion-25"
                      data-testid="styled-input-volume"
                      style="background: rgb(255, 255, 255);"
                    >
                      <div
                        role="presentation"
                      />
                    </div>
                  </div>
                </div>
              </div>
              <div
                class="emotion-26"
              >
                <div
                  class="emotion-6"
                >
                  <div
                    class="emotion-7"
                  >
                    <div
                      class="emotion-8"
                    >
                      <div>
                        <p
                          class="amp-cms--p"
                          style="text-align:left;"
                        >
                          <span
                            class="amp-cms--body-1"
                          >
                            Mobile Card
                          </span>
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </a>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`Gap FeaturedCategories inset mobile should match snapshots for size large 1`] = `
.emotion-0 {
  height: 100%;
  padding: 16px 0px;
}

.emotion-0>div {
  gap: 30px;
  display: grid;
  margin: 0 16px 0 16px;
}

.emotion-1 {
  margin-bottom: 15px;
  margin-left: 15px;
  margin: 0 0 15px 16px;
}

.emotion-1 .amp-cms--p {
  padding: 0;
  margin: 0;
  line-height: 0;
}

.emotion-1 .amp-cms--p>span {
  display: inline;
  white-space: break-spaces;
}

.emotion-1 .amp-cms--p a {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  position: relative;
  z-index: 2;
  pointer-events: auto;
}

.emotion-1 .amp-cms--p a:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-1 .amp-cms--legal-copy {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 10px;
  line-height: 1.4;
  letter-spacing: 0;
}

.emotion-1 sup {
  vertical-align: top;
  display: inline-block;
  margin-top: -0.25ex;
}

.emotion-1 sub {
  vertical-align: bottom;
  display: inline-block;
  margin-bottom: -0.25ex;
}

.emotion-1 .amp-cms--f-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 11px;
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--f-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 11px;
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--f-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 11px;
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--fn-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 11px;
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--body-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 18px;
  line-height: 1.2777777777777777;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--body-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 16px;
  line-height: 1.25;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--body-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 14px;
  line-height: 1.2857142857142858;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--body-4 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--body-5 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 10px;
  line-height: 1.4;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--eyebrow-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 30px;
  line-height: 1;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--eyebrow-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 26px;
  line-height: 1;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--eyebrow-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 24px;
  line-height: 1;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--headline-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 60px;
  line-height: 1;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--headline-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 55px;
  line-height: 1;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--headline-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 55px;
  line-height: 1;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--headline-4 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 50px;
  line-height: 1.1;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--headline-5 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 45px;
  line-height: 1;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--headline-6 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 40px;
  line-height: 1;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--headline-7 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 35px;
  line-height: 1;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--headlineAlt-1 {
  color: #2B2B2B;
  font-size: 64px;
  line-height: 1;
  letter-spacing: -0.3px;
  font-weight: 400;
}

.emotion-1 .amp-cms--headlineAlt-2 {
  color: #2B2B2B;
  font-size: 54px;
  line-height: 1;
  letter-spacing: -0.3px;
  font-weight: 400;
}

.emotion-1 .amp-cms--headlineAlt-3 {
  color: #2B2B2B;
  font-size: 34px;
  line-height: 1;
  letter-spacing: -0.3px;
  font-weight: 400;
}

.emotion-1 .amp-cms--headlineAlt-4 {
  color: #2B2B2B;
  font-size: 24px;
  line-height: 1.4166666666666667;
  letter-spacing: -0.3px;
  font-weight: 400;
}

.emotion-1 .amp-cms--headlineAlt-5 {
  color: #2B2B2B;
  font-size: 14px;
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-1 .amp-cms--headlineAlt-6 {
  color: #2B2B2B;
  font-size: 14px;
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-1 .amp-cms--headlineAlt-7 {
  color: #2B2B2B;
  font-size: 14px;
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-1 .amp-cms--promo-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 60px;
  line-height: 1;
  letter-spacing: 0px;
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-1 .amp-cms--promo-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 40px;
  line-height: 1;
  letter-spacing: 0px;
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-1 .amp-cms--promoAlt-1 {
  color: #2B2B2B;
  font-size: 14px;
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 600;
}

.emotion-1 .amp-cms--subhead-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 22px;
  line-height: 1.0909090909090908;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--subhead-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 20px;
  line-height: 1.1;
  letter-spacing: 0;
}

.emotion-1 .amp-cms--subhead-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 18px;
  line-height: 1.1111111111111112;
  letter-spacing: 0;
}

.emotion-2 {
  display: grid;
  grid-template-columns: 1fr 1fr;
}

.emotion-3 {
  position: relative;
  width: 100%;
}

.emotion-4 {
  cursor: pointer;
  height: 100%;
  width: 100%;
}

.emotion-5 {
  background: transparent;
  width: 100%!important;
  height: min(58.59375vw, 187.5px)!important;
  -webkit-background-size: cover;
  background-size: cover;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: end;
  -webkit-box-align: end;
  -ms-flex-align: end;
  align-items: end;
  box-sizing: border-box;
}

.emotion-6 {
  width: 100%!important;
  height: auto!important;
  -webkit-background-size: cover;
  background-size: cover;
  display: -webkit-box!important;
  display: -webkit-flex!important;
  display: -ms-flexbox!important;
  display: flex!important;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: end;
  -webkit-box-align: end;
  -ms-flex-align: end;
  align-items: end;
  box-sizing: border-box;
  padding: 20px 15px;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  gap: 20px;
}

.emotion-7 {
  box-sizing: border-box;
  height: auto!important;
  width: 100%!important;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  gap: 15px;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
}

.emotion-8 .amp-cms--p {
  padding: 0;
  margin: 0;
  line-height: 0;
}

.emotion-8 .amp-cms--p>span {
  display: inline;
  white-space: break-spaces;
}

.emotion-8 .amp-cms--p a {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  position: relative;
  z-index: 2;
  pointer-events: auto;
}

.emotion-8 .amp-cms--p a:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-8 .amp-cms--legal-copy {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, 2.666666666666667vw);
  line-height: 1.4;
  letter-spacing: 0;
}

.emotion-8 sup {
  vertical-align: top;
  display: inline-block;
  margin-top: -0.25ex;
}

.emotion-8 sub {
  vertical-align: bottom;
  display: inline-block;
  margin-bottom: -0.25ex;
}

.emotion-8 .amp-cms--f-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 2.933333333333333vw;
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-8 .amp-cms--f-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 2.933333333333333vw;
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-8 .amp-cms--f-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 2.933333333333333vw;
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-8 .amp-cms--fn-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 2.933333333333333vw;
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-8 .amp-cms--body-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, 4.8vw);
  line-height: 1.2777777777777777;
  letter-spacing: 0;
}

.emotion-8 .amp-cms--body-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, 4.266666666666667vw);
  line-height: 1.25;
  letter-spacing: 0;
}

.emotion-8 .amp-cms--body-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, 3.733333333333334vw);
  line-height: 1.2857142857142858;
  letter-spacing: 0;
}

.emotion-8 .amp-cms--body-4 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, 3.2vw);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-8 .amp-cms--body-5 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, 2.666666666666667vw);
  line-height: 1.4;
  letter-spacing: 0;
}

.emotion-8 .amp-cms--eyebrow-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, 8vw);
  line-height: 1;
  letter-spacing: 0;
}

.emotion-8 .amp-cms--eyebrow-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, 6.933333333333333vw);
  line-height: 1;
  letter-spacing: 0;
}

.emotion-8 .amp-cms--eyebrow-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, 6.4vw);
  line-height: 1;
  letter-spacing: 0;
}

.emotion-8 .amp-cms--headline-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, 16vw);
  line-height: 1;
  letter-spacing: 0;
}

.emotion-8 .amp-cms--headline-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, 14.666666666666666vw);
  line-height: 1;
  letter-spacing: 0;
}

.emotion-8 .amp-cms--headline-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, 14.666666666666666vw);
  line-height: 1;
  letter-spacing: 0;
}

.emotion-8 .amp-cms--headline-4 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, 13.333333333333334vw);
  line-height: 1.1;
  letter-spacing: 0;
}

.emotion-8 .amp-cms--headline-5 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, 12vw);
  line-height: 1;
  letter-spacing: 0;
}

.emotion-8 .amp-cms--headline-6 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, 10.666666666666668vw);
  line-height: 1;
  letter-spacing: 0;
}

.emotion-8 .amp-cms--headline-7 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, 9.333333333333334vw);
  line-height: 1;
  letter-spacing: 0;
}

.emotion-8 .amp-cms--headlineAlt-1 {
  color: #2B2B2B;
  font-size: max(13px, 17.066666666666666vw);
  line-height: 1;
  letter-spacing: -0.07999999999999999vw;
  font-weight: 400;
}

.emotion-8 .amp-cms--headlineAlt-2 {
  color: #2B2B2B;
  font-size: max(13px, 14.399999999999999vw);
  line-height: 1;
  letter-spacing: -0.07999999999999999vw;
  font-weight: 400;
}

.emotion-8 .amp-cms--headlineAlt-3 {
  color: #2B2B2B;
  font-size: max(13px, 9.066666666666666vw);
  line-height: 1;
  letter-spacing: -0.07999999999999999vw;
  font-weight: 400;
}

.emotion-8 .amp-cms--headlineAlt-4 {
  color: #2B2B2B;
  font-size: max(13px, 6.4vw);
  line-height: 1.4166666666666667;
  letter-spacing: -0.07999999999999999vw;
  font-weight: 400;
}

.emotion-8 .amp-cms--headlineAlt-5 {
  color: #2B2B2B;
  font-size: 3.733333333333334vw;
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-8 .amp-cms--headlineAlt-6 {
  color: #2B2B2B;
  font-size: 3.733333333333334vw;
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-8 .amp-cms--headlineAlt-7 {
  color: #2B2B2B;
  font-size: 3.733333333333334vw;
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-8 .amp-cms--promo-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(13px, 16vw);
  line-height: 1;
  letter-spacing: 0vw;
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-8 .amp-cms--promo-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(13px, 10.666666666666668vw);
  line-height: 1;
  letter-spacing: 0vw;
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-8 .amp-cms--promoAlt-1 {
  color: #2B2B2B;
  font-size: 3.733333333333334vw;
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 600;
}

.emotion-8 .amp-cms--subhead-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, 5.866666666666666vw);
  line-height: 1.0909090909090908;
  letter-spacing: 0;
}

.emotion-8 .amp-cms--subhead-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, 5.333333333333334vw);
  line-height: 1.1;
  letter-spacing: 0;
}

.emotion-8 .amp-cms--subhead-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, 4.8vw);
  line-height: 1.1111111111111112;
  letter-spacing: 0;
}

<div>
  <div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      class="emotion-0"
    >
      <div
        class="emotion-1"
      >
        <div>
          <p
            class="amp-cms--p"
            style="text-align:left;"
          >
            <span
              class="amp-cms--headline-3"
            >
              OPTIONAL HEADLINE
            </span>
          </p>
        </div>
      </div>
      <div
        class="emotion-2"
      >
        <div
          class="emotion-3"
        >
          <a
            aria-label="new arrivals"
            class="emotion-4"
            data-testid="conditional-link"
            href="www.google.com"
            target="_self"
          >
            <div
              aria-label="mobile alt text"
              class="emotion-5"
              height="0"
              role="img"
              width="0"
            >
              <div
                class="emotion-6"
              >
                <div
                  class="emotion-7"
                >
                  <div
                    class="emotion-8"
                  >
                    <div>
                      <p
                        class="amp-cms--p"
                        style="text-align:left;"
                      >
                        <span
                          class="amp-cms--body-1"
                        >
                          Mobile Card
                        </span>
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </a>
        </div>
        <div
          class="emotion-3"
        >
          <a
            aria-label="new arrivals"
            class="emotion-4"
            data-testid="conditional-link"
            href="www.google.com"
            target="_self"
          >
            <div
              aria-label="mobile alt text"
              class="emotion-5"
              height="0"
              role="img"
              width="0"
            >
              <div
                class="emotion-6"
              >
                <div
                  class="emotion-7"
                >
                  <div
                    class="emotion-8"
                  >
                    <div>
                      <p
                        class="amp-cms--p"
                        style="text-align:left;"
                      >
                        <span
                          class="amp-cms--body-1"
                        >
                          Mobile Card
                        </span>
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </a>
        </div>
        <div
          class="emotion-3"
        >
          <a
            aria-label="new arrivals"
            class="emotion-4"
            data-testid="conditional-link"
            href="www.google.com"
            target="_self"
          >
            <div
              aria-label="mobile alt text"
              class="emotion-5"
              height="0"
              role="img"
              width="0"
            >
              <div
                class="emotion-6"
              >
                <div
                  class="emotion-7"
                >
                  <div
                    class="emotion-8"
                  >
                    <div>
                      <p
                        class="amp-cms--p"
                        style="text-align:left;"
                      >
                        <span
                          class="amp-cms--body-1"
                        >
                          Mobile Card
                        </span>
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </a>
        </div>
        <div
          class="emotion-3"
        >
          <a
            aria-label="new arrivals"
            class="emotion-4"
            data-testid="conditional-link"
            href="www.google.com"
            target="_self"
          >
            <div
              aria-label="mobile alt text"
              class="emotion-5"
              height="0"
              role="img"
              width="0"
            >
              <div
                class="emotion-6"
              >
                <div
                  class="emotion-7"
                >
                  <div
                    class="emotion-8"
                  >
                    <div>
                      <p
                        class="amp-cms--p"
                        style="text-align:left;"
                      >
                        <span
                          class="amp-cms--body-1"
                        >
                          Mobile Card
                        </span>
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </a>
        </div>
      </div>
    </div>
  </div>
</div>
`;
