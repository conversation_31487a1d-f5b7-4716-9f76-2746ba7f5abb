// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`ISMBannerFullImage - AT should match double full image snapshot on desktop 1`] = `
{
  "asFragment": [Function],
  "baseElement": .emotion-0 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  position: relative;
  height: 100%;
  width: 100%;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-column-gap: 12px;
  column-gap: 12px;
  box-sizing: border-box;
  -webkit-box-pack: space-evenly;
  -ms-flex-pack: space-evenly;
  -webkit-justify-content: space-evenly;
  justify-content: space-evenly;
  -webkit-align-content: center;
  -ms-flex-line-pack: center;
  align-content: center;
}

.emotion-1 {
  background: transparent;
  aspect-ratio: 484/430;
  width: 100%;
  height: 100%;
}

.emotion-2 {
  box-sizing: border-box;
  padding: 15px;
  padding-bottom: 32px;
  position: relative;
  height: 100%;
  display: grid;
  grid-template-columns: 1fr;
  grid-template-rows: repeat(5, 1fr);
}

.emotion-3 {
  width: 100%;
  grid-row: 1;
}

<body>
    <div>
      <div
        style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
      >
        <div
          class="emotion-0"
        >
          <div
            class="mui_ism__background-type-container emotion-1"
            height="0"
            width="0"
          >
            <div
              class="emotion-2"
            >
              <div
                class="emotion-3"
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  </body>,
  "container": .emotion-0 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  position: relative;
  height: 100%;
  width: 100%;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-column-gap: 12px;
  column-gap: 12px;
  box-sizing: border-box;
  -webkit-box-pack: space-evenly;
  -ms-flex-pack: space-evenly;
  -webkit-justify-content: space-evenly;
  justify-content: space-evenly;
  -webkit-align-content: center;
  -ms-flex-line-pack: center;
  align-content: center;
}

.emotion-1 {
  background: transparent;
  aspect-ratio: 484/430;
  width: 100%;
  height: 100%;
}

.emotion-2 {
  box-sizing: border-box;
  padding: 15px;
  padding-bottom: 32px;
  position: relative;
  height: 100%;
  display: grid;
  grid-template-columns: 1fr;
  grid-template-rows: repeat(5, 1fr);
}

.emotion-3 {
  width: 100%;
  grid-row: 1;
}

<div>
    <div
      style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <div
        class="emotion-0"
      >
        <div
          class="mui_ism__background-type-container emotion-1"
          height="0"
          width="0"
        >
          <div
            class="emotion-2"
          >
            <div
              class="emotion-3"
            />
          </div>
        </div>
      </div>
    </div>
  </div>,
  "debug": [Function],
  "findAllByAltText": [Function],
  "findAllByDisplayValue": [Function],
  "findAllByLabelText": [Function],
  "findAllByPlaceholderText": [Function],
  "findAllByRole": [Function],
  "findAllByTestId": [Function],
  "findAllByText": [Function],
  "findAllByTitle": [Function],
  "findByAltText": [Function],
  "findByDisplayValue": [Function],
  "findByLabelText": [Function],
  "findByPlaceholderText": [Function],
  "findByRole": [Function],
  "findByTestId": [Function],
  "findByText": [Function],
  "findByTitle": [Function],
  "getAllByAltText": [Function],
  "getAllByDisplayValue": [Function],
  "getAllByLabelText": [Function],
  "getAllByPlaceholderText": [Function],
  "getAllByRole": [Function],
  "getAllByTestId": [Function],
  "getAllByText": [Function],
  "getAllByTitle": [Function],
  "getByAltText": [Function],
  "getByDisplayValue": [Function],
  "getByLabelText": [Function],
  "getByPlaceholderText": [Function],
  "getByRole": [Function],
  "getByTestId": [Function],
  "getByText": [Function],
  "getByTitle": [Function],
  "queryAllByAltText": [Function],
  "queryAllByDisplayValue": [Function],
  "queryAllByLabelText": [Function],
  "queryAllByPlaceholderText": [Function],
  "queryAllByRole": [Function],
  "queryAllByTestId": [Function],
  "queryAllByText": [Function],
  "queryAllByTitle": [Function],
  "queryByAltText": [Function],
  "queryByDisplayValue": [Function],
  "queryByLabelText": [Function],
  "queryByPlaceholderText": [Function],
  "queryByRole": [Function],
  "queryByTestId": [Function],
  "queryByText": [Function],
  "queryByTitle": [Function],
  "rerender": [Function],
  "unmount": [Function],
}
`;

exports[`ISMBannerFullImage - AT should match double full image snapshot on mobile 1`] = `
{
  "asFragment": [Function],
  "baseElement": .emotion-0 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  position: relative;
  height: 100%;
  width: 100%;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-column-gap: 12px;
  column-gap: 12px;
  box-sizing: border-box;
  -webkit-box-pack: space-evenly;
  -ms-flex-pack: space-evenly;
  -webkit-justify-content: space-evenly;
  justify-content: space-evenly;
  -webkit-align-content: center;
  -ms-flex-line-pack: center;
  align-content: center;
}

.emotion-1 {
  background: transparent;
  aspect-ratio: 335/340;
  width: 100%;
  height: 100%;
}

.emotion-2 {
  box-sizing: border-box;
  padding: 8px;
  padding-bottom: 32px;
  position: relative;
  height: 100%;
  display: grid;
  grid-template-columns: 1fr;
  grid-template-rows: repeat(5, 1fr);
}

.emotion-3 {
  width: 100%;
  grid-row: 1;
}

<body>
    <div>
      <div
        style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
      >
        <div
          class="emotion-0"
        >
          <div
            class="mui_ism__background-type-container emotion-1"
            height="0"
            width="0"
          >
            <div
              class="emotion-2"
            >
              <div
                class="emotion-3"
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  </body>,
  "container": .emotion-0 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  position: relative;
  height: 100%;
  width: 100%;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-column-gap: 12px;
  column-gap: 12px;
  box-sizing: border-box;
  -webkit-box-pack: space-evenly;
  -ms-flex-pack: space-evenly;
  -webkit-justify-content: space-evenly;
  justify-content: space-evenly;
  -webkit-align-content: center;
  -ms-flex-line-pack: center;
  align-content: center;
}

.emotion-1 {
  background: transparent;
  aspect-ratio: 335/340;
  width: 100%;
  height: 100%;
}

.emotion-2 {
  box-sizing: border-box;
  padding: 8px;
  padding-bottom: 32px;
  position: relative;
  height: 100%;
  display: grid;
  grid-template-columns: 1fr;
  grid-template-rows: repeat(5, 1fr);
}

.emotion-3 {
  width: 100%;
  grid-row: 1;
}

<div>
    <div
      style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <div
        class="emotion-0"
      >
        <div
          class="mui_ism__background-type-container emotion-1"
          height="0"
          width="0"
        >
          <div
            class="emotion-2"
          >
            <div
              class="emotion-3"
            />
          </div>
        </div>
      </div>
    </div>
  </div>,
  "debug": [Function],
  "findAllByAltText": [Function],
  "findAllByDisplayValue": [Function],
  "findAllByLabelText": [Function],
  "findAllByPlaceholderText": [Function],
  "findAllByRole": [Function],
  "findAllByTestId": [Function],
  "findAllByText": [Function],
  "findAllByTitle": [Function],
  "findByAltText": [Function],
  "findByDisplayValue": [Function],
  "findByLabelText": [Function],
  "findByPlaceholderText": [Function],
  "findByRole": [Function],
  "findByTestId": [Function],
  "findByText": [Function],
  "findByTitle": [Function],
  "getAllByAltText": [Function],
  "getAllByDisplayValue": [Function],
  "getAllByLabelText": [Function],
  "getAllByPlaceholderText": [Function],
  "getAllByRole": [Function],
  "getAllByTestId": [Function],
  "getAllByText": [Function],
  "getAllByTitle": [Function],
  "getByAltText": [Function],
  "getByDisplayValue": [Function],
  "getByLabelText": [Function],
  "getByPlaceholderText": [Function],
  "getByRole": [Function],
  "getByTestId": [Function],
  "getByText": [Function],
  "getByTitle": [Function],
  "queryAllByAltText": [Function],
  "queryAllByDisplayValue": [Function],
  "queryAllByLabelText": [Function],
  "queryAllByPlaceholderText": [Function],
  "queryAllByRole": [Function],
  "queryAllByTestId": [Function],
  "queryAllByText": [Function],
  "queryAllByTitle": [Function],
  "queryByAltText": [Function],
  "queryByDisplayValue": [Function],
  "queryByLabelText": [Function],
  "queryByPlaceholderText": [Function],
  "queryByRole": [Function],
  "queryByTestId": [Function],
  "queryByText": [Function],
  "queryByTitle": [Function],
  "rerender": [Function],
  "unmount": [Function],
}
`;

exports[`ISMBannerFullImage - AT should match single full image snapshot on desktop 1`] = `
{
  "asFragment": [Function],
  "baseElement": .emotion-0 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  position: relative;
  height: 100%;
  width: 100%;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-column-gap: 12px;
  column-gap: 12px;
  box-sizing: border-box;
  -webkit-box-pack: space-evenly;
  -ms-flex-pack: space-evenly;
  -webkit-justify-content: space-evenly;
  justify-content: space-evenly;
  -webkit-align-content: center;
  -ms-flex-line-pack: center;
  align-content: center;
}

.emotion-1 {
  background: url(https://example.com/image.svg) left no-repeat;
  -webkit-background-size: cover;
  background-size: cover;
  background-repeat: no-repeat;
  aspect-ratio: 234/430;
  width: 100%;
  height: 100%;
}

.emotion-2 {
  box-sizing: border-box;
  padding: 15px;
  padding-bottom: 32px;
  position: relative;
  height: 100%;
  display: grid;
  grid-template-columns: 1fr;
  grid-template-rows: repeat(5, 1fr);
}

.emotion-3 {
  width: 100%;
  grid-row: 1;
}

.emotion-4 {
  display: block;
  width: 100%;
  padding: 0 0 20px;
  text-align: center;
}

.emotion-5 .amp-cms--p {
  padding: 0;
  margin: 0;
  line-height: 0;
}

.emotion-5 .amp-cms--p>span {
  display: inline;
  white-space: break-spaces;
}

.emotion-5 .amp-cms--p a {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  position: relative;
  z-index: 2;
  pointer-events: auto;
}

.emotion-5 .amp-cms--p a:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-5 .amp-cms--legal-copy {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 10px;
  line-height: 1.5;
  letter-spacing: 0.5px;
  font-weight: 500;
}

.emotion-5 sup {
  vertical-align: top;
  display: inline-block;
  margin-top: -0.25ex;
}

.emotion-5 sub {
  vertical-align: bottom;
  display: inline-block;
  margin-bottom: -0.25ex;
}

.emotion-5 .amp-cms--f-0 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-5 .amp-cms--f-1 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-5 .amp-cms--f-2 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-5 .amp-cms--fn-1 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-5 .amp-cms--body-1 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 18px;
  line-height: 1.5555555555555556;
  letter-spacing: 0.9px;
  font-weight: 500;
}

.emotion-5 .amp-cms--body-2 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 16px;
  line-height: 1.25;
  letter-spacing: 0px;
  font-weight: 500;
}

.emotion-5 .amp-cms--body-3 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 14px;
  line-height: 1.5714285714285714;
  letter-spacing: 0.7px;
  font-weight: 500;
}

.emotion-5 .amp-cms--body-4 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.5;
  letter-spacing: 0px;
  font-weight: 500;
}

.emotion-5 .amp-cms--body-5 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 10px;
  line-height: 1.5;
  letter-spacing: 0.5px;
  font-weight: 500;
}

.emotion-5 .amp-cms--eyebrow-1 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 17px;
  line-height: 1.588235294117647;
  letter-spacing: 1.7px;
  font-weight: 700;
  text-transform: uppercase;
}

.emotion-5 .amp-cms--eyebrow-2 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 16px;
  line-height: 1.75;
  letter-spacing: 1.6px;
  font-weight: 700;
  text-transform: uppercase;
}

.emotion-5 .amp-cms--eyebrow-3 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.8333333333333333;
  letter-spacing: 0.82px;
  font-weight: 700;
  text-transform: uppercase;
}

.emotion-5 .amp-cms--headline-1 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 60px;
  line-height: 1;
  letter-spacing: 2.4px;
  text-transform: uppercase;
  font-weight: 500;
}

.emotion-5 .amp-cms--headline-2 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 56px;
  line-height: 1;
  letter-spacing: 2.8px;
  text-transform: uppercase;
  font-weight: 500;
}

.emotion-5 .amp-cms--headline-3 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 50px;
  line-height: 1;
  letter-spacing: 2.5px;
  text-transform: uppercase;
  font-weight: 500;
}

.emotion-5 .amp-cms--headline-4 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 40px;
  line-height: 1;
  letter-spacing: 2.8px;
  text-transform: uppercase;
  font-weight: 500;
}

.emotion-5 .amp-cms--headline-5 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 36px;
  line-height: 1;
  letter-spacing: 3.6px;
  text-transform: uppercase;
  font-weight: 600;
}

.emotion-5 .amp-cms--headline-6 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 28px;
  line-height: 1.1428571428571428;
  letter-spacing: 1.51px;
  text-transform: none;
  font-weight: 600;
}

.emotion-5 .amp-cms--headline-7 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 20px;
  line-height: 1.2;
  letter-spacing: 1.3px;
  text-transform: uppercase;
  font-weight: 700;
}

.emotion-5 .amp-cms--headlineAlt-1 {
  color: #000000;
  font-family: var(--font-soehne),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 124px;
  line-height: 1;
  letter-spacing: -0.4px;
  font-weight: 400;
}

.emotion-5 .amp-cms--headlineAlt-2 {
  color: #000000;
  font-family: var(--font-soehne),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 84px;
  line-height: 1;
  letter-spacing: -0.4px;
  font-weight: 400;
}

.emotion-5 .amp-cms--headlineAlt-3 {
  color: #000000;
  font-family: var(--font-soehne),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 64px;
  line-height: 1;
  letter-spacing: -0.4px;
  font-weight: 400;
}

.emotion-5 .amp-cms--headlineAlt-4 {
  color: #000000;
  font-family: var(--font-soehne),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 34px;
  line-height: 1;
  letter-spacing: -0.4px;
  font-weight: 400;
}

.emotion-5 .amp-cms--headlineAlt-5 {
  color: #000000;
  font-family: var(--font-soehne),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 16px;
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-5 .amp-cms--headlineAlt-6 {
  color: #000000;
  font-family: var(--font-soehne),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 16px;
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-5 .amp-cms--headlineAlt-7 {
  color: #000000;
  font-family: var(--font-soehne),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 16px;
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-5 .amp-cms--promo-1 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 100px;
  line-height: 1;
  letter-spacing: 0px;
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-5 .amp-cms--promo-2 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 80px;
  line-height: 1;
  letter-spacing: 0px;
  font-weight: 600;
}

.emotion-5 .amp-cms--promoAlt-1 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 16px;
  line-height: 0.0625;
  letter-spacing: 0;
}

.emotion-5 .amp-cms--subhead-1 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 24px;
  line-height: 1.5;
  letter-spacing: 1.2px;
  font-weight: 400;
  text-transform: uppercase;
}

.emotion-5 .amp-cms--subhead-2 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 18px;
  line-height: 1.5555555555555556;
  letter-spacing: 0.36px;
  font-weight: 500;
  text-transform: uppercase;
}

.emotion-5 .amp-cms--subhead-3 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 14px;
  line-height: 1.1428571428571428;
  letter-spacing: 0.7px;
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-6 {
  zIndex:2,boxSizing:border-box,display:flex,maxWidth:100%,paddingTop:16,paddingBottom:16,gap:16,justifyContent:left,flexDirection:? row :column,width:100%,flexWrap:wrap,gridRow: 5,;
}

.emotion-7 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: none;
  transition: none;
  box-sizing: border-box;
  border: 1.5px solid transparent;
  border-radius: 0;
  font-size: 15px;
  font-weight: 500;
  letter-spacing: 0px;
  min-height: auto;
  max-height: 55px;
  line-height: 1.3333333333333333;
  padding: 20px 40px;
  width: auto;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #FFFFFF;
  background-color: #000000;
  border-color: #000000;
  -webkit-align-self: flex-start;
  -ms-flex-item-align: flex-start;
  align-self: flex-start;
  white-space: normal;
}

.emotion-7:focus {
  outline: none;
}

.emotion-7>span {
  padding: 1px 0;
}

.emotion-7:hover,
.emotion-7:focus {
  text-shadow: 0 0 2px currentColor;
}

.emotion-7:active {
  color: #000000;
  background-color: #FFFFFF;
  border-color: #FFFFFF;
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-9 {
  position: absolute;
  width: -webkit-max-content;
  width: -moz-max-content;
  width: max-content;
  bottom: 8px;
  left: 50%;
  -webkit-transform: translate(-50%, 0);
  -moz-transform: translate(-50%, 0);
  -ms-transform: translate(-50%, 0);
  transform: translate(-50%, 0);
  z-index: 2;
}

.emotion-10 {
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  padding: 0;
}

.emotion-11 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 10px;
  line-height: 1.5;
  letter-spacing: 0.5px;
  font-weight: 500;
  text-transform: none;
  font-weight: normal;
  min-height: initial;
  color: #003764;
  font-size: 12px;
  line-height: 16px;
  letter-spacing: normal;
}

.emotion-12 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: none;
  transition: none;
  box-sizing: border-box;
  border: 1.5px solid transparent;
  border-radius: 0;
  font-size: 16px;
  font-weight: 500;
  letter-spacing: 0px;
  min-height: auto;
  max-height: auto;
  line-height: 1.3125;
  padding: 0;
  width: auto;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: left;
  border-radius: 0;
  border: none;
  background-color: transparent;
  color: #000000;
  height: auto;
  -webkit-box-pack: left;
  -ms-flex-pack: left;
  -webkit-justify-content: left;
  justify-content: left;
  font-weight: normal;
  text-transform: none;
  z-index: 1;
  color: #003764;
  -webkit-text-decoration: underline;
  text-decoration: underline;
  font-size: 12px;
  line-height: 16px;
  min-height: initial;
  padding: 0;
  margin-left: 5px;
  pointer-events: auto;
  text-underline-offset: 2px;
}

.emotion-12:focus {
  outline: none;
}

.emotion-12>span {
  padding: 1px 0;
}

.emotion-12 span span {
  padding-left: 3px;
  min-width: auto;
  min-height: auto;
  position: relative;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-transition: 250ms ease-in-out;
  transition: 250ms ease-in-out;
  transition-property: height,width,margin;
  top: 0;
  width: calc(1.2rem * 0.72);
}

.emotion-12 span span svg {
  -webkit-transition: fill 250ms ease-in-out;
  transition: fill 250ms ease-in-out;
  fill: #000000;
}

.emotion-12 span span {
  padding-left: initial;
}

.emotion-12:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-12:focus-visible {
  outline: auto;
}

.emotion-13 {
  cursor: pointer;
  position: absolute;
  width: 100%;
  height: 100%;
  z-index: 0;
  top: 0;
  left: 0;
}

<body>
    <div>
      <div
        style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
      >
        <div
          class="emotion-0"
        >
          <div
            class="mui_ism__background-type-container emotion-1"
            height="0"
            width="0"
          >
            <div
              class="emotion-2"
            >
              <div
                class="emotion-3"
              >
                <div
                  class="emotion-4"
                />
                <div
                  class="emotion-5"
                >
                  <div>
                    <p>
                      This is a sample text
                    </p>
                  </div>
                </div>
              </div>
              <div
                class="emotion-6"
                data-testid="cta-container"
              >
                <a
                  class="emotion-7"
                  color="dark"
                  href="/shop-now"
                >
                  Shop Now
                </a>
                <a
                  class="emotion-7"
                  color="dark"
                  href="/learn-more"
                >
                  Learn More
                </a>
              </div>
              <div
                class="emotion-9"
              >
                <div
                  class="emotion-10"
                >
                  <span
                    class="emotion-11"
                  >
                    prefix
                  </span>
                </div>
                <button
                  class="emotion-12"
                >
                  View Details
                </button>
              </div>
              <a
                aria-label="Click Here"
                class="emotion-13"
                href="/click-here"
                title="Click Here"
              >
                 
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>
  </body>,
  "container": .emotion-0 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  position: relative;
  height: 100%;
  width: 100%;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-column-gap: 12px;
  column-gap: 12px;
  box-sizing: border-box;
  -webkit-box-pack: space-evenly;
  -ms-flex-pack: space-evenly;
  -webkit-justify-content: space-evenly;
  justify-content: space-evenly;
  -webkit-align-content: center;
  -ms-flex-line-pack: center;
  align-content: center;
}

.emotion-1 {
  background: url(https://example.com/image.svg) left no-repeat;
  -webkit-background-size: cover;
  background-size: cover;
  background-repeat: no-repeat;
  aspect-ratio: 234/430;
  width: 100%;
  height: 100%;
}

.emotion-2 {
  box-sizing: border-box;
  padding: 15px;
  padding-bottom: 32px;
  position: relative;
  height: 100%;
  display: grid;
  grid-template-columns: 1fr;
  grid-template-rows: repeat(5, 1fr);
}

.emotion-3 {
  width: 100%;
  grid-row: 1;
}

.emotion-4 {
  display: block;
  width: 100%;
  padding: 0 0 20px;
  text-align: center;
}

.emotion-5 .amp-cms--p {
  padding: 0;
  margin: 0;
  line-height: 0;
}

.emotion-5 .amp-cms--p>span {
  display: inline;
  white-space: break-spaces;
}

.emotion-5 .amp-cms--p a {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  position: relative;
  z-index: 2;
  pointer-events: auto;
}

.emotion-5 .amp-cms--p a:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-5 .amp-cms--legal-copy {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 10px;
  line-height: 1.5;
  letter-spacing: 0.5px;
  font-weight: 500;
}

.emotion-5 sup {
  vertical-align: top;
  display: inline-block;
  margin-top: -0.25ex;
}

.emotion-5 sub {
  vertical-align: bottom;
  display: inline-block;
  margin-bottom: -0.25ex;
}

.emotion-5 .amp-cms--f-0 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-5 .amp-cms--f-1 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-5 .amp-cms--f-2 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-5 .amp-cms--fn-1 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-5 .amp-cms--body-1 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 18px;
  line-height: 1.5555555555555556;
  letter-spacing: 0.9px;
  font-weight: 500;
}

.emotion-5 .amp-cms--body-2 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 16px;
  line-height: 1.25;
  letter-spacing: 0px;
  font-weight: 500;
}

.emotion-5 .amp-cms--body-3 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 14px;
  line-height: 1.5714285714285714;
  letter-spacing: 0.7px;
  font-weight: 500;
}

.emotion-5 .amp-cms--body-4 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.5;
  letter-spacing: 0px;
  font-weight: 500;
}

.emotion-5 .amp-cms--body-5 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 10px;
  line-height: 1.5;
  letter-spacing: 0.5px;
  font-weight: 500;
}

.emotion-5 .amp-cms--eyebrow-1 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 17px;
  line-height: 1.588235294117647;
  letter-spacing: 1.7px;
  font-weight: 700;
  text-transform: uppercase;
}

.emotion-5 .amp-cms--eyebrow-2 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 16px;
  line-height: 1.75;
  letter-spacing: 1.6px;
  font-weight: 700;
  text-transform: uppercase;
}

.emotion-5 .amp-cms--eyebrow-3 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.8333333333333333;
  letter-spacing: 0.82px;
  font-weight: 700;
  text-transform: uppercase;
}

.emotion-5 .amp-cms--headline-1 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 60px;
  line-height: 1;
  letter-spacing: 2.4px;
  text-transform: uppercase;
  font-weight: 500;
}

.emotion-5 .amp-cms--headline-2 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 56px;
  line-height: 1;
  letter-spacing: 2.8px;
  text-transform: uppercase;
  font-weight: 500;
}

.emotion-5 .amp-cms--headline-3 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 50px;
  line-height: 1;
  letter-spacing: 2.5px;
  text-transform: uppercase;
  font-weight: 500;
}

.emotion-5 .amp-cms--headline-4 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 40px;
  line-height: 1;
  letter-spacing: 2.8px;
  text-transform: uppercase;
  font-weight: 500;
}

.emotion-5 .amp-cms--headline-5 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 36px;
  line-height: 1;
  letter-spacing: 3.6px;
  text-transform: uppercase;
  font-weight: 600;
}

.emotion-5 .amp-cms--headline-6 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 28px;
  line-height: 1.1428571428571428;
  letter-spacing: 1.51px;
  text-transform: none;
  font-weight: 600;
}

.emotion-5 .amp-cms--headline-7 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 20px;
  line-height: 1.2;
  letter-spacing: 1.3px;
  text-transform: uppercase;
  font-weight: 700;
}

.emotion-5 .amp-cms--headlineAlt-1 {
  color: #000000;
  font-family: var(--font-soehne),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 124px;
  line-height: 1;
  letter-spacing: -0.4px;
  font-weight: 400;
}

.emotion-5 .amp-cms--headlineAlt-2 {
  color: #000000;
  font-family: var(--font-soehne),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 84px;
  line-height: 1;
  letter-spacing: -0.4px;
  font-weight: 400;
}

.emotion-5 .amp-cms--headlineAlt-3 {
  color: #000000;
  font-family: var(--font-soehne),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 64px;
  line-height: 1;
  letter-spacing: -0.4px;
  font-weight: 400;
}

.emotion-5 .amp-cms--headlineAlt-4 {
  color: #000000;
  font-family: var(--font-soehne),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 34px;
  line-height: 1;
  letter-spacing: -0.4px;
  font-weight: 400;
}

.emotion-5 .amp-cms--headlineAlt-5 {
  color: #000000;
  font-family: var(--font-soehne),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 16px;
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-5 .amp-cms--headlineAlt-6 {
  color: #000000;
  font-family: var(--font-soehne),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 16px;
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-5 .amp-cms--headlineAlt-7 {
  color: #000000;
  font-family: var(--font-soehne),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 16px;
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-5 .amp-cms--promo-1 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 100px;
  line-height: 1;
  letter-spacing: 0px;
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-5 .amp-cms--promo-2 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 80px;
  line-height: 1;
  letter-spacing: 0px;
  font-weight: 600;
}

.emotion-5 .amp-cms--promoAlt-1 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 16px;
  line-height: 0.0625;
  letter-spacing: 0;
}

.emotion-5 .amp-cms--subhead-1 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 24px;
  line-height: 1.5;
  letter-spacing: 1.2px;
  font-weight: 400;
  text-transform: uppercase;
}

.emotion-5 .amp-cms--subhead-2 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 18px;
  line-height: 1.5555555555555556;
  letter-spacing: 0.36px;
  font-weight: 500;
  text-transform: uppercase;
}

.emotion-5 .amp-cms--subhead-3 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 14px;
  line-height: 1.1428571428571428;
  letter-spacing: 0.7px;
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-6 {
  zIndex:2,boxSizing:border-box,display:flex,maxWidth:100%,paddingTop:16,paddingBottom:16,gap:16,justifyContent:left,flexDirection:? row :column,width:100%,flexWrap:wrap,gridRow: 5,;
}

.emotion-7 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: none;
  transition: none;
  box-sizing: border-box;
  border: 1.5px solid transparent;
  border-radius: 0;
  font-size: 15px;
  font-weight: 500;
  letter-spacing: 0px;
  min-height: auto;
  max-height: 55px;
  line-height: 1.3333333333333333;
  padding: 20px 40px;
  width: auto;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #FFFFFF;
  background-color: #000000;
  border-color: #000000;
  -webkit-align-self: flex-start;
  -ms-flex-item-align: flex-start;
  align-self: flex-start;
  white-space: normal;
}

.emotion-7:focus {
  outline: none;
}

.emotion-7>span {
  padding: 1px 0;
}

.emotion-7:hover,
.emotion-7:focus {
  text-shadow: 0 0 2px currentColor;
}

.emotion-7:active {
  color: #000000;
  background-color: #FFFFFF;
  border-color: #FFFFFF;
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-9 {
  position: absolute;
  width: -webkit-max-content;
  width: -moz-max-content;
  width: max-content;
  bottom: 8px;
  left: 50%;
  -webkit-transform: translate(-50%, 0);
  -moz-transform: translate(-50%, 0);
  -ms-transform: translate(-50%, 0);
  transform: translate(-50%, 0);
  z-index: 2;
}

.emotion-10 {
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  padding: 0;
}

.emotion-11 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 10px;
  line-height: 1.5;
  letter-spacing: 0.5px;
  font-weight: 500;
  text-transform: none;
  font-weight: normal;
  min-height: initial;
  color: #003764;
  font-size: 12px;
  line-height: 16px;
  letter-spacing: normal;
}

.emotion-12 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: none;
  transition: none;
  box-sizing: border-box;
  border: 1.5px solid transparent;
  border-radius: 0;
  font-size: 16px;
  font-weight: 500;
  letter-spacing: 0px;
  min-height: auto;
  max-height: auto;
  line-height: 1.3125;
  padding: 0;
  width: auto;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: left;
  border-radius: 0;
  border: none;
  background-color: transparent;
  color: #000000;
  height: auto;
  -webkit-box-pack: left;
  -ms-flex-pack: left;
  -webkit-justify-content: left;
  justify-content: left;
  font-weight: normal;
  text-transform: none;
  z-index: 1;
  color: #003764;
  -webkit-text-decoration: underline;
  text-decoration: underline;
  font-size: 12px;
  line-height: 16px;
  min-height: initial;
  padding: 0;
  margin-left: 5px;
  pointer-events: auto;
  text-underline-offset: 2px;
}

.emotion-12:focus {
  outline: none;
}

.emotion-12>span {
  padding: 1px 0;
}

.emotion-12 span span {
  padding-left: 3px;
  min-width: auto;
  min-height: auto;
  position: relative;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-transition: 250ms ease-in-out;
  transition: 250ms ease-in-out;
  transition-property: height,width,margin;
  top: 0;
  width: calc(1.2rem * 0.72);
}

.emotion-12 span span svg {
  -webkit-transition: fill 250ms ease-in-out;
  transition: fill 250ms ease-in-out;
  fill: #000000;
}

.emotion-12 span span {
  padding-left: initial;
}

.emotion-12:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-12:focus-visible {
  outline: auto;
}

.emotion-13 {
  cursor: pointer;
  position: absolute;
  width: 100%;
  height: 100%;
  z-index: 0;
  top: 0;
  left: 0;
}

<div>
    <div
      style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <div
        class="emotion-0"
      >
        <div
          class="mui_ism__background-type-container emotion-1"
          height="0"
          width="0"
        >
          <div
            class="emotion-2"
          >
            <div
              class="emotion-3"
            >
              <div
                class="emotion-4"
              />
              <div
                class="emotion-5"
              >
                <div>
                  <p>
                    This is a sample text
                  </p>
                </div>
              </div>
            </div>
            <div
              class="emotion-6"
              data-testid="cta-container"
            >
              <a
                class="emotion-7"
                color="dark"
                href="/shop-now"
              >
                Shop Now
              </a>
              <a
                class="emotion-7"
                color="dark"
                href="/learn-more"
              >
                Learn More
              </a>
            </div>
            <div
              class="emotion-9"
            >
              <div
                class="emotion-10"
              >
                <span
                  class="emotion-11"
                >
                  prefix
                </span>
              </div>
              <button
                class="emotion-12"
              >
                View Details
              </button>
            </div>
            <a
              aria-label="Click Here"
              class="emotion-13"
              href="/click-here"
              title="Click Here"
            >
               
            </a>
          </div>
        </div>
      </div>
    </div>
  </div>,
  "debug": [Function],
  "findAllByAltText": [Function],
  "findAllByDisplayValue": [Function],
  "findAllByLabelText": [Function],
  "findAllByPlaceholderText": [Function],
  "findAllByRole": [Function],
  "findAllByTestId": [Function],
  "findAllByText": [Function],
  "findAllByTitle": [Function],
  "findByAltText": [Function],
  "findByDisplayValue": [Function],
  "findByLabelText": [Function],
  "findByPlaceholderText": [Function],
  "findByRole": [Function],
  "findByTestId": [Function],
  "findByText": [Function],
  "findByTitle": [Function],
  "getAllByAltText": [Function],
  "getAllByDisplayValue": [Function],
  "getAllByLabelText": [Function],
  "getAllByPlaceholderText": [Function],
  "getAllByRole": [Function],
  "getAllByTestId": [Function],
  "getAllByText": [Function],
  "getAllByTitle": [Function],
  "getByAltText": [Function],
  "getByDisplayValue": [Function],
  "getByLabelText": [Function],
  "getByPlaceholderText": [Function],
  "getByRole": [Function],
  "getByTestId": [Function],
  "getByText": [Function],
  "getByTitle": [Function],
  "queryAllByAltText": [Function],
  "queryAllByDisplayValue": [Function],
  "queryAllByLabelText": [Function],
  "queryAllByPlaceholderText": [Function],
  "queryAllByRole": [Function],
  "queryAllByTestId": [Function],
  "queryAllByText": [Function],
  "queryAllByTitle": [Function],
  "queryByAltText": [Function],
  "queryByDisplayValue": [Function],
  "queryByLabelText": [Function],
  "queryByPlaceholderText": [Function],
  "queryByRole": [Function],
  "queryByTestId": [Function],
  "queryByText": [Function],
  "queryByTitle": [Function],
  "rerender": [Function],
  "unmount": [Function],
}
`;

exports[`ISMBannerFullImage - AT should match single full image snapshot on mobile 1`] = `
{
  "asFragment": [Function],
  "baseElement": .emotion-0 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  position: relative;
  height: 100%;
  width: 100%;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-column-gap: 12px;
  column-gap: 12px;
  box-sizing: border-box;
  -webkit-box-pack: space-evenly;
  -ms-flex-pack: space-evenly;
  -webkit-justify-content: space-evenly;
  justify-content: space-evenly;
  -webkit-align-content: center;
  -ms-flex-line-pack: center;
  align-content: center;
}

.emotion-1 {
  background: url(https://example.com/image.svg) left no-repeat;
  -webkit-background-size: cover;
  background-size: cover;
  background-repeat: no-repeat;
  aspect-ratio: 160/340;
  width: 100%;
  height: 100%;
}

.emotion-2 {
  box-sizing: border-box;
  padding: 8px;
  padding-bottom: 32px;
  position: relative;
  height: 100%;
  display: grid;
  grid-template-columns: 1fr;
  grid-template-rows: repeat(5, 1fr);
}

.emotion-3 {
  width: 100%;
  grid-row: 1;
}

.emotion-4 {
  display: block;
  width: 100%;
  padding: 0 0 20px;
  text-align: center;
}

.emotion-5 .amp-cms--p {
  padding: 0;
  margin: 0;
  line-height: 0;
}

.emotion-5 .amp-cms--p>span {
  display: inline;
  white-space: break-spaces;
}

.emotion-5 .amp-cms--p a {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  position: relative;
  z-index: 2;
  pointer-events: auto;
}

.emotion-5 .amp-cms--p a:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-5 .amp-cms--legal-copy {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 10px;
  line-height: 1.5;
  letter-spacing: 0.5px;
  font-weight: 500;
}

.emotion-5 sup {
  vertical-align: top;
  display: inline-block;
  margin-top: -0.25ex;
}

.emotion-5 sub {
  vertical-align: bottom;
  display: inline-block;
  margin-bottom: -0.25ex;
}

.emotion-5 .amp-cms--f-0 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 11px;
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-5 .amp-cms--f-1 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 11px;
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-5 .amp-cms--f-2 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 11px;
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-5 .amp-cms--fn-1 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 11px;
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-5 .amp-cms--body-1 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 16px;
  line-height: 1.625;
  letter-spacing: 0.8px;
  font-weight: 500;
}

.emotion-5 .amp-cms--body-2 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 14px;
  line-height: 1.4285714285714286;
  letter-spacing: 0px;
  font-weight: 500;
}

.emotion-5 .amp-cms--body-3 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.5;
  letter-spacing: 0.6px;
  font-weight: 500;
}

.emotion-5 .amp-cms--body-4 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 10px;
  line-height: 1.6;
  letter-spacing: 0px;
  font-weight: 500;
}

.emotion-5 .amp-cms--body-5 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 10px;
  line-height: 1.5;
  letter-spacing: 0.5px;
  font-weight: 500;
}

.emotion-5 .amp-cms--eyebrow-1 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 2;
  letter-spacing: 1.2px;
  font-weight: 700;
  text-transform: uppercase;
}

.emotion-5 .amp-cms--eyebrow-2 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 11px;
  line-height: 2;
  letter-spacing: 1.1px;
  font-weight: 700;
  text-transform: uppercase;
}

.emotion-5 .amp-cms--eyebrow-3 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 10px;
  line-height: 1.5;
  letter-spacing: 0.68px;
  font-weight: 700;
  text-transform: uppercase;
}

.emotion-5 .amp-cms--headline-1 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 36px;
  line-height: 1;
  letter-spacing: 1.37px;
  text-transform: uppercase;
  font-weight: 500;
}

.emotion-5 .amp-cms--headline-2 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 34px;
  line-height: 1;
  letter-spacing: 1.7px;
  text-transform: uppercase;
  font-weight: 500;
}

.emotion-5 .amp-cms--headline-3 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 30px;
  line-height: 1;
  letter-spacing: 0.3px;
  text-transform: uppercase;
  font-weight: 500;
}

.emotion-5 .amp-cms--headline-4 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 24px;
  line-height: 1;
  letter-spacing: 1.8px;
  text-transform: uppercase;
  font-weight: 500;
}

.emotion-5 .amp-cms--headline-5 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 22px;
  line-height: 1.1818181818181819;
  letter-spacing: 2.2px;
  text-transform: uppercase;
  font-weight: 600;
}

.emotion-5 .amp-cms--headline-6 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 16px;
  line-height: 1.375;
  letter-spacing: 0.8px;
  text-transform: none;
  font-weight: 600;
}

.emotion-5 .amp-cms--headline-7 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 14px;
  line-height: 1.4285714285714286;
  letter-spacing: 0.1px;
  text-transform: uppercase;
  font-weight: 700;
}

.emotion-5 .amp-cms--headlineAlt-1 {
  color: #000000;
  font-family: var(--font-soehne),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 64px;
  line-height: 1;
  letter-spacing: -0.3px;
  font-weight: 400;
}

.emotion-5 .amp-cms--headlineAlt-2 {
  color: #000000;
  font-family: var(--font-soehne),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 54px;
  line-height: 1;
  letter-spacing: -0.3px;
  font-weight: 400;
}

.emotion-5 .amp-cms--headlineAlt-3 {
  color: #000000;
  font-family: var(--font-soehne),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 34px;
  line-height: 1;
  letter-spacing: -0.3px;
  font-weight: 400;
}

.emotion-5 .amp-cms--headlineAlt-4 {
  color: #000000;
  font-family: var(--font-soehne),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 24px;
  line-height: 1.4166666666666667;
  letter-spacing: -0.3px;
  font-weight: 400;
}

.emotion-5 .amp-cms--headlineAlt-5 {
  color: #000000;
  font-family: var(--font-soehne),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 14px;
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-5 .amp-cms--headlineAlt-6 {
  color: #000000;
  font-family: var(--font-soehne),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 14px;
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-5 .amp-cms--headlineAlt-7 {
  color: #000000;
  font-family: var(--font-soehne),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 14px;
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-5 .amp-cms--promo-1 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 60px;
  line-height: 1;
  letter-spacing: 0px;
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-5 .amp-cms--promo-2 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 40px;
  line-height: 1;
  letter-spacing: 0px;
  font-weight: 600;
}

.emotion-5 .amp-cms--promoAlt-1 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 14px;
  line-height: 0.07142857142857142;
  letter-spacing: 0;
}

.emotion-5 .amp-cms--subhead-1 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 16px;
  line-height: 1.75;
  letter-spacing: 0.32px;
  font-weight: 400;
  text-transform: uppercase;
}

.emotion-5 .amp-cms--subhead-2 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 14px;
  line-height: 1.5714285714285714;
  letter-spacing: 0.7px;
  font-weight: 500;
  text-transform: uppercase;
}

.emotion-5 .amp-cms--subhead-3 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 13px;
  line-height: 1;
  letter-spacing: 0.97px;
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-6 {
  zIndex:2,boxSizing:border-box,display:flex,maxWidth:100%,paddingTop:16,paddingBottom:16,gap:16,justifyContent:left,flexDirection:? row :column,width:100%,flexWrap:wrap,gridRow: 5,;
}

.emotion-7 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: none;
  transition: none;
  box-sizing: border-box;
  border: 1.5px solid transparent;
  border-radius: 0;
  font-size: 15px;
  font-weight: 500;
  letter-spacing: 0px;
  min-height: auto;
  max-height: 55px;
  line-height: 1.3333333333333333;
  padding: 20px 40px;
  width: auto;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #FFFFFF;
  background-color: #000000;
  border-color: #000000;
  -webkit-align-self: flex-start;
  -ms-flex-item-align: flex-start;
  align-self: flex-start;
  white-space: normal;
}

.emotion-7:focus {
  outline: none;
}

.emotion-7>span {
  padding: 1px 0;
}

.emotion-7:hover,
.emotion-7:focus {
  text-shadow: 0 0 2px currentColor;
}

.emotion-7:active {
  color: #000000;
  background-color: #FFFFFF;
  border-color: #FFFFFF;
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-9 {
  position: absolute;
  width: -webkit-max-content;
  width: -moz-max-content;
  width: max-content;
  bottom: 8px;
  left: 50%;
  -webkit-transform: translate(-50%, 0);
  -moz-transform: translate(-50%, 0);
  -ms-transform: translate(-50%, 0);
  transform: translate(-50%, 0);
  z-index: 2;
}

.emotion-10 {
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  padding: 0;
}

.emotion-11 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 10px;
  line-height: 1.5;
  letter-spacing: 0.5px;
  font-weight: 500;
  text-transform: none;
  font-weight: normal;
  min-height: initial;
  color: #003764;
  font-size: 11px;
  line-height: 14px;
  letter-spacing: normal;
}

.emotion-12 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: none;
  transition: none;
  box-sizing: border-box;
  border: 1.5px solid transparent;
  border-radius: 0;
  font-size: 16px;
  font-weight: 500;
  letter-spacing: 0px;
  min-height: 44px;
  max-height: auto;
  line-height: 1.3125;
  padding: 0;
  width: auto;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: left;
  border-radius: 0;
  border: none;
  background-color: transparent;
  color: #000000;
  height: auto;
  -webkit-box-pack: left;
  -ms-flex-pack: left;
  -webkit-justify-content: left;
  justify-content: left;
  font-weight: normal;
  text-transform: none;
  z-index: 1;
  color: #003764;
  -webkit-text-decoration: underline;
  text-decoration: underline;
  font-size: 11px;
  line-height: 14px;
  min-height: initial;
  padding: 0;
  margin-left: 5px;
  pointer-events: auto;
  text-underline-offset: 2px;
}

.emotion-12:focus {
  outline: none;
}

.emotion-12>span {
  padding: 1px 0;
}

.emotion-12 span span {
  padding-left: 3px;
  min-width: auto;
  min-height: auto;
  position: relative;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-transition: 250ms ease-in-out;
  transition: 250ms ease-in-out;
  transition-property: height,width,margin;
  top: 0;
  width: calc(1.2rem * 0.72);
}

.emotion-12 span span svg {
  -webkit-transition: fill 250ms ease-in-out;
  transition: fill 250ms ease-in-out;
  fill: #000000;
}

.emotion-12 span span {
  padding-left: initial;
}

.emotion-12:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-12:focus-visible {
  outline: auto;
}

.emotion-13 {
  cursor: pointer;
  position: absolute;
  width: 100%;
  height: 100%;
  z-index: 0;
  top: 0;
  left: 0;
}

<body>
    <div>
      <div
        style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
      >
        <div
          class="emotion-0"
        >
          <div
            class="mui_ism__background-type-container emotion-1"
            height="0"
            width="0"
          >
            <div
              class="emotion-2"
            >
              <div
                class="emotion-3"
              >
                <div
                  class="emotion-4"
                />
                <div
                  class="emotion-5"
                >
                  <div>
                    <p>
                      This is a sample text
                    </p>
                  </div>
                </div>
              </div>
              <div
                class="emotion-6"
                data-testid="cta-container"
              >
                <a
                  class="emotion-7"
                  color="dark"
                  href="/shop-now"
                >
                  Shop Now
                </a>
                <a
                  class="emotion-7"
                  color="dark"
                  href="/learn-more"
                >
                  Learn More
                </a>
              </div>
              <div
                class="emotion-9"
              >
                <div
                  class="emotion-10"
                >
                  <span
                    class="emotion-11"
                  >
                    prefix
                  </span>
                </div>
                <button
                  class="emotion-12"
                >
                  View Details
                </button>
              </div>
              <a
                aria-label="Click Here"
                class="emotion-13"
                href="/click-here"
                title="Click Here"
              >
                 
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>
  </body>,
  "container": .emotion-0 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  position: relative;
  height: 100%;
  width: 100%;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-column-gap: 12px;
  column-gap: 12px;
  box-sizing: border-box;
  -webkit-box-pack: space-evenly;
  -ms-flex-pack: space-evenly;
  -webkit-justify-content: space-evenly;
  justify-content: space-evenly;
  -webkit-align-content: center;
  -ms-flex-line-pack: center;
  align-content: center;
}

.emotion-1 {
  background: url(https://example.com/image.svg) left no-repeat;
  -webkit-background-size: cover;
  background-size: cover;
  background-repeat: no-repeat;
  aspect-ratio: 160/340;
  width: 100%;
  height: 100%;
}

.emotion-2 {
  box-sizing: border-box;
  padding: 8px;
  padding-bottom: 32px;
  position: relative;
  height: 100%;
  display: grid;
  grid-template-columns: 1fr;
  grid-template-rows: repeat(5, 1fr);
}

.emotion-3 {
  width: 100%;
  grid-row: 1;
}

.emotion-4 {
  display: block;
  width: 100%;
  padding: 0 0 20px;
  text-align: center;
}

.emotion-5 .amp-cms--p {
  padding: 0;
  margin: 0;
  line-height: 0;
}

.emotion-5 .amp-cms--p>span {
  display: inline;
  white-space: break-spaces;
}

.emotion-5 .amp-cms--p a {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  position: relative;
  z-index: 2;
  pointer-events: auto;
}

.emotion-5 .amp-cms--p a:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-5 .amp-cms--legal-copy {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 10px;
  line-height: 1.5;
  letter-spacing: 0.5px;
  font-weight: 500;
}

.emotion-5 sup {
  vertical-align: top;
  display: inline-block;
  margin-top: -0.25ex;
}

.emotion-5 sub {
  vertical-align: bottom;
  display: inline-block;
  margin-bottom: -0.25ex;
}

.emotion-5 .amp-cms--f-0 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 11px;
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-5 .amp-cms--f-1 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 11px;
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-5 .amp-cms--f-2 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 11px;
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-5 .amp-cms--fn-1 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 11px;
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-5 .amp-cms--body-1 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 16px;
  line-height: 1.625;
  letter-spacing: 0.8px;
  font-weight: 500;
}

.emotion-5 .amp-cms--body-2 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 14px;
  line-height: 1.4285714285714286;
  letter-spacing: 0px;
  font-weight: 500;
}

.emotion-5 .amp-cms--body-3 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.5;
  letter-spacing: 0.6px;
  font-weight: 500;
}

.emotion-5 .amp-cms--body-4 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 10px;
  line-height: 1.6;
  letter-spacing: 0px;
  font-weight: 500;
}

.emotion-5 .amp-cms--body-5 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 10px;
  line-height: 1.5;
  letter-spacing: 0.5px;
  font-weight: 500;
}

.emotion-5 .amp-cms--eyebrow-1 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 2;
  letter-spacing: 1.2px;
  font-weight: 700;
  text-transform: uppercase;
}

.emotion-5 .amp-cms--eyebrow-2 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 11px;
  line-height: 2;
  letter-spacing: 1.1px;
  font-weight: 700;
  text-transform: uppercase;
}

.emotion-5 .amp-cms--eyebrow-3 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 10px;
  line-height: 1.5;
  letter-spacing: 0.68px;
  font-weight: 700;
  text-transform: uppercase;
}

.emotion-5 .amp-cms--headline-1 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 36px;
  line-height: 1;
  letter-spacing: 1.37px;
  text-transform: uppercase;
  font-weight: 500;
}

.emotion-5 .amp-cms--headline-2 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 34px;
  line-height: 1;
  letter-spacing: 1.7px;
  text-transform: uppercase;
  font-weight: 500;
}

.emotion-5 .amp-cms--headline-3 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 30px;
  line-height: 1;
  letter-spacing: 0.3px;
  text-transform: uppercase;
  font-weight: 500;
}

.emotion-5 .amp-cms--headline-4 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 24px;
  line-height: 1;
  letter-spacing: 1.8px;
  text-transform: uppercase;
  font-weight: 500;
}

.emotion-5 .amp-cms--headline-5 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 22px;
  line-height: 1.1818181818181819;
  letter-spacing: 2.2px;
  text-transform: uppercase;
  font-weight: 600;
}

.emotion-5 .amp-cms--headline-6 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 16px;
  line-height: 1.375;
  letter-spacing: 0.8px;
  text-transform: none;
  font-weight: 600;
}

.emotion-5 .amp-cms--headline-7 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 14px;
  line-height: 1.4285714285714286;
  letter-spacing: 0.1px;
  text-transform: uppercase;
  font-weight: 700;
}

.emotion-5 .amp-cms--headlineAlt-1 {
  color: #000000;
  font-family: var(--font-soehne),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 64px;
  line-height: 1;
  letter-spacing: -0.3px;
  font-weight: 400;
}

.emotion-5 .amp-cms--headlineAlt-2 {
  color: #000000;
  font-family: var(--font-soehne),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 54px;
  line-height: 1;
  letter-spacing: -0.3px;
  font-weight: 400;
}

.emotion-5 .amp-cms--headlineAlt-3 {
  color: #000000;
  font-family: var(--font-soehne),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 34px;
  line-height: 1;
  letter-spacing: -0.3px;
  font-weight: 400;
}

.emotion-5 .amp-cms--headlineAlt-4 {
  color: #000000;
  font-family: var(--font-soehne),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 24px;
  line-height: 1.4166666666666667;
  letter-spacing: -0.3px;
  font-weight: 400;
}

.emotion-5 .amp-cms--headlineAlt-5 {
  color: #000000;
  font-family: var(--font-soehne),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 14px;
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-5 .amp-cms--headlineAlt-6 {
  color: #000000;
  font-family: var(--font-soehne),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 14px;
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-5 .amp-cms--headlineAlt-7 {
  color: #000000;
  font-family: var(--font-soehne),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 14px;
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-5 .amp-cms--promo-1 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 60px;
  line-height: 1;
  letter-spacing: 0px;
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-5 .amp-cms--promo-2 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 40px;
  line-height: 1;
  letter-spacing: 0px;
  font-weight: 600;
}

.emotion-5 .amp-cms--promoAlt-1 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 14px;
  line-height: 0.07142857142857142;
  letter-spacing: 0;
}

.emotion-5 .amp-cms--subhead-1 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 16px;
  line-height: 1.75;
  letter-spacing: 0.32px;
  font-weight: 400;
  text-transform: uppercase;
}

.emotion-5 .amp-cms--subhead-2 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 14px;
  line-height: 1.5714285714285714;
  letter-spacing: 0.7px;
  font-weight: 500;
  text-transform: uppercase;
}

.emotion-5 .amp-cms--subhead-3 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 13px;
  line-height: 1;
  letter-spacing: 0.97px;
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-6 {
  zIndex:2,boxSizing:border-box,display:flex,maxWidth:100%,paddingTop:16,paddingBottom:16,gap:16,justifyContent:left,flexDirection:? row :column,width:100%,flexWrap:wrap,gridRow: 5,;
}

.emotion-7 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: none;
  transition: none;
  box-sizing: border-box;
  border: 1.5px solid transparent;
  border-radius: 0;
  font-size: 15px;
  font-weight: 500;
  letter-spacing: 0px;
  min-height: auto;
  max-height: 55px;
  line-height: 1.3333333333333333;
  padding: 20px 40px;
  width: auto;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #FFFFFF;
  background-color: #000000;
  border-color: #000000;
  -webkit-align-self: flex-start;
  -ms-flex-item-align: flex-start;
  align-self: flex-start;
  white-space: normal;
}

.emotion-7:focus {
  outline: none;
}

.emotion-7>span {
  padding: 1px 0;
}

.emotion-7:hover,
.emotion-7:focus {
  text-shadow: 0 0 2px currentColor;
}

.emotion-7:active {
  color: #000000;
  background-color: #FFFFFF;
  border-color: #FFFFFF;
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-9 {
  position: absolute;
  width: -webkit-max-content;
  width: -moz-max-content;
  width: max-content;
  bottom: 8px;
  left: 50%;
  -webkit-transform: translate(-50%, 0);
  -moz-transform: translate(-50%, 0);
  -ms-transform: translate(-50%, 0);
  transform: translate(-50%, 0);
  z-index: 2;
}

.emotion-10 {
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  padding: 0;
}

.emotion-11 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 10px;
  line-height: 1.5;
  letter-spacing: 0.5px;
  font-weight: 500;
  text-transform: none;
  font-weight: normal;
  min-height: initial;
  color: #003764;
  font-size: 11px;
  line-height: 14px;
  letter-spacing: normal;
}

.emotion-12 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: none;
  transition: none;
  box-sizing: border-box;
  border: 1.5px solid transparent;
  border-radius: 0;
  font-size: 16px;
  font-weight: 500;
  letter-spacing: 0px;
  min-height: 44px;
  max-height: auto;
  line-height: 1.3125;
  padding: 0;
  width: auto;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: left;
  border-radius: 0;
  border: none;
  background-color: transparent;
  color: #000000;
  height: auto;
  -webkit-box-pack: left;
  -ms-flex-pack: left;
  -webkit-justify-content: left;
  justify-content: left;
  font-weight: normal;
  text-transform: none;
  z-index: 1;
  color: #003764;
  -webkit-text-decoration: underline;
  text-decoration: underline;
  font-size: 11px;
  line-height: 14px;
  min-height: initial;
  padding: 0;
  margin-left: 5px;
  pointer-events: auto;
  text-underline-offset: 2px;
}

.emotion-12:focus {
  outline: none;
}

.emotion-12>span {
  padding: 1px 0;
}

.emotion-12 span span {
  padding-left: 3px;
  min-width: auto;
  min-height: auto;
  position: relative;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-transition: 250ms ease-in-out;
  transition: 250ms ease-in-out;
  transition-property: height,width,margin;
  top: 0;
  width: calc(1.2rem * 0.72);
}

.emotion-12 span span svg {
  -webkit-transition: fill 250ms ease-in-out;
  transition: fill 250ms ease-in-out;
  fill: #000000;
}

.emotion-12 span span {
  padding-left: initial;
}

.emotion-12:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-12:focus-visible {
  outline: auto;
}

.emotion-13 {
  cursor: pointer;
  position: absolute;
  width: 100%;
  height: 100%;
  z-index: 0;
  top: 0;
  left: 0;
}

<div>
    <div
      style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <div
        class="emotion-0"
      >
        <div
          class="mui_ism__background-type-container emotion-1"
          height="0"
          width="0"
        >
          <div
            class="emotion-2"
          >
            <div
              class="emotion-3"
            >
              <div
                class="emotion-4"
              />
              <div
                class="emotion-5"
              >
                <div>
                  <p>
                    This is a sample text
                  </p>
                </div>
              </div>
            </div>
            <div
              class="emotion-6"
              data-testid="cta-container"
            >
              <a
                class="emotion-7"
                color="dark"
                href="/shop-now"
              >
                Shop Now
              </a>
              <a
                class="emotion-7"
                color="dark"
                href="/learn-more"
              >
                Learn More
              </a>
            </div>
            <div
              class="emotion-9"
            >
              <div
                class="emotion-10"
              >
                <span
                  class="emotion-11"
                >
                  prefix
                </span>
              </div>
              <button
                class="emotion-12"
              >
                View Details
              </button>
            </div>
            <a
              aria-label="Click Here"
              class="emotion-13"
              href="/click-here"
              title="Click Here"
            >
               
            </a>
          </div>
        </div>
      </div>
    </div>
  </div>,
  "debug": [Function],
  "findAllByAltText": [Function],
  "findAllByDisplayValue": [Function],
  "findAllByLabelText": [Function],
  "findAllByPlaceholderText": [Function],
  "findAllByRole": [Function],
  "findAllByTestId": [Function],
  "findAllByText": [Function],
  "findAllByTitle": [Function],
  "findByAltText": [Function],
  "findByDisplayValue": [Function],
  "findByLabelText": [Function],
  "findByPlaceholderText": [Function],
  "findByRole": [Function],
  "findByTestId": [Function],
  "findByText": [Function],
  "findByTitle": [Function],
  "getAllByAltText": [Function],
  "getAllByDisplayValue": [Function],
  "getAllByLabelText": [Function],
  "getAllByPlaceholderText": [Function],
  "getAllByRole": [Function],
  "getAllByTestId": [Function],
  "getAllByText": [Function],
  "getAllByTitle": [Function],
  "getByAltText": [Function],
  "getByDisplayValue": [Function],
  "getByLabelText": [Function],
  "getByPlaceholderText": [Function],
  "getByRole": [Function],
  "getByTestId": [Function],
  "getByText": [Function],
  "getByTitle": [Function],
  "queryAllByAltText": [Function],
  "queryAllByDisplayValue": [Function],
  "queryAllByLabelText": [Function],
  "queryAllByPlaceholderText": [Function],
  "queryAllByRole": [Function],
  "queryAllByTestId": [Function],
  "queryAllByText": [Function],
  "queryAllByTitle": [Function],
  "queryByAltText": [Function],
  "queryByDisplayValue": [Function],
  "queryByLabelText": [Function],
  "queryByPlaceholderText": [Function],
  "queryByRole": [Function],
  "queryByTestId": [Function],
  "queryByText": [Function],
  "queryByTitle": [Function],
  "rerender": [Function],
  "unmount": [Function],
}
`;

exports[`ISMBannerFullImage - AT should render with details link 1`] = `
.emotion-0 {
  position: absolute;
  width: -webkit-max-content;
  width: -moz-max-content;
  width: max-content;
  bottom: 8px;
  left: 50%;
  -webkit-transform: translate(-50%, 0);
  -moz-transform: translate(-50%, 0);
  -ms-transform: translate(-50%, 0);
  transform: translate(-50%, 0);
  z-index: 2;
}

.emotion-1 {
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  padding: 0;
}

.emotion-2 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 10px;
  line-height: 1.5;
  letter-spacing: 0.5px;
  font-weight: 500;
  text-transform: none;
  font-weight: normal;
  min-height: initial;
  color: #FFFFFF;
  font-size: 12px;
  line-height: 16px;
  letter-spacing: normal;
}

.emotion-3 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: none;
  transition: none;
  box-sizing: border-box;
  border: 1.5px solid transparent;
  border-radius: 0;
  font-size: 16px;
  font-weight: 500;
  letter-spacing: 0px;
  min-height: auto;
  max-height: auto;
  line-height: 1.3125;
  padding: 0;
  width: auto;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: left;
  border-radius: 0;
  border: none;
  background-color: transparent;
  color: #000000;
  height: auto;
  -webkit-box-pack: left;
  -ms-flex-pack: left;
  -webkit-justify-content: left;
  justify-content: left;
  font-weight: normal;
  text-transform: none;
  z-index: 1;
  -webkit-text-decoration: underline;
  text-decoration: underline;
  font-size: 12px;
  line-height: 16px;
  min-height: initial;
  padding: 0;
  margin-left: 5px;
  color: #FFFFFF;
  pointer-events: auto;
  text-underline-offset: 2px;
}

.emotion-3:focus {
  outline: none;
}

.emotion-3>span {
  padding: 1px 0;
}

.emotion-3 span span {
  padding-left: 3px;
  min-width: auto;
  min-height: auto;
  position: relative;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-transition: 250ms ease-in-out;
  transition: 250ms ease-in-out;
  transition-property: height,width,margin;
  top: 0;
  width: calc(1.2rem * 0.72);
}

.emotion-3 span span svg {
  -webkit-transition: fill 250ms ease-in-out;
  transition: fill 250ms ease-in-out;
  fill: #000000;
}

.emotion-3 span span {
  padding-left: initial;
}

.emotion-3:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-3:focus-visible {
  outline: auto;
}

<div
  class="emotion-0"
>
  <div
    class="emotion-1"
  >
    <span
      class="emotion-2"
    >
      prefix
    </span>
  </div>
  <button
    class="emotion-3"
  >
    View Details
  </button>
</div>
`;
