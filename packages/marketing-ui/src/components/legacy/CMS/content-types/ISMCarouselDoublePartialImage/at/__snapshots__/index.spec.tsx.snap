// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`ISM Carousel Double Partial Renders Carousel with proper aspect ratio Render Desktop Carousel 1`] = `
<DocumentFragment>
  .emotion-0 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  position: relative;
  height: 100%;
  width: 100%;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-column-gap: 12px;
  column-gap: 12px;
  box-sizing: border-box;
  -webkit-box-pack: space-evenly;
  -ms-flex-pack: space-evenly;
  -webkit-justify-content: space-evenly;
  justify-content: space-evenly;
  -webkit-align-content: center;
  -ms-flex-line-pack: center;
  align-content: center;
}

.emotion-1 {
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-1 .slick-list {
  overflow: hidden;
}

.emotion-1 .slick-track {
  width: -webkit-max-content!important;
  width: -moz-max-content!important;
  width: max-content!important;
}

.emotion-1 button.slick-next.slick-arrow.slick-next,
.emotion-1 button.slick-prev.slick-arrow.slick-prev {
  z-index: 2;
  top: undefined;
  background: #00000080;
  opacity: 60%;
  height: 44px;
  width: 44px;
}

.emotion-1 button.slick-next.slick-arrow.slick-next span svg path,
.emotion-1 button.slick-prev.slick-arrow.slick-prev span svg path {
  fill: #FFFFFF80;
}

.emotion-1 button.slick-next.slick-arrow.slick-next:hover,
.emotion-1 button.slick-prev.slick-arrow.slick-prev:hover {
  background: #00000080;
}

.emotion-1 button.slick-next.slick-arrow.slick-next:focus,
.emotion-1 button.slick-prev.slick-arrow.slick-prev:focus {
  background: #00000080;
}

.emotion-1 button.slick-next.slick-arrow.slick-next>span,
.emotion-1 button.slick-prev.slick-arrow.slick-prev>span {
  margin: auto;
}

.emotion-1 button.slick-next.slick-arrow.slick-next svg,
.emotion-1 button.slick-prev.slick-arrow.slick-prev svg {
  margin: auto;
  height: 25px;
  width: 14px;
}

.emotion-1 button.slick-next.slick-arrow.slick-next:focus-visible,
.emotion-1 button.slick-prev.slick-arrow.slick-prev:focus-visible {
  outline: auto;
}

.emotion-1 .slick-slide {
  margin: 0;
  width: auto;
}

.emotion-1 .slick-disabled {
  display: none!important;
}

.emotion-1 .slick-next {
  left: calc(100% - 44px);
}

.emotion-1 .slick-slide:first-of-type a>div:nth-of-type(2)>div>div:nth-of-type(2) {
  border-left: none;
}

.emotion-1 .slick-slide>div:first-of-type {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-1 .slick-slide>div:first-of-type>div:first-of-type {
  width: 100%;
}

.emotion-2 {
  position: relative;
}

.emotion-2 .slick-slider {
  position: relative;
  display: block;
  box-sizing: border-box;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  -webkit-touch-callout: none;
  touch-action: pan-y;
  -webkit-tap-highlight-color: transparent;
  scroll-behavior: smooth;
}

.emotion-2 .slick-slider .slick-track,
.emotion-2 .slick-slider .slick-list {
  -webkit-transform: translate3d(0, 0, 0);
  -moz-transform: translate3d(0, 0, 0);
  -ms-transform: translate3d(0, 0, 0);
  transform: translate3d(0, 0, 0);
}

.emotion-2 .slick-list {
  position: relative;
  display: block;
  overflow-x: hidden;
  overflow-y: hidden;
  margin: 0;
  padding: 0;
}

.emotion-2 .slick-list:focus {
  outline: none;
}

.emotion-2 .slick-list.dragging {
  cursor: pointer;
  cursor: hand;
}

.emotion-2 .slick-track {
  position: relative;
  top: 0;
  left: 0;
  display: block;
  margin-left: auto;
  margin-right: auto;
}

.emotion-2 .slick-track:before,
.emotion-2 .slick-track:after {
  display: table;
  content: "";
}

.emotion-2 .slick-track:after {
  clear: both;
}

.emotion-2 .slick-slide {
  display: none;
  float: left;
  height: 100%;
  min-height: 1px;
}

.emotion-2 .slick-slide img {
  display: block;
}

.emotion-2 .slick-slide.slick-loading img {
  display: none;
}

.emotion-2 .slick-slide.dragging img {
  pointer-events: none;
}

.emotion-2 [dir="rtl"] .slick-slide {
  float: right;
}

.emotion-2 .slick-initialized .slick-slide,
.emotion-2 .slick-vertical .slick-slide {
  display: block;
}

.emotion-2 .slick-vertical .slick-slide {
  border: 1px solid transparent;
  height: auto;
}

.emotion-2 .slick-loading .slick-track,
.emotion-2 .slick-loading .slick-slide {
  visibility: hidden;
}

.emotion-2 .slick-loading .slick-list {
  background: #fff url("./ajax-loader.gif") center center no-repeat;
}

.emotion-2 .slick-arrow.slick-hidden {
  display: none;
}

.emotion-2 .slick-prev,
.emotion-2 .slick-next {
  font-size: 0;
  line-height: 0;
  position: absolute;
  top: 50%;
  display: block;
  padding: 0;
  cursor: pointer;
  color: transparent;
  border: none;
  outline: none;
  background: transparent;
}

.emotion-2 .slick-prev:hover,
.emotion-2 .slick-next:hover,
.emotion-2 .slick-prev:focus,
.emotion-2 .slick-next:focus {
  color: transparent;
  outline: none;
  background: transparent;
  opacity: 1;
}

.emotion-2 .slick-prev.slick-disabled,
.emotion-2 .slick-next.slick-disabled {
  opacity: 0.25;
}

.emotion-2 .slick-prev {
  left: -0;
  -webkit-transform: translate(0, -50%) rotate(90deg);
  -moz-transform: translate(0, -50%) rotate(90deg);
  -ms-transform: translate(0, -50%) rotate(90deg);
  transform: translate(0, -50%) rotate(90deg);
}

.emotion-2 .slick-prev span {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-2 [dir="rtl"] .slick-prev {
  right: -0;
  left: auto;
}

.emotion-2 .slick-next {
  right: -0;
  -webkit-transform: translate(0, -50%) rotate(-90deg);
  -moz-transform: translate(0, -50%) rotate(-90deg);
  -ms-transform: translate(0, -50%) rotate(-90deg);
  transform: translate(0, -50%) rotate(-90deg);
}

.emotion-2 .slick-next span {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-2 [dir="rtl"] .slick-next {
  right: auto;
  left: -0;
}

.emotion-2 .slick-dotted.slick-slider {
  margin-bottom: 30px;
}

.emotion-2 .slick-dots {
  position: absolute;
  bottom: -25px;
  display: block;
  width: 100%;
  padding: 0;
  margin: 0;
  list-style: none;
  text-align: center;
}

.emotion-2 .slick-dots li {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: 20px;
  height: 20px;
  margin: 0 5px;
  padding: 0;
  cursor: pointer;
}

.emotion-2 .slick-dots li.slick-active button:before {
  opacity: 0.75;
  color: black;
}

.emotion-2 .slick-dots li button {
  font-size: 0;
  line-height: 0;
  display: block;
  padding: 5px;
  cursor: pointer;
  color: transparent;
  border: 0;
  outline: none;
  background: transparent;
}

.emotion-2 .slick-dots li button:hover,
.emotion-2 .slick-dots li button:focus {
  outline: none;
}

.emotion-2 .slick-dots li button:hover:before,
.emotion-2 .slick-dots li button:focus:before,
.emotion-2 .slick-dots li button:hover:before,
.emotion-2 .slick-dots li button:focus:before {
  opacity: 1;
}

.emotion-2 .slick-dots li button:before {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 5px;
  height: 5px;
  content: "";
  opacity: 0.25;
  background-color: black;
  border-radius: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-3 {
  display: inline-block;
  height: 2rem;
  width: 2rem;
  min-height: 2rem;
  min-width: 2rem;
}

.emotion-3 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-4 {
  background: url(https://pqm4o81ro4dn1332lt5a8eas8.staging.bigcontent.io/i/gap/CBVH_1?fmt=auto) left no-repeat;
  -webkit-background-size: cover;
  background-size: cover;
  background-repeat: no-repeat;
  aspect-ratio: 492/318;
  width: 100%;
  height: 100%;
}

.emotion-5 {
  position: absolute;
  z-index: 1;
  width: 100%;
  height: 100%;
  pointer-events: auto;
  aspect-ratio:492: 318;
}

.emotion-6 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: start;
  -ms-flex-pack: start;
  -webkit-justify-content: flex-start;
  justify-content: flex-start;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  width: 100%;
  height: 100%;
}

.emotion-7 {
  box-sizing: border-box;
  padding: 20px;
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
}

.emotion-8 {
  width: 100%;
}

.emotion-8 .amp-cms--p {
  padding: 0;
  margin: 0;
  line-height: 0;
}

.emotion-8 .amp-cms--p>span {
  display: inline;
  white-space: break-spaces;
}

.emotion-8 .amp-cms--p a {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  position: relative;
  z-index: 2;
  pointer-events: auto;
}

.emotion-8 .amp-cms--p a:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-8 .amp-cms--legal-copy {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 10px;
  line-height: 1.5;
  letter-spacing: 0.5px;
  font-weight: 500;
}

.emotion-8 sup {
  vertical-align: top;
  display: inline-block;
  margin-top: -0.25ex;
}

.emotion-8 sub {
  vertical-align: bottom;
  display: inline-block;
  margin-bottom: -0.25ex;
}

.emotion-8 .amp-cms--f-0 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-8 .amp-cms--f-1 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-8 .amp-cms--f-2 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-8 .amp-cms--fn-1 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-8 .amp-cms--body-1 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 18px;
  line-height: 1.5555555555555556;
  letter-spacing: 0.9px;
  font-weight: 500;
}

.emotion-8 .amp-cms--body-2 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 16px;
  line-height: 1.25;
  letter-spacing: 0px;
  font-weight: 500;
}

.emotion-8 .amp-cms--body-3 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 14px;
  line-height: 1.5714285714285714;
  letter-spacing: 0.7px;
  font-weight: 500;
}

.emotion-8 .amp-cms--body-4 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.5;
  letter-spacing: 0px;
  font-weight: 500;
}

.emotion-8 .amp-cms--body-5 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 10px;
  line-height: 1.5;
  letter-spacing: 0.5px;
  font-weight: 500;
}

.emotion-8 .amp-cms--eyebrow-1 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 17px;
  line-height: 1.588235294117647;
  letter-spacing: 1.7px;
  font-weight: 700;
  text-transform: uppercase;
}

.emotion-8 .amp-cms--eyebrow-2 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 16px;
  line-height: 1.75;
  letter-spacing: 1.6px;
  font-weight: 700;
  text-transform: uppercase;
}

.emotion-8 .amp-cms--eyebrow-3 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.8333333333333333;
  letter-spacing: 0.82px;
  font-weight: 700;
  text-transform: uppercase;
}

.emotion-8 .amp-cms--headline-1 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 60px;
  line-height: 1;
  letter-spacing: 2.4px;
  text-transform: uppercase;
  font-weight: 500;
}

.emotion-8 .amp-cms--headline-2 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 56px;
  line-height: 1;
  letter-spacing: 2.8px;
  text-transform: uppercase;
  font-weight: 500;
}

.emotion-8 .amp-cms--headline-3 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 50px;
  line-height: 1;
  letter-spacing: 2.5px;
  text-transform: uppercase;
  font-weight: 500;
}

.emotion-8 .amp-cms--headline-4 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 40px;
  line-height: 1;
  letter-spacing: 2.8px;
  text-transform: uppercase;
  font-weight: 500;
}

.emotion-8 .amp-cms--headline-5 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 36px;
  line-height: 1;
  letter-spacing: 3.6px;
  text-transform: uppercase;
  font-weight: 600;
}

.emotion-8 .amp-cms--headline-6 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 28px;
  line-height: 1.1428571428571428;
  letter-spacing: 1.51px;
  text-transform: none;
  font-weight: 600;
}

.emotion-8 .amp-cms--headline-7 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 20px;
  line-height: 1.2;
  letter-spacing: 1.3px;
  text-transform: uppercase;
  font-weight: 700;
}

.emotion-8 .amp-cms--headlineAlt-1 {
  color: #000000;
  font-family: var(--font-soehne),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 124px;
  line-height: 1;
  letter-spacing: -0.4px;
  font-weight: 400;
}

.emotion-8 .amp-cms--headlineAlt-2 {
  color: #000000;
  font-family: var(--font-soehne),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 84px;
  line-height: 1;
  letter-spacing: -0.4px;
  font-weight: 400;
}

.emotion-8 .amp-cms--headlineAlt-3 {
  color: #000000;
  font-family: var(--font-soehne),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 64px;
  line-height: 1;
  letter-spacing: -0.4px;
  font-weight: 400;
}

.emotion-8 .amp-cms--headlineAlt-4 {
  color: #000000;
  font-family: var(--font-soehne),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 34px;
  line-height: 1;
  letter-spacing: -0.4px;
  font-weight: 400;
}

.emotion-8 .amp-cms--headlineAlt-5 {
  color: #000000;
  font-family: var(--font-soehne),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 16px;
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-8 .amp-cms--headlineAlt-6 {
  color: #000000;
  font-family: var(--font-soehne),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 16px;
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-8 .amp-cms--headlineAlt-7 {
  color: #000000;
  font-family: var(--font-soehne),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 16px;
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-8 .amp-cms--promo-1 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 100px;
  line-height: 1;
  letter-spacing: 0px;
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-8 .amp-cms--promo-2 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 80px;
  line-height: 1;
  letter-spacing: 0px;
  font-weight: 600;
}

.emotion-8 .amp-cms--promoAlt-1 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 16px;
  line-height: 0.0625;
  letter-spacing: 0;
}

.emotion-8 .amp-cms--subhead-1 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 24px;
  line-height: 1.5;
  letter-spacing: 1.2px;
  font-weight: 400;
  text-transform: uppercase;
}

.emotion-8 .amp-cms--subhead-2 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 18px;
  line-height: 1.5555555555555556;
  letter-spacing: 0.36px;
  font-weight: 500;
  text-transform: uppercase;
}

.emotion-8 .amp-cms--subhead-3 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 14px;
  line-height: 1.1428571428571428;
  letter-spacing: 0.7px;
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-9 {
  cursor: pointer;
  position: absolute;
  width: 100%;
  height: 100%;
  z-index: 0;
  top: 0;
  left: 0;
}

.emotion-10 {
  background: url(https://pqm4o81ro4dn1332lt5a8eas8.staging.bigcontent.io/i/gap/ONLY_ISM_SundayRib_US?fmt=auto) left no-repeat;
  -webkit-background-size: cover;
  background-size: cover;
  background-repeat: no-repeat;
  aspect-ratio: 492/318;
  width: 100%;
  height: 100%;
}

.emotion-12 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: end;
  -ms-flex-pack: end;
  -webkit-justify-content: flex-end;
  justify-content: flex-end;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  width: 100%;
  height: 100%;
}

.emotion-13 {
  box-sizing: border-box;
  padding: 20px;
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-flex-direction: column-reverse;
  -ms-flex-direction: column-reverse;
  flex-direction: column-reverse;
}

.emotion-14 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  width: 100%;
  -webkit-box-pack: right;
  -ms-flex-pack: right;
  -webkit-justify-content: right;
  justify-content: right;
}

.emotion-15 {
  max-height: 1X;
}

.emotion-22 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  width: 100%;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
}

.emotion-23 {
  max-height: 32px;
}

.emotion-31 {
  max-height: 64px;
}

.emotion-39 {
  max-height: 105px;
}

.emotion-43 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  padding-top: 16px;
  height: 112px;
  min-height: 112px;
}

.emotion-44 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: none;
  transition: none;
  box-sizing: border-box;
  border: 1.5px solid transparent;
  border-radius: 0;
  font-size: 15px;
  font-weight: 500;
  letter-spacing: 0px;
  min-height: auto;
  max-height: auto;
  line-height: 1.7333333333333334;
  padding: 0;
  width: auto;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: left;
  border-left: none;
  border-right: none;
  border-radius: 0;
  background-color: transparent;
  border-bottom: 0;
  -webkit-box-pack: left;
  -ms-flex-pack: left;
  -webkit-justify-content: left;
  justify-content: left;
  color: #000000;
  height: auto;
  -webkit-text-decoration: underline;
  text-decoration: underline;
  text-underline-offset: 5px;
  text-decoration-thickness: 1px;
  -webkit-align-self: flex-start;
  -ms-flex-item-align: flex-start;
  align-self: flex-start;
  white-space: normal;
  z-index: 2;
  margin-bottom: 13px;
  margin-top: 0;
}

.emotion-44:focus {
  outline: none;
}

.emotion-44>span {
  padding: 1px 0;
}

.emotion-44:hover,
.emotion-44:focus {
  text-shadow: 0 0 2px currentColor;
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-44:active {
  text-transform: uppercase;
  text-shadow: none;
  -webkit-text-decoration: underline;
  text-decoration: underline;
}

.emotion-45 {
  box-sizing: border-box;
}

.emotion-46 .amp-cms--p {
  padding: 0;
  margin: 0;
  line-height: 0;
}

.emotion-46 .amp-cms--p>span {
  display: inline;
  white-space: break-spaces;
}

.emotion-46 .amp-cms--p a {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  position: relative;
  z-index: 2;
  pointer-events: auto;
}

.emotion-46 .amp-cms--p a:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-46 .amp-cms--legal-copy {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 10px;
  line-height: 1.5;
  letter-spacing: 0.5px;
  font-weight: 500;
}

.emotion-46 sup {
  vertical-align: top;
  display: inline-block;
  margin-top: -0.25ex;
}

.emotion-46 sub {
  vertical-align: bottom;
  display: inline-block;
  margin-bottom: -0.25ex;
}

.emotion-46 .amp-cms--f-0 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-46 .amp-cms--f-1 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-46 .amp-cms--f-2 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-46 .amp-cms--fn-1 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-46 .amp-cms--body-1 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 18px;
  line-height: 1.5555555555555556;
  letter-spacing: 0.9px;
  font-weight: 500;
}

.emotion-46 .amp-cms--body-2 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 16px;
  line-height: 1.25;
  letter-spacing: 0px;
  font-weight: 500;
}

.emotion-46 .amp-cms--body-3 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 14px;
  line-height: 1.5714285714285714;
  letter-spacing: 0.7px;
  font-weight: 500;
}

.emotion-46 .amp-cms--body-4 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.5;
  letter-spacing: 0px;
  font-weight: 500;
}

.emotion-46 .amp-cms--body-5 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 10px;
  line-height: 1.5;
  letter-spacing: 0.5px;
  font-weight: 500;
}

.emotion-46 .amp-cms--eyebrow-1 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 17px;
  line-height: 1.588235294117647;
  letter-spacing: 1.7px;
  font-weight: 700;
  text-transform: uppercase;
}

.emotion-46 .amp-cms--eyebrow-2 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 16px;
  line-height: 1.75;
  letter-spacing: 1.6px;
  font-weight: 700;
  text-transform: uppercase;
}

.emotion-46 .amp-cms--eyebrow-3 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.8333333333333333;
  letter-spacing: 0.82px;
  font-weight: 700;
  text-transform: uppercase;
}

.emotion-46 .amp-cms--headline-1 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 60px;
  line-height: 1;
  letter-spacing: 2.4px;
  text-transform: uppercase;
  font-weight: 500;
}

.emotion-46 .amp-cms--headline-2 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 56px;
  line-height: 1;
  letter-spacing: 2.8px;
  text-transform: uppercase;
  font-weight: 500;
}

.emotion-46 .amp-cms--headline-3 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 50px;
  line-height: 1;
  letter-spacing: 2.5px;
  text-transform: uppercase;
  font-weight: 500;
}

.emotion-46 .amp-cms--headline-4 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 40px;
  line-height: 1;
  letter-spacing: 2.8px;
  text-transform: uppercase;
  font-weight: 500;
}

.emotion-46 .amp-cms--headline-5 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 36px;
  line-height: 1;
  letter-spacing: 3.6px;
  text-transform: uppercase;
  font-weight: 600;
}

.emotion-46 .amp-cms--headline-6 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 28px;
  line-height: 1.1428571428571428;
  letter-spacing: 1.51px;
  text-transform: none;
  font-weight: 600;
}

.emotion-46 .amp-cms--headline-7 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 20px;
  line-height: 1.2;
  letter-spacing: 1.3px;
  text-transform: uppercase;
  font-weight: 700;
}

.emotion-46 .amp-cms--headlineAlt-1 {
  color: #000000;
  font-family: var(--font-soehne),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 124px;
  line-height: 1;
  letter-spacing: -0.4px;
  font-weight: 400;
}

.emotion-46 .amp-cms--headlineAlt-2 {
  color: #000000;
  font-family: var(--font-soehne),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 84px;
  line-height: 1;
  letter-spacing: -0.4px;
  font-weight: 400;
}

.emotion-46 .amp-cms--headlineAlt-3 {
  color: #000000;
  font-family: var(--font-soehne),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 64px;
  line-height: 1;
  letter-spacing: -0.4px;
  font-weight: 400;
}

.emotion-46 .amp-cms--headlineAlt-4 {
  color: #000000;
  font-family: var(--font-soehne),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 34px;
  line-height: 1;
  letter-spacing: -0.4px;
  font-weight: 400;
}

.emotion-46 .amp-cms--headlineAlt-5 {
  color: #000000;
  font-family: var(--font-soehne),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 16px;
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-46 .amp-cms--headlineAlt-6 {
  color: #000000;
  font-family: var(--font-soehne),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 16px;
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-46 .amp-cms--headlineAlt-7 {
  color: #000000;
  font-family: var(--font-soehne),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 16px;
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-46 .amp-cms--promo-1 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 100px;
  line-height: 1;
  letter-spacing: 0px;
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-46 .amp-cms--promo-2 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 80px;
  line-height: 1;
  letter-spacing: 0px;
  font-weight: 600;
}

.emotion-46 .amp-cms--promoAlt-1 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 16px;
  line-height: 0.0625;
  letter-spacing: 0;
}

.emotion-46 .amp-cms--subhead-1 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 24px;
  line-height: 1.5;
  letter-spacing: 1.2px;
  font-weight: 400;
  text-transform: uppercase;
}

.emotion-46 .amp-cms--subhead-2 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 18px;
  line-height: 1.5555555555555556;
  letter-spacing: 0.36px;
  font-weight: 500;
  text-transform: uppercase;
}

.emotion-46 .amp-cms--subhead-3 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 14px;
  line-height: 1.1428571428571428;
  letter-spacing: 0.7px;
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-47 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: none;
  transition: none;
  box-sizing: border-box;
  border: 1.5px solid transparent;
  border-radius: 0;
  font-size: 15px;
  font-weight: 500;
  letter-spacing: 0px;
  min-height: auto;
  max-height: auto;
  line-height: 1.7333333333333334;
  padding: 0;
  width: auto;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: left;
  border-left: none;
  border-right: none;
  border-radius: 0;
  background-color: transparent;
  border-bottom: 0;
  -webkit-box-pack: left;
  -ms-flex-pack: left;
  -webkit-justify-content: left;
  justify-content: left;
  color: #000000;
  height: auto;
  -webkit-text-decoration: underline;
  text-decoration: underline;
  text-underline-offset: 5px;
  text-decoration-thickness: 1px;
  -webkit-align-self: flex-start;
  -ms-flex-item-align: flex-start;
  align-self: flex-start;
  white-space: normal;
  z-index: 2;
  margin-top: 10px;
}

.emotion-47:focus {
  outline: none;
}

.emotion-47>span {
  padding: 1px 0;
}

.emotion-47:hover,
.emotion-47:focus {
  text-shadow: 0 0 2px currentColor;
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-47:active {
  text-transform: uppercase;
  text-shadow: none;
  -webkit-text-decoration: underline;
  text-decoration: underline;
}

.emotion-49 {
  position: absolute;
  width: -webkit-max-content;
  width: -moz-max-content;
  width: max-content;
  bottom: 8px;
  left: 50%;
  -webkit-transform: translate(-50%, 0);
  -moz-transform: translate(-50%, 0);
  -ms-transform: translate(-50%, 0);
  transform: translate(-50%, 0);
  z-index: 2;
}

.emotion-50 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: none;
  transition: none;
  box-sizing: border-box;
  border: 1.5px solid transparent;
  border-radius: 0;
  font-size: 16px;
  font-weight: 500;
  letter-spacing: 0px;
  min-height: auto;
  max-height: auto;
  line-height: 1.3125;
  padding: 0;
  width: auto;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: left;
  border-radius: 0;
  border: none;
  background-color: transparent;
  color: #000000;
  height: auto;
  -webkit-box-pack: left;
  -ms-flex-pack: left;
  -webkit-justify-content: left;
  justify-content: left;
  font-weight: normal;
  text-transform: none;
  z-index: 1;
  -webkit-text-decoration: underline;
  text-decoration: underline;
  font-size: 12px;
  line-height: 16px;
  min-height: initial;
  padding: 0;
  margin-left: unset;
  color: #FFFFFF;
  pointer-events: auto;
  text-underline-offset: 2px;
}

.emotion-50:focus {
  outline: none;
}

.emotion-50>span {
  padding: 1px 0;
}

.emotion-50 span span {
  padding-left: 3px;
  min-width: auto;
  min-height: auto;
  position: relative;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-transition: 250ms ease-in-out;
  transition: 250ms ease-in-out;
  transition-property: height,width,margin;
  top: 0;
  width: calc(1.2rem * 0.72);
}

.emotion-50 span span svg {
  -webkit-transition: fill 250ms ease-in-out;
  transition: fill 250ms ease-in-out;
  fill: #000000;
}

.emotion-50 span span {
  padding-left: initial;
}

.emotion-50:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-50:focus-visible {
  outline: auto;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <div
        class="emotion-0"
      >
        <nav
          class="emotion-1"
        >
          <div
            class="emotion-2"
          >
            <div
              class="slick-slider slick-initialized"
              dir="ltr"
            >
              <button
                aria-label="Previous"
                class="slick-prev slick-arrow slick-prev slick-disabled"
                data-role="none"
                disabled=""
              >
                <span
                  aria-hidden="true"
                  class="emotion-3"
                >
                  <svg
                    viewBox="0 0 26.78 17.63"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M0 1.95l13 15.68L26.79 1.98 24.53 0 13.08 13.01 2.3.03z"
                      fill="#000000"
                    />
                  </svg>
                </span>
              </button>
              <div
                class="slick-list"
              >
                <div
                  class="slick-track"
                  style="opacity: 1; transform: translate3d(0px, 0px, 0px);"
                >
                  <div
                    aria-hidden="false"
                    class="slick-slide slick-active slick-current"
                    data-index="0"
                    style="outline: none; width: 0px;"
                    tabindex="-1"
                  >
                    <div>
                      <div
                        class="mui_ism__background-type-container emotion-4"
                        height="0"
                        width="0"
                      >
                        <div
                          class="emotion-5"
                          data-testid="ism-children-wrapper"
                        >
                          <div
                            class="emotion-6"
                          >
                            <div
                              class="emotion-7"
                            >
                              <div
                                class="emotion-8"
                              >
                                <div>
                                  <p
                                    class"amp-cms--p"=""
                                    style="text-align:center;"
                                  >
                                    <span
                                      class="amp-cms--subhead-1"
                                    >
                                      Frame Rich Text 1
                                    </span>
                                  </p>
                                </div>
                              </div>
                            </div>
                          </div>
                          <a
                            aria-label="bannerLink Test"
                            class="emotion-9"
                            href="bannerLink Value"
                            title="bannerLink Test"
                          >
                             
                          </a>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div
                    aria-hidden="true"
                    class="slick-slide"
                    data-index="1"
                    style="outline: none; width: 0px;"
                    tabindex="-1"
                  >
                    <div>
                      <div
                        class="mui_ism__background-type-container emotion-10"
                        height="0"
                        width="0"
                      >
                        <div
                          class="emotion-5"
                          data-testid="ism-children-wrapper"
                        >
                          <div
                            class="emotion-12"
                          >
                            <div
                              class="emotion-13"
                            >
                              <div
                                class="emotion-14"
                              >
                                <img
                                  alt="girl running outfit"
                                  class="emotion-15"
                                  src="https://1jzn2l1y362bq1fktn2mfkqw10.staging.bigcontent.io/i/gapfactory/Freesample?fmt=auto&h=2"
                                />
                              </div>
                              <div
                                class="emotion-8"
                              >
                                <div>
                                  <p
                                    class"amp-cms--p"=""
                                    style="text-align:center;"
                                  >
                                    <span
                                      class="amp-cms--subhead-1"
                                    >
                                      Frame Rich Text 2
                                    </span>
                                  </p>
                                </div>
                              </div>
                            </div>
                          </div>
                          <a
                            aria-label="bannerLink Test"
                            class="emotion-9"
                            href="bannerLink Value"
                            title="bannerLink Test"
                          >
                             
                          </a>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div
                    aria-hidden="true"
                    class="slick-slide"
                    data-index="2"
                    style="outline: none; width: 0px;"
                    tabindex="-1"
                  >
                    <div>
                      <div
                        class="mui_ism__background-type-container emotion-4"
                        height="0"
                        width="0"
                      >
                        <div
                          class="emotion-5"
                          data-testid="ism-children-wrapper"
                        >
                          <div
                            class="emotion-6"
                          >
                            <div
                              class="emotion-7"
                            >
                              <div
                                class="emotion-22"
                              >
                                <img
                                  alt="girl running outfit"
                                  class="emotion-23"
                                  src="https://1jzn2l1y362bq1fktn2mfkqw10.staging.bigcontent.io/i/gapfactory/Freesample?fmt=auto&h=64"
                                />
                              </div>
                              <div
                                class="emotion-8"
                              >
                                <div>
                                  <p
                                    class"amp-cms--p"=""
                                    style="text-align:center;"
                                  >
                                    <span
                                      class="amp-cms--subhead-1"
                                    >
                                      Frame Rich Text 1
                                    </span>
                                  </p>
                                </div>
                              </div>
                            </div>
                          </div>
                          <a
                            aria-label="bannerLink Test"
                            class="emotion-9"
                            href="bannerLink Value"
                            title="bannerLink Test"
                          >
                             
                          </a>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div
                    aria-hidden="true"
                    class="slick-slide"
                    data-index="3"
                    style="outline: none; width: 0px;"
                    tabindex="-1"
                  >
                    <div>
                      <div
                        class="mui_ism__background-type-container emotion-4"
                        height="0"
                        width="0"
                      >
                        <div
                          class="emotion-5"
                          data-testid="ism-children-wrapper"
                        >
                          <div
                            class="emotion-6"
                          >
                            <div
                              class="emotion-7"
                            >
                              <div
                                class="emotion-22"
                              >
                                <img
                                  alt="girl running outfit"
                                  class="emotion-31"
                                  src="https://1jzn2l1y362bq1fktn2mfkqw10.staging.bigcontent.io/i/gapfactory/Freesample?fmt=auto&h=128"
                                />
                              </div>
                              <div
                                class="emotion-8"
                              >
                                <div>
                                  <p
                                    class"amp-cms--p"=""
                                    style="text-align:center;"
                                  >
                                    <span
                                      class="amp-cms--subhead-1"
                                    >
                                      Frame Rich Text 1
                                    </span>
                                  </p>
                                </div>
                              </div>
                            </div>
                          </div>
                          <a
                            aria-label="bannerLink Test"
                            class="emotion-9"
                            href="bannerLink Value"
                            title="bannerLink Test"
                          >
                             
                          </a>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div
                    aria-hidden="true"
                    class="slick-slide"
                    data-index="4"
                    style="outline: none; width: 0px;"
                    tabindex="-1"
                  >
                    <div>
                      <div
                        class="mui_ism__background-type-container emotion-4"
                        height="0"
                        width="0"
                      >
                        <div
                          class="emotion-5"
                          data-testid="ism-children-wrapper"
                        >
                          <div
                            class="emotion-6"
                          >
                            <div
                              class="emotion-7"
                            >
                              <div
                                class="emotion-22"
                              >
                                <img
                                  alt="girl running outfit"
                                  class="emotion-39"
                                  src="https://1jzn2l1y362bq1fktn2mfkqw10.staging.bigcontent.io/i/gapfactory/Freesample?fmt=auto&h=210"
                                />
                              </div>
                              <div
                                class="emotion-8"
                              >
                                <div>
                                  <p
                                    class"amp-cms--p"=""
                                    style="text-align:center;"
                                  >
                                    <span
                                      class="amp-cms--subhead-1"
                                    >
                                      Frame Rich Text 1
                                    </span>
                                  </p>
                                </div>
                              </div>
                            </div>
                          </div>
                          <a
                            aria-label="bannerLink Test"
                            class="emotion-9"
                            href="bannerLink Value"
                            title="bannerLink Test"
                          >
                             
                          </a>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <button
                aria-label="Next"
                class="slick-next slick-arrow slick-next"
                data-role="none"
              >
                <span
                  aria-hidden="true"
                  class="emotion-3"
                >
                  <svg
                    viewBox="0 0 26.78 17.63"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M0 1.95l13 15.68L26.79 1.98 24.53 0 13.08 13.01 2.3.03z"
                      fill="#000000"
                    />
                  </svg>
                </span>
              </button>
            </div>
          </div>
        </nav>
        <div
          class="emotion-43"
        >
          <a
            class="emotion-44"
            color="dark"
            href="/testing cta2"
          >
            <span
              class="emotion-45"
            >
              CTA 1
            </span>
          </a>
          <div
            class="emotion-46"
          >
            <div>
              <p
                class"amp-cms--p"=""
                style="text-align:center;"
              >
                <span
                  class="amp-cms--subhead-1"
                >
                  This is RTE Body Copy
                </span>
              </p>
            </div>
          </div>
          <a
            class="emotion-47"
            color="primary"
            href="/testing cta2"
          >
            <span
              class="emotion-45"
            >
              CTA 2
            </span>
          </a>
          <div
            class="emotion-49"
          >
            <button
              class="emotion-50"
            >
              Details links test
            </button>
          </div>
        </div>
      </div>
      ,
    </div>
  </div>
</DocumentFragment>
`;

exports[`ISM Carousel Double Partial Renders Carousel with proper aspect ratio Render Mobile Carousel 1`] = `
<DocumentFragment>
  .emotion-0 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  position: relative;
  height: 100%;
  width: 100%;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-column-gap: 12px;
  column-gap: 12px;
  box-sizing: border-box;
  -webkit-box-pack: space-evenly;
  -ms-flex-pack: space-evenly;
  -webkit-justify-content: space-evenly;
  justify-content: space-evenly;
  -webkit-align-content: center;
  -ms-flex-line-pack: center;
  align-content: center;
}

.emotion-1 {
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-1 .slick-list {
  overflow: hidden;
}

.emotion-1 .slick-track {
  width: -webkit-max-content!important;
  width: -moz-max-content!important;
  width: max-content!important;
}

.emotion-1 button.slick-next.slick-arrow.slick-next,
.emotion-1 button.slick-prev.slick-arrow.slick-prev {
  z-index: 2;
  top: undefined;
  background: #00000080;
  opacity: 60%;
  height: 44px;
  width: 44px;
}

.emotion-1 button.slick-next.slick-arrow.slick-next span svg path,
.emotion-1 button.slick-prev.slick-arrow.slick-prev span svg path {
  fill: #FFFFFF80;
}

.emotion-1 button.slick-next.slick-arrow.slick-next:hover,
.emotion-1 button.slick-prev.slick-arrow.slick-prev:hover {
  background: #00000080;
}

.emotion-1 button.slick-next.slick-arrow.slick-next:focus,
.emotion-1 button.slick-prev.slick-arrow.slick-prev:focus {
  background: #00000080;
}

.emotion-1 button.slick-next.slick-arrow.slick-next>span,
.emotion-1 button.slick-prev.slick-arrow.slick-prev>span {
  margin: auto;
}

.emotion-1 button.slick-next.slick-arrow.slick-next svg,
.emotion-1 button.slick-prev.slick-arrow.slick-prev svg {
  margin: auto;
  height: 25px;
  width: 14px;
}

.emotion-1 button.slick-next.slick-arrow.slick-next:focus-visible,
.emotion-1 button.slick-prev.slick-arrow.slick-prev:focus-visible {
  outline: auto;
}

.emotion-1 .slick-slide {
  margin: 0;
  width: auto;
}

.emotion-1 .slick-disabled {
  display: none!important;
}

.emotion-1 .slick-next {
  left: calc(100% - 44px);
}

.emotion-1 .slick-slide:first-of-type a>div:nth-of-type(2)>div>div:nth-of-type(2) {
  border-left: none;
}

.emotion-1 .slick-slide>div:first-of-type {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-1 .slick-slide>div:first-of-type>div:first-of-type {
  width: 100%;
}

.emotion-2 {
  position: relative;
}

.emotion-2 .slick-slider {
  position: relative;
  display: block;
  box-sizing: border-box;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  -webkit-touch-callout: none;
  touch-action: pan-y;
  -webkit-tap-highlight-color: transparent;
  scroll-behavior: smooth;
}

.emotion-2 .slick-slider .slick-track,
.emotion-2 .slick-slider .slick-list {
  -webkit-transform: translate3d(0, 0, 0);
  -moz-transform: translate3d(0, 0, 0);
  -ms-transform: translate3d(0, 0, 0);
  transform: translate3d(0, 0, 0);
}

.emotion-2 .slick-list {
  position: relative;
  display: block;
  overflow-x: hidden;
  overflow-y: hidden;
  margin: 0;
  padding: 0;
}

.emotion-2 .slick-list:focus {
  outline: none;
}

.emotion-2 .slick-list.dragging {
  cursor: pointer;
  cursor: hand;
}

.emotion-2 .slick-track {
  position: relative;
  top: 0;
  left: 0;
  display: block;
  margin-left: auto;
  margin-right: auto;
}

.emotion-2 .slick-track:before,
.emotion-2 .slick-track:after {
  display: table;
  content: "";
}

.emotion-2 .slick-track:after {
  clear: both;
}

.emotion-2 .slick-slide {
  display: none;
  float: left;
  height: 100%;
  min-height: 1px;
}

.emotion-2 .slick-slide img {
  display: block;
}

.emotion-2 .slick-slide.slick-loading img {
  display: none;
}

.emotion-2 .slick-slide.dragging img {
  pointer-events: none;
}

.emotion-2 [dir="rtl"] .slick-slide {
  float: right;
}

.emotion-2 .slick-initialized .slick-slide,
.emotion-2 .slick-vertical .slick-slide {
  display: block;
}

.emotion-2 .slick-vertical .slick-slide {
  border: 1px solid transparent;
  height: auto;
}

.emotion-2 .slick-loading .slick-track,
.emotion-2 .slick-loading .slick-slide {
  visibility: hidden;
}

.emotion-2 .slick-loading .slick-list {
  background: #fff url("./ajax-loader.gif") center center no-repeat;
}

.emotion-2 .slick-arrow.slick-hidden {
  display: none;
}

.emotion-2 .slick-prev,
.emotion-2 .slick-next {
  font-size: 0;
  line-height: 0;
  position: absolute;
  top: 50%;
  display: block;
  padding: 0;
  cursor: pointer;
  color: transparent;
  border: none;
  outline: none;
  background: transparent;
}

.emotion-2 .slick-prev:hover,
.emotion-2 .slick-next:hover,
.emotion-2 .slick-prev:focus,
.emotion-2 .slick-next:focus {
  color: transparent;
  outline: none;
  background: transparent;
  opacity: 1;
}

.emotion-2 .slick-prev.slick-disabled,
.emotion-2 .slick-next.slick-disabled {
  opacity: 0.25;
}

.emotion-2 .slick-prev {
  left: -0;
  -webkit-transform: translate(0, -50%) rotate(90deg);
  -moz-transform: translate(0, -50%) rotate(90deg);
  -ms-transform: translate(0, -50%) rotate(90deg);
  transform: translate(0, -50%) rotate(90deg);
}

.emotion-2 .slick-prev span {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-2 [dir="rtl"] .slick-prev {
  right: -0;
  left: auto;
}

.emotion-2 .slick-next {
  right: -0;
  -webkit-transform: translate(0, -50%) rotate(-90deg);
  -moz-transform: translate(0, -50%) rotate(-90deg);
  -ms-transform: translate(0, -50%) rotate(-90deg);
  transform: translate(0, -50%) rotate(-90deg);
}

.emotion-2 .slick-next span {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-2 [dir="rtl"] .slick-next {
  right: auto;
  left: -0;
}

.emotion-2 .slick-dotted.slick-slider {
  margin-bottom: 30px;
}

.emotion-2 .slick-dots {
  position: absolute;
  bottom: -25px;
  display: block;
  width: 100%;
  padding: 0;
  margin: 0;
  list-style: none;
  text-align: center;
}

.emotion-2 .slick-dots li {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: 20px;
  height: 20px;
  margin: 0 5px;
  padding: 0;
  cursor: pointer;
}

.emotion-2 .slick-dots li.slick-active button:before {
  opacity: 0.75;
  color: black;
}

.emotion-2 .slick-dots li button {
  font-size: 0;
  line-height: 0;
  display: block;
  padding: 5px;
  cursor: pointer;
  color: transparent;
  border: 0;
  outline: none;
  background: transparent;
}

.emotion-2 .slick-dots li button:hover,
.emotion-2 .slick-dots li button:focus {
  outline: none;
}

.emotion-2 .slick-dots li button:hover:before,
.emotion-2 .slick-dots li button:focus:before,
.emotion-2 .slick-dots li button:hover:before,
.emotion-2 .slick-dots li button:focus:before {
  opacity: 1;
}

.emotion-2 .slick-dots li button:before {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 5px;
  height: 5px;
  content: "";
  opacity: 0.25;
  background-color: black;
  border-radius: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-3 {
  display: inline-block;
  height: 2rem;
  width: 2rem;
  min-height: 2rem;
  min-width: 2rem;
}

.emotion-3 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-4 {
  background: url(https://pqm4o81ro4dn1332lt5a8eas8.staging.bigcontent.io/i/gap/CBVH_1?fmt=auto) left no-repeat;
  -webkit-background-size: cover;
  background-size: cover;
  background-repeat: no-repeat;
  aspect-ratio: 343/213;
  width: 100%;
  height: 100%;
}

.emotion-5 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: start;
  -ms-flex-pack: start;
  -webkit-justify-content: flex-start;
  justify-content: flex-start;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  width: 100%;
  height: 100%;
}

.emotion-6 {
  box-sizing: border-box;
  padding: 15px;
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
}

.emotion-7 {
  width: 100%;
}

.emotion-7 .amp-cms--p {
  padding: 0;
  margin: 0;
  line-height: 0;
}

.emotion-7 .amp-cms--p>span {
  display: inline;
  white-space: break-spaces;
}

.emotion-7 .amp-cms--p a {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  position: relative;
  z-index: 2;
  pointer-events: auto;
}

.emotion-7 .amp-cms--p a:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-7 .amp-cms--legal-copy {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 10px;
  line-height: 1.5;
  letter-spacing: 0.5px;
  font-weight: 500;
}

.emotion-7 sup {
  vertical-align: top;
  display: inline-block;
  margin-top: -0.25ex;
}

.emotion-7 sub {
  vertical-align: bottom;
  display: inline-block;
  margin-bottom: -0.25ex;
}

.emotion-7 .amp-cms--f-0 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 11px;
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-7 .amp-cms--f-1 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 11px;
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-7 .amp-cms--f-2 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 11px;
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-7 .amp-cms--fn-1 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 11px;
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-7 .amp-cms--body-1 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 16px;
  line-height: 1.625;
  letter-spacing: 0.8px;
  font-weight: 500;
}

.emotion-7 .amp-cms--body-2 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 14px;
  line-height: 1.4285714285714286;
  letter-spacing: 0px;
  font-weight: 500;
}

.emotion-7 .amp-cms--body-3 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.5;
  letter-spacing: 0.6px;
  font-weight: 500;
}

.emotion-7 .amp-cms--body-4 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 10px;
  line-height: 1.6;
  letter-spacing: 0px;
  font-weight: 500;
}

.emotion-7 .amp-cms--body-5 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 10px;
  line-height: 1.5;
  letter-spacing: 0.5px;
  font-weight: 500;
}

.emotion-7 .amp-cms--eyebrow-1 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 2;
  letter-spacing: 1.2px;
  font-weight: 700;
  text-transform: uppercase;
}

.emotion-7 .amp-cms--eyebrow-2 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 11px;
  line-height: 2;
  letter-spacing: 1.1px;
  font-weight: 700;
  text-transform: uppercase;
}

.emotion-7 .amp-cms--eyebrow-3 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 10px;
  line-height: 1.5;
  letter-spacing: 0.68px;
  font-weight: 700;
  text-transform: uppercase;
}

.emotion-7 .amp-cms--headline-1 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 36px;
  line-height: 1;
  letter-spacing: 1.37px;
  text-transform: uppercase;
  font-weight: 500;
}

.emotion-7 .amp-cms--headline-2 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 34px;
  line-height: 1;
  letter-spacing: 1.7px;
  text-transform: uppercase;
  font-weight: 500;
}

.emotion-7 .amp-cms--headline-3 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 30px;
  line-height: 1;
  letter-spacing: 0.3px;
  text-transform: uppercase;
  font-weight: 500;
}

.emotion-7 .amp-cms--headline-4 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 24px;
  line-height: 1;
  letter-spacing: 1.8px;
  text-transform: uppercase;
  font-weight: 500;
}

.emotion-7 .amp-cms--headline-5 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 22px;
  line-height: 1.1818181818181819;
  letter-spacing: 2.2px;
  text-transform: uppercase;
  font-weight: 600;
}

.emotion-7 .amp-cms--headline-6 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 16px;
  line-height: 1.375;
  letter-spacing: 0.8px;
  text-transform: none;
  font-weight: 600;
}

.emotion-7 .amp-cms--headline-7 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 14px;
  line-height: 1.4285714285714286;
  letter-spacing: 0.1px;
  text-transform: uppercase;
  font-weight: 700;
}

.emotion-7 .amp-cms--headlineAlt-1 {
  color: #000000;
  font-family: var(--font-soehne),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 64px;
  line-height: 1;
  letter-spacing: -0.3px;
  font-weight: 400;
}

.emotion-7 .amp-cms--headlineAlt-2 {
  color: #000000;
  font-family: var(--font-soehne),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 54px;
  line-height: 1;
  letter-spacing: -0.3px;
  font-weight: 400;
}

.emotion-7 .amp-cms--headlineAlt-3 {
  color: #000000;
  font-family: var(--font-soehne),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 34px;
  line-height: 1;
  letter-spacing: -0.3px;
  font-weight: 400;
}

.emotion-7 .amp-cms--headlineAlt-4 {
  color: #000000;
  font-family: var(--font-soehne),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 24px;
  line-height: 1.4166666666666667;
  letter-spacing: -0.3px;
  font-weight: 400;
}

.emotion-7 .amp-cms--headlineAlt-5 {
  color: #000000;
  font-family: var(--font-soehne),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 14px;
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-7 .amp-cms--headlineAlt-6 {
  color: #000000;
  font-family: var(--font-soehne),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 14px;
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-7 .amp-cms--headlineAlt-7 {
  color: #000000;
  font-family: var(--font-soehne),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 14px;
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-7 .amp-cms--promo-1 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 60px;
  line-height: 1;
  letter-spacing: 0px;
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-7 .amp-cms--promo-2 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 40px;
  line-height: 1;
  letter-spacing: 0px;
  font-weight: 600;
}

.emotion-7 .amp-cms--promoAlt-1 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 14px;
  line-height: 0.07142857142857142;
  letter-spacing: 0;
}

.emotion-7 .amp-cms--subhead-1 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 16px;
  line-height: 1.75;
  letter-spacing: 0.32px;
  font-weight: 400;
  text-transform: uppercase;
}

.emotion-7 .amp-cms--subhead-2 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 14px;
  line-height: 1.5714285714285714;
  letter-spacing: 0.7px;
  font-weight: 500;
  text-transform: uppercase;
}

.emotion-7 .amp-cms--subhead-3 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 13px;
  line-height: 1;
  letter-spacing: 0.97px;
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-8 {
  cursor: pointer;
  position: absolute;
  width: 100%;
  height: 100%;
  z-index: 0;
  top: 0;
  left: 0;
}

.emotion-9 {
  background: url(https://pqm4o81ro4dn1332lt5a8eas8.staging.bigcontent.io/i/gap/ONLY_ISM_SundayRib_US?fmt=auto) left no-repeat;
  -webkit-background-size: cover;
  background-size: cover;
  background-repeat: no-repeat;
  aspect-ratio: 343/213;
  width: 100%;
  height: 100%;
}

.emotion-10 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: end;
  -ms-flex-pack: end;
  -webkit-justify-content: flex-end;
  justify-content: flex-end;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  width: 100%;
  height: 100%;
}

.emotion-11 {
  box-sizing: border-box;
  padding: 15px;
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-flex-direction: column-reverse;
  -ms-flex-direction: column-reverse;
  flex-direction: column-reverse;
}

.emotion-12 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  width: 100%;
  -webkit-box-pack: right;
  -ms-flex-pack: right;
  -webkit-justify-content: right;
  justify-content: right;
}

.emotion-13 {
  max-height: 1X;
}

.emotion-19 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  width: 100%;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
}

.emotion-20 {
  max-height: 24px;
}

.emotion-27 {
  max-height: 48px;
}

.emotion-34 {
  max-height: 80px;
}

.emotion-38 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  padding-top: 0;
  height: 127px;
  min-height: 127px;
}

.emotion-39 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: none;
  transition: none;
  box-sizing: border-box;
  border: 1.5px solid transparent;
  border-radius: 0;
  font-size: 15px;
  font-weight: 500;
  letter-spacing: 0px;
  min-height: 44px;
  max-height: auto;
  line-height: 1.7333333333333334;
  padding: 0;
  width: auto;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: left;
  border-left: none;
  border-right: none;
  border-radius: 0;
  background-color: transparent;
  border-bottom: 0;
  -webkit-box-pack: left;
  -ms-flex-pack: left;
  -webkit-justify-content: left;
  justify-content: left;
  color: #000000;
  height: auto;
  -webkit-text-decoration: underline;
  text-decoration: underline;
  text-underline-offset: 5px;
  text-decoration-thickness: 1px;
  -webkit-align-self: flex-start;
  -ms-flex-item-align: flex-start;
  align-self: flex-start;
  white-space: normal;
  z-index: 2;
  margin-bottom: 0;
  margin-top: 0;
}

.emotion-39:focus {
  outline: none;
}

.emotion-39>span {
  padding: 1px 0;
}

.emotion-39:hover,
.emotion-39:focus {
  text-shadow: 0 0 2px currentColor;
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-39:active {
  text-transform: uppercase;
  text-shadow: none;
  -webkit-text-decoration: underline;
  text-decoration: underline;
}

.emotion-40 {
  box-sizing: border-box;
}

.emotion-41 .amp-cms--p {
  padding: 0;
  margin: 0;
  line-height: 0;
}

.emotion-41 .amp-cms--p>span {
  display: inline;
  white-space: break-spaces;
}

.emotion-41 .amp-cms--p a {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  position: relative;
  z-index: 2;
  pointer-events: auto;
}

.emotion-41 .amp-cms--p a:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-41 .amp-cms--legal-copy {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 10px;
  line-height: 1.5;
  letter-spacing: 0.5px;
  font-weight: 500;
}

.emotion-41 sup {
  vertical-align: top;
  display: inline-block;
  margin-top: -0.25ex;
}

.emotion-41 sub {
  vertical-align: bottom;
  display: inline-block;
  margin-bottom: -0.25ex;
}

.emotion-41 .amp-cms--f-0 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 11px;
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-41 .amp-cms--f-1 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 11px;
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-41 .amp-cms--f-2 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 11px;
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-41 .amp-cms--fn-1 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 11px;
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-41 .amp-cms--body-1 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 16px;
  line-height: 1.625;
  letter-spacing: 0.8px;
  font-weight: 500;
}

.emotion-41 .amp-cms--body-2 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 14px;
  line-height: 1.4285714285714286;
  letter-spacing: 0px;
  font-weight: 500;
}

.emotion-41 .amp-cms--body-3 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.5;
  letter-spacing: 0.6px;
  font-weight: 500;
}

.emotion-41 .amp-cms--body-4 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 10px;
  line-height: 1.6;
  letter-spacing: 0px;
  font-weight: 500;
}

.emotion-41 .amp-cms--body-5 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 10px;
  line-height: 1.5;
  letter-spacing: 0.5px;
  font-weight: 500;
}

.emotion-41 .amp-cms--eyebrow-1 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 2;
  letter-spacing: 1.2px;
  font-weight: 700;
  text-transform: uppercase;
}

.emotion-41 .amp-cms--eyebrow-2 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 11px;
  line-height: 2;
  letter-spacing: 1.1px;
  font-weight: 700;
  text-transform: uppercase;
}

.emotion-41 .amp-cms--eyebrow-3 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 10px;
  line-height: 1.5;
  letter-spacing: 0.68px;
  font-weight: 700;
  text-transform: uppercase;
}

.emotion-41 .amp-cms--headline-1 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 36px;
  line-height: 1;
  letter-spacing: 1.37px;
  text-transform: uppercase;
  font-weight: 500;
}

.emotion-41 .amp-cms--headline-2 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 34px;
  line-height: 1;
  letter-spacing: 1.7px;
  text-transform: uppercase;
  font-weight: 500;
}

.emotion-41 .amp-cms--headline-3 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 30px;
  line-height: 1;
  letter-spacing: 0.3px;
  text-transform: uppercase;
  font-weight: 500;
}

.emotion-41 .amp-cms--headline-4 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 24px;
  line-height: 1;
  letter-spacing: 1.8px;
  text-transform: uppercase;
  font-weight: 500;
}

.emotion-41 .amp-cms--headline-5 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 22px;
  line-height: 1.1818181818181819;
  letter-spacing: 2.2px;
  text-transform: uppercase;
  font-weight: 600;
}

.emotion-41 .amp-cms--headline-6 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 16px;
  line-height: 1.375;
  letter-spacing: 0.8px;
  text-transform: none;
  font-weight: 600;
}

.emotion-41 .amp-cms--headline-7 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 14px;
  line-height: 1.4285714285714286;
  letter-spacing: 0.1px;
  text-transform: uppercase;
  font-weight: 700;
}

.emotion-41 .amp-cms--headlineAlt-1 {
  color: #000000;
  font-family: var(--font-soehne),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 64px;
  line-height: 1;
  letter-spacing: -0.3px;
  font-weight: 400;
}

.emotion-41 .amp-cms--headlineAlt-2 {
  color: #000000;
  font-family: var(--font-soehne),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 54px;
  line-height: 1;
  letter-spacing: -0.3px;
  font-weight: 400;
}

.emotion-41 .amp-cms--headlineAlt-3 {
  color: #000000;
  font-family: var(--font-soehne),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 34px;
  line-height: 1;
  letter-spacing: -0.3px;
  font-weight: 400;
}

.emotion-41 .amp-cms--headlineAlt-4 {
  color: #000000;
  font-family: var(--font-soehne),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 24px;
  line-height: 1.4166666666666667;
  letter-spacing: -0.3px;
  font-weight: 400;
}

.emotion-41 .amp-cms--headlineAlt-5 {
  color: #000000;
  font-family: var(--font-soehne),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 14px;
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-41 .amp-cms--headlineAlt-6 {
  color: #000000;
  font-family: var(--font-soehne),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 14px;
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-41 .amp-cms--headlineAlt-7 {
  color: #000000;
  font-family: var(--font-soehne),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 14px;
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-41 .amp-cms--promo-1 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 60px;
  line-height: 1;
  letter-spacing: 0px;
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-41 .amp-cms--promo-2 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 40px;
  line-height: 1;
  letter-spacing: 0px;
  font-weight: 600;
}

.emotion-41 .amp-cms--promoAlt-1 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 14px;
  line-height: 0.07142857142857142;
  letter-spacing: 0;
}

.emotion-41 .amp-cms--subhead-1 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 16px;
  line-height: 1.75;
  letter-spacing: 0.32px;
  font-weight: 400;
  text-transform: uppercase;
}

.emotion-41 .amp-cms--subhead-2 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 14px;
  line-height: 1.5714285714285714;
  letter-spacing: 0.7px;
  font-weight: 500;
  text-transform: uppercase;
}

.emotion-41 .amp-cms--subhead-3 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 13px;
  line-height: 1;
  letter-spacing: 0.97px;
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-42 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: none;
  transition: none;
  box-sizing: border-box;
  border: 1.5px solid transparent;
  border-radius: 0;
  font-size: 15px;
  font-weight: 500;
  letter-spacing: 0px;
  min-height: 44px;
  max-height: auto;
  line-height: 1.7333333333333334;
  padding: 0;
  width: auto;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: left;
  border-left: none;
  border-right: none;
  border-radius: 0;
  background-color: transparent;
  border-bottom: 0;
  -webkit-box-pack: left;
  -ms-flex-pack: left;
  -webkit-justify-content: left;
  justify-content: left;
  color: #000000;
  height: auto;
  -webkit-text-decoration: underline;
  text-decoration: underline;
  text-underline-offset: 5px;
  text-decoration-thickness: 1px;
  -webkit-align-self: flex-start;
  -ms-flex-item-align: flex-start;
  align-self: flex-start;
  white-space: normal;
  z-index: 2;
  margin-top: 0;
}

.emotion-42:focus {
  outline: none;
}

.emotion-42>span {
  padding: 1px 0;
}

.emotion-42:hover,
.emotion-42:focus {
  text-shadow: 0 0 2px currentColor;
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-42:active {
  text-transform: uppercase;
  text-shadow: none;
  -webkit-text-decoration: underline;
  text-decoration: underline;
}

.emotion-44 {
  position: absolute;
  width: -webkit-max-content;
  width: -moz-max-content;
  width: max-content;
  bottom: 8px;
  left: 50%;
  -webkit-transform: translate(-50%, 0);
  -moz-transform: translate(-50%, 0);
  -ms-transform: translate(-50%, 0);
  transform: translate(-50%, 0);
  z-index: 2;
}

.emotion-45 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: none;
  transition: none;
  box-sizing: border-box;
  border: 1.5px solid transparent;
  border-radius: 0;
  font-size: 16px;
  font-weight: 500;
  letter-spacing: 0px;
  min-height: 44px;
  max-height: auto;
  line-height: 1.3125;
  padding: 0;
  width: auto;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: left;
  border-radius: 0;
  border: none;
  background-color: transparent;
  color: #000000;
  height: auto;
  -webkit-box-pack: left;
  -ms-flex-pack: left;
  -webkit-justify-content: left;
  justify-content: left;
  font-weight: normal;
  text-transform: none;
  z-index: 1;
  -webkit-text-decoration: underline;
  text-decoration: underline;
  font-size: 11px;
  line-height: 14px;
  min-height: initial;
  padding: 0;
  margin-left: unset;
  color: #FFFFFF;
  pointer-events: auto;
  text-underline-offset: 2px;
}

.emotion-45:focus {
  outline: none;
}

.emotion-45>span {
  padding: 1px 0;
}

.emotion-45 span span {
  padding-left: 3px;
  min-width: auto;
  min-height: auto;
  position: relative;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-transition: 250ms ease-in-out;
  transition: 250ms ease-in-out;
  transition-property: height,width,margin;
  top: 0;
  width: calc(1.2rem * 0.72);
}

.emotion-45 span span svg {
  -webkit-transition: fill 250ms ease-in-out;
  transition: fill 250ms ease-in-out;
  fill: #000000;
}

.emotion-45 span span {
  padding-left: initial;
}

.emotion-45:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-45:focus-visible {
  outline: auto;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <div
        class="emotion-0"
      >
        <nav
          class="emotion-1"
        >
          <div
            class="emotion-2"
          >
            <div
              class="slick-slider slick-initialized"
              dir="ltr"
            >
              <button
                aria-label="Previous"
                class="slick-prev slick-arrow slick-prev slick-disabled"
                data-role="none"
                disabled=""
              >
                <span
                  aria-hidden="true"
                  class="emotion-3"
                >
                  <svg
                    viewBox="0 0 26.78 17.63"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M0 1.95l13 15.68L26.79 1.98 24.53 0 13.08 13.01 2.3.03z"
                      fill="#000000"
                    />
                  </svg>
                </span>
              </button>
              <div
                class="slick-list"
              >
                <div
                  class="slick-track"
                  style="opacity: 1; transform: translate3d(0px, 0px, 0px);"
                >
                  <div
                    aria-hidden="false"
                    class="slick-slide slick-active slick-current"
                    data-index="0"
                    style="outline: none; width: 0px;"
                    tabindex="-1"
                  >
                    <div>
                      <div
                        class="mui_ism__background-type-container emotion-4"
                        height="0"
                        width="0"
                      >
                        <div
                          class="emotion-5"
                        >
                          <div
                            class="emotion-6"
                          >
                            <div
                              class="emotion-7"
                            >
                              <div>
                                <p
                                  class"amp-cms--p"=""
                                  style="text-align:center;"
                                >
                                  <span
                                    class="amp-cms--subhead-1"
                                  >
                                    Frame Rich Text 1
                                  </span>
                                </p>
                              </div>
                            </div>
                          </div>
                        </div>
                        <a
                          aria-label="bannerLink Test"
                          class="emotion-8"
                          href="bannerLink Value"
                          title="bannerLink Test"
                        >
                           
                        </a>
                      </div>
                    </div>
                  </div>
                  <div
                    aria-hidden="true"
                    class="slick-slide"
                    data-index="1"
                    style="outline: none; width: 0px;"
                    tabindex="-1"
                  >
                    <div>
                      <div
                        class="mui_ism__background-type-container emotion-9"
                        height="0"
                        width="0"
                      >
                        <div
                          class="emotion-10"
                        >
                          <div
                            class="emotion-11"
                          >
                            <div
                              class="emotion-12"
                            >
                              <img
                                alt="girl running outfit"
                                class="emotion-13"
                                src="https://1jzn2l1y362bq1fktn2mfkqw10.staging.bigcontent.io/i/gapfactory/Freesample?fmt=auto&h=2"
                              />
                            </div>
                            <div
                              class="emotion-7"
                            >
                              <div>
                                <p
                                  class"amp-cms--p"=""
                                  style="text-align:center;"
                                >
                                  <span
                                    class="amp-cms--subhead-1"
                                  >
                                    Frame Rich Text 2
                                  </span>
                                </p>
                              </div>
                            </div>
                          </div>
                        </div>
                        <a
                          aria-label="bannerLink Test"
                          class="emotion-8"
                          href="bannerLink Value"
                          title="bannerLink Test"
                        >
                           
                        </a>
                      </div>
                    </div>
                  </div>
                  <div
                    aria-hidden="true"
                    class="slick-slide"
                    data-index="2"
                    style="outline: none; width: 0px;"
                    tabindex="-1"
                  >
                    <div>
                      <div
                        class="mui_ism__background-type-container emotion-4"
                        height="0"
                        width="0"
                      >
                        <div
                          class="emotion-5"
                        >
                          <div
                            class="emotion-6"
                          >
                            <div
                              class="emotion-19"
                            >
                              <img
                                alt="girl running outfit"
                                class="emotion-20"
                                src="https://1jzn2l1y362bq1fktn2mfkqw10.staging.bigcontent.io/i/gapfactory/Freesample?fmt=auto&h=48"
                              />
                            </div>
                            <div
                              class="emotion-7"
                            >
                              <div>
                                <p
                                  class"amp-cms--p"=""
                                  style="text-align:center;"
                                >
                                  <span
                                    class="amp-cms--subhead-1"
                                  >
                                    Frame Rich Text 1
                                  </span>
                                </p>
                              </div>
                            </div>
                          </div>
                        </div>
                        <a
                          aria-label="bannerLink Test"
                          class="emotion-8"
                          href="bannerLink Value"
                          title="bannerLink Test"
                        >
                           
                        </a>
                      </div>
                    </div>
                  </div>
                  <div
                    aria-hidden="true"
                    class="slick-slide"
                    data-index="3"
                    style="outline: none; width: 0px;"
                    tabindex="-1"
                  >
                    <div>
                      <div
                        class="mui_ism__background-type-container emotion-4"
                        height="0"
                        width="0"
                      >
                        <div
                          class="emotion-5"
                        >
                          <div
                            class="emotion-6"
                          >
                            <div
                              class="emotion-19"
                            >
                              <img
                                alt="girl running outfit"
                                class="emotion-27"
                                src="https://1jzn2l1y362bq1fktn2mfkqw10.staging.bigcontent.io/i/gapfactory/Freesample?fmt=auto&h=96"
                              />
                            </div>
                            <div
                              class="emotion-7"
                            >
                              <div>
                                <p
                                  class"amp-cms--p"=""
                                  style="text-align:center;"
                                >
                                  <span
                                    class="amp-cms--subhead-1"
                                  >
                                    Frame Rich Text 1
                                  </span>
                                </p>
                              </div>
                            </div>
                          </div>
                        </div>
                        <a
                          aria-label="bannerLink Test"
                          class="emotion-8"
                          href="bannerLink Value"
                          title="bannerLink Test"
                        >
                           
                        </a>
                      </div>
                    </div>
                  </div>
                  <div
                    aria-hidden="true"
                    class="slick-slide"
                    data-index="4"
                    style="outline: none; width: 0px;"
                    tabindex="-1"
                  >
                    <div>
                      <div
                        class="mui_ism__background-type-container emotion-4"
                        height="0"
                        width="0"
                      >
                        <div
                          class="emotion-5"
                        >
                          <div
                            class="emotion-6"
                          >
                            <div
                              class="emotion-19"
                            >
                              <img
                                alt="girl running outfit"
                                class="emotion-34"
                                src="https://1jzn2l1y362bq1fktn2mfkqw10.staging.bigcontent.io/i/gapfactory/Freesample?fmt=auto&h=160"
                              />
                            </div>
                            <div
                              class="emotion-7"
                            >
                              <div>
                                <p
                                  class"amp-cms--p"=""
                                  style="text-align:center;"
                                >
                                  <span
                                    class="amp-cms--subhead-1"
                                  >
                                    Frame Rich Text 1
                                  </span>
                                </p>
                              </div>
                            </div>
                          </div>
                        </div>
                        <a
                          aria-label="bannerLink Test"
                          class="emotion-8"
                          href="bannerLink Value"
                          title="bannerLink Test"
                        >
                           
                        </a>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <button
                aria-label="Next"
                class="slick-next slick-arrow slick-next"
                data-role="none"
              >
                <span
                  aria-hidden="true"
                  class="emotion-3"
                >
                  <svg
                    viewBox="0 0 26.78 17.63"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M0 1.95l13 15.68L26.79 1.98 24.53 0 13.08 13.01 2.3.03z"
                      fill="#000000"
                    />
                  </svg>
                </span>
              </button>
            </div>
          </div>
        </nav>
        <div
          class="emotion-38"
        >
          <a
            class="emotion-39"
            color="dark"
            href="/testing cta2"
          >
            <span
              class="emotion-40"
            >
              CTA 1
            </span>
          </a>
          <div
            class="emotion-41"
          >
            <div>
              <p
                class"amp-cms--p"=""
                style="text-align:center;"
              >
                <span
                  class="amp-cms--subhead-1"
                >
                  This is RTE Body Copy
                </span>
              </p>
            </div>
          </div>
          <a
            class="emotion-42"
            color="primary"
            href="/testing cta2"
          >
            <span
              class="emotion-40"
            >
              CTA 2
            </span>
          </a>
          <div
            class="emotion-44"
          >
            <button
              class="emotion-45"
            >
              Details links test
            </button>
          </div>
        </div>
      </div>
      ,
    </div>
  </div>
</DocumentFragment>
`;
