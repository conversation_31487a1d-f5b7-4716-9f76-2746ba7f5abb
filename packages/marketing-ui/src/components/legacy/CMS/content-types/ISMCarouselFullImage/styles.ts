// @ts-nocheck
'use client';
import { styled } from '@ecom-next/core/react-stitch';
import { WrapperProps } from './types';

export const ContentDiv = styled.div`
  position: absolute;
  top: 0;
  width: 100%;
  height: 100%;
  z-index: 0;
`;

export const WrapperDiv = styled.div<WrapperProps>(({ padding }) => ({
  boxSizing: 'border-box',
  padding: `${padding}px`,
  position: 'relative',
  height: '100%',
  display: ' flex',
  alignItems: 'center',
  justifyContent: 'top',
  flexDirection: 'column',
}));
