// @ts-nocheck
import { ISMCarouselSinglePartialImageProps } from '../types';

const backgroundViewPng = require('../../../../assets/background-2.png').default?.src;
const lightBackgroundPng = require('../../../../assets/light-background.png').default?.src;

export const ISMCarouselSinglePartialImageData: ISMCarouselSinglePartialImageProps = {
  frames: [
    {
      backgroundImage: [
        {
          svgPath: backgroundViewPng,
          altText: 'Background View',
          variations: [
            {
              variation: 'desktop',
            },
            {
              variation: 'mobile',
            },
          ],
          fliph: false,
          flipv: false,
          enableChroma: false,
          chromaQuality: 80,
        },
      ],
      image: {
        _meta: {
          schema: 'http://bigcontent.io/cms/schema/v1/core#/definitions/image-link',
        },
        id: '406434f0-59fc-4a53-83f5-874620f1129c',
        name: 'GapFit',
        endpoint: 'gap',
        defaultHost: '1qm2jngqyb45s1j3ksoortf4y7.staging.bigcontent.io',
      },
      richTextArea: '<p class"amp-cms--p" style="text-align:center;"><span class="amp-cms--subhead-1">Lorem Ipsum 1</span></p>',
      webAppearance: {
        verticalTextAlignment: 'top',
        imageOrIconHorizontalAlignment: 'center',
        imageOrIconPlacement: 'above',
        desktopImageOrIconSize: '32px',
        mobileImageOrIconSize: '32px',
      },
    },
    {
      backgroundImage: [
        {
          svgPath: lightBackgroundPng,
          altText: 'Light Background',
          variations: [
            {
              variation: 'desktop',
            },
            {
              variation: 'mobile',
            },
          ],
          fliph: false,
          flipv: false,
          enableChroma: false,
          chromaQuality: 80,
        },
      ],
      image: {
        _meta: {
          schema: 'http://bigcontent.io/cms/schema/v1/core#/definitions/image-link',
        },
        id: '6fea0157-405c-4c27-a796-d5aa5dc101f6',
        name: 'Freesample',
        endpoint: 'gapfactory',
        defaultHost: '1jzn2l1y362bq1fktn2mfkqw10.staging.bigcontent.io',
      },
      richTextArea: '<p class"amp-cms--p" style="text-align:center;"><span class="amp-cms--subhead-1">Lorem Ipsum 2</span></p>',
      webAppearance: {
        verticalTextAlignment: 'middle',
        imageOrIconHorizontalAlignment: 'center',
        imageOrIconPlacement: 'above',
        desktopImageOrIconSize: '32px',
        mobileImageOrIconSize: '32px',
      },
    },
  ],
  contentConfiguration: 'unique',
  cta1: {
    label: 'CTA 1',
    value: 'testing cta2',
  },
  cta2: {
    label: 'CTA 2',
    value: 'testing cta2',
  },
  bodyCopy: '<p class="amp-cms--p" style="text-align:left;"><span class="amp-cms--body-2">Quick-drying fabrics, stay-put waistbands.</span></p>',
  bannerLink: {
    label: '',
    value: 'linkToNewArrivals',
  },
  detailsLink: 'Details',
  detailsPrefix: 'Details Prefix',
  detailsLinkLocation: 'right',
  pemoleCode: 'pemolecode',
  htmlModalUrl: 'https://www.gap.com/Asset_Archive/GPWeb/content/static-marketing/xbrand-edfs-content/edfsLegal-GP-FS25.html?v=0',
  carouselSettings: {
    transition: 'slide',
    autoplay: {
      delay: 60000,
      pauseOnHover: true,
    },
    animation: {
      speed: 500,
      ease: false,
    },
    styling: {
      controlsIconsColor: 'secondary',
      playPausePosition: 'top-left',
    },
  },
  webAppearance: {
    detailsLinkFontColor: '#000000',
    ctaButton1Styling: {
      buttonStyle: 'underline',
      buttonColor: 'dark',
    },
    ctaButton2Styling: {
      buttonStyle: 'underline',
      buttonColor: 'primary',
    },
    showHideBasedOnScreenSize: 'alwaysShow',
  },
  rowPosition: {
    desktop: 1,
    mobile: 1,
  },
};
