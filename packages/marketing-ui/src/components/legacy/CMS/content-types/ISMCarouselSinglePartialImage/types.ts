// @ts-nocheck
'use client';
import { CtaProps } from '../../subcomponents/CTAButton/types';
import { MediaImageLink, AdvanceImageData } from '../../helpers/ImageEncoder';
import { AdvanceImageExtensionValue } from '../../types/amplience';
import { AlignmentProps, RowPositionType, BannerLinkType, VerticalAlignmentProps } from '../../global/types';
import { ControlColorTypes } from '../../subcomponents/CMSMarketingCarousel/types';
import { ShowHideBasedOnScreenSizeProps } from '../../subcomponents/ShowHideWrapper/types';
import { DetailsLinkAlignment } from '../ISMBanner/types';

export type ISMCarouselSinglePartialImageSchema = 'https://cms.gap.com/schema/content/v1/ism-single-partial-image-carousel.json';

type AmpIconSizeType = string;
export type Transitions = 'slide' | 'fade' | 'pan';
export interface WebAppearanceFrameProps {
  imageOrIconHorizontalAlignment?: AlignmentProps;
  imageOrIconPlacement?: 'above' | 'below';
  verticalTextAlignment?: VerticalAlignmentProps;
  desktopImageOrIconSize?: AmpIconSizeType;
  mobileImageOrIconSize?: AmpIconSizeType;
}
export interface WebAppearanceProps {
  ctaButton1Styling?: CtaProps['ctaButtonStyling'];
  ctaButton2Styling?: CtaProps['ctaButtonStyling'];
  detailsLinkFontColor?: string;
  showHideBasedOnScreenSize: ShowHideBasedOnScreenSizeProps;
}
export interface Frame {
  backgroundImage: AdvanceImageExtensionValue;
  contentConfiguration?: 'unique' | 'persistent';
  image?: MediaImageLink | AdvanceImageData[];
  richTextArea?: string;
  webAppearance: WebAppearanceFrameProps;
}
interface AutoplaySettings {
  delay?: number;
  pauseOnHover: boolean;
}
interface Animation {
  speed?: number;
  ease: boolean;
}
interface Styling {
  controlsIconsColor: ControlColorTypes;
  playPausePosition?: 'top-left' | 'top-right' | 'bottom-left' | 'bottom-right';
}
export interface CarouselSettings {
  autoplay: AutoplaySettings;
  transition?: Transitions;
  animation: Animation;
  styling: Styling;
}
export interface ISMCarouselSinglePartialImageProps {
  contentConfiguration?: 'unique' | 'persistent';
  rowPosition?: RowPositionType;
  instanceName?: string;
  bodyCopy?: string;
  cta1?: CtaProps['ctaButton'];
  cta2?: CtaProps['ctaButton'];
  detailsLink?: string;
  detailsPrefix?: string;
  detailsLinkLocation?: DetailsLinkAlignment;
  bannerLink?: BannerLinkType;
  pemoleCode?: string;
  htmlModalUrl?: string;
  frames: Array<Frame>;
  carouselSettings: CarouselSettings;
  webAppearance: WebAppearanceProps;
  onPlayPauseChange?: (status: 'play' | 'pause') => void;
}
export interface CarouselSlideOverlayProps {
  width: number;
  height: number;
}
