// @ts-nocheck
import { ProductDetailsPageContentProps } from '../types';

// mobile carousel bottom aligned and center justified
export const productDetailsPageUseCase7: ProductDetailsPageContentProps = {
  _meta: {
    name: 'Product Detail Page - VR7',
    schema: 'https://cms.gap.com/schema/content/v1/product-detail-page.json',
    deliveryId: '571f3a7d-f9de-4256-a575-3c9a4963b4a1',
  },
  general: {
    desktopLayout: '4/1',
    mobileLayout: '4/5',
    showHideBasedOnScreenSize: 'alwaysShow',
  },
  defaultImage: {
    splitViewImages: {},
  },
  media: {
    useGradientBackfill: false,
    carousel: [
      {
        frames: [
          {
            backgroundImage: [
              {
                image: {
                  _meta: {
                    schema: 'http://bigcontent.io/cms/schema/v1/core#/definitions/image-link',
                  },
                  id: 'f8ddfd3f-7da6-480a-879a-cbf8672bac4d',
                  name: 'HOL22_D3_BFWEEKEND_HP_SpotlightCTDWN_XL_FC',
                  endpoint: 'athleta',
                  defaultHost: '1d9xxafvh577y12766tt2karod.staging.bigcontent.io',
                },
                altText: 'img',
                variations: [
                  {
                    variation: 'desktop',
                  },
                  {
                    variation: 'mobile',
                  },
                ],
                fliph: false,
                flipv: false,
                enableChroma: false,
                chromaQuality: 80,
              },
            ],
          },
          {
            backgroundImage: [
              {
                image: {
                  _meta: {
                    schema: 'http://bigcontent.io/cms/schema/v1/core#/definitions/image-link',
                  },
                  id: 'ca86dfb1-b16f-429c-96d9-c57a04a5aeba',
                  name: 'storytelling_AF_backg_xl@2x',
                  endpoint: 'athleta',
                  defaultHost: '1d9xxafvh577y12766tt2karod.staging.bigcontent.io',
                },
                altText: 'img',
                variations: [
                  {
                    variation: 'desktop',
                  },
                  {
                    variation: 'mobile',
                  },
                ],
                fliph: false,
                flipv: false,
                enableChroma: false,
                chromaQuality: 80,
              },
            ],
          },
          {
            backgroundImage: [
              {
                image: {
                  _meta: {
                    schema: 'http://bigcontent.io/cms/schema/v1/core#/definitions/image-link',
                  },
                  id: 'febc68e9-c7b8-43ea-8265-f15d30bc7ea2',
                  name: 'Su4_ATG_DP_Spotlight_BTS_XL@2x',
                  endpoint: 'athleta',
                  defaultHost: '1d9xxafvh577y12766tt2karod.staging.bigcontent.io',
                },
                altText: 'img',
                variations: [
                  {
                    variation: 'desktop',
                  },
                  {
                    variation: 'mobile',
                  },
                ],
                fliph: false,
                flipv: false,
                enableChroma: false,
                chromaQuality: 80,
              },
            ],
          },
          {
            backgroundImage: [
              {
                image: {
                  _meta: {
                    schema: 'http://bigcontent.io/cms/schema/v1/core#/definitions/image-link',
                  },
                  id: '793645e3-65e1-46c3-b14b-76327090ea49',
                  name: 'ATG_DP_secondary_XL@2x_1',
                  endpoint: 'athleta',
                  defaultHost: '1d9xxafvh577y12766tt2karod.staging.bigcontent.io',
                },
                altText: 'img',
                variations: [
                  {
                    variation: 'desktop',
                  },
                  {
                    variation: 'mobile',
                  },
                ],
                fliph: false,
                flipv: false,
                enableChroma: false,
                chromaQuality: 80,
              },
            ],
          },
          {
            backgroundImage: [
              {
                image: {
                  _meta: {
                    schema: 'http://bigcontent.io/cms/schema/v1/core#/definitions/image-link',
                  },
                  id: 'febc68e9-c7b8-43ea-8265-f15d30bc7ea2',
                  name: 'Su4_ATG_DP_Spotlight_BTS_XL@2x',
                  endpoint: 'athleta',
                  defaultHost: '1d9xxafvh577y12766tt2karod.staging.bigcontent.io',
                },
                altText: 'img',
                variations: [
                  {
                    variation: 'desktop',
                  },
                  {
                    variation: 'mobile',
                  },
                ],
                fliph: false,
                flipv: false,
                enableChroma: false,
                chromaQuality: 80,
              },
            ],
          },
          {
            backgroundImage: [
              {
                image: {
                  _meta: {
                    schema: 'http://bigcontent.io/cms/schema/v1/core#/definitions/image-link',
                  },
                  id: 'ca86dfb1-b16f-429c-96d9-c57a04a5aeba',
                  name: 'storytelling_AF_backg_xl@2x',
                  endpoint: 'athleta',
                  defaultHost: '1d9xxafvh577y12766tt2karod.staging.bigcontent.io',
                },
                altText: 'img',
                variations: [
                  {
                    variation: 'desktop',
                  },
                  {
                    variation: 'mobile',
                  },
                ],
                fliph: false,
                flipv: false,
                enableChroma: false,
                chromaQuality: 80,
              },
            ],
          },
          {
            backgroundImage: [
              {
                image: {
                  _meta: {
                    schema: 'http://bigcontent.io/cms/schema/v1/core#/definitions/image-link',
                  },
                  id: 'cc10c869-d709-4034-bef5-5e40541288e2',
                  name: 'spotlight_BAP_XL@2x_1',
                  endpoint: 'athleta',
                  defaultHost: '1d9xxafvh577y12766tt2karod.staging.bigcontent.io',
                },
                altText: 'img',
                variations: [
                  {
                    variation: 'desktop',
                  },
                  {
                    variation: 'mobile',
                  },
                ],
                fliph: false,
                flipv: false,
                enableChroma: false,
                chromaQuality: 80,
              },
            ],
          },
          {
            backgroundImage: [
              {
                image: {
                  _meta: {
                    schema: 'http://bigcontent.io/cms/schema/v1/core#/definitions/image-link',
                  },
                  id: 'b2fb92a3-9928-42d1-810c-b05e4df806a8',
                  name: 'SU22_D1_BestOfBottoms_Cabo_IMG_XL_1',
                  endpoint: 'athleta',
                  defaultHost: '1d9xxafvh577y12766tt2karod.staging.bigcontent.io',
                },
                altText: 'img',
                variations: [
                  {
                    variation: 'desktop',
                  },
                  {
                    variation: 'mobile',
                  },
                ],
                fliph: false,
                flipv: false,
                enableChroma: false,
                chromaQuality: 80,
              },
            ],
          },
          {
            backgroundImage: [
              {
                image: {
                  _meta: {
                    schema: 'http://bigcontent.io/cms/schema/v1/core#/definitions/image-link',
                  },
                  id: 'febc68e9-c7b8-43ea-8265-f15d30bc7ea2',
                  name: 'Su4_ATG_DP_Spotlight_BTS_XL@2x',
                  endpoint: 'athleta',
                  defaultHost: '1d9xxafvh577y12766tt2karod.staging.bigcontent.io',
                },
                altText: 'img',
                variations: [
                  {
                    variation: 'desktop',
                  },
                  {
                    variation: 'mobile',
                  },
                ],
                fliph: false,
                flipv: false,
                enableChroma: false,
                chromaQuality: 80,
              },
            ],
          },
          {
            backgroundImage: [
              {
                image: {
                  _meta: {
                    schema: 'http://bigcontent.io/cms/schema/v1/core#/definitions/image-link',
                  },
                  id: 'd7563db3-2153-4dcf-be09-2b706f8de14c',
                  name: 'PG_IMG2_XL',
                  endpoint: 'athleta',
                  defaultHost: '1d9xxafvh577y12766tt2karod.staging.bigcontent.io',
                },
                altText: 'img',
                variations: [
                  {
                    variation: 'desktop',
                  },
                  {
                    variation: 'mobile',
                  },
                ],
                fliph: false,
                flipv: false,
                enableChroma: false,
                chromaQuality: 80,
              },
            ],
          },
        ],
        carouselSettings: {
          transition: 'slide',
          type: 'clickThrough',
          continuousLoop: false,
          autoplay: {
            delay: 3000,
            pauseOnHover: false,
          },
          animation: {
            speed: 500,
            ease: false,
          },
          styling: {
            controlsIconsColor: 'primary',
            pagination: 'hide',
            hideChevronsDesktop: false,
            hideChevronsMobile: false,
          },
        },
      },
    ],
  },
  content: {
    background: {
      type: 'solid',
      color: '#17D5DE',
    },
    icon: {
      iconSize: '24px',
    },
    mobileIcon: {
      iconSize: '14px',
    },
    textArea: [
      {
        text: '<p class="amp-cms--p" style="text-align:left;"><span class="amp-cms--eyebrow-2" style="text-transform:uppercase;color:#C10076">introducing</span></p><p class="amp-cms--p" style="text-align:left;"><span class="amp-cms--headline-5" style="text-transform:uppercase;color:#C10076">The Ultimate II Tight</span></p><p class="amp-cms--p" style="text-align:left;"><span class="amp-cms--body-1" style="color:#C10076">Our #1 sweat ready style just got a major upgrade.</span></p>',
        mobileText: [
          {
            mobileText:
              '<p class="amp-cms--p" style="text-align:left;"><span class="amp-cms--headline-2" style="color:#FFF">THE ULTIMATE II TIGHT</span></p><p class="amp-cms--p" style="text-align:left;"><span class="amp-cms--body-1" style="color:#FFF">Our #1 sweat ready style just got a major upgrade. Lorem ipsum dolor sit amet.</span></p>',
          },
        ],
      },
    ],
    iconLibrary: [
      {
        id: '32cf0fc9-ea4f-4ee3-98a5-2c981a6f4538',
        name: 'WRINKLE-RESISTANT_light',
        label: 'Wrinkle-Resistant',
        staticUrl: 'https://athleta.a.bigcontent.io/v1/static/WRINKLE-RESISTANT_light',
      },
      {
        id: '3de7887e-56b4-4f1f-a16a-e63a16cd76a8',
        name: 'BREATHABLE_light',
        label: 'Breathable',
        staticUrl: 'https://athleta.a.bigcontent.io/v1/static/BREATHABLE_light',
      },
      {
        id: '6de3b8d1-4c22-4037-a8a4-c9c5debda89a',
        name: 'ALL-AROUND-STRETCH_light',
        label: 'All-Around Stretch',
        staticUrl: 'https://athleta.a.bigcontent.io/v1/static/ALL-AROUND-STRETCH_light',
      },
    ],
    contentJustification: {
      defaultContentJustification: 'center',
    },
    horizontalPosition: 'center',
    verticalPosition: 'bottom',
    cta: [
      {
        buttonStyle: {
          buttonStyle: 'chevron',
          buttonColor: 'light',
        },
        cta: {
          label: 'Shop The Ultimate Collection',
          value: 'https://www.gap.com',
        },
      },
    ],
  },
};
