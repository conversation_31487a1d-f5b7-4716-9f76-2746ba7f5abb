// @ts-nocheck
import { PromoDrawerContentType, PromoDrawerProps, BrPromoDrawerProps } from '../types';
const giftsImage = require('../../../../assets/gifts.png').default?.src;

export const promoDrawerData: PromoDrawerProps = {
  _meta: {
    name: 'Promo Drawer - Alina',
    schema: 'https://cms.gap.com/schema/content/v1/promo-drawer.json',
    deliveryId: '34347faa-0718-4fe8-8be3-a8c105429133',
  },
  promoCards: [
    // Card 1 - 4 CTAs, link wrapper should not appear even authored
    {
      background: {
        type: 'solid',
        color: '#F00',
      },
      ctaButtonStylingForDesktop: {
        buttonStyle: 'solid',
        buttonColor: 'light',
      },
      ctaButtonStylingForMobile: {
        buttonStyle: 'solid',
        buttonColor: 'dark',
      },
      tapToApply: false,
      promoDetails: 'Secondary Details',
      detailsLink: 'Details',
      legalDetailsLocation: 'above',
      detailsLinkColor: 'primary',
      pemoleCode: '123456',
      cta: [
        {
          cta: {
            label: 'Shop All',
            value: 'test',
          },
        },
        {
          cta: {
            label: 'Women',
            value: 'test',
          },
        },
        {
          cta: {
            label: 'Men',
            value: 'test',
          },
        },
      ],
      linkWrapperURL: 'should.not.appear',
      linkWrapperAltText: 'should not appear',
    },
    // Card 2 - 4 CTAs, no link authored
    {
      background: {
        type: 'solid',
        color: '#F00',
      },
      ctaButtonStylingForDesktop: {
        buttonStyle: 'underline',
        buttonColor: 'light',
      },
      ctaButtonStylingForMobile: {
        buttonStyle: 'underline',
        buttonColor: 'dark',
      },
      tapToApply: false,
      mainPromoMessage: '<p class="amp-cms--p" style="text-align:left;"><span class="amp-cms--body-1">Main Message</span></p>',
      promoDetails: 'Secondary Details',
      detailsLink: 'Details',
      legalDetailsLocation: 'below',
      detailsLinkColor: 'secondary',
      pemoleCode: '123456',
      htmlModalUrl: 'https://cdn.c1.amplience.net/c/oldnavyprod/rest_details_070823',
      cta: [
        {
          cta: {
            label: 'CTA 1',
            value: 'test',
          },
        },
        {
          cta: {
            label: 'CTA 2',
            value: 'test',
          },
        },
        {
          cta: {
            label: 'CTA 3',
            value: 'test',
          },
        },
        {
          cta: {
            label: 'CTA 4',
            value: 'test',
          },
        },
      ],
    },
    // Card 3 - no CTA, link wrapper authored
    {
      background: {
        type: 'image',
        images: [
          {
            svgPath: giftsImage,
            altText: 'Gifts from $6',
            variations: [
              {
                variation: 'desktop',
              },
              {
                variation: 'mobile',
              },
            ],
            fliph: false,
            flipv: false,
            enableChroma: false,
            chromaQuality: 80,
          },
        ],
      },
      tapToApply: false,
      linkWrapperURL: 'link.to.somewhere',
      linkWrapperAltText: 'link to somewhere',
      legalDetailsLocation: 'above',
      detailsLinkColor: 'primary',
    },
    // Card 4 - 2 CTAs, no link authored
    {
      background: {
        type: 'gradient',
        gradient: {
          from: '#FFFFFF',
          to: '#00F',
        },
      },
      mainPromoMessage: 'Rich Text Editor 1',
      promoDetails: 'Rich Text Editor 2',
      detailsLink: 'Details',
      legalDetailsLocation: 'above',
      pemoleCode: '123456',
      htmlModalUrl: 'https://cdn.c1.amplience.net/c/oldnavyprod/rest_details_070823',
      cta: [
        {
          cta: {
            label: 'CTA 1',
            value: 'test',
          },
        },
        {
          cta: {
            label: 'CTA 2',
            value: 'test',
          },
        },
      ],
    },
    // Card 5 - 3 CTAs
    {
      background: {
        color: '#003764',
        type: 'solid',
      },
      mainPromoMessage: 'Rich Text Editor 1',
      promoDetails: 'Rich Text Editor 2',
      detailsLink: 'Details',
      legalDetailsLocation: 'above',
      pemoleCode: '123456',
      htmlModalUrl: 'https://cdn.c1.amplience.net/c/oldnavyprod/rest_details_070823',
      cta: [
        {
          cta: {
            label: 'CTA 1',
            value: 'test',
          },
        },
        {
          cta: {
            label: 'CTA 2',
            value: 'test',
          },
        },
        {
          cta: {
            label: 'CTA 3',
            value: 'test',
          },
        },
      ],
    },
    // Card 6 - 1 CTA, link authorable
    {
      background: {
        color: '#F31F00',
        type: 'solid',
      },
      mainPromoMessage: 'Rich Text Editor 1',
      promoDetails: 'Rich Text Editor 2',
      detailsLink: 'Details',
      legalDetailsLocation: 'above',
      pemoleCode: '123456',
      htmlModalUrl: 'https://cdn.c1.amplience.net/c/oldnavyprod/rest_details_070823',
      cta: [
        {
          cta: {
            label: 'CTA 1',
            value: 'test',
          },
        },
      ],
      linkWrapperURL: 'link.to.somewhere',
      linkWrapperAltText: 'link to somewhere',
    },
    // Card 7 - No CTA, no link
    {
      background: {
        color: '#0F4F43',
        type: 'solid',
      },
      mainPromoMessage: 'Rich Text Editor 1',
      promoDetails: 'Rich Text Editor 2',
      detailsLink: 'Details',
      legalDetailsLocation: 'above',
      pemoleCode: '123456',
      htmlModalUrl: 'https://cdn.c1.amplience.net/c/oldnavyprod/rest_details_070823',
    },
    // Card 8 - Tap to Apply, no link
    {
      background: {
        color: '#003764',
        type: 'solid',
      },
      tapToApply: true,
      promoCode: 'YOURS',
      promoCodeId: '972578',
    },
    // Card 9 - Tap to Apply, link authorable
    {
      background: {
        type: 'solid',
        color: '#F3EFE0',
      },
      detailsLinkColor: 'primary',
      legalDetailsLocation: 'above',
      tapToApply: true,
      ctaButtonStylingForDesktop: {
        buttonStyle: 'border',
        buttonColor: 'dark',
      },
      ctaButtonStylingForMobile: {
        buttonStyle: 'border',
        buttonColor: 'dark',
      },
      mainPromoMessage: '<p class="amp-cms--p" style="text-align:left;"><span class="amp-cms--body-1">Tap to apply card</span></p>',
      promoCode: 'YOURS',
      promoCodeId: '972577',
      linkWrapperURL: 'link.to.somewhere',
      linkWrapperAltText: 'link to somewhere',
    },
  ],
  main: {
    handlebarText: '2 Days Only! 60% off mystery styles',
  },
};

export const promoDrawerData10Cards: PromoDrawerContentType = {
  _meta: {
    name: 'Promo Drawer - Alina Example',
    schema: 'https://cms.gap.com/schema/content/v1/promo-drawer.json',
    deliveryId: 'cc6a9870-2c38-47be-98b7-ca2c32d7c057',
  },
  promoCards: [
    {
      background: {
        type: 'solid',
        color: '#FF0',
      },
      tapToApply: false,
      mainPromoMessage: '<p class="amp-cms--p" style="text-align:left;"><span class="amp-cms--subhead-2">Rich Text Main</span></p>',
      cta: [
        {
          cta: {
            label: 'A',
            value: '#A',
          },
        },
        {
          cta: {
            label: 'B',
            value: '#B',
          },
        },
      ],
    },
    {
      background: {
        type: 'image',
        images: [
          {
            image: {
              _meta: {
                schema: 'http://bigcontent.io/cms/schema/v1/core#/definitions/image-link',
              },
              id: '2ed124be-4027-4daa-8c46-941909973ac9',
              name: '231031_80_M9595_Gifting_BI_HP_Secondary_SM_US_1031',
              endpoint: 'oldnavy',
              defaultHost: 'fmzlik0m0z6r1oifp8ex832rf.staging.bigcontent.io',
            },
            variations: [
              {
                variation: 'desktop',
              },
              {
                variation: 'mobile',
              },
            ],
            fliph: false,
            flipv: false,
            enableChroma: false,
            chromaQuality: 80,
          },
        ],
      },
      tapToApply: false,
      cta: [
        {
          cta: {
            label: 'Only CTA',
            value: '#only',
          },
        },
      ],
      mainPromoMessage: '<p class="amp-cms--p" style="text-align:left;"><span class="amp-cms--body-1">Rich Text details</span></p>',
    },
    {
      background: {
        type: 'solid',
        color: '#F0F',
      },
      tapToApply: false,
      cta: [
        {
          cta: {
            label: '1',
            value: '#1',
          },
        },
        {
          cta: {
            label: '2',
            value: '#2',
          },
        },
        {
          cta: {
            label: '3',
            value: '#3',
          },
        },
      ],
      detailsLink: 'Details',
      pemoleCode: '123456',
    },
    {
      background: {
        type: 'solid',
        color: '#F51',
      },
      tapToApply: true,
      mainPromoMessage: '<p class="amp-cms--p" style="text-align:left;"><span class="amp-cms--body-1">Card 4</span></p>',
    },
    {
      background: {
        type: 'solid',
        color: '#0F0',
      },
      tapToApply: false,
      mainPromoMessage: '<p class="amp-cms--p" style="text-align:left;"><span class="amp-cms--body-1">Card 5</span></p>',
    },
    {
      background: {
        type: 'solid',
        color: '#22F',
      },
      tapToApply: false,
      mainPromoMessage: '<p class="amp-cms--p" style="text-align:left;"><span class="amp-cms--subhead-2" style="color:#FFF">Card 6</span></p>',
    },
    {
      background: {
        type: 'solid',
        color: '#F95',
      },
      tapToApply: false,
      mainPromoMessage: '<p class="amp-cms--p" style="text-align:left;"><span class="amp-cms--body-1">Card 7</span></p>',
    },
    {
      background: {
        type: 'solid',
        color: '#F99',
      },
      tapToApply: false,
      mainPromoMessage: '<p class="amp-cms--p" style="text-align:left;"><span class="amp-cms--body-1">Card 8</span></p>',
    },
    {
      background: {
        type: 'solid',
        color: '#FFA',
      },
      tapToApply: false,
      mainPromoMessage: '<p class="amp-cms--p" style="text-align:left;"><span class="amp-cms--body-1">Card 9</span></p>',
    },
    {
      background: {
        type: 'solid',
        color: '#FAF',
      },
      tapToApply: false,
      mainPromoMessage: '<p class="amp-cms--p" style="text-align:left;"><span class="amp-cms--body-1">Card 10</span></p>',
    },
  ],
  main: {
    handlebarText: 'Big Deals',
  },
};

export const promoDrawerData2Cards: PromoDrawerProps = {
  _meta: {
    name: 'Promo Drawer - Alina Example',
    schema: 'https://cms.gap.com/schema/content/v1/promo-drawer.json',
    deliveryId: 'cc6a9870-2c38-47be-98b7-ca2c32d7c057',
  },
  promoCards: [
    {
      background: {
        type: 'solid',
        color: '#f00',
      },
      detailsLink: 'Details',
      pemoleCode: '123456',
      tapToApply: false,
      ctaButtonStylingForDesktop: {
        buttonStyle: 'border',
        buttonColor: 'dark',
      },
      ctaButtonStylingForMobile: {
        buttonStyle: 'border',
        buttonColor: 'dark',
      },
      promoDetails: '<p class="amp-cms--p" style="text-align:left;"><span class="amp-cms--subhead-2">Rich Text Details</span></p>',
      cta: [
        {
          cta: {
            label: 'A',
            value: '#A',
          },
        },
        {
          cta: {
            label: 'B',
            value: '#B',
          },
        },
        {
          cta: {
            label: 'C',
            value: '#B',
          },
        },
      ],
    },
    {
      background: {
        type: 'solid',
        color: '#F51',
      },
      tapToApply: true,
      ctaButtonStylingForDesktop: {
        buttonStyle: 'border',
        buttonColor: 'dark',
      },
      ctaButtonStylingForMobile: {
        buttonStyle: 'border',
        buttonColor: 'dark',
      },
      mainPromoMessage: '<p class="amp-cms--p" style="text-align:left;"><span class="amp-cms--body-1">Use Promo Code</span></p>',
    },
  ],
  main: {
    handlebarText: 'Big Deals',
  },
};

export const brPromoDrawerData: BrPromoDrawerProps = {
  _meta: {
    name: 'Promo Drawer',
    schema: 'https://cms.gap.com/schema/content/v1/promo-drawer.json',
    deliveryId: 'cc6a9870-2c38-47be-98b7-ca2c32d7c057',
  },
  promoCards: [
    {
      background: {
        type: 'solid',
        color: '#BCC7E1',
      },
      detailsLink: 'Details',
      pemoleCode: '123456',
      tapToApply: false,
      detailsLinkColor: 'primary',
      legalDetailsLocation: 'below',
      ctaButtonStylingForDesktop: {
        buttonStyle: 'underline',
        buttonColor: 'primary',
      },
      ctaButtonStylingForMobile: {
        buttonStyle: 'underline',
        buttonColor: 'primary',
      },
      promoDetails: '<p class="amp-cms--p" style="text-align:left;"><span class="amp-cms--body-3">Rich Text Details</span></p>',
      cta: [
        {
          cta: {
            label: "Shop Men's",
            value: '#A',
          },
        },
        {
          cta: {
            label: "Shop Women's",
            value: '#B',
          },
        },
      ],
    },
    {
      background: {
        type: 'solid',
        color: '#000000',
      },
      detailsLink: 'Details',
      pemoleCode: '123456',
      tapToApply: false,
      ctaButtonStylingForDesktop: {
        buttonStyle: 'underline',
        buttonColor: 'secondary',
      },
      ctaButtonStylingForMobile: {
        buttonStyle: 'underline',
        buttonColor: 'secondary',
      },
      promoDetails: '<p class="amp-cms--p" style="text-align:left;"><span class="amp-cms--subhead-2">Rich Text Details</span></p>',
      cta: [
        {
          cta: {
            label: "Shop Men's",
            value: '#A',
          },
        },
        {
          cta: {
            label: "Shop Women's",
            value: '#B',
          },
        },
      ],
    },
    {
      background: {
        type: 'solid',
        color: '#000000',
      },
      detailsLink: 'Details',
      pemoleCode: '123456',
      detailsLinkColor: 'primary',
      legalDetailsLocation: 'below',
      tapToApply: true,
      ctaButtonStylingForDesktop: {
        buttonStyle: 'underline',
        buttonColor: 'secondary',
      },
      ctaButtonStylingForMobile: {
        buttonStyle: 'underline',
        buttonColor: 'secondary',
      },
      promoDetails: '<p class="amp-cms--p" style="text-align:left"><span style="color:#FFF" class="amp-cms--body-2">Rich Text Details</span></p>',
      cta: [
        {
          cta: {
            label: "Shop Men's",
            value: '#A',
          },
        },
        {
          cta: {
            label: "Shop Women's",
            value: '#B',
          },
        },
      ],
    },
  ],
  main: {
    handlebarText: 'Big Deals',
  },
};
