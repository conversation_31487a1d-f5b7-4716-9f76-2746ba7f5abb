'use client';
import React from 'react';
// @ts-ignore
import { Brands } from '@ecom-next/core/react-stitch';
import { BackgroundTypeExtensionValue } from '../../types/amplience';
import { Capitalization } from '../../../components/ComposableButton/types';

import {
  SitewideBannerContainer,
  SitewideBannerContent,
  SitewideBannerImageOrIcon,
  SitewideBannerOverlay,
  CountdownClock,
} from '../../components/sitewide-banner/index';
import { useViewportIsLarge } from '../../../hooks/useViewportIsLarge/index';
import { CtaButton } from '../../subcomponents/CTAButton/index';
import { DetailsButton, getDetailsContent } from '../../subcomponents/Details/index';
import { RichText } from '../../subcomponents/RichText/index';
import { ShowHideWrapper } from '../../subcomponents/ShowHideWrapper/index';
import { SitewideBannerContentType } from './types';

export type SitewideBannerProps = SitewideBannerContentType;

// eslint-disable-next-line no-undef
const AthletaSiteWideBanner = (props: SitewideBannerProps): JSX.Element | null => {
  const {
    background,
    bannerLink,
    imageIconOrLogo,
    mainRichText,
    cta1,
    cta2,
    secondaryRichText,
    mobileBackground,
    mobileRichTextArea1,
    mobileRichTextArea2,
    detailsPrefix,
    detailsLink,
    pemoleCode,
    htmlModalUrl,
    webAppearance,
    timer,
  } = props;
  const isLargeVP = useViewportIsLarge();

  const detailsContent = getDetailsContent(Brands.Athleta, pemoleCode, htmlModalUrl);

  const contentBackground: BackgroundTypeExtensionValue =
    !isLargeVP && (mobileBackground?.images || mobileBackground?.gradient || mobileBackground?.color) ? mobileBackground : background;

  const imageOrIconSize = isLargeVP ? webAppearance?.desktopImageOrIconSize : webAppearance?.mobileImageOrIconSize;

  const imageOrIcon = imageIconOrLogo ? (
    <div
      // eslint-disable-next-line react/no-unknown-property
      css={{
        display: 'flex',
      }}
    >
      <SitewideBannerImageOrIcon size={imageOrIconSize} src={imageIconOrLogo} />
    </div>
  ) : undefined;

  const mainCopy = !isLargeVP && mobileRichTextArea1 ? mobileRichTextArea1 : mainRichText;

  const secondaryCopy = !isLargeVP && mobileRichTextArea2 ? mobileRichTextArea2 : secondaryRichText;

  const mobileLayout = webAppearance?.mobileLayout;

  const isMobileLinear = !isLargeVP && mobileLayout === 'Linear';

  const contentFlexDirection = isLargeVP || isMobileLinear ? 'row' : 'column';

  const flexGap = isLargeVP ? 15 : 5;

  const contentPadding = isLargeVP ? '8px 50px' : '10px 15px';
  return (
    <ShowHideWrapper breakpoint='large' showHideBasedOnScreenSize={webAppearance?.showHideBasedOnScreenSize}>
      <SitewideBannerContainer minHeight={44}>
        <SitewideBannerContent background={contentBackground} contentPadding={contentPadding}>
          <div
            // eslint-disable-next-line react/no-unknown-property
            css={{
              display: 'flex',
              flexDirection: contentFlexDirection,
              flexWrap: 'wrap',
              justifyContent: 'center',
              alignItems: 'center',
              marginLeft: 'auto',
              marginRight: 'auto',
              textAlign: 'center',
              width: isLargeVP ? 'auto' : '100%',
              gap: flexGap,
              rowGap: 4,
            }}
          >
            {imageIconOrLogo && imageOrIcon}
            {mainCopy && (
              <div
                // eslint-disable-next-line react/no-unknown-property
                css={{
                  display: 'flex',
                }}
              >
                <RichText text={mainCopy} />
              </div>
            )}
            {timer && <CountdownClock {...timer} />}
            {(cta1 || cta2) && (
              <div
                // eslint-disable-next-line react/no-unknown-property
                css={{
                  display: 'flex',
                  gap: 15,
                }}
              >
                {cta1 && (
                  <div
                    // eslint-disable-next-line react/no-unknown-property
                    css={{
                      zIndex: 2,
                    }}
                  >
                    <CtaButton
                      capitalization={Capitalization.capitalize}
                      css={{
                        minHeight: 0,
                        textUnderlineOffset: '2px',
                      }}
                      ctaButton={cta1}
                      ctaButtonStyling={webAppearance?.ctaButtonStyling}
                      ctaSize='small'
                    />
                  </div>
                )}
                {cta2 && (
                  <div
                    // eslint-disable-next-line react/no-unknown-property
                    css={{
                      zIndex: 2,
                    }}
                  >
                    <CtaButton
                      capitalization={Capitalization.capitalize}
                      css={{
                        minHeight: 0,
                        textUnderlineOffset: '2px',
                      }}
                      ctaButton={cta2}
                      ctaButtonStyling={webAppearance?.ctaButtonStyling}
                      ctaSize='small'
                    />
                  </div>
                )}
              </div>
            )}
            {secondaryCopy && (
              <div
                // eslint-disable-next-line react/no-unknown-property
                css={{
                  display: 'flex',
                }}
              >
                <RichText text={secondaryCopy} />
              </div>
            )}
            {detailsLink && (
              <div
                // eslint-disable-next-line react/no-unknown-property
                css={{
                  alignSelf: 'center',
                  display: 'flex',
                  alignItems: 'center',
                }}
              >
                <DetailsButton
                  color={webAppearance?.detailsLinkFontColor}
                  css={{
                    zIndex: 2,
                  }}
                  label={detailsLink}
                  prefix={detailsPrefix}
                  prefixColor={webAppearance?.detailsPrefixFontColor}
                  prefixVariant='body5'
                  value={detailsContent}
                />
              </div>
            )}
            {bannerLink && <SitewideBannerOverlay href={bannerLink.value} title={bannerLink.label} />}
          </div>
        </SitewideBannerContent>
      </SitewideBannerContainer>
    </ShowHideWrapper>
  );
};

export default AthletaSiteWideBanner;
