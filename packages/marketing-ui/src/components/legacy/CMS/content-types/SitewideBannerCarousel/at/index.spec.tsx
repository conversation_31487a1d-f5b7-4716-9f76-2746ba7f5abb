// @ts-nocheck
import React from 'react';
import { AppState } from '@ecom-next/sitewide/app-state-provider';
import { render, act } from 'test-utils';
import { LARGE, Size, SMALL } from '@ecom-next/core/breakpoint-provider';
import { Brands } from '@ecom-next/core/react-stitch';
import { LocalizationTestWrapper } from '../../../subcomponents/LocalizationTestWrapper';
import { sitewideBannerCarouselBasicData, carouselSettingsAutoplay } from '../__fixtures__/test-data';
import { detailsButtonCustomStyles as atDetailsButtonCustomStyles } from './styles';
import { AthletaSitewideBannerCarousel, SitewideBannerCarouselProps } from '.';

describe('SitewideBannerCarousel content type for Athleta', () => {
  const renderComponent = (props?: Partial<SitewideBannerCarouselProps>, breakpoint: Size = LARGE) =>
    render(
      <LocalizationTestWrapper>
        <AthletaSitewideBannerCarousel {...sitewideBannerCarouselBasicData} {...props} />
      </LocalizationTestWrapper>,
      {
        breakpoint,
        appState: {
          brandName: Brands.Athleta,
        } as AppState,
      }
    );

  it('should match snapshot on desktop', () => {
    const result = renderComponent();
    expect(result.asFragment()).toMatchSnapshot();
  });

  it('should match snapshot on mobile linear', () => {
    const result = renderComponent({}, SMALL);
    expect(result.asFragment()).toMatchSnapshot();
  });

  it('should match snapshot on mobile stacked', () => {
    const result = renderComponent(
      {
        webAppearance: {
          showHideBasedOnScreenSize: 'alwaysShow',
          mobileLayout: 'stacked',
        },
      },
      SMALL
    );
    expect(result.asFragment()).toMatchSnapshot();
  });
  describe('should have the correct minHeight', () => {
    it('when only 1 slide is authored', () => {
      const { container } = renderComponent(
        {
          webAppearance: {
            showHideBasedOnScreenSize: 'alwaysShow',
            mobileLayout: 'stacked',
          },
          carouselBanners: [sitewideBannerCarouselBasicData.carouselBanners[0]],
        },
        LARGE
      );
      expect(container.firstChild.firstChild).toHaveStyle('min-height: 20px');
    });
    it('when 2 or more slides are authored', () => {
      const { container } = renderComponent(
        {
          webAppearance: {
            showHideBasedOnScreenSize: 'alwaysShow',
            mobileLayout: 'stacked',
          },
        },
        LARGE
      );
      expect(container.firstChild.firstChild).toHaveStyle('min-height: 20px');
    });
  });

  describe('carousel controls', () => {
    it('should match snapshot when on autoplay', () => {
      const result = renderComponent({ carouselSettings: carouselSettingsAutoplay }, SMALL);
      expect(result.asFragment()).toMatchSnapshot();
    });

    it('should initially show pause button when on autoplay', () => {
      const { getByLabelText } = renderComponent({
        carouselSettings: carouselSettingsAutoplay,
      });
      expect(getByLabelText('pause')).toBeInTheDocument();
    });

    it('should hide play pause button when on autoplay & hidePlayPause is true', () => {
      const { queryByTestId } = renderComponent({
        carouselSettings: {
          ...carouselSettingsAutoplay,
          hidePlayPause: true,
        },
      });
      expect(queryByTestId('swb-play-pause-button')).not.toBeInTheDocument();
    });

    it('should not hide play pause button when on autoplay & hidePlayPause is false', () => {
      const { getByLabelText } = renderComponent({
        carouselSettings: {
          ...carouselSettingsAutoplay,
          hidePlayPause: false
        },
      });
      expect(getByLabelText('pause')).toBeInTheDocument();
    });

    it('should not show pause button when using clickThrough type carousel', () => {
      const { queryByLabelText } = renderComponent();
      expect(queryByLabelText('pause')).toBeNull();
    });
  });

  describe('desktop details layout', () => {
    it('should display stacked', () => {
      const { container } = renderComponent({
        carouselBanners: sitewideBannerCarouselBasicData.carouselBanners.map(item => ({
          ...item,
          desktopDetailsLayout: 'stacked',
        })),
      });
      expect(container).toMatchSnapshot();
    });

    it("should display linear if it's mobile", () => {
      const { container } = renderComponent(
        {
          carouselBanners: sitewideBannerCarouselBasicData.carouselBanners.map(item => ({
            ...item,
            desktopDetailsLayout: 'stacked',
          })),
        },
        SMALL
      );
      expect(container).toMatchSnapshot();
    });

    it('should display linear', () => {
      const { container } = renderComponent({
        carouselBanners: sitewideBannerCarouselBasicData.carouselBanners.map(item => ({
          ...item,
          desktopDetailsLayout: 'stacked',
        })),
      });
      expect(container).toMatchSnapshot();
    });

    it('should display the default (linear)', () => {
      const { container } = renderComponent();

      expect(container).toMatchSnapshot();
    });
  });
});
