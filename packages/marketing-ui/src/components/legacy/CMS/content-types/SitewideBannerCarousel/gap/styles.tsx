// @ts-nocheck
'use client';
import { styled } from '@ecom-next/core/react-stitch';
import { ctasWithoutFixedWidth } from '../../../global/constants';
import { CtaButtonStylingProps } from '../../../subcomponents/CTAButton/types';
import { CAROUSEL_ARROW_SIZE } from './constants';

export const ctaButtonMarginsTopBottom = (isLargeVP: boolean, isStacked: boolean, buttonStyle: CtaButtonStylingProps['buttonStyle']) => {
  const isLargeOrLinear = (isLargeVP: boolean, isStacked: boolean): boolean => !isStacked || isLargeVP;
  return (buttonStyle && ctasWithoutFixedWidth.includes(buttonStyle)) || !buttonStyle
    ? {}
    : isLargeOrLinear(isLargeVP, isStacked) && {
        marginTop: '8px',
        marginBottom: '8px',
      };
};

export const ClockAndRte1Wrapper = styled.div<{
  isLargeVP: boolean;
  isStacked: boolean;
  mobileContentJustification?: string;
  hasOtherContents: boolean;
}>(({ isLargeVP, isStacked, mobileContentJustification, hasOtherContents = false }) => ({
  // Style to control RTE1 + Clock width
  flex: hasOtherContents && isLargeVP ? '1' : 'unset',
  maxWidth: isLargeVP ? 'fit-content' : 'unset',

  // Styles to control RTE1 and Clock placements
  display: 'flex',
  flexDirection: isLargeVP || !isStacked ? 'row' : 'column',
  flexWrap: 'wrap',
  gap: '5px',
  alignItems: !isLargeVP && isStacked && mobileContentJustification === 'left' ? 'baseline' : 'center',
}));

export const SBCContentWrapper = styled.div<{
  isAutoplay: boolean;
  isLargeVP: boolean;
  hideChevrons?: boolean;
  hasImageIconOrLogo: boolean;
  hasDetailsLink: boolean;
  isSingleBanner: boolean;
}>(({ isAutoplay, isLargeVP, hideChevrons, hasImageIconOrLogo, isSingleBanner }) => {
  const marginWithoutChevrons = hideChevrons ? 0 : `${CAROUSEL_ARROW_SIZE}px`;

  let marginInline: number | string;

  if (isSingleBanner) {
    marginInline = 0;
  } else if (isAutoplay) {
    marginInline = `0 ${CAROUSEL_ARROW_SIZE}px`;
  } else {
    marginInline = marginWithoutChevrons;
  }

  const wrapperSideMargins = { marginInline };

  let paddingVertical: string;

  if (hasImageIconOrLogo || !isLargeVP) {
    paddingVertical = '10px';
  } else {
    paddingVertical = '15px';
  }

  return {
    position: 'relative',
    display: 'flex',
    width: '100%',
    padding: `${paddingVertical} 0`,
    ...wrapperSideMargins,
  };
});

export const SBCIconTimerRteCtaWrapper = styled.div<{
  isLargeVP: boolean;
  isStacked: boolean;
  isAutoplay?: boolean;
  hideChevrons?: boolean;
  isSingleBanner: boolean;
  desktopContentJustification?: string;
  mobileContentJustification?: string;
}>(({ isLargeVP, isStacked, isAutoplay, hideChevrons, isSingleBanner, desktopContentJustification = 'center', mobileContentJustification = 'center' }) => {
  const marginNextToChevronOrEdgeDesktop = isAutoplay || isSingleBanner || hideChevrons ? 30 : 5;
  const marginNextToChevronOrEdgeMobile = isAutoplay || isSingleBanner || hideChevrons ? 15 : 8;
  const CAROUSEL_SIDE_MARGIN = isLargeVP ? marginNextToChevronOrEdgeDesktop : marginNextToChevronOrEdgeMobile;
  const stackedJustification = isStacked ? 'center' : mobileContentJustification;

  return {
    width: '100%',
    height: '100%',
    display: 'flex',
    flexDirection: isLargeVP || !isStacked ? 'row' : 'column',
    flexWrap: !isLargeVP && !isStacked ? 'wrap' : 'nowrap',
    justifyContent: isLargeVP ? desktopContentJustification : stackedJustification,
    alignItems: !isLargeVP && mobileContentJustification === 'left' && isStacked ? 'baseline' : 'center',
    gap: `${isLargeVP ? '15px' : '5px 10px'}`,
    marginInline: CAROUSEL_SIDE_MARGIN,
  };
});

export const CtaFlexWrapper = styled.div<{
  isLargeVP: boolean;
  isStacked: boolean;
  buttonStyle: CtaButtonStylingProps['buttonStyle'];
  hasOtherContents?: boolean;
}>(({ isLargeVP, isStacked, buttonStyle, hasOtherContents = false }) => ({
  // Styles to control CTAs width
  flex: hasOtherContents && isLargeVP ? '1' : 'unset',
  maxWidth: isLargeVP ? 'fit-content' : 'unset',

  // Styles to control CTA 1 and 2 placements
  display: 'flex',
  ...ctaButtonMarginsTopBottom(isLargeVP, isStacked, buttonStyle),
  gap: isLargeVP ? 15 : 10,
}));

export const SecondaryRichTextWrapper = styled.div<{
  isLargeVP: boolean;
  hasOtherContents: boolean;
}>(({ isLargeVP, hasOtherContents = false }) => ({
  flex: hasOtherContents && isLargeVP ? '1' : 'unset',
  maxWidth: isLargeVP ? 'fit-content' : 'unset',
}));

export const DetailsContainer = styled.div<{
  isLargeVP: boolean;
  isStacked: boolean;
}>({
  display: 'flex',
  position: 'relative',
  gap: 5,
});
