// @ts-nocheck
'use client';
import React from 'react';
import { styled, <PERSON><PERSON> } from '@ecom-next/core/react-stitch';
import { Capitalization } from '../../../components/ComposableButton/types';
import {
  SitewideBannerContainer,
  SitewideBannerContent,
  SitewideBannerImageOrIcon,
  SitewideBannerOverlay,
  CountdownClock,
} from '../../components/sitewide-banner';
import { useViewportIsLarge } from '../../../hooks/useViewportIsLarge';
import { CtaButton } from '../../subcomponents/CTAButton';
import { DetailsButton, getDetailsContent } from '../../subcomponents/Details';
import { RichText } from '../../subcomponents/RichText';
import { SitewideBannerCountdownClockContentType } from './types';
import { ShowHideWrapper } from '../../subcomponents/ShowHideWrapper';

export type SitewideBannerCountdownClockProps = SitewideBannerCountdownClockContentType;

const Container = styled.div({ display: 'flex' });

const SiteWideContentWrapper = styled.div<{
  isLargeVP: boolean;
  isMobileLinear: boolean;
}>(({ isLargeVP, isMobileLinear }) => ({
  display: 'flex',
  flexDirection: isLargeVP || isMobileLinear ? 'row' : 'column',
  flexWrap: 'wrap',
  justifyContent: 'center',
  alignItems: 'center',
  marginLeft: 'auto',
  marginRight: 'auto',
  textAlign: 'center',
  width: isLargeVP ? 'auto' : '100%',
  gap: isLargeVP ? 16 : 8,
  padding: isLargeVP ? '0 20px' : 10,
}));

const CtaWrapper = styled.div<{ isLargeVP: boolean; isMobileLinear: boolean }>(({ isLargeVP, isMobileLinear }) => ({
  display: 'flex',
  paddingTop: isLargeVP || isMobileLinear ? 13 : 0,
  paddingBottom: isLargeVP || isMobileLinear ? 16 : 0,
  gap: isLargeVP ? 16 : 8,
}));

const DetailsLinkWrapper = styled.div({
  alignSelf: 'center',
  display: 'flex',
  gap: 2,
});

const CtaElement = styled.div({
  zIndex: 2,
});

const OldNavySitewideBannerCountdownClock = (props: SitewideBannerCountdownClockProps): JSX.Element | null => {
  const {
    background,
    bannerLink,
    imageIconOrLogo,
    mainRichText,
    cta1,
    cta2,
    secondaryRichText,
    mobileBackground,
    mobileRichTextArea1,
    mobileRichTextArea2,
    detailsPrefix,
    detailsLink,
    pemoleCode,
    htmlModalUrl,
    webAppearance,
    timer,
  } = props;

  const isLargeVP = useViewportIsLarge();

  const detailsContent = getDetailsContent(Brands.OldNavy, pemoleCode, htmlModalUrl);

  const mobileBackgroundImg = mobileBackground && mobileBackground.images && mobileBackground.images[0].image?.endpoint;

  const useMobileBackground = mobileBackgroundImg || mobileBackground?.gradient || mobileBackground?.color;

  const mobileContentBackground = (useMobileBackground && mobileBackground) || background;

  const contentBackground = isLargeVP ? background : mobileContentBackground;

  const imageOrIconSize = isLargeVP ? webAppearance?.desktopImageOrIconSize : webAppearance?.mobileImageOrIconSize;

  const imageOrIcon = imageIconOrLogo ? (
    <Container>
      <SitewideBannerImageOrIcon size={imageOrIconSize} src={imageIconOrLogo} />
    </Container>
  ) : undefined;

  const mainCopy = !isLargeVP && mobileRichTextArea1 ? mobileRichTextArea1 : mainRichText;

  const secondaryCopy = !isLargeVP && mobileRichTextArea2 ? mobileRichTextArea2 : secondaryRichText;

  const mobileLayout = webAppearance?.mobileLayout;

  const isMobileLinear = !isLargeVP && mobileLayout === 'Linear';

  return (
    <ShowHideWrapper breakpoint='large' showHideBasedOnScreenSize={webAppearance?.showHideBasedOnScreenSize}>
      <SitewideBannerContainer minHeight={44}>
        <SitewideBannerContent background={contentBackground}>
          <SiteWideContentWrapper isLargeVP={isLargeVP} isMobileLinear={isMobileLinear}>
            {imageIconOrLogo && imageOrIcon}
            {mainCopy && (
              <Container>
                <RichText text={mainCopy} />
              </Container>
            )}
            {timer && <CountdownClock {...timer} />}
            {(cta1 || cta2) && (
              <CtaWrapper isLargeVP={isLargeVP} isMobileLinear={isMobileLinear}>
                {cta1 && (
                  <CtaElement>
                    <CtaButton
                      capitalization={Capitalization.capitalize}
                      css={{
                        minHeight: 0,
                      }}
                      ctaButton={cta1}
                      ctaButtonStyling={webAppearance?.ctaButtonStyling}
                      ctaSize='small'
                    />
                  </CtaElement>
                )}
                {cta2 && (
                  <CtaElement>
                    <CtaButton
                      capitalization={Capitalization.capitalize}
                      css={{
                        minHeight: 0,
                      }}
                      ctaButton={cta2}
                      ctaButtonStyling={webAppearance?.ctaButtonStyling}
                      ctaSize='small'
                    />
                  </CtaElement>
                )}
              </CtaWrapper>
            )}
            {secondaryCopy && (
              <Container>
                <RichText text={secondaryCopy} />
              </Container>
            )}
            {detailsLink && (
              <DetailsLinkWrapper>
                <DetailsButton
                  color={webAppearance?.detailsLinkFontColor}
                  css={{
                    alignSelf: 'center',
                    display: 'flex',
                    zIndex: 2,
                  }}
                  detailsPrefixWrapperStyles={{
                    padding: 0,
                  }}
                  label={detailsLink}
                  prefix={detailsPrefix}
                  prefixColor={webAppearance?.detailsPrefixFontColor}
                  prefixVariant='body5'
                  value={detailsContent}
                />
              </DetailsLinkWrapper>
            )}
            {bannerLink && <SitewideBannerOverlay href={bannerLink.value} title={bannerLink.label} />}
          </SiteWideContentWrapper>
        </SitewideBannerContent>
      </SitewideBannerContainer>
    </ShowHideWrapper>
  );
};

export default OldNavySitewideBannerCountdownClock;
