// @ts-nocheck
import { SpotlightImageContentType } from '../types';

export const mobileOverrideText = 'Mobile Text';
export const defaultText = 'SpotlightImage Desktop Text';

export const spotlightImageFullbleedMinimumData: SpotlightImageContentType = {
  _meta: {
    name: 'SpotlightImage - Almost No Optional Data',
    schema: 'https://cms.gap.com/schema/content/v1/spotlight.json',
    deliveryId: '4b77a4b6-89d4-4199-98b9-c3d082e5384e',
  },
  general: {
    layout: 'fullBleed',
    background: {
      type: 'solid',
      color: '#e9f807',
    },
    mobileBackground: [
      {
        mobileBackground: {
          type: 'solid',
          color: '#e9f807',
        },
      },
    ],
    showHideBasedOnScreenSize: 'hideOnMobile',
  },
  image: {
    mainImage: [
      {
        image: {
          _meta: {
            schema: 'http://bigcontent.io/cms/schema/v1/core#/definitions/image-link',
          },
          id: '5f373131-0298-45bf-acc0-bd80db78650c',
          name: '981470_082_BRWH_AT_WOMENS_C69_SU22_Studio_1_1751',
          endpoint: 'athleta',
          defaultHost: '2hmc8flz4ofg1dy89cc38f2xm.staging.bigcontent.io',
        },
        altText: 'image here',
        variations: [
          {
            variation: 'desktop',
          },
          {
            variation: 'mobile',
          },
        ],
        fliph: false,
        flipv: false,
        enableChroma: false,
        chromaQuality: 80,
      },
    ],
  },
  content: {
    contentJustification: 'left',
    verticalAlignment: 'middle',
    icon: {
      icon: [
        {
          image: {
            _meta: {
              schema: 'http://bigcontent.io/cms/schema/v1/core#/definitions/image-link',
            },
            id: 'c1fab4c1-86a2-4296-8853-da0e334d7763',
            name: 'Gap_BOPIS_car-icon',
            endpoint: 'gap',
            defaultHost: 'pqm4o81ro4dn1332lt5a8eas8.staging.bigcontent.io',
          },
          altText: 'Gap Car Icon',
          variations: [
            {
              variation: 'desktop',
            },
            {
              variation: 'mobile',
            },
          ],
          fliph: false,
          flipv: false,
          enableChroma: false,
          chromaQuality: 80,
        },
      ],
      iconSize: '24px',
    },
    spotlightText: {
      useGradientBackfill: false,
      defaultText: `<p class="amp-cms--p" style="text-align: "right"
    };"><span class="amp-cms--headlineAlt-4">${defaultText}</span></p>`,
    },
    ctaButtons: [],
    mobileContentJustification: 'right',
    mobileVerticalAlignment: 'middle',
  },
};

export const getSpotlightImageData = (layout: 'inset' | 'fullBleed'): SpotlightImageContentType => ({
  _meta: {
    name: 'SpotlightImage',
    schema: 'https://cms.gap.com/schema/content/v1/spotlight.json',
    deliveryId: '4b77a4b6-89d4-4199-98b9-c3d082e5384e',
  },
  general: {
    layout,
    background: {
      type: 'image',
      images: [
        {
          image: {
            _meta: {
              schema: 'http://bigcontent.io/cms/schema/v1/core#/definitions/image-link',
            },
            id: 'a9e5872d-9007-4039-b9f4-1e01256ad792',
            name: 'SPOTLIGHT_3B_XL_TEST2',
            endpoint: 'athleta',
            defaultHost: '1d9xxafvh577y12766tt2karod.staging.bigcontent.io',
          },
          altText: '#',
          variations: [
            {
              variation: 'desktop',
            },
            {
              variation: 'mobile',
              crop: {
                x: 507.7894736842105,
                y: 32.59976525821595,
                width: 720,
                height: 978.7042253521126,
                unit: 'px',
              },
            },
          ],
          fliph: false,
          flipv: false,
          enableChroma: false,
          chromaQuality: 80,
        },
      ],
    },
    mobileBackground: [
      {
        mobileBackground: {
          type: 'gradient',
          gradient: {
            from: '#2C3E50',
            to: '#FD746C',
          },
        },
      },
    ],
  },
  image: {
    mainImage: [
      {
        image: {
          _meta: {
            schema: 'http://bigcontent.io/cms/schema/v1/core#/definitions/image-link',
          },
          id: '5f373131-0298-45bf-acc0-bd80db78650c',
          name: '981470_082_BRWH_AT_WOMENS_C69_SU22_Studio_1_1751',
          endpoint: 'athleta',
          defaultHost: '2hmc8flz4ofg1dy89cc38f2xm.staging.bigcontent.io',
        },
        altText: 'image here',
        variations: [
          {
            variation: 'desktop',
          },
          {
            variation: 'mobile',
          },
        ],
        fliph: false,
        flipv: false,
        enableChroma: false,
        chromaQuality: 80,
      },
    ],
    mobileImageOverride: [
      {
        image: {
          _meta: {
            schema: 'http://bigcontent.io/cms/schema/v1/core#/definitions/image-link',
          },
          id: '3474dbf7-7855-47ed-b20c-967f669e4948',
          name: '980113_002_BKCM_AT_WMN_LS_38_SU21_TR_2_1719',
          endpoint: 'athleta',
          defaultHost: '1d9xxafvh577y12766tt2karod.staging.bigcontent.io',
        },
        altText: 'Image 02',
        variations: [
          {
            variation: 'desktop',
          },
          {
            variation: 'mobile',
          },
        ],
        fliph: false,
        flipv: false,
      },
    ],
  },
  imageOverlays: {
    handle: {
      placement: `${layout === 'inset' ? 'right' : 'left'}`,

      text: '<p class="amp-cms--p" style="text-align:left;"><span class="amp-cms--body-1">Image overlay text</span></p>',
    },
    detailsLink: {
      label: 'Details',
      fontColor: '#FFFFFF',
      prefixLabel: 'prefix',
    },
    useGradientBackfill: layout === 'inset',
  },
  content: {
    contentJustification: `${layout === 'inset' ? 'center' : 'right'}` as SpotlightImageContentType['content']['contentJustification'],
    verticalAlignment: 'middle',
    icon: {
      icon: [
        {
          image: {
            _meta: {
              schema: 'http://bigcontent.io/cms/schema/v1/core#/definitions/image-link',
            },
            id: 'c1fab4c1-86a2-4296-8853-da0e334d7763',
            name: 'Gap_BOPIS_car-icon',
            endpoint: 'gap',
            defaultHost: 'pqm4o81ro4dn1332lt5a8eas8.staging.bigcontent.io',
          },
          altText: 'Gap Car Icon',
          variations: [
            {
              variation: 'desktop',
            },
            {
              variation: 'mobile',
            },
          ],
          fliph: false,
          flipv: false,
          enableChroma: false,
          chromaQuality: 80,
        },
      ],
      iconSize: '105px',
    },
    mobileIcon: {
      icon:
        layout === 'inset'
          ? undefined
          : [
              {
                image: {
                  _meta: {
                    schema: 'http://bigcontent.io/cms/schema/v1/core#/definitions/image-link',
                  },
                  id: '15587c86-1e59-46b9-852d-d318dd80b190',
                  name: 'Gap_BOPIS_bag-icon',
                  endpoint: 'gap',
                  defaultHost: 'pqm4o81ro4dn1332lt5a8eas8.staging.bigcontent.io',
                },
                altText: 'Gap Bag Icon',
                variations: [
                  {
                    variation: 'desktop',
                  },
                  {
                    variation: 'mobile',
                  },
                ],
                fliph: false,
                flipv: false,
                enableChroma: false,
                chromaQuality: 80,
              },
            ],
      iconSize: '80px',
    },
    spotlightText: {
      useGradientBackfill: true,
      defaultText: `<p class="amp-cms--p" style="text-align: ${
        layout === 'inset' ? 'center' : 'left'
      };"><span class="amp-cms--subhead-1">${defaultText}</span></p>`,
      mobileOverride: `<p class="amp-cms--p" style="text-align:left;"><span class="amp-cms--subhead-3">${mobileOverrideText}</span></p>`,
    },
    ctaButtons: [
      {
        cta: {
          label: 'CTA 1',
          value: 'cta1',
        },
        buttonStyle: {
          buttonStyle: 'chevron',
          buttonColor: 'dark',
        },
      },
      {
        cta: {
          label: 'CTA 2',
          value: 'cta2',
        },
        buttonStyle: {
          buttonStyle: 'solid',
          buttonColor: 'dark',
        },
      },
    ],
    mobileContentJustification: 'left',
    mobileVerticalAlignment: 'middle',
  },
});

export const getSpotlightImageLinkWrapperData = (layout: 'inset' | 'fullBleed'): SpotlightImageContentType => ({
  _meta: {
    name: 'SpotlightImage',
    schema: 'https://cms.gap.com/schema/content/v1/spotlight.json',
    deliveryId: '4b77a4b6-89d4-4199-98b9-c3d082e5384e',
  },
  general: {
    layout,
    background: {
      type: 'solid',
      color: layout === 'fullBleed' ? '#e9f807' : '#FAF',
    },
    mobileBackground: [
      {
        mobileBackground: {
          type: 'gradient',
          gradient: {
            from: '#2C3E50',
            to: '#FD746C',
          },
        },
      },
    ],
  },
  image: {
    mainImage: [
      {
        image: {
          _meta: {
            schema: 'http://bigcontent.io/cms/schema/v1/core#/definitions/image-link',
          },
          id: '5f373131-0298-45bf-acc0-bd80db78650c',
          name: '981470_082_BRWH_AT_WOMENS_C69_SU22_Studio_1_1751',
          endpoint: 'athleta',
          defaultHost: '2hmc8flz4ofg1dy89cc38f2xm.staging.bigcontent.io',
        },
        altText: 'image here',
        variations: [
          {
            variation: 'desktop',
          },
          {
            variation: 'mobile',
          },
        ],
        fliph: false,
        flipv: false,
        enableChroma: false,
        chromaQuality: 80,
      },
    ],
    mobileImageOverride: [
      {
        image: {
          _meta: {
            schema: 'http://bigcontent.io/cms/schema/v1/core#/definitions/image-link',
          },
          id: '3474dbf7-7855-47ed-b20c-967f669e4948',
          name: '980113_002_BKCM_AT_WMN_LS_38_SU21_TR_2_1719',
          endpoint: 'athleta',
          defaultHost: '1d9xxafvh577y12766tt2karod.staging.bigcontent.io',
        },
        altText: 'Image 02',
        variations: [
          {
            variation: 'desktop',
          },
          {
            variation: 'mobile',
          },
        ],
        fliph: false,
        flipv: false,
      },
    ],
    link: {
      label: 'Spotlight Link Url',
      value: 'SpotlightLinkUrl.com',
    },
  },
  imageOverlays: {
    handle: {
      placement: `${layout === 'inset' ? 'right' : 'left'}`,

      text: '<p class="amp-cms--p" style="text-align:left;"><span class="amp-cms--body-1">Image overlay text</span></p>',
    },
    detailsLink: {
      label: 'Details',
      fontColor: '#FFFFFF',
      prefixLabel: 'prefix',
    },
    useGradientBackfill: layout === 'inset',
  },
  content: {
    contentJustification: `${layout === 'inset' ? 'center' : 'right'}` as SpotlightImageContentType['content']['contentJustification'],
    verticalAlignment: 'middle',
    icon: {
      icon: [
        {
          image: {
            _meta: {
              schema: 'http://bigcontent.io/cms/schema/v1/core#/definitions/image-link',
            },
            id: 'c1fab4c1-86a2-4296-8853-da0e334d7763',
            name: 'Gap_BOPIS_car-icon',
            endpoint: 'gap',
            defaultHost: 'pqm4o81ro4dn1332lt5a8eas8.staging.bigcontent.io',
          },
          altText: 'Gap Car Icon',
          variations: [
            {
              variation: 'desktop',
            },
            {
              variation: 'mobile',
            },
          ],
          fliph: false,
          flipv: false,
          enableChroma: false,
          chromaQuality: 80,
        },
      ],
      iconSize: '105px',
    },
    mobileIcon: {
      icon:
        layout === 'inset'
          ? undefined
          : [
              {
                image: {
                  _meta: {
                    schema: 'http://bigcontent.io/cms/schema/v1/core#/definitions/image-link',
                  },
                  id: '15587c86-1e59-46b9-852d-d318dd80b190',
                  name: 'Gap_BOPIS_bag-icon',
                  endpoint: 'gap',
                  defaultHost: 'pqm4o81ro4dn1332lt5a8eas8.staging.bigcontent.io',
                },
                altText: 'Gap Bag Icon',
                variations: [
                  {
                    variation: 'desktop',
                  },
                  {
                    variation: 'mobile',
                  },
                ],
                fliph: false,
                flipv: false,
                enableChroma: false,
                chromaQuality: 80,
              },
            ],
      iconSize: '80px',
    },
    spotlightText: {
      useGradientBackfill: true,
      defaultText: `<p class="amp-cms--p" style="text-align: ${
        layout === 'inset' ? 'center' : 'left'
      };"><span class="amp-cms--subhead-1">${defaultText} <a href="#">with link</a></span></p>`,
      mobileOverride: `<p class="amp-cms--p" style="text-align:left;"><span class="amp-cms--subhead-3">${mobileOverrideText}</span></p>`,
    },
    ctaButtons: [
      {
        cta: {
          label: 'CTA 1',
          value: 'cta1',
        },
        buttonStyle: {
          buttonStyle: 'chevron',
          buttonColor: 'dark',
        },
      },
      {
        cta: {
          label: 'CTA 2',
          value: 'cta2',
        },
        buttonStyle: {
          buttonStyle: 'solid',
          buttonColor: 'dark',
        },
      },
    ],
    mobileContentJustification: 'left',
    mobileVerticalAlignment: 'middle',
  },
});

export const spotlightInsetData: SpotlightImageContentType = getSpotlightImageData('inset');

export const spotlightFullBleedData: SpotlightImageContentType = getSpotlightImageData('fullBleed');

export const spotlightFullBleedLinkWrapperData: SpotlightImageContentType = getSpotlightImageLinkWrapperData('fullBleed');
