'use client';

import React from 'react';
// @ts-ignore
import { CSSObject } from '@ecom-next/core/react-stitch';
import { SpotlightVariableHeight } from '../../../components/spotlight-variable-height/index';
import { VideoOverlay } from '../../SpotlightVariableHeightVideo/types';
import { useViewportIsLarge } from '../../../../hooks/index';
import { ContentBlock, ImageOverlay, SpotlightVariableHeightContentType } from '../types';

export const getCTAContainerStyles = (media: ContentBlock | VideoOverlay | ImageOverlay, isDesktop?: boolean) => {
  const { cta, contentJustification = 'middle' } = media;
  const buttonLength = cta?.ctaDropdownList?.length || 0;
  const isLinearLayout = cta?.mobileLayout === 'linear' && !isDesktop;
  const buttonStyle = cta?.buttonStyle?.buttonStyle || 'solid';
  const isUnderLineButtonStyle = buttonStyle === 'underline';
  const contentPlacementMap = {
    left: 'left',
    middle: 'center',
    right: 'right',
  } as const;

  const linearCtaLayoutStyles = {
    display: 'grid',
    gridTemplateRows: 'auto',
    justifyContent: contentPlacementMap[contentJustification],
    justifyItems: contentPlacementMap[contentJustification],
    gap: '8px',
    gridTemplateColumns: buttonLength > 1 ? 'repeat(2, 1fr)' : 'repeat(1, 1fr)',
    alignItems: 'end',
    textAlign: contentPlacementMap[contentJustification],
    '& > div': {
      width: '100%',
      maxWidth: '100%',
      alignSelf: isUnderLineButtonStyle ? undefined : 'baseline',
      ...(isUnderLineButtonStyle &&
        buttonLength > 3 && {
          textAlign: 'center',
        }),
      ...(buttonLength % 2 !== 0 && {
        [`:nth-child(${buttonLength})`]: {
          gridColumn: 'span 2',
        },
      }),
      '& > div > ul': {
        maxWidth: '100%',
        width: '100%',
        '& > li': {
          maxWidth: '100%',
          width: '100%',
          '& > a': {
            maxWidth: '100%',
            width: '100%',
          },
        },
      },
      '& .cta-label': {
        textAlign: 'center',
        maxWidth: '100%',
        width: isUnderLineButtonStyle ? 'fit-content' : '100%',
      },
    },
  } as CSSObject;

  return isLinearLayout ? linearCtaLayoutStyles : {};
};

const OldNavySpotlightVariableHeight = (props: SpotlightVariableHeightContentType): React.JSX.Element => {
  const { contentBlocks, imageOverlay } = props;
  const { aboveImage, belowImage } = contentBlocks || {};
  const aboveMedia = aboveImage;
  const belowMedia = belowImage;

  const isDesktop = useViewportIsLarge();

  const aboveMediaCtaContainerStyles = aboveMedia ? getCTAContainerStyles(aboveMedia, isDesktop) : {};
  const belowMediaCtaContainerStyles = belowMedia ? getCTAContainerStyles(belowMedia, isDesktop) : {};
  const spotlightMediaCtaContainerStyles = imageOverlay ? getCTAContainerStyles(imageOverlay, isDesktop) : {};

  const customCtaContainerStyles = {
    aboveMediaCtaContainerStyles,
    belowMediaCtaContainerStyles,
    spotlightMediaCtaContainerStyles,
  };

  return <SpotlightVariableHeight {...props} customCtaContainerStyles={customCtaContainerStyles} />;
};

export default OldNavySpotlightVariableHeight;
