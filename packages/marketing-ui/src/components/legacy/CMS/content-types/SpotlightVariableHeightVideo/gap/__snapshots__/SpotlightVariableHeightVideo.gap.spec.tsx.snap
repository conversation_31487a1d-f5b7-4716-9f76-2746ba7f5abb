// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`SpotlightVariableHeightVideo - GAP should match snapshots matches snapshots for desktop 1`] = `
.emotion-0 {
  max-width: 100%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  margin: auto;
}

.emotion-1 {
  background: rgb(203, 214, 230);
  width: 100%;
}

.emotion-2 {
  padding: min(40px, 2.7777777777777777vw) min(147px, 10.208333333333334vw) min(32px, 2.2222222222222223vw);
  width: 100%;
  padding-inline: 2.5vw;
  padding-bottom: 30px!important;
  gap: 30px;
}

.emotion-3 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-align-items: flex-start;
  -webkit-box-align: flex-start;
  -ms-flex-align: flex-start;
  align-items: flex-start;
}

.emotion-4 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  text-align: left;
  width: 100%;
}

.emotion-5 .amp-cms--p {
  padding: 0;
  margin: 0;
  line-height: 0;
}

.emotion-5 .amp-cms--p>span {
  display: inline;
  white-space: break-spaces;
}

.emotion-5 .amp-cms--p a {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  position: relative;
  z-index: 2;
  pointer-events: auto;
}

.emotion-5 .amp-cms--p a:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-5 .amp-cms--legal-copy {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(0.8333333333333334vw, 12px));
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-5 sup {
  vertical-align: top;
  display: inline-block;
  margin-top: -0.25ex;
}

.emotion-5 sub {
  vertical-align: bottom;
  display: inline-block;
  margin-bottom: -0.25ex;
}

.emotion-5 .amp-cms--f-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.8333333333333334vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-5 .amp-cms--f-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.8333333333333334vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-5 .amp-cms--f-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.8333333333333334vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-5 .amp-cms--fn-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.8333333333333334vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-5 .amp-cms--body-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(1.3888888888888888vw, 20px));
  line-height: 1.3;
  letter-spacing: 0;
}

.emotion-5 .amp-cms--body-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(1.25vw, 18px));
  line-height: 1.2777777777777777;
  letter-spacing: 0;
}

.emotion-5 .amp-cms--body-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(1.1111111111111112vw, 16px));
  line-height: 1.25;
  letter-spacing: 0;
}

.emotion-5 .amp-cms--body-4 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(0.9722222222222222vw, 14px));
  line-height: 1.2857142857142858;
  letter-spacing: 0;
}

.emotion-5 .amp-cms--body-5 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(0.8333333333333334vw, 12px));
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-5 .amp-cms--eyebrow-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(35px, min(3.4722222222222223vw, 50px));
  line-height: 0.92;
  letter-spacing: 0;
}

.emotion-5 .amp-cms--eyebrow-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(35px, min(2.7777777777777777vw, 40px));
  line-height: 1;
  letter-spacing: 0;
}

.emotion-5 .amp-cms--eyebrow-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(35px, min(2.430555555555556vw, 35px));
  line-height: 1;
  letter-spacing: 0;
}

.emotion-5 .amp-cms--headline-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(8.333333333333332vw, 120px));
  line-height: 0.8333333333333334;
  letter-spacing: 0;
}

.emotion-5 .amp-cms--headline-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(7.638888888888889vw, 110px));
  line-height: 0.8636363636363636;
  letter-spacing: 0;
}

.emotion-5 .amp-cms--headline-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(6.944444444444445vw, 100px));
  line-height: 0.85;
  letter-spacing: 0;
}

.emotion-5 .amp-cms--headline-4 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(6.944444444444445vw, 100px));
  line-height: 1.05;
  letter-spacing: 0;
}

.emotion-5 .amp-cms--headline-5 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(5.555555555555555vw, 80px));
  line-height: 0.9375;
  letter-spacing: 0;
}

.emotion-5 .amp-cms--headline-6 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(4.861111111111112vw, 70px));
  line-height: 0.9285714285714286;
  letter-spacing: 0;
}

.emotion-5 .amp-cms--headline-7 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(4.166666666666666vw, 60px));
  line-height: 0.9166666666666666;
  letter-spacing: 0;
}

.emotion-5 .amp-cms--headlineAlt-1 {
  color: #2B2B2B;
  font-size: max(14px, min(7.916666666666666vw, 114px));
  line-height: 1;
  letter-spacing: min(-0.027777777777777776vw, -0.4px);
  font-weight: 400;
}

.emotion-5 .amp-cms--headlineAlt-2 {
  color: #2B2B2B;
  font-size: max(14px, min(5.833333333333333vw, 84px));
  line-height: 1;
  letter-spacing: min(-0.027777777777777776vw, -0.4px);
  font-weight: 400;
}

.emotion-5 .amp-cms--headlineAlt-3 {
  color: #2B2B2B;
  font-size: max(14px, min(4.444444444444445vw, 64px));
  line-height: 1;
  letter-spacing: min(-0.027777777777777776vw, -0.4px);
  font-weight: 400;
}

.emotion-5 .amp-cms--headlineAlt-4 {
  color: #2B2B2B;
  font-size: max(14px, min(2.361111111111111vw, 34px));
  line-height: 1;
  letter-spacing: min(-0.027777777777777776vw, -0.4px);
  font-weight: 400;
}

.emotion-5 .amp-cms--headlineAlt-5 {
  color: #2B2B2B;
  font-size: min(1.1111111111111112vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-5 .amp-cms--headlineAlt-6 {
  color: #2B2B2B;
  font-size: min(1.1111111111111112vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-5 .amp-cms--headlineAlt-7 {
  color: #2B2B2B;
  font-size: min(1.1111111111111112vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-5 .amp-cms--promo-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(6.944444444444445vw, 100px));
  line-height: 1;
  letter-spacing: min(0vw, 0px);
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-5 .amp-cms--promo-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(5.555555555555555vw, 80px));
  line-height: 1;
  letter-spacing: min(0vw, 0px);
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-5 .amp-cms--promoAlt-1 {
  color: #2B2B2B;
  font-size: min(1.1111111111111112vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 600;
}

.emotion-5 .amp-cms--subhead-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(2.083333333333333vw, 30px));
  line-height: 1;
  letter-spacing: 0;
}

.emotion-5 .amp-cms--subhead-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(1.7361111111111112vw, 25px));
  line-height: 1;
  letter-spacing: 0;
}

.emotion-5 .amp-cms--subhead-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(1.3888888888888888vw, 20px));
  line-height: 1.1;
  letter-spacing: 0;
}

.emotion-7 {
  background: transparent;
  position: relative;
  width: 100%;
  height: 100%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
}

.emotion-8 {
  position: absolute;
  width: 100%;
  height: 100%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  pointer-events: none;
  gap: min(24px, 1.6666666666666667vw);
}

.emotion-9 {
  width: 100%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  pointer-events: none;
  gap: min(24px, 1.6666666666666667vw);
  background: linear-gradient(180deg, rgba(0, 0, 0, 0) -0.33%, rgba(0, 0, 0, 0.4) 43.94%, rgba(0, 0, 0, 0) 88.73%);
  background-blend-mode: darken;
}

.emotion-10 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  width: 100%;
  padding: min(32px, 2.2222222222222223vw) min(147px, 10.208333333333334vw);
  gap: 30px;
  pointer-events: none;
  padding-inline: 2.5vw;
  padding-bottom: 30px!important;
}

.emotion-11 {
  text-align: center;
  z-index: 1;
}

.emotion-11 .amp-cms--p {
  padding: 0;
  margin: 0;
  line-height: 0;
}

.emotion-11 .amp-cms--p>span {
  display: inline;
  white-space: break-spaces;
}

.emotion-11 .amp-cms--p a {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  position: relative;
  z-index: 2;
  pointer-events: auto;
}

.emotion-11 .amp-cms--p a:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-11 .amp-cms--legal-copy {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(0.8333333333333334vw, 12px));
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-11 sup {
  vertical-align: top;
  display: inline-block;
  margin-top: -0.25ex;
}

.emotion-11 sub {
  vertical-align: bottom;
  display: inline-block;
  margin-bottom: -0.25ex;
}

.emotion-11 .amp-cms--f-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.8333333333333334vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-11 .amp-cms--f-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.8333333333333334vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-11 .amp-cms--f-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.8333333333333334vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-11 .amp-cms--fn-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.8333333333333334vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-11 .amp-cms--body-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(1.3888888888888888vw, 20px));
  line-height: 1.3;
  letter-spacing: 0;
}

.emotion-11 .amp-cms--body-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(1.25vw, 18px));
  line-height: 1.2777777777777777;
  letter-spacing: 0;
}

.emotion-11 .amp-cms--body-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(1.1111111111111112vw, 16px));
  line-height: 1.25;
  letter-spacing: 0;
}

.emotion-11 .amp-cms--body-4 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(0.9722222222222222vw, 14px));
  line-height: 1.2857142857142858;
  letter-spacing: 0;
}

.emotion-11 .amp-cms--body-5 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(0.8333333333333334vw, 12px));
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-11 .amp-cms--eyebrow-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(35px, min(3.4722222222222223vw, 50px));
  line-height: 0.92;
  letter-spacing: 0;
}

.emotion-11 .amp-cms--eyebrow-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(35px, min(2.7777777777777777vw, 40px));
  line-height: 1;
  letter-spacing: 0;
}

.emotion-11 .amp-cms--eyebrow-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(35px, min(2.430555555555556vw, 35px));
  line-height: 1;
  letter-spacing: 0;
}

.emotion-11 .amp-cms--headline-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(8.333333333333332vw, 120px));
  line-height: 0.8333333333333334;
  letter-spacing: 0;
}

.emotion-11 .amp-cms--headline-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(7.638888888888889vw, 110px));
  line-height: 0.8636363636363636;
  letter-spacing: 0;
}

.emotion-11 .amp-cms--headline-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(6.944444444444445vw, 100px));
  line-height: 0.85;
  letter-spacing: 0;
}

.emotion-11 .amp-cms--headline-4 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(6.944444444444445vw, 100px));
  line-height: 1.05;
  letter-spacing: 0;
}

.emotion-11 .amp-cms--headline-5 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(5.555555555555555vw, 80px));
  line-height: 0.9375;
  letter-spacing: 0;
}

.emotion-11 .amp-cms--headline-6 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(4.861111111111112vw, 70px));
  line-height: 0.9285714285714286;
  letter-spacing: 0;
}

.emotion-11 .amp-cms--headline-7 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(4.166666666666666vw, 60px));
  line-height: 0.9166666666666666;
  letter-spacing: 0;
}

.emotion-11 .amp-cms--headlineAlt-1 {
  color: #2B2B2B;
  font-size: max(14px, min(7.916666666666666vw, 114px));
  line-height: 1;
  letter-spacing: min(-0.027777777777777776vw, -0.4px);
  font-weight: 400;
}

.emotion-11 .amp-cms--headlineAlt-2 {
  color: #2B2B2B;
  font-size: max(14px, min(5.833333333333333vw, 84px));
  line-height: 1;
  letter-spacing: min(-0.027777777777777776vw, -0.4px);
  font-weight: 400;
}

.emotion-11 .amp-cms--headlineAlt-3 {
  color: #2B2B2B;
  font-size: max(14px, min(4.444444444444445vw, 64px));
  line-height: 1;
  letter-spacing: min(-0.027777777777777776vw, -0.4px);
  font-weight: 400;
}

.emotion-11 .amp-cms--headlineAlt-4 {
  color: #2B2B2B;
  font-size: max(14px, min(2.361111111111111vw, 34px));
  line-height: 1;
  letter-spacing: min(-0.027777777777777776vw, -0.4px);
  font-weight: 400;
}

.emotion-11 .amp-cms--headlineAlt-5 {
  color: #2B2B2B;
  font-size: min(1.1111111111111112vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-11 .amp-cms--headlineAlt-6 {
  color: #2B2B2B;
  font-size: min(1.1111111111111112vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-11 .amp-cms--headlineAlt-7 {
  color: #2B2B2B;
  font-size: min(1.1111111111111112vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-11 .amp-cms--promo-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(6.944444444444445vw, 100px));
  line-height: 1;
  letter-spacing: min(0vw, 0px);
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-11 .amp-cms--promo-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(5.555555555555555vw, 80px));
  line-height: 1;
  letter-spacing: min(0vw, 0px);
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-11 .amp-cms--promoAlt-1 {
  color: #2B2B2B;
  font-size: min(1.1111111111111112vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 600;
}

.emotion-11 .amp-cms--subhead-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(2.083333333333333vw, 30px));
  line-height: 1;
  letter-spacing: 0;
}

.emotion-11 .amp-cms--subhead-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(1.7361111111111112vw, 25px));
  line-height: 1;
  letter-spacing: 0;
}

.emotion-11 .amp-cms--subhead-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(1.3888888888888888vw, 20px));
  line-height: 1.1;
  letter-spacing: 0;
}

.emotion-12 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  height: auto;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  gap: 20px;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
}

.emotion-12 span[data-id="cta-dropdown-label"] {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  justify-content: space-between;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

.emotion-12 span[data-id="cta-dropdown-label"]>span {
  position: relative;
  -webkit-transform: none;
  -moz-transform: none;
  -ms-transform: none;
  transform: none;
}

.emotion-12 button {
  width: auto;
}

.emotion-12 a {
  margin-left: 1px;
}

.emotion-13 {
  position: relative;
}

.emotion-14 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  display: inline-block;
  text-transform: none;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 1px solid transparent;
  border-radius: 0;
  max-width: 380px;
  font-size: 18px;
  font-weight: 500;
  letter-spacing: 2%;
  min-height: auto;
  max-height: auto;
  line-height: 18px;
  padding: 0;
  width: 100%;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: left;
  border-left: none;
  border-right: none;
  border-radius: 0;
  background-color: transparent;
  border-bottom: 0;
  -webkit-box-pack: left;
  -ms-flex-pack: left;
  -webkit-justify-content: left;
  justify-content: left;
  color: #000000;
  height: auto;
  -webkit-text-decoration: underline;
  text-decoration: underline;
  text-underline-offset: 5px;
  text-decoration-thickness: 2.3px;
  position: relative;
  z-index: 2;
  pointer-events: auto;
  text-align: left;
  line-height: 1;
  padding: 0;
  border: 0;
  text-underline-offset: 5px;
}

.emotion-14:focus {
  outline: none;
}

.emotion-14>span {
  padding: 1px 0;
}

.emotion-14:hover,
.emotion-14:focus {
  -webkit-text-decoration: unset;
  text-decoration: unset;
}

.emotion-14:active {
  -webkit-text-decoration: unset;
  text-decoration: unset;
}

.emotion-14>* {
  width: inherit;
  -webkit-text-decoration: inherit;
  text-decoration: inherit;
}

.emotion-14>span {
  padding: 0;
}

.emotion-15 {
  box-sizing: border-box;
}

.emotion-16 {
  display: inline-block;
  text-transform: none;
  position: relative;
  width: 100%;
  -webkit-text-decoration: inherit;
  text-decoration: inherit;
}

.emotion-17 {
  display: inline-block;
  height: 10px;
  width: 10px;
  min-height: 10px;
  min-width: 10px;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  position: absolute;
  right: 0;
  top: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  margin-left: 8px;
}

.emotion-17 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-17 svg path {
  fill: currentColor;
}

.emotion-17 svg rect {
  fill: currentColor;
}

.emotion-19 {
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
  background: #FFFFFF;
  box-sizing: border-box;
  list-style-type: none;
  min-width: 100%;
  overflow: hidden;
  position: relative;
  z-index: 390;
  border-width: 0;
  border-style: solid;
  text-align: left;
  letter-spacing: 0;
  pointer-events: auto;
  padding: 0px;
  width: -webkit-fit-content!important;
  width: -moz-fit-content!important;
  width: fit-content!important;
  position: absolute;
  max-height: 0;
  width: 100%;
  -webkit-transition: max-height .5s ease-in-out,visibility .5s;
  transition: max-height .5s ease-in-out,visibility .5s;
  visibility: hidden;
  margin-top: 12px;
  color: #000000;
  background-color: #FFFFFF;
}

.emotion-19 :hover {
  color: #FFFFFF;
  background-color: #000000;
}

.emotion-20 {
  box-sizing: border-box;
  width: 100%;
  border-bottom: none;
  letter-spacing: 1px;
  margin-left: 0;
  text-transform: none;
  padding: 12px;
  border-color: #000000;
}

.emotion-20:last-child {
  border: none;
}

.emotion-21 {
  cursor: pointer;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  padding: 0px;
  -webkit-text-decoration: none;
  text-decoration: none;
  -webkit-transition: color 0.3s ease;
  transition: color 0.3s ease;
  white-space: nowrap;
  font-size: 18px;
  text-align: center;
  color: inherit;
  -webkit-box-pack: start;
  -ms-flex-pack: start;
  -webkit-justify-content: start;
  justify-content: start;
  background-color: inherit;
  font-weight: 400;
  letter-spacing: 1px;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-74 {
  position: relative;
  height: 100%;
  pointer-events: auto;
  aspect-ratio: 1440/712;
}

.emotion-75 {
  width: -webkit-fit-content;
  width: -moz-fit-content;
  width: fit-content;
  position: relative;
  width: 100%;
}

.emotion-76 {
  height: 100%;
}

.emotion-77 {
  box-sizing: border-box;
  width: 100%;
  position: absolute;
  bottom: 0;
  pointer-events: none;
  padding: 0 2.5vw 30px;
  background: linear-gradient(0deg, rgba(0, 0, 0, 0.4) 50%, rgba(0, 0, 0, 0) 100%);
  display: grid;
  grid-template-columns: auto auto;
  grid-template-rows: auto auto;
  grid-template-areas: 'top-left top-right' 'bottom-left bottom-right';
  grid-row-gap: 10px;
}

.emotion-78 {
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  grid-area: bottom-left;
}

.emotion-79 {
  pointer-events: auto;
}

.emotion-79 .amp-cms--p {
  padding: 0;
  margin: 0;
  line-height: 0;
}

.emotion-79 .amp-cms--p>span {
  display: inline;
  white-space: break-spaces;
}

.emotion-79 .amp-cms--p a {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  position: relative;
  z-index: 2;
  pointer-events: auto;
}

.emotion-79 .amp-cms--p a:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-79 .amp-cms--legal-copy {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(0.8333333333333334vw, 12px));
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-79 sup {
  vertical-align: top;
  display: inline-block;
  margin-top: -0.25ex;
}

.emotion-79 sub {
  vertical-align: bottom;
  display: inline-block;
  margin-bottom: -0.25ex;
}

.emotion-79 .amp-cms--f-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.8333333333333334vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-79 .amp-cms--f-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.8333333333333334vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-79 .amp-cms--f-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.8333333333333334vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-79 .amp-cms--fn-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.8333333333333334vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-79 .amp-cms--body-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(1.3888888888888888vw, 20px));
  line-height: 1.3;
  letter-spacing: 0;
}

.emotion-79 .amp-cms--body-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(1.25vw, 18px));
  line-height: 1.2777777777777777;
  letter-spacing: 0;
}

.emotion-79 .amp-cms--body-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(1.1111111111111112vw, 16px));
  line-height: 1.25;
  letter-spacing: 0;
}

.emotion-79 .amp-cms--body-4 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(0.9722222222222222vw, 14px));
  line-height: 1.2857142857142858;
  letter-spacing: 0;
}

.emotion-79 .amp-cms--body-5 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(0.8333333333333334vw, 12px));
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-79 .amp-cms--eyebrow-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(35px, min(3.4722222222222223vw, 50px));
  line-height: 0.92;
  letter-spacing: 0;
}

.emotion-79 .amp-cms--eyebrow-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(35px, min(2.7777777777777777vw, 40px));
  line-height: 1;
  letter-spacing: 0;
}

.emotion-79 .amp-cms--eyebrow-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(35px, min(2.430555555555556vw, 35px));
  line-height: 1;
  letter-spacing: 0;
}

.emotion-79 .amp-cms--headline-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(8.333333333333332vw, 120px));
  line-height: 0.8333333333333334;
  letter-spacing: 0;
}

.emotion-79 .amp-cms--headline-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(7.638888888888889vw, 110px));
  line-height: 0.8636363636363636;
  letter-spacing: 0;
}

.emotion-79 .amp-cms--headline-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(6.944444444444445vw, 100px));
  line-height: 0.85;
  letter-spacing: 0;
}

.emotion-79 .amp-cms--headline-4 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(6.944444444444445vw, 100px));
  line-height: 1.05;
  letter-spacing: 0;
}

.emotion-79 .amp-cms--headline-5 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(5.555555555555555vw, 80px));
  line-height: 0.9375;
  letter-spacing: 0;
}

.emotion-79 .amp-cms--headline-6 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(4.861111111111112vw, 70px));
  line-height: 0.9285714285714286;
  letter-spacing: 0;
}

.emotion-79 .amp-cms--headline-7 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(4.166666666666666vw, 60px));
  line-height: 0.9166666666666666;
  letter-spacing: 0;
}

.emotion-79 .amp-cms--headlineAlt-1 {
  color: #2B2B2B;
  font-size: max(14px, min(7.916666666666666vw, 114px));
  line-height: 1;
  letter-spacing: min(-0.027777777777777776vw, -0.4px);
  font-weight: 400;
}

.emotion-79 .amp-cms--headlineAlt-2 {
  color: #2B2B2B;
  font-size: max(14px, min(5.833333333333333vw, 84px));
  line-height: 1;
  letter-spacing: min(-0.027777777777777776vw, -0.4px);
  font-weight: 400;
}

.emotion-79 .amp-cms--headlineAlt-3 {
  color: #2B2B2B;
  font-size: max(14px, min(4.444444444444445vw, 64px));
  line-height: 1;
  letter-spacing: min(-0.027777777777777776vw, -0.4px);
  font-weight: 400;
}

.emotion-79 .amp-cms--headlineAlt-4 {
  color: #2B2B2B;
  font-size: max(14px, min(2.361111111111111vw, 34px));
  line-height: 1;
  letter-spacing: min(-0.027777777777777776vw, -0.4px);
  font-weight: 400;
}

.emotion-79 .amp-cms--headlineAlt-5 {
  color: #2B2B2B;
  font-size: min(1.1111111111111112vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-79 .amp-cms--headlineAlt-6 {
  color: #2B2B2B;
  font-size: min(1.1111111111111112vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-79 .amp-cms--headlineAlt-7 {
  color: #2B2B2B;
  font-size: min(1.1111111111111112vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-79 .amp-cms--promo-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(6.944444444444445vw, 100px));
  line-height: 1;
  letter-spacing: min(0vw, 0px);
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-79 .amp-cms--promo-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(5.555555555555555vw, 80px));
  line-height: 1;
  letter-spacing: min(0vw, 0px);
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-79 .amp-cms--promoAlt-1 {
  color: #2B2B2B;
  font-size: min(1.1111111111111112vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 600;
}

.emotion-79 .amp-cms--subhead-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(2.083333333333333vw, 30px));
  line-height: 1;
  letter-spacing: 0;
}

.emotion-79 .amp-cms--subhead-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(1.7361111111111112vw, 25px));
  line-height: 1;
  letter-spacing: 0;
}

.emotion-79 .amp-cms--subhead-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(1.3888888888888888vw, 20px));
  line-height: 1.1;
  letter-spacing: 0;
}

.emotion-80 {
  grid-area: bottom-right;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: end;
  -ms-flex-pack: end;
  -webkit-justify-content: end;
  justify-content: end;
}

.emotion-81 {
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  padding: 0;
  padding: 0;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: end;
  -webkit-box-align: end;
  -ms-flex-align: end;
  align-items: end;
}

.emotion-82 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
  text-transform: none;
  font-weight: normal;
  min-height: auto;
  color: #FFFFFF;
  font-size: 12px;
  padding: 0;
  line-height: normal;
}

.emotion-83 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  display: inline-block;
  text-transform: none;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 1px solid transparent;
  border-radius: 0;
  max-width: 300px;
  font-size: 12px;
  font-weight: 500;
  letter-spacing: 2%;
  min-height: auto;
  max-height: auto;
  line-height: 12px;
  padding: 0;
  width: auto;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: left;
  border-radius: 0;
  border: none;
  background-color: transparent;
  color: #000000;
  height: auto;
  -webkit-box-pack: left;
  -ms-flex-pack: left;
  -webkit-justify-content: left;
  justify-content: left;
  font-weight: normal;
  text-transform: none;
  z-index: 1;
  color: #FFFFFF;
  -webkit-text-decoration: underline;
  text-decoration: underline;
  font-size: 12px;
  min-height: auto;
  padding: 0;
  line-height: normal;
  margin-left: 8px;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: end;
  -webkit-box-align: end;
  -ms-flex-align: end;
  align-items: end;
  pointer-events: auto;
}

.emotion-83:focus {
  outline: none;
}

.emotion-83>span {
  padding: 1px 0;
}

.emotion-83 span svg path {
  -webkit-transition: fill 250ms ease-in-out;
  transition: fill 250ms ease-in-out;
  fill: #000000;
}

.emotion-83 span span {
  padding-left: initial;
}

.emotion-83:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-83:focus-visible {
  outline: auto;
}

.emotion-84 {
  cursor: pointer;
  position: absolute;
  height: calc(100% - 16px);
  width: 100%;
  top: 0px;
  left: 0px;
}

.emotion-85 {
  background: -webkit-linear-gradient(45deg, #DDDDFF,#3333FF);
  width: 100%;
}

.emotion-86 {
  padding: min(24px, 1.6666666666666667vw) min(147px, 10.208333333333334vw) min(40px, 2.7777777777777777vw);
  width: 100%;
  padding-inline: 2.5vw;
  padding-bottom: 30px!important;
  gap: 30px;
}

.emotion-87 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-align-items: flex-start;
  -webkit-box-align: flex-start;
  -ms-flex-align: flex-start;
  align-items: flex-start;
  gap: min(24px, 1.6666666666666667vw);
}

.emotion-91 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  height: auto;
  -webkit-box-pack: start;
  -ms-flex-pack: start;
  -webkit-justify-content: flex-start;
  justify-content: flex-start;
  gap: 60px;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

.emotion-98 {
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
  background: #FFFFFF;
  box-sizing: border-box;
  list-style-type: none;
  min-width: 100%;
  overflow: hidden;
  position: relative;
  z-index: 390;
  border-width: 0;
  border-style: solid;
  text-align: left;
  letter-spacing: 0;
  pointer-events: auto;
  padding: 0px;
  position: absolute;
  max-height: 0;
  width: 100%;
  -webkit-transition: max-height .5s ease-in-out,visibility .5s;
  transition: max-height .5s ease-in-out,visibility .5s;
  visibility: hidden;
  margin-top: 12px;
  color: #000000;
  background-color: #FFFFFF;
}

.emotion-98 :hover {
  color: #FFFFFF;
  background-color: #000000;
}

<div>
  <div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      class="emotion-0"
    >
      <div
        class="emotion-1"
        height="0"
        width="0"
      >
        <div
          class="emotion-2"
        >
          <div
            class="emotion-3"
          >
            <div
              class="emotion-4"
            >
              <div
                class="emotion-5"
              >
                <div>
                  <p
                    class="amp-cms--p"
                  >
                    <span
                      class="amp-cms--headline-1"
                    >
                      Above section
                    </span>
                  </p>
                  <p
                    class="amp-cms--p"
                  >
                    <span
                      class="amp-cms--body-1"
                    >
                      Above section body
                    </span>
                  </p>
                </div>
              </div>
            </div>
            <div
              class="emotion-4"
            />
          </div>
        </div>
      </div>
      <div
        class="emotion-7"
        height="0"
        width="0"
      >
        <div
          class="emotion-8"
        >
          <div
            class="emotion-9"
          >
            <div
              class="emotion-10"
            >
              <div
                class="emotion-11"
              >
                <div>
                  <p
                    class="amp-cms--p"
                  >
                    <span
                      class="amp-cms--headline-4"
                      style="color:#FFFFFF"
                    >
                      Video Text
                    </span>
                  </p>
                </div>
              </div>
              <div
                class="emotion-12"
              >
                <div
                  class="emotion-13"
                  data-testid="ctaDropdownWrapper"
                >
                  <button
                    aria-expanded="false"
                    class="emotion-14"
                    color="dark"
                  >
                    <span
                      class="emotion-15"
                    >
                      <span
                        class="emotion-16"
                        data-id="cta-dropdown-label"
                      >
                        Shop All
                        <span
                          aria-hidden="true"
                          class="emotion-17"
                        >
                          <svg
                            fill="none"
                            viewBox="0 0 12 12"
                            xmlns="http://www.w3.org/2000/svg"
                          >
                            <path
                              clip-rule="evenodd"
                              d="M7 0H5v5H0v2h5v5h2V7h5V5H7V0z"
                              fill="#000000"
                              fill-rule="evenodd"
                            />
                          </svg>
                        </span>
                      </span>
                    </span>
                  </button>
                  <div
                    class="emotion-18"
                  >
                    <ul
                      aria-hidden="true"
                      class="emotion-19"
                    >
                      <li
                        class="emotion-20"
                      >
                        <a
                          breakpoint="desktop"
                          class="emotion-21"
                          href="/Women"
                          target="_self"
                        >
                          Women
                        </a>
                      </li>
                      <li
                        class="emotion-20"
                      >
                        <a
                          breakpoint="desktop"
                          class="emotion-21"
                          href="/Men"
                          target="_self"
                        >
                          Men
                        </a>
                      </li>
                      <li
                        class="emotion-20"
                      >
                        <a
                          breakpoint="desktop"
                          class="emotion-21"
                          href="/Girls"
                          target="_self"
                        >
                          Girls
                        </a>
                      </li>
                      <li
                        class="emotion-20"
                      >
                        <a
                          breakpoint="desktop"
                          class="emotion-21"
                          href="/Boys"
                          target="_self"
                        >
                          Boys
                        </a>
                      </li>
                      <li
                        class="emotion-20"
                      >
                        <a
                          breakpoint="desktop"
                          class="emotion-21"
                          href="/Toddler Girls"
                          target="_self"
                        >
                          Toddler Girls
                        </a>
                      </li>
                      <li
                        class="emotion-20"
                      >
                        <a
                          breakpoint="desktop"
                          class="emotion-21"
                          href="/Toddler Boys"
                          target="_self"
                        >
                          Toddler Boys
                        </a>
                      </li>
                      <li
                        class="emotion-20"
                      >
                        <a
                          breakpoint="desktop"
                          class="emotion-21"
                          href="/Baby Girls"
                          target="_self"
                        >
                          Baby Girls
                        </a>
                      </li>
                      <li
                        class="emotion-20"
                      >
                        <a
                          breakpoint="desktop"
                          class="emotion-21"
                          href="/Baby Boys"
                          target="_self"
                        >
                          Baby Boys
                        </a>
                      </li>
                    </ul>
                  </div>
                </div>
                <div
                  class="emotion-13"
                  data-testid="ctaDropdownWrapper"
                >
                  <button
                    aria-expanded="false"
                    class="emotion-14"
                    color="dark"
                  >
                    <span
                      class="emotion-15"
                    >
                      <span
                        class="emotion-16"
                        data-id="cta-dropdown-label"
                      >
                        Shop Pants
                        <span
                          aria-hidden="true"
                          class="emotion-17"
                        >
                          <svg
                            fill="none"
                            viewBox="0 0 12 12"
                            xmlns="http://www.w3.org/2000/svg"
                          >
                            <path
                              clip-rule="evenodd"
                              d="M7 0H5v5H0v2h5v5h2V7h5V5H7V0z"
                              fill="#000000"
                              fill-rule="evenodd"
                            />
                          </svg>
                        </span>
                      </span>
                    </span>
                  </button>
                  <div
                    class="emotion-18"
                  >
                    <ul
                      aria-hidden="true"
                      class="emotion-19"
                    >
                      <li
                        class="emotion-20"
                      >
                        <a
                          breakpoint="desktop"
                          class="emotion-21"
                          href="/jeans"
                          target="_self"
                        >
                          Jeans
                        </a>
                      </li>
                      <li
                        class="emotion-20"
                      >
                        <a
                          breakpoint="desktop"
                          class="emotion-21"
                          href="/khakis"
                          target="_self"
                        >
                          Khakis
                        </a>
                      </li>
                      <li
                        class="emotion-20"
                      >
                        <a
                          breakpoint="desktop"
                          class="emotion-21"
                          href="/sweatpants"
                          target="_self"
                        >
                          Sweatpants Sweatpants Sweatpants Sweatpants Sweatpants Sweatpants Sweatpants
                        </a>
                      </li>
                    </ul>
                  </div>
                </div>
                <div
                  class="emotion-13"
                  data-testid="ctaDropdownWrapper"
                >
                  <button
                    aria-expanded="false"
                    class="emotion-14"
                    color="dark"
                  >
                    <span
                      class="emotion-15"
                    >
                      <span
                        class="emotion-16"
                        data-id="cta-dropdown-label"
                      >
                        Shop New
                        <span
                          aria-hidden="true"
                          class="emotion-17"
                        >
                          <svg
                            fill="none"
                            viewBox="0 0 12 12"
                            xmlns="http://www.w3.org/2000/svg"
                          >
                            <path
                              clip-rule="evenodd"
                              d="M7 0H5v5H0v2h5v5h2V7h5V5H7V0z"
                              fill="#000000"
                              fill-rule="evenodd"
                            />
                          </svg>
                        </span>
                      </span>
                    </span>
                  </button>
                  <div
                    class="emotion-18"
                  >
                    <ul
                      aria-hidden="true"
                      class="emotion-19"
                    >
                      <li
                        class="emotion-20"
                      >
                        <a
                          breakpoint="desktop"
                          class="emotion-21"
                          href="/Women"
                          target="_self"
                        >
                          Women
                        </a>
                      </li>
                      <li
                        class="emotion-20"
                      >
                        <a
                          breakpoint="desktop"
                          class="emotion-21"
                          href="/Men"
                          target="_self"
                        >
                          Men
                        </a>
                      </li>
                      <li
                        class="emotion-20"
                      >
                        <a
                          breakpoint="desktop"
                          class="emotion-21"
                          href="/Girls"
                          target="_self"
                        >
                          Girls
                        </a>
                      </li>
                      <li
                        class="emotion-20"
                      >
                        <a
                          breakpoint="desktop"
                          class="emotion-21"
                          href="/Boys"
                          target="_self"
                        >
                          Boys
                        </a>
                      </li>
                      <li
                        class="emotion-20"
                      >
                        <a
                          breakpoint="desktop"
                          class="emotion-21"
                          href="/Toddler Girls"
                          target="_self"
                        >
                          Toddler Girls
                        </a>
                      </li>
                      <li
                        class="emotion-20"
                      >
                        <a
                          breakpoint="desktop"
                          class="emotion-21"
                          href="/Toddler Boys"
                          target="_self"
                        >
                          Toddler Boys
                        </a>
                      </li>
                      <li
                        class="emotion-20"
                      >
                        <a
                          breakpoint="desktop"
                          class="emotion-21"
                          href="/Baby Girls"
                          target="_self"
                        >
                          Baby Girls
                        </a>
                      </li>
                      <li
                        class="emotion-20"
                      >
                        <a
                          breakpoint="desktop"
                          class="emotion-21"
                          href="/Maternity"
                          target="_self"
                        >
                          Maternity
                        </a>
                      </li>
                      <li
                        class="emotion-20"
                      >
                        <a
                          breakpoint="desktop"
                          class="emotion-21"
                          href="/Shop for the Fam Fam"
                          target="_self"
                        >
                          Shop for the Fam Fam
                        </a>
                      </li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div
          class="emotion-74"
        >
          <div
            class="emotion-75"
            data-testid="videocomponent-container"
          >
            <div
              style="position: relative;"
            >
              <img
                fetchpriority="high"
                src="https://fmzlik0m0z6r1oifp8ex832rf.staging.bigcontent.io/i/oldnavy/FA23_D3_Banner_NA_IMG1_XL?fmt=auto"
                style="height: 100%; left: 0px; object-fit: cover; position: absolute; top: 0px; width: 100%;"
              />
            </div>
          </div>
        </div>
        <div
          class="emotion-76"
        >
          <div
            class="emotion-77"
          >
            <div
              class="emotion-78"
            >
              <div
                class="emotion-79"
              >
                <div>
                  <p
                    style="color:#FFFFFF"
                  >
                    Handle Text
                  </p>
                </div>
              </div>
            </div>
            <div
              class="emotion-80"
            >
              <div
                class="emotion-81"
              >
                <span
                  class="emotion-82"
                >
                  Prefix Label
                </span>
              </div>
              <button
                class="emotion-83"
              >
                Details Link
              </button>
            </div>
          </div>
        </div>
        <a
          aria-label="Voluptatem voluptas"
          class="emotion-84"
          data-testid="conditional-link"
          href="#3"
          target="_self"
        />
      </div>
      <div
        class="emotion-85"
        height="0"
        width="0"
      >
        <div
          class="emotion-86"
        >
          <div
            class="emotion-87"
          >
            <div
              class="emotion-4"
            >
              <div
                class="emotion-5"
              >
                <div>
                  <p
                    class="amp-cms--p"
                  >
                    <span
                      class="amp-cms--headline-1"
                    >
                      Below section
                    </span>
                  </p>
                  <p
                    class="amp-cms--p"
                  >
                    <span
                      class="amp-cms--body-1"
                    >
                      Below section body
                    </span>
                  </p>
                </div>
              </div>
            </div>
            <div
              class="emotion-4"
            >
              <div
                class="emotion-91"
              >
                <div
                  class="emotion-13"
                  data-testid="ctaDropdownWrapper"
                >
                  <button
                    aria-expanded="false"
                    class="emotion-14"
                    color="dark"
                  >
                    <span
                      class="emotion-15"
                    >
                      <span
                        class="emotion-16"
                        data-id="cta-dropdown-label"
                      >
                        Shop Sale
                        <span
                          aria-hidden="true"
                          class="emotion-17"
                        >
                          <svg
                            fill="none"
                            viewBox="0 0 12 12"
                            xmlns="http://www.w3.org/2000/svg"
                          >
                            <path
                              clip-rule="evenodd"
                              d="M7 0H5v5H0v2h5v5h2V7h5V5H7V0z"
                              fill="#000000"
                              fill-rule="evenodd"
                            />
                          </svg>
                        </span>
                      </span>
                    </span>
                  </button>
                  <div
                    class="emotion-18"
                  >
                    <ul
                      aria-hidden="true"
                      class="emotion-98"
                    >
                      <li
                        class="emotion-20"
                      >
                        <a
                          breakpoint="desktop"
                          class="emotion-21"
                          href="/Mens Sale"
                          target="_self"
                        >
                          Mens Sale
                        </a>
                      </li>
                      <li
                        class="emotion-20"
                      >
                        <a
                          breakpoint="desktop"
                          class="emotion-21"
                          href="/Womens Sale"
                          target="_self"
                        >
                          Womens Sale
                        </a>
                      </li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`SpotlightVariableHeightVideo - GAP should match snapshots matches snapshots for mobile 1`] = `
.emotion-0 {
  max-width: 100%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  margin: auto;
}

.emotion-1 {
  background: rgb(203, 214, 230);
  width: 100%;
}

.emotion-2 {
  padding: 6.4vw 4.533333333333333vw 8.533333333333333vw;
  width: 100%;
  padding-inline: 5vw;
  padding-bottom: 30px!important;
  gap: 30px;
}

.emotion-3 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-align-items: flex-start;
  -webkit-box-align: flex-start;
  -ms-flex-align: flex-start;
  align-items: flex-start;
}

.emotion-4 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  text-align: left;
  width: 100%;
}

.emotion-5 .amp-cms--p {
  padding: 0;
  margin: 0;
  line-height: 0;
}

.emotion-5 .amp-cms--p>span {
  display: inline;
  white-space: break-spaces;
}

.emotion-5 .amp-cms--p a {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  position: relative;
  z-index: 2;
  pointer-events: auto;
}

.emotion-5 .amp-cms--p a:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-5 .amp-cms--legal-copy {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(2.666666666666667vw, 38.4px));
  line-height: 1.4;
  letter-spacing: 0;
}

.emotion-5 sup {
  vertical-align: top;
  display: inline-block;
  margin-top: -0.25ex;
}

.emotion-5 sub {
  vertical-align: bottom;
  display: inline-block;
  margin-bottom: -0.25ex;
}

.emotion-5 .amp-cms--f-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(2.933333333333333vw, 42.239999999999995px);
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-5 .amp-cms--f-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(2.933333333333333vw, 42.239999999999995px);
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-5 .amp-cms--f-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(2.933333333333333vw, 42.239999999999995px);
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-5 .amp-cms--fn-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(2.933333333333333vw, 42.239999999999995px);
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-5 .amp-cms--body-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(4.8vw, 69.12px));
  line-height: 1.2777777777777777;
  letter-spacing: 0;
}

.emotion-5 .amp-cms--body-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(4.266666666666667vw, 61.44px));
  line-height: 1.25;
  letter-spacing: 0;
}

.emotion-5 .amp-cms--body-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(3.733333333333334vw, 53.76px));
  line-height: 1.2857142857142858;
  letter-spacing: 0;
}

.emotion-5 .amp-cms--body-4 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(3.2vw, 46.08px));
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-5 .amp-cms--body-5 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(2.666666666666667vw, 38.4px));
  line-height: 1.4;
  letter-spacing: 0;
}

.emotion-5 .amp-cms--eyebrow-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(8vw, 115.19999999999999px));
  line-height: 1;
  letter-spacing: 0;
}

.emotion-5 .amp-cms--eyebrow-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(6.933333333333333vw, 99.84px));
  line-height: 1;
  letter-spacing: 0;
}

.emotion-5 .amp-cms--eyebrow-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(6.4vw, 92.16px));
  line-height: 1;
  letter-spacing: 0;
}

.emotion-5 .amp-cms--headline-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, min(16vw, 230.39999999999998px));
  line-height: 1;
  letter-spacing: 0;
}

.emotion-5 .amp-cms--headline-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, min(14.666666666666666vw, 211.2px));
  line-height: 1;
  letter-spacing: 0;
}

.emotion-5 .amp-cms--headline-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, min(14.666666666666666vw, 211.2px));
  line-height: 1;
  letter-spacing: 0;
}

.emotion-5 .amp-cms--headline-4 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, min(13.333333333333334vw, 192px));
  line-height: 1.1;
  letter-spacing: 0;
}

.emotion-5 .amp-cms--headline-5 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, min(12vw, 172.79999999999998px));
  line-height: 1;
  letter-spacing: 0;
}

.emotion-5 .amp-cms--headline-6 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, min(10.666666666666668vw, 153.6px));
  line-height: 1;
  letter-spacing: 0;
}

.emotion-5 .amp-cms--headline-7 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, min(9.333333333333334vw, 134.4px));
  line-height: 1;
  letter-spacing: 0;
}

.emotion-5 .amp-cms--headlineAlt-1 {
  color: #2B2B2B;
  font-size: max(13px, min(17.066666666666666vw, 245.76px));
  line-height: 1;
  letter-spacing: min(-0.07999999999999999vw, -1.152px);
  font-weight: 400;
}

.emotion-5 .amp-cms--headlineAlt-2 {
  color: #2B2B2B;
  font-size: max(13px, min(14.399999999999999vw, 207.35999999999999px));
  line-height: 1;
  letter-spacing: min(-0.07999999999999999vw, -1.152px);
  font-weight: 400;
}

.emotion-5 .amp-cms--headlineAlt-3 {
  color: #2B2B2B;
  font-size: max(13px, min(9.066666666666666vw, 130.56px));
  line-height: 1;
  letter-spacing: min(-0.07999999999999999vw, -1.152px);
  font-weight: 400;
}

.emotion-5 .amp-cms--headlineAlt-4 {
  color: #2B2B2B;
  font-size: max(13px, min(6.4vw, 92.16px));
  line-height: 1.4166666666666667;
  letter-spacing: min(-0.07999999999999999vw, -1.152px);
  font-weight: 400;
}

.emotion-5 .amp-cms--headlineAlt-5 {
  color: #2B2B2B;
  font-size: min(3.733333333333334vw, 53.76px);
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-5 .amp-cms--headlineAlt-6 {
  color: #2B2B2B;
  font-size: min(3.733333333333334vw, 53.76px);
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-5 .amp-cms--headlineAlt-7 {
  color: #2B2B2B;
  font-size: min(3.733333333333334vw, 53.76px);
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-5 .amp-cms--promo-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(13px, min(16vw, 230.39999999999998px));
  line-height: 1;
  letter-spacing: min(0vw, 0px);
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-5 .amp-cms--promo-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(13px, min(10.666666666666668vw, 153.6px));
  line-height: 1;
  letter-spacing: min(0vw, 0px);
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-5 .amp-cms--promoAlt-1 {
  color: #2B2B2B;
  font-size: min(3.733333333333334vw, 53.76px);
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 600;
}

.emotion-5 .amp-cms--subhead-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, min(5.866666666666666vw, 84.47999999999999px));
  line-height: 1.0909090909090908;
  letter-spacing: 0;
}

.emotion-5 .amp-cms--subhead-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, min(5.333333333333334vw, 76.8px));
  line-height: 1.1;
  letter-spacing: 0;
}

.emotion-5 .amp-cms--subhead-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, min(4.8vw, 69.12px));
  line-height: 1.1111111111111112;
  letter-spacing: 0;
}

.emotion-7 {
  background: transparent;
  position: relative;
  width: 100%;
  height: 100%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
}

.emotion-8 {
  position: absolute;
  width: 100%;
  height: 100%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  pointer-events: none;
  gap: 3.2vw;
}

.emotion-9 {
  width: 100%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  pointer-events: none;
  gap: 3.2vw;
  background: linear-gradient(180deg, rgba(0, 0, 0, 0) -0.33%, rgba(0, 0, 0, 0.4) 43.94%, rgba(0, 0, 0, 0) 88.73%);
  background-blend-mode: darken;
}

.emotion-10 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  width: 100%;
  padding: 6.4vw 4.533333333333333vw;
  gap: 30px;
  pointer-events: none;
  padding-inline: 5vw;
  padding-bottom: 30px!important;
}

.emotion-11 {
  text-align: center;
  z-index: 1;
}

.emotion-11 .amp-cms--p {
  padding: 0;
  margin: 0;
  line-height: 0;
}

.emotion-11 .amp-cms--p>span {
  display: inline;
  white-space: break-spaces;
}

.emotion-11 .amp-cms--p a {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  position: relative;
  z-index: 2;
  pointer-events: auto;
}

.emotion-11 .amp-cms--p a:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-11 .amp-cms--legal-copy {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(2.666666666666667vw, 38.4px));
  line-height: 1.4;
  letter-spacing: 0;
}

.emotion-11 sup {
  vertical-align: top;
  display: inline-block;
  margin-top: -0.25ex;
}

.emotion-11 sub {
  vertical-align: bottom;
  display: inline-block;
  margin-bottom: -0.25ex;
}

.emotion-11 .amp-cms--f-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(2.933333333333333vw, 42.239999999999995px);
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-11 .amp-cms--f-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(2.933333333333333vw, 42.239999999999995px);
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-11 .amp-cms--f-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(2.933333333333333vw, 42.239999999999995px);
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-11 .amp-cms--fn-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(2.933333333333333vw, 42.239999999999995px);
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-11 .amp-cms--body-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(4.8vw, 69.12px));
  line-height: 1.2777777777777777;
  letter-spacing: 0;
}

.emotion-11 .amp-cms--body-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(4.266666666666667vw, 61.44px));
  line-height: 1.25;
  letter-spacing: 0;
}

.emotion-11 .amp-cms--body-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(3.733333333333334vw, 53.76px));
  line-height: 1.2857142857142858;
  letter-spacing: 0;
}

.emotion-11 .amp-cms--body-4 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(3.2vw, 46.08px));
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-11 .amp-cms--body-5 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(2.666666666666667vw, 38.4px));
  line-height: 1.4;
  letter-spacing: 0;
}

.emotion-11 .amp-cms--eyebrow-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(8vw, 115.19999999999999px));
  line-height: 1;
  letter-spacing: 0;
}

.emotion-11 .amp-cms--eyebrow-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(6.933333333333333vw, 99.84px));
  line-height: 1;
  letter-spacing: 0;
}

.emotion-11 .amp-cms--eyebrow-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(6.4vw, 92.16px));
  line-height: 1;
  letter-spacing: 0;
}

.emotion-11 .amp-cms--headline-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, min(16vw, 230.39999999999998px));
  line-height: 1;
  letter-spacing: 0;
}

.emotion-11 .amp-cms--headline-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, min(14.666666666666666vw, 211.2px));
  line-height: 1;
  letter-spacing: 0;
}

.emotion-11 .amp-cms--headline-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, min(14.666666666666666vw, 211.2px));
  line-height: 1;
  letter-spacing: 0;
}

.emotion-11 .amp-cms--headline-4 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, min(13.333333333333334vw, 192px));
  line-height: 1.1;
  letter-spacing: 0;
}

.emotion-11 .amp-cms--headline-5 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, min(12vw, 172.79999999999998px));
  line-height: 1;
  letter-spacing: 0;
}

.emotion-11 .amp-cms--headline-6 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, min(10.666666666666668vw, 153.6px));
  line-height: 1;
  letter-spacing: 0;
}

.emotion-11 .amp-cms--headline-7 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, min(9.333333333333334vw, 134.4px));
  line-height: 1;
  letter-spacing: 0;
}

.emotion-11 .amp-cms--headlineAlt-1 {
  color: #2B2B2B;
  font-size: max(13px, min(17.066666666666666vw, 245.76px));
  line-height: 1;
  letter-spacing: min(-0.07999999999999999vw, -1.152px);
  font-weight: 400;
}

.emotion-11 .amp-cms--headlineAlt-2 {
  color: #2B2B2B;
  font-size: max(13px, min(14.399999999999999vw, 207.35999999999999px));
  line-height: 1;
  letter-spacing: min(-0.07999999999999999vw, -1.152px);
  font-weight: 400;
}

.emotion-11 .amp-cms--headlineAlt-3 {
  color: #2B2B2B;
  font-size: max(13px, min(9.066666666666666vw, 130.56px));
  line-height: 1;
  letter-spacing: min(-0.07999999999999999vw, -1.152px);
  font-weight: 400;
}

.emotion-11 .amp-cms--headlineAlt-4 {
  color: #2B2B2B;
  font-size: max(13px, min(6.4vw, 92.16px));
  line-height: 1.4166666666666667;
  letter-spacing: min(-0.07999999999999999vw, -1.152px);
  font-weight: 400;
}

.emotion-11 .amp-cms--headlineAlt-5 {
  color: #2B2B2B;
  font-size: min(3.733333333333334vw, 53.76px);
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-11 .amp-cms--headlineAlt-6 {
  color: #2B2B2B;
  font-size: min(3.733333333333334vw, 53.76px);
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-11 .amp-cms--headlineAlt-7 {
  color: #2B2B2B;
  font-size: min(3.733333333333334vw, 53.76px);
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-11 .amp-cms--promo-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(13px, min(16vw, 230.39999999999998px));
  line-height: 1;
  letter-spacing: min(0vw, 0px);
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-11 .amp-cms--promo-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(13px, min(10.666666666666668vw, 153.6px));
  line-height: 1;
  letter-spacing: min(0vw, 0px);
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-11 .amp-cms--promoAlt-1 {
  color: #2B2B2B;
  font-size: min(3.733333333333334vw, 53.76px);
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 600;
}

.emotion-11 .amp-cms--subhead-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, min(5.866666666666666vw, 84.47999999999999px));
  line-height: 1.0909090909090908;
  letter-spacing: 0;
}

.emotion-11 .amp-cms--subhead-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, min(5.333333333333334vw, 76.8px));
  line-height: 1.1;
  letter-spacing: 0;
}

.emotion-11 .amp-cms--subhead-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, min(4.8vw, 69.12px));
  line-height: 1.1111111111111112;
  letter-spacing: 0;
}

.emotion-12 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  height: auto;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  gap: 15px 20px;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  -webkit-box-flex-wrap: nowrap;
  -webkit-flex-wrap: nowrap;
  -ms-flex-wrap: nowrap;
  flex-wrap: nowrap;
}

.emotion-12 * ul {
  position: absolute;
  max-height: 242px;
  overflow: scroll;
}

@supports selector (::-webkit-scrollbar) {
  .emotion-12 * ul::-webkit-scrollbar {
    -webkit-appearance: none;
  }

  .emotion-12 * ul::-webkit-scrollbar:vertical {
    width: 11px;
    background-color: #FFFFFF;
  }

  .emotion-12 * ul::-webkit-scrollbar:horizontal {
    height: 0;
  }

  .emotion-12 * ul::-webkit-scrollbar-corner {
    background-color: #FFFFFF;
  }

  .emotion-12 * ul::-webkit-scrollbar-thumb {
    border-radius: 8px;
    border: 2px solid #FFF;
    background-color: rgba(0, 0, 0, .5);
  }
}

.emotion-12 ul {
  width: -webkit-fit-content!important;
  width: -moz-fit-content!important;
  width: fit-content!important;
  max-height: none;
  overflow: initial;
}

.emotion-12 span[data-id="cta-dropdown-label"] {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  justify-content: space-between;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

.emotion-12 span[data-id="cta-dropdown-label"]>span {
  position: relative;
  -webkit-transform: none;
  -moz-transform: none;
  -ms-transform: none;
  transform: none;
}

.emotion-12 button {
  width: auto;
  font-size: 14px;
}

.emotion-12 a {
  font-size: 14px;
  margin-left: 1px;
}

.emotion-13 {
  position: relative;
}

.emotion-14 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  display: inline-block;
  text-transform: none;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 1px solid transparent;
  border-radius: 0;
  font-size: 18px;
  font-weight: 500;
  letter-spacing: 2%;
  min-height: auto;
  max-height: auto;
  line-height: 1.0833333333333333;
  padding: 0;
  width: 100%;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: left;
  border-left: none;
  border-right: none;
  border-radius: 0;
  background-color: transparent;
  border-bottom: 0;
  -webkit-box-pack: left;
  -ms-flex-pack: left;
  -webkit-justify-content: left;
  justify-content: left;
  color: #000000;
  height: auto;
  -webkit-text-decoration: underline;
  text-decoration: underline;
  text-underline-offset: 5px;
  text-decoration-thickness: 2.3px;
  position: relative;
  z-index: 2;
  pointer-events: auto;
  text-align: left;
  line-height: 1;
  padding: 0;
  border: 0;
  text-underline-offset: 5px;
}

.emotion-14:focus {
  outline: none;
}

.emotion-14>span {
  padding: 1px 0;
}

.emotion-14>* {
  width: inherit;
  -webkit-text-decoration: inherit;
  text-decoration: inherit;
}

.emotion-14>span {
  padding: 0;
}

.emotion-15 {
  box-sizing: border-box;
}

.emotion-16 {
  display: inline-block;
  text-transform: none;
  position: relative;
  width: 100%;
  -webkit-text-decoration: inherit;
  text-decoration: inherit;
}

.emotion-17 {
  display: inline-block;
  height: 10px;
  width: 10px;
  min-height: 10px;
  min-width: 10px;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  position: absolute;
  right: 0;
  top: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  margin-left: 8px;
}

.emotion-17 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-17 svg path {
  fill: currentColor;
}

.emotion-17 svg rect {
  fill: currentColor;
}

.emotion-19 {
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
  background: #FFFFFF;
  box-sizing: border-box;
  list-style-type: none;
  min-width: 100%;
  overflow: hidden;
  position: relative;
  z-index: 390;
  border-width: 0;
  border-style: solid;
  text-align: left;
  letter-spacing: 0;
  pointer-events: auto;
  padding: 0px;
  width: -webkit-fit-content!important;
  width: -moz-fit-content!important;
  width: fit-content!important;
  max-height: 0;
  width: 100%;
  -webkit-transition: max-height .5s ease-in-out,visibility .5s;
  transition: max-height .5s ease-in-out,visibility .5s;
  visibility: hidden;
  margin-top: 12px;
  color: #000000;
  background-color: #FFFFFF;
}

.emotion-19 :hover {
  color: #FFFFFF;
  background-color: #000000;
}

.emotion-20 {
  box-sizing: border-box;
  width: auto;
  border-bottom: none;
  letter-spacing: 1px;
  margin-left: 0;
  text-transform: none;
  padding: 12px;
  border-color: #000000;
}

.emotion-20:last-child {
  border: none;
}

.emotion-21 {
  cursor: pointer;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  padding: 0px;
  -webkit-text-decoration: none;
  text-decoration: none;
  -webkit-transition: color 0.3s ease;
  transition: color 0.3s ease;
  white-space: nowrap;
  font-size: 18px;
  text-align: center;
  color: inherit;
  -webkit-box-pack: start;
  -ms-flex-pack: start;
  -webkit-justify-content: start;
  justify-content: start;
  background-color: inherit;
  font-weight: 400;
  letter-spacing: 1px;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-74 {
  position: relative;
  height: 100%;
  pointer-events: auto;
  aspect-ratio: 390/530;
}

.emotion-75 {
  width: -webkit-fit-content;
  width: -moz-fit-content;
  width: fit-content;
  position: relative;
  width: 100%;
}

.emotion-76>button {
  right: 5vw;
  bottom: 57px;
  height: 24px;
  width: 24px;
  z-index: 4;
}

.emotion-76>div>button {
  right: calc(5vw + 15px + 24px);
  bottom: 57px;
  height: 24px;
  width: 24px;
  z-index: 4;
}

.emotion-76>div>div {
  right: calc(5vw + 15px + 24px);
  bottom: 57px;
  z-index: 3;
  margin-bottom: 24px;
}

.emotion-77 {
  background: none;
  border: none;
  padding: 0;
  height: 24px;
  margin: 0;
  position: absolute;
  bottom: 8px;
  right: 112px;
  width: 24px;
  height: 24px;
}

.emotion-77:is(:not(:focus, :active, :hover, :focus-within)) {
  opacity: 0.64;
}

.emotion-77:is(:hover) {
  opacity: 1;
}

@media (min-width: 767px) {
  .emotion-77 {
    right: 120px;
  }
}

.emotion-78 {
  box-sizing: border-box;
  background: none;
  border: none;
  padding: 0;
  position: relative;
  width: 100%;
  height: 0px;
}

.emotion-78:focus div {
  opacity: 0.84;
  -webkit-transition: opacity 200ms;
  transition: opacity 200ms;
  border-radius: 20px 20px 0 0;
}

.emotion-78 div {
  border-radius: 20px 20px 0 0;
}

.emotion-78 .keepOpen,
.emotion-78 .staysOpen {
  opacity: 0.84;
  -webkit-transition: opacity 200ms;
  transition: opacity 200ms;
  z-index: 2;
}

.emotion-79 {
  background: none;
  border: none;
  padding: 0;
  height: 24px;
  margin: 0;
  position: absolute;
  bottom: 8px;
  right: 72px;
  z-index: 11;
}

.emotion-79:is(:not(:focus, :active, :hover, :focus-within)) {
  opacity: 0.64;
}

.emotion-79:is(:hover) {
  opacity: 1;
}

@media (min-width: 767px) {
  .emotion-79 {
    right: 80px;
  }
}

.emotion-80 {
  opacity: 0;
  -webkit-transition: opacity 200ms;
  transition: opacity 200ms;
  box-sizing: border-box;
  background: #ffffff;
  border: none;
  padding: 0;
  position: absolute;
  width: 24px;
  height: 60px;
  bottom: 32px;
  right: 72px;
  border-radius: 20px 20px 0 0;
}

.emotion-80:before {
  content: "";
  height: 24px;
  background: #ffffff;
  display: block;
  position: absolute;
  bottom: -24px;
  width: 24px;
  border-bottom-right-radius: 50%;
  border-bottom-left-radius: 50%;
}

.emotion-81 {
  height: 100%;
}

.emotion-82 {
  box-sizing: border-box;
  width: 100%;
  position: absolute;
  bottom: 0;
  pointer-events: none;
  padding: 0 5vw 30px;
  background: linear-gradient(0deg, rgba(0, 0, 0, 0.4) 50%, rgba(0, 0, 0, 0) 100%);
  display: grid;
  grid-template-columns: auto auto;
  grid-template-rows: auto auto;
  grid-template-areas: 'top-left top-right' 'bottom-left bottom-right';
  grid-row-gap: 10px;
}

.emotion-83 {
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  grid-area: bottom-left;
}

.emotion-84 {
  pointer-events: auto;
}

.emotion-84 .amp-cms--p {
  padding: 0;
  margin: 0;
  line-height: 0;
}

.emotion-84 .amp-cms--p>span {
  display: inline;
  white-space: break-spaces;
}

.emotion-84 .amp-cms--p a {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  position: relative;
  z-index: 2;
  pointer-events: auto;
}

.emotion-84 .amp-cms--p a:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-84 .amp-cms--legal-copy {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(2.666666666666667vw, 38.4px));
  line-height: 1.4;
  letter-spacing: 0;
}

.emotion-84 sup {
  vertical-align: top;
  display: inline-block;
  margin-top: -0.25ex;
}

.emotion-84 sub {
  vertical-align: bottom;
  display: inline-block;
  margin-bottom: -0.25ex;
}

.emotion-84 .amp-cms--f-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(2.933333333333333vw, 42.239999999999995px);
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-84 .amp-cms--f-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(2.933333333333333vw, 42.239999999999995px);
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-84 .amp-cms--f-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(2.933333333333333vw, 42.239999999999995px);
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-84 .amp-cms--fn-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(2.933333333333333vw, 42.239999999999995px);
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-84 .amp-cms--body-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(4.8vw, 69.12px));
  line-height: 1.2777777777777777;
  letter-spacing: 0;
}

.emotion-84 .amp-cms--body-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(4.266666666666667vw, 61.44px));
  line-height: 1.25;
  letter-spacing: 0;
}

.emotion-84 .amp-cms--body-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(3.733333333333334vw, 53.76px));
  line-height: 1.2857142857142858;
  letter-spacing: 0;
}

.emotion-84 .amp-cms--body-4 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(3.2vw, 46.08px));
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-84 .amp-cms--body-5 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(2.666666666666667vw, 38.4px));
  line-height: 1.4;
  letter-spacing: 0;
}

.emotion-84 .amp-cms--eyebrow-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(8vw, 115.19999999999999px));
  line-height: 1;
  letter-spacing: 0;
}

.emotion-84 .amp-cms--eyebrow-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(6.933333333333333vw, 99.84px));
  line-height: 1;
  letter-spacing: 0;
}

.emotion-84 .amp-cms--eyebrow-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(6.4vw, 92.16px));
  line-height: 1;
  letter-spacing: 0;
}

.emotion-84 .amp-cms--headline-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, min(16vw, 230.39999999999998px));
  line-height: 1;
  letter-spacing: 0;
}

.emotion-84 .amp-cms--headline-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, min(14.666666666666666vw, 211.2px));
  line-height: 1;
  letter-spacing: 0;
}

.emotion-84 .amp-cms--headline-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, min(14.666666666666666vw, 211.2px));
  line-height: 1;
  letter-spacing: 0;
}

.emotion-84 .amp-cms--headline-4 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, min(13.333333333333334vw, 192px));
  line-height: 1.1;
  letter-spacing: 0;
}

.emotion-84 .amp-cms--headline-5 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, min(12vw, 172.79999999999998px));
  line-height: 1;
  letter-spacing: 0;
}

.emotion-84 .amp-cms--headline-6 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, min(10.666666666666668vw, 153.6px));
  line-height: 1;
  letter-spacing: 0;
}

.emotion-84 .amp-cms--headline-7 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, min(9.333333333333334vw, 134.4px));
  line-height: 1;
  letter-spacing: 0;
}

.emotion-84 .amp-cms--headlineAlt-1 {
  color: #2B2B2B;
  font-size: max(13px, min(17.066666666666666vw, 245.76px));
  line-height: 1;
  letter-spacing: min(-0.07999999999999999vw, -1.152px);
  font-weight: 400;
}

.emotion-84 .amp-cms--headlineAlt-2 {
  color: #2B2B2B;
  font-size: max(13px, min(14.399999999999999vw, 207.35999999999999px));
  line-height: 1;
  letter-spacing: min(-0.07999999999999999vw, -1.152px);
  font-weight: 400;
}

.emotion-84 .amp-cms--headlineAlt-3 {
  color: #2B2B2B;
  font-size: max(13px, min(9.066666666666666vw, 130.56px));
  line-height: 1;
  letter-spacing: min(-0.07999999999999999vw, -1.152px);
  font-weight: 400;
}

.emotion-84 .amp-cms--headlineAlt-4 {
  color: #2B2B2B;
  font-size: max(13px, min(6.4vw, 92.16px));
  line-height: 1.4166666666666667;
  letter-spacing: min(-0.07999999999999999vw, -1.152px);
  font-weight: 400;
}

.emotion-84 .amp-cms--headlineAlt-5 {
  color: #2B2B2B;
  font-size: min(3.733333333333334vw, 53.76px);
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-84 .amp-cms--headlineAlt-6 {
  color: #2B2B2B;
  font-size: min(3.733333333333334vw, 53.76px);
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-84 .amp-cms--headlineAlt-7 {
  color: #2B2B2B;
  font-size: min(3.733333333333334vw, 53.76px);
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-84 .amp-cms--promo-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(13px, min(16vw, 230.39999999999998px));
  line-height: 1;
  letter-spacing: min(0vw, 0px);
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-84 .amp-cms--promo-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(13px, min(10.666666666666668vw, 153.6px));
  line-height: 1;
  letter-spacing: min(0vw, 0px);
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-84 .amp-cms--promoAlt-1 {
  color: #2B2B2B;
  font-size: min(3.733333333333334vw, 53.76px);
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 600;
}

.emotion-84 .amp-cms--subhead-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, min(5.866666666666666vw, 84.47999999999999px));
  line-height: 1.0909090909090908;
  letter-spacing: 0;
}

.emotion-84 .amp-cms--subhead-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, min(5.333333333333334vw, 76.8px));
  line-height: 1.1;
  letter-spacing: 0;
}

.emotion-84 .amp-cms--subhead-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, min(4.8vw, 69.12px));
  line-height: 1.1111111111111112;
  letter-spacing: 0;
}

.emotion-85 {
  grid-area: bottom-right;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: end;
  -ms-flex-pack: end;
  -webkit-justify-content: end;
  justify-content: end;
}

.emotion-86 {
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  padding: 0;
  padding: 0;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: end;
  -webkit-box-align: end;
  -ms-flex-align: end;
  align-items: end;
}

.emotion-87 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 10px;
  line-height: 1.4;
  letter-spacing: 0;
  text-transform: none;
  font-weight: normal;
  min-height: auto;
  color: #FFFFFF;
  font-size: 11px;
  padding: 0;
  line-height: normal;
}

.emotion-88 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  display: inline-block;
  text-transform: none;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 1px solid transparent;
  border-radius: 0;
  font-size: 12px;
  font-weight: 500;
  letter-spacing: 2%;
  min-height: auto;
  max-height: auto;
  line-height: 1.125;
  padding: 0;
  width: auto;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: left;
  border-radius: 0;
  border: none;
  background-color: transparent;
  color: #000000;
  height: auto;
  -webkit-box-pack: left;
  -ms-flex-pack: left;
  -webkit-justify-content: left;
  justify-content: left;
  font-weight: normal;
  text-transform: none;
  z-index: 1;
  color: #FFFFFF;
  -webkit-text-decoration: underline;
  text-decoration: underline;
  font-size: 11px;
  min-height: auto;
  padding: 0;
  line-height: normal;
  margin-left: 8px;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: end;
  -webkit-box-align: end;
  -ms-flex-align: end;
  align-items: end;
  pointer-events: auto;
}

.emotion-88:focus {
  outline: none;
}

.emotion-88>span {
  padding: 1px 0;
}

.emotion-88 span svg path {
  -webkit-transition: fill 250ms ease-in-out;
  transition: fill 250ms ease-in-out;
  fill: #000000;
}

.emotion-88 span span {
  padding-left: initial;
}

.emotion-88:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-88:focus-visible {
  outline: auto;
}

.emotion-89 {
  cursor: pointer;
  position: absolute;
  height: calc(100% - 16px);
  width: 100%;
  top: 0px;
  left: 0px;
}

.emotion-90 {
  background: -webkit-linear-gradient(45deg, #DDDDFF,#3333FF);
  width: 100%;
}

.emotion-92 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-align-items: flex-start;
  -webkit-box-align: flex-start;
  -ms-flex-align: flex-start;
  align-items: flex-start;
  gap: 6.4vw;
}

.emotion-96 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  height: auto;
  -webkit-box-pack: start;
  -ms-flex-pack: start;
  -webkit-justify-content: flex-start;
  justify-content: flex-start;
  gap: 12px;
  -webkit-align-items: normal;
  -webkit-box-align: normal;
  -ms-flex-align: normal;
  align-items: normal;
}

.emotion-103 {
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
  background: #FFFFFF;
  box-sizing: border-box;
  list-style-type: none;
  min-width: 100%;
  overflow: hidden;
  position: relative;
  z-index: 390;
  border-width: 0;
  border-style: solid;
  text-align: left;
  letter-spacing: 0;
  pointer-events: auto;
  padding: 0px;
  max-height: 0;
  width: 100%;
  -webkit-transition: max-height .5s ease-in-out,visibility .5s;
  transition: max-height .5s ease-in-out,visibility .5s;
  visibility: hidden;
  margin-top: 12px;
  color: #000000;
  background-color: #FFFFFF;
}

.emotion-103 :hover {
  color: #FFFFFF;
  background-color: #000000;
}

<div>
  <div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      class="emotion-0"
    >
      <div
        class="emotion-1"
        height="0"
        width="0"
      >
        <div
          class="emotion-2"
        >
          <div
            class="emotion-3"
          >
            <div
              class="emotion-4"
            >
              <div
                class="emotion-5"
              >
                <div>
                  <p
                    class="amp-cms--p"
                  >
                    <span
                      class="amp-cms--headline-1"
                    >
                      Above section
                    </span>
                  </p>
                  <p
                    class="amp-cms--p"
                  >
                    <span
                      class="amp-cms--body-1"
                    >
                      Above section body
                    </span>
                  </p>
                </div>
              </div>
            </div>
            <div
              class="emotion-4"
            />
          </div>
        </div>
      </div>
      <div
        class="emotion-7"
        height="0"
        width="0"
      >
        <div
          class="emotion-8"
        >
          <div
            class="emotion-9"
          >
            <div
              class="emotion-10"
            >
              <div
                class="emotion-11"
              >
                <div>
                  <p
                    class="amp-cms--p"
                  >
                    <span
                      class="amp-cms--headline-4"
                      style="color:#FFFFFF"
                    >
                      Video Text
                    </span>
                  </p>
                </div>
              </div>
              <div
                class="emotion-12"
              >
                <div
                  class="emotion-13"
                  data-testid="ctaDropdownWrapper"
                >
                  <button
                    aria-expanded="false"
                    class="emotion-14"
                    color="dark"
                  >
                    <span
                      class="emotion-15"
                    >
                      <span
                        class="emotion-16"
                        data-id="cta-dropdown-label"
                      >
                        Shop All
                        <span
                          aria-hidden="true"
                          class="emotion-17"
                        >
                          <svg
                            fill="none"
                            viewBox="0 0 12 12"
                            xmlns="http://www.w3.org/2000/svg"
                          >
                            <path
                              clip-rule="evenodd"
                              d="M7 0H5v5H0v2h5v5h2V7h5V5H7V0z"
                              fill="#000000"
                              fill-rule="evenodd"
                            />
                          </svg>
                        </span>
                      </span>
                    </span>
                  </button>
                  <div
                    class="emotion-18"
                  >
                    <ul
                      aria-hidden="true"
                      class="emotion-19"
                    >
                      <li
                        class="emotion-20"
                      >
                        <a
                          breakpoint="mobile"
                          class="emotion-21"
                          href="/Women"
                          target="_self"
                        >
                          Women
                        </a>
                      </li>
                      <li
                        class="emotion-20"
                      >
                        <a
                          breakpoint="mobile"
                          class="emotion-21"
                          href="/Men"
                          target="_self"
                        >
                          Men
                        </a>
                      </li>
                      <li
                        class="emotion-20"
                      >
                        <a
                          breakpoint="mobile"
                          class="emotion-21"
                          href="/Girls"
                          target="_self"
                        >
                          Girls
                        </a>
                      </li>
                      <li
                        class="emotion-20"
                      >
                        <a
                          breakpoint="mobile"
                          class="emotion-21"
                          href="/Boys"
                          target="_self"
                        >
                          Boys
                        </a>
                      </li>
                      <li
                        class="emotion-20"
                      >
                        <a
                          breakpoint="mobile"
                          class="emotion-21"
                          href="/Toddler Girls"
                          target="_self"
                        >
                          Toddler Girls
                        </a>
                      </li>
                      <li
                        class="emotion-20"
                      >
                        <a
                          breakpoint="mobile"
                          class="emotion-21"
                          href="/Toddler Boys"
                          target="_self"
                        >
                          Toddler Boys
                        </a>
                      </li>
                      <li
                        class="emotion-20"
                      >
                        <a
                          breakpoint="mobile"
                          class="emotion-21"
                          href="/Baby Girls"
                          target="_self"
                        >
                          Baby Girls
                        </a>
                      </li>
                      <li
                        class="emotion-20"
                      >
                        <a
                          breakpoint="mobile"
                          class="emotion-21"
                          href="/Baby Boys"
                          target="_self"
                        >
                          Baby Boys
                        </a>
                      </li>
                    </ul>
                  </div>
                </div>
                <div
                  class="emotion-13"
                  data-testid="ctaDropdownWrapper"
                >
                  <button
                    aria-expanded="false"
                    class="emotion-14"
                    color="dark"
                  >
                    <span
                      class="emotion-15"
                    >
                      <span
                        class="emotion-16"
                        data-id="cta-dropdown-label"
                      >
                        Shop Pants
                        <span
                          aria-hidden="true"
                          class="emotion-17"
                        >
                          <svg
                            fill="none"
                            viewBox="0 0 12 12"
                            xmlns="http://www.w3.org/2000/svg"
                          >
                            <path
                              clip-rule="evenodd"
                              d="M7 0H5v5H0v2h5v5h2V7h5V5H7V0z"
                              fill="#000000"
                              fill-rule="evenodd"
                            />
                          </svg>
                        </span>
                      </span>
                    </span>
                  </button>
                  <div
                    class="emotion-18"
                  >
                    <ul
                      aria-hidden="true"
                      class="emotion-19"
                    >
                      <li
                        class="emotion-20"
                      >
                        <a
                          breakpoint="mobile"
                          class="emotion-21"
                          href="/jeans"
                          target="_self"
                        >
                          Jeans
                        </a>
                      </li>
                      <li
                        class="emotion-20"
                      >
                        <a
                          breakpoint="mobile"
                          class="emotion-21"
                          href="/khakis"
                          target="_self"
                        >
                          Khakis
                        </a>
                      </li>
                      <li
                        class="emotion-20"
                      >
                        <a
                          breakpoint="mobile"
                          class="emotion-21"
                          href="/sweatpants"
                          target="_self"
                        >
                          Sweatpants Sweatpants Sweatpants Sweatpants Sweatpants Sweatpants Sweatpants
                        </a>
                      </li>
                    </ul>
                  </div>
                </div>
                <div
                  class="emotion-13"
                  data-testid="ctaDropdownWrapper"
                >
                  <button
                    aria-expanded="false"
                    class="emotion-14"
                    color="dark"
                  >
                    <span
                      class="emotion-15"
                    >
                      <span
                        class="emotion-16"
                        data-id="cta-dropdown-label"
                      >
                        Shop New
                        <span
                          aria-hidden="true"
                          class="emotion-17"
                        >
                          <svg
                            fill="none"
                            viewBox="0 0 12 12"
                            xmlns="http://www.w3.org/2000/svg"
                          >
                            <path
                              clip-rule="evenodd"
                              d="M7 0H5v5H0v2h5v5h2V7h5V5H7V0z"
                              fill="#000000"
                              fill-rule="evenodd"
                            />
                          </svg>
                        </span>
                      </span>
                    </span>
                  </button>
                  <div
                    class="emotion-18"
                  >
                    <ul
                      aria-hidden="true"
                      class="emotion-19"
                    >
                      <li
                        class="emotion-20"
                      >
                        <a
                          breakpoint="mobile"
                          class="emotion-21"
                          href="/Women"
                          target="_self"
                        >
                          Women
                        </a>
                      </li>
                      <li
                        class="emotion-20"
                      >
                        <a
                          breakpoint="mobile"
                          class="emotion-21"
                          href="/Men"
                          target="_self"
                        >
                          Men
                        </a>
                      </li>
                      <li
                        class="emotion-20"
                      >
                        <a
                          breakpoint="mobile"
                          class="emotion-21"
                          href="/Girls"
                          target="_self"
                        >
                          Girls
                        </a>
                      </li>
                      <li
                        class="emotion-20"
                      >
                        <a
                          breakpoint="mobile"
                          class="emotion-21"
                          href="/Boys"
                          target="_self"
                        >
                          Boys
                        </a>
                      </li>
                      <li
                        class="emotion-20"
                      >
                        <a
                          breakpoint="mobile"
                          class="emotion-21"
                          href="/Toddler Girls"
                          target="_self"
                        >
                          Toddler Girls
                        </a>
                      </li>
                      <li
                        class="emotion-20"
                      >
                        <a
                          breakpoint="mobile"
                          class="emotion-21"
                          href="/Toddler Boys"
                          target="_self"
                        >
                          Toddler Boys
                        </a>
                      </li>
                      <li
                        class="emotion-20"
                      >
                        <a
                          breakpoint="mobile"
                          class="emotion-21"
                          href="/Baby Girls"
                          target="_self"
                        >
                          Baby Girls
                        </a>
                      </li>
                      <li
                        class="emotion-20"
                      >
                        <a
                          breakpoint="mobile"
                          class="emotion-21"
                          href="/Maternity"
                          target="_self"
                        >
                          Maternity
                        </a>
                      </li>
                      <li
                        class="emotion-20"
                      >
                        <a
                          breakpoint="mobile"
                          class="emotion-21"
                          href="/Shop for the Fam Fam"
                          target="_self"
                        >
                          Shop for the Fam Fam
                        </a>
                      </li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div
          class="emotion-74"
        >
          <div
            class="emotion-75"
            data-testid="videocomponent-container"
          >
            <div
              style="position: relative;"
            >
              <img
                fetchpriority="high"
                src="https://fmzlik0m0z6r1oifp8ex832rf.staging.bigcontent.io/i/oldnavy/230901_14-M5283_LaborDay_CatNav_Tops_HP_US_XL?fmt=auto"
                style="height: 100%; left: 0px; object-fit: cover; position: absolute; top: 0px; width: 100%;"
              />
              <h1
                role="presentation"
              >
                ReactPlayer
              </h1>
            </div>
            <div
              class="player-custom-controls emotion-76"
              data-testid="player-custom-controls"
            >
              <button
                aria-label="Pause"
                class="emotion-77"
              >
                <svg
                  aria-label="pause-button"
                  fill="none"
                  height="24"
                  viewBox="0 0 24 24"
                  width="24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M24 12C24 18.6275 18.6275 24 12 24C5.3725 24 0 18.6275 0 12C0 5.3725 5.3725 0 12 0C18.6275 0 24 5.3725 24 12Z"
                    fill="#FFFFFF"
                  />
                  <path
                    d="M7.5 7.5C7.5 7.10218 7.65804 6.72064 7.93934 6.43934C8.22064 6.15804 8.60218 6 9 6C9.39782 6 9.77936 6.15804 10.0607 6.43934C10.342 6.72064 10.5 7.10218 10.5 7.5V16.5C10.5 16.8978 10.342 17.2794 10.0607 17.5607C9.77936 17.842 9.39782 18 9 18C8.60218 18 8.22064 17.842 7.93934 17.5607C7.65804 17.2794 7.5 16.8978 7.5 16.5V7.5ZM13.5 7.5C13.5 7.10218 13.658 6.72064 13.9393 6.43934C14.2206 6.15804 14.6022 6 15 6C15.3978 6 15.7794 6.15804 16.0607 6.43934C16.342 6.72064 16.5 7.10218 16.5 7.5V16.5C16.5 16.8978 16.342 17.2794 16.0607 17.5607C15.7794 17.842 15.3978 18 15 18C14.6022 18 14.2206 17.842 13.9393 17.5607C13.658 17.2794 13.5 16.8978 13.5 16.5V7.5Z"
                    fill="#2C2824"
                  />
                </svg>
              </button>
              <div
                class="emotion-78"
                data-testid="styled-mute-controls"
              >
                <button
                  aria-label="Mute"
                  aria-pressed="false"
                  class="emotion-79"
                >
                  <svg
                    aria-label="mute-button"
                    fill="none"
                    height="24"
                    viewBox="0 0 24 24"
                    width="24"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M24 12C24 18.6275 18.6275 24 12 24C5.3725 24 0 18.6275 0 12C0 5.3725 5.3725 0 12 0C18.6275 0 24 5.3725 24 12Z"
                      fill="#FFFFFF"
                    />
                    <g>
                      <path
                        clip-rule="evenodd"
                        d="M14.4996 14.2613C15.7486 13.0124 15.7486 10.9876 14.4996 9.73866C14.28 9.51899 14.28 9.16283 14.4996 8.94316C14.7193 8.72349 15.0755 8.72349 15.2951 8.94316C16.9835 10.6315 16.9835 13.3685 15.2951 15.0568C15.0755 15.2765 14.7193 15.2765 14.4996 15.0568C14.28 14.8372 14.28 14.481 14.4996 14.2613Z"
                        fill="#2C2824"
                        fill-rule="evenodd"
                      />
                      <path
                        clip-rule="evenodd"
                        d="M16.4087 16.034C18.6368 13.8059 18.6368 10.1939 16.4087 7.96586C16.1891 7.74619 16.1891 7.39003 16.4087 7.17036C16.6284 6.95069 16.9846 6.95069 17.2042 7.17036C19.8716 9.83778 19.8716 14.1621 17.2042 16.8295C16.9846 17.0492 16.6284 17.0492 16.4087 16.8295C16.1891 16.6098 16.1891 16.2537 16.4087 16.034Z"
                        fill="#2C2824"
                        fill-rule="evenodd"
                      />
                      <path
                        d="M4.7383 12.8103V12V11.1897C4.7383 10.6374 5.18602 10.1897 5.7383 10.1897H7.14083C7.39437 10.1897 7.63844 10.0934 7.82366 9.92023L11.0042 6.94759C11.14 6.82063 11.319 6.75 11.5049 6.75C11.91 6.75 12.2383 7.07834 12.2383 7.48337V12V16.5166C12.2383 16.9217 11.91 17.25 11.5049 17.25C11.319 17.25 11.14 17.1794 11.0042 17.0524L7.82366 14.0798C7.63844 13.9066 7.39437 13.8103 7.14083 13.8103H5.7383C5.18602 13.8103 4.7383 13.3626 4.7383 12.8103Z"
                        fill="#2C2824"
                      />
                      <rect
                        fill="#2C2824"
                        height="19.3235"
                        transform="rotate(120 21.2347 16.4121)"
                        width="1.5"
                        x="21.2347"
                        y="16.4121"
                      />
                    </g>
                  </svg>
                </button>
                <div
                  class="emotion-80"
                  data-testid="styled-input-volume"
                  style="background: rgb(255, 255, 255);"
                >
                  <div
                    role="presentation"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
        <div
          class="emotion-81"
        >
          <div
            class="emotion-82"
          >
            <div
              class="emotion-83"
            >
              <div
                class="emotion-84"
              >
                <div>
                  <p
                    style="color:#FFFFFF"
                  >
                    Handle Text
                  </p>
                </div>
              </div>
            </div>
            <div
              class="emotion-85"
            >
              <div
                class="emotion-86"
              >
                <span
                  class="emotion-87"
                >
                  Prefix Label
                </span>
              </div>
              <button
                class="emotion-88"
              >
                Details Link
              </button>
            </div>
          </div>
        </div>
        <a
          aria-label="Voluptatem voluptas"
          class="emotion-89"
          data-testid="conditional-link"
          href="#3"
          target="_self"
        />
      </div>
      <div
        class="emotion-90"
        height="0"
        width="0"
      >
        <div
          class="emotion-2"
        >
          <div
            class="emotion-92"
          >
            <div
              class="emotion-4"
            >
              <div
                class="emotion-5"
              >
                <div>
                  <p
                    class="amp-cms--p"
                  >
                    <span
                      class="amp-cms--headline-1"
                    >
                      Below section
                    </span>
                  </p>
                  <p
                    class="amp-cms--p"
                  >
                    <span
                      class="amp-cms--body-1"
                    >
                      Below section body
                    </span>
                  </p>
                </div>
              </div>
            </div>
            <div
              class="emotion-4"
            >
              <div
                class="emotion-96"
              >
                <div
                  class="emotion-13"
                  data-testid="ctaDropdownWrapper"
                >
                  <button
                    aria-expanded="false"
                    class="emotion-14"
                    color="dark"
                  >
                    <span
                      class="emotion-15"
                    >
                      <span
                        class="emotion-16"
                        data-id="cta-dropdown-label"
                      >
                        Shop Sale
                        <span
                          aria-hidden="true"
                          class="emotion-17"
                        >
                          <svg
                            fill="none"
                            viewBox="0 0 12 12"
                            xmlns="http://www.w3.org/2000/svg"
                          >
                            <path
                              clip-rule="evenodd"
                              d="M7 0H5v5H0v2h5v5h2V7h5V5H7V0z"
                              fill="#000000"
                              fill-rule="evenodd"
                            />
                          </svg>
                        </span>
                      </span>
                    </span>
                  </button>
                  <div
                    class="emotion-18"
                  >
                    <ul
                      aria-hidden="true"
                      class="emotion-103"
                    >
                      <li
                        class="emotion-20"
                      >
                        <a
                          breakpoint="mobile"
                          class="emotion-21"
                          href="/Mens Sale"
                          target="_self"
                        >
                          Mens Sale
                        </a>
                      </li>
                      <li
                        class="emotion-20"
                      >
                        <a
                          breakpoint="mobile"
                          class="emotion-21"
                          href="/Womens Sale"
                          target="_self"
                        >
                          Womens Sale
                        </a>
                      </li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`SpotlightVariableHeightVideo - GAP styles should render secondary color ctas in the video overlay 1`] = `
.emotion-0 {
  max-width: 100%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  margin: auto;
}

.emotion-1 {
  background: rgb(203, 214, 230);
  width: 100%;
}

.emotion-2 {
  padding: min(40px, 2.7777777777777777vw) min(147px, 10.208333333333334vw) min(32px, 2.2222222222222223vw);
  width: 100%;
  padding-inline: 2.5vw;
  padding-bottom: 30px!important;
  gap: 30px;
}

.emotion-3 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-align-items: flex-start;
  -webkit-box-align: flex-start;
  -ms-flex-align: flex-start;
  align-items: flex-start;
}

.emotion-4 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  text-align: left;
  width: 100%;
}

.emotion-5 .amp-cms--p {
  padding: 0;
  margin: 0;
  line-height: 0;
}

.emotion-5 .amp-cms--p>span {
  display: inline;
  white-space: break-spaces;
}

.emotion-5 .amp-cms--p a {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  position: relative;
  z-index: 2;
  pointer-events: auto;
}

.emotion-5 .amp-cms--p a:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-5 .amp-cms--legal-copy {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(0.8333333333333334vw, 12px));
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-5 sup {
  vertical-align: top;
  display: inline-block;
  margin-top: -0.25ex;
}

.emotion-5 sub {
  vertical-align: bottom;
  display: inline-block;
  margin-bottom: -0.25ex;
}

.emotion-5 .amp-cms--f-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.8333333333333334vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-5 .amp-cms--f-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.8333333333333334vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-5 .amp-cms--f-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.8333333333333334vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-5 .amp-cms--fn-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.8333333333333334vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-5 .amp-cms--body-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(1.3888888888888888vw, 20px));
  line-height: 1.3;
  letter-spacing: 0;
}

.emotion-5 .amp-cms--body-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(1.25vw, 18px));
  line-height: 1.2777777777777777;
  letter-spacing: 0;
}

.emotion-5 .amp-cms--body-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(1.1111111111111112vw, 16px));
  line-height: 1.25;
  letter-spacing: 0;
}

.emotion-5 .amp-cms--body-4 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(0.9722222222222222vw, 14px));
  line-height: 1.2857142857142858;
  letter-spacing: 0;
}

.emotion-5 .amp-cms--body-5 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(0.8333333333333334vw, 12px));
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-5 .amp-cms--eyebrow-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(35px, min(3.4722222222222223vw, 50px));
  line-height: 0.92;
  letter-spacing: 0;
}

.emotion-5 .amp-cms--eyebrow-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(35px, min(2.7777777777777777vw, 40px));
  line-height: 1;
  letter-spacing: 0;
}

.emotion-5 .amp-cms--eyebrow-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(35px, min(2.430555555555556vw, 35px));
  line-height: 1;
  letter-spacing: 0;
}

.emotion-5 .amp-cms--headline-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(8.333333333333332vw, 120px));
  line-height: 0.8333333333333334;
  letter-spacing: 0;
}

.emotion-5 .amp-cms--headline-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(7.638888888888889vw, 110px));
  line-height: 0.8636363636363636;
  letter-spacing: 0;
}

.emotion-5 .amp-cms--headline-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(6.944444444444445vw, 100px));
  line-height: 0.85;
  letter-spacing: 0;
}

.emotion-5 .amp-cms--headline-4 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(6.944444444444445vw, 100px));
  line-height: 1.05;
  letter-spacing: 0;
}

.emotion-5 .amp-cms--headline-5 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(5.555555555555555vw, 80px));
  line-height: 0.9375;
  letter-spacing: 0;
}

.emotion-5 .amp-cms--headline-6 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(4.861111111111112vw, 70px));
  line-height: 0.9285714285714286;
  letter-spacing: 0;
}

.emotion-5 .amp-cms--headline-7 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(4.166666666666666vw, 60px));
  line-height: 0.9166666666666666;
  letter-spacing: 0;
}

.emotion-5 .amp-cms--headlineAlt-1 {
  color: #2B2B2B;
  font-size: max(14px, min(7.916666666666666vw, 114px));
  line-height: 1;
  letter-spacing: min(-0.027777777777777776vw, -0.4px);
  font-weight: 400;
}

.emotion-5 .amp-cms--headlineAlt-2 {
  color: #2B2B2B;
  font-size: max(14px, min(5.833333333333333vw, 84px));
  line-height: 1;
  letter-spacing: min(-0.027777777777777776vw, -0.4px);
  font-weight: 400;
}

.emotion-5 .amp-cms--headlineAlt-3 {
  color: #2B2B2B;
  font-size: max(14px, min(4.444444444444445vw, 64px));
  line-height: 1;
  letter-spacing: min(-0.027777777777777776vw, -0.4px);
  font-weight: 400;
}

.emotion-5 .amp-cms--headlineAlt-4 {
  color: #2B2B2B;
  font-size: max(14px, min(2.361111111111111vw, 34px));
  line-height: 1;
  letter-spacing: min(-0.027777777777777776vw, -0.4px);
  font-weight: 400;
}

.emotion-5 .amp-cms--headlineAlt-5 {
  color: #2B2B2B;
  font-size: min(1.1111111111111112vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-5 .amp-cms--headlineAlt-6 {
  color: #2B2B2B;
  font-size: min(1.1111111111111112vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-5 .amp-cms--headlineAlt-7 {
  color: #2B2B2B;
  font-size: min(1.1111111111111112vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-5 .amp-cms--promo-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(6.944444444444445vw, 100px));
  line-height: 1;
  letter-spacing: min(0vw, 0px);
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-5 .amp-cms--promo-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(5.555555555555555vw, 80px));
  line-height: 1;
  letter-spacing: min(0vw, 0px);
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-5 .amp-cms--promoAlt-1 {
  color: #2B2B2B;
  font-size: min(1.1111111111111112vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 600;
}

.emotion-5 .amp-cms--subhead-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(2.083333333333333vw, 30px));
  line-height: 1;
  letter-spacing: 0;
}

.emotion-5 .amp-cms--subhead-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(1.7361111111111112vw, 25px));
  line-height: 1;
  letter-spacing: 0;
}

.emotion-5 .amp-cms--subhead-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(1.3888888888888888vw, 20px));
  line-height: 1.1;
  letter-spacing: 0;
}

.emotion-7 {
  background: transparent;
  position: relative;
  width: 100%;
  height: 100%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
}

.emotion-8 {
  position: absolute;
  width: 100%;
  height: 100%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  pointer-events: none;
  gap: min(24px, 1.6666666666666667vw);
}

.emotion-9 {
  width: 100%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  pointer-events: none;
  gap: min(24px, 1.6666666666666667vw);
  background: linear-gradient(180deg, rgba(0, 0, 0, 0) -0.33%, rgba(0, 0, 0, 0.4) 43.94%, rgba(0, 0, 0, 0) 88.73%);
  background-blend-mode: darken;
}

.emotion-10 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  width: 100%;
  padding: min(32px, 2.2222222222222223vw) min(147px, 10.208333333333334vw);
  gap: 30px;
  pointer-events: none;
  padding-inline: 2.5vw;
  padding-bottom: 30px!important;
}

.emotion-11 {
  text-align: center;
  z-index: 1;
}

.emotion-11 .amp-cms--p {
  padding: 0;
  margin: 0;
  line-height: 0;
}

.emotion-11 .amp-cms--p>span {
  display: inline;
  white-space: break-spaces;
}

.emotion-11 .amp-cms--p a {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  position: relative;
  z-index: 2;
  pointer-events: auto;
}

.emotion-11 .amp-cms--p a:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-11 .amp-cms--legal-copy {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(0.8333333333333334vw, 12px));
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-11 sup {
  vertical-align: top;
  display: inline-block;
  margin-top: -0.25ex;
}

.emotion-11 sub {
  vertical-align: bottom;
  display: inline-block;
  margin-bottom: -0.25ex;
}

.emotion-11 .amp-cms--f-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.8333333333333334vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-11 .amp-cms--f-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.8333333333333334vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-11 .amp-cms--f-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.8333333333333334vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-11 .amp-cms--fn-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.8333333333333334vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-11 .amp-cms--body-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(1.3888888888888888vw, 20px));
  line-height: 1.3;
  letter-spacing: 0;
}

.emotion-11 .amp-cms--body-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(1.25vw, 18px));
  line-height: 1.2777777777777777;
  letter-spacing: 0;
}

.emotion-11 .amp-cms--body-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(1.1111111111111112vw, 16px));
  line-height: 1.25;
  letter-spacing: 0;
}

.emotion-11 .amp-cms--body-4 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(0.9722222222222222vw, 14px));
  line-height: 1.2857142857142858;
  letter-spacing: 0;
}

.emotion-11 .amp-cms--body-5 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(0.8333333333333334vw, 12px));
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-11 .amp-cms--eyebrow-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(35px, min(3.4722222222222223vw, 50px));
  line-height: 0.92;
  letter-spacing: 0;
}

.emotion-11 .amp-cms--eyebrow-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(35px, min(2.7777777777777777vw, 40px));
  line-height: 1;
  letter-spacing: 0;
}

.emotion-11 .amp-cms--eyebrow-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(35px, min(2.430555555555556vw, 35px));
  line-height: 1;
  letter-spacing: 0;
}

.emotion-11 .amp-cms--headline-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(8.333333333333332vw, 120px));
  line-height: 0.8333333333333334;
  letter-spacing: 0;
}

.emotion-11 .amp-cms--headline-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(7.638888888888889vw, 110px));
  line-height: 0.8636363636363636;
  letter-spacing: 0;
}

.emotion-11 .amp-cms--headline-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(6.944444444444445vw, 100px));
  line-height: 0.85;
  letter-spacing: 0;
}

.emotion-11 .amp-cms--headline-4 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(6.944444444444445vw, 100px));
  line-height: 1.05;
  letter-spacing: 0;
}

.emotion-11 .amp-cms--headline-5 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(5.555555555555555vw, 80px));
  line-height: 0.9375;
  letter-spacing: 0;
}

.emotion-11 .amp-cms--headline-6 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(4.861111111111112vw, 70px));
  line-height: 0.9285714285714286;
  letter-spacing: 0;
}

.emotion-11 .amp-cms--headline-7 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(4.166666666666666vw, 60px));
  line-height: 0.9166666666666666;
  letter-spacing: 0;
}

.emotion-11 .amp-cms--headlineAlt-1 {
  color: #2B2B2B;
  font-size: max(14px, min(7.916666666666666vw, 114px));
  line-height: 1;
  letter-spacing: min(-0.027777777777777776vw, -0.4px);
  font-weight: 400;
}

.emotion-11 .amp-cms--headlineAlt-2 {
  color: #2B2B2B;
  font-size: max(14px, min(5.833333333333333vw, 84px));
  line-height: 1;
  letter-spacing: min(-0.027777777777777776vw, -0.4px);
  font-weight: 400;
}

.emotion-11 .amp-cms--headlineAlt-3 {
  color: #2B2B2B;
  font-size: max(14px, min(4.444444444444445vw, 64px));
  line-height: 1;
  letter-spacing: min(-0.027777777777777776vw, -0.4px);
  font-weight: 400;
}

.emotion-11 .amp-cms--headlineAlt-4 {
  color: #2B2B2B;
  font-size: max(14px, min(2.361111111111111vw, 34px));
  line-height: 1;
  letter-spacing: min(-0.027777777777777776vw, -0.4px);
  font-weight: 400;
}

.emotion-11 .amp-cms--headlineAlt-5 {
  color: #2B2B2B;
  font-size: min(1.1111111111111112vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-11 .amp-cms--headlineAlt-6 {
  color: #2B2B2B;
  font-size: min(1.1111111111111112vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-11 .amp-cms--headlineAlt-7 {
  color: #2B2B2B;
  font-size: min(1.1111111111111112vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-11 .amp-cms--promo-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(6.944444444444445vw, 100px));
  line-height: 1;
  letter-spacing: min(0vw, 0px);
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-11 .amp-cms--promo-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(5.555555555555555vw, 80px));
  line-height: 1;
  letter-spacing: min(0vw, 0px);
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-11 .amp-cms--promoAlt-1 {
  color: #2B2B2B;
  font-size: min(1.1111111111111112vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 600;
}

.emotion-11 .amp-cms--subhead-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(2.083333333333333vw, 30px));
  line-height: 1;
  letter-spacing: 0;
}

.emotion-11 .amp-cms--subhead-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(1.7361111111111112vw, 25px));
  line-height: 1;
  letter-spacing: 0;
}

.emotion-11 .amp-cms--subhead-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(1.3888888888888888vw, 20px));
  line-height: 1.1;
  letter-spacing: 0;
}

.emotion-12 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  height: auto;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  gap: 20px;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
}

.emotion-12 span[data-id="cta-dropdown-label"] {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  justify-content: space-between;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

.emotion-12 span[data-id="cta-dropdown-label"]>span {
  position: relative;
  -webkit-transform: none;
  -moz-transform: none;
  -ms-transform: none;
  transform: none;
}

.emotion-12 button {
  width: auto;
}

.emotion-12 a {
  margin-left: 1px;
}

.emotion-13 {
  position: relative;
}

.emotion-14 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  display: inline-block;
  text-transform: none;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 1px solid transparent;
  border-radius: 0;
  max-width: 380px;
  font-size: 18px;
  font-weight: 500;
  letter-spacing: 2%;
  min-height: auto;
  max-height: auto;
  line-height: 18px;
  padding: 0;
  width: 100%;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: left;
  border-left: none;
  border-right: none;
  border-radius: 0;
  background-color: transparent;
  border-bottom: 0;
  -webkit-box-pack: left;
  -ms-flex-pack: left;
  -webkit-justify-content: left;
  justify-content: left;
  color: #FFFFFF;
  height: auto;
  -webkit-text-decoration: underline;
  text-decoration: underline;
  text-underline-offset: 5px;
  text-decoration-thickness: 2.3px;
  position: relative;
  z-index: 2;
  pointer-events: auto;
  text-align: left;
  line-height: 1;
  padding: 0;
  border: 0;
  text-underline-offset: 5px;
}

.emotion-14:focus {
  outline: none;
}

.emotion-14>span {
  padding: 1px 0;
}

.emotion-14:hover,
.emotion-14:focus {
  -webkit-text-decoration: unset;
  text-decoration: unset;
}

.emotion-14:active {
  -webkit-text-decoration: unset;
  text-decoration: unset;
}

.emotion-14>* {
  width: inherit;
  -webkit-text-decoration: inherit;
  text-decoration: inherit;
}

.emotion-14>span {
  padding: 0;
}

.emotion-15 {
  box-sizing: border-box;
}

.emotion-16 {
  display: inline-block;
  text-transform: none;
  position: relative;
  width: 100%;
  -webkit-text-decoration: inherit;
  text-decoration: inherit;
}

.emotion-17 {
  display: inline-block;
  height: 10px;
  width: 10px;
  min-height: 10px;
  min-width: 10px;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  position: absolute;
  right: 0;
  top: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  margin-left: 8px;
}

.emotion-17 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-17 svg path {
  fill: currentColor;
}

.emotion-17 svg rect {
  fill: currentColor;
}

.emotion-19 {
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
  background: #FFFFFF;
  box-sizing: border-box;
  list-style-type: none;
  min-width: 100%;
  overflow: hidden;
  position: relative;
  z-index: 390;
  border-width: 0;
  border-style: solid;
  text-align: left;
  letter-spacing: 0;
  pointer-events: auto;
  padding: 0px;
  width: -webkit-fit-content!important;
  width: -moz-fit-content!important;
  width: fit-content!important;
  position: absolute;
  max-height: 0;
  width: 100%;
  -webkit-transition: max-height .5s ease-in-out,visibility .5s;
  transition: max-height .5s ease-in-out,visibility .5s;
  visibility: hidden;
  margin-top: 12px;
  color: #FFFFFF;
  background-color: #000000;
}

.emotion-19 :hover {
  color: #000000;
  background-color: #FFFFFF;
}

.emotion-20 {
  box-sizing: border-box;
  width: 100%;
  border-bottom: none;
  letter-spacing: 1px;
  margin-left: 0;
  text-transform: none;
  padding: 12px;
  border-color: #FFFFFF;
}

.emotion-20:last-child {
  border: none;
}

.emotion-21 {
  cursor: pointer;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  padding: 0px;
  -webkit-text-decoration: none;
  text-decoration: none;
  -webkit-transition: color 0.3s ease;
  transition: color 0.3s ease;
  white-space: nowrap;
  font-size: 18px;
  text-align: center;
  color: inherit;
  -webkit-box-pack: start;
  -ms-flex-pack: start;
  -webkit-justify-content: start;
  justify-content: start;
  background-color: inherit;
  font-weight: 400;
  letter-spacing: 1px;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-74 {
  position: relative;
  height: 100%;
  pointer-events: auto;
  aspect-ratio: 1440/712;
}

.emotion-75 {
  width: -webkit-fit-content;
  width: -moz-fit-content;
  width: fit-content;
  position: relative;
  width: 100%;
}

.emotion-76>button {
  right: 2.5vw;
  bottom: 59px;
  height: 24px;
  width: 24px;
  z-index: 4;
}

.emotion-76>div>button {
  right: calc(2.5vw + 15px + 24px);
  bottom: 59px;
  height: 24px;
  width: 24px;
  z-index: 4;
}

.emotion-76>div>div {
  right: calc(2.5vw + 15px + 24px);
  bottom: 59px;
  z-index: 3;
  margin-bottom: 24px;
}

.emotion-77 {
  background: none;
  border: none;
  padding: 0;
  height: 24px;
  margin: 0;
  position: absolute;
  bottom: 8px;
  right: 112px;
  width: 24px;
  height: 24px;
}

.emotion-77:is(:not(:focus, :active, :hover, :focus-within)) {
  opacity: 0.64;
}

.emotion-77:is(:hover) {
  opacity: 1;
}

@media (min-width: 767px) {
  .emotion-77 {
    right: 120px;
  }
}

.emotion-78 {
  box-sizing: border-box;
  background: none;
  border: none;
  padding: 0;
  position: relative;
  width: 100%;
  height: 0px;
}

.emotion-78:focus div {
  opacity: 0.84;
  -webkit-transition: opacity 200ms;
  transition: opacity 200ms;
  border-radius: 20px 20px 0 0;
}

.emotion-78 div {
  border-radius: 20px 20px 0 0;
}

.emotion-78 .keepOpen,
.emotion-78 .staysOpen {
  opacity: 0.84;
  -webkit-transition: opacity 200ms;
  transition: opacity 200ms;
  z-index: 2;
}

.emotion-79 {
  background: none;
  border: none;
  padding: 0;
  height: 24px;
  margin: 0;
  position: absolute;
  bottom: 8px;
  right: 72px;
  z-index: 11;
}

.emotion-79:is(:not(:focus, :active, :hover, :focus-within)) {
  opacity: 0.64;
}

.emotion-79:is(:hover) {
  opacity: 1;
}

@media (min-width: 767px) {
  .emotion-79 {
    right: 80px;
  }
}

.emotion-80 {
  opacity: 0;
  -webkit-transition: opacity 200ms;
  transition: opacity 200ms;
  box-sizing: border-box;
  background: #ffffff;
  border: none;
  padding: 0;
  position: absolute;
  width: 24px;
  height: 60px;
  bottom: 32px;
  right: 80px;
  border-radius: 20px 20px 0 0;
}

.emotion-80:before {
  content: "";
  height: 24px;
  background: #ffffff;
  display: block;
  position: absolute;
  bottom: -24px;
  width: 24px;
  border-bottom-right-radius: 50%;
  border-bottom-left-radius: 50%;
}

.emotion-81 {
  height: 100%;
}

.emotion-82 {
  box-sizing: border-box;
  width: 100%;
  position: absolute;
  bottom: 0;
  pointer-events: none;
  padding: 0 2.5vw 30px;
  background: linear-gradient(0deg, rgba(0, 0, 0, 0.4) 50%, rgba(0, 0, 0, 0) 100%);
  display: grid;
  grid-template-columns: auto auto;
  grid-template-rows: auto auto;
  grid-template-areas: 'top-left top-right' 'bottom-left bottom-right';
  grid-row-gap: 10px;
}

.emotion-83 {
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  grid-area: bottom-left;
}

.emotion-84 {
  pointer-events: auto;
}

.emotion-84 .amp-cms--p {
  padding: 0;
  margin: 0;
  line-height: 0;
}

.emotion-84 .amp-cms--p>span {
  display: inline;
  white-space: break-spaces;
}

.emotion-84 .amp-cms--p a {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  position: relative;
  z-index: 2;
  pointer-events: auto;
}

.emotion-84 .amp-cms--p a:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-84 .amp-cms--legal-copy {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(0.8333333333333334vw, 12px));
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-84 sup {
  vertical-align: top;
  display: inline-block;
  margin-top: -0.25ex;
}

.emotion-84 sub {
  vertical-align: bottom;
  display: inline-block;
  margin-bottom: -0.25ex;
}

.emotion-84 .amp-cms--f-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.8333333333333334vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-84 .amp-cms--f-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.8333333333333334vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-84 .amp-cms--f-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.8333333333333334vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-84 .amp-cms--fn-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.8333333333333334vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-84 .amp-cms--body-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(1.3888888888888888vw, 20px));
  line-height: 1.3;
  letter-spacing: 0;
}

.emotion-84 .amp-cms--body-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(1.25vw, 18px));
  line-height: 1.2777777777777777;
  letter-spacing: 0;
}

.emotion-84 .amp-cms--body-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(1.1111111111111112vw, 16px));
  line-height: 1.25;
  letter-spacing: 0;
}

.emotion-84 .amp-cms--body-4 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(0.9722222222222222vw, 14px));
  line-height: 1.2857142857142858;
  letter-spacing: 0;
}

.emotion-84 .amp-cms--body-5 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(0.8333333333333334vw, 12px));
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-84 .amp-cms--eyebrow-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(35px, min(3.4722222222222223vw, 50px));
  line-height: 0.92;
  letter-spacing: 0;
}

.emotion-84 .amp-cms--eyebrow-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(35px, min(2.7777777777777777vw, 40px));
  line-height: 1;
  letter-spacing: 0;
}

.emotion-84 .amp-cms--eyebrow-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(35px, min(2.430555555555556vw, 35px));
  line-height: 1;
  letter-spacing: 0;
}

.emotion-84 .amp-cms--headline-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(8.333333333333332vw, 120px));
  line-height: 0.8333333333333334;
  letter-spacing: 0;
}

.emotion-84 .amp-cms--headline-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(7.638888888888889vw, 110px));
  line-height: 0.8636363636363636;
  letter-spacing: 0;
}

.emotion-84 .amp-cms--headline-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(6.944444444444445vw, 100px));
  line-height: 0.85;
  letter-spacing: 0;
}

.emotion-84 .amp-cms--headline-4 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(6.944444444444445vw, 100px));
  line-height: 1.05;
  letter-spacing: 0;
}

.emotion-84 .amp-cms--headline-5 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(5.555555555555555vw, 80px));
  line-height: 0.9375;
  letter-spacing: 0;
}

.emotion-84 .amp-cms--headline-6 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(4.861111111111112vw, 70px));
  line-height: 0.9285714285714286;
  letter-spacing: 0;
}

.emotion-84 .amp-cms--headline-7 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(4.166666666666666vw, 60px));
  line-height: 0.9166666666666666;
  letter-spacing: 0;
}

.emotion-84 .amp-cms--headlineAlt-1 {
  color: #2B2B2B;
  font-size: max(14px, min(7.916666666666666vw, 114px));
  line-height: 1;
  letter-spacing: min(-0.027777777777777776vw, -0.4px);
  font-weight: 400;
}

.emotion-84 .amp-cms--headlineAlt-2 {
  color: #2B2B2B;
  font-size: max(14px, min(5.833333333333333vw, 84px));
  line-height: 1;
  letter-spacing: min(-0.027777777777777776vw, -0.4px);
  font-weight: 400;
}

.emotion-84 .amp-cms--headlineAlt-3 {
  color: #2B2B2B;
  font-size: max(14px, min(4.444444444444445vw, 64px));
  line-height: 1;
  letter-spacing: min(-0.027777777777777776vw, -0.4px);
  font-weight: 400;
}

.emotion-84 .amp-cms--headlineAlt-4 {
  color: #2B2B2B;
  font-size: max(14px, min(2.361111111111111vw, 34px));
  line-height: 1;
  letter-spacing: min(-0.027777777777777776vw, -0.4px);
  font-weight: 400;
}

.emotion-84 .amp-cms--headlineAlt-5 {
  color: #2B2B2B;
  font-size: min(1.1111111111111112vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-84 .amp-cms--headlineAlt-6 {
  color: #2B2B2B;
  font-size: min(1.1111111111111112vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-84 .amp-cms--headlineAlt-7 {
  color: #2B2B2B;
  font-size: min(1.1111111111111112vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-84 .amp-cms--promo-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(6.944444444444445vw, 100px));
  line-height: 1;
  letter-spacing: min(0vw, 0px);
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-84 .amp-cms--promo-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(5.555555555555555vw, 80px));
  line-height: 1;
  letter-spacing: min(0vw, 0px);
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-84 .amp-cms--promoAlt-1 {
  color: #2B2B2B;
  font-size: min(1.1111111111111112vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 600;
}

.emotion-84 .amp-cms--subhead-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(2.083333333333333vw, 30px));
  line-height: 1;
  letter-spacing: 0;
}

.emotion-84 .amp-cms--subhead-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(1.7361111111111112vw, 25px));
  line-height: 1;
  letter-spacing: 0;
}

.emotion-84 .amp-cms--subhead-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(1.3888888888888888vw, 20px));
  line-height: 1.1;
  letter-spacing: 0;
}

.emotion-85 {
  grid-area: bottom-right;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: end;
  -ms-flex-pack: end;
  -webkit-justify-content: end;
  justify-content: end;
}

.emotion-86 {
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  padding: 0;
  padding: 0;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: end;
  -webkit-box-align: end;
  -ms-flex-align: end;
  align-items: end;
}

.emotion-87 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
  text-transform: none;
  font-weight: normal;
  min-height: auto;
  color: #FFFFFF;
  font-size: 12px;
  padding: 0;
  line-height: normal;
}

.emotion-88 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  display: inline-block;
  text-transform: none;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 1px solid transparent;
  border-radius: 0;
  max-width: 300px;
  font-size: 12px;
  font-weight: 500;
  letter-spacing: 2%;
  min-height: auto;
  max-height: auto;
  line-height: 12px;
  padding: 0;
  width: auto;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: left;
  border-radius: 0;
  border: none;
  background-color: transparent;
  color: #000000;
  height: auto;
  -webkit-box-pack: left;
  -ms-flex-pack: left;
  -webkit-justify-content: left;
  justify-content: left;
  font-weight: normal;
  text-transform: none;
  z-index: 1;
  color: #FFFFFF;
  -webkit-text-decoration: underline;
  text-decoration: underline;
  font-size: 12px;
  min-height: auto;
  padding: 0;
  line-height: normal;
  margin-left: 8px;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: end;
  -webkit-box-align: end;
  -ms-flex-align: end;
  align-items: end;
  pointer-events: auto;
}

.emotion-88:focus {
  outline: none;
}

.emotion-88>span {
  padding: 1px 0;
}

.emotion-88 span svg path {
  -webkit-transition: fill 250ms ease-in-out;
  transition: fill 250ms ease-in-out;
  fill: #000000;
}

.emotion-88 span span {
  padding-left: initial;
}

.emotion-88:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-88:focus-visible {
  outline: auto;
}

.emotion-89 {
  cursor: pointer;
  position: absolute;
  height: calc(100% - 16px);
  width: 100%;
  top: 0px;
  left: 0px;
}

.emotion-90 {
  background: -webkit-linear-gradient(45deg, #DDDDFF,#3333FF);
  width: 100%;
}

.emotion-91 {
  padding: min(24px, 1.6666666666666667vw) min(147px, 10.208333333333334vw) min(40px, 2.7777777777777777vw);
  width: 100%;
  padding-inline: 2.5vw;
  padding-bottom: 30px!important;
  gap: 30px;
}

.emotion-92 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-align-items: flex-start;
  -webkit-box-align: flex-start;
  -ms-flex-align: flex-start;
  align-items: flex-start;
  gap: min(24px, 1.6666666666666667vw);
}

.emotion-96 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  height: auto;
  -webkit-box-pack: start;
  -ms-flex-pack: start;
  -webkit-justify-content: flex-start;
  justify-content: flex-start;
  gap: 60px;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

.emotion-98 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  display: inline-block;
  text-transform: none;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 1px solid transparent;
  border-radius: 0;
  max-width: 380px;
  font-size: 18px;
  font-weight: 500;
  letter-spacing: 2%;
  min-height: auto;
  max-height: auto;
  line-height: 18px;
  padding: 0;
  width: 100%;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: left;
  border-left: none;
  border-right: none;
  border-radius: 0;
  background-color: transparent;
  border-bottom: 0;
  -webkit-box-pack: left;
  -ms-flex-pack: left;
  -webkit-justify-content: left;
  justify-content: left;
  color: #000000;
  height: auto;
  -webkit-text-decoration: underline;
  text-decoration: underline;
  text-underline-offset: 5px;
  text-decoration-thickness: 2.3px;
  position: relative;
  z-index: 2;
  pointer-events: auto;
  text-align: left;
  line-height: 1;
  padding: 0;
  border: 0;
  text-underline-offset: 5px;
}

.emotion-98:focus {
  outline: none;
}

.emotion-98>span {
  padding: 1px 0;
}

.emotion-98:hover,
.emotion-98:focus {
  -webkit-text-decoration: unset;
  text-decoration: unset;
}

.emotion-98:active {
  -webkit-text-decoration: unset;
  text-decoration: unset;
}

.emotion-98>* {
  width: inherit;
  -webkit-text-decoration: inherit;
  text-decoration: inherit;
}

.emotion-98>span {
  padding: 0;
}

.emotion-103 {
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
  background: #FFFFFF;
  box-sizing: border-box;
  list-style-type: none;
  min-width: 100%;
  overflow: hidden;
  position: relative;
  z-index: 390;
  border-width: 0;
  border-style: solid;
  text-align: left;
  letter-spacing: 0;
  pointer-events: auto;
  padding: 0px;
  position: absolute;
  max-height: 0;
  width: 100%;
  -webkit-transition: max-height .5s ease-in-out,visibility .5s;
  transition: max-height .5s ease-in-out,visibility .5s;
  visibility: hidden;
  margin-top: 12px;
  color: #000000;
  background-color: #FFFFFF;
}

.emotion-103 :hover {
  color: #FFFFFF;
  background-color: #000000;
}

.emotion-104 {
  box-sizing: border-box;
  width: 100%;
  border-bottom: none;
  letter-spacing: 1px;
  margin-left: 0;
  text-transform: none;
  padding: 12px;
  border-color: #000000;
}

.emotion-104:last-child {
  border: none;
}

<div>
  <div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      class="emotion-0"
    >
      <div
        class="emotion-1"
        height="0"
        width="0"
      >
        <div
          class="emotion-2"
        >
          <div
            class="emotion-3"
          >
            <div
              class="emotion-4"
            >
              <div
                class="emotion-5"
              >
                <div>
                  <p
                    class="amp-cms--p"
                  >
                    <span
                      class="amp-cms--headline-1"
                    >
                      Above section
                    </span>
                  </p>
                  <p
                    class="amp-cms--p"
                  >
                    <span
                      class="amp-cms--body-1"
                    >
                      Above section body
                    </span>
                  </p>
                </div>
              </div>
            </div>
            <div
              class="emotion-4"
            />
          </div>
        </div>
      </div>
      <div
        class="emotion-7"
        height="0"
        width="0"
      >
        <div
          class="emotion-8"
        >
          <div
            class="emotion-9"
          >
            <div
              class="emotion-10"
            >
              <div
                class="emotion-11"
              >
                <div>
                  <p
                    class="amp-cms--p"
                  >
                    <span
                      class="amp-cms--headline-4"
                      style="color:#FFFFFF"
                    >
                      Video Text
                    </span>
                  </p>
                </div>
              </div>
              <div
                class="emotion-12"
              >
                <div
                  class="emotion-13"
                  data-testid="ctaDropdownWrapper"
                >
                  <button
                    aria-expanded="false"
                    class="emotion-14"
                    color="light"
                  >
                    <span
                      class="emotion-15"
                    >
                      <span
                        class="emotion-16"
                        data-id="cta-dropdown-label"
                      >
                        Shop All
                        <span
                          aria-hidden="true"
                          class="emotion-17"
                        >
                          <svg
                            fill="none"
                            viewBox="0 0 12 12"
                            xmlns="http://www.w3.org/2000/svg"
                          >
                            <path
                              clip-rule="evenodd"
                              d="M7 0H5v5H0v2h5v5h2V7h5V5H7V0z"
                              fill="#000000"
                              fill-rule="evenodd"
                            />
                          </svg>
                        </span>
                      </span>
                    </span>
                  </button>
                  <div
                    class="emotion-18"
                  >
                    <ul
                      aria-hidden="true"
                      class="emotion-19"
                    >
                      <li
                        class="emotion-20"
                      >
                        <a
                          breakpoint="desktop"
                          class="emotion-21"
                          href="/Women"
                          target="_self"
                        >
                          Women
                        </a>
                      </li>
                      <li
                        class="emotion-20"
                      >
                        <a
                          breakpoint="desktop"
                          class="emotion-21"
                          href="/Men"
                          target="_self"
                        >
                          Men
                        </a>
                      </li>
                      <li
                        class="emotion-20"
                      >
                        <a
                          breakpoint="desktop"
                          class="emotion-21"
                          href="/Girls"
                          target="_self"
                        >
                          Girls
                        </a>
                      </li>
                      <li
                        class="emotion-20"
                      >
                        <a
                          breakpoint="desktop"
                          class="emotion-21"
                          href="/Boys"
                          target="_self"
                        >
                          Boys
                        </a>
                      </li>
                      <li
                        class="emotion-20"
                      >
                        <a
                          breakpoint="desktop"
                          class="emotion-21"
                          href="/Toddler Girls"
                          target="_self"
                        >
                          Toddler Girls
                        </a>
                      </li>
                      <li
                        class="emotion-20"
                      >
                        <a
                          breakpoint="desktop"
                          class="emotion-21"
                          href="/Toddler Boys"
                          target="_self"
                        >
                          Toddler Boys
                        </a>
                      </li>
                      <li
                        class="emotion-20"
                      >
                        <a
                          breakpoint="desktop"
                          class="emotion-21"
                          href="/Baby Girls"
                          target="_self"
                        >
                          Baby Girls
                        </a>
                      </li>
                      <li
                        class="emotion-20"
                      >
                        <a
                          breakpoint="desktop"
                          class="emotion-21"
                          href="/Baby Boys"
                          target="_self"
                        >
                          Baby Boys
                        </a>
                      </li>
                    </ul>
                  </div>
                </div>
                <div
                  class="emotion-13"
                  data-testid="ctaDropdownWrapper"
                >
                  <button
                    aria-expanded="false"
                    class="emotion-14"
                    color="light"
                  >
                    <span
                      class="emotion-15"
                    >
                      <span
                        class="emotion-16"
                        data-id="cta-dropdown-label"
                      >
                        Shop Pants
                        <span
                          aria-hidden="true"
                          class="emotion-17"
                        >
                          <svg
                            fill="none"
                            viewBox="0 0 12 12"
                            xmlns="http://www.w3.org/2000/svg"
                          >
                            <path
                              clip-rule="evenodd"
                              d="M7 0H5v5H0v2h5v5h2V7h5V5H7V0z"
                              fill="#000000"
                              fill-rule="evenodd"
                            />
                          </svg>
                        </span>
                      </span>
                    </span>
                  </button>
                  <div
                    class="emotion-18"
                  >
                    <ul
                      aria-hidden="true"
                      class="emotion-19"
                    >
                      <li
                        class="emotion-20"
                      >
                        <a
                          breakpoint="desktop"
                          class="emotion-21"
                          href="/jeans"
                          target="_self"
                        >
                          Jeans
                        </a>
                      </li>
                      <li
                        class="emotion-20"
                      >
                        <a
                          breakpoint="desktop"
                          class="emotion-21"
                          href="/khakis"
                          target="_self"
                        >
                          Khakis
                        </a>
                      </li>
                      <li
                        class="emotion-20"
                      >
                        <a
                          breakpoint="desktop"
                          class="emotion-21"
                          href="/sweatpants"
                          target="_self"
                        >
                          Sweatpants Sweatpants Sweatpants Sweatpants Sweatpants Sweatpants Sweatpants
                        </a>
                      </li>
                    </ul>
                  </div>
                </div>
                <div
                  class="emotion-13"
                  data-testid="ctaDropdownWrapper"
                >
                  <button
                    aria-expanded="false"
                    class="emotion-14"
                    color="light"
                  >
                    <span
                      class="emotion-15"
                    >
                      <span
                        class="emotion-16"
                        data-id="cta-dropdown-label"
                      >
                        Shop New
                        <span
                          aria-hidden="true"
                          class="emotion-17"
                        >
                          <svg
                            fill="none"
                            viewBox="0 0 12 12"
                            xmlns="http://www.w3.org/2000/svg"
                          >
                            <path
                              clip-rule="evenodd"
                              d="M7 0H5v5H0v2h5v5h2V7h5V5H7V0z"
                              fill="#000000"
                              fill-rule="evenodd"
                            />
                          </svg>
                        </span>
                      </span>
                    </span>
                  </button>
                  <div
                    class="emotion-18"
                  >
                    <ul
                      aria-hidden="true"
                      class="emotion-19"
                    >
                      <li
                        class="emotion-20"
                      >
                        <a
                          breakpoint="desktop"
                          class="emotion-21"
                          href="/Women"
                          target="_self"
                        >
                          Women
                        </a>
                      </li>
                      <li
                        class="emotion-20"
                      >
                        <a
                          breakpoint="desktop"
                          class="emotion-21"
                          href="/Men"
                          target="_self"
                        >
                          Men
                        </a>
                      </li>
                      <li
                        class="emotion-20"
                      >
                        <a
                          breakpoint="desktop"
                          class="emotion-21"
                          href="/Girls"
                          target="_self"
                        >
                          Girls
                        </a>
                      </li>
                      <li
                        class="emotion-20"
                      >
                        <a
                          breakpoint="desktop"
                          class="emotion-21"
                          href="/Boys"
                          target="_self"
                        >
                          Boys
                        </a>
                      </li>
                      <li
                        class="emotion-20"
                      >
                        <a
                          breakpoint="desktop"
                          class="emotion-21"
                          href="/Toddler Girls"
                          target="_self"
                        >
                          Toddler Girls
                        </a>
                      </li>
                      <li
                        class="emotion-20"
                      >
                        <a
                          breakpoint="desktop"
                          class="emotion-21"
                          href="/Toddler Boys"
                          target="_self"
                        >
                          Toddler Boys
                        </a>
                      </li>
                      <li
                        class="emotion-20"
                      >
                        <a
                          breakpoint="desktop"
                          class="emotion-21"
                          href="/Baby Girls"
                          target="_self"
                        >
                          Baby Girls
                        </a>
                      </li>
                      <li
                        class="emotion-20"
                      >
                        <a
                          breakpoint="desktop"
                          class="emotion-21"
                          href="/Maternity"
                          target="_self"
                        >
                          Maternity
                        </a>
                      </li>
                      <li
                        class="emotion-20"
                      >
                        <a
                          breakpoint="desktop"
                          class="emotion-21"
                          href="/Shop for the Fam Fam"
                          target="_self"
                        >
                          Shop for the Fam Fam
                        </a>
                      </li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div
          class="emotion-74"
        >
          <div
            class="emotion-75"
            data-testid="videocomponent-container"
          >
            <div
              style="position: relative;"
            >
              <img
                fetchpriority="high"
                src="https://fmzlik0m0z6r1oifp8ex832rf.staging.bigcontent.io/i/oldnavy/FA23_D3_Banner_NA_IMG1_XL?fmt=auto"
                style="height: 100%; left: 0px; object-fit: cover; position: absolute; top: 0px; width: 100%;"
              />
              <h1
                role="presentation"
              >
                ReactPlayer
              </h1>
            </div>
            <div
              class="player-custom-controls emotion-76"
              data-testid="player-custom-controls"
            >
              <button
                aria-label="Pause"
                class="emotion-77"
              >
                <svg
                  aria-label="pause-button"
                  fill="none"
                  height="24"
                  viewBox="0 0 24 24"
                  width="24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M24 12C24 18.6275 18.6275 24 12 24C5.3725 24 0 18.6275 0 12C0 5.3725 5.3725 0 12 0C18.6275 0 24 5.3725 24 12Z"
                    fill="#FFFFFF"
                  />
                  <path
                    d="M7.5 7.5C7.5 7.10218 7.65804 6.72064 7.93934 6.43934C8.22064 6.15804 8.60218 6 9 6C9.39782 6 9.77936 6.15804 10.0607 6.43934C10.342 6.72064 10.5 7.10218 10.5 7.5V16.5C10.5 16.8978 10.342 17.2794 10.0607 17.5607C9.77936 17.842 9.39782 18 9 18C8.60218 18 8.22064 17.842 7.93934 17.5607C7.65804 17.2794 7.5 16.8978 7.5 16.5V7.5ZM13.5 7.5C13.5 7.10218 13.658 6.72064 13.9393 6.43934C14.2206 6.15804 14.6022 6 15 6C15.3978 6 15.7794 6.15804 16.0607 6.43934C16.342 6.72064 16.5 7.10218 16.5 7.5V16.5C16.5 16.8978 16.342 17.2794 16.0607 17.5607C15.7794 17.842 15.3978 18 15 18C14.6022 18 14.2206 17.842 13.9393 17.5607C13.658 17.2794 13.5 16.8978 13.5 16.5V7.5Z"
                    fill="#2C2824"
                  />
                </svg>
              </button>
              <div
                class="emotion-78"
                data-testid="styled-mute-controls"
              >
                <button
                  aria-label="Mute"
                  aria-pressed="false"
                  class="emotion-79"
                >
                  <svg
                    aria-label="mute-button"
                    fill="none"
                    height="24"
                    viewBox="0 0 24 24"
                    width="24"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M24 12C24 18.6275 18.6275 24 12 24C5.3725 24 0 18.6275 0 12C0 5.3725 5.3725 0 12 0C18.6275 0 24 5.3725 24 12Z"
                      fill="#FFFFFF"
                    />
                    <g>
                      <path
                        clip-rule="evenodd"
                        d="M14.4996 14.2613C15.7486 13.0124 15.7486 10.9876 14.4996 9.73866C14.28 9.51899 14.28 9.16283 14.4996 8.94316C14.7193 8.72349 15.0755 8.72349 15.2951 8.94316C16.9835 10.6315 16.9835 13.3685 15.2951 15.0568C15.0755 15.2765 14.7193 15.2765 14.4996 15.0568C14.28 14.8372 14.28 14.481 14.4996 14.2613Z"
                        fill="#2C2824"
                        fill-rule="evenodd"
                      />
                      <path
                        clip-rule="evenodd"
                        d="M16.4087 16.034C18.6368 13.8059 18.6368 10.1939 16.4087 7.96586C16.1891 7.74619 16.1891 7.39003 16.4087 7.17036C16.6284 6.95069 16.9846 6.95069 17.2042 7.17036C19.8716 9.83778 19.8716 14.1621 17.2042 16.8295C16.9846 17.0492 16.6284 17.0492 16.4087 16.8295C16.1891 16.6098 16.1891 16.2537 16.4087 16.034Z"
                        fill="#2C2824"
                        fill-rule="evenodd"
                      />
                      <path
                        d="M4.7383 12.8103V12V11.1897C4.7383 10.6374 5.18602 10.1897 5.7383 10.1897H7.14083C7.39437 10.1897 7.63844 10.0934 7.82366 9.92023L11.0042 6.94759C11.14 6.82063 11.319 6.75 11.5049 6.75C11.91 6.75 12.2383 7.07834 12.2383 7.48337V12V16.5166C12.2383 16.9217 11.91 17.25 11.5049 17.25C11.319 17.25 11.14 17.1794 11.0042 17.0524L7.82366 14.0798C7.63844 13.9066 7.39437 13.8103 7.14083 13.8103H5.7383C5.18602 13.8103 4.7383 13.3626 4.7383 12.8103Z"
                        fill="#2C2824"
                      />
                      <rect
                        fill="#2C2824"
                        height="19.3235"
                        transform="rotate(120 21.2347 16.4121)"
                        width="1.5"
                        x="21.2347"
                        y="16.4121"
                      />
                    </g>
                  </svg>
                </button>
                <div
                  class="emotion-80"
                  data-testid="styled-input-volume"
                  style="background: rgb(255, 255, 255);"
                >
                  <div
                    role="presentation"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
        <div
          class="emotion-81"
        >
          <div
            class="emotion-82"
          >
            <div
              class="emotion-83"
            >
              <div
                class="emotion-84"
              >
                <div>
                  <p
                    style="color:#FFFFFF"
                  >
                    Handle Text
                  </p>
                </div>
              </div>
            </div>
            <div
              class="emotion-85"
            >
              <div
                class="emotion-86"
              >
                <span
                  class="emotion-87"
                >
                  Prefix Label
                </span>
              </div>
              <button
                class="emotion-88"
              >
                Details Link
              </button>
            </div>
          </div>
        </div>
        <a
          aria-label="Voluptatem voluptas"
          class="emotion-89"
          data-testid="conditional-link"
          href="#3"
          target="_self"
        />
      </div>
      <div
        class="emotion-90"
        height="0"
        width="0"
      >
        <div
          class="emotion-91"
        >
          <div
            class="emotion-92"
          >
            <div
              class="emotion-4"
            >
              <div
                class="emotion-5"
              >
                <div>
                  <p
                    class="amp-cms--p"
                  >
                    <span
                      class="amp-cms--headline-1"
                    >
                      Below section
                    </span>
                  </p>
                  <p
                    class="amp-cms--p"
                  >
                    <span
                      class="amp-cms--body-1"
                    >
                      Below section body
                    </span>
                  </p>
                </div>
              </div>
            </div>
            <div
              class="emotion-4"
            >
              <div
                class="emotion-96"
              >
                <div
                  class="emotion-13"
                  data-testid="ctaDropdownWrapper"
                >
                  <button
                    aria-expanded="false"
                    class="emotion-98"
                    color="dark"
                  >
                    <span
                      class="emotion-15"
                    >
                      <span
                        class="emotion-16"
                        data-id="cta-dropdown-label"
                      >
                        Shop Sale
                        <span
                          aria-hidden="true"
                          class="emotion-17"
                        >
                          <svg
                            fill="none"
                            viewBox="0 0 12 12"
                            xmlns="http://www.w3.org/2000/svg"
                          >
                            <path
                              clip-rule="evenodd"
                              d="M7 0H5v5H0v2h5v5h2V7h5V5H7V0z"
                              fill="#000000"
                              fill-rule="evenodd"
                            />
                          </svg>
                        </span>
                      </span>
                    </span>
                  </button>
                  <div
                    class="emotion-18"
                  >
                    <ul
                      aria-hidden="true"
                      class="emotion-103"
                    >
                      <li
                        class="emotion-104"
                      >
                        <a
                          breakpoint="desktop"
                          class="emotion-21"
                          href="/Mens Sale"
                          target="_self"
                        >
                          Mens Sale
                        </a>
                      </li>
                      <li
                        class="emotion-104"
                      >
                        <a
                          breakpoint="desktop"
                          class="emotion-21"
                          href="/Womens Sale"
                          target="_self"
                        >
                          Womens Sale
                        </a>
                      </li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;
