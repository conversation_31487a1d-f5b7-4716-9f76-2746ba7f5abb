// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`SpotlightVideo Content Type FullBleed Vertical alignment Bottom should match for desktop 1`] = `
.emotion-0 {
  position: relative;
  width: 100%;
  height: 100%;
  box-sizing: border-box;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  padding-top: 0;
  padding: 0;
}

.emotion-1 {
  -webkit-align-self: center;
  -ms-flex-item-align: center;
  align-self: center;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  width: 100%;
  gap: 20px;
  position: absolute;
  bottom: 0;
  -webkit-align-items: end;
  -webkit-box-align: end;
  -ms-flex-align: end;
  align-items: end;
  pointer-events: none;
  z-index: 1;
  width: 100%;
  bottom: 0;
  -webkit-align-items: end;
  -webkit-box-align: end;
  -ms-flex-align: end;
  align-items: end;
}

.emotion-1>div {
  width: 100%;
}

.emotion-2 {
  background: linear-gradient(180deg, rgba(0, 0, 0, 0) -0.33%, rgba(0, 0, 0, 0.4) 43.94%, rgba(0, 0, 0, 0) 88.73%);
  background-blend-mode: darken;
  width: 100%;
}

.emotion-3 {
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  padding: 50px 30px;
}

.emotion-4 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  gap: 20px;
  pointer-events: none;
  -webkit-align-items: end;
  -webkit-box-align: end;
  -ms-flex-align: end;
  align-items: end;
  -webkit-box-pack: right;
  -ms-flex-pack: right;
  -webkit-justify-content: right;
  justify-content: right;
}

.emotion-5 {
  text-align: right;
}

.emotion-5 .amp-cms--p {
  padding: 0;
  margin: 0;
  line-height: 0;
}

.emotion-5 .amp-cms--p>span {
  display: inline;
  white-space: break-spaces;
}

.emotion-5 .amp-cms--p a {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  position: relative;
  z-index: 2;
  pointer-events: auto;
}

.emotion-5 .amp-cms--p a:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-5 .amp-cms--legal-copy {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, 2.666666666666667vw);
  line-height: 1.5;
  letter-spacing: 0.13333333333333333vw;
  font-weight: 500;
}

.emotion-5 sup {
  vertical-align: top;
  display: inline-block;
  margin-top: -0.25ex;
}

.emotion-5 sub {
  vertical-align: bottom;
  display: inline-block;
  margin-bottom: -0.25ex;
}

.emotion-5 .amp-cms--f-0 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 2.933333333333333vw;
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-5 .amp-cms--f-1 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 2.933333333333333vw;
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-5 .amp-cms--f-2 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 2.933333333333333vw;
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-5 .amp-cms--fn-1 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 2.933333333333333vw;
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-5 .amp-cms--body-1 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, 4.266666666666667vw);
  line-height: 1.625;
  letter-spacing: 0.21333333333333335vw;
  font-weight: 500;
}

.emotion-5 .amp-cms--body-2 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, 3.733333333333334vw);
  line-height: 1.4285714285714286;
  letter-spacing: 0vw;
  font-weight: 500;
}

.emotion-5 .amp-cms--body-3 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, 3.2vw);
  line-height: 1.5;
  letter-spacing: 0.15999999999999998vw;
  font-weight: 500;
}

.emotion-5 .amp-cms--body-4 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, 2.666666666666667vw);
  line-height: 1.6;
  letter-spacing: 0vw;
  font-weight: 500;
}

.emotion-5 .amp-cms--body-5 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, 2.666666666666667vw);
  line-height: 1.5;
  letter-spacing: 0.13333333333333333vw;
  font-weight: 500;
}

.emotion-5 .amp-cms--eyebrow-1 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, 3.2vw);
  line-height: 2;
  letter-spacing: 0.31999999999999995vw;
  font-weight: 700;
  text-transform: uppercase;
}

.emotion-5 .amp-cms--eyebrow-2 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(11px, 2.933333333333333vw);
  line-height: 2;
  letter-spacing: 0.29333333333333333vw;
  font-weight: 700;
  text-transform: uppercase;
}

.emotion-5 .amp-cms--eyebrow-3 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, 2.666666666666667vw);
  line-height: 1.5;
  letter-spacing: 0.18133333333333335vw;
  font-weight: 700;
  text-transform: uppercase;
}

.emotion-5 .amp-cms--headline-1 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(13px, 9.6vw);
  line-height: 1;
  letter-spacing: 0.36533333333333334vw;
  text-transform: uppercase;
  font-weight: 500;
}

.emotion-5 .amp-cms--headline-2 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(13px, 9.066666666666666vw);
  line-height: 1;
  letter-spacing: 0.45333333333333325vw;
  text-transform: uppercase;
  font-weight: 500;
}

.emotion-5 .amp-cms--headline-3 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(13px, 8vw);
  line-height: 1;
  letter-spacing: 0.07999999999999999vw;
  text-transform: uppercase;
  font-weight: 500;
}

.emotion-5 .amp-cms--headline-4 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(13px, 6.4vw);
  line-height: 1;
  letter-spacing: 0.48000000000000004vw;
  text-transform: uppercase;
  font-weight: 500;
}

.emotion-5 .amp-cms--headline-5 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(13px, 5.866666666666666vw);
  line-height: 1.1818181818181819;
  letter-spacing: 0.5866666666666667vw;
  text-transform: uppercase;
  font-weight: 600;
}

.emotion-5 .amp-cms--headline-6 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(13px, 4.266666666666667vw);
  line-height: 1.375;
  letter-spacing: 0.21333333333333335vw;
  text-transform: none;
  font-weight: 600;
}

.emotion-5 .amp-cms--headline-7 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(13px, 3.733333333333334vw);
  line-height: 1.4285714285714286;
  letter-spacing: 0.02666666666666667vw;
  text-transform: uppercase;
  font-weight: 700;
}

.emotion-5 .amp-cms--headlineAlt-1 {
  color: #000000;
  font-family: var(--font-soehne),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(13px, 17.066666666666666vw);
  line-height: 1;
  letter-spacing: -0.07999999999999999vw;
  font-weight: 400;
}

.emotion-5 .amp-cms--headlineAlt-2 {
  color: #000000;
  font-family: var(--font-soehne),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(13px, 14.399999999999999vw);
  line-height: 1;
  letter-spacing: -0.07999999999999999vw;
  font-weight: 400;
}

.emotion-5 .amp-cms--headlineAlt-3 {
  color: #000000;
  font-family: var(--font-soehne),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(13px, 9.066666666666666vw);
  line-height: 1;
  letter-spacing: -0.07999999999999999vw;
  font-weight: 400;
}

.emotion-5 .amp-cms--headlineAlt-4 {
  color: #000000;
  font-family: var(--font-soehne),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(13px, 6.4vw);
  line-height: 1.4166666666666667;
  letter-spacing: -0.07999999999999999vw;
  font-weight: 400;
}

.emotion-5 .amp-cms--headlineAlt-5 {
  color: #000000;
  font-family: var(--font-soehne),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 3.733333333333334vw;
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-5 .amp-cms--headlineAlt-6 {
  color: #000000;
  font-family: var(--font-soehne),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 3.733333333333334vw;
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-5 .amp-cms--headlineAlt-7 {
  color: #000000;
  font-family: var(--font-soehne),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 3.733333333333334vw;
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-5 .amp-cms--promo-1 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(13px, 16vw);
  line-height: 1;
  letter-spacing: 0vw;
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-5 .amp-cms--promo-2 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(13px, 10.666666666666668vw);
  line-height: 1;
  letter-spacing: 0vw;
  font-weight: 600;
}

.emotion-5 .amp-cms--promoAlt-1 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 3.733333333333334vw;
  line-height: 0.07142857142857142;
  letter-spacing: 0;
}

.emotion-5 .amp-cms--subhead-1 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(13px, 4.266666666666667vw);
  line-height: 1.75;
  letter-spacing: 0.08533333333333333vw;
  font-weight: 400;
  text-transform: uppercase;
}

.emotion-5 .amp-cms--subhead-2 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(13px, 3.733333333333334vw);
  line-height: 1.5714285714285714;
  letter-spacing: 0.18666666666666668vw;
  font-weight: 500;
  text-transform: uppercase;
}

.emotion-5 .amp-cms--subhead-3 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(13px, 3.4666666666666663vw);
  line-height: 1;
  letter-spacing: 0.25866666666666666vw;
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-5 a {
  pointer-events: auto;
}

.emotion-6 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  gap: 15px;
}

.emotion-6 a {
  -webkit-align-self: center;
  -ms-flex-item-align: center;
  align-self: center;
}

.emotion-7 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: none;
  transition: none;
  box-sizing: border-box;
  border: 1.5px solid transparent;
  border-radius: 0;
  font-size: 14px;
  font-weight: 500;
  letter-spacing: 0px;
  min-height: auto;
  max-height: 50px;
  line-height: 1.2857142857142858;
  padding: 18px 40px;
  width: auto;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  background-color: #FFFFFF;
  color: #000000;
  border-color: #000000;
  -webkit-align-self: flex-start;
  -ms-flex-item-align: flex-start;
  align-self: flex-start;
  white-space: normal;
  pointer-events: auto;
}

.emotion-7:focus {
  outline: none;
}

.emotion-7>span {
  padding: 1px 0;
}

.emotion-7:hover,
.emotion-7:focus {
  text-shadow: 0 0 2px currentColor;
}

.emotion-7:active {
  color: #FFFFFF;
  background-color: #000000;
  border-color: #000000;
  -webkit-text-decoration: none;
  text-decoration: none;
  text-shadow: none;
}

.emotion-8 {
  position: relative;
  aspect-ratio: 9/16;
}

.emotion-8>div {
  height: 100%;
}

.emotion-9 {
  height: 100%;
  width: 100%;
  position: absolute;
  right: 0;
  bottom: 0;
}

.emotion-10 {
  box-sizing: border-box;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: end;
  -webkit-box-align: end;
  -ms-flex-align: end;
  align-items: end;
  gap: 10px;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  justify-content: space-between;
  position: absolute;
  padding: 0 10px 10px 10px;
  -webkit-flex-direction: row-reverse;
  -ms-flex-direction: row-reverse;
  flex-direction: row-reverse;
  bottom: 0;
  width: 100%;
  background: linear-gradient(0deg, rgba(0, 0, 0, 0.4) 50%, rgba(0, 0, 0, 0) 100%);
}

.emotion-11 {
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
}

.emotion-12 {
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  padding: 0;
}

.emotion-13 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 10px;
  line-height: 1.5;
  letter-spacing: 0.5px;
  font-weight: 500;
  text-transform: none;
  font-weight: normal;
  min-height: initial;
  color: #FFFFFF;
  font-size: 11px;
  line-height: 14px;
  letter-spacing: normal;
}

.emotion-14 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: none;
  transition: none;
  box-sizing: border-box;
  border: 1.5px solid transparent;
  border-radius: 0;
  font-size: 16px;
  font-weight: 500;
  letter-spacing: 0px;
  min-height: 44px;
  max-height: auto;
  line-height: 1.3125;
  padding: 0;
  width: auto;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: left;
  border-radius: 0;
  border: none;
  background-color: transparent;
  color: #000000;
  height: auto;
  -webkit-box-pack: left;
  -ms-flex-pack: left;
  -webkit-justify-content: left;
  justify-content: left;
  font-weight: normal;
  text-transform: none;
  z-index: 1;
  font-size: 11px;
  line-height: 14px;
  min-height: initial;
  padding: 0;
  margin-left: 5px;
  color: #FFFFFF;
  pointer-events: auto;
  text-underline-offset: 2px;
}

.emotion-14:focus {
  outline: none;
}

.emotion-14>span {
  padding: 1px 0;
}

.emotion-14 span span {
  padding-left: 3px;
  min-width: auto;
  min-height: auto;
  position: relative;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-transition: 250ms ease-in-out;
  transition: 250ms ease-in-out;
  transition-property: height,width,margin;
  top: 0;
  width: calc(1.2rem * 0.72);
}

.emotion-14 span span svg {
  -webkit-transition: fill 250ms ease-in-out;
  transition: fill 250ms ease-in-out;
  fill: #000000;
}

.emotion-14 span span {
  padding-left: initial;
}

.emotion-14:focus-visible {
  outline: auto;
}

.emotion-15 {
  -webkit-text-decoration: underline;
  text-decoration: underline;
}

.emotion-15:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

<div>
  <div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <div
        class="emotion-0"
        height="0"
        width="0"
      >
        <div
          class="emotion-1"
        >
          <div
            class="spotlight-background-gradient emotion-2"
          >
            <div
              class="emotion-3"
            >
              <div
                class="emotion-4"
              >
                <div
                  class="emotion-5"
                >
                  <div>
                    <p
                      class="amp-cms--p"
                    >
                      <span
                        class="amp-cms--body-2"
                        style="color:#FFFFFF"
                      >
                        Mobile Headline
                      </span>
                    </p>
                    <hr
                      aria-hidden="true"
                      style="display:block;border:0;height:8px;margin:0;background:transparent;"
                    />
                    <p
                      class="amp-cms--p"
                    >
                      <span
                        class="amp-cms--body-4"
                        style="color:#FFFFFF"
                      >
                        Lorem ipsum dolor sit amet, consectetur
                      </span>
                    </p>
                    <p
                      class="amp-cms--p"
                    >
                      <span
                        class="amp-cms--body-4"
                        style="color:#FFFFFF"
                      >
                        adipiscing elit, sed do eiusmod
                      </span>
                    </p>
                    <p
                      class="amp-cms--p"
                    >
                      <span
                        class="amp-cms--body-4"
                        style="color:#FFFFFF"
                      >
                        tempor incididunt ut labore.
                      </span>
                    </p>
                  </div>
                </div>
                <div
                  class="emotion-6"
                >
                  <a
                    class="emotion-7"
                    color="dark"
                    href="#"
                  >
                    Shop all
                  </a>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div
          class="emotion-8"
        >
          <h1
            role="presentation"
          >
            ReactPlayer
          </h1>
          <div
            class="emotion-9"
          >
            <div
              class="emotion-10"
            >
              <div
                class="emotion-11"
              >
                <div
                  class="emotion-12"
                >
                  <span
                    class="emotion-13"
                  >
                    Exclusions apply
                  </span>
                </div>
                <button
                  class="emotion-14"
                >
                  <span>
                    *
                    <span
                      class="emotion-15"
                    >
                      Details
                    </span>
                  </span>
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`SpotlightVideo Content Type FullBleed Vertical alignment Bottom should match for mobile 1`] = `
.emotion-0 {
  position: relative;
  width: 100%;
  height: 100%;
  box-sizing: border-box;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  padding-top: 0;
  padding: 0;
}

.emotion-1 {
  -webkit-align-self: center;
  -ms-flex-item-align: center;
  align-self: center;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  width: 100%;
  gap: 20px;
  position: absolute;
  bottom: 0;
  -webkit-align-items: start;
  -webkit-box-align: start;
  -ms-flex-align: start;
  align-items: start;
  pointer-events: none;
  z-index: 1;
  width: 100%;
  bottom: 0;
  -webkit-align-items: start;
  -webkit-box-align: start;
  -ms-flex-align: start;
  align-items: start;
}

.emotion-1>div {
  width: 100%;
}

.emotion-2 {
  width: 100%;
}

.emotion-3 {
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  padding: 50px 30px;
  -webkit-align-items: start;
  -webkit-box-align: start;
  -ms-flex-align: start;
  align-items: start;
  -webkit-box-pack: left;
  -ms-flex-pack: left;
  -webkit-justify-content: left;
  justify-content: left;
  gap: 20px;
}

.emotion-4 {
  text-align: left;
}

.emotion-4 .amp-cms--p {
  padding: 0;
  margin: 0;
  line-height: 0;
}

.emotion-4 .amp-cms--p>span {
  display: inline;
  white-space: break-spaces;
}

.emotion-4 .amp-cms--p a {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  position: relative;
  z-index: 2;
  pointer-events: auto;
}

.emotion-4 .amp-cms--p a:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-4 .amp-cms--legal-copy {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, 2.666666666666667vw);
  line-height: 1.5;
  letter-spacing: 0.13333333333333333vw;
  font-weight: 500;
}

.emotion-4 sup {
  vertical-align: top;
  display: inline-block;
  margin-top: -0.25ex;
}

.emotion-4 sub {
  vertical-align: bottom;
  display: inline-block;
  margin-bottom: -0.25ex;
}

.emotion-4 .amp-cms--f-0 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 2.933333333333333vw;
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-4 .amp-cms--f-1 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 2.933333333333333vw;
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-4 .amp-cms--f-2 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 2.933333333333333vw;
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-4 .amp-cms--fn-1 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 2.933333333333333vw;
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-4 .amp-cms--body-1 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, 4.266666666666667vw);
  line-height: 1.625;
  letter-spacing: 0.21333333333333335vw;
  font-weight: 500;
}

.emotion-4 .amp-cms--body-2 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, 3.733333333333334vw);
  line-height: 1.4285714285714286;
  letter-spacing: 0vw;
  font-weight: 500;
}

.emotion-4 .amp-cms--body-3 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, 3.2vw);
  line-height: 1.5;
  letter-spacing: 0.15999999999999998vw;
  font-weight: 500;
}

.emotion-4 .amp-cms--body-4 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, 2.666666666666667vw);
  line-height: 1.6;
  letter-spacing: 0vw;
  font-weight: 500;
}

.emotion-4 .amp-cms--body-5 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, 2.666666666666667vw);
  line-height: 1.5;
  letter-spacing: 0.13333333333333333vw;
  font-weight: 500;
}

.emotion-4 .amp-cms--eyebrow-1 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, 3.2vw);
  line-height: 2;
  letter-spacing: 0.31999999999999995vw;
  font-weight: 700;
  text-transform: uppercase;
}

.emotion-4 .amp-cms--eyebrow-2 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(11px, 2.933333333333333vw);
  line-height: 2;
  letter-spacing: 0.29333333333333333vw;
  font-weight: 700;
  text-transform: uppercase;
}

.emotion-4 .amp-cms--eyebrow-3 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, 2.666666666666667vw);
  line-height: 1.5;
  letter-spacing: 0.18133333333333335vw;
  font-weight: 700;
  text-transform: uppercase;
}

.emotion-4 .amp-cms--headline-1 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(13px, 9.6vw);
  line-height: 1;
  letter-spacing: 0.36533333333333334vw;
  text-transform: uppercase;
  font-weight: 500;
}

.emotion-4 .amp-cms--headline-2 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(13px, 9.066666666666666vw);
  line-height: 1;
  letter-spacing: 0.45333333333333325vw;
  text-transform: uppercase;
  font-weight: 500;
}

.emotion-4 .amp-cms--headline-3 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(13px, 8vw);
  line-height: 1;
  letter-spacing: 0.07999999999999999vw;
  text-transform: uppercase;
  font-weight: 500;
}

.emotion-4 .amp-cms--headline-4 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(13px, 6.4vw);
  line-height: 1;
  letter-spacing: 0.48000000000000004vw;
  text-transform: uppercase;
  font-weight: 500;
}

.emotion-4 .amp-cms--headline-5 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(13px, 5.866666666666666vw);
  line-height: 1.1818181818181819;
  letter-spacing: 0.5866666666666667vw;
  text-transform: uppercase;
  font-weight: 600;
}

.emotion-4 .amp-cms--headline-6 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(13px, 4.266666666666667vw);
  line-height: 1.375;
  letter-spacing: 0.21333333333333335vw;
  text-transform: none;
  font-weight: 600;
}

.emotion-4 .amp-cms--headline-7 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(13px, 3.733333333333334vw);
  line-height: 1.4285714285714286;
  letter-spacing: 0.02666666666666667vw;
  text-transform: uppercase;
  font-weight: 700;
}

.emotion-4 .amp-cms--headlineAlt-1 {
  color: #000000;
  font-family: var(--font-soehne),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(13px, 17.066666666666666vw);
  line-height: 1;
  letter-spacing: -0.07999999999999999vw;
  font-weight: 400;
}

.emotion-4 .amp-cms--headlineAlt-2 {
  color: #000000;
  font-family: var(--font-soehne),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(13px, 14.399999999999999vw);
  line-height: 1;
  letter-spacing: -0.07999999999999999vw;
  font-weight: 400;
}

.emotion-4 .amp-cms--headlineAlt-3 {
  color: #000000;
  font-family: var(--font-soehne),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(13px, 9.066666666666666vw);
  line-height: 1;
  letter-spacing: -0.07999999999999999vw;
  font-weight: 400;
}

.emotion-4 .amp-cms--headlineAlt-4 {
  color: #000000;
  font-family: var(--font-soehne),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(13px, 6.4vw);
  line-height: 1.4166666666666667;
  letter-spacing: -0.07999999999999999vw;
  font-weight: 400;
}

.emotion-4 .amp-cms--headlineAlt-5 {
  color: #000000;
  font-family: var(--font-soehne),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 3.733333333333334vw;
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-4 .amp-cms--headlineAlt-6 {
  color: #000000;
  font-family: var(--font-soehne),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 3.733333333333334vw;
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-4 .amp-cms--headlineAlt-7 {
  color: #000000;
  font-family: var(--font-soehne),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 3.733333333333334vw;
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-4 .amp-cms--promo-1 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(13px, 16vw);
  line-height: 1;
  letter-spacing: 0vw;
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-4 .amp-cms--promo-2 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(13px, 10.666666666666668vw);
  line-height: 1;
  letter-spacing: 0vw;
  font-weight: 600;
}

.emotion-4 .amp-cms--promoAlt-1 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 3.733333333333334vw;
  line-height: 0.07142857142857142;
  letter-spacing: 0;
}

.emotion-4 .amp-cms--subhead-1 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(13px, 4.266666666666667vw);
  line-height: 1.75;
  letter-spacing: 0.08533333333333333vw;
  font-weight: 400;
  text-transform: uppercase;
}

.emotion-4 .amp-cms--subhead-2 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(13px, 3.733333333333334vw);
  line-height: 1.5714285714285714;
  letter-spacing: 0.18666666666666668vw;
  font-weight: 500;
  text-transform: uppercase;
}

.emotion-4 .amp-cms--subhead-3 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(13px, 3.4666666666666663vw);
  line-height: 1;
  letter-spacing: 0.25866666666666666vw;
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-4 a {
  pointer-events: auto;
}

.emotion-5 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  gap: 15px;
}

.emotion-5 a {
  -webkit-align-self: center;
  -ms-flex-item-align: center;
  align-self: center;
}

.emotion-6 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: none;
  transition: none;
  box-sizing: border-box;
  border: 1.5px solid transparent;
  border-radius: 0;
  font-size: 14px;
  font-weight: 500;
  letter-spacing: 0px;
  min-height: auto;
  max-height: 50px;
  line-height: 1.2857142857142858;
  padding: 18px 40px;
  width: auto;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  background-color: #FFFFFF;
  color: #000000;
  border-color: #000000;
  -webkit-align-self: flex-start;
  -ms-flex-item-align: flex-start;
  align-self: flex-start;
  white-space: normal;
  pointer-events: auto;
}

.emotion-6:focus {
  outline: none;
}

.emotion-6>span {
  padding: 1px 0;
}

.emotion-6:hover,
.emotion-6:focus {
  text-shadow: 0 0 2px currentColor;
}

.emotion-6:active {
  color: #FFFFFF;
  background-color: #000000;
  border-color: #000000;
  -webkit-text-decoration: none;
  text-decoration: none;
  text-shadow: none;
}

.emotion-7 {
  position: relative;
  aspect-ratio: 9/16;
}

.emotion-7>div {
  height: 100%;
}

<div>
  <div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <div
        class="emotion-0"
        height="0"
        width="0"
      >
        <div
          class="emotion-1"
        >
          <div
            class="emotion-2"
          >
            <div
              class="emotion-3"
            >
              <div
                class="emotion-4"
              >
                <div>
                  <p
                    class="amp-cms--p"
                  >
                    <span
                      class="amp-cms--headlineAlt-1"
                      style="color:#FFFFFF"
                    >
                      Large Headline
                    </span>
                  </p>
                  <hr
                    aria-hidden="true"
                    style="display:block;border:0;height:8px;margin:0;background:transparent;"
                  />
                  <p
                    class="amp-cms--p"
                  >
                    <span
                      class="amp-cms--body-2"
                      style="color:#FFFFFF"
                    >
                      Lorem ipsum dolor sit amet, consectetur
                    </span>
                  </p>
                  <p
                    class="amp-cms--p"
                  >
                    <span
                      class="amp-cms--body-2"
                      style="color:#FFFFFF"
                    >
                      adipiscing elit, sed do eiusmod
                    </span>
                  </p>
                  <p
                    class="amp-cms--p"
                  >
                    <span
                      class="amp-cms--body-2"
                      style="color:#FFFFFF"
                    >
                      tempor incididunt ut labore.
                    </span>
                  </p>
                </div>
              </div>
              <div
                class="emotion-5"
              >
                <a
                  class="emotion-6"
                  color="dark"
                  href="#"
                >
                  Shop all
                </a>
              </div>
            </div>
          </div>
        </div>
        <div
          class="emotion-7"
        >
          <h1
            role="presentation"
          >
            ReactPlayer
          </h1>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`SpotlightVideo Content Type FullBleed Vertical alignment Top should match for desktop 1`] = `<div />`;

exports[`SpotlightVideo Content Type FullBleed Vertical alignment Top should match for mobile 1`] = `
.emotion-0 {
  position: relative;
  width: 100%;
  height: 100%;
  box-sizing: border-box;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  padding-top: 0;
  padding: 0;
}

.emotion-1 {
  -webkit-align-self: center;
  -ms-flex-item-align: center;
  align-self: center;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  width: 100%;
  gap: 20px;
  position: absolute;
  -webkit-align-items: end;
  -webkit-box-align: end;
  -ms-flex-align: end;
  align-items: end;
  pointer-events: none;
  z-index: 1;
  width: 100%;
  -webkit-align-items: end;
  -webkit-box-align: end;
  -ms-flex-align: end;
  align-items: end;
}

.emotion-1>div {
  width: 100%;
}

.emotion-2 {
  width: 100%;
}

.emotion-3 {
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  padding: 50px 30px;
  -webkit-align-items: end;
  -webkit-box-align: end;
  -ms-flex-align: end;
  align-items: end;
  -webkit-box-pack: right;
  -ms-flex-pack: right;
  -webkit-justify-content: right;
  justify-content: right;
  gap: 20px;
}

.emotion-4 {
  text-align: right;
}

.emotion-4 .amp-cms--p {
  padding: 0;
  margin: 0;
  line-height: 0;
}

.emotion-4 .amp-cms--p>span {
  display: inline;
  white-space: break-spaces;
}

.emotion-4 .amp-cms--p a {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  position: relative;
  z-index: 2;
  pointer-events: auto;
}

.emotion-4 .amp-cms--p a:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-4 .amp-cms--legal-copy {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, 2.666666666666667vw);
  line-height: 1.5;
  letter-spacing: 0.13333333333333333vw;
  font-weight: 500;
}

.emotion-4 sup {
  vertical-align: top;
  display: inline-block;
  margin-top: -0.25ex;
}

.emotion-4 sub {
  vertical-align: bottom;
  display: inline-block;
  margin-bottom: -0.25ex;
}

.emotion-4 .amp-cms--f-0 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 2.933333333333333vw;
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-4 .amp-cms--f-1 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 2.933333333333333vw;
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-4 .amp-cms--f-2 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 2.933333333333333vw;
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-4 .amp-cms--fn-1 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 2.933333333333333vw;
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-4 .amp-cms--body-1 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, 4.266666666666667vw);
  line-height: 1.625;
  letter-spacing: 0.21333333333333335vw;
  font-weight: 500;
}

.emotion-4 .amp-cms--body-2 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, 3.733333333333334vw);
  line-height: 1.4285714285714286;
  letter-spacing: 0vw;
  font-weight: 500;
}

.emotion-4 .amp-cms--body-3 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, 3.2vw);
  line-height: 1.5;
  letter-spacing: 0.15999999999999998vw;
  font-weight: 500;
}

.emotion-4 .amp-cms--body-4 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, 2.666666666666667vw);
  line-height: 1.6;
  letter-spacing: 0vw;
  font-weight: 500;
}

.emotion-4 .amp-cms--body-5 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, 2.666666666666667vw);
  line-height: 1.5;
  letter-spacing: 0.13333333333333333vw;
  font-weight: 500;
}

.emotion-4 .amp-cms--eyebrow-1 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, 3.2vw);
  line-height: 2;
  letter-spacing: 0.31999999999999995vw;
  font-weight: 700;
  text-transform: uppercase;
}

.emotion-4 .amp-cms--eyebrow-2 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(11px, 2.933333333333333vw);
  line-height: 2;
  letter-spacing: 0.29333333333333333vw;
  font-weight: 700;
  text-transform: uppercase;
}

.emotion-4 .amp-cms--eyebrow-3 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, 2.666666666666667vw);
  line-height: 1.5;
  letter-spacing: 0.18133333333333335vw;
  font-weight: 700;
  text-transform: uppercase;
}

.emotion-4 .amp-cms--headline-1 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(13px, 9.6vw);
  line-height: 1;
  letter-spacing: 0.36533333333333334vw;
  text-transform: uppercase;
  font-weight: 500;
}

.emotion-4 .amp-cms--headline-2 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(13px, 9.066666666666666vw);
  line-height: 1;
  letter-spacing: 0.45333333333333325vw;
  text-transform: uppercase;
  font-weight: 500;
}

.emotion-4 .amp-cms--headline-3 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(13px, 8vw);
  line-height: 1;
  letter-spacing: 0.07999999999999999vw;
  text-transform: uppercase;
  font-weight: 500;
}

.emotion-4 .amp-cms--headline-4 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(13px, 6.4vw);
  line-height: 1;
  letter-spacing: 0.48000000000000004vw;
  text-transform: uppercase;
  font-weight: 500;
}

.emotion-4 .amp-cms--headline-5 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(13px, 5.866666666666666vw);
  line-height: 1.1818181818181819;
  letter-spacing: 0.5866666666666667vw;
  text-transform: uppercase;
  font-weight: 600;
}

.emotion-4 .amp-cms--headline-6 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(13px, 4.266666666666667vw);
  line-height: 1.375;
  letter-spacing: 0.21333333333333335vw;
  text-transform: none;
  font-weight: 600;
}

.emotion-4 .amp-cms--headline-7 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(13px, 3.733333333333334vw);
  line-height: 1.4285714285714286;
  letter-spacing: 0.02666666666666667vw;
  text-transform: uppercase;
  font-weight: 700;
}

.emotion-4 .amp-cms--headlineAlt-1 {
  color: #000000;
  font-family: var(--font-soehne),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(13px, 17.066666666666666vw);
  line-height: 1;
  letter-spacing: -0.07999999999999999vw;
  font-weight: 400;
}

.emotion-4 .amp-cms--headlineAlt-2 {
  color: #000000;
  font-family: var(--font-soehne),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(13px, 14.399999999999999vw);
  line-height: 1;
  letter-spacing: -0.07999999999999999vw;
  font-weight: 400;
}

.emotion-4 .amp-cms--headlineAlt-3 {
  color: #000000;
  font-family: var(--font-soehne),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(13px, 9.066666666666666vw);
  line-height: 1;
  letter-spacing: -0.07999999999999999vw;
  font-weight: 400;
}

.emotion-4 .amp-cms--headlineAlt-4 {
  color: #000000;
  font-family: var(--font-soehne),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(13px, 6.4vw);
  line-height: 1.4166666666666667;
  letter-spacing: -0.07999999999999999vw;
  font-weight: 400;
}

.emotion-4 .amp-cms--headlineAlt-5 {
  color: #000000;
  font-family: var(--font-soehne),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 3.733333333333334vw;
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-4 .amp-cms--headlineAlt-6 {
  color: #000000;
  font-family: var(--font-soehne),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 3.733333333333334vw;
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-4 .amp-cms--headlineAlt-7 {
  color: #000000;
  font-family: var(--font-soehne),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 3.733333333333334vw;
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-4 .amp-cms--promo-1 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(13px, 16vw);
  line-height: 1;
  letter-spacing: 0vw;
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-4 .amp-cms--promo-2 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(13px, 10.666666666666668vw);
  line-height: 1;
  letter-spacing: 0vw;
  font-weight: 600;
}

.emotion-4 .amp-cms--promoAlt-1 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 3.733333333333334vw;
  line-height: 0.07142857142857142;
  letter-spacing: 0;
}

.emotion-4 .amp-cms--subhead-1 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(13px, 4.266666666666667vw);
  line-height: 1.75;
  letter-spacing: 0.08533333333333333vw;
  font-weight: 400;
  text-transform: uppercase;
}

.emotion-4 .amp-cms--subhead-2 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(13px, 3.733333333333334vw);
  line-height: 1.5714285714285714;
  letter-spacing: 0.18666666666666668vw;
  font-weight: 500;
  text-transform: uppercase;
}

.emotion-4 .amp-cms--subhead-3 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(13px, 3.4666666666666663vw);
  line-height: 1;
  letter-spacing: 0.25866666666666666vw;
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-4 a {
  pointer-events: auto;
}

.emotion-5 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  gap: 15px;
}

.emotion-5 a {
  -webkit-align-self: center;
  -ms-flex-item-align: center;
  align-self: center;
}

.emotion-6 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: none;
  transition: none;
  box-sizing: border-box;
  border: 1.5px solid transparent;
  border-radius: 0;
  font-size: 14px;
  font-weight: 500;
  letter-spacing: 0px;
  min-height: auto;
  max-height: 50px;
  line-height: 1.2857142857142858;
  padding: 18px 40px;
  width: auto;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  background-color: #FFFFFF;
  color: #000000;
  border-color: #000000;
  -webkit-align-self: flex-start;
  -ms-flex-item-align: flex-start;
  align-self: flex-start;
  white-space: normal;
  pointer-events: auto;
}

.emotion-6:focus {
  outline: none;
}

.emotion-6>span {
  padding: 1px 0;
}

.emotion-6:hover,
.emotion-6:focus {
  text-shadow: 0 0 2px currentColor;
}

.emotion-6:active {
  color: #FFFFFF;
  background-color: #000000;
  border-color: #000000;
  -webkit-text-decoration: none;
  text-decoration: none;
  text-shadow: none;
}

.emotion-7 {
  position: relative;
  aspect-ratio: 9/16;
}

.emotion-7>div {
  height: 100%;
}

.emotion-8 {
  height: 100%;
  width: 100%;
  position: absolute;
  right: 0;
  bottom: 0;
}

.emotion-9 {
  box-sizing: border-box;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: end;
  -webkit-box-align: end;
  -ms-flex-align: end;
  align-items: end;
  gap: 10px;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  justify-content: space-between;
  position: absolute;
  padding: 0 10px 10px 10px;
  -webkit-flex-direction: row-reverse;
  -ms-flex-direction: row-reverse;
  flex-direction: row-reverse;
  bottom: 0;
  width: 100%;
}

.emotion-10 {
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
}

.emotion-11 {
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  padding: 0;
}

.emotion-12 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 10px;
  line-height: 1.5;
  letter-spacing: 0.5px;
  font-weight: 500;
  text-transform: none;
  font-weight: normal;
  min-height: initial;
  color: #FFFFFF;
  font-size: 11px;
  line-height: 14px;
  letter-spacing: normal;
}

.emotion-13 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: none;
  transition: none;
  box-sizing: border-box;
  border: 1.5px solid transparent;
  border-radius: 0;
  font-size: 16px;
  font-weight: 500;
  letter-spacing: 0px;
  min-height: 44px;
  max-height: auto;
  line-height: 1.3125;
  padding: 0;
  width: auto;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: left;
  border-radius: 0;
  border: none;
  background-color: transparent;
  color: #000000;
  height: auto;
  -webkit-box-pack: left;
  -ms-flex-pack: left;
  -webkit-justify-content: left;
  justify-content: left;
  font-weight: normal;
  text-transform: none;
  z-index: 1;
  font-size: 11px;
  line-height: 14px;
  min-height: initial;
  padding: 0;
  margin-left: 5px;
  color: #FFFFFF;
  pointer-events: auto;
  text-underline-offset: 2px;
}

.emotion-13:focus {
  outline: none;
}

.emotion-13>span {
  padding: 1px 0;
}

.emotion-13 span span {
  padding-left: 3px;
  min-width: auto;
  min-height: auto;
  position: relative;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-transition: 250ms ease-in-out;
  transition: 250ms ease-in-out;
  transition-property: height,width,margin;
  top: 0;
  width: calc(1.2rem * 0.72);
}

.emotion-13 span span svg {
  -webkit-transition: fill 250ms ease-in-out;
  transition: fill 250ms ease-in-out;
  fill: #000000;
}

.emotion-13 span span {
  padding-left: initial;
}

.emotion-13:focus-visible {
  outline: auto;
}

.emotion-14 {
  -webkit-text-decoration: underline;
  text-decoration: underline;
}

.emotion-14:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

<div>
  <div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <div
        class="emotion-0"
        height="0"
        width="0"
      >
        <div
          class="emotion-1"
        >
          <div
            class="emotion-2"
          >
            <div
              class="emotion-3"
            >
              <div
                class="emotion-4"
              >
                <div>
                  <p
                    class="amp-cms--p"
                  >
                    <span
                      class="amp-cms--headlineAlt-1"
                      style="color:#FFFFFF"
                    >
                      Large Headline
                    </span>
                  </p>
                  <hr
                    aria-hidden="true"
                    style="display:block;border:0;height:8px;margin:0;background:transparent;"
                  />
                  <p
                    class="amp-cms--p"
                  >
                    <span
                      class="amp-cms--body-2"
                      style="color:#FFFFFF"
                    >
                      Lorem ipsum dolor sit amet, consectetur
                    </span>
                  </p>
                  <p
                    class="amp-cms--p"
                  >
                    <span
                      class="amp-cms--body-2"
                      style="color:#FFFFFF"
                    >
                      adipiscing elit, sed do eiusmod
                    </span>
                  </p>
                  <p
                    class="amp-cms--p"
                  >
                    <span
                      class="amp-cms--body-2"
                      style="color:#FFFFFF"
                    >
                      tempor incididunt ut labore.
                    </span>
                  </p>
                </div>
              </div>
              <div
                class="emotion-5"
              >
                <a
                  class="emotion-6"
                  color="dark"
                  href="#"
                >
                  Shop all
                </a>
              </div>
            </div>
          </div>
        </div>
        <div
          class="emotion-7"
        >
          <h1
            role="presentation"
          >
            ReactPlayer
          </h1>
          <div
            class="emotion-8"
          >
            <div
              class="emotion-9"
            >
              <div
                class="emotion-10"
              >
                <div
                  class="emotion-11"
                >
                  <span
                    class="emotion-12"
                  >
                    Exclusions apply
                  </span>
                </div>
                <button
                  class="emotion-13"
                >
                  <span>
                    *
                    <span
                      class="emotion-14"
                    >
                      Details
                    </span>
                  </span>
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`SpotlightVideo Content Type FullBleed should match desktop 1`] = `
.emotion-0 {
  background: #e9f807;
  position: relative;
  width: 100%;
  height: 100%;
  box-sizing: border-box;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  padding-top: 0;
  padding: 0;
}

.emotion-1 {
  -webkit-align-self: center;
  -ms-flex-item-align: center;
  align-self: center;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  width: 100%;
  gap: 20px;
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  -webkit-align-items: start;
  -webkit-box-align: start;
  -ms-flex-align: start;
  align-items: start;
  pointer-events: none;
  z-index: 1;
  width: 100%;
  -webkit-align-items: start;
  -webkit-box-align: start;
  -ms-flex-align: start;
  align-items: start;
}

.emotion-1>div {
  width: 100%;
}

.emotion-2 {
  width: 100%;
}

.emotion-3 {
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  padding: 50px 135px;
  -webkit-align-items: start;
  -webkit-box-align: start;
  -ms-flex-align: start;
  align-items: start;
  -webkit-box-pack: left;
  -ms-flex-pack: left;
  -webkit-justify-content: left;
  justify-content: left;
  gap: 20px;
}

.emotion-4 {
  height: auto;
  max-width: 100%;
}

.emotion-5 {
  min-height: 105px;
  max-height: 105px;
}

.emotion-6 {
  text-align: left;
}

.emotion-6 .amp-cms--p {
  padding: 0;
  margin: 0;
  line-height: 0;
}

.emotion-6 .amp-cms--p>span {
  display: inline;
  white-space: break-spaces;
}

.emotion-6 .amp-cms--p a {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  position: relative;
  z-index: 2;
  pointer-events: auto;
}

.emotion-6 .amp-cms--p a:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-6 .amp-cms--legal-copy {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, 0.6944444444444444vw);
  line-height: 1.5;
  letter-spacing: 0.034722222222222224vw;
  font-weight: 500;
}

.emotion-6 sup {
  vertical-align: top;
  display: inline-block;
  margin-top: -0.25ex;
}

.emotion-6 sub {
  vertical-align: bottom;
  display: inline-block;
  margin-bottom: -0.25ex;
}

.emotion-6 .amp-cms--f-0 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 0.8333333333333334vw;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-6 .amp-cms--f-1 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 0.8333333333333334vw;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-6 .amp-cms--f-2 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 0.8333333333333334vw;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-6 .amp-cms--fn-1 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 0.8333333333333334vw;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-6 .amp-cms--body-1 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, 1.25vw);
  line-height: 1.5555555555555556;
  letter-spacing: 0.0625vw;
  font-weight: 500;
}

.emotion-6 .amp-cms--body-2 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, 1.1111111111111112vw);
  line-height: 1.25;
  letter-spacing: 0vw;
  font-weight: 500;
}

.emotion-6 .amp-cms--body-3 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, 0.9722222222222222vw);
  line-height: 1.5714285714285714;
  letter-spacing: 0.04861111111111111vw;
  font-weight: 500;
}

.emotion-6 .amp-cms--body-4 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, 0.8333333333333334vw);
  line-height: 1.5;
  letter-spacing: 0vw;
  font-weight: 500;
}

.emotion-6 .amp-cms--body-5 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, 0.6944444444444444vw);
  line-height: 1.5;
  letter-spacing: 0.034722222222222224vw;
  font-weight: 500;
}

.emotion-6 .amp-cms--eyebrow-1 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, 1.1805555555555556vw);
  line-height: 1.588235294117647;
  letter-spacing: 0.11805555555555555vw;
  font-weight: 700;
  text-transform: uppercase;
}

.emotion-6 .amp-cms--eyebrow-2 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(11px, 1.1111111111111112vw);
  line-height: 1.75;
  letter-spacing: 0.1111111111111111vw;
  font-weight: 700;
  text-transform: uppercase;
}

.emotion-6 .amp-cms--eyebrow-3 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, 0.8333333333333334vw);
  line-height: 1.8333333333333333;
  letter-spacing: 0.056944444444444436vw;
  font-weight: 700;
  text-transform: uppercase;
}

.emotion-6 .amp-cms--headline-1 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, 4.166666666666666vw);
  line-height: 1;
  letter-spacing: 0.16666666666666666vw;
  text-transform: uppercase;
  font-weight: 500;
}

.emotion-6 .amp-cms--headline-2 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, 3.888888888888889vw);
  line-height: 1;
  letter-spacing: 0.19444444444444445vw;
  text-transform: uppercase;
  font-weight: 500;
}

.emotion-6 .amp-cms--headline-3 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, 3.4722222222222223vw);
  line-height: 1;
  letter-spacing: 0.1736111111111111vw;
  text-transform: uppercase;
  font-weight: 500;
}

.emotion-6 .amp-cms--headline-4 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, 2.7777777777777777vw);
  line-height: 1;
  letter-spacing: 0.19444444444444445vw;
  text-transform: uppercase;
  font-weight: 500;
}

.emotion-6 .amp-cms--headline-5 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, 2.5vw);
  line-height: 1;
  letter-spacing: 0.25vw;
  text-transform: uppercase;
  font-weight: 600;
}

.emotion-6 .amp-cms--headline-6 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, 1.9444444444444444vw);
  line-height: 1.1428571428571428;
  letter-spacing: 0.1048611111111111vw;
  text-transform: none;
  font-weight: 600;
}

.emotion-6 .amp-cms--headline-7 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, 1.3888888888888888vw);
  line-height: 1.2;
  letter-spacing: 0.09027777777777779vw;
  text-transform: uppercase;
  font-weight: 700;
}

.emotion-6 .amp-cms--headlineAlt-1 {
  color: #000000;
  font-family: var(--font-soehne),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, 8.61111111111111vw);
  line-height: 1;
  letter-spacing: -0.027777777777777776vw;
  font-weight: 400;
}

.emotion-6 .amp-cms--headlineAlt-2 {
  color: #000000;
  font-family: var(--font-soehne),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, 5.833333333333333vw);
  line-height: 1;
  letter-spacing: -0.027777777777777776vw;
  font-weight: 400;
}

.emotion-6 .amp-cms--headlineAlt-3 {
  color: #000000;
  font-family: var(--font-soehne),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, 4.444444444444445vw);
  line-height: 1;
  letter-spacing: -0.027777777777777776vw;
  font-weight: 400;
}

.emotion-6 .amp-cms--headlineAlt-4 {
  color: #000000;
  font-family: var(--font-soehne),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, 2.361111111111111vw);
  line-height: 1;
  letter-spacing: -0.027777777777777776vw;
  font-weight: 400;
}

.emotion-6 .amp-cms--headlineAlt-5 {
  color: #000000;
  font-family: var(--font-soehne),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 1.1111111111111112vw;
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-6 .amp-cms--headlineAlt-6 {
  color: #000000;
  font-family: var(--font-soehne),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 1.1111111111111112vw;
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-6 .amp-cms--headlineAlt-7 {
  color: #000000;
  font-family: var(--font-soehne),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 1.1111111111111112vw;
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-6 .amp-cms--promo-1 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, 6.944444444444445vw);
  line-height: 1;
  letter-spacing: 0vw;
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-6 .amp-cms--promo-2 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, 5.555555555555555vw);
  line-height: 1;
  letter-spacing: 0vw;
  font-weight: 600;
}

.emotion-6 .amp-cms--promoAlt-1 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 1.1111111111111112vw;
  line-height: 0.0625;
  letter-spacing: 0;
}

.emotion-6 .amp-cms--subhead-1 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, 1.6666666666666667vw);
  line-height: 1.5;
  letter-spacing: 0.08333333333333333vw;
  font-weight: 400;
  text-transform: uppercase;
}

.emotion-6 .amp-cms--subhead-2 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, 1.25vw);
  line-height: 1.5555555555555556;
  letter-spacing: 0.025vw;
  font-weight: 500;
  text-transform: uppercase;
}

.emotion-6 .amp-cms--subhead-3 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, 0.9722222222222222vw);
  line-height: 1.1428571428571428;
  letter-spacing: 0.04861111111111111vw;
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-6 a {
  pointer-events: auto;
}

.emotion-7 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  gap: 15px;
}

.emotion-7 a {
  -webkit-align-self: center;
  -ms-flex-item-align: center;
  align-self: center;
}

.emotion-8 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: none;
  transition: none;
  box-sizing: border-box;
  border: 1.5px solid transparent;
  border-radius: 0;
  font-size: 16px;
  font-weight: 500;
  letter-spacing: 0px;
  min-height: auto;
  max-height: 60px;
  line-height: 1.25;
  padding: 22px 40px;
  width: auto;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  background-color: #FFFFFF;
  color: #000000;
  border-color: #000000;
  -webkit-align-self: flex-start;
  -ms-flex-item-align: flex-start;
  align-self: flex-start;
  white-space: normal;
  pointer-events: auto;
}

.emotion-8:focus {
  outline: none;
}

.emotion-8>span {
  padding: 1px 0;
}

.emotion-8:hover,
.emotion-8:focus {
  text-shadow: 0 0 2px currentColor;
}

.emotion-8:active {
  color: #FFFFFF;
  background-color: #000000;
  border-color: #000000;
  -webkit-text-decoration: none;
  text-decoration: none;
  text-shadow: none;
}

.emotion-9 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: none;
  transition: none;
  box-sizing: border-box;
  border: 1.5px solid transparent;
  border-radius: 0;
  font-size: 16px;
  font-weight: 500;
  letter-spacing: 0px;
  min-height: auto;
  max-height: auto;
  line-height: 1.3125;
  padding: 0;
  width: auto;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: left;
  border-radius: 0;
  border: none;
  background-color: transparent;
  color: #000000;
  height: auto;
  -webkit-box-pack: left;
  -ms-flex-pack: left;
  -webkit-justify-content: left;
  justify-content: left;
  -webkit-align-self: flex-start;
  -ms-flex-item-align: flex-start;
  align-self: flex-start;
  white-space: normal;
  pointer-events: auto;
}

.emotion-9:focus {
  outline: none;
}

.emotion-9>span span {
  height: calc(16px * 0.7133333333333334);
}

.emotion-9 span span {
  padding-left: 3px;
  min-width: auto;
  min-height: auto;
  position: relative;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-transition: 250ms ease-in-out;
  transition: 250ms ease-in-out;
  transition-property: height,width,margin;
  top: 0;
  width: calc(1.2rem * 0.72);
}

.emotion-9 span span svg {
  -webkit-transition: fill 250ms ease-in-out;
  transition: fill 250ms ease-in-out;
  fill: #000000;
}

.emotion-9:hover,
.emotion-9:focus {
  text-shadow: 0 0 2px currentColor;
}

.emotion-9:active {
  text-transform: uppercase;
  text-shadow: none;
}

.emotion-10 {
  box-sizing: border-box;
}

.emotion-11 {
  display: inline-block;
  height: 2rem;
  width: 2rem;
  min-height: 2rem;
  min-width: 2rem;
}

.emotion-11 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-12 {
  position: relative;
  aspect-ratio: 16/9;
}

.emotion-12>div {
  height: 100%;
}

.emotion-12>img {
  position: absolute;
  top: 0;
}

.emotion-13 {
  width: -webkit-fit-content;
  width: -moz-fit-content;
  width: fit-content;
  position: relative;
  width: 100%;
}

.emotion-14>button {
  z-index: 1;
}

.emotion-15 {
  background: none;
  border: none;
  padding: 0;
  height: 24px;
  margin: 0;
  position: absolute;
  bottom: 8px;
  right: 112px;
  width: 24px;
  height: 24px;
}

.emotion-15:is(:not(:focus, :active, :hover, :focus-within)) {
  opacity: 0.64;
}

.emotion-15:is(:hover) {
  opacity: 1;
}

@media (min-width: 767px) {
  .emotion-15 {
    right: 120px;
  }
}

.emotion-16 {
  box-sizing: border-box;
  background: none;
  border: none;
  padding: 0;
  position: relative;
  width: 100%;
  height: 0px;
}

.emotion-16:focus div {
  opacity: 0.84;
  -webkit-transition: opacity 200ms;
  transition: opacity 200ms;
  border-radius: 20px 20px 0 0;
}

.emotion-16 div {
  border-radius: 20px 20px 0 0;
}

.emotion-16 .keepOpen,
.emotion-16 .staysOpen {
  opacity: 0.84;
  -webkit-transition: opacity 200ms;
  transition: opacity 200ms;
  z-index: 2;
}

.emotion-17 {
  background: none;
  border: none;
  padding: 0;
  height: 24px;
  margin: 0;
  position: absolute;
  bottom: 8px;
  right: 72px;
  z-index: 11;
}

.emotion-17:is(:not(:focus, :active, :hover, :focus-within)) {
  opacity: 0.64;
}

.emotion-17:is(:hover) {
  opacity: 1;
}

@media (min-width: 767px) {
  .emotion-17 {
    right: 80px;
  }
}

.emotion-18 {
  opacity: 0;
  -webkit-transition: opacity 200ms;
  transition: opacity 200ms;
  box-sizing: border-box;
  background: #ffffff;
  border: none;
  padding: 0;
  position: absolute;
  width: 24px;
  height: 60px;
  bottom: 32px;
  right: 80px;
  border-radius: 20px 20px 0 0;
}

.emotion-18:before {
  content: "";
  height: 24px;
  background: #ffffff;
  display: block;
  position: absolute;
  bottom: -24px;
  width: 24px;
  border-bottom-right-radius: 50%;
  border-bottom-left-radius: 50%;
}

.emotion-19 {
  height: 100%;
  width: 100%;
  position: absolute;
  right: 0;
  bottom: 0;
}

.emotion-20 {
  box-sizing: border-box;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: end;
  -webkit-box-align: end;
  -ms-flex-align: end;
  align-items: end;
  gap: 10px;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  justify-content: space-between;
  position: absolute;
  padding: 0 15px 10px 15px;
  -webkit-flex-direction: row-reverse;
  -ms-flex-direction: row-reverse;
  flex-direction: row-reverse;
  bottom: 0;
  width: 100%;
  background: linear-gradient(0deg, rgba(0, 0, 0, 0.4) 50%, rgba(0, 0, 0, 0) 100%);
}

.emotion-21 {
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
}

.emotion-22 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: none;
  transition: none;
  box-sizing: border-box;
  border: 1.5px solid transparent;
  border-radius: 0;
  font-size: 16px;
  font-weight: 500;
  letter-spacing: 0px;
  min-height: auto;
  max-height: auto;
  line-height: 1.3125;
  padding: 0;
  width: auto;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: left;
  border-radius: 0;
  border: none;
  background-color: transparent;
  color: #000000;
  height: auto;
  -webkit-box-pack: left;
  -ms-flex-pack: left;
  -webkit-justify-content: left;
  justify-content: left;
  font-weight: normal;
  text-transform: none;
  z-index: 1;
  -webkit-text-decoration: underline;
  text-decoration: underline;
  font-size: 12px;
  line-height: 16px;
  min-height: initial;
  padding: 0;
  margin-left: unset;
  color: #FFFFFF;
  pointer-events: auto;
  text-underline-offset: 2px;
}

.emotion-22:focus {
  outline: none;
}

.emotion-22>span {
  padding: 1px 0;
}

.emotion-22 span span {
  padding-left: 3px;
  min-width: auto;
  min-height: auto;
  position: relative;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-transition: 250ms ease-in-out;
  transition: 250ms ease-in-out;
  transition-property: height,width,margin;
  top: 0;
  width: calc(1.2rem * 0.72);
}

.emotion-22 span span svg {
  -webkit-transition: fill 250ms ease-in-out;
  transition: fill 250ms ease-in-out;
  fill: #000000;
}

.emotion-22 span span {
  padding-left: initial;
}

.emotion-22:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-22:focus-visible {
  outline: auto;
}

<div>
  <div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <div
        class="emotion-0"
        height="0"
        width="0"
      >
        <div
          class="emotion-1"
        >
          <div
            class="emotion-2"
          >
            <div
              class="emotion-3"
            >
              <div
                class="emotion-4"
                data-testid="advance-image-container-test-id"
              >
                <img
                  alt="Moisture Wicking"
                  class="emotion-5"
                  maxminheight="105px"
                  src="https://athleta.a.bigcontent.io/v1/static/WATER-RESISTANT_dark"
                />
              </div>
              <div
                class="emotion-6"
              >
                <div>
                  <p
                    class="amp-cms--p"
                  >
                    <span
                      class="amp-cms--body-1"
                    >
                      Spotlight Video
                    </span>
                  </p>
                </div>
              </div>
              <div
                class="emotion-7"
              >
                <a
                  class="emotion-8"
                  color="dark"
                  href="/1"
                >
                  First CTA
                </a>
                <a
                  class="emotion-9"
                  color="dark"
                  href="https://www.google.com/"
                >
                  <span
                    class="emotion-10"
                  >
                    Second CTA2
                    <span
                      aria-hidden="true"
                      class="emotion-11"
                    />
                  </span>
                </a>
              </div>
            </div>
          </div>
        </div>
        <div
          class="emotion-12"
        >
          <div
            class="emotion-13"
            data-testid="videocomponent-container"
          >
            <div
              style="position: relative;"
            >
              <img
                fetchpriority="high"
                src="https://fmzlik0m0z6r1oifp8ex832rf.staging.bigcontent.io/i/oldnavy/FA23_D3_Banner_NA_IMG1_XL?fmt=auto"
                style="height: 100%; left: 0px; object-fit: cover; position: absolute; top: 0px; width: 100%;"
              />
              <h1
                role="presentation"
              >
                ReactPlayer
              </h1>
            </div>
            <div
              class="player-custom-controls emotion-14"
              data-testid="player-custom-controls"
            >
              <button
                aria-label="Play"
                aria-pressed="false"
                class="emotion-15"
              >
                <svg
                  aria-label="play-button"
                  fill="none"
                  height="24"
                  viewBox="0 0 24 24"
                  width="24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M24 12C24 18.6275 18.6275 24 12 24C5.3725 24 0 18.6275 0 12C0 5.3725 5.3725 0 12 0C18.6275 0 24 5.3725 24 12Z"
                    fill="#FFFFFF"
                  />
                  <path
                    d="M7.5 7.43526C7.5 6.78726 8.19225 6.37326 8.76375 6.68076L17.241 11.246C17.3771 11.3194 17.4909 11.4283 17.5702 11.5612C17.6494 11.694 17.6913 11.8458 17.6913 12.0005C17.6913 12.1552 17.6494 12.307 17.5702 12.4398C17.4909 12.5727 17.3771 12.6816 17.241 12.755L8.76375 17.3203C8.63317 17.3906 8.48659 17.4258 8.33831 17.4226C8.19004 17.4193 8.04515 17.3776 7.91781 17.3016C7.79046 17.2255 7.68502 17.1178 7.61179 16.9888C7.53856 16.8598 7.50004 16.7141 7.5 16.5658V7.43526Z"
                    fill="#2C2824"
                  />
                </svg>
              </button>
              <div
                class="emotion-16"
                data-testid="styled-mute-controls"
              >
                <button
                  aria-label="Mute"
                  aria-pressed="false"
                  class="emotion-17"
                >
                  <svg
                    aria-label="mute-button"
                    fill="none"
                    height="24"
                    viewBox="0 0 24 24"
                    width="24"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M24 12C24 18.6275 18.6275 24 12 24C5.3725 24 0 18.6275 0 12C0 5.3725 5.3725 0 12 0C18.6275 0 24 5.3725 24 12Z"
                      fill="#FFFFFF"
                    />
                    <g>
                      <path
                        clip-rule="evenodd"
                        d="M14.4996 14.2613C15.7486 13.0124 15.7486 10.9876 14.4996 9.73866C14.28 9.51899 14.28 9.16283 14.4996 8.94316C14.7193 8.72349 15.0755 8.72349 15.2951 8.94316C16.9835 10.6315 16.9835 13.3685 15.2951 15.0568C15.0755 15.2765 14.7193 15.2765 14.4996 15.0568C14.28 14.8372 14.28 14.481 14.4996 14.2613Z"
                        fill="#2C2824"
                        fill-rule="evenodd"
                      />
                      <path
                        clip-rule="evenodd"
                        d="M16.4087 16.034C18.6368 13.8059 18.6368 10.1939 16.4087 7.96586C16.1891 7.74619 16.1891 7.39003 16.4087 7.17036C16.6284 6.95069 16.9846 6.95069 17.2042 7.17036C19.8716 9.83778 19.8716 14.1621 17.2042 16.8295C16.9846 17.0492 16.6284 17.0492 16.4087 16.8295C16.1891 16.6098 16.1891 16.2537 16.4087 16.034Z"
                        fill="#2C2824"
                        fill-rule="evenodd"
                      />
                      <path
                        d="M4.7383 12.8103V12V11.1897C4.7383 10.6374 5.18602 10.1897 5.7383 10.1897H7.14083C7.39437 10.1897 7.63844 10.0934 7.82366 9.92023L11.0042 6.94759C11.14 6.82063 11.319 6.75 11.5049 6.75C11.91 6.75 12.2383 7.07834 12.2383 7.48337V12V16.5166C12.2383 16.9217 11.91 17.25 11.5049 17.25C11.319 17.25 11.14 17.1794 11.0042 17.0524L7.82366 14.0798C7.63844 13.9066 7.39437 13.8103 7.14083 13.8103H5.7383C5.18602 13.8103 4.7383 13.3626 4.7383 12.8103Z"
                        fill="#2C2824"
                      />
                      <rect
                        fill="#2C2824"
                        height="19.3235"
                        transform="rotate(120 21.2347 16.4121)"
                        width="1.5"
                        x="21.2347"
                        y="16.4121"
                      />
                    </g>
                  </svg>
                </button>
                <div
                  class="emotion-18"
                  data-testid="styled-input-volume"
                  style="background: rgb(255, 255, 255);"
                >
                  <div
                    role="presentation"
                  />
                </div>
              </div>
            </div>
          </div>
          <div
            class="emotion-19"
          >
            <div
              class="emotion-20"
            >
              <div
                class="emotion-21"
              >
                <button
                  class="emotion-22"
                >
                  Details
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`SpotlightVideo Content Type FullBleed should match mobile 1`] = `
.emotion-0 {
  background: #D10B25;
  position: relative;
  width: 100%;
  height: 100%;
  box-sizing: border-box;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  padding-top: 0;
  padding: 0;
}

.emotion-1 {
  -webkit-align-self: center;
  -ms-flex-item-align: center;
  align-self: center;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  width: 100%;
  gap: 20px;
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  -webkit-align-items: end;
  -webkit-box-align: end;
  -ms-flex-align: end;
  align-items: end;
  pointer-events: none;
  z-index: 1;
  width: 100%;
  -webkit-align-items: end;
  -webkit-box-align: end;
  -ms-flex-align: end;
  align-items: end;
}

.emotion-1>div {
  width: 100%;
}

.emotion-2 {
  width: 100%;
}

.emotion-3 {
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  padding: 50px 30px;
  -webkit-align-items: end;
  -webkit-box-align: end;
  -ms-flex-align: end;
  align-items: end;
  -webkit-box-pack: right;
  -ms-flex-pack: right;
  -webkit-justify-content: right;
  justify-content: right;
  gap: 20px;
}

.emotion-4 {
  height: auto;
  max-width: 100%;
}

.emotion-5 {
  min-height: 80px;
  max-height: 80px;
}

.emotion-6 {
  text-align: right;
}

.emotion-6 .amp-cms--p {
  padding: 0;
  margin: 0;
  line-height: 0;
}

.emotion-6 .amp-cms--p>span {
  display: inline;
  white-space: break-spaces;
}

.emotion-6 .amp-cms--p a {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  position: relative;
  z-index: 2;
  pointer-events: auto;
}

.emotion-6 .amp-cms--p a:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-6 .amp-cms--legal-copy {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, 2.666666666666667vw);
  line-height: 1.5;
  letter-spacing: 0.13333333333333333vw;
  font-weight: 500;
}

.emotion-6 sup {
  vertical-align: top;
  display: inline-block;
  margin-top: -0.25ex;
}

.emotion-6 sub {
  vertical-align: bottom;
  display: inline-block;
  margin-bottom: -0.25ex;
}

.emotion-6 .amp-cms--f-0 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 2.933333333333333vw;
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-6 .amp-cms--f-1 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 2.933333333333333vw;
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-6 .amp-cms--f-2 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 2.933333333333333vw;
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-6 .amp-cms--fn-1 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 2.933333333333333vw;
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-6 .amp-cms--body-1 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, 4.266666666666667vw);
  line-height: 1.625;
  letter-spacing: 0.21333333333333335vw;
  font-weight: 500;
}

.emotion-6 .amp-cms--body-2 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, 3.733333333333334vw);
  line-height: 1.4285714285714286;
  letter-spacing: 0vw;
  font-weight: 500;
}

.emotion-6 .amp-cms--body-3 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, 3.2vw);
  line-height: 1.5;
  letter-spacing: 0.15999999999999998vw;
  font-weight: 500;
}

.emotion-6 .amp-cms--body-4 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, 2.666666666666667vw);
  line-height: 1.6;
  letter-spacing: 0vw;
  font-weight: 500;
}

.emotion-6 .amp-cms--body-5 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, 2.666666666666667vw);
  line-height: 1.5;
  letter-spacing: 0.13333333333333333vw;
  font-weight: 500;
}

.emotion-6 .amp-cms--eyebrow-1 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, 3.2vw);
  line-height: 2;
  letter-spacing: 0.31999999999999995vw;
  font-weight: 700;
  text-transform: uppercase;
}

.emotion-6 .amp-cms--eyebrow-2 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(11px, 2.933333333333333vw);
  line-height: 2;
  letter-spacing: 0.29333333333333333vw;
  font-weight: 700;
  text-transform: uppercase;
}

.emotion-6 .amp-cms--eyebrow-3 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, 2.666666666666667vw);
  line-height: 1.5;
  letter-spacing: 0.18133333333333335vw;
  font-weight: 700;
  text-transform: uppercase;
}

.emotion-6 .amp-cms--headline-1 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(13px, 9.6vw);
  line-height: 1;
  letter-spacing: 0.36533333333333334vw;
  text-transform: uppercase;
  font-weight: 500;
}

.emotion-6 .amp-cms--headline-2 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(13px, 9.066666666666666vw);
  line-height: 1;
  letter-spacing: 0.45333333333333325vw;
  text-transform: uppercase;
  font-weight: 500;
}

.emotion-6 .amp-cms--headline-3 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(13px, 8vw);
  line-height: 1;
  letter-spacing: 0.07999999999999999vw;
  text-transform: uppercase;
  font-weight: 500;
}

.emotion-6 .amp-cms--headline-4 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(13px, 6.4vw);
  line-height: 1;
  letter-spacing: 0.48000000000000004vw;
  text-transform: uppercase;
  font-weight: 500;
}

.emotion-6 .amp-cms--headline-5 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(13px, 5.866666666666666vw);
  line-height: 1.1818181818181819;
  letter-spacing: 0.5866666666666667vw;
  text-transform: uppercase;
  font-weight: 600;
}

.emotion-6 .amp-cms--headline-6 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(13px, 4.266666666666667vw);
  line-height: 1.375;
  letter-spacing: 0.21333333333333335vw;
  text-transform: none;
  font-weight: 600;
}

.emotion-6 .amp-cms--headline-7 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(13px, 3.733333333333334vw);
  line-height: 1.4285714285714286;
  letter-spacing: 0.02666666666666667vw;
  text-transform: uppercase;
  font-weight: 700;
}

.emotion-6 .amp-cms--headlineAlt-1 {
  color: #000000;
  font-family: var(--font-soehne),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(13px, 17.066666666666666vw);
  line-height: 1;
  letter-spacing: -0.07999999999999999vw;
  font-weight: 400;
}

.emotion-6 .amp-cms--headlineAlt-2 {
  color: #000000;
  font-family: var(--font-soehne),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(13px, 14.399999999999999vw);
  line-height: 1;
  letter-spacing: -0.07999999999999999vw;
  font-weight: 400;
}

.emotion-6 .amp-cms--headlineAlt-3 {
  color: #000000;
  font-family: var(--font-soehne),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(13px, 9.066666666666666vw);
  line-height: 1;
  letter-spacing: -0.07999999999999999vw;
  font-weight: 400;
}

.emotion-6 .amp-cms--headlineAlt-4 {
  color: #000000;
  font-family: var(--font-soehne),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(13px, 6.4vw);
  line-height: 1.4166666666666667;
  letter-spacing: -0.07999999999999999vw;
  font-weight: 400;
}

.emotion-6 .amp-cms--headlineAlt-5 {
  color: #000000;
  font-family: var(--font-soehne),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 3.733333333333334vw;
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-6 .amp-cms--headlineAlt-6 {
  color: #000000;
  font-family: var(--font-soehne),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 3.733333333333334vw;
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-6 .amp-cms--headlineAlt-7 {
  color: #000000;
  font-family: var(--font-soehne),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 3.733333333333334vw;
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-6 .amp-cms--promo-1 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(13px, 16vw);
  line-height: 1;
  letter-spacing: 0vw;
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-6 .amp-cms--promo-2 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(13px, 10.666666666666668vw);
  line-height: 1;
  letter-spacing: 0vw;
  font-weight: 600;
}

.emotion-6 .amp-cms--promoAlt-1 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 3.733333333333334vw;
  line-height: 0.07142857142857142;
  letter-spacing: 0;
}

.emotion-6 .amp-cms--subhead-1 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(13px, 4.266666666666667vw);
  line-height: 1.75;
  letter-spacing: 0.08533333333333333vw;
  font-weight: 400;
  text-transform: uppercase;
}

.emotion-6 .amp-cms--subhead-2 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(13px, 3.733333333333334vw);
  line-height: 1.5714285714285714;
  letter-spacing: 0.18666666666666668vw;
  font-weight: 500;
  text-transform: uppercase;
}

.emotion-6 .amp-cms--subhead-3 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(13px, 3.4666666666666663vw);
  line-height: 1;
  letter-spacing: 0.25866666666666666vw;
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-6 a {
  pointer-events: auto;
}

.emotion-7 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  gap: 15px;
}

.emotion-7 a {
  -webkit-align-self: center;
  -ms-flex-item-align: center;
  align-self: center;
}

.emotion-8 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: none;
  transition: none;
  box-sizing: border-box;
  border: 1.5px solid transparent;
  border-radius: 0;
  font-size: 14px;
  font-weight: 500;
  letter-spacing: 0px;
  min-height: auto;
  max-height: 50px;
  line-height: 1.2857142857142858;
  padding: 18px 40px;
  width: auto;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  background-color: #FFFFFF;
  color: #000000;
  border-color: #000000;
  -webkit-align-self: flex-start;
  -ms-flex-item-align: flex-start;
  align-self: flex-start;
  white-space: normal;
  pointer-events: auto;
}

.emotion-8:focus {
  outline: none;
}

.emotion-8>span {
  padding: 1px 0;
}

.emotion-8:hover,
.emotion-8:focus {
  text-shadow: 0 0 2px currentColor;
}

.emotion-8:active {
  color: #FFFFFF;
  background-color: #000000;
  border-color: #000000;
  -webkit-text-decoration: none;
  text-decoration: none;
  text-shadow: none;
}

.emotion-9 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: none;
  transition: none;
  box-sizing: border-box;
  border: 1.5px solid transparent;
  border-radius: 0;
  font-size: 14px;
  font-weight: 500;
  letter-spacing: 0px;
  min-height: 44px;
  max-height: auto;
  line-height: 1.1428571428571428;
  padding: 0;
  width: auto;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: left;
  border-radius: 0;
  border: none;
  background-color: transparent;
  color: #000000;
  height: auto;
  -webkit-box-pack: left;
  -ms-flex-pack: left;
  -webkit-justify-content: left;
  justify-content: left;
  -webkit-align-self: flex-start;
  -ms-flex-item-align: flex-start;
  align-self: flex-start;
  white-space: normal;
  pointer-events: auto;
}

.emotion-9:focus {
  outline: none;
}

.emotion-9>span span {
  height: calc(14px * 0.7133333333333334);
}

.emotion-9 span span {
  padding-left: 3px;
  min-width: auto;
  min-height: auto;
  position: relative;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-transition: 250ms ease-in-out;
  transition: 250ms ease-in-out;
  transition-property: height,width,margin;
  top: 0;
  width: calc(0.8rem * 0.72);
}

.emotion-9 span span svg {
  -webkit-transition: fill 250ms ease-in-out;
  transition: fill 250ms ease-in-out;
  fill: #000000;
}

.emotion-9:hover,
.emotion-9:focus {
  text-shadow: 0 0 2px currentColor;
}

.emotion-9:active {
  text-transform: uppercase;
  text-shadow: none;
}

.emotion-10 {
  box-sizing: border-box;
}

.emotion-11 {
  display: inline-block;
  height: 2rem;
  width: 2rem;
  min-height: 2rem;
  min-width: 2rem;
}

.emotion-11 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-12 {
  position: relative;
  aspect-ratio: 9/16;
}

.emotion-12>div {
  height: 100%;
}

.emotion-13 {
  width: -webkit-fit-content;
  width: -moz-fit-content;
  width: fit-content;
  position: relative;
  width: 100%;
}

.emotion-14>button {
  z-index: 1;
}

.emotion-15 {
  background: none;
  border: none;
  padding: 0;
  height: 24px;
  margin: 0;
  position: absolute;
  bottom: 8px;
  right: 112px;
  width: 24px;
  height: 24px;
}

.emotion-15:is(:not(:focus, :active, :hover, :focus-within)) {
  opacity: 0.64;
}

.emotion-15:is(:hover) {
  opacity: 1;
}

@media (min-width: 767px) {
  .emotion-15 {
    right: 120px;
  }
}

.emotion-16 {
  box-sizing: border-box;
  background: none;
  border: none;
  padding: 0;
  position: relative;
  width: 100%;
  height: 0px;
}

.emotion-16:focus div {
  opacity: 0.84;
  -webkit-transition: opacity 200ms;
  transition: opacity 200ms;
  border-radius: 20px 20px 0 0;
}

.emotion-16 div {
  border-radius: 20px 20px 0 0;
}

.emotion-16 .keepOpen,
.emotion-16 .staysOpen {
  opacity: 0.84;
  -webkit-transition: opacity 200ms;
  transition: opacity 200ms;
  z-index: 2;
}

.emotion-17 {
  background: none;
  border: none;
  padding: 0;
  height: 24px;
  margin: 0;
  position: absolute;
  bottom: 8px;
  right: 72px;
  z-index: 11;
}

.emotion-17:is(:not(:focus, :active, :hover, :focus-within)) {
  opacity: 0.64;
}

.emotion-17:is(:hover) {
  opacity: 1;
}

@media (min-width: 767px) {
  .emotion-17 {
    right: 80px;
  }
}

.emotion-18 {
  opacity: 0;
  -webkit-transition: opacity 200ms;
  transition: opacity 200ms;
  box-sizing: border-box;
  background: #ffffff;
  border: none;
  padding: 0;
  position: absolute;
  width: 24px;
  height: 60px;
  bottom: 32px;
  right: 72px;
  border-radius: 20px 20px 0 0;
}

.emotion-18:before {
  content: "";
  height: 24px;
  background: #ffffff;
  display: block;
  position: absolute;
  bottom: -24px;
  width: 24px;
  border-bottom-right-radius: 50%;
  border-bottom-left-radius: 50%;
}

.emotion-19 {
  height: 100%;
  width: 100%;
  position: absolute;
  right: 0;
  bottom: 0;
}

.emotion-20 {
  box-sizing: border-box;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: end;
  -webkit-box-align: end;
  -ms-flex-align: end;
  align-items: end;
  gap: 10px;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  justify-content: space-between;
  position: absolute;
  padding: 0 10px 10px 10px;
  -webkit-flex-direction: row-reverse;
  -ms-flex-direction: row-reverse;
  flex-direction: row-reverse;
  bottom: 0;
  width: 100%;
  background: linear-gradient(0deg, rgba(0, 0, 0, 0.4) 50%, rgba(0, 0, 0, 0) 100%);
}

.emotion-21 {
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
}

.emotion-22 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: none;
  transition: none;
  box-sizing: border-box;
  border: 1.5px solid transparent;
  border-radius: 0;
  font-size: 16px;
  font-weight: 500;
  letter-spacing: 0px;
  min-height: 44px;
  max-height: auto;
  line-height: 1.3125;
  padding: 0;
  width: auto;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: left;
  border-radius: 0;
  border: none;
  background-color: transparent;
  color: #000000;
  height: auto;
  -webkit-box-pack: left;
  -ms-flex-pack: left;
  -webkit-justify-content: left;
  justify-content: left;
  font-weight: normal;
  text-transform: none;
  z-index: 1;
  -webkit-text-decoration: underline;
  text-decoration: underline;
  font-size: 11px;
  line-height: 14px;
  min-height: initial;
  padding: 0;
  margin-left: unset;
  color: #FFFFFF;
  pointer-events: auto;
  text-underline-offset: 2px;
}

.emotion-22:focus {
  outline: none;
}

.emotion-22>span {
  padding: 1px 0;
}

.emotion-22 span span {
  padding-left: 3px;
  min-width: auto;
  min-height: auto;
  position: relative;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-transition: 250ms ease-in-out;
  transition: 250ms ease-in-out;
  transition-property: height,width,margin;
  top: 0;
  width: calc(1.2rem * 0.72);
}

.emotion-22 span span svg {
  -webkit-transition: fill 250ms ease-in-out;
  transition: fill 250ms ease-in-out;
  fill: #000000;
}

.emotion-22 span span {
  padding-left: initial;
}

.emotion-22:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-22:focus-visible {
  outline: auto;
}

<div>
  <div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <div
        class="emotion-0"
        height="0"
        width="0"
      >
        <div
          class="emotion-1"
        >
          <div
            class="emotion-2"
          >
            <div
              class="emotion-3"
            >
              <div
                class="emotion-4"
                data-testid="advance-image-container-test-id"
              >
                <img
                  alt="Athleta Girl"
                  class="emotion-5"
                  maxminheight="80px"
                  src="https://2hmc8flz4ofg1dy89cc38f2xm.staging.bigcontent.io/i/athleta/GIRL_logo@2x?fmt=auto"
                />
              </div>
              <div
                class="emotion-6"
              >
                <div>
                  <p
                    class="amp-cms--p"
                  >
                    <span
                      class="amp-cms--body-1"
                      style="color:#F0F"
                    >
                      Mobile Override
                    </span>
                  </p>
                </div>
              </div>
              <div
                class="emotion-7"
              >
                <a
                  class="emotion-8"
                  color="dark"
                  href="/1"
                >
                  First CTA
                </a>
                <a
                  class="emotion-9"
                  color="dark"
                  href="https://www.google.com/"
                >
                  <span
                    class="emotion-10"
                  >
                    Second CTA2
                    <span
                      aria-hidden="true"
                      class="emotion-11"
                    />
                  </span>
                </a>
              </div>
            </div>
          </div>
        </div>
        <div
          class="emotion-12"
        >
          <div
            class="emotion-13"
            data-testid="videocomponent-container"
          >
            <div
              style="position: relative;"
            >
              <img
                fetchpriority="high"
                src="https://fmzlik0m0z6r1oifp8ex832rf.staging.bigcontent.io/i/oldnavy/230901_14-M5283_LaborDay_CatNav_Tops_HP_US_XL?fmt=auto"
                style="height: 100%; left: 0px; object-fit: cover; position: absolute; top: 0px; width: 100%;"
              />
              <h1
                role="presentation"
              >
                ReactPlayer
              </h1>
            </div>
            <div
              class="player-custom-controls emotion-14"
              data-testid="player-custom-controls"
            >
              <button
                aria-label="Play"
                aria-pressed="false"
                class="emotion-15"
              >
                <svg
                  aria-label="play-button"
                  fill="none"
                  height="24"
                  viewBox="0 0 24 24"
                  width="24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M24 12C24 18.6275 18.6275 24 12 24C5.3725 24 0 18.6275 0 12C0 5.3725 5.3725 0 12 0C18.6275 0 24 5.3725 24 12Z"
                    fill="#FFFFFF"
                  />
                  <path
                    d="M7.5 7.43526C7.5 6.78726 8.19225 6.37326 8.76375 6.68076L17.241 11.246C17.3771 11.3194 17.4909 11.4283 17.5702 11.5612C17.6494 11.694 17.6913 11.8458 17.6913 12.0005C17.6913 12.1552 17.6494 12.307 17.5702 12.4398C17.4909 12.5727 17.3771 12.6816 17.241 12.755L8.76375 17.3203C8.63317 17.3906 8.48659 17.4258 8.33831 17.4226C8.19004 17.4193 8.04515 17.3776 7.91781 17.3016C7.79046 17.2255 7.68502 17.1178 7.61179 16.9888C7.53856 16.8598 7.50004 16.7141 7.5 16.5658V7.43526Z"
                    fill="#2C2824"
                  />
                </svg>
              </button>
              <div
                class="emotion-16"
                data-testid="styled-mute-controls"
              >
                <button
                  aria-label="Mute"
                  aria-pressed="false"
                  class="emotion-17"
                >
                  <svg
                    aria-label="mute-button"
                    fill="none"
                    height="24"
                    viewBox="0 0 24 24"
                    width="24"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M24 12C24 18.6275 18.6275 24 12 24C5.3725 24 0 18.6275 0 12C0 5.3725 5.3725 0 12 0C18.6275 0 24 5.3725 24 12Z"
                      fill="#FFFFFF"
                    />
                    <g>
                      <path
                        clip-rule="evenodd"
                        d="M14.4996 14.2613C15.7486 13.0124 15.7486 10.9876 14.4996 9.73866C14.28 9.51899 14.28 9.16283 14.4996 8.94316C14.7193 8.72349 15.0755 8.72349 15.2951 8.94316C16.9835 10.6315 16.9835 13.3685 15.2951 15.0568C15.0755 15.2765 14.7193 15.2765 14.4996 15.0568C14.28 14.8372 14.28 14.481 14.4996 14.2613Z"
                        fill="#2C2824"
                        fill-rule="evenodd"
                      />
                      <path
                        clip-rule="evenodd"
                        d="M16.4087 16.034C18.6368 13.8059 18.6368 10.1939 16.4087 7.96586C16.1891 7.74619 16.1891 7.39003 16.4087 7.17036C16.6284 6.95069 16.9846 6.95069 17.2042 7.17036C19.8716 9.83778 19.8716 14.1621 17.2042 16.8295C16.9846 17.0492 16.6284 17.0492 16.4087 16.8295C16.1891 16.6098 16.1891 16.2537 16.4087 16.034Z"
                        fill="#2C2824"
                        fill-rule="evenodd"
                      />
                      <path
                        d="M4.7383 12.8103V12V11.1897C4.7383 10.6374 5.18602 10.1897 5.7383 10.1897H7.14083C7.39437 10.1897 7.63844 10.0934 7.82366 9.92023L11.0042 6.94759C11.14 6.82063 11.319 6.75 11.5049 6.75C11.91 6.75 12.2383 7.07834 12.2383 7.48337V12V16.5166C12.2383 16.9217 11.91 17.25 11.5049 17.25C11.319 17.25 11.14 17.1794 11.0042 17.0524L7.82366 14.0798C7.63844 13.9066 7.39437 13.8103 7.14083 13.8103H5.7383C5.18602 13.8103 4.7383 13.3626 4.7383 12.8103Z"
                        fill="#2C2824"
                      />
                      <rect
                        fill="#2C2824"
                        height="19.3235"
                        transform="rotate(120 21.2347 16.4121)"
                        width="1.5"
                        x="21.2347"
                        y="16.4121"
                      />
                    </g>
                  </svg>
                </button>
                <div
                  class="emotion-18"
                  data-testid="styled-input-volume"
                  style="background: rgb(255, 255, 255);"
                >
                  <div
                    role="presentation"
                  />
                </div>
              </div>
            </div>
          </div>
          <div
            class="emotion-19"
          >
            <div
              class="emotion-20"
            >
              <div
                class="emotion-21"
              >
                <button
                  class="emotion-22"
                >
                  Details
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`SpotlightVideo Content Type Inset No content is provided should match desktop when no content is provided 1`] = `
.emotion-0 {
  background: #FFFABC;
  position: relative;
  width: 100%;
  height: 100%;
  box-sizing: border-box;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  padding-top: 20px;
  padding: 20px;
}

.emotion-1 {
  position: relative;
  aspect-ratio: 16/9;
}

.emotion-1>div {
  height: 100%;
}

.emotion-1>img {
  position: absolute;
  top: 0;
}

.emotion-2 {
  width: -webkit-fit-content;
  width: -moz-fit-content;
  width: fit-content;
  position: relative;
  width: 100%;
}

.emotion-3>button {
  z-index: 1;
}

.emotion-4 {
  background: none;
  border: none;
  padding: 0;
  height: 24px;
  margin: 0;
  position: absolute;
  bottom: 8px;
  right: 112px;
  width: 24px;
  height: 24px;
}

.emotion-4:is(:not(:focus, :active, :hover, :focus-within)) {
  opacity: 0.64;
}

.emotion-4:is(:hover) {
  opacity: 1;
}

@media (min-width: 767px) {
  .emotion-4 {
    right: 120px;
  }
}

.emotion-5 {
  box-sizing: border-box;
  background: none;
  border: none;
  padding: 0;
  position: relative;
  width: 100%;
  height: 0px;
}

.emotion-5:focus div {
  opacity: 0.84;
  -webkit-transition: opacity 200ms;
  transition: opacity 200ms;
  border-radius: 20px 20px 0 0;
}

.emotion-5 div {
  border-radius: 20px 20px 0 0;
}

.emotion-5 .keepOpen,
.emotion-5 .staysOpen {
  opacity: 0.84;
  -webkit-transition: opacity 200ms;
  transition: opacity 200ms;
  z-index: 2;
}

.emotion-6 {
  background: none;
  border: none;
  padding: 0;
  height: 24px;
  margin: 0;
  position: absolute;
  bottom: 8px;
  right: 72px;
  z-index: 11;
}

.emotion-6:is(:not(:focus, :active, :hover, :focus-within)) {
  opacity: 0.64;
}

.emotion-6:is(:hover) {
  opacity: 1;
}

@media (min-width: 767px) {
  .emotion-6 {
    right: 80px;
  }
}

.emotion-7 {
  opacity: 0;
  -webkit-transition: opacity 200ms;
  transition: opacity 200ms;
  box-sizing: border-box;
  background: #ffffff;
  border: none;
  padding: 0;
  position: absolute;
  width: 24px;
  height: 60px;
  bottom: 32px;
  right: 80px;
  border-radius: 20px 20px 0 0;
}

.emotion-7:before {
  content: "";
  height: 24px;
  background: #ffffff;
  display: block;
  position: absolute;
  bottom: -24px;
  width: 24px;
  border-bottom-right-radius: 50%;
  border-bottom-left-radius: 50%;
}

.emotion-8 {
  height: 100%;
  width: 100%;
  position: absolute;
  right: 0;
  bottom: 0;
}

.emotion-9 {
  box-sizing: border-box;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: end;
  -webkit-box-align: end;
  -ms-flex-align: end;
  align-items: end;
  gap: 10px;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  justify-content: space-between;
  position: absolute;
  padding: 0 15px 10px 15px;
  -webkit-flex-direction: row-reverse;
  -ms-flex-direction: row-reverse;
  flex-direction: row-reverse;
  bottom: 0;
  width: 100%;
  background: linear-gradient(0deg, rgba(0, 0, 0, 0.4) 50%, rgba(0, 0, 0, 0) 100%);
}

.emotion-10 {
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
}

.emotion-11 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: none;
  transition: none;
  box-sizing: border-box;
  border: 1.5px solid transparent;
  border-radius: 0;
  font-size: 16px;
  font-weight: 500;
  letter-spacing: 0px;
  min-height: auto;
  max-height: auto;
  line-height: 1.3125;
  padding: 0;
  width: auto;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: left;
  border-radius: 0;
  border: none;
  background-color: transparent;
  color: #000000;
  height: auto;
  -webkit-box-pack: left;
  -ms-flex-pack: left;
  -webkit-justify-content: left;
  justify-content: left;
  font-weight: normal;
  text-transform: none;
  z-index: 1;
  -webkit-text-decoration: underline;
  text-decoration: underline;
  font-size: 12px;
  line-height: 16px;
  min-height: initial;
  padding: 0;
  margin-left: unset;
  color: #FFFFFF;
  pointer-events: auto;
  text-underline-offset: 2px;
}

.emotion-11:focus {
  outline: none;
}

.emotion-11>span {
  padding: 1px 0;
}

.emotion-11 span span {
  padding-left: 3px;
  min-width: auto;
  min-height: auto;
  position: relative;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-transition: 250ms ease-in-out;
  transition: 250ms ease-in-out;
  transition-property: height,width,margin;
  top: 0;
  width: calc(1.2rem * 0.72);
}

.emotion-11 span span svg {
  -webkit-transition: fill 250ms ease-in-out;
  transition: fill 250ms ease-in-out;
  fill: #000000;
}

.emotion-11 span span {
  padding-left: initial;
}

.emotion-11:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-11:focus-visible {
  outline: auto;
}

<div>
  <div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <div
        class="emotion-0"
        height="0"
        width="0"
      >
        <div
          class="emotion-1"
        >
          <div
            class="emotion-2"
            data-testid="videocomponent-container"
          >
            <div
              style="position: relative;"
            >
              <img
                fetchpriority="high"
                src="https://fmzlik0m0z6r1oifp8ex832rf.staging.bigcontent.io/i/oldnavy/FA23_D3_Banner_NA_IMG1_XL?fmt=auto"
                style="height: 100%; left: 0px; object-fit: cover; position: absolute; top: 0px; width: 100%;"
              />
              <h1
                role="presentation"
              >
                ReactPlayer
              </h1>
            </div>
            <div
              class="player-custom-controls emotion-3"
              data-testid="player-custom-controls"
            >
              <button
                aria-label="Play"
                aria-pressed="false"
                class="emotion-4"
              >
                <svg
                  aria-label="play-button"
                  fill="none"
                  height="24"
                  viewBox="0 0 24 24"
                  width="24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M24 12C24 18.6275 18.6275 24 12 24C5.3725 24 0 18.6275 0 12C0 5.3725 5.3725 0 12 0C18.6275 0 24 5.3725 24 12Z"
                    fill="#FFFFFF"
                  />
                  <path
                    d="M7.5 7.43526C7.5 6.78726 8.19225 6.37326 8.76375 6.68076L17.241 11.246C17.3771 11.3194 17.4909 11.4283 17.5702 11.5612C17.6494 11.694 17.6913 11.8458 17.6913 12.0005C17.6913 12.1552 17.6494 12.307 17.5702 12.4398C17.4909 12.5727 17.3771 12.6816 17.241 12.755L8.76375 17.3203C8.63317 17.3906 8.48659 17.4258 8.33831 17.4226C8.19004 17.4193 8.04515 17.3776 7.91781 17.3016C7.79046 17.2255 7.68502 17.1178 7.61179 16.9888C7.53856 16.8598 7.50004 16.7141 7.5 16.5658V7.43526Z"
                    fill="#2C2824"
                  />
                </svg>
              </button>
              <div
                class="emotion-5"
                data-testid="styled-mute-controls"
              >
                <button
                  aria-label="Mute"
                  aria-pressed="false"
                  class="emotion-6"
                >
                  <svg
                    aria-label="mute-button"
                    fill="none"
                    height="24"
                    viewBox="0 0 24 24"
                    width="24"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M24 12C24 18.6275 18.6275 24 12 24C5.3725 24 0 18.6275 0 12C0 5.3725 5.3725 0 12 0C18.6275 0 24 5.3725 24 12Z"
                      fill="#FFFFFF"
                    />
                    <g>
                      <path
                        clip-rule="evenodd"
                        d="M14.4996 14.2613C15.7486 13.0124 15.7486 10.9876 14.4996 9.73866C14.28 9.51899 14.28 9.16283 14.4996 8.94316C14.7193 8.72349 15.0755 8.72349 15.2951 8.94316C16.9835 10.6315 16.9835 13.3685 15.2951 15.0568C15.0755 15.2765 14.7193 15.2765 14.4996 15.0568C14.28 14.8372 14.28 14.481 14.4996 14.2613Z"
                        fill="#2C2824"
                        fill-rule="evenodd"
                      />
                      <path
                        clip-rule="evenodd"
                        d="M16.4087 16.034C18.6368 13.8059 18.6368 10.1939 16.4087 7.96586C16.1891 7.74619 16.1891 7.39003 16.4087 7.17036C16.6284 6.95069 16.9846 6.95069 17.2042 7.17036C19.8716 9.83778 19.8716 14.1621 17.2042 16.8295C16.9846 17.0492 16.6284 17.0492 16.4087 16.8295C16.1891 16.6098 16.1891 16.2537 16.4087 16.034Z"
                        fill="#2C2824"
                        fill-rule="evenodd"
                      />
                      <path
                        d="M4.7383 12.8103V12V11.1897C4.7383 10.6374 5.18602 10.1897 5.7383 10.1897H7.14083C7.39437 10.1897 7.63844 10.0934 7.82366 9.92023L11.0042 6.94759C11.14 6.82063 11.319 6.75 11.5049 6.75C11.91 6.75 12.2383 7.07834 12.2383 7.48337V12V16.5166C12.2383 16.9217 11.91 17.25 11.5049 17.25C11.319 17.25 11.14 17.1794 11.0042 17.0524L7.82366 14.0798C7.63844 13.9066 7.39437 13.8103 7.14083 13.8103H5.7383C5.18602 13.8103 4.7383 13.3626 4.7383 12.8103Z"
                        fill="#2C2824"
                      />
                      <rect
                        fill="#2C2824"
                        height="19.3235"
                        transform="rotate(120 21.2347 16.4121)"
                        width="1.5"
                        x="21.2347"
                        y="16.4121"
                      />
                    </g>
                  </svg>
                </button>
                <div
                  class="emotion-7"
                  data-testid="styled-input-volume"
                  style="background: rgb(255, 255, 255);"
                >
                  <div
                    role="presentation"
                  />
                </div>
              </div>
            </div>
          </div>
          <div
            class="emotion-8"
          >
            <div
              class="emotion-9"
            >
              <div
                class="emotion-10"
              >
                <button
                  class="emotion-11"
                >
                  Details
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`SpotlightVideo Content Type Inset No content is provided should match mobile when no content is provided 1`] = `
.emotion-0 {
  background: #D10B25;
  position: relative;
  width: 100%;
  height: 100%;
  box-sizing: border-box;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  padding-top: 10px;
  padding: 10px;
}

.emotion-1 {
  position: relative;
  aspect-ratio: 9/16;
}

.emotion-1>div {
  height: 100%;
}

.emotion-2 {
  width: -webkit-fit-content;
  width: -moz-fit-content;
  width: fit-content;
  position: relative;
  width: 100%;
}

.emotion-3>button {
  z-index: 1;
}

.emotion-4 {
  background: none;
  border: none;
  padding: 0;
  height: 24px;
  margin: 0;
  position: absolute;
  bottom: 8px;
  right: 112px;
  width: 24px;
  height: 24px;
}

.emotion-4:is(:not(:focus, :active, :hover, :focus-within)) {
  opacity: 0.64;
}

.emotion-4:is(:hover) {
  opacity: 1;
}

@media (min-width: 767px) {
  .emotion-4 {
    right: 120px;
  }
}

.emotion-5 {
  box-sizing: border-box;
  background: none;
  border: none;
  padding: 0;
  position: relative;
  width: 100%;
  height: 0px;
}

.emotion-5:focus div {
  opacity: 0.84;
  -webkit-transition: opacity 200ms;
  transition: opacity 200ms;
  border-radius: 20px 20px 0 0;
}

.emotion-5 div {
  border-radius: 20px 20px 0 0;
}

.emotion-5 .keepOpen,
.emotion-5 .staysOpen {
  opacity: 0.84;
  -webkit-transition: opacity 200ms;
  transition: opacity 200ms;
  z-index: 2;
}

.emotion-6 {
  background: none;
  border: none;
  padding: 0;
  height: 24px;
  margin: 0;
  position: absolute;
  bottom: 8px;
  right: 72px;
  z-index: 11;
}

.emotion-6:is(:not(:focus, :active, :hover, :focus-within)) {
  opacity: 0.64;
}

.emotion-6:is(:hover) {
  opacity: 1;
}

@media (min-width: 767px) {
  .emotion-6 {
    right: 80px;
  }
}

.emotion-7 {
  opacity: 0;
  -webkit-transition: opacity 200ms;
  transition: opacity 200ms;
  box-sizing: border-box;
  background: #ffffff;
  border: none;
  padding: 0;
  position: absolute;
  width: 24px;
  height: 60px;
  bottom: 32px;
  right: 72px;
  border-radius: 20px 20px 0 0;
}

.emotion-7:before {
  content: "";
  height: 24px;
  background: #ffffff;
  display: block;
  position: absolute;
  bottom: -24px;
  width: 24px;
  border-bottom-right-radius: 50%;
  border-bottom-left-radius: 50%;
}

.emotion-8 {
  height: 100%;
  width: 100%;
  position: absolute;
  right: 0;
  bottom: 0;
}

.emotion-9 {
  box-sizing: border-box;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: end;
  -webkit-box-align: end;
  -ms-flex-align: end;
  align-items: end;
  gap: 10px;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  justify-content: space-between;
  position: absolute;
  padding: 0 10px 10px 10px;
  -webkit-flex-direction: row-reverse;
  -ms-flex-direction: row-reverse;
  flex-direction: row-reverse;
  bottom: 0;
  width: 100%;
  background: linear-gradient(0deg, rgba(0, 0, 0, 0.4) 50%, rgba(0, 0, 0, 0) 100%);
}

.emotion-10 {
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
}

.emotion-11 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: none;
  transition: none;
  box-sizing: border-box;
  border: 1.5px solid transparent;
  border-radius: 0;
  font-size: 16px;
  font-weight: 500;
  letter-spacing: 0px;
  min-height: 44px;
  max-height: auto;
  line-height: 1.3125;
  padding: 0;
  width: auto;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: left;
  border-radius: 0;
  border: none;
  background-color: transparent;
  color: #000000;
  height: auto;
  -webkit-box-pack: left;
  -ms-flex-pack: left;
  -webkit-justify-content: left;
  justify-content: left;
  font-weight: normal;
  text-transform: none;
  z-index: 1;
  -webkit-text-decoration: underline;
  text-decoration: underline;
  font-size: 11px;
  line-height: 14px;
  min-height: initial;
  padding: 0;
  margin-left: unset;
  color: #FFFFFF;
  pointer-events: auto;
  text-underline-offset: 2px;
}

.emotion-11:focus {
  outline: none;
}

.emotion-11>span {
  padding: 1px 0;
}

.emotion-11 span span {
  padding-left: 3px;
  min-width: auto;
  min-height: auto;
  position: relative;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-transition: 250ms ease-in-out;
  transition: 250ms ease-in-out;
  transition-property: height,width,margin;
  top: 0;
  width: calc(1.2rem * 0.72);
}

.emotion-11 span span svg {
  -webkit-transition: fill 250ms ease-in-out;
  transition: fill 250ms ease-in-out;
  fill: #000000;
}

.emotion-11 span span {
  padding-left: initial;
}

.emotion-11:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-11:focus-visible {
  outline: auto;
}

<div>
  <div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <div
        class="emotion-0"
        height="0"
        width="0"
      >
        <div
          class="emotion-1"
        >
          <div
            class="emotion-2"
            data-testid="videocomponent-container"
          >
            <div
              style="position: relative;"
            >
              <img
                fetchpriority="high"
                src="https://fmzlik0m0z6r1oifp8ex832rf.staging.bigcontent.io/i/oldnavy/230901_14-M5283_LaborDay_CatNav_Tops_HP_US_XL?fmt=auto"
                style="height: 100%; left: 0px; object-fit: cover; position: absolute; top: 0px; width: 100%;"
              />
              <h1
                role="presentation"
              >
                ReactPlayer
              </h1>
            </div>
            <div
              class="player-custom-controls emotion-3"
              data-testid="player-custom-controls"
            >
              <button
                aria-label="Play"
                aria-pressed="false"
                class="emotion-4"
              >
                <svg
                  aria-label="play-button"
                  fill="none"
                  height="24"
                  viewBox="0 0 24 24"
                  width="24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M24 12C24 18.6275 18.6275 24 12 24C5.3725 24 0 18.6275 0 12C0 5.3725 5.3725 0 12 0C18.6275 0 24 5.3725 24 12Z"
                    fill="#FFFFFF"
                  />
                  <path
                    d="M7.5 7.43526C7.5 6.78726 8.19225 6.37326 8.76375 6.68076L17.241 11.246C17.3771 11.3194 17.4909 11.4283 17.5702 11.5612C17.6494 11.694 17.6913 11.8458 17.6913 12.0005C17.6913 12.1552 17.6494 12.307 17.5702 12.4398C17.4909 12.5727 17.3771 12.6816 17.241 12.755L8.76375 17.3203C8.63317 17.3906 8.48659 17.4258 8.33831 17.4226C8.19004 17.4193 8.04515 17.3776 7.91781 17.3016C7.79046 17.2255 7.68502 17.1178 7.61179 16.9888C7.53856 16.8598 7.50004 16.7141 7.5 16.5658V7.43526Z"
                    fill="#2C2824"
                  />
                </svg>
              </button>
              <div
                class="emotion-5"
                data-testid="styled-mute-controls"
              >
                <button
                  aria-label="Mute"
                  aria-pressed="false"
                  class="emotion-6"
                >
                  <svg
                    aria-label="mute-button"
                    fill="none"
                    height="24"
                    viewBox="0 0 24 24"
                    width="24"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M24 12C24 18.6275 18.6275 24 12 24C5.3725 24 0 18.6275 0 12C0 5.3725 5.3725 0 12 0C18.6275 0 24 5.3725 24 12Z"
                      fill="#FFFFFF"
                    />
                    <g>
                      <path
                        clip-rule="evenodd"
                        d="M14.4996 14.2613C15.7486 13.0124 15.7486 10.9876 14.4996 9.73866C14.28 9.51899 14.28 9.16283 14.4996 8.94316C14.7193 8.72349 15.0755 8.72349 15.2951 8.94316C16.9835 10.6315 16.9835 13.3685 15.2951 15.0568C15.0755 15.2765 14.7193 15.2765 14.4996 15.0568C14.28 14.8372 14.28 14.481 14.4996 14.2613Z"
                        fill="#2C2824"
                        fill-rule="evenodd"
                      />
                      <path
                        clip-rule="evenodd"
                        d="M16.4087 16.034C18.6368 13.8059 18.6368 10.1939 16.4087 7.96586C16.1891 7.74619 16.1891 7.39003 16.4087 7.17036C16.6284 6.95069 16.9846 6.95069 17.2042 7.17036C19.8716 9.83778 19.8716 14.1621 17.2042 16.8295C16.9846 17.0492 16.6284 17.0492 16.4087 16.8295C16.1891 16.6098 16.1891 16.2537 16.4087 16.034Z"
                        fill="#2C2824"
                        fill-rule="evenodd"
                      />
                      <path
                        d="M4.7383 12.8103V12V11.1897C4.7383 10.6374 5.18602 10.1897 5.7383 10.1897H7.14083C7.39437 10.1897 7.63844 10.0934 7.82366 9.92023L11.0042 6.94759C11.14 6.82063 11.319 6.75 11.5049 6.75C11.91 6.75 12.2383 7.07834 12.2383 7.48337V12V16.5166C12.2383 16.9217 11.91 17.25 11.5049 17.25C11.319 17.25 11.14 17.1794 11.0042 17.0524L7.82366 14.0798C7.63844 13.9066 7.39437 13.8103 7.14083 13.8103H5.7383C5.18602 13.8103 4.7383 13.3626 4.7383 12.8103Z"
                        fill="#2C2824"
                      />
                      <rect
                        fill="#2C2824"
                        height="19.3235"
                        transform="rotate(120 21.2347 16.4121)"
                        width="1.5"
                        x="21.2347"
                        y="16.4121"
                      />
                    </g>
                  </svg>
                </button>
                <div
                  class="emotion-7"
                  data-testid="styled-input-volume"
                  style="background: rgb(255, 255, 255);"
                >
                  <div
                    role="presentation"
                  />
                </div>
              </div>
            </div>
          </div>
          <div
            class="emotion-8"
          >
            <div
              class="emotion-9"
            >
              <div
                class="emotion-10"
              >
                <button
                  class="emotion-11"
                >
                  Details
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`SpotlightVideo Content Type Inset should match desktop 1`] = `
.emotion-0 {
  background: #FFFABC;
  position: relative;
  width: 100%;
  height: 100%;
  box-sizing: border-box;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  padding-top: 64px;
  padding: 64px 20px 20px 20px;
}

.emotion-1 {
  -webkit-align-self: center;
  -ms-flex-item-align: center;
  align-self: center;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  width: 83.33%;
  gap: 20px;
  padding: 0px 0px 64px 0px;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  pointer-events: none;
}

.emotion-2 {
  height: auto;
  max-width: 100%;
}

.emotion-3 {
  min-height: 105px;
  max-height: 105px;
}

.emotion-4 {
  text-align: center;
}

.emotion-4 .amp-cms--p {
  padding: 0;
  margin: 0;
  line-height: 0;
}

.emotion-4 .amp-cms--p>span {
  display: inline;
  white-space: break-spaces;
}

.emotion-4 .amp-cms--p a {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  position: relative;
  z-index: 2;
  pointer-events: auto;
}

.emotion-4 .amp-cms--p a:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-4 .amp-cms--legal-copy {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, 0.6944444444444444vw);
  line-height: 1.5;
  letter-spacing: 0.034722222222222224vw;
  font-weight: 500;
}

.emotion-4 sup {
  vertical-align: top;
  display: inline-block;
  margin-top: -0.25ex;
}

.emotion-4 sub {
  vertical-align: bottom;
  display: inline-block;
  margin-bottom: -0.25ex;
}

.emotion-4 .amp-cms--f-0 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 0.8333333333333334vw;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-4 .amp-cms--f-1 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 0.8333333333333334vw;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-4 .amp-cms--f-2 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 0.8333333333333334vw;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-4 .amp-cms--fn-1 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 0.8333333333333334vw;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-4 .amp-cms--body-1 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, 1.25vw);
  line-height: 1.5555555555555556;
  letter-spacing: 0.0625vw;
  font-weight: 500;
}

.emotion-4 .amp-cms--body-2 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, 1.1111111111111112vw);
  line-height: 1.25;
  letter-spacing: 0vw;
  font-weight: 500;
}

.emotion-4 .amp-cms--body-3 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, 0.9722222222222222vw);
  line-height: 1.5714285714285714;
  letter-spacing: 0.04861111111111111vw;
  font-weight: 500;
}

.emotion-4 .amp-cms--body-4 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, 0.8333333333333334vw);
  line-height: 1.5;
  letter-spacing: 0vw;
  font-weight: 500;
}

.emotion-4 .amp-cms--body-5 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, 0.6944444444444444vw);
  line-height: 1.5;
  letter-spacing: 0.034722222222222224vw;
  font-weight: 500;
}

.emotion-4 .amp-cms--eyebrow-1 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, 1.1805555555555556vw);
  line-height: 1.588235294117647;
  letter-spacing: 0.11805555555555555vw;
  font-weight: 700;
  text-transform: uppercase;
}

.emotion-4 .amp-cms--eyebrow-2 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(11px, 1.1111111111111112vw);
  line-height: 1.75;
  letter-spacing: 0.1111111111111111vw;
  font-weight: 700;
  text-transform: uppercase;
}

.emotion-4 .amp-cms--eyebrow-3 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, 0.8333333333333334vw);
  line-height: 1.8333333333333333;
  letter-spacing: 0.056944444444444436vw;
  font-weight: 700;
  text-transform: uppercase;
}

.emotion-4 .amp-cms--headline-1 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, 4.166666666666666vw);
  line-height: 1;
  letter-spacing: 0.16666666666666666vw;
  text-transform: uppercase;
  font-weight: 500;
}

.emotion-4 .amp-cms--headline-2 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, 3.888888888888889vw);
  line-height: 1;
  letter-spacing: 0.19444444444444445vw;
  text-transform: uppercase;
  font-weight: 500;
}

.emotion-4 .amp-cms--headline-3 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, 3.4722222222222223vw);
  line-height: 1;
  letter-spacing: 0.1736111111111111vw;
  text-transform: uppercase;
  font-weight: 500;
}

.emotion-4 .amp-cms--headline-4 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, 2.7777777777777777vw);
  line-height: 1;
  letter-spacing: 0.19444444444444445vw;
  text-transform: uppercase;
  font-weight: 500;
}

.emotion-4 .amp-cms--headline-5 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, 2.5vw);
  line-height: 1;
  letter-spacing: 0.25vw;
  text-transform: uppercase;
  font-weight: 600;
}

.emotion-4 .amp-cms--headline-6 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, 1.9444444444444444vw);
  line-height: 1.1428571428571428;
  letter-spacing: 0.1048611111111111vw;
  text-transform: none;
  font-weight: 600;
}

.emotion-4 .amp-cms--headline-7 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, 1.3888888888888888vw);
  line-height: 1.2;
  letter-spacing: 0.09027777777777779vw;
  text-transform: uppercase;
  font-weight: 700;
}

.emotion-4 .amp-cms--headlineAlt-1 {
  color: #000000;
  font-family: var(--font-soehne),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, 8.61111111111111vw);
  line-height: 1;
  letter-spacing: -0.027777777777777776vw;
  font-weight: 400;
}

.emotion-4 .amp-cms--headlineAlt-2 {
  color: #000000;
  font-family: var(--font-soehne),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, 5.833333333333333vw);
  line-height: 1;
  letter-spacing: -0.027777777777777776vw;
  font-weight: 400;
}

.emotion-4 .amp-cms--headlineAlt-3 {
  color: #000000;
  font-family: var(--font-soehne),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, 4.444444444444445vw);
  line-height: 1;
  letter-spacing: -0.027777777777777776vw;
  font-weight: 400;
}

.emotion-4 .amp-cms--headlineAlt-4 {
  color: #000000;
  font-family: var(--font-soehne),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, 2.361111111111111vw);
  line-height: 1;
  letter-spacing: -0.027777777777777776vw;
  font-weight: 400;
}

.emotion-4 .amp-cms--headlineAlt-5 {
  color: #000000;
  font-family: var(--font-soehne),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 1.1111111111111112vw;
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-4 .amp-cms--headlineAlt-6 {
  color: #000000;
  font-family: var(--font-soehne),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 1.1111111111111112vw;
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-4 .amp-cms--headlineAlt-7 {
  color: #000000;
  font-family: var(--font-soehne),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 1.1111111111111112vw;
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-4 .amp-cms--promo-1 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, 6.944444444444445vw);
  line-height: 1;
  letter-spacing: 0vw;
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-4 .amp-cms--promo-2 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, 5.555555555555555vw);
  line-height: 1;
  letter-spacing: 0vw;
  font-weight: 600;
}

.emotion-4 .amp-cms--promoAlt-1 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 1.1111111111111112vw;
  line-height: 0.0625;
  letter-spacing: 0;
}

.emotion-4 .amp-cms--subhead-1 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, 1.6666666666666667vw);
  line-height: 1.5;
  letter-spacing: 0.08333333333333333vw;
  font-weight: 400;
  text-transform: uppercase;
}

.emotion-4 .amp-cms--subhead-2 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, 1.25vw);
  line-height: 1.5555555555555556;
  letter-spacing: 0.025vw;
  font-weight: 500;
  text-transform: uppercase;
}

.emotion-4 .amp-cms--subhead-3 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, 0.9722222222222222vw);
  line-height: 1.1428571428571428;
  letter-spacing: 0.04861111111111111vw;
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-4 a {
  pointer-events: auto;
}

.emotion-5 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  gap: 15px;
}

.emotion-5 a {
  -webkit-align-self: center;
  -ms-flex-item-align: center;
  align-self: center;
}

.emotion-6 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: none;
  transition: none;
  box-sizing: border-box;
  border: 1.5px solid transparent;
  border-radius: 0;
  font-size: 16px;
  font-weight: 500;
  letter-spacing: 0px;
  min-height: auto;
  max-height: 60px;
  line-height: 1.25;
  padding: 22px 40px;
  width: auto;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  background-color: #FFFFFF;
  color: #000000;
  border-color: #000000;
  -webkit-align-self: flex-start;
  -ms-flex-item-align: flex-start;
  align-self: flex-start;
  white-space: normal;
  pointer-events: auto;
}

.emotion-6:focus {
  outline: none;
}

.emotion-6>span {
  padding: 1px 0;
}

.emotion-6:hover,
.emotion-6:focus {
  text-shadow: 0 0 2px currentColor;
}

.emotion-6:active {
  color: #FFFFFF;
  background-color: #000000;
  border-color: #000000;
  -webkit-text-decoration: none;
  text-decoration: none;
  text-shadow: none;
}

.emotion-7 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: none;
  transition: none;
  box-sizing: border-box;
  border: 1.5px solid transparent;
  border-radius: 0;
  font-size: 16px;
  font-weight: 500;
  letter-spacing: 0px;
  min-height: auto;
  max-height: auto;
  line-height: 1.3125;
  padding: 0;
  width: auto;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: left;
  border-radius: 0;
  border: none;
  background-color: transparent;
  color: #000000;
  height: auto;
  -webkit-box-pack: left;
  -ms-flex-pack: left;
  -webkit-justify-content: left;
  justify-content: left;
  -webkit-align-self: flex-start;
  -ms-flex-item-align: flex-start;
  align-self: flex-start;
  white-space: normal;
  pointer-events: auto;
}

.emotion-7:focus {
  outline: none;
}

.emotion-7>span span {
  height: calc(16px * 0.7133333333333334);
}

.emotion-7 span span {
  padding-left: 3px;
  min-width: auto;
  min-height: auto;
  position: relative;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-transition: 250ms ease-in-out;
  transition: 250ms ease-in-out;
  transition-property: height,width,margin;
  top: 0;
  width: calc(1.2rem * 0.72);
}

.emotion-7 span span svg {
  -webkit-transition: fill 250ms ease-in-out;
  transition: fill 250ms ease-in-out;
  fill: #000000;
}

.emotion-7:hover,
.emotion-7:focus {
  text-shadow: 0 0 2px currentColor;
}

.emotion-7:active {
  text-transform: uppercase;
  text-shadow: none;
}

.emotion-8 {
  box-sizing: border-box;
}

.emotion-9 {
  display: inline-block;
  height: 2rem;
  width: 2rem;
  min-height: 2rem;
  min-width: 2rem;
}

.emotion-9 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-10 {
  position: relative;
  aspect-ratio: 16/9;
}

.emotion-10>div {
  height: 100%;
}

.emotion-10>img {
  position: absolute;
  top: 0;
}

.emotion-11 {
  width: -webkit-fit-content;
  width: -moz-fit-content;
  width: fit-content;
  position: relative;
  width: 100%;
}

.emotion-12 {
  height: 100%;
  width: 100%;
  position: absolute;
  right: 0;
  bottom: 0;
}

.emotion-13 {
  box-sizing: border-box;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: end;
  -webkit-box-align: end;
  -ms-flex-align: end;
  align-items: end;
  gap: 10px;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  justify-content: space-between;
  position: absolute;
  padding: 0 15px 10px 15px;
  -webkit-flex-direction: row-reverse;
  -ms-flex-direction: row-reverse;
  flex-direction: row-reverse;
  bottom: 0;
  width: 100%;
  background: linear-gradient(0deg, rgba(0, 0, 0, 0.4) 50%, rgba(0, 0, 0, 0) 100%);
}

.emotion-14 {
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
}

.emotion-15 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: none;
  transition: none;
  box-sizing: border-box;
  border: 1.5px solid transparent;
  border-radius: 0;
  font-size: 16px;
  font-weight: 500;
  letter-spacing: 0px;
  min-height: auto;
  max-height: auto;
  line-height: 1.3125;
  padding: 0;
  width: auto;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: left;
  border-radius: 0;
  border: none;
  background-color: transparent;
  color: #000000;
  height: auto;
  -webkit-box-pack: left;
  -ms-flex-pack: left;
  -webkit-justify-content: left;
  justify-content: left;
  font-weight: normal;
  text-transform: none;
  z-index: 1;
  -webkit-text-decoration: underline;
  text-decoration: underline;
  font-size: 12px;
  line-height: 16px;
  min-height: initial;
  padding: 0;
  margin-left: unset;
  color: #FFFFFF;
  pointer-events: auto;
  text-underline-offset: 2px;
}

.emotion-15:focus {
  outline: none;
}

.emotion-15>span {
  padding: 1px 0;
}

.emotion-15 span span {
  padding-left: 3px;
  min-width: auto;
  min-height: auto;
  position: relative;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-transition: 250ms ease-in-out;
  transition: 250ms ease-in-out;
  transition-property: height,width,margin;
  top: 0;
  width: calc(1.2rem * 0.72);
}

.emotion-15 span span svg {
  -webkit-transition: fill 250ms ease-in-out;
  transition: fill 250ms ease-in-out;
  fill: #000000;
}

.emotion-15 span span {
  padding-left: initial;
}

.emotion-15:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-15:focus-visible {
  outline: auto;
}

<div>
  <div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <div
        class="emotion-0"
        height="0"
        width="0"
      >
        <div
          class="emotion-1"
        >
          <div
            class="emotion-2"
            data-testid="advance-image-container-test-id"
          >
            <img
              alt="Moisture Wicking"
              class="emotion-3"
              maxminheight="105px"
              src="https://athleta.a.bigcontent.io/v1/static/WATER-RESISTANT_dark"
            />
          </div>
          <div
            class="emotion-4"
          >
            <div>
              <p
                class="amp-cms--p"
              >
                <span
                  class="amp-cms--body-1"
                >
                  Spotlight Video
                </span>
              </p>
            </div>
          </div>
          <div
            class="emotion-5"
          >
            <a
              class="emotion-6"
              color="dark"
              href="/1"
            >
              First CTA
            </a>
            <a
              class="emotion-7"
              color="dark"
              href="https://www.google.com/"
            >
              <span
                class="emotion-8"
              >
                Second CTA2
                <span
                  aria-hidden="true"
                  class="emotion-9"
                />
              </span>
            </a>
          </div>
        </div>
        <div
          class="emotion-10"
        >
          <div
            class="emotion-11"
            data-testid="videocomponent-container"
          >
            <div
              style="position: relative;"
            >
              <img
                fetchpriority="high"
                src="https://fmzlik0m0z6r1oifp8ex832rf.staging.bigcontent.io/i/oldnavy/FA23_D3_Banner_NA_IMG1_XL?fmt=auto"
                style="height: 100%; left: 0px; object-fit: cover; position: absolute; top: 0px; width: 100%;"
              />
            </div>
          </div>
          <div
            class="emotion-12"
          >
            <div
              class="emotion-13"
            >
              <div
                class="emotion-14"
              >
                <button
                  class="emotion-15"
                >
                  Details
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`SpotlightVideo Content Type Inset should match mobile 1`] = `
.emotion-0 {
  background: #D10B25;
  position: relative;
  width: 100%;
  height: 100%;
  box-sizing: border-box;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  padding-top: 40px;
  padding: 40px 10px 10px 10px;
}

.emotion-1 {
  -webkit-align-self: center;
  -ms-flex-item-align: center;
  align-self: center;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  width: 87.5%;
  gap: 20px;
  padding: 0px 30px 40px 30px;
  -webkit-align-items: start;
  -webkit-box-align: start;
  -ms-flex-align: start;
  align-items: start;
  pointer-events: none;
}

.emotion-2 {
  height: auto;
  max-width: 100%;
}

.emotion-3 {
  min-height: 80px;
  max-height: 80px;
}

.emotion-4 {
  text-align: left;
}

.emotion-4 .amp-cms--p {
  padding: 0;
  margin: 0;
  line-height: 0;
}

.emotion-4 .amp-cms--p>span {
  display: inline;
  white-space: break-spaces;
}

.emotion-4 .amp-cms--p a {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  position: relative;
  z-index: 2;
  pointer-events: auto;
}

.emotion-4 .amp-cms--p a:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-4 .amp-cms--legal-copy {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, 2.666666666666667vw);
  line-height: 1.5;
  letter-spacing: 0.13333333333333333vw;
  font-weight: 500;
}

.emotion-4 sup {
  vertical-align: top;
  display: inline-block;
  margin-top: -0.25ex;
}

.emotion-4 sub {
  vertical-align: bottom;
  display: inline-block;
  margin-bottom: -0.25ex;
}

.emotion-4 .amp-cms--f-0 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 2.933333333333333vw;
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-4 .amp-cms--f-1 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 2.933333333333333vw;
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-4 .amp-cms--f-2 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 2.933333333333333vw;
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-4 .amp-cms--fn-1 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 2.933333333333333vw;
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-4 .amp-cms--body-1 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, 4.266666666666667vw);
  line-height: 1.625;
  letter-spacing: 0.21333333333333335vw;
  font-weight: 500;
}

.emotion-4 .amp-cms--body-2 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, 3.733333333333334vw);
  line-height: 1.4285714285714286;
  letter-spacing: 0vw;
  font-weight: 500;
}

.emotion-4 .amp-cms--body-3 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, 3.2vw);
  line-height: 1.5;
  letter-spacing: 0.15999999999999998vw;
  font-weight: 500;
}

.emotion-4 .amp-cms--body-4 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, 2.666666666666667vw);
  line-height: 1.6;
  letter-spacing: 0vw;
  font-weight: 500;
}

.emotion-4 .amp-cms--body-5 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, 2.666666666666667vw);
  line-height: 1.5;
  letter-spacing: 0.13333333333333333vw;
  font-weight: 500;
}

.emotion-4 .amp-cms--eyebrow-1 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, 3.2vw);
  line-height: 2;
  letter-spacing: 0.31999999999999995vw;
  font-weight: 700;
  text-transform: uppercase;
}

.emotion-4 .amp-cms--eyebrow-2 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(11px, 2.933333333333333vw);
  line-height: 2;
  letter-spacing: 0.29333333333333333vw;
  font-weight: 700;
  text-transform: uppercase;
}

.emotion-4 .amp-cms--eyebrow-3 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, 2.666666666666667vw);
  line-height: 1.5;
  letter-spacing: 0.18133333333333335vw;
  font-weight: 700;
  text-transform: uppercase;
}

.emotion-4 .amp-cms--headline-1 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(13px, 9.6vw);
  line-height: 1;
  letter-spacing: 0.36533333333333334vw;
  text-transform: uppercase;
  font-weight: 500;
}

.emotion-4 .amp-cms--headline-2 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(13px, 9.066666666666666vw);
  line-height: 1;
  letter-spacing: 0.45333333333333325vw;
  text-transform: uppercase;
  font-weight: 500;
}

.emotion-4 .amp-cms--headline-3 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(13px, 8vw);
  line-height: 1;
  letter-spacing: 0.07999999999999999vw;
  text-transform: uppercase;
  font-weight: 500;
}

.emotion-4 .amp-cms--headline-4 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(13px, 6.4vw);
  line-height: 1;
  letter-spacing: 0.48000000000000004vw;
  text-transform: uppercase;
  font-weight: 500;
}

.emotion-4 .amp-cms--headline-5 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(13px, 5.866666666666666vw);
  line-height: 1.1818181818181819;
  letter-spacing: 0.5866666666666667vw;
  text-transform: uppercase;
  font-weight: 600;
}

.emotion-4 .amp-cms--headline-6 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(13px, 4.266666666666667vw);
  line-height: 1.375;
  letter-spacing: 0.21333333333333335vw;
  text-transform: none;
  font-weight: 600;
}

.emotion-4 .amp-cms--headline-7 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(13px, 3.733333333333334vw);
  line-height: 1.4285714285714286;
  letter-spacing: 0.02666666666666667vw;
  text-transform: uppercase;
  font-weight: 700;
}

.emotion-4 .amp-cms--headlineAlt-1 {
  color: #000000;
  font-family: var(--font-soehne),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(13px, 17.066666666666666vw);
  line-height: 1;
  letter-spacing: -0.07999999999999999vw;
  font-weight: 400;
}

.emotion-4 .amp-cms--headlineAlt-2 {
  color: #000000;
  font-family: var(--font-soehne),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(13px, 14.399999999999999vw);
  line-height: 1;
  letter-spacing: -0.07999999999999999vw;
  font-weight: 400;
}

.emotion-4 .amp-cms--headlineAlt-3 {
  color: #000000;
  font-family: var(--font-soehne),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(13px, 9.066666666666666vw);
  line-height: 1;
  letter-spacing: -0.07999999999999999vw;
  font-weight: 400;
}

.emotion-4 .amp-cms--headlineAlt-4 {
  color: #000000;
  font-family: var(--font-soehne),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(13px, 6.4vw);
  line-height: 1.4166666666666667;
  letter-spacing: -0.07999999999999999vw;
  font-weight: 400;
}

.emotion-4 .amp-cms--headlineAlt-5 {
  color: #000000;
  font-family: var(--font-soehne),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 3.733333333333334vw;
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-4 .amp-cms--headlineAlt-6 {
  color: #000000;
  font-family: var(--font-soehne),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 3.733333333333334vw;
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-4 .amp-cms--headlineAlt-7 {
  color: #000000;
  font-family: var(--font-soehne),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 3.733333333333334vw;
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-4 .amp-cms--promo-1 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(13px, 16vw);
  line-height: 1;
  letter-spacing: 0vw;
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-4 .amp-cms--promo-2 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(13px, 10.666666666666668vw);
  line-height: 1;
  letter-spacing: 0vw;
  font-weight: 600;
}

.emotion-4 .amp-cms--promoAlt-1 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 3.733333333333334vw;
  line-height: 0.07142857142857142;
  letter-spacing: 0;
}

.emotion-4 .amp-cms--subhead-1 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(13px, 4.266666666666667vw);
  line-height: 1.75;
  letter-spacing: 0.08533333333333333vw;
  font-weight: 400;
  text-transform: uppercase;
}

.emotion-4 .amp-cms--subhead-2 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(13px, 3.733333333333334vw);
  line-height: 1.5714285714285714;
  letter-spacing: 0.18666666666666668vw;
  font-weight: 500;
  text-transform: uppercase;
}

.emotion-4 .amp-cms--subhead-3 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(13px, 3.4666666666666663vw);
  line-height: 1;
  letter-spacing: 0.25866666666666666vw;
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-4 a {
  pointer-events: auto;
}

.emotion-5 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  gap: 15px;
}

.emotion-5 a {
  -webkit-align-self: center;
  -ms-flex-item-align: center;
  align-self: center;
}

.emotion-6 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: none;
  transition: none;
  box-sizing: border-box;
  border: 1.5px solid transparent;
  border-radius: 0;
  font-size: 14px;
  font-weight: 500;
  letter-spacing: 0px;
  min-height: auto;
  max-height: 50px;
  line-height: 1.2857142857142858;
  padding: 18px 40px;
  width: auto;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  background-color: #FFFFFF;
  color: #000000;
  border-color: #000000;
  -webkit-align-self: flex-start;
  -ms-flex-item-align: flex-start;
  align-self: flex-start;
  white-space: normal;
  pointer-events: auto;
}

.emotion-6:focus {
  outline: none;
}

.emotion-6>span {
  padding: 1px 0;
}

.emotion-6:hover,
.emotion-6:focus {
  text-shadow: 0 0 2px currentColor;
}

.emotion-6:active {
  color: #FFFFFF;
  background-color: #000000;
  border-color: #000000;
  -webkit-text-decoration: none;
  text-decoration: none;
  text-shadow: none;
}

.emotion-7 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: none;
  transition: none;
  box-sizing: border-box;
  border: 1.5px solid transparent;
  border-radius: 0;
  font-size: 14px;
  font-weight: 500;
  letter-spacing: 0px;
  min-height: 44px;
  max-height: auto;
  line-height: 1.1428571428571428;
  padding: 0;
  width: auto;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: left;
  border-radius: 0;
  border: none;
  background-color: transparent;
  color: #000000;
  height: auto;
  -webkit-box-pack: left;
  -ms-flex-pack: left;
  -webkit-justify-content: left;
  justify-content: left;
  -webkit-align-self: flex-start;
  -ms-flex-item-align: flex-start;
  align-self: flex-start;
  white-space: normal;
  pointer-events: auto;
}

.emotion-7:focus {
  outline: none;
}

.emotion-7>span span {
  height: calc(14px * 0.7133333333333334);
}

.emotion-7 span span {
  padding-left: 3px;
  min-width: auto;
  min-height: auto;
  position: relative;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-transition: 250ms ease-in-out;
  transition: 250ms ease-in-out;
  transition-property: height,width,margin;
  top: 0;
  width: calc(0.8rem * 0.72);
}

.emotion-7 span span svg {
  -webkit-transition: fill 250ms ease-in-out;
  transition: fill 250ms ease-in-out;
  fill: #000000;
}

.emotion-7:hover,
.emotion-7:focus {
  text-shadow: 0 0 2px currentColor;
}

.emotion-7:active {
  text-transform: uppercase;
  text-shadow: none;
}

.emotion-8 {
  box-sizing: border-box;
}

.emotion-9 {
  display: inline-block;
  height: 2rem;
  width: 2rem;
  min-height: 2rem;
  min-width: 2rem;
}

.emotion-9 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-10 {
  position: relative;
  aspect-ratio: 9/16;
}

.emotion-10>div {
  height: 100%;
}

.emotion-11 {
  width: -webkit-fit-content;
  width: -moz-fit-content;
  width: fit-content;
  position: relative;
  width: 100%;
}

.emotion-12>button {
  z-index: 1;
}

.emotion-13 {
  background: none;
  border: none;
  padding: 0;
  height: 24px;
  margin: 0;
  position: absolute;
  bottom: 8px;
  right: 112px;
  width: 24px;
  height: 24px;
}

.emotion-13:is(:not(:focus, :active, :hover, :focus-within)) {
  opacity: 0.64;
}

.emotion-13:is(:hover) {
  opacity: 1;
}

@media (min-width: 767px) {
  .emotion-13 {
    right: 120px;
  }
}

.emotion-14 {
  box-sizing: border-box;
  background: none;
  border: none;
  padding: 0;
  position: relative;
  width: 100%;
  height: 0px;
}

.emotion-14:focus div {
  opacity: 0.84;
  -webkit-transition: opacity 200ms;
  transition: opacity 200ms;
  border-radius: 20px 20px 0 0;
}

.emotion-14 div {
  border-radius: 20px 20px 0 0;
}

.emotion-14 .keepOpen,
.emotion-14 .staysOpen {
  opacity: 0.84;
  -webkit-transition: opacity 200ms;
  transition: opacity 200ms;
  z-index: 2;
}

.emotion-15 {
  background: none;
  border: none;
  padding: 0;
  height: 24px;
  margin: 0;
  position: absolute;
  bottom: 8px;
  right: 72px;
  z-index: 11;
}

.emotion-15:is(:not(:focus, :active, :hover, :focus-within)) {
  opacity: 0.64;
}

.emotion-15:is(:hover) {
  opacity: 1;
}

@media (min-width: 767px) {
  .emotion-15 {
    right: 80px;
  }
}

.emotion-16 {
  opacity: 0;
  -webkit-transition: opacity 200ms;
  transition: opacity 200ms;
  box-sizing: border-box;
  background: #ffffff;
  border: none;
  padding: 0;
  position: absolute;
  width: 24px;
  height: 60px;
  bottom: 32px;
  right: 72px;
  border-radius: 20px 20px 0 0;
}

.emotion-16:before {
  content: "";
  height: 24px;
  background: #ffffff;
  display: block;
  position: absolute;
  bottom: -24px;
  width: 24px;
  border-bottom-right-radius: 50%;
  border-bottom-left-radius: 50%;
}

.emotion-17 {
  height: 100%;
  width: 100%;
  position: absolute;
  right: 0;
  bottom: 0;
}

.emotion-18 {
  box-sizing: border-box;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: end;
  -webkit-box-align: end;
  -ms-flex-align: end;
  align-items: end;
  gap: 10px;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  justify-content: space-between;
  position: absolute;
  padding: 0 10px 10px 10px;
  -webkit-flex-direction: row-reverse;
  -ms-flex-direction: row-reverse;
  flex-direction: row-reverse;
  bottom: 0;
  width: 100%;
  background: linear-gradient(0deg, rgba(0, 0, 0, 0.4) 50%, rgba(0, 0, 0, 0) 100%);
}

.emotion-19 {
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
}

.emotion-20 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: none;
  transition: none;
  box-sizing: border-box;
  border: 1.5px solid transparent;
  border-radius: 0;
  font-size: 16px;
  font-weight: 500;
  letter-spacing: 0px;
  min-height: 44px;
  max-height: auto;
  line-height: 1.3125;
  padding: 0;
  width: auto;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: left;
  border-radius: 0;
  border: none;
  background-color: transparent;
  color: #000000;
  height: auto;
  -webkit-box-pack: left;
  -ms-flex-pack: left;
  -webkit-justify-content: left;
  justify-content: left;
  font-weight: normal;
  text-transform: none;
  z-index: 1;
  -webkit-text-decoration: underline;
  text-decoration: underline;
  font-size: 11px;
  line-height: 14px;
  min-height: initial;
  padding: 0;
  margin-left: unset;
  color: #FFFFFF;
  pointer-events: auto;
  text-underline-offset: 2px;
}

.emotion-20:focus {
  outline: none;
}

.emotion-20>span {
  padding: 1px 0;
}

.emotion-20 span span {
  padding-left: 3px;
  min-width: auto;
  min-height: auto;
  position: relative;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-transition: 250ms ease-in-out;
  transition: 250ms ease-in-out;
  transition-property: height,width,margin;
  top: 0;
  width: calc(1.2rem * 0.72);
}

.emotion-20 span span svg {
  -webkit-transition: fill 250ms ease-in-out;
  transition: fill 250ms ease-in-out;
  fill: #000000;
}

.emotion-20 span span {
  padding-left: initial;
}

.emotion-20:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-20:focus-visible {
  outline: auto;
}

<div>
  <div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <div
        class="emotion-0"
        height="0"
        width="0"
      >
        <div
          class="emotion-1"
        >
          <div
            class="emotion-2"
            data-testid="advance-image-container-test-id"
          >
            <img
              alt="Athleta Girl"
              class="emotion-3"
              maxminheight="80px"
              src="https://2hmc8flz4ofg1dy89cc38f2xm.staging.bigcontent.io/i/athleta/GIRL_logo@2x?fmt=auto"
            />
          </div>
          <div
            class="emotion-4"
          >
            <div>
              <p
                class="amp-cms--p"
              >
                <span
                  class="amp-cms--body-1"
                  style="color:#F0F"
                >
                  Mobile Override
                </span>
              </p>
            </div>
          </div>
          <div
            class="emotion-5"
          >
            <a
              class="emotion-6"
              color="dark"
              href="/1"
            >
              First CTA
            </a>
            <a
              class="emotion-7"
              color="dark"
              href="https://www.google.com/"
            >
              <span
                class="emotion-8"
              >
                Second CTA2
                <span
                  aria-hidden="true"
                  class="emotion-9"
                />
              </span>
            </a>
          </div>
        </div>
        <div
          class="emotion-10"
        >
          <div
            class="emotion-11"
            data-testid="videocomponent-container"
          >
            <div
              style="position: relative;"
            >
              <img
                fetchpriority="high"
                src="https://fmzlik0m0z6r1oifp8ex832rf.staging.bigcontent.io/i/oldnavy/230901_14-M5283_LaborDay_CatNav_Tops_HP_US_XL?fmt=auto"
                style="height: 100%; left: 0px; object-fit: cover; position: absolute; top: 0px; width: 100%;"
              />
              <h1
                role="presentation"
              >
                ReactPlayer
              </h1>
            </div>
            <div
              class="player-custom-controls emotion-12"
              data-testid="player-custom-controls"
            >
              <button
                aria-label="Play"
                aria-pressed="false"
                class="emotion-13"
              >
                <svg
                  aria-label="play-button"
                  fill="none"
                  height="24"
                  viewBox="0 0 24 24"
                  width="24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M24 12C24 18.6275 18.6275 24 12 24C5.3725 24 0 18.6275 0 12C0 5.3725 5.3725 0 12 0C18.6275 0 24 5.3725 24 12Z"
                    fill="#FFFFFF"
                  />
                  <path
                    d="M7.5 7.43526C7.5 6.78726 8.19225 6.37326 8.76375 6.68076L17.241 11.246C17.3771 11.3194 17.4909 11.4283 17.5702 11.5612C17.6494 11.694 17.6913 11.8458 17.6913 12.0005C17.6913 12.1552 17.6494 12.307 17.5702 12.4398C17.4909 12.5727 17.3771 12.6816 17.241 12.755L8.76375 17.3203C8.63317 17.3906 8.48659 17.4258 8.33831 17.4226C8.19004 17.4193 8.04515 17.3776 7.91781 17.3016C7.79046 17.2255 7.68502 17.1178 7.61179 16.9888C7.53856 16.8598 7.50004 16.7141 7.5 16.5658V7.43526Z"
                    fill="#2C2824"
                  />
                </svg>
              </button>
              <div
                class="emotion-14"
                data-testid="styled-mute-controls"
              >
                <button
                  aria-label="Mute"
                  aria-pressed="false"
                  class="emotion-15"
                >
                  <svg
                    aria-label="mute-button"
                    fill="none"
                    height="24"
                    viewBox="0 0 24 24"
                    width="24"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M24 12C24 18.6275 18.6275 24 12 24C5.3725 24 0 18.6275 0 12C0 5.3725 5.3725 0 12 0C18.6275 0 24 5.3725 24 12Z"
                      fill="#FFFFFF"
                    />
                    <g>
                      <path
                        clip-rule="evenodd"
                        d="M14.4996 14.2613C15.7486 13.0124 15.7486 10.9876 14.4996 9.73866C14.28 9.51899 14.28 9.16283 14.4996 8.94316C14.7193 8.72349 15.0755 8.72349 15.2951 8.94316C16.9835 10.6315 16.9835 13.3685 15.2951 15.0568C15.0755 15.2765 14.7193 15.2765 14.4996 15.0568C14.28 14.8372 14.28 14.481 14.4996 14.2613Z"
                        fill="#2C2824"
                        fill-rule="evenodd"
                      />
                      <path
                        clip-rule="evenodd"
                        d="M16.4087 16.034C18.6368 13.8059 18.6368 10.1939 16.4087 7.96586C16.1891 7.74619 16.1891 7.39003 16.4087 7.17036C16.6284 6.95069 16.9846 6.95069 17.2042 7.17036C19.8716 9.83778 19.8716 14.1621 17.2042 16.8295C16.9846 17.0492 16.6284 17.0492 16.4087 16.8295C16.1891 16.6098 16.1891 16.2537 16.4087 16.034Z"
                        fill="#2C2824"
                        fill-rule="evenodd"
                      />
                      <path
                        d="M4.7383 12.8103V12V11.1897C4.7383 10.6374 5.18602 10.1897 5.7383 10.1897H7.14083C7.39437 10.1897 7.63844 10.0934 7.82366 9.92023L11.0042 6.94759C11.14 6.82063 11.319 6.75 11.5049 6.75C11.91 6.75 12.2383 7.07834 12.2383 7.48337V12V16.5166C12.2383 16.9217 11.91 17.25 11.5049 17.25C11.319 17.25 11.14 17.1794 11.0042 17.0524L7.82366 14.0798C7.63844 13.9066 7.39437 13.8103 7.14083 13.8103H5.7383C5.18602 13.8103 4.7383 13.3626 4.7383 12.8103Z"
                        fill="#2C2824"
                      />
                      <rect
                        fill="#2C2824"
                        height="19.3235"
                        transform="rotate(120 21.2347 16.4121)"
                        width="1.5"
                        x="21.2347"
                        y="16.4121"
                      />
                    </g>
                  </svg>
                </button>
                <div
                  class="emotion-16"
                  data-testid="styled-input-volume"
                  style="background: rgb(255, 255, 255);"
                >
                  <div
                    role="presentation"
                  />
                </div>
              </div>
            </div>
          </div>
          <div
            class="emotion-17"
          >
            <div
              class="emotion-18"
            >
              <div
                class="emotion-19"
              >
                <button
                  class="emotion-20"
                >
                  Details
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;
