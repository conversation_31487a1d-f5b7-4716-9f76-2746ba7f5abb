// Jest <PERSON>napshot v1, https://goo.gl/fbAQLP

exports[`Athleta StorytellingAndProductRating on Desktop should render on desktop 1`] = `
.emotion-0 {
  background: #EFE;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  box-sizing: border-box;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  padding: 80px 20px;
  gap: 40px;
  width: 100%;
}

.emotion-1 {
  position: relative;
  display: grid;
  width: 100%;
  gap: 20px;
  grid-template-columns: repeat(12, 1fr);
  grid-template-rows: min-content minmax(min-content, max-content);
  grid-auto-flow: row;
  grid-template-areas: "header header header header header header header header header header header header" "richText-ctas richText-ctas richText-ctas hero-content hero-content hero-content hero-content hero-content hero-content product-card product-card product-card" "quote-text quote-text quote-text hero-content hero-content hero-content hero-content hero-content hero-content product-card product-card product-card" ". . . hero-richText hero-richText hero-richText hero-richText hero-richText hero-richText card-richText card-richText card-richText";
}

.emotion-2 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  position: relative;
  grid-area: header;
  padding-bottom: 20px;
}

.emotion-3 .amp-cms--p {
  padding: 0;
  margin: 0;
  line-height: 0;
}

.emotion-3 .amp-cms--p>span {
  display: inline;
  white-space: break-spaces;
}

.emotion-3 .amp-cms--p a {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  position: relative;
  z-index: 2;
  pointer-events: auto;
}

.emotion-3 .amp-cms--p a:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-3 .amp-cms--legal-copy {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, 0.8333333333333334vw);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-3 sup {
  vertical-align: top;
  display: inline-block;
  margin-top: -0.25ex;
}

.emotion-3 sub {
  vertical-align: bottom;
  display: inline-block;
  margin-bottom: -0.25ex;
}

.emotion-3 .amp-cms--f-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 0.8333333333333334vw;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--f-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 0.8333333333333334vw;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--f-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 0.8333333333333334vw;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--fn-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 0.8333333333333334vw;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--body-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, 1.3888888888888888vw);
  line-height: 1.3;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--body-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, 1.25vw);
  line-height: 1.2777777777777777;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--body-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, 1.1111111111111112vw);
  line-height: 1.25;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--body-4 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, 0.9722222222222222vw);
  line-height: 1.2857142857142858;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--body-5 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, 0.8333333333333334vw);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--eyebrow-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(35px, 3.4722222222222223vw);
  line-height: 0.92;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--eyebrow-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(35px, 2.7777777777777777vw);
  line-height: 1;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--eyebrow-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(35px, 2.430555555555556vw);
  line-height: 1;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--headline-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, 8.333333333333332vw);
  line-height: 0.8333333333333334;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--headline-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, 7.638888888888889vw);
  line-height: 0.8636363636363636;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--headline-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, 6.944444444444445vw);
  line-height: 0.85;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--headline-4 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, 6.944444444444445vw);
  line-height: 1.05;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--headline-5 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, 5.555555555555555vw);
  line-height: 0.9375;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--headline-6 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, 4.861111111111112vw);
  line-height: 0.9285714285714286;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--headline-7 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, 4.166666666666666vw);
  line-height: 0.9166666666666666;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--headlineAlt-1 {
  color: #2B2B2B;
  font-size: max(14px, 7.916666666666666vw);
  line-height: 1;
  letter-spacing: -0.027777777777777776vw;
  font-weight: 400;
}

.emotion-3 .amp-cms--headlineAlt-2 {
  color: #2B2B2B;
  font-size: max(14px, 5.833333333333333vw);
  line-height: 1;
  letter-spacing: -0.027777777777777776vw;
  font-weight: 400;
}

.emotion-3 .amp-cms--headlineAlt-3 {
  color: #2B2B2B;
  font-size: max(14px, 4.444444444444445vw);
  line-height: 1;
  letter-spacing: -0.027777777777777776vw;
  font-weight: 400;
}

.emotion-3 .amp-cms--headlineAlt-4 {
  color: #2B2B2B;
  font-size: max(14px, 2.361111111111111vw);
  line-height: 1;
  letter-spacing: -0.027777777777777776vw;
  font-weight: 400;
}

.emotion-3 .amp-cms--headlineAlt-5 {
  color: #2B2B2B;
  font-size: 1.1111111111111112vw;
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-3 .amp-cms--headlineAlt-6 {
  color: #2B2B2B;
  font-size: 1.1111111111111112vw;
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-3 .amp-cms--headlineAlt-7 {
  color: #2B2B2B;
  font-size: 1.1111111111111112vw;
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-3 .amp-cms--promo-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, 6.944444444444445vw);
  line-height: 1;
  letter-spacing: 0vw;
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-3 .amp-cms--promo-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, 5.555555555555555vw);
  line-height: 1;
  letter-spacing: 0vw;
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-3 .amp-cms--promoAlt-1 {
  color: #2B2B2B;
  font-size: 1.1111111111111112vw;
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 600;
}

.emotion-3 .amp-cms--subhead-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, 2.083333333333333vw);
  line-height: 1;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--subhead-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, 1.7361111111111112vw);
  line-height: 1;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--subhead-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, 1.3888888888888888vw);
  line-height: 1.1;
  letter-spacing: 0;
}

.emotion-4 {
  grid-area: richText-ctas;
}

.emotion-5 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  position: relative;
  grid-area: richText-ctas;
  padding-bottom: 40px;
}

.emotion-7 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  display: inline-block;
  text-transform: none;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 1px solid transparent;
  border-radius: 0;
  max-width: 380px;
  font-size: 16px;
  font-weight: 500;
  letter-spacing: 2%;
  min-height: unset;
  max-height: auto;
  line-height: 16px;
  padding: 12px;
  width: auto;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  background-color: #FFFFFF;
  color: #000000;
  border-color: #000000;
  -webkit-align-self: flex-start;
  -ms-flex-item-align: flex-start;
  align-self: flex-start;
  white-space: normal;
  text-transform: none;
  margin-bottom: 0;
}

.emotion-7:focus {
  outline: none;
}

.emotion-7>span {
  padding: 1px 0;
}

.emotion-7:hover,
.emotion-7:focus {
  color: #FFFFFF;
  background-color: #000000;
  border-color: #000000;
}

.emotion-7:active {
  color: #FFFFFF;
  background-color: #000000;
  border-color: #000000;
}

.emotion-8 {
  grid-area: quote-text;
  -webkit-align-self: end;
  -ms-flex-item-align: end;
  align-self: end;
  margin: 0;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  gap: 12px;
}

.emotion-9 {
  width: 14.26vw;
}

.emotion-9 figure {
  gap: 0.1756vw;
}

.emotion-10 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  -webkit-box-flex-flow: wrap;
  -webkit-flex-flow: wrap;
  -ms-flex-flow: wrap;
  flex-flow: wrap;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: unset;
  padding-bottom: 12px;
}

.emotion-11 {
  position: absolute;
  height: 1px;
  width: 1px;
  overflow: hidden;
  clip: rect(0 0 0 0);
}

.emotion-12 {
  position: relative;
  width: 60%;
  margin-right: 0.8125em;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-13 {
  width: 100%;
  margin: 0;
  padding: 0;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: space-around;
  -ms-flex-pack: space-around;
  -webkit-justify-content: space-around;
  justify-content: space-around;
  margin-block-start: 0;
  margin-block-end: 0;
  -webkit-margin-start: 0;
  margin-inline-start: 0;
  -webkit-margin-end: 0;
  margin-inline-end: 0;
}

.emotion-13 span {
  width: 100%;
  margin: 0;
}

.emotion-14 {
  display: inline-block;
  height: auto;
  width: auto;
  min-height: auto;
  min-width: auto;
}

.emotion-14 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-14 svg path {
  fill: #2B2B2B;
}

.emotion-14 svg rect {
  fill: #2B2B2B;
}

.emotion-19 {
  position: relative;
}

.emotion-21 {
  aspect-ratio: 3/4;
  height: 100%;
  object-fit: cover;
  grid-area: hero-content;
  width: 100%;
}

.emotion-22 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  position: relative;
  grid-area: hero-richText;
  padding-top: 0;
}

.emotion-24 {
  grid-area: product-card;
  -webkit-align-self: end;
  -ms-flex-item-align: end;
  align-self: end;
}

.emotion-25 {
  grid-area: product-card;
  width: 100%;
  -webkit-align-self: end;
  -ms-flex-item-align: end;
  align-self: end;
}

.emotion-26 {
  -webkit-background-size: cover;
  background-size: cover;
  aspect-ratio: 3/4;
  overflow: hidden;
}

.emotion-27 {
  width: 100%;
  aspect-ratio: 3/4;
  object-fit: cover;
}

.emotion-28 {
  grid-area: card-richText;
}

.emotion-29 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  position: relative;
  grid-area: card-richText;
  margin-top: 0;
  padding-top: 0;
}

<div>
  <div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      class="emotion-0"
      height="0"
      width="0"
    >
      <div
        class="emotion-1"
      >
        <div
          class="emotion-2"
        >
          <div
            class="emotion-3"
          >
            <div>
              <p
                class="amp-cms--p"
                style="text-align:left;"
              >
                <span
                  class="amp-cms--headline-3"
                >
                  Upper Text Rich Text Default
                </span>
              </p>
            </div>
          </div>
        </div>
        <div
          class="emotion-4"
        >
          <div
            class="emotion-5"
          >
            <div
              class="emotion-3"
            >
              <div>
                <p
                  class="amp-cms--p"
                  style="text-align:left;"
                >
                  <span
                    class="amp-cms--body-1"
                  >
                    Lower Text Rich Text Default
                  </span>
                </p>
              </div>
            </div>
          </div>
          <a
            class="emotion-7"
            color="dark"
            href="/moreshop"
          >
            Shop More
          </a>
        </div>
        <div
          class="emotion-8"
        >
          <div
            class="emotion-9"
          >
            <a
              class="emotion-10"
              data-testid="reviewRatings"
              tabindex="0"
            >
              <span
                class="emotion-11"
                data-testid="reviewRatingsAriaLabel"
              >
                Image of 5 stars, 5 out of the 5 stars are filled
              </span>
              <div
                class="emotion-12"
              >
                <div>
                  <svg
                    style="width: 0px; height: 0px; display: block;"
                    viewBox="0 0 14 13"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <defs>
                      <lineargradient
                        id="fractional-gradient_1"
                        x1="0"
                        x2="100%"
                        y1="0"
                        y2="0"
                      >
                        <stop
                          offset="0%"
                          stop-color="#2B2B2B"
                          stop-opacity="100%"
                        />
                        <stop
                          offset="0%"
                          stop-color="#2B2B2B"
                          stop-opacity="100%"
                        />
                        <stop
                          offset="0%"
                          stop-color="#CCC"
                          stop-opacity="100%"
                        />
                        <stop
                          offset="100%"
                          stop-color="#CCC"
                          stop-opacity="100%"
                        />
                      </lineargradient>
                    </defs>
                  </svg>
                </div>
                <figure
                  class="emotion-13"
                >
                  <span
                    aria-hidden="true"
                    class="emotion-14"
                  >
                    <svg
                      fill="none"
                      viewBox="0 0 14 13"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        clip-rule="evenodd"
                        d="M7.02697 9.38889l-4.219 3.53831 1.40821-4.98276L0 4.76725l5.07966-.0364L7.02697 0l1.89337 4.73085L14 4.76725 9.83776 7.94444 11.2431 13 7.02697 9.38889z"
                        fill="#2B2B2B"
                        fill-rule="evenodd"
                      />
                    </svg>
                  </span>
                  <span
                    aria-hidden="true"
                    class="emotion-14"
                  >
                    <svg
                      fill="none"
                      viewBox="0 0 14 13"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        clip-rule="evenodd"
                        d="M7.02697 9.38889l-4.219 3.53831 1.40821-4.98276L0 4.76725l5.07966-.0364L7.02697 0l1.89337 4.73085L14 4.76725 9.83776 7.94444 11.2431 13 7.02697 9.38889z"
                        fill="#2B2B2B"
                        fill-rule="evenodd"
                      />
                    </svg>
                  </span>
                  <span
                    aria-hidden="true"
                    class="emotion-14"
                  >
                    <svg
                      fill="none"
                      viewBox="0 0 14 13"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        clip-rule="evenodd"
                        d="M7.02697 9.38889l-4.219 3.53831 1.40821-4.98276L0 4.76725l5.07966-.0364L7.02697 0l1.89337 4.73085L14 4.76725 9.83776 7.94444 11.2431 13 7.02697 9.38889z"
                        fill="#2B2B2B"
                        fill-rule="evenodd"
                      />
                    </svg>
                  </span>
                  <span
                    aria-hidden="true"
                    class="emotion-14"
                  >
                    <svg
                      fill="none"
                      viewBox="0 0 14 13"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        clip-rule="evenodd"
                        d="M7.02697 9.38889l-4.219 3.53831 1.40821-4.98276L0 4.76725l5.07966-.0364L7.02697 0l1.89337 4.73085L14 4.76725 9.83776 7.94444 11.2431 13 7.02697 9.38889z"
                        fill="#2B2B2B"
                        fill-rule="evenodd"
                      />
                    </svg>
                  </span>
                  <span
                    aria-hidden="true"
                    class="emotion-14"
                  >
                    <svg
                      fill="none"
                      viewBox="0 0 14 13"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        clip-rule="evenodd"
                        d="M7.02697 9.38889l-4.219 3.53831 1.40821-4.98276L0 4.76725l5.07966-.0364L7.02697 0l1.89337 4.73085L14 4.76725 9.83776 7.94444 11.2431 13 7.02697 9.38889z"
                        fill="#2B2B2B"
                        fill-rule="evenodd"
                      />
                    </svg>
                  </span>
                </figure>
              </div>
            </a>
          </div>
          <div
            class="emotion-19"
          >
            <div
              class="emotion-3"
            >
              <div>
                <p
                  class="amp-cms--p"
                  style="text-align:left;"
                >
                  <span
                    class="amp-cms--subhead-1"
                  >
                    Quote Rich Text Default
                  </span>
                </p>
              </div>
            </div>
          </div>
        </div>
        <img
          alt="Desktop Image"
          class="emotion-21"
          src="https://2hmc8flz4ofg1dy89cc38f2xm.staging.bigcontent.io/i/athleta/986992_012_MAGW_AT_WMN_Tier_C_142_SP22_PL_3_1140?fmt=auto"
        />
        <div
          class="emotion-22"
        >
          <div
            class="emotion-3"
          >
            <div>
              <p
                class="amp-cms--p"
                style="text-align:left;"
              >
                <span
                  class="amp-cms--body-1"
                >
                  Main Image Rich Text 
                </span>
              </p>
            </div>
          </div>
        </div>
        <a
          aria-label="Link title"
          aria-labelledby="storytelling-product-rating-card"
          class="emotion-24"
          href="#"
          target="_blank"
        >
          <div
            class="emotion-25"
          >
            <div
              class="emotion-26"
              data-testid="product-card-image"
            >
              <img
                alt="alt"
                class="emotion-27"
                src="https://2hmc8flz4ofg1dy89cc38f2xm.staging.bigcontent.io/i/athleta/HOL1_Winter-Essentials_S?fmt=webp"
              />
            </div>
          </div>
        </a>
        <a
          aria-label="Link title"
          class="emotion-28"
          href="#"
          id="storytelling-product-rating-card"
          target="_blank"
        >
          <div
            class="emotion-29"
          >
            <div
              class="emotion-3"
            >
              <div>
                <p
                  class="amp-cms--p"
                  style="text-align:left;"
                >
                  <span
                    class="amp-cms--body-1"
                  >
                    Card Rich Text
                  </span>
                </p>
              </div>
            </div>
          </div>
        </a>
      </div>
    </div>
  </div>
</div>
`;

exports[`Athleta StorytellingAndProductRating on Mobile should render on mobile 1`] = `
.emotion-0 {
  background: -webkit-linear-gradient(45deg, #DAE2F8,#D6A4A4);
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  box-sizing: border-box;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  padding: 64px 10px;
  gap: 40px;
  width: 100%;
}

.emotion-1 {
  position: relative;
  display: grid;
  width: 100%;
  gap: 0;
  grid-template-columns: repeat(4,1fr);
  grid-template-rows: min-content minmax(min-content, max-content);
  grid-auto-flow: row;
  grid-template-areas: "header header header header" "richText-ctas richText-ctas richText-ctas richText-ctas" "hero-content hero-content hero-content hero-content" "hero-richText hero-richText hero-richText hero-richText" "quote-text quote-text quote-text quote-text" "product-card product-card product-card product-card" "card-richText card-richText card-richText card-richText";
}

.emotion-2 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  position: relative;
  grid-area: header;
  padding-bottom: 20px;
}

.emotion-3 .amp-cms--p {
  padding: 0;
  margin: 0;
  line-height: 0;
}

.emotion-3 .amp-cms--p>span {
  display: inline;
  white-space: break-spaces;
}

.emotion-3 .amp-cms--p a {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  position: relative;
  z-index: 2;
  pointer-events: auto;
}

.emotion-3 .amp-cms--p a:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-3 .amp-cms--legal-copy {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, 2.666666666666667vw);
  line-height: 1.4;
  letter-spacing: 0;
}

.emotion-3 sup {
  vertical-align: top;
  display: inline-block;
  margin-top: -0.25ex;
}

.emotion-3 sub {
  vertical-align: bottom;
  display: inline-block;
  margin-bottom: -0.25ex;
}

.emotion-3 .amp-cms--f-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 2.933333333333333vw;
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--f-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 2.933333333333333vw;
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--f-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 2.933333333333333vw;
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--fn-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 2.933333333333333vw;
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--body-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, 4.8vw);
  line-height: 1.2777777777777777;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--body-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, 4.266666666666667vw);
  line-height: 1.25;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--body-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, 3.733333333333334vw);
  line-height: 1.2857142857142858;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--body-4 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, 3.2vw);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--body-5 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, 2.666666666666667vw);
  line-height: 1.4;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--eyebrow-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, 8vw);
  line-height: 1;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--eyebrow-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, 6.933333333333333vw);
  line-height: 1;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--eyebrow-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, 6.4vw);
  line-height: 1;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--headline-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, 16vw);
  line-height: 1;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--headline-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, 14.666666666666666vw);
  line-height: 1;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--headline-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, 14.666666666666666vw);
  line-height: 1;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--headline-4 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, 13.333333333333334vw);
  line-height: 1.1;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--headline-5 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, 12vw);
  line-height: 1;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--headline-6 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, 10.666666666666668vw);
  line-height: 1;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--headline-7 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, 9.333333333333334vw);
  line-height: 1;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--headlineAlt-1 {
  color: #2B2B2B;
  font-size: max(13px, 17.066666666666666vw);
  line-height: 1;
  letter-spacing: -0.07999999999999999vw;
  font-weight: 400;
}

.emotion-3 .amp-cms--headlineAlt-2 {
  color: #2B2B2B;
  font-size: max(13px, 14.399999999999999vw);
  line-height: 1;
  letter-spacing: -0.07999999999999999vw;
  font-weight: 400;
}

.emotion-3 .amp-cms--headlineAlt-3 {
  color: #2B2B2B;
  font-size: max(13px, 9.066666666666666vw);
  line-height: 1;
  letter-spacing: -0.07999999999999999vw;
  font-weight: 400;
}

.emotion-3 .amp-cms--headlineAlt-4 {
  color: #2B2B2B;
  font-size: max(13px, 6.4vw);
  line-height: 1.4166666666666667;
  letter-spacing: -0.07999999999999999vw;
  font-weight: 400;
}

.emotion-3 .amp-cms--headlineAlt-5 {
  color: #2B2B2B;
  font-size: 3.733333333333334vw;
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-3 .amp-cms--headlineAlt-6 {
  color: #2B2B2B;
  font-size: 3.733333333333334vw;
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-3 .amp-cms--headlineAlt-7 {
  color: #2B2B2B;
  font-size: 3.733333333333334vw;
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-3 .amp-cms--promo-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(13px, 16vw);
  line-height: 1;
  letter-spacing: 0vw;
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-3 .amp-cms--promo-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(13px, 10.666666666666668vw);
  line-height: 1;
  letter-spacing: 0vw;
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-3 .amp-cms--promoAlt-1 {
  color: #2B2B2B;
  font-size: 3.733333333333334vw;
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 600;
}

.emotion-3 .amp-cms--subhead-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, 5.866666666666666vw);
  line-height: 1.0909090909090908;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--subhead-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, 5.333333333333334vw);
  line-height: 1.1;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--subhead-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, 4.8vw);
  line-height: 1.1111111111111112;
  letter-spacing: 0;
}

.emotion-4 {
  grid-area: richText-ctas;
}

.emotion-5 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  position: relative;
  grid-area: richText-ctas;
  padding-bottom: 20px;
}

.emotion-7 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  display: inline-block;
  text-transform: none;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 1px solid transparent;
  border-radius: 0;
  font-size: 14px;
  font-weight: 500;
  letter-spacing: 2%;
  min-height: unset;
  max-height: auto;
  line-height: 1.125;
  padding: 12px;
  width: auto;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  background-color: #FFFFFF;
  color: #000000;
  border-color: #000000;
  -webkit-align-self: flex-start;
  -ms-flex-item-align: flex-start;
  align-self: flex-start;
  white-space: normal;
  text-transform: none;
  margin-bottom: 40px;
}

.emotion-7:focus {
  outline: none;
}

.emotion-7>span {
  padding: 1px 0;
}

.emotion-7:hover,
.emotion-7:focus {
  color: #FFFFFF;
  background-color: #000000;
  border-color: #000000;
}

.emotion-7:active {
  color: #FFFFFF;
  background-color: #000000;
  border-color: #000000;
}

.emotion-8 {
  grid-area: quote-text;
  -webkit-align-self: end;
  -ms-flex-item-align: end;
  align-self: end;
  margin: 64px 0px;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  gap: 8px;
}

.emotion-9 {
  width: 41.85vw;
}

.emotion-9 figure {
  gap: 0.5334vw;
}

.emotion-10 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  -webkit-box-flex-flow: wrap;
  -webkit-flex-flow: wrap;
  -ms-flex-flow: wrap;
  flex-flow: wrap;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: unset;
  padding-bottom: 8px;
}

.emotion-11 {
  position: absolute;
  height: 1px;
  width: 1px;
  overflow: hidden;
  clip: rect(0 0 0 0);
}

.emotion-12 {
  position: relative;
  width: 60%;
  margin-right: 0.8125em;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-13 {
  width: 100%;
  margin: 0;
  padding: 0;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: space-around;
  -ms-flex-pack: space-around;
  -webkit-justify-content: space-around;
  justify-content: space-around;
  margin-block-start: 0;
  margin-block-end: 0;
  -webkit-margin-start: 0;
  margin-inline-start: 0;
  -webkit-margin-end: 0;
  margin-inline-end: 0;
}

.emotion-13 span {
  width: 100%;
  margin: 0;
}

.emotion-14 {
  display: inline-block;
  height: auto;
  width: auto;
  min-height: auto;
  min-width: auto;
}

.emotion-14 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-14 svg path {
  fill: #2B2B2B;
}

.emotion-14 svg rect {
  fill: #2B2B2B;
}

.emotion-19 {
  position: relative;
}

.emotion-21 {
  aspect-ratio: 3/4;
  height: 100%;
  object-fit: cover;
  grid-area: hero-content;
  width: 100%;
}

.emotion-22 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  position: relative;
  grid-area: hero-richText;
  padding-top: 12px;
}

.emotion-24 {
  grid-area: product-card;
  -webkit-align-self: end;
  -ms-flex-item-align: end;
  align-self: end;
}

.emotion-25 {
  grid-area: product-card;
  width: 100%;
  -webkit-align-self: end;
  -ms-flex-item-align: end;
  align-self: end;
}

.emotion-26 {
  -webkit-background-size: cover;
  background-size: cover;
  aspect-ratio: 3/4;
  overflow: hidden;
}

.emotion-27 {
  width: 100%;
  aspect-ratio: 3/4;
  object-fit: cover;
}

.emotion-28 {
  grid-area: card-richText;
}

.emotion-29 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  position: relative;
  grid-area: card-richText;
  margin-top: 0;
  padding-top: 10px;
}

<div>
  <div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      class="emotion-0"
      height="0"
      width="0"
    >
      <div
        class="emotion-1"
      >
        <div
          class="emotion-2"
        >
          <div
            class="emotion-3"
          >
            <div>
              <p
                class="amp-cms--p"
                style="text-align:left;"
              >
                <span
                  class="amp-cms--headlineAlt-3"
                >
                  Upper Text Rich Text Mobile
                </span>
              </p>
            </div>
          </div>
        </div>
        <div
          class="emotion-4"
        >
          <div
            class="emotion-5"
          >
            <div
              class="emotion-3"
            >
              <div>
                <p
                  class="amp-cms--p"
                  style="text-align:left;"
                >
                  <span
                    class="amp-cms--body-1"
                  >
                    Lower Text Rich Text Mobile
                  </span>
                </p>
              </div>
            </div>
          </div>
          <a
            class="emotion-7"
            color="dark"
            href="/moreshop"
          >
            Shop More
          </a>
        </div>
        <div
          class="emotion-8"
        >
          <div
            class="emotion-9"
          >
            <a
              class="emotion-10"
              data-testid="reviewRatings"
              tabindex="0"
            >
              <span
                class="emotion-11"
                data-testid="reviewRatingsAriaLabel"
              >
                Image of 5 stars, 5 out of the 5 stars are filled
              </span>
              <div
                class="emotion-12"
              >
                <div>
                  <svg
                    style="width: 0px; height: 0px; display: block;"
                    viewBox="0 0 14 13"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <defs>
                      <lineargradient
                        id="fractional-gradient_10"
                        x1="0"
                        x2="100%"
                        y1="0"
                        y2="0"
                      >
                        <stop
                          offset="0%"
                          stop-color="#2B2B2B"
                          stop-opacity="100%"
                        />
                        <stop
                          offset="0%"
                          stop-color="#2B2B2B"
                          stop-opacity="100%"
                        />
                        <stop
                          offset="0%"
                          stop-color="#CCC"
                          stop-opacity="100%"
                        />
                        <stop
                          offset="100%"
                          stop-color="#CCC"
                          stop-opacity="100%"
                        />
                      </lineargradient>
                    </defs>
                  </svg>
                </div>
                <figure
                  class="emotion-13"
                >
                  <span
                    aria-hidden="true"
                    class="emotion-14"
                  >
                    <svg
                      fill="none"
                      viewBox="0 0 14 13"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        clip-rule="evenodd"
                        d="M7.02697 9.38889l-4.219 3.53831 1.40821-4.98276L0 4.76725l5.07966-.0364L7.02697 0l1.89337 4.73085L14 4.76725 9.83776 7.94444 11.2431 13 7.02697 9.38889z"
                        fill="#2B2B2B"
                        fill-rule="evenodd"
                      />
                    </svg>
                  </span>
                  <span
                    aria-hidden="true"
                    class="emotion-14"
                  >
                    <svg
                      fill="none"
                      viewBox="0 0 14 13"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        clip-rule="evenodd"
                        d="M7.02697 9.38889l-4.219 3.53831 1.40821-4.98276L0 4.76725l5.07966-.0364L7.02697 0l1.89337 4.73085L14 4.76725 9.83776 7.94444 11.2431 13 7.02697 9.38889z"
                        fill="#2B2B2B"
                        fill-rule="evenodd"
                      />
                    </svg>
                  </span>
                  <span
                    aria-hidden="true"
                    class="emotion-14"
                  >
                    <svg
                      fill="none"
                      viewBox="0 0 14 13"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        clip-rule="evenodd"
                        d="M7.02697 9.38889l-4.219 3.53831 1.40821-4.98276L0 4.76725l5.07966-.0364L7.02697 0l1.89337 4.73085L14 4.76725 9.83776 7.94444 11.2431 13 7.02697 9.38889z"
                        fill="#2B2B2B"
                        fill-rule="evenodd"
                      />
                    </svg>
                  </span>
                  <span
                    aria-hidden="true"
                    class="emotion-14"
                  >
                    <svg
                      fill="none"
                      viewBox="0 0 14 13"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        clip-rule="evenodd"
                        d="M7.02697 9.38889l-4.219 3.53831 1.40821-4.98276L0 4.76725l5.07966-.0364L7.02697 0l1.89337 4.73085L14 4.76725 9.83776 7.94444 11.2431 13 7.02697 9.38889z"
                        fill="#2B2B2B"
                        fill-rule="evenodd"
                      />
                    </svg>
                  </span>
                  <span
                    aria-hidden="true"
                    class="emotion-14"
                  >
                    <svg
                      fill="none"
                      viewBox="0 0 14 13"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        clip-rule="evenodd"
                        d="M7.02697 9.38889l-4.219 3.53831 1.40821-4.98276L0 4.76725l5.07966-.0364L7.02697 0l1.89337 4.73085L14 4.76725 9.83776 7.94444 11.2431 13 7.02697 9.38889z"
                        fill="#2B2B2B"
                        fill-rule="evenodd"
                      />
                    </svg>
                  </span>
                </figure>
              </div>
            </a>
          </div>
          <div
            class="emotion-19"
          >
            <div
              class="emotion-3"
            >
              <div>
                <p
                  class="amp-cms--p"
                  style="text-align:left;"
                >
                  <span
                    class="amp-cms--body-1"
                  >
                    Quote Rich Text Mobile
                  </span>
                </p>
              </div>
            </div>
          </div>
        </div>
        <img
          alt="Mobile Image"
          class="emotion-21"
          src="https://2hmc8flz4ofg1dy89cc38f2xm.staging.bigcontent.io/i/athleta/SU22_D1_BestOfBottoms_Cabo_IMG_XL_1?fmt=auto"
        />
        <div
          class="emotion-22"
        >
          <div
            class="emotion-3"
          >
            <div>
              <p
                class="amp-cms--p"
                style="text-align:left;"
              >
                <span
                  class="amp-cms--body-1"
                >
                  Main Image Rich Text 
                </span>
              </p>
            </div>
          </div>
        </div>
        <a
          aria-label="Link title"
          aria-labelledby="storytelling-product-rating-card"
          class="emotion-24"
          href="#"
          target="_blank"
        >
          <div
            class="emotion-25"
          >
            <div
              class="emotion-26"
              data-testid="product-card-image"
            >
              <img
                alt="alt"
                class="emotion-27"
                src="https://2hmc8flz4ofg1dy89cc38f2xm.staging.bigcontent.io/i/athleta/HOL1_Winter-Essentials_S?fmt=webp"
              />
            </div>
          </div>
        </a>
        <a
          aria-label="Link title"
          class="emotion-28"
          href="#"
          id="storytelling-product-rating-card"
          target="_blank"
        >
          <div
            class="emotion-29"
          >
            <div
              class="emotion-3"
            >
              <div>
                <p
                  class="amp-cms--p"
                  style="text-align:left;"
                >
                  <span
                    class="amp-cms--body-1"
                  >
                    Card Rich Text
                  </span>
                </p>
              </div>
            </div>
          </div>
        </a>
      </div>
    </div>
  </div>
</div>
`;
