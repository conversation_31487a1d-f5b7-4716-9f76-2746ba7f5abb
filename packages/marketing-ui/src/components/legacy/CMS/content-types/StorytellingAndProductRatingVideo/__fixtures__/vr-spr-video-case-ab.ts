// @ts-nocheck
import { StorytellingAndProductRatingContentType } from '../../StorytellingAndProductRating/types';
// Example A
export const storytellingAndProductRatingVideoCompA: StorytellingAndProductRatingContentType = {
  _meta: {
    name: 'Storytelling And Product Rating Video - UAT',
    schema: 'https://cms.gap.com/schema/content/v1/storytelling-product-rating-video.json',
    deliveryId: '04807521-5af9-4f27-ad70-ae26d084bb0c',
  },
  defaultVideo: {
    desktop: {
      fallbackImage: [
        {
          image: {
            _meta: {
              schema: 'http://bigcontent.io/cms/schema/v1/core#/definitions/image-link',
            },
            id: 'f943333f-a76f-48a4-bb31-75a45486a625',
            name: 'storytelling_backg_xl@2x',
            endpoint: 'athleta',
            defaultHost: '1d9xxafvh577y12766tt2karod.staging.bigcontent.io',
          },
          altText: '#',
          variations: [
            {
              variation: 'desktop',
            },
            {
              variation: 'mobile',
            },
          ],
          fliph: false,
          flipv: false,
          enableChroma: false,
          chromaQuality: 80,
        },
      ],
      url: 'https://player.vimeo.com/video/797530501?h=ecd25dc777&autopause=1&muted=1&controls=1',
    },
    mobile: {
      fallbackImage: [
        {
          image: {
            _meta: {
              schema: 'http://bigcontent.io/cms/schema/v1/core#/definitions/image-link',
            },
            id: '4cf08578-5394-42f3-a88f-404681a76cc1',
            name: 'FPO_198671_292_SBCY_AT_WMN_165_SU23_All_Day_Active_D01_4542 1',
            endpoint: 'athleta',
            defaultHost: '1d9xxafvh577y12766tt2karod.staging.bigcontent.io',
          },
          altText: '#',
          variations: [
            {
              variation: 'desktop',
            },
            {
              variation: 'mobile',
            },
          ],
        },
      ],
    },
  },
  storytellingProductRating: {
    general: {
      background: {
        background: {
          type: 'solid',
          color: '#EAD1B1',
        },
      },
      showHideBasedOnScreenSize: 'alwaysShow',
    },
    content: {
      text: {
        upperText: {
          defaultText: '<p class="amp-cms--p" style="text-align:left;"><span class="amp-cms--headlineAlt-2">Meet the new Jacket</span></p>',
          mobileOverride:
            '<p class="amp-cms--p" style="text-align:left;"><span class="amp-cms--headlineAlt-2">Meet the new</span></p><p class="amp-cms--p" style="text-align:left;"><span class="amp-cms--headlineAlt-2">Jacket</span></p>',
        },
        lowerText: {
          defaultText:
            '<p class="amp-cms--p" style="text-align:left;"><span class="amp-cms--body-3">Stay comfortable on cold-weather commutes and chilly evening walks.  Gives you room to move without the bulk—and is made of recycled materials.</span></p>',
          mobileOverride:
            '<p class="amp-cms--p" style="text-align:left;"><span class="amp-cms--body-3">Stay comfortable on cold-weather commutes and </span></p><p class="amp-cms--p" style="text-align:left;"><span class="amp-cms--body-3">chilly evening walks. Gives you room to move</span></p><p class="amp-cms--p" style="text-align:left;"><span class="amp-cms--body-3">without the bulk—and is made of recycled materials.</span></p>',
        },
      },
      ctaButton: [
        {
          buttonStyle: {
            buttonStyle: 'solid',
            buttonColor: 'dark',
          },
          cta: {
            label: 'Shop all Jackets',
            value: '#',
          },
        },
      ],
      ratingColor: 'dark',
      quoteText: {
        defaultText:
          '<p class="amp-cms--p" style="text-align:left;"><span class="amp-cms--headlineAlt-4">“Love this lightweight</span></p><p class="amp-cms--p" style="text-align:left;"><span class="amp-cms--headlineAlt-4">jacket. Super warm</span></p><p class="amp-cms--p" style="text-align:left;"><span class="amp-cms--headlineAlt-4">and great for layering.”</span></p><p class="amp-cms--p" style="text-align:left;"><span class="amp-cms--body-2"> - Lisa Smith, Portland Or.</span></p>',
        mobileOverride:
          '<p class="amp-cms--p" style="text-align:left;"><span class="amp-cms--headlineAlt-4">“Love this lightweight jacket. Super warm and great for layering.”</span></p><p class="amp-cms--p" style="text-align:left;"><span class="amp-cms--body-3"> - Lisa Smith, Portland Or.</span></p>',
      },
      rating: 4.5,
    },
    productCard: {
      image: [
        {
          image: {
            _meta: {
              schema: 'http://bigcontent.io/cms/schema/v1/core#/definitions/image-link',
            },
            id: '5ed45ae8-8488-45d9-9e14-56a6e22c2541',
            name: 'storytelling1@2x',
            endpoint: 'athleta',
            defaultHost: '1d9xxafvh577y12766tt2karod.staging.bigcontent.io',
          },
          altText: '#',
          variations: [
            {
              variation: 'desktop',
            },
            {
              variation: 'mobile',
            },
          ],
          fliph: false,
          flipv: false,
          enableChroma: false,
          chromaQuality: 80,
        },
      ],
      url: {
        label: '#',
        value: '#',
      },
      text: '<p class="amp-cms--p" style="text-align:left;"><span class="amp-cms--body-3">Product Name</span></p><p class="amp-cms--p" style="text-align:left;"><span class="amp-cms--body-3">3 colors</span></p><p class="amp-cms--p" style="text-align:left;"><span class="amp-cms--body-3">$00</span></p>',
    },
  },
};
// example B
export const storytellingAndProductRatingVideoCompB: StorytellingAndProductRatingContentType = {
  _meta: {
    name: 'Storytelling And Product Rating Video - UAT',
    schema: 'https://cms.gap.com/schema/content/v1/storytelling-product-rating-video.json',
    deliveryId: '7d5b880b-fc5b-42e2-8be6-b976b138f86a',
  },
  defaultVideo: {
    desktop: {
      fallbackImage: [
        {
          image: {
            _meta: {
              schema: 'http://bigcontent.io/cms/schema/v1/core#/definitions/image-link',
            },
            id: 'cf7ff592-6c67-46b8-a5bf-fdebd3940647',
            name: 'storytelling2@2x',
            endpoint: 'athleta',
            defaultHost: '1d9xxafvh577y12766tt2karod.staging.bigcontent.io',
          },
          altText: '#',
          variations: [
            {
              variation: 'desktop',
            },
            {
              variation: 'mobile',
            },
          ],
          fliph: false,
          flipv: false,
          enableChroma: false,
          chromaQuality: 80,
        },
      ],
      url: 'https://player.vimeo.com/video/797530501?h=ecd25dc777&autopause=1&muted=1&controls=1',
    },
    mobile: {
      fallbackImage: [
        {
          image: {
            _meta: {
              schema: 'http://bigcontent.io/cms/schema/v1/core#/definitions/image-link',
            },
            id: '5ed45ae8-8488-45d9-9e14-56a6e22c2541',
            name: 'storytelling1@2x',
            endpoint: 'athleta',
            defaultHost: '1d9xxafvh577y12766tt2karod.staging.bigcontent.io',
          },
          altText: '#',
          variations: [
            {
              variation: 'desktop',
            },
            {
              variation: 'mobile',
            },
          ],
          fliph: false,
          flipv: false,
          enableChroma: false,
          chromaQuality: 80,
        },
      ],
    },
  },
  storytellingProductRating: {
    general: {
      background: {
        background: {
          type: 'gradient',
          gradient: {
            from: '#FFFFFF',
            to: '#EAD1B1',
          },
        },
      },
      showHideBasedOnScreenSize: 'alwaysShow',
    },
    content: {
      text: {
        upperText: {
          defaultText: '<p class="amp-cms--p" style="text-align:left;"><span class="amp-cms--headlineAlt-2">Meet the new Jacket</span></p>',
          mobileOverride:
            '<p class="amp-cms--p" style="text-align:left;"><span class="amp-cms--headlineAlt-2">Meet the new</span></p><p class="amp-cms--p" style="text-align:left;"><span class="amp-cms--headlineAlt-2">Jacket</span></p>',
        },
      },
      ctaButton: [
        {
          buttonStyle: {
            buttonStyle: 'solid',
            buttonColor: 'dark',
          },
          cta: {
            label: 'Shop all Jackets',
            value: '#',
          },
        },
      ],
      ratingColor: 'dark',
    },
    productCard: {
      image: [
        {
          image: {
            _meta: {
              schema: 'http://bigcontent.io/cms/schema/v1/core#/definitions/image-link',
            },
            id: '2d72aa0d-cd5b-4feb-8eb9-738152dd552f',
            name: 'AW_Product_IMG_S',
            endpoint: 'athleta',
            defaultHost: '1d9xxafvh577y12766tt2karod.staging.bigcontent.io',
          },
          altText: '#',
          variations: [
            {
              variation: 'desktop',
            },
            {
              variation: 'mobile',
            },
          ],
          fliph: false,
          flipv: false,
          enableChroma: false,
          chromaQuality: 80,
        },
      ],
      url: {
        label: '#',
        value: '#',
      },
      text: '<p class="amp-cms--p" style="text-align:left;"><span class="amp-cms--body-3">Product Name</span></p><p class="amp-cms--p" style="text-align:left;"><span class="amp-cms--body-3">3 colors</span></p><p class="amp-cms--p" style="text-align:left;"><span class="amp-cms--body-3">$00</span></p>',
    },
  },
};
