// @ts-nocheck
import { StorytellingAndProductRatingContentType } from '../../StorytellingAndProductRating/types';
// Example C
export const storytellingAndProductRatingVideoCompC: StorytellingAndProductRatingContentType = {
  _meta: {
    name: 'Storytelling And Product Rating Video - UAT',
    schema: 'https://cms.gap.com/schema/content/v1/storytelling-product-rating-video.json',
    deliveryId: '43621062-268f-45d2-b0b3-cc11004ec7da',
  },
  defaultVideo: {
    desktop: {
      fallbackImage: [
        {
          image: {
            _meta: {
              schema: 'http://bigcontent.io/cms/schema/v1/core#/definitions/image-link',
            },
            id: 'f943333f-a76f-48a4-bb31-75a45486a625',
            name: 'storytelling_backg_xl@2x',
            endpoint: 'athleta',
            defaultHost: '1d9xxafvh577y12766tt2karod.staging.bigcontent.io',
          },
          altText: '#',
          variations: [
            {
              variation: 'desktop',
            },
            {
              variation: 'mobile',
            },
          ],
          fliph: false,
          flipv: false,
          enableChroma: false,
          chromaQuality: 80,
        },
      ],
      url: 'https://player.vimeo.com/video/797530501?h=ecd25dc777&autopause=1&muted=1&controls=1',
    },
    mobile: {
      fallbackImage: [
        {
          image: {
            _meta: {
              schema: 'http://bigcontent.io/cms/schema/v1/core#/definitions/image-link',
            },
            id: '5ed45ae8-8488-45d9-9e14-56a6e22c2541',
            name: 'storytelling1@2x',
            endpoint: 'athleta',
            defaultHost: '1d9xxafvh577y12766tt2karod.staging.bigcontent.io',
          },
          altText: '#',
          variations: [
            {
              variation: 'desktop',
            },
            {
              variation: 'mobile',
            },
          ],
          fliph: false,
          flipv: false,
          enableChroma: false,
          chromaQuality: 80,
        },
      ],
    },
  },
  storytellingProductRating: {
    general: {
      background: {
        background: {
          type: 'image',
          images: [
            {
              image: {
                _meta: {
                  schema: 'http://bigcontent.io/cms/schema/v1/core#/definitions/image-link',
                },
                id: '1a0a7a61-f504-48b4-a4bc-0d1cef77c637',
                name: 'HP_Storytelling_DowntoEarth_XL@2x',
                endpoint: 'athleta',
                defaultHost: '1d9xxafvh577y12766tt2karod.staging.bigcontent.io',
              },
              altText: 'Background',
              variations: [
                {
                  variation: 'desktop',
                },
                {
                  variation: 'mobile',
                },
              ],
              fliph: false,
              flipv: false,
              enableChroma: false,
              chromaQuality: 80,
            },
          ],
        },
      },
      showHideBasedOnScreenSize: 'alwaysShow',
    },
    content: {
      text: {
        lowerText: {
          defaultText:
            '<p class="amp-cms--p" style="text-align:left;"><span class="amp-cms--body-3">Stay comfortable on cold-weather commutes and chilly evening walks. Gives you room to move without the bulk—and is made of recycled materials.</span></p>',
          mobileOverride:
            '<p class="amp-cms--p" style="text-align:left;"><span class="amp-cms--body-3">Stay comfortable on cold-weather commutes and </span></p><p class="amp-cms--p" style="text-align:left;"><span class="amp-cms--body-3">chilly evening walks. Gives you room to move </span></p><p class="amp-cms--p" style="text-align:left;"><span class="amp-cms--body-3">without the bulk—and is made of recycled materials.</span></p>',
        },
      },
      ctaButton: [
        {
          buttonStyle: {
            buttonStyle: 'solid',
            buttonColor: 'dark',
          },
          cta: {
            label: 'Shop all Jackets',
            value: '#',
          },
        },
      ],
      ratingColor: 'dark',
      quoteText: {
        defaultText:
          '<p class="amp-cms--p" style="text-align:left;"><span class="amp-cms--headlineAlt-4">“Love this lightweight</span></p><p class="amp-cms--p" style="text-align:left;"><span class="amp-cms--headlineAlt-4">jacket. Super warm</span></p><p class="amp-cms--p" style="text-align:left;"><span class="amp-cms--headlineAlt-4">and great for layering.”</span></p><p class="amp-cms--p" style="text-align:left;"><span class="amp-cms--body-2"> - Lisa Smith, Portland Or.</span></p>',
        mobileOverride:
          '<p class="amp-cms--p" style="text-align:left;"><span class="amp-cms--headlineAlt-4">“Love this lightweight jacket. Super warm and great for layering.”</span></p><p class="amp-cms--p" style="text-align:left;"><span class="amp-cms--body-3"> - Lisa Smith, Portland Or.</span></p>',
      },
      rating: 4.5,
    },
  },
};
// Example D
export const storytellingAndProductRatingVideoCompD: StorytellingAndProductRatingContentType = {
  _meta: {
    name: 'Storytelling And Product Rating Video - UAT',
    schema: 'https://cms.gap.com/schema/content/v1/storytelling-product-rating-video.json',
    deliveryId: '8ab61321-9ead-4fc1-979e-9d2a9e1d0528',
  },
  defaultVideo: {
    desktop: {
      fallbackImage: [
        {
          image: {
            _meta: {
              schema: 'http://bigcontent.io/cms/schema/v1/core#/definitions/image-link',
            },
            id: '8a535945-44ca-4692-86d7-73f2da746f08',
            name: 'wayfinding_SU1_1',
            endpoint: 'athleta',
            defaultHost: '1d9xxafvh577y12766tt2karod.staging.bigcontent.io',
          },
          altText: '#',
          variations: [
            {
              variation: 'desktop',
            },
            {
              variation: 'mobile',
            },
          ],
          fliph: false,
          flipv: false,
          enableChroma: false,
          chromaQuality: 80,
        },
      ],
      url: 'https://player.vimeo.com/video/797530501?h=ecd25dc777&autopause=1&muted=1&controls=1',
    },
    mobile: {
      fallbackImage: [
        {
          image: {
            _meta: {
              schema: 'http://bigcontent.io/cms/schema/v1/core#/definitions/image-link',
            },
            id: '6f137c92-b542-4a40-a5a0-3f5fa1e69921',
            name: 'wayfinding_SU1_3',
            endpoint: 'athleta',
            defaultHost: '1d9xxafvh577y12766tt2karod.staging.bigcontent.io',
          },
          altText: '#',
          variations: [
            {
              variation: 'desktop',
            },
            {
              variation: 'mobile',
            },
          ],
          fliph: false,
          flipv: false,
          enableChroma: false,
          chromaQuality: 80,
        },
      ],
    },
  },
  storytellingProductRating: {
    general: {
      background: {
        background: {
          type: 'solid',
          color: '#000000',
        },
      },
      showHideBasedOnScreenSize: 'alwaysShow',
    },
    content: {
      text: {
        lowerText: {
          defaultText:
            '<p class="amp-cms--p" style="text-align:left;"><span class="amp-cms--body-3" style="color:#FFFFFF">Stay comfortable on cold-weather commutes and chilly evening walks. Gives you room to move without the bulk — and is made of recycled materials.</span></p>',
        },
        upperText: {
          mobileOverride:
            '<p class="amp-cms--p" style="text-align:left;"><span class="amp-cms--body-3" style="color:#FFFFFF">Stay comfortable on cold-weather commutes and chilly evening walks. Gives you room to move without the bulk — and is made of recycled materials.</span></p>',
        },
      },
      ctaButton: [
        {
          buttonStyle: {
            buttonStyle: 'border',
            buttonColor: 'dark',
          },
          cta: {
            label: 'Shop all Jackets',
            value: '#',
          },
        },
      ],
      ratingColor: 'light',
      quoteText: {
        defaultText:
          '<p class="amp-cms--p" style="text-align:left;"><span class="amp-cms--headlineAlt-4" style="color:#FFFFFF">“Love this lightweight</span></p><p class="amp-cms--p" style="text-align:left;"><span class="amp-cms--headlineAlt-4" style="color:#FFFFFF">jacket. Super warm</span></p><p class="amp-cms--p" style="text-align:left;"><span class="amp-cms--headlineAlt-4" style="color:#FFFFFF">and great for layering.”</span></p><p class="amp-cms--p" style="text-align:left;"><span class="amp-cms--body-2" style="color:#FFFFFF"> - Lisa Smith, Portland Or.</span></p>',
      },
      rating: 4.5,
    },
  },
};
