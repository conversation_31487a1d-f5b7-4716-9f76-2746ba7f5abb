// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`SubCategoryBannerVariableHeightCarousel SCBVHCarouselWrapper StaticFile should render persistent on mobile below 1`] = `
<DocumentFragment>
  .emotion-0 {
  margin-left: auto;
  margin-right: auto;
  max-width: 1280px;
}

.emotion-1 nav .slick-slider ul.slick-dots {
  bottom: -185px;
}

.emotion-2 {
  position: relative;
}

.emotion-3 {
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-3 .slick-list {
  overflow: hidden;
}

.emotion-3 .slick-track {
  width: -webkit-max-content!important;
  width: -moz-max-content!important;
  width: max-content!important;
}

.emotion-3 button.slick-next.slick-arrow.slick-next,
.emotion-3 button.slick-prev.slick-arrow.slick-prev {
  z-index: 2;
  top: min(25.32551319648094vw, 50%);
  height: 44px;
  width: 44px;
  background: #003764;
  opacity: 75%;
}

.emotion-3 button.slick-next.slick-arrow.slick-next>span,
.emotion-3 button.slick-prev.slick-arrow.slick-prev>span {
  margin: auto;
}

.emotion-3 button.slick-next.slick-arrow.slick-next svg,
.emotion-3 button.slick-prev.slick-arrow.slick-prev svg {
  margin: auto;
  height: 25px;
  width: 14px;
}

.emotion-3 button.slick-next.slick-arrow.slick-next span svg path,
.emotion-3 button.slick-prev.slick-arrow.slick-prev span svg path {
  fill: #ffffff;
}

.emotion-3 button.slick-next.slick-arrow.slick-next:hover,
.emotion-3 button.slick-prev.slick-arrow.slick-prev:hover {
  opacity: 100%;
}

.emotion-3 button.slick-next.slick-arrow.slick-next:focus,
.emotion-3 button.slick-prev.slick-arrow.slick-prev:focus {
  background: #003764;
}

.emotion-3 button.slick-next.slick-arrow.slick-next:focus-visible,
.emotion-3 button.slick-prev.slick-arrow.slick-prev:focus-visible {
  outline: auto;
}

.emotion-3 .slick-slide {
  margin: 0;
  width: auto;
}

.emotion-3 .slick-disabled {
  display: none!important;
}

.emotion-3 .slick-next {
  left: calc(100% - 44px);
}

.emotion-3 .slick-slide:first-of-type a>div:nth-of-type(2)>div>div:nth-of-type(2) {
  border-left: none;
}

.emotion-3 .slick-slide>div:first-of-type {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-3 .slick-slide>div:first-of-type>div:first-of-type {
  width: 100%;
}

.emotion-4 {
  position: relative;
}

.emotion-4 .slick-slider {
  position: relative;
  display: block;
  box-sizing: border-box;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  -webkit-touch-callout: none;
  touch-action: pan-y;
  -webkit-tap-highlight-color: transparent;
  scroll-behavior: smooth;
}

.emotion-4 .slick-slider .slick-track,
.emotion-4 .slick-slider .slick-list {
  -webkit-transform: translate3d(0, 0, 0);
  -moz-transform: translate3d(0, 0, 0);
  -ms-transform: translate3d(0, 0, 0);
  transform: translate3d(0, 0, 0);
}

.emotion-4 .slick-list {
  position: relative;
  display: block;
  overflow-x: hidden;
  overflow-y: hidden;
  margin: 0;
  padding: 0;
}

.emotion-4 .slick-list:focus {
  outline: none;
}

.emotion-4 .slick-list.dragging {
  cursor: pointer;
  cursor: hand;
}

.emotion-4 .slick-track {
  position: relative;
  top: 0;
  left: 0;
  display: block;
  margin-left: auto;
  margin-right: auto;
}

.emotion-4 .slick-track:before,
.emotion-4 .slick-track:after {
  display: table;
  content: "";
}

.emotion-4 .slick-track:after {
  clear: both;
}

.emotion-4 .slick-slide {
  display: none;
  float: left;
  height: 100%;
  min-height: 1px;
}

.emotion-4 .slick-slide img {
  display: block;
}

.emotion-4 .slick-slide.slick-loading img {
  display: none;
}

.emotion-4 .slick-slide.dragging img {
  pointer-events: none;
}

.emotion-4 [dir="rtl"] .slick-slide {
  float: right;
}

.emotion-4 .slick-initialized .slick-slide,
.emotion-4 .slick-vertical .slick-slide {
  display: block;
}

.emotion-4 .slick-vertical .slick-slide {
  border: 1px solid transparent;
  height: auto;
}

.emotion-4 .slick-loading .slick-track,
.emotion-4 .slick-loading .slick-slide {
  visibility: hidden;
}

.emotion-4 .slick-loading .slick-list {
  background: #fff url("./ajax-loader.gif") center center no-repeat;
}

.emotion-4 .slick-arrow.slick-hidden {
  display: none;
}

.emotion-4 .slick-prev,
.emotion-4 .slick-next {
  font-size: 0;
  line-height: 0;
  position: absolute;
  top: 50%;
  display: block;
  padding: 0;
  cursor: pointer;
  color: transparent;
  border: none;
  outline: none;
  background: transparent;
}

.emotion-4 .slick-prev:hover,
.emotion-4 .slick-next:hover,
.emotion-4 .slick-prev:focus,
.emotion-4 .slick-next:focus {
  color: transparent;
  outline: none;
  background: transparent;
  opacity: 1;
}

.emotion-4 .slick-prev.slick-disabled,
.emotion-4 .slick-next.slick-disabled {
  opacity: 0.25;
}

.emotion-4 .slick-prev {
  left: -0;
  -webkit-transform: translate(0, -50%) rotate(90deg);
  -moz-transform: translate(0, -50%) rotate(90deg);
  -ms-transform: translate(0, -50%) rotate(90deg);
  transform: translate(0, -50%) rotate(90deg);
}

.emotion-4 .slick-prev span {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-4 [dir="rtl"] .slick-prev {
  right: -0;
  left: auto;
}

.emotion-4 .slick-next {
  right: -0;
  -webkit-transform: translate(0, -50%) rotate(-90deg);
  -moz-transform: translate(0, -50%) rotate(-90deg);
  -ms-transform: translate(0, -50%) rotate(-90deg);
  transform: translate(0, -50%) rotate(-90deg);
}

.emotion-4 .slick-next span {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-4 [dir="rtl"] .slick-next {
  right: auto;
  left: -0;
}

.emotion-4 .slick-dotted.slick-slider {
  margin-bottom: 30px;
}

.emotion-4 .slick-dots {
  position: absolute;
  bottom: -25px;
  display: block;
  width: 100%;
  padding: 0;
  margin: 0;
  list-style: none;
  text-align: center;
}

.emotion-4 .slick-dots li {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: 20px;
  height: 20px;
  margin: 0 5px;
  padding: 0;
  cursor: pointer;
}

.emotion-4 .slick-dots li.slick-active button:before {
  opacity: 0.75;
  color: black;
}

.emotion-4 .slick-dots li button {
  font-size: 0;
  line-height: 0;
  display: block;
  padding: 5px;
  cursor: pointer;
  color: transparent;
  border: 0;
  outline: none;
  background: transparent;
}

.emotion-4 .slick-dots li button:hover,
.emotion-4 .slick-dots li button:focus {
  outline: none;
}

.emotion-4 .slick-dots li button:hover:before,
.emotion-4 .slick-dots li button:focus:before,
.emotion-4 .slick-dots li button:hover:before,
.emotion-4 .slick-dots li button:focus:before {
  opacity: 1;
}

.emotion-4 .slick-dots li button:before {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 5px;
  height: 5px;
  content: "";
  opacity: 0.25;
  background-color: black;
  border-radius: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-5 {
  display: inline-block;
  height: 2rem;
  width: 2rem;
  min-height: 2rem;
  min-width: 2rem;
}

.emotion-5 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-7 {
  max-height: 200px;
  overflow: hidden;
}

.emotion-8 {
  overflow: hidden;
  position: relative;
  box-sizing: border-box;
  aspect-ratio: 341/200;
}

.emotion-9 {
  -webkit-background-size: cover;
  background-size: cover;
  aspect-ratio: 341/200;
  overflow: hidden;
}

.emotion-10 {
  width: 100%;
  aspect-ratio: 341/200;
  object-fit: cover;
}

.emotion-11 {
  width: 100%;
  height: 100%;
  position: absolute;
  left: 0;
  top: 0;
  z-index: 1;
}

.emotion-28 {
  width: 100%;
  height: 100%;
  padding: 30px 16px;
  box-sizing: border-box;
  position: static;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  grid-template-columns: repeat(3, minmax(20%,max-content));
  -webkit-align-content: center;
  -ms-flex-line-pack: center;
  align-content: center;
  grid-template-rows: repeat(3, minmax(0, max-content));
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  row-gap: 18px;
}

.emotion-28 .rteWrapperDivs {
  grid-column: 2;
  grid-row: 2;
}

.emotion-28 .ctaWrapperDivs {
  grid-column: 2;
  grid-row: 2;
}

.emotion-29 {
  box-sizing: content-box;
  text-align: center;
}

.emotion-30 {
  text-align: center;
}

.emotion-30 .amp-cms--p {
  padding: 0;
  margin: 0;
  line-height: 0;
}

.emotion-30 .amp-cms--p>span {
  display: inline;
  white-space: break-spaces;
}

.emotion-30 .amp-cms--p a {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  position: relative;
  z-index: 2;
  pointer-events: auto;
}

.emotion-30 .amp-cms--p a:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-30 .amp-cms--legal-copy {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 10px;
  line-height: 1.5;
  letter-spacing: 0;
}

.emotion-30 sup {
  vertical-align: top;
  display: inline-block;
  margin-top: -0.20ex;
}

.emotion-30 sub {
  vertical-align: bottom;
  display: inline-block;
  margin-bottom: -0.20ex;
}

.emotion-30 .amp-cms--f-0 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 11px;
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-30 .amp-cms--f-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 11px;
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-30 .amp-cms--f-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 11px;
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-30 .amp-cms--fn-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 11px;
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-30 .amp-cms--body-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 14px;
  line-height: 1.5;
  letter-spacing: 0.336px;
  font-weight: 500;
}

.emotion-30 .amp-cms--body-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.5;
  letter-spacing: 0.288px;
  font-weight: 500;
}

.emotion-30 .amp-cms--body-3 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.5;
  letter-spacing: 0.288px;
}

.emotion-30 .amp-cms--body-4 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 10px;
  line-height: 1.5;
  letter-spacing: 0;
}

.emotion-30 .amp-cms--body-5 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 10px;
  line-height: 1.5;
  letter-spacing: 0;
}

.emotion-30 .amp-cms--eyebrow-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 23px;
  line-height: 1;
  letter-spacing: 0.92px;
  font-weight: 500;
}

.emotion-30 .amp-cms--eyebrow-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 19px;
  line-height: 1;
  letter-spacing: 0.76px;
  font-weight: 500;
}

.emotion-30 .amp-cms--eyebrow-3 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 14px;
  line-height: 1.0714285714285714;
  letter-spacing: 0.56px;
  font-weight: 500;
}

.emotion-30 .amp-cms--headline-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 68px;
  line-height: 1;
  letter-spacing: 0;
  font-weight: 700;
}

.emotion-30 .amp-cms--headline-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 50px;
  line-height: 1;
  letter-spacing: 0;
  font-weight: 700;
}

.emotion-30 .amp-cms--headline-3 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 43px;
  line-height: 1;
  letter-spacing: 0;
  font-weight: 700;
}

.emotion-30 .amp-cms--headline-4 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 36px;
  line-height: 1;
  letter-spacing: 0.72px;
  font-weight: 700;
}

.emotion-30 .amp-cms--headline-5 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 28px;
  line-height: 1;
  letter-spacing: 1.12px;
  font-weight: 700;
}

.emotion-30 .amp-cms--headline-6 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 24px;
  line-height: 1.0833333333333333;
  letter-spacing: 0.96px;
  font-weight: 700;
}

.emotion-30 .amp-cms--headline-7 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 20px;
  line-height: 1;
  letter-spacing: 0.48px;
  font-weight: 700;
}

.emotion-30 .amp-cms--headlineAlt-1 {
  color: #003764;
  font-size: 64px;
  line-height: 1;
  letter-spacing: -0.3px;
  font-weight: 400;
}

.emotion-30 .amp-cms--headlineAlt-2 {
  color: #003764;
  font-size: 54px;
  line-height: 1;
  letter-spacing: -0.3px;
  font-weight: 400;
}

.emotion-30 .amp-cms--headlineAlt-3 {
  color: #003764;
  font-size: 34px;
  line-height: 1;
  letter-spacing: -0.3px;
  font-weight: 400;
}

.emotion-30 .amp-cms--headlineAlt-4 {
  color: #003764;
  font-size: 24px;
  line-height: 1.4166666666666667;
  letter-spacing: -0.3px;
  font-weight: 400;
}

.emotion-30 .amp-cms--headlineAlt-5 {
  color: #003764;
  font-size: 14px;
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-30 .amp-cms--headlineAlt-6 {
  color: #003764;
  font-size: 14px;
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-30 .amp-cms--headlineAlt-7 {
  color: #003764;
  font-size: 14px;
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-30 .amp-cms--promo-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 60px;
  line-height: 1;
  letter-spacing: 0px;
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-30 .amp-cms--promo-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 40px;
  line-height: 1;
  letter-spacing: 0px;
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-30 .amp-cms--promoAlt-1 {
  color: #003764;
  font-size: 14px;
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 600;
}

.emotion-30 .amp-cms--subhead-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 20px;
  line-height: 1.5;
  letter-spacing: 0;
  font-weight: 500;
}

.emotion-30 .amp-cms--subhead-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 18px;
  line-height: 1.5;
  letter-spacing: 0;
  font-weight: 500;
}

.emotion-30 .amp-cms--subhead-3 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 16px;
  line-height: 1.5;
  letter-spacing: 0;
  font-weight: 500;
}

.emotion-31 {
  display: block;
  width: 100%;
  position: relative;
  z-index: 2;
  text-align: center;
}

.emotion-32 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 700;
  letter-spacing: 1.6800000000000002px;
  min-height: 44px;
  max-height: auto;
  line-height: 1.1428571428571428;
  padding: 12px 22px;
  width: auto;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  background-color: #FFFFFF;
  color: #003764;
  border-color: #003764;
  -webkit-align-self: flex-start;
  -ms-flex-item-align: flex-start;
  align-self: flex-start;
  white-space: normal;
}

.emotion-32:focus {
  outline: none;
}

.emotion-32>span {
  padding: 1px 0;
}

.emotion-32:hover,
.emotion-32:focus {
  color: #FFFFFF;
  background-color: #003764;
  border-color: #003764;
}

.emotion-32:active {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  color: #FFFFFF;
  background-color: #000000;
  border-color: #000000;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-family-font1),sans-serif;"
    >
      <div
        class="emotion-0"
      >
        <section
          class="emotion-1"
        >
          <div
            class="emotion-2"
          >
            <nav
              class="emotion-3"
            >
              <div
                class="emotion-4"
              >
                <div
                  class="slick-slider slick-initialized"
                  dir="ltr"
                >
                  <button
                    aria-label="Previous"
                    class="slick-prev slick-arrow slick-prev slick-disabled"
                    data-role="none"
                    disabled=""
                  >
                    <span
                      aria-hidden="true"
                      class="emotion-5"
                    >
                      <svg
                        viewBox="0 0 13.29 8.07"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          d="M6.13 7.86a.73.73 0 001.04 0l5.92-5.92a.73.73 0 000-1.03l-.7-.7a.73.73 0 00-1.03 0l-4.71 4.7L1.94.2A.73.73 0 00.9.21l-.69.7a.73.73 0 000 1.03z"
                          fill="#003764"
                        />
                      </svg>
                    </span>
                  </button>
                  <div
                    class="slick-list"
                  >
                    <div
                      class="slick-track"
                      style="opacity: 1; transform: translate3d(0px, 0px, 0px);"
                    >
                      <div
                        aria-hidden="false"
                        class="slick-slide slick-active slick-current"
                        data-index="0"
                        style="outline: none; width: 0px;"
                        tabindex="-1"
                      >
                        <div>
                          <div
                            class="emotion-2"
                          >
                            <div
                              class="emotion-7"
                            >
                              <div
                                class="emotion-8"
                                height="200"
                                width="341"
                              >
                                <div
                                  class="emotion-9"
                                  data-testid="product-card-image"
                                >
                                  <img
                                    alt="Female red shirt"
                                    class="emotion-10"
                                    src="https://1puomodm9vwdc1sabswsbq9607.staging.bigcontent.io/i/oldnavy/210706_26-M1837_EarlyBTS_HP_Primary_CatNav03_US_XL?fmt=webp"
                                  />
                                </div>
                              </div>
                              <a
                                class="emotion-11"
                                href="https://page.not.found"
                                tabindex="-1"
                                target="_self"
                                title="page not found"
                              />
                            </div>
                          </div>
                        </div>
                      </div>
                      <div
                        aria-hidden="true"
                        class="slick-slide"
                        data-index="1"
                        style="outline: none; width: 0px;"
                        tabindex="-1"
                      >
                        <div>
                          <div
                            class="emotion-2"
                          >
                            <div
                              class="emotion-7"
                            >
                              <div
                                class="emotion-8"
                                height="200"
                                width="341"
                              >
                                <div
                                  class="emotion-9"
                                  data-testid="product-card-image"
                                >
                                  <img
                                    alt="Man landed and wearing white shirt and jeans"
                                    class="emotion-10"
                                    src="https://pqm4o81ro4dn1332lt5a8eas8.staging.bigcontent.io/i/gap/tshirt_mobile?fmt=webp"
                                  />
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                      <div
                        aria-hidden="true"
                        class="slick-slide"
                        data-index="2"
                        style="outline: none; width: 0px;"
                        tabindex="-1"
                      >
                        <div>
                          <div
                            class="emotion-2"
                          >
                            <div
                              class="emotion-7"
                            >
                              <div
                                class="emotion-8"
                                height="200"
                                width="341"
                              >
                                <div
                                  class="emotion-9"
                                  data-testid="product-card-image"
                                >
                                  <img
                                    alt="Boy sitting and smiling"
                                    class="emotion-10"
                                    src="https://pqm4o81ro4dn1332lt5a8eas8.staging.bigcontent.io/i/gap/G32408_Boys_MOB_anim2?fmt=webp"
                                  />
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                      <div
                        aria-hidden="true"
                        class="slick-slide"
                        data-index="3"
                        style="outline: none; width: 0px;"
                        tabindex="-1"
                      >
                        <div>
                          <div
                            class="emotion-2"
                          >
                            <div
                              class="emotion-7"
                            >
                              <div
                                class="emotion-8"
                                height="200"
                                width="341"
                              >
                                <div
                                  class="emotion-9"
                                  data-testid="product-card-image"
                                >
                                  <img
                                    alt="Woman landing and wearing white shirt and black pants"
                                    class="emotion-10"
                                    src="https://1puomodm9vwdc1sabswsbq9607.staging.bigcontent.io/i/oldnavy/Test-AG-Men-Pants-4?fmt=webp"
                                  />
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                  <button
                    aria-label="Next"
                    class="slick-next slick-arrow slick-next"
                    data-role="none"
                  >
                    <span
                      aria-hidden="true"
                      class="emotion-5"
                    >
                      <svg
                        viewBox="0 0 13.29 8.07"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          d="M6.13 7.86a.73.73 0 001.04 0l5.92-5.92a.73.73 0 000-1.03l-.7-.7a.73.73 0 00-1.03 0l-4.71 4.7L1.94.2A.73.73 0 00.9.21l-.69.7a.73.73 0 000 1.03z"
                          fill="#003764"
                        />
                      </svg>
                    </span>
                  </button>
                </div>
              </div>
            </nav>
            <div
              class="emotion-28"
            >
              <div
                class="rteWrapperDivs emotion-29"
              >
                <div
                  class="emotion-30"
                >
                  <div>
                    <p
                      class="amp-cms--p"
                    >
                      <span
                        class="amp-cms--headline-6"
                      >
                        Lorem Ipsum Dolor
                      </span>
                    </p>
                    <p
                      class="amp-cms--p"
                    >
                      <span
                        class="amp-cms--body-1"
                      >
                        Sit amet, consectetur adipiscing
                      </span>
                    </p>
                  </div>
                </div>
              </div>
              <div
                class="ctaWrapperDivs emotion-31"
              >
                <a
                  class="emotion-32"
                  color="dark"
                  href="https://page.not.found"
                >
                  Buy now
                </a>
              </div>
            </div>
          </div>
        </section>
      </div>
    </div>
  </div>
</DocumentFragment>
`;

exports[`SubCategoryBannerVariableHeightCarousel should match snapshot 1`] = `
.emotion-0 {
  margin-left: auto;
  margin-right: auto;
  max-width: 1280px;
}

.emotion-2 {
  position: relative;
}

.emotion-3 {
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-3 .slick-list {
  overflow: hidden;
}

.emotion-3 .slick-track {
  width: -webkit-max-content!important;
  width: -moz-max-content!important;
  width: max-content!important;
}

.emotion-3 button.slick-next.slick-arrow.slick-next,
.emotion-3 button.slick-prev.slick-arrow.slick-prev {
  z-index: 2;
  top: min(15vw, 50%);
  height: 44px;
  width: 44px;
  background: #003764;
  opacity: 75%;
}

.emotion-3 button.slick-next.slick-arrow.slick-next>span,
.emotion-3 button.slick-prev.slick-arrow.slick-prev>span {
  margin: auto;
}

.emotion-3 button.slick-next.slick-arrow.slick-next svg,
.emotion-3 button.slick-prev.slick-arrow.slick-prev svg {
  margin: auto;
  height: 25px;
  width: 14px;
}

.emotion-3 button.slick-next.slick-arrow.slick-next span svg path,
.emotion-3 button.slick-prev.slick-arrow.slick-prev span svg path {
  fill: #ffffff;
}

.emotion-3 button.slick-next.slick-arrow.slick-next:hover,
.emotion-3 button.slick-prev.slick-arrow.slick-prev:hover {
  opacity: 100%;
}

.emotion-3 button.slick-next.slick-arrow.slick-next:focus,
.emotion-3 button.slick-prev.slick-arrow.slick-prev:focus {
  background: #003764;
}

.emotion-3 button.slick-next.slick-arrow.slick-next:focus-visible,
.emotion-3 button.slick-prev.slick-arrow.slick-prev:focus-visible {
  outline: auto;
}

.emotion-3 .slick-slide {
  margin: 0;
  width: auto;
}

.emotion-3 .slick-disabled {
  display: none!important;
}

.emotion-3 .slick-next {
  left: calc(100% - 44px);
}

.emotion-3 .slick-slide:first-of-type a>div:nth-of-type(2)>div>div:nth-of-type(2) {
  border-left: none;
}

.emotion-3 .slick-slide>div:first-of-type {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-3 .slick-slide>div:first-of-type>div:first-of-type {
  width: 100%;
}

.emotion-4 {
  position: relative;
}

.emotion-4 .slick-slider {
  position: relative;
  display: block;
  box-sizing: border-box;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  -webkit-touch-callout: none;
  touch-action: pan-y;
  -webkit-tap-highlight-color: transparent;
  scroll-behavior: smooth;
}

.emotion-4 .slick-slider .slick-track,
.emotion-4 .slick-slider .slick-list {
  -webkit-transform: translate3d(0, 0, 0);
  -moz-transform: translate3d(0, 0, 0);
  -ms-transform: translate3d(0, 0, 0);
  transform: translate3d(0, 0, 0);
}

.emotion-4 .slick-list {
  position: relative;
  display: block;
  overflow-x: hidden;
  overflow-y: hidden;
  margin: 0;
  padding: 0;
}

.emotion-4 .slick-list:focus {
  outline: none;
}

.emotion-4 .slick-list.dragging {
  cursor: pointer;
  cursor: hand;
}

.emotion-4 .slick-track {
  position: relative;
  top: 0;
  left: 0;
  display: block;
  margin-left: auto;
  margin-right: auto;
}

.emotion-4 .slick-track:before,
.emotion-4 .slick-track:after {
  display: table;
  content: "";
}

.emotion-4 .slick-track:after {
  clear: both;
}

.emotion-4 .slick-slide {
  display: none;
  float: left;
  height: 100%;
  min-height: 1px;
}

.emotion-4 .slick-slide img {
  display: block;
}

.emotion-4 .slick-slide.slick-loading img {
  display: none;
}

.emotion-4 .slick-slide.dragging img {
  pointer-events: none;
}

.emotion-4 [dir="rtl"] .slick-slide {
  float: right;
}

.emotion-4 .slick-initialized .slick-slide,
.emotion-4 .slick-vertical .slick-slide {
  display: block;
}

.emotion-4 .slick-vertical .slick-slide {
  border: 1px solid transparent;
  height: auto;
}

.emotion-4 .slick-loading .slick-track,
.emotion-4 .slick-loading .slick-slide {
  visibility: hidden;
}

.emotion-4 .slick-loading .slick-list {
  background: #fff url("./ajax-loader.gif") center center no-repeat;
}

.emotion-4 .slick-arrow.slick-hidden {
  display: none;
}

.emotion-4 .slick-prev,
.emotion-4 .slick-next {
  font-size: 0;
  line-height: 0;
  position: absolute;
  top: 50%;
  display: block;
  padding: 0;
  cursor: pointer;
  color: transparent;
  border: none;
  outline: none;
  background: transparent;
}

.emotion-4 .slick-prev:hover,
.emotion-4 .slick-next:hover,
.emotion-4 .slick-prev:focus,
.emotion-4 .slick-next:focus {
  color: transparent;
  outline: none;
  background: transparent;
  opacity: 1;
}

.emotion-4 .slick-prev.slick-disabled,
.emotion-4 .slick-next.slick-disabled {
  opacity: 0.25;
}

.emotion-4 .slick-prev {
  left: -0;
  -webkit-transform: translate(0, -50%) rotate(90deg);
  -moz-transform: translate(0, -50%) rotate(90deg);
  -ms-transform: translate(0, -50%) rotate(90deg);
  transform: translate(0, -50%) rotate(90deg);
}

.emotion-4 .slick-prev span {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-4 [dir="rtl"] .slick-prev {
  right: -0;
  left: auto;
}

.emotion-4 .slick-next {
  right: -0;
  -webkit-transform: translate(0, -50%) rotate(-90deg);
  -moz-transform: translate(0, -50%) rotate(-90deg);
  -ms-transform: translate(0, -50%) rotate(-90deg);
  transform: translate(0, -50%) rotate(-90deg);
}

.emotion-4 .slick-next span {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-4 [dir="rtl"] .slick-next {
  right: auto;
  left: -0;
}

.emotion-4 .slick-dotted.slick-slider {
  margin-bottom: 30px;
}

.emotion-4 .slick-dots {
  position: absolute;
  bottom: -25px;
  display: block;
  width: 100%;
  padding: 0;
  margin: 0;
  list-style: none;
  text-align: center;
}

.emotion-4 .slick-dots li {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: 20px;
  height: 20px;
  margin: 0 5px;
  padding: 0;
  cursor: pointer;
}

.emotion-4 .slick-dots li.slick-active button:before {
  opacity: 0.75;
  color: black;
}

.emotion-4 .slick-dots li button {
  font-size: 0;
  line-height: 0;
  display: block;
  padding: 5px;
  cursor: pointer;
  color: transparent;
  border: 0;
  outline: none;
  background: transparent;
}

.emotion-4 .slick-dots li button:hover,
.emotion-4 .slick-dots li button:focus {
  outline: none;
}

.emotion-4 .slick-dots li button:hover:before,
.emotion-4 .slick-dots li button:focus:before,
.emotion-4 .slick-dots li button:hover:before,
.emotion-4 .slick-dots li button:focus:before {
  opacity: 1;
}

.emotion-4 .slick-dots li button:before {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 5px;
  height: 5px;
  content: "";
  opacity: 0.25;
  background-color: black;
  border-radius: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-5 {
  display: inline-block;
  height: 2rem;
  width: 2rem;
  min-height: 2rem;
  min-width: 2rem;
}

.emotion-5 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-7 {
  max-height: 300px;
  overflow: hidden;
}

.emotion-8 {
  overflow: hidden;
  position: relative;
  box-sizing: border-box;
  aspect-ratio: 1000/300;
}

.emotion-9 {
  -webkit-background-size: cover;
  background-size: cover;
  aspect-ratio: 1000/300;
  overflow: hidden;
}

.emotion-10 {
  width: 100%;
  aspect-ratio: 1000/300;
  object-fit: cover;
}

.emotion-11 {
  width: 100%;
  height: 100%;
  position: absolute;
  left: 0;
  top: 0;
  z-index: 1;
}

.emotion-12 {
  height: 100%;
  width: 100%;
  position: absolute;
  top: 0;
  left: 0;
  position: absolute;
  height: 100%;
}

.emotion-13 {
  width: 100%;
  height: 100%;
  padding: 50px 60px;
  box-sizing: border-box;
  position: absolute;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  display: grid;
  grid-template-columns: repeat(3, minmax(0,max-content));
  -webkit-align-content: center;
  -ms-flex-line-pack: center;
  align-content: center;
  grid-template-rows: 1fr max-content 1fr;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  justify-content: space-between;
  row-gap: 18px;
}

.emotion-13 .rteWrapperDivs {
  grid-column: 2;
  grid-row: 2;
}

.emotion-13 .ctaWrapperDivs {
  grid-column: 2;
  grid-row: 3;
  text-align: center;
}

.emotion-14 {
  box-sizing: content-box;
  text-align: center;
}

.emotion-15 {
  text-align: center;
}

.emotion-15 .amp-cms--p {
  padding: 0;
  margin: 0;
  line-height: 0;
}

.emotion-15 .amp-cms--p>span {
  display: inline;
  white-space: break-spaces;
}

.emotion-15 .amp-cms--p a {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  position: relative;
  z-index: 2;
  pointer-events: auto;
}

.emotion-15 .amp-cms--p a:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-15 .amp-cms--legal-copy {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 10px;
  line-height: 1.5;
  letter-spacing: 0;
}

.emotion-15 sup {
  vertical-align: top;
  display: inline-block;
  margin-top: -0.20ex;
}

.emotion-15 sub {
  vertical-align: bottom;
  display: inline-block;
  margin-bottom: -0.20ex;
}

.emotion-15 .amp-cms--f-0 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-15 .amp-cms--f-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-15 .amp-cms--f-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-15 .amp-cms--fn-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-15 .amp-cms--body-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 18px;
  line-height: 1.5;
  letter-spacing: 0.72px;
  font-weight: 500;
}

.emotion-15 .amp-cms--body-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 16px;
  line-height: 1.5;
  letter-spacing: 0.64px;
  font-weight: 500;
}

.emotion-15 .amp-cms--body-3 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 14px;
  line-height: 1.5;
  letter-spacing: 0.56px;
}

.emotion-15 .amp-cms--body-4 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.5;
  letter-spacing: 0;
}

.emotion-15 .amp-cms--body-5 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 10px;
  line-height: 1.5;
  letter-spacing: 0;
}

.emotion-15 .amp-cms--eyebrow-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 28px;
  line-height: 1;
  letter-spacing: 1.12px;
  font-weight: 500;
}

.emotion-15 .amp-cms--eyebrow-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 23px;
  line-height: 1;
  letter-spacing: 0.92px;
  font-weight: 500;
}

.emotion-15 .amp-cms--eyebrow-3 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 17px;
  line-height: 1;
  letter-spacing: 0.68px;
  font-weight: 500;
}

.emotion-15 .amp-cms--headline-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 118px;
  line-height: 1;
  letter-spacing: 0;
  font-weight: 700;
}

.emotion-15 .amp-cms--headline-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 80px;
  line-height: 1;
  letter-spacing: 0;
  font-weight: 700;
}

.emotion-15 .amp-cms--headline-3 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 72px;
  line-height: 1;
  letter-spacing: 0;
  font-weight: 700;
}

.emotion-15 .amp-cms--headline-4 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 64px;
  line-height: 1;
  letter-spacing: 1.28px;
  font-weight: 700;
}

.emotion-15 .amp-cms--headline-5 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 48px;
  line-height: 1;
  letter-spacing: 2.88px;
  font-weight: 700;
}

.emotion-15 .amp-cms--headline-6 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 40px;
  line-height: 1.1;
  letter-spacing: 2.4px;
  font-weight: 700;
}

.emotion-15 .amp-cms--headline-7 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 32px;
  line-height: 1;
  letter-spacing: 1.28px;
  font-weight: 700;
}

.emotion-15 .amp-cms--headlineAlt-1 {
  color: #003764;
  font-size: 114px;
  line-height: 1;
  letter-spacing: -0.4px;
  font-weight: 400;
}

.emotion-15 .amp-cms--headlineAlt-2 {
  color: #003764;
  font-size: 84px;
  line-height: 1;
  letter-spacing: -0.4px;
  font-weight: 400;
}

.emotion-15 .amp-cms--headlineAlt-3 {
  color: #003764;
  font-size: 64px;
  line-height: 1;
  letter-spacing: -0.4px;
  font-weight: 400;
}

.emotion-15 .amp-cms--headlineAlt-4 {
  color: #003764;
  font-size: 34px;
  line-height: 1;
  letter-spacing: -0.4px;
  font-weight: 400;
}

.emotion-15 .amp-cms--headlineAlt-5 {
  color: #003764;
  font-size: 16px;
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-15 .amp-cms--headlineAlt-6 {
  color: #003764;
  font-size: 16px;
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-15 .amp-cms--headlineAlt-7 {
  color: #003764;
  font-size: 16px;
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-15 .amp-cms--promo-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 100px;
  line-height: 1;
  letter-spacing: 0px;
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-15 .amp-cms--promo-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 80px;
  line-height: 1;
  letter-spacing: 0px;
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-15 .amp-cms--promoAlt-1 {
  color: #003764;
  font-size: 16px;
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 600;
}

.emotion-15 .amp-cms--subhead-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 24px;
  line-height: 1.5;
  letter-spacing: 0;
  font-weight: 500;
}

.emotion-15 .amp-cms--subhead-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 22px;
  line-height: 1.5;
  letter-spacing: 0;
  font-weight: 500;
}

.emotion-15 .amp-cms--subhead-3 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 20px;
  line-height: 1.5;
  letter-spacing: 0;
  font-weight: 500;
}

.emotion-16 {
  display: block;
  width: 100%;
  position: relative;
  z-index: 2;
  text-align: center;
}

.emotion-17 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 700;
  letter-spacing: 1.6800000000000002px;
  min-height: 48px;
  max-height: auto;
  line-height: 1.1428571428571428;
  padding: 14px 22px;
  width: auto;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  background-color: #FFFFFF;
  color: #003764;
  border-color: #003764;
  -webkit-align-self: flex-start;
  -ms-flex-item-align: flex-start;
  align-self: flex-start;
  white-space: normal;
}

.emotion-17:focus {
  outline: none;
}

.emotion-17>span {
  padding: 1px 0;
}

.emotion-17:hover,
.emotion-17:focus {
  color: #FFFFFF;
  background-color: #003764;
  border-color: #003764;
}

.emotion-17:active {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  color: #FFFFFF;
  background-color: #000000;
  border-color: #000000;
}

.emotion-24 {
  width: 100%;
  height: 100%;
  padding: 50px 60px;
  box-sizing: border-box;
  position: absolute;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  display: grid;
  grid-template-columns: repeat(3, minmax(0,max-content));
  -webkit-align-content: center;
  -ms-flex-line-pack: center;
  align-content: center;
  grid-template-rows: repeat(3, minmax(0, max-content));
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  justify-content: space-between;
  row-gap: 18px;
}

.emotion-24 .rteWrapperDivs {
  grid-column: 1;
  grid-row: 1;
}

.emotion-24 .ctaWrapperDivs {
  grid-column: 2;
  grid-row: 2;
  text-align: center;
}

.emotion-25 {
  box-sizing: content-box;
  text-align: start;
}

.emotion-33 {
  width: 100%;
  height: 100%;
  padding: 50px 60px;
  box-sizing: border-box;
  position: absolute;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  display: grid;
  grid-template-columns: repeat(3, minmax(0,max-content));
  -webkit-align-content: center;
  -ms-flex-line-pack: center;
  align-content: center;
  grid-template-rows: repeat(3, minmax(0, max-content));
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  justify-content: space-between;
  row-gap: 18px;
}

.emotion-33 .rteWrapperDivs {
  grid-column: 3;
  grid-row: 2;
}

.emotion-33 .ctaWrapperDivs {
  grid-column: 3;
  grid-row: 2;
  text-align: center;
}

.emotion-34 {
  display: block;
  width: 100%;
  position: relative;
  z-index: 2;
  text-align: right;
}

.emotion-35 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 700;
  letter-spacing: 1.05px;
  min-height: auto;
  max-height: auto;
  line-height: 1.8571428571428572;
  padding: 0;
  width: auto;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: left;
  border-left: none;
  border-right: none;
  border-radius: 0;
  background-color: transparent;
  border-bottom: 0;
  -webkit-box-pack: left;
  -ms-flex-pack: left;
  -webkit-justify-content: left;
  justify-content: left;
  color: #003764;
  height: auto;
  -webkit-text-decoration: underline;
  text-decoration: underline;
  text-underline-offset: 3.5px;
  text-decoration-thickness: 3px;
  -webkit-align-self: flex-start;
  -ms-flex-item-align: flex-start;
  align-self: flex-start;
  white-space: normal;
}

.emotion-35:focus {
  outline: none;
}

.emotion-35>span {
  padding: 1px 0;
}

.emotion-35:hover,
.emotion-35:focus {
  text-decoration-color: #003764;
}

.emotion-35:hover span,
.emotion-35:focus span {
  color: #FFFFFF;
  background-color: #003764;
}

.emotion-35:active {
  text-decoration-color: #000000;
}

.emotion-35:active>span {
  color: #FFFFFF;
  background-color: #000000;
}

.emotion-36 {
  box-sizing: border-box;
}

.emotion-43 {
  width: 100%;
  height: 100%;
  padding: 50px 60px;
  box-sizing: border-box;
  position: absolute;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  display: grid;
  grid-template-columns: repeat(3, minmax(0,max-content));
  -webkit-align-content: center;
  -ms-flex-line-pack: center;
  align-content: center;
  grid-template-rows: max-content max-content 1fr;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  justify-content: space-between;
  row-gap: 18px;
}

.emotion-43 .rteWrapperDivs {
  grid-column: 3;
  grid-row: 1;
}

.emotion-43 .ctaWrapperDivs {
  grid-column: 3;
  grid-row: 2;
  text-align: center;
}

.emotion-44 {
  box-sizing: content-box;
  text-align: end;
}

.emotion-47 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 700;
  letter-spacing: 1.6800000000000002px;
  min-height: 48px;
  max-height: auto;
  line-height: 1.1428571428571428;
  padding: 14px 22px;
  width: auto;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #003764;
  background: transparent;
  border-color: #003764;
  -webkit-align-self: flex-start;
  -ms-flex-item-align: flex-start;
  align-self: flex-start;
  white-space: normal;
}

.emotion-47:focus {
  outline: none;
}

.emotion-47>span {
  padding: 1px 0;
}

.emotion-47:hover,
.emotion-47:focus {
  color: #FFFFFF;
  background-color: #003764;
  border-color: #003764;
}

.emotion-47:active {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  color: #000000;
  background-color: transparent;
  border-color: #000000;
}

<div>
  <div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-family-font1),sans-serif;"
    >
      <div
        class="emotion-0"
      >
        <section
          class="emotion-1"
        >
          <div
            class="emotion-2"
          >
            <nav
              class="emotion-3"
            >
              <div
                class="emotion-4"
              >
                <div
                  class="slick-slider slick-initialized"
                  dir="ltr"
                >
                  <button
                    aria-label="Previous"
                    class="slick-prev slick-arrow slick-prev slick-disabled"
                    data-role="none"
                    disabled=""
                  >
                    <span
                      aria-hidden="true"
                      class="emotion-5"
                    >
                      <svg
                        viewBox="0 0 13.29 8.07"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          d="M6.13 7.86a.73.73 0 001.04 0l5.92-5.92a.73.73 0 000-1.03l-.7-.7a.73.73 0 00-1.03 0l-4.71 4.7L1.94.2A.73.73 0 00.9.21l-.69.7a.73.73 0 000 1.03z"
                          fill="#003764"
                        />
                      </svg>
                    </span>
                  </button>
                  <div
                    class="slick-list"
                  >
                    <div
                      class="slick-track"
                      style="opacity: 1; transform: translate3d(0px, 0px, 0px);"
                    >
                      <div
                        aria-hidden="false"
                        class="slick-slide slick-active slick-current"
                        data-index="0"
                        style="outline: none; width: 0px;"
                        tabindex="-1"
                      >
                        <div>
                          <div
                            class="emotion-2"
                          >
                            <div
                              class="emotion-7"
                            >
                              <div
                                class="emotion-8"
                                height="300"
                                width="1000"
                              >
                                <div
                                  class="emotion-9"
                                  data-testid="product-card-image"
                                >
                                  <img
                                    alt="Female red shirt"
                                    class="emotion-10"
                                    src="https://1puomodm9vwdc1sabswsbq9607.staging.bigcontent.io/i/oldnavy/210706_26-M1837_EarlyBTS_HP_Primary_CatNav03_US_XL?fmt=webp"
                                  />
                                </div>
                              </div>
                              <a
                                class="emotion-11"
                                href="https://page.not.found"
                                tabindex="-1"
                                target="_self"
                                title="page not found"
                              />
                            </div>
                            <div
                              class="emotion-12"
                            >
                              <div
                                class="emotion-13"
                              >
                                <div
                                  class="rteWrapperDivs emotion-14"
                                >
                                  <div
                                    class="emotion-15"
                                  >
                                    <div>
                                      <p
                                        class="amp-cms--p"
                                      >
                                        <span
                                          class="amp-cms--headline-6"
                                        >
                                          Lorem Ipsum Dolor
                                        </span>
                                      </p>
                                      <p
                                        class="amp-cms--p"
                                      >
                                        <span
                                          class="amp-cms--body-1"
                                        >
                                          Sit amet, consectetur adipiscing
                                        </span>
                                      </p>
                                    </div>
                                  </div>
                                </div>
                                <div
                                  class="ctaWrapperDivs emotion-16"
                                >
                                  <a
                                    class="emotion-17"
                                    color="dark"
                                    href="https://page.not.found"
                                  >
                                    Buy now
                                  </a>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                      <div
                        aria-hidden="true"
                        class="slick-slide"
                        data-index="1"
                        style="outline: none; width: 0px;"
                        tabindex="-1"
                      >
                        <div>
                          <div
                            class="emotion-2"
                          >
                            <div
                              class="emotion-7"
                            >
                              <div
                                class="emotion-8"
                                height="300"
                                width="1000"
                              >
                                <div
                                  class="emotion-9"
                                  data-testid="product-card-image"
                                >
                                  <img
                                    alt="Man landed and wearing white shirt and jeans"
                                    class="emotion-10"
                                    src="https://pqm4o81ro4dn1332lt5a8eas8.staging.bigcontent.io/i/gap/tshirt_mobile?fmt=webp"
                                  />
                                </div>
                              </div>
                            </div>
                            <div
                              class="emotion-12"
                            >
                              <div
                                class="emotion-24"
                              >
                                <div
                                  class="rteWrapperDivs emotion-25"
                                >
                                  <div
                                    class="emotion-15"
                                  >
                                    <div>
                                      <p
                                        class="amp-cms--p"
                                      >
                                        <span
                                          class="amp-cms--headline-6"
                                          style="text-transform:uppercase;font-style:italic"
                                        >
                                          Lorem Ipsum Dolor
                                        </span>
                                      </p>
                                      <p
                                        class="amp-cms--p"
                                      >
                                        <span
                                          class="amp-cms--body-1"
                                        >
                                          Sit amet, 
                                        </span>
                                        <span
                                          class="amp-cms--body-1"
                                          style="text-decoration:underline"
                                        >
                                          consectetur
                                        </span>
                                        <span
                                          class="amp-cms--body-1"
                                        >
                                           adipiscing
                                        </span>
                                      </p>
                                    </div>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                      <div
                        aria-hidden="true"
                        class="slick-slide"
                        data-index="2"
                        style="outline: none; width: 0px;"
                        tabindex="-1"
                      >
                        <div>
                          <div
                            class="emotion-2"
                          >
                            <div
                              class="emotion-7"
                            >
                              <div
                                class="emotion-8"
                                height="300"
                                width="1000"
                              >
                                <div
                                  class="emotion-9"
                                  data-testid="product-card-image"
                                >
                                  <img
                                    alt="Boy sitting and smiling"
                                    class="emotion-10"
                                    src="https://pqm4o81ro4dn1332lt5a8eas8.staging.bigcontent.io/i/gap/G32408_Boys_MOB_anim2?fmt=webp"
                                  />
                                </div>
                              </div>
                            </div>
                            <div
                              class="emotion-12"
                            >
                              <div
                                class="emotion-33"
                              >
                                <div
                                  class="ctaWrapperDivs emotion-34"
                                >
                                  <a
                                    class="emotion-35"
                                    color="dark"
                                    href="https://page.not.found"
                                    tabindex="-1"
                                  >
                                    <span
                                      class="emotion-36"
                                    >
                                      New Arrivals
                                    </span>
                                  </a>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                      <div
                        aria-hidden="true"
                        class="slick-slide"
                        data-index="3"
                        style="outline: none; width: 0px;"
                        tabindex="-1"
                      >
                        <div>
                          <div
                            class="emotion-2"
                          >
                            <div
                              class="emotion-7"
                            >
                              <div
                                class="emotion-8"
                                height="300"
                                width="1000"
                              >
                                <div
                                  class="emotion-9"
                                  data-testid="product-card-image"
                                >
                                  <img
                                    alt="Woman landing and wearing white shirt and black pants"
                                    class="emotion-10"
                                    src="https://1puomodm9vwdc1sabswsbq9607.staging.bigcontent.io/i/oldnavy/Test-AG-Men-Pants-4?fmt=webp"
                                  />
                                </div>
                              </div>
                            </div>
                            <div
                              class="emotion-12"
                            >
                              <div
                                class="emotion-43"
                              >
                                <div
                                  class="rteWrapperDivs emotion-44"
                                >
                                  <div
                                    class="emotion-15"
                                  >
                                    <div>
                                      <p
                                        class="amp-cms--p"
                                      >
                                        <span
                                          class="amp-cms--headline-6"
                                          style="text-transform:uppercase"
                                        >
                                          Lorem Ipsum Dolor
                                        </span>
                                      </p>
                                      <p
                                        class="amp-cms--p"
                                      >
                                        <span
                                          class="amp-cms--body-1"
                                          style="font-style:italic"
                                        >
                                          Sit amet, consectetur adipiscing
                                        </span>
                                      </p>
                                    </div>
                                  </div>
                                </div>
                                <div
                                  class="ctaWrapperDivs emotion-34"
                                >
                                  <a
                                    class="emotion-47"
                                    color="dark"
                                    href="https://page.not.found"
                                    tabindex="-1"
                                  >
                                    SHOP HERE
                                  </a>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                  <button
                    aria-label="Next"
                    class="slick-next slick-arrow slick-next"
                    data-role="none"
                  >
                    <span
                      aria-hidden="true"
                      class="emotion-5"
                    >
                      <svg
                        viewBox="0 0 13.29 8.07"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          d="M6.13 7.86a.73.73 0 001.04 0l5.92-5.92a.73.73 0 000-1.03l-.7-.7a.73.73 0 00-1.03 0l-4.71 4.7L1.94.2A.73.73 0 00.9.21l-.69.7a.73.73 0 000 1.03z"
                          fill="#003764"
                        />
                      </svg>
                    </span>
                  </button>
                </div>
              </div>
            </nav>
          </div>
        </section>
      </div>
    </div>
  </div>
</div>
`;

exports[`SubCategoryBannerVariableHeightCarousel should render persistent && below, play pause button on mobile 1`] = `
<DocumentFragment>
  .emotion-0 {
  margin-left: auto;
  margin-right: auto;
  max-width: 1280px;
}

.emotion-1 nav .slick-slider ul.slick-dots {
  bottom: -185px;
}

.emotion-2 {
  position: relative;
}

.emotion-3 {
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-3 .slick-list {
  overflow: hidden;
}

.emotion-3 .slick-track {
  width: -webkit-max-content!important;
  width: -moz-max-content!important;
  width: max-content!important;
}

.emotion-3 button.slick-next.slick-arrow.slick-next,
.emotion-3 button.slick-prev.slick-arrow.slick-prev {
  z-index: 2;
  top: min(25.32551319648094vw, 50%);
  height: 44px;
  width: 44px;
  background: #003764;
  opacity: 75%;
}

.emotion-3 button.slick-next.slick-arrow.slick-next>span,
.emotion-3 button.slick-prev.slick-arrow.slick-prev>span {
  margin: auto;
}

.emotion-3 button.slick-next.slick-arrow.slick-next svg,
.emotion-3 button.slick-prev.slick-arrow.slick-prev svg {
  margin: auto;
  height: 25px;
  width: 14px;
}

.emotion-3 button.slick-next.slick-arrow.slick-next span svg path,
.emotion-3 button.slick-prev.slick-arrow.slick-prev span svg path {
  fill: #ffffff;
}

.emotion-3 button.slick-next.slick-arrow.slick-next:hover,
.emotion-3 button.slick-prev.slick-arrow.slick-prev:hover {
  opacity: 100%;
}

.emotion-3 button.slick-next.slick-arrow.slick-next:focus,
.emotion-3 button.slick-prev.slick-arrow.slick-prev:focus {
  background: #003764;
}

.emotion-3 button.slick-next.slick-arrow.slick-next:focus-visible,
.emotion-3 button.slick-prev.slick-arrow.slick-prev:focus-visible {
  outline: auto;
}

.emotion-3 .slick-slide {
  margin: 0;
  width: auto;
}

.emotion-3 .slick-disabled {
  display: none!important;
}

.emotion-3 .slick-next {
  left: calc(100% - 44px);
}

.emotion-3 .slick-slide:first-of-type a>div:nth-of-type(2)>div>div:nth-of-type(2) {
  border-left: none;
}

.emotion-3 .slick-slide>div:first-of-type {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-3 .slick-slide>div:first-of-type>div:first-of-type {
  width: 100%;
}

.emotion-4 {
  position: relative;
}

.emotion-4 .slick-slider {
  position: relative;
  display: block;
  box-sizing: border-box;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  -webkit-touch-callout: none;
  touch-action: pan-y;
  -webkit-tap-highlight-color: transparent;
  scroll-behavior: smooth;
}

.emotion-4 .slick-slider .slick-track,
.emotion-4 .slick-slider .slick-list {
  -webkit-transform: translate3d(0, 0, 0);
  -moz-transform: translate3d(0, 0, 0);
  -ms-transform: translate3d(0, 0, 0);
  transform: translate3d(0, 0, 0);
}

.emotion-4 .slick-list {
  position: relative;
  display: block;
  overflow-x: hidden;
  overflow-y: hidden;
  margin: 0;
  padding: 0;
}

.emotion-4 .slick-list:focus {
  outline: none;
}

.emotion-4 .slick-list.dragging {
  cursor: pointer;
  cursor: hand;
}

.emotion-4 .slick-track {
  position: relative;
  top: 0;
  left: 0;
  display: block;
  margin-left: auto;
  margin-right: auto;
}

.emotion-4 .slick-track:before,
.emotion-4 .slick-track:after {
  display: table;
  content: "";
}

.emotion-4 .slick-track:after {
  clear: both;
}

.emotion-4 .slick-slide {
  display: none;
  float: left;
  height: 100%;
  min-height: 1px;
}

.emotion-4 .slick-slide img {
  display: block;
}

.emotion-4 .slick-slide.slick-loading img {
  display: none;
}

.emotion-4 .slick-slide.dragging img {
  pointer-events: none;
}

.emotion-4 [dir="rtl"] .slick-slide {
  float: right;
}

.emotion-4 .slick-initialized .slick-slide,
.emotion-4 .slick-vertical .slick-slide {
  display: block;
}

.emotion-4 .slick-vertical .slick-slide {
  border: 1px solid transparent;
  height: auto;
}

.emotion-4 .slick-loading .slick-track,
.emotion-4 .slick-loading .slick-slide {
  visibility: hidden;
}

.emotion-4 .slick-loading .slick-list {
  background: #fff url("./ajax-loader.gif") center center no-repeat;
}

.emotion-4 .slick-arrow.slick-hidden {
  display: none;
}

.emotion-4 .slick-prev,
.emotion-4 .slick-next {
  font-size: 0;
  line-height: 0;
  position: absolute;
  top: 50%;
  display: block;
  padding: 0;
  cursor: pointer;
  color: transparent;
  border: none;
  outline: none;
  background: transparent;
}

.emotion-4 .slick-prev:hover,
.emotion-4 .slick-next:hover,
.emotion-4 .slick-prev:focus,
.emotion-4 .slick-next:focus {
  color: transparent;
  outline: none;
  background: transparent;
  opacity: 1;
}

.emotion-4 .slick-prev.slick-disabled,
.emotion-4 .slick-next.slick-disabled {
  opacity: 0.25;
}

.emotion-4 .slick-prev {
  left: -0;
  -webkit-transform: translate(0, -50%) rotate(90deg);
  -moz-transform: translate(0, -50%) rotate(90deg);
  -ms-transform: translate(0, -50%) rotate(90deg);
  transform: translate(0, -50%) rotate(90deg);
}

.emotion-4 .slick-prev span {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-4 [dir="rtl"] .slick-prev {
  right: -0;
  left: auto;
}

.emotion-4 .slick-next {
  right: -0;
  -webkit-transform: translate(0, -50%) rotate(-90deg);
  -moz-transform: translate(0, -50%) rotate(-90deg);
  -ms-transform: translate(0, -50%) rotate(-90deg);
  transform: translate(0, -50%) rotate(-90deg);
}

.emotion-4 .slick-next span {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-4 [dir="rtl"] .slick-next {
  right: auto;
  left: -0;
}

.emotion-4 .slick-dotted.slick-slider {
  margin-bottom: 30px;
}

.emotion-4 .slick-dots {
  position: absolute;
  bottom: -25px;
  display: block;
  width: 100%;
  padding: 0;
  margin: 0;
  list-style: none;
  text-align: center;
}

.emotion-4 .slick-dots li {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: 20px;
  height: 20px;
  margin: 0 5px;
  padding: 0;
  cursor: pointer;
}

.emotion-4 .slick-dots li.slick-active button:before {
  opacity: 0.75;
  color: black;
}

.emotion-4 .slick-dots li button {
  font-size: 0;
  line-height: 0;
  display: block;
  padding: 5px;
  cursor: pointer;
  color: transparent;
  border: 0;
  outline: none;
  background: transparent;
}

.emotion-4 .slick-dots li button:hover,
.emotion-4 .slick-dots li button:focus {
  outline: none;
}

.emotion-4 .slick-dots li button:hover:before,
.emotion-4 .slick-dots li button:focus:before,
.emotion-4 .slick-dots li button:hover:before,
.emotion-4 .slick-dots li button:focus:before {
  opacity: 1;
}

.emotion-4 .slick-dots li button:before {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 5px;
  height: 5px;
  content: "";
  opacity: 0.25;
  background-color: black;
  border-radius: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-5 {
  position: absolute;
  border: none;
  padding: 0;
  z-index: 1;
  background: transparent;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  bottom: 0;
  top: inherit;
  height: 44px;
  width: 44px;
  left: 0;
}

.emotion-5>span {
  display: initial;
}

.emotion-6 {
  width: 44px;
  height: 44px;
}

.emotion-7 {
  display: inline-block;
  height: 44px;
  width: 44px;
  min-height: 44px;
  min-width: 44px;
}

.emotion-7 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-8 {
  display: inline-block;
  height: 2rem;
  width: 2rem;
  min-height: 2rem;
  min-width: 2rem;
}

.emotion-8 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-10 {
  max-height: 200px;
  overflow: hidden;
}

.emotion-11 {
  overflow: hidden;
  position: relative;
  box-sizing: border-box;
  aspect-ratio: 341/200;
}

.emotion-12 {
  -webkit-background-size: cover;
  background-size: cover;
  aspect-ratio: 341/200;
  overflow: hidden;
}

.emotion-13 {
  width: 100%;
  aspect-ratio: 341/200;
  object-fit: cover;
}

.emotion-14 {
  width: 100%;
  height: 100%;
  position: absolute;
  left: 0;
  top: 0;
  z-index: 1;
}

.emotion-31 {
  width: 100%;
  height: 100%;
  padding: 30px 16px;
  box-sizing: border-box;
  position: static;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  grid-template-columns: repeat(3, minmax(20%,max-content));
  -webkit-align-content: center;
  -ms-flex-line-pack: center;
  align-content: center;
  grid-template-rows: repeat(3, minmax(0, max-content));
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  row-gap: 18px;
}

.emotion-31 .rteWrapperDivs {
  grid-column: 2;
  grid-row: 2;
}

.emotion-31 .ctaWrapperDivs {
  grid-column: 2;
  grid-row: 2;
}

.emotion-32 {
  box-sizing: content-box;
  text-align: center;
}

.emotion-33 {
  text-align: center;
}

.emotion-33 .amp-cms--p {
  padding: 0;
  margin: 0;
  line-height: 0;
}

.emotion-33 .amp-cms--p>span {
  display: inline;
  white-space: break-spaces;
}

.emotion-33 .amp-cms--p a {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  position: relative;
  z-index: 2;
  pointer-events: auto;
}

.emotion-33 .amp-cms--p a:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-33 .amp-cms--legal-copy {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 10px;
  line-height: 1.5;
  letter-spacing: 0;
}

.emotion-33 sup {
  vertical-align: top;
  display: inline-block;
  margin-top: -0.20ex;
}

.emotion-33 sub {
  vertical-align: bottom;
  display: inline-block;
  margin-bottom: -0.20ex;
}

.emotion-33 .amp-cms--f-0 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 11px;
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-33 .amp-cms--f-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 11px;
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-33 .amp-cms--f-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 11px;
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-33 .amp-cms--fn-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 11px;
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-33 .amp-cms--body-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 14px;
  line-height: 1.5;
  letter-spacing: 0.336px;
  font-weight: 500;
}

.emotion-33 .amp-cms--body-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.5;
  letter-spacing: 0.288px;
  font-weight: 500;
}

.emotion-33 .amp-cms--body-3 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.5;
  letter-spacing: 0.288px;
}

.emotion-33 .amp-cms--body-4 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 10px;
  line-height: 1.5;
  letter-spacing: 0;
}

.emotion-33 .amp-cms--body-5 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 10px;
  line-height: 1.5;
  letter-spacing: 0;
}

.emotion-33 .amp-cms--eyebrow-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 23px;
  line-height: 1;
  letter-spacing: 0.92px;
  font-weight: 500;
}

.emotion-33 .amp-cms--eyebrow-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 19px;
  line-height: 1;
  letter-spacing: 0.76px;
  font-weight: 500;
}

.emotion-33 .amp-cms--eyebrow-3 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 14px;
  line-height: 1.0714285714285714;
  letter-spacing: 0.56px;
  font-weight: 500;
}

.emotion-33 .amp-cms--headline-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 68px;
  line-height: 1;
  letter-spacing: 0;
  font-weight: 700;
}

.emotion-33 .amp-cms--headline-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 50px;
  line-height: 1;
  letter-spacing: 0;
  font-weight: 700;
}

.emotion-33 .amp-cms--headline-3 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 43px;
  line-height: 1;
  letter-spacing: 0;
  font-weight: 700;
}

.emotion-33 .amp-cms--headline-4 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 36px;
  line-height: 1;
  letter-spacing: 0.72px;
  font-weight: 700;
}

.emotion-33 .amp-cms--headline-5 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 28px;
  line-height: 1;
  letter-spacing: 1.12px;
  font-weight: 700;
}

.emotion-33 .amp-cms--headline-6 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 24px;
  line-height: 1.0833333333333333;
  letter-spacing: 0.96px;
  font-weight: 700;
}

.emotion-33 .amp-cms--headline-7 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 20px;
  line-height: 1;
  letter-spacing: 0.48px;
  font-weight: 700;
}

.emotion-33 .amp-cms--headlineAlt-1 {
  color: #003764;
  font-size: 64px;
  line-height: 1;
  letter-spacing: -0.3px;
  font-weight: 400;
}

.emotion-33 .amp-cms--headlineAlt-2 {
  color: #003764;
  font-size: 54px;
  line-height: 1;
  letter-spacing: -0.3px;
  font-weight: 400;
}

.emotion-33 .amp-cms--headlineAlt-3 {
  color: #003764;
  font-size: 34px;
  line-height: 1;
  letter-spacing: -0.3px;
  font-weight: 400;
}

.emotion-33 .amp-cms--headlineAlt-4 {
  color: #003764;
  font-size: 24px;
  line-height: 1.4166666666666667;
  letter-spacing: -0.3px;
  font-weight: 400;
}

.emotion-33 .amp-cms--headlineAlt-5 {
  color: #003764;
  font-size: 14px;
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-33 .amp-cms--headlineAlt-6 {
  color: #003764;
  font-size: 14px;
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-33 .amp-cms--headlineAlt-7 {
  color: #003764;
  font-size: 14px;
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-33 .amp-cms--promo-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 60px;
  line-height: 1;
  letter-spacing: 0px;
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-33 .amp-cms--promo-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 40px;
  line-height: 1;
  letter-spacing: 0px;
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-33 .amp-cms--promoAlt-1 {
  color: #003764;
  font-size: 14px;
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 600;
}

.emotion-33 .amp-cms--subhead-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 20px;
  line-height: 1.5;
  letter-spacing: 0;
  font-weight: 500;
}

.emotion-33 .amp-cms--subhead-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 18px;
  line-height: 1.5;
  letter-spacing: 0;
  font-weight: 500;
}

.emotion-33 .amp-cms--subhead-3 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 16px;
  line-height: 1.5;
  letter-spacing: 0;
  font-weight: 500;
}

.emotion-34 {
  display: block;
  width: 100%;
  position: relative;
  z-index: 2;
  text-align: center;
}

.emotion-35 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 700;
  letter-spacing: 1.6800000000000002px;
  min-height: 44px;
  max-height: auto;
  line-height: 1.1428571428571428;
  padding: 12px 22px;
  width: auto;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  background-color: #FFFFFF;
  color: #003764;
  border-color: #003764;
  -webkit-align-self: flex-start;
  -ms-flex-item-align: flex-start;
  align-self: flex-start;
  white-space: normal;
}

.emotion-35:focus {
  outline: none;
}

.emotion-35>span {
  padding: 1px 0;
}

.emotion-35:hover,
.emotion-35:focus {
  color: #FFFFFF;
  background-color: #003764;
  border-color: #003764;
}

.emotion-35:active {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  color: #FFFFFF;
  background-color: #000000;
  border-color: #000000;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-family-font1),sans-serif;"
    >
      <div
        class="emotion-0"
      >
        <section
          class="emotion-1"
        >
          <div
            class="emotion-2"
          >
            <nav
              class="emotion-3"
            >
              <div
                class="emotion-4"
              >
                <button
                  aria-label="pause"
                  class="emotion-5"
                >
                  <div
                    class="emotion-6"
                  >
                    <span
                      aria-hidden="true"
                      class="emotion-7"
                    />
                  </div>
                </button>
                <div
                  class="slick-slider slick-initialized"
                  dir="ltr"
                >
                  <button
                    aria-label="Previous"
                    class="slick-prev slick-arrow slick-prev slick-disabled"
                    data-role="none"
                    disabled=""
                  >
                    <span
                      aria-hidden="true"
                      class="emotion-8"
                    >
                      <svg
                        viewBox="0 0 13.29 8.07"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          d="M6.13 7.86a.73.73 0 001.04 0l5.92-5.92a.73.73 0 000-1.03l-.7-.7a.73.73 0 00-1.03 0l-4.71 4.7L1.94.2A.73.73 0 00.9.21l-.69.7a.73.73 0 000 1.03z"
                          fill="#003764"
                        />
                      </svg>
                    </span>
                  </button>
                  <div
                    class="slick-list"
                  >
                    <div
                      class="slick-track"
                      style="opacity: 1; transform: translate3d(0px, 0px, 0px);"
                    >
                      <div
                        aria-hidden="false"
                        class="slick-slide slick-active slick-current"
                        data-index="0"
                        style="outline: none; width: 0px;"
                        tabindex="-1"
                      >
                        <div>
                          <div
                            class="emotion-2"
                          >
                            <div
                              class="emotion-10"
                            >
                              <div
                                class="emotion-11"
                                height="200"
                                width="341"
                              >
                                <div
                                  class="emotion-12"
                                  data-testid="product-card-image"
                                >
                                  <img
                                    alt="Female red shirt"
                                    class="emotion-13"
                                    src="https://1puomodm9vwdc1sabswsbq9607.staging.bigcontent.io/i/oldnavy/210706_26-M1837_EarlyBTS_HP_Primary_CatNav03_US_XL?fmt=webp"
                                  />
                                </div>
                              </div>
                              <a
                                class="emotion-14"
                                href="https://page.not.found"
                                tabindex="-1"
                                target="_self"
                                title="page not found"
                              />
                            </div>
                          </div>
                        </div>
                      </div>
                      <div
                        aria-hidden="true"
                        class="slick-slide"
                        data-index="1"
                        style="outline: none; width: 0px;"
                        tabindex="-1"
                      >
                        <div>
                          <div
                            class="emotion-2"
                          >
                            <div
                              class="emotion-10"
                            >
                              <div
                                class="emotion-11"
                                height="200"
                                width="341"
                              >
                                <div
                                  class="emotion-12"
                                  data-testid="product-card-image"
                                >
                                  <img
                                    alt="Man landed and wearing white shirt and jeans"
                                    class="emotion-13"
                                    src="https://pqm4o81ro4dn1332lt5a8eas8.staging.bigcontent.io/i/gap/tshirt_mobile?fmt=webp"
                                  />
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                      <div
                        aria-hidden="true"
                        class="slick-slide"
                        data-index="2"
                        style="outline: none; width: 0px;"
                        tabindex="-1"
                      >
                        <div>
                          <div
                            class="emotion-2"
                          >
                            <div
                              class="emotion-10"
                            >
                              <div
                                class="emotion-11"
                                height="200"
                                width="341"
                              >
                                <div
                                  class="emotion-12"
                                  data-testid="product-card-image"
                                >
                                  <img
                                    alt="Boy sitting and smiling"
                                    class="emotion-13"
                                    src="https://pqm4o81ro4dn1332lt5a8eas8.staging.bigcontent.io/i/gap/G32408_Boys_MOB_anim2?fmt=webp"
                                  />
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                      <div
                        aria-hidden="true"
                        class="slick-slide"
                        data-index="3"
                        style="outline: none; width: 0px;"
                        tabindex="-1"
                      >
                        <div>
                          <div
                            class="emotion-2"
                          >
                            <div
                              class="emotion-10"
                            >
                              <div
                                class="emotion-11"
                                height="200"
                                width="341"
                              >
                                <div
                                  class="emotion-12"
                                  data-testid="product-card-image"
                                >
                                  <img
                                    alt="Woman landing and wearing white shirt and black pants"
                                    class="emotion-13"
                                    src="https://1puomodm9vwdc1sabswsbq9607.staging.bigcontent.io/i/oldnavy/Test-AG-Men-Pants-4?fmt=webp"
                                  />
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                  <button
                    aria-label="Next"
                    class="slick-next slick-arrow slick-next"
                    data-role="none"
                  >
                    <span
                      aria-hidden="true"
                      class="emotion-8"
                    >
                      <svg
                        viewBox="0 0 13.29 8.07"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          d="M6.13 7.86a.73.73 0 001.04 0l5.92-5.92a.73.73 0 000-1.03l-.7-.7a.73.73 0 00-1.03 0l-4.71 4.7L1.94.2A.73.73 0 00.9.21l-.69.7a.73.73 0 000 1.03z"
                          fill="#003764"
                        />
                      </svg>
                    </span>
                  </button>
                </div>
              </div>
            </nav>
            <div
              class="emotion-31"
            >
              <div
                class="rteWrapperDivs emotion-32"
              >
                <div
                  class="emotion-33"
                >
                  <div>
                    <p
                      class="amp-cms--p"
                    >
                      <span
                        class="amp-cms--headline-6"
                      >
                        Lorem Ipsum Dolor
                      </span>
                    </p>
                    <p
                      class="amp-cms--p"
                    >
                      <span
                        class="amp-cms--body-1"
                      >
                        Sit amet, consectetur adipiscing
                      </span>
                    </p>
                  </div>
                </div>
              </div>
              <div
                class="ctaWrapperDivs emotion-34"
              >
                <a
                  class="emotion-35"
                  color="dark"
                  href="https://page.not.found"
                >
                  Buy now
                </a>
              </div>
            </div>
          </div>
        </section>
      </div>
    </div>
  </div>
</DocumentFragment>
`;

exports[`SubCategoryBannerVariableHeightCarousel should render persistent on mobile below 1`] = `
<DocumentFragment>
  .emotion-0 {
  margin-left: auto;
  margin-right: auto;
  max-width: 1280px;
}

.emotion-1 nav .slick-slider ul.slick-dots {
  bottom: -185px;
}

.emotion-2 {
  position: relative;
}

.emotion-3 {
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-3 .slick-list {
  overflow: hidden;
}

.emotion-3 .slick-track {
  width: -webkit-max-content!important;
  width: -moz-max-content!important;
  width: max-content!important;
}

.emotion-3 button.slick-next.slick-arrow.slick-next,
.emotion-3 button.slick-prev.slick-arrow.slick-prev {
  z-index: 2;
  top: min(25.32551319648094vw, 50%);
  height: 44px;
  width: 44px;
  background: #003764;
  opacity: 75%;
}

.emotion-3 button.slick-next.slick-arrow.slick-next>span,
.emotion-3 button.slick-prev.slick-arrow.slick-prev>span {
  margin: auto;
}

.emotion-3 button.slick-next.slick-arrow.slick-next svg,
.emotion-3 button.slick-prev.slick-arrow.slick-prev svg {
  margin: auto;
  height: 25px;
  width: 14px;
}

.emotion-3 button.slick-next.slick-arrow.slick-next span svg path,
.emotion-3 button.slick-prev.slick-arrow.slick-prev span svg path {
  fill: #ffffff;
}

.emotion-3 button.slick-next.slick-arrow.slick-next:hover,
.emotion-3 button.slick-prev.slick-arrow.slick-prev:hover {
  opacity: 100%;
}

.emotion-3 button.slick-next.slick-arrow.slick-next:focus,
.emotion-3 button.slick-prev.slick-arrow.slick-prev:focus {
  background: #003764;
}

.emotion-3 button.slick-next.slick-arrow.slick-next:focus-visible,
.emotion-3 button.slick-prev.slick-arrow.slick-prev:focus-visible {
  outline: auto;
}

.emotion-3 .slick-slide {
  margin: 0;
  width: auto;
}

.emotion-3 .slick-disabled {
  display: none!important;
}

.emotion-3 .slick-next {
  left: calc(100% - 44px);
}

.emotion-3 .slick-slide:first-of-type a>div:nth-of-type(2)>div>div:nth-of-type(2) {
  border-left: none;
}

.emotion-3 .slick-slide>div:first-of-type {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-3 .slick-slide>div:first-of-type>div:first-of-type {
  width: 100%;
}

.emotion-4 {
  position: relative;
}

.emotion-4 .slick-slider {
  position: relative;
  display: block;
  box-sizing: border-box;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  -webkit-touch-callout: none;
  touch-action: pan-y;
  -webkit-tap-highlight-color: transparent;
  scroll-behavior: smooth;
}

.emotion-4 .slick-slider .slick-track,
.emotion-4 .slick-slider .slick-list {
  -webkit-transform: translate3d(0, 0, 0);
  -moz-transform: translate3d(0, 0, 0);
  -ms-transform: translate3d(0, 0, 0);
  transform: translate3d(0, 0, 0);
}

.emotion-4 .slick-list {
  position: relative;
  display: block;
  overflow-x: hidden;
  overflow-y: hidden;
  margin: 0;
  padding: 0;
}

.emotion-4 .slick-list:focus {
  outline: none;
}

.emotion-4 .slick-list.dragging {
  cursor: pointer;
  cursor: hand;
}

.emotion-4 .slick-track {
  position: relative;
  top: 0;
  left: 0;
  display: block;
  margin-left: auto;
  margin-right: auto;
}

.emotion-4 .slick-track:before,
.emotion-4 .slick-track:after {
  display: table;
  content: "";
}

.emotion-4 .slick-track:after {
  clear: both;
}

.emotion-4 .slick-slide {
  display: none;
  float: left;
  height: 100%;
  min-height: 1px;
}

.emotion-4 .slick-slide img {
  display: block;
}

.emotion-4 .slick-slide.slick-loading img {
  display: none;
}

.emotion-4 .slick-slide.dragging img {
  pointer-events: none;
}

.emotion-4 [dir="rtl"] .slick-slide {
  float: right;
}

.emotion-4 .slick-initialized .slick-slide,
.emotion-4 .slick-vertical .slick-slide {
  display: block;
}

.emotion-4 .slick-vertical .slick-slide {
  border: 1px solid transparent;
  height: auto;
}

.emotion-4 .slick-loading .slick-track,
.emotion-4 .slick-loading .slick-slide {
  visibility: hidden;
}

.emotion-4 .slick-loading .slick-list {
  background: #fff url("./ajax-loader.gif") center center no-repeat;
}

.emotion-4 .slick-arrow.slick-hidden {
  display: none;
}

.emotion-4 .slick-prev,
.emotion-4 .slick-next {
  font-size: 0;
  line-height: 0;
  position: absolute;
  top: 50%;
  display: block;
  padding: 0;
  cursor: pointer;
  color: transparent;
  border: none;
  outline: none;
  background: transparent;
}

.emotion-4 .slick-prev:hover,
.emotion-4 .slick-next:hover,
.emotion-4 .slick-prev:focus,
.emotion-4 .slick-next:focus {
  color: transparent;
  outline: none;
  background: transparent;
  opacity: 1;
}

.emotion-4 .slick-prev.slick-disabled,
.emotion-4 .slick-next.slick-disabled {
  opacity: 0.25;
}

.emotion-4 .slick-prev {
  left: -0;
  -webkit-transform: translate(0, -50%) rotate(90deg);
  -moz-transform: translate(0, -50%) rotate(90deg);
  -ms-transform: translate(0, -50%) rotate(90deg);
  transform: translate(0, -50%) rotate(90deg);
}

.emotion-4 .slick-prev span {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-4 [dir="rtl"] .slick-prev {
  right: -0;
  left: auto;
}

.emotion-4 .slick-next {
  right: -0;
  -webkit-transform: translate(0, -50%) rotate(-90deg);
  -moz-transform: translate(0, -50%) rotate(-90deg);
  -ms-transform: translate(0, -50%) rotate(-90deg);
  transform: translate(0, -50%) rotate(-90deg);
}

.emotion-4 .slick-next span {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-4 [dir="rtl"] .slick-next {
  right: auto;
  left: -0;
}

.emotion-4 .slick-dotted.slick-slider {
  margin-bottom: 30px;
}

.emotion-4 .slick-dots {
  position: absolute;
  bottom: -25px;
  display: block;
  width: 100%;
  padding: 0;
  margin: 0;
  list-style: none;
  text-align: center;
}

.emotion-4 .slick-dots li {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: 20px;
  height: 20px;
  margin: 0 5px;
  padding: 0;
  cursor: pointer;
}

.emotion-4 .slick-dots li.slick-active button:before {
  opacity: 0.75;
  color: black;
}

.emotion-4 .slick-dots li button {
  font-size: 0;
  line-height: 0;
  display: block;
  padding: 5px;
  cursor: pointer;
  color: transparent;
  border: 0;
  outline: none;
  background: transparent;
}

.emotion-4 .slick-dots li button:hover,
.emotion-4 .slick-dots li button:focus {
  outline: none;
}

.emotion-4 .slick-dots li button:hover:before,
.emotion-4 .slick-dots li button:focus:before,
.emotion-4 .slick-dots li button:hover:before,
.emotion-4 .slick-dots li button:focus:before {
  opacity: 1;
}

.emotion-4 .slick-dots li button:before {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 5px;
  height: 5px;
  content: "";
  opacity: 0.25;
  background-color: black;
  border-radius: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-5 {
  display: inline-block;
  height: 2rem;
  width: 2rem;
  min-height: 2rem;
  min-width: 2rem;
}

.emotion-5 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-7 {
  max-height: 200px;
  overflow: hidden;
}

.emotion-8 {
  overflow: hidden;
  position: relative;
  box-sizing: border-box;
  aspect-ratio: 341/200;
}

.emotion-9 {
  -webkit-background-size: cover;
  background-size: cover;
  aspect-ratio: 341/200;
  overflow: hidden;
}

.emotion-10 {
  width: 100%;
  aspect-ratio: 341/200;
  object-fit: cover;
}

.emotion-11 {
  width: 100%;
  height: 100%;
  position: absolute;
  left: 0;
  top: 0;
  z-index: 1;
}

.emotion-28 {
  width: 100%;
  height: 100%;
  padding: 30px 16px;
  box-sizing: border-box;
  position: static;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  grid-template-columns: repeat(3, minmax(20%,max-content));
  -webkit-align-content: center;
  -ms-flex-line-pack: center;
  align-content: center;
  grid-template-rows: repeat(3, minmax(0, max-content));
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  row-gap: 18px;
}

.emotion-28 .rteWrapperDivs {
  grid-column: 2;
  grid-row: 2;
}

.emotion-28 .ctaWrapperDivs {
  grid-column: 2;
  grid-row: 2;
}

.emotion-29 {
  box-sizing: content-box;
  text-align: center;
}

.emotion-30 {
  text-align: center;
}

.emotion-30 .amp-cms--p {
  padding: 0;
  margin: 0;
  line-height: 0;
}

.emotion-30 .amp-cms--p>span {
  display: inline;
  white-space: break-spaces;
}

.emotion-30 .amp-cms--p a {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  position: relative;
  z-index: 2;
  pointer-events: auto;
}

.emotion-30 .amp-cms--p a:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-30 .amp-cms--legal-copy {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 10px;
  line-height: 1.5;
  letter-spacing: 0;
}

.emotion-30 sup {
  vertical-align: top;
  display: inline-block;
  margin-top: -0.20ex;
}

.emotion-30 sub {
  vertical-align: bottom;
  display: inline-block;
  margin-bottom: -0.20ex;
}

.emotion-30 .amp-cms--f-0 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 11px;
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-30 .amp-cms--f-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 11px;
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-30 .amp-cms--f-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 11px;
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-30 .amp-cms--fn-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 11px;
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-30 .amp-cms--body-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 14px;
  line-height: 1.5;
  letter-spacing: 0.336px;
  font-weight: 500;
}

.emotion-30 .amp-cms--body-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.5;
  letter-spacing: 0.288px;
  font-weight: 500;
}

.emotion-30 .amp-cms--body-3 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.5;
  letter-spacing: 0.288px;
}

.emotion-30 .amp-cms--body-4 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 10px;
  line-height: 1.5;
  letter-spacing: 0;
}

.emotion-30 .amp-cms--body-5 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 10px;
  line-height: 1.5;
  letter-spacing: 0;
}

.emotion-30 .amp-cms--eyebrow-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 23px;
  line-height: 1;
  letter-spacing: 0.92px;
  font-weight: 500;
}

.emotion-30 .amp-cms--eyebrow-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 19px;
  line-height: 1;
  letter-spacing: 0.76px;
  font-weight: 500;
}

.emotion-30 .amp-cms--eyebrow-3 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 14px;
  line-height: 1.0714285714285714;
  letter-spacing: 0.56px;
  font-weight: 500;
}

.emotion-30 .amp-cms--headline-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 68px;
  line-height: 1;
  letter-spacing: 0;
  font-weight: 700;
}

.emotion-30 .amp-cms--headline-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 50px;
  line-height: 1;
  letter-spacing: 0;
  font-weight: 700;
}

.emotion-30 .amp-cms--headline-3 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 43px;
  line-height: 1;
  letter-spacing: 0;
  font-weight: 700;
}

.emotion-30 .amp-cms--headline-4 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 36px;
  line-height: 1;
  letter-spacing: 0.72px;
  font-weight: 700;
}

.emotion-30 .amp-cms--headline-5 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 28px;
  line-height: 1;
  letter-spacing: 1.12px;
  font-weight: 700;
}

.emotion-30 .amp-cms--headline-6 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 24px;
  line-height: 1.0833333333333333;
  letter-spacing: 0.96px;
  font-weight: 700;
}

.emotion-30 .amp-cms--headline-7 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 20px;
  line-height: 1;
  letter-spacing: 0.48px;
  font-weight: 700;
}

.emotion-30 .amp-cms--headlineAlt-1 {
  color: #003764;
  font-size: 64px;
  line-height: 1;
  letter-spacing: -0.3px;
  font-weight: 400;
}

.emotion-30 .amp-cms--headlineAlt-2 {
  color: #003764;
  font-size: 54px;
  line-height: 1;
  letter-spacing: -0.3px;
  font-weight: 400;
}

.emotion-30 .amp-cms--headlineAlt-3 {
  color: #003764;
  font-size: 34px;
  line-height: 1;
  letter-spacing: -0.3px;
  font-weight: 400;
}

.emotion-30 .amp-cms--headlineAlt-4 {
  color: #003764;
  font-size: 24px;
  line-height: 1.4166666666666667;
  letter-spacing: -0.3px;
  font-weight: 400;
}

.emotion-30 .amp-cms--headlineAlt-5 {
  color: #003764;
  font-size: 14px;
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-30 .amp-cms--headlineAlt-6 {
  color: #003764;
  font-size: 14px;
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-30 .amp-cms--headlineAlt-7 {
  color: #003764;
  font-size: 14px;
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-30 .amp-cms--promo-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 60px;
  line-height: 1;
  letter-spacing: 0px;
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-30 .amp-cms--promo-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 40px;
  line-height: 1;
  letter-spacing: 0px;
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-30 .amp-cms--promoAlt-1 {
  color: #003764;
  font-size: 14px;
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 600;
}

.emotion-30 .amp-cms--subhead-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 20px;
  line-height: 1.5;
  letter-spacing: 0;
  font-weight: 500;
}

.emotion-30 .amp-cms--subhead-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 18px;
  line-height: 1.5;
  letter-spacing: 0;
  font-weight: 500;
}

.emotion-30 .amp-cms--subhead-3 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 16px;
  line-height: 1.5;
  letter-spacing: 0;
  font-weight: 500;
}

.emotion-31 {
  display: block;
  width: 100%;
  position: relative;
  z-index: 2;
  text-align: center;
}

.emotion-32 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 700;
  letter-spacing: 1.6800000000000002px;
  min-height: 44px;
  max-height: auto;
  line-height: 1.1428571428571428;
  padding: 12px 22px;
  width: auto;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  background-color: #FFFFFF;
  color: #003764;
  border-color: #003764;
  -webkit-align-self: flex-start;
  -ms-flex-item-align: flex-start;
  align-self: flex-start;
  white-space: normal;
}

.emotion-32:focus {
  outline: none;
}

.emotion-32>span {
  padding: 1px 0;
}

.emotion-32:hover,
.emotion-32:focus {
  color: #FFFFFF;
  background-color: #003764;
  border-color: #003764;
}

.emotion-32:active {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  color: #FFFFFF;
  background-color: #000000;
  border-color: #000000;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-family-font1),sans-serif;"
    >
      <div
        class="emotion-0"
      >
        <section
          class="emotion-1"
        >
          <div
            class="emotion-2"
          >
            <nav
              class="emotion-3"
            >
              <div
                class="emotion-4"
              >
                <div
                  class="slick-slider slick-initialized"
                  dir="ltr"
                >
                  <button
                    aria-label="Previous"
                    class="slick-prev slick-arrow slick-prev slick-disabled"
                    data-role="none"
                    disabled=""
                  >
                    <span
                      aria-hidden="true"
                      class="emotion-5"
                    >
                      <svg
                        viewBox="0 0 13.29 8.07"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          d="M6.13 7.86a.73.73 0 001.04 0l5.92-5.92a.73.73 0 000-1.03l-.7-.7a.73.73 0 00-1.03 0l-4.71 4.7L1.94.2A.73.73 0 00.9.21l-.69.7a.73.73 0 000 1.03z"
                          fill="#003764"
                        />
                      </svg>
                    </span>
                  </button>
                  <div
                    class="slick-list"
                  >
                    <div
                      class="slick-track"
                      style="opacity: 1; transform: translate3d(0px, 0px, 0px);"
                    >
                      <div
                        aria-hidden="false"
                        class="slick-slide slick-active slick-current"
                        data-index="0"
                        style="outline: none; width: 0px;"
                        tabindex="-1"
                      >
                        <div>
                          <div
                            class="emotion-2"
                          >
                            <div
                              class="emotion-7"
                            >
                              <div
                                class="emotion-8"
                                height="200"
                                width="341"
                              >
                                <div
                                  class="emotion-9"
                                  data-testid="product-card-image"
                                >
                                  <img
                                    alt="Female red shirt"
                                    class="emotion-10"
                                    src="https://1puomodm9vwdc1sabswsbq9607.staging.bigcontent.io/i/oldnavy/210706_26-M1837_EarlyBTS_HP_Primary_CatNav03_US_XL?fmt=webp"
                                  />
                                </div>
                              </div>
                              <a
                                class="emotion-11"
                                href="https://page.not.found"
                                tabindex="-1"
                                target="_self"
                                title="page not found"
                              />
                            </div>
                          </div>
                        </div>
                      </div>
                      <div
                        aria-hidden="true"
                        class="slick-slide"
                        data-index="1"
                        style="outline: none; width: 0px;"
                        tabindex="-1"
                      >
                        <div>
                          <div
                            class="emotion-2"
                          >
                            <div
                              class="emotion-7"
                            >
                              <div
                                class="emotion-8"
                                height="200"
                                width="341"
                              >
                                <div
                                  class="emotion-9"
                                  data-testid="product-card-image"
                                >
                                  <img
                                    alt="Man landed and wearing white shirt and jeans"
                                    class="emotion-10"
                                    src="https://pqm4o81ro4dn1332lt5a8eas8.staging.bigcontent.io/i/gap/tshirt_mobile?fmt=webp"
                                  />
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                      <div
                        aria-hidden="true"
                        class="slick-slide"
                        data-index="2"
                        style="outline: none; width: 0px;"
                        tabindex="-1"
                      >
                        <div>
                          <div
                            class="emotion-2"
                          >
                            <div
                              class="emotion-7"
                            >
                              <div
                                class="emotion-8"
                                height="200"
                                width="341"
                              >
                                <div
                                  class="emotion-9"
                                  data-testid="product-card-image"
                                >
                                  <img
                                    alt="Boy sitting and smiling"
                                    class="emotion-10"
                                    src="https://pqm4o81ro4dn1332lt5a8eas8.staging.bigcontent.io/i/gap/G32408_Boys_MOB_anim2?fmt=webp"
                                  />
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                      <div
                        aria-hidden="true"
                        class="slick-slide"
                        data-index="3"
                        style="outline: none; width: 0px;"
                        tabindex="-1"
                      >
                        <div>
                          <div
                            class="emotion-2"
                          >
                            <div
                              class="emotion-7"
                            >
                              <div
                                class="emotion-8"
                                height="200"
                                width="341"
                              >
                                <div
                                  class="emotion-9"
                                  data-testid="product-card-image"
                                >
                                  <img
                                    alt="Woman landing and wearing white shirt and black pants"
                                    class="emotion-10"
                                    src="https://1puomodm9vwdc1sabswsbq9607.staging.bigcontent.io/i/oldnavy/Test-AG-Men-Pants-4?fmt=webp"
                                  />
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                  <button
                    aria-label="Next"
                    class="slick-next slick-arrow slick-next"
                    data-role="none"
                  >
                    <span
                      aria-hidden="true"
                      class="emotion-5"
                    >
                      <svg
                        viewBox="0 0 13.29 8.07"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          d="M6.13 7.86a.73.73 0 001.04 0l5.92-5.92a.73.73 0 000-1.03l-.7-.7a.73.73 0 00-1.03 0l-4.71 4.7L1.94.2A.73.73 0 00.9.21l-.69.7a.73.73 0 000 1.03z"
                          fill="#003764"
                        />
                      </svg>
                    </span>
                  </button>
                </div>
              </div>
            </nav>
            <div
              class="emotion-28"
            >
              <div
                class="rteWrapperDivs emotion-29"
              >
                <div
                  class="emotion-30"
                >
                  <div>
                    <p
                      class="amp-cms--p"
                    >
                      <span
                        class="amp-cms--headline-6"
                      >
                        Lorem Ipsum Dolor
                      </span>
                    </p>
                    <p
                      class="amp-cms--p"
                    >
                      <span
                        class="amp-cms--body-1"
                      >
                        Sit amet, consectetur adipiscing
                      </span>
                    </p>
                  </div>
                </div>
              </div>
              <div
                class="ctaWrapperDivs emotion-31"
              >
                <a
                  class="emotion-32"
                  color="dark"
                  href="https://page.not.found"
                >
                  Buy now
                </a>
              </div>
            </div>
          </div>
        </section>
      </div>
    </div>
  </div>
</DocumentFragment>
`;

exports[`SubCategoryBannerVariableHeightCarousel should render unique && below, play pause button on mobile, size medium 1`] = `
<DocumentFragment>
  .emotion-0 {
  margin-left: auto;
  margin-right: auto;
  max-width: 1280px;
}

.emotion-1 nav .slick-slider ul.slick-dots {
  bottom: -185px;
}

.emotion-2 {
  position: relative;
}

.emotion-3 {
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-3 .slick-list {
  overflow: hidden;
}

.emotion-3 .slick-track {
  width: -webkit-max-content!important;
  width: -moz-max-content!important;
  width: max-content!important;
}

.emotion-3 button.slick-next.slick-arrow.slick-next,
.emotion-3 button.slick-prev.slick-arrow.slick-prev {
  z-index: 2;
  top: min(25.32551319648094vw, 50%);
  height: 44px;
  width: 44px;
  background: #003764;
  opacity: 75%;
}

.emotion-3 button.slick-next.slick-arrow.slick-next>span,
.emotion-3 button.slick-prev.slick-arrow.slick-prev>span {
  margin: auto;
}

.emotion-3 button.slick-next.slick-arrow.slick-next svg,
.emotion-3 button.slick-prev.slick-arrow.slick-prev svg {
  margin: auto;
  height: 25px;
  width: 14px;
}

.emotion-3 button.slick-next.slick-arrow.slick-next span svg path,
.emotion-3 button.slick-prev.slick-arrow.slick-prev span svg path {
  fill: #ffffff;
}

.emotion-3 button.slick-next.slick-arrow.slick-next:hover,
.emotion-3 button.slick-prev.slick-arrow.slick-prev:hover {
  opacity: 100%;
}

.emotion-3 button.slick-next.slick-arrow.slick-next:focus,
.emotion-3 button.slick-prev.slick-arrow.slick-prev:focus {
  background: #003764;
}

.emotion-3 button.slick-next.slick-arrow.slick-next:focus-visible,
.emotion-3 button.slick-prev.slick-arrow.slick-prev:focus-visible {
  outline: auto;
}

.emotion-3 .slick-slide {
  margin: 0;
  width: auto;
}

.emotion-3 .slick-disabled {
  display: none!important;
}

.emotion-3 .slick-next {
  left: calc(100% - 44px);
}

.emotion-3 .slick-slide:first-of-type a>div:nth-of-type(2)>div>div:nth-of-type(2) {
  border-left: none;
}

.emotion-3 .slick-slide>div:first-of-type {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-3 .slick-slide>div:first-of-type>div:first-of-type {
  width: 100%;
}

.emotion-4 {
  position: relative;
}

.emotion-4 .slick-slider {
  position: relative;
  display: block;
  box-sizing: border-box;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  -webkit-touch-callout: none;
  touch-action: pan-y;
  -webkit-tap-highlight-color: transparent;
  scroll-behavior: smooth;
}

.emotion-4 .slick-slider .slick-track,
.emotion-4 .slick-slider .slick-list {
  -webkit-transform: translate3d(0, 0, 0);
  -moz-transform: translate3d(0, 0, 0);
  -ms-transform: translate3d(0, 0, 0);
  transform: translate3d(0, 0, 0);
}

.emotion-4 .slick-list {
  position: relative;
  display: block;
  overflow-x: hidden;
  overflow-y: hidden;
  margin: 0;
  padding: 0;
}

.emotion-4 .slick-list:focus {
  outline: none;
}

.emotion-4 .slick-list.dragging {
  cursor: pointer;
  cursor: hand;
}

.emotion-4 .slick-track {
  position: relative;
  top: 0;
  left: 0;
  display: block;
  margin-left: auto;
  margin-right: auto;
}

.emotion-4 .slick-track:before,
.emotion-4 .slick-track:after {
  display: table;
  content: "";
}

.emotion-4 .slick-track:after {
  clear: both;
}

.emotion-4 .slick-slide {
  display: none;
  float: left;
  height: 100%;
  min-height: 1px;
}

.emotion-4 .slick-slide img {
  display: block;
}

.emotion-4 .slick-slide.slick-loading img {
  display: none;
}

.emotion-4 .slick-slide.dragging img {
  pointer-events: none;
}

.emotion-4 [dir="rtl"] .slick-slide {
  float: right;
}

.emotion-4 .slick-initialized .slick-slide,
.emotion-4 .slick-vertical .slick-slide {
  display: block;
}

.emotion-4 .slick-vertical .slick-slide {
  border: 1px solid transparent;
  height: auto;
}

.emotion-4 .slick-loading .slick-track,
.emotion-4 .slick-loading .slick-slide {
  visibility: hidden;
}

.emotion-4 .slick-loading .slick-list {
  background: #fff url("./ajax-loader.gif") center center no-repeat;
}

.emotion-4 .slick-arrow.slick-hidden {
  display: none;
}

.emotion-4 .slick-prev,
.emotion-4 .slick-next {
  font-size: 0;
  line-height: 0;
  position: absolute;
  top: 50%;
  display: block;
  padding: 0;
  cursor: pointer;
  color: transparent;
  border: none;
  outline: none;
  background: transparent;
}

.emotion-4 .slick-prev:hover,
.emotion-4 .slick-next:hover,
.emotion-4 .slick-prev:focus,
.emotion-4 .slick-next:focus {
  color: transparent;
  outline: none;
  background: transparent;
  opacity: 1;
}

.emotion-4 .slick-prev.slick-disabled,
.emotion-4 .slick-next.slick-disabled {
  opacity: 0.25;
}

.emotion-4 .slick-prev {
  left: -0;
  -webkit-transform: translate(0, -50%) rotate(90deg);
  -moz-transform: translate(0, -50%) rotate(90deg);
  -ms-transform: translate(0, -50%) rotate(90deg);
  transform: translate(0, -50%) rotate(90deg);
}

.emotion-4 .slick-prev span {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-4 [dir="rtl"] .slick-prev {
  right: -0;
  left: auto;
}

.emotion-4 .slick-next {
  right: -0;
  -webkit-transform: translate(0, -50%) rotate(-90deg);
  -moz-transform: translate(0, -50%) rotate(-90deg);
  -ms-transform: translate(0, -50%) rotate(-90deg);
  transform: translate(0, -50%) rotate(-90deg);
}

.emotion-4 .slick-next span {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-4 [dir="rtl"] .slick-next {
  right: auto;
  left: -0;
}

.emotion-4 .slick-dotted.slick-slider {
  margin-bottom: 30px;
}

.emotion-4 .slick-dots {
  position: absolute;
  bottom: -25px;
  display: block;
  width: 100%;
  padding: 0;
  margin: 0;
  list-style: none;
  text-align: center;
}

.emotion-4 .slick-dots li {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: 20px;
  height: 20px;
  margin: 0 5px;
  padding: 0;
  cursor: pointer;
}

.emotion-4 .slick-dots li.slick-active button:before {
  opacity: 0.75;
  color: black;
}

.emotion-4 .slick-dots li button {
  font-size: 0;
  line-height: 0;
  display: block;
  padding: 5px;
  cursor: pointer;
  color: transparent;
  border: 0;
  outline: none;
  background: transparent;
}

.emotion-4 .slick-dots li button:hover,
.emotion-4 .slick-dots li button:focus {
  outline: none;
}

.emotion-4 .slick-dots li button:hover:before,
.emotion-4 .slick-dots li button:focus:before,
.emotion-4 .slick-dots li button:hover:before,
.emotion-4 .slick-dots li button:focus:before {
  opacity: 1;
}

.emotion-4 .slick-dots li button:before {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 5px;
  height: 5px;
  content: "";
  opacity: 0.25;
  background-color: black;
  border-radius: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-5 {
  position: absolute;
  border: none;
  padding: 0;
  z-index: 1;
  background: transparent;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  bottom: inherit;
  top: min(41.6vw, 156px);
  height: 44px;
  width: 44px;
  left: 0;
}

.emotion-5>span {
  display: initial;
}

.emotion-6 {
  width: 44px;
  height: 44px;
}

.emotion-7 {
  display: inline-block;
  height: 44px;
  width: 44px;
  min-height: 44px;
  min-width: 44px;
}

.emotion-7 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-8 {
  display: inline-block;
  height: 2rem;
  width: 2rem;
  min-height: 2rem;
  min-width: 2rem;
}

.emotion-8 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-10 {
  max-height: 200px;
  overflow: hidden;
}

.emotion-11 {
  overflow: hidden;
  position: relative;
  box-sizing: border-box;
  aspect-ratio: 341/200;
}

.emotion-12 {
  -webkit-background-size: cover;
  background-size: cover;
  aspect-ratio: 341/200;
  overflow: hidden;
}

.emotion-13 {
  width: 100%;
  aspect-ratio: 341/200;
  object-fit: cover;
}

.emotion-14 {
  width: 100%;
  height: 100%;
  position: absolute;
  left: 0;
  top: 0;
  z-index: 1;
}

.emotion-15 {
  height: 100%;
  width: 100%;
  position: absolute;
  top: 0;
  left: 0;
  position: relative;
  height: auto;
}

.emotion-16 {
  width: 100%;
  height: 100%;
  padding: 30px 16px;
  box-sizing: border-box;
  position: static;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  grid-template-columns: repeat(3, minmax(0,max-content));
  -webkit-align-content: center;
  -ms-flex-line-pack: center;
  align-content: center;
  grid-template-rows: repeat(3, minmax(0, max-content));
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  row-gap: 18px;
}

.emotion-16 .rteWrapperDivs {
  grid-column: 2;
  grid-row: 2;
}

.emotion-16 .ctaWrapperDivs {
  grid-column: 2;
  grid-row: 2;
}

.emotion-17 {
  box-sizing: content-box;
  text-align: center;
}

.emotion-18 {
  text-align: center;
}

.emotion-18 .amp-cms--p {
  padding: 0;
  margin: 0;
  line-height: 0;
}

.emotion-18 .amp-cms--p>span {
  display: inline;
  white-space: break-spaces;
}

.emotion-18 .amp-cms--p a {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  position: relative;
  z-index: 2;
  pointer-events: auto;
}

.emotion-18 .amp-cms--p a:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-18 .amp-cms--legal-copy {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 10px;
  line-height: 1.5;
  letter-spacing: 0;
}

.emotion-18 sup {
  vertical-align: top;
  display: inline-block;
  margin-top: -0.20ex;
}

.emotion-18 sub {
  vertical-align: bottom;
  display: inline-block;
  margin-bottom: -0.20ex;
}

.emotion-18 .amp-cms--f-0 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 11px;
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-18 .amp-cms--f-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 11px;
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-18 .amp-cms--f-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 11px;
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-18 .amp-cms--fn-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 11px;
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-18 .amp-cms--body-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 14px;
  line-height: 1.5;
  letter-spacing: 0.336px;
  font-weight: 500;
}

.emotion-18 .amp-cms--body-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.5;
  letter-spacing: 0.288px;
  font-weight: 500;
}

.emotion-18 .amp-cms--body-3 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.5;
  letter-spacing: 0.288px;
}

.emotion-18 .amp-cms--body-4 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 10px;
  line-height: 1.5;
  letter-spacing: 0;
}

.emotion-18 .amp-cms--body-5 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 10px;
  line-height: 1.5;
  letter-spacing: 0;
}

.emotion-18 .amp-cms--eyebrow-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 23px;
  line-height: 1;
  letter-spacing: 0.92px;
  font-weight: 500;
}

.emotion-18 .amp-cms--eyebrow-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 19px;
  line-height: 1;
  letter-spacing: 0.76px;
  font-weight: 500;
}

.emotion-18 .amp-cms--eyebrow-3 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 14px;
  line-height: 1.0714285714285714;
  letter-spacing: 0.56px;
  font-weight: 500;
}

.emotion-18 .amp-cms--headline-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 68px;
  line-height: 1;
  letter-spacing: 0;
  font-weight: 700;
}

.emotion-18 .amp-cms--headline-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 50px;
  line-height: 1;
  letter-spacing: 0;
  font-weight: 700;
}

.emotion-18 .amp-cms--headline-3 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 43px;
  line-height: 1;
  letter-spacing: 0;
  font-weight: 700;
}

.emotion-18 .amp-cms--headline-4 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 36px;
  line-height: 1;
  letter-spacing: 0.72px;
  font-weight: 700;
}

.emotion-18 .amp-cms--headline-5 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 28px;
  line-height: 1;
  letter-spacing: 1.12px;
  font-weight: 700;
}

.emotion-18 .amp-cms--headline-6 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 24px;
  line-height: 1.0833333333333333;
  letter-spacing: 0.96px;
  font-weight: 700;
}

.emotion-18 .amp-cms--headline-7 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 20px;
  line-height: 1;
  letter-spacing: 0.48px;
  font-weight: 700;
}

.emotion-18 .amp-cms--headlineAlt-1 {
  color: #003764;
  font-size: 64px;
  line-height: 1;
  letter-spacing: -0.3px;
  font-weight: 400;
}

.emotion-18 .amp-cms--headlineAlt-2 {
  color: #003764;
  font-size: 54px;
  line-height: 1;
  letter-spacing: -0.3px;
  font-weight: 400;
}

.emotion-18 .amp-cms--headlineAlt-3 {
  color: #003764;
  font-size: 34px;
  line-height: 1;
  letter-spacing: -0.3px;
  font-weight: 400;
}

.emotion-18 .amp-cms--headlineAlt-4 {
  color: #003764;
  font-size: 24px;
  line-height: 1.4166666666666667;
  letter-spacing: -0.3px;
  font-weight: 400;
}

.emotion-18 .amp-cms--headlineAlt-5 {
  color: #003764;
  font-size: 14px;
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-18 .amp-cms--headlineAlt-6 {
  color: #003764;
  font-size: 14px;
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-18 .amp-cms--headlineAlt-7 {
  color: #003764;
  font-size: 14px;
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-18 .amp-cms--promo-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 60px;
  line-height: 1;
  letter-spacing: 0px;
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-18 .amp-cms--promo-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 40px;
  line-height: 1;
  letter-spacing: 0px;
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-18 .amp-cms--promoAlt-1 {
  color: #003764;
  font-size: 14px;
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 600;
}

.emotion-18 .amp-cms--subhead-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 20px;
  line-height: 1.5;
  letter-spacing: 0;
  font-weight: 500;
}

.emotion-18 .amp-cms--subhead-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 18px;
  line-height: 1.5;
  letter-spacing: 0;
  font-weight: 500;
}

.emotion-18 .amp-cms--subhead-3 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 16px;
  line-height: 1.5;
  letter-spacing: 0;
  font-weight: 500;
}

.emotion-19 {
  display: block;
  width: 100%;
  position: relative;
  z-index: 2;
  text-align: center;
}

.emotion-20 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 700;
  letter-spacing: 1.6800000000000002px;
  min-height: 44px;
  max-height: auto;
  line-height: 1.1428571428571428;
  padding: 12px 22px;
  width: auto;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  background-color: #FFFFFF;
  color: #003764;
  border-color: #003764;
  -webkit-align-self: flex-start;
  -ms-flex-item-align: flex-start;
  align-self: flex-start;
  white-space: normal;
}

.emotion-20:focus {
  outline: none;
}

.emotion-20>span {
  padding: 1px 0;
}

.emotion-20:hover,
.emotion-20:focus {
  color: #FFFFFF;
  background-color: #003764;
  border-color: #003764;
}

.emotion-20:active {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  color: #FFFFFF;
  background-color: #000000;
  border-color: #000000;
}

.emotion-27 {
  width: 100%;
  height: 100%;
  padding: 30px 16px;
  box-sizing: border-box;
  position: static;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  grid-template-columns: repeat(3, minmax(0,max-content));
  -webkit-align-content: center;
  -ms-flex-line-pack: center;
  align-content: center;
  grid-template-rows: repeat(3, minmax(0, max-content));
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  row-gap: 18px;
}

.emotion-27 .rteWrapperDivs {
  grid-column: 1;
  grid-row: 1;
}

.emotion-27 .ctaWrapperDivs {
  grid-column: 2;
  grid-row: 2;
}

.emotion-28 {
  box-sizing: content-box;
  text-align: left;
}

.emotion-29 {
  text-align: start;
}

.emotion-29 .amp-cms--p {
  padding: 0;
  margin: 0;
  line-height: 0;
}

.emotion-29 .amp-cms--p>span {
  display: inline;
  white-space: break-spaces;
}

.emotion-29 .amp-cms--p a {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  position: relative;
  z-index: 2;
  pointer-events: auto;
}

.emotion-29 .amp-cms--p a:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-29 .amp-cms--legal-copy {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 10px;
  line-height: 1.5;
  letter-spacing: 0;
}

.emotion-29 sup {
  vertical-align: top;
  display: inline-block;
  margin-top: -0.20ex;
}

.emotion-29 sub {
  vertical-align: bottom;
  display: inline-block;
  margin-bottom: -0.20ex;
}

.emotion-29 .amp-cms--f-0 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 11px;
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-29 .amp-cms--f-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 11px;
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-29 .amp-cms--f-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 11px;
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-29 .amp-cms--fn-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 11px;
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-29 .amp-cms--body-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 14px;
  line-height: 1.5;
  letter-spacing: 0.336px;
  font-weight: 500;
}

.emotion-29 .amp-cms--body-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.5;
  letter-spacing: 0.288px;
  font-weight: 500;
}

.emotion-29 .amp-cms--body-3 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.5;
  letter-spacing: 0.288px;
}

.emotion-29 .amp-cms--body-4 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 10px;
  line-height: 1.5;
  letter-spacing: 0;
}

.emotion-29 .amp-cms--body-5 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 10px;
  line-height: 1.5;
  letter-spacing: 0;
}

.emotion-29 .amp-cms--eyebrow-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 23px;
  line-height: 1;
  letter-spacing: 0.92px;
  font-weight: 500;
}

.emotion-29 .amp-cms--eyebrow-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 19px;
  line-height: 1;
  letter-spacing: 0.76px;
  font-weight: 500;
}

.emotion-29 .amp-cms--eyebrow-3 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 14px;
  line-height: 1.0714285714285714;
  letter-spacing: 0.56px;
  font-weight: 500;
}

.emotion-29 .amp-cms--headline-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 68px;
  line-height: 1;
  letter-spacing: 0;
  font-weight: 700;
}

.emotion-29 .amp-cms--headline-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 50px;
  line-height: 1;
  letter-spacing: 0;
  font-weight: 700;
}

.emotion-29 .amp-cms--headline-3 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 43px;
  line-height: 1;
  letter-spacing: 0;
  font-weight: 700;
}

.emotion-29 .amp-cms--headline-4 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 36px;
  line-height: 1;
  letter-spacing: 0.72px;
  font-weight: 700;
}

.emotion-29 .amp-cms--headline-5 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 28px;
  line-height: 1;
  letter-spacing: 1.12px;
  font-weight: 700;
}

.emotion-29 .amp-cms--headline-6 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 24px;
  line-height: 1.0833333333333333;
  letter-spacing: 0.96px;
  font-weight: 700;
}

.emotion-29 .amp-cms--headline-7 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 20px;
  line-height: 1;
  letter-spacing: 0.48px;
  font-weight: 700;
}

.emotion-29 .amp-cms--headlineAlt-1 {
  color: #003764;
  font-size: 64px;
  line-height: 1;
  letter-spacing: -0.3px;
  font-weight: 400;
}

.emotion-29 .amp-cms--headlineAlt-2 {
  color: #003764;
  font-size: 54px;
  line-height: 1;
  letter-spacing: -0.3px;
  font-weight: 400;
}

.emotion-29 .amp-cms--headlineAlt-3 {
  color: #003764;
  font-size: 34px;
  line-height: 1;
  letter-spacing: -0.3px;
  font-weight: 400;
}

.emotion-29 .amp-cms--headlineAlt-4 {
  color: #003764;
  font-size: 24px;
  line-height: 1.4166666666666667;
  letter-spacing: -0.3px;
  font-weight: 400;
}

.emotion-29 .amp-cms--headlineAlt-5 {
  color: #003764;
  font-size: 14px;
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-29 .amp-cms--headlineAlt-6 {
  color: #003764;
  font-size: 14px;
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-29 .amp-cms--headlineAlt-7 {
  color: #003764;
  font-size: 14px;
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-29 .amp-cms--promo-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 60px;
  line-height: 1;
  letter-spacing: 0px;
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-29 .amp-cms--promo-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 40px;
  line-height: 1;
  letter-spacing: 0px;
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-29 .amp-cms--promoAlt-1 {
  color: #003764;
  font-size: 14px;
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 600;
}

.emotion-29 .amp-cms--subhead-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 20px;
  line-height: 1.5;
  letter-spacing: 0;
  font-weight: 500;
}

.emotion-29 .amp-cms--subhead-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 18px;
  line-height: 1.5;
  letter-spacing: 0;
  font-weight: 500;
}

.emotion-29 .amp-cms--subhead-3 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 16px;
  line-height: 1.5;
  letter-spacing: 0;
  font-weight: 500;
}

.emotion-36 {
  width: 100%;
  height: 100%;
  padding: 30px 16px;
  box-sizing: border-box;
  position: static;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  grid-template-columns: repeat(3, minmax(0,max-content));
  -webkit-align-content: center;
  -ms-flex-line-pack: center;
  align-content: center;
  grid-template-rows: repeat(3, minmax(0, max-content));
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  row-gap: 18px;
}

.emotion-36 .rteWrapperDivs {
  grid-column: 3;
  grid-row: 2;
}

.emotion-36 .ctaWrapperDivs {
  grid-column: 3;
  grid-row: 2;
}

.emotion-37 {
  display: block;
  width: 100%;
  position: relative;
  z-index: 2;
  text-align: end;
}

.emotion-38 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 700;
  letter-spacing: 1.05px;
  min-height: 44px;
  max-height: auto;
  line-height: 1.8571428571428572;
  padding: 0;
  width: auto;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: left;
  border-left: none;
  border-right: none;
  border-radius: 0;
  background-color: transparent;
  border-bottom: 0;
  -webkit-box-pack: left;
  -ms-flex-pack: left;
  -webkit-justify-content: left;
  justify-content: left;
  color: #003764;
  height: auto;
  -webkit-text-decoration: underline;
  text-decoration: underline;
  text-underline-offset: 3.5px;
  text-decoration-thickness: 3px;
  -webkit-align-self: flex-start;
  -ms-flex-item-align: flex-start;
  align-self: flex-start;
  white-space: normal;
}

.emotion-38:focus {
  outline: none;
}

.emotion-38>span {
  padding: 1px 0;
}

.emotion-38:hover,
.emotion-38:focus {
  text-decoration-color: #003764;
}

.emotion-38:hover span,
.emotion-38:focus span {
  color: #FFFFFF;
  background-color: #003764;
}

.emotion-38:active {
  text-decoration-color: #000000;
}

.emotion-38:active>span {
  color: #FFFFFF;
  background-color: #000000;
}

.emotion-39 {
  box-sizing: border-box;
}

.emotion-46 {
  width: 100%;
  height: 100%;
  padding: 30px 16px;
  box-sizing: border-box;
  position: static;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  grid-template-columns: repeat(3, minmax(0,max-content));
  -webkit-align-content: center;
  -ms-flex-line-pack: center;
  align-content: center;
  grid-template-rows: repeat(3, minmax(0, max-content));
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  row-gap: 18px;
}

.emotion-46 .rteWrapperDivs {
  grid-column: 3;
  grid-row: 1;
}

.emotion-46 .ctaWrapperDivs {
  grid-column: 3;
  grid-row: 1;
}

.emotion-47 {
  box-sizing: content-box;
  text-align: right;
}

.emotion-48 {
  text-align: end;
}

.emotion-48 .amp-cms--p {
  padding: 0;
  margin: 0;
  line-height: 0;
}

.emotion-48 .amp-cms--p>span {
  display: inline;
  white-space: break-spaces;
}

.emotion-48 .amp-cms--p a {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  position: relative;
  z-index: 2;
  pointer-events: auto;
}

.emotion-48 .amp-cms--p a:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-48 .amp-cms--legal-copy {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 10px;
  line-height: 1.5;
  letter-spacing: 0;
}

.emotion-48 sup {
  vertical-align: top;
  display: inline-block;
  margin-top: -0.20ex;
}

.emotion-48 sub {
  vertical-align: bottom;
  display: inline-block;
  margin-bottom: -0.20ex;
}

.emotion-48 .amp-cms--f-0 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 11px;
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-48 .amp-cms--f-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 11px;
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-48 .amp-cms--f-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 11px;
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-48 .amp-cms--fn-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 11px;
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-48 .amp-cms--body-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 14px;
  line-height: 1.5;
  letter-spacing: 0.336px;
  font-weight: 500;
}

.emotion-48 .amp-cms--body-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.5;
  letter-spacing: 0.288px;
  font-weight: 500;
}

.emotion-48 .amp-cms--body-3 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.5;
  letter-spacing: 0.288px;
}

.emotion-48 .amp-cms--body-4 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 10px;
  line-height: 1.5;
  letter-spacing: 0;
}

.emotion-48 .amp-cms--body-5 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 10px;
  line-height: 1.5;
  letter-spacing: 0;
}

.emotion-48 .amp-cms--eyebrow-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 23px;
  line-height: 1;
  letter-spacing: 0.92px;
  font-weight: 500;
}

.emotion-48 .amp-cms--eyebrow-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 19px;
  line-height: 1;
  letter-spacing: 0.76px;
  font-weight: 500;
}

.emotion-48 .amp-cms--eyebrow-3 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 14px;
  line-height: 1.0714285714285714;
  letter-spacing: 0.56px;
  font-weight: 500;
}

.emotion-48 .amp-cms--headline-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 68px;
  line-height: 1;
  letter-spacing: 0;
  font-weight: 700;
}

.emotion-48 .amp-cms--headline-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 50px;
  line-height: 1;
  letter-spacing: 0;
  font-weight: 700;
}

.emotion-48 .amp-cms--headline-3 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 43px;
  line-height: 1;
  letter-spacing: 0;
  font-weight: 700;
}

.emotion-48 .amp-cms--headline-4 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 36px;
  line-height: 1;
  letter-spacing: 0.72px;
  font-weight: 700;
}

.emotion-48 .amp-cms--headline-5 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 28px;
  line-height: 1;
  letter-spacing: 1.12px;
  font-weight: 700;
}

.emotion-48 .amp-cms--headline-6 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 24px;
  line-height: 1.0833333333333333;
  letter-spacing: 0.96px;
  font-weight: 700;
}

.emotion-48 .amp-cms--headline-7 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 20px;
  line-height: 1;
  letter-spacing: 0.48px;
  font-weight: 700;
}

.emotion-48 .amp-cms--headlineAlt-1 {
  color: #003764;
  font-size: 64px;
  line-height: 1;
  letter-spacing: -0.3px;
  font-weight: 400;
}

.emotion-48 .amp-cms--headlineAlt-2 {
  color: #003764;
  font-size: 54px;
  line-height: 1;
  letter-spacing: -0.3px;
  font-weight: 400;
}

.emotion-48 .amp-cms--headlineAlt-3 {
  color: #003764;
  font-size: 34px;
  line-height: 1;
  letter-spacing: -0.3px;
  font-weight: 400;
}

.emotion-48 .amp-cms--headlineAlt-4 {
  color: #003764;
  font-size: 24px;
  line-height: 1.4166666666666667;
  letter-spacing: -0.3px;
  font-weight: 400;
}

.emotion-48 .amp-cms--headlineAlt-5 {
  color: #003764;
  font-size: 14px;
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-48 .amp-cms--headlineAlt-6 {
  color: #003764;
  font-size: 14px;
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-48 .amp-cms--headlineAlt-7 {
  color: #003764;
  font-size: 14px;
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-48 .amp-cms--promo-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 60px;
  line-height: 1;
  letter-spacing: 0px;
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-48 .amp-cms--promo-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 40px;
  line-height: 1;
  letter-spacing: 0px;
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-48 .amp-cms--promoAlt-1 {
  color: #003764;
  font-size: 14px;
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 600;
}

.emotion-48 .amp-cms--subhead-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 20px;
  line-height: 1.5;
  letter-spacing: 0;
  font-weight: 500;
}

.emotion-48 .amp-cms--subhead-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 18px;
  line-height: 1.5;
  letter-spacing: 0;
  font-weight: 500;
}

.emotion-48 .amp-cms--subhead-3 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 16px;
  line-height: 1.5;
  letter-spacing: 0;
  font-weight: 500;
}

.emotion-50 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 700;
  letter-spacing: 1.6800000000000002px;
  min-height: 44px;
  max-height: auto;
  line-height: 1.1428571428571428;
  padding: 12px 22px;
  width: auto;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #003764;
  background: transparent;
  border-color: #003764;
  -webkit-align-self: flex-start;
  -ms-flex-item-align: flex-start;
  align-self: flex-start;
  white-space: normal;
}

.emotion-50:focus {
  outline: none;
}

.emotion-50>span {
  padding: 1px 0;
}

.emotion-50:hover,
.emotion-50:focus {
  color: #FFFFFF;
  background-color: #003764;
  border-color: #003764;
}

.emotion-50:active {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  color: #000000;
  background-color: transparent;
  border-color: #000000;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-family-font1),sans-serif;"
    >
      <div
        class="emotion-0"
      >
        <section
          class="emotion-1"
        >
          <div
            class="emotion-2"
          >
            <nav
              class="emotion-3"
            >
              <div
                class="emotion-4"
              >
                <button
                  aria-label="pause"
                  class="emotion-5"
                >
                  <div
                    class="emotion-6"
                  >
                    <span
                      aria-hidden="true"
                      class="emotion-7"
                    />
                  </div>
                </button>
                <div
                  class="slick-slider slick-initialized"
                  dir="ltr"
                >
                  <button
                    aria-label="Previous"
                    class="slick-prev slick-arrow slick-prev slick-disabled"
                    data-role="none"
                    disabled=""
                  >
                    <span
                      aria-hidden="true"
                      class="emotion-8"
                    >
                      <svg
                        viewBox="0 0 13.29 8.07"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          d="M6.13 7.86a.73.73 0 001.04 0l5.92-5.92a.73.73 0 000-1.03l-.7-.7a.73.73 0 00-1.03 0l-4.71 4.7L1.94.2A.73.73 0 00.9.21l-.69.7a.73.73 0 000 1.03z"
                          fill="#003764"
                        />
                      </svg>
                    </span>
                  </button>
                  <div
                    class="slick-list"
                  >
                    <div
                      class="slick-track"
                      style="opacity: 1; transform: translate3d(0px, 0px, 0px);"
                    >
                      <div
                        aria-hidden="false"
                        class="slick-slide slick-active slick-current"
                        data-index="0"
                        style="outline: none; width: 0px;"
                        tabindex="-1"
                      >
                        <div>
                          <div
                            class="emotion-2"
                          >
                            <div
                              class="emotion-10"
                            >
                              <div
                                class="emotion-11"
                                height="200"
                                width="341"
                              >
                                <div
                                  class="emotion-12"
                                  data-testid="product-card-image"
                                >
                                  <img
                                    alt="Female red shirt"
                                    class="emotion-13"
                                    src="https://1puomodm9vwdc1sabswsbq9607.staging.bigcontent.io/i/oldnavy/210706_26-M1837_EarlyBTS_HP_Primary_CatNav03_US_XL?fmt=webp"
                                  />
                                </div>
                              </div>
                              <a
                                class="emotion-14"
                                href="https://page.not.found"
                                tabindex="-1"
                                target="_self"
                                title="page not found"
                              />
                            </div>
                            <div
                              class="emotion-15"
                            >
                              <div
                                class="emotion-16"
                              >
                                <div
                                  class="rteWrapperDivs emotion-17"
                                >
                                  <div
                                    class="emotion-18"
                                  >
                                    <div>
                                      <p
                                        class="amp-cms--p"
                                      >
                                        <span
                                          class="amp-cms--headline-6"
                                        >
                                          Lorem Ipsum Dolor
                                        </span>
                                      </p>
                                      <p
                                        class="amp-cms--p"
                                      >
                                        <span
                                          class="amp-cms--body-1"
                                        >
                                          Sit amet, consectetur adipiscing
                                        </span>
                                      </p>
                                    </div>
                                  </div>
                                </div>
                                <div
                                  class="ctaWrapperDivs emotion-19"
                                >
                                  <a
                                    class="emotion-20"
                                    color="dark"
                                    href="https://page.not.found"
                                  >
                                    Buy now
                                  </a>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                      <div
                        aria-hidden="true"
                        class="slick-slide"
                        data-index="1"
                        style="outline: none; width: 0px;"
                        tabindex="-1"
                      >
                        <div>
                          <div
                            class="emotion-2"
                          >
                            <div
                              class="emotion-10"
                            >
                              <div
                                class="emotion-11"
                                height="200"
                                width="341"
                              >
                                <div
                                  class="emotion-12"
                                  data-testid="product-card-image"
                                >
                                  <img
                                    alt="Man landed and wearing white shirt and jeans"
                                    class="emotion-13"
                                    src="https://pqm4o81ro4dn1332lt5a8eas8.staging.bigcontent.io/i/gap/tshirt_mobile?fmt=webp"
                                  />
                                </div>
                              </div>
                            </div>
                            <div
                              class="emotion-15"
                            >
                              <div
                                class="emotion-27"
                              >
                                <div
                                  class="rteWrapperDivs emotion-28"
                                >
                                  <div
                                    class="emotion-29"
                                  >
                                    <div>
                                      <p
                                        class="amp-cms--p"
                                      >
                                        <span
                                          class="amp-cms--headline-6"
                                          style="text-transform:uppercase;font-style:italic"
                                        >
                                          Lorem Ipsum Dolor
                                        </span>
                                      </p>
                                      <p
                                        class="amp-cms--p"
                                      >
                                        <span
                                          class="amp-cms--body-1"
                                        >
                                          Sit amet, 
                                        </span>
                                        <span
                                          class="amp-cms--body-1"
                                          style="text-decoration:underline"
                                        >
                                          consectetur
                                        </span>
                                        <span
                                          class="amp-cms--body-1"
                                        >
                                           adipiscing
                                        </span>
                                      </p>
                                    </div>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                      <div
                        aria-hidden="true"
                        class="slick-slide"
                        data-index="2"
                        style="outline: none; width: 0px;"
                        tabindex="-1"
                      >
                        <div>
                          <div
                            class="emotion-2"
                          >
                            <div
                              class="emotion-10"
                            >
                              <div
                                class="emotion-11"
                                height="200"
                                width="341"
                              >
                                <div
                                  class="emotion-12"
                                  data-testid="product-card-image"
                                >
                                  <img
                                    alt="Boy sitting and smiling"
                                    class="emotion-13"
                                    src="https://pqm4o81ro4dn1332lt5a8eas8.staging.bigcontent.io/i/gap/G32408_Boys_MOB_anim2?fmt=webp"
                                  />
                                </div>
                              </div>
                            </div>
                            <div
                              class="emotion-15"
                            >
                              <div
                                class="emotion-36"
                              >
                                <div
                                  class="ctaWrapperDivs emotion-37"
                                >
                                  <a
                                    class="emotion-38"
                                    color="dark"
                                    href="https://page.not.found"
                                    tabindex="-1"
                                  >
                                    <span
                                      class="emotion-39"
                                    >
                                      New Arrivals
                                    </span>
                                  </a>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                      <div
                        aria-hidden="true"
                        class="slick-slide"
                        data-index="3"
                        style="outline: none; width: 0px;"
                        tabindex="-1"
                      >
                        <div>
                          <div
                            class="emotion-2"
                          >
                            <div
                              class="emotion-10"
                            >
                              <div
                                class="emotion-11"
                                height="200"
                                width="341"
                              >
                                <div
                                  class="emotion-12"
                                  data-testid="product-card-image"
                                >
                                  <img
                                    alt="Woman landing and wearing white shirt and black pants"
                                    class="emotion-13"
                                    src="https://1puomodm9vwdc1sabswsbq9607.staging.bigcontent.io/i/oldnavy/Test-AG-Men-Pants-4?fmt=webp"
                                  />
                                </div>
                              </div>
                            </div>
                            <div
                              class="emotion-15"
                            >
                              <div
                                class="emotion-46"
                              >
                                <div
                                  class="rteWrapperDivs emotion-47"
                                >
                                  <div
                                    class="emotion-48"
                                  >
                                    <div>
                                      <p
                                        class="amp-cms--p"
                                      >
                                        <span
                                          class="amp-cms--headline-6"
                                          style="text-transform:uppercase"
                                        >
                                          Lorem Ipsum Dolor
                                        </span>
                                      </p>
                                      <p
                                        class="amp-cms--p"
                                      >
                                        <span
                                          class="amp-cms--body-1"
                                          style="font-style:italic"
                                        >
                                          Sit amet, consectetur adipiscing
                                        </span>
                                      </p>
                                    </div>
                                  </div>
                                </div>
                                <div
                                  class="ctaWrapperDivs emotion-37"
                                >
                                  <a
                                    class="emotion-50"
                                    color="dark"
                                    href="https://page.not.found"
                                    tabindex="-1"
                                  >
                                    SHOP HERE
                                  </a>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                  <button
                    aria-label="Next"
                    class="slick-next slick-arrow slick-next"
                    data-role="none"
                  >
                    <span
                      aria-hidden="true"
                      class="emotion-8"
                    >
                      <svg
                        viewBox="0 0 13.29 8.07"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          d="M6.13 7.86a.73.73 0 001.04 0l5.92-5.92a.73.73 0 000-1.03l-.7-.7a.73.73 0 00-1.03 0l-4.71 4.7L1.94.2A.73.73 0 00.9.21l-.69.7a.73.73 0 000 1.03z"
                          fill="#003764"
                        />
                      </svg>
                    </span>
                  </button>
                </div>
              </div>
            </nav>
          </div>
        </section>
      </div>
    </div>
  </div>
</DocumentFragment>
`;
