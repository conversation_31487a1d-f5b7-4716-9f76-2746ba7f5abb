// @ts-nocheck
import { SubCategoryBannerVHCarouselProps } from '../../../components/SubCategoryBannerVHCarousel';

export const scbvhCarouselBaseData: SubCategoryBannerVHCarouselProps = {
  desktopBannerSize: 'medium',
  mobileBannerSize: 'medium',
  contentConfiguration: 'unique',
  mobileTextTreatment: 'below',
  carouselSettings: {
    transition: 'slide',
    type: 'clickThrough',
    continuousLoop: false,
    autoplay: {
      delay: 3000,
      pauseOnHover: false,
    },
    animation: {
      speed: 500,
      ease: false,
    },
    styling: {
      controlsIconsColor: 'primary',
      pagination: 'hide',
      hideChevrons: false,
    },
  },
  frames: [
    {
      backgroundImage: [
        {
          image: {
            _meta: {
              schema: 'http://bigcontent.io/cms/schema/v1/core#/definitions/image-link',
            },
            id: '0e1911e3-d193-472e-ac07-eb19491a084b',
            name: '210706_26-M1837_EarlyBTS_HP_Primary_CatNav03_US_XL',
            endpoint: 'oldnavy',
            defaultHost: '1puomodm9vwdc1sabswsbq9607.staging.bigcontent.io',
          },
          altText: 'Female red shirt',
          variations: [
            {
              variation: 'desktop',
            },
            {
              variation: 'mobile',
            },
          ],
          fliph: false,
          flipv: false,
          enableChroma: false,
          chromaQuality: 80,
        },
      ],
      ctaButton: {
        label: 'Buy now',
        value: 'page.not.found',
      },
      bannerLink: {
        value: 'page.not.found',
        label: 'page not found',
      },
      webAppearance: {
        desktop: {
          verticalPlacement: 'center',
          horizontalPlacement: 'center',
          textJustification: 'center',
          ctaVerticalPlacement: 'center',
          ctaHorizontalPlacement: 'center',
          ctaJustification: 'center',
          ctaButtonStyling: {
            buttonStyle: 'border',
            buttonColor: 'dark',
          },
        },
        mobile: {
          textJustification: 'center',
          ctaPlacement: 'center',
        },
      },
      text: '<p class="amp-cms--p" style="text-align:left;"><span class="amp-cms--headline-6">Lorem Ipsum Dolor</span></p><p class="amp-cms--p" style="text-align:left;"><span class="amp-cms--body-1">Sit amet, consectetur adipiscing</span></p>',
    },
    {
      backgroundImage: [
        {
          image: {
            _meta: {
              schema: 'http://bigcontent.io/cms/schema/v1/core#/definitions/image-link',
            },
            id: 'db5630cc-56c1-4b0a-8585-b15136bd222c',
            name: 'tshirt_mobile',
            endpoint: 'gap',
            defaultHost: 'pqm4o81ro4dn1332lt5a8eas8.staging.bigcontent.io',
          },
          altText: 'Man landed and wearing white shirt and jeans',
          variations: [
            {
              variation: 'desktop',
            },
            {
              variation: 'mobile',
            },
          ],
          fliph: false,
          flipv: false,
          enableChroma: false,
          chromaQuality: 80,
        },
      ],
      webAppearance: {
        desktop: {
          verticalPlacement: 'start',
          horizontalPlacement: 'start',
          textJustification: 'center',
          ctaVerticalPlacement: 'center',
          ctaHorizontalPlacement: 'center',
          ctaJustification: 'center',
          ctaButtonStyling: {
            buttonStyle: 'border',
            buttonColor: 'dark',
          },
        },
        mobile: {
          textJustification: 'start',
          ctaPlacement: 'start',
        },
      },
      text: '<p class="amp-cms--p" style="text-align:left;"><span class="amp-cms--headline-6" style="text-transform:uppercase;font-style:italic">Lorem Ipsum Dolor</span></p><p class="amp-cms--p" style="text-align:left;"><span class="amp-cms--body-1">Sit amet, </span><span class="amp-cms--body-1" style="text-decoration:underline">consectetur</span><span class="amp-cms--body-1"> adipiscing</span></p>',
    },
    {
      backgroundImage: [
        {
          image: {
            _meta: {
              schema: 'http://bigcontent.io/cms/schema/v1/core#/definitions/image-link',
            },
            id: '7cd404ee-a242-4512-8a7a-8bb4cdb7f656',
            name: 'G32408_Boys_MOB_anim2',
            endpoint: 'gap',
            defaultHost: 'pqm4o81ro4dn1332lt5a8eas8.staging.bigcontent.io',
          },
          altText: 'Boy sitting and smiling',
          variations: [
            {
              variation: 'desktop',
            },
            {
              variation: 'mobile',
            },
          ],
          fliph: false,
          flipv: false,
          enableChroma: false,
          chromaQuality: 80,
        },
      ],
      webAppearance: {
        desktop: {
          verticalPlacement: 'center',
          horizontalPlacement: 'end',
          ctaVerticalPlacement: 'center',
          textJustification: 'center',
          ctaHorizontalPlacement: 'right',
          ctaJustification: 'center',
          ctaButtonStyling: {
            buttonStyle: 'underline',
            buttonColor: 'dark',
          },
        },
        mobile: {
          textJustification: 'end',
          ctaPlacement: 'end',
        },
      },
      ctaButton: {
        label: 'New Arrivals',
        value: 'page.not.found',
      },
    },
    {
      backgroundImage: [
        {
          image: {
            _meta: {
              schema: 'http://bigcontent.io/cms/schema/v1/core#/definitions/image-link',
            },
            id: '515952bd-0c77-45fa-b249-4db090a25012',
            name: 'Test-AG-Men-Pants-4',
            endpoint: 'oldnavy',
            defaultHost: '1puomodm9vwdc1sabswsbq9607.staging.bigcontent.io',
          },
          altText: 'Woman landing and wearing white shirt and black pants',
          variations: [
            {
              variation: 'desktop',
            },
            {
              variation: 'mobile',
            },
          ],
          fliph: false,
          flipv: false,
          enableChroma: false,
          chromaQuality: 80,
        },
      ],
      webAppearance: {
        desktop: {
          verticalPlacement: 'start',
          horizontalPlacement: 'end',
          ctaVerticalPlacement: 'start',
          textJustification: 'center',
          ctaHorizontalPlacement: 'right',
          ctaJustification: 'center',
          ctaButtonStyling: {
            buttonStyle: 'outline',
            buttonColor: 'dark',
          },
        },
        mobile: {
          textJustification: 'end',
          ctaPlacement: 'end',
        },
      },
      text: '<p class="amp-cms--p" style="text-align:left;"><span class="amp-cms--headline-6" style="text-transform:uppercase">Lorem Ipsum Dolor</span></p><p class="amp-cms--p" style="text-align:left;"><span class="amp-cms--body-1" style="font-style:italic">Sit amet, consectetur adipiscing</span></p>',
      ctaButton: {
        label: 'SHOP HERE',
        value: 'page.not.found',
      },
    },
  ],
  webAppearance: {
    showHideBasedOnScreenSize: 'alwaysShow',
  },
};
