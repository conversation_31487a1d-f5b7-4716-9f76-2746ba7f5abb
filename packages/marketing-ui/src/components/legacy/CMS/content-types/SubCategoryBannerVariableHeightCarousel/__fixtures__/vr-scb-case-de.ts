// @ts-nocheck
import { SubCategoryBannerVHCarouselProps } from '../../../components/SubCategoryBannerVHCarousel';
// Example D - Sub Category Banner Carousel Variable Height: Large Background Image, RTE - left, CTA - left, NO Chevrons, Pagination Mobile
export const subCategoryBannerCaseD: SubCategoryBannerVHCarouselProps = {
  carouselSettings: {
    transition: 'fade',
    type: 'autoplay',
    continuousLoop: false,
    autoplay: {
      delay: 100,
      pauseOnHover: true,
    },
    animation: {
      speed: 900,
      ease: false,
    },
    styling: {
      controlsIconsColor: 'secondary',
      pagination: 'mobile',
      hideChevrons: true,
    },
  },
  frames: [
    {
      backgroundImage: [
        {
          image: {
            _meta: {
              schema: 'http://bigcontent.io/cms/schema/v1/core#/definitions/image-link',
            },
            id: '99aa32aa-84a2-4d93-9a9c-dd3916b9a654',
            name: 'Now_Trending_XL_QC',
            endpoint: 'oldnavy',
            defaultHost: 'fmzlik0m0z6r1oifp8ex832rf.staging.bigcontent.io',
          },
          altText: '#',
          variations: [
            {
              variation: 'desktop',
            },
            {
              variation: 'mobile',
            },
          ],
          fliph: false,
          flipv: false,
          enableChroma: false,
          chromaQuality: 80,
        },
      ],
      webAppearance: {
        desktop: {
          verticalPlacement: 'center',
          horizontalPlacement: 'end',
          textJustification: 'end',
          ctaVerticalPlacement: 'center',
          ctaHorizontalPlacement: 'right',
          ctaJustification: 'end',
          ctaButtonStyling: {
            buttonStyle: 'underline',
            buttonColor: 'dark',
          },
        },
        mobile: {
          textJustification: 'end',
          ctaPlacement: 'end',
        },
      },
      text: '<p class="amp-cms--p" style="text-align:left;"><span class="amp-cms--body-2" style="color:#FFFFFF">NOW AVAILABLE AT YOUR LOCAL STORES & ONLINE</span></p><hr style="display:block;border:0;height:8px;margin:0;background:transparent;" aria-hidden="true" /><p class="amp-cms--p" style="text-align:left;"><span class="amp-cms--headlineAlt-2" style="color:#F59A11;font-weight:700">Lorem ipsum dolor</span></p>',
      ctaButton: {
        label: 'SHOP NOW',
        value: '#',
      },
      mobileBackgroundImage: [
        {
          image: {
            _meta: {
              schema: 'http://bigcontent.io/cms/schema/v1/core#/definitions/image-link',
            },
            id: '175902c1-d939-46af-be2b-4562ac879c88',
            name: 'EDM_tile5',
            endpoint: 'oldnavy',
            defaultHost: 'fmzlik0m0z6r1oifp8ex832rf.staging.bigcontent.io',
          },
          altText: '',
          variations: [
            {
              variation: 'desktop',
            },
            {
              variation: 'mobile',
            },
          ],
          fliph: false,
          flipv: false,
          svgPath: 'https://oldnavy.a.bigcontent.io/v1/static/EDM_tile5',
          enableChroma: false,
          chromaQuality: 80,
        },
      ],
    },
    {
      backgroundImage: [
        {
          image: {
            _meta: {
              schema: 'http://bigcontent.io/cms/schema/v1/core#/definitions/image-link',
            },
            id: '3199e243-9711-4547-a524-74aabceb46a1',
            name: '230424_60_M5003_SummerHouse_Hero3_HP_XL_US',
            endpoint: 'oldnavy',
            defaultHost: 'fmzlik0m0z6r1oifp8ex832rf.staging.bigcontent.io',
          },
          altText: '',
          variations: [
            {
              variation: 'desktop',
            },
            {
              variation: 'mobile',
            },
          ],
          fliph: false,
          flipv: false,
          enableChroma: false,
          chromaQuality: 80,
        },
      ],
      webAppearance: {
        desktop: {
          verticalPlacement: 'center',
          horizontalPlacement: 'start',
          textJustification: 'start',
          ctaVerticalPlacement: 'center',
          ctaHorizontalPlacement: 'left',
          ctaJustification: 'start',
          ctaButtonStyling: {
            buttonStyle: 'underline',
            buttonColor: 'dark',
          },
        },
        mobile: {
          textJustification: 'start',
          ctaPlacement: 'start',
        },
      },
      text: '<p class="amp-cms--p" style="text-align:left;"><span class="amp-cms--body-2" style="color:##2BFA14">NOW AVAILABLE AT YOUR LOCAL STORES & ONLINE</span></p><hr style="display:block;border:0;height:8px;margin:0;background:transparent;" aria-hidden="true" /><p class="amp-cms--p" style="text-align:left;"><span class="amp-cms--headlineAlt-2" style="color:#F59A11;font-weight:700">Lorem ipsum dolor</span></p>',
      ctaButton: {
        label: 'SHOP ALL',
        value: '#',
      },
      mobileBackgroundImage: [
        {
          image: {
            _meta: {
              schema: 'http://bigcontent.io/cms/schema/v1/core#/definitions/image-link',
            },
            id: '491d188b-9620-4c95-bc67-bc793197460a',
            name: 'shopping-card-bkg',
            endpoint: 'oldnavy',
            defaultHost: 'fmzlik0m0z6r1oifp8ex832rf.staging.bigcontent.io',
          },
          altText: '',
          variations: [
            {
              variation: 'desktop',
            },
            {
              variation: 'mobile',
            },
          ],
          fliph: false,
          flipv: false,
          enableChroma: false,
          chromaQuality: 80,
        },
      ],
    },
  ],
  contentConfiguration: 'unique',
  desktopBannerSize: 'large',
  webAppearance: {
    showHideBasedOnScreenSize: 'alwaysShow',
  },
  mobileBannerSize: 'large',
  mobileTextTreatment: 'on',
};
// Example E - Sub Category Banner Carousel Variable Height: Medium Background Image, RTE - right, CTA - right, Chevrons
export const subCategoryBannerCaseE: SubCategoryBannerVHCarouselProps = {
  carouselSettings: {
    transition: 'slide',
    type: 'clickThrough',
    continuousLoop: true,
    autoplay: {
      delay: 100,
      pauseOnHover: true,
    },
    animation: {
      speed: 900,
      ease: false,
    },
    styling: {
      controlsIconsColor: 'primary',
      pagination: 'hide',
      hideChevrons: false,
    },
  },
  frames: [
    {
      backgroundImage: [
        {
          image: {
            _meta: {
              schema: 'http://bigcontent.io/cms/schema/v1/core#/definitions/image-link',
            },
            id: 'f9f4e686-ec54-4961-b188-ba8958e449e9',
            name: '201102_99_M0180_JingleJammies_FAM_VIBanner_USCA_XL',
            endpoint: 'oldnavy',
            defaultHost: 'fmzlik0m0z6r1oifp8ex832rf.staging.bigcontent.io',
          },
          altText: '#',
          variations: [
            {
              variation: 'desktop',
            },
            {
              variation: 'mobile',
            },
          ],
          fliph: false,
          flipv: false,
          enableChroma: false,
          chromaQuality: 80,
        },
      ],
      webAppearance: {
        desktop: {
          verticalPlacement: 'center',
          horizontalPlacement: 'end',
          textJustification: 'end',
          ctaVerticalPlacement: 'center',
          ctaHorizontalPlacement: 'right',
          ctaJustification: 'end',
          ctaButtonStyling: {
            buttonStyle: 'underline',
            buttonColor: 'dark',
          },
        },
        mobile: {
          textJustification: 'end',
          ctaPlacement: 'end',
        },
      },
      text: '<p class="amp-cms--p" style="text-align:left;"><span class="amp-cms--body-2" style="color:#2BFA14">NOW AVAILABLE AT YOUR LOCAL STORES & ONLINE</span></p><p class="amp-cms--p" style="text-align:left;"><span class="amp-cms--headlineAlt-2" style="color:#F59A11;font-weight:700">Lorem ipsum dolor</span></p><p class="amp-cms--p" style="text-align:left;"><span class="amp-cms--body-1" style="color:#F59A11">dolor sit amet consectetur adipiscing elit sed do eiusmod tempor incididunt ut</span></p><p class="amp-cms--p" style="text-align:left;"><span class="amp-cms--body-1" style="color:#F59A11">labore et dolore magna aliqua.</span></p>',
      ctaButton: {
        label: 'SHOP NOW',
        value: '#',
      },
      mobileBackgroundImage: [
        {
          image: {
            _meta: {
              schema: 'http://bigcontent.io/cms/schema/v1/core#/definitions/image-link',
            },
            id: '9b4d8f75-f812-48eb-be03-a836b80d632b',
            name: 'rm222batch5-kul-03',
            endpoint: 'oldnavy',
            defaultHost: 'fmzlik0m0z6r1oifp8ex832rf.staging.bigcontent.io',
          },
          altText: '#',
          variations: [
            {
              variation: 'desktop',
            },
            {
              variation: 'mobile',
            },
          ],
          fliph: false,
          flipv: false,
          enableChroma: false,
          chromaQuality: 80,
        },
      ],
    },
    {
      backgroundImage: [
        {
          image: {
            _meta: {
              schema: 'http://bigcontent.io/cms/schema/v1/core#/definitions/image-link',
            },
            id: '99aa32aa-84a2-4d93-9a9c-dd3916b9a654',
            name: 'Now_Trending_XL_QC',
            endpoint: 'oldnavy',
            defaultHost: 'fmzlik0m0z6r1oifp8ex832rf.staging.bigcontent.io',
          },
          altText: '#',
          variations: [
            {
              variation: 'desktop',
            },
            {
              variation: 'mobile',
            },
          ],
          fliph: false,
          flipv: false,
          enableChroma: false,
          chromaQuality: 80,
        },
      ],
      webAppearance: {
        desktop: {
          verticalPlacement: 'center',
          horizontalPlacement: 'start',
          textJustification: 'start',
          ctaVerticalPlacement: 'center',
          ctaHorizontalPlacement: 'left',
          ctaJustification: 'start',
          ctaButtonStyling: {
            buttonStyle: 'underline',
            buttonColor: 'dark',
          },
        },
        mobile: {
          textJustification: 'start',
          ctaPlacement: 'start',
        },
      },
      text: '<p class="amp-cms--p" style="text-align:left;"><span class="amp-cms--body-2" style="color:#2BFA14">NOW AVAILABLE AT YOUR LOCAL STORES & ONLINE</span></p><p class="amp-cms--p" style="text-align:left;"><span class="amp-cms--headlineAlt-2" style="color:#F59A11;font-weight:700">Lorem ipsum dolor</span></p>',
      ctaButton: {
        label: 'SHOP ALL',
        value: '#',
      },
    },
    {
      backgroundImage: [
        {
          image: {
            _meta: {
              schema: 'http://bigcontent.io/cms/schema/v1/core#/definitions/image-link',
            },
            id: '0901d8b2-5b28-4b67-afa4-5b107adb7b63',
            name: '220418_84_M8050_ShopBySizeRefresh_B_VIBanner_USCA_1',
            endpoint: 'oldnavy',
            defaultHost: 'fmzlik0m0z6r1oifp8ex832rf.staging.bigcontent.io',
          },
          altText: '#',
          variations: [
            {
              variation: 'desktop',
            },
            {
              variation: 'mobile',
            },
          ],
          fliph: false,
          flipv: false,
          enableChroma: false,
          chromaQuality: 80,
        },
      ],
      webAppearance: {
        desktop: {
          verticalPlacement: 'center',
          horizontalPlacement: 'center',
          textJustification: 'center',
          ctaVerticalPlacement: 'end',
          ctaHorizontalPlacement: 'center',
          ctaJustification: 'center',
          ctaButtonStyling: {
            buttonStyle: 'underline',
            buttonColor: 'dark',
          },
        },
        mobile: {
          textJustification: 'center',
          ctaPlacement: 'center',
        },
      },
      text: '<p class="amp-cms--p" style="text-align:left;"><span class="amp-cms--headlineAlt-2" style="color:#F59A11;font-weight:700">Lorem ipsum dolor</span></p>',
      ctaButton: {
        label: 'SHOP FOR LESS',
        value: '#',
      },
    },
  ],
  contentConfiguration: 'unique',
  desktopBannerSize: 'medium',
  webAppearance: {
    showHideBasedOnScreenSize: 'alwaysShow',
  },
  mobileBannerSize: 'medium',
  mobileTextTreatment: 'below',
};
