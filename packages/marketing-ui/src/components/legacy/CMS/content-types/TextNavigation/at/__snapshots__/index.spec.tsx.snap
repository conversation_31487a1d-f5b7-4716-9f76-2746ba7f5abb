// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Text Navigation AT content type customized button styles desktop shows styles for TextNavCarousel 1`] = `
.emotion-0 {
  margin-left: auto;
  margin-right: auto;
  max-width: 1280px;
}

.emotion-1 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  min-height: 50px;
}

.emotion-2 {
  -webkit-flex: 1;
  -ms-flex: 1;
  flex: 1;
  gap: 0;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-2>button,
.emotion-2>a {
  min-height: 50px;
  -webkit-flex: 1 1 auto;
  -ms-flex: 1 1 auto;
  flex: 1 1 auto;
}

.emotion-2>button:not(:first-of-type),
.emotion-2>a:not(:first-of-type) {
  border-left-color: transparent;
}

.emotion-3 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: none;
  transition: none;
  box-sizing: border-box;
  border: 1.5px solid transparent;
  border-radius: 0;
  font-size: 16px;
  font-weight: 500;
  letter-spacing: 0px;
  min-height: auto;
  max-height: 60px;
  line-height: 1.25;
  padding: 22px 40px;
  width: auto;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  background-color: #FFFFFF;
  color: #000000;
  border-color: #000000;
  border-width: 0.5px;
  box-sizing: border-box;
  margin: 0;
  border-radius: 0;
  padding: 9px 14px;
  line-height: 1.1176470588235294;
  letter-spacing: 0px;
  min-height: 56px;
  color: #FFFFFF;
  background-color: #000000;
  border-color: #FF0000;
}

.emotion-3:focus {
  outline: none;
}

.emotion-3>span {
  padding: 1px 0;
}

.emotion-3::after {
  height: 0.5px;
  bottom: -0.5px;
}

.emotion-3:hover,
.emotion-3:focus {
  text-shadow: 0 0 2px currentColor;
}

.emotion-3:active {
  color: #FFFFFF;
  background-color: #000000;
  border-color: #000000;
  -webkit-text-decoration: none;
  text-decoration: none;
  text-shadow: none;
}

.emotion-3:first-of-type {
  margin-left: 0!important;
}

.emotion-3>span {
  height: 100%;
}

.emotion-3:hover {
  color: #000000;
  background-color: #FFFFFF;
  border-color: #00FF00;
}

.emotion-3:active {
  color: #000000;
  background-color: #FFFFFF;
  border-color: #00FF00;
}

.emotion-3:focus {
  color: #000000;
  background-color: #FFFFFF;
  border-color: #00FF00;
}

<div>
  <div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <div
        class="emotion-0"
      >
        <div
          class="emotion-1"
        >
          <div
            class="emotion-2"
          >
            <a
              class="emotion-3"
              href="https://www.google.com"
            >
              Yoga & Studio
            </a>
            <a
              class="emotion-3"
              href="#jump-to-bottoms"
              target="_self"
            >
              Travel * Commute
            </a>
            <a
              class="emotion-3"
              href="https://www.apple.com"
            >
              Test
            </a>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`Text Navigation AT content type customized button styles desktop shows styles for TextNavDropdown 1`] = `
.emotion-0 {
  margin-left: auto;
  margin-right: auto;
  max-width: 1280px;
}

.emotion-1 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  min-height: 50px;
}

.emotion-2 {
  -webkit-flex: 1;
  -ms-flex: 1;
  flex: 1;
  gap: 0;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-2>button,
.emotion-2>a {
  min-height: 50px;
  -webkit-flex: 1 1 auto;
  -ms-flex: 1 1 auto;
  flex: 1 1 auto;
}

.emotion-2>button:not(:first-of-type),
.emotion-2>a:not(:first-of-type) {
  border-left-color: transparent;
}

.emotion-3 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: none;
  transition: none;
  box-sizing: border-box;
  border: 1.5px solid transparent;
  border-radius: 0;
  font-size: 16px;
  font-weight: 500;
  letter-spacing: 0px;
  min-height: auto;
  max-height: 60px;
  line-height: 1.25;
  padding: 22px 40px;
  width: auto;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  background-color: #FFFFFF;
  color: #000000;
  border-color: #000000;
  border-width: 0.5px;
  box-sizing: border-box;
  margin: 0;
  border-radius: 0;
  padding: 9px 14px;
  line-height: 1.1176470588235294;
  letter-spacing: 0px;
  min-height: 56px;
  color: #FFFFFF;
  background-color: #000000;
  border-color: #FF0000;
}

.emotion-3:focus {
  outline: none;
}

.emotion-3>span {
  padding: 1px 0;
}

.emotion-3::after {
  height: 0.5px;
  bottom: -0.5px;
}

.emotion-3:hover,
.emotion-3:focus {
  text-shadow: 0 0 2px currentColor;
}

.emotion-3:active {
  color: #FFFFFF;
  background-color: #000000;
  border-color: #000000;
  -webkit-text-decoration: none;
  text-decoration: none;
  text-shadow: none;
}

.emotion-3:first-of-type {
  margin-left: 0!important;
}

.emotion-3>span {
  height: 100%;
}

.emotion-3:hover {
  color: #000000;
  background-color: #FFFFFF;
  border-color: #00FF00;
}

.emotion-3:active {
  color: #000000;
  background-color: #FFFFFF;
  border-color: #00FF00;
}

.emotion-3:focus {
  color: #000000;
  background-color: #FFFFFF;
  border-color: #00FF00;
}

<div>
  <div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <div
        class="emotion-0"
      >
        <div
          class="emotion-1"
        >
          <div
            class="emotion-2"
          >
            <a
              class="emotion-3"
              href="https://www.google.com"
            >
              Yoga & Studio
            </a>
            <a
              class="emotion-3"
              href="#jump-to-bottoms"
              target="_self"
            >
              Travel * Commute
            </a>
            <a
              class="emotion-3"
              href="https://www.apple.com"
            >
              Test
            </a>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`Text Navigation AT content type customized button styles desktop shows styles for TextNavExposed 1`] = `
.emotion-0 {
  margin-left: auto;
  margin-right: auto;
  max-width: 1280px;
}

.emotion-1 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  min-height: 50px;
}

.emotion-2 {
  -webkit-flex: 1;
  -ms-flex: 1;
  flex: 1;
  gap: 0;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-2>button,
.emotion-2>a {
  min-height: 50px;
  -webkit-flex: 1 1 auto;
  -ms-flex: 1 1 auto;
  flex: 1 1 auto;
}

.emotion-2>button:not(:first-of-type),
.emotion-2>a:not(:first-of-type) {
  border-left-color: transparent;
}

.emotion-3 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: none;
  transition: none;
  box-sizing: border-box;
  border: 1.5px solid transparent;
  border-radius: 0;
  font-size: 16px;
  font-weight: 500;
  letter-spacing: 0px;
  min-height: auto;
  max-height: 60px;
  line-height: 1.25;
  padding: 22px 40px;
  width: auto;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  background-color: #FFFFFF;
  color: #000000;
  border-color: #000000;
  border-width: 0.5px;
  box-sizing: border-box;
  margin: 0;
  border-radius: 0;
  padding: 9px 14px;
  line-height: 1.1176470588235294;
  letter-spacing: 0px;
  min-height: 56px;
  color: #FFFFFF;
  background-color: #000000;
  border-color: #FF0000;
}

.emotion-3:focus {
  outline: none;
}

.emotion-3>span {
  padding: 1px 0;
}

.emotion-3::after {
  height: 0.5px;
  bottom: -0.5px;
}

.emotion-3:hover,
.emotion-3:focus {
  text-shadow: 0 0 2px currentColor;
}

.emotion-3:active {
  color: #FFFFFF;
  background-color: #000000;
  border-color: #000000;
  -webkit-text-decoration: none;
  text-decoration: none;
  text-shadow: none;
}

.emotion-3:first-of-type {
  margin-left: 0!important;
}

.emotion-3>span {
  height: 100%;
}

.emotion-3:hover {
  color: #000000;
  background-color: #FFFFFF;
  border-color: #00FF00;
}

.emotion-3:active {
  color: #000000;
  background-color: #FFFFFF;
  border-color: #00FF00;
}

.emotion-3:focus {
  color: #000000;
  background-color: #FFFFFF;
  border-color: #00FF00;
}

<div>
  <div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <div
        class="emotion-0"
      >
        <div
          class="emotion-1"
        >
          <div
            class="emotion-2"
          >
            <a
              class="emotion-3"
              href="https://www.google.com"
            >
              Yoga & Studio
            </a>
            <a
              class="emotion-3"
              href="#jump-to-bottoms"
              target="_self"
            >
              Travel * Commute
            </a>
            <a
              class="emotion-3"
              href="https://www.apple.com"
            >
              Test
            </a>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`Text Navigation AT content type customized button styles mobile shows styles for TextNavCarousel 1`] = `
.emotion-0 {
  margin-left: auto;
  margin-right: auto;
  max-width: 1280px;
}

.emotion-1 {
  position: relative;
  letter-spacing: 0;
}

.emotion-1 .slick-slider {
  position: relative;
  display: block;
  box-sizing: border-box;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  -webkit-touch-callout: none;
  touch-action: pan-y;
  -webkit-tap-highlight-color: transparent;
  scroll-behavior: smooth;
}

.emotion-1 .slick-slider .slick-track,
.emotion-1 .slick-slider .slick-list {
  -webkit-transform: translate3d(0, 0, 0);
  -moz-transform: translate3d(0, 0, 0);
  -ms-transform: translate3d(0, 0, 0);
  transform: translate3d(0, 0, 0);
}

.emotion-1 .slick-list {
  position: relative;
  display: block;
  overflow-x: hidden;
  overflow-y: hidden;
  margin: 0;
  padding: 0;
}

.emotion-1 .slick-list:focus {
  outline: none;
}

.emotion-1 .slick-list.dragging {
  cursor: pointer;
  cursor: hand;
}

.emotion-1 .slick-track {
  position: relative;
  top: 0;
  left: 0;
  display: block;
  margin-left: auto;
  margin-right: auto;
}

.emotion-1 .slick-track:before,
.emotion-1 .slick-track:after {
  display: table;
  content: "";
}

.emotion-1 .slick-track:after {
  clear: both;
}

.emotion-1 .slick-slide {
  display: none;
  float: left;
  height: 100%;
  min-height: 1px;
}

.emotion-1 .slick-slide img {
  display: block;
}

.emotion-1 .slick-slide.slick-loading img {
  display: none;
}

.emotion-1 .slick-slide.dragging img {
  pointer-events: none;
}

.emotion-1 [dir="rtl"] .slick-slide {
  float: right;
}

.emotion-1 .slick-initialized .slick-slide,
.emotion-1 .slick-vertical .slick-slide {
  display: block;
}

.emotion-1 .slick-vertical .slick-slide {
  border: 1px solid transparent;
  height: auto;
}

.emotion-1 .slick-loading .slick-track,
.emotion-1 .slick-loading .slick-slide {
  visibility: hidden;
}

.emotion-1 .slick-loading .slick-list {
  background: #fff url("./ajax-loader.gif") center center no-repeat;
}

.emotion-1 .slick-arrow.slick-hidden {
  display: none;
}

.emotion-1 .slick-prev,
.emotion-1 .slick-next {
  font-size: 0;
  line-height: 0;
  position: absolute;
  top: 50%;
  display: block;
  padding: 0;
  cursor: pointer;
  color: transparent;
  border: none;
  outline: none;
  background: transparent;
}

.emotion-1 .slick-prev:hover,
.emotion-1 .slick-next:hover,
.emotion-1 .slick-prev:focus,
.emotion-1 .slick-next:focus {
  color: transparent;
  outline: none;
  background: transparent;
  opacity: 1;
}

.emotion-1 .slick-prev.slick-disabled,
.emotion-1 .slick-next.slick-disabled {
  opacity: 0.25;
}

.emotion-1 .slick-prev {
  left: -25px;
  -webkit-transform: translate(0, -50%) rotate(90deg);
  -moz-transform: translate(0, -50%) rotate(90deg);
  -ms-transform: translate(0, -50%) rotate(90deg);
  transform: translate(0, -50%) rotate(90deg);
}

.emotion-1 .slick-prev span {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-1 [dir="rtl"] .slick-prev {
  right: -25px;
  left: auto;
}

.emotion-1 .slick-next {
  right: -25px;
  -webkit-transform: translate(0, -50%) rotate(-90deg);
  -moz-transform: translate(0, -50%) rotate(-90deg);
  -ms-transform: translate(0, -50%) rotate(-90deg);
  transform: translate(0, -50%) rotate(-90deg);
}

.emotion-1 .slick-next span {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-1 [dir="rtl"] .slick-next {
  right: auto;
  left: -25px;
}

.emotion-1 .slick-dotted.slick-slider {
  margin-bottom: 30px;
}

.emotion-1 .slick-dots {
  position: absolute;
  bottom: -25px;
  display: block;
  width: 100%;
  padding: 0;
  margin: 0;
  list-style: none;
  text-align: center;
}

.emotion-1 .slick-dots li {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: 20px;
  height: 20px;
  margin: 0 5px;
  padding: 0;
  cursor: pointer;
}

.emotion-1 .slick-dots li.slick-active button:before {
  opacity: 0.75;
  color: black;
}

.emotion-1 .slick-dots li button {
  font-size: 0;
  line-height: 0;
  display: block;
  padding: 5px;
  cursor: pointer;
  color: transparent;
  border: 0;
  outline: none;
  background: transparent;
}

.emotion-1 .slick-dots li button:hover,
.emotion-1 .slick-dots li button:focus {
  outline: none;
}

.emotion-1 .slick-dots li button:hover:before,
.emotion-1 .slick-dots li button:focus:before,
.emotion-1 .slick-dots li button:hover:before,
.emotion-1 .slick-dots li button:focus:before {
  opacity: 1;
}

.emotion-1 .slick-dots li button:before {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 5px;
  height: 5px;
  content: "";
  opacity: 0.25;
  background-color: black;
  border-radius: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-1 .slick-track {
  width: -webkit-max-content!important;
  width: -moz-max-content!important;
  width: max-content!important;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-1 .slick-slide {
  width: auto!important;
  height: auto;
  float: none;
  padding-right: 0px;
  box-sizing: border-box;
}

.emotion-1 .slick-slide>div,
.emotion-1 .slick-slide>div>* {
  height: 100%;
  width: 100%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  min-height: 50px;
}

.emotion-1 .slick-arrow span {
  box-sizing: border-box;
  height: 44px;
  width: 44px;
  padding: 16px 13px;
}

.emotion-1 .slick-prev {
  left: -5px;
}

.emotion-1 .slick-next {
  right: -5px;
}

.emotion-1 .slick-list {
  margin: 0 44px;
}

.emotion-1 .slick-disabled {
  visibility: hidden;
}

.emotion-1 *.focus-visible {
  box-shadow: 0 0 0 3px #5cabf7;
}

.emotion-1 .slick-slide:not(:first-of-type):not(:nth-of-type(1)):not(:last-of-type) a,
.emotion-1 .slick-slide:not(:first-of-type):not(:nth-of-type(1)):not(:last-of-type) button {
  border-right: 0;
  border-radius: 0;
}

.emotion-1 .slick-slide:nth-of-type(1) a,
.emotion-1 .slick-slide:nth-of-type(1) button {
  border-bottom-right-radius: 0;
  border-top-right-radius: 0;
  border-right: 0;
}

.emotion-1 .slick-slide:last-of-type a,
.emotion-1 .slick-slide:last-of-type button {
  border-bottom-left-radius: 0;
  border-top-left-radius: 0;
}

.emotion-2 {
  position: relative;
  height: 44px;
}

.emotion-2:hover {
  cursor: initial;
}

.emotion-3 {
  display: inline-block;
  height: 2rem;
  width: 2rem;
  min-height: 2rem;
  min-width: 2rem;
}

.emotion-3 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-4 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: none;
  transition: none;
  box-sizing: border-box;
  border: 1.5px solid transparent;
  border-radius: 0;
  font-size: 16px;
  font-weight: 500;
  letter-spacing: 0px;
  min-height: auto;
  max-height: 60px;
  line-height: 1.25;
  padding: 22px 40px;
  width: auto;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  background-color: #FFFFFF;
  color: #000000;
  border-color: #000000;
  border-width: 0.5px;
  width: 100%;
  display: inline-block;
  box-sizing: border-box;
  margin: 0;
  border-radius: 0;
  padding: 9px 14px;
  line-height: 1.1176470588235294;
  letter-spacing: 0px;
  min-height: 56px;
  color: #FFFFFF;
  background-color: pink;
  border-color: blue;
}

.emotion-4:focus {
  outline: none;
}

.emotion-4>span {
  padding: 1px 0;
}

.emotion-4::after {
  height: 0.5px;
  bottom: -0.5px;
}

.emotion-4:hover,
.emotion-4:focus {
  text-shadow: 0 0 2px currentColor;
}

.emotion-4:active {
  color: #FFFFFF;
  background-color: #000000;
  border-color: #000000;
  -webkit-text-decoration: none;
  text-decoration: none;
  text-shadow: none;
}

.emotion-4:first-of-type {
  margin-left: 0!important;
}

.emotion-4>span {
  height: 100%;
}

.emotion-7 {
  position: relative;
  height: 44px;
}

<div>
  <div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <div
        class="emotion-0"
      >
        <div>
          <div
            class="emotion-1"
          >
            <div
              class="slick-slider slick-initialized"
              dir="ltr"
            >
              <button
                aria-label="View previous of 3 slides"
                class="slick-arrow slick-prev slick-disabled emotion-2"
                data-role="none"
                disabled=""
                style="display: block;"
                type="button"
              >
                <span
                  aria-hidden="true"
                  class="emotion-3"
                >
                  <svg
                    viewBox="0 0 18 7.742"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M0,.839,6.44,9,0,17.162.818,18,7.743,9.225,7.522,9l.22-.225L.818,0Z"
                      fill="#333333"
                      transform="translate(18) rotate(90)"
                    />
                  </svg>
                </span>
              </button>
              <div
                class="slick-list"
              >
                <div
                  class="slick-track"
                  style="opacity: 1; transform: translate3d(0px, 0px, 0px);"
                >
                  <div
                    aria-hidden="false"
                    class="slick-slide slick-active slick-current"
                    data-index="0"
                    style="outline: none;"
                    tabindex="-1"
                  >
                    <div>
                      <a
                        class="emotion-4"
                        href="https://www.google.com"
                        tabindex="-1"
                      >
                        Yoga & Studio
                      </a>
                    </div>
                  </div>
                  <div
                    aria-hidden="false"
                    class="slick-slide slick-active"
                    data-index="1"
                    style="outline: none;"
                    tabindex="-1"
                  >
                    <div>
                      <a
                        class="emotion-4"
                        href="#jump-to-bottoms"
                        tabindex="-1"
                        target="_self"
                      >
                        Travel * Commute
                      </a>
                    </div>
                  </div>
                  <div
                    aria-hidden="true"
                    class="slick-slide"
                    data-index="2"
                    style="outline: none;"
                    tabindex="-1"
                  >
                    <div>
                      <a
                        class="emotion-4"
                        href="https://www.apple.com"
                        tabindex="-1"
                      >
                        Test
                      </a>
                    </div>
                  </div>
                </div>
              </div>
              <button
                aria-label="View next of 3 slides"
                class="slick-arrow slick-next emotion-7"
                data-role="none"
                style="display: block;"
                type="button"
              >
                <span
                  aria-hidden="true"
                  class="emotion-3"
                >
                  <svg
                    viewBox="0 0 18 7.742"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M0,.839,6.44,9,0,17.162.818,18,7.743,9.225,7.522,9l.22-.225L.818,0Z"
                      fill="#333333"
                      transform="translate(18) rotate(90)"
                    />
                  </svg>
                </span>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`Text Navigation AT content type customized button styles mobile shows styles for TextNavDropdown 1`] = `
.emotion-0 {
  margin-left: auto;
  margin-right: auto;
  max-width: 1280px;
}

.emotion-1 {
  font-size: 0.9735rem;
  width: 100%;
}

.emotion-2 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: none;
  transition: none;
  box-sizing: border-box;
  border: 1.5px solid transparent;
  border-radius: 0;
  font-size: 16px;
  font-weight: 500;
  letter-spacing: 0px;
  min-height: auto;
  max-height: 60px;
  line-height: 1.25;
  padding: 22px 40px;
  width: 100%;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #000000;
  background: transparent;
  border-color: #000000;
  border-width: 0.5px;
  box-sizing: border-box;
  margin: 0;
  border-radius: 0;
  padding: 9px 14px;
  line-height: 1.1176470588235294;
  letter-spacing: 0px;
  min-height: 56px;
  border: 1px solid #000000;
  font-weight: 500;
  fill: currentColor;
  background-color: pink;
  border-color: blue;
  color: #FFFFFF;
}

.emotion-2:focus {
  outline: none;
}

.emotion-2>span {
  padding: 1px 0;
}

.emotion-2::after {
  height: 0.5px;
  bottom: -0.5px;
}

.emotion-2:first-of-type {
  margin-left: 0!important;
}

.emotion-2>span {
  height: 100%;
}

.emotion-2 span {
  color: inherit;
  fill: inherit;
}

.emotion-2 span svg {
  fill: inherit;
}

.emotion-4 {
  display: inline-block;
  height: 10px;
  width: 10px;
  min-height: 10px;
  min-width: 10px;
  margin-left: 0.65rem;
  padding-top: 2.4px;
}

.emotion-4 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-4 svg path {
  fill: inherit;
}

.emotion-4 svg rect {
  fill: inherit;
}

.emotion-5 {
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
  background: #FFFFFF;
  box-sizing: border-box;
  list-style-type: none;
  min-width: 100%;
  overflow: hidden;
  position: relative;
  z-index: 390;
  border-width: 0;
  border-style: solid;
  text-align: left;
  letter-spacing: 0;
  pointer-events: auto;
  padding: 0;
  max-height: 0;
  -webkit-transition: max-height .5s ease-out,visibility 0s .5s;
  transition: max-height .5s ease-out,visibility 0s .5s;
  visibility: hidden;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  -webkit-box-flex-wrap: wrap;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  box-shadow: none;
  padding: 0;
  border-top-left-radius: 0;
  border-top-right-radius: 0;
  border-radius: none;
  z-index: 2;
  background-color: pink;
  border-color: blue;
  color: #FFFFFF;
}

.emotion-5 li {
  border: 1px solid #333333;
  box-sizing: border-box;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  width: 50%;
  border-top: 0;
}

.emotion-5 li:nth-of-type(1) {
  border-top-right-radius: inherit;
}

.emotion-5 li:nth-of-type(even) {
  border-left: none;
}

.emotion-5 li:nth-last-of-type(2):nth-of-type(odd) {
  border-bottom-left-radius: none;
}

.emotion-5 li:nth-last-of-type(1):nth-of-type(even) {
  border-bottom-right-radius: none;
}

.emotion-5 li:nth-last-of-type(1):nth-of-type(odd) {
  -webkit-flex: 1;
  -ms-flex: 1;
  flex: 1;
  border-width: 1px;
  border-top-width: 0;
  border-bottom-left-radius: none;
  border-bottom-right-radius: none;
}

.emotion-5>li {
  border-color: blue;
}

.emotion-5>li>a {
  color: #FFFFFF;
}

.emotion-6 {
  padding: 0;
}

.emotion-6>a {
  padding: 0 16px;
  min-height: 44px;
  font-size: 16px;
  line-height: 1.1176470588235294;
  padding: 10px 14px;
  min-height: 56px;
  font-weight: 500;
}

.emotion-7 {
  cursor: pointer;
  display: block;
  padding: 1.125rem 0;
  -webkit-text-decoration: none;
  text-decoration: none;
  -webkit-transition: color 0.3s ease;
  transition: color 0.3s ease;
  white-space: nowrap;
  font-size: 14px;
  text-align: center;
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  border: none;
  margin-left: auto;
  margin-right: auto;
  box-sizing: border-box;
  text-align: center;
  height: auto;
  white-space: normal;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  width: 100%;
}

.emotion-7:hover,
.emotion-7:focus {
  -webkit-text-decoration: underline;
  text-decoration: underline;
}

<div>
  <div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <div
        class="emotion-0"
      >
        <div
          class="emotion-1"
          data-testid="button-dropdown-container"
        >
          <button
            aria-expanded="false"
            class="emotion-2"
            color="primary"
          >
            <span
              class="emotion-3"
            />
            <span
              aria-hidden="true"
              class="emotion-4"
            >
              <svg
                fill="none"
                viewBox="0 0 12 12"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  clip-rule="evenodd"
                  d="M7 0H5v5H0v2h5v5h2V7h5V5H7V0z"
                  fill="#000000"
                  fill-rule="evenodd"
                />
              </svg>
            </span>
          </button>
          <ul
            aria-hidden="true"
            class="emotion-5"
          >
            <li
              class="emotion-6"
            >
              <a
                breakpoint="mobile"
                class="emotion-7"
                href="https://www.google.com"
                target="_self"
              >
                Yoga & Studio
              </a>
            </li>
            <li
              class="emotion-6"
            >
              <a
                breakpoint="mobile"
                class="emotion-7"
                href="#jump-to-bottoms"
                target="_self"
              >
                Travel * Commute
              </a>
            </li>
            <li
              class="emotion-6"
            >
              <a
                breakpoint="mobile"
                class="emotion-7"
                href="https://www.apple.com"
                target="_self"
              >
                Test
              </a>
            </li>
          </ul>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`Text Navigation AT content type customized button styles mobile shows styles for TextNavExposed 1`] = `
.emotion-0 {
  margin-left: auto;
  margin-right: auto;
  max-width: 1280px;
}

.emotion-2 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  -webkit-box-flex-wrap: wrap;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
}

.emotion-3 {
  box-sizing: border-box;
  width: 50%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-3:not(:last-of-type) button,
.emotion-3:not(:last-of-type) a {
  border-bottom: 0;
}

.emotion-3>a {
  border: 1px solid blue;
  border-bottom: none;
}

.emotion-3:nth-of-type(1)>a {
  border-top-left-radius: 0;
}

.emotion-3:nth-of-type(2)>a {
  border-top-right-radius: 0;
}

.emotion-3:nth-of-type(even)>a {
  border-left: none;
}

.emotion-3:nth-of-type(2n+1)>a {
  border-left: solid 1px blue;
}

.emotion-3:nth-last-of-type(2)>a {
  border-bottom: none;
  border-bottom-left-radius: 0;
}

.emotion-3:last-of-type {
  -webkit-flex: 1;
  -ms-flex: 1;
  flex: 1;
}

.emotion-3:last-of-type>a {
  border-bottom: 1px solid blue;
  border-bottom-right-radius: 0;
  border-bottom-left-radius: 0;
}

.emotion-4 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: none;
  transition: none;
  box-sizing: border-box;
  border: 1.5px solid transparent;
  border-radius: 0;
  font-size: 16px;
  font-weight: 500;
  letter-spacing: 0px;
  min-height: auto;
  max-height: 60px;
  line-height: 1.25;
  padding: 22px 40px;
  width: auto;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  background-color: #FFFFFF;
  color: #000000;
  border-color: #000000;
  box-sizing: border-box;
  margin: 0;
  border-radius: 0;
  padding: 9px 14px;
  line-height: 1.1176470588235294;
  letter-spacing: 0px;
  min-height: 56px;
  color: #FFFFFF;
  background-color: pink;
  border-color: blue;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  margin-left: auto;
  margin-right: auto;
  border-color: blue;
  box-sizing: border-box;
  text-align: center;
  height: auto;
  white-space: normal;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  width: 100%;
  border-radius: 0;
  font-size: 16px;
  padding: 10px 14px 9px;
  background-color: pink;
  border: 1px solid #333333;
  color: #FFFFFF;
}

.emotion-4:focus {
  outline: none;
}

.emotion-4>span {
  padding: 1px 0;
}

.emotion-4:first-of-type {
  margin-left: 0!important;
}

.emotion-4>span {
  height: 100%;
}

<div>
  <div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <div
        class="emotion-0"
      >
        <div
          class="emotion-1"
        >
          <nav
            aria-label="Categories"
          >
            <div
              class="emotion-2"
            >
              <div
                class="emotion-3"
              >
                <a
                  aria-hidden="false"
                  class="emotion-4"
                  href="https://www.google.com"
                  id="YogaStudio-0"
                  target="_self"
                  title="Yoga & Studio"
                >
                  Yoga & Studio
                </a>
              </div>
              <div
                class="emotion-3"
              >
                <a
                  aria-hidden="false"
                  class="emotion-4"
                  href="#jump-to-bottoms"
                  id="TravelCommute-1"
                  target="_self"
                  title="Travel * Commute"
                >
                  Travel * Commute
                </a>
              </div>
              <div
                class="emotion-3"
              >
                <a
                  aria-hidden="false"
                  class="emotion-4"
                  href="https://www.apple.com"
                  id="Test-2"
                  target="_self"
                  title="Test"
                >
                  Test
                </a>
              </div>
            </div>
          </nav>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`Text Navigation AT content type selected state shows selected option for TextNavCarousel 1`] = `
.emotion-0 {
  margin-left: auto;
  margin-right: auto;
  max-width: 1280px;
}

.emotion-1 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  min-height: 50px;
}

.emotion-2 {
  -webkit-flex: 1;
  -ms-flex: 1;
  flex: 1;
  gap: 0;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-2>button,
.emotion-2>a {
  min-height: 50px;
  -webkit-flex: 1 1 auto;
  -ms-flex: 1 1 auto;
  flex: 1 1 auto;
}

.emotion-2>button:not(:first-of-type),
.emotion-2>a:not(:first-of-type) {
  border-left-color: transparent;
}

.emotion-3 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: none;
  transition: none;
  box-sizing: border-box;
  border: 1.5px solid transparent;
  border-radius: 0;
  font-size: 16px;
  font-weight: 500;
  letter-spacing: 0px;
  min-height: auto;
  max-height: 60px;
  line-height: 1.25;
  padding: 22px 40px;
  width: auto;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  background-color: #FFFFFF;
  color: #000000;
  border-color: #000000;
  border-width: 0.5px;
  box-sizing: border-box;
  margin: 0;
  border-radius: 0;
  padding: 9px 14px;
  line-height: 1.1176470588235294;
  letter-spacing: 0px;
  min-height: 56px;
}

.emotion-3:focus {
  outline: none;
}

.emotion-3>span {
  padding: 1px 0;
}

.emotion-3::after {
  height: 0.5px;
  bottom: -0.5px;
}

.emotion-3:hover,
.emotion-3:focus {
  text-shadow: 0 0 2px currentColor;
}

.emotion-3:active {
  color: #FFFFFF;
  background-color: #000000;
  border-color: #000000;
  -webkit-text-decoration: none;
  text-decoration: none;
  text-shadow: none;
}

.emotion-3:first-of-type {
  margin-left: 0!important;
}

.emotion-3>span {
  height: 100%;
}

.emotion-4 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: none;
  transition: none;
  box-sizing: border-box;
  border: 1.5px solid transparent;
  border-radius: 0;
  font-size: 16px;
  font-weight: 500;
  letter-spacing: 0px;
  min-height: auto;
  max-height: 60px;
  line-height: 1.25;
  padding: 22px 40px;
  width: auto;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  background-color: #FFFFFF;
  color: #000000;
  border-color: #000000;
  border-width: 0.5px;
  color: #FFFFFF;
  background-color: #000000;
  border-color: #000000;
  -webkit-text-decoration: none;
  text-decoration: none;
  text-shadow: none;
  box-sizing: border-box;
  margin: 0;
  border-radius: 0;
  padding: 9px 14px;
  line-height: 1.1176470588235294;
  letter-spacing: 0px;
  min-height: 56px;
}

.emotion-4:focus {
  outline: none;
}

.emotion-4>span {
  padding: 1px 0;
}

.emotion-4::after {
  height: 0.5px;
  bottom: -0.5px;
}

.emotion-4:hover,
.emotion-4:focus {
  text-shadow: 0 0 2px currentColor;
}

.emotion-4:active {
  color: #FFFFFF;
  background-color: #000000;
  border-color: #000000;
  -webkit-text-decoration: none;
  text-decoration: none;
  text-shadow: none;
}

.emotion-4:first-of-type {
  margin-left: 0!important;
}

.emotion-4>span {
  height: 100%;
}

<div>
  <div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <div
        class="emotion-0"
      >
        <div
          class="emotion-1"
        >
          <div
            class="emotion-2"
          >
            <a
              class="emotion-3"
              href="https://www.google.com"
            >
              Yoga & Studio
            </a>
            <a
              class="emotion-4"
              href="#jump-to-bottoms"
              target="_self"
            >
              Travel * Commute
            </a>
            <a
              class="emotion-3"
              href="https://www.apple.com"
            >
              Test
            </a>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`Text Navigation AT content type selected state shows selected option for TextNavDropdown 1`] = `
.emotion-0 {
  margin-left: auto;
  margin-right: auto;
  max-width: 1280px;
}

.emotion-1 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  min-height: 50px;
}

.emotion-2 {
  -webkit-flex: 1;
  -ms-flex: 1;
  flex: 1;
  gap: 0;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-2>button,
.emotion-2>a {
  min-height: 50px;
  -webkit-flex: 1 1 auto;
  -ms-flex: 1 1 auto;
  flex: 1 1 auto;
}

.emotion-2>button:not(:first-of-type),
.emotion-2>a:not(:first-of-type) {
  border-left-color: transparent;
}

.emotion-3 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: none;
  transition: none;
  box-sizing: border-box;
  border: 1.5px solid transparent;
  border-radius: 0;
  font-size: 16px;
  font-weight: 500;
  letter-spacing: 0px;
  min-height: auto;
  max-height: 60px;
  line-height: 1.25;
  padding: 22px 40px;
  width: auto;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  background-color: #FFFFFF;
  color: #000000;
  border-color: #000000;
  border-width: 0.5px;
  box-sizing: border-box;
  margin: 0;
  border-radius: 0;
  padding: 9px 14px;
  line-height: 1.1176470588235294;
  letter-spacing: 0px;
  min-height: 56px;
}

.emotion-3:focus {
  outline: none;
}

.emotion-3>span {
  padding: 1px 0;
}

.emotion-3::after {
  height: 0.5px;
  bottom: -0.5px;
}

.emotion-3:hover,
.emotion-3:focus {
  text-shadow: 0 0 2px currentColor;
}

.emotion-3:active {
  color: #FFFFFF;
  background-color: #000000;
  border-color: #000000;
  -webkit-text-decoration: none;
  text-decoration: none;
  text-shadow: none;
}

.emotion-3:first-of-type {
  margin-left: 0!important;
}

.emotion-3>span {
  height: 100%;
}

.emotion-4 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: none;
  transition: none;
  box-sizing: border-box;
  border: 1.5px solid transparent;
  border-radius: 0;
  font-size: 16px;
  font-weight: 500;
  letter-spacing: 0px;
  min-height: auto;
  max-height: 60px;
  line-height: 1.25;
  padding: 22px 40px;
  width: auto;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  background-color: #FFFFFF;
  color: #000000;
  border-color: #000000;
  border-width: 0.5px;
  color: #FFFFFF;
  background-color: #000000;
  border-color: #000000;
  -webkit-text-decoration: none;
  text-decoration: none;
  text-shadow: none;
  box-sizing: border-box;
  margin: 0;
  border-radius: 0;
  padding: 9px 14px;
  line-height: 1.1176470588235294;
  letter-spacing: 0px;
  min-height: 56px;
}

.emotion-4:focus {
  outline: none;
}

.emotion-4>span {
  padding: 1px 0;
}

.emotion-4::after {
  height: 0.5px;
  bottom: -0.5px;
}

.emotion-4:hover,
.emotion-4:focus {
  text-shadow: 0 0 2px currentColor;
}

.emotion-4:active {
  color: #FFFFFF;
  background-color: #000000;
  border-color: #000000;
  -webkit-text-decoration: none;
  text-decoration: none;
  text-shadow: none;
}

.emotion-4:first-of-type {
  margin-left: 0!important;
}

.emotion-4>span {
  height: 100%;
}

<div>
  <div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <div
        class="emotion-0"
      >
        <div
          class="emotion-1"
        >
          <div
            class="emotion-2"
          >
            <a
              class="emotion-3"
              href="https://www.google.com"
            >
              Yoga & Studio
            </a>
            <a
              class="emotion-4"
              href="#jump-to-bottoms"
              target="_self"
            >
              Travel * Commute
            </a>
            <a
              class="emotion-3"
              href="https://www.apple.com"
            >
              Test
            </a>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`Text Navigation AT content type selected state shows selected option for TextNavExposed 1`] = `
.emotion-0 {
  margin-left: auto;
  margin-right: auto;
  max-width: 1280px;
}

.emotion-1 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  min-height: 50px;
}

.emotion-2 {
  -webkit-flex: 1;
  -ms-flex: 1;
  flex: 1;
  gap: 0;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-2>button,
.emotion-2>a {
  min-height: 50px;
  -webkit-flex: 1 1 auto;
  -ms-flex: 1 1 auto;
  flex: 1 1 auto;
}

.emotion-2>button:not(:first-of-type),
.emotion-2>a:not(:first-of-type) {
  border-left-color: transparent;
}

.emotion-3 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: none;
  transition: none;
  box-sizing: border-box;
  border: 1.5px solid transparent;
  border-radius: 0;
  font-size: 16px;
  font-weight: 500;
  letter-spacing: 0px;
  min-height: auto;
  max-height: 60px;
  line-height: 1.25;
  padding: 22px 40px;
  width: auto;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  background-color: #FFFFFF;
  color: #000000;
  border-color: #000000;
  border-width: 0.5px;
  box-sizing: border-box;
  margin: 0;
  border-radius: 0;
  padding: 9px 14px;
  line-height: 1.1176470588235294;
  letter-spacing: 0px;
  min-height: 56px;
}

.emotion-3:focus {
  outline: none;
}

.emotion-3>span {
  padding: 1px 0;
}

.emotion-3::after {
  height: 0.5px;
  bottom: -0.5px;
}

.emotion-3:hover,
.emotion-3:focus {
  text-shadow: 0 0 2px currentColor;
}

.emotion-3:active {
  color: #FFFFFF;
  background-color: #000000;
  border-color: #000000;
  -webkit-text-decoration: none;
  text-decoration: none;
  text-shadow: none;
}

.emotion-3:first-of-type {
  margin-left: 0!important;
}

.emotion-3>span {
  height: 100%;
}

.emotion-4 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: none;
  transition: none;
  box-sizing: border-box;
  border: 1.5px solid transparent;
  border-radius: 0;
  font-size: 16px;
  font-weight: 500;
  letter-spacing: 0px;
  min-height: auto;
  max-height: 60px;
  line-height: 1.25;
  padding: 22px 40px;
  width: auto;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  background-color: #FFFFFF;
  color: #000000;
  border-color: #000000;
  border-width: 0.5px;
  color: #FFFFFF;
  background-color: #000000;
  border-color: #000000;
  -webkit-text-decoration: none;
  text-decoration: none;
  text-shadow: none;
  box-sizing: border-box;
  margin: 0;
  border-radius: 0;
  padding: 9px 14px;
  line-height: 1.1176470588235294;
  letter-spacing: 0px;
  min-height: 56px;
}

.emotion-4:focus {
  outline: none;
}

.emotion-4>span {
  padding: 1px 0;
}

.emotion-4::after {
  height: 0.5px;
  bottom: -0.5px;
}

.emotion-4:hover,
.emotion-4:focus {
  text-shadow: 0 0 2px currentColor;
}

.emotion-4:active {
  color: #FFFFFF;
  background-color: #000000;
  border-color: #000000;
  -webkit-text-decoration: none;
  text-decoration: none;
  text-shadow: none;
}

.emotion-4:first-of-type {
  margin-left: 0!important;
}

.emotion-4>span {
  height: 100%;
}

<div>
  <div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <div
        class="emotion-0"
      >
        <div
          class="emotion-1"
        >
          <div
            class="emotion-2"
          >
            <a
              class="emotion-3"
              href="https://www.google.com"
            >
              Yoga & Studio
            </a>
            <a
              class="emotion-4"
              href="#jump-to-bottoms"
              target="_self"
            >
              Travel * Commute
            </a>
            <a
              class="emotion-3"
              href="https://www.apple.com"
            >
              Test
            </a>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`Text Navigation AT content type snapshots should match desktop 1`] = `
.emotion-0 {
  margin-left: auto;
  margin-right: auto;
  max-width: 1280px;
}

.emotion-1 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  min-height: 50px;
}

.emotion-2 {
  -webkit-flex: 1;
  -ms-flex: 1;
  flex: 1;
  gap: 0;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-2>button,
.emotion-2>a {
  min-height: 50px;
  -webkit-flex: 1 1 auto;
  -ms-flex: 1 1 auto;
  flex: 1 1 auto;
}

.emotion-2>button:not(:first-of-type),
.emotion-2>a:not(:first-of-type) {
  border-left-color: transparent;
}

.emotion-3 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: none;
  transition: none;
  box-sizing: border-box;
  border: 1.5px solid transparent;
  border-radius: 0;
  font-size: 16px;
  font-weight: 500;
  letter-spacing: 0px;
  min-height: auto;
  max-height: 60px;
  line-height: 1.25;
  padding: 22px 40px;
  width: auto;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  background-color: #FFFFFF;
  color: #000000;
  border-color: #000000;
  border-width: 0.5px;
  box-sizing: border-box;
  margin: 0;
  border-radius: 0;
  padding: 9px 14px;
  line-height: 1.1176470588235294;
  letter-spacing: 0px;
  min-height: 56px;
}

.emotion-3:focus {
  outline: none;
}

.emotion-3>span {
  padding: 1px 0;
}

.emotion-3::after {
  height: 0.5px;
  bottom: -0.5px;
}

.emotion-3:hover,
.emotion-3:focus {
  text-shadow: 0 0 2px currentColor;
}

.emotion-3:active {
  color: #FFFFFF;
  background-color: #000000;
  border-color: #000000;
  -webkit-text-decoration: none;
  text-decoration: none;
  text-shadow: none;
}

.emotion-3:first-of-type {
  margin-left: 0!important;
}

.emotion-3>span {
  height: 100%;
}

<div>
  <div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <div
        class="emotion-0"
      >
        <div
          class="emotion-1"
        >
          <div
            class="emotion-2"
          >
            <a
              class="emotion-3"
              href="https://www.google.com"
            >
              Yoga & Studio
            </a>
            <a
              class="emotion-3"
              href="#jump-to-bottoms"
              target="_self"
            >
              Travel * Commute
            </a>
            <a
              class="emotion-3"
              href="https://www.apple.com"
            >
              Test
            </a>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`Text Navigation AT content type snapshots should match mobile carousel 1`] = `
.emotion-0 {
  margin-left: auto;
  margin-right: auto;
  max-width: 1280px;
}

.emotion-1 {
  position: relative;
  letter-spacing: 0;
}

.emotion-1 .slick-slider {
  position: relative;
  display: block;
  box-sizing: border-box;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  -webkit-touch-callout: none;
  touch-action: pan-y;
  -webkit-tap-highlight-color: transparent;
  scroll-behavior: smooth;
}

.emotion-1 .slick-slider .slick-track,
.emotion-1 .slick-slider .slick-list {
  -webkit-transform: translate3d(0, 0, 0);
  -moz-transform: translate3d(0, 0, 0);
  -ms-transform: translate3d(0, 0, 0);
  transform: translate3d(0, 0, 0);
}

.emotion-1 .slick-list {
  position: relative;
  display: block;
  overflow-x: hidden;
  overflow-y: hidden;
  margin: 0;
  padding: 0;
}

.emotion-1 .slick-list:focus {
  outline: none;
}

.emotion-1 .slick-list.dragging {
  cursor: pointer;
  cursor: hand;
}

.emotion-1 .slick-track {
  position: relative;
  top: 0;
  left: 0;
  display: block;
  margin-left: auto;
  margin-right: auto;
}

.emotion-1 .slick-track:before,
.emotion-1 .slick-track:after {
  display: table;
  content: "";
}

.emotion-1 .slick-track:after {
  clear: both;
}

.emotion-1 .slick-slide {
  display: none;
  float: left;
  height: 100%;
  min-height: 1px;
}

.emotion-1 .slick-slide img {
  display: block;
}

.emotion-1 .slick-slide.slick-loading img {
  display: none;
}

.emotion-1 .slick-slide.dragging img {
  pointer-events: none;
}

.emotion-1 [dir="rtl"] .slick-slide {
  float: right;
}

.emotion-1 .slick-initialized .slick-slide,
.emotion-1 .slick-vertical .slick-slide {
  display: block;
}

.emotion-1 .slick-vertical .slick-slide {
  border: 1px solid transparent;
  height: auto;
}

.emotion-1 .slick-loading .slick-track,
.emotion-1 .slick-loading .slick-slide {
  visibility: hidden;
}

.emotion-1 .slick-loading .slick-list {
  background: #fff url("./ajax-loader.gif") center center no-repeat;
}

.emotion-1 .slick-arrow.slick-hidden {
  display: none;
}

.emotion-1 .slick-prev,
.emotion-1 .slick-next {
  font-size: 0;
  line-height: 0;
  position: absolute;
  top: 50%;
  display: block;
  padding: 0;
  cursor: pointer;
  color: transparent;
  border: none;
  outline: none;
  background: transparent;
}

.emotion-1 .slick-prev:hover,
.emotion-1 .slick-next:hover,
.emotion-1 .slick-prev:focus,
.emotion-1 .slick-next:focus {
  color: transparent;
  outline: none;
  background: transparent;
  opacity: 1;
}

.emotion-1 .slick-prev.slick-disabled,
.emotion-1 .slick-next.slick-disabled {
  opacity: 0.25;
}

.emotion-1 .slick-prev {
  left: -25px;
  -webkit-transform: translate(0, -50%) rotate(90deg);
  -moz-transform: translate(0, -50%) rotate(90deg);
  -ms-transform: translate(0, -50%) rotate(90deg);
  transform: translate(0, -50%) rotate(90deg);
}

.emotion-1 .slick-prev span {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-1 [dir="rtl"] .slick-prev {
  right: -25px;
  left: auto;
}

.emotion-1 .slick-next {
  right: -25px;
  -webkit-transform: translate(0, -50%) rotate(-90deg);
  -moz-transform: translate(0, -50%) rotate(-90deg);
  -ms-transform: translate(0, -50%) rotate(-90deg);
  transform: translate(0, -50%) rotate(-90deg);
}

.emotion-1 .slick-next span {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-1 [dir="rtl"] .slick-next {
  right: auto;
  left: -25px;
}

.emotion-1 .slick-dotted.slick-slider {
  margin-bottom: 30px;
}

.emotion-1 .slick-dots {
  position: absolute;
  bottom: -25px;
  display: block;
  width: 100%;
  padding: 0;
  margin: 0;
  list-style: none;
  text-align: center;
}

.emotion-1 .slick-dots li {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: 20px;
  height: 20px;
  margin: 0 5px;
  padding: 0;
  cursor: pointer;
}

.emotion-1 .slick-dots li.slick-active button:before {
  opacity: 0.75;
  color: black;
}

.emotion-1 .slick-dots li button {
  font-size: 0;
  line-height: 0;
  display: block;
  padding: 5px;
  cursor: pointer;
  color: transparent;
  border: 0;
  outline: none;
  background: transparent;
}

.emotion-1 .slick-dots li button:hover,
.emotion-1 .slick-dots li button:focus {
  outline: none;
}

.emotion-1 .slick-dots li button:hover:before,
.emotion-1 .slick-dots li button:focus:before,
.emotion-1 .slick-dots li button:hover:before,
.emotion-1 .slick-dots li button:focus:before {
  opacity: 1;
}

.emotion-1 .slick-dots li button:before {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 5px;
  height: 5px;
  content: "";
  opacity: 0.25;
  background-color: black;
  border-radius: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-1 .slick-track {
  width: -webkit-max-content!important;
  width: -moz-max-content!important;
  width: max-content!important;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-1 .slick-slide {
  width: auto!important;
  height: auto;
  float: none;
  padding-right: 0px;
  box-sizing: border-box;
}

.emotion-1 .slick-slide>div,
.emotion-1 .slick-slide>div>* {
  height: 100%;
  width: 100%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  min-height: 50px;
}

.emotion-1 .slick-arrow span {
  box-sizing: border-box;
  height: 44px;
  width: 44px;
  padding: 16px 13px;
}

.emotion-1 .slick-prev {
  left: -5px;
}

.emotion-1 .slick-next {
  right: -5px;
}

.emotion-1 .slick-list {
  margin: 0 44px;
}

.emotion-1 .slick-disabled {
  visibility: hidden;
}

.emotion-1 *.focus-visible {
  box-shadow: 0 0 0 3px #5cabf7;
}

.emotion-1 .slick-slide:not(:first-of-type):not(:nth-of-type(1)):not(:last-of-type) a,
.emotion-1 .slick-slide:not(:first-of-type):not(:nth-of-type(1)):not(:last-of-type) button {
  border-right: 0;
  border-radius: 0;
}

.emotion-1 .slick-slide:nth-of-type(1) a,
.emotion-1 .slick-slide:nth-of-type(1) button {
  border-bottom-right-radius: 0;
  border-top-right-radius: 0;
  border-right: 0;
}

.emotion-1 .slick-slide:last-of-type a,
.emotion-1 .slick-slide:last-of-type button {
  border-bottom-left-radius: 0;
  border-top-left-radius: 0;
}

.emotion-2 {
  position: relative;
  height: 44px;
}

.emotion-2:hover {
  cursor: initial;
}

.emotion-3 {
  display: inline-block;
  height: 2rem;
  width: 2rem;
  min-height: 2rem;
  min-width: 2rem;
}

.emotion-3 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-4 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: none;
  transition: none;
  box-sizing: border-box;
  border: 1.5px solid transparent;
  border-radius: 0;
  font-size: 16px;
  font-weight: 500;
  letter-spacing: 0px;
  min-height: auto;
  max-height: 60px;
  line-height: 1.25;
  padding: 22px 40px;
  width: auto;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  background-color: #FFFFFF;
  color: #000000;
  border-color: #000000;
  border-width: 0.5px;
  width: 100%;
  display: inline-block;
  box-sizing: border-box;
  margin: 0;
  border-radius: 0;
  padding: 9px 14px;
  line-height: 1.1176470588235294;
  letter-spacing: 0px;
  min-height: 56px;
}

.emotion-4:focus {
  outline: none;
}

.emotion-4>span {
  padding: 1px 0;
}

.emotion-4::after {
  height: 0.5px;
  bottom: -0.5px;
}

.emotion-4:hover,
.emotion-4:focus {
  text-shadow: 0 0 2px currentColor;
}

.emotion-4:active {
  color: #FFFFFF;
  background-color: #000000;
  border-color: #000000;
  -webkit-text-decoration: none;
  text-decoration: none;
  text-shadow: none;
}

.emotion-4:first-of-type {
  margin-left: 0!important;
}

.emotion-4>span {
  height: 100%;
}

.emotion-7 {
  position: relative;
  height: 44px;
}

<div>
  <div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <div
        class="emotion-0"
      >
        <div>
          <div
            class="emotion-1"
          >
            <div
              class="slick-slider slick-initialized"
              dir="ltr"
            >
              <button
                aria-label="View previous of 3 slides"
                class="slick-arrow slick-prev slick-disabled emotion-2"
                data-role="none"
                disabled=""
                style="display: block;"
                type="button"
              >
                <span
                  aria-hidden="true"
                  class="emotion-3"
                >
                  <svg
                    viewBox="0 0 18 7.742"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M0,.839,6.44,9,0,17.162.818,18,7.743,9.225,7.522,9l.22-.225L.818,0Z"
                      fill="#333333"
                      transform="translate(18) rotate(90)"
                    />
                  </svg>
                </span>
              </button>
              <div
                class="slick-list"
              >
                <div
                  class="slick-track"
                  style="opacity: 1; transform: translate3d(0px, 0px, 0px);"
                >
                  <div
                    aria-hidden="false"
                    class="slick-slide slick-active slick-current"
                    data-index="0"
                    style="outline: none;"
                    tabindex="-1"
                  >
                    <div>
                      <a
                        class="emotion-4"
                        href="https://www.google.com"
                        tabindex="-1"
                      >
                        Yoga & Studio
                      </a>
                    </div>
                  </div>
                  <div
                    aria-hidden="false"
                    class="slick-slide slick-active"
                    data-index="1"
                    style="outline: none;"
                    tabindex="-1"
                  >
                    <div>
                      <a
                        class="emotion-4"
                        href="#jump-to-bottoms"
                        tabindex="-1"
                        target="_self"
                      >
                        Travel * Commute
                      </a>
                    </div>
                  </div>
                  <div
                    aria-hidden="true"
                    class="slick-slide"
                    data-index="2"
                    style="outline: none;"
                    tabindex="-1"
                  >
                    <div>
                      <a
                        class="emotion-4"
                        href="https://www.apple.com"
                        tabindex="-1"
                      >
                        Test
                      </a>
                    </div>
                  </div>
                </div>
              </div>
              <button
                aria-label="View next of 3 slides"
                class="slick-arrow slick-next emotion-7"
                data-role="none"
                style="display: block;"
                type="button"
              >
                <span
                  aria-hidden="true"
                  class="emotion-3"
                >
                  <svg
                    viewBox="0 0 18 7.742"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M0,.839,6.44,9,0,17.162.818,18,7.743,9.225,7.522,9l.22-.225L.818,0Z"
                      fill="#333333"
                      transform="translate(18) rotate(90)"
                    />
                  </svg>
                </span>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`Text Navigation AT content type snapshots should match mobile dropdown 1`] = `
.emotion-0 {
  margin-left: auto;
  margin-right: auto;
  max-width: 1280px;
}

.emotion-1 {
  font-size: 0.9735rem;
  width: 100%;
}

.emotion-2 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: none;
  transition: none;
  box-sizing: border-box;
  border: 1.5px solid transparent;
  border-radius: 0;
  font-size: 16px;
  font-weight: 500;
  letter-spacing: 0px;
  min-height: auto;
  max-height: 60px;
  line-height: 1.25;
  padding: 22px 40px;
  width: 100%;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #000000;
  background: transparent;
  border-color: #000000;
  border-width: 0.5px;
  box-sizing: border-box;
  margin: 0;
  border-radius: 0;
  padding: 9px 14px;
  line-height: 1.1176470588235294;
  letter-spacing: 0px;
  min-height: 56px;
  border: 1px solid #000000;
  font-weight: 500;
  fill: currentColor;
}

.emotion-2:focus {
  outline: none;
}

.emotion-2>span {
  padding: 1px 0;
}

.emotion-2::after {
  height: 0.5px;
  bottom: -0.5px;
}

.emotion-2:first-of-type {
  margin-left: 0!important;
}

.emotion-2>span {
  height: 100%;
}

.emotion-2 span {
  color: inherit;
  fill: inherit;
}

.emotion-2 span svg {
  fill: inherit;
}

.emotion-4 {
  display: inline-block;
  height: 10px;
  width: 10px;
  min-height: 10px;
  min-width: 10px;
  margin-left: 0.65rem;
  padding-top: 2.4px;
}

.emotion-4 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-4 svg path {
  fill: inherit;
}

.emotion-4 svg rect {
  fill: inherit;
}

.emotion-5 {
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
  background: #FFFFFF;
  box-sizing: border-box;
  list-style-type: none;
  min-width: 100%;
  overflow: hidden;
  position: relative;
  z-index: 390;
  border-width: 0;
  border-style: solid;
  text-align: left;
  letter-spacing: 0;
  pointer-events: auto;
  padding: 0;
  max-height: 0;
  -webkit-transition: max-height .5s ease-out,visibility 0s .5s;
  transition: max-height .5s ease-out,visibility 0s .5s;
  visibility: hidden;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  -webkit-box-flex-wrap: wrap;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  box-shadow: none;
  padding: 0;
  border-top-left-radius: 0;
  border-top-right-radius: 0;
  border-radius: none;
  z-index: 2;
}

.emotion-5 li {
  border: 1px solid #333333;
  box-sizing: border-box;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  width: 50%;
  border-top: 0;
}

.emotion-5 li:nth-of-type(1) {
  border-top-right-radius: inherit;
}

.emotion-5 li:nth-of-type(even) {
  border-left: none;
}

.emotion-5 li:nth-last-of-type(2):nth-of-type(odd) {
  border-bottom-left-radius: none;
}

.emotion-5 li:nth-last-of-type(1):nth-of-type(even) {
  border-bottom-right-radius: none;
}

.emotion-5 li:nth-last-of-type(1):nth-of-type(odd) {
  -webkit-flex: 1;
  -ms-flex: 1;
  flex: 1;
  border-width: 1px;
  border-top-width: 0;
  border-bottom-left-radius: none;
  border-bottom-right-radius: none;
}

.emotion-6 {
  padding: 0;
}

.emotion-6>a {
  padding: 0 16px;
  min-height: 44px;
  font-size: 16px;
  line-height: 1.1176470588235294;
  padding: 10px 14px;
  min-height: 56px;
  font-weight: 500;
}

.emotion-7 {
  cursor: pointer;
  display: block;
  padding: 1.125rem 0;
  -webkit-text-decoration: none;
  text-decoration: none;
  -webkit-transition: color 0.3s ease;
  transition: color 0.3s ease;
  white-space: nowrap;
  font-size: 14px;
  text-align: center;
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  border: none;
  margin-left: auto;
  margin-right: auto;
  box-sizing: border-box;
  text-align: center;
  height: auto;
  white-space: normal;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  width: 100%;
}

.emotion-7:hover,
.emotion-7:focus {
  -webkit-text-decoration: underline;
  text-decoration: underline;
}

<div>
  <div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <div
        class="emotion-0"
      >
        <div
          class="emotion-1"
          data-testid="button-dropdown-container"
        >
          <button
            aria-expanded="false"
            class="emotion-2"
            color="primary"
          >
            <span
              class="emotion-3"
            />
            <span
              aria-hidden="true"
              class="emotion-4"
            >
              <svg
                fill="none"
                viewBox="0 0 12 12"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  clip-rule="evenodd"
                  d="M7 0H5v5H0v2h5v5h2V7h5V5H7V0z"
                  fill="#000000"
                  fill-rule="evenodd"
                />
              </svg>
            </span>
          </button>
          <ul
            aria-hidden="true"
            class="emotion-5"
          >
            <li
              class="emotion-6"
            >
              <a
                breakpoint="mobile"
                class="emotion-7"
                href="https://www.google.com"
                target="_self"
              >
                Yoga & Studio
              </a>
            </li>
            <li
              class="emotion-6"
            >
              <a
                breakpoint="mobile"
                class="emotion-7"
                href="#jump-to-bottoms"
                target="_self"
              >
                Travel * Commute
              </a>
            </li>
            <li
              class="emotion-6"
            >
              <a
                breakpoint="mobile"
                class="emotion-7"
                href="https://www.apple.com"
                target="_self"
              >
                Test
              </a>
            </li>
          </ul>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`Text Navigation AT content type snapshots should match mobile exposed 1`] = `
.emotion-0 {
  margin-left: auto;
  margin-right: auto;
  max-width: 1280px;
}

.emotion-2 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  -webkit-box-flex-wrap: wrap;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
}

.emotion-3 {
  box-sizing: border-box;
  width: 50%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-3:not(:last-of-type) button,
.emotion-3:not(:last-of-type) a {
  border-bottom: 0;
}

.emotion-3>a {
  border: 1px solid #333333;
  border-bottom: none;
}

.emotion-3:nth-of-type(1)>a {
  border-top-left-radius: 0;
}

.emotion-3:nth-of-type(2)>a {
  border-top-right-radius: 0;
}

.emotion-3:nth-of-type(even)>a {
  border-left: none;
}

.emotion-3:nth-of-type(2n+1)>a {
  border-left: solid 1px #333333;
}

.emotion-3:nth-last-of-type(2)>a {
  border-bottom: none;
  border-bottom-left-radius: 0;
}

.emotion-3:last-of-type {
  -webkit-flex: 1;
  -ms-flex: 1;
  flex: 1;
}

.emotion-3:last-of-type>a {
  border-bottom: 1px solid #333333;
  border-bottom-right-radius: 0;
  border-bottom-left-radius: 0;
}

.emotion-4 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: none;
  transition: none;
  box-sizing: border-box;
  border: 1.5px solid transparent;
  border-radius: 0;
  font-size: 16px;
  font-weight: 500;
  letter-spacing: 0px;
  min-height: auto;
  max-height: 60px;
  line-height: 1.25;
  padding: 22px 40px;
  width: auto;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  background-color: #FFFFFF;
  color: #000000;
  border-color: #000000;
  box-sizing: border-box;
  margin: 0;
  border-radius: 0;
  padding: 9px 14px;
  line-height: 1.1176470588235294;
  letter-spacing: 0px;
  min-height: 56px;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  margin-left: auto;
  margin-right: auto;
  border-color: initial;
  box-sizing: border-box;
  text-align: center;
  height: auto;
  white-space: normal;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  width: 100%;
  border-radius: 0;
  font-size: 16px;
  padding: 10px 14px 9px;
  background-color: #FFFFFF;
  border: 1px solid #333333;
}

.emotion-4:focus {
  outline: none;
}

.emotion-4>span {
  padding: 1px 0;
}

.emotion-4:first-of-type {
  margin-left: 0!important;
}

.emotion-4>span {
  height: 100%;
}

<div>
  <div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <div
        class="emotion-0"
      >
        <div
          class="emotion-1"
        >
          <nav
            aria-label="Categories"
          >
            <div
              class="emotion-2"
            >
              <div
                class="emotion-3"
              >
                <a
                  aria-hidden="false"
                  class="emotion-4"
                  href="https://www.google.com"
                  id="YogaStudio-0"
                  target="_self"
                  title="Yoga & Studio"
                >
                  Yoga & Studio
                </a>
              </div>
              <div
                class="emotion-3"
              >
                <a
                  aria-hidden="false"
                  class="emotion-4"
                  href="#jump-to-bottoms"
                  id="TravelCommute-1"
                  target="_self"
                  title="Travel * Commute"
                >
                  Travel * Commute
                </a>
              </div>
              <div
                class="emotion-3"
              >
                <a
                  aria-hidden="false"
                  class="emotion-4"
                  href="https://www.apple.com"
                  id="Test-2"
                  target="_self"
                  title="Test"
                >
                  Test
                </a>
              </div>
            </div>
          </nav>
        </div>
      </div>
    </div>
  </div>
</div>
`;
