// @ts-nocheck
import { LARGE, SMALL } from '@ecom-next/core/breakpoint-provider';
import { screen, act } from 'test-utils';
import { Brands } from '@ecom-next/core/react-stitch';
import { SizeToggle } from '../SizeToggle.gap';
import { setupRender } from '../../../../../test-helpers';

const _render = setupRender(SizeToggle);

const theme = { color: { b1: '#2B2B2B', wh: '#FFFFFF' } };
describe('SizeToggle - Gap', () => {
  it('should render a SizeToggleButton for desktop', () => {
    const result = _render({ children: 'Hello' }, Brands.Gap, LARGE);
    expect(result.asFragment()).toMatchSnapshot();
    const button = screen.getByText(/Hello/);
    expect(button).toHaveStyleRules({
      border: `1px solid ${theme.color.b1}`,
      'background-color': theme.color.wh,
      color: theme.color.b1,
    });
  });

  it('should render a SizeToggleButton for mobile', () => {
    const result = _render({ children: 'Hello' }, Brands.Gap, SMALL);
    expect(result.asFragment()).toMatchSnapshot();
  });

  it('should render the active button style from prop', () => {
    _render({ active: true, children: 'Hello' }, Brands.Gap, LARGE);
    const button = screen.getByText(/Hello/);
    expect(button).toHaveStyleRules({
      'background-color': '#2B2B2B',
      color: '#FFFFFF!important',
    });
  });
});
