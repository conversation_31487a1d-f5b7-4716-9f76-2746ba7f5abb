// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`VisualNavigationSizeToggleCarousel in extra large viewports - extra large desktops should match snapshots for extra large viewports 1`] = `
.emotion-0 {
  margin-left: auto;
  margin-right: auto;
  max-width: 1280px;
}

.emotion-1 {
  aspect-ratio: 1280/354;
}

.emotion-2 {
  padding-bottom: 9px;
  padding-inline: 0;
}

.emotion-2 .amp-cms--p {
  padding: 0;
  margin: 0;
  line-height: 0;
}

.emotion-2 .amp-cms--p>span {
  display: inline;
  white-space: break-spaces;
}

.emotion-2 .amp-cms--p a {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  position: relative;
  z-index: 2;
  pointer-events: auto;
}

.emotion-2 .amp-cms--p a:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-2 .amp-cms--legal-copy {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(0.78125vw, 10px));
  line-height: 1.5;
  letter-spacing: 0;
}

.emotion-2 sup {
  vertical-align: top;
  display: inline-block;
  margin-top: -0.20ex;
}

.emotion-2 sub {
  vertical-align: bottom;
  display: inline-block;
  margin-bottom: -0.20ex;
}

.emotion-2 .amp-cms--f-0 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.9375vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-2 .amp-cms--f-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.9375vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-2 .amp-cms--f-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.9375vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-2 .amp-cms--fn-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.9375vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-2 .amp-cms--body-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(1.40625vw, 18px));
  line-height: 1.5;
  letter-spacing: min(0.056249999999999994vw, 0.72px);
  font-weight: 500;
}

.emotion-2 .amp-cms--body-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(1.25vw, 16px));
  line-height: 1.5;
  letter-spacing: min(0.05vw, 0.64px);
  font-weight: 500;
}

.emotion-2 .amp-cms--body-3 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(1.09375vw, 14px));
  line-height: 1.5;
  letter-spacing: min(0.043750000000000004vw, 0.56px);
}

.emotion-2 .amp-cms--body-4 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(0.9375vw, 12px));
  line-height: 1.5;
  letter-spacing: 0;
}

.emotion-2 .amp-cms--body-5 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(0.78125vw, 10px));
  line-height: 1.5;
  letter-spacing: 0;
}

.emotion-2 .amp-cms--eyebrow-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(2.1875vw, 28px));
  line-height: 1;
  letter-spacing: min(0.08750000000000001vw, 1.12px);
  font-weight: 500;
}

.emotion-2 .amp-cms--eyebrow-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(1.7968749999999998vw, 23px));
  line-height: 1;
  letter-spacing: min(0.071875vw, 0.92px);
  font-weight: 500;
}

.emotion-2 .amp-cms--eyebrow-3 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(1.328125vw, 17px));
  line-height: 1;
  letter-spacing: min(0.053125000000000006vw, 0.68px);
  font-weight: 500;
}

.emotion-2 .amp-cms--headline-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(9.21875vw, 118px));
  line-height: 1;
  letter-spacing: 0;
  font-weight: 700;
}

.emotion-2 .amp-cms--headline-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(6.25vw, 80px));
  line-height: 1;
  letter-spacing: 0;
  font-weight: 700;
}

.emotion-2 .amp-cms--headline-3 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(5.625vw, 72px));
  line-height: 1;
  letter-spacing: 0;
  font-weight: 700;
}

.emotion-2 .amp-cms--headline-4 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(5vw, 64px));
  line-height: 1;
  letter-spacing: min(0.1vw, 1.28px);
  font-weight: 700;
}

.emotion-2 .amp-cms--headline-5 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(3.75vw, 48px));
  line-height: 1;
  letter-spacing: min(0.22499999999999998vw, 2.88px);
  font-weight: 700;
}

.emotion-2 .amp-cms--headline-6 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(3.125vw, 40px));
  line-height: 1.1;
  letter-spacing: min(0.1875vw, 2.4px);
  font-weight: 700;
}

.emotion-2 .amp-cms--headline-7 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(2.5vw, 32px));
  line-height: 1;
  letter-spacing: min(0.1vw, 1.28px);
  font-weight: 700;
}

.emotion-2 .amp-cms--headlineAlt-1 {
  color: #003764;
  font-size: max(14px, min(8.90625vw, 114px));
  line-height: 1;
  letter-spacing: min(-0.03125vw, -0.4px);
  font-weight: 400;
}

.emotion-2 .amp-cms--headlineAlt-2 {
  color: #003764;
  font-size: max(14px, min(6.5625vw, 84px));
  line-height: 1;
  letter-spacing: min(-0.03125vw, -0.4px);
  font-weight: 400;
}

.emotion-2 .amp-cms--headlineAlt-3 {
  color: #003764;
  font-size: max(14px, min(5vw, 64px));
  line-height: 1;
  letter-spacing: min(-0.03125vw, -0.4px);
  font-weight: 400;
}

.emotion-2 .amp-cms--headlineAlt-4 {
  color: #003764;
  font-size: max(14px, min(2.65625vw, 34px));
  line-height: 1;
  letter-spacing: min(-0.03125vw, -0.4px);
  font-weight: 400;
}

.emotion-2 .amp-cms--headlineAlt-5 {
  color: #003764;
  font-size: min(1.25vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-2 .amp-cms--headlineAlt-6 {
  color: #003764;
  font-size: min(1.25vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-2 .amp-cms--headlineAlt-7 {
  color: #003764;
  font-size: min(1.25vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-2 .amp-cms--promo-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(7.8125vw, 100px));
  line-height: 1;
  letter-spacing: min(0vw, 0px);
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-2 .amp-cms--promo-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(6.25vw, 80px));
  line-height: 1;
  letter-spacing: min(0vw, 0px);
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-2 .amp-cms--promoAlt-1 {
  color: #003764;
  font-size: min(1.25vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 600;
}

.emotion-2 .amp-cms--subhead-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(1.875vw, 24px));
  line-height: 1.5;
  letter-spacing: 0;
  font-weight: 500;
}

.emotion-2 .amp-cms--subhead-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(1.7187500000000002vw, 22px));
  line-height: 1.5;
  letter-spacing: 0;
  font-weight: 500;
}

.emotion-2 .amp-cms--subhead-3 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(1.5625vw, 20px));
  line-height: 1.5;
  letter-spacing: 0;
  font-weight: 500;
}

.emotion-3 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-4 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  overflow: hidden;
}

.emotion-5 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  gap: 0;
  overflow: hidden;
  height: min(27.65625vw, 354px);
  min-width: min(6.25vw, 80px);
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
}

.emotion-6 {
  -webkit-box-flex: 1;
  -webkit-flex-grow: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
  -webkit-flex-basis: 0;
  -ms-flex-preferred-size: 0;
  flex-basis: 0;
  box-sizing: border-box;
  border: 1px solid #003764;
  border-radius: 8px 0 0 8px;
  background-color: #FFFFFF;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  color: #003764;
  margin: 0;
  outline: none;
  background-color: #003764;
  -webkit-text-decoration: underline;
  text-decoration: underline;
}

.emotion-6:not(:first-of-type) {
  margin-top: -1px;
}

.emotion-6:focus {
  outline: none;
  background-color: #003764;
  -webkit-text-decoration: underline;
  text-decoration: underline;
}

.emotion-6:focus,
.emotion-6:focus p * {
  color: #FFFFFF!important;
}

.emotion-6,
.emotion-6 p * {
  color: #FFFFFF!important;
}

.emotion-7 {
  width: 100%;
}

.emotion-7 .amp-cms--p {
  padding: 0;
  margin: 0;
  line-height: 0;
}

.emotion-7 .amp-cms--p>span {
  display: inline;
  white-space: break-spaces;
}

.emotion-7 .amp-cms--p a {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  position: relative;
  z-index: 2;
  pointer-events: auto;
}

.emotion-7 .amp-cms--p a:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-7 .amp-cms--legal-copy {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(0.78125vw, 10px));
  line-height: 1.5;
  letter-spacing: 0;
}

.emotion-7 sup {
  vertical-align: top;
  display: inline-block;
  margin-top: -0.20ex;
}

.emotion-7 sub {
  vertical-align: bottom;
  display: inline-block;
  margin-bottom: -0.20ex;
}

.emotion-7 .amp-cms--f-0 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.9375vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-7 .amp-cms--f-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.9375vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-7 .amp-cms--f-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.9375vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-7 .amp-cms--fn-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.9375vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-7 .amp-cms--body-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(1.40625vw, 18px));
  line-height: 1.5;
  letter-spacing: min(0.056249999999999994vw, 0.72px);
  font-weight: 500;
}

.emotion-7 .amp-cms--body-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(1.25vw, 16px));
  line-height: 1.5;
  letter-spacing: min(0.05vw, 0.64px);
  font-weight: 500;
}

.emotion-7 .amp-cms--body-3 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(1.09375vw, 14px));
  line-height: 1.5;
  letter-spacing: min(0.043750000000000004vw, 0.56px);
}

.emotion-7 .amp-cms--body-4 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(0.9375vw, 12px));
  line-height: 1.5;
  letter-spacing: 0;
}

.emotion-7 .amp-cms--body-5 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(0.78125vw, 10px));
  line-height: 1.5;
  letter-spacing: 0;
}

.emotion-7 .amp-cms--eyebrow-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(2.1875vw, 28px));
  line-height: 1;
  letter-spacing: min(0.08750000000000001vw, 1.12px);
  font-weight: 500;
}

.emotion-7 .amp-cms--eyebrow-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(1.7968749999999998vw, 23px));
  line-height: 1;
  letter-spacing: min(0.071875vw, 0.92px);
  font-weight: 500;
}

.emotion-7 .amp-cms--eyebrow-3 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(1.328125vw, 17px));
  line-height: 1;
  letter-spacing: min(0.053125000000000006vw, 0.68px);
  font-weight: 500;
}

.emotion-7 .amp-cms--headline-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(9.21875vw, 118px));
  line-height: 1;
  letter-spacing: 0;
  font-weight: 700;
}

.emotion-7 .amp-cms--headline-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(6.25vw, 80px));
  line-height: 1;
  letter-spacing: 0;
  font-weight: 700;
}

.emotion-7 .amp-cms--headline-3 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(5.625vw, 72px));
  line-height: 1;
  letter-spacing: 0;
  font-weight: 700;
}

.emotion-7 .amp-cms--headline-4 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(5vw, 64px));
  line-height: 1;
  letter-spacing: min(0.1vw, 1.28px);
  font-weight: 700;
}

.emotion-7 .amp-cms--headline-5 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(3.75vw, 48px));
  line-height: 1;
  letter-spacing: min(0.22499999999999998vw, 2.88px);
  font-weight: 700;
}

.emotion-7 .amp-cms--headline-6 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(3.125vw, 40px));
  line-height: 1.1;
  letter-spacing: min(0.1875vw, 2.4px);
  font-weight: 700;
}

.emotion-7 .amp-cms--headline-7 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(2.5vw, 32px));
  line-height: 1;
  letter-spacing: min(0.1vw, 1.28px);
  font-weight: 700;
}

.emotion-7 .amp-cms--headlineAlt-1 {
  color: #003764;
  font-size: max(14px, min(8.90625vw, 114px));
  line-height: 1;
  letter-spacing: min(-0.03125vw, -0.4px);
  font-weight: 400;
}

.emotion-7 .amp-cms--headlineAlt-2 {
  color: #003764;
  font-size: max(14px, min(6.5625vw, 84px));
  line-height: 1;
  letter-spacing: min(-0.03125vw, -0.4px);
  font-weight: 400;
}

.emotion-7 .amp-cms--headlineAlt-3 {
  color: #003764;
  font-size: max(14px, min(5vw, 64px));
  line-height: 1;
  letter-spacing: min(-0.03125vw, -0.4px);
  font-weight: 400;
}

.emotion-7 .amp-cms--headlineAlt-4 {
  color: #003764;
  font-size: max(14px, min(2.65625vw, 34px));
  line-height: 1;
  letter-spacing: min(-0.03125vw, -0.4px);
  font-weight: 400;
}

.emotion-7 .amp-cms--headlineAlt-5 {
  color: #003764;
  font-size: min(1.25vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-7 .amp-cms--headlineAlt-6 {
  color: #003764;
  font-size: min(1.25vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-7 .amp-cms--headlineAlt-7 {
  color: #003764;
  font-size: min(1.25vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-7 .amp-cms--promo-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(7.8125vw, 100px));
  line-height: 1;
  letter-spacing: min(0vw, 0px);
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-7 .amp-cms--promo-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(6.25vw, 80px));
  line-height: 1;
  letter-spacing: min(0vw, 0px);
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-7 .amp-cms--promoAlt-1 {
  color: #003764;
  font-size: min(1.25vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 600;
}

.emotion-7 .amp-cms--subhead-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(1.875vw, 24px));
  line-height: 1.5;
  letter-spacing: 0;
  font-weight: 500;
}

.emotion-7 .amp-cms--subhead-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(1.7187500000000002vw, 22px));
  line-height: 1.5;
  letter-spacing: 0;
  font-weight: 500;
}

.emotion-7 .amp-cms--subhead-3 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(1.5625vw, 20px));
  line-height: 1.5;
  letter-spacing: 0;
  font-weight: 500;
}

.emotion-8 {
  -webkit-box-flex: 1;
  -webkit-flex-grow: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
  -webkit-flex-basis: 0;
  -ms-flex-preferred-size: 0;
  flex-basis: 0;
  box-sizing: border-box;
  border: 1px solid #003764;
  border-radius: 8px 0 0 8px;
  background-color: #FFFFFF;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  color: #003764;
  margin: 0;
}

.emotion-8:not(:first-of-type) {
  margin-top: -1px;
}

.emotion-8:focus {
  outline: none;
  background-color: #003764;
  -webkit-text-decoration: underline;
  text-decoration: underline;
}

.emotion-8:focus,
.emotion-8:focus p * {
  color: #FFFFFF!important;
}

.emotion-8:hover {
  outline: none;
  background-color: #003764;
}

.emotion-8:hover,
.emotion-8:hover p * {
  color: #FFFFFF!important;
}

.emotion-12 {
  overflow: hidden;
}

.emotion-12 div.slick-track {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  gap: min(0.9765625vw, 12.5px);
}

.emotion-12 div.slick-track:before {
  display: none;
}

.emotion-12 div.slick-track:after {
  display: none;
}

.emotion-12 div.slick-slide {
  height: auto;
}

.emotion-12 div.slick-slide>div {
  height: 100%;
}

.emotion-13 {
  position: relative;
}

.emotion-13 .slick-slider {
  position: relative;
  display: block;
  box-sizing: border-box;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  -webkit-touch-callout: none;
  touch-action: pan-y;
  -webkit-tap-highlight-color: transparent;
  scroll-behavior: smooth;
}

.emotion-13 .slick-slider .slick-track,
.emotion-13 .slick-slider .slick-list {
  -webkit-transform: translate3d(0, 0, 0);
  -moz-transform: translate3d(0, 0, 0);
  -ms-transform: translate3d(0, 0, 0);
  transform: translate3d(0, 0, 0);
}

.emotion-13 .slick-list {
  position: relative;
  display: block;
  overflow-x: hidden;
  overflow-y: hidden;
  margin: 0;
  padding: 0;
}

.emotion-13 .slick-list:focus {
  outline: none;
}

.emotion-13 .slick-list.dragging {
  cursor: pointer;
  cursor: hand;
}

.emotion-13 .slick-track {
  position: relative;
  top: 0;
  left: 0;
  display: block;
  margin-left: auto;
  margin-right: auto;
}

.emotion-13 .slick-track:before,
.emotion-13 .slick-track:after {
  display: table;
  content: "";
}

.emotion-13 .slick-track:after {
  clear: both;
}

.emotion-13 .slick-slide {
  display: none;
  float: left;
  height: 100%;
  min-height: 1px;
}

.emotion-13 .slick-slide img {
  display: block;
}

.emotion-13 .slick-slide.slick-loading img {
  display: none;
}

.emotion-13 .slick-slide.dragging img {
  pointer-events: none;
}

.emotion-13 [dir="rtl"] .slick-slide {
  float: right;
}

.emotion-13 .slick-initialized .slick-slide,
.emotion-13 .slick-vertical .slick-slide {
  display: block;
}

.emotion-13 .slick-vertical .slick-slide {
  border: 1px solid transparent;
  height: auto;
}

.emotion-13 .slick-loading .slick-track,
.emotion-13 .slick-loading .slick-slide {
  visibility: hidden;
}

.emotion-13 .slick-loading .slick-list {
  background: #fff url("./ajax-loader.gif") center center no-repeat;
}

.emotion-13 .slick-arrow.slick-hidden {
  display: none;
}

.emotion-13 .slick-prev,
.emotion-13 .slick-next {
  font-size: 0;
  line-height: 0;
  position: absolute;
  top: 50%;
  display: block;
  padding: 0;
  cursor: pointer;
  color: transparent;
  border: none;
  outline: none;
  background: transparent;
}

.emotion-13 .slick-prev:hover,
.emotion-13 .slick-next:hover,
.emotion-13 .slick-prev:focus,
.emotion-13 .slick-next:focus {
  color: transparent;
  outline: none;
  background: transparent;
  opacity: 1;
}

.emotion-13 .slick-prev.slick-disabled,
.emotion-13 .slick-next.slick-disabled {
  opacity: 0.25;
}

.emotion-13 .slick-prev {
  left: -0px;
  -webkit-transform: translate(0, -50%) rotate(90deg);
  -moz-transform: translate(0, -50%) rotate(90deg);
  -ms-transform: translate(0, -50%) rotate(90deg);
  transform: translate(0, -50%) rotate(90deg);
}

.emotion-13 .slick-prev span {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-13 [dir="rtl"] .slick-prev {
  right: -0px;
  left: auto;
}

.emotion-13 .slick-next {
  right: -0px;
  -webkit-transform: translate(0, -50%) rotate(-90deg);
  -moz-transform: translate(0, -50%) rotate(-90deg);
  -ms-transform: translate(0, -50%) rotate(-90deg);
  transform: translate(0, -50%) rotate(-90deg);
}

.emotion-13 .slick-next span {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-13 [dir="rtl"] .slick-next {
  right: auto;
  left: -0px;
}

.emotion-13 .slick-dotted.slick-slider {
  margin-bottom: 30px;
}

.emotion-13 .slick-dots {
  position: absolute;
  bottom: -25px;
  display: block;
  width: 100%;
  padding: 0;
  margin: 0;
  list-style: none;
  text-align: center;
}

.emotion-13 .slick-dots li {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: 20px;
  height: 20px;
  margin: 0 5px;
  padding: 0;
  cursor: pointer;
}

.emotion-13 .slick-dots li.slick-active button:before {
  opacity: 0.75;
  color: black;
}

.emotion-13 .slick-dots li button {
  font-size: 0;
  line-height: 0;
  display: block;
  padding: 5px;
  cursor: pointer;
  color: transparent;
  border: 0;
  outline: none;
  background: transparent;
}

.emotion-13 .slick-dots li button:hover,
.emotion-13 .slick-dots li button:focus {
  outline: none;
}

.emotion-13 .slick-dots li button:hover:before,
.emotion-13 .slick-dots li button:focus:before,
.emotion-13 .slick-dots li button:hover:before,
.emotion-13 .slick-dots li button:focus:before {
  opacity: 1;
}

.emotion-13 .slick-dots li button:before {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 5px;
  height: 5px;
  content: "";
  opacity: 0.25;
  background-color: black;
  border-radius: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-14.slick-prev.slick-arrow {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: min(3.4375000000000004vw, 44px);
  height: min(3.4375000000000004vw, 44px);
  z-index: 2;
  top: min(13.828125vw, 177px);
  margin: 0 min(1.5625vw, 20px);
  background-color: rgba(0,55,100, 0.75);
}

.emotion-14.slick-prev.slick-arrow.slick-disabled {
  display: none;
}

.emotion-14.slick-prev.slick-arrow>span {
  margin: auto;
  height: auto;
  width: auto;
  min-height: auto;
  min-width: auto;
}

.emotion-14.slick-prev.slick-arrow svg {
  margin: auto;
  height: min(1.953125vw, 25px);
  width: min(1.40625vw, 18px);
}

.emotion-15 {
  display: inline-block;
  height: 2rem;
  width: 2rem;
  min-height: 2rem;
  min-width: 2rem;
}

.emotion-15 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-16 {
  outline: none;
  position: relative;
  -webkit-flex: 1;
  -ms-flex: 1;
  flex: 1;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  min-width: auto;
}

.emotion-17 {
  overflow: hidden;
  -webkit-transition: background-color .25s ease;
  transition: background-color .25s ease;
  background-color: transparent;
  height: min(27.65625vw, 354px);
  position: relative;
}

.emotion-18 {
  box-sizing: border-box;
  aspect-ratio: 230/354;
  position: relative;
  -webkit-transition: -webkit-transform .25s ease,opacity .25s ease;
  transition: transform .25s ease,opacity .25s ease;
  -webkit-transform: none;
  -moz-transform: none;
  -ms-transform: none;
  transform: none;
  opacity: 1;
  width: 100%;
  z-index: 1;
}

.emotion-19 {
  width: 100%;
  aspect-ratio: 230/354;
  object-fit: cover;
}

.emotion-20 {
  background: transparent;
  position: relative;
  box-sizing: border-box;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-box-flex: 1;
  -webkit-flex-grow: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
  z-index: 1;
}

.emotion-21 {
  height: 100%;
  padding: 0 6px;
  position: relative;
  background-color: #FFFFFF;
}

.emotion-22 {
  padding-top: 7px;
  text-decoration-color: #003764;
}

.emotion-22 .amp-cms--p {
  padding: 0;
  margin: 0;
  line-height: 0;
}

.emotion-22 .amp-cms--p>span {
  display: inline;
  white-space: break-spaces;
}

.emotion-22 .amp-cms--p a {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  position: relative;
  z-index: 2;
  pointer-events: auto;
}

.emotion-22 .amp-cms--p a:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-22 .amp-cms--legal-copy {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 10px;
  line-height: 1.5;
  letter-spacing: 0;
}

.emotion-22 sup {
  vertical-align: top;
  display: inline-block;
  margin-top: -0.20ex;
}

.emotion-22 sub {
  vertical-align: bottom;
  display: inline-block;
  margin-bottom: -0.20ex;
}

.emotion-22 .amp-cms--f-0 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-22 .amp-cms--f-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-22 .amp-cms--f-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-22 .amp-cms--fn-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-22 .amp-cms--body-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 18px;
  line-height: 1.5;
  letter-spacing: 0.72px;
  font-weight: 500;
}

.emotion-22 .amp-cms--body-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 16px;
  line-height: 1.5;
  letter-spacing: 0.64px;
  font-weight: 500;
}

.emotion-22 .amp-cms--body-3 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 14px;
  line-height: 1.5;
  letter-spacing: 0.56px;
}

.emotion-22 .amp-cms--body-4 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.5;
  letter-spacing: 0;
}

.emotion-22 .amp-cms--body-5 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 10px;
  line-height: 1.5;
  letter-spacing: 0;
}

.emotion-22 .amp-cms--eyebrow-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 28px;
  line-height: 1;
  letter-spacing: 1.12px;
  font-weight: 500;
}

.emotion-22 .amp-cms--eyebrow-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 23px;
  line-height: 1;
  letter-spacing: 0.92px;
  font-weight: 500;
}

.emotion-22 .amp-cms--eyebrow-3 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 17px;
  line-height: 1;
  letter-spacing: 0.68px;
  font-weight: 500;
}

.emotion-22 .amp-cms--headline-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 118px;
  line-height: 1;
  letter-spacing: 0;
  font-weight: 700;
}

.emotion-22 .amp-cms--headline-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 80px;
  line-height: 1;
  letter-spacing: 0;
  font-weight: 700;
}

.emotion-22 .amp-cms--headline-3 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 72px;
  line-height: 1;
  letter-spacing: 0;
  font-weight: 700;
}

.emotion-22 .amp-cms--headline-4 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 64px;
  line-height: 1;
  letter-spacing: 1.28px;
  font-weight: 700;
}

.emotion-22 .amp-cms--headline-5 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 48px;
  line-height: 1;
  letter-spacing: 2.88px;
  font-weight: 700;
}

.emotion-22 .amp-cms--headline-6 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 40px;
  line-height: 1.1;
  letter-spacing: 2.4px;
  font-weight: 700;
}

.emotion-22 .amp-cms--headline-7 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 32px;
  line-height: 1;
  letter-spacing: 1.28px;
  font-weight: 700;
}

.emotion-22 .amp-cms--headlineAlt-1 {
  color: #003764;
  font-size: 114px;
  line-height: 1;
  letter-spacing: -0.4px;
  font-weight: 400;
}

.emotion-22 .amp-cms--headlineAlt-2 {
  color: #003764;
  font-size: 84px;
  line-height: 1;
  letter-spacing: -0.4px;
  font-weight: 400;
}

.emotion-22 .amp-cms--headlineAlt-3 {
  color: #003764;
  font-size: 64px;
  line-height: 1;
  letter-spacing: -0.4px;
  font-weight: 400;
}

.emotion-22 .amp-cms--headlineAlt-4 {
  color: #003764;
  font-size: 34px;
  line-height: 1;
  letter-spacing: -0.4px;
  font-weight: 400;
}

.emotion-22 .amp-cms--headlineAlt-5 {
  color: #003764;
  font-size: 16px;
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-22 .amp-cms--headlineAlt-6 {
  color: #003764;
  font-size: 16px;
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-22 .amp-cms--headlineAlt-7 {
  color: #003764;
  font-size: 16px;
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-22 .amp-cms--promo-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 100px;
  line-height: 1;
  letter-spacing: 0px;
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-22 .amp-cms--promo-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 80px;
  line-height: 1;
  letter-spacing: 0px;
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-22 .amp-cms--promoAlt-1 {
  color: #003764;
  font-size: 16px;
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 600;
}

.emotion-22 .amp-cms--subhead-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 24px;
  line-height: 1.5;
  letter-spacing: 0;
  font-weight: 500;
}

.emotion-22 .amp-cms--subhead-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 22px;
  line-height: 1.5;
  letter-spacing: 0;
  font-weight: 500;
}

.emotion-22 .amp-cms--subhead-3 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 20px;
  line-height: 1.5;
  letter-spacing: 0;
  font-weight: 500;
}

.emotion-149.slick-next.slick-arrow {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: min(3.4375000000000004vw, 44px);
  height: min(3.4375000000000004vw, 44px);
  z-index: 2;
  top: min(13.828125vw, 177px);
  margin: 0 min(1.5625vw, 20px);
  background-color: rgba(0,55,100, 0.75);
}

.emotion-149.slick-next.slick-arrow.slick-disabled {
  display: none;
}

.emotion-149.slick-next.slick-arrow>span {
  margin: auto;
  height: auto;
  width: auto;
  min-height: auto;
  min-width: auto;
}

.emotion-149.slick-next.slick-arrow svg {
  margin: auto;
  height: min(1.953125vw, 25px);
  width: min(1.40625vw, 18px);
}

<div>
  <div
    style="font-family: var(--font-family-font1),sans-serif;"
  >
    <div
      class="emotion-0"
    >
      <article
        class="emotion-1"
      >
        <div
          class="emotion-2"
        >
          <div>
            <p
              class="amp-cms--p"
              style="text-align:left;"
            >
              <span
                class="amp-cms--headline-6"
              >
                Lorem ipsum dolor
              </span>
              <span
                class="amp-cms--headline-5"
              >
                 
              </span>
              <span
                class="amp-cms--body-3"
              >
                sit amet consectetur adipiscing elit sed do eiusmod.
              </span>
            </p>
          </div>
        </div>
        <div
          class="emotion-3"
        >
          <div
            class="emotion-4"
          >
            <div
              class="emotion-5"
            >
              <button
                class="emotion-6"
              >
                <div
                  class="emotion-7"
                >
                  <div>
                    <p
                      class="amp-cms--p"
                      style="text-align:left;"
                    >
                      <span
                        class="amp-cms--body-1"
                      >
                        Toggle 1
                      </span>
                    </p>
                  </div>
                </div>
              </button>
              <button
                class="emotion-8"
              >
                <div
                  class="emotion-7"
                >
                  <div>
                    <p
                      class="amp-cms--p"
                      style="text-align:left;"
                    >
                      <span
                        class="amp-cms--body-1"
                      >
                        Toggle 2
                      </span>
                    </p>
                  </div>
                </div>
              </button>
              <button
                class="emotion-8"
              >
                <div
                  class="emotion-7"
                >
                  <div>
                    <p
                      class="amp-cms--p"
                      style="text-align:left;"
                    >
                      <span
                        class="amp-cms--body-1"
                      >
                        Toggle 3
                      </span>
                    </p>
                  </div>
                </div>
              </button>
            </div>
            <div
              class="emotion-12"
            >
              <div
                class="emotion-13"
              >
                <div
                  class="slick-slider slick-initialized"
                  dir="ltr"
                >
                  <button
                    aria-label="Previous"
                    class="slick-prev slick-arrow slick-prev emotion-14"
                  >
                    <span
                      aria-hidden="true"
                      class="emotion-15"
                    >
                      <svg
                        viewBox="0 0 13.29 8.07"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          d="M6.13 7.86a.73.73 0 001.04 0l5.92-5.92a.73.73 0 000-1.03l-.7-.7a.73.73 0 00-1.03 0l-4.71 4.7L1.94.2A.73.73 0 00.9.21l-.69.7a.73.73 0 000 1.03z"
                          fill="#FFFFFF"
                        />
                      </svg>
                    </span>
                  </button>
                  <div
                    class="slick-list"
                  >
                    <div
                      class="slick-track"
                      style="opacity: 1; transform: translate3d(0px, 0px, 0px);"
                    >
                      <div
                        aria-hidden="true"
                        class="slick-slide slick-cloned"
                        data-index="-5"
                        style="width: 0px;"
                        tabindex="-1"
                      >
                        <div>
                          <a
                            class="emotion-16"
                            href="www.C.com"
                            target="_self"
                          >
                            <div
                              aria-live="polite"
                              class="emotion-17"
                            >
                              <div
                                aria-hidden="false"
                                class="emotion-18"
                              >
                                <img
                                  alt="Woman"
                                  class="emotion-19"
                                  src=""
                                />
                              </div>
                            </div>
                            <div
                              class="emotion-20"
                            >
                              <div
                                class="emotion-21"
                              >
                                <div
                                  class="emotion-22"
                                >
                                  <div>
                                    <p
                                      class="amp-cms--p"
                                      style="text-align:left;"
                                    >
                                      <span
                                        class="amp-cms--body-1"
                                      >
                                        Category 3
                                      </span>
                                    </p>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </a>
                        </div>
                      </div>
                      <div
                        aria-hidden="true"
                        class="slick-slide slick-cloned"
                        data-index="-4"
                        style="width: 0px;"
                        tabindex="-1"
                      >
                        <div>
                          <a
                            class="emotion-16"
                            href="www.gap.com"
                            target="_self"
                          >
                            <div
                              aria-live="polite"
                              class="emotion-17"
                            >
                              <div
                                aria-hidden="false"
                                class="emotion-18"
                              >
                                <img
                                  alt="Another Man"
                                  class="emotion-19"
                                  src=""
                                />
                              </div>
                            </div>
                            <div
                              class="emotion-20"
                            >
                              <div
                                class="emotion-21"
                              >
                                <div
                                  class="emotion-22"
                                >
                                  <div>
                                    <p
                                      class="amp-cms--p"
                                      style="text-align:left;"
                                    >
                                      <span
                                        class="amp-cms--body-1"
                                      >
                                        Category 4
                                      </span>
                                    </p>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </a>
                        </div>
                      </div>
                      <div
                        aria-hidden="true"
                        class="slick-slide slick-cloned"
                        data-index="-3"
                        style="width: 0px;"
                        tabindex="-1"
                      >
                        <div>
                          <a
                            class="emotion-16"
                            href="www.gap.com"
                            target="_self"
                          >
                            <div
                              aria-live="polite"
                              class="emotion-17"
                            >
                              <div
                                aria-hidden="false"
                                class="emotion-18"
                              >
                                <img
                                  alt="Posing"
                                  class="emotion-19"
                                  src=""
                                />
                              </div>
                            </div>
                            <div
                              class="emotion-20"
                            >
                              <div
                                class="emotion-21"
                              >
                                <div
                                  class="emotion-22"
                                >
                                  <div>
                                    <p
                                      class="amp-cms--p"
                                      style="text-align:left;"
                                    >
                                      <span
                                        class="amp-cms--body-1"
                                      >
                                        Category 5
                                      </span>
                                    </p>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </a>
                        </div>
                      </div>
                      <div
                        aria-hidden="true"
                        class="slick-slide slick-cloned"
                        data-index="-2"
                        style="width: 0px;"
                        tabindex="-1"
                      >
                        <div>
                          <a
                            class="emotion-16"
                            href="www.gap.com"
                            target="_self"
                          >
                            <div
                              aria-live="polite"
                              class="emotion-17"
                            >
                              <div
                                aria-hidden="false"
                                class="emotion-18"
                              >
                                <img
                                  alt="People"
                                  class="emotion-19"
                                  src=""
                                />
                              </div>
                            </div>
                            <div
                              class="emotion-20"
                            >
                              <div
                                class="emotion-21"
                              >
                                <div
                                  class="emotion-22"
                                >
                                  <div>
                                    <p
                                      class="amp-cms--p"
                                      style="text-align:left;"
                                    >
                                      <span
                                        class="amp-cms--body-1"
                                      >
                                        Category 6
                                      </span>
                                    </p>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </a>
                        </div>
                      </div>
                      <div
                        aria-hidden="true"
                        class="slick-slide slick-cloned"
                        data-index="-1"
                        style="width: 0px;"
                        tabindex="-1"
                      >
                        <div>
                          <a
                            class="emotion-16"
                            href="www.gap.com"
                            target="_self"
                          >
                            <div
                              aria-live="polite"
                              class="emotion-17"
                            >
                              <div
                                aria-hidden="false"
                                class="emotion-18"
                              >
                                <img
                                  alt="Yoga"
                                  class="emotion-19"
                                  src=""
                                />
                              </div>
                            </div>
                            <div
                              class="emotion-20"
                            >
                              <div
                                class="emotion-21"
                              >
                                <div
                                  class="emotion-22"
                                >
                                  <div>
                                    <p
                                      class="amp-cms--p"
                                      style="text-align:left;"
                                    >
                                      <span
                                        class="amp-cms--body-1"
                                      >
                                        Category 7
                                      </span>
                                    </p>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </a>
                        </div>
                      </div>
                      <div
                        aria-hidden="false"
                        class="slick-slide slick-active slick-current"
                        data-index="0"
                        style="outline: none; width: 0px;"
                        tabindex="-1"
                      >
                        <div>
                          <a
                            class="emotion-16"
                            href="www.A.com"
                            target="_self"
                          >
                            <div
                              aria-live="polite"
                              class="emotion-17"
                            >
                              <div
                                aria-hidden="false"
                                class="emotion-18"
                              >
                                <img
                                  alt="Jeans"
                                  class="emotion-19"
                                  src=""
                                />
                              </div>
                            </div>
                            <div
                              class="emotion-20"
                            >
                              <div
                                class="emotion-21"
                              >
                                <div
                                  class="emotion-22"
                                >
                                  <div>
                                    <p
                                      class="amp-cms--p"
                                      style="text-align:left;"
                                    >
                                      <span
                                        class="amp-cms--body-1"
                                      >
                                        Category 1
                                      </span>
                                    </p>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </a>
                        </div>
                      </div>
                      <div
                        aria-hidden="false"
                        class="slick-slide slick-active"
                        data-index="1"
                        style="outline: none; width: 0px;"
                        tabindex="-1"
                      >
                        <div>
                          <a
                            class="emotion-16"
                            href="www.B.com"
                            target="_self"
                          >
                            <div
                              aria-live="polite"
                              class="emotion-17"
                            >
                              <div
                                aria-hidden="false"
                                class="emotion-18"
                              >
                                <img
                                  alt="Man"
                                  class="emotion-19"
                                  src=""
                                />
                              </div>
                            </div>
                            <div
                              class="emotion-20"
                            >
                              <div
                                class="emotion-21"
                              >
                                <div
                                  class="emotion-22"
                                >
                                  <div>
                                    <p
                                      class="amp-cms--p"
                                      style="text-align:left;"
                                    >
                                      <span
                                        class="amp-cms--body-1"
                                      >
                                        Category 2
                                      </span>
                                    </p>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </a>
                        </div>
                      </div>
                      <div
                        aria-hidden="false"
                        class="slick-slide slick-active"
                        data-index="2"
                        style="outline: none; width: 0px;"
                        tabindex="-1"
                      >
                        <div>
                          <a
                            class="emotion-16"
                            href="www.C.com"
                            target="_self"
                          >
                            <div
                              aria-live="polite"
                              class="emotion-17"
                            >
                              <div
                                aria-hidden="false"
                                class="emotion-18"
                              >
                                <img
                                  alt="Woman"
                                  class="emotion-19"
                                  src=""
                                />
                              </div>
                            </div>
                            <div
                              class="emotion-20"
                            >
                              <div
                                class="emotion-21"
                              >
                                <div
                                  class="emotion-22"
                                >
                                  <div>
                                    <p
                                      class="amp-cms--p"
                                      style="text-align:left;"
                                    >
                                      <span
                                        class="amp-cms--body-1"
                                      >
                                        Category 3
                                      </span>
                                    </p>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </a>
                        </div>
                      </div>
                      <div
                        aria-hidden="false"
                        class="slick-slide slick-active"
                        data-index="3"
                        style="outline: none; width: 0px;"
                        tabindex="-1"
                      >
                        <div>
                          <a
                            class="emotion-16"
                            href="www.gap.com"
                            target="_self"
                          >
                            <div
                              aria-live="polite"
                              class="emotion-17"
                            >
                              <div
                                aria-hidden="false"
                                class="emotion-18"
                              >
                                <img
                                  alt="Another Man"
                                  class="emotion-19"
                                  src=""
                                />
                              </div>
                            </div>
                            <div
                              class="emotion-20"
                            >
                              <div
                                class="emotion-21"
                              >
                                <div
                                  class="emotion-22"
                                >
                                  <div>
                                    <p
                                      class="amp-cms--p"
                                      style="text-align:left;"
                                    >
                                      <span
                                        class="amp-cms--body-1"
                                      >
                                        Category 4
                                      </span>
                                    </p>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </a>
                        </div>
                      </div>
                      <div
                        aria-hidden="false"
                        class="slick-slide slick-active"
                        data-index="4"
                        style="outline: none; width: 0px;"
                        tabindex="-1"
                      >
                        <div>
                          <a
                            class="emotion-16"
                            href="www.gap.com"
                            target="_self"
                          >
                            <div
                              aria-live="polite"
                              class="emotion-17"
                            >
                              <div
                                aria-hidden="false"
                                class="emotion-18"
                              >
                                <img
                                  alt="Posing"
                                  class="emotion-19"
                                  src=""
                                />
                              </div>
                            </div>
                            <div
                              class="emotion-20"
                            >
                              <div
                                class="emotion-21"
                              >
                                <div
                                  class="emotion-22"
                                >
                                  <div>
                                    <p
                                      class="amp-cms--p"
                                      style="text-align:left;"
                                    >
                                      <span
                                        class="amp-cms--body-1"
                                      >
                                        Category 5
                                      </span>
                                    </p>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </a>
                        </div>
                      </div>
                      <div
                        aria-hidden="false"
                        class="slick-slide slick-active"
                        data-index="5"
                        style="outline: none; width: 0px;"
                        tabindex="-1"
                      >
                        <div>
                          <a
                            class="emotion-16"
                            href="www.gap.com"
                            target="_self"
                          >
                            <div
                              aria-live="polite"
                              class="emotion-17"
                            >
                              <div
                                aria-hidden="false"
                                class="emotion-18"
                              >
                                <img
                                  alt="People"
                                  class="emotion-19"
                                  src=""
                                />
                              </div>
                            </div>
                            <div
                              class="emotion-20"
                            >
                              <div
                                class="emotion-21"
                              >
                                <div
                                  class="emotion-22"
                                >
                                  <div>
                                    <p
                                      class="amp-cms--p"
                                      style="text-align:left;"
                                    >
                                      <span
                                        class="amp-cms--body-1"
                                      >
                                        Category 6
                                      </span>
                                    </p>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </a>
                        </div>
                      </div>
                      <div
                        aria-hidden="true"
                        class="slick-slide"
                        data-index="6"
                        style="outline: none; width: 0px;"
                        tabindex="-1"
                      >
                        <div>
                          <a
                            class="emotion-16"
                            href="www.gap.com"
                            target="_self"
                          >
                            <div
                              aria-live="polite"
                              class="emotion-17"
                            >
                              <div
                                aria-hidden="false"
                                class="emotion-18"
                              >
                                <img
                                  alt="Yoga"
                                  class="emotion-19"
                                  src=""
                                />
                              </div>
                            </div>
                            <div
                              class="emotion-20"
                            >
                              <div
                                class="emotion-21"
                              >
                                <div
                                  class="emotion-22"
                                >
                                  <div>
                                    <p
                                      class="amp-cms--p"
                                      style="text-align:left;"
                                    >
                                      <span
                                        class="amp-cms--body-1"
                                      >
                                        Category 7
                                      </span>
                                    </p>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </a>
                        </div>
                      </div>
                      <div
                        aria-hidden="true"
                        class="slick-slide slick-cloned"
                        data-index="7"
                        style="width: 0px;"
                        tabindex="-1"
                      >
                        <div>
                          <a
                            class="emotion-16"
                            href="www.A.com"
                            target="_self"
                          >
                            <div
                              aria-live="polite"
                              class="emotion-17"
                            >
                              <div
                                aria-hidden="false"
                                class="emotion-18"
                              >
                                <img
                                  alt="Jeans"
                                  class="emotion-19"
                                  src=""
                                />
                              </div>
                            </div>
                            <div
                              class="emotion-20"
                            >
                              <div
                                class="emotion-21"
                              >
                                <div
                                  class="emotion-22"
                                >
                                  <div>
                                    <p
                                      class="amp-cms--p"
                                      style="text-align:left;"
                                    >
                                      <span
                                        class="amp-cms--body-1"
                                      >
                                        Category 1
                                      </span>
                                    </p>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </a>
                        </div>
                      </div>
                      <div
                        aria-hidden="true"
                        class="slick-slide slick-cloned"
                        data-index="8"
                        style="width: 0px;"
                        tabindex="-1"
                      >
                        <div>
                          <a
                            class="emotion-16"
                            href="www.B.com"
                            target="_self"
                          >
                            <div
                              aria-live="polite"
                              class="emotion-17"
                            >
                              <div
                                aria-hidden="false"
                                class="emotion-18"
                              >
                                <img
                                  alt="Man"
                                  class="emotion-19"
                                  src=""
                                />
                              </div>
                            </div>
                            <div
                              class="emotion-20"
                            >
                              <div
                                class="emotion-21"
                              >
                                <div
                                  class="emotion-22"
                                >
                                  <div>
                                    <p
                                      class="amp-cms--p"
                                      style="text-align:left;"
                                    >
                                      <span
                                        class="amp-cms--body-1"
                                      >
                                        Category 2
                                      </span>
                                    </p>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </a>
                        </div>
                      </div>
                      <div
                        aria-hidden="true"
                        class="slick-slide slick-cloned"
                        data-index="9"
                        style="width: 0px;"
                        tabindex="-1"
                      >
                        <div>
                          <a
                            class="emotion-16"
                            href="www.C.com"
                            target="_self"
                          >
                            <div
                              aria-live="polite"
                              class="emotion-17"
                            >
                              <div
                                aria-hidden="false"
                                class="emotion-18"
                              >
                                <img
                                  alt="Woman"
                                  class="emotion-19"
                                  src=""
                                />
                              </div>
                            </div>
                            <div
                              class="emotion-20"
                            >
                              <div
                                class="emotion-21"
                              >
                                <div
                                  class="emotion-22"
                                >
                                  <div>
                                    <p
                                      class="amp-cms--p"
                                      style="text-align:left;"
                                    >
                                      <span
                                        class="amp-cms--body-1"
                                      >
                                        Category 3
                                      </span>
                                    </p>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </a>
                        </div>
                      </div>
                      <div
                        aria-hidden="true"
                        class="slick-slide slick-cloned"
                        data-index="10"
                        style="width: 0px;"
                        tabindex="-1"
                      >
                        <div>
                          <a
                            class="emotion-16"
                            href="www.gap.com"
                            target="_self"
                          >
                            <div
                              aria-live="polite"
                              class="emotion-17"
                            >
                              <div
                                aria-hidden="false"
                                class="emotion-18"
                              >
                                <img
                                  alt="Another Man"
                                  class="emotion-19"
                                  src=""
                                />
                              </div>
                            </div>
                            <div
                              class="emotion-20"
                            >
                              <div
                                class="emotion-21"
                              >
                                <div
                                  class="emotion-22"
                                >
                                  <div>
                                    <p
                                      class="amp-cms--p"
                                      style="text-align:left;"
                                    >
                                      <span
                                        class="amp-cms--body-1"
                                      >
                                        Category 4
                                      </span>
                                    </p>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </a>
                        </div>
                      </div>
                      <div
                        aria-hidden="true"
                        class="slick-slide slick-cloned"
                        data-index="11"
                        style="width: 0px;"
                        tabindex="-1"
                      >
                        <div>
                          <a
                            class="emotion-16"
                            href="www.gap.com"
                            target="_self"
                          >
                            <div
                              aria-live="polite"
                              class="emotion-17"
                            >
                              <div
                                aria-hidden="false"
                                class="emotion-18"
                              >
                                <img
                                  alt="Posing"
                                  class="emotion-19"
                                  src=""
                                />
                              </div>
                            </div>
                            <div
                              class="emotion-20"
                            >
                              <div
                                class="emotion-21"
                              >
                                <div
                                  class="emotion-22"
                                >
                                  <div>
                                    <p
                                      class="amp-cms--p"
                                      style="text-align:left;"
                                    >
                                      <span
                                        class="amp-cms--body-1"
                                      >
                                        Category 5
                                      </span>
                                    </p>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </a>
                        </div>
                      </div>
                      <div
                        aria-hidden="true"
                        class="slick-slide slick-cloned"
                        data-index="12"
                        style="width: 0px;"
                        tabindex="-1"
                      >
                        <div>
                          <a
                            class="emotion-16"
                            href="www.gap.com"
                            target="_self"
                          >
                            <div
                              aria-live="polite"
                              class="emotion-17"
                            >
                              <div
                                aria-hidden="false"
                                class="emotion-18"
                              >
                                <img
                                  alt="People"
                                  class="emotion-19"
                                  src=""
                                />
                              </div>
                            </div>
                            <div
                              class="emotion-20"
                            >
                              <div
                                class="emotion-21"
                              >
                                <div
                                  class="emotion-22"
                                >
                                  <div>
                                    <p
                                      class="amp-cms--p"
                                      style="text-align:left;"
                                    >
                                      <span
                                        class="amp-cms--body-1"
                                      >
                                        Category 6
                                      </span>
                                    </p>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </a>
                        </div>
                      </div>
                      <div
                        aria-hidden="true"
                        class="slick-slide slick-cloned"
                        data-index="13"
                        style="width: 0px;"
                        tabindex="-1"
                      >
                        <div>
                          <a
                            class="emotion-16"
                            href="www.gap.com"
                            target="_self"
                          >
                            <div
                              aria-live="polite"
                              class="emotion-17"
                            >
                              <div
                                aria-hidden="false"
                                class="emotion-18"
                              >
                                <img
                                  alt="Yoga"
                                  class="emotion-19"
                                  src=""
                                />
                              </div>
                            </div>
                            <div
                              class="emotion-20"
                            >
                              <div
                                class="emotion-21"
                              >
                                <div
                                  class="emotion-22"
                                >
                                  <div>
                                    <p
                                      class="amp-cms--p"
                                      style="text-align:left;"
                                    >
                                      <span
                                        class="amp-cms--body-1"
                                      >
                                        Category 7
                                      </span>
                                    </p>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </a>
                        </div>
                      </div>
                    </div>
                  </div>
                  <button
                    aria-label="Next"
                    class="slick-next slick-arrow slick-next emotion-149"
                  >
                    <span
                      aria-hidden="true"
                      class="emotion-15"
                    >
                      <svg
                        viewBox="0 0 13.29 8.07"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          d="M6.13 7.86a.73.73 0 001.04 0l5.92-5.92a.73.73 0 000-1.03l-.7-.7a.73.73 0 00-1.03 0l-4.71 4.7L1.94.2A.73.73 0 00.9.21l-.69.7a.73.73 0 000 1.03z"
                          fill="#FFFFFF"
                        />
                      </svg>
                    </span>
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </article>
    </div>
  </div>
</div>
`;

exports[`VisualNavigationSizeToggleCarousel in large viewports - Desktop should match snapshots 1`] = `
.emotion-0 {
  margin-left: auto;
  margin-right: auto;
  max-width: 1280px;
}

.emotion-1 {
  aspect-ratio: 1280/354;
}

.emotion-2 {
  padding-bottom: 9px;
  padding-inline: 0;
}

.emotion-2 .amp-cms--p {
  padding: 0;
  margin: 0;
  line-height: 0;
}

.emotion-2 .amp-cms--p>span {
  display: inline;
  white-space: break-spaces;
}

.emotion-2 .amp-cms--p a {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  position: relative;
  z-index: 2;
  pointer-events: auto;
}

.emotion-2 .amp-cms--p a:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-2 .amp-cms--legal-copy {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(0.78125vw, 10px));
  line-height: 1.5;
  letter-spacing: 0;
}

.emotion-2 sup {
  vertical-align: top;
  display: inline-block;
  margin-top: -0.20ex;
}

.emotion-2 sub {
  vertical-align: bottom;
  display: inline-block;
  margin-bottom: -0.20ex;
}

.emotion-2 .amp-cms--f-0 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.9375vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-2 .amp-cms--f-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.9375vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-2 .amp-cms--f-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.9375vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-2 .amp-cms--fn-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.9375vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-2 .amp-cms--body-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(1.40625vw, 18px));
  line-height: 1.5;
  letter-spacing: min(0.056249999999999994vw, 0.72px);
  font-weight: 500;
}

.emotion-2 .amp-cms--body-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(1.25vw, 16px));
  line-height: 1.5;
  letter-spacing: min(0.05vw, 0.64px);
  font-weight: 500;
}

.emotion-2 .amp-cms--body-3 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(1.09375vw, 14px));
  line-height: 1.5;
  letter-spacing: min(0.043750000000000004vw, 0.56px);
}

.emotion-2 .amp-cms--body-4 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(0.9375vw, 12px));
  line-height: 1.5;
  letter-spacing: 0;
}

.emotion-2 .amp-cms--body-5 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(0.78125vw, 10px));
  line-height: 1.5;
  letter-spacing: 0;
}

.emotion-2 .amp-cms--eyebrow-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(2.1875vw, 28px));
  line-height: 1;
  letter-spacing: min(0.08750000000000001vw, 1.12px);
  font-weight: 500;
}

.emotion-2 .amp-cms--eyebrow-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(1.7968749999999998vw, 23px));
  line-height: 1;
  letter-spacing: min(0.071875vw, 0.92px);
  font-weight: 500;
}

.emotion-2 .amp-cms--eyebrow-3 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(1.328125vw, 17px));
  line-height: 1;
  letter-spacing: min(0.053125000000000006vw, 0.68px);
  font-weight: 500;
}

.emotion-2 .amp-cms--headline-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(9.21875vw, 118px));
  line-height: 1;
  letter-spacing: 0;
  font-weight: 700;
}

.emotion-2 .amp-cms--headline-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(6.25vw, 80px));
  line-height: 1;
  letter-spacing: 0;
  font-weight: 700;
}

.emotion-2 .amp-cms--headline-3 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(5.625vw, 72px));
  line-height: 1;
  letter-spacing: 0;
  font-weight: 700;
}

.emotion-2 .amp-cms--headline-4 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(5vw, 64px));
  line-height: 1;
  letter-spacing: min(0.1vw, 1.28px);
  font-weight: 700;
}

.emotion-2 .amp-cms--headline-5 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(3.75vw, 48px));
  line-height: 1;
  letter-spacing: min(0.22499999999999998vw, 2.88px);
  font-weight: 700;
}

.emotion-2 .amp-cms--headline-6 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(3.125vw, 40px));
  line-height: 1.1;
  letter-spacing: min(0.1875vw, 2.4px);
  font-weight: 700;
}

.emotion-2 .amp-cms--headline-7 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(2.5vw, 32px));
  line-height: 1;
  letter-spacing: min(0.1vw, 1.28px);
  font-weight: 700;
}

.emotion-2 .amp-cms--headlineAlt-1 {
  color: #003764;
  font-size: max(14px, min(8.90625vw, 114px));
  line-height: 1;
  letter-spacing: min(-0.03125vw, -0.4px);
  font-weight: 400;
}

.emotion-2 .amp-cms--headlineAlt-2 {
  color: #003764;
  font-size: max(14px, min(6.5625vw, 84px));
  line-height: 1;
  letter-spacing: min(-0.03125vw, -0.4px);
  font-weight: 400;
}

.emotion-2 .amp-cms--headlineAlt-3 {
  color: #003764;
  font-size: max(14px, min(5vw, 64px));
  line-height: 1;
  letter-spacing: min(-0.03125vw, -0.4px);
  font-weight: 400;
}

.emotion-2 .amp-cms--headlineAlt-4 {
  color: #003764;
  font-size: max(14px, min(2.65625vw, 34px));
  line-height: 1;
  letter-spacing: min(-0.03125vw, -0.4px);
  font-weight: 400;
}

.emotion-2 .amp-cms--headlineAlt-5 {
  color: #003764;
  font-size: min(1.25vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-2 .amp-cms--headlineAlt-6 {
  color: #003764;
  font-size: min(1.25vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-2 .amp-cms--headlineAlt-7 {
  color: #003764;
  font-size: min(1.25vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-2 .amp-cms--promo-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(7.8125vw, 100px));
  line-height: 1;
  letter-spacing: min(0vw, 0px);
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-2 .amp-cms--promo-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(6.25vw, 80px));
  line-height: 1;
  letter-spacing: min(0vw, 0px);
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-2 .amp-cms--promoAlt-1 {
  color: #003764;
  font-size: min(1.25vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 600;
}

.emotion-2 .amp-cms--subhead-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(1.875vw, 24px));
  line-height: 1.5;
  letter-spacing: 0;
  font-weight: 500;
}

.emotion-2 .amp-cms--subhead-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(1.7187500000000002vw, 22px));
  line-height: 1.5;
  letter-spacing: 0;
  font-weight: 500;
}

.emotion-2 .amp-cms--subhead-3 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(1.5625vw, 20px));
  line-height: 1.5;
  letter-spacing: 0;
  font-weight: 500;
}

.emotion-3 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-4 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  overflow: hidden;
}

.emotion-5 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  gap: 0;
  overflow: hidden;
  height: min(27.65625vw, 354px);
  min-width: min(6.25vw, 80px);
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
}

.emotion-6 {
  -webkit-box-flex: 1;
  -webkit-flex-grow: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
  -webkit-flex-basis: 0;
  -ms-flex-preferred-size: 0;
  flex-basis: 0;
  box-sizing: border-box;
  border: 1px solid #003764;
  border-radius: 8px 0 0 8px;
  background-color: #FFFFFF;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  color: #003764;
  margin: 0;
  outline: none;
  background-color: #003764;
  -webkit-text-decoration: underline;
  text-decoration: underline;
}

.emotion-6:not(:first-of-type) {
  margin-top: -1px;
}

.emotion-6:focus {
  outline: none;
  background-color: #003764;
  -webkit-text-decoration: underline;
  text-decoration: underline;
}

.emotion-6:focus,
.emotion-6:focus p * {
  color: #FFFFFF!important;
}

.emotion-6,
.emotion-6 p * {
  color: #FFFFFF!important;
}

.emotion-7 {
  width: 100%;
}

.emotion-7 .amp-cms--p {
  padding: 0;
  margin: 0;
  line-height: 0;
}

.emotion-7 .amp-cms--p>span {
  display: inline;
  white-space: break-spaces;
}

.emotion-7 .amp-cms--p a {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  position: relative;
  z-index: 2;
  pointer-events: auto;
}

.emotion-7 .amp-cms--p a:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-7 .amp-cms--legal-copy {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(0.78125vw, 10px));
  line-height: 1.5;
  letter-spacing: 0;
}

.emotion-7 sup {
  vertical-align: top;
  display: inline-block;
  margin-top: -0.20ex;
}

.emotion-7 sub {
  vertical-align: bottom;
  display: inline-block;
  margin-bottom: -0.20ex;
}

.emotion-7 .amp-cms--f-0 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.9375vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-7 .amp-cms--f-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.9375vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-7 .amp-cms--f-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.9375vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-7 .amp-cms--fn-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.9375vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-7 .amp-cms--body-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(1.40625vw, 18px));
  line-height: 1.5;
  letter-spacing: min(0.056249999999999994vw, 0.72px);
  font-weight: 500;
}

.emotion-7 .amp-cms--body-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(1.25vw, 16px));
  line-height: 1.5;
  letter-spacing: min(0.05vw, 0.64px);
  font-weight: 500;
}

.emotion-7 .amp-cms--body-3 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(1.09375vw, 14px));
  line-height: 1.5;
  letter-spacing: min(0.043750000000000004vw, 0.56px);
}

.emotion-7 .amp-cms--body-4 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(0.9375vw, 12px));
  line-height: 1.5;
  letter-spacing: 0;
}

.emotion-7 .amp-cms--body-5 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(0.78125vw, 10px));
  line-height: 1.5;
  letter-spacing: 0;
}

.emotion-7 .amp-cms--eyebrow-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(2.1875vw, 28px));
  line-height: 1;
  letter-spacing: min(0.08750000000000001vw, 1.12px);
  font-weight: 500;
}

.emotion-7 .amp-cms--eyebrow-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(1.7968749999999998vw, 23px));
  line-height: 1;
  letter-spacing: min(0.071875vw, 0.92px);
  font-weight: 500;
}

.emotion-7 .amp-cms--eyebrow-3 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(1.328125vw, 17px));
  line-height: 1;
  letter-spacing: min(0.053125000000000006vw, 0.68px);
  font-weight: 500;
}

.emotion-7 .amp-cms--headline-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(9.21875vw, 118px));
  line-height: 1;
  letter-spacing: 0;
  font-weight: 700;
}

.emotion-7 .amp-cms--headline-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(6.25vw, 80px));
  line-height: 1;
  letter-spacing: 0;
  font-weight: 700;
}

.emotion-7 .amp-cms--headline-3 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(5.625vw, 72px));
  line-height: 1;
  letter-spacing: 0;
  font-weight: 700;
}

.emotion-7 .amp-cms--headline-4 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(5vw, 64px));
  line-height: 1;
  letter-spacing: min(0.1vw, 1.28px);
  font-weight: 700;
}

.emotion-7 .amp-cms--headline-5 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(3.75vw, 48px));
  line-height: 1;
  letter-spacing: min(0.22499999999999998vw, 2.88px);
  font-weight: 700;
}

.emotion-7 .amp-cms--headline-6 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(3.125vw, 40px));
  line-height: 1.1;
  letter-spacing: min(0.1875vw, 2.4px);
  font-weight: 700;
}

.emotion-7 .amp-cms--headline-7 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(2.5vw, 32px));
  line-height: 1;
  letter-spacing: min(0.1vw, 1.28px);
  font-weight: 700;
}

.emotion-7 .amp-cms--headlineAlt-1 {
  color: #003764;
  font-size: max(14px, min(8.90625vw, 114px));
  line-height: 1;
  letter-spacing: min(-0.03125vw, -0.4px);
  font-weight: 400;
}

.emotion-7 .amp-cms--headlineAlt-2 {
  color: #003764;
  font-size: max(14px, min(6.5625vw, 84px));
  line-height: 1;
  letter-spacing: min(-0.03125vw, -0.4px);
  font-weight: 400;
}

.emotion-7 .amp-cms--headlineAlt-3 {
  color: #003764;
  font-size: max(14px, min(5vw, 64px));
  line-height: 1;
  letter-spacing: min(-0.03125vw, -0.4px);
  font-weight: 400;
}

.emotion-7 .amp-cms--headlineAlt-4 {
  color: #003764;
  font-size: max(14px, min(2.65625vw, 34px));
  line-height: 1;
  letter-spacing: min(-0.03125vw, -0.4px);
  font-weight: 400;
}

.emotion-7 .amp-cms--headlineAlt-5 {
  color: #003764;
  font-size: min(1.25vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-7 .amp-cms--headlineAlt-6 {
  color: #003764;
  font-size: min(1.25vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-7 .amp-cms--headlineAlt-7 {
  color: #003764;
  font-size: min(1.25vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-7 .amp-cms--promo-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(7.8125vw, 100px));
  line-height: 1;
  letter-spacing: min(0vw, 0px);
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-7 .amp-cms--promo-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(6.25vw, 80px));
  line-height: 1;
  letter-spacing: min(0vw, 0px);
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-7 .amp-cms--promoAlt-1 {
  color: #003764;
  font-size: min(1.25vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 600;
}

.emotion-7 .amp-cms--subhead-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(1.875vw, 24px));
  line-height: 1.5;
  letter-spacing: 0;
  font-weight: 500;
}

.emotion-7 .amp-cms--subhead-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(1.7187500000000002vw, 22px));
  line-height: 1.5;
  letter-spacing: 0;
  font-weight: 500;
}

.emotion-7 .amp-cms--subhead-3 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(1.5625vw, 20px));
  line-height: 1.5;
  letter-spacing: 0;
  font-weight: 500;
}

.emotion-8 {
  -webkit-box-flex: 1;
  -webkit-flex-grow: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
  -webkit-flex-basis: 0;
  -ms-flex-preferred-size: 0;
  flex-basis: 0;
  box-sizing: border-box;
  border: 1px solid #003764;
  border-radius: 8px 0 0 8px;
  background-color: #FFFFFF;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  color: #003764;
  margin: 0;
}

.emotion-8:not(:first-of-type) {
  margin-top: -1px;
}

.emotion-8:focus {
  outline: none;
  background-color: #003764;
  -webkit-text-decoration: underline;
  text-decoration: underline;
}

.emotion-8:focus,
.emotion-8:focus p * {
  color: #FFFFFF!important;
}

.emotion-8:hover {
  outline: none;
  background-color: #003764;
}

.emotion-8:hover,
.emotion-8:hover p * {
  color: #FFFFFF!important;
}

.emotion-12 {
  overflow: hidden;
}

.emotion-12 div.slick-track {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  gap: min(0.9765625vw, 12.5px);
}

.emotion-12 div.slick-track:before {
  display: none;
}

.emotion-12 div.slick-track:after {
  display: none;
}

.emotion-12 div.slick-slide {
  height: auto;
}

.emotion-12 div.slick-slide>div {
  height: 100%;
}

.emotion-13 {
  position: relative;
}

.emotion-13 .slick-slider {
  position: relative;
  display: block;
  box-sizing: border-box;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  -webkit-touch-callout: none;
  touch-action: pan-y;
  -webkit-tap-highlight-color: transparent;
  scroll-behavior: smooth;
}

.emotion-13 .slick-slider .slick-track,
.emotion-13 .slick-slider .slick-list {
  -webkit-transform: translate3d(0, 0, 0);
  -moz-transform: translate3d(0, 0, 0);
  -ms-transform: translate3d(0, 0, 0);
  transform: translate3d(0, 0, 0);
}

.emotion-13 .slick-list {
  position: relative;
  display: block;
  overflow-x: hidden;
  overflow-y: hidden;
  margin: 0;
  padding: 0;
}

.emotion-13 .slick-list:focus {
  outline: none;
}

.emotion-13 .slick-list.dragging {
  cursor: pointer;
  cursor: hand;
}

.emotion-13 .slick-track {
  position: relative;
  top: 0;
  left: 0;
  display: block;
  margin-left: auto;
  margin-right: auto;
}

.emotion-13 .slick-track:before,
.emotion-13 .slick-track:after {
  display: table;
  content: "";
}

.emotion-13 .slick-track:after {
  clear: both;
}

.emotion-13 .slick-slide {
  display: none;
  float: left;
  height: 100%;
  min-height: 1px;
}

.emotion-13 .slick-slide img {
  display: block;
}

.emotion-13 .slick-slide.slick-loading img {
  display: none;
}

.emotion-13 .slick-slide.dragging img {
  pointer-events: none;
}

.emotion-13 [dir="rtl"] .slick-slide {
  float: right;
}

.emotion-13 .slick-initialized .slick-slide,
.emotion-13 .slick-vertical .slick-slide {
  display: block;
}

.emotion-13 .slick-vertical .slick-slide {
  border: 1px solid transparent;
  height: auto;
}

.emotion-13 .slick-loading .slick-track,
.emotion-13 .slick-loading .slick-slide {
  visibility: hidden;
}

.emotion-13 .slick-loading .slick-list {
  background: #fff url("./ajax-loader.gif") center center no-repeat;
}

.emotion-13 .slick-arrow.slick-hidden {
  display: none;
}

.emotion-13 .slick-prev,
.emotion-13 .slick-next {
  font-size: 0;
  line-height: 0;
  position: absolute;
  top: 50%;
  display: block;
  padding: 0;
  cursor: pointer;
  color: transparent;
  border: none;
  outline: none;
  background: transparent;
}

.emotion-13 .slick-prev:hover,
.emotion-13 .slick-next:hover,
.emotion-13 .slick-prev:focus,
.emotion-13 .slick-next:focus {
  color: transparent;
  outline: none;
  background: transparent;
  opacity: 1;
}

.emotion-13 .slick-prev.slick-disabled,
.emotion-13 .slick-next.slick-disabled {
  opacity: 0.25;
}

.emotion-13 .slick-prev {
  left: -0px;
  -webkit-transform: translate(0, -50%) rotate(90deg);
  -moz-transform: translate(0, -50%) rotate(90deg);
  -ms-transform: translate(0, -50%) rotate(90deg);
  transform: translate(0, -50%) rotate(90deg);
}

.emotion-13 .slick-prev span {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-13 [dir="rtl"] .slick-prev {
  right: -0px;
  left: auto;
}

.emotion-13 .slick-next {
  right: -0px;
  -webkit-transform: translate(0, -50%) rotate(-90deg);
  -moz-transform: translate(0, -50%) rotate(-90deg);
  -ms-transform: translate(0, -50%) rotate(-90deg);
  transform: translate(0, -50%) rotate(-90deg);
}

.emotion-13 .slick-next span {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-13 [dir="rtl"] .slick-next {
  right: auto;
  left: -0px;
}

.emotion-13 .slick-dotted.slick-slider {
  margin-bottom: 30px;
}

.emotion-13 .slick-dots {
  position: absolute;
  bottom: -25px;
  display: block;
  width: 100%;
  padding: 0;
  margin: 0;
  list-style: none;
  text-align: center;
}

.emotion-13 .slick-dots li {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: 20px;
  height: 20px;
  margin: 0 5px;
  padding: 0;
  cursor: pointer;
}

.emotion-13 .slick-dots li.slick-active button:before {
  opacity: 0.75;
  color: black;
}

.emotion-13 .slick-dots li button {
  font-size: 0;
  line-height: 0;
  display: block;
  padding: 5px;
  cursor: pointer;
  color: transparent;
  border: 0;
  outline: none;
  background: transparent;
}

.emotion-13 .slick-dots li button:hover,
.emotion-13 .slick-dots li button:focus {
  outline: none;
}

.emotion-13 .slick-dots li button:hover:before,
.emotion-13 .slick-dots li button:focus:before,
.emotion-13 .slick-dots li button:hover:before,
.emotion-13 .slick-dots li button:focus:before {
  opacity: 1;
}

.emotion-13 .slick-dots li button:before {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 5px;
  height: 5px;
  content: "";
  opacity: 0.25;
  background-color: black;
  border-radius: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-14.slick-prev.slick-arrow {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: min(3.4375000000000004vw, 44px);
  height: min(3.4375000000000004vw, 44px);
  z-index: 2;
  top: min(13.828125vw, 177px);
  margin: 0 min(1.5625vw, 20px);
  background-color: rgba(0,55,100, 0.75);
}

.emotion-14.slick-prev.slick-arrow.slick-disabled {
  display: none;
}

.emotion-14.slick-prev.slick-arrow>span {
  margin: auto;
  height: auto;
  width: auto;
  min-height: auto;
  min-width: auto;
}

.emotion-14.slick-prev.slick-arrow svg {
  margin: auto;
  height: min(1.953125vw, 25px);
  width: min(1.40625vw, 18px);
}

.emotion-15 {
  display: inline-block;
  height: 2rem;
  width: 2rem;
  min-height: 2rem;
  min-width: 2rem;
}

.emotion-15 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-16 {
  outline: none;
  position: relative;
  -webkit-flex: 1;
  -ms-flex: 1;
  flex: 1;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  min-width: auto;
}

.emotion-17 {
  overflow: hidden;
  -webkit-transition: background-color .25s ease;
  transition: background-color .25s ease;
  background-color: transparent;
  height: min(27.65625vw, 354px);
  position: relative;
}

.emotion-18 {
  box-sizing: border-box;
  aspect-ratio: 230/354;
  position: relative;
  -webkit-transition: -webkit-transform .25s ease,opacity .25s ease;
  transition: transform .25s ease,opacity .25s ease;
  -webkit-transform: none;
  -moz-transform: none;
  -ms-transform: none;
  transform: none;
  opacity: 1;
  width: 100%;
  z-index: 1;
}

.emotion-19 {
  width: 100%;
  aspect-ratio: 230/354;
  object-fit: cover;
}

.emotion-20 {
  background: transparent;
  position: relative;
  box-sizing: border-box;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-box-flex: 1;
  -webkit-flex-grow: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
  z-index: 1;
}

.emotion-21 {
  height: 100%;
  padding: 0 6px;
  position: relative;
  background-color: #FFFFFF;
}

.emotion-22 {
  padding-top: 7px;
  text-decoration-color: #003764;
}

.emotion-22 .amp-cms--p {
  padding: 0;
  margin: 0;
  line-height: 0;
}

.emotion-22 .amp-cms--p>span {
  display: inline;
  white-space: break-spaces;
}

.emotion-22 .amp-cms--p a {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  position: relative;
  z-index: 2;
  pointer-events: auto;
}

.emotion-22 .amp-cms--p a:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-22 .amp-cms--legal-copy {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 10px;
  line-height: 1.5;
  letter-spacing: 0;
}

.emotion-22 sup {
  vertical-align: top;
  display: inline-block;
  margin-top: -0.20ex;
}

.emotion-22 sub {
  vertical-align: bottom;
  display: inline-block;
  margin-bottom: -0.20ex;
}

.emotion-22 .amp-cms--f-0 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-22 .amp-cms--f-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-22 .amp-cms--f-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-22 .amp-cms--fn-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-22 .amp-cms--body-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 18px;
  line-height: 1.5;
  letter-spacing: 0.72px;
  font-weight: 500;
}

.emotion-22 .amp-cms--body-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 16px;
  line-height: 1.5;
  letter-spacing: 0.64px;
  font-weight: 500;
}

.emotion-22 .amp-cms--body-3 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 14px;
  line-height: 1.5;
  letter-spacing: 0.56px;
}

.emotion-22 .amp-cms--body-4 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.5;
  letter-spacing: 0;
}

.emotion-22 .amp-cms--body-5 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 10px;
  line-height: 1.5;
  letter-spacing: 0;
}

.emotion-22 .amp-cms--eyebrow-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 28px;
  line-height: 1;
  letter-spacing: 1.12px;
  font-weight: 500;
}

.emotion-22 .amp-cms--eyebrow-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 23px;
  line-height: 1;
  letter-spacing: 0.92px;
  font-weight: 500;
}

.emotion-22 .amp-cms--eyebrow-3 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 17px;
  line-height: 1;
  letter-spacing: 0.68px;
  font-weight: 500;
}

.emotion-22 .amp-cms--headline-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 118px;
  line-height: 1;
  letter-spacing: 0;
  font-weight: 700;
}

.emotion-22 .amp-cms--headline-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 80px;
  line-height: 1;
  letter-spacing: 0;
  font-weight: 700;
}

.emotion-22 .amp-cms--headline-3 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 72px;
  line-height: 1;
  letter-spacing: 0;
  font-weight: 700;
}

.emotion-22 .amp-cms--headline-4 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 64px;
  line-height: 1;
  letter-spacing: 1.28px;
  font-weight: 700;
}

.emotion-22 .amp-cms--headline-5 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 48px;
  line-height: 1;
  letter-spacing: 2.88px;
  font-weight: 700;
}

.emotion-22 .amp-cms--headline-6 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 40px;
  line-height: 1.1;
  letter-spacing: 2.4px;
  font-weight: 700;
}

.emotion-22 .amp-cms--headline-7 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 32px;
  line-height: 1;
  letter-spacing: 1.28px;
  font-weight: 700;
}

.emotion-22 .amp-cms--headlineAlt-1 {
  color: #003764;
  font-size: 114px;
  line-height: 1;
  letter-spacing: -0.4px;
  font-weight: 400;
}

.emotion-22 .amp-cms--headlineAlt-2 {
  color: #003764;
  font-size: 84px;
  line-height: 1;
  letter-spacing: -0.4px;
  font-weight: 400;
}

.emotion-22 .amp-cms--headlineAlt-3 {
  color: #003764;
  font-size: 64px;
  line-height: 1;
  letter-spacing: -0.4px;
  font-weight: 400;
}

.emotion-22 .amp-cms--headlineAlt-4 {
  color: #003764;
  font-size: 34px;
  line-height: 1;
  letter-spacing: -0.4px;
  font-weight: 400;
}

.emotion-22 .amp-cms--headlineAlt-5 {
  color: #003764;
  font-size: 16px;
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-22 .amp-cms--headlineAlt-6 {
  color: #003764;
  font-size: 16px;
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-22 .amp-cms--headlineAlt-7 {
  color: #003764;
  font-size: 16px;
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-22 .amp-cms--promo-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 100px;
  line-height: 1;
  letter-spacing: 0px;
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-22 .amp-cms--promo-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 80px;
  line-height: 1;
  letter-spacing: 0px;
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-22 .amp-cms--promoAlt-1 {
  color: #003764;
  font-size: 16px;
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 600;
}

.emotion-22 .amp-cms--subhead-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 24px;
  line-height: 1.5;
  letter-spacing: 0;
  font-weight: 500;
}

.emotion-22 .amp-cms--subhead-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 22px;
  line-height: 1.5;
  letter-spacing: 0;
  font-weight: 500;
}

.emotion-22 .amp-cms--subhead-3 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 20px;
  line-height: 1.5;
  letter-spacing: 0;
  font-weight: 500;
}

.emotion-149.slick-next.slick-arrow {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: min(3.4375000000000004vw, 44px);
  height: min(3.4375000000000004vw, 44px);
  z-index: 2;
  top: min(13.828125vw, 177px);
  margin: 0 min(1.5625vw, 20px);
  background-color: rgba(0,55,100, 0.75);
}

.emotion-149.slick-next.slick-arrow.slick-disabled {
  display: none;
}

.emotion-149.slick-next.slick-arrow>span {
  margin: auto;
  height: auto;
  width: auto;
  min-height: auto;
  min-width: auto;
}

.emotion-149.slick-next.slick-arrow svg {
  margin: auto;
  height: min(1.953125vw, 25px);
  width: min(1.40625vw, 18px);
}

<div>
  <div
    style="font-family: var(--font-family-font1),sans-serif;"
  >
    <div
      class="emotion-0"
    >
      <article
        class="emotion-1"
      >
        <div
          class="emotion-2"
        >
          <div>
            <p
              class="amp-cms--p"
              style="text-align:left;"
            >
              <span
                class="amp-cms--headline-6"
              >
                Lorem ipsum dolor
              </span>
              <span
                class="amp-cms--headline-5"
              >
                 
              </span>
              <span
                class="amp-cms--body-3"
              >
                sit amet consectetur adipiscing elit sed do eiusmod.
              </span>
            </p>
          </div>
        </div>
        <div
          class="emotion-3"
        >
          <div
            class="emotion-4"
          >
            <div
              class="emotion-5"
            >
              <button
                class="emotion-6"
              >
                <div
                  class="emotion-7"
                >
                  <div>
                    <p
                      class="amp-cms--p"
                      style="text-align:left;"
                    >
                      <span
                        class="amp-cms--body-1"
                      >
                        Toggle 1
                      </span>
                    </p>
                  </div>
                </div>
              </button>
              <button
                class="emotion-8"
              >
                <div
                  class="emotion-7"
                >
                  <div>
                    <p
                      class="amp-cms--p"
                      style="text-align:left;"
                    >
                      <span
                        class="amp-cms--body-1"
                      >
                        Toggle 2
                      </span>
                    </p>
                  </div>
                </div>
              </button>
              <button
                class="emotion-8"
              >
                <div
                  class="emotion-7"
                >
                  <div>
                    <p
                      class="amp-cms--p"
                      style="text-align:left;"
                    >
                      <span
                        class="amp-cms--body-1"
                      >
                        Toggle 3
                      </span>
                    </p>
                  </div>
                </div>
              </button>
            </div>
            <div
              class="emotion-12"
            >
              <div
                class="emotion-13"
              >
                <div
                  class="slick-slider slick-initialized"
                  dir="ltr"
                >
                  <button
                    aria-label="Previous"
                    class="slick-prev slick-arrow slick-prev emotion-14"
                  >
                    <span
                      aria-hidden="true"
                      class="emotion-15"
                    >
                      <svg
                        viewBox="0 0 13.29 8.07"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          d="M6.13 7.86a.73.73 0 001.04 0l5.92-5.92a.73.73 0 000-1.03l-.7-.7a.73.73 0 00-1.03 0l-4.71 4.7L1.94.2A.73.73 0 00.9.21l-.69.7a.73.73 0 000 1.03z"
                          fill="#FFFFFF"
                        />
                      </svg>
                    </span>
                  </button>
                  <div
                    class="slick-list"
                  >
                    <div
                      class="slick-track"
                      style="opacity: 1; transform: translate3d(0px, 0px, 0px);"
                    >
                      <div
                        aria-hidden="true"
                        class="slick-slide slick-cloned"
                        data-index="-5"
                        style="width: 0px;"
                        tabindex="-1"
                      >
                        <div>
                          <a
                            class="emotion-16"
                            href="www.C.com"
                            target="_self"
                          >
                            <div
                              aria-live="polite"
                              class="emotion-17"
                            >
                              <div
                                aria-hidden="false"
                                class="emotion-18"
                              >
                                <img
                                  alt="Woman"
                                  class="emotion-19"
                                  src=""
                                />
                              </div>
                            </div>
                            <div
                              class="emotion-20"
                            >
                              <div
                                class="emotion-21"
                              >
                                <div
                                  class="emotion-22"
                                >
                                  <div>
                                    <p
                                      class="amp-cms--p"
                                      style="text-align:left;"
                                    >
                                      <span
                                        class="amp-cms--body-1"
                                      >
                                        Category 3
                                      </span>
                                    </p>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </a>
                        </div>
                      </div>
                      <div
                        aria-hidden="true"
                        class="slick-slide slick-cloned"
                        data-index="-4"
                        style="width: 0px;"
                        tabindex="-1"
                      >
                        <div>
                          <a
                            class="emotion-16"
                            href="www.gap.com"
                            target="_self"
                          >
                            <div
                              aria-live="polite"
                              class="emotion-17"
                            >
                              <div
                                aria-hidden="false"
                                class="emotion-18"
                              >
                                <img
                                  alt="Another Man"
                                  class="emotion-19"
                                  src=""
                                />
                              </div>
                            </div>
                            <div
                              class="emotion-20"
                            >
                              <div
                                class="emotion-21"
                              >
                                <div
                                  class="emotion-22"
                                >
                                  <div>
                                    <p
                                      class="amp-cms--p"
                                      style="text-align:left;"
                                    >
                                      <span
                                        class="amp-cms--body-1"
                                      >
                                        Category 4
                                      </span>
                                    </p>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </a>
                        </div>
                      </div>
                      <div
                        aria-hidden="true"
                        class="slick-slide slick-cloned"
                        data-index="-3"
                        style="width: 0px;"
                        tabindex="-1"
                      >
                        <div>
                          <a
                            class="emotion-16"
                            href="www.gap.com"
                            target="_self"
                          >
                            <div
                              aria-live="polite"
                              class="emotion-17"
                            >
                              <div
                                aria-hidden="false"
                                class="emotion-18"
                              >
                                <img
                                  alt="Posing"
                                  class="emotion-19"
                                  src=""
                                />
                              </div>
                            </div>
                            <div
                              class="emotion-20"
                            >
                              <div
                                class="emotion-21"
                              >
                                <div
                                  class="emotion-22"
                                >
                                  <div>
                                    <p
                                      class="amp-cms--p"
                                      style="text-align:left;"
                                    >
                                      <span
                                        class="amp-cms--body-1"
                                      >
                                        Category 5
                                      </span>
                                    </p>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </a>
                        </div>
                      </div>
                      <div
                        aria-hidden="true"
                        class="slick-slide slick-cloned"
                        data-index="-2"
                        style="width: 0px;"
                        tabindex="-1"
                      >
                        <div>
                          <a
                            class="emotion-16"
                            href="www.gap.com"
                            target="_self"
                          >
                            <div
                              aria-live="polite"
                              class="emotion-17"
                            >
                              <div
                                aria-hidden="false"
                                class="emotion-18"
                              >
                                <img
                                  alt="People"
                                  class="emotion-19"
                                  src=""
                                />
                              </div>
                            </div>
                            <div
                              class="emotion-20"
                            >
                              <div
                                class="emotion-21"
                              >
                                <div
                                  class="emotion-22"
                                >
                                  <div>
                                    <p
                                      class="amp-cms--p"
                                      style="text-align:left;"
                                    >
                                      <span
                                        class="amp-cms--body-1"
                                      >
                                        Category 6
                                      </span>
                                    </p>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </a>
                        </div>
                      </div>
                      <div
                        aria-hidden="true"
                        class="slick-slide slick-cloned"
                        data-index="-1"
                        style="width: 0px;"
                        tabindex="-1"
                      >
                        <div>
                          <a
                            class="emotion-16"
                            href="www.gap.com"
                            target="_self"
                          >
                            <div
                              aria-live="polite"
                              class="emotion-17"
                            >
                              <div
                                aria-hidden="false"
                                class="emotion-18"
                              >
                                <img
                                  alt="Yoga"
                                  class="emotion-19"
                                  src=""
                                />
                              </div>
                            </div>
                            <div
                              class="emotion-20"
                            >
                              <div
                                class="emotion-21"
                              >
                                <div
                                  class="emotion-22"
                                >
                                  <div>
                                    <p
                                      class="amp-cms--p"
                                      style="text-align:left;"
                                    >
                                      <span
                                        class="amp-cms--body-1"
                                      >
                                        Category 7
                                      </span>
                                    </p>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </a>
                        </div>
                      </div>
                      <div
                        aria-hidden="false"
                        class="slick-slide slick-active slick-current"
                        data-index="0"
                        style="outline: none; width: 0px;"
                        tabindex="-1"
                      >
                        <div>
                          <a
                            class="emotion-16"
                            href="www.A.com"
                            target="_self"
                          >
                            <div
                              aria-live="polite"
                              class="emotion-17"
                            >
                              <div
                                aria-hidden="false"
                                class="emotion-18"
                              >
                                <img
                                  alt="Jeans"
                                  class="emotion-19"
                                  src=""
                                />
                              </div>
                            </div>
                            <div
                              class="emotion-20"
                            >
                              <div
                                class="emotion-21"
                              >
                                <div
                                  class="emotion-22"
                                >
                                  <div>
                                    <p
                                      class="amp-cms--p"
                                      style="text-align:left;"
                                    >
                                      <span
                                        class="amp-cms--body-1"
                                      >
                                        Category 1
                                      </span>
                                    </p>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </a>
                        </div>
                      </div>
                      <div
                        aria-hidden="false"
                        class="slick-slide slick-active"
                        data-index="1"
                        style="outline: none; width: 0px;"
                        tabindex="-1"
                      >
                        <div>
                          <a
                            class="emotion-16"
                            href="www.B.com"
                            target="_self"
                          >
                            <div
                              aria-live="polite"
                              class="emotion-17"
                            >
                              <div
                                aria-hidden="false"
                                class="emotion-18"
                              >
                                <img
                                  alt="Man"
                                  class="emotion-19"
                                  src=""
                                />
                              </div>
                            </div>
                            <div
                              class="emotion-20"
                            >
                              <div
                                class="emotion-21"
                              >
                                <div
                                  class="emotion-22"
                                >
                                  <div>
                                    <p
                                      class="amp-cms--p"
                                      style="text-align:left;"
                                    >
                                      <span
                                        class="amp-cms--body-1"
                                      >
                                        Category 2
                                      </span>
                                    </p>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </a>
                        </div>
                      </div>
                      <div
                        aria-hidden="false"
                        class="slick-slide slick-active"
                        data-index="2"
                        style="outline: none; width: 0px;"
                        tabindex="-1"
                      >
                        <div>
                          <a
                            class="emotion-16"
                            href="www.C.com"
                            target="_self"
                          >
                            <div
                              aria-live="polite"
                              class="emotion-17"
                            >
                              <div
                                aria-hidden="false"
                                class="emotion-18"
                              >
                                <img
                                  alt="Woman"
                                  class="emotion-19"
                                  src=""
                                />
                              </div>
                            </div>
                            <div
                              class="emotion-20"
                            >
                              <div
                                class="emotion-21"
                              >
                                <div
                                  class="emotion-22"
                                >
                                  <div>
                                    <p
                                      class="amp-cms--p"
                                      style="text-align:left;"
                                    >
                                      <span
                                        class="amp-cms--body-1"
                                      >
                                        Category 3
                                      </span>
                                    </p>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </a>
                        </div>
                      </div>
                      <div
                        aria-hidden="false"
                        class="slick-slide slick-active"
                        data-index="3"
                        style="outline: none; width: 0px;"
                        tabindex="-1"
                      >
                        <div>
                          <a
                            class="emotion-16"
                            href="www.gap.com"
                            target="_self"
                          >
                            <div
                              aria-live="polite"
                              class="emotion-17"
                            >
                              <div
                                aria-hidden="false"
                                class="emotion-18"
                              >
                                <img
                                  alt="Another Man"
                                  class="emotion-19"
                                  src=""
                                />
                              </div>
                            </div>
                            <div
                              class="emotion-20"
                            >
                              <div
                                class="emotion-21"
                              >
                                <div
                                  class="emotion-22"
                                >
                                  <div>
                                    <p
                                      class="amp-cms--p"
                                      style="text-align:left;"
                                    >
                                      <span
                                        class="amp-cms--body-1"
                                      >
                                        Category 4
                                      </span>
                                    </p>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </a>
                        </div>
                      </div>
                      <div
                        aria-hidden="false"
                        class="slick-slide slick-active"
                        data-index="4"
                        style="outline: none; width: 0px;"
                        tabindex="-1"
                      >
                        <div>
                          <a
                            class="emotion-16"
                            href="www.gap.com"
                            target="_self"
                          >
                            <div
                              aria-live="polite"
                              class="emotion-17"
                            >
                              <div
                                aria-hidden="false"
                                class="emotion-18"
                              >
                                <img
                                  alt="Posing"
                                  class="emotion-19"
                                  src=""
                                />
                              </div>
                            </div>
                            <div
                              class="emotion-20"
                            >
                              <div
                                class="emotion-21"
                              >
                                <div
                                  class="emotion-22"
                                >
                                  <div>
                                    <p
                                      class="amp-cms--p"
                                      style="text-align:left;"
                                    >
                                      <span
                                        class="amp-cms--body-1"
                                      >
                                        Category 5
                                      </span>
                                    </p>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </a>
                        </div>
                      </div>
                      <div
                        aria-hidden="false"
                        class="slick-slide slick-active"
                        data-index="5"
                        style="outline: none; width: 0px;"
                        tabindex="-1"
                      >
                        <div>
                          <a
                            class="emotion-16"
                            href="www.gap.com"
                            target="_self"
                          >
                            <div
                              aria-live="polite"
                              class="emotion-17"
                            >
                              <div
                                aria-hidden="false"
                                class="emotion-18"
                              >
                                <img
                                  alt="People"
                                  class="emotion-19"
                                  src=""
                                />
                              </div>
                            </div>
                            <div
                              class="emotion-20"
                            >
                              <div
                                class="emotion-21"
                              >
                                <div
                                  class="emotion-22"
                                >
                                  <div>
                                    <p
                                      class="amp-cms--p"
                                      style="text-align:left;"
                                    >
                                      <span
                                        class="amp-cms--body-1"
                                      >
                                        Category 6
                                      </span>
                                    </p>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </a>
                        </div>
                      </div>
                      <div
                        aria-hidden="true"
                        class="slick-slide"
                        data-index="6"
                        style="outline: none; width: 0px;"
                        tabindex="-1"
                      >
                        <div>
                          <a
                            class="emotion-16"
                            href="www.gap.com"
                            target="_self"
                          >
                            <div
                              aria-live="polite"
                              class="emotion-17"
                            >
                              <div
                                aria-hidden="false"
                                class="emotion-18"
                              >
                                <img
                                  alt="Yoga"
                                  class="emotion-19"
                                  src=""
                                />
                              </div>
                            </div>
                            <div
                              class="emotion-20"
                            >
                              <div
                                class="emotion-21"
                              >
                                <div
                                  class="emotion-22"
                                >
                                  <div>
                                    <p
                                      class="amp-cms--p"
                                      style="text-align:left;"
                                    >
                                      <span
                                        class="amp-cms--body-1"
                                      >
                                        Category 7
                                      </span>
                                    </p>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </a>
                        </div>
                      </div>
                      <div
                        aria-hidden="true"
                        class="slick-slide slick-cloned"
                        data-index="7"
                        style="width: 0px;"
                        tabindex="-1"
                      >
                        <div>
                          <a
                            class="emotion-16"
                            href="www.A.com"
                            target="_self"
                          >
                            <div
                              aria-live="polite"
                              class="emotion-17"
                            >
                              <div
                                aria-hidden="false"
                                class="emotion-18"
                              >
                                <img
                                  alt="Jeans"
                                  class="emotion-19"
                                  src=""
                                />
                              </div>
                            </div>
                            <div
                              class="emotion-20"
                            >
                              <div
                                class="emotion-21"
                              >
                                <div
                                  class="emotion-22"
                                >
                                  <div>
                                    <p
                                      class="amp-cms--p"
                                      style="text-align:left;"
                                    >
                                      <span
                                        class="amp-cms--body-1"
                                      >
                                        Category 1
                                      </span>
                                    </p>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </a>
                        </div>
                      </div>
                      <div
                        aria-hidden="true"
                        class="slick-slide slick-cloned"
                        data-index="8"
                        style="width: 0px;"
                        tabindex="-1"
                      >
                        <div>
                          <a
                            class="emotion-16"
                            href="www.B.com"
                            target="_self"
                          >
                            <div
                              aria-live="polite"
                              class="emotion-17"
                            >
                              <div
                                aria-hidden="false"
                                class="emotion-18"
                              >
                                <img
                                  alt="Man"
                                  class="emotion-19"
                                  src=""
                                />
                              </div>
                            </div>
                            <div
                              class="emotion-20"
                            >
                              <div
                                class="emotion-21"
                              >
                                <div
                                  class="emotion-22"
                                >
                                  <div>
                                    <p
                                      class="amp-cms--p"
                                      style="text-align:left;"
                                    >
                                      <span
                                        class="amp-cms--body-1"
                                      >
                                        Category 2
                                      </span>
                                    </p>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </a>
                        </div>
                      </div>
                      <div
                        aria-hidden="true"
                        class="slick-slide slick-cloned"
                        data-index="9"
                        style="width: 0px;"
                        tabindex="-1"
                      >
                        <div>
                          <a
                            class="emotion-16"
                            href="www.C.com"
                            target="_self"
                          >
                            <div
                              aria-live="polite"
                              class="emotion-17"
                            >
                              <div
                                aria-hidden="false"
                                class="emotion-18"
                              >
                                <img
                                  alt="Woman"
                                  class="emotion-19"
                                  src=""
                                />
                              </div>
                            </div>
                            <div
                              class="emotion-20"
                            >
                              <div
                                class="emotion-21"
                              >
                                <div
                                  class="emotion-22"
                                >
                                  <div>
                                    <p
                                      class="amp-cms--p"
                                      style="text-align:left;"
                                    >
                                      <span
                                        class="amp-cms--body-1"
                                      >
                                        Category 3
                                      </span>
                                    </p>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </a>
                        </div>
                      </div>
                      <div
                        aria-hidden="true"
                        class="slick-slide slick-cloned"
                        data-index="10"
                        style="width: 0px;"
                        tabindex="-1"
                      >
                        <div>
                          <a
                            class="emotion-16"
                            href="www.gap.com"
                            target="_self"
                          >
                            <div
                              aria-live="polite"
                              class="emotion-17"
                            >
                              <div
                                aria-hidden="false"
                                class="emotion-18"
                              >
                                <img
                                  alt="Another Man"
                                  class="emotion-19"
                                  src=""
                                />
                              </div>
                            </div>
                            <div
                              class="emotion-20"
                            >
                              <div
                                class="emotion-21"
                              >
                                <div
                                  class="emotion-22"
                                >
                                  <div>
                                    <p
                                      class="amp-cms--p"
                                      style="text-align:left;"
                                    >
                                      <span
                                        class="amp-cms--body-1"
                                      >
                                        Category 4
                                      </span>
                                    </p>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </a>
                        </div>
                      </div>
                      <div
                        aria-hidden="true"
                        class="slick-slide slick-cloned"
                        data-index="11"
                        style="width: 0px;"
                        tabindex="-1"
                      >
                        <div>
                          <a
                            class="emotion-16"
                            href="www.gap.com"
                            target="_self"
                          >
                            <div
                              aria-live="polite"
                              class="emotion-17"
                            >
                              <div
                                aria-hidden="false"
                                class="emotion-18"
                              >
                                <img
                                  alt="Posing"
                                  class="emotion-19"
                                  src=""
                                />
                              </div>
                            </div>
                            <div
                              class="emotion-20"
                            >
                              <div
                                class="emotion-21"
                              >
                                <div
                                  class="emotion-22"
                                >
                                  <div>
                                    <p
                                      class="amp-cms--p"
                                      style="text-align:left;"
                                    >
                                      <span
                                        class="amp-cms--body-1"
                                      >
                                        Category 5
                                      </span>
                                    </p>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </a>
                        </div>
                      </div>
                      <div
                        aria-hidden="true"
                        class="slick-slide slick-cloned"
                        data-index="12"
                        style="width: 0px;"
                        tabindex="-1"
                      >
                        <div>
                          <a
                            class="emotion-16"
                            href="www.gap.com"
                            target="_self"
                          >
                            <div
                              aria-live="polite"
                              class="emotion-17"
                            >
                              <div
                                aria-hidden="false"
                                class="emotion-18"
                              >
                                <img
                                  alt="People"
                                  class="emotion-19"
                                  src=""
                                />
                              </div>
                            </div>
                            <div
                              class="emotion-20"
                            >
                              <div
                                class="emotion-21"
                              >
                                <div
                                  class="emotion-22"
                                >
                                  <div>
                                    <p
                                      class="amp-cms--p"
                                      style="text-align:left;"
                                    >
                                      <span
                                        class="amp-cms--body-1"
                                      >
                                        Category 6
                                      </span>
                                    </p>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </a>
                        </div>
                      </div>
                      <div
                        aria-hidden="true"
                        class="slick-slide slick-cloned"
                        data-index="13"
                        style="width: 0px;"
                        tabindex="-1"
                      >
                        <div>
                          <a
                            class="emotion-16"
                            href="www.gap.com"
                            target="_self"
                          >
                            <div
                              aria-live="polite"
                              class="emotion-17"
                            >
                              <div
                                aria-hidden="false"
                                class="emotion-18"
                              >
                                <img
                                  alt="Yoga"
                                  class="emotion-19"
                                  src=""
                                />
                              </div>
                            </div>
                            <div
                              class="emotion-20"
                            >
                              <div
                                class="emotion-21"
                              >
                                <div
                                  class="emotion-22"
                                >
                                  <div>
                                    <p
                                      class="amp-cms--p"
                                      style="text-align:left;"
                                    >
                                      <span
                                        class="amp-cms--body-1"
                                      >
                                        Category 7
                                      </span>
                                    </p>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </a>
                        </div>
                      </div>
                    </div>
                  </div>
                  <button
                    aria-label="Next"
                    class="slick-next slick-arrow slick-next emotion-149"
                  >
                    <span
                      aria-hidden="true"
                      class="emotion-15"
                    >
                      <svg
                        viewBox="0 0 13.29 8.07"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          d="M6.13 7.86a.73.73 0 001.04 0l5.92-5.92a.73.73 0 000-1.03l-.7-.7a.73.73 0 00-1.03 0l-4.71 4.7L1.94.2A.73.73 0 00.9.21l-.69.7a.73.73 0 000 1.03z"
                          fill="#FFFFFF"
                        />
                      </svg>
                    </span>
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </article>
    </div>
  </div>
</div>
`;

exports[`VisualNavigationSizeToggleCarousel in small viewports - mobile should match snapshots for mobile 1`] = `
.emotion-0 {
  margin-left: auto;
  margin-right: auto;
  max-width: 1280px;
}

.emotion-1 {
  min-height: 194px;
}

.emotion-2 {
  padding-bottom: 9px;
  padding-inline: 18px 0;
}

.emotion-2 .amp-cms--p {
  padding: 0;
  margin: 0;
  line-height: 0;
}

.emotion-2 .amp-cms--p>span {
  display: inline;
  white-space: break-spaces;
}

.emotion-2 .amp-cms--p a {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  position: relative;
  z-index: 2;
  pointer-events: auto;
}

.emotion-2 .amp-cms--p a:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-2 .amp-cms--legal-copy {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(2.666666666666667vw, 10px));
  line-height: 1.5;
  letter-spacing: 0;
}

.emotion-2 sup {
  vertical-align: top;
  display: inline-block;
  margin-top: -0.20ex;
}

.emotion-2 sub {
  vertical-align: bottom;
  display: inline-block;
  margin-bottom: -0.20ex;
}

.emotion-2 .amp-cms--f-0 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(2.933333333333333vw, 11px);
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-2 .amp-cms--f-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(2.933333333333333vw, 11px);
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-2 .amp-cms--f-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(2.933333333333333vw, 11px);
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-2 .amp-cms--fn-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(2.933333333333333vw, 11px);
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-2 .amp-cms--body-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(3.733333333333334vw, 14px));
  line-height: 1.5;
  letter-spacing: min(0.08960000000000001vw, 0.336px);
  font-weight: 500;
}

.emotion-2 .amp-cms--body-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(3.2vw, 12px));
  line-height: 1.5;
  letter-spacing: min(0.0768vw, 0.288px);
  font-weight: 500;
}

.emotion-2 .amp-cms--body-3 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(3.2vw, 12px));
  line-height: 1.5;
  letter-spacing: min(0.0768vw, 0.288px);
}

.emotion-2 .amp-cms--body-4 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(2.666666666666667vw, 10px));
  line-height: 1.5;
  letter-spacing: 0;
}

.emotion-2 .amp-cms--body-5 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(2.666666666666667vw, 10px));
  line-height: 1.5;
  letter-spacing: 0;
}

.emotion-2 .amp-cms--eyebrow-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(6.133333333333333vw, 23px));
  line-height: 1;
  letter-spacing: min(0.24533333333333335vw, 0.92px);
  font-weight: 500;
}

.emotion-2 .amp-cms--eyebrow-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(5.066666666666666vw, 19px));
  line-height: 1;
  letter-spacing: min(0.20266666666666666vw, 0.76px);
  font-weight: 500;
}

.emotion-2 .amp-cms--eyebrow-3 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(3.733333333333334vw, 14px));
  line-height: 1.0714285714285714;
  letter-spacing: min(0.14933333333333335vw, 0.56px);
  font-weight: 500;
}

.emotion-2 .amp-cms--headline-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(18.133333333333333vw, 68px));
  line-height: 1;
  letter-spacing: 0;
  font-weight: 700;
}

.emotion-2 .amp-cms--headline-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(13.333333333333334vw, 50px));
  line-height: 1;
  letter-spacing: 0;
  font-weight: 700;
}

.emotion-2 .amp-cms--headline-3 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(11.466666666666667vw, 43px));
  line-height: 1;
  letter-spacing: 0;
  font-weight: 700;
}

.emotion-2 .amp-cms--headline-4 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(9.6vw, 36px));
  line-height: 1;
  letter-spacing: min(0.19199999999999998vw, 0.72px);
  font-weight: 700;
}

.emotion-2 .amp-cms--headline-5 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(7.466666666666668vw, 28px));
  line-height: 1;
  letter-spacing: min(0.2986666666666667vw, 1.12px);
  font-weight: 700;
}

.emotion-2 .amp-cms--headline-6 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(6.4vw, 24px));
  line-height: 1.0833333333333333;
  letter-spacing: min(0.256vw, 0.96px);
  font-weight: 700;
}

.emotion-2 .amp-cms--headline-7 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(5.333333333333334vw, 20px));
  line-height: 1;
  letter-spacing: min(0.128vw, 0.48px);
  font-weight: 700;
}

.emotion-2 .amp-cms--headlineAlt-1 {
  color: #003764;
  font-size: max(13px, min(17.066666666666666vw, 64px));
  line-height: 1;
  letter-spacing: min(-0.07999999999999999vw, -0.3px);
  font-weight: 400;
}

.emotion-2 .amp-cms--headlineAlt-2 {
  color: #003764;
  font-size: max(13px, min(14.399999999999999vw, 54px));
  line-height: 1;
  letter-spacing: min(-0.07999999999999999vw, -0.3px);
  font-weight: 400;
}

.emotion-2 .amp-cms--headlineAlt-3 {
  color: #003764;
  font-size: max(13px, min(9.066666666666666vw, 34px));
  line-height: 1;
  letter-spacing: min(-0.07999999999999999vw, -0.3px);
  font-weight: 400;
}

.emotion-2 .amp-cms--headlineAlt-4 {
  color: #003764;
  font-size: max(13px, min(6.4vw, 24px));
  line-height: 1.4166666666666667;
  letter-spacing: min(-0.07999999999999999vw, -0.3px);
  font-weight: 400;
}

.emotion-2 .amp-cms--headlineAlt-5 {
  color: #003764;
  font-size: min(3.733333333333334vw, 14px);
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-2 .amp-cms--headlineAlt-6 {
  color: #003764;
  font-size: min(3.733333333333334vw, 14px);
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-2 .amp-cms--headlineAlt-7 {
  color: #003764;
  font-size: min(3.733333333333334vw, 14px);
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-2 .amp-cms--promo-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(13px, min(16vw, 60px));
  line-height: 1;
  letter-spacing: min(0vw, 0px);
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-2 .amp-cms--promo-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(13px, min(10.666666666666668vw, 40px));
  line-height: 1;
  letter-spacing: min(0vw, 0px);
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-2 .amp-cms--promoAlt-1 {
  color: #003764;
  font-size: min(3.733333333333334vw, 14px);
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 600;
}

.emotion-2 .amp-cms--subhead-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(16px, min(5.333333333333334vw, 20px));
  line-height: 1.5;
  letter-spacing: 0;
  font-weight: 500;
}

.emotion-2 .amp-cms--subhead-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(16px, min(4.8vw, 18px));
  line-height: 1.5;
  letter-spacing: 0;
  font-weight: 500;
}

.emotion-2 .amp-cms--subhead-3 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(16px, min(4.266666666666667vw, 16px));
  line-height: 1.5;
  letter-spacing: 0;
  font-weight: 500;
}

.emotion-3 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-4 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  overflow: hidden;
}

.emotion-5 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  gap: 0;
  overflow: hidden;
  height: min(51.73599999999999vw, 194.01px);
  min-width: min(16vw, 60px);
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
}

.emotion-6 {
  -webkit-box-flex: 1;
  -webkit-flex-grow: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
  -webkit-flex-basis: 0;
  -ms-flex-preferred-size: 0;
  flex-basis: 0;
  box-sizing: border-box;
  border: 1px solid #003764;
  border-radius: 8px 0 0 8px;
  background-color: #FFFFFF;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  color: #003764;
  margin: 0;
  outline: none;
  background-color: #003764;
  -webkit-text-decoration: underline;
  text-decoration: underline;
}

.emotion-6:not(:first-of-type) {
  margin-top: -1px;
}

.emotion-6:focus {
  outline: none;
  background-color: #003764;
  -webkit-text-decoration: underline;
  text-decoration: underline;
}

.emotion-6:focus,
.emotion-6:focus p * {
  color: #FFFFFF!important;
}

.emotion-6,
.emotion-6 p * {
  color: #FFFFFF!important;
}

.emotion-7 {
  width: 100%;
}

.emotion-7 .amp-cms--p {
  padding: 0;
  margin: 0;
  line-height: 0;
}

.emotion-7 .amp-cms--p>span {
  display: inline;
  white-space: break-spaces;
}

.emotion-7 .amp-cms--p a {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  position: relative;
  z-index: 2;
  pointer-events: auto;
}

.emotion-7 .amp-cms--p a:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-7 .amp-cms--legal-copy {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(2.666666666666667vw, 10px));
  line-height: 1.5;
  letter-spacing: 0;
}

.emotion-7 sup {
  vertical-align: top;
  display: inline-block;
  margin-top: -0.20ex;
}

.emotion-7 sub {
  vertical-align: bottom;
  display: inline-block;
  margin-bottom: -0.20ex;
}

.emotion-7 .amp-cms--f-0 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(2.933333333333333vw, 11px);
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-7 .amp-cms--f-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(2.933333333333333vw, 11px);
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-7 .amp-cms--f-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(2.933333333333333vw, 11px);
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-7 .amp-cms--fn-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(2.933333333333333vw, 11px);
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-7 .amp-cms--body-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(3.733333333333334vw, 14px));
  line-height: 1.5;
  letter-spacing: min(0.08960000000000001vw, 0.336px);
  font-weight: 500;
}

.emotion-7 .amp-cms--body-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(3.2vw, 12px));
  line-height: 1.5;
  letter-spacing: min(0.0768vw, 0.288px);
  font-weight: 500;
}

.emotion-7 .amp-cms--body-3 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(3.2vw, 12px));
  line-height: 1.5;
  letter-spacing: min(0.0768vw, 0.288px);
}

.emotion-7 .amp-cms--body-4 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(2.666666666666667vw, 10px));
  line-height: 1.5;
  letter-spacing: 0;
}

.emotion-7 .amp-cms--body-5 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(2.666666666666667vw, 10px));
  line-height: 1.5;
  letter-spacing: 0;
}

.emotion-7 .amp-cms--eyebrow-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(6.133333333333333vw, 23px));
  line-height: 1;
  letter-spacing: min(0.24533333333333335vw, 0.92px);
  font-weight: 500;
}

.emotion-7 .amp-cms--eyebrow-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(5.066666666666666vw, 19px));
  line-height: 1;
  letter-spacing: min(0.20266666666666666vw, 0.76px);
  font-weight: 500;
}

.emotion-7 .amp-cms--eyebrow-3 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(3.733333333333334vw, 14px));
  line-height: 1.0714285714285714;
  letter-spacing: min(0.14933333333333335vw, 0.56px);
  font-weight: 500;
}

.emotion-7 .amp-cms--headline-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(18.133333333333333vw, 68px));
  line-height: 1;
  letter-spacing: 0;
  font-weight: 700;
}

.emotion-7 .amp-cms--headline-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(13.333333333333334vw, 50px));
  line-height: 1;
  letter-spacing: 0;
  font-weight: 700;
}

.emotion-7 .amp-cms--headline-3 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(11.466666666666667vw, 43px));
  line-height: 1;
  letter-spacing: 0;
  font-weight: 700;
}

.emotion-7 .amp-cms--headline-4 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(9.6vw, 36px));
  line-height: 1;
  letter-spacing: min(0.19199999999999998vw, 0.72px);
  font-weight: 700;
}

.emotion-7 .amp-cms--headline-5 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(7.466666666666668vw, 28px));
  line-height: 1;
  letter-spacing: min(0.2986666666666667vw, 1.12px);
  font-weight: 700;
}

.emotion-7 .amp-cms--headline-6 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(6.4vw, 24px));
  line-height: 1.0833333333333333;
  letter-spacing: min(0.256vw, 0.96px);
  font-weight: 700;
}

.emotion-7 .amp-cms--headline-7 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(5.333333333333334vw, 20px));
  line-height: 1;
  letter-spacing: min(0.128vw, 0.48px);
  font-weight: 700;
}

.emotion-7 .amp-cms--headlineAlt-1 {
  color: #003764;
  font-size: max(13px, min(17.066666666666666vw, 64px));
  line-height: 1;
  letter-spacing: min(-0.07999999999999999vw, -0.3px);
  font-weight: 400;
}

.emotion-7 .amp-cms--headlineAlt-2 {
  color: #003764;
  font-size: max(13px, min(14.399999999999999vw, 54px));
  line-height: 1;
  letter-spacing: min(-0.07999999999999999vw, -0.3px);
  font-weight: 400;
}

.emotion-7 .amp-cms--headlineAlt-3 {
  color: #003764;
  font-size: max(13px, min(9.066666666666666vw, 34px));
  line-height: 1;
  letter-spacing: min(-0.07999999999999999vw, -0.3px);
  font-weight: 400;
}

.emotion-7 .amp-cms--headlineAlt-4 {
  color: #003764;
  font-size: max(13px, min(6.4vw, 24px));
  line-height: 1.4166666666666667;
  letter-spacing: min(-0.07999999999999999vw, -0.3px);
  font-weight: 400;
}

.emotion-7 .amp-cms--headlineAlt-5 {
  color: #003764;
  font-size: min(3.733333333333334vw, 14px);
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-7 .amp-cms--headlineAlt-6 {
  color: #003764;
  font-size: min(3.733333333333334vw, 14px);
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-7 .amp-cms--headlineAlt-7 {
  color: #003764;
  font-size: min(3.733333333333334vw, 14px);
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-7 .amp-cms--promo-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(13px, min(16vw, 60px));
  line-height: 1;
  letter-spacing: min(0vw, 0px);
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-7 .amp-cms--promo-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(13px, min(10.666666666666668vw, 40px));
  line-height: 1;
  letter-spacing: min(0vw, 0px);
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-7 .amp-cms--promoAlt-1 {
  color: #003764;
  font-size: min(3.733333333333334vw, 14px);
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 600;
}

.emotion-7 .amp-cms--subhead-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(16px, min(5.333333333333334vw, 20px));
  line-height: 1.5;
  letter-spacing: 0;
  font-weight: 500;
}

.emotion-7 .amp-cms--subhead-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(16px, min(4.8vw, 18px));
  line-height: 1.5;
  letter-spacing: 0;
  font-weight: 500;
}

.emotion-7 .amp-cms--subhead-3 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(16px, min(4.266666666666667vw, 16px));
  line-height: 1.5;
  letter-spacing: 0;
  font-weight: 500;
}

.emotion-8 {
  -webkit-box-flex: 1;
  -webkit-flex-grow: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
  -webkit-flex-basis: 0;
  -ms-flex-preferred-size: 0;
  flex-basis: 0;
  box-sizing: border-box;
  border: 1px solid #003764;
  border-radius: 8px 0 0 8px;
  background-color: #FFFFFF;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  color: #003764;
  margin: 0;
}

.emotion-8:not(:first-of-type) {
  margin-top: -1px;
}

.emotion-8:focus {
  outline: none;
  background-color: #003764;
  -webkit-text-decoration: underline;
  text-decoration: underline;
}

.emotion-8:focus,
.emotion-8:focus p * {
  color: #FFFFFF!important;
}

.emotion-8:hover {
  outline: none;
  background-color: #003764;
}

.emotion-8:hover,
.emotion-8:hover p * {
  color: #FFFFFF!important;
}

.emotion-12 {
  overflow: hidden;
}

.emotion-12 div.slick-track {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  gap: min(1.6vw, 6px);
}

.emotion-12 div.slick-track:before {
  display: none;
}

.emotion-12 div.slick-track:after {
  display: none;
}

.emotion-12 div.slick-slide {
  height: auto;
}

.emotion-12 div.slick-slide>div {
  height: 100%;
}

.emotion-13 {
  position: relative;
}

.emotion-13 .slick-slider {
  position: relative;
  display: block;
  box-sizing: border-box;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  -webkit-touch-callout: none;
  touch-action: pan-y;
  -webkit-tap-highlight-color: transparent;
  scroll-behavior: smooth;
}

.emotion-13 .slick-slider .slick-track,
.emotion-13 .slick-slider .slick-list {
  -webkit-transform: translate3d(0, 0, 0);
  -moz-transform: translate3d(0, 0, 0);
  -ms-transform: translate3d(0, 0, 0);
  transform: translate3d(0, 0, 0);
}

.emotion-13 .slick-list {
  position: relative;
  display: block;
  overflow-x: hidden;
  overflow-y: hidden;
  margin: 0;
  padding: 0;
}

.emotion-13 .slick-list:focus {
  outline: none;
}

.emotion-13 .slick-list.dragging {
  cursor: pointer;
  cursor: hand;
}

.emotion-13 .slick-track {
  position: relative;
  top: 0;
  left: 0;
  display: block;
  margin-left: auto;
  margin-right: auto;
}

.emotion-13 .slick-track:before,
.emotion-13 .slick-track:after {
  display: table;
  content: "";
}

.emotion-13 .slick-track:after {
  clear: both;
}

.emotion-13 .slick-slide {
  display: none;
  float: left;
  height: 100%;
  min-height: 1px;
}

.emotion-13 .slick-slide img {
  display: block;
}

.emotion-13 .slick-slide.slick-loading img {
  display: none;
}

.emotion-13 .slick-slide.dragging img {
  pointer-events: none;
}

.emotion-13 [dir="rtl"] .slick-slide {
  float: right;
}

.emotion-13 .slick-initialized .slick-slide,
.emotion-13 .slick-vertical .slick-slide {
  display: block;
}

.emotion-13 .slick-vertical .slick-slide {
  border: 1px solid transparent;
  height: auto;
}

.emotion-13 .slick-loading .slick-track,
.emotion-13 .slick-loading .slick-slide {
  visibility: hidden;
}

.emotion-13 .slick-loading .slick-list {
  background: #fff url("./ajax-loader.gif") center center no-repeat;
}

.emotion-13 .slick-arrow.slick-hidden {
  display: none;
}

.emotion-13 .slick-prev,
.emotion-13 .slick-next {
  font-size: 0;
  line-height: 0;
  position: absolute;
  top: 50%;
  display: block;
  padding: 0;
  cursor: pointer;
  color: transparent;
  border: none;
  outline: none;
  background: transparent;
}

.emotion-13 .slick-prev:hover,
.emotion-13 .slick-next:hover,
.emotion-13 .slick-prev:focus,
.emotion-13 .slick-next:focus {
  color: transparent;
  outline: none;
  background: transparent;
  opacity: 1;
}

.emotion-13 .slick-prev.slick-disabled,
.emotion-13 .slick-next.slick-disabled {
  opacity: 0.25;
}

.emotion-13 .slick-prev {
  left: -0px;
  -webkit-transform: translate(0, -50%) rotate(90deg);
  -moz-transform: translate(0, -50%) rotate(90deg);
  -ms-transform: translate(0, -50%) rotate(90deg);
  transform: translate(0, -50%) rotate(90deg);
}

.emotion-13 .slick-prev span {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-13 [dir="rtl"] .slick-prev {
  right: -0px;
  left: auto;
}

.emotion-13 .slick-next {
  right: -0px;
  -webkit-transform: translate(0, -50%) rotate(-90deg);
  -moz-transform: translate(0, -50%) rotate(-90deg);
  -ms-transform: translate(0, -50%) rotate(-90deg);
  transform: translate(0, -50%) rotate(-90deg);
}

.emotion-13 .slick-next span {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-13 [dir="rtl"] .slick-next {
  right: auto;
  left: -0px;
}

.emotion-13 .slick-dotted.slick-slider {
  margin-bottom: 30px;
}

.emotion-13 .slick-dots {
  position: absolute;
  bottom: -25px;
  display: block;
  width: 100%;
  padding: 0;
  margin: 0;
  list-style: none;
  text-align: center;
}

.emotion-13 .slick-dots li {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: 20px;
  height: 20px;
  margin: 0 5px;
  padding: 0;
  cursor: pointer;
}

.emotion-13 .slick-dots li.slick-active button:before {
  opacity: 0.75;
  color: black;
}

.emotion-13 .slick-dots li button {
  font-size: 0;
  line-height: 0;
  display: block;
  padding: 5px;
  cursor: pointer;
  color: transparent;
  border: 0;
  outline: none;
  background: transparent;
}

.emotion-13 .slick-dots li button:hover,
.emotion-13 .slick-dots li button:focus {
  outline: none;
}

.emotion-13 .slick-dots li button:hover:before,
.emotion-13 .slick-dots li button:focus:before,
.emotion-13 .slick-dots li button:hover:before,
.emotion-13 .slick-dots li button:focus:before {
  opacity: 1;
}

.emotion-13 .slick-dots li button:before {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 5px;
  height: 5px;
  content: "";
  opacity: 0.25;
  background-color: black;
  border-radius: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-14 {
  outline: none;
  position: relative;
  -webkit-flex: 1;
  -ms-flex: 1;
  flex: 1;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  min-width: auto;
}

.emotion-15 {
  overflow: hidden;
  -webkit-transition: background-color .25s ease;
  transition: background-color .25s ease;
  background-color: transparent;
  min-width: 133px;
  height: min(51.73599999999999vw, 194.01px);
  position: relative;
}

.emotion-16 {
  box-sizing: border-box;
  aspect-ratio: 133/194.01;
  position: relative;
  -webkit-transition: -webkit-transform .25s ease,opacity .25s ease;
  transition: transform .25s ease,opacity .25s ease;
  -webkit-transform: none;
  -moz-transform: none;
  -ms-transform: none;
  transform: none;
  opacity: 1;
  width: 100%;
  z-index: 1;
}

.emotion-17 {
  width: 100%;
  aspect-ratio: 133/194.01;
  object-fit: cover;
}

.emotion-18 {
  background: transparent;
  position: relative;
  box-sizing: border-box;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-box-flex: 1;
  -webkit-flex-grow: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
  z-index: 1;
}

.emotion-19 {
  height: 100%;
  padding: 0 6px;
  position: relative;
  background-color: #FFFFFF;
}

.emotion-20 {
  padding-top: 7px;
  text-decoration-color: #003764;
}

.emotion-20 .amp-cms--p {
  padding: 0;
  margin: 0;
  line-height: 0;
}

.emotion-20 .amp-cms--p>span {
  display: inline;
  white-space: break-spaces;
}

.emotion-20 .amp-cms--p a {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  position: relative;
  z-index: 2;
  pointer-events: auto;
}

.emotion-20 .amp-cms--p a:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-20 .amp-cms--legal-copy {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 10px;
  line-height: 1.5;
  letter-spacing: 0;
}

.emotion-20 sup {
  vertical-align: top;
  display: inline-block;
  margin-top: -0.20ex;
}

.emotion-20 sub {
  vertical-align: bottom;
  display: inline-block;
  margin-bottom: -0.20ex;
}

.emotion-20 .amp-cms--f-0 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 11px;
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-20 .amp-cms--f-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 11px;
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-20 .amp-cms--f-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 11px;
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-20 .amp-cms--fn-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 11px;
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-20 .amp-cms--body-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 14px;
  line-height: 1.5;
  letter-spacing: 0.336px;
  font-weight: 500;
}

.emotion-20 .amp-cms--body-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.5;
  letter-spacing: 0.288px;
  font-weight: 500;
}

.emotion-20 .amp-cms--body-3 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.5;
  letter-spacing: 0.288px;
}

.emotion-20 .amp-cms--body-4 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 10px;
  line-height: 1.5;
  letter-spacing: 0;
}

.emotion-20 .amp-cms--body-5 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 10px;
  line-height: 1.5;
  letter-spacing: 0;
}

.emotion-20 .amp-cms--eyebrow-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 23px;
  line-height: 1;
  letter-spacing: 0.92px;
  font-weight: 500;
}

.emotion-20 .amp-cms--eyebrow-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 19px;
  line-height: 1;
  letter-spacing: 0.76px;
  font-weight: 500;
}

.emotion-20 .amp-cms--eyebrow-3 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 14px;
  line-height: 1.0714285714285714;
  letter-spacing: 0.56px;
  font-weight: 500;
}

.emotion-20 .amp-cms--headline-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 68px;
  line-height: 1;
  letter-spacing: 0;
  font-weight: 700;
}

.emotion-20 .amp-cms--headline-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 50px;
  line-height: 1;
  letter-spacing: 0;
  font-weight: 700;
}

.emotion-20 .amp-cms--headline-3 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 43px;
  line-height: 1;
  letter-spacing: 0;
  font-weight: 700;
}

.emotion-20 .amp-cms--headline-4 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 36px;
  line-height: 1;
  letter-spacing: 0.72px;
  font-weight: 700;
}

.emotion-20 .amp-cms--headline-5 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 28px;
  line-height: 1;
  letter-spacing: 1.12px;
  font-weight: 700;
}

.emotion-20 .amp-cms--headline-6 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 24px;
  line-height: 1.0833333333333333;
  letter-spacing: 0.96px;
  font-weight: 700;
}

.emotion-20 .amp-cms--headline-7 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 20px;
  line-height: 1;
  letter-spacing: 0.48px;
  font-weight: 700;
}

.emotion-20 .amp-cms--headlineAlt-1 {
  color: #003764;
  font-size: 64px;
  line-height: 1;
  letter-spacing: -0.3px;
  font-weight: 400;
}

.emotion-20 .amp-cms--headlineAlt-2 {
  color: #003764;
  font-size: 54px;
  line-height: 1;
  letter-spacing: -0.3px;
  font-weight: 400;
}

.emotion-20 .amp-cms--headlineAlt-3 {
  color: #003764;
  font-size: 34px;
  line-height: 1;
  letter-spacing: -0.3px;
  font-weight: 400;
}

.emotion-20 .amp-cms--headlineAlt-4 {
  color: #003764;
  font-size: 24px;
  line-height: 1.4166666666666667;
  letter-spacing: -0.3px;
  font-weight: 400;
}

.emotion-20 .amp-cms--headlineAlt-5 {
  color: #003764;
  font-size: 14px;
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-20 .amp-cms--headlineAlt-6 {
  color: #003764;
  font-size: 14px;
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-20 .amp-cms--headlineAlt-7 {
  color: #003764;
  font-size: 14px;
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-20 .amp-cms--promo-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 60px;
  line-height: 1;
  letter-spacing: 0px;
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-20 .amp-cms--promo-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 40px;
  line-height: 1;
  letter-spacing: 0px;
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-20 .amp-cms--promoAlt-1 {
  color: #003764;
  font-size: 14px;
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 600;
}

.emotion-20 .amp-cms--subhead-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 20px;
  line-height: 1.5;
  letter-spacing: 0;
  font-weight: 500;
}

.emotion-20 .amp-cms--subhead-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 18px;
  line-height: 1.5;
  letter-spacing: 0;
  font-weight: 500;
}

.emotion-20 .amp-cms--subhead-3 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 16px;
  line-height: 1.5;
  letter-spacing: 0;
  font-weight: 500;
}

<div>
  <div
    style="font-family: var(--font-family-font1),sans-serif;"
  >
    <div
      class="emotion-0"
    >
      <article
        class="emotion-1"
      >
        <div
          class="emotion-2"
        >
          <div>
            <p
              class="amp-cms--p"
              style="text-align:left;"
            >
              <span
                class="amp-cms--headline-6"
              >
                Lorem ipsum dolor
              </span>
              <span
                class="amp-cms--headline-5"
              >
                 
              </span>
              <span
                class="amp-cms--body-3"
              >
                sit amet consectetur adipiscing elit sed do eiusmod.
              </span>
            </p>
          </div>
        </div>
        <div
          class="emotion-3"
        >
          <div
            class="emotion-4"
          >
            <div
              class="emotion-5"
            >
              <button
                class="emotion-6"
              >
                <div
                  class="emotion-7"
                >
                  <div>
                    <p
                      class="amp-cms--p"
                      style="text-align:left;"
                    >
                      <span
                        class="amp-cms--body-1"
                      >
                        Toggle 1
                      </span>
                    </p>
                  </div>
                </div>
              </button>
              <button
                class="emotion-8"
              >
                <div
                  class="emotion-7"
                >
                  <div>
                    <p
                      class="amp-cms--p"
                      style="text-align:left;"
                    >
                      <span
                        class="amp-cms--body-1"
                      >
                        Toggle 2
                      </span>
                    </p>
                  </div>
                </div>
              </button>
              <button
                class="emotion-8"
              >
                <div
                  class="emotion-7"
                >
                  <div>
                    <p
                      class="amp-cms--p"
                      style="text-align:left;"
                    >
                      <span
                        class="amp-cms--body-1"
                      >
                        Toggle 3
                      </span>
                    </p>
                  </div>
                </div>
              </button>
            </div>
            <div
              class="emotion-12"
            >
              <div
                class="emotion-13"
              >
                <div
                  class="slick-slider slick-initialized"
                  dir="ltr"
                >
                  <div
                    class="slick-list"
                  >
                    <div
                      class="slick-track"
                      style="opacity: 1; transform: translate3d(0px, 0px, 0px);"
                    >
                      <div
                        aria-hidden="false"
                        class="slick-slide slick-active slick-current"
                        data-index="0"
                        style="outline: none; width: 0px;"
                        tabindex="-1"
                      >
                        <div>
                          <a
                            class="emotion-14"
                            href="www.A.com"
                            target="_self"
                          >
                            <div
                              aria-live="polite"
                              class="emotion-15"
                            >
                              <div
                                aria-hidden="false"
                                class="emotion-16"
                              >
                                <img
                                  alt="Jeans"
                                  class="emotion-17"
                                  src=""
                                />
                              </div>
                            </div>
                            <div
                              class="emotion-18"
                            >
                              <div
                                class="emotion-19"
                              >
                                <div
                                  class="emotion-20"
                                >
                                  <div>
                                    <p
                                      class="amp-cms--p"
                                      style="text-align:left;"
                                    >
                                      <span
                                        class="amp-cms--body-1"
                                      >
                                        Category 1
                                      </span>
                                    </p>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </a>
                        </div>
                      </div>
                      <div
                        aria-hidden="true"
                        class="slick-slide"
                        data-index="1"
                        style="outline: none; width: 0px;"
                        tabindex="-1"
                      >
                        <div>
                          <a
                            class="emotion-14"
                            href="www.B.com"
                            target="_self"
                          >
                            <div
                              aria-live="polite"
                              class="emotion-15"
                            >
                              <div
                                aria-hidden="false"
                                class="emotion-16"
                              >
                                <img
                                  alt="Man"
                                  class="emotion-17"
                                  src=""
                                />
                              </div>
                            </div>
                            <div
                              class="emotion-18"
                            >
                              <div
                                class="emotion-19"
                              >
                                <div
                                  class="emotion-20"
                                >
                                  <div>
                                    <p
                                      class="amp-cms--p"
                                      style="text-align:left;"
                                    >
                                      <span
                                        class="amp-cms--body-1"
                                      >
                                        Category 2
                                      </span>
                                    </p>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </a>
                        </div>
                      </div>
                      <div
                        aria-hidden="true"
                        class="slick-slide"
                        data-index="2"
                        style="outline: none; width: 0px;"
                        tabindex="-1"
                      >
                        <div>
                          <a
                            class="emotion-14"
                            href="www.C.com"
                            target="_self"
                          >
                            <div
                              aria-live="polite"
                              class="emotion-15"
                            >
                              <div
                                aria-hidden="false"
                                class="emotion-16"
                              >
                                <img
                                  alt="Woman"
                                  class="emotion-17"
                                  src=""
                                />
                              </div>
                            </div>
                            <div
                              class="emotion-18"
                            >
                              <div
                                class="emotion-19"
                              >
                                <div
                                  class="emotion-20"
                                >
                                  <div>
                                    <p
                                      class="amp-cms--p"
                                      style="text-align:left;"
                                    >
                                      <span
                                        class="amp-cms--body-1"
                                      >
                                        Category 3
                                      </span>
                                    </p>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </a>
                        </div>
                      </div>
                      <div
                        aria-hidden="true"
                        class="slick-slide"
                        data-index="3"
                        style="outline: none; width: 0px;"
                        tabindex="-1"
                      >
                        <div>
                          <a
                            class="emotion-14"
                            href="www.gap.com"
                            target="_self"
                          >
                            <div
                              aria-live="polite"
                              class="emotion-15"
                            >
                              <div
                                aria-hidden="false"
                                class="emotion-16"
                              >
                                <img
                                  alt="Another Man"
                                  class="emotion-17"
                                  src=""
                                />
                              </div>
                            </div>
                            <div
                              class="emotion-18"
                            >
                              <div
                                class="emotion-19"
                              >
                                <div
                                  class="emotion-20"
                                >
                                  <div>
                                    <p
                                      class="amp-cms--p"
                                      style="text-align:left;"
                                    >
                                      <span
                                        class="amp-cms--body-1"
                                      >
                                        Category 4
                                      </span>
                                    </p>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </a>
                        </div>
                      </div>
                      <div
                        aria-hidden="true"
                        class="slick-slide"
                        data-index="4"
                        style="outline: none; width: 0px;"
                        tabindex="-1"
                      >
                        <div>
                          <a
                            class="emotion-14"
                            href="www.gap.com"
                            target="_self"
                          >
                            <div
                              aria-live="polite"
                              class="emotion-15"
                            >
                              <div
                                aria-hidden="false"
                                class="emotion-16"
                              >
                                <img
                                  alt="Posing"
                                  class="emotion-17"
                                  src=""
                                />
                              </div>
                            </div>
                            <div
                              class="emotion-18"
                            >
                              <div
                                class="emotion-19"
                              >
                                <div
                                  class="emotion-20"
                                >
                                  <div>
                                    <p
                                      class="amp-cms--p"
                                      style="text-align:left;"
                                    >
                                      <span
                                        class="amp-cms--body-1"
                                      >
                                        Category 5
                                      </span>
                                    </p>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </a>
                        </div>
                      </div>
                      <div
                        aria-hidden="true"
                        class="slick-slide"
                        data-index="5"
                        style="outline: none; width: 0px;"
                        tabindex="-1"
                      >
                        <div>
                          <a
                            class="emotion-14"
                            href="www.gap.com"
                            target="_self"
                          >
                            <div
                              aria-live="polite"
                              class="emotion-15"
                            >
                              <div
                                aria-hidden="false"
                                class="emotion-16"
                              >
                                <img
                                  alt="People"
                                  class="emotion-17"
                                  src=""
                                />
                              </div>
                            </div>
                            <div
                              class="emotion-18"
                            >
                              <div
                                class="emotion-19"
                              >
                                <div
                                  class="emotion-20"
                                >
                                  <div>
                                    <p
                                      class="amp-cms--p"
                                      style="text-align:left;"
                                    >
                                      <span
                                        class="amp-cms--body-1"
                                      >
                                        Category 6
                                      </span>
                                    </p>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </a>
                        </div>
                      </div>
                      <div
                        aria-hidden="true"
                        class="slick-slide"
                        data-index="6"
                        style="outline: none; width: 0px;"
                        tabindex="-1"
                      >
                        <div>
                          <a
                            class="emotion-14"
                            href="www.gap.com"
                            target="_self"
                          >
                            <div
                              aria-live="polite"
                              class="emotion-15"
                            >
                              <div
                                aria-hidden="false"
                                class="emotion-16"
                              >
                                <img
                                  alt="Yoga"
                                  class="emotion-17"
                                  src=""
                                />
                              </div>
                            </div>
                            <div
                              class="emotion-18"
                            >
                              <div
                                class="emotion-19"
                              >
                                <div
                                  class="emotion-20"
                                >
                                  <div>
                                    <p
                                      class="amp-cms--p"
                                      style="text-align:left;"
                                    >
                                      <span
                                        class="amp-cms--body-1"
                                      >
                                        Category 7
                                      </span>
                                    </p>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </a>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </article>
    </div>
  </div>
</div>
`;
