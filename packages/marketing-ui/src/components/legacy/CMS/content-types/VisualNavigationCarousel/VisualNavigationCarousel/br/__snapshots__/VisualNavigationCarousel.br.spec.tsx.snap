// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`VisualNavigationCarouselBR on desktop should match snapshot with no toggles 1`] = `
.emotion-0 {
  margin-left: auto;
  margin-right: auto;
  max-width: 1280px;
}

.emotion-1 {
  aspect-ratio: 1280/365;
}

.emotion-2 {
  padding-bottom: 32px;
  padding-inline: 0;
  padding-top: 40px;
}

.emotion-2 .amp-cms--p {
  padding: 0;
  margin: 0;
  line-height: 0;
}

.emotion-2 .amp-cms--p>span {
  display: inline;
  white-space: break-spaces;
}

.emotion-2 .amp-cms--p a {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  position: relative;
  z-index: 2;
  pointer-events: auto;
}

.emotion-2 .amp-cms--p a:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-2 .amp-cms--legal-copy {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(0.625vw, 12px));
  line-height: 1.5;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 350;
}

.emotion-2 sup {
  vertical-align: top;
  display: inline-block;
  margin-top: -0.25ex;
}

.emotion-2 sub {
  vertical-align: bottom;
  display: inline-block;
  margin-bottom: -0.25ex;
}

.emotion-2 .amp-cms--f-0 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.625vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-2 .amp-cms--f-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.625vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-2 .amp-cms--f-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.625vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-2 .amp-cms--fn-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.625vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-2 .amp-cms--body-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(1.0416666666666665vw, 20px));
  line-height: 1.5;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 350;
}

.emotion-2 .amp-cms--body-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(16px, min(0.9375vw, 18px));
  line-height: 1.4444444444444444;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 350;
}

.emotion-2 .amp-cms--body-3 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(0.8333333333333334vw, 16px));
  line-height: 1.375;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 350;
}

.emotion-2 .amp-cms--body-4 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(0.7291666666666666vw, 14px));
  line-height: 1.4285714285714286;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 350;
}

.emotion-2 .amp-cms--body-5 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(0.625vw, 12px));
  line-height: 1.5;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 350;
}

.emotion-2 .amp-cms--eyebrow-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(1.0416666666666665vw, 20px));
  line-height: 1;
  letter-spacing: min(0.078125vw, 1.5px);
  font-weight: 600;
}

.emotion-2 .amp-cms--eyebrow-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(0.8333333333333334vw, 16px));
  line-height: 1;
  letter-spacing: min(0.078125vw, 1.5px);
  font-weight: 600;
}

.emotion-2 .amp-cms--eyebrow-3 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(0.625vw, 12px));
  line-height: 1;
  letter-spacing: min(0.078125vw, 1.5px);
  font-weight: 600;
}

.emotion-2 .amp-cms--headline-1 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(48px, min(5vw, 96px));
  line-height: 1;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 300;
}

.emotion-2 .amp-cms--headline-2 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(40px, min(4.166666666666666vw, 80px));
  line-height: 1;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 300;
}

.emotion-2 .amp-cms--headline-3 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(32px, min(3.3333333333333335vw, 64px));
  line-height: 1;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 300;
}

.emotion-2 .amp-cms--headline-4 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(28px, min(2.5vw, 48px));
  line-height: 1;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 300;
}

.emotion-2 .amp-cms--headline-5 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(1.6666666666666667vw, 32px));
  line-height: 1.125;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 300;
}

.emotion-2 .amp-cms--headline-6 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, min(1.25vw, 24px));
  line-height: 1.3333333333333333;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 300;
}

.emotion-2 .amp-cms--headline-7 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(1.0416666666666665vw, 20px));
  line-height: 1.4;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 300;
}

.emotion-2 .amp-cms--headlineAlt-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(32px, min(3.3333333333333335vw, 64px));
  line-height: 1;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 400;
}

.emotion-2 .amp-cms--headlineAlt-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(2.5vw, 48px));
  line-height: 1;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 400;
}

.emotion-2 .amp-cms--headlineAlt-3 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(1.6666666666666667vw, 32px));
  line-height: 1.0625;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 400;
}

.emotion-2 .amp-cms--headlineAlt-4 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(60px, min(6.25vw, 120px));
  line-height: 1;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 300;
}

.emotion-2 .amp-cms--headlineAlt-5 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.8333333333333334vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-2 .amp-cms--headlineAlt-6 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.8333333333333334vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-2 .amp-cms--headlineAlt-7 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.8333333333333334vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-2 .amp-cms--promo-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(8px, min(0.5208333333333333vw, 10px));
  line-height: 1.6;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 350;
}

.emotion-2 .amp-cms--promo-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(4.166666666666666vw, 80px));
  line-height: 1;
  letter-spacing: min(0vw, 0px);
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-2 .amp-cms--promoAlt-1 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.8333333333333334vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 600;
}

.emotion-2 .amp-cms--subhead-1 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(0.9375vw, 18px));
  line-height: 1.3333333333333333;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 300;
}

.emotion-2 .amp-cms--subhead-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(16px, min(1.25vw, 24px));
  line-height: 1;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 350;
}

.emotion-2 .amp-cms--subhead-3 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(1.0416666666666665vw, 20px));
  line-height: 1;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 350;
}

.emotion-3 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  border-bottom: 1px solid #E5E5E5;
}

.emotion-4 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  margin-bottom: -1px;
}

.emotion-5 {
  display: grid;
  grid-auto-flow: column;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  grid-gap: 4px;
  position: relative;
  grid-auto-columns: 94px;
}

.emotion-6 {
  outline: none;
  position: relative;
  -webkit-flex: 1;
  -ms-flex: 1;
  flex: 1;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  height: 100%;
  max-width: 94px;
}

.emotion-7 {
  overflow: hidden;
  -webkit-transition: background-color .25s ease;
  transition: background-color .25s ease;
  background-color: transparent;
  position: relative;
}

.emotion-8 {
  box-sizing: border-box;
  aspect-ratio: 3/4;
  position: relative;
  -webkit-transition: -webkit-transform .25s ease,opacity .25s ease;
  transition: transform .25s ease,opacity .25s ease;
  -webkit-transform: none;
  -moz-transform: none;
  -ms-transform: none;
  transform: none;
  opacity: 1;
  width: 100%;
  z-index: 1;
}

.emotion-9 {
  width: 100%;
  aspect-ratio: 3/4;
  object-fit: cover;
}

.emotion-10 {
  background: transparent;
  position: relative;
  box-sizing: border-box;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-box-flex: 1;
  -webkit-flex-grow: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
  z-index: 1;
}

.emotion-11 {
  height: 100%;
  padding: 8px 10px;
  position: relative;
  background-color: white;
}

.emotion-12 .amp-cms--p {
  padding: 0;
  margin: 0;
  line-height: 0;
}

.emotion-12 .amp-cms--p>span {
  display: inline;
  white-space: break-spaces;
}

.emotion-12 .amp-cms--p a {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  position: relative;
  z-index: 2;
  pointer-events: auto;
}

.emotion-12 .amp-cms--p a:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-12 .amp-cms--legal-copy {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.5;
  letter-spacing: 0.5px;
  font-weight: 350;
}

.emotion-12 sup {
  vertical-align: top;
  display: inline-block;
  margin-top: -0.25ex;
}

.emotion-12 sub {
  vertical-align: bottom;
  display: inline-block;
  margin-bottom: -0.25ex;
}

.emotion-12 .amp-cms--f-0 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-12 .amp-cms--f-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-12 .amp-cms--f-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-12 .amp-cms--fn-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-12 .amp-cms--body-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 20px;
  line-height: 1.5;
  letter-spacing: 0.5px;
  font-weight: 350;
}

.emotion-12 .amp-cms--body-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 18px;
  line-height: 1.4444444444444444;
  letter-spacing: 0.5px;
  font-weight: 350;
}

.emotion-12 .amp-cms--body-3 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 16px;
  line-height: 1.375;
  letter-spacing: 0.5px;
  font-weight: 350;
}

.emotion-12 .amp-cms--body-4 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 14px;
  line-height: 1.4285714285714286;
  letter-spacing: 0.5px;
  font-weight: 350;
}

.emotion-12 .amp-cms--body-5 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.5;
  letter-spacing: 0.5px;
  font-weight: 350;
}

.emotion-12 .amp-cms--eyebrow-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 20px;
  line-height: 1;
  letter-spacing: 1.5px;
  font-weight: 600;
}

.emotion-12 .amp-cms--eyebrow-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 16px;
  line-height: 1;
  letter-spacing: 1.5px;
  font-weight: 600;
}

.emotion-12 .amp-cms--eyebrow-3 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1;
  letter-spacing: 1.5px;
  font-weight: 600;
}

.emotion-12 .amp-cms--headline-1 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 96px;
  line-height: 1;
  letter-spacing: 0.5px;
  font-weight: 300;
}

.emotion-12 .amp-cms--headline-2 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 80px;
  line-height: 1;
  letter-spacing: 0.5px;
  font-weight: 300;
}

.emotion-12 .amp-cms--headline-3 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 64px;
  line-height: 1;
  letter-spacing: 0.5px;
  font-weight: 300;
}

.emotion-12 .amp-cms--headline-4 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 48px;
  line-height: 1;
  letter-spacing: 0.5px;
  font-weight: 300;
}

.emotion-12 .amp-cms--headline-5 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 32px;
  line-height: 1.125;
  letter-spacing: 0.5px;
  font-weight: 300;
}

.emotion-12 .amp-cms--headline-6 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 24px;
  line-height: 1.3333333333333333;
  letter-spacing: 0.5px;
  font-weight: 300;
}

.emotion-12 .amp-cms--headline-7 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 20px;
  line-height: 1.4;
  letter-spacing: 0.5px;
  font-weight: 300;
}

.emotion-12 .amp-cms--headlineAlt-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 64px;
  line-height: 1;
  letter-spacing: 0.5px;
  font-weight: 400;
}

.emotion-12 .amp-cms--headlineAlt-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 48px;
  line-height: 1;
  letter-spacing: 0.5px;
  font-weight: 400;
}

.emotion-12 .amp-cms--headlineAlt-3 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 32px;
  line-height: 1.0625;
  letter-spacing: 0.5px;
  font-weight: 400;
}

.emotion-12 .amp-cms--headlineAlt-4 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 120px;
  line-height: 1;
  letter-spacing: 0.5px;
  font-weight: 300;
}

.emotion-12 .amp-cms--headlineAlt-5 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 16px;
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-12 .amp-cms--headlineAlt-6 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 16px;
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-12 .amp-cms--headlineAlt-7 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 16px;
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-12 .amp-cms--promo-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 10px;
  line-height: 1.6;
  letter-spacing: 0.5px;
  font-weight: 350;
}

.emotion-12 .amp-cms--promo-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 80px;
  line-height: 1;
  letter-spacing: 0px;
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-12 .amp-cms--promoAlt-1 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 16px;
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 600;
}

.emotion-12 .amp-cms--subhead-1 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 18px;
  line-height: 1.3333333333333333;
  letter-spacing: 0.5px;
  font-weight: 300;
}

.emotion-12 .amp-cms--subhead-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 24px;
  line-height: 1;
  letter-spacing: 0.5px;
  font-weight: 350;
}

.emotion-12 .amp-cms--subhead-3 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 20px;
  line-height: 1;
  letter-spacing: 0.5px;
  font-weight: 350;
}

.emotion-13 {
  padding-top: 8px;
}

.emotion-13 .amp-cms--p {
  padding: 0;
  margin: 0;
  line-height: 0;
}

.emotion-13 .amp-cms--p>span {
  display: inline;
  white-space: break-spaces;
}

.emotion-13 .amp-cms--p a {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  position: relative;
  z-index: 2;
  pointer-events: auto;
}

.emotion-13 .amp-cms--p a:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-13 .amp-cms--legal-copy {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.5;
  letter-spacing: 0.5px;
  font-weight: 350;
}

.emotion-13 sup {
  vertical-align: top;
  display: inline-block;
  margin-top: -0.25ex;
}

.emotion-13 sub {
  vertical-align: bottom;
  display: inline-block;
  margin-bottom: -0.25ex;
}

.emotion-13 .amp-cms--f-0 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-13 .amp-cms--f-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-13 .amp-cms--f-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-13 .amp-cms--fn-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-13 .amp-cms--body-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 20px;
  line-height: 1.5;
  letter-spacing: 0.5px;
  font-weight: 350;
}

.emotion-13 .amp-cms--body-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 18px;
  line-height: 1.4444444444444444;
  letter-spacing: 0.5px;
  font-weight: 350;
}

.emotion-13 .amp-cms--body-3 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 16px;
  line-height: 1.375;
  letter-spacing: 0.5px;
  font-weight: 350;
}

.emotion-13 .amp-cms--body-4 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 14px;
  line-height: 1.4285714285714286;
  letter-spacing: 0.5px;
  font-weight: 350;
}

.emotion-13 .amp-cms--body-5 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.5;
  letter-spacing: 0.5px;
  font-weight: 350;
}

.emotion-13 .amp-cms--eyebrow-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 20px;
  line-height: 1;
  letter-spacing: 1.5px;
  font-weight: 600;
}

.emotion-13 .amp-cms--eyebrow-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 16px;
  line-height: 1;
  letter-spacing: 1.5px;
  font-weight: 600;
}

.emotion-13 .amp-cms--eyebrow-3 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1;
  letter-spacing: 1.5px;
  font-weight: 600;
}

.emotion-13 .amp-cms--headline-1 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 96px;
  line-height: 1;
  letter-spacing: 0.5px;
  font-weight: 300;
}

.emotion-13 .amp-cms--headline-2 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 80px;
  line-height: 1;
  letter-spacing: 0.5px;
  font-weight: 300;
}

.emotion-13 .amp-cms--headline-3 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 64px;
  line-height: 1;
  letter-spacing: 0.5px;
  font-weight: 300;
}

.emotion-13 .amp-cms--headline-4 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 48px;
  line-height: 1;
  letter-spacing: 0.5px;
  font-weight: 300;
}

.emotion-13 .amp-cms--headline-5 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 32px;
  line-height: 1.125;
  letter-spacing: 0.5px;
  font-weight: 300;
}

.emotion-13 .amp-cms--headline-6 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 24px;
  line-height: 1.3333333333333333;
  letter-spacing: 0.5px;
  font-weight: 300;
}

.emotion-13 .amp-cms--headline-7 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 20px;
  line-height: 1.4;
  letter-spacing: 0.5px;
  font-weight: 300;
}

.emotion-13 .amp-cms--headlineAlt-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 64px;
  line-height: 1;
  letter-spacing: 0.5px;
  font-weight: 400;
}

.emotion-13 .amp-cms--headlineAlt-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 48px;
  line-height: 1;
  letter-spacing: 0.5px;
  font-weight: 400;
}

.emotion-13 .amp-cms--headlineAlt-3 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 32px;
  line-height: 1.0625;
  letter-spacing: 0.5px;
  font-weight: 400;
}

.emotion-13 .amp-cms--headlineAlt-4 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 120px;
  line-height: 1;
  letter-spacing: 0.5px;
  font-weight: 300;
}

.emotion-13 .amp-cms--headlineAlt-5 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 16px;
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-13 .amp-cms--headlineAlt-6 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 16px;
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-13 .amp-cms--headlineAlt-7 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 16px;
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-13 .amp-cms--promo-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 10px;
  line-height: 1.6;
  letter-spacing: 0.5px;
  font-weight: 350;
}

.emotion-13 .amp-cms--promo-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 80px;
  line-height: 1;
  letter-spacing: 0px;
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-13 .amp-cms--promoAlt-1 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 16px;
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 600;
}

.emotion-13 .amp-cms--subhead-1 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 18px;
  line-height: 1.3333333333333333;
  letter-spacing: 0.5px;
  font-weight: 300;
}

.emotion-13 .amp-cms--subhead-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 24px;
  line-height: 1;
  letter-spacing: 0.5px;
  font-weight: 350;
}

.emotion-13 .amp-cms--subhead-3 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 20px;
  line-height: 1;
  letter-spacing: 0.5px;
  font-weight: 350;
}

.emotion-14 {
  background-color: transparent;
  box-shadow: none;
  box-sizing: border-box;
  border: 0;
  border-bottom: 1px solid transparent;
  -webkit-transition: border-bottom-color 0.2s ease;
  transition: border-bottom-color 0.2s ease;
}

<div>
  <div
    style="font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;"
  >
    <div
      class="emotion-0"
    >
      <article
        class="emotion-1"
      >
        <div
          class="emotion-2"
        >
          <div>
            <p
              class="amp-cms--p"
              style="text-align:left;"
            >
              <span
                class="amp-cms--headline-5"
              >
                Lorem ipsum dolor
              </span>
              <span
                class="amp-cms--body-1"
              >
                 sit amet consectetur adipiscing elit sed do eiusmod.
              </span>
            </p>
          </div>
        </div>
        <div
          class="emotion-3"
        >
          <div
            class="emotion-4"
          >
            <div
              class="emotion-5"
            >
              <a
                class="category-card emotion-6"
                href="https://www.google.com"
                target="_self"
              >
                <div
                  aria-live="polite"
                  class="emotion-7"
                >
                  <div
                    aria-hidden="false"
                    class="emotion-8"
                  >
                    <img
                      alt="Woman"
                      class="emotion-9"
                      src=""
                    />
                  </div>
                </div>
                <div
                  class="emotion-10"
                >
                  <div
                    class="emotion-11"
                  >
                    <div
                      class="emotion-12"
                    >
                      <div>
                        <p
                          class="amp-cms--p"
                          style="text-align:center;"
                        >
                          <span
                            class="amp-cms--eyebrow-1"
                            style="font-weight:700"
                          >
                            Straight
                          </span>
                        </p>
                      </div>
                    </div>
                    <div
                      class="emotion-13"
                    >
                      <div>
                        <p
                          class="amp-cms--p"
                          style="text-align:center;"
                        >
                          <span
                            class="amp-cms--body-2"
                          >
                            sit amet consectetur adipiscing elit eiusmod.
                          </span>
                        </p>
                      </div>
                    </div>
                  </div>
                  <div
                    class="emotion-14"
                    data-testid="category-card-bottom-border"
                  />
                </div>
              </a>
              <a
                class="category-card emotion-6"
                href="https://www.google.com"
                target="_self"
              >
                <div
                  aria-live="polite"
                  class="emotion-7"
                >
                  <div
                    aria-hidden="false"
                    class="emotion-8"
                  >
                    <img
                      alt="Man"
                      class="emotion-9"
                      src=""
                    />
                  </div>
                </div>
                <div
                  class="emotion-10"
                >
                  <div
                    class="emotion-11"
                  >
                    <div
                      class="emotion-12"
                    >
                      <div>
                        <p
                          class="amp-cms--p"
                          style="text-align:center;"
                        >
                          <span
                            class="amp-cms--eyebrow-1"
                            style="font-weight:700"
                          >
                            Skinny
                          </span>
                        </p>
                      </div>
                    </div>
                    <div
                      class="emotion-13"
                    >
                      <div>
                        <p
                          class="amp-cms--p"
                          style="text-align:center;"
                        >
                          <span
                            class="amp-cms--body-2"
                          >
                            sit amet consectetur adipiscing elit eiusmod.
                          </span>
                        </p>
                      </div>
                    </div>
                  </div>
                  <div
                    class="emotion-14"
                    data-testid="category-card-bottom-border"
                  />
                </div>
              </a>
              <a
                class="category-card emotion-6"
                href="https://www.google.com"
                target="_self"
              >
                <div
                  aria-live="polite"
                  class="emotion-7"
                >
                  <div
                    aria-hidden="false"
                    class="emotion-8"
                  >
                    <img
                      alt="Jeans"
                      class="emotion-9"
                      src=""
                    />
                  </div>
                </div>
                <div
                  class="emotion-10"
                >
                  <div
                    class="emotion-11"
                  >
                    <div
                      class="emotion-12"
                    >
                      <div>
                        <p
                          class="amp-cms--p"
                          style="text-align:center;"
                        >
                          <span
                            class="amp-cms--eyebrow-1"
                            style="font-weight:700"
                          >
                            Super-skinny
                          </span>
                        </p>
                      </div>
                    </div>
                    <div
                      class="emotion-13"
                    >
                      <div>
                        <p
                          class="amp-cms--p"
                          style="text-align:center;"
                        >
                          <span
                            class="amp-cms--body-2"
                          >
                            sit amet consectetur adipiscing elit eiusmod.
                          </span>
                        </p>
                      </div>
                    </div>
                  </div>
                  <div
                    class="emotion-14"
                    data-testid="category-card-bottom-border"
                  />
                </div>
              </a>
              <a
                class="category-card emotion-6"
                href="https://www.gap.com"
                target="_self"
              >
                <div
                  aria-live="polite"
                  class="emotion-7"
                >
                  <div
                    aria-hidden="false"
                    class="emotion-8"
                  >
                    <img
                      alt="Another Man"
                      class="emotion-9"
                      src=""
                    />
                  </div>
                </div>
                <div
                  class="emotion-10"
                >
                  <div
                    class="emotion-11"
                  >
                    <div
                      class="emotion-12"
                    >
                      <div>
                        <p
                          class="amp-cms--p"
                          style="text-align:center;"
                        >
                          <span
                            class="amp-cms--eyebrow-1"
                            style="font-weight:700;text-decoration:underline"
                          >
                            Hover Headline
                          </span>
                        </p>
                      </div>
                    </div>
                    <div
                      class="emotion-13"
                    >
                      <div>
                        <p
                          class="amp-cms--p"
                          style="text-align:center;"
                        >
                          <span
                            class="amp-cms--body-2"
                          >
                            sit amet consectetur adipiscing elit eiusmod.
                          </span>
                        </p>
                      </div>
                    </div>
                  </div>
                  <div
                    class="emotion-14"
                    data-testid="category-card-bottom-border"
                  />
                </div>
              </a>
              <a
                class="category-card emotion-6"
                href="https://www.gap.com"
                target="_self"
              >
                <div
                  aria-live="polite"
                  class="emotion-7"
                >
                  <div
                    aria-hidden="false"
                    class="emotion-8"
                  >
                    <img
                      alt="Posing"
                      class="emotion-9"
                      src=""
                    />
                  </div>
                </div>
                <div
                  class="emotion-10"
                >
                  <div
                    class="emotion-11"
                  >
                    <div
                      class="emotion-12"
                    >
                      <div>
                        <p
                          class="amp-cms--p"
                          style="text-align:center;"
                        >
                          <span
                            class="amp-cms--eyebrow-1"
                            style="font-weight:700;text-decoration:underline"
                          >
                            Selected Headline
                          </span>
                        </p>
                      </div>
                    </div>
                    <div
                      class="emotion-13"
                    >
                      <div>
                        <p
                          class="amp-cms--p"
                          style="text-align:center;"
                        >
                          <span
                            class="amp-cms--body-2"
                          >
                            sit amet consectetur adipiscing elit eiusmod.
                          </span>
                        </p>
                      </div>
                    </div>
                  </div>
                  <div
                    class="emotion-14"
                    data-testid="category-card-bottom-border"
                  />
                </div>
              </a>
              <a
                class="category-card emotion-6"
                href="https://www.google.com"
                target="_self"
              >
                <div
                  aria-live="polite"
                  class="emotion-7"
                >
                  <div
                    aria-hidden="false"
                    class="emotion-8"
                  >
                    <img
                      alt="Yoga"
                      class="emotion-9"
                      src=""
                    />
                  </div>
                </div>
                <div
                  class="emotion-10"
                >
                  <div
                    class="emotion-11"
                  >
                    <div
                      class="emotion-12"
                    >
                      <div>
                        <p
                          class="amp-cms--p"
                          style="text-align:center;"
                        >
                          <span
                            class="amp-cms--eyebrow-1"
                            style="font-weight:700"
                          >
                            Wide-Leg
                          </span>
                        </p>
                      </div>
                    </div>
                    <div
                      class="emotion-13"
                    >
                      <div>
                        <p
                          class="amp-cms--p"
                          style="text-align:center;"
                        >
                          <span
                            class="amp-cms--body-2"
                          >
                            sit amet consectetur adipiscing elit eiusmod.
                          </span>
                        </p>
                      </div>
                    </div>
                  </div>
                  <div
                    class="emotion-14"
                    data-testid="category-card-bottom-border"
                  />
                </div>
              </a>
              <a
                class="category-card emotion-6"
                href="htttps://www.gap.com"
                target="_self"
              >
                <div
                  aria-live="polite"
                  class="emotion-7"
                >
                  <div
                    aria-hidden="false"
                    class="emotion-8"
                  >
                    <img
                      alt="People"
                      class="emotion-9"
                      src=""
                    />
                  </div>
                </div>
                <div
                  class="emotion-10"
                >
                  <div
                    class="emotion-11"
                  >
                    <div
                      class="emotion-12"
                    >
                      <div>
                        <p
                          class="amp-cms--p"
                          style="text-align:center;"
                        >
                          <span
                            class="amp-cms--eyebrow-1"
                            style="font-weight:700"
                          >
                            Skinny-Leg
                          </span>
                        </p>
                      </div>
                    </div>
                    <div
                      class="emotion-13"
                    >
                      <div>
                        <p
                          class="amp-cms--p"
                          style="text-align:center;"
                        >
                          <span
                            class="amp-cms--body-2"
                          >
                            sit amet consectetur adipiscing elit eiusmod.
                          </span>
                        </p>
                      </div>
                    </div>
                  </div>
                  <div
                    class="emotion-14"
                    data-testid="category-card-bottom-border"
                  />
                </div>
              </a>
              <a
                class="category-card emotion-6"
                href="https://www.google.com"
                target="_self"
              >
                <div
                  aria-live="polite"
                  class="emotion-7"
                >
                  <div
                    aria-hidden="false"
                    class="emotion-8"
                  >
                    <img
                      alt="Woman"
                      class="emotion-9"
                      src=""
                    />
                  </div>
                </div>
                <div
                  class="emotion-10"
                >
                  <div
                    class="emotion-11"
                  >
                    <div
                      class="emotion-12"
                    >
                      <div>
                        <p
                          class="amp-cms--p"
                          style="text-align:center;"
                        >
                          <span
                            class="amp-cms--eyebrow-1"
                            style="font-weight:700"
                          >
                            Hover
                          </span>
                        </p>
                      </div>
                    </div>
                    <div
                      class="emotion-13"
                    >
                      <div>
                        <p
                          class="amp-cms--p"
                          style="text-align:center;"
                        >
                          <span
                            class="amp-cms--body-2"
                          >
                            sit amet consectetur adipiscing elit sed do.
                          </span>
                        </p>
                      </div>
                    </div>
                  </div>
                  <div
                    class="emotion-14"
                    data-testid="category-card-bottom-border"
                  />
                </div>
              </a>
              <a
                class="category-card emotion-6"
                href="https://www.google.com"
                target="_self"
              >
                <div
                  aria-live="polite"
                  class="emotion-7"
                >
                  <div
                    aria-hidden="false"
                    class="emotion-8"
                  >
                    <img
                      alt="Man"
                      class="emotion-9"
                      src=""
                    />
                  </div>
                </div>
                <div
                  class="emotion-10"
                >
                  <div
                    class="emotion-11"
                  >
                    <div
                      class="emotion-12"
                    >
                      <div>
                        <p
                          class="amp-cms--p"
                          style="text-align:center;"
                        >
                          <span
                            class="amp-cms--eyebrow-1"
                            style="font-weight:700"
                          >
                            Selected
                          </span>
                        </p>
                      </div>
                    </div>
                    <div
                      class="emotion-13"
                    >
                      <div>
                        <p
                          class="amp-cms--p"
                          style="text-align:center;"
                        >
                          <span
                            class="amp-cms--body-2"
                          >
                            sit amet consectetur adipiscing elit sed do.
                          </span>
                        </p>
                      </div>
                    </div>
                  </div>
                  <div
                    class="emotion-14"
                    data-testid="category-card-bottom-border"
                  />
                </div>
              </a>
              <a
                class="category-card emotion-6"
                href="https://www,gap.com"
                target="_self"
              >
                <div
                  aria-live="polite"
                  class="emotion-7"
                >
                  <div
                    aria-hidden="false"
                    class="emotion-8"
                  >
                    <img
                      alt="Another Man"
                      class="emotion-9"
                      src=""
                    />
                  </div>
                </div>
                <div
                  class="emotion-10"
                >
                  <div
                    class="emotion-11"
                  >
                    <div
                      class="emotion-12"
                    >
                      <div>
                        <p
                          class="amp-cms--p"
                          style="text-align:center;"
                        >
                          <span
                            class="amp-cms--eyebrow-1"
                            style="font-weight:700"
                          >
                            Card 10
                          </span>
                        </p>
                      </div>
                    </div>
                    <div
                      class="emotion-13"
                    >
                      <div>
                        <p
                          class="amp-cms--p"
                          style="text-align:center;"
                        >
                          <span
                            class="amp-cms--body-2"
                          >
                            sit amet consectetur adipiscing elit sed do.
                          </span>
                        </p>
                      </div>
                    </div>
                  </div>
                  <div
                    class="emotion-14"
                    data-testid="category-card-bottom-border"
                  />
                </div>
              </a>
            </div>
          </div>
        </div>
      </article>
    </div>
  </div>
</div>
`;

exports[`VisualNavigationCarouselBR on desktop should match snapshot with toggles 1`] = `
.emotion-0 {
  margin-left: auto;
  margin-right: auto;
  max-width: 1280px;
}

.emotion-1 {
  aspect-ratio: 1280/365;
}

.emotion-2 {
  padding-bottom: 32px;
  padding-inline: 0;
  padding-top: 40px;
}

.emotion-2 .amp-cms--p {
  padding: 0;
  margin: 0;
  line-height: 0;
}

.emotion-2 .amp-cms--p>span {
  display: inline;
  white-space: break-spaces;
}

.emotion-2 .amp-cms--p a {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  position: relative;
  z-index: 2;
  pointer-events: auto;
}

.emotion-2 .amp-cms--p a:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-2 .amp-cms--legal-copy {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(0.625vw, 12px));
  line-height: 1.5;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 350;
}

.emotion-2 sup {
  vertical-align: top;
  display: inline-block;
  margin-top: -0.25ex;
}

.emotion-2 sub {
  vertical-align: bottom;
  display: inline-block;
  margin-bottom: -0.25ex;
}

.emotion-2 .amp-cms--f-0 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.625vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-2 .amp-cms--f-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.625vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-2 .amp-cms--f-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.625vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-2 .amp-cms--fn-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.625vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-2 .amp-cms--body-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(1.0416666666666665vw, 20px));
  line-height: 1.5;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 350;
}

.emotion-2 .amp-cms--body-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(16px, min(0.9375vw, 18px));
  line-height: 1.4444444444444444;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 350;
}

.emotion-2 .amp-cms--body-3 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(0.8333333333333334vw, 16px));
  line-height: 1.375;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 350;
}

.emotion-2 .amp-cms--body-4 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(0.7291666666666666vw, 14px));
  line-height: 1.4285714285714286;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 350;
}

.emotion-2 .amp-cms--body-5 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(0.625vw, 12px));
  line-height: 1.5;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 350;
}

.emotion-2 .amp-cms--eyebrow-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(1.0416666666666665vw, 20px));
  line-height: 1;
  letter-spacing: min(0.078125vw, 1.5px);
  font-weight: 600;
}

.emotion-2 .amp-cms--eyebrow-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(0.8333333333333334vw, 16px));
  line-height: 1;
  letter-spacing: min(0.078125vw, 1.5px);
  font-weight: 600;
}

.emotion-2 .amp-cms--eyebrow-3 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(0.625vw, 12px));
  line-height: 1;
  letter-spacing: min(0.078125vw, 1.5px);
  font-weight: 600;
}

.emotion-2 .amp-cms--headline-1 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(48px, min(5vw, 96px));
  line-height: 1;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 300;
}

.emotion-2 .amp-cms--headline-2 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(40px, min(4.166666666666666vw, 80px));
  line-height: 1;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 300;
}

.emotion-2 .amp-cms--headline-3 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(32px, min(3.3333333333333335vw, 64px));
  line-height: 1;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 300;
}

.emotion-2 .amp-cms--headline-4 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(28px, min(2.5vw, 48px));
  line-height: 1;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 300;
}

.emotion-2 .amp-cms--headline-5 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(1.6666666666666667vw, 32px));
  line-height: 1.125;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 300;
}

.emotion-2 .amp-cms--headline-6 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, min(1.25vw, 24px));
  line-height: 1.3333333333333333;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 300;
}

.emotion-2 .amp-cms--headline-7 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(1.0416666666666665vw, 20px));
  line-height: 1.4;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 300;
}

.emotion-2 .amp-cms--headlineAlt-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(32px, min(3.3333333333333335vw, 64px));
  line-height: 1;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 400;
}

.emotion-2 .amp-cms--headlineAlt-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(2.5vw, 48px));
  line-height: 1;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 400;
}

.emotion-2 .amp-cms--headlineAlt-3 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(1.6666666666666667vw, 32px));
  line-height: 1.0625;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 400;
}

.emotion-2 .amp-cms--headlineAlt-4 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(60px, min(6.25vw, 120px));
  line-height: 1;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 300;
}

.emotion-2 .amp-cms--headlineAlt-5 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.8333333333333334vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-2 .amp-cms--headlineAlt-6 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.8333333333333334vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-2 .amp-cms--headlineAlt-7 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.8333333333333334vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-2 .amp-cms--promo-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(8px, min(0.5208333333333333vw, 10px));
  line-height: 1.6;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 350;
}

.emotion-2 .amp-cms--promo-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(4.166666666666666vw, 80px));
  line-height: 1;
  letter-spacing: min(0vw, 0px);
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-2 .amp-cms--promoAlt-1 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.8333333333333334vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 600;
}

.emotion-2 .amp-cms--subhead-1 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(0.9375vw, 18px));
  line-height: 1.3333333333333333;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 300;
}

.emotion-2 .amp-cms--subhead-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(16px, min(1.25vw, 24px));
  line-height: 1;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 350;
}

.emotion-2 .amp-cms--subhead-3 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(1.0416666666666665vw, 20px));
  line-height: 1;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 350;
}

.emotion-3 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  border-bottom: 1px solid #E5E5E5;
}

.emotion-4 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  margin-bottom: -1px;
}

.emotion-5 {
  display: grid;
  grid-auto-flow: column;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  grid-gap: 4px;
  position: relative;
  grid-auto-columns: 94px;
}

.emotion-6 {
  outline: none;
  position: relative;
  -webkit-flex: 1;
  -ms-flex: 1;
  flex: 1;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  height: 100%;
  max-width: 94px;
}

.emotion-7 {
  overflow: hidden;
  -webkit-transition: background-color .25s ease;
  transition: background-color .25s ease;
  background-color: transparent;
  position: relative;
}

.emotion-8 {
  box-sizing: border-box;
  aspect-ratio: 3/4;
  position: relative;
  -webkit-transition: -webkit-transform .25s ease,opacity .25s ease;
  transition: transform .25s ease,opacity .25s ease;
  -webkit-transform: none;
  -moz-transform: none;
  -ms-transform: none;
  transform: none;
  opacity: 1;
  width: 100%;
  z-index: 1;
}

.emotion-9 {
  width: 100%;
  aspect-ratio: 3/4;
  object-fit: cover;
}

.emotion-10 {
  background: transparent;
  position: relative;
  box-sizing: border-box;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-box-flex: 1;
  -webkit-flex-grow: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
  z-index: 1;
}

.emotion-11 {
  height: 100%;
  padding: 8px 10px;
  position: relative;
  background-color: white;
}

.emotion-12 .amp-cms--p {
  padding: 0;
  margin: 0;
  line-height: 0;
}

.emotion-12 .amp-cms--p>span {
  display: inline;
  white-space: break-spaces;
}

.emotion-12 .amp-cms--p a {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  position: relative;
  z-index: 2;
  pointer-events: auto;
}

.emotion-12 .amp-cms--p a:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-12 .amp-cms--legal-copy {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.5;
  letter-spacing: 0.5px;
  font-weight: 350;
}

.emotion-12 sup {
  vertical-align: top;
  display: inline-block;
  margin-top: -0.25ex;
}

.emotion-12 sub {
  vertical-align: bottom;
  display: inline-block;
  margin-bottom: -0.25ex;
}

.emotion-12 .amp-cms--f-0 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-12 .amp-cms--f-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-12 .amp-cms--f-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-12 .amp-cms--fn-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-12 .amp-cms--body-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 20px;
  line-height: 1.5;
  letter-spacing: 0.5px;
  font-weight: 350;
}

.emotion-12 .amp-cms--body-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 18px;
  line-height: 1.4444444444444444;
  letter-spacing: 0.5px;
  font-weight: 350;
}

.emotion-12 .amp-cms--body-3 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 16px;
  line-height: 1.375;
  letter-spacing: 0.5px;
  font-weight: 350;
}

.emotion-12 .amp-cms--body-4 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 14px;
  line-height: 1.4285714285714286;
  letter-spacing: 0.5px;
  font-weight: 350;
}

.emotion-12 .amp-cms--body-5 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.5;
  letter-spacing: 0.5px;
  font-weight: 350;
}

.emotion-12 .amp-cms--eyebrow-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 20px;
  line-height: 1;
  letter-spacing: 1.5px;
  font-weight: 600;
}

.emotion-12 .amp-cms--eyebrow-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 16px;
  line-height: 1;
  letter-spacing: 1.5px;
  font-weight: 600;
}

.emotion-12 .amp-cms--eyebrow-3 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1;
  letter-spacing: 1.5px;
  font-weight: 600;
}

.emotion-12 .amp-cms--headline-1 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 96px;
  line-height: 1;
  letter-spacing: 0.5px;
  font-weight: 300;
}

.emotion-12 .amp-cms--headline-2 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 80px;
  line-height: 1;
  letter-spacing: 0.5px;
  font-weight: 300;
}

.emotion-12 .amp-cms--headline-3 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 64px;
  line-height: 1;
  letter-spacing: 0.5px;
  font-weight: 300;
}

.emotion-12 .amp-cms--headline-4 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 48px;
  line-height: 1;
  letter-spacing: 0.5px;
  font-weight: 300;
}

.emotion-12 .amp-cms--headline-5 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 32px;
  line-height: 1.125;
  letter-spacing: 0.5px;
  font-weight: 300;
}

.emotion-12 .amp-cms--headline-6 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 24px;
  line-height: 1.3333333333333333;
  letter-spacing: 0.5px;
  font-weight: 300;
}

.emotion-12 .amp-cms--headline-7 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 20px;
  line-height: 1.4;
  letter-spacing: 0.5px;
  font-weight: 300;
}

.emotion-12 .amp-cms--headlineAlt-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 64px;
  line-height: 1;
  letter-spacing: 0.5px;
  font-weight: 400;
}

.emotion-12 .amp-cms--headlineAlt-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 48px;
  line-height: 1;
  letter-spacing: 0.5px;
  font-weight: 400;
}

.emotion-12 .amp-cms--headlineAlt-3 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 32px;
  line-height: 1.0625;
  letter-spacing: 0.5px;
  font-weight: 400;
}

.emotion-12 .amp-cms--headlineAlt-4 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 120px;
  line-height: 1;
  letter-spacing: 0.5px;
  font-weight: 300;
}

.emotion-12 .amp-cms--headlineAlt-5 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 16px;
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-12 .amp-cms--headlineAlt-6 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 16px;
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-12 .amp-cms--headlineAlt-7 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 16px;
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-12 .amp-cms--promo-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 10px;
  line-height: 1.6;
  letter-spacing: 0.5px;
  font-weight: 350;
}

.emotion-12 .amp-cms--promo-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 80px;
  line-height: 1;
  letter-spacing: 0px;
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-12 .amp-cms--promoAlt-1 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 16px;
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 600;
}

.emotion-12 .amp-cms--subhead-1 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 18px;
  line-height: 1.3333333333333333;
  letter-spacing: 0.5px;
  font-weight: 300;
}

.emotion-12 .amp-cms--subhead-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 24px;
  line-height: 1;
  letter-spacing: 0.5px;
  font-weight: 350;
}

.emotion-12 .amp-cms--subhead-3 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 20px;
  line-height: 1;
  letter-spacing: 0.5px;
  font-weight: 350;
}

.emotion-13 {
  padding-top: 8px;
}

.emotion-13 .amp-cms--p {
  padding: 0;
  margin: 0;
  line-height: 0;
}

.emotion-13 .amp-cms--p>span {
  display: inline;
  white-space: break-spaces;
}

.emotion-13 .amp-cms--p a {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  position: relative;
  z-index: 2;
  pointer-events: auto;
}

.emotion-13 .amp-cms--p a:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-13 .amp-cms--legal-copy {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.5;
  letter-spacing: 0.5px;
  font-weight: 350;
}

.emotion-13 sup {
  vertical-align: top;
  display: inline-block;
  margin-top: -0.25ex;
}

.emotion-13 sub {
  vertical-align: bottom;
  display: inline-block;
  margin-bottom: -0.25ex;
}

.emotion-13 .amp-cms--f-0 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-13 .amp-cms--f-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-13 .amp-cms--f-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-13 .amp-cms--fn-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-13 .amp-cms--body-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 20px;
  line-height: 1.5;
  letter-spacing: 0.5px;
  font-weight: 350;
}

.emotion-13 .amp-cms--body-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 18px;
  line-height: 1.4444444444444444;
  letter-spacing: 0.5px;
  font-weight: 350;
}

.emotion-13 .amp-cms--body-3 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 16px;
  line-height: 1.375;
  letter-spacing: 0.5px;
  font-weight: 350;
}

.emotion-13 .amp-cms--body-4 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 14px;
  line-height: 1.4285714285714286;
  letter-spacing: 0.5px;
  font-weight: 350;
}

.emotion-13 .amp-cms--body-5 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.5;
  letter-spacing: 0.5px;
  font-weight: 350;
}

.emotion-13 .amp-cms--eyebrow-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 20px;
  line-height: 1;
  letter-spacing: 1.5px;
  font-weight: 600;
}

.emotion-13 .amp-cms--eyebrow-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 16px;
  line-height: 1;
  letter-spacing: 1.5px;
  font-weight: 600;
}

.emotion-13 .amp-cms--eyebrow-3 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1;
  letter-spacing: 1.5px;
  font-weight: 600;
}

.emotion-13 .amp-cms--headline-1 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 96px;
  line-height: 1;
  letter-spacing: 0.5px;
  font-weight: 300;
}

.emotion-13 .amp-cms--headline-2 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 80px;
  line-height: 1;
  letter-spacing: 0.5px;
  font-weight: 300;
}

.emotion-13 .amp-cms--headline-3 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 64px;
  line-height: 1;
  letter-spacing: 0.5px;
  font-weight: 300;
}

.emotion-13 .amp-cms--headline-4 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 48px;
  line-height: 1;
  letter-spacing: 0.5px;
  font-weight: 300;
}

.emotion-13 .amp-cms--headline-5 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 32px;
  line-height: 1.125;
  letter-spacing: 0.5px;
  font-weight: 300;
}

.emotion-13 .amp-cms--headline-6 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 24px;
  line-height: 1.3333333333333333;
  letter-spacing: 0.5px;
  font-weight: 300;
}

.emotion-13 .amp-cms--headline-7 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 20px;
  line-height: 1.4;
  letter-spacing: 0.5px;
  font-weight: 300;
}

.emotion-13 .amp-cms--headlineAlt-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 64px;
  line-height: 1;
  letter-spacing: 0.5px;
  font-weight: 400;
}

.emotion-13 .amp-cms--headlineAlt-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 48px;
  line-height: 1;
  letter-spacing: 0.5px;
  font-weight: 400;
}

.emotion-13 .amp-cms--headlineAlt-3 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 32px;
  line-height: 1.0625;
  letter-spacing: 0.5px;
  font-weight: 400;
}

.emotion-13 .amp-cms--headlineAlt-4 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 120px;
  line-height: 1;
  letter-spacing: 0.5px;
  font-weight: 300;
}

.emotion-13 .amp-cms--headlineAlt-5 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 16px;
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-13 .amp-cms--headlineAlt-6 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 16px;
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-13 .amp-cms--headlineAlt-7 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 16px;
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-13 .amp-cms--promo-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 10px;
  line-height: 1.6;
  letter-spacing: 0.5px;
  font-weight: 350;
}

.emotion-13 .amp-cms--promo-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 80px;
  line-height: 1;
  letter-spacing: 0px;
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-13 .amp-cms--promoAlt-1 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 16px;
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 600;
}

.emotion-13 .amp-cms--subhead-1 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 18px;
  line-height: 1.3333333333333333;
  letter-spacing: 0.5px;
  font-weight: 300;
}

.emotion-13 .amp-cms--subhead-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 24px;
  line-height: 1;
  letter-spacing: 0.5px;
  font-weight: 350;
}

.emotion-13 .amp-cms--subhead-3 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 20px;
  line-height: 1;
  letter-spacing: 0.5px;
  font-weight: 350;
}

.emotion-14 {
  background-color: transparent;
  box-shadow: none;
  box-sizing: border-box;
  border: 0;
  border-bottom: 1px solid transparent;
  -webkit-transition: border-bottom-color 0.2s ease;
  transition: border-bottom-color 0.2s ease;
}

<div>
  <div
    style="font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;"
  >
    <div
      class="emotion-0"
    >
      <article
        class="emotion-1"
      >
        <div
          class="emotion-2"
        >
          <div>
            <p
              class="amp-cms--p"
              style="text-align:center;"
            >
              <span
                class="amp-cms--headline-5"
              >
                Lorem ipsum dolor
              </span>
              <span
                class="amp-cms--body-1"
              >
                 sit amet consectetur adipiscing elit sed do eiusmod.
              </span>
            </p>
          </div>
        </div>
        <div
          class="emotion-3"
        >
          <div
            class="emotion-4"
          >
            <div
              class="emotion-5"
            >
              <a
                class="category-card emotion-6"
                href="https://www.google.com"
                target="_self"
              >
                <div
                  aria-live="polite"
                  class="emotion-7"
                >
                  <div
                    aria-hidden="false"
                    class="emotion-8"
                  >
                    <img
                      alt="Woman"
                      class="emotion-9"
                      src=""
                    />
                  </div>
                </div>
                <div
                  class="emotion-10"
                >
                  <div
                    class="emotion-11"
                  >
                    <div
                      class="emotion-12"
                    >
                      <div>
                        <p
                          class="amp-cms--p"
                          style="text-align:center;"
                        >
                          <span
                            class="amp-cms--eyebrow-1"
                            style="font-weight:700"
                          >
                            Straight
                          </span>
                        </p>
                      </div>
                    </div>
                    <div
                      class="emotion-13"
                    >
                      <div>
                        <p
                          class="amp-cms--p"
                          style="text-align:center;"
                        >
                          <span
                            class="amp-cms--body-2"
                          >
                            sit amet consectetur adipiscing elit eiusmod.
                          </span>
                        </p>
                      </div>
                    </div>
                  </div>
                  <div
                    class="emotion-14"
                    data-testid="category-card-bottom-border"
                  />
                </div>
              </a>
              <a
                class="category-card emotion-6"
                href="https://www.google.com"
                target="_self"
              >
                <div
                  aria-live="polite"
                  class="emotion-7"
                >
                  <div
                    aria-hidden="false"
                    class="emotion-8"
                  >
                    <img
                      alt="Man"
                      class="emotion-9"
                      src=""
                    />
                  </div>
                </div>
                <div
                  class="emotion-10"
                >
                  <div
                    class="emotion-11"
                  >
                    <div
                      class="emotion-12"
                    >
                      <div>
                        <p
                          class="amp-cms--p"
                          style="text-align:center;"
                        >
                          <span
                            class="amp-cms--eyebrow-1"
                            style="font-weight:700"
                          >
                            Skinny
                          </span>
                        </p>
                      </div>
                    </div>
                    <div
                      class="emotion-13"
                    >
                      <div>
                        <p
                          class="amp-cms--p"
                          style="text-align:center;"
                        >
                          <span
                            class="amp-cms--body-2"
                          >
                            sit amet consectetur adipiscing elit eiusmod.
                          </span>
                        </p>
                      </div>
                    </div>
                  </div>
                  <div
                    class="emotion-14"
                    data-testid="category-card-bottom-border"
                  />
                </div>
              </a>
              <a
                class="category-card emotion-6"
                href="https://www.google.com"
                target="_self"
              >
                <div
                  aria-live="polite"
                  class="emotion-7"
                >
                  <div
                    aria-hidden="false"
                    class="emotion-8"
                  >
                    <img
                      alt="Jeans"
                      class="emotion-9"
                      src=""
                    />
                  </div>
                </div>
                <div
                  class="emotion-10"
                >
                  <div
                    class="emotion-11"
                  >
                    <div
                      class="emotion-12"
                    >
                      <div>
                        <p
                          class="amp-cms--p"
                          style="text-align:center;"
                        >
                          <span
                            class="amp-cms--eyebrow-1"
                            style="font-weight:700"
                          >
                            Super-skinny
                          </span>
                        </p>
                      </div>
                    </div>
                    <div
                      class="emotion-13"
                    >
                      <div>
                        <p
                          class="amp-cms--p"
                          style="text-align:center;"
                        >
                          <span
                            class="amp-cms--body-2"
                          >
                            sit amet consectetur adipiscing elit eiusmod.
                          </span>
                        </p>
                      </div>
                    </div>
                  </div>
                  <div
                    class="emotion-14"
                    data-testid="category-card-bottom-border"
                  />
                </div>
              </a>
              <a
                class="category-card emotion-6"
                href="https://www.gap.com"
                target="_self"
              >
                <div
                  aria-live="polite"
                  class="emotion-7"
                >
                  <div
                    aria-hidden="false"
                    class="emotion-8"
                  >
                    <img
                      alt="Another Man"
                      class="emotion-9"
                      src=""
                    />
                  </div>
                </div>
                <div
                  class="emotion-10"
                >
                  <div
                    class="emotion-11"
                  >
                    <div
                      class="emotion-12"
                    >
                      <div>
                        <p
                          class="amp-cms--p"
                          style="text-align:center;"
                        >
                          <span
                            class="amp-cms--eyebrow-1"
                            style="font-weight:700;text-decoration:underline"
                          >
                            Hover Headline
                          </span>
                        </p>
                      </div>
                    </div>
                    <div
                      class="emotion-13"
                    >
                      <div>
                        <p
                          class="amp-cms--p"
                          style="text-align:center;"
                        >
                          <span
                            class="amp-cms--body-2"
                          >
                            sit amet consectetur adipiscing elit eiusmod.
                          </span>
                        </p>
                      </div>
                    </div>
                  </div>
                  <div
                    class="emotion-14"
                    data-testid="category-card-bottom-border"
                  />
                </div>
              </a>
              <a
                class="category-card emotion-6"
                href="https://www.gap.com"
                target="_self"
              >
                <div
                  aria-live="polite"
                  class="emotion-7"
                >
                  <div
                    aria-hidden="false"
                    class="emotion-8"
                  >
                    <img
                      alt="Posing"
                      class="emotion-9"
                      src=""
                    />
                  </div>
                </div>
                <div
                  class="emotion-10"
                >
                  <div
                    class="emotion-11"
                  >
                    <div
                      class="emotion-12"
                    >
                      <div>
                        <p
                          class="amp-cms--p"
                          style="text-align:center;"
                        >
                          <span
                            class="amp-cms--eyebrow-1"
                            style="font-weight:700;text-decoration:underline"
                          >
                            Selected Headline
                          </span>
                        </p>
                      </div>
                    </div>
                    <div
                      class="emotion-13"
                    >
                      <div>
                        <p
                          class="amp-cms--p"
                          style="text-align:center;"
                        >
                          <span
                            class="amp-cms--body-2"
                          >
                            sit amet consectetur adipiscing elit eiusmod.
                          </span>
                        </p>
                      </div>
                    </div>
                  </div>
                  <div
                    class="emotion-14"
                    data-testid="category-card-bottom-border"
                  />
                </div>
              </a>
              <a
                class="category-card emotion-6"
                href="https://www.google.com"
                target="_self"
              >
                <div
                  aria-live="polite"
                  class="emotion-7"
                >
                  <div
                    aria-hidden="false"
                    class="emotion-8"
                  >
                    <img
                      alt="Yoga"
                      class="emotion-9"
                      src=""
                    />
                  </div>
                </div>
                <div
                  class="emotion-10"
                >
                  <div
                    class="emotion-11"
                  >
                    <div
                      class="emotion-12"
                    >
                      <div>
                        <p
                          class="amp-cms--p"
                          style="text-align:center;"
                        >
                          <span
                            class="amp-cms--eyebrow-1"
                            style="font-weight:700"
                          >
                            Wide-Leg
                          </span>
                        </p>
                      </div>
                    </div>
                    <div
                      class="emotion-13"
                    >
                      <div>
                        <p
                          class="amp-cms--p"
                          style="text-align:center;"
                        >
                          <span
                            class="amp-cms--body-2"
                          >
                            sit amet consectetur adipiscing elit eiusmod.
                          </span>
                        </p>
                      </div>
                    </div>
                  </div>
                  <div
                    class="emotion-14"
                    data-testid="category-card-bottom-border"
                  />
                </div>
              </a>
              <a
                class="category-card emotion-6"
                href="htttps://www.gap.com"
                target="_self"
              >
                <div
                  aria-live="polite"
                  class="emotion-7"
                >
                  <div
                    aria-hidden="false"
                    class="emotion-8"
                  >
                    <img
                      alt="People"
                      class="emotion-9"
                      src=""
                    />
                  </div>
                </div>
                <div
                  class="emotion-10"
                >
                  <div
                    class="emotion-11"
                  >
                    <div
                      class="emotion-12"
                    >
                      <div>
                        <p
                          class="amp-cms--p"
                          style="text-align:center;"
                        >
                          <span
                            class="amp-cms--eyebrow-1"
                            style="font-weight:700"
                          >
                            Skinny-Leg
                          </span>
                        </p>
                      </div>
                    </div>
                    <div
                      class="emotion-13"
                    >
                      <div>
                        <p
                          class="amp-cms--p"
                          style="text-align:center;"
                        >
                          <span
                            class="amp-cms--body-2"
                          >
                            sit amet consectetur adipiscing elit eiusmod.
                          </span>
                        </p>
                      </div>
                    </div>
                  </div>
                  <div
                    class="emotion-14"
                    data-testid="category-card-bottom-border"
                  />
                </div>
              </a>
              <a
                class="category-card emotion-6"
                href="https://www.google.com"
                target="_self"
              >
                <div
                  aria-live="polite"
                  class="emotion-7"
                >
                  <div
                    aria-hidden="false"
                    class="emotion-8"
                  >
                    <img
                      alt="Woman"
                      class="emotion-9"
                      src=""
                    />
                  </div>
                </div>
                <div
                  class="emotion-10"
                >
                  <div
                    class="emotion-11"
                  >
                    <div
                      class="emotion-12"
                    >
                      <div>
                        <p
                          class="amp-cms--p"
                          style="text-align:center;"
                        >
                          <span
                            class="amp-cms--eyebrow-1"
                            style="font-weight:700"
                          >
                            Hover
                          </span>
                        </p>
                      </div>
                    </div>
                    <div
                      class="emotion-13"
                    >
                      <div>
                        <p
                          class="amp-cms--p"
                          style="text-align:center;"
                        >
                          <span
                            class="amp-cms--body-2"
                          >
                            sit amet consectetur adipiscing elit sed do.
                          </span>
                        </p>
                      </div>
                    </div>
                  </div>
                  <div
                    class="emotion-14"
                    data-testid="category-card-bottom-border"
                  />
                </div>
              </a>
              <a
                class="category-card emotion-6"
                href="https://www.google.com"
                target="_self"
              >
                <div
                  aria-live="polite"
                  class="emotion-7"
                >
                  <div
                    aria-hidden="false"
                    class="emotion-8"
                  >
                    <img
                      alt="Man"
                      class="emotion-9"
                      src=""
                    />
                  </div>
                </div>
                <div
                  class="emotion-10"
                >
                  <div
                    class="emotion-11"
                  >
                    <div
                      class="emotion-12"
                    >
                      <div>
                        <p
                          class="amp-cms--p"
                          style="text-align:center;"
                        >
                          <span
                            class="amp-cms--eyebrow-1"
                            style="font-weight:700"
                          >
                            Selected
                          </span>
                        </p>
                      </div>
                    </div>
                    <div
                      class="emotion-13"
                    >
                      <div>
                        <p
                          class="amp-cms--p"
                          style="text-align:center;"
                        >
                          <span
                            class="amp-cms--body-2"
                          >
                            sit amet consectetur adipiscing elit sed do.
                          </span>
                        </p>
                      </div>
                    </div>
                  </div>
                  <div
                    class="emotion-14"
                    data-testid="category-card-bottom-border"
                  />
                </div>
              </a>
              <a
                class="category-card emotion-6"
                href="https://www,gap.com"
                target="_self"
              >
                <div
                  aria-live="polite"
                  class="emotion-7"
                >
                  <div
                    aria-hidden="false"
                    class="emotion-8"
                  >
                    <img
                      alt="Another Man"
                      class="emotion-9"
                      src=""
                    />
                  </div>
                </div>
                <div
                  class="emotion-10"
                >
                  <div
                    class="emotion-11"
                  >
                    <div
                      class="emotion-12"
                    >
                      <div>
                        <p
                          class="amp-cms--p"
                          style="text-align:center;"
                        >
                          <span
                            class="amp-cms--eyebrow-1"
                            style="font-weight:700"
                          >
                            Card 10
                          </span>
                        </p>
                      </div>
                    </div>
                    <div
                      class="emotion-13"
                    >
                      <div>
                        <p
                          class="amp-cms--p"
                          style="text-align:center;"
                        >
                          <span
                            class="amp-cms--body-2"
                          >
                            sit amet consectetur adipiscing elit sed do.
                          </span>
                        </p>
                      </div>
                    </div>
                  </div>
                  <div
                    class="emotion-14"
                    data-testid="category-card-bottom-border"
                  />
                </div>
              </a>
            </div>
          </div>
        </div>
      </article>
    </div>
  </div>
</div>
`;

exports[`VisualNavigationCarouselBR on desktop should match snapshots for desktop with introcard hideondesktop 1`] = `
.emotion-0 {
  margin-left: auto;
  margin-right: auto;
  max-width: 1280px;
}

.emotion-1 {
  aspect-ratio: 1280/365;
}

.emotion-2 {
  padding-bottom: 32px;
  padding-inline: 0;
  padding-top: 40px;
}

.emotion-2 .amp-cms--p {
  padding: 0;
  margin: 0;
  line-height: 0;
}

.emotion-2 .amp-cms--p>span {
  display: inline;
  white-space: break-spaces;
}

.emotion-2 .amp-cms--p a {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  position: relative;
  z-index: 2;
  pointer-events: auto;
}

.emotion-2 .amp-cms--p a:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-2 .amp-cms--legal-copy {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(0.625vw, 12px));
  line-height: 1.5;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 350;
}

.emotion-2 sup {
  vertical-align: top;
  display: inline-block;
  margin-top: -0.25ex;
}

.emotion-2 sub {
  vertical-align: bottom;
  display: inline-block;
  margin-bottom: -0.25ex;
}

.emotion-2 .amp-cms--f-0 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.625vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-2 .amp-cms--f-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.625vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-2 .amp-cms--f-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.625vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-2 .amp-cms--fn-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.625vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-2 .amp-cms--body-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(1.0416666666666665vw, 20px));
  line-height: 1.5;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 350;
}

.emotion-2 .amp-cms--body-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(16px, min(0.9375vw, 18px));
  line-height: 1.4444444444444444;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 350;
}

.emotion-2 .amp-cms--body-3 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(0.8333333333333334vw, 16px));
  line-height: 1.375;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 350;
}

.emotion-2 .amp-cms--body-4 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(0.7291666666666666vw, 14px));
  line-height: 1.4285714285714286;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 350;
}

.emotion-2 .amp-cms--body-5 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(0.625vw, 12px));
  line-height: 1.5;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 350;
}

.emotion-2 .amp-cms--eyebrow-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(1.0416666666666665vw, 20px));
  line-height: 1;
  letter-spacing: min(0.078125vw, 1.5px);
  font-weight: 600;
}

.emotion-2 .amp-cms--eyebrow-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(0.8333333333333334vw, 16px));
  line-height: 1;
  letter-spacing: min(0.078125vw, 1.5px);
  font-weight: 600;
}

.emotion-2 .amp-cms--eyebrow-3 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(0.625vw, 12px));
  line-height: 1;
  letter-spacing: min(0.078125vw, 1.5px);
  font-weight: 600;
}

.emotion-2 .amp-cms--headline-1 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(48px, min(5vw, 96px));
  line-height: 1;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 300;
}

.emotion-2 .amp-cms--headline-2 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(40px, min(4.166666666666666vw, 80px));
  line-height: 1;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 300;
}

.emotion-2 .amp-cms--headline-3 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(32px, min(3.3333333333333335vw, 64px));
  line-height: 1;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 300;
}

.emotion-2 .amp-cms--headline-4 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(28px, min(2.5vw, 48px));
  line-height: 1;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 300;
}

.emotion-2 .amp-cms--headline-5 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(1.6666666666666667vw, 32px));
  line-height: 1.125;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 300;
}

.emotion-2 .amp-cms--headline-6 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, min(1.25vw, 24px));
  line-height: 1.3333333333333333;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 300;
}

.emotion-2 .amp-cms--headline-7 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(1.0416666666666665vw, 20px));
  line-height: 1.4;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 300;
}

.emotion-2 .amp-cms--headlineAlt-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(32px, min(3.3333333333333335vw, 64px));
  line-height: 1;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 400;
}

.emotion-2 .amp-cms--headlineAlt-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(2.5vw, 48px));
  line-height: 1;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 400;
}

.emotion-2 .amp-cms--headlineAlt-3 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(1.6666666666666667vw, 32px));
  line-height: 1.0625;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 400;
}

.emotion-2 .amp-cms--headlineAlt-4 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(60px, min(6.25vw, 120px));
  line-height: 1;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 300;
}

.emotion-2 .amp-cms--headlineAlt-5 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.8333333333333334vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-2 .amp-cms--headlineAlt-6 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.8333333333333334vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-2 .amp-cms--headlineAlt-7 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.8333333333333334vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-2 .amp-cms--promo-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(8px, min(0.5208333333333333vw, 10px));
  line-height: 1.6;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 350;
}

.emotion-2 .amp-cms--promo-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(4.166666666666666vw, 80px));
  line-height: 1;
  letter-spacing: min(0vw, 0px);
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-2 .amp-cms--promoAlt-1 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.8333333333333334vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 600;
}

.emotion-2 .amp-cms--subhead-1 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(0.9375vw, 18px));
  line-height: 1.3333333333333333;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 300;
}

.emotion-2 .amp-cms--subhead-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(16px, min(1.25vw, 24px));
  line-height: 1;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 350;
}

.emotion-2 .amp-cms--subhead-3 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(1.0416666666666665vw, 20px));
  line-height: 1;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 350;
}

.emotion-3 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  border-bottom: 1px solid #E5E5E5;
}

.emotion-4 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  margin-bottom: -1px;
}

.emotion-5 {
  display: grid;
  grid-auto-flow: column;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  grid-gap: 4px;
  position: relative;
  grid-auto-columns: 94px;
}

.emotion-6 {
  outline: none;
  position: relative;
  -webkit-flex: 1;
  -ms-flex: 1;
  flex: 1;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  height: 100%;
  max-width: 94px;
}

.emotion-7 {
  overflow: hidden;
  -webkit-transition: background-color .25s ease;
  transition: background-color .25s ease;
  background-color: transparent;
  position: relative;
}

.emotion-8 {
  box-sizing: border-box;
  aspect-ratio: 3/4;
  position: relative;
  -webkit-transition: -webkit-transform .25s ease,opacity .25s ease;
  transition: transform .25s ease,opacity .25s ease;
  -webkit-transform: none;
  -moz-transform: none;
  -ms-transform: none;
  transform: none;
  opacity: 1;
  width: 100%;
  z-index: 1;
}

.emotion-9 {
  width: 100%;
  aspect-ratio: 3/4;
  object-fit: cover;
}

.emotion-10 {
  background: transparent;
  position: relative;
  box-sizing: border-box;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-box-flex: 1;
  -webkit-flex-grow: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
  z-index: 1;
}

.emotion-11 {
  height: 100%;
  padding: 8px 10px;
  position: relative;
  background-color: white;
}

.emotion-12 .amp-cms--p {
  padding: 0;
  margin: 0;
  line-height: 0;
}

.emotion-12 .amp-cms--p>span {
  display: inline;
  white-space: break-spaces;
}

.emotion-12 .amp-cms--p a {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  position: relative;
  z-index: 2;
  pointer-events: auto;
}

.emotion-12 .amp-cms--p a:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-12 .amp-cms--legal-copy {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.5;
  letter-spacing: 0.5px;
  font-weight: 350;
}

.emotion-12 sup {
  vertical-align: top;
  display: inline-block;
  margin-top: -0.25ex;
}

.emotion-12 sub {
  vertical-align: bottom;
  display: inline-block;
  margin-bottom: -0.25ex;
}

.emotion-12 .amp-cms--f-0 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-12 .amp-cms--f-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-12 .amp-cms--f-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-12 .amp-cms--fn-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-12 .amp-cms--body-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 20px;
  line-height: 1.5;
  letter-spacing: 0.5px;
  font-weight: 350;
}

.emotion-12 .amp-cms--body-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 18px;
  line-height: 1.4444444444444444;
  letter-spacing: 0.5px;
  font-weight: 350;
}

.emotion-12 .amp-cms--body-3 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 16px;
  line-height: 1.375;
  letter-spacing: 0.5px;
  font-weight: 350;
}

.emotion-12 .amp-cms--body-4 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 14px;
  line-height: 1.4285714285714286;
  letter-spacing: 0.5px;
  font-weight: 350;
}

.emotion-12 .amp-cms--body-5 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.5;
  letter-spacing: 0.5px;
  font-weight: 350;
}

.emotion-12 .amp-cms--eyebrow-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 20px;
  line-height: 1;
  letter-spacing: 1.5px;
  font-weight: 600;
}

.emotion-12 .amp-cms--eyebrow-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 16px;
  line-height: 1;
  letter-spacing: 1.5px;
  font-weight: 600;
}

.emotion-12 .amp-cms--eyebrow-3 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1;
  letter-spacing: 1.5px;
  font-weight: 600;
}

.emotion-12 .amp-cms--headline-1 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 96px;
  line-height: 1;
  letter-spacing: 0.5px;
  font-weight: 300;
}

.emotion-12 .amp-cms--headline-2 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 80px;
  line-height: 1;
  letter-spacing: 0.5px;
  font-weight: 300;
}

.emotion-12 .amp-cms--headline-3 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 64px;
  line-height: 1;
  letter-spacing: 0.5px;
  font-weight: 300;
}

.emotion-12 .amp-cms--headline-4 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 48px;
  line-height: 1;
  letter-spacing: 0.5px;
  font-weight: 300;
}

.emotion-12 .amp-cms--headline-5 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 32px;
  line-height: 1.125;
  letter-spacing: 0.5px;
  font-weight: 300;
}

.emotion-12 .amp-cms--headline-6 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 24px;
  line-height: 1.3333333333333333;
  letter-spacing: 0.5px;
  font-weight: 300;
}

.emotion-12 .amp-cms--headline-7 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 20px;
  line-height: 1.4;
  letter-spacing: 0.5px;
  font-weight: 300;
}

.emotion-12 .amp-cms--headlineAlt-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 64px;
  line-height: 1;
  letter-spacing: 0.5px;
  font-weight: 400;
}

.emotion-12 .amp-cms--headlineAlt-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 48px;
  line-height: 1;
  letter-spacing: 0.5px;
  font-weight: 400;
}

.emotion-12 .amp-cms--headlineAlt-3 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 32px;
  line-height: 1.0625;
  letter-spacing: 0.5px;
  font-weight: 400;
}

.emotion-12 .amp-cms--headlineAlt-4 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 120px;
  line-height: 1;
  letter-spacing: 0.5px;
  font-weight: 300;
}

.emotion-12 .amp-cms--headlineAlt-5 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 16px;
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-12 .amp-cms--headlineAlt-6 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 16px;
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-12 .amp-cms--headlineAlt-7 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 16px;
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-12 .amp-cms--promo-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 10px;
  line-height: 1.6;
  letter-spacing: 0.5px;
  font-weight: 350;
}

.emotion-12 .amp-cms--promo-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 80px;
  line-height: 1;
  letter-spacing: 0px;
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-12 .amp-cms--promoAlt-1 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 16px;
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 600;
}

.emotion-12 .amp-cms--subhead-1 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 18px;
  line-height: 1.3333333333333333;
  letter-spacing: 0.5px;
  font-weight: 300;
}

.emotion-12 .amp-cms--subhead-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 24px;
  line-height: 1;
  letter-spacing: 0.5px;
  font-weight: 350;
}

.emotion-12 .amp-cms--subhead-3 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 20px;
  line-height: 1;
  letter-spacing: 0.5px;
  font-weight: 350;
}

.emotion-13 {
  padding-top: 8px;
}

.emotion-13 .amp-cms--p {
  padding: 0;
  margin: 0;
  line-height: 0;
}

.emotion-13 .amp-cms--p>span {
  display: inline;
  white-space: break-spaces;
}

.emotion-13 .amp-cms--p a {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  position: relative;
  z-index: 2;
  pointer-events: auto;
}

.emotion-13 .amp-cms--p a:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-13 .amp-cms--legal-copy {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.5;
  letter-spacing: 0.5px;
  font-weight: 350;
}

.emotion-13 sup {
  vertical-align: top;
  display: inline-block;
  margin-top: -0.25ex;
}

.emotion-13 sub {
  vertical-align: bottom;
  display: inline-block;
  margin-bottom: -0.25ex;
}

.emotion-13 .amp-cms--f-0 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-13 .amp-cms--f-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-13 .amp-cms--f-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-13 .amp-cms--fn-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-13 .amp-cms--body-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 20px;
  line-height: 1.5;
  letter-spacing: 0.5px;
  font-weight: 350;
}

.emotion-13 .amp-cms--body-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 18px;
  line-height: 1.4444444444444444;
  letter-spacing: 0.5px;
  font-weight: 350;
}

.emotion-13 .amp-cms--body-3 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 16px;
  line-height: 1.375;
  letter-spacing: 0.5px;
  font-weight: 350;
}

.emotion-13 .amp-cms--body-4 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 14px;
  line-height: 1.4285714285714286;
  letter-spacing: 0.5px;
  font-weight: 350;
}

.emotion-13 .amp-cms--body-5 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.5;
  letter-spacing: 0.5px;
  font-weight: 350;
}

.emotion-13 .amp-cms--eyebrow-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 20px;
  line-height: 1;
  letter-spacing: 1.5px;
  font-weight: 600;
}

.emotion-13 .amp-cms--eyebrow-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 16px;
  line-height: 1;
  letter-spacing: 1.5px;
  font-weight: 600;
}

.emotion-13 .amp-cms--eyebrow-3 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1;
  letter-spacing: 1.5px;
  font-weight: 600;
}

.emotion-13 .amp-cms--headline-1 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 96px;
  line-height: 1;
  letter-spacing: 0.5px;
  font-weight: 300;
}

.emotion-13 .amp-cms--headline-2 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 80px;
  line-height: 1;
  letter-spacing: 0.5px;
  font-weight: 300;
}

.emotion-13 .amp-cms--headline-3 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 64px;
  line-height: 1;
  letter-spacing: 0.5px;
  font-weight: 300;
}

.emotion-13 .amp-cms--headline-4 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 48px;
  line-height: 1;
  letter-spacing: 0.5px;
  font-weight: 300;
}

.emotion-13 .amp-cms--headline-5 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 32px;
  line-height: 1.125;
  letter-spacing: 0.5px;
  font-weight: 300;
}

.emotion-13 .amp-cms--headline-6 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 24px;
  line-height: 1.3333333333333333;
  letter-spacing: 0.5px;
  font-weight: 300;
}

.emotion-13 .amp-cms--headline-7 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 20px;
  line-height: 1.4;
  letter-spacing: 0.5px;
  font-weight: 300;
}

.emotion-13 .amp-cms--headlineAlt-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 64px;
  line-height: 1;
  letter-spacing: 0.5px;
  font-weight: 400;
}

.emotion-13 .amp-cms--headlineAlt-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 48px;
  line-height: 1;
  letter-spacing: 0.5px;
  font-weight: 400;
}

.emotion-13 .amp-cms--headlineAlt-3 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 32px;
  line-height: 1.0625;
  letter-spacing: 0.5px;
  font-weight: 400;
}

.emotion-13 .amp-cms--headlineAlt-4 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 120px;
  line-height: 1;
  letter-spacing: 0.5px;
  font-weight: 300;
}

.emotion-13 .amp-cms--headlineAlt-5 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 16px;
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-13 .amp-cms--headlineAlt-6 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 16px;
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-13 .amp-cms--headlineAlt-7 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 16px;
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-13 .amp-cms--promo-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 10px;
  line-height: 1.6;
  letter-spacing: 0.5px;
  font-weight: 350;
}

.emotion-13 .amp-cms--promo-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 80px;
  line-height: 1;
  letter-spacing: 0px;
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-13 .amp-cms--promoAlt-1 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 16px;
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 600;
}

.emotion-13 .amp-cms--subhead-1 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 18px;
  line-height: 1.3333333333333333;
  letter-spacing: 0.5px;
  font-weight: 300;
}

.emotion-13 .amp-cms--subhead-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 24px;
  line-height: 1;
  letter-spacing: 0.5px;
  font-weight: 350;
}

.emotion-13 .amp-cms--subhead-3 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 20px;
  line-height: 1;
  letter-spacing: 0.5px;
  font-weight: 350;
}

.emotion-14 {
  background-color: transparent;
  box-shadow: none;
  box-sizing: border-box;
  border: 0;
  border-bottom: 1px solid transparent;
  -webkit-transition: border-bottom-color 0.2s ease;
  transition: border-bottom-color 0.2s ease;
}

<div>
  <div
    style="font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;"
  >
    <div
      class="emotion-0"
    >
      <article
        class="emotion-1"
      >
        <div
          class="emotion-2"
        >
          <div>
            <p
              class="amp-cms--p"
              style="text-align:center;"
            >
              <span
                class="amp-cms--headline-5"
              >
                Lorem ipsum dolor
              </span>
              <span
                class="amp-cms--body-1"
              >
                 sit amet consectetur adipiscing elit sed do eiusmod.
              </span>
            </p>
          </div>
        </div>
        <div
          class="emotion-3"
        >
          <div
            class="emotion-4"
          >
            <div
              class="emotion-5"
            >
              <a
                class="category-card emotion-6"
                href="https://www.google.com"
                target="_self"
              >
                <div
                  aria-live="polite"
                  class="emotion-7"
                >
                  <div
                    aria-hidden="false"
                    class="emotion-8"
                  >
                    <img
                      alt="Woman"
                      class="emotion-9"
                      src=""
                    />
                  </div>
                </div>
                <div
                  class="emotion-10"
                >
                  <div
                    class="emotion-11"
                  >
                    <div
                      class="emotion-12"
                    >
                      <div>
                        <p
                          class="amp-cms--p"
                          style="text-align:center;"
                        >
                          <span
                            class="amp-cms--eyebrow-1"
                            style="font-weight:700"
                          >
                            Straight
                          </span>
                        </p>
                      </div>
                    </div>
                    <div
                      class="emotion-13"
                    >
                      <div>
                        <p
                          class="amp-cms--p"
                          style="text-align:center;"
                        >
                          <span
                            class="amp-cms--body-2"
                          >
                            sit amet consectetur adipiscing elit eiusmod.
                          </span>
                        </p>
                      </div>
                    </div>
                  </div>
                  <div
                    class="emotion-14"
                    data-testid="category-card-bottom-border"
                  />
                </div>
              </a>
              <a
                class="category-card emotion-6"
                href="https://www.google.com"
                target="_self"
              >
                <div
                  aria-live="polite"
                  class="emotion-7"
                >
                  <div
                    aria-hidden="false"
                    class="emotion-8"
                  >
                    <img
                      alt="Man"
                      class="emotion-9"
                      src=""
                    />
                  </div>
                </div>
                <div
                  class="emotion-10"
                >
                  <div
                    class="emotion-11"
                  >
                    <div
                      class="emotion-12"
                    >
                      <div>
                        <p
                          class="amp-cms--p"
                          style="text-align:center;"
                        >
                          <span
                            class="amp-cms--eyebrow-1"
                            style="font-weight:700"
                          >
                            Skinny
                          </span>
                        </p>
                      </div>
                    </div>
                    <div
                      class="emotion-13"
                    >
                      <div>
                        <p
                          class="amp-cms--p"
                          style="text-align:center;"
                        >
                          <span
                            class="amp-cms--body-2"
                          >
                            sit amet consectetur adipiscing elit eiusmod.
                          </span>
                        </p>
                      </div>
                    </div>
                  </div>
                  <div
                    class="emotion-14"
                    data-testid="category-card-bottom-border"
                  />
                </div>
              </a>
              <a
                class="category-card emotion-6"
                href="https://www.google.com"
                target="_self"
              >
                <div
                  aria-live="polite"
                  class="emotion-7"
                >
                  <div
                    aria-hidden="false"
                    class="emotion-8"
                  >
                    <img
                      alt="Jeans"
                      class="emotion-9"
                      src=""
                    />
                  </div>
                </div>
                <div
                  class="emotion-10"
                >
                  <div
                    class="emotion-11"
                  >
                    <div
                      class="emotion-12"
                    >
                      <div>
                        <p
                          class="amp-cms--p"
                          style="text-align:center;"
                        >
                          <span
                            class="amp-cms--eyebrow-1"
                            style="font-weight:700"
                          >
                            Super-skinny
                          </span>
                        </p>
                      </div>
                    </div>
                    <div
                      class="emotion-13"
                    >
                      <div>
                        <p
                          class="amp-cms--p"
                          style="text-align:center;"
                        >
                          <span
                            class="amp-cms--body-2"
                          >
                            sit amet consectetur adipiscing elit eiusmod.
                          </span>
                        </p>
                      </div>
                    </div>
                  </div>
                  <div
                    class="emotion-14"
                    data-testid="category-card-bottom-border"
                  />
                </div>
              </a>
              <a
                class="category-card emotion-6"
                href="https://www.gap.com"
                target="_self"
              >
                <div
                  aria-live="polite"
                  class="emotion-7"
                >
                  <div
                    aria-hidden="false"
                    class="emotion-8"
                  >
                    <img
                      alt="Another Man"
                      class="emotion-9"
                      src=""
                    />
                  </div>
                </div>
                <div
                  class="emotion-10"
                >
                  <div
                    class="emotion-11"
                  >
                    <div
                      class="emotion-12"
                    >
                      <div>
                        <p
                          class="amp-cms--p"
                          style="text-align:center;"
                        >
                          <span
                            class="amp-cms--eyebrow-1"
                            style="font-weight:700;text-decoration:underline"
                          >
                            Hover Headline
                          </span>
                        </p>
                      </div>
                    </div>
                    <div
                      class="emotion-13"
                    >
                      <div>
                        <p
                          class="amp-cms--p"
                          style="text-align:center;"
                        >
                          <span
                            class="amp-cms--body-2"
                          >
                            sit amet consectetur adipiscing elit eiusmod.
                          </span>
                        </p>
                      </div>
                    </div>
                  </div>
                  <div
                    class="emotion-14"
                    data-testid="category-card-bottom-border"
                  />
                </div>
              </a>
              <a
                class="category-card emotion-6"
                href="https://www.gap.com"
                target="_self"
              >
                <div
                  aria-live="polite"
                  class="emotion-7"
                >
                  <div
                    aria-hidden="false"
                    class="emotion-8"
                  >
                    <img
                      alt="Posing"
                      class="emotion-9"
                      src=""
                    />
                  </div>
                </div>
                <div
                  class="emotion-10"
                >
                  <div
                    class="emotion-11"
                  >
                    <div
                      class="emotion-12"
                    >
                      <div>
                        <p
                          class="amp-cms--p"
                          style="text-align:center;"
                        >
                          <span
                            class="amp-cms--eyebrow-1"
                            style="font-weight:700;text-decoration:underline"
                          >
                            Selected Headline
                          </span>
                        </p>
                      </div>
                    </div>
                    <div
                      class="emotion-13"
                    >
                      <div>
                        <p
                          class="amp-cms--p"
                          style="text-align:center;"
                        >
                          <span
                            class="amp-cms--body-2"
                          >
                            sit amet consectetur adipiscing elit eiusmod.
                          </span>
                        </p>
                      </div>
                    </div>
                  </div>
                  <div
                    class="emotion-14"
                    data-testid="category-card-bottom-border"
                  />
                </div>
              </a>
              <a
                class="category-card emotion-6"
                href="https://www.google.com"
                target="_self"
              >
                <div
                  aria-live="polite"
                  class="emotion-7"
                >
                  <div
                    aria-hidden="false"
                    class="emotion-8"
                  >
                    <img
                      alt="Yoga"
                      class="emotion-9"
                      src=""
                    />
                  </div>
                </div>
                <div
                  class="emotion-10"
                >
                  <div
                    class="emotion-11"
                  >
                    <div
                      class="emotion-12"
                    >
                      <div>
                        <p
                          class="amp-cms--p"
                          style="text-align:center;"
                        >
                          <span
                            class="amp-cms--eyebrow-1"
                            style="font-weight:700"
                          >
                            Wide-Leg
                          </span>
                        </p>
                      </div>
                    </div>
                    <div
                      class="emotion-13"
                    >
                      <div>
                        <p
                          class="amp-cms--p"
                          style="text-align:center;"
                        >
                          <span
                            class="amp-cms--body-2"
                          >
                            sit amet consectetur adipiscing elit eiusmod.
                          </span>
                        </p>
                      </div>
                    </div>
                  </div>
                  <div
                    class="emotion-14"
                    data-testid="category-card-bottom-border"
                  />
                </div>
              </a>
              <a
                class="category-card emotion-6"
                href="htttps://www.gap.com"
                target="_self"
              >
                <div
                  aria-live="polite"
                  class="emotion-7"
                >
                  <div
                    aria-hidden="false"
                    class="emotion-8"
                  >
                    <img
                      alt="People"
                      class="emotion-9"
                      src=""
                    />
                  </div>
                </div>
                <div
                  class="emotion-10"
                >
                  <div
                    class="emotion-11"
                  >
                    <div
                      class="emotion-12"
                    >
                      <div>
                        <p
                          class="amp-cms--p"
                          style="text-align:center;"
                        >
                          <span
                            class="amp-cms--eyebrow-1"
                            style="font-weight:700"
                          >
                            Skinny-Leg
                          </span>
                        </p>
                      </div>
                    </div>
                    <div
                      class="emotion-13"
                    >
                      <div>
                        <p
                          class="amp-cms--p"
                          style="text-align:center;"
                        >
                          <span
                            class="amp-cms--body-2"
                          >
                            sit amet consectetur adipiscing elit eiusmod.
                          </span>
                        </p>
                      </div>
                    </div>
                  </div>
                  <div
                    class="emotion-14"
                    data-testid="category-card-bottom-border"
                  />
                </div>
              </a>
              <a
                class="category-card emotion-6"
                href="https://www.google.com"
                target="_self"
              >
                <div
                  aria-live="polite"
                  class="emotion-7"
                >
                  <div
                    aria-hidden="false"
                    class="emotion-8"
                  >
                    <img
                      alt="Woman"
                      class="emotion-9"
                      src=""
                    />
                  </div>
                </div>
                <div
                  class="emotion-10"
                >
                  <div
                    class="emotion-11"
                  >
                    <div
                      class="emotion-12"
                    >
                      <div>
                        <p
                          class="amp-cms--p"
                          style="text-align:center;"
                        >
                          <span
                            class="amp-cms--eyebrow-1"
                            style="font-weight:700"
                          >
                            Hover
                          </span>
                        </p>
                      </div>
                    </div>
                    <div
                      class="emotion-13"
                    >
                      <div>
                        <p
                          class="amp-cms--p"
                          style="text-align:center;"
                        >
                          <span
                            class="amp-cms--body-2"
                          >
                            sit amet consectetur adipiscing elit sed do.
                          </span>
                        </p>
                      </div>
                    </div>
                  </div>
                  <div
                    class="emotion-14"
                    data-testid="category-card-bottom-border"
                  />
                </div>
              </a>
              <a
                class="category-card emotion-6"
                href="https://www.google.com"
                target="_self"
              >
                <div
                  aria-live="polite"
                  class="emotion-7"
                >
                  <div
                    aria-hidden="false"
                    class="emotion-8"
                  >
                    <img
                      alt="Man"
                      class="emotion-9"
                      src=""
                    />
                  </div>
                </div>
                <div
                  class="emotion-10"
                >
                  <div
                    class="emotion-11"
                  >
                    <div
                      class="emotion-12"
                    >
                      <div>
                        <p
                          class="amp-cms--p"
                          style="text-align:center;"
                        >
                          <span
                            class="amp-cms--eyebrow-1"
                            style="font-weight:700"
                          >
                            Selected
                          </span>
                        </p>
                      </div>
                    </div>
                    <div
                      class="emotion-13"
                    >
                      <div>
                        <p
                          class="amp-cms--p"
                          style="text-align:center;"
                        >
                          <span
                            class="amp-cms--body-2"
                          >
                            sit amet consectetur adipiscing elit sed do.
                          </span>
                        </p>
                      </div>
                    </div>
                  </div>
                  <div
                    class="emotion-14"
                    data-testid="category-card-bottom-border"
                  />
                </div>
              </a>
              <a
                class="category-card emotion-6"
                href="https://www,gap.com"
                target="_self"
              >
                <div
                  aria-live="polite"
                  class="emotion-7"
                >
                  <div
                    aria-hidden="false"
                    class="emotion-8"
                  >
                    <img
                      alt="Another Man"
                      class="emotion-9"
                      src=""
                    />
                  </div>
                </div>
                <div
                  class="emotion-10"
                >
                  <div
                    class="emotion-11"
                  >
                    <div
                      class="emotion-12"
                    >
                      <div>
                        <p
                          class="amp-cms--p"
                          style="text-align:center;"
                        >
                          <span
                            class="amp-cms--eyebrow-1"
                            style="font-weight:700"
                          >
                            Card 10
                          </span>
                        </p>
                      </div>
                    </div>
                    <div
                      class="emotion-13"
                    >
                      <div>
                        <p
                          class="amp-cms--p"
                          style="text-align:center;"
                        >
                          <span
                            class="amp-cms--body-2"
                          >
                            sit amet consectetur adipiscing elit sed do.
                          </span>
                        </p>
                      </div>
                    </div>
                  </div>
                  <div
                    class="emotion-14"
                    data-testid="category-card-bottom-border"
                  />
                </div>
              </a>
            </div>
          </div>
        </div>
      </article>
    </div>
  </div>
</div>
`;

exports[`VisualNavigationCarouselBR on mobile should match snapshot 1`] = `
.emotion-0 {
  margin-left: auto;
  margin-right: auto;
  max-width: 1280px;
}

.emotion-1 {
  min-height: 235px;
}

.emotion-2 {
  padding-bottom: 24px;
  padding-inline: 10px;
  padding-top: 40px;
}

.emotion-2 .amp-cms--p {
  padding: 0;
  margin: 0;
  line-height: 0;
}

.emotion-2 .amp-cms--p>span {
  display: inline;
  white-space: break-spaces;
}

.emotion-2 .amp-cms--p a {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  position: relative;
  z-index: 2;
  pointer-events: auto;
}

.emotion-2 .amp-cms--p a:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-2 .amp-cms--legal-copy {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(8px, min(2.666666666666667vw, 10px));
  line-height: 1.8;
  letter-spacing: min(0.13333333333333333vw, 0.5px);
  font-weight: 350;
}

.emotion-2 sup {
  vertical-align: top;
  display: inline-block;
  margin-top: -0.25ex;
}

.emotion-2 sub {
  vertical-align: bottom;
  display: inline-block;
  margin-bottom: -0.25ex;
}

.emotion-2 .amp-cms--f-0 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(2.933333333333333vw, 11px);
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-2 .amp-cms--f-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(2.933333333333333vw, 11px);
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-2 .amp-cms--f-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(2.933333333333333vw, 11px);
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-2 .amp-cms--fn-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(2.933333333333333vw, 11px);
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-2 .amp-cms--body-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(3.2vw, 12px));
  line-height: 1.5;
  letter-spacing: min(0.13333333333333333vw, 0.5px);
  font-weight: 350;
}

.emotion-2 .amp-cms--body-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(4.266666666666667vw, 16px));
  line-height: 1.625;
  letter-spacing: min(0.13333333333333333vw, 0.5px);
  font-weight: 350;
}

.emotion-2 .amp-cms--body-3 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(3.733333333333334vw, 14px));
  line-height: 1.5714285714285714;
  letter-spacing: min(0.13333333333333333vw, 0.5px);
  font-weight: 350;
}

.emotion-2 .amp-cms--body-4 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(3.2vw, 12px));
  line-height: 1.6666666666666667;
  letter-spacing: min(0.13333333333333333vw, 0.5px);
  font-weight: 350;
}

.emotion-2 .amp-cms--body-5 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(8px, min(2.666666666666667vw, 10px));
  line-height: 1.8;
  letter-spacing: min(0.13333333333333333vw, 0.5px);
  font-weight: 350;
}

.emotion-2 .amp-cms--eyebrow-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(3.733333333333334vw, 14px));
  line-height: 1;
  letter-spacing: min(0.4vw, 1.5px);
  font-weight: 600;
}

.emotion-2 .amp-cms--eyebrow-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(11px, min(3.2vw, 12px));
  line-height: 1;
  letter-spacing: min(0.4vw, 1.5px);
  font-weight: 600;
}

.emotion-2 .amp-cms--eyebrow-3 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(2.666666666666667vw, 10px));
  line-height: 1;
  letter-spacing: min(0.4vw, 1.5px);
  font-weight: 600;
}

.emotion-2 .amp-cms--headline-1 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(12.8vw, 48px));
  line-height: 1;
  letter-spacing: min(0.13333333333333333vw, 0.5px);
  font-weight: 300;
}

.emotion-2 .amp-cms--headline-2 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(10.666666666666668vw, 40px));
  line-height: 1;
  letter-spacing: min(0.13333333333333333vw, 0.5px);
  font-weight: 300;
}

.emotion-2 .amp-cms--headline-3 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(8.533333333333333vw, 32px));
  line-height: 1.0625;
  letter-spacing: min(0.13333333333333333vw, 0.5px);
  font-weight: 300;
}

.emotion-2 .amp-cms--headline-4 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(7.466666666666668vw, 28px));
  line-height: 1.0714285714285714;
  letter-spacing: min(0.13333333333333333vw, 0.5px);
  font-weight: 300;
}

.emotion-2 .amp-cms--headline-5 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(6.4vw, 24px));
  line-height: 1.0833333333333333;
  letter-spacing: min(0.13333333333333333vw, 0.5px);
  font-weight: 300;
}

.emotion-2 .amp-cms--headline-6 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(4.8vw, 18px));
  line-height: 1.1111111111111112;
  letter-spacing: min(0.13333333333333333vw, 0.5px);
  font-weight: 300;
}

.emotion-2 .amp-cms--headline-7 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(3.733333333333334vw, 14px));
  line-height: 1.1428571428571428;
  letter-spacing: min(0.13333333333333333vw, 0.5px);
  font-weight: 400;
}

.emotion-2 .amp-cms--headlineAlt-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(8.533333333333333vw, 32px));
  line-height: 1.0625;
  letter-spacing: min(0.13333333333333333vw, 0.5px);
  font-weight: 400;
}

.emotion-2 .amp-cms--headlineAlt-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(6.4vw, 24px));
  line-height: 1.0833333333333333;
  letter-spacing: min(0.13333333333333333vw, 0.5px);
  font-weight: 400;
}

.emotion-2 .amp-cms--headlineAlt-3 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(5.333333333333334vw, 20px));
  line-height: 1.1;
  letter-spacing: min(0.13333333333333333vw, 0.5px);
  font-weight: 400;
}

.emotion-2 .amp-cms--headlineAlt-4 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(16vw, 60px));
  line-height: 1;
  letter-spacing: min(0.13333333333333333vw, 0.5px);
  font-weight: 300;
}

.emotion-2 .amp-cms--headlineAlt-5 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(3.733333333333334vw, 14px);
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-2 .amp-cms--headlineAlt-6 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(3.733333333333334vw, 14px);
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-2 .amp-cms--headlineAlt-7 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(3.733333333333334vw, 14px);
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-2 .amp-cms--promo-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(8px, min(2.1333333333333333vw, 8px));
  line-height: 1.5;
  letter-spacing: min(0.13333333333333333vw, 0.5px);
  font-weight: 400;
}

.emotion-2 .amp-cms--promo-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(13px, min(10.666666666666668vw, 40px));
  line-height: 1;
  letter-spacing: min(0vw, 0px);
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-2 .amp-cms--promoAlt-1 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(3.733333333333334vw, 14px);
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 600;
}

.emotion-2 .amp-cms--subhead-1 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(3.733333333333334vw, 14px));
  line-height: 1.2857142857142858;
  letter-spacing: min(0.13333333333333333vw, 0.5px);
  font-weight: 300;
}

.emotion-2 .amp-cms--subhead-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(4.266666666666667vw, 16px));
  line-height: 1;
  letter-spacing: min(0.13333333333333333vw, 0.5px);
  font-weight: 350;
}

.emotion-2 .amp-cms--subhead-3 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(3.2vw, 12px));
  line-height: 1;
  letter-spacing: min(0.13333333333333333vw, 0.5px);
  font-weight: 350;
}

.emotion-3 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  border-bottom: 1px solid transparent;
}

.emotion-4 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  margin-bottom: 0;
}

.emotion-5 {
  overflow: hidden;
}

.emotion-5 div.slick-track {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  gap: 0;
}

.emotion-5 div.slick-track:before {
  display: none;
}

.emotion-5 div.slick-track:after {
  display: none;
}

.emotion-5 div.slick-slide {
  height: auto;
}

.emotion-5 div.slick-slide>div {
  height: 100%;
}

.emotion-6 {
  position: relative;
}

.emotion-6 .slick-slider {
  position: relative;
  display: block;
  box-sizing: border-box;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  -webkit-touch-callout: none;
  touch-action: pan-y;
  -webkit-tap-highlight-color: transparent;
  scroll-behavior: smooth;
}

.emotion-6 .slick-slider .slick-track,
.emotion-6 .slick-slider .slick-list {
  -webkit-transform: translate3d(0, 0, 0);
  -moz-transform: translate3d(0, 0, 0);
  -ms-transform: translate3d(0, 0, 0);
  transform: translate3d(0, 0, 0);
}

.emotion-6 .slick-list {
  position: relative;
  display: block;
  overflow-x: hidden;
  overflow-y: hidden;
  margin: 0;
  padding: 0;
}

.emotion-6 .slick-list:focus {
  outline: none;
}

.emotion-6 .slick-list.dragging {
  cursor: pointer;
  cursor: hand;
}

.emotion-6 .slick-track {
  position: relative;
  top: 0;
  left: 0;
  display: block;
  margin-left: auto;
  margin-right: auto;
}

.emotion-6 .slick-track:before,
.emotion-6 .slick-track:after {
  display: table;
  content: "";
}

.emotion-6 .slick-track:after {
  clear: both;
}

.emotion-6 .slick-slide {
  display: none;
  float: left;
  height: 100%;
  min-height: 1px;
}

.emotion-6 .slick-slide img {
  display: block;
}

.emotion-6 .slick-slide.slick-loading img {
  display: none;
}

.emotion-6 .slick-slide.dragging img {
  pointer-events: none;
}

.emotion-6 [dir="rtl"] .slick-slide {
  float: right;
}

.emotion-6 .slick-initialized .slick-slide,
.emotion-6 .slick-vertical .slick-slide {
  display: block;
}

.emotion-6 .slick-vertical .slick-slide {
  border: 1px solid transparent;
  height: auto;
}

.emotion-6 .slick-loading .slick-track,
.emotion-6 .slick-loading .slick-slide {
  visibility: hidden;
}

.emotion-6 .slick-loading .slick-list {
  background: #fff url("./ajax-loader.gif") center center no-repeat;
}

.emotion-6 .slick-arrow.slick-hidden {
  display: none;
}

.emotion-6 .slick-prev,
.emotion-6 .slick-next {
  font-size: 0;
  line-height: 0;
  position: absolute;
  top: 50%;
  display: block;
  padding: 0;
  cursor: pointer;
  color: transparent;
  border: none;
  outline: none;
  background: transparent;
}

.emotion-6 .slick-prev:hover,
.emotion-6 .slick-next:hover,
.emotion-6 .slick-prev:focus,
.emotion-6 .slick-next:focus {
  color: transparent;
  outline: none;
  background: transparent;
  opacity: 1;
}

.emotion-6 .slick-prev.slick-disabled,
.emotion-6 .slick-next.slick-disabled {
  opacity: 0.25;
}

.emotion-6 .slick-prev {
  left: -0px;
  -webkit-transform: translate(0, -50%) rotate(90deg);
  -moz-transform: translate(0, -50%) rotate(90deg);
  -ms-transform: translate(0, -50%) rotate(90deg);
  transform: translate(0, -50%) rotate(90deg);
}

.emotion-6 .slick-prev span {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-6 [dir="rtl"] .slick-prev {
  right: -0px;
  left: auto;
}

.emotion-6 .slick-next {
  right: -0px;
  -webkit-transform: translate(0, -50%) rotate(-90deg);
  -moz-transform: translate(0, -50%) rotate(-90deg);
  -ms-transform: translate(0, -50%) rotate(-90deg);
  transform: translate(0, -50%) rotate(-90deg);
}

.emotion-6 .slick-next span {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-6 [dir="rtl"] .slick-next {
  right: auto;
  left: -0px;
}

.emotion-6 .slick-dotted.slick-slider {
  margin-bottom: 30px;
}

.emotion-6 .slick-dots {
  position: absolute;
  bottom: -25px;
  display: block;
  width: 100%;
  padding: 0;
  margin: 0;
  list-style: none;
  text-align: center;
}

.emotion-6 .slick-dots li {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: 20px;
  height: 20px;
  margin: 0 5px;
  padding: 0;
  cursor: pointer;
}

.emotion-6 .slick-dots li.slick-active button:before {
  opacity: 0.75;
  color: black;
}

.emotion-6 .slick-dots li button {
  font-size: 0;
  line-height: 0;
  display: block;
  padding: 5px;
  cursor: pointer;
  color: transparent;
  border: 0;
  outline: none;
  background: transparent;
}

.emotion-6 .slick-dots li button:hover,
.emotion-6 .slick-dots li button:focus {
  outline: none;
}

.emotion-6 .slick-dots li button:hover:before,
.emotion-6 .slick-dots li button:focus:before,
.emotion-6 .slick-dots li button:hover:before,
.emotion-6 .slick-dots li button:focus:before {
  opacity: 1;
}

.emotion-6 .slick-dots li button:before {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 5px;
  height: 5px;
  content: "";
  opacity: 0.25;
  background-color: black;
  border-radius: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-6 .slick-slider {
  padding-left: 24px;
}

.emotion-6 .slick-list {
  margin: 0 -2px;
}

.emotion-6 .slick-track {
  margin-left: 0!important;
  margin-right: 0!important;
}

.emotion-6 .slick-dots {
  position: unset;
  line-height: 0;
  width: calc(100% + 24px);
  margin-left: -24px;
  margin-top: -1px;
  border-top: 1px solid #E5E5E5;
}

.emotion-6 .slick-dots li {
  margin-top: 35px;
}

.emotion-6 .category-card {
  margin: 0 2px!important;
  overflow: hidden;
}

.emotion-7 {
  outline: none;
  position: relative;
  -webkit-flex: 1;
  -ms-flex: 1;
  flex: 1;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  height: 100%;
  max-width: 396.8px;
}

.emotion-8 {
  overflow: hidden;
  -webkit-transition: background-color .25s ease;
  transition: background-color .25s ease;
  background-color: transparent;
  min-width: 3px;
  position: relative;
}

.emotion-9 {
  box-sizing: border-box;
  aspect-ratio: 3/4;
  position: relative;
  -webkit-transition: -webkit-transform .25s ease,opacity .25s ease;
  transition: transform .25s ease,opacity .25s ease;
  -webkit-transform: none;
  -moz-transform: none;
  -ms-transform: none;
  transform: none;
  opacity: 1;
  width: 100%;
  z-index: 1;
}

.emotion-10 {
  width: 100%;
  aspect-ratio: 3/4;
  object-fit: cover;
}

.emotion-11 {
  background: transparent;
  position: relative;
  box-sizing: border-box;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-box-flex: 1;
  -webkit-flex-grow: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
  z-index: 1;
}

.emotion-12 {
  height: 100%;
  padding: 8px 10px;
  position: relative;
  background-color: white;
}

.emotion-13 .amp-cms--p {
  padding: 0;
  margin: 0;
  line-height: 0;
}

.emotion-13 .amp-cms--p>span {
  display: inline;
  white-space: break-spaces;
}

.emotion-13 .amp-cms--p a {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  position: relative;
  z-index: 2;
  pointer-events: auto;
}

.emotion-13 .amp-cms--p a:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-13 .amp-cms--legal-copy {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 10px;
  line-height: 1.8;
  letter-spacing: 0.5px;
  font-weight: 350;
}

.emotion-13 sup {
  vertical-align: top;
  display: inline-block;
  margin-top: -0.25ex;
}

.emotion-13 sub {
  vertical-align: bottom;
  display: inline-block;
  margin-bottom: -0.25ex;
}

.emotion-13 .amp-cms--f-0 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 11px;
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-13 .amp-cms--f-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 11px;
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-13 .amp-cms--f-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 11px;
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-13 .amp-cms--fn-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 11px;
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-13 .amp-cms--body-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.5;
  letter-spacing: 0.5px;
  font-weight: 350;
}

.emotion-13 .amp-cms--body-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 16px;
  line-height: 1.625;
  letter-spacing: 0.5px;
  font-weight: 350;
}

.emotion-13 .amp-cms--body-3 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 14px;
  line-height: 1.5714285714285714;
  letter-spacing: 0.5px;
  font-weight: 350;
}

.emotion-13 .amp-cms--body-4 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.6666666666666667;
  letter-spacing: 0.5px;
  font-weight: 350;
}

.emotion-13 .amp-cms--body-5 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 10px;
  line-height: 1.8;
  letter-spacing: 0.5px;
  font-weight: 350;
}

.emotion-13 .amp-cms--eyebrow-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 14px;
  line-height: 1;
  letter-spacing: 1.5px;
  font-weight: 600;
}

.emotion-13 .amp-cms--eyebrow-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1;
  letter-spacing: 1.5px;
  font-weight: 600;
}

.emotion-13 .amp-cms--eyebrow-3 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 10px;
  line-height: 1;
  letter-spacing: 1.5px;
  font-weight: 600;
}

.emotion-13 .amp-cms--headline-1 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 48px;
  line-height: 1;
  letter-spacing: 0.5px;
  font-weight: 300;
}

.emotion-13 .amp-cms--headline-2 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 40px;
  line-height: 1;
  letter-spacing: 0.5px;
  font-weight: 300;
}

.emotion-13 .amp-cms--headline-3 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 32px;
  line-height: 1.0625;
  letter-spacing: 0.5px;
  font-weight: 300;
}

.emotion-13 .amp-cms--headline-4 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 28px;
  line-height: 1.0714285714285714;
  letter-spacing: 0.5px;
  font-weight: 300;
}

.emotion-13 .amp-cms--headline-5 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 24px;
  line-height: 1.0833333333333333;
  letter-spacing: 0.5px;
  font-weight: 300;
}

.emotion-13 .amp-cms--headline-6 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 18px;
  line-height: 1.1111111111111112;
  letter-spacing: 0.5px;
  font-weight: 300;
}

.emotion-13 .amp-cms--headline-7 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 14px;
  line-height: 1.1428571428571428;
  letter-spacing: 0.5px;
  font-weight: 400;
}

.emotion-13 .amp-cms--headlineAlt-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 32px;
  line-height: 1.0625;
  letter-spacing: 0.5px;
  font-weight: 400;
}

.emotion-13 .amp-cms--headlineAlt-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 24px;
  line-height: 1.0833333333333333;
  letter-spacing: 0.5px;
  font-weight: 400;
}

.emotion-13 .amp-cms--headlineAlt-3 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 20px;
  line-height: 1.1;
  letter-spacing: 0.5px;
  font-weight: 400;
}

.emotion-13 .amp-cms--headlineAlt-4 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 60px;
  line-height: 1;
  letter-spacing: 0.5px;
  font-weight: 300;
}

.emotion-13 .amp-cms--headlineAlt-5 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 14px;
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-13 .amp-cms--headlineAlt-6 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 14px;
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-13 .amp-cms--headlineAlt-7 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 14px;
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-13 .amp-cms--promo-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 8px;
  line-height: 1.5;
  letter-spacing: 0.5px;
  font-weight: 400;
}

.emotion-13 .amp-cms--promo-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 40px;
  line-height: 1;
  letter-spacing: 0px;
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-13 .amp-cms--promoAlt-1 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 14px;
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 600;
}

.emotion-13 .amp-cms--subhead-1 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 14px;
  line-height: 1.2857142857142858;
  letter-spacing: 0.5px;
  font-weight: 300;
}

.emotion-13 .amp-cms--subhead-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 16px;
  line-height: 1;
  letter-spacing: 0.5px;
  font-weight: 350;
}

.emotion-13 .amp-cms--subhead-3 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1;
  letter-spacing: 0.5px;
  font-weight: 350;
}

.emotion-14 {
  padding-top: 8px;
}

.emotion-14 .amp-cms--p {
  padding: 0;
  margin: 0;
  line-height: 0;
}

.emotion-14 .amp-cms--p>span {
  display: inline;
  white-space: break-spaces;
}

.emotion-14 .amp-cms--p a {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  position: relative;
  z-index: 2;
  pointer-events: auto;
}

.emotion-14 .amp-cms--p a:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-14 .amp-cms--legal-copy {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 10px;
  line-height: 1.8;
  letter-spacing: 0.5px;
  font-weight: 350;
}

.emotion-14 sup {
  vertical-align: top;
  display: inline-block;
  margin-top: -0.25ex;
}

.emotion-14 sub {
  vertical-align: bottom;
  display: inline-block;
  margin-bottom: -0.25ex;
}

.emotion-14 .amp-cms--f-0 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 11px;
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-14 .amp-cms--f-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 11px;
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-14 .amp-cms--f-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 11px;
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-14 .amp-cms--fn-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 11px;
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-14 .amp-cms--body-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.5;
  letter-spacing: 0.5px;
  font-weight: 350;
}

.emotion-14 .amp-cms--body-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 16px;
  line-height: 1.625;
  letter-spacing: 0.5px;
  font-weight: 350;
}

.emotion-14 .amp-cms--body-3 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 14px;
  line-height: 1.5714285714285714;
  letter-spacing: 0.5px;
  font-weight: 350;
}

.emotion-14 .amp-cms--body-4 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.6666666666666667;
  letter-spacing: 0.5px;
  font-weight: 350;
}

.emotion-14 .amp-cms--body-5 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 10px;
  line-height: 1.8;
  letter-spacing: 0.5px;
  font-weight: 350;
}

.emotion-14 .amp-cms--eyebrow-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 14px;
  line-height: 1;
  letter-spacing: 1.5px;
  font-weight: 600;
}

.emotion-14 .amp-cms--eyebrow-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1;
  letter-spacing: 1.5px;
  font-weight: 600;
}

.emotion-14 .amp-cms--eyebrow-3 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 10px;
  line-height: 1;
  letter-spacing: 1.5px;
  font-weight: 600;
}

.emotion-14 .amp-cms--headline-1 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 48px;
  line-height: 1;
  letter-spacing: 0.5px;
  font-weight: 300;
}

.emotion-14 .amp-cms--headline-2 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 40px;
  line-height: 1;
  letter-spacing: 0.5px;
  font-weight: 300;
}

.emotion-14 .amp-cms--headline-3 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 32px;
  line-height: 1.0625;
  letter-spacing: 0.5px;
  font-weight: 300;
}

.emotion-14 .amp-cms--headline-4 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 28px;
  line-height: 1.0714285714285714;
  letter-spacing: 0.5px;
  font-weight: 300;
}

.emotion-14 .amp-cms--headline-5 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 24px;
  line-height: 1.0833333333333333;
  letter-spacing: 0.5px;
  font-weight: 300;
}

.emotion-14 .amp-cms--headline-6 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 18px;
  line-height: 1.1111111111111112;
  letter-spacing: 0.5px;
  font-weight: 300;
}

.emotion-14 .amp-cms--headline-7 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 14px;
  line-height: 1.1428571428571428;
  letter-spacing: 0.5px;
  font-weight: 400;
}

.emotion-14 .amp-cms--headlineAlt-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 32px;
  line-height: 1.0625;
  letter-spacing: 0.5px;
  font-weight: 400;
}

.emotion-14 .amp-cms--headlineAlt-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 24px;
  line-height: 1.0833333333333333;
  letter-spacing: 0.5px;
  font-weight: 400;
}

.emotion-14 .amp-cms--headlineAlt-3 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 20px;
  line-height: 1.1;
  letter-spacing: 0.5px;
  font-weight: 400;
}

.emotion-14 .amp-cms--headlineAlt-4 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 60px;
  line-height: 1;
  letter-spacing: 0.5px;
  font-weight: 300;
}

.emotion-14 .amp-cms--headlineAlt-5 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 14px;
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-14 .amp-cms--headlineAlt-6 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 14px;
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-14 .amp-cms--headlineAlt-7 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 14px;
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-14 .amp-cms--promo-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 8px;
  line-height: 1.5;
  letter-spacing: 0.5px;
  font-weight: 400;
}

.emotion-14 .amp-cms--promo-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 40px;
  line-height: 1;
  letter-spacing: 0px;
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-14 .amp-cms--promoAlt-1 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 14px;
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 600;
}

.emotion-14 .amp-cms--subhead-1 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 14px;
  line-height: 1.2857142857142858;
  letter-spacing: 0.5px;
  font-weight: 300;
}

.emotion-14 .amp-cms--subhead-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 16px;
  line-height: 1;
  letter-spacing: 0.5px;
  font-weight: 350;
}

.emotion-14 .amp-cms--subhead-3 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1;
  letter-spacing: 0.5px;
  font-weight: 350;
}

.emotion-15 {
  background-color: transparent;
  box-shadow: none;
  box-sizing: border-box;
  border: 0;
  border-bottom: 1px solid transparent;
  -webkit-transition: border-bottom-color 0.2s ease;
  transition: border-bottom-color 0.2s ease;
}

.emotion-97.slick-next.slick-arrow {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: var(--size);
  height: var(--size);
  z-index: 2;
  --margin: 10px;
  --size: 40px;
  margin: var(--margin);
  --cardsHeight: 529.07px;
  top: calc(var(--cardsHeight) / 2 - var(--margin));
}

.emotion-97.slick-next.slick-arrow.slick-disabled {
  display: none;
}

.emotion-97.slick-next.slick-arrow>span {
  margin: auto;
  height: auto;
  width: auto;
  min-height: auto;
  min-width: auto;
}

.emotion-97.slick-next.slick-arrow svg {
  height: var(--size);
  width: var(--size);
}

.emotion-98 {
  display: inline-block;
  height: 2rem;
  width: 2rem;
  min-height: 2rem;
  min-width: 2rem;
  -webkit-transform: rotate(90deg);
  -moz-transform: rotate(90deg);
  -ms-transform: rotate(90deg);
  transform: rotate(90deg);
}

.emotion-98 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

<div>
  <div
    style="font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;"
  >
    <div
      class="emotion-0"
    >
      <article
        class="emotion-1"
      >
        <div
          class="emotion-2"
        >
          <div>
            <p
              class="amp-cms--p"
              style="text-align:left;"
            >
              <span
                class="amp-cms--headline-5"
              >
                Lorem ipsum dolor
              </span>
              <span
                class="amp-cms--body-1"
              >
                 sit amet consectetur adipiscing elit sed do eiusmod.
              </span>
            </p>
          </div>
        </div>
        <div
          class="emotion-3"
        >
          <div
            class="emotion-4"
          >
            <div
              class="emotion-5"
            >
              <div
                class="emotion-6"
              >
                <div
                  class="slick-slider slick-initialized"
                  dir="ltr"
                >
                  <div
                    class="slick-list"
                  >
                    <div
                      class="slick-track"
                      style="opacity: 1; transform: translate3d(0px, 0px, 0px);"
                    >
                      <div
                        aria-hidden="false"
                        class="slick-slide slick-active slick-current"
                        data-index="0"
                        style="outline: none; width: 0px;"
                        tabindex="-1"
                      >
                        <div>
                          <a
                            class="category-card emotion-7"
                            href="https://www.google.com"
                            target="_self"
                          >
                            <div
                              aria-live="polite"
                              class="emotion-8"
                            >
                              <div
                                aria-hidden="false"
                                class="emotion-9"
                              >
                                <img
                                  alt="Woman"
                                  class="emotion-10"
                                  src=""
                                />
                              </div>
                            </div>
                            <div
                              class="emotion-11"
                            >
                              <div
                                class="emotion-12"
                              >
                                <div
                                  class="emotion-13"
                                >
                                  <div>
                                    <p
                                      class="amp-cms--p"
                                      style="text-align:center;"
                                    >
                                      <span
                                        class="amp-cms--eyebrow-1"
                                        style="font-weight:700"
                                      >
                                        Straight
                                      </span>
                                    </p>
                                  </div>
                                </div>
                                <div
                                  class="emotion-14"
                                >
                                  <div>
                                    <p
                                      class="amp-cms--p"
                                      style="text-align:center;"
                                    >
                                      <span
                                        class="amp-cms--body-2"
                                      >
                                        sit amet consectetur adipiscing elit eiusmod.
                                      </span>
                                    </p>
                                  </div>
                                </div>
                              </div>
                              <div
                                class="emotion-15"
                                data-testid="category-card-bottom-border"
                              />
                            </div>
                          </a>
                        </div>
                      </div>
                      <div
                        aria-hidden="false"
                        class="slick-slide slick-active"
                        data-index="1"
                        style="outline: none; width: 0px;"
                        tabindex="-1"
                      >
                        <div>
                          <a
                            class="category-card emotion-7"
                            href="https://www.google.com"
                            target="_self"
                          >
                            <div
                              aria-live="polite"
                              class="emotion-8"
                            >
                              <div
                                aria-hidden="false"
                                class="emotion-9"
                              >
                                <img
                                  alt="Man"
                                  class="emotion-10"
                                  src=""
                                />
                              </div>
                            </div>
                            <div
                              class="emotion-11"
                            >
                              <div
                                class="emotion-12"
                              >
                                <div
                                  class="emotion-13"
                                >
                                  <div>
                                    <p
                                      class="amp-cms--p"
                                      style="text-align:center;"
                                    >
                                      <span
                                        class="amp-cms--eyebrow-1"
                                        style="font-weight:700"
                                      >
                                        Skinny
                                      </span>
                                    </p>
                                  </div>
                                </div>
                                <div
                                  class="emotion-14"
                                >
                                  <div>
                                    <p
                                      class="amp-cms--p"
                                      style="text-align:center;"
                                    >
                                      <span
                                        class="amp-cms--body-2"
                                      >
                                        sit amet consectetur adipiscing elit eiusmod.
                                      </span>
                                    </p>
                                  </div>
                                </div>
                              </div>
                              <div
                                class="emotion-15"
                                data-testid="category-card-bottom-border"
                              />
                            </div>
                          </a>
                        </div>
                      </div>
                      <div
                        aria-hidden="false"
                        class="slick-slide slick-active"
                        data-index="2"
                        style="outline: none; width: 0px;"
                        tabindex="-1"
                      >
                        <div>
                          <a
                            class="category-card emotion-7"
                            href="https://www.google.com"
                            target="_self"
                          >
                            <div
                              aria-live="polite"
                              class="emotion-8"
                            >
                              <div
                                aria-hidden="false"
                                class="emotion-9"
                              >
                                <img
                                  alt="Jeans"
                                  class="emotion-10"
                                  src=""
                                />
                              </div>
                            </div>
                            <div
                              class="emotion-11"
                            >
                              <div
                                class="emotion-12"
                              >
                                <div
                                  class="emotion-13"
                                >
                                  <div>
                                    <p
                                      class="amp-cms--p"
                                      style="text-align:center;"
                                    >
                                      <span
                                        class="amp-cms--eyebrow-1"
                                        style="font-weight:700"
                                      >
                                        Super-skinny
                                      </span>
                                    </p>
                                  </div>
                                </div>
                                <div
                                  class="emotion-14"
                                >
                                  <div>
                                    <p
                                      class="amp-cms--p"
                                      style="text-align:center;"
                                    >
                                      <span
                                        class="amp-cms--body-2"
                                      >
                                        sit amet consectetur adipiscing elit eiusmod.
                                      </span>
                                    </p>
                                  </div>
                                </div>
                              </div>
                              <div
                                class="emotion-15"
                                data-testid="category-card-bottom-border"
                              />
                            </div>
                          </a>
                        </div>
                      </div>
                      <div
                        aria-hidden="true"
                        class="slick-slide"
                        data-index="3"
                        style="outline: none; width: 0px;"
                        tabindex="-1"
                      >
                        <div>
                          <a
                            class="category-card emotion-7"
                            href="https://www.gap.com"
                            target="_self"
                          >
                            <div
                              aria-live="polite"
                              class="emotion-8"
                            >
                              <div
                                aria-hidden="false"
                                class="emotion-9"
                              >
                                <img
                                  alt="Another Man"
                                  class="emotion-10"
                                  src=""
                                />
                              </div>
                            </div>
                            <div
                              class="emotion-11"
                            >
                              <div
                                class="emotion-12"
                              >
                                <div
                                  class="emotion-13"
                                >
                                  <div>
                                    <p
                                      class="amp-cms--p"
                                      style="text-align:center;"
                                    >
                                      <span
                                        class="amp-cms--eyebrow-1"
                                        style="font-weight:700;text-decoration:underline"
                                      >
                                        Hover Headline
                                      </span>
                                    </p>
                                  </div>
                                </div>
                                <div
                                  class="emotion-14"
                                >
                                  <div>
                                    <p
                                      class="amp-cms--p"
                                      style="text-align:center;"
                                    >
                                      <span
                                        class="amp-cms--body-2"
                                      >
                                        sit amet consectetur adipiscing elit eiusmod.
                                      </span>
                                    </p>
                                  </div>
                                </div>
                              </div>
                              <div
                                class="emotion-15"
                                data-testid="category-card-bottom-border"
                              />
                            </div>
                          </a>
                        </div>
                      </div>
                      <div
                        aria-hidden="true"
                        class="slick-slide"
                        data-index="4"
                        style="outline: none; width: 0px;"
                        tabindex="-1"
                      >
                        <div>
                          <a
                            class="category-card emotion-7"
                            href="https://www.gap.com"
                            target="_self"
                          >
                            <div
                              aria-live="polite"
                              class="emotion-8"
                            >
                              <div
                                aria-hidden="false"
                                class="emotion-9"
                              >
                                <img
                                  alt="Posing"
                                  class="emotion-10"
                                  src=""
                                />
                              </div>
                            </div>
                            <div
                              class="emotion-11"
                            >
                              <div
                                class="emotion-12"
                              >
                                <div
                                  class="emotion-13"
                                >
                                  <div>
                                    <p
                                      class="amp-cms--p"
                                      style="text-align:center;"
                                    >
                                      <span
                                        class="amp-cms--eyebrow-1"
                                        style="font-weight:700;text-decoration:underline"
                                      >
                                        Selected Headline
                                      </span>
                                    </p>
                                  </div>
                                </div>
                                <div
                                  class="emotion-14"
                                >
                                  <div>
                                    <p
                                      class="amp-cms--p"
                                      style="text-align:center;"
                                    >
                                      <span
                                        class="amp-cms--body-2"
                                      >
                                        sit amet consectetur adipiscing elit eiusmod.
                                      </span>
                                    </p>
                                  </div>
                                </div>
                              </div>
                              <div
                                class="emotion-15"
                                data-testid="category-card-bottom-border"
                              />
                            </div>
                          </a>
                        </div>
                      </div>
                      <div
                        aria-hidden="true"
                        class="slick-slide"
                        data-index="5"
                        style="outline: none; width: 0px;"
                        tabindex="-1"
                      >
                        <div>
                          <a
                            class="category-card emotion-7"
                            href="https://www.google.com"
                            target="_self"
                          >
                            <div
                              aria-live="polite"
                              class="emotion-8"
                            >
                              <div
                                aria-hidden="false"
                                class="emotion-9"
                              >
                                <img
                                  alt="Yoga"
                                  class="emotion-10"
                                  src=""
                                />
                              </div>
                            </div>
                            <div
                              class="emotion-11"
                            >
                              <div
                                class="emotion-12"
                              >
                                <div
                                  class="emotion-13"
                                >
                                  <div>
                                    <p
                                      class="amp-cms--p"
                                      style="text-align:center;"
                                    >
                                      <span
                                        class="amp-cms--eyebrow-1"
                                        style="font-weight:700"
                                      >
                                        Wide-Leg
                                      </span>
                                    </p>
                                  </div>
                                </div>
                                <div
                                  class="emotion-14"
                                >
                                  <div>
                                    <p
                                      class="amp-cms--p"
                                      style="text-align:center;"
                                    >
                                      <span
                                        class="amp-cms--body-2"
                                      >
                                        sit amet consectetur adipiscing elit eiusmod.
                                      </span>
                                    </p>
                                  </div>
                                </div>
                              </div>
                              <div
                                class="emotion-15"
                                data-testid="category-card-bottom-border"
                              />
                            </div>
                          </a>
                        </div>
                      </div>
                      <div
                        aria-hidden="true"
                        class="slick-slide"
                        data-index="6"
                        style="outline: none; width: 0px;"
                        tabindex="-1"
                      >
                        <div>
                          <a
                            class="category-card emotion-7"
                            href="htttps://www.gap.com"
                            target="_self"
                          >
                            <div
                              aria-live="polite"
                              class="emotion-8"
                            >
                              <div
                                aria-hidden="false"
                                class="emotion-9"
                              >
                                <img
                                  alt="People"
                                  class="emotion-10"
                                  src=""
                                />
                              </div>
                            </div>
                            <div
                              class="emotion-11"
                            >
                              <div
                                class="emotion-12"
                              >
                                <div
                                  class="emotion-13"
                                >
                                  <div>
                                    <p
                                      class="amp-cms--p"
                                      style="text-align:center;"
                                    >
                                      <span
                                        class="amp-cms--eyebrow-1"
                                        style="font-weight:700"
                                      >
                                        Skinny-Leg
                                      </span>
                                    </p>
                                  </div>
                                </div>
                                <div
                                  class="emotion-14"
                                >
                                  <div>
                                    <p
                                      class="amp-cms--p"
                                      style="text-align:center;"
                                    >
                                      <span
                                        class="amp-cms--body-2"
                                      >
                                        sit amet consectetur adipiscing elit eiusmod.
                                      </span>
                                    </p>
                                  </div>
                                </div>
                              </div>
                              <div
                                class="emotion-15"
                                data-testid="category-card-bottom-border"
                              />
                            </div>
                          </a>
                        </div>
                      </div>
                      <div
                        aria-hidden="true"
                        class="slick-slide"
                        data-index="7"
                        style="outline: none; width: 0px;"
                        tabindex="-1"
                      >
                        <div>
                          <a
                            class="category-card emotion-7"
                            href="https://www.google.com"
                            target="_self"
                          >
                            <div
                              aria-live="polite"
                              class="emotion-8"
                            >
                              <div
                                aria-hidden="false"
                                class="emotion-9"
                              >
                                <img
                                  alt="Woman"
                                  class="emotion-10"
                                  src=""
                                />
                              </div>
                            </div>
                            <div
                              class="emotion-11"
                            >
                              <div
                                class="emotion-12"
                              >
                                <div
                                  class="emotion-13"
                                >
                                  <div>
                                    <p
                                      class="amp-cms--p"
                                      style="text-align:center;"
                                    >
                                      <span
                                        class="amp-cms--eyebrow-1"
                                        style="font-weight:700"
                                      >
                                        Hover
                                      </span>
                                    </p>
                                  </div>
                                </div>
                                <div
                                  class="emotion-14"
                                >
                                  <div>
                                    <p
                                      class="amp-cms--p"
                                      style="text-align:center;"
                                    >
                                      <span
                                        class="amp-cms--body-2"
                                      >
                                        sit amet consectetur adipiscing elit sed do.
                                      </span>
                                    </p>
                                  </div>
                                </div>
                              </div>
                              <div
                                class="emotion-15"
                                data-testid="category-card-bottom-border"
                              />
                            </div>
                          </a>
                        </div>
                      </div>
                      <div
                        aria-hidden="true"
                        class="slick-slide"
                        data-index="8"
                        style="outline: none; width: 0px;"
                        tabindex="-1"
                      >
                        <div>
                          <a
                            class="category-card emotion-7"
                            href="https://www.google.com"
                            target="_self"
                          >
                            <div
                              aria-live="polite"
                              class="emotion-8"
                            >
                              <div
                                aria-hidden="false"
                                class="emotion-9"
                              >
                                <img
                                  alt="Man"
                                  class="emotion-10"
                                  src=""
                                />
                              </div>
                            </div>
                            <div
                              class="emotion-11"
                            >
                              <div
                                class="emotion-12"
                              >
                                <div
                                  class="emotion-13"
                                >
                                  <div>
                                    <p
                                      class="amp-cms--p"
                                      style="text-align:center;"
                                    >
                                      <span
                                        class="amp-cms--eyebrow-1"
                                        style="font-weight:700"
                                      >
                                        Selected
                                      </span>
                                    </p>
                                  </div>
                                </div>
                                <div
                                  class="emotion-14"
                                >
                                  <div>
                                    <p
                                      class="amp-cms--p"
                                      style="text-align:center;"
                                    >
                                      <span
                                        class="amp-cms--body-2"
                                      >
                                        sit amet consectetur adipiscing elit sed do.
                                      </span>
                                    </p>
                                  </div>
                                </div>
                              </div>
                              <div
                                class="emotion-15"
                                data-testid="category-card-bottom-border"
                              />
                            </div>
                          </a>
                        </div>
                      </div>
                      <div
                        aria-hidden="true"
                        class="slick-slide"
                        data-index="9"
                        style="outline: none; width: 0px;"
                        tabindex="-1"
                      >
                        <div>
                          <a
                            class="category-card emotion-7"
                            href="https://www,gap.com"
                            target="_self"
                          >
                            <div
                              aria-live="polite"
                              class="emotion-8"
                            >
                              <div
                                aria-hidden="false"
                                class="emotion-9"
                              >
                                <img
                                  alt="Another Man"
                                  class="emotion-10"
                                  src=""
                                />
                              </div>
                            </div>
                            <div
                              class="emotion-11"
                            >
                              <div
                                class="emotion-12"
                              >
                                <div
                                  class="emotion-13"
                                >
                                  <div>
                                    <p
                                      class="amp-cms--p"
                                      style="text-align:center;"
                                    >
                                      <span
                                        class="amp-cms--eyebrow-1"
                                        style="font-weight:700"
                                      >
                                        Card 10
                                      </span>
                                    </p>
                                  </div>
                                </div>
                                <div
                                  class="emotion-14"
                                >
                                  <div>
                                    <p
                                      class="amp-cms--p"
                                      style="text-align:center;"
                                    >
                                      <span
                                        class="amp-cms--body-2"
                                      >
                                        sit amet consectetur adipiscing elit sed do.
                                      </span>
                                    </p>
                                  </div>
                                </div>
                              </div>
                              <div
                                class="emotion-15"
                                data-testid="category-card-bottom-border"
                              />
                            </div>
                          </a>
                        </div>
                      </div>
                    </div>
                  </div>
                  <button
                    aria-label="Next"
                    class="slick-arrow slick-next emotion-97"
                  >
                    <span
                      aria-hidden="true"
                      class="emotion-98"
                    >
                      <svg
                        fill="none"
                        viewBox="0 0 41 40"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <g
                          opacity="0.8"
                        >
                          <rect
                            fill="#000000"
                            height="40"
                            rx="20"
                            width="40"
                            x="0.5"
                          />
                          <path
                            d="M14.5 20.35C14.3067 20.35 14.15 20.1933 14.15 20C14.15 19.8067 14.3067 19.65 14.5 19.65V20.35ZM26.7475 19.7525C26.8842 19.8892 26.8842 20.1108 26.7475 20.2475L24.5201 22.4749C24.3834 22.6116 24.1618 22.6116 24.0251 22.4749C23.8884 22.3382 23.8884 22.1166 24.0251 21.9799L26.005 20L24.0251 18.0201C23.8884 17.8834 23.8884 17.6618 24.0251 17.5251C24.1618 17.3884 24.3834 17.3884 24.5201 17.5251L26.7475 19.7525ZM14.5 19.65L26.5 19.65V20.35L14.5 20.35V19.65Z"
                            fill="#FFFFFF"
                          />
                        </g>
                      </svg>
                    </span>
                  </button>
                  <ul
                    class="slick-dots"
                    style="display: block;"
                  >
                    <li
                      class="slick-active"
                    >
                      <button>
                        1
                      </button>
                    </li>
                    <li
                      class=""
                    >
                      <button>
                        2
                      </button>
                    </li>
                    <li
                      class=""
                    >
                      <button>
                        3
                      </button>
                    </li>
                    <li
                      class=""
                    >
                      <button>
                        4
                      </button>
                    </li>
                    <li
                      class=""
                    >
                      <button>
                        5
                      </button>
                    </li>
                    <li
                      class=""
                    >
                      <button>
                        6
                      </button>
                    </li>
                    <li
                      class=""
                    >
                      <button>
                        7
                      </button>
                    </li>
                    <li
                      class=""
                    >
                      <button>
                        8
                      </button>
                    </li>
                    <li
                      class=""
                    >
                      <button>
                        9
                      </button>
                    </li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </div>
      </article>
    </div>
  </div>
</div>
`;

exports[`VisualNavigationCarouselBR on mobile should match snapshot with toggles 1`] = `
.emotion-0 {
  margin-left: auto;
  margin-right: auto;
  max-width: 1280px;
}

.emotion-1 {
  min-height: 235px;
}

.emotion-2 {
  padding-bottom: 24px;
  padding-inline: 10px;
  padding-top: 40px;
}

.emotion-2 .amp-cms--p {
  padding: 0;
  margin: 0;
  line-height: 0;
}

.emotion-2 .amp-cms--p>span {
  display: inline;
  white-space: break-spaces;
}

.emotion-2 .amp-cms--p a {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  position: relative;
  z-index: 2;
  pointer-events: auto;
}

.emotion-2 .amp-cms--p a:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-2 .amp-cms--legal-copy {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(8px, min(2.666666666666667vw, 10px));
  line-height: 1.8;
  letter-spacing: min(0.13333333333333333vw, 0.5px);
  font-weight: 350;
}

.emotion-2 sup {
  vertical-align: top;
  display: inline-block;
  margin-top: -0.25ex;
}

.emotion-2 sub {
  vertical-align: bottom;
  display: inline-block;
  margin-bottom: -0.25ex;
}

.emotion-2 .amp-cms--f-0 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(2.933333333333333vw, 11px);
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-2 .amp-cms--f-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(2.933333333333333vw, 11px);
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-2 .amp-cms--f-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(2.933333333333333vw, 11px);
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-2 .amp-cms--fn-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(2.933333333333333vw, 11px);
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-2 .amp-cms--body-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(3.2vw, 12px));
  line-height: 1.5;
  letter-spacing: min(0.13333333333333333vw, 0.5px);
  font-weight: 350;
}

.emotion-2 .amp-cms--body-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(4.266666666666667vw, 16px));
  line-height: 1.625;
  letter-spacing: min(0.13333333333333333vw, 0.5px);
  font-weight: 350;
}

.emotion-2 .amp-cms--body-3 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(3.733333333333334vw, 14px));
  line-height: 1.5714285714285714;
  letter-spacing: min(0.13333333333333333vw, 0.5px);
  font-weight: 350;
}

.emotion-2 .amp-cms--body-4 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(3.2vw, 12px));
  line-height: 1.6666666666666667;
  letter-spacing: min(0.13333333333333333vw, 0.5px);
  font-weight: 350;
}

.emotion-2 .amp-cms--body-5 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(8px, min(2.666666666666667vw, 10px));
  line-height: 1.8;
  letter-spacing: min(0.13333333333333333vw, 0.5px);
  font-weight: 350;
}

.emotion-2 .amp-cms--eyebrow-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(3.733333333333334vw, 14px));
  line-height: 1;
  letter-spacing: min(0.4vw, 1.5px);
  font-weight: 600;
}

.emotion-2 .amp-cms--eyebrow-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(11px, min(3.2vw, 12px));
  line-height: 1;
  letter-spacing: min(0.4vw, 1.5px);
  font-weight: 600;
}

.emotion-2 .amp-cms--eyebrow-3 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(2.666666666666667vw, 10px));
  line-height: 1;
  letter-spacing: min(0.4vw, 1.5px);
  font-weight: 600;
}

.emotion-2 .amp-cms--headline-1 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(12.8vw, 48px));
  line-height: 1;
  letter-spacing: min(0.13333333333333333vw, 0.5px);
  font-weight: 300;
}

.emotion-2 .amp-cms--headline-2 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(10.666666666666668vw, 40px));
  line-height: 1;
  letter-spacing: min(0.13333333333333333vw, 0.5px);
  font-weight: 300;
}

.emotion-2 .amp-cms--headline-3 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(8.533333333333333vw, 32px));
  line-height: 1.0625;
  letter-spacing: min(0.13333333333333333vw, 0.5px);
  font-weight: 300;
}

.emotion-2 .amp-cms--headline-4 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(7.466666666666668vw, 28px));
  line-height: 1.0714285714285714;
  letter-spacing: min(0.13333333333333333vw, 0.5px);
  font-weight: 300;
}

.emotion-2 .amp-cms--headline-5 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(6.4vw, 24px));
  line-height: 1.0833333333333333;
  letter-spacing: min(0.13333333333333333vw, 0.5px);
  font-weight: 300;
}

.emotion-2 .amp-cms--headline-6 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(4.8vw, 18px));
  line-height: 1.1111111111111112;
  letter-spacing: min(0.13333333333333333vw, 0.5px);
  font-weight: 300;
}

.emotion-2 .amp-cms--headline-7 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(3.733333333333334vw, 14px));
  line-height: 1.1428571428571428;
  letter-spacing: min(0.13333333333333333vw, 0.5px);
  font-weight: 400;
}

.emotion-2 .amp-cms--headlineAlt-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(8.533333333333333vw, 32px));
  line-height: 1.0625;
  letter-spacing: min(0.13333333333333333vw, 0.5px);
  font-weight: 400;
}

.emotion-2 .amp-cms--headlineAlt-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(6.4vw, 24px));
  line-height: 1.0833333333333333;
  letter-spacing: min(0.13333333333333333vw, 0.5px);
  font-weight: 400;
}

.emotion-2 .amp-cms--headlineAlt-3 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(5.333333333333334vw, 20px));
  line-height: 1.1;
  letter-spacing: min(0.13333333333333333vw, 0.5px);
  font-weight: 400;
}

.emotion-2 .amp-cms--headlineAlt-4 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(16vw, 60px));
  line-height: 1;
  letter-spacing: min(0.13333333333333333vw, 0.5px);
  font-weight: 300;
}

.emotion-2 .amp-cms--headlineAlt-5 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(3.733333333333334vw, 14px);
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-2 .amp-cms--headlineAlt-6 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(3.733333333333334vw, 14px);
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-2 .amp-cms--headlineAlt-7 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(3.733333333333334vw, 14px);
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-2 .amp-cms--promo-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(8px, min(2.1333333333333333vw, 8px));
  line-height: 1.5;
  letter-spacing: min(0.13333333333333333vw, 0.5px);
  font-weight: 400;
}

.emotion-2 .amp-cms--promo-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(13px, min(10.666666666666668vw, 40px));
  line-height: 1;
  letter-spacing: min(0vw, 0px);
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-2 .amp-cms--promoAlt-1 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(3.733333333333334vw, 14px);
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 600;
}

.emotion-2 .amp-cms--subhead-1 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(3.733333333333334vw, 14px));
  line-height: 1.2857142857142858;
  letter-spacing: min(0.13333333333333333vw, 0.5px);
  font-weight: 300;
}

.emotion-2 .amp-cms--subhead-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(4.266666666666667vw, 16px));
  line-height: 1;
  letter-spacing: min(0.13333333333333333vw, 0.5px);
  font-weight: 350;
}

.emotion-2 .amp-cms--subhead-3 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(3.2vw, 12px));
  line-height: 1;
  letter-spacing: min(0.13333333333333333vw, 0.5px);
  font-weight: 350;
}

.emotion-3 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  border-bottom: 1px solid transparent;
}

.emotion-4 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  margin-bottom: 0;
}

.emotion-5 {
  overflow: hidden;
}

.emotion-5 div.slick-track {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  gap: 0;
}

.emotion-5 div.slick-track:before {
  display: none;
}

.emotion-5 div.slick-track:after {
  display: none;
}

.emotion-5 div.slick-slide {
  height: auto;
}

.emotion-5 div.slick-slide>div {
  height: 100%;
}

.emotion-6 {
  position: relative;
}

.emotion-6 .slick-slider {
  position: relative;
  display: block;
  box-sizing: border-box;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  -webkit-touch-callout: none;
  touch-action: pan-y;
  -webkit-tap-highlight-color: transparent;
  scroll-behavior: smooth;
}

.emotion-6 .slick-slider .slick-track,
.emotion-6 .slick-slider .slick-list {
  -webkit-transform: translate3d(0, 0, 0);
  -moz-transform: translate3d(0, 0, 0);
  -ms-transform: translate3d(0, 0, 0);
  transform: translate3d(0, 0, 0);
}

.emotion-6 .slick-list {
  position: relative;
  display: block;
  overflow-x: hidden;
  overflow-y: hidden;
  margin: 0;
  padding: 0;
}

.emotion-6 .slick-list:focus {
  outline: none;
}

.emotion-6 .slick-list.dragging {
  cursor: pointer;
  cursor: hand;
}

.emotion-6 .slick-track {
  position: relative;
  top: 0;
  left: 0;
  display: block;
  margin-left: auto;
  margin-right: auto;
}

.emotion-6 .slick-track:before,
.emotion-6 .slick-track:after {
  display: table;
  content: "";
}

.emotion-6 .slick-track:after {
  clear: both;
}

.emotion-6 .slick-slide {
  display: none;
  float: left;
  height: 100%;
  min-height: 1px;
}

.emotion-6 .slick-slide img {
  display: block;
}

.emotion-6 .slick-slide.slick-loading img {
  display: none;
}

.emotion-6 .slick-slide.dragging img {
  pointer-events: none;
}

.emotion-6 [dir="rtl"] .slick-slide {
  float: right;
}

.emotion-6 .slick-initialized .slick-slide,
.emotion-6 .slick-vertical .slick-slide {
  display: block;
}

.emotion-6 .slick-vertical .slick-slide {
  border: 1px solid transparent;
  height: auto;
}

.emotion-6 .slick-loading .slick-track,
.emotion-6 .slick-loading .slick-slide {
  visibility: hidden;
}

.emotion-6 .slick-loading .slick-list {
  background: #fff url("./ajax-loader.gif") center center no-repeat;
}

.emotion-6 .slick-arrow.slick-hidden {
  display: none;
}

.emotion-6 .slick-prev,
.emotion-6 .slick-next {
  font-size: 0;
  line-height: 0;
  position: absolute;
  top: 50%;
  display: block;
  padding: 0;
  cursor: pointer;
  color: transparent;
  border: none;
  outline: none;
  background: transparent;
}

.emotion-6 .slick-prev:hover,
.emotion-6 .slick-next:hover,
.emotion-6 .slick-prev:focus,
.emotion-6 .slick-next:focus {
  color: transparent;
  outline: none;
  background: transparent;
  opacity: 1;
}

.emotion-6 .slick-prev.slick-disabled,
.emotion-6 .slick-next.slick-disabled {
  opacity: 0.25;
}

.emotion-6 .slick-prev {
  left: -0px;
  -webkit-transform: translate(0, -50%) rotate(90deg);
  -moz-transform: translate(0, -50%) rotate(90deg);
  -ms-transform: translate(0, -50%) rotate(90deg);
  transform: translate(0, -50%) rotate(90deg);
}

.emotion-6 .slick-prev span {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-6 [dir="rtl"] .slick-prev {
  right: -0px;
  left: auto;
}

.emotion-6 .slick-next {
  right: -0px;
  -webkit-transform: translate(0, -50%) rotate(-90deg);
  -moz-transform: translate(0, -50%) rotate(-90deg);
  -ms-transform: translate(0, -50%) rotate(-90deg);
  transform: translate(0, -50%) rotate(-90deg);
}

.emotion-6 .slick-next span {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-6 [dir="rtl"] .slick-next {
  right: auto;
  left: -0px;
}

.emotion-6 .slick-dotted.slick-slider {
  margin-bottom: 30px;
}

.emotion-6 .slick-dots {
  position: absolute;
  bottom: -25px;
  display: block;
  width: 100%;
  padding: 0;
  margin: 0;
  list-style: none;
  text-align: center;
}

.emotion-6 .slick-dots li {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: 20px;
  height: 20px;
  margin: 0 5px;
  padding: 0;
  cursor: pointer;
}

.emotion-6 .slick-dots li.slick-active button:before {
  opacity: 0.75;
  color: black;
}

.emotion-6 .slick-dots li button {
  font-size: 0;
  line-height: 0;
  display: block;
  padding: 5px;
  cursor: pointer;
  color: transparent;
  border: 0;
  outline: none;
  background: transparent;
}

.emotion-6 .slick-dots li button:hover,
.emotion-6 .slick-dots li button:focus {
  outline: none;
}

.emotion-6 .slick-dots li button:hover:before,
.emotion-6 .slick-dots li button:focus:before,
.emotion-6 .slick-dots li button:hover:before,
.emotion-6 .slick-dots li button:focus:before {
  opacity: 1;
}

.emotion-6 .slick-dots li button:before {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 5px;
  height: 5px;
  content: "";
  opacity: 0.25;
  background-color: black;
  border-radius: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-6 .slick-slider {
  padding-left: 24px;
}

.emotion-6 .slick-list {
  margin: 0 -2px;
}

.emotion-6 .slick-track {
  margin-left: 0!important;
  margin-right: 0!important;
}

.emotion-6 .slick-dots {
  position: unset;
  line-height: 0;
  width: calc(100% + 24px);
  margin-left: -24px;
  margin-top: -1px;
  border-top: 1px solid #E5E5E5;
}

.emotion-6 .slick-dots li {
  margin-top: 35px;
}

.emotion-6 .category-card {
  margin: 0 2px!important;
  overflow: hidden;
}

.emotion-7 {
  outline: none;
  position: relative;
  -webkit-flex: 1;
  -ms-flex: 1;
  flex: 1;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  height: 100%;
  max-width: 396.8px;
}

.emotion-8 {
  overflow: hidden;
  -webkit-transition: background-color .25s ease;
  transition: background-color .25s ease;
  background-color: transparent;
  min-width: 3px;
  position: relative;
}

.emotion-9 {
  box-sizing: border-box;
  aspect-ratio: 3/4;
  position: relative;
  -webkit-transition: -webkit-transform .25s ease,opacity .25s ease;
  transition: transform .25s ease,opacity .25s ease;
  -webkit-transform: none;
  -moz-transform: none;
  -ms-transform: none;
  transform: none;
  opacity: 1;
  width: 100%;
  z-index: 1;
}

.emotion-10 {
  width: 100%;
  aspect-ratio: 3/4;
  object-fit: cover;
}

.emotion-11 {
  background: transparent;
  position: relative;
  box-sizing: border-box;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-box-flex: 1;
  -webkit-flex-grow: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
  z-index: 1;
}

.emotion-12 {
  height: 100%;
  padding: 8px 10px;
  position: relative;
  background-color: white;
}

.emotion-13 .amp-cms--p {
  padding: 0;
  margin: 0;
  line-height: 0;
}

.emotion-13 .amp-cms--p>span {
  display: inline;
  white-space: break-spaces;
}

.emotion-13 .amp-cms--p a {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  position: relative;
  z-index: 2;
  pointer-events: auto;
}

.emotion-13 .amp-cms--p a:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-13 .amp-cms--legal-copy {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 10px;
  line-height: 1.8;
  letter-spacing: 0.5px;
  font-weight: 350;
}

.emotion-13 sup {
  vertical-align: top;
  display: inline-block;
  margin-top: -0.25ex;
}

.emotion-13 sub {
  vertical-align: bottom;
  display: inline-block;
  margin-bottom: -0.25ex;
}

.emotion-13 .amp-cms--f-0 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 11px;
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-13 .amp-cms--f-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 11px;
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-13 .amp-cms--f-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 11px;
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-13 .amp-cms--fn-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 11px;
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-13 .amp-cms--body-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.5;
  letter-spacing: 0.5px;
  font-weight: 350;
}

.emotion-13 .amp-cms--body-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 16px;
  line-height: 1.625;
  letter-spacing: 0.5px;
  font-weight: 350;
}

.emotion-13 .amp-cms--body-3 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 14px;
  line-height: 1.5714285714285714;
  letter-spacing: 0.5px;
  font-weight: 350;
}

.emotion-13 .amp-cms--body-4 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.6666666666666667;
  letter-spacing: 0.5px;
  font-weight: 350;
}

.emotion-13 .amp-cms--body-5 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 10px;
  line-height: 1.8;
  letter-spacing: 0.5px;
  font-weight: 350;
}

.emotion-13 .amp-cms--eyebrow-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 14px;
  line-height: 1;
  letter-spacing: 1.5px;
  font-weight: 600;
}

.emotion-13 .amp-cms--eyebrow-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1;
  letter-spacing: 1.5px;
  font-weight: 600;
}

.emotion-13 .amp-cms--eyebrow-3 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 10px;
  line-height: 1;
  letter-spacing: 1.5px;
  font-weight: 600;
}

.emotion-13 .amp-cms--headline-1 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 48px;
  line-height: 1;
  letter-spacing: 0.5px;
  font-weight: 300;
}

.emotion-13 .amp-cms--headline-2 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 40px;
  line-height: 1;
  letter-spacing: 0.5px;
  font-weight: 300;
}

.emotion-13 .amp-cms--headline-3 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 32px;
  line-height: 1.0625;
  letter-spacing: 0.5px;
  font-weight: 300;
}

.emotion-13 .amp-cms--headline-4 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 28px;
  line-height: 1.0714285714285714;
  letter-spacing: 0.5px;
  font-weight: 300;
}

.emotion-13 .amp-cms--headline-5 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 24px;
  line-height: 1.0833333333333333;
  letter-spacing: 0.5px;
  font-weight: 300;
}

.emotion-13 .amp-cms--headline-6 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 18px;
  line-height: 1.1111111111111112;
  letter-spacing: 0.5px;
  font-weight: 300;
}

.emotion-13 .amp-cms--headline-7 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 14px;
  line-height: 1.1428571428571428;
  letter-spacing: 0.5px;
  font-weight: 400;
}

.emotion-13 .amp-cms--headlineAlt-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 32px;
  line-height: 1.0625;
  letter-spacing: 0.5px;
  font-weight: 400;
}

.emotion-13 .amp-cms--headlineAlt-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 24px;
  line-height: 1.0833333333333333;
  letter-spacing: 0.5px;
  font-weight: 400;
}

.emotion-13 .amp-cms--headlineAlt-3 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 20px;
  line-height: 1.1;
  letter-spacing: 0.5px;
  font-weight: 400;
}

.emotion-13 .amp-cms--headlineAlt-4 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 60px;
  line-height: 1;
  letter-spacing: 0.5px;
  font-weight: 300;
}

.emotion-13 .amp-cms--headlineAlt-5 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 14px;
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-13 .amp-cms--headlineAlt-6 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 14px;
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-13 .amp-cms--headlineAlt-7 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 14px;
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-13 .amp-cms--promo-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 8px;
  line-height: 1.5;
  letter-spacing: 0.5px;
  font-weight: 400;
}

.emotion-13 .amp-cms--promo-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 40px;
  line-height: 1;
  letter-spacing: 0px;
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-13 .amp-cms--promoAlt-1 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 14px;
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 600;
}

.emotion-13 .amp-cms--subhead-1 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 14px;
  line-height: 1.2857142857142858;
  letter-spacing: 0.5px;
  font-weight: 300;
}

.emotion-13 .amp-cms--subhead-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 16px;
  line-height: 1;
  letter-spacing: 0.5px;
  font-weight: 350;
}

.emotion-13 .amp-cms--subhead-3 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1;
  letter-spacing: 0.5px;
  font-weight: 350;
}

.emotion-14 {
  padding-top: 8px;
}

.emotion-14 .amp-cms--p {
  padding: 0;
  margin: 0;
  line-height: 0;
}

.emotion-14 .amp-cms--p>span {
  display: inline;
  white-space: break-spaces;
}

.emotion-14 .amp-cms--p a {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  position: relative;
  z-index: 2;
  pointer-events: auto;
}

.emotion-14 .amp-cms--p a:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-14 .amp-cms--legal-copy {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 10px;
  line-height: 1.8;
  letter-spacing: 0.5px;
  font-weight: 350;
}

.emotion-14 sup {
  vertical-align: top;
  display: inline-block;
  margin-top: -0.25ex;
}

.emotion-14 sub {
  vertical-align: bottom;
  display: inline-block;
  margin-bottom: -0.25ex;
}

.emotion-14 .amp-cms--f-0 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 11px;
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-14 .amp-cms--f-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 11px;
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-14 .amp-cms--f-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 11px;
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-14 .amp-cms--fn-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 11px;
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-14 .amp-cms--body-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.5;
  letter-spacing: 0.5px;
  font-weight: 350;
}

.emotion-14 .amp-cms--body-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 16px;
  line-height: 1.625;
  letter-spacing: 0.5px;
  font-weight: 350;
}

.emotion-14 .amp-cms--body-3 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 14px;
  line-height: 1.5714285714285714;
  letter-spacing: 0.5px;
  font-weight: 350;
}

.emotion-14 .amp-cms--body-4 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.6666666666666667;
  letter-spacing: 0.5px;
  font-weight: 350;
}

.emotion-14 .amp-cms--body-5 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 10px;
  line-height: 1.8;
  letter-spacing: 0.5px;
  font-weight: 350;
}

.emotion-14 .amp-cms--eyebrow-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 14px;
  line-height: 1;
  letter-spacing: 1.5px;
  font-weight: 600;
}

.emotion-14 .amp-cms--eyebrow-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1;
  letter-spacing: 1.5px;
  font-weight: 600;
}

.emotion-14 .amp-cms--eyebrow-3 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 10px;
  line-height: 1;
  letter-spacing: 1.5px;
  font-weight: 600;
}

.emotion-14 .amp-cms--headline-1 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 48px;
  line-height: 1;
  letter-spacing: 0.5px;
  font-weight: 300;
}

.emotion-14 .amp-cms--headline-2 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 40px;
  line-height: 1;
  letter-spacing: 0.5px;
  font-weight: 300;
}

.emotion-14 .amp-cms--headline-3 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 32px;
  line-height: 1.0625;
  letter-spacing: 0.5px;
  font-weight: 300;
}

.emotion-14 .amp-cms--headline-4 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 28px;
  line-height: 1.0714285714285714;
  letter-spacing: 0.5px;
  font-weight: 300;
}

.emotion-14 .amp-cms--headline-5 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 24px;
  line-height: 1.0833333333333333;
  letter-spacing: 0.5px;
  font-weight: 300;
}

.emotion-14 .amp-cms--headline-6 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 18px;
  line-height: 1.1111111111111112;
  letter-spacing: 0.5px;
  font-weight: 300;
}

.emotion-14 .amp-cms--headline-7 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 14px;
  line-height: 1.1428571428571428;
  letter-spacing: 0.5px;
  font-weight: 400;
}

.emotion-14 .amp-cms--headlineAlt-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 32px;
  line-height: 1.0625;
  letter-spacing: 0.5px;
  font-weight: 400;
}

.emotion-14 .amp-cms--headlineAlt-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 24px;
  line-height: 1.0833333333333333;
  letter-spacing: 0.5px;
  font-weight: 400;
}

.emotion-14 .amp-cms--headlineAlt-3 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 20px;
  line-height: 1.1;
  letter-spacing: 0.5px;
  font-weight: 400;
}

.emotion-14 .amp-cms--headlineAlt-4 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 60px;
  line-height: 1;
  letter-spacing: 0.5px;
  font-weight: 300;
}

.emotion-14 .amp-cms--headlineAlt-5 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 14px;
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-14 .amp-cms--headlineAlt-6 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 14px;
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-14 .amp-cms--headlineAlt-7 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 14px;
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-14 .amp-cms--promo-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 8px;
  line-height: 1.5;
  letter-spacing: 0.5px;
  font-weight: 400;
}

.emotion-14 .amp-cms--promo-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 40px;
  line-height: 1;
  letter-spacing: 0px;
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-14 .amp-cms--promoAlt-1 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 14px;
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 600;
}

.emotion-14 .amp-cms--subhead-1 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 14px;
  line-height: 1.2857142857142858;
  letter-spacing: 0.5px;
  font-weight: 300;
}

.emotion-14 .amp-cms--subhead-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 16px;
  line-height: 1;
  letter-spacing: 0.5px;
  font-weight: 350;
}

.emotion-14 .amp-cms--subhead-3 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1;
  letter-spacing: 0.5px;
  font-weight: 350;
}

.emotion-15 {
  background-color: transparent;
  box-shadow: none;
  box-sizing: border-box;
  border: 0;
  border-bottom: 1px solid transparent;
  -webkit-transition: border-bottom-color 0.2s ease;
  transition: border-bottom-color 0.2s ease;
}

.emotion-97.slick-next.slick-arrow {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: var(--size);
  height: var(--size);
  z-index: 2;
  --margin: 10px;
  --size: 40px;
  margin: var(--margin);
  --cardsHeight: 529.07px;
  top: calc(var(--cardsHeight) / 2 - var(--margin));
}

.emotion-97.slick-next.slick-arrow.slick-disabled {
  display: none;
}

.emotion-97.slick-next.slick-arrow>span {
  margin: auto;
  height: auto;
  width: auto;
  min-height: auto;
  min-width: auto;
}

.emotion-97.slick-next.slick-arrow svg {
  height: var(--size);
  width: var(--size);
}

.emotion-98 {
  display: inline-block;
  height: 2rem;
  width: 2rem;
  min-height: 2rem;
  min-width: 2rem;
  -webkit-transform: rotate(90deg);
  -moz-transform: rotate(90deg);
  -ms-transform: rotate(90deg);
  transform: rotate(90deg);
}

.emotion-98 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

<div>
  <div
    style="font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;"
  >
    <div
      class="emotion-0"
    >
      <article
        class="emotion-1"
      >
        <div
          class="emotion-2"
        >
          <div>
            <p
              class="amp-cms--p"
              style="text-align:center;"
            >
              <span
                class="amp-cms--headline-5"
              >
                Lorem ipsum dolor
              </span>
              <span
                class="amp-cms--body-1"
              >
                 sit amet consectetur adipiscing elit sed do eiusmod.
              </span>
            </p>
          </div>
        </div>
        <div
          class="emotion-3"
        >
          <div
            class="emotion-4"
          >
            <div
              class="emotion-5"
            >
              <div
                class="emotion-6"
              >
                <div
                  class="slick-slider slick-initialized"
                  dir="ltr"
                >
                  <div
                    class="slick-list"
                  >
                    <div
                      class="slick-track"
                      style="opacity: 1; transform: translate3d(0px, 0px, 0px);"
                    >
                      <div
                        aria-hidden="false"
                        class="slick-slide slick-active slick-current"
                        data-index="0"
                        style="outline: none; width: 0px;"
                        tabindex="-1"
                      >
                        <div>
                          <a
                            class="category-card emotion-7"
                            href="https://www.google.com"
                            target="_self"
                          >
                            <div
                              aria-live="polite"
                              class="emotion-8"
                            >
                              <div
                                aria-hidden="false"
                                class="emotion-9"
                              >
                                <img
                                  alt="Woman"
                                  class="emotion-10"
                                  src=""
                                />
                              </div>
                            </div>
                            <div
                              class="emotion-11"
                            >
                              <div
                                class="emotion-12"
                              >
                                <div
                                  class="emotion-13"
                                >
                                  <div>
                                    <p
                                      class="amp-cms--p"
                                      style="text-align:center;"
                                    >
                                      <span
                                        class="amp-cms--eyebrow-1"
                                        style="font-weight:700"
                                      >
                                        Straight
                                      </span>
                                    </p>
                                  </div>
                                </div>
                                <div
                                  class="emotion-14"
                                >
                                  <div>
                                    <p
                                      class="amp-cms--p"
                                      style="text-align:center;"
                                    >
                                      <span
                                        class="amp-cms--body-2"
                                      >
                                        sit amet consectetur adipiscing elit eiusmod.
                                      </span>
                                    </p>
                                  </div>
                                </div>
                              </div>
                              <div
                                class="emotion-15"
                                data-testid="category-card-bottom-border"
                              />
                            </div>
                          </a>
                        </div>
                      </div>
                      <div
                        aria-hidden="false"
                        class="slick-slide slick-active"
                        data-index="1"
                        style="outline: none; width: 0px;"
                        tabindex="-1"
                      >
                        <div>
                          <a
                            class="category-card emotion-7"
                            href="https://www.google.com"
                            target="_self"
                          >
                            <div
                              aria-live="polite"
                              class="emotion-8"
                            >
                              <div
                                aria-hidden="false"
                                class="emotion-9"
                              >
                                <img
                                  alt="Man"
                                  class="emotion-10"
                                  src=""
                                />
                              </div>
                            </div>
                            <div
                              class="emotion-11"
                            >
                              <div
                                class="emotion-12"
                              >
                                <div
                                  class="emotion-13"
                                >
                                  <div>
                                    <p
                                      class="amp-cms--p"
                                      style="text-align:center;"
                                    >
                                      <span
                                        class="amp-cms--eyebrow-1"
                                        style="font-weight:700"
                                      >
                                        Skinny
                                      </span>
                                    </p>
                                  </div>
                                </div>
                                <div
                                  class="emotion-14"
                                >
                                  <div>
                                    <p
                                      class="amp-cms--p"
                                      style="text-align:center;"
                                    >
                                      <span
                                        class="amp-cms--body-2"
                                      >
                                        sit amet consectetur adipiscing elit eiusmod.
                                      </span>
                                    </p>
                                  </div>
                                </div>
                              </div>
                              <div
                                class="emotion-15"
                                data-testid="category-card-bottom-border"
                              />
                            </div>
                          </a>
                        </div>
                      </div>
                      <div
                        aria-hidden="false"
                        class="slick-slide slick-active"
                        data-index="2"
                        style="outline: none; width: 0px;"
                        tabindex="-1"
                      >
                        <div>
                          <a
                            class="category-card emotion-7"
                            href="https://www.google.com"
                            target="_self"
                          >
                            <div
                              aria-live="polite"
                              class="emotion-8"
                            >
                              <div
                                aria-hidden="false"
                                class="emotion-9"
                              >
                                <img
                                  alt="Jeans"
                                  class="emotion-10"
                                  src=""
                                />
                              </div>
                            </div>
                            <div
                              class="emotion-11"
                            >
                              <div
                                class="emotion-12"
                              >
                                <div
                                  class="emotion-13"
                                >
                                  <div>
                                    <p
                                      class="amp-cms--p"
                                      style="text-align:center;"
                                    >
                                      <span
                                        class="amp-cms--eyebrow-1"
                                        style="font-weight:700"
                                      >
                                        Super-skinny
                                      </span>
                                    </p>
                                  </div>
                                </div>
                                <div
                                  class="emotion-14"
                                >
                                  <div>
                                    <p
                                      class="amp-cms--p"
                                      style="text-align:center;"
                                    >
                                      <span
                                        class="amp-cms--body-2"
                                      >
                                        sit amet consectetur adipiscing elit eiusmod.
                                      </span>
                                    </p>
                                  </div>
                                </div>
                              </div>
                              <div
                                class="emotion-15"
                                data-testid="category-card-bottom-border"
                              />
                            </div>
                          </a>
                        </div>
                      </div>
                      <div
                        aria-hidden="true"
                        class="slick-slide"
                        data-index="3"
                        style="outline: none; width: 0px;"
                        tabindex="-1"
                      >
                        <div>
                          <a
                            class="category-card emotion-7"
                            href="https://www.gap.com"
                            target="_self"
                          >
                            <div
                              aria-live="polite"
                              class="emotion-8"
                            >
                              <div
                                aria-hidden="false"
                                class="emotion-9"
                              >
                                <img
                                  alt="Another Man"
                                  class="emotion-10"
                                  src=""
                                />
                              </div>
                            </div>
                            <div
                              class="emotion-11"
                            >
                              <div
                                class="emotion-12"
                              >
                                <div
                                  class="emotion-13"
                                >
                                  <div>
                                    <p
                                      class="amp-cms--p"
                                      style="text-align:center;"
                                    >
                                      <span
                                        class="amp-cms--eyebrow-1"
                                        style="font-weight:700;text-decoration:underline"
                                      >
                                        Hover Headline
                                      </span>
                                    </p>
                                  </div>
                                </div>
                                <div
                                  class="emotion-14"
                                >
                                  <div>
                                    <p
                                      class="amp-cms--p"
                                      style="text-align:center;"
                                    >
                                      <span
                                        class="amp-cms--body-2"
                                      >
                                        sit amet consectetur adipiscing elit eiusmod.
                                      </span>
                                    </p>
                                  </div>
                                </div>
                              </div>
                              <div
                                class="emotion-15"
                                data-testid="category-card-bottom-border"
                              />
                            </div>
                          </a>
                        </div>
                      </div>
                      <div
                        aria-hidden="true"
                        class="slick-slide"
                        data-index="4"
                        style="outline: none; width: 0px;"
                        tabindex="-1"
                      >
                        <div>
                          <a
                            class="category-card emotion-7"
                            href="https://www.gap.com"
                            target="_self"
                          >
                            <div
                              aria-live="polite"
                              class="emotion-8"
                            >
                              <div
                                aria-hidden="false"
                                class="emotion-9"
                              >
                                <img
                                  alt="Posing"
                                  class="emotion-10"
                                  src=""
                                />
                              </div>
                            </div>
                            <div
                              class="emotion-11"
                            >
                              <div
                                class="emotion-12"
                              >
                                <div
                                  class="emotion-13"
                                >
                                  <div>
                                    <p
                                      class="amp-cms--p"
                                      style="text-align:center;"
                                    >
                                      <span
                                        class="amp-cms--eyebrow-1"
                                        style="font-weight:700;text-decoration:underline"
                                      >
                                        Selected Headline
                                      </span>
                                    </p>
                                  </div>
                                </div>
                                <div
                                  class="emotion-14"
                                >
                                  <div>
                                    <p
                                      class="amp-cms--p"
                                      style="text-align:center;"
                                    >
                                      <span
                                        class="amp-cms--body-2"
                                      >
                                        sit amet consectetur adipiscing elit eiusmod.
                                      </span>
                                    </p>
                                  </div>
                                </div>
                              </div>
                              <div
                                class="emotion-15"
                                data-testid="category-card-bottom-border"
                              />
                            </div>
                          </a>
                        </div>
                      </div>
                      <div
                        aria-hidden="true"
                        class="slick-slide"
                        data-index="5"
                        style="outline: none; width: 0px;"
                        tabindex="-1"
                      >
                        <div>
                          <a
                            class="category-card emotion-7"
                            href="https://www.google.com"
                            target="_self"
                          >
                            <div
                              aria-live="polite"
                              class="emotion-8"
                            >
                              <div
                                aria-hidden="false"
                                class="emotion-9"
                              >
                                <img
                                  alt="Yoga"
                                  class="emotion-10"
                                  src=""
                                />
                              </div>
                            </div>
                            <div
                              class="emotion-11"
                            >
                              <div
                                class="emotion-12"
                              >
                                <div
                                  class="emotion-13"
                                >
                                  <div>
                                    <p
                                      class="amp-cms--p"
                                      style="text-align:center;"
                                    >
                                      <span
                                        class="amp-cms--eyebrow-1"
                                        style="font-weight:700"
                                      >
                                        Wide-Leg
                                      </span>
                                    </p>
                                  </div>
                                </div>
                                <div
                                  class="emotion-14"
                                >
                                  <div>
                                    <p
                                      class="amp-cms--p"
                                      style="text-align:center;"
                                    >
                                      <span
                                        class="amp-cms--body-2"
                                      >
                                        sit amet consectetur adipiscing elit eiusmod.
                                      </span>
                                    </p>
                                  </div>
                                </div>
                              </div>
                              <div
                                class="emotion-15"
                                data-testid="category-card-bottom-border"
                              />
                            </div>
                          </a>
                        </div>
                      </div>
                      <div
                        aria-hidden="true"
                        class="slick-slide"
                        data-index="6"
                        style="outline: none; width: 0px;"
                        tabindex="-1"
                      >
                        <div>
                          <a
                            class="category-card emotion-7"
                            href="htttps://www.gap.com"
                            target="_self"
                          >
                            <div
                              aria-live="polite"
                              class="emotion-8"
                            >
                              <div
                                aria-hidden="false"
                                class="emotion-9"
                              >
                                <img
                                  alt="People"
                                  class="emotion-10"
                                  src=""
                                />
                              </div>
                            </div>
                            <div
                              class="emotion-11"
                            >
                              <div
                                class="emotion-12"
                              >
                                <div
                                  class="emotion-13"
                                >
                                  <div>
                                    <p
                                      class="amp-cms--p"
                                      style="text-align:center;"
                                    >
                                      <span
                                        class="amp-cms--eyebrow-1"
                                        style="font-weight:700"
                                      >
                                        Skinny-Leg
                                      </span>
                                    </p>
                                  </div>
                                </div>
                                <div
                                  class="emotion-14"
                                >
                                  <div>
                                    <p
                                      class="amp-cms--p"
                                      style="text-align:center;"
                                    >
                                      <span
                                        class="amp-cms--body-2"
                                      >
                                        sit amet consectetur adipiscing elit eiusmod.
                                      </span>
                                    </p>
                                  </div>
                                </div>
                              </div>
                              <div
                                class="emotion-15"
                                data-testid="category-card-bottom-border"
                              />
                            </div>
                          </a>
                        </div>
                      </div>
                      <div
                        aria-hidden="true"
                        class="slick-slide"
                        data-index="7"
                        style="outline: none; width: 0px;"
                        tabindex="-1"
                      >
                        <div>
                          <a
                            class="category-card emotion-7"
                            href="https://www.google.com"
                            target="_self"
                          >
                            <div
                              aria-live="polite"
                              class="emotion-8"
                            >
                              <div
                                aria-hidden="false"
                                class="emotion-9"
                              >
                                <img
                                  alt="Woman"
                                  class="emotion-10"
                                  src=""
                                />
                              </div>
                            </div>
                            <div
                              class="emotion-11"
                            >
                              <div
                                class="emotion-12"
                              >
                                <div
                                  class="emotion-13"
                                >
                                  <div>
                                    <p
                                      class="amp-cms--p"
                                      style="text-align:center;"
                                    >
                                      <span
                                        class="amp-cms--eyebrow-1"
                                        style="font-weight:700"
                                      >
                                        Hover
                                      </span>
                                    </p>
                                  </div>
                                </div>
                                <div
                                  class="emotion-14"
                                >
                                  <div>
                                    <p
                                      class="amp-cms--p"
                                      style="text-align:center;"
                                    >
                                      <span
                                        class="amp-cms--body-2"
                                      >
                                        sit amet consectetur adipiscing elit sed do.
                                      </span>
                                    </p>
                                  </div>
                                </div>
                              </div>
                              <div
                                class="emotion-15"
                                data-testid="category-card-bottom-border"
                              />
                            </div>
                          </a>
                        </div>
                      </div>
                      <div
                        aria-hidden="true"
                        class="slick-slide"
                        data-index="8"
                        style="outline: none; width: 0px;"
                        tabindex="-1"
                      >
                        <div>
                          <a
                            class="category-card emotion-7"
                            href="https://www.google.com"
                            target="_self"
                          >
                            <div
                              aria-live="polite"
                              class="emotion-8"
                            >
                              <div
                                aria-hidden="false"
                                class="emotion-9"
                              >
                                <img
                                  alt="Man"
                                  class="emotion-10"
                                  src=""
                                />
                              </div>
                            </div>
                            <div
                              class="emotion-11"
                            >
                              <div
                                class="emotion-12"
                              >
                                <div
                                  class="emotion-13"
                                >
                                  <div>
                                    <p
                                      class="amp-cms--p"
                                      style="text-align:center;"
                                    >
                                      <span
                                        class="amp-cms--eyebrow-1"
                                        style="font-weight:700"
                                      >
                                        Selected
                                      </span>
                                    </p>
                                  </div>
                                </div>
                                <div
                                  class="emotion-14"
                                >
                                  <div>
                                    <p
                                      class="amp-cms--p"
                                      style="text-align:center;"
                                    >
                                      <span
                                        class="amp-cms--body-2"
                                      >
                                        sit amet consectetur adipiscing elit sed do.
                                      </span>
                                    </p>
                                  </div>
                                </div>
                              </div>
                              <div
                                class="emotion-15"
                                data-testid="category-card-bottom-border"
                              />
                            </div>
                          </a>
                        </div>
                      </div>
                      <div
                        aria-hidden="true"
                        class="slick-slide"
                        data-index="9"
                        style="outline: none; width: 0px;"
                        tabindex="-1"
                      >
                        <div>
                          <a
                            class="category-card emotion-7"
                            href="https://www,gap.com"
                            target="_self"
                          >
                            <div
                              aria-live="polite"
                              class="emotion-8"
                            >
                              <div
                                aria-hidden="false"
                                class="emotion-9"
                              >
                                <img
                                  alt="Another Man"
                                  class="emotion-10"
                                  src=""
                                />
                              </div>
                            </div>
                            <div
                              class="emotion-11"
                            >
                              <div
                                class="emotion-12"
                              >
                                <div
                                  class="emotion-13"
                                >
                                  <div>
                                    <p
                                      class="amp-cms--p"
                                      style="text-align:center;"
                                    >
                                      <span
                                        class="amp-cms--eyebrow-1"
                                        style="font-weight:700"
                                      >
                                        Card 10
                                      </span>
                                    </p>
                                  </div>
                                </div>
                                <div
                                  class="emotion-14"
                                >
                                  <div>
                                    <p
                                      class="amp-cms--p"
                                      style="text-align:center;"
                                    >
                                      <span
                                        class="amp-cms--body-2"
                                      >
                                        sit amet consectetur adipiscing elit sed do.
                                      </span>
                                    </p>
                                  </div>
                                </div>
                              </div>
                              <div
                                class="emotion-15"
                                data-testid="category-card-bottom-border"
                              />
                            </div>
                          </a>
                        </div>
                      </div>
                    </div>
                  </div>
                  <button
                    aria-label="Next"
                    class="slick-arrow slick-next emotion-97"
                  >
                    <span
                      aria-hidden="true"
                      class="emotion-98"
                    >
                      <svg
                        fill="none"
                        viewBox="0 0 41 40"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <g
                          opacity="0.8"
                        >
                          <rect
                            fill="#FFFFFF"
                            height="40"
                            rx="20"
                            width="40"
                            x="0.5"
                          />
                          <path
                            d="M14.5 20.35C14.3067 20.35 14.15 20.1933 14.15 20C14.15 19.8067 14.3067 19.65 14.5 19.65V20.35ZM26.7475 19.7525C26.8842 19.8892 26.8842 20.1108 26.7475 20.2475L24.5201 22.4749C24.3834 22.6116 24.1618 22.6116 24.0251 22.4749C23.8884 22.3382 23.8884 22.1166 24.0251 21.9799L26.005 20L24.0251 18.0201C23.8884 17.8834 23.8884 17.6618 24.0251 17.5251C24.1618 17.3884 24.3834 17.3884 24.5201 17.5251L26.7475 19.7525ZM14.5 19.65L26.5 19.65V20.35L14.5 20.35V19.65Z"
                            fill="#000000"
                          />
                        </g>
                      </svg>
                    </span>
                  </button>
                  <ul
                    class="slick-dots"
                    style="display: block;"
                  >
                    <li
                      class="slick-active"
                    >
                      <button>
                        1
                      </button>
                    </li>
                    <li
                      class=""
                    >
                      <button>
                        2
                      </button>
                    </li>
                    <li
                      class=""
                    >
                      <button>
                        3
                      </button>
                    </li>
                    <li
                      class=""
                    >
                      <button>
                        4
                      </button>
                    </li>
                    <li
                      class=""
                    >
                      <button>
                        5
                      </button>
                    </li>
                    <li
                      class=""
                    >
                      <button>
                        6
                      </button>
                    </li>
                    <li
                      class=""
                    >
                      <button>
                        7
                      </button>
                    </li>
                    <li
                      class=""
                    >
                      <button>
                        8
                      </button>
                    </li>
                    <li
                      class=""
                    >
                      <button>
                        9
                      </button>
                    </li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </div>
      </article>
    </div>
  </div>
</div>
`;

exports[`VisualNavigationCarouselBR on mobile should match snapshots for desktop with introcard hideonmobile 1`] = `
.emotion-0 {
  margin-left: auto;
  margin-right: auto;
  max-width: 1280px;
}

.emotion-1 {
  min-height: 235px;
}

.emotion-2 {
  padding-bottom: 24px;
  padding-inline: 10px;
  padding-top: 40px;
}

.emotion-2 .amp-cms--p {
  padding: 0;
  margin: 0;
  line-height: 0;
}

.emotion-2 .amp-cms--p>span {
  display: inline;
  white-space: break-spaces;
}

.emotion-2 .amp-cms--p a {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  position: relative;
  z-index: 2;
  pointer-events: auto;
}

.emotion-2 .amp-cms--p a:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-2 .amp-cms--legal-copy {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(8px, min(2.666666666666667vw, 10px));
  line-height: 1.8;
  letter-spacing: min(0.13333333333333333vw, 0.5px);
  font-weight: 350;
}

.emotion-2 sup {
  vertical-align: top;
  display: inline-block;
  margin-top: -0.25ex;
}

.emotion-2 sub {
  vertical-align: bottom;
  display: inline-block;
  margin-bottom: -0.25ex;
}

.emotion-2 .amp-cms--f-0 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(2.933333333333333vw, 11px);
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-2 .amp-cms--f-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(2.933333333333333vw, 11px);
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-2 .amp-cms--f-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(2.933333333333333vw, 11px);
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-2 .amp-cms--fn-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(2.933333333333333vw, 11px);
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-2 .amp-cms--body-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(3.2vw, 12px));
  line-height: 1.5;
  letter-spacing: min(0.13333333333333333vw, 0.5px);
  font-weight: 350;
}

.emotion-2 .amp-cms--body-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(4.266666666666667vw, 16px));
  line-height: 1.625;
  letter-spacing: min(0.13333333333333333vw, 0.5px);
  font-weight: 350;
}

.emotion-2 .amp-cms--body-3 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(3.733333333333334vw, 14px));
  line-height: 1.5714285714285714;
  letter-spacing: min(0.13333333333333333vw, 0.5px);
  font-weight: 350;
}

.emotion-2 .amp-cms--body-4 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(3.2vw, 12px));
  line-height: 1.6666666666666667;
  letter-spacing: min(0.13333333333333333vw, 0.5px);
  font-weight: 350;
}

.emotion-2 .amp-cms--body-5 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(8px, min(2.666666666666667vw, 10px));
  line-height: 1.8;
  letter-spacing: min(0.13333333333333333vw, 0.5px);
  font-weight: 350;
}

.emotion-2 .amp-cms--eyebrow-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(3.733333333333334vw, 14px));
  line-height: 1;
  letter-spacing: min(0.4vw, 1.5px);
  font-weight: 600;
}

.emotion-2 .amp-cms--eyebrow-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(11px, min(3.2vw, 12px));
  line-height: 1;
  letter-spacing: min(0.4vw, 1.5px);
  font-weight: 600;
}

.emotion-2 .amp-cms--eyebrow-3 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(2.666666666666667vw, 10px));
  line-height: 1;
  letter-spacing: min(0.4vw, 1.5px);
  font-weight: 600;
}

.emotion-2 .amp-cms--headline-1 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(12.8vw, 48px));
  line-height: 1;
  letter-spacing: min(0.13333333333333333vw, 0.5px);
  font-weight: 300;
}

.emotion-2 .amp-cms--headline-2 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(10.666666666666668vw, 40px));
  line-height: 1;
  letter-spacing: min(0.13333333333333333vw, 0.5px);
  font-weight: 300;
}

.emotion-2 .amp-cms--headline-3 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(8.533333333333333vw, 32px));
  line-height: 1.0625;
  letter-spacing: min(0.13333333333333333vw, 0.5px);
  font-weight: 300;
}

.emotion-2 .amp-cms--headline-4 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(7.466666666666668vw, 28px));
  line-height: 1.0714285714285714;
  letter-spacing: min(0.13333333333333333vw, 0.5px);
  font-weight: 300;
}

.emotion-2 .amp-cms--headline-5 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(6.4vw, 24px));
  line-height: 1.0833333333333333;
  letter-spacing: min(0.13333333333333333vw, 0.5px);
  font-weight: 300;
}

.emotion-2 .amp-cms--headline-6 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(4.8vw, 18px));
  line-height: 1.1111111111111112;
  letter-spacing: min(0.13333333333333333vw, 0.5px);
  font-weight: 300;
}

.emotion-2 .amp-cms--headline-7 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(3.733333333333334vw, 14px));
  line-height: 1.1428571428571428;
  letter-spacing: min(0.13333333333333333vw, 0.5px);
  font-weight: 400;
}

.emotion-2 .amp-cms--headlineAlt-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(8.533333333333333vw, 32px));
  line-height: 1.0625;
  letter-spacing: min(0.13333333333333333vw, 0.5px);
  font-weight: 400;
}

.emotion-2 .amp-cms--headlineAlt-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(6.4vw, 24px));
  line-height: 1.0833333333333333;
  letter-spacing: min(0.13333333333333333vw, 0.5px);
  font-weight: 400;
}

.emotion-2 .amp-cms--headlineAlt-3 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(5.333333333333334vw, 20px));
  line-height: 1.1;
  letter-spacing: min(0.13333333333333333vw, 0.5px);
  font-weight: 400;
}

.emotion-2 .amp-cms--headlineAlt-4 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(16vw, 60px));
  line-height: 1;
  letter-spacing: min(0.13333333333333333vw, 0.5px);
  font-weight: 300;
}

.emotion-2 .amp-cms--headlineAlt-5 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(3.733333333333334vw, 14px);
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-2 .amp-cms--headlineAlt-6 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(3.733333333333334vw, 14px);
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-2 .amp-cms--headlineAlt-7 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(3.733333333333334vw, 14px);
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-2 .amp-cms--promo-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(8px, min(2.1333333333333333vw, 8px));
  line-height: 1.5;
  letter-spacing: min(0.13333333333333333vw, 0.5px);
  font-weight: 400;
}

.emotion-2 .amp-cms--promo-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(13px, min(10.666666666666668vw, 40px));
  line-height: 1;
  letter-spacing: min(0vw, 0px);
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-2 .amp-cms--promoAlt-1 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(3.733333333333334vw, 14px);
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 600;
}

.emotion-2 .amp-cms--subhead-1 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(3.733333333333334vw, 14px));
  line-height: 1.2857142857142858;
  letter-spacing: min(0.13333333333333333vw, 0.5px);
  font-weight: 300;
}

.emotion-2 .amp-cms--subhead-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(4.266666666666667vw, 16px));
  line-height: 1;
  letter-spacing: min(0.13333333333333333vw, 0.5px);
  font-weight: 350;
}

.emotion-2 .amp-cms--subhead-3 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(3.2vw, 12px));
  line-height: 1;
  letter-spacing: min(0.13333333333333333vw, 0.5px);
  font-weight: 350;
}

.emotion-3 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  border-bottom: 1px solid transparent;
}

.emotion-4 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  margin-bottom: 0;
}

.emotion-5 {
  overflow: hidden;
}

.emotion-5 div.slick-track {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  gap: 0;
}

.emotion-5 div.slick-track:before {
  display: none;
}

.emotion-5 div.slick-track:after {
  display: none;
}

.emotion-5 div.slick-slide {
  height: auto;
}

.emotion-5 div.slick-slide>div {
  height: 100%;
}

.emotion-6 {
  position: relative;
}

.emotion-6 .slick-slider {
  position: relative;
  display: block;
  box-sizing: border-box;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  -webkit-touch-callout: none;
  touch-action: pan-y;
  -webkit-tap-highlight-color: transparent;
  scroll-behavior: smooth;
}

.emotion-6 .slick-slider .slick-track,
.emotion-6 .slick-slider .slick-list {
  -webkit-transform: translate3d(0, 0, 0);
  -moz-transform: translate3d(0, 0, 0);
  -ms-transform: translate3d(0, 0, 0);
  transform: translate3d(0, 0, 0);
}

.emotion-6 .slick-list {
  position: relative;
  display: block;
  overflow-x: hidden;
  overflow-y: hidden;
  margin: 0;
  padding: 0;
}

.emotion-6 .slick-list:focus {
  outline: none;
}

.emotion-6 .slick-list.dragging {
  cursor: pointer;
  cursor: hand;
}

.emotion-6 .slick-track {
  position: relative;
  top: 0;
  left: 0;
  display: block;
  margin-left: auto;
  margin-right: auto;
}

.emotion-6 .slick-track:before,
.emotion-6 .slick-track:after {
  display: table;
  content: "";
}

.emotion-6 .slick-track:after {
  clear: both;
}

.emotion-6 .slick-slide {
  display: none;
  float: left;
  height: 100%;
  min-height: 1px;
}

.emotion-6 .slick-slide img {
  display: block;
}

.emotion-6 .slick-slide.slick-loading img {
  display: none;
}

.emotion-6 .slick-slide.dragging img {
  pointer-events: none;
}

.emotion-6 [dir="rtl"] .slick-slide {
  float: right;
}

.emotion-6 .slick-initialized .slick-slide,
.emotion-6 .slick-vertical .slick-slide {
  display: block;
}

.emotion-6 .slick-vertical .slick-slide {
  border: 1px solid transparent;
  height: auto;
}

.emotion-6 .slick-loading .slick-track,
.emotion-6 .slick-loading .slick-slide {
  visibility: hidden;
}

.emotion-6 .slick-loading .slick-list {
  background: #fff url("./ajax-loader.gif") center center no-repeat;
}

.emotion-6 .slick-arrow.slick-hidden {
  display: none;
}

.emotion-6 .slick-prev,
.emotion-6 .slick-next {
  font-size: 0;
  line-height: 0;
  position: absolute;
  top: 50%;
  display: block;
  padding: 0;
  cursor: pointer;
  color: transparent;
  border: none;
  outline: none;
  background: transparent;
}

.emotion-6 .slick-prev:hover,
.emotion-6 .slick-next:hover,
.emotion-6 .slick-prev:focus,
.emotion-6 .slick-next:focus {
  color: transparent;
  outline: none;
  background: transparent;
  opacity: 1;
}

.emotion-6 .slick-prev.slick-disabled,
.emotion-6 .slick-next.slick-disabled {
  opacity: 0.25;
}

.emotion-6 .slick-prev {
  left: -0px;
  -webkit-transform: translate(0, -50%) rotate(90deg);
  -moz-transform: translate(0, -50%) rotate(90deg);
  -ms-transform: translate(0, -50%) rotate(90deg);
  transform: translate(0, -50%) rotate(90deg);
}

.emotion-6 .slick-prev span {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-6 [dir="rtl"] .slick-prev {
  right: -0px;
  left: auto;
}

.emotion-6 .slick-next {
  right: -0px;
  -webkit-transform: translate(0, -50%) rotate(-90deg);
  -moz-transform: translate(0, -50%) rotate(-90deg);
  -ms-transform: translate(0, -50%) rotate(-90deg);
  transform: translate(0, -50%) rotate(-90deg);
}

.emotion-6 .slick-next span {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-6 [dir="rtl"] .slick-next {
  right: auto;
  left: -0px;
}

.emotion-6 .slick-dotted.slick-slider {
  margin-bottom: 30px;
}

.emotion-6 .slick-dots {
  position: absolute;
  bottom: -25px;
  display: block;
  width: 100%;
  padding: 0;
  margin: 0;
  list-style: none;
  text-align: center;
}

.emotion-6 .slick-dots li {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: 20px;
  height: 20px;
  margin: 0 5px;
  padding: 0;
  cursor: pointer;
}

.emotion-6 .slick-dots li.slick-active button:before {
  opacity: 0.75;
  color: black;
}

.emotion-6 .slick-dots li button {
  font-size: 0;
  line-height: 0;
  display: block;
  padding: 5px;
  cursor: pointer;
  color: transparent;
  border: 0;
  outline: none;
  background: transparent;
}

.emotion-6 .slick-dots li button:hover,
.emotion-6 .slick-dots li button:focus {
  outline: none;
}

.emotion-6 .slick-dots li button:hover:before,
.emotion-6 .slick-dots li button:focus:before,
.emotion-6 .slick-dots li button:hover:before,
.emotion-6 .slick-dots li button:focus:before {
  opacity: 1;
}

.emotion-6 .slick-dots li button:before {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 5px;
  height: 5px;
  content: "";
  opacity: 0.25;
  background-color: black;
  border-radius: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-6 .slick-slider {
  padding-left: 24px;
}

.emotion-6 .slick-list {
  margin: 0 -2px;
}

.emotion-6 .slick-track {
  margin-left: 0!important;
  margin-right: 0!important;
}

.emotion-6 .slick-dots {
  position: unset;
  line-height: 0;
  width: calc(100% + 24px);
  margin-left: -24px;
  margin-top: -1px;
  border-top: 1px solid #E5E5E5;
}

.emotion-6 .slick-dots li {
  margin-top: 35px;
}

.emotion-6 .category-card {
  margin: 0 2px!important;
  overflow: hidden;
}

.emotion-7 {
  outline: none;
  position: relative;
  -webkit-flex: 1;
  -ms-flex: 1;
  flex: 1;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  height: 100%;
  max-width: 396.8px;
}

.emotion-8 {
  overflow: hidden;
  -webkit-transition: background-color .25s ease;
  transition: background-color .25s ease;
  background-color: transparent;
  min-width: 3px;
  position: relative;
}

.emotion-9 {
  box-sizing: border-box;
  aspect-ratio: 3/4;
  position: relative;
  -webkit-transition: -webkit-transform .25s ease,opacity .25s ease;
  transition: transform .25s ease,opacity .25s ease;
  -webkit-transform: none;
  -moz-transform: none;
  -ms-transform: none;
  transform: none;
  opacity: 1;
  width: 100%;
  z-index: 1;
}

.emotion-10 {
  width: 100%;
  aspect-ratio: 3/4;
  object-fit: cover;
}

.emotion-11 {
  background: transparent;
  position: relative;
  box-sizing: border-box;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-box-flex: 1;
  -webkit-flex-grow: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
  z-index: 1;
}

.emotion-12 {
  height: 100%;
  padding: 8px 10px;
  position: relative;
  background-color: white;
}

.emotion-13 .amp-cms--p {
  padding: 0;
  margin: 0;
  line-height: 0;
}

.emotion-13 .amp-cms--p>span {
  display: inline;
  white-space: break-spaces;
}

.emotion-13 .amp-cms--p a {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  position: relative;
  z-index: 2;
  pointer-events: auto;
}

.emotion-13 .amp-cms--p a:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-13 .amp-cms--legal-copy {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 10px;
  line-height: 1.8;
  letter-spacing: 0.5px;
  font-weight: 350;
}

.emotion-13 sup {
  vertical-align: top;
  display: inline-block;
  margin-top: -0.25ex;
}

.emotion-13 sub {
  vertical-align: bottom;
  display: inline-block;
  margin-bottom: -0.25ex;
}

.emotion-13 .amp-cms--f-0 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 11px;
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-13 .amp-cms--f-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 11px;
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-13 .amp-cms--f-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 11px;
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-13 .amp-cms--fn-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 11px;
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-13 .amp-cms--body-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.5;
  letter-spacing: 0.5px;
  font-weight: 350;
}

.emotion-13 .amp-cms--body-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 16px;
  line-height: 1.625;
  letter-spacing: 0.5px;
  font-weight: 350;
}

.emotion-13 .amp-cms--body-3 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 14px;
  line-height: 1.5714285714285714;
  letter-spacing: 0.5px;
  font-weight: 350;
}

.emotion-13 .amp-cms--body-4 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.6666666666666667;
  letter-spacing: 0.5px;
  font-weight: 350;
}

.emotion-13 .amp-cms--body-5 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 10px;
  line-height: 1.8;
  letter-spacing: 0.5px;
  font-weight: 350;
}

.emotion-13 .amp-cms--eyebrow-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 14px;
  line-height: 1;
  letter-spacing: 1.5px;
  font-weight: 600;
}

.emotion-13 .amp-cms--eyebrow-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1;
  letter-spacing: 1.5px;
  font-weight: 600;
}

.emotion-13 .amp-cms--eyebrow-3 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 10px;
  line-height: 1;
  letter-spacing: 1.5px;
  font-weight: 600;
}

.emotion-13 .amp-cms--headline-1 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 48px;
  line-height: 1;
  letter-spacing: 0.5px;
  font-weight: 300;
}

.emotion-13 .amp-cms--headline-2 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 40px;
  line-height: 1;
  letter-spacing: 0.5px;
  font-weight: 300;
}

.emotion-13 .amp-cms--headline-3 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 32px;
  line-height: 1.0625;
  letter-spacing: 0.5px;
  font-weight: 300;
}

.emotion-13 .amp-cms--headline-4 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 28px;
  line-height: 1.0714285714285714;
  letter-spacing: 0.5px;
  font-weight: 300;
}

.emotion-13 .amp-cms--headline-5 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 24px;
  line-height: 1.0833333333333333;
  letter-spacing: 0.5px;
  font-weight: 300;
}

.emotion-13 .amp-cms--headline-6 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 18px;
  line-height: 1.1111111111111112;
  letter-spacing: 0.5px;
  font-weight: 300;
}

.emotion-13 .amp-cms--headline-7 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 14px;
  line-height: 1.1428571428571428;
  letter-spacing: 0.5px;
  font-weight: 400;
}

.emotion-13 .amp-cms--headlineAlt-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 32px;
  line-height: 1.0625;
  letter-spacing: 0.5px;
  font-weight: 400;
}

.emotion-13 .amp-cms--headlineAlt-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 24px;
  line-height: 1.0833333333333333;
  letter-spacing: 0.5px;
  font-weight: 400;
}

.emotion-13 .amp-cms--headlineAlt-3 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 20px;
  line-height: 1.1;
  letter-spacing: 0.5px;
  font-weight: 400;
}

.emotion-13 .amp-cms--headlineAlt-4 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 60px;
  line-height: 1;
  letter-spacing: 0.5px;
  font-weight: 300;
}

.emotion-13 .amp-cms--headlineAlt-5 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 14px;
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-13 .amp-cms--headlineAlt-6 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 14px;
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-13 .amp-cms--headlineAlt-7 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 14px;
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-13 .amp-cms--promo-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 8px;
  line-height: 1.5;
  letter-spacing: 0.5px;
  font-weight: 400;
}

.emotion-13 .amp-cms--promo-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 40px;
  line-height: 1;
  letter-spacing: 0px;
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-13 .amp-cms--promoAlt-1 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 14px;
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 600;
}

.emotion-13 .amp-cms--subhead-1 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 14px;
  line-height: 1.2857142857142858;
  letter-spacing: 0.5px;
  font-weight: 300;
}

.emotion-13 .amp-cms--subhead-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 16px;
  line-height: 1;
  letter-spacing: 0.5px;
  font-weight: 350;
}

.emotion-13 .amp-cms--subhead-3 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1;
  letter-spacing: 0.5px;
  font-weight: 350;
}

.emotion-14 {
  padding-top: 8px;
}

.emotion-14 .amp-cms--p {
  padding: 0;
  margin: 0;
  line-height: 0;
}

.emotion-14 .amp-cms--p>span {
  display: inline;
  white-space: break-spaces;
}

.emotion-14 .amp-cms--p a {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  position: relative;
  z-index: 2;
  pointer-events: auto;
}

.emotion-14 .amp-cms--p a:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-14 .amp-cms--legal-copy {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 10px;
  line-height: 1.8;
  letter-spacing: 0.5px;
  font-weight: 350;
}

.emotion-14 sup {
  vertical-align: top;
  display: inline-block;
  margin-top: -0.25ex;
}

.emotion-14 sub {
  vertical-align: bottom;
  display: inline-block;
  margin-bottom: -0.25ex;
}

.emotion-14 .amp-cms--f-0 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 11px;
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-14 .amp-cms--f-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 11px;
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-14 .amp-cms--f-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 11px;
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-14 .amp-cms--fn-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 11px;
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-14 .amp-cms--body-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.5;
  letter-spacing: 0.5px;
  font-weight: 350;
}

.emotion-14 .amp-cms--body-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 16px;
  line-height: 1.625;
  letter-spacing: 0.5px;
  font-weight: 350;
}

.emotion-14 .amp-cms--body-3 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 14px;
  line-height: 1.5714285714285714;
  letter-spacing: 0.5px;
  font-weight: 350;
}

.emotion-14 .amp-cms--body-4 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.6666666666666667;
  letter-spacing: 0.5px;
  font-weight: 350;
}

.emotion-14 .amp-cms--body-5 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 10px;
  line-height: 1.8;
  letter-spacing: 0.5px;
  font-weight: 350;
}

.emotion-14 .amp-cms--eyebrow-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 14px;
  line-height: 1;
  letter-spacing: 1.5px;
  font-weight: 600;
}

.emotion-14 .amp-cms--eyebrow-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1;
  letter-spacing: 1.5px;
  font-weight: 600;
}

.emotion-14 .amp-cms--eyebrow-3 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 10px;
  line-height: 1;
  letter-spacing: 1.5px;
  font-weight: 600;
}

.emotion-14 .amp-cms--headline-1 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 48px;
  line-height: 1;
  letter-spacing: 0.5px;
  font-weight: 300;
}

.emotion-14 .amp-cms--headline-2 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 40px;
  line-height: 1;
  letter-spacing: 0.5px;
  font-weight: 300;
}

.emotion-14 .amp-cms--headline-3 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 32px;
  line-height: 1.0625;
  letter-spacing: 0.5px;
  font-weight: 300;
}

.emotion-14 .amp-cms--headline-4 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 28px;
  line-height: 1.0714285714285714;
  letter-spacing: 0.5px;
  font-weight: 300;
}

.emotion-14 .amp-cms--headline-5 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 24px;
  line-height: 1.0833333333333333;
  letter-spacing: 0.5px;
  font-weight: 300;
}

.emotion-14 .amp-cms--headline-6 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 18px;
  line-height: 1.1111111111111112;
  letter-spacing: 0.5px;
  font-weight: 300;
}

.emotion-14 .amp-cms--headline-7 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 14px;
  line-height: 1.1428571428571428;
  letter-spacing: 0.5px;
  font-weight: 400;
}

.emotion-14 .amp-cms--headlineAlt-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 32px;
  line-height: 1.0625;
  letter-spacing: 0.5px;
  font-weight: 400;
}

.emotion-14 .amp-cms--headlineAlt-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 24px;
  line-height: 1.0833333333333333;
  letter-spacing: 0.5px;
  font-weight: 400;
}

.emotion-14 .amp-cms--headlineAlt-3 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 20px;
  line-height: 1.1;
  letter-spacing: 0.5px;
  font-weight: 400;
}

.emotion-14 .amp-cms--headlineAlt-4 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 60px;
  line-height: 1;
  letter-spacing: 0.5px;
  font-weight: 300;
}

.emotion-14 .amp-cms--headlineAlt-5 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 14px;
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-14 .amp-cms--headlineAlt-6 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 14px;
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-14 .amp-cms--headlineAlt-7 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 14px;
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-14 .amp-cms--promo-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 8px;
  line-height: 1.5;
  letter-spacing: 0.5px;
  font-weight: 400;
}

.emotion-14 .amp-cms--promo-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 40px;
  line-height: 1;
  letter-spacing: 0px;
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-14 .amp-cms--promoAlt-1 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 14px;
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 600;
}

.emotion-14 .amp-cms--subhead-1 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 14px;
  line-height: 1.2857142857142858;
  letter-spacing: 0.5px;
  font-weight: 300;
}

.emotion-14 .amp-cms--subhead-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 16px;
  line-height: 1;
  letter-spacing: 0.5px;
  font-weight: 350;
}

.emotion-14 .amp-cms--subhead-3 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1;
  letter-spacing: 0.5px;
  font-weight: 350;
}

.emotion-15 {
  background-color: transparent;
  box-shadow: none;
  box-sizing: border-box;
  border: 0;
  border-bottom: 1px solid transparent;
  -webkit-transition: border-bottom-color 0.2s ease;
  transition: border-bottom-color 0.2s ease;
}

.emotion-97.slick-next.slick-arrow {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: var(--size);
  height: var(--size);
  z-index: 2;
  --margin: 10px;
  --size: 40px;
  margin: var(--margin);
  --cardsHeight: 529.07px;
  top: calc(var(--cardsHeight) / 2 - var(--margin));
}

.emotion-97.slick-next.slick-arrow.slick-disabled {
  display: none;
}

.emotion-97.slick-next.slick-arrow>span {
  margin: auto;
  height: auto;
  width: auto;
  min-height: auto;
  min-width: auto;
}

.emotion-97.slick-next.slick-arrow svg {
  height: var(--size);
  width: var(--size);
}

.emotion-98 {
  display: inline-block;
  height: 2rem;
  width: 2rem;
  min-height: 2rem;
  min-width: 2rem;
  -webkit-transform: rotate(90deg);
  -moz-transform: rotate(90deg);
  -ms-transform: rotate(90deg);
  transform: rotate(90deg);
}

.emotion-98 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

<div>
  <div
    style="font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;"
  >
    <div
      class="emotion-0"
    >
      <article
        class="emotion-1"
      >
        <div
          class="emotion-2"
        >
          <div>
            <p
              class="amp-cms--p"
              style="text-align:center;"
            >
              <span
                class="amp-cms--headline-5"
              >
                Lorem ipsum dolor
              </span>
              <span
                class="amp-cms--body-1"
              >
                 sit amet consectetur adipiscing elit sed do eiusmod.
              </span>
            </p>
          </div>
        </div>
        <div
          class="emotion-3"
        >
          <div
            class="emotion-4"
          >
            <div
              class="emotion-5"
            >
              <div
                class="emotion-6"
              >
                <div
                  class="slick-slider slick-initialized"
                  dir="ltr"
                >
                  <div
                    class="slick-list"
                  >
                    <div
                      class="slick-track"
                      style="opacity: 1; transform: translate3d(0px, 0px, 0px);"
                    >
                      <div
                        aria-hidden="false"
                        class="slick-slide slick-active slick-current"
                        data-index="0"
                        style="outline: none; width: 0px;"
                        tabindex="-1"
                      >
                        <div>
                          <a
                            class="category-card emotion-7"
                            href="https://www.google.com"
                            target="_self"
                          >
                            <div
                              aria-live="polite"
                              class="emotion-8"
                            >
                              <div
                                aria-hidden="false"
                                class="emotion-9"
                              >
                                <img
                                  alt="Woman"
                                  class="emotion-10"
                                  src=""
                                />
                              </div>
                            </div>
                            <div
                              class="emotion-11"
                            >
                              <div
                                class="emotion-12"
                              >
                                <div
                                  class="emotion-13"
                                >
                                  <div>
                                    <p
                                      class="amp-cms--p"
                                      style="text-align:center;"
                                    >
                                      <span
                                        class="amp-cms--eyebrow-1"
                                        style="font-weight:700"
                                      >
                                        Straight
                                      </span>
                                    </p>
                                  </div>
                                </div>
                                <div
                                  class="emotion-14"
                                >
                                  <div>
                                    <p
                                      class="amp-cms--p"
                                      style="text-align:center;"
                                    >
                                      <span
                                        class="amp-cms--body-2"
                                      >
                                        sit amet consectetur adipiscing elit eiusmod.
                                      </span>
                                    </p>
                                  </div>
                                </div>
                              </div>
                              <div
                                class="emotion-15"
                                data-testid="category-card-bottom-border"
                              />
                            </div>
                          </a>
                        </div>
                      </div>
                      <div
                        aria-hidden="false"
                        class="slick-slide slick-active"
                        data-index="1"
                        style="outline: none; width: 0px;"
                        tabindex="-1"
                      >
                        <div>
                          <a
                            class="category-card emotion-7"
                            href="https://www.google.com"
                            target="_self"
                          >
                            <div
                              aria-live="polite"
                              class="emotion-8"
                            >
                              <div
                                aria-hidden="false"
                                class="emotion-9"
                              >
                                <img
                                  alt="Man"
                                  class="emotion-10"
                                  src=""
                                />
                              </div>
                            </div>
                            <div
                              class="emotion-11"
                            >
                              <div
                                class="emotion-12"
                              >
                                <div
                                  class="emotion-13"
                                >
                                  <div>
                                    <p
                                      class="amp-cms--p"
                                      style="text-align:center;"
                                    >
                                      <span
                                        class="amp-cms--eyebrow-1"
                                        style="font-weight:700"
                                      >
                                        Skinny
                                      </span>
                                    </p>
                                  </div>
                                </div>
                                <div
                                  class="emotion-14"
                                >
                                  <div>
                                    <p
                                      class="amp-cms--p"
                                      style="text-align:center;"
                                    >
                                      <span
                                        class="amp-cms--body-2"
                                      >
                                        sit amet consectetur adipiscing elit eiusmod.
                                      </span>
                                    </p>
                                  </div>
                                </div>
                              </div>
                              <div
                                class="emotion-15"
                                data-testid="category-card-bottom-border"
                              />
                            </div>
                          </a>
                        </div>
                      </div>
                      <div
                        aria-hidden="false"
                        class="slick-slide slick-active"
                        data-index="2"
                        style="outline: none; width: 0px;"
                        tabindex="-1"
                      >
                        <div>
                          <a
                            class="category-card emotion-7"
                            href="https://www.google.com"
                            target="_self"
                          >
                            <div
                              aria-live="polite"
                              class="emotion-8"
                            >
                              <div
                                aria-hidden="false"
                                class="emotion-9"
                              >
                                <img
                                  alt="Jeans"
                                  class="emotion-10"
                                  src=""
                                />
                              </div>
                            </div>
                            <div
                              class="emotion-11"
                            >
                              <div
                                class="emotion-12"
                              >
                                <div
                                  class="emotion-13"
                                >
                                  <div>
                                    <p
                                      class="amp-cms--p"
                                      style="text-align:center;"
                                    >
                                      <span
                                        class="amp-cms--eyebrow-1"
                                        style="font-weight:700"
                                      >
                                        Super-skinny
                                      </span>
                                    </p>
                                  </div>
                                </div>
                                <div
                                  class="emotion-14"
                                >
                                  <div>
                                    <p
                                      class="amp-cms--p"
                                      style="text-align:center;"
                                    >
                                      <span
                                        class="amp-cms--body-2"
                                      >
                                        sit amet consectetur adipiscing elit eiusmod.
                                      </span>
                                    </p>
                                  </div>
                                </div>
                              </div>
                              <div
                                class="emotion-15"
                                data-testid="category-card-bottom-border"
                              />
                            </div>
                          </a>
                        </div>
                      </div>
                      <div
                        aria-hidden="true"
                        class="slick-slide"
                        data-index="3"
                        style="outline: none; width: 0px;"
                        tabindex="-1"
                      >
                        <div>
                          <a
                            class="category-card emotion-7"
                            href="https://www.gap.com"
                            target="_self"
                          >
                            <div
                              aria-live="polite"
                              class="emotion-8"
                            >
                              <div
                                aria-hidden="false"
                                class="emotion-9"
                              >
                                <img
                                  alt="Another Man"
                                  class="emotion-10"
                                  src=""
                                />
                              </div>
                            </div>
                            <div
                              class="emotion-11"
                            >
                              <div
                                class="emotion-12"
                              >
                                <div
                                  class="emotion-13"
                                >
                                  <div>
                                    <p
                                      class="amp-cms--p"
                                      style="text-align:center;"
                                    >
                                      <span
                                        class="amp-cms--eyebrow-1"
                                        style="font-weight:700;text-decoration:underline"
                                      >
                                        Hover Headline
                                      </span>
                                    </p>
                                  </div>
                                </div>
                                <div
                                  class="emotion-14"
                                >
                                  <div>
                                    <p
                                      class="amp-cms--p"
                                      style="text-align:center;"
                                    >
                                      <span
                                        class="amp-cms--body-2"
                                      >
                                        sit amet consectetur adipiscing elit eiusmod.
                                      </span>
                                    </p>
                                  </div>
                                </div>
                              </div>
                              <div
                                class="emotion-15"
                                data-testid="category-card-bottom-border"
                              />
                            </div>
                          </a>
                        </div>
                      </div>
                      <div
                        aria-hidden="true"
                        class="slick-slide"
                        data-index="4"
                        style="outline: none; width: 0px;"
                        tabindex="-1"
                      >
                        <div>
                          <a
                            class="category-card emotion-7"
                            href="https://www.gap.com"
                            target="_self"
                          >
                            <div
                              aria-live="polite"
                              class="emotion-8"
                            >
                              <div
                                aria-hidden="false"
                                class="emotion-9"
                              >
                                <img
                                  alt="Posing"
                                  class="emotion-10"
                                  src=""
                                />
                              </div>
                            </div>
                            <div
                              class="emotion-11"
                            >
                              <div
                                class="emotion-12"
                              >
                                <div
                                  class="emotion-13"
                                >
                                  <div>
                                    <p
                                      class="amp-cms--p"
                                      style="text-align:center;"
                                    >
                                      <span
                                        class="amp-cms--eyebrow-1"
                                        style="font-weight:700;text-decoration:underline"
                                      >
                                        Selected Headline
                                      </span>
                                    </p>
                                  </div>
                                </div>
                                <div
                                  class="emotion-14"
                                >
                                  <div>
                                    <p
                                      class="amp-cms--p"
                                      style="text-align:center;"
                                    >
                                      <span
                                        class="amp-cms--body-2"
                                      >
                                        sit amet consectetur adipiscing elit eiusmod.
                                      </span>
                                    </p>
                                  </div>
                                </div>
                              </div>
                              <div
                                class="emotion-15"
                                data-testid="category-card-bottom-border"
                              />
                            </div>
                          </a>
                        </div>
                      </div>
                      <div
                        aria-hidden="true"
                        class="slick-slide"
                        data-index="5"
                        style="outline: none; width: 0px;"
                        tabindex="-1"
                      >
                        <div>
                          <a
                            class="category-card emotion-7"
                            href="https://www.google.com"
                            target="_self"
                          >
                            <div
                              aria-live="polite"
                              class="emotion-8"
                            >
                              <div
                                aria-hidden="false"
                                class="emotion-9"
                              >
                                <img
                                  alt="Yoga"
                                  class="emotion-10"
                                  src=""
                                />
                              </div>
                            </div>
                            <div
                              class="emotion-11"
                            >
                              <div
                                class="emotion-12"
                              >
                                <div
                                  class="emotion-13"
                                >
                                  <div>
                                    <p
                                      class="amp-cms--p"
                                      style="text-align:center;"
                                    >
                                      <span
                                        class="amp-cms--eyebrow-1"
                                        style="font-weight:700"
                                      >
                                        Wide-Leg
                                      </span>
                                    </p>
                                  </div>
                                </div>
                                <div
                                  class="emotion-14"
                                >
                                  <div>
                                    <p
                                      class="amp-cms--p"
                                      style="text-align:center;"
                                    >
                                      <span
                                        class="amp-cms--body-2"
                                      >
                                        sit amet consectetur adipiscing elit eiusmod.
                                      </span>
                                    </p>
                                  </div>
                                </div>
                              </div>
                              <div
                                class="emotion-15"
                                data-testid="category-card-bottom-border"
                              />
                            </div>
                          </a>
                        </div>
                      </div>
                      <div
                        aria-hidden="true"
                        class="slick-slide"
                        data-index="6"
                        style="outline: none; width: 0px;"
                        tabindex="-1"
                      >
                        <div>
                          <a
                            class="category-card emotion-7"
                            href="htttps://www.gap.com"
                            target="_self"
                          >
                            <div
                              aria-live="polite"
                              class="emotion-8"
                            >
                              <div
                                aria-hidden="false"
                                class="emotion-9"
                              >
                                <img
                                  alt="People"
                                  class="emotion-10"
                                  src=""
                                />
                              </div>
                            </div>
                            <div
                              class="emotion-11"
                            >
                              <div
                                class="emotion-12"
                              >
                                <div
                                  class="emotion-13"
                                >
                                  <div>
                                    <p
                                      class="amp-cms--p"
                                      style="text-align:center;"
                                    >
                                      <span
                                        class="amp-cms--eyebrow-1"
                                        style="font-weight:700"
                                      >
                                        Skinny-Leg
                                      </span>
                                    </p>
                                  </div>
                                </div>
                                <div
                                  class="emotion-14"
                                >
                                  <div>
                                    <p
                                      class="amp-cms--p"
                                      style="text-align:center;"
                                    >
                                      <span
                                        class="amp-cms--body-2"
                                      >
                                        sit amet consectetur adipiscing elit eiusmod.
                                      </span>
                                    </p>
                                  </div>
                                </div>
                              </div>
                              <div
                                class="emotion-15"
                                data-testid="category-card-bottom-border"
                              />
                            </div>
                          </a>
                        </div>
                      </div>
                      <div
                        aria-hidden="true"
                        class="slick-slide"
                        data-index="7"
                        style="outline: none; width: 0px;"
                        tabindex="-1"
                      >
                        <div>
                          <a
                            class="category-card emotion-7"
                            href="https://www.google.com"
                            target="_self"
                          >
                            <div
                              aria-live="polite"
                              class="emotion-8"
                            >
                              <div
                                aria-hidden="false"
                                class="emotion-9"
                              >
                                <img
                                  alt="Woman"
                                  class="emotion-10"
                                  src=""
                                />
                              </div>
                            </div>
                            <div
                              class="emotion-11"
                            >
                              <div
                                class="emotion-12"
                              >
                                <div
                                  class="emotion-13"
                                >
                                  <div>
                                    <p
                                      class="amp-cms--p"
                                      style="text-align:center;"
                                    >
                                      <span
                                        class="amp-cms--eyebrow-1"
                                        style="font-weight:700"
                                      >
                                        Hover
                                      </span>
                                    </p>
                                  </div>
                                </div>
                                <div
                                  class="emotion-14"
                                >
                                  <div>
                                    <p
                                      class="amp-cms--p"
                                      style="text-align:center;"
                                    >
                                      <span
                                        class="amp-cms--body-2"
                                      >
                                        sit amet consectetur adipiscing elit sed do.
                                      </span>
                                    </p>
                                  </div>
                                </div>
                              </div>
                              <div
                                class="emotion-15"
                                data-testid="category-card-bottom-border"
                              />
                            </div>
                          </a>
                        </div>
                      </div>
                      <div
                        aria-hidden="true"
                        class="slick-slide"
                        data-index="8"
                        style="outline: none; width: 0px;"
                        tabindex="-1"
                      >
                        <div>
                          <a
                            class="category-card emotion-7"
                            href="https://www.google.com"
                            target="_self"
                          >
                            <div
                              aria-live="polite"
                              class="emotion-8"
                            >
                              <div
                                aria-hidden="false"
                                class="emotion-9"
                              >
                                <img
                                  alt="Man"
                                  class="emotion-10"
                                  src=""
                                />
                              </div>
                            </div>
                            <div
                              class="emotion-11"
                            >
                              <div
                                class="emotion-12"
                              >
                                <div
                                  class="emotion-13"
                                >
                                  <div>
                                    <p
                                      class="amp-cms--p"
                                      style="text-align:center;"
                                    >
                                      <span
                                        class="amp-cms--eyebrow-1"
                                        style="font-weight:700"
                                      >
                                        Selected
                                      </span>
                                    </p>
                                  </div>
                                </div>
                                <div
                                  class="emotion-14"
                                >
                                  <div>
                                    <p
                                      class="amp-cms--p"
                                      style="text-align:center;"
                                    >
                                      <span
                                        class="amp-cms--body-2"
                                      >
                                        sit amet consectetur adipiscing elit sed do.
                                      </span>
                                    </p>
                                  </div>
                                </div>
                              </div>
                              <div
                                class="emotion-15"
                                data-testid="category-card-bottom-border"
                              />
                            </div>
                          </a>
                        </div>
                      </div>
                      <div
                        aria-hidden="true"
                        class="slick-slide"
                        data-index="9"
                        style="outline: none; width: 0px;"
                        tabindex="-1"
                      >
                        <div>
                          <a
                            class="category-card emotion-7"
                            href="https://www,gap.com"
                            target="_self"
                          >
                            <div
                              aria-live="polite"
                              class="emotion-8"
                            >
                              <div
                                aria-hidden="false"
                                class="emotion-9"
                              >
                                <img
                                  alt="Another Man"
                                  class="emotion-10"
                                  src=""
                                />
                              </div>
                            </div>
                            <div
                              class="emotion-11"
                            >
                              <div
                                class="emotion-12"
                              >
                                <div
                                  class="emotion-13"
                                >
                                  <div>
                                    <p
                                      class="amp-cms--p"
                                      style="text-align:center;"
                                    >
                                      <span
                                        class="amp-cms--eyebrow-1"
                                        style="font-weight:700"
                                      >
                                        Card 10
                                      </span>
                                    </p>
                                  </div>
                                </div>
                                <div
                                  class="emotion-14"
                                >
                                  <div>
                                    <p
                                      class="amp-cms--p"
                                      style="text-align:center;"
                                    >
                                      <span
                                        class="amp-cms--body-2"
                                      >
                                        sit amet consectetur adipiscing elit sed do.
                                      </span>
                                    </p>
                                  </div>
                                </div>
                              </div>
                              <div
                                class="emotion-15"
                                data-testid="category-card-bottom-border"
                              />
                            </div>
                          </a>
                        </div>
                      </div>
                    </div>
                  </div>
                  <button
                    aria-label="Next"
                    class="slick-arrow slick-next emotion-97"
                  >
                    <span
                      aria-hidden="true"
                      class="emotion-98"
                    >
                      <svg
                        fill="none"
                        viewBox="0 0 41 40"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <g
                          opacity="0.8"
                        >
                          <rect
                            fill="#FFFFFF"
                            height="40"
                            rx="20"
                            width="40"
                            x="0.5"
                          />
                          <path
                            d="M14.5 20.35C14.3067 20.35 14.15 20.1933 14.15 20C14.15 19.8067 14.3067 19.65 14.5 19.65V20.35ZM26.7475 19.7525C26.8842 19.8892 26.8842 20.1108 26.7475 20.2475L24.5201 22.4749C24.3834 22.6116 24.1618 22.6116 24.0251 22.4749C23.8884 22.3382 23.8884 22.1166 24.0251 21.9799L26.005 20L24.0251 18.0201C23.8884 17.8834 23.8884 17.6618 24.0251 17.5251C24.1618 17.3884 24.3834 17.3884 24.5201 17.5251L26.7475 19.7525ZM14.5 19.65L26.5 19.65V20.35L14.5 20.35V19.65Z"
                            fill="#000000"
                          />
                        </g>
                      </svg>
                    </span>
                  </button>
                  <ul
                    class="slick-dots"
                    style="display: block;"
                  >
                    <li
                      class="slick-active"
                    >
                      <button>
                        1
                      </button>
                    </li>
                    <li
                      class=""
                    >
                      <button>
                        2
                      </button>
                    </li>
                    <li
                      class=""
                    >
                      <button>
                        3
                      </button>
                    </li>
                    <li
                      class=""
                    >
                      <button>
                        4
                      </button>
                    </li>
                    <li
                      class=""
                    >
                      <button>
                        5
                      </button>
                    </li>
                    <li
                      class=""
                    >
                      <button>
                        6
                      </button>
                    </li>
                    <li
                      class=""
                    >
                      <button>
                        7
                      </button>
                    </li>
                    <li
                      class=""
                    >
                      <button>
                        8
                      </button>
                    </li>
                    <li
                      class=""
                    >
                      <button>
                        9
                      </button>
                    </li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </div>
      </article>
    </div>
  </div>
</div>
`;
