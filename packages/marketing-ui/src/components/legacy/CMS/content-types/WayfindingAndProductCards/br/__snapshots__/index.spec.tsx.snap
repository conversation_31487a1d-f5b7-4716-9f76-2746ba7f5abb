// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`BananaRepublic WayfindingAndProductCards on Desktop with carousel settings and pagination: desktop-carousel 1`] = `
.emotion-0 {
  background: #8A8AFF;
}

.emotion-1 {
  padding: 40px 32px;
}

.emotion-2 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  justify-content: space-between;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  margin-bottom: 64px;
  margin-bottom: 24px;
  display: block;
}

.emotion-3 .amp-cms--p {
  padding: 0;
  margin: 0;
  line-height: 0;
}

.emotion-3 .amp-cms--p>span {
  display: inline;
  white-space: break-spaces;
}

.emotion-3 .amp-cms--p a {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  position: relative;
  z-index: 2;
  pointer-events: auto;
}

.emotion-3 .amp-cms--p a:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-3 .amp-cms--legal-copy {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, 0.8333333333333334vw);
  line-height: 1.5;
  letter-spacing: 0.034722222222222224vw;
  font-weight: 350;
}

.emotion-3 sup {
  vertical-align: top;
  display: inline-block;
  margin-top: -0.25ex;
}

.emotion-3 sub {
  vertical-align: bottom;
  display: inline-block;
  margin-bottom: -0.25ex;
}

.emotion-3 .amp-cms--f-0 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 0.8333333333333334vw;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--f-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 0.8333333333333334vw;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--f-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 0.8333333333333334vw;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--fn-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 0.8333333333333334vw;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--body-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, 1.3888888888888888vw);
  line-height: 1.5;
  letter-spacing: 0.034722222222222224vw;
  font-weight: 350;
}

.emotion-3 .amp-cms--body-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(16px, 1.25vw);
  line-height: 1.4444444444444444;
  letter-spacing: 0.034722222222222224vw;
  font-weight: 350;
}

.emotion-3 .amp-cms--body-3 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, 1.1111111111111112vw);
  line-height: 1.375;
  letter-spacing: 0.034722222222222224vw;
  font-weight: 350;
}

.emotion-3 .amp-cms--body-4 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, 0.9722222222222222vw);
  line-height: 1.4285714285714286;
  letter-spacing: 0.034722222222222224vw;
  font-weight: 350;
}

.emotion-3 .amp-cms--body-5 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, 0.8333333333333334vw);
  line-height: 1.5;
  letter-spacing: 0.034722222222222224vw;
  font-weight: 350;
}

.emotion-3 .amp-cms--eyebrow-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, 1.3888888888888888vw);
  line-height: 1;
  letter-spacing: 0.10416666666666667vw;
  font-weight: 600;
}

.emotion-3 .amp-cms--eyebrow-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, 1.1111111111111112vw);
  line-height: 1;
  letter-spacing: 0.10416666666666667vw;
  font-weight: 600;
}

.emotion-3 .amp-cms--eyebrow-3 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, 0.8333333333333334vw);
  line-height: 1;
  letter-spacing: 0.10416666666666667vw;
  font-weight: 600;
}

.emotion-3 .amp-cms--headline-1 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(48px, 6.666666666666667vw);
  line-height: 1;
  letter-spacing: 0.034722222222222224vw;
  font-weight: 300;
}

.emotion-3 .amp-cms--headline-2 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(40px, 5.555555555555555vw);
  line-height: 1;
  letter-spacing: 0.034722222222222224vw;
  font-weight: 300;
}

.emotion-3 .amp-cms--headline-3 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(32px, 4.444444444444445vw);
  line-height: 1;
  letter-spacing: 0.034722222222222224vw;
  font-weight: 300;
}

.emotion-3 .amp-cms--headline-4 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(28px, 3.3333333333333335vw);
  line-height: 1;
  letter-spacing: 0.034722222222222224vw;
  font-weight: 300;
}

.emotion-3 .amp-cms--headline-5 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, 2.2222222222222223vw);
  line-height: 1.125;
  letter-spacing: 0.034722222222222224vw;
  font-weight: 300;
}

.emotion-3 .amp-cms--headline-6 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, 1.6666666666666667vw);
  line-height: 1.3333333333333333;
  letter-spacing: 0.034722222222222224vw;
  font-weight: 300;
}

.emotion-3 .amp-cms--headline-7 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, 1.3888888888888888vw);
  line-height: 1.4;
  letter-spacing: 0.034722222222222224vw;
  font-weight: 300;
}

.emotion-3 .amp-cms--headlineAlt-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(32px, 4.444444444444445vw);
  line-height: 1;
  letter-spacing: 0.034722222222222224vw;
  font-weight: 400;
}

.emotion-3 .amp-cms--headlineAlt-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, 3.3333333333333335vw);
  line-height: 1;
  letter-spacing: 0.034722222222222224vw;
  font-weight: 400;
}

.emotion-3 .amp-cms--headlineAlt-3 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, 2.2222222222222223vw);
  line-height: 1.0625;
  letter-spacing: 0.034722222222222224vw;
  font-weight: 400;
}

.emotion-3 .amp-cms--headlineAlt-4 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(60px, 8.333333333333332vw);
  line-height: 1;
  letter-spacing: 0.034722222222222224vw;
  font-weight: 300;
}

.emotion-3 .amp-cms--headlineAlt-5 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 1.1111111111111112vw;
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-3 .amp-cms--headlineAlt-6 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 1.1111111111111112vw;
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-3 .amp-cms--headlineAlt-7 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 1.1111111111111112vw;
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-3 .amp-cms--promo-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(8px, 0.6944444444444444vw);
  line-height: 1.6;
  letter-spacing: 0.034722222222222224vw;
  font-weight: 350;
}

.emotion-3 .amp-cms--promo-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, 5.555555555555555vw);
  line-height: 1;
  letter-spacing: 0vw;
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-3 .amp-cms--promoAlt-1 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 1.1111111111111112vw;
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 600;
}

.emotion-3 .amp-cms--subhead-1 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, 1.25vw);
  line-height: 1.3333333333333333;
  letter-spacing: 0.034722222222222224vw;
  font-weight: 300;
}

.emotion-3 .amp-cms--subhead-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(16px, 1.6666666666666667vw);
  line-height: 1;
  letter-spacing: 0.034722222222222224vw;
  font-weight: 350;
}

.emotion-3 .amp-cms--subhead-3 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, 1.3888888888888888vw);
  line-height: 1;
  letter-spacing: 0.034722222222222224vw;
  font-weight: 350;
}

.emotion-4 {
  display: block;
}

.emotion-4 div.slick-track {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-4 div.slick-list {
  margin-right: 0;
}

.emotion-4 div.slick-slide {
  height: auto;
}

.emotion-4 div.slick-slide>div {
  position: relative;
  margin-right: 4px;
  height: 100%;
}

.emotion-4 .slick-slider button.slick-next.slick-arrow.slick-next {
  left: calc(100% - 16px);
  width: 40px;
  height: 40px;
  -webkit-transform: translate(0,-55%) rotate(0);
  -moz-transform: translate(0,-55%) rotate(0);
  -ms-transform: translate(0,-55%) rotate(0);
  transform: translate(0,-55%) rotate(0);
}

.emotion-4 .slick-slider button.slick-prev.slick-arrow.slick-prev {
  left: -24px;
  width: 40px;
  height: 40px;
  -webkit-transform: translate(0,-55%) rotate(-180deg);
  -moz-transform: translate(0,-55%) rotate(-180deg);
  -ms-transform: translate(0,-55%) rotate(-180deg);
  transform: translate(0,-55%) rotate(-180deg);
}

.emotion-4 .slick-slider ul.slick-dots {
  padding-top: 24px;
  margin-top: 24px;
  position: static;
  gap: 16px;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
}

.emotion-4 .slick-slider ul.slick-dots li {
  margin: 0;
  pointer-events: all;
  width: 4px!important;
  height: 4px!important;
}

.emotion-5 {
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-5 .slick-list {
  overflow: hidden;
}

.emotion-5 .slick-track {
  width: -webkit-max-content!important;
  width: -moz-max-content!important;
  width: max-content!important;
}

.emotion-5 button.slick-next.slick-arrow.slick-next,
.emotion-5 button.slick-prev.slick-arrow.slick-prev {
  z-index: 2;
  top: 0px;
}

.emotion-5 .slick-slide {
  margin: 0;
  width: auto;
}

.emotion-5 .slick-disabled {
  display: none!important;
}

.emotion-5 .slick-next {
  left: calc(100% - 44px);
}

.emotion-5 .slick-slide:first-of-type a>div:nth-of-type(2)>div>div:nth-of-type(2) {
  border-left: none;
}

.emotion-5 .slick-slide>div:first-of-type {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-5 .slick-slide>div:first-of-type>div:first-of-type {
  width: 100%;
}

.emotion-5 .product-cards-carousel .slick-list {
  overflow: auto;
  overflow-x: clip;
  overflow-y: visible;
  -webkit-transform: none;
  -moz-transform: none;
  -ms-transform: none;
  transform: none;
}

.emotion-6 {
  position: relative;
}

.emotion-6 .slick-slider {
  position: relative;
  display: block;
  box-sizing: border-box;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  -webkit-touch-callout: none;
  touch-action: pan-y;
  -webkit-tap-highlight-color: transparent;
  scroll-behavior: smooth;
}

.emotion-6 .slick-slider .slick-track,
.emotion-6 .slick-slider .slick-list {
  -webkit-transform: translate3d(0, 0, 0);
  -moz-transform: translate3d(0, 0, 0);
  -ms-transform: translate3d(0, 0, 0);
  transform: translate3d(0, 0, 0);
}

.emotion-6 .slick-list {
  position: relative;
  display: block;
  overflow-x: hidden;
  overflow-y: hidden;
  margin: 0;
  padding: 0;
}

.emotion-6 .slick-list:focus {
  outline: none;
}

.emotion-6 .slick-list.dragging {
  cursor: pointer;
  cursor: hand;
}

.emotion-6 .slick-track {
  position: relative;
  top: 0;
  left: 0;
  display: block;
  margin-left: auto;
  margin-right: auto;
}

.emotion-6 .slick-track:before,
.emotion-6 .slick-track:after {
  display: table;
  content: "";
}

.emotion-6 .slick-track:after {
  clear: both;
}

.emotion-6 .slick-slide {
  display: none;
  float: left;
  height: 100%;
  min-height: 1px;
}

.emotion-6 .slick-slide img {
  display: block;
}

.emotion-6 .slick-slide.slick-loading img {
  display: none;
}

.emotion-6 .slick-slide.dragging img {
  pointer-events: none;
}

.emotion-6 [dir="rtl"] .slick-slide {
  float: right;
}

.emotion-6 .slick-initialized .slick-slide,
.emotion-6 .slick-vertical .slick-slide {
  display: block;
}

.emotion-6 .slick-vertical .slick-slide {
  border: 1px solid transparent;
  height: auto;
}

.emotion-6 .slick-loading .slick-track,
.emotion-6 .slick-loading .slick-slide {
  visibility: hidden;
}

.emotion-6 .slick-loading .slick-list {
  background: #fff url("./ajax-loader.gif") center center no-repeat;
}

.emotion-6 .slick-arrow.slick-hidden {
  display: none;
}

.emotion-6 .slick-prev,
.emotion-6 .slick-next {
  font-size: 0;
  line-height: 0;
  position: absolute;
  top: 50%;
  display: block;
  padding: 0;
  cursor: pointer;
  color: transparent;
  border: none;
  outline: none;
  background: transparent;
}

.emotion-6 .slick-prev:hover,
.emotion-6 .slick-next:hover,
.emotion-6 .slick-prev:focus,
.emotion-6 .slick-next:focus {
  color: transparent;
  outline: none;
  background: transparent;
  opacity: 1;
}

.emotion-6 .slick-prev.slick-disabled,
.emotion-6 .slick-next.slick-disabled {
  opacity: 0.25;
}

.emotion-6 .slick-prev {
  left: -0;
  -webkit-transform: translate(0, -50%) rotate(90deg);
  -moz-transform: translate(0, -50%) rotate(90deg);
  -ms-transform: translate(0, -50%) rotate(90deg);
  transform: translate(0, -50%) rotate(90deg);
}

.emotion-6 .slick-prev span {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-6 [dir="rtl"] .slick-prev {
  right: -0;
  left: auto;
}

.emotion-6 .slick-next {
  right: -0;
  -webkit-transform: translate(0, -50%) rotate(-90deg);
  -moz-transform: translate(0, -50%) rotate(-90deg);
  -ms-transform: translate(0, -50%) rotate(-90deg);
  transform: translate(0, -50%) rotate(-90deg);
}

.emotion-6 .slick-next span {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-6 [dir="rtl"] .slick-next {
  right: auto;
  left: -0;
}

.emotion-6 .slick-dotted.slick-slider {
  margin-bottom: 30px;
}

.emotion-6 .slick-dots {
  position: absolute;
  bottom: -25px;
  display: block;
  width: 100%;
  padding: 0;
  margin: 0;
  list-style: none;
  text-align: center;
}

.emotion-6 .slick-dots li {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: 20px;
  height: 20px;
  margin: 0 5px;
  padding: 0;
  cursor: pointer;
}

.emotion-6 .slick-dots li.slick-active button:before {
  opacity: 0.75;
  color: black;
}

.emotion-6 .slick-dots li button {
  font-size: 0;
  line-height: 0;
  display: block;
  padding: 5px;
  cursor: pointer;
  color: transparent;
  border: 0;
  outline: none;
  background: transparent;
}

.emotion-6 .slick-dots li button:hover,
.emotion-6 .slick-dots li button:focus {
  outline: none;
}

.emotion-6 .slick-dots li button:hover:before,
.emotion-6 .slick-dots li button:focus:before,
.emotion-6 .slick-dots li button:hover:before,
.emotion-6 .slick-dots li button:focus:before {
  opacity: 1;
}

.emotion-6 .slick-dots li button:before {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 5px;
  height: 5px;
  content: "";
  opacity: 0.25;
  background-color: black;
  border-radius: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-6.pagination-line .slick-dots {
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
}

.emotion-6.pagination-line .slick-dots[data-alignment='left'] {
  left: 20px;
  bottom: 0;
  top: 0;
  width: 3px;
}

.emotion-6.pagination-line .slick-dots[data-alignment='center'] {
  right: 0;
  left: 0;
  height: 2px;
  bottom: 0;
}

.emotion-6 .br-spotlight-carousel-pagination-wrapper {
  -webkit-flex: 0;
  -ms-flex: 0;
  flex: 0;
  text-wrap: nowrap;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  gap: 8px;
}

.emotion-6.pagination-line .slick-dots[data-alignment='left'] .br-spotlight-carousel-pagination-wrapper {
  -webkit-transform: rotate(90deg);
  -moz-transform: rotate(90deg);
  -ms-transform: rotate(90deg);
  transform: rotate(90deg);
}

.emotion-6.pagination-line li {
  width: auto!important;
  height: auto!important;
  cursor: default!important;
}

.emotion-6 .slick-dots li {
  margin: 0!important;
}

.emotion-6 .slick-dots li button {
  padding-left: 0!important;
  padding-right: 0!important;
}

.emotion-6 .br-spotlight-carousel-pagination-line {
  position: relative;
  width: 37px;
  height: 2px;
  padding: 0;
  background-color: #808080;
}

.emotion-6 button.br-spotlight-carousel-button::before {
  content: none;
  display: none;
}

.emotion-6 .slick-active>.br-spotlight-carousel-button>.br-spotlight-carousel-pagination-line {
  background-color: #000;
}

.emotion-7 {
  display: inline-block;
  height: 2rem;
  width: 2rem;
  min-height: 2rem;
  min-width: 2rem;
}

.emotion-7 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-8 {
  width: 100%;
  height: 100%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  position: relative;
  -webkit-flex: 1;
  -ms-flex: 1;
  flex: 1;
  gap: 16px;
}

.emotion-8 p>span {
  line-height: 22px;
  letter-spacing: 0.2px;
}

.emotion-9 {
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  width: 100%;
}

.emotion-10 {
  -webkit-background-size: cover;
  background-size: cover;
  aspect-ratio: 3/4;
  overflow: hidden;
}

.emotion-11 {
  width: 100%;
  aspect-ratio: 3/4;
  object-fit: cover;
}

.emotion-12 .amp-cms--p {
  padding: 0;
  margin: 0;
  line-height: 0;
}

.emotion-12 .amp-cms--p>span {
  display: inline;
  white-space: break-spaces;
}

.emotion-12 .amp-cms--p a {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  position: relative;
  z-index: 2;
  pointer-events: auto;
}

.emotion-12 .amp-cms--p a:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-12 .amp-cms--legal-copy {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.5;
  letter-spacing: 0.5px;
  font-weight: 350;
}

.emotion-12 sup {
  vertical-align: top;
  display: inline-block;
  margin-top: -0.25ex;
}

.emotion-12 sub {
  vertical-align: bottom;
  display: inline-block;
  margin-bottom: -0.25ex;
}

.emotion-12 .amp-cms--f-0 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-12 .amp-cms--f-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-12 .amp-cms--f-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-12 .amp-cms--fn-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-12 .amp-cms--body-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 20px;
  line-height: 1.5;
  letter-spacing: 0.5px;
  font-weight: 350;
}

.emotion-12 .amp-cms--body-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 18px;
  line-height: 1.4444444444444444;
  letter-spacing: 0.5px;
  font-weight: 350;
}

.emotion-12 .amp-cms--body-3 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 16px;
  line-height: 1.375;
  letter-spacing: 0.5px;
  font-weight: 350;
}

.emotion-12 .amp-cms--body-4 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 14px;
  line-height: 1.4285714285714286;
  letter-spacing: 0.5px;
  font-weight: 350;
}

.emotion-12 .amp-cms--body-5 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.5;
  letter-spacing: 0.5px;
  font-weight: 350;
}

.emotion-12 .amp-cms--eyebrow-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 20px;
  line-height: 1;
  letter-spacing: 1.5px;
  font-weight: 600;
}

.emotion-12 .amp-cms--eyebrow-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 16px;
  line-height: 1;
  letter-spacing: 1.5px;
  font-weight: 600;
}

.emotion-12 .amp-cms--eyebrow-3 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1;
  letter-spacing: 1.5px;
  font-weight: 600;
}

.emotion-12 .amp-cms--headline-1 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 96px;
  line-height: 1;
  letter-spacing: 0.5px;
  font-weight: 300;
}

.emotion-12 .amp-cms--headline-2 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 80px;
  line-height: 1;
  letter-spacing: 0.5px;
  font-weight: 300;
}

.emotion-12 .amp-cms--headline-3 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 64px;
  line-height: 1;
  letter-spacing: 0.5px;
  font-weight: 300;
}

.emotion-12 .amp-cms--headline-4 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 48px;
  line-height: 1;
  letter-spacing: 0.5px;
  font-weight: 300;
}

.emotion-12 .amp-cms--headline-5 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 32px;
  line-height: 1.125;
  letter-spacing: 0.5px;
  font-weight: 300;
}

.emotion-12 .amp-cms--headline-6 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 24px;
  line-height: 1.3333333333333333;
  letter-spacing: 0.5px;
  font-weight: 300;
}

.emotion-12 .amp-cms--headline-7 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 20px;
  line-height: 1.4;
  letter-spacing: 0.5px;
  font-weight: 300;
}

.emotion-12 .amp-cms--headlineAlt-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 64px;
  line-height: 1;
  letter-spacing: 0.5px;
  font-weight: 400;
}

.emotion-12 .amp-cms--headlineAlt-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 48px;
  line-height: 1;
  letter-spacing: 0.5px;
  font-weight: 400;
}

.emotion-12 .amp-cms--headlineAlt-3 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 32px;
  line-height: 1.0625;
  letter-spacing: 0.5px;
  font-weight: 400;
}

.emotion-12 .amp-cms--headlineAlt-4 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 120px;
  line-height: 1;
  letter-spacing: 0.5px;
  font-weight: 300;
}

.emotion-12 .amp-cms--headlineAlt-5 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 16px;
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-12 .amp-cms--headlineAlt-6 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 16px;
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-12 .amp-cms--headlineAlt-7 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 16px;
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-12 .amp-cms--promo-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 10px;
  line-height: 1.6;
  letter-spacing: 0.5px;
  font-weight: 350;
}

.emotion-12 .amp-cms--promo-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 80px;
  line-height: 1;
  letter-spacing: 0px;
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-12 .amp-cms--promoAlt-1 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 16px;
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 600;
}

.emotion-12 .amp-cms--subhead-1 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 18px;
  line-height: 1.3333333333333333;
  letter-spacing: 0.5px;
  font-weight: 300;
}

.emotion-12 .amp-cms--subhead-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 24px;
  line-height: 1;
  letter-spacing: 0.5px;
  font-weight: 350;
}

.emotion-12 .amp-cms--subhead-3 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 20px;
  line-height: 1;
  letter-spacing: 0.5px;
  font-weight: 350;
}

.emotion-13 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  -webkit-box-flex-wrap: wrap;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  gap: 8px 16px;
  z-index: 2;
  pointer-events: none;
  padding-bottom: 2px;
}

.emotion-14 {
  background-color: transparent;
  border: 0;
  box-sizing: border-box;
  color: #000000;
  display: inline-block;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  font-size: 12px;
  font-weight: 400;
  height: 16px;
  letter-spacing: 0.5px;
  line-height: 1.33;
  outline: 0;
  padding: 0;
  text-align: center;
  -webkit-text-decoration: underline;
  text-decoration: underline;
  text-decoration-color: #000000;
  text-underline-offset: 5px;
  -webkit-transition: text-decoration-color 200ms;
  transition: text-decoration-color 200ms;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  letter-spacing: 1.3px;
  pointer-events: all;
}

.emotion-14[aria-disabled=true] {
  color: #999999;
  pointer-events: none;
  text-decoration-color: #CCCCCC;
}

.emotion-14:hover:not([aria-disabled=true]) {
  text-decoration-color: #000000;
}

.emotion-89 {
  padding-top: 24px!important;
  margin-top: 24px!important;
}

<div>
  <div
    style="font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;"
  >
    <div
      class="emotion-0"
      height="0"
      width="0"
    >
      <div
        class="emotion-1"
      >
        <div
          class="emotion-2"
        >
          <div
            class="emotion-3"
          >
            <div>
              <p
                class="amp-cms--p"
                style="text-align:left;"
              >
                <span
                  class="amp-cms--headline-7"
                >
                  lorem ipsum dolor default text
                </span>
              </p>
            </div>
          </div>
        </div>
        <div
          class="emotion-4"
        >
          <nav
            class="emotion-5"
          >
            <div
              class="pagination-line emotion-6"
            >
              <div
                class="slick-slider slick-initialized"
                dir="ltr"
              >
                <button
                  class="slick-arrow slick-prev slick-disabled"
                  currentslide="0"
                  data-role="none"
                  slidecount="10"
                  style="display: block;"
                >
                  <span
                    aria-hidden="true"
                    class="emotion-7"
                  >
                    <svg
                      fill="none"
                      viewBox="0 0 41 40"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <g
                        opacity="0.8"
                      >
                        <rect
                          fill="#FFFFFF"
                          height="40"
                          rx="20"
                          width="40"
                          x="0.5"
                        />
                        <path
                          d="M14.5 20.35C14.3067 20.35 14.15 20.1933 14.15 20C14.15 19.8067 14.3067 19.65 14.5 19.65V20.35ZM26.7475 19.7525C26.8842 19.8892 26.8842 20.1108 26.7475 20.2475L24.5201 22.4749C24.3834 22.6116 24.1618 22.6116 24.0251 22.4749C23.8884 22.3382 23.8884 22.1166 24.0251 21.9799L26.005 20L24.0251 18.0201C23.8884 17.8834 23.8884 17.6618 24.0251 17.5251C24.1618 17.3884 24.3834 17.3884 24.5201 17.5251L26.7475 19.7525ZM14.5 19.65L26.5 19.65V20.35L14.5 20.35V19.65Z"
                          fill="#000000"
                        />
                      </g>
                    </svg>
                  </span>
                </button>
                <div
                  class="slick-list"
                >
                  <div
                    class="slick-track"
                    style="opacity: 1; transform: translate3d(0px, 0px, 0px);"
                  >
                    <div
                      aria-hidden="false"
                      class="slick-slide slick-active slick-current"
                      data-index="0"
                      style="outline: none; width: 0px;"
                      tabindex="-1"
                    >
                      <div>
                        <div
                          class="emotion-8"
                        >
                          <a
                            aria-label="card1 link wrapper"
                            class="emotion-9"
                            href="www.gap.com"
                          />
                          <div
                            class="emotion-10"
                            data-testid="product-card-image"
                          >
                            <img
                              alt="card1 image"
                              class="emotion-11"
                              src=""
                            />
                          </div>
                          <div
                            class="emotion-12"
                          >
                            <div>
                              <p
                                class="amp-cms--p"
                                style="text-align:left;"
                              >
                                <span
                                  class="amp-cms--body-2"
                                >
                                  Card1 rich text
                                </span>
                              </p>
                            </div>
                          </div>
                          <div
                            class="emotion-13"
                          >
                            <a
                              class="emotion-14"
                              data-testid="composable-btn-br"
                              href="www.cta1.com"
                              role="button"
                            >
                              SHOP WOMEN
                            </a>
                            <a
                              class="emotion-14"
                              data-testid="composable-btn-br"
                              href="www.cta2.com"
                              role="button"
                            >
                              SHOP MEN
                            </a>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div
                      aria-hidden="false"
                      class="slick-slide slick-active"
                      data-index="1"
                      style="outline: none; width: 0px;"
                      tabindex="-1"
                    >
                      <div>
                        <div
                          class="emotion-8"
                        >
                          <a
                            aria-label="card2 link wrapper"
                            class="emotion-9"
                            href="www.oldnavy.com"
                          />
                          <div
                            class="emotion-10"
                            data-testid="product-card-image"
                          >
                            <img
                              alt="card2 image"
                              class="emotion-11"
                              src=""
                            />
                          </div>
                          <div
                            class="emotion-12"
                          >
                            <div>
                              <p
                                class="amp-cms--p"
                                style="text-align:left;"
                              >
                                <span
                                  class="amp-cms--body-2"
                                >
                                  Card2 rich text
                                </span>
                              </p>
                            </div>
                          </div>
                          <div
                            class="emotion-13"
                          >
                            <a
                              class="emotion-14"
                              data-testid="composable-btn-br"
                              href="www.cta1.com"
                              role="button"
                            >
                              SHOP WOMEN
                            </a>
                            <a
                              class="emotion-14"
                              data-testid="composable-btn-br"
                              href="www.cta2.com"
                              role="button"
                            >
                              SHOP MEN
                            </a>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div
                      aria-hidden="false"
                      class="slick-slide slick-active"
                      data-index="2"
                      style="outline: none; width: 0px;"
                      tabindex="-1"
                    >
                      <div>
                        <div
                          class="emotion-8"
                        >
                          <a
                            aria-label="card3 link wrapper"
                            class="emotion-9"
                            href="www.bananarepublic.com"
                          />
                          <div
                            class="emotion-10"
                            data-testid="product-card-image"
                          >
                            <img
                              alt="card3 image"
                              class="emotion-11"
                              src=""
                            />
                          </div>
                          <div
                            class="emotion-12"
                          >
                            <div>
                              <p
                                class="amp-cms--p"
                                style="text-align:left;"
                              >
                                <span
                                  class="amp-cms--body-2"
                                >
                                  Card3 rich text
                                </span>
                              </p>
                            </div>
                          </div>
                          <div
                            class="emotion-13"
                          >
                            <a
                              class="emotion-14"
                              data-testid="composable-btn-br"
                              href="www.cta1.com"
                              role="button"
                            >
                              SHOP WOMEN
                            </a>
                            <a
                              class="emotion-14"
                              data-testid="composable-btn-br"
                              href="www.cta2.com"
                              role="button"
                            >
                              SHOP MEN
                            </a>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div
                      aria-hidden="false"
                      class="slick-slide slick-active"
                      data-index="3"
                      style="outline: none; width: 0px;"
                      tabindex="-1"
                    >
                      <div>
                        <div
                          class="emotion-8"
                        >
                          <a
                            aria-label="card4 link wrapper"
                            class="emotion-9"
                            href="www.c.com"
                          />
                          <div
                            class="emotion-10"
                            data-testid="product-card-image"
                          >
                            <img
                              alt="card4 image"
                              class="emotion-11"
                              src=""
                            />
                          </div>
                          <div
                            class="emotion-12"
                          >
                            <div>
                              <p
                                class="amp-cms--p"
                                style="text-align:left;"
                              >
                                <span
                                  class="amp-cms--body-2"
                                >
                                  Card4 rich text
                                </span>
                              </p>
                            </div>
                          </div>
                          <div
                            class="emotion-13"
                          >
                            <a
                              class="emotion-14"
                              data-testid="composable-btn-br"
                              href="www.c.com"
                              role="button"
                            >
                              SHOP WOMEN
                            </a>
                            <a
                              class="emotion-14"
                              data-testid="composable-btn-br"
                              href="www.c.com"
                              role="button"
                            >
                              SHOP MEN
                            </a>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div
                      aria-hidden="true"
                      class="slick-slide"
                      data-index="4"
                      style="outline: none; width: 0px;"
                      tabindex="-1"
                    >
                      <div>
                        <div
                          class="emotion-8"
                        >
                          <a
                            aria-label="card5 link wrapper"
                            class="emotion-9"
                            href="www.c.com"
                          />
                          <div
                            class="emotion-10"
                            data-testid="product-card-image"
                          >
                            <img
                              alt="card5 image"
                              class="emotion-11"
                              src=""
                            />
                          </div>
                          <div
                            class="emotion-12"
                          >
                            <div>
                              <p
                                class="amp-cms--p"
                                style="text-align:left;"
                              >
                                <span
                                  class="amp-cms--body-2"
                                >
                                  Card5 rich text
                                </span>
                              </p>
                            </div>
                          </div>
                          <div
                            class="emotion-13"
                          >
                            <a
                              class="emotion-14"
                              data-testid="composable-btn-br"
                              href="www.c.com"
                              role="button"
                            >
                              SHOP WOMEN
                            </a>
                            <a
                              class="emotion-14"
                              data-testid="composable-btn-br"
                              href="www.c.com"
                              role="button"
                            >
                              SHOP MEN
                            </a>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div
                      aria-hidden="true"
                      class="slick-slide"
                      data-index="5"
                      style="outline: none; width: 0px;"
                      tabindex="-1"
                    >
                      <div>
                        <div
                          class="emotion-8"
                        >
                          <a
                            aria-label="card6 link wrapper"
                            class="emotion-9"
                            href="www.c.com"
                          />
                          <div
                            class="emotion-10"
                            data-testid="product-card-image"
                          >
                            <img
                              alt="card6 image"
                              class="emotion-11"
                              src=""
                            />
                          </div>
                          <div
                            class="emotion-12"
                          >
                            <div>
                              <p
                                class="amp-cms--p"
                                style="text-align:left;"
                              >
                                <span
                                  class="amp-cms--body-2"
                                >
                                  Card6 rich text
                                </span>
                              </p>
                            </div>
                          </div>
                          <div
                            class="emotion-13"
                          >
                            <a
                              class="emotion-14"
                              data-testid="composable-btn-br"
                              href="www.c.com"
                              role="button"
                            >
                              SHOP WOMEN
                            </a>
                            <a
                              class="emotion-14"
                              data-testid="composable-btn-br"
                              href="www.c.com"
                              role="button"
                            >
                              SHOP MEN
                            </a>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div
                      aria-hidden="true"
                      class="slick-slide"
                      data-index="6"
                      style="outline: none; width: 0px;"
                      tabindex="-1"
                    >
                      <div>
                        <div
                          class="emotion-8"
                        >
                          <a
                            aria-label="card7 link wrapper"
                            class="emotion-9"
                            href="www.c.com"
                          />
                          <div
                            class="emotion-10"
                            data-testid="product-card-image"
                          >
                            <img
                              alt="card7 image"
                              class="emotion-11"
                              src=""
                            />
                          </div>
                          <div
                            class="emotion-12"
                          >
                            <div>
                              <p
                                class="amp-cms--p"
                                style="text-align:left;"
                              >
                                <span
                                  class="amp-cms--body-2"
                                >
                                  Card7 rich text
                                </span>
                              </p>
                            </div>
                          </div>
                          <div
                            class="emotion-13"
                          >
                            <a
                              class="emotion-14"
                              data-testid="composable-btn-br"
                              href="www.c.com"
                              role="button"
                            >
                              SHOP WOMEN
                            </a>
                            <a
                              class="emotion-14"
                              data-testid="composable-btn-br"
                              href="www.c.com"
                              role="button"
                            >
                              SHOP MEN
                            </a>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div
                      aria-hidden="true"
                      class="slick-slide"
                      data-index="7"
                      style="outline: none; width: 0px;"
                      tabindex="-1"
                    >
                      <div>
                        <div
                          class="emotion-8"
                        >
                          <a
                            aria-label="card8 link wrapper"
                            class="emotion-9"
                            href="www.c.com"
                          />
                          <div
                            class="emotion-10"
                            data-testid="product-card-image"
                          >
                            <img
                              alt="card8 image"
                              class="emotion-11"
                              src=""
                            />
                          </div>
                          <div
                            class="emotion-12"
                          >
                            <div>
                              <p
                                class="amp-cms--p"
                                style="text-align:left;"
                              >
                                <span
                                  class="amp-cms--body-2"
                                >
                                  Card8 rich text
                                </span>
                              </p>
                            </div>
                          </div>
                          <div
                            class="emotion-13"
                          >
                            <a
                              class="emotion-14"
                              data-testid="composable-btn-br"
                              href="www.c.com"
                              role="button"
                            >
                              SHOP WOMEN
                            </a>
                            <a
                              class="emotion-14"
                              data-testid="composable-btn-br"
                              href="www.c.com"
                              role="button"
                            >
                              SHOP MEN
                            </a>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div
                      aria-hidden="true"
                      class="slick-slide"
                      data-index="8"
                      style="outline: none; width: 0px;"
                      tabindex="-1"
                    >
                      <div>
                        <div
                          class="emotion-8"
                        >
                          <a
                            aria-label="card9 link wrapper"
                            class="emotion-9"
                            href="www.c.com"
                          />
                          <div
                            class="emotion-10"
                            data-testid="product-card-image"
                          >
                            <img
                              alt="card9 image"
                              class="emotion-11"
                              src=""
                            />
                          </div>
                          <div
                            class="emotion-12"
                          >
                            <div>
                              <p
                                class="amp-cms--p"
                                style="text-align:left;"
                              >
                                <span
                                  class="amp-cms--body-2"
                                >
                                  Card9 rich text
                                </span>
                              </p>
                            </div>
                          </div>
                          <div
                            class="emotion-13"
                          >
                            <a
                              class="emotion-14"
                              data-testid="composable-btn-br"
                              href="www.c.com"
                              role="button"
                            >
                              SHOP WOMEN
                            </a>
                            <a
                              class="emotion-14"
                              data-testid="composable-btn-br"
                              href="www.c.com"
                              role="button"
                            >
                              SHOP MEN
                            </a>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div
                      aria-hidden="true"
                      class="slick-slide"
                      data-index="9"
                      style="outline: none; width: 0px;"
                      tabindex="-1"
                    >
                      <div>
                        <div
                          class="emotion-8"
                        >
                          <a
                            aria-label="card10 link wrapper"
                            class="emotion-9"
                            href="www.c.com"
                          />
                          <div
                            class="emotion-10"
                            data-testid="product-card-image"
                          >
                            <img
                              alt="card10 image"
                              class="emotion-11"
                              src=""
                            />
                          </div>
                          <div
                            class="emotion-12"
                          >
                            <div>
                              <p
                                class="amp-cms--p"
                                style="text-align:left;"
                              >
                                <span
                                  class="amp-cms--body-2"
                                >
                                  Card10 rich text
                                </span>
                              </p>
                            </div>
                          </div>
                          <div
                            class="emotion-13"
                          >
                            <a
                              class="emotion-14"
                              data-testid="composable-btn-br"
                              href="www.c.com"
                              role="button"
                            >
                              SHOP WOMEN
                            </a>
                            <a
                              class="emotion-14"
                              data-testid="composable-btn-br"
                              href="www.c.com"
                              role="button"
                            >
                              SHOP MEN
                            </a>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <button
                  class="slick-arrow slick-next"
                  currentslide="0"
                  data-role="none"
                  slidecount="10"
                  style="display: block;"
                >
                  <span
                    aria-hidden="true"
                    class="emotion-7"
                  >
                    <svg
                      fill="none"
                      viewBox="0 0 41 40"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <g
                        opacity="0.8"
                      >
                        <rect
                          fill="#FFFFFF"
                          height="40"
                          rx="20"
                          width="40"
                          x="0.5"
                        />
                        <path
                          d="M14.5 20.35C14.3067 20.35 14.15 20.1933 14.15 20C14.15 19.8067 14.3067 19.65 14.5 19.65V20.35ZM26.7475 19.7525C26.8842 19.8892 26.8842 20.1108 26.7475 20.2475L24.5201 22.4749C24.3834 22.6116 24.1618 22.6116 24.0251 22.4749C23.8884 22.3382 23.8884 22.1166 24.0251 21.9799L26.005 20L24.0251 18.0201C23.8884 17.8834 23.8884 17.6618 24.0251 17.5251C24.1618 17.3884 24.3834 17.3884 24.5201 17.5251L26.7475 19.7525ZM14.5 19.65L26.5 19.65V20.35L14.5 20.35V19.65Z"
                          fill="#000000"
                        />
                      </g>
                    </svg>
                  </span>
                </button>
                <div
                  class="slick-dots emotion-89"
                  data-alignment="center"
                >
                  <ul
                    class="br-spotlight-carousel-pagination-wrapper"
                  >
                    <li
                      class="slick-active"
                    >
                      <button
                        brand="br"
                        class="br-spotlight-carousel-button"
                      >
                        <div
                          class="br-spotlight-carousel-pagination-line"
                        >
                          <div
                            class="br-spotlight-carousel-pagination-progress"
                          />
                        </div>
                      </button>
                    </li>
                    <li
                      class=""
                    >
                      <button
                        brand="br"
                        class="br-spotlight-carousel-button"
                      >
                        <div
                          class="br-spotlight-carousel-pagination-line"
                        >
                          <div
                            class="br-spotlight-carousel-pagination-progress"
                          />
                        </div>
                      </button>
                    </li>
                    <li
                      class=""
                    >
                      <button
                        brand="br"
                        class="br-spotlight-carousel-button"
                      >
                        <div
                          class="br-spotlight-carousel-pagination-line"
                        >
                          <div
                            class="br-spotlight-carousel-pagination-progress"
                          />
                        </div>
                      </button>
                    </li>
                  </ul>
                </div>
              </div>
            </div>
          </nav>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`BananaRepublic WayfindingAndProductCards on Desktop with three cards responsive: desktopThreeCards 1`] = `
.emotion-0 {
  background: #8A8AFF;
}

.emotion-1 {
  padding: 40px 32px;
}

.emotion-2 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  justify-content: space-between;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  margin-bottom: 64px;
  margin-bottom: 24px;
  display: block;
}

.emotion-3 .amp-cms--p {
  padding: 0;
  margin: 0;
  line-height: 0;
}

.emotion-3 .amp-cms--p>span {
  display: inline;
  white-space: break-spaces;
}

.emotion-3 .amp-cms--p a {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  position: relative;
  z-index: 2;
  pointer-events: auto;
}

.emotion-3 .amp-cms--p a:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-3 .amp-cms--legal-copy {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, 0.8333333333333334vw);
  line-height: 1.5;
  letter-spacing: 0.034722222222222224vw;
  font-weight: 350;
}

.emotion-3 sup {
  vertical-align: top;
  display: inline-block;
  margin-top: -0.25ex;
}

.emotion-3 sub {
  vertical-align: bottom;
  display: inline-block;
  margin-bottom: -0.25ex;
}

.emotion-3 .amp-cms--f-0 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 0.8333333333333334vw;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--f-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 0.8333333333333334vw;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--f-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 0.8333333333333334vw;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--fn-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 0.8333333333333334vw;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--body-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, 1.3888888888888888vw);
  line-height: 1.5;
  letter-spacing: 0.034722222222222224vw;
  font-weight: 350;
}

.emotion-3 .amp-cms--body-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(16px, 1.25vw);
  line-height: 1.4444444444444444;
  letter-spacing: 0.034722222222222224vw;
  font-weight: 350;
}

.emotion-3 .amp-cms--body-3 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, 1.1111111111111112vw);
  line-height: 1.375;
  letter-spacing: 0.034722222222222224vw;
  font-weight: 350;
}

.emotion-3 .amp-cms--body-4 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, 0.9722222222222222vw);
  line-height: 1.4285714285714286;
  letter-spacing: 0.034722222222222224vw;
  font-weight: 350;
}

.emotion-3 .amp-cms--body-5 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, 0.8333333333333334vw);
  line-height: 1.5;
  letter-spacing: 0.034722222222222224vw;
  font-weight: 350;
}

.emotion-3 .amp-cms--eyebrow-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, 1.3888888888888888vw);
  line-height: 1;
  letter-spacing: 0.10416666666666667vw;
  font-weight: 600;
}

.emotion-3 .amp-cms--eyebrow-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, 1.1111111111111112vw);
  line-height: 1;
  letter-spacing: 0.10416666666666667vw;
  font-weight: 600;
}

.emotion-3 .amp-cms--eyebrow-3 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, 0.8333333333333334vw);
  line-height: 1;
  letter-spacing: 0.10416666666666667vw;
  font-weight: 600;
}

.emotion-3 .amp-cms--headline-1 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(48px, 6.666666666666667vw);
  line-height: 1;
  letter-spacing: 0.034722222222222224vw;
  font-weight: 300;
}

.emotion-3 .amp-cms--headline-2 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(40px, 5.555555555555555vw);
  line-height: 1;
  letter-spacing: 0.034722222222222224vw;
  font-weight: 300;
}

.emotion-3 .amp-cms--headline-3 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(32px, 4.444444444444445vw);
  line-height: 1;
  letter-spacing: 0.034722222222222224vw;
  font-weight: 300;
}

.emotion-3 .amp-cms--headline-4 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(28px, 3.3333333333333335vw);
  line-height: 1;
  letter-spacing: 0.034722222222222224vw;
  font-weight: 300;
}

.emotion-3 .amp-cms--headline-5 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, 2.2222222222222223vw);
  line-height: 1.125;
  letter-spacing: 0.034722222222222224vw;
  font-weight: 300;
}

.emotion-3 .amp-cms--headline-6 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, 1.6666666666666667vw);
  line-height: 1.3333333333333333;
  letter-spacing: 0.034722222222222224vw;
  font-weight: 300;
}

.emotion-3 .amp-cms--headline-7 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, 1.3888888888888888vw);
  line-height: 1.4;
  letter-spacing: 0.034722222222222224vw;
  font-weight: 300;
}

.emotion-3 .amp-cms--headlineAlt-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(32px, 4.444444444444445vw);
  line-height: 1;
  letter-spacing: 0.034722222222222224vw;
  font-weight: 400;
}

.emotion-3 .amp-cms--headlineAlt-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, 3.3333333333333335vw);
  line-height: 1;
  letter-spacing: 0.034722222222222224vw;
  font-weight: 400;
}

.emotion-3 .amp-cms--headlineAlt-3 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, 2.2222222222222223vw);
  line-height: 1.0625;
  letter-spacing: 0.034722222222222224vw;
  font-weight: 400;
}

.emotion-3 .amp-cms--headlineAlt-4 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(60px, 8.333333333333332vw);
  line-height: 1;
  letter-spacing: 0.034722222222222224vw;
  font-weight: 300;
}

.emotion-3 .amp-cms--headlineAlt-5 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 1.1111111111111112vw;
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-3 .amp-cms--headlineAlt-6 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 1.1111111111111112vw;
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-3 .amp-cms--headlineAlt-7 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 1.1111111111111112vw;
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-3 .amp-cms--promo-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(8px, 0.6944444444444444vw);
  line-height: 1.6;
  letter-spacing: 0.034722222222222224vw;
  font-weight: 350;
}

.emotion-3 .amp-cms--promo-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, 5.555555555555555vw);
  line-height: 1;
  letter-spacing: 0vw;
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-3 .amp-cms--promoAlt-1 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 1.1111111111111112vw;
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 600;
}

.emotion-3 .amp-cms--subhead-1 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, 1.25vw);
  line-height: 1.3333333333333333;
  letter-spacing: 0.034722222222222224vw;
  font-weight: 300;
}

.emotion-3 .amp-cms--subhead-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(16px, 1.6666666666666667vw);
  line-height: 1;
  letter-spacing: 0.034722222222222224vw;
  font-weight: 350;
}

.emotion-3 .amp-cms--subhead-3 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, 1.3888888888888888vw);
  line-height: 1;
  letter-spacing: 0.034722222222222224vw;
  font-weight: 350;
}

.emotion-4 {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 4px;
}

.emotion-5 {
  width: 100%;
  height: 100%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  position: relative;
  -webkit-flex: 1;
  -ms-flex: 1;
  flex: 1;
  gap: 16px;
}

.emotion-5 p>span {
  line-height: 22px;
  letter-spacing: 0.2px;
}

.emotion-6 {
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  width: 100%;
}

.emotion-7 {
  -webkit-background-size: cover;
  background-size: cover;
  aspect-ratio: 3/4;
  overflow: hidden;
}

.emotion-8 {
  width: 100%;
  aspect-ratio: 3/4;
  object-fit: cover;
}

.emotion-9 .amp-cms--p {
  padding: 0;
  margin: 0;
  line-height: 0;
}

.emotion-9 .amp-cms--p>span {
  display: inline;
  white-space: break-spaces;
}

.emotion-9 .amp-cms--p a {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  position: relative;
  z-index: 2;
  pointer-events: auto;
}

.emotion-9 .amp-cms--p a:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-9 .amp-cms--legal-copy {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.5;
  letter-spacing: 0.5px;
  font-weight: 350;
}

.emotion-9 sup {
  vertical-align: top;
  display: inline-block;
  margin-top: -0.25ex;
}

.emotion-9 sub {
  vertical-align: bottom;
  display: inline-block;
  margin-bottom: -0.25ex;
}

.emotion-9 .amp-cms--f-0 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--f-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--f-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--fn-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--body-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 20px;
  line-height: 1.5;
  letter-spacing: 0.5px;
  font-weight: 350;
}

.emotion-9 .amp-cms--body-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 18px;
  line-height: 1.4444444444444444;
  letter-spacing: 0.5px;
  font-weight: 350;
}

.emotion-9 .amp-cms--body-3 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 16px;
  line-height: 1.375;
  letter-spacing: 0.5px;
  font-weight: 350;
}

.emotion-9 .amp-cms--body-4 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 14px;
  line-height: 1.4285714285714286;
  letter-spacing: 0.5px;
  font-weight: 350;
}

.emotion-9 .amp-cms--body-5 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.5;
  letter-spacing: 0.5px;
  font-weight: 350;
}

.emotion-9 .amp-cms--eyebrow-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 20px;
  line-height: 1;
  letter-spacing: 1.5px;
  font-weight: 600;
}

.emotion-9 .amp-cms--eyebrow-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 16px;
  line-height: 1;
  letter-spacing: 1.5px;
  font-weight: 600;
}

.emotion-9 .amp-cms--eyebrow-3 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1;
  letter-spacing: 1.5px;
  font-weight: 600;
}

.emotion-9 .amp-cms--headline-1 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 96px;
  line-height: 1;
  letter-spacing: 0.5px;
  font-weight: 300;
}

.emotion-9 .amp-cms--headline-2 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 80px;
  line-height: 1;
  letter-spacing: 0.5px;
  font-weight: 300;
}

.emotion-9 .amp-cms--headline-3 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 64px;
  line-height: 1;
  letter-spacing: 0.5px;
  font-weight: 300;
}

.emotion-9 .amp-cms--headline-4 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 48px;
  line-height: 1;
  letter-spacing: 0.5px;
  font-weight: 300;
}

.emotion-9 .amp-cms--headline-5 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 32px;
  line-height: 1.125;
  letter-spacing: 0.5px;
  font-weight: 300;
}

.emotion-9 .amp-cms--headline-6 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 24px;
  line-height: 1.3333333333333333;
  letter-spacing: 0.5px;
  font-weight: 300;
}

.emotion-9 .amp-cms--headline-7 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 20px;
  line-height: 1.4;
  letter-spacing: 0.5px;
  font-weight: 300;
}

.emotion-9 .amp-cms--headlineAlt-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 64px;
  line-height: 1;
  letter-spacing: 0.5px;
  font-weight: 400;
}

.emotion-9 .amp-cms--headlineAlt-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 48px;
  line-height: 1;
  letter-spacing: 0.5px;
  font-weight: 400;
}

.emotion-9 .amp-cms--headlineAlt-3 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 32px;
  line-height: 1.0625;
  letter-spacing: 0.5px;
  font-weight: 400;
}

.emotion-9 .amp-cms--headlineAlt-4 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 120px;
  line-height: 1;
  letter-spacing: 0.5px;
  font-weight: 300;
}

.emotion-9 .amp-cms--headlineAlt-5 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 16px;
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-9 .amp-cms--headlineAlt-6 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 16px;
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-9 .amp-cms--headlineAlt-7 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 16px;
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-9 .amp-cms--promo-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 10px;
  line-height: 1.6;
  letter-spacing: 0.5px;
  font-weight: 350;
}

.emotion-9 .amp-cms--promo-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 80px;
  line-height: 1;
  letter-spacing: 0px;
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-9 .amp-cms--promoAlt-1 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 16px;
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 600;
}

.emotion-9 .amp-cms--subhead-1 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 18px;
  line-height: 1.3333333333333333;
  letter-spacing: 0.5px;
  font-weight: 300;
}

.emotion-9 .amp-cms--subhead-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 24px;
  line-height: 1;
  letter-spacing: 0.5px;
  font-weight: 350;
}

.emotion-9 .amp-cms--subhead-3 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 20px;
  line-height: 1;
  letter-spacing: 0.5px;
  font-weight: 350;
}

.emotion-10 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  -webkit-box-flex-wrap: wrap;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  gap: 8px 16px;
  z-index: 2;
  pointer-events: none;
  padding-bottom: 2px;
}

.emotion-11 {
  background-color: transparent;
  border: 0;
  box-sizing: border-box;
  color: #000000;
  display: inline-block;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  font-size: 12px;
  font-weight: 400;
  height: 16px;
  letter-spacing: 0.5px;
  line-height: 1.33;
  outline: 0;
  padding: 0;
  text-align: center;
  -webkit-text-decoration: underline;
  text-decoration: underline;
  text-decoration-color: #000000;
  text-underline-offset: 5px;
  -webkit-transition: text-decoration-color 200ms;
  transition: text-decoration-color 200ms;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  letter-spacing: 1.3px;
  pointer-events: all;
}

.emotion-11[aria-disabled=true] {
  color: #999999;
  pointer-events: none;
  text-decoration-color: #CCCCCC;
}

.emotion-11:hover:not([aria-disabled=true]) {
  text-decoration-color: #000000;
}

<div>
  <div
    style="font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;"
  >
    <div
      class="emotion-0"
      height="0"
      width="0"
    >
      <div
        class="emotion-1"
      >
        <div
          class="emotion-2"
        >
          <div
            class="emotion-3"
          >
            <div>
              <p
                class="amp-cms--p"
                style="text-align:left;"
              >
                <span
                  class="amp-cms--headline-7"
                >
                  lorem ipsum dolor default text
                </span>
              </p>
            </div>
          </div>
        </div>
        <div
          class="emotion-4"
        >
          <div
            class="emotion-5"
          >
            <a
              aria-label="card1 link wrapper"
              class="emotion-6"
              href="www.gap.com"
            />
            <div
              class="emotion-7"
              data-testid="product-card-image"
            >
              <img
                alt="card1 image"
                class="emotion-8"
                src=""
              />
            </div>
            <div
              class="emotion-9"
            >
              <div>
                <p
                  class="amp-cms--p"
                  style="text-align:left;"
                >
                  <span
                    class="amp-cms--body-2"
                  >
                    Card1 rich text
                  </span>
                </p>
              </div>
            </div>
            <div
              class="emotion-10"
            >
              <a
                class="emotion-11"
                data-testid="composable-btn-br"
                href="www.cta1.com"
                role="button"
              >
                SHOP WOMEN
              </a>
              <a
                class="emotion-11"
                data-testid="composable-btn-br"
                href="www.cta2.com"
                role="button"
              >
                SHOP MEN
              </a>
            </div>
          </div>
          <div
            class="emotion-5"
          >
            <a
              aria-label="card2 link wrapper"
              class="emotion-6"
              href="www.oldnavy.com"
            />
            <div
              class="emotion-7"
              data-testid="product-card-image"
            >
              <img
                alt="card2 image"
                class="emotion-8"
                src=""
              />
            </div>
            <div
              class="emotion-9"
            >
              <div>
                <p
                  class="amp-cms--p"
                  style="text-align:left;"
                >
                  <span
                    class="amp-cms--body-2"
                  >
                    Card2 rich text
                  </span>
                </p>
              </div>
            </div>
            <div
              class="emotion-10"
            >
              <a
                class="emotion-11"
                data-testid="composable-btn-br"
                href="www.cta1.com"
                role="button"
              >
                SHOP WOMEN
              </a>
              <a
                class="emotion-11"
                data-testid="composable-btn-br"
                href="www.cta2.com"
                role="button"
              >
                SHOP MEN
              </a>
            </div>
          </div>
          <div
            class="emotion-5"
          >
            <a
              aria-label="card3 link wrapper"
              class="emotion-6"
              href="www.bananarepublic.com"
            />
            <div
              class="emotion-7"
              data-testid="product-card-image"
            >
              <img
                alt="card3 image"
                class="emotion-8"
                src=""
              />
            </div>
            <div
              class="emotion-9"
            >
              <div>
                <p
                  class="amp-cms--p"
                  style="text-align:left;"
                >
                  <span
                    class="amp-cms--body-2"
                  >
                    Card3 rich text
                  </span>
                </p>
              </div>
            </div>
            <div
              class="emotion-10"
            >
              <a
                class="emotion-11"
                data-testid="composable-btn-br"
                href="www.cta1.com"
                role="button"
              >
                SHOP WOMEN
              </a>
              <a
                class="emotion-11"
                data-testid="composable-btn-br"
                href="www.cta2.com"
                role="button"
              >
                SHOP MEN
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`BananaRepublic WayfindingAndProductCards on Desktop with two cards responsive: desktopTwoCards 1`] = `
.emotion-0 {
  background: #8A8AFF;
}

.emotion-1 {
  padding: 40px 32px;
}

.emotion-2 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  justify-content: space-between;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  margin-bottom: 64px;
  margin-bottom: 24px;
  display: block;
}

.emotion-3 .amp-cms--p {
  padding: 0;
  margin: 0;
  line-height: 0;
}

.emotion-3 .amp-cms--p>span {
  display: inline;
  white-space: break-spaces;
}

.emotion-3 .amp-cms--p a {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  position: relative;
  z-index: 2;
  pointer-events: auto;
}

.emotion-3 .amp-cms--p a:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-3 .amp-cms--legal-copy {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, 0.8333333333333334vw);
  line-height: 1.5;
  letter-spacing: 0.034722222222222224vw;
  font-weight: 350;
}

.emotion-3 sup {
  vertical-align: top;
  display: inline-block;
  margin-top: -0.25ex;
}

.emotion-3 sub {
  vertical-align: bottom;
  display: inline-block;
  margin-bottom: -0.25ex;
}

.emotion-3 .amp-cms--f-0 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 0.8333333333333334vw;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--f-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 0.8333333333333334vw;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--f-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 0.8333333333333334vw;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--fn-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 0.8333333333333334vw;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--body-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, 1.3888888888888888vw);
  line-height: 1.5;
  letter-spacing: 0.034722222222222224vw;
  font-weight: 350;
}

.emotion-3 .amp-cms--body-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(16px, 1.25vw);
  line-height: 1.4444444444444444;
  letter-spacing: 0.034722222222222224vw;
  font-weight: 350;
}

.emotion-3 .amp-cms--body-3 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, 1.1111111111111112vw);
  line-height: 1.375;
  letter-spacing: 0.034722222222222224vw;
  font-weight: 350;
}

.emotion-3 .amp-cms--body-4 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, 0.9722222222222222vw);
  line-height: 1.4285714285714286;
  letter-spacing: 0.034722222222222224vw;
  font-weight: 350;
}

.emotion-3 .amp-cms--body-5 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, 0.8333333333333334vw);
  line-height: 1.5;
  letter-spacing: 0.034722222222222224vw;
  font-weight: 350;
}

.emotion-3 .amp-cms--eyebrow-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, 1.3888888888888888vw);
  line-height: 1;
  letter-spacing: 0.10416666666666667vw;
  font-weight: 600;
}

.emotion-3 .amp-cms--eyebrow-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, 1.1111111111111112vw);
  line-height: 1;
  letter-spacing: 0.10416666666666667vw;
  font-weight: 600;
}

.emotion-3 .amp-cms--eyebrow-3 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, 0.8333333333333334vw);
  line-height: 1;
  letter-spacing: 0.10416666666666667vw;
  font-weight: 600;
}

.emotion-3 .amp-cms--headline-1 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(48px, 6.666666666666667vw);
  line-height: 1;
  letter-spacing: 0.034722222222222224vw;
  font-weight: 300;
}

.emotion-3 .amp-cms--headline-2 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(40px, 5.555555555555555vw);
  line-height: 1;
  letter-spacing: 0.034722222222222224vw;
  font-weight: 300;
}

.emotion-3 .amp-cms--headline-3 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(32px, 4.444444444444445vw);
  line-height: 1;
  letter-spacing: 0.034722222222222224vw;
  font-weight: 300;
}

.emotion-3 .amp-cms--headline-4 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(28px, 3.3333333333333335vw);
  line-height: 1;
  letter-spacing: 0.034722222222222224vw;
  font-weight: 300;
}

.emotion-3 .amp-cms--headline-5 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, 2.2222222222222223vw);
  line-height: 1.125;
  letter-spacing: 0.034722222222222224vw;
  font-weight: 300;
}

.emotion-3 .amp-cms--headline-6 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, 1.6666666666666667vw);
  line-height: 1.3333333333333333;
  letter-spacing: 0.034722222222222224vw;
  font-weight: 300;
}

.emotion-3 .amp-cms--headline-7 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, 1.3888888888888888vw);
  line-height: 1.4;
  letter-spacing: 0.034722222222222224vw;
  font-weight: 300;
}

.emotion-3 .amp-cms--headlineAlt-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(32px, 4.444444444444445vw);
  line-height: 1;
  letter-spacing: 0.034722222222222224vw;
  font-weight: 400;
}

.emotion-3 .amp-cms--headlineAlt-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, 3.3333333333333335vw);
  line-height: 1;
  letter-spacing: 0.034722222222222224vw;
  font-weight: 400;
}

.emotion-3 .amp-cms--headlineAlt-3 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, 2.2222222222222223vw);
  line-height: 1.0625;
  letter-spacing: 0.034722222222222224vw;
  font-weight: 400;
}

.emotion-3 .amp-cms--headlineAlt-4 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(60px, 8.333333333333332vw);
  line-height: 1;
  letter-spacing: 0.034722222222222224vw;
  font-weight: 300;
}

.emotion-3 .amp-cms--headlineAlt-5 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 1.1111111111111112vw;
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-3 .amp-cms--headlineAlt-6 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 1.1111111111111112vw;
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-3 .amp-cms--headlineAlt-7 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 1.1111111111111112vw;
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-3 .amp-cms--promo-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(8px, 0.6944444444444444vw);
  line-height: 1.6;
  letter-spacing: 0.034722222222222224vw;
  font-weight: 350;
}

.emotion-3 .amp-cms--promo-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, 5.555555555555555vw);
  line-height: 1;
  letter-spacing: 0vw;
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-3 .amp-cms--promoAlt-1 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 1.1111111111111112vw;
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 600;
}

.emotion-3 .amp-cms--subhead-1 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, 1.25vw);
  line-height: 1.3333333333333333;
  letter-spacing: 0.034722222222222224vw;
  font-weight: 300;
}

.emotion-3 .amp-cms--subhead-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(16px, 1.6666666666666667vw);
  line-height: 1;
  letter-spacing: 0.034722222222222224vw;
  font-weight: 350;
}

.emotion-3 .amp-cms--subhead-3 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, 1.3888888888888888vw);
  line-height: 1;
  letter-spacing: 0.034722222222222224vw;
  font-weight: 350;
}

.emotion-4 {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 4px;
}

.emotion-5 {
  width: 100%;
  height: 100%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  position: relative;
  -webkit-flex: 1;
  -ms-flex: 1;
  flex: 1;
  gap: 16px;
}

.emotion-5 p>span {
  line-height: 22px;
  letter-spacing: 0.2px;
}

.emotion-6 {
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  width: 100%;
}

.emotion-7 {
  -webkit-background-size: cover;
  background-size: cover;
  aspect-ratio: 3/4;
  overflow: hidden;
}

.emotion-8 {
  width: 100%;
  aspect-ratio: 3/4;
  object-fit: cover;
}

.emotion-9 .amp-cms--p {
  padding: 0;
  margin: 0;
  line-height: 0;
}

.emotion-9 .amp-cms--p>span {
  display: inline;
  white-space: break-spaces;
}

.emotion-9 .amp-cms--p a {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  position: relative;
  z-index: 2;
  pointer-events: auto;
}

.emotion-9 .amp-cms--p a:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-9 .amp-cms--legal-copy {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.5;
  letter-spacing: 0.5px;
  font-weight: 350;
}

.emotion-9 sup {
  vertical-align: top;
  display: inline-block;
  margin-top: -0.25ex;
}

.emotion-9 sub {
  vertical-align: bottom;
  display: inline-block;
  margin-bottom: -0.25ex;
}

.emotion-9 .amp-cms--f-0 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--f-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--f-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--fn-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--body-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 20px;
  line-height: 1.5;
  letter-spacing: 0.5px;
  font-weight: 350;
}

.emotion-9 .amp-cms--body-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 18px;
  line-height: 1.4444444444444444;
  letter-spacing: 0.5px;
  font-weight: 350;
}

.emotion-9 .amp-cms--body-3 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 16px;
  line-height: 1.375;
  letter-spacing: 0.5px;
  font-weight: 350;
}

.emotion-9 .amp-cms--body-4 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 14px;
  line-height: 1.4285714285714286;
  letter-spacing: 0.5px;
  font-weight: 350;
}

.emotion-9 .amp-cms--body-5 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.5;
  letter-spacing: 0.5px;
  font-weight: 350;
}

.emotion-9 .amp-cms--eyebrow-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 20px;
  line-height: 1;
  letter-spacing: 1.5px;
  font-weight: 600;
}

.emotion-9 .amp-cms--eyebrow-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 16px;
  line-height: 1;
  letter-spacing: 1.5px;
  font-weight: 600;
}

.emotion-9 .amp-cms--eyebrow-3 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1;
  letter-spacing: 1.5px;
  font-weight: 600;
}

.emotion-9 .amp-cms--headline-1 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 96px;
  line-height: 1;
  letter-spacing: 0.5px;
  font-weight: 300;
}

.emotion-9 .amp-cms--headline-2 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 80px;
  line-height: 1;
  letter-spacing: 0.5px;
  font-weight: 300;
}

.emotion-9 .amp-cms--headline-3 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 64px;
  line-height: 1;
  letter-spacing: 0.5px;
  font-weight: 300;
}

.emotion-9 .amp-cms--headline-4 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 48px;
  line-height: 1;
  letter-spacing: 0.5px;
  font-weight: 300;
}

.emotion-9 .amp-cms--headline-5 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 32px;
  line-height: 1.125;
  letter-spacing: 0.5px;
  font-weight: 300;
}

.emotion-9 .amp-cms--headline-6 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 24px;
  line-height: 1.3333333333333333;
  letter-spacing: 0.5px;
  font-weight: 300;
}

.emotion-9 .amp-cms--headline-7 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 20px;
  line-height: 1.4;
  letter-spacing: 0.5px;
  font-weight: 300;
}

.emotion-9 .amp-cms--headlineAlt-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 64px;
  line-height: 1;
  letter-spacing: 0.5px;
  font-weight: 400;
}

.emotion-9 .amp-cms--headlineAlt-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 48px;
  line-height: 1;
  letter-spacing: 0.5px;
  font-weight: 400;
}

.emotion-9 .amp-cms--headlineAlt-3 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 32px;
  line-height: 1.0625;
  letter-spacing: 0.5px;
  font-weight: 400;
}

.emotion-9 .amp-cms--headlineAlt-4 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 120px;
  line-height: 1;
  letter-spacing: 0.5px;
  font-weight: 300;
}

.emotion-9 .amp-cms--headlineAlt-5 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 16px;
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-9 .amp-cms--headlineAlt-6 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 16px;
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-9 .amp-cms--headlineAlt-7 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 16px;
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-9 .amp-cms--promo-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 10px;
  line-height: 1.6;
  letter-spacing: 0.5px;
  font-weight: 350;
}

.emotion-9 .amp-cms--promo-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 80px;
  line-height: 1;
  letter-spacing: 0px;
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-9 .amp-cms--promoAlt-1 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 16px;
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 600;
}

.emotion-9 .amp-cms--subhead-1 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 18px;
  line-height: 1.3333333333333333;
  letter-spacing: 0.5px;
  font-weight: 300;
}

.emotion-9 .amp-cms--subhead-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 24px;
  line-height: 1;
  letter-spacing: 0.5px;
  font-weight: 350;
}

.emotion-9 .amp-cms--subhead-3 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 20px;
  line-height: 1;
  letter-spacing: 0.5px;
  font-weight: 350;
}

.emotion-10 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  -webkit-box-flex-wrap: wrap;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  gap: 8px 16px;
  z-index: 2;
  pointer-events: none;
  padding-bottom: 2px;
}

.emotion-11 {
  background-color: transparent;
  border: 0;
  box-sizing: border-box;
  color: #000000;
  display: inline-block;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  font-size: 12px;
  font-weight: 400;
  height: 16px;
  letter-spacing: 0.5px;
  line-height: 1.33;
  outline: 0;
  padding: 0;
  text-align: center;
  -webkit-text-decoration: underline;
  text-decoration: underline;
  text-decoration-color: #000000;
  text-underline-offset: 5px;
  -webkit-transition: text-decoration-color 200ms;
  transition: text-decoration-color 200ms;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  letter-spacing: 1.3px;
  pointer-events: all;
}

.emotion-11[aria-disabled=true] {
  color: #999999;
  pointer-events: none;
  text-decoration-color: #CCCCCC;
}

.emotion-11:hover:not([aria-disabled=true]) {
  text-decoration-color: #000000;
}

<div>
  <div
    style="font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;"
  >
    <div
      class="emotion-0"
      height="0"
      width="0"
    >
      <div
        class="emotion-1"
      >
        <div
          class="emotion-2"
        >
          <div
            class="emotion-3"
          >
            <div>
              <p
                class="amp-cms--p"
                style="text-align:left;"
              >
                <span
                  class="amp-cms--headline-7"
                >
                  lorem ipsum dolor default text
                </span>
              </p>
            </div>
          </div>
        </div>
        <div
          class="emotion-4"
        >
          <div
            class="emotion-5"
          >
            <a
              aria-label="card1 link wrapper"
              class="emotion-6"
              href="www.gap.com"
            />
            <div
              class="emotion-7"
              data-testid="product-card-image"
            >
              <img
                alt="card1 image"
                class="emotion-8"
                src=""
              />
            </div>
            <div
              class="emotion-9"
            >
              <div>
                <p
                  class="amp-cms--p"
                  style="text-align:left;"
                >
                  <span
                    class="amp-cms--body-2"
                  >
                    Card1 rich text
                  </span>
                </p>
              </div>
            </div>
            <div
              class="emotion-10"
            >
              <a
                class="emotion-11"
                data-testid="composable-btn-br"
                href="www.cta1.com"
                role="button"
              >
                SHOP WOMEN
              </a>
              <a
                class="emotion-11"
                data-testid="composable-btn-br"
                href="www.cta2.com"
                role="button"
              >
                SHOP MEN
              </a>
            </div>
          </div>
          <div
            class="emotion-5"
          >
            <a
              aria-label="card2 link wrapper"
              class="emotion-6"
              href="www.oldnavy.com"
            />
            <div
              class="emotion-7"
              data-testid="product-card-image"
            >
              <img
                alt="card2 image"
                class="emotion-8"
                src=""
              />
            </div>
            <div
              class="emotion-9"
            >
              <div>
                <p
                  class="amp-cms--p"
                  style="text-align:left;"
                >
                  <span
                    class="amp-cms--body-2"
                  >
                    Card2 rich text
                  </span>
                </p>
              </div>
            </div>
            <div
              class="emotion-10"
            >
              <a
                class="emotion-11"
                data-testid="composable-btn-br"
                href="www.cta1.com"
                role="button"
              >
                SHOP WOMEN
              </a>
              <a
                class="emotion-11"
                data-testid="composable-btn-br"
                href="www.cta2.com"
                role="button"
              >
                SHOP MEN
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`BananaRepublic WayfindingAndProductCards on Mobile exposed : mobileExposed 1`] = `
.emotion-0 {
  background: #75E6DA;
}

.emotion-1 {
  padding: 24px 16px;
}

.emotion-2 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  justify-content: space-between;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  margin-bottom: 40px;
  margin-bottom: 24px;
  display: block;
}

.emotion-3 .amp-cms--p {
  padding: 0;
  margin: 0;
  line-height: 0;
}

.emotion-3 .amp-cms--p>span {
  display: inline;
  white-space: break-spaces;
}

.emotion-3 .amp-cms--p a {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  position: relative;
  z-index: 2;
  pointer-events: auto;
}

.emotion-3 .amp-cms--p a:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-3 .amp-cms--legal-copy {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(8px, 2.666666666666667vw);
  line-height: 1.8;
  letter-spacing: 0.13333333333333333vw;
  font-weight: 350;
}

.emotion-3 sup {
  vertical-align: top;
  display: inline-block;
  margin-top: -0.25ex;
}

.emotion-3 sub {
  vertical-align: bottom;
  display: inline-block;
  margin-bottom: -0.25ex;
}

.emotion-3 .amp-cms--f-0 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 2.933333333333333vw;
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--f-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 2.933333333333333vw;
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--f-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 2.933333333333333vw;
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--fn-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 2.933333333333333vw;
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--body-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, 3.2vw);
  line-height: 1.5;
  letter-spacing: 0.13333333333333333vw;
  font-weight: 350;
}

.emotion-3 .amp-cms--body-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, 4.266666666666667vw);
  line-height: 1.625;
  letter-spacing: 0.13333333333333333vw;
  font-weight: 350;
}

.emotion-3 .amp-cms--body-3 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, 3.733333333333334vw);
  line-height: 1.5714285714285714;
  letter-spacing: 0.13333333333333333vw;
  font-weight: 350;
}

.emotion-3 .amp-cms--body-4 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, 3.2vw);
  line-height: 1.6666666666666667;
  letter-spacing: 0.13333333333333333vw;
  font-weight: 350;
}

.emotion-3 .amp-cms--body-5 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(8px, 2.666666666666667vw);
  line-height: 1.8;
  letter-spacing: 0.13333333333333333vw;
  font-weight: 350;
}

.emotion-3 .amp-cms--eyebrow-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, 3.733333333333334vw);
  line-height: 1;
  letter-spacing: 0.4vw;
  font-weight: 600;
}

.emotion-3 .amp-cms--eyebrow-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(11px, 3.2vw);
  line-height: 1;
  letter-spacing: 0.4vw;
  font-weight: 600;
}

.emotion-3 .amp-cms--eyebrow-3 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, 2.666666666666667vw);
  line-height: 1;
  letter-spacing: 0.4vw;
  font-weight: 600;
}

.emotion-3 .amp-cms--headline-1 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, 12.8vw);
  line-height: 1;
  letter-spacing: 0.13333333333333333vw;
  font-weight: 300;
}

.emotion-3 .amp-cms--headline-2 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, 10.666666666666668vw);
  line-height: 1;
  letter-spacing: 0.13333333333333333vw;
  font-weight: 300;
}

.emotion-3 .amp-cms--headline-3 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, 8.533333333333333vw);
  line-height: 1.0625;
  letter-spacing: 0.13333333333333333vw;
  font-weight: 300;
}

.emotion-3 .amp-cms--headline-4 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, 7.466666666666668vw);
  line-height: 1.0714285714285714;
  letter-spacing: 0.13333333333333333vw;
  font-weight: 300;
}

.emotion-3 .amp-cms--headline-5 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, 6.4vw);
  line-height: 1.0833333333333333;
  letter-spacing: 0.13333333333333333vw;
  font-weight: 300;
}

.emotion-3 .amp-cms--headline-6 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, 4.8vw);
  line-height: 1.1111111111111112;
  letter-spacing: 0.13333333333333333vw;
  font-weight: 300;
}

.emotion-3 .amp-cms--headline-7 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, 3.733333333333334vw);
  line-height: 1.1428571428571428;
  letter-spacing: 0.13333333333333333vw;
  font-weight: 400;
}

.emotion-3 .amp-cms--headlineAlt-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, 8.533333333333333vw);
  line-height: 1.0625;
  letter-spacing: 0.13333333333333333vw;
  font-weight: 400;
}

.emotion-3 .amp-cms--headlineAlt-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, 6.4vw);
  line-height: 1.0833333333333333;
  letter-spacing: 0.13333333333333333vw;
  font-weight: 400;
}

.emotion-3 .amp-cms--headlineAlt-3 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, 5.333333333333334vw);
  line-height: 1.1;
  letter-spacing: 0.13333333333333333vw;
  font-weight: 400;
}

.emotion-3 .amp-cms--headlineAlt-4 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, 16vw);
  line-height: 1;
  letter-spacing: 0.13333333333333333vw;
  font-weight: 300;
}

.emotion-3 .amp-cms--headlineAlt-5 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 3.733333333333334vw;
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-3 .amp-cms--headlineAlt-6 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 3.733333333333334vw;
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-3 .amp-cms--headlineAlt-7 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 3.733333333333334vw;
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-3 .amp-cms--promo-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(8px, 2.1333333333333333vw);
  line-height: 1.5;
  letter-spacing: 0.13333333333333333vw;
  font-weight: 400;
}

.emotion-3 .amp-cms--promo-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(13px, 10.666666666666668vw);
  line-height: 1;
  letter-spacing: 0vw;
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-3 .amp-cms--promoAlt-1 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 3.733333333333334vw;
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 600;
}

.emotion-3 .amp-cms--subhead-1 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, 3.733333333333334vw);
  line-height: 1.2857142857142858;
  letter-spacing: 0.13333333333333333vw;
  font-weight: 300;
}

.emotion-3 .amp-cms--subhead-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, 4.266666666666667vw);
  line-height: 1;
  letter-spacing: 0.13333333333333333vw;
  font-weight: 350;
}

.emotion-3 .amp-cms--subhead-3 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, 3.2vw);
  line-height: 1;
  letter-spacing: 0.13333333333333333vw;
  font-weight: 350;
}

.emotion-4 {
  display: grid;
  gap: 20px 4px;
  grid-template-columns: 1fr 1fr;
}

.emotion-5 {
  width: 100%;
  height: 100%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  position: relative;
  -webkit-flex: 1;
  -ms-flex: 1;
  flex: 1;
  gap: 16px;
}

.emotion-5 p>span {
  line-height: 22px;
  letter-spacing: 0.2px;
}

.emotion-5:nth-child(odd):nth-last-child(1) {
  grid-column: span 2;
}

.emotion-6 {
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  width: 100%;
}

.emotion-7 {
  -webkit-background-size: cover;
  background-size: cover;
  aspect-ratio: 3/4;
  overflow: hidden;
}

.emotion-8 {
  width: 100%;
  aspect-ratio: 3/4;
  object-fit: cover;
}

.emotion-9 .amp-cms--p {
  padding: 0;
  margin: 0;
  line-height: 0;
}

.emotion-9 .amp-cms--p>span {
  display: inline;
  white-space: break-spaces;
}

.emotion-9 .amp-cms--p a {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  position: relative;
  z-index: 2;
  pointer-events: auto;
}

.emotion-9 .amp-cms--p a:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-9 .amp-cms--legal-copy {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 10px;
  line-height: 1.8;
  letter-spacing: 0.5px;
  font-weight: 350;
}

.emotion-9 sup {
  vertical-align: top;
  display: inline-block;
  margin-top: -0.25ex;
}

.emotion-9 sub {
  vertical-align: bottom;
  display: inline-block;
  margin-bottom: -0.25ex;
}

.emotion-9 .amp-cms--f-0 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 11px;
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--f-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 11px;
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--f-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 11px;
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--fn-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 11px;
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--body-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.5;
  letter-spacing: 0.5px;
  font-weight: 350;
}

.emotion-9 .amp-cms--body-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 16px;
  line-height: 1.625;
  letter-spacing: 0.5px;
  font-weight: 350;
}

.emotion-9 .amp-cms--body-3 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 14px;
  line-height: 1.5714285714285714;
  letter-spacing: 0.5px;
  font-weight: 350;
}

.emotion-9 .amp-cms--body-4 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.6666666666666667;
  letter-spacing: 0.5px;
  font-weight: 350;
}

.emotion-9 .amp-cms--body-5 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 10px;
  line-height: 1.8;
  letter-spacing: 0.5px;
  font-weight: 350;
}

.emotion-9 .amp-cms--eyebrow-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 14px;
  line-height: 1;
  letter-spacing: 1.5px;
  font-weight: 600;
}

.emotion-9 .amp-cms--eyebrow-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1;
  letter-spacing: 1.5px;
  font-weight: 600;
}

.emotion-9 .amp-cms--eyebrow-3 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 10px;
  line-height: 1;
  letter-spacing: 1.5px;
  font-weight: 600;
}

.emotion-9 .amp-cms--headline-1 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 48px;
  line-height: 1;
  letter-spacing: 0.5px;
  font-weight: 300;
}

.emotion-9 .amp-cms--headline-2 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 40px;
  line-height: 1;
  letter-spacing: 0.5px;
  font-weight: 300;
}

.emotion-9 .amp-cms--headline-3 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 32px;
  line-height: 1.0625;
  letter-spacing: 0.5px;
  font-weight: 300;
}

.emotion-9 .amp-cms--headline-4 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 28px;
  line-height: 1.0714285714285714;
  letter-spacing: 0.5px;
  font-weight: 300;
}

.emotion-9 .amp-cms--headline-5 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 24px;
  line-height: 1.0833333333333333;
  letter-spacing: 0.5px;
  font-weight: 300;
}

.emotion-9 .amp-cms--headline-6 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 18px;
  line-height: 1.1111111111111112;
  letter-spacing: 0.5px;
  font-weight: 300;
}

.emotion-9 .amp-cms--headline-7 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 14px;
  line-height: 1.1428571428571428;
  letter-spacing: 0.5px;
  font-weight: 400;
}

.emotion-9 .amp-cms--headlineAlt-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 32px;
  line-height: 1.0625;
  letter-spacing: 0.5px;
  font-weight: 400;
}

.emotion-9 .amp-cms--headlineAlt-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 24px;
  line-height: 1.0833333333333333;
  letter-spacing: 0.5px;
  font-weight: 400;
}

.emotion-9 .amp-cms--headlineAlt-3 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 20px;
  line-height: 1.1;
  letter-spacing: 0.5px;
  font-weight: 400;
}

.emotion-9 .amp-cms--headlineAlt-4 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 60px;
  line-height: 1;
  letter-spacing: 0.5px;
  font-weight: 300;
}

.emotion-9 .amp-cms--headlineAlt-5 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 14px;
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-9 .amp-cms--headlineAlt-6 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 14px;
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-9 .amp-cms--headlineAlt-7 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 14px;
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-9 .amp-cms--promo-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 8px;
  line-height: 1.5;
  letter-spacing: 0.5px;
  font-weight: 400;
}

.emotion-9 .amp-cms--promo-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 40px;
  line-height: 1;
  letter-spacing: 0px;
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-9 .amp-cms--promoAlt-1 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 14px;
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 600;
}

.emotion-9 .amp-cms--subhead-1 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 14px;
  line-height: 1.2857142857142858;
  letter-spacing: 0.5px;
  font-weight: 300;
}

.emotion-9 .amp-cms--subhead-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 16px;
  line-height: 1;
  letter-spacing: 0.5px;
  font-weight: 350;
}

.emotion-9 .amp-cms--subhead-3 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1;
  letter-spacing: 0.5px;
  font-weight: 350;
}

.emotion-10 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  -webkit-box-flex-wrap: wrap;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  gap: 8px 16px;
  z-index: 2;
  pointer-events: none;
  padding-bottom: 2px;
}

.emotion-11 {
  background-color: transparent;
  border: 0;
  box-sizing: border-box;
  color: #000000;
  display: inline-block;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  font-size: 12px;
  font-weight: 400;
  height: 16px;
  letter-spacing: 0.5px;
  line-height: 1.33;
  outline: 0;
  padding: 0;
  text-align: center;
  -webkit-text-decoration: underline;
  text-decoration: underline;
  text-decoration-color: #000000;
  text-underline-offset: 5px;
  -webkit-transition: text-decoration-color 200ms;
  transition: text-decoration-color 200ms;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  letter-spacing: 1.3px;
  pointer-events: all;
}

.emotion-11[aria-disabled=true] {
  color: #999999;
  pointer-events: none;
  text-decoration-color: #CCCCCC;
}

.emotion-11:hover:not([aria-disabled=true]) {
  text-decoration-color: #000000;
}

<div>
  <div
    style="font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;"
  >
    <div
      class="emotion-0"
      height="0"
      width="0"
    >
      <div
        class="emotion-1"
      >
        <div
          class="emotion-2"
        >
          <div
            class="emotion-3"
          >
            <div>
              <p
                class="amp-cms--p"
                style="text-align:left;"
              >
                <span
                  class="amp-cms--headline-7"
                >
                  lorem ipsum dolor mobile override
                </span>
              </p>
            </div>
          </div>
        </div>
        <div
          class="emotion-4"
        >
          <div
            class="emotion-5"
          >
            <a
              aria-label="card1 link wrapper"
              class="emotion-6"
              href="www.gap.com"
            />
            <div
              class="emotion-7"
              data-testid="product-card-image"
            >
              <img
                alt="card1 image"
                class="emotion-8"
                src=""
              />
            </div>
            <div
              class="emotion-9"
            >
              <div>
                <p
                  class="amp-cms--p"
                  style="text-align:left;"
                >
                  <span
                    class="amp-cms--body-2"
                  >
                    Card1 rich text
                  </span>
                </p>
              </div>
            </div>
            <div
              class="emotion-10"
            >
              <a
                class="emotion-11"
                data-testid="composable-btn-br"
                href="www.cta1.com"
                role="button"
              >
                SHOP WOMEN
              </a>
              <a
                class="emotion-11"
                data-testid="composable-btn-br"
                href="www.cta2.com"
                role="button"
              >
                SHOP MEN
              </a>
            </div>
          </div>
          <div
            class="emotion-5"
          >
            <a
              aria-label="card2 link wrapper"
              class="emotion-6"
              href="www.oldnavy.com"
            />
            <div
              class="emotion-7"
              data-testid="product-card-image"
            >
              <img
                alt="card2 image"
                class="emotion-8"
                src=""
              />
            </div>
            <div
              class="emotion-9"
            >
              <div>
                <p
                  class="amp-cms--p"
                  style="text-align:left;"
                >
                  <span
                    class="amp-cms--body-2"
                  >
                    Card2 rich text
                  </span>
                </p>
              </div>
            </div>
            <div
              class="emotion-10"
            >
              <a
                class="emotion-11"
                data-testid="composable-btn-br"
                href="www.cta1.com"
                role="button"
              >
                SHOP WOMEN
              </a>
              <a
                class="emotion-11"
                data-testid="composable-btn-br"
                href="www.cta2.com"
                role="button"
              >
                SHOP MEN
              </a>
            </div>
          </div>
          <div
            class="emotion-5"
          >
            <a
              aria-label="card3 link wrapper"
              class="emotion-6"
              href="www.bananarepublic.com"
            />
            <div
              class="emotion-7"
              data-testid="product-card-image"
            >
              <img
                alt="card3 image"
                class="emotion-8"
                src=""
              />
            </div>
            <div
              class="emotion-9"
            >
              <div>
                <p
                  class="amp-cms--p"
                  style="text-align:left;"
                >
                  <span
                    class="amp-cms--body-2"
                  >
                    Card3 rich text
                  </span>
                </p>
              </div>
            </div>
            <div
              class="emotion-10"
            >
              <a
                class="emotion-11"
                data-testid="composable-btn-br"
                href="www.cta1.com"
                role="button"
              >
                SHOP WOMEN
              </a>
              <a
                class="emotion-11"
                data-testid="composable-btn-br"
                href="www.cta2.com"
                role="button"
              >
                SHOP MEN
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`BananaRepublic WayfindingAndProductCards on Mobile with carousel settings and pagination: mobile-carousel 1`] = `
.emotion-0 {
  background: #75E6DA;
}

.emotion-1 {
  padding: 24px 16px;
}

.emotion-2 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  justify-content: space-between;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  margin-bottom: 40px;
  margin-bottom: 24px;
  display: block;
}

.emotion-3 .amp-cms--p {
  padding: 0;
  margin: 0;
  line-height: 0;
}

.emotion-3 .amp-cms--p>span {
  display: inline;
  white-space: break-spaces;
}

.emotion-3 .amp-cms--p a {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  position: relative;
  z-index: 2;
  pointer-events: auto;
}

.emotion-3 .amp-cms--p a:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-3 .amp-cms--legal-copy {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(8px, 2.666666666666667vw);
  line-height: 1.8;
  letter-spacing: 0.13333333333333333vw;
  font-weight: 350;
}

.emotion-3 sup {
  vertical-align: top;
  display: inline-block;
  margin-top: -0.25ex;
}

.emotion-3 sub {
  vertical-align: bottom;
  display: inline-block;
  margin-bottom: -0.25ex;
}

.emotion-3 .amp-cms--f-0 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 2.933333333333333vw;
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--f-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 2.933333333333333vw;
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--f-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 2.933333333333333vw;
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--fn-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 2.933333333333333vw;
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--body-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, 3.2vw);
  line-height: 1.5;
  letter-spacing: 0.13333333333333333vw;
  font-weight: 350;
}

.emotion-3 .amp-cms--body-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, 4.266666666666667vw);
  line-height: 1.625;
  letter-spacing: 0.13333333333333333vw;
  font-weight: 350;
}

.emotion-3 .amp-cms--body-3 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, 3.733333333333334vw);
  line-height: 1.5714285714285714;
  letter-spacing: 0.13333333333333333vw;
  font-weight: 350;
}

.emotion-3 .amp-cms--body-4 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, 3.2vw);
  line-height: 1.6666666666666667;
  letter-spacing: 0.13333333333333333vw;
  font-weight: 350;
}

.emotion-3 .amp-cms--body-5 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(8px, 2.666666666666667vw);
  line-height: 1.8;
  letter-spacing: 0.13333333333333333vw;
  font-weight: 350;
}

.emotion-3 .amp-cms--eyebrow-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, 3.733333333333334vw);
  line-height: 1;
  letter-spacing: 0.4vw;
  font-weight: 600;
}

.emotion-3 .amp-cms--eyebrow-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(11px, 3.2vw);
  line-height: 1;
  letter-spacing: 0.4vw;
  font-weight: 600;
}

.emotion-3 .amp-cms--eyebrow-3 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, 2.666666666666667vw);
  line-height: 1;
  letter-spacing: 0.4vw;
  font-weight: 600;
}

.emotion-3 .amp-cms--headline-1 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, 12.8vw);
  line-height: 1;
  letter-spacing: 0.13333333333333333vw;
  font-weight: 300;
}

.emotion-3 .amp-cms--headline-2 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, 10.666666666666668vw);
  line-height: 1;
  letter-spacing: 0.13333333333333333vw;
  font-weight: 300;
}

.emotion-3 .amp-cms--headline-3 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, 8.533333333333333vw);
  line-height: 1.0625;
  letter-spacing: 0.13333333333333333vw;
  font-weight: 300;
}

.emotion-3 .amp-cms--headline-4 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, 7.466666666666668vw);
  line-height: 1.0714285714285714;
  letter-spacing: 0.13333333333333333vw;
  font-weight: 300;
}

.emotion-3 .amp-cms--headline-5 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, 6.4vw);
  line-height: 1.0833333333333333;
  letter-spacing: 0.13333333333333333vw;
  font-weight: 300;
}

.emotion-3 .amp-cms--headline-6 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, 4.8vw);
  line-height: 1.1111111111111112;
  letter-spacing: 0.13333333333333333vw;
  font-weight: 300;
}

.emotion-3 .amp-cms--headline-7 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, 3.733333333333334vw);
  line-height: 1.1428571428571428;
  letter-spacing: 0.13333333333333333vw;
  font-weight: 400;
}

.emotion-3 .amp-cms--headlineAlt-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, 8.533333333333333vw);
  line-height: 1.0625;
  letter-spacing: 0.13333333333333333vw;
  font-weight: 400;
}

.emotion-3 .amp-cms--headlineAlt-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, 6.4vw);
  line-height: 1.0833333333333333;
  letter-spacing: 0.13333333333333333vw;
  font-weight: 400;
}

.emotion-3 .amp-cms--headlineAlt-3 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, 5.333333333333334vw);
  line-height: 1.1;
  letter-spacing: 0.13333333333333333vw;
  font-weight: 400;
}

.emotion-3 .amp-cms--headlineAlt-4 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, 16vw);
  line-height: 1;
  letter-spacing: 0.13333333333333333vw;
  font-weight: 300;
}

.emotion-3 .amp-cms--headlineAlt-5 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 3.733333333333334vw;
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-3 .amp-cms--headlineAlt-6 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 3.733333333333334vw;
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-3 .amp-cms--headlineAlt-7 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 3.733333333333334vw;
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-3 .amp-cms--promo-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(8px, 2.1333333333333333vw);
  line-height: 1.5;
  letter-spacing: 0.13333333333333333vw;
  font-weight: 400;
}

.emotion-3 .amp-cms--promo-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(13px, 10.666666666666668vw);
  line-height: 1;
  letter-spacing: 0vw;
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-3 .amp-cms--promoAlt-1 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 3.733333333333334vw;
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 600;
}

.emotion-3 .amp-cms--subhead-1 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, 3.733333333333334vw);
  line-height: 1.2857142857142858;
  letter-spacing: 0.13333333333333333vw;
  font-weight: 300;
}

.emotion-3 .amp-cms--subhead-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, 4.266666666666667vw);
  line-height: 1;
  letter-spacing: 0.13333333333333333vw;
  font-weight: 350;
}

.emotion-3 .amp-cms--subhead-3 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, 3.2vw);
  line-height: 1;
  letter-spacing: 0.13333333333333333vw;
  font-weight: 350;
}

.emotion-4 {
  display: block;
}

.emotion-4 div.slick-track {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-4 div.slick-list {
  margin-right: 0;
}

.emotion-4 div.slick-slide {
  height: auto;
}

.emotion-4 div.slick-slide>div {
  position: relative;
  margin-right: 0;
  height: 100%;
}

.emotion-4 .slick-slider button.slick-next.slick-arrow.slick-next {
  left: calc(100% - 32px);
  width: 40px;
  height: 40px;
  -webkit-transform: translate(0,-55%) rotate(0);
  -moz-transform: translate(0,-55%) rotate(0);
  -ms-transform: translate(0,-55%) rotate(0);
  transform: translate(0,-55%) rotate(0);
}

.emotion-4 .slick-slider button.slick-prev.slick-arrow.slick-prev {
  left: -8px;
  width: 40px;
  height: 40px;
  -webkit-transform: translate(0,-55%) rotate(-180deg);
  -moz-transform: translate(0,-55%) rotate(-180deg);
  -ms-transform: translate(0,-55%) rotate(-180deg);
  transform: translate(0,-55%) rotate(-180deg);
}

.emotion-4 .slick-slider ul.slick-dots {
  padding-top: 16px;
  margin-top: 24px;
  position: static;
  gap: 16px;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
}

.emotion-4 .slick-slider ul.slick-dots li {
  margin: 0;
  pointer-events: all;
  width: 4px!important;
  height: 4px!important;
}

.emotion-5 {
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-5 .slick-list {
  overflow: hidden;
}

.emotion-5 .slick-track {
  width: -webkit-max-content!important;
  width: -moz-max-content!important;
  width: max-content!important;
}

.emotion-5 button.slick-next.slick-arrow.slick-next,
.emotion-5 button.slick-prev.slick-arrow.slick-prev {
  z-index: 2;
  top: 0px;
}

.emotion-5 .slick-slide {
  margin: 0;
  width: auto;
}

.emotion-5 .slick-disabled {
  display: none!important;
}

.emotion-5 .slick-next {
  left: calc(100% - 44px);
}

.emotion-5 .slick-slide:first-of-type a>div:nth-of-type(2)>div>div:nth-of-type(2) {
  border-left: none;
}

.emotion-5 .slick-slide>div:first-of-type {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-5 .slick-slide>div:first-of-type>div:first-of-type {
  width: 100%;
}

.emotion-5 .product-cards-carousel .slick-list {
  overflow: auto;
  overflow-x: clip;
  overflow-y: visible;
  -webkit-transform: none;
  -moz-transform: none;
  -ms-transform: none;
  transform: none;
}

.emotion-6 {
  position: relative;
}

.emotion-6 .slick-slider {
  position: relative;
  display: block;
  box-sizing: border-box;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  -webkit-touch-callout: none;
  touch-action: pan-y;
  -webkit-tap-highlight-color: transparent;
  scroll-behavior: smooth;
}

.emotion-6 .slick-slider .slick-track,
.emotion-6 .slick-slider .slick-list {
  -webkit-transform: translate3d(0, 0, 0);
  -moz-transform: translate3d(0, 0, 0);
  -ms-transform: translate3d(0, 0, 0);
  transform: translate3d(0, 0, 0);
}

.emotion-6 .slick-list {
  position: relative;
  display: block;
  overflow-x: hidden;
  overflow-y: hidden;
  margin: 0;
  padding: 0;
}

.emotion-6 .slick-list:focus {
  outline: none;
}

.emotion-6 .slick-list.dragging {
  cursor: pointer;
  cursor: hand;
}

.emotion-6 .slick-track {
  position: relative;
  top: 0;
  left: 0;
  display: block;
  margin-left: auto;
  margin-right: auto;
}

.emotion-6 .slick-track:before,
.emotion-6 .slick-track:after {
  display: table;
  content: "";
}

.emotion-6 .slick-track:after {
  clear: both;
}

.emotion-6 .slick-slide {
  display: none;
  float: left;
  height: 100%;
  min-height: 1px;
}

.emotion-6 .slick-slide img {
  display: block;
}

.emotion-6 .slick-slide.slick-loading img {
  display: none;
}

.emotion-6 .slick-slide.dragging img {
  pointer-events: none;
}

.emotion-6 [dir="rtl"] .slick-slide {
  float: right;
}

.emotion-6 .slick-initialized .slick-slide,
.emotion-6 .slick-vertical .slick-slide {
  display: block;
}

.emotion-6 .slick-vertical .slick-slide {
  border: 1px solid transparent;
  height: auto;
}

.emotion-6 .slick-loading .slick-track,
.emotion-6 .slick-loading .slick-slide {
  visibility: hidden;
}

.emotion-6 .slick-loading .slick-list {
  background: #fff url("./ajax-loader.gif") center center no-repeat;
}

.emotion-6 .slick-arrow.slick-hidden {
  display: none;
}

.emotion-6 .slick-prev,
.emotion-6 .slick-next {
  font-size: 0;
  line-height: 0;
  position: absolute;
  top: 50%;
  display: block;
  padding: 0;
  cursor: pointer;
  color: transparent;
  border: none;
  outline: none;
  background: transparent;
}

.emotion-6 .slick-prev:hover,
.emotion-6 .slick-next:hover,
.emotion-6 .slick-prev:focus,
.emotion-6 .slick-next:focus {
  color: transparent;
  outline: none;
  background: transparent;
  opacity: 1;
}

.emotion-6 .slick-prev.slick-disabled,
.emotion-6 .slick-next.slick-disabled {
  opacity: 0.25;
}

.emotion-6 .slick-prev {
  left: -0;
  -webkit-transform: translate(0, -50%) rotate(90deg);
  -moz-transform: translate(0, -50%) rotate(90deg);
  -ms-transform: translate(0, -50%) rotate(90deg);
  transform: translate(0, -50%) rotate(90deg);
}

.emotion-6 .slick-prev span {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-6 [dir="rtl"] .slick-prev {
  right: -0;
  left: auto;
}

.emotion-6 .slick-next {
  right: -0;
  -webkit-transform: translate(0, -50%) rotate(-90deg);
  -moz-transform: translate(0, -50%) rotate(-90deg);
  -ms-transform: translate(0, -50%) rotate(-90deg);
  transform: translate(0, -50%) rotate(-90deg);
}

.emotion-6 .slick-next span {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-6 [dir="rtl"] .slick-next {
  right: auto;
  left: -0;
}

.emotion-6 .slick-dotted.slick-slider {
  margin-bottom: 30px;
}

.emotion-6 .slick-dots {
  position: absolute;
  bottom: -25px;
  display: block;
  width: 100%;
  padding: 0;
  margin: 0;
  list-style: none;
  text-align: center;
}

.emotion-6 .slick-dots li {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: 20px;
  height: 20px;
  margin: 0 5px;
  padding: 0;
  cursor: pointer;
}

.emotion-6 .slick-dots li.slick-active button:before {
  opacity: 0.75;
  color: black;
}

.emotion-6 .slick-dots li button {
  font-size: 0;
  line-height: 0;
  display: block;
  padding: 5px;
  cursor: pointer;
  color: transparent;
  border: 0;
  outline: none;
  background: transparent;
}

.emotion-6 .slick-dots li button:hover,
.emotion-6 .slick-dots li button:focus {
  outline: none;
}

.emotion-6 .slick-dots li button:hover:before,
.emotion-6 .slick-dots li button:focus:before,
.emotion-6 .slick-dots li button:hover:before,
.emotion-6 .slick-dots li button:focus:before {
  opacity: 1;
}

.emotion-6 .slick-dots li button:before {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 5px;
  height: 5px;
  content: "";
  opacity: 0.25;
  background-color: black;
  border-radius: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-6.pagination-line .slick-dots {
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
}

.emotion-6.pagination-line .slick-dots[data-alignment='left'] {
  left: 15px;
  bottom: 0;
  top: 0;
  width: 3px;
}

.emotion-6.pagination-line .slick-dots[data-alignment='center'] {
  right: 0;
  left: 0;
  height: 2px;
  bottom: 0;
}

.emotion-6 .br-spotlight-carousel-pagination-wrapper {
  -webkit-flex: 0;
  -ms-flex: 0;
  flex: 0;
  text-wrap: nowrap;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  gap: 8px;
}

.emotion-6.pagination-line .slick-dots[data-alignment='left'] .br-spotlight-carousel-pagination-wrapper {
  -webkit-transform: rotate(90deg);
  -moz-transform: rotate(90deg);
  -ms-transform: rotate(90deg);
  transform: rotate(90deg);
}

.emotion-6.pagination-line li {
  width: auto!important;
  height: auto!important;
  cursor: default!important;
}

.emotion-6 .slick-dots li {
  margin: 0!important;
}

.emotion-6 .slick-dots li button {
  padding-left: 0!important;
  padding-right: 0!important;
}

.emotion-6 .br-spotlight-carousel-pagination-line {
  position: relative;
  width: 37px;
  height: 2px;
  padding: 0;
  background-color: #808080;
}

.emotion-6 button.br-spotlight-carousel-button::before {
  content: none;
  display: none;
}

.emotion-6 .slick-active>.br-spotlight-carousel-button>.br-spotlight-carousel-pagination-line {
  background-color: #000;
}

.emotion-7 {
  display: inline-block;
  height: 2rem;
  width: 2rem;
  min-height: 2rem;
  min-width: 2rem;
}

.emotion-7 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-8 {
  width: 100%;
  height: 100%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  position: relative;
  -webkit-flex: 1;
  -ms-flex: 1;
  flex: 1;
  gap: 16px;
}

.emotion-8 p>span {
  line-height: 22px;
  letter-spacing: 0.2px;
}

.emotion-9 {
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  width: 100%;
}

.emotion-10 {
  -webkit-background-size: cover;
  background-size: cover;
  aspect-ratio: 3/4;
  overflow: hidden;
}

.emotion-11 {
  width: 100%;
  aspect-ratio: 3/4;
  object-fit: cover;
}

.emotion-12 .amp-cms--p {
  padding: 0;
  margin: 0;
  line-height: 0;
}

.emotion-12 .amp-cms--p>span {
  display: inline;
  white-space: break-spaces;
}

.emotion-12 .amp-cms--p a {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  position: relative;
  z-index: 2;
  pointer-events: auto;
}

.emotion-12 .amp-cms--p a:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-12 .amp-cms--legal-copy {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 10px;
  line-height: 1.8;
  letter-spacing: 0.5px;
  font-weight: 350;
}

.emotion-12 sup {
  vertical-align: top;
  display: inline-block;
  margin-top: -0.25ex;
}

.emotion-12 sub {
  vertical-align: bottom;
  display: inline-block;
  margin-bottom: -0.25ex;
}

.emotion-12 .amp-cms--f-0 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 11px;
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-12 .amp-cms--f-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 11px;
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-12 .amp-cms--f-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 11px;
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-12 .amp-cms--fn-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 11px;
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-12 .amp-cms--body-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.5;
  letter-spacing: 0.5px;
  font-weight: 350;
}

.emotion-12 .amp-cms--body-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 16px;
  line-height: 1.625;
  letter-spacing: 0.5px;
  font-weight: 350;
}

.emotion-12 .amp-cms--body-3 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 14px;
  line-height: 1.5714285714285714;
  letter-spacing: 0.5px;
  font-weight: 350;
}

.emotion-12 .amp-cms--body-4 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.6666666666666667;
  letter-spacing: 0.5px;
  font-weight: 350;
}

.emotion-12 .amp-cms--body-5 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 10px;
  line-height: 1.8;
  letter-spacing: 0.5px;
  font-weight: 350;
}

.emotion-12 .amp-cms--eyebrow-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 14px;
  line-height: 1;
  letter-spacing: 1.5px;
  font-weight: 600;
}

.emotion-12 .amp-cms--eyebrow-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1;
  letter-spacing: 1.5px;
  font-weight: 600;
}

.emotion-12 .amp-cms--eyebrow-3 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 10px;
  line-height: 1;
  letter-spacing: 1.5px;
  font-weight: 600;
}

.emotion-12 .amp-cms--headline-1 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 48px;
  line-height: 1;
  letter-spacing: 0.5px;
  font-weight: 300;
}

.emotion-12 .amp-cms--headline-2 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 40px;
  line-height: 1;
  letter-spacing: 0.5px;
  font-weight: 300;
}

.emotion-12 .amp-cms--headline-3 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 32px;
  line-height: 1.0625;
  letter-spacing: 0.5px;
  font-weight: 300;
}

.emotion-12 .amp-cms--headline-4 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 28px;
  line-height: 1.0714285714285714;
  letter-spacing: 0.5px;
  font-weight: 300;
}

.emotion-12 .amp-cms--headline-5 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 24px;
  line-height: 1.0833333333333333;
  letter-spacing: 0.5px;
  font-weight: 300;
}

.emotion-12 .amp-cms--headline-6 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 18px;
  line-height: 1.1111111111111112;
  letter-spacing: 0.5px;
  font-weight: 300;
}

.emotion-12 .amp-cms--headline-7 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 14px;
  line-height: 1.1428571428571428;
  letter-spacing: 0.5px;
  font-weight: 400;
}

.emotion-12 .amp-cms--headlineAlt-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 32px;
  line-height: 1.0625;
  letter-spacing: 0.5px;
  font-weight: 400;
}

.emotion-12 .amp-cms--headlineAlt-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 24px;
  line-height: 1.0833333333333333;
  letter-spacing: 0.5px;
  font-weight: 400;
}

.emotion-12 .amp-cms--headlineAlt-3 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 20px;
  line-height: 1.1;
  letter-spacing: 0.5px;
  font-weight: 400;
}

.emotion-12 .amp-cms--headlineAlt-4 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 60px;
  line-height: 1;
  letter-spacing: 0.5px;
  font-weight: 300;
}

.emotion-12 .amp-cms--headlineAlt-5 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 14px;
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-12 .amp-cms--headlineAlt-6 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 14px;
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-12 .amp-cms--headlineAlt-7 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 14px;
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-12 .amp-cms--promo-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 8px;
  line-height: 1.5;
  letter-spacing: 0.5px;
  font-weight: 400;
}

.emotion-12 .amp-cms--promo-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 40px;
  line-height: 1;
  letter-spacing: 0px;
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-12 .amp-cms--promoAlt-1 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 14px;
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 600;
}

.emotion-12 .amp-cms--subhead-1 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 14px;
  line-height: 1.2857142857142858;
  letter-spacing: 0.5px;
  font-weight: 300;
}

.emotion-12 .amp-cms--subhead-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 16px;
  line-height: 1;
  letter-spacing: 0.5px;
  font-weight: 350;
}

.emotion-12 .amp-cms--subhead-3 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1;
  letter-spacing: 0.5px;
  font-weight: 350;
}

.emotion-13 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  -webkit-box-flex-wrap: wrap;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  gap: 8px 16px;
  z-index: 2;
  pointer-events: none;
  padding-bottom: 2px;
}

.emotion-14 {
  background-color: transparent;
  border: 0;
  box-sizing: border-box;
  color: #000000;
  display: inline-block;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  font-size: 12px;
  font-weight: 400;
  height: 16px;
  letter-spacing: 0.5px;
  line-height: 1.33;
  outline: 0;
  padding: 0;
  text-align: center;
  -webkit-text-decoration: underline;
  text-decoration: underline;
  text-decoration-color: #000000;
  text-underline-offset: 5px;
  -webkit-transition: text-decoration-color 200ms;
  transition: text-decoration-color 200ms;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  letter-spacing: 1.3px;
  pointer-events: all;
}

.emotion-14[aria-disabled=true] {
  color: #999999;
  pointer-events: none;
  text-decoration-color: #CCCCCC;
}

.emotion-14:hover:not([aria-disabled=true]) {
  text-decoration-color: #000000;
}

<div>
  <div
    style="font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;"
  >
    <div
      class="emotion-0"
      height="0"
      width="0"
    >
      <div
        class="emotion-1"
      >
        <div
          class="emotion-2"
        >
          <div
            class="emotion-3"
          >
            <div>
              <p
                class="amp-cms--p"
                style="text-align:left;"
              >
                <span
                  class="amp-cms--headline-7"
                >
                  lorem ipsum dolor mobile override
                </span>
              </p>
            </div>
          </div>
        </div>
        <div
          class="emotion-4"
        >
          <nav
            class="emotion-5"
          >
            <div
              class="pagination-line emotion-6"
            >
              <div
                class="slick-slider slick-initialized"
                dir="ltr"
              >
                <button
                  class="slick-arrow slick-prev slick-disabled"
                  currentslide="0"
                  data-role="none"
                  slidecount="10"
                  style="display: block;"
                >
                  <span
                    aria-hidden="true"
                    class="emotion-7"
                  >
                    <svg
                      fill="none"
                      viewBox="0 0 41 40"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <g
                        opacity="0.8"
                      >
                        <rect
                          fill="#FFFFFF"
                          height="40"
                          rx="20"
                          width="40"
                          x="0.5"
                        />
                        <path
                          d="M14.5 20.35C14.3067 20.35 14.15 20.1933 14.15 20C14.15 19.8067 14.3067 19.65 14.5 19.65V20.35ZM26.7475 19.7525C26.8842 19.8892 26.8842 20.1108 26.7475 20.2475L24.5201 22.4749C24.3834 22.6116 24.1618 22.6116 24.0251 22.4749C23.8884 22.3382 23.8884 22.1166 24.0251 21.9799L26.005 20L24.0251 18.0201C23.8884 17.8834 23.8884 17.6618 24.0251 17.5251C24.1618 17.3884 24.3834 17.3884 24.5201 17.5251L26.7475 19.7525ZM14.5 19.65L26.5 19.65V20.35L14.5 20.35V19.65Z"
                          fill="#000000"
                        />
                      </g>
                    </svg>
                  </span>
                </button>
                <div
                  class="slick-list"
                >
                  <div
                    class="slick-track"
                    style="opacity: 1; transform: translate3d(0px, 0px, 0px);"
                  >
                    <div
                      aria-hidden="false"
                      class="slick-slide slick-active slick-current"
                      data-index="0"
                      style="outline: none; width: 0px;"
                      tabindex="-1"
                    >
                      <div>
                        <div
                          class="emotion-8"
                        >
                          <a
                            aria-label="card1 link wrapper"
                            class="emotion-9"
                            href="www.gap.com"
                          />
                          <div
                            class="emotion-10"
                            data-testid="product-card-image"
                          >
                            <img
                              alt="card1 image"
                              class="emotion-11"
                              src=""
                            />
                          </div>
                          <div
                            class="emotion-12"
                          >
                            <div>
                              <p
                                class="amp-cms--p"
                                style="text-align:left;"
                              >
                                <span
                                  class="amp-cms--body-2"
                                >
                                  Card1 rich text
                                </span>
                              </p>
                            </div>
                          </div>
                          <div
                            class="emotion-13"
                          >
                            <a
                              class="emotion-14"
                              data-testid="composable-btn-br"
                              href="www.cta1.com"
                              role="button"
                            >
                              SHOP WOMEN
                            </a>
                            <a
                              class="emotion-14"
                              data-testid="composable-btn-br"
                              href="www.cta2.com"
                              role="button"
                            >
                              SHOP MEN
                            </a>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div
                      aria-hidden="true"
                      class="slick-slide"
                      data-index="1"
                      style="outline: none; width: 0px;"
                      tabindex="-1"
                    >
                      <div>
                        <div
                          class="emotion-8"
                        >
                          <a
                            aria-label="card2 link wrapper"
                            class="emotion-9"
                            href="www.oldnavy.com"
                          />
                          <div
                            class="emotion-10"
                            data-testid="product-card-image"
                          >
                            <img
                              alt="card2 image"
                              class="emotion-11"
                              src=""
                            />
                          </div>
                          <div
                            class="emotion-12"
                          >
                            <div>
                              <p
                                class="amp-cms--p"
                                style="text-align:left;"
                              >
                                <span
                                  class="amp-cms--body-2"
                                >
                                  Card2 rich text
                                </span>
                              </p>
                            </div>
                          </div>
                          <div
                            class="emotion-13"
                          >
                            <a
                              class="emotion-14"
                              data-testid="composable-btn-br"
                              href="www.cta1.com"
                              role="button"
                            >
                              SHOP WOMEN
                            </a>
                            <a
                              class="emotion-14"
                              data-testid="composable-btn-br"
                              href="www.cta2.com"
                              role="button"
                            >
                              SHOP MEN
                            </a>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div
                      aria-hidden="true"
                      class="slick-slide"
                      data-index="2"
                      style="outline: none; width: 0px;"
                      tabindex="-1"
                    >
                      <div>
                        <div
                          class="emotion-8"
                        >
                          <a
                            aria-label="card3 link wrapper"
                            class="emotion-9"
                            href="www.bananarepublic.com"
                          />
                          <div
                            class="emotion-10"
                            data-testid="product-card-image"
                          >
                            <img
                              alt="card3 image"
                              class="emotion-11"
                              src=""
                            />
                          </div>
                          <div
                            class="emotion-12"
                          >
                            <div>
                              <p
                                class="amp-cms--p"
                                style="text-align:left;"
                              >
                                <span
                                  class="amp-cms--body-2"
                                >
                                  Card3 rich text
                                </span>
                              </p>
                            </div>
                          </div>
                          <div
                            class="emotion-13"
                          >
                            <a
                              class="emotion-14"
                              data-testid="composable-btn-br"
                              href="www.cta1.com"
                              role="button"
                            >
                              SHOP WOMEN
                            </a>
                            <a
                              class="emotion-14"
                              data-testid="composable-btn-br"
                              href="www.cta2.com"
                              role="button"
                            >
                              SHOP MEN
                            </a>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div
                      aria-hidden="true"
                      class="slick-slide"
                      data-index="3"
                      style="outline: none; width: 0px;"
                      tabindex="-1"
                    >
                      <div>
                        <div
                          class="emotion-8"
                        >
                          <a
                            aria-label="card4 link wrapper"
                            class="emotion-9"
                            href="www.c.com"
                          />
                          <div
                            class="emotion-10"
                            data-testid="product-card-image"
                          >
                            <img
                              alt="card4 image"
                              class="emotion-11"
                              src=""
                            />
                          </div>
                          <div
                            class="emotion-12"
                          >
                            <div>
                              <p
                                class="amp-cms--p"
                                style="text-align:left;"
                              >
                                <span
                                  class="amp-cms--body-2"
                                >
                                  Card4 rich text
                                </span>
                              </p>
                            </div>
                          </div>
                          <div
                            class="emotion-13"
                          >
                            <a
                              class="emotion-14"
                              data-testid="composable-btn-br"
                              href="www.c.com"
                              role="button"
                            >
                              SHOP WOMEN
                            </a>
                            <a
                              class="emotion-14"
                              data-testid="composable-btn-br"
                              href="www.c.com"
                              role="button"
                            >
                              SHOP MEN
                            </a>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div
                      aria-hidden="true"
                      class="slick-slide"
                      data-index="4"
                      style="outline: none; width: 0px;"
                      tabindex="-1"
                    >
                      <div>
                        <div
                          class="emotion-8"
                        >
                          <a
                            aria-label="card5 link wrapper"
                            class="emotion-9"
                            href="www.c.com"
                          />
                          <div
                            class="emotion-10"
                            data-testid="product-card-image"
                          >
                            <img
                              alt="card5 image"
                              class="emotion-11"
                              src=""
                            />
                          </div>
                          <div
                            class="emotion-12"
                          >
                            <div>
                              <p
                                class="amp-cms--p"
                                style="text-align:left;"
                              >
                                <span
                                  class="amp-cms--body-2"
                                >
                                  Card5 rich text
                                </span>
                              </p>
                            </div>
                          </div>
                          <div
                            class="emotion-13"
                          >
                            <a
                              class="emotion-14"
                              data-testid="composable-btn-br"
                              href="www.c.com"
                              role="button"
                            >
                              SHOP WOMEN
                            </a>
                            <a
                              class="emotion-14"
                              data-testid="composable-btn-br"
                              href="www.c.com"
                              role="button"
                            >
                              SHOP MEN
                            </a>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div
                      aria-hidden="true"
                      class="slick-slide"
                      data-index="5"
                      style="outline: none; width: 0px;"
                      tabindex="-1"
                    >
                      <div>
                        <div
                          class="emotion-8"
                        >
                          <a
                            aria-label="card6 link wrapper"
                            class="emotion-9"
                            href="www.c.com"
                          />
                          <div
                            class="emotion-10"
                            data-testid="product-card-image"
                          >
                            <img
                              alt="card6 image"
                              class="emotion-11"
                              src=""
                            />
                          </div>
                          <div
                            class="emotion-12"
                          >
                            <div>
                              <p
                                class="amp-cms--p"
                                style="text-align:left;"
                              >
                                <span
                                  class="amp-cms--body-2"
                                >
                                  Card6 rich text
                                </span>
                              </p>
                            </div>
                          </div>
                          <div
                            class="emotion-13"
                          >
                            <a
                              class="emotion-14"
                              data-testid="composable-btn-br"
                              href="www.c.com"
                              role="button"
                            >
                              SHOP WOMEN
                            </a>
                            <a
                              class="emotion-14"
                              data-testid="composable-btn-br"
                              href="www.c.com"
                              role="button"
                            >
                              SHOP MEN
                            </a>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div
                      aria-hidden="true"
                      class="slick-slide"
                      data-index="6"
                      style="outline: none; width: 0px;"
                      tabindex="-1"
                    >
                      <div>
                        <div
                          class="emotion-8"
                        >
                          <a
                            aria-label="card7 link wrapper"
                            class="emotion-9"
                            href="www.c.com"
                          />
                          <div
                            class="emotion-10"
                            data-testid="product-card-image"
                          >
                            <img
                              alt="card7 image"
                              class="emotion-11"
                              src=""
                            />
                          </div>
                          <div
                            class="emotion-12"
                          >
                            <div>
                              <p
                                class="amp-cms--p"
                                style="text-align:left;"
                              >
                                <span
                                  class="amp-cms--body-2"
                                >
                                  Card7 rich text
                                </span>
                              </p>
                            </div>
                          </div>
                          <div
                            class="emotion-13"
                          >
                            <a
                              class="emotion-14"
                              data-testid="composable-btn-br"
                              href="www.c.com"
                              role="button"
                            >
                              SHOP WOMEN
                            </a>
                            <a
                              class="emotion-14"
                              data-testid="composable-btn-br"
                              href="www.c.com"
                              role="button"
                            >
                              SHOP MEN
                            </a>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div
                      aria-hidden="true"
                      class="slick-slide"
                      data-index="7"
                      style="outline: none; width: 0px;"
                      tabindex="-1"
                    >
                      <div>
                        <div
                          class="emotion-8"
                        >
                          <a
                            aria-label="card8 link wrapper"
                            class="emotion-9"
                            href="www.c.com"
                          />
                          <div
                            class="emotion-10"
                            data-testid="product-card-image"
                          >
                            <img
                              alt="card8 image"
                              class="emotion-11"
                              src=""
                            />
                          </div>
                          <div
                            class="emotion-12"
                          >
                            <div>
                              <p
                                class="amp-cms--p"
                                style="text-align:left;"
                              >
                                <span
                                  class="amp-cms--body-2"
                                >
                                  Card8 rich text
                                </span>
                              </p>
                            </div>
                          </div>
                          <div
                            class="emotion-13"
                          >
                            <a
                              class="emotion-14"
                              data-testid="composable-btn-br"
                              href="www.c.com"
                              role="button"
                            >
                              SHOP WOMEN
                            </a>
                            <a
                              class="emotion-14"
                              data-testid="composable-btn-br"
                              href="www.c.com"
                              role="button"
                            >
                              SHOP MEN
                            </a>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div
                      aria-hidden="true"
                      class="slick-slide"
                      data-index="8"
                      style="outline: none; width: 0px;"
                      tabindex="-1"
                    >
                      <div>
                        <div
                          class="emotion-8"
                        >
                          <a
                            aria-label="card9 link wrapper"
                            class="emotion-9"
                            href="www.c.com"
                          />
                          <div
                            class="emotion-10"
                            data-testid="product-card-image"
                          >
                            <img
                              alt="card9 image"
                              class="emotion-11"
                              src=""
                            />
                          </div>
                          <div
                            class="emotion-12"
                          >
                            <div>
                              <p
                                class="amp-cms--p"
                                style="text-align:left;"
                              >
                                <span
                                  class="amp-cms--body-2"
                                >
                                  Card9 rich text
                                </span>
                              </p>
                            </div>
                          </div>
                          <div
                            class="emotion-13"
                          >
                            <a
                              class="emotion-14"
                              data-testid="composable-btn-br"
                              href="www.c.com"
                              role="button"
                            >
                              SHOP WOMEN
                            </a>
                            <a
                              class="emotion-14"
                              data-testid="composable-btn-br"
                              href="www.c.com"
                              role="button"
                            >
                              SHOP MEN
                            </a>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div
                      aria-hidden="true"
                      class="slick-slide"
                      data-index="9"
                      style="outline: none; width: 0px;"
                      tabindex="-1"
                    >
                      <div>
                        <div
                          class="emotion-8"
                        >
                          <a
                            aria-label="card10 link wrapper"
                            class="emotion-9"
                            href="www.c.com"
                          />
                          <div
                            class="emotion-10"
                            data-testid="product-card-image"
                          >
                            <img
                              alt="card10 image"
                              class="emotion-11"
                              src=""
                            />
                          </div>
                          <div
                            class="emotion-12"
                          >
                            <div>
                              <p
                                class="amp-cms--p"
                                style="text-align:left;"
                              >
                                <span
                                  class="amp-cms--body-2"
                                >
                                  Card10 rich text
                                </span>
                              </p>
                            </div>
                          </div>
                          <div
                            class="emotion-13"
                          >
                            <a
                              class="emotion-14"
                              data-testid="composable-btn-br"
                              href="www.c.com"
                              role="button"
                            >
                              SHOP WOMEN
                            </a>
                            <a
                              class="emotion-14"
                              data-testid="composable-btn-br"
                              href="www.c.com"
                              role="button"
                            >
                              SHOP MEN
                            </a>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <button
                  class="slick-arrow slick-next"
                  currentslide="0"
                  data-role="none"
                  slidecount="10"
                  style="display: block;"
                >
                  <span
                    aria-hidden="true"
                    class="emotion-7"
                  >
                    <svg
                      fill="none"
                      viewBox="0 0 41 40"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <g
                        opacity="0.8"
                      >
                        <rect
                          fill="#FFFFFF"
                          height="40"
                          rx="20"
                          width="40"
                          x="0.5"
                        />
                        <path
                          d="M14.5 20.35C14.3067 20.35 14.15 20.1933 14.15 20C14.15 19.8067 14.3067 19.65 14.5 19.65V20.35ZM26.7475 19.7525C26.8842 19.8892 26.8842 20.1108 26.7475 20.2475L24.5201 22.4749C24.3834 22.6116 24.1618 22.6116 24.0251 22.4749C23.8884 22.3382 23.8884 22.1166 24.0251 21.9799L26.005 20L24.0251 18.0201C23.8884 17.8834 23.8884 17.6618 24.0251 17.5251C24.1618 17.3884 24.3834 17.3884 24.5201 17.5251L26.7475 19.7525ZM14.5 19.65L26.5 19.65V20.35L14.5 20.35V19.65Z"
                          fill="#000000"
                        />
                      </g>
                    </svg>
                  </span>
                </button>
                <ul
                  class="slick-dots"
                >
                  <li
                    class="slick-active"
                  >
                    <button>
                      1
                    </button>
                  </li>
                  <li
                    class=""
                  >
                    <button>
                      2
                    </button>
                  </li>
                  <li
                    class=""
                  >
                    <button>
                      3
                    </button>
                  </li>
                  <li
                    class=""
                  >
                    <button>
                      4
                    </button>
                  </li>
                  <li
                    class=""
                  >
                    <button>
                      5
                    </button>
                  </li>
                  <li
                    class=""
                  >
                    <button>
                      6
                    </button>
                  </li>
                  <li
                    class=""
                  >
                    <button>
                      7
                    </button>
                  </li>
                  <li
                    class=""
                  >
                    <button>
                      8
                    </button>
                  </li>
                  <li
                    class=""
                  >
                    <button>
                      9
                    </button>
                  </li>
                  <li
                    class=""
                  >
                    <button>
                      10
                    </button>
                  </li>
                </ul>
              </div>
            </div>
          </nav>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`BananaRepublic WayfindingAndProductCards on Mobile with scroll layout: mobile-scroll 1`] = `
.emotion-0 {
  background: #75E6DA;
}

.emotion-1 {
  padding: 24px 16px;
}

.emotion-2 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  justify-content: space-between;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  margin-bottom: 40px;
  margin-bottom: 24px;
  display: block;
}

.emotion-3 .amp-cms--p {
  padding: 0;
  margin: 0;
  line-height: 0;
}

.emotion-3 .amp-cms--p>span {
  display: inline;
  white-space: break-spaces;
}

.emotion-3 .amp-cms--p a {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  position: relative;
  z-index: 2;
  pointer-events: auto;
}

.emotion-3 .amp-cms--p a:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-3 .amp-cms--legal-copy {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(8px, 2.666666666666667vw);
  line-height: 1.8;
  letter-spacing: 0.13333333333333333vw;
  font-weight: 350;
}

.emotion-3 sup {
  vertical-align: top;
  display: inline-block;
  margin-top: -0.25ex;
}

.emotion-3 sub {
  vertical-align: bottom;
  display: inline-block;
  margin-bottom: -0.25ex;
}

.emotion-3 .amp-cms--f-0 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 2.933333333333333vw;
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--f-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 2.933333333333333vw;
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--f-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 2.933333333333333vw;
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--fn-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 2.933333333333333vw;
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--body-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, 3.2vw);
  line-height: 1.5;
  letter-spacing: 0.13333333333333333vw;
  font-weight: 350;
}

.emotion-3 .amp-cms--body-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, 4.266666666666667vw);
  line-height: 1.625;
  letter-spacing: 0.13333333333333333vw;
  font-weight: 350;
}

.emotion-3 .amp-cms--body-3 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, 3.733333333333334vw);
  line-height: 1.5714285714285714;
  letter-spacing: 0.13333333333333333vw;
  font-weight: 350;
}

.emotion-3 .amp-cms--body-4 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, 3.2vw);
  line-height: 1.6666666666666667;
  letter-spacing: 0.13333333333333333vw;
  font-weight: 350;
}

.emotion-3 .amp-cms--body-5 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(8px, 2.666666666666667vw);
  line-height: 1.8;
  letter-spacing: 0.13333333333333333vw;
  font-weight: 350;
}

.emotion-3 .amp-cms--eyebrow-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, 3.733333333333334vw);
  line-height: 1;
  letter-spacing: 0.4vw;
  font-weight: 600;
}

.emotion-3 .amp-cms--eyebrow-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(11px, 3.2vw);
  line-height: 1;
  letter-spacing: 0.4vw;
  font-weight: 600;
}

.emotion-3 .amp-cms--eyebrow-3 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, 2.666666666666667vw);
  line-height: 1;
  letter-spacing: 0.4vw;
  font-weight: 600;
}

.emotion-3 .amp-cms--headline-1 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, 12.8vw);
  line-height: 1;
  letter-spacing: 0.13333333333333333vw;
  font-weight: 300;
}

.emotion-3 .amp-cms--headline-2 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, 10.666666666666668vw);
  line-height: 1;
  letter-spacing: 0.13333333333333333vw;
  font-weight: 300;
}

.emotion-3 .amp-cms--headline-3 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, 8.533333333333333vw);
  line-height: 1.0625;
  letter-spacing: 0.13333333333333333vw;
  font-weight: 300;
}

.emotion-3 .amp-cms--headline-4 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, 7.466666666666668vw);
  line-height: 1.0714285714285714;
  letter-spacing: 0.13333333333333333vw;
  font-weight: 300;
}

.emotion-3 .amp-cms--headline-5 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, 6.4vw);
  line-height: 1.0833333333333333;
  letter-spacing: 0.13333333333333333vw;
  font-weight: 300;
}

.emotion-3 .amp-cms--headline-6 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, 4.8vw);
  line-height: 1.1111111111111112;
  letter-spacing: 0.13333333333333333vw;
  font-weight: 300;
}

.emotion-3 .amp-cms--headline-7 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, 3.733333333333334vw);
  line-height: 1.1428571428571428;
  letter-spacing: 0.13333333333333333vw;
  font-weight: 400;
}

.emotion-3 .amp-cms--headlineAlt-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, 8.533333333333333vw);
  line-height: 1.0625;
  letter-spacing: 0.13333333333333333vw;
  font-weight: 400;
}

.emotion-3 .amp-cms--headlineAlt-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, 6.4vw);
  line-height: 1.0833333333333333;
  letter-spacing: 0.13333333333333333vw;
  font-weight: 400;
}

.emotion-3 .amp-cms--headlineAlt-3 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, 5.333333333333334vw);
  line-height: 1.1;
  letter-spacing: 0.13333333333333333vw;
  font-weight: 400;
}

.emotion-3 .amp-cms--headlineAlt-4 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, 16vw);
  line-height: 1;
  letter-spacing: 0.13333333333333333vw;
  font-weight: 300;
}

.emotion-3 .amp-cms--headlineAlt-5 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 3.733333333333334vw;
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-3 .amp-cms--headlineAlt-6 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 3.733333333333334vw;
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-3 .amp-cms--headlineAlt-7 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 3.733333333333334vw;
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-3 .amp-cms--promo-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(8px, 2.1333333333333333vw);
  line-height: 1.5;
  letter-spacing: 0.13333333333333333vw;
  font-weight: 400;
}

.emotion-3 .amp-cms--promo-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(13px, 10.666666666666668vw);
  line-height: 1;
  letter-spacing: 0vw;
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-3 .amp-cms--promoAlt-1 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 3.733333333333334vw;
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 600;
}

.emotion-3 .amp-cms--subhead-1 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, 3.733333333333334vw);
  line-height: 1.2857142857142858;
  letter-spacing: 0.13333333333333333vw;
  font-weight: 300;
}

.emotion-3 .amp-cms--subhead-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, 4.266666666666667vw);
  line-height: 1;
  letter-spacing: 0.13333333333333333vw;
  font-weight: 350;
}

.emotion-3 .amp-cms--subhead-3 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, 3.2vw);
  line-height: 1;
  letter-spacing: 0.13333333333333333vw;
  font-weight: 350;
}

.emotion-4 {
  display: grid;
  gap: 4px;
  grid-template-columns: repeat(4, 99%);
  overflow-x: scroll;
  scrollbar-width: none;
  margin: 0px -16px;
  padding: 0px 16px;
  -ms-overflow-style: none;
}

.emotion-4::-webkit-scrollbar {
  display: none;
}

.emotion-5 {
  width: 100%;
  height: 100%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  position: relative;
  -webkit-flex: 1;
  -ms-flex: 1;
  flex: 1;
  gap: 16px;
}

.emotion-5 p>span {
  line-height: 22px;
  letter-spacing: 0.2px;
}

.emotion-6 {
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  width: 100%;
}

.emotion-7 {
  -webkit-background-size: cover;
  background-size: cover;
  aspect-ratio: 3/4;
  overflow: hidden;
}

.emotion-8 {
  width: 100%;
  aspect-ratio: 3/4;
  object-fit: cover;
}

.emotion-9 .amp-cms--p {
  padding: 0;
  margin: 0;
  line-height: 0;
}

.emotion-9 .amp-cms--p>span {
  display: inline;
  white-space: break-spaces;
}

.emotion-9 .amp-cms--p a {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  position: relative;
  z-index: 2;
  pointer-events: auto;
}

.emotion-9 .amp-cms--p a:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-9 .amp-cms--legal-copy {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 10px;
  line-height: 1.8;
  letter-spacing: 0.5px;
  font-weight: 350;
}

.emotion-9 sup {
  vertical-align: top;
  display: inline-block;
  margin-top: -0.25ex;
}

.emotion-9 sub {
  vertical-align: bottom;
  display: inline-block;
  margin-bottom: -0.25ex;
}

.emotion-9 .amp-cms--f-0 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 11px;
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--f-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 11px;
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--f-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 11px;
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--fn-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 11px;
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--body-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.5;
  letter-spacing: 0.5px;
  font-weight: 350;
}

.emotion-9 .amp-cms--body-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 16px;
  line-height: 1.625;
  letter-spacing: 0.5px;
  font-weight: 350;
}

.emotion-9 .amp-cms--body-3 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 14px;
  line-height: 1.5714285714285714;
  letter-spacing: 0.5px;
  font-weight: 350;
}

.emotion-9 .amp-cms--body-4 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.6666666666666667;
  letter-spacing: 0.5px;
  font-weight: 350;
}

.emotion-9 .amp-cms--body-5 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 10px;
  line-height: 1.8;
  letter-spacing: 0.5px;
  font-weight: 350;
}

.emotion-9 .amp-cms--eyebrow-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 14px;
  line-height: 1;
  letter-spacing: 1.5px;
  font-weight: 600;
}

.emotion-9 .amp-cms--eyebrow-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1;
  letter-spacing: 1.5px;
  font-weight: 600;
}

.emotion-9 .amp-cms--eyebrow-3 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 10px;
  line-height: 1;
  letter-spacing: 1.5px;
  font-weight: 600;
}

.emotion-9 .amp-cms--headline-1 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 48px;
  line-height: 1;
  letter-spacing: 0.5px;
  font-weight: 300;
}

.emotion-9 .amp-cms--headline-2 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 40px;
  line-height: 1;
  letter-spacing: 0.5px;
  font-weight: 300;
}

.emotion-9 .amp-cms--headline-3 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 32px;
  line-height: 1.0625;
  letter-spacing: 0.5px;
  font-weight: 300;
}

.emotion-9 .amp-cms--headline-4 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 28px;
  line-height: 1.0714285714285714;
  letter-spacing: 0.5px;
  font-weight: 300;
}

.emotion-9 .amp-cms--headline-5 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 24px;
  line-height: 1.0833333333333333;
  letter-spacing: 0.5px;
  font-weight: 300;
}

.emotion-9 .amp-cms--headline-6 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 18px;
  line-height: 1.1111111111111112;
  letter-spacing: 0.5px;
  font-weight: 300;
}

.emotion-9 .amp-cms--headline-7 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 14px;
  line-height: 1.1428571428571428;
  letter-spacing: 0.5px;
  font-weight: 400;
}

.emotion-9 .amp-cms--headlineAlt-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 32px;
  line-height: 1.0625;
  letter-spacing: 0.5px;
  font-weight: 400;
}

.emotion-9 .amp-cms--headlineAlt-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 24px;
  line-height: 1.0833333333333333;
  letter-spacing: 0.5px;
  font-weight: 400;
}

.emotion-9 .amp-cms--headlineAlt-3 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 20px;
  line-height: 1.1;
  letter-spacing: 0.5px;
  font-weight: 400;
}

.emotion-9 .amp-cms--headlineAlt-4 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 60px;
  line-height: 1;
  letter-spacing: 0.5px;
  font-weight: 300;
}

.emotion-9 .amp-cms--headlineAlt-5 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 14px;
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-9 .amp-cms--headlineAlt-6 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 14px;
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-9 .amp-cms--headlineAlt-7 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 14px;
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-9 .amp-cms--promo-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 8px;
  line-height: 1.5;
  letter-spacing: 0.5px;
  font-weight: 400;
}

.emotion-9 .amp-cms--promo-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 40px;
  line-height: 1;
  letter-spacing: 0px;
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-9 .amp-cms--promoAlt-1 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 14px;
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 600;
}

.emotion-9 .amp-cms--subhead-1 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 14px;
  line-height: 1.2857142857142858;
  letter-spacing: 0.5px;
  font-weight: 300;
}

.emotion-9 .amp-cms--subhead-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 16px;
  line-height: 1;
  letter-spacing: 0.5px;
  font-weight: 350;
}

.emotion-9 .amp-cms--subhead-3 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1;
  letter-spacing: 0.5px;
  font-weight: 350;
}

.emotion-10 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  -webkit-box-flex-wrap: wrap;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  gap: 8px 16px;
  z-index: 2;
  pointer-events: none;
  padding-bottom: 2px;
}

.emotion-11 {
  background-color: transparent;
  border: 0;
  box-sizing: border-box;
  color: #000000;
  display: inline-block;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  font-size: 12px;
  font-weight: 400;
  height: 16px;
  letter-spacing: 0.5px;
  line-height: 1.33;
  outline: 0;
  padding: 0;
  text-align: center;
  -webkit-text-decoration: underline;
  text-decoration: underline;
  text-decoration-color: #000000;
  text-underline-offset: 5px;
  -webkit-transition: text-decoration-color 200ms;
  transition: text-decoration-color 200ms;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  letter-spacing: 1.3px;
  pointer-events: all;
}

.emotion-11[aria-disabled=true] {
  color: #999999;
  pointer-events: none;
  text-decoration-color: #CCCCCC;
}

.emotion-11:hover:not([aria-disabled=true]) {
  text-decoration-color: #000000;
}

<div>
  <div
    style="font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;"
  >
    <div
      class="emotion-0"
      height="0"
      width="0"
    >
      <div
        class="emotion-1"
      >
        <div
          class="emotion-2"
        >
          <div
            class="emotion-3"
          >
            <div>
              <p
                class="amp-cms--p"
                style="text-align:left;"
              >
                <span
                  class="amp-cms--headline-7"
                >
                  lorem ipsum dolor mobile override
                </span>
              </p>
            </div>
          </div>
        </div>
        <div
          class="emotion-4"
        >
          <div
            class="emotion-5"
          >
            <a
              aria-label="card1 link wrapper"
              class="emotion-6"
              href="www.gap.com"
            />
            <div
              class="emotion-7"
              data-testid="product-card-image"
            >
              <img
                alt="card1 image"
                class="emotion-8"
                src=""
              />
            </div>
            <div
              class="emotion-9"
            >
              <div>
                <p
                  class="amp-cms--p"
                  style="text-align:left;"
                >
                  <span
                    class="amp-cms--body-2"
                  >
                    Card1 rich text
                  </span>
                </p>
              </div>
            </div>
            <div
              class="emotion-10"
            >
              <a
                class="emotion-11"
                data-testid="composable-btn-br"
                href="www.cta1.com"
                role="button"
              >
                SHOP WOMEN
              </a>
              <a
                class="emotion-11"
                data-testid="composable-btn-br"
                href="www.cta2.com"
                role="button"
              >
                SHOP MEN
              </a>
            </div>
          </div>
          <div
            class="emotion-5"
          >
            <a
              aria-label="card2 link wrapper"
              class="emotion-6"
              href="www.oldnavy.com"
            />
            <div
              class="emotion-7"
              data-testid="product-card-image"
            >
              <img
                alt="card2 image"
                class="emotion-8"
                src=""
              />
            </div>
            <div
              class="emotion-9"
            >
              <div>
                <p
                  class="amp-cms--p"
                  style="text-align:left;"
                >
                  <span
                    class="amp-cms--body-2"
                  >
                    Card2 rich text
                  </span>
                </p>
              </div>
            </div>
            <div
              class="emotion-10"
            >
              <a
                class="emotion-11"
                data-testid="composable-btn-br"
                href="www.cta1.com"
                role="button"
              >
                SHOP WOMEN
              </a>
              <a
                class="emotion-11"
                data-testid="composable-btn-br"
                href="www.cta2.com"
                role="button"
              >
                SHOP MEN
              </a>
            </div>
          </div>
          <div
            class="emotion-5"
          >
            <a
              aria-label="card3 link wrapper"
              class="emotion-6"
              href="www.bananarepublic.com"
            />
            <div
              class="emotion-7"
              data-testid="product-card-image"
            >
              <img
                alt="card3 image"
                class="emotion-8"
                src=""
              />
            </div>
            <div
              class="emotion-9"
            >
              <div>
                <p
                  class="amp-cms--p"
                  style="text-align:left;"
                >
                  <span
                    class="amp-cms--body-2"
                  >
                    Card3 rich text
                  </span>
                </p>
              </div>
            </div>
            <div
              class="emotion-10"
            >
              <a
                class="emotion-11"
                data-testid="composable-btn-br"
                href="www.cta1.com"
                role="button"
              >
                SHOP WOMEN
              </a>
              <a
                class="emotion-11"
                data-testid="composable-btn-br"
                href="www.cta2.com"
                role="button"
              >
                SHOP MEN
              </a>
            </div>
          </div>
          <div
            class="emotion-5"
          >
            <a
              aria-label="card4 link wrapper"
              class="emotion-6"
              href="www.c.com"
            />
            <div
              class="emotion-7"
              data-testid="product-card-image"
            >
              <img
                alt="card4 image"
                class="emotion-8"
                src=""
              />
            </div>
            <div
              class="emotion-9"
            >
              <div>
                <p
                  class="amp-cms--p"
                  style="text-align:left;"
                >
                  <span
                    class="amp-cms--body-2"
                  >
                    Card4 rich text
                  </span>
                </p>
              </div>
            </div>
            <div
              class="emotion-10"
            >
              <a
                class="emotion-11"
                data-testid="composable-btn-br"
                href="www.c.com"
                role="button"
              >
                SHOP WOMEN
              </a>
              <a
                class="emotion-11"
                data-testid="composable-btn-br"
                href="www.c.com"
                role="button"
              >
                SHOP MEN
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;
