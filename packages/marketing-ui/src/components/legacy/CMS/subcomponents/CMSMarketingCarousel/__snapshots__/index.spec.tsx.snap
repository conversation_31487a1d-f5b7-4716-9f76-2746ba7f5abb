// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`CMS Marketing Carousel per brand chevron styling should match snapshots in desktop for Old Navy 1`] = `
.emotion-0 {
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-0 .slick-list {
  overflow: hidden;
}

.emotion-0 .slick-track {
  width: -webkit-max-content!important;
  width: -moz-max-content!important;
  width: max-content!important;
}

.emotion-0 button.slick-next.slick-arrow.slick-next,
.emotion-0 button.slick-prev.slick-arrow.slick-prev {
  z-index: 2;
  top: undefined;
  height: 44px;
  width: 44px;
  background: #ffffff;
  opacity: 75%;
}

.emotion-0 button.slick-next.slick-arrow.slick-next>span,
.emotion-0 button.slick-prev.slick-arrow.slick-prev>span {
  margin: auto;
}

.emotion-0 button.slick-next.slick-arrow.slick-next svg,
.emotion-0 button.slick-prev.slick-arrow.slick-prev svg {
  margin: auto;
  height: 25px;
  width: 14px;
}

.emotion-0 button.slick-next.slick-arrow.slick-next span svg path,
.emotion-0 button.slick-prev.slick-arrow.slick-prev span svg path {
  fill: #003764;
}

.emotion-0 button.slick-next.slick-arrow.slick-next:hover,
.emotion-0 button.slick-prev.slick-arrow.slick-prev:hover {
  opacity: 100%;
}

.emotion-0 button.slick-next.slick-arrow.slick-next:focus,
.emotion-0 button.slick-prev.slick-arrow.slick-prev:focus {
  background: #ffffff;
}

.emotion-0 button.slick-next.slick-arrow.slick-next:focus-visible,
.emotion-0 button.slick-prev.slick-arrow.slick-prev:focus-visible {
  outline: auto;
}

.emotion-0 .slick-slide {
  margin: 0;
  width: auto;
}

.emotion-0 .slick-disabled {
  display: none!important;
}

.emotion-0 .slick-next {
  left: calc(100% - 44px);
}

.emotion-0 .slick-slide:first-of-type a>div:nth-of-type(2)>div>div:nth-of-type(2) {
  border-left: none;
}

.emotion-0 .slick-slide>div:first-of-type {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-0 .slick-slide>div:first-of-type>div:first-of-type {
  width: 100%;
}

.emotion-1 {
  position: relative;
}

.emotion-1 .slick-slider {
  position: relative;
  display: block;
  box-sizing: border-box;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  -webkit-touch-callout: none;
  touch-action: pan-y;
  -webkit-tap-highlight-color: transparent;
  scroll-behavior: smooth;
}

.emotion-1 .slick-slider .slick-track,
.emotion-1 .slick-slider .slick-list {
  -webkit-transform: translate3d(0, 0, 0);
  -moz-transform: translate3d(0, 0, 0);
  -ms-transform: translate3d(0, 0, 0);
  transform: translate3d(0, 0, 0);
}

.emotion-1 .slick-list {
  position: relative;
  display: block;
  overflow-x: hidden;
  overflow-y: hidden;
  margin: 0;
  padding: 0;
}

.emotion-1 .slick-list:focus {
  outline: none;
}

.emotion-1 .slick-list.dragging {
  cursor: pointer;
  cursor: hand;
}

.emotion-1 .slick-track {
  position: relative;
  top: 0;
  left: 0;
  display: block;
  margin-left: auto;
  margin-right: auto;
}

.emotion-1 .slick-track:before,
.emotion-1 .slick-track:after {
  display: table;
  content: "";
}

.emotion-1 .slick-track:after {
  clear: both;
}

.emotion-1 .slick-slide {
  display: none;
  float: left;
  height: 100%;
  min-height: 1px;
}

.emotion-1 .slick-slide img {
  display: block;
}

.emotion-1 .slick-slide.slick-loading img {
  display: none;
}

.emotion-1 .slick-slide.dragging img {
  pointer-events: none;
}

.emotion-1 [dir="rtl"] .slick-slide {
  float: right;
}

.emotion-1 .slick-initialized .slick-slide,
.emotion-1 .slick-vertical .slick-slide {
  display: block;
}

.emotion-1 .slick-vertical .slick-slide {
  border: 1px solid transparent;
  height: auto;
}

.emotion-1 .slick-loading .slick-track,
.emotion-1 .slick-loading .slick-slide {
  visibility: hidden;
}

.emotion-1 .slick-loading .slick-list {
  background: #fff url("./ajax-loader.gif") center center no-repeat;
}

.emotion-1 .slick-arrow.slick-hidden {
  display: none;
}

.emotion-1 .slick-prev,
.emotion-1 .slick-next {
  font-size: 0;
  line-height: 0;
  position: absolute;
  top: 50%;
  display: block;
  padding: 0;
  cursor: pointer;
  color: transparent;
  border: none;
  outline: none;
  background: transparent;
}

.emotion-1 .slick-prev:hover,
.emotion-1 .slick-next:hover,
.emotion-1 .slick-prev:focus,
.emotion-1 .slick-next:focus {
  color: transparent;
  outline: none;
  background: transparent;
  opacity: 1;
}

.emotion-1 .slick-prev.slick-disabled,
.emotion-1 .slick-next.slick-disabled {
  opacity: 0.25;
}

.emotion-1 .slick-prev {
  left: -0;
  -webkit-transform: translate(0, -50%) rotate(90deg);
  -moz-transform: translate(0, -50%) rotate(90deg);
  -ms-transform: translate(0, -50%) rotate(90deg);
  transform: translate(0, -50%) rotate(90deg);
}

.emotion-1 .slick-prev span {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-1 [dir="rtl"] .slick-prev {
  right: -0;
  left: auto;
}

.emotion-1 .slick-next {
  right: -0;
  -webkit-transform: translate(0, -50%) rotate(-90deg);
  -moz-transform: translate(0, -50%) rotate(-90deg);
  -ms-transform: translate(0, -50%) rotate(-90deg);
  transform: translate(0, -50%) rotate(-90deg);
}

.emotion-1 .slick-next span {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-1 [dir="rtl"] .slick-next {
  right: auto;
  left: -0;
}

.emotion-1 .slick-dotted.slick-slider {
  margin-bottom: 30px;
}

.emotion-1 .slick-dots {
  position: absolute;
  bottom: -25px;
  display: block;
  width: 100%;
  padding: 0;
  margin: 0;
  list-style: none;
  text-align: center;
}

.emotion-1 .slick-dots li {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: 20px;
  height: 20px;
  margin: 0 5px;
  padding: 0;
  cursor: pointer;
}

.emotion-1 .slick-dots li.slick-active button:before {
  opacity: 0.75;
  color: black;
}

.emotion-1 .slick-dots li button {
  font-size: 0;
  line-height: 0;
  display: block;
  padding: 5px;
  cursor: pointer;
  color: transparent;
  border: 0;
  outline: none;
  background: transparent;
}

.emotion-1 .slick-dots li button:hover,
.emotion-1 .slick-dots li button:focus {
  outline: none;
}

.emotion-1 .slick-dots li button:hover:before,
.emotion-1 .slick-dots li button:focus:before,
.emotion-1 .slick-dots li button:hover:before,
.emotion-1 .slick-dots li button:focus:before {
  opacity: 1;
}

.emotion-1 .slick-dots li button:before {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 5px;
  height: 5px;
  content: "";
  opacity: 0.25;
  background-color: black;
  border-radius: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-2 {
  display: inline-block;
  height: 2rem;
  width: 2rem;
  min-height: 2rem;
  min-width: 2rem;
}

.emotion-2 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

<div>
  <div
    style="font-family: var(--font-family-font1),sans-serif;"
  >
    <div
      style="font-family: var(--font-family-font1),sans-serif;"
    >
      <nav
        class="emotion-0"
      >
        <div
          class="emotion-1"
        >
          <div
            class="slick-slider slick-initialized"
            dir="ltr"
          >
            <button
              aria-label="Previous"
              class="slick-prev slick-arrow slick-prev slick-disabled"
              data-role="none"
              disabled=""
            >
              <span
                aria-hidden="true"
                class="emotion-2"
              >
                <svg
                  viewBox="0 0 13.29 8.07"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M6.13 7.86a.73.73 0 001.04 0l5.92-5.92a.73.73 0 000-1.03l-.7-.7a.73.73 0 00-1.03 0l-4.71 4.7L1.94.2A.73.73 0 00.9.21l-.69.7a.73.73 0 000 1.03z"
                    fill="#FFFFFF"
                  />
                </svg>
              </span>
            </button>
            <div
              class="slick-list"
            >
              <div
                class="slick-track"
                style="opacity: 1; transform: translate3d(0px, 0px, 0px);"
              >
                <div
                  aria-hidden="false"
                  class="slick-slide slick-active slick-current"
                  data-index="0"
                  style="outline: none; width: 0px;"
                  tabindex="-1"
                >
                  <div>
                    <div>
                      I am a child component
                    </div>
                  </div>
                </div>
                <div
                  aria-hidden="true"
                  class="slick-slide"
                  data-index="1"
                  style="outline: none; width: 0px;"
                  tabindex="-1"
                >
                  <div>
                    <div>
                      I am a child component
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <button
              aria-label="Next"
              class="slick-next slick-arrow slick-next"
              data-role="none"
            >
              <span
                aria-hidden="true"
                class="emotion-2"
              >
                <svg
                  viewBox="0 0 13.29 8.07"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M6.13 7.86a.73.73 0 001.04 0l5.92-5.92a.73.73 0 000-1.03l-.7-.7a.73.73 0 00-1.03 0l-4.71 4.7L1.94.2A.73.73 0 00.9.21l-.69.7a.73.73 0 000 1.03z"
                    fill="#FFFFFF"
                  />
                </svg>
              </span>
            </button>
          </div>
        </div>
      </nav>
    </div>
  </div>
</div>
`;

exports[`CMS Marketing Carousel should match snapshots in desktop for at 1`] = `
<DocumentFragment>
  .emotion-0 {
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-0 .slick-list {
  overflow: hidden;
}

.emotion-0 .slick-track {
  width: -webkit-max-content!important;
  width: -moz-max-content!important;
  width: max-content!important;
}

.emotion-0 button.slick-next.slick-arrow.slick-next,
.emotion-0 button.slick-prev.slick-arrow.slick-prev {
  z-index: 2;
  top: undefined;
  background: #FFFFFF80;
  opacity: 60%;
  height: 44px;
  width: 44px;
}

.emotion-0 button.slick-next.slick-arrow.slick-next span svg path,
.emotion-0 button.slick-prev.slick-arrow.slick-prev span svg path {
  fill: #00000080;
}

.emotion-0 button.slick-next.slick-arrow.slick-next:hover,
.emotion-0 button.slick-prev.slick-arrow.slick-prev:hover {
  background: #FFFFFF80;
}

.emotion-0 button.slick-next.slick-arrow.slick-next:focus,
.emotion-0 button.slick-prev.slick-arrow.slick-prev:focus {
  background: #FFFFFF80;
}

.emotion-0 button.slick-next.slick-arrow.slick-next>span,
.emotion-0 button.slick-prev.slick-arrow.slick-prev>span {
  margin: auto;
}

.emotion-0 button.slick-next.slick-arrow.slick-next svg,
.emotion-0 button.slick-prev.slick-arrow.slick-prev svg {
  margin: auto;
  height: 25px;
  width: 14px;
}

.emotion-0 button.slick-next.slick-arrow.slick-next:focus-visible,
.emotion-0 button.slick-prev.slick-arrow.slick-prev:focus-visible {
  outline: auto;
}

.emotion-0 .slick-slide {
  margin: 0;
  width: auto;
}

.emotion-0 .slick-disabled {
  display: none!important;
}

.emotion-0 .slick-next {
  left: calc(100% - 44px);
}

.emotion-0 .slick-slide:first-of-type a>div:nth-of-type(2)>div>div:nth-of-type(2) {
  border-left: none;
}

.emotion-0 .slick-slide>div:first-of-type {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-0 .slick-slide>div:first-of-type>div:first-of-type {
  width: 100%;
}

.emotion-1 {
  position: relative;
}

.emotion-1 .slick-slider {
  position: relative;
  display: block;
  box-sizing: border-box;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  -webkit-touch-callout: none;
  touch-action: pan-y;
  -webkit-tap-highlight-color: transparent;
  scroll-behavior: smooth;
}

.emotion-1 .slick-slider .slick-track,
.emotion-1 .slick-slider .slick-list {
  -webkit-transform: translate3d(0, 0, 0);
  -moz-transform: translate3d(0, 0, 0);
  -ms-transform: translate3d(0, 0, 0);
  transform: translate3d(0, 0, 0);
}

.emotion-1 .slick-list {
  position: relative;
  display: block;
  overflow-x: hidden;
  overflow-y: hidden;
  margin: 0;
  padding: 0;
}

.emotion-1 .slick-list:focus {
  outline: none;
}

.emotion-1 .slick-list.dragging {
  cursor: pointer;
  cursor: hand;
}

.emotion-1 .slick-track {
  position: relative;
  top: 0;
  left: 0;
  display: block;
  margin-left: auto;
  margin-right: auto;
}

.emotion-1 .slick-track:before,
.emotion-1 .slick-track:after {
  display: table;
  content: "";
}

.emotion-1 .slick-track:after {
  clear: both;
}

.emotion-1 .slick-slide {
  display: none;
  float: left;
  height: 100%;
  min-height: 1px;
}

.emotion-1 .slick-slide img {
  display: block;
}

.emotion-1 .slick-slide.slick-loading img {
  display: none;
}

.emotion-1 .slick-slide.dragging img {
  pointer-events: none;
}

.emotion-1 [dir="rtl"] .slick-slide {
  float: right;
}

.emotion-1 .slick-initialized .slick-slide,
.emotion-1 .slick-vertical .slick-slide {
  display: block;
}

.emotion-1 .slick-vertical .slick-slide {
  border: 1px solid transparent;
  height: auto;
}

.emotion-1 .slick-loading .slick-track,
.emotion-1 .slick-loading .slick-slide {
  visibility: hidden;
}

.emotion-1 .slick-loading .slick-list {
  background: #fff url("./ajax-loader.gif") center center no-repeat;
}

.emotion-1 .slick-arrow.slick-hidden {
  display: none;
}

.emotion-1 .slick-prev,
.emotion-1 .slick-next {
  font-size: 0;
  line-height: 0;
  position: absolute;
  top: 50%;
  display: block;
  padding: 0;
  cursor: pointer;
  color: transparent;
  border: none;
  outline: none;
  background: transparent;
}

.emotion-1 .slick-prev:hover,
.emotion-1 .slick-next:hover,
.emotion-1 .slick-prev:focus,
.emotion-1 .slick-next:focus {
  color: transparent;
  outline: none;
  background: transparent;
  opacity: 1;
}

.emotion-1 .slick-prev.slick-disabled,
.emotion-1 .slick-next.slick-disabled {
  opacity: 0.25;
}

.emotion-1 .slick-prev {
  left: -0;
  -webkit-transform: translate(0, -50%) rotate(90deg);
  -moz-transform: translate(0, -50%) rotate(90deg);
  -ms-transform: translate(0, -50%) rotate(90deg);
  transform: translate(0, -50%) rotate(90deg);
}

.emotion-1 .slick-prev span {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-1 [dir="rtl"] .slick-prev {
  right: -0;
  left: auto;
}

.emotion-1 .slick-next {
  right: -0;
  -webkit-transform: translate(0, -50%) rotate(-90deg);
  -moz-transform: translate(0, -50%) rotate(-90deg);
  -ms-transform: translate(0, -50%) rotate(-90deg);
  transform: translate(0, -50%) rotate(-90deg);
}

.emotion-1 .slick-next span {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-1 [dir="rtl"] .slick-next {
  right: auto;
  left: -0;
}

.emotion-1 .slick-dotted.slick-slider {
  margin-bottom: 30px;
}

.emotion-1 .slick-dots {
  position: absolute;
  bottom: -25px;
  display: block;
  width: 100%;
  padding: 0;
  margin: 0;
  list-style: none;
  text-align: center;
}

.emotion-1 .slick-dots li {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: 20px;
  height: 20px;
  margin: 0 5px;
  padding: 0;
  cursor: pointer;
}

.emotion-1 .slick-dots li.slick-active button:before {
  opacity: 0.75;
  color: black;
}

.emotion-1 .slick-dots li button {
  font-size: 0;
  line-height: 0;
  display: block;
  padding: 5px;
  cursor: pointer;
  color: transparent;
  border: 0;
  outline: none;
  background: transparent;
}

.emotion-1 .slick-dots li button:hover,
.emotion-1 .slick-dots li button:focus {
  outline: none;
}

.emotion-1 .slick-dots li button:hover:before,
.emotion-1 .slick-dots li button:focus:before,
.emotion-1 .slick-dots li button:hover:before,
.emotion-1 .slick-dots li button:focus:before {
  opacity: 1;
}

.emotion-1 .slick-dots li button:before {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 5px;
  height: 5px;
  content: "";
  opacity: 0.25;
  background-color: black;
  border-radius: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-2 {
  position: absolute;
  border: none;
  padding: 0;
  z-index: 1;
  background: transparent;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  margin: 0;
  bottom: 0;
  left: 0;
}

.emotion-2>span {
  display: initial;
}

.emotion-3 {
  width: 44px;
  height: 44px;
}

.emotion-4 {
  display: inline-block;
  height: 44px;
  width: 44px;
  min-height: 44px;
  min-width: 44px;
}

.emotion-4 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-5 {
  display: inline-block;
  height: 2rem;
  width: 2rem;
  min-height: 2rem;
  min-width: 2rem;
}

.emotion-5 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

<div
    style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <nav
        class="emotion-0"
      >
        <div
          class="emotion-1"
        >
          <button
            aria-label="pause"
            class="emotion-2"
          >
            <div
              class="emotion-3"
            >
              <span
                aria-hidden="true"
                class="emotion-4"
              >
                <svg
                  fill="none"
                  viewBox="0 0 61 61"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M61 0H0v61h61V0Z"
                    fill="#FFFFFF"
                    fill-opacity=".6"
                  />
                  <path
                    d="M33.5 25H35v12h-1.5V25ZM25 25h1.5v12H25V25Z"
                    fill="#111"
                  />
                </svg>
              </span>
            </div>
          </button>
          <div
            class="slick-slider slick-initialized"
            dir="ltr"
          >
            <button
              aria-label="Previous"
              class="slick-prev slick-arrow slick-prev"
              data-role="none"
            >
              <span
                aria-hidden="true"
                class="emotion-5"
              >
                <svg
                  viewBox="0 0 26.78 17.63"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M0 1.95l13 15.68L26.79 1.98 24.53 0 13.08 13.01 2.3.03z"
                    fill="#FFFFFF"
                  />
                </svg>
              </span>
            </button>
            <div
              class="slick-list"
            >
              <div
                class="slick-track"
                style="opacity: 1; transform: translate3d(0px, 0px, 0px);"
              >
                <div
                  aria-hidden="true"
                  class="slick-slide slick-cloned"
                  data-index="-1"
                  style="width: 0px;"
                  tabindex="-1"
                >
                  <div>
                    <div>
                      I am a child component
                    </div>
                  </div>
                </div>
                <div
                  aria-hidden="false"
                  class="slick-slide slick-active slick-current"
                  data-index="0"
                  style="outline: none; width: 0px;"
                  tabindex="-1"
                >
                  <div>
                    <div>
                      I am a child component
                    </div>
                  </div>
                </div>
                <div
                  aria-hidden="true"
                  class="slick-slide"
                  data-index="1"
                  style="outline: none; width: 0px;"
                  tabindex="-1"
                >
                  <div>
                    <div>
                      I am a child component
                    </div>
                  </div>
                </div>
                <div
                  aria-hidden="true"
                  class="slick-slide slick-cloned"
                  data-index="2"
                  style="width: 0px;"
                  tabindex="-1"
                >
                  <div>
                    <div>
                      I am a child component
                    </div>
                  </div>
                </div>
                <div
                  aria-hidden="true"
                  class="slick-slide slick-cloned"
                  data-index="3"
                  style="width: 0px;"
                  tabindex="-1"
                >
                  <div>
                    <div>
                      I am a child component
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <button
              aria-label="Next"
              class="slick-next slick-arrow slick-next"
              data-role="none"
            >
              <span
                aria-hidden="true"
                class="emotion-5"
              >
                <svg
                  viewBox="0 0 26.78 17.63"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M0 1.95l13 15.68L26.79 1.98 24.53 0 13.08 13.01 2.3.03z"
                    fill="#FFFFFF"
                  />
                </svg>
              </span>
            </button>
          </div>
        </div>
      </nav>
    </div>
  </div>
</DocumentFragment>
`;

exports[`CMS Marketing Carousel should match snapshots in desktop for gap 1`] = `
<DocumentFragment>
  .emotion-0 {
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-0 .slick-list {
  overflow: hidden;
}

.emotion-0 .slick-track {
  width: -webkit-max-content!important;
  width: -moz-max-content!important;
  width: max-content!important;
}

.emotion-0 button.slick-next.slick-arrow.slick-next,
.emotion-0 button.slick-prev.slick-arrow.slick-prev {
  z-index: 2;
  top: undefined;
  height: 44px;
  width: 44px;
}

.emotion-0 button.slick-next.slick-arrow.slick-next>span,
.emotion-0 button.slick-prev.slick-arrow.slick-prev>span {
  margin: auto;
}

.emotion-0 button.slick-next.slick-arrow.slick-next svg,
.emotion-0 button.slick-prev.slick-arrow.slick-prev svg {
  margin: auto;
  height: 25px;
  width: 14px;
}

.emotion-0 .slick-slide {
  margin: 0;
  width: auto;
}

.emotion-0 .slick-disabled {
  display: none!important;
}

.emotion-0 .slick-next {
  left: calc(100% - 44px);
}

.emotion-0 .slick-slide:first-of-type a>div:nth-of-type(2)>div>div:nth-of-type(2) {
  border-left: none;
}

.emotion-0 .slick-slide>div:first-of-type {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-0 .slick-slide>div:first-of-type>div:first-of-type {
  width: 100%;
}

.emotion-1 {
  position: relative;
}

.emotion-1 .slick-slider {
  position: relative;
  display: block;
  box-sizing: border-box;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  -webkit-touch-callout: none;
  touch-action: pan-y;
  -webkit-tap-highlight-color: transparent;
  scroll-behavior: smooth;
}

.emotion-1 .slick-slider .slick-track,
.emotion-1 .slick-slider .slick-list {
  -webkit-transform: translate3d(0, 0, 0);
  -moz-transform: translate3d(0, 0, 0);
  -ms-transform: translate3d(0, 0, 0);
  transform: translate3d(0, 0, 0);
}

.emotion-1 .slick-list {
  position: relative;
  display: block;
  overflow-x: hidden;
  overflow-y: hidden;
  margin: 0;
  padding: 0;
}

.emotion-1 .slick-list:focus {
  outline: none;
}

.emotion-1 .slick-list.dragging {
  cursor: pointer;
  cursor: hand;
}

.emotion-1 .slick-track {
  position: relative;
  top: 0;
  left: 0;
  display: block;
  margin-left: auto;
  margin-right: auto;
}

.emotion-1 .slick-track:before,
.emotion-1 .slick-track:after {
  display: table;
  content: "";
}

.emotion-1 .slick-track:after {
  clear: both;
}

.emotion-1 .slick-slide {
  display: none;
  float: left;
  height: 100%;
  min-height: 1px;
}

.emotion-1 .slick-slide img {
  display: block;
}

.emotion-1 .slick-slide.slick-loading img {
  display: none;
}

.emotion-1 .slick-slide.dragging img {
  pointer-events: none;
}

.emotion-1 [dir="rtl"] .slick-slide {
  float: right;
}

.emotion-1 .slick-initialized .slick-slide,
.emotion-1 .slick-vertical .slick-slide {
  display: block;
}

.emotion-1 .slick-vertical .slick-slide {
  border: 1px solid transparent;
  height: auto;
}

.emotion-1 .slick-loading .slick-track,
.emotion-1 .slick-loading .slick-slide {
  visibility: hidden;
}

.emotion-1 .slick-loading .slick-list {
  background: #fff url("./ajax-loader.gif") center center no-repeat;
}

.emotion-1 .slick-arrow.slick-hidden {
  display: none;
}

.emotion-1 .slick-prev,
.emotion-1 .slick-next {
  font-size: 0;
  line-height: 0;
  position: absolute;
  top: 50%;
  display: block;
  padding: 0;
  cursor: pointer;
  color: transparent;
  border: none;
  outline: none;
  background: transparent;
}

.emotion-1 .slick-prev:hover,
.emotion-1 .slick-next:hover,
.emotion-1 .slick-prev:focus,
.emotion-1 .slick-next:focus {
  color: transparent;
  outline: none;
  background: transparent;
  opacity: 1;
}

.emotion-1 .slick-prev.slick-disabled,
.emotion-1 .slick-next.slick-disabled {
  opacity: 0.25;
}

.emotion-1 .slick-prev {
  left: -0;
  -webkit-transform: translate(0, -50%) rotate(90deg);
  -moz-transform: translate(0, -50%) rotate(90deg);
  -ms-transform: translate(0, -50%) rotate(90deg);
  transform: translate(0, -50%) rotate(90deg);
}

.emotion-1 .slick-prev span {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-1 [dir="rtl"] .slick-prev {
  right: -0;
  left: auto;
}

.emotion-1 .slick-next {
  right: -0;
  -webkit-transform: translate(0, -50%) rotate(-90deg);
  -moz-transform: translate(0, -50%) rotate(-90deg);
  -ms-transform: translate(0, -50%) rotate(-90deg);
  transform: translate(0, -50%) rotate(-90deg);
}

.emotion-1 .slick-next span {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-1 [dir="rtl"] .slick-next {
  right: auto;
  left: -0;
}

.emotion-1 .slick-dotted.slick-slider {
  margin-bottom: 30px;
}

.emotion-1 .slick-dots {
  position: absolute;
  bottom: -25px;
  display: block;
  width: 100%;
  padding: 0;
  margin: 0;
  list-style: none;
  text-align: center;
}

.emotion-1 .slick-dots li {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: 20px;
  height: 20px;
  margin: 0 5px;
  padding: 0;
  cursor: pointer;
}

.emotion-1 .slick-dots li.slick-active button:before {
  opacity: 0.75;
  color: black;
}

.emotion-1 .slick-dots li button {
  font-size: 0;
  line-height: 0;
  display: block;
  padding: 5px;
  cursor: pointer;
  color: transparent;
  border: 0;
  outline: none;
  background: transparent;
}

.emotion-1 .slick-dots li button:hover,
.emotion-1 .slick-dots li button:focus {
  outline: none;
}

.emotion-1 .slick-dots li button:hover:before,
.emotion-1 .slick-dots li button:focus:before,
.emotion-1 .slick-dots li button:hover:before,
.emotion-1 .slick-dots li button:focus:before {
  opacity: 1;
}

.emotion-1 .slick-dots li button:before {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 5px;
  height: 5px;
  content: "";
  opacity: 0.25;
  background-color: black;
  border-radius: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-2 {
  position: absolute;
  border: none;
  padding: 0;
  z-index: 1;
  background: transparent;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  margin: 0;
  bottom: 0;
  left: 0;
}

.emotion-2>span {
  display: initial;
}

.emotion-3 {
  width: 44px;
  height: 44px;
}

.emotion-4 {
  display: inline-block;
  height: 44px;
  width: 44px;
  min-height: 44px;
  min-width: 44px;
}

.emotion-4 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-5 {
  display: inline-block;
  height: 2rem;
  width: 2rem;
  min-height: 2rem;
  min-width: 2rem;
}

.emotion-5 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <nav
        class="emotion-0"
      >
        <div
          class="emotion-1"
        >
          <button
            aria-label="pause"
            class="emotion-2"
          >
            <div
              class="emotion-3"
            >
              <span
                aria-hidden="true"
                class="emotion-4"
              >
                <svg
                  viewBox="0 0 8.093 18"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <g
                    transform="translate(1.125)"
                  >
                    <path
                      d="M.321,18H-1.125V0H.321Z"
                      fill="#FFFFFF"
                      id="Left"
                      transform="translate(0 0)"
                    />
                    <path
                      d="M.321,17.749H-1.125V0H.321Z"
                      fill="#FFFFFF"
                      id="Right"
                      transform="translate(6.646 0)"
                    />
                  </g>
                </svg>
              </span>
            </div>
          </button>
          <div
            class="slick-slider slick-initialized"
            dir="ltr"
          >
            <button
              aria-label="Previous"
              class="slick-prev slick-arrow slick-prev"
              data-role="none"
            >
              <span
                aria-hidden="true"
                class="emotion-5"
              >
                <svg
                  viewBox="0 0 18 7.742"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M0,.839,6.44,9,0,17.162.818,18,7.743,9.225,7.522,9l.22-.225L.818,0Z"
                    fill="#FFFFFF"
                    transform="translate(18) rotate(90)"
                  />
                </svg>
              </span>
            </button>
            <div
              class="slick-list"
            >
              <div
                class="slick-track"
                style="opacity: 1; transform: translate3d(0px, 0px, 0px);"
              >
                <div
                  aria-hidden="true"
                  class="slick-slide slick-cloned"
                  data-index="-1"
                  style="width: 0px;"
                  tabindex="-1"
                >
                  <div>
                    <div>
                      I am a child component
                    </div>
                  </div>
                </div>
                <div
                  aria-hidden="false"
                  class="slick-slide slick-active slick-current"
                  data-index="0"
                  style="outline: none; width: 0px;"
                  tabindex="-1"
                >
                  <div>
                    <div>
                      I am a child component
                    </div>
                  </div>
                </div>
                <div
                  aria-hidden="true"
                  class="slick-slide"
                  data-index="1"
                  style="outline: none; width: 0px;"
                  tabindex="-1"
                >
                  <div>
                    <div>
                      I am a child component
                    </div>
                  </div>
                </div>
                <div
                  aria-hidden="true"
                  class="slick-slide slick-cloned"
                  data-index="2"
                  style="width: 0px;"
                  tabindex="-1"
                >
                  <div>
                    <div>
                      I am a child component
                    </div>
                  </div>
                </div>
                <div
                  aria-hidden="true"
                  class="slick-slide slick-cloned"
                  data-index="3"
                  style="width: 0px;"
                  tabindex="-1"
                >
                  <div>
                    <div>
                      I am a child component
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <button
              aria-label="Next"
              class="slick-next slick-arrow slick-next"
              data-role="none"
            >
              <span
                aria-hidden="true"
                class="emotion-5"
              >
                <svg
                  viewBox="0 0 18 7.742"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M0,.839,6.44,9,0,17.162.818,18,7.743,9.225,7.522,9l.22-.225L.818,0Z"
                    fill="#FFFFFF"
                    transform="translate(18) rotate(90)"
                  />
                </svg>
              </span>
            </button>
          </div>
        </div>
      </nav>
    </div>
  </div>
</DocumentFragment>
`;

exports[`CMS Marketing Carousel should match snapshots in desktop for on 1`] = `
<DocumentFragment>
  .emotion-0 {
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-0 .slick-list {
  overflow: hidden;
}

.emotion-0 .slick-track {
  width: -webkit-max-content!important;
  width: -moz-max-content!important;
  width: max-content!important;
}

.emotion-0 button.slick-next.slick-arrow.slick-next,
.emotion-0 button.slick-prev.slick-arrow.slick-prev {
  z-index: 2;
  top: undefined;
  height: 44px;
  width: 44px;
  background: #ffffff;
  opacity: 75%;
}

.emotion-0 button.slick-next.slick-arrow.slick-next>span,
.emotion-0 button.slick-prev.slick-arrow.slick-prev>span {
  margin: auto;
}

.emotion-0 button.slick-next.slick-arrow.slick-next svg,
.emotion-0 button.slick-prev.slick-arrow.slick-prev svg {
  margin: auto;
  height: 25px;
  width: 14px;
}

.emotion-0 button.slick-next.slick-arrow.slick-next span svg path,
.emotion-0 button.slick-prev.slick-arrow.slick-prev span svg path {
  fill: #003764;
}

.emotion-0 button.slick-next.slick-arrow.slick-next:hover,
.emotion-0 button.slick-prev.slick-arrow.slick-prev:hover {
  opacity: 100%;
}

.emotion-0 button.slick-next.slick-arrow.slick-next:focus,
.emotion-0 button.slick-prev.slick-arrow.slick-prev:focus {
  background: #ffffff;
}

.emotion-0 button.slick-next.slick-arrow.slick-next:focus-visible,
.emotion-0 button.slick-prev.slick-arrow.slick-prev:focus-visible {
  outline: auto;
}

.emotion-0 .slick-slide {
  margin: 0;
  width: auto;
}

.emotion-0 .slick-disabled {
  display: none!important;
}

.emotion-0 .slick-next {
  left: calc(100% - 44px);
}

.emotion-0 .slick-slide:first-of-type a>div:nth-of-type(2)>div>div:nth-of-type(2) {
  border-left: none;
}

.emotion-0 .slick-slide>div:first-of-type {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-0 .slick-slide>div:first-of-type>div:first-of-type {
  width: 100%;
}

.emotion-1 {
  position: relative;
}

.emotion-1 .slick-slider {
  position: relative;
  display: block;
  box-sizing: border-box;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  -webkit-touch-callout: none;
  touch-action: pan-y;
  -webkit-tap-highlight-color: transparent;
  scroll-behavior: smooth;
}

.emotion-1 .slick-slider .slick-track,
.emotion-1 .slick-slider .slick-list {
  -webkit-transform: translate3d(0, 0, 0);
  -moz-transform: translate3d(0, 0, 0);
  -ms-transform: translate3d(0, 0, 0);
  transform: translate3d(0, 0, 0);
}

.emotion-1 .slick-list {
  position: relative;
  display: block;
  overflow-x: hidden;
  overflow-y: hidden;
  margin: 0;
  padding: 0;
}

.emotion-1 .slick-list:focus {
  outline: none;
}

.emotion-1 .slick-list.dragging {
  cursor: pointer;
  cursor: hand;
}

.emotion-1 .slick-track {
  position: relative;
  top: 0;
  left: 0;
  display: block;
  margin-left: auto;
  margin-right: auto;
}

.emotion-1 .slick-track:before,
.emotion-1 .slick-track:after {
  display: table;
  content: "";
}

.emotion-1 .slick-track:after {
  clear: both;
}

.emotion-1 .slick-slide {
  display: none;
  float: left;
  height: 100%;
  min-height: 1px;
}

.emotion-1 .slick-slide img {
  display: block;
}

.emotion-1 .slick-slide.slick-loading img {
  display: none;
}

.emotion-1 .slick-slide.dragging img {
  pointer-events: none;
}

.emotion-1 [dir="rtl"] .slick-slide {
  float: right;
}

.emotion-1 .slick-initialized .slick-slide,
.emotion-1 .slick-vertical .slick-slide {
  display: block;
}

.emotion-1 .slick-vertical .slick-slide {
  border: 1px solid transparent;
  height: auto;
}

.emotion-1 .slick-loading .slick-track,
.emotion-1 .slick-loading .slick-slide {
  visibility: hidden;
}

.emotion-1 .slick-loading .slick-list {
  background: #fff url("./ajax-loader.gif") center center no-repeat;
}

.emotion-1 .slick-arrow.slick-hidden {
  display: none;
}

.emotion-1 .slick-prev,
.emotion-1 .slick-next {
  font-size: 0;
  line-height: 0;
  position: absolute;
  top: 50%;
  display: block;
  padding: 0;
  cursor: pointer;
  color: transparent;
  border: none;
  outline: none;
  background: transparent;
}

.emotion-1 .slick-prev:hover,
.emotion-1 .slick-next:hover,
.emotion-1 .slick-prev:focus,
.emotion-1 .slick-next:focus {
  color: transparent;
  outline: none;
  background: transparent;
  opacity: 1;
}

.emotion-1 .slick-prev.slick-disabled,
.emotion-1 .slick-next.slick-disabled {
  opacity: 0.25;
}

.emotion-1 .slick-prev {
  left: -0;
  -webkit-transform: translate(0, -50%) rotate(90deg);
  -moz-transform: translate(0, -50%) rotate(90deg);
  -ms-transform: translate(0, -50%) rotate(90deg);
  transform: translate(0, -50%) rotate(90deg);
}

.emotion-1 .slick-prev span {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-1 [dir="rtl"] .slick-prev {
  right: -0;
  left: auto;
}

.emotion-1 .slick-next {
  right: -0;
  -webkit-transform: translate(0, -50%) rotate(-90deg);
  -moz-transform: translate(0, -50%) rotate(-90deg);
  -ms-transform: translate(0, -50%) rotate(-90deg);
  transform: translate(0, -50%) rotate(-90deg);
}

.emotion-1 .slick-next span {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-1 [dir="rtl"] .slick-next {
  right: auto;
  left: -0;
}

.emotion-1 .slick-dotted.slick-slider {
  margin-bottom: 30px;
}

.emotion-1 .slick-dots {
  position: absolute;
  bottom: -25px;
  display: block;
  width: 100%;
  padding: 0;
  margin: 0;
  list-style: none;
  text-align: center;
}

.emotion-1 .slick-dots li {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: 20px;
  height: 20px;
  margin: 0 5px;
  padding: 0;
  cursor: pointer;
}

.emotion-1 .slick-dots li.slick-active button:before {
  opacity: 0.75;
  color: black;
}

.emotion-1 .slick-dots li button {
  font-size: 0;
  line-height: 0;
  display: block;
  padding: 5px;
  cursor: pointer;
  color: transparent;
  border: 0;
  outline: none;
  background: transparent;
}

.emotion-1 .slick-dots li button:hover,
.emotion-1 .slick-dots li button:focus {
  outline: none;
}

.emotion-1 .slick-dots li button:hover:before,
.emotion-1 .slick-dots li button:focus:before,
.emotion-1 .slick-dots li button:hover:before,
.emotion-1 .slick-dots li button:focus:before {
  opacity: 1;
}

.emotion-1 .slick-dots li button:before {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 5px;
  height: 5px;
  content: "";
  opacity: 0.25;
  background-color: black;
  border-radius: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-2 {
  position: absolute;
  border: none;
  padding: 0;
  z-index: 1;
  background: transparent;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  margin: 0;
  bottom: 0;
  left: 0;
}

.emotion-2>span {
  display: initial;
}

.emotion-3 {
  width: 44px;
  height: 44px;
}

.emotion-4 {
  display: inline-block;
  height: 44px;
  width: 44px;
  min-height: 44px;
  min-width: 44px;
}

.emotion-4 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-5 {
  display: inline-block;
  height: 2rem;
  width: 2rem;
  min-height: 2rem;
  min-width: 2rem;
}

.emotion-5 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

<div
    style="font-family: var(--font-family-font1),sans-serif;"
  >
    <div
      style="font-family: var(--font-family-font1),sans-serif;"
    >
      <nav
        class="emotion-0"
      >
        <div
          class="emotion-1"
        >
          <button
            aria-label="pause"
            class="emotion-2"
          >
            <div
              class="emotion-3"
            >
              <span
                aria-hidden="true"
                class="emotion-4"
              >
                <svg
                  viewBox="0 0 50 50"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M0 0h50v50H0z"
                    fill="#FFFFFF"
                    opacity=".7"
                  />
                  <path
                    d="M20 19h4v13h-4zM27 19h4v13h-4z"
                    fill="#003764"
                  />
                </svg>
              </span>
            </div>
          </button>
          <div
            class="slick-slider slick-initialized"
            dir="ltr"
          >
            <button
              aria-label="Previous"
              class="slick-prev slick-arrow slick-prev"
              data-role="none"
            >
              <span
                aria-hidden="true"
                class="emotion-5"
              >
                <svg
                  viewBox="0 0 13.29 8.07"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M6.13 7.86a.73.73 0 001.04 0l5.92-5.92a.73.73 0 000-1.03l-.7-.7a.73.73 0 00-1.03 0l-4.71 4.7L1.94.2A.73.73 0 00.9.21l-.69.7a.73.73 0 000 1.03z"
                    fill="#FFFFFF"
                  />
                </svg>
              </span>
            </button>
            <div
              class="slick-list"
            >
              <div
                class="slick-track"
                style="opacity: 1; transform: translate3d(0px, 0px, 0px);"
              >
                <div
                  aria-hidden="true"
                  class="slick-slide slick-cloned"
                  data-index="-1"
                  style="width: 0px;"
                  tabindex="-1"
                >
                  <div>
                    <div>
                      I am a child component
                    </div>
                  </div>
                </div>
                <div
                  aria-hidden="false"
                  class="slick-slide slick-active slick-current"
                  data-index="0"
                  style="outline: none; width: 0px;"
                  tabindex="-1"
                >
                  <div>
                    <div>
                      I am a child component
                    </div>
                  </div>
                </div>
                <div
                  aria-hidden="true"
                  class="slick-slide"
                  data-index="1"
                  style="outline: none; width: 0px;"
                  tabindex="-1"
                >
                  <div>
                    <div>
                      I am a child component
                    </div>
                  </div>
                </div>
                <div
                  aria-hidden="true"
                  class="slick-slide slick-cloned"
                  data-index="2"
                  style="width: 0px;"
                  tabindex="-1"
                >
                  <div>
                    <div>
                      I am a child component
                    </div>
                  </div>
                </div>
                <div
                  aria-hidden="true"
                  class="slick-slide slick-cloned"
                  data-index="3"
                  style="width: 0px;"
                  tabindex="-1"
                >
                  <div>
                    <div>
                      I am a child component
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <button
              aria-label="Next"
              class="slick-next slick-arrow slick-next"
              data-role="none"
            >
              <span
                aria-hidden="true"
                class="emotion-5"
              >
                <svg
                  viewBox="0 0 13.29 8.07"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M6.13 7.86a.73.73 0 001.04 0l5.92-5.92a.73.73 0 000-1.03l-.7-.7a.73.73 0 00-1.03 0l-4.71 4.7L1.94.2A.73.73 0 00.9.21l-.69.7a.73.73 0 000 1.03z"
                    fill="#FFFFFF"
                  />
                </svg>
              </span>
            </button>
          </div>
        </div>
      </nav>
    </div>
  </div>
</DocumentFragment>
`;

exports[`CMS Marketing Carousel should match snapshots in mobile for at 1`] = `
<DocumentFragment>
  .emotion-0 {
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-0 .slick-list {
  overflow: hidden;
}

.emotion-0 .slick-track {
  width: -webkit-max-content!important;
  width: -moz-max-content!important;
  width: max-content!important;
}

.emotion-0 button.slick-next.slick-arrow.slick-next,
.emotion-0 button.slick-prev.slick-arrow.slick-prev {
  z-index: 2;
  top: undefined;
  background: #FFFFFF80;
  opacity: 60%;
  height: 44px;
  width: 44px;
}

.emotion-0 button.slick-next.slick-arrow.slick-next span svg path,
.emotion-0 button.slick-prev.slick-arrow.slick-prev span svg path {
  fill: #00000080;
}

.emotion-0 button.slick-next.slick-arrow.slick-next:hover,
.emotion-0 button.slick-prev.slick-arrow.slick-prev:hover {
  background: #FFFFFF80;
}

.emotion-0 button.slick-next.slick-arrow.slick-next:focus,
.emotion-0 button.slick-prev.slick-arrow.slick-prev:focus {
  background: #FFFFFF80;
}

.emotion-0 button.slick-next.slick-arrow.slick-next>span,
.emotion-0 button.slick-prev.slick-arrow.slick-prev>span {
  margin: auto;
}

.emotion-0 button.slick-next.slick-arrow.slick-next svg,
.emotion-0 button.slick-prev.slick-arrow.slick-prev svg {
  margin: auto;
  height: 25px;
  width: 14px;
}

.emotion-0 button.slick-next.slick-arrow.slick-next:focus-visible,
.emotion-0 button.slick-prev.slick-arrow.slick-prev:focus-visible {
  outline: auto;
}

.emotion-0 .slick-slide {
  margin: 0;
  width: auto;
}

.emotion-0 .slick-disabled {
  display: none!important;
}

.emotion-0 .slick-next {
  left: calc(100% - 44px);
}

.emotion-0 .slick-slide:first-of-type a>div:nth-of-type(2)>div>div:nth-of-type(2) {
  border-left: none;
}

.emotion-0 .slick-slide>div:first-of-type {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-0 .slick-slide>div:first-of-type>div:first-of-type {
  width: 100%;
}

.emotion-1 {
  position: relative;
}

.emotion-1 .slick-slider {
  position: relative;
  display: block;
  box-sizing: border-box;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  -webkit-touch-callout: none;
  touch-action: pan-y;
  -webkit-tap-highlight-color: transparent;
  scroll-behavior: smooth;
}

.emotion-1 .slick-slider .slick-track,
.emotion-1 .slick-slider .slick-list {
  -webkit-transform: translate3d(0, 0, 0);
  -moz-transform: translate3d(0, 0, 0);
  -ms-transform: translate3d(0, 0, 0);
  transform: translate3d(0, 0, 0);
}

.emotion-1 .slick-list {
  position: relative;
  display: block;
  overflow-x: hidden;
  overflow-y: hidden;
  margin: 0;
  padding: 0;
}

.emotion-1 .slick-list:focus {
  outline: none;
}

.emotion-1 .slick-list.dragging {
  cursor: pointer;
  cursor: hand;
}

.emotion-1 .slick-track {
  position: relative;
  top: 0;
  left: 0;
  display: block;
  margin-left: auto;
  margin-right: auto;
}

.emotion-1 .slick-track:before,
.emotion-1 .slick-track:after {
  display: table;
  content: "";
}

.emotion-1 .slick-track:after {
  clear: both;
}

.emotion-1 .slick-slide {
  display: none;
  float: left;
  height: 100%;
  min-height: 1px;
}

.emotion-1 .slick-slide img {
  display: block;
}

.emotion-1 .slick-slide.slick-loading img {
  display: none;
}

.emotion-1 .slick-slide.dragging img {
  pointer-events: none;
}

.emotion-1 [dir="rtl"] .slick-slide {
  float: right;
}

.emotion-1 .slick-initialized .slick-slide,
.emotion-1 .slick-vertical .slick-slide {
  display: block;
}

.emotion-1 .slick-vertical .slick-slide {
  border: 1px solid transparent;
  height: auto;
}

.emotion-1 .slick-loading .slick-track,
.emotion-1 .slick-loading .slick-slide {
  visibility: hidden;
}

.emotion-1 .slick-loading .slick-list {
  background: #fff url("./ajax-loader.gif") center center no-repeat;
}

.emotion-1 .slick-arrow.slick-hidden {
  display: none;
}

.emotion-1 .slick-prev,
.emotion-1 .slick-next {
  font-size: 0;
  line-height: 0;
  position: absolute;
  top: 50%;
  display: block;
  padding: 0;
  cursor: pointer;
  color: transparent;
  border: none;
  outline: none;
  background: transparent;
}

.emotion-1 .slick-prev:hover,
.emotion-1 .slick-next:hover,
.emotion-1 .slick-prev:focus,
.emotion-1 .slick-next:focus {
  color: transparent;
  outline: none;
  background: transparent;
  opacity: 1;
}

.emotion-1 .slick-prev.slick-disabled,
.emotion-1 .slick-next.slick-disabled {
  opacity: 0.25;
}

.emotion-1 .slick-prev {
  left: -0;
  -webkit-transform: translate(0, -50%) rotate(90deg);
  -moz-transform: translate(0, -50%) rotate(90deg);
  -ms-transform: translate(0, -50%) rotate(90deg);
  transform: translate(0, -50%) rotate(90deg);
}

.emotion-1 .slick-prev span {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-1 [dir="rtl"] .slick-prev {
  right: -0;
  left: auto;
}

.emotion-1 .slick-next {
  right: -0;
  -webkit-transform: translate(0, -50%) rotate(-90deg);
  -moz-transform: translate(0, -50%) rotate(-90deg);
  -ms-transform: translate(0, -50%) rotate(-90deg);
  transform: translate(0, -50%) rotate(-90deg);
}

.emotion-1 .slick-next span {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-1 [dir="rtl"] .slick-next {
  right: auto;
  left: -0;
}

.emotion-1 .slick-dotted.slick-slider {
  margin-bottom: 30px;
}

.emotion-1 .slick-dots {
  position: absolute;
  bottom: -25px;
  display: block;
  width: 100%;
  padding: 0;
  margin: 0;
  list-style: none;
  text-align: center;
}

.emotion-1 .slick-dots li {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: 20px;
  height: 20px;
  margin: 0 5px;
  padding: 0;
  cursor: pointer;
}

.emotion-1 .slick-dots li.slick-active button:before {
  opacity: 0.75;
  color: black;
}

.emotion-1 .slick-dots li button {
  font-size: 0;
  line-height: 0;
  display: block;
  padding: 5px;
  cursor: pointer;
  color: transparent;
  border: 0;
  outline: none;
  background: transparent;
}

.emotion-1 .slick-dots li button:hover,
.emotion-1 .slick-dots li button:focus {
  outline: none;
}

.emotion-1 .slick-dots li button:hover:before,
.emotion-1 .slick-dots li button:focus:before,
.emotion-1 .slick-dots li button:hover:before,
.emotion-1 .slick-dots li button:focus:before {
  opacity: 1;
}

.emotion-1 .slick-dots li button:before {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 5px;
  height: 5px;
  content: "";
  opacity: 0.25;
  background-color: black;
  border-radius: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-2 {
  position: absolute;
  border: none;
  padding: 0;
  z-index: 1;
  background: transparent;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  margin: 0;
  bottom: 0;
  left: 0;
}

.emotion-2>span {
  display: initial;
}

.emotion-3 {
  width: 44px;
  height: 44px;
}

.emotion-4 {
  display: inline-block;
  height: 44px;
  width: 44px;
  min-height: 44px;
  min-width: 44px;
}

.emotion-4 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-5 {
  display: inline-block;
  height: 2rem;
  width: 2rem;
  min-height: 2rem;
  min-width: 2rem;
}

.emotion-5 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

<div
    style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <nav
        class="emotion-0"
      >
        <div
          class="emotion-1"
        >
          <button
            aria-label="pause"
            class="emotion-2"
          >
            <div
              class="emotion-3"
            >
              <span
                aria-hidden="true"
                class="emotion-4"
              >
                <svg
                  fill="none"
                  viewBox="0 0 61 61"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M61 0H0v61h61V0Z"
                    fill="#FFFFFF"
                    fill-opacity=".6"
                  />
                  <path
                    d="M33.5 25H35v12h-1.5V25ZM25 25h1.5v12H25V25Z"
                    fill="#111"
                  />
                </svg>
              </span>
            </div>
          </button>
          <div
            class="slick-slider slick-initialized"
            dir="ltr"
          >
            <button
              aria-label="Previous"
              class="slick-prev slick-arrow slick-prev"
              data-role="none"
            >
              <span
                aria-hidden="true"
                class="emotion-5"
              >
                <svg
                  viewBox="0 0 26.78 17.63"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M0 1.95l13 15.68L26.79 1.98 24.53 0 13.08 13.01 2.3.03z"
                    fill="#FFFFFF"
                  />
                </svg>
              </span>
            </button>
            <div
              class="slick-list"
            >
              <div
                class="slick-track"
                style="opacity: 1; transform: translate3d(0px, 0px, 0px);"
              >
                <div
                  aria-hidden="true"
                  class="slick-slide slick-cloned"
                  data-index="-1"
                  style="width: 0px;"
                  tabindex="-1"
                >
                  <div>
                    <div>
                      I am a child component
                    </div>
                  </div>
                </div>
                <div
                  aria-hidden="false"
                  class="slick-slide slick-active slick-current"
                  data-index="0"
                  style="outline: none; width: 0px;"
                  tabindex="-1"
                >
                  <div>
                    <div>
                      I am a child component
                    </div>
                  </div>
                </div>
                <div
                  aria-hidden="true"
                  class="slick-slide"
                  data-index="1"
                  style="outline: none; width: 0px;"
                  tabindex="-1"
                >
                  <div>
                    <div>
                      I am a child component
                    </div>
                  </div>
                </div>
                <div
                  aria-hidden="true"
                  class="slick-slide slick-cloned"
                  data-index="2"
                  style="width: 0px;"
                  tabindex="-1"
                >
                  <div>
                    <div>
                      I am a child component
                    </div>
                  </div>
                </div>
                <div
                  aria-hidden="true"
                  class="slick-slide slick-cloned"
                  data-index="3"
                  style="width: 0px;"
                  tabindex="-1"
                >
                  <div>
                    <div>
                      I am a child component
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <button
              aria-label="Next"
              class="slick-next slick-arrow slick-next"
              data-role="none"
            >
              <span
                aria-hidden="true"
                class="emotion-5"
              >
                <svg
                  viewBox="0 0 26.78 17.63"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M0 1.95l13 15.68L26.79 1.98 24.53 0 13.08 13.01 2.3.03z"
                    fill="#FFFFFF"
                  />
                </svg>
              </span>
            </button>
            <ul
              class="slick-dots"
              style="display: block;"
            >
              <li
                class="slick-active"
              >
                <button>
                  1
                </button>
              </li>
              <li
                class=""
              >
                <button>
                  2
                </button>
              </li>
            </ul>
          </div>
        </div>
      </nav>
    </div>
  </div>
</DocumentFragment>
`;

exports[`CMS Marketing Carousel should match snapshots in mobile for gap 1`] = `
<DocumentFragment>
  .emotion-0 {
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-0 .slick-list {
  overflow: hidden;
}

.emotion-0 .slick-track {
  width: -webkit-max-content!important;
  width: -moz-max-content!important;
  width: max-content!important;
}

.emotion-0 button.slick-next.slick-arrow.slick-next,
.emotion-0 button.slick-prev.slick-arrow.slick-prev {
  z-index: 2;
  top: undefined;
  height: 44px;
  width: 44px;
}

.emotion-0 button.slick-next.slick-arrow.slick-next>span,
.emotion-0 button.slick-prev.slick-arrow.slick-prev>span {
  margin: auto;
}

.emotion-0 button.slick-next.slick-arrow.slick-next svg,
.emotion-0 button.slick-prev.slick-arrow.slick-prev svg {
  margin: auto;
  height: 25px;
  width: 14px;
}

.emotion-0 .slick-slide {
  margin: 0;
  width: auto;
}

.emotion-0 .slick-disabled {
  display: none!important;
}

.emotion-0 .slick-next {
  left: calc(100% - 44px);
}

.emotion-0 .slick-slide:first-of-type a>div:nth-of-type(2)>div>div:nth-of-type(2) {
  border-left: none;
}

.emotion-0 .slick-slide>div:first-of-type {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-0 .slick-slide>div:first-of-type>div:first-of-type {
  width: 100%;
}

.emotion-1 {
  position: relative;
}

.emotion-1 .slick-slider {
  position: relative;
  display: block;
  box-sizing: border-box;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  -webkit-touch-callout: none;
  touch-action: pan-y;
  -webkit-tap-highlight-color: transparent;
  scroll-behavior: smooth;
}

.emotion-1 .slick-slider .slick-track,
.emotion-1 .slick-slider .slick-list {
  -webkit-transform: translate3d(0, 0, 0);
  -moz-transform: translate3d(0, 0, 0);
  -ms-transform: translate3d(0, 0, 0);
  transform: translate3d(0, 0, 0);
}

.emotion-1 .slick-list {
  position: relative;
  display: block;
  overflow-x: hidden;
  overflow-y: hidden;
  margin: 0;
  padding: 0;
}

.emotion-1 .slick-list:focus {
  outline: none;
}

.emotion-1 .slick-list.dragging {
  cursor: pointer;
  cursor: hand;
}

.emotion-1 .slick-track {
  position: relative;
  top: 0;
  left: 0;
  display: block;
  margin-left: auto;
  margin-right: auto;
}

.emotion-1 .slick-track:before,
.emotion-1 .slick-track:after {
  display: table;
  content: "";
}

.emotion-1 .slick-track:after {
  clear: both;
}

.emotion-1 .slick-slide {
  display: none;
  float: left;
  height: 100%;
  min-height: 1px;
}

.emotion-1 .slick-slide img {
  display: block;
}

.emotion-1 .slick-slide.slick-loading img {
  display: none;
}

.emotion-1 .slick-slide.dragging img {
  pointer-events: none;
}

.emotion-1 [dir="rtl"] .slick-slide {
  float: right;
}

.emotion-1 .slick-initialized .slick-slide,
.emotion-1 .slick-vertical .slick-slide {
  display: block;
}

.emotion-1 .slick-vertical .slick-slide {
  border: 1px solid transparent;
  height: auto;
}

.emotion-1 .slick-loading .slick-track,
.emotion-1 .slick-loading .slick-slide {
  visibility: hidden;
}

.emotion-1 .slick-loading .slick-list {
  background: #fff url("./ajax-loader.gif") center center no-repeat;
}

.emotion-1 .slick-arrow.slick-hidden {
  display: none;
}

.emotion-1 .slick-prev,
.emotion-1 .slick-next {
  font-size: 0;
  line-height: 0;
  position: absolute;
  top: 50%;
  display: block;
  padding: 0;
  cursor: pointer;
  color: transparent;
  border: none;
  outline: none;
  background: transparent;
}

.emotion-1 .slick-prev:hover,
.emotion-1 .slick-next:hover,
.emotion-1 .slick-prev:focus,
.emotion-1 .slick-next:focus {
  color: transparent;
  outline: none;
  background: transparent;
  opacity: 1;
}

.emotion-1 .slick-prev.slick-disabled,
.emotion-1 .slick-next.slick-disabled {
  opacity: 0.25;
}

.emotion-1 .slick-prev {
  left: -0;
  -webkit-transform: translate(0, -50%) rotate(90deg);
  -moz-transform: translate(0, -50%) rotate(90deg);
  -ms-transform: translate(0, -50%) rotate(90deg);
  transform: translate(0, -50%) rotate(90deg);
}

.emotion-1 .slick-prev span {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-1 [dir="rtl"] .slick-prev {
  right: -0;
  left: auto;
}

.emotion-1 .slick-next {
  right: -0;
  -webkit-transform: translate(0, -50%) rotate(-90deg);
  -moz-transform: translate(0, -50%) rotate(-90deg);
  -ms-transform: translate(0, -50%) rotate(-90deg);
  transform: translate(0, -50%) rotate(-90deg);
}

.emotion-1 .slick-next span {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-1 [dir="rtl"] .slick-next {
  right: auto;
  left: -0;
}

.emotion-1 .slick-dotted.slick-slider {
  margin-bottom: 30px;
}

.emotion-1 .slick-dots {
  position: absolute;
  bottom: -25px;
  display: block;
  width: 100%;
  padding: 0;
  margin: 0;
  list-style: none;
  text-align: center;
}

.emotion-1 .slick-dots li {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: 20px;
  height: 20px;
  margin: 0 5px;
  padding: 0;
  cursor: pointer;
}

.emotion-1 .slick-dots li.slick-active button:before {
  opacity: 0.75;
  color: black;
}

.emotion-1 .slick-dots li button {
  font-size: 0;
  line-height: 0;
  display: block;
  padding: 5px;
  cursor: pointer;
  color: transparent;
  border: 0;
  outline: none;
  background: transparent;
}

.emotion-1 .slick-dots li button:hover,
.emotion-1 .slick-dots li button:focus {
  outline: none;
}

.emotion-1 .slick-dots li button:hover:before,
.emotion-1 .slick-dots li button:focus:before,
.emotion-1 .slick-dots li button:hover:before,
.emotion-1 .slick-dots li button:focus:before {
  opacity: 1;
}

.emotion-1 .slick-dots li button:before {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 5px;
  height: 5px;
  content: "";
  opacity: 0.25;
  background-color: black;
  border-radius: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-2 {
  position: absolute;
  border: none;
  padding: 0;
  z-index: 1;
  background: transparent;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  margin: 0;
  bottom: 0;
  left: 0;
}

.emotion-2>span {
  display: initial;
}

.emotion-3 {
  width: 44px;
  height: 44px;
}

.emotion-4 {
  display: inline-block;
  height: 44px;
  width: 44px;
  min-height: 44px;
  min-width: 44px;
}

.emotion-4 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-5 {
  display: inline-block;
  height: 2rem;
  width: 2rem;
  min-height: 2rem;
  min-width: 2rem;
}

.emotion-5 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <nav
        class="emotion-0"
      >
        <div
          class="emotion-1"
        >
          <button
            aria-label="pause"
            class="emotion-2"
          >
            <div
              class="emotion-3"
            >
              <span
                aria-hidden="true"
                class="emotion-4"
              >
                <svg
                  viewBox="0 0 8.093 18"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <g
                    transform="translate(1.125)"
                  >
                    <path
                      d="M.321,18H-1.125V0H.321Z"
                      fill="#FFFFFF"
                      id="Left"
                      transform="translate(0 0)"
                    />
                    <path
                      d="M.321,17.749H-1.125V0H.321Z"
                      fill="#FFFFFF"
                      id="Right"
                      transform="translate(6.646 0)"
                    />
                  </g>
                </svg>
              </span>
            </div>
          </button>
          <div
            class="slick-slider slick-initialized"
            dir="ltr"
          >
            <button
              aria-label="Previous"
              class="slick-prev slick-arrow slick-prev"
              data-role="none"
            >
              <span
                aria-hidden="true"
                class="emotion-5"
              >
                <svg
                  viewBox="0 0 18 7.742"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M0,.839,6.44,9,0,17.162.818,18,7.743,9.225,7.522,9l.22-.225L.818,0Z"
                    fill="#FFFFFF"
                    transform="translate(18) rotate(90)"
                  />
                </svg>
              </span>
            </button>
            <div
              class="slick-list"
            >
              <div
                class="slick-track"
                style="opacity: 1; transform: translate3d(0px, 0px, 0px);"
              >
                <div
                  aria-hidden="true"
                  class="slick-slide slick-cloned"
                  data-index="-1"
                  style="width: 0px;"
                  tabindex="-1"
                >
                  <div>
                    <div>
                      I am a child component
                    </div>
                  </div>
                </div>
                <div
                  aria-hidden="false"
                  class="slick-slide slick-active slick-current"
                  data-index="0"
                  style="outline: none; width: 0px;"
                  tabindex="-1"
                >
                  <div>
                    <div>
                      I am a child component
                    </div>
                  </div>
                </div>
                <div
                  aria-hidden="true"
                  class="slick-slide"
                  data-index="1"
                  style="outline: none; width: 0px;"
                  tabindex="-1"
                >
                  <div>
                    <div>
                      I am a child component
                    </div>
                  </div>
                </div>
                <div
                  aria-hidden="true"
                  class="slick-slide slick-cloned"
                  data-index="2"
                  style="width: 0px;"
                  tabindex="-1"
                >
                  <div>
                    <div>
                      I am a child component
                    </div>
                  </div>
                </div>
                <div
                  aria-hidden="true"
                  class="slick-slide slick-cloned"
                  data-index="3"
                  style="width: 0px;"
                  tabindex="-1"
                >
                  <div>
                    <div>
                      I am a child component
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <button
              aria-label="Next"
              class="slick-next slick-arrow slick-next"
              data-role="none"
            >
              <span
                aria-hidden="true"
                class="emotion-5"
              >
                <svg
                  viewBox="0 0 18 7.742"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M0,.839,6.44,9,0,17.162.818,18,7.743,9.225,7.522,9l.22-.225L.818,0Z"
                    fill="#FFFFFF"
                    transform="translate(18) rotate(90)"
                  />
                </svg>
              </span>
            </button>
            <ul
              class="slick-dots"
              style="display: block;"
            >
              <li
                class="slick-active"
              >
                <button>
                  1
                </button>
              </li>
              <li
                class=""
              >
                <button>
                  2
                </button>
              </li>
            </ul>
          </div>
        </div>
      </nav>
    </div>
  </div>
</DocumentFragment>
`;

exports[`CMS Marketing Carousel should match snapshots in mobile for on 1`] = `
<DocumentFragment>
  .emotion-0 {
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-0 .slick-list {
  overflow: hidden;
}

.emotion-0 .slick-track {
  width: -webkit-max-content!important;
  width: -moz-max-content!important;
  width: max-content!important;
}

.emotion-0 button.slick-next.slick-arrow.slick-next,
.emotion-0 button.slick-prev.slick-arrow.slick-prev {
  z-index: 2;
  top: undefined;
  height: 44px;
  width: 44px;
  background: #ffffff;
  opacity: 75%;
}

.emotion-0 button.slick-next.slick-arrow.slick-next>span,
.emotion-0 button.slick-prev.slick-arrow.slick-prev>span {
  margin: auto;
}

.emotion-0 button.slick-next.slick-arrow.slick-next svg,
.emotion-0 button.slick-prev.slick-arrow.slick-prev svg {
  margin: auto;
  height: 25px;
  width: 14px;
}

.emotion-0 button.slick-next.slick-arrow.slick-next span svg path,
.emotion-0 button.slick-prev.slick-arrow.slick-prev span svg path {
  fill: #003764;
}

.emotion-0 button.slick-next.slick-arrow.slick-next:hover,
.emotion-0 button.slick-prev.slick-arrow.slick-prev:hover {
  opacity: 100%;
}

.emotion-0 button.slick-next.slick-arrow.slick-next:focus,
.emotion-0 button.slick-prev.slick-arrow.slick-prev:focus {
  background: #ffffff;
}

.emotion-0 button.slick-next.slick-arrow.slick-next:focus-visible,
.emotion-0 button.slick-prev.slick-arrow.slick-prev:focus-visible {
  outline: auto;
}

.emotion-0 .slick-slide {
  margin: 0;
  width: auto;
}

.emotion-0 .slick-disabled {
  display: none!important;
}

.emotion-0 .slick-next {
  left: calc(100% - 44px);
}

.emotion-0 .slick-slide:first-of-type a>div:nth-of-type(2)>div>div:nth-of-type(2) {
  border-left: none;
}

.emotion-0 .slick-slide>div:first-of-type {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-0 .slick-slide>div:first-of-type>div:first-of-type {
  width: 100%;
}

.emotion-0 .slick-slider ul.slick-dots {
  bottom: 4px;
  width: unset;
  height: 44px;
  margin-left: 50%;
  -webkit-transform: translateX(-50%);
  -moz-transform: translateX(-50%);
  -ms-transform: translateX(-50%);
  transform: translateX(-50%);
  -webkit-align-content: center;
  -ms-flex-line-pack: center;
  align-content: center;
}

.emotion-0 .slick-slider .slick-dots li.slick-active button:before {
  background-color: #FFFFFF;
}

.emotion-0 .slick-slider ul.slick-dots button:before {
  width: 10px;
  height: 10px;
  opacity: 1;
  border: 1px solid #FFFFFF;
  background-color: transparent;
}

.emotion-1 {
  position: relative;
}

.emotion-1 .slick-slider {
  position: relative;
  display: block;
  box-sizing: border-box;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  -webkit-touch-callout: none;
  touch-action: pan-y;
  -webkit-tap-highlight-color: transparent;
  scroll-behavior: smooth;
}

.emotion-1 .slick-slider .slick-track,
.emotion-1 .slick-slider .slick-list {
  -webkit-transform: translate3d(0, 0, 0);
  -moz-transform: translate3d(0, 0, 0);
  -ms-transform: translate3d(0, 0, 0);
  transform: translate3d(0, 0, 0);
}

.emotion-1 .slick-list {
  position: relative;
  display: block;
  overflow-x: hidden;
  overflow-y: hidden;
  margin: 0;
  padding: 0;
}

.emotion-1 .slick-list:focus {
  outline: none;
}

.emotion-1 .slick-list.dragging {
  cursor: pointer;
  cursor: hand;
}

.emotion-1 .slick-track {
  position: relative;
  top: 0;
  left: 0;
  display: block;
  margin-left: auto;
  margin-right: auto;
}

.emotion-1 .slick-track:before,
.emotion-1 .slick-track:after {
  display: table;
  content: "";
}

.emotion-1 .slick-track:after {
  clear: both;
}

.emotion-1 .slick-slide {
  display: none;
  float: left;
  height: 100%;
  min-height: 1px;
}

.emotion-1 .slick-slide img {
  display: block;
}

.emotion-1 .slick-slide.slick-loading img {
  display: none;
}

.emotion-1 .slick-slide.dragging img {
  pointer-events: none;
}

.emotion-1 [dir="rtl"] .slick-slide {
  float: right;
}

.emotion-1 .slick-initialized .slick-slide,
.emotion-1 .slick-vertical .slick-slide {
  display: block;
}

.emotion-1 .slick-vertical .slick-slide {
  border: 1px solid transparent;
  height: auto;
}

.emotion-1 .slick-loading .slick-track,
.emotion-1 .slick-loading .slick-slide {
  visibility: hidden;
}

.emotion-1 .slick-loading .slick-list {
  background: #fff url("./ajax-loader.gif") center center no-repeat;
}

.emotion-1 .slick-arrow.slick-hidden {
  display: none;
}

.emotion-1 .slick-prev,
.emotion-1 .slick-next {
  font-size: 0;
  line-height: 0;
  position: absolute;
  top: 50%;
  display: block;
  padding: 0;
  cursor: pointer;
  color: transparent;
  border: none;
  outline: none;
  background: transparent;
}

.emotion-1 .slick-prev:hover,
.emotion-1 .slick-next:hover,
.emotion-1 .slick-prev:focus,
.emotion-1 .slick-next:focus {
  color: transparent;
  outline: none;
  background: transparent;
  opacity: 1;
}

.emotion-1 .slick-prev.slick-disabled,
.emotion-1 .slick-next.slick-disabled {
  opacity: 0.25;
}

.emotion-1 .slick-prev {
  left: -0;
  -webkit-transform: translate(0, -50%) rotate(90deg);
  -moz-transform: translate(0, -50%) rotate(90deg);
  -ms-transform: translate(0, -50%) rotate(90deg);
  transform: translate(0, -50%) rotate(90deg);
}

.emotion-1 .slick-prev span {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-1 [dir="rtl"] .slick-prev {
  right: -0;
  left: auto;
}

.emotion-1 .slick-next {
  right: -0;
  -webkit-transform: translate(0, -50%) rotate(-90deg);
  -moz-transform: translate(0, -50%) rotate(-90deg);
  -ms-transform: translate(0, -50%) rotate(-90deg);
  transform: translate(0, -50%) rotate(-90deg);
}

.emotion-1 .slick-next span {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-1 [dir="rtl"] .slick-next {
  right: auto;
  left: -0;
}

.emotion-1 .slick-dotted.slick-slider {
  margin-bottom: 30px;
}

.emotion-1 .slick-dots {
  position: absolute;
  bottom: -25px;
  display: block;
  width: 100%;
  padding: 0;
  margin: 0;
  list-style: none;
  text-align: center;
}

.emotion-1 .slick-dots li {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: 20px;
  height: 20px;
  margin: 0 5px;
  padding: 0;
  cursor: pointer;
}

.emotion-1 .slick-dots li.slick-active button:before {
  opacity: 0.75;
  color: black;
}

.emotion-1 .slick-dots li button {
  font-size: 0;
  line-height: 0;
  display: block;
  padding: 5px;
  cursor: pointer;
  color: transparent;
  border: 0;
  outline: none;
  background: transparent;
}

.emotion-1 .slick-dots li button:hover,
.emotion-1 .slick-dots li button:focus {
  outline: none;
}

.emotion-1 .slick-dots li button:hover:before,
.emotion-1 .slick-dots li button:focus:before,
.emotion-1 .slick-dots li button:hover:before,
.emotion-1 .slick-dots li button:focus:before {
  opacity: 1;
}

.emotion-1 .slick-dots li button:before {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 5px;
  height: 5px;
  content: "";
  opacity: 0.25;
  background-color: black;
  border-radius: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-2 {
  position: absolute;
  border: none;
  padding: 0;
  z-index: 1;
  background: transparent;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  margin: 0;
  bottom: 0;
  left: 0;
}

.emotion-2>span {
  display: initial;
}

.emotion-3 {
  width: 44px;
  height: 44px;
}

.emotion-4 {
  display: inline-block;
  height: 44px;
  width: 44px;
  min-height: 44px;
  min-width: 44px;
}

.emotion-4 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-5 {
  display: inline-block;
  height: 2rem;
  width: 2rem;
  min-height: 2rem;
  min-width: 2rem;
}

.emotion-5 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

<div
    style="font-family: var(--font-family-font1),sans-serif;"
  >
    <div
      style="font-family: var(--font-family-font1),sans-serif;"
    >
      <nav
        class="emotion-0"
      >
        <div
          class="emotion-1"
        >
          <button
            aria-label="pause"
            class="emotion-2"
          >
            <div
              class="emotion-3"
            >
              <span
                aria-hidden="true"
                class="emotion-4"
              >
                <svg
                  viewBox="0 0 50 50"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M0 0h50v50H0z"
                    fill="#FFFFFF"
                    opacity=".7"
                  />
                  <path
                    d="M20 19h4v13h-4zM27 19h4v13h-4z"
                    fill="#003764"
                  />
                </svg>
              </span>
            </div>
          </button>
          <div
            class="slick-slider slick-initialized"
            dir="ltr"
          >
            <button
              aria-label="Previous"
              class="slick-prev slick-arrow slick-prev"
              data-role="none"
            >
              <span
                aria-hidden="true"
                class="emotion-5"
              >
                <svg
                  viewBox="0 0 13.29 8.07"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M6.13 7.86a.73.73 0 001.04 0l5.92-5.92a.73.73 0 000-1.03l-.7-.7a.73.73 0 00-1.03 0l-4.71 4.7L1.94.2A.73.73 0 00.9.21l-.69.7a.73.73 0 000 1.03z"
                    fill="#FFFFFF"
                  />
                </svg>
              </span>
            </button>
            <div
              class="slick-list"
            >
              <div
                class="slick-track"
                style="opacity: 1; transform: translate3d(0px, 0px, 0px);"
              >
                <div
                  aria-hidden="true"
                  class="slick-slide slick-cloned"
                  data-index="-1"
                  style="width: 0px;"
                  tabindex="-1"
                >
                  <div>
                    <div>
                      I am a child component
                    </div>
                  </div>
                </div>
                <div
                  aria-hidden="false"
                  class="slick-slide slick-active slick-current"
                  data-index="0"
                  style="outline: none; width: 0px;"
                  tabindex="-1"
                >
                  <div>
                    <div>
                      I am a child component
                    </div>
                  </div>
                </div>
                <div
                  aria-hidden="true"
                  class="slick-slide"
                  data-index="1"
                  style="outline: none; width: 0px;"
                  tabindex="-1"
                >
                  <div>
                    <div>
                      I am a child component
                    </div>
                  </div>
                </div>
                <div
                  aria-hidden="true"
                  class="slick-slide slick-cloned"
                  data-index="2"
                  style="width: 0px;"
                  tabindex="-1"
                >
                  <div>
                    <div>
                      I am a child component
                    </div>
                  </div>
                </div>
                <div
                  aria-hidden="true"
                  class="slick-slide slick-cloned"
                  data-index="3"
                  style="width: 0px;"
                  tabindex="-1"
                >
                  <div>
                    <div>
                      I am a child component
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <button
              aria-label="Next"
              class="slick-next slick-arrow slick-next"
              data-role="none"
            >
              <span
                aria-hidden="true"
                class="emotion-5"
              >
                <svg
                  viewBox="0 0 13.29 8.07"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M6.13 7.86a.73.73 0 001.04 0l5.92-5.92a.73.73 0 000-1.03l-.7-.7a.73.73 0 00-1.03 0l-4.71 4.7L1.94.2A.73.73 0 00.9.21l-.69.7a.73.73 0 000 1.03z"
                    fill="#FFFFFF"
                  />
                </svg>
              </span>
            </button>
            <ul
              class="slick-dots"
              style="display: block;"
            >
              <li
                class="slick-active"
              >
                <button>
                  1
                </button>
              </li>
              <li
                class=""
              >
                <button>
                  2
                </button>
              </li>
            </ul>
          </div>
        </div>
      </nav>
    </div>
  </div>
</DocumentFragment>
`;
