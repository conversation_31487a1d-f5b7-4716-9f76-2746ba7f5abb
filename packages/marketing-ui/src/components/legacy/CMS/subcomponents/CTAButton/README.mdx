# CTAButton helper

`CTAButton` is a helper for Amplience-configurable components. It creates a button that opens a link or jumplink.

- When should you use the component?
  - Use `CTAButton` when you are building a CMS component and needs a button which opens a link or jumplink.

Props for `CTAButton`
[CTA types](https://github.gapinc.com/ecomfrontend/core-ui/blob/main/packages/marketing-ui/CMS/helpers/CTAButton/types.ts)

### Color Options

There are 3 color options on the CTA button.

- `dark | Primary`
- `light | Secondary`
- `custom | Custom`

The default color theme is `dark`.

### CTA Width

Per UX direction, `underline` and `chevron` options are always `natural` width, meaning that setting the button style to `fullWidth` or `fixedWidth` using this CTA helper will be ignored for those styles. `border`, `outline`, and `solid` CTA styles, on the other hand, can use `natural` or `fixedWidth` options.

Since fixed width buttons are needed frequently, we have a helper, `getCtaFixedWidth`, which accepts `isDesktop` and `theme.brand` and returns the correct width for a fixed button.

Ex. `getCtaFixedWidth(true, Brands.Athleta)` returns `278px`.

## Examples

You can find this helper being used in components like:

- [Category Banner More to Shop](/story/common-json-components-marketing-cms-categorybannermoretoshop--variable-image-height)
- [Subcategory Banner with cta](/story/common-json-components-marketing-cms-subcategorybanner--with-cta)
- [Left Nav Tile](/story/common-json-components-marketing-cms-leftnavtile--with-cta)

## Important Notes

### Gap 2024 CTA Button Update

As of September 2024, the Gap brand has updated its guidelines for CTA buttons, removing predefined casing rules or
overrides. Authors now have complete control over the casing of text on CTA buttons, allowing them to set the desired
capitalization style directly.

### Old Navy 2024 CTA Button Redesign

As of November 2024, Old Navy decided to restyle its medium size Border and Solid style `CTAButton` variants.
The new 2024 cta redesign styles for those `CTAButton` variants are only available if the "on-cta-redesign-2024"
feature flag is turned on _and_ the opt-in `OnCtaRedesign2024Context` contains `{enabled: true}`.
