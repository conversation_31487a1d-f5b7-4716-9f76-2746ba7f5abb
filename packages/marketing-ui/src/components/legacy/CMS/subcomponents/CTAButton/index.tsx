// @ts-nocheck
'use client';
import React, { CSSProperties, useContext } from 'react';
import { CSSObject, forBrands, Theme, useTheme, useEnabledFeatures } from '@ecom-next/core/react-stitch';
import { OnCtaRedesign2024Context } from '../../../contexts/OnCtaRedesign2024Context';
import { BorderThickness, Capitalization, Color, Size, Variant } from '../../../components/ComposableButton/types';
import { ComposableButton, ComposableButtonProps } from '../../../components/ComposableButton';
import { useViewportIsXLarge, useViewportIsLarge } from '../../../hooks/useViewportIsLarge';
import { ctasWithoutFixedWidth } from '../../global/constants';
import { ctaButtonStyles } from './styles';
import { CtaProps, CTASize, CTATreatmentProp, CustomColorsProps } from './types';
import { formatUrl } from '../../../helper';

export * from './types';

export const getCustomColors = (variant: Variant, primaryHex: string, secondaryHex: string): CustomColorsProps | undefined => ({
  foregroundColor: primaryHex,
  backgroundColor: secondaryHex,
});

export function getFixedCtaWidth(theme: Theme, isDesktop: boolean, size?: CTASize): string {
  switch (size) {
    case Size.xl:
      return forBrands(theme, {
        at: isDesktop ? '335px' : '278px',
        on: isDesktop ? '380px' : '340px',
      }) as string;
    case Size.large:
      return forBrands(theme, {
        at: isDesktop ? '300px' : '278px',
        on: isDesktop ? '360px' : '310px',
      }) as string;
    case Size.medium:
      return forBrands(theme, {
        at: '270px',
        on: isDesktop ? '335px' : '280px',
      }) as string;
    case Size.small:
      return forBrands(theme, {
        at: '230px',
        on: isDesktop ? '300px' : '250px',
      }) as string;
    case Size.xs:
      return forBrands(theme, {
        at: '200px',
        on: isDesktop ? '230px' : '210px',
      }) as string;
    default:
      return forBrands(theme, {
        at: isDesktop ? '335px' : '278px', // default is xl
        default: isDesktop ? '335px' : '280px', // default for other brands is medium
      }) as string;
  }
}

export const CtaButton = ({
  className,
  ctaButton,
  ctaButtonStyling,
  alignment = 'left',
  ctaSize = Size.medium,
  customCtaStyles,
  ctaPadding,
  fullWidth,
  fixedWidth,
  interactiveStyles = true,
  isAJumplink,
  jumplinkCSSSelector,
  target,
  selected = false,
  ctaTreatment,
  isDesktop,
  borderThickness,
  id,
  ariaLabelledby,
  capitalization,
  tabIndex,
  borderRadius,
}: CtaProps & CTATreatmentProp): JSX.Element => {
  const theme = useTheme();
  const { label, value } = ctaButton;
  const { buttonStyle = Variant.solid, buttonColor = Color.dark, primaryHex, secondaryHex } = ctaButtonStyling || {};
  const isXLargeVP = useViewportIsXLarge();
  const isLargeVP = useViewportIsLarge();
  const enabledFeatures = useEnabledFeatures();
  const useOnCtaRedesign2024 = useContext(OnCtaRedesign2024Context).enable;
  const isOnCtaRedesign2024Enabled = !!enabledFeatures?.['on-cta-redesign-2024'] && useOnCtaRedesign2024;

  const isDesktopCheck = isDesktop || isXLargeVP;

  const ctaButtonStyle = ctaButtonStyles(alignment, ctaPadding);

  const conditionalOnCTARedesignStyles = (): CSSObject => {
    if (isOnCtaRedesign2024Enabled && ctaSize === Size.medium && (buttonStyle === 'solid' || buttonStyle === 'border'))
      return isLargeVP ? { minWidth: 136, maxWidth: 255 } : { minWidth: 136, maxWidth: 359 };
    return {};
  };

  const brandSpecificStyle = forBrands(theme, {
    gap: {
      textTransform: 'none',
    },
    gapfs: {
      textTransform: 'none',
    },
    on: conditionalOnCTARedesignStyles(),
    default: {},
  }) as CSSObject;

  const mergedStyle: CSSObject = {
    ...ctaButtonStyle,
    ...brandSpecificStyle,
  };

  if (fixedWidth && !ctasWithoutFixedWidth.includes(buttonStyle as Variant)) {
    let mobileButtonsFixedWidth = getFixedCtaWidth(theme, isDesktopCheck, ctaSize);
    if (ctaTreatment === 'two') {
      mobileButtonsFixedWidth = '100%';
    }
    mergedStyle.width = isDesktopCheck ? '335px' : mobileButtonsFixedWidth;
  }

  const targetValue = target ? { target } : {};

  const formattedUrl = formatUrl(value);

  const optionalTabIndex = tabIndex && {
    tabIndex,
  };

  const ctaButtonData: Omit<ComposableButtonProps, 'children'> = {
    className,
    linkProps: {
      to: formattedUrl,
      ...targetValue,
      isAJumplink,
      jumplinkCSSSelector,
      ...optionalTabIndex,
    },
    interactiveStyles,
    variant: buttonStyle as Variant,
    style: mergedStyle as CSSProperties & CSSObject, // Type Cast is required
    color: buttonColor as Color | undefined,
    size: ctaSize as Size | undefined,
    capitalization: capitalization as Capitalization | undefined,
    borderThickness: borderThickness as BorderThickness | undefined,
    fullWidth: !ctasWithoutFixedWidth.includes(buttonStyle) && fullWidth,
    customColors:
      primaryHex && secondaryHex && buttonColor && buttonColor === 'custom' ? getCustomColors(buttonStyle as Variant, primaryHex, secondaryHex) : undefined,
    selected,
  };

  const optionalAriaLabelledBy = ariaLabelledby && {
    'aria-labelledby': ariaLabelledby,
  };

  return (
    <ComposableButton {...ctaButtonData} {...optionalAriaLabelledBy} borderRadius={borderRadius} css={customCtaStyles} id={id} selected={selected}>
      {label}
    </ComposableButton>
  );
};
