'use client';
import React from 'react'; // @ts-ignore
import { StoryFn } from '@storybook/react';
import README from '../README.mdx';
import { ChevronWithTextWrap, BorderWithTextWrap, Configurable, BySize, Selected, CustomColor, ButtonStyle } from './index-story';
import { OnCtaRedesign2024Context } from '../../../../contexts/OnCtaRedesign2024Context';

export default {
  title: 'Common/JSON Components (Marketing)/CMS/Utilities/CTAButton',
  parameters: {
    docs: { page: README },
    knobs: { disabled: true },
  },
  tags: ['visual:check'],
};

export const VRTestsPartOne: StoryFn = () => (
  <OnCtaRedesign2024Context.Provider value={{ enable: true }}>
    <div // @ts-ignore
      css={{
        fontSize: '20px',
        width: '100%',
        display: 'flex',
        flexFlow: 'column',
        gap: '10px',
      }}
    >
      <h1 // @ts-ignore
        css={{ fontSize: 25, fontWeight: 'bold' }}
      >
        CTA Button Part 1
      </h1>

      <div>Border With Text Wrap:</div>
      <BorderWithTextWrap />

      <div // @ts-ignore
        css={{ borderTop: '1px solid black' }}
      >
        By Size:
      </div>
      <BySize />

      <div // @ts-ignore
        css={{ borderTop: '1px solid black' }}
      >
        Chevron With Text Wrap:
      </div>
      <ChevronWithTextWrap />

      <div // @ts-ignore
        css={{ borderTop: '1px solid black' }}
      >
        Custom Color:
      </div>
      <CustomColor />

      <div // @ts-ignore
        css={{ borderTop: '1px solid black' }}
      >
        Configurable:
      </div>
      <Configurable />
    </div>
  </OnCtaRedesign2024Context.Provider>
);

export const VRTestsPartTwo: StoryFn = () => (
  <OnCtaRedesign2024Context.Provider value={{ enable: true }}>
    <div // @ts-ignore
      css={{
        fontSize: '20px',
        width: '100%',
        display: 'flex',
        flexFlow: 'column',
        gap: '10px',
      }}
    >
      <h1 // @ts-ignore
        css={{ fontSize: 25, fontWeight: 'bold' }}
      >
        CTA Button Part 2
      </h1>
      <div // @ts-ignore
        css={{ borderTop: '1px solid black' }}
      >
        Button Style:
      </div>
      <ButtonStyle />

      <div // @ts-ignore
        css={{ borderTop: '1px solid black' }}
      >
        Selected:
      </div>
      <Selected />
    </div>
  </OnCtaRedesign2024Context.Provider>
);
