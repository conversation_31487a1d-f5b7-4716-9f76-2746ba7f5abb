// @ts-nocheck
'use client';
import { CSSObject } from '@ecom-next/core/react-stitch';
import { Size, Variant, Color, BorderThickness, Capitalization } from '../../../components/ComposableButton/types';
import { ComposableButtonProps } from '../../../components/ComposableButton';
import { AlignmentProps, CtaProps as CTABasicProps } from '../../global/types';

export type CTAVariant = `${Variant}`;
export type CTAColor = `${Color}`;
export type CTASize = `${Size}`;

export type CTABorderThickness = `${BorderThickness}`;

export type CTATreatmentProp = {
  ctaTreatment?: 'single' | 'two';
};

export type CtaButtonStylingProps = {
  /**
   * string - one of `"solid" "outline" "border" "underline" "flat" "chevron". Sets the base styles for the button.
   */
  buttonStyle?: CTAVariant;
  /**
   * string optional - one of "dark", "light", "custom", "black", "white", "primary"` sets color configuration on the button,
   * only "light" and "custom" have styles defined in the component, anything else defaults to dark at this moment.
   */
  buttonColor?: CTAColor;
  /**
   * string optional - sets the color for the text of the component, it's ignored if button color is not set to custom.
   */
  primaryHex?: string;
  /**
   * string optional - sets the color for the background of the component, it's ignored if button color is not set to custom.
   */
  secondaryHex?: string;
};

/**
 * fullWidth boolean optional - defines if the button should take 100% width
 * interactiveStyles boolean - if false the button won't have states that means no hover effect, defaults to true.
 */
export interface CtaProps extends Partial<Pick<ComposableButtonProps, 'interactiveStyles' | 'fullWidth' | 'id' | 'className'>> {
  ariaLabelledby?: ComposableButtonProps['aria-labelledby'];
  target?: '_self' | '_blank' | '_parent' | '_top';
  /** Sets the `text-transform` CSS property
   */
  capitalization?: Capitalization;
  /**
  Object containing the keys label and value label is used as the button text and value for the url it should go to. {label: "Click me", value:"gap.com"}
  */
  ctaButton: {
    label: string;
    value: string;
  };
  /** object - optional; sets styling for the button */
  ctaButtonStyling?: CtaButtonStylingProps;
  /**
   * string - one of 'center','left','right' sets the alignment for the button, it works if its parent has display flex and flex-direction column.
   */
  alignment?: AlignmentProps;
  /** string optional- sets the padding inside of the button. */
  ctaPadding?: string;
  /** optional one of "xs" "small" "medium" "large" "xl" sets predefined size for the button. */
  ctaSize?: CTASize;
  /** object optional - sets custom styles for the button. Properties like fixedWidth and alignment can be overriden by this prop. */
  customCtaStyles?: CSSObject;
  /**
   *  boolean optional - sets the button width to 335px for desktop and 278px for mobile
   */
  fixedWidth?: boolean;
  isAJumplink?: boolean;
  jumplinkCSSSelector?: string;
  /** boolean optional - sets the seleccion state for the button. */
  selected?: boolean;
  isDesktop?: boolean;
  borderThickness?: CTABorderThickness;
  tabIndex?: number;
  borderRadius?: string;
}

export type CustomColorsProps = {
  foregroundColor: string;
  backgroundColor: string;
};

export interface CTADropdownItem {
  ctaDropdown: CTABasicProps[];
  label?: string;
}
