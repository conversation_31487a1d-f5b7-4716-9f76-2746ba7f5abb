# Details Button Changelog

## January 24th, 2023

### Overview

It was found that the `DetailsButton` did not show any visible indication of keyboard focus. In order to meet accessibility standards, all clickable elements on a webpage must have keyboard focus. So, this update adds a focus ring to all `DetailsButtons`.

### Relevant Links

- Jira Ticket: https://gapinc.atlassian.net/browse/PTMDS-523
- CMS Keyboard Accessibility Issues Documentation: https://gapinc.atlassian.net/l/cp/1RS9d0AW

## January 13th, 2023

### Overview

Changes in GapTech's infrastructure has led to the decommissioning of the `/buy/promo_legal_details.do` endpoint.
In this update, we updated the endpoint to be the new `/Asset_Archive/AllBrands/promoAPI/promo_lookup_details.html` endpoint.

### Relevant Links

- Jira Ticket: https://gapinc.atlassian.net/browse/ptmds-517
- New Endpoint Documentation: https://gapinc.atlassian.net/wiki/spaces/WCD/pages/1187807233/promo+lookup+fetch+and+promo+lookup+details
- Tracker for Decommissioning Old Endpoint: https://gapinc.atlassian.net/wiki/spaces/~Mijutea/pages/1143931666/Ecombase+Ecombuy+Requests+to+Migrate+or+Sunset#WCD-Team-TO-DO
