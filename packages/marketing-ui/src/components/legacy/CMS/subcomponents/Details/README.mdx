# Details Button

- [Overview](#overview)
- [Exports](#exports)
  - [`DetailsButton`](#detailsbutton)
  - [`getDetailsContent`](#getdetailscontent)
- [Examples](#examples)

## Overview

`DetailsButton` is a helper for Amplience-configurable components. It creates a button that opens a modal.

- When should you use the component?
  - Use `DetailsButton` when you are building a CMS component and needs a details button that receive the following properties.

## Exports

### `DetailsButton`

A button that opens a modal when clicked. It should be used to showcase details about a promotion.

- Props:
  - `label`: Details Link Label
  - `value`: Must be an HTML modal URL. Use [`getDetailsContent`](#getdetailscontent) to create a url from a pemoleCode
  - `color`: Details link font color (hex code)
  - `customStyles`: to add custom styles to the button
  - `modalHeight`: to change the modal height

#### Add a Prefix

If you want to insert additional copy to the left of the DetailsButton link, you can use the following props.

Prefix Props:

- `prefix`: string
- `prefixVariant`: TypographyVariant - default "body5"
- `prefixColor`: hexadecimal string; #ABC

#### Limitations

It does not support links or other sub components.

### `getDetailsContent`

This helper takes a `brand` and `pemoleCode` and returns a URL to an html page with details about the related promotion.

Example url: https://secure-athleta.gap.com/Asset_Archive/AllBrands/promoAPI/promo_lookup_details.html?promoId=463595

## Examples

You can find this helper being used in the following components:

- [Category Banner Variable Height](/story/common-json-components-marketing-cms-categorybannervariableheight--small)
- [Insort Marketing Banner](/story/common-json-components-marketing-cms-insortmarketingbanner--ism-single-full-image)
- [Left Nav Tile](/story/common-json-components-marketing-cms-leftnavtile--default)
- [Sub Category Banner](/story/common-json-components-marketing-cms-subcategorybanner--with-eyebrow)
