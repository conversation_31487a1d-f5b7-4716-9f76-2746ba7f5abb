'use client';
//@ts-ignore
import { CSSObject } from '@ecom-next/core/react-stitch';

const baseLinkStyles = {
  fontWeight: 350,
  textDecoration: 'underline',
};

export const detailsLinkStyles: Record<'desktop' | 'mobile', CSSObject> = {
  desktop: {
    ...baseLinkStyles,
    fontSize: '12px',
    lineHeight: 1.33, // 16px
    letterSpacing: '0.5px',
  },
  mobile: {
    ...baseLinkStyles,
    fontSize: '8px',
    lineHeight: 1.5, // 12px
    letterSpacing: '0.1px',
  },
};

export const prefixStyles: Record<'desktop' | 'mobile', CSSObject> = {
  desktop: { ...detailsLinkStyles.desktop, textDecoration: 'none' },
  mobile: { ...detailsLinkStyles.mobile, textDecoration: 'none' },
};
