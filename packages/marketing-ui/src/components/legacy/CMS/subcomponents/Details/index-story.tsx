// @ts-nocheck
'use client';
import { Meta, StoryObj } from '@storybook/react';
import React from 'react';
import { sampleData } from './__fixtures__';
import README from './README.mdx';
import { DetailsButton } from '.';

const withBackground = (storyFn: () => JSX.Element) => (
  <div css={{ background: '#eee', padding: '10px', 'text-align': 'center' }}>
    <div css={{ display: 'flex', justifyContent: 'center' }}>{storyFn()}</div>
  </div>
);

const meta: Meta<typeof DetailsButton> = {
  argTypes: {
    color: {
      control: 'color',
      description: 'Details Link Font Color',
    },
    customStyles: {
      control: 'object',
      description: 'Custom Styles',
    },
    label: {
      control: 'text',
      description: 'Details Link label',
      table: {
        defaultValue: { summary: '' },
        type: { summary: 'string' },
      },
      type: { name: 'string', required: true },
    },
    modalHeight: {
      control: 'text',
      description: 'Modal Height',
    },
    prefix: {
      control: 'text',
    },
    prefixColor: {
      control: 'color',
      description: 'hexadecimal color code',
    },
    prefixVariant: {
      control: 'text',
      description: 'TypographyVariant',
      table: {
        defaultValue: { summary: 'body5' },
        type: { summary: 'string' },
      },
    },
    value: {
      control: 'text',
      description: 'Pemole Code / HTML modal URL',
      table: {
        defaultValue: { summary: '' },
        type: { summary: 'string' },
      },
      type: { name: 'string', required: true },
    },
  },
  component: DetailsButton,
  title: 'Common/JSON Components (Marketing)/CMS/Utilities/DetailsButton',
  decorators: [withBackground],
  parameters: {
    controls: { expanded: true, sort: 'alpha' },
    docs: { page: README },
    sandbox: true,
  },
  tags: ['visual:check'],
};

export default meta;

type Story = StoryObj<typeof DetailsButton>;

export const Default: Story = { args: sampleData };
