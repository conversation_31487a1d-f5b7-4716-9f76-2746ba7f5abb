'use client';
import React, { useCallback, useContext } from 'react'; //@ts-ignore
import { AppContext } from '@ecom-next/sitewide/app-state-provider'; //@ts-ignore
import { Brands, CSSObject, forBrands, useTheme } from '@ecom-next/core/react-stitch';
import { Variant } from '../../../components/ComposableButton/types';
import { Typography } from '../../../components/Typography/components/index';
import { ComposableButton, ComposableButtonProps } from '../../../components/ComposableButton/index';
import { useViewportIsLarge } from '../../../hooks/useViewportIsLarge/index';
import { useIsBrand } from '../../../hooks/useBrand/index';
import { DetailsPrefixWrapper } from './details-styles';
import { detailsLinkStyles as detailsLinkBRStyles, prefixStyles as brPrefixStyles } from './br/styles';
import { DetailLinkProps } from './types';

export function getDetailsContent(brand: Brands, pemoleCode?: string, htmlModalUrl?: string): string {
  if (htmlModalUrl) return htmlModalUrl;

  if ([Brands.Athleta, Brands.BananaRepublic, Brands.OldNavy].includes(brand)) {
    const urlSuffix: Partial<Record<Brands, string>> = {
      at: 'athleta',
      br: 'bananarepublic',
      on: 'oldnavy',
    };
    return `https://secure-${urlSuffix[brand]}.gap.com/Asset_Archive/AllBrands/promoAPI/promo_lookup_details.html?promoId=${pemoleCode}`;
  }
  return `https://www.gap.com/Asset_Archive/AllBrands/promoAPI/promo_lookup_details.html?promoId=${pemoleCode}`;
}

export const DetailsButton = ({
  color,
  customStyles,
  detailsPrefixWrapperStyles,
  label,
  modalHeight,
  prefix,
  prefixColor,
  prefixVariant,
  tabIndex,
  value,
  useBaseStylesOnlyForBrands = [],
  className,
  ...otherProps
}: DetailLinkProps): React.JSX.Element => {
  const theme = useTheme(); //@ts-ignore
  const locale = useContext(AppContext)?.locale?.replace('-', '_');
  const isDesktopVP = useViewportIsLarge();
  const isValueHTTPLink = value.startsWith('http');
  const valueContainsLocale = value.includes('locale=');
  const valueContainsPemoleCode = value.includes('promoId=');
  const valueWithLocale = locale && isValueHTTPLink && !valueContainsLocale ? value.concat(`&locale=${locale}`) : value;
  const shouldUseBrandsBaseStyles = useIsBrand(...useBaseStylesOnlyForBrands);

  const detailsLinkData: ComposableButtonProps = {
    children: label,
    modalProps: {
      src: valueContainsPemoleCode ? valueWithLocale : value,
      height: modalHeight,
      width: '100%',
      closeButtonAriaLabel: 'close modal',
    },
    variant: Variant.flat,
  };

  const underlineCSS = React.useMemo(
    () =>
      label.includes('*')
        ? {}
        : ({
            textDecoration: 'underline',
            ':hover': { textDecoration: 'none' },
          } as CSSObject),
    [label]
  );

  const getBrandDetailsStyles = useCallback(
    (prefixParam: DetailLinkProps['prefix']): CSSObject => {
      const gapStyles: CSSObject = {
        ...underlineCSS,
        fontSize: isDesktopVP ? '12px' : '11px',
        minHeight: 'auto',
      };
      return forBrands(theme, {
        at: {
          ...underlineCSS,
          fontSize: isDesktopVP ? '12px' : '11px',
          lineHeight: isDesktopVP ? '16px' : '14px',
          minHeight: 'initial',
          padding: 0,
          marginLeft: prefixParam ? '5px' : 'unset',
          color: color ?? theme.color.wh,
          pointerEvents: 'auto',
          textUnderlineOffset: '2px',
        },
        br: isDesktopVP ? detailsLinkBRStyles.desktop : detailsLinkBRStyles.mobile,
        brfs: isDesktopVP ? detailsLinkBRStyles.desktop : detailsLinkBRStyles.mobile,
        gap: gapStyles,
        gapfs: gapStyles,
        on: {
          ...underlineCSS,
          fontSize: '10px',
          lineHeight: '12px',
          minHeight: '10px',
          textTransform: 'capitalize',
          letterSpacing: 'normal',
        },
        default: {
          ...underlineCSS,
          fontSize: '12px',
        },
      }) as CSSObject;
    },
    [theme, isDesktopVP, color, underlineCSS]
  );

  const focusStyles = {
    ':focus-visible': { outline: 'auto' },
  };

  const detailsLinkStyle: CSSObject = {
    fontWeight: 'normal',
    textTransform: 'none',
    span: {
      span: {
        paddingLeft: 'initial',
      },
    },
    zIndex: 1,
    ...(color ? { color } : null),
    ...getBrandDetailsStyles(prefix),
    ...(shouldUseBrandsBaseStyles ? {} : customStyles),
    ...focusStyles,
  };

  const getBrandDetailsPrefixStyles = useCallback((): CSSObject => {
    const gapPrefixStyles: CSSObject = {
      fontSize: isDesktopVP ? '12px' : '11px',
    };
    return forBrands(theme, {
      at: {
        fontSize: isDesktopVP ? '12px' : '11px',
        lineHeight: isDesktopVP ? '16px' : '14px',
        minHeight: 'initial',
        letterSpacing: 'normal',
        color: prefixColor || color || theme.color.wh,
      },
      gap: gapPrefixStyles,
      gapfs: gapPrefixStyles,
      br: isDesktopVP ? brPrefixStyles.desktop : brPrefixStyles.mobile,
      brfs: isDesktopVP ? brPrefixStyles.desktop : brPrefixStyles.mobile,
      on: {
        fontSize: isDesktopVP ? '12px' : '11px',
        letterSpacing: isDesktopVP ? '0.24px' : '0.22px',
        fontWeight: 500,
        lineHeight: isDesktopVP ? '18px' : '16px',
      },
      default: {
        fontSize: '12px',
      },
    }) as CSSObject;
  }, [theme, isDesktopVP]);

  const detailsPrefixStyle: CSSObject = {
    textTransform: 'none',
    fontWeight: 'normal',
    minHeight: 'auto',
    ...(prefixColor ? { color: prefixColor } : { color }),
    ...getBrandDetailsPrefixStyles(),
    ...(shouldUseBrandsBaseStyles ? {} : customStyles),
  };

  return (
    <>
      {prefix && (
        <DetailsPrefixWrapper css={shouldUseBrandsBaseStyles ? {} : detailsPrefixWrapperStyles}>
          <Typography css={detailsPrefixStyle} variant={prefixVariant || 'body5'}>
            {prefix}
          </Typography>
        </DetailsPrefixWrapper>
      )}
      <ComposableButton
        {...detailsLinkData}
        css={detailsLinkStyle}
        className={shouldUseBrandsBaseStyles ? undefined : className}
        {...otherProps}
        tabIndex={tabIndex}
      />
    </>
  );
};
