// @ts-nocheck
'use client';
import { CSSObject, Brands } from '@ecom-next/core/react-stitch';
import { TypographyVariant } from '../../../components/Typography';

export type DetailLinkProps = {
  className?: string;
  color?: string;
  customStyles?: CSSObject;
  detailsPrefixWrapperStyles?: CSSObject;
  label: string;
  modalHeight?: string;
  prefix?: string;
  prefixColor?: string;
  prefixVariant?: TypographyVariant;
  tabIndex?: number;
  useBaseStylesOnlyForBrands?: Brands[];
  value: string;
};
