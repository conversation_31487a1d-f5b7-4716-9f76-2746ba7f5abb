# RichText

- What is the `RichText`?
  - `RichText` is a helper for Amplience-configurable components. It accepts an HTML injection string from Amplience, and provides Amplience-generated styles for the `amp-cms--*` classes that are in the string.
- When should you use the component?
  - Use `RichText` when you are building a "Rich Text Area" into a CMS component.

## Default Behavior

- Because the `RichText` helper already contains a set of `amp-cms--*` classes, only the HTML injection string needs to be passed into the `RichText` component. The styles are then applied automatically.

```jsx
const htmlInjectionString =
  '<p class="amp-cms--p"><span class="amp-cms--body-1 amp-cms--avenir-next-regular">Hello.</span><span class="amp-cms--body-1 amp-cms--avenir-next-demi">This is some content.</span></p>';

const styledRichText = <RichText text={htmlInjectionString} />;
```

## Scalable Fonts

### Viewport Scalable

- `RichText` hooks into the Typography component which then enables text content to have scalable fonts. To enable scalable fonts, add the `scalableText` prop and specify the `parentMaxWidthPX` and `enable` to be true. Also be sure to add the `isDesktop` parameter so that it scales using the px value of either the desktop or the mobile depending on the breakpoint.

```jsx
<RichText isDesktop={IsDesktop()} scalableText={{ parentMaxWidthPx: '1280px', enable: true }} text='I am now scalable.' />
```

### Container Scalable

Sometimes you want to fluidly scale text relative to a container, with no concern for breakpoints. Examples might include banners and banner text where the text is machine readable instead of baked into an image.

- `RichText` can also scale based on the size of a container using [CSS container queries](https://developer.mozilla.org/en-US/docs/Web/CSS/CSS_containment/Container_queries).
- In order to use container based text-scaling the ancestor of the `RichText` component that the scaling should be based on needs to have a container defined.
- The `type` of `container` is how the program determines whether to scale by container or scale by viewport (type `viewport` is the default).
- The `containerSize` represents the width of the container when it matches the design spec. It is similar to the desktop scale point, but scoped to a container rather than the viewport width.
- By default `RichText` will use the desktop font-size for a given typography variant. Optionally, you can pass in a font-size in pixels as an override.

```jsx
<div css={{ containerType: 'size' }}>
  ...
  <RichText scalableText={{ type: 'container', containerSize: container_scale_point }} />
</div>
```

## Limitations

- The classes provided by `RichText` are currently copied from what is defined in Amplience. This is a temporary solution. Since it's likely we'll be copying and pasting multiple iterations here, refactoring css variables, breakpoints, etc may not be feasible in the early stages of the CMS.

## Technical Notes

- `RichText` uses `HTMLInjection` and [`Typography`](https://core-ui-main.apps.cfplatform.dev.azeus.gaptech.com/?path=/docs/mui_common-json-components-marketing-typography-scaling--style-guide) components under the hood.

- `RichText` accepts feature flags via `{useEnabledFeatures} from "@ecom-next/core/react-stitch"`.

## Breaking Changes Information

To view information regarding BREAKING CHANGES, please view the [Marketing UI MIGRATION.md file](/src/MIGRATION.md).
