// @ts-nocheck
export const textNavRichText =
  '<p class="amp-cms--p"><span class="amp-cms--headline-1">headline 1</span></p><p class="amp-cms--p"><span class="amp-cms--headline-2">headline 2</span></p><p class="amp-cms--p"><span class="amp-cms--headline-3">headline 3</span></p><p class="amp-cms--p"><span class="amp-cms--headline-4">headline 4</span></p><p class="amp-cms--p"><span class="amp-cms--headline-5">headline 5</span></p><p class="amp-cms--p"><span class="amp-cms--headline-6">headline 6</span></p><p class="amp-cms--p"><p class="amp-cms--p"><span class="amp-cms--headline-7">headline 7</span></p><span class="amp-cms--headlineAlt-1">headlineAlt 1</span><p class="amp-cms--p"><span class="amp-cms--headlineAlt-2">headlineAlt 2</span><p class="amp-cms--p"><span class="amp-cms--headlineAlt-3">headlineAlt 3</span><p class="amp-cms--p"><span class="amp-cms--headlineAlt-4">headlineAlt 4</span><p class="amp-cms--p"><span class="amp-cms--eyebrow-1">eyebrow 1</span></p><p class="amp-cms--p"><span class="amp-cms--eyebrow-2">eyebrow 2</span></p><p class="amp-cms--p"><span class="amp-cms--eyebrow-3">eyebrow 3</span></p><p class="amp-cms--p"><span class="amp-cms--promo-1">promo 1</span></p><span class="amp-cms--subhead-1">subhead 1</span></p><p class="amp-cms--p"><span class="amp-cms--subhead-2">subhead 2</span></p><p class="amp-cms--p"><span class="amp-cms--subhead-3">subhead 3</span></p><p class="amp-cms--p"><span class="amp-cms--body-1">body 1</span></p><p class="amp-cms--p"><span class="amp-cms--body-2">body 2</span></p><p class="amp-cms--p"><span class="amp-cms--body-3">body 3</span></p><p class="amp-cms--p"><span class="amp-cms--body-4">body 4</span></p><p class="amp-cms--p"><span class="amp-cms--body-5">body 5</span></p><p class="amp-cms--p"><span class="amp-cms--legal-copy">legal (deprecated)</span></p><p class="amp-cms--p"><span class="amp-cms--quote">quote (deprecated)</span></p>';

export const promoTileRichText =
  '<p class="amp-cms--p"><span class="amp-cms--promo-1">Promo Tile</span></p><p class="amp-cms--p"><span class="amp-cms--body-1">A demo of the Left Nav Promo</span></p>';

export const seoRichText =
  '<p class="amp-cms--p"><span class="amp-cms--legal-copy">Shopping for a bikini top shouldn’t be stressful––it should be fun. Get outside in styles that are supportive, comfortable, and designed for athletes. Whether you’re a pro-surfer, SUP-enthusiast, or just like hitting the beach, we have a range of swim tops in all the styles and colors you could want.</span></p><p class="amp-cms--p"><span class="amp-cms--legal-copy">From twist-up, to halter tops, to high-neck, to triangle, and more, our assortment reaches far and wide. You can shop by the type of coverage you want to have, such as light coverage, medium coverage, or high coverage. We also offer bra-sized cup swim tops in sizes B-DD, because you shouldn’t have to compromise on the things you want most in a bra, just because you’re in the water. The hidden underwire supports like your favorite bra, while the styles are cute and flattering like your favorite swimsuit should be.</span></p><p class="amp-cms--p"><span class="amp-cms--legal-copy">Removeable QuickDri Cups are in many of our women’s bathing suit tops and are our fastest drying cups, ever. They never fold over and maintain their perfect shape, wear after wear. Our swim styles are made with LYCRA® XTRA LIFE™ SPANDEX which is an ultra-resilient fabric that snaps back like a pro and lasts 5 to 10X longer. It dries quickly, and is rated UPF 50+ which offers excellent protection against harmful UV rays, so you can stay out there longer and conquer another wave.</span></p>';

export const complexRichText =
  '<p class="amp-cms--p"><span class="amp-cms--headline-1">Welcome!</span> <span class="amp-cms--eyebrow-2">and Goodbye</span></p><p class="amp-cms--p"><span class="amp-cms--subhead-1">Here is a subhead.</span> <span class="amp-cms--body-1">The quick brown fox jumps over the lazy dog.</span></p><p class="amp-cms--p"><span class="amp-cms--body-1">Body 1 text goes here!</span><span class="amp-cms--body-1"> Here is a </span><a href="www.gap.com" class="amp-cms--body-2">Hyperlink</a></p><p class="amp-cms--p"><span class="amp-cms--legal-copy">legacy legal copy that should be the same styles as body 5 (this is legal)</span></p><p class="amp-cms--p"><span class="amp-cms--body-5">legacy legal copy that should be the same styles as body 5 (this is body 5)</span></p>';

export const hyperLink =
  '<p class="amp-cms--p"><span class="amp-cms--body-1"> Here is a </span><a href="www.gap.com" class="amp-cms--body-2">Hyperlink</a></p>';

export const disableTextAlign = '<p class="amp-cms--p" style="text-align:right;">Text Align Example</p>';

export const inlineFontWeight =
  '<p class="amp-cms--p" style="text-align:left;"><span class="amp-cms--body-1" style="font-weight:200;">Font Weight Example</span></p>';
