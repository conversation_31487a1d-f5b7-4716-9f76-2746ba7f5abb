// @ts-nocheck
import { Theme } from '@ecom-next/core/react-stitch';
import { TypographyVariantConfig } from '../../../../components/Typography';
import { getBaseStyles } from './RichTextWrapper';

const mockGapTheme = () =>
  ({
    brand: 'gap',
    brandFont: {
      MozOsxFontSmoothing: 'grayscale',
      WebkitFontSmoothing: 'antialiased',
      fontFamily: "'Helvetica Neue', Helvetica, Arial, Roboto, sans-serif",
    },
    color: {
      wh: '#FFFFFF',
      b1: '#2B2B2B',
      bk: '#000000',
      alpha00: 'transparent',
    },
  }) as Theme;

describe('rich-text-styles', () => {
  it('should get rte base styles for gap', () => {
    const theme: Theme = mockGapTheme();
    const legalConfig: TypographyVariantConfig = {
      fontSize: [10, 12],
      lineHeight: [14, 16],
      minFontSize: [10, 12],
    };
    expect(getBaseStyles(theme, legalConfig, true)).toEqual({
      '.amp-cms--p': {
        padding: 0,
        margin: 0,
        lineHeight: 0,
        '& > span': {
          display: 'inline',
          whiteSpace: 'break-spaces',
        },
        '& a': {
          textDecoration: 'underline',
          position: 'relative',
          zIndex: 2,
          '&:hover': {
            textDecoration: 'none',
          },
          pointerEvents: 'auto',
        },
      },
      '.amp-cms--legal-copy': {
        color: '#2B2B2B',
        fontFamily: "'Helvetica Neue', Helvetica, Arial, Roboto, sans-serif",
        WebkitFontSmoothing: 'antialiased',
        MozOsxFontSmoothing: 'grayscale',
        fontSize: '12px',
        lineHeight: 1.3333333333333333,
        letterSpacing: 0,
      },
      '& sup': {
        verticalAlign: 'top',
        display: 'inline-block',
        marginTop: '-0.25ex',
      },
      '& sub': {
        verticalAlign: 'bottom',
        display: 'inline-block',
        marginBottom: '-0.25ex',
      },
    });
  });
});
