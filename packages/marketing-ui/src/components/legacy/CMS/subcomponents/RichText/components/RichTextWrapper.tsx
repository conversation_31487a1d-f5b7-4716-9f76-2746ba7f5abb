// @ts-nocheck
'use client';
import { CSSObject, Theme, styled } from '@ecom-next/core/react-stitch';
import { ScalableText, TypographyVariant, TypographyVariantConfig, getTypographyConfig, getTypographyStyles } from '../../../../components/Typography';
import { RichTextWrapperProps } from '../types';

const TYPOGRAPHY_VARIANTS: TypographyVariant[] = [
  'F0',
  'F1',
  'F2',
  'FN1',
  'body1',
  'body2',
  'body3',
  'body4',
  'body5',
  'eyebrow1',
  'eyebrow2',
  'eyebrow3',
  'headline1',
  'headline2',
  'headline3',
  'headline4',
  'headline5',
  'headline6',
  'headline7',
  'headlineAlt1',
  'headlineAlt2',
  'headlineAlt3',
  'headlineAlt4',
  'headlineAlt5',
  'headlineAlt6',
  'headlineAlt7',
  'promo1',
  'promo2',
  'promoAlt1',
  'subhead1',
  'subhead2',
  'subhead3',
] as TypographyVariant[];

function getRTEVariantClassName(variant: TypographyVariant): string | undefined {
  if (
    variant.includes('F') ||
    variant.includes('body') ||
    variant.includes('headline') ||
    variant.includes('headlineAlt') ||
    variant.includes('subhead') ||
    variant.includes('eyebrow') ||
    variant.includes('promo') ||
    variant.includes('promoAlt')
  ) {
    const variantName = variant.replace(/[^0-9]/g, '');
    const variantNumber = variant.replace(variantName, '');

    if (variant.includes('F')) {
      return `.amp-cms--${variantNumber}-${variantName}`.toLowerCase();
    }

    return `.amp-cms--${variantNumber}-${variantName}`;
  }

  return undefined;
}

export const getBaseStyles = (theme: Theme, legalConfig: TypographyVariantConfig | undefined, isDesktop: boolean, scalableText?: ScalableText): CSSObject => ({
  '.amp-cms--p': {
    padding: 0,
    margin: 0,
    lineHeight: 0,
    '& > span': {
      // includes vertical margin/padding for different line-heights
      display: 'inline',
      whiteSpace: 'break-spaces',
    },
    '& a': {
      textDecoration: 'underline',
      position: 'relative',
      zIndex: 2,
      '&:hover': {
        textDecoration: 'none',
      },
      pointerEvents: 'auto',
    },
  },
  // LEGACY support for legal
  '.amp-cms--legal-copy': getTypographyStyles({
    variant: 'body5',
    theme,
    config: legalConfig,
    isDesktop,
    scalableText,
  }),
  '& sup': {
    verticalAlign: 'top',
    display: 'inline-block',
    marginTop: theme.brand === 'on' ? '-0.20ex' : '-0.25ex',
  },
  '& sub': {
    verticalAlign: 'bottom',
    display: 'inline-block',
    marginBottom: theme.brand === 'on' ? '-0.20ex' : '-0.25ex',
  },
});

export const RichTextWrapper = styled.div<RichTextWrapperProps>(
  ({ theme, isDesktop = true, scalableText, customStyles = {}, color, fontWeight, enabledFeatures }) => {
    const overriddenStyles: CSSObject = {};

    if (color) {
      overriddenStyles.color = `${color} !important`;
    }

    if (fontWeight) {
      overriddenStyles.fontWeight = fontWeight;
    }
    const typographyConfig = getTypographyConfig(theme.brand, {}, enabledFeatures);

    const baseStyles: CSSObject = getBaseStyles(theme, typographyConfig.body5, isDesktop, scalableText);

    Object.assign(baseStyles, customStyles);

    return TYPOGRAPHY_VARIANTS.reduce((styles: CSSObject, variant) => {
      const className = getRTEVariantClassName(variant);
      if (className) {
        return {
          ...styles,
          ...overriddenStyles,
          '& *': {
            ...overriddenStyles,
          },
          [className]: {
            ...(getTypographyStyles({
              variant,
              theme,
              config: typographyConfig[variant],
              isDesktop,
              scalableText,
              enabledFeatures,
            }) as CSSObject),
            ...overriddenStyles,
            ...customStyles,
          },
        };
      }
      return styles;
    }, baseStyles);
  }
);
