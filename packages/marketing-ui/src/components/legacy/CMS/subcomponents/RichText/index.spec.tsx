// @ts-nocheck
import React from 'react';
import { screen, render } from 'test-utils';
import { SMALL } from '@ecom-next/core/breakpoint-provider';
import { getTypographyFontStyles } from '../../../components/Typography/styles/helpers';
import { textNavRichText, promoTileRichText, disableTextAlign, hyperLink, inlineFontWeight } from './__fixtures__/test-data';
import { RichText } from '.';

jest.mock('../../../components/Typography/styles/helpers');
const mockGetTypographyFontStyles = getTypographyFontStyles as jest.Mock;

describe('RichText', () => {
  describe('Snapshot Tests', () => {
    it('matches hyperlink snapshot', () => {
      const { container } = render(<RichText text={hyperLink} />);
      expect(container).toMatchSnapshot();
    });

    it('matches promo tile snapshot', () => {
      const { container } = render(<RichText text={promoTileRichText} />);
      expect(container).toMatchSnapshot();
    });
  });

  describe('Prop Functionality', () => {
    describe('text', () => {
      describe('string values', () => {
        it('renders string', () => {
          render(<RichText text={textNavRichText} />);
          expect(screen.getByText('headline 1')).toBeInTheDocument();
        });
      });

      describe('text value objects', () => {
        it('renders defaultText on desktop', () => {
          render(<RichText text={{ defaultText: 'desktop', mobileOverride: 'mobile' }} />);
          expect(screen.getByText('desktop')).toBeInTheDocument();
        });

        it('renders mobileOverride on mobile', () => {
          render(<RichText text={{ defaultText: 'desktop', mobileOverride: 'mobile' }} />, { breakpoint: SMALL });
          expect(screen.getByText('mobile')).toBeInTheDocument();
        });

        it('renders defaultText on mobile if no mobileOverride', () => {
          render(<RichText text={{ defaultText: 'desktop' }} />, {
            breakpoint: SMALL,
          });
          expect(screen.getByText('desktop')).toBeInTheDocument();
        });

        it('renders mobileOverride on mobile if mobileOverride is present', () => {
          render(<RichText text={{ mobileOverride: 'mobile' }} />, {
            breakpoint: SMALL,
          });
          expect(screen.getByText('mobile')).toBeInTheDocument();
        });

        it('renders nothing on desktop if defaultText is undefined and mobileOverride is present', () => {
          const { container } = render(<RichText text={{ mobileOverride: 'mobile' }} />);
          expect(container.firstChild).toBeEmptyDOMElement();
        });

        it('renders nothing on desktop if object is empty', () => {
          const { container } = render(<RichText text={{}} />);
          expect(container.firstChild).toBeEmptyDOMElement();
        });
      });

      describe('undefined', () => {
        it('renders nothing', () => {
          const { container } = render(<RichText text={undefined} />);
          expect(container.firstChild).toBeEmptyDOMElement();
        });
      });
    });

    describe('disableTextAlign', () => {
      it("doesn't disable text-align", () => {
        render(<RichText text={disableTextAlign} />);
        expect(screen.getByText('Text Align Example')).toHaveStyle({
          'text-align': 'right',
        });
      });

      it('disables text align', () => {
        render(<RichText disableTextAlign text={disableTextAlign} />);
        expect(screen.getByText('Text Align Example')).not.toHaveStyle({
          'text-align': 'right',
        });
      });
    });

    describe('disabledFontWeight', () => {
      it("doesn't disable font weight", () => {
        render(<RichText text={inlineFontWeight} />);
        expect(screen.getByText('Font Weight Example')).toHaveStyle({
          'font-weight': '200',
        });
      });

      it('disables inline font weight', () => {
        render(<RichText disabledFontWeight text={inlineFontWeight} />);
        expect(screen.getByText('Font Weight Example')).not.toHaveStyle({
          'font-weight': '200',
        });
      });
    });

    describe('scalableText', () => {
      it('enables scalable text', () => {
        mockGetTypographyFontStyles.mockClear();
        const scalableTextTestData = { enable: true, desktopScalingPoint: 1440 };
        render(<RichText scalableText={scalableTextTestData} text={disableTextAlign} />);
        const { scalableText } = mockGetTypographyFontStyles.mock.calls[0][0];
        expect(scalableText).toMatchObject(scalableTextTestData);
      });
    });
  });
});
