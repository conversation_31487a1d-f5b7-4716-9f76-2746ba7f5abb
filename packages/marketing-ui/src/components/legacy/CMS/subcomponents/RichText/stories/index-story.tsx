// @ts-nocheck
'use client';
import React, { useState } from 'react';
import { Story } from '@storybook/react';
import styled from '@emotion/styled';
import { LARGE, XLARGE, BreakpointContext, BreakpointProvider } from '@ecom-next/core/breakpoint-provider';
import { VIEW_WIDTH_BASE_DESKTOP } from '../../../../components/Typography';
import useDebouncedWindowResize from '../../../../helper/hooks/useDebouncedWindowResize';
import { RichText } from '..';
import { promoTileRichText, seoRichText, complexRichText } from '../__fixtures__/test-data';
import README from '../README.mdx';

const CONTAINER_SIZE = VIEW_WIDTH_BASE_DESKTOP;

export default {
  title: 'Common/JSON Components (Marketing)/CMS/Utilities/RichText',
  parameters: {
    docs: { page: README },
    knobs: { disable: true },
  },
  tags: ['exclude'],
  argTypes: {
    useContainer: {
      name: `Use max-width container (${CONTAINER_SIZE}px)`,
      control: { type: 'boolean' },
    },
    scalable: {
      name: 'scalable text: enable',
      control: { type: 'boolean' },
    },
    desktopBreakpoint: {
      name: 'Breakpoint Threshold',
      options: [LARGE, XLARGE],
      control: {
        type: 'select',
      },
    },
    maxWidth: {
      name: 'Typography max width',
      control: { type: 'number' },
    },
    htmlText: {
      name: 'HTML Injection',
    },
  },
};

const StyledContainerMaxWidth = styled.div({
  maxWidth: CONTAINER_SIZE,
  background: '#EEE',
  margin: '0 auto 0',
});

const StyledContainer = styled.div({
  background: '#EEE',
  backgroundSize: 'cover',
});

export const TypographyTemplate: Story = ({ useContainer, scalable, htmlText }): JSX.Element => {
  const [size, setSize] = useState(window.innerWidth);

  useDebouncedWindowResize(() => {
    setSize(window.innerWidth);
  }, 100);

  return (
    <BreakpointProvider>
      <BreakpointContext.Consumer>
        {value => {
          if (useContainer) {
            return (
              <div>
                <p>Storybook Window Size: {size}px</p>
                <StyledContainerMaxWidth>
                  <RichText
                    isDesktop={value.greaterOrEqualTo('large')}
                    scalableText={{
                      parentMaxWidthPx: useContainer ? CONTAINER_SIZE : undefined,
                      enable: scalable,
                    }}
                    text={htmlText}
                  />
                </StyledContainerMaxWidth>
              </div>
            );
          }
          return (
            <div>
              <p>Storybook Window Size: {size}px</p>
              <StyledContainer>
                <RichText
                  isDesktop={value.greaterOrEqualTo('large')}
                  scalableText={{
                    parentMaxWidthPx: useContainer ? CONTAINER_SIZE : undefined,
                    enable: scalable,
                  }}
                  text={htmlText}
                />
              </StyledContainer>
            </div>
          );
        }}
      </BreakpointContext.Consumer>
    </BreakpointProvider>
  );
};

export const LegalTextExample = TypographyTemplate.bind({});
LegalTextExample.argTypes = {
  desktopBreakpoint: { table: { disable: true } },
  maxWidth: { table: { disable: true } },
};
LegalTextExample.args = {
  useContainer: false,
  scalable: false,
  htmlText: seoRichText,
};

const RichTextTemplate: Story = ({ scalable, desktopBreakpoint, maxWidth, htmlText, color, fontWeight }): JSX.Element => {
  const Wrapper = styled.div({
    maxWidth,
  });

  return (
    <BreakpointProvider>
      <BreakpointContext.Consumer>
        {value => (
          <Wrapper>
            <RichText
              color={color}
              fontWeight={fontWeight}
              isDesktop={value.greaterOrEqualTo(desktopBreakpoint)}
              scalableText={{ parentMaxWidthPx: maxWidth, enable: scalable }}
              text={htmlText}
            />
          </Wrapper>
        )}
      </BreakpointContext.Consumer>
    </BreakpointProvider>
  );
};

export const Default = RichTextTemplate.bind({});
Default.argTypes = {
  useContainer: { table: { disable: true } },
  fontWeight: { options: [100, 200, 300, 400, 500, 600, 700, 800, 900] },
  color: { type: 'string' },
};
Default.args = {
  scalable: true,
  desktopBreakpoint: LARGE,
  maxWidth: 1280,
  htmlText: complexRichText,
  color: undefined,
  fontWeight: undefined,
};

export const Promo = RichTextTemplate.bind({});
Promo.argTypes = { useContainer: { table: { disable: true } } };
Promo.args = {
  scalable: true,
  desktopBreakpoint: LARGE,
  maxWidth: 1280,
  htmlText: promoTileRichText,
};
