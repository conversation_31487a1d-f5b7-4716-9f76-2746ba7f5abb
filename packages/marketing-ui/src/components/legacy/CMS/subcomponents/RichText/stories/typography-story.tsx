// @ts-nocheck
'use client';
import { LARGE, XLARGE } from '@ecom-next/core/breakpoint-provider';
import { VIEW_WIDTH_BASE_DESKTOP } from '../../../../components/Typography';
import { textNavRichText } from '../__fixtures__/test-data';
import { TypographyTemplate } from './index-story';
import README from '../README.mdx';

const CONTAINER_SIZE = VIEW_WIDTH_BASE_DESKTOP;

export default {
  title: 'Common/JSON Components (Marketing)/CMS/Utilities/RichText',
  parameters: {
    docs: { page: README },
    knobs: { disable: true },
  },
  tags: ['exclude'],
  argTypes: {
    useContainer: {
      name: `Use max-width container (${CONTAINER_SIZE}px)`,
      control: { type: 'boolean' },
    },
    scalable: {
      name: 'scalable text: enable',
      control: { type: 'boolean' },
    },
    desktopBreakpoint: {
      name: 'Breakpoint Threshold',
      options: [LARGE, XLARGE],
      control: {
        type: 'select',
      },
    },
    maxWidth: {
      name: 'Typography max width',
      control: { type: 'number' },
    },
    htmlText: {
      name: 'HTML Injection',
    },
  },
};

export const TypographyExample = TypographyTemplate.bind({});
TypographyExample.argTypes = {
  desktopBreakpoint: { table: { disable: true } },
  maxWidth: { table: { disable: true } },
};
TypographyExample.args = {
  useContainer: false,
  scalable: false,
  htmlText: textNavRichText,
};
