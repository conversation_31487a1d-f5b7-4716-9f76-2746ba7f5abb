// @ts-nocheck
'use client';
import { CSSObject } from '@ecom-next/core/react-stitch';
import { EnabledFeaturesType } from '@ecom-next/core/legacy/feature-flags';
import { TextValue } from '../../types/amplience';

export interface RichTextWrapperProps extends React.DetailedHTMLProps<React.HTMLAttributes<HTMLDivElement>, HTMLDivElement> {
  isDesktop?: boolean;
  scalableText?: ViewportScalableText | ContainerScalableText;
  customStyles?: CSSObject;
  fontWeight?: CSSObject['fontWeight'];
  enabledFeatures?: EnabledFeaturesType;
}

export type ViewportScalableText = {
  type?: 'viewport';
  enable?: boolean;
  parentMaxWidthPx?: number;
  desktopBreakpoint?: 'large' | 'x-large';
  maxSizePx?: number;
  minSizePx?: number;
  disableInfiniteScaling?: boolean;
  desktopScalingPoint?: number;
};

export type ContainerScalableText = {
  type: 'container';
  containerSize: number; // size in px
  fontSize?: number; // size in px
};

export interface RichTextProps extends RichTextWrapperProps {
  text?: string | TextValue;
  disableTextAlign?: boolean;
  disabledFontWeight?: boolean;
}

export function isTextValue(text: RichTextProps['text']): text is TextValue {
  return typeof text === 'object' && (Boolean(text.defaultText) || Boolean(text.mobileOverride));
}
