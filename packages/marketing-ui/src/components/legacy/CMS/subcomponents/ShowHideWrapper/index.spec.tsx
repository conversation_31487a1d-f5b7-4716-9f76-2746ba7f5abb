// @ts-nocheck
import React from 'react';
import { render } from 'test-utils';
import { SMALL, LARGE, XLARGE } from '@ecom-next/core/breakpoint-provider';
import { ShowHideBasedOnScreenSizeProps } from './types';
import { ShowHideWrapper } from '.';

describe('ShouldHideWrapper Component', () => {
  describe('should match snapshots', () => {
    it('always show', () => {
      const showHideBasedOnScreenSize: ShowHideBasedOnScreenSizeProps = 'alwaysShow';
      const { container } = render(
        <ShowHideWrapper breakpoint='large' showHideBasedOnScreenSize={showHideBasedOnScreenSize}>
          Test
        </ShowHideWrapper>
      );
      expect(container).toMatchSnapshot();
    });
    it('hide on Mobile', () => {
      const showHideBasedOnScreenSize: ShowHideBasedOnScreenSizeProps = 'hideOnMobile';
      const { container } = render(
        <ShowHideWrapper breakpoint='large' showHideBasedOnScreenSize={showHideBasedOnScreenSize}>
          Test
        </ShowHideWrapper>
      );
      expect(container).toMatchSnapshot();
    });
    it('hide on Desktop', () => {
      const showHideBasedOnScreenSize: ShowHideBasedOnScreenSizeProps = 'hideOnDesktop';
      const { container } = render(
        <ShowHideWrapper breakpoint='large' showHideBasedOnScreenSize={showHideBasedOnScreenSize}>
          Test
        </ShowHideWrapper>
      );
      expect(container).toMatchSnapshot();
    });
  });

  describe('Props: ', () => {
    describe('Breakpoint', () => {
      const testString1 = 'Lorem ipsum dolor sit amet';
      const testString2 = 'consectetur adipiscing elit';
      const hideOnMobile: ShowHideBasedOnScreenSizeProps = 'hideOnMobile';
      const TestComponent = (): JSX.Element => (
        <div>
          <span>{testString1}</span>
          <span>{testString2}</span>
        </div>
      );

      describe('default behavior should set mobile breakpoint to 768', () => {
        it('and hide content below 768px', () => {
          const { queryByText } = render(
            <ShowHideWrapper breakpoint='large' showHideBasedOnScreenSize={hideOnMobile}>
              <TestComponent />
            </ShowHideWrapper>,
            {
              breakpoint: SMALL,
            }
          );
          expect(queryByText(testString1)).not.toBeInTheDocument();
          expect(queryByText(testString2)).not.toBeInTheDocument();
        });

        it('and show content above 768', () => {
          const { queryByText } = render(
            <ShowHideWrapper breakpoint='large' showHideBasedOnScreenSize={hideOnMobile}>
              <TestComponent />
            </ShowHideWrapper>,
            {
              breakpoint: LARGE,
            }
          );
          expect(queryByText(testString1)).toBeInTheDocument();
          expect(queryByText(testString2)).toBeInTheDocument();
        });

        it('and show content above 1024', () => {
          const { queryByText } = render(
            <ShowHideWrapper breakpoint='large' showHideBasedOnScreenSize={hideOnMobile}>
              <TestComponent />
            </ShowHideWrapper>,
            {
              breakpoint: XLARGE,
            }
          );
          expect(queryByText(testString1)).toBeInTheDocument();
          expect(queryByText(testString2)).toBeInTheDocument();
        });
      });

      describe('when IsDesktop is passed should set mobile breakpoint to 768', () => {
        it('and hide content below 768px', () => {
          const { queryByText } = render(
            <ShowHideWrapper breakpoint='large' showHideBasedOnScreenSize={hideOnMobile}>
              <TestComponent />
            </ShowHideWrapper>,
            {
              breakpoint: SMALL,
            }
          );
          expect(queryByText(testString1)).not.toBeInTheDocument();
          expect(queryByText(testString2)).not.toBeInTheDocument();
        });

        it('and show content above 768', () => {
          const { queryByText } = render(
            <ShowHideWrapper breakpoint='large' showHideBasedOnScreenSize={hideOnMobile}>
              <TestComponent />
            </ShowHideWrapper>,
            {
              breakpoint: LARGE,
            }
          );
          expect(queryByText(testString1)).toBeInTheDocument();
          expect(queryByText(testString2)).toBeInTheDocument();
        });

        it('and show content above 1024', () => {
          const { queryByText } = render(
            <ShowHideWrapper breakpoint='large' showHideBasedOnScreenSize={hideOnMobile}>
              <TestComponent />
            </ShowHideWrapper>,
            {
              breakpoint: XLARGE,
            }
          );
          expect(queryByText(testString1)).toBeInTheDocument();
          expect(queryByText(testString2)).toBeInTheDocument();
        });
      });

      describe('when IsDesktopXLarge is passed should set mobile breakpoint to 1024', () => {
        it('and hide content below 768px', () => {
          const { queryByText } = render(
            <ShowHideWrapper breakpoint='x-large' showHideBasedOnScreenSize={hideOnMobile}>
              <TestComponent />
            </ShowHideWrapper>,
            {
              breakpoint: SMALL,
            }
          );
          expect(queryByText(testString1)).not.toBeInTheDocument();
          expect(queryByText(testString2)).not.toBeInTheDocument();
        });

        it('and hide content between 768px and 1024px', () => {
          const { queryByText } = render(
            <ShowHideWrapper breakpoint='x-large' showHideBasedOnScreenSize={hideOnMobile}>
              <TestComponent />
            </ShowHideWrapper>,
            {
              breakpoint: SMALL,
            }
          );
          expect(queryByText(testString1)).not.toBeInTheDocument();
          expect(queryByText(testString2)).not.toBeInTheDocument();
        });

        it('and show content above 1024', () => {
          const { queryByText } = render(
            <ShowHideWrapper breakpoint='x-large' showHideBasedOnScreenSize={hideOnMobile}>
              <TestComponent />
            </ShowHideWrapper>,
            {
              breakpoint: XLARGE,
            }
          );
          expect(queryByText(testString1)).toBeInTheDocument();
          expect(queryByText(testString2)).toBeInTheDocument();
        });
      });
    });
  });
});
