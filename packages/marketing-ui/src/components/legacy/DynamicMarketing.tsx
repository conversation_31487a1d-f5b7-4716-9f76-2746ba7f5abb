// @ts-nocheck
'use client';
import React, { createContext, useContext } from 'react';
import { Brands } from '@ecom-next/core/react-stitch';
import { useAppState, PageType } from '@ecom-next/sitewide/app-state-provider';

export type DynamicMarketingProps = {
  name: string;
  type: string;
  data: Record<string, unknown>;
  meta?: Record<string, unknown> | string;
};

type DynamicMarketingComponentProps = {
  brandName: Brands;
  market: string;
  pageType: PageType;
  data?: { [key: string]: any };
};

type DynamicMarketingComponent = (props: DynamicMarketingComponentProps) => JSX.Element | null;

export const DynamicMarketingContext = createContext<DynamicMarketingComponent>(() => null);

export const DynamicMarketingProvider = DynamicMarketingContext.Provider;

export function DynamicMarketing(props: DynamicMarketingProps): JSX.Element {
  const { brandName, market, pageType } = useAppState();
  const DynamicMarketingComponent = useContext(DynamicMarketingContext);
  return <DynamicMarketingComponent {...props} brandName={brandName} market={market} pageType={pageType} />;
}
