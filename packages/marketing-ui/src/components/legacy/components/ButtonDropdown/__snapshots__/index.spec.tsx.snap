// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`<ButtonDropdown /> snapshots for Athleta brand closed dropdown 1`] = `
.emotion-0 {
  font-size: 0.9735rem;
  width: 100%;
}

.emotion-0:not(:first-child) {
  margin-left: none;
}

.emotion-1 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: none;
  transition: none;
  box-sizing: border-box;
  border: 1.5px solid transparent;
  border-radius: 0;
  font-size: 16px;
  font-weight: 500;
  letter-spacing: 0px;
  min-height: auto;
  max-height: 60px;
  line-height: 1.25;
  padding: 22px 40px;
  width: 100%;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #000000;
  background: transparent;
  border-color: #000000;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  width: 100%;
  text-align: center;
  box-sizing: border-box;
  color: red;
  background-color: #000000;
  height: 60px;
  font-size: 16px;
}

.emotion-1:focus {
  outline: none;
}

.emotion-1>span {
  padding: 1px 0;
}

.emotion-1 svg path {
  fill: #FFFFFF!important;
}

.emotion-1:hover {
  -webkit-text-decoration: underline;
  text-decoration: underline;
}

.emotion-1>span {
  padding: 0;
}

.emotion-3 {
  display: inline-block;
  height: 10px;
  width: 10px;
  min-height: 10px;
  min-width: 10px;
  margin-left: 0.65rem;
  padding-top: 2.4px;
}

.emotion-3 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-3 svg path {
  fill: inherit;
}

.emotion-3 svg rect {
  fill: inherit;
}

.emotion-4 {
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
  background: #FFFFFF;
  box-sizing: border-box;
  list-style-type: none;
  min-width: 100%;
  overflow: hidden;
  position: relative;
  z-index: 390;
  border-width: 0;
  border-style: solid;
  text-align: left;
  letter-spacing: 0;
  pointer-events: auto;
  padding: 0;
  max-height: 0;
  -webkit-transition: max-height .5s ease-out,visibility 0s .5s;
  transition: max-height .5s ease-out,visibility 0s .5s;
  visibility: hidden;
  color: pink;
}

.emotion-5 {
  box-sizing: border-box;
  width: 100%;
  border-bottom: 1px solid #000000;
  border-left: 1px solid #333333;
  border-right: 1px solid #333333;
  height: 60px;
}

.emotion-5:last-child {
  border-bottom: 1px solid #333333;
}

.emotion-6 {
  cursor: pointer;
  display: block;
  padding: 1.375rem 0;
  -webkit-text-decoration: none;
  text-decoration: none;
  -webkit-transition: color 0.3s ease;
  transition: color 0.3s ease;
  white-space: nowrap;
  font-size: 16px;
  text-align: center;
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  color: orange;
}

.emotion-6:hover,
.emotion-6:focus {
  -webkit-text-decoration: underline;
  text-decoration: underline;
}

<div
  class="emotion-0"
  data-testid="button-dropdown-container"
>
  <button
    aria-expanded="false"
    class="emotion-1"
    color="primary"
  >
    <span
      class="emotion-2"
    >
      heading text
    </span>
    <span
      aria-hidden="true"
      class="emotion-3"
    >
      <svg
        fill="none"
        viewBox="0 0 12 12"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          clip-rule="evenodd"
          d="M7 0H5v5H0v2h5v5h2V7h5V5H7V0z"
          fill="#000000"
          fill-rule="evenodd"
        />
      </svg>
    </span>
  </button>
  <ul
    aria-hidden="true"
    class="emotion-4"
  >
    <li
      class="emotion-5"
    >
      <a
        breakpoint="desktop"
        class="emotion-6"
        href="girls"
        target="_blank"
      >
        Girls
      </a>
    </li>
    <li
      class="emotion-5"
    >
      <a
        breakpoint="desktop"
        class="emotion-6"
        href="boys"
        target="_self"
      >
        Boys
      </a>
    </li>
  </ul>
</div>
`;

exports[`<ButtonDropdown /> snapshots for Athleta brand mobile closed dropdown 1`] = `
.emotion-0 {
  font-size: 0.9735rem;
  width: 100%;
}

.emotion-0:not(:first-child) {
  margin-left: none;
}

.emotion-1 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: none;
  transition: none;
  box-sizing: border-box;
  border: 1.5px solid transparent;
  border-radius: 0;
  font-size: 16px;
  font-weight: 500;
  letter-spacing: 0px;
  min-height: auto;
  max-height: 60px;
  line-height: 1.25;
  padding: 22px 40px;
  width: 100%;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #000000;
  background: transparent;
  border-color: #000000;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  width: 100%;
  text-align: center;
  box-sizing: border-box;
  color: red;
  background-color: #000000;
  height: 60px;
  font-size: 16px;
}

.emotion-1:focus {
  outline: none;
}

.emotion-1>span {
  padding: 1px 0;
}

.emotion-1 svg path {
  fill: #FFFFFF!important;
}

.emotion-1:hover {
  -webkit-text-decoration: underline;
  text-decoration: underline;
}

.emotion-1>span {
  padding: 0;
}

.emotion-3 {
  display: inline-block;
  height: 10px;
  width: 10px;
  min-height: 10px;
  min-width: 10px;
  margin-left: 0.65rem;
  padding-top: 2.4px;
}

.emotion-3 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-3 svg path {
  fill: inherit;
}

.emotion-3 svg rect {
  fill: inherit;
}

.emotion-4 {
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
  background: #FFFFFF;
  box-sizing: border-box;
  list-style-type: none;
  min-width: 100%;
  overflow: hidden;
  position: relative;
  z-index: 390;
  border-width: 0;
  border-style: solid;
  text-align: left;
  letter-spacing: 0;
  pointer-events: auto;
  padding: 0;
  max-height: 0;
  -webkit-transition: max-height .5s ease-out,visibility 0s .5s;
  transition: max-height .5s ease-out,visibility 0s .5s;
  visibility: hidden;
  color: pink;
}

.emotion-5 {
  box-sizing: border-box;
  width: 100%;
  border-bottom: 1px solid #000000;
  border-left: 1px solid #333333;
  border-right: 1px solid #333333;
  height: 60px;
}

.emotion-5:last-child {
  border-bottom: 1px solid #333333;
}

.emotion-6 {
  cursor: pointer;
  display: block;
  padding: 1.375rem 0;
  -webkit-text-decoration: none;
  text-decoration: none;
  -webkit-transition: color 0.3s ease;
  transition: color 0.3s ease;
  white-space: nowrap;
  font-size: 16px;
  text-align: center;
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  color: orange;
}

.emotion-6:hover,
.emotion-6:focus {
  -webkit-text-decoration: underline;
  text-decoration: underline;
}

<div
  class="emotion-0"
  data-testid="button-dropdown-container"
>
  <button
    aria-expanded="false"
    class="emotion-1"
    color="primary"
  >
    <span
      class="emotion-2"
    >
      heading text
    </span>
    <span
      aria-hidden="true"
      class="emotion-3"
    >
      <svg
        fill="none"
        viewBox="0 0 12 12"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          clip-rule="evenodd"
          d="M7 0H5v5H0v2h5v5h2V7h5V5H7V0z"
          fill="#000000"
          fill-rule="evenodd"
        />
      </svg>
    </span>
  </button>
  <ul
    aria-hidden="true"
    class="emotion-4"
  >
    <li
      class="emotion-5"
    >
      <a
        breakpoint="desktop"
        class="emotion-6"
        href="girls"
        target="_blank"
      >
        Girls
      </a>
    </li>
    <li
      class="emotion-5"
    >
      <a
        breakpoint="desktop"
        class="emotion-6"
        href="boys"
        target="_self"
      >
        Boys
      </a>
    </li>
  </ul>
</div>
`;

exports[`<ButtonDropdown /> snapshots for Athleta brand open dropdown 1`] = `
.emotion-0 {
  font-size: 0.9735rem;
  width: 100%;
}

.emotion-0:not(:first-child) {
  margin-left: none;
}

.emotion-1 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: none;
  transition: none;
  box-sizing: border-box;
  border: 1.5px solid transparent;
  border-radius: 0;
  font-size: 16px;
  font-weight: 500;
  letter-spacing: 0px;
  min-height: auto;
  max-height: 60px;
  line-height: 1.25;
  padding: 22px 40px;
  width: 100%;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #000000;
  background: transparent;
  border-color: #000000;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  width: 100%;
  text-align: center;
  box-sizing: border-box;
  color: red;
  background-color: #000000;
  height: 60px;
  font-size: 16px;
}

.emotion-1:focus {
  outline: none;
}

.emotion-1>span {
  padding: 1px 0;
}

.emotion-1 svg path {
  fill: #FFFFFF!important;
}

.emotion-1:hover {
  -webkit-text-decoration: underline;
  text-decoration: underline;
}

.emotion-1>span {
  padding: 0;
}

.emotion-3 {
  display: inline-block;
  height: 1.53px;
  width: 10px;
  min-height: 1.53px;
  min-width: 10px;
  margin-left: 0.65rem;
  padding-top: 2.4px;
}

.emotion-3 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-3 svg path {
  fill: inherit;
}

.emotion-3 svg rect {
  fill: inherit;
}

.emotion-4 {
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
  background: #FFFFFF;
  box-sizing: border-box;
  list-style-type: none;
  min-width: 100%;
  overflow: hidden;
  position: relative;
  z-index: 390;
  border-width: 0;
  border-style: solid;
  text-align: left;
  letter-spacing: 0;
  pointer-events: auto;
  padding: 0;
  max-height: 800px;
  -webkit-transition: max-height .3s ease-in;
  transition: max-height .3s ease-in;
  visibility: visible;
  color: pink;
}

.emotion-5 {
  box-sizing: border-box;
  width: 100%;
  border-bottom: 1px solid #000000;
  border-left: 1px solid #333333;
  border-right: 1px solid #333333;
  height: 60px;
}

.emotion-5:last-child {
  border-bottom: 1px solid #333333;
}

.emotion-6 {
  cursor: pointer;
  display: block;
  padding: 1.375rem 0;
  -webkit-text-decoration: none;
  text-decoration: none;
  -webkit-transition: color 0.3s ease;
  transition: color 0.3s ease;
  white-space: nowrap;
  font-size: 16px;
  text-align: center;
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  color: orange;
}

.emotion-6:hover,
.emotion-6:focus {
  -webkit-text-decoration: underline;
  text-decoration: underline;
}

<div
  class="emotion-0"
  data-testid="button-dropdown-container"
>
  <button
    aria-expanded="true"
    class="emotion-1"
    color="primary"
  >
    <span
      class="emotion-2"
    >
      heading text
    </span>
    <span
      aria-hidden="true"
      class="emotion-3"
    >
      <svg
        fill="none"
        viewBox="0 0 12 2"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M0 0h12v2H0V0z"
          fill="#000000"
        />
      </svg>
    </span>
  </button>
  <ul
    aria-hidden="false"
    class="emotion-4"
  >
    <li
      class="emotion-5"
    >
      <a
        breakpoint="desktop"
        class="emotion-6"
        href="girls"
        target="_blank"
      >
        Girls
      </a>
    </li>
    <li
      class="emotion-5"
    >
      <a
        breakpoint="desktop"
        class="emotion-6"
        href="boys"
        target="_self"
      >
        Boys
      </a>
    </li>
  </ul>
</div>
`;

exports[`<ButtonDropdown /> snapshots for Gap and Gap Factory Store brands closed dropdown 1`] = `
.emotion-0 {
  font-size: 0.9735rem;
  width: 100%;
}

.emotion-0:not(:first-child) {
  margin-left: none;
}

.emotion-1 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  display: inline-block;
  text-transform: none;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 1px solid transparent;
  border-radius: 0;
  max-width: 300px;
  font-size: 12px;
  font-weight: 500;
  letter-spacing: 2%;
  min-height: unset;
  max-height: auto;
  line-height: 12px;
  padding: 12px;
  width: 100%;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #000000;
  background: transparent;
  border-color: #000000;
  display: block;
  width: -webkit-fit-content!important;
  width: -moz-fit-content!important;
  width: fit-content!important;
  text-align: center;
  box-sizing: border-box;
  color: red;
  line-height: 1;
  margin: 0;
  text-transform: uppercase;
  background-color: #FFFFFF;
  border-color: #CCCCCC;
  border-width: 1px 0;
  font-weight: 700;
  letter-spacing: 1px;
  padding: 7px 1.5rem;
  min-height: 0;
  font-size: 12px;
}

.emotion-1:focus {
  outline: none;
}

.emotion-1>span {
  padding: 1px 0;
}

.emotion-1>span {
  padding: 0;
}

.emotion-3 {
  display: inline-block;
  width: 10px;
  margin-left: 0.65rem;
  vertical-align: initial;
  font-size: 1.3rem;
}

.emotion-4 {
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
  background: #FFFFFF;
  box-sizing: border-box;
  list-style-type: none;
  min-width: 100%;
  overflow: hidden;
  position: relative;
  z-index: 390;
  border-width: 0;
  border-style: solid;
  text-align: left;
  letter-spacing: 0;
  pointer-events: auto;
  padding: 0px;
  max-height: 0;
  -webkit-transition: max-height .5s ease-out,visibility 0s .5s;
  transition: max-height .5s ease-out,visibility 0s .5s;
  visibility: hidden;
  color: pink;
}

.emotion-5 {
  box-sizing: border-box;
  width: 100%;
  border-bottom: none;
  letter-spacing: 1px;
  margin-left: 0;
  text-transform: none;
  padding: 12px;
}

.emotion-5:last-child {
  border: none;
}

.emotion-6 {
  cursor: pointer;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  padding: 0px;
  -webkit-text-decoration: none;
  text-decoration: none;
  -webkit-transition: color 0.3s ease;
  transition: color 0.3s ease;
  white-space: nowrap;
  font-size: 18px;
  text-align: center;
  color: inherit;
  -webkit-box-pack: start;
  -ms-flex-pack: start;
  -webkit-justify-content: start;
  justify-content: start;
  background-color: inherit;
  font-weight: 400;
  letter-spacing: 1px;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  color: orange;
}

<div
  class="emotion-0"
  data-testid="button-dropdown-container"
>
  <button
    aria-expanded="false"
    class="emotion-1"
    color="primary"
  >
    <span
      class="emotion-2"
    >
      heading text
    </span>
    <span
      aria-hidden="true"
      class="emotion-3"
    >
      +
    </span>
  </button>
  <ul
    aria-hidden="true"
    class="emotion-4"
  >
    <li
      class="emotion-5"
    >
      <a
        breakpoint="desktop"
        class="emotion-6"
        href="girls"
        target="_blank"
      >
        Girls
      </a>
    </li>
    <li
      class="emotion-5"
    >
      <a
        breakpoint="desktop"
        class="emotion-6"
        href="boys"
        target="_self"
      >
        Boys
      </a>
    </li>
  </ul>
</div>
`;

exports[`<ButtonDropdown /> snapshots for Gap and Gap Factory Store brands mobile closed dropdown 1`] = `
.emotion-0 {
  font-size: 0.9735rem;
  width: 100%;
}

.emotion-0:not(:first-child) {
  margin-left: none;
}

.emotion-1 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  display: inline-block;
  text-transform: none;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 1px solid transparent;
  border-radius: 0;
  max-width: 300px;
  font-size: 12px;
  font-weight: 500;
  letter-spacing: 2%;
  min-height: unset;
  max-height: auto;
  line-height: 12px;
  padding: 12px;
  width: 100%;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #000000;
  background: transparent;
  border-color: #000000;
  display: block;
  width: -webkit-fit-content!important;
  width: -moz-fit-content!important;
  width: fit-content!important;
  text-align: center;
  box-sizing: border-box;
  color: red;
  line-height: 1;
  margin: 0;
  text-transform: uppercase;
  background-color: #FFFFFF;
  border-color: #CCCCCC;
  border-width: 1px 0;
  font-weight: 700;
  letter-spacing: 1px;
  padding: 7px 1.5rem;
  min-height: 0;
  font-size: 12px;
}

.emotion-1:focus {
  outline: none;
}

.emotion-1>span {
  padding: 1px 0;
}

.emotion-1>span {
  padding: 0;
}

.emotion-3 {
  display: inline-block;
  width: 10px;
  margin-left: 0.65rem;
  vertical-align: initial;
  font-size: 1.3rem;
}

.emotion-4 {
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
  background: #FFFFFF;
  box-sizing: border-box;
  list-style-type: none;
  min-width: 100%;
  overflow: hidden;
  position: relative;
  z-index: 390;
  border-width: 0;
  border-style: solid;
  text-align: left;
  letter-spacing: 0;
  pointer-events: auto;
  padding: 0px;
  max-height: 0;
  -webkit-transition: max-height .5s ease-out,visibility 0s .5s;
  transition: max-height .5s ease-out,visibility 0s .5s;
  visibility: hidden;
  color: pink;
}

.emotion-5 {
  box-sizing: border-box;
  width: 100%;
  border-bottom: none;
  letter-spacing: 1px;
  margin-left: 0;
  text-transform: none;
  padding: 12px;
}

.emotion-5:last-child {
  border: none;
}

.emotion-6 {
  cursor: pointer;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  padding: 0px;
  -webkit-text-decoration: none;
  text-decoration: none;
  -webkit-transition: color 0.3s ease;
  transition: color 0.3s ease;
  white-space: nowrap;
  font-size: 18px;
  text-align: center;
  color: inherit;
  -webkit-box-pack: start;
  -ms-flex-pack: start;
  -webkit-justify-content: start;
  justify-content: start;
  background-color: inherit;
  font-weight: 400;
  letter-spacing: 1px;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  color: orange;
}

<div
  class="emotion-0"
  data-testid="button-dropdown-container"
>
  <button
    aria-expanded="false"
    class="emotion-1"
    color="primary"
  >
    <span
      class="emotion-2"
    >
      heading text
    </span>
    <span
      aria-hidden="true"
      class="emotion-3"
    >
      +
    </span>
  </button>
  <ul
    aria-hidden="true"
    class="emotion-4"
  >
    <li
      class="emotion-5"
    >
      <a
        breakpoint="desktop"
        class="emotion-6"
        href="girls"
        target="_blank"
      >
        Girls
      </a>
    </li>
    <li
      class="emotion-5"
    >
      <a
        breakpoint="desktop"
        class="emotion-6"
        href="boys"
        target="_self"
      >
        Boys
      </a>
    </li>
  </ul>
</div>
`;

exports[`<ButtonDropdown /> snapshots for Gap and Gap Factory Store brands mobile open dropdown 1`] = `
.emotion-0 {
  font-size: 0.9735rem;
  width: 100%;
}

.emotion-0:not(:first-child) {
  margin-left: none;
}

.emotion-1 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  display: inline-block;
  text-transform: none;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 1px solid transparent;
  border-radius: 0;
  max-width: 300px;
  font-size: 12px;
  font-weight: 500;
  letter-spacing: 2%;
  min-height: unset;
  max-height: auto;
  line-height: 12px;
  padding: 12px;
  width: 100%;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #000000;
  background: transparent;
  border-color: #000000;
  display: block;
  width: -webkit-fit-content!important;
  width: -moz-fit-content!important;
  width: fit-content!important;
  text-align: center;
  box-sizing: border-box;
  color: red;
  line-height: 1;
  margin: 0;
  text-transform: uppercase;
  background-color: #FFFFFF;
  border-color: #CCCCCC;
  border-width: 1px 0;
  font-weight: 700;
  letter-spacing: 1px;
  padding: 7px 1.5rem;
  min-height: 0;
  font-size: 12px;
}

.emotion-1:focus {
  outline: none;
}

.emotion-1>span {
  padding: 1px 0;
}

.emotion-1>span {
  padding: 0;
}

.emotion-3 {
  display: inline-block;
  width: 10px;
  margin-left: 0.65rem;
  vertical-align: initial;
  font-size: 1.3rem;
}

.emotion-4 {
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
  background: #FFFFFF;
  box-sizing: border-box;
  list-style-type: none;
  min-width: 100%;
  overflow: hidden;
  position: relative;
  z-index: 390;
  border-width: 0;
  border-style: solid;
  text-align: left;
  letter-spacing: 0;
  pointer-events: auto;
  padding: 0px;
  max-height: 800px;
  -webkit-transition: max-height .3s ease-in;
  transition: max-height .3s ease-in;
  visibility: visible;
  color: pink;
}

.emotion-5 {
  box-sizing: border-box;
  width: 100%;
  border-bottom: none;
  letter-spacing: 1px;
  margin-left: 0;
  text-transform: none;
  padding: 12px;
}

.emotion-5:last-child {
  border: none;
}

.emotion-6 {
  cursor: pointer;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  padding: 0px;
  -webkit-text-decoration: none;
  text-decoration: none;
  -webkit-transition: color 0.3s ease;
  transition: color 0.3s ease;
  white-space: nowrap;
  font-size: 18px;
  text-align: center;
  color: inherit;
  -webkit-box-pack: start;
  -ms-flex-pack: start;
  -webkit-justify-content: start;
  justify-content: start;
  background-color: inherit;
  font-weight: 400;
  letter-spacing: 1px;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  color: orange;
}

<div
  class="emotion-0"
  data-testid="button-dropdown-container"
>
  <button
    aria-expanded="true"
    class="emotion-1"
    color="primary"
  >
    <span
      class="emotion-2"
    >
      heading text
    </span>
    <span
      aria-hidden="true"
      class="emotion-3"
    >
      -
    </span>
  </button>
  <ul
    aria-hidden="false"
    class="emotion-4"
  >
    <li
      class="emotion-5"
    >
      <a
        breakpoint="desktop"
        class="emotion-6"
        href="girls"
        target="_blank"
      >
        Girls
      </a>
    </li>
    <li
      class="emotion-5"
    >
      <a
        breakpoint="desktop"
        class="emotion-6"
        href="boys"
        target="_self"
      >
        Boys
      </a>
    </li>
  </ul>
</div>
`;

exports[`<ButtonDropdown /> snapshots for Gap and Gap Factory Store brands open dropdown 1`] = `
.emotion-0 {
  font-size: 0.9735rem;
  width: 100%;
}

.emotion-0:not(:first-child) {
  margin-left: none;
}

.emotion-1 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  display: inline-block;
  text-transform: none;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 1px solid transparent;
  border-radius: 0;
  max-width: 300px;
  font-size: 12px;
  font-weight: 500;
  letter-spacing: 2%;
  min-height: unset;
  max-height: auto;
  line-height: 12px;
  padding: 12px;
  width: 100%;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #000000;
  background: transparent;
  border-color: #000000;
  display: block;
  width: -webkit-fit-content!important;
  width: -moz-fit-content!important;
  width: fit-content!important;
  text-align: center;
  box-sizing: border-box;
  color: red;
  line-height: 1;
  margin: 0;
  text-transform: uppercase;
  background-color: #FFFFFF;
  border-color: #CCCCCC;
  border-width: 1px 0;
  font-weight: 700;
  letter-spacing: 1px;
  padding: 7px 1.5rem;
  min-height: 0;
  font-size: 12px;
}

.emotion-1:focus {
  outline: none;
}

.emotion-1>span {
  padding: 1px 0;
}

.emotion-1>span {
  padding: 0;
}

.emotion-3 {
  display: inline-block;
  width: 10px;
  margin-left: 0.65rem;
  vertical-align: initial;
  font-size: 1.3rem;
}

.emotion-4 {
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
  background: #FFFFFF;
  box-sizing: border-box;
  list-style-type: none;
  min-width: 100%;
  overflow: hidden;
  position: relative;
  z-index: 390;
  border-width: 0;
  border-style: solid;
  text-align: left;
  letter-spacing: 0;
  pointer-events: auto;
  padding: 0px;
  max-height: 800px;
  -webkit-transition: max-height .3s ease-in;
  transition: max-height .3s ease-in;
  visibility: visible;
  color: pink;
}

.emotion-5 {
  box-sizing: border-box;
  width: 100%;
  border-bottom: none;
  letter-spacing: 1px;
  margin-left: 0;
  text-transform: none;
  padding: 12px;
}

.emotion-5:last-child {
  border: none;
}

.emotion-6 {
  cursor: pointer;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  padding: 0px;
  -webkit-text-decoration: none;
  text-decoration: none;
  -webkit-transition: color 0.3s ease;
  transition: color 0.3s ease;
  white-space: nowrap;
  font-size: 18px;
  text-align: center;
  color: inherit;
  -webkit-box-pack: start;
  -ms-flex-pack: start;
  -webkit-justify-content: start;
  justify-content: start;
  background-color: inherit;
  font-weight: 400;
  letter-spacing: 1px;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  color: orange;
}

<div
  class="emotion-0"
  data-testid="button-dropdown-container"
>
  <button
    aria-expanded="true"
    class="emotion-1"
    color="primary"
  >
    <span
      class="emotion-2"
    >
      heading text
    </span>
    <span
      aria-hidden="true"
      class="emotion-3"
    >
      -
    </span>
  </button>
  <ul
    aria-hidden="false"
    class="emotion-4"
  >
    <li
      class="emotion-5"
    >
      <a
        breakpoint="desktop"
        class="emotion-6"
        href="girls"
        target="_blank"
      >
        Girls
      </a>
    </li>
    <li
      class="emotion-5"
    >
      <a
        breakpoint="desktop"
        class="emotion-6"
        href="boys"
        target="_self"
      >
        Boys
      </a>
    </li>
  </ul>
</div>
`;

exports[`<ButtonDropdown /> snapshots for Old Navy brand closed dropdown 1`] = `
.emotion-0 {
  font-size: 0.9735rem;
  width: 100%;
}

.emotion-0:not(:first-child) {
  margin-left: none;
}

.emotion-1 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 700;
  letter-spacing: 1.6800000000000002px;
  min-height: 48px;
  max-height: auto;
  line-height: 1.1428571428571428;
  padding: 14px 22px;
  width: 100%;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #003764;
  background: transparent;
  border-color: #003764;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  width: 100%;
  text-align: center;
  box-sizing: border-box;
  color: red;
  line-height: 1;
  letter-spacing: 0;
  font-size: 1rem;
  padding: 0 0.8em;
  margin: 0;
  text-transform: uppercase;
  background-color: #FFFFFF;
  border-width: 2px;
  border-color: #003764;
}

.emotion-1:focus {
  outline: none;
}

.emotion-1>span {
  padding: 1px 0;
}

.emotion-3 {
  display: inline-block;
  height: 10px;
  width: 10px;
  min-height: 10px;
  min-width: 10px;
  margin-left: 0.65rem;
  padding-top: 0;
}

.emotion-3 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-3 svg path {
  fill: #003764;
}

.emotion-3 svg rect {
  fill: #003764;
}

.emotion-4 {
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
  background: #FFFFFF;
  box-sizing: border-box;
  list-style-type: none;
  min-width: 100%;
  overflow: hidden;
  position: relative;
  z-index: 390;
  border-width: 0;
  border-style: solid;
  text-align: left;
  letter-spacing: 0;
  pointer-events: auto;
  padding: 0 1rem;
  max-height: 0;
  -webkit-transition: max-height .5s ease-out,visibility 0s .5s;
  transition: max-height .5s ease-out,visibility 0s .5s;
  visibility: hidden;
  color: pink;
}

.emotion-5 {
  box-sizing: border-box;
  width: 100%;
  border-bottom: 1px solid #000000;
}

.emotion-5:last-child {
  border: none;
}

.emotion-6 {
  cursor: pointer;
  display: block;
  padding: 0.5rem 0;
  -webkit-text-decoration: none;
  text-decoration: none;
  -webkit-transition: color 0.3s ease;
  transition: color 0.3s ease;
  white-space: nowrap;
  font-size: inherit;
  text-align: center;
  color: #000000;
  font-family: var(--font-family-font1),sans-serif;
  font-weight: 700;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  color: orange;
}

<div
  class="emotion-0"
  data-testid="button-dropdown-container"
>
  <button
    aria-expanded="false"
    class="emotion-1"
    color="primary"
  >
    <span
      class="emotion-2"
    >
      heading text
    </span>
    <span
      aria-hidden="true"
      class="emotion-3"
    >
      <svg
        fill="none"
        viewBox="0 0 12 12"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          clip-rule="evenodd"
          d="M7 0H5v5H0v2h5v5h2V7h5V5H7V0z"
          fill="#000000"
          fill-rule="evenodd"
        />
      </svg>
    </span>
  </button>
  <ul
    aria-hidden="true"
    class="emotion-4"
  >
    <li
      class="emotion-5"
    >
      <a
        breakpoint="desktop"
        class="emotion-6"
        href="girls"
        target="_blank"
      >
        Girls
      </a>
    </li>
    <li
      class="emotion-5"
    >
      <a
        breakpoint="desktop"
        class="emotion-6"
        href="boys"
        target="_self"
      >
        Boys
      </a>
    </li>
  </ul>
</div>
`;

exports[`<ButtonDropdown /> snapshots for Old Navy brand mobile closed dropdown 1`] = `
.emotion-0 {
  font-size: 0.9735rem;
  width: 100%;
}

.emotion-0:not(:first-child) {
  margin-left: none;
}

.emotion-1 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 700;
  letter-spacing: 1.6800000000000002px;
  min-height: 48px;
  max-height: auto;
  line-height: 1.1428571428571428;
  padding: 14px 22px;
  width: 100%;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #003764;
  background: transparent;
  border-color: #003764;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  width: 100%;
  text-align: center;
  box-sizing: border-box;
  color: red;
  line-height: 1;
  letter-spacing: 0;
  font-size: 1rem;
  padding: 0 0.8em;
  margin: 0;
  text-transform: uppercase;
  background-color: #FFFFFF;
  border-width: 2px;
  border-color: #003764;
}

.emotion-1:focus {
  outline: none;
}

.emotion-1>span {
  padding: 1px 0;
}

.emotion-3 {
  display: inline-block;
  height: 10px;
  width: 10px;
  min-height: 10px;
  min-width: 10px;
  margin-left: 0.65rem;
  padding-top: 0;
}

.emotion-3 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-3 svg path {
  fill: #003764;
}

.emotion-3 svg rect {
  fill: #003764;
}

.emotion-4 {
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
  background: #FFFFFF;
  box-sizing: border-box;
  list-style-type: none;
  min-width: 100%;
  overflow: hidden;
  position: relative;
  z-index: 390;
  border-width: 0;
  border-style: solid;
  text-align: left;
  letter-spacing: 0;
  pointer-events: auto;
  padding: 0 1rem;
  max-height: 0;
  -webkit-transition: max-height .5s ease-out,visibility 0s .5s;
  transition: max-height .5s ease-out,visibility 0s .5s;
  visibility: hidden;
  color: pink;
}

.emotion-5 {
  box-sizing: border-box;
  width: 100%;
  border-bottom: 1px solid #000000;
}

.emotion-5:last-child {
  border: none;
}

.emotion-6 {
  cursor: pointer;
  display: block;
  padding: 0.5rem 0;
  -webkit-text-decoration: none;
  text-decoration: none;
  -webkit-transition: color 0.3s ease;
  transition: color 0.3s ease;
  white-space: nowrap;
  font-size: inherit;
  text-align: center;
  color: #000000;
  font-family: var(--font-family-font1),sans-serif;
  font-weight: 700;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  color: orange;
}

<div
  class="emotion-0"
  data-testid="button-dropdown-container"
>
  <button
    aria-expanded="false"
    class="emotion-1"
    color="primary"
  >
    <span
      class="emotion-2"
    >
      heading text
    </span>
    <span
      aria-hidden="true"
      class="emotion-3"
    >
      <svg
        fill="none"
        viewBox="0 0 12 12"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          clip-rule="evenodd"
          d="M7 0H5v5H0v2h5v5h2V7h5V5H7V0z"
          fill="#000000"
          fill-rule="evenodd"
        />
      </svg>
    </span>
  </button>
  <ul
    aria-hidden="true"
    class="emotion-4"
  >
    <li
      class="emotion-5"
    >
      <a
        breakpoint="desktop"
        class="emotion-6"
        href="girls"
        target="_blank"
      >
        Girls
      </a>
    </li>
    <li
      class="emotion-5"
    >
      <a
        breakpoint="desktop"
        class="emotion-6"
        href="boys"
        target="_self"
      >
        Boys
      </a>
    </li>
  </ul>
</div>
`;

exports[`<ButtonDropdown /> snapshots for Old Navy brand open dropdown 1`] = `
.emotion-0 {
  font-size: 0.9735rem;
  width: 100%;
}

.emotion-0:not(:first-child) {
  margin-left: none;
}

.emotion-1 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 700;
  letter-spacing: 1.6800000000000002px;
  min-height: 48px;
  max-height: auto;
  line-height: 1.1428571428571428;
  padding: 14px 22px;
  width: 100%;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #003764;
  background: transparent;
  border-color: #003764;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  width: 100%;
  text-align: center;
  box-sizing: border-box;
  color: red;
  line-height: 1;
  letter-spacing: 0;
  font-size: 1rem;
  padding: 0 0.8em;
  margin: 0;
  text-transform: uppercase;
  background-color: #FFFFFF;
  border-width: 2px;
  border-color: #003764;
}

.emotion-1:focus {
  outline: none;
}

.emotion-1>span {
  padding: 1px 0;
}

.emotion-3 {
  display: inline-block;
  height: 2.25px;
  width: 10px;
  min-height: 2.25px;
  min-width: 10px;
  margin-left: 0.65rem;
  padding-top: 0;
}

.emotion-3 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-3 svg path {
  fill: #003764;
}

.emotion-3 svg rect {
  fill: #003764;
}

.emotion-4 {
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
  background: #FFFFFF;
  box-sizing: border-box;
  list-style-type: none;
  min-width: 100%;
  overflow: hidden;
  position: relative;
  z-index: 390;
  border-width: 0;
  border-style: solid;
  text-align: left;
  letter-spacing: 0;
  pointer-events: auto;
  padding: 0 1rem;
  max-height: 800px;
  -webkit-transition: max-height .3s ease-in;
  transition: max-height .3s ease-in;
  visibility: visible;
  color: pink;
}

.emotion-5 {
  box-sizing: border-box;
  width: 100%;
  border-bottom: 1px solid #000000;
}

.emotion-5:last-child {
  border: none;
}

.emotion-6 {
  cursor: pointer;
  display: block;
  padding: 0.5rem 0;
  -webkit-text-decoration: none;
  text-decoration: none;
  -webkit-transition: color 0.3s ease;
  transition: color 0.3s ease;
  white-space: nowrap;
  font-size: inherit;
  text-align: center;
  color: #000000;
  font-family: var(--font-family-font1),sans-serif;
  font-weight: 700;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  color: orange;
}

<div
  class="emotion-0"
  data-testid="button-dropdown-container"
>
  <button
    aria-expanded="true"
    class="emotion-1"
    color="primary"
  >
    <span
      class="emotion-2"
    >
      heading text
    </span>
    <span
      aria-hidden="true"
      class="emotion-3"
    >
      <svg
        fill="none"
        viewBox="0 0 12 2"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M0 0h12v2H0V0z"
          fill="#000000"
        />
      </svg>
    </span>
  </button>
  <ul
    aria-hidden="false"
    class="emotion-4"
  >
    <li
      class="emotion-5"
    >
      <a
        breakpoint="desktop"
        class="emotion-6"
        href="girls"
        target="_blank"
      >
        Girls
      </a>
    </li>
    <li
      class="emotion-5"
    >
      <a
        breakpoint="desktop"
        class="emotion-6"
        href="boys"
        target="_self"
      >
        Boys
      </a>
    </li>
  </ul>
</div>
`;

exports[`<ButtonDropdown /> snapshots mobile open dropdown 1`] = `
.emotion-0 {
  font-size: 0.9735rem;
  width: 100%;
}

.emotion-1 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  display: inline-block;
  text-transform: none;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 1px solid transparent;
  border-radius: 0;
  font-size: 12px;
  font-weight: 500;
  letter-spacing: 2%;
  min-height: unset;
  max-height: auto;
  line-height: 1.125;
  padding: 12px;
  width: 100%;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #000000;
  background: transparent;
  border-color: #000000;
  display: block;
  width: -webkit-fit-content!important;
  width: -moz-fit-content!important;
  width: fit-content!important;
  text-align: center;
  box-sizing: border-box;
  color: blue;
  line-height: 1;
  margin: 0;
  text-transform: uppercase;
  background-color: #FFFFFF;
  border-color: #CCCCCC;
  border-width: 1px 0;
  font-weight: 700;
  letter-spacing: 1px;
  padding: 7px 1.5rem;
  min-height: 0;
  font-size: inherit;
}

.emotion-1:focus {
  outline: none;
}

.emotion-1>span {
  padding: 1px 0;
}

.emotion-1>span {
  padding: 0;
}

.emotion-3 {
  display: inline-block;
  width: 10px;
  margin-left: 0.65rem;
  vertical-align: initial;
  font-size: 1.3rem;
}

.emotion-4 {
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
  background: #FFFFFF;
  box-sizing: border-box;
  list-style-type: none;
  min-width: 100%;
  overflow: hidden;
  position: relative;
  z-index: 390;
  border-width: 0;
  border-style: solid;
  text-align: left;
  letter-spacing: 0;
  pointer-events: auto;
  padding: 0px;
  max-height: 800px;
  -webkit-transition: max-height .3s ease-in;
  transition: max-height .3s ease-in;
  visibility: visible;
  color: purple;
}

.emotion-5 {
  box-sizing: border-box;
  width: auto;
  border-bottom: none;
  letter-spacing: 1px;
  margin-left: 0;
  text-transform: none;
  padding: 12px;
}

.emotion-5:last-child {
  border: none;
}

.emotion-6 {
  cursor: pointer;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  padding: 0px;
  -webkit-text-decoration: none;
  text-decoration: none;
  -webkit-transition: color 0.3s ease;
  transition: color 0.3s ease;
  white-space: nowrap;
  font-size: 18px;
  text-align: center;
  color: inherit;
  -webkit-box-pack: start;
  -ms-flex-pack: start;
  -webkit-justify-content: start;
  justify-content: start;
  background-color: inherit;
  font-weight: 400;
  letter-spacing: 1px;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  color: green;
}

<div
  class="emotion-0"
  data-testid="button-dropdown-container"
>
  <button
    aria-expanded="true"
    class="emotion-1"
    color="primary"
  >
    <span
      class="emotion-2"
    >
      heading text
    </span>
    <span
      aria-hidden="true"
      class="emotion-3"
    >
      -
    </span>
  </button>
  <ul
    aria-hidden="false"
    class="emotion-4"
  >
    <li
      class="emotion-5"
    >
      <a
        breakpoint="mobile"
        class="emotion-6"
        href="girls"
        target="_blank"
      >
        Girls
      </a>
    </li>
    <li
      class="emotion-5"
    >
      <a
        breakpoint="mobile"
        class="emotion-6"
        href="boys"
        target="_self"
      >
        Boys
      </a>
    </li>
  </ul>
</div>
`;

exports[`<ButtonDropdown /> snapshots with flag on-cta-redesign-2024 as true and OnCtaRedesign2024Context is enabled closed dropdown 1`] = `
.emotion-0 {
  font-size: 0.9735rem;
  width: 100%;
  min-width: 136px;
  max-width: 255px;
}

.emotion-0:not(:first-child) {
  margin-left: none;
}

.emotion-1 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 700;
  letter-spacing: 1.6800000000000002px;
  min-height: 44px;
  max-height: 44px;
  line-height: 14px;
  padding: 6px;
  width: 100%;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  background-color: #FFFFFF;
  color: #003764;
  border-color: #003764;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  width: 100%;
  text-align: center;
  box-sizing: border-box;
  color: red;
  line-height: 14px;
  letter-spacing: 1.68px;
  font-size: 14px;
  padding: 6px;
  font-weight: 700;
  max-height: 44px;
  margin: 0;
  text-transform: uppercase;
  background-color: #FFFFFF;
  border-width: 2px;
  border-color: #003764;
}

.emotion-1:focus {
  outline: none;
}

.emotion-1>span {
  padding: 1px 0;
}

.emotion-1:hover {
  background-color: #003764;
  color: #FFFFFF;
}

.emotion-1:hover svg path {
  fill: #FFFFFF;
}

.emotion-1.emotion-1[aria-expanded="true"] {
  background-color: #000000;
  border-color: #000000;
  color: #FFFFFF;
  -webkit-text-decoration: underline;
  text-decoration: underline;
}

.emotion-1.emotion-1[aria-expanded="true"] svg path {
  fill: #FFFFFF;
}

.emotion-2 {
  margin: 0 auto;
}

.emotion-3 {
  display: inline-block;
  height: 10px;
  width: 10px;
  min-height: 10px;
  min-width: 10px;
  margin-left: 8px;
  padding-top: 0;
}

.emotion-3 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-3 svg path {
  fill: #003764;
}

.emotion-3 svg rect {
  fill: #003764;
}

.emotion-4 {
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
  background: #FFFFFF;
  box-sizing: border-box;
  list-style-type: none;
  min-width: 100%;
  overflow: hidden;
  position: relative;
  z-index: 390;
  border-width: 0;
  border-style: solid;
  text-align: left;
  letter-spacing: 0;
  pointer-events: auto;
  border-top: 1px solid #003764;
  border-bottom: 1px solid #003764;
  max-height: 0;
  -webkit-transition: max-height .5s ease-out,visibility 0s .5s;
  transition: max-height .5s ease-out,visibility 0s .5s;
  visibility: hidden;
  color: pink;
}

.emotion-5 {
  box-sizing: border-box;
  width: 100%;
  border-bottom: 1px solid #003764;
  height: 44px;
  border-top: 1px solid #003764;
  border-left: 2px solid #003764;
  border-right: 2px solid #003764;
}

.emotion-5:last-child {
  border-top: 1px solid #003764;
  border-bottom: 1px solid #003764;
  border-left: 2px solid #003764;
  border-right: 2px solid #003764;
}

.emotion-6 {
  cursor: pointer;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  padding: 6px;
  -webkit-text-decoration: none;
  text-decoration: none;
  -webkit-transition: color 0.3s ease;
  transition: color 0.3s ease;
  white-space: normal;
  font-size: 12px;
  text-align: center;
  color: #003764;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-content: center;
  -ms-flex-line-pack: center;
  align-content: center;
  -webkit-box-flex-wrap: wrap;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  height: 100%;
  line-height: 1.1666666666666667;
  letter-spacing: 1.44px;
  font-family: var(--font-family-font1),sans-serif;
  font-weight: 700;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  color: orange;
}

.emotion-6:hover {
  background-color: #003764;
  color: #FFFFFF;
}

<div
  class="emotion-0"
  data-testid="button-dropdown-container"
>
  <button
    aria-expanded="false"
    class="emotion-1"
    color="primary"
  >
    <span
      class="emotion-2"
    >
      heading text
    </span>
    <span
      aria-hidden="true"
      class="emotion-3"
    >
      <svg
        fill="none"
        viewBox="0 0 12 12"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          clip-rule="evenodd"
          d="M7 0H5v5H0v2h5v5h2V7h5V5H7V0z"
          fill="#000000"
          fill-rule="evenodd"
        />
      </svg>
    </span>
  </button>
  <ul
    aria-hidden="true"
    class="emotion-4"
  >
    <li
      class="emotion-5"
    >
      <a
        breakpoint="desktop"
        class="emotion-6"
        href="girls"
        target="_blank"
      >
        Girls
      </a>
    </li>
    <li
      class="emotion-5"
    >
      <a
        breakpoint="desktop"
        class="emotion-6"
        href="boys"
        target="_self"
      >
        Boys
      </a>
    </li>
  </ul>
</div>
`;

exports[`<ButtonDropdown /> snapshots with flag on-cta-redesign-2024 as true and OnCtaRedesign2024Context is enabled mobile closed dropdown 1`] = `
.emotion-0 {
  font-size: 0.9735rem;
  width: 100%;
  min-width: 136px;
  max-width: 255px;
}

.emotion-0:not(:first-child) {
  margin-left: none;
}

.emotion-1 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 700;
  letter-spacing: 1.6800000000000002px;
  min-height: 44px;
  max-height: 44px;
  line-height: 14px;
  padding: 6px;
  width: 100%;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  background-color: #FFFFFF;
  color: #003764;
  border-color: #003764;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  width: 100%;
  text-align: center;
  box-sizing: border-box;
  color: red;
  line-height: 14px;
  letter-spacing: 1.68px;
  font-size: 14px;
  padding: 6px;
  font-weight: 700;
  max-height: 44px;
  margin: 0;
  text-transform: uppercase;
  background-color: #FFFFFF;
  border-width: 2px;
  border-color: #003764;
}

.emotion-1:focus {
  outline: none;
}

.emotion-1>span {
  padding: 1px 0;
}

.emotion-1:hover {
  background-color: #003764;
  color: #FFFFFF;
}

.emotion-1:hover svg path {
  fill: #FFFFFF;
}

.emotion-1.emotion-1[aria-expanded="true"] {
  background-color: #000000;
  border-color: #000000;
  color: #FFFFFF;
  -webkit-text-decoration: underline;
  text-decoration: underline;
}

.emotion-1.emotion-1[aria-expanded="true"] svg path {
  fill: #FFFFFF;
}

.emotion-2 {
  margin: 0 auto;
}

.emotion-3 {
  display: inline-block;
  height: 10px;
  width: 10px;
  min-height: 10px;
  min-width: 10px;
  margin-left: 8px;
  padding-top: 0;
}

.emotion-3 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-3 svg path {
  fill: #003764;
}

.emotion-3 svg rect {
  fill: #003764;
}

.emotion-4 {
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
  background: #FFFFFF;
  box-sizing: border-box;
  list-style-type: none;
  min-width: 100%;
  overflow: hidden;
  position: relative;
  z-index: 390;
  border-width: 0;
  border-style: solid;
  text-align: left;
  letter-spacing: 0;
  pointer-events: auto;
  border-top: 1px solid #003764;
  border-bottom: 1px solid #003764;
  max-height: 0;
  -webkit-transition: max-height .5s ease-out,visibility 0s .5s;
  transition: max-height .5s ease-out,visibility 0s .5s;
  visibility: hidden;
  color: pink;
}

.emotion-5 {
  box-sizing: border-box;
  width: 100%;
  border-bottom: 1px solid #003764;
  height: 44px;
  border-top: 1px solid #003764;
  border-left: 2px solid #003764;
  border-right: 2px solid #003764;
}

.emotion-5:last-child {
  border-top: 1px solid #003764;
  border-bottom: 1px solid #003764;
  border-left: 2px solid #003764;
  border-right: 2px solid #003764;
}

.emotion-6 {
  cursor: pointer;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  padding: 6px;
  -webkit-text-decoration: none;
  text-decoration: none;
  -webkit-transition: color 0.3s ease;
  transition: color 0.3s ease;
  white-space: normal;
  font-size: 12px;
  text-align: center;
  color: #003764;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-content: center;
  -ms-flex-line-pack: center;
  align-content: center;
  -webkit-box-flex-wrap: wrap;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  height: 100%;
  line-height: 1.1666666666666667;
  letter-spacing: 1.44px;
  font-family: var(--font-family-font1),sans-serif;
  font-weight: 700;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  color: orange;
}

.emotion-6:hover {
  background-color: #003764;
  color: #FFFFFF;
}

<div
  class="emotion-0"
  data-testid="button-dropdown-container"
>
  <button
    aria-expanded="false"
    class="emotion-1"
    color="primary"
  >
    <span
      class="emotion-2"
    >
      heading text
    </span>
    <span
      aria-hidden="true"
      class="emotion-3"
    >
      <svg
        fill="none"
        viewBox="0 0 12 12"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          clip-rule="evenodd"
          d="M7 0H5v5H0v2h5v5h2V7h5V5H7V0z"
          fill="#000000"
          fill-rule="evenodd"
        />
      </svg>
    </span>
  </button>
  <ul
    aria-hidden="true"
    class="emotion-4"
  >
    <li
      class="emotion-5"
    >
      <a
        breakpoint="desktop"
        class="emotion-6"
        href="girls"
        target="_blank"
      >
        Girls
      </a>
    </li>
    <li
      class="emotion-5"
    >
      <a
        breakpoint="desktop"
        class="emotion-6"
        href="boys"
        target="_self"
      >
        Boys
      </a>
    </li>
  </ul>
</div>
`;

exports[`<ButtonDropdown /> snapshots with flag on-cta-redesign-2024 as true and OnCtaRedesign2024Context is enabled open dropdown 1`] = `
.emotion-0 {
  font-size: 0.9735rem;
  width: 100%;
  min-width: 136px;
  max-width: 255px;
}

.emotion-0:not(:first-child) {
  margin-left: none;
}

.emotion-1 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 700;
  letter-spacing: 1.6800000000000002px;
  min-height: 44px;
  max-height: 44px;
  line-height: 14px;
  padding: 6px;
  width: 100%;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  background-color: #FFFFFF;
  color: #003764;
  border-color: #003764;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  width: 100%;
  text-align: center;
  box-sizing: border-box;
  color: red;
  line-height: 14px;
  letter-spacing: 1.68px;
  font-size: 14px;
  padding: 6px;
  font-weight: 700;
  max-height: 44px;
  margin: 0;
  text-transform: uppercase;
  background-color: #FFFFFF;
  border-width: 2px;
  border-color: #003764;
}

.emotion-1:focus {
  outline: none;
}

.emotion-1>span {
  padding: 1px 0;
}

.emotion-1:hover {
  background-color: #003764;
  color: #FFFFFF;
}

.emotion-1:hover svg path {
  fill: #FFFFFF;
}

.emotion-1.emotion-1[aria-expanded="true"] {
  background-color: #000000;
  border-color: #000000;
  color: #FFFFFF;
  -webkit-text-decoration: underline;
  text-decoration: underline;
}

.emotion-1.emotion-1[aria-expanded="true"] svg path {
  fill: #FFFFFF;
}

.emotion-2 {
  margin: 0 auto;
}

.emotion-3 {
  display: inline-block;
  height: 2.25px;
  width: 10px;
  min-height: 2.25px;
  min-width: 10px;
  margin-left: 8px;
  padding-top: 0;
}

.emotion-3 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-3 svg path {
  fill: #003764;
}

.emotion-3 svg rect {
  fill: #003764;
}

.emotion-4 {
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
  background: #FFFFFF;
  box-sizing: border-box;
  list-style-type: none;
  min-width: 100%;
  overflow: hidden;
  position: relative;
  z-index: 390;
  border-width: 0;
  border-style: solid;
  text-align: left;
  letter-spacing: 0;
  pointer-events: auto;
  border-top: 1px solid #003764;
  border-bottom: 1px solid #003764;
  max-height: 800px;
  -webkit-transition: max-height .3s ease-in;
  transition: max-height .3s ease-in;
  visibility: visible;
  color: pink;
}

.emotion-5 {
  box-sizing: border-box;
  width: 100%;
  border-bottom: 1px solid #003764;
  height: 44px;
  border-top: 1px solid #003764;
  border-left: 2px solid #003764;
  border-right: 2px solid #003764;
}

.emotion-5:last-child {
  border-top: 1px solid #003764;
  border-bottom: 1px solid #003764;
  border-left: 2px solid #003764;
  border-right: 2px solid #003764;
}

.emotion-6 {
  cursor: pointer;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  padding: 6px;
  -webkit-text-decoration: none;
  text-decoration: none;
  -webkit-transition: color 0.3s ease;
  transition: color 0.3s ease;
  white-space: normal;
  font-size: 12px;
  text-align: center;
  color: #003764;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-content: center;
  -ms-flex-line-pack: center;
  align-content: center;
  -webkit-box-flex-wrap: wrap;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  height: 100%;
  line-height: 1.1666666666666667;
  letter-spacing: 1.44px;
  font-family: var(--font-family-font1),sans-serif;
  font-weight: 700;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  color: orange;
}

.emotion-6:hover {
  background-color: #003764;
  color: #FFFFFF;
}

<div
  class="emotion-0"
  data-testid="button-dropdown-container"
>
  <button
    aria-expanded="true"
    class="emotion-1"
    color="primary"
  >
    <span
      class="emotion-2"
    >
      heading text
    </span>
    <span
      aria-hidden="true"
      class="emotion-3"
    >
      <svg
        fill="none"
        viewBox="0 0 12 2"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M0 0h12v2H0V0z"
          fill="#000000"
        />
      </svg>
    </span>
  </button>
  <ul
    aria-hidden="false"
    class="emotion-4"
  >
    <li
      class="emotion-5"
    >
      <a
        breakpoint="desktop"
        class="emotion-6"
        href="girls"
        target="_blank"
      >
        Girls
      </a>
    </li>
    <li
      class="emotion-5"
    >
      <a
        breakpoint="desktop"
        class="emotion-6"
        href="boys"
        target="_self"
      >
        Boys
      </a>
    </li>
  </ul>
</div>
`;
