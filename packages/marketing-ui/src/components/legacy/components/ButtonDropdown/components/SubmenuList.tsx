'use client';
import { useContext } from 'react';
// @ts-ignore
import { CSSObject, forBrands, styled, useEnabledFeatures } from '@ecom-next/core/react-stitch';
import { OnCtaRedesign2024Context } from '../../../contexts/OnCtaRedesign2024Context';
import { SubmenuListProps } from '../types';

export const SubmenuList = styled.ul<SubmenuListProps>(({ theme, breakpoint, variant, naturalWidth = false }) => {
  const enabledFeatures = useEnabledFeatures();
  const useOnCtaRedesign2024 = useContext(OnCtaRedesign2024Context).enable;
  const isDesktop = breakpoint === 'desktop';

  const gapStyles = {
    padding: '0px',
    ...(naturalWidth ? { width: 'fit-content !important' } : {}),
  };

  const onSubmenuListStyles = () => {
    const isOnCtaRedesign2024Enabled = !!enabledFeatures?.['on-cta-redesign-2024'] && useOnCtaRedesign2024;
    const isBorderStyleVariant = variant === 'border';

    if (!isOnCtaRedesign2024Enabled)
      return {
        padding: isDesktop ? '0 1rem' : 'inherit',
      };
    if (!isBorderStyleVariant) return {};
    return {
      // @ts-ignore
      borderTop: `1px solid ${theme.color.b1}`,
      // @ts-ignore
      borderBottom: `1px solid ${theme.color.b1}`,
    };
  };

  const brandStyles = forBrands(theme, {
    gap: gapStyles,
    gapfs: gapStyles,
    br: () => ({
      textAlign: 'center',
      padding: isDesktop ? '0' : 'inherit',
    }),
    brfs: () => ({
      textAlign: 'center',
      padding: isDesktop ? '0' : 'inherit',
    }),
    at: () => ({
      padding: '0',
    }),
    on: onSubmenuListStyles(),
  }) as CSSObject;

  return {
    boxShadow: '0 1px 2px 0 rgba(0, 0, 0, 0.3)',
    // @ts-ignore
    background: theme.color.wh,
    boxSizing: 'border-box',
    listStyleType: 'none',
    minWidth: '100%',
    overflow: 'hidden',
    position: 'relative',
    // @ts-ignore
    zIndex: theme.layers.buttonDropdown,
    borderWidth: '0',
    borderStyle: 'solid',
    textAlign: 'left',
    letterSpacing: '0',
    pointerEvents: 'auto',
    ...brandStyles,
  };
});
