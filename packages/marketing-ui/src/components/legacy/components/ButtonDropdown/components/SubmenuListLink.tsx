'use client';
import { useContext } from 'react';
// @ts-ignore
import { Link } from '@ecom-next/core/migration/link';
// @ts-ignore
import { CSSObject, forBrands, styled, useEnabledFeatures } from '@ecom-next/core/react-stitch';
import { Size } from '../../ComposableButton/types';
import { OnCtaRedesign2024Context } from '../../../contexts/OnCtaRedesign2024Context';
import { SubmenuListLinkProps } from '../types';

const styledLink = styled(Link);

export const SubmenuListLink = styledLink<SubmenuListLinkProps>(({ theme, breakpoint, size }) => {
  const enabledFeatures = useEnabledFeatures();
  const useOnCtaRedesign2024 = useContext(OnCtaRedesign2024Context).enable;
  const isDesktop = breakpoint === 'desktop';
  const gapFontSize = (): string => {
    const currentSize: Size = size || Size.xl;
    const fontSize = {
      xl: '18px',
      large: '16px',
      medium: '14px',
      small: '12px',
      xs: '10px',
    };
    return fontSize[currentSize];
  };

  const gapStyles = (): CSSObject => {
    const commonStyles = {
      display: 'flex',
      justifyContent: 'start',
      padding: '0px',
      backgroundColor: 'inherit',
      color: 'inherit',
      fontSize: gapFontSize(),
    };
    return {
      ...commonStyles,
      fontWeight: 700,
      textAlign: 'center',
      letterSpacing: '1px',
      ...theme.font.primary,
    };
  };
  const onSubmenuListLinkStyles = (): CSSObject => {
    const isOnCtaRedesign2024Enabled = !!enabledFeatures?.['on-cta-redesign-2024'] && useOnCtaRedesign2024;
    const conditionalOnCTARedesignStyles: CSSObject = isOnCtaRedesign2024Enabled
      ? {
          display: 'flex',
          justifyContent: 'center',
          alignContent: 'center',
          flexWrap: 'wrap',
          height: '100%',
          color: theme.color.b1,
          whiteSpace: 'normal',
          fontSize: '12px',
          lineHeight: 14 / 12,
          letterSpacing: '1.44px',
          padding: '6px',
          '&:hover': {
            backgroundColor: theme.color.b1,
            color: theme.color.wh,
          },
        }
      : {};
    return {
      ...conditionalOnCTARedesignStyles,
      textAlign: 'center',
      ...theme.font.secondary,
    };
  };
  const brandStyles = forBrands(theme, {
    gap: gapStyles(),
    gapfs: gapStyles(),
    on: onSubmenuListLinkStyles(),
    br: () => ({
      padding: '1rem',
      textAlign: 'center',
      textTransform: 'uppercase',
      transition: 'none',
      '&:hover, &:focus': {
        background: theme.color.bk,
        color: theme.color.wh,
      },
      ...theme.font.secondary,
    }),
    brfs: () => ({
      padding: '1rem',
      textAlign: 'center',
      textTransform: 'uppercase',
      transition: 'none',
      '&:hover, &:focus': {
        background: theme.color.bk,
        color: theme.color.wh,
      },
      ...theme.font.secondary,
    }),
    at: () => ({
      textAlign: 'center',
      fontSize: isDesktop ? '16px' : '14px',
      '&:hover, &:focus': {
        textDecoration: 'underline',
      },
      padding: isDesktop ? '1.375rem 0' : '1.125rem 0',
      ...theme?.brandFont,
    }),
  }) as CSSObject;

  return {
    cursor: 'pointer',
    display: 'block',
    padding: isDesktop ? '0.5rem 0' : '0.75rem 0',
    textDecoration: 'none',
    transition: 'color 0.3s ease',
    whiteSpace: 'nowrap',
    fontSize: 'inherit',
    textAlign: 'left',
    color: theme.color.bk,
    ...brandStyles,
  };
});
