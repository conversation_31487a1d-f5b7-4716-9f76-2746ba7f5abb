import { LARGE, SMALL } from '@ecom-next/core/breakpoint-provider';
import { <PERSON><PERSON>, StitchStyleProvider } from '@ecom-next/core/react-stitch';
import React from 'react';
import { act, fireEvent, render, RenderOptions, RenderResult, screen } from 'test-utils';
import { OnCtaRedesign2024Context } from '../../contexts/OnCtaRedesign2024Context';
import { jumpToSection } from '../../helper/jumpToSection';
import { wcdTracking } from '../../helper/wcdTracking';
import { CommonStyleProps } from '../../index';
import { ButtonDropdown, ButtonDropdownProps } from './index';

const mockUseEnabledFeatures = jest.fn();
jest.mock('@ecom-next/core/react-stitch', () => ({
  ...jest.requireActual('@ecom-next/core/react-stitch'),
  useEnabledFeatures: () => mockUseEnabledFeatures(),
}));
jest.mock('../../helper/wcdTracking');
jest.mock('../../helper/jumpToSection');

const baseProps = {
  heading: {
    text: 'heading text',
  },
  submenu: [
    {
      href: 'girls',
      text: 'Girls',
      trackingId: '123',
      isAJumplink: true,
      jumplinkCSSSelector: '.faceted-grid',
      target: '_blank',
    },
    {
      href: 'boys',
      text: 'Boys',
      trackingId: '456',
    },
  ],
  buttonStyle: {
    desktopStyle: {
      color: 'red',
    },
    style: {
      color: 'blue',
    },
  },
  submenuItemStyles: {
    desktopStyle: {
      color: 'orange',
    },
    style: {
      color: 'green',
    },
  },
  submenuListStyles: {
    desktopStyle: {
      color: 'pink',
    },
    style: {
      color: 'purple',
    },
  },
  startsExpanded: false,
};

const getButtonDropdownContainer = (): HTMLElement => screen.getByTestId(/button-dropdown-container/i);

const getButtonDropdown = (): HTMLElement => screen.getByRole('button', { name: /heading text/i });

const getSubmenuList = (hidden = false): HTMLElement => screen.getByRole('list', { hidden });

const getSubmenuListLink = (name?: string): HTMLElement | null => screen.queryByRole('link', { name });

type RenderButtonDropdownProps = {
  options?: RenderOptions;
  props?: ButtonDropdownProps;
};

const renderButtonDropdown = ({ props, options }: RenderButtonDropdownProps): RenderResult =>
  render(<ButtonDropdown {...{ ...baseProps, ...props }} />, options);

describe('<ButtonDropdown />', () => {
  describe('test function in click handler', () => {
    test('handleLinkClick for wcdTracking & jumpToSection functions', async () => {
      renderButtonDropdown({});
      const link = screen.getByText(baseProps.submenu[1].text);
      await act(async () => {
        fireEvent.click(link);
      });

      expect(wcdTracking).toHaveBeenCalled();
      expect(jumpToSection).toHaveBeenCalled();
    });
  });

  describe('snapshots', () => {
    describe('for Gap and Gap Factory Store brands', () => {
      const renderGapButtonDropdown = options =>
        render(
          <StitchStyleProvider brand={Brands.Gap}>
            <ButtonDropdown {...{ ...baseProps }} />
          </StitchStyleProvider>,
          options
        );

      test('closed dropdown', () => {
        renderGapButtonDropdown({});
        expect(getButtonDropdownContainer()).toMatchSnapshot();
      });

      test('open dropdown', async () => {
        renderGapButtonDropdown({});
        await act(async () => {
          fireEvent.click(getButtonDropdown());
        });
        expect(getButtonDropdownContainer()).toMatchSnapshot();
      });

      test('mobile closed dropdown', () => {
        renderGapButtonDropdown({
          options: {
            breakpoint: SMALL,
          },
        });
        expect(getButtonDropdownContainer()).toMatchSnapshot();
      });

      test('mobile open dropdown', async () => {
        renderGapButtonDropdown({
          options: {
            breakpoint: SMALL,
          },
        });
        await act(async () => {
          fireEvent.click(getButtonDropdown());
        });
        expect(getButtonDropdownContainer()).toMatchSnapshot();
      });
    });

    describe('for Old Navy brand', () => {
      const renderOldNavyButtonDropdown = options =>
        render(
          <StitchStyleProvider brand={Brands.OldNavy}>
            <ButtonDropdown {...{ ...baseProps }} />
          </StitchStyleProvider>,
          options
        );

      test('closed dropdown', () => {
        renderOldNavyButtonDropdown({});
        expect(getButtonDropdownContainer()).toMatchSnapshot();
      });

      test('open dropdown', async () => {
        renderOldNavyButtonDropdown({});
        await act(async () => {
          fireEvent.click(getButtonDropdown());
        });
        expect(getButtonDropdownContainer()).toMatchSnapshot();
      });

      test('mobile closed dropdown', () => {
        renderOldNavyButtonDropdown({
          options: {
            breakpoint: SMALL,
          },
        });
        expect(getButtonDropdownContainer()).toMatchSnapshot();
      });
    });

    describe('for Athleta brand', () => {
      const renderAthletaButtonDropdown = options =>
        render(
          <StitchStyleProvider brand={Brands.Athleta}>
            <ButtonDropdown {...{ ...baseProps }} />
          </StitchStyleProvider>,
          options
        );

      test('closed dropdown', () => {
        renderAthletaButtonDropdown({});
        expect(getButtonDropdownContainer()).toMatchSnapshot();
      });

      test('open dropdown', async () => {
        renderAthletaButtonDropdown({});
        await act(async () => {
          fireEvent.click(getButtonDropdown());
        });
        expect(getButtonDropdownContainer()).toMatchSnapshot();
      });

      test('mobile closed dropdown', () => {
        renderAthletaButtonDropdown({
          options: {
            breakpoint: SMALL,
          },
        });
        expect(getButtonDropdownContainer()).toMatchSnapshot();
      });
    });

    describe('with flag on-cta-redesign-2024 as true and OnCtaRedesign2024Context is enabled', () => {
      beforeEach(() => {
        mockUseEnabledFeatures.mockReturnValue({
          'on-cta-redesign-2024': true,
        });
      });
      const renderOnButtonDropdown = options =>
        render(
          <OnCtaRedesign2024Context.Provider value={{ enable: true }}>
            <StitchStyleProvider brand={Brands.OldNavy}>
              <ButtonDropdown {...{ ...baseProps }} />
            </StitchStyleProvider>{' '}
          </OnCtaRedesign2024Context.Provider>,
          options
        );

      test('closed dropdown', () => {
        renderOnButtonDropdown({});
        expect(getButtonDropdownContainer()).toMatchSnapshot();
      });

      test('open dropdown', async () => {
        renderOnButtonDropdown({});
        await act(async () => {
          fireEvent.click(getButtonDropdown());
        });
        expect(getButtonDropdownContainer()).toMatchSnapshot();
      });

      test('mobile closed dropdown', () => {
        renderOnButtonDropdown({
          options: {
            breakpoint: SMALL,
          },
        });
        expect(getButtonDropdownContainer()).toMatchSnapshot();
      });
    });

    test('mobile open dropdown', async () => {
      renderButtonDropdown({
        options: {
          breakpoint: SMALL,
        },
      });
      await act(async () => {
        fireEvent.click(getButtonDropdown());
      });

      expect(getButtonDropdownContainer()).toMatchSnapshot();
    });
  });
});

describe('closed state', () => {
  test('aria-expanded is false for aria users', () => {
    renderButtonDropdown({});

    expect(getButtonDropdown()).toHaveAttribute('aria-expanded', 'false');
  });

  test('list aria-hidden should be true', () => {
    renderButtonDropdown({});

    expect(getSubmenuList(true)).toBeInTheDocument();
  });
});

describe('open state', () => {
  test('aria-expanded is true for aria users', async () => {
    renderButtonDropdown({});
    await act(async () => {
      fireEvent.click(getButtonDropdown());
    });

    expect(getButtonDropdown()).toHaveAttribute('aria-expanded', 'true');
  });

  test('list aria-hidden should be false', async () => {
    renderButtonDropdown({});
    await act(async () => {
      fireEvent.click(getButtonDropdown());
    });

    expect(getSubmenuList()).toBeInTheDocument();
  });
});

describe('when startsExpanded is true', () => {
  beforeEach(() => renderButtonDropdown({ props: { startsExpanded: true } }));
  test('aria-expanded is true for aria users', () => {
    expect(getButtonDropdown()).toHaveAttribute('aria-expanded', 'true');
  });
  test('list aria-hidden should be false', () => {
    expect(getSubmenuList()).toBeInTheDocument();
  });
  test('aria-expanded is false for aria users', async () => {
    await act(async () => {
      fireEvent.click(getButtonDropdown());
    });

    expect(getButtonDropdown()).toHaveAttribute('aria-expanded', 'false');
  });
  test('list aria-hidden should be true', async () => {
    await act(async () => {
      fireEvent.click(getButtonDropdown());
    });
    expect(getSubmenuList(true)).toBeInTheDocument();
  });
});

describe('dropdown button items', () => {
  test('menu renders links with proper attributes', async () => {
    renderButtonDropdown({});
    await act(async () => {
      fireEvent.click(getButtonDropdown());
    });

    baseProps.submenu.forEach(({ text, href, target }) => {
      expect(getSubmenuListLink(text)).toHaveAttribute('href', href);
      expect(getSubmenuListLink(text)).toHaveAttribute('target', target);
    });
  });

  test('links should not be accessible when not expanded', () => {
    renderButtonDropdown({});

    baseProps.submenu.forEach(({ text }) => {
      expect(getSubmenuListLink(text)).not.toBeInTheDocument();
    });
  });

  test('links should be accessible when expanded', async () => {
    renderButtonDropdown({});
    await act(async () => {
      fireEvent.click(getButtonDropdown());
    });

    baseProps.submenu.forEach(({ text }) => {
      expect(getSubmenuListLink(text)).toBeInTheDocument();
    });
  });
});

describe('custom styles', () => {
  describe('<ComposableButton />', () => {
    test.each`
      isDesktop | styleProp
      ${true}   | ${'desktopStyle'}
      ${false}  | ${'style'}
    `('renders styles exclusively for $styleProp', ({ isDesktop, styleProp }) => {
      renderButtonDropdown({
        options: {
          breakpoint: isDesktop ? LARGE : SMALL,
        },
      });
      fireEvent.click(getButtonDropdown());

      expect(getButtonDropdown()).toHaveStyleRule('color', baseProps.buttonStyle[styleProp as keyof CommonStyleProps].color);
    });
  });

  describe('<SubmenuList />', () => {
    test.each`
      isDesktop | styleProp
      ${true}   | ${'desktopStyle'}
      ${false}  | ${'style'}
    `('renders styles exclusively for $styleProp', ({ isDesktop, styleProp }) => {
      renderButtonDropdown({
        options: {
          breakpoint: isDesktop ? LARGE : SMALL,
        },
      });
      fireEvent.click(getButtonDropdown());

      expect(getSubmenuList()).toHaveStyleRule('color', baseProps.submenuListStyles[styleProp as keyof CommonStyleProps].color);
    });
  });

  describe('<SubmenuListLink>', () => {
    test.each`
      isDesktop | styleProp
      ${true}   | ${'desktopStyle'}
      ${false}  | ${'style'}
    `('renders styles exclusively for $styleProp', ({ isDesktop, styleProp }) => {
      renderButtonDropdown({
        options: {
          breakpoint: isDesktop ? LARGE : SMALL,
        },
      });
      fireEvent.click(getButtonDropdown());
      baseProps.submenu.forEach(({ text }) => {
        expect(getSubmenuListLink(text)).toHaveStyleRule('color', baseProps.submenuItemStyles[styleProp as keyof CommonStyleProps].color);
      });
    });
  });
});
