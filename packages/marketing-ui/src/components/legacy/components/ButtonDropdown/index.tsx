// @ts-nocheck
'use client';
import { BreakpointContext, LARGE } from '@ecom-next/core/breakpoint-provider';
import { CSSObject, useEnabledFeatures, forBrands, styled, Theme, useTheme } from '@ecom-next/core/react-stitch';
import React, { DetailedHTMLProps, LiHTMLAttributes, useContext, useState } from 'react';
import { OnCtaRedesign2024Context } from '../../contexts/OnCtaRedesign2024Context';
import { jumpToSection } from '../../helper/jumpToSection';
import mapDataToProps from '../../helper/mapDataToProps';
import { wcdTracking } from '../../helper/wcdTracking';
import { Color, ComposableButton, ComposableButtonProps, Variant } from '../ComposableButton/components';
import { ButtonIcon } from './components/ButtonIcon';
import { SubmenuList } from './components/SubmenuList';
import { SubmenuListItem } from './components/SubmenuListItem';
import { SubmenuListLink } from './components/SubmenuListLink';
import { useSelectedButtonStyles } from './hooks';
import {
  BackwardCompatibleStyles,
  BreakpointProp,
  ButtonDropdownContainerProps,
  ButtonDropdownProps,
  DefaultStyleFn,
  HandleLinkClickProps,
  SubmenuItem,
  SubmenuListItemProps,
} from './types';

const subMenuOpen: CSSObject = {
  maxHeight: '800px',
  transition: 'max-height .3s ease-in',
  visibility: 'visible',
};

const subMenuClosed: CSSObject = {
  maxHeight: 0,
  transition: 'max-height .5s ease-out,visibility 0s .5s',
  visibility: 'hidden',
};

const ButtonDropDownContainer = styled.div<ButtonDropdownContainerProps>(({ breakpoint, theme }) => {
  const isDesktop = breakpoint === 'desktop';
  const isOldNavyBrand = theme.brand === 'on';
  const enabledFeatures = useEnabledFeatures();
  const useOnCtaRedesign2024 = useContext(OnCtaRedesign2024Context).enable;
  const isOnCtaRedesign2024Enabled = !!enabledFeatures?.['on-cta-redesign-2024'] && useOnCtaRedesign2024;

  const conditionalOnCTARedesignStyles = () => {
    if (isOnCtaRedesign2024Enabled && isOldNavyBrand) {
      return isDesktop ? { minWidth: 136, maxWidth: 255 } : { minWidth: 136, maxWidth: 359 };
    }
    return {};
  };

  const desktopStyles = isDesktop
    ? {
        '&:not(:first-child)': {
          marginLeft: 'none',
        },
      }
    : {};

  return {
    fontSize: '0.9735rem',
    width: '100%',
    ...desktopStyles,
    ...conditionalOnCTARedesignStyles(),
  };
});

export const StyledHeadingContainer = styled.span<{
  theme: Theme;
}>(({ theme }) => {
  const enabledFeatures = useEnabledFeatures();
  const useOnCtaRedesign2024 = useContext(OnCtaRedesign2024Context).enable;
  const isOnCtaRedesign2024Enabled = !!enabledFeatures?.['on-cta-redesign-2024'] && useOnCtaRedesign2024;
  if (theme.brand === 'on' && isOnCtaRedesign2024Enabled) {
    return {
      margin: '0 auto',
    };
  }
  return {};
});

const getBackwardsCompatibleStyles = (prop: BackwardCompatibleStyles): BackwardCompatibleStyles => ({
  desktop: prop?.desktopStyle || prop?.desktop,
  mobile: prop?.style || prop?.mobile,
});

export const getFontColor = (theme: Theme): CSSObject =>
  forBrands(theme, {
    gap: theme => theme.color.b1,
    gapfs: theme => theme.color.b1,
    on: theme => theme.color.b1,
    default: theme => theme.color.wh,
  }) as CSSObject;

const getButtonStyles: DefaultStyleFn = ({ theme, breakpoint, isOnCtaRedesign2024Enabled }) => {
  const isDesktop = breakpoint === 'desktop';
  const svgCSS: CSSObject = {
    'svg path': {
      fill: `${theme.color.wh} !important`,
    },
  };
  const onButtonDropdownStyles = (): CSSObject => {
    const conditionalOnStyles: CSSObject = isOnCtaRedesign2024Enabled
      ? {
          lineHeight: '14px',
          letterSpacing: '1.68px',
          fontSize: '14px',
          padding: '6px',
          fontWeight: 700,
          maxHeight: '44px',
          '&:hover': {
            backgroundColor: theme.color.b1,
            color: theme.color.wh,
            '& svg path': {
              fill: theme.color.wh,
            },
          },
          '&&[aria-expanded="true"]': {
            backgroundColor: theme.color.bk,
            borderColor: theme.color.bk,
            color: theme.color.wh,
            textDecoration: 'underline',
            '& svg path': {
              fill: theme.color.wh,
            },
          },
        }
      : {
          lineHeight: 1,
          letterSpacing: 0,
          fontSize: '1rem',
          padding: '0 0.8em',
        };
    return {
      ...conditionalOnStyles,
      margin: 0,
      textTransform: 'uppercase',
      backgroundColor: theme.color.wh,
      borderWidth: '2px',
      borderColor: theme.color.b1,
      display: 'flex', // center icon
    };
  };

  const brandStyles = forBrands(theme, {
    on: () => onButtonDropdownStyles(),
    br: theme => ({
      lineHeight: 1,
      margin: 0,
      borderWidth: 0,
      letterSpacing: 0,
      fontSize: '1rem',
      textTransform: 'uppercase',
      padding: isDesktop ? '0.75rem 1rem' : '1rem 0',
      backgroundColor: theme.color.b1,
      ...svgCSS,
    }),
    brfs: theme => ({
      lineHeight: 1,
      margin: 0,
      borderWidth: 0,
      letterSpacing: 0,
      fontSize: '1rem',
      textTransform: 'uppercase',
      padding: isDesktop ? '0.75rem 1rem' : '1rem 0',
      backgroundColor: theme.color.b1,
      ...svgCSS,
    }),
    at: theme => ({
      ...svgCSS,
      display: 'flex', // center icon
      backgroundColor: theme.color.bk,
      color: theme.color.wh,
      height: isDesktop ? '60px' : '50px',
      fontSize: isDesktop ? '16px' : '14px',
      '&:hover': {
        textDecoration: 'underline',
      },
      '& > span': {
        padding: '0',
      },
    }),
    gap: theme => ({
      lineHeight: 1,
      margin: 0,
      textTransform: 'uppercase',
      backgroundColor: theme.color.wh,
      textAlign: 'center',
      borderColor: theme.color.g3,
      borderWidth: '1px 0',
      fontWeight: 700,
      letterSpacing: '1px',
      padding: '7px 1.5rem',
      minHeight: 0,
      width: 'fit-content !important', // allows the width to be determined by the content of the button
      fontSize: isDesktop ? '12px' : 'inherit',
      '& > span': {
        padding: '0',
      },
    }),
    gapfs: theme => ({
      lineHeight: 1,
      margin: 0,
      textTransform: 'uppercase',
      backgroundColor: theme.color.wh,
      textAlign: 'center',
      borderColor: theme.color.g3,
      borderWidth: '1px 0',
      fontWeight: 700,
      letterSpacing: '1px',
      padding: '7px 1.5rem',
      minHeight: 0,
      width: 'fit-content !important', // allows the width to be determined by the content of the button
      fontSize: isDesktop ? '12px' : 'inherit',
      '& > span': {
        padding: '0',
      },
    }),
    default: theme => ({
      lineHeight: 1,
      margin: 0,
      textTransform: 'uppercase',
      backgroundColor: theme.color.wh,
      textAlign: 'center',
      borderColor: theme.color.g3,
      borderWidth: '1px 0',
      fontWeight: 700,
      letterSpacing: '1px',
      padding: isDesktop ? '7px 1.5rem 0.75rem' : '7px 0.5rem 0.75rem',
      fontSize: isDesktop ? '12px' : 'inherit',
    }),
  }) as CSSObject;

  return {
    display: 'block',
    width: '100%',
    textAlign: 'center',
    boxSizing: 'border-box',
    color: getFontColor(theme),
    ...brandStyles,
  };
};

export const ButtonDropdown = ({
  DropdownButtonComponent,
  SubItemComponent = SubmenuListItem,
  buttonStyle,
  customClasses,
  heading,
  id,
  submenu,
  submenuItemStyles: rawSubmenuListLinkStyles,
  submenuListStyles: rawSubmenuListStyles,
  style,
  startsExpanded = false,
  selectedLink = undefined,
  customSubMenuStyles = undefined,
  naturalWidth,
}: ButtonDropdownProps & {
  DropdownButtonComponent?: (props: Omit<ComposableButtonProps, 'style'> & { children: React.ReactNode }) => React.ReactElement | null;
  SubItemComponent?: (
    props:
      | SubmenuListItemProps
      | (DetailedHTMLProps<LiHTMLAttributes<HTMLLIElement>, HTMLLIElement> & {
          children: JSX.Element;
          breakpoint: BreakpointProp['breakpoint'];
        })
  ) => React.ReactElement | null;
}): JSX.Element => {
  const [isExpanded, setIsExpanded] = useState(startsExpanded);
  const { minWidth } = useContext(BreakpointContext);
  const breakpoint = minWidth(LARGE) ? 'desktop' : 'mobile';
  const isDesktop = breakpoint === 'desktop';
  const theme = useTheme();
  const enabledFeatures = useEnabledFeatures();
  const useOnCtaRedesign2024 = useContext(OnCtaRedesign2024Context).enable;
  const isOnCtaRedesign2024Enabled = !!enabledFeatures?.['on-cta-redesign-2024'] && useOnCtaRedesign2024;

  const submenuListLinkStylesBP = getBackwardsCompatibleStyles(rawSubmenuListLinkStyles);

  const subMenuListLinkStyles = {
    ...submenuListLinkStylesBP?.mobile,
    ...(isDesktop && submenuListLinkStylesBP?.desktop),
  };

  const submenuListStylesByBP = getBackwardsCompatibleStyles(rawSubmenuListStyles);

  const subMenuListStyles = {
    ...(isExpanded ? subMenuOpen : subMenuClosed),
    ...submenuListStylesByBP?.mobile,
    ...(isDesktop && submenuListStylesByBP?.desktop),
    ...customSubMenuStyles,
  };

  const backwardsCompatibleButtonStyle = {
    desktop: buttonStyle?.desktopStyle || style?.desktop,
    mobile: buttonStyle?.style || style?.mobile,
  };

  const buttonStyles = {
    ...(!DropdownButtonComponent ? getButtonStyles({ theme, breakpoint, isOnCtaRedesign2024Enabled }) : {}),
    ...backwardsCompatibleButtonStyle.mobile,
    ...(isDesktop && backwardsCompatibleButtonStyle.desktop),
  };

  const DropdownButton = DropdownButtonComponent || ComposableButton;

  const handleLinkClick = ({ isAJumplink = false, jumplinkCSSSelector = '', trackingId }: HandleLinkClickProps): void => {
    wcdTracking(trackingId);
    jumpToSection(isAJumplink, jumplinkCSSSelector);
  };

  const selectedButtonStyles = useSelectedButtonStyles();

  const defaultVariant = theme.brand === 'on' && isOnCtaRedesign2024Enabled ? Variant.border : Variant.outline;

  return (
    <ButtonDropDownContainer breakpoint={breakpoint} data-testid='button-dropdown-container'>
      <DropdownButton
        aria-expanded={isExpanded}
        className={customClasses}
        color={Color.primary}
        css={buttonStyles}
        fullWidth
        id={id}
        onClick={() => {
          setIsExpanded(!isExpanded);
        }}
        variant={defaultVariant}
      >
        <StyledHeadingContainer theme={theme}>{heading?.text}</StyledHeadingContainer>
        <ButtonIcon isExpanded={isExpanded} theme={theme} />
      </DropdownButton>
      <SubmenuList aria-hidden={!isExpanded} breakpoint={breakpoint} css={subMenuListStyles} variant={defaultVariant} naturalWidth={naturalWidth}>
        {submenu?.map(({ href, isAJumplink, jumplinkCSSSelector, target, text, trackingId }: SubmenuItem, index) => (
          <SubItemComponent key={`${href}_${index.toString()}`} breakpoint={breakpoint} variant={defaultVariant}>
            <SubmenuListLink
              breakpoint={breakpoint}
              css={[subMenuListLinkStyles, selectedLink && selectedLink === href ? selectedButtonStyles : undefined]}
              onClick={() =>
                handleLinkClick({
                  isAJumplink,
                  jumplinkCSSSelector,
                  trackingId,
                })
              }
              target={target}
              to={href}
            >
              {text}
            </SubmenuListLink>
          </SubItemComponent>
        ))}
      </SubmenuList>
    </ButtonDropDownContainer>
  );
};

export default mapDataToProps(ButtonDropdown);
export type { ButtonDropdownProps } from './types';
