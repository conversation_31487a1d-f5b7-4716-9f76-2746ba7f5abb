// @ts-nocheck
'use client';
import React from 'react';
import README from '../README.mdx';
import JSONButtonDropdown from '..';
import { OnCtaRedesign2024Context } from '../../../contexts/OnCtaRedesign2024Context';

const withPadding = Story => (
  <div css={{ padding: '1rem' }} data-testid='buttondropdownroot'>
    <Story />
  </div>
);

const baseProps = {
  heading: { text: 'Shop Halloween styles' },
  id: 'sampleID',
  submenu: [
    {
      href: 'girls',
      isAJumplink: true,
      jumplinkCSSSelector: '.faceted-grid',
      target: '_self',
      text: 'Girls',
      trackingId: 'abc_123_1',
    },
    {
      href: 'boys',
      isAJumplink: true,
      jumplinkCSSSelector: '.faceted-grid',
      target: '_self',
      text: 'Boys',
      trackingId: 'abc_123_1',
    },
    {
      href: 'toddler girl',
      isAJumplink: true,
      jumplinkCSSSelector: '.faceted-grid',
      target: '_self',
      text: 'Toddler girl',
      trackingId: 'abc_123_1',
    },
    {
      href: 'toddler boy',
      isAJumplink: true,
      jumplinkCSSSelector: '.faceted-grid',
      target: '_self',
      text: 'Toddler boy',
      trackingId: 'abc_123_1',
    },
    {
      href: 'baby boy',
      isAJumplink: true,
      jumplinkCSSSelector: '.faceted-grid',
      target: '_self',
      text: 'Baby boy',
      trackingId: 'abc_123_1',
    },
  ],
  buttonStyle: {
    style: {},
    desktopStyle: {},
  },
  submenuItemStyles: {
    style: {},
    desktopStyle: {},
  },
  submenuListStyles: {
    style: {},
    desktopStyle: {},
  },
  customClasses: '',
};

const getJSON = () => baseProps;

export default {
  title: 'Common/JSON Components (Marketing)/ButtonDropdown',
  decorators: [withPadding],
  parameters: {
    docs: {
      page: README,
    },
  },
  tags: ['exclude'],
};

const JsonButtonDropdownTemplate = args => (
  <OnCtaRedesign2024Context.Provider value={{ enable: true }}>
    <JSONButtonDropdown {...args} />
  </OnCtaRedesign2024Context.Provider>
);

export const JsonConfiguration = JsonButtonDropdownTemplate.bind({});
JsonConfiguration.args = { data: { ...getJSON() } };

JsonConfiguration.storyName = 'json configuration';
