# CSSAnimation

The `CSSAnimation` component is a wrapper that enables Marketing-UI Components<sup>\*</sup> to take on CSS3 animations. It provides a way to play or pause animations that are set to loop, much like a video. Animations are also set to play only when the marketing feature is in view.

NOTE: This is not intended to use for entire page layout. If you would like to animate multiple marketing messages (i.e. additional animations below the page fold, please use a 2nd CSSAnimation component for better performance).

<sup>\*</sup> Currently enabled only for BuiltIn child components at this time.

## Basic Example

```json
{
  "name": "CSSAnimation",
  "type": "sitewide",
  "data": {
    "animations": [
      {
        "animation": {
          "keyframes": "0% {transform: translateX(0px);}100% {transform: translateX(-25%);}",
          "duration": "20s",
          "direction": "alternate",
          "delay": "50ms",
          "iterationCount": "infinite",
          "fillMode": "both",
          "timingFunction": "linear"
        },
        "component": {
          "name": "div",
          "type": "builtin",
          "data": {
            "style": {
              "display": "flex",
              "justifyContent": "center",
              "alignItems": "center",
              "background": "#dee",
              "padding": "20px"
            },
            "components": ["Bounce"]
          }
        }
      }
    ]
  }
}
```

# Props

| prop                        | type         | default value | description                              |
| --------------------------- | ------------ | ------------- | ---------------------------------------- |
| [`animations`](#animations) | `array`      | `undefined`   | `array of animation & component objects` |
| [`controls`](#controls)     | `Object?`    | `undefined`   | `Object alt/src`                         |
| [`options`](#options)       | `CSSObject?` | `undefined`   | `animation options`                      |
| [`style`](#style)           | `CSSObject?` | `undefined`   | `Parent styles`                          |

# Using the `CSSAnimation` component

## Animations

Add an `animations` array to hold all of the components and associated animations. The grouping of the animations coincides with the order of the components in the components array. If a component does not require any animation, simply remove the `animation` prop.

NOTE: The current version of this component only supports a single component/animation. Any additional components will share the control button.<br /><br />

```
"animations": [
  {
    "animation": {
      "keyframes":
        "0% {transform: translateX(0px);}100% {transform: translateX(-25%);}",
      "duration": "20s",
      "direction": "alternate",
      "delay": "50ms",
      "iterationCount": "infinite",
      "fillMode": "both",
      "timingFunction": "linear"
    },
    component: {
      "name": "div",
      "type": "builtin",
      "data": {
        "style": {
          "display": "flex",
          "justifyContent": "center",
          "alignItems": "center",
          "background": "#dee",
          "padding": "20px"
        },
        "components": ["Bounce"]
      }
    }
  }
],
```

### Animation Props

| prop             | type                                             | default value | description                                                                                                                           |
| ---------------- | ------------------------------------------------ | ------------- | ------------------------------------------------------------------------------------------------------------------------------------- |
| `keyframes`      | `string`                                         | `undefined`   | `one line with all keyframe data`                                                                                                     |
| `duration`       | `number\|string?`                                | `1s`          | `duration of animation in 's' or 'ms'`                                                                                                |
| `direction`      | `normal\|reverse\|alternate\|alternate-reverse?` | `normal`      | `direction of the animation`                                                                                                          |
| `delay`          | `number\|string?`                                | `0s`          | `time to wait before animation starts`                                                                                                |
| `iterationCount` | `number\|string?`                                | `1`           | `number of times to animate`                                                                                                          |
| `fillMode`       | `none\|forwards\|backwards\|both?`               | `none`        | `how an animation applies styles through each cycle` [See more](https://developer.mozilla.org/en-US/docs/Web/CSS/animation-fill-mode) |
| `timingFunction` | `string?`                                        | `ease`        | `how an animation progress through each cycle` [See more](https://developer.mozilla.org/en-US/docs/Web/CSS/animation-timing-function) |

See https://www.w3schools.com/css/css3_animations.asp for details about how to work with CSS3 Animations

## Controls

Optionally add a `controls` object to add a play/pause button to control animations. This is ideally used for animations that loop infinitely or 5 or more seconds (for a11y standards).

NOTE: "playAltText" and "pauseAltText" are <b>optional</b>. If these are not in the JSON then a localized default will be used instead.

```
"controls": {
  "playBtnSrc": "/Asset_Archive/ONWeb/content/lp-assets/denim-fit-guide/v2/assets/white-play-btn.svg",
  "pauseBtnSrc": "/Asset_Archive/ONWeb/content/lp-assets/denim-fit-guide/v2/assets/white-pause-btn.svg",
  "playAltText": "Play Animation", (this is optional and localized default will be used if not given)
  "pauseAltText": "Pause Animation" (this is optional and localized default will be used if not given)
},
```

## Options

Optionally add an `options` object to finetune things like when animations should auto trigger when the feature is in view or setting the animation to not autoplay.

```
"options": {
  "autoPlay": true,
  "rootMargin": "-50%",
  "restartWhenVisible": true
},
```

### Animations pause when not within view

CSSAnimation uses IntersectionObserver and is set to only animate when the component is within the viewport. To control exactly how far into the viewport the component must be in order for the animation to play, you can specify the `rootMargin`. This is defaulted to 0px if not specified. By defining a negative value (like -300px or -20%), you can essentially expose a portion of the feature before it begins playing or have it pause before you've scrolled the component out of view.

If a user has interacted with the pause button, scrolling the component into view will not play the animation until they have pushed the control button to unpause.

### autoPlay

`autoPlay` tells the component to start the animation immediately when in view. This is set to true by default. Setting the feature to autoplay false will set the animation to paused.

### restartWhenVisible

`restartWhenVisible` tells the component to restart the animation when the feature comes back into view. This should only be used when the animation does not loop.

### rootMargin

`rootMargin` tells the browser when the feature is considered in view. If set at '0px', it will consider that the feature is in view when the top edge of the feature is visible and no longer in view if a user scrolls past the bottom edge of the feature.

## Style

Use your standard css style object configuration here

```
  style: {
    overflow: "hidden",
    margin: "0 auto 3000px",
    width: "1000px",
  }
```
