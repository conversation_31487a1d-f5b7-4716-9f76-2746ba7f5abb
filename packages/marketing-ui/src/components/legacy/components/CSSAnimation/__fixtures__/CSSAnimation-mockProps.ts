// @ts-nocheck
import { CSSAnimationProps } from '..';

export const mockJson: CSSAnimationProps = {
  style: {
    overflow: 'auto',
    margin: '0 auto 3000px',
    width: '1000px',
  },
  controls: {
    style: {
      right: '20px',
      left: 'auto',
    },
    playBtnSrc: 'https://oldnavy.gap.com/Asset_Archive/ONWeb/content/lp-assets/denim-fit-guide/v2/assets/white-play-btn.svg',
    pauseBtnSrc: 'https://oldnavy.gap.com/Asset_Archive/ONWeb/content/lp-assets/denim-fit-guide/v2/assets/white-pause-btn.svg',
    playAltText: 'Play animation',
    pauseAltText: 'Pause animation',
  },
  options: {
    autoPlay: false,
    rootMargin: '0%',
    restartWhenVisible: true,
  },
  animations: [
    {
      animation: {
        keyframes: '0% {transform: translateX(0px);}100% {transform: translateX(-25%);}',
        duration: '20s',
        direction: 'alternate',
        delay: '50ms',
        iterationCount: 'infinite',
        fillMode: 'both',
      },
      component: {
        type: 'builtin',
        name: 'img',
        data: {
          props: {
            style: {
              width: '2000px',
              maxWidth: '2000px',
            },
            src: 'https://oldnavy.gap.com/Asset_Archive/ONWeb/content/0020/645/000/assets/210614_95_M2413_NonCard_Hero_HP_US_XL_2880.jpg',
            alt: 'Gap Cards',
          },
        },
      },
    },
  ],
};
