// @ts-nocheck
export const horizontalLayoutData = {
  name: 'CSSAnimation',
  type: 'sitewide',
  data: {
    style: {
      overflow: 'hidden',
      margin: '0 auto 3000px',
      width: '1000px',
      '@media (max-width: 768px)': {
        width: '100%',
      },
    },
    controls: {
      style: {
        right: '20px',
        left: 'auto',
      },
      playBtnSrc: 'https://oldnavy.gap.com/Asset_Archive/ONWeb/content/lp-assets/denim-fit-guide/v2/assets/white-play-btn.svg',
      pauseBtnSrc: 'https://oldnavy.gap.com/Asset_Archive/ONWeb/content/lp-assets/denim-fit-guide/v2/assets/white-pause-btn.svg',
      playAltText: 'Play animation',
      pauseAltText: 'Pause animation',
    },
    options: {
      autoPlay: false,
      rootMargin: '0%',
    },
    animations: [
      {
        animation: {
          keyframes: '0% {transform: translateX(0px);}100% {transform: translateX(-25%);}',
          duration: '20s',
          direction: 'alternate',
          delay: '50ms',
          iterationCount: 'infinite',
          fillMode: 'both',
          timingFunction: 'linear',
        },
        component: {
          type: 'builtin',
          name: 'div',
          data: {
            props: {
              style: {
                display: 'flex',
                'flex-direction': 'row',
              },
            },
            components: [
              {
                type: 'builtin',
                name: 'img',
                data: {
                  props: {
                    style: {
                      width: '75%',
                    },
                    src: '/Asset_Archive/ONWeb/content/0029/627/469/assets/221000_43_M4728_Gifting_HP_Secondary_Banner_1_SM_US.jpg',
                    alt: 'Gap Cards',
                  },
                },
              },
              {
                type: 'builtin',
                name: 'img',
                data: {
                  props: {
                    style: {
                      width: '75%',
                    },
                    src: 'https://oldnavy.gap.com/Asset_Archive/ONWeb/content/0029/627/469/assets/221000_43_M4728_Gifting_HP_Secondary_Banner_2_SM_US.jpg',
                    alt: 'Gap Cards',
                  },
                },
              },
            ],
            desktopComponents: [
              {
                type: 'builtin',
                name: 'img',
                data: {
                  props: {
                    style: {
                      width: '2000px',
                      maxWidth: '2000px',
                    },
                    src: 'https://oldnavy.gap.com/Asset_Archive/ONWeb/content/0020/645/000/assets/210614_95_M2413_NonCard_Hero_HP_US_XL_2880.jpg',
                    alt: 'Gap Cards',
                  },
                },
              },
            ],
          },
        },
      },
    ],
  },
};

export const verticalLayoutData = {
  name: 'CSSAnimation',
  type: 'sitewide',
  data: {
    style: {
      overflow: 'hidden',
      margin: '20px auto',
      width: '960px',
      height: '400px',
      a: {
        display: 'inherit',
      },
    },
    controls: {
      style: {
        right: '20px',
        left: 'auto',
      },
      playBtnSrc: 'https://oldnavy.gap.com/Asset_Archive/ONWeb/content/lp-assets/denim-fit-guide/v2/assets/white-play-btn.svg',
      pauseBtnSrc: 'https://oldnavy.gap.com/Asset_Archive/ONWeb/content/lp-assets/denim-fit-guide/v2/assets/white-pause-btn.svg',
      playAltText: 'Play animation',
      pauseAltText: 'Pause animation',
    },
    options: {
      autoPlay: false,
      rootMargin: '0%',
    },
    animations: [
      {
        animation: {
          keyframes: '0% {transform: translateY(0px);}100% {transform: translateY(-66%);}',
          duration: '25s',
          direction: 'alternate',
          delay: '50ms',
          iterationCount: 'infinite',
          fillMode: 'both',
          timingFunction: 'linear',
        },
        component: {
          type: 'builtin',
          name: 'div',
          data: {
            props: {
              style: {
                display: 'flex',
                '-webkit-box-orient': 'vertical',
                '-moz-box-orient': 'vertical',
                '-webkit-flex-direction': 'column',
                '-ms-flex-direction': 'column',
                'flex-direction': 'column',
                a: {
                  display: 'inline',
                },
              },
            },
            components: [
              {
                type: 'builtin',
                name: 'img',
                data: {
                  props: {
                    style: {
                      width: '75%',
                    },
                    src: '/Asset_Archive/ONWeb/content/0029/627/469/assets/221000_43_M4728_Gifting_HP_Secondary_Banner_1_SM_US.jpg',
                    alt: 'Gap Cards',
                  },
                },
              },
              {
                type: 'builtin',
                name: 'img',
                data: {
                  props: {
                    style: {
                      width: '75%',
                    },
                    src: 'https://oldnavy.gap.com/Asset_Archive/ONWeb/content/0029/627/469/assets/221000_43_M4728_Gifting_HP_Secondary_Banner_2_SM_US.jpg',
                    alt: 'Gap Cards',
                  },
                },
              },
            ],
            desktopComponents: [
              {
                type: 'builtin',
                name: 'a',
                data: {
                  style: {},
                  props: {
                    href: 'https://www.gap.com',
                    target: '_blank',
                  },
                  components: [
                    {
                      type: 'builtin',
                      name: 'img',
                      data: {
                        style: {},
                        props: {
                          src: 'https://www.gap.com/Asset_Archive/GPWeb/content/0029/608/454/assets/m1_v3/HOL223281_desk_s4.jpg',
                          alt: 'desktop image 1',
                        },
                      },
                    },
                  ],
                },
              },
              {
                type: 'builtin',
                name: 'a',
                data: {
                  style: {},
                  props: {
                    href: 'https://www.gap.com',
                    target: '_blank',
                  },
                  components: [
                    {
                      type: 'builtin',
                      name: 'img',
                      data: {
                        style: {},
                        props: {
                          src: 'https://www.gap.com/Asset_Archive/GPWeb/content/0029/608/454/assets/m1_v3/HOL223281_desk_s5.jpg',
                          alt: 'desktop image 2',
                        },
                      },
                    },
                  ],
                },
              },
              {
                type: 'builtin',
                name: 'a',
                data: {
                  style: {},
                  props: {
                    href: 'https://www.gap.com/?link=2',
                    target: '_blank',
                  },
                  components: [
                    {
                      type: 'builtin',
                      name: 'img',
                      data: {
                        style: {},
                        props: {
                          src: 'https://www.gap.com/Asset_Archive/GPWeb/content/0029/608/454/assets/m1_v3/HOL223281_desk_s1.jpg',
                          alt: 'desktop image 3',
                        },
                      },
                    },
                  ],
                },
              },
            ],
          },
        },
      },
    ],
  },
};

export const defaultIconsData = {
  name: 'CSSAnimation',
  type: 'sitewide',
  data: {
    style: {
      overflow: 'auto',
      margin: '20px auto',
      width: '1200px',
      textAlign: 'center',
      height: '700px',
      a: {
        display: 'inherit',
      },
      '@media (max-width: 768px)': {
        width: '100%',
      },
    },
    controls: {
      style: {
        right: '20px',
        left: 'auto',
      },
    },
    options: {
      autoPlay: false,
      rootMargin: '0%',
    },
    animations: [
      {
        animation: {
          keyframes: '0% { margin-top: -700px; } 100% { margin-top: 0px; }',
          duration: '500ms',
          direction: 'alternate',
          delay: '1s',
          iterationCount: '1',
          fillMode: 'both',
          timingFunction: 'linear',
        },
        component: {
          type: 'builtin',
          name: 'div',
          data: {
            style: {},
            props: {
              href: 'https://www.gap.com/?link=2',
              target: '_blank',
            },
            components: [
              {
                type: 'builtin',
                name: 'img',
                data: {
                  props: {
                    style: {
                      width: '75%',
                    },
                    src: '/Asset_Archive/ONWeb/content/0029/627/469/assets/221000_43_M4728_Gifting_HP_Secondary_Banner_1_SM_US.jpg',
                    alt: 'Gap Cards',
                  },
                },
              },
            ],
            desktopComponents: [
              {
                type: 'builtin',
                name: 'img',
                data: {
                  style: {},
                  props: {
                    src: 'https://www.gap.com/Asset_Archive/GPWeb/content/0029/608/454/assets/m1_v3/HOL223281_desk_s1.jpg',
                    alt: 'desktop image 3',
                  },
                },
              },
            ],
          },
        },
      },
    ],
  },
};

export const multipleAnimations = {
  name: 'BasicAnimation',
  type: 'sitewide',
  data: {
    style: {
      display: 'flex',
      flexDirection: 'row',
      justifyContent: 'space-around',
      alignItems: 'flex-start',
      background: 'rgb(0, 55, 100)',
      padding: '5%',
      position: 'relative',
      height: '500px',
      overflow: 'hidden',
      textAlign: 'center',
    },
    desktopStyle: {
      padding: '5%',
    },
    controls: {
      style: {
        position: 'absolute',
        width: '20px',
        height: '20px',
        top: '20px',
        left: '20px',
      },
      desktopStyle: {
        top: 'auto',
        left: 'auto',
        bottom: '20px',
        right: '20px',
      },
      playBtnSrc: 'https://oldnavy.gap.com/Asset_Archive/ONWeb/content/lp-assets/denim-fit-guide/v2/assets/white-play-btn.svg',
      pauseBtnSrc: 'https://oldnavy.gap.com/Asset_Archive/ONWeb/content/lp-assets/denim-fit-guide/v2/assets/white-pause-btn.svg',
      playAltText: 'Play animation',
      pauseAltText: 'Pause animation',
    },
    options: {
      autoPlay: false,
      rootMargin: '0%',
    },
    animations: [
      {
        animation: {
          keyframes: '0% { margin-top: -700px; } 100% { margin-top: 0px; }',
          duration: '500ms',
          direction: 'alternate',
          delay: '1s',
          iterationCount: '1',
          fillMode: 'both',
          timingFunction: 'linear',
        },
        component: {
          name: 'img',
          type: 'builtin',
          data: {
            style: {
              position: 'absolute',
              background: '#c7379a',
              top: '0',
              left: '0',
              color: '#FFFFFF',
              width: '100%',
              height: '100%',
              zIndex: 0,
            },
            props: {
              src: '/Asset_Archive/ONWeb/content/0029/542/403/assets/220800_78-M8599_primary_slide1_HP_US_SM.jpg',
              alt: 'A group of professional women wearing various dark shades of pixie and stevie style pants.',
            },
            desktopProps: {
              src: '/Asset_Archive/ONWeb/content/0029/542/403/assets/220800_78-M8599_primary_slide1_HP_US_XL.jpg',
            },
          },
        },
      },
      {
        animation: {
          keyframes: '0% { opacity: 0;top:9%; width: 300px; height: 300px; } 100% {opacity: 100; top: 2%; width: 700px; height: 700px; }',
          duration: '3s',
          direction: 'alternate',
          delay: '2s',
          iterationCount: '1',
          fillMode: 'both',
          timingFunction: 'linear',
          opacity: 0,
        },
        component: {
          name: 'div',
          type: 'builtin',
          data: {
            style: {
              position: 'absolute',
              background: '#ff856f',
              color: '#FFFFFF',
              width: '300px',
              height: '300px',
              borderRadius: '50%',
              boxShadow: '0px 10px 5px rgba(0,0,0,.25)',
              opacity: 0.96,
              zIndex: 1,
              textAlign: 'center',
            },
            desktopStyle: {
              width: '500px',
              height: '500px',
            },
            components: [
              {
                name: 'span',
                type: 'builtin',
                data: {
                  style: {
                    fontSize: '48px',
                    fontWeight: 'bold',
                    position: 'relative',
                    zIndex: 3,
                    top: '33%',
                  },
                  desktopStyle: {
                    width: '500px',
                    height: '500px',
                  },
                  components: ['2nd Animation'],
                },
              },
            ],
          },
        },
      },
    ],
  },
};
