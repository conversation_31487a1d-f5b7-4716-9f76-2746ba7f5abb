// @ts-nocheck
import React from 'react';
import { render, act } from 'test-utils';
import { DynamicMarketing as Marketing } from '../../../../json-marketing.client';
import { CSSAnimationProps } from '../types';
import { CSSAnimation } from '..';
import { mockJson } from '../__fixtures__/CSSAnimation-mockProps';
import { setAnimationFrame } from './setAnimationFrame';

const mockIntersectionObserver = () => {
  // eslint-disable-next-line @typescript-eslint/ban-ts-comment
  // @ts-ignore
  window.IntersectionObserver = jest.fn(() => ({
    observe: jest.fn().mockReturnValue(true),
    unobserve: jest.fn(),
  }));
};
mockIntersectionObserver();

const MockComponent = () => <div>Mock 1</div>;
const renderCSSAnimation = (componentJson: CSSAnimationProps) => {
  {
    (Marketing as jest.Mock).mockImplementation(MockComponent);
  }

  return render(<CSSAnimation {...componentJson} />);
};

describe('setAnimationFrame()', () => {
  it('calls requestAnimationFrame', () => {
    const mockSetState = jest.spyOn(React, 'useState');
    window.requestAnimationFrame = jest.fn().mockImplementation(callback => callback());

    const { getByText } = renderCSSAnimation(mockJson);
    const child = getByText('Mock 1');
    const childNodes = child.parentElement?.childNodes;
    const childNodesLength = childNodes?.length || 0;
    setAnimationFrame(childNodes!);
    expect(window.requestAnimationFrame).toHaveBeenCalledTimes(childNodesLength);
    expect(mockSetState).toHaveBeenCalledTimes(4);
  });
});
