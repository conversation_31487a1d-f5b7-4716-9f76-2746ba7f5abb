// @ts-nocheck
import React from 'react';
import { axe } from 'jest-axe';
import { fireEvent, render, act } from 'test-utils';
import { mockJson } from './__fixtures__/CSSAnimation-mockProps';
import { DynamicMarketing as Marketing } from '../../../json-marketing.client';
import { CSSAnimationProps, ControlsProps } from './types';
import { CSSAnimation } from '.';
import { getFullProps } from './helper/getFullProps';
import { parseTimeValue } from './helper/parseTimeValue';
import { setAnimationFrame } from './helper/setAnimationFrame';
import { PlayPauseButton } from './components/PlayPauseButton';
import { localTranslations } from './localTranslations';

jest.mock('./helper/setAnimationFrame');
const mockIntersectionObserver = () => {
  // eslint-disable-next-line @typescript-eslint/ban-ts-comment
  // @ts-ignore
  window.IntersectionObserver = jest.fn(() => ({
    observe: jest.fn().mockReturnValue(true),
    unobserve: jest.fn(),
  }));
};
mockIntersectionObserver();

const MockComponent = () => <div>Mock 1</div>;

const mockPlayPauseHandler = jest.fn();

const renderPlayPauseButton = () =>
  render(
    <PlayPauseButton
      buttonSources={{
        pauseBtnSrc: mockJson.controls?.pauseBtnSrc,
        playBtnSrc: mockJson.controls?.playBtnSrc,
      }}
      isInView
      isPlaying
      pauseAltText={mockJson.controls?.pauseAltText}
      playAltText={mockJson.controls?.playAltText}
      playPauseHandler={mockPlayPauseHandler}
    />
  );

const renderCSSAnimation = (componentJson: CSSAnimationProps) => {
  {
    (Marketing as jest.Mock).mockImplementation(MockComponent);
  }

  return render(<CSSAnimation {...componentJson} />);
};

describe('<CSSAnimation />', () => {
  describe('render', () => {
    it('should render child, button and all styles', () => {
      const { playBtnSrc, playAltText } = mockJson.controls as ControlsProps;
      const { getByText, getByRole } = renderCSSAnimation(mockJson);
      const child = getByText('Mock 1');
      const wrapper = child.parentElement;
      const buttonImg = getByRole('img');
      const button = getByRole('button');
      const buttonContainer = button.parentElement;
      expect(child).toBeTruthy();
      expect(wrapper).toHaveStyle(mockJson?.style || {});
      expect(button).toHaveAttribute('aria-label', playAltText);
      expect(buttonContainer).toHaveStyle(mockJson.controls?.style || {});
      expect(buttonImg).toHaveAttribute('alt', playAltText);
      expect(buttonImg).toHaveAttribute('src', playBtnSrc);
      expect(setAnimationFrame).not.toHaveBeenCalled();
    });
    it('renders with NO controls', () => {
      const mockJsonNoControls = {
        style: mockJson.style!,
        options: mockJson.options,
        animations: mockJson.animations,
      };
      const { queryByRole } = renderCSSAnimation(mockJsonNoControls);
      expect(queryByRole('button')).not.toBeInTheDocument();
    });
    it('renders with NO options', () => {
      const mockJsonNoControls = {
        style: mockJson.style!,
        animations: mockJson.animations,
      };
      const { queryByRole } = renderCSSAnimation(mockJsonNoControls);
      expect(queryByRole('button')).not.toBeInTheDocument();
    });
  });
  describe('PlayPauseButton', () => {
    it('play and pause buttons work', async () => {
      const { pauseBtnSrc, pauseAltText, playAltText, playBtnSrc } = mockJson.controls as ControlsProps;
      const { getByRole } = renderCSSAnimation(mockJson);

      await act(async () => {
        fireEvent.click(getByRole('button'));
      });

      expect(getByRole('button')).toHaveAttribute('aria-label', pauseAltText);
      expect(getByRole('img')).toHaveAttribute('alt', pauseAltText);
      expect(getByRole('img')).toHaveAttribute('src', pauseBtnSrc);

      await act(async () => {
        fireEvent.click(getByRole('button'));
      });

      expect(getByRole('button')).toHaveAttribute('aria-label', playAltText);
      expect(getByRole('img')).toHaveAttribute('alt', playAltText);
      expect(getByRole('img')).toHaveAttribute('src', playBtnSrc);
    });
    it('play and pause buttons use default localized text', async () => {
      const mockJsonNoAltText = {
        controls: {},
        animations: mockJson.animations,
      };
      const playAltText = localTranslations['en-CA'].translation['marketing.CSSAnimation.playAltText'];
      const pauseAltText = localTranslations['en-CA'].translation['marketing.CSSAnimation.pauseAltText'];

      const { getByRole } = renderCSSAnimation(mockJsonNoAltText);

      expect(getByRole('button')).toHaveAttribute('aria-label', playAltText);

      await act(async () => {
        fireEvent.click(getByRole('button'));
      });

      expect(getByRole('button')).toHaveAttribute('aria-label', pauseAltText);
    });
    it('click calls onClick handler', async () => {
      const { getByRole } = renderPlayPauseButton();
      const button = getByRole('button');
      await act(async () => {
        fireEvent.click(button);
      });
      expect(mockPlayPauseHandler).toHaveBeenCalled();
    });
    it('onBlur uses blur handler', () => {
      const { getByRole } = renderPlayPauseButton();
      const button = getByRole('button');
      fireEvent.blur(button);
      expect(button).not.toHaveFocus();
    });
  });
  describe('parseTimeValue()', () => {
    const timeWithSeconds = '20s';
    it('returns value in correct format', () => {
      expect(parseTimeValue('20ms')).toEqual('20ms');
      expect(parseTimeValue('20s')).toEqual(timeWithSeconds);
      expect(parseTimeValue(20)).toEqual(timeWithSeconds);
    });
  });
  describe('getFullProps()', () => {
    const mockComponent = mockJson.animations[0].component;

    it('returns default styles', () => {
      const fullProps = getFullProps(
        {
          keyframes: '0%{transform:translateY(0px);}100%{transform:translateY(-1%);}',
        },
        false,
        mockComponent
      );
      const styles = fullProps?.animationCss?.styles;
      expect(styles).toContain('animation-duration:1');
      expect(styles).toContain('animation-direction:normal');
      expect(styles).toContain('animation-delay:0s');
      expect(styles).toContain('animation-fill-mode:none');
      expect(styles).toContain('animation-timing-function:ease');
      expect(styles).toContain('animation-iteration-count:1');
      expect(styles).toContain('animation-play-state:paused');
    });
    it('play state is running', () => {
      const fullProps = getFullProps(
        {
          keyframes: '0%{transform:translateY(0px);}100%{transform:translateY(-1%);}',
        },
        true,
        mockComponent
      );
      const styles = fullProps?.animationCss?.styles;
      expect(styles).toContain('animation-play-state:running');
    });
    it('returns styles from config', () => {
      const mockAnimation = mockJson.animations[0].animation!;
      const fullProps = getFullProps(mockAnimation, false, mockComponent);
      const styles = fullProps?.animationCss?.styles;
      expect(styles).toContain(`animation-duration:${mockAnimation.duration}`);
      expect(styles).toContain(`animation-direction:${mockAnimation.direction}`);
      expect(styles).toContain(`animation-delay:${mockAnimation.delay}`);
      expect(styles).toContain(`animation-iteration-count:${mockAnimation.iterationCount}`);
      expect(styles).toContain(`animation-fill-mode:${mockAnimation.fillMode}`);
    });
    it('returns component props without style data', () => {
      const fullProps = getFullProps(undefined, false, mockComponent);
      expect(fullProps).toEqual(mockComponent);
    });
  });
  describe('a11y', () => {
    it('should not have a11y violations', async () => {
      const { getByRole, getByText } = renderCSSAnimation(mockJson);
      const controlButtons = getByRole('button');
      const child = getByText('Mock 1');
      const wrapper = child.parentElement;
      expect(await axe(controlButtons!)).toHaveNoViolations();
    });
  });
});
