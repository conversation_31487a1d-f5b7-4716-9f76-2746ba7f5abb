# CTALinksContainer

## Purpose

Currently used only in SVGOverlay as a subcomponent configurable through WCD JSON. Works as a container of CTA Links.

## How to use

Can be configured through JSON as a subcomponent or standalone component.

## Accepted properties

- _style_: container style section. _(Optional)_
- _verticalStacking_: boolean value for vertical stacking option. _(Defaults to false when unspecified)_
- _linkClasses_: css(stitch) classes that will be applied to links. _(Optional)_
- _content_: you put your link objects into this section. _(Required)_  
  Every link in _content_ section consists of:
  - _url_: link url
  - _tid_: tracking id
  - _text_: link text
  - _type_: type of the link. e.g. link (_'link'_), video(_'video'_), modal(_'modal'_). If unspecified will be set to _link_ by default.

## Example

```json
{
  "name": "CTALinksContainer",
  "type": "sitewide",
  "data": {
    "verticalStacking": false,
    "linkClasses": "sds_btn--underline",
    "style": {
      "desktop": {
        "top": "88%",
        "bottom": "auto",
        "left": "50%",
        "right": "auto",
        "transform": "translate(-50%,0)"
      }
    },
    "content": [
      {
        "tid": "HP_Prim_1_b",
        "url": "/browse/category.do?cid=1118115",
        "text": "Women"
      },
      {
        "tid": "HP_Prim_1_b",
        "url": "/browse/category.do?cid=1118111",
        "text": "Women's Plus"
      }
    ]
  }
}
```
