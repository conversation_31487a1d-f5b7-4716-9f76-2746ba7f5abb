// @ts-nocheck
import React from 'react';
import { LARGE, SMALL } from '@ecom-next/core/breakpoint-provider';
import { screen, render, act } from 'test-utils';
import CTALinksContainer from '.';
import { CTALinksContainerProps } from './types';

const originalProps: CTALinksContainerProps = {
  data: {
    content: [
      {
        tid: 'HP_Prim_1_b',
        url: '/browse/category.do?cid=1118115',
        text: 'Women',
      },
      {
        tid: 'HP_Prim_1_b',
        url: '/browse/category.do?cid=1118111',
        text: "Women's Plus",
      },
    ],
    style: {
      desktop: { color: 'red' },
      mobile: { color: 'blue' },
    },
  },
};

const getCtaLinkWithModal = (): HTMLElement[] | null => screen.getAllByRole('link');

const getCtaLinkForWomen = (): HTMLElement | null => screen.getByRole('link', { name: 'Women' });

const getCtaLinkForWomensPlus = (): HTMLElement | null => screen.getByRole('link', { name: "Women's Plus" });

const getCtaContainer = (): HTMLElement | null => screen.getByTestId('ctaContainer');

describe('<CTALinksContainer/>', () => {
  let props: CTALinksContainerProps;
  beforeEach(() => {
    props = { ...originalProps };
  });

  test('should have cta-container class', () => {
    render(<CTALinksContainer {...props} />);
    expect(getCtaContainer()).toHaveStyleRules({
      display: 'flex',
      padding: '1rem 0.5rem',
      'justify-content': 'space-around',
    });
  });

  test('should have marginBottom 0 for vertical stacking true', () => {
    props.data.verticalStacking = true;

    render(<CTALinksContainer {...props} />);

    const ctaLinkWithModal = getCtaLinkWithModal();

    expect(ctaLinkWithModal?.[0]).toHaveStyleRules(
      { 'margin-bottom': '0' },
      {
        media: '(max-width: 767px)',
      }
    );

    expect(ctaLinkWithModal?.[1]).toHaveStyleRules(
      { 'margin-bottom': '0' },
      {
        media: '(max-width: 767px)',
      }
    );
  });

  test('should render all of link components defined in data.content', () => {
    render(<CTALinksContainer {...props} />);

    const ctaLinkWithModal = getCtaLinkWithModal();

    expect(ctaLinkWithModal).toHaveLength(2);
  });

  test('should add custom class to link if was passed', () => {
    props.data.linkClasses = 'someCustomClass';

    render(<CTALinksContainer {...props} />);

    const ctaLinkWithModal = getCtaLinkWithModal();

    expect(ctaLinkWithModal?.[0]).toHaveClass('someCustomClass');
    expect(ctaLinkWithModal?.[1]).toHaveClass('someCustomClass');
  });

  test('should render links with correct urls', () => {
    render(<CTALinksContainer {...props} />);

    expect(getCtaLinkForWomen()).toHaveAttribute('href', '/browse/category.do?cid=1118115');

    expect(getCtaLinkForWomensPlus()).toHaveAttribute('href', '/browse/category.do?cid=1118111');
  });

  describe('CTALinksContainerWithBreakpointProvider', () => {
    test('should pass isDesktop=true style when it is large breakpoint', () => {
      render(<CTALinksContainer {...props} />, {
        breakpoint: LARGE,
      });

      expect(getCtaContainer()).toHaveStyleRules({
        color: 'red',
      });
    });

    test('should pass isDesktop=false style when it is small breakpoint', () => {
      render(<CTALinksContainer {...props} />, {
        breakpoint: SMALL,
      });

      expect(getCtaContainer()).toHaveStyleRules({
        color: 'blue',
      });
    });
  });
});
