// @ts-nocheck
'use client';
import React, { useContext } from 'react';
import { BreakpointContext, LARGE } from '@ecom-next/core/breakpoint-provider';
import classnames from 'classnames';
import { CSSObject } from '@emotion/react';
import LinkWithModal from '../LinkWithModal';
import { CTALinksContainerProps } from './types';

export const CTALinksContainer = (props: CTALinksContainerProps) => {
  const { minWidth } = useContext(BreakpointContext);
  const { content, style, verticalStacking, linkClasses } = props.data;

  const GetCTALinksContainerStyle = {
    ...(minWidth(LARGE) ? style?.desktop : style?.mobile),
  } as CSSObject;

  const GetVerticalStackingContainerStyle =
    verticalStacking &&
    ({
      display: 'flex',
      flexDirection: 'column',
      alignItems: 'flex-start',

      '@media (min-width: 768px)': {
        width: 'auto',
        position: 'absolute',
        margin: 0,
      },

      '@media (max-width: 767px)': {
        flexDirection: 'row',
      },
    } as CSSObject);

  const ctaContainerStyle: CSSObject = {
    display: 'flex',
    padding: '1rem 0.5rem',
    justifyContent: 'space-around',

    '@media (min-width: 768px)': {
      width: 'auto',
      position: 'absolute',
      margin: 0,
      whiteSpace: 'nowrap',
    },
    ...GetCTALinksContainerStyle,
    ...GetVerticalStackingContainerStyle,
  };

  const GetVerticalStackingLinkStyle =
    verticalStacking &&
    ({
      '@media (max-width: 767px)': {
        marginBottom: 0,
      },

      '@media (min-width: 768px)': {
        width: 'auto',
        marginLeft: 0,
        marginBottom: '1rem',

        '&:last-child': {
          marginLeft: 0,
          marginBottom: 0,
        },
      },
    } as CSSObject);

  const ctaContainerLinkStyle: CSSObject = {
    width: '18%',
    textAlign: 'left',
    margin: 0,
    display: 'inline-block',
    ...style,
    ...GetVerticalStackingLinkStyle,
  } as CSSObject;

  return (
    <div css={ctaContainerStyle} data-testid='ctaContainer'>
      {content.map(link => (
        <LinkWithModal
          key={link.url || link.href}
          className={classnames('sds_btn', linkClasses)}
          css={ctaContainerLinkStyle}
          tid={link.tid}
          to={link.url || link.href}
          type={link.type}
        >
          <span>{link.text}</span>
        </LinkWithModal>
      ))}
    </div>
  );
};

export default CTALinksContainer;
