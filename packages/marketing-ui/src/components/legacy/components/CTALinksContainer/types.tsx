// @ts-nocheck
'use client';
import { CSSObject } from '@ecom-next/core/react-stitch';

export interface LinkContent {
  href?: string;
  text: string;
  tid?: string;
  type?: string;
  url: string;
}

export interface CTALinksContainerProps {
  data: {
    content: LinkContent[];
    linkClasses?: string;
    style?: {
      desktop?: CSSObject;
      mobile?: CSSObject;
    };
    verticalStacking?: boolean;
  };
}
