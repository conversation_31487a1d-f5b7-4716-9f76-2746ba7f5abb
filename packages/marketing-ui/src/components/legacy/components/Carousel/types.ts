// @ts-nocheck
'use client';
import { MouseEventHandler } from 'react';
import { CSSObject } from '@ecom-next/core/react-stitch';

export type CarouselAccessibilityOptions = {
  initialSlide?: number;
  currentSlide?: number;
  slideCount?: number;
  disabled?: boolean;
};
export type CarouselOptions = {
  appendDots: (dots: JSX.Element) => JSX.Element;
  arrowPosition?: string;
  autoplay: boolean;
  displayArrows?: {
    mobile: boolean;
    desktop: boolean;
  };
  slidesToScroll?: number;
  slidesToShow?: number;
  mobileSmoothScroll?: boolean;
  displayPlayPauseBtn?: boolean;
  infinite?: boolean;
  prevArrowUrl?: string;
  nextArrowUrl?: string;
  disabled?: CarouselAccessibilityOptions['disabled'];
};

export type TileProps = {
  name: string;
  type: string;
  instanceName: string;
  data: Record<string, unknown>;
  hideOnBreakpoint?: 'mobile' | 'desktop';
};

export type CarouselProps = {
  onNextClick?: () => void;
  onPreviousClick?: () => void;
  buttonSetting: ButtonSetting;
  carouselContainerStyle: CSSObject;
  carouselOptions: CarouselOptions;
  carouselStyle?: CSSObject;
  components: Array<TileProps>;
  noHeaderModal: boolean;
  modalCloseButtonAriaLabel: string;
  id: string;
  style?: CSSObject;
  initialSlide?: number;
};

export type ButtonSetting = {
  buttonStyle?: CSSObject;
  buttonImagePath?: { pauseBtnSrc?: string; playBtnSrc?: string };
  playAltText: string;
  pauseAltText: string;
  prevArrowAlt: string;
  nextArrowAlt: string;
};

export type CarouselArrows = Pick<ButtonSetting, 'prevArrowAlt' | 'nextArrowAlt'> & {
  prevArrowUrl?: string;
  nextArrowUrl?: string;
  pauseCarousel: () => void;
};

export type ButtonProps = {
  onPreviousClick?: () => void;
  onNextClick?: () => void;
  alt: string;
  DefaultButton: React.ComponentType<DefaultButtonProps>;
  src?: string;
  onClick?: MouseEventHandler;
  infinite?: boolean;
  className?: string;
  disabled?: CarouselAccessibilityOptions['disabled'];
};

export type MediaControlButtonProps = Pick<ButtonProps, 'alt' | 'src' | 'onClick'>;

export type DefaultButtonProps = Pick<ButtonProps, 'alt' | 'className' | 'disabled' | 'onClick'>;

export type CarouselButtonProps = Omit<ButtonProps, 'onClick'> & {
  pauseCarousel: () => void;
  onClick?: MouseEventHandler;
};

export type CarouselActionProps = Omit<ButtonProps, 'DefaultButton'> & {
  pauseCarousel: () => void;
};
