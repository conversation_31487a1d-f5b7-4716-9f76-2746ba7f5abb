// @ts-nocheck
import React from 'react';
import { fireEvent, render, RenderOptions, RenderResult, screen, act } from 'test-utils';
import { Brands as Brand } from '@ecom-next/core/legacy/utility';
import { Clienteling } from './Clienteling';
import { ClientelingJson } from './types';

const defaultProps = {
  src: 'localhost',
  buttonText: 'Clienteling',
  iframeTitle: 'Curbside Booking',
  height: '1000px',
};

describe('Clienteling', () => {
  const renderClienteling = (props: ClientelingJson = defaultProps): RenderResult =>
    render(<Clienteling {...props} />, {
      appState: { brandName: Brand.Athleta } as RenderOptions,
    });

  const button = (): HTMLElement => screen.getByRole('button', { name: /clienteling/i });

  const link = (): HTMLElement | null => screen.getByText(/clienteling/i).closest('a');

  const modal = (): HTMLElement => screen.getByRole('dialog');

  const iframe = (iframeTitle = 'Curbside Booking'): HTMLElement => screen.getByTitle(iframeTitle);

  it('renders a button we can click', () => {
    renderClienteling();
    expect(button()).toBeVisible();
  });

  it('opens a modal when we click the button', async () => {
    renderClienteling();

    await act(async () => {
      fireEvent.click(button());
    });

    expect(modal()).toBeVisible();
    expect(iframe()).toBeVisible();
  });

  it("propagates the configuration's src to the modal's iframe", async () => {
    renderClienteling({ ...defaultProps, src: 'somewhere' });

    await act(async () => {
      fireEvent.click(button());
    });

    expect(iframe()).toHaveAttribute('src', 'somewhere');
  });

  it("propagates the configuration's title to the modal's iframe title", async () => {
    renderClienteling({ ...defaultProps, iframeTitle: 'Hello World!' });

    await act(async () => {
      fireEvent.click(button());
    });

    expect(iframe('Hello World!')).toBeInTheDocument();
  });

  it("propagates the configuration's height to the modal's iframe height", async () => {
    renderClienteling({ ...defaultProps, height: '500px' });

    await act(async () => {
      fireEvent.click(button());
    });

    expect(iframe()).toHaveStyleRule('height', '500px');
  });

  it('renders a link given the trigger configuration is set to link', () => {
    renderClienteling({ ...defaultProps, trigger: 'link' });

    expect(link()).toBeInTheDocument();
  });

  it('renders a button given the trigger configuration is set to button', () => {
    renderClienteling({ ...defaultProps, trigger: 'button' });

    expect(button()).toBeInTheDocument();
  });
});
