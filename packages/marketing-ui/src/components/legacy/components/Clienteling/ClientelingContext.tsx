// @ts-nocheck
'use client';
import React, { createContext, useContext, useRef } from 'react';

type DoOpenCallback = () => void;

interface ClientelingContextType {
  doOpen: DoOpenCallback;
  setDoOpenCallback: (callback: DoOpenCallback) => void;
}

interface ClientelingProviderProps {
  children: JSX.Element;
}

const ClientelingContext = createContext<ClientelingContextType>({
  doOpen: () => {},
  setDoOpenCallback: () => {},
});

export const useClienteling = (): ClientelingContextType => useContext(ClientelingContext);

export const ClientelingProvider = ({ children }: ClientelingProviderProps): JSX.Element | null => {
  const doOpenRef = useRef<DoOpenCallback>(() => {});

  const doOpen = (): void => {
    doOpenRef?.current();
  };

  const setDoOpenCallback = (callback: DoOpenCallback): void => {
    doOpenRef.current = callback;
  };

  const providerValues: ClientelingContextType = {
    doOpen,
    setDoOpenCallback,
  };

  return <ClientelingContext.Provider value={providerValues}>{children}</ClientelingContext.Provider>;
};
