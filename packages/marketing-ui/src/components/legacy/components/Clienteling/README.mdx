# Clienteling

This is a component that can be used inside placements or other marketing components. Using the `Clienteling` configuration displays a button to open a modal to a specific iframe `src` location.

# JSON Configuration

This is a simple example of the JSON configuration inside the "preheader" placement:

Preheader example:

```json
{
  "preheader":
    {
      "name": "Clienteling",
      "type": "sitewide",
      "data": {
        "buttonProps": {},
        "buttonText": "Clienteling",
        "height": "1000px",
        "iframeTitle": "Curbside Booking",
        "src": "https://somewhere.com",
        "trigger": "button" | "link"
      }
    }
}
```

Example usage in a banner using a layout component

```json
    "desktopemergencybanner": {
      "name": "LayoutComponent",
      "type": "sitewide",
      "data": {
        "desktop": {
          "shouldDisplay": true,
          "data": {
            "style": {},
            "components": [
              {
                "name": "LayoutComponent",
                "type": "sitewide",
                "tileStyle": {
                  "desktop": {
                    "width": "100%"
                  }
                },
                "data": {
                  "desktop": {
                    "shouldDisplay": true,
                    "data": {
                      "style": {
                        "maxWidth": "1400px"
                      },
                      "components": [
                        {
                          "type": "builtin",
                          "name": "div",
                          "data": {
                            "props": {
                              "style": {}
                            },
                            "components": [
                              {
                                "type": "builtin",
                                "name": "div",
                                "data": {
                                  "props": {
                                    "style": {}
                                  },
                                  "components": [
                                    "Clienteling example"
                                  ]
                                }
                              }
                            ]
                          }
                        }
                      ]
                    }
                  }
                }
              },
              {
                "name": "Clienteling",
                "type": "sitewide",
                "data": {
                  "buttonText": "Clienteling Button",
                  "src": "https://somewhere.com",
                  "height": "1000px",
                  "iframeTitle": "Curbside Booking",
                  "buttonProps": {
                    "style": {
                      "maxWidth": "200px",
                      "backgroundColor": "#FFFFFF",
                      "fontSize": "12px",
                      "color": "#000000"
                    }
                  }
                }
              }
            ]
          }
        }
      }
    }
```

- `buttonProps` - accepts all the props from [Fixed Button Props](https://github.gapinc.com/ecomfrontend/core-ui/blob/main/packages/fixed-button/types.ts) to configure and style the `button`. It is optional.
- `buttonText` - sets the text for the `button`. Required value.
- `height` - allows the modal to be configured with a specific height when opened. It is not required, as it has a default value of 700px.
- `iframeTitle` - sets the title for the `iframe`. Required value.
- `src` - provides the source location for the iframe embedded in the modal. Required value.
- `trigger` - provides the ability to use a `link` element instead of a `button`. It is optional as it defauls to `button`.
