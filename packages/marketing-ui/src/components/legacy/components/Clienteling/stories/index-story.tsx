// @ts-nocheck
'use client';
import type { Meta, StoryObj } from '@storybook/react';
import { Clienteling } from '../Clienteling';
import README from '../README.mdx';

const meta: Meta<typeof Clienteling> = {
  title: 'Common/JSON Components (Marketing)/Clienteling',
  component: Clienteling,
  parameters: {
    docs: {
      page: README,
    },
    eyes: { include: false },
  },
  tags: ['exclude'],
};

export default meta;

type Story = StoryObj<typeof Clienteling>;

export const ClientelingWithButton: Story = {
  args: {
    src: 'https://proximity-athletaclienteling.cs94.force.com/Booking/?pathName=Booking',
    iframeTitle: 'Clienteling Storybook Demo',
    buttonText: 'Clienteling Button',
    height: '1000px',
  },
};

export const ClientelingWithLink: Story = {
  args: {
    src: 'https://proximity-athletaclienteling.cs94.force.com/Booking/?pathName=Booking',
    iframeTitle: 'Clienteling Storybook Demo',
    buttonText: 'Clienteling Link',
    trigger: 'link',
    buttonProps: {
      children: null,
      style: {
        background: 'transparent',
        color: 'navy',
        border: 'none',
        width: '200px',
      },
    },
  },
};
