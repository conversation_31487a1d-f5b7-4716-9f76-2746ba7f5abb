// @ts-nocheck
'use client';
import type { Meta, StoryObj } from '@storybook/react';
import { userEvent, within, waitFor, expect, screen } from '@storybook/test';
import { Clienteling } from '../Clienteling';
import README from '../README.mdx';

const meta: Meta<typeof Clienteling> = {
  title: 'Common/JSON Components (Marketing)/Clienteling/Interaction Tests',
  component: Clienteling,
  parameters: {
    docs: {
      page: README,
    },
    eyes: { include: false },
  },
  tags: ['exclude', 'flaky-test'],
};

export default meta;

type Story = StoryObj<typeof Clienteling>;

export const ClientelingWithButtonInteractionTest: Story = {
  args: {
    src: '/images/product-photos/gap/jeans/light-indigo/av3/thumbnail.jpg',
    iframeTitle: 'Clienteling Storybook Demo',
    buttonText: 'Clienteling Button',
    height: '1000px',
  },
  play: async ({ canvasElement, step }) => {
    const canvas = within(canvasElement);

    await step('Click the button', async () => {
      const button = await canvas.getByRole('button', {
        name: /Clienteling Button/i,
      });
      await userEvent.click(button);
    });

    await waitFor(() =>
      expect(within(screen.getByRole('dialog')).getByTitle('Clienteling Storybook Demo')).toHaveAttribute(
        'src',
        '/images/product-photos/gap/jeans/light-indigo/av3/thumbnail.jpg'
      )
    );
  },
};

export const ClientelingWithLinkInteractionTest: Story = {
  args: {
    src: '/images/product-photos/gap/jeans/light-indigo/av3/thumbnail.jpg',
    iframeTitle: 'Clienteling Storybook Demo',
    buttonText: 'Clienteling Link',
    trigger: 'link',
    buttonProps: {
      children: null,
      style: {
        background: 'transparent',
        color: 'navy',
        border: 'none',
        width: '200px',
      },
    },
  },
  play: async ({ canvasElement, step }) => {
    const canvas = within(canvasElement);

    await step('Click the link', async () => {
      const link = await canvas.getByText('Clienteling Link');
      await userEvent.click(link);
    });

    await waitFor(() =>
      expect(within(screen.getByRole('dialog')).getByTitle('Clienteling Storybook Demo')).toHaveAttribute(
        'src',
        '/images/product-photos/gap/jeans/light-indigo/av3/thumbnail.jpg'
      )
    );
  },
};
