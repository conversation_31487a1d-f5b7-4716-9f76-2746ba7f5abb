// @ts-nocheck
'use client';
import React, { useContext, Fragment } from 'react';
import { BreakpointContext, LARGE } from '@ecom-next/core/breakpoint-provider';
import { Modal } from '@ecom-next/core/legacy/modal';
import { styled } from '@ecom-next/core/react-stitch';
import { IframeModalProps } from './types';

export function IframeModal({ title, src, height = '600px', width = '100%', ...modalProps }: IframeModalProps): JSX.Element {
  const { minWidth } = useContext(BreakpointContext);
  const isDesktop = minWidth(LARGE);
  const IframeContainer = isDesktop ? Fragment : styled.div({ padding: '1rem' });
  const iframeHeight = isDesktop ? height : 'calc(100vh - 6.5rem)';
  const iframeWidth = isDesktop ? width : '100%';
  const iframeStyles = {
    border: 0,
    width: iframeWidth,
    height: iframeHeight,
  };
  return (
    <Modal title={title} {...modalProps}>
      <IframeContainer>
        <iframe css={iframeStyles} data-testid='iframeModal' src={src} title={title} />
      </IframeContainer>
    </Modal>
  );
}
