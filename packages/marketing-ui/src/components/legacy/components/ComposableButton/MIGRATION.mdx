# Migration

- [8.1.5](#815)
- [8.0.0 to 8.1.15](#800-to-815)
- [7.7.x to 8.0.0](#77x-to-800)
- [7.3.x to 7.4.0](#73x-to-740)
- [6.x.x to 7.0.0](#6xx-to-700)
- [5.x.x to 6.0.0](#5xx-to-600)
- [3.x.x to 4.0.0](#3xx-to-400)
- [1.5.2 to 2.0.0](#152-to-200)
- [0.3.14 to 1.0.0](#0314-to-100)

## 8.1.5

**Summary**

Migrated from Core-UI

## 8.0.0 to 8.1.5

**Summary**

Undocumented

## 7.7.x to 8.0.0

**Summary**

Athleta styles have been updated in `@core-ui/composable-button`. Styles have been changed to align with requested restyles.

Athleta composable-button text-transform now defaults to having no value, while other brands remain with a default of `uppercase`.

## 7.3.x to 7.4.0

**Summary**

Gap & GapFactory styles have been updated in `@core-ui/composable-button`. This was introduced to align styling with a restyling initiative from the `@core-ui/marketing-ui` project.

If button size is not specified, Gap & Gapfs buttons will default to the "small" variation with 16px text.
Otherwise, usage does not change, but be aware that Gap / Gapfs styles have changed quite a bit.

Underline interactive styles have been fixed for AT & ON.

At this point, Gap, Gapfs, AT, ON styles have been refactored. crossBrand, BR, & Brfs are still using legacy files that we can hopefully refactor in the future.

## 6.X.X to 7.0.0

**Summary**

`@core-ui/composable-button` has been refactored to use brand-level configurations to allow for easier (type-safe) management. This was introduced to align styling with a restyling initiative from the `@core-ui/marketing-ui` project.

**Breaking Change**

Usage does not change, this is classified as a breaking change because of the amount of styling changes that are being implemented (and aligned).

## 5.x.x to 6.0.0

**Summary**

`@core-ui/composable-button` is now being transpiled with rollupJS. Also, in following with norms, default exports have been removed.

Also, `@core-ui/reacts-stitch` is now a `peerDependency`.

**Steps to Resolve Breaking Changes**

- import `@core-ui/composable-button` by the named expor

  t

- make sure that your project is installing a copy of `@core-ui/react-stitch`

**Code Snippet**
Before (5.x.x):

```jsx
import ComposableButton from '@ecom-next/core/legacy/composable-button';
```

Current (6.0.0):

```jsx
import { ComposableButton } from '@ecom-next/core/legacy/composable-button';
```

## 3.x.x to 4.0.0

THIS WAS AN ACCIDENTAL BREAKING CHANGE VERSION BUMP!
`@core-ui/composable-button` should be usable in the same way as the previous breaking change version (eg v3.x.x).

## 1.5.2 to 2.0.0

Removes SCSS
Uses `React-Stitch` to style component
Needs to be wrapped in `StitchStyleProvider` in order to properly style component
Added new `roundedCorners` prop

## 0.3.14 to 1.0.0

Implements forwardRef. displayName has changed from "ComposableButton" to "ForwardRef(ComposableButton)"
