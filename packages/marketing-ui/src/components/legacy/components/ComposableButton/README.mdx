# MUI Composable Button

- What is the component?
  - The MUI `ComposableButton` is the MUI version of the Core-UI `ComposableButton`. It has the same styles as the Core-UI `ComposableButton`; however, it has more functional variants, such as configurations for link navigation and modal functionality.

## Technical Notes

### Note about style overrides

A lot of refactoring and re-styling happened to core-ui/composable-button in Fall 2021, and MUI `ComposableButton` inherited many of these styles. Some of the most common styles that have caused problems were new button padding, and new min-heights (ex: Gap button min-height is 44px for accessibility). If existing JSON styling has been broken, consider restoring with explicit padding styles or min-height:0 to `ComposableButton` files.

### Old Navy 2024 CTA Redesign

As of November 2024, Old Navy decided to restyle their `CTAButton` and `CTADropdown` subcomponents. The `CTAButton`
restyle required updates to the `ComposableButton` on.config.ts file. The new 2024 cta redesign styles for those components will only be available if the
"on-cta-redesign-2024" feature flag is turned on _and_ the opt-in `OnCtaRedesign2024Context` contains `{enabled: true}`.

### JSON Structure Explained

This section will go into detail about each of the configurable keys present within the `ComposableButton`

`name`: `"ComposableButton"` - the name of the MUI component

`data`: object - props for `ComposableButton`

- `children`: string - the text of the button
- `borderThickness`: one of "thin", "medium", "thick" - specifies a border width
- `bright`: boolean - if `true`, uses the primary Bright font, overriding the 'font' and the crossBrand font
- `capitalization`: one of "lowercase", "uppercase", "capitalize" - sets the `text-transform` CSS property
- `className`: string - a class name applied to the button
- `crossBrand`: boolean - if `true`, uses crossbrand styling, including fonts and colors
- `color`: one of "primary", "black", "white" - Sets the composable button color based on the Stitch color definitions in Composable Button
- `font`: one of "primary", "secondary", "tertiary" - Sets the composable button font. Crossbrand and Bright fonts are used when the respective props are set to `true`
- `fullWidth`: boolean - If true, button will be full width of the container
- `roundedCorners`: boolean - Determines if the button has rounded corners
- `size`: one of "small", "medium", "large", "xl" - Set the size based on the Stitch size definitions in Composable Button
- `variant`: one of "solid", "outline", "border", "underline", "flat" - Set the variant of the button based on the Stitch definitions in Composable Button
- `desktopProps`: object - optional; can be used to change a button's style at the desktop breakpoint size. Accepts all props that listed above. NOTE: This does NOT change the functionality of the button (ie this does NOT take `linkProps` nor `modalProps` which are listed below).
- `style`: CSSObject - CSS applied to both mobile and desktop breakpoints
- `desktopStyle`: CSSObject - CSS applied solely on desktop breakpoints
- `linkProps`: object - if present, renders with `ComposableButton` styles but as an `a` tag. NOTE: this should NOT be used with `modalProps`.
  - Allows all attributes available on the `a` tag (ie `href`, `target`, etc...)
- `modalProps`: object - if present, pressing the `ComposableButton` will open a modal containing an `iframe`. NOTE: this should NOT be used with `linkProps`.
  - `className`: string - a custom class name
  - `crossBrand`: boolean - if `true` render with crossBrand styles. Note that crossBrand will not carry over to modal content.
  - `closeButtonAriaLabel`: string - text used for aria-label for close modal button
  - `hasRoundedCorners`: boolean - If `true`, rounds corners of the modal.
  - `mobileFitSizeToContent`: boolean - For mobile, modal sizes to the content instead of sizing itself to the whole screen.
  - `modalSize`: one of "mini", "standard", "max" - The different modal sizes on large breakpoints.
  - `negatePadding`: boolean
  - `noHeader`: boolean - Can be set to true to remove the header banner at the top of the modal. The close modal button will remain.
  - `title`: string - If `noHeader` is false, then it is used as the visible title of the `Modal` as well as read by screen readers when the `Modal` first opens.
  - `width`: string - the `width` attribute of the iframe
  - `height`: string - the `height` attribute of the iframe
  - `src`: string - the `src` attribute for the iframe; a URL

### JSON Examples

The JSON below creates the experience found on [Storybook](https://core-ui-main.apps.cfplatform.dev.azeus.gaptech.com/?path=/story/common-json-components-marketing-composablebutton--default)

#### Default

```json
{
  "name": "ComposableButton",
  "data": {
    "borderThickness": "medium",
    "bright": false,
    "capitalization": "uppercase",
    "className": "",
    "color": "primary",
    "crossBrand": false,
    "font": "secondary",
    "fullWidth": false,
    "size": "large",
    "variant": "solid",
    "buttonText": "Join Now!"
  }
}
```

#### Link

```json
{
  "name": "ComposableButton",
  "data": {
    "linkProps": {
      "href": "https://www.gap.com",
      "target": "_blank"
    },
    "borderThickness": "medium",
    "bright": false,
    "capitalization": "uppercase",
    "className": "",
    "color": "primary",
    "crossBrand": false,
    "font": "secondary",
    "fullWidth": false,
    "size": "large",
    "variant": "solid",
    "buttonText": "Go to gap homepage"
  }
}
```

#### JumpLink

```json
{
  "name": "ComposableButton",
  "data": {
    "linkProps": {
      "isAJumplink": true,
      "jumplinkCSSSelector": ".jump-to-me"
    },
    "children": "Go to jump link"
  }
}
```

#### Modal Iframe Opener

```json
{
  "name": "ComposableButton",
  "data": {
    "modalProps": {
      "src": "https://www.gap.com/Asset_Archive/GPWeb/content/static-marketing/xbrand-edfs-content/edfsLegal-GP-FS25.html?v=0",
      "height": "700px",
      "width": "100%",
      "closeButtonAriaLabel": "close the edfs modal",
      "title": "every day free shipping"
    },
    "borderThickness": "medium",
    "bright": false,
    "capitalization": "uppercase",
    "className": "",
    "color": "primary",
    "crossBrand": false,
    "font": "secondary",
    "fullWidth": false,
    "size": "large",
    "variant": "solid",
    "buttonText": "Open Edfs Modal"
  }
}
```
