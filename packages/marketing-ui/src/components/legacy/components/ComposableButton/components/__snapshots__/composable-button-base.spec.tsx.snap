// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`<ComposableButtonBase /> Snapshots should match snapshots for at 1`] = `
@keyframes animation-0 {
  0%, 80%, 100% {
    -webkit-transform: scale(0);
    -moz-transform: scale(0);
    -ms-transform: scale(0);
    transform: scale(0);
  }

  40% {
    -webkit-transform: scale(1);
    -moz-transform: scale(1);
    -ms-transform: scale(1);
    transform: scale(1);
  }
}

@keyframes animation-1 {
  0% {
    -webkit-transform: scale(1.0);
    -moz-transform: scale(1.0);
    -ms-transform: scale(1.0);
    transform: scale(1.0);
  }

  10% {
    -webkit-transform: scale(1.5);
    -moz-transform: scale(1.5);
    -ms-transform: scale(1.5);
    transform: scale(1.5);
  }

  30% {
    -webkit-transform: scale(1.0);
    -moz-transform: scale(1.0);
    -ms-transform: scale(1.0);
    transform: scale(1.0);
  }

  40% {
    -webkit-transform: translate(0.9375rem, 0);
    -moz-transform: translate(0.9375rem, 0);
    -ms-transform: translate(0.9375rem, 0);
    transform: translate(0.9375rem, 0);
  }

  50% {
    -webkit-transform: translate(0, 0);
    -moz-transform: translate(0, 0);
    -ms-transform: translate(0, 0);
    transform: translate(0, 0);
  }
}

@keyframes animation-2 {
  0% {
    opacity: 0;
    width: 0;
  }

  35% {
    width: 0;
  }

  50% {
    width: 100%;
  }

  60% {
    opacity: 0;
  }

  100% {
    opacity: 1;
  }
}

.emotion-0 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: none;
  transition: none;
  box-sizing: border-box;
  border: 1.5px solid transparent;
  border-radius: 0;
  font-size: 16px;
  font-weight: 500;
  letter-spacing: 0px;
  min-height: auto;
  max-height: 60px;
  line-height: 1.25;
  padding: 22px 40px;
  width: auto;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #FFFFFF;
  background-color: #000000;
  border-color: #000000;
}

.emotion-0:focus {
  outline: none;
}

.emotion-0>span {
  padding: 1px 0;
}

.emotion-2 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: none;
  transition: none;
  box-sizing: border-box;
  border: 1.5px solid transparent;
  border-radius: 0;
  font-size: 16px;
  font-weight: 500;
  letter-spacing: 0px;
  min-height: auto;
  max-height: 60px;
  line-height: 1.25;
  padding: 22px 40px;
  width: auto;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #FFFFFF;
  background-color: #000000;
  border-color: #000000;
}

.emotion-2:focus {
  outline: none;
}

.emotion-2>span {
  padding: 1px 0;
}

.emotion-2:hover,
.emotion-2:focus {
  text-shadow: 0 0 2px currentColor;
}

.emotion-2:active {
  color: #000000;
  background-color: #FFFFFF;
  border-color: #FFFFFF;
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-4 {
  width: 100%;
  text-align: center;
}

.emotion-4>div {
  width: 1rem;
  height: 1rem;
  background-color: currentColor;
  margin: 0.1rem;
  border-radius: 50%;
  display: inline-block;
  -webkit-animation: animation-0 1.4s infinite ease-in-out both;
  animation: animation-0 1.4s infinite ease-in-out both;
}

.emotion-4 :nth-of-type(1) {
  -webkit-animation-delay: -0.32s;
  animation-delay: -0.32s;
}

.emotion-4 :nth-of-type(2) {
  -webkit-animation-delay: -0.16s;
  animation-delay: -0.16s;
}

.emotion-10 {
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-flex-flow: row;
  -webkit-flex-flow: row;
  -ms-flex-flow: row;
  flex-flow: row;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
}

.emotion-11 {
  -webkit-animation: animation-1 2s;
  animation: animation-1 2s;
  -webkit-animation-iteration-count: 1;
  animation-iteration-count: 1;
  -webkit-animation-delay: 0;
  animation-delay: 0;
  height: 1.25rem;
  width: 1.25rem;
  -webkit-align-self: center;
  -ms-flex-item-align: center;
  align-self: center;
}

.emotion-12 {
  fill: currentColor;
  display: block;
}

.emotion-13 {
  -webkit-animation: animation-2 2s;
  animation: animation-2 2s;
  -webkit-animation-iteration-count: 1;
  animation-iteration-count: 1;
  -webkit-animation-delay: 0;
  animation-delay: 0;
  -webkit-align-self: center;
  -ms-flex-item-align: center;
  align-self: center;
  padding-left: 0.625rem;
}

.emotion-39 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: none;
  transition: none;
  box-sizing: border-box;
  border: 1.5px solid transparent;
  border-radius: 0;
  font-size: 16px;
  font-weight: 500;
  letter-spacing: 0px;
  min-height: auto;
  max-height: 60px;
  line-height: 1.25;
  padding: 22px 40px;
  width: 100%;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #FFFFFF;
  background-color: #000000;
  border-color: #000000;
}

.emotion-39:focus {
  outline: none;
}

.emotion-39>span {
  padding: 1px 0;
}

.emotion-41 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: none;
  transition: none;
  box-sizing: border-box;
  border: 1.5px solid transparent;
  border-radius: 0;
  font-size: 16px;
  font-weight: 500;
  letter-spacing: 0px;
  min-height: auto;
  max-height: 60px;
  line-height: 1.25;
  padding: 22px 40px;
  width: 100%;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #FFFFFF;
  background-color: #000000;
  border-color: #000000;
}

.emotion-41:focus {
  outline: none;
}

.emotion-41>span {
  padding: 1px 0;
}

.emotion-41:hover,
.emotion-41:focus {
  text-shadow: 0 0 2px currentColor;
}

.emotion-41:active {
  color: #000000;
  background-color: #FFFFFF;
  border-color: #FFFFFF;
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-42 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: none;
  transition: none;
  box-sizing: border-box;
  border: 1.5px solid transparent;
  border-radius: 4px;
  font-size: 16px;
  font-weight: 500;
  letter-spacing: 0px;
  min-height: auto;
  max-height: 60px;
  line-height: 1.25;
  padding: 22px 40px;
  width: auto;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #FFFFFF;
  background-color: #000000;
  border-color: #000000;
}

.emotion-42:focus {
  outline: none;
}

.emotion-42>span {
  padding: 1px 0;
}

.emotion-44 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: none;
  transition: none;
  box-sizing: border-box;
  border: 1.5px solid transparent;
  border-radius: 4px;
  font-size: 16px;
  font-weight: 500;
  letter-spacing: 0px;
  min-height: auto;
  max-height: 60px;
  line-height: 1.25;
  padding: 22px 40px;
  width: auto;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #FFFFFF;
  background-color: #000000;
  border-color: #000000;
}

.emotion-44:focus {
  outline: none;
}

.emotion-44>span {
  padding: 1px 0;
}

.emotion-44:hover,
.emotion-44:focus {
  text-shadow: 0 0 2px currentColor;
}

.emotion-44:active {
  color: #000000;
  background-color: #FFFFFF;
  border-color: #FFFFFF;
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-45 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: none;
  transition: none;
  box-sizing: border-box;
  border: 1.5px solid transparent;
  border-radius: 99em;
  font-size: 16px;
  font-weight: 500;
  letter-spacing: 0px;
  min-height: auto;
  max-height: 60px;
  line-height: 1.25;
  padding: 22px 40px;
  width: auto;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #FFFFFF;
  background-color: #000000;
  border-color: #000000;
}

.emotion-45:focus {
  outline: none;
}

.emotion-45>span {
  padding: 1px 0;
}

.emotion-47 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: none;
  transition: none;
  box-sizing: border-box;
  border: 1.5px solid transparent;
  border-radius: 99em;
  font-size: 16px;
  font-weight: 500;
  letter-spacing: 0px;
  min-height: auto;
  max-height: 60px;
  line-height: 1.25;
  padding: 22px 40px;
  width: auto;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #FFFFFF;
  background-color: #000000;
  border-color: #000000;
}

.emotion-47:focus {
  outline: none;
}

.emotion-47>span {
  padding: 1px 0;
}

.emotion-47:hover,
.emotion-47:focus {
  text-shadow: 0 0 2px currentColor;
}

.emotion-47:active {
  color: #000000;
  background-color: #FFFFFF;
  border-color: #FFFFFF;
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-48 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: none;
  transition: none;
  box-sizing: border-box;
  border: 1.5px solid transparent;
  border-radius: 0;
  font-size: 16px;
  font-weight: 500;
  letter-spacing: 0px;
  min-height: auto;
  max-height: 60px;
  line-height: 1.25;
  padding: 22px 40px;
  width: auto;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #FFFFFF;
  background-color: #000000;
  border-color: #000000;
  display: none;
}

.emotion-48:focus {
  outline: none;
}

.emotion-48>span {
  padding: 1px 0;
}

.emotion-50 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: none;
  transition: none;
  box-sizing: border-box;
  border: 1.5px solid transparent;
  border-radius: 0;
  font-size: 16px;
  font-weight: 500;
  letter-spacing: 0px;
  min-height: auto;
  max-height: 60px;
  line-height: 1.25;
  padding: 22px 40px;
  width: auto;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #FFFFFF;
  background-color: #000000;
  border-color: #000000;
  display: none;
}

.emotion-50:focus {
  outline: none;
}

.emotion-50>span {
  padding: 1px 0;
}

.emotion-50:hover,
.emotion-50:focus {
  text-shadow: 0 0 2px currentColor;
}

.emotion-50:active {
  color: #000000;
  background-color: #FFFFFF;
  border-color: #FFFFFF;
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-51 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: none;
  transition: none;
  box-sizing: border-box;
  border: 1.5px solid transparent;
  border-radius: 0;
  font-size: 18px;
  font-weight: 500;
  letter-spacing: 0px;
  min-height: auto;
  max-height: 64px;
  line-height: 1.1111111111111112;
  padding: 23px 40px;
  width: auto;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #FFFFFF;
  background-color: #000000;
  border-color: #000000;
}

.emotion-51:focus {
  outline: none;
}

.emotion-51>span {
  padding: 1px 0;
}

.emotion-53 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: none;
  transition: none;
  box-sizing: border-box;
  border: 1.5px solid transparent;
  border-radius: 0;
  font-size: 15px;
  font-weight: 500;
  letter-spacing: 0px;
  min-height: auto;
  max-height: 55px;
  line-height: 1.3333333333333333;
  padding: 20px 40px;
  width: auto;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #FFFFFF;
  background-color: #000000;
  border-color: #000000;
}

.emotion-53:focus {
  outline: none;
}

.emotion-53>span {
  padding: 1px 0;
}

.emotion-54 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: none;
  transition: none;
  box-sizing: border-box;
  border: 1.5px solid transparent;
  border-radius: 0;
  font-size: 14px;
  font-weight: 500;
  letter-spacing: 0px;
  min-height: auto;
  max-height: 50px;
  line-height: 1.2857142857142858;
  padding: 18px 40px;
  width: auto;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #FFFFFF;
  background-color: #000000;
  border-color: #000000;
}

.emotion-54:focus {
  outline: none;
}

.emotion-54>span {
  padding: 1px 0;
}

.emotion-55 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: none;
  transition: none;
  box-sizing: border-box;
  border: 1.5px solid transparent;
  border-radius: 0;
  font-size: 12px;
  font-weight: 500;
  letter-spacing: 0px;
  min-height: auto;
  max-height: 44px;
  line-height: 1.5;
  padding: 16px 40px;
  width: auto;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #FFFFFF;
  background-color: #000000;
  border-color: #000000;
}

.emotion-55:focus {
  outline: none;
}

.emotion-55>span {
  padding: 1px 0;
}

<div>
  <div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <button
        class="emotion-0"
      >
        Default
      </button>
      <button
        class="emotion-0"
      >
        Default Selected
      </button>
      <button
        class="emotion-2"
      >
        Default Interactive Styles
      </button>
      <button
        class="emotion-0"
      >
        <div
          aria-busy="true"
          aria-label="loading"
          aria-live="polite"
          class="emotion-4"
          role="alert"
        >
          <div />
          <div />
          <div />
        </div>
      </button>
      <button
        class="emotion-0"
      >
        <div
          aria-busy="true"
          aria-label="loading"
          aria-live="polite"
          class="emotion-4"
          role="alert"
        >
          <div />
          <div />
          <div />
        </div>
      </button>
      <button
        class="emotion-2"
      >
        <div
          aria-busy="true"
          aria-label="loading"
          aria-live="polite"
          class="emotion-4"
          role="alert"
        >
          <div />
          <div />
          <div />
        </div>
      </button>
      <button
        class="emotion-0"
      >
        <div
          aria-busy="false"
          aria-live="polite"
          class="emotion-10"
          role="alert"
        >
          <div
            class="emotion-11"
          >
            <svg
              class="emotion-12"
              height="20"
              viewBox="0 0 30 30"
              width="20"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M15 0c8.284 0 15 6.716 15 15 0 8.284-6.716 15-15 15-8.284 0-15-6.716-15-15C0 6.716 6.716 0 15 0zm0 2C7.82 2 2 7.82 2 15s5.82 13 13 13 13-5.82 13-13S22.18 2 15 2zm5.463 7.65l2.492 2.145-9.787 9.689-5.52-4.788 2.529-2.64 2.991 2.64 7.295-7.046z"
              />
            </svg>
          </div>
          <div
            class="emotion-13"
          />
        </div>
      </button>
      <button
        class="emotion-0"
      >
        <div
          aria-busy="false"
          aria-live="polite"
          class="emotion-10"
          role="alert"
        >
          <div
            class="emotion-11"
          >
            <svg
              class="emotion-12"
              height="20"
              viewBox="0 0 30 30"
              width="20"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M15 0c8.284 0 15 6.716 15 15 0 8.284-6.716 15-15 15-8.284 0-15-6.716-15-15C0 6.716 6.716 0 15 0zm0 2C7.82 2 2 7.82 2 15s5.82 13 13 13 13-5.82 13-13S22.18 2 15 2zm5.463 7.65l2.492 2.145-9.787 9.689-5.52-4.788 2.529-2.64 2.991 2.64 7.295-7.046z"
              />
            </svg>
          </div>
          <div
            class="emotion-13"
          />
        </div>
      </button>
      <button
        class="emotion-2"
      >
        <div
          aria-busy="false"
          aria-live="polite"
          class="emotion-10"
          role="alert"
        >
          <div
            class="emotion-11"
          >
            <svg
              class="emotion-12"
              height="20"
              viewBox="0 0 30 30"
              width="20"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M15 0c8.284 0 15 6.716 15 15 0 8.284-6.716 15-15 15-8.284 0-15-6.716-15-15C0 6.716 6.716 0 15 0zm0 2C7.82 2 2 7.82 2 15s5.82 13 13 13 13-5.82 13-13S22.18 2 15 2zm5.463 7.65l2.492 2.145-9.787 9.689-5.52-4.788 2.529-2.64 2.991 2.64 7.295-7.046z"
              />
            </svg>
          </div>
          <div
            class="emotion-13"
          />
        </div>
      </button>
      <button
        class="emotion-0"
      >
        <div
          aria-busy="false"
          aria-live="polite"
          class="emotion-10"
          role="alert"
        >
          <div
            class="emotion-11"
          >
            <svg
              class="emotion-12"
              height="20"
              viewBox="0 0 30 30"
              width="20"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M15 0c8.284 0 15 6.716 15 15 0 8.284-6.716 15-15 15-8.284 0-15-6.716-15-15C0 6.716 6.716 0 15 0zm0 2C7.82 2 2 7.82 2 15s5.82 13 13 13 13-5.82 13-13S22.18 2 15 2zm6.713 5.636l1.446 1.446-6.508 6.509 6.508 6.509-1.446 1.446-6.508-6.509-6.51 6.509L7.25 22.1l6.509-6.509-6.51-6.509 1.447-1.446 6.509 6.509 6.508-6.509z"
              />
            </svg>
          </div>
          <div
            class="emotion-13"
          />
        </div>
      </button>
      <button
        class="emotion-0"
      >
        <div
          aria-busy="false"
          aria-live="polite"
          class="emotion-10"
          role="alert"
        >
          <div
            class="emotion-11"
          >
            <svg
              class="emotion-12"
              height="20"
              viewBox="0 0 30 30"
              width="20"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M15 0c8.284 0 15 6.716 15 15 0 8.284-6.716 15-15 15-8.284 0-15-6.716-15-15C0 6.716 6.716 0 15 0zm0 2C7.82 2 2 7.82 2 15s5.82 13 13 13 13-5.82 13-13S22.18 2 15 2zm6.713 5.636l1.446 1.446-6.508 6.509 6.508 6.509-1.446 1.446-6.508-6.509-6.51 6.509L7.25 22.1l6.509-6.509-6.51-6.509 1.447-1.446 6.509 6.509 6.508-6.509z"
              />
            </svg>
          </div>
          <div
            class="emotion-13"
          />
        </div>
      </button>
      <button
        class="emotion-2"
      >
        <div
          aria-busy="false"
          aria-live="polite"
          class="emotion-10"
          role="alert"
        >
          <div
            class="emotion-11"
          >
            <svg
              class="emotion-12"
              height="20"
              viewBox="0 0 30 30"
              width="20"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M15 0c8.284 0 15 6.716 15 15 0 8.284-6.716 15-15 15-8.284 0-15-6.716-15-15C0 6.716 6.716 0 15 0zm0 2C7.82 2 2 7.82 2 15s5.82 13 13 13 13-5.82 13-13S22.18 2 15 2zm6.713 5.636l1.446 1.446-6.508 6.509 6.508 6.509-1.446 1.446-6.508-6.509-6.51 6.509L7.25 22.1l6.509-6.509-6.51-6.509 1.447-1.446 6.509 6.509 6.508-6.509z"
              />
            </svg>
          </div>
          <div
            class="emotion-13"
          />
        </div>
      </button>
      <button
        class="emotion-39"
      >
        Full Width
      </button>
      <button
        class="emotion-39"
      >
        Full Width Selected
      </button>
      <button
        class="emotion-41"
      >
        Full Width Interactive Styles
      </button>
      <button
        class="emotion-42"
      >
        Rounded Corners
      </button>
      <button
        class="emotion-42"
      >
        Rounded Corners Selected
      </button>
      <button
        class="emotion-44"
      >
        Rounded Corners Interative Styles
      </button>
      <button
        class="emotion-45"
      >
        Pill Shape
      </button>
      <button
        class="emotion-45"
      >
        Pill Shape Selected
      </button>
      <button
        class="emotion-47"
      >
        Pill Shape Interactive Styles
      </button>
      <button
        class="emotion-48"
      >
        Custom Styles
      </button>
      <button
        class="emotion-48"
      >
        Custom Styles
      </button>
      <button
        class="emotion-50"
      >
        Custom Styles
      </button>
      <button
        class="emotion-51"
      >
        XLarge Size
      </button>
      <button
        class="emotion-0"
      >
        Large Size
      </button>
      <button
        class="emotion-53"
      >
        Medium Size
      </button>
      <button
        class="emotion-54"
      >
        Small Size
      </button>
      <button
        class="emotion-55"
      >
        XSmall Size
      </button>
      <button
        class="emotion-0"
      >
        X-Large Viewport
      </button>
      <button
        class="emotion-0"
      >
        Small Viewport
      </button>
    </div>
  </div>
</div>
`;

exports[`<ComposableButtonBase /> Snapshots should match snapshots for at with feature flags 1`] = `
@keyframes animation-0 {
  0%, 80%, 100% {
    -webkit-transform: scale(0);
    -moz-transform: scale(0);
    -ms-transform: scale(0);
    transform: scale(0);
  }

  40% {
    -webkit-transform: scale(1);
    -moz-transform: scale(1);
    -ms-transform: scale(1);
    transform: scale(1);
  }
}

@keyframes animation-1 {
  0% {
    -webkit-transform: scale(1.0);
    -moz-transform: scale(1.0);
    -ms-transform: scale(1.0);
    transform: scale(1.0);
  }

  10% {
    -webkit-transform: scale(1.5);
    -moz-transform: scale(1.5);
    -ms-transform: scale(1.5);
    transform: scale(1.5);
  }

  30% {
    -webkit-transform: scale(1.0);
    -moz-transform: scale(1.0);
    -ms-transform: scale(1.0);
    transform: scale(1.0);
  }

  40% {
    -webkit-transform: translate(0.9375rem, 0);
    -moz-transform: translate(0.9375rem, 0);
    -ms-transform: translate(0.9375rem, 0);
    transform: translate(0.9375rem, 0);
  }

  50% {
    -webkit-transform: translate(0, 0);
    -moz-transform: translate(0, 0);
    -ms-transform: translate(0, 0);
    transform: translate(0, 0);
  }
}

@keyframes animation-2 {
  0% {
    opacity: 0;
    width: 0;
  }

  35% {
    width: 0;
  }

  50% {
    width: 100%;
  }

  60% {
    opacity: 0;
  }

  100% {
    opacity: 1;
  }
}

.emotion-0 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: none;
  transition: none;
  box-sizing: border-box;
  border: 1.5px solid transparent;
  border-radius: 0;
  font-size: 16px;
  font-weight: 500;
  letter-spacing: 0px;
  min-height: auto;
  max-height: 60px;
  line-height: 1.25;
  padding: 22px 40px;
  width: auto;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #FFFFFF;
  background-color: #000000;
  border-color: #000000;
}

.emotion-0:focus {
  outline: none;
}

.emotion-0>span {
  padding: 1px 0;
}

.emotion-2 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: none;
  transition: none;
  box-sizing: border-box;
  border: 1.5px solid transparent;
  border-radius: 0;
  font-size: 16px;
  font-weight: 500;
  letter-spacing: 0px;
  min-height: auto;
  max-height: 60px;
  line-height: 1.25;
  padding: 22px 40px;
  width: auto;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #FFFFFF;
  background-color: #000000;
  border-color: #000000;
}

.emotion-2:focus {
  outline: none;
}

.emotion-2>span {
  padding: 1px 0;
}

.emotion-2:hover,
.emotion-2:focus {
  text-shadow: 0 0 2px currentColor;
}

.emotion-2:active {
  color: #000000;
  background-color: #FFFFFF;
  border-color: #FFFFFF;
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-4 {
  width: 100%;
  text-align: center;
}

.emotion-4>div {
  width: 1rem;
  height: 1rem;
  background-color: currentColor;
  margin: 0.1rem;
  border-radius: 50%;
  display: inline-block;
  -webkit-animation: animation-0 1.4s infinite ease-in-out both;
  animation: animation-0 1.4s infinite ease-in-out both;
}

.emotion-4 :nth-of-type(1) {
  -webkit-animation-delay: -0.32s;
  animation-delay: -0.32s;
}

.emotion-4 :nth-of-type(2) {
  -webkit-animation-delay: -0.16s;
  animation-delay: -0.16s;
}

.emotion-10 {
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-flex-flow: row;
  -webkit-flex-flow: row;
  -ms-flex-flow: row;
  flex-flow: row;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
}

.emotion-11 {
  -webkit-animation: animation-1 2s;
  animation: animation-1 2s;
  -webkit-animation-iteration-count: 1;
  animation-iteration-count: 1;
  -webkit-animation-delay: 0;
  animation-delay: 0;
  height: 1.25rem;
  width: 1.25rem;
  -webkit-align-self: center;
  -ms-flex-item-align: center;
  align-self: center;
}

.emotion-12 {
  fill: currentColor;
  display: block;
}

.emotion-13 {
  -webkit-animation: animation-2 2s;
  animation: animation-2 2s;
  -webkit-animation-iteration-count: 1;
  animation-iteration-count: 1;
  -webkit-animation-delay: 0;
  animation-delay: 0;
  -webkit-align-self: center;
  -ms-flex-item-align: center;
  align-self: center;
  padding-left: 0.625rem;
}

.emotion-39 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: none;
  transition: none;
  box-sizing: border-box;
  border: 1.5px solid transparent;
  border-radius: 0;
  font-size: 16px;
  font-weight: 500;
  letter-spacing: 0px;
  min-height: auto;
  max-height: 60px;
  line-height: 1.25;
  padding: 22px 40px;
  width: 100%;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #FFFFFF;
  background-color: #000000;
  border-color: #000000;
}

.emotion-39:focus {
  outline: none;
}

.emotion-39>span {
  padding: 1px 0;
}

.emotion-41 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: none;
  transition: none;
  box-sizing: border-box;
  border: 1.5px solid transparent;
  border-radius: 0;
  font-size: 16px;
  font-weight: 500;
  letter-spacing: 0px;
  min-height: auto;
  max-height: 60px;
  line-height: 1.25;
  padding: 22px 40px;
  width: 100%;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #FFFFFF;
  background-color: #000000;
  border-color: #000000;
}

.emotion-41:focus {
  outline: none;
}

.emotion-41>span {
  padding: 1px 0;
}

.emotion-41:hover,
.emotion-41:focus {
  text-shadow: 0 0 2px currentColor;
}

.emotion-41:active {
  color: #000000;
  background-color: #FFFFFF;
  border-color: #FFFFFF;
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-42 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: none;
  transition: none;
  box-sizing: border-box;
  border: 1.5px solid transparent;
  border-radius: 4px;
  font-size: 16px;
  font-weight: 500;
  letter-spacing: 0px;
  min-height: auto;
  max-height: 60px;
  line-height: 1.25;
  padding: 22px 40px;
  width: auto;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #FFFFFF;
  background-color: #000000;
  border-color: #000000;
}

.emotion-42:focus {
  outline: none;
}

.emotion-42>span {
  padding: 1px 0;
}

.emotion-44 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: none;
  transition: none;
  box-sizing: border-box;
  border: 1.5px solid transparent;
  border-radius: 4px;
  font-size: 16px;
  font-weight: 500;
  letter-spacing: 0px;
  min-height: auto;
  max-height: 60px;
  line-height: 1.25;
  padding: 22px 40px;
  width: auto;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #FFFFFF;
  background-color: #000000;
  border-color: #000000;
}

.emotion-44:focus {
  outline: none;
}

.emotion-44>span {
  padding: 1px 0;
}

.emotion-44:hover,
.emotion-44:focus {
  text-shadow: 0 0 2px currentColor;
}

.emotion-44:active {
  color: #000000;
  background-color: #FFFFFF;
  border-color: #FFFFFF;
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-45 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: none;
  transition: none;
  box-sizing: border-box;
  border: 1.5px solid transparent;
  border-radius: 99em;
  font-size: 16px;
  font-weight: 500;
  letter-spacing: 0px;
  min-height: auto;
  max-height: 60px;
  line-height: 1.25;
  padding: 22px 40px;
  width: auto;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #FFFFFF;
  background-color: #000000;
  border-color: #000000;
}

.emotion-45:focus {
  outline: none;
}

.emotion-45>span {
  padding: 1px 0;
}

.emotion-47 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: none;
  transition: none;
  box-sizing: border-box;
  border: 1.5px solid transparent;
  border-radius: 99em;
  font-size: 16px;
  font-weight: 500;
  letter-spacing: 0px;
  min-height: auto;
  max-height: 60px;
  line-height: 1.25;
  padding: 22px 40px;
  width: auto;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #FFFFFF;
  background-color: #000000;
  border-color: #000000;
}

.emotion-47:focus {
  outline: none;
}

.emotion-47>span {
  padding: 1px 0;
}

.emotion-47:hover,
.emotion-47:focus {
  text-shadow: 0 0 2px currentColor;
}

.emotion-47:active {
  color: #000000;
  background-color: #FFFFFF;
  border-color: #FFFFFF;
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-48 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: none;
  transition: none;
  box-sizing: border-box;
  border: 1.5px solid transparent;
  border-radius: 0;
  font-size: 16px;
  font-weight: 500;
  letter-spacing: 0px;
  min-height: auto;
  max-height: 60px;
  line-height: 1.25;
  padding: 22px 40px;
  width: auto;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #FFFFFF;
  background-color: #000000;
  border-color: #000000;
  display: none;
}

.emotion-48:focus {
  outline: none;
}

.emotion-48>span {
  padding: 1px 0;
}

.emotion-50 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: none;
  transition: none;
  box-sizing: border-box;
  border: 1.5px solid transparent;
  border-radius: 0;
  font-size: 16px;
  font-weight: 500;
  letter-spacing: 0px;
  min-height: auto;
  max-height: 60px;
  line-height: 1.25;
  padding: 22px 40px;
  width: auto;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #FFFFFF;
  background-color: #000000;
  border-color: #000000;
  display: none;
}

.emotion-50:focus {
  outline: none;
}

.emotion-50>span {
  padding: 1px 0;
}

.emotion-50:hover,
.emotion-50:focus {
  text-shadow: 0 0 2px currentColor;
}

.emotion-50:active {
  color: #000000;
  background-color: #FFFFFF;
  border-color: #FFFFFF;
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-51 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: none;
  transition: none;
  box-sizing: border-box;
  border: 1.5px solid transparent;
  border-radius: 0;
  font-size: 18px;
  font-weight: 500;
  letter-spacing: 0px;
  min-height: auto;
  max-height: 64px;
  line-height: 1.1111111111111112;
  padding: 23px 40px;
  width: auto;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #FFFFFF;
  background-color: #000000;
  border-color: #000000;
}

.emotion-51:focus {
  outline: none;
}

.emotion-51>span {
  padding: 1px 0;
}

.emotion-53 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: none;
  transition: none;
  box-sizing: border-box;
  border: 1.5px solid transparent;
  border-radius: 0;
  font-size: 15px;
  font-weight: 500;
  letter-spacing: 0px;
  min-height: auto;
  max-height: 55px;
  line-height: 1.3333333333333333;
  padding: 20px 40px;
  width: auto;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #FFFFFF;
  background-color: #000000;
  border-color: #000000;
}

.emotion-53:focus {
  outline: none;
}

.emotion-53>span {
  padding: 1px 0;
}

.emotion-54 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: none;
  transition: none;
  box-sizing: border-box;
  border: 1.5px solid transparent;
  border-radius: 0;
  font-size: 14px;
  font-weight: 500;
  letter-spacing: 0px;
  min-height: auto;
  max-height: 50px;
  line-height: 1.2857142857142858;
  padding: 18px 40px;
  width: auto;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #FFFFFF;
  background-color: #000000;
  border-color: #000000;
}

.emotion-54:focus {
  outline: none;
}

.emotion-54>span {
  padding: 1px 0;
}

.emotion-55 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: none;
  transition: none;
  box-sizing: border-box;
  border: 1.5px solid transparent;
  border-radius: 0;
  font-size: 12px;
  font-weight: 500;
  letter-spacing: 0px;
  min-height: auto;
  max-height: 44px;
  line-height: 1.5;
  padding: 16px 40px;
  width: auto;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #FFFFFF;
  background-color: #000000;
  border-color: #000000;
}

.emotion-55:focus {
  outline: none;
}

.emotion-55>span {
  padding: 1px 0;
}

<div>
  <div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <button
        class="emotion-0"
      >
        Default
      </button>
      <button
        class="emotion-0"
      >
        Default Selected
      </button>
      <button
        class="emotion-2"
      >
        Default Interactive Styles
      </button>
      <button
        class="emotion-0"
      >
        <div
          aria-busy="true"
          aria-label="loading"
          aria-live="polite"
          class="emotion-4"
          role="alert"
        >
          <div />
          <div />
          <div />
        </div>
      </button>
      <button
        class="emotion-0"
      >
        <div
          aria-busy="true"
          aria-label="loading"
          aria-live="polite"
          class="emotion-4"
          role="alert"
        >
          <div />
          <div />
          <div />
        </div>
      </button>
      <button
        class="emotion-2"
      >
        <div
          aria-busy="true"
          aria-label="loading"
          aria-live="polite"
          class="emotion-4"
          role="alert"
        >
          <div />
          <div />
          <div />
        </div>
      </button>
      <button
        class="emotion-0"
      >
        <div
          aria-busy="false"
          aria-live="polite"
          class="emotion-10"
          role="alert"
        >
          <div
            class="emotion-11"
          >
            <svg
              class="emotion-12"
              height="20"
              viewBox="0 0 30 30"
              width="20"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M15 0c8.284 0 15 6.716 15 15 0 8.284-6.716 15-15 15-8.284 0-15-6.716-15-15C0 6.716 6.716 0 15 0zm0 2C7.82 2 2 7.82 2 15s5.82 13 13 13 13-5.82 13-13S22.18 2 15 2zm5.463 7.65l2.492 2.145-9.787 9.689-5.52-4.788 2.529-2.64 2.991 2.64 7.295-7.046z"
              />
            </svg>
          </div>
          <div
            class="emotion-13"
          />
        </div>
      </button>
      <button
        class="emotion-0"
      >
        <div
          aria-busy="false"
          aria-live="polite"
          class="emotion-10"
          role="alert"
        >
          <div
            class="emotion-11"
          >
            <svg
              class="emotion-12"
              height="20"
              viewBox="0 0 30 30"
              width="20"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M15 0c8.284 0 15 6.716 15 15 0 8.284-6.716 15-15 15-8.284 0-15-6.716-15-15C0 6.716 6.716 0 15 0zm0 2C7.82 2 2 7.82 2 15s5.82 13 13 13 13-5.82 13-13S22.18 2 15 2zm5.463 7.65l2.492 2.145-9.787 9.689-5.52-4.788 2.529-2.64 2.991 2.64 7.295-7.046z"
              />
            </svg>
          </div>
          <div
            class="emotion-13"
          />
        </div>
      </button>
      <button
        class="emotion-2"
      >
        <div
          aria-busy="false"
          aria-live="polite"
          class="emotion-10"
          role="alert"
        >
          <div
            class="emotion-11"
          >
            <svg
              class="emotion-12"
              height="20"
              viewBox="0 0 30 30"
              width="20"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M15 0c8.284 0 15 6.716 15 15 0 8.284-6.716 15-15 15-8.284 0-15-6.716-15-15C0 6.716 6.716 0 15 0zm0 2C7.82 2 2 7.82 2 15s5.82 13 13 13 13-5.82 13-13S22.18 2 15 2zm5.463 7.65l2.492 2.145-9.787 9.689-5.52-4.788 2.529-2.64 2.991 2.64 7.295-7.046z"
              />
            </svg>
          </div>
          <div
            class="emotion-13"
          />
        </div>
      </button>
      <button
        class="emotion-0"
      >
        <div
          aria-busy="false"
          aria-live="polite"
          class="emotion-10"
          role="alert"
        >
          <div
            class="emotion-11"
          >
            <svg
              class="emotion-12"
              height="20"
              viewBox="0 0 30 30"
              width="20"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M15 0c8.284 0 15 6.716 15 15 0 8.284-6.716 15-15 15-8.284 0-15-6.716-15-15C0 6.716 6.716 0 15 0zm0 2C7.82 2 2 7.82 2 15s5.82 13 13 13 13-5.82 13-13S22.18 2 15 2zm6.713 5.636l1.446 1.446-6.508 6.509 6.508 6.509-1.446 1.446-6.508-6.509-6.51 6.509L7.25 22.1l6.509-6.509-6.51-6.509 1.447-1.446 6.509 6.509 6.508-6.509z"
              />
            </svg>
          </div>
          <div
            class="emotion-13"
          />
        </div>
      </button>
      <button
        class="emotion-0"
      >
        <div
          aria-busy="false"
          aria-live="polite"
          class="emotion-10"
          role="alert"
        >
          <div
            class="emotion-11"
          >
            <svg
              class="emotion-12"
              height="20"
              viewBox="0 0 30 30"
              width="20"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M15 0c8.284 0 15 6.716 15 15 0 8.284-6.716 15-15 15-8.284 0-15-6.716-15-15C0 6.716 6.716 0 15 0zm0 2C7.82 2 2 7.82 2 15s5.82 13 13 13 13-5.82 13-13S22.18 2 15 2zm6.713 5.636l1.446 1.446-6.508 6.509 6.508 6.509-1.446 1.446-6.508-6.509-6.51 6.509L7.25 22.1l6.509-6.509-6.51-6.509 1.447-1.446 6.509 6.509 6.508-6.509z"
              />
            </svg>
          </div>
          <div
            class="emotion-13"
          />
        </div>
      </button>
      <button
        class="emotion-2"
      >
        <div
          aria-busy="false"
          aria-live="polite"
          class="emotion-10"
          role="alert"
        >
          <div
            class="emotion-11"
          >
            <svg
              class="emotion-12"
              height="20"
              viewBox="0 0 30 30"
              width="20"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M15 0c8.284 0 15 6.716 15 15 0 8.284-6.716 15-15 15-8.284 0-15-6.716-15-15C0 6.716 6.716 0 15 0zm0 2C7.82 2 2 7.82 2 15s5.82 13 13 13 13-5.82 13-13S22.18 2 15 2zm6.713 5.636l1.446 1.446-6.508 6.509 6.508 6.509-1.446 1.446-6.508-6.509-6.51 6.509L7.25 22.1l6.509-6.509-6.51-6.509 1.447-1.446 6.509 6.509 6.508-6.509z"
              />
            </svg>
          </div>
          <div
            class="emotion-13"
          />
        </div>
      </button>
      <button
        class="emotion-39"
      >
        Full Width
      </button>
      <button
        class="emotion-39"
      >
        Full Width Selected
      </button>
      <button
        class="emotion-41"
      >
        Full Width Interactive Styles
      </button>
      <button
        class="emotion-42"
      >
        Rounded Corners
      </button>
      <button
        class="emotion-42"
      >
        Rounded Corners Selected
      </button>
      <button
        class="emotion-44"
      >
        Rounded Corners Interative Styles
      </button>
      <button
        class="emotion-45"
      >
        Pill Shape
      </button>
      <button
        class="emotion-45"
      >
        Pill Shape Selected
      </button>
      <button
        class="emotion-47"
      >
        Pill Shape Interactive Styles
      </button>
      <button
        class="emotion-48"
      >
        Custom Styles
      </button>
      <button
        class="emotion-48"
      >
        Custom Styles
      </button>
      <button
        class="emotion-50"
      >
        Custom Styles
      </button>
      <button
        class="emotion-51"
      >
        XLarge Size
      </button>
      <button
        class="emotion-0"
      >
        Large Size
      </button>
      <button
        class="emotion-53"
      >
        Medium Size
      </button>
      <button
        class="emotion-54"
      >
        Small Size
      </button>
      <button
        class="emotion-55"
      >
        XSmall Size
      </button>
      <button
        class="emotion-0"
      >
        X-Large Viewport
      </button>
      <button
        class="emotion-0"
      >
        Small Viewport
      </button>
    </div>
  </div>
</div>
`;

exports[`<ComposableButtonBase /> Snapshots should match snapshots for br 1`] = `
@keyframes animation-0 {
  0%, 80%, 100% {
    -webkit-transform: scale(0);
    -moz-transform: scale(0);
    -ms-transform: scale(0);
    transform: scale(0);
  }

  40% {
    -webkit-transform: scale(1);
    -moz-transform: scale(1);
    -ms-transform: scale(1);
    transform: scale(1);
  }
}

@keyframes animation-1 {
  0% {
    -webkit-transform: scale(1.0);
    -moz-transform: scale(1.0);
    -ms-transform: scale(1.0);
    transform: scale(1.0);
  }

  10% {
    -webkit-transform: scale(1.5);
    -moz-transform: scale(1.5);
    -ms-transform: scale(1.5);
    transform: scale(1.5);
  }

  30% {
    -webkit-transform: scale(1.0);
    -moz-transform: scale(1.0);
    -ms-transform: scale(1.0);
    transform: scale(1.0);
  }

  40% {
    -webkit-transform: translate(0.9375rem, 0);
    -moz-transform: translate(0.9375rem, 0);
    -ms-transform: translate(0.9375rem, 0);
    transform: translate(0.9375rem, 0);
  }

  50% {
    -webkit-transform: translate(0, 0);
    -moz-transform: translate(0, 0);
    -ms-transform: translate(0, 0);
    transform: translate(0, 0);
  }
}

@keyframes animation-2 {
  0% {
    opacity: 0;
    width: 0;
  }

  35% {
    width: 0;
  }

  50% {
    width: 100%;
  }

  60% {
    opacity: 0;
  }

  100% {
    opacity: 1;
  }
}

.emotion-0 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 0;
  text-align: center;
  padding: 0.5em 0.8em;
  font-size: 1rem;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 600;
  color: #FFFFFF;
  background-color: #000000;
}

.emotion-0:focus {
  outline: none;
}

.emotion-2 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 0;
  text-align: center;
  padding: 0.5em 0.8em;
  font-size: 1rem;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 600;
  color: #FFFFFF;
  background-color: #000000;
}

.emotion-2:focus {
  outline: none;
}

.emotion-2:hover,
.emotion-2:focus {
  color: #000000;
  border-color: #000000;
  background-color: #FFFFFF;
}

.emotion-2:active {
  -webkit-text-decoration: underline;
  text-decoration: underline;
}

.emotion-4 {
  width: 100%;
  text-align: center;
}

.emotion-4>div {
  width: 1rem;
  height: 1rem;
  background-color: currentColor;
  margin: 0.1rem;
  border-radius: 50%;
  display: inline-block;
  -webkit-animation: animation-0 1.4s infinite ease-in-out both;
  animation: animation-0 1.4s infinite ease-in-out both;
}

.emotion-4 :nth-of-type(1) {
  -webkit-animation-delay: -0.32s;
  animation-delay: -0.32s;
}

.emotion-4 :nth-of-type(2) {
  -webkit-animation-delay: -0.16s;
  animation-delay: -0.16s;
}

.emotion-10 {
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-flex-flow: row;
  -webkit-flex-flow: row;
  -ms-flex-flow: row;
  flex-flow: row;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
}

.emotion-11 {
  -webkit-animation: animation-1 2s;
  animation: animation-1 2s;
  -webkit-animation-iteration-count: 1;
  animation-iteration-count: 1;
  -webkit-animation-delay: 0;
  animation-delay: 0;
  height: 1.25rem;
  width: 1.25rem;
  -webkit-align-self: center;
  -ms-flex-item-align: center;
  align-self: center;
}

.emotion-12 {
  fill: currentColor;
  display: block;
}

.emotion-13 {
  -webkit-animation: animation-2 2s;
  animation: animation-2 2s;
  -webkit-animation-iteration-count: 1;
  animation-iteration-count: 1;
  -webkit-animation-delay: 0;
  animation-delay: 0;
  -webkit-align-self: center;
  -ms-flex-item-align: center;
  align-self: center;
  padding-left: 0.625rem;
}

.emotion-39 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 0;
  text-align: center;
  display: block;
  width: 100%;
  padding: 0.5em 0.8em;
  font-size: 1rem;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 600;
  color: #FFFFFF;
  background-color: #000000;
}

.emotion-39:focus {
  outline: none;
}

.emotion-41 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 0;
  text-align: center;
  display: block;
  width: 100%;
  padding: 0.5em 0.8em;
  font-size: 1rem;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 600;
  color: #FFFFFF;
  background-color: #000000;
}

.emotion-41:focus {
  outline: none;
}

.emotion-41:hover,
.emotion-41:focus {
  color: #000000;
  border-color: #000000;
  background-color: #FFFFFF;
}

.emotion-41:active {
  -webkit-text-decoration: underline;
  text-decoration: underline;
}

.emotion-42 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 4px;
  text-align: center;
  padding: 0.5em 0.8em;
  font-size: 1rem;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 600;
  color: #FFFFFF;
  background-color: #000000;
}

.emotion-42:focus {
  outline: none;
}

.emotion-44 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 4px;
  text-align: center;
  padding: 0.5em 0.8em;
  font-size: 1rem;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 600;
  color: #FFFFFF;
  background-color: #000000;
}

.emotion-44:focus {
  outline: none;
}

.emotion-44:hover,
.emotion-44:focus {
  color: #000000;
  border-color: #000000;
  background-color: #FFFFFF;
}

.emotion-44:active {
  -webkit-text-decoration: underline;
  text-decoration: underline;
}

.emotion-45 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 99em;
  text-align: center;
  padding: 0.5em 0.8em;
  font-size: 1rem;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 600;
  color: #FFFFFF;
  background-color: #000000;
}

.emotion-45:focus {
  outline: none;
}

.emotion-47 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 99em;
  text-align: center;
  padding: 0.5em 0.8em;
  font-size: 1rem;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 600;
  color: #FFFFFF;
  background-color: #000000;
}

.emotion-47:focus {
  outline: none;
}

.emotion-47:hover,
.emotion-47:focus {
  color: #000000;
  border-color: #000000;
  background-color: #FFFFFF;
}

.emotion-47:active {
  -webkit-text-decoration: underline;
  text-decoration: underline;
}

.emotion-48 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 0;
  text-align: center;
  padding: 0.5em 0.8em;
  font-size: 1rem;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 600;
  color: #FFFFFF;
  background-color: #000000;
  display: none;
}

.emotion-48:focus {
  outline: none;
}

.emotion-50 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 0;
  text-align: center;
  padding: 0.5em 0.8em;
  font-size: 1rem;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 600;
  color: #FFFFFF;
  background-color: #000000;
  display: none;
}

.emotion-50:focus {
  outline: none;
}

.emotion-50:hover,
.emotion-50:focus {
  color: #000000;
  border-color: #000000;
  background-color: #FFFFFF;
}

.emotion-50:active {
  -webkit-text-decoration: underline;
  text-decoration: underline;
}

.emotion-51 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 0;
  text-align: center;
  padding: 0.5em 0.8em;
  font-size: 1.5rem;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 600;
  color: #FFFFFF;
  background-color: #000000;
}

.emotion-51:focus {
  outline: none;
}

.emotion-52 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 0;
  text-align: center;
  padding: 0.5em 0.8em;
  font-size: 1.2rem;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 600;
  color: #FFFFFF;
  background-color: #000000;
}

.emotion-52:focus {
  outline: none;
}

.emotion-54 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 0;
  text-align: center;
  padding: 0.5em 0.8em;
  font-size: 0.8rem;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 600;
  color: #FFFFFF;
  background-color: #000000;
}

.emotion-54:focus {
  outline: none;
}

<div>
  <div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;"
    >
      <button
        class="emotion-0"
      >
        Default
      </button>
      <button
        class="emotion-0"
      >
        Default Selected
      </button>
      <button
        class="emotion-2"
      >
        Default Interactive Styles
      </button>
      <button
        class="emotion-0"
      >
        <div
          aria-busy="true"
          aria-label="loading"
          aria-live="polite"
          class="emotion-4"
          role="alert"
        >
          <div />
          <div />
          <div />
        </div>
      </button>
      <button
        class="emotion-0"
      >
        <div
          aria-busy="true"
          aria-label="loading"
          aria-live="polite"
          class="emotion-4"
          role="alert"
        >
          <div />
          <div />
          <div />
        </div>
      </button>
      <button
        class="emotion-2"
      >
        <div
          aria-busy="true"
          aria-label="loading"
          aria-live="polite"
          class="emotion-4"
          role="alert"
        >
          <div />
          <div />
          <div />
        </div>
      </button>
      <button
        class="emotion-0"
      >
        <div
          aria-busy="false"
          aria-live="polite"
          class="emotion-10"
          role="alert"
        >
          <div
            class="emotion-11"
          >
            <svg
              class="emotion-12"
              height="20"
              viewBox="0 0 30 30"
              width="20"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M15 0c8.284 0 15 6.716 15 15 0 8.284-6.716 15-15 15-8.284 0-15-6.716-15-15C0 6.716 6.716 0 15 0zm0 2C7.82 2 2 7.82 2 15s5.82 13 13 13 13-5.82 13-13S22.18 2 15 2zm5.463 7.65l2.492 2.145-9.787 9.689-5.52-4.788 2.529-2.64 2.991 2.64 7.295-7.046z"
              />
            </svg>
          </div>
          <div
            class="emotion-13"
          />
        </div>
      </button>
      <button
        class="emotion-0"
      >
        <div
          aria-busy="false"
          aria-live="polite"
          class="emotion-10"
          role="alert"
        >
          <div
            class="emotion-11"
          >
            <svg
              class="emotion-12"
              height="20"
              viewBox="0 0 30 30"
              width="20"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M15 0c8.284 0 15 6.716 15 15 0 8.284-6.716 15-15 15-8.284 0-15-6.716-15-15C0 6.716 6.716 0 15 0zm0 2C7.82 2 2 7.82 2 15s5.82 13 13 13 13-5.82 13-13S22.18 2 15 2zm5.463 7.65l2.492 2.145-9.787 9.689-5.52-4.788 2.529-2.64 2.991 2.64 7.295-7.046z"
              />
            </svg>
          </div>
          <div
            class="emotion-13"
          />
        </div>
      </button>
      <button
        class="emotion-2"
      >
        <div
          aria-busy="false"
          aria-live="polite"
          class="emotion-10"
          role="alert"
        >
          <div
            class="emotion-11"
          >
            <svg
              class="emotion-12"
              height="20"
              viewBox="0 0 30 30"
              width="20"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M15 0c8.284 0 15 6.716 15 15 0 8.284-6.716 15-15 15-8.284 0-15-6.716-15-15C0 6.716 6.716 0 15 0zm0 2C7.82 2 2 7.82 2 15s5.82 13 13 13 13-5.82 13-13S22.18 2 15 2zm5.463 7.65l2.492 2.145-9.787 9.689-5.52-4.788 2.529-2.64 2.991 2.64 7.295-7.046z"
              />
            </svg>
          </div>
          <div
            class="emotion-13"
          />
        </div>
      </button>
      <button
        class="emotion-0"
      >
        <div
          aria-busy="false"
          aria-live="polite"
          class="emotion-10"
          role="alert"
        >
          <div
            class="emotion-11"
          >
            <svg
              class="emotion-12"
              height="20"
              viewBox="0 0 30 30"
              width="20"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M15 0c8.284 0 15 6.716 15 15 0 8.284-6.716 15-15 15-8.284 0-15-6.716-15-15C0 6.716 6.716 0 15 0zm0 2C7.82 2 2 7.82 2 15s5.82 13 13 13 13-5.82 13-13S22.18 2 15 2zm6.713 5.636l1.446 1.446-6.508 6.509 6.508 6.509-1.446 1.446-6.508-6.509-6.51 6.509L7.25 22.1l6.509-6.509-6.51-6.509 1.447-1.446 6.509 6.509 6.508-6.509z"
              />
            </svg>
          </div>
          <div
            class="emotion-13"
          />
        </div>
      </button>
      <button
        class="emotion-0"
      >
        <div
          aria-busy="false"
          aria-live="polite"
          class="emotion-10"
          role="alert"
        >
          <div
            class="emotion-11"
          >
            <svg
              class="emotion-12"
              height="20"
              viewBox="0 0 30 30"
              width="20"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M15 0c8.284 0 15 6.716 15 15 0 8.284-6.716 15-15 15-8.284 0-15-6.716-15-15C0 6.716 6.716 0 15 0zm0 2C7.82 2 2 7.82 2 15s5.82 13 13 13 13-5.82 13-13S22.18 2 15 2zm6.713 5.636l1.446 1.446-6.508 6.509 6.508 6.509-1.446 1.446-6.508-6.509-6.51 6.509L7.25 22.1l6.509-6.509-6.51-6.509 1.447-1.446 6.509 6.509 6.508-6.509z"
              />
            </svg>
          </div>
          <div
            class="emotion-13"
          />
        </div>
      </button>
      <button
        class="emotion-2"
      >
        <div
          aria-busy="false"
          aria-live="polite"
          class="emotion-10"
          role="alert"
        >
          <div
            class="emotion-11"
          >
            <svg
              class="emotion-12"
              height="20"
              viewBox="0 0 30 30"
              width="20"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M15 0c8.284 0 15 6.716 15 15 0 8.284-6.716 15-15 15-8.284 0-15-6.716-15-15C0 6.716 6.716 0 15 0zm0 2C7.82 2 2 7.82 2 15s5.82 13 13 13 13-5.82 13-13S22.18 2 15 2zm6.713 5.636l1.446 1.446-6.508 6.509 6.508 6.509-1.446 1.446-6.508-6.509-6.51 6.509L7.25 22.1l6.509-6.509-6.51-6.509 1.447-1.446 6.509 6.509 6.508-6.509z"
              />
            </svg>
          </div>
          <div
            class="emotion-13"
          />
        </div>
      </button>
      <button
        class="emotion-39"
      >
        Full Width
      </button>
      <button
        class="emotion-39"
      >
        Full Width Selected
      </button>
      <button
        class="emotion-41"
      >
        Full Width Interactive Styles
      </button>
      <button
        class="emotion-42"
      >
        Rounded Corners
      </button>
      <button
        class="emotion-42"
      >
        Rounded Corners Selected
      </button>
      <button
        class="emotion-44"
      >
        Rounded Corners Interative Styles
      </button>
      <button
        class="emotion-45"
      >
        Pill Shape
      </button>
      <button
        class="emotion-45"
      >
        Pill Shape Selected
      </button>
      <button
        class="emotion-47"
      >
        Pill Shape Interactive Styles
      </button>
      <button
        class="emotion-48"
      >
        Custom Styles
      </button>
      <button
        class="emotion-48"
      >
        Custom Styles
      </button>
      <button
        class="emotion-50"
      >
        Custom Styles
      </button>
      <button
        class="emotion-51"
      >
        XLarge Size
      </button>
      <button
        class="emotion-52"
      >
        Large Size
      </button>
      <button
        class="emotion-0"
      >
        Medium Size
      </button>
      <button
        class="emotion-54"
      >
        Small Size
      </button>
      <button
        class="emotion-0"
      >
        XSmall Size
      </button>
      <button
        class="emotion-0"
      >
        X-Large Viewport
      </button>
      <button
        class="emotion-0"
      >
        Small Viewport
      </button>
    </div>
  </div>
</div>
`;

exports[`<ComposableButtonBase /> Snapshots should match snapshots for br with feature flags 1`] = `
@keyframes animation-0 {
  0%, 80%, 100% {
    -webkit-transform: scale(0);
    -moz-transform: scale(0);
    -ms-transform: scale(0);
    transform: scale(0);
  }

  40% {
    -webkit-transform: scale(1);
    -moz-transform: scale(1);
    -ms-transform: scale(1);
    transform: scale(1);
  }
}

@keyframes animation-1 {
  0% {
    -webkit-transform: scale(1.0);
    -moz-transform: scale(1.0);
    -ms-transform: scale(1.0);
    transform: scale(1.0);
  }

  10% {
    -webkit-transform: scale(1.5);
    -moz-transform: scale(1.5);
    -ms-transform: scale(1.5);
    transform: scale(1.5);
  }

  30% {
    -webkit-transform: scale(1.0);
    -moz-transform: scale(1.0);
    -ms-transform: scale(1.0);
    transform: scale(1.0);
  }

  40% {
    -webkit-transform: translate(0.9375rem, 0);
    -moz-transform: translate(0.9375rem, 0);
    -ms-transform: translate(0.9375rem, 0);
    transform: translate(0.9375rem, 0);
  }

  50% {
    -webkit-transform: translate(0, 0);
    -moz-transform: translate(0, 0);
    -ms-transform: translate(0, 0);
    transform: translate(0, 0);
  }
}

@keyframes animation-2 {
  0% {
    opacity: 0;
    width: 0;
  }

  35% {
    width: 0;
  }

  50% {
    width: 100%;
  }

  60% {
    opacity: 0;
  }

  100% {
    opacity: 1;
  }
}

.emotion-0 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 0;
  text-align: center;
  padding: 0.5em 0.8em;
  font-size: 1rem;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 600;
  color: #FFFFFF;
  background-color: #000000;
}

.emotion-0:focus {
  outline: none;
}

.emotion-2 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 0;
  text-align: center;
  padding: 0.5em 0.8em;
  font-size: 1rem;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 600;
  color: #FFFFFF;
  background-color: #000000;
}

.emotion-2:focus {
  outline: none;
}

.emotion-2:hover,
.emotion-2:focus {
  color: #000000;
  border-color: #000000;
  background-color: #FFFFFF;
}

.emotion-2:active {
  -webkit-text-decoration: underline;
  text-decoration: underline;
}

.emotion-4 {
  width: 100%;
  text-align: center;
}

.emotion-4>div {
  width: 1rem;
  height: 1rem;
  background-color: currentColor;
  margin: 0.1rem;
  border-radius: 50%;
  display: inline-block;
  -webkit-animation: animation-0 1.4s infinite ease-in-out both;
  animation: animation-0 1.4s infinite ease-in-out both;
}

.emotion-4 :nth-of-type(1) {
  -webkit-animation-delay: -0.32s;
  animation-delay: -0.32s;
}

.emotion-4 :nth-of-type(2) {
  -webkit-animation-delay: -0.16s;
  animation-delay: -0.16s;
}

.emotion-10 {
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-flex-flow: row;
  -webkit-flex-flow: row;
  -ms-flex-flow: row;
  flex-flow: row;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
}

.emotion-11 {
  -webkit-animation: animation-1 2s;
  animation: animation-1 2s;
  -webkit-animation-iteration-count: 1;
  animation-iteration-count: 1;
  -webkit-animation-delay: 0;
  animation-delay: 0;
  height: 1.25rem;
  width: 1.25rem;
  -webkit-align-self: center;
  -ms-flex-item-align: center;
  align-self: center;
}

.emotion-12 {
  fill: currentColor;
  display: block;
}

.emotion-13 {
  -webkit-animation: animation-2 2s;
  animation: animation-2 2s;
  -webkit-animation-iteration-count: 1;
  animation-iteration-count: 1;
  -webkit-animation-delay: 0;
  animation-delay: 0;
  -webkit-align-self: center;
  -ms-flex-item-align: center;
  align-self: center;
  padding-left: 0.625rem;
}

.emotion-39 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 0;
  text-align: center;
  display: block;
  width: 100%;
  padding: 0.5em 0.8em;
  font-size: 1rem;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 600;
  color: #FFFFFF;
  background-color: #000000;
}

.emotion-39:focus {
  outline: none;
}

.emotion-41 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 0;
  text-align: center;
  display: block;
  width: 100%;
  padding: 0.5em 0.8em;
  font-size: 1rem;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 600;
  color: #FFFFFF;
  background-color: #000000;
}

.emotion-41:focus {
  outline: none;
}

.emotion-41:hover,
.emotion-41:focus {
  color: #000000;
  border-color: #000000;
  background-color: #FFFFFF;
}

.emotion-41:active {
  -webkit-text-decoration: underline;
  text-decoration: underline;
}

.emotion-42 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 4px;
  text-align: center;
  padding: 0.5em 0.8em;
  font-size: 1rem;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 600;
  color: #FFFFFF;
  background-color: #000000;
}

.emotion-42:focus {
  outline: none;
}

.emotion-44 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 4px;
  text-align: center;
  padding: 0.5em 0.8em;
  font-size: 1rem;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 600;
  color: #FFFFFF;
  background-color: #000000;
}

.emotion-44:focus {
  outline: none;
}

.emotion-44:hover,
.emotion-44:focus {
  color: #000000;
  border-color: #000000;
  background-color: #FFFFFF;
}

.emotion-44:active {
  -webkit-text-decoration: underline;
  text-decoration: underline;
}

.emotion-45 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 99em;
  text-align: center;
  padding: 0.5em 0.8em;
  font-size: 1rem;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 600;
  color: #FFFFFF;
  background-color: #000000;
}

.emotion-45:focus {
  outline: none;
}

.emotion-47 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 99em;
  text-align: center;
  padding: 0.5em 0.8em;
  font-size: 1rem;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 600;
  color: #FFFFFF;
  background-color: #000000;
}

.emotion-47:focus {
  outline: none;
}

.emotion-47:hover,
.emotion-47:focus {
  color: #000000;
  border-color: #000000;
  background-color: #FFFFFF;
}

.emotion-47:active {
  -webkit-text-decoration: underline;
  text-decoration: underline;
}

.emotion-48 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 0;
  text-align: center;
  padding: 0.5em 0.8em;
  font-size: 1rem;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 600;
  color: #FFFFFF;
  background-color: #000000;
  display: none;
}

.emotion-48:focus {
  outline: none;
}

.emotion-50 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 0;
  text-align: center;
  padding: 0.5em 0.8em;
  font-size: 1rem;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 600;
  color: #FFFFFF;
  background-color: #000000;
  display: none;
}

.emotion-50:focus {
  outline: none;
}

.emotion-50:hover,
.emotion-50:focus {
  color: #000000;
  border-color: #000000;
  background-color: #FFFFFF;
}

.emotion-50:active {
  -webkit-text-decoration: underline;
  text-decoration: underline;
}

.emotion-51 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 0;
  text-align: center;
  padding: 0.5em 0.8em;
  font-size: 1.5rem;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 600;
  color: #FFFFFF;
  background-color: #000000;
}

.emotion-51:focus {
  outline: none;
}

.emotion-52 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 0;
  text-align: center;
  padding: 0.5em 0.8em;
  font-size: 1.2rem;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 600;
  color: #FFFFFF;
  background-color: #000000;
}

.emotion-52:focus {
  outline: none;
}

.emotion-54 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 0;
  text-align: center;
  padding: 0.5em 0.8em;
  font-size: 0.8rem;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 600;
  color: #FFFFFF;
  background-color: #000000;
}

.emotion-54:focus {
  outline: none;
}

<div>
  <div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;"
    >
      <button
        class="emotion-0"
      >
        Default
      </button>
      <button
        class="emotion-0"
      >
        Default Selected
      </button>
      <button
        class="emotion-2"
      >
        Default Interactive Styles
      </button>
      <button
        class="emotion-0"
      >
        <div
          aria-busy="true"
          aria-label="loading"
          aria-live="polite"
          class="emotion-4"
          role="alert"
        >
          <div />
          <div />
          <div />
        </div>
      </button>
      <button
        class="emotion-0"
      >
        <div
          aria-busy="true"
          aria-label="loading"
          aria-live="polite"
          class="emotion-4"
          role="alert"
        >
          <div />
          <div />
          <div />
        </div>
      </button>
      <button
        class="emotion-2"
      >
        <div
          aria-busy="true"
          aria-label="loading"
          aria-live="polite"
          class="emotion-4"
          role="alert"
        >
          <div />
          <div />
          <div />
        </div>
      </button>
      <button
        class="emotion-0"
      >
        <div
          aria-busy="false"
          aria-live="polite"
          class="emotion-10"
          role="alert"
        >
          <div
            class="emotion-11"
          >
            <svg
              class="emotion-12"
              height="20"
              viewBox="0 0 30 30"
              width="20"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M15 0c8.284 0 15 6.716 15 15 0 8.284-6.716 15-15 15-8.284 0-15-6.716-15-15C0 6.716 6.716 0 15 0zm0 2C7.82 2 2 7.82 2 15s5.82 13 13 13 13-5.82 13-13S22.18 2 15 2zm5.463 7.65l2.492 2.145-9.787 9.689-5.52-4.788 2.529-2.64 2.991 2.64 7.295-7.046z"
              />
            </svg>
          </div>
          <div
            class="emotion-13"
          />
        </div>
      </button>
      <button
        class="emotion-0"
      >
        <div
          aria-busy="false"
          aria-live="polite"
          class="emotion-10"
          role="alert"
        >
          <div
            class="emotion-11"
          >
            <svg
              class="emotion-12"
              height="20"
              viewBox="0 0 30 30"
              width="20"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M15 0c8.284 0 15 6.716 15 15 0 8.284-6.716 15-15 15-8.284 0-15-6.716-15-15C0 6.716 6.716 0 15 0zm0 2C7.82 2 2 7.82 2 15s5.82 13 13 13 13-5.82 13-13S22.18 2 15 2zm5.463 7.65l2.492 2.145-9.787 9.689-5.52-4.788 2.529-2.64 2.991 2.64 7.295-7.046z"
              />
            </svg>
          </div>
          <div
            class="emotion-13"
          />
        </div>
      </button>
      <button
        class="emotion-2"
      >
        <div
          aria-busy="false"
          aria-live="polite"
          class="emotion-10"
          role="alert"
        >
          <div
            class="emotion-11"
          >
            <svg
              class="emotion-12"
              height="20"
              viewBox="0 0 30 30"
              width="20"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M15 0c8.284 0 15 6.716 15 15 0 8.284-6.716 15-15 15-8.284 0-15-6.716-15-15C0 6.716 6.716 0 15 0zm0 2C7.82 2 2 7.82 2 15s5.82 13 13 13 13-5.82 13-13S22.18 2 15 2zm5.463 7.65l2.492 2.145-9.787 9.689-5.52-4.788 2.529-2.64 2.991 2.64 7.295-7.046z"
              />
            </svg>
          </div>
          <div
            class="emotion-13"
          />
        </div>
      </button>
      <button
        class="emotion-0"
      >
        <div
          aria-busy="false"
          aria-live="polite"
          class="emotion-10"
          role="alert"
        >
          <div
            class="emotion-11"
          >
            <svg
              class="emotion-12"
              height="20"
              viewBox="0 0 30 30"
              width="20"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M15 0c8.284 0 15 6.716 15 15 0 8.284-6.716 15-15 15-8.284 0-15-6.716-15-15C0 6.716 6.716 0 15 0zm0 2C7.82 2 2 7.82 2 15s5.82 13 13 13 13-5.82 13-13S22.18 2 15 2zm6.713 5.636l1.446 1.446-6.508 6.509 6.508 6.509-1.446 1.446-6.508-6.509-6.51 6.509L7.25 22.1l6.509-6.509-6.51-6.509 1.447-1.446 6.509 6.509 6.508-6.509z"
              />
            </svg>
          </div>
          <div
            class="emotion-13"
          />
        </div>
      </button>
      <button
        class="emotion-0"
      >
        <div
          aria-busy="false"
          aria-live="polite"
          class="emotion-10"
          role="alert"
        >
          <div
            class="emotion-11"
          >
            <svg
              class="emotion-12"
              height="20"
              viewBox="0 0 30 30"
              width="20"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M15 0c8.284 0 15 6.716 15 15 0 8.284-6.716 15-15 15-8.284 0-15-6.716-15-15C0 6.716 6.716 0 15 0zm0 2C7.82 2 2 7.82 2 15s5.82 13 13 13 13-5.82 13-13S22.18 2 15 2zm6.713 5.636l1.446 1.446-6.508 6.509 6.508 6.509-1.446 1.446-6.508-6.509-6.51 6.509L7.25 22.1l6.509-6.509-6.51-6.509 1.447-1.446 6.509 6.509 6.508-6.509z"
              />
            </svg>
          </div>
          <div
            class="emotion-13"
          />
        </div>
      </button>
      <button
        class="emotion-2"
      >
        <div
          aria-busy="false"
          aria-live="polite"
          class="emotion-10"
          role="alert"
        >
          <div
            class="emotion-11"
          >
            <svg
              class="emotion-12"
              height="20"
              viewBox="0 0 30 30"
              width="20"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M15 0c8.284 0 15 6.716 15 15 0 8.284-6.716 15-15 15-8.284 0-15-6.716-15-15C0 6.716 6.716 0 15 0zm0 2C7.82 2 2 7.82 2 15s5.82 13 13 13 13-5.82 13-13S22.18 2 15 2zm6.713 5.636l1.446 1.446-6.508 6.509 6.508 6.509-1.446 1.446-6.508-6.509-6.51 6.509L7.25 22.1l6.509-6.509-6.51-6.509 1.447-1.446 6.509 6.509 6.508-6.509z"
              />
            </svg>
          </div>
          <div
            class="emotion-13"
          />
        </div>
      </button>
      <button
        class="emotion-39"
      >
        Full Width
      </button>
      <button
        class="emotion-39"
      >
        Full Width Selected
      </button>
      <button
        class="emotion-41"
      >
        Full Width Interactive Styles
      </button>
      <button
        class="emotion-42"
      >
        Rounded Corners
      </button>
      <button
        class="emotion-42"
      >
        Rounded Corners Selected
      </button>
      <button
        class="emotion-44"
      >
        Rounded Corners Interative Styles
      </button>
      <button
        class="emotion-45"
      >
        Pill Shape
      </button>
      <button
        class="emotion-45"
      >
        Pill Shape Selected
      </button>
      <button
        class="emotion-47"
      >
        Pill Shape Interactive Styles
      </button>
      <button
        class="emotion-48"
      >
        Custom Styles
      </button>
      <button
        class="emotion-48"
      >
        Custom Styles
      </button>
      <button
        class="emotion-50"
      >
        Custom Styles
      </button>
      <button
        class="emotion-51"
      >
        XLarge Size
      </button>
      <button
        class="emotion-52"
      >
        Large Size
      </button>
      <button
        class="emotion-0"
      >
        Medium Size
      </button>
      <button
        class="emotion-54"
      >
        Small Size
      </button>
      <button
        class="emotion-0"
      >
        XSmall Size
      </button>
      <button
        class="emotion-0"
      >
        X-Large Viewport
      </button>
      <button
        class="emotion-0"
      >
        Small Viewport
      </button>
    </div>
  </div>
</div>
`;

exports[`<ComposableButtonBase /> Snapshots should match snapshots for brfs 1`] = `
@keyframes animation-0 {
  0%, 80%, 100% {
    -webkit-transform: scale(0);
    -moz-transform: scale(0);
    -ms-transform: scale(0);
    transform: scale(0);
  }

  40% {
    -webkit-transform: scale(1);
    -moz-transform: scale(1);
    -ms-transform: scale(1);
    transform: scale(1);
  }
}

@keyframes animation-1 {
  0% {
    -webkit-transform: scale(1.0);
    -moz-transform: scale(1.0);
    -ms-transform: scale(1.0);
    transform: scale(1.0);
  }

  10% {
    -webkit-transform: scale(1.5);
    -moz-transform: scale(1.5);
    -ms-transform: scale(1.5);
    transform: scale(1.5);
  }

  30% {
    -webkit-transform: scale(1.0);
    -moz-transform: scale(1.0);
    -ms-transform: scale(1.0);
    transform: scale(1.0);
  }

  40% {
    -webkit-transform: translate(0.9375rem, 0);
    -moz-transform: translate(0.9375rem, 0);
    -ms-transform: translate(0.9375rem, 0);
    transform: translate(0.9375rem, 0);
  }

  50% {
    -webkit-transform: translate(0, 0);
    -moz-transform: translate(0, 0);
    -ms-transform: translate(0, 0);
    transform: translate(0, 0);
  }
}

@keyframes animation-2 {
  0% {
    opacity: 0;
    width: 0;
  }

  35% {
    width: 0;
  }

  50% {
    width: 100%;
  }

  60% {
    opacity: 0;
  }

  100% {
    opacity: 1;
  }
}

.emotion-0 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 0;
  text-align: center;
  padding: 0.5em 0.8em;
  font-size: 1rem;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 700;
  color: #FFFFFF;
  background-color: #000000;
}

.emotion-0:focus {
  outline: none;
}

.emotion-2 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 0;
  text-align: center;
  padding: 0.5em 0.8em;
  font-size: 1rem;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 700;
  color: #FFFFFF;
  background-color: #000000;
}

.emotion-2:focus {
  outline: none;
}

.emotion-2:hover,
.emotion-2:focus {
  color: #000000;
  border-color: #000000;
  background-color: #FFFFFF;
}

.emotion-2:active {
  -webkit-text-decoration: underline;
  text-decoration: underline;
}

.emotion-4 {
  width: 100%;
  text-align: center;
}

.emotion-4>div {
  width: 1rem;
  height: 1rem;
  background-color: currentColor;
  margin: 0.1rem;
  border-radius: 50%;
  display: inline-block;
  -webkit-animation: animation-0 1.4s infinite ease-in-out both;
  animation: animation-0 1.4s infinite ease-in-out both;
}

.emotion-4 :nth-of-type(1) {
  -webkit-animation-delay: -0.32s;
  animation-delay: -0.32s;
}

.emotion-4 :nth-of-type(2) {
  -webkit-animation-delay: -0.16s;
  animation-delay: -0.16s;
}

.emotion-10 {
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-flex-flow: row;
  -webkit-flex-flow: row;
  -ms-flex-flow: row;
  flex-flow: row;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
}

.emotion-11 {
  -webkit-animation: animation-1 2s;
  animation: animation-1 2s;
  -webkit-animation-iteration-count: 1;
  animation-iteration-count: 1;
  -webkit-animation-delay: 0;
  animation-delay: 0;
  height: 1.25rem;
  width: 1.25rem;
  -webkit-align-self: center;
  -ms-flex-item-align: center;
  align-self: center;
}

.emotion-12 {
  fill: currentColor;
  display: block;
}

.emotion-13 {
  -webkit-animation: animation-2 2s;
  animation: animation-2 2s;
  -webkit-animation-iteration-count: 1;
  animation-iteration-count: 1;
  -webkit-animation-delay: 0;
  animation-delay: 0;
  -webkit-align-self: center;
  -ms-flex-item-align: center;
  align-self: center;
  padding-left: 0.625rem;
}

.emotion-39 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 0;
  text-align: center;
  display: block;
  width: 100%;
  padding: 0.5em 0.8em;
  font-size: 1rem;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 700;
  color: #FFFFFF;
  background-color: #000000;
}

.emotion-39:focus {
  outline: none;
}

.emotion-41 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 0;
  text-align: center;
  display: block;
  width: 100%;
  padding: 0.5em 0.8em;
  font-size: 1rem;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 700;
  color: #FFFFFF;
  background-color: #000000;
}

.emotion-41:focus {
  outline: none;
}

.emotion-41:hover,
.emotion-41:focus {
  color: #000000;
  border-color: #000000;
  background-color: #FFFFFF;
}

.emotion-41:active {
  -webkit-text-decoration: underline;
  text-decoration: underline;
}

.emotion-42 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 4px;
  text-align: center;
  padding: 0.5em 0.8em;
  font-size: 1rem;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 700;
  color: #FFFFFF;
  background-color: #000000;
}

.emotion-42:focus {
  outline: none;
}

.emotion-44 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 4px;
  text-align: center;
  padding: 0.5em 0.8em;
  font-size: 1rem;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 700;
  color: #FFFFFF;
  background-color: #000000;
}

.emotion-44:focus {
  outline: none;
}

.emotion-44:hover,
.emotion-44:focus {
  color: #000000;
  border-color: #000000;
  background-color: #FFFFFF;
}

.emotion-44:active {
  -webkit-text-decoration: underline;
  text-decoration: underline;
}

.emotion-45 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 99em;
  text-align: center;
  padding: 0.5em 0.8em;
  font-size: 1rem;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 700;
  color: #FFFFFF;
  background-color: #000000;
}

.emotion-45:focus {
  outline: none;
}

.emotion-47 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 99em;
  text-align: center;
  padding: 0.5em 0.8em;
  font-size: 1rem;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 700;
  color: #FFFFFF;
  background-color: #000000;
}

.emotion-47:focus {
  outline: none;
}

.emotion-47:hover,
.emotion-47:focus {
  color: #000000;
  border-color: #000000;
  background-color: #FFFFFF;
}

.emotion-47:active {
  -webkit-text-decoration: underline;
  text-decoration: underline;
}

.emotion-48 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 0;
  text-align: center;
  padding: 0.5em 0.8em;
  font-size: 1rem;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 700;
  color: #FFFFFF;
  background-color: #000000;
  display: none;
}

.emotion-48:focus {
  outline: none;
}

.emotion-50 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 0;
  text-align: center;
  padding: 0.5em 0.8em;
  font-size: 1rem;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 700;
  color: #FFFFFF;
  background-color: #000000;
  display: none;
}

.emotion-50:focus {
  outline: none;
}

.emotion-50:hover,
.emotion-50:focus {
  color: #000000;
  border-color: #000000;
  background-color: #FFFFFF;
}

.emotion-50:active {
  -webkit-text-decoration: underline;
  text-decoration: underline;
}

.emotion-51 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 0;
  text-align: center;
  padding: 0.5em 0.8em;
  font-size: 1.5rem;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 700;
  color: #FFFFFF;
  background-color: #000000;
}

.emotion-51:focus {
  outline: none;
}

.emotion-52 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 0;
  text-align: center;
  padding: 0.5em 0.8em;
  font-size: 1.2rem;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 700;
  color: #FFFFFF;
  background-color: #000000;
}

.emotion-52:focus {
  outline: none;
}

.emotion-54 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 0;
  text-align: center;
  padding: 0.5em 0.8em;
  font-size: 0.8rem;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 700;
  color: #FFFFFF;
  background-color: #000000;
}

.emotion-54:focus {
  outline: none;
}

<div>
  <div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;"
    >
      <button
        class="emotion-0"
      >
        Default
      </button>
      <button
        class="emotion-0"
      >
        Default Selected
      </button>
      <button
        class="emotion-2"
      >
        Default Interactive Styles
      </button>
      <button
        class="emotion-0"
      >
        <div
          aria-busy="true"
          aria-label="loading"
          aria-live="polite"
          class="emotion-4"
          role="alert"
        >
          <div />
          <div />
          <div />
        </div>
      </button>
      <button
        class="emotion-0"
      >
        <div
          aria-busy="true"
          aria-label="loading"
          aria-live="polite"
          class="emotion-4"
          role="alert"
        >
          <div />
          <div />
          <div />
        </div>
      </button>
      <button
        class="emotion-2"
      >
        <div
          aria-busy="true"
          aria-label="loading"
          aria-live="polite"
          class="emotion-4"
          role="alert"
        >
          <div />
          <div />
          <div />
        </div>
      </button>
      <button
        class="emotion-0"
      >
        <div
          aria-busy="false"
          aria-live="polite"
          class="emotion-10"
          role="alert"
        >
          <div
            class="emotion-11"
          >
            <svg
              class="emotion-12"
              height="20"
              viewBox="0 0 30 30"
              width="20"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M15 0c8.284 0 15 6.716 15 15 0 8.284-6.716 15-15 15-8.284 0-15-6.716-15-15C0 6.716 6.716 0 15 0zm0 2C7.82 2 2 7.82 2 15s5.82 13 13 13 13-5.82 13-13S22.18 2 15 2zm5.463 7.65l2.492 2.145-9.787 9.689-5.52-4.788 2.529-2.64 2.991 2.64 7.295-7.046z"
              />
            </svg>
          </div>
          <div
            class="emotion-13"
          />
        </div>
      </button>
      <button
        class="emotion-0"
      >
        <div
          aria-busy="false"
          aria-live="polite"
          class="emotion-10"
          role="alert"
        >
          <div
            class="emotion-11"
          >
            <svg
              class="emotion-12"
              height="20"
              viewBox="0 0 30 30"
              width="20"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M15 0c8.284 0 15 6.716 15 15 0 8.284-6.716 15-15 15-8.284 0-15-6.716-15-15C0 6.716 6.716 0 15 0zm0 2C7.82 2 2 7.82 2 15s5.82 13 13 13 13-5.82 13-13S22.18 2 15 2zm5.463 7.65l2.492 2.145-9.787 9.689-5.52-4.788 2.529-2.64 2.991 2.64 7.295-7.046z"
              />
            </svg>
          </div>
          <div
            class="emotion-13"
          />
        </div>
      </button>
      <button
        class="emotion-2"
      >
        <div
          aria-busy="false"
          aria-live="polite"
          class="emotion-10"
          role="alert"
        >
          <div
            class="emotion-11"
          >
            <svg
              class="emotion-12"
              height="20"
              viewBox="0 0 30 30"
              width="20"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M15 0c8.284 0 15 6.716 15 15 0 8.284-6.716 15-15 15-8.284 0-15-6.716-15-15C0 6.716 6.716 0 15 0zm0 2C7.82 2 2 7.82 2 15s5.82 13 13 13 13-5.82 13-13S22.18 2 15 2zm5.463 7.65l2.492 2.145-9.787 9.689-5.52-4.788 2.529-2.64 2.991 2.64 7.295-7.046z"
              />
            </svg>
          </div>
          <div
            class="emotion-13"
          />
        </div>
      </button>
      <button
        class="emotion-0"
      >
        <div
          aria-busy="false"
          aria-live="polite"
          class="emotion-10"
          role="alert"
        >
          <div
            class="emotion-11"
          >
            <svg
              class="emotion-12"
              height="20"
              viewBox="0 0 30 30"
              width="20"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M15 0c8.284 0 15 6.716 15 15 0 8.284-6.716 15-15 15-8.284 0-15-6.716-15-15C0 6.716 6.716 0 15 0zm0 2C7.82 2 2 7.82 2 15s5.82 13 13 13 13-5.82 13-13S22.18 2 15 2zm6.713 5.636l1.446 1.446-6.508 6.509 6.508 6.509-1.446 1.446-6.508-6.509-6.51 6.509L7.25 22.1l6.509-6.509-6.51-6.509 1.447-1.446 6.509 6.509 6.508-6.509z"
              />
            </svg>
          </div>
          <div
            class="emotion-13"
          />
        </div>
      </button>
      <button
        class="emotion-0"
      >
        <div
          aria-busy="false"
          aria-live="polite"
          class="emotion-10"
          role="alert"
        >
          <div
            class="emotion-11"
          >
            <svg
              class="emotion-12"
              height="20"
              viewBox="0 0 30 30"
              width="20"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M15 0c8.284 0 15 6.716 15 15 0 8.284-6.716 15-15 15-8.284 0-15-6.716-15-15C0 6.716 6.716 0 15 0zm0 2C7.82 2 2 7.82 2 15s5.82 13 13 13 13-5.82 13-13S22.18 2 15 2zm6.713 5.636l1.446 1.446-6.508 6.509 6.508 6.509-1.446 1.446-6.508-6.509-6.51 6.509L7.25 22.1l6.509-6.509-6.51-6.509 1.447-1.446 6.509 6.509 6.508-6.509z"
              />
            </svg>
          </div>
          <div
            class="emotion-13"
          />
        </div>
      </button>
      <button
        class="emotion-2"
      >
        <div
          aria-busy="false"
          aria-live="polite"
          class="emotion-10"
          role="alert"
        >
          <div
            class="emotion-11"
          >
            <svg
              class="emotion-12"
              height="20"
              viewBox="0 0 30 30"
              width="20"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M15 0c8.284 0 15 6.716 15 15 0 8.284-6.716 15-15 15-8.284 0-15-6.716-15-15C0 6.716 6.716 0 15 0zm0 2C7.82 2 2 7.82 2 15s5.82 13 13 13 13-5.82 13-13S22.18 2 15 2zm6.713 5.636l1.446 1.446-6.508 6.509 6.508 6.509-1.446 1.446-6.508-6.509-6.51 6.509L7.25 22.1l6.509-6.509-6.51-6.509 1.447-1.446 6.509 6.509 6.508-6.509z"
              />
            </svg>
          </div>
          <div
            class="emotion-13"
          />
        </div>
      </button>
      <button
        class="emotion-39"
      >
        Full Width
      </button>
      <button
        class="emotion-39"
      >
        Full Width Selected
      </button>
      <button
        class="emotion-41"
      >
        Full Width Interactive Styles
      </button>
      <button
        class="emotion-42"
      >
        Rounded Corners
      </button>
      <button
        class="emotion-42"
      >
        Rounded Corners Selected
      </button>
      <button
        class="emotion-44"
      >
        Rounded Corners Interative Styles
      </button>
      <button
        class="emotion-45"
      >
        Pill Shape
      </button>
      <button
        class="emotion-45"
      >
        Pill Shape Selected
      </button>
      <button
        class="emotion-47"
      >
        Pill Shape Interactive Styles
      </button>
      <button
        class="emotion-48"
      >
        Custom Styles
      </button>
      <button
        class="emotion-48"
      >
        Custom Styles
      </button>
      <button
        class="emotion-50"
      >
        Custom Styles
      </button>
      <button
        class="emotion-51"
      >
        XLarge Size
      </button>
      <button
        class="emotion-52"
      >
        Large Size
      </button>
      <button
        class="emotion-0"
      >
        Medium Size
      </button>
      <button
        class="emotion-54"
      >
        Small Size
      </button>
      <button
        class="emotion-0"
      >
        XSmall Size
      </button>
      <button
        class="emotion-0"
      >
        X-Large Viewport
      </button>
      <button
        class="emotion-0"
      >
        Small Viewport
      </button>
    </div>
  </div>
</div>
`;

exports[`<ComposableButtonBase /> Snapshots should match snapshots for brfs with feature flags 1`] = `
@keyframes animation-0 {
  0%, 80%, 100% {
    -webkit-transform: scale(0);
    -moz-transform: scale(0);
    -ms-transform: scale(0);
    transform: scale(0);
  }

  40% {
    -webkit-transform: scale(1);
    -moz-transform: scale(1);
    -ms-transform: scale(1);
    transform: scale(1);
  }
}

@keyframes animation-1 {
  0% {
    -webkit-transform: scale(1.0);
    -moz-transform: scale(1.0);
    -ms-transform: scale(1.0);
    transform: scale(1.0);
  }

  10% {
    -webkit-transform: scale(1.5);
    -moz-transform: scale(1.5);
    -ms-transform: scale(1.5);
    transform: scale(1.5);
  }

  30% {
    -webkit-transform: scale(1.0);
    -moz-transform: scale(1.0);
    -ms-transform: scale(1.0);
    transform: scale(1.0);
  }

  40% {
    -webkit-transform: translate(0.9375rem, 0);
    -moz-transform: translate(0.9375rem, 0);
    -ms-transform: translate(0.9375rem, 0);
    transform: translate(0.9375rem, 0);
  }

  50% {
    -webkit-transform: translate(0, 0);
    -moz-transform: translate(0, 0);
    -ms-transform: translate(0, 0);
    transform: translate(0, 0);
  }
}

@keyframes animation-2 {
  0% {
    opacity: 0;
    width: 0;
  }

  35% {
    width: 0;
  }

  50% {
    width: 100%;
  }

  60% {
    opacity: 0;
  }

  100% {
    opacity: 1;
  }
}

.emotion-0 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 0;
  text-align: center;
  padding: 0.5em 0.8em;
  font-size: 1rem;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 700;
  color: #FFFFFF;
  background-color: #000000;
}

.emotion-0:focus {
  outline: none;
}

.emotion-2 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 0;
  text-align: center;
  padding: 0.5em 0.8em;
  font-size: 1rem;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 700;
  color: #FFFFFF;
  background-color: #000000;
}

.emotion-2:focus {
  outline: none;
}

.emotion-2:hover,
.emotion-2:focus {
  color: #000000;
  border-color: #000000;
  background-color: #FFFFFF;
}

.emotion-2:active {
  -webkit-text-decoration: underline;
  text-decoration: underline;
}

.emotion-4 {
  width: 100%;
  text-align: center;
}

.emotion-4>div {
  width: 1rem;
  height: 1rem;
  background-color: currentColor;
  margin: 0.1rem;
  border-radius: 50%;
  display: inline-block;
  -webkit-animation: animation-0 1.4s infinite ease-in-out both;
  animation: animation-0 1.4s infinite ease-in-out both;
}

.emotion-4 :nth-of-type(1) {
  -webkit-animation-delay: -0.32s;
  animation-delay: -0.32s;
}

.emotion-4 :nth-of-type(2) {
  -webkit-animation-delay: -0.16s;
  animation-delay: -0.16s;
}

.emotion-10 {
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-flex-flow: row;
  -webkit-flex-flow: row;
  -ms-flex-flow: row;
  flex-flow: row;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
}

.emotion-11 {
  -webkit-animation: animation-1 2s;
  animation: animation-1 2s;
  -webkit-animation-iteration-count: 1;
  animation-iteration-count: 1;
  -webkit-animation-delay: 0;
  animation-delay: 0;
  height: 1.25rem;
  width: 1.25rem;
  -webkit-align-self: center;
  -ms-flex-item-align: center;
  align-self: center;
}

.emotion-12 {
  fill: currentColor;
  display: block;
}

.emotion-13 {
  -webkit-animation: animation-2 2s;
  animation: animation-2 2s;
  -webkit-animation-iteration-count: 1;
  animation-iteration-count: 1;
  -webkit-animation-delay: 0;
  animation-delay: 0;
  -webkit-align-self: center;
  -ms-flex-item-align: center;
  align-self: center;
  padding-left: 0.625rem;
}

.emotion-39 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 0;
  text-align: center;
  display: block;
  width: 100%;
  padding: 0.5em 0.8em;
  font-size: 1rem;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 700;
  color: #FFFFFF;
  background-color: #000000;
}

.emotion-39:focus {
  outline: none;
}

.emotion-41 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 0;
  text-align: center;
  display: block;
  width: 100%;
  padding: 0.5em 0.8em;
  font-size: 1rem;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 700;
  color: #FFFFFF;
  background-color: #000000;
}

.emotion-41:focus {
  outline: none;
}

.emotion-41:hover,
.emotion-41:focus {
  color: #000000;
  border-color: #000000;
  background-color: #FFFFFF;
}

.emotion-41:active {
  -webkit-text-decoration: underline;
  text-decoration: underline;
}

.emotion-42 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 4px;
  text-align: center;
  padding: 0.5em 0.8em;
  font-size: 1rem;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 700;
  color: #FFFFFF;
  background-color: #000000;
}

.emotion-42:focus {
  outline: none;
}

.emotion-44 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 4px;
  text-align: center;
  padding: 0.5em 0.8em;
  font-size: 1rem;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 700;
  color: #FFFFFF;
  background-color: #000000;
}

.emotion-44:focus {
  outline: none;
}

.emotion-44:hover,
.emotion-44:focus {
  color: #000000;
  border-color: #000000;
  background-color: #FFFFFF;
}

.emotion-44:active {
  -webkit-text-decoration: underline;
  text-decoration: underline;
}

.emotion-45 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 99em;
  text-align: center;
  padding: 0.5em 0.8em;
  font-size: 1rem;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 700;
  color: #FFFFFF;
  background-color: #000000;
}

.emotion-45:focus {
  outline: none;
}

.emotion-47 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 99em;
  text-align: center;
  padding: 0.5em 0.8em;
  font-size: 1rem;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 700;
  color: #FFFFFF;
  background-color: #000000;
}

.emotion-47:focus {
  outline: none;
}

.emotion-47:hover,
.emotion-47:focus {
  color: #000000;
  border-color: #000000;
  background-color: #FFFFFF;
}

.emotion-47:active {
  -webkit-text-decoration: underline;
  text-decoration: underline;
}

.emotion-48 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 0;
  text-align: center;
  padding: 0.5em 0.8em;
  font-size: 1rem;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 700;
  color: #FFFFFF;
  background-color: #000000;
  display: none;
}

.emotion-48:focus {
  outline: none;
}

.emotion-50 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 0;
  text-align: center;
  padding: 0.5em 0.8em;
  font-size: 1rem;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 700;
  color: #FFFFFF;
  background-color: #000000;
  display: none;
}

.emotion-50:focus {
  outline: none;
}

.emotion-50:hover,
.emotion-50:focus {
  color: #000000;
  border-color: #000000;
  background-color: #FFFFFF;
}

.emotion-50:active {
  -webkit-text-decoration: underline;
  text-decoration: underline;
}

.emotion-51 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 0;
  text-align: center;
  padding: 0.5em 0.8em;
  font-size: 1.5rem;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 700;
  color: #FFFFFF;
  background-color: #000000;
}

.emotion-51:focus {
  outline: none;
}

.emotion-52 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 0;
  text-align: center;
  padding: 0.5em 0.8em;
  font-size: 1.2rem;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 700;
  color: #FFFFFF;
  background-color: #000000;
}

.emotion-52:focus {
  outline: none;
}

.emotion-54 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 0;
  text-align: center;
  padding: 0.5em 0.8em;
  font-size: 0.8rem;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 700;
  color: #FFFFFF;
  background-color: #000000;
}

.emotion-54:focus {
  outline: none;
}

<div>
  <div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;"
    >
      <button
        class="emotion-0"
      >
        Default
      </button>
      <button
        class="emotion-0"
      >
        Default Selected
      </button>
      <button
        class="emotion-2"
      >
        Default Interactive Styles
      </button>
      <button
        class="emotion-0"
      >
        <div
          aria-busy="true"
          aria-label="loading"
          aria-live="polite"
          class="emotion-4"
          role="alert"
        >
          <div />
          <div />
          <div />
        </div>
      </button>
      <button
        class="emotion-0"
      >
        <div
          aria-busy="true"
          aria-label="loading"
          aria-live="polite"
          class="emotion-4"
          role="alert"
        >
          <div />
          <div />
          <div />
        </div>
      </button>
      <button
        class="emotion-2"
      >
        <div
          aria-busy="true"
          aria-label="loading"
          aria-live="polite"
          class="emotion-4"
          role="alert"
        >
          <div />
          <div />
          <div />
        </div>
      </button>
      <button
        class="emotion-0"
      >
        <div
          aria-busy="false"
          aria-live="polite"
          class="emotion-10"
          role="alert"
        >
          <div
            class="emotion-11"
          >
            <svg
              class="emotion-12"
              height="20"
              viewBox="0 0 30 30"
              width="20"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M15 0c8.284 0 15 6.716 15 15 0 8.284-6.716 15-15 15-8.284 0-15-6.716-15-15C0 6.716 6.716 0 15 0zm0 2C7.82 2 2 7.82 2 15s5.82 13 13 13 13-5.82 13-13S22.18 2 15 2zm5.463 7.65l2.492 2.145-9.787 9.689-5.52-4.788 2.529-2.64 2.991 2.64 7.295-7.046z"
              />
            </svg>
          </div>
          <div
            class="emotion-13"
          />
        </div>
      </button>
      <button
        class="emotion-0"
      >
        <div
          aria-busy="false"
          aria-live="polite"
          class="emotion-10"
          role="alert"
        >
          <div
            class="emotion-11"
          >
            <svg
              class="emotion-12"
              height="20"
              viewBox="0 0 30 30"
              width="20"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M15 0c8.284 0 15 6.716 15 15 0 8.284-6.716 15-15 15-8.284 0-15-6.716-15-15C0 6.716 6.716 0 15 0zm0 2C7.82 2 2 7.82 2 15s5.82 13 13 13 13-5.82 13-13S22.18 2 15 2zm5.463 7.65l2.492 2.145-9.787 9.689-5.52-4.788 2.529-2.64 2.991 2.64 7.295-7.046z"
              />
            </svg>
          </div>
          <div
            class="emotion-13"
          />
        </div>
      </button>
      <button
        class="emotion-2"
      >
        <div
          aria-busy="false"
          aria-live="polite"
          class="emotion-10"
          role="alert"
        >
          <div
            class="emotion-11"
          >
            <svg
              class="emotion-12"
              height="20"
              viewBox="0 0 30 30"
              width="20"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M15 0c8.284 0 15 6.716 15 15 0 8.284-6.716 15-15 15-8.284 0-15-6.716-15-15C0 6.716 6.716 0 15 0zm0 2C7.82 2 2 7.82 2 15s5.82 13 13 13 13-5.82 13-13S22.18 2 15 2zm5.463 7.65l2.492 2.145-9.787 9.689-5.52-4.788 2.529-2.64 2.991 2.64 7.295-7.046z"
              />
            </svg>
          </div>
          <div
            class="emotion-13"
          />
        </div>
      </button>
      <button
        class="emotion-0"
      >
        <div
          aria-busy="false"
          aria-live="polite"
          class="emotion-10"
          role="alert"
        >
          <div
            class="emotion-11"
          >
            <svg
              class="emotion-12"
              height="20"
              viewBox="0 0 30 30"
              width="20"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M15 0c8.284 0 15 6.716 15 15 0 8.284-6.716 15-15 15-8.284 0-15-6.716-15-15C0 6.716 6.716 0 15 0zm0 2C7.82 2 2 7.82 2 15s5.82 13 13 13 13-5.82 13-13S22.18 2 15 2zm6.713 5.636l1.446 1.446-6.508 6.509 6.508 6.509-1.446 1.446-6.508-6.509-6.51 6.509L7.25 22.1l6.509-6.509-6.51-6.509 1.447-1.446 6.509 6.509 6.508-6.509z"
              />
            </svg>
          </div>
          <div
            class="emotion-13"
          />
        </div>
      </button>
      <button
        class="emotion-0"
      >
        <div
          aria-busy="false"
          aria-live="polite"
          class="emotion-10"
          role="alert"
        >
          <div
            class="emotion-11"
          >
            <svg
              class="emotion-12"
              height="20"
              viewBox="0 0 30 30"
              width="20"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M15 0c8.284 0 15 6.716 15 15 0 8.284-6.716 15-15 15-8.284 0-15-6.716-15-15C0 6.716 6.716 0 15 0zm0 2C7.82 2 2 7.82 2 15s5.82 13 13 13 13-5.82 13-13S22.18 2 15 2zm6.713 5.636l1.446 1.446-6.508 6.509 6.508 6.509-1.446 1.446-6.508-6.509-6.51 6.509L7.25 22.1l6.509-6.509-6.51-6.509 1.447-1.446 6.509 6.509 6.508-6.509z"
              />
            </svg>
          </div>
          <div
            class="emotion-13"
          />
        </div>
      </button>
      <button
        class="emotion-2"
      >
        <div
          aria-busy="false"
          aria-live="polite"
          class="emotion-10"
          role="alert"
        >
          <div
            class="emotion-11"
          >
            <svg
              class="emotion-12"
              height="20"
              viewBox="0 0 30 30"
              width="20"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M15 0c8.284 0 15 6.716 15 15 0 8.284-6.716 15-15 15-8.284 0-15-6.716-15-15C0 6.716 6.716 0 15 0zm0 2C7.82 2 2 7.82 2 15s5.82 13 13 13 13-5.82 13-13S22.18 2 15 2zm6.713 5.636l1.446 1.446-6.508 6.509 6.508 6.509-1.446 1.446-6.508-6.509-6.51 6.509L7.25 22.1l6.509-6.509-6.51-6.509 1.447-1.446 6.509 6.509 6.508-6.509z"
              />
            </svg>
          </div>
          <div
            class="emotion-13"
          />
        </div>
      </button>
      <button
        class="emotion-39"
      >
        Full Width
      </button>
      <button
        class="emotion-39"
      >
        Full Width Selected
      </button>
      <button
        class="emotion-41"
      >
        Full Width Interactive Styles
      </button>
      <button
        class="emotion-42"
      >
        Rounded Corners
      </button>
      <button
        class="emotion-42"
      >
        Rounded Corners Selected
      </button>
      <button
        class="emotion-44"
      >
        Rounded Corners Interative Styles
      </button>
      <button
        class="emotion-45"
      >
        Pill Shape
      </button>
      <button
        class="emotion-45"
      >
        Pill Shape Selected
      </button>
      <button
        class="emotion-47"
      >
        Pill Shape Interactive Styles
      </button>
      <button
        class="emotion-48"
      >
        Custom Styles
      </button>
      <button
        class="emotion-48"
      >
        Custom Styles
      </button>
      <button
        class="emotion-50"
      >
        Custom Styles
      </button>
      <button
        class="emotion-51"
      >
        XLarge Size
      </button>
      <button
        class="emotion-52"
      >
        Large Size
      </button>
      <button
        class="emotion-0"
      >
        Medium Size
      </button>
      <button
        class="emotion-54"
      >
        Small Size
      </button>
      <button
        class="emotion-0"
      >
        XSmall Size
      </button>
      <button
        class="emotion-0"
      >
        X-Large Viewport
      </button>
      <button
        class="emotion-0"
      >
        Small Viewport
      </button>
    </div>
  </div>
</div>
`;

exports[`<ComposableButtonBase /> Snapshots should match snapshots for gap 1`] = `
@keyframes animation-0 {
  0%, 80%, 100% {
    -webkit-transform: scale(0);
    -moz-transform: scale(0);
    -ms-transform: scale(0);
    transform: scale(0);
  }

  40% {
    -webkit-transform: scale(1);
    -moz-transform: scale(1);
    -ms-transform: scale(1);
    transform: scale(1);
  }
}

@keyframes animation-1 {
  0% {
    -webkit-transform: scale(1.0);
    -moz-transform: scale(1.0);
    -ms-transform: scale(1.0);
    transform: scale(1.0);
  }

  10% {
    -webkit-transform: scale(1.5);
    -moz-transform: scale(1.5);
    -ms-transform: scale(1.5);
    transform: scale(1.5);
  }

  30% {
    -webkit-transform: scale(1.0);
    -moz-transform: scale(1.0);
    -ms-transform: scale(1.0);
    transform: scale(1.0);
  }

  40% {
    -webkit-transform: translate(0.9375rem, 0);
    -moz-transform: translate(0.9375rem, 0);
    -ms-transform: translate(0.9375rem, 0);
    transform: translate(0.9375rem, 0);
  }

  50% {
    -webkit-transform: translate(0, 0);
    -moz-transform: translate(0, 0);
    -ms-transform: translate(0, 0);
    transform: translate(0, 0);
  }
}

@keyframes animation-2 {
  0% {
    opacity: 0;
    width: 0;
  }

  35% {
    width: 0;
  }

  50% {
    width: 100%;
  }

  60% {
    opacity: 0;
  }

  100% {
    opacity: 1;
  }
}

.emotion-0 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  display: inline-block;
  text-transform: none;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 1px solid transparent;
  border-radius: 0;
  font-size: 12px;
  font-weight: 500;
  letter-spacing: 2%;
  min-height: unset;
  max-height: auto;
  line-height: 1.125;
  padding: 12px;
  width: auto;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #000000;
  background-color: #FFFFFF;
  border-color: #FFFFFF;
}

.emotion-0:focus {
  outline: none;
}

.emotion-0>span {
  padding: 1px 0;
}

.emotion-2 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  display: inline-block;
  text-transform: none;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 1px solid transparent;
  border-radius: 0;
  font-size: 12px;
  font-weight: 500;
  letter-spacing: 2%;
  min-height: unset;
  max-height: auto;
  line-height: 1.125;
  padding: 12px;
  width: auto;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #000000;
  background-color: #FFFFFF;
  border-color: #FFFFFF;
}

.emotion-2:focus {
  outline: none;
}

.emotion-2>span {
  padding: 1px 0;
}

.emotion-2:hover,
.emotion-2:focus {
  color: #FFFFFF;
  background-color: #000000;
  border-color: #000000;
}

.emotion-2:active {
  color: #FFFFFF;
  background-color: #000000;
  border-color: #000000;
}

.emotion-4 {
  width: 100%;
  text-align: center;
}

.emotion-4>div {
  width: 1rem;
  height: 1rem;
  background-color: currentColor;
  margin: 0.1rem;
  border-radius: 50%;
  display: inline-block;
  -webkit-animation: animation-0 1.4s infinite ease-in-out both;
  animation: animation-0 1.4s infinite ease-in-out both;
}

.emotion-4 :nth-of-type(1) {
  -webkit-animation-delay: -0.32s;
  animation-delay: -0.32s;
}

.emotion-4 :nth-of-type(2) {
  -webkit-animation-delay: -0.16s;
  animation-delay: -0.16s;
}

.emotion-10 {
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-flex-flow: row;
  -webkit-flex-flow: row;
  -ms-flex-flow: row;
  flex-flow: row;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
}

.emotion-11 {
  -webkit-animation: animation-1 2s;
  animation: animation-1 2s;
  -webkit-animation-iteration-count: 1;
  animation-iteration-count: 1;
  -webkit-animation-delay: 0;
  animation-delay: 0;
  height: 1.25rem;
  width: 1.25rem;
  -webkit-align-self: center;
  -ms-flex-item-align: center;
  align-self: center;
}

.emotion-12 {
  fill: currentColor;
  display: block;
}

.emotion-13 {
  -webkit-animation: animation-2 2s;
  animation: animation-2 2s;
  -webkit-animation-iteration-count: 1;
  animation-iteration-count: 1;
  -webkit-animation-delay: 0;
  animation-delay: 0;
  -webkit-align-self: center;
  -ms-flex-item-align: center;
  align-self: center;
  padding-left: 0.625rem;
}

.emotion-39 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  display: inline-block;
  text-transform: none;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 1px solid transparent;
  border-radius: 0;
  font-size: 12px;
  font-weight: 500;
  letter-spacing: 2%;
  min-height: unset;
  max-height: auto;
  line-height: 1.125;
  padding: 12px;
  width: 100%;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #000000;
  background-color: #FFFFFF;
  border-color: #FFFFFF;
}

.emotion-39:focus {
  outline: none;
}

.emotion-39>span {
  padding: 1px 0;
}

.emotion-41 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  display: inline-block;
  text-transform: none;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 1px solid transparent;
  border-radius: 0;
  font-size: 12px;
  font-weight: 500;
  letter-spacing: 2%;
  min-height: unset;
  max-height: auto;
  line-height: 1.125;
  padding: 12px;
  width: 100%;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #000000;
  background-color: #FFFFFF;
  border-color: #FFFFFF;
}

.emotion-41:focus {
  outline: none;
}

.emotion-41>span {
  padding: 1px 0;
}

.emotion-41:hover,
.emotion-41:focus {
  color: #FFFFFF;
  background-color: #000000;
  border-color: #000000;
}

.emotion-41:active {
  color: #FFFFFF;
  background-color: #000000;
  border-color: #000000;
}

.emotion-42 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  display: inline-block;
  text-transform: none;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 1px solid transparent;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
  letter-spacing: 2%;
  min-height: unset;
  max-height: auto;
  line-height: 1.125;
  padding: 12px;
  width: auto;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #000000;
  background-color: #FFFFFF;
  border-color: #FFFFFF;
}

.emotion-42:focus {
  outline: none;
}

.emotion-42>span {
  padding: 1px 0;
}

.emotion-44 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  display: inline-block;
  text-transform: none;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 1px solid transparent;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
  letter-spacing: 2%;
  min-height: unset;
  max-height: auto;
  line-height: 1.125;
  padding: 12px;
  width: auto;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #000000;
  background-color: #FFFFFF;
  border-color: #FFFFFF;
}

.emotion-44:focus {
  outline: none;
}

.emotion-44>span {
  padding: 1px 0;
}

.emotion-44:hover,
.emotion-44:focus {
  color: #FFFFFF;
  background-color: #000000;
  border-color: #000000;
}

.emotion-44:active {
  color: #FFFFFF;
  background-color: #000000;
  border-color: #000000;
}

.emotion-45 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  display: inline-block;
  text-transform: none;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 1px solid transparent;
  border-radius: 99em;
  font-size: 12px;
  font-weight: 500;
  letter-spacing: 2%;
  min-height: unset;
  max-height: auto;
  line-height: 1.125;
  padding: 12px;
  width: auto;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #000000;
  background-color: #FFFFFF;
  border-color: #FFFFFF;
}

.emotion-45:focus {
  outline: none;
}

.emotion-45>span {
  padding: 1px 0;
}

.emotion-47 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  display: inline-block;
  text-transform: none;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 1px solid transparent;
  border-radius: 99em;
  font-size: 12px;
  font-weight: 500;
  letter-spacing: 2%;
  min-height: unset;
  max-height: auto;
  line-height: 1.125;
  padding: 12px;
  width: auto;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #000000;
  background-color: #FFFFFF;
  border-color: #FFFFFF;
}

.emotion-47:focus {
  outline: none;
}

.emotion-47>span {
  padding: 1px 0;
}

.emotion-47:hover,
.emotion-47:focus {
  color: #FFFFFF;
  background-color: #000000;
  border-color: #000000;
}

.emotion-47:active {
  color: #FFFFFF;
  background-color: #000000;
  border-color: #000000;
}

.emotion-48 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  display: inline-block;
  text-transform: none;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 1px solid transparent;
  border-radius: 0;
  font-size: 12px;
  font-weight: 500;
  letter-spacing: 2%;
  min-height: unset;
  max-height: auto;
  line-height: 1.125;
  padding: 12px;
  width: auto;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #000000;
  background-color: #FFFFFF;
  border-color: #FFFFFF;
  display: none;
}

.emotion-48:focus {
  outline: none;
}

.emotion-48>span {
  padding: 1px 0;
}

.emotion-50 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  display: inline-block;
  text-transform: none;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 1px solid transparent;
  border-radius: 0;
  font-size: 12px;
  font-weight: 500;
  letter-spacing: 2%;
  min-height: unset;
  max-height: auto;
  line-height: 1.125;
  padding: 12px;
  width: auto;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #000000;
  background-color: #FFFFFF;
  border-color: #FFFFFF;
  display: none;
}

.emotion-50:focus {
  outline: none;
}

.emotion-50>span {
  padding: 1px 0;
}

.emotion-50:hover,
.emotion-50:focus {
  color: #FFFFFF;
  background-color: #000000;
  border-color: #000000;
}

.emotion-50:active {
  color: #FFFFFF;
  background-color: #000000;
  border-color: #000000;
}

.emotion-51 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  display: inline-block;
  text-transform: none;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 1px solid transparent;
  border-radius: 0;
  font-size: 18px;
  font-weight: 500;
  letter-spacing: 2%;
  min-height: unset;
  max-height: auto;
  line-height: 1.0833333333333333;
  padding: 12px;
  width: auto;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #000000;
  background-color: #FFFFFF;
  border-color: #FFFFFF;
}

.emotion-51:focus {
  outline: none;
}

.emotion-51>span {
  padding: 1px 0;
}

.emotion-52 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  display: inline-block;
  text-transform: none;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 1px solid transparent;
  border-radius: 0;
  font-size: 16px;
  font-weight: 500;
  letter-spacing: 2%;
  min-height: unset;
  max-height: auto;
  line-height: 1.1;
  padding: 12px;
  width: auto;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #000000;
  background-color: #FFFFFF;
  border-color: #FFFFFF;
}

.emotion-52:focus {
  outline: none;
}

.emotion-52>span {
  padding: 1px 0;
}

.emotion-53 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  display: inline-block;
  text-transform: none;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 1px solid transparent;
  border-radius: 0;
  font-size: 14px;
  font-weight: 500;
  letter-spacing: 2%;
  min-height: unset;
  max-height: auto;
  line-height: 1.125;
  padding: 12px;
  width: auto;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #000000;
  background-color: #FFFFFF;
  border-color: #FFFFFF;
}

.emotion-53:focus {
  outline: none;
}

.emotion-53>span {
  padding: 1px 0;
}

.emotion-55 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  display: inline-block;
  text-transform: none;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 1px solid transparent;
  border-radius: 0;
  font-size: 10px;
  font-weight: 500;
  letter-spacing: 2%;
  min-height: unset;
  max-height: auto;
  line-height: 1.3333333333333333;
  padding: 12px;
  width: auto;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #000000;
  background-color: #FFFFFF;
  border-color: #FFFFFF;
}

.emotion-55:focus {
  outline: none;
}

.emotion-55>span {
  padding: 1px 0;
}

.emotion-56 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  display: inline-block;
  text-transform: none;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 1px solid transparent;
  border-radius: 0;
  max-width: 300px;
  font-size: 12px;
  font-weight: 500;
  letter-spacing: 2%;
  min-height: unset;
  max-height: auto;
  line-height: 12px;
  padding: 12px;
  width: auto;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #000000;
  background-color: #FFFFFF;
  border-color: #FFFFFF;
}

.emotion-56:focus {
  outline: none;
}

.emotion-56>span {
  padding: 1px 0;
}

<div>
  <div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <button
        class="emotion-0"
      >
        Default
      </button>
      <button
        class="emotion-0"
      >
        Default Selected
      </button>
      <button
        class="emotion-2"
      >
        Default Interactive Styles
      </button>
      <button
        class="emotion-0"
      >
        <div
          aria-busy="true"
          aria-label="loading"
          aria-live="polite"
          class="emotion-4"
          role="alert"
        >
          <div />
          <div />
          <div />
        </div>
      </button>
      <button
        class="emotion-0"
      >
        <div
          aria-busy="true"
          aria-label="loading"
          aria-live="polite"
          class="emotion-4"
          role="alert"
        >
          <div />
          <div />
          <div />
        </div>
      </button>
      <button
        class="emotion-2"
      >
        <div
          aria-busy="true"
          aria-label="loading"
          aria-live="polite"
          class="emotion-4"
          role="alert"
        >
          <div />
          <div />
          <div />
        </div>
      </button>
      <button
        class="emotion-0"
      >
        <div
          aria-busy="false"
          aria-live="polite"
          class="emotion-10"
          role="alert"
        >
          <div
            class="emotion-11"
          >
            <svg
              class="emotion-12"
              height="20"
              viewBox="0 0 30 30"
              width="20"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M15 0c8.284 0 15 6.716 15 15 0 8.284-6.716 15-15 15-8.284 0-15-6.716-15-15C0 6.716 6.716 0 15 0zm0 2C7.82 2 2 7.82 2 15s5.82 13 13 13 13-5.82 13-13S22.18 2 15 2zm5.463 7.65l2.492 2.145-9.787 9.689-5.52-4.788 2.529-2.64 2.991 2.64 7.295-7.046z"
              />
            </svg>
          </div>
          <div
            class="emotion-13"
          />
        </div>
      </button>
      <button
        class="emotion-0"
      >
        <div
          aria-busy="false"
          aria-live="polite"
          class="emotion-10"
          role="alert"
        >
          <div
            class="emotion-11"
          >
            <svg
              class="emotion-12"
              height="20"
              viewBox="0 0 30 30"
              width="20"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M15 0c8.284 0 15 6.716 15 15 0 8.284-6.716 15-15 15-8.284 0-15-6.716-15-15C0 6.716 6.716 0 15 0zm0 2C7.82 2 2 7.82 2 15s5.82 13 13 13 13-5.82 13-13S22.18 2 15 2zm5.463 7.65l2.492 2.145-9.787 9.689-5.52-4.788 2.529-2.64 2.991 2.64 7.295-7.046z"
              />
            </svg>
          </div>
          <div
            class="emotion-13"
          />
        </div>
      </button>
      <button
        class="emotion-2"
      >
        <div
          aria-busy="false"
          aria-live="polite"
          class="emotion-10"
          role="alert"
        >
          <div
            class="emotion-11"
          >
            <svg
              class="emotion-12"
              height="20"
              viewBox="0 0 30 30"
              width="20"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M15 0c8.284 0 15 6.716 15 15 0 8.284-6.716 15-15 15-8.284 0-15-6.716-15-15C0 6.716 6.716 0 15 0zm0 2C7.82 2 2 7.82 2 15s5.82 13 13 13 13-5.82 13-13S22.18 2 15 2zm5.463 7.65l2.492 2.145-9.787 9.689-5.52-4.788 2.529-2.64 2.991 2.64 7.295-7.046z"
              />
            </svg>
          </div>
          <div
            class="emotion-13"
          />
        </div>
      </button>
      <button
        class="emotion-0"
      >
        <div
          aria-busy="false"
          aria-live="polite"
          class="emotion-10"
          role="alert"
        >
          <div
            class="emotion-11"
          >
            <svg
              class="emotion-12"
              height="20"
              viewBox="0 0 30 30"
              width="20"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M15 0c8.284 0 15 6.716 15 15 0 8.284-6.716 15-15 15-8.284 0-15-6.716-15-15C0 6.716 6.716 0 15 0zm0 2C7.82 2 2 7.82 2 15s5.82 13 13 13 13-5.82 13-13S22.18 2 15 2zm6.713 5.636l1.446 1.446-6.508 6.509 6.508 6.509-1.446 1.446-6.508-6.509-6.51 6.509L7.25 22.1l6.509-6.509-6.51-6.509 1.447-1.446 6.509 6.509 6.508-6.509z"
              />
            </svg>
          </div>
          <div
            class="emotion-13"
          />
        </div>
      </button>
      <button
        class="emotion-0"
      >
        <div
          aria-busy="false"
          aria-live="polite"
          class="emotion-10"
          role="alert"
        >
          <div
            class="emotion-11"
          >
            <svg
              class="emotion-12"
              height="20"
              viewBox="0 0 30 30"
              width="20"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M15 0c8.284 0 15 6.716 15 15 0 8.284-6.716 15-15 15-8.284 0-15-6.716-15-15C0 6.716 6.716 0 15 0zm0 2C7.82 2 2 7.82 2 15s5.82 13 13 13 13-5.82 13-13S22.18 2 15 2zm6.713 5.636l1.446 1.446-6.508 6.509 6.508 6.509-1.446 1.446-6.508-6.509-6.51 6.509L7.25 22.1l6.509-6.509-6.51-6.509 1.447-1.446 6.509 6.509 6.508-6.509z"
              />
            </svg>
          </div>
          <div
            class="emotion-13"
          />
        </div>
      </button>
      <button
        class="emotion-2"
      >
        <div
          aria-busy="false"
          aria-live="polite"
          class="emotion-10"
          role="alert"
        >
          <div
            class="emotion-11"
          >
            <svg
              class="emotion-12"
              height="20"
              viewBox="0 0 30 30"
              width="20"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M15 0c8.284 0 15 6.716 15 15 0 8.284-6.716 15-15 15-8.284 0-15-6.716-15-15C0 6.716 6.716 0 15 0zm0 2C7.82 2 2 7.82 2 15s5.82 13 13 13 13-5.82 13-13S22.18 2 15 2zm6.713 5.636l1.446 1.446-6.508 6.509 6.508 6.509-1.446 1.446-6.508-6.509-6.51 6.509L7.25 22.1l6.509-6.509-6.51-6.509 1.447-1.446 6.509 6.509 6.508-6.509z"
              />
            </svg>
          </div>
          <div
            class="emotion-13"
          />
        </div>
      </button>
      <button
        class="emotion-39"
      >
        Full Width
      </button>
      <button
        class="emotion-39"
      >
        Full Width Selected
      </button>
      <button
        class="emotion-41"
      >
        Full Width Interactive Styles
      </button>
      <button
        class="emotion-42"
      >
        Rounded Corners
      </button>
      <button
        class="emotion-42"
      >
        Rounded Corners Selected
      </button>
      <button
        class="emotion-44"
      >
        Rounded Corners Interative Styles
      </button>
      <button
        class="emotion-45"
      >
        Pill Shape
      </button>
      <button
        class="emotion-45"
      >
        Pill Shape Selected
      </button>
      <button
        class="emotion-47"
      >
        Pill Shape Interactive Styles
      </button>
      <button
        class="emotion-48"
      >
        Custom Styles
      </button>
      <button
        class="emotion-48"
      >
        Custom Styles
      </button>
      <button
        class="emotion-50"
      >
        Custom Styles
      </button>
      <button
        class="emotion-51"
      >
        XLarge Size
      </button>
      <button
        class="emotion-52"
      >
        Large Size
      </button>
      <button
        class="emotion-53"
      >
        Medium Size
      </button>
      <button
        class="emotion-0"
      >
        Small Size
      </button>
      <button
        class="emotion-55"
      >
        XSmall Size
      </button>
      <button
        class="emotion-56"
      >
        X-Large Viewport
      </button>
      <button
        class="emotion-0"
      >
        Small Viewport
      </button>
    </div>
  </div>
</div>
`;

exports[`<ComposableButtonBase /> Snapshots should match snapshots for gap with feature flags 1`] = `
@keyframes animation-0 {
  0%, 80%, 100% {
    -webkit-transform: scale(0);
    -moz-transform: scale(0);
    -ms-transform: scale(0);
    transform: scale(0);
  }

  40% {
    -webkit-transform: scale(1);
    -moz-transform: scale(1);
    -ms-transform: scale(1);
    transform: scale(1);
  }
}

@keyframes animation-1 {
  0% {
    -webkit-transform: scale(1.0);
    -moz-transform: scale(1.0);
    -ms-transform: scale(1.0);
    transform: scale(1.0);
  }

  10% {
    -webkit-transform: scale(1.5);
    -moz-transform: scale(1.5);
    -ms-transform: scale(1.5);
    transform: scale(1.5);
  }

  30% {
    -webkit-transform: scale(1.0);
    -moz-transform: scale(1.0);
    -ms-transform: scale(1.0);
    transform: scale(1.0);
  }

  40% {
    -webkit-transform: translate(0.9375rem, 0);
    -moz-transform: translate(0.9375rem, 0);
    -ms-transform: translate(0.9375rem, 0);
    transform: translate(0.9375rem, 0);
  }

  50% {
    -webkit-transform: translate(0, 0);
    -moz-transform: translate(0, 0);
    -ms-transform: translate(0, 0);
    transform: translate(0, 0);
  }
}

@keyframes animation-2 {
  0% {
    opacity: 0;
    width: 0;
  }

  35% {
    width: 0;
  }

  50% {
    width: 100%;
  }

  60% {
    opacity: 0;
  }

  100% {
    opacity: 1;
  }
}

.emotion-0 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  display: inline-block;
  text-transform: none;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 1px solid transparent;
  border-radius: 0;
  font-size: 12px;
  font-weight: 500;
  letter-spacing: 2%;
  min-height: unset;
  max-height: auto;
  line-height: 1.125;
  padding: 12px;
  width: auto;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #000000;
  background-color: #FFFFFF;
  border-color: #FFFFFF;
}

.emotion-0:focus {
  outline: none;
}

.emotion-0>span {
  padding: 1px 0;
}

.emotion-2 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  display: inline-block;
  text-transform: none;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 1px solid transparent;
  border-radius: 0;
  font-size: 12px;
  font-weight: 500;
  letter-spacing: 2%;
  min-height: unset;
  max-height: auto;
  line-height: 1.125;
  padding: 12px;
  width: auto;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #000000;
  background-color: #FFFFFF;
  border-color: #FFFFFF;
}

.emotion-2:focus {
  outline: none;
}

.emotion-2>span {
  padding: 1px 0;
}

.emotion-2:hover,
.emotion-2:focus {
  color: #FFFFFF;
  background-color: #000000;
  border-color: #000000;
}

.emotion-2:active {
  color: #FFFFFF;
  background-color: #000000;
  border-color: #000000;
}

.emotion-4 {
  width: 100%;
  text-align: center;
}

.emotion-4>div {
  width: 1rem;
  height: 1rem;
  background-color: currentColor;
  margin: 0.1rem;
  border-radius: 50%;
  display: inline-block;
  -webkit-animation: animation-0 1.4s infinite ease-in-out both;
  animation: animation-0 1.4s infinite ease-in-out both;
}

.emotion-4 :nth-of-type(1) {
  -webkit-animation-delay: -0.32s;
  animation-delay: -0.32s;
}

.emotion-4 :nth-of-type(2) {
  -webkit-animation-delay: -0.16s;
  animation-delay: -0.16s;
}

.emotion-10 {
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-flex-flow: row;
  -webkit-flex-flow: row;
  -ms-flex-flow: row;
  flex-flow: row;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
}

.emotion-11 {
  -webkit-animation: animation-1 2s;
  animation: animation-1 2s;
  -webkit-animation-iteration-count: 1;
  animation-iteration-count: 1;
  -webkit-animation-delay: 0;
  animation-delay: 0;
  height: 1.25rem;
  width: 1.25rem;
  -webkit-align-self: center;
  -ms-flex-item-align: center;
  align-self: center;
}

.emotion-12 {
  fill: currentColor;
  display: block;
}

.emotion-13 {
  -webkit-animation: animation-2 2s;
  animation: animation-2 2s;
  -webkit-animation-iteration-count: 1;
  animation-iteration-count: 1;
  -webkit-animation-delay: 0;
  animation-delay: 0;
  -webkit-align-self: center;
  -ms-flex-item-align: center;
  align-self: center;
  padding-left: 0.625rem;
}

.emotion-39 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  display: inline-block;
  text-transform: none;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 1px solid transparent;
  border-radius: 0;
  font-size: 12px;
  font-weight: 500;
  letter-spacing: 2%;
  min-height: unset;
  max-height: auto;
  line-height: 1.125;
  padding: 12px;
  width: 100%;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #000000;
  background-color: #FFFFFF;
  border-color: #FFFFFF;
}

.emotion-39:focus {
  outline: none;
}

.emotion-39>span {
  padding: 1px 0;
}

.emotion-41 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  display: inline-block;
  text-transform: none;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 1px solid transparent;
  border-radius: 0;
  font-size: 12px;
  font-weight: 500;
  letter-spacing: 2%;
  min-height: unset;
  max-height: auto;
  line-height: 1.125;
  padding: 12px;
  width: 100%;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #000000;
  background-color: #FFFFFF;
  border-color: #FFFFFF;
}

.emotion-41:focus {
  outline: none;
}

.emotion-41>span {
  padding: 1px 0;
}

.emotion-41:hover,
.emotion-41:focus {
  color: #FFFFFF;
  background-color: #000000;
  border-color: #000000;
}

.emotion-41:active {
  color: #FFFFFF;
  background-color: #000000;
  border-color: #000000;
}

.emotion-42 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  display: inline-block;
  text-transform: none;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 1px solid transparent;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
  letter-spacing: 2%;
  min-height: unset;
  max-height: auto;
  line-height: 1.125;
  padding: 12px;
  width: auto;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #000000;
  background-color: #FFFFFF;
  border-color: #FFFFFF;
}

.emotion-42:focus {
  outline: none;
}

.emotion-42>span {
  padding: 1px 0;
}

.emotion-44 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  display: inline-block;
  text-transform: none;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 1px solid transparent;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
  letter-spacing: 2%;
  min-height: unset;
  max-height: auto;
  line-height: 1.125;
  padding: 12px;
  width: auto;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #000000;
  background-color: #FFFFFF;
  border-color: #FFFFFF;
}

.emotion-44:focus {
  outline: none;
}

.emotion-44>span {
  padding: 1px 0;
}

.emotion-44:hover,
.emotion-44:focus {
  color: #FFFFFF;
  background-color: #000000;
  border-color: #000000;
}

.emotion-44:active {
  color: #FFFFFF;
  background-color: #000000;
  border-color: #000000;
}

.emotion-45 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  display: inline-block;
  text-transform: none;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 1px solid transparent;
  border-radius: 99em;
  font-size: 12px;
  font-weight: 500;
  letter-spacing: 2%;
  min-height: unset;
  max-height: auto;
  line-height: 1.125;
  padding: 12px;
  width: auto;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #000000;
  background-color: #FFFFFF;
  border-color: #FFFFFF;
}

.emotion-45:focus {
  outline: none;
}

.emotion-45>span {
  padding: 1px 0;
}

.emotion-47 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  display: inline-block;
  text-transform: none;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 1px solid transparent;
  border-radius: 99em;
  font-size: 12px;
  font-weight: 500;
  letter-spacing: 2%;
  min-height: unset;
  max-height: auto;
  line-height: 1.125;
  padding: 12px;
  width: auto;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #000000;
  background-color: #FFFFFF;
  border-color: #FFFFFF;
}

.emotion-47:focus {
  outline: none;
}

.emotion-47>span {
  padding: 1px 0;
}

.emotion-47:hover,
.emotion-47:focus {
  color: #FFFFFF;
  background-color: #000000;
  border-color: #000000;
}

.emotion-47:active {
  color: #FFFFFF;
  background-color: #000000;
  border-color: #000000;
}

.emotion-48 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  display: inline-block;
  text-transform: none;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 1px solid transparent;
  border-radius: 0;
  font-size: 12px;
  font-weight: 500;
  letter-spacing: 2%;
  min-height: unset;
  max-height: auto;
  line-height: 1.125;
  padding: 12px;
  width: auto;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #000000;
  background-color: #FFFFFF;
  border-color: #FFFFFF;
  display: none;
}

.emotion-48:focus {
  outline: none;
}

.emotion-48>span {
  padding: 1px 0;
}

.emotion-50 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  display: inline-block;
  text-transform: none;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 1px solid transparent;
  border-radius: 0;
  font-size: 12px;
  font-weight: 500;
  letter-spacing: 2%;
  min-height: unset;
  max-height: auto;
  line-height: 1.125;
  padding: 12px;
  width: auto;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #000000;
  background-color: #FFFFFF;
  border-color: #FFFFFF;
  display: none;
}

.emotion-50:focus {
  outline: none;
}

.emotion-50>span {
  padding: 1px 0;
}

.emotion-50:hover,
.emotion-50:focus {
  color: #FFFFFF;
  background-color: #000000;
  border-color: #000000;
}

.emotion-50:active {
  color: #FFFFFF;
  background-color: #000000;
  border-color: #000000;
}

.emotion-51 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  display: inline-block;
  text-transform: none;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 1px solid transparent;
  border-radius: 0;
  font-size: 18px;
  font-weight: 500;
  letter-spacing: 2%;
  min-height: unset;
  max-height: auto;
  line-height: 1.0833333333333333;
  padding: 12px;
  width: auto;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #000000;
  background-color: #FFFFFF;
  border-color: #FFFFFF;
}

.emotion-51:focus {
  outline: none;
}

.emotion-51>span {
  padding: 1px 0;
}

.emotion-52 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  display: inline-block;
  text-transform: none;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 1px solid transparent;
  border-radius: 0;
  font-size: 16px;
  font-weight: 500;
  letter-spacing: 2%;
  min-height: unset;
  max-height: auto;
  line-height: 1.1;
  padding: 12px;
  width: auto;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #000000;
  background-color: #FFFFFF;
  border-color: #FFFFFF;
}

.emotion-52:focus {
  outline: none;
}

.emotion-52>span {
  padding: 1px 0;
}

.emotion-53 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  display: inline-block;
  text-transform: none;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 1px solid transparent;
  border-radius: 0;
  font-size: 14px;
  font-weight: 500;
  letter-spacing: 2%;
  min-height: unset;
  max-height: auto;
  line-height: 1.125;
  padding: 12px;
  width: auto;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #000000;
  background-color: #FFFFFF;
  border-color: #FFFFFF;
}

.emotion-53:focus {
  outline: none;
}

.emotion-53>span {
  padding: 1px 0;
}

.emotion-55 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  display: inline-block;
  text-transform: none;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 1px solid transparent;
  border-radius: 0;
  font-size: 10px;
  font-weight: 500;
  letter-spacing: 2%;
  min-height: unset;
  max-height: auto;
  line-height: 1.3333333333333333;
  padding: 12px;
  width: auto;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #000000;
  background-color: #FFFFFF;
  border-color: #FFFFFF;
}

.emotion-55:focus {
  outline: none;
}

.emotion-55>span {
  padding: 1px 0;
}

.emotion-56 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  display: inline-block;
  text-transform: none;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 1px solid transparent;
  border-radius: 0;
  max-width: 300px;
  font-size: 12px;
  font-weight: 500;
  letter-spacing: 2%;
  min-height: unset;
  max-height: auto;
  line-height: 12px;
  padding: 12px;
  width: auto;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #000000;
  background-color: #FFFFFF;
  border-color: #FFFFFF;
}

.emotion-56:focus {
  outline: none;
}

.emotion-56>span {
  padding: 1px 0;
}

<div>
  <div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <button
        class="emotion-0"
      >
        Default
      </button>
      <button
        class="emotion-0"
      >
        Default Selected
      </button>
      <button
        class="emotion-2"
      >
        Default Interactive Styles
      </button>
      <button
        class="emotion-0"
      >
        <div
          aria-busy="true"
          aria-label="loading"
          aria-live="polite"
          class="emotion-4"
          role="alert"
        >
          <div />
          <div />
          <div />
        </div>
      </button>
      <button
        class="emotion-0"
      >
        <div
          aria-busy="true"
          aria-label="loading"
          aria-live="polite"
          class="emotion-4"
          role="alert"
        >
          <div />
          <div />
          <div />
        </div>
      </button>
      <button
        class="emotion-2"
      >
        <div
          aria-busy="true"
          aria-label="loading"
          aria-live="polite"
          class="emotion-4"
          role="alert"
        >
          <div />
          <div />
          <div />
        </div>
      </button>
      <button
        class="emotion-0"
      >
        <div
          aria-busy="false"
          aria-live="polite"
          class="emotion-10"
          role="alert"
        >
          <div
            class="emotion-11"
          >
            <svg
              class="emotion-12"
              height="20"
              viewBox="0 0 30 30"
              width="20"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M15 0c8.284 0 15 6.716 15 15 0 8.284-6.716 15-15 15-8.284 0-15-6.716-15-15C0 6.716 6.716 0 15 0zm0 2C7.82 2 2 7.82 2 15s5.82 13 13 13 13-5.82 13-13S22.18 2 15 2zm5.463 7.65l2.492 2.145-9.787 9.689-5.52-4.788 2.529-2.64 2.991 2.64 7.295-7.046z"
              />
            </svg>
          </div>
          <div
            class="emotion-13"
          />
        </div>
      </button>
      <button
        class="emotion-0"
      >
        <div
          aria-busy="false"
          aria-live="polite"
          class="emotion-10"
          role="alert"
        >
          <div
            class="emotion-11"
          >
            <svg
              class="emotion-12"
              height="20"
              viewBox="0 0 30 30"
              width="20"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M15 0c8.284 0 15 6.716 15 15 0 8.284-6.716 15-15 15-8.284 0-15-6.716-15-15C0 6.716 6.716 0 15 0zm0 2C7.82 2 2 7.82 2 15s5.82 13 13 13 13-5.82 13-13S22.18 2 15 2zm5.463 7.65l2.492 2.145-9.787 9.689-5.52-4.788 2.529-2.64 2.991 2.64 7.295-7.046z"
              />
            </svg>
          </div>
          <div
            class="emotion-13"
          />
        </div>
      </button>
      <button
        class="emotion-2"
      >
        <div
          aria-busy="false"
          aria-live="polite"
          class="emotion-10"
          role="alert"
        >
          <div
            class="emotion-11"
          >
            <svg
              class="emotion-12"
              height="20"
              viewBox="0 0 30 30"
              width="20"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M15 0c8.284 0 15 6.716 15 15 0 8.284-6.716 15-15 15-8.284 0-15-6.716-15-15C0 6.716 6.716 0 15 0zm0 2C7.82 2 2 7.82 2 15s5.82 13 13 13 13-5.82 13-13S22.18 2 15 2zm5.463 7.65l2.492 2.145-9.787 9.689-5.52-4.788 2.529-2.64 2.991 2.64 7.295-7.046z"
              />
            </svg>
          </div>
          <div
            class="emotion-13"
          />
        </div>
      </button>
      <button
        class="emotion-0"
      >
        <div
          aria-busy="false"
          aria-live="polite"
          class="emotion-10"
          role="alert"
        >
          <div
            class="emotion-11"
          >
            <svg
              class="emotion-12"
              height="20"
              viewBox="0 0 30 30"
              width="20"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M15 0c8.284 0 15 6.716 15 15 0 8.284-6.716 15-15 15-8.284 0-15-6.716-15-15C0 6.716 6.716 0 15 0zm0 2C7.82 2 2 7.82 2 15s5.82 13 13 13 13-5.82 13-13S22.18 2 15 2zm6.713 5.636l1.446 1.446-6.508 6.509 6.508 6.509-1.446 1.446-6.508-6.509-6.51 6.509L7.25 22.1l6.509-6.509-6.51-6.509 1.447-1.446 6.509 6.509 6.508-6.509z"
              />
            </svg>
          </div>
          <div
            class="emotion-13"
          />
        </div>
      </button>
      <button
        class="emotion-0"
      >
        <div
          aria-busy="false"
          aria-live="polite"
          class="emotion-10"
          role="alert"
        >
          <div
            class="emotion-11"
          >
            <svg
              class="emotion-12"
              height="20"
              viewBox="0 0 30 30"
              width="20"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M15 0c8.284 0 15 6.716 15 15 0 8.284-6.716 15-15 15-8.284 0-15-6.716-15-15C0 6.716 6.716 0 15 0zm0 2C7.82 2 2 7.82 2 15s5.82 13 13 13 13-5.82 13-13S22.18 2 15 2zm6.713 5.636l1.446 1.446-6.508 6.509 6.508 6.509-1.446 1.446-6.508-6.509-6.51 6.509L7.25 22.1l6.509-6.509-6.51-6.509 1.447-1.446 6.509 6.509 6.508-6.509z"
              />
            </svg>
          </div>
          <div
            class="emotion-13"
          />
        </div>
      </button>
      <button
        class="emotion-2"
      >
        <div
          aria-busy="false"
          aria-live="polite"
          class="emotion-10"
          role="alert"
        >
          <div
            class="emotion-11"
          >
            <svg
              class="emotion-12"
              height="20"
              viewBox="0 0 30 30"
              width="20"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M15 0c8.284 0 15 6.716 15 15 0 8.284-6.716 15-15 15-8.284 0-15-6.716-15-15C0 6.716 6.716 0 15 0zm0 2C7.82 2 2 7.82 2 15s5.82 13 13 13 13-5.82 13-13S22.18 2 15 2zm6.713 5.636l1.446 1.446-6.508 6.509 6.508 6.509-1.446 1.446-6.508-6.509-6.51 6.509L7.25 22.1l6.509-6.509-6.51-6.509 1.447-1.446 6.509 6.509 6.508-6.509z"
              />
            </svg>
          </div>
          <div
            class="emotion-13"
          />
        </div>
      </button>
      <button
        class="emotion-39"
      >
        Full Width
      </button>
      <button
        class="emotion-39"
      >
        Full Width Selected
      </button>
      <button
        class="emotion-41"
      >
        Full Width Interactive Styles
      </button>
      <button
        class="emotion-42"
      >
        Rounded Corners
      </button>
      <button
        class="emotion-42"
      >
        Rounded Corners Selected
      </button>
      <button
        class="emotion-44"
      >
        Rounded Corners Interative Styles
      </button>
      <button
        class="emotion-45"
      >
        Pill Shape
      </button>
      <button
        class="emotion-45"
      >
        Pill Shape Selected
      </button>
      <button
        class="emotion-47"
      >
        Pill Shape Interactive Styles
      </button>
      <button
        class="emotion-48"
      >
        Custom Styles
      </button>
      <button
        class="emotion-48"
      >
        Custom Styles
      </button>
      <button
        class="emotion-50"
      >
        Custom Styles
      </button>
      <button
        class="emotion-51"
      >
        XLarge Size
      </button>
      <button
        class="emotion-52"
      >
        Large Size
      </button>
      <button
        class="emotion-53"
      >
        Medium Size
      </button>
      <button
        class="emotion-0"
      >
        Small Size
      </button>
      <button
        class="emotion-55"
      >
        XSmall Size
      </button>
      <button
        class="emotion-56"
      >
        X-Large Viewport
      </button>
      <button
        class="emotion-0"
      >
        Small Viewport
      </button>
    </div>
  </div>
</div>
`;

exports[`<ComposableButtonBase /> Snapshots should match snapshots for gapfs 1`] = `
@keyframes animation-0 {
  0%, 80%, 100% {
    -webkit-transform: scale(0);
    -moz-transform: scale(0);
    -ms-transform: scale(0);
    transform: scale(0);
  }

  40% {
    -webkit-transform: scale(1);
    -moz-transform: scale(1);
    -ms-transform: scale(1);
    transform: scale(1);
  }
}

@keyframes animation-1 {
  0% {
    -webkit-transform: scale(1.0);
    -moz-transform: scale(1.0);
    -ms-transform: scale(1.0);
    transform: scale(1.0);
  }

  10% {
    -webkit-transform: scale(1.5);
    -moz-transform: scale(1.5);
    -ms-transform: scale(1.5);
    transform: scale(1.5);
  }

  30% {
    -webkit-transform: scale(1.0);
    -moz-transform: scale(1.0);
    -ms-transform: scale(1.0);
    transform: scale(1.0);
  }

  40% {
    -webkit-transform: translate(0.9375rem, 0);
    -moz-transform: translate(0.9375rem, 0);
    -ms-transform: translate(0.9375rem, 0);
    transform: translate(0.9375rem, 0);
  }

  50% {
    -webkit-transform: translate(0, 0);
    -moz-transform: translate(0, 0);
    -ms-transform: translate(0, 0);
    transform: translate(0, 0);
  }
}

@keyframes animation-2 {
  0% {
    opacity: 0;
    width: 0;
  }

  35% {
    width: 0;
  }

  50% {
    width: 100%;
  }

  60% {
    opacity: 0;
  }

  100% {
    opacity: 1;
  }
}

.emotion-0 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  display: inline-block;
  text-transform: none;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 1px solid transparent;
  border-radius: 0;
  font-size: 12px;
  font-weight: 500;
  letter-spacing: 2%;
  min-height: unset;
  max-height: auto;
  line-height: 1.125;
  padding: 12px;
  width: auto;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #000000;
  background-color: #FFFFFF;
  border-color: #FFFFFF;
}

.emotion-0:focus {
  outline: none;
}

.emotion-0>span {
  padding: 1px 0;
}

.emotion-2 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  display: inline-block;
  text-transform: none;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 1px solid transparent;
  border-radius: 0;
  font-size: 12px;
  font-weight: 500;
  letter-spacing: 2%;
  min-height: unset;
  max-height: auto;
  line-height: 1.125;
  padding: 12px;
  width: auto;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #000000;
  background-color: #FFFFFF;
  border-color: #FFFFFF;
}

.emotion-2:focus {
  outline: none;
}

.emotion-2>span {
  padding: 1px 0;
}

.emotion-2:hover,
.emotion-2:focus {
  color: #FFFFFF;
  background-color: #000000;
  border-color: #000000;
}

.emotion-2:active {
  color: #FFFFFF;
  background-color: #000000;
  border-color: #000000;
}

.emotion-4 {
  width: 100%;
  text-align: center;
}

.emotion-4>div {
  width: 1rem;
  height: 1rem;
  background-color: currentColor;
  margin: 0.1rem;
  border-radius: 50%;
  display: inline-block;
  -webkit-animation: animation-0 1.4s infinite ease-in-out both;
  animation: animation-0 1.4s infinite ease-in-out both;
}

.emotion-4 :nth-of-type(1) {
  -webkit-animation-delay: -0.32s;
  animation-delay: -0.32s;
}

.emotion-4 :nth-of-type(2) {
  -webkit-animation-delay: -0.16s;
  animation-delay: -0.16s;
}

.emotion-10 {
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-flex-flow: row;
  -webkit-flex-flow: row;
  -ms-flex-flow: row;
  flex-flow: row;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
}

.emotion-11 {
  -webkit-animation: animation-1 2s;
  animation: animation-1 2s;
  -webkit-animation-iteration-count: 1;
  animation-iteration-count: 1;
  -webkit-animation-delay: 0;
  animation-delay: 0;
  height: 1.25rem;
  width: 1.25rem;
  -webkit-align-self: center;
  -ms-flex-item-align: center;
  align-self: center;
}

.emotion-12 {
  fill: currentColor;
  display: block;
}

.emotion-13 {
  -webkit-animation: animation-2 2s;
  animation: animation-2 2s;
  -webkit-animation-iteration-count: 1;
  animation-iteration-count: 1;
  -webkit-animation-delay: 0;
  animation-delay: 0;
  -webkit-align-self: center;
  -ms-flex-item-align: center;
  align-self: center;
  padding-left: 0.625rem;
}

.emotion-39 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  display: inline-block;
  text-transform: none;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 1px solid transparent;
  border-radius: 0;
  font-size: 12px;
  font-weight: 500;
  letter-spacing: 2%;
  min-height: unset;
  max-height: auto;
  line-height: 1.125;
  padding: 12px;
  width: 100%;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #000000;
  background-color: #FFFFFF;
  border-color: #FFFFFF;
}

.emotion-39:focus {
  outline: none;
}

.emotion-39>span {
  padding: 1px 0;
}

.emotion-41 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  display: inline-block;
  text-transform: none;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 1px solid transparent;
  border-radius: 0;
  font-size: 12px;
  font-weight: 500;
  letter-spacing: 2%;
  min-height: unset;
  max-height: auto;
  line-height: 1.125;
  padding: 12px;
  width: 100%;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #000000;
  background-color: #FFFFFF;
  border-color: #FFFFFF;
}

.emotion-41:focus {
  outline: none;
}

.emotion-41>span {
  padding: 1px 0;
}

.emotion-41:hover,
.emotion-41:focus {
  color: #FFFFFF;
  background-color: #000000;
  border-color: #000000;
}

.emotion-41:active {
  color: #FFFFFF;
  background-color: #000000;
  border-color: #000000;
}

.emotion-42 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  display: inline-block;
  text-transform: none;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 1px solid transparent;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
  letter-spacing: 2%;
  min-height: unset;
  max-height: auto;
  line-height: 1.125;
  padding: 12px;
  width: auto;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #000000;
  background-color: #FFFFFF;
  border-color: #FFFFFF;
}

.emotion-42:focus {
  outline: none;
}

.emotion-42>span {
  padding: 1px 0;
}

.emotion-44 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  display: inline-block;
  text-transform: none;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 1px solid transparent;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
  letter-spacing: 2%;
  min-height: unset;
  max-height: auto;
  line-height: 1.125;
  padding: 12px;
  width: auto;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #000000;
  background-color: #FFFFFF;
  border-color: #FFFFFF;
}

.emotion-44:focus {
  outline: none;
}

.emotion-44>span {
  padding: 1px 0;
}

.emotion-44:hover,
.emotion-44:focus {
  color: #FFFFFF;
  background-color: #000000;
  border-color: #000000;
}

.emotion-44:active {
  color: #FFFFFF;
  background-color: #000000;
  border-color: #000000;
}

.emotion-45 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  display: inline-block;
  text-transform: none;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 1px solid transparent;
  border-radius: 99em;
  font-size: 12px;
  font-weight: 500;
  letter-spacing: 2%;
  min-height: unset;
  max-height: auto;
  line-height: 1.125;
  padding: 12px;
  width: auto;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #000000;
  background-color: #FFFFFF;
  border-color: #FFFFFF;
}

.emotion-45:focus {
  outline: none;
}

.emotion-45>span {
  padding: 1px 0;
}

.emotion-47 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  display: inline-block;
  text-transform: none;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 1px solid transparent;
  border-radius: 99em;
  font-size: 12px;
  font-weight: 500;
  letter-spacing: 2%;
  min-height: unset;
  max-height: auto;
  line-height: 1.125;
  padding: 12px;
  width: auto;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #000000;
  background-color: #FFFFFF;
  border-color: #FFFFFF;
}

.emotion-47:focus {
  outline: none;
}

.emotion-47>span {
  padding: 1px 0;
}

.emotion-47:hover,
.emotion-47:focus {
  color: #FFFFFF;
  background-color: #000000;
  border-color: #000000;
}

.emotion-47:active {
  color: #FFFFFF;
  background-color: #000000;
  border-color: #000000;
}

.emotion-48 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  display: inline-block;
  text-transform: none;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 1px solid transparent;
  border-radius: 0;
  font-size: 12px;
  font-weight: 500;
  letter-spacing: 2%;
  min-height: unset;
  max-height: auto;
  line-height: 1.125;
  padding: 12px;
  width: auto;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #000000;
  background-color: #FFFFFF;
  border-color: #FFFFFF;
  display: none;
}

.emotion-48:focus {
  outline: none;
}

.emotion-48>span {
  padding: 1px 0;
}

.emotion-50 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  display: inline-block;
  text-transform: none;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 1px solid transparent;
  border-radius: 0;
  font-size: 12px;
  font-weight: 500;
  letter-spacing: 2%;
  min-height: unset;
  max-height: auto;
  line-height: 1.125;
  padding: 12px;
  width: auto;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #000000;
  background-color: #FFFFFF;
  border-color: #FFFFFF;
  display: none;
}

.emotion-50:focus {
  outline: none;
}

.emotion-50>span {
  padding: 1px 0;
}

.emotion-50:hover,
.emotion-50:focus {
  color: #FFFFFF;
  background-color: #000000;
  border-color: #000000;
}

.emotion-50:active {
  color: #FFFFFF;
  background-color: #000000;
  border-color: #000000;
}

.emotion-51 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  display: inline-block;
  text-transform: none;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 1px solid transparent;
  border-radius: 0;
  font-size: 18px;
  font-weight: 500;
  letter-spacing: 2%;
  min-height: unset;
  max-height: auto;
  line-height: 1.0833333333333333;
  padding: 12px;
  width: auto;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #000000;
  background-color: #FFFFFF;
  border-color: #FFFFFF;
}

.emotion-51:focus {
  outline: none;
}

.emotion-51>span {
  padding: 1px 0;
}

.emotion-52 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  display: inline-block;
  text-transform: none;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 1px solid transparent;
  border-radius: 0;
  font-size: 16px;
  font-weight: 500;
  letter-spacing: 2%;
  min-height: unset;
  max-height: auto;
  line-height: 1.1;
  padding: 12px;
  width: auto;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #000000;
  background-color: #FFFFFF;
  border-color: #FFFFFF;
}

.emotion-52:focus {
  outline: none;
}

.emotion-52>span {
  padding: 1px 0;
}

.emotion-53 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  display: inline-block;
  text-transform: none;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 1px solid transparent;
  border-radius: 0;
  font-size: 14px;
  font-weight: 500;
  letter-spacing: 2%;
  min-height: unset;
  max-height: auto;
  line-height: 1.125;
  padding: 12px;
  width: auto;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #000000;
  background-color: #FFFFFF;
  border-color: #FFFFFF;
}

.emotion-53:focus {
  outline: none;
}

.emotion-53>span {
  padding: 1px 0;
}

.emotion-55 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  display: inline-block;
  text-transform: none;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 1px solid transparent;
  border-radius: 0;
  font-size: 10px;
  font-weight: 500;
  letter-spacing: 2%;
  min-height: unset;
  max-height: auto;
  line-height: 1.3333333333333333;
  padding: 12px;
  width: auto;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #000000;
  background-color: #FFFFFF;
  border-color: #FFFFFF;
}

.emotion-55:focus {
  outline: none;
}

.emotion-55>span {
  padding: 1px 0;
}

.emotion-56 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  display: inline-block;
  text-transform: none;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 1px solid transparent;
  border-radius: 0;
  max-width: 300px;
  font-size: 12px;
  font-weight: 500;
  letter-spacing: 2%;
  min-height: unset;
  max-height: auto;
  line-height: 12px;
  padding: 12px;
  width: auto;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #000000;
  background-color: #FFFFFF;
  border-color: #FFFFFF;
}

.emotion-56:focus {
  outline: none;
}

.emotion-56>span {
  padding: 1px 0;
}

<div>
  <div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <button
        class="emotion-0"
      >
        Default
      </button>
      <button
        class="emotion-0"
      >
        Default Selected
      </button>
      <button
        class="emotion-2"
      >
        Default Interactive Styles
      </button>
      <button
        class="emotion-0"
      >
        <div
          aria-busy="true"
          aria-label="loading"
          aria-live="polite"
          class="emotion-4"
          role="alert"
        >
          <div />
          <div />
          <div />
        </div>
      </button>
      <button
        class="emotion-0"
      >
        <div
          aria-busy="true"
          aria-label="loading"
          aria-live="polite"
          class="emotion-4"
          role="alert"
        >
          <div />
          <div />
          <div />
        </div>
      </button>
      <button
        class="emotion-2"
      >
        <div
          aria-busy="true"
          aria-label="loading"
          aria-live="polite"
          class="emotion-4"
          role="alert"
        >
          <div />
          <div />
          <div />
        </div>
      </button>
      <button
        class="emotion-0"
      >
        <div
          aria-busy="false"
          aria-live="polite"
          class="emotion-10"
          role="alert"
        >
          <div
            class="emotion-11"
          >
            <svg
              class="emotion-12"
              height="20"
              viewBox="0 0 30 30"
              width="20"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M15 0c8.284 0 15 6.716 15 15 0 8.284-6.716 15-15 15-8.284 0-15-6.716-15-15C0 6.716 6.716 0 15 0zm0 2C7.82 2 2 7.82 2 15s5.82 13 13 13 13-5.82 13-13S22.18 2 15 2zm5.463 7.65l2.492 2.145-9.787 9.689-5.52-4.788 2.529-2.64 2.991 2.64 7.295-7.046z"
              />
            </svg>
          </div>
          <div
            class="emotion-13"
          />
        </div>
      </button>
      <button
        class="emotion-0"
      >
        <div
          aria-busy="false"
          aria-live="polite"
          class="emotion-10"
          role="alert"
        >
          <div
            class="emotion-11"
          >
            <svg
              class="emotion-12"
              height="20"
              viewBox="0 0 30 30"
              width="20"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M15 0c8.284 0 15 6.716 15 15 0 8.284-6.716 15-15 15-8.284 0-15-6.716-15-15C0 6.716 6.716 0 15 0zm0 2C7.82 2 2 7.82 2 15s5.82 13 13 13 13-5.82 13-13S22.18 2 15 2zm5.463 7.65l2.492 2.145-9.787 9.689-5.52-4.788 2.529-2.64 2.991 2.64 7.295-7.046z"
              />
            </svg>
          </div>
          <div
            class="emotion-13"
          />
        </div>
      </button>
      <button
        class="emotion-2"
      >
        <div
          aria-busy="false"
          aria-live="polite"
          class="emotion-10"
          role="alert"
        >
          <div
            class="emotion-11"
          >
            <svg
              class="emotion-12"
              height="20"
              viewBox="0 0 30 30"
              width="20"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M15 0c8.284 0 15 6.716 15 15 0 8.284-6.716 15-15 15-8.284 0-15-6.716-15-15C0 6.716 6.716 0 15 0zm0 2C7.82 2 2 7.82 2 15s5.82 13 13 13 13-5.82 13-13S22.18 2 15 2zm5.463 7.65l2.492 2.145-9.787 9.689-5.52-4.788 2.529-2.64 2.991 2.64 7.295-7.046z"
              />
            </svg>
          </div>
          <div
            class="emotion-13"
          />
        </div>
      </button>
      <button
        class="emotion-0"
      >
        <div
          aria-busy="false"
          aria-live="polite"
          class="emotion-10"
          role="alert"
        >
          <div
            class="emotion-11"
          >
            <svg
              class="emotion-12"
              height="20"
              viewBox="0 0 30 30"
              width="20"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M15 0c8.284 0 15 6.716 15 15 0 8.284-6.716 15-15 15-8.284 0-15-6.716-15-15C0 6.716 6.716 0 15 0zm0 2C7.82 2 2 7.82 2 15s5.82 13 13 13 13-5.82 13-13S22.18 2 15 2zm6.713 5.636l1.446 1.446-6.508 6.509 6.508 6.509-1.446 1.446-6.508-6.509-6.51 6.509L7.25 22.1l6.509-6.509-6.51-6.509 1.447-1.446 6.509 6.509 6.508-6.509z"
              />
            </svg>
          </div>
          <div
            class="emotion-13"
          />
        </div>
      </button>
      <button
        class="emotion-0"
      >
        <div
          aria-busy="false"
          aria-live="polite"
          class="emotion-10"
          role="alert"
        >
          <div
            class="emotion-11"
          >
            <svg
              class="emotion-12"
              height="20"
              viewBox="0 0 30 30"
              width="20"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M15 0c8.284 0 15 6.716 15 15 0 8.284-6.716 15-15 15-8.284 0-15-6.716-15-15C0 6.716 6.716 0 15 0zm0 2C7.82 2 2 7.82 2 15s5.82 13 13 13 13-5.82 13-13S22.18 2 15 2zm6.713 5.636l1.446 1.446-6.508 6.509 6.508 6.509-1.446 1.446-6.508-6.509-6.51 6.509L7.25 22.1l6.509-6.509-6.51-6.509 1.447-1.446 6.509 6.509 6.508-6.509z"
              />
            </svg>
          </div>
          <div
            class="emotion-13"
          />
        </div>
      </button>
      <button
        class="emotion-2"
      >
        <div
          aria-busy="false"
          aria-live="polite"
          class="emotion-10"
          role="alert"
        >
          <div
            class="emotion-11"
          >
            <svg
              class="emotion-12"
              height="20"
              viewBox="0 0 30 30"
              width="20"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M15 0c8.284 0 15 6.716 15 15 0 8.284-6.716 15-15 15-8.284 0-15-6.716-15-15C0 6.716 6.716 0 15 0zm0 2C7.82 2 2 7.82 2 15s5.82 13 13 13 13-5.82 13-13S22.18 2 15 2zm6.713 5.636l1.446 1.446-6.508 6.509 6.508 6.509-1.446 1.446-6.508-6.509-6.51 6.509L7.25 22.1l6.509-6.509-6.51-6.509 1.447-1.446 6.509 6.509 6.508-6.509z"
              />
            </svg>
          </div>
          <div
            class="emotion-13"
          />
        </div>
      </button>
      <button
        class="emotion-39"
      >
        Full Width
      </button>
      <button
        class="emotion-39"
      >
        Full Width Selected
      </button>
      <button
        class="emotion-41"
      >
        Full Width Interactive Styles
      </button>
      <button
        class="emotion-42"
      >
        Rounded Corners
      </button>
      <button
        class="emotion-42"
      >
        Rounded Corners Selected
      </button>
      <button
        class="emotion-44"
      >
        Rounded Corners Interative Styles
      </button>
      <button
        class="emotion-45"
      >
        Pill Shape
      </button>
      <button
        class="emotion-45"
      >
        Pill Shape Selected
      </button>
      <button
        class="emotion-47"
      >
        Pill Shape Interactive Styles
      </button>
      <button
        class="emotion-48"
      >
        Custom Styles
      </button>
      <button
        class="emotion-48"
      >
        Custom Styles
      </button>
      <button
        class="emotion-50"
      >
        Custom Styles
      </button>
      <button
        class="emotion-51"
      >
        XLarge Size
      </button>
      <button
        class="emotion-52"
      >
        Large Size
      </button>
      <button
        class="emotion-53"
      >
        Medium Size
      </button>
      <button
        class="emotion-0"
      >
        Small Size
      </button>
      <button
        class="emotion-55"
      >
        XSmall Size
      </button>
      <button
        class="emotion-56"
      >
        X-Large Viewport
      </button>
      <button
        class="emotion-0"
      >
        Small Viewport
      </button>
    </div>
  </div>
</div>
`;

exports[`<ComposableButtonBase /> Snapshots should match snapshots for gapfs with feature flags 1`] = `
@keyframes animation-0 {
  0%, 80%, 100% {
    -webkit-transform: scale(0);
    -moz-transform: scale(0);
    -ms-transform: scale(0);
    transform: scale(0);
  }

  40% {
    -webkit-transform: scale(1);
    -moz-transform: scale(1);
    -ms-transform: scale(1);
    transform: scale(1);
  }
}

@keyframes animation-1 {
  0% {
    -webkit-transform: scale(1.0);
    -moz-transform: scale(1.0);
    -ms-transform: scale(1.0);
    transform: scale(1.0);
  }

  10% {
    -webkit-transform: scale(1.5);
    -moz-transform: scale(1.5);
    -ms-transform: scale(1.5);
    transform: scale(1.5);
  }

  30% {
    -webkit-transform: scale(1.0);
    -moz-transform: scale(1.0);
    -ms-transform: scale(1.0);
    transform: scale(1.0);
  }

  40% {
    -webkit-transform: translate(0.9375rem, 0);
    -moz-transform: translate(0.9375rem, 0);
    -ms-transform: translate(0.9375rem, 0);
    transform: translate(0.9375rem, 0);
  }

  50% {
    -webkit-transform: translate(0, 0);
    -moz-transform: translate(0, 0);
    -ms-transform: translate(0, 0);
    transform: translate(0, 0);
  }
}

@keyframes animation-2 {
  0% {
    opacity: 0;
    width: 0;
  }

  35% {
    width: 0;
  }

  50% {
    width: 100%;
  }

  60% {
    opacity: 0;
  }

  100% {
    opacity: 1;
  }
}

.emotion-0 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  display: inline-block;
  text-transform: none;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 1px solid transparent;
  border-radius: 0;
  font-size: 12px;
  font-weight: 500;
  letter-spacing: 2%;
  min-height: unset;
  max-height: auto;
  line-height: 1.125;
  padding: 12px;
  width: auto;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #000000;
  background-color: #FFFFFF;
  border-color: #FFFFFF;
}

.emotion-0:focus {
  outline: none;
}

.emotion-0>span {
  padding: 1px 0;
}

.emotion-2 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  display: inline-block;
  text-transform: none;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 1px solid transparent;
  border-radius: 0;
  font-size: 12px;
  font-weight: 500;
  letter-spacing: 2%;
  min-height: unset;
  max-height: auto;
  line-height: 1.125;
  padding: 12px;
  width: auto;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #000000;
  background-color: #FFFFFF;
  border-color: #FFFFFF;
}

.emotion-2:focus {
  outline: none;
}

.emotion-2>span {
  padding: 1px 0;
}

.emotion-2:hover,
.emotion-2:focus {
  color: #FFFFFF;
  background-color: #000000;
  border-color: #000000;
}

.emotion-2:active {
  color: #FFFFFF;
  background-color: #000000;
  border-color: #000000;
}

.emotion-4 {
  width: 100%;
  text-align: center;
}

.emotion-4>div {
  width: 1rem;
  height: 1rem;
  background-color: currentColor;
  margin: 0.1rem;
  border-radius: 50%;
  display: inline-block;
  -webkit-animation: animation-0 1.4s infinite ease-in-out both;
  animation: animation-0 1.4s infinite ease-in-out both;
}

.emotion-4 :nth-of-type(1) {
  -webkit-animation-delay: -0.32s;
  animation-delay: -0.32s;
}

.emotion-4 :nth-of-type(2) {
  -webkit-animation-delay: -0.16s;
  animation-delay: -0.16s;
}

.emotion-10 {
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-flex-flow: row;
  -webkit-flex-flow: row;
  -ms-flex-flow: row;
  flex-flow: row;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
}

.emotion-11 {
  -webkit-animation: animation-1 2s;
  animation: animation-1 2s;
  -webkit-animation-iteration-count: 1;
  animation-iteration-count: 1;
  -webkit-animation-delay: 0;
  animation-delay: 0;
  height: 1.25rem;
  width: 1.25rem;
  -webkit-align-self: center;
  -ms-flex-item-align: center;
  align-self: center;
}

.emotion-12 {
  fill: currentColor;
  display: block;
}

.emotion-13 {
  -webkit-animation: animation-2 2s;
  animation: animation-2 2s;
  -webkit-animation-iteration-count: 1;
  animation-iteration-count: 1;
  -webkit-animation-delay: 0;
  animation-delay: 0;
  -webkit-align-self: center;
  -ms-flex-item-align: center;
  align-self: center;
  padding-left: 0.625rem;
}

.emotion-39 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  display: inline-block;
  text-transform: none;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 1px solid transparent;
  border-radius: 0;
  font-size: 12px;
  font-weight: 500;
  letter-spacing: 2%;
  min-height: unset;
  max-height: auto;
  line-height: 1.125;
  padding: 12px;
  width: 100%;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #000000;
  background-color: #FFFFFF;
  border-color: #FFFFFF;
}

.emotion-39:focus {
  outline: none;
}

.emotion-39>span {
  padding: 1px 0;
}

.emotion-41 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  display: inline-block;
  text-transform: none;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 1px solid transparent;
  border-radius: 0;
  font-size: 12px;
  font-weight: 500;
  letter-spacing: 2%;
  min-height: unset;
  max-height: auto;
  line-height: 1.125;
  padding: 12px;
  width: 100%;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #000000;
  background-color: #FFFFFF;
  border-color: #FFFFFF;
}

.emotion-41:focus {
  outline: none;
}

.emotion-41>span {
  padding: 1px 0;
}

.emotion-41:hover,
.emotion-41:focus {
  color: #FFFFFF;
  background-color: #000000;
  border-color: #000000;
}

.emotion-41:active {
  color: #FFFFFF;
  background-color: #000000;
  border-color: #000000;
}

.emotion-42 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  display: inline-block;
  text-transform: none;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 1px solid transparent;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
  letter-spacing: 2%;
  min-height: unset;
  max-height: auto;
  line-height: 1.125;
  padding: 12px;
  width: auto;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #000000;
  background-color: #FFFFFF;
  border-color: #FFFFFF;
}

.emotion-42:focus {
  outline: none;
}

.emotion-42>span {
  padding: 1px 0;
}

.emotion-44 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  display: inline-block;
  text-transform: none;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 1px solid transparent;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
  letter-spacing: 2%;
  min-height: unset;
  max-height: auto;
  line-height: 1.125;
  padding: 12px;
  width: auto;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #000000;
  background-color: #FFFFFF;
  border-color: #FFFFFF;
}

.emotion-44:focus {
  outline: none;
}

.emotion-44>span {
  padding: 1px 0;
}

.emotion-44:hover,
.emotion-44:focus {
  color: #FFFFFF;
  background-color: #000000;
  border-color: #000000;
}

.emotion-44:active {
  color: #FFFFFF;
  background-color: #000000;
  border-color: #000000;
}

.emotion-45 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  display: inline-block;
  text-transform: none;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 1px solid transparent;
  border-radius: 99em;
  font-size: 12px;
  font-weight: 500;
  letter-spacing: 2%;
  min-height: unset;
  max-height: auto;
  line-height: 1.125;
  padding: 12px;
  width: auto;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #000000;
  background-color: #FFFFFF;
  border-color: #FFFFFF;
}

.emotion-45:focus {
  outline: none;
}

.emotion-45>span {
  padding: 1px 0;
}

.emotion-47 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  display: inline-block;
  text-transform: none;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 1px solid transparent;
  border-radius: 99em;
  font-size: 12px;
  font-weight: 500;
  letter-spacing: 2%;
  min-height: unset;
  max-height: auto;
  line-height: 1.125;
  padding: 12px;
  width: auto;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #000000;
  background-color: #FFFFFF;
  border-color: #FFFFFF;
}

.emotion-47:focus {
  outline: none;
}

.emotion-47>span {
  padding: 1px 0;
}

.emotion-47:hover,
.emotion-47:focus {
  color: #FFFFFF;
  background-color: #000000;
  border-color: #000000;
}

.emotion-47:active {
  color: #FFFFFF;
  background-color: #000000;
  border-color: #000000;
}

.emotion-48 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  display: inline-block;
  text-transform: none;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 1px solid transparent;
  border-radius: 0;
  font-size: 12px;
  font-weight: 500;
  letter-spacing: 2%;
  min-height: unset;
  max-height: auto;
  line-height: 1.125;
  padding: 12px;
  width: auto;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #000000;
  background-color: #FFFFFF;
  border-color: #FFFFFF;
  display: none;
}

.emotion-48:focus {
  outline: none;
}

.emotion-48>span {
  padding: 1px 0;
}

.emotion-50 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  display: inline-block;
  text-transform: none;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 1px solid transparent;
  border-radius: 0;
  font-size: 12px;
  font-weight: 500;
  letter-spacing: 2%;
  min-height: unset;
  max-height: auto;
  line-height: 1.125;
  padding: 12px;
  width: auto;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #000000;
  background-color: #FFFFFF;
  border-color: #FFFFFF;
  display: none;
}

.emotion-50:focus {
  outline: none;
}

.emotion-50>span {
  padding: 1px 0;
}

.emotion-50:hover,
.emotion-50:focus {
  color: #FFFFFF;
  background-color: #000000;
  border-color: #000000;
}

.emotion-50:active {
  color: #FFFFFF;
  background-color: #000000;
  border-color: #000000;
}

.emotion-51 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  display: inline-block;
  text-transform: none;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 1px solid transparent;
  border-radius: 0;
  font-size: 18px;
  font-weight: 500;
  letter-spacing: 2%;
  min-height: unset;
  max-height: auto;
  line-height: 1.0833333333333333;
  padding: 12px;
  width: auto;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #000000;
  background-color: #FFFFFF;
  border-color: #FFFFFF;
}

.emotion-51:focus {
  outline: none;
}

.emotion-51>span {
  padding: 1px 0;
}

.emotion-52 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  display: inline-block;
  text-transform: none;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 1px solid transparent;
  border-radius: 0;
  font-size: 16px;
  font-weight: 500;
  letter-spacing: 2%;
  min-height: unset;
  max-height: auto;
  line-height: 1.1;
  padding: 12px;
  width: auto;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #000000;
  background-color: #FFFFFF;
  border-color: #FFFFFF;
}

.emotion-52:focus {
  outline: none;
}

.emotion-52>span {
  padding: 1px 0;
}

.emotion-53 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  display: inline-block;
  text-transform: none;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 1px solid transparent;
  border-radius: 0;
  font-size: 14px;
  font-weight: 500;
  letter-spacing: 2%;
  min-height: unset;
  max-height: auto;
  line-height: 1.125;
  padding: 12px;
  width: auto;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #000000;
  background-color: #FFFFFF;
  border-color: #FFFFFF;
}

.emotion-53:focus {
  outline: none;
}

.emotion-53>span {
  padding: 1px 0;
}

.emotion-55 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  display: inline-block;
  text-transform: none;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 1px solid transparent;
  border-radius: 0;
  font-size: 10px;
  font-weight: 500;
  letter-spacing: 2%;
  min-height: unset;
  max-height: auto;
  line-height: 1.3333333333333333;
  padding: 12px;
  width: auto;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #000000;
  background-color: #FFFFFF;
  border-color: #FFFFFF;
}

.emotion-55:focus {
  outline: none;
}

.emotion-55>span {
  padding: 1px 0;
}

.emotion-56 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  display: inline-block;
  text-transform: none;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 1px solid transparent;
  border-radius: 0;
  max-width: 300px;
  font-size: 12px;
  font-weight: 500;
  letter-spacing: 2%;
  min-height: unset;
  max-height: auto;
  line-height: 12px;
  padding: 12px;
  width: auto;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #000000;
  background-color: #FFFFFF;
  border-color: #FFFFFF;
}

.emotion-56:focus {
  outline: none;
}

.emotion-56>span {
  padding: 1px 0;
}

<div>
  <div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <button
        class="emotion-0"
      >
        Default
      </button>
      <button
        class="emotion-0"
      >
        Default Selected
      </button>
      <button
        class="emotion-2"
      >
        Default Interactive Styles
      </button>
      <button
        class="emotion-0"
      >
        <div
          aria-busy="true"
          aria-label="loading"
          aria-live="polite"
          class="emotion-4"
          role="alert"
        >
          <div />
          <div />
          <div />
        </div>
      </button>
      <button
        class="emotion-0"
      >
        <div
          aria-busy="true"
          aria-label="loading"
          aria-live="polite"
          class="emotion-4"
          role="alert"
        >
          <div />
          <div />
          <div />
        </div>
      </button>
      <button
        class="emotion-2"
      >
        <div
          aria-busy="true"
          aria-label="loading"
          aria-live="polite"
          class="emotion-4"
          role="alert"
        >
          <div />
          <div />
          <div />
        </div>
      </button>
      <button
        class="emotion-0"
      >
        <div
          aria-busy="false"
          aria-live="polite"
          class="emotion-10"
          role="alert"
        >
          <div
            class="emotion-11"
          >
            <svg
              class="emotion-12"
              height="20"
              viewBox="0 0 30 30"
              width="20"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M15 0c8.284 0 15 6.716 15 15 0 8.284-6.716 15-15 15-8.284 0-15-6.716-15-15C0 6.716 6.716 0 15 0zm0 2C7.82 2 2 7.82 2 15s5.82 13 13 13 13-5.82 13-13S22.18 2 15 2zm5.463 7.65l2.492 2.145-9.787 9.689-5.52-4.788 2.529-2.64 2.991 2.64 7.295-7.046z"
              />
            </svg>
          </div>
          <div
            class="emotion-13"
          />
        </div>
      </button>
      <button
        class="emotion-0"
      >
        <div
          aria-busy="false"
          aria-live="polite"
          class="emotion-10"
          role="alert"
        >
          <div
            class="emotion-11"
          >
            <svg
              class="emotion-12"
              height="20"
              viewBox="0 0 30 30"
              width="20"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M15 0c8.284 0 15 6.716 15 15 0 8.284-6.716 15-15 15-8.284 0-15-6.716-15-15C0 6.716 6.716 0 15 0zm0 2C7.82 2 2 7.82 2 15s5.82 13 13 13 13-5.82 13-13S22.18 2 15 2zm5.463 7.65l2.492 2.145-9.787 9.689-5.52-4.788 2.529-2.64 2.991 2.64 7.295-7.046z"
              />
            </svg>
          </div>
          <div
            class="emotion-13"
          />
        </div>
      </button>
      <button
        class="emotion-2"
      >
        <div
          aria-busy="false"
          aria-live="polite"
          class="emotion-10"
          role="alert"
        >
          <div
            class="emotion-11"
          >
            <svg
              class="emotion-12"
              height="20"
              viewBox="0 0 30 30"
              width="20"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M15 0c8.284 0 15 6.716 15 15 0 8.284-6.716 15-15 15-8.284 0-15-6.716-15-15C0 6.716 6.716 0 15 0zm0 2C7.82 2 2 7.82 2 15s5.82 13 13 13 13-5.82 13-13S22.18 2 15 2zm5.463 7.65l2.492 2.145-9.787 9.689-5.52-4.788 2.529-2.64 2.991 2.64 7.295-7.046z"
              />
            </svg>
          </div>
          <div
            class="emotion-13"
          />
        </div>
      </button>
      <button
        class="emotion-0"
      >
        <div
          aria-busy="false"
          aria-live="polite"
          class="emotion-10"
          role="alert"
        >
          <div
            class="emotion-11"
          >
            <svg
              class="emotion-12"
              height="20"
              viewBox="0 0 30 30"
              width="20"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M15 0c8.284 0 15 6.716 15 15 0 8.284-6.716 15-15 15-8.284 0-15-6.716-15-15C0 6.716 6.716 0 15 0zm0 2C7.82 2 2 7.82 2 15s5.82 13 13 13 13-5.82 13-13S22.18 2 15 2zm6.713 5.636l1.446 1.446-6.508 6.509 6.508 6.509-1.446 1.446-6.508-6.509-6.51 6.509L7.25 22.1l6.509-6.509-6.51-6.509 1.447-1.446 6.509 6.509 6.508-6.509z"
              />
            </svg>
          </div>
          <div
            class="emotion-13"
          />
        </div>
      </button>
      <button
        class="emotion-0"
      >
        <div
          aria-busy="false"
          aria-live="polite"
          class="emotion-10"
          role="alert"
        >
          <div
            class="emotion-11"
          >
            <svg
              class="emotion-12"
              height="20"
              viewBox="0 0 30 30"
              width="20"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M15 0c8.284 0 15 6.716 15 15 0 8.284-6.716 15-15 15-8.284 0-15-6.716-15-15C0 6.716 6.716 0 15 0zm0 2C7.82 2 2 7.82 2 15s5.82 13 13 13 13-5.82 13-13S22.18 2 15 2zm6.713 5.636l1.446 1.446-6.508 6.509 6.508 6.509-1.446 1.446-6.508-6.509-6.51 6.509L7.25 22.1l6.509-6.509-6.51-6.509 1.447-1.446 6.509 6.509 6.508-6.509z"
              />
            </svg>
          </div>
          <div
            class="emotion-13"
          />
        </div>
      </button>
      <button
        class="emotion-2"
      >
        <div
          aria-busy="false"
          aria-live="polite"
          class="emotion-10"
          role="alert"
        >
          <div
            class="emotion-11"
          >
            <svg
              class="emotion-12"
              height="20"
              viewBox="0 0 30 30"
              width="20"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M15 0c8.284 0 15 6.716 15 15 0 8.284-6.716 15-15 15-8.284 0-15-6.716-15-15C0 6.716 6.716 0 15 0zm0 2C7.82 2 2 7.82 2 15s5.82 13 13 13 13-5.82 13-13S22.18 2 15 2zm6.713 5.636l1.446 1.446-6.508 6.509 6.508 6.509-1.446 1.446-6.508-6.509-6.51 6.509L7.25 22.1l6.509-6.509-6.51-6.509 1.447-1.446 6.509 6.509 6.508-6.509z"
              />
            </svg>
          </div>
          <div
            class="emotion-13"
          />
        </div>
      </button>
      <button
        class="emotion-39"
      >
        Full Width
      </button>
      <button
        class="emotion-39"
      >
        Full Width Selected
      </button>
      <button
        class="emotion-41"
      >
        Full Width Interactive Styles
      </button>
      <button
        class="emotion-42"
      >
        Rounded Corners
      </button>
      <button
        class="emotion-42"
      >
        Rounded Corners Selected
      </button>
      <button
        class="emotion-44"
      >
        Rounded Corners Interative Styles
      </button>
      <button
        class="emotion-45"
      >
        Pill Shape
      </button>
      <button
        class="emotion-45"
      >
        Pill Shape Selected
      </button>
      <button
        class="emotion-47"
      >
        Pill Shape Interactive Styles
      </button>
      <button
        class="emotion-48"
      >
        Custom Styles
      </button>
      <button
        class="emotion-48"
      >
        Custom Styles
      </button>
      <button
        class="emotion-50"
      >
        Custom Styles
      </button>
      <button
        class="emotion-51"
      >
        XLarge Size
      </button>
      <button
        class="emotion-52"
      >
        Large Size
      </button>
      <button
        class="emotion-53"
      >
        Medium Size
      </button>
      <button
        class="emotion-0"
      >
        Small Size
      </button>
      <button
        class="emotion-55"
      >
        XSmall Size
      </button>
      <button
        class="emotion-56"
      >
        X-Large Viewport
      </button>
      <button
        class="emotion-0"
      >
        Small Viewport
      </button>
    </div>
  </div>
</div>
`;

exports[`<ComposableButtonBase /> Snapshots should match snapshots for on 1`] = `
@keyframes animation-0 {
  0%, 80%, 100% {
    -webkit-transform: scale(0);
    -moz-transform: scale(0);
    -ms-transform: scale(0);
    transform: scale(0);
  }

  40% {
    -webkit-transform: scale(1);
    -moz-transform: scale(1);
    -ms-transform: scale(1);
    transform: scale(1);
  }
}

@keyframes animation-1 {
  0% {
    -webkit-transform: scale(1.0);
    -moz-transform: scale(1.0);
    -ms-transform: scale(1.0);
    transform: scale(1.0);
  }

  10% {
    -webkit-transform: scale(1.5);
    -moz-transform: scale(1.5);
    -ms-transform: scale(1.5);
    transform: scale(1.5);
  }

  30% {
    -webkit-transform: scale(1.0);
    -moz-transform: scale(1.0);
    -ms-transform: scale(1.0);
    transform: scale(1.0);
  }

  40% {
    -webkit-transform: translate(0.9375rem, 0);
    -moz-transform: translate(0.9375rem, 0);
    -ms-transform: translate(0.9375rem, 0);
    transform: translate(0.9375rem, 0);
  }

  50% {
    -webkit-transform: translate(0, 0);
    -moz-transform: translate(0, 0);
    -ms-transform: translate(0, 0);
    transform: translate(0, 0);
  }
}

@keyframes animation-2 {
  0% {
    opacity: 0;
    width: 0;
  }

  35% {
    width: 0;
  }

  50% {
    width: 100%;
  }

  60% {
    opacity: 0;
  }

  100% {
    opacity: 1;
  }
}

.emotion-0 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 700;
  letter-spacing: 1.6800000000000002px;
  min-height: 44px;
  max-height: auto;
  line-height: 1.1428571428571428;
  padding: 12px 22px;
  width: auto;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #FFFFFF;
  background-color: #003764;
  border-color: #003764;
}

.emotion-0:focus {
  outline: none;
}

.emotion-0>span {
  padding: 1px 0;
}

.emotion-2 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 700;
  letter-spacing: 1.6800000000000002px;
  min-height: 44px;
  max-height: auto;
  line-height: 1.1428571428571428;
  padding: 12px 22px;
  width: auto;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #FFFFFF;
  background-color: #003764;
  border-color: #003764;
}

.emotion-2:focus {
  outline: none;
}

.emotion-2>span {
  padding: 1px 0;
}

.emotion-2:hover,
.emotion-2:focus {
  color: #003764;
  background-color: #FFFFFF;
  border-color: #003764;
}

.emotion-2:active {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  color: #FFFFFF;
  background-color: #000000;
  border-color: #000000;
}

.emotion-4 {
  width: 100%;
  text-align: center;
}

.emotion-4>div {
  width: 1rem;
  height: 1rem;
  background-color: currentColor;
  margin: 0.1rem;
  border-radius: 50%;
  display: inline-block;
  -webkit-animation: animation-0 1.4s infinite ease-in-out both;
  animation: animation-0 1.4s infinite ease-in-out both;
}

.emotion-4 :nth-of-type(1) {
  -webkit-animation-delay: -0.32s;
  animation-delay: -0.32s;
}

.emotion-4 :nth-of-type(2) {
  -webkit-animation-delay: -0.16s;
  animation-delay: -0.16s;
}

.emotion-10 {
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-flex-flow: row;
  -webkit-flex-flow: row;
  -ms-flex-flow: row;
  flex-flow: row;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
}

.emotion-11 {
  -webkit-animation: animation-1 2s;
  animation: animation-1 2s;
  -webkit-animation-iteration-count: 1;
  animation-iteration-count: 1;
  -webkit-animation-delay: 0;
  animation-delay: 0;
  height: 1.25rem;
  width: 1.25rem;
  -webkit-align-self: center;
  -ms-flex-item-align: center;
  align-self: center;
}

.emotion-12 {
  fill: currentColor;
  display: block;
}

.emotion-13 {
  -webkit-animation: animation-2 2s;
  animation: animation-2 2s;
  -webkit-animation-iteration-count: 1;
  animation-iteration-count: 1;
  -webkit-animation-delay: 0;
  animation-delay: 0;
  -webkit-align-self: center;
  -ms-flex-item-align: center;
  align-self: center;
  padding-left: 0.625rem;
}

.emotion-39 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 700;
  letter-spacing: 1.6800000000000002px;
  min-height: 44px;
  max-height: auto;
  line-height: 1.1428571428571428;
  padding: 12px 22px;
  width: 100%;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #FFFFFF;
  background-color: #003764;
  border-color: #003764;
}

.emotion-39:focus {
  outline: none;
}

.emotion-39>span {
  padding: 1px 0;
}

.emotion-41 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 700;
  letter-spacing: 1.6800000000000002px;
  min-height: 44px;
  max-height: auto;
  line-height: 1.1428571428571428;
  padding: 12px 22px;
  width: 100%;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #FFFFFF;
  background-color: #003764;
  border-color: #003764;
}

.emotion-41:focus {
  outline: none;
}

.emotion-41>span {
  padding: 1px 0;
}

.emotion-41:hover,
.emotion-41:focus {
  color: #003764;
  background-color: #FFFFFF;
  border-color: #003764;
}

.emotion-41:active {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  color: #FFFFFF;
  background-color: #000000;
  border-color: #000000;
}

.emotion-42 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 4px;
  font-size: 14px;
  font-weight: 700;
  letter-spacing: 1.6800000000000002px;
  min-height: 44px;
  max-height: auto;
  line-height: 1.1428571428571428;
  padding: 12px 22px;
  width: auto;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #FFFFFF;
  background-color: #003764;
  border-color: #003764;
}

.emotion-42:focus {
  outline: none;
}

.emotion-42>span {
  padding: 1px 0;
}

.emotion-44 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 4px;
  font-size: 14px;
  font-weight: 700;
  letter-spacing: 1.6800000000000002px;
  min-height: 44px;
  max-height: auto;
  line-height: 1.1428571428571428;
  padding: 12px 22px;
  width: auto;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #FFFFFF;
  background-color: #003764;
  border-color: #003764;
}

.emotion-44:focus {
  outline: none;
}

.emotion-44>span {
  padding: 1px 0;
}

.emotion-44:hover,
.emotion-44:focus {
  color: #003764;
  background-color: #FFFFFF;
  border-color: #003764;
}

.emotion-44:active {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  color: #FFFFFF;
  background-color: #000000;
  border-color: #000000;
}

.emotion-45 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 99em;
  font-size: 14px;
  font-weight: 700;
  letter-spacing: 1.6800000000000002px;
  min-height: 44px;
  max-height: auto;
  line-height: 1.1428571428571428;
  padding: 12px 22px;
  width: auto;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #FFFFFF;
  background-color: #003764;
  border-color: #003764;
}

.emotion-45:focus {
  outline: none;
}

.emotion-45>span {
  padding: 1px 0;
}

.emotion-47 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 99em;
  font-size: 14px;
  font-weight: 700;
  letter-spacing: 1.6800000000000002px;
  min-height: 44px;
  max-height: auto;
  line-height: 1.1428571428571428;
  padding: 12px 22px;
  width: auto;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #FFFFFF;
  background-color: #003764;
  border-color: #003764;
}

.emotion-47:focus {
  outline: none;
}

.emotion-47>span {
  padding: 1px 0;
}

.emotion-47:hover,
.emotion-47:focus {
  color: #003764;
  background-color: #FFFFFF;
  border-color: #003764;
}

.emotion-47:active {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  color: #FFFFFF;
  background-color: #000000;
  border-color: #000000;
}

.emotion-48 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 700;
  letter-spacing: 1.6800000000000002px;
  min-height: 44px;
  max-height: auto;
  line-height: 1.1428571428571428;
  padding: 12px 22px;
  width: auto;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #FFFFFF;
  background-color: #003764;
  border-color: #003764;
  display: none;
}

.emotion-48:focus {
  outline: none;
}

.emotion-48>span {
  padding: 1px 0;
}

.emotion-50 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 700;
  letter-spacing: 1.6800000000000002px;
  min-height: 44px;
  max-height: auto;
  line-height: 1.1428571428571428;
  padding: 12px 22px;
  width: auto;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #FFFFFF;
  background-color: #003764;
  border-color: #003764;
  display: none;
}

.emotion-50:focus {
  outline: none;
}

.emotion-50>span {
  padding: 1px 0;
}

.emotion-50:hover,
.emotion-50:focus {
  color: #003764;
  background-color: #FFFFFF;
  border-color: #003764;
}

.emotion-50:active {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  color: #FFFFFF;
  background-color: #000000;
  border-color: #000000;
}

.emotion-51 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 8px;
  font-size: 18px;
  font-weight: 700;
  letter-spacing: 2.1599999999999997px;
  min-height: 48px;
  max-height: auto;
  line-height: 1.1111111111111112;
  padding: 12px 22px;
  width: auto;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #FFFFFF;
  background-color: #003764;
  border-color: #003764;
}

.emotion-51:focus {
  outline: none;
}

.emotion-51>span {
  padding: 1px 0;
}

.emotion-52 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 700;
  letter-spacing: 1.92px;
  min-height: 46px;
  max-height: auto;
  line-height: 1.125;
  padding: 12px 22px;
  width: auto;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #FFFFFF;
  background-color: #003764;
  border-color: #003764;
}

.emotion-52:focus {
  outline: none;
}

.emotion-52>span {
  padding: 1px 0;
}

.emotion-54 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 8px;
  font-size: 12px;
  font-weight: 700;
  letter-spacing: 1.44px;
  min-height: 44px;
  max-height: auto;
  line-height: 1.1666666666666667;
  padding: 13px 22px;
  width: auto;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #FFFFFF;
  background-color: #003764;
  border-color: #003764;
}

.emotion-54:focus {
  outline: none;
}

.emotion-54>span {
  padding: 1px 0;
}

.emotion-55 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 8px;
  font-size: 10px;
  font-weight: 700;
  letter-spacing: 1.2px;
  min-height: 44px;
  max-height: auto;
  line-height: 1.2;
  padding: 14px 22px;
  width: auto;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #FFFFFF;
  background-color: #003764;
  border-color: #003764;
}

.emotion-55:focus {
  outline: none;
}

.emotion-55>span {
  padding: 1px 0;
}

.emotion-56 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 700;
  letter-spacing: 1.6800000000000002px;
  min-height: 48px;
  max-height: auto;
  line-height: 1.1428571428571428;
  padding: 14px 22px;
  width: auto;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #FFFFFF;
  background-color: #003764;
  border-color: #003764;
}

.emotion-56:focus {
  outline: none;
}

.emotion-56>span {
  padding: 1px 0;
}

<div>
  <div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-family-font1),sans-serif;"
    >
      <button
        class="emotion-0"
      >
        Default
      </button>
      <button
        class="emotion-0"
      >
        Default Selected
      </button>
      <button
        class="emotion-2"
      >
        Default Interactive Styles
      </button>
      <button
        class="emotion-0"
      >
        <div
          aria-busy="true"
          aria-label="loading"
          aria-live="polite"
          class="emotion-4"
          role="alert"
        >
          <div />
          <div />
          <div />
        </div>
      </button>
      <button
        class="emotion-0"
      >
        <div
          aria-busy="true"
          aria-label="loading"
          aria-live="polite"
          class="emotion-4"
          role="alert"
        >
          <div />
          <div />
          <div />
        </div>
      </button>
      <button
        class="emotion-2"
      >
        <div
          aria-busy="true"
          aria-label="loading"
          aria-live="polite"
          class="emotion-4"
          role="alert"
        >
          <div />
          <div />
          <div />
        </div>
      </button>
      <button
        class="emotion-0"
      >
        <div
          aria-busy="false"
          aria-live="polite"
          class="emotion-10"
          role="alert"
        >
          <div
            class="emotion-11"
          >
            <svg
              class="emotion-12"
              height="20"
              viewBox="0 0 30 30"
              width="20"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M15 0c8.284 0 15 6.716 15 15 0 8.284-6.716 15-15 15-8.284 0-15-6.716-15-15C0 6.716 6.716 0 15 0zm0 2C7.82 2 2 7.82 2 15s5.82 13 13 13 13-5.82 13-13S22.18 2 15 2zm5.463 7.65l2.492 2.145-9.787 9.689-5.52-4.788 2.529-2.64 2.991 2.64 7.295-7.046z"
              />
            </svg>
          </div>
          <div
            class="emotion-13"
          />
        </div>
      </button>
      <button
        class="emotion-0"
      >
        <div
          aria-busy="false"
          aria-live="polite"
          class="emotion-10"
          role="alert"
        >
          <div
            class="emotion-11"
          >
            <svg
              class="emotion-12"
              height="20"
              viewBox="0 0 30 30"
              width="20"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M15 0c8.284 0 15 6.716 15 15 0 8.284-6.716 15-15 15-8.284 0-15-6.716-15-15C0 6.716 6.716 0 15 0zm0 2C7.82 2 2 7.82 2 15s5.82 13 13 13 13-5.82 13-13S22.18 2 15 2zm5.463 7.65l2.492 2.145-9.787 9.689-5.52-4.788 2.529-2.64 2.991 2.64 7.295-7.046z"
              />
            </svg>
          </div>
          <div
            class="emotion-13"
          />
        </div>
      </button>
      <button
        class="emotion-2"
      >
        <div
          aria-busy="false"
          aria-live="polite"
          class="emotion-10"
          role="alert"
        >
          <div
            class="emotion-11"
          >
            <svg
              class="emotion-12"
              height="20"
              viewBox="0 0 30 30"
              width="20"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M15 0c8.284 0 15 6.716 15 15 0 8.284-6.716 15-15 15-8.284 0-15-6.716-15-15C0 6.716 6.716 0 15 0zm0 2C7.82 2 2 7.82 2 15s5.82 13 13 13 13-5.82 13-13S22.18 2 15 2zm5.463 7.65l2.492 2.145-9.787 9.689-5.52-4.788 2.529-2.64 2.991 2.64 7.295-7.046z"
              />
            </svg>
          </div>
          <div
            class="emotion-13"
          />
        </div>
      </button>
      <button
        class="emotion-0"
      >
        <div
          aria-busy="false"
          aria-live="polite"
          class="emotion-10"
          role="alert"
        >
          <div
            class="emotion-11"
          >
            <svg
              class="emotion-12"
              height="20"
              viewBox="0 0 30 30"
              width="20"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M15 0c8.284 0 15 6.716 15 15 0 8.284-6.716 15-15 15-8.284 0-15-6.716-15-15C0 6.716 6.716 0 15 0zm0 2C7.82 2 2 7.82 2 15s5.82 13 13 13 13-5.82 13-13S22.18 2 15 2zm6.713 5.636l1.446 1.446-6.508 6.509 6.508 6.509-1.446 1.446-6.508-6.509-6.51 6.509L7.25 22.1l6.509-6.509-6.51-6.509 1.447-1.446 6.509 6.509 6.508-6.509z"
              />
            </svg>
          </div>
          <div
            class="emotion-13"
          />
        </div>
      </button>
      <button
        class="emotion-0"
      >
        <div
          aria-busy="false"
          aria-live="polite"
          class="emotion-10"
          role="alert"
        >
          <div
            class="emotion-11"
          >
            <svg
              class="emotion-12"
              height="20"
              viewBox="0 0 30 30"
              width="20"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M15 0c8.284 0 15 6.716 15 15 0 8.284-6.716 15-15 15-8.284 0-15-6.716-15-15C0 6.716 6.716 0 15 0zm0 2C7.82 2 2 7.82 2 15s5.82 13 13 13 13-5.82 13-13S22.18 2 15 2zm6.713 5.636l1.446 1.446-6.508 6.509 6.508 6.509-1.446 1.446-6.508-6.509-6.51 6.509L7.25 22.1l6.509-6.509-6.51-6.509 1.447-1.446 6.509 6.509 6.508-6.509z"
              />
            </svg>
          </div>
          <div
            class="emotion-13"
          />
        </div>
      </button>
      <button
        class="emotion-2"
      >
        <div
          aria-busy="false"
          aria-live="polite"
          class="emotion-10"
          role="alert"
        >
          <div
            class="emotion-11"
          >
            <svg
              class="emotion-12"
              height="20"
              viewBox="0 0 30 30"
              width="20"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M15 0c8.284 0 15 6.716 15 15 0 8.284-6.716 15-15 15-8.284 0-15-6.716-15-15C0 6.716 6.716 0 15 0zm0 2C7.82 2 2 7.82 2 15s5.82 13 13 13 13-5.82 13-13S22.18 2 15 2zm6.713 5.636l1.446 1.446-6.508 6.509 6.508 6.509-1.446 1.446-6.508-6.509-6.51 6.509L7.25 22.1l6.509-6.509-6.51-6.509 1.447-1.446 6.509 6.509 6.508-6.509z"
              />
            </svg>
          </div>
          <div
            class="emotion-13"
          />
        </div>
      </button>
      <button
        class="emotion-39"
      >
        Full Width
      </button>
      <button
        class="emotion-39"
      >
        Full Width Selected
      </button>
      <button
        class="emotion-41"
      >
        Full Width Interactive Styles
      </button>
      <button
        class="emotion-42"
      >
        Rounded Corners
      </button>
      <button
        class="emotion-42"
      >
        Rounded Corners Selected
      </button>
      <button
        class="emotion-44"
      >
        Rounded Corners Interative Styles
      </button>
      <button
        class="emotion-45"
      >
        Pill Shape
      </button>
      <button
        class="emotion-45"
      >
        Pill Shape Selected
      </button>
      <button
        class="emotion-47"
      >
        Pill Shape Interactive Styles
      </button>
      <button
        class="emotion-48"
      >
        Custom Styles
      </button>
      <button
        class="emotion-48"
      >
        Custom Styles
      </button>
      <button
        class="emotion-50"
      >
        Custom Styles
      </button>
      <button
        class="emotion-51"
      >
        XLarge Size
      </button>
      <button
        class="emotion-52"
      >
        Large Size
      </button>
      <button
        class="emotion-0"
      >
        Medium Size
      </button>
      <button
        class="emotion-54"
      >
        Small Size
      </button>
      <button
        class="emotion-55"
      >
        XSmall Size
      </button>
      <button
        class="emotion-56"
      >
        X-Large Viewport
      </button>
      <button
        class="emotion-0"
      >
        Small Viewport
      </button>
    </div>
  </div>
</div>
`;

exports[`<ComposableButtonBase /> Snapshots should match snapshots for on with feature flags 1`] = `
@keyframes animation-0 {
  0%, 80%, 100% {
    -webkit-transform: scale(0);
    -moz-transform: scale(0);
    -ms-transform: scale(0);
    transform: scale(0);
  }

  40% {
    -webkit-transform: scale(1);
    -moz-transform: scale(1);
    -ms-transform: scale(1);
    transform: scale(1);
  }
}

@keyframes animation-1 {
  0% {
    -webkit-transform: scale(1.0);
    -moz-transform: scale(1.0);
    -ms-transform: scale(1.0);
    transform: scale(1.0);
  }

  10% {
    -webkit-transform: scale(1.5);
    -moz-transform: scale(1.5);
    -ms-transform: scale(1.5);
    transform: scale(1.5);
  }

  30% {
    -webkit-transform: scale(1.0);
    -moz-transform: scale(1.0);
    -ms-transform: scale(1.0);
    transform: scale(1.0);
  }

  40% {
    -webkit-transform: translate(0.9375rem, 0);
    -moz-transform: translate(0.9375rem, 0);
    -ms-transform: translate(0.9375rem, 0);
    transform: translate(0.9375rem, 0);
  }

  50% {
    -webkit-transform: translate(0, 0);
    -moz-transform: translate(0, 0);
    -ms-transform: translate(0, 0);
    transform: translate(0, 0);
  }
}

@keyframes animation-2 {
  0% {
    opacity: 0;
    width: 0;
  }

  35% {
    width: 0;
  }

  50% {
    width: 100%;
  }

  60% {
    opacity: 0;
  }

  100% {
    opacity: 1;
  }
}

.emotion-0 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 700;
  letter-spacing: 1.6800000000000002px;
  min-height: 44px;
  max-height: auto;
  line-height: 1.1428571428571428;
  padding: 12px 22px;
  width: auto;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #FFFFFF;
  background-color: #003764;
  border-color: #003764;
}

.emotion-0:focus {
  outline: none;
}

.emotion-0>span {
  padding: 1px 0;
}

.emotion-2 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 700;
  letter-spacing: 1.6800000000000002px;
  min-height: 44px;
  max-height: auto;
  line-height: 1.1428571428571428;
  padding: 12px 22px;
  width: auto;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #FFFFFF;
  background-color: #003764;
  border-color: #003764;
}

.emotion-2:focus {
  outline: none;
}

.emotion-2>span {
  padding: 1px 0;
}

.emotion-2:hover,
.emotion-2:focus {
  color: #003764;
  background-color: #FFFFFF;
  border-color: #003764;
}

.emotion-2:active {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  color: #FFFFFF;
  background-color: #000000;
  border-color: #000000;
}

.emotion-4 {
  width: 100%;
  text-align: center;
}

.emotion-4>div {
  width: 1rem;
  height: 1rem;
  background-color: currentColor;
  margin: 0.1rem;
  border-radius: 50%;
  display: inline-block;
  -webkit-animation: animation-0 1.4s infinite ease-in-out both;
  animation: animation-0 1.4s infinite ease-in-out both;
}

.emotion-4 :nth-of-type(1) {
  -webkit-animation-delay: -0.32s;
  animation-delay: -0.32s;
}

.emotion-4 :nth-of-type(2) {
  -webkit-animation-delay: -0.16s;
  animation-delay: -0.16s;
}

.emotion-10 {
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-flex-flow: row;
  -webkit-flex-flow: row;
  -ms-flex-flow: row;
  flex-flow: row;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
}

.emotion-11 {
  -webkit-animation: animation-1 2s;
  animation: animation-1 2s;
  -webkit-animation-iteration-count: 1;
  animation-iteration-count: 1;
  -webkit-animation-delay: 0;
  animation-delay: 0;
  height: 1.25rem;
  width: 1.25rem;
  -webkit-align-self: center;
  -ms-flex-item-align: center;
  align-self: center;
}

.emotion-12 {
  fill: currentColor;
  display: block;
}

.emotion-13 {
  -webkit-animation: animation-2 2s;
  animation: animation-2 2s;
  -webkit-animation-iteration-count: 1;
  animation-iteration-count: 1;
  -webkit-animation-delay: 0;
  animation-delay: 0;
  -webkit-align-self: center;
  -ms-flex-item-align: center;
  align-self: center;
  padding-left: 0.625rem;
}

.emotion-39 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 700;
  letter-spacing: 1.6800000000000002px;
  min-height: 44px;
  max-height: auto;
  line-height: 1.1428571428571428;
  padding: 12px 22px;
  width: 100%;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #FFFFFF;
  background-color: #003764;
  border-color: #003764;
}

.emotion-39:focus {
  outline: none;
}

.emotion-39>span {
  padding: 1px 0;
}

.emotion-41 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 700;
  letter-spacing: 1.6800000000000002px;
  min-height: 44px;
  max-height: auto;
  line-height: 1.1428571428571428;
  padding: 12px 22px;
  width: 100%;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #FFFFFF;
  background-color: #003764;
  border-color: #003764;
}

.emotion-41:focus {
  outline: none;
}

.emotion-41>span {
  padding: 1px 0;
}

.emotion-41:hover,
.emotion-41:focus {
  color: #003764;
  background-color: #FFFFFF;
  border-color: #003764;
}

.emotion-41:active {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  color: #FFFFFF;
  background-color: #000000;
  border-color: #000000;
}

.emotion-42 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 4px;
  font-size: 14px;
  font-weight: 700;
  letter-spacing: 1.6800000000000002px;
  min-height: 44px;
  max-height: auto;
  line-height: 1.1428571428571428;
  padding: 12px 22px;
  width: auto;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #FFFFFF;
  background-color: #003764;
  border-color: #003764;
}

.emotion-42:focus {
  outline: none;
}

.emotion-42>span {
  padding: 1px 0;
}

.emotion-44 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 4px;
  font-size: 14px;
  font-weight: 700;
  letter-spacing: 1.6800000000000002px;
  min-height: 44px;
  max-height: auto;
  line-height: 1.1428571428571428;
  padding: 12px 22px;
  width: auto;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #FFFFFF;
  background-color: #003764;
  border-color: #003764;
}

.emotion-44:focus {
  outline: none;
}

.emotion-44>span {
  padding: 1px 0;
}

.emotion-44:hover,
.emotion-44:focus {
  color: #003764;
  background-color: #FFFFFF;
  border-color: #003764;
}

.emotion-44:active {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  color: #FFFFFF;
  background-color: #000000;
  border-color: #000000;
}

.emotion-45 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 99em;
  font-size: 14px;
  font-weight: 700;
  letter-spacing: 1.6800000000000002px;
  min-height: 44px;
  max-height: auto;
  line-height: 1.1428571428571428;
  padding: 12px 22px;
  width: auto;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #FFFFFF;
  background-color: #003764;
  border-color: #003764;
}

.emotion-45:focus {
  outline: none;
}

.emotion-45>span {
  padding: 1px 0;
}

.emotion-47 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 99em;
  font-size: 14px;
  font-weight: 700;
  letter-spacing: 1.6800000000000002px;
  min-height: 44px;
  max-height: auto;
  line-height: 1.1428571428571428;
  padding: 12px 22px;
  width: auto;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #FFFFFF;
  background-color: #003764;
  border-color: #003764;
}

.emotion-47:focus {
  outline: none;
}

.emotion-47>span {
  padding: 1px 0;
}

.emotion-47:hover,
.emotion-47:focus {
  color: #003764;
  background-color: #FFFFFF;
  border-color: #003764;
}

.emotion-47:active {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  color: #FFFFFF;
  background-color: #000000;
  border-color: #000000;
}

.emotion-48 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 700;
  letter-spacing: 1.6800000000000002px;
  min-height: 44px;
  max-height: auto;
  line-height: 1.1428571428571428;
  padding: 12px 22px;
  width: auto;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #FFFFFF;
  background-color: #003764;
  border-color: #003764;
  display: none;
}

.emotion-48:focus {
  outline: none;
}

.emotion-48>span {
  padding: 1px 0;
}

.emotion-50 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 700;
  letter-spacing: 1.6800000000000002px;
  min-height: 44px;
  max-height: auto;
  line-height: 1.1428571428571428;
  padding: 12px 22px;
  width: auto;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #FFFFFF;
  background-color: #003764;
  border-color: #003764;
  display: none;
}

.emotion-50:focus {
  outline: none;
}

.emotion-50>span {
  padding: 1px 0;
}

.emotion-50:hover,
.emotion-50:focus {
  color: #003764;
  background-color: #FFFFFF;
  border-color: #003764;
}

.emotion-50:active {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  color: #FFFFFF;
  background-color: #000000;
  border-color: #000000;
}

.emotion-51 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 8px;
  font-size: 18px;
  font-weight: 700;
  letter-spacing: 2.1599999999999997px;
  min-height: 48px;
  max-height: auto;
  line-height: 1.1111111111111112;
  padding: 12px 22px;
  width: auto;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #FFFFFF;
  background-color: #003764;
  border-color: #003764;
}

.emotion-51:focus {
  outline: none;
}

.emotion-51>span {
  padding: 1px 0;
}

.emotion-52 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 700;
  letter-spacing: 1.92px;
  min-height: 46px;
  max-height: auto;
  line-height: 1.125;
  padding: 12px 22px;
  width: auto;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #FFFFFF;
  background-color: #003764;
  border-color: #003764;
}

.emotion-52:focus {
  outline: none;
}

.emotion-52>span {
  padding: 1px 0;
}

.emotion-54 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 8px;
  font-size: 12px;
  font-weight: 700;
  letter-spacing: 1.44px;
  min-height: 44px;
  max-height: auto;
  line-height: 1.1666666666666667;
  padding: 13px 22px;
  width: auto;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #FFFFFF;
  background-color: #003764;
  border-color: #003764;
}

.emotion-54:focus {
  outline: none;
}

.emotion-54>span {
  padding: 1px 0;
}

.emotion-55 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 8px;
  font-size: 10px;
  font-weight: 700;
  letter-spacing: 1.2px;
  min-height: 44px;
  max-height: auto;
  line-height: 1.2;
  padding: 14px 22px;
  width: auto;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #FFFFFF;
  background-color: #003764;
  border-color: #003764;
}

.emotion-55:focus {
  outline: none;
}

.emotion-55>span {
  padding: 1px 0;
}

.emotion-56 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 700;
  letter-spacing: 1.6800000000000002px;
  min-height: 48px;
  max-height: auto;
  line-height: 1.1428571428571428;
  padding: 14px 22px;
  width: auto;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #FFFFFF;
  background-color: #003764;
  border-color: #003764;
}

.emotion-56:focus {
  outline: none;
}

.emotion-56>span {
  padding: 1px 0;
}

<div>
  <div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-family-font1),sans-serif;"
    >
      <button
        class="emotion-0"
      >
        Default
      </button>
      <button
        class="emotion-0"
      >
        Default Selected
      </button>
      <button
        class="emotion-2"
      >
        Default Interactive Styles
      </button>
      <button
        class="emotion-0"
      >
        <div
          aria-busy="true"
          aria-label="loading"
          aria-live="polite"
          class="emotion-4"
          role="alert"
        >
          <div />
          <div />
          <div />
        </div>
      </button>
      <button
        class="emotion-0"
      >
        <div
          aria-busy="true"
          aria-label="loading"
          aria-live="polite"
          class="emotion-4"
          role="alert"
        >
          <div />
          <div />
          <div />
        </div>
      </button>
      <button
        class="emotion-2"
      >
        <div
          aria-busy="true"
          aria-label="loading"
          aria-live="polite"
          class="emotion-4"
          role="alert"
        >
          <div />
          <div />
          <div />
        </div>
      </button>
      <button
        class="emotion-0"
      >
        <div
          aria-busy="false"
          aria-live="polite"
          class="emotion-10"
          role="alert"
        >
          <div
            class="emotion-11"
          >
            <svg
              class="emotion-12"
              height="20"
              viewBox="0 0 30 30"
              width="20"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M15 0c8.284 0 15 6.716 15 15 0 8.284-6.716 15-15 15-8.284 0-15-6.716-15-15C0 6.716 6.716 0 15 0zm0 2C7.82 2 2 7.82 2 15s5.82 13 13 13 13-5.82 13-13S22.18 2 15 2zm5.463 7.65l2.492 2.145-9.787 9.689-5.52-4.788 2.529-2.64 2.991 2.64 7.295-7.046z"
              />
            </svg>
          </div>
          <div
            class="emotion-13"
          />
        </div>
      </button>
      <button
        class="emotion-0"
      >
        <div
          aria-busy="false"
          aria-live="polite"
          class="emotion-10"
          role="alert"
        >
          <div
            class="emotion-11"
          >
            <svg
              class="emotion-12"
              height="20"
              viewBox="0 0 30 30"
              width="20"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M15 0c8.284 0 15 6.716 15 15 0 8.284-6.716 15-15 15-8.284 0-15-6.716-15-15C0 6.716 6.716 0 15 0zm0 2C7.82 2 2 7.82 2 15s5.82 13 13 13 13-5.82 13-13S22.18 2 15 2zm5.463 7.65l2.492 2.145-9.787 9.689-5.52-4.788 2.529-2.64 2.991 2.64 7.295-7.046z"
              />
            </svg>
          </div>
          <div
            class="emotion-13"
          />
        </div>
      </button>
      <button
        class="emotion-2"
      >
        <div
          aria-busy="false"
          aria-live="polite"
          class="emotion-10"
          role="alert"
        >
          <div
            class="emotion-11"
          >
            <svg
              class="emotion-12"
              height="20"
              viewBox="0 0 30 30"
              width="20"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M15 0c8.284 0 15 6.716 15 15 0 8.284-6.716 15-15 15-8.284 0-15-6.716-15-15C0 6.716 6.716 0 15 0zm0 2C7.82 2 2 7.82 2 15s5.82 13 13 13 13-5.82 13-13S22.18 2 15 2zm5.463 7.65l2.492 2.145-9.787 9.689-5.52-4.788 2.529-2.64 2.991 2.64 7.295-7.046z"
              />
            </svg>
          </div>
          <div
            class="emotion-13"
          />
        </div>
      </button>
      <button
        class="emotion-0"
      >
        <div
          aria-busy="false"
          aria-live="polite"
          class="emotion-10"
          role="alert"
        >
          <div
            class="emotion-11"
          >
            <svg
              class="emotion-12"
              height="20"
              viewBox="0 0 30 30"
              width="20"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M15 0c8.284 0 15 6.716 15 15 0 8.284-6.716 15-15 15-8.284 0-15-6.716-15-15C0 6.716 6.716 0 15 0zm0 2C7.82 2 2 7.82 2 15s5.82 13 13 13 13-5.82 13-13S22.18 2 15 2zm6.713 5.636l1.446 1.446-6.508 6.509 6.508 6.509-1.446 1.446-6.508-6.509-6.51 6.509L7.25 22.1l6.509-6.509-6.51-6.509 1.447-1.446 6.509 6.509 6.508-6.509z"
              />
            </svg>
          </div>
          <div
            class="emotion-13"
          />
        </div>
      </button>
      <button
        class="emotion-0"
      >
        <div
          aria-busy="false"
          aria-live="polite"
          class="emotion-10"
          role="alert"
        >
          <div
            class="emotion-11"
          >
            <svg
              class="emotion-12"
              height="20"
              viewBox="0 0 30 30"
              width="20"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M15 0c8.284 0 15 6.716 15 15 0 8.284-6.716 15-15 15-8.284 0-15-6.716-15-15C0 6.716 6.716 0 15 0zm0 2C7.82 2 2 7.82 2 15s5.82 13 13 13 13-5.82 13-13S22.18 2 15 2zm6.713 5.636l1.446 1.446-6.508 6.509 6.508 6.509-1.446 1.446-6.508-6.509-6.51 6.509L7.25 22.1l6.509-6.509-6.51-6.509 1.447-1.446 6.509 6.509 6.508-6.509z"
              />
            </svg>
          </div>
          <div
            class="emotion-13"
          />
        </div>
      </button>
      <button
        class="emotion-2"
      >
        <div
          aria-busy="false"
          aria-live="polite"
          class="emotion-10"
          role="alert"
        >
          <div
            class="emotion-11"
          >
            <svg
              class="emotion-12"
              height="20"
              viewBox="0 0 30 30"
              width="20"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M15 0c8.284 0 15 6.716 15 15 0 8.284-6.716 15-15 15-8.284 0-15-6.716-15-15C0 6.716 6.716 0 15 0zm0 2C7.82 2 2 7.82 2 15s5.82 13 13 13 13-5.82 13-13S22.18 2 15 2zm6.713 5.636l1.446 1.446-6.508 6.509 6.508 6.509-1.446 1.446-6.508-6.509-6.51 6.509L7.25 22.1l6.509-6.509-6.51-6.509 1.447-1.446 6.509 6.509 6.508-6.509z"
              />
            </svg>
          </div>
          <div
            class="emotion-13"
          />
        </div>
      </button>
      <button
        class="emotion-39"
      >
        Full Width
      </button>
      <button
        class="emotion-39"
      >
        Full Width Selected
      </button>
      <button
        class="emotion-41"
      >
        Full Width Interactive Styles
      </button>
      <button
        class="emotion-42"
      >
        Rounded Corners
      </button>
      <button
        class="emotion-42"
      >
        Rounded Corners Selected
      </button>
      <button
        class="emotion-44"
      >
        Rounded Corners Interative Styles
      </button>
      <button
        class="emotion-45"
      >
        Pill Shape
      </button>
      <button
        class="emotion-45"
      >
        Pill Shape Selected
      </button>
      <button
        class="emotion-47"
      >
        Pill Shape Interactive Styles
      </button>
      <button
        class="emotion-48"
      >
        Custom Styles
      </button>
      <button
        class="emotion-48"
      >
        Custom Styles
      </button>
      <button
        class="emotion-50"
      >
        Custom Styles
      </button>
      <button
        class="emotion-51"
      >
        XLarge Size
      </button>
      <button
        class="emotion-52"
      >
        Large Size
      </button>
      <button
        class="emotion-0"
      >
        Medium Size
      </button>
      <button
        class="emotion-54"
      >
        Small Size
      </button>
      <button
        class="emotion-55"
      >
        XSmall Size
      </button>
      <button
        class="emotion-56"
      >
        X-Large Viewport
      </button>
      <button
        class="emotion-0"
      >
        Small Viewport
      </button>
    </div>
  </div>
</div>
`;
