// @ts-ignore
import { Brands, fontFamilies, StitchStyleProvider } from '@ecom-next/core/react-stitch';
import { render } from 'test-utils';
import * as React from 'react';
// @ts-ignore
import { AnimationStatus } from '@ecom-next/core/legacy/button';
// @ts-ignore
import { gapBrandFont } from '@ecom-next/core/react-stitch';
import { ComposableButton } from '../index';
import { Size } from '../types';
import { ComposableButtonBase } from './composable-button-base';

const gapFont = gapBrandFont.replace(/(, )/g, ','); // replaceAll() function doesn't work with node 14.x
const brandFontFamilies = {
  ...fontFamilies,
  gap: [{ fontFamily: gapFont }],
  gapfs: [{ fontFamily: gapFont }],
};
const brands = Object.values(Brands);
const featureFlags = {
  'at-redesign-2023': true,
  'at-redesign-2024': true,
  'at-button-redesign-2024': true,
  'gap-colors-2022': true,
  'gap-redesign-2024': true,
  'on-cta-redesign-2024': true,
};

describe('<ComposableButtonBase />', () => {
  describe('Snapshots', () => {
    const Buttons: JSX.Element = (
      <>
        <ComposableButtonBase>Default</ComposableButtonBase>
        <ComposableButtonBase selected>Default Selected</ComposableButtonBase>
        <ComposableButtonBase interactiveStyles>Default Interactive Styles</ComposableButtonBase>

        <ComposableButtonBase animationStatus={AnimationStatus.Loading}>Loading Animation</ComposableButtonBase>
        <ComposableButtonBase animationStatus={AnimationStatus.Loading} selected>
          Loading Animation Selected
        </ComposableButtonBase>
        <ComposableButtonBase animationStatus={AnimationStatus.Loading} interactiveStyles>
          Loading Animation Interactive Styles
        </ComposableButtonBase>

        <ComposableButtonBase animationStatus={AnimationStatus.Success}>Success Animation</ComposableButtonBase>
        <ComposableButtonBase animationStatus={AnimationStatus.Success} selected>
          Success Animation Selected
        </ComposableButtonBase>
        <ComposableButtonBase animationStatus={AnimationStatus.Success} interactiveStyles>
          Success Animation Interactive Styles
        </ComposableButtonBase>

        <ComposableButtonBase animationStatus={AnimationStatus.Failure}>Failure Animation</ComposableButtonBase>
        <ComposableButtonBase animationStatus={AnimationStatus.Failure} selected>
          Failure Animation Selected
        </ComposableButtonBase>
        <ComposableButtonBase animationStatus={AnimationStatus.Failure} interactiveStyles>
          Failure Animation Interactive Styles
        </ComposableButtonBase>

        <ComposableButtonBase fullWidth>Full Width</ComposableButtonBase>
        <ComposableButtonBase fullWidth selected>
          Full Width Selected
        </ComposableButtonBase>
        <ComposableButtonBase fullWidth interactiveStyles>
          Full Width Interactive Styles
        </ComposableButtonBase>

        <ComposableButtonBase roundedCorners>Rounded Corners</ComposableButtonBase>
        <ComposableButtonBase roundedCorners selected>
          Rounded Corners Selected
        </ComposableButtonBase>
        <ComposableButtonBase interactiveStyles roundedCorners>
          Rounded Corners Interative Styles
        </ComposableButtonBase>

        <ComposableButtonBase pillShape>Pill Shape</ComposableButtonBase>
        <ComposableButtonBase pillShape selected>
          Pill Shape Selected
        </ComposableButtonBase>
        <ComposableButtonBase interactiveStyles pillShape>
          Pill Shape Interactive Styles
        </ComposableButtonBase>

        <ComposableButtonBase css={{ display: 'none' }}>Custom Styles</ComposableButtonBase>
        <ComposableButtonBase css={{ display: 'none' }} selected>
          Custom Styles
        </ComposableButtonBase>
        <ComposableButtonBase css={{ display: 'none' }} interactiveStyles>
          Custom Styles
        </ComposableButtonBase>

        <ComposableButtonBase size={Size.xl}>XLarge Size</ComposableButtonBase>

        <ComposableButtonBase size={Size.large}>Large Size</ComposableButtonBase>

        <ComposableButtonBase size={Size.medium}>Medium Size</ComposableButtonBase>

        <ComposableButtonBase size={Size.small}>Small Size</ComposableButtonBase>

        <ComposableButtonBase size={Size.xs}>XSmall Size</ComposableButtonBase>

        <ComposableButtonBase viewport='x-large'>X-Large Viewport</ComposableButtonBase>

        <ComposableButtonBase viewport='small'>Small Viewport</ComposableButtonBase>
      </>
    );

    brands.forEach((brand: Brands) => {
      it(`should match snapshots for ${brand}`, () => {
        const { container } = render(<StitchStyleProvider brand={brand}>{Buttons}</StitchStyleProvider>);
        expect(container).toMatchSnapshot();
      });

      it(`should match snapshots for ${brand} with feature flags`, () => {
        const { container } = render(
          <StitchStyleProvider brand={brand} enabledFeatures={featureFlags}>
            {Buttons}
          </StitchStyleProvider>
        );
        expect(container).toMatchSnapshot();
      });
    });
  });

  describe('shows correct fontFamily', () => {
    Object.values(Brands).forEach(brand => {
      it(`for supported brand: ${brand}`, () => {
        const { container } = render(
          <StitchStyleProvider brand={brand}>
            <ComposableButton>Lorem ipsum</ComposableButton>
          </StitchStyleProvider>
        );
        const brandedFont = brandFontFamilies[brand as Brands][0].fontFamily ?? '';
        const content = container.querySelector('button');
        const style = content !== null ? window.getComputedStyle(content) : { fontFamily: '' };
        expect(style?.fontFamily).toEqual(expect.stringContaining(brandedFont as string));
      });
    });
  });
});
