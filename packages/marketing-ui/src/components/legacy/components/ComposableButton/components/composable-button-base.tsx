// @ts-nocheck
'use client';
import { Button } from '@ecom-next/core/legacy/button';
import { Brands, css, forBrands, styled, useEnabledFeatures } from '@ecom-next/core/react-stitch';
import React, { useContext } from 'react';
import { OnCtaRedesign2024Context } from '../../../contexts/OnCtaRedesign2024Context';
import { getInteractiveStyles } from '../config/interactive-styles';
import { getComposableButtonStylesPerBrand, setTextAlign } from '../styles';
import { defaultStyle } from '../styles/helpers';
import { Color, ComposableButtonBaseProps, ComposableButtonStyleFn, Variant } from '../types';
import {
  interactiveStyles as interactiveStylesLegacy,
  setBorder,
  setBorderButtonStyles,
  setButtonHeight,
  setCapitalization,
  setChevronButtonStyles,
  setFlatButtonStyles,
  setFontSize,
  setFontStyles,
  setFullWidth,
  setOutlineButtonStyles,
  setSolidButtonStyles,
  setUnderlineButtonStyles,
} from './composable-button-styles';

const removeDefaultButtonStyles = css`
  // Remove some standard appearance issues,
  // like border-radius on Firefox mobile
  appearance: none;
  border-radius: 0;
`;

const setStyles: ComposableButtonStyleFn = ({
  color: colorProp = Color.dark,
  crossBrand,
  customColors = {
    backgroundColor: '#000000',
    foregroundColor: '#FFFFFF',
  },
  variant,
  theme,
  size,
}) => {
  switch (variant) {
    case Variant.border:
      return setBorderButtonStyles(theme, crossBrand, colorProp, customColors);
    case Variant.flat:
      return setFlatButtonStyles(theme, crossBrand, colorProp, customColors);
    case Variant.outline:
      return setOutlineButtonStyles(theme, crossBrand, colorProp, customColors);
    case Variant.underline:
      return setUnderlineButtonStyles(theme, crossBrand, colorProp, customColors);
    case Variant.chevron:
      return setChevronButtonStyles(theme, size, crossBrand, colorProp, customColors);
    default:
      return setSolidButtonStyles(theme, crossBrand, colorProp, customColors);
  }
};

export const ComposableButtonStyled = styled(Button)<ComposableButtonBaseProps>`
  ${removeDefaultButtonStyles};
  padding: 0;
  border: 0;

  &:focus {
    outline: none;
  }
  // Put padding *inside* the width
  box-sizing: border-box;

  display: inline-flex;
  align-items: center;
  justify-content: center;
  line-height: normal;

  //Set capitalization
  ${({ enabledFeatures, theme, capitalization = 'uppercase' }) =>
    setCapitalization({
      theme,
      capitalization,
    })}

  cursor: pointer;
  vertical-align: middle;

  // Simple transitions for hover states
  ${({ theme, crossBrand }) =>
    forBrands(
      theme,
      {
        at: { transition: 'none' },
        default: { transition: 'all 250ms ease-in-out' },
      },
      crossBrand
    )}

  // Put padding *inside* the width
  box-sizing: border-box;

  // Solid type is default
  border: ${({ theme, crossBrand }) => defaultStyle(theme, crossBrand).borderWidth}px solid transparent;

  // Set border-radius
  ${({ theme, crossBrand, pillShape, roundedCorners, borderRadius }) => {
    if (pillShape) return 'border-radius: 99em;';
    if (roundedCorners) return `border-radius: ${theme.borderRadius.button};`;
    return forBrands(
      theme,
      {
        on: {
          borderRadius: borderRadius || '8px',
        },
        crossBrand: { borderRadius: '0' },
        default: { borderRadius: '0' },
      },
      crossBrand
    );
  }}

  ${props => {
    if (!props.crossBrand && [Brands.OldNavy, Brands.Athleta, Brands.Gap, Brands.GapFactoryStore].includes(props.theme.brand)) {
      return css`
        ${getComposableButtonStylesPerBrand(props)}
        ${setBorder(props)}
        ${props.interactiveStyles && getInteractiveStyles(props)}
      `;
    }
    return css`
      ${setTextAlign(props)}
      ${setFullWidth(props)}
      ${setButtonHeight(props)}
      ${setFontSize(props)}
      ${setFontStyles(props)}
      ${setStyles(props)}
      ${setBorder(props)}
      ${interactiveStylesLegacy(props)}
    `;
  }};
`;

export const ComposableButtonBase = React.forwardRef<HTMLButtonElement, ComposableButtonBaseProps>((props, ref) => {
  let enabledFeatures = useEnabledFeatures();
  const useOnCtaRedesign2024 = useContext(OnCtaRedesign2024Context).enable;
  if (useOnCtaRedesign2024 === false && enabledFeatures) {
    enabledFeatures = {
      ...enabledFeatures,
      'on-cta-redesign-2024': false,
    };
  }

  return <ComposableButtonStyled {...{ ...props, enabledFeatures, useOnCtaRedesign2024 }} ref={ref} />;
});
