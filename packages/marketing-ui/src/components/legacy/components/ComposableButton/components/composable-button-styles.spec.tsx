import * as React from 'react';
// @ts-ignore
import { <PERSON><PERSON>, StitchStyleProvider, Theme } from '@ecom-next/core/react-stitch';
import { render } from 'test-utils';
import { BorderThickness, Color, ComposableButtonBaseProps, Size, Variant } from '../types';
import { ComposableButtonBase } from './composable-button-base';
import { setButtonHeight, setChevronButtonStyles, setFullWidth } from './composable-button-styles';
import { OnCtaRedesign2024Context } from '../../../contexts/OnCtaRedesign2024Context';

const borderThickness: BorderThickness[] = Object.values(BorderThickness);
const colors: Color[] = Object.values(Color);
const variants: Variant[] = Object.values(Variant);
const brands: Brands[] = Object.values(Brands);
const featureFlags = {
  'at-redesign-2023': true,
  'at-redesign-2024': true,
  'at-button-redesign-2024': true,
  'gap-colors-2022': true,
  'gap-redesign-2024': true,
  'on-cta-redesign-2024': true,
};

describe('composable-button-styles', () => {
  describe('setChevronButtonStyles', () => {
    it('should use the b1 color when no foregroundColor is provided', () => {
      const chevronButtonStyles = setChevronButtonStyles({ brand: Brands.Gap, color: { b1: 'navyblue' } } as Theme, Size.medium, false, Color.custom);
      expect(JSON.stringify(chevronButtonStyles)).toContain('navyblue');
    });
  });

  describe('setButtonHeight', () => {
    it('should set the button height for Old Navy on desktop', () => {
      expect(
        setButtonHeight({
          theme: { brand: Brands.OldNavy } as Theme,
          viewport: 'large',
        })
      ).toEqual({
        padding: '0 0.8em',
        height: '48px',
      });
    });
  });

  describe('setFullWidth', () => {
    it('should set button width to 100% when true', () => {
      const props = { fullWidth: true } as Partial<ComposableButtonBaseProps> & {
        theme: Theme;
      };
      expect(setFullWidth(props).styles).toEqual(`
        display: block;
        width: 100%;
      `);
    });
  });

  const customColors = {
    backgroundColor: 'red',
    foregroundColor: 'blue',
  };

  describe('Snapshots', () => {
    const Buttons: JSX.Element = (
      <>
        {variants.map((variant: Variant) => (
          <div key={variant}>
            {colors.map((color: Color) => (
              <div key={color}>
                <ComposableButtonBase color={color} customColors={customColors} variant={variant}>
                  {variant} {color}
                </ComposableButtonBase>

                <ComposableButtonBase color={color} customColors={customColors} selected variant={variant}>
                  {variant} {color} Selected
                </ComposableButtonBase>

                <ComposableButtonBase color={color} customColors={customColors} interactiveStyles variant={variant}>
                  {variant} {color} InteractiveStyles
                </ComposableButtonBase>
              </div>
            ))}

            {borderThickness.map((borderThickness: BorderThickness) => (
              <div key={borderThickness}>
                <ComposableButtonBase borderThickness={borderThickness} color={Color.dark} variant={Variant.border}>
                  {borderThickness}
                </ComposableButtonBase>
              </div>
            ))}
          </div>
        ))}
      </>
    );

    brands.forEach((brand: Brands) => {
      it(`should match snapshot for ${brand}`, () => {
        const { container } = render(<StitchStyleProvider brand={brand}>{Buttons}</StitchStyleProvider>);

        expect(container).toMatchSnapshot();
      });

      it(`should match snapshot for ${brand} with feature flags`, () => {
        const { container } = render(
          <OnCtaRedesign2024Context.Provider value={{ enable: true }}>
            <StitchStyleProvider brand={brand} enabledFeatures={featureFlags}>
              {Buttons}
            </StitchStyleProvider>
          </OnCtaRedesign2024Context.Provider>
        );

        expect(container).toMatchSnapshot();
      });
    });
  });
});
