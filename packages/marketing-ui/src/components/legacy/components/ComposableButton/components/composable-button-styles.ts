'use client';
// @ts-ignore
import { Brands, css, CSSObject, forBrands, getFontWeight, SerializedStyles, Theme } from '@ecom-next/core/react-stitch';
import { defaultStyle } from '../styles/helpers';
import { Color, ComposableButtonStyleFn, CustomColors, Font, Size, Variant } from '../types';

enum GlobalFontSizes {
  large = '1.2rem',
  medium = '1rem',
  small = '0.8rem',
  xl = '1.5rem',
  xs = '0.625rem',
}

const getDarkColor = (theme: Theme, crossBrand?: boolean): string =>
  forBrands(
    theme,
    {
      crossBrand: theme.color.bk,
      at: theme.color.bk,
      br: theme.color.g1,
      brfs: theme.color.g1,
      gap: theme.color.bk,
      gapfs: theme.color.bk,
      default: () => theme.color.b1,
    },
    crossBrand
  ) as string;

export const setBorderButtonStyles = (theme: Theme, crossBrand?: boolean, colorProp?: Color, customColors?: CustomColors): CSSObject => {
  switch (colorProp) {
    case Color.custom:
      return {
        backgroundColor: customColors?.backgroundColor,
        color: customColors?.foregroundColor,
        borderColor: customColors?.foregroundColor,
      };
    case Color.light:
    case Color.white:
      return {
        backgroundColor: getDarkColor(theme, crossBrand),
        color: theme.color.wh,
        borderColor: theme.color.wh,
      };
    default:
      return {
        backgroundColor: theme.color.wh,
        color: getDarkColor(theme, crossBrand),
        borderColor: getDarkColor(theme, crossBrand),
      };
  }
};

export const setFlatButtonStyles = (theme: Theme, crossBrand?: boolean, colorProp?: Color, customColors?: CustomColors): SerializedStyles => {
  let color: string | undefined;
  switch (colorProp) {
    case Color.custom:
      color = customColors?.foregroundColor;
      break;
    case Color.light:
    case Color.white:
      color = theme.color.wh;
      break;
    default:
      color = getDarkColor(theme, crossBrand);
  }
  const { borderWidth, horizontalPadding } = defaultStyle(theme, crossBrand);
  return css`
    position: relative;
    border: none;
    background-color: ${theme.color.alpha00};
    color: ${color};

    &::after {
      content: '';
      position: absolute;
      bottom: -${borderWidth}px;
      left: ${horizontalPadding};
      right: ${horizontalPadding};
      background-color: ${theme.color.alpha00};
      display: block;
      height: ${borderWidth}px;
    }
  `;
};

export const setOutlineButtonStyles = (theme: Theme, crossBrand?: boolean, colorProp?: Color, customColors?: CustomColors): CSSObject => {
  switch (colorProp) {
    case Color.custom:
      return {
        color: customColors?.foregroundColor,
        backgroundColor: theme.color.alpha00,
        borderColor: customColors?.foregroundColor,
      };
    case Color.light:
    case Color.white:
      return {
        color: theme.color.wh,
        backgroundColor: theme.color.alpha00,
        borderColor: theme.color.wh,
      };
    default:
      return {
        color: getDarkColor(theme, crossBrand),
        backgroundColor: theme.color.alpha00,
        borderColor: getDarkColor(theme, crossBrand),
      };
  }
};

export const setUnderlineButtonStyles = (theme: Theme, crossBrand?: boolean, colorProp?: Color, customColors?: CustomColors): CSSObject => {
  let color;
  switch (colorProp) {
    case Color.custom:
      color = customColors?.foregroundColor;
      break;
    case Color.light:
    case Color.white:
      color = theme.color.wh;
      break;
    default:
      color = getDarkColor(theme, crossBrand);
  }
  return {
    borderLeft: 'none',
    borderRight: 'none',
    borderRadius: '0',
    backgroundColor: theme.color.alpha00,
    borderBottom: `${defaultStyle(theme, crossBrand).borderWidth}px solid currentColor`,
    color,
    paddingLeft: 0,
    paddingRight: 0,
    height: 'auto',
    ...(forBrands(
      theme,
      {
        at: {
          borderTop: '1px solid transparent',
          borderBottom: '1px solid currentColor',
          paddingTop: '4px',
          paddingBottom: '4px',
        },
        on: {
          borderTop: '3px solid transparent',
          borderBottom: '3px solid currentColor',
          paddingTop: '2px',
          paddingBottom: '2px',
        },
        crossBrand: {},
      },
      crossBrand
    ) as CSSObject),
  };
};

export const setChevronButtonStyles = (
  theme: Theme,
  size: Size = Size.medium,
  crossBrand?: boolean,
  colorProp?: Color,
  customColors?: CustomColors
): CSSObject => {
  let color: string;
  switch (colorProp) {
    case Color.custom:
      color = customColors?.foregroundColor || theme.color.b1;
      break;
    case Color.white:
      color = theme.color.wh;
      break;
    case Color.light:
      color = theme.color.wh;
      break;
    default:
      color = getDarkColor(theme, crossBrand);
  }

  enum BrandFontScaleFactor {
    at = 0.6,
    on = 0.7,
    cb = 0.65,
  }
  const chevronBaseStyles: Record<string, string | Record<string, string>> = {
    flexDirection: 'row',
    position: 'relative',
    alignItems: 'center',
    transition: '250ms ease-in-out',
    transitionProperty: 'height, width, margin',
    svg: {
      transition: 'fill 250ms ease-in-out',
      fill: color,
    },
  };
  const globalSize = GlobalFontSizes[size as keyof typeof GlobalFontSizes];
  return {
    borderRadius: 0,
    border: 'none',
    backgroundColor: 'transparent',
    color,
    height: 'auto',
    alignItems: 'center',
    ...(forBrands(
      theme,
      {
        at: {
          paddingBottom: '.15em',
          paddingRight: '.20em',
          paddingLeft: '.25em',
          span: {
            ...chevronBaseStyles,
            margin: '.18em 0 0 .15em',
            width: `calc(${globalSize} * ${BrandFontScaleFactor.at})`,
            minWidth: `calc(${globalSize} * ${BrandFontScaleFactor.at})`,
            height: `calc(${globalSize} * ${BrandFontScaleFactor.at})`,
            minHeight: `calc(${globalSize} * ${BrandFontScaleFactor.at})`,
          },
        },
        on: {
          paddingTop: '.05em',
          paddingRight: '.15em',
          paddingLeft: '.25em',
          span: {
            ...chevronBaseStyles,
            margin: '-.075em 0 0 .15em',
            width: `calc(${globalSize} * ${BrandFontScaleFactor.on})`,
            minWidth: `calc(${globalSize} * ${BrandFontScaleFactor.on})`,
            height: `calc(${globalSize} * ${BrandFontScaleFactor.on})`,
            minHeight: `calc(${globalSize} * ${BrandFontScaleFactor.on})`,
          },
        },
        gap: {
          'span span': {
            path: { fill: color },
            ...chevronBaseStyles,
            // the Icon component overrides some styles for ArrowIcons, so they are added directly in `buttonChildren`
          },
        },
        gapfs: {
          'span span': {
            path: { fill: color },
            ...chevronBaseStyles,
            // the Icon component overrides some styles for ArrowIcons, so they are added directly in `buttonChildren`
          },
        },
        crossBrand: {
          paddingRight: '.4em',
          span: {
            ...chevronBaseStyles,
            margin: '.06em 0 0 .15em',
            width: `calc(${globalSize} * ${BrandFontScaleFactor.on})`,
            minWidth: `calc(${globalSize} * ${BrandFontScaleFactor.on})`,
            height: `calc(${globalSize} * ${BrandFontScaleFactor.on})`,
            minHeight: `calc(${globalSize} * ${BrandFontScaleFactor.on})`,
          },
        },
        default: {
          paddingTop: '.05em',
          paddingRight: '.15em',
          paddingLeft: '.25em',
          span: {
            ...chevronBaseStyles,
            margin: '-.075em 0 0 .15em',
            width: `calc(${globalSize} * ${BrandFontScaleFactor.on})`,
            minWidth: `calc(${globalSize} * ${BrandFontScaleFactor.on})`,
            height: `calc(${globalSize} * ${BrandFontScaleFactor.on})`,
            minHeight: `calc(${globalSize} * ${BrandFontScaleFactor.on})`,
          },
        },
      },
      crossBrand
    ) as CSSObject),
  };
};

export const setSolidButtonStyles = (theme: Theme, crossBrand?: boolean, colorProp?: Color, customColors?: CustomColors): CSSObject => {
  switch (colorProp) {
    case Color.custom:
      return {
        color: customColors?.foregroundColor,
        backgroundColor: customColors?.backgroundColor,
      };
    case Color.light:
    case Color.white:
      return {
        color: theme.color.bk,
        backgroundColor: theme.color.wh,
      };
    default:
      return {
        color: theme.color.wh,
        backgroundColor: getDarkColor(theme, crossBrand),
      };
  }
};

export const setFontStyles: ComposableButtonStyleFn = ({ font, theme, crossBrand }): CSSObject | undefined => {
  const fontWeight = forBrands(
    theme,
    {
      br: getFontWeight('demiBold'),
      at: getFontWeight('demiBold'),
      crossBrand: getFontWeight('bold'),
      default: getFontWeight('bold'),
    },
    crossBrand
  ) as CSSObject;
  const letterSpacing = forBrands(
    theme,
    {
      at: { letterSpacing: '2.16px' },
      on: { letterSpacing: '1.6px' },
      crossBrand: {},
    },
    crossBrand
  ) as CSSObject;
  if (font === Font.brandFontAlt) {
    return crossBrand ? { ...theme.crossBrand.brandFont, ...fontWeight, ...letterSpacing } : { ...theme.brandFontAlt, ...fontWeight };
  }
  return crossBrand ? { ...theme.crossBrand.brandFont, ...fontWeight, ...letterSpacing } : { ...theme.brandFont, ...fontWeight, ...letterSpacing };
};

export const setBorder: ComposableButtonStyleFn = ({ theme, crossBrand, borderThickness }): SerializedStyles | undefined => {
  const { borderWidth } = defaultStyle(theme, crossBrand);
  let output;
  switch (borderThickness) {
    case 'thin':
      output = css`
        border-width: ${borderWidth - 1}px;

        &::after {
          height: ${borderWidth - 1}px;
          bottom: -${borderWidth - 1}px;
        }
      `;
      break;
    case 'thick':
      output = css`
        border-width: ${borderWidth + 1}px;

        &::after {
          height: ${borderWidth + 1}px;
          bottom: -${borderWidth + 1}px;
        }
      `;
      break;
    default:
  }
  return output;
};

// @ts-ignore
export const setCapitalization = ({ theme, capitalization = 'uppercase' }) => {
  if ([Brands.Gap, Brands.GapFactoryStore].includes(theme.brand)) {
    return css`
      display: inline-block;
      text-transform: none;
    `;
  }
  return theme.brand !== Brands.Athleta && `text-transform: ${capitalization};`;
};

export const setFontSize: ComposableButtonStyleFn = ({ size, crossBrand, variant, theme }) => {
  switch (size) {
    case Size.small:
      return forBrands(
        theme,
        {
          on: {
            fontSize: '0.875rem',
          },
          crossBrand: {
            fontSize: GlobalFontSizes.small,
          },
          default: {
            fontSize: GlobalFontSizes.small,
          },
        },
        crossBrand
      );
    case Size.large:
      return {
        fontSize: GlobalFontSizes.large,
      };
    case Size.xl:
      return {
        fontSize: GlobalFontSizes.xl,
      };
    default:
      return variant === Variant.underline || variant === Variant.chevron
        ? forBrands(
            theme,
            {
              at: { fontSize: '0.875rem' },
              crossBrand: { fontSize: GlobalFontSizes.medium },
              default: { fontSize: GlobalFontSizes.medium },
            },
            crossBrand
          )
        : forBrands(
            theme,
            {
              at: { fontSize: '1.125rem' },
              on: { fontSize: '0.875rem' },
              crossBrand: { fontSize: GlobalFontSizes.medium },
              default: { fontSize: GlobalFontSizes.medium },
            },
            crossBrand
          );
  }
};

export const setFullWidth: ComposableButtonStyleFn = ({ fullWidth }) =>
  fullWidth
    ? css`
        display: block;
        width: 100%;
      `
    : '';

export const setButtonHeight: ComposableButtonStyleFn = ({ theme, crossBrand, viewport }) => {
  const { horizontalPadding, verticalPadding } = defaultStyle(theme, crossBrand);
  return forBrands(
    theme,
    {
      at: {
        padding: `0 ${horizontalPadding}`,
        height: '54px',
      },
      on: {
        padding: `0 ${horizontalPadding}`,
        height: viewport && ['x-large', 'large'].includes(viewport) ? '48px' : '44px',
      },
      crossBrand: {
        padding: `${verticalPadding} ${horizontalPadding}`,
      },
      default: {
        padding: `${verticalPadding} ${horizontalPadding}`,
      },
    },
    crossBrand
  );
};

export const composableActiveButtonStateStyles: ComposableButtonStyleFn = ({ theme, variant, color, customColors, crossBrand, enabledFeatures }) => {
  if (color === Color.custom) {
    switch (variant) {
      case Variant.outline:
      case Variant.border:
        return {
          color: customColors?.backgroundColor,
          borderColor: customColors?.backgroundColor,
          backgroundColor: customColors?.foregroundColor,
        };
      case Variant.flat:
        return {
          textDecoration: 'underline',
        };
      case Variant.chevron:
        return forBrands(theme, {
          default: {
            'span svg': {
              fill: customColors?.backgroundColor,
            },
            color: customColors?.backgroundColor,
            backgroundColor: customColors?.foregroundColor,
          },
        });
      default:
        return forBrands(
          theme,
          {
            at: {
              color: customColors?.backgroundColor,
              borderColor: customColors?.foregroundColor,
              backgroundColor: customColors?.foregroundColor,
            },
            on: {
              color: customColors?.backgroundColor,
              borderColor: customColors?.foregroundColor,
              backgroundColor: customColors?.foregroundColor,
            },
            crossBrand: {
              color: customColors?.backgroundColor,
              borderColor: customColors?.backgroundColor,
              backgroundColor: customColors?.foregroundColor,
            },
            default: {
              color: customColors?.backgroundColor,
              borderColor: customColors?.backgroundColor,
              backgroundColor: customColors?.foregroundColor,
            },
          },
          crossBrand
        );
    }
  }
  if (color === Color.light || color === Color.white) {
    switch (variant) {
      case Variant.border:
      case Variant.outline:
        return {
          color: getDarkColor(theme, crossBrand),
          borderColor: theme.color.wh,
          backgroundColor: theme.color.wh,
        };
      case Variant.underline:
        return {
          color: getDarkColor(theme, crossBrand),
          borderBottomColor: theme.color.wh,
          backgroundColor: theme.color.wh,
        };
      case Variant.flat:
        return {
          textDecoration: 'underline',
        };
      case Variant.chevron:
        return {
          color: getDarkColor(theme, crossBrand),
          backgroundColor: theme.color.wh,
          'span svg': {
            fill: getDarkColor(theme, crossBrand),
          },
        };
      default:
        return forBrands(
          theme,
          {
            at: {
              color: theme.color.wh,
              borderColor: getDarkColor(theme, crossBrand),
              backgroundColor: getDarkColor(theme, crossBrand),
            },
            crossBrand: {
              color: theme.color.wh,
              borderColor: theme.color.wh,
              backgroundColor: getDarkColor(theme, crossBrand),
            },
            default: {
              color: theme.color.wh,
              borderColor: theme.color.wh,
              backgroundColor: getDarkColor(theme, crossBrand),
            },
          },
          crossBrand
        );
    }
  }
  // Default (dark)
  switch (variant) {
    case Variant.border:
    case Variant.outline:
      return {
        color: theme.color.wh,
        borderColor: getDarkColor(theme, crossBrand),
        backgroundColor: getDarkColor(theme, crossBrand),
      };
    case Variant.underline:
      return {
        color: theme.color.wh,
        borderBottomColor: getDarkColor(theme, crossBrand),
        backgroundColor: getDarkColor(theme, crossBrand),
      };
    case Variant.flat:
      return {
        textDecoration: 'underline',
      };
    case Variant.chevron:
      return {
        color: theme.color.wh,
        backgroundColor: getDarkColor(theme, crossBrand),
        'span svg': {
          fill: theme.color.wh,
        },
      };
    default:
      return forBrands(
        theme,
        {
          at: {
            color: getDarkColor(theme, crossBrand),
            borderColor: theme.color.wh,
            backgroundColor: theme.color.wh,
          },
          crossBrand: {
            color: getDarkColor(theme, crossBrand),
            borderColor: getDarkColor(theme, crossBrand),
            backgroundColor: theme.color.wh,
          },
          default: {
            color: getDarkColor(theme, crossBrand),
            borderColor: getDarkColor(theme, crossBrand),
            backgroundColor: theme.color.wh,
          },
        },
        crossBrand
      );
  }
};

export const hasActiveUnderline = (variant: Variant | undefined): boolean => {
  /*
    only chevron and underline variants do not get underline
  */
  if (variant === Variant.underline || variant === Variant.chevron) {
    return false;
  }

  return true;
};

// At time of writing after the refactor, this only works for BR & BRFS
// eslint-disable-next-line @typescript-eslint/no-shadow
export const interactiveStyles: ComposableButtonStyleFn = ({ theme, variant, color, customColors, crossBrand, interactiveStyles, selected }) => {
  if (!interactiveStyles) return {};

  const composedStyles = composableActiveButtonStateStyles({
    theme,
    variant,
    color,
    customColors,
    crossBrand,
  });

  const underline = {
    textDecoration: 'underline',
  };

  const activeStyles = {
    '&:hover, &:focus': composedStyles,
  };

  const activeUnderline = {
    '&:active': underline,
  };

  const selectedStyles = {
    ...composedStyles,
    ...underline,
  };

  return {
    ...activeStyles,
    ...(selected ? selectedStyles : {}),
    ...(hasActiveUnderline(variant) ? activeUnderline : {}),
  };
};
