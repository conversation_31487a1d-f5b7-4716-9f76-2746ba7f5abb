// @ts-nocheck
'use client';
import { AT_FEATURE_FLAG_REDESIGN } from '../../constants';
import { Size } from '../../types';
import { TextButtonChildrenGetStylesFn } from '../types';

export const getAtTextButtonStyles: TextButtonChildrenGetStylesFn = ({ viewport, enabledFeatures, size }) => {
  const smallViewportGap = size && [Size.xs, Size.small].includes(size) ? 4 : 8;
  const largeViewportGap = size && [Size.xs].includes(size) ? 4 : 8;

  if (enabledFeatures?.[AT_FEATURE_FLAG_REDESIGN])
    return {
      display: 'flex',
      alignItems: 'center',
      gap: viewport === 'small' ? smallViewportGap : largeViewportGap,
    };

  return {};
};
