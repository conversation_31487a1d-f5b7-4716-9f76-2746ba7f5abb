// @ts-nocheck
'use client';
import React, { useContext, useMemo } from 'react';
import { BreakpointContext } from '@ecom-next/core/breakpoint-provider';
import { ArrowIcon, ArrowBoldIcon, ChevronBoldIcon, ColorTheme } from '@ecom-next/core/legacy/icons';
import { useTheme, Brands, styled, forBrands, CSSObject, useEnabledFeatures } from '@ecom-next/core/react-stitch';
import { ComposableButtonProps, Variant, Size } from '../types';
import { ComposableButtonBase } from './composable-button-base';
import { gapCommonFontSize } from '../config/gap.config';
import { IconSize, TextButtonChildrenProps } from './types';
import { getAtTextButtonStyles } from './config/textButtonChildren.at';
import { AT_FEATURE_FLAG_REDESIGN } from '../constants';

const TextButtonChildrenSpan = styled.span<TextButtonChildrenProps>(props => ({
  boxSizing: 'border-box',
  ...(forBrands(props.theme, {
    at: getAtTextButtonStyles({ ...props }),
  }) as CSSObject),
}));

export const ComposableButton = React.forwardRef<HTMLButtonElement, ComposableButtonProps>(
  ({ failureMessage = 'FAILURE', successMessage = 'SUCCESS', children, variant, borderRadius, ...other }, ref): JSX.Element => {
    const theme = useTheme();
    const { size: viewport } = useContext(BreakpointContext);
    const color: ColorTheme = other.color as string as ColorTheme;
    const enabledFeatures = useEnabledFeatures();

    const brandSupportedChevron = [Brands.Athleta, Brands.OldNavy, Brands.Gap, Brands.GapFactoryStore].includes(theme.brand);

    const buttonChildren = useMemo(() => {
      const showChevron: boolean = variant === Variant.chevron && brandSupportedChevron;
      const isGapGapfs = theme.brand === Brands.Gap || theme.brand === Brands.GapFactoryStore;
      const gapBtnLargeOrXlarge = other.size === 'large' || other.size === 'xl';

      if (variant === Variant.underline) {
        return (
          <TextButtonChildrenSpan enabledFeatures={enabledFeatures} size={other.size} theme={theme} viewport={viewport}>
            {children}
          </TextButtonChildrenSpan>
        );
      }

      if (showChevron && isGapGapfs) {
        const gapIconSize: () => {
          size: { width: string; height: string };
        } = () => {
          const gapFontSize = gapCommonFontSize(other.size ?? Size.medium, viewport);
          const iconHeight = `${parseFloat(gapFontSize) * 0.75}px`;
          const iconWidth = `${parseFloat(gapFontSize) * 0.8}px`;

          return {
            size: {
              height: iconHeight,
              width: iconWidth,
            },
          };
        };

        return (
          <TextButtonChildrenSpan enabledFeatures={enabledFeatures} size={other.size} theme={theme} viewport={viewport}>
            {children}
            {!gapBtnLargeOrXlarge && <ArrowBoldIcon colorTheme={color} css={{ marginLeft: '4px' }} {...gapIconSize()} />}
            {gapBtnLargeOrXlarge && <ArrowIcon colorTheme={color} css={{ marginLeft: '6px' }} {...gapIconSize()} />}
          </TextButtonChildrenSpan>
        );
      }

      const iconSize = forBrands(theme, {
        at: enabledFeatures?.[AT_FEATURE_FLAG_REDESIGN]
          ? {
              size: {
                width: 6,
                height: 12,
              },
            }
          : undefined,
        default: undefined,
      }) as IconSize | undefined;

      if (showChevron) {
        return (
          <TextButtonChildrenSpan enabledFeatures={enabledFeatures} size={other.size} theme={theme} viewport={viewport}>
            {children}
            <ChevronBoldIcon colorTheme={color} {...iconSize} />
          </TextButtonChildrenSpan>
        );
      }

      return children;
      // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [brandSupportedChevron, children, color, other.size, theme.brand, variant, viewport]);

    return (
      <ComposableButtonBase
        {...other}
        ref={ref}
        borderRadius={borderRadius}
        failureMessage={failureMessage}
        successMessage={successMessage}
        variant={variant}
        viewport={viewport}
      >
        {buttonChildren}
      </ComposableButtonBase>
    );
  }
);

export { buttonStyles, BorderThickness, Capitalization, Color, Font, Size, Variant } from '../types';
export type { ComposableButtonProps } from '../types';
