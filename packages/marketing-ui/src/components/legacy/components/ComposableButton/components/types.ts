// @ts-nocheck
'use client';
import { Size as ViewportSize } from '@ecom-next/core/breakpoint-provider';
import { CSSObject, Theme } from '@emotion/react';
import { Size } from '../types';

export interface IconSize {
  size: { width: string; height: string };
}

export interface TextButtonChildrenProps {
  theme: Theme;
  size?: Size;
  viewport: ViewportSize;
  enabledFeatures: Record<string, boolean>;
}

export type TextButtonChildrenGetStylesFn = (props: TextButtonChildrenProps) => CSSObject;
