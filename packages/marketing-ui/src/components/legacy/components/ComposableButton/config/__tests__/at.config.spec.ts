// @ts-nocheck
import at from '../at.config';
import { Variant, Size } from '../../types';

const sizes = Object.keys(Size).sort();
const variants = Object.values(Variant).sort();

let config = at();
describe('athleta composable-button config', () => {
  beforeEach(() => {
    config = at();
  });

  describe('desktop configuration', () => {
    it('should have configurations for all variants', () => {
      expect(Object.keys(config.small).sort()).toEqual(variants);
    });

    it('should get the correct configuration for the solid variant', () => {
      const { small } = config;
      expect(Object.keys(small.solid).sort()).toEqual(sizes);

      expect(small.solid).toEqual({
        [Size.xl]: {
          fontSize: '18px',
          fontWeight: 'medium',
          maxHeight: '64px',
          letterSpacing: '0px',
          lineHeight: 20 / 18,
          padding: ['23px', '40px'],
        },
        [Size.large]: {
          fontSize: '16px',
          fontWeight: 'medium',
          maxHeight: '60px',
          letterSpacing: '0px',
          lineHeight: 20 / 16,
          padding: ['22px', '40px'],
        },
        [Size.medium]: {
          fontSize: '15px',
          fontWeight: 'medium',
          maxHeight: '55px',
          letterSpacing: '0px',
          lineHeight: 20 / 15,
          padding: ['20px', '40px'],
        },
        [Size.small]: {
          fontSize: '14px',
          fontWeight: 'medium',
          maxHeight: '50px',
          letterSpacing: '0px',
          lineHeight: 18 / 14,
          padding: ['18px', '40px'],
        },
        [Size.xs]: {
          fontSize: '12px',
          fontWeight: 'medium',
          maxHeight: '44px',
          letterSpacing: '0px',
          lineHeight: 18 / 12,
          padding: ['16px', '40px'],
        },
      });
    });
  });
});
