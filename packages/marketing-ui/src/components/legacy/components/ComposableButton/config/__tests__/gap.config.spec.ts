import gap from '../gap.config';
import { Variant, Size } from '../../types';

const sizes = Object.keys(Size).sort();
const variants = Object.values(Variant).sort();

let config = gap();
describe('gap composable-button config', () => {
  beforeEach(() => {
    config = gap();
  });

  describe('desktop configuration', () => {
    it('should have configurations for all variants', () => {
      expect(Object.keys(config.small).sort()).toEqual(variants);
    });

    it('should get the correct configuration for the solid variant', () => {
      const { small } = config;
      expect(Object.keys(small.solid).sort()).toEqual(sizes);

      expect(small.solid).toEqual({
        [Size.xl]: {
          fontSize: '18px',
          fontWeight: 'medium',
          minHeight: 'unset',
          letterSpacing: '2%',
          lineHeight: 26 / 24,
          padding: ['12px'],
        },
        [Size.large]: {
          fontSize: '16px',
          fontWeight: 'medium',
          minHeight: 'unset',
          letterSpacing: '2%',
          lineHeight: 22 / 20,
          padding: ['12px'],
        },
        [Size.medium]: {
          fontSize: '14px',
          fontWeight: 'medium',
          minHeight: 'unset',
          letterSpacing: '2%',
          lineHeight: 18 / 16,
          padding: ['12px'],
        },
        [Size.small]: {
          fontSize: '12px',
          fontWeight: 'medium',
          minHeight: 'unset',
          letterSpacing: '2%',
          lineHeight: 18 / 16,
          padding: ['12px'],
        },
        [Size.xs]: {
          fontSize: '10px',
          fontWeight: 'medium',
          minHeight: 'unset',
          letterSpacing: '2%',
          lineHeight: 16 / 12,
          padding: ['12px'],
        },
      });
    });
  });
});
