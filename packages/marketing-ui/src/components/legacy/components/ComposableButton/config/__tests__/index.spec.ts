// @ts-nocheck
import { Brands } from '@ecom-next/core/react-stitch';
import getComposableButtonConfig, { getValue } from '../index';
import gapConfig from '../gap.config';
import athletaConfig from '../at.config';
import oldNavyConfig from '../on.config';
import { Si<PERSON>, Variant } from '../../types';

describe('composable-button config', () => {
  describe('getComposableButtonConfig', () => {
    it('should default to the desktop viewport', () => {
      expect(getComposableButtonConfig(Brands.Athleta)[0]).toEqual(athletaConfig().small);
      expect(getComposableButtonConfig(Brands.Gap)[0]).toEqual(gapConfig().small);
      expect(getComposableButtonConfig(Brands.OldNavy)[0]).toEqual(oldNavyConfig().small);
    });

    it('should default to Gap brand when no brand is provided', () => {
      expect(getComposableButtonConfig()[0]).toEqual(gapConfig().small);
    });

    it('should get the brand configuration for small', () => {
      expect(getComposableButtonConfig(Brands.Athleta, 'small')[0]).toEqual(athletaConfig().small);
      expect(getComposableButtonConfig(Brands.OldNavy, 'small')[0]).toEqual(oldNavyConfig().small);
    });

    it('should get the brand configuration for desktop if mobile is not defined', () => {
      expect(getComposableButtonConfig(Brands.Athleta, 'large')[0]).toEqual(athletaConfig().small);
    });

    it('should use desktop config when invalid viewport is used', () => {
      const config = athletaConfig();
      expect(getComposableButtonConfig(Brands.Athleta)).toEqual([config.small, config.defaults]);
    });

    it('should include the brand _defaults_ parameters from the config', () => {
      const config = athletaConfig();
      expect(getComposableButtonConfig(Brands.Athleta)).toEqual([config.small, config.defaults]);
      expect(getComposableButtonConfig(Brands.Athleta)).toEqual([config.small, config.defaults]);
    });
  });

  describe('getValue', () => {
    let config = getComposableButtonConfig(Brands.Athleta);

    beforeEach(() => {
      config = getComposableButtonConfig(Brands.Athleta);
    });

    it('should get button style with default parameters', () => {
      expect(getValue(config[0])).toMatchObject({
        fontSize: '15px',
        fontWeight: 'medium',
        lineHeight: 20 / 15,
        padding: ['20px', '40px'],
      });
    });
    it('should get the correct button style values from variant and size', () => {
      expect(getValue(config[0], Variant.border, Size.xl)).toMatchObject({
        fontSize: '18px',
        fontWeight: 'medium',
        lineHeight: 20 / 18,
        padding: ['23px', '40px'],
      });
    });
  });
});
