// @ts-nocheck
import on from '../on.config';
import { Variant, Size } from '../../types';
import { calculateLetterSpacing } from '../helpers';

const sizes = Object.keys(Size).sort();
const variants = Object.values(Variant).sort();

let config = on();
describe('old navy composable-button config', () => {
  beforeEach(() => {
    config = on();
  });

  describe('desktop configuration', () => {
    it('should have configurations for all variants', () => {
      expect(Object.keys(config.small).sort()).toEqual(variants);
    });

    it('should get the correct configuration for the solid variant', () => {
      const { small, 'x-large': xLarge } = config;
      expect(Object.keys(small.solid).sort()).toEqual(sizes);

      expect(small.solid).toEqual({
        [Size.xl]: {
          fontSize: '18px',
          fontWeight: 'bold',
          minHeight: '48px',
          letterSpacing: calculateLetterSpacing(120, 18),
          lineHeight: 20 / 18,
          padding: ['12px', '22px'],
        },
        [Size.large]: {
          fontSize: '16px',
          fontWeight: 'bold',
          minHeight: '46px',
          letterSpacing: calculateLetterSpacing(120, 16),
          lineHeight: 18 / 16,
          padding: ['12px', '22px'],
        },
        [Size.medium]: {
          fontSize: '14px',
          fontWeight: 'bold',
          minHeight: '44px',
          letterSpacing: calculateLetterSpacing(120, 14),
          lineHeight: 16 / 14,
          padding: ['12px', '22px'],
        },
        [Size.small]: {
          fontSize: '12px',
          fontWeight: 'bold',
          minHeight: '44px',
          letterSpacing: calculateLetterSpacing(120, 12),
          lineHeight: 14 / 12,
          padding: ['13px', '22px'],
        },
        [Size.xs]: {
          fontSize: '10px',
          fontWeight: 'bold',
          minHeight: '44px',
          letterSpacing: calculateLetterSpacing(120, 10),
          lineHeight: 12 / 10,
          padding: ['14px', '22px'],
        },
      });

      expect(xLarge?.solid).toEqual({
        [Size.xl]: {
          fontSize: '18px',
          fontWeight: 'bold',
          minHeight: '52px',
          letterSpacing: calculateLetterSpacing(120, 18),
          lineHeight: 20 / 18,
          padding: ['14px', '22px'],
        },
        [Size.large]: {
          fontSize: '16px',
          fontWeight: 'bold',
          minHeight: '50px',
          letterSpacing: calculateLetterSpacing(120, 16),
          lineHeight: 18 / 16,
          padding: ['14px', '22px'],
        },
        [Size.medium]: {
          fontSize: '14px',
          fontWeight: 'bold',
          minHeight: '48px',
          letterSpacing: calculateLetterSpacing(120, 14),
          lineHeight: 16 / 14,
          padding: ['14px', '22px'],
        },
        [Size.small]: {
          fontSize: '12px',
          fontWeight: 'bold',
          minHeight: '46px',
          letterSpacing: calculateLetterSpacing(120, 12),
          lineHeight: 14 / 12,
          padding: ['14px', '22px'],
        },
        [Size.xs]: {
          fontSize: '10px',
          fontWeight: 'bold',
          minHeight: '44px',
          letterSpacing: calculateLetterSpacing(120, 10),
          lineHeight: 12 / 10,
          padding: ['14px', '22px'],
        },
      });
    });
  });
});
