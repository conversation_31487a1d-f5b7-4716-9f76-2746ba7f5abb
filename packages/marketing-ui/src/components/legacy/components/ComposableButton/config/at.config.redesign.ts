// @ts-nocheck
'use client';
import { Variant, Size, ComposableButtonConfig } from '../types';

const athletaComposableButtonRedesignConfig = (): ComposableButtonConfig => ({
  defaults: {
    size: Size.large,
    variant: Variant.solid,
  },
  small: {
    shared: {
      fontWeight: 'medium',
    },
    [Variant.border]: {
      [Size.xl]: {
        padding: ['12px', '16px'],
        fontSize: '18px',
        lineHeight: '18px',
        minWidth: '132px',
        maxWidth: '355px',
        minHeight: '42px',
        maxHeight: '60px',
      },
      [Size.large]: {
        padding: ['12px', '16px'],
        fontSize: '16px',
        lineHeight: '16px',
        minWidth: '121px',
        maxWidth: '355px',
        minHeight: '40px',
        maxHeight: '52px',
      },
      [Size.medium]: {
        padding: ['12px', '12px'],
        fontSize: '14px',
        lineHeight: '14px',
        minWidth: '102px',
        maxWidth: '355px',
        minHeight: '38px',
        maxHeight: '50px',
      },
      [Size.small]: {
        padding: ['12px', '12px'],
        fontSize: '12px',
        lineHeight: '12px',
        minWidth: '91px',
        maxWidth: '355px',
        minHeight: '36px',
        maxHeight: '48px',
      },
      [Size.xs]: {
        padding: ['10px', '12px'],
        fontSize: '11px',
        lineHeight: '11px',
        minWidth: '86px',
        maxWidth: '355px',
        minHeight: '35px',
        maxHeight: '46px',
      },
    },
    [Variant.chevron]: {
      [Size.xl]: {
        fontWeight: 'medium',
        padding: ['0px', '0px'],
        fontSize: '18px',
        lineHeight: '24px',
      },
      [Size.large]: {
        fontWeight: 'medium',
        padding: ['0px', '0px'],
        fontSize: '16px',
        lineHeight: '20px',
      },
      [Size.medium]: {
        fontWeight: 'medium',
        padding: ['0px', '0px'],
        fontSize: '14px',
        lineHeight: '16px',
      },
      [Size.small]: {
        fontWeight: 'medium',
        padding: ['0px', '0px'],
        fontSize: '12px',
        lineHeight: '16px',
      },
      [Size.xs]: {
        fontWeight: 'medium',
        padding: ['10px', '12px'],
        fontSize: '11px',
        lineHeight: '14px',
      },
    },
    [Variant.flat]: {
      [Size.xl]: {
        fontWeight: 'medium',
        padding: ['0px', '0px'],
        fontSize: '18px',
        lineHeight: '24px',
      },
      [Size.large]: {
        fontWeight: 'medium',
        padding: ['0px', '0px'],
        fontSize: '16px',
        lineHeight: '20px',
      },
      [Size.medium]: {
        fontWeight: 'medium',
        padding: ['0px', '0px'],
        fontSize: '14px',
        lineHeight: '16px',
      },
      [Size.small]: {
        fontWeight: 'medium',
        padding: ['0px', '0px'],
        fontSize: '12px',
        lineHeight: '16px',
      },
      [Size.xs]: {
        fontWeight: 'medium',
        padding: ['10px', '12px'],
        fontSize: '11px',
        lineHeight: '14px',
      },
    },
    [Variant.outline]: {
      [Size.xl]: {
        padding: ['12px', '16px'],
        fontSize: '18px',
        lineHeight: '18px',
        minWidth: '132px',
        maxWidth: '355px',
        minHeight: '42px',
        maxHeight: '60px',
      },
      [Size.large]: {
        padding: ['12px', '16px'],
        fontSize: '16px',
        lineHeight: '16px',
        minWidth: '121px',
        maxWidth: '355px',
        minHeight: '40px',
        maxHeight: '52px',
      },
      [Size.medium]: {
        padding: ['12px', '12px'],
        fontSize: '14px',
        lineHeight: '14px',
        minWidth: '102px',
        maxWidth: '355px',
        minHeight: '38px',
        maxHeight: '50px',
      },
      [Size.small]: {
        padding: ['12px', '12px'],
        fontSize: '12px',
        lineHeight: '12px',
        minWidth: '91px',
        maxWidth: '355px',
        minHeight: '36px',
        maxHeight: '48px',
      },
      [Size.xs]: {
        padding: ['10px', '12px'],
        fontSize: '11px',
        lineHeight: '11px',
        minWidth: '86px',
        maxWidth: '355px',
        minHeight: '35px',
        maxHeight: '46px',
      },
    },
    [Variant.solid]: {
      shared: {
        border: 'none',
      },
      [Size.xl]: {
        padding: ['12px', '16px'],
        fontSize: '18px',
        lineHeight: '18px',
        minWidth: '132px',
        maxWidth: '355px',
        minHeight: '42px',
        maxHeight: '60px',
      },
      [Size.large]: {
        padding: ['12px', '16px'],
        fontSize: '16px',
        lineHeight: '16px',
        minWidth: '121px',
        maxWidth: '355px',
        minHeight: '40px',
        maxHeight: '52px',
      },
      [Size.medium]: {
        padding: ['12px', '12px'],
        fontSize: '14px',
        lineHeight: '14px',
        minWidth: '102px',
        maxWidth: '355px',
        minHeight: '38px',
        maxHeight: '50px',
      },
      [Size.small]: {
        padding: ['12px', '12px'],
        fontSize: '12px',
        lineHeight: '12px',
        minWidth: '91px',
        maxWidth: '355px',
        minHeight: '36px',
        maxHeight: '48px',
      },
      [Size.xs]: {
        padding: ['10px', '12px'],
        fontSize: '11px',
        lineHeight: '11px',
        minWidth: '86px',
        maxWidth: '355px',
        minHeight: '35px',
        maxHeight: '46px',
      },
    },
    [Variant.underline]: {
      [Size.xl]: {
        fontWeight: 'medium',
        padding: ['0px', '0px'],
        fontSize: '18px',
        lineHeight: '24px',
      },
      [Size.large]: {
        fontWeight: 'medium',
        padding: ['0px', '0px'],
        fontSize: '16px',
        lineHeight: '20px',
      },
      [Size.medium]: {
        fontWeight: 'medium',
        padding: ['0px', '0px'],
        fontSize: '14px',
        lineHeight: '16px',
      },
      [Size.small]: {
        fontWeight: 'medium',
        padding: ['0px', '0px'],
        fontSize: '12px',
        lineHeight: '16px',
      },
      [Size.xs]: {
        fontWeight: 'medium',
        padding: ['10px', '12px'],
        fontSize: '11px',
        lineHeight: '14px',
      },
    },
  },
  'x-large': {
    shared: {
      fontWeight: 'medium',
    },
    [Variant.border]: {
      [Size.xl]: {
        padding: ['16px', '20px'],
        fontSize: '20px',
        lineHeight: '20px',
        minWidth: '151px',
        maxWidth: '355px',
        minHeight: '52px',
        maxHeight: '76px',
      },
      [Size.large]: {
        padding: ['16px', '20px'],
        fontSize: '18px',
        lineHeight: '18px',
        minWidth: '140px',
        maxWidth: '355px',
        minHeight: '50px',
        maxHeight: '74px',
      },
      [Size.medium]: {
        padding: ['16px', '20px'],
        fontSize: '16px',
        lineHeight: '16px',
        minWidth: '129px',
        maxWidth: '355px',
        minHeight: '48px',
        maxHeight: '64px',
      },
      [Size.small]: {
        padding: ['13px', '16px'],
        fontSize: '14px',
        lineHeight: '14px',
        minWidth: '110px',
        maxWidth: '355px',
        minHeight: '40px',
        maxHeight: '54px',
      },
      [Size.xs]: {
        padding: ['12px', '16px'],
        fontSize: '12px',
        lineHeight: '12px',
        minWidth: '99px',
        maxWidth: '355px',
        minHeight: '36px',
        maxHeight: '48px',
      },
    },
    [Variant.chevron]: {
      [Size.xl]: {
        fontWeight: 'medium',
        padding: ['0px', '0px'],
        fontSize: '20px',
        lineHeight: '26px',
      },
      [Size.large]: {
        fontWeight: 'medium',
        padding: ['0px', '0px'],
        fontSize: '18px',
        lineHeight: '22px',
      },
      [Size.medium]: {
        fontWeight: 'medium',
        padding: ['0px', '0px'],
        fontSize: '16px',
        lineHeight: '20px',
      },
      [Size.small]: {
        fontWeight: 'medium',
        padding: ['0px', '0px'],
        fontSize: '14px',
        lineHeight: '18px',
      },
      [Size.xs]: {
        fontWeight: 'medium',
        padding: ['0px', '0px'],
        fontSize: '12px',
        lineHeight: '16px',
      },
    },
    [Variant.flat]: {
      [Size.xl]: {
        padding: ['0px', '0px'],
        fontSize: '20px',
        lineHeight: '26px',
      },
      [Size.large]: {
        padding: ['0px', '0px'],
        fontSize: '18px',
        lineHeight: '22px',
      },
      [Size.medium]: {
        padding: ['0px', '0px'],
        fontSize: '16px',
        lineHeight: '20px',
      },
      [Size.small]: {
        padding: ['0px', '0px'],
        fontSize: '14px',
        lineHeight: '18px',
      },
      [Size.xs]: {
        padding: ['0px', '0px'],
        fontSize: '12px',
        lineHeight: '16px',
      },
    },
    [Variant.outline]: {
      [Size.xl]: {
        padding: ['16px', '20px'],
        fontSize: '20px',
        lineHeight: '20px',
        minWidth: '151px',
        maxWidth: '355px',
        minHeight: '52px',
        maxHeight: '76px',
      },
      [Size.large]: {
        padding: ['16px', '20px'],
        fontSize: '18px',
        lineHeight: '18px',
        minWidth: '140px',
        maxWidth: '355px',
        minHeight: '50px',
        maxHeight: '74px',
      },
      [Size.medium]: {
        padding: ['16px', '20px'],
        fontSize: '16px',
        lineHeight: '16px',
        minWidth: '129px',
        maxWidth: '355px',
        minHeight: '48px',
        maxHeight: '64px',
      },
      [Size.small]: {
        padding: ['13px', '16px'],
        fontSize: '14px',
        lineHeight: '14px',
        minWidth: '110px',
        maxWidth: '355px',
        minHeight: '40px',
        maxHeight: '54px',
      },
      [Size.xs]: {
        padding: ['12px', '16px'],
        fontSize: '12px',
        lineHeight: '12px',
        minWidth: '99px',
        maxWidth: '355px',
        minHeight: '36px',
        maxHeight: '48px',
      },
    },
    [Variant.solid]: {
      [Size.xl]: {
        padding: ['16px', '20px'],
        fontSize: '20px',
        lineHeight: '20px',
        minWidth: '151px',
        maxWidth: '355px',
        minHeight: '52px',
        maxHeight: '76px',
      },
      [Size.large]: {
        padding: ['16px', '20px'],
        fontSize: '18px',
        lineHeight: '18px',
        minWidth: '140px',
        maxWidth: '355px',
        minHeight: '50px',
        maxHeight: '74px',
      },
      [Size.medium]: {
        padding: ['16px', '20px'],
        fontSize: '16px',
        lineHeight: '16px',
        minWidth: '129px',
        maxWidth: '355px',
        minHeight: '48px',
        maxHeight: '64px',
      },
      [Size.small]: {
        padding: ['13px', '16px'],
        fontSize: '14px',
        lineHeight: '14px',
        minWidth: '110px',
        maxWidth: '355px',
        minHeight: '40px',
        maxHeight: '54px',
      },
      [Size.xs]: {
        padding: ['12px', '16px'],
        fontSize: '12px',
        lineHeight: '12px',
        minWidth: '99px',
        maxWidth: '355px',
        minHeight: '36px',
        maxHeight: '48px',
      },
    },
    [Variant.underline]: {
      [Size.xl]: {
        padding: ['0px', '0px'],
        fontSize: '20px',
        lineHeight: '26px',
      },
      [Size.large]: {
        padding: ['0px', '0px'],
        fontSize: '18px',
        lineHeight: '22px',
        fontWeight: 'medium',
      },
      [Size.medium]: {
        fontWeight: 'medium',
        padding: ['0px', '0px'],
        fontSize: '16px',
        lineHeight: '20px',
      },
      [Size.small]: {
        fontWeight: 'medium',
        padding: ['0px', '0px'],
        fontSize: '14px',
        lineHeight: '18px',
      },
      [Size.xs]: {
        fontWeight: 'medium',
        padding: ['0px', '0px'],
        fontSize: '12px',
        lineHeight: '16px',
      },
    },
  },
});

export default athletaComposableButtonRedesignConfig;
