// @ts-nocheck
'use client';
import { Variant, Size, ComposableButtonConfig } from '../types';

const athletaComposableButtonConfig = (): ComposableButtonConfig => ({
  defaults: {
    size: Size.large,
    variant: Variant.solid,
  },
  small: {
    [Variant.border]: {
      [Size.xl]: {
        fontSize: '18px',
        fontWeight: 'medium',
        maxHeight: '64px',
        letterSpacing: '0px',
        lineHeight: 20 / 18,
        padding: ['23px', '40px'],
      },
      [Size.large]: {
        fontSize: '16px',
        fontWeight: 'medium',
        maxHeight: '60px',
        letterSpacing: '0px',
        lineHeight: 20 / 16,
        padding: ['22px', '40px'],
      },
      [Size.medium]: {
        fontSize: '15px',
        fontWeight: 'medium',
        maxHeight: '55px',
        letterSpacing: '0px',
        lineHeight: 20 / 15,
        padding: ['20px', '40px'],
      },
      [Size.small]: {
        fontSize: '14px',
        fontWeight: 'medium',
        maxHeight: '50px',
        letterSpacing: '0px',
        lineHeight: 18 / 14,
        padding: ['18px', '40px'],
      },
      [Size.xs]: {
        fontSize: '12px',
        fontWeight: 'medium',
        maxHeight: '44px',
        letterSpacing: '0px',
        lineHeight: 18 / 12,
        padding: ['16px', '40px'],
      },
    },
    [Variant.chevron]: {
      [Size.xl]: {
        fontSize: '18px',
        fontWeight: 'medium',
        minHeight: '44px',
        letterSpacing: '0px',
        lineHeight: 23 / 18,
        padding: ['23px', '40px'],
      },
      [Size.large]: {
        fontSize: '16px',
        fontWeight: 'medium',
        minHeight: '44px',
        letterSpacing: '0px',
        lineHeight: 21 / 16,
        padding: ['22px', '40px'],
      },
      [Size.medium]: {
        fontSize: '15px',
        fontWeight: 'medium',
        minHeight: '48px',
        letterSpacing: '0px',
        lineHeight: 18 / 15,
        padding: ['20px', '40px'],
      },
      [Size.small]: {
        fontSize: '14px',
        fontWeight: 'medium',
        minHeight: '44px',
        letterSpacing: '0px',
        lineHeight: 16 / 14,
        padding: ['18px', '40px'],
      },
      [Size.xs]: {
        fontSize: '12px',
        fontWeight: 'medium',
        minHeight: '44px',
        letterSpacing: '0px',
        lineHeight: 13 / 12,
        padding: ['16px', '40px'],
      },
    },
    [Variant.flat]: {
      [Size.xl]: {
        fontSize: '18px',
        fontWeight: 'medium',
        minHeight: '44px',
        letterSpacing: '0px',
        lineHeight: 23 / 18,
      },
      [Size.large]: {
        fontSize: '16px',
        fontWeight: 'medium',
        minHeight: '44px',
        letterSpacing: '0px',
        lineHeight: 21 / 16,
      },
      [Size.medium]: {
        fontSize: '15px',
        fontWeight: 'medium',
        minHeight: '44px',
        letterSpacing: '0px',
        lineHeight: 18 / 15,
      },
      [Size.small]: {
        fontSize: '14px',
        fontWeight: 'medium',
        minHeight: '44px',
        letterSpacing: '0px',
        lineHeight: 16 / 14,
      },
      [Size.xs]: {
        fontSize: '12px',
        fontWeight: 'medium',
        minHeight: '44px',
        letterSpacing: '0px',
        lineHeight: 13 / 12,
      },
    },
    [Variant.outline]: {
      [Size.xl]: {
        fontSize: '18px',
        fontWeight: 'medium',
        maxHeight: '64px',
        letterSpacing: '0px',
        lineHeight: 20 / 18,
        padding: ['23px', '40px'],
      },
      [Size.large]: {
        fontSize: '16px',
        fontWeight: 'medium',
        maxHeight: '60px',
        letterSpacing: '0px',
        lineHeight: 20 / 16,
        padding: ['22px', '40px'],
      },
      [Size.medium]: {
        fontSize: '15px',
        fontWeight: 'medium',
        maxHeight: '55px',
        letterSpacing: '0px',
        lineHeight: 20 / 15,
        padding: ['20px', '40px'],
      },
      [Size.small]: {
        fontSize: '14px',
        fontWeight: 'medium',
        maxHeight: '50px',
        letterSpacing: '0px',
        lineHeight: 18 / 14,
        padding: ['18px', '40px'],
      },
      [Size.xs]: {
        fontSize: '12px',
        fontWeight: 'medium',
        maxHeight: '44px',
        letterSpacing: '0px',
        lineHeight: 18 / 12,
        padding: ['16px', '40px'],
      },
    },
    [Variant.solid]: {
      [Size.xl]: {
        fontSize: '18px',
        fontWeight: 'medium',
        maxHeight: '64px',
        letterSpacing: '0px',
        lineHeight: 20 / 18,
        padding: ['23px', '40px'],
      },
      [Size.large]: {
        fontSize: '16px',
        fontWeight: 'medium',
        maxHeight: '60px',
        letterSpacing: '0px',
        lineHeight: 20 / 16,
        padding: ['22px', '40px'],
      },
      [Size.medium]: {
        fontSize: '15px',
        fontWeight: 'medium',
        maxHeight: '55px',
        letterSpacing: '0px',
        lineHeight: 20 / 15,
        padding: ['20px', '40px'],
      },
      [Size.small]: {
        fontSize: '14px',
        fontWeight: 'medium',
        maxHeight: '50px',
        letterSpacing: '0px',
        lineHeight: 18 / 14,
        padding: ['18px', '40px'],
      },
      [Size.xs]: {
        fontSize: '12px',
        fontWeight: 'medium',
        maxHeight: '44px',
        letterSpacing: '0px',
        lineHeight: 18 / 12,
        padding: ['16px', '40px'],
      },
    },
    [Variant.underline]: {
      [Size.xl]: {
        fontSize: '18px',
        fontWeight: 'medium',
        minHeight: '44px',
        letterSpacing: '0px',
        lineHeight: 30 / 18,
        padding: ['23px', '40px'],
      },
      [Size.large]: {
        fontSize: '16px',
        fontWeight: 'medium',
        minHeight: '44px',
        letterSpacing: '0px',
        lineHeight: 28 / 16,
        padding: ['22px', '40px'],
      },
      [Size.medium]: {
        fontSize: '15px',
        fontWeight: 'medium',
        minHeight: '44px',
        letterSpacing: '0px',
        lineHeight: 26 / 15,
        padding: ['20px', '40px'],
      },
      [Size.small]: {
        fontSize: '14px',
        fontWeight: 'medium',
        minHeight: '44px',
        letterSpacing: '0px',
        lineHeight: 24 / 14,
        padding: ['18px', '40px'],
      },
      [Size.xs]: {
        fontSize: '12px',
        fontWeight: 'medium',
        minHeight: '44px',
        letterSpacing: '0px',
        lineHeight: 22 / 12,
        padding: ['16px', '40px'],
      },
    },
  },
  'x-large': {
    [Variant.border]: {
      [Size.xl]: {
        fontSize: '18px',
        fontWeight: 'medium',
        maxHeight: '64px',
        letterSpacing: '0px',
        lineHeight: 20 / 18,
        padding: ['23px', '40px'],
      },
      [Size.large]: {
        fontSize: '16px',
        fontWeight: 'medium',
        maxHeight: '60px',
        letterSpacing: '0px',
        lineHeight: 20 / 16,
        padding: ['22px', '40px'],
      },
      [Size.medium]: {
        fontSize: '15px',
        fontWeight: 'medium',
        maxHeight: '55px',
        letterSpacing: '0px',
        lineHeight: 20 / 15,
        padding: ['20px', '40px'],
      },
      [Size.small]: {
        fontSize: '14px',
        fontWeight: 'medium',
        maxHeight: '50px',
        letterSpacing: '0px',
        lineHeight: 18 / 14,
        padding: ['18px', '40px'],
      },
      [Size.xs]: {
        fontSize: '12px',
        fontWeight: 'medium',
        maxHeight: '44px',
        letterSpacing: '0px',
        lineHeight: 18 / 12,
        padding: ['16px', '40px'],
      },
    },
    [Variant.chevron]: {
      [Size.xl]: {
        fontSize: '18px',
        fontWeight: 'medium',
        letterSpacing: '0px',
        lineHeight: 23 / 18,
        padding: ['13.5px', '14px'],
      },
      [Size.large]: {
        fontSize: '16px',
        fontWeight: 'medium',
        letterSpacing: '0px',
        lineHeight: 21 / 16,
        padding: ['14px', '14px'],
      },
      [Size.medium]: {
        fontSize: '15px',
        fontWeight: 'medium',
        letterSpacing: '0px',
        lineHeight: 18 / 15,
        padding: ['14px', '14px'],
      },
      [Size.small]: {
        fontSize: '14px',
        fontWeight: 'medium',
        letterSpacing: '0px',
        lineHeight: 16 / 14,
        padding: ['12px', '14px'],
      },
      [Size.xs]: {
        fontSize: '12px',
        fontWeight: 'medium',
        letterSpacing: '0px',
        lineHeight: 13 / 12,
        padding: ['8.5px', '14px'],
      },
    },
    [Variant.flat]: {
      [Size.xl]: {
        fontSize: '18px',
        fontWeight: 'medium',
        letterSpacing: '0px',
        lineHeight: 23 / 18,
      },
      [Size.large]: {
        fontSize: '16px',
        fontWeight: 'medium',
        letterSpacing: '0px',
        lineHeight: 21 / 16,
      },
      [Size.medium]: {
        fontSize: '15px',
        fontWeight: 'medium',
        letterSpacing: '0px',
        lineHeight: 18 / 15,
      },
      [Size.small]: {
        fontSize: '14px',
        fontWeight: 'medium',
        letterSpacing: '0px',
        lineHeight: 16 / 14,
      },
      [Size.xs]: {
        fontSize: '12px',
        fontWeight: 'medium',
        letterSpacing: '0px',
        lineHeight: 13 / 12,
      },
    },
    [Variant.outline]: {
      [Size.xl]: {
        fontSize: '18px',
        fontWeight: 'medium',
        maxHeight: '64px',
        letterSpacing: '0px',
        lineHeight: 20 / 18,
        padding: ['23px', '40px'],
      },
      [Size.large]: {
        fontSize: '16px',
        fontWeight: 'medium',
        maxHeight: '60px',
        letterSpacing: '0px',
        lineHeight: 20 / 16,
        padding: ['22px', '40px'],
      },
      [Size.medium]: {
        fontSize: '15px',
        fontWeight: 'medium',
        maxHeight: '55px',
        letterSpacing: '0px',
        lineHeight: 20 / 15,
        padding: ['20px', '40px'],
      },
      [Size.small]: {
        fontSize: '14px',
        fontWeight: 'medium',
        maxHeight: '50px',
        letterSpacing: '0px',
        lineHeight: 18 / 14,
        padding: ['18px', '40px'],
      },
      [Size.xs]: {
        fontSize: '12px',
        fontWeight: 'medium',
        maxHeight: '44px',
        letterSpacing: '0px',
        lineHeight: 18 / 12,
        padding: ['16px', '40px'],
      },
    },
    [Variant.solid]: {
      [Size.xl]: {
        fontSize: '18px',
        fontWeight: 'medium',
        maxHeight: '64px',
        letterSpacing: '0px',
        lineHeight: 20 / 18,
        padding: ['23px', '40px'],
      },
      [Size.large]: {
        fontSize: '16px',
        fontWeight: 'medium',
        maxHeight: '60px',
        letterSpacing: '0px',
        lineHeight: 20 / 16,
        padding: ['22px', '40px'],
      },
      [Size.medium]: {
        fontSize: '15px',
        fontWeight: 'medium',
        maxHeight: '55px',
        letterSpacing: '0px',
        lineHeight: 20 / 15,
        padding: ['20px', '40px'],
      },
      [Size.small]: {
        fontSize: '14px',
        fontWeight: 'medium',
        maxHeight: '50px',
        letterSpacing: '0px',
        lineHeight: 18 / 14,
        padding: ['18px', '40px'],
      },
      [Size.xs]: {
        fontSize: '12px',
        fontWeight: 'medium',
        maxHeight: '44px',
        letterSpacing: '0px',
        lineHeight: 18 / 12,
        padding: ['16px', '40px'],
      },
    },
    [Variant.underline]: {
      [Size.xl]: {
        fontSize: '18px',
        fontWeight: 'medium',
        letterSpacing: '0px',
        lineHeight: 30 / 18,
        padding: ['23px', '40px'],
      },
      [Size.large]: {
        fontSize: '16px',
        fontWeight: 'medium',
        letterSpacing: '0px',
        lineHeight: 28 / 16,
        padding: ['22px', '40px'],
      },
      [Size.medium]: {
        fontSize: '15px',
        fontWeight: 'medium',
        letterSpacing: '0px',
        lineHeight: 26 / 15,
        padding: ['20px', '40px'],
      },
      [Size.small]: {
        fontSize: '14px',
        fontWeight: 'medium',
        letterSpacing: '0px',
        lineHeight: 24 / 14,
        padding: ['18px', '40px'],
      },
      [Size.xs]: {
        fontSize: '12px',
        fontWeight: 'medium',
        letterSpacing: '0px',
        lineHeight: 22 / 12,
        padding: ['16px', '40px'],
      },
    },
  },
});

export default athletaComposableButtonConfig;
