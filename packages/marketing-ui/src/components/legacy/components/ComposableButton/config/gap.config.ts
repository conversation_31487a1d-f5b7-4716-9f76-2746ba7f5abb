'use client';
import { Variant, Size, ComposableButtonConfig, ComposableButtonBaseProps, VariantSizeConfig } from '../types';

type paddingType = [string] | [string, string] | [string, string, string] | [string, string, string, string];

const gapCommonFontWeightLetterSpacing: Pick<VariantSizeConfig, 'fontWeight' | 'letterSpacing'> = {
  fontWeight: 'regular',
  letterSpacing: 0,
};

const getGapCommonFontWeightLetterSpacing = () => {
  gapCommonFontWeightLetterSpacing.fontWeight = 'medium';
  gapCommonFontWeightLetterSpacing.letterSpacing = '2%';

  return gapCommonFontWeightLetterSpacing;
};

export const gapCommonFontSize: (ctaSize: Size) => string = ctaSize => {
  const gapButtonRedesignFontSizeMap = {
    xl: '18px',
    large: '16px',
    medium: '14px',
    small: '12px',
    xs: '10px',
  };

  return gapButtonRedesignFontSizeMap[ctaSize];
};

export const gapCommonMaxWidth: (ctaSize: Size) => string = ctaSize => {
  const maxWidthMap = {
    xl: 380,
    large: 380,
    medium: 335,
    small: 300,
    xs: 230,
  };
  return `${maxWidthMap[ctaSize]}px`;
};

const gapCommonLineHeight: (ctaSize: Size, viewport?: ComposableButtonBaseProps['viewport']) => string | number = (ctaSize, viewport) => {
  const defaultLineHeight: Record<Size, string | number> = {
    xl: '18px',
    large: '16px',
    medium: '14px',
    small: '12px',
    xs: '10px',
  };

  const smallVWLineHeightMap: Record<Size, string | number> = {
    xl: 26 / 24,
    large: 22 / 20,
    medium: 18 / 16,
    small: 18 / 16,
    xs: 16 / 12,
  };

  return viewport === 'small' ? smallVWLineHeightMap[ctaSize] : defaultLineHeight[ctaSize];
};

const gapCommonPadding: (ctaSize: Size) => paddingType = ctaSize => {
  const gapButtonRedesignForNewFontPaddingMap: Record<Size, paddingType> = {
    xl: ['12px'],
    large: ['12px'],
    medium: ['12px'],
    small: ['12px'],
    xs: ['12px'],
  };

  return gapButtonRedesignForNewFontPaddingMap[ctaSize];
};

// @ts-ignore
const gapComposableButtonConfig = (): ComposableButtonConfig => ({
  defaults: {
    size: Size.small,
    variant: Variant.solid,
  },
  small: {
    [Variant.solid]: {
      [Size.xl]: {
        fontSize: gapCommonFontSize(Size.xl),
        ...getGapCommonFontWeightLetterSpacing(),
        minHeight: 'unset',
        lineHeight: gapCommonLineHeight(Size.xl, 'small'),
        padding: gapCommonPadding(Size.xl),
      },
      [Size.large]: {
        fontSize: gapCommonFontSize(Size.large),
        ...getGapCommonFontWeightLetterSpacing(),
        minHeight: 'unset',
        lineHeight: gapCommonLineHeight(Size.large, 'small'),
        padding: gapCommonPadding(Size.large),
      },
      [Size.medium]: {
        fontSize: gapCommonFontSize(Size.medium),
        ...getGapCommonFontWeightLetterSpacing(),
        minHeight: 'unset',
        lineHeight: gapCommonLineHeight(Size.medium, 'small'),
        padding: gapCommonPadding(Size.medium),
      },
      [Size.small]: {
        fontSize: gapCommonFontSize(Size.small),
        ...getGapCommonFontWeightLetterSpacing(),
        minHeight: 'unset',
        lineHeight: gapCommonLineHeight(Size.small, 'small'),
        padding: gapCommonPadding(Size.small),
      },
      [Size.xs]: {
        fontSize: gapCommonFontSize(Size.xs),
        ...getGapCommonFontWeightLetterSpacing(),
        minHeight: 'unset',
        lineHeight: gapCommonLineHeight(Size.xs, 'small'),
        padding: gapCommonPadding(Size.xs),
      },
    },
    [Variant.outline]: {
      [Size.xl]: {
        fontSize: gapCommonFontSize(Size.xl),
        ...getGapCommonFontWeightLetterSpacing(),
        minHeight: 'unset',
        lineHeight: gapCommonLineHeight(Size.xl, 'small'),
        padding: gapCommonPadding(Size.xl),
      },
      [Size.large]: {
        fontSize: gapCommonFontSize(Size.large),
        ...getGapCommonFontWeightLetterSpacing(),
        minHeight: 'unset',
        lineHeight: gapCommonLineHeight(Size.large, 'small'),
        padding: gapCommonPadding(Size.large),
      },
      [Size.medium]: {
        fontSize: gapCommonFontSize(Size.medium),
        ...getGapCommonFontWeightLetterSpacing(),
        minHeight: 'unset',
        lineHeight: gapCommonLineHeight(Size.medium, 'small'),
        padding: gapCommonPadding(Size.medium),
      },
      [Size.small]: {
        fontSize: gapCommonFontSize(Size.small),
        ...getGapCommonFontWeightLetterSpacing(),
        minHeight: 'unset',
        lineHeight: gapCommonLineHeight(Size.small, 'small'),
        padding: gapCommonPadding(Size.small),
      },
      [Size.xs]: {
        fontSize: gapCommonFontSize(Size.xs),
        ...getGapCommonFontWeightLetterSpacing(),
        minHeight: 'unset',
        lineHeight: gapCommonLineHeight(Size.xs, 'small'),
        padding: gapCommonPadding(Size.xs),
      },
    },
    [Variant.border]: {
      [Size.xl]: {
        fontSize: gapCommonFontSize(Size.xl),
        ...getGapCommonFontWeightLetterSpacing(),
        minHeight: 'unset',
        lineHeight: gapCommonLineHeight(Size.xl, 'small'),
        padding: gapCommonPadding(Size.xl),
      },
      [Size.large]: {
        fontSize: gapCommonFontSize(Size.large),
        ...getGapCommonFontWeightLetterSpacing(),
        minHeight: 'unset',
        lineHeight: gapCommonLineHeight(Size.large, 'small'),
        padding: gapCommonPadding(Size.large),
      },
      [Size.medium]: {
        fontSize: gapCommonFontSize(Size.medium),
        ...getGapCommonFontWeightLetterSpacing(),
        minHeight: 'unset',
        lineHeight: gapCommonLineHeight(Size.medium, 'small'),
        padding: gapCommonPadding(Size.medium),
      },
      [Size.small]: {
        fontSize: gapCommonFontSize(Size.small),
        ...getGapCommonFontWeightLetterSpacing(),
        minHeight: 'unset',
        lineHeight: gapCommonLineHeight(Size.small, 'small'),
        padding: gapCommonPadding(Size.small),
      },
      [Size.xs]: {
        fontSize: gapCommonFontSize(Size.xs),
        ...getGapCommonFontWeightLetterSpacing(),
        minHeight: 'unset',
        lineHeight: gapCommonLineHeight(Size.xs, 'small'),
        padding: gapCommonPadding(Size.xs),
      },
    },
    [Variant.underline]: {
      [Size.xl]: {
        fontSize: gapCommonFontSize(Size.xl),
        ...getGapCommonFontWeightLetterSpacing(),
        lineHeight: gapCommonLineHeight(Size.xl, 'small'),
        minHeight: 'auto',
      },
      [Size.large]: {
        fontSize: gapCommonFontSize(Size.large),
        ...getGapCommonFontWeightLetterSpacing(),
        lineHeight: gapCommonLineHeight(Size.large, 'small'),
        minHeight: 'auto',
      },
      [Size.medium]: {
        fontSize: gapCommonFontSize(Size.medium),
        ...getGapCommonFontWeightLetterSpacing(),
        lineHeight: gapCommonLineHeight(Size.medium, 'small'),
        minHeight: 'auto',
      },
      [Size.small]: {
        fontSize: gapCommonFontSize(Size.small),
        ...getGapCommonFontWeightLetterSpacing(),
        lineHeight: gapCommonLineHeight(Size.small, 'small'),
        minHeight: 'auto',
      },
      [Size.xs]: {
        fontSize: gapCommonFontSize(Size.xs),
        ...getGapCommonFontWeightLetterSpacing(),
        lineHeight: gapCommonLineHeight(Size.xs, 'small'),
        minHeight: 'auto',
      },
    },
    [Variant.flat]: {
      [Size.xl]: {
        fontSize: gapCommonFontSize(Size.xl),
        ...getGapCommonFontWeightLetterSpacing(),
        lineHeight: gapCommonLineHeight(Size.xl, 'small'),
        minHeight: 'auto',
      },
      [Size.large]: {
        fontSize: gapCommonFontSize(Size.large),
        ...getGapCommonFontWeightLetterSpacing(),
        lineHeight: gapCommonLineHeight(Size.large, 'small'),
        minHeight: 'auto',
      },
      [Size.medium]: {
        fontSize: gapCommonFontSize(Size.medium),
        ...getGapCommonFontWeightLetterSpacing(),
        lineHeight: gapCommonLineHeight(Size.medium, 'small'),
        minHeight: 'auto',
      },
      [Size.small]: {
        fontSize: gapCommonFontSize(Size.small),
        ...getGapCommonFontWeightLetterSpacing(),
        lineHeight: gapCommonLineHeight(Size.small, 'small'),
        minHeight: 'auto',
      },
      [Size.xs]: {
        fontSize: gapCommonFontSize(Size.xs),
        ...getGapCommonFontWeightLetterSpacing(),
        lineHeight: gapCommonLineHeight(Size.xs, 'small'),
        minHeight: 'auto',
      },
    },
    [Variant.chevron]: {
      [Size.xl]: {
        fontSize: gapCommonFontSize(Size.xl),
        ...getGapCommonFontWeightLetterSpacing(),
        lineHeight: 26 / 24,
        minHeight: 'auto',
      },
      [Size.large]: {
        fontSize: gapCommonFontSize(Size.large),
        ...getGapCommonFontWeightLetterSpacing(),
        lineHeight: 22 / 20,
        minHeight: 'auto',
      },
      [Size.medium]: {
        fontSize: gapCommonFontSize(Size.medium),
        ...getGapCommonFontWeightLetterSpacing(),
        lineHeight: 18 / 16,
        minHeight: 'auto',
      },
      [Size.small]: {
        fontSize: gapCommonFontSize(Size.small),
        ...getGapCommonFontWeightLetterSpacing(),
        lineHeight: 18 / 16,
        minHeight: 'auto',
      },
      [Size.xs]: {
        fontSize: gapCommonFontSize(Size.xs),
        ...getGapCommonFontWeightLetterSpacing(),
        lineHeight: 16 / 12,
        minHeight: 'auto',
      },
    },
  },
  'x-large': {
    [Variant.solid]: {
      [Size.xl]: {
        fontSize: gapCommonFontSize(Size.xl),
        ...getGapCommonFontWeightLetterSpacing(),
        minHeight: 'unset',
        lineHeight: gapCommonLineHeight(Size.xl),
        padding: gapCommonPadding(Size.xl),
        maxWidth: gapCommonMaxWidth(Size.xl),
      },
      [Size.large]: {
        fontSize: gapCommonFontSize(Size.large),
        ...getGapCommonFontWeightLetterSpacing(),
        minHeight: 'unset',
        lineHeight: gapCommonLineHeight(Size.large),
        padding: gapCommonPadding(Size.large),
        maxWidth: gapCommonMaxWidth(Size.large),
      },
      [Size.medium]: {
        fontSize: gapCommonFontSize(Size.medium),
        ...getGapCommonFontWeightLetterSpacing(),
        minHeight: 'unset',
        lineHeight: gapCommonLineHeight(Size.medium),
        padding: gapCommonPadding(Size.medium),
        maxWidth: gapCommonMaxWidth(Size.medium),
      },
      [Size.small]: {
        fontSize: gapCommonFontSize(Size.small),
        ...getGapCommonFontWeightLetterSpacing(),
        minHeight: 'unset',
        lineHeight: gapCommonLineHeight(Size.small),
        padding: gapCommonPadding(Size.small),
        maxWidth: gapCommonMaxWidth(Size.small),
      },
      [Size.xs]: {
        fontSize: gapCommonFontSize(Size.xs),
        ...getGapCommonFontWeightLetterSpacing(),
        minHeight: 'unset',
        lineHeight: gapCommonLineHeight(Size.xs),
        padding: gapCommonPadding(Size.xs),
        maxWidth: gapCommonMaxWidth(Size.xs),
      },
    },
    [Variant.outline]: {
      [Size.xl]: {
        fontSize: gapCommonFontSize(Size.xl),
        ...getGapCommonFontWeightLetterSpacing(),
        minHeight: 'unset',
        lineHeight: gapCommonLineHeight(Size.xl),
        padding: gapCommonPadding(Size.xl),
        maxWidth: gapCommonMaxWidth(Size.xl),
      },
      [Size.large]: {
        fontSize: gapCommonFontSize(Size.large),
        ...getGapCommonFontWeightLetterSpacing(),
        minHeight: 'unset',
        lineHeight: gapCommonLineHeight(Size.large),
        padding: gapCommonPadding(Size.large),
        maxWidth: gapCommonMaxWidth(Size.large),
      },
      [Size.medium]: {
        fontSize: gapCommonFontSize(Size.medium),
        ...getGapCommonFontWeightLetterSpacing(),
        minHeight: 'unset',
        lineHeight: gapCommonLineHeight(Size.medium),
        padding: gapCommonPadding(Size.medium),
        maxWidth: gapCommonMaxWidth(Size.medium),
      },
      [Size.small]: {
        fontSize: gapCommonFontSize(Size.small),
        ...getGapCommonFontWeightLetterSpacing(),
        minHeight: 'unset',
        lineHeight: gapCommonLineHeight(Size.small),
        padding: gapCommonPadding(Size.small),
        maxWidth: gapCommonMaxWidth(Size.small),
      },
      [Size.xs]: {
        fontSize: gapCommonFontSize(Size.xs),
        ...getGapCommonFontWeightLetterSpacing(),
        minHeight: 'unset',
        lineHeight: gapCommonLineHeight(Size.xs),
        padding: gapCommonPadding(Size.xs),
        maxWidth: gapCommonMaxWidth(Size.xs),
      },
    },
    [Variant.border]: {
      [Size.xl]: {
        fontSize: gapCommonFontSize(Size.xl),
        ...getGapCommonFontWeightLetterSpacing(),
        minHeight: 'unset',
        lineHeight: gapCommonLineHeight(Size.xl),
        padding: gapCommonPadding(Size.xl),
        maxWidth: gapCommonMaxWidth(Size.xl),
      },
      [Size.large]: {
        fontSize: gapCommonFontSize(Size.large),
        ...getGapCommonFontWeightLetterSpacing(),
        minHeight: 'unset',
        lineHeight: gapCommonLineHeight(Size.large),
        padding: gapCommonPadding(Size.large),
        maxWidth: gapCommonMaxWidth(Size.large),
      },
      [Size.medium]: {
        fontSize: gapCommonFontSize(Size.medium),
        ...getGapCommonFontWeightLetterSpacing(),
        minHeight: 'unset',
        lineHeight: gapCommonLineHeight(Size.medium),
        padding: gapCommonPadding(Size.medium),
        maxWidth: gapCommonMaxWidth(Size.medium),
      },
      [Size.small]: {
        fontSize: gapCommonFontSize(Size.small),
        ...getGapCommonFontWeightLetterSpacing(),
        minHeight: 'unset',
        lineHeight: gapCommonLineHeight(Size.small),
        padding: gapCommonPadding(Size.small),
        maxWidth: gapCommonMaxWidth(Size.small),
      },
      [Size.xs]: {
        fontSize: gapCommonFontSize(Size.xs),
        ...getGapCommonFontWeightLetterSpacing(),
        minHeight: 'unset',
        lineHeight: gapCommonLineHeight(Size.xs),
        padding: gapCommonPadding(Size.xs),
        maxWidth: gapCommonMaxWidth(Size.xs),
      },
    },
    [Variant.underline]: {
      [Size.xl]: {
        fontSize: gapCommonFontSize(Size.xl),
        ...getGapCommonFontWeightLetterSpacing(),
        lineHeight: gapCommonLineHeight(Size.xl),
        minHeight: 'auto',
        maxWidth: gapCommonMaxWidth(Size.xl),
      },
      [Size.large]: {
        fontSize: gapCommonFontSize(Size.large),
        ...getGapCommonFontWeightLetterSpacing(),
        lineHeight: gapCommonLineHeight(Size.large),
        minHeight: 'auto',
        maxWidth: gapCommonMaxWidth(Size.large),
      },
      [Size.medium]: {
        fontSize: gapCommonFontSize(Size.medium),
        ...getGapCommonFontWeightLetterSpacing(),
        lineHeight: gapCommonLineHeight(Size.medium),
        minHeight: 'auto',
        maxWidth: gapCommonMaxWidth(Size.medium),
      },
      [Size.small]: {
        fontSize: gapCommonFontSize(Size.small),
        ...getGapCommonFontWeightLetterSpacing(),
        lineHeight: gapCommonLineHeight(Size.small),
        minHeight: 'auto',
        maxWidth: gapCommonMaxWidth(Size.small),
      },
      [Size.xs]: {
        fontSize: gapCommonFontSize(Size.xs),
        ...getGapCommonFontWeightLetterSpacing(),
        lineHeight: gapCommonLineHeight(Size.xs),
        minHeight: 'auto',
        maxWidth: gapCommonMaxWidth(Size.xs),
      },
    },
    [Variant.flat]: {
      [Size.xl]: {
        fontSize: gapCommonFontSize(Size.xl),
        ...getGapCommonFontWeightLetterSpacing(),
        lineHeight: gapCommonLineHeight(Size.xl),
        minHeight: 'auto',
        maxWidth: gapCommonMaxWidth(Size.xl),
      },
      [Size.large]: {
        fontSize: gapCommonFontSize(Size.large),
        ...getGapCommonFontWeightLetterSpacing(),
        lineHeight: gapCommonLineHeight(Size.large),
        minHeight: 'auto',
        maxWidth: gapCommonMaxWidth(Size.large),
      },
      [Size.medium]: {
        fontSize: gapCommonFontSize(Size.medium),
        ...getGapCommonFontWeightLetterSpacing(),
        lineHeight: gapCommonLineHeight(Size.medium),
        minHeight: 'auto',
        maxWidth: gapCommonMaxWidth(Size.medium),
      },
      [Size.small]: {
        fontSize: gapCommonFontSize(Size.small),
        ...getGapCommonFontWeightLetterSpacing(),
        lineHeight: gapCommonLineHeight(Size.small),
        minHeight: 'auto',
        maxWidth: gapCommonMaxWidth(Size.small),
      },
      [Size.xs]: {
        fontSize: gapCommonFontSize(Size.xs),
        ...getGapCommonFontWeightLetterSpacing(),
        lineHeight: gapCommonLineHeight(Size.xs),
        minHeight: 'auto',
        maxWidth: gapCommonMaxWidth(Size.xs),
      },
    },
    [Variant.chevron]: {
      [Size.xl]: {
        fontSize: gapCommonFontSize(Size.xl),
        ...getGapCommonFontWeightLetterSpacing(),
        lineHeight: 1,
        minHeight: 'auto',
      },
      [Size.large]: {
        fontSize: gapCommonFontSize(Size.large),
        ...getGapCommonFontWeightLetterSpacing(),
        lineHeight: 1,
        minHeight: 'auto',
      },
      [Size.medium]: {
        fontSize: gapCommonFontSize(Size.medium),
        ...getGapCommonFontWeightLetterSpacing(),
        lineHeight: 20 / 18,
        minHeight: 'auto',
      },
      [Size.small]: {
        fontSize: gapCommonFontSize(Size.small),
        ...getGapCommonFontWeightLetterSpacing(),
        lineHeight: 18 / 16,
        minHeight: 'auto',
      },
      [Size.xs]: {
        fontSize: gapCommonFontSize(Size.xs),
        ...getGapCommonFontWeightLetterSpacing(),
        lineHeight: 17 / 14,
        minHeight: 'auto',
      },
    },
  },
});

export default gapComposableButtonConfig;
