// @ts-nocheck
'use client';
import { Size as BreakpointSize } from '@ecom-next/core/breakpoint-provider';
import { EnabledFeaturesType } from '@ecom-next/core/legacy/feature-flags';
import { Brands } from '@ecom-next/core/react-stitch';
import { ComposableButtonConfig, ComposableButtonViewportConfig, Size, Variant, VariantSizeConfig } from '../types';
import at from './at.config';
import atRedesign from './at.config.redesign';
import gap from './gap.config';
import on from './on.config';
import { AT_FEATURE_FLAG_REDESIGN } from '../constants';

export function getValue(config: ComposableButtonViewportConfig, variant: Variant = Variant.solid, size: Size = Size.medium): VariantSizeConfig {
  return {
    ...config.shared,
    ...config[variant].shared,
    ...config[variant][size],
  };
}

export default function getComposableButtonConfig(
  brand: Brands = Brands.Gap,
  viewport: BreakpointSize = 'small',
  enabledFeatures?: EnabledFeaturesType
): [ComposableButtonViewportConfig, ComposableButtonConfig['defaults']] {
  const fn = (
    {
      [Brands.Athleta]: enabledFeatures?.[AT_FEATURE_FLAG_REDESIGN] ? atRedesign : at,
      [Brands.BananaRepublic]: at,
      [Brands.BananaRepublicFactoryStore]: at,
      [Brands.Gap]: gap,
      [Brands.GapFactoryStore]: gap,
      [Brands.OldNavy]: on,
    } as Record<Brands, (enabledFeatures?: EnabledFeaturesType) => ComposableButtonConfig>
  )[brand];

  const config = [Brands.Gap, Brands.GapFactoryStore, Brands.OldNavy].includes(brand) ? fn(enabledFeatures) : fn();

  const viewportConfig = (viewport && config[viewport]) || config.small;

  return [viewportConfig, config.defaults];
}
