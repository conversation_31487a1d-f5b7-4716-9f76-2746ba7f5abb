// @ts-nocheck
'use client';
import { Theme } from '@ecom-next/core/react-stitch';
import { ComposableButtonInteractiveConfig, ComposableButtonBaseProps, Variant, Color } from '../../types';
import { getActiveButtonColor } from '../../styles/helpers';

const transparentWhite = 'rgba(255, 255, 255, 0.80)';
const transparentGray = 'rgba(17, 17, 17, 0.80)';

const athletaInteractiveStylesRedesignConfig = (props: Partial<ComposableButtonBaseProps> & { theme: Theme }): ComposableButtonInteractiveConfig => {
  const { primary, secondary } = getActiveButtonColor(props);

  const { color = Color.dark } = props;

  return {
    hoverFocus: {
      [Variant.border]: {
        '&:hover, &:focus': {
          backgroundColor: color === Color.light ? transparentGray : undefined,
        },
      },
      [Variant.solid]: {
        '&:hover, &:focus': {
          backgroundColor: color === Color.light ? transparentWhite : transparentGray,
        },
      },
      [Variant.outline]: {
        '&:hover, &:focus': {
          backgroundColor: color === Color.light ? transparentGray : transparentWhite,
        },
      },
      [Variant.chevron]: {
        '&:hover, &:focus': {},
      },
      [Variant.flat]: {
        '&:hover, &:focus': {
          textDecoration: 'underline',
          textDecorationThickness: 2,
        },
      },
      [Variant.underline]: {
        '&:hover, &:focus': {
          textDecorationThickness: 2,
        },
      },
    },
    active: {
      [Variant.border]: {
        '&:active': {
          color: secondary,
          borderColor: color === Color.light ? secondary : primary,
          backgroundColor: color === Color.light ? 'white' : transparentGray,
        },
      },
      [Variant.solid]: {
        '&:active': {
          color: primary,
          borderColor: transparentWhite,
          backgroundColor: color === Color.light ? transparentGray : transparentWhite,
        },
      },
      [Variant.outline]: {
        '&:active': {
          color: secondary,
          borderColor: color === Color.light ? secondary : primary,
          backgroundColor: color === Color.light ? transparentWhite : transparentGray,
        },
      },
      [Variant.chevron]: {
        '&:active': {},
      },
      [Variant.flat]: {
        '&:active': {
          textDecorationThickness: 1,
        },
      },
      [Variant.underline]: {
        '&:active': {
          textDecoration: 'none',
        },
      },
    },
  };
};

export default athletaInteractiveStylesRedesignConfig;
