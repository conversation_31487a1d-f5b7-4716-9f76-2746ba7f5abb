// @ts-nocheck
'use client';
import { Theme } from '@ecom-next/core/react-stitch';
import { ComposableButtonInteractiveConfig, ComposableButtonBaseProps, Variant } from '../../types';
import { getActiveButtonColor } from '../../styles/helpers';
import athletaInteractiveStylesRedesignConfig from './at.interactive-styles.config.redesign';
import { AT_FEATURE_FLAG_REDESIGN } from '../../constants';

const athletaInteractiveStylesConfig = (props: Partial<ComposableButtonBaseProps> & { theme: Theme }): ComposableButtonInteractiveConfig => {
  const { primary, secondary } = getActiveButtonColor(props);

  if (props.enabledFeatures?.[AT_FEATURE_FLAG_REDESIGN]) return athletaInteractiveStylesRedesignConfig(props);

  return {
    hoverFocus: {
      [Variant.border]: {
        '&:hover, &:focus': {
          textShadow: '0 0 2px currentColor',
        },
      },
      [Variant.solid]: {
        '&:hover, &:focus': {
          textShadow: '0 0 2px currentColor',
        },
      },
      [Variant.outline]: {
        '&:hover, &:focus': {
          textShadow: '0 0 2px currentColor',
        },
      },
      [Variant.chevron]: {
        '&:hover, &:focus': {
          textShadow: '0 0 2px currentColor',
        },
      },
      [Variant.flat]: {
        '&:hover, &:focus': {
          textShadow: '0 0 2px currentColor',
        },
      },
      [Variant.underline]: {
        '&:hover, &:focus': {
          textShadow: '0 0 2px currentColor',
          textDecoration: 'none',
        },
      },
    },
    active: {
      [Variant.border]: {
        '&:active': {
          color: secondary,
          backgroundColor: primary,
          borderColor: primary,
          textDecoration: 'none',
          textShadow: 'none',
        },
      },
      [Variant.solid]: {
        '&:active': {
          color: primary,
          backgroundColor: secondary,
          borderColor: secondary,
          textDecoration: 'none',
        },
      },
      [Variant.outline]: {
        '&:active': {
          color: secondary,
          backgroundColor: primary,
          borderColor: primary,
          textDecoration: 'none',
          textShadow: 'none',
        },
      },
      [Variant.chevron]: {
        '&:active': {
          textTransform: 'uppercase',
          textShadow: 'none',
        },
      },
      [Variant.flat]: {
        '&:active': {
          textTransform: 'uppercase',
          textShadow: 'none',
        },
      },
      [Variant.underline]: {
        '&:active': {
          textTransform: 'uppercase',
          textShadow: 'none',
          textDecoration: 'underline',
        },
      },
    },
  };
};

export default athletaInteractiveStylesConfig;
