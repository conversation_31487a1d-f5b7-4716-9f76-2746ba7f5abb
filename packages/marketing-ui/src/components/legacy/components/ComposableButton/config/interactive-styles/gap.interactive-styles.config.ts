'use client';
// @ts-ignore
import { EnabledFeaturesType } from '@ecom-next/core/legacy/feature-flags';
// @ts-ignore
import { Theme } from '@ecom-next/core/react-stitch';
import { getActiveButtonColor, textDecorationThicknessMap, textUnderlineOffsetMap } from '../../styles/helpers';
import { ComposableButtonBaseProps, ComposableButtonInteractiveConfig, Variant } from '../../types';

// current Gap & Gapfs designs have the two brands as exactly the same, and focus & active styles as exactly the same, unlike AT & ON focus & active
const gapInteractiveStylesConfig = (
  props: Partial<ComposableButtonBaseProps> & {
    enabledFeatures?: EnabledFeaturesType;
    theme: Theme;
  }
): ComposableButtonInteractiveConfig => {
  const { primary, secondary } = getActiveButtonColor(props);
  const decorationStyle = {
    textDecoration: 'underline',
    textDecorationThickness: textDecorationThicknessMap(props?.size),
    textUnderlineOffset: textUnderlineOffsetMap(props?.size),
  };

  return {
    hoverFocus: {
      [Variant.border]: {
        '&:hover, &:focus': {
          color: primary,
          backgroundColor: secondary,
          borderColor: secondary,
        },
      },
      [Variant.chevron]: {
        '&:hover, &:focus': { ...decorationStyle },
      },
      [Variant.flat]: {
        '&:hover, &:focus': { ...decorationStyle },
      },
      [Variant.solid]: {
        '&:hover, &:focus': {
          color: primary,
          backgroundColor: secondary,
          borderColor: secondary,
        },
      },
      [Variant.outline]: {
        '&:hover, &:focus': {
          color: primary,
          backgroundColor: secondary,
          borderColor: secondary,
        },
      },
      [Variant.underline]: {
        '&:hover, &:focus': {
          textDecoration: 'unset',
        },
      },
    },
    active: {
      [Variant.border]: {
        '&:active': {
          color: primary,
          backgroundColor: secondary,
          borderColor: secondary,
        },
      },
      [Variant.chevron]: {
        '&:active': { ...decorationStyle },
      },
      [Variant.flat]: {
        '&:active': { ...decorationStyle },
      },
      [Variant.solid]: {
        '&:active': {
          color: primary,
          backgroundColor: secondary,
          borderColor: secondary,
        },
      },
      [Variant.outline]: {
        '&:active': {
          color: primary,
          backgroundColor: secondary,
          borderColor: secondary,
        },
      },
      [Variant.underline]: {
        '&:active': {
          textDecoration: 'unset',
        },
      },
    },
  };
};

export default gapInteractiveStylesConfig;
