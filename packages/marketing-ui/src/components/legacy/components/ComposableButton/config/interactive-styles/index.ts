// @ts-nocheck
'use client';
import { EnabledFeaturesType } from '@ecom-next/core/legacy/feature-flags';
import { Brands, CSSObject, Theme } from '@ecom-next/core/react-stitch';
import getComposableButtonConfig from '..';
import { ComposableButtonBaseProps, ComposableButtonInteractiveConfig } from '../../types';
import at from './at.interactive-styles.config';
import gap from './gap.interactive-styles.config';
import on from './on.interactive-styles.config';

const interactiveStylesConfig: Record<Brands, (props: Partial<ComposableButtonBaseProps> & { theme: Theme }) => ComposableButtonInteractiveConfig> = {
  [Brands.Athleta]: at,
  [Brands.BananaRepublic]: gap,
  [Brands.BananaRepublicFactoryStore]: gap,
  [Brands.Gap]: gap,
  [Brands.GapFactoryStore]: gap,
  [Brands.OldNavy]: on,
};

export const getInteractiveStyles: (
  props: Partial<ComposableButtonBaseProps> & {
    enabledFeatures?: EnabledFeaturesType;
    theme: Theme;
  }
) => CSSObject = props => {
  const config = interactiveStylesConfig;
  const brandConfig = config[props.theme.brand];
  const defaultVariant = getComposableButtonConfig(props.theme.brand, props.viewport, props.enabledFeatures)[1].variant;
  const variant = props.variant ? props.variant : defaultVariant;
  const activeCSS = brandConfig(props).active[variant];
  const cssSelector = Object.keys(activeCSS)[0];

  return {
    ...brandConfig(props).hoverFocus[variant],
    ...activeCSS,
    ...(props.selected ? (activeCSS[cssSelector] as CSSObject) : {}),
  };
};
