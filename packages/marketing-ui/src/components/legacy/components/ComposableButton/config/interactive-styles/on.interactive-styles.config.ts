// @ts-nocheck
'use client';
import { CSSObject, Theme } from '@ecom-next/core/react-stitch';
import { useContext } from 'react';
import { Color, ComposableButtonInteractiveConfig, ComposableButtonBaseProps, Variant } from '../../types';
import { getActiveButtonColor } from '../../styles/helpers';

const oldNavyInteractiveStylesConfig = (props: Partial<ComposableButtonBaseProps> & { theme: Theme }): ComposableButtonInteractiveConfig => {
  const { primary, secondary } = getActiveButtonColor(props);
  const { color, customColors, enabledFeatures, theme, useOnCtaRedesign2024 } = props;
  const isLightOrWhite = color && [Color.white, Color.light].includes(color);
  const defaultONActiveColor = isLightOrWhite ? theme.color.wh : theme.color.bk;

  const isOnCtaRedesign2024Enabled = !!enabledFeatures?.['on-cta-redesign-2024'] && useOnCtaRedesign2024;

  const onActiveColorAllThemes = () => {
    if (color === Color.custom && customColors) {
      return customColors.foregroundColor;
    }
    return isLightOrWhite ? theme.color.bk : secondary;
  };

  const underlineHoverAndFocusStyles = (): CSSObject =>
    isOnCtaRedesign2024Enabled
      ? {
          textDecorationColor: 'none',
          span: {
            backgroundColor: 'transparent',
          },
        }
      : {
          textDecorationColor: primary,
          span: {
            color: secondary,
            backgroundColor: primary,
          },
        };

  const underlineActiveStyles = (): CSSObject =>
    isOnCtaRedesign2024Enabled
      ? {
          color: 'none',
          backgroundColor: 'none',
        }
      : {
          color: onActiveColorAllThemes(),
          backgroundColor: color === Color.custom && customColors ? secondary : defaultONActiveColor,
        };

  return {
    hoverFocus: {
      [Variant.border]: {
        '&:hover, &:focus': {
          color: secondary,
          backgroundColor: primary,
          borderColor: primary,
        },
      },
      [Variant.chevron]: {
        '&:hover, &:focus': {
          color: secondary,
          backgroundColor: primary,
          '& span svg': {
            fill: secondary,
          },
        },
      },
      [Variant.flat]: {
        '&:hover, &:focus': {
          textDecoration: 'underline',
        },
      },
      [Variant.solid]: {
        '&:hover, &:focus': {
          color: primary,
          backgroundColor: secondary,
          borderColor: primary,
        },
      },
      [Variant.outline]: {
        '&:hover, &:focus': {
          color: secondary,
          backgroundColor: primary,
          borderColor: primary,
        },
      },
      [Variant.underline]: {
        '&:hover, &:focus': underlineHoverAndFocusStyles(),
      },
    },
    active: {
      [Variant.border]: {
        '&:active': {
          textDecoration: 'underline',
          color: onActiveColorAllThemes(),
          backgroundColor: color === Color.custom && customColors ? customColors.backgroundColor : defaultONActiveColor,
          borderColor: color === Color.custom && customColors ? customColors.foregroundColor : defaultONActiveColor,
        },
      },
      [Variant.chevron]: {
        'span:active': {
          color: onActiveColorAllThemes(),
          backgroundColor: color === Color.custom && customColors ? secondary : defaultONActiveColor,
          '& span svg': {
            fill: onActiveColorAllThemes(),
          },
        },
      },
      [Variant.flat]: {
        '&:active': {
          textDecoration: 'underline',
        },
      },
      [Variant.solid]: {
        '&:active': {
          textDecoration: 'underline',
          color: isLightOrWhite ? theme.color.bk : secondary,
          backgroundColor: defaultONActiveColor,
          borderColor: defaultONActiveColor,
        },
      },
      [Variant.outline]: {
        '&:active': {
          textDecoration: 'underline',
          color: defaultONActiveColor,
          backgroundColor: 'transparent',
          borderColor: defaultONActiveColor,
        },
      },
      [Variant.underline]: {
        '&:active': {
          textDecorationColor: color === Color.custom && customColors ? secondary : defaultONActiveColor,
          '& > span': {
            ...underlineActiveStyles(),
          },
        },
      },
    },
  };
};

export default oldNavyInteractiveStylesConfig;
