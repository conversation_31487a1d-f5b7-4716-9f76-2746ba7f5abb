// @ts-nocheck
'use client';
import { EnabledFeaturesType } from '@ecom-next/core/legacy/feature-flags';
import { Variant, Size, ComposableButtonConfig, VariantSizeConfig } from '../types';
import { calculateLetterSpacing } from './helpers';

type Opts = {
  fontSize: number;
  lineHeight: number;
  tracking: number;
  horizontalPadding?: string;
};
function createButtonConfig({ fontSize, lineHeight, tracking, horizontalPadding }: Opts) {
  return (minHeight?: number): VariantSizeConfig => ({
    fontSize: `${fontSize}px`,
    lineHeight: lineHeight / fontSize,
    letterSpacing: calculateLetterSpacing(tracking, fontSize),
    minHeight: minHeight ? `${minHeight}px` : 'auto',
    // 4 is current border size (2 / 2)
    padding: [minHeight ? `${(minHeight - 4 - lineHeight) / 2}px` : '0', horizontalPadding || '0'],
  });
}

const buttonStyleSmallFn = {
  [Size.xl]: createButtonConfig({
    fontSize: 18,
    lineHeight: 20,
    tracking: 120,
    horizontalPadding: '22px',
  }),
  [Size.large]: createButtonConfig({
    fontSize: 16,
    lineHeight: 18,
    tracking: 120,
    horizontalPadding: '22px',
  }),
  [Size.medium]: createButtonConfig({
    fontSize: 14,
    lineHeight: 16,
    tracking: 120,
    horizontalPadding: '22px',
  }),
  [Size.small]: createButtonConfig({
    fontSize: 12,
    lineHeight: 14,
    tracking: 120,
    horizontalPadding: '22px',
  }),
  [Size.xs]: createButtonConfig({
    fontSize: 10,
    lineHeight: 12,
    tracking: 120,
    horizontalPadding: '22px',
  }),
};

const chevronDefaultFn = {
  [Size.xl]: createButtonConfig({
    fontSize: 18,
    lineHeight: 22,
    tracking: 100,
    horizontalPadding: '22px',
  }),
  [Size.large]: createButtonConfig({
    fontSize: 16,
    lineHeight: 20,
    tracking: 100,
    horizontalPadding: '22px',
  }),
  [Size.medium]: createButtonConfig({
    fontSize: 14,
    lineHeight: 18,
    tracking: 75,
    horizontalPadding: '22px',
  }),
  [Size.small]: createButtonConfig({
    fontSize: 12,
    lineHeight: 16,
    tracking: 75,
    horizontalPadding: '22px',
  }),
  [Size.xs]: createButtonConfig({
    fontSize: 10,
    lineHeight: 14,
    tracking: 75,
    horizontalPadding: '22px',
  }),
};

const underlineDefaultFn = {
  [Size.xl]: createButtonConfig({
    fontSize: 18,
    lineHeight: 30,
    tracking: 100,
    horizontalPadding: '22px',
  }),
  [Size.large]: createButtonConfig({
    fontSize: 16,
    lineHeight: 28,
    tracking: 100,
    horizontalPadding: '22px',
  }),
  [Size.medium]: createButtonConfig({
    fontSize: 14,
    lineHeight: 26,
    tracking: 75,
    horizontalPadding: '22px',
  }),
  [Size.small]: createButtonConfig({
    fontSize: 12,
    lineHeight: 24,
    tracking: 75,
    horizontalPadding: '22px',
  }),
  [Size.xs]: createButtonConfig({
    fontSize: 10,
    lineHeight: 22,
    tracking: 75,
    horizontalPadding: '22px',
  }),
};

const oldNavyButtonRedesign2024: VariantSizeConfig = {
  fontSize: '14px',
  fontWeight: 'bold',
  lineHeight: '14px',
  letterSpacing: calculateLetterSpacing(120, 14),
  minHeight: '44px',
  maxHeight: '44px',
  padding: ['6px'],
};

const oldNavyComposableButtonConfig = (enabledFeatures?: EnabledFeaturesType): ComposableButtonConfig => {
  const isOnCtaRedesign2024Enabled = !!enabledFeatures?.['on-cta-redesign-2024'];

  const oldNavyMobileBorderAndSolidMedium: VariantSizeConfig = isOnCtaRedesign2024Enabled
    ? oldNavyButtonRedesign2024
    : {
        ...buttonStyleSmallFn.medium(44),
        fontWeight: 'bold',
      };
  const oldNavyDesktopBorderAndSolidMedium: VariantSizeConfig = isOnCtaRedesign2024Enabled
    ? oldNavyButtonRedesign2024
    : {
        ...buttonStyleSmallFn.medium(48),
        fontWeight: 'bold',
      };

  return {
    defaults: {
      size: Size.medium,
      variant: Variant.solid,
    },
    small: {
      [Variant.border]: {
        [Size.xl]: {
          ...buttonStyleSmallFn.xl(48),
          fontWeight: 'bold',
        },
        [Size.large]: {
          ...buttonStyleSmallFn.large(46),
          fontWeight: 'bold',
        },
        [Size.medium]: {
          ...oldNavyMobileBorderAndSolidMedium,
        },
        [Size.small]: {
          ...buttonStyleSmallFn.small(44),
          fontWeight: 'bold',
        },
        [Size.xs]: {
          ...buttonStyleSmallFn.xs(44),
          fontWeight: 'bold',
        },
      },
      [Variant.chevron]: {
        [Size.xl]: {
          ...chevronDefaultFn.xl(44),
          fontWeight: 'bold',
          padding: ['0px'],
        },
        [Size.large]: {
          ...chevronDefaultFn.large(44),
          fontWeight: 'bold',
          padding: ['0px'],
        },
        [Size.medium]: {
          ...chevronDefaultFn.medium(44),
          fontWeight: 'bold',
          padding: ['0px'],
        },
        [Size.small]: {
          ...chevronDefaultFn.small(44),
          fontWeight: 'bold',
          padding: ['0px'],
        },
        [Size.xs]: {
          ...chevronDefaultFn.xs(44),
          fontWeight: 'bold',
          padding: ['0px'],
        },
      },
      [Variant.flat]: {
        [Size.xl]: {
          ...chevronDefaultFn.xl(44),
          fontWeight: 'bold',
          padding: ['0px'],
        },
        [Size.large]: {
          ...chevronDefaultFn.large(44),
          fontWeight: 'bold',
          padding: ['0px'],
        },
        [Size.medium]: {
          ...chevronDefaultFn.medium(44),
          fontWeight: 'bold',
          padding: ['0px'],
        },
        [Size.small]: {
          ...chevronDefaultFn.small(44),
          fontWeight: 'bold',
          padding: ['0px'],
        },
        [Size.xs]: {
          ...chevronDefaultFn.xs(44),
          fontWeight: 'bold',
          padding: ['0px'],
        },
      },
      [Variant.outline]: {
        [Size.xl]: {
          ...buttonStyleSmallFn.xl(48),
          fontWeight: 'bold',
        },
        [Size.large]: {
          ...buttonStyleSmallFn.large(46),
          fontWeight: 'bold',
        },
        [Size.medium]: {
          ...buttonStyleSmallFn.medium(44),
          fontWeight: 'bold',
        },
        [Size.small]: {
          ...buttonStyleSmallFn.small(44),
          fontWeight: 'bold',
        },
        [Size.xs]: {
          ...buttonStyleSmallFn.xs(44),
          fontWeight: 'bold',
        },
      },
      [Variant.solid]: {
        [Size.xl]: {
          ...buttonStyleSmallFn.xl(48),
          fontWeight: 'bold',
        },
        [Size.large]: {
          ...buttonStyleSmallFn.large(46),
          fontWeight: 'bold',
        },
        [Size.medium]: {
          ...oldNavyMobileBorderAndSolidMedium,
        },
        [Size.small]: {
          ...buttonStyleSmallFn.small(44),
          fontWeight: 'bold',
        },
        [Size.xs]: {
          ...buttonStyleSmallFn.xs(44),
          fontWeight: 'bold',
        },
      },
      [Variant.underline]: {
        [Size.xl]: {
          ...underlineDefaultFn.xl(44),
          fontWeight: 'bold',
          padding: ['0px'],
        },
        [Size.large]: {
          ...underlineDefaultFn.large(44),
          fontWeight: 'bold',
          padding: ['0px'],
        },
        [Size.medium]: {
          ...underlineDefaultFn.medium(44),
          fontWeight: 'bold',
          padding: ['0px'],
        },
        [Size.small]: {
          ...underlineDefaultFn.small(44),
          fontWeight: 'bold',
          padding: ['0px'],
        },
        [Size.xs]: {
          ...underlineDefaultFn.xs(44),
          fontWeight: 'bold',
          padding: ['0px'],
        },
      },
    },
    'x-large': {
      [Variant.border]: {
        [Size.xl]: {
          ...buttonStyleSmallFn.xl(52),
          fontWeight: 'bold',
        },
        [Size.large]: {
          ...buttonStyleSmallFn.large(50),
          fontWeight: 'bold',
        },
        [Size.medium]: {
          ...oldNavyDesktopBorderAndSolidMedium,
        },
        [Size.small]: {
          ...buttonStyleSmallFn.small(46),
          fontWeight: 'bold',
        },
        [Size.xs]: {
          ...buttonStyleSmallFn.xs(44),
          fontWeight: 'bold',
        },
      },
      [Variant.chevron]: {
        [Size.xl]: {
          ...chevronDefaultFn.xl(),
          fontWeight: 'bold',
        },
        [Size.large]: {
          ...chevronDefaultFn.large(),
          fontWeight: 'bold',
        },
        [Size.medium]: {
          ...chevronDefaultFn.medium(),
          fontWeight: 'bold',
        },
        [Size.small]: {
          ...chevronDefaultFn.small(),
          fontWeight: 'bold',
        },
        [Size.xs]: {
          ...chevronDefaultFn.xs(),
          fontWeight: 'bold',
        },
      },
      [Variant.flat]: {
        [Size.xl]: {
          ...chevronDefaultFn.xl(),
          fontWeight: 'bold',
        },
        [Size.large]: {
          ...chevronDefaultFn.large(),
          fontWeight: 'bold',
        },
        [Size.medium]: {
          ...chevronDefaultFn.medium(),
          fontWeight: 'bold',
        },
        [Size.small]: {
          ...chevronDefaultFn.small(),
          fontWeight: 'bold',
        },
        [Size.xs]: {
          ...chevronDefaultFn.xs(),
          fontWeight: 'bold',
        },
      },
      [Variant.outline]: {
        [Size.xl]: {
          ...buttonStyleSmallFn.xl(52),
          fontWeight: 'bold',
        },
        [Size.large]: {
          ...buttonStyleSmallFn.large(50),
          fontWeight: 'bold',
        },
        [Size.medium]: {
          ...buttonStyleSmallFn.medium(48),
          fontWeight: 'bold',
        },
        [Size.small]: {
          ...buttonStyleSmallFn.small(46),
          fontWeight: 'bold',
        },
        [Size.xs]: {
          ...buttonStyleSmallFn.xs(44),
          fontWeight: 'bold',
        },
      },
      [Variant.solid]: {
        [Size.xl]: {
          ...buttonStyleSmallFn.xl(52),
          fontWeight: 'bold',
        },
        [Size.large]: {
          ...buttonStyleSmallFn.large(50),
          fontWeight: 'bold',
        },
        [Size.medium]: {
          ...oldNavyDesktopBorderAndSolidMedium,
        },
        [Size.small]: {
          ...buttonStyleSmallFn.small(46),
          fontWeight: 'bold',
        },
        [Size.xs]: {
          ...buttonStyleSmallFn.xs(44),
          fontWeight: 'bold',
        },
      },
      [Variant.underline]: {
        [Size.xl]: {
          ...underlineDefaultFn.xl(),
          fontWeight: 'bold',
        },
        [Size.large]: {
          ...underlineDefaultFn.large(),
          fontWeight: 'bold',
        },
        [Size.medium]: {
          ...underlineDefaultFn.medium(),
          fontWeight: 'bold',
        },
        [Size.small]: {
          ...underlineDefaultFn.small(),
          fontWeight: 'bold',
        },
        [Size.xs]: {
          ...underlineDefaultFn.xs(),
          fontWeight: 'bold',
        },
      },
    },
  };
};

export default oldNavyComposableButtonConfig;
