// @ts-nocheck
'use client';
import { CtaButtonStylingProps } from '../../CMS/subcomponents/CTAButton';
import { StylesObject } from '../ComposableButtonBR/types';
import { Color, Variant } from './types';

export const isComposableButtonData = (stylingObject: CtaButtonStylingProps | StylesObject): boolean => {
  const { buttonColor, buttonStyle } = stylingObject;
  const colors: string[] = Object.values(Color);
  const isValidColor = Boolean(buttonColor && colors.includes(buttonColor));
  const variants: string[] = Object.values(Variant);
  const isValidStyle = Boolean(buttonStyle && variants.includes(buttonStyle));
  return (isValidColor && isValidStyle) || isValidColor || isValidStyle;
};
