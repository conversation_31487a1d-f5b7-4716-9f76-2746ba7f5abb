// @ts-nocheck
import React from 'react';
import { render, screen, act } from 'test-utils';
import ComposableButton, { ComposableButtonProps } from '.';

const json: ComposableButtonProps = {
  children: 'Click Me',
};

describe('<ComposableButton />', () => {
  it('should render without crashing', () => {
    render(<ComposableButton data={json} />);
    expect(screen.queryByText(json.children)).toBeInTheDocument();
  });
});
