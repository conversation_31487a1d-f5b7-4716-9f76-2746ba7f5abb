// @ts-nocheck
'use client';
import React, { useState, useContext } from 'react';
import { LinkProps } from '@ecom-next/core/migration/link';
import { CSSObject, useTheme } from '@ecom-next/core/react-stitch';
import { BreakpointContext, LARGE } from '@ecom-next/core/breakpoint-provider';
import { ComposableButton as CoreComposableButton, ComposableButtonProps as BaseComposableButtonProps } from './components';
import { jumpToSection } from '../../helper/jumpToSection';
import { mapDataToProps } from '../../helper';
import { CommonStyleProps } from '../../types';
import { IframeModal } from './IframeModal';
import { ModalProps } from './types';

export type ComposableButtonProps = Omit<BaseComposableButtonProps, 'children'> &
  CommonStyleProps & {
    modalProps?: ModalProps;
    linkProps?: LinkProps & {
      isAJumplink?: boolean;
      jumplinkCSSSelector?: string;
    };
    children: string;
    buttonText?: string;
    desktopProps?: BaseComposableButtonProps;
    selected?: boolean;
  };

export function ComposableButton({
  buttonText,
  children,
  modalProps,
  linkProps,
  style,
  desktopStyle,
  desktopProps,
  selected = false,
  onClick,
  ...rest
}: ComposableButtonProps): JSX.Element {
  const theme = useTheme();
  const { minWidth } = useContext(BreakpointContext);
  const isDesktop = minWidth(LARGE);
  const [isModalOpen, setIsModalOpen] = useState(Boolean(modalProps?.isOpen));

  let buttonLinkProps = {};
  if (linkProps) {
    const { to, ...restLinkProps } = linkProps;
    buttonLinkProps = {
      as: 'a',
      href: to,
      ...restLinkProps,
      // Jump Links need target _self so they don't navigate
      target: restLinkProps.isAJumplink ? '_self' : restLinkProps.target,
    };
  }

  const breakpointProps = {
    children,
    ...rest,
    ...(isDesktop ? desktopProps : {}),
  };

  const text: string = children || buttonText || '';

  const removeAsterisk = (text: string) => text.replace('*', '');

  const onModalBtnStyles: CSSObject = modalProps && theme.brand === 'on' ? { textTransform: 'capitalize' } : {};

  return (
    <>
      {modalProps && <IframeModal {...modalProps} isOpen={isModalOpen} onClose={() => setIsModalOpen(false)} />}
      <CoreComposableButton
        {...breakpointProps}
        {...buttonLinkProps}
        css={[{ ...style, ...onModalBtnStyles }, isDesktop && { ...desktopStyle, ...onModalBtnStyles }]}
        onClick={event => {
          if (linkProps && linkProps.isAJumplink) {
            jumpToSection(linkProps.isAJumplink, linkProps.jumplinkCSSSelector);
          }
          if (onClick) {
            onClick(event);
          } else {
            setIsModalOpen(true);
          }
        }}
        selected={selected}
      >
        {typeof text === 'string' && (text.startsWith('*') || text.endsWith('*')) ? (
          <span>
            {text.startsWith('*') ? '*' : ''}
            <span
              css={{
                textDecoration: 'underline',
                ':hover': {
                  textDecoration: 'none',
                },
              }}
            >
              {removeAsterisk(text)}
            </span>
            {text.endsWith('*') ? '*' : ''}
          </span>
        ) : (
          <>{text}</>
        )}
      </CoreComposableButton>
    </>
  );
}

export default mapDataToProps(ComposableButton);
