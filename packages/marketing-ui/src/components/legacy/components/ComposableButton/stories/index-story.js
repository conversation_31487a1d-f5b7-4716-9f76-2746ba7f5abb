'use client';
import React from 'react';
import ComposableButton from '..';
import README from '../README.mdx';
import { OnCtaRedesign2024Context } from '../../../contexts/OnCtaRedesign2024Context';

const buttonProps = {
  borderThickness: 'medium',
  bright: false,
  capitalization: 'uppercase',
  className: '',
  color: 'primary',
  crossBrand: false,
  font: 'secondary',
  fullWidth: false,
  size: 'large',
  variant: 'solid',
  buttonText: 'Join Now!',
};

export default {
  title: 'Common/JSON Components (Marketing)/ComposableButton',
  component: ComposableButton,
  parameters: {
    docs: {
      page: README,
    },
    knobs: { disable: true },
  },
  tags: ['exclude-br'],
};

const ComposableButtonTemplate = args => {
  const backgroundStyles = {
    backgroundColor: '#CCCCCC',
    padding: '10px',
  };

  const linkProps = {
    href: 'https://www.gap.com',
    target: '_blank',
  };
  const linkButtonText = 'Go to gap homepage';
  const linkData = {
    ...args,
    linkProps,
    linkButtonText,
  };

  const modalProps = {
    src: 'https://www.gap.com/Asset_Archive/GPWeb/content/static-marketing/xbrand-edfs-content/edfsLegal-GP-FS25.html?v=0',
    height: '700px',
    width: '100%',
    closeButtonAriaLabel: 'close the edfs modal',
    title: 'every day free shipping',
  };
  const modalButtonText = 'Open Edfs Modal';
  const modalIFrameOpenerData = {
    modalProps,
    ...args,
    modalButtonText,
  };

  return (
    <OnCtaRedesign2024Context.Provider value={{ enable: true }}>
      <div>
        <h1 style={{ fontSize: '32px', padding: '20px' }}>Composable Button</h1>

        <p style={{ padding: '10px' }}>Default</p>
        <div style={backgroundStyles}>
          <ComposableButton {...args} />
        </div>

        <p style={{ padding: '10px' }}>Disabled</p>
        <div style={backgroundStyles}>
          <ComposableButton {...{ ...args, disabled: true }} />
        </div>

        <p style={{ padding: '10px' }}>Link</p>
        <div style={backgroundStyles}>
          <ComposableButton {...linkData} />
        </div>

        <p style={{ padding: '10px' }}>Modal</p>
        <div style={backgroundStyles}>
          <ComposableButton {...modalIFrameOpenerData} />
        </div>

        <p style={{ padding: '10px' }}>With Interactive Styles</p>
        <div style={backgroundStyles}>
          <ComposableButton {...{ ...args, interactiveStyles: true }} />
        </div>
      </div>
    </OnCtaRedesign2024Context.Provider>
  );
};
export const Default = ComposableButtonTemplate.bind({});
Default.args = {
  data: { ...buttonProps },
};
