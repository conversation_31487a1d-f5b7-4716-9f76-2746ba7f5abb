// @ts-ignore
import { Theme, Brands } from '@ecom-next/core/react-stitch';
import { Color, Variant } from '../../types';
import {
  defaultStyle,
  setTextAlign,
  setFullWidth,
  setUnderlineStyling,
  getActiveButtonColor,
  getUnderlineStyling,
  setBorderButtonStyles,
  setFlatButtonStyles,
  setChevronButtonStyles,
  composableActiveButtonStateStyles,
  setOutlineButtonStyles,
  setSolidButtonStyles,
  setUnderlineButtonStyles,
} from '../helpers';

function mockTheme(brand: Brands = Brands.Gap): Theme {
  return {
    brand,
    color: { wh: '#FFFFFF', bk: '#000000', b1: '#000000', alpha00: 'transparent' },
  } as Theme;
}

describe('composable-button style helpers', () => {
  describe('defaultStyle', () => {
    const crossBrand = true;
    it('should declare AT defaultStyle used', () => {
      expect(defaultStyle(mockTheme(Brands.Athleta), !crossBrand)).toEqual({
        horizontalPadding: '0.8em',
        verticalPadding: '0.5em',
        borderWidth: 1.5,
      });
    });
    it('should declare Gap defaultStyle used', () => {
      expect(defaultStyle(mockTheme(Brands.Gap), !crossBrand)).toEqual({
        horizontalPadding: '0.8em',
        verticalPadding: '0.5em',
        borderWidth: 1,
      });
    });
    it('should declare crossBrand defaultStyle used', () => {
      expect(defaultStyle(mockTheme(Brands.Athleta), crossBrand)).toEqual({
        horizontalPadding: '0.8em',
        verticalPadding: '0.5em',
        borderWidth: 2,
      });
    });
  });

  describe('setTextAlign', () => {
    it('should have text-align center', () => {
      expect(setTextAlign({ theme: mockTheme(Brands.Athleta), variant: Variant.solid })).toEqual({
        textAlign: 'center',
      });
      expect(
        setTextAlign({
          theme: mockTheme(Brands.Athleta),
          variant: Variant.border,
        })
      ).toEqual({
        textAlign: 'center',
      });
      expect(
        setTextAlign({
          theme: mockTheme(Brands.Athleta),
          variant: Variant.outline,
        })
      ).toEqual({
        textAlign: 'center',
      });
      expect(setTextAlign({ theme: mockTheme(Brands.Athleta), variant: Variant.flat })).toEqual({
        textAlign: 'center',
      });
    });

    it('should have text-align left', () => {
      expect(
        setTextAlign({
          theme: mockTheme(Brands.Athleta),
          variant: Variant.chevron,
        })
      ).toEqual({
        textAlign: 'left',
      });
      expect(
        setTextAlign({
          theme: mockTheme(Brands.Athleta),
          variant: Variant.underline,
        })
      ).toEqual({
        textAlign: 'left',
      });
    });
  });

  describe('setFullWidth', () => {
    it('should set width to 100%', () => {
      expect(setFullWidth({ theme: mockTheme(Brands.Athleta), fullWidth: true })).toEqual({
        width: '100%',
      });
    });

    it('should not set width', () => {
      expect(setFullWidth({ theme: mockTheme(Brands.Athleta), fullWidth: false })).toEqual({});
    });
  });

  describe('setUnderlineStyling', () => {
    it('should set the underline styling for unsupported brand', () => {
      expect(setUnderlineStyling({ theme: mockTheme('' as Brands) })).toEqual({
        textDecoration: 'underline',
        textUnderlineOffset: '0px',
        textDecorationThickness: '1px',
      });
    });

    it('should set the underline styling for AT', () => {
      expect(setUnderlineStyling({ theme: mockTheme(Brands.Athleta) })).toEqual({
        textDecoration: 'underline',
        textUnderlineOffset: '5px',
        textDecorationThickness: '1px',
      });
    });

    it('should set the underline styling for ON', () => {
      expect(setUnderlineStyling({ theme: mockTheme(Brands.OldNavy) })).toEqual({
        textDecoration: 'underline',
        textUnderlineOffset: '3.5px',
        textDecorationThickness: '3px',
      });
    });
  });

  describe('getActiveButtonColor', () => {
    it('should get the active colors with only a brand passed', () => {
      const result = getActiveButtonColor({
        theme: mockTheme(Brands.Gap),
      });
      expect(result).toEqual({
        primary: '#FFFFFF',
        secondary: '#000000',
      });
    });

    it('should get active color for brand without custom color', () => {
      expect(
        getActiveButtonColor({
          theme: mockTheme(Brands.Gap),
          variant: Variant.solid,
          color: Color.dark,
        })
      ).toEqual({
        primary: '#FFFFFF',
        secondary: '#000000',
      });
    });

    it('should get active color for brand custom color but not `Color.custom`', () => {
      expect(
        getActiveButtonColor({
          theme: mockTheme(Brands.Gap),
          variant: Variant.solid,
          color: Color.dark,
          customColors: {
            backgroundColor: 'red',
            foregroundColor: 'yellow',
          },
        })
      ).toEqual({
        primary: '#FFFFFF',
        secondary: '#000000',
      });
    });

    it('should get the active color for GAP with custom color', () => {
      const expected = {
        primary: 'yellow',
        secondary: 'red',
      };
      expect(
        getActiveButtonColor({
          theme: mockTheme(Brands.Gap),
          variant: Variant.solid,
          color: Color.custom,
          customColors: {
            backgroundColor: 'red',
            foregroundColor: 'yellow',
          },
        })
      ).toEqual(expected);

      expect(
        getActiveButtonColor({
          theme: mockTheme(Brands.Gap),
          variant: Variant.border,
          color: Color.custom,
          customColors: {
            backgroundColor: 'red',
            foregroundColor: 'yellow',
          },
        })
      ).toEqual(expected);

      expect(
        getActiveButtonColor({
          theme: mockTheme(Brands.Gap),
          variant: Variant.flat,
          color: Color.custom,
          customColors: {
            backgroundColor: 'red',
            foregroundColor: 'yellow',
          },
        })
      ).toEqual(expected);
    });
  });

  describe('getUnderlineStyling', () => {
    it('should get a default underline style', () => {
      expect(getUnderlineStyling({ theme: mockTheme('' as unknown as Brands) })).toEqual({
        textDecoration: 'underline',
        textUnderlineOffset: '0px',
        textDecorationThickness: '1px',
      });
    });

    it('should get a underline style for each brand', () => {
      expect(getUnderlineStyling({ theme: mockTheme(Brands.Gap) })).toEqual({
        textDecoration: 'underline',
        textUnderlineOffset: '0px',
        textDecorationThickness: '1px',
      });

      expect(getUnderlineStyling({ theme: mockTheme(Brands.Athleta) })).toEqual({
        textDecoration: 'underline',
        textUnderlineOffset: '5px',
        textDecorationThickness: '1px',
      });

      expect(getUnderlineStyling({ theme: mockTheme(Brands.OldNavy) })).toEqual({
        textDecoration: 'underline',
        textUnderlineOffset: '3.5px',
        textDecorationThickness: '3px',
      });
    });
  });

  describe('setBorderButtonStyles', () => {
    it('should set the base styles for border button variant (custom)', () => {
      expect(
        setBorderButtonStyles({
          theme: mockTheme(Brands.Gap),
          color: Color.custom,
          customColors: { foregroundColor: 'yellow', backgroundColor: 'red' },
        })
      ).toEqual({
        backgroundColor: 'red',
        borderColor: 'yellow',
        color: 'yellow',
      });
    });

    it('should set the base styles for border button variant (default/dark)', () => {
      expect(setBorderButtonStyles({ theme: mockTheme(Brands.Gap) })).toEqual({
        backgroundColor: '#FFFFFF',
        borderColor: '#000000',
        color: '#000000',
      });

      expect(setBorderButtonStyles({ theme: mockTheme(Brands.Gap), color: Color.dark })).toEqual({
        backgroundColor: '#FFFFFF',
        borderColor: '#000000',
        color: '#000000',
      });
    });

    it('should set the base styles for border button variant (light/white)', () => {
      expect(
        setBorderButtonStyles({
          theme: mockTheme(Brands.Gap),
          color: Color.white,
        })
      ).toEqual({
        backgroundColor: '#000000',
        borderColor: '#FFFFFF',
        color: '#FFFFFF',
      });

      expect(
        setBorderButtonStyles({
          theme: mockTheme(Brands.Gap),
          color: Color.light,
        })
      ).toEqual({
        backgroundColor: '#000000',
        borderColor: '#FFFFFF',
        color: '#FFFFFF',
      });
    });
  });

  describe('setFlatButtonStyles', () => {
    it('should set the base styles for flat button variant (custom)', () => {
      expect(
        setFlatButtonStyles({
          theme: mockTheme(Brands.Gap),
          color: Color.custom,
          customColors: {
            foregroundColor: 'red',
            backgroundColor: 'yellow',
          },
        })
      ).toEqual({
        position: 'relative',
        border: 'none',
        backgroundColor: 'transparent',
        color: 'red',
        '&::after': {
          content: "''",
          position: 'absolute',
          bottom: '-1px',
          left: '0.8em',
          right: '0.8em',
          backgroundColor: 'transparent',
          display: 'block',
          height: '1px',
        },
      });
    });

    it('should set the base styles for flat button variant (light/white)', () => {
      expect(setFlatButtonStyles({ theme: mockTheme(Brands.Gap), color: Color.white })).toEqual({
        position: 'relative',
        border: 'none',
        backgroundColor: 'transparent',
        color: '#FFFFFF',
        '&::after': {
          content: "''",
          position: 'absolute',
          bottom: '-1px',
          left: '0.8em',
          right: '0.8em',
          backgroundColor: 'transparent',
          display: 'block',
          height: '1px',
        },
      });

      expect(setFlatButtonStyles({ theme: mockTheme(Brands.Gap), color: Color.light })).toEqual({
        position: 'relative',
        border: 'none',
        backgroundColor: 'transparent',
        color: '#FFFFFF',
        '&::after': {
          content: "''",
          position: 'absolute',
          bottom: '-1px',
          left: '0.8em',
          right: '0.8em',
          backgroundColor: 'transparent',
          display: 'block',
          height: '1px',
        },
      });
    });

    it('should set the base styles for flat button variant (black/dark)', () => {
      expect(setFlatButtonStyles({ theme: mockTheme(Brands.Gap), color: Color.black })).toEqual({
        position: 'relative',
        border: 'none',
        backgroundColor: 'transparent',
        color: '#000000',
        '&::after': {
          content: "''",
          position: 'absolute',
          bottom: '-1px',
          left: '0.8em',
          right: '0.8em',
          backgroundColor: 'transparent',
          display: 'block',
          height: '1px',
        },
      });

      expect(setFlatButtonStyles({ theme: mockTheme(Brands.Gap), color: Color.dark })).toEqual({
        position: 'relative',
        border: 'none',
        backgroundColor: 'transparent',
        color: '#000000',
        '&::after': {
          content: "''",
          position: 'absolute',
          bottom: '-1px',
          left: '0.8em',
          right: '0.8em',
          backgroundColor: 'transparent',
          display: 'block',
          height: '1px',
        },
      });
    });
  });

  describe('setSolidButtonStyles', () => {
    it('should get default solid button styles for Gap', () => {
      expect(setSolidButtonStyles({ theme: mockTheme(Brands.Gap) })).toEqual({
        backgroundColor: '#FFFFFF',
        borderColor: '#FFFFFF',
        color: '#000000',
      });
    });
  });

  describe('setOutlineButtonStyles', () => {
    it('should get default outline button styles for Gap', () => {
      expect(setOutlineButtonStyles({ theme: mockTheme(Brands.Gap) })).toEqual({
        background: 'transparent',
        borderColor: '#000000',
        color: '#000000',
      });
    });
  });

  describe('setUnderlineButtonStyles', () => {
    it('should get default chevron button styles for Gap', () => {
      expect(setUnderlineButtonStyles({ theme: mockTheme(Brands.Gap) })).toEqual({
        borderLeft: 'none',
        borderRight: 'none',
        borderRadius: '0',
        backgroundColor: 'transparent',
        borderBottom: '1px solid currentColor',
        color: '#000000',
        height: 'auto',
        justifyContent: 'left',
        padding: 0,
        textAlign: 'left',
      });
    });
  });

  describe('setChevronButtonStyles', () => {
    it('should get default chevron button styles for Gap', () => {
      expect(setChevronButtonStyles({ theme: mockTheme(Brands.Gap) })).toEqual({
        '& span': {
          'svg path': {
            fill: '#000000',
            transition: 'fill 250ms ease-in-out',
          },
        },
        borderRadius: 0,
        border: 'none',
        backgroundColor: 'transparent',
        color: '#000000',
        height: 'auto',
        justifyContent: 'left',
        padding: 0,
        textAlign: 'left',
      });
    });

    it('should get chevron button styles for Athleta', () => {
      expect(setChevronButtonStyles({ theme: mockTheme(Brands.Athleta) })).toEqual({
        '& span': {
          span: {
            alignItems: 'center',
            minHeight: 'auto',
            minWidth: 'auto',
            paddingLeft: '3px',
            position: 'relative',
            svg: {
              fill: '#000000',
              transition: 'fill 250ms ease-in-out',
            },
            top: '0',
            transition: '250ms ease-in-out',
            transitionProperty: 'height, width, margin',
            width: 'calc(undefined * 0.72)',
          },
        },
        backgroundColor: 'transparent',
        border: 'none',
        borderRadius: 0,
        color: '#000000',
        height: 'auto',
        justifyContent: 'left',
        padding: 0,
        textAlign: 'left',
      });
    });
  });

  describe('composableActiveButtonStateStyles', () => {
    describe('Gap Brand', () => {
      describe('color:dark', () => {
        it('variant:border', () => {
          expect(
            composableActiveButtonStateStyles({
              theme: mockTheme(),
              variant: Variant.border,
              color: Color.dark,
            })
          ).toEqual({
            color: '#FFFFFF',
            borderColor: '#000000',
            backgroundColor: '#000000',
          });
        });

        it('variant:chevron', () => {
          expect(
            composableActiveButtonStateStyles({
              theme: mockTheme(),
              variant: Variant.chevron,
              color: Color.dark,
            })
          ).toEqual({
            color: '#FFFFFF',
            backgroundColor: '#000000',
            'span svg': {
              fill: '#FFFFFF',
            },
          });
        });

        it('variant:flat', () => {
          expect(
            composableActiveButtonStateStyles({
              theme: mockTheme(),
              variant: Variant.flat,
              color: Color.dark,
            })
          ).toEqual({
            textDecoration: 'underline',
          });
        });

        it('variant:outline', () => {
          expect(
            composableActiveButtonStateStyles({
              theme: mockTheme(),
              variant: Variant.outline,
              color: Color.dark,
            })
          ).toEqual({
            color: '#FFFFFF',
            borderColor: '#000000',
            backgroundColor: '#000000',
          });
        });

        it('variant:underline', () => {
          expect(
            composableActiveButtonStateStyles({
              theme: mockTheme(),
              variant: Variant.underline,
              color: Color.dark,
            })
          ).toEqual({
            color: '#FFFFFF',
            borderBottomColor: '#000000',
            backgroundColor: '#000000',
          });
        });

        it('variant:solid', () => {
          expect(
            composableActiveButtonStateStyles({
              theme: mockTheme(),
              variant: Variant.solid,
              color: Color.dark,
            })
          ).toEqual({
            color: '#000000',
            borderColor: '#000000',
            backgroundColor: '#FFFFFF',
          });
        });
      });

      describe('color:light', () => {
        it('variant:border', () => {
          expect(
            composableActiveButtonStateStyles({
              theme: mockTheme(),
              variant: Variant.border,
              color: Color.light,
            })
          ).toEqual({
            color: '#000000',
            borderColor: '#FFFFFF',
            backgroundColor: '#FFFFFF',
          });
        });

        it('variant:chevron', () => {
          expect(
            composableActiveButtonStateStyles({
              theme: mockTheme(),
              variant: Variant.chevron,
              color: Color.light,
            })
          ).toEqual({
            color: '#000000',
            backgroundColor: '#FFFFFF',
            'span svg': {
              fill: '#000000',
            },
          });
        });

        it('variant:flat', () => {
          expect(
            composableActiveButtonStateStyles({
              theme: mockTheme(),
              variant: Variant.flat,
              color: Color.light,
            })
          ).toEqual({
            textDecoration: 'underline',
          });
        });

        it('variant:outline', () => {
          expect(
            composableActiveButtonStateStyles({
              theme: mockTheme(),
              variant: Variant.outline,
              color: Color.light,
            })
          ).toEqual({
            color: '#000000',
            borderColor: '#FFFFFF',
            backgroundColor: '#FFFFFF',
          });
        });

        it('variant:underline', () => {
          expect(
            composableActiveButtonStateStyles({
              theme: mockTheme(),
              variant: Variant.underline,
              color: Color.light,
            })
          ).toEqual({
            color: '#000000',
            borderBottomColor: '#FFFFFF',
            backgroundColor: '#FFFFFF',
          });
        });

        it('variant:solid', () => {
          expect(
            composableActiveButtonStateStyles({
              theme: mockTheme(),
              variant: Variant.solid,
              color: Color.light,
            })
          ).toEqual({
            color: '#FFFFFF',
            borderColor: '#FFFFFF',
            backgroundColor: '#000000',
          });
        });
      });
    });

    describe('color:custom', () => {
      it('variant:border', () => {
        expect(
          composableActiveButtonStateStyles({
            theme: mockTheme(),
            variant: Variant.border,
            color: Color.custom,
            customColors: { foregroundColor: 'red', backgroundColor: 'yellow' },
          })
        ).toEqual({
          color: 'yellow',
          borderColor: 'yellow',
          backgroundColor: 'red',
        });
      });

      it('variant:outline', () => {
        expect(
          composableActiveButtonStateStyles({
            theme: mockTheme(),
            variant: Variant.outline,
            color: Color.custom,
            customColors: { foregroundColor: 'red', backgroundColor: 'yellow' },
          })
        ).toEqual({
          color: 'yellow',
          borderColor: 'yellow',
          backgroundColor: 'red',
        });
      });

      it('variant:flat', () => {
        expect(
          composableActiveButtonStateStyles({
            theme: mockTheme(),
            variant: Variant.flat,
            color: Color.custom,
            customColors: { foregroundColor: 'red', backgroundColor: 'yellow' },
          })
        ).toEqual({
          textDecoration: 'underline',
        });
      });

      it('variant:chevron', () => {
        expect(
          composableActiveButtonStateStyles({
            theme: mockTheme(),
            variant: Variant.chevron,
            color: Color.custom,
            customColors: { foregroundColor: 'red', backgroundColor: 'yellow' },
          })
        ).toEqual({
          'span svg': {
            fill: 'yellow',
          },
          color: 'yellow',
          backgroundColor: 'red',
        });
      });

      it('variant:solid', () => {
        expect(
          composableActiveButtonStateStyles({
            theme: mockTheme(),
            variant: Variant.solid,
            color: Color.custom,
            customColors: { foregroundColor: 'red', backgroundColor: 'yellow' },
          })
        ).toEqual({
          color: 'yellow',
          borderColor: 'yellow',
          backgroundColor: 'red',
        });
      });

      it('should get default active styles for solid button (custom colors)', () => {
        expect(
          composableActiveButtonStateStyles({
            theme: mockTheme('' as unknown as Brands),
            variant: Variant.solid,
            color: Color.custom,
            customColors: { foregroundColor: 'red', backgroundColor: 'yellow' },
          })
        ).toEqual({
          color: 'yellow',
          borderColor: 'yellow',
          backgroundColor: 'red',
        });
      });

      it('should get active styles for Athleta solid button (custom colors)', () => {
        expect(
          composableActiveButtonStateStyles({
            theme: mockTheme(Brands.Athleta),
            variant: Variant.solid,
            color: Color.custom,
            customColors: { foregroundColor: 'red', backgroundColor: 'yellow' },
          })
        ).toEqual({
          color: 'yellow',
          borderColor: 'red',
          backgroundColor: 'red',
        });
      });

      it('should get active styles for Old Navy solid button (custom colors)', () => {
        expect(
          composableActiveButtonStateStyles({
            theme: mockTheme(Brands.OldNavy),
            variant: Variant.solid,
            color: Color.custom,
            customColors: { foregroundColor: 'red', backgroundColor: 'yellow' },
          })
        ).toEqual({
          color: 'yellow',
          borderColor: 'red',
          backgroundColor: 'red',
        });
      });

      it('should get active styles for flat button (custom colors)', () => {
        expect(
          composableActiveButtonStateStyles({
            theme: mockTheme(Brands.Gap),
            variant: Variant.flat,
            color: Color.custom,
            customColors: { foregroundColor: 'red', backgroundColor: 'yellow' },
          })
        ).toEqual({
          textDecoration: 'underline',
        });
      });
    });
  });
});
