// @ts-nocheck
import { Brands, Theme } from '@ecom-next/core/react-stitch';
import { getComposableButtonStylesPerBrand } from '..';
import { Variant } from '../../types';
import { gapComposableButtonStyles } from '../test-fixtures/variation-styles';

const mockTheme = (brand: Brands = Brands.Gap) =>
  ({
    brand,
    color: {
      b1: '#2B2B2B',
      wh: '#FFFFFF',
      bk: '#000000',
      alpha00: 'transparent',
    },
  }) as Theme;

let theme: Theme = mockTheme();
describe('composable-button styles', () => {
  beforeEach(() => {
    theme = mockTheme();
  });

  describe('getComposableButtonStylesPerBrand', () => {
    const variants = [Variant.solid, Variant.outline, Variant.border, Variant.flat, Variant.chevron, Variant.underline];

    it('should get the default styles for Gap, which are the solid variant', () => {
      expect(
        getComposableButtonStylesPerBrand({
          theme,
        })
      ).toEqual(gapComposableButtonStyles(Variant.solid));
    });

    variants.forEach(variant => {
      it(`should get the ${variant} styles for Gap`, () => {
        expect(
          getComposableButtonStylesPerBrand({
            theme,
            variant,
          })
        ).toEqual(gapComposableButtonStyles(variant));
      });
    });
  });
});
