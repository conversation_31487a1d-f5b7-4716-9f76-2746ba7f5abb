'use client';
// @ts-ignore
import { Brands, CSSObject, Theme, forBrands } from '@ecom-next/core/react-stitch';
// @ts-ignore
import { EnabledFeaturesType } from '@ecom-next/core/legacy/feature-flags';
import { Color, ComposableButtonStyleFn, Size, Variant } from '../types';
import { AT_FEATURE_FLAG_REDESIGN } from '../constants';

//
// === CONSTANTS ===
//

export const defaultStyle = (theme: Theme, crossBrand?: boolean) => ({
  horizontalPadding: '0.8em',
  verticalPadding: '0.5em',
  borderWidth: forBrands(
    theme,
    {
      crossBrand: 2,
      gap: 1,
      gapfs: 1,
      at: 1.5,
      default: 2, // on, br, brfs
    },
    crossBrand
  ) as number,
});

export enum GlobalFontSizes {
  large = '1.2rem',
  medium = '1rem',
  small = '0.8rem',
  xl = '1.5rem',
  xs = '0.625rem',
}

function forSize<T = string>(
  opts: Partial<Record<Size, T>> & {
    default: T;
  },
  size?: Size
): T {
  return opts[size || 'default'] || opts.default;
}

//
// === Helpers ===

/**
 * Internal helper to get the brand's "dark color" used for button background
 */
const getDarkColor = (theme: Theme, crossBrand: boolean | undefined, enabledFeatures: EnabledFeaturesType | undefined): string =>
  forBrands(
    theme,
    {
      crossBrand: theme.color.bk,
      at: enabledFeatures?.[AT_FEATURE_FLAG_REDESIGN] ? '#111' : theme.color.bk,
      br: theme.color.g1,
      brfs: theme.color.g1,
      gap: theme.color.bk,
      gapfs: theme.color.bk,
      default: theme.color.b1,
    },
    crossBrand
  ) as string;

/**
 * Get the underline CSS styling
 */
export const getUnderlineStyling: ComposableButtonStyleFn = ({ theme, enabledFeatures }) => ({
  textDecoration: 'underline',
  textUnderlineOffset: forBrands(theme, {
    at: enabledFeatures?.[AT_FEATURE_FLAG_REDESIGN] ? '4px' : '5px',
    on: '3.5px',
    default: '0px',
  }),
  textDecorationThickness: forBrands(theme, {
    on: '3px',
    default: '1px',
  }),
});

type ActiveButtonColors = {
  primary: string;
  secondary: string;
};
export const getActiveButtonColor: ComposableButtonStyleFn = ({ enabledFeatures, theme, crossBrand, customColors, color }): ActiveButtonColors => {
  if (color === Color.custom && customColors) {
    const { foregroundColor, backgroundColor } = customColors;
    return {
      primary: foregroundColor,
      secondary: backgroundColor,
    };
  }

  if ([Brands.Gap, Brands.GapFactoryStore].includes(theme.brand)) {
    if (color && [Color.white, Color.light].includes(color)) {
      return {
        primary: theme.color.bk,
        secondary: theme.color.wh,
      };
    }

    return {
      primary: theme.color.wh,
      secondary: theme.color.bk,
    };
  }

  if (theme.brand === Brands.Athleta) {
    if (color && [Color.white, Color.light].includes(color)) {
      return {
        primary: theme.color.wh,
        secondary: getDarkColor(theme, crossBrand, enabledFeatures),
      };
    }

    return {
      primary: getDarkColor(theme, crossBrand, enabledFeatures),
      secondary: theme.color.wh,
    };
  }

  if (color && [Color.white, Color.light].includes(color)) {
    return {
      primary: theme.color.wh,
      secondary: getDarkColor(theme, crossBrand, enabledFeatures),
    };
  }

  // Dark is default
  return {
    primary: getDarkColor(theme, crossBrand, enabledFeatures),
    secondary: theme.color.wh,
  };
};

export const composableActiveButtonStateStyles: ComposableButtonStyleFn = ({ theme, variant, color, customColors, crossBrand, enabledFeatures }) => {
  if (color === Color.custom) {
    switch (variant) {
      case Variant.outline:
      case Variant.border:
        return {
          color: customColors?.backgroundColor,
          borderColor: customColors?.backgroundColor,
          backgroundColor: customColors?.foregroundColor,
        };
      case Variant.flat:
        return {
          textDecoration: 'underline',
        };
      case Variant.chevron:
        return forBrands(theme, {
          default: {
            'span svg': {
              fill: customColors?.backgroundColor,
            },
            color: customColors?.backgroundColor,
            backgroundColor: customColors?.foregroundColor,
          },
        });
      default:
        return forBrands(
          theme,
          {
            at: {
              color: customColors?.backgroundColor,
              borderColor: customColors?.foregroundColor,
              backgroundColor: customColors?.foregroundColor,
            },
            on: {
              color: customColors?.backgroundColor,
              borderColor: customColors?.foregroundColor,
              backgroundColor: customColors?.foregroundColor,
            },
            crossBrand: {
              color: customColors?.backgroundColor,
              borderColor: customColors?.backgroundColor,
              backgroundColor: customColors?.foregroundColor,
            },
            default: {
              color: customColors?.backgroundColor,
              borderColor: customColors?.backgroundColor,
              backgroundColor: customColors?.foregroundColor,
            },
          },
          crossBrand
        );
    }
  }
  if (color === Color.light || color === Color.white) {
    switch (variant) {
      case Variant.border:
      case Variant.outline:
        return {
          color: getDarkColor(theme, crossBrand, enabledFeatures),
          borderColor: theme.color.wh,
          backgroundColor: theme.color.wh,
        };
      case Variant.underline:
        return {
          color: getDarkColor(theme, crossBrand, enabledFeatures),
          borderBottomColor: theme.color.wh,
          backgroundColor: theme.color.wh,
        };
      case Variant.flat:
        return {
          textDecoration: 'underline',
        };
      case Variant.chevron:
        return {
          color: getDarkColor(theme, crossBrand, enabledFeatures),
          backgroundColor: theme.color.wh,
          'span svg': {
            fill: getDarkColor(theme, crossBrand, enabledFeatures),
          },
        };
      default:
        return forBrands(
          theme,
          {
            at: {
              color: theme.color.wh,
              borderColor: getDarkColor(theme, crossBrand, enabledFeatures),
              backgroundColor: getDarkColor(theme, crossBrand, enabledFeatures),
            },
            crossBrand: {
              color: theme.color.wh,
              borderColor: theme.color.wh,
              backgroundColor: getDarkColor(theme, crossBrand, enabledFeatures),
            },
            default: {
              color: theme.color.wh,
              borderColor: theme.color.wh,
              backgroundColor: getDarkColor(theme, crossBrand, enabledFeatures),
            },
          },
          crossBrand
        );
    }
  }
  // Default (dark)
  switch (variant) {
    case Variant.border:
    case Variant.outline:
      return {
        color: theme.color.wh,
        borderColor: getDarkColor(theme, crossBrand, enabledFeatures),
        backgroundColor: getDarkColor(theme, crossBrand, enabledFeatures),
      };
    case Variant.underline:
      return {
        color: theme.color.wh,
        borderBottomColor: getDarkColor(theme, crossBrand, enabledFeatures),
        backgroundColor: getDarkColor(theme, crossBrand, enabledFeatures),
      };
    case Variant.flat:
      return {
        textDecoration: 'underline',
      };
    case Variant.chevron:
      return {
        color: theme.color.wh,
        backgroundColor: getDarkColor(theme, crossBrand, enabledFeatures),
        'span svg': {
          fill: theme.color.wh,
        },
      };
    default:
      return forBrands(
        theme,
        {
          at: {
            color: getDarkColor(theme, crossBrand, enabledFeatures),
            borderColor: theme.color.wh,
            backgroundColor: theme.color.wh,
          },
          crossBrand: {
            color: getDarkColor(theme, crossBrand, enabledFeatures),
            borderColor: getDarkColor(theme, crossBrand, enabledFeatures),
            backgroundColor: theme.color.wh,
          },
          default: {
            color: getDarkColor(theme, crossBrand, enabledFeatures),
            borderColor: getDarkColor(theme, crossBrand, enabledFeatures),
            backgroundColor: theme.color.wh,
          },
        },
        crossBrand
      );
  }
};

export const setBorderButtonStyles: ComposableButtonStyleFn = ({ theme, crossBrand, color, customColors, enabledFeatures }) => {
  switch (color) {
    case Color.custom:
      return {
        backgroundColor: customColors?.backgroundColor,
        color: customColors?.foregroundColor,
        borderColor: customColors?.foregroundColor,
      };
    case Color.light:
    case Color.white:
      return {
        backgroundColor: getDarkColor(theme, crossBrand, enabledFeatures),
        color: theme.color.wh,
        borderColor: theme.color.wh,
      };
    default:
      return {
        backgroundColor: theme.color.wh,
        color: getDarkColor(theme, crossBrand, enabledFeatures),
        borderColor: getDarkColor(theme, crossBrand, enabledFeatures),
      };
  }
};

export const setFlatButtonStyles: ComposableButtonStyleFn = ({ theme, crossBrand, color: colorProp, customColors, enabledFeatures }) => {
  let color;
  switch (colorProp) {
    case Color.custom:
      color = customColors?.foregroundColor;
      break;
    case Color.light:
    case Color.white:
      color = theme.color.wh;
      break;
    default:
      color = getDarkColor(theme, crossBrand, enabledFeatures);
  }

  return {
    position: 'relative',
    border: 'none',
    backgroundColor: theme.color.alpha00,
    color,
    '&::after': {
      content: "''",
      position: 'absolute',
      bottom: `-${defaultStyle(theme, crossBrand).borderWidth}px`,
      left: `${defaultStyle(theme, crossBrand).horizontalPadding}`,
      right: `${defaultStyle(theme, crossBrand).horizontalPadding}`,
      backgroundColor: `${theme.color.alpha00}`,
      display: 'block',
      height: `${defaultStyle(theme, crossBrand).borderWidth}px`,
    } as CSSObject,
  };
};

export const setSolidButtonStyles: ComposableButtonStyleFn = props => {
  const { primary, secondary } = getActiveButtonColor(props);
  return {
    color: secondary,
    backgroundColor: primary,
    borderColor: primary,
  };
};

export const setOutlineButtonStyles: ComposableButtonStyleFn = props => {
  const { theme } = props;
  const { primary, secondary } = getActiveButtonColor(props);

  if ([Brands.Gap, Brands.GapFactoryStore].includes(theme.brand) && props.color !== Color.custom) {
    return {
      color: secondary,
      background: theme.color.alpha00,
      borderColor: secondary,
    };
  }

  return {
    color: primary,
    background: theme.color.alpha00,
    borderColor: primary,
  };
};

const setColor = (theme: Theme, color?: Color, primary?: Color) => {
  let colorForGap: string | undefined;
  if ([Brands.Gap, Brands.GapFactoryStore].includes(theme.brand)) {
    colorForGap = color === Color.light || color === Color.white ? theme.color.wh : theme.color.bk;
  }
  return colorForGap ?? primary;
};

export const setUnderlineButtonStyles: ComposableButtonStyleFn = props => {
  const { theme, color, crossBrand } = props;
  const { primary } = getActiveButtonColor(props);

  return {
    borderLeft: 'none',
    borderRight: 'none',
    borderRadius: '0',
    backgroundColor: theme.color.alpha00,
    borderBottom: `${defaultStyle(theme, crossBrand).borderWidth}px solid currentColor`,
    justifyContent: 'left',
    color: setColor(theme, color, primary),
    height: 'auto',
    padding: 0,
    textAlign: 'left',
  };
};

export const setChevronButtonStyles: ComposableButtonStyleFn = props => {
  const { theme, color, size, crossBrand, variant } = props;
  const { primary } = getActiveButtonColor(props);

  enum BrandFontScaleFactor {
    // Athleta glyphs are ~28% smaller than the font's "height"
    at = 0.72,
    on = 0.7,
    cb = 0.65,
  }

  const buttonOverrideStyles = {
    textAlign: 'left',
    borderRadius: 0,
    border: 'none',
    backgroundColor: 'transparent',
    color: setColor(theme, color, primary),
    height: 'auto',
    justifyContent: 'left',
    // have to undo padding to target the inner span
    padding: 0,
    ...((variant === Variant.flat && props.enabledFeatures?.[AT_FEATURE_FLAG_REDESIGN]
      ? forBrands(theme, {
          at: { textUnderlineOffset: '4px' },
          default: {},
        })
      : {}) as CSSObject),
  };

  const chevronBaseStyles: (relativeSize: string) => Record<string, string | Record<string, string>> = relativeSize => ({
    minWidth: 'auto',
    minHeight: 'auto',
    position: 'relative',
    alignItems: 'center',
    transition: '250ms ease-in-out',
    transitionProperty: 'height, width, margin',
    top: forSize(
      {
        default: '0',
      },
      size
    ),
    svg: {
      transition: 'fill 250ms ease-in-out',
      fill: primary,
    },
    width: relativeSize,
  });

  const globalSize = GlobalFontSizes[size as keyof typeof GlobalFontSizes];
  const getScaledChevronSizeOn = `calc(${globalSize} * ${BrandFontScaleFactor.on})`;
  const getScaledChevronSizeAt = `calc(${globalSize} * ${BrandFontScaleFactor.at})`;
  return {
    ...buttonOverrideStyles,
    '& span': {
      ...(forBrands(
        theme,
        {
          gap: {
            'svg path': {
              transition: 'fill 250ms ease-in-out',
              fill: setColor(theme, color, primary),
            },
          },
          gapfs: {
            'svg path': {
              transition: 'fill 250ms ease-in-out',
              fill: setColor(theme, color, primary),
            },
          },
          at: {
            span: {
              paddingLeft: '3px',
              ...chevronBaseStyles(getScaledChevronSizeAt),
            },
          },
          on: {
            span: {
              paddingLeft: '1px',
              ...chevronBaseStyles(getScaledChevronSizeOn),
            },
          },
          crossBrand: {
            paddingRight: '.4em',
            span: {
              ...chevronBaseStyles(getScaledChevronSizeOn),
              margin: '.06em 0 0 .15em',
              width: getScaledChevronSizeOn,
              minWidth: getScaledChevronSizeOn,
              height: getScaledChevronSizeOn,
              minHeight: getScaledChevronSizeOn,
            },
          },
        },
        crossBrand
      ) as CSSObject),
    },
  };
};

export const setTextAlign: ComposableButtonStyleFn = ({ variant }) => {
  const isChevronOrUnderline = variant && [Variant.chevron, Variant.underline].includes(variant);

  return {
    textAlign: isChevronOrUnderline ? 'left' : 'center',
  };
};

export const setFullWidth: ComposableButtonStyleFn = ({ fullWidth }) =>
  fullWidth
    ? {
        width: '100%',
      }
    : {};

export const textUnderlineOffsetMap = (size: Size | undefined) => {
  if (size) {
    return {
      [Size.xl]: '5px',
      [Size.large]: '5px',
      [Size.medium]: '3.5px',
      [Size.small]: '3.3px',
      [Size.xs]: '2.5px',
    }[size];
  }

  return '0px';
};

export const textDecorationThicknessMap = (size: Size | undefined) => {
  if (size) {
    return {
      [Size.xl]: '2.3px',
      [Size.large]: '2.3px',
      [Size.medium]: '2px',
      [Size.small]: '1.6px',
      [Size.xs]: '1.4px',
    }[size];
  }

  return '0px';
};

export const setUnderlineStyling: ComposableButtonStyleFn = ({ enabledFeatures, theme, size }) => ({
  textDecoration: 'underline',

  textUnderlineOffset: forBrands(theme, {
    at: enabledFeatures?.[AT_FEATURE_FLAG_REDESIGN] ? '4px' : '5px',
    gap: textUnderlineOffsetMap(size),
    gapfs: textUnderlineOffsetMap(size),
    on: '3.5px',
    default: '0px',
  }),
  textDecorationThickness: forBrands(theme, {
    gap: textDecorationThicknessMap(size),
    gapfs: textDecorationThicknessMap(size),
    on: '3px',
    default: '1px',
  }),
});
