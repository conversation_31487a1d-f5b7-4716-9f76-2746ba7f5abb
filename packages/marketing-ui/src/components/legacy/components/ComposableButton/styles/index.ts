// @ts-nocheck
'use client';
import { CSSObject, getFontWeight } from '@ecom-next/core/react-stitch';
import getComposableButtonConfig, { getValue } from '../config';
import { ComposableButtonStyleFn, Variant, Font, VariantSizeConfig, Size } from '../types';
import {
  setBorderButtonStyles,
  setChevronButtonStyles,
  setOutlineButtonStyles,
  setUnderlineButtonStyles,
  setSolidButtonStyles,
  setTextAlign,
  setFullWidth,
  setUnderlineStyling,
} from './helpers';

export { setTextAlign };

function getStylesFromComposableButtonConfig(config: VariantSizeConfig): CSSObject {
  const { fontSize, fontWeight, letterSpacing, lineHeight, padding, minHeight, maxHeight, ...restConfig } = config;

  return {
    ...restConfig,
    fontSize,
    ...getFontWeight(fontWeight || 'regular'),
    letterSpacing: letterSpacing || 'normal',
    minHeight: minHeight || 'auto',
    maxHeight: maxHeight || 'auto',
    lineHeight: lineHeight !== undefined ? lineHeight : 'normal',
    padding: padding ? padding.join(' ') : 0,
    width: 'auto',
    // will add padding to the inner span for chevron and underline
    // so that the :hover state will highlight over the underline
    '& > span': {
      padding: '1px 0',
    },
  };
}

enum BrandFontScaleFactor {
  // actual px character height / rendered font-size height
  at = 107 / 150,
  on = 0.7,

  // Not defined, using default
  gap = 0.65,
  gapfs = 0.65,
  br = 0.65,
  brfs = 0.65,
}

/**
 * Root style function (not hook) that will compute the CSSObject of the needed styles for a button
 * based on variant, size, and brand
 */
export const getComposableButtonStylesPerBrand: ComposableButtonStyleFn = props => {
  const { theme, font, crossBrand, viewport } = props;
  const { brand } = theme;
  const [config, defaults] = getComposableButtonConfig(brand, viewport, props.enabledFeatures);

  // have to separately expose variant and size AFTER we get the config
  const { variant = defaults.variant || Variant.solid, size = defaults.size || Size.medium } = props;

  // shared CSS properties
  const variantConfig = getValue(config, variant, size);
  const baseStyles: CSSObject = getStylesFromComposableButtonConfig(variantConfig);

  const textAlign: CSSObject = setTextAlign(props);
  const fullWidth: CSSObject = setFullWidth(props);

  let fontCSS: CSSObject = crossBrand ? { ...theme.crossBrand.brandFont, fontWeight: 700 } : { ...theme.brandFont };

  if (font === Font.brandFontAlt) {
    fontCSS = crossBrand ? { ...theme.crossBrand.brandFont, fontWeight: 700 } : { ...theme.brandFontAlt };
  }

  // "update" props with default size and variant from config
  const newProps = {
    ...props,
    variant,
    size,
  };

  let variantStyles: CSSObject;
  switch (variant) {
    case Variant.border:
      variantStyles = setBorderButtonStyles(newProps);
      break;
    case Variant.outline:
      variantStyles = setOutlineButtonStyles(newProps);
      break;
    case Variant.chevron:
      variantStyles = {
        ...setChevronButtonStyles(newProps),
        '& > span': {
          '& span': {
            height: `calc(${baseStyles.fontSize} * ${BrandFontScaleFactor[brand]})`,
          },
        },
      };
      break;
    case Variant.flat:
      variantStyles = {
        // flat shared chevron styles
        ...setChevronButtonStyles(newProps),
        padding: 0,
      };
      break;
    case Variant.underline:
      variantStyles = {
        ...setUnderlineButtonStyles(newProps),
        ...setUnderlineStyling(newProps),
        borderBottom: 0,
      };
      break;
    case Variant.solid:
    default:
      variantStyles = setSolidButtonStyles(newProps);
  }

  const styles: CSSObject = {
    ...baseStyles,
    ...fontCSS,
    ...textAlign,
    ...fullWidth,
    ...variantStyles,
  };

  return styles;
};
