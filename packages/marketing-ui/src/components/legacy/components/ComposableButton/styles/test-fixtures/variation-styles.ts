'use client';
import { Variant } from '../../types';

export const gapComposableButtonStyles = (variant: Variant) => {
  let styles;
  switch (variant) {
    case Variant.border:
      styles = {
        '& > span': {
          padding: '1px 0',
        },
        backgroundColor: '#FFFFFF',
        borderColor: '#000000',
        color: '#000000',
        fontSize: '12px',
        fontWeight: 500,
        letterSpacing: '2%',
        lineHeight: 1.125,
        minHeight: 'unset',
        maxHeight: 'auto',
        padding: '12px',
        textAlign: 'center',
        width: 'auto',
      };
      break;
    case Variant.flat:
      styles = {
        '& > span': {
          padding: '1px 0',
        },
        '& span': {
          'svg path': {
            fill: '#000000',
            transition: 'fill 250ms ease-in-out',
          },
        },
        backgroundColor: 'transparent',
        border: 'none',
        borderRadius: 0,
        color: '#000000',
        fontSize: '12px',
        fontWeight: 500,
        height: 'auto',
        justifyContent: 'left',
        letterSpacing: '2%',
        lineHeight: 1.125,
        minHeight: 'auto',
        maxHeight: 'auto',
        padding: 0,
        textAlign: 'left',
        width: 'auto',
      };
      break;
    case Variant.outline:
      styles = {
        '& > span': {
          padding: '1px 0',
        },
        background: 'transparent',
        borderColor: '#000000',
        color: '#000000',
        fontSize: '12px',
        fontWeight: 500,
        letterSpacing: '2%',
        lineHeight: 1.125,
        minHeight: 'unset',
        maxHeight: 'auto',
        padding: '12px',
        textAlign: 'center',
        width: 'auto',
      };
      break;
    case Variant.underline:
      styles = {
        '& > span': {
          padding: '1px 0',
        },
        backgroundColor: 'transparent',
        borderBottom: 0,
        borderLeft: 'none',
        borderRadius: '0',
        borderRight: 'none',
        color: '#000000',
        fontSize: '12px',
        fontWeight: 500,
        height: 'auto',
        justifyContent: 'left',
        letterSpacing: '2%',
        lineHeight: 1.125,
        minHeight: 'auto',
        maxHeight: 'auto',
        padding: 0,
        textAlign: 'left',
        textDecoration: 'underline',
        textDecorationThickness: '1.6px',
        textUnderlineOffset: '3.3px',
        width: 'auto',
      };
      break;
    case Variant.chevron:
      styles = {
        '& > span': {
          '& span': {
            height: 'calc(12px * 0.65)',
          },
        },
        '& span': {
          'svg path': {
            fill: '#000000',
            transition: 'fill 250ms ease-in-out',
          },
        },
        backgroundColor: 'transparent',
        border: 'none',
        borderRadius: 0,
        color: '#000000',
        fontSize: '12px',
        fontWeight: 500,
        height: 'auto',
        justifyContent: 'left',
        letterSpacing: '2%',
        lineHeight: 1.125,
        minHeight: 'auto',
        maxHeight: 'auto',
        padding: 0,
        textAlign: 'left',
        width: 'auto',
      };
      break;
    case Variant.solid:
    default:
      styles = {
        '& > span': {
          padding: '1px 0',
        },
        backgroundColor: '#FFFFFF',
        borderColor: '#FFFFFF',
        color: '#000000',
        fontSize: '12px',
        fontWeight: 500,
        letterSpacing: '2%',
        lineHeight: 1.125,
        minHeight: 'unset',
        maxHeight: 'auto',
        padding: '12px',
        textAlign: 'center',
        width: 'auto',
      };
  }
  return styles;
};
