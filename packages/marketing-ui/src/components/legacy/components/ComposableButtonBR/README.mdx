export const RedAsterisk = () => <span style={{ color: 'red' }}>*</span>;

# ComposableButtonBR

`ComposableButtonBR` is the `ComposableButton` for **Banana Republic** and **Banana Republic Factory Store**.

The original `ComposableButton` renders `ComposableButtonBR` when the `Brand` is `br` or `brfs`.

## Default Behavior

This is the default for `ComposableButtonBR` (**required** props are `label` and `value`):

```tsx
<ComposableButtonBR label='SOLID BUTTON' value='https://bananarepublic.gap.com/' />
```

`ComposableButtonBR` has **4 variants**: `arrow`, `outline`, `solid` and `underline`. The **default** one is `solid`.

These are all the props available for `ComposableButtonBR`:

| Prop                    | Required | Description                                                                                              | Default   | Values                                     |
| ----------------------- | -------- | -------------------------------------------------------------------------------------------------------- | --------- | ------------------------------------------ |
| `color`                 | No       | Control the button color theme, primary or secondary. `enum`                                             | `primary` | `primary` or `secondary`                   |
| `disabled`              | No       | Control if the button is disabled or enabled, if true or present, it's disabled. `boolean`               | `false`   | `false` or `true`                          |
| `label` <RedAsterisk /> | Yes      | The label text string to be displayed in the button. `string`                                            | -         | -                                          |
| `size`                  | No       | Control the button size, large or small. Only solid and outline variants have sizes. `enum`              | `large`   | `large` or `small`                         |
| `value` <RedAsterisk /> | Yes      | The URL to navigate when the button is clicked. If present, will be rendered as an anchor link. `string` | -         | -                                          |
| `variant`               | No       | The variant / design for the button. Styles and interactions might differ from one to another. `enum`    | `solid`   | `arrow`, `outline`, `solid` or `underline` |

## Limitations

- Since this is totally new and separate component from `ComposableButton`, not all functionality from `ComposableButton` is supported by `ComposableButtonBR`.
- `size: small` is only supported by the variants `outline` and `solid`.
- `background-color: theme.color.b2` used by the variants `outline` and `solid` on its `disabled` state is dependent on the feature flags `br-colors-2023: true` and `br-redesign-2023: false`. Button colors are dependent on the `br-colors-2023: true`, be aware of it when dealing with the testing, screenshots and snapshots.

## Technical Notes

We use [React-Stitch](https://core-ui-main.apps.cfplatform.dev.azeus.gaptech.com/?path=%2Fstory%2Fcommon-reactstitch--default&brand=br) for styling, same as we have been doing for other components here in MUI and [core-ui](https://github.gapinc.com/ecomfrontend/core-ui). React-Stitch is built with [emotion](https://emotion.sh/).

_Please **DON'T** mix up with [react-stitches](https://stitches.dev/), that's a <u>different library</u>._

## UX Guidelines

All UX guidelines are in our Figma files, please check with your brand or tech lead if needed.

## Testing the Component in Storybook

Please check all the stories for this component, we tried to provide you all the expected functionalities on those stories.

## Breaking Changes Information

To view information regarding **BREAKING CHANGES**, please view the [Marketing UI MIGRATION.md file](/src/MIGRATION.md).
