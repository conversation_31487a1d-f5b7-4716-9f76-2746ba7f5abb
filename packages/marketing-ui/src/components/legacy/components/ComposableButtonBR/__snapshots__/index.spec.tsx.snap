// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`ComposableButtonBR Component Test for each variant Test for each size should shown size large correctly for variant arrow 1`] = `
.emotion-0 {
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  background-color: transparent;
  border: 0;
  box-sizing: border-box;
  color: #000000;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  font-size: 12px;
  font-weight: 350;
  gap: 8px;
  height: 16px;
  letter-spacing: 0.5px;
  line-height: 1.33;
  outline: 0;
  padding: 0;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

.emotion-0 svg {
  fill: #000000;
  -webkit-transition: width 400ms;
  transition: width 400ms;
}

.emotion-0 svg .arrow-line {
  -webkit-transition: -webkit-transform 400ms;
  transition: transform 400ms;
}

.emotion-0:hover:not([aria-disabled=true]) svg {
  width: 102px;
}

.emotion-0:hover:not([aria-disabled=true]) svg .arrow-line {
  -webkit-transform: translateX(-23px) scaleX(1.28);
  -moz-transform: translateX(-23px) scaleX(1.28);
  -ms-transform: translateX(-23px) scaleX(1.28);
  transform: translateX(-23px) scaleX(1.28);
}

.emotion-0[aria-disabled=true] {
  color: #999999;
  pointer-events: none;
}

.emotion-0[aria-disabled=true] svg {
  fill: #999999;
}

<div>
  <div
    style="font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;"
  >
    <a
      class="emotion-0"
      data-testid="composable-btn-br"
      href="https://bananarepublic.gap.com/"
      role="button"
    >
      ARROW LARGE
      <svg
        height="6"
        preserveAspectRatio="xMaxYMin"
        viewBox="0 0 79 6"
        width="79"
        xmlns="http://www.w3.org/2000/svg"
      >
        <g>
          <path
            class="arrow-head"
            d="M73.5 0L78.5 3L73.5 6L75.6429 3L73.5 0Z"
          />
          <rect
            class="arrow-line"
            height="1"
            width="77"
            y="2.5"
          />
        </g>
      </svg>
    </a>
  </div>
</div>
`;

exports[`ComposableButtonBR Component Test for each variant Test for each size should shown size large correctly for variant outline 1`] = `
.emotion-0 {
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  border: 1px solid #000000;
  box-sizing: border-box;
  color: #000000;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  font-size: 12px;
  font-weight: 600;
  height: 56px;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  letter-spacing: 1px;
  line-height: 1.33;
  outline: 0;
  overflow: hidden;
  padding: 0;
  -webkit-transition: border-color 200ms;
  transition: border-color 200ms;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  white-space: nowrap;
  width: 280px;
}

.emotion-0[aria-disabled=true] {
  color: rgba(0, 0, 0, 0.25);
  border-color: rgba(0, 0, 0, 0.25);
  pointer-events: none;
}

<div>
  <div
    style="font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;"
  >
    <a
      class="emotion-0"
      data-testid="composable-btn-br"
      href="https://bananarepublic.gap.com/"
      role="button"
    >
      OUTLINE LARGE
    </a>
  </div>
</div>
`;

exports[`ComposableButtonBR Component Test for each variant Test for each size should shown size large correctly for variant solid 1`] = `
.emotion-0 {
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  background-color: #FFFFFF;
  border: 0;
  box-sizing: border-box;
  color: #000000;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  font-size: 12px;
  font-weight: 600;
  height: 56px;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  letter-spacing: 1px;
  line-height: 1.33;
  outline: 0;
  overflow: hidden;
  padding: 0;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  white-space: nowrap;
  width: 280px;
}

.emotion-0[aria-disabled=true] {
  color: rgba(0, 0, 0, 0.25);
  pointer-events: none;
}

<div>
  <div
    style="font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;"
  >
    <a
      class="emotion-0"
      data-testid="composable-btn-br"
      href="https://bananarepublic.gap.com/"
      role="button"
    >
      SOLID LARGE
    </a>
  </div>
</div>
`;

exports[`ComposableButtonBR Component Test for each variant Test for each size should shown size large correctly for variant underline 1`] = `
.emotion-0 {
  background-color: transparent;
  border: 0;
  box-sizing: border-box;
  color: #000000;
  display: inline-block;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  font-size: 12px;
  font-weight: 400;
  height: 16px;
  letter-spacing: 0.5px;
  line-height: 1.33;
  outline: 0;
  padding: 0;
  text-align: center;
  -webkit-text-decoration: underline;
  text-decoration: underline;
  text-decoration-color: #000000;
  text-underline-offset: 5px;
  -webkit-transition: text-decoration-color 200ms;
  transition: text-decoration-color 200ms;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

.emotion-0[aria-disabled=true] {
  color: #999999;
  pointer-events: none;
  text-decoration-color: #CCCCCC;
}

.emotion-0:hover:not([aria-disabled=true]) {
  text-decoration-color: #000000;
}

<div>
  <div
    style="font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;"
  >
    <a
      class="emotion-0"
      data-testid="composable-btn-br"
      href="https://bananarepublic.gap.com/"
      role="button"
    >
      UNDERLINE LARGE
    </a>
  </div>
</div>
`;

exports[`ComposableButtonBR Component Test for each variant Test for each size should shown size small correctly for variant arrow 1`] = `
.emotion-0 {
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  background-color: transparent;
  border: 0;
  box-sizing: border-box;
  color: #000000;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  font-size: 12px;
  font-weight: 350;
  gap: 8px;
  height: 16px;
  letter-spacing: 0.5px;
  line-height: 1.33;
  outline: 0;
  padding: 0;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

.emotion-0 svg {
  fill: #000000;
  -webkit-transition: width 400ms;
  transition: width 400ms;
}

.emotion-0 svg .arrow-line {
  -webkit-transition: -webkit-transform 400ms;
  transition: transform 400ms;
}

.emotion-0:hover:not([aria-disabled=true]) svg {
  width: 102px;
}

.emotion-0:hover:not([aria-disabled=true]) svg .arrow-line {
  -webkit-transform: translateX(-23px) scaleX(1.28);
  -moz-transform: translateX(-23px) scaleX(1.28);
  -ms-transform: translateX(-23px) scaleX(1.28);
  transform: translateX(-23px) scaleX(1.28);
}

.emotion-0[aria-disabled=true] {
  color: #999999;
  pointer-events: none;
}

.emotion-0[aria-disabled=true] svg {
  fill: #999999;
}

<div>
  <div
    style="font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;"
  >
    <a
      class="emotion-0"
      data-testid="composable-btn-br"
      href="https://bananarepublic.gap.com/"
      role="button"
    >
      ARROW SMALL
      <svg
        height="6"
        preserveAspectRatio="xMaxYMin"
        viewBox="0 0 79 6"
        width="79"
        xmlns="http://www.w3.org/2000/svg"
      >
        <g>
          <path
            class="arrow-head"
            d="M73.5 0L78.5 3L73.5 6L75.6429 3L73.5 0Z"
          />
          <rect
            class="arrow-line"
            height="1"
            width="77"
            y="2.5"
          />
        </g>
      </svg>
    </a>
  </div>
</div>
`;

exports[`ComposableButtonBR Component Test for each variant Test for each size should shown size small correctly for variant outline 1`] = `
.emotion-0 {
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  border: 1px solid #000000;
  box-sizing: border-box;
  color: #000000;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  font-size: 9px;
  font-weight: 600;
  height: 32px;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  letter-spacing: 1px;
  line-height: 1.33;
  outline: 0;
  overflow: hidden;
  padding: 0;
  -webkit-transition: border-color 200ms;
  transition: border-color 200ms;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  white-space: nowrap;
  width: 244px;
}

.emotion-0[aria-disabled=true] {
  color: rgba(0, 0, 0, 0.25);
  border-color: rgba(0, 0, 0, 0.25);
  pointer-events: none;
}

<div>
  <div
    style="font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;"
  >
    <a
      class="emotion-0"
      data-testid="composable-btn-br"
      href="https://bananarepublic.gap.com/"
      role="button"
    >
      OUTLINE SMALL
    </a>
  </div>
</div>
`;

exports[`ComposableButtonBR Component Test for each variant Test for each size should shown size small correctly for variant solid 1`] = `
.emotion-0 {
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  background-color: #FFFFFF;
  border: 0;
  box-sizing: border-box;
  color: #000000;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  font-size: 9px;
  font-weight: 600;
  height: 32px;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  letter-spacing: 1px;
  line-height: 1.33;
  outline: 0;
  overflow: hidden;
  padding: 0;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  white-space: nowrap;
  width: 244px;
}

.emotion-0[aria-disabled=true] {
  color: rgba(0, 0, 0, 0.25);
  pointer-events: none;
}

<div>
  <div
    style="font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;"
  >
    <a
      class="emotion-0"
      data-testid="composable-btn-br"
      href="https://bananarepublic.gap.com/"
      role="button"
    >
      SOLID SMALL
    </a>
  </div>
</div>
`;

exports[`ComposableButtonBR Component Test for each variant Test for each size should shown size small correctly for variant underline 1`] = `
.emotion-0 {
  background-color: transparent;
  border: 0;
  box-sizing: border-box;
  color: #000000;
  display: inline-block;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  font-size: 12px;
  font-weight: 400;
  height: 16px;
  letter-spacing: 0.5px;
  line-height: 1.33;
  outline: 0;
  padding: 0;
  text-align: center;
  -webkit-text-decoration: underline;
  text-decoration: underline;
  text-decoration-color: #000000;
  text-underline-offset: 5px;
  -webkit-transition: text-decoration-color 200ms;
  transition: text-decoration-color 200ms;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

.emotion-0[aria-disabled=true] {
  color: #999999;
  pointer-events: none;
  text-decoration-color: #CCCCCC;
}

.emotion-0:hover:not([aria-disabled=true]) {
  text-decoration-color: #000000;
}

<div>
  <div
    style="font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;"
  >
    <a
      class="emotion-0"
      data-testid="composable-btn-br"
      href="https://bananarepublic.gap.com/"
      role="button"
    >
      UNDERLINE SMALL
    </a>
  </div>
</div>
`;

exports[`ComposableButtonBR Component Test for each variant should render disabled state properly for variant arrow 1`] = `
.emotion-0 {
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  background-color: transparent;
  border: 0;
  box-sizing: border-box;
  color: #000000;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  font-size: 12px;
  font-weight: 350;
  gap: 8px;
  height: 16px;
  letter-spacing: 0.5px;
  line-height: 1.33;
  outline: 0;
  padding: 0;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

.emotion-0 svg {
  fill: #000000;
  -webkit-transition: width 400ms;
  transition: width 400ms;
}

.emotion-0 svg .arrow-line {
  -webkit-transition: -webkit-transform 400ms;
  transition: transform 400ms;
}

.emotion-0:hover:not([aria-disabled=true]) svg {
  width: 102px;
}

.emotion-0:hover:not([aria-disabled=true]) svg .arrow-line {
  -webkit-transform: translateX(-23px) scaleX(1.28);
  -moz-transform: translateX(-23px) scaleX(1.28);
  -ms-transform: translateX(-23px) scaleX(1.28);
  transform: translateX(-23px) scaleX(1.28);
}

.emotion-0[aria-disabled=true] {
  color: #999999;
  pointer-events: none;
}

.emotion-0[aria-disabled=true] svg {
  fill: #999999;
}

<div>
  <div
    style="font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;"
  >
    <a
      aria-disabled="true"
      class="emotion-0"
      data-testid="composable-btn-br"
      role="button"
    >
      VARIANT ARROW
      <svg
        height="6"
        preserveAspectRatio="xMaxYMin"
        viewBox="0 0 79 6"
        width="79"
        xmlns="http://www.w3.org/2000/svg"
      >
        <g>
          <path
            class="arrow-head"
            d="M73.5 0L78.5 3L73.5 6L75.6429 3L73.5 0Z"
          />
          <rect
            class="arrow-line"
            height="1"
            width="77"
            y="2.5"
          />
        </g>
      </svg>
    </a>
  </div>
</div>
`;

exports[`ComposableButtonBR Component Test for each variant should render disabled state properly for variant outline 1`] = `
.emotion-0 {
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  border: 1px solid #000000;
  box-sizing: border-box;
  color: #000000;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  font-size: 12px;
  font-weight: 600;
  height: 56px;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  letter-spacing: 1px;
  line-height: 1.33;
  outline: 0;
  overflow: hidden;
  padding: 0;
  -webkit-transition: border-color 200ms;
  transition: border-color 200ms;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  white-space: nowrap;
  width: 280px;
}

.emotion-0[aria-disabled=true] {
  color: rgba(0, 0, 0, 0.25);
  border-color: rgba(0, 0, 0, 0.25);
  pointer-events: none;
}

<div>
  <div
    style="font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;"
  >
    <a
      aria-disabled="true"
      class="emotion-0"
      data-testid="composable-btn-br"
      role="button"
    >
      VARIANT OUTLINE
    </a>
  </div>
</div>
`;

exports[`ComposableButtonBR Component Test for each variant should render disabled state properly for variant solid 1`] = `
.emotion-0 {
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  background-color: #FFFFFF;
  border: 0;
  box-sizing: border-box;
  color: #000000;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  font-size: 12px;
  font-weight: 600;
  height: 56px;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  letter-spacing: 1px;
  line-height: 1.33;
  outline: 0;
  overflow: hidden;
  padding: 0;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  white-space: nowrap;
  width: 280px;
}

.emotion-0[aria-disabled=true] {
  color: rgba(0, 0, 0, 0.25);
  pointer-events: none;
}

<div>
  <div
    style="font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;"
  >
    <a
      aria-disabled="true"
      class="emotion-0"
      data-testid="composable-btn-br"
      role="button"
    >
      VARIANT SOLID
    </a>
  </div>
</div>
`;

exports[`ComposableButtonBR Component Test for each variant should render disabled state properly for variant underline 1`] = `
.emotion-0 {
  background-color: transparent;
  border: 0;
  box-sizing: border-box;
  color: #000000;
  display: inline-block;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  font-size: 12px;
  font-weight: 400;
  height: 16px;
  letter-spacing: 0.5px;
  line-height: 1.33;
  outline: 0;
  padding: 0;
  text-align: center;
  -webkit-text-decoration: underline;
  text-decoration: underline;
  text-decoration-color: #000000;
  text-underline-offset: 5px;
  -webkit-transition: text-decoration-color 200ms;
  transition: text-decoration-color 200ms;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

.emotion-0[aria-disabled=true] {
  color: #999999;
  pointer-events: none;
  text-decoration-color: #CCCCCC;
}

.emotion-0:hover:not([aria-disabled=true]) {
  text-decoration-color: #000000;
}

<div>
  <div
    style="font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;"
  >
    <a
      aria-disabled="true"
      class="emotion-0"
      data-testid="composable-btn-br"
      role="button"
    >
      VARIANT UNDERLINE
    </a>
  </div>
</div>
`;

exports[`ComposableButtonBR Component Test for each variant should render up state properly for variant arrow 1`] = `
.emotion-0 {
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  background-color: transparent;
  border: 0;
  box-sizing: border-box;
  color: #000000;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  font-size: 12px;
  font-weight: 350;
  gap: 8px;
  height: 16px;
  letter-spacing: 0.5px;
  line-height: 1.33;
  outline: 0;
  padding: 0;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

.emotion-0 svg {
  fill: #000000;
  -webkit-transition: width 400ms;
  transition: width 400ms;
}

.emotion-0 svg .arrow-line {
  -webkit-transition: -webkit-transform 400ms;
  transition: transform 400ms;
}

.emotion-0:hover:not([aria-disabled=true]) svg {
  width: 102px;
}

.emotion-0:hover:not([aria-disabled=true]) svg .arrow-line {
  -webkit-transform: translateX(-23px) scaleX(1.28);
  -moz-transform: translateX(-23px) scaleX(1.28);
  -ms-transform: translateX(-23px) scaleX(1.28);
  transform: translateX(-23px) scaleX(1.28);
}

.emotion-0[aria-disabled=true] {
  color: #999999;
  pointer-events: none;
}

.emotion-0[aria-disabled=true] svg {
  fill: #999999;
}

<div>
  <div
    style="font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;"
  >
    <a
      class="emotion-0"
      data-testid="composable-btn-br"
      href="https://bananarepublic.gap.com/"
      role="button"
    >
      VARIANT ARROW
      <svg
        height="6"
        preserveAspectRatio="xMaxYMin"
        viewBox="0 0 79 6"
        width="79"
        xmlns="http://www.w3.org/2000/svg"
      >
        <g>
          <path
            class="arrow-head"
            d="M73.5 0L78.5 3L73.5 6L75.6429 3L73.5 0Z"
          />
          <rect
            class="arrow-line"
            height="1"
            width="77"
            y="2.5"
          />
        </g>
      </svg>
    </a>
  </div>
</div>
`;

exports[`ComposableButtonBR Component Test for each variant should render up state properly for variant outline 1`] = `
.emotion-0 {
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  border: 1px solid #000000;
  box-sizing: border-box;
  color: #000000;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  font-size: 12px;
  font-weight: 600;
  height: 56px;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  letter-spacing: 1px;
  line-height: 1.33;
  outline: 0;
  overflow: hidden;
  padding: 0;
  -webkit-transition: border-color 200ms;
  transition: border-color 200ms;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  white-space: nowrap;
  width: 280px;
}

.emotion-0[aria-disabled=true] {
  color: rgba(0, 0, 0, 0.25);
  border-color: rgba(0, 0, 0, 0.25);
  pointer-events: none;
}

<div>
  <div
    style="font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;"
  >
    <a
      class="emotion-0"
      data-testid="composable-btn-br"
      href="https://bananarepublic.gap.com/"
      role="button"
    >
      VARIANT OUTLINE
    </a>
  </div>
</div>
`;

exports[`ComposableButtonBR Component Test for each variant should render up state properly for variant solid 1`] = `
.emotion-0 {
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  background-color: #FFFFFF;
  border: 0;
  box-sizing: border-box;
  color: #000000;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  font-size: 12px;
  font-weight: 600;
  height: 56px;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  letter-spacing: 1px;
  line-height: 1.33;
  outline: 0;
  overflow: hidden;
  padding: 0;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  white-space: nowrap;
  width: 280px;
}

.emotion-0[aria-disabled=true] {
  color: rgba(0, 0, 0, 0.25);
  pointer-events: none;
}

<div>
  <div
    style="font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;"
  >
    <a
      class="emotion-0"
      data-testid="composable-btn-br"
      href="https://bananarepublic.gap.com/"
      role="button"
    >
      VARIANT SOLID
    </a>
  </div>
</div>
`;

exports[`ComposableButtonBR Component Test for each variant should render up state properly for variant underline 1`] = `
.emotion-0 {
  background-color: transparent;
  border: 0;
  box-sizing: border-box;
  color: #000000;
  display: inline-block;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  font-size: 12px;
  font-weight: 400;
  height: 16px;
  letter-spacing: 0.5px;
  line-height: 1.33;
  outline: 0;
  padding: 0;
  text-align: center;
  -webkit-text-decoration: underline;
  text-decoration: underline;
  text-decoration-color: #000000;
  text-underline-offset: 5px;
  -webkit-transition: text-decoration-color 200ms;
  transition: text-decoration-color 200ms;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

.emotion-0[aria-disabled=true] {
  color: #999999;
  pointer-events: none;
  text-decoration-color: #CCCCCC;
}

.emotion-0:hover:not([aria-disabled=true]) {
  text-decoration-color: #000000;
}

<div>
  <div
    style="font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;"
  >
    <a
      class="emotion-0"
      data-testid="composable-btn-br"
      href="https://bananarepublic.gap.com/"
      role="button"
    >
      VARIANT UNDERLINE
    </a>
  </div>
</div>
`;
