// @ts-nocheck
import React from 'react';
import { render, screen } from '@testing-library/react';
import { BaseBtn } from './base-btn';

describe('BaseBtn', () => {
  const href = 'https://bananarepublic.gap.com/';

  const setup = (props: Partial<React.ComponentProps<typeof BaseBtn>> = {}) => {
    const renderResult = render(
      <BaseBtn color='primary' href={href} size='large' {...props}>
        Button Text
      </BaseBtn>
    );
    const button = screen.getByRole('button', { name: /Button Text/i });
    return { ...renderResult, button };
  };

  test('It should render the component', () => {
    const { button } = setup();
    expect(button).toBeInTheDocument();
  });

  test('It should accept the className attr', () => {
    const className = 'custom-class';
    const { button } = setup({ className });
    expect(button).toHaveClass(className);
  });

  test('It should have data-testid equals to composable-btn-br', () => {
    const { button } = setup();
    expect(button).toHaveAttribute('data-testid', 'composable-btn-br');
  });

  describe('when disabled', () => {
    test('It should have the aria-disabled as true', () => {
      const { button } = setup({ disabled: true });
      expect(button).toHaveAttribute('aria-disabled', 'true');
    });

    test('It should NOT have the href', () => {
      const { button } = setup({ disabled: true });
      expect(button).not.toHaveAttribute('href');
    });
  });

  describe('when enabled', () => {
    test('It should NOT have the aria-disabled', () => {
      const { button } = setup();
      expect(button).not.toHaveAttribute('aria-disabled');
    });

    test('It should have the correct href', () => {
      const { button } = setup();
      expect(button).toHaveAttribute('href', href);
    });
  });
});
