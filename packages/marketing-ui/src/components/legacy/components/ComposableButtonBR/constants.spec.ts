// @ts-nocheck
import { COLORS, SIZES, VARIANTS } from './constants';

describe('ComposableButtonBR constants', () => {
  describe('COLORS', () => {
    it("should be equal to ['primary', 'secondary']", () => {
      expect(COLORS).toEqual(['primary', 'secondary']);
    });
  });

  describe('SIZES', () => {
    it("should be equal to ['large', 'small']", () => {
      expect(SIZES).toEqual(['large', 'small']);
    });
  });

  describe('VARIANTS', () => {
    it("should be equal to ['arrow', 'outline', 'solid', 'underline']", () => {
      expect(VARIANTS).toEqual(['arrow', 'outline', 'solid', 'underline']);
    });
  });
});
