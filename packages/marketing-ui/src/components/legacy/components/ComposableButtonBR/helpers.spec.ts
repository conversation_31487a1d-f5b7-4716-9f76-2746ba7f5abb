// @ts-nocheck
import { CtaButtonStylingProps } from '../../CMS/subcomponents/CTAButton';
import { Color as ComposableButtonColor, Variant as ComposableButtonVariant } from '../ComposableButton/types';
import { COLORS, SIZES, VARIANTS } from './constants';
import { isComposableButtonBRData } from './helpers';
import { Color, StylesObject, Variant } from './types';

describe('ComposableButtonBR helpers', () => {
  describe('isComposableButtonBRData', () => {
    it("should return true if the button's data is valid", () => {
      SIZES.forEach(size => {
        const data: StylesObject = { buttonSize: size };
        expect(isComposableButtonBRData(data)).toBe(true);
      });
      COLORS.forEach(color => {
        VARIANTS.forEach(variant => {
          const data: StylesObject = { buttonColor: color, buttonStyle: variant };
          expect(isComposableButtonBRData(data)).toBe(true);
        });
      });
      COLORS.forEach(color => {
        const data: StylesObject = { buttonColor: color };
        expect(isComposableButtonBRData(data)).toBe(true);
      });
      VARIANTS.forEach(variant => {
        const data: StylesObject = { buttonStyle: variant };
        expect(isComposableButtonBRData(data)).toBe(true);
      });
    });

    it("should return false if the button's data is invalid", () => {
      const CTAButtonColors = Object.values(ComposableButtonColor).filter(
        color => !COLORS.includes(color as Color) // remove the ones also used by ComposableButtonBR
      );
      CTAButtonColors.forEach(color => {
        const data: CtaButtonStylingProps = { buttonColor: color };
        expect(isComposableButtonBRData(data)).toBe(false);
      });
      const CTAButtonVariants = Object.values(ComposableButtonVariant).filter(
        variant => !VARIANTS.includes(variant as Variant) // remove the ones also used by ComposableButtonBR
      );
      CTAButtonVariants.forEach(variant => {
        const data: CtaButtonStylingProps = { buttonStyle: variant };
        expect(isComposableButtonBRData(data)).toBe(false);
      });
    });
  });
});
