// @ts-nocheck
'use client';
import { CtaButtonStylingProps } from '../../CMS/subcomponents/CTAButton';
import { COLORS, VARIANTS } from './constants';
import { StylesObject } from './types';

export const isComposableButtonBRData = (stylingObject: CtaButtonStylingProps | StylesObject): boolean => {
  const { buttonColor, buttonStyle } = stylingObject;
  const colors: string[] = [...COLORS];
  const isValidColor = Boolean(buttonColor && colors.includes(buttonColor));
  const variants: string[] = [...VARIANTS];
  const isValidStyle = Boolean(buttonStyle && variants.includes(buttonStyle));
  return 'buttonSize' in stylingObject || (isValidColor && isValidStyle) || isValidColor || isValidStyle;
};
