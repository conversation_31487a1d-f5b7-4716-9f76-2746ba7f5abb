'use client';
import React from 'react';
import { Brands, useTheme } from '@ecom-next/core/react-stitch';
import type { Meta, StoryObj } from '@storybook/react';
import { UnsupportedByBrand } from '../../stories/story-helpers';
import README from './README.mdx';
import { ComposableButtonBR } from './index';

type ComponentType = typeof ComposableButtonBR;

const { BananaRepublic, BananaRepublicFactoryStore } = Brands;

export default {
  argTypes: {
    color: {
      control: 'inline-radio',
      description: 'Control the button color, primary or secondary.',
      options: ['primary', 'secondary'],
      table: {
        defaultValue: { summary: 'primary' },
        type: { summary: 'enum' },
      },
    },
    disabled: {
      control: 'boolean',
      description: "Control if the button is disabled or enabled, if true or present, it's disabled.",
      table: {
        defaultValue: { summary: false },
        type: { summary: 'boolean' },
      },
    },
    label: {
      control: 'text',
      description: 'The label text string to be displayed in the button.',
      table: {
        type: { summary: 'string' },
      },
      type: { name: 'string', required: true },
    },
    size: {
      control: 'inline-radio',
      description: 'Control the button size, large or small. Only default and outline variants have sizes.',
      options: ['large', 'small'],
      table: {
        defaultValue: { summary: 'large' },
        type: { summary: 'enum' },
      },
    },
    value: {
      control: 'text',
      description: 'The URL to navigate when the button is clicked. If present, will be rendered as an anchor link.',
      table: {
        type: { summary: 'string', required: true },
      },
    },
    variant: {
      control: 'select',
      description: 'The variant / design for the button. Styles and interactions might differ from one to another.',
      options: ['arrow', 'outline', 'solid', 'underline'],
      table: {
        defaultValue: { summary: 'solid' },
        type: { summary: 'enum' },
      },
    },
  },
  component: ComposableButtonBR,
  decorators: [
    // Show "Unsupported by brand" in case not BR/BRfs
    (Story, context) => {
      const { brand } = useTheme();
      const { parameters } = context;
      if (![BananaRepublic, BananaRepublicFactoryStore].includes(brand)) {
        return <UnsupportedByBrand />;
      }
      return (
        <div style={{ background: parameters.backgrounds.default === 'dark' ? '#333333 !important' : '#F8F8F8 !important' }}>
          <Story />
        </div>
      );
    },
  ],
  parameters: {
    controls: { expanded: true, sort: 'alpha' },
    docs: { page: README },
    knobs: { disable: true },
    layout: 'centered',
    // ComposableButtonBR is identical for mobile and desktop; skip mobile VR tests. Update prop if mobile changes
    visualRegressionDevice: 'desktop',
  },
  tags: ['visual:check', 'exclude-at', 'exclude-gap', 'exclude-on', 'desktop-only'],
  title: 'Common/JSON Components (Marketing)/ComposableButtonBR',
} as Meta<ComponentType>;

type Story = StoryObj<ComponentType>;

const value = 'https://bananarepublic.gap.com/';
const darkBackground: Story = { parameters: { backgrounds: { default: 'dark' } } };
const lightBackground: Story = { parameters: { backgrounds: { default: 'light' } } };

const arrowArgs: Story['args'] = {
  label: 'Arrow Button'.toLocaleUpperCase(),
  value,
  variant: 'arrow',
};

export const Arrow: Story = {
  ...lightBackground,
  args: { ...arrowArgs },
};

export const Outline: Story = {
  ...darkBackground,
  args: {
    label: 'Outline Default'.toLocaleUpperCase(),
    value,
    variant: 'outline',
  },
};

const solidArgs: Story['args'] = {
  label: 'Solid Default'.toLocaleUpperCase(),
  value,
};

export const Solid: Story = {
  ...darkBackground,
  args: { ...solidArgs },
};

const underlineArgs: Story['args'] = {
  label: 'BR Text Button'.toLocaleUpperCase(),
  value,
  variant: 'underline',
};

export const Underline: Story = {
  args: { ...underlineArgs },
};
