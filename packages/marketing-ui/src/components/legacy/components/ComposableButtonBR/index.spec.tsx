// @ts-nocheck
import React from 'react';
import { render, screen } from '@testing-library/react';
import { withBRStyleProvider } from '../../helper/withBRStyleProvider';
import { ComposableButtonBR } from './index';
import { SIZES, VARIANTS } from './constants';

describe('ComposableButtonBR Component', () => {
  const href = 'https://bananarepublic.gap.com/';

  it('should behave like a button and have a label', () => {
    const label = 'Button';
    render(withBRStyleProvider(<ComposableButtonBR label={label} value={href} />));
    const button = screen.getByRole('button', { name: /Button/i });
    expect(button).toBeInTheDocument();
  });

  it('should accept disabled property', () => {
    const label = 'Disabled Button';
    render(withBRStyleProvider(<ComposableButtonBR disabled label={label} value={href} />));
    const button = screen.getByRole('button', { name: /Disabled Button/i });
    expect(button).toHaveAttribute('aria-disabled', 'true');
  });

  describe.each(VARIANTS)('Test for each variant', variant => {
    it(`should render up state properly for variant ${variant}`, () => {
      const label = `Variant ${variant}`.toUpperCase();
      const { container } = render(withBRStyleProvider(<ComposableButtonBR label={label} value={href} variant={variant} />));
      expect(container).toMatchSnapshot();
    });

    it(`should render disabled state properly for variant ${variant}`, () => {
      const label = `Variant ${variant}`.toUpperCase();
      const { container, getByText } = render(withBRStyleProvider(<ComposableButtonBR disabled label={label} value={href} variant={variant} />));
      const button = getByText(label);
      expect(button).toHaveAttribute('aria-disabled', 'true');
      expect(container).toMatchSnapshot();
    });

    describe.each(SIZES)('Test for each size', size => {
      it(`should shown size ${size} correctly for variant ${variant}`, () => {
        const label = `${variant} ${size}`.toUpperCase();
        const { container } = render(withBRStyleProvider(<ComposableButtonBR label={label} size={size} value={href} variant={variant} />));
        expect(container).toMatchSnapshot();
      });
    });
  });
});
