// @ts-nocheck
'use client';
import React from 'react';
import { ComponentProps, VariantComponent, Variant } from './types';
import { ArrowVariant } from './variants/ArrowVariant';
import { OutlineVariant } from './variants/OutlineVariant';
import { SolidVariant } from './variants/SolidVariant';
import { UnderlineVariant } from './variants/UnderlineVariant';

const variants: Record<Variant, VariantComponent> = {
  arrow: ArrowVariant,
  outline: OutlineVariant,
  solid: SolidVariant,
  underline: UnderlineVariant,
};

export const ComposableButtonBR = (props: ComponentProps) => {
  const { color = 'primary', disabled = false, label, size = 'large', variant = 'solid', value, ...rest } = props;
  const BtnVariant = variants[variant];

  return (
    <BtnVariant {...rest} {...(disabled ? { disabled: true } : {})} color={color} href={value} size={size}>
      {label.toLocaleUpperCase()}
    </BtnVariant>
  );
};
