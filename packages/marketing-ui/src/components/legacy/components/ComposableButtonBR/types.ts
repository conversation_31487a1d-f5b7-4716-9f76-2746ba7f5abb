'use client';

import React from 'react';
import { COLORS, SIZES, VARIANTS } from './constants';

export type Color = (typeof COLORS)[number];

export type CTA = {
  label?: string;
  value?: string;
};

export type Size = (typeof SIZES)[number];

export type Variant = (typeof VARIANTS)[number];

export type ComponentProps = {
  color?: Color;
  disabled?: boolean;
  label: string;
  size?: Size;
  value: string;
  variant?: Variant;
};

export type CTAButtonStyling = {
  buttonColor: Color;
  buttonSize: Size;
  buttonStyle: Variant;
};

export type VariantProps = {
  children: React.ReactNode;
  className?: string;
  color: Color;
  disabled?: true;
  href: string;
  size: Size;
};

export type VariantComponent = (props: VariantProps) => React.JSX.Element;

export type StylesObject = {
  buttonColor?: Color;
  buttonSize?: Size;
  buttonStyle?: Variant;
};
