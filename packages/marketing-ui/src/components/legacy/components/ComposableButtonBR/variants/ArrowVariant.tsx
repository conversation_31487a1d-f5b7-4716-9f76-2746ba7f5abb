// @ts-nocheck
'use client';
import { styled } from '@ecom-next/core/react-stitch';
import React from 'react';
import { BaseBtn } from '../base-btn';
import { VariantComponent } from '../types';

const StyledArrowBtn = styled(BaseBtn)(({ color, theme }) => {
  const isSecondary = color === 'secondary';
  return {
    alignItems: 'center',
    backgroundColor: 'transparent',
    border: 0,
    boxSizing: 'border-box',
    color: isSecondary ? theme.color.wh : theme.color.bk,
    display: 'inline-flex',
    fontFamily: theme.brandFont.fontFamily,
    fontSize: 12,
    fontWeight: 350,
    gap: 8,
    height: 16,
    letterSpacing: 0.5,
    lineHeight: 1.33, // 16px
    outline: 0,
    padding: 0,
    userSelect: 'none',
    '& svg': {
      fill: isSecondary ? theme.color.wh : theme.color.bk,
      transition: 'width 400ms',
    },
    '& svg .arrow-line': {
      transition: 'transform 400ms',
    },
    '&:hover:not([aria-disabled=true]) svg': {
      width: 102,
    },
    '&:hover:not([aria-disabled=true]) svg .arrow-line': {
      // todo: magic numbers? order must be respected
      transform: 'translateX(-23px) scaleX(1.28)',
    },
    '&[aria-disabled=true]': {
      color: isSecondary ? theme.color.whAlpha25 : theme.color.gray40,
      pointerEvents: 'none',
    },
    '&[aria-disabled=true] svg': {
      fill: isSecondary ? theme.color.whAlpha25 : theme.color.gray40,
    },
  };
});

const ArrowIcon = () => (
  <svg height='6' preserveAspectRatio='xMaxYMin' viewBox='0 0 79 6' width='79' xmlns='http://www.w3.org/2000/svg'>
    <g>
      <path className='arrow-head' d='M73.5 0L78.5 3L73.5 6L75.6429 3L73.5 0Z' />
      <rect className='arrow-line' height='1' width='77' y='2.5' />
    </g>
  </svg>
);

export const ArrowVariant: VariantComponent = ({ children, ...rest }) => (
  <StyledArrowBtn {...rest}>
    {children}
    <ArrowIcon />
  </StyledArrowBtn>
);
