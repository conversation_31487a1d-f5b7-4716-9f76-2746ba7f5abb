// @ts-nocheck
'use client';
import { styled } from '@ecom-next/core/react-stitch';
import React from 'react';
import { BaseBtn } from '../base-btn';
import { VariantComponent } from '../types';

const StyledOutlineBtn = styled(BaseBtn)(({ color, size, theme }) => {
  const isSmall = size === 'small';
  const isSecondary = color === 'secondary';
  return {
    alignItems: 'center',
    border: `1px solid ${isSecondary ? theme.color.wh : theme.color.bk}`,
    boxSizing: 'border-box',
    color: isSecondary ? theme.color.wh : theme.color.bk,
    display: 'inline-flex',
    fontFamily: theme.brandFont.fontFamily,
    fontSize: isSmall ? 9 : 12,
    fontWeight: 600,
    height: isSmall ? 32 : 56,
    justifyContent: 'center',
    letterSpacing: 1,
    lineHeight: 1.33, // 12px for small and 16px for large
    outline: 0,
    overflow: 'hidden',
    padding: 0,
    transition: 'border-color 200ms',
    userSelect: 'none',
    whiteSpace: 'nowrap',
    width: isSmall ? 244 : 280,
    '&[aria-disabled=true]': {
      color: isSecondary ? theme.color.whAlpha25 : theme.color.bkAlpha25,
      borderColor: isSecondary ? theme.color.whAlpha25 : theme.color.bkAlpha25,
      pointerEvents: 'none',
    },
  };
});

export const OutlineVariant: VariantComponent = ({ children, ...rest }) => <StyledOutlineBtn {...rest}>{children}</StyledOutlineBtn>;
