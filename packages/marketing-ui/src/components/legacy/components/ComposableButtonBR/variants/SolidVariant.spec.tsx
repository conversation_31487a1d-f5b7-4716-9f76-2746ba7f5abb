// @ts-nocheck
import React from 'react';
import { render, screen } from '@testing-library/react';
import { withBRStyleProvider } from '../../../helper/withBRStyleProvider';
import { COLORS, SIZES } from '../constants';
import { SolidVariant } from './SolidVariant';

describe('SolidVariant', () => {
  const href = 'https://bananarepublic.gap.com/';

  const setup = (props: Partial<React.ComponentProps<typeof SolidVariant>> = {}) => {
    const utils = render(
      withBRStyleProvider(
        <SolidVariant color='primary' href={href} size='large' {...props}>
          Button Text
        </SolidVariant>
      )
    );
    const button = screen.getByRole('button', { name: /Button Text/i });
    return { ...utils, button };
  };

  describe.each(SIZES)('%s', size => {
    describe.each(COLORS)('%s', color => {
      test('It should render the component', () => {
        const { button } = setup({ color, size });
        expect(button).toBeInTheDocument();
        expect(button).toHaveAttribute('data-testid', 'composable-btn-br');
      });

      test('It should match the snapshot', () => {
        const { button } = setup({ color, size });
        expect(button).toMatchSnapshot();
      });

      test('It should match the snapshot when disabled', () => {
        const { button } = setup({ color, disabled: true, size });
        expect(button).toMatchSnapshot();
      });
    });
  });
});
