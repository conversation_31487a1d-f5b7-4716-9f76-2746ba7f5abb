// @ts-nocheck
'use client';
import { styled } from '@ecom-next/core/react-stitch';
import React from 'react';
import { BaseBtn } from '../base-btn';
import { VariantComponent } from '../types';

const StyledSolidBn = styled(BaseBtn)(({ color, size, theme }) => {
  const isSmall = size === 'small';
  const isSecondary = color === 'secondary';
  return {
    alignItems: 'center',
    backgroundColor: isSecondary ? theme.color.inverse.b2 : theme.color.wh,
    border: 0,
    boxSizing: 'border-box',
    color: theme.color.bk,
    display: 'inline-flex',
    fontFamily: theme.brandFont.fontFamily,
    fontSize: isSmall ? 9 : 12,
    fontWeight: 600,
    height: isSmall ? 32 : 56,
    justifyContent: 'center',
    letterSpacing: 1,
    lineHeight: 1.33, // 12px for small and 16px for large
    outline: 0,
    overflow: 'hidden',
    padding: 0,
    userSelect: 'none',
    whiteSpace: 'nowrap',
    width: isSmall ? 244 : 280,
    '&[aria-disabled=true]': {
      ...(isSecondary ? { backgroundColor: theme.color.b2 } : {}),
      color: theme.color.bkAlpha25,
      pointerEvents: 'none',
    },
  };
});

export const SolidVariant: VariantComponent = ({ children, ...rest }) => <StyledSolidBn {...rest}>{children}</StyledSolidBn>;
