// @ts-nocheck
import React from 'react';
import { render, screen } from '@testing-library/react';
import { withBRStyleProvider } from '../../../helper/withBRStyleProvider';
import { COLORS } from '../constants';
import { UnderlineVariant } from './UnderlineVariant';

describe('UnderlineVariant', () => {
  const href = 'https://bananarepublic.gap.com/';

  const setup = (props: Partial<React.ComponentProps<typeof UnderlineVariant>> = {}) => {
    const utils = render(
      withBRStyleProvider(
        <UnderlineVariant color='primary' href={href} size='large' {...props}>
          Button Text
        </UnderlineVariant>
      )
    );
    const button = screen.getByRole('button', { name: /Button Text/i });
    return { ...utils, button };
  };

  describe.each(COLORS)('%s', color => {
    test('It should render the component', () => {
      const { button } = setup({ color });
      expect(button).toBeInTheDocument();
      expect(button).toHaveAttribute('data-testid', 'composable-btn-br');
    });

    test('It should match the snapshot', () => {
      const { button } = setup({ color });
      expect(button).toMatchSnapshot();
    });

    test('It should match the snapshot when disabled', () => {
      const { button } = setup({ color, disabled: true });
      expect(button).toMatchSnapshot();
    });
  });
});
