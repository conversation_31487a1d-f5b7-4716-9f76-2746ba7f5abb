// @ts-nocheck
'use client';
import { styled } from '@ecom-next/core/react-stitch';
import React from 'react';
import { BaseBtn } from '../base-btn';
import { VariantComponent } from '../types';

const UnderlineBtn = styled(BaseBtn)(({ color, theme }) => {
  const isSecondary = color === 'secondary';
  return {
    backgroundColor: 'transparent',
    border: 0,
    boxSizing: 'border-box',
    color: isSecondary ? theme.color.wh : theme.color.bk,
    display: 'inline-block',
    fontFamily: theme.brandFont.fontFamily,
    fontSize: 12,
    fontWeight: 400,
    height: 16,
    letterSpacing: 0.5,
    lineHeight: 1.33, // 16px
    outline: 0,
    padding: 0,
    textAlign: 'center',
    textDecoration: 'underline',
    textDecorationColor: isSecondary ? theme.color.wh : theme.color.bk,
    textUnderlineOffset: 5,
    transition: 'text-decoration-color 200ms',
    userSelect: 'none',
    '&[aria-disabled=true]': {
      color: isSecondary ? theme.color.whAlpha25 : theme.color.gray40,
      pointerEvents: 'none',
      textDecorationColor: isSecondary ? theme.color.whAlpha25 : theme.color.gray20,
    },
    '&:hover:not([aria-disabled=true])': {
      textDecorationColor: isSecondary ? theme.color.wh : theme.color.bk,
    },
  };
});

export const UnderlineVariant: VariantComponent = ({ children, ...rest }) => <UnderlineBtn {...rest}>{children}</UnderlineBtn>;
