// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`ArrowVariant primary It should match the snapshot 1`] = `
.emotion-0 {
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  background-color: transparent;
  border: 0;
  box-sizing: border-box;
  color: #000000;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  font-size: 12px;
  font-weight: 350;
  gap: 8px;
  height: 16px;
  letter-spacing: 0.5px;
  line-height: 1.33;
  outline: 0;
  padding: 0;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

.emotion-0 svg {
  fill: #000000;
  -webkit-transition: width 400ms;
  transition: width 400ms;
}

.emotion-0 svg .arrow-line {
  -webkit-transition: -webkit-transform 400ms;
  transition: transform 400ms;
}

.emotion-0:hover:not([aria-disabled=true]) svg {
  width: 102px;
}

.emotion-0:hover:not([aria-disabled=true]) svg .arrow-line {
  -webkit-transform: translateX(-23px) scaleX(1.28);
  -moz-transform: translateX(-23px) scaleX(1.28);
  -ms-transform: translateX(-23px) scaleX(1.28);
  transform: translateX(-23px) scaleX(1.28);
}

.emotion-0[aria-disabled=true] {
  color: #999999;
  pointer-events: none;
}

.emotion-0[aria-disabled=true] svg {
  fill: #999999;
}

<a
  class="emotion-0"
  data-testid="composable-btn-br"
  href="https://bananarepublic.gap.com/"
  role="button"
>
  Button Text
  <svg
    height="6"
    preserveAspectRatio="xMaxYMin"
    viewBox="0 0 79 6"
    width="79"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g>
      <path
        class="arrow-head"
        d="M73.5 0L78.5 3L73.5 6L75.6429 3L73.5 0Z"
      />
      <rect
        class="arrow-line"
        height="1"
        width="77"
        y="2.5"
      />
    </g>
  </svg>
</a>
`;

exports[`ArrowVariant primary It should match the snapshot when disabled 1`] = `
.emotion-0 {
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  background-color: transparent;
  border: 0;
  box-sizing: border-box;
  color: #000000;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  font-size: 12px;
  font-weight: 350;
  gap: 8px;
  height: 16px;
  letter-spacing: 0.5px;
  line-height: 1.33;
  outline: 0;
  padding: 0;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

.emotion-0 svg {
  fill: #000000;
  -webkit-transition: width 400ms;
  transition: width 400ms;
}

.emotion-0 svg .arrow-line {
  -webkit-transition: -webkit-transform 400ms;
  transition: transform 400ms;
}

.emotion-0:hover:not([aria-disabled=true]) svg {
  width: 102px;
}

.emotion-0:hover:not([aria-disabled=true]) svg .arrow-line {
  -webkit-transform: translateX(-23px) scaleX(1.28);
  -moz-transform: translateX(-23px) scaleX(1.28);
  -ms-transform: translateX(-23px) scaleX(1.28);
  transform: translateX(-23px) scaleX(1.28);
}

.emotion-0[aria-disabled=true] {
  color: #999999;
  pointer-events: none;
}

.emotion-0[aria-disabled=true] svg {
  fill: #999999;
}

<a
  aria-disabled="true"
  class="emotion-0"
  data-testid="composable-btn-br"
  role="button"
>
  Button Text
  <svg
    height="6"
    preserveAspectRatio="xMaxYMin"
    viewBox="0 0 79 6"
    width="79"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g>
      <path
        class="arrow-head"
        d="M73.5 0L78.5 3L73.5 6L75.6429 3L73.5 0Z"
      />
      <rect
        class="arrow-line"
        height="1"
        width="77"
        y="2.5"
      />
    </g>
  </svg>
</a>
`;

exports[`ArrowVariant secondary It should match the snapshot 1`] = `
.emotion-0 {
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  background-color: transparent;
  border: 0;
  box-sizing: border-box;
  color: #FFFFFF;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  font-size: 12px;
  font-weight: 350;
  gap: 8px;
  height: 16px;
  letter-spacing: 0.5px;
  line-height: 1.33;
  outline: 0;
  padding: 0;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

.emotion-0 svg {
  fill: #FFFFFF;
  -webkit-transition: width 400ms;
  transition: width 400ms;
}

.emotion-0 svg .arrow-line {
  -webkit-transition: -webkit-transform 400ms;
  transition: transform 400ms;
}

.emotion-0:hover:not([aria-disabled=true]) svg {
  width: 102px;
}

.emotion-0:hover:not([aria-disabled=true]) svg .arrow-line {
  -webkit-transform: translateX(-23px) scaleX(1.28);
  -moz-transform: translateX(-23px) scaleX(1.28);
  -ms-transform: translateX(-23px) scaleX(1.28);
  transform: translateX(-23px) scaleX(1.28);
}

.emotion-0[aria-disabled=true] {
  color: rgba(255, 255, 255, 0.25);
  pointer-events: none;
}

.emotion-0[aria-disabled=true] svg {
  fill: rgba(255, 255, 255, 0.25);
}

<a
  class="emotion-0"
  data-testid="composable-btn-br"
  href="https://bananarepublic.gap.com/"
  role="button"
>
  Button Text
  <svg
    height="6"
    preserveAspectRatio="xMaxYMin"
    viewBox="0 0 79 6"
    width="79"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g>
      <path
        class="arrow-head"
        d="M73.5 0L78.5 3L73.5 6L75.6429 3L73.5 0Z"
      />
      <rect
        class="arrow-line"
        height="1"
        width="77"
        y="2.5"
      />
    </g>
  </svg>
</a>
`;

exports[`ArrowVariant secondary It should match the snapshot when disabled 1`] = `
.emotion-0 {
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  background-color: transparent;
  border: 0;
  box-sizing: border-box;
  color: #FFFFFF;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  font-size: 12px;
  font-weight: 350;
  gap: 8px;
  height: 16px;
  letter-spacing: 0.5px;
  line-height: 1.33;
  outline: 0;
  padding: 0;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

.emotion-0 svg {
  fill: #FFFFFF;
  -webkit-transition: width 400ms;
  transition: width 400ms;
}

.emotion-0 svg .arrow-line {
  -webkit-transition: -webkit-transform 400ms;
  transition: transform 400ms;
}

.emotion-0:hover:not([aria-disabled=true]) svg {
  width: 102px;
}

.emotion-0:hover:not([aria-disabled=true]) svg .arrow-line {
  -webkit-transform: translateX(-23px) scaleX(1.28);
  -moz-transform: translateX(-23px) scaleX(1.28);
  -ms-transform: translateX(-23px) scaleX(1.28);
  transform: translateX(-23px) scaleX(1.28);
}

.emotion-0[aria-disabled=true] {
  color: rgba(255, 255, 255, 0.25);
  pointer-events: none;
}

.emotion-0[aria-disabled=true] svg {
  fill: rgba(255, 255, 255, 0.25);
}

<a
  aria-disabled="true"
  class="emotion-0"
  data-testid="composable-btn-br"
  role="button"
>
  Button Text
  <svg
    height="6"
    preserveAspectRatio="xMaxYMin"
    viewBox="0 0 79 6"
    width="79"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g>
      <path
        class="arrow-head"
        d="M73.5 0L78.5 3L73.5 6L75.6429 3L73.5 0Z"
      />
      <rect
        class="arrow-line"
        height="1"
        width="77"
        y="2.5"
      />
    </g>
  </svg>
</a>
`;
