// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`OutlineVariant large primary It should match the snapshot 1`] = `
.emotion-0 {
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  border: 1px solid #000000;
  box-sizing: border-box;
  color: #000000;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  font-size: 12px;
  font-weight: 600;
  height: 56px;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  letter-spacing: 1px;
  line-height: 1.33;
  outline: 0;
  overflow: hidden;
  padding: 0;
  -webkit-transition: border-color 200ms;
  transition: border-color 200ms;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  white-space: nowrap;
  width: 280px;
}

.emotion-0[aria-disabled=true] {
  color: rgba(0, 0, 0, 0.25);
  border-color: rgba(0, 0, 0, 0.25);
  pointer-events: none;
}

<a
  class="emotion-0"
  data-testid="composable-btn-br"
  href="https://bananarepublic.gap.com/"
  role="button"
>
  Button Text
</a>
`;

exports[`OutlineVariant large primary It should match the snapshot when disabled 1`] = `
.emotion-0 {
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  border: 1px solid #000000;
  box-sizing: border-box;
  color: #000000;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  font-size: 12px;
  font-weight: 600;
  height: 56px;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  letter-spacing: 1px;
  line-height: 1.33;
  outline: 0;
  overflow: hidden;
  padding: 0;
  -webkit-transition: border-color 200ms;
  transition: border-color 200ms;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  white-space: nowrap;
  width: 280px;
}

.emotion-0[aria-disabled=true] {
  color: rgba(0, 0, 0, 0.25);
  border-color: rgba(0, 0, 0, 0.25);
  pointer-events: none;
}

<a
  aria-disabled="true"
  class="emotion-0"
  data-testid="composable-btn-br"
  role="button"
>
  Button Text
</a>
`;

exports[`OutlineVariant large secondary It should match the snapshot 1`] = `
.emotion-0 {
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  border: 1px solid #FFFFFF;
  box-sizing: border-box;
  color: #FFFFFF;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  font-size: 12px;
  font-weight: 600;
  height: 56px;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  letter-spacing: 1px;
  line-height: 1.33;
  outline: 0;
  overflow: hidden;
  padding: 0;
  -webkit-transition: border-color 200ms;
  transition: border-color 200ms;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  white-space: nowrap;
  width: 280px;
}

.emotion-0[aria-disabled=true] {
  color: rgba(255, 255, 255, 0.25);
  border-color: rgba(255, 255, 255, 0.25);
  pointer-events: none;
}

<a
  class="emotion-0"
  data-testid="composable-btn-br"
  href="https://bananarepublic.gap.com/"
  role="button"
>
  Button Text
</a>
`;

exports[`OutlineVariant large secondary It should match the snapshot when disabled 1`] = `
.emotion-0 {
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  border: 1px solid #FFFFFF;
  box-sizing: border-box;
  color: #FFFFFF;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  font-size: 12px;
  font-weight: 600;
  height: 56px;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  letter-spacing: 1px;
  line-height: 1.33;
  outline: 0;
  overflow: hidden;
  padding: 0;
  -webkit-transition: border-color 200ms;
  transition: border-color 200ms;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  white-space: nowrap;
  width: 280px;
}

.emotion-0[aria-disabled=true] {
  color: rgba(255, 255, 255, 0.25);
  border-color: rgba(255, 255, 255, 0.25);
  pointer-events: none;
}

<a
  aria-disabled="true"
  class="emotion-0"
  data-testid="composable-btn-br"
  role="button"
>
  Button Text
</a>
`;

exports[`OutlineVariant small primary It should match the snapshot 1`] = `
.emotion-0 {
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  border: 1px solid #000000;
  box-sizing: border-box;
  color: #000000;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  font-size: 9px;
  font-weight: 600;
  height: 32px;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  letter-spacing: 1px;
  line-height: 1.33;
  outline: 0;
  overflow: hidden;
  padding: 0;
  -webkit-transition: border-color 200ms;
  transition: border-color 200ms;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  white-space: nowrap;
  width: 244px;
}

.emotion-0[aria-disabled=true] {
  color: rgba(0, 0, 0, 0.25);
  border-color: rgba(0, 0, 0, 0.25);
  pointer-events: none;
}

<a
  class="emotion-0"
  data-testid="composable-btn-br"
  href="https://bananarepublic.gap.com/"
  role="button"
>
  Button Text
</a>
`;

exports[`OutlineVariant small primary It should match the snapshot when disabled 1`] = `
.emotion-0 {
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  border: 1px solid #000000;
  box-sizing: border-box;
  color: #000000;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  font-size: 9px;
  font-weight: 600;
  height: 32px;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  letter-spacing: 1px;
  line-height: 1.33;
  outline: 0;
  overflow: hidden;
  padding: 0;
  -webkit-transition: border-color 200ms;
  transition: border-color 200ms;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  white-space: nowrap;
  width: 244px;
}

.emotion-0[aria-disabled=true] {
  color: rgba(0, 0, 0, 0.25);
  border-color: rgba(0, 0, 0, 0.25);
  pointer-events: none;
}

<a
  aria-disabled="true"
  class="emotion-0"
  data-testid="composable-btn-br"
  role="button"
>
  Button Text
</a>
`;

exports[`OutlineVariant small secondary It should match the snapshot 1`] = `
.emotion-0 {
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  border: 1px solid #FFFFFF;
  box-sizing: border-box;
  color: #FFFFFF;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  font-size: 9px;
  font-weight: 600;
  height: 32px;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  letter-spacing: 1px;
  line-height: 1.33;
  outline: 0;
  overflow: hidden;
  padding: 0;
  -webkit-transition: border-color 200ms;
  transition: border-color 200ms;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  white-space: nowrap;
  width: 244px;
}

.emotion-0[aria-disabled=true] {
  color: rgba(255, 255, 255, 0.25);
  border-color: rgba(255, 255, 255, 0.25);
  pointer-events: none;
}

<a
  class="emotion-0"
  data-testid="composable-btn-br"
  href="https://bananarepublic.gap.com/"
  role="button"
>
  Button Text
</a>
`;

exports[`OutlineVariant small secondary It should match the snapshot when disabled 1`] = `
.emotion-0 {
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  border: 1px solid #FFFFFF;
  box-sizing: border-box;
  color: #FFFFFF;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  font-size: 9px;
  font-weight: 600;
  height: 32px;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  letter-spacing: 1px;
  line-height: 1.33;
  outline: 0;
  overflow: hidden;
  padding: 0;
  -webkit-transition: border-color 200ms;
  transition: border-color 200ms;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  white-space: nowrap;
  width: 244px;
}

.emotion-0[aria-disabled=true] {
  color: rgba(255, 255, 255, 0.25);
  border-color: rgba(255, 255, 255, 0.25);
  pointer-events: none;
}

<a
  aria-disabled="true"
  class="emotion-0"
  data-testid="composable-btn-br"
  role="button"
>
  Button Text
</a>
`;
