// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`SolidVariant large primary It should match the snapshot 1`] = `
.emotion-0 {
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  background-color: #FFFFFF;
  border: 0;
  box-sizing: border-box;
  color: #000000;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  font-size: 12px;
  font-weight: 600;
  height: 56px;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  letter-spacing: 1px;
  line-height: 1.33;
  outline: 0;
  overflow: hidden;
  padding: 0;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  white-space: nowrap;
  width: 280px;
}

.emotion-0[aria-disabled=true] {
  color: rgba(0, 0, 0, 0.25);
  pointer-events: none;
}

<a
  class="emotion-0"
  data-testid="composable-btn-br"
  href="https://bananarepublic.gap.com/"
  role="button"
>
  Button Text
</a>
`;

exports[`SolidVariant large primary It should match the snapshot when disabled 1`] = `
.emotion-0 {
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  background-color: #FFFFFF;
  border: 0;
  box-sizing: border-box;
  color: #000000;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  font-size: 12px;
  font-weight: 600;
  height: 56px;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  letter-spacing: 1px;
  line-height: 1.33;
  outline: 0;
  overflow: hidden;
  padding: 0;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  white-space: nowrap;
  width: 280px;
}

.emotion-0[aria-disabled=true] {
  color: rgba(0, 0, 0, 0.25);
  pointer-events: none;
}

<a
  aria-disabled="true"
  class="emotion-0"
  data-testid="composable-btn-br"
  role="button"
>
  Button Text
</a>
`;

exports[`SolidVariant large secondary It should match the snapshot 1`] = `
.emotion-0 {
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  background-color: #E4D6C8;
  border: 0;
  box-sizing: border-box;
  color: #000000;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  font-size: 12px;
  font-weight: 600;
  height: 56px;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  letter-spacing: 1px;
  line-height: 1.33;
  outline: 0;
  overflow: hidden;
  padding: 0;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  white-space: nowrap;
  width: 280px;
}

.emotion-0[aria-disabled=true] {
  background-color: #FFFFFF;
  color: rgba(0, 0, 0, 0.25);
  pointer-events: none;
}

<a
  class="emotion-0"
  data-testid="composable-btn-br"
  href="https://bananarepublic.gap.com/"
  role="button"
>
  Button Text
</a>
`;

exports[`SolidVariant large secondary It should match the snapshot when disabled 1`] = `
.emotion-0 {
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  background-color: #E4D6C8;
  border: 0;
  box-sizing: border-box;
  color: #000000;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  font-size: 12px;
  font-weight: 600;
  height: 56px;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  letter-spacing: 1px;
  line-height: 1.33;
  outline: 0;
  overflow: hidden;
  padding: 0;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  white-space: nowrap;
  width: 280px;
}

.emotion-0[aria-disabled=true] {
  background-color: #FFFFFF;
  color: rgba(0, 0, 0, 0.25);
  pointer-events: none;
}

<a
  aria-disabled="true"
  class="emotion-0"
  data-testid="composable-btn-br"
  role="button"
>
  Button Text
</a>
`;

exports[`SolidVariant small primary It should match the snapshot 1`] = `
.emotion-0 {
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  background-color: #FFFFFF;
  border: 0;
  box-sizing: border-box;
  color: #000000;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  font-size: 9px;
  font-weight: 600;
  height: 32px;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  letter-spacing: 1px;
  line-height: 1.33;
  outline: 0;
  overflow: hidden;
  padding: 0;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  white-space: nowrap;
  width: 244px;
}

.emotion-0[aria-disabled=true] {
  color: rgba(0, 0, 0, 0.25);
  pointer-events: none;
}

<a
  class="emotion-0"
  data-testid="composable-btn-br"
  href="https://bananarepublic.gap.com/"
  role="button"
>
  Button Text
</a>
`;

exports[`SolidVariant small primary It should match the snapshot when disabled 1`] = `
.emotion-0 {
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  background-color: #FFFFFF;
  border: 0;
  box-sizing: border-box;
  color: #000000;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  font-size: 9px;
  font-weight: 600;
  height: 32px;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  letter-spacing: 1px;
  line-height: 1.33;
  outline: 0;
  overflow: hidden;
  padding: 0;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  white-space: nowrap;
  width: 244px;
}

.emotion-0[aria-disabled=true] {
  color: rgba(0, 0, 0, 0.25);
  pointer-events: none;
}

<a
  aria-disabled="true"
  class="emotion-0"
  data-testid="composable-btn-br"
  role="button"
>
  Button Text
</a>
`;

exports[`SolidVariant small secondary It should match the snapshot 1`] = `
.emotion-0 {
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  background-color: #E4D6C8;
  border: 0;
  box-sizing: border-box;
  color: #000000;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  font-size: 9px;
  font-weight: 600;
  height: 32px;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  letter-spacing: 1px;
  line-height: 1.33;
  outline: 0;
  overflow: hidden;
  padding: 0;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  white-space: nowrap;
  width: 244px;
}

.emotion-0[aria-disabled=true] {
  background-color: #FFFFFF;
  color: rgba(0, 0, 0, 0.25);
  pointer-events: none;
}

<a
  class="emotion-0"
  data-testid="composable-btn-br"
  href="https://bananarepublic.gap.com/"
  role="button"
>
  Button Text
</a>
`;

exports[`SolidVariant small secondary It should match the snapshot when disabled 1`] = `
.emotion-0 {
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  background-color: #E4D6C8;
  border: 0;
  box-sizing: border-box;
  color: #000000;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  font-size: 9px;
  font-weight: 600;
  height: 32px;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  letter-spacing: 1px;
  line-height: 1.33;
  outline: 0;
  overflow: hidden;
  padding: 0;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  white-space: nowrap;
  width: 244px;
}

.emotion-0[aria-disabled=true] {
  background-color: #FFFFFF;
  color: rgba(0, 0, 0, 0.25);
  pointer-events: none;
}

<a
  aria-disabled="true"
  class="emotion-0"
  data-testid="composable-btn-br"
  role="button"
>
  Button Text
</a>
`;
