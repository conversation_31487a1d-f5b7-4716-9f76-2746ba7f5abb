// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`UnderlineVariant primary It should match the snapshot 1`] = `
.emotion-0 {
  background-color: transparent;
  border: 0;
  box-sizing: border-box;
  color: #000000;
  display: inline-block;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  font-size: 12px;
  font-weight: 400;
  height: 16px;
  letter-spacing: 0.5px;
  line-height: 1.33;
  outline: 0;
  padding: 0;
  text-align: center;
  -webkit-text-decoration: underline;
  text-decoration: underline;
  text-decoration-color: #000000;
  text-underline-offset: 5px;
  -webkit-transition: text-decoration-color 200ms;
  transition: text-decoration-color 200ms;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

.emotion-0[aria-disabled=true] {
  color: #999999;
  pointer-events: none;
  text-decoration-color: #CCCCCC;
}

.emotion-0:hover:not([aria-disabled=true]) {
  text-decoration-color: #000000;
}

<a
  class="emotion-0"
  data-testid="composable-btn-br"
  href="https://bananarepublic.gap.com/"
  role="button"
>
  Button Text
</a>
`;

exports[`UnderlineVariant primary It should match the snapshot when disabled 1`] = `
.emotion-0 {
  background-color: transparent;
  border: 0;
  box-sizing: border-box;
  color: #000000;
  display: inline-block;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  font-size: 12px;
  font-weight: 400;
  height: 16px;
  letter-spacing: 0.5px;
  line-height: 1.33;
  outline: 0;
  padding: 0;
  text-align: center;
  -webkit-text-decoration: underline;
  text-decoration: underline;
  text-decoration-color: #000000;
  text-underline-offset: 5px;
  -webkit-transition: text-decoration-color 200ms;
  transition: text-decoration-color 200ms;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

.emotion-0[aria-disabled=true] {
  color: #999999;
  pointer-events: none;
  text-decoration-color: #CCCCCC;
}

.emotion-0:hover:not([aria-disabled=true]) {
  text-decoration-color: #000000;
}

<a
  aria-disabled="true"
  class="emotion-0"
  data-testid="composable-btn-br"
  role="button"
>
  Button Text
</a>
`;

exports[`UnderlineVariant secondary It should match the snapshot 1`] = `
.emotion-0 {
  background-color: transparent;
  border: 0;
  box-sizing: border-box;
  color: #FFFFFF;
  display: inline-block;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  font-size: 12px;
  font-weight: 400;
  height: 16px;
  letter-spacing: 0.5px;
  line-height: 1.33;
  outline: 0;
  padding: 0;
  text-align: center;
  -webkit-text-decoration: underline;
  text-decoration: underline;
  text-decoration-color: #FFFFFF;
  text-underline-offset: 5px;
  -webkit-transition: text-decoration-color 200ms;
  transition: text-decoration-color 200ms;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

.emotion-0[aria-disabled=true] {
  color: rgba(255, 255, 255, 0.25);
  pointer-events: none;
  text-decoration-color: rgba(255, 255, 255, 0.25);
}

.emotion-0:hover:not([aria-disabled=true]) {
  text-decoration-color: #FFFFFF;
}

<a
  class="emotion-0"
  data-testid="composable-btn-br"
  href="https://bananarepublic.gap.com/"
  role="button"
>
  Button Text
</a>
`;

exports[`UnderlineVariant secondary It should match the snapshot when disabled 1`] = `
.emotion-0 {
  background-color: transparent;
  border: 0;
  box-sizing: border-box;
  color: #FFFFFF;
  display: inline-block;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  font-size: 12px;
  font-weight: 400;
  height: 16px;
  letter-spacing: 0.5px;
  line-height: 1.33;
  outline: 0;
  padding: 0;
  text-align: center;
  -webkit-text-decoration: underline;
  text-decoration: underline;
  text-decoration-color: #FFFFFF;
  text-underline-offset: 5px;
  -webkit-transition: text-decoration-color 200ms;
  transition: text-decoration-color 200ms;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

.emotion-0[aria-disabled=true] {
  color: rgba(255, 255, 255, 0.25);
  pointer-events: none;
  text-decoration-color: rgba(255, 255, 255, 0.25);
}

.emotion-0:hover:not([aria-disabled=true]) {
  text-decoration-color: #FFFFFF;
}

<a
  aria-disabled="true"
  class="emotion-0"
  data-testid="composable-btn-br"
  role="button"
>
  Button Text
</a>
`;
