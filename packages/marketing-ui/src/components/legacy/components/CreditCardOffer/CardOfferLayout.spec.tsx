// @ts-nocheck
import React from 'react';
import userEvent from '@testing-library/user-event';
import { render, screen, fireEvent, within, act } from 'test-utils';
import { SMALL, LARGE, BreakpointProvider } from '@ecom-next/core/breakpoint-provider';
import { PersonalizationContext } from '@ecom-next/sitewide/personalization-provider';
import type { PersonalizationContextData } from '@ecom-next/core/legacy/personalization-provider/types';
import { AppStateProvider, AppState } from '@ecom-next/sitewide/app-state-provider';
import { Brands, StitchStyleProvider } from '@ecom-next/core/react-stitch';

import { CardOfferLayout, CardOfferLayoutProps, AppStateExtraProp, PRE_SCREEN_STATUSES } from './CardOfferLayout';

const mockLoyaltyEnrollmentHook = {
  viewPrescreenOffer: jest.fn(),
  acceptPrescreenOffer: jest.fn(),
};

jest.mock('@ecom-next/core/legacy/hooks', () => ({
  useLoyaltyCreditCardEnrollmentService: () => mockLoyaltyEnrollmentHook,
}));

describe('<CardOfferLayout />', () => {
  let componentProps: CardOfferLayoutProps;
  const personalizationData = {
    isLoading: true,
    isEmpty: true,
    isError: false,
    firstName: 'Test',
    preScreenResponse: {
      preScreenId: '1234',
      preScreenStatus: 'PRSC_APPROVED',
    },
    shoppingBag: {
      totalItemCount: 0,
    },
  } as unknown as PersonalizationContextData;
  const personalizationDataWithoutPreScreen = {
    isLoading: true,
    isEmpty: true,
    isError: false,
    firstName: 'Test',
    shoppingBag: {
      totalItemCount: 0,
    },
  } as unknown as PersonalizationContextData;
  const appState: AppStateExtraProp = {
    brandName: Brands.Athleta,
    brandCode: 1,
    criticalCss: [],
    criticalResources: [],
    locale: 'en_US',
    market: 'us',
    pageType: 'sitewide',
    datalayer: {
      add: () => {},
      build: () => Promise.resolve({ business_unit_id: 69 }),
      builderNames: () => 'string',
      isTealiumReady: () => null,
      link: () => {},
      view: () => Promise.resolve(),
    } as any,
  };

  const renderComponent = (props: CardOfferLayoutProps, personalizationData: PersonalizationContextData) => {
    render(
      <PersonalizationContext.Provider
        // @ts-ignore
        value={personalizationData}
      >
        <BreakpointProvider>
          <AppStateProvider value={appState}>
            <StitchStyleProvider brand={Brands.Athleta}>
              <CardOfferLayout {...props} />
            </StitchStyleProvider>
          </AppStateProvider>
        </BreakpointProvider>
      </PersonalizationContext.Provider>
    );
  };

  beforeEach(() => {
    jest.clearAllMocks();

    componentProps = {
      currencySymbol: '$',
      data: {
        showSavingsDetails: true,
        offerDescriptionEmphasis: 'Save 20 percent',
        offerDescription: ' of your of purchase',
        applyLinkURL: 'https://www.desktop.com',
        applyLinkURLMobile: 'https://www.mobile.com',
        cardImg: 'https://www.gap.com/cardSvgImg.svg',
        legalDetails: 'some legal details',
      },
      datalayerValues: {
        channel: 'gp:buy',
        page_name: 'gp:buy:ShoppingBag',
        page_type: 'ShoppingBag',
        market: 'us',
        recognition_status: 'recognized',
      },
      discountAmount: '20.00',
      mktingType: '',
      onClickCsc: undefined,
      onClickOca: undefined,
      shoppingBagTotal: '200.00',
      totalAmount: '180.00',
      prescreenApiHost: 'http://test.com/',
    };
  });

  describe('Prescreen Offer Hook', () => {
    describe('when layout is seen by the user', () => {
      it('should not fire viewPrescreenOffer if there is no prescreen', () => {
        const spyMockLogger = jest.spyOn(mockLoyaltyEnrollmentHook, 'viewPrescreenOffer');

        renderComponent(componentProps, personalizationDataWithoutPreScreen);

        expect(spyMockLogger).not.toHaveBeenCalled();
      });

      it('should not fire acceptPrescreenOffer if there is no prescreen', async () => {
        const spyMockLogger = jest.spyOn(mockLoyaltyEnrollmentHook, 'acceptPrescreenOffer');

        renderComponent(componentProps, personalizationDataWithoutPreScreen);
        const composablebutton = screen.getByText('apply now');

        await act(async () => {
          fireEvent.click(composablebutton);
        });

        expect(spyMockLogger).not.toHaveBeenCalled();
      });

      const preScreenStatusKeys = Object.keys(PRE_SCREEN_STATUSES);

      preScreenStatusKeys.forEach(preScreenStatus => {
        it(`should call viewPrescreenOffer when the preScreenStatus is ${preScreenStatus}`, () => {
          const spyMockLogger = jest.spyOn(mockLoyaltyEnrollmentHook, 'viewPrescreenOffer');

          const pDataWithPrscStatus = {
            ...personalizationData,
            preScreenResponse: {
              ...personalizationData.preScreenResponse,
              preScreenStatus,
            },
          };

          renderComponent(componentProps, pDataWithPrscStatus);

          expect(spyMockLogger).toHaveBeenCalled();
        });
      });

      preScreenStatusKeys.forEach(preScreenStatus => {
        it(`should call acceptPrescreenOffer when apply-button is clicked and there is prescreen is ${preScreenStatus}`, async () => {
          const spyMockLogger = jest.spyOn(mockLoyaltyEnrollmentHook, 'acceptPrescreenOffer');

          const pDataWithPrscStatus = {
            ...personalizationData,
            preScreenResponse: {
              ...personalizationData.preScreenResponse,
              preScreenStatus,
            },
          };

          renderComponent(componentProps, pDataWithPrscStatus);
          const composablebutton = screen.getByText('apply now');

          await act(async () => {
            fireEvent.click(composablebutton);
          });

          expect(spyMockLogger).toHaveBeenCalled();
        });
      });
    });
  });

  describe('Analytics', () => {
    let onClickCscSpy: jest.Mock;
    let onClickOcaSpy: jest.Mock;

    beforeEach(() => {
      onClickCscSpy = jest.fn();
      onClickOcaSpy = jest.fn();
      componentProps.mktingType = '';
      componentProps.onClickCsc = undefined;
      componentProps.onClickOca = undefined;
    });

    it('should fire tealium datalayer link tag on initial component load', () => {
      const spyMockDatalayerAdd = jest.spyOn(appState.datalayer, 'link');

      renderComponent(componentProps, personalizationData);
      expect(spyMockDatalayerAdd).toHaveBeenCalledTimes(1);
    });

    it('should not fire the tealium action when mktingType is not provided', async () => {
      componentProps.onClickCsc = onClickCscSpy;
      componentProps.onClickOca = onClickOcaSpy;

      const { getByText } = render(<CardOfferLayout {...componentProps} />);
      const composablebutton = getByText('apply now');
      await userEvent.click(composablebutton);

      expect(onClickCscSpy).not.toHaveBeenCalled();
      expect(onClickOcaSpy).not.toHaveBeenCalled();
    });

    it('should fire onClickCsc when apply-button is clicked if mktingType is cardPromo', async () => {
      componentProps.mktingType = 'cardPromo';
      componentProps.onClickCsc = onClickCscSpy;
      componentProps.onClickOca = onClickOcaSpy;

      const { getByText } = render(<CardOfferLayout {...componentProps} />);
      const composablebutton = getByText('apply now');

      const expectedArgs = {
        asset_site_code: '',
        card_offer_name: 'generic - savings calc',
        channel: 'gp:buy',
        market: 'us',
        page_name: 'gp:buy:ShoppingBag',
        page_type: 'ShoppingBag',
        recognition_status: 'recognized',
      };

      await userEvent.click(composablebutton);

      expect(onClickCscSpy).toHaveBeenCalledTimes(1);
      expect(onClickCscSpy).toHaveBeenCalledWith(expectedArgs);
    });

    it('should fire onClickOca when apply-button is clicked if mktingType is cbccPreapproved', async () => {
      componentProps.mktingType = 'cbccPreapproved';
      componentProps.onClickCsc = onClickCscSpy;
      componentProps.onClickOca = onClickOcaSpy;

      const { getByText } = render(<CardOfferLayout {...componentProps} />);
      const composablebutton = getByText('apply now');
      await userEvent.click(composablebutton);

      expect(onClickOcaSpy).toHaveBeenCalledTimes(1);
    });
  });

  it('should show layout with Personalization first name when savings details is enabled', () => {
    renderComponent(componentProps, personalizationData);
    expect(screen.getByText(/Hi Test/i)).toBeDefined();
  });

  it('should show layout when savings details is enabled', async () => {
    const { container } = render(<CardOfferLayout {...componentProps} />);
    const promoDescOfferDetails = await screen.findByTestId('promo-desc');
    const promoApplyOfferDetails = await screen.findByTestId('apply-button-wrapper');

    expect(container.querySelector('.card-calc-wrapper')).toBeInTheDocument();
    expect(container.querySelector('.discount-bubble')).toBeInTheDocument();

    expect(within(promoDescOfferDetails).getByText(/Offer Details/i)).toBeDefined();
    expect(() => within(promoApplyOfferDetails).getByText(/Offer Details/i)).toThrow();
  });

  it('should show layout when savings details is disabled', async () => {
    componentProps.data.showSavingsDetails = false;
    const { container } = render(<CardOfferLayout {...componentProps} />);
    const promoDescOfferDetails = await screen.findByTestId('promo-desc');
    const promoApplyOfferDetails = await screen.findByTestId('apply-button-wrapper');

    expect(container.querySelector('.card-calc-wrapper')).not.toBeInTheDocument();
    expect(container.querySelector('.discount-bubble')).not.toBeInTheDocument();

    expect(() => within(promoDescOfferDetails).getByText(/Offer Details/i)).toThrow();
    expect(within(promoApplyOfferDetails).getByText(/Offer Details/i)).toBeDefined();
  });

  it('should show mobile layout when breakpoint is smaller than large', async () => {
    render(<CardOfferLayout {...componentProps} />, {
      breakpoint: SMALL,
    });
    const promoDescOfferDetails = await screen.findByTestId('promo-desc');
    const promoApplyOfferDetails = await screen.findByTestId('apply-button-wrapper');

    expect(() => within(promoDescOfferDetails).getByText(/Offer Details/i)).toThrow();
    expect(within(promoApplyOfferDetails).getByText(/Offer Details/i)).toBeDefined();
  });

  it('should open the desktop URL if the apply button is clicked', async () => {
    render(<CardOfferLayout {...componentProps} />, {
      breakpoint: LARGE,
    });
    const applyButton = await screen.findByTestId('apply-button');

    expect(applyButton).toHaveAttribute('href', 'https://www.desktop.com');
  });

  it('should open the mobile URL if the apply button is clicked', async () => {
    render(<CardOfferLayout {...componentProps} />, {
      breakpoint: SMALL,
    });
    const applyButton = await screen.findByTestId('apply-button');

    expect(applyButton).toHaveAttribute('href', 'https://www.mobile.com');
  });

  it('should open the desktop URL as default if the apply button is clicked', async () => {
    componentProps.data.applyLinkURLMobile = '';
    render(<CardOfferLayout {...componentProps} />, {
      breakpoint: SMALL,
    });
    const applyButton = await screen.findByTestId('apply-button');

    expect(applyButton).toHaveAttribute('href', 'https://www.desktop.com');
  });

  it('should add retUlr query param to the apply link if returnUrl is present', async () => {
    componentProps.returnUrl = 'www.test.com';

    render(<CardOfferLayout {...componentProps} />, {
      breakpoint: SMALL,
    });
    const applyButton = await screen.findByTestId('apply-button');

    expect(applyButton).toHaveAttribute('href', 'https://www.mobile.com/?retUrl=www.test.com');
  });

  it('should not add retUlr query param to the apply link if already present', async () => {
    const propsWithRetUrl = {
      ...componentProps,
      returnUrl: 'www.test.com',
      data: {
        ...componentProps.data,
        applyLinkURLMobile: 'https://www.mobile.com/?retUrl=www.test.com',
      },
    };

    render(<CardOfferLayout {...propsWithRetUrl} />, {
      breakpoint: SMALL,
    });
    const applyButton = await screen.findByTestId('apply-button');
    expect(applyButton).toHaveAttribute('href', 'https://www.mobile.com/?retUrl=www.test.com');
  });

  it("should show submit button with 'apply now' when no custom text is provided", async () => {
    render(<CardOfferLayout {...componentProps} />);
    const applyButton = await screen.findByTestId('apply-button');

    expect(applyButton.textContent).toEqual('apply now');
  });

  it('should show submit button with custom text when provided', async () => {
    componentProps.submitButtonText = 'get started';
    render(<CardOfferLayout {...componentProps} />);
    const applyButton = await screen.findByTestId('apply-button');

    expect(applyButton.textContent).toEqual('get started');
  });
});
