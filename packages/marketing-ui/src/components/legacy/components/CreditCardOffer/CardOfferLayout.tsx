// @ts-nocheck
'use client';
import React, { useContext, ComponentType, useEffect, FC } from 'react';
import { AppContext, AppState } from '@ecom-next/sitewide/app-state-provider';
import { BreakpointContext, LARGE, XLARGE } from '@ecom-next/core/breakpoint-provider';
import { PersonalizationContext } from '@ecom-next/sitewide/personalization-provider';
import classNames from 'classnames';
import { useLoyaltyCreditCardEnrollmentService } from '@ecom-next/core/legacy/hooks';
import { Link } from '@ecom-next/core/migration/link';
import type { PersonalizationContextData } from '@ecom-next/core/legacy/personalization-provider/types';
import { Color, ComposableButton, Variant } from '../ComposableButton/components';
import { OfferDetails } from './OfferDetails';
import { withStyles } from '../../helper';
import './CreditCardOffer.css';
import { Market } from '.';

interface dataProps {
  applyLinkURL: string;
  applyLinkURLMobile: string;
  cardImg: string;
  legalDetails: string;
  offerDescription: string;
  offerDescriptionEmphasis: string;
  showSavingsDetails: boolean;
}
interface DatalayerValues {
  channel: string;
  page_name: string;
  page_type: string;
  market: Market;
  recognition_status: 'unknown' | 'guest' | 'recognized' | 'authenticated';
}

interface OnClickCsc extends DatalayerValues {
  card_offer_name: string;
  asset_site_code: string;
}

export type CardOfferLayoutProps = {
  currencySymbol: string;
  data: dataProps;
  datalayerValues: DatalayerValues;
  discountAmount: string;
  mktingType: string;
  shoppingBagTotal: string;
  totalAmount: string;
  submitButtonText?: string;
  onClickCsc?: (arg: OnClickCsc) => void;
  onClickOca?: () => void;
  prescreenApiHost?: string;
  returnUrl?: string;
  onOfferRender?: () => void;
  onOfferUnmount?: () => void;
};

// Adding property missing in definition.
export interface AppStateExtraProp extends AppState {
  brandCode: number | string;
}

export const PRE_SCREEN_STATUSES = {
  PRSC_APPROVED: true,
  CUST_PRSC_READ: true,
  CUST_PRSC_ACCEPTED: true,
} as const;

const CardOfferRenderCallback: FC<Pick<CardOfferLayoutProps, 'onOfferUnmount' | 'onOfferRender'>> = ({ onOfferRender, onOfferUnmount }) => {
  useEffect(() => {
    onOfferRender?.();
    return () => onOfferUnmount?.();
  }, [onOfferRender, onOfferUnmount]);
  return null;
};

export const CardOfferLayout = ({
  currencySymbol,
  data: { applyLinkURL, applyLinkURLMobile, cardImg, legalDetails, offerDescription, offerDescriptionEmphasis, showSavingsDetails },
  datalayerValues,
  discountAmount,
  mktingType,
  onClickCsc,
  onClickOca,
  shoppingBagTotal,
  submitButtonText,
  totalAmount,
  prescreenApiHost = '',
  returnUrl,
  onOfferUnmount,
  onOfferRender,
}: CardOfferLayoutProps) => {
  const { viewPrescreenOffer, acceptPrescreenOffer } = useLoyaltyCreditCardEnrollmentService('ECOM', prescreenApiHost);
  const { brandName, datalayer, brandCode } = useContext(AppContext) as AppStateExtraProp;
  const { smallerThan, greaterOrEqualTo } = useContext(BreakpointContext);
  const breakpointClasses = {
    wrap: smallerThan(XLARGE) && greaterOrEqualTo(LARGE) && showSavingsDetails,
    mobile: smallerThan(LARGE),
    hideSavingsDetails: !showSavingsDetails,
  };
  const { firstName, preScreenResponse } = useContext(PersonalizationContext) as unknown as PersonalizationContextData;

  const isPreScreenApproved = !!PRE_SCREEN_STATUSES[preScreenResponse?.preScreenStatus as keyof typeof PRE_SCREEN_STATUSES];

  let applyLink = smallerThan(LARGE) ? applyLinkURLMobile || applyLinkURL : applyLinkURL;

  let siteCode = '';

  if (returnUrl) {
    const url = new URL(applyLink);
    siteCode = url.searchParams.get('sitecode') || '';

    if (!url.searchParams.get('retUrl')) url.searchParams.append('retUrl', returnUrl);

    applyLink = decodeURIComponent(url.toString());
  }

  const cardOfferDlValues = {
    card_offer_name: isPreScreenApproved ? 'pre-approved - savings calc' : 'generic - savings calc',
    asset_site_code: siteCode,
    ...datalayerValues,
  };

  const onApplyButtonClick = () => {
    if (isPreScreenApproved) {
      acceptPrescreenOffer();
    }

    if (mktingType === 'cardPromo' && onClickCsc) {
      return onClickCsc(cardOfferDlValues);
    }
    if ((mktingType === 'cbccPreapproved' || mktingType === 'plccPreapproved') && onClickOca) return onClickOca();
    return null;
  };

  if (isPreScreenApproved) {
    viewPrescreenOffer();
  }

  useEffect(() => {
    datalayer.link({
      ...cardOfferDlValues,
      event_name: 'csc-offer-view',
      business_unit_id: brandCode,
    });
  }, []);

  return (
    <section aria-label={`${brandName} Credit Card Offer`} className={classNames('credit-card-offer', 'sds-cb_font--primary', breakpointClasses)}>
      <div className={classNames('credit-card-offer__left-wrapper', breakpointClasses)}>
        <div className='card-image-wrapper'>
          <div className={classNames('card-image-wrapper__image-container', breakpointClasses)}>
            <img alt={`${brandName} credit card`} className={classNames(breakpointClasses)} height='35px' src={cardImg} width='69px' />
            {showSavingsDetails && (
              <>
                <span className='sds_visually-hidden'>Savings with card</span>
                <p className='discount-bubble'>${discountAmount}</p>
              </>
            )}
          </div>
        </div>
        <div className={classNames('promo-desc-wrapper', breakpointClasses)} data-testid='promo-desc'>
          <p>
            {firstName && (
              <>
                Hi {firstName},<br />
              </>
            )}
            <span>{`${offerDescriptionEmphasis} `}</span>
            {offerDescription}
          </p>
          {showSavingsDetails && !breakpointClasses.mobile && <OfferDetails legalDetails={legalDetails} />}
        </div>
      </div>
      <div className={classNames('credit-card-offer__right-wrapper', breakpointClasses)}>
        {showSavingsDetails && (
          <dl className={classNames('card-calc-wrapper', breakpointClasses)}>
            <dt>
              <span>Total</span>
            </dt>
            <dd>
              <span>{`${currencySymbol}${shoppingBagTotal}`}</span>
            </dd>
            <dt className='card-calc-wrapper__savings-line'>
              <span>Savings with Card</span>
            </dt>
            <dd>
              <span className='sds_visually-hidden'>{`you save ${currencySymbol}${discountAmount}`}</span>
              <span aria-hidden className='card-calc-wrapper__savings-line'>{`-${currencySymbol}${discountAmount}`}</span>
            </dd>
            <hr className='card-calc-wrapper__line-break' />
            <dt className='card-calc-wrapper__total-line'>
              <span aria-hidden>Total with Card</span>
            </dt>
            <dd className='card-calc-wrapper__total-line'>
              <span className='sds_visually-hidden'>Total with Card, before tax</span>
              <span>{`${currencySymbol}${totalAmount}`}</span>
            </dd>
            <dt aria-hidden className='card-calc-wrapper__before-tax'>
              Before tax
            </dt>
          </dl>
        )}
        <div className={classNames('apply-button-wrapper', breakpointClasses)} data-testid='apply-button-wrapper'>
          <ComposableButton
            as={Link}
            className={classNames('apply-button', breakpointClasses)}
            color={Color.black}
            crossBrand
            data-testid='apply-button'
            href={applyLink}
            onClick={onApplyButtonClick}
            variant={Variant.outline}
          >
            {submitButtonText}
          </ComposableButton>
          {(!showSavingsDetails || breakpointClasses.mobile) && <OfferDetails legalDetails={legalDetails} />}
          <CardOfferRenderCallback onOfferRender={onOfferRender} onOfferUnmount={onOfferUnmount} />
        </div>
      </div>
    </section>
  );
};

const defaultProps = {
  currencySymbol: '$',
  data: {
    applyLinkURL: '',
    cardImg: '',
    legalDetails: '',
    offerDescription: '',
    offerDescriptionEmphasis: '',
    showSavingsDetails: true,
  },
  discountAmount: '0.00',
  mktingType: null,
  shoppingBagTotal: '0.00',
  submitButtonText: 'apply now',
  totalAmount: '0.00',
  prescreenApiHost: undefined,
};

CardOfferLayout.defaultProps = defaultProps;

export default CardOfferLayout;
