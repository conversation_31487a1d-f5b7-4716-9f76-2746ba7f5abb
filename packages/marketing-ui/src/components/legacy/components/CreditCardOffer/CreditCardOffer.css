.credit-card-offer {
  background-color: #f2f2f2;
  display: flex;
  margin-bottom: 1.125rem;
  max-width: 1400px;
  width: 100%;
  padding: 0.5rem;

  .sr-only {
    display: none;
  }

  .offerDetailsButton {
    color: #0466ca;
    font-size: 0.75rem;
    padding-left: 0;
    border: none;
    font-family: 'Source Sans Pro', Helvetica, Arial, Roboto, sans-serif;
  }

  dl {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    width: 100%;
  }

  dt {
    width: calc(100% - 4.5rem);
  }

  dd {
    display: flex;
    margin: 0;
  }

  dd + dd {
    width: 100%;
    padding-top: 0;
  }

  &.mobile {
    flex-direction: column;
  }

  &__left-wrapper {
    display: flex;
    width: 100%;

    &.hideSavingsDetails {
      width: 100%;
    }

    &.wrap {
      flex-direction: column;
    }

    &.mobile {
      padding-top: 0;
    }

    .card-image-wrapper {
      &__image-container {
        width: 70px;
        position: relative;

        &.hideSavingsDetails {
          width: 57px;
        }

        img {
          box-sizing: border-box;
          padding-left: 13px;

          &.hideSavingsDetails {
            padding-left: 0;
          }

          &.mobile {
            margin-bottom: 0.5rem;
          }
        }

        p {
          background-color: #333;
          border-radius: 13px;
          color: #fff;
          position: absolute;
          top: 26px;
          font-size: 0.8125rem;
          padding: 0 0.25rem;
        }
      }
    }

    .promo-desc-wrapper {
      margin: 0 0.625rem;

      &.wrap {
        padding-top: 0.625rem;
        margin-left: 0;
      }

      p {
        margin-bottom: 0.625rem;
        font-size: 0.8125rem;

        span {
          font-weight: bold;
        }
      }

      a {
        cursor: pointer;
        color: #0466ca;
        font-size: 0.75rem;
      }
    }
  }

  &__right-wrapper {
    display: flex;
    width: 100%;

    &.hideSavingsDetails {
      width: 89px;
    }

    &.mobile {
      width: 100%;
      flex-direction: column;
    }

    &.wrap {
      flex-direction: column;
    }

    .card-calc-wrapper {
      border-left: 1px solid #ccc;
      padding: 0 0 0 0.625rem;
      width: 100%;
      font-size: 0.8125rem;

      &.mobile {
        border-left: none;
        border-top: 1px solid #ccc;
        padding: 0.5rem 0;
      }

      &.wrap {
        width: unset;
      }

      li {
        display: flex;
        justify-content: space-between;
      }

      &__savings-line {
        color: #d00000;
        padding: 0.25rem 0 0.5rem 0;
      }

      &__total-line {
        font-weight: bold;
        margin: 0.5rem 0 0.15rem 0;
      }

      &__line-break {
        height: 1px;
      }

      &__before-tax {
        font-size: 0.75rem;
        color: #666;
      }
    }

    .apply-button-wrapper {
      padding: 2.9rem 0 0.625rem 0.625rem;

      &.hideSavingsDetails {
        padding: 0 0 0 0;
      }

      &.mobile {
        align-items: flex-end;
        display: flex;
        flex-direction: row-reverse;
        justify-content: space-between;
        padding: 0;
        width: 100%;
      }

      &.wrap {
        border-left: 1px solid #ccc;
        padding: 1.125rem 0 0 0.625rem;
      }

      a {
        color: #0466ca;
        cursor: pointer;
        font-size: 0.75rem;
      }

      .apply-button {
        color: #000;
        margin-bottom: 0.2rem;
        width: 100%;
        font-size: 0.625rem;
        border-color: #ccc;
        border-width: 1px;
        background-color: #ffffff;

        &.mobile {
          width: 75px;
        }
      }
    }
  }
}
