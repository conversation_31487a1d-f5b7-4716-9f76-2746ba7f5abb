// @ts-nocheck
import React from 'react';
import { fireEvent, render, act } from 'test-utils';
import { OfferDetails } from './OfferDetails';

describe('<OfferDetails />', () => {
  it('should open a model when clicked', async () => {
    const screen = render(<OfferDetails legalDetails='Some legal stuff' />);

    const closeModal = await screen.queryByTestId('modal');
    expect(closeModal).toBeNull();

    const offerDetailsLink = await screen.findByTestId('offer-details-button');
    await act(async () => {
      fireEvent.click(offerDetailsLink);
    });

    const openModal = await screen.queryByTestId('modal');
    expect(openModal).toBeInTheDocument();
  });

  it('should render a paragraph if passed a string', async () => {
    const screen = render(<OfferDetails legalDetails='Some legal stuff' />);

    const offerDetailsLink = await screen.findByTestId('offer-details-button');
    await act(async () => {
      fireEvent.click(offerDetailsLink);
    });

    const paragraph = await screen.findByTestId('legal-terms');

    expect(paragraph.textContent).toEqual('Some legal stuff');
    expect(await screen.queryByTestId('iframe')).toBeNull();
  });

  it('should render a page if legalDetails is an absolute link', async () => {
    const screen = render(<OfferDetails legalDetails='http://www.gap.com' />);

    const offerDetailsLink = await screen.findByTestId('offer-details-button');
    await act(async () => {
      fireEvent.click(offerDetailsLink);
    });

    const iFrame = await screen.findByTestId('iframe');
    expect(iFrame).toHaveAttribute('src', 'http://www.gap.com');
    expect(await screen.queryByTestId('legal-terms')).toBeNull();
  });

  it('should render a page if legalDetails is a secure absolute link', async () => {
    const screen = render(<OfferDetails legalDetails='https://www.gap.com' />);

    const offerDetailsLink = await screen.findByTestId('offer-details-button');
    await act(async () => {
      fireEvent.click(offerDetailsLink);
    });

    const iFrame = await screen.findByTestId('iframe');
    expect(iFrame).toHaveAttribute('src', 'https://www.gap.com');
    expect(await screen.queryByTestId('legal-terms')).toBeNull();
  });

  it('should render a page if legalDetails is a relative link', async () => {
    const screen = await render(<OfferDetails legalDetails='/Asset_Archive' />);

    const offerDetailsLink = await screen.findByTestId('offer-details-button');
    await act(async () => {
      fireEvent.click(offerDetailsLink);
    });

    const iFrame = await screen.findByTestId('iframe');
    expect(iFrame).toHaveAttribute('src', '/Asset_Archive');
    expect(await screen.queryByTestId('legal-terms')).toBeNull();
  });
});
