// @ts-nocheck
'use client';
import React, { useState } from 'react';
import { Modal } from '@ecom-next/core/legacy/modal';

const isLinkRegEx = /^((https?)|(\/))/;
const isLink = (string: string) => isLinkRegEx.test(string);

type OfferDetailsProps = {
  legalDetails: string;
};

export const OfferDetails = ({ legalDetails }: OfferDetailsProps) => {
  const [isOpen, setIsOpen] = useState(false);
  const toggleModal = () => setIsOpen(!isOpen);

  return (
    <>
      <a className='offerDetailsButton' data-testid='offer-details-button' onClick={toggleModal} role='button' tabIndex={0}>
        *Offer Details
      </a>
      <Modal closeButtonAriaLabel='close offer details' isOpen={isOpen} onClose={toggleModal} title='Offer Details'>
        <div className='sds_pd' data-testid='modal'>
          {isLink(legalDetails) ? (
            <iframe className='mkt-svg-overlay__iframe' data-testid='iframe' src={legalDetails} title='Offer Details' />
          ) : (
            <p className='promo-legal-terms sds_font-size--13' data-testid='legal-terms'>
              {legalDetails}
            </p>
          )}
        </div>
      </Modal>
    </>
  );
};

const defaultProps = {
  legalDetails: '',
};

OfferDetails.defaultProps = defaultProps;
