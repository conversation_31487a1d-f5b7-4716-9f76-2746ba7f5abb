// @ts-nocheck
'use client';
import { getCookie, removePipes } from '@ecom-next/core/legacy/utility';
import { OIDCHeaders } from '.';

export const getCardSavings = async (headers: OIDCHeaders, shoppingBagTotal: number, prescreenApiHost: string) => {
  const shopperId = removePipes(getCookie('cam') || getCookie('unknownShopperId'));
  const locale = removePipes(getCookie('locale')) || 'en_US';

  const CARD_SAVINGS_URL = `${prescreenApiHost}/commerce/shopping-bags/${shopperId}/card-savings?locale=${locale}&subTotal=${shoppingBagTotal}`;
  try {
    const response = await fetch(CARD_SAVINGS_URL, {
      method: 'GET',
      credentials: 'include',
      // Bag services require market header in uppercase
      headers: { ...headers, market: headers.market.toUpperCase() },
    });

    if (response && response.ok) {
      const data = await response.json();

      return {
        ...data,
        hasError: false,
      };
    }

    throw new Error(`Response Status: ${response.status} ${response.statusText}`);
  } catch (error) {
    console.error('fetch data error', error);
    return {
      hasError: true,
    };
  }
};
