// @ts-nocheck
'use client';
type PreviewTypeValues = 'WIP' | 'APP' | 'ECOM';

export const getContentType = (host: string): PreviewTypeValues => {
  const wipTypeRegex = /wip(\.|-)(px\.)?(gidapps|gidfsapps|gaptecholapps)?/;
  const appTypeRegex = /app(\.|-)(px\.)?(gidapps|gidfsapps|gaptecholapps)?/;

  if (host.match(wipTypeRegex)) {
    return 'WIP';
  }

  if (host.match(appTypeRegex)) {
    return 'APP';
  }

  return 'ECOM';
};
