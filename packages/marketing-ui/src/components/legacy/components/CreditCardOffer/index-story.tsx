// @ts-nocheck
'use client';
import React, { useRef } from 'react';
import { Meta, StoryFn } from '@storybook/react';
import { PersonalizationContext } from '@ecom-next/sitewide/personalization-provider';
import { Key, AnalyticsEvents } from '@ecom-next/core/legacy/analytics';
import { data } from './__fixtures__';
import README from './README.mdx';
// @CreditCardOffers is the same version of the default CreditCardOffer that is wrapped using withAnalytics()
// @ts-ignore
import CreditCardOffers from '.';

export interface CreditCardOfferProps {
  brandName: string;
  currencySymbol: string;
  data: { [key: string]: Key | boolean | AnalyticsEvents<'data'> };
  itemsTotalForBrand: number;
  pageType: string;
  shoppingBagTotal: number;
  submitButtonText: string;
  onOfferRender?: () => void;
  onOfferUnmount?: () => void;
}

const personalizationContextData = {
  firstName: 'Test',
  isEmpty: false,
  isError: false,
  isLoading: false,
  shoppingBag: {
    totalItemCount: 0,
  },
};

const CreditCardOffer = CreditCardOffers as unknown as React.ComponentType<CreditCardOfferProps>;

export default {
  title: 'Common/JSON Components (Marketing)/CreditCardOffer',
  parameters: {
    docs: {
      page: README,
    },
    eyes: { include: false },
  },
  tags: ['exclude', 'flaky-test'],
} as Meta<typeof CreditCardOffer>;

const CreditCardOfferTemplate: StoryFn<typeof CreditCardOffer> = args => {
  const divRef = useRef<HTMLDivElement>(null);
  return (
    <PersonalizationContext.Provider
      // @ts-ignore
      value={personalizationContextData}
    >
      <div ref={divRef} css={{ margin: '1em', maxWidth: '573px' }}>
        <CreditCardOffer
          {...args}
          onOfferRender={() => {
            const rect = divRef.current?.getClientRects()?.[0];
            console.log('Card Offer Rendered', rect); // eslint-disable-line no-console
          }}
          onOfferUnmount={() => {
            console.log('Card Offer Unmounted'); // eslint-disable-line no-console
          }}
        />
      </div>
    </PersonalizationContext.Provider>
  );
};

export const Default = CreditCardOfferTemplate.bind({});
Default.args = {
  ...data,
};
