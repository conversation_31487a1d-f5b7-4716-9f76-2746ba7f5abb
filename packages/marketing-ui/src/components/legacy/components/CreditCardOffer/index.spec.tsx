// @ts-nocheck
import React from 'react';
import { act, render, screen, waitFor } from 'test-utils';
import fetchMock, { MockCall } from 'fetch-mock';
import { AnalyticsEvents } from '@ecom-next/core/legacy/analytics';
import CreditCardOffer, { PureCreditCardOffer as UnwrappedCreditCardOffer, CreditCardOfferProps } from '.';
import { getCardSavings } from './getCardSavings';

// TODO: Please fix the tests that are failing due to some errurs such as undefined URL
describe.skip('<CreditCardOffer />', () => {
  let componentProps: CreditCardOfferProps & {
    brandName: string;
    businessUnitId: string;
    data: {
      analytics: AnalyticsEvents<keyof CreditCardOfferProps>;
      showSavingsDetails: boolean;
    };
  };

  beforeEach(() => {
    componentProps = {
      itemsTotalForBrand: 200,
      shoppingBagTotal: 500,
      brandName: 'gap',
      businessUnitId: '',
      isFeatureEnabled: false,
      prescreenApiHost: 'https://test.api.azus.gaptech.com',
      market: 'US',
      brandAbbr: 'GP',
      data: {
        analytics: '',
        showSavingsDetails: true,
        savingsThreshold: 10.24,
        discountPercentage: 15.99,
      },
      returnUrl: 'https://secure-www.stage.gaptechol.com/shopping-bag',
    };
  });

  it('returns a CardOfferLayout when the savings threshold is met', () => {
    render(<CreditCardOffer {...componentProps} />);

    expect(screen.getByLabelText('gap Credit Card Offer')).toBeInTheDocument();

    expect(screen.getByText('$500.00')).toBeInTheDocument();
    expect(screen.getByText('$31.98')).toBeInTheDocument();
    expect(screen.getByText('$468.02')).toBeInTheDocument();
  });

  it('returns the correct decimal amount', () => {
    componentProps.itemsTotalForBrand = 144;
    componentProps.data.discountPercentage = 20;

    render(<CreditCardOffer {...componentProps} />);

    expect(screen.getByLabelText('gap Credit Card Offer')).toBeInTheDocument();

    expect(screen.getByText('$500.00')).toBeInTheDocument();
    expect(screen.getByText('$28.80')).toBeInTheDocument();
    expect(screen.getByText('$471.20')).toBeInTheDocument();
  });

  it('returns null when the savings threshold is not met', () => {
    componentProps.data.savingsThreshold = 100;
    render(<UnwrappedCreditCardOffer {...componentProps} />);

    expect(screen.queryByLabelText('gap Credit Card Offer')).not.toBeInTheDocument();
  });

  it('returns null if the discount percentage is not set', () => {
    componentProps.data.discountPercentage = 0;
    render(<UnwrappedCreditCardOffer {...componentProps} />);

    expect(screen.queryByLabelText('gap Credit Card Offer')).not.toBeInTheDocument();
  });

  describe('card savings endpoint', () => {
    const responseOk = {
      promoCode: '',
      promoID: '',
      currencyCode: '',
      currentSubTotal: 45.65,
      estimatedSavings: 12.64,
      estimatedSubTotal: 33.01,
    };

    beforeEach(() => fetchMock.get('*', responseOk));
    afterEach(() => fetchMock.reset());

    describe('headers', () => {
      it('should call without previewType in all environments except preview environment', async () => {
        await act(async () => {
          render(<CreditCardOffer {...componentProps} isFeatureEnabled />);
        });

        const [, cardSavingsCall]: MockCall = fetchMock.lastCall()!;

        expect(cardSavingsCall?.headers).not.toHaveProperty('previewType', 'WIP');
      });

      it("should call with previewType 'wip' in preview environment", async () => {
        const prodPreview = 'https://secure.www.wip.prod.gaptecholapps.com';

        await act(async () => {
          render(<CreditCardOffer {...componentProps} isFeatureEnabled prescreenApiHost={prodPreview} />);
        });

        const [, cardSavingsCall]: MockCall = fetchMock.lastCall()!;

        expect(cardSavingsCall?.headers).toHaveProperty('previewType', 'WIP');
      });
    });

    it('should not call card savings endpoint if isFeaturesEnabled is disabled.', async () => {
      await act(async () => {
        render(<CreditCardOffer {...componentProps} />);
      });

      expect(fetchMock.called()).toBeFalsy();
    });

    describe('When isFeaturesEnabled returns TRUE', () => {
      it('should not call card savings endpoint when shoppingBagTotal is less than savingsThreshold', async () => {
        await act(async () => {
          render(<CreditCardOffer {...componentProps} isFeatureEnabled shoppingBagTotal={5} />);
        });

        expect(fetchMock.called()).toBeFalsy();
      });

      it('should call card savings endpoint', async () => {
        const expectedUrl = '/card-savings?locale=';

        await act(async () => {
          render(<CreditCardOffer {...componentProps} isFeatureEnabled />);
        });

        expect(fetchMock.lastUrl()).toContain(expectedUrl);
      });

      it('should display cardSavings component if discount amount is higher than savings threshold.', async () => {
        await getCardSavings(
          {
            'Content-Type': 'application/json',
            'X-Gap-ApiMode': 'leapfrog',
            market: 'us',
            brand: 'GP',
            channel: 'WEB',
            clientId: 'checkout-xapi',
          },
          24345545322423,
          componentProps.prescreenApiHost
        ).then();

        render(<CreditCardOffer {...componentProps} isFeatureEnabled />);
        const { innerHTML } = await screen.findByText(/Savings with card/);
        expect(innerHTML).toContain('Savings with card');
      });

      it('should not display cardSavings component if API call fails.', async () => {
        fetchMock.getOnce(
          '*',
          { throws: 'API Error' },
          {
            overwriteRoutes: true,
          }
        );

        await act(async () => {
          render(<CreditCardOffer {...componentProps} isFeatureEnabled />);
        });

        await waitFor(() => {
          const savingsComponent = screen.queryByText(/Savings with card/);
          expect(savingsComponent).toBeNull();
        });
      });
    });
  });

  describe('mount/unmount callbacks', () => {
    it('should invoke the onOfferRender and onOfferUnmount at the respective times', () => {
      const renderSpy = jest.fn();
      const unmountSpy = jest.fn();
      const { unmount } = render(<CreditCardOffer {...componentProps} onOfferRender={renderSpy} onOfferUnmount={unmountSpy} />);
      expect(renderSpy).toHaveBeenCalled();
      expect(unmountSpy).not.toHaveBeenCalled();
      unmount();
      expect(unmountSpy).toHaveBeenCalled();
    });
  });
});
