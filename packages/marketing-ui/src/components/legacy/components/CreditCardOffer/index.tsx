// @ts-nocheck
'use client';
import React, { useState, useEffect } from 'react';
import { withAnalytics } from '@ecom-next/core/legacy/analytics';
import CardOfferLayout, { CardOfferLayoutProps } from './CardOfferLayout';
import { getCardSavings } from './getCardSavings';
import { getContentType } from './getContentType';

type Data = {
  savingsThreshold: number;
  discountPercentage: number;
};

export interface CreditCardOfferProps extends Pick<CardOfferLayoutProps, 'onOfferRender' | 'onOfferUnmount'> {
  itemsTotalForBrand: number;
  data: Data;
  shoppingBagTotal: number;
  isFeatureEnabled: boolean;
  prescreenApiHost: string;
  market: Market;
  brandAbbr: ValidBrand;
  returnUrl: string;
}

export type ValidBrand = 'GPFS' | 'BRFS' | 'GP' | 'BR' | 'ON' | 'AT';

export type MarketOptions = 'US' | 'CA';
export type Market = MarketOptions | Lowercase<MarketOptions>;
export type ClientId = 'bag-ui-xapi' | 'checkout-xapi';

export type OIDCHeaders = {
  'Content-Type': string;
  'X-Gap-ApiMode': string;
  market: Market;
  brand: ValidBrand;
  channel: string;
  clientId: ClientId;
};

type OIDCHeadersWithPreviewType = OIDCHeaders & { previewType: 'WIP' };

export type CardSavingsInfoType = typeof defaultCardSavings;

const defaultCardSavings = {
  promoCode: '',
  promoID: '',
  currencyCode: '',
  currentSubTotal: 0,
  estimatedSavings: 0,
  estimatedSubTotal: 0,
  failedCall: false,
};

const CreditCardOffer = ({
  itemsTotalForBrand,
  shoppingBagTotal,
  data,
  prescreenApiHost,
  isFeatureEnabled = false,
  market,
  brandAbbr,
  returnUrl,
  ...props
}: CreditCardOfferProps) => {
  const [cardSavingsInfo, setCardSavingsInfo] = useState<CardSavingsInfoType>(defaultCardSavings);

  const { savingsThreshold, discountPercentage } = data;

  const discountAmount: number = isFeatureEnabled ? cardSavingsInfo.estimatedSavings : itemsTotalForBrand * (discountPercentage / 100);

  const totalAmount: number = isFeatureEnabled ? cardSavingsInfo.estimatedSubTotal : shoppingBagTotal - discountAmount;

  const isBagTotalGreaterThanThreshold = shoppingBagTotal > savingsThreshold;

  const previewType = getContentType(prescreenApiHost);

  let clientName = '';
  if (returnUrl) {
    const clientReturnUrl = new URL(returnUrl);
    clientName = clientReturnUrl.pathname.split('/')[1];
  }

  const clientMap = {
    'shopping-bag': 'bag-ui-xapi',
    checkout: 'checkout-xapi',
  } as const satisfies Record<string, ClientId>;

  const clientId = clientName && clientMap[clientName] ? clientMap[clientName] : '';

  useEffect(() => {
    let headers: OIDCHeaders = {
      'Content-Type': 'application/json',
      'X-Gap-ApiMode': 'leapfrog',
      market,
      brand: brandAbbr,
      channel: 'WEB',
      clientId,
    };

    if (previewType === 'WIP') {
      headers = { ...headers, previewType } as OIDCHeadersWithPreviewType;
    }

    const fetchCardSavings = async () => {
      try {
        const response = await getCardSavings(headers, shoppingBagTotal, prescreenApiHost);
        setCardSavingsInfo({ ...response, failedCall: false });
      } catch {
        setCardSavingsInfo({ ...defaultCardSavings, failedCall: true });
      }
    };

    if (isFeatureEnabled && isBagTotalGreaterThanThreshold) {
      fetchCardSavings();
    }
  }, [shoppingBagTotal, isFeatureEnabled, prescreenApiHost, brandAbbr, market, isBagTotalGreaterThanThreshold, previewType]);

  if (!isBagTotalGreaterThanThreshold || !discountAmount || discountAmount < savingsThreshold || cardSavingsInfo.failedCall) {
    return null;
  }

  const marketingProps = {
    shoppingBagTotal: shoppingBagTotal.toFixed(2),
    discountAmount: discountAmount.toFixed(2),
    totalAmount: totalAmount.toFixed(2),
    prescreenApiHost,
    market,
    brandAbbr,
    data,
    returnUrl,
    ...props,
  };

  return <CardOfferLayout {...marketingProps} />;
};

const defaultProps = {
  itemsTotalForBrand: 0.0,
  shoppingBagTotal: 0.0,
  data: {
    savingsThreshold: 0.0,
    discountPercentage: 0.0,
  },
};

CreditCardOffer.defaultProps = defaultProps;

export default withAnalytics(CreditCardOffer);

export { CreditCardOffer as PureCreditCardOffer };
