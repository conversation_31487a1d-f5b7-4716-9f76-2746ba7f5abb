// @ts-nocheck
'use client';
import React from 'react';
import { DecoratorFn } from '@storybook/react';
import README from './README.mdx';

const DeprecationNotice = () => (
  <p
    css={{
      backgroundColor: '#f2dede',
      border: '1px #eed3d7 solid',
      borderRadius: '4px',
      color: '#b94a48',
      marginBottom: '2rem',
      padding: '10px',
      textAlign: 'center',
    }}
  >
    Attention! This component has been deprecated! Please use{' '}
    <a
      css={{ textDecoration: 'underline' }}
      href='https://marketing-ui-main.apps.cfplatform.dev.azeus.gaptech.com/?path=/story/common-json-components-marketing-detailslinkwithprefix--default&brand=gap'
    >
      DetailsLinkWithPrefix
    </a>
    .
  </p>
);

const withDeprecation: DecoratorFn = (story, context) => (
  <div css={{ margin: '1em' }}>
    <DeprecationNotice />
    {story(context)}
  </div>
);

export default {
  title: 'Common/JSON Components (Marketing)/DetailsLink',
  decorators: [withDeprecation],
  parameters: {
    docs: {
      page: README,
    },
    eyes: { include: false },
  },
  tags: [],
};

export const Default = () => <div>See Docs tab for more info.</div>;
