# DetailsLinkWithPrefix

_Most of our content-types are using `DetailsButton` instead, please consider it before using this._

An example of how do use the `DetailsButton` can be found [here](https://marketing-ui-main.apps.cfplatform.dev.azeus.gaptech.com/?path=%2Fstory%2Fcommon-json-components-marketing-cms-utilities-detailsbutton--default).

## Purpose

Component provides a link to open a modal window along with optional prefix text that is not linked.

## How to use

Can be configured through JSON as a subcomponent or standalone component.

## Note

If the text for the Details link contains an \* it will automatically be underlined

## Example

```json
{
  "text": "*Details",
  "detailsLinkUrl": "url",
  "closeButtonAriaLabel": "close aria label",
  "prefixStyles": {
    "fontSize": "10px",
    "fontWeight": "normal",
    "padding": "0",
    "minHeight": "0",
    "border": "none",
    "textTransform": "none"
  },
  "linkStyles": {
    "fontSize": "10px",
    "right": "5%"
  },
  "wrapperStyles": {
    "position": "absolute",
    "right": "2%",
    "bottom": "5%"
  },
  "modalSize": "standard",
  "title": "Modal Title"
}
```

`DetailsLinkWithPrefix` is used at [`LayeredContentModule`](https://marketing-ui-main.apps.cfplatform.dev.azeus.gaptech.com/?path=%2Fstory%2Fcommon-json-components-marketing-layeredcontentmodule--with-details-link).
