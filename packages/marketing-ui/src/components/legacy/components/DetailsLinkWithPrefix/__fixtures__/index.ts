// @ts-nocheck
import { DetailsLinkWithPrefixProps } from '..';

export const baseProps: DetailsLinkWithPrefixProps = {
  prefix: 'exclusions apply.',
  text: 'Details',
  detailsLinkUrl: 'url',
  closeButtonAriaLabel: 'close aria label',
  prefixStyles: {
    fontSize: '10px',
    fontWeight: 'normal',
    padding: '0',
    minHeight: '0',
    border: 'none',
    textTransform: 'none',
  },
  linkStyles: {
    fontSize: '10px',
    right: '5%',
    '@media only screen and (max-width: 1024px)': {},
  },
  modalSize: 'standard',
  title: 'Modal Title',
};
