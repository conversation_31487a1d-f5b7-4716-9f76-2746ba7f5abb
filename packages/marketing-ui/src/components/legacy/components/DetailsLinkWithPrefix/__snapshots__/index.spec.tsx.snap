// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`<Marketing UI/DetailsLinkWithPrefix /> snapshots for large breakpoint renders default state correctly Athleta 1`] = `
<DocumentFragment>
  <div
    style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      class="emotion-0"
    >
      <span
        class="emotion-0"
      >
        exclusions apply.
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`<Marketing UI/DetailsLinkWithPrefix /> snapshots for large breakpoint renders default state correctly Athleta in crossBrand 1`] = `
<DocumentFragment>
  <div
    style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      class="emotion-0"
    >
      <span
        class="emotion-0"
      >
        exclusions apply.
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`<Marketing UI/DetailsLinkWithPrefix /> snapshots for large breakpoint renders default state correctly BananaRepublic 1`] = `
<DocumentFragment>
  <div
    style="font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;"
  >
    <div
      class="emotion-0"
    >
      <span
        class="emotion-0"
      >
        exclusions apply.
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`<Marketing UI/DetailsLinkWithPrefix /> snapshots for large breakpoint renders default state correctly BananaRepublic in crossBrand 1`] = `
<DocumentFragment>
  <div
    style="font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;"
  >
    <div
      class="emotion-0"
    >
      <span
        class="emotion-0"
      >
        exclusions apply.
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`<Marketing UI/DetailsLinkWithPrefix /> snapshots for large breakpoint renders default state correctly BananaRepublicFactoryStore 1`] = `
<DocumentFragment>
  <div
    style="font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;"
  >
    <div
      class="emotion-0"
    >
      <span
        class="emotion-0"
      >
        exclusions apply.
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`<Marketing UI/DetailsLinkWithPrefix /> snapshots for large breakpoint renders default state correctly BananaRepublicFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  <div
    style="font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;"
  >
    <div
      class="emotion-0"
    >
      <span
        class="emotion-0"
      >
        exclusions apply.
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`<Marketing UI/DetailsLinkWithPrefix /> snapshots for large breakpoint renders default state correctly Gap 1`] = `
<DocumentFragment>
  <div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      class="emotion-0"
    >
      <span
        class="emotion-0"
      >
        exclusions apply.
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`<Marketing UI/DetailsLinkWithPrefix /> snapshots for large breakpoint renders default state correctly Gap in crossBrand 1`] = `
<DocumentFragment>
  <div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      class="emotion-0"
    >
      <span
        class="emotion-0"
      >
        exclusions apply.
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`<Marketing UI/DetailsLinkWithPrefix /> snapshots for large breakpoint renders default state correctly GapFactoryStore 1`] = `
<DocumentFragment>
  <div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      class="emotion-0"
    >
      <span
        class="emotion-0"
      >
        exclusions apply.
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`<Marketing UI/DetailsLinkWithPrefix /> snapshots for large breakpoint renders default state correctly GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  <div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      class="emotion-0"
    >
      <span
        class="emotion-0"
      >
        exclusions apply.
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`<Marketing UI/DetailsLinkWithPrefix /> snapshots for large breakpoint renders default state correctly OldNavy 1`] = `
<DocumentFragment>
  <div
    style="font-family: var(--font-family-font1),sans-serif;"
  >
    <div
      class="emotion-0"
    >
      <span
        class="emotion-0"
      >
        exclusions apply.
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`<Marketing UI/DetailsLinkWithPrefix /> snapshots for large breakpoint renders default state correctly OldNavy in crossBrand 1`] = `
<DocumentFragment>
  <div
    style="font-family: var(--font-family-font1),sans-serif;"
  >
    <div
      class="emotion-0"
    >
      <span
        class="emotion-0"
      >
        exclusions apply.
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`<Marketing UI/DetailsLinkWithPrefix /> snapshots for small breakpoint renders default state correctly Athleta 1`] = `
<DocumentFragment>
  <div
    style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      class="emotion-0"
    >
      <span
        class="emotion-0"
      >
        exclusions apply.
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`<Marketing UI/DetailsLinkWithPrefix /> snapshots for small breakpoint renders default state correctly Athleta in crossBrand 1`] = `
<DocumentFragment>
  <div
    style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      class="emotion-0"
    >
      <span
        class="emotion-0"
      >
        exclusions apply.
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`<Marketing UI/DetailsLinkWithPrefix /> snapshots for small breakpoint renders default state correctly BananaRepublic 1`] = `
<DocumentFragment>
  <div
    style="font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;"
  >
    <div
      class="emotion-0"
    >
      <span
        class="emotion-0"
      >
        exclusions apply.
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`<Marketing UI/DetailsLinkWithPrefix /> snapshots for small breakpoint renders default state correctly BananaRepublic in crossBrand 1`] = `
<DocumentFragment>
  <div
    style="font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;"
  >
    <div
      class="emotion-0"
    >
      <span
        class="emotion-0"
      >
        exclusions apply.
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`<Marketing UI/DetailsLinkWithPrefix /> snapshots for small breakpoint renders default state correctly BananaRepublicFactoryStore 1`] = `
<DocumentFragment>
  <div
    style="font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;"
  >
    <div
      class="emotion-0"
    >
      <span
        class="emotion-0"
      >
        exclusions apply.
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`<Marketing UI/DetailsLinkWithPrefix /> snapshots for small breakpoint renders default state correctly BananaRepublicFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  <div
    style="font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;"
  >
    <div
      class="emotion-0"
    >
      <span
        class="emotion-0"
      >
        exclusions apply.
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`<Marketing UI/DetailsLinkWithPrefix /> snapshots for small breakpoint renders default state correctly Gap 1`] = `
<DocumentFragment>
  <div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      class="emotion-0"
    >
      <span
        class="emotion-0"
      >
        exclusions apply.
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`<Marketing UI/DetailsLinkWithPrefix /> snapshots for small breakpoint renders default state correctly Gap in crossBrand 1`] = `
<DocumentFragment>
  <div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      class="emotion-0"
    >
      <span
        class="emotion-0"
      >
        exclusions apply.
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`<Marketing UI/DetailsLinkWithPrefix /> snapshots for small breakpoint renders default state correctly GapFactoryStore 1`] = `
<DocumentFragment>
  <div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      class="emotion-0"
    >
      <span
        class="emotion-0"
      >
        exclusions apply.
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`<Marketing UI/DetailsLinkWithPrefix /> snapshots for small breakpoint renders default state correctly GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  <div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      class="emotion-0"
    >
      <span
        class="emotion-0"
      >
        exclusions apply.
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`<Marketing UI/DetailsLinkWithPrefix /> snapshots for small breakpoint renders default state correctly OldNavy 1`] = `
<DocumentFragment>
  <div
    style="font-family: var(--font-family-font1),sans-serif;"
  >
    <div
      class="emotion-0"
    >
      <span
        class="emotion-0"
      >
        exclusions apply.
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`<Marketing UI/DetailsLinkWithPrefix /> snapshots for small breakpoint renders default state correctly OldNavy in crossBrand 1`] = `
<DocumentFragment>
  <div
    style="font-family: var(--font-family-font1),sans-serif;"
  >
    <div
      class="emotion-0"
    >
      <span
        class="emotion-0"
      >
        exclusions apply.
      </span>
    </div>
  </div>
</DocumentFragment>
`;
