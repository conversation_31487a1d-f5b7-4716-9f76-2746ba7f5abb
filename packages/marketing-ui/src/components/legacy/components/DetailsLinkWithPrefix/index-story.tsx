// @ts-nocheck
'use client';
import React from 'react';
import { StoryFn } from '@storybook/react';
import { baseProps } from './__fixtures__';
import README from './README.mdx';
import DetailsLinkWithPrefix from '.';

export default {
  title: 'Common/JSON Components (Marketing)/DetailsLinkWithPrefix',
  component: DetailsLinkWithPrefix,
  parameters: {
    docs: {
      page: README,
    },
    eyes: { include: false },
  },
};

const DetailsLinkWithPrefixTemplate: StoryFn<typeof DetailsLinkWithPrefix> = args => <DetailsLinkWithPrefix {...args} />;

export const Default = DetailsLinkWithPrefixTemplate.bind({});
Default.args = { ...baseProps };
