// @ts-nocheck
import React from 'react';
import { render, screen, fireEvent, RenderResult, act } from 'test-utils';
import { axe } from 'jest-axe';
import { snapshotsForBreakpoints } from 'test-utils';
import { LARGE, SMALL } from '@ecom-next/core/breakpoint-provider';
import { DetailsLinkWithPrefix, DetailsLinkWithPrefixProps } from './index';
import { baseProps } from './__fixtures__';

const renderDetailsLinkWithPrefix = (config?: { props?: Partial<DetailsLinkWithPrefixProps> }): RenderResult => {
  const { props = baseProps } = config ?? {};
  return render(<DetailsLinkWithPrefix {...baseProps} {...props} />);
};

describe('<Marketing UI/DetailsLinkWithPrefix />', () => {
  describe('snapshots', () => {
    snapshotsForBreakpoints([SMALL, LARGE], DetailsLinkWithPrefix as React.ComponentType, [['default', { prefix: baseProps.prefix }]]);
  });
  describe('Details Prefix', () => {
    it('should render details link with prefix when provided', () => {
      renderDetailsLinkWithPrefix();
      expect(screen.getByText(baseProps.text)).toBeInTheDocument();
      expect(screen.getByText(baseProps.prefix!)).toBeInTheDocument();
    });
  });
  describe('Props passed to Modal', () => {
    it('should use title prop', async () => {
      renderDetailsLinkWithPrefix();
      await act(async () => {
        fireEvent.click(screen.queryByText(baseProps.text)!);
      });
      expect(screen.queryByTitle(baseProps.title!)).toBeInTheDocument();
    });
    it('should use text prop for title', async () => {
      renderDetailsLinkWithPrefix({ props: { title: undefined } });
      await act(async () => {
        fireEvent.click(screen.queryByText(baseProps.text)!);
      });
      expect(screen.queryByTitle(baseProps.text)).toBeInTheDocument();
    });
    it("should use correct modalSize for 'standard'", async () => {
      renderDetailsLinkWithPrefix();
      await act(async () => {
        fireEvent.click(screen.queryByText(baseProps.text)!);
      });
      const modalWrapper = screen.getByTestId('isolationLayer').nextSibling as HTMLElement;
      expect(modalWrapper).toHaveStyleRules({ 'max-width': '600px' });
    });
    it("should use correct modalSize for 'max'", async () => {
      renderDetailsLinkWithPrefix({ props: { modalSize: 'max' } });
      await act(async () => {
        fireEvent.click(screen.queryByText(baseProps.text)!);
      });
      const modalWrapper = screen.getByTestId('isolationLayer').nextSibling as HTMLElement;
      expect(modalWrapper).toHaveStyleRules({ 'max-width': '1000px' });
    });
  });
  describe('a11y', () => {
    it('should not have a11y violations', async () => {
      renderDetailsLinkWithPrefix({ props: { prefix: baseProps.prefix } });
      const prefix = screen.getByText(baseProps.prefix!);
      const btn = screen.queryByText(baseProps.text);
      expect(await axe(prefix!)).toHaveNoViolations();
      expect(await axe(btn!)).toHaveNoViolations();
    });
  });
});
