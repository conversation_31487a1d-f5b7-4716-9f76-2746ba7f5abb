// @ts-nocheck
'use client';
import React from 'react';
import { styled } from '@ecom-next/core/react-stitch';
import { DetailsLinkWithPrefixProps } from './types';
import { ComposableButton } from '../ComposableButton';
import { Variant } from '../ComposableButton/types';
import { mapDataToProps } from '../../helper';

export const DetailsLinkWithPrefix = ({
  prefix,
  text,
  detailsLinkUrl,
  closeButtonAriaLabel,
  prefixStyles,
  linkStyles,
  modalSize,
  title,
  wrapperStyles,
}: DetailsLinkWithPrefixProps): JSX.Element => {
  const modalDataProps = {
    src: detailsLinkUrl,
    closeButtonAriaLabel,
    title: title !== undefined ? title : text,
    modalSize: modalSize !== undefined ? modalSize : 'standard',
  };

  const StyledPrefix = styled.span({
    ...prefixStyles,
  });

  const DetailsLinkWithPrefixWrapper = styled.div({
    ...wrapperStyles,
  });

  return (
    <DetailsLinkWithPrefixWrapper>
      {prefix && <StyledPrefix>{prefix}</StyledPrefix>}
      {text && (
        <ComposableButton css={linkStyles} modalProps={modalDataProps} variant={Variant.flat}>
          {text}
        </ComposableButton>
      )}
    </DetailsLinkWithPrefixWrapper>
  );
};

export default mapDataToProps(DetailsLinkWithPrefix);
export type { DetailsLinkWithPrefixProps } from './types';
