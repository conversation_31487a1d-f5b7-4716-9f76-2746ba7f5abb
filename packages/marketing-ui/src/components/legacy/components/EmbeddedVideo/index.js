// @ts-nocheck
'use client';
import React, { Component } from 'react';
import { BreakpointContext, LARGE } from '@ecom-next/core/breakpoint-provider';
import { compose } from '@ecom-next/core/legacy/utility';
import types from './types';
import wrapWithFallbackContentErrorBoundary from '../../helper/FallbackContentErrorBoundary';
import './styles/EmbeddedVideo.css';

export class EmbeddedVideo extends Component {
  constructor(props) {
    super(props);
    this.state = { isOpen: this.props.data.isOpen };

    this.handleClose = this.handleClose.bind(this);
  }

  handleClose() {
    this.setState(prevState => ({
      isOpen: !prevState.isOpen,
    }));
  }

  render() {
    const { data, isDesktop } = this.props;
    const { onClose, video, containerStyle = {}, closeButton = {}, id, isOpen } = data;

    const shouldShow = onClose ? isOpen : this.state.isOpen;
    if (!shouldShow) return null;

    let videoStyle = {};
    if (video.style) {
      videoStyle = isDesktop ? video.style.desktop : video.style.mobile;
    }
    let closeButtonStyle = {};
    if (closeButton.style) {
      closeButtonStyle = isDesktop ? closeButton.style.desktop : closeButton.style.mobile;
    }
    /* eslint-disable */
    return (
      <div className='video-popup-container' id={id}>
        <div className='video-popup-overlay' data-testid='video-popup-overlay' onClick={onClose || this.handleClose} />
        <div className='popup-content'>
          <div className='video-container' style={isDesktop ? containerStyle.desktop : containerStyle.mobile}>
            <iframe allowFullScreen frameBorder='0' src={video.url} style={videoStyle} />
          </div>
          <div className='video-close' data-testid='video-close' onClick={onClose || this.handleClose} style={closeButtonStyle}>
            <img src={closeButton.src} />
          </div>
        </div>
      </div>
    );
  }
}
/* eslint-enable */

EmbeddedVideo.propTypes = types;
EmbeddedVideo.defaultProps = {
  data: {
    isOpen: false,
    isDesktop: true,
  },
};

const EmbeddedVideoWithBreakpointProvider = props => (
  <BreakpointContext.Consumer>
    {({ greaterOrEqualTo }) => (greaterOrEqualTo(LARGE) ? <EmbeddedVideo {...props} isDesktop /> : <EmbeddedVideo {...props} isDesktop={false} />)}
  </BreakpointContext.Consumer>
);

export default compose(wrapWithFallbackContentErrorBoundary)(EmbeddedVideoWithBreakpointProvider);
