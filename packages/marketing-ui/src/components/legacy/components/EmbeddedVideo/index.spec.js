// @ts-nocheck
import React from 'react';
import { render, fireEvent, act, screen } from 'test-utils';
import { EmbeddedVideo } from '.';

const testData = {
  video: {
    url: 'some video src',
    style: {},
  },
  closeButton: {
    src: 'some close button image source',
    style: {},
  },
  isOpen: true,
};

describe('EmbeddedVideo', () => {
  let data;

  beforeEach(() => {
    data = { ...testData };
  });

  it('should not render when isOpen=false', () => {
    const { container } = render(<EmbeddedVideo data={{ ...data, isOpen: false }} />);
    expect(container.querySelector('.video-popup-container')).not.toBeInTheDocument();
  });

  it('should have isOpen equal to false by default', () => {
    const { container } = render(<EmbeddedVideo />);
    expect(container.querySelector('.video-popup-container')).not.toBeInTheDocument();
  });

  it('should have a video', () => {
    const { container } = render(<EmbeddedVideo data={data} />);
    expect(container.querySelector('iframe')).toBeInTheDocument();
  });

  it('should have video url as video source', () => {
    const { container } = render(<EmbeddedVideo data={data} />);
    expect(container.querySelector(`iframe[src="${testData.video.url}"]`)).toBeInTheDocument();
  });

  it('should have a close button', () => {
    const { container } = render(<EmbeddedVideo data={data} />);
    expect(container.querySelector('.video-close')).toBeInTheDocument();
  });

  it('should pass default close function when callback not provided', async () => {
    const { getByTestId } = render(<EmbeddedVideo data={data} />);
    const videoPopupOverlay = getByTestId('video-popup-overlay');
    await act(async () => {
      fireEvent.click(videoPopupOverlay);
    });
    expect(videoPopupOverlay).not.toBeInTheDocument();
  });

  it('should use callback function when provided', async () => {
    data.onClose = jest.fn();
    render(<EmbeddedVideo data={data} />);
    const videoPopupOverlay = screen.getByTestId('video-popup-overlay');
    await act(async () => {
      fireEvent.click(videoPopupOverlay);
    });
    expect(videoPopupOverlay).toBeInTheDocument();
  });

  it('should close the video popup when close button clicked', async () => {
    const { container } = render(<EmbeddedVideo data={data} />);
    const closeButton = screen.getByTestId('video-close');
    await act(async () => {
      fireEvent.click(closeButton);
    });

    expect(container.querySelector('.video-popup-container')).not.toBeInTheDocument();
  });

  it('should close the video popup when overlay area clicked', async () => {
    render(<EmbeddedVideo data={data} />);
    const videoPopupOverlay = screen.getByTestId('video-popup-overlay');
    await act(async () => {
      fireEvent.click(videoPopupOverlay);
    });
    expect(videoPopupOverlay).not.toBeInTheDocument();
  });

  it('should pass desktop style when it is large breakpoint', () => {
    data.video.style = {
      desktop: {
        height: '10px',
      },
    };
    const { container } = render(<EmbeddedVideo data={data} isDesktop />);
    expect(container.querySelector('iframe')?.style.height).toBe(data.video.style.desktop.height);
  });

  it('should pass mobile style when it is small breakpoint', () => {
    data.video.style = {
      mobile: {
        height: '5px',
      },
    };
    const { container } = render(<EmbeddedVideo data={data} isDesktop={false} />);
    expect(container.querySelector('iframe')?.style.height).toBe(data.video.style.mobile.height);
  });

  it('should pass container style to .video-container', () => {
    data.containerStyle = {
      mobile: {
        height: '3px',
      },
    };
    const { container } = render(<EmbeddedVideo data={data} isDesktop={false} />);
    expect(container.querySelector(`.video-container[style="height: ${data.containerStyle.mobile.height};"]`)).toBeInTheDocument();
  });

  it('should pass close button image source to .video-close>img', () => {
    data.closeButton.src = 'some source image';
    const { container } = render(<EmbeddedVideo data={data} isDesktop={false} />);
    expect(container.querySelector(`.video-close img[src="${data.closeButton.src}"]`)).toBeInTheDocument();
  });

  it('should pass close button style to .video-close', () => {
    data.closeButton.style = {
      mobile: {
        height: '8px',
      },
    };
    const { container } = render(<EmbeddedVideo data={data} isDesktop={false} />);
    expect(container.querySelector(`.video-close[style="height: ${data.closeButton.style.mobile.height};"]`)).toBeInTheDocument();
  });
});
