.video-popup-container {
  float: none;
  clear: both;
  height: 85%;
  position: fixed;
  width: 95%;
  z-index: 999;
  top: 0;
  left: 0;
  right: 0;
  margin: 0 auto;
  text-align: center;

  .video-container {
    position: absolute;
    min-width: 100%;
    min-height: 100%;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    -webkit-transform: translate(-50%, -50%);

    @media only screen and (max-width: 767px) {
      margin-top: 100px;
    }

    iframe {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 92%;
      margin-top: 40px;
    }
  }

  .video-popup-overlay {
    background: #000 none repeat scroll 0 0;
    height: 100%;
    left: 0;
    overflow: hidden;
    position: fixed;
    top: 0;
    width: 100%;
    opacity: 0.85;
  }

  .video-close {
    width: 30px;
    height: 30px;
    background-image: url(/Asset_Archive/ATWeb/content/0011/042/262/assets/close.png);
    float: right;
    position: fixed;
    top: 25px;
    right: 1em;
    cursor: pointer;

    @media only screen and (max-width: 767px) {
      right: 6px;
      top: 98px;
    }
  }
}
