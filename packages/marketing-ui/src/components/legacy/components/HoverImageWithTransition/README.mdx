# HoverImageWithTransition

HoverImageWithTransition is a cross-brand JSON-configurable component that is capable of rendering a background image with an SVG
overlay, both of which have a hover state with an optional transition you can pass via json. The component allows the
flexibility to render only a hover-enabled background image or SVG overlay as well.

| prop              | type         | default value | description                     |
| ----------------- | ------------ | ------------- | ------------------------------- |
| `background`      | `Object?`    | `undefined`   | `background object`             |
| `svgOverlay`      | `Object?`    | `undefined`   | `svgOverlay object`             |
| `backgroundHover` | `Object?`    | `undefined`   | `backgroundHover object`        |
| `svgOverlayHover` | `Object?`    | `undefined`   | `svgOverlayHover object`        |
| `containerStyle`  | `CSSObject?` | `undefined`   | `container styles`              |
| `img`             | `string?`    | `undefined`   | `background image`              |
| `desktopImg`      | `string?`    | `undefined`   | `desktopbackground image`       |
| `altText`         | `string?`    | `undefined`   | `background image alt text`     |
| `style`           | `CSSObject?` | `undefined`   | `background css styles`         |
| `desktopStyle`    | `CSSObject?` | `undefined`   | `desktop background css styles` |

```

Within `data`, users can configure the component's background image and SVG overlay, their hover states, as well as a
`containerStyle` for the component as a whole.

The `background` prop contains configurations for the background image, with it's hover state configured via
`backgroundHover`. Similarly, `svgOverlay` contains configurations for the overlying SVG, with it's hover state
configurations living in `svgOverlayHover`.

Each of these four sections use five props of their own, leveraging a mobile-first approach. `img` is the path to the
default image rendered by each section using the default `style`, which is overriden by `desktopImg` and
`desktopStyle` respectively at non-mobile screen sizes. Both use the provided `altText`.

Although the `data` object is required, none of the four sections are required themselves, nor are any of
their underlying props. However, if the user finds that they do not need either hover state, they should use other
more simple components instead of HoverImage.

```
