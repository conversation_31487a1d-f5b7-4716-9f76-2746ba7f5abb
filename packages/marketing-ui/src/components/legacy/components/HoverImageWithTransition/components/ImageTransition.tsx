// @ts-nocheck
'use client';
import React, { Fragment, useContext, useState } from 'react';
import { CSSObject } from '@ecom-next/core/react-stitch';
import { LARGE, BreakpointContext } from '@ecom-next/core/breakpoint-provider';
import { HoverImageWithTransitionProps, ImageWithTransitionProps, ImageWithTransitionType } from '../types';

export const Image = ({ desktopImg, img, altText, style, desktopStyle, isDesktop, isHover, isOverlay, dataTestid }: ImageWithTransitionProps): JSX.Element => {
  const isDesktopWithStyle = isDesktop && desktopStyle;
  const baseStyles: CSSObject = {
    left: 0,
    top: 0,
    position: isOverlay || isHover ? 'absolute' : 'relative',
    width: '100%',
    opacity: isHover ? 0 : 1,
  };

  const [hover, setHover] = useState(false);

  return (
    <div
      css={[baseStyles, isDesktopWithStyle || style, isHover && hover && { opacity: 1, visibility: 'visible' }]}
      data-testid={dataTestid}
      onMouseEnter={() => {
        setHover(true);
      }}
      onMouseLeave={() => {
        setHover(false);
      }}
    >
      <img alt={altText} src={isDesktop && desktopImg ? desktopImg : img} />
    </div>
  );
};

export const ImageTransition = ({ background, svgOverlay, backgroundHover, svgOverlayHover }: HoverImageWithTransitionProps): JSX.Element => {
  const { minWidth } = useContext(BreakpointContext);
  const isDesktop = minWidth(LARGE);

  const hasImage = ({ img, desktopImg }: ImageWithTransitionType): boolean => !!(img || desktopImg);

  return (
    <>
      {background && hasImage(background) && (
        <>
          <Image isDesktop={isDesktop} {...background} />
          {backgroundHover && hasImage(backgroundHover) && <Image isDesktop={isDesktop} isHover {...backgroundHover} />}
        </>
      )}
      {svgOverlay && hasImage(svgOverlay) && (
        <>
          <Image isDesktop={isDesktop} isOverlay {...svgOverlay} />
          {svgOverlayHover && hasImage(svgOverlayHover) && <Image isDesktop={isDesktop} isHover {...svgOverlayHover} />}
        </>
      )}
    </>
  );
};

export default ImageTransition;
