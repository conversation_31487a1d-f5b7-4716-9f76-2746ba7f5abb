// @ts-nocheck
'use client';
import React from 'react';
import data from './__fixtures__/story-data';
import README from './README.mdx';
import HoverImageWithTransition from '.';

export default {
  title: 'Common/JSON Components (Marketing)/HoverImageWithTransition',
  parameters: {
    knobs: { disable: true },
    docs: {
      page: README,
    },
    eyes: { include: false },
  },
  tags: ['exclude'],
};

const HoverImageWithTransitionTemplate = args => (
  <div style={{ margin: '1em auto', maxWidth: 'fit-content' }}>
    <HoverImageWithTransition {...args} />
  </div>
);

export const Default = HoverImageWithTransitionTemplate.bind({});
Default.args = { ...data };
