// @ts-nocheck
import React from 'react';
import { screen, render, RenderResult, fireEvent, act } from 'test-utils';
import { HoverImageWithTransition } from './index';
import { imageHoverData, imgHoverDataDesktop, imgHoverDataMobile, imgHoverDataDesktopUseImg } from './__fixtures__';
import { HoverImageWithTransitionProps } from './types';

describe('<HoverImageWithTransition />', () => {
  const renderImageTransition = (props?: HoverImageWithTransitionProps): RenderResult => render(<HoverImageWithTransition {...props} />);

  const hoverContainer = (): HTMLElement => screen.getByTestId('hover-container');
  const hoverElement = (): HTMLElement => screen.getByTestId('img-hover');
  const hoverImg = (): HTMLElement | null => screen.queryByAltText('Super Skinny overlay hover');

  test('should match the snapshot', () => {
    renderImageTransition(imageHoverData);
    expect(hoverContainer()).toMatchSnapshot();
  });

  it('should display the hover image when provided and on hover', () => {
    renderImageTransition(imgHoverDataDesktop);
    fireEvent.mouseEnter(hoverElement());
    expect(hoverElement()).toHaveStyleRule('opacity', '1');
  });
  it('should not display the svgoverlay hover image when provided and not on hover', () => {
    renderImageTransition(imgHoverDataDesktop);
    fireEvent.mouseLeave(hoverElement());
    expect(hoverElement()).toHaveStyleRule('opacity', '0');
  });
  it('should display the desktop image when isDesktop equals true', () => {
    renderImageTransition(imgHoverDataDesktop);
    expect(hoverImg()).toHaveAttribute('src', imgHoverDataDesktop.svgOverlay.desktopImg);
  });
  it('should display the mobile image when isDesktop equals false', () => {
    renderImageTransition(imgHoverDataMobile);
    expect(hoverImg()).toHaveAttribute('src', imgHoverDataMobile.svgOverlay.img);
  });
  it('should display the desktop image when isDesktop equals true', () => {
    renderImageTransition(imgHoverDataDesktop);
    expect(hoverImg()).toHaveAttribute('src', imgHoverDataDesktop.svgOverlay.desktopImg);
  });
  it('should use default img on Desktop when desktop img is not provided', () => {
    renderImageTransition(imgHoverDataDesktopUseImg);
    expect(hoverImg()).toHaveAttribute('src', imgHoverDataDesktopUseImg.svgOverlay.img);
  });
});
