// @ts-nocheck
'use client';
import React, { useContext } from 'react';
import { LARGE, BreakpointContext } from '@ecom-next/core/breakpoint-provider';
import { styled } from '@ecom-next/core/react-stitch';
import ImageTransition from './components/ImageTransition';
import { HoverImageWithTransitionProps } from './types';
import { mapDataToProps } from '../../helper';

const HoverImageContainer = styled.div`
  max-width: 100%;
  margin: 0 auto;
  position: relative;
  width: auto;
  height: auto;
`;

const ImgTransitionContainer = styled.div`
  z-index: -50;
`;

export const HoverImageWithTransition = ({
  background,
  svgOverlay,
  backgroundHover,
  svgOverlayHover,
  containerStyle = {},
}: HoverImageWithTransitionProps): JSX.Element => {
  const { minWidth } = useContext(BreakpointContext);
  const isDesktop = minWidth(LARGE);

  return (
    <HoverImageContainer css={isDesktop && containerStyle.desktopStyle ? containerStyle.desktopStyle : containerStyle.style} data-testid='hover-container'>
      <ImgTransitionContainer>
        <ImageTransition background={background} backgroundHover={backgroundHover} svgOverlay={svgOverlay} svgOverlayHover={svgOverlayHover} />
      </ImgTransitionContainer>
    </HoverImageContainer>
  );
};

export default mapDataToProps(HoverImageWithTransition);
