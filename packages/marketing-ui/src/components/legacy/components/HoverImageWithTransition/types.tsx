// @ts-nocheck
'use client';
import { CommonStyleProps } from '../../types';

export type ImageWithTransitionProps = CommonStyleProps & {
  altText?: string;
  desktopImg?: string;
  img?: string;
  isDesktop?: boolean;
  isOverlay?: boolean;
  isHover?: boolean;
  dataTestid?: string;
};

export type ImageWithTransitionType = Pick<ImageWithTransitionProps, 'altText' | 'desktopImg' | 'img'>;

export type HoverImageWithTransitionProps = {
  background?: ImageWithTransitionType;
  backgroundHover?: ImageWithTransitionType & CommonStyleProps;
  containerStyle?: CommonStyleProps;
  svgOverlay?: ImageWithTransitionType & CommonStyleProps;
  svgOverlayHover?: ImageWithTransitionType;
};
