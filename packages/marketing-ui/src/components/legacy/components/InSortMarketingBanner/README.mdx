## `InSortMarketingBanner`

The goal of the `InSortMarketingBanner` is to allow flexible composition of components to be inserted into the product grid. The `InSortMarketingBanner` is very similar to `LayoutComponent` and accepts `marketing-ui` components that are JSON configurable.

If you use `"size":{ "desktop": 1, "mobile": 1 }`, a single ISM banner will occupy one grid cell.

<div
  style={{
    display: 'flex',
    padding: '5px 10px',
    maxWidth: '600px',
    width: '100%',
  }}
>
  <div
    style={{
      padding: '5px',
      backgroundColor: 'rgb(102 102 102 / 18%)',
      width: '150px',
      height: '300px',
      margin: '0 10px',
    }}
  >
    ISM
  </div>
  <div
    style={{
      padding: '5px',
      backgroundColor: 'rgb(102 102 102 / 18%)',
      width: '150px',
      height: '300px',
      margin: '0 10px',
    }}
  >
    product 1
  </div>
  <div
    style={{
      padding: '5px',
      backgroundColor: 'rgb(102 102 102 / 18%)',
      width: '150px',
      height: '300px',
      margin: '0 10px',
    }}
  >
    product 2
  </div>
  <div
    style={{
      padding: '5px',
      backgroundColor: 'rgb(102 102 102 / 18%)',
      width: '150px',
      height: '300px',
      margin: '0 10px',
    }}
  >
    product 3
  </div>
</div>

If you use `"size":{ "desktop": 2, "mobile" : 2 }`, double ISM banner will occupy two grid cells.

<div
  style={{
    display: 'flex',
    padding: '5px 10px',
    maxWidth: '600px',
    width: '100%',
  }}
>
  <div
    style={{
      padding: '5px',
      backgroundColor: 'rgb(102 102 102 / 18%)',
      width: '300px',
      height: '300px',
      margin: '0 10px',
    }}
  >
    ISM
  </div>
  <div
    style={{
      padding: '5px',
      backgroundColor: 'rgb(102 102 102 / 18%)',
      width: '150px',
      height: '300px',
      margin: '0 10px',
    }}
  >
    product 1
  </div>
  <div
    style={{
      padding: '5px',
      backgroundColor: 'rgb(102 102 102 / 18%)',
      width: '150px',
      height: '300px',
      margin: '0 10px',
    }}
  >
    product 2
  </div>
</div>

You can also do `"size":{ "desktop": 2, "mobile" : 1 }`.

## InSortMarketingBanner Priority

There is only one level of InSortMarketingBanner shown on any given Category Page.

1. First priority is sub-category configuration. If sub-category InSortMarketingBanner config exists on the page, it will be shown regardless of whether category or division level configs are available.
2. Second priority is category configuration. If category InSortMarketingBanner config exists on the page, it will be shown regardless of whether a division level config is available.
3. Third priority is division configuration. Division configuration will only be shown when there is no higher priority configuration available. Division will not be shown on pages that contain sub-categories.

## JSON Config

This component uses the same data object as the `LayoutComponent`.

**Notes:**

- The value `size` can be 1 or 2. This number represents the number of product-cards that the banner will occupy.
- The value `rowPosition` is used by the Category Page and represents the number of rows from the top the component will be placed within the product grid. `rowPosition` position is always a number. if you don't specify `rowPosition`, it will default to the first `rowPosition`.
- The value `excludedCids` is used only at the Division level and represents the Category Pages that will not show the Division ISM component from which they're excluded.

<details style={{border: "1px solid #afafaf", padding: "5px", borderRadius: "5px", margin: "5px 0 5px 0"}}>
  <summary>Sample JSON for InSortMarketingBanner with size 1</summary>
  <pre>
 ```
{
  "name": "InSortMarketingBanner",
  "type": "sitewide",
  "excludedCids": [
    "1234",
    "5678"
  ],
  "rowPosition": {
    "desktop": 1,
    "mobile": 1
  },
   "size": {
      "desktop": 1,
      "mobile": 1
  },
  "data": {
    "desktop": {
      "shouldDisplay": true,
      "data": {
        "style": {
          "padding": "0 .5em",
          "display": "flex"
        },
        "components": [
          {
            "type": "builtin",
            "name": "span",
            "data": {
              "props": {
                "style": {
                  "background": "#e7e7e7",
                  "display": "flex",
                  "padding": "0",
                  "justifyContent": "center",
                  "alignItems": "center",
                  "width": "500px",
                  "height": "321px",
                },
              },
              "components": ["Single grid cell"],
            }
          }
        ]
      }
    },
    "mobile": {
      "shouldDisplay": true,
      "data": {
        "style": {},
        "components": [
          {
            "type": "builtin",
            "name": "span",
            "data": {
              "props": {
                "style": {
                  "background": "#e7e7e7",
                  "display": "flex",
                  "padding": "0",
                  "justifyContent": "center",
                  "alignItems": "center",
                  "width": "500px",
                  "height": "321px",
                },
              },
              "components": ["Single grid cell"],
            }
          }
        ]
      }
    }
  }
}
```
  </pre>
</details>
<details style={{border: "1px solid #afafaf", padding: "5px", borderRadius: "5px", margin: "5px 0 5px 0"}}>
  <summary>Sample JSON for InSortMarketingBanner with size 2</summary>
  <pre>
  ```
{
  "name": "InSortMarketingBanner",
  "type": "sitewide",
  "excludedCids": [
    "1234",
    "5678"
  ],
  "rowPosition": {
    "desktop": 1,
    "mobile": 1
  },
   "size": {
      "desktop": 2,
      "mobile": 2
  },
  "data": {
    "desktop": {
      "shouldDisplay": true,
      "data": {
        "style": {
          "padding": "0 .5em",
          "display": "flex"
        },
        "components": [
          {
            "type": "builtin",
            "name": "span",
            "data": {
              "props": {
                "style": {
                  "background": "#e7e7e7",
                  "display": "flex",
                  "padding": "0",
                  "justifyContent": "center",
                  "alignItems": "center",
                  "width": "500px",
                  "height": "321px",
                },
              },
              "components": ["Double grid cell"],
            }
          }
        ]
      }
    },
    "mobile": {
      "shouldDisplay": true,
      "data": {
        "style": {},
        "components": [
          {
            "type": "builtin",
            "name": "span",
            "data": {
              "props": {
                "style": {
                  "background": "#e7e7e7",
                  "display": "flex",
                  "padding": "0",
                  "justifyContent": "center",
                  "alignItems": "center",
                  "width": "500px",
                  "height": "321px",
                },
              },
              "components": ["Double grid cell"],
            }
          }
        ]
      }
    }
  }
}
  ```
  </pre>
</details>
