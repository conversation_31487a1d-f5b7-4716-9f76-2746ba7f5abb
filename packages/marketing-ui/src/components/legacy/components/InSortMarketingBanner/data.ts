// @ts-nocheck
'use client';
const mockDataSize1 = {
  name: 'InSortMarketingBanner',
  type: 'sitewide',
  size: {
    desktop: 1,
    mobile: 1,
  },
  rowPosition: {
    desktop: 1,
    mobile: 1,
  },
  data: {
    desktop: {
      shouldDisplay: true,
      data: {
        style: {
          padding: '0 .5em',
          display: 'flex',
        },
        classes: 'testingClassForLayout',
        components: [
          {
            type: 'builtin',
            name: 'span',
            data: {
              props: {
                style: {
                  background: '#e7e7e7',
                  display: 'flex',
                  padding: '0',
                  justifyContent: 'center',
                  alignItems: 'center',
                  width: '240px',
                  height: '321px',
                },
              },
              components: ['Single grid cell'],
            },
          },
        ],
      },
    },
    mobile: {
      shouldDisplay: true,
      data: {
        classes: 'testingClassForLayout',
        components: [
          {
            type: 'builtin',
            name: 'span',
            data: {
              props: {
                style: {
                  background: '#e7e7e7',
                  display: 'flex',
                  padding: '0',
                  justifyContent: 'center',
                  alignItems: 'center',
                  width: '240px',
                  height: '321px',
                },
              },
              components: ['Single grid cell'],
            },
          },
        ],
      },
    },
  },
};

const mockDataSize2 = {
  name: 'InSortMarketingBanner',
  type: 'sitewide',
  size: {
    desktop: 2,
    mobile: 2,
  },
  rowPosition: {
    desktop: 1,
    mobile: 1,
  },
  data: {
    desktop: {
      shouldDisplay: true,
      data: {
        style: {
          padding: '0 .5em',
          display: 'flex',
        },
        components: [
          {
            type: 'builtin',
            name: 'span',
            data: {
              props: {
                style: {
                  background: '#e7e7e7',
                  display: 'flex',
                  padding: '0',
                  justifyContent: 'center',
                  alignItems: 'center',
                  width: '100%',
                  height: '321px',
                },
              },
              components: ['Double grid cell'],
            },
          },
        ],
      },
    },
    mobile: {
      shouldDisplay: true,
      data: {
        components: [
          {
            type: 'builtin',
            name: 'span',
            data: {
              props: {
                style: {
                  background: '#e7e7e7',
                  display: 'flex',
                  padding: '0',
                  justifyContent: 'center',
                  alignItems: 'center',
                  width: '100%',
                  height: '321px',
                },
              },
              components: ['Double grid cell'],
            },
          },
        ],
      },
    },
  },
};

const mockDataSize3 = {
  name: 'InSortMarketingBanner',
  type: 'sitewide',
  size: {
    desktop: 2,
    mobile: 1,
  },
  rowPosition: {
    desktop: 1,
    mobile: 1,
  },
  data: {
    desktop: {
      shouldDisplay: true,
      data: {
        style: {
          padding: '0 .5em',
          display: 'flex',
        },
        components: [
          {
            type: 'builtin',
            name: 'span',
            data: {
              props: {
                style: {
                  background: '#e7e7e7',
                  display: 'flex',
                  padding: '0',
                  justifyContent: 'center',
                  alignItems: 'center',
                  width: '100%',
                  height: '321px',
                },
              },
              components: ['Double grid cell on Desktop / Single grid cell on Mobile'],
            },
          },
        ],
      },
    },
    mobile: {
      shouldDisplay: true,
      data: {
        components: [
          {
            type: 'builtin',
            name: 'span',
            data: {
              props: {
                style: {
                  background: '#e7e7e7',
                  display: 'flex',
                  padding: '0',
                  justifyContent: 'center',
                  alignItems: 'center',
                  width: '100%',
                  height: '321px',
                },
              },
              components: ['Single grid cell'],
            },
          },
        ],
      },
    },
  },
};

const mockedDataForDesktopAndMobile = {
  desktop: {
    shouldDisplay: true,
    data: {
      style: {
        padding: '0 .5em',
        display: 'flex',
      },
      classes: 'testingClassForLayout',
      components: [
        {
          type: 'builtin',
          name: 'span',
          data: {
            props: {
              style: {
                background: '#e7e7e7',
                display: 'flex',
                padding: '0',
                justifyContent: 'center',
                alignItems: 'center',
                width: '100%',
                height: '321px',
              },
            },
            components: ['Single grid cell'],
          },
        },
      ],
    },
  },
  mobile: {
    shouldDisplay: true,
    data: {
      classes: 'testingClassForLayout',
      components: [
        {
          type: 'builtin',
          name: 'span',
          data: {
            props: {
              style: {
                background: '#e7e7e7',
                display: 'flex',
                padding: '0',
                justifyContent: 'center',
                alignItems: 'center',
                width: '100%',
                height: '321px',
              },
            },
            components: ['Single grid cell'],
          },
        },
      ],
    },
  },
  analytics: {
    onClick: {
      event_name: 'ism_click',
    },
    onView: {
      event_name: 'ism_view',
    },
  },
};

export { mockedDataForDesktopAndMobile, mockDataSize1, mockDataSize2, mockDataSize3 };
