// @ts-nocheck
'use client';
/* eslint consistent-return: "off" */

import { useCallback, useMemo, useContext } from 'react';
import { AnalyticsFn } from '@ecom-next/core/legacy/analytics';
import { MarketingContext } from '@ecom-next/marketing-ui/marketing-provider';

type AnalyticEventsSent = Array<string>;

type AnalyticEventsSentState = {
  analyticEventsSent: AnalyticEventsSent;
};

type MarketingContextAnalytics = {
  analyticEventsSent?: AnalyticEventsSent;
  setMarketingContextState: (state: AnalyticEventsSentState) => void;
};

type UseOnAnalyticsDecorator = {
  name?: string;
  row: number;
  width: number;
};

type DecoratedAnalyticsFn = () => void;

type Decorator = (name: string, fn?: AnalyticsFn) => DecoratedAnalyticsFn | undefined;

const useOnAnalyticsDecorator = ({ name, row, width }: UseOnAnalyticsDecorator): Decorator => {
  const { setMarketingContextState, analyticEventsSent: eventsSent = [] } = useContext<MarketingContextAnalytics>(MarketingContext);

  const analytics = useMemo(() => (name ? { ism_grid: `${name}:R${row}:I${width}` } : undefined), [name, row, width]);

  return useCallback(
    (identifier: string, analyticsFunction?: AnalyticsFn): DecoratedAnalyticsFn | undefined => {
      if (typeof analyticsFunction !== 'function') return;
      if (analytics === undefined) return;

      const event = `${identifier}:${analytics.ism_grid}`;

      if (eventsSent?.includes(event)) return;

      return () => {
        setMarketingContextState({
          analyticEventsSent: [...eventsSent, event],
        });

        return analyticsFunction(analytics);
      };
    },
    [analytics, eventsSent, setMarketingContextState]
  );
};

export default useOnAnalyticsDecorator;
