// @ts-nocheck
import { renderHook, act } from 'test-utils';
import useOnClick from './useOnClick';

describe('useOnClick', () => {
  let mockSetState = jest.fn();
  beforeEach(() => {
    mockSetState = jest.fn();
    jest.mock('react', () => ({
      useState: (initial: any) => [initial, mockSetState],
    }));
  });

  afterEach(() => {
    jest.unmock('react');
  });

  it('should skip click after first click', () => {
    const hook = renderHook(() => useOnClick(mockSetState));

    expect(mockSetState).toHaveBeenCalledTimes(0);
    expect(mockSetState).toHaveBeenCalledTimes(0);
    act(() => {
      hook.result.current();
    });
    expect(mockSetState).toHaveBeenCalledTimes(1);
    expect(mockSetState).toHaveBeenCalledTimes(1);
    act(() => {
      hook.result.current();
    });
    expect(mockSetState).toHaveBeenCalledTimes(1);
    expect(mockSetState).toHaveBeenCalledTimes(1);
  });

  it('should not call a function that is not a function', () => {
    const hook = renderHook(() => useOnClick());
    expect(mockSetState).toHaveBeenCalledTimes(0);
    act(() => {
      hook.result.current();
      hook.result.current();
      hook.result.current();
      hook.result.current();
      hook.result.current();
    });
    expect(mockSetState).toHaveBeenCalledTimes(0);
  });
});
