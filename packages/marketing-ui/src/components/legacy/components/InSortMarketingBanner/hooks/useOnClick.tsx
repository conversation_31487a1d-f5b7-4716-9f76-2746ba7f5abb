// @ts-nocheck
'use client';
import { useState, useCallback } from 'react';
import { AnalyticsFn } from '@ecom-next/core/legacy/analytics';

type OnClick = () => void;

const useOnClick = (onClick?: AnalyticsFn): OnClick => {
  const [clicked, setClicked] = useState(false);

  return useCallback(() => {
    if (clicked) return;
    if (typeof onClick !== 'function') return;

    onClick();
    setClicked(true);
  }, [onClick, clicked]);
};

export default useOnClick;
