// @ts-nocheck
'use client';
import { useState, useEffect } from 'react';
import { useInView } from 'react-intersection-observer';
import { AnalyticsFn } from '@ecom-next/core/legacy/analytics';

type Ref = (node?: Element | null) => void;

const useOnView = (onView?: AnalyticsFn): Ref => {
  const [viewed, setViewed] = useState(false);
  const { ref, inView } = useInView({ triggerOnce: true });

  useEffect(() => {
    if (viewed) return;
    if (!inView) return;
    if (typeof onView !== 'function') return;

    onView();
    setViewed(true);
  }, [inView, onView, viewed]);

  return ref;
};

export default useOnView;
