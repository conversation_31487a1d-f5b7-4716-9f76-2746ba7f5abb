// @ts-nocheck
'use client';
import React from 'react';
import InSortMarketingBanner from '.';
import JsonDynamicMarketing from '../../../json-marketing';
import CmsDynamicMarketing from '../../../legacy-mui-entry';
import { MarketingProvider } from '../../../marketing-provider';
import { mockDataSize1, mockDataSize2, mockDataSize3 } from './data';
import readme from './README.mdx';

const Grid = ({ children, size }) => {
  const desktopClass = `sds_g-1280-${size.desktop > 1 ? '1-2' : '1-4'}`;
  const tabletClass = `sds_g-lg-${size.desktop > 1 ? '2-3' : '1-3'}`;
  const mobileClass = `sds_g-${size.mobile > 1 ? '1' : '1-2'}`;

  return <div className={`${mobileClass} ${tabletClass} ${desktopClass}`}>{children}</div>;
};

const withGrid = story => <div className='sds_grid-root'>{story()}</div>;

const marketingData = {
  contentData: {
    contentItems: [],
  },
};

const withProviders = (story, context) => (
  <MarketingProvider value={marketingData} jsonMarketingComponent={JsonDynamicMarketing} cmsMarketingComponent={CmsDynamicMarketing}>
    {story(context)}
  </MarketingProvider>
);

export default {
  title: 'Common/JSON Components (Marketing)/InSortMarketingBanner',
  decorators: [withGrid, withProviders],
  parameters: {
    docs: {
      page: readme,
    },
    eyes: { include: false },
  },
  tags: ['exclude-at', 'exclude-br', 'exclude-on'],
};

const InSortMarketingBannerTemplate = args => (
  <Grid size={args.size}>
    <InSortMarketingBanner {...args} />
  </Grid>
);

export const ContentFillsSingleGridCell = InSortMarketingBannerTemplate.bind({});
ContentFillsSingleGridCell.args = { ...mockDataSize1 };

export const ContentFillsTwoGridCells = InSortMarketingBannerTemplate.bind({});
ContentFillsTwoGridCells.args = { ...mockDataSize2 };

export const ContentFillsTwoGridCellsDesktopSingleCellMobile = InSortMarketingBannerTemplate.bind({});
ContentFillsTwoGridCellsDesktopSingleCellMobile.args = { ...mockDataSize3 };
