// @ts-nocheck
import React, { ReactElement } from 'react';
import { render, screen, fireEvent, RenderOptions, RenderResult, act } from 'test-utils';
import { SMALL } from '@ecom-next/core/breakpoint-provider';
import { mockAllIsIntersecting } from 'react-intersection-observer/test-utils';
import { AppState } from '@ecom-next/sitewide/app-state-provider';
import { MarketingContext } from '@ecom-next/marketing-ui/marketing-provider';
import { RowSizeTypes } from './types';
import InSortMarketingBanner from './index';
import { mockedDataForDesktopAndMobile } from './data';

describe('InSortMarketingBanner Component', () => {
  const onView = jest.fn();
  const onClick = jest.fn();
  const setMarketingContextState = jest.fn();

  const renderWithProviders = (children: ReactElement, options?: RenderOptions<AppState>): RenderResult =>
    render(
      <MarketingContext.Provider
        value={{
          setMarketingContextState,
        }}
      >
        {children}
      </MarketingContext.Provider>,
      options
    );

  beforeEach(() => jest.clearAllMocks());

  it('checks that LayoutComponent renders correctly', () => {
    const size = {
      desktop: 1,
      mobile: 1,
    } as RowSizeTypes;

    const rowPosition = {
      desktop: 1,
      mobile: 3,
    };

    renderWithProviders(
      <InSortMarketingBanner
        businessUnitId='1'
        data={mockedDataForDesktopAndMobile}
        marketingName='Rockstar Super-Skinny'
        rowPosition={rowPosition}
        size={size}
      />
    );

    const element = screen.queryByTestId('InSortMarketingBanner');

    expect(element?.firstChild).toHaveClass('testingClassForLayout');
  });

  it('does not call datalayer link when component is not visible', () => {
    const size = {
      desktop: 1,
      mobile: 2,
    } as RowSizeTypes;

    const rowPosition = {
      desktop: 1,
      mobile: 3,
    };

    renderWithProviders(
      <InSortMarketingBanner
        businessUnitId='1'
        data={mockedDataForDesktopAndMobile}
        marketingName='Rockstar Super-Skinny'
        onView={onView}
        rowPosition={rowPosition}
        size={size}
      />
    );

    mockAllIsIntersecting(false);

    expect(onView).not.toHaveBeenCalled();
  });

  it('calls datalayer link when component is visible', async () => {
    const size = {
      desktop: 1,
      mobile: 2,
    } as RowSizeTypes;

    const rowPosition = {
      desktop: 1,
      mobile: 3,
    };

    renderWithProviders(
      <InSortMarketingBanner
        businessUnitId='1'
        data={mockedDataForDesktopAndMobile}
        marketingName='Rockstar Super-Skinny'
        onView={onView}
        rowPosition={rowPosition}
        size={size}
      />
    );

    mockAllIsIntersecting(true);

    const params = [
      {
        ism_grid: 'Rockstar Super-Skinny:R1:I1',
      },
    ];

    expect(onView).toHaveBeenCalledWith(params);
  });

  it('calls the datalayer link just once when component is visible', async () => {
    const size = {
      desktop: 1,
      mobile: 2,
    } as RowSizeTypes;

    const rowPosition = {
      desktop: 1,
      mobile: 3,
    };

    renderWithProviders(
      <InSortMarketingBanner
        businessUnitId='1'
        data={mockedDataForDesktopAndMobile}
        marketingName='Rockstar Super-Skinny'
        onView={onView}
        rowPosition={rowPosition}
        size={size}
      />
    );

    mockAllIsIntersecting(true);
    mockAllIsIntersecting(false);
    mockAllIsIntersecting(true);

    expect(onView).toHaveBeenCalledTimes(1);
  });

  it('calls datalayer link when component is clicked', async () => {
    const size = {
      desktop: 1,
      mobile: 2,
    } as RowSizeTypes;

    const rowPosition = {
      desktop: 1,
      mobile: 3,
    };

    renderWithProviders(
      <InSortMarketingBanner
        businessUnitId='1'
        data={mockedDataForDesktopAndMobile}
        marketingName='Rockstar Super-Skinny'
        onClick={onClick}
        rowPosition={rowPosition}
        size={size}
      />
    );

    const element = screen.queryByTestId('InSortMarketingBanner');

    if (element)
      await act(async () => {
        fireEvent.click(element);
      });

    const params = [
      {
        ism_grid: 'Rockstar Super-Skinny:R1:I1',
      },
    ];

    expect(onClick).toHaveBeenCalledWith(params);
  });

  it('calls datalayer link just once when component is clicked', async () => {
    const size = {
      desktop: 1,
      mobile: 2,
    } as RowSizeTypes;

    const rowPosition = {
      desktop: 1,
      mobile: 3,
    };

    renderWithProviders(
      <InSortMarketingBanner
        businessUnitId='1'
        data={mockedDataForDesktopAndMobile}
        marketingName='Rockstar Super-Skinny'
        onClick={onClick}
        rowPosition={rowPosition}
        size={size}
      />
    );

    const element = screen.queryByTestId('InSortMarketingBanner');

    if (element) {
      await act(async () => {
        fireEvent.click(element);
      });
      await act(async () => {
        fireEvent.click(element);
      });
    }

    expect(onClick).toHaveBeenCalledTimes(1);
  });

  it('updates marketing context state with sent events', async () => {
    const size = {
      desktop: 1,
      mobile: 2,
    } as RowSizeTypes;

    const rowPosition = {
      desktop: 1,
      mobile: 3,
    };

    renderWithProviders(
      <InSortMarketingBanner
        businessUnitId='1'
        data={mockedDataForDesktopAndMobile}
        marketingName='Rockstar Super-Skinny'
        onClick={onClick}
        rowPosition={rowPosition}
        size={size}
      />
    );

    mockAllIsIntersecting(true);

    const element = screen.queryByTestId('InSortMarketingBanner');

    if (element) {
      await act(async () => {
        fireEvent.click(element);
      });
    }

    expect(setMarketingContextState).toHaveBeenCalledWith({
      analyticEventsSent: ['onView:Rockstar Super-Skinny:R1:I1'],
    });
    expect(setMarketingContextState).toHaveBeenCalledWith({
      analyticEventsSent: ['onClick:Rockstar Super-Skinny:R1:I1'],
    });
  });

  describe('on mobile', () => {
    it('calls datalayer link when component is clicked', async () => {
      const size = {
        desktop: 1,
        mobile: 2,
      } as RowSizeTypes;

      const rowPosition = {
        desktop: 1,
        mobile: 3,
      };

      renderWithProviders(
        <InSortMarketingBanner
          businessUnitId='1'
          data={mockedDataForDesktopAndMobile}
          marketingName='Rockstar Super-Skinny'
          onClick={onClick}
          rowPosition={rowPosition}
          size={size}
        />,
        { breakpoint: SMALL }
      );

      const element = screen.queryByTestId('InSortMarketingBanner');

      if (element)
        await act(async () => {
          fireEvent.click(element);
        });

      const params = [
        {
          ism_grid: 'Rockstar Super-Skinny:R3:I2',
        },
      ];

      expect(onClick).toHaveBeenCalledWith(params);
    });

    it('calls datalayer link when component is visible', async () => {
      const size = {
        desktop: 1,
        mobile: 2,
      } as RowSizeTypes;

      const rowPosition = {
        desktop: 1,
        mobile: 3,
      };

      renderWithProviders(
        <InSortMarketingBanner
          businessUnitId='1'
          data={mockedDataForDesktopAndMobile}
          marketingName='Rockstar Super-Skinny'
          onView={onView}
          rowPosition={rowPosition}
          size={size}
        />,
        { breakpoint: SMALL }
      );

      mockAllIsIntersecting(true);

      const params = [
        {
          ism_grid: 'Rockstar Super-Skinny:R3:I2',
        },
      ];

      expect(onView).toHaveBeenCalledWith(params);
    });
  });
});
