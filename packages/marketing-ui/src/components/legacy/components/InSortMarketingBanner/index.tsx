// @ts-nocheck
'use client';
import React, { useContext } from 'react';
import { BreakpointContext, LARGE } from '@ecom-next/core/breakpoint-provider';
import { withAnalytics } from '@ecom-next/core/legacy/analytics';
import { InSortMarketingBannerTypes } from './types';
import LayoutComponent from '../LayoutComponent';
import useOnView from './hooks/useOnView';
import useOnClick from './hooks/useOnClick';
import useOnAnalyticsDecorator from './hooks/useOnAnalyticsDecorator';

const InSortMarketingBanner = ({
  data,
  marketingName,
  onClick,
  onView,
  size = { desktop: 1, mobile: 1 },
  rowPosition = { desktop: 1, mobile: 1 },
}: InSortMarketingBannerTypes): JSX.Element => {
  const { greaterOrEqualTo } = useContext(BreakpointContext);
  const isDesktop = greaterOrEqualTo(LARGE);
  const width = isDesktop ? size.desktop : size.mobile;
  const row = isDesktop ? rowPosition.desktop : rowPosition.mobile;

  const onAnalyticsDecorator = useOnAnalyticsDecorator({
    name: marketingName,
    row,
    width,
  });

  const onMarketingClick = useOnClick(onAnalyticsDecorator('onClick', onClick));

  const ref = useOnView(onAnalyticsDecorator('onView', onView));

  return (
    <div ref={ref} data-testid='InSortMarketingBanner' onClick={onMarketingClick} role='button' tabIndex={0}>
      {data && <LayoutComponent data={data} />}
    </div>
  );
};

export default withAnalytics(InSortMarketingBanner);
export * from './types';
