// @ts-nocheck
'use client';
import { AnalyticsFn } from '@ecom-next/core/legacy/analytics';
import { LayoutDataProps } from '../LayoutComponent/types';

export type RowTypes = {
  desktop: number;
  mobile: number;
};

type NumberRowOption = 1 | 2;

export type RowSizeTypes = {
  desktop: NumberRowOption;
  mobile: NumberRowOption;
};

export type InSortMarketingBannerTypes = {
  data?: LayoutDataProps;
  rowPosition?: RowTypes;
  size?: RowSizeTypes;
  onClick?: AnalyticsFn;
  onView?: AnalyticsFn;
  marketingName?: string;
};
