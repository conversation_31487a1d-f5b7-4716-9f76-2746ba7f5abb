## 2022-12

### Feature

- Add Optional Desktop link for background. [WCD-492](https://gapinc.atlassian.net/browse/WCD-492)

## 2022-12

### Feature

- Add DetailsLinkWithPrefix. [WCD-481](https://gapinc.atlassian.net/browse/WCD-481)

## 2022-09

### Feature

- Remove detailsLink object that renders a Details button (ComposableButton). [WCD-428](https://gapinc.atlassian.net/browse/WCD-428)

- Add a story and test that shows how to use the `to` inside the `linkData` prop when using jumplinking for the background image so that it renders a hash in the URL for tracking purposes. [WCD-317](https://gapinc.atlassian.net/browse/WCD-317)

- Add new detailsLink object that renders a Details button (ComposableButton) in one simplify LCM instance. [WCD-150](https://gapinc.atlassian.net/browse/WCD-150)

- Add a style option to the Div that is rendered as a placeholder for when linkData does not exist. https://gapinc.atlassian.net/browse/WCD-153

- Update to LCM to include jumpLink on the background image as optional, update to tests, update to return null in functions. https://gapinc.atlassian.net/browse/WCD-147
