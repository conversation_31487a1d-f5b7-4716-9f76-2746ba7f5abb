// @ts-nocheck
import { merge } from 'lodash';
import { LayeredContentModuleProps } from '..';

export const baseProps: Omit<LayeredContentModuleProps, 'ctaList'> & {
  ctaList: Partial<LayeredContentModuleProps['ctaList']>;
} = {
  container: {
    style: {
      color: 'red',
    },
    desktopStyle: {
      color: 'blue',
    },
  },
  overlay: {
    alt: 'overlay label',
    srcUrl: 'mobile.svg',
    desktopSrcUrl: 'desktop.svg',
    style: {
      color: 'red',
    },
    desktopStyle: {
      color: 'blue',
    },
  },
  ctaList: {
    style: {
      color: 'red',
    },
    desktopStyle: {
      color: 'blue',
    },
  },
  background: {
    className: 'backgroundDiv',
    style: {
      color: 'red',
    },
    desktopStyle: {
      color: 'blue',
    },
    image: {
      alt: 'This is alt text',
      srcUrl: 'mobile.jpg',
      desktopSrcUrl: 'desktop.jpg',
      style: {
        color: 'red',
      },
      desktopStyle: {
        color: 'blue',
      },
    },
    linkData: {
      to: 'http://gap.com',
      title: 'Link to Gap',
      'aria-label': 'Go to Gap Home',
    },
  },
};

export const linkCTAProps: LayeredContentModuleProps = merge({}, baseProps, {
  ctaList: {
    className: 'ctaListDiv',
    ctas: [
      {
        style: {
          color: 'red',
        },
        desktopStyle: {
          color: 'blue',
        },
        linkData: {
          to: 'href',
          title: 'This is Number One',
        },
        composableButtonData: {
          children: 'One',
        },
      },
      {
        linkData: {
          to: 'href',
          title: 'This is Number Two',
        },
        composableButtonData: {
          children: 'Two',
        },
      },
      {
        linkData: {
          isAJumplink: true,
          jumplinkCSSSelector: '.jump-to-me',
          title: 'This is Number Three',
        },
        composableButtonData: {
          children: 'Three',
        },
      },
    ],
  },
});

export const noLinkDataProps: LayeredContentModuleProps = {
  container: {
    style: {
      color: 'red',
    },
    desktopStyle: {
      color: 'blue',
    },
  },
  overlay: {
    alt: 'overlay label',
    srcUrl: 'mobile.svg',
    desktopSrcUrl: 'desktop.svg',
    style: {
      color: 'red',
    },
    desktopStyle: {
      color: 'blue',
    },
  },
  background: {
    className: 'backgroundDiv',
    style: {
      color: 'red',
    },
    noLinkDataStyle: {
      display: 'flex',
    },
    desktopStyle: {
      color: 'blue',
    },
    image: {
      alt: 'This is alt text',
      srcUrl: 'mobile.jpg',
      desktopSrcUrl: 'desktop.jpg',
      style: {
        color: 'red',
      },
      desktopStyle: {
        color: 'blue',
      },
    },
  },
};

export const buttonDropdownProps: LayeredContentModuleProps = merge({}, baseProps, {
  ctaList: {
    ctas: [
      {
        buttonDropdownData: {
          heading: {
            text: 'Shop Halloween styles',
          },
          submenu: [
            {
              href: 'girls',
              text: 'Girls',
            },
          ],
          style: {
            desktop: {},
            mobile: {},
          },
        },
      },
    ],
  },
});

export const modalCTAProps: LayeredContentModuleProps = merge({}, baseProps, {
  ctaList: {
    ctas: [
      {
        modalData: {
          closeButtonAriaLabel: 'close modal',
          modalSize: 'max',
          title: 'Hello',
          disablePortal: true,
          iframeData: {
            title: "It's a placeholder",
            height: '500px',
            src: 'https://placeholder.pics/svg/968x500/svg',
          },
        },
        composableButtonData: {
          children: 'Hello',
        },
        style: {},
        desktopStyle: {},
      },
    ],
  },
});

export const modalCTAOpenProps: LayeredContentModuleProps = merge({}, baseProps, {
  ctaList: {
    ctas: [
      {
        modalData: {
          closeButtonAriaLabel: 'close modal',
          modalSize: 'max',
          isOpen: true,
          disablePortal: true,
          iframeData: {
            title: 'Hello',
            src: 'https://placeholder.pics/svg/968x500/svg',
            height: '500px',
          },
        },
        composableButtonData: {
          children: 'Hello',
        },
        style: {},
        desktopStyle: {},
      },
    ],
  },
});

export const overlayWithDivTextNode: LayeredContentModuleProps = merge({}, baseProps, {
  overlay: {
    tag: 'div',
    text: 'hello!!!!',
  },
  ctaList: {
    ctas: [],
  },
});

export const overlayWithChildTextNode: LayeredContentModuleProps = merge({}, baseProps, {
  overlay: {
    children: [
      {
        tag: 'h2',
        text: 'This should be wrapped by a h2',
      },
      {
        text: 'This should be wrapped by a span',
      },
      {
        children: [
          {
            tag: 'p',
            text: 'This should be wrapped by a p',
          },
          {
            text: 'This should be wrapped by a span',
          },
        ],
        style: {
          display: 'flex',
          flexDirection: 'row',
          fontSize: '2rem',
        },
      },
      {
        tag: 'span',
        text: 'This should be wrapped by a span',
      },
    ],
    color: 'white',
    style: {
      position: 'absolute',
      top: '0',
      fontSize: '3rem',
    },
  },
  ctaList: {
    ctas: [],
  },
});

export const backgroundImageJumpLinkProps: Omit<LayeredContentModuleProps, 'ctaList'> = merge({}, baseProps, {
  background: {
    className: 'backgroundDiv',
    style: {
      color: 'red',
    },
    desktopStyle: {
      color: 'blue',
    },
    image: {
      alt: 'This is alt text',
      srcUrl: 'mobile.jpg',
      desktopSrcUrl: 'desktop.jpg',
      style: {
        color: 'red',
      },
      desktopStyle: {
        color: 'blue',
      },
    },
    linkData: {
      isAJumplink: true,
      jumplinkCSSSelector: '.jump-to-me',
    },
  },
});

export const withDetailsLinkWithPrefixProps = merge({}, baseProps, {
  ctaList: {
    ctas: [],
  },
  detailsLink: {
    prefix: 'exclusions apply.',
    text: 'Details',
    detailsLinkUrl: 'url',
    closeButtonAriaLabel: 'close aria label',
    prefixStyles: {
      fontSize: '10px',
      fontWeight: 'normal',
      padding: '0',
      minHeight: '0',
      border: 'none',
      textTransform: 'none',
    },
    linkStyles: {
      fontSize: '10px',
      right: '5%',
      '@media only screen and (max-width: 1024px)': {},
    },
    modalSize: 'standard',
    title: 'Modal Title',
  },
});

export const withDesktopToProps = merge({}, baseProps, {
  ctaList: {
    ctas: [],
  },
  background: {
    className: 'backgroundDiv',
    style: {
      color: 'red',
    },
    desktopStyle: {
      color: 'blue',
    },
    image: {
      alt: 'This is alt text',
      srcUrl: 'mobile.jpg',
      desktopSrcUrl: 'desktop.jpg',
      style: {
        color: 'red',
      },
      desktopStyle: {
        color: 'blue',
      },
    },
    linkData: {
      desktopTo: 'www.some-link-for-desktop.com',
      to: 'http://gap.com',
      title: 'Link to Gap',
      'aria-label': 'Go to Gap Home',
    },
  },
});
