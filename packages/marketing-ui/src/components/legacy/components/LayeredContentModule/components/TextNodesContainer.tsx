// @ts-nocheck
'use client';
import React, { useContext } from 'react';
import { useTheme, Theme, CSSObject } from '@ecom-next/core/react-stitch';
import { PersonalizationContext } from '@ecom-next/sitewide/personalization-provider';
import type { PersonalizationContextData } from '@ecom-next/core/legacy/personalization-provider/types';
import { TextNodesContainer as TextNodesContainerProps, TextNode as TextNodeProps, isTextNode, OverlayNode } from '../types';

const getColor = (theme: Theme, color: Required<OverlayNode>['color']): CSSObject => {
  const colors = {
    primary: { color: theme.color.b1 },
    black: { color: theme.color.bk },
    white: { color: theme.color.wh },
  };
  return colors[color];
};

const replaceFirstName = (childNode: string, firstName: string) => {
  const regEx = /({{FIRST_NAME}})/g;
  const newText = childNode.replace(regEx, firstName);
  return newText;
};

export const TextNode = ({
  className,
  color = 'black',
  font = 'secondary',
  desktopStyle,
  isDesktopSize,
  style,
  text,
  tag = 'span',
}: TextNodeProps & { isDesktopSize: boolean }): JSX.Element => {
  const theme = useTheme();
  const context = useContext(PersonalizationContext) as unknown as PersonalizationContextData;
  const firstName = context.firstName ?? '';
  const CustomTag = tag;
  return (
    <CustomTag className={className} css={[getColor(theme, color), font && theme.font[font], style, isDesktopSize && desktopStyle]}>
      {typeof text === 'object' ? text : replaceFirstName(text, firstName)}
    </CustomTag>
  );
};

export const TextNodesContainer = ({
  children,
  className,
  color = 'black',
  font = 'secondary',
  desktopStyle,
  isDesktopSize,
  style,
}: TextNodesContainerProps & { isDesktopSize: boolean }): JSX.Element => {
  const theme = useTheme();
  return (
    <div className={className} css={[getColor(theme, color), font && theme.font[font], style, isDesktopSize && desktopStyle]}>
      {children.map((currentNode, index) => {
        if (isTextNode(currentNode))
          return (
            <TextNode
              // Safe to use index as key since this isn't being re-arranged by client
              // eslint-disable-next-line react/no-array-index-key
              key={`${currentNode.text}--${index}`}
              color={color}
              isDesktopSize={isDesktopSize}
              {...currentNode}
            />
          );
        return (
          <TextNodesContainer
            // eslint-disable-next-line react/no-array-index-key
            key={`text-node-container--${index}`}
            color={color}
            isDesktopSize={isDesktopSize}
            {...currentNode}
          />
        );
      })}
    </div>
  );
};
