// @ts-nocheck
'use client';
import { styled } from '@ecom-next/core/react-stitch';
import { Link } from '@ecom-next/core/migration/link';

export const BackgroundImgContainer = styled.div`
  position: relative;
`;

export const BackgroundImage = styled.img`
  width: 100%;
  height: auto;
`;

export const OverlayImage = styled.img`
  position: absolute;
  width: 100%;
  height: auto;
  top: 0;
  left: 0;
`;

export const BackgroundLink = styled(Link)`
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  z-index: 25;
`;

export const CTAList = styled.div`
  z-index: 30;
  pointer-events: none;

  a,
  button {
    pointer-events: auto;
  }
`;

export { TextNodesContainer } from './TextNodesContainer';
