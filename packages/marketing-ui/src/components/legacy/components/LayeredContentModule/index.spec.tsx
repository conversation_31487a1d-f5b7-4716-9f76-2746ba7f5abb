// @ts-nocheck
import React from 'react';
import { axe } from 'jest-axe';
import { render, screen, RenderResult, fireEvent, waitForElementToBeRemoved, act } from 'test-utils';
import { snapshotsForBreakpoints } from 'test-utils';
import { LARGE, SMALL, Size } from '@ecom-next/core/breakpoint-provider';
import { PersonalizationContext } from '@ecom-next/sitewide/personalization-provider';
import type { PersonalizationContextData } from '@ecom-next/core/legacy/personalization-provider/types';
import { merge } from 'lodash';
import { jumpToSection } from '../../helper/jumpToSection';
import { CombinedLinkData } from './types';
import { LinkProps } from '../Link';
import { LayeredContentModule, LayeredContentModuleProps, LinkCTAProps, Image, ButtonDropdownCTAProps, ModalCTAProps } from '.';
import {
  linkCTAProps,
  modalCTAProps,
  modalCTAOpenProps,
  buttonDropdownProps,
  noLinkDataProps,
  overlayWithDivTextNode,
  overlayWithChildTextNode,
  withDetailsLinkWithPrefixProps,
  withDesktopToProps,
} from './__fixtures__';

jest.mock('../../helper/jumpToSection');

const renderLCM = (config?: { props?: LayeredContentModuleProps; breakpoint?: Size }): RenderResult => {
  const { props = linkCTAProps, breakpoint = SMALL } = config ?? {};
  return render(<LayeredContentModule {...props} />, { breakpoint });
};

const defaultPersonalizationData = {
  isLoading: true,
  isEmpty: true,
  isError: false,
  personalizationReferrerInfo: {},
  shoppingBag: {
    totalItemCount: 0,
  },
};

const renderWithPersonalization = (config?: { props?: LayeredContentModuleProps; breakpoint?: Size; context?: PersonalizationContextData }) => {
  const { props = linkCTAProps, breakpoint = SMALL, context = defaultPersonalizationData } = config ?? {};
  return render(
    <PersonalizationContext.Provider
      // @ts-ignore
      value={{
        ...context,
        isEmpty: false,
        isError: false,
        isLoading: false,
      }}
    >
      <div>
        <LayeredContentModule {...props} />
      </div>
    </PersonalizationContext.Provider>,
    { breakpoint }
  );
};

describe('<Marketing UI/LayeredContentModule />', () => {
  describe('snapshots', () => {
    snapshotsForBreakpoints([SMALL, LARGE], LayeredContentModule as React.ComponentType, [
      ['default', linkCTAProps],
      ['button dropdown', buttonDropdownProps],
      ['with modal (closed)', modalCTAProps],
      ['with modal (open)', modalCTAOpenProps],
      ['with noLinkDataStyle applied', noLinkDataProps],
      ['with a TextNode wrapped by a div', overlayWithDivTextNode],
      ['with children TextNode with different tags', overlayWithChildTextNode],
    ]);
  });

  describe('CTA List', () => {
    beforeEach(() => renderLCM());

    test('should render the correct number of links', () => {
      expect(screen.getAllByRole('link').length).toBe(3);
    });

    test('should have a testid that is represented by a key', () => {
      linkCTAProps.ctaList!.ctas.forEach((cta, index) => {
        expect(screen.getByTestId(`cta-button-dropdown-link-${index}`)).toBeInTheDocument();
      });
    });

    test('should render correct text for CTAs', () => {
      linkCTAProps.ctaList!.ctas.forEach(cta => {
        expect(screen.getByText((cta as LinkCTAProps).composableButtonData.children!)).toBeInTheDocument();
      });
    });

    test('should render a title attribute to the link when provided a description', () => {
      expect(screen.getByTitle((linkCTAProps.ctaList!.ctas[0] as LinkCTAProps).linkData.title!)).toBeInTheDocument();
    });

    test('should render a jumplink when provided', async () => {
      await act(async () => {
        fireEvent.click(screen.getByTitle('This is Number Three'));
      });
      expect(jumpToSection).toHaveBeenCalledWith(true, '.jump-to-me');
    });

    test('should render ButtonDropdown when provided', () => {
      renderLCM({
        props: buttonDropdownProps,
        breakpoint: LARGE,
      });
      expect(
        screen.getByRole('button', {
          name: (buttonDropdownProps.ctaList!.ctas[0] as ButtonDropdownCTAProps).buttonDropdownData.heading!.text!,
        })
      ).toBeInTheDocument();
    });

    test('should have no border color by default', () => {
      linkCTAProps.ctaList!.ctas.forEach((cta, index) => {
        expect(screen.getByTestId(`cta-button-dropdown-link-${index}`)).toHaveStyle({ borderColor: 'transparent' });
      });
    });
  });

  describe('Modal CTA', () => {
    it('should render a modal when button clicked', async () => {
      renderLCM({ props: modalCTAProps });
      expect(screen.queryByRole('dialog')).not.toBeInTheDocument();
      await act(async () => {
        fireEvent.click(screen.getByText((modalCTAProps.ctaList!.ctas[0] as ModalCTAProps).composableButtonData.children!));
      });
      expect(screen.getByRole('dialog')).toBeInTheDocument();
    });

    it('should have a testid that is represented by a key', () => {
      renderLCM({ props: modalCTAProps });
      modalCTAProps.ctaList!.ctas.forEach((cta, index) => {
        expect(screen.getByTestId(`cta-button-dropdown-modal-${index}`)).toBeInTheDocument();
      });
    });

    it.skip('should close modal when button clicked', async () => {
      renderLCM({ props: modalCTAProps });
      fireEvent.click(screen.getByText((modalCTAProps.ctaList!.ctas[0] as ModalCTAProps).composableButtonData.children!));
      expect(screen.getByRole('dialog')).toBeInTheDocument();

      fireEvent.click(screen.getByLabelText('close modal'));
      await waitForElementToBeRemoved(screen.getByRole('dialog'));
      expect(screen.queryByRole('dialog')).toBeNull();
    });
  });

  describe('Background', () => {
    test('should render an image', () => {
      renderLCM();
      expect(screen.getByAltText(linkCTAProps.background!.image!.alt!)).toBeInTheDocument();
    });

    test('should have a testid that is represented by a key', () => {
      renderLCM();
      expect(screen.getByTestId(`cta-background-link-${linkCTAProps.background!.className}`)).toBeInTheDocument();
    });

    test('should render a mobile image at mobile breakpoints', () => {
      renderLCM();
      expect(screen.getByAltText(linkCTAProps.background!.image!.alt!)).toHaveAttribute('src', linkCTAProps.background!.image!.srcUrl);
    });

    test('should render a desktop image at LARGE breakpoints', () => {
      renderLCM({ breakpoint: LARGE });
      expect(screen.getByAltText(linkCTAProps.background!.image!.alt!)).toHaveAttribute('src', linkCTAProps.background?.image?.desktopSrcUrl);
    });

    test('should render a link around background image if provided', () => {
      renderLCM();

      const linkElement = screen.getByTitle((linkCTAProps.background!.linkData as LinkProps)!.title!);

      const backgroundImageElement = screen.getByAltText(linkCTAProps.background!.image!.alt!);
      expect(linkElement).toContainElement(backgroundImageElement);
    });

    test('should render an aria-label attribute to the background link when provided one', () => {
      renderLCM();
      const linkElement = screen.getByTitle((linkCTAProps.background!.linkData as LinkProps).title!);
      expect(linkElement).toHaveAttribute('aria-label', (linkCTAProps.background!.linkData as LinkProps)['aria-label']);
    });
  });

  describe('Overlay', () => {
    describe('ImageOverlay', () => {
      test('should render a mobile image at mobile breakpoints', () => {
        renderLCM();
        expect(screen.getByAltText(linkCTAProps.background!.image!.alt!)).toHaveAttribute('src', linkCTAProps.background!.image!.srcUrl);
      });

      test('should use a mobile image at LARGE breakpoints if no desktop image provided', () => {
        const props = {
          overlay: {
            alt: 'Overlay',
            srcUrl: 'mobile.svg',
          },
          background: {
            image: {
              alt: 'Background',
              srcUrl: 'mobile.svg',
              desktopSrcUrl: 'desktop.svg',
            },
          },
        };
        renderLCM({ props, breakpoint: LARGE });
        const overlayImageElement = screen.getByAltText(props.overlay.alt!);
        expect(overlayImageElement).toHaveAttribute('src', props.overlay.srcUrl);
      });

      test('should render a desktop image at LARGE breakpoints', () => {
        renderLCM({ breakpoint: LARGE });
        const overlayImageElement = screen.getByAltText((linkCTAProps.overlay as Image)!.alt!);
        expect(overlayImageElement).toHaveAttribute('src', (linkCTAProps.overlay as Image)!.desktopSrcUrl);
      });
    });

    describe('TextOverlay', () => {
      const baseBackground = {
        background: {
          image: {
            alt: 'Background',
            srcUrl: 'mobile.svg',
            desktopSrcUrl: 'desktop.svg',
          },
        },
      };
      test('should render a text node when provided', () => {
        const props = merge({}, baseBackground, {
          overlay: {
            text: 'This is a text node!',
          },
        });
        renderLCM({ props });
        expect(screen.getByText(props.overlay.text)).toBeInTheDocument();
      });

      test('should render multiple text nodes when provided', () => {
        const props = merge({}, baseBackground, {
          overlay: {
            children: [{ text: 'Text Node 1' }, { text: 'Text Node 2' }],
          },
        });
        renderLCM({ props });
        const child1Text = props.overlay.children[0].text;
        const child2Text = props.overlay.children[1].text;
        expect(screen.getByText(child1Text)).toBeInTheDocument();
        expect(screen.getByText(child2Text)).toBeInTheDocument();
      });

      test('should render nested text nodes within their own text node container', () => {
        const props = merge({}, baseBackground, {
          overlay: {
            children: [{ text: 'Text Node 1' }, { text: 'Text Node 2' }, { children: [{ text: 'Nested Text Node' }] }],
          },
        });
        renderLCM({ props });
        const childTextNode1 = props.overlay.children[0].text || '';
        const nestedText = props.overlay.children[2].children?.[0]?.text || '';
        expect(screen.getByText(nestedText).parentElement).not.toContainElement(screen.getByText(childTextNode1));
      });

      describe('with PersonalizationContext.Provider', () => {
        test('should render template value to text nodes when personalization is provided', () => {
          const props = merge({}, baseBackground, {
            overlay: {
              children: [{ text: 'Hi {{FIRST_NAME}}' }, { text: 'Text Node 2' }],
            },
          });

          renderWithPersonalization({
            props,
            context: {
              ...defaultPersonalizationData,
              firstName: 'Adam',
            } as unknown as PersonalizationContextData,
          });

          const child1Text = 'Hi Adam';
          const child2Text = props.overlay.children[1].text;
          expect(screen.getByText(child1Text)).toBeInTheDocument();
          expect(screen.getByText(child2Text)).toBeInTheDocument();
        });

        test('should fail to render template value when personalization is not provided', () => {
          const props = merge({}, baseBackground, {
            overlay: {
              children: [{ text: 'Hi {{FIRST_NAME}}' }, { text: 'Text Node 2' }],
            },
          });

          renderWithPersonalization({
            props,
          });

          const child1Text = 'Hi';
          const child2Text = props.overlay.children[1].text;

          expect(screen.getByText(child1Text)).toBeInTheDocument();
          expect(screen.getByText(child2Text)).toBeInTheDocument();
        });
      });
    });
  });

  describe('background image with jump link', () => {
    test('should render a jumplink with hash in URL when provided', async () => {
      const props: LayeredContentModuleProps = {
        container: {
          style: {
            position: 'relative',
          },
        },
        ctaList: {
          style: {
            position: 'absolute',
            top: 0,
            height: '100%',
            width: '100%',
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
          },
          className: '',
          ctas: [
            {
              composableButtonData: { children: 'primary button' },
              linkData: {
                isAJumplink: true,
                jumplinkCSSSelector: '.jump-to-me',
              },
            },
          ],
        },
        overlay: {
          alt: 'Image with the following text: This is An Overlay',
          srcUrl: 'www.mobile.gap.com',
          desktopSrcUrl: 'www.desktop.gap.com',
        },
        background: {
          linkData: {
            isAJumplink: true,
            jumplinkCSSSelector: '.jump-to-me',
            title: 'Link to hash',
            to: '#hash-goes-here',
          },
          image: {
            alt: 'Image of a fabric as background',
            srcUrl: 'www.mobile.gap.com',
            desktopSrcUrl: 'www.desktop.gap.com',
          },
        },
      };

      renderLCM({ props });
      const linkElement = screen.getByTitle((props.background!.linkData as LinkProps)!.title!);
      expect(linkElement).toHaveAttribute('href', (props.background!.linkData as LinkProps)!.to!);
      const getImgText = props!.background!.image!.alt!;
      await act(async () => {
        fireEvent.click(screen.getByAltText(getImgText));
      });
      expect(jumpToSection).toHaveBeenCalledWith(true, '.jump-to-me');
    });
  });

  describe('a11y', () => {
    it('should not have a11y violations', async () => {
      const { container } = renderLCM();
      expect(await axe(container)).toHaveNoViolations();
    });
  });

  describe('with detailsLink', () => {
    test('should render a details link with prefix when provided', () => {
      const props = { ...withDetailsLinkWithPrefixProps };
      renderLCM({ props });
      expect(
        screen.getByRole('button', {
          name: 'Details',
        })
      ).toBeInTheDocument();
      expect(screen.getByText(props.detailsLink.prefix)).toBeInTheDocument();
    });
  });

  describe('with desktopTo', () => {
    test('should use the desktopTo link when provided and in Desktop BP', () => {
      const props = { ...withDesktopToProps };
      renderLCM({ props, breakpoint: LARGE });
      const linkElement = screen.getByTitle((props.background!.linkData as CombinedLinkData)!.title!);
      expect(linkElement).toHaveAttribute(
        'href',
        (props.background!.linkData as CombinedLinkData & {
          desktopTo?: string;
        })!.desktopTo!
      );
    });
  });
});
