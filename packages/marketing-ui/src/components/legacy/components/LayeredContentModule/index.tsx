// @ts-nocheck
'use client';
import React, { useContext } from 'react';
import { LARGE, BreakpointContext } from '@ecom-next/core/breakpoint-provider';
import { Brands, CSSObject, useTheme } from '@ecom-next/core/react-stitch';
import { ComposableButton } from '../ComposableButton';
import { mapDataToProps } from '../../helper';
import { ButtonDropdown } from '../ButtonDropdown';
import { DetailsLinkWithPrefix } from '../DetailsLinkWithPrefix';
import { jumpToSection, JumpLinkProps } from '../../helper/jumpToSection';
import { BackgroundImage, BackgroundImgContainer, BackgroundLink, CTAList, OverlayImage, TextNodesContainer } from './components';
import { LayeredContentModuleProps, isLinkCTA, isModalCTA, isTextNodesContainer, isTextNode, CombinedLinkData } from './types';
import { TextNode } from './components/TextNodesContainer';

export const LayeredContentModule = ({ background, container, ctaList, detailsLink, overlay }: LayeredContentModuleProps): JSX.Element => {
  const { minWidth } = useContext(BreakpointContext);
  const isDesktopSize = minWidth(LARGE);
  const theme = useTheme();
  const isGapGapfs = theme.brand === Brands.Gap || theme.brand === Brands.GapFactoryStore;
  const isAT = theme.brand === Brands.Athleta;
  const renderDetailsLinkWithPrefix = (): JSX.Element | null => (detailsLink ? <DetailsLinkWithPrefix {...detailsLink} /> : null);

  const renderCTAList = (): JSX.Element | null => {
    if (!ctaList) return null;
    const ctaArray = ctaList.ctas;
    const defaultCTAStyles: CSSObject = {
      zIndex: 30,
      borderColor: 'transparent',
    };
    const defaultCTAStylesAT: CSSObject = isAT ? { maxHeight: 'unset' } : {};
    const defaultCTAStylesGap_GapFS: CSSObject = isGapGapfs ? { minHeight: 0 } : {};
    const defaultGapFSWhiteSpace: CSSObject = isGapGapfs ? { whiteSpace: 'nowrap' } : {};

    const ctaArrayMap = ctaArray.map((cta, index) => {
      const setKeyId = index.toString();
      if (isLinkCTA(cta)) {
        const { className, composableButtonData, linkData } = cta;

        return (
          <ComposableButton
            key={`cta-button-dropdown-link-${setKeyId}`}
            className={className}
            css={{
              ...defaultCTAStyles,
              ...defaultCTAStylesGap_GapFS,
              ...defaultGapFSWhiteSpace,
              ...defaultCTAStylesAT,
            }}
            data-testid={`cta-button-dropdown-link-${setKeyId}`}
            {...composableButtonData}
            linkProps={linkData}
          />
        );
      }

      if (isModalCTA(cta)) {
        const {
          composableButtonData,
          modalData: { iframeData, ...modalData },
        } = cta;

        return (
          <ComposableButton
            key={`cta-button-dropdown-modal-${setKeyId}`}
            css={{ ...defaultCTAStyles, ...defaultCTAStylesGap_GapFS }}
            data-testid={`cta-button-dropdown-modal-${setKeyId}`}
            {...composableButtonData}
            modalProps={{ ...iframeData, ...modalData }}
          />
        );
      }

      const { buttonDropdownData, desktopStyle, style } = cta;
      buttonDropdownData.style = buttonDropdownData.style || { mobile: {} };
      const { mobile } = buttonDropdownData.style;
      buttonDropdownData.style.mobile = { ...defaultGapFSWhiteSpace, ...mobile };

      return (
        <ButtonDropdown
          key={`cta-button-dropdown-${setKeyId}`}
          data-testid={`cta-button-dropdown-${setKeyId}`}
          style={{ desktop: desktopStyle, mobile: style }}
          {...buttonDropdownData}
        />
      );
    });

    const { className, style, desktopStyle } = ctaList;
    return (
      <CTAList key='cta-list' className={className} css={[style, isDesktopSize && desktopStyle]}>
        {ctaArrayMap}
      </CTAList>
    );
  };

  const renderOverlay = (): JSX.Element | null => {
    if (!overlay) return null;

    if (isTextNodesContainer(overlay)) return <TextNodesContainer {...overlay} isDesktopSize={isDesktopSize} />;

    if (isTextNode(overlay)) return <TextNode {...overlay} isDesktopSize={isDesktopSize} />;

    const { alt, className, srcUrl, desktopSrcUrl, style, desktopStyle } = overlay;

    return <OverlayImage alt={alt} className={className} css={[style, isDesktopSize && desktopStyle]} src={(isDesktopSize && desktopSrcUrl) || srcUrl} />;
  };

  const renderBackgroundImage = (): JSX.Element | null => {
    if (!background?.image) return null;
    const { alt, desktopSrcUrl, desktopStyle, srcUrl, style } = background.image;
    return <BackgroundImage alt={alt} css={[style, isDesktopSize && desktopStyle]} src={(isDesktopSize && desktopSrcUrl) || srcUrl} />;
  };

  const renderBackground = (): JSX.Element | null => {
    if (!background) return null;
    const { className, desktopStyle, linkData, noLinkDataStyle, style } = background;

    const BackgroundWrapper = ({ children, linkData }: { linkData?: CombinedLinkData; children: React.ReactNode }): JSX.Element =>
      linkData && Object.prototype.hasOwnProperty.call(linkData, 'to') ? (
        <BackgroundLink
          data-testid={`cta-background-link-${className}`}
          {...linkData}
          to={isDesktopSize && linkData.desktopTo ? linkData.desktopTo : linkData.to}
        >
          {children}
        </BackgroundLink>
      ) : (
        <div css={noLinkDataStyle}>{children}</div>
      );

    const isJumplink = linkData && (linkData as JumpLinkProps) && Object.prototype.hasOwnProperty.call(linkData, 'isAJumplink');
    const jumpLinkSelector =
      (isJumplink && (linkData as JumpLinkProps) && ((linkData as JumpLinkProps)?.jumplinkCSSSelector as JumpLinkProps['jumplinkCSSSelector'])) || '';
    const bgImgPointerCssRule = (isJumplink && { cursor: 'pointer' }) || {};

    return (
      <BackgroundWrapper key={`cta-background-wrapper-${className}`} linkData={linkData}>
        <BackgroundImgContainer
          className={className}
          css={[style, isDesktopSize && desktopStyle, isJumplink && bgImgPointerCssRule]}
          onClick={() => {
            isJumplink && jumpToSection(isJumplink, jumpLinkSelector);
          }}
        >
          {renderBackgroundImage()}
          {renderOverlay()}
          {renderDetailsLinkWithPrefix()}
        </BackgroundImgContainer>
      </BackgroundWrapper>
    );
  };

  return (
    <div className={container?.className} css={[container?.style, isDesktopSize && container?.desktopStyle]}>
      {[renderBackground(), renderCTAList()]}
    </div>
  );
};

export default mapDataToProps(LayeredContentModule);
export type { LayeredContentModuleProps, ModalCTAProps, TextNode, TextNodesContainer, ButtonDropdownCTAProps, LinkCTAProps, Image } from './types';
