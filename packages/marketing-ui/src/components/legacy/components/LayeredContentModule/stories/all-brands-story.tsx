// @ts-nocheck
'use client';
import React from 'react';
import { StoryFn } from '@storybook/react';
import LayeredContentModule from '..';
import README from '../README.mdx';
import buttonDropdownData from './data/button-dropdown';
import modalData from './data/modal';
import buttonLinksOnlyHover from './data/buttons-only-hover';

export default {
  title: 'Common/JSON Components (Marketing)/LayeredContentModule',
  parameters: {
    knobs: { disable: true },
    docs: {
      page: README,
    },
  },
  tags: ['exclude'],
};

const LCMTemplate: StoryFn<typeof LayeredContentModule> = args => (
  <div>
    <LayeredContentModule {...args} />
  </div>
);

// At time of writing, only LayeredContentModule CTAs have any branded styles.
// The rest of LCM test need only 1 brand for testing.
export const OverlayWithButtonDropdown = LCMTemplate.bind({});
OverlayWithButtonDropdown.args = { ...buttonDropdownData };

export const OverlayWithModal = LCMTemplate.bind({});
OverlayWithModal.args = { ...modalData };

export const ButtonLinksWithHoverStates = LCMTemplate.bind({});
ButtonLinksWithHoverStates.args = { ...buttonLinksOnlyHover };
