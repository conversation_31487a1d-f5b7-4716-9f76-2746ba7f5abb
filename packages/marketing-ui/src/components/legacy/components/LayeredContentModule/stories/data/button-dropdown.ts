// @ts-nocheck
'use client';
import { LayeredContentModuleProps } from '../..';
import overlaySrc from '../assets/overlay.svg';
import overlayMobileSrc from '../assets/overlay-mobile.svg';
import backgroundImgSrc from '../assets/background.jpg';
import backgroundMobileImgSrc from '../assets/background-mobile.jpg';

const storyData: LayeredContentModuleProps = {
  container: {
    style: {
      position: 'relative',
    },
  },
  ctaList: {
    style: {
      position: 'absolute',
      top: '50%',
      display: 'flex',
      flexDirection: 'row',
      width: '275px',
      left: '50%',
      transform: 'translate(-50%)',
    },
    desktopStyle: {
      top: '40%',
    },
    className: '',
    ctas: [
      {
        buttonDropdownData: {
          heading: {
            text: 'This is a dropdown',
          },
          style: {
            desktop: {},
            mobile: {},
          },
          submenu: [
            {
              href: '/',
              target: '_blank',
              text: 'Link 1 - Same Page target',
            },
            {
              href: '/',
              target: '_blank',
              text: 'Link 2 - Same Page target',
            },
            {
              href: '/',
              target: '_blank',
              text: 'Link 3 - Same Page target',
            },
            {
              href: '/',
              target: '_blank',
              text: 'Link 4 - Same Page target',
            },
            {
              href: '/',
              target: '_blank',
              text: 'Link 5 - Same Page target',
            },
          ],
        },
      },
    ],
  },
  overlay: {
    alt: 'Image with the following text: This is An Overlay',
    srcUrl: overlayMobileSrc,
    desktopSrcUrl: overlaySrc,
  },
  background: {
    image: {
      alt: 'Image of a fabric as background',
      srcUrl: backgroundMobileImgSrc,
      desktopSrcUrl: backgroundImgSrc,
    },
  },
};

export default storyData;
