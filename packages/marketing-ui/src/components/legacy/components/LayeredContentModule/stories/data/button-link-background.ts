// @ts-nocheck
'use client';
import { LayeredContentModuleProps } from '../../types';
import overlaySrc from '../assets/overlay.svg';
import overlayMobileSrc from '../assets/overlay-mobile.svg';
import backgroundImgSrc from '../assets/background.jpg';
import backgroundMobileImgSrc from '../assets/background-mobile.jpg';

const storyData: LayeredContentModuleProps = {
  container: {
    style: {
      position: 'relative',
    },
  },
  ctaList: {
    style: {
      position: 'absolute',
      top: 0,
      height: '100%',
      width: '100%',
      display: 'flex',
      justifyContent: 'center',
      alignItems: 'center',
    },
    className: '',
    ctas: [
      {
        composableButtonData: { children: 'primary button' },
        linkData: {
          target: '_blank',
          to: 'about:blank#button-link',
          title: 'primary button',
        },
      },
    ],
  },
  overlay: {
    alt: 'Image with the following text: This is An Overlay',
    srcUrl: overlayMobileSrc,
    desktopSrcUrl: overlaySrc,
  },
  background: {
    linkData: {
      to: 'about:blank#background-link',
      target: '_blank',
      title: 'background link',
    },
    image: {
      alt: 'Image of a fabric as background',
      srcUrl: backgroundMobileImgSrc,
      desktopSrcUrl: backgroundImgSrc,
    },
  },
};

export default storyData;
