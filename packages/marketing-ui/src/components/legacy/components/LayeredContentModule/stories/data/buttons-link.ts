// @ts-nocheck
'use client';
import { Color } from '../../../ComposableButton/types';
import { LayeredContentModuleProps } from '../../types';
import overlaySrc from '../assets/overlay.svg';
import overlayMobileSrc from '../assets/overlay-mobile.svg';
import backgroundImgSrc from '../assets/background.jpg';
import backgroundMobileImgSrc from '../assets/background-mobile.jpg';

const storyData: LayeredContentModuleProps = {
  container: {
    style: {
      position: 'relative',
    },
  },
  ctaList: {
    style: {
      position: 'absolute',
      top: 0,
      height: '100%',
      width: '100%',
      display: 'flex',
      justifyContent: 'center',
      alignItems: 'center',
      flexDirection: 'column',
    },
    desktopStyle: {
      flexDirection: 'row',
    },
    className: '',
    ctas: [
      {
        composableButtonData: {
          children: 'button 1',
          style: { margin: '5px' },
        },
        linkData: {
          target: '_blank',
          to: 'about:blank',
          title: 'link #1',
        },
      },
      {
        composableButtonData: {
          children: 'button 2',
          color: 'white' as Color,
          style: { margin: '5px' },
        },
        linkData: {
          target: '_blank',
          to: 'about:blank',
          title: 'link #2',
        },
      },
    ],
  },
  overlay: {
    alt: 'Image with the following text: This is An Overlay',
    srcUrl: overlayMobileSrc,
    desktopSrcUrl: overlaySrc,
  },
  background: {
    image: {
      alt: 'Image of a fabric as background',
      srcUrl: backgroundMobileImgSrc,
      desktopSrcUrl: backgroundImgSrc,
    },
  },
};

export default storyData;
