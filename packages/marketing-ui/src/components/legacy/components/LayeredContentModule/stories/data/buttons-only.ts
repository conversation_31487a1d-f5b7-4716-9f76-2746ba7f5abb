// @ts-nocheck
'use client';
import { LayeredContentModuleProps } from '../../types';

const buttonLinksOnly: LayeredContentModuleProps = {
  ctaList: {
    style: {
      position: 'absolute',
      top: 0,
      height: '100%',
      width: '100%',
      display: 'flex',
      justifyContent: 'center',
      alignItems: 'center',
      flexDirection: 'column',
      '& a': { margin: '0.5rem' },
    },
    desktopStyle: {
      flexDirection: 'row',
    },
    ctas: [
      {
        composableButtonData: {
          children: 'Link 1',
        },
        linkData: {
          target: '_blank',
          to: 'about:blank',
          title: 'Link 1',
        },
      },
      {
        composableButtonData: {
          children: 'Link 2',
        },
        linkData: {
          target: '_blank',
          to: 'about:blank',
          title: 'Link 2',
        },
      },
      {
        composableButtonData: {
          children: 'Link 3',
        },
        linkData: {
          target: '_blank',
          to: 'about:blank',
          title: 'Link 3',
        },
      },
    ],
  },
};

export default buttonLinksOnly;
