// @ts-nocheck
'use client';
import { LayeredContentModuleProps } from '../../types';
import backgroundImgSrc from '../assets/background.jpg';
import backgroundMobileImgSrc from '../assets/background-mobile.jpg';

const storyData: LayeredContentModuleProps = {
  container: {
    style: {
      position: 'relative',
    },
  },
  ctaList: {
    style: {
      position: 'absolute',
      top: 0,
      height: '100%',
      width: '100%',
      display: 'flex',
      justifyContent: 'center',
      alignItems: 'center',
    },
    className: '',
    ctas: [
      {
        composableButtonData: { children: 'primary button' },
        linkData: {
          target: '_blank',
          to: 'about:blank',
          title: 'link',
        },
      },
    ],
  },
  detailsLink: {
    prefix: 'exclusions apply.',
    text: '*Details',
    detailsLinkUrl: 'url',
    closeButtonAriaLabel: 'close aria label',
    prefixStyles: {
      fontSize: '10px',
      fontWeight: 'normal',
      padding: '0',
      minHeight: '0',
      border: 'none',
      textTransform: 'none',
      marginRight: '10px',
      color: 'white',
    },
    linkStyles: {
      fontSize: '10px',
      color: 'white',
    },
    modalSize: 'standard',
    title: 'Modal Title',
    wrapperStyles: {
      position: 'absolute',
      right: '2%',
      bottom: '5%',
    },
  },
  overlay: {
    children: [
      {
        tag: 'div',
        text: 'Text Configured with a <div> tag',
      },
      { text: 'Text Configured Via JSON' },
      {
        children: [{ text: 'This Text Too' }, { text: 'And Also This!' }],
        style: {
          display: 'flex',
          flexDirection: 'row',
          fontSize: '5vw',
          justifyContent: 'space-around',
          width: '100%',
        },
      },
      { text: 'Text Configured Via JSON' },
    ],
    color: 'white',
    style: {
      position: 'absolute',
      top: '0',
      display: 'flex',
      flexDirection: 'column',
      alignItems: 'center',
      width: '100%',
      height: '100%',
      fontSize: '2.5vw',
      justifyContent: 'space-around',
    },
  },
  background: {
    image: {
      alt: 'Image of a fabric as background',
      srcUrl: backgroundMobileImgSrc,
      desktopSrcUrl: backgroundImgSrc,
    },
  },
};

export default storyData;
