// @ts-nocheck
'use client';
import { LayeredContentModuleProps } from '../../types';
import backgroundImgSrc from '../assets/image-map-desktop.jpg';
import backgroundMobileImgSrc from '../assets/image-map-mobile.jpg';

const storyData: LayeredContentModuleProps = {
  container: {
    style: {
      position: 'relative',
    },
  },
  ctaList: {
    style: {
      position: 'absolute',
      top: 0,
      height: '100%',
      width: '100%',
      display: 'flex',
      justifyContent: 'space-around',
      flexDirection: 'column',
      alignItems: 'center',
    },
    desktopStyle: {
      flexDirection: 'row',
    },
    className: '',
    ctas: [
      {
        composableButtonData: {
          children: 'link #1',
          style: {
            position: 'absolute',
            top: '0',
            left: '0',
            height: '50%',
            width: '100%',
            backgroundColor: 'rgba(0, 0, 255, 0.2)',
          },
          desktopStyle: {
            height: '100%',
            width: '50%',
          },
        },
        linkData: {
          target: '_blank',
          to: 'about:blank#link1',
          title: 'left side image map',
        },
      },
      {
        composableButtonData: {
          children: 'link #2',
          style: {
            position: 'absolute',
            top: '50%',
            left: '0',
            height: '50%',
            width: '100%',
            backgroundColor: 'rgba(255, 0, 0, 0.2)',
          },
          desktopStyle: {
            top: '0',
            left: '50%',
            height: '100%',
            width: '50%',
          },
        },
        linkData: {
          target: '_blank',
          to: 'about:blank#link2',
          title: 'right side image map',
        },
      },
    ],
  },
  background: {
    image: {
      alt: 'Image of a fabric as background',
      srcUrl: backgroundMobileImgSrc,
      desktopSrcUrl: backgroundImgSrc,
    },
  },
};

export default storyData;
