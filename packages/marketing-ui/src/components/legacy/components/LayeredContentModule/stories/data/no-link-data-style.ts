// @ts-nocheck
'use client';
import { LayeredContentModuleProps } from '../../types';
import overlaySrc from '../assets/overlay.svg';
import overlayMobileSrc from '../assets/overlay-mobile.svg';
import backgroundImgSrc from '../assets/background.jpg';
import backgroundMobileImgSrc from '../assets/background-mobile.jpg';

const storyData: LayeredContentModuleProps = {
  overlay: {
    alt: 'Image with the following text: This is An Overlay',
    srcUrl: overlayMobileSrc,
    desktopSrcUrl: overlaySrc,
  },
  background: {
    noLinkDataStyle: {
      display: 'flex',
    },
    image: {
      alt: 'Image of a fabric as background',
      srcUrl: backgroundMobileImgSrc,
      desktopSrcUrl: backgroundImgSrc,
    },
  },
};

export default storyData;
