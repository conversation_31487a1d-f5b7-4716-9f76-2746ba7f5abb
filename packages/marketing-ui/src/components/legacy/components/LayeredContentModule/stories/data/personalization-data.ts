// @ts-nocheck
'use client';
import backgroundImgSrc from '../assets/credit-offer-banner.png';

export const personalizationDataLoggedIn = {
  shoppingBag: {
    totalItemCount: 2,
    totalItemAmount: 26,
  },
  userContext: {
    isAnonymousUser: false,
    isRecognizedUser: false,
    isLoggedInUser: true,
    globalShippingCountryCode: 'us',
    globalShippingCountryName: 'United States',
    localeCode: 'en_US',
    clickStream: {
      sitesVisited: '1',
      viewAll: '1',
      isFilteredTabActive: false,
      searchFacetSelectionsMap: null,
      cipher: 'bf-cbc',
      vector: '37ae5213fa8968b4',
    },
    viewDate: '2018-06-20T15:16:20.349-04:00',
    reportingAccount: 'gapproduction,gapgidproduction',
    campaignTrackingId: '',
    preferredLocaleCode: '',
    guest: false,
    userEmailEncrypt: '2451197ee65afb44076ce2653c6cc19d72feb3853b5e780862fb80629978cd982068e7c4d59993c532b8c0ece1bd632a',
    userEmailMMEncrypt: '66f19c565f05fcf8392ad5e7066f20b351011c15',
    customerIdEncrypted: '1413e69f4405b335ea6dfebf10179b3394b4dc98',
    maskedEmailAddress: 'a*****@nisum.com',
    isEligibleForQuickCheckout: false,
  },
  firstName: 'Adam',
  isLoggedInUser: true,
  isRecognizedUser: false,
  internationalShipping: {
    countryName: '',
    countryFlagURL: 'us.gif',
  },
  virtualValueInterrupterStatus: {
    bouncebackActive: false,
    rewardsActive: false,
    bouncebackText: '',
  },
  virtualValueMessage: '',
};

export const creditOffer = {
  container: {
    style: {
      position: 'relative',
      color: '#367EBE',
    },
  },
  ctaList: {
    style: {
      position: 'relative',
      height: '100%',
      width: '100%',
      display: 'flex',
      justifyContent: 'center',
      alignItems: 'center',
      top: '-55px',
    },
    className: '',
    ctas: [
      {
        composableButtonData: {
          children: 'apply now',
          color: 'primary',
          variant: 'border',
          interactiveStyles: true,
          css: { width: '30vw' },
        },
        linkData: {
          target: '_blank',
          to: 'about:blank',
          title: 'link',
        },
      },
    ],
  },
  overlay: {
    children: [
      {
        tag: 'div',
        text: 'Hi {{FIRST_NAME}}',
        style: {
          color: '#367EBE',
          position: 'relative',
          top: '-20px',
        },
      },
      {
        tag: 'div',
        text: 'You’re Pre-approved',
        style: {
          textTransform: 'uppercase',
          fontSize: '1.95vw',
          position: 'relative',
          color: '#367EBE',
          textAlign: 'center',
        },
      },
      {
        children: [
          {
            tag: 'div',
            text: '{{FIRST_NAME}} starting today when you',
            color: 'primary',
          },
        ],
        style: {
          position: 'relative',
          top: '-95px',
          fontSize: '2vw',
          fontWeight: '400',
          color: '#367EBE',
        },
      },
      {
        children: [
          {
            text: 'details',
          },
        ],
        style: {
          position: 'relative',
          top: '-12px',
          textTransform: 'uppercase',
          fontSize: '1.75vw',
          fontWeight: '300',
        },
      },
    ],
    style: {
      position: 'absolute',
      top: '0',
      display: 'flex',
      flexDirection: 'column',
      alignItems: 'center',
      width: '100%',
      height: '100%',
      fontSize: '2.5vw',
      justifyContent: 'space-around',
    },
  },
  background: {
    image: {
      alt: 'Image of a fabric as background',
      srcUrl: backgroundImgSrc,
      desktopSrcUrl: backgroundImgSrc,
    },
  },
};
