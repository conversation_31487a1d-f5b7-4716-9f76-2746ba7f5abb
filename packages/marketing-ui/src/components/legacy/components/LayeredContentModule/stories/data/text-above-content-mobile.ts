// @ts-nocheck
'use client';
import { LayeredContentModuleProps } from '../../types';
import backgroundImgSrc from '../assets/background.jpg';
import backgroundMobileImgSrc from '../assets/background-mobile.jpg';

const storyData: LayeredContentModuleProps = {
  overlay: {
    children: [{ text: 'Text Configured Via JSON' }],
    color: 'white',
    style: {
      position: 'relative',
      background: 'black',
      top: '0',
      display: 'flex',
      flexDirection: 'column',
      alignItems: 'center',
      width: '100%',
      height: '100%',
      fontSize: '3vw',
      justifyContent: 'space-around',
    },
    desktopStyle: {
      position: 'absolute',
      background: 'none',
    },
  },
  background: {
    image: {
      alt: 'Image of a fabric as background',
      srcUrl: backgroundMobileImgSrc,
      desktopSrcUrl: backgroundImgSrc,
    },
    style: {
      position: 'relative',
      display: 'flex',
      flexDirection: 'column-reverse',
    },
  },
};

export default storyData;
