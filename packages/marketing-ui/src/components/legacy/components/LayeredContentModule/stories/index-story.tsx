// @ts-nocheck
'use client';
import React from 'react';
import { PersonalizationContext } from '@ecom-next/sitewide/personalization-provider';
import type { PersonalizationContextData } from '@ecom-next/core/legacy/personalization-provider/types';
import { CSSObject } from '@ecom-next/core/react-stitch';
import { StoryFn } from '@storybook/react';
import LayeredContentModule from '..';
import README from '../README.mdx';
import buttonLinkData from './data/button-link';
import buttonJumpLinkData from './data/button-jumplink';
import buttonLinkBackgroundData from './data/button-link-background';
import buttonsLinkData from './data/buttons-link';
import basicLayoutData from './data/basic-layout';
import noLinkData from './data/no-link-data-style';
import imageMappingData from './data/image-mapping';
import textOverlayData from './data/text-overlay';
import buttonLinksOnly from './data/buttons-only';
import textAboveContent from './data/text-above-content-mobile';
import imageBackgroundWithJumpLinkData from './data/image-jumplink';
import detailsLinkData from './data/details-link';
import desktopToData from './data/desktop-to';
import { personalizationDataLoggedIn, creditOffer } from './data/personalization-data';

export default {
  title: 'Common/JSON Components (Marketing)/LayeredContentModule',
  parameters: {
    knobs: { disable: true },
    docs: {
      page: README,
    },
  },
  tags: ['exclude'],
};

const defaultPersonalizationData = {
  isLoading: true,
  isEmpty: true,
  isError: false,
  personalizationReferrerInfo: {},
  shoppingBag: {
    totalItemCount: 0,
  },
};

const withPersonalizationProvider: StoryFn<typeof LayeredContentModule> = args => (
  <PersonalizationContext.Provider
    // @ts-ignore
    value={{
      ...(args.enablePersonalization ? (args.personalizationData as PersonalizationContextData) : defaultPersonalizationData),
      ...args.data,
      isEmpty: false,
      isError: false,
      isLoading: false,
    }}
  >
    <div css={args.style as CSSObject}>
      <LayeredContentModule {...args} />
    </div>
  </PersonalizationContext.Provider>
);

const LCMTemplate: StoryFn<typeof LayeredContentModule> = args => (
  <div>
    <LayeredContentModule {...args} />
  </div>
);

const LCMJumpLinkTemplate: StoryFn<typeof LayeredContentModule> = args => (
  <div>
    <LayeredContentModule {...args} />
    <div className='jump-to-me' css={{ marginTop: '50em', width: '100%', textAlign: 'center' }}>
      Jump To Me Section
    </div>
  </div>
);

export const BackgroundImageWithJumpLink = LCMJumpLinkTemplate.bind({});
BackgroundImageWithJumpLink.args = {
  ...imageBackgroundWithJumpLinkData,
};

export const BasicSVGOverlay = LCMTemplate.bind({});
BasicSVGOverlay.args = {
  ...basicLayoutData,
};

export const WithDetailsLink = LCMTemplate.bind({});
WithDetailsLink.args = {
  ...detailsLinkData,
};

export const WithOptionalDesktopLink = LCMTemplate.bind({});
WithOptionalDesktopLink.args = {
  ...desktopToData,
};

export const OverlayWithNoLinkDataStyle = LCMTemplate.bind({});
OverlayWithNoLinkDataStyle.args = {
  ...noLinkData,
};

export const OverlayWithButtonLink = LCMTemplate.bind({});
OverlayWithButtonLink.args = { ...buttonLinkData };

export const OverlayWithButtonJumpLink = LCMJumpLinkTemplate.bind({});
OverlayWithButtonJumpLink.args = { ...buttonJumpLinkData };

export const OverlayWithButtonAndSeparateBackgroundLinks = LCMTemplate.bind({});
OverlayWithButtonAndSeparateBackgroundLinks.args = {
  ...buttonLinkBackgroundData,
};

export const OverlayWithMultipleButtonLinks = LCMTemplate.bind({});
OverlayWithMultipleButtonLinks.args = { ...buttonsLinkData };

export const OverlayWithImageMapping = LCMTemplate.bind({});
OverlayWithImageMapping.args = { ...imageMappingData };

export const OverlayWithText = LCMTemplate.bind({});
OverlayWithText.args = { ...textOverlayData };

export const ButtonLinksOnly = LCMTemplate.bind({});
ButtonLinksOnly.args = { ...buttonLinksOnly };

export const TextAboveContentInMobile = LCMTemplate.bind({});
TextAboveContentInMobile.args = { ...textAboveContent };

export const OverlayWithPersonalizationData = withPersonalizationProvider.bind({});

OverlayWithPersonalizationData.args = {
  data: { ...creditOffer },
  style: {
    width: '296px',
    height: '549px',
  },
  personalizationData: personalizationDataLoggedIn,
  enablePersonalization: false,
};

OverlayWithPersonalizationData.parameters = {
  knobs: { disable: true },
};
