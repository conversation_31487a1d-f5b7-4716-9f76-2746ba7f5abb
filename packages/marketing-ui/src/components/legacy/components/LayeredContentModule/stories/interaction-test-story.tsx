// @ts-nocheck
'use client';
import React from 'react';
import { userEvent, within, expect } from '@storybook/test';
import { StoryFn } from '@storybook/react';
import LayeredContentModule from '..';
import README from '../README.mdx';
import buttonJumpLinkData from './data/button-jumplink';
import imageBackgroundWithJumpLinkData from './data/image-jumplink';

export default {
  title: 'Common/JSON Components (Marketing)/LayeredContentModule/Interaction Tests',
  parameters: {
    knobs: { disable: true },
    docs: {
      page: README,
    },
  },
  tags: [],
};

const LCMJumpLinkTemplate: StoryFn<typeof LayeredContentModule> = args => (
  <div>
    <LayeredContentModule {...args} />
    <div className='jump-to-me' css={{ marginTop: '50em', width: '100%', textAlign: 'center' }}>
      Jump To Me Section
    </div>
  </div>
);

export const BackgroundImageWithJumpLinkInteractionTest = LCMJumpLinkTemplate.bind({});
BackgroundImageWithJumpLinkInteractionTest.args = {
  ...imageBackgroundWithJumpLinkData,
};

// Storybook Test Runner e2e Interaction Test for BackgroundImageWithJumpLink Play function
BackgroundImageWithJumpLinkInteractionTest.play = async ({ canvasElement }) => {
  const canvas = within(canvasElement);
  const link = await canvas.getByText('primary button');
  await userEvent.click(link);

  await expect(canvas.getByText('Jump To Me Section')).toBeInTheDocument();
};

export const OverlayWithButtonJumpLinkInteractionTest = LCMJumpLinkTemplate.bind({});
OverlayWithButtonJumpLinkInteractionTest.args = { ...buttonJumpLinkData };

// Storybook Test Runner e2e Interaction Test for OverlayWithButtonJumpLink Play function
OverlayWithButtonJumpLinkInteractionTest.play = async ({ canvasElement }) => {
  const canvas = within(canvasElement);
  const link = await canvas.getByText('primary button');
  await userEvent.click(link);

  await expect(canvas.getByText('Jump To Me Section')).toBeInTheDocument();
};
