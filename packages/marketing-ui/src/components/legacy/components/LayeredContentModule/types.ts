// @ts-nocheck
'use client';
import { CSSObject } from '@ecom-next/core/react-stitch';
import { ModalProps } from '@ecom-next/core/legacy/modal';
import { Optional } from '@ecom-next/core/legacy/utility';
import { CommonStyleProps } from '../../types';
import { LinkProps } from '../Link';
import { JumpLinkProps } from '../../helper/jumpToSection';
import { ComposableButtonProps } from '../ComposableButton';
import { ButtonDropdownProps } from '../ButtonDropdown';
import { DetailsLinkWithPrefixProps } from '../DetailsLinkWithPrefix';

export type LinkCTAProps = {
  composableButtonData: ComposableButtonProps;
  linkData: LinkProps & JumpLinkProps;
};

export type ButtonDropdownCTAProps = {
  buttonDropdownData: ButtonDropdownProps;
};

export type IFrameProps = {
  iframeData: {
    height?: string;
    src: string;
    title: string;
  };
};

type ModifiedModalProps = Omit<Optional<ModalProps, 'isOpen'>, 'onClose' | 'children'>;

export type MobileFirstStyleProps = CommonStyleProps & {
  className?: string;
};
export type ModalCTAProps = {
  modalData: ModifiedModalProps & IFrameProps;
  composableButtonData: ComposableButtonProps;
};

export type CTA = MobileFirstStyleProps & (LinkCTAProps | ButtonDropdownCTAProps | ModalCTAProps);

export type Image = MobileFirstStyleProps & {
  alt: string;
  desktopSrcUrl?: string;
  srcUrl: string;
};

export type OverlayNode = MobileFirstStyleProps & {
  font?: 'primary' | 'secondary' | 'tertiary';
  color?: 'primary' | 'black' | 'white';
};

export type TextNode = OverlayNode & {
  text: string | JSX.Element;
  tag?: 'div' | 'p' | 'span' | 'h2' | 'h3' | 'h4' | 'h5' | 'h6';
};

export type TextNodesContainer = OverlayNode & {
  children: Array<TextNode | TextNodesContainer>;
};

export type CombinedLinkData = LinkProps & JumpLinkProps & { desktopTo?: string };

export type LayeredContentModuleProps = {
  background?: MobileFirstStyleProps & {
    image?: Image;
    linkData?: CombinedLinkData;
    noLinkDataStyle?: CSSObject;
  };
  container?: MobileFirstStyleProps;
  ctaList?: MobileFirstStyleProps & {
    ctas: CTA[];
  };
  detailsLink?: DetailsLinkWithPrefixProps;
  overlay?: Image | TextNodesContainer | TextNode;
};

export function isLinkCTA(cta: CTA): cta is LinkCTAProps {
  return (cta as LinkCTAProps).linkData !== undefined;
}

export function isModalCTA(cta: CTA): cta is ModalCTAProps {
  return (cta as ModalCTAProps).modalData !== undefined;
}

export function isTextNodesContainer(overlay: LayeredContentModuleProps['overlay']): overlay is TextNodesContainer {
  return (overlay as TextNodesContainer).children !== undefined;
}

export function isTextNode(overlay: LayeredContentModuleProps['overlay']): overlay is TextNode {
  return (overlay as TextNode).text !== undefined;
}
