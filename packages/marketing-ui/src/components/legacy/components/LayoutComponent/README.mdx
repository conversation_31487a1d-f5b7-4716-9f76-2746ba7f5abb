## LayoutComponent

The goal of the LayoutComponent is to allow flexible composition of components. The LayoutComponent accepts core-ui components that are JSON configurable.

If right now you need,

<div>
<table>
  <tbody>
    <tr>
      <td colspan="3" align="center"> SVGOverlay</td>
    </tr>
    <tr>
      <td> SVGOverlay</td>
      <td> SVGOverlay</td>
      <td> SVGOverlay</td>
    </tr>
  </tbody>
</table>

but sometimes you need this,

<table>
  <tbody>
    <tr>
      <td> SVGOverlay</td>
      <td> SVGOverlay</td>
      <td> SVGOverlay</td>
    </tr>
    <tr>
      <td colspan="3" align="center"> SVGOverlay</td>
    </tr>
    <tr>
      <td colspan="3" align="center"> SVGOverlay</td>
    </tr>
  </tbody>
</table>
</div>

and other times you need this,

<div>
  <table>
    <tbody>
      <tr>
        <td colspan='3' align='center'>
          {' '}
          SVGOverlay
        </td>
      </tr>
      <tr>
        <td> SVGOverlay</td>
        <td> SVGOverlay</td>
        <td> SVGOverlay</td>
      </tr>
      <tr>
        <td colspan='3' align='center'>
          {' '}
          SVGOverlay
        </td>
      </tr>
    </tbody>
  </table>
</div>

You can achieve all of these using LayoutComponents

<a href='#exampleJsons' id='topExamples'>
  See what the configs would look like for these examples
</a>

## Usage

- Horizontally or vertically arrange a group of components (Note: use "flexDirection: column" for vertical components)
- Specify a layout to display in desktop
- Specify a layout to display in mobile

## JSON Config

#### General Format for a LayoutComponent that uses the _same_ layout (i.e., components _and_ styles) for mobile and desktop.

The topmost "data" object should contain a "desktopAndMobile" object if the same components in the same order with the same styles are used in both desktop and mobile.

<details style={{border: "1px solid #afafaf", padding: "5px", borderRadius: "5px", margin: "5px 0 5px 0"}}>
  <summary>Sample JSON for LayoutComponent with tileStyle</summary>
  <pre>
  ```javascript
  {
    "name":"LayoutComponent", // Only valid value is "LayoutComponent"
    "type":"sitewide", // Only valid value is "sitewide"
    "data":{  // Required
      "desktopAndMobile": { // required
        "shouldDisplay": <boolean>,   //required
        "data": {  //required
          "style": <Valid style object>,  //optional, to be applied to layout container
          "components": [  //required
            {
              "tileStyle": { //optional, to be applied to this component (tile) inside the layout container
                "desktop": <Valid Style Object>,
                "mobile": <Valid Style Object>,
              },
              <valid json for a component>
            },
            <Any Component>,
            .
            .
            <Any Component>
          ]
        }
      }
    }
  }
  ```
  </pre>
</details>

#### General Format for a LayoutComponent that uses _different_ layouts (i.e., different components or styles) for mobile and desktop.

If the component should display differently on mobile and desktop (e.g., different styles, different components, different ordering of components), the "data" object should contain both "desktop" and "mobile" blocks.

<details style={{border: "1px solid #afafaf", padding: "5px", borderRadius: "5px", margin: "5px 0 5px 0"}}>
  <summary>Sample JSON for LayoutComponent for mobile and desktop</summary>
  <pre>
```javascript
{
  "name":"LayoutComponent", // Only valid value is "LayoutComponent"
  "type":"sitewide", // Only valid value is "sitewide"
  "data":{  // Required
    "desktop": { //required
      "shouldDisplay": <boolean>,   //required
      "data": { //required
        "style": <Valid style object>, //optional, to be applied to layout container
        "components": [  //required
          {
            "tileStyle": { //optional, to be applied to this component (tile) inside the layout container
              "desktop": <Valid Style Object>,
              "mobile": <Valid Style Object>,
            },
            <valid json for a component>
          },
          <Any Component>
          .
          .
          <Any Component>
        ]
      }
    },
    "mobile": { //required
      "shouldDisplay": <boolean>,   //required
      "data": {  //required
        "style": <Valid style object>,  //optional
        "components": [  //required
          <Any Component>,
          .
          .
          <Any Component>
        ]
      }
    }
  }
}
```
  </pre>
</details>

#### Sample JSON for a LayoutComponent that contains unique layouts for mobile and desktop.

<details style={{border: "1px solid #afafaf", padding: "5px", borderRadius: "5px", margin: "5px 0 5px 0"}}>
  <summary>Sample JSON for LayoutComponent with unique layouts for mobile and desktop</summary>
  <pre>
```javascript
{
  "name":"LayoutComponent",
  "type":"sitewide",
  "data":{
    "desktop":{
      "shouldDisplay":true,
      "data":{
        "style":{
          "flexDirection":"column",
          "padding":"40px 0px"
        },
        "components":[
          {
            "type":"sitewide",
            "name":"SVGOverlay",
            "tileStyle":{
              "desktop":{
                "flex":"1",
                "marginRight":"2%",
                "padding":"0 0 10px 0"
              }
            },
            "data":{
              "background":{
                "content":{
                  "smallImg":"/Asset_Archive/ATWeb/content/0016/141/106/assets/MostLovedTops_1400.svg",
                  "largeImg":"/Asset_Archive/ATWeb/content/0016/141/106/assets/MostLovedTops_1400.svg",
                  "altText":"Most Loved Tops"
                }
              },
              "containerStyle":{
                "desktop":{
                  "padding":"0 0 0 0"
                },
                "mobile":{
                  "padding":"0 0 0 0"
                }
              },
              "svgoverlay":{
                "smallImg":"",
                "largeImg":"",
                "alttext":""
              }
            }
          },
          {
            "type":"sitewide",
            "name":"SVGOverlay",
            "tileStyle":{
              "desktop":{
                "flex":"1",
                "marginRight":"2%",
                "padding":"10px 0 10px 0"
              }
            },
            "data":{
              "background":{
                "content":{
                  "smallImg":"/Asset_Archive/ATWeb/content/0016/141/106/assets/MostLovedTops_1400.svg",
                  "largeImg":"/Asset_Archive/ATWeb/content/0016/141/106/assets/MostLovedTops_1400.svg",
                  "altText":"Most Loved Tops"
                }
              },
              "containerStyle":{
                "desktop":{
                  "padding":"0 0 0 0"
                },
                "mobile":{
                  "padding":"0 0 0 0"
                }
              },
              "svgoverlay":{
                "smallImg":"",
                "largeImg":"",
                "alttext":""
              }
            }
          }
        ]
      }
    },
    "mobile":{
      "shouldDisplay":true,
      "data":{
        "style":{
          "flexDirection":"column",
          "padding":"40px 0px"
        },
        "components":[
          {
            "type":"sitewide",
            "name":"SVGOverlay",
            "tileStyle":{
              "flex":"1",
              "marginRight":"2%"
            },
            "data":{
              "background":{
                "content":{
                  "smallImg":"/Asset_Archive/ATWeb/content/0015/940/038/assets/commute.jpg",
                  "largeImg":"/Asset_Archive/ATWeb/content/0015/940/038/assets/commute.jpg",
                  "altText":"all shoes. background image"
                }
              },
              "containerStyle":{
                "mobile":{
                  "padding":"0 0 1rem 0"
                }
              },
              "svgoverlay":{
                "smallImg":"/Asset_Archive/BRWeb/content/0015/961/655/assets/S_M/ShopAllShoes_TXT_SM.svg",
                "largeImg":"/Asset_Archive/BRWeb/content/0015/961/655/assets/L_XL/ShopAllShoes_TXT_XL.svg",
                "altText":"all shoes. copy image",
                "link":{
                  "url":"/browse/category.do?cid=48422&mlink=5151,15890556,Promo_HP_Spotlight_BG&clink=15890556",
                  "tid":"29818_EBB"
                }
              }
            }
          },
          {
            "type":"sitewide",
            "name":"SVGOverlay",
            "tileStyle":{
              "flex":"1",
              "marginRight":"2%"
            },
            "data":{
              "background":{
                "content":{
                  "smallImg":"/Asset_Archive/ATWeb/content/0015/940/038/assets/commute.jpg",
                  "largeImg":"/Asset_Archive/ATWeb/content/0015/940/038/assets/commute.jpg",
                  "altText":"all shoes. background image"
                }
              },
              "containerStyle":{
                "mobile":{
                  "padding":"0 0 1rem 0"
                }
              },
              "svgoverlay":{
                "smallImg":"/Asset_Archive/BRWeb/content/0015/961/655/assets/S_M/ShopAllShoes_TXT_SM.svg",
                "largeImg":"/Asset_Archive/BRWeb/content/0015/961/655/assets/L_XL/ShopAllShoes_TXT_XL.svg",
                "altText":"all shoes. copy image",
                "link":{
                  "url":"/browse/category.do?cid=48422&mlink=5151,15890556,Promo_HP_Spotlight_BG&clink=15890556",
                  "tid":"29818_EBB"
                }
              }
            }
          }
        ]
      }
    }
  }
}
```
  </pre>
</details>

## Example layouts and what their configs would look like

<a id='exampleJsons' href='#topExamples'>
  back to top
</a>

If right now you need,

<table>
  <tbody>
    <tr>
      <td colspan='3' align='center'>
        {' '}
        SVGOverlay
      </td>
    </tr>
    <tr>
      <td> SVGOverlay</td>
      <td> SVGOverlay</td>
      <td> SVGOverlay</td>
    </tr>
  </tbody>
</table>

your json might look like this

<details style={{border: "1px solid #afafaf", padding: "5px", borderRadius: "5px", margin: "5px 0 5px 0"}}>
  <summary>Sample JSON</summary>
  <pre>
```javascript
{
  "name":"LayoutComponent",
  "type":"sitewide",
  "data":{
    "desktop": {
      "shouldDisplay": true,
      "data": {
        "style": {
          "flexDirection": "column"
        },
        "components": [
          { // AnyComponent JSON },
          {
            "name":"LayoutComponent",
            "type":"sitewide",
            "data":{
              "desktop": {
                "shouldDisplay": true,
                "data": {
                  "style": {
                    "flexDirection": "row"
                  },
                  "components": [
                    { // AnyComponent JSON },
                    { // AnyComponent JSON },
                    { // AnyComponent JSON }
                  ]
                }
              }
            }
          }
        ]
      }
    }
  }
}
```
  </pre>
</details>

but sometimes you need this

<table>
  <tbody>
    <tr>
      <td> SVGOverlay</td>
      <td> SVGOverlay</td>
      <td> SVGOverlay</td>
    </tr>
    <tr>
      <td colspan='3' align='center'>
        {' '}
        SVGOverlay
      </td>
    </tr>
    <tr>
      <td colspan='3' align='center'>
        {' '}
        SVGOverlay
      </td>
    </tr>
  </tbody>
</table>

your json might look like this

<details style={{border: "1px solid #afafaf", padding: "5px", borderRadius: "5px", margin: "5px 0 5px 0"}}>
  <summary>Sample JSON</summary>
  <pre>
 ```javascript
{
  "name":"LayoutComponent",
  "type":"sitewide",
  "data":{
    "desktop": {
      "shouldDisplay": true,
      "data": {
        "style": {
          "flexDirection": "column"
        },
        "components": [
          {
            "name":"LayoutComponent",
            "type":"sitewide",
            "data":{
              "desktop": {
                "shouldDisplay": true,
                "data": {
                  "style": {
                    "flexDirection": "row"
                  },
                  "components": [
                    { // AnyComponent JSON },
                    { // AnyComponent JSON },
                    { // AnyComponent JSON }
                  ]
                }
              }
            }
          },
          { // AnyComponent JSON },
          { // AnyComponent JSON }
        ]
      }
    }
  }
}
```
  </pre>
</details>

and other times you need this,

<table>
  <tbody>
    <tr>
      <td colspan='3' align='center'>
        {' '}
        SVGOverlay
      </td>
    </tr>
    <tr>
      <td> SVGOverlay</td>
      <td> SVGOverlay</td>
      <td> SVGOverlay</td>
    </tr>
    <tr>
      <td colspan='3' align='center'>
        {' '}
        SVGOverlay
      </td>
    </tr>
  </tbody>
</table>

your json might look like this

<details style={{border: "1px solid #afafaf", padding: "5px", borderRadius: "5px", margin: "5px 0 5px 0"}}>
  <summary>Sample JSON</summary>
  <pre>
```javascript
{
  "name":"LayoutComponent",
  "type":"sitewide",
  "data":{
    "desktop": {
      "shouldDisplay": true,
      "data": {
        "style": {
          "flexDirection": "column"
        },
        "components": [
          { // AnyComponent JSON },
          {
            "name":"LayoutComponent",
            "type":"sitewide",
            "data":{
              "desktop": {
                "shouldDisplay": true,
                "data": {
                  "style": {
                    "flexDirection": "row"
                  },
                  "components": [
                    { // AnyComponent JSON },
                    { // AnyComponent JSON },
                    { // AnyComponent JSON }
                  ]
                }
              }
            }
          },
          { // AnyComponent JSON }
        ]
      }
    }
  }
}
```
  </pre>
</details>

## Please Note

- SVGOverlay component has been deprecated
- The styling of some components (i.e. SVGOverlay), in storybook, will not render correctly because criticalCss is not included here in Storybook.
