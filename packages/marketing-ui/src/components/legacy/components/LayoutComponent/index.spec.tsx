// @ts-nocheck
import React from 'react';
import { axe } from 'jest-axe';
import { render, act } from 'test-utils';
import { SMALL, LARGE, Size } from '@ecom-next/core/breakpoint-provider';
import LayoutComponent from '.';
import { DynamicMarketing as Marketing } from '../../../json-marketing.client';
import { LayoutDataProps, LayoutComponentProps } from './types';
import { baseComponentData, desktopAndMobileData } from './__fixtures__/data';

describe('Layout Component', () => {
  const renderLayoutComponent = (props: LayoutDataProps, size: Size = LARGE) => {
    const fakeMarketingComp = () => <div aria-label='component'>mkt component viewport {size}</div>;

    {
      (Marketing as jest.Mock).mockImplementation(fakeMarketingComp);
    }

    return render(<LayoutComponent data={props} />, {
      breakpoint: size,
    });
  };

  test('should render desktop components', () => {
    const { container, getByLabelText } = renderLayoutComponent(baseComponentData, LARGE);

    expect(container.firstChild.firstChild).toHaveStyleRules({
      display: 'flex',
      'flex-direction': 'column',
      outline: '1px unset red',
    });
    expect(getByLabelText('component').parentElement).toHaveStyleRules({
      outline: '1px dashed orange',
    });
    expect(getByLabelText('component')).toHaveTextContent('mkt component viewport large');
  });

  test('should render mobile components using mobile data', () => {
    const { container, getByLabelText } = renderLayoutComponent(baseComponentData, SMALL);

    expect(container.firstChild.firstChild).toHaveStyleRules({
      display: 'flex',
      'flex-direction': 'column',
      outline: '1px dashed blue',
    });
    expect(getByLabelText('component').parentElement).toHaveStyleRules({
      outline: '1px unset green',
    });
    expect(getByLabelText('component')).toHaveTextContent('mkt component viewport small');
  });

  test('should render shared components at mobile size', () => {
    const { container, getByLabelText } = renderLayoutComponent(desktopAndMobileData, SMALL);

    expect(container.firstChild.firstChild).toHaveStyleRules({
      display: 'flex',
      'flex-direction': 'column',
      outline: '1px groove azure',
    });
    expect(getByLabelText('component').parentElement).toHaveStyleRules({
      outline: '10px solid lemonchiffon',
    });
    expect(getByLabelText('component')).toHaveTextContent('mkt component viewport small');
  });

  test('should render shared components at desktop size', () => {
    const { container, getByLabelText } = renderLayoutComponent(desktopAndMobileData, LARGE);

    expect(container.firstChild.firstChild).toHaveStyleRules({
      display: 'flex',
      'flex-direction': 'column',
      outline: '1px groove azure',
    });
    expect(getByLabelText('component').parentElement).toHaveStyleRules({
      outline: '5px solid aquamarine',
    });
    expect(getByLabelText('component')).toHaveTextContent('mkt component viewport large');
  });

  test('should render with container style', () => {
    const { container } = renderLayoutComponent(baseComponentData, LARGE);
    expect(container.firstChild.firstChild).toHaveStyleRules({
      display: 'flex',
      'flex-direction': 'column',
      outline: '1px unset red',
    });
  });

  test('should render fallback', () => {
    const fakeMarketingComp = jest.fn();
    const fallbackContent: LayoutComponentProps = {
      data: {
        desktop: {
          shouldDisplay: true,
          data: {
            style: {
              background: 'orange',
            },
            components: [],
          },
        },
      },
      fallbackContent: fakeMarketingComp(),
    };

    renderLayoutComponent(fallbackContent as LayoutComponentProps['data'], LARGE);
    expect(fakeMarketingComp).toHaveBeenCalled();
  });

  test('should not render if no component data ', () => {
    const noComponentData: LayoutComponentProps = {
      data: {
        desktop: {
          shouldDisplay: true,
          data: {
            style: {
              background: 'orange',
            },
            components: [],
          },
        },
      },
    };

    const { container } = renderLayoutComponent(noComponentData as LayoutComponentProps['data'], LARGE);

    expect(container.firstChild).toBeEmptyDOMElement();
  });

  test('should not render Layout Component', () => {
    const { container } = renderLayoutComponent({}, LARGE);

    expect(container.firstChild).toBeEmptyDOMElement();
  });

  describe('a11y', () => {
    it('should not have a11y violations', async () => {
      const { container } = renderLayoutComponent(baseComponentData, LARGE);
      expect(await axe(container)).toHaveNoViolations();
    });

    it('should not have a11y violations on mobile', async () => {
      const { container } = renderLayoutComponent(baseComponentData, SMALL);
      expect(await axe(container)).toHaveNoViolations();
    });
  });
});
