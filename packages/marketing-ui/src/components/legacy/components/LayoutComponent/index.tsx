// @ts-nocheck
'use client';
import React, { memo } from 'react';
import { BreakpointContext, LARGE } from '@ecom-next/core/breakpoint-provider';
import { CSSObject } from '@ecom-next/core/react-stitch';

import { DynamicMarketing } from '../../../json-marketing.client';
import { LayoutDataType, LayoutComponentProps, MobileOrDesktop, TileStyle, LayoutDataProps } from './types';

export const LayoutComponent = ({ data, screenSize = 'desktop' }: LayoutDataType): JSX.Element | null => {
  const { style = {}, components = [], classes = '' } = data;
  const isCustomStyles: CSSObject | {} = Object.keys(style as CSSObject).length > 0 ? { ...style } : {};

  const containerStyle = { display: 'flex', ...isCustomStyles };
  const componentTiles = components.map((componentData, index: number) => {
    if (componentData.name.length === 0) return null;

    const { data, tileStyle = { mobile: {}, desktop: {} }, name, type = '', instanceName = '', ...rest } = componentData;
    const isTile: boolean = Object.keys(tileStyle as CSSObject).length > 0 && tileStyle !== undefined;

    const tileStyleRules: CSSObject = isTile ? (tileStyle[screenSize] as TileStyle) : {};

    const tileStyleWithDefaults = {
      flex: '1',
      flexBasis: 'auto',
      ...tileStyleRules,
    };

    // use index for keys here, because it is memoed
    const key = `layout-item-${name}-${instanceName || index}`;
    return (
      <div key={key} css={tileStyleWithDefaults}>
        <DynamicMarketing data={data} name={name} type={type} {...rest} />
      </div>
    );
  });

  return (
    <div className={classes} css={containerStyle} data-testid='layout-component'>
      {componentTiles}
    </div>
  );
};

const getDataForBreakpoint = (componentData: LayoutDataProps, screenSize: MobileOrDesktop): LayoutDataType['data'] | null => {
  if (Object.keys(componentData).length === 0) return null;
  const { desktopAndMobile, desktop, mobile } = componentData;

  if (desktop?.shouldDisplay && screenSize === 'desktop') return desktop?.data;
  if (mobile?.shouldDisplay && screenSize === 'mobile') return mobile?.data;
  if (desktopAndMobile?.shouldDisplay && (screenSize === 'desktop' || screenSize === 'mobile')) return desktopAndMobile?.data;

  return null;
};

const wrappedWithBreakpoint = ({ data, fallbackContent }: LayoutComponentProps): JSX.Element => (
  <BreakpointContext.Consumer>
    {({ greaterOrEqualTo }) => {
      const isDesktop = greaterOrEqualTo(LARGE);
      const screenSize = isDesktop ? 'desktop' : 'mobile';
      const dataForBreakpoint = getDataForBreakpoint(data, screenSize);

      const shouldDisplay = () => {
        const { desktopAndMobile, desktop, mobile } = data;
        if ((screenSize === 'desktop' || screenSize === 'mobile') && desktopAndMobile?.shouldDisplay) return desktopAndMobile?.shouldDisplay;
        if (screenSize === 'desktop' && desktop?.shouldDisplay) return desktop?.shouldDisplay;
        if (screenSize === 'mobile' && mobile?.shouldDisplay) return mobile?.shouldDisplay;

        return false;
      };

      const shouldDisplayData = shouldDisplay();

      return dataForBreakpoint ? <LayoutComponent data={dataForBreakpoint} screenSize={screenSize} shouldDisplay={shouldDisplayData} /> : fallbackContent;
    }}
  </BreakpointContext.Consumer>
);

export default wrappedWithBreakpoint;
