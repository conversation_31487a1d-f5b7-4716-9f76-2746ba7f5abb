// @ts-nocheck
'use client';
import { ComponentMeta, ComponentStory } from '@storybook/react';
import React from 'react';
import LayoutComponent from '..';
import JsonDynamicMarketing from '../../../../json-marketing';
import CmsDynamicMarketing from '../../../../legacy-mui-entry';
import { MarketingProvider } from '../../../../marketing-provider';
import README from '../README.mdx';
import { LayoutDataProps } from '../types';
import {
  mockExampleLayoutDesktop,
  mockExampleLayoutDesktopAndMobile,
  mockExampleLayoutDesktopAndMobileWithCarousel,
  mockExampleLayoutMobile,
  mockLayoutOneSVGOverlay,
  mockLayoutThreeSVGOverlays,
  mockLayoutTwoSVGOverlays,
} from './vr-data';

const marketingData = {
  contentData: {
    contentItems: [],
  },
};

const withProviders = (story, context) => (
  <MarketingProvider value={marketingData} jsonMarketingComponent={JsonDynamicMarketing} cmsMarketingComponent={CmsDynamicMarketing}>
    {story(context)}
  </MarketingProvider>
);

export default {
  title: 'Common/JSON Components (Marketing)/LayoutComponent',
  decorators: [withProviders],
  parameters: {
    docs: {
      page: README,
    },
    eyes: { include: false },
    layout: 'fullscreen',
    layoutBreakpoints: true,
    waitBeforeCapture: 2500,
  },
  tags: ['exclude'],
  knobs: { disable: true },
} as ComponentMeta<typeof LayoutComponent>;

const LayoutComponentTemplate: ComponentStory<typeof LayoutComponent> = args => <LayoutComponent {...args} />;

export const with1Image = LayoutComponentTemplate.bind({});
with1Image.args = {
  data: mockLayoutOneSVGOverlay as LayoutDataProps,
};

export const with2Image = LayoutComponentTemplate.bind({});
with2Image.args = {
  data: mockLayoutTwoSVGOverlays as LayoutDataProps,
};

export const with3Image = LayoutComponentTemplate.bind({});
with3Image.args = { data: mockLayoutThreeSVGOverlays as LayoutDataProps };

export const Desktop = LayoutComponentTemplate.bind({});
Desktop.args = {
  data: mockExampleLayoutDesktop as LayoutDataProps,
};

export const Mobile = LayoutComponentTemplate.bind({});
Mobile.args = {
  data: mockExampleLayoutMobile as LayoutDataProps,
};

export const DesktopAndMobile = LayoutComponentTemplate.bind({});
DesktopAndMobile.args = {
  data: mockExampleLayoutDesktopAndMobile as LayoutDataProps,
};

export const DesktopAndMobileCarousel = LayoutComponentTemplate.bind({});
DesktopAndMobileCarousel.args = {
  data: mockExampleLayoutDesktopAndMobileWithCarousel as LayoutDataProps,
};
