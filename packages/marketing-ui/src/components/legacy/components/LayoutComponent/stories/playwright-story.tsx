// @ts-nocheck
'use client';
import { Meta, StoryFn } from '@storybook/react';
import React from 'react';
import LayoutComponent from '..';
import JsonDynamicMarketing from '../../../../json-marketing';
import CmsDynamicMarketing from '../../../../legacy-mui-entry';
import { MarketingProvider } from '../../../../marketing-provider';
import { LayoutDataProps } from '../types';
import { mockExampleLayoutDesktopAndMobile } from './vr-data';

const marketingData = {
  contentData: {
    contentItems: [],
  },
};

const withProviders = (story, context) => (
  <MarketingProvider value={marketingData} jsonMarketingComponent={JsonDynamicMarketing} cmsMarketingComponent={CmsDynamicMarketing}>
    {story(context)}
  </MarketingProvider>
);

export default {
  title: 'Common/JSON Components (Marketing)/LayoutComponent/Playwright',
  decorators: [withProviders],
  parameters: {
    layout: 'fullscreen',
    eyes: { include: false },
    layoutBreakpoints: true,
    waitBeforeCapture: 2500,
  },
  tags: ['exclude'],
  knobs: { disable: true },
} as Meta<typeof LayoutComponent>;

const getComponent = index => mockExampleLayoutDesktopAndMobile.desktopAndMobile!.data.components[index];

const LayoutComponentTemplate: StoryFn<typeof LayoutComponent> = args => (
  <span data-testid='layout-component'>
    <LayoutComponent {...args} />
  </span>
);

export const ImageContentOverlay = LayoutComponentTemplate.bind({});
export const CtaContentOverlay = LayoutComponentTemplate.bind({});
export const ComposableButton = LayoutComponentTemplate.bind({});
export const HTML5Video = LayoutComponentTemplate.bind({});
export const CtaMobileOnly = LayoutComponentTemplate.bind({});

const imageContentOverlayData = structuredClone(mockExampleLayoutDesktopAndMobile);
const imageComponent = getComponent(0);
imageContentOverlayData.desktopAndMobile!.data.components = [imageComponent];
ImageContentOverlay.args = {
  data: imageContentOverlayData as LayoutDataProps,
};

const ctaContentOverlayData = structuredClone(mockExampleLayoutDesktopAndMobile);
const ctaComponent = getComponent(1);
ctaContentOverlayData.desktopAndMobile!.data.components = [ctaComponent];
CtaContentOverlay.args = {
  data: ctaContentOverlayData as LayoutDataProps,
};

const composableButtonData = structuredClone(mockExampleLayoutDesktopAndMobile);
const composableButtonComponent = getComponent(2);
composableButtonData.desktopAndMobile!.data.components = [composableButtonComponent];
ComposableButton.args = {
  data: composableButtonData as LayoutDataProps,
};

const html5VideoData = structuredClone(mockExampleLayoutDesktopAndMobile);
const html5VideoComponent = getComponent(3);
html5VideoData.desktopAndMobile!.data.components = [html5VideoComponent];
HTML5Video.args = {
  data: html5VideoData as LayoutDataProps,
};

const ctaMobileOnlyData = structuredClone(mockExampleLayoutDesktopAndMobile);
const ctaMobileOnlyComponent = getComponent(5);
ctaMobileOnlyData.desktopAndMobile!.data.components = [ctaMobileOnlyComponent];
CtaMobileOnly.args = {
  data: ctaMobileOnlyData as LayoutDataProps,
};
