// @ts-nocheck
'use client';
import { LayoutDataProps } from '../types';

export const mockLayoutOneSVGOverlay: LayoutDataProps = {
  desktop: {
    shouldDisplay: true,
    data: {
      style: {
        padding: '1rem 1rem',
      },
      components: [
        {
          name: 'SVGOverlay',
          type: 'sitewide',
          data: {
            defaultHeight: '300px',
            containerStyle: {
              mobile: {
                padding: '0 0 1rem 0',
              },
              desktop: {
                padding: '0 0 0 0',
                maxWidth: '1440px',
              },
            },
            background: {
              content: {
                smallImg: '/images/image-placeholder-500x500-300x300.jpg',
                largeImg: '/images/image-placeholder-500x500-300x300.jpg',
                altText: 'Color Forecast. image',
              },
              style: {
                mobile: {},
                desktop: {
                  padding: '0',
                },
              },
            },
            svgoverlay: {
              smallImg: '//bananarepublic.gap.com/assets/common/clear.gif',
              largeImg: '//bananarepublic.gap.com/assets/common/clear.gif',
              altText: 'Color Forecast. copy image',
              link: {
                url: 'https://bananarepublic.gap.com/browse/info.do?cid=1127324&mlink=5151,16833188,HP_Widget1_W_Default_DressesLP&clink=16833188',
                tid: 'HP_Widget1_W_Default_DressesLP',
              },
            },
          },
        },
      ],
    },
  },
  mobile: {
    shouldDisplay: true,
    data: {
      style: {
        padding: '1rem 1rem',
      },
      components: [
        {
          name: 'SVGOverlay',
          type: 'sitewide',
          data: {
            defaultHeight: '300px',
            containerStyle: {
              mobile: {
                padding: '0 0 1rem 0',
              },
              desktop: {
                padding: '0 0 0 0',
                maxWidth: '1440px',
              },
            },
            background: {
              content: {
                smallImg: '/images/image-placeholder-500x500-300x300.jpg',
                largeImg: '/images/image-placeholder-500x500-300x300.jpg',
                altText: 'Color Forecast. image',
              },
              style: {
                mobile: {},
                desktop: {
                  padding: '0',
                },
              },
            },
            svgoverlay: {
              smallImg: '//bananarepublic.gap.com/assets/common/clear.gif',
              largeImg: '//bananarepublic.gap.com/assets/common/clear.gif',
              altText: 'Color Forecast. copy image',
              link: {
                url: 'https://bananarepublic.gap.com/browse/info.do?cid=1127324&mlink=5151,16833188,HP_Widget1_W_Default_DressesLP&clink=16833188',
                tid: 'HP_Widget1_W_Default_DressesLP',
              },
            },
          },
        },
      ],
    },
  },
};

export const mockLayoutTwoSVGOverlays: LayoutDataProps = {
  desktop: {
    shouldDisplay: true,
    data: {
      style: {
        padding: '1rem 1rem',
        flexDirection: 'row',
        margin: '0 auto',
        maxWidth: '1440px',
      },
      components: [
        {
          name: 'SVGOverlay',
          type: 'sitewide',
          data: {
            defaultHeight: '300px',
            containerStyle: {
              mobile: {
                padding: '0 0 0 0',
              },
              desktop: {
                padding: '0 0 0 0',
              },
            },
            background: {
              content: {
                smallImg: '/images/image-placeholder-500x500-300x300.jpg',
                largeImg: '/images/image-placeholder-500x500-300x300.jpg',
                altText: 'Color Forecast. image',
              },
              style: {
                mobile: {},
                desktop: {
                  padding: '0',
                },
              },
            },
            svgoverlay: {
              smallImg: '//bananarepublic.gap.com/assets/common/clear.gif',
              largeImg: '//bananarepublic.gap.com/assets/common/clear.gif',
              altText: 'Color Forecast. copy image',
              link: {
                url: 'https://bananarepublic.gap.com/browse/info.do?cid=1127324&mlink=5151,16833188,HP_Widget1_W_Default_DressesLP&clink=16833188',
                tid: 'HP_Widget1_W_Default_DressesLP',
              },
            },
          },
        },
        {
          name: 'SVGOverlay',
          type: 'sitewide',
          data: {
            defaultHeight: '300px',
            containerStyle: {
              mobile: {
                padding: '0 0 0 0',
              },
              desktop: {
                padding: '0 0 0 0',
              },
            },
            background: {
              content: {
                smallImg: '/images/image-placeholder-500x500-300x300.jpg',
                largeImg: '/images/image-placeholder-500x500-300x300.jpg',
                altText: 'Color Forecast. image',
              },
              style: {
                mobile: {},
                desktop: {
                  padding: '0',
                },
              },
            },
            svgoverlay: {
              smallImg: '//bananarepublic.gap.com/assets/common/clear.gif',
              largeImg: '//bananarepublic.gap.com/assets/common/clear.gif',
              altText: 'Color Forecast. copy image',
              link: {
                url: 'https://bananarepublic.gap.com/browse/info.do?cid=1127324&mlink=5151,16833188,HP_Widget1_W_Default_DressesLP&clink=16833188',
                tid: 'HP_Widget1_W_Default_DressesLP',
              },
            },
          },
        },
      ],
    },
  },
  mobile: {
    shouldDisplay: true,
    data: {
      style: {
        padding: '1rem 1rem',
      },
      components: [
        {
          name: 'SVGOverlay',
          type: 'sitewide',
          data: {
            defaultHeight: '300px',
            containerStyle: {
              mobile: {
                padding: '0 0 1rem 0',
              },
              desktop: {
                padding: '0 0 0 0',
                maxWidth: '1440px',
              },
            },
            background: {
              content: {
                smallImg: '/images/image-placeholder-500x500-300x300.jpg',
                largeImg: '/images/image-placeholder-500x500-300x300.jpg',
                altText: 'Color Forecast. image',
              },
              style: {
                mobile: {},
                desktop: {
                  padding: '0',
                },
              },
            },
            svgoverlay: {
              smallImg: '//bananarepublic.gap.com/assets/common/clear.gif',
              largeImg: '//bananarepublic.gap.com/assets/common/clear.gif',
              altText: 'Color Forecast. copy image',
              link: {
                url: 'https://bananarepublic.gap.com/browse/info.do?cid=1127324&mlink=5151,16833188,HP_Widget1_W_Default_DressesLP&clink=16833188',
                tid: 'HP_Widget1_W_Default_DressesLP',
              },
            },
          },
        },
      ],
    },
  },
};

export const mockLayoutThreeSVGOverlays: LayoutDataProps = {
  desktop: {
    shouldDisplay: true,
    data: {
      style: {
        padding: '1rem 1rem',
        flexDirection: 'row',
        margin: '0 auto',
        maxWidth: '1440px',
      },
      components: [
        {
          name: 'SVGOverlay',
          type: 'sitewide',
          data: {
            defaultHeight: '300px',
            containerStyle: {
              mobile: {
                padding: '0 0 0 0',
              },
              desktop: {
                padding: '0 0 0 0',
              },
            },
            background: {
              content: {
                smallImg: '/images/image-placeholder-500x500-300x300.jpg',
                largeImg: '/images/image-placeholder-500x500-300x300.jpg',
                altText: 'Color Forecast. image',
              },
              style: {
                mobile: {},
                desktop: {
                  padding: '0',
                },
              },
            },
            svgoverlay: {
              smallImg: '//bananarepublic.gap.com/assets/common/clear.gif',
              largeImg: '//bananarepublic.gap.com/assets/common/clear.gif',
              altText: 'Color Forecast. copy image',
              link: {
                url: 'https://bananarepublic.gap.com/browse/info.do?cid=1127324&mlink=5151,16833188,HP_Widget1_W_Default_DressesLP&clink=16833188',
                tid: 'HP_Widget1_W_Default_DressesLP',
              },
            },
          },
        },
        {
          name: 'SVGOverlay',
          type: 'sitewide',
          data: {
            defaultHeight: '300px',
            containerStyle: {
              mobile: {
                padding: '0 0 0 0',
              },
              desktop: {
                padding: '0 0 0 0',
              },
            },
            background: {
              content: {
                smallImg: '/images/image-placeholder-500x500-300x300.jpg',
                largeImg: '/images/image-placeholder-500x500-300x300.jpg',
                altText: 'Color Forecast. image',
              },
              style: {
                mobile: {},
                desktop: {
                  padding: '0',
                },
              },
            },
            svgoverlay: {
              smallImg: '//bananarepublic.gap.com/assets/common/clear.gif',
              largeImg: '//bananarepublic.gap.com/assets/common/clear.gif',
              altText: 'Color Forecast. copy image',
              link: {
                url: 'https://bananarepublic.gap.com/browse/info.do?cid=1127324&mlink=5151,16833188,HP_Widget1_W_Default_DressesLP&clink=16833188',
                tid: 'HP_Widget1_W_Default_DressesLP',
              },
            },
          },
        },
        {
          name: 'SVGOverlay',
          type: 'sitewide',
          data: {
            defaultHeight: '300px',
            containerStyle: {
              mobile: {
                padding: '0 0 0 0',
              },
              desktop: {
                padding: '0 0 0 0',
              },
            },
            background: {
              content: {
                smallImg: '/images/image-placeholder-500x500-300x300.jpg',
                largeImg: '/images/image-placeholder-500x500-300x300.jpg',
                altText: 'Color Forecast. image',
              },
              style: {
                mobile: {},
                desktop: {
                  padding: '0',
                },
              },
            },
            svgoverlay: {
              smallImg: '//bananarepublic.gap.com/assets/common/clear.gif',
              largeImg: '//bananarepublic.gap.com/assets/common/clear.gif',
              altText: 'Color Forecast. copy image',
              link: {
                url: 'https://bananarepublic.gap.com/browse/info.do?cid=1127324&mlink=5151,16833188,HP_Widget1_W_Default_DressesLP&clink=16833188',
                tid: 'HP_Widget1_W_Default_DressesLP',
              },
            },
          },
        },
      ],
    },
  },
  mobile: {
    shouldDisplay: true,
    data: {
      style: {
        padding: '1rem 1rem',
      },
      components: [
        {
          name: 'SVGOverlay',
          type: 'sitewide',
          data: {
            defaultHeight: '300px',
            containerStyle: {
              mobile: {
                padding: '0 0 1rem 0',
              },
              desktop: {
                padding: '0 0 0 0',
                maxWidth: '1440px',
              },
            },
            background: {
              content: {
                smallImg: '/images/image-placeholder-500x500-300x300.jpg',
                largeImg: '/images/image-placeholder-500x500-300x300.jpg',
                altText: 'Color Forecast. image',
              },
              style: {
                mobile: {},
                desktop: {
                  padding: '0',
                },
              },
            },
            svgoverlay: {
              smallImg: '//bananarepublic.gap.com/assets/common/clear.gif',
              largeImg: '//bananarepublic.gap.com/assets/common/clear.gif',
              altText: 'Color Forecast. copy image',
              link: {
                url: 'https://bananarepublic.gap.com/browse/info.do?cid=1127324&mlink=5151,16833188,HP_Widget1_W_Default_DressesLP&clink=16833188',
                tid: 'HP_Widget1_W_Default_DressesLP',
              },
            },
          },
        },
      ],
    },
  },
};

export const mockExampleLayoutDesktop: LayoutDataProps = {
  desktop: {
    shouldDisplay: true,
    data: {
      classes: 'ONhome1-recs',
      style: {
        display: 'flex',
        margin: '0 auto',
        maxWidth: '1440px',
        flexDirection: 'column',
        flexWrap: 'nowrap',
        justifyContent: 'flex-start',
        height: '100%',
      },
      components: [
        {
          name: 'ComposableButton',
          tileStyle: {
            desktop: {
              display: 'block',
              width: '90%',
              margin: 'calc(100vw / 720 * 20) auto calc(100vw / 720 * 40)',
              textAlign: 'center',
              '& a': {
                backgroundColor: '#FFFFFF',
                width: '100%',
                border: 'solid 2px #003764',
                borderRadius: '8px',
                color: '#003764',
                padding: '0.6875em 0.8em',
                fontSize: '0.9rem',
                letterSpacing: 'normal',
              },
            },
            mobile: {
              display: 'block',
            },
          },
          data: {
            linkProps: {
              href: '/browse/info.do?cid=1113546&mlink=5151,1,HP_Certona_More',
            },
            borderThickness: 'medium',
            bright: false,
            capitalization: 'uppercase',
            color: 'primary',
            crossBrand: false,
            font: 'secondary',
            fullWidth: false,
            variant: 'border',
            roundedCorner: 'true',
            buttonText: 'Shop More Picks (Desktop)',
          },
        },
      ],
    },
  },
};

export const mockExampleLayoutMobile: LayoutDataProps = {
  mobile: {
    shouldDisplay: true,
    data: {
      classes: 'ONhome1-recs',
      style: {
        display: 'flex',
        margin: '0 auto',
        maxWidth: '1440px',
        flexDirection: 'column',
        flexWrap: 'nowrap',
        justifyContent: 'flex-start',
        height: '100%',
      },
      components: [
        {
          name: 'ComposableButton',
          tileStyle: {
            mobile: {
              display: 'block',
              width: '90%',
              margin: 'calc(100vw / 720 * 20) auto calc(100vw / 720 * 40)',
              textAlign: 'center',
              '& a': {
                backgroundColor: '#FFFFFF',
                width: '100%',
                border: 'solid 2px #003764',
                borderRadius: '8px',
                color: '#003764',
                padding: '0.6875em 0.8em',
                fontSize: '0.9rem',
                letterSpacing: 'normal',
              },
            },
            desktop: {
              display: 'block',
            },
          },
          data: {
            linkProps: {
              href: '/browse/info.do?cid=1113546&mlink=5151,1,HP_Certona_More',
            },
            borderThickness: 'medium',
            bright: false,
            capitalization: 'uppercase',
            color: 'primary',
            crossBrand: false,
            font: 'secondary',
            fullWidth: false,
            variant: 'border',
            roundedCorner: 'true',
            buttonText: 'Shop More Picks (mobile)',
          },
        },
      ],
    },
  },
};

export const mockExampleLayoutDesktopAndMobile: LayoutDataProps = {
  desktopAndMobile: {
    shouldDisplay: true,
    data: {
      classes: 'ONhome1-recs',
      style: {
        display: 'flex',
        margin: '0 auto',
        maxWidth: '1440px',
        flexDirection: 'column',
        flexWrap: 'nowrap',
        justifyContent: 'flex-start',
        height: '100%',
      },
      components: [
        {
          type: 'sitewide',
          name: 'LayeredContentModule',
          data: {
            lazy: true,
            placeholderSettings: {
              useGreyLoadingEffect: false,
              desktop: {
                'min-height': '300px',
                background: '#FFFFFF',
              },
              mobile: {
                'min-height': '100px',
                background: '#FFFFFF',
              },
            },
            style: {},
            excludePageTypes: [],
            background: {
              className: '',
              style: {},
              desktopStyle: {
                width: '100%',
                margin: '0',
                maxWidth: 'inherit',
              },
              image: {
                alt: '',
                srcUrl: 'https://cdn.media.amplience.net/i/athletaprod/SPR23_D3B_HP_F1_Sec_S',
                desktopSrcUrl: 'https://cdn.media.amplience.net/i/athletaprod/SPR23_D3B_HP_F1_Sec_XL',
              },
              linkData: {
                to: '/browse/category.do?cid=89745&mlink=46650,1,HP_Secondary_0314_AllDresses',
                title: 'Shop Dresses',
                alt: 'Shop Dresses',
              },
            },
            overlay: {
              alt: 'Campaign: Dress to thrill. Meet the limitless pieces made with built in shorts, adjustable details, and breathable fabrics.',
              srcUrl: 'https://athletaprod.a.bigcontent.io/v1/static/SPR23_D3B_HP_F1_Sec_S_1',
              desktopSrcUrl: 'https://athletaprod.a.bigcontent.io/v1/static/SPR23_D3B_HP_F1_Sec_XL_1',
            },
            container: {
              style: {
                position: 'relative',
                background: '#FFFFFF',
              },
            },
          },
        },
        {
          name: 'LayeredContentModule',
          data: {
            overlay: {
              alt: 'Travel Bag Essentials',
              srcUrl: 'https://athletaprod.a.bigcontent.io/v1/static/SPR23_D3B_HP_F1_ATGCats_XL_1',
              desktopSrcUrl: 'https://athletaprod.a.bigcontent.io/v1/static/SPR23_D3B_HP_F1_ATGCats_XL_1',
            },
            background: {
              desktopStyle: {
                display: 'flex',
              },
              image: {
                desktopStyle: {
                  display: 'show',
                },
                srcUrl: 'https://cdn.media.amplience.net/i/athletaprod/SPR23_D3B_HP_F1_ATGCats_XL',
                desktopSrcUrl: 'https://cdn.media.amplience.net/i/athletaprod/SPR23_D3B_HP_F1_ATGCats_XL',
              },
              style: {
                display: 'flex',
              },
            },
            container: {
              desktopStyle: {
                width: '100%',
                marginTop: 0,
              },
              style: {
                margin: '0 auto',
                position: 'relative',
                marginTop: '-0.3em',
                background: '#FFFFFF',
              },
            },
            ctaList: {
              mobilePositionAboveContent: false,
              style: {
                position: 'absolute',
                top: '0%',
                left: '0%',
                height: '100%',
                width: '100%',
                '& a': {
                  color: 'transparent',
                  background: 'transparent',
                  userSelect: 'none',
                  width: '50%',
                  height: '50%',
                  'max-height': 'none',
                  fontSize: '1px',
                },
              },
              desktopStyle: {
                display: 'flex',
                justifyContent: 'center',
                alignItems: 'center',
                flexDirection: 'row',
                flexWrap: 'nowrap',
                top: 'auto',
                bottom: '0',
                left: '0%',
                height: '79%',
                width: '100%',
                '& a': {
                  width: '25%',
                  height: '100%',
                },
              },
              className: '',
              ctas: [
                {
                  composableButtonData: {
                    alt: 'Shop Girl Swim',
                    children: 'Shop Girl Swim',
                  },
                  linkData: {
                    target: '_self',
                    to: '/browse/category.do?cid=1054836&mlink=46650,1,HP_GirlTopCats_0314_GirlSwim',
                  },
                },
                {
                  composableButtonData: {
                    alt: 'Shop Girl Tops',
                    children: 'Shop Girl Tops',
                  },
                  linkData: {
                    target: '_self',
                    to: '/browse/category.do?cid=1055327&mlink=46650,1,HP_GirlTopCats_0314_GirlTops',
                  },
                },
                {
                  composableButtonData: {
                    alt: 'Shop Girl Shorts and Skorts',
                    children: 'Shop Girl Shorts and Skorts',
                  },
                  linkData: {
                    target: '_self',
                    to: '/browse/category.do?cid=1178975&mlink=46650,1,HP_GirlTopCats_0314_GirlShortsSkorts',
                  },
                },
                {
                  composableButtonData: {
                    alt: 'Shop All Girl',
                    children: 'Shop All Girl',
                  },
                  linkData: {
                    target: '_self',
                    to: '/browse/category.do?cid=1067955&mlink=46650,1,HP_GirlTopCats_0314_AllGirl',
                  },
                },
              ],
            },
          },
        },
        {
          name: 'ComposableButton',
          tileStyle: {
            mobile: {
              display: 'block',
              width: '90%',
              margin: 'calc(100vw / 720 * 20) auto calc(100vw / 720 * 40)',
              textAlign: 'center',
              '& a': {
                backgroundColor: '#FFFFFF',
                width: '100%',
                border: 'solid 2px #922',
                borderRadius: '8px',
                color: '#003764',
                padding: '0.6875em 0.8em',
                fontSize: '0.9rem',
                letterSpacing: 'normal',
              },
            },
            desktop: {
              display: 'block',
              width: '90%',
              margin: 'calc(100vw / 720 * 20) auto calc(100vw / 720 * 40)',
              textAlign: 'center',
              '& a': {
                backgroundColor: '#FFFFFF',
                width: '100%',
                border: 'solid 2px #003764',
                borderRadius: '8px',
                color: '#922',
                padding: '0.6875em 0.8em',
                fontSize: '0.9rem',
                letterSpacing: 'normal',
              },
            },
          },
          data: {
            linkProps: {
              href: '/browse/info.do?cid=1113546&mlink=5151,1,HP_Certona_More',
            },
            borderThickness: 'medium',
            bright: false,
            capitalization: 'uppercase',
            color: 'primary',
            crossBrand: false,
            font: 'secondary',
            fullWidth: false,
            variant: 'border',
            roundedCorner: 'true',
            buttonText: 'Shop More Picks',
          },
        },
        {
          instanceName: 'html5-video-component',
          name: 'VideoComponent',
          type: 'sitewide',
          tileStyle: {
            desktop: {
              flex: 'unset',
              backgroundColor: '#000000',
              width: '100%',
              padding: '0',
              fontSize: '0',
            },
            mobile: {},
          },
          data: {
            playerStyles: {
              margin: '0 auto',
            },
            url: 'http://peach.themazzone.com/durian/movies/sintel-1024-surround.mp4',
            light: 'https://media.w3.org/2010/05/sintel/poster.png',
            controls: true,
            muted: true,
            playing: true,
            loop: false,
            width: 'unset',
            height: '360px',
            containerStyle: {
              mobile: {
                marginBottom: '50px',
                margin: '0 0 1.5rem 0',
              },
              desktop: {
                marginBottom: '50px',
                margin: '0 auto 1.5rem',
                maxWidth: '1350px',
              },
            },
            analytics: {
              onPlay: {
                video_name_play: 'active_track_jacket_desktop_video_played_content',
                event_name: 'video_play_click',
              },
            },
          },
        },
        {
          type: 'sitewide',
          name: 'LayeredContentModule',
          instanceDesc: 'AthletaWell Community Section Mobile',
          tileStyle: {
            mobile: {
              display: 'block',
            },
            desktop: {},
          },
          data: {
            container: {
              style: {
                position: 'relative',
              },
            },
            background: {
              className: '',
              style: {
                width: '100%',
              },
              desktopStyle: {},
              image: {
                alt: 'Only on AthletaWell',
                srcUrl: 'https://athletaprod.a.bigcontent.io/v1/static/AW_JanWC_0104_HP_XL_03_COPY',
                desktopSrcUrl: '',
              },
              linkData: {
                to: 'https://community.athletawell.com/?utm_source=site&utm_medium=communityspotlight&utm_campaign=caroselbutton&tid=awaw000043',
                alt: 'view all',
                title: 'view all',
                target: '_blank',
              },
            },
          },
        },
        {
          instanceName: 'shop-now-cta-mobile',
          name: 'LayeredContentModule',
          type: 'sitewide',
          data: {
            container: {
              style: {
                position: 'relative',
              },
              desktopStyle: {
                display: 'none',
              },
            },
            ctaList: {
              mobilePositionAboveContent: false,
              style: {
                margin: '0 auto',
                justifyContent: 'center',
                alignItems: 'center',
                textAlign: 'center',
                '& ul': {
                  zIndex: '200',
                },
                '& > a': {
                  fontSize: '0.9rem',
                  height: '44px',
                  letterSpacing: 'normal',
                  backgroundColor: '#FFFFFF',
                  width: '90%',
                  margin: '0 auto 7.5px',
                  border: 'solid 2px #003764',
                  borderRadius: '8px',
                  color: '#003764',
                  '@media (min-width: 768px)': {
                    height: '48px',
                  },
                },
                '& > a:first-child': {
                  fontSize: '0.9rem',
                  marginTop: '0',
                },
                '& > div': {
                  width: '90%',
                  margin: '0 auto 7.5px',
                },
                '& button': {
                  padding: '0.625rem',
                  fontSize: '0.9rem',
                  width: '100%',
                  margin: '0',
                },
              },
              desktopStyle: {
                position: 'absolute',
                top: '0',
                maxWidth: '1440px',
                left: '50%',
                transform: 'translate(-50%, 0)',
                zIndex: '33',
                margin: '0 auto',
                '& > a': {
                  backgroundColor: '#FFFFFF',
                  border: 'solid 2px #003764',
                  borderRadius: '8px',
                  color: '#003764',
                  padding: '0.5rem 0.5rem',
                  whiteSpace: 'normal',
                  lineHeight: '1',
                  width: '378px',
                  height: '48px',
                  margin: '0',
                },
                '& > div': {
                  width: '100%',
                  margin: '0',
                },
                '& button': {
                  width: '378px',
                  height: '48px',
                },
              },
              className: '',
              ctas: [
                {
                  buttonDropdownData: {
                    heading: {
                      text: 'Shop Now',
                    },
                    submenu: [
                      {
                        href: '/browse/category.do?cid=10018&mlink=5151,1,HP_NA',
                        trackingId: 'HP_NA_W',
                        text: 'Women',
                      },
                      {
                        href: '/browse/category.do?cid=11174&mlink=5151,1,HP_NA',
                        trackingId: 'HP_NA_M',
                        text: 'Men',
                      },
                      {
                        href: '/browse/category.do?cid=6036&mlink=5151,1,HP_NA',
                        trackingId: 'HP_NA_G',
                        text: 'Girls',
                      },
                      {
                        href: '/browse/category.do?cid=5918&mlink=5151,1,HP_NA',
                        trackingId: 'HP_NA_B',
                        text: 'Boys',
                      },
                      {
                        href: '/browse/category.do?cid=6825&#pageId=0&department=165&mlink=5151,1,HP_NA',
                        trackingId: 'HP_NA_G',
                        text: 'Toddler Girls',
                      },
                      {
                        href: '/browse/category.do?cid=6157&#pageId=0&department=166&mlink=5151,1,HP_NA',
                        trackingId: 'HP_NA_TB',
                        text: 'Toddler Boys',
                      },
                      {
                        href: '/browse/category.do?cid=37505&#pageId=0&department=165&mlink=5151,1,HP_NA',
                        trackingId: 'HP_NA_G',
                        text: 'Baby Girls',
                      },
                      {
                        href: '/browse/category.do?cid=37508&#pageId=0&department=166&mlink=5151,1,HP_NA',
                        trackingId: 'HP_NA_BB',
                        text: 'Baby Boys',
                      },
                      {
                        href: '/browse/category.do?cid=8454&mlink=5151,1,HP_NA',
                        trackingId: 'HP_NA_W',
                        text: 'Maternity',
                      },
                    ],
                    style: {
                      desktop: {
                        backgroundColor: '#FFFFFF',
                        margin: '0 auto',
                        border: 'solid 2px #003764',
                        borderRadius: '8px',
                        color: '#003764',
                        padding: '0.5rem',
                        whiteSpace: 'normal',
                        lineHeight: '1',
                        width: '95%',
                      },
                      mobile: {
                        backgroundColor: '#FFFFFF',
                        width: '90%',
                        margin: '0 auto',
                        border: 'solid 2px #003764',
                        borderRadius: '8px',
                        color: '#003764',
                      },
                    },
                  },
                },
              ],
            },
          },
        },
      ],
    },
  },
};

export const mockExampleLayoutDesktopAndMobileWithCarousel: LayoutDataProps = {
  desktopAndMobile: {
    shouldDisplay: true,
    data: {
      classes: 'ONhome1-recs',
      style: {
        display: 'flex',
        margin: '0 auto',
        maxWidth: '1440px',
        flexDirection: 'column',
        flexWrap: 'nowrap',
        justifyContent: 'flex-start',
        height: '100%',
      },
      components: [
        {
          instanceName: 'carousel-2slides',
          name: 'Carousel',
          type: 'sitewide',
          data: {
            carouselOptions: {
              slidesToShow: 1,
              slidesToScroll: 1,
              autoplay: false,
              speed: 5000,
              autoplaySpeed: 5000,
              fade: false,
              displayPlayPauseBtn: false,
              dots: false,
              dotsClass: 'slick-dots',
              infinite: false,
              displayArrows: {
                desktop: false,
                mobile: false,
              },
              pauseOnHover: false,
              prevArrowUrl: '',
              nextArrowUrl: '',
            },
            buttonSetting: {
              buttonStyle: {
                position: 'absolute',
                top: 'auto',
                bottom: '2%',
                right: '1%',
                left: 'auto',
                transform: 'translate(0,0)',
                height: '2em',
                width: '2em',
                zIndex: '0',
                '@media (max-width: 767px)': {
                  top: 'auto',
                  bottom: '2%',
                },
              },
            },
            style: {
              maxWidth: '1440px',
              margin: '0 auto',
              position: 'relative',
            },
            components: [
              {
                instanceName: 'frame1',
                name: 'LayeredContentModule',
                type: 'sitewide',
                data: {
                  container: {
                    style: {
                      position: 'relative',
                    },
                  },
                  background: {
                    linkData: {
                      to: '/browse/category.do?cid=26190&mlink=5151,1,HP_Prim_2_a',
                    },
                    image: {
                      srcUrl: '/Asset_Archive/ONWeb/content/0029/723/755/assets/230314_27-M3325_LinenBottoms_S1_HP_SM.jpg',
                      desktopSrcUrl: '/Asset_Archive/ONWeb/content/0029/723/755/assets/230314_27-M3325_LinenBottoms_S1_HP_XL.jpg',
                      alt: 'Two female models dressed in matching beige linen pants and shorts.',
                      style: {
                        display: 'block',
                      },
                    },
                  },
                  overlay: {
                    srcUrl: '/Asset_Archive/ONWeb/content/0029/723/755/assets/230314_27-M3325_LinenBottoms_S1_HP_US_SM.svg',
                    desktopSrcUrl: '/Asset_Archive/ONWeb/content/0029/723/755/assets/230314_27-M3325_LinenBottoms_S1_HP_US_XL.svg',
                    alt: 'That linen life. Some call it a trend. We call it a lifestyle. Pants on sale from $20.',
                  },
                },
              },
              {
                instanceName: 'frame2',
                name: 'LayeredContentModule',
                type: 'sitewide',
                data: {
                  container: {
                    style: {
                      position: 'relative',
                    },
                  },
                  background: {
                    linkData: {
                      to: '/browse/category.do?cid=26190&mlink=5151,1,HP_Prim_2_a',
                    },
                    image: {
                      srcUrl: '/Asset_Archive/ONWeb/content/0029/723/755/assets/230314_27-M3325_LinenBottoms_S2_HP_SM.jpg',
                      desktopSrcUrl: '/Asset_Archive/ONWeb/content/0029/723/755/assets/230314_27-M3325_LinenBottoms_S2_HP_XL.jpg',
                      alt: 'Three women dressed for a night out. One model is wearing a black one-shoulder pants romper. Another model is wearing black wide leg pants and a white ruched top. The third model is wearing a black halter top shorts romper.',
                      style: {
                        display: 'block',
                      },
                    },
                  },
                  overlay: {
                    srcUrl: '/Asset_Archive/ONWeb/content/0029/723/755/assets/230314_27-M3325_LinenBottoms_S2_HP_US_SM.svg',
                    desktopSrcUrl: '/Asset_Archive/ONWeb/content/0029/723/755/assets/230314_27-M3325_LinenBottoms_S2_HP_US_XL.svg',
                    alt: "Flirty for girls' night out.",
                  },
                },
              },
            ],
          },
        },
      ],
    },
  },
};
