// @ts-nocheck
'use client';
import { CSSObject } from '@ecom-next/core/react-stitch';

export type LayoutDataType = {
  data: {
    components: ComponentArrayProps;
    style?: CSSObject;
    classes?: string;
  };
  shouldDisplay: boolean;
  screenSize?: MobileOrDesktop;
};

export type TileDeviceType = 'desktop' | 'mobile' | 'desktopAndMobile';
export type MobileOrDesktop = Exclude<TileDeviceType, 'desktopAndMobile'>;

export type ComponentArrayProps = Array<{
  name: string;
  tileStyle?: TileStyle;
  type?: string;
  data: Record<string, boolean | string | undefined | {}>;
  meta?: Record<string, unknown> | string;
  instanceName?: string;
  instanceDesc?: string;
}>;

export type TileStyle = Record<MobileOrDesktop, CSSObject>;

export type LayoutDataProps = {
  desktop?: LayoutDataType;
  desktopAndMobile?: LayoutDataType;
  mobile?: LayoutDataType;
};

export type LayoutComponentProps = {
  data: LayoutDataProps;
  fallbackContent?: JSX.Element;
};
