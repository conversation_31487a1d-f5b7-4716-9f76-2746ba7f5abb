// @ts-nocheck
import { LinkWithModalType } from '../types';

export const linkData: LinkWithModalType = {
  className: 'testClass',
  target: 'https://google.com',
  to: 'https://google.com',
};

export const defaultModalData: LinkWithModalType = {
  className: 'testClass',
  modalCloseButtonAriaLabel: 'close',
  type: 'modal',
  url: 'https://www.gap.com/Asset_Archive/AllBrands/promoAPI/promo_lookup_details.html?promoId=962117,962177,962097',
  linkText: 'Click Me, I Open A Modal',
  isOpen: false,
};
