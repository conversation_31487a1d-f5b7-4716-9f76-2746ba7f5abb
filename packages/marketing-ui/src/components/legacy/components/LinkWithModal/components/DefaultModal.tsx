// @ts-nocheck
'use client';
import React from 'react';
import { Modal } from '@ecom-next/core/legacy/modal';
import { DefaultModalType } from '../types';

export const DefaultModal = (props: DefaultModalType): JSX.Element => {
  const { closeButtonAriaLabel, isOpen, toggleModal, title, linkText, contentSrc } = props;

  return (
    <Modal closeButtonAriaLabel={closeButtonAriaLabel || 'close'} data-testid='default-modal' isOpen={isOpen} onClose={toggleModal} title={title}>
      <div className='sds_pd'>
        <iframe className='mkt-svg-overlay__iframe' src={contentSrc} title={linkText} />
      </div>
    </Modal>
  );
};
