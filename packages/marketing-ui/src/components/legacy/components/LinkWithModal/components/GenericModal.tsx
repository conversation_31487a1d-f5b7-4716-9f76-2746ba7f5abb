// @ts-nocheck
'use client';
import React from 'react';
import { DynamicMarketing } from '../../../../json-marketing.client';
import { GenericModalType } from '../types';

export const GenericModal = (props: GenericModalType): JSX.Element | null => {
  const { isOpen, toggleModal, actionComponentConfig = {} } = props;
  const { data = {} } = actionComponentConfig;

  data.isOpen = isOpen;
  data.onClose = toggleModal;

  const { name, type } = actionComponentConfig;
  return name && isOpen ? <DynamicMarketing data={data} data-testid='generic-modal' name={name} type={type} /> : null;
};
