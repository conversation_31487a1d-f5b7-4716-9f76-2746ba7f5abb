// @ts-nocheck
'use client';
import { Meta, StoryFn, StoryObj } from '@storybook/react';
import { expect, userEvent, waitFor, within } from '@storybook/test';
import React from 'react';
import LinkWithModal from '.';
import JsonDynamicMarketing from '../../../json-marketing';
import CmsDynamicMarketing from '../../../legacy-mui-entry';
import { MarketingProvider } from '../../../marketing-provider';
import { defaultModalData, linkData } from './__fixtures__/story-data';
import { LinkWithModalType } from './types';

export default {
  title: 'Common/JSON Components (Marketing)/LinkWithModal',
  component: LinkWithModal,
  parameters: {
    knobs: { disable: true },
  },
  tags: ['exclude'],
} satisfies Meta<typeof LinkWithModal>;

const marketingData = {
  contentData: {
    contentItems: [],
  },
};

const LinkWithModalTemplate: StoryFn<typeof LinkWithModal> = (args: LinkWithModalType) => (
  <MarketingProvider value={marketingData} jsonMarketingComponent={JsonDynamicMarketing} cmsMarketingComponent={CmsDynamicMarketing}>
    <LinkWithModal {...args} />
  </MarketingProvider>
);

const LinkWithModalWithChild: StoryFn<typeof LinkWithModal> = (args: LinkWithModalType) => (
  <MarketingProvider value={marketingData} jsonMarketingComponent={JsonDynamicMarketing} cmsMarketingComponent={CmsDynamicMarketing}>
    <LinkWithModal {...args}>
      <div>Click Me, I Open A Link</div>
    </LinkWithModal>
  </MarketingProvider>
);

export const DefaultLink = LinkWithModalWithChild.bind({});
DefaultLink.args = linkData;

export const DefaultLinkModal = LinkWithModalTemplate.bind({});
DefaultLinkModal.args = defaultModalData;

export const InteractionTest: StoryObj<typeof LinkWithModal> = {
  args: defaultModalData,
  play: async ({ args, canvasElement, step }) => {
    const canvas = within(canvasElement);

    await step('Open modal', async () => {
      await userEvent.click(canvas.getByText('Click Me, I Open A Modal'));
    });
    const iframe = canvas.getByRole('iframe');
    await step('Check modal content', async () => {
      await waitFor(() => expect(iframe).toBeInTheDocument());
    });
  },
};
