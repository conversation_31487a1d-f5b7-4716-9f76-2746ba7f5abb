// @ts-nocheck
'use client';
import React from 'react';
import { DynamicMarketing, DynamicMarketingProps } from '../../../json-marketing.client';

type MarketingBannerProps = {
  componentData: DynamicMarketingProps;
  fallbackContent: React.ReactNode;
  type: string;
  meta?: Record<string, unknown>;
};

/*
 * A simple banner component that allows anchoring atomic marketing components like SVGOverlay etc,
 * at top and/or bottom of a block of content
 */
const MarketingBanner = (props: MarketingBannerProps) => {
  const { type, componentData = {} as MarketingBannerProps['componentData'], fallbackContent, meta } = props;

  const { data, name, ...restComponentData } = componentData;

  if (!data) {
    return fallbackContent || '';
  }

  return <DynamicMarketing {...restComponentData} data={data} meta={meta} name={name} type={type} />;
};

export default MarketingBanner;
