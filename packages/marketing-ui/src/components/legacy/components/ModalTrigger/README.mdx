# ModalTrigger

ModalTrigger displays clickable content that triggers the opening of a modal in TriggerableModal.

- ModalTrigger accepts children `layoutData`, `"buttonAriaLabel"` and `modalLayoutData` within its `data` prop.

- ModalTrigger will render all child `layoutData` that is passed in, contained within single button. A click anywhere on the trigger/button will open a modal in its parent component (see [TriggerableModal](https://core-ui-main.apps.cfplatform.dev.azeus.gaptech.com/?path=/story/marketing-TriggerableModal--default-example)). It has a required aria-label to describe the action of the click event (see below)

- `modalLayoutData` is a sibling of Data, and will get passed to the modal once it is opened using the `openModal` callback provided by TriggerableModal.

## Accessibility

### buttonAriaLabel

- `buttonAriaLabel` can be set individually for each `Modal Trigger`.

```
{
  "name": "ModalTrigger",
  "type": "sitewide",
  "data": {
    "buttonAriaLabel": "Click to open a modal to learn more about rockstar jeans",
    "layoutData": {
    ...
```

- This is for accessibility purposes and should describe in detail the action that clicking the trigger will perform.

- If `buttonAriaLabel` is set, this is the **only** information that a screen reader will receive about the trigger. Content within the `layoutData` of the trigger will not be read!

- If left unset, the `aria-label` will be left blank and a screen reader will read the content of the layoutData underneath. It is **important to note** that the only click event associated with any of the `layoutData` content within the trigger will cause the modal to pop up, so all trigger `layoutData` content should be explicit about what to expect on click.

- ✅ Some examples of explicit descriptions: _"Click to open a modal to learn more about Rockstar super-skinny jeans"_, _"Open Rockstar jeans modal"_

- 🚫 Some examples of what to avoid: _"Click here"_, _"Rockstar Skinny Jeans"_, _"Shop now"_

## Example JSON:

```
{
  "name": "ModalTrigger",
  "type": "sitewide",
  "data": {
    "buttonAriaLabel": "this button label was set in json",
    "layoutData": {
      "desktopAndMobile": {
        "shouldDisplay": true,
        "data": {
          "style": {"display": "block"},
          "components": []
        }
      }
    },
    "modalLayoutData": {
      "desktopAndMobile": {
        "shouldDisplay": true,
        "data": {
          "components": []
        }
      }}
  }
}
```
