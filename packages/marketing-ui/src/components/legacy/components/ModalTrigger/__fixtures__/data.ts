// @ts-nocheck
import { ModalTriggerProps } from '../types';

export const baseData: ModalTriggerProps = {
  data: {
    layoutData: {
      desktopAndMobile: {
        shouldDisplay: true,
        data: {
          style: { display: 'flex', alignItems: 'center' },
          components: [],
        },
      },
    },
    modalLayoutData: {
      desktopAndMobile: {
        shouldDisplay: true,
        data: {
          style: { display: 'flex', alignItems: 'center' },
          components: [],
        },
      },
    },
  },
};

export const baseDataWithButtonAriaLabel: ModalTriggerProps = {
  data: {
    buttonAriaLabel: 'Click to open a modal',
    layoutData: {
      desktopAndMobile: {
        shouldDisplay: true,
        data: {
          components: [
            {
              name: 'Image',
              type: 'sitewide',
              data: {
                smallImg: 'https://bananarepublic.gap.com/Asset_Archive/BRWeb/content/0016/881/583/assets/SM/BRSP190226_SITE_US_HP_02_IMG_SM.jpg',
                largeImg: 'https://bananarepublic.gap.com/Asset_Archive/BRWeb/content/0016/881/583/assets/SM/BRSP190226_SITE_US_HP_02_IMG_SM.jpg',
                altText: 'this is an image',
                style: {
                  desktop: {},
                  mobile: {},
                },
              },
            },
          ],
        },
      },
    },
    modalLayoutData: {
      desktopAndMobile: {
        shouldDisplay: true,
        data: {
          style: { display: 'flex', alignItems: 'center' },
          components: [],
        },
      },
    },
  },
};
