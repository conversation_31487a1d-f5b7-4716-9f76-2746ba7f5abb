// @ts-nocheck
'use client';
import React, { useContext } from 'react';
import { ModalTriggerProps } from './types';
import { TriggerableModalContext } from '../TriggerableModal';
import LayoutComponent from '../LayoutComponent';

const ModalTrigger = ({ data: { modalLayoutData, buttonAriaLabel, layoutData } }: ModalTriggerProps) => {
  const { openModal } = useContext(TriggerableModalContext);
  return (
    <button aria-label={buttonAriaLabel || ''} className='sds_unbuttonize' data-testid='modal-trigger' onClick={() => openModal(modalLayoutData)}>
      <LayoutComponent data={layoutData} />
    </button>
  );
};

ModalTrigger.defaultProps = {
  data: {},
};

export default ModalTrigger;
