// @ts-nocheck
import React from 'react';
import { render, act } from 'test-utils';
import MultiComponent from '.';
import { Data, MarketingProps } from './types';

describe('<MultiComponent />', () => {
  type MultiComponentTestParams = {
    schema: string;
    showHideBasedOnScreenSize?: string;
    imageSize?: string;
    screenSize?: string;
  };

  const createMultiComponentProps = (params: MultiComponentTestParams) => {
    const { schema, showHideBasedOnScreenSize = 'alwaysShow', imageSize = 'mobile', screenSize = 'large' } = params;

    const component = {
      _meta: {
        name: 'Sitewide -- test',
        schema,
        deliveryId: 'abc123',
      },
      background: {
        backgroundColor: '#92a8d1',
        backgroundOpacity: 10,
      },
      webAppearance: {
        showHideBasedOnScreenSize,
        size: '20px',
        mobileOverrideSize: '5px',
      },
      screenSize,
      imageSize,
    };
    const components = [JSON.parse(JSON.stringify(component))];

    return {
      errorLogger: () => {},
      marketing: {
        components,
      } as MarketingProps & Data,
      pageType: 'home',
      enabledFeatures: {
        'react-personalization-polling': true,
      },
      screenSize,
    };
  };

  test('renders Vertical Spacer with a minimum height of 0px', () => {
    const props = createMultiComponentProps({
      schema: 'https://cms.gap.com/schema/content/v1/vertical-spacer.json',
    });
    const { container } = render(<MultiComponent {...props} />);
    expect(container.firstChild.firstChild).toHaveStyleRule('min-height', '0px');
  });

  test('renders Site Wide with a minimum height of 0px', () => {
    const props = createMultiComponentProps({
      schema: 'https://cms.gap.com/schema/content/v2/sitewide-.json',
    });
    const { container } = render(<MultiComponent {...props} />);
    expect(container.firstChild.firstChild).toHaveStyleRule('min-height', '0px');
  });

  test('renders Rich Text with a minimum height of 0px', () => {
    const props = createMultiComponentProps({
      schema: 'https://cms.gap.com/schema/content/v1/rich-text.json',
    });
    const { container } = render(<MultiComponent {...props} />);
    expect(container.firstChild.firstChild).toHaveStyleRule('min-height', '0px');
  });

  test('renders Text Navigation with a minimum height of 0px', () => {
    const props = createMultiComponentProps({
      schema: 'https://cms.gap.com/schema/content/v1/text-navigation-.json',
    });
    const { container } = render(<MultiComponent {...props} />);
    expect(container.firstChild.firstChild).toHaveStyleRule('min-height', '0px');
  });

  test('renders CTA or Dropdown with a minimum height of 0px', () => {
    const props = createMultiComponentProps({
      schema: 'https://cms.gap.com/schema/content/v1/cta-or-dropdown.json',
    });
    const { container } = render(<MultiComponent {...props} />);
    expect(container.firstChild.firstChild).toHaveStyleRule('min-height', '0px');
  });

  test('renders circle navigation with a minimum height of 0px', () => {
    const props = createMultiComponentProps({
      schema: 'https://cms.gap.com/schema/content/v1/circle-navigation.json',
    });
    const { container } = render(<MultiComponent {...props} />);
    expect(container.firstChild.firstChild).toHaveStyleRule('min-height', '0px');
  });

  test('renders Spotlight Variable Height (Small) with a minimum height of 0px', () => {
    const props = createMultiComponentProps({
      schema: 'https://cms.gap.com/schema/content/v1/spotlight-variable-height.json',
      imageSize: 'mobile',
      screenSize: 'small',
    });
    const { container } = render(<MultiComponent {...props} />);
    expect(container.firstChild.firstChild).toHaveStyleRule('min-height', '0px');
  });

  test('render spotlight variable height (Large) with a minimum height of 300px', () => {
    const props = createMultiComponentProps({
      schema: 'https://cms.gap.com/schema/content/v1/spotlight-variable-height.json',
      showHideBasedOnScreenSize: 'hideOnMobile',
      imageSize: 'large',
      screenSize: 'large',
    });
    const { container } = render(<MultiComponent {...props} />);
    expect(container.firstChild.firstChild).toHaveStyleRule('min-height', '300px');
  });

  test('renders minimum height with non-matching schemas', () => {
    const props = createMultiComponentProps({
      schema: 'https://cms.gap.com/schema/content/v1/not-real.json',
    });
    const { container } = render(<MultiComponent {...props} />);
    expect(container.firstChild.firstChild).toHaveStyleRule('min-height', '300px');
  });
});
