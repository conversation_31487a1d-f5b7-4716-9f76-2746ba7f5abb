// @ts-nocheck
'use client';
import { DynamicMarketingProps } from '../../DynamicMarketing';
import { MetaProps } from '../../CMS/types/amplience';
import { GlobalProps } from '../../CMS/global/types';
import { ShowHideBasedOnScreenSizeProps } from '../../CMS/subcomponents/ShowHideWrapper/types';
import { VerticalSpacerContentType } from '../../CMS/content-types/VerticalSpacer/types';

export type ComponentSize = { small: string | boolean; large: string | boolean } | string;

export interface WebAppearanceProps extends Partial<GlobalProps<'webAppearance'>> {
  showHideBasedOnScreenSize?: ShowHideBasedOnScreenSizeProps;
}

export interface ComponentTypeProps {
  output?: string;
  name: string;
  type: string;
  experimentRunning?: boolean;
  redpointExperimentRunning?: boolean;
  instanceName?: string;
  data: Data;
  componentDefaultHeight: ComponentSize | undefined;
  meta?: Record<string, unknown>;
  _meta?: MetaProps;
  webAppearance?: WebAppearanceProps & VerticalSpacerContentType['webAppearance'];
  general?: Record<string, unknown>;
  appearance?: WebAppearanceProps;
}
export interface Data {
  components?: ComponentTypeProps[];
  componentDefaultHeight?: ComponentSize;
  marginTop?: ComponentSize;
  marginBottom?: ComponentSize;
  small?: string;
  large?: string;
  isVisible?: boolean | ComponentSize;
  lazy?: boolean;
  defaultHeight?: ComponentSize;
  id?: string;
}
export interface MarketingShape extends Data {
  components: ComponentTypeProps[];
}

export interface MarketingProps {
  marketing?: MarketingShape;
  data?: Data;
  components: ComponentTypeProps[];
}
export interface DynamicMarketingPropsWithExperimentRunning extends DynamicMarketingProps {
  experimentRunning: boolean;
}

export type IsSmallType = boolean | undefined;

export interface MultiComponentProps {
  isSmall?: IsSmallType;
  marketing?: MarketingProps;
  enabledFeatures?: { [key: string]: boolean };
  errorLogger?: (error: Error) => void;
  isBot?: string;
  pageType?: string;
  experimentRunning?: boolean;
}
