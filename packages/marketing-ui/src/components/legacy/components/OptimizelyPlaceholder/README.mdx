## What I do

- Allows developers to easily add placeholders for Optimizely experiments without worrying too much about configuring unnecessary parameters

## JSON Config

<ul>
  <li>
    name <em>(required)</em>
  </li>
  <li>
    instanceName <em>(required)</em>
  </li>
  <li>
    type <em>(required)</em>
  </li>
  <li>
    experimentRunning <em>(optional)</em>
  </li>
  <li>
    data <em>(optional)</em>
  </li>
  <ul>
    <li>
      defaultHeight <em>(optional)</em>
      <ul>
        <li>
          large <em>(optional: defaults to 300 if experimentRunning is true or not defined)</em>
        </li>
        <li>
          small <em>(optional: defaults to 300 if experimentRunning is true or not defined)</em>
        </li>
      </ul>
    </li>
    <li>
      isVisible <em>(optional)</em>
      <ul>
        <li>
          large <em>(optional: defaults to true if experimentRunning is true or not defined)</em>
        </li>
        <li>
          small <em>(optional: defaults to true if experimentRunning is true or not defined)</em>
        </li>
      </ul>
    </li>
    <li>
      placeholderSettings <em>(optional)</em>
      <ul>
        <li>
          useGreyLoadingEffect <em>(optional: boolean)</em>
        </li>
        <li>
          desktop <em>(optional: an object that contains CSS properties)</em>
        </li>
        <li>
          mobile <em>(optional: an object that contains CSS properties)</em>
        </li>
      </ul>
    </li>
  </ul>
</ul>

## Recommendations

When using this component, it is highly recommended to set a default height in the data object for the content that will be injected into the website by Optimizely. If a default height is not set, it will default to 300 pixels and cause either extra whitespace or an unpleasant jump on the user's view.

#### Implementation with No Customizations

```javascript
  {
    "name": "OptimizelyPlaceholder",
    "type": "sitewide",
    "instanceName": "my-new-experiment",
    "experimentRunning": true,
  }
```

#### Recommended Implementation

Define heights based on content from Optimizely. You may also set to 0px, however, it may create a sudden jump behavior for shoppers.

```javascript
  {
    "name": "OptimizelyPlaceholder",
    "type": "sitewide",
    "instanceName": "my-new-experiment",
    "experimentRunning": true,
    "data": {
        "defaultHeight": {
            "large": "XXpx",
            "small": "XXpx"
        }
    }
  }
```

#### Notes:

- You can explicitly set **_experimentRunning_** to true or false. When setting to false, component will not render at all.
