// @ts-nocheck
'use client';
import React from 'react';
import { ComponentStory } from '@storybook/react';
import { Notification as NotificationBase } from '@ecom-next/core/legacy/notification';
import README from './README.mdx';
import OptimizelyPlaceholder, { OptimizelyPlaceholderProps } from '.';

export default {
  title: 'Common/JSON Components (Marketing)/OptimizelyPlaceholder',
  parameters: {
    docs: {
      page: README,
    },
    eyes: { include: false },
  },
  tags: ['exclude'],
};

const data: OptimizelyPlaceholderProps = {
  type: 'sitewide',
  name: 'OptimizelyPlaceholder',
  instanceName: 'my-new-experiment',
  experimentRunning: true,
  data: {
    defaultHeight: {
      large: '0',
      small: '0',
    },
  },
};

const Notification: typeof NotificationBase = props => <NotificationBase {...props} />;

const OptimizelyPlaceholderTemplate: ComponentStory<typeof OptimizelyPlaceholder> = (args): JSX.Element => (
  <>
    <OptimizelyPlaceholder {...args} />
    <p>&nbsp;</p>
    <Notification kind='information'>
      Optimizely placeholder appears in this canvas as an empty div. We will continue to work on a visual demo that will hook into an actual Optimizely
      experiment. For now, review the{' '}
      <a className='sds_link' href='/?path=/info/common-json-components-marketing-optimizelyplaceholder--default'>
        README
      </a>{' '}
      for details about this component.
    </Notification>
  </>
);

export const Default = OptimizelyPlaceholderTemplate.bind({});
Default.args = data;
