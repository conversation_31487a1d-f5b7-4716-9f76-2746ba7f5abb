// @ts-nocheck
import React from 'react';
import { screen, render, act } from 'test-utils';
import OptimizelyPlaceholder, { OptimizelyPlaceholderProps } from '.';

const defaultProps: OptimizelyPlaceholderProps = {
  name: 'OptimizelyPlaceholder',
  type: 'sitewide',
  instanceName: 'my-experiment-01',
};

describe('OptimizelyPlaceholder', () => {
  test('should render optimizely-placeholder div if experimentRunning is true', () => {
    const props: OptimizelyPlaceholderProps = {
      ...defaultProps,
      experimentRunning: true,
    };

    render(<OptimizelyPlaceholder {...props} />);
    expect(screen.getByTestId('optimizely-placeholder')).toBeInTheDocument();
  });

  test('should render optimizely-placeholder div if experimentRunning is true', () => {
    const props: OptimizelyPlaceholderProps = {
      ...defaultProps,
      experimentRunning: false,
    };

    render(<OptimizelyPlaceholder {...props} />);
    expect(screen.queryByTestId('optimizely-placeholder')).not.toBeInTheDocument();
  });
});
