// @ts-nocheck
'use client';
import React from 'react';
import { OptimizelyPlaceholderProps } from './types';

const OptimizelyPlaceholder = ({ experimentRunning }: OptimizelyPlaceholderProps): JSX.Element | null => {
  if (experimentRunning) {
    return <div data-testid='optimizely-placeholder' />;
  }
  return null;
};

export type { OptimizelyPlaceholderProps } from './types';

export default OptimizelyPlaceholder;
