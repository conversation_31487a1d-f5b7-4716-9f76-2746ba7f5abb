# PixleeModule

PixleeModule is a JSON-configurable marketing component that utilized a the third party Pixlee api. It essentially embeds the Pixlee script and container generated from the Pix<PERSON> tool here: https://app.pixlee.com/login#dashboard

In order to use the previous stated tool, one must have a login/apiKey, which will generate a unique embed code. This unique embed code allows the tool to create experiences based on what was configured in the tool

The JSON allows to control the main container housing the component, the min-height of the iframe which <PERSON><PERSON><PERSON> generates, as well as input for the unique api key, and widget id (generated by the Pixlee).

## Developer Docs

Visit the [Pixlee Developer Guide](https://developers.pixlee.com/docs/pixlee-widget-user-guide) to get further implementation examples, API documentation, etc.

## Prop Breakdown

PixleeModule uses the following props:

```
data:
    apiKey
    widgetId
    iframeMinHeight
    containerStyles
```

Prop Descriptions

`apiKey` is a required prop that receives a string. It allows the developer to insert the unique api key of the user that created the embed code. Each unique embed code will have its own api key.

`widgetId` is similarly required and receives a string. Like apiKey, the embed code will generate a unique widgetId that will need to be input into the json to allow the embed code to work properly.

`iframeMinHeight` is also required and takes a string in the form of a CSS value. It controls the minimum height of the iframe that gets embedded by the Pixlee script. By default the height is set to 0px, but this allows the option to adjust the minimum height.

`containerStyles` lastly this prop is not required and receives an object that contains CSS styles. This prop will control the styles of the parent container that house the Pixlee Module, and the iframe that gets embedded inside that element.

## JSON Example

A JumpLink wrapping a HoverImage component:

```
{
  "name": "PixleeModule",
  "type": "sitewide",
  "data": {
    "apiKey": "{{API_VALUE}}",
    "widgetId": "1925432",
    "iframeMinHeight": "425px",
    "containerStyles": {
      "height": "500px",
      "width": "100%",
      "margin": "auto",
      "paddingTop": "50px"
    }
  }
}
```
