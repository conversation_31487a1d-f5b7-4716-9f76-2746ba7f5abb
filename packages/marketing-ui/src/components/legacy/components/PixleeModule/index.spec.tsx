// @ts-nocheck
import { screen, render, act } from 'test-utils';
import React from 'react';
import { PixleeModule } from '.';
import { PIXLEE_KEY } from './constants';

jest.useFakeTimers();

describe('<PixleeModule />', () => {
  afterEach(() => {
    document.querySelectorAll("script[src='https://assets.pxlecdn.com/assets/pixlee_widget_1_0_0.js']").forEach(script => script.remove());
  });

  test("should render 'div' tag with id='pixlee_container'", () => {
    const { container } = render(<PixleeModule apiKey={PIXLEE_KEY} containerStyles={{ height: '500px' }} iframeMinHeight='500px' widgetId='1925432' />);
    expect(container.querySelector('#pixlee_container')).toBeInTheDocument();
  });

  test("should render a script tag with src='https://assets.pxlecdn.com/assets/pixlee_widget_1_0_0.js'", () => {
    render(<PixleeModule apiKey={PIXLEE_KEY} containerStyles={{ height: '500px' }} iframeMinHeight='500px' widgetId='1925432' />);

    const script = document.querySelector('script');

    expect(script).toHaveAttribute('src', 'https://assets.pxlecdn.com/assets/pixlee_widget_1_0_0.js');
  });
});
