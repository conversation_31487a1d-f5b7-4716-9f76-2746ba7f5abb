// @ts-nocheck
'use client';
import React, { useEffect, Fragment, useState } from 'react';
import { styled } from '@ecom-next/core/react-stitch';
import { safeWindow } from '../../helper/safeWindow';
import { PixleeModuleProps } from './types';
import Script from './script';
import { mapDataToProps } from '../../helper';

declare global {
  interface Window {
    Pixlee: {
      init: (apiKey: { apiKey: string }) => void;
      addSimpleWidget: (widgetId: { widgetId: string }) => void;
    };
    PixleeAsyncInit: () => void;
  }
}
export const PixleeModule = (props: PixleeModuleProps): JSX.Element => {
  const { iframeMinHeight, apiKey, widgetId, containerStyles } = props;
  const PixleeContainer = styled.div`
    & > iframe {
      min-height: ${iframeMinHeight};
      min-width: 100%;
    }
  `;

  const [pixleeLoaded, setPixleeLoaded] = useState(false);
  const sWindow = safeWindow();

  useEffect(() => {
    if (!sWindow) return;

    const { Pixlee } = sWindow;
    if (Pixlee) {
      sWindow.PixleeAsyncInit = () => {
        Pixlee.init({ apiKey });
        Pixlee.addSimpleWidget({ widgetId });
      };
      sWindow.PixleeAsyncInit();
    }
  }, [pixleeLoaded, sWindow]);

  return (
    <Fragment>
      <PixleeContainer id='pixlee_container' style={containerStyles} />
      <Script onLoad={() => setPixleeLoaded(true)} src='https://assets.pxlecdn.com/assets/pixlee_widget_1_0_0.js' />
    </Fragment>
  );
};

export default mapDataToProps(PixleeModule);
export type { PixleeModuleProps } from './types';
