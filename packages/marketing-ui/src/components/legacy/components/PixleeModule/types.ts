// @ts-nocheck
'use client';
import { CSSProperties } from 'react';

export type PixleeModuleProps = {
  /**
   * when provided, it will render the render pixlee widget from the specific creator's api key
   */
  apiKey: string;
  /**
   * when provided, the consumer can style the pixlee widget container
   */
  containerStyles: CSSProperties;
  /**
   * when provided, the consumer can adjust iframe height to view the pixlee widget
   */
  iframeMinHeight: string;
  /**
   * when provided, it will render the pixlee widget's specific widgetId experience
   */
  widgetId: string;
};
