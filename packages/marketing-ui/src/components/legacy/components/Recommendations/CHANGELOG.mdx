# Changelog

### Fix

- Recommendations - Deactivated click functionality during the dragging action on desktop view for draggable slides [WCD-794](https://gapinc.atlassian.net/browse/WCD-794).

## 6-2023

### Added, then Reverted

The commit below _required_ numberOfProduct, which cannot be relied upon in the new API.

- Recommendations - use numberOfProduct in url for aggregation service api [WCD-743](https://gapinc.atlassian.net/browse/WCD-743).

## 6-2023

### Added

- Recommendations - enable draggable feature for DesktopRecs [WCD-792](https://gapinc.atlassian.net/browse/WCD-792).

## 4-2023

### Added

- Recommendations - show product marketing flags [WCD-732](https://gapinc.atlassian.net/browse/WCD-732).

## 2-2023

### Added

- Recommendations - Use ccList to find products in product array #wcd-617

## 1-2023

### Added

- Recommendations - Update to support new API [WCD-500](https://gapinc.atlassian.net/browse/WCD-500).

## 10-2022

### Added

- Desktop Pagination [WCD-311](https://gapinc.atlassian.net/browse/WCD-311).

## 09-2022

### Added

- Pagination helper function [WCD-151](https://gapinc.atlassian.net/browse/WCD-151).

### BUG FIX

- Remove extra spacing when MaxStyleRegularPrice prop is not present [WCD-289](https://gapinc.atlassian.net/browse/WCD-289).
