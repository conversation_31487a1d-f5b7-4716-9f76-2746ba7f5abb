// @ts-nocheck
'use client';
import React from 'react';
import PropTypes from 'prop-types';
import { styled, getFont, forBrands, getFontWeight } from '@ecom-next/core/react-stitch';
import { ReviewRatings } from '@ecom-next/core/legacy/review-ratings';

const RatingWrapper = styled.div(({ theme }) => {
  const primaryFont = getFont('primary')({ theme, crossBrand: false });
  const brFontWeight = getFontWeight('regular');

  const defaultCSS = {
    color: theme.color.gray60,
    overflow: 'hidden',
    textOverflow: 'ellipsis',
    lineHeight: '1.2',
    fontWeight: '400',
    whiteSpace: 'normal',
    marginTop: '0.5rem',
    position: 'relative',
  };

  const brandedFontWeight = forBrands(theme, {
    br: () => brFontWeight,
    brfs: () => brFontWeight,
  });

  const brandedTextAlign = forBrands(theme, {
    gap: () => 'left',
    gapfs: () => 'left',
    at: () => 'left',
    default: () => 'center',
  });

  return {
    ...primaryFont,
    ...defaultCSS,
    textAlign: brandedTextAlign,
    display: 'flex',
    alignItems: 'center',
    ...brandedFontWeight,
  };
});

const CertonaRatings = ({ ratings, product, tabIndex }) => {
  const { minRating, showReviewCount, ariaLabel, reviewCountLabel } = ratings;

  if (product.Rating && parseInt(product.Rating, 10) >= parseInt(minRating, 10)) {
    const starRating = parseFloat(product.Rating, 10);

    const reviewCount = () => (showReviewCount ? product.ReviewCount : 0);

    return (
      <RatingWrapper>
        <ReviewRatings ariaLabel={ariaLabel} reviewCount={reviewCount()} reviewCountLabel={reviewCountLabel} starRating={starRating} tabIndex={tabIndex} />
      </RatingWrapper>
    );
  }
  return null;
};

export default CertonaRatings;

CertonaRatings.propTypes = {
  product: PropTypes.shape({
    /**
     * show rating of the product
     */
    Rating: PropTypes.string.isRequired,
    /**
     * show review count of the product
     */
    ReviewCount: PropTypes.string.isRequired,
  }).isRequired,
  /**
   * CSS style helper objects
   */
  productTextStyles: PropTypes.shape({
    productPrice: PropTypes.object,
    productSalePrice: PropTypes.object,
    productTitle: PropTypes.object,
  }),
  /**
   * config for rating
   */
  ratings: PropTypes.shape({
    /**
     * Option aria-label for review ratings
     */
    ariaLabel: PropTypes.string,
    /**
     * minimum rating of the product
     */
    minRating: PropTypes.number,
    /**
     * font color for review count
     */
    reviewCountColor: PropTypes.string,
    /**
     * Option for review count label for singular and plural
     */
    reviewCountLabel: PropTypes.string,
    /**
     * show rating of the product
     */
    showRating: PropTypes.bool,
    /**
     * show review count of the product
     */
    showReviewCount: PropTypes.bool,
  }),
};

CertonaRatings.defaultProps = {
  product: {
    Rating: '0',
    ReviewCount: '0',
  },
  data: {
    ratings: {
      ariaLabel: '',
      minRating: 0,
      reviewCountColor: '#000000',
      reviewCountLabel: '',
      showRating: false,
      showReviewCount: false,
    },
    productTextStyles: {
      productPrice: {},
      productSalePrice: {},
      productTitle: {},
    },
  },
};
