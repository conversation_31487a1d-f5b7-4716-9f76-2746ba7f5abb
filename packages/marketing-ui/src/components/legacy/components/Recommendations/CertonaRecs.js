// @ts-nocheck
'use client';
import React from 'react';
import PropTypes from 'prop-types';
import { useEnabledFeatures } from '@ecom-next/core/react-stitch';
import { useProductRecommendations } from '@ecom-next/sitewide/product-recs-provider';
import getTitle from './helpers/get-certona-title';
import { getRecommendations, parseNumberOfProducts } from './helpers/get-certona-recommendations';

const CertonaRecs = ({ Recommendations, ...props }) => {
  const enabledFeatures = useEnabledFeatures();
  const isNullRenderEnabled = !!enabledFeatures['mui-certona-recommendations'];

  const { aiRecommendationsData, certonaRecommendationsData, aiRecsActiveForPageType } = useProductRecommendations();

  const shouldUseAiData = Boolean(aiRecsActiveForPageType && aiRecommendationsData);

  const resolvedRecommendationData = shouldUseAiData ? aiRecommendationsData : certonaRecommendationsData;

  const noTopLevelData = isNullRenderEnabled && (!resolvedRecommendationData || !resolvedRecommendationData?.recommendationsData);
  if (noTopLevelData) return null;

  const { schemes: schemesArray } = resolvedRecommendationData.recommendationsData;

  const noSchemeData = isNullRenderEnabled && (!schemesArray || schemesArray?.length === 0);
  if (noSchemeData) return null;

  const { scheme: targetSchemeName, numberOfProduct } = props.data;

  const recommendations = getRecommendations({
    schemes: schemesArray,
    scheme: targetSchemeName,
    numProducts: parseNumberOfProducts({ numProducts: numberOfProduct }),
  });

  const noRecsPresent = isNullRenderEnabled && (!recommendations || recommendations?.length === 0);
  if (noRecsPresent) return null;

  return <Recommendations {...props} recommendations={recommendations} title={getTitle(schemesArray, targetSchemeName)} />;
};

export default CertonaRecs;

CertonaRecs.propTypes = {
  data: PropTypes.shape({
    numberOfProduct: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
    scheme: PropTypes.string.isRequired,
  }).isRequired,
  isLarge: PropTypes.bool.isRequired,
  Recommendations: PropTypes.elementType.isRequired,
};
