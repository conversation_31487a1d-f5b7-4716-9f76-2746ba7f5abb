// @ts-nocheck
'use client';
import React from 'react';
import PropTypes from 'prop-types';
import RecommendedProduct from './RecommendedProduct';
import { mapLoadingPlaceholders, mapRecommendations } from './helpers/map-recommendations';

const GridLayout = props => {
  const { recommendations, marketingProduct, data, isLarge } = props;
  const { numberOfProduct } = data;
  const { style: { desktop: desktopStyle = {}, mobile: mobileStyle = {} } = {} } = data.gridLayout;
  const showLoading = !recommendations || recommendations.length === 0;
  const cssStyle = isLarge ? desktopStyle : mobileStyle;
  return (
    <>
      <div className='gridProducts' style={cssStyle}>
        {!showLoading && marketingProduct ? (
          <RecommendedProduct key={marketingProduct.ID} cnt={marketingProduct.ID} product={marketingProduct} {...props} />
        ) : null}
        {!showLoading && mapRecommendations(recommendations, props)}
        {showLoading && mapLoadingPlaceholders(numberOfProduct, props)}
      </div>
    </>
  );
};

export default GridLayout;

GridLayout.propTypes = {
  data: PropTypes.shape({}).isRequired,
  isLarge: PropTypes.bool.isRequired,
  marketingProduct: PropTypes.shape({}).isRequired,
  recommendations: PropTypes.arrayOf(PropTypes.shape({})).isRequired,
};
