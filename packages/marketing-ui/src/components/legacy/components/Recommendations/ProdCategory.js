// @ts-nocheck
'use client';
import React, { Fragment } from 'react';
import PropTypes from 'prop-types';
import { withAppState } from '@ecom-next/sitewide/app-state-provider';
import { PersonalizationContext } from '@ecom-next/sitewide/personalization-provider';
import { EnabledFeaturesContext } from '@ecom-next/core/react-stitch';
import prepareDataForRecs from './helpers/prepare-for-recs';
import prepareDataForRecsAggregationServiceAPI from './helpers/prepare-for-recs-aggregation-service-api';
import getDivPrefAttribute from './helpers/get-div-pref-attribute';
import { DIV_PREF_VALUES } from './helpers/div-pref-values';
import Cookies from 'js-cookie';

export class ProdCategory extends React.Component {
  constructor(props) {
    super(props);
    this.receivedPersonalization = false;
    this.DIV_PREF_VALUES = DIV_PREF_VALUES[this.props.brandName];

    let params;
    if (typeof window !== 'undefined') {
      params = new URLSearchParams(window.location.search);
    } else {
      params = new URLSearchParams();
    }
    try {
      const previewButtonCookieValues = JSON.parse(Cookies.get('previewButtonValues')) || {};
      this.previewDate = previewButtonCookieValues.previewDate || params.get('previewDate') || '';
      this.previewMode = previewButtonCookieValues.previewMode || params.get('previewMode') || '';
    } catch (e) {
      this.previewDate = params.get('previewDate') || '';
      this.previewMode = params.get('previewMode') || '';
    }

    this.state = {
      productList: [],
    };
    this.tid = false;
    this.categoryCID = null;
  }

  componentDidMount() {
    const { useDivPref, cid, apiKey } = this.props.data;
    if (!useDivPref) {
      this.getProductList(cid, apiKey);
    }
  }

  static contextType = EnabledFeaturesContext;

  /**
   * accepts data argument (from Personalization Provider) and
   * returns Division Preference Name if one exists (WOMEN is the default)
   * otherwise returns false.
   */
  getDivPrefFromPersonalizationData = data => {
    if (!data.isEmpty && this.receivedPersonalization === false) {
      this.receivedPersonalization = true;
      return getDivPrefAttribute(data, this.DIV_PREF_VALUES.personalizationAttribute) || 'WOMEN';
    }
    return false;
  };

  /**
   * ajax request to requestUrl
   * response is saved in State variable
   */
  getProductList = (divPrefCID, apiKey) => {
    let fetchUrl = this.props.data.requestUrl + divPrefCID;
    if (this.previewDate && this.context.enabledFeatures['at-homepage-recommendations-preview'] === true) {
      fetchUrl += `&previewDate=${encodeURIComponent(this.previewDate)}&mode=${this.previewMode}&ignoreInventory=false`;
    }
    fetch(fetchUrl)
      .then(res => res.json())
      .then(data => {
        const productCategory = data?.productCategoryFacetedSearch?.productCategory;
        const isAggregationServiceAPI = data?.products?.length > 0 && data?.categories?.length > 0;

        if (productCategory) {
          let childProducts = productCategory?.childProducts || [];
          let cid = '';
          if (productCategory.childCategories && Array.isArray(productCategory.childCategories) && productCategory.childCategories.length > 0) {
            childProducts = productCategory.childCategories[0].childProducts; // Array form
            cid = productCategory.childCategories[0].businessCatalogItemId || '';
          } else if (productCategory.childCategories) {
            childProducts = productCategory.childCategories.childProducts; // Object form
            cid = productCategory.childCategories.businessCatalogItemId || '';
          }
          if (childProducts.length > 0) {
            this.setState({
              productList: prepareDataForRecs(childProducts, this.props.data.numberOfProduct, divPrefCID, cid) || [],
            });
          }
        } else if (isAggregationServiceAPI) {
          const selectedCategory = data.categories.find(category => category.categoryId === divPrefCID);
          let productList =
            prepareDataForRecsAggregationServiceAPI({
              ccList: selectedCategory.ccList,
              products: data.products,
              numProducts: this.props.data.numberOfProduct,
              pcid: divPrefCID,
              cid: divPrefCID,
              enabledFlags: this.context.enabledFeatures, // featureFlags
              brandName: this.props.brandName,
            }) || [];
          if (this.context.enabledFeatures['homepage-recommendations-limit-to-50'] === true) {
            productList = productList.filter(product => !product.ImageURL?.includes('undefined') && !product.LightWeightImageURL?.includes('undefined'));
          }
          this.setState({
            productList,
          });
        }
      })
      .catch(err => {
        console.error(err);
      });
  };

  /**
   * check if there is a new DIVPREF (Division Preference) in
   * the data returned from Personalization Provider component.
   * if there is, get-product-list.
   */
  checkForNewDivPref = data => {
    const divPrefName = this.getDivPrefFromPersonalizationData(data);
    if (divPrefName) {
      const divPrefCID = this.DIV_PREF_VALUES.categoryIds[divPrefName].cid;
      if (this.categoryCID !== divPrefCID) {
        this.categoryCID = divPrefCID;
        this.tid = this.DIV_PREF_VALUES.categoryIds[divPrefName].tid;
        this.getProductList(this.categoryCID, this.props.data.apiKey);
      }
    }
  };

  render() {
    const { Recommendations, ...restProps } = this.props;
    const useDivPref = restProps.data.useDivPref;

    return (
      <Fragment>
        {useDivPref ? <PersonalizationContext.Consumer>{data => this.checkForNewDivPref(data)}</PersonalizationContext.Consumer> : null}
        <Recommendations {...restProps} recommendations={this.state.productList} tid={this.tid} />
      </Fragment>
    );
  }
}

export default withAppState(({ brandName }) => ({ brandName }))(ProdCategory);

ProdCategory.propTypes = {
  /**
   * brandName - used to map appropriate division preference config values
   */
  brandName: PropTypes.string.isRequired,
  /**
   * data - config form JSON API
   */
  data: PropTypes.shape({
    cid: PropTypes.string.isRequired,
    /**
     * requestUrl - endpoint url to get category data
     */
    numberOfProduct: PropTypes.number.isRequired,
    /**
     * cid - category id used as querystring parameter on endpoint url
     */
    requestUrl: PropTypes.string.isRequired,
    /**
     * numberOfProduct - number of products rendered in Recommendations component
     */
    scheme: PropTypes.string.isRequired,
    /**
     * useDivPref - boolean flag to enable Personalization data
     */
    useDivPref: PropTypes.bool.isRequired,
  }).isRequired,
  /**
   * Recommendations - component that will render here
   */
  Recommendations: PropTypes.oneOfType([PropTypes.string, PropTypes.func]).isRequired,
};
