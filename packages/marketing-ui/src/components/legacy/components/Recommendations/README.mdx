# Recommendations

## Contents

- [General Information](#general-information)
- [Full Width](#full-width)
- [Pagination](#Pagination)
- [Styling Examples](#styling-examples)
- [Styling Pricing Through JSON](#styling-pricing-through-json)
- [Recommendations - Ratings and Reviews](#recommendations-ratings-and-reviews)
- [JSON for Reference](#json-for-reference)

## General Information

This component is brand agnostic, that is, it can be used all brands. This component uses the React-stitch design system for brand styles.

The Recommendations component uses either a **grid layout** or **carousel layout**. It can receive data from different data sources, such as Certona and ProductCategory. The file below shows how data is fetched from `productCategory`.

> marketing-ui/components/Recommendations/ProdCategory

`RecommendedProduct` is the carousel item: a simple product with image, name and so on.

You define which source you want products to come from. This is done by modifying `data.source` in the JSON. It can either be `productCategory` or `certona`. Please make sure to provide the **API Key** when using `productCategory`.

### API Key

It is **necessary** to provide the correct `apiKey` when using `productCategory` which will make a request to the Product Discovery Service API (i.e. https://api.gap.com/ux/web/productdiscovery-web-experience/products/us/gap).

```json
  "apiKey": "YOUR-APIKEY"
```

Click here for documentation on the [Product Discovery Service API](https://productdiscovery-service-test.apps.cfplatform.dev.phx.gaptech.com/api-docs/#/default/get_search__market___brand_)

Click here For more information on [getting an API key for your team](https://confluence.gapinc.com/pages/viewpage.action?pageId=305925455#APIProduct/ProxyAccessFAQ-3.HowdoIcreateaDevApp?)

### Aggregation Service API

Click here for documentation on the [Aggregation Service API](https://product-search-aggregation-service-stage.apps.cfcommerce.dev.azeus.gaptech.com/webjars/swagger-ui/index.html#/Aggregation%2[…]ice%20V2/getCC)

```json
  "requestUrl": "https://api.gap.com/commerce/search/products/v2/cc?brand=on&market=us&locale=en_US&pageSize=10&cid=",
  "cid": "1119888",
```

`apiKey` is no longer needed when using `Aggregation Service` which will make a request to the Aggregation Service API (i.e. "/api.gap.com/commerce/search/products/v2/cc").

`PageSize` **always** include PageSize in the URL for performance reasons. If no page size is given the default could be over 100 and have a negative impact on performance.

### For Pulling Products Directly From Product Catalog

It is **necessary** to add in a correct `cid` and `requestUrl` that corresponds to the appropriate endpoint where products are getting pulled from. The full endpoint looks something like this:

> https://brol.wip.prod.gaptecholapps.com/resources/productSearch/v1/search?isFacetsEnabled=true&pageId=0&cid=1116990

In the JSON that is returned at the URL above, notice how the cid of 1116990 has a key of **`childProducts`** and not **`childCategories`**. It will not work if **`childCategories`** is present.

You can also modify how many products you get back directly from the catalog by modifying `data.numberOfProduct`.

### For Pulling Products Using Certona

The number of products pulled from Certona is configurable using the `numberOfProduct` key.

You have control on how you want the layout to look. This is done by modifying `data.layout` to either `grid` or `carousel`

If you choose `grid` you can specify the number of products that appear in the row by changing the number for `desktop` and `mobile` respective to desktop and mobile nested in the `productsPerRow` data of the json

**AT has an optional marketing image** added to the carousel, style can be configurable via JSON.

**Headline is no longer part of Recommendations**. If you want a headline on your Certona, you have to create an instance of `TextHeadline` component.

**Additional Titles** are added from Certona if the `displayTitle` flag is set to true and the source is `certona`. You can override this title or add a title to non-Certona recommendations by adding to the `title` key under `certonaTitle`.

**You may not style the placeholder container**. A new prop `placeholderStyles` has been added to the component.

### `placeholderStyles`

Object that takes css styles, to adjust the container housing the HomeLoadingPlaceholder component, which shows the loading animation, while Certona products are being populated. This is only used when loading Certona products.

### React Slider Presentation and Configuration Through JSON

For the configuration of the slider of the carousel, we use a third-party library called React Slick. For more information, see [The Slick documentation](https://react-slick.neostack.com/docs/api/).

**Note:** the Slick slider changes from the Desktop Slick slider to the Mobile Slick slider at `768px`.

#### defaultslidesToShowSlick

You can set the number of displayed Recommendations in the carousel with `defaultslidesToShowSlick`. The mobile default is `2.5` slides shown, and the desktop default is `5` slides shown.

#### defaultslidesToScrollSlick

You can choose the number of slides to scroll with `defaultslidesToScrollSlick`. The mobile default is `2` to scroll and, the desktop is `5` to scroll.

#### autoplay

Default: false. When enabled the carousel will automatically change slides. The time which each slide is shown can be adjusted using the `autoplaySpeed` prop.

> Accessibility standards don't allow for auto-play. In rare, well designed cases a play/pause button can be added that works with voice-over and tabbed navigation.

When autoplay is enabled a play/pause button will be displayed by default. For more information on this, check [core-ui Carousel documentation](/info/common-carousel--auto-play)

#### autoplaySpeed

Default: 500. Number of milliseconds the slide will show when `autoplay` is true.

#### pauseOnHover

When `autoplay` is true, setting this option to `true` will cause the autoplay to pause when you hover the mouse over any slide.

#### `arrows`

By default, `arrows` is false on mobile and true on desktop. UX would prefer that arrows are _not_ used on mobile. This option is only configurable on mobile. On desktop, arrows will always show.

#### `arrowMaxWidth`

When arrows are active, the `arrowMaxWidth` prop adjusts the `max-width` property of the carousel's arrows. The default value is 20px. The `max-width` will not change the width of the arrows, it will constrain the arrows to the given width.

#### `arrowPosition`

When arrows are active, `arrowPosition` adjusts horizontally the arrow's position relative to the carousel slides. Default position is 50px.

#### `arrowVerticalPosition`

When arrows are active, `arrowVerticalPosition` adjusts vertically the arrow's position relative to the carousel slides. By default, arrows are placed at the middle of the carousel. Values can be positive (will move arrows down) or negative (will move arrows up). You can use any valid CSS property (`50%`, `-12px`, `5rem`...).

#### `useMobileConfig` - Configurable on Mobile Slider Only

This is false by default. If true, this will allow you to specify slick slider overrides for the Mobile slick slider.

You can pass through the following props in the data object to alter the functionality and behavior of the slick slider. If you do not specify one of the following, it will fallback gracefully to the default that has been set:

```json
data: {
  "dots": false,
  "arrows": false,
  "infinite": true,
  "speed": 300,
  "centerMode": false,
  "defaultslidesToShowSlick": 2.5,
  "defaultslidesToScrollSlick": 2,
  "swipeToSlide": false,
  "nextArrowSlick": "/Asset_Archive/BRWeb/content/0014/678/755/assets/Certona_Right_Arrow_OFF.svg",
  "prevArrowSlick": "/Asset_Archive/BRWeb/content/0014/678/755/assets/Certona_Right_Arrow_OFF.svg"
}
```

- Note that for the `defaultslidesToScrollSlick` prop on mobile, that it doesn't appear to take effect unless you're using `arrows: true`. However, we recommend not using arrows on mobile as the area to click is too small for a good touch experience.

#### resslidesToShowSlick and resslidesToScrollSlick - Configurable on Desktop Slider Only

To configure `Recommendations` to be responsive on desktop, you can set how many slides show and scroll below desktop sized viewports with the `resslidesToShowSlick` and `resslidesToScrollSlick` props:

```json
responsive: [
  {
    "breakpoint": 1350,
    "settings": {
      "slidesToShow": data.resslidesToShowSlick,
      "slidesToScroll": data.resslidesToScrollSlick,
    },
  },
]
```

#### Autoplay Options

There are some options when it comes to a Carousel autoplay experience.

##### default

This is the experience we recommend currently due to being the more stable one.
With this option you will have more of a slideshow autoplay experience.

You want to make sure to enable the following options to enable autoplay.

```json
{
  "autoplay": true,
  "autoplaySpeed": 1500
}
```

##### withSmoothScroll

You can achieve a smoother slide transition by making some adjustments to the default configuration.

```json
{
  "arrows": false, // disabling arrows. They don't work in this scenario
  "autoplay": true,
  "defaultslidesToShowSlick": 4,
  "defaultslidesToScrollSlick": 0.1,
  "autoplaySpeed": 0, // This will make it so slick carousel wait zero milliseconds to change slides
  "speed": 10000, // The higher the number, the slower each slide will pass by
  "cssEase": "linear",
  "pauseOnHover": true,
  "infinite": true
}
```

Although this will make the Carousel autoplay smoother, there are some drawbacks.

- Due to `defaultslidesToScrollSlick` being set to `0.1`, the user will notice a frame drop effect or stuttering that is more obvious in lower values of `speed` (which in this case makes it play faster). We recommend keeping `speed` at a high number value to minimize this effect.
  - Be aware that `pauseOnHover` will not pause the carousel until the exact number of `defaultslidesToShowSlick` are showing on screen. You are free to use whole numbers - for example `"defaultslidesToShowSlick"=2`, but the user would have to hover and wait until two full slides were showing before triggering the pause. A number below zero would mean a shorter wait until pause.
- Arrows don't work with these settings

#### `infinite`

By default, `infinite` is set to `false` on mobile. UX does not recommend [infinite scroll for mobile](https://gapinc.atlassian.net/browse/FUI-611?focusedCommentId=1167420&page=com.atlassian.jira.plugin.system.issuetabpanels%3Acomment-tabpanel#comment-1167420).

Setting `data.infinite` to `false` will disable the "infinite" loop of the carousel, but if you do this, it is recommended you change the `defaultslidesToShowSlick` to an even number (2). Half slides (i.e. 2.5 slides) and `data.infinite` set to `false` creates a large empty space next to your slides.

If you want to show half slides (i.e. `defaultslidesToShowSlick`: 2.5), you will need to set `data.infinite` to `true`. This allows the "half slide" to show on the right side of the screen, and enables the user to continuously scroll through recommendations.

#### Whitespace at End of Carousel

The whitespace at end of carousel was fixed by removing the styles overriding slick-slide to auto `!important`. You also need to make sure the `slidesToScroll` is set to a value that divides evenly into the number of products shown to the user and the `defaultslidesToShowSlick`

## Pagination

You can add pagination to the component by passing the pagination key in the json.

In order to place the arrows in the desired location, you will need to pass in the necessary styling for it. An example
of it being used in the top left of the carousel container is shown in its storybook example.

Pagination accepts the following properties:

```
paginator: {
    showPaginator: boolean;
    paginatorStyles: {
      paginatorPrevArrowStyles: {
        // CSS styles
      };
      paginatorNextArrowStyles: {
        // CSS styles
      };
      pageNumberStyles: {
        // CSS styles
      };
    };
  };
```

### `draggable`

Default: `false`.
A boolean prop which, when set to `true`, enables the items in the carousel to be draggable,
providing a click and drag interaction model for navigating the carousel.
This can be especially useful on desktop interfaces
where a user may prefer to click and drag items rather than using navigation buttons.

In order to enable draggable items in the carousel, add the `draggable` key to your JSON configuration:

```json
{
  "draggable": true,
  "dots": false,
  "arrows": false,
  "infinite": true
}
```

### Caveats

- Feature only enabled for Desktop screen size.
- When pagination is enabled, slidesToScroll == slidesToShow.

## Full Width

`fullWidth` is a boolean (true/false) prop to allow the parent and some of its decendants to be updated with an additional class called `full-width`. This allows the Carousel to utilize the entire width of the available space within its parent.

### _Important_

When adjusting to full width, your existing configuration may not appear as expected. For example, it may display wider whitespace columns between elements. Just as described previously, proper spacing can be achieved by adjusting the size of the cards and number to display.

### Use

`fullWidth` prop is optional. To use, set the value to true. To disable, set its value to false, or, remove the prop.

```json
{
  "source": "productCategory",
  "layout": "carousel",
  "numberOfProduct": 10,
  "displayTitle": true,
  "fullWidth": true,
  ...
}
```

## Styling Examples

### Slider

Slider styling is achieved by adding the `style` property through JSON:

```json
{
  "fullWidth": false,
  "style": {
    "maxWidth": "1920px",
    "sliderContainer": {}
  }
}
```

- `fullWidth` : boolean - When enabled, overrides `maxWidth` in `style` property.
- `maxWidth` : string - Optional; by default, `maxWidth` is set to 1920px.
- `sliderContainer` : object - Optional; this styles the inner slider and track.

### Card

Card styling is the same as the default experience for the Carousel with some slight changes. For example, to achieve full width of the product images, you could adjust `width` and `max-width`.

```
  "productCardStyles": {
    "style": {
      // "width": "auto",
      "width": "100%!important", // note: !important should only be used in rare cases
      "marginBottom": "8px",
      "textAlign": "left",
      // "maxWidth": "min-content!important"
      "maxWidth": "65vh!important",
    }
```

### _Caveats_

_Setting width to 100% could create visual challenges for zoom levels. The image will retain its 100% value of the parent container. Make sure to adjust your style properties accordingly._

## Styling Pricing Through JSON

### `PriceFlag`

Boolean (true/false): allows you to show pricing beneath the product title.

Price is formatted by the localization-provider based on the current locale and language.

### `priceMarkdownRange`

Boolean: when true, allows you to show the strikethrough price with a discount range and a current range. `PriceFlag` must also be true to use this option.

### `priceNowText`

Text to disaply for price now Eg: "Now"

### `priceOffText`

Text to disaply for price off Eg: "off"

### `priceWasText`

Text to disaply for price was Eg: "Was"

### `strikeThroughOriginalPriceFlag`

Boolean (true/false): _if there is a sale_, it will add a strikethrough line over the original price text (using CSS).

### `showMarketingFlag`

Boolean (true/false): allows you to show Marketing promo text beneath the product pricing. For example, "Hot Deal," "20% Taken Off at Checkout," "Cool beans."

### `showPercentage`

Boolean (true/false): allows you to show the "percentage off" text beside the product pricing, if there is a current price that is lower than the original price. For example, "60% Off."

### `productTextStyles object`

This object allows you to style different text styles:

- productTitle
- productMarketingFlag
- productPercentage
- productPrice (original price)
- productSalePrice

For any of these pieces of text, you may pass valid CSS.

For example:

```json
"productTextStyles": {
  "productTitle": {
    "style":{
      "fontWeight": "bold",
      "text-align" : "center"
    }
  },
  "productMarketingFlag": {
    "style": {
      "color": "fuchsia",
      "font-weight": "bold"
    }
  },
  "productPercentage": {
    "style": {
      "font-weight": "bold",
      "float": "center"
    }
  },
  "productPrice": {
    "style": {
      "color" : "green",
      "float" :"center"
    }
  },
  "productSalePrice": {
    "style": {
      "color" : "red"
    }
  }
}
```

### `productCardStyles`

- Allows styling on the outer product card. To apply styles to the image within use `productCardImageStyles`.

- styles defined here WILL NOT cascade to the `productCardImageStyles` element.

### `productCardImageStyles`

- This object allows you to style the image within the product card separately from the outer product card styles.

## Recommendations - Ratings and Reviews

### CertonaRecs

As of May 2025, the [CertonaRecs component](packages/marketing-ui/src/components/legacy/components/Recommendations/CertonaRecs.js) has been set up to receive data from either our new in-house reccomendations system or our existing Certona system. Theare are both delivered via our [new Ai Reccomendations provider](packages/sitewide/src/providers/product-recs-provider/ProductRecommendationProvider.tsx).

The first page to use this will be our Home page, done via a feature flag and experiment. The implementation can be viewed in the [marketing-ui/slots/home file](packages/marketing-ui/slots/home/<USER>

### `ratings`

Object : Contains the following configuration for the certona product ratings and reviews in addition to translation labels:

```json
"ratings": {
  "ariaLabel": "Image of 5 stars, 2 are filled, 30 Reviews",
  "ariaLabelNoReview": "Image of 5 stars, 2 are filled",
  "minRating": 4, //minimum star rating to show
  "showRating": true,
  "showReviewCount": true,
  "reviewCountColor": "#000000", //font color to use for review count
  "reviewCountPluralLabel": "{{ reviewCount }} Reviews", // plural review label
  "reviewCountSingularLabel": "{{ reviewCount }} Review" // singular review label
}
```

### Localization

In order to provide localalized translations, the label props are required otherwise they won't be accessible or display labels correctly. The actual prop name itself will be displayed by default if the label prop is not set.

The label props also support placement tokens to further customize your label. For example, the 'reviewCountPluralLabel' prop supports `{{reviewCount}}` placement token to get the actual product review count.

```json
"ratings": {
  ...
  "reviewCountPluralLabel": "{{reviewCount}} Reviews",
  ...
}
```

### `ariaLabel`

aria-label to be added to the component.

- `{{startRating}}`
- `{{reviewCount}}`

### `ariaLabelNoReview`

aria-label to be added to the component when the review count is 0.

- `{{startRating}}`

### `reviewCountPluralLabel`

The plural label when review count is greater than 1.

- `{{reviewCount}}`

### `reviewCountSingularLabel`

The singular label when review count equals 1.

- `{{reviewCount}}`
  <p>&nbsp;</p>

### Tracking ID

### `tid`

String : allows to give custom tracking id for the certona product:

By default, the TID used for tracking will be created from the pageType (defined by the MFE) and combined with scheme.
Otherwise, you can use this prop to override the default TID.

```json
"tid": "some-tid"
```

## JSON for Reference

### AT

**Attention**: AT has a `MarketingOptionalImage` object

```json
{
  "name": "Recommendations",
  "type": "home",
  "tileStyle": {
    "desktop": {
      "flexBasis": "100%"
    },
    "mobile": {
      "flexBasis": "100%"
    }
  },
  "data": {
    "source" : "productCategory",
    "useDivPref": false,
    "layout": "grid",
    "lazy": true,
    "requestUrl":
      "/resources/productSearch/v1/search?isFacetsEnabled=true&pageId=0&cid=",
    "cid": "1009206",
    "apiKey": "ABC1234567",
    "numberOfProduct" : 6,
    "scheme": "athome1_rr",
    "priceFlag": true,
    "prevArrowSlick":
      "/Asset_Archive/ATWeb/content/0015/468/042/assets/Arrow.png",
    "nextArrowSlick":
      "/Asset_Archive/ATWeb/content/0015/468/042/assets/Arrow.png",
    "defaultslidesToShowSlick": 5.5,
    "defaultslidesToScrollSlick": 5,
    "resslidesToShowSlick": 3,
    "resslidesToScrollSlick": 3,
    "marketingOptionalImage": {
      "src": "/webcontent/0015/582/963/cn15582963.jpg",
      "CurrentPrice": "15.0",
      "OriginalPrice": "20.0",
      "productName": "Product Name",
      "detailURL": "/browse/product.do?pid=488420002&rrec=true",
      "minstylecurrentprice": "5.99",
      "maxstylecurrentprice": "7.49",
      "minstyleregularprice": "14.99",
      "maxstyleregularprice": "14.99",
      "CurrentPriceRange": "Now $5.99 - $7.49",
			"DiscountRange": "50%-60% off",
      "id": "marketingImage",
      "style": {
        "parent": {
          "desktop": {
            "width": "560px"
          },
          "mobile": {}
        },
        "image": {
          "desktop": {
            "width": "100%",
            "maxHeight": "321px",
            "objectFit": "cover",
            "objectPosition": "top"
          },
          "mobile": {}
        }
      }
    },
    "productTextStyles": {
      "productTitle": {
        "style":{
          "fontWeight": "bold",
          "text-align" : "center"
        }
      },
      "productPrice": {
        "style": {
          "color" : "green",
          "float" :"center"
        }
      },
      "productSalePrice": {
        "style": {
          "color" : "red"
        }
      }
    },
    "productCardStyles": {
      "style": {
        "width" : "auto",
        "margin" : "10px 0 0 10px",
        "flex-grow" : "1"
      }
    },
    "displayTitle" : true,
    "certonaTitle": {
      "title" : "",
      "style" : {
        "desktop": {
          "display" : "flex",
          "padding-bottom": "50px",
          "font-size": "100%",
          "text-align": "center"
        },
        "mobile" : {
          "display" : "flex",
          "padding-bottom": "50px",
          "font-size": "100%",
          "text-align": "center"
        }
      }
    },
    "gridLayout": {
      "style" : {
        "desktop": {
          "display": "block",
          "height": "500px",
          "overflow-y": "scroll"
      },
      "mobile" : {
        "display": "flex",
        "flex-flow": "row wrap",
        "justify-content": "space-around"
      }
    },
    "productsPerRow": {
      "desktop": 4,
      "mobile": 2
    }
  }
}
```

### ON

```
{
  "name": "Recommendations",
  "type": "home",
  "tileStyle": {},
  "data": {
    "source": "certona",
    "useDivPref": false,
    "requestUrl": "/resources/productSearch/v1/search?isFacetsEnabled=true&pageId=0&cid=",
    "cid": "1119888",
    "apiKey": "ABC1234567",
    "numberOfProduct": 20,
    "layout": "carousel",
    "lazy": false,
    "scheme": "onhome2_rr",
    "priceFlag": true,
    "ratings": {
      "minRating": 4,
      "showRating": true,
      "showReviewCount": true,
      "reviewCountColor": "#000000"
    },
    "prevArrowSlick": "/Asset_Archive/ONWeb/content/0013/268/652/assets/020416_US_DenimWovenSweaters_site_hp_v2_arrowL.png",
    "nextArrowSlick": "/Asset_Archive/ONWeb/content/0013/268/652/assets/020416_US_DenimWovenSweaters_site_hp_v2_arrowL.png",
    "defaultslidesToShowSlick": 5.5,
    "defaultslidesToScrollSlick": 5,
    "resslidesToShowSlick": 4,
    "resslidesToScrollSlick": 3,
    "priceNowText": "Now",
    "priceOffText": "off",
    "priceWasText": "Was",
    "productTextStyles": {
      "productTitle": {
        "style": {
          "fontWeight": "bold",
          "text-align": "left"
        }
      },
      "productPrice": {
        "style": {
          "color": "green",
          "float": "left"
        }
      },
      "productSalePrice": {
        "style": {
          "color": "red",
          "float": "left"
        }
      },
      "size": {
        "width": "auto",
        "height": "250px"
      }
    },
    "productCardStyles": {
      "style": {
        "width": "auto",
        "margin-bottom": "8px"
      }
    },
    "displayTitle": true,
    "certonaTitle": {
      "title": "",
      "style": {
        "desktop": {
          "display": "flex",
          "padding-bottom": "50px",
          "font-size": "100%",
          "text-align": "center"
        },
        "mobile": {}
      }
    },
    "gridLayout": {
      "style": {
        "desktop": {
          "display": "flex",
          "flex-flow": "row wrap"
        },
        "mobile": {}
      },
      "productsPerRow": {
        "desktop": 4,
        "mobile": 2
      }
    }
  }
}
```

### `product name`

New semantic HTML was added to the product name `<div>` structure.

The wrapper `<div>` for the product name now has a nested `<p>` with a class of `sds_font-tertiary` to accommodate BR new font.
You can view BR font when switching brand name (BR) and inspecting the class on the `<p>` element.
