// @ts-nocheck
'use client';
/* eslint react/no-danger: 0 */
import React, { useContext } from 'react';
import PropTypes from 'prop-types';
import { Link } from '@ecom-next/core/migration/link';
import { withAppState } from '@ecom-next/sitewide/app-state-provider';
import { BreakpointContext } from '@ecom-next/core/breakpoint-provider';
import { lazy } from 'react';
import { quickAddContainerId } from './components/CertonaRecsContainer';
import productStylePrice from './helpers/product-style-price';
import CertonaRatings from './CertonaRatings';

import {
  renderMarketingFlagText,
  renderPricing,
  renderMarkdownRangePricing,
  RenderTextParagraphAndBRFont,
  RenderMarketingTextDiv,
  RenderProductCardInnerDiv,
  RenderMarketingCleanDiv,
  RenderProductCardDiv,
} from './helpers/recommendedProductHelpers';
import useDragDetection from './helpers/use-drag-detection';

import { useLocalize } from '@ecom-next/sitewide/localization-provider';

const getStyle = (data, cnt, isLarge) => {
  if (cnt !== 'marketingImage') {
    return data.productCardStyles.style || {};
  }
  let style = data.marketingOptionalImage.style || {};
  style = style.image || {};
  style = isLarge ? style.desktop : style.mobile;
  return style || {};
};

const QuickAdd = () => <button>Quick Add</button>;

const RecommendedProduct = props => {
  const { pageType = 'home', data, product, cnt, isLarge, muiRecsQuickAddCallback } = props;
  const { localize, formatCurrency } = useLocalize();
  const { ratings = {}, withQuickAddEnabled } = data;
  const localizedRatings = { ...ratings };
  const starRating = parseFloat(product.Rating, 10);
  const reviewCount = ratings.showReviewCount ? parseFloat(product.ReviewCount, 10) : 0;
  const { productTextStyles = {} } = data;

  localizedRatings.ariaLabel =
    reviewCount === 0
      ? localize('ariaLabelNoReview', {
          starRating,
        })
      : localize('ariaLabel', {
          reviewCount,
          starRating,
        });
  localizedRatings.reviewCountLabel = localize('reviewCountLabel', {
    count: reviewCount,
    reviewCount,
  });

  /* eslint-enable react/no-danger */

  const tid = props.data.tid ?? `${pageType}_${data.scheme}`;
  const detailUrl = `${product.DetailURL}&mlink=5001,1,${tid}_${cnt}&clink=1`;
  const { smallerThan } = useContext(BreakpointContext);

  let productCardWidth = 100;
  if (data.gridLayout.productPerRow) {
    productCardWidth = 100 / (data.gridLayout.productPerRow || 1);
  } else if (data.gridLayout?.productsPerRow?.mobile && data.gridLayout.productsPerRow.desktop) {
    productCardWidth = smallerThan('large') ? 100 / (data.gridLayout.productsPerRow.mobile || 1) : 100 / (data.gridLayout.productsPerRow.desktop || 1);
  }

  let style = getStyle(data, cnt, isLarge);
  const { productCardImageStyles } = data;

  const imageCardStyles = productCardImageStyles?.style || {};
  let imageStyle;
  if (data.layout === 'grid') {
    style = {
      ...style,
      width: `${productCardWidth}%`,
    };
    imageStyle = {
      ...imageCardStyles,
      width: '100%',
    };
  } else {
    imageStyle = imageCardStyles;
  }

  const { handleMouseDown, dragging } = useDragDetection();
  function handleChildClick(e) {
    if (dragging) {
      e.preventDefault();
    }
  }

  return (
    <RenderProductCardDiv
      css={[{ display: 'inline-block' }, style]}
      data-testid='recommended-product-card'
      onClickCapture={data.draggable ? handleChildClick : undefined}
      onMouseDownCapture={data.draggable ? handleMouseDown : undefined}
    >
      <RenderProductCardInnerDiv>
        <Link to={detailUrl}>
          <RenderMarketingCleanDiv>
            <div css={{ position: 'relative' }}>
              <img alt={product.ProductName} aria-hidden='true' css={imageStyle} src={product.ImageURL} />
              {withQuickAddEnabled && (
                <QuickAdd
                  modalContainerId={quickAddContainerId}
                  onAddToBag={muiRecsQuickAddCallback}
                  productId={product.ID}
                  productName={product.Name}
                  reviewCount={reviewCount}
                  starRating={starRating}
                />
              )}
            </div>
            <RenderMarketingTextDiv style={productStylePrice(productTextStyles, 'productTitle')}>
              <RenderTextParagraphAndBRFont
                // recieves data from a Gap API, need to leave dangerouslySetInnerHTML becuase we get HTML elements
                // as part of the response
                dangerouslySetInnerHTML={{
                  __html: product.ProductName.replace(/(&#\d*;?)/g, ''),
                }}
              />
            </RenderMarketingTextDiv>
            {data.priceFlag && data.priceMarkdownRange ? renderMarkdownRangePricing(props, formatCurrency) : null}
            {data.priceFlag && !data.priceMarkdownRange ? renderPricing(props, formatCurrency) : null}
            {data.showMarketingFlag ? renderMarketingFlagText(props) : null}
            {ratings.showRating ? <CertonaRatings product={product} ratings={localizedRatings} tabIndex={-1} /> : null}
          </RenderMarketingCleanDiv>
        </Link>
      </RenderProductCardInnerDiv>
    </RenderProductCardDiv>
  );
};

RecommendedProduct.propTypes = {
  /**
   * used to for tid/tracking index
   */
  cnt: PropTypes.number,
  /**
   * config form JSON API
   */
  data: PropTypes.shape({
    /**
     * Mobile/Desktop styles from props
     */
    gridLayout: PropTypes.shape({
      /**
       * integer
       */
      productsPerRow: PropTypes.shape({
        desktop: PropTypes.number,
        mobile: PropTypes.number,
      }),
    }),
    /**
     * bool
     */
    priceFlag: PropTypes.bool,

    /**
     * price label strings
     */
    priceNowText: PropTypes.string,

    priceOffText: PropTypes.string,

    priceWasText: PropTypes.string,
    /**
     * to style image only (WCD)
     */
    productCardImageStyles: PropTypes.shape({
      style: PropTypes.object,
    }),
    /**
     * CSS style helper objects
     */
    productTextStyles: PropTypes.shape({
      productPrice: PropTypes.object,
      productSalePrice: PropTypes.object,
      productTitle: PropTypes.object,
    }),
    /**
     * config for rating
     */
    ratings: PropTypes.shape({
      /**
       * minimum rating of the product
       */
      minRating: PropTypes.number,
      /**
       * font color for review count
       */
      reviewCountColor: PropTypes.string,
      /**
       * show rating of the product
       */
      showRating: PropTypes.bool,
      /**
       * show review count of the product
       */
      showReviewCount: PropTypes.bool,
    }),
    /**
     * criteria for which products to show (from Tealium)
     */
    scheme: PropTypes.string.isRequired,
    /**
     * if product on sale
     */
    showMarketingFlag: PropTypes.bool,
    /**
     * show '%' difference from original price
     */
    showPercentage: PropTypes.bool,
    /**
     * would set css style if true
     */
    strikeThroughOriginalPriceFlag: PropTypes.bool,
    /**
     * tracking ID (WCD)
     */
    tid: PropTypes.string,
  }),
  /**
   * viewport size is at-least 768px
   */
  isLarge: PropTypes.bool.isRequired,
  /**
   * single object data for product
   */
  product: PropTypes.shape({
    /**
     * the selling price
     */
    CurrentPrice: PropTypes.string.isRequired,
    /**
     * link to the product
     */
    DetailURL: PropTypes.string.isRequired,
    /**
     * cached asset image
     */
    ImageURL: PropTypes.string.isRequired,
    /**
     * promotion description
     */
    MarketingFlag: PropTypes.string,
    /**
     * natural price before discount
     */
    OriginalPrice: PropTypes.string.isRequired,
    /**
     * detailed product name
     */
    ProductName: PropTypes.string.isRequired,
  }).isRequired,
};

RecommendedProduct.defaultProps = {
  cnt: 0,
  product: {
    MarketingFlag: null,
  },
  data: {
    ratings: {
      minRating: 0,
      reviewCountColor: '#000000',
      showRating: false,
      showReviewCount: false,
    },
    showMarketingFlag: false,
    showPercentage: false,
    priceFlag: false,
    priceNowText: '',
    priceOffText: '',
    priceWasText: '',
    strikeThroughOriginalPriceFlag: false,
    productTextStyles: {
      productPrice: {},
      productSalePrice: {},
      productTitle: {},
    },
    gridLayout: {
      productsPerRow: {
        desktop: 1,
        mobile: 1,
      },
    },
    tid: undefined,
    productCardImageStyles: {
      style: {},
    },
  },
};

export default withAppState(({ brandName, pageType, muiRecsQuickAddCallback }) => ({
  brandName,
  pageType,
  muiRecsQuickAddCallback,
}))(RecommendedProduct);
