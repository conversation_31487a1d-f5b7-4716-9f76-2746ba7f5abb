// @ts-nocheck
'use client';
/* eslint react/no-danger: 0 */
import React from 'react';
import { LoadingPlaceholder } from '@ecom-next/core/legacy/loading-placeholder';
import { withAppState } from '@ecom-next/sitewide/app-state-provider';
import { RenderProductCardInnerDiv, RenderProductCardDiv } from './helpers/recommendedProductHelpers';

const defaultPlaceholderStyles = { flex: 1 };

const getStyles = (data, isLarge) => {
  let style = data.placeholderStyles || {};
  style = isLarge ? style.desktop : style.mobile;
  return style || defaultPlaceholderStyles;
};

const getSettings = data => data.placeholderSettings || {};

const RecsPlaceholderContainer = props => {
  const { data, isLarge } = props;
  const settings = getSettings(data);
  const styles = getStyles(data, isLarge);

  return (
    <RenderProductCardDiv css={[styles]} data-testid='recommended-placeholder-card'>
      <RenderProductCardInnerDiv>
        <LoadingPlaceholder ratio={{ width: 1, height: 2 }} {...settings} />
      </RenderProductCardInnerDiv>
    </RenderProductCardDiv>
  );
};

export const RecsPlaceholder = withAppState(({ brandName, pageType }) => ({
  brandName,
  pageType,
}))(RecsPlaceholderContainer);
