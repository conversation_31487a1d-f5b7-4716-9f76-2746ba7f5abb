// @ts-nocheck
import { arrow, construction, critterHood, dinosaurs, excavation, fossils, headsAndTails, oceanStickers, oneLove, theOcean, usPuzzle } from './assets';

export const certonaContextValue = {
  data: {
    schemes: [
      {
        scheme: 'onoos1_rr',
        explanation: "WE THINK YOU'LL LOVE THESE",
        display: 'yes',
        items: [
          {
            ID: '868975002',
            ProductName: "<i>Let's STEP&#153 Books to Grow On: Heads and Tails</i> Board Book for Toddler & Baby",
            ImageURL: headsAndTails,
            LightWeightImageURL: headsAndTails,
            DetailURL: '/browse/product.do?pid=868975002&rrec=true',
            instock: 'False',
            OriginalPrice: '12.00',
            CurrentPrice: '12.00',
            MarketingFlag: "Hi, I'm New",
            PromotionDisplay: '',
            Rating: '3.5',
            ReviewCount: '349',
            minStyleCurrentPrice: '1.0',
            maxStyleCurrentPrice: '2.0',
            minStyleRegularPrice: '3.0',
            maxStyleRegularPrice: '4.0',
            CurrentPriceRange: 'Now $14.99 - $16.49',
            DiscountRange: '70%-73% off',
          },
          {
            ID: '836507002',
            ProductName: '<i>Touch and Explore&#153 The Ocean</i> Board Book for Toddler & Baby ',
            ImageURL: theOcean,
            LightWeightImageURL: theOcean,
            DetailURL: '/browse/product.do?pid=836507002&rrec=true',
            instock: 'False',
            OriginalPrice: '15.00',
            CurrentPrice: '15.00',
            MarketingFlag: "Hi, I'm New",
            PromotionDisplay: '',
            Rating: '4.9',
            ReviewCount: '557',
          },
          {
            ID: '733508002',
            ProductName: '<i>Goodnight, Goodnight, Construction Site</i> Board Book for Toddler & Baby',
            ImageURL: construction,
            LightWeightImageURL: construction,
            DetailURL: '/browse/product.do?pid=733508002&rrec=true',
            instock: 'False',
            OriginalPrice: '8.00',
            CurrentPrice: '8.00',
            MarketingFlag: "Hi, I'm New",
            PromotionDisplay: '',
            Rating: '2.7',
            ReviewCount: '17',
          },
          {
            ID: '760202002',
            ProductName: '<i>In the Age of Dinosaurs: My Nature Sticker Activity Book</i> Activity Book for Kids',
            ImageURL: dinosaurs,
            LightWeightImageURL: dinosaurs,
            DetailURL: '/browse/product.do?pid=760202002&rrec=true',
            instock: 'False',
            OriginalPrice: '8.00',
            CurrentPrice: '8.00',
            MarketingFlag: "Hi, I'm New",
            PromotionDisplay: '',
            Rating: '5',
            ReviewCount: '2122',
          },
          {
            ID: '736552002',
            ProductName: '<i>In the Ocean: My Nature Sticker Activity Book</i> Activity Book for Kids',
            ImageURL: oceanStickers,
            LightWeightImageURL: oceanStickers,
            DetailURL: '/browse/product.do?pid=736552002&rrec=true',
            instock: 'False',
            OriginalPrice: '8.00',
            CurrentPrice: '8.00',
            MarketingFlag: "Hi, I'm New",
            PromotionDisplay: '',
            Rating: '3.5',
            ReviewCount: '3',
          },
          {
            ID: '701467002',
            ProductName: '<i>One Love</i> Picture Book for Kids (Based on the Song by Bob Marley ',
            ImageURL: oneLove,
            LightWeightImageURL: oneLove,
            DetailURL: '/browse/product.do?pid=701467002&rrec=true',
            instock: 'False',
            OriginalPrice: '17.00',
            CurrentPrice: '17.00',
            MarketingFlag: "Hi, I'm New",
            PromotionDisplay: '',
            Rating: '1.6',
            ReviewCount: '548',
          },
          {
            ID: '705357002',
            ProductName: 'Mudpuppy&#153 The United States 1000-Piece Puzzle for the Family',
            ImageURL: usPuzzle,
            LightWeightImageURL: usPuzzle,
            DetailURL: '/browse/product.do?pid=705357002&rrec=true',
            instock: 'False',
            OriginalPrice: '17.00',
            CurrentPrice: '17.00',
            MarketingFlag: 'Save More with Code at Checkout',
            PromotionDisplay: '',
            Rating: '4.7',
            ReviewCount: '3000',
          },
          {
            ID: '788640012',
            ProductName: 'Adventure Club Glow-in-the-Dark Fossils Activity Kit for Kids',
            ImageURL: fossils,
            LightWeightImageURL: fossils,
            DetailURL: '/browse/product.do?pid=788640012&rrec=true',
            instock: 'False',
            OriginalPrice: '12.00',
            CurrentPrice: '12.00',
            MarketingFlag: "Hi, I'm New",
            PromotionDisplay: '',
            Rating: '5',
            ReviewCount: '567',
          },
          {
            ID: '647331002',
            ProductName: 'Micro Performance Critter Hooded Blanket for Toddler',
            ImageURL: critterHood,
            LightWeightImageURL: critterHood,
            DetailURL: '/browse/product.do?pid=647331002&rrec=true',
            instock: 'False',
            OriginalPrice: '12.00',
            CurrentPrice: '10.00',
            MarketingFlag: 'Best Seller',
            PromotionDisplay: '',
            Rating: '4.2',
            ReviewCount: '897',
          },
          {
            ID: '764841002',
            ProductName: 'Adventure Club 10-in-1 Treasure Excavation Mega Dig Kit for Kids ',
            ImageURL: excavation,
            LightWeightImageURL: excavation,
            DetailURL: '/browse/product.do?pid=764841002&rrec=true',
            instock: 'False',
            OriginalPrice: '16.00',
            CurrentPrice: '16.00',
            MarketingFlag: "Hi, I'm New",
            PromotionDisplay: '',
            Rating: '3.5',
            ReviewCount: '422',
          },
        ],
      },
    ],
  },
};

export const certonaData = {
  defaultHeight: '0px',
  placeholderSettings: {
    useGreyLoadingEffect: false,
    desktop: {
      paddingBottom: 0,
      width: 0,
      height: 0,
    },
    mobile: {
      width: 0,
      height: 0,
    },
  },
  source: 'certona',
  layout: 'grid',
  lazy: false,
  scheme: 'onoos1_rr',
  priceFlag: true,
  priceMarkdownRange: true,
  customBrand: 'ON',
  prevArrowSlick: arrow,
  nextArrowSlick: arrow,
  prevArrowAlt: 'previous recommendation',
  nextArrowAlt: 'next recommendation',
  defaultslidesToShowSlick: 5,
  defaultslidesToScrollSlick: 2.5,
  resslidesToShowSlick: 4,
  resslidesToScrollSlick: 3,
  priceNowText: 'Now',
  priceOffText: 'off',
  priceWasText: 'Was',
  fullWidth: false,
  style: {
    sliderContainer: {},
  },
  productTextStyles: {
    productTitle: {
      style: {
        fontWeight: 'bold',
        textAlign: 'center',
      },
    },
    productPrice: {
      style: {
        display: 'none',
        color: 'green',
        float: 'center',
      },
    },
    productSalePrice: {
      style: {
        display: 'none',
        color: 'red',
        float: 'center',
      },
    },
    size: {
      width: 'auto',
      height: '250px',
    },
  },
  productCardStyles: {
    style: {
      width: '100%',
      marginBottom: '8px',
    },
  },
  numberOfProduct: 6,
  displayTitle: true,
  certonaTitle: {
    title: "We think you'll love these",
    style: {
      desktop: {
        display: 'block',
        textAlign: 'center',
        margin: 'auto 0',
        textTransform: 'uppercase',
        letterSpacing: '.1em',
        padding: '1em 0',
        fontSize: '1.2rem',
        fontWeight: '900',
      },
      mobile: {
        display: 'block',
        textAlign: 'center',
        margin: 'auto 0',
        textTransform: 'uppercase',
        letterSpacing: '.1em',
        padding: '1em 0',
        fontSize: '1.2rem',
        fontWeight: '900',
      },
    },
  },
  gridLayout: {
    style: {
      desktop: {
        display: 'flex',
        flexFlow: 'row wrap',
      },
      mobile: {
        display: 'flex',
        flexFlow: 'row wrap',
      },
    },
    productsPerRow: {
      desktop: 3,
      mobile: 2,
    },
    marketingProduct: {},
  },
};
