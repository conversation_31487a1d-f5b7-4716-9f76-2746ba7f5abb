// @ts-nocheck
const construction = require('./certona/construction-site.jpg').default?.src;
const critterHood = require('./certona/critter-hood.jpg').default?.src;
const dinosaurs = require('./certona/dinosaurs-sticker-book.jpg').default?.src;
const excavation = require('./certona/excavation-kit.jpg').default?.src;
const fossils = require('./certona/fossils.jpg').default?.src;
const headsAndTails = require('./certona/heads-and-tails.jpg').default?.src;
const oceanStickers = require('./certona/ocean-sticker-book.jpg').default?.src;
const oneLove = require('./certona/one-love.jpg').default?.src;
const theOcean = require('./certona/the-ocean.jpg').default?.src;
const usPuzzle = require('./certona/us-puzzle.jpg').default?.src;
const blackAnkleJeans = require('./default/black-ankle-jeans.jpg').default?.src;
const boyfriendJeans = require('./default/boyfriend-jeans.jpg').default?.src;
const pufferJacket = require('./default/diamond-puffer-jacket.jpg').default?.src;
const flannel = require('./default/flannel.jpg').default?.src;
const rippedJeans = require('./default/high-waisted-ripped-jeans.jpg').default?.src;
const jeanShirt = require('./default/jean-shirt.jpg').default?.src;
const sweatshirt = require('./default/mock-neck-sweatshirt.jpg').default?.src;
const ankleJeans = require('./default/straight-ankle-jeans.jpg').default?.src;
const vNeck = require('./default/tie-dye-v-neck.jpg').default?.src;
const blouse = require('./default/tie-sleeve-blouse.jpg').default?.src;
const arrow = require('./arrow/arrow.png').default?.src;

export {
  arrow,
  construction,
  critterHood,
  dinosaurs,
  excavation,
  fossils,
  headsAndTails,
  oceanStickers,
  oneLove,
  theOcean,
  usPuzzle,
  blackAnkleJeans,
  boyfriendJeans,
  pufferJacket,
  flannel,
  rippedJeans,
  jeanShirt,
  sweatshirt,
  ankleJeans,
  vNeck,
  blouse,
};
