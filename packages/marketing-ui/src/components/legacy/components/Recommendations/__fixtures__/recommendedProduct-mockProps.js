// @ts-nocheck
export const mockProps = {
  brandName: 'on',
  isLarge: true,
  product: {
    ID: '372436002',
    ImageURL: 'some/url.jpg',
    ProductName: 'Denim Pants',
    DetailURL: 'some/detail/url.do?',
    CurrentPrice: '59.99',
    OriginalPrice: '89.99',
    PromotionDisplay: '',
    InStock: 'true',
    LightWeightImageURL: 'https://www4.assets-gap.com/webcontent/0016/273/931/cn16273931.jpg',
    MarketingFlag: 'More Colors Available',
    Rating: '3.5',
    ReviewCount: '18',
  },
  data: {
    showPercentage: true,
    source: 'productCategory',
    apiKey: 'ABC1234567',
    useDivPref: true,
    requestUrl: '/resources/productSearch/v1/search?isFacetsEnabled=true&pageId=0&cid=',
    cid: '1119888',
    numberOfProduct: 20,
    layout: 'grid',
    lazy: false,
    scheme: 'onhome2_rr',
    priceFlag: true,
    priceNowText: 'Now',
    priceOffText: 'off',
    priceWasText: 'Was',
    ratings: {
      showRating: true,
      minRating: 3,
      showReviewCount: true,
      reviewCountColor: '#000000',
    },
    prevArrowSlick: '/Asset_Archive/ONWeb/content/0013/268/652/assets/020416_US_DenimWovenSweaters_site_hp_v2_arrowL.png',
    nextArrowSlick: '/Asset_Archive/ONWeb/content/0013/268/652/assets/020416_US_DenimWovenSweaters_site_hp_v2_arrowL.png',
    defaultslidesToShowSlick: 5,
    defaultslidesToScrollSlick: 2.5,
    resslidesToShowSlick: 4,
    resslidesToScrollSlick: 3,
    productTextStyles: {
      productTitle: {
        style: {
          fontWeight: 'bold',
          textAlign: 'left',
        },
      },
      productPrice: {
        style: {
          color: 'green',
          textAlign: 'left',
        },
      },
      productSalePrice: {
        style: {
          color: 'red',
        },
      },
      size: {
        width: 'auto',
        height: '250px',
      },
    },
    productCardStyles: {
      style: {
        width: 'auto',
        marginBottom: '8px',
        border: '2px solid green',
      },
    },
    productCardImageStyles: {
      style: {
        border: '13px solid purple',
      },
    },
    gridLayout: {
      style: {},
      productsPerRow: {
        desktop: 4,
        mobile: 2,
      },
    },
  },
};

export const mockPropsWithDraggable = {
  ...mockProps,
  data: { ...mockProps.data, draggable: true },
};
export const mockPropsWithOutTextProps = {
  brandName: 'on',
  isLarge: true,
  product: {
    ID: '372436002',
    ImageURL: 'some/url.jpg',
    ProductName: 'Denim Pants',
    DetailURL: 'some/detail/url.do?',
    CurrentPrice: '59.99',
    OriginalPrice: '89.99',
    PromotionDisplay: '',
    InStock: 'true',
    LightWeightImageURL: 'https://www4.assets-gap.com/webcontent/0016/273/931/cn16273931.jpg',
    MarketingFlag: 'More Colors Available',
    Rating: '3.5',
    ReviewCount: '18',
  },
  data: {
    showPercentage: true,
    source: 'productCategory',
    apiKey: 'ABC1234567',
    useDivPref: true,
    requestUrl: '/resources/productSearch/v1/search?isFacetsEnabled=true&pageId=0&cid=',
    cid: '1119888',
    numberOfProduct: 20,
    layout: 'grid',
    lazy: false,
    scheme: 'onhome2_rr',
    priceFlag: true,
    ratings: {
      showRating: true,
      minRating: 3,
      showReviewCount: true,
      reviewCountColor: '#000000',
    },
    prevArrowSlick: '/Asset_Archive/ONWeb/content/0013/268/652/assets/020416_US_DenimWovenSweaters_site_hp_v2_arrowL.png',
    nextArrowSlick: '/Asset_Archive/ONWeb/content/0013/268/652/assets/020416_US_DenimWovenSweaters_site_hp_v2_arrowL.png',
    defaultslidesToShowSlick: 5,
    defaultslidesToScrollSlick: 2.5,
    resslidesToShowSlick: 4,
    resslidesToScrollSlick: 3,
    productTextStyles: {
      productTitle: {
        style: {
          fontWeight: 'bold',
          textAlign: 'left',
        },
      },
      productPrice: {
        style: {
          color: 'green',
          textAlign: 'left',
        },
      },
      productSalePrice: {
        style: {
          color: 'red',
        },
      },
      size: {
        width: 'auto',
        height: '250px',
      },
    },
    productCardStyles: {
      style: {
        width: 'auto',
        marginBottom: '8px',
        border: '2px solid green',
      },
    },
    productCardImageStyles: {
      style: {
        outline: '13px solid purple',
      },
    },
    gridLayout: {
      style: {},
      productsPerRow: {
        desktop: 4,
        mobile: 2,
      },
    },
  },
};

export const mockPropsWithoutImageStyles = {
  brandName: 'on',
  isLarge: true,
  product: {
    ID: '372436002',
    ImageURL: 'some/url.jpg',
    ProductName: 'Denim Pants',
    DetailURL: 'some/detail/url.do?',
    CurrentPrice: '59.99',
    OriginalPrice: '89.99',
    PromotionDisplay: '',
    InStock: 'true',
    LightWeightImageURL: 'https://www4.assets-gap.com/webcontent/0016/273/931/cn16273931.jpg',
    MarketingFlag: 'More Colors Available',
    Rating: '3.5',
    ReviewCount: '18',
  },
  data: {
    showPercentage: true,
    source: 'productCategory',
    apiKey: 'ABC1234567',
    useDivPref: true,
    requestUrl: '/resources/productSearch/v1/search?isFacetsEnabled=true&pageId=0&cid=',
    cid: '1119888',
    numberOfProduct: 20,
    layout: 'grid',
    lazy: false,
    scheme: 'onhome2_rr',
    priceFlag: true,
    priceNowText: 'Now',
    priceOffText: 'off',
    priceWasText: 'Was',
    ratings: {
      showRating: true,
      minRating: 3,
      showReviewCount: true,
      reviewCountColor: '#000000',
    },
    prevArrowSlick: '/Asset_Archive/ONWeb/content/0013/268/652/assets/020416_US_DenimWovenSweaters_site_hp_v2_arrowL.png',
    nextArrowSlick: '/Asset_Archive/ONWeb/content/0013/268/652/assets/020416_US_DenimWovenSweaters_site_hp_v2_arrowL.png',
    defaultslidesToShowSlick: 5,
    defaultslidesToScrollSlick: 2.5,
    resslidesToShowSlick: 4,
    resslidesToScrollSlick: 3,
    productTextStyles: {
      productTitle: {
        style: {
          fontWeight: 'bold',
          textAlign: 'left',
        },
      },
      productPrice: {
        style: {
          color: 'green',
          textAlign: 'left',
        },
      },
      productSalePrice: {
        style: {
          color: 'red',
        },
      },
      size: {
        width: 'auto',
        height: '250px',
      },
    },
    productCardStyles: {
      style: {
        color: 'red',
        marginBottom: '8px',
      },
    },
    gridLayout: {
      style: {},
      productsPerRow: {
        desktop: 4,
        mobile: 2,
      },
    },
  },
};

export const mockPropsWithRange = {
  brandName: 'on',
  isLarge: true,
  product: {
    ID: '372436002',
    ImageURL: 'some/url.jpg',
    ProductName: 'Denim Pants',
    DetailURL: 'some/detail/url.do?',
    CurrentPrice: '59.99',
    OriginalPrice: '89.99',
    PromotionDisplay: '',
    InStock: 'true',
    LightWeightImageURL: 'https://www4.assets-gap.com/webcontent/0016/273/931/cn16273931.jpg',
    MarketingFlag: 'More Colors Available',
    Rating: '3.5',
    ReviewCount: '18',
  },
  data: {
    showPercentage: true,
    source: 'productCategory',
    apiKey: 'ABC1234567',
    useDivPref: true,
    requestUrl: '/resources/productSearch/v1/search?isFacetsEnabled=true&pageId=0&cid=',
    cid: '1119888',
    numberOfProduct: 20,
    layout: 'grid',
    lazy: false,
    scheme: 'onhome2_rr',
    priceFlag: true,
    priceMarkdownRange: true,
    priceNowText: 'Now',
    priceOffText: 'off',
    priceWasText: 'Was',
    ratings: {
      showRating: true,
      minRating: 3,
      showReviewCount: true,
      reviewCountColor: '#000000',
    },
    prevArrowSlick: '/Asset_Archive/ONWeb/content/0013/268/652/assets/020416_US_DenimWovenSweaters_site_hp_v2_arrowL.png',
    nextArrowSlick: '/Asset_Archive/ONWeb/content/0013/268/652/assets/020416_US_DenimWovenSweaters_site_hp_v2_arrowL.png',
    defaultslidesToShowSlick: 5,
    defaultslidesToScrollSlick: 2.5,
    resslidesToShowSlick: 4,
    resslidesToScrollSlick: 3,
    productTextStyles: {
      productTitle: {
        style: {
          fontWeight: 'bold',
          textAlign: 'left',
        },
      },
      productPrice: {
        style: {
          color: 'green',
          textAlign: 'left',
        },
      },
      productSalePrice: {
        style: {
          color: 'red',
        },
      },
      size: {
        width: 'auto',
        height: '250px',
      },
    },
    productCardStyles: {
      style: {
        width: 'auto',
        marginBottom: '8px',
        border: '2px solid green',
      },
    },
    productCardImageStyles: {
      style: {
        border: '13px solid purple',
      },
    },
    gridLayout: {
      style: {},
      productsPerRow: {
        desktop: 4,
        mobile: 2,
      },
    },
  },
};
