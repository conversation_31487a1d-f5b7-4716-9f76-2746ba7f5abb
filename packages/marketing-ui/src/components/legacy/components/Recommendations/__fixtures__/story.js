// @ts-nocheck
const arrow = require('./assets/arrow/arrow.png').default.src;
import { API_KEY, CID, REQUEST_URL, AGGREGATION_SERVICE_API_REQUEST_URL, AT_CID, AT_AGGREGATION_SERVICE_API_REQUEST_URL } from './response';

export const defaultStoryData = {
  source: 'productCategory',
  apiKey: API_KEY,
  cid: CID,
  requestUrl: REQUEST_URL,
  useDivPref: false,
  layout: 'carousel',
  priceFlag: true,
  numberOfProduct: 10,
  strikeThroughOriginalPriceFlag: true,
  showMarketingFlag: true,
  customBrand: 'ON',
  scheme: '',
  displayTitle: true,
  priceNowText: 'Now',
  priceOffText: 'off',
  priceWasText: 'Was',
  fullWidth: false,
  style: {
    sliderContainer: {},
  },
  certonaTitle: {
    title: 'Shop New Arrivals',
    style: {
      desktop: {
        display: 'flex',
        paddingBottom: '60px',
        fontSize: '2rem',
        fontWeight: '700',
        textAlign: 'center',
        textTransform: 'uppercase',
        justifyContent: 'center',
        letterSpacing: '1.5px',
      },
      mobile: {
        display: 'flex',
        paddingBottom: '20px',
        fontSize: '1.3rem',
        fontWeight: '700',
        textAlign: 'center',
        textTransform: 'uppercase',
        justifyContent: 'center',
        letterSpacing: '1.5px',
      },
    },
  },
  defaultslidesToShowSlick: 4,
  defaultslidesToScrollSlick: 4,
  resslidesToShowSlick: 4,
  resslidesToScrollSlick: 4,
  prevArrowSlick: arrow,
  nextArrowSlick: arrow,
  prevArrowAlt: 'previous',
  nextArrowAlt: 'next',
  arrowMaxWidth: '20px',
  productTextStyles: {
    productTitle: {
      style: {
        textAlign: 'left',
        paddingRight: '0rem',
      },
    },
    productMarketingFlag: {
      style: {
        fontWeight: 'bold',
        fontSize: '.9rem',
        textAlign: 'left',
      },
    },
    productPrice: {
      style: {
        textAlign: 'left',
      },
    },
    productSalePrice: {
      style: {
        color: 'red',
        textAlign: 'left',
      },
    },
    size: {
      width: 'auto',
    },
  },
  productCardStyles: {
    style: {
      width: 'auto',
      marginBottom: '8px',
      textAlign: 'left',
    },
  },
  gridLayout: {},
  productsPerRow: {
    desktop: 4,
    mobile: 2,
  },
};

export const DesktopDraggableSlidesData = {
  ...defaultStoryData,
  draggable: true,
};

export const paginatorDataTopRight = {
  ...defaultStoryData,
  paginator: {
    showPaginator: true,
    pageNumberAriaText: 'page number',
    paginatorStyles: {
      paginatorPrevArrowStyles: {
        right: '5%',
      },
      paginatorNextArrowStyles: {
        height: 'auto',
        position: 'absolute',
        top: '10%',
        left: 'auto',
        right: '0',
      },
      pageNumberStyles: {
        color: 'black',
        fontSize: '20px',
        position: 'absolute',
        top: '10%',
        right: '2%',
        paddingTop: '8.5px',
      },
    },
  },
};

export const paginatorDataTopLeft = {
  ...defaultStoryData,
  paginator: {
    showPaginator: true,
    pageNumberAriaText: 'page number',
    paginatorStyles: {
      paginatorPrevArrowStyles: {
        left: '0px',
      },
      paginatorNextArrowStyles: {
        left: '5%',
        right: 'auto',
        height: 'auto',
        position: 'absolute',
        top: '10%',
      },
      pageNumberStyles: {
        color: 'black',
        fontSize: '20px',
        position: 'absolute',
        top: '10%',
        paddingTop: '9px',
        left: '2%',
      },
    },
  },
};

export const paginatorDataBottomLeft = {
  ...defaultStoryData,
  paginator: {
    showPaginator: true,
    pageNumberAriaText: 'page number',
    paginatorStyles: {
      paginatorPrevArrowStyles: {
        left: '0',
      },
      paginatorNextArrowStyles: {
        left: '4.5%',
        right: 'auto',
        height: 'auto',
        position: 'absolute',
        top: 'auto',
        bottom: '-10%',
      },
      pageNumberStyles: {
        color: 'black',
        fontSize: '20px',
        position: 'absolute',
        top: 'auto',
        left: '2%',
        bottom: '-8.5%',
      },
    },
  },
};

export const paginatorDataBottomRight = {
  ...defaultStoryData,
  paginator: {
    showPaginator: true,
    pageNumberAriaText: 'page number',
    paginatorStyles: {
      paginatorPrevArrowStyles: {
        right: '5%',
      },
      paginatorNextArrowStyles: {
        top: 'auto',
        left: 'auto',
        height: 'auto',
        position: 'absolute',
        bottom: '-10%',
      },
      pageNumberStyles: {
        color: 'black',
        fontSize: '20px',
        marginRight: '3px',
        position: 'absolute',
        top: 'auto',
        right: '2.1%',
        bottom: '-8.5%',
      },
    },
  },
};

export const certonaStoryData = {
  defaultHeight: '0px',
  placeholderSettings: {
    useGreyLoadingEffect: false,
    desktop: {
      paddingBottom: 0,
      width: 0,
      height: 0,
    },
    mobile: {
      width: 0,
      height: 0,
    },
  },
  source: 'certona',
  layout: 'grid',
  lazy: false,
  scheme: 'onoos1_rr',
  priceFlag: true,
  customBrand: 'ON',
  prevArrowSlick: arrow,
  nextArrowSlick: arrow,
  prevArrowAlt: 'previous recommendation',
  nextArrowAlt: 'next recommendation',
  defaultslidesToShowSlick: 5,
  defaultslidesToScrollSlick: 2.5,
  resslidesToShowSlick: 4,
  resslidesToScrollSlick: 3,
  priceNowText: 'Now',
  priceOffText: 'off',
  priceWasText: 'Was',
  fullWidth: false,
  style: {
    sliderContainer: {},
  },
  productTextStyles: {
    productTitle: {
      style: {
        fontWeight: 'bold',
        textAlign: 'center',
      },
    },
    productPrice: {
      style: {
        display: 'none',
        color: 'green',
        float: 'center',
      },
    },
    productSalePrice: {
      style: {
        display: 'none',
        color: 'red',
        float: 'center',
      },
    },
    size: {
      width: 'auto',
      height: '250px',
    },
  },
  productCardStyles: {
    style: {
      width: '100%',
      marginBottom: '8px',
    },
  },
  displayTitle: false,
  certonaTitle: {
    title: "We think you'll love these",
    style: {
      desktop: {
        display: 'block',
        textAlign: 'center',
        margin: 'auto 0',
        textTransform: 'uppercase',
        letterSpacing: '.1em',
        padding: '1em 0',
        fontSize: '1.2rem',
        fontWeight: '900',
      },
      mobile: {
        display: 'block',
        textAlign: 'center',
        margin: 'auto 0',
        textTransform: 'uppercase',
        letterSpacing: '.1em',
        padding: '1em 0',
        fontSize: '1.2rem',
        fontWeight: '900',
      },
    },
  },
  gridLayout: {
    style: {
      desktop: {
        display: 'flex',
        flexFlow: 'row wrap',
      },
      mobile: {
        display: 'flex',
        flexFlow: 'row wrap',
      },
    },
    productsPerRow: {
      desktop: 5,
      mobile: 2,
    },
  },
};

export const withImageStylesStoryData = {
  source: 'productCategory',
  useDivPref: false,
  requestUrl: REQUEST_URL,
  cid: CID,
  apiKey: API_KEY,
  numberOfProduct: 20,
  layout: 'carousel',
  lazy: false,
  scheme: 'onhome2_rr',
  priceFlag: true,
  prevArrowSlick: arrow,
  nextArrowSlick: arrow,
  nextArrowAlt: 'Next',
  prevArrowAlt: 'Prev',
  alt: 'arrows',
  priceNowText: 'Now',
  priceOffText: 'off',
  priceWasText: 'Was',
  fullWidth: false,
  style: {
    sliderContainer: {},
  },
  defaultslidesToShowSlick: 5,
  defaultslidesToScrollSlick: 2.5,
  resslidesToShowSlick: 4,
  resslidesToScrollSlick: 3,
  productTextStyles: {
    productTitle: {
      style: {
        fontWeight: 'bold',
        textAlign: 'left',
      },
    },
    productPrice: {
      style: {
        color: 'black',
        textDecoration: 'line-through',
      },
    },
    productSalePrice: {
      style: {
        color: 'red',
        textAlign: 'left',
      },
    },
    size: {
      width: 'auto',
      height: '250px',
    },
  },
  productCardStyles: {
    style: {
      width: 'auto',
      marginBottom: '8px',
    },
  },
  productCardImageStyles: {
    style: {
      border: '6px solid #8080801a',
      borderRadius: '4px',
      boxShadow: '3px 3px 8px grey',
    },
  },
  displayTitle: true,
  certonaTitle: {
    style: {
      desktop: {
        display: 'flex',
        paddingBottom: '50px',
        fontSize: '100%',
        textAlign: 'center',
      },
    },
  },
  gridLayout: {
    style: {
      desktop: {
        display: 'flex',
        flexFlow: 'row wrap',
      },
    },
    productPerRow: 5,
  },
};

export const withAutoplayData = {
  source: 'productCategory',
  useDivPref: false,
  requestUrl: REQUEST_URL,
  cid: CID,
  apiKey: API_KEY,
  numberOfProduct: 20,
  layout: 'carousel',
  lazy: false,
  scheme: 'onhome2_rr',
  priceFlag: true,
  arrows: true,
  prevArrowSlick: arrow,
  nextArrowSlick: arrow,
  nextArrowAlt: 'Next',
  prevArrowAlt: 'Prev',
  priceNowText: 'Now',
  priceOffText: 'off',
  priceWasText: 'Was',
  fullWidth: false,
  style: {
    sliderContainer: {},
  },
  defaultslidesToShowSlick: 4,
  defaultslidesToScrollSlick: 1,
  resslidesToShowSlick: 4,
  resslidesToScrollSlick: 1,
  pauseOnHover: true,
  autoplay: false,
  infinite: true,
  autoplaySpeed: 1500,
  speed: 500,
  productTextStyles: {
    productTitle: {
      style: {
        fontWeight: 'bold',
        textAlign: 'left',
      },
    },
    productPrice: {
      style: {
        color: 'black',
        textDecoration: 'line-through',
      },
    },
    productSalePrice: {
      style: {
        color: 'red',
      },
    },
    size: {
      width: 'auto',
      height: '250px',
    },
  },
  productCardStyles: {
    style: {
      width: 'auto',
      marginBottom: '8px',
    },
  },
  productCardImageStyles: {
    style: {
      border: '6px solid #8080801a',
      borderRadius: '4px',
      boxShadow: '3px 3px 8px grey',
    },
  },
  displayTitle: true,
  certonaTitle: {
    style: {
      desktop: {
        display: 'flex',
        paddingBottom: '50px',
        fontSize: '100%',
        textAlign: 'center',
      },
    },
  },
  gridLayout: {
    style: {
      desktop: {
        display: 'flex',
        flexFlow: 'row wrap',
      },
    },
    productPerRow: 5,
  },
};

export const withRatingsData = {
  ratings: {
    ariaLabel: 'Image of 5 stars, {{starRating}} are filled, {{reviewCount}} Reviews',
    ariaLabelNoReview: 'Image of 5 stars, {{starRating}} are filled',
    minRating: 3,
    showRating: true,
    showReviewCount: true,
    reviewCountColor: '#000000',
    reviewCountSingularLabel: '{{reviewCount}} Review',
    reviewCountPluralLabel: '{{reviewCount}} Reviews',
  },
  ...certonaStoryData,
};

export const withMarkdownPriceRangeData = {
  strikeThroughOriginalPriceFlag: true,
  showMarketingFlag: false,
  defaultHeight: '0px',
  placeholderSettings: {
    useGreyLoadingEffect: false,
    desktop: {
      paddingBottom: 0,
      width: 0,
      height: 0,
    },
    mobile: {
      width: 0,
      height: 0,
    },
  },
  source: 'certona',
  layout: 'carousel',
  lazy: false,
  scheme: 'onoos1_rr',
  numberOfProduct: 10,
  priceFlag: true,
  priceMarkdownRange: true,
  customBrand: 'ON',
  prevArrowSlick: arrow,
  nextArrowSlick: arrow,
  prevArrowAlt: 'previous recommendation',
  nextArrowAlt: 'next recommendation',
  defaultslidesToShowSlick: 5,
  defaultslidesToScrollSlick: 2.5,
  resslidesToShowSlick: 4,
  resslidesToScrollSlick: 3,
  priceNowText: 'Now',
  priceOffText: 'off',
  priceWasText: 'Was',
  fullWidth: false,
  style: {
    sliderContainer: {},
  },
  productTextStyles: {
    productTitle: {
      style: {
        fontWeight: 'normal',
        textAlign: 'left',
      },
    },
    productMarketingFlag: {
      style: {
        fontWeight: 'bold',
        fontSize: '.9rem',
        textAlign: 'left',
      },
    },
    productPrice: {
      style: {
        textAlign: 'left',
      },
    },
    productSalePrice: {
      style: {
        fontWeight: 'bold',
        color: 'red',
        textAlign: 'left',
        lineHeight: '15px',
      },
    },
    size: {
      width: 'auto',
    },
  },
  productCardStyles: {
    style: {
      width: 'auto',
      marginBottom: '8px',
      textAlign: 'left',
    },
  },
  displayTitle: true,
  certonaTitle: {
    title: "We think you'll love these",
    style: {
      desktop: {
        display: 'block',
        textAlign: 'center',
        margin: 'auto 0',
        textTransform: 'uppercase',
        letterSpacing: '.1em',
        padding: '1em 0',
        fontSize: '1.2rem',
        fontWeight: '900',
      },
      mobile: {
        display: 'block',
        textAlign: 'center',
        margin: 'auto 0',
        textTransform: 'uppercase',
        letterSpacing: '.1em',
        padding: '1em 0',
        fontSize: '1.2rem',
        fontWeight: '900',
      },
    },
  },
  gridLayout: {
    style: {
      desktop: {
        display: 'flex',
        flexFlow: 'row wrap',
      },
      mobile: {
        display: 'flex',
        flexFlow: 'row wrap',
      },
    },
    productsPerRow: {
      desktop: 5,
      mobile: 2,
    },
  },
};

export const withAggregationServiceAPIStoryData = {
  source: 'productCategory',
  cid: CID,
  requestUrl: AGGREGATION_SERVICE_API_REQUEST_URL,
  useDivPref: false,
  layout: 'carousel',
  priceFlag: true,
  numberOfProduct: 10,
  strikeThroughOriginalPriceFlag: true,
  showMarketingFlag: true,
  customBrand: 'ON',
  scheme: '',
  displayTitle: true,
  priceNowText: 'Now',
  priceOffText: 'off',
  priceWasText: 'Was',
  fullWidth: false,
  style: {
    sliderContainer: {},
  },
  certonaTitle: {
    title: 'Shop New Arrivals',
    style: {
      desktop: {
        display: 'flex',
        paddingBottom: '60px',
        fontSize: '2rem',
        fontWeight: '700',
        textAlign: 'center',
        textTransform: 'uppercase',
        justifyContent: 'center',
        letterSpacing: '1.5px',
      },
      mobile: {
        display: 'flex',
        paddingBottom: '20px',
        fontSize: '1.3rem',
        fontWeight: '700',
        textAlign: 'center',
        textTransform: 'uppercase',
        justifyContent: 'center',
        letterSpacing: '1.5px',
      },
    },
  },
  defaultslidesToShowSlick: 4,
  defaultslidesToScrollSlick: 4,
  resslidesToShowSlick: 4,
  resslidesToScrollSlick: 4,
  prevArrowSlick: arrow,
  nextArrowSlick: arrow,
  prevArrowAlt: 'previous',
  nextArrowAlt: 'next',
  arrowMaxWidth: '20px',
  productTextStyles: {
    productTitle: {
      style: {
        textAlign: 'left',
        paddingRight: '0rem',
      },
    },
    productMarketingFlag: {
      style: {
        fontWeight: 'bold',
        fontSize: '.9rem',
        textAlign: 'left',
      },
    },
    productPrice: {
      style: {
        textAlign: 'left',
      },
    },
    productSalePrice: {
      style: {
        color: 'red',
        textAlign: 'left',
      },
    },
    size: {
      width: 'auto',
    },
  },
  productCardStyles: {
    style: {
      width: 'auto',
      marginBottom: '8px',
      textAlign: 'left',
    },
  },
  gridLayout: {},
  productsPerRow: {
    desktop: 4,
    mobile: 2,
  },
};

export const withAtAggregationServiceAPIStoryData = {
  source: 'productCategory',
  cid: AT_CID,
  requestUrl: AT_AGGREGATION_SERVICE_API_REQUEST_URL,
  useDivPref: false,
  layout: 'carousel',
  priceFlag: true,
  numberOfProduct: 10,
  strikeThroughOriginalPriceFlag: true,
  showMarketingFlag: true,
  customBrand: 'ON',
  scheme: '',
  displayTitle: true,
  priceNowText: 'Now',
  priceOffText: 'off',
  priceWasText: 'Was',
  fullWidth: false,
  style: {
    sliderContainer: {},
  },
  certonaTitle: {
    title: 'Shop New Arrivals',
    style: {
      desktop: {
        display: 'flex',
        paddingBottom: '60px',
        fontSize: '2rem',
        fontWeight: '700',
        textAlign: 'center',
        textTransform: 'uppercase',
        justifyContent: 'center',
        letterSpacing: '1.5px',
      },
      mobile: {
        display: 'flex',
        paddingBottom: '20px',
        fontSize: '1.3rem',
        fontWeight: '700',
        textAlign: 'center',
        textTransform: 'uppercase',
        justifyContent: 'center',
        letterSpacing: '1.5px',
      },
    },
  },
  defaultslidesToShowSlick: 4,
  defaultslidesToScrollSlick: 4,
  resslidesToShowSlick: 4,
  resslidesToScrollSlick: 4,
  prevArrowSlick: arrow,
  nextArrowSlick: arrow,
  prevArrowAlt: 'previous',
  nextArrowAlt: 'next',
  arrowMaxWidth: '20px',
  productTextStyles: {
    productTitle: {
      style: {
        textAlign: 'left',
        paddingRight: '0rem',
      },
    },
    productMarketingFlag: {
      style: {
        fontWeight: 'bold',
        fontSize: '.9rem',
        textAlign: 'left',
      },
    },
    productPrice: {
      style: {
        textAlign: 'left',
      },
    },
    productSalePrice: {
      style: {
        color: 'red',
        textAlign: 'left',
      },
    },
    size: {
      width: 'auto',
    },
  },
  productCardStyles: {
    style: {
      width: 'auto',
      marginBottom: '8px',
      textAlign: 'left',
    },
  },
  gridLayout: {},
  productsPerRow: {
    desktop: 4,
    mobile: 2,
  },
};
