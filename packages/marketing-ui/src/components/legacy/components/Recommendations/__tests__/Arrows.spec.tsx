// @ts-nocheck
import React from 'react';
import { render, fireEvent, act } from 'test-utils';
import { PrevArrow, NextArrow } from '../components/Arrows';

const data = {
  className: '',
  style: {},
  data: {},
  nextArrowAlt: 'next',
  prevArrowAlt: 'previous',
  prevArrowSlick: '/Asset_Archive/ONWeb/content/0013/268/652/assets/020416_US_DenimWovenSweaters_site_hp_v2_arrowL.png',
  nextArrowSlick: '/Asset_Archive/ONWeb/content/0013/268/652/assets/020416_US_DenimWovenSweaters_site_hp_v2_arrowL.png',
};

describe('Recommendations Arrows', () => {
  describe('DesktopRecs Arrows', () => {
    describe('<PrevArrow />', () => {
      const customData = {
        ...data,
        prevArrowAlt: 'previous',
        ariaLabel: 'previous',
        disabled: false,
        alt: 'previous',
        onClick: jest.fn(),
      };

      test('should have a default onClick function when none is provided', async () => {
        const { getByRole } = render(<PrevArrow {...customData} />);
        const button = getByRole('button');
        await act(async () => {
          fireEvent.click(button);
        });
        expect(customData.onClick).toHaveBeenCalledTimes(1);
      });

      test('should use alt text provided for aria-label', () => {
        const { getByLabelText } = render(<PrevArrow {...customData} />);
        expect(getByLabelText(customData.prevArrowAlt)).toBeInTheDocument();
      });
    });

    describe('<NextArrow />', () => {
      const customData = {
        ...data,
        nextArrowAlt: 'next',
        ariaLabel: 'next',
        disabled: false,
        alt: 'next',
        onClick: jest.fn(),
      };
      test('should have a default onClick function when none is provided', async () => {
        const { getByRole } = render(<NextArrow {...customData} />);
        const button = getByRole('button');
        await act(async () => {
          fireEvent.click(button);
        });
        expect(customData.onClick).toHaveBeenCalledTimes(1);
      });

      test('should use alt text provided for aria-label', () => {
        const { getByLabelText } = render(<NextArrow {...customData} />);
        expect(getByLabelText(customData.nextArrowAlt)).toBeInTheDocument();
      });
    });
  });

  describe('MobileRecs Arrows ', () => {
    [NextArrow, NextArrow].forEach(Arrow => {
      const compName = Arrow.name;
      const alt = compName === 'PrevArrow' ? data.prevArrowAlt : data.nextArrowAlt;
      const src = compName === 'PrevArrow' ? data.prevArrowSlick : data.nextArrowSlick;

      describe(`<${compName} />`, () => {
        test('use src and alt in arrow components', () => {
          const { getByRole, getByAltText } = render(<Arrow alt={alt} src={src} />);
          const image = getByRole('img');
          expect(getByAltText(alt)).toBeInTheDocument();
          expect(image).toHaveAttribute('src');
        });
      });
    });
  });
});
