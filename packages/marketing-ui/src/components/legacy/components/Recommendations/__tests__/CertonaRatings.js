// @ts-nocheck
import React from 'react';
import { render, act } from 'test-utils';
import { snapshotTests } from 'test-utils';
import CertonaRatings from '../CertonaRatings';

// to mock the non-deterministic IDs generated in @core-ui/core/review-ratings
jest.mock('random-uuid-v4', () => {
  let value = 0;
  return () => {
    value += 1;
    return value;
  };
});

describe('Certona Ratings', () => {
  const certonaRatingsProps = ({ shouldShowReviewCount = true, fakeMinRating = 0, ariaLabel, reviewCountLabel }) => ({
    product: {
      Rating: '4',
      ReviewCount: '3',
    },
    ratings: {
      minRating: fakeMinRating,
      showRating: true,
      showReviewCount: shouldShowReviewCount,
      ariaLabel,
      reviewCountLabel,
    },
  });

  it("minRating set to 5. It shouldn't render ratings for product less than 5", () => {
    const { queryByText } = render(
      <CertonaRatings
        {...certonaRatingsProps({
          fakeMinRating: 5,
          reviewCountLabel: '3 Reviews',
        })}
      />
    );
    expect(queryByText('3 Reviews')).not.toBeInTheDocument();
  });

  describe('Certona review count', () => {
    it('does not show review ratings if showRatings prop is false', () => {
      const { queryByText } = render(
        <CertonaRatings
          {...certonaRatingsProps({
            shouldShowReviewCount: false,
            reviewCountLabel: '3 Reviews',
          })}
        />
      );
      expect(queryByText('3 Reviews')).not.toBeInTheDocument();
    });

    it('show review ratings if showRatings prop is true', () => {
      const { queryByText } = render(
        <CertonaRatings
          {...certonaRatingsProps({
            shouldShowReviewCount: true,
            reviewCountLabel: '3 Reviews',
          })}
        />
      );
      expect(queryByText('3 Reviews')).toBeInTheDocument();
    });
  });
});

describe('<CertonaRatings /> Snapshots', () => {
  const certonaRatingsProps = ({ ariaLabel, reviewCountLabel, shouldShowReviewCount, fakeMinRating }) => ({
    product: {
      Rating: '4',
      ReviewCount: '3',
    },
    ratings: {
      minRating: fakeMinRating,
      showRating: true,
      showReviewCount: shouldShowReviewCount,
      ariaLabel,
      reviewCountLabel,
    },
  });

  snapshotTests(
    CertonaRatings,
    [['default', {}]],
    certonaRatingsProps({
      ariaLabel: 'with an average rating of 4 out of 5',
      reviewCountLabel: '3 Reviews',
      shouldShowReviewCount: true,
      fakeMinRating: 0,
    })
  );
});
