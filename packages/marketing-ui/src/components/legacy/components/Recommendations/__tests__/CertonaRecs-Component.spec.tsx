import React from 'react';
import { render, screen } from 'test-utils';
import { useProductRecommendations, ProductRecommendationsProvider } from '@sitewide/providers/product-recs-provider/ProductRecommendationProvider';
import CertonaRecs from '../CertonaRecs';
import { certonaFixtureData, aiRecData } from '../__fixtures__/CertonaRecs-Component-Data';

const productRecsMock = useProductRecommendations as jest.Mock;

jest.mock('@sitewide/providers/product-recs-provider/ProductRecommendationProvider', () => ({
  ...jest.requireActual('@sitewide/providers/product-recs-provider/ProductRecommendationProvider'),
  useProductRecommendations: jest.fn(),
}));

const didRender = 'did render';
const expectToRenderNull = () => expect(screen.queryByText(didRender)).not.toBeInTheDocument();

const renderRecs = (scheme?: string) =>
  render(
    <ProductRecommendationsProvider>
      <CertonaRecs isLarge={true} Recommendations={({ title }) => <span>{title || didRender}</span>} data={{ scheme, numberOfProduct: 5 }} />
    </ProductRecommendationsProvider>,
    { appState: { enabledFeatures: { ['mui-certona-recommendations']: true } } }
  );

describe('CertonaRecs Component Tests', () => {
  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should return null when there is no top level data and ai recs are active', () => {
    productRecsMock.mockReturnValue({
      aiRecommendationsData: {},
      certonaRecommendationsData: {},
      aiRecsActiveForPageType: true,
    });
    renderRecs();
    expectToRenderNull();
  });

  it('should return null when there is no top level data and ai recs are NOT active', () => {
    productRecsMock.mockReturnValue({
      aiRecommendationsData: {},
      certonaRecommendationsData: {},
      aiRecsActiveForPageType: false,
    });
    renderRecs();
    expectToRenderNull();
  });

  it('should return null when there is no scheme data and ai recs are active', () => {
    productRecsMock.mockReturnValue({
      aiRecommendationsData: { recommendationsData: { schemes: [] } },
      certonaRecommendationsData: {},
      aiRecsActiveForPageType: true,
    });
    renderRecs();
    expectToRenderNull();
  });

  it('should return null when there is no scheme data and ai recs are NOT active', () => {
    productRecsMock.mockReturnValue({
      aiRecommendationsData: {},
      certonaRecommendationsData: { recommendationsData: { schemes: [] } },
      aiRecsActiveForPageType: false,
    });
    renderRecs();
    expectToRenderNull();
  });

  it('should return null when there is no reccomendation data and ai recs are active', () => {
    productRecsMock.mockReturnValue({
      aiRecommendationsData: { recommendationsData: { schemes: [''] } },
      certonaRecommendationsData: {},
      aiRecsActiveForPageType: true,
    });
    renderRecs();
    expectToRenderNull();
  });

  it('should return null when there is no reccomendation data and ai recs are NOT active', () => {
    productRecsMock.mockReturnValue({
      aiRecommendationsData: {},
      certonaRecommendationsData: { recommendationsData: { schemes: [''] } },
      aiRecsActiveForPageType: false,
    });
    renderRecs();
    expectToRenderNull();
  });

  it('should return expected AI reccomendation data when ai recs are active', () => {
    const [aiScheme, aiTitle] = ['gaphome1_rr', "Don't Miss Out!"];
    productRecsMock.mockReturnValue({
      aiRecommendationsData: { recommendationsData: { schemes: aiRecData } },
      certonaRecommendationsData: {},
      aiRecsActiveForPageType: true,
    });
    renderRecs(aiScheme);
    expect(screen.getByText(aiTitle)).toBeInTheDocument();
  });

  it('should return expected Certona reccomendation data when ai recs are NOT active', () => {
    const [certonaScheme, certonaTitle] = ['gaphome2_rr', 'Get in some new'];
    productRecsMock.mockReturnValue({
      aiRecommendationsData: {},
      certonaRecommendationsData: { recommendationsData: { schemes: certonaFixtureData } },
      aiRecsActiveForPageType: false,
    });
    renderRecs(certonaScheme);
    expect(screen.getByText(certonaTitle)).toBeInTheDocument();
  });
});
