// @ts-nocheck
import { getRecommendations, parseNumberOfProducts } from '../helpers/get-certona-recommendations';
import { certonaContextValue } from '../__fixtures__/CertonaRecs-test-data';

const schemes = [
  {
    scheme: 'scheme1',
    items: ['stuff'],
  },
  {
    scheme: 'scheme2',
    items: ['moreStuff'],
  },
];

describe('Certona with numberOfProduct ', () => {
  it('is not set and returns 10 items', () => {
    const schemeItems = getRecommendations({
      schemes: certonaContextValue.data.schemes,
      scheme: 'onoos1_rr',
    });
    expect(schemeItems.length).toEqual(10);
  });

  it('is set and returns 6 items', () => {
    const schemeItems = getRecommendations({
      schemes: certonaContextValue.data.schemes,
      scheme: 'onoos1_rr',
      numProducts: parseNumberOfProducts({ numProducts: 6 }),
    });
    expect(schemeItems.length).toEqual(6);
  });

  it('is set as a string and returns 6 items', () => {
    const schemeItems = getRecommendations({
      schemes: certonaContextValue.data.schemes,
      scheme: 'onoos1_rr',
      numProducts: parseNumberOfProducts({ numProducts: '6' }),
    });

    expect(schemeItems.length).toStrictEqual(6);
  });

  it('is set to 0 and returns all items', () => {
    const schemeItems = getRecommendations({
      schemes: certonaContextValue.data.schemes,
      scheme: 'onoos1_rr',
      numProducts: parseNumberOfProducts({ numProducts: 0 }),
    });

    expect(schemeItems.length).toEqual(10);
  });

  it('is set as a string but has no value and returns all items', () => {
    const schemeItems = getRecommendations({
      schemes: certonaContextValue.data.schemes,
      scheme: 'onoos1_rr',
      numProducts: parseNumberOfProducts({ numProducts: '' }),
    });

    expect(schemeItems.length).toStrictEqual(10);
  });

  it('is set with schemes that are undefined it returns an empty array', () => {
    const schemeItems = getRecommendations({
      schemes: undefined,
      scheme: 'onoos1_rr',
      numProducts: parseNumberOfProducts({ numProducts: 10 }),
    });

    expect(schemeItems).toStrictEqual([]);
  });

  describe('filters the correct scheme', () => {
    it('should return the correct scheme item array', () => {
      const expectedArray = ['stuff'];
      const schemeItems = getRecommendations({ schemes, scheme: 'scheme1' });
      expect(expectedArray).toStrictEqual(schemeItems);
    });

    it('should set scheme items array to empty array when no scheme present', () => {
      const expectedArray: never[] = [];
      const schemeItems = getRecommendations({ schemes, scheme: 'scheme3' });
      expect(expectedArray).toStrictEqual(schemeItems);
    });

    it('should return the correct array of items based on scheme', () => {
      const expectedArray = ['moreStuff'];
      const schemeItems = getRecommendations({ schemes, scheme: 'scheme2' });
      expect(expectedArray).toStrictEqual(schemeItems);
    });
  });
});
