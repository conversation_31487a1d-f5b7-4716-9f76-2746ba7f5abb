// @ts-nocheck
import React from 'react';
import { render, act } from 'test-utils';
import { Brands, StitchStyleProvider } from '@ecom-next/core/react-stitch';
import MarketingCarousel from '../../../helper/MarketingCarousel';
import { paginatorDataTopRight } from '../__fixtures__/story';
import DesktopRecs, { isDisabledNext, isDisabledPrev } from '../components/DesktopRecs';

const mockMarketingCarousel = jest.fn();
jest.mock('../../../helper/MarketingCarousel', () => props => mockMarketingCarousel(props));

mockMarketingCarousel.mockImplementation(props => {
  const MarketingCarousel = jest.requireActual('../../../helper/MarketingCarousel').default;
  return <MarketingCarousel {...props} />;
});

const data = {
  cid: '1123863',
  defaultHeight: {
    large: '375px',
    small: '380px',
  },
  fullWidth: false,
  displayTitle: false,
  gridLayout: {
    style: {
      desktop: {
        display: 'flex',
        flexFlow: 'row wrap',
      },
    },
  },
  layout: 'carousel',
  lazy: true,
  numberOfProduct: 20,
  priceFlag: true,
  productCardStyles: {
    style: {
      display: 'block',
      flexGrow: '5',
      margin: 'auto',
      width: '85%',
    },
  },
  requestUrl: '/resources/productSearch/v1/search?isFacetsEnabled=true&pageId=0&cid=',
  prevArrowSlick: 'img-imp.png',
  nextArrowSlick: 'img-imp-l.png',
  nextArrowAlt: 'next',
  prevArrowAlt: 'previous',
  resslidesToScrollSlick: 3,
  resslidesToShowSlick: 3,
  scheme: 'onhome1_rr',
  source: 'productCategory',
};

// Product recommendation data
const recommendations = [
  {
    CurrentPrice: '$78.00',
    DetailURL: '/browse/product.do?cid=1123863&pcid=372436&vid=1&pid=372436002',
    ID: '372436002',
    ImageURL: 'https://www4.assets-gap.com/webcontent/0016/273/931/cn16273931.jpg',
    InStock: 'true',
    LightWeightImageURL: 'https://www4.assets-gap.com/webcontent/0016/273/931/cn16273931.jpg',
    MarketingFlag: 'More Colors Available',
    OriginalPrice: '$98.00',
    ProductName: 'Lightweight Run Pant',
    PromotionDisplay: '',
  },
  {
    CurrentPrice: '$78.00',
    DetailURL: '/browse/product.do?cid=1123863&pcid=372436&vid=1&pid=372436002',
    ID: '372436002',
    ImageURL: 'https://www4.assets-gap.com/webcontent/0016/273/931/cn16273931.jpg',
    InStock: 'true',
    LightWeightImageURL: 'https://www4.assets-gap.com/webcontent/0016/273/931/cn16273931.jpg',
    MarketingFlag: 'More Colors Available',
    OriginalPrice: '$98.00',
    ProductName: 'Lightweight Run Pant',
    PromotionDisplay: '',
  },
  {
    CurrentPrice: '$78.00',
    DetailURL: '/browse/product.do?cid=1123863&pcid=372436&vid=1&pid=372436002',
    ID: '372436002',
    ImageURL: 'https://www4.assets-gap.com/webcontent/0016/273/931/cn16273931.jpg',
    InStock: 'true',
    LightWeightImageURL: 'https://www4.assets-gap.com/webcontent/0016/273/931/cn16273931.jpg',
    MarketingFlag: 'More Colors Available',
    OriginalPrice: '$98.00',
    ProductName: 'Lightweight Run Pant',
    PromotionDisplay: '',
  },
];

const mountComponent = customData => render(<DesktopRecs data={customData || data} isLarge Layout={MarketingCarousel} recommendations={recommendations} />);

const renderRecommendations = ({ customData = {}, recommendationData = {} }) =>
  render(<DesktopRecs data={customData} isLarge Layout={MarketingCarousel} recommendations={recommendationData} />);

describe('<DesktopRecs />', () => {
  afterEach(() => {
    mockMarketingCarousel.mockClear();
  });

  describe('Paginator', () => {
    describe('Paginator data present', () => {
      it('Renders Page Number Component', () => {
        const component = renderRecommendations({
          customData: paginatorDataTopRight,
          recommendationData: recommendations,
        });
        expect(component.queryByLabelText('page number 1/1')).toBeInTheDocument();
      });
      it('Renders only when layout is carousel', () => {
        const component = renderRecommendations({
          customData: { ...paginatorDataTopRight, layout: 'grid' },
          recommendationData: recommendations,
        });
        expect(component.queryByLabelText('page number 1/1')).not.toBeInTheDocument();
      });
      it('slidesToScroll tracks slidestoShow (slidesToScroll ===  slidestoShow', () => {
        mountComponent({
          ...paginatorDataTopRight,
          defaultslidesToScrollSlick: 3,
        });

        const marketingCarouselProps = mockMarketingCarousel.mock.calls[0][0];
        const { slidesToShow, slidesToScroll } = marketingCarouselProps;
        expect(slidesToShow).toBe(4);
        expect(slidesToScroll).toBe(4);
      });
      it('Responsive: slidesToScroll tracks slidestoShow (slidesToScroll ===  slidestoShow', () => {
        mountComponent({
          ...paginatorDataTopRight,
          ...{
            defaultslidesToShowSlick: 6,
            defaultslidesToScrollSlick: 2,
            resslidesToShowSlick: 4,
            resslidesToScrollSlick: 3,
            infinite: true,
            autoplay: true,
            autoplaySpeed: 500,
          },
        });

        const marketingCarouselProps = mockMarketingCarousel.mock.calls[0][0];
        const { slidesToShow, slidesToScroll, responsive } = marketingCarouselProps;
        expect(slidesToShow).toBe(6);
        expect(slidesToScroll).toBe(6);
        expect(responsive[0].settings.slidesToShow).toBe(4);
        expect(responsive[0].settings.slidesToScroll).toBe(4);
      });
    });
    describe('Paginator data not present', () => {
      it('Does not render Page Number Component', () => {
        const component = renderRecommendations({
          customData: data,
          recommendationData: recommendations,
        });
        expect(component.queryByLabelText('page number')).not.toBeInTheDocument();
      });
      it('Responsive: slidesToScroll does not track slidestoShow', () => {
        mountComponent({
          ...data,
          ...{
            defaultslidesToShowSlick: 6,
            defaultslidesToScrollSlick: 2,
            resslidesToShowSlick: 4,
            resslidesToScrollSlick: 3,
            infinite: true,
            autoplay: true,
            autoplaySpeed: 500,
          },
        });

        const marketingCarouselProps = mockMarketingCarousel.mock.calls[0][0];
        const { slidesToShow, slidesToScroll, responsive } = marketingCarouselProps;
        expect(slidesToShow).toBe(6);
        expect(slidesToScroll).toBe(2);
        expect(responsive[0].settings.slidesToShow).toBe(4);
        expect(responsive[0].settings.slidesToScroll).toBe(3);
      });
    });
  });

  describe('slick slider settings', () => {
    test('can use default slickOptions', () => {
      mountComponent();
      const marketingCarouselProps = mockMarketingCarousel.mock.calls[0][0];
      const { slidesToShow, slidesToScroll, dots, infinite, arrows, speed, centerMode } = marketingCarouselProps;

      expect(dots).toBe(false);
      expect(arrows).toBe(true);
      expect(infinite).toBe(false);
      expect(speed).toBe(300);
      expect(centerMode).toBe(false);
      expect(slidesToShow).toBe(5);
      expect(slidesToScroll).toBe(5);
    });

    test('will use JSON configured slickOptions when set', () => {
      const customData = {
        ...data,
        ...{
          defaultslidesToShowSlick: 1,
          defaultslidesToScrollSlick: 1,
          resslidesToShowSlick: 4,
          resslidesToScrollSlick: 4,
          infinite: true,
          autoplay: true,
          autoplaySpeed: 500,
        },
      };

      mountComponent(customData);

      const { slidesToShow, slidesToScroll, dots, infinite, arrows, speed, centerMode, autoplay, autoplaySpeed, responsive } =
        mockMarketingCarousel.mock.calls[0][0];

      expect(dots).toBe(false);
      expect(arrows).toBe(true);
      expect(infinite).toBe(true);
      expect(speed).toBe(300);
      expect(autoplay).toBe(true);
      expect(autoplaySpeed).toBe(500);
      expect(centerMode).toBe(false);
      expect(slidesToShow).toBe(1);
      expect(slidesToScroll).toBe(1);
      expect(responsive[0].settings.slidesToShow).toBe(4);
      expect(responsive[0].settings.slidesToScroll).toBe(4);
    });

    test('set to full-width', () => {
      const fullWidthData = { ...data, fullWidth: true };
      mountComponent(fullWidthData);
      const { fullWidth } = mockMarketingCarousel.mock.calls[0][0].data;
      expect(fullWidth).toBe(true);
    });

    test('confirm that all brands observe max-width set without overriding', () => {
      Object.values(Brands).forEach(brand => {
        ['1200px', '1400px', '1600px'].forEach(size => {
          const fullWidthData = {
            ...data,
            style: {
              maxWidth: size,
            },
          };
          const { container } = render(
            <StitchStyleProvider brand={brand}>
              <DesktopRecs data={fullWidthData} isLarge Layout={MarketingCarousel} recommendations={recommendations} />
            </StitchStyleProvider>
          );
          expect(container.firstChild).toHaveStyleRule('max-width', size);
        });
      });
    });

    test('MarketingCarousel receives draggable as true when prop is set to true', () => {
      const draggableData = { ...data, draggable: true };
      mountComponent(draggableData);

      const { draggable } = mockMarketingCarousel.mock.calls[0][0];

      expect(draggable).toBe(true);
    });
    test('MarketingCarousel receives draggable as false draggable prop is provided', () => {
      mountComponent(data);
      const { draggable } = mockMarketingCarousel.mock.calls[0][0];

      expect(draggable).toBe(false);
    });
  });

  describe('should match snapshots', () => {
    it('container max-width should default to 1920px', () => {
      const { container } = renderRecommendations({});
      expect(container).toMatchSnapshot();
    });

    it('container width set to 1920px using an empty style property', () => {
      const dataWithNoStyles = { ...data, style: {} };
      const { container } = renderRecommendations({
        customData: dataWithNoStyles,
        recommendationData: recommendations,
      });
      expect(container).toMatchSnapshot();
    });

    it('container max-width is set to 1440px using the style property', () => {
      const dataWithStyles = { ...data, style: { maxWidth: '1440px' } };
      const { container } = renderRecommendations({
        customData: dataWithStyles,
        recommendationData: recommendations,
      });
      expect(container).toMatchSnapshot();
    });

    it("container should be set to fullWidth and override style property with max-width to 'none'", () => {
      const dataWithStyles = {
        ...data,
        style: { maxWidth: '1440px' },
        fullWidth: true,
      };
      const { container } = renderRecommendations({
        customData: dataWithStyles,
        recommendationData: recommendations,
      });
      expect(container).toMatchSnapshot();
    });
    it('Paginator snapshots', () => {
      const { container } = renderRecommendations({
        customData: paginatorDataTopRight,
        recommendationData: recommendations,
      });
      expect(container).toMatchSnapshot();
    });
  });
  describe('disabled states of arrows', () => {
    describe('isDisabledPrev', () => {
      it('should not be disabled if the infinite prop is true.', () => {
        const isInfinite = true;
        const sliderLength = 10;
        const currentSlideIndex = 0;
        expect(isDisabledPrev(isInfinite, sliderLength, currentSlideIndex)).toBe(false);
      });
      it('should be disabled if the currentSlideIndex is 0 given infinite is false.', () => {
        const isInfinite = false;
        const sliderLength = 10;
        const currentSlideIndex = 0;
        expect(isDisabledPrev(isInfinite, sliderLength, currentSlideIndex)).toBe(true);
      });
      it('should not be disabled if the currentSlideIndex is not the first or last.', () => {
        const isInfinite = false;
        const sliderLength = 10;
        const currentSlideIndex = 5;
        expect(isDisabledPrev(isInfinite, sliderLength, currentSlideIndex)).toBe(false);
      });
    });
    describe('isDisabledNext', () => {
      it('should not be disabled if the infinite prop is true', () => {
        const isInfinite = true;
        const currentSlideIndex = 0;
        const lastIndex = 10;
        const slickDefaultSlidesToShow = 5;
        expect(isDisabledNext(isInfinite, currentSlideIndex, lastIndex, slickDefaultSlidesToShow)).toBe(false);
      });
      it('should not be disabled given there are more slides to show.', () => {
        const isInfinite = false;
        const currentSlideIndex = 0;
        const lastIndex = 10;
        const slickDefaultSlidesToShow = 5;
        expect(isDisabledNext(isInfinite, currentSlideIndex, lastIndex, slickDefaultSlidesToShow)).toBe(false);
      });
      it('should not be disabled if the currentSlideIndex plus slickDefaultSlidesToShow is not the first or last.', () => {
        const isInfinite = false;
        const currentSlideIndex = 5;
        const lastIndex = 10;
        const slickDefaultSlidesToShow = 3;
        expect(isDisabledNext(isInfinite, currentSlideIndex, lastIndex, slickDefaultSlidesToShow)).toBe(false);
      });
    });
  });
});
