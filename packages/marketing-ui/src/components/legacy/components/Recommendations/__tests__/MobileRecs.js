// @ts-nocheck
import React from 'react';
import { render, act } from 'test-utils';
import MarketingCarousel from '../../../helper/MarketingCarousel';
import MobileRecs from '../components/MobileRecs';

const mockMarketingCarousel = jest.fn();
jest.mock('../../../helper/MarketingCarousel', () => props => mockMarketingCarousel(props));

mockMarketingCarousel.mockImplementation(props => {
  const MarketingCarousel = jest.requireActual('../../../helper/MarketingCarousel').default;
  return <MarketingCarousel {...props} />;
});

// Data for Recommendations
const data = {
  cid: '1123863',
  defaultHeight: {
    large: '375px',
    small: '380px',
  },
  displayTitle: false,
  gridLayout: {
    style: {
      desktop: {
        display: 'flex',
        flexFlow: 'row wrap',
      },
    },
  },
  layout: 'carousel',
  lazy: true,
  numberOfProduct: 20,
  priceFlag: true,
  productCardStyles: {
    style: {
      display: 'block',
      flexGrow: '5',
      margin: 'auto',
      width: '85%',
    },
  },
  requestUrl: '/resources/productSearch/v1/search?isFacetsEnabled=true&pageId=0&cid=',
  scheme: 'onhome1_rr',
  source: 'productCategory',
  prevArrowAlt: 'previous recommendation',
  nextArrowAlt: 'next recommendation',
};

// Product recommendation data
const recommendations = [
  {
    CurrentPrice: '$78.00',
    DetailURL: '/browse/product.do?cid=1123863&pcid=372436&vid=1&pid=372436002',
    ID: '372436002',
    ImageURL: 'https://www4.assets-gap.com/webcontent/0016/273/931/cn16273931.jpg',
    InStock: 'true',
    LightWeightImageURL: 'https://www4.assets-gap.com/webcontent/0016/273/931/cn16273931.jpg',
    MarketingFlag: 'More Colors Available',
    OriginalPrice: '$98.00',
    ProductName: 'Lightweight Run Pant',
    PromotionDisplay: '',
  },
  {
    CurrentPrice: '$78.00',
    DetailURL: '/browse/product.do?cid=1123863&pcid=372436&vid=1&pid=372436002',
    ID: '372436002',
    ImageURL: 'https://www4.assets-gap.com/webcontent/0016/273/931/cn16273931.jpg',
    InStock: 'true',
    LightWeightImageURL: 'https://www4.assets-gap.com/webcontent/0016/273/931/cn16273931.jpg',
    MarketingFlag: 'More Colors Available',
    OriginalPrice: '$98.00',
    ProductName: 'Lightweight Run Pant',
    PromotionDisplay: '',
  },
  {
    CurrentPrice: '$78.00',
    DetailURL: '/browse/product.do?cid=1123863&pcid=372436&vid=1&pid=372436002',
    ID: '372436002',
    ImageURL: 'https://www4.assets-gap.com/webcontent/0016/273/931/cn16273931.jpg',
    InStock: 'true',
    LightWeightImageURL: 'https://www4.assets-gap.com/webcontent/0016/273/931/cn16273931.jpg',
    MarketingFlag: 'More Colors Available',
    OriginalPrice: '$98.00',
    ProductName: 'Lightweight Run Pant',
    PromotionDisplay: '',
  },
];

let component;
const mountComponent = customData => {
  component = render(<MobileRecs data={{ ...data, ...customData }} isLarge Layout={MarketingCarousel} recommendations={recommendations} />);
};

describe('<MobileRecs />', () => {
  afterEach(() => {
    if (component) {
      component = null;
    }
    mockMarketingCarousel.mockClear();
  });
  describe('MobileRecs will use default settings', () => {
    afterEach(() => {
      mockMarketingCarousel.mockClear();
    });

    const expectedSlickConfigurationTests = () => {
      const marketingCarouselProps = mockMarketingCarousel.mock.calls[0][0];
      const { dots, arrows, centerMode, slidesToShow, slidesToScroll } = marketingCarouselProps;

      expect(dots).toBe(false);
      expect(arrows).toBe(false);
      expect(centerMode).toBe(false);
      expect(slidesToShow).toBe(2.5);
      expect(slidesToScroll).toBe(2);
    };

    test('if useMobileConfig is false', () => {
      const customData = {
        ...data,
        ...{
          dots: true,
          arrows: true,
          centerMode: true,
          defaultslidesToShowSlick: 1,
          defaultslidesToScrollSlick: 1,
          useMobileConfig: false,
        },
      };
      mountComponent(customData);

      expectedSlickConfigurationTests(component);
    });

    test('if useMobileConfig is not set', () => {
      const customData = {
        ...data,
        ...{
          dots: true,
          arrows: true,
          centerMode: true,
          defaultslidesToShowSlick: 1,
          defaultslidesToScrollSlick: 1,
        },
      };
      mountComponent(customData);
      expectedSlickConfigurationTests(component);
    });
  });

  describe('MobileRecs will use custom settings', () => {
    test('can use default slickOptions', () => {
      mountComponent();
      const marketingCarouselProps = mockMarketingCarousel.mock.calls[0][0];
      const { dots, arrows, centerMode, speed, slidesToShow, slidesToScroll } = marketingCarouselProps;

      expect(dots).toBe(false);
      expect(arrows).toBe(false);
      expect(speed).toBe(300);
      expect(centerMode).toBe(false);
      expect(slidesToShow).toBe(2.5);
      expect(slidesToScroll).toBe(2);
    });

    test('will use JSON configured slickOptions when useMobileConfig is set', () => {
      const customData = {
        ...data,
        ...{
          dots: true,
          arrows: true,
          centerMode: true,
          infinite: true,
          defaultslidesToShowSlick: 1,
          defaultslidesToScrollSlick: 1,
          useMobileConfig: true,
          autoplay: true,
          autoplaySpeed: 400,
        },
      };

      mountComponent(customData);
      const marketingCarouselProps = mockMarketingCarousel.mock.calls[0][0];
      const { dots, arrows, autoplay, autoplaySpeed, centerMode, infinite, slidesToShow, slidesToScroll } = marketingCarouselProps;

      expect(dots).toBe(true);
      expect(arrows).toBe(true);
      expect(centerMode).toBe(true);
      expect(infinite).toBe(true);
      expect(slidesToShow).toBe(1);
      expect(slidesToScroll).toBe(1);
      expect(autoplay).toBe(true);
      expect(autoplaySpeed).toBe(400);
    });

    test('can use default slick arrows', () => {
      const customData = {
        ...data,
        ...{
          arrows: true,
          useMobileConfig: true,
        },
      };
      mountComponent(customData);
      expect(component.container.querySelector('.slick-prev svg').tagName).toBe('svg');
      expect(component.container.querySelector('.slick-next svg').tagName).toBe('svg');
    });

    test('can use JSON configured arrows', () => {
      const prevArrowSlick = 'non-standard prev image path';
      const nextArrowSlick = 'non-standard next image path';
      const customData = {
        ...data,
        ...{
          prevArrowSlick,
          nextArrowSlick,
          useMobileConfig: true,
        },
      };
      mountComponent(customData);
      const marketingCarouselDataProps = mockMarketingCarousel.mock.calls[0][0].data;

      expect(marketingCarouselDataProps.prevArrowSlick).toContain(prevArrowSlick);
      expect(marketingCarouselDataProps.nextArrowSlick).toContain(nextArrowSlick);
    });

    test('prev slick arrow is `disabled` on initial slide and slick next arrow is not `disabled`', () => {
      const customData = {
        ...data,
        ...{
          arrows: true,
          useMobileConfig: true,
        },
      };
      mountComponent(customData);
      expect(component.container.querySelector('.slick-prev').disabled).toBe(true);
      expect(component.container.querySelector('.slick-next').disabled).toBe(false);
    });

    test('prev slick & next slick arrows are never `disabled` when carousel infinite is true', () => {
      const customData = {
        ...data,
        ...{
          arrows: true,
          infinite: true,
          useMobileConfig: true,
        },
      };
      mountComponent(customData);
      expect(component.container.querySelector('.slick-prev').disabled).toBe(false);
      expect(component.container.querySelector('.slick-next').disabled).toBe(false);
    });
  });
});
