// @ts-nocheck
import React from 'react';
import { render, act } from 'test-utils';
import { ShallowWrapper } from 'test-utils';
import { Paginator } from '../components/Paginator';
import { PaginatorProps } from '../types';

const pageNumberStylesData: PaginatorProps['pageNumberStyles'] = {
  color: 'black',
  fontSize: '20px',
  marginRight: '3px',
  position: 'absolute',
  top: '10%',
  paddingTop: '9px',
  right: '2%',
};
let component: ShallowWrapper | null = null;

describe('Paginator', () => {
  afterEach(() => {
    if (component) {
      component.unmount();
      component = null;
    }
  });
  it('should have correct pages', () => {
    const component = render(
      <Paginator
        pageNumberAriaText='page number'
        pageNumberStyles={pageNumberStylesData}
        paginatedCurrentSlideIndex={0}
        prodPageMap={{ 0: 1, 2: 2 }}
        totalPages={2}
      />
    );
    expect(component.getByRole('navigation')).toHaveTextContent('1/2');
    expect(component.container).toMatchSnapshot();
  });
  it('should match styles', () => {
    const component = render(
      <Paginator
        pageNumberAriaText='page number'
        pageNumberStyles={pageNumberStylesData}
        paginatedCurrentSlideIndex={0}
        prodPageMap={{ 0: 1, 2: 2 }}
        totalPages={2}
      />
    );
    expect(component.container).toMatchSnapshot();
  });
});
