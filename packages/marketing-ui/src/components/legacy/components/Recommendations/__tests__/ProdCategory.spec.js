// @ts-nocheck
import React from 'react';
import { render, act } from 'test-utils';
import fetchMock from 'fetch-mock';
import { PersonalizationContext } from '@ecom-next/sitewide/personalization-provider';
import { testData, aggregationServiceTestData, apiMockData, aggregationServiceMockData } from '../__fixtures__/prodCategory-mockProps';
import { ProdCategory } from '../ProdCategory';
import prepareDataForRecs from '../helpers/prepare-for-recs';
import prepareDataForRecsAggregationServiceAPI, { productAdapter } from '../helpers/prepare-for-recs-aggregation-service-api';
import { DIV_PREF_VALUES } from '../helpers/div-pref-values';
import { EnabledFeaturesContext } from '@ecom-next/core/react-stitch';

jest.mock('@ecom-next/core/react-stitch', () => ({
  ...jest.requireActual('@ecom-next/core/react-stitch'),
  EnabledFeaturesContext: React.createContext({
    enabledFeatures: {
      'at-homepage-recommendations-preview': true,
    },
    featureVariables: {},
  }),
}));

fetchMock.config.overwriteRoutes = true;

const getPlaceHolderRecsComp = () => () => {};
const getProdCategoryComponent = testData => render(<ProdCategory brandName='on' data={testData} Recommendations={getPlaceHolderRecsComp()} />);

const brandName = 'on';

const personalizationData = {
  isEmpty: false,
  featureSelections: {
    Evergreens: {
      AttributeName: 'ONDIVPREF',
      AttributeValue: 'MEN',
    },
  },
};

const getPersonalizedComponent = (contextData, testData) =>
  render(
    <PersonalizationContext.Provider value={contextData}>
      <ProdCategory brandName='on' data={testData} Recommendations={getPlaceHolderRecsComp()} />
    </PersonalizationContext.Provider>,
    {
      personalizationData: { ...contextData },
    }
  );

const products = [];
const objPattern = {
  price: {
    localizedCurrentMaxPrice: 0,
  },
  categoryLargeImage: {
    path: '',
  },
};
for (let i = 0; i < 15; i += 1) {
  products[i] = objPattern;
}

describe('ProdCatgeory', () => {
  let component;

  describe('Fetch correct PCID from Product Service API', () => {
    const pcid = apiMockData.productCategoryFacetedSearch.productCategory.businessCatalogItemId;
    const { businessCatalogItemId, childProducts } = apiMockData.productCategoryFacetedSearch.productCategory.childCategories[0];

    test('should get PCID when API call is successful', () => {
      const recommendationResults = prepareDataForRecs(childProducts, 2, pcid, businessCatalogItemId);
      expect(recommendationResults[0].DetailURL).toContain(`pcid=${testData.cid}`);
    });
    test('should get CID when API call is successful', () => {
      const recommendationResults = prepareDataForRecs(childProducts, 2, pcid, businessCatalogItemId);
      expect(recommendationResults[0].DetailURL).toContain(`cid=${businessCatalogItemId}`);
    });
  });

  describe('Receive Data', () => {
    test('should fetch data from url when no products are present', async () => {
      fetchMock.getOnce(`${testData.requestUrl}${testData.cid}`, {
        status: 201,
        body: { data: apiMockData },
      });
      await act(() => {
        getProdCategoryComponent(testData).container;
      });
      expect(fetchMock.called()).toEqual(true);
    });

    test('should fetch data with previewDate and mode when the preview feature flag is enabled', async () => {
      const expectedUrl = `${testData.requestUrl}${testData.cid}&previewDate=2025-03-01&mode=wip&ignoreInventory=false`;
      fetchMock.getOnce(`${expectedUrl}`, {
        status: 201,
        body: { data: apiMockData },
      });
      window.location = {
        ...window.location,
        search: '?previewDate=2025-03-01&previewMode=wip',
      };
      const newTestData = { ...testData, previewDate: '2025-03-01', previewMode: 'wip' };
      await act(() => {
        getProdCategoryComponent(newTestData).container;
      });
      expect(fetchMock.lastUrl()).toBe(expectedUrl);
    });
  });
  describe('Data sent to Recommendations', () => {
    test('should only have as many objects as numberOfProduct', () => {
      expect(prepareDataForRecs(products, 6, '1010').length).toEqual(6);
    });
  });

  describe('useDivPref is set to true', () => {
    describe('Fetch URL is correct', () => {
      [
        {
          name: 'WOMEN',
          description: 'Fetch URL has correct CID when divPref does not exist in personalization data',
        },
        {
          name: 'MEN',
          description: 'Fetch URL has correct CID when divPref exists in personalization data',
        },
      ].map(divPref =>
        test(`${divPref.description}`, () => {
          testData.useDivPref = true;
          const data = divPref.name === 'MEN' ? personalizationData : { isEmpty: false };
          const divPrefCID = DIV_PREF_VALUES[brandName].categoryIds[divPref.name].cid;
          fetchMock.get('https://api.gap.com/ux/web/productdiscovery-web-experience/products/us/on?cid=10018', {
            status: 201,
            body: { data: apiMockData },
          });
          fetchMock.get('https://api.gap.com/ux/web/productdiscovery-web-experience/products/us/on?cid=11174', {
            status: 201,
            body: { data: apiMockData },
          });
          component = getPersonalizedComponent(data, testData);
          expect(fetchMock.lastUrl()).toContain(divPrefCID);
        })
      );
    });
  });

  describe('Aggregation Service API', () => {
    describe('Fetch correct CID/PCID from Aggregation Service API', () => {
      const cid = aggregationServiceTestData.cid;
      const pcid = aggregationServiceTestData.pcid;
      const { products, categories } = aggregationServiceMockData;
      const ccList = categories[0].ccList;

      test('should get MarketingFlag, CID & PCID when API call is successful', () => {
        const recommendationResults = prepareDataForRecsAggregationServiceAPI({
          ccList,
          products,
          pcid,
          cid,
        });
        expect(recommendationResults[0].MarketingFlag).toEqual(products[0].styleMarketingFlags[0]);
        expect(recommendationResults[0].DetailURL).toContain(`pcid=${aggregationServiceTestData.pcid}`);
        expect(recommendationResults[0].DetailURL).toContain(`cid=${aggregationServiceTestData.cid}`);
      });
      describe('productAdapter converts product list properly', () => {
        test('products in result (productList) should match the length of products', () => {
          const productList = productAdapter(products);
          expect(Object.keys(productList).length).toEqual(products.length);
        });
        test('each key in productList should match styleId in corresponding value', () => {
          const productList = productAdapter(products);
          const productListKeys = Object.keys(productList);
          productListKeys.forEach(key => {
            expect(key).toEqual(productList[key].styleId);
          });
        });
      });
    });
  });
});
