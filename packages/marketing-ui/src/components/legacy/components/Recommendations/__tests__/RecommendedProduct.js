// @ts-nocheck
import React from 'react';
import { render, renderHook, act } from 'test-utils';
import { mountWithContext } from 'test-utils';
import { cloneDeep } from 'lodash';
import { SMALL, LARGE } from '@ecom-next/core/breakpoint-provider';
import { LocalizationProvider, normalizeLocale } from '@ecom-next/sitewide/localization-provider';
import { ReviewRatings } from '@ecom-next/core/legacy/review-ratings';
import { Link } from '@ecom-next/core/migration/link';
import RecommendedProduct from '../RecommendedProduct';
import { getPercentage } from '../helpers/recommendedProductHelpers';
import { mockProps, mockPropsWithDraggable, mockPropsWithoutImageStyles, mockPropsWithRange } from '../__fixtures__/recommendedProduct-mockProps';
import translations from '../__fixtures__/translation-data';
import useDragDetection from '../helpers/use-drag-detection';

let component;
let recommendedProduct;
//  The space for non-english markets differs from the english market character.
const NON_ENGLISH_SPACE_CHAR = ' ';
const mountComponent = (props, options) => {
  const dataToUse = props || mockProps;
  component = mountWithContext(
    <LocalizationProvider
      locale={options?.appState?.locale ?? 'en_US'}
      translations={translations[normalizeLocale(options?.appState?.locale ?? 'en_US')].translation}
    >
      <RecommendedProduct cnt={0} product='clear.gif' {...dataToUse} />
    </LocalizationProvider>,
    options
  );
  recommendedProduct = component.find('RecommendedProduct');
};

const renderRecommendedProduct = (props = mockProps, options) =>
  render(<RecommendedProduct cnt={0} product='clear.gif' {...props} />, {
    localization: {
      locale: 'en_US',
      translations,
    },
    ...options,
  });

// to mock the non-deterministic IDs generated in @core-ui/core/review-ratings
jest.mock('random-uuid-v4', () => {
  let value = 0;
  return () => {
    value += 1;
    return value;
  };
});

describe('RecommendedProduct', () => {
  afterEach(() => {
    if (component) {
      component.unmount();
      component = null;
    }
  });

  describe('renders ProductName correctly', () => {
    test('should render string normally', () => {
      const { queryAllByText } = renderRecommendedProduct();
      expect(queryAllByText(`${mockProps.product.ProductName}`)[0]).toBeInTheDocument();
    });
    test('should render html tags as html', () => {
      const props = cloneDeep(mockProps);
      props.product.ProductName = '<i>Denim Pants</i>';
      const { container } = renderRecommendedProduct(props);
      expect(container.querySelector('i')).toBeInTheDocument();
    });
  });

  describe('Link uses pageType for TID', () => {
    ['home', 'division', 'category', 'product', 'shoppingBag'].map(pageType =>
      test(`TID contains ${pageType} PageType`, () => {
        const passProps = cloneDeep(mockProps);
        passProps.pageType = pageType;
        mountComponent(passProps, { appState: { pageType } });
        const href = recommendedProduct.find(Link).prop('to');
        expect(href).toMatch(passProps.pageType);
      })
    );
  });

  describe('Link uses scheme for TID', () => {
    test('TID matches Scheme', () => {
      mountComponent();
      const href = recommendedProduct.find(Link).prop('to');
      expect(href).toMatch(mockProps.data.scheme);
    });
  });

  describe('Uses Prop TID when passed in props', () => {
    test('TID matches prop.data.tid', () => {
      const passProps = {
        ...mockProps,
      };
      passProps.data.tid = 'TEST-TID';
      mountComponent(passProps);
      const href = recommendedProduct.find(Link).prop('to');
      expect(href).toMatch(passProps.data.tid);
    });
  });

  describe('Uses Props productsPerRow and productsPerRow to determine width of card based on breakpoint', () => {
    test('productCard width should be 100 / productsPerRow.desktop on large breakpoint', () => {
      mountComponent(null, { breakpoint: LARGE });
      expect(recommendedProduct).toHaveStyleRule('width', `${100 / mockProps.data.gridLayout.productsPerRow.desktop}%`);
    });

    test('productCard width should be 100 / productsPerRow.mobile on small breakpoint', () => {
      mountComponent(null, { breakpoint: SMALL });
      expect(recommendedProduct).toHaveStyleRule('width', `${100 / mockProps.data.gridLayout.productsPerRow.mobile}%`);
    });
  });

  describe('Price Styling and Visibility', () => {
    describe('default pricing with priceFlag set to true', () => {
      test('has an original price', () => {
        const { getByLabelText } = renderRecommendedProduct();
        const ariaText = getByLabelText(`${mockProps.data.priceWasText} ${mockProps.product.OriginalPrice}`);
        const selectorTextElement = ariaText.firstChild.nodeValue;
        expect(selectorTextElement).toBe(`$${mockProps.product.OriginalPrice}`);
      });

      test('has a percent off price', () => {
        const { getByText } = renderRecommendedProduct(mockPropsWithoutImageStyles);

        const getPrecentageResults = getPercentage(mockProps.product.CurrentPrice, mockProps.product.OriginalPrice);
        const percentageString = `${getPrecentageResults}% ${mockProps.data.priceOffText}`;
        expect(getByText(percentageString).toBeInTheDocument);
      });

      test('has a sale price', () => {
        const { getByText } = renderRecommendedProduct(mockPropsWithoutImageStyles);

        const salesPrice = getByText(`${mockPropsWithoutImageStyles.data.priceNowText} $${mockPropsWithoutImageStyles.product.CurrentPrice}`);
        expect(salesPrice.toBeInTheDocument);
      });
    });

    describe('default pricing with priceFlag set to true and priceMarkdownRange set to true', () => {
      let props;
      beforeEach(() => {
        props = cloneDeep(mockPropsWithRange);
        props.product.MaxStyleRegularPrice = '10.00';
        props.product.CurrentPriceRange = '10-20';
        props.product.DiscountRange = '10%-20%';
      });
      it('the pricing range should be displayed', () => {
        const { queryByTestId } = renderRecommendedProduct(props);
        const blockPriceDiv = queryByTestId('price-block-range');

        expect(blockPriceDiv).toBeInTheDocument();
      });
    });

    describe('default pricing with priceFlag or priceMarkdownRange set to false', () => {
      let props;
      beforeEach(() => {
        props = cloneDeep(mockPropsWithRange);
        props.product.MaxStyleRegularPrice = '';
        props.product.CurrentPriceRange = '';
        props.product.DiscountRange = '';
        props.data.priceMarkdownRange = false;
        props.data.priceFlag = false;
      });
      it('the pricing range should not be displayed', () => {
        const { queryByTestId } = renderRecommendedProduct(props);
        const blockPriceDiv = queryByTestId('price-block-range');

        expect(blockPriceDiv).not.toBeInTheDocument();
      });
    });

    describe('original price not present', () => {
      let props;
      beforeEach(() => {
        props = cloneDeep(mockPropsWithoutImageStyles);
        props.product.OriginalPrice = '';
      });

      test('does not render original price text', () => {
        // supposedly this should never happen with our data sources though
        const { queryByTestId } = renderRecommendedProduct(props);
        const blockPriceDiv = queryByTestId('price-block');

        expect(blockPriceDiv).not.toBeInTheDocument();
      });

      test('does not render a sale price', () => {
        const { queryByTestId } = renderRecommendedProduct(props);
        const blockSalesPriceDiv = queryByTestId('price-block-now');

        expect(blockSalesPriceDiv).not.toBeInTheDocument();
      });
    });

    describe('price flag set to false', () => {
      let props;
      beforeEach(() => {
        props = cloneDeep(mockPropsWithoutImageStyles);
        props.data.priceFlag = false;
      });

      test('does not render an original price', () => {
        const { queryByTestId } = renderRecommendedProduct(props);
        const blockPriceDiv = queryByTestId('price-block');

        expect(blockPriceDiv).not.toBeInTheDocument();
      });

      test('does not render a sale price', () => {
        const { queryByTestId } = renderRecommendedProduct(props);
        const blockSalesPriceDiv = queryByTestId('price-block-now');

        expect(blockSalesPriceDiv).not.toBeInTheDocument();
      });
    });

    describe('sale price not present', () => {
      const props = cloneDeep(mockPropsWithoutImageStyles);
      props.data.priceFlag = false;
      const { queryByTestId } = renderRecommendedProduct(props);
      const blockSalesPriceDiv = queryByTestId('price-block-now');

      expect(blockSalesPriceDiv).not.toBeInTheDocument();
    });

    describe('original AND sale price not present', () => {
      let props;
      beforeEach(() => {
        props = cloneDeep(mockPropsWithoutImageStyles);
        props.product.OriginalPrice = '';
        props.product.CurrentPrice = '';
      });

      test('does not have original price text', () => {
        const { queryByTestId } = renderRecommendedProduct(props);
        const blockPriceDiv = queryByTestId('price-block');

        expect(blockPriceDiv).not.toBeInTheDocument();
      });

      test('does not render a sale price', () => {
        const { queryByTestId } = renderRecommendedProduct(props);
        const blockSalesPriceDiv = queryByTestId('price-block-now');

        expect(blockSalesPriceDiv).not.toBeInTheDocument();
      });
    });

    describe('original AND sale price the same', () => {
      let props;

      beforeEach(() => {
        props = cloneDeep(mockPropsWithoutImageStyles);
        props.product.OriginalPrice = '7.00';
        props.product.CurrentPrice = '7.00';
      });

      test('has an original price', () => {
        const { queryByTestId } = renderRecommendedProduct(props);
        const blockPriceDiv = queryByTestId('price-block');

        expect(blockPriceDiv).toBeInTheDocument();
      });

      test('does not render a sale price', () => {
        const { queryByTestId } = renderRecommendedProduct(props);
        const blockSalesPriceDiv = queryByTestId('price-block-now');

        expect(blockSalesPriceDiv).not.toBeInTheDocument();
      });
    });

    describe('edge case for breaking string comparison', () => {
      // found this edge case while testing
      // if currentPrice is '7.00' and originalPrice is '16.99'
      // since they are strings, the comparison of '7.00' < '16.99' is false...
      // so we needed to convert to real numbers to fix
      let props;
      beforeEach(() => {
        props = cloneDeep(mockPropsWithoutImageStyles);
        props.product.OriginalPrice = '16.99';
        props.product.CurrentPrice = '7.00';
      });

      test('has an original price', () => {
        const { getByLabelText, queryAllByText } = renderRecommendedProduct(props);

        expect(queryAllByText(`$${props.product.OriginalPrice}`).toBeInTheDocument);
        expect(getByLabelText(`${props.data.priceWasText} ${props.product.OriginalPrice}`).toBeInTheDocument);
      });

      test('has a sale price', () => {
        const { getByText } = renderRecommendedProduct(props);
        expect(getByText(`Now $${props.product.CurrentPrice}`));
      });
    });

    describe('default pricing with showPercentage set to true', () => {
      let props;

      beforeEach(() => {
        props = cloneDeep(mockPropsWithoutImageStyles);
        props.product.showPercentage = true;
      });

      test('has an original price', () => {
        const { getByLabelText, queryAllByText } = renderRecommendedProduct(props);

        expect(queryAllByText(`$${props.product.OriginalPrice}`).toBeInTheDocument);
        expect(getByLabelText(`${props.data.priceWasText} ${props.product.OriginalPrice}`).toBeInTheDocument);
      });

      test('has a sale price', () => {
        const { getByLabelText } = renderRecommendedProduct(props);
        expect(getByLabelText(`${props.data.priceWasText} ${props.product.OriginalPrice}`).toBeInTheDocument);
      });

      test('has a percentage', () => {
        const { queryAllByText } = renderRecommendedProduct(props);
        const getPrecentageResults = getPercentage(props.product.CurrentPrice, props.product.OriginalPrice);
        const percentageString = `${getPrecentageResults}% ${props.data.priceOffText}`;
        expect(queryAllByText(percentageString).toBeInTheDocument);
      });
    });

    describe('Marketing Styling and Visibility', () => {
      let props;
      beforeEach(() => {
        props = cloneDeep(mockProps);
        props.data.showMarketingFlag = true;
      });

      describe('showMarketingFlag set to true', () => {
        const props = cloneDeep(mockProps);
        props.data.showMarketingFlag = true;
        const { queryAllByText } = renderRecommendedProduct(props);
        const marketingFlagMsg = `${props.product.MarketingFlag}`;
        const getText = queryAllByText(marketingFlagMsg)[0];
        test('has a marketingFlag', () => {
          expect(getText.toBeInTheDocument);
        });
      });
    });

    describe('Certona Rating Styling and Visibility', () => {
      describe('render Certona rating', () => {
        [true, false].map(val =>
          it(`showRating prop set to ${val}`, () => {
            const passProps = cloneDeep(mockProps);
            passProps.data.ratings.showRating = val;
            mountComponent(passProps);
            const recProductComponent = recommendedProduct.find(ReviewRatings);
            if (val) {
              expect(recProductComponent).toExist();
            } else {
              expect(recProductComponent).not.toExist();
            }
          })
        );
      });
    });

    describe('Strike Through Styling on Original Price', () => {
      let saleProps;

      describe('when there is a sale and strikeThroughOriginalPriceFlag set to true', () => {
        beforeEach(() => {
          saleProps = cloneDeep(mockProps);
          saleProps.data.strikeThroughOriginalPriceFlag = true;
        });

        test('renders strike through on original price', () => {
          const { getByLabelText } = renderRecommendedProduct(saleProps);
          const ariaText = getByLabelText(`${saleProps.data.priceWasText} ${saleProps.product.OriginalPrice}`);
          const textStyles = ariaText.style;
          const styleObject = { 'text-decoration': 'line-through' };
          expect(textStyles._values).toEqual(expect.objectContaining(styleObject));
        });
      });
      describe('when there is not a sale and strikeThroughOriginalPriceFlag set to true', () => {
        let passProps;
        beforeEach(() => {
          passProps = cloneDeep(mockProps);
          passProps.data.strikeThroughOriginalPriceFlag = true;
          passProps.product.OriginalPrice = '7.00';
          passProps.product.CurrentPrice = '7.00';
        });

        test('does not render strike through on original price', () => {
          const { queryByTestId } = renderRecommendedProduct(passProps);
          const priceText = queryByTestId('price-block');
          const textStyles = priceText.firstChild.style;
          const styleObject = { 'text-decoration': 'line-through' };

          expect(textStyles._values).toEqual(expect.not.objectContaining(styleObject));
        });
      });
    });

    describe('currency formatter', () => {
      const props = cloneDeep(mockProps);
      props.product.CurrentPrice = '39.99';
      const price = props.product.CurrentPrice;
      const localePrices = [
        {
          locale: 'en-US',
          price: `$${price}`,
        },
        {
          locale: 'fr-CA',
          price: `${price}${NON_ENGLISH_SPACE_CHAR}$CA`.replace('.', ','),
        },
        {
          locale: 'en-CA',
          price: `CA$${price}`,
        },
      ];

      localePrices.forEach(localeTestCase => {
        props.product.CurrentPrice = '39.99';
        test(`should properly format ${localeTestCase.locale} ${localeTestCase.price} currency`, () => {
          const { container } = renderRecommendedProduct(props, {
            localization: {
              locale: localeTestCase.locale,
              translations,
            },
          });
          const priceBlock = container.querySelector("[data-testid='price-block-now']");
          const innerHTMLValue = priceBlock.innerHTML;
          const sanitize = value => value.replace('Now', '').replace('&nbsp;', ' ').trim();

          expect(sanitize(innerHTMLValue)).toContain(localeTestCase.price);
        });
      });
    });

    describe('product card image style', () => {
      test('when product card image styles provided, styles are applied to image', () => {
        const { container } = renderRecommendedProduct(mockProps);
        const img = container.querySelector('img');
        expect(img).toHaveStyleRule('border', '13px solid purple');
      });
    });
  });
});

describe('useDragDetection', () => {
  test('returns handleMouseDown function and dragging state', () => {
    const { result } = renderHook(() => useDragDetection());
    expect(result.current.handleMouseDown).toBeInstanceOf(Function);
    expect(result.current.dragging).toBe(false);
  });
});

describe('RecommendedProduct snapshot', () => {
  it('should match the snapshot', () => {
    const component = renderRecommendedProduct();
    expect(component.container).toMatchSnapshot();
  });
  it('should match the snapshot with draggable props', () => {
    const component = renderRecommendedProduct(mockPropsWithDraggable);
    expect(component.container).toMatchSnapshot();
  });
});
