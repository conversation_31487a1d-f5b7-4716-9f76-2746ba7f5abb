// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`<CertonaRatings /> Snapshots renders default state correctly Athleta 1`] = `
<DocumentFragment>
  .emotion-0 {
  font-family: 'Avenir Next','Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  color: #666;
  overflow: hidden;
  text-overflow: ellipsis;
  line-height: 1.2;
  white-space: normal;
  margin-top: 0.5rem;
  position: relative;
  text-align: left;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

.emotion-1 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  -webkit-box-flex-flow: wrap;
  -webkit-flex-flow: wrap;
  -ms-flex-flow: wrap;
  flex-flow: wrap;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: unset;
}

.emotion-2 {
  position: absolute;
  height: 1px;
  width: 1px;
  overflow: hidden;
  clip: rect(0 0 0 0);
}

.emotion-3 {
  position: relative;
  width: 93px;
  margin-right: 0.8125em;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-4 {
  width: 100%;
  margin: 0;
  padding: 0;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: space-around;
  -ms-flex-pack: space-around;
  -webkit-justify-content: space-around;
  justify-content: space-around;
  margin-block-start: 0;
  margin-block-end: 0;
  -webkit-margin-start: 0;
  margin-inline-start: 0;
  -webkit-margin-end: 0;
  margin-inline-end: 0;
}

.emotion-4 span {
  width: 100%;
  margin: 0;
}

.emotion-5 {
  display: inline-block;
  height: auto;
  width: auto;
  min-height: auto;
  min-width: auto;
}

.emotion-5 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-5 svg path {
  fill: #111111;
}

.emotion-5 svg rect {
  fill: #111111;
}

.emotion-9 {
  display: inline-block;
  height: auto;
  width: auto;
  min-height: auto;
  min-width: auto;
}

.emotion-9 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-9 svg path {
  fill: #111111;
}

.emotion-9 svg rect {
  fill: #111111;
}

.emotion-9 svg path {
  fill-opacity: 20%;
}

.emotion-10 {
  color: #666;
  border-bottom: 1px solid #666;
  border-top: 1px solid transparent;
  padding-bottom: 1px;
  font-family: 'Avenir Next','Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 0.8125em;
  margin-left: 0;
  margin-top: -4px;
}

<div
    class="emotion-0"
  >
    <a
      class="emotion-1"
      data-testid="reviewRatings"
      tabindex="0"
    >
      <span
        class="emotion-2"
        data-testid="reviewRatingsAriaLabel"
      >
        with an average rating of 4 out of 5, 3 Reviews
      </span>
      <div
        class="emotion-3"
      >
        <div>
          <svg
            style="width: 0px; height: 0px; display: block;"
            viewBox="0 0 14 13"
            xmlns="http://www.w3.org/2000/svg"
          >
            <defs>
              <lineargradient
                id="fractional-gradient_3"
                x1="0"
                x2="100%"
                y1="0"
                y2="0"
              >
                <stop
                  offset="0%"
                  stop-color="#111111"
                  stop-opacity="100%"
                />
                <stop
                  offset="0%"
                  stop-color="#111111"
                  stop-opacity="100%"
                />
                <stop
                  offset="0%"
                  stop-color="#111111"
                  stop-opacity="20%"
                />
                <stop
                  offset="100%"
                  stop-color="#111111"
                  stop-opacity="20%"
                />
              </lineargradient>
            </defs>
          </svg>
        </div>
        <figure
          class="emotion-4"
        >
          <span
            aria-hidden="true"
            class="emotion-5"
          >
            <svg
              fill="none"
              viewBox="0 0 14 13"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                clip-rule="evenodd"
                d="M7.02697 9.38889l-4.219 3.53831 1.40821-4.98276L0 4.76725l5.07966-.0364L7.02697 0l1.89337 4.73085L14 4.76725 9.83776 7.94444 11.2431 13 7.02697 9.38889z"
                fill="#111111"
                fill-rule="evenodd"
              />
            </svg>
          </span>
          <span
            aria-hidden="true"
            class="emotion-5"
          >
            <svg
              fill="none"
              viewBox="0 0 14 13"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                clip-rule="evenodd"
                d="M7.02697 9.38889l-4.219 3.53831 1.40821-4.98276L0 4.76725l5.07966-.0364L7.02697 0l1.89337 4.73085L14 4.76725 9.83776 7.94444 11.2431 13 7.02697 9.38889z"
                fill="#111111"
                fill-rule="evenodd"
              />
            </svg>
          </span>
          <span
            aria-hidden="true"
            class="emotion-5"
          >
            <svg
              fill="none"
              viewBox="0 0 14 13"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                clip-rule="evenodd"
                d="M7.02697 9.38889l-4.219 3.53831 1.40821-4.98276L0 4.76725l5.07966-.0364L7.02697 0l1.89337 4.73085L14 4.76725 9.83776 7.94444 11.2431 13 7.02697 9.38889z"
                fill="#111111"
                fill-rule="evenodd"
              />
            </svg>
          </span>
          <span
            aria-hidden="true"
            class="emotion-5"
          >
            <svg
              fill="none"
              viewBox="0 0 14 13"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                clip-rule="evenodd"
                d="M7.02697 9.38889l-4.219 3.53831 1.40821-4.98276L0 4.76725l5.07966-.0364L7.02697 0l1.89337 4.73085L14 4.76725 9.83776 7.94444 11.2431 13 7.02697 9.38889z"
                fill="#111111"
                fill-rule="evenodd"
              />
            </svg>
          </span>
          <span
            aria-hidden="true"
            class="emotion-9"
          >
            <svg
              fill="none"
              viewBox="0 0 14 13"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                clip-rule="evenodd"
                d="M7.02697 9.38889l-4.219 3.53831 1.40821-4.98276L0 4.76725l5.07966-.0364L7.02697 0l1.89337 4.73085L14 4.76725 9.83776 7.94444 11.2431 13 7.02697 9.38889z"
                fill="#111111"
                fill-rule="evenodd"
              />
            </svg>
          </span>
        </figure>
      </div>
      <div
        aria-hidden="true"
        class="emotion-10"
      >
        3  Reviews
      </div>
    </a>
  </div>
</DocumentFragment>
`;

exports[`<CertonaRatings /> Snapshots renders default state correctly Athleta in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  font-family: 'Avenir Next','Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  color: #666;
  overflow: hidden;
  text-overflow: ellipsis;
  line-height: 1.2;
  white-space: normal;
  margin-top: 0.5rem;
  position: relative;
  text-align: left;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

.emotion-1 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  -webkit-box-flex-flow: wrap;
  -webkit-flex-flow: wrap;
  -ms-flex-flow: wrap;
  flex-flow: wrap;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: unset;
}

.emotion-2 {
  position: absolute;
  height: 1px;
  width: 1px;
  overflow: hidden;
  clip: rect(0 0 0 0);
}

.emotion-3 {
  position: relative;
  width: 93px;
  margin-right: 0.8125em;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-4 {
  width: 100%;
  margin: 0;
  padding: 0;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: space-around;
  -ms-flex-pack: space-around;
  -webkit-justify-content: space-around;
  justify-content: space-around;
  margin-block-start: 0;
  margin-block-end: 0;
  -webkit-margin-start: 0;
  margin-inline-start: 0;
  -webkit-margin-end: 0;
  margin-inline-end: 0;
}

.emotion-4 span {
  width: 100%;
  margin: 0;
}

.emotion-5 {
  display: inline-block;
  height: auto;
  width: auto;
  min-height: auto;
  min-width: auto;
}

.emotion-5 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-5 svg path {
  fill: #111111;
}

.emotion-5 svg rect {
  fill: #111111;
}

.emotion-9 {
  display: inline-block;
  height: auto;
  width: auto;
  min-height: auto;
  min-width: auto;
}

.emotion-9 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-9 svg path {
  fill: #111111;
}

.emotion-9 svg rect {
  fill: #111111;
}

.emotion-9 svg path {
  fill-opacity: 20%;
}

.emotion-10 {
  color: #666;
  border-bottom: 1px solid #666;
  border-top: 1px solid transparent;
  padding-bottom: 1px;
  font-family: 'Avenir Next','Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 0.8125em;
  margin-left: 0;
  margin-top: -4px;
}

<div
    class="emotion-0"
  >
    <a
      class="emotion-1"
      data-testid="reviewRatings"
      tabindex="0"
    >
      <span
        class="emotion-2"
        data-testid="reviewRatingsAriaLabel"
      >
        with an average rating of 4 out of 5, 3 Reviews
      </span>
      <div
        class="emotion-3"
      >
        <div>
          <svg
            style="width: 0px; height: 0px; display: block;"
            viewBox="0 0 14 13"
            xmlns="http://www.w3.org/2000/svg"
          >
            <defs>
              <lineargradient
                id="fractional-gradient_4"
                x1="0"
                x2="100%"
                y1="0"
                y2="0"
              >
                <stop
                  offset="0%"
                  stop-color="#111111"
                  stop-opacity="100%"
                />
                <stop
                  offset="0%"
                  stop-color="#111111"
                  stop-opacity="100%"
                />
                <stop
                  offset="0%"
                  stop-color="#111111"
                  stop-opacity="20%"
                />
                <stop
                  offset="100%"
                  stop-color="#111111"
                  stop-opacity="20%"
                />
              </lineargradient>
            </defs>
          </svg>
        </div>
        <figure
          class="emotion-4"
        >
          <span
            aria-hidden="true"
            class="emotion-5"
          >
            <svg
              fill="none"
              viewBox="0 0 14 13"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                clip-rule="evenodd"
                d="M7.02697 9.38889l-4.219 3.53831 1.40821-4.98276L0 4.76725l5.07966-.0364L7.02697 0l1.89337 4.73085L14 4.76725 9.83776 7.94444 11.2431 13 7.02697 9.38889z"
                fill="#111111"
                fill-rule="evenodd"
              />
            </svg>
          </span>
          <span
            aria-hidden="true"
            class="emotion-5"
          >
            <svg
              fill="none"
              viewBox="0 0 14 13"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                clip-rule="evenodd"
                d="M7.02697 9.38889l-4.219 3.53831 1.40821-4.98276L0 4.76725l5.07966-.0364L7.02697 0l1.89337 4.73085L14 4.76725 9.83776 7.94444 11.2431 13 7.02697 9.38889z"
                fill="#111111"
                fill-rule="evenodd"
              />
            </svg>
          </span>
          <span
            aria-hidden="true"
            class="emotion-5"
          >
            <svg
              fill="none"
              viewBox="0 0 14 13"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                clip-rule="evenodd"
                d="M7.02697 9.38889l-4.219 3.53831 1.40821-4.98276L0 4.76725l5.07966-.0364L7.02697 0l1.89337 4.73085L14 4.76725 9.83776 7.94444 11.2431 13 7.02697 9.38889z"
                fill="#111111"
                fill-rule="evenodd"
              />
            </svg>
          </span>
          <span
            aria-hidden="true"
            class="emotion-5"
          >
            <svg
              fill="none"
              viewBox="0 0 14 13"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                clip-rule="evenodd"
                d="M7.02697 9.38889l-4.219 3.53831 1.40821-4.98276L0 4.76725l5.07966-.0364L7.02697 0l1.89337 4.73085L14 4.76725 9.83776 7.94444 11.2431 13 7.02697 9.38889z"
                fill="#111111"
                fill-rule="evenodd"
              />
            </svg>
          </span>
          <span
            aria-hidden="true"
            class="emotion-9"
          >
            <svg
              fill="none"
              viewBox="0 0 14 13"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                clip-rule="evenodd"
                d="M7.02697 9.38889l-4.219 3.53831 1.40821-4.98276L0 4.76725l5.07966-.0364L7.02697 0l1.89337 4.73085L14 4.76725 9.83776 7.94444 11.2431 13 7.02697 9.38889z"
                fill="#111111"
                fill-rule="evenodd"
              />
            </svg>
          </span>
        </figure>
      </div>
      <div
        aria-hidden="true"
        class="emotion-10"
      >
        3  Reviews
      </div>
    </a>
  </div>
</DocumentFragment>
`;

exports[`<CertonaRatings /> Snapshots renders default state correctly BananaRepublic 1`] = `
<DocumentFragment>
  .emotion-0 {
  font-family: 'Lynstone',Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  color: #666;
  overflow: hidden;
  text-overflow: ellipsis;
  line-height: 1.2;
  white-space: normal;
  margin-top: 0.5rem;
  position: relative;
  text-align: center;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

.emotion-1 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  -webkit-box-flex-flow: wrap;
  -webkit-flex-flow: wrap;
  -ms-flex-flow: wrap;
  flex-flow: wrap;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: unset;
}

.emotion-2 {
  position: absolute;
  height: 1px;
  width: 1px;
  overflow: hidden;
  clip: rect(0 0 0 0);
}

.emotion-3 {
  position: relative;
  width: 93px;
  margin-right: 0.8125em;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-4 {
  width: 100%;
  margin: 0;
  padding: 0;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: space-around;
  -ms-flex-pack: space-around;
  -webkit-justify-content: space-around;
  justify-content: space-around;
  margin-block-start: 0;
  margin-block-end: 0;
  -webkit-margin-start: 0;
  margin-inline-start: 0;
  -webkit-margin-end: 0;
  margin-inline-end: 0;
}

.emotion-4 span {
  width: 100%;
  margin: 0;
}

.emotion-5 {
  display: inline-block;
  height: auto;
  width: auto;
  min-height: auto;
  min-width: auto;
}

.emotion-5 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-5 svg path {
  fill: #000;
}

.emotion-5 svg rect {
  fill: #000;
}

.emotion-9 {
  display: inline-block;
  height: auto;
  width: auto;
  min-height: auto;
  min-width: auto;
}

.emotion-9 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-9 svg path {
  fill: #CCC;
}

.emotion-9 svg rect {
  fill: #CCC;
}

.emotion-9 svg path {
  fill-opacity: 100%;
}

.emotion-10 {
  color: #666;
  border-bottom: 1px solid #666;
  border-top: 1px solid transparent;
  padding-bottom: 1px;
  font-family: 'Lynstone',Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 0.8125em;
  margin-left: 0;
  margin-top: -2px;
}

<div
    class="emotion-0"
  >
    <a
      class="emotion-1"
      data-testid="reviewRatings"
      tabindex="0"
    >
      <span
        class="emotion-2"
        data-testid="reviewRatingsAriaLabel"
      >
        with an average rating of 4 out of 5, 3 Reviews
      </span>
      <div
        class="emotion-3"
      >
        <div>
          <svg
            style="width: 0px; height: 0px; display: block;"
            viewBox="0 0 14 13"
            xmlns="http://www.w3.org/2000/svg"
          >
            <defs>
              <lineargradient
                id="fractional-gradient_5"
                x1="0"
                x2="100%"
                y1="0"
                y2="0"
              >
                <stop
                  offset="0%"
                  stop-color="#000000"
                  stop-opacity="100%"
                />
                <stop
                  offset="0%"
                  stop-color="#000000"
                  stop-opacity="100%"
                />
                <stop
                  offset="0%"
                  stop-color="#CCC"
                  stop-opacity="100%"
                />
                <stop
                  offset="100%"
                  stop-color="#CCC"
                  stop-opacity="100%"
                />
              </lineargradient>
            </defs>
          </svg>
        </div>
        <figure
          class="emotion-4"
        >
          <span
            aria-hidden="true"
            class="emotion-5"
          >
            <svg
              fill="none"
              viewBox="0 0 14 13"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                clip-rule="evenodd"
                d="M7.02697 9.38889l-4.219 3.53831 1.40821-4.98276L0 4.76725l5.07966-.0364L7.02697 0l1.89337 4.73085L14 4.76725 9.83776 7.94444 11.2431 13 7.02697 9.38889z"
                fill="#000000"
                fill-rule="evenodd"
              />
            </svg>
          </span>
          <span
            aria-hidden="true"
            class="emotion-5"
          >
            <svg
              fill="none"
              viewBox="0 0 14 13"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                clip-rule="evenodd"
                d="M7.02697 9.38889l-4.219 3.53831 1.40821-4.98276L0 4.76725l5.07966-.0364L7.02697 0l1.89337 4.73085L14 4.76725 9.83776 7.94444 11.2431 13 7.02697 9.38889z"
                fill="#000000"
                fill-rule="evenodd"
              />
            </svg>
          </span>
          <span
            aria-hidden="true"
            class="emotion-5"
          >
            <svg
              fill="none"
              viewBox="0 0 14 13"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                clip-rule="evenodd"
                d="M7.02697 9.38889l-4.219 3.53831 1.40821-4.98276L0 4.76725l5.07966-.0364L7.02697 0l1.89337 4.73085L14 4.76725 9.83776 7.94444 11.2431 13 7.02697 9.38889z"
                fill="#000000"
                fill-rule="evenodd"
              />
            </svg>
          </span>
          <span
            aria-hidden="true"
            class="emotion-5"
          >
            <svg
              fill="none"
              viewBox="0 0 14 13"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                clip-rule="evenodd"
                d="M7.02697 9.38889l-4.219 3.53831 1.40821-4.98276L0 4.76725l5.07966-.0364L7.02697 0l1.89337 4.73085L14 4.76725 9.83776 7.94444 11.2431 13 7.02697 9.38889z"
                fill="#000000"
                fill-rule="evenodd"
              />
            </svg>
          </span>
          <span
            aria-hidden="true"
            class="emotion-9"
          >
            <svg
              fill="none"
              viewBox="0 0 14 13"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                clip-rule="evenodd"
                d="M7.02697 9.38889l-4.219 3.53831 1.40821-4.98276L0 4.76725l5.07966-.0364L7.02697 0l1.89337 4.73085L14 4.76725 9.83776 7.94444 11.2431 13 7.02697 9.38889z"
                fill="#CCC"
                fill-rule="evenodd"
              />
            </svg>
          </span>
        </figure>
      </div>
      <div
        aria-hidden="true"
        class="emotion-10"
      >
        3  Reviews
      </div>
    </a>
  </div>
</DocumentFragment>
`;

exports[`<CertonaRatings /> Snapshots renders default state correctly BananaRepublic in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  font-family: 'Lynstone',Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  color: #666;
  overflow: hidden;
  text-overflow: ellipsis;
  line-height: 1.2;
  white-space: normal;
  margin-top: 0.5rem;
  position: relative;
  text-align: center;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

.emotion-1 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  -webkit-box-flex-flow: wrap;
  -webkit-flex-flow: wrap;
  -ms-flex-flow: wrap;
  flex-flow: wrap;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: unset;
}

.emotion-2 {
  position: absolute;
  height: 1px;
  width: 1px;
  overflow: hidden;
  clip: rect(0 0 0 0);
}

.emotion-3 {
  position: relative;
  width: 93px;
  margin-right: 0.8125em;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-4 {
  width: 100%;
  margin: 0;
  padding: 0;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: space-around;
  -ms-flex-pack: space-around;
  -webkit-justify-content: space-around;
  justify-content: space-around;
  margin-block-start: 0;
  margin-block-end: 0;
  -webkit-margin-start: 0;
  margin-inline-start: 0;
  -webkit-margin-end: 0;
  margin-inline-end: 0;
}

.emotion-4 span {
  width: 100%;
  margin: 0;
}

.emotion-5 {
  display: inline-block;
  height: auto;
  width: auto;
  min-height: auto;
  min-width: auto;
}

.emotion-5 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-5 svg path {
  fill: #000;
}

.emotion-5 svg rect {
  fill: #000;
}

.emotion-9 {
  display: inline-block;
  height: auto;
  width: auto;
  min-height: auto;
  min-width: auto;
}

.emotion-9 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-9 svg path {
  fill: #CCC;
}

.emotion-9 svg rect {
  fill: #CCC;
}

.emotion-9 svg path {
  fill-opacity: 100%;
}

.emotion-10 {
  color: #666;
  border-bottom: 1px solid #666;
  border-top: 1px solid transparent;
  padding-bottom: 1px;
  font-family: 'Lynstone',Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 0.8125em;
  margin-left: 0;
  margin-top: -2px;
}

<div
    class="emotion-0"
  >
    <a
      class="emotion-1"
      data-testid="reviewRatings"
      tabindex="0"
    >
      <span
        class="emotion-2"
        data-testid="reviewRatingsAriaLabel"
      >
        with an average rating of 4 out of 5, 3 Reviews
      </span>
      <div
        class="emotion-3"
      >
        <div>
          <svg
            style="width: 0px; height: 0px; display: block;"
            viewBox="0 0 14 13"
            xmlns="http://www.w3.org/2000/svg"
          >
            <defs>
              <lineargradient
                id="fractional-gradient_6"
                x1="0"
                x2="100%"
                y1="0"
                y2="0"
              >
                <stop
                  offset="0%"
                  stop-color="#000000"
                  stop-opacity="100%"
                />
                <stop
                  offset="0%"
                  stop-color="#000000"
                  stop-opacity="100%"
                />
                <stop
                  offset="0%"
                  stop-color="#CCC"
                  stop-opacity="100%"
                />
                <stop
                  offset="100%"
                  stop-color="#CCC"
                  stop-opacity="100%"
                />
              </lineargradient>
            </defs>
          </svg>
        </div>
        <figure
          class="emotion-4"
        >
          <span
            aria-hidden="true"
            class="emotion-5"
          >
            <svg
              fill="none"
              viewBox="0 0 14 13"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                clip-rule="evenodd"
                d="M7.02697 9.38889l-4.219 3.53831 1.40821-4.98276L0 4.76725l5.07966-.0364L7.02697 0l1.89337 4.73085L14 4.76725 9.83776 7.94444 11.2431 13 7.02697 9.38889z"
                fill="#000000"
                fill-rule="evenodd"
              />
            </svg>
          </span>
          <span
            aria-hidden="true"
            class="emotion-5"
          >
            <svg
              fill="none"
              viewBox="0 0 14 13"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                clip-rule="evenodd"
                d="M7.02697 9.38889l-4.219 3.53831 1.40821-4.98276L0 4.76725l5.07966-.0364L7.02697 0l1.89337 4.73085L14 4.76725 9.83776 7.94444 11.2431 13 7.02697 9.38889z"
                fill="#000000"
                fill-rule="evenodd"
              />
            </svg>
          </span>
          <span
            aria-hidden="true"
            class="emotion-5"
          >
            <svg
              fill="none"
              viewBox="0 0 14 13"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                clip-rule="evenodd"
                d="M7.02697 9.38889l-4.219 3.53831 1.40821-4.98276L0 4.76725l5.07966-.0364L7.02697 0l1.89337 4.73085L14 4.76725 9.83776 7.94444 11.2431 13 7.02697 9.38889z"
                fill="#000000"
                fill-rule="evenodd"
              />
            </svg>
          </span>
          <span
            aria-hidden="true"
            class="emotion-5"
          >
            <svg
              fill="none"
              viewBox="0 0 14 13"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                clip-rule="evenodd"
                d="M7.02697 9.38889l-4.219 3.53831 1.40821-4.98276L0 4.76725l5.07966-.0364L7.02697 0l1.89337 4.73085L14 4.76725 9.83776 7.94444 11.2431 13 7.02697 9.38889z"
                fill="#000000"
                fill-rule="evenodd"
              />
            </svg>
          </span>
          <span
            aria-hidden="true"
            class="emotion-9"
          >
            <svg
              fill="none"
              viewBox="0 0 14 13"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                clip-rule="evenodd"
                d="M7.02697 9.38889l-4.219 3.53831 1.40821-4.98276L0 4.76725l5.07966-.0364L7.02697 0l1.89337 4.73085L14 4.76725 9.83776 7.94444 11.2431 13 7.02697 9.38889z"
                fill="#CCC"
                fill-rule="evenodd"
              />
            </svg>
          </span>
        </figure>
      </div>
      <div
        aria-hidden="true"
        class="emotion-10"
      >
        3  Reviews
      </div>
    </a>
  </div>
</DocumentFragment>
`;

exports[`<CertonaRatings /> Snapshots renders default state correctly BananaRepublicFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  font-family: 'Lynstone',Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  color: #666;
  overflow: hidden;
  text-overflow: ellipsis;
  line-height: 1.2;
  white-space: normal;
  margin-top: 0.5rem;
  position: relative;
  text-align: center;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

.emotion-1 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  -webkit-box-flex-flow: wrap;
  -webkit-flex-flow: wrap;
  -ms-flex-flow: wrap;
  flex-flow: wrap;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: unset;
}

.emotion-2 {
  position: absolute;
  height: 1px;
  width: 1px;
  overflow: hidden;
  clip: rect(0 0 0 0);
}

.emotion-3 {
  position: relative;
  width: 93px;
  margin-right: 0.8125em;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-4 {
  width: 100%;
  margin: 0;
  padding: 0;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: space-around;
  -ms-flex-pack: space-around;
  -webkit-justify-content: space-around;
  justify-content: space-around;
  margin-block-start: 0;
  margin-block-end: 0;
  -webkit-margin-start: 0;
  margin-inline-start: 0;
  -webkit-margin-end: 0;
  margin-inline-end: 0;
}

.emotion-4 span {
  width: 100%;
  margin: 0;
}

.emotion-5 {
  display: inline-block;
  height: auto;
  width: auto;
  min-height: auto;
  min-width: auto;
}

.emotion-5 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-5 svg path {
  fill: #000;
}

.emotion-5 svg rect {
  fill: #000;
}

.emotion-9 {
  display: inline-block;
  height: auto;
  width: auto;
  min-height: auto;
  min-width: auto;
}

.emotion-9 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-9 svg path {
  fill: #CCC;
}

.emotion-9 svg rect {
  fill: #CCC;
}

.emotion-9 svg path {
  fill-opacity: 100%;
}

.emotion-10 {
  color: #666;
  border-bottom: 1px solid #666;
  border-top: 1px solid transparent;
  padding-bottom: 1px;
  font-family: 'Lynstone',Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 0.8125em;
  margin-left: 0;
  margin-top: -2px;
}

<div
    class="emotion-0"
  >
    <a
      class="emotion-1"
      data-testid="reviewRatings"
      tabindex="0"
    >
      <span
        class="emotion-2"
        data-testid="reviewRatingsAriaLabel"
      >
        with an average rating of 4 out of 5, 3 Reviews
      </span>
      <div
        class="emotion-3"
      >
        <div>
          <svg
            style="width: 0px; height: 0px; display: block;"
            viewBox="0 0 14 13"
            xmlns="http://www.w3.org/2000/svg"
          >
            <defs>
              <lineargradient
                id="fractional-gradient_7"
                x1="0"
                x2="100%"
                y1="0"
                y2="0"
              >
                <stop
                  offset="0%"
                  stop-color="#000000"
                  stop-opacity="100%"
                />
                <stop
                  offset="0%"
                  stop-color="#000000"
                  stop-opacity="100%"
                />
                <stop
                  offset="0%"
                  stop-color="#CCC"
                  stop-opacity="100%"
                />
                <stop
                  offset="100%"
                  stop-color="#CCC"
                  stop-opacity="100%"
                />
              </lineargradient>
            </defs>
          </svg>
        </div>
        <figure
          class="emotion-4"
        >
          <span
            aria-hidden="true"
            class="emotion-5"
          >
            <svg
              fill="none"
              viewBox="0 0 14 13"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                clip-rule="evenodd"
                d="M7.02697 9.38889l-4.219 3.53831 1.40821-4.98276L0 4.76725l5.07966-.0364L7.02697 0l1.89337 4.73085L14 4.76725 9.83776 7.94444 11.2431 13 7.02697 9.38889z"
                fill="#000000"
                fill-rule="evenodd"
              />
            </svg>
          </span>
          <span
            aria-hidden="true"
            class="emotion-5"
          >
            <svg
              fill="none"
              viewBox="0 0 14 13"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                clip-rule="evenodd"
                d="M7.02697 9.38889l-4.219 3.53831 1.40821-4.98276L0 4.76725l5.07966-.0364L7.02697 0l1.89337 4.73085L14 4.76725 9.83776 7.94444 11.2431 13 7.02697 9.38889z"
                fill="#000000"
                fill-rule="evenodd"
              />
            </svg>
          </span>
          <span
            aria-hidden="true"
            class="emotion-5"
          >
            <svg
              fill="none"
              viewBox="0 0 14 13"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                clip-rule="evenodd"
                d="M7.02697 9.38889l-4.219 3.53831 1.40821-4.98276L0 4.76725l5.07966-.0364L7.02697 0l1.89337 4.73085L14 4.76725 9.83776 7.94444 11.2431 13 7.02697 9.38889z"
                fill="#000000"
                fill-rule="evenodd"
              />
            </svg>
          </span>
          <span
            aria-hidden="true"
            class="emotion-5"
          >
            <svg
              fill="none"
              viewBox="0 0 14 13"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                clip-rule="evenodd"
                d="M7.02697 9.38889l-4.219 3.53831 1.40821-4.98276L0 4.76725l5.07966-.0364L7.02697 0l1.89337 4.73085L14 4.76725 9.83776 7.94444 11.2431 13 7.02697 9.38889z"
                fill="#000000"
                fill-rule="evenodd"
              />
            </svg>
          </span>
          <span
            aria-hidden="true"
            class="emotion-9"
          >
            <svg
              fill="none"
              viewBox="0 0 14 13"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                clip-rule="evenodd"
                d="M7.02697 9.38889l-4.219 3.53831 1.40821-4.98276L0 4.76725l5.07966-.0364L7.02697 0l1.89337 4.73085L14 4.76725 9.83776 7.94444 11.2431 13 7.02697 9.38889z"
                fill="#CCC"
                fill-rule="evenodd"
              />
            </svg>
          </span>
        </figure>
      </div>
      <div
        aria-hidden="true"
        class="emotion-10"
      >
        3  Reviews
      </div>
    </a>
  </div>
</DocumentFragment>
`;

exports[`<CertonaRatings /> Snapshots renders default state correctly BananaRepublicFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  font-family: 'Lynstone',Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  color: #666;
  overflow: hidden;
  text-overflow: ellipsis;
  line-height: 1.2;
  white-space: normal;
  margin-top: 0.5rem;
  position: relative;
  text-align: center;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

.emotion-1 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  -webkit-box-flex-flow: wrap;
  -webkit-flex-flow: wrap;
  -ms-flex-flow: wrap;
  flex-flow: wrap;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: unset;
}

.emotion-2 {
  position: absolute;
  height: 1px;
  width: 1px;
  overflow: hidden;
  clip: rect(0 0 0 0);
}

.emotion-3 {
  position: relative;
  width: 93px;
  margin-right: 0.8125em;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-4 {
  width: 100%;
  margin: 0;
  padding: 0;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: space-around;
  -ms-flex-pack: space-around;
  -webkit-justify-content: space-around;
  justify-content: space-around;
  margin-block-start: 0;
  margin-block-end: 0;
  -webkit-margin-start: 0;
  margin-inline-start: 0;
  -webkit-margin-end: 0;
  margin-inline-end: 0;
}

.emotion-4 span {
  width: 100%;
  margin: 0;
}

.emotion-5 {
  display: inline-block;
  height: auto;
  width: auto;
  min-height: auto;
  min-width: auto;
}

.emotion-5 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-5 svg path {
  fill: #000;
}

.emotion-5 svg rect {
  fill: #000;
}

.emotion-9 {
  display: inline-block;
  height: auto;
  width: auto;
  min-height: auto;
  min-width: auto;
}

.emotion-9 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-9 svg path {
  fill: #CCC;
}

.emotion-9 svg rect {
  fill: #CCC;
}

.emotion-9 svg path {
  fill-opacity: 100%;
}

.emotion-10 {
  color: #666;
  border-bottom: 1px solid #666;
  border-top: 1px solid transparent;
  padding-bottom: 1px;
  font-family: 'Lynstone',Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 0.8125em;
  margin-left: 0;
  margin-top: -2px;
}

<div
    class="emotion-0"
  >
    <a
      class="emotion-1"
      data-testid="reviewRatings"
      tabindex="0"
    >
      <span
        class="emotion-2"
        data-testid="reviewRatingsAriaLabel"
      >
        with an average rating of 4 out of 5, 3 Reviews
      </span>
      <div
        class="emotion-3"
      >
        <div>
          <svg
            style="width: 0px; height: 0px; display: block;"
            viewBox="0 0 14 13"
            xmlns="http://www.w3.org/2000/svg"
          >
            <defs>
              <lineargradient
                id="fractional-gradient_8"
                x1="0"
                x2="100%"
                y1="0"
                y2="0"
              >
                <stop
                  offset="0%"
                  stop-color="#000000"
                  stop-opacity="100%"
                />
                <stop
                  offset="0%"
                  stop-color="#000000"
                  stop-opacity="100%"
                />
                <stop
                  offset="0%"
                  stop-color="#CCC"
                  stop-opacity="100%"
                />
                <stop
                  offset="100%"
                  stop-color="#CCC"
                  stop-opacity="100%"
                />
              </lineargradient>
            </defs>
          </svg>
        </div>
        <figure
          class="emotion-4"
        >
          <span
            aria-hidden="true"
            class="emotion-5"
          >
            <svg
              fill="none"
              viewBox="0 0 14 13"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                clip-rule="evenodd"
                d="M7.02697 9.38889l-4.219 3.53831 1.40821-4.98276L0 4.76725l5.07966-.0364L7.02697 0l1.89337 4.73085L14 4.76725 9.83776 7.94444 11.2431 13 7.02697 9.38889z"
                fill="#000000"
                fill-rule="evenodd"
              />
            </svg>
          </span>
          <span
            aria-hidden="true"
            class="emotion-5"
          >
            <svg
              fill="none"
              viewBox="0 0 14 13"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                clip-rule="evenodd"
                d="M7.02697 9.38889l-4.219 3.53831 1.40821-4.98276L0 4.76725l5.07966-.0364L7.02697 0l1.89337 4.73085L14 4.76725 9.83776 7.94444 11.2431 13 7.02697 9.38889z"
                fill="#000000"
                fill-rule="evenodd"
              />
            </svg>
          </span>
          <span
            aria-hidden="true"
            class="emotion-5"
          >
            <svg
              fill="none"
              viewBox="0 0 14 13"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                clip-rule="evenodd"
                d="M7.02697 9.38889l-4.219 3.53831 1.40821-4.98276L0 4.76725l5.07966-.0364L7.02697 0l1.89337 4.73085L14 4.76725 9.83776 7.94444 11.2431 13 7.02697 9.38889z"
                fill="#000000"
                fill-rule="evenodd"
              />
            </svg>
          </span>
          <span
            aria-hidden="true"
            class="emotion-5"
          >
            <svg
              fill="none"
              viewBox="0 0 14 13"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                clip-rule="evenodd"
                d="M7.02697 9.38889l-4.219 3.53831 1.40821-4.98276L0 4.76725l5.07966-.0364L7.02697 0l1.89337 4.73085L14 4.76725 9.83776 7.94444 11.2431 13 7.02697 9.38889z"
                fill="#000000"
                fill-rule="evenodd"
              />
            </svg>
          </span>
          <span
            aria-hidden="true"
            class="emotion-9"
          >
            <svg
              fill="none"
              viewBox="0 0 14 13"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                clip-rule="evenodd"
                d="M7.02697 9.38889l-4.219 3.53831 1.40821-4.98276L0 4.76725l5.07966-.0364L7.02697 0l1.89337 4.73085L14 4.76725 9.83776 7.94444 11.2431 13 7.02697 9.38889z"
                fill="#CCC"
                fill-rule="evenodd"
              />
            </svg>
          </span>
        </figure>
      </div>
      <div
        aria-hidden="true"
        class="emotion-10"
      >
        3  Reviews
      </div>
    </a>
  </div>
</DocumentFragment>
`;

exports[`<CertonaRatings /> Snapshots renders default state correctly Gap 1`] = `
<DocumentFragment>
  .emotion-0 {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  color: #666;
  overflow: hidden;
  text-overflow: ellipsis;
  line-height: 1.2;
  white-space: normal;
  margin-top: 0.5rem;
  position: relative;
  text-align: left;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

.emotion-1 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  -webkit-box-flex-flow: wrap;
  -webkit-flex-flow: wrap;
  -ms-flex-flow: wrap;
  flex-flow: wrap;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: unset;
}

.emotion-2 {
  position: absolute;
  height: 1px;
  width: 1px;
  overflow: hidden;
  clip: rect(0 0 0 0);
}

.emotion-3 {
  position: relative;
  width: 93px;
  margin-right: 0.8125em;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-4 {
  width: 100%;
  margin: 0;
  padding: 0;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: space-around;
  -ms-flex-pack: space-around;
  -webkit-justify-content: space-around;
  justify-content: space-around;
  margin-block-start: 0;
  margin-block-end: 0;
  -webkit-margin-start: 0;
  margin-inline-start: 0;
  -webkit-margin-end: 0;
  margin-inline-end: 0;
}

.emotion-4 span {
  width: 100%;
  margin: 0;
}

.emotion-5 {
  display: inline-block;
  height: auto;
  width: auto;
  min-height: auto;
  min-width: auto;
}

.emotion-5 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-5 svg path {
  fill: #0A5694;
}

.emotion-5 svg rect {
  fill: #0A5694;
}

.emotion-9 {
  display: inline-block;
  height: auto;
  width: auto;
  min-height: auto;
  min-width: auto;
}

.emotion-9 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-9 svg path {
  fill: #CCC;
}

.emotion-9 svg rect {
  fill: #CCC;
}

.emotion-9 svg path {
  fill-opacity: 100%;
}

.emotion-10 {
  color: #666;
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 0.8125em;
  margin-left: 0;
  margin-top: 0;
}

<div
    class="emotion-0"
  >
    <a
      class="emotion-1"
      data-testid="reviewRatings"
      tabindex="0"
    >
      <span
        class="emotion-2"
        data-testid="reviewRatingsAriaLabel"
      >
        with an average rating of 4 out of 5, 3 Reviews
      </span>
      <div
        class="emotion-3"
      >
        <div>
          <svg
            style="width: 0px; height: 0px; display: block;"
            viewBox="0 0 14 13"
            xmlns="http://www.w3.org/2000/svg"
          >
            <defs>
              <lineargradient
                id="fractional-gradient_9"
                x1="0"
                x2="100%"
                y1="0"
                y2="0"
              >
                <stop
                  offset="0%"
                  stop-color="#0A5694"
                  stop-opacity="100%"
                />
                <stop
                  offset="0%"
                  stop-color="#0A5694"
                  stop-opacity="100%"
                />
                <stop
                  offset="0%"
                  stop-color="#CCC"
                  stop-opacity="100%"
                />
                <stop
                  offset="100%"
                  stop-color="#CCC"
                  stop-opacity="100%"
                />
              </lineargradient>
            </defs>
          </svg>
        </div>
        <figure
          class="emotion-4"
        >
          <span
            aria-hidden="true"
            class="emotion-5"
          >
            <svg
              fill="none"
              viewBox="0 0 14 13"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                clip-rule="evenodd"
                d="M7.02697 9.38889l-4.219 3.53831 1.40821-4.98276L0 4.76725l5.07966-.0364L7.02697 0l1.89337 4.73085L14 4.76725 9.83776 7.94444 11.2431 13 7.02697 9.38889z"
                fill="#0A5694"
                fill-rule="evenodd"
              />
            </svg>
          </span>
          <span
            aria-hidden="true"
            class="emotion-5"
          >
            <svg
              fill="none"
              viewBox="0 0 14 13"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                clip-rule="evenodd"
                d="M7.02697 9.38889l-4.219 3.53831 1.40821-4.98276L0 4.76725l5.07966-.0364L7.02697 0l1.89337 4.73085L14 4.76725 9.83776 7.94444 11.2431 13 7.02697 9.38889z"
                fill="#0A5694"
                fill-rule="evenodd"
              />
            </svg>
          </span>
          <span
            aria-hidden="true"
            class="emotion-5"
          >
            <svg
              fill="none"
              viewBox="0 0 14 13"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                clip-rule="evenodd"
                d="M7.02697 9.38889l-4.219 3.53831 1.40821-4.98276L0 4.76725l5.07966-.0364L7.02697 0l1.89337 4.73085L14 4.76725 9.83776 7.94444 11.2431 13 7.02697 9.38889z"
                fill="#0A5694"
                fill-rule="evenodd"
              />
            </svg>
          </span>
          <span
            aria-hidden="true"
            class="emotion-5"
          >
            <svg
              fill="none"
              viewBox="0 0 14 13"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                clip-rule="evenodd"
                d="M7.02697 9.38889l-4.219 3.53831 1.40821-4.98276L0 4.76725l5.07966-.0364L7.02697 0l1.89337 4.73085L14 4.76725 9.83776 7.94444 11.2431 13 7.02697 9.38889z"
                fill="#0A5694"
                fill-rule="evenodd"
              />
            </svg>
          </span>
          <span
            aria-hidden="true"
            class="emotion-9"
          >
            <svg
              fill="none"
              viewBox="0 0 14 13"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                clip-rule="evenodd"
                d="M7.02697 9.38889l-4.219 3.53831 1.40821-4.98276L0 4.76725l5.07966-.0364L7.02697 0l1.89337 4.73085L14 4.76725 9.83776 7.94444 11.2431 13 7.02697 9.38889z"
                fill="#CCC"
                fill-rule="evenodd"
              />
            </svg>
          </span>
        </figure>
      </div>
      <div
        aria-hidden="true"
        class="emotion-10"
      >
        3  Reviews
      </div>
    </a>
  </div>
</DocumentFragment>
`;

exports[`<CertonaRatings /> Snapshots renders default state correctly Gap in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  color: #666;
  overflow: hidden;
  text-overflow: ellipsis;
  line-height: 1.2;
  white-space: normal;
  margin-top: 0.5rem;
  position: relative;
  text-align: left;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

.emotion-1 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  -webkit-box-flex-flow: wrap;
  -webkit-flex-flow: wrap;
  -ms-flex-flow: wrap;
  flex-flow: wrap;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: unset;
}

.emotion-2 {
  position: absolute;
  height: 1px;
  width: 1px;
  overflow: hidden;
  clip: rect(0 0 0 0);
}

.emotion-3 {
  position: relative;
  width: 93px;
  margin-right: 0.8125em;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-4 {
  width: 100%;
  margin: 0;
  padding: 0;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: space-around;
  -ms-flex-pack: space-around;
  -webkit-justify-content: space-around;
  justify-content: space-around;
  margin-block-start: 0;
  margin-block-end: 0;
  -webkit-margin-start: 0;
  margin-inline-start: 0;
  -webkit-margin-end: 0;
  margin-inline-end: 0;
}

.emotion-4 span {
  width: 100%;
  margin: 0;
}

.emotion-5 {
  display: inline-block;
  height: auto;
  width: auto;
  min-height: auto;
  min-width: auto;
}

.emotion-5 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-5 svg path {
  fill: #0A5694;
}

.emotion-5 svg rect {
  fill: #0A5694;
}

.emotion-9 {
  display: inline-block;
  height: auto;
  width: auto;
  min-height: auto;
  min-width: auto;
}

.emotion-9 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-9 svg path {
  fill: #CCC;
}

.emotion-9 svg rect {
  fill: #CCC;
}

.emotion-9 svg path {
  fill-opacity: 100%;
}

.emotion-10 {
  color: #666;
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 0.8125em;
  margin-left: 0;
  margin-top: 0;
}

<div
    class="emotion-0"
  >
    <a
      class="emotion-1"
      data-testid="reviewRatings"
      tabindex="0"
    >
      <span
        class="emotion-2"
        data-testid="reviewRatingsAriaLabel"
      >
        with an average rating of 4 out of 5, 3 Reviews
      </span>
      <div
        class="emotion-3"
      >
        <div>
          <svg
            style="width: 0px; height: 0px; display: block;"
            viewBox="0 0 14 13"
            xmlns="http://www.w3.org/2000/svg"
          >
            <defs>
              <lineargradient
                id="fractional-gradient_10"
                x1="0"
                x2="100%"
                y1="0"
                y2="0"
              >
                <stop
                  offset="0%"
                  stop-color="#0A5694"
                  stop-opacity="100%"
                />
                <stop
                  offset="0%"
                  stop-color="#0A5694"
                  stop-opacity="100%"
                />
                <stop
                  offset="0%"
                  stop-color="#CCC"
                  stop-opacity="100%"
                />
                <stop
                  offset="100%"
                  stop-color="#CCC"
                  stop-opacity="100%"
                />
              </lineargradient>
            </defs>
          </svg>
        </div>
        <figure
          class="emotion-4"
        >
          <span
            aria-hidden="true"
            class="emotion-5"
          >
            <svg
              fill="none"
              viewBox="0 0 14 13"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                clip-rule="evenodd"
                d="M7.02697 9.38889l-4.219 3.53831 1.40821-4.98276L0 4.76725l5.07966-.0364L7.02697 0l1.89337 4.73085L14 4.76725 9.83776 7.94444 11.2431 13 7.02697 9.38889z"
                fill="#0A5694"
                fill-rule="evenodd"
              />
            </svg>
          </span>
          <span
            aria-hidden="true"
            class="emotion-5"
          >
            <svg
              fill="none"
              viewBox="0 0 14 13"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                clip-rule="evenodd"
                d="M7.02697 9.38889l-4.219 3.53831 1.40821-4.98276L0 4.76725l5.07966-.0364L7.02697 0l1.89337 4.73085L14 4.76725 9.83776 7.94444 11.2431 13 7.02697 9.38889z"
                fill="#0A5694"
                fill-rule="evenodd"
              />
            </svg>
          </span>
          <span
            aria-hidden="true"
            class="emotion-5"
          >
            <svg
              fill="none"
              viewBox="0 0 14 13"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                clip-rule="evenodd"
                d="M7.02697 9.38889l-4.219 3.53831 1.40821-4.98276L0 4.76725l5.07966-.0364L7.02697 0l1.89337 4.73085L14 4.76725 9.83776 7.94444 11.2431 13 7.02697 9.38889z"
                fill="#0A5694"
                fill-rule="evenodd"
              />
            </svg>
          </span>
          <span
            aria-hidden="true"
            class="emotion-5"
          >
            <svg
              fill="none"
              viewBox="0 0 14 13"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                clip-rule="evenodd"
                d="M7.02697 9.38889l-4.219 3.53831 1.40821-4.98276L0 4.76725l5.07966-.0364L7.02697 0l1.89337 4.73085L14 4.76725 9.83776 7.94444 11.2431 13 7.02697 9.38889z"
                fill="#0A5694"
                fill-rule="evenodd"
              />
            </svg>
          </span>
          <span
            aria-hidden="true"
            class="emotion-9"
          >
            <svg
              fill="none"
              viewBox="0 0 14 13"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                clip-rule="evenodd"
                d="M7.02697 9.38889l-4.219 3.53831 1.40821-4.98276L0 4.76725l5.07966-.0364L7.02697 0l1.89337 4.73085L14 4.76725 9.83776 7.94444 11.2431 13 7.02697 9.38889z"
                fill="#CCC"
                fill-rule="evenodd"
              />
            </svg>
          </span>
        </figure>
      </div>
      <div
        aria-hidden="true"
        class="emotion-10"
      >
        3  Reviews
      </div>
    </a>
  </div>
</DocumentFragment>
`;

exports[`<CertonaRatings /> Snapshots renders default state correctly GapFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  color: #666;
  overflow: hidden;
  text-overflow: ellipsis;
  line-height: 1.2;
  white-space: normal;
  margin-top: 0.5rem;
  position: relative;
  text-align: left;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

.emotion-1 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  -webkit-box-flex-flow: wrap;
  -webkit-flex-flow: wrap;
  -ms-flex-flow: wrap;
  flex-flow: wrap;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: unset;
}

.emotion-2 {
  position: absolute;
  height: 1px;
  width: 1px;
  overflow: hidden;
  clip: rect(0 0 0 0);
}

.emotion-3 {
  position: relative;
  width: 93px;
  margin-right: 0.8125em;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-4 {
  width: 100%;
  margin: 0;
  padding: 0;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: space-around;
  -ms-flex-pack: space-around;
  -webkit-justify-content: space-around;
  justify-content: space-around;
  margin-block-start: 0;
  margin-block-end: 0;
  -webkit-margin-start: 0;
  margin-inline-start: 0;
  -webkit-margin-end: 0;
  margin-inline-end: 0;
}

.emotion-4 span {
  width: 100%;
  margin: 0;
}

.emotion-5 {
  display: inline-block;
  height: auto;
  width: auto;
  min-height: auto;
  min-width: auto;
}

.emotion-5 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-5 svg path {
  fill: #0A5694;
}

.emotion-5 svg rect {
  fill: #0A5694;
}

.emotion-9 {
  display: inline-block;
  height: auto;
  width: auto;
  min-height: auto;
  min-width: auto;
}

.emotion-9 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-9 svg path {
  fill: #CCC;
}

.emotion-9 svg rect {
  fill: #CCC;
}

.emotion-9 svg path {
  fill-opacity: 100%;
}

.emotion-10 {
  color: #666;
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 0.8125em;
  margin-left: 0;
  margin-top: 0;
}

<div
    class="emotion-0"
  >
    <a
      class="emotion-1"
      data-testid="reviewRatings"
      tabindex="0"
    >
      <span
        class="emotion-2"
        data-testid="reviewRatingsAriaLabel"
      >
        with an average rating of 4 out of 5, 3 Reviews
      </span>
      <div
        class="emotion-3"
      >
        <div>
          <svg
            style="width: 0px; height: 0px; display: block;"
            viewBox="0 0 14 13"
            xmlns="http://www.w3.org/2000/svg"
          >
            <defs>
              <lineargradient
                id="fractional-gradient_11"
                x1="0"
                x2="100%"
                y1="0"
                y2="0"
              >
                <stop
                  offset="0%"
                  stop-color="#0A5694"
                  stop-opacity="100%"
                />
                <stop
                  offset="0%"
                  stop-color="#0A5694"
                  stop-opacity="100%"
                />
                <stop
                  offset="0%"
                  stop-color="#CCC"
                  stop-opacity="100%"
                />
                <stop
                  offset="100%"
                  stop-color="#CCC"
                  stop-opacity="100%"
                />
              </lineargradient>
            </defs>
          </svg>
        </div>
        <figure
          class="emotion-4"
        >
          <span
            aria-hidden="true"
            class="emotion-5"
          >
            <svg
              fill="none"
              viewBox="0 0 14 13"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                clip-rule="evenodd"
                d="M7.02697 9.38889l-4.219 3.53831 1.40821-4.98276L0 4.76725l5.07966-.0364L7.02697 0l1.89337 4.73085L14 4.76725 9.83776 7.94444 11.2431 13 7.02697 9.38889z"
                fill="#0A5694"
                fill-rule="evenodd"
              />
            </svg>
          </span>
          <span
            aria-hidden="true"
            class="emotion-5"
          >
            <svg
              fill="none"
              viewBox="0 0 14 13"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                clip-rule="evenodd"
                d="M7.02697 9.38889l-4.219 3.53831 1.40821-4.98276L0 4.76725l5.07966-.0364L7.02697 0l1.89337 4.73085L14 4.76725 9.83776 7.94444 11.2431 13 7.02697 9.38889z"
                fill="#0A5694"
                fill-rule="evenodd"
              />
            </svg>
          </span>
          <span
            aria-hidden="true"
            class="emotion-5"
          >
            <svg
              fill="none"
              viewBox="0 0 14 13"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                clip-rule="evenodd"
                d="M7.02697 9.38889l-4.219 3.53831 1.40821-4.98276L0 4.76725l5.07966-.0364L7.02697 0l1.89337 4.73085L14 4.76725 9.83776 7.94444 11.2431 13 7.02697 9.38889z"
                fill="#0A5694"
                fill-rule="evenodd"
              />
            </svg>
          </span>
          <span
            aria-hidden="true"
            class="emotion-5"
          >
            <svg
              fill="none"
              viewBox="0 0 14 13"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                clip-rule="evenodd"
                d="M7.02697 9.38889l-4.219 3.53831 1.40821-4.98276L0 4.76725l5.07966-.0364L7.02697 0l1.89337 4.73085L14 4.76725 9.83776 7.94444 11.2431 13 7.02697 9.38889z"
                fill="#0A5694"
                fill-rule="evenodd"
              />
            </svg>
          </span>
          <span
            aria-hidden="true"
            class="emotion-9"
          >
            <svg
              fill="none"
              viewBox="0 0 14 13"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                clip-rule="evenodd"
                d="M7.02697 9.38889l-4.219 3.53831 1.40821-4.98276L0 4.76725l5.07966-.0364L7.02697 0l1.89337 4.73085L14 4.76725 9.83776 7.94444 11.2431 13 7.02697 9.38889z"
                fill="#CCC"
                fill-rule="evenodd"
              />
            </svg>
          </span>
        </figure>
      </div>
      <div
        aria-hidden="true"
        class="emotion-10"
      >
        3  Reviews
      </div>
    </a>
  </div>
</DocumentFragment>
`;

exports[`<CertonaRatings /> Snapshots renders default state correctly GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  color: #666;
  overflow: hidden;
  text-overflow: ellipsis;
  line-height: 1.2;
  white-space: normal;
  margin-top: 0.5rem;
  position: relative;
  text-align: left;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

.emotion-1 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  -webkit-box-flex-flow: wrap;
  -webkit-flex-flow: wrap;
  -ms-flex-flow: wrap;
  flex-flow: wrap;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: unset;
}

.emotion-2 {
  position: absolute;
  height: 1px;
  width: 1px;
  overflow: hidden;
  clip: rect(0 0 0 0);
}

.emotion-3 {
  position: relative;
  width: 93px;
  margin-right: 0.8125em;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-4 {
  width: 100%;
  margin: 0;
  padding: 0;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: space-around;
  -ms-flex-pack: space-around;
  -webkit-justify-content: space-around;
  justify-content: space-around;
  margin-block-start: 0;
  margin-block-end: 0;
  -webkit-margin-start: 0;
  margin-inline-start: 0;
  -webkit-margin-end: 0;
  margin-inline-end: 0;
}

.emotion-4 span {
  width: 100%;
  margin: 0;
}

.emotion-5 {
  display: inline-block;
  height: auto;
  width: auto;
  min-height: auto;
  min-width: auto;
}

.emotion-5 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-5 svg path {
  fill: #0A5694;
}

.emotion-5 svg rect {
  fill: #0A5694;
}

.emotion-9 {
  display: inline-block;
  height: auto;
  width: auto;
  min-height: auto;
  min-width: auto;
}

.emotion-9 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-9 svg path {
  fill: #CCC;
}

.emotion-9 svg rect {
  fill: #CCC;
}

.emotion-9 svg path {
  fill-opacity: 100%;
}

.emotion-10 {
  color: #666;
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 0.8125em;
  margin-left: 0;
  margin-top: 0;
}

<div
    class="emotion-0"
  >
    <a
      class="emotion-1"
      data-testid="reviewRatings"
      tabindex="0"
    >
      <span
        class="emotion-2"
        data-testid="reviewRatingsAriaLabel"
      >
        with an average rating of 4 out of 5, 3 Reviews
      </span>
      <div
        class="emotion-3"
      >
        <div>
          <svg
            style="width: 0px; height: 0px; display: block;"
            viewBox="0 0 14 13"
            xmlns="http://www.w3.org/2000/svg"
          >
            <defs>
              <lineargradient
                id="fractional-gradient_12"
                x1="0"
                x2="100%"
                y1="0"
                y2="0"
              >
                <stop
                  offset="0%"
                  stop-color="#0A5694"
                  stop-opacity="100%"
                />
                <stop
                  offset="0%"
                  stop-color="#0A5694"
                  stop-opacity="100%"
                />
                <stop
                  offset="0%"
                  stop-color="#CCC"
                  stop-opacity="100%"
                />
                <stop
                  offset="100%"
                  stop-color="#CCC"
                  stop-opacity="100%"
                />
              </lineargradient>
            </defs>
          </svg>
        </div>
        <figure
          class="emotion-4"
        >
          <span
            aria-hidden="true"
            class="emotion-5"
          >
            <svg
              fill="none"
              viewBox="0 0 14 13"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                clip-rule="evenodd"
                d="M7.02697 9.38889l-4.219 3.53831 1.40821-4.98276L0 4.76725l5.07966-.0364L7.02697 0l1.89337 4.73085L14 4.76725 9.83776 7.94444 11.2431 13 7.02697 9.38889z"
                fill="#0A5694"
                fill-rule="evenodd"
              />
            </svg>
          </span>
          <span
            aria-hidden="true"
            class="emotion-5"
          >
            <svg
              fill="none"
              viewBox="0 0 14 13"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                clip-rule="evenodd"
                d="M7.02697 9.38889l-4.219 3.53831 1.40821-4.98276L0 4.76725l5.07966-.0364L7.02697 0l1.89337 4.73085L14 4.76725 9.83776 7.94444 11.2431 13 7.02697 9.38889z"
                fill="#0A5694"
                fill-rule="evenodd"
              />
            </svg>
          </span>
          <span
            aria-hidden="true"
            class="emotion-5"
          >
            <svg
              fill="none"
              viewBox="0 0 14 13"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                clip-rule="evenodd"
                d="M7.02697 9.38889l-4.219 3.53831 1.40821-4.98276L0 4.76725l5.07966-.0364L7.02697 0l1.89337 4.73085L14 4.76725 9.83776 7.94444 11.2431 13 7.02697 9.38889z"
                fill="#0A5694"
                fill-rule="evenodd"
              />
            </svg>
          </span>
          <span
            aria-hidden="true"
            class="emotion-5"
          >
            <svg
              fill="none"
              viewBox="0 0 14 13"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                clip-rule="evenodd"
                d="M7.02697 9.38889l-4.219 3.53831 1.40821-4.98276L0 4.76725l5.07966-.0364L7.02697 0l1.89337 4.73085L14 4.76725 9.83776 7.94444 11.2431 13 7.02697 9.38889z"
                fill="#0A5694"
                fill-rule="evenodd"
              />
            </svg>
          </span>
          <span
            aria-hidden="true"
            class="emotion-9"
          >
            <svg
              fill="none"
              viewBox="0 0 14 13"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                clip-rule="evenodd"
                d="M7.02697 9.38889l-4.219 3.53831 1.40821-4.98276L0 4.76725l5.07966-.0364L7.02697 0l1.89337 4.73085L14 4.76725 9.83776 7.94444 11.2431 13 7.02697 9.38889z"
                fill="#CCC"
                fill-rule="evenodd"
              />
            </svg>
          </span>
        </figure>
      </div>
      <div
        aria-hidden="true"
        class="emotion-10"
      >
        3  Reviews
      </div>
    </a>
  </div>
</DocumentFragment>
`;

exports[`<CertonaRatings /> Snapshots renders default state correctly OldNavy 1`] = `
<DocumentFragment>
  .emotion-0 {
  font-family: 'Gotham','Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  color: #666;
  overflow: hidden;
  text-overflow: ellipsis;
  line-height: 1.2;
  white-space: normal;
  margin-top: 0.5rem;
  position: relative;
  text-align: center;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

.emotion-1 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  -webkit-box-flex-flow: wrap;
  -webkit-flex-flow: wrap;
  -ms-flex-flow: wrap;
  flex-flow: wrap;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: unset;
}

.emotion-2 {
  position: absolute;
  height: 1px;
  width: 1px;
  overflow: hidden;
  clip: rect(0 0 0 0);
}

.emotion-3 {
  position: relative;
  width: 93px;
  margin-right: 0.8125em;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-4 {
  width: 100%;
  margin: 0;
  padding: 0;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: space-around;
  -ms-flex-pack: space-around;
  -webkit-justify-content: space-around;
  justify-content: space-around;
  margin-block-start: 0;
  margin-block-end: 0;
  -webkit-margin-start: 0;
  margin-inline-start: 0;
  -webkit-margin-end: 0;
  margin-inline-end: 0;
}

.emotion-4 span {
  width: 100%;
  margin: 0;
}

.emotion-5 {
  display: inline-block;
  height: auto;
  width: auto;
  min-height: auto;
  min-width: auto;
}

.emotion-5 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-5 svg path {
  fill: #003764;
}

.emotion-5 svg rect {
  fill: #003764;
}

.emotion-9 {
  display: inline-block;
  height: auto;
  width: auto;
  min-height: auto;
  min-width: auto;
}

.emotion-9 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-9 svg path {
  fill: #CCC;
}

.emotion-9 svg rect {
  fill: #CCC;
}

.emotion-9 svg path {
  fill-opacity: 100%;
}

.emotion-10 {
  color: #666;
  font-family: 'Gotham','Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 0.8125em;
  margin-left: 0;
  margin-top: 0;
}

<div
    class="emotion-0"
  >
    <a
      class="emotion-1"
      data-testid="reviewRatings"
      tabindex="0"
    >
      <span
        class="emotion-2"
        data-testid="reviewRatingsAriaLabel"
      >
        with an average rating of 4 out of 5, 3 Reviews
      </span>
      <div
        class="emotion-3"
      >
        <div>
          <svg
            style="width: 0px; height: 0px; display: block;"
            viewBox="0 0 14 13"
            xmlns="http://www.w3.org/2000/svg"
          >
            <defs>
              <lineargradient
                id="fractional-gradient_13"
                x1="0"
                x2="100%"
                y1="0"
                y2="0"
              >
                <stop
                  offset="0%"
                  stop-color="#003764"
                  stop-opacity="100%"
                />
                <stop
                  offset="0%"
                  stop-color="#003764"
                  stop-opacity="100%"
                />
                <stop
                  offset="0%"
                  stop-color="#CCC"
                  stop-opacity="100%"
                />
                <stop
                  offset="100%"
                  stop-color="#CCC"
                  stop-opacity="100%"
                />
              </lineargradient>
            </defs>
          </svg>
        </div>
        <figure
          class="emotion-4"
        >
          <span
            aria-hidden="true"
            class="emotion-5"
          >
            <svg
              fill="none"
              viewBox="0 0 14 13"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                clip-rule="evenodd"
                d="M7.02697 9.38889l-4.219 3.53831 1.40821-4.98276L0 4.76725l5.07966-.0364L7.02697 0l1.89337 4.73085L14 4.76725 9.83776 7.94444 11.2431 13 7.02697 9.38889z"
                fill="#003764"
                fill-rule="evenodd"
              />
            </svg>
          </span>
          <span
            aria-hidden="true"
            class="emotion-5"
          >
            <svg
              fill="none"
              viewBox="0 0 14 13"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                clip-rule="evenodd"
                d="M7.02697 9.38889l-4.219 3.53831 1.40821-4.98276L0 4.76725l5.07966-.0364L7.02697 0l1.89337 4.73085L14 4.76725 9.83776 7.94444 11.2431 13 7.02697 9.38889z"
                fill="#003764"
                fill-rule="evenodd"
              />
            </svg>
          </span>
          <span
            aria-hidden="true"
            class="emotion-5"
          >
            <svg
              fill="none"
              viewBox="0 0 14 13"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                clip-rule="evenodd"
                d="M7.02697 9.38889l-4.219 3.53831 1.40821-4.98276L0 4.76725l5.07966-.0364L7.02697 0l1.89337 4.73085L14 4.76725 9.83776 7.94444 11.2431 13 7.02697 9.38889z"
                fill="#003764"
                fill-rule="evenodd"
              />
            </svg>
          </span>
          <span
            aria-hidden="true"
            class="emotion-5"
          >
            <svg
              fill="none"
              viewBox="0 0 14 13"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                clip-rule="evenodd"
                d="M7.02697 9.38889l-4.219 3.53831 1.40821-4.98276L0 4.76725l5.07966-.0364L7.02697 0l1.89337 4.73085L14 4.76725 9.83776 7.94444 11.2431 13 7.02697 9.38889z"
                fill="#003764"
                fill-rule="evenodd"
              />
            </svg>
          </span>
          <span
            aria-hidden="true"
            class="emotion-9"
          >
            <svg
              fill="none"
              viewBox="0 0 14 13"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                clip-rule="evenodd"
                d="M7.02697 9.38889l-4.219 3.53831 1.40821-4.98276L0 4.76725l5.07966-.0364L7.02697 0l1.89337 4.73085L14 4.76725 9.83776 7.94444 11.2431 13 7.02697 9.38889z"
                fill="#CCC"
                fill-rule="evenodd"
              />
            </svg>
          </span>
        </figure>
      </div>
      <div
        aria-hidden="true"
        class="emotion-10"
      >
        3  Reviews
      </div>
    </a>
  </div>
</DocumentFragment>
`;

exports[`<CertonaRatings /> Snapshots renders default state correctly OldNavy in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  font-family: 'Gotham','Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  color: #666;
  overflow: hidden;
  text-overflow: ellipsis;
  line-height: 1.2;
  white-space: normal;
  margin-top: 0.5rem;
  position: relative;
  text-align: center;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

.emotion-1 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  -webkit-box-flex-flow: wrap;
  -webkit-flex-flow: wrap;
  -ms-flex-flow: wrap;
  flex-flow: wrap;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: unset;
}

.emotion-2 {
  position: absolute;
  height: 1px;
  width: 1px;
  overflow: hidden;
  clip: rect(0 0 0 0);
}

.emotion-3 {
  position: relative;
  width: 93px;
  margin-right: 0.8125em;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-4 {
  width: 100%;
  margin: 0;
  padding: 0;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: space-around;
  -ms-flex-pack: space-around;
  -webkit-justify-content: space-around;
  justify-content: space-around;
  margin-block-start: 0;
  margin-block-end: 0;
  -webkit-margin-start: 0;
  margin-inline-start: 0;
  -webkit-margin-end: 0;
  margin-inline-end: 0;
}

.emotion-4 span {
  width: 100%;
  margin: 0;
}

.emotion-5 {
  display: inline-block;
  height: auto;
  width: auto;
  min-height: auto;
  min-width: auto;
}

.emotion-5 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-5 svg path {
  fill: #003764;
}

.emotion-5 svg rect {
  fill: #003764;
}

.emotion-9 {
  display: inline-block;
  height: auto;
  width: auto;
  min-height: auto;
  min-width: auto;
}

.emotion-9 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-9 svg path {
  fill: #CCC;
}

.emotion-9 svg rect {
  fill: #CCC;
}

.emotion-9 svg path {
  fill-opacity: 100%;
}

.emotion-10 {
  color: #666;
  font-family: 'Gotham','Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 0.8125em;
  margin-left: 0;
  margin-top: 0;
}

<div
    class="emotion-0"
  >
    <a
      class="emotion-1"
      data-testid="reviewRatings"
      tabindex="0"
    >
      <span
        class="emotion-2"
        data-testid="reviewRatingsAriaLabel"
      >
        with an average rating of 4 out of 5, 3 Reviews
      </span>
      <div
        class="emotion-3"
      >
        <div>
          <svg
            style="width: 0px; height: 0px; display: block;"
            viewBox="0 0 14 13"
            xmlns="http://www.w3.org/2000/svg"
          >
            <defs>
              <lineargradient
                id="fractional-gradient_14"
                x1="0"
                x2="100%"
                y1="0"
                y2="0"
              >
                <stop
                  offset="0%"
                  stop-color="#003764"
                  stop-opacity="100%"
                />
                <stop
                  offset="0%"
                  stop-color="#003764"
                  stop-opacity="100%"
                />
                <stop
                  offset="0%"
                  stop-color="#CCC"
                  stop-opacity="100%"
                />
                <stop
                  offset="100%"
                  stop-color="#CCC"
                  stop-opacity="100%"
                />
              </lineargradient>
            </defs>
          </svg>
        </div>
        <figure
          class="emotion-4"
        >
          <span
            aria-hidden="true"
            class="emotion-5"
          >
            <svg
              fill="none"
              viewBox="0 0 14 13"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                clip-rule="evenodd"
                d="M7.02697 9.38889l-4.219 3.53831 1.40821-4.98276L0 4.76725l5.07966-.0364L7.02697 0l1.89337 4.73085L14 4.76725 9.83776 7.94444 11.2431 13 7.02697 9.38889z"
                fill="#003764"
                fill-rule="evenodd"
              />
            </svg>
          </span>
          <span
            aria-hidden="true"
            class="emotion-5"
          >
            <svg
              fill="none"
              viewBox="0 0 14 13"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                clip-rule="evenodd"
                d="M7.02697 9.38889l-4.219 3.53831 1.40821-4.98276L0 4.76725l5.07966-.0364L7.02697 0l1.89337 4.73085L14 4.76725 9.83776 7.94444 11.2431 13 7.02697 9.38889z"
                fill="#003764"
                fill-rule="evenodd"
              />
            </svg>
          </span>
          <span
            aria-hidden="true"
            class="emotion-5"
          >
            <svg
              fill="none"
              viewBox="0 0 14 13"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                clip-rule="evenodd"
                d="M7.02697 9.38889l-4.219 3.53831 1.40821-4.98276L0 4.76725l5.07966-.0364L7.02697 0l1.89337 4.73085L14 4.76725 9.83776 7.94444 11.2431 13 7.02697 9.38889z"
                fill="#003764"
                fill-rule="evenodd"
              />
            </svg>
          </span>
          <span
            aria-hidden="true"
            class="emotion-5"
          >
            <svg
              fill="none"
              viewBox="0 0 14 13"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                clip-rule="evenodd"
                d="M7.02697 9.38889l-4.219 3.53831 1.40821-4.98276L0 4.76725l5.07966-.0364L7.02697 0l1.89337 4.73085L14 4.76725 9.83776 7.94444 11.2431 13 7.02697 9.38889z"
                fill="#003764"
                fill-rule="evenodd"
              />
            </svg>
          </span>
          <span
            aria-hidden="true"
            class="emotion-9"
          >
            <svg
              fill="none"
              viewBox="0 0 14 13"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                clip-rule="evenodd"
                d="M7.02697 9.38889l-4.219 3.53831 1.40821-4.98276L0 4.76725l5.07966-.0364L7.02697 0l1.89337 4.73085L14 4.76725 9.83776 7.94444 11.2431 13 7.02697 9.38889z"
                fill="#CCC"
                fill-rule="evenodd"
              />
            </svg>
          </span>
        </figure>
      </div>
      <div
        aria-hidden="true"
        class="emotion-10"
      >
        3  Reviews
      </div>
    </a>
  </div>
</DocumentFragment>
`;
