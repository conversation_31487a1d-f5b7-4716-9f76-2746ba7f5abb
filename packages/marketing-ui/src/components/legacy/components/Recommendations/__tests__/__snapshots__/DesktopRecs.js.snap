// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`<DesktopRecs /> should match snapshots Paginator snapshots 1`] = `
.emotion-0 {
  max-width: 1920px;
  -webkit-overflow-scrolling: touch;
  width: 100%;
  position: relative;
  margin: 0 auto;
}

@media only screen and (max-width: 1023px) {
  .emotion-0 nav[role='navigation'] {
    display: none;
  }
}

.emotion-0 .slick-slider .slick-arrow {
  height: auto;
  position: absolute;
  top: 10%;
  left: auto;
  right: 0;
}

.emotion-0 .slick-slider .slick-arrow:first-child {
  right: 5%;
}

.emotion-0 .slick-list {
  overflow-x: hidden;
  overflow-y: auto;
}

.emotion-1 {
  position: relative;
  float: none;
  margin: 0 auto;
  display: inline-block;
  width: 100%;
}

.emotion-2 {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  padding-bottom: 60px;
  font-size: 2rem;
  font-weight: 700;
  text-align: center;
  text-transform: uppercase;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  letter-spacing: 1.5px;
}

.emotion-3 {
  position: relative;
  float: none;
  width: 100%;
  margin: 0 auto;
}

.emotion-3 .slick-slider {
  position: relative;
  display: block;
  box-sizing: border-box;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  -webkit-touch-callout: none;
  touch-action: pan-y;
  -webkit-tap-highlight-color: transparent;
  scroll-behavior: smooth;
}

.emotion-3 .slick-slider .slick-track,
.emotion-3 .slick-slider .slick-list {
  -webkit-transform: translate3d(0, 0, 0);
  -moz-transform: translate3d(0, 0, 0);
  -ms-transform: translate3d(0, 0, 0);
  transform: translate3d(0, 0, 0);
}

.emotion-3 .slick-list {
  position: relative;
  display: block;
  overflow-x: hidden;
  overflow-y: hidden;
  margin: 0;
  padding: 0;
}

.emotion-3 .slick-list:focus {
  outline: none;
}

.emotion-3 .slick-list.dragging {
  cursor: pointer;
  cursor: hand;
}

.emotion-3 .slick-track {
  position: relative;
  top: 0;
  left: 0;
  display: block;
  margin-left: auto;
  margin-right: auto;
}

.emotion-3 .slick-track:before,
.emotion-3 .slick-track:after {
  display: table;
  content: "";
}

.emotion-3 .slick-track:after {
  clear: both;
}

.emotion-3 .slick-slide {
  display: none;
  float: left;
  height: 100%;
  min-height: 1px;
}

.emotion-3 .slick-slide img {
  display: block;
}

.emotion-3 .slick-slide.slick-loading img {
  display: none;
}

.emotion-3 .slick-slide.dragging img {
  pointer-events: none;
}

.emotion-3 [dir="rtl"] .slick-slide {
  float: right;
}

.emotion-3 .slick-initialized .slick-slide,
.emotion-3 .slick-vertical .slick-slide {
  display: block;
}

.emotion-3 .slick-vertical .slick-slide {
  border: 1px solid transparent;
  height: auto;
}

.emotion-3 .slick-loading .slick-track,
.emotion-3 .slick-loading .slick-slide {
  visibility: hidden;
}

.emotion-3 .slick-loading .slick-list {
  background: #fff url("./ajax-loader.gif") center center no-repeat;
}

.emotion-3 .slick-arrow.slick-hidden {
  display: none;
}

.emotion-3 .slick-prev,
.emotion-3 .slick-next {
  font-size: 0;
  line-height: 0;
  position: absolute;
  top: 0;
  display: block;
  padding: 0;
  cursor: pointer;
  color: transparent;
  border: none;
  outline: none;
  background: transparent;
}

.emotion-3 .slick-prev:hover,
.emotion-3 .slick-next:hover,
.emotion-3 .slick-prev:focus,
.emotion-3 .slick-next:focus {
  color: transparent;
  outline: none;
  background: transparent;
  opacity: 1;
}

.emotion-3 .slick-prev.slick-disabled,
.emotion-3 .slick-next.slick-disabled {
  opacity: 0.25;
}

.emotion-3 .slick-prev {
  left: -0px;
  -webkit-transform: translate(0, -50%) rotate(90deg);
  -moz-transform: translate(0, -50%) rotate(90deg);
  -ms-transform: translate(0, -50%) rotate(90deg);
  transform: translate(0, -50%) rotate(90deg);
}

.emotion-3 .slick-prev span {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-3 [dir="rtl"] .slick-prev {
  right: -0px;
  left: auto;
}

.emotion-3 .slick-next {
  right: -0px;
  -webkit-transform: translate(0, -50%) rotate(-90deg);
  -moz-transform: translate(0, -50%) rotate(-90deg);
  -ms-transform: translate(0, -50%) rotate(-90deg);
  transform: translate(0, -50%) rotate(-90deg);
}

.emotion-3 .slick-next span {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-3 [dir="rtl"] .slick-next {
  right: auto;
  left: -0px;
}

.emotion-3 .slick-dotted.slick-slider {
  margin-bottom: 30px;
}

.emotion-3 .slick-dots {
  position: absolute;
  bottom: -25px;
  display: block;
  width: 100%;
  padding: 0;
  margin: 0;
  list-style: none;
  text-align: center;
}

.emotion-3 .slick-dots li {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: 20px;
  height: 20px;
  margin: 0 5px;
  padding: 0;
  cursor: pointer;
}

.emotion-3 .slick-dots li.slick-active button:before {
  opacity: 0.75;
  color: black;
}

.emotion-3 .slick-dots li button {
  font-size: 0;
  line-height: 0;
  display: block;
  padding: 5px;
  cursor: pointer;
  color: transparent;
  border: 0;
  outline: none;
  background: transparent;
}

.emotion-3 .slick-dots li button:hover,
.emotion-3 .slick-dots li button:focus {
  outline: none;
}

.emotion-3 .slick-dots li button:hover:before,
.emotion-3 .slick-dots li button:focus:before,
.emotion-3 .slick-dots li button:hover:before,
.emotion-3 .slick-dots li button:focus:before {
  opacity: 1;
}

.emotion-3 .slick-dots li button:before {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 5px;
  height: 5px;
  content: "";
  opacity: 0.25;
  background-color: black;
  border-radius: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-3 .slick-prev,
.emotion-3 .slick-next {
  display: none;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  height: 100%;
  max-width: 50px;
  z-index: 1;
  top: 0;
  -webkit-transform: none;
  -moz-transform: none;
  -ms-transform: none;
  transform: none;
}

.emotion-3 .slick-prev.slick-disabled,
.emotion-3 .slick-next.slick-disabled {
  cursor: default;
  opacity: 0.125;
}

.emotion-3 .slick-prev {
  max-width: 20px;
}

.emotion-3 .slick-next {
  max-width: 20px;
  -webkit-transform: rotate(180deg);
  -moz-transform: rotate(180deg);
  -ms-transform: rotate(180deg);
  transform: rotate(180deg);
}

.emotion-5 {
  max-width: 192px;
  display: inline-block;
  width: auto;
  margin-bottom: 8px;
  text-align: left;
}

.emotion-6 {
  padding: 0 0.5rem;
}

.emotion-9 {
  position: relative;
}

.emotion-11 {
  margin-top: 0.5rem;
  color: #666;
  overflow: hidden;
  text-overflow: ellipsis;
  line-height: 1.2;
  white-space: normal;
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
}

.emotion-15 {
  margin-top: 0.5rem;
  color: #666;
  overflow: hidden;
  text-overflow: ellipsis;
  line-height: 1.2;
  white-space: normal;
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  margin-top: 0;
}

.emotion-42 {
  color: black;
  font-size: 20px;
  position: absolute;
  top: 10%;
  right: 2%;
  padding-top: 8.5px;
}

<div>
  <div
    class="emotion-0"
    id="mui-certona-recs-container"
  >
    <div
      class="emotion-1"
    >
      <h2
        class="emotion-2"
      >
        Shop New Arrivals
      </h2>
      <div
        class="emotion-3"
      >
        <div
          class="slick-slider slick-initialized"
          dir="ltr"
          style="position: unset;"
        >
          <button
            alt="previous"
            aria-label="previous"
            class="slick-arrow slick-prev slick-disabled emotion-4"
            disabled=""
          >
            <img
              alt="previous"
              draggable="false"
              src="arrow.png"
            />
          </button>
          <div
            class="slick-list"
          >
            <div
              class="slick-track"
              style="opacity: 1; transform: translate3d(0px, 0px, 0px);"
            >
              <div
                aria-hidden="false"
                class="slick-slide slick-active slick-current"
                data-index="0"
                style="outline: none; width: 0px;"
                tabindex="-1"
              >
                <div>
                  <div
                    class="emotion-5"
                    data-testid="recommended-product-card"
                  >
                    <div
                      class="emotion-6"
                    >
                      <a
                        class="emotion-4"
                        href="/browse/product.do?cid=1123863&pcid=372436&vid=1&pid=372436002&mlink=5001,1,sitewide__0&clink=1"
                        target="_self"
                      >
                        <div
                          class="emotion-4"
                        >
                          <div
                            class="emotion-9"
                          >
                            <img
                              alt="Lightweight Run Pant"
                              aria-hidden="true"
                              class="emotion-10"
                              src="https://www4.assets-gap.com/webcontent/0016/273/931/cn16273931.jpg"
                            />
                          </div>
                          <div
                            class="emotion-11"
                            style="text-align: left; padding-right: 0rem;"
                          >
                            <p
                              class="emotion-4"
                            >
                              Lightweight Run Pant
                            </p>
                          </div>
                          <div
                            class="emotion-11"
                            data-testid="price-block"
                          >
                            <div
                              class="emotion-4"
                              style="text-align: left;"
                            >
                              $NaN
                            </div>
                          </div>
                          <div
                            class="emotion-15"
                          >
                            <p
                              class="emotion-16"
                              style="font-weight: bold; font-size: .9rem; text-align: left;"
                            >
                              More Colors Available
                            </p>
                          </div>
                        </div>
                      </a>
                    </div>
                  </div>
                </div>
              </div>
              <div
                aria-hidden="false"
                class="slick-slide slick-active"
                data-index="1"
                style="outline: none; width: 0px;"
                tabindex="-1"
              >
                <div>
                  <div
                    class="emotion-5"
                    data-testid="recommended-product-card"
                  >
                    <div
                      class="emotion-6"
                    >
                      <a
                        class="emotion-4"
                        href="/browse/product.do?cid=1123863&pcid=372436&vid=1&pid=372436002&mlink=5001,1,sitewide__1&clink=1"
                        target="_self"
                      >
                        <div
                          class="emotion-4"
                        >
                          <div
                            class="emotion-9"
                          >
                            <img
                              alt="Lightweight Run Pant"
                              aria-hidden="true"
                              class="emotion-10"
                              src="https://www4.assets-gap.com/webcontent/0016/273/931/cn16273931.jpg"
                            />
                          </div>
                          <div
                            class="emotion-11"
                            style="text-align: left; padding-right: 0rem;"
                          >
                            <p
                              class="emotion-4"
                            >
                              Lightweight Run Pant
                            </p>
                          </div>
                          <div
                            class="emotion-11"
                            data-testid="price-block"
                          >
                            <div
                              class="emotion-4"
                              style="text-align: left;"
                            >
                              $NaN
                            </div>
                          </div>
                          <div
                            class="emotion-15"
                          >
                            <p
                              class="emotion-16"
                              style="font-weight: bold; font-size: .9rem; text-align: left;"
                            >
                              More Colors Available
                            </p>
                          </div>
                        </div>
                      </a>
                    </div>
                  </div>
                </div>
              </div>
              <div
                aria-hidden="false"
                class="slick-slide slick-active"
                data-index="2"
                style="outline: none; width: 0px;"
                tabindex="-1"
              >
                <div>
                  <div
                    class="emotion-5"
                    data-testid="recommended-product-card"
                  >
                    <div
                      class="emotion-6"
                    >
                      <a
                        class="emotion-4"
                        href="/browse/product.do?cid=1123863&pcid=372436&vid=1&pid=372436002&mlink=5001,1,sitewide__2&clink=1"
                        target="_self"
                      >
                        <div
                          class="emotion-4"
                        >
                          <div
                            class="emotion-9"
                          >
                            <img
                              alt="Lightweight Run Pant"
                              aria-hidden="true"
                              class="emotion-10"
                              src="https://www4.assets-gap.com/webcontent/0016/273/931/cn16273931.jpg"
                            />
                          </div>
                          <div
                            class="emotion-11"
                            style="text-align: left; padding-right: 0rem;"
                          >
                            <p
                              class="emotion-4"
                            >
                              Lightweight Run Pant
                            </p>
                          </div>
                          <div
                            class="emotion-11"
                            data-testid="price-block"
                          >
                            <div
                              class="emotion-4"
                              style="text-align: left;"
                            >
                              $NaN
                            </div>
                          </div>
                          <div
                            class="emotion-15"
                          >
                            <p
                              class="emotion-16"
                              style="font-weight: bold; font-size: .9rem; text-align: left;"
                            >
                              More Colors Available
                            </p>
                          </div>
                        </div>
                      </a>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <button
            alt="next"
            aria-label="next"
            class="slick-arrow slick-next slick-disabled emotion-4"
            disabled=""
          >
            <img
              alt="next"
              draggable="false"
              src="arrow.png"
            />
          </button>
        </div>
      </div>
      <nav
        aria-label="page number 1/1"
        class="emotion-42"
        role="navigation"
      >
        1/1
      </nav>
    </div>
  </div>
</div>
`;

exports[`<DesktopRecs /> should match snapshots container max-width is set to 1440px using the style property 1`] = `
.emotion-0 {
  max-width: 1440px;
  -webkit-overflow-scrolling: touch;
  width: 100%;
  position: relative;
  margin: 0 auto;
}

.emotion-0 .slick-list {
  overflow-x: hidden;
  overflow-y: auto;
}

.emotion-1 {
  position: relative;
  float: none;
  margin: 0 auto;
  display: inline-block;
  width: 100%;
}

.emotion-2 {
  position: relative;
  float: none;
  width: 100%;
  margin: 0 auto;
}

.emotion-2 .slick-slider {
  position: relative;
  display: block;
  box-sizing: border-box;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  -webkit-touch-callout: none;
  touch-action: pan-y;
  -webkit-tap-highlight-color: transparent;
  scroll-behavior: smooth;
}

.emotion-2 .slick-slider .slick-track,
.emotion-2 .slick-slider .slick-list {
  -webkit-transform: translate3d(0, 0, 0);
  -moz-transform: translate3d(0, 0, 0);
  -ms-transform: translate3d(0, 0, 0);
  transform: translate3d(0, 0, 0);
}

.emotion-2 .slick-list {
  position: relative;
  display: block;
  overflow-x: hidden;
  overflow-y: hidden;
  margin: 0;
  padding: 0;
}

.emotion-2 .slick-list:focus {
  outline: none;
}

.emotion-2 .slick-list.dragging {
  cursor: pointer;
  cursor: hand;
}

.emotion-2 .slick-track {
  position: relative;
  top: 0;
  left: 0;
  display: block;
  margin-left: auto;
  margin-right: auto;
}

.emotion-2 .slick-track:before,
.emotion-2 .slick-track:after {
  display: table;
  content: "";
}

.emotion-2 .slick-track:after {
  clear: both;
}

.emotion-2 .slick-slide {
  display: none;
  float: left;
  height: 100%;
  min-height: 1px;
}

.emotion-2 .slick-slide img {
  display: block;
}

.emotion-2 .slick-slide.slick-loading img {
  display: none;
}

.emotion-2 .slick-slide.dragging img {
  pointer-events: none;
}

.emotion-2 [dir="rtl"] .slick-slide {
  float: right;
}

.emotion-2 .slick-initialized .slick-slide,
.emotion-2 .slick-vertical .slick-slide {
  display: block;
}

.emotion-2 .slick-vertical .slick-slide {
  border: 1px solid transparent;
  height: auto;
}

.emotion-2 .slick-loading .slick-track,
.emotion-2 .slick-loading .slick-slide {
  visibility: hidden;
}

.emotion-2 .slick-loading .slick-list {
  background: #fff url("./ajax-loader.gif") center center no-repeat;
}

.emotion-2 .slick-arrow.slick-hidden {
  display: none;
}

.emotion-2 .slick-prev,
.emotion-2 .slick-next {
  font-size: 0;
  line-height: 0;
  position: absolute;
  top: 0;
  display: block;
  padding: 0;
  cursor: pointer;
  color: transparent;
  border: none;
  outline: none;
  background: transparent;
}

.emotion-2 .slick-prev:hover,
.emotion-2 .slick-next:hover,
.emotion-2 .slick-prev:focus,
.emotion-2 .slick-next:focus {
  color: transparent;
  outline: none;
  background: transparent;
  opacity: 1;
}

.emotion-2 .slick-prev.slick-disabled,
.emotion-2 .slick-next.slick-disabled {
  opacity: 0.25;
}

.emotion-2 .slick-prev {
  left: -0px;
  -webkit-transform: translate(0, -50%) rotate(90deg);
  -moz-transform: translate(0, -50%) rotate(90deg);
  -ms-transform: translate(0, -50%) rotate(90deg);
  transform: translate(0, -50%) rotate(90deg);
}

.emotion-2 .slick-prev span {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-2 [dir="rtl"] .slick-prev {
  right: -0px;
  left: auto;
}

.emotion-2 .slick-next {
  right: -0px;
  -webkit-transform: translate(0, -50%) rotate(-90deg);
  -moz-transform: translate(0, -50%) rotate(-90deg);
  -ms-transform: translate(0, -50%) rotate(-90deg);
  transform: translate(0, -50%) rotate(-90deg);
}

.emotion-2 .slick-next span {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-2 [dir="rtl"] .slick-next {
  right: auto;
  left: -0px;
}

.emotion-2 .slick-dotted.slick-slider {
  margin-bottom: 30px;
}

.emotion-2 .slick-dots {
  position: absolute;
  bottom: -25px;
  display: block;
  width: 100%;
  padding: 0;
  margin: 0;
  list-style: none;
  text-align: center;
}

.emotion-2 .slick-dots li {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: 20px;
  height: 20px;
  margin: 0 5px;
  padding: 0;
  cursor: pointer;
}

.emotion-2 .slick-dots li.slick-active button:before {
  opacity: 0.75;
  color: black;
}

.emotion-2 .slick-dots li button {
  font-size: 0;
  line-height: 0;
  display: block;
  padding: 5px;
  cursor: pointer;
  color: transparent;
  border: 0;
  outline: none;
  background: transparent;
}

.emotion-2 .slick-dots li button:hover,
.emotion-2 .slick-dots li button:focus {
  outline: none;
}

.emotion-2 .slick-dots li button:hover:before,
.emotion-2 .slick-dots li button:focus:before,
.emotion-2 .slick-dots li button:hover:before,
.emotion-2 .slick-dots li button:focus:before {
  opacity: 1;
}

.emotion-2 .slick-dots li button:before {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 5px;
  height: 5px;
  content: "";
  opacity: 0.25;
  background-color: black;
  border-radius: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-2 .slick-prev,
.emotion-2 .slick-next {
  display: none;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  height: 100%;
  max-width: 50px;
  z-index: 1;
  top: 0;
  -webkit-transform: none;
  -moz-transform: none;
  -ms-transform: none;
  transform: none;
}

.emotion-2 .slick-prev.slick-disabled,
.emotion-2 .slick-next.slick-disabled {
  cursor: default;
  opacity: 0.125;
}

.emotion-2 .slick-prev {
  max-width: 20px;
}

.emotion-2 .slick-next {
  max-width: 20px;
  -webkit-transform: rotate(180deg);
  -moz-transform: rotate(180deg);
  -ms-transform: rotate(180deg);
  transform: rotate(180deg);
}

.emotion-4 {
  max-width: 192px;
  display: inline-block;
  display: block;
  -webkit-box-flex: 5;
  -webkit-flex-grow: 5;
  -ms-flex-positive: 5;
  flex-grow: 5;
  margin: auto;
  width: 85%;
}

.emotion-5 {
  padding: 0 0.5rem;
}

.emotion-8 {
  position: relative;
}

.emotion-10 {
  margin-top: 0.5rem;
  color: #666;
  overflow: hidden;
  text-overflow: ellipsis;
  line-height: 1.2;
  white-space: normal;
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
}

<div>
  <div
    class="emotion-0"
    id="mui-certona-recs-container"
  >
    <div
      class="emotion-1"
    >
      <div
        class="emotion-2"
      >
        <div
          class="slick-slider slick-initialized"
          dir="ltr"
        >
          <button
            alt="previous"
            aria-label="previous"
            class="slick-arrow slick-prev slick-disabled emotion-3"
            disabled=""
          >
            <img
              alt="previous"
              draggable="false"
              src="img-imp.png"
            />
          </button>
          <div
            class="slick-list"
          >
            <div
              class="slick-track"
              style="opacity: 1; transform: translate3d(0px, 0px, 0px);"
            >
              <div
                aria-hidden="false"
                class="slick-slide slick-active slick-current"
                data-index="0"
                style="outline: none; width: 0px;"
                tabindex="-1"
              >
                <div>
                  <div
                    class="emotion-4"
                    data-testid="recommended-product-card"
                  >
                    <div
                      class="emotion-5"
                    >
                      <a
                        class="emotion-3"
                        href="/browse/product.do?cid=1123863&pcid=372436&vid=1&pid=372436002&mlink=5001,1,sitewide_onhome1_rr_0&clink=1"
                        target="_self"
                      >
                        <div
                          class="emotion-3"
                        >
                          <div
                            class="emotion-8"
                          >
                            <img
                              alt="Lightweight Run Pant"
                              aria-hidden="true"
                              class="emotion-9"
                              src="https://www4.assets-gap.com/webcontent/0016/273/931/cn16273931.jpg"
                            />
                          </div>
                          <div
                            class="emotion-10"
                          >
                            <p
                              class="emotion-3"
                            >
                              Lightweight Run Pant
                            </p>
                          </div>
                          <div
                            class="emotion-10"
                            data-testid="price-block"
                          >
                            <div
                              class="emotion-3"
                            >
                              $NaN
                            </div>
                          </div>
                        </div>
                      </a>
                    </div>
                  </div>
                </div>
              </div>
              <div
                aria-hidden="false"
                class="slick-slide slick-active"
                data-index="1"
                style="outline: none; width: 0px;"
                tabindex="-1"
              >
                <div>
                  <div
                    class="emotion-4"
                    data-testid="recommended-product-card"
                  >
                    <div
                      class="emotion-5"
                    >
                      <a
                        class="emotion-3"
                        href="/browse/product.do?cid=1123863&pcid=372436&vid=1&pid=372436002&mlink=5001,1,sitewide_onhome1_rr_1&clink=1"
                        target="_self"
                      >
                        <div
                          class="emotion-3"
                        >
                          <div
                            class="emotion-8"
                          >
                            <img
                              alt="Lightweight Run Pant"
                              aria-hidden="true"
                              class="emotion-9"
                              src="https://www4.assets-gap.com/webcontent/0016/273/931/cn16273931.jpg"
                            />
                          </div>
                          <div
                            class="emotion-10"
                          >
                            <p
                              class="emotion-3"
                            >
                              Lightweight Run Pant
                            </p>
                          </div>
                          <div
                            class="emotion-10"
                            data-testid="price-block"
                          >
                            <div
                              class="emotion-3"
                            >
                              $NaN
                            </div>
                          </div>
                        </div>
                      </a>
                    </div>
                  </div>
                </div>
              </div>
              <div
                aria-hidden="false"
                class="slick-slide slick-active"
                data-index="2"
                style="outline: none; width: 0px;"
                tabindex="-1"
              >
                <div>
                  <div
                    class="emotion-4"
                    data-testid="recommended-product-card"
                  >
                    <div
                      class="emotion-5"
                    >
                      <a
                        class="emotion-3"
                        href="/browse/product.do?cid=1123863&pcid=372436&vid=1&pid=372436002&mlink=5001,1,sitewide_onhome1_rr_2&clink=1"
                        target="_self"
                      >
                        <div
                          class="emotion-3"
                        >
                          <div
                            class="emotion-8"
                          >
                            <img
                              alt="Lightweight Run Pant"
                              aria-hidden="true"
                              class="emotion-9"
                              src="https://www4.assets-gap.com/webcontent/0016/273/931/cn16273931.jpg"
                            />
                          </div>
                          <div
                            class="emotion-10"
                          >
                            <p
                              class="emotion-3"
                            >
                              Lightweight Run Pant
                            </p>
                          </div>
                          <div
                            class="emotion-10"
                            data-testid="price-block"
                          >
                            <div
                              class="emotion-3"
                            >
                              $NaN
                            </div>
                          </div>
                        </div>
                      </a>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <button
            alt="next"
            aria-label="next"
            class="slick-arrow slick-next slick-disabled emotion-3"
            disabled=""
          >
            <img
              alt="next"
              draggable="false"
              src="img-imp-l.png"
            />
          </button>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`<DesktopRecs /> should match snapshots container max-width should default to 1920px 1`] = `
.emotion-0 {
  max-width: 1920px;
  -webkit-overflow-scrolling: touch;
  width: 100%;
  position: relative;
  margin: 0 auto;
}

.emotion-0 .slick-list {
  overflow-x: hidden;
  overflow-y: auto;
}

.emotion-1 {
  position: relative;
  float: none;
  margin: 0 auto;
  display: inline-block;
  width: 100%;
}

.emotion-2 {
  position: relative;
  float: none;
  width: 100%;
  margin: 0 auto;
}

.emotion-2 .slick-slider {
  position: relative;
  display: block;
  box-sizing: border-box;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  -webkit-touch-callout: none;
  touch-action: pan-y;
  -webkit-tap-highlight-color: transparent;
  scroll-behavior: smooth;
}

.emotion-2 .slick-slider .slick-track,
.emotion-2 .slick-slider .slick-list {
  -webkit-transform: translate3d(0, 0, 0);
  -moz-transform: translate3d(0, 0, 0);
  -ms-transform: translate3d(0, 0, 0);
  transform: translate3d(0, 0, 0);
}

.emotion-2 .slick-list {
  position: relative;
  display: block;
  overflow-x: hidden;
  overflow-y: hidden;
  margin: 0;
  padding: 0;
}

.emotion-2 .slick-list:focus {
  outline: none;
}

.emotion-2 .slick-list.dragging {
  cursor: pointer;
  cursor: hand;
}

.emotion-2 .slick-track {
  position: relative;
  top: 0;
  left: 0;
  display: block;
  margin-left: auto;
  margin-right: auto;
}

.emotion-2 .slick-track:before,
.emotion-2 .slick-track:after {
  display: table;
  content: "";
}

.emotion-2 .slick-track:after {
  clear: both;
}

.emotion-2 .slick-slide {
  display: none;
  float: left;
  height: 100%;
  min-height: 1px;
}

.emotion-2 .slick-slide img {
  display: block;
}

.emotion-2 .slick-slide.slick-loading img {
  display: none;
}

.emotion-2 .slick-slide.dragging img {
  pointer-events: none;
}

.emotion-2 [dir="rtl"] .slick-slide {
  float: right;
}

.emotion-2 .slick-initialized .slick-slide,
.emotion-2 .slick-vertical .slick-slide {
  display: block;
}

.emotion-2 .slick-vertical .slick-slide {
  border: 1px solid transparent;
  height: auto;
}

.emotion-2 .slick-loading .slick-track,
.emotion-2 .slick-loading .slick-slide {
  visibility: hidden;
}

.emotion-2 .slick-loading .slick-list {
  background: #fff url("./ajax-loader.gif") center center no-repeat;
}

.emotion-2 .slick-arrow.slick-hidden {
  display: none;
}

.emotion-2 .slick-prev,
.emotion-2 .slick-next {
  font-size: 0;
  line-height: 0;
  position: absolute;
  top: 0;
  display: block;
  padding: 0;
  cursor: pointer;
  color: transparent;
  border: none;
  outline: none;
  background: transparent;
}

.emotion-2 .slick-prev:hover,
.emotion-2 .slick-next:hover,
.emotion-2 .slick-prev:focus,
.emotion-2 .slick-next:focus {
  color: transparent;
  outline: none;
  background: transparent;
  opacity: 1;
}

.emotion-2 .slick-prev.slick-disabled,
.emotion-2 .slick-next.slick-disabled {
  opacity: 0.25;
}

.emotion-2 .slick-prev {
  left: -0px;
  -webkit-transform: translate(0, -50%) rotate(90deg);
  -moz-transform: translate(0, -50%) rotate(90deg);
  -ms-transform: translate(0, -50%) rotate(90deg);
  transform: translate(0, -50%) rotate(90deg);
}

.emotion-2 .slick-prev span {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-2 [dir="rtl"] .slick-prev {
  right: -0px;
  left: auto;
}

.emotion-2 .slick-next {
  right: -0px;
  -webkit-transform: translate(0, -50%) rotate(-90deg);
  -moz-transform: translate(0, -50%) rotate(-90deg);
  -ms-transform: translate(0, -50%) rotate(-90deg);
  transform: translate(0, -50%) rotate(-90deg);
}

.emotion-2 .slick-next span {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-2 [dir="rtl"] .slick-next {
  right: auto;
  left: -0px;
}

.emotion-2 .slick-dotted.slick-slider {
  margin-bottom: 30px;
}

.emotion-2 .slick-dots {
  position: absolute;
  bottom: -25px;
  display: block;
  width: 100%;
  padding: 0;
  margin: 0;
  list-style: none;
  text-align: center;
}

.emotion-2 .slick-dots li {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: 20px;
  height: 20px;
  margin: 0 5px;
  padding: 0;
  cursor: pointer;
}

.emotion-2 .slick-dots li.slick-active button:before {
  opacity: 0.75;
  color: black;
}

.emotion-2 .slick-dots li button {
  font-size: 0;
  line-height: 0;
  display: block;
  padding: 5px;
  cursor: pointer;
  color: transparent;
  border: 0;
  outline: none;
  background: transparent;
}

.emotion-2 .slick-dots li button:hover,
.emotion-2 .slick-dots li button:focus {
  outline: none;
}

.emotion-2 .slick-dots li button:hover:before,
.emotion-2 .slick-dots li button:focus:before,
.emotion-2 .slick-dots li button:hover:before,
.emotion-2 .slick-dots li button:focus:before {
  opacity: 1;
}

.emotion-2 .slick-dots li button:before {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 5px;
  height: 5px;
  content: "";
  opacity: 0.25;
  background-color: black;
  border-radius: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-2 .slick-prev,
.emotion-2 .slick-next {
  display: none;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  height: 100%;
  max-width: 50px;
  z-index: 1;
  top: 0;
  -webkit-transform: none;
  -moz-transform: none;
  -ms-transform: none;
  transform: none;
}

.emotion-2 .slick-prev.slick-disabled,
.emotion-2 .slick-next.slick-disabled {
  cursor: default;
  opacity: 0.125;
}

.emotion-2 .slick-prev {
  max-width: 20px;
}

.emotion-2 .slick-next {
  max-width: 20px;
  -webkit-transform: rotate(180deg);
  -moz-transform: rotate(180deg);
  -ms-transform: rotate(180deg);
  transform: rotate(180deg);
}

.emotion-4 {
  display: inline-block;
  height: 2rem;
  width: 2rem;
  min-height: 2rem;
  min-width: 2rem;
  -webkit-transform: rotate(0.25turn);
  -moz-transform: rotate(0.25turn);
  -ms-transform: rotate(0.25turn);
  transform: rotate(0.25turn);
}

.emotion-4 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-6 {
  display: inline-block;
  height: 2rem;
  width: 2rem;
  min-height: 2rem;
  min-width: 2rem;
  -webkit-transform: rotate(0.75turn);
  -moz-transform: rotate(0.75turn);
  -ms-transform: rotate(0.75turn);
  transform: rotate(0.75turn);
}

.emotion-6 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

<div>
  <div
    class="emotion-0"
    id="mui-certona-recs-container"
  >
    <div
      class="emotion-1"
    >
      <div
        class="emotion-2"
      >
        <div
          class="slick-slider slick-initialized"
          dir="ltr"
        >
          <button
            class="slick-arrow slick-prev slick-disabled emotion-3"
            disabled=""
          >
            <span
              aria-hidden="true"
              class="emotion-4"
            >
              <svg
                viewBox="0 0 18 7.742"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M0,.839,6.44,9,0,17.162.818,18,7.743,9.225,7.522,9l.22-.225L.818,0Z"
                  fill="#2B2B2B"
                  transform="translate(18) rotate(90)"
                />
              </svg>
            </span>
          </button>
          <div
            class="slick-list"
          >
            <div
              class="slick-track"
              style="opacity: 1; transform: translate3d(0px, 0px, 0px);"
            />
          </div>
          <button
            class="slick-arrow slick-next slick-disabled emotion-3"
          >
            <span
              aria-hidden="true"
              class="emotion-6"
            >
              <svg
                viewBox="0 0 18 7.742"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M0,.839,6.44,9,0,17.162.818,18,7.743,9.225,7.522,9l.22-.225L.818,0Z"
                  fill="#2B2B2B"
                  transform="translate(18) rotate(90)"
                />
              </svg>
            </span>
          </button>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`<DesktopRecs /> should match snapshots container should be set to fullWidth and override style property with max-width to 'none' 1`] = `
.emotion-0 {
  max-width: none;
  -webkit-overflow-scrolling: touch;
  width: 100%;
  position: relative;
  margin: 0 auto;
}

.emotion-0 .slick-list {
  overflow-x: hidden;
  overflow-y: auto;
}

.emotion-1 {
  position: relative;
  float: none;
  margin: 0 auto;
  display: inline-block;
  width: 100%;
}

.emotion-2 {
  position: relative;
  float: none;
  width: 100%;
  margin: 0 auto;
}

.emotion-2 .slick-slider {
  position: relative;
  display: block;
  box-sizing: border-box;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  -webkit-touch-callout: none;
  touch-action: pan-y;
  -webkit-tap-highlight-color: transparent;
  scroll-behavior: smooth;
}

.emotion-2 .slick-slider .slick-track,
.emotion-2 .slick-slider .slick-list {
  -webkit-transform: translate3d(0, 0, 0);
  -moz-transform: translate3d(0, 0, 0);
  -ms-transform: translate3d(0, 0, 0);
  transform: translate3d(0, 0, 0);
}

.emotion-2 .slick-list {
  position: relative;
  display: block;
  overflow-x: hidden;
  overflow-y: hidden;
  margin: 0;
  padding: 0;
}

.emotion-2 .slick-list:focus {
  outline: none;
}

.emotion-2 .slick-list.dragging {
  cursor: pointer;
  cursor: hand;
}

.emotion-2 .slick-track {
  position: relative;
  top: 0;
  left: 0;
  display: block;
  margin-left: auto;
  margin-right: auto;
}

.emotion-2 .slick-track:before,
.emotion-2 .slick-track:after {
  display: table;
  content: "";
}

.emotion-2 .slick-track:after {
  clear: both;
}

.emotion-2 .slick-slide {
  display: none;
  float: left;
  height: 100%;
  min-height: 1px;
}

.emotion-2 .slick-slide img {
  display: block;
}

.emotion-2 .slick-slide.slick-loading img {
  display: none;
}

.emotion-2 .slick-slide.dragging img {
  pointer-events: none;
}

.emotion-2 [dir="rtl"] .slick-slide {
  float: right;
}

.emotion-2 .slick-initialized .slick-slide,
.emotion-2 .slick-vertical .slick-slide {
  display: block;
}

.emotion-2 .slick-vertical .slick-slide {
  border: 1px solid transparent;
  height: auto;
}

.emotion-2 .slick-loading .slick-track,
.emotion-2 .slick-loading .slick-slide {
  visibility: hidden;
}

.emotion-2 .slick-loading .slick-list {
  background: #fff url("./ajax-loader.gif") center center no-repeat;
}

.emotion-2 .slick-arrow.slick-hidden {
  display: none;
}

.emotion-2 .slick-prev,
.emotion-2 .slick-next {
  font-size: 0;
  line-height: 0;
  position: absolute;
  top: 0;
  display: block;
  padding: 0;
  cursor: pointer;
  color: transparent;
  border: none;
  outline: none;
  background: transparent;
}

.emotion-2 .slick-prev:hover,
.emotion-2 .slick-next:hover,
.emotion-2 .slick-prev:focus,
.emotion-2 .slick-next:focus {
  color: transparent;
  outline: none;
  background: transparent;
  opacity: 1;
}

.emotion-2 .slick-prev.slick-disabled,
.emotion-2 .slick-next.slick-disabled {
  opacity: 0.25;
}

.emotion-2 .slick-prev {
  left: -0px;
  -webkit-transform: translate(0, -50%) rotate(90deg);
  -moz-transform: translate(0, -50%) rotate(90deg);
  -ms-transform: translate(0, -50%) rotate(90deg);
  transform: translate(0, -50%) rotate(90deg);
}

.emotion-2 .slick-prev span {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-2 [dir="rtl"] .slick-prev {
  right: -0px;
  left: auto;
}

.emotion-2 .slick-next {
  right: -0px;
  -webkit-transform: translate(0, -50%) rotate(-90deg);
  -moz-transform: translate(0, -50%) rotate(-90deg);
  -ms-transform: translate(0, -50%) rotate(-90deg);
  transform: translate(0, -50%) rotate(-90deg);
}

.emotion-2 .slick-next span {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-2 [dir="rtl"] .slick-next {
  right: auto;
  left: -0px;
}

.emotion-2 .slick-dotted.slick-slider {
  margin-bottom: 30px;
}

.emotion-2 .slick-dots {
  position: absolute;
  bottom: -25px;
  display: block;
  width: 100%;
  padding: 0;
  margin: 0;
  list-style: none;
  text-align: center;
}

.emotion-2 .slick-dots li {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: 20px;
  height: 20px;
  margin: 0 5px;
  padding: 0;
  cursor: pointer;
}

.emotion-2 .slick-dots li.slick-active button:before {
  opacity: 0.75;
  color: black;
}

.emotion-2 .slick-dots li button {
  font-size: 0;
  line-height: 0;
  display: block;
  padding: 5px;
  cursor: pointer;
  color: transparent;
  border: 0;
  outline: none;
  background: transparent;
}

.emotion-2 .slick-dots li button:hover,
.emotion-2 .slick-dots li button:focus {
  outline: none;
}

.emotion-2 .slick-dots li button:hover:before,
.emotion-2 .slick-dots li button:focus:before,
.emotion-2 .slick-dots li button:hover:before,
.emotion-2 .slick-dots li button:focus:before {
  opacity: 1;
}

.emotion-2 .slick-dots li button:before {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 5px;
  height: 5px;
  content: "";
  opacity: 0.25;
  background-color: black;
  border-radius: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-2 .slick-prev,
.emotion-2 .slick-next {
  display: none;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  height: 100%;
  max-width: 50px;
  z-index: 1;
  top: 0;
  -webkit-transform: none;
  -moz-transform: none;
  -ms-transform: none;
  transform: none;
}

.emotion-2 .slick-prev.slick-disabled,
.emotion-2 .slick-next.slick-disabled {
  cursor: default;
  opacity: 0.125;
}

.emotion-2 .slick-prev {
  max-width: 20px;
}

.emotion-2 .slick-next {
  max-width: 20px;
  -webkit-transform: rotate(180deg);
  -moz-transform: rotate(180deg);
  -ms-transform: rotate(180deg);
  transform: rotate(180deg);
}

.emotion-4 {
  max-width: 192px;
  display: inline-block;
  display: block;
  -webkit-box-flex: 5;
  -webkit-flex-grow: 5;
  -ms-flex-positive: 5;
  flex-grow: 5;
  margin: auto;
  width: 85%;
}

.emotion-5 {
  padding: 0 0.5rem;
}

.emotion-8 {
  position: relative;
}

.emotion-10 {
  margin-top: 0.5rem;
  color: #666;
  overflow: hidden;
  text-overflow: ellipsis;
  line-height: 1.2;
  white-space: normal;
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
}

<div>
  <div
    class="emotion-0"
    id="mui-certona-recs-container"
  >
    <div
      class="emotion-1"
    >
      <div
        class="emotion-2"
      >
        <div
          class="slick-slider slick-initialized"
          dir="ltr"
        >
          <button
            alt="previous"
            aria-label="previous"
            class="slick-arrow slick-prev slick-disabled emotion-3"
            disabled=""
          >
            <img
              alt="previous"
              draggable="false"
              src="img-imp.png"
            />
          </button>
          <div
            class="slick-list"
          >
            <div
              class="slick-track"
              style="opacity: 1; transform: translate3d(0px, 0px, 0px);"
            >
              <div
                aria-hidden="false"
                class="slick-slide slick-active slick-current"
                data-index="0"
                style="outline: none; width: 0px;"
                tabindex="-1"
              >
                <div>
                  <div
                    class="emotion-4"
                    data-testid="recommended-product-card"
                  >
                    <div
                      class="emotion-5"
                    >
                      <a
                        class="emotion-3"
                        href="/browse/product.do?cid=1123863&pcid=372436&vid=1&pid=372436002&mlink=5001,1,sitewide_onhome1_rr_0&clink=1"
                        target="_self"
                      >
                        <div
                          class="emotion-3"
                        >
                          <div
                            class="emotion-8"
                          >
                            <img
                              alt="Lightweight Run Pant"
                              aria-hidden="true"
                              class="emotion-9"
                              src="https://www4.assets-gap.com/webcontent/0016/273/931/cn16273931.jpg"
                            />
                          </div>
                          <div
                            class="emotion-10"
                          >
                            <p
                              class="emotion-3"
                            >
                              Lightweight Run Pant
                            </p>
                          </div>
                          <div
                            class="emotion-10"
                            data-testid="price-block"
                          >
                            <div
                              class="emotion-3"
                            >
                              $NaN
                            </div>
                          </div>
                        </div>
                      </a>
                    </div>
                  </div>
                </div>
              </div>
              <div
                aria-hidden="false"
                class="slick-slide slick-active"
                data-index="1"
                style="outline: none; width: 0px;"
                tabindex="-1"
              >
                <div>
                  <div
                    class="emotion-4"
                    data-testid="recommended-product-card"
                  >
                    <div
                      class="emotion-5"
                    >
                      <a
                        class="emotion-3"
                        href="/browse/product.do?cid=1123863&pcid=372436&vid=1&pid=372436002&mlink=5001,1,sitewide_onhome1_rr_1&clink=1"
                        target="_self"
                      >
                        <div
                          class="emotion-3"
                        >
                          <div
                            class="emotion-8"
                          >
                            <img
                              alt="Lightweight Run Pant"
                              aria-hidden="true"
                              class="emotion-9"
                              src="https://www4.assets-gap.com/webcontent/0016/273/931/cn16273931.jpg"
                            />
                          </div>
                          <div
                            class="emotion-10"
                          >
                            <p
                              class="emotion-3"
                            >
                              Lightweight Run Pant
                            </p>
                          </div>
                          <div
                            class="emotion-10"
                            data-testid="price-block"
                          >
                            <div
                              class="emotion-3"
                            >
                              $NaN
                            </div>
                          </div>
                        </div>
                      </a>
                    </div>
                  </div>
                </div>
              </div>
              <div
                aria-hidden="false"
                class="slick-slide slick-active"
                data-index="2"
                style="outline: none; width: 0px;"
                tabindex="-1"
              >
                <div>
                  <div
                    class="emotion-4"
                    data-testid="recommended-product-card"
                  >
                    <div
                      class="emotion-5"
                    >
                      <a
                        class="emotion-3"
                        href="/browse/product.do?cid=1123863&pcid=372436&vid=1&pid=372436002&mlink=5001,1,sitewide_onhome1_rr_2&clink=1"
                        target="_self"
                      >
                        <div
                          class="emotion-3"
                        >
                          <div
                            class="emotion-8"
                          >
                            <img
                              alt="Lightweight Run Pant"
                              aria-hidden="true"
                              class="emotion-9"
                              src="https://www4.assets-gap.com/webcontent/0016/273/931/cn16273931.jpg"
                            />
                          </div>
                          <div
                            class="emotion-10"
                          >
                            <p
                              class="emotion-3"
                            >
                              Lightweight Run Pant
                            </p>
                          </div>
                          <div
                            class="emotion-10"
                            data-testid="price-block"
                          >
                            <div
                              class="emotion-3"
                            >
                              $NaN
                            </div>
                          </div>
                        </div>
                      </a>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <button
            alt="next"
            aria-label="next"
            class="slick-arrow slick-next slick-disabled emotion-3"
            disabled=""
          >
            <img
              alt="next"
              draggable="false"
              src="img-imp-l.png"
            />
          </button>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`<DesktopRecs /> should match snapshots container width set to 1920px using an empty style property 1`] = `
.emotion-0 {
  max-width: 1920px;
  -webkit-overflow-scrolling: touch;
  width: 100%;
  position: relative;
  margin: 0 auto;
}

.emotion-0 .slick-list {
  overflow-x: hidden;
  overflow-y: auto;
}

.emotion-1 {
  position: relative;
  float: none;
  margin: 0 auto;
  display: inline-block;
  width: 100%;
}

.emotion-2 {
  position: relative;
  float: none;
  width: 100%;
  margin: 0 auto;
}

.emotion-2 .slick-slider {
  position: relative;
  display: block;
  box-sizing: border-box;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  -webkit-touch-callout: none;
  touch-action: pan-y;
  -webkit-tap-highlight-color: transparent;
  scroll-behavior: smooth;
}

.emotion-2 .slick-slider .slick-track,
.emotion-2 .slick-slider .slick-list {
  -webkit-transform: translate3d(0, 0, 0);
  -moz-transform: translate3d(0, 0, 0);
  -ms-transform: translate3d(0, 0, 0);
  transform: translate3d(0, 0, 0);
}

.emotion-2 .slick-list {
  position: relative;
  display: block;
  overflow-x: hidden;
  overflow-y: hidden;
  margin: 0;
  padding: 0;
}

.emotion-2 .slick-list:focus {
  outline: none;
}

.emotion-2 .slick-list.dragging {
  cursor: pointer;
  cursor: hand;
}

.emotion-2 .slick-track {
  position: relative;
  top: 0;
  left: 0;
  display: block;
  margin-left: auto;
  margin-right: auto;
}

.emotion-2 .slick-track:before,
.emotion-2 .slick-track:after {
  display: table;
  content: "";
}

.emotion-2 .slick-track:after {
  clear: both;
}

.emotion-2 .slick-slide {
  display: none;
  float: left;
  height: 100%;
  min-height: 1px;
}

.emotion-2 .slick-slide img {
  display: block;
}

.emotion-2 .slick-slide.slick-loading img {
  display: none;
}

.emotion-2 .slick-slide.dragging img {
  pointer-events: none;
}

.emotion-2 [dir="rtl"] .slick-slide {
  float: right;
}

.emotion-2 .slick-initialized .slick-slide,
.emotion-2 .slick-vertical .slick-slide {
  display: block;
}

.emotion-2 .slick-vertical .slick-slide {
  border: 1px solid transparent;
  height: auto;
}

.emotion-2 .slick-loading .slick-track,
.emotion-2 .slick-loading .slick-slide {
  visibility: hidden;
}

.emotion-2 .slick-loading .slick-list {
  background: #fff url("./ajax-loader.gif") center center no-repeat;
}

.emotion-2 .slick-arrow.slick-hidden {
  display: none;
}

.emotion-2 .slick-prev,
.emotion-2 .slick-next {
  font-size: 0;
  line-height: 0;
  position: absolute;
  top: 0;
  display: block;
  padding: 0;
  cursor: pointer;
  color: transparent;
  border: none;
  outline: none;
  background: transparent;
}

.emotion-2 .slick-prev:hover,
.emotion-2 .slick-next:hover,
.emotion-2 .slick-prev:focus,
.emotion-2 .slick-next:focus {
  color: transparent;
  outline: none;
  background: transparent;
  opacity: 1;
}

.emotion-2 .slick-prev.slick-disabled,
.emotion-2 .slick-next.slick-disabled {
  opacity: 0.25;
}

.emotion-2 .slick-prev {
  left: -0px;
  -webkit-transform: translate(0, -50%) rotate(90deg);
  -moz-transform: translate(0, -50%) rotate(90deg);
  -ms-transform: translate(0, -50%) rotate(90deg);
  transform: translate(0, -50%) rotate(90deg);
}

.emotion-2 .slick-prev span {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-2 [dir="rtl"] .slick-prev {
  right: -0px;
  left: auto;
}

.emotion-2 .slick-next {
  right: -0px;
  -webkit-transform: translate(0, -50%) rotate(-90deg);
  -moz-transform: translate(0, -50%) rotate(-90deg);
  -ms-transform: translate(0, -50%) rotate(-90deg);
  transform: translate(0, -50%) rotate(-90deg);
}

.emotion-2 .slick-next span {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-2 [dir="rtl"] .slick-next {
  right: auto;
  left: -0px;
}

.emotion-2 .slick-dotted.slick-slider {
  margin-bottom: 30px;
}

.emotion-2 .slick-dots {
  position: absolute;
  bottom: -25px;
  display: block;
  width: 100%;
  padding: 0;
  margin: 0;
  list-style: none;
  text-align: center;
}

.emotion-2 .slick-dots li {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: 20px;
  height: 20px;
  margin: 0 5px;
  padding: 0;
  cursor: pointer;
}

.emotion-2 .slick-dots li.slick-active button:before {
  opacity: 0.75;
  color: black;
}

.emotion-2 .slick-dots li button {
  font-size: 0;
  line-height: 0;
  display: block;
  padding: 5px;
  cursor: pointer;
  color: transparent;
  border: 0;
  outline: none;
  background: transparent;
}

.emotion-2 .slick-dots li button:hover,
.emotion-2 .slick-dots li button:focus {
  outline: none;
}

.emotion-2 .slick-dots li button:hover:before,
.emotion-2 .slick-dots li button:focus:before,
.emotion-2 .slick-dots li button:hover:before,
.emotion-2 .slick-dots li button:focus:before {
  opacity: 1;
}

.emotion-2 .slick-dots li button:before {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 5px;
  height: 5px;
  content: "";
  opacity: 0.25;
  background-color: black;
  border-radius: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-2 .slick-prev,
.emotion-2 .slick-next {
  display: none;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  height: 100%;
  max-width: 50px;
  z-index: 1;
  top: 0;
  -webkit-transform: none;
  -moz-transform: none;
  -ms-transform: none;
  transform: none;
}

.emotion-2 .slick-prev.slick-disabled,
.emotion-2 .slick-next.slick-disabled {
  cursor: default;
  opacity: 0.125;
}

.emotion-2 .slick-prev {
  max-width: 20px;
}

.emotion-2 .slick-next {
  max-width: 20px;
  -webkit-transform: rotate(180deg);
  -moz-transform: rotate(180deg);
  -ms-transform: rotate(180deg);
  transform: rotate(180deg);
}

.emotion-4 {
  max-width: 192px;
  display: inline-block;
  display: block;
  -webkit-box-flex: 5;
  -webkit-flex-grow: 5;
  -ms-flex-positive: 5;
  flex-grow: 5;
  margin: auto;
  width: 85%;
}

.emotion-5 {
  padding: 0 0.5rem;
}

.emotion-8 {
  position: relative;
}

.emotion-10 {
  margin-top: 0.5rem;
  color: #666;
  overflow: hidden;
  text-overflow: ellipsis;
  line-height: 1.2;
  white-space: normal;
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
}

<div>
  <div
    class="emotion-0"
    id="mui-certona-recs-container"
  >
    <div
      class="emotion-1"
    >
      <div
        class="emotion-2"
      >
        <div
          class="slick-slider slick-initialized"
          dir="ltr"
        >
          <button
            alt="previous"
            aria-label="previous"
            class="slick-arrow slick-prev slick-disabled emotion-3"
            disabled=""
          >
            <img
              alt="previous"
              draggable="false"
              src="img-imp.png"
            />
          </button>
          <div
            class="slick-list"
          >
            <div
              class="slick-track"
              style="opacity: 1; transform: translate3d(0px, 0px, 0px);"
            >
              <div
                aria-hidden="false"
                class="slick-slide slick-active slick-current"
                data-index="0"
                style="outline: none; width: 0px;"
                tabindex="-1"
              >
                <div>
                  <div
                    class="emotion-4"
                    data-testid="recommended-product-card"
                  >
                    <div
                      class="emotion-5"
                    >
                      <a
                        class="emotion-3"
                        href="/browse/product.do?cid=1123863&pcid=372436&vid=1&pid=372436002&mlink=5001,1,sitewide_onhome1_rr_0&clink=1"
                        target="_self"
                      >
                        <div
                          class="emotion-3"
                        >
                          <div
                            class="emotion-8"
                          >
                            <img
                              alt="Lightweight Run Pant"
                              aria-hidden="true"
                              class="emotion-9"
                              src="https://www4.assets-gap.com/webcontent/0016/273/931/cn16273931.jpg"
                            />
                          </div>
                          <div
                            class="emotion-10"
                          >
                            <p
                              class="emotion-3"
                            >
                              Lightweight Run Pant
                            </p>
                          </div>
                          <div
                            class="emotion-10"
                            data-testid="price-block"
                          >
                            <div
                              class="emotion-3"
                            >
                              $NaN
                            </div>
                          </div>
                        </div>
                      </a>
                    </div>
                  </div>
                </div>
              </div>
              <div
                aria-hidden="false"
                class="slick-slide slick-active"
                data-index="1"
                style="outline: none; width: 0px;"
                tabindex="-1"
              >
                <div>
                  <div
                    class="emotion-4"
                    data-testid="recommended-product-card"
                  >
                    <div
                      class="emotion-5"
                    >
                      <a
                        class="emotion-3"
                        href="/browse/product.do?cid=1123863&pcid=372436&vid=1&pid=372436002&mlink=5001,1,sitewide_onhome1_rr_1&clink=1"
                        target="_self"
                      >
                        <div
                          class="emotion-3"
                        >
                          <div
                            class="emotion-8"
                          >
                            <img
                              alt="Lightweight Run Pant"
                              aria-hidden="true"
                              class="emotion-9"
                              src="https://www4.assets-gap.com/webcontent/0016/273/931/cn16273931.jpg"
                            />
                          </div>
                          <div
                            class="emotion-10"
                          >
                            <p
                              class="emotion-3"
                            >
                              Lightweight Run Pant
                            </p>
                          </div>
                          <div
                            class="emotion-10"
                            data-testid="price-block"
                          >
                            <div
                              class="emotion-3"
                            >
                              $NaN
                            </div>
                          </div>
                        </div>
                      </a>
                    </div>
                  </div>
                </div>
              </div>
              <div
                aria-hidden="false"
                class="slick-slide slick-active"
                data-index="2"
                style="outline: none; width: 0px;"
                tabindex="-1"
              >
                <div>
                  <div
                    class="emotion-4"
                    data-testid="recommended-product-card"
                  >
                    <div
                      class="emotion-5"
                    >
                      <a
                        class="emotion-3"
                        href="/browse/product.do?cid=1123863&pcid=372436&vid=1&pid=372436002&mlink=5001,1,sitewide_onhome1_rr_2&clink=1"
                        target="_self"
                      >
                        <div
                          class="emotion-3"
                        >
                          <div
                            class="emotion-8"
                          >
                            <img
                              alt="Lightweight Run Pant"
                              aria-hidden="true"
                              class="emotion-9"
                              src="https://www4.assets-gap.com/webcontent/0016/273/931/cn16273931.jpg"
                            />
                          </div>
                          <div
                            class="emotion-10"
                          >
                            <p
                              class="emotion-3"
                            >
                              Lightweight Run Pant
                            </p>
                          </div>
                          <div
                            class="emotion-10"
                            data-testid="price-block"
                          >
                            <div
                              class="emotion-3"
                            >
                              $NaN
                            </div>
                          </div>
                        </div>
                      </a>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <button
            alt="next"
            aria-label="next"
            class="slick-arrow slick-next slick-disabled emotion-3"
            disabled=""
          >
            <img
              alt="next"
              draggable="false"
              src="img-imp-l.png"
            />
          </button>
        </div>
      </div>
    </div>
  </div>
</div>
`;
