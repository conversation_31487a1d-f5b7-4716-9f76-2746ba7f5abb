// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`RecommendedProduct snapshot should match the snapshot 1`] = `
.emotion-0 {
  max-width: 192px;
  display: inline-block;
  width: 25%;
  margin-bottom: 8px;
  border: 2px solid green;
}

.emotion-1 {
  padding: 0 0.5rem;
}

.emotion-4 {
  position: relative;
}

.emotion-5 {
  border: 13px solid purple;
  width: 100%;
}

.emotion-6 {
  margin-top: 0.5rem;
  color: #666;
  overflow: hidden;
  text-overflow: ellipsis;
  line-height: 1.2;
  white-space: normal;
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
}

.emotion-12 {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  color: #666;
  overflow: hidden;
  text-overflow: ellipsis;
  line-height: 1.2;
  white-space: normal;
  margin-top: 0.5rem;
  position: relative;
  text-align: left;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

.emotion-13 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  -webkit-box-flex-flow: wrap;
  -webkit-flex-flow: wrap;
  -ms-flex-flow: wrap;
  flex-flow: wrap;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: unset;
}

.emotion-14 {
  position: absolute;
  height: 1px;
  width: 1px;
  overflow: hidden;
  clip: rect(0 0 0 0);
}

.emotion-15 {
  position: relative;
  width: 93px;
  margin-right: 0.8125em;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-16 {
  width: 100%;
  margin: 0;
  padding: 0;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: space-around;
  -ms-flex-pack: space-around;
  -webkit-justify-content: space-around;
  justify-content: space-around;
  margin-block-start: 0;
  margin-block-end: 0;
  -webkit-margin-start: 0;
  margin-inline-start: 0;
  -webkit-margin-end: 0;
  margin-inline-end: 0;
}

.emotion-16 span {
  width: 100%;
  margin: 0;
}

.emotion-17 {
  display: inline-block;
  height: auto;
  width: auto;
  min-height: auto;
  min-width: auto;
}

.emotion-17 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-17 svg path {
  fill: #0A5694;
}

.emotion-17 svg rect {
  fill: #0A5694;
}

.emotion-20 {
  display: inline-block;
  height: auto;
  width: auto;
  min-height: auto;
  min-width: auto;
}

.emotion-20 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-20 svg path {
  fill: url(#fractional-gradient_39);
}

.emotion-20 svg rect {
  fill: url(#fractional-gradient_39);
}

.emotion-21 {
  display: inline-block;
  height: auto;
  width: auto;
  min-height: auto;
  min-width: auto;
}

.emotion-21 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-21 svg path {
  fill: #CCC;
}

.emotion-21 svg rect {
  fill: #CCC;
}

.emotion-21 svg path {
  fill-opacity: 100%;
}

.emotion-22 {
  color: #666;
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 0.8125em;
  margin-left: 0;
  margin-top: 0;
}

<div>
  <div
    class="emotion-0"
    data-testid="recommended-product-card"
  >
    <div
      class="emotion-1"
    >
      <a
        class="emotion-2"
        href="some/detail/url.do?&mlink=5001,1,TEST-TID_0&clink=1"
        target="_self"
      >
        <div
          class="emotion-2"
        >
          <div
            class="emotion-4"
          >
            <img
              alt="Denim Pants"
              aria-hidden="true"
              class="emotion-5"
              src="some/url.jpg"
            />
          </div>
          <div
            class="emotion-6"
            style="font-weight: bold; text-align: left;"
          >
            <p
              class="emotion-2"
            >
              Denim Pants
            </p>
          </div>
          <div
            class="emotion-6"
            data-testid="price-block"
          >
            <div
              aria-label="Was 89.99"
              class="emotion-2"
              style="color: green; text-align: left;"
            >
              $89.99
            </div>
            <span
              class="emotion-2"
            >
               
              33
              % 
              off
            </span>
            <div
              class="emotion-2"
              data-testid="price-block-now"
              style="color: red;"
            >
              Now
               
              $59.99
            </div>
          </div>
          <div
            class="emotion-12"
          >
            <a
              class="emotion-13"
              data-testid="reviewRatings"
              tabindex="-1"
            >
              <span
                class="emotion-14"
                data-testid="reviewRatingsAriaLabel"
              >
                ariaLabel, reviewCountLabel_plural
              </span>
              <div
                class="emotion-15"
              >
                <div>
                  <svg
                    style="width: 0px; height: 0px; display: block;"
                    viewBox="0 0 14 13"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <defs>
                      <lineargradient
                        id="fractional-gradient_39"
                        x1="0"
                        x2="100%"
                        y1="0"
                        y2="0"
                      >
                        <stop
                          offset="0%"
                          stop-color="#0A5694"
                          stop-opacity="100%"
                        />
                        <stop
                          offset="50%"
                          stop-color="#0A5694"
                          stop-opacity="100%"
                        />
                        <stop
                          offset="50%"
                          stop-color="#CCC"
                          stop-opacity="100%"
                        />
                        <stop
                          offset="100%"
                          stop-color="#CCC"
                          stop-opacity="100%"
                        />
                      </lineargradient>
                    </defs>
                  </svg>
                </div>
                <figure
                  class="emotion-16"
                >
                  <span
                    aria-hidden="true"
                    class="emotion-17"
                  >
                    <svg
                      fill="none"
                      viewBox="0 0 14 13"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        clip-rule="evenodd"
                        d="M7.02697 9.38889l-4.219 3.53831 1.40821-4.98276L0 4.76725l5.07966-.0364L7.02697 0l1.89337 4.73085L14 4.76725 9.83776 7.94444 11.2431 13 7.02697 9.38889z"
                        fill="#0A5694"
                        fill-rule="evenodd"
                      />
                    </svg>
                  </span>
                  <span
                    aria-hidden="true"
                    class="emotion-17"
                  >
                    <svg
                      fill="none"
                      viewBox="0 0 14 13"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        clip-rule="evenodd"
                        d="M7.02697 9.38889l-4.219 3.53831 1.40821-4.98276L0 4.76725l5.07966-.0364L7.02697 0l1.89337 4.73085L14 4.76725 9.83776 7.94444 11.2431 13 7.02697 9.38889z"
                        fill="#0A5694"
                        fill-rule="evenodd"
                      />
                    </svg>
                  </span>
                  <span
                    aria-hidden="true"
                    class="emotion-17"
                  >
                    <svg
                      fill="none"
                      viewBox="0 0 14 13"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        clip-rule="evenodd"
                        d="M7.02697 9.38889l-4.219 3.53831 1.40821-4.98276L0 4.76725l5.07966-.0364L7.02697 0l1.89337 4.73085L14 4.76725 9.83776 7.94444 11.2431 13 7.02697 9.38889z"
                        fill="#0A5694"
                        fill-rule="evenodd"
                      />
                    </svg>
                  </span>
                  <span
                    aria-hidden="true"
                    class="emotion-20"
                  >
                    <svg
                      fill="none"
                      viewBox="0 0 14 13"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        clip-rule="evenodd"
                        d="M7.02697 9.38889l-4.219 3.53831 1.40821-4.98276L0 4.76725l5.07966-.0364L7.02697 0l1.89337 4.73085L14 4.76725 9.83776 7.94444 11.2431 13 7.02697 9.38889z"
                        fill="url(#fractional-gradient_39)"
                        fill-rule="evenodd"
                      />
                    </svg>
                  </span>
                  <span
                    aria-hidden="true"
                    class="emotion-21"
                  >
                    <svg
                      fill="none"
                      viewBox="0 0 14 13"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        clip-rule="evenodd"
                        d="M7.02697 9.38889l-4.219 3.53831 1.40821-4.98276L0 4.76725l5.07966-.0364L7.02697 0l1.89337 4.73085L14 4.76725 9.83776 7.94444 11.2431 13 7.02697 9.38889z"
                        fill="#CCC"
                        fill-rule="evenodd"
                      />
                    </svg>
                  </span>
                </figure>
              </div>
              <div
                aria-hidden="true"
                class="emotion-22"
              >
                reviewCountLabel_plural
              </div>
            </a>
          </div>
        </div>
      </a>
    </div>
  </div>
</div>
`;

exports[`RecommendedProduct snapshot should match the snapshot with draggable props 1`] = `
.emotion-0 {
  max-width: 192px;
  display: inline-block;
  width: 25%;
  margin-bottom: 8px;
  border: 2px solid green;
}

.emotion-1 {
  padding: 0 0.5rem;
}

.emotion-4 {
  position: relative;
}

.emotion-5 {
  border: 13px solid purple;
  width: 100%;
}

.emotion-6 {
  margin-top: 0.5rem;
  color: #666;
  overflow: hidden;
  text-overflow: ellipsis;
  line-height: 1.2;
  white-space: normal;
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
}

.emotion-12 {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  color: #666;
  overflow: hidden;
  text-overflow: ellipsis;
  line-height: 1.2;
  white-space: normal;
  margin-top: 0.5rem;
  position: relative;
  text-align: left;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

.emotion-13 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  -webkit-box-flex-flow: wrap;
  -webkit-flex-flow: wrap;
  -ms-flex-flow: wrap;
  flex-flow: wrap;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: unset;
}

.emotion-14 {
  position: absolute;
  height: 1px;
  width: 1px;
  overflow: hidden;
  clip: rect(0 0 0 0);
}

.emotion-15 {
  position: relative;
  width: 93px;
  margin-right: 0.8125em;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-16 {
  width: 100%;
  margin: 0;
  padding: 0;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: space-around;
  -ms-flex-pack: space-around;
  -webkit-justify-content: space-around;
  justify-content: space-around;
  margin-block-start: 0;
  margin-block-end: 0;
  -webkit-margin-start: 0;
  margin-inline-start: 0;
  -webkit-margin-end: 0;
  margin-inline-end: 0;
}

.emotion-16 span {
  width: 100%;
  margin: 0;
}

.emotion-17 {
  display: inline-block;
  height: auto;
  width: auto;
  min-height: auto;
  min-width: auto;
}

.emotion-17 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-17 svg path {
  fill: #0A5694;
}

.emotion-17 svg rect {
  fill: #0A5694;
}

.emotion-20 {
  display: inline-block;
  height: auto;
  width: auto;
  min-height: auto;
  min-width: auto;
}

.emotion-20 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-20 svg path {
  fill: url(#fractional-gradient_40);
}

.emotion-20 svg rect {
  fill: url(#fractional-gradient_40);
}

.emotion-21 {
  display: inline-block;
  height: auto;
  width: auto;
  min-height: auto;
  min-width: auto;
}

.emotion-21 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-21 svg path {
  fill: #CCC;
}

.emotion-21 svg rect {
  fill: #CCC;
}

.emotion-21 svg path {
  fill-opacity: 100%;
}

.emotion-22 {
  color: #666;
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 0.8125em;
  margin-left: 0;
  margin-top: 0;
}

<div>
  <div
    class="emotion-0"
    data-testid="recommended-product-card"
  >
    <div
      class="emotion-1"
    >
      <a
        class="emotion-2"
        href="some/detail/url.do?&mlink=5001,1,sitewide_onhome2_rr_0&clink=1"
        target="_self"
      >
        <div
          class="emotion-2"
        >
          <div
            class="emotion-4"
          >
            <img
              alt="Denim Pants"
              aria-hidden="true"
              class="emotion-5"
              src="some/url.jpg"
            />
          </div>
          <div
            class="emotion-6"
            style="font-weight: bold; text-align: left;"
          >
            <p
              class="emotion-2"
            >
              Denim Pants
            </p>
          </div>
          <div
            class="emotion-6"
            data-testid="price-block"
          >
            <div
              aria-label="Was 89.99"
              class="emotion-2"
              style="color: green; text-align: left;"
            >
              $89.99
            </div>
            <span
              class="emotion-2"
            >
               
              33
              % 
              off
            </span>
            <div
              class="emotion-2"
              data-testid="price-block-now"
              style="color: red;"
            >
              Now
               
              $59.99
            </div>
          </div>
          <div
            class="emotion-12"
          >
            <a
              class="emotion-13"
              data-testid="reviewRatings"
              tabindex="-1"
            >
              <span
                class="emotion-14"
                data-testid="reviewRatingsAriaLabel"
              >
                ariaLabel, reviewCountLabel_plural
              </span>
              <div
                class="emotion-15"
              >
                <div>
                  <svg
                    style="width: 0px; height: 0px; display: block;"
                    viewBox="0 0 14 13"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <defs>
                      <lineargradient
                        id="fractional-gradient_40"
                        x1="0"
                        x2="100%"
                        y1="0"
                        y2="0"
                      >
                        <stop
                          offset="0%"
                          stop-color="#0A5694"
                          stop-opacity="100%"
                        />
                        <stop
                          offset="50%"
                          stop-color="#0A5694"
                          stop-opacity="100%"
                        />
                        <stop
                          offset="50%"
                          stop-color="#CCC"
                          stop-opacity="100%"
                        />
                        <stop
                          offset="100%"
                          stop-color="#CCC"
                          stop-opacity="100%"
                        />
                      </lineargradient>
                    </defs>
                  </svg>
                </div>
                <figure
                  class="emotion-16"
                >
                  <span
                    aria-hidden="true"
                    class="emotion-17"
                  >
                    <svg
                      fill="none"
                      viewBox="0 0 14 13"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        clip-rule="evenodd"
                        d="M7.02697 9.38889l-4.219 3.53831 1.40821-4.98276L0 4.76725l5.07966-.0364L7.02697 0l1.89337 4.73085L14 4.76725 9.83776 7.94444 11.2431 13 7.02697 9.38889z"
                        fill="#0A5694"
                        fill-rule="evenodd"
                      />
                    </svg>
                  </span>
                  <span
                    aria-hidden="true"
                    class="emotion-17"
                  >
                    <svg
                      fill="none"
                      viewBox="0 0 14 13"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        clip-rule="evenodd"
                        d="M7.02697 9.38889l-4.219 3.53831 1.40821-4.98276L0 4.76725l5.07966-.0364L7.02697 0l1.89337 4.73085L14 4.76725 9.83776 7.94444 11.2431 13 7.02697 9.38889z"
                        fill="#0A5694"
                        fill-rule="evenodd"
                      />
                    </svg>
                  </span>
                  <span
                    aria-hidden="true"
                    class="emotion-17"
                  >
                    <svg
                      fill="none"
                      viewBox="0 0 14 13"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        clip-rule="evenodd"
                        d="M7.02697 9.38889l-4.219 3.53831 1.40821-4.98276L0 4.76725l5.07966-.0364L7.02697 0l1.89337 4.73085L14 4.76725 9.83776 7.94444 11.2431 13 7.02697 9.38889z"
                        fill="#0A5694"
                        fill-rule="evenodd"
                      />
                    </svg>
                  </span>
                  <span
                    aria-hidden="true"
                    class="emotion-20"
                  >
                    <svg
                      fill="none"
                      viewBox="0 0 14 13"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        clip-rule="evenodd"
                        d="M7.02697 9.38889l-4.219 3.53831 1.40821-4.98276L0 4.76725l5.07966-.0364L7.02697 0l1.89337 4.73085L14 4.76725 9.83776 7.94444 11.2431 13 7.02697 9.38889z"
                        fill="url(#fractional-gradient_40)"
                        fill-rule="evenodd"
                      />
                    </svg>
                  </span>
                  <span
                    aria-hidden="true"
                    class="emotion-21"
                  >
                    <svg
                      fill="none"
                      viewBox="0 0 14 13"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        clip-rule="evenodd"
                        d="M7.02697 9.38889l-4.219 3.53831 1.40821-4.98276L0 4.76725l5.07966-.0364L7.02697 0l1.89337 4.73085L14 4.76725 9.83776 7.94444 11.2431 13 7.02697 9.38889z"
                        fill="#CCC"
                        fill-rule="evenodd"
                      />
                    </svg>
                  </span>
                </figure>
              </div>
              <div
                aria-hidden="true"
                class="emotion-22"
              >
                reviewCountLabel_plural
              </div>
            </a>
          </div>
        </div>
      </a>
    </div>
  </div>
</div>
`;
