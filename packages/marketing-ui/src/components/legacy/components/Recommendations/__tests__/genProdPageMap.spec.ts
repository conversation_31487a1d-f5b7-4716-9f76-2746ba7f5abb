// @ts-nocheck
import { genProdPageMap, getTotalNumPages } from '../helpers/genProdPageMap';

const TotalPages = 'Number of Pages';
describe('Helper Function: Pagination', () => {
  it(`${TotalPages} = 0 when showPagintor is falsy`, () => {
    expect(getTotalNumPages(false, 3, 2)).toEqual(0);
  });
  it(`${TotalPages} = 0 when nTotalProducts is falsy`, () => {
    expect(getTotalNumPages(true, 0, 2)).toEqual(0);
  });
  it(`${TotalPages} = 0 when nSlidesToShow is falsy`, () => {
    expect(getTotalNumPages(true, 3, 0)).toEqual(0);
  });
  it(`${TotalPages} = 1 when nProducts > nSlidesToShow`, () => {
    expect(getTotalNumPages(true, 3, 2)).toEqual(2);
  });
  it(`${TotalPages} = 7, when 7 total products, and 1 slide to show per page`, () => {
    expect(getTotalNumPages(true, 7, 1)).toEqual(7);
  });
  it(`${TotalPages} = 1, when 7 total products, and 7 slide to show per page`, () => {
    expect(getTotalNumPages(true, 7, 7)).toEqual(1);
  });

  // ODD Total Products
  it(`${TotalPages} = 4  when odd Products Even Slides`, () => {
    expect(getTotalNumPages(true, 7, 2)).toEqual(4);
  });
  it(`${TotalPages} = 3 when Odd Products Odd Slides`, () => {
    expect(getTotalNumPages(true, 7, 3)).toEqual(3);
  });
  it(`${TotalPages} = 1 when Odd Products Odd Slides with remainder`, () => {
    expect(getTotalNumPages(true, 3, 7)).toEqual(1);
  });

  // Even Total Products
  it(`${TotalPages} = 4  when Even Products Even Slides`, () => {
    expect(getTotalNumPages(true, 6, 2)).toEqual(3);
  });
  it(`${TotalPages} = 3 when Even Products Odd Slides`, () => {
    expect(getTotalNumPages(true, 6, 3)).toEqual(2);
  });
  it(`${TotalPages} = 2 when Even Products Odd Slides with remainder`, () => {
    expect(getTotalNumPages(true, 6, 5)).toEqual(2);
  });
});

const productMapTitle = 'Product page map object';
describe(`Generate ${productMapTitle}. Where: key is the active product, value is the page no.`, () => {
  it(`${productMapTitle} = {} when showPagintor is falsy`, () => {
    expect(genProdPageMap(false, 3, 2)).toEqual({});
  });
  it(`${productMapTitle} = {} when nTotalProducts is falsy`, () => {
    expect(genProdPageMap(true, 0, 2)).toEqual({});
  });
  it(`${productMapTitle} = {} when nSlidesToShow is falsy`, () => {
    expect(genProdPageMap(true, 3, 0)).toEqual({});
  });
  it(`${productMapTitle} = {0:1} when nProducts > nSlidesToShow is 1`, () => {
    expect(genProdPageMap(true, 3, 1)).toEqual({ 0: 1, 1: 2, 2: 3 });
  });

  // ODD Total Products
  it(`${productMapTitle} = {0: 1, 2: 2, 4: 3, 6: 4} when odd Products Even Slides`, () => {
    expect(genProdPageMap(true, 7, 2)).toEqual({ 0: 1, 2: 2, 4: 3, 6: 4 });
  });
  it(`${productMapTitle} = {0: 1, 3: 2, 6: 3} when Odd Products Odd Slides`, () => {
    expect(genProdPageMap(true, 7, 3)).toEqual({ 0: 1, 3: 2, 6: 3 });
  });
  it(`${productMapTitle} = {0: 1} when Odd Products Odd Slides with remainder`, () => {
    expect(genProdPageMap(true, 3, 7)).toEqual({ 0: 1 });
  });

  // Even Total Products
  it(`${productMapTitle} = {0: 1, 2: 2, 4: 3} when Even Products Even Slides`, () => {
    expect(genProdPageMap(true, 6, 2)).toEqual({ 0: 1, 2: 2, 4: 3 });
  });
  it(`${productMapTitle} = {0: 1, 3: 2} when Even Products Odd Slides`, () => {
    expect(genProdPageMap(true, 6, 3)).toEqual({ 0: 1, 3: 2 });
  });
  it(`${productMapTitle} = {0: 1, 5: 2} when Even Products Odd Slides with remainder`, () => {
    expect(genProdPageMap(true, 6, 5)).toEqual({ 0: 1, 5: 2 });
  });
});
