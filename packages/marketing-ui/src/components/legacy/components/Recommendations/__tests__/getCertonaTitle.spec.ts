// @ts-nocheck
import getCertonaTitle from '../helpers/get-certona-title';

const mockedSchemes = [
  {
    scheme: 'scheme1',
    items: ['stuff'],
    explanation: 'SHOP NEW ARRIVALS',
  },
  {
    scheme: 'scheme2',
    items: ['moreStuff'],
    explanation: 'WE HAVE STUFF',
  },
];

describe('getCertonaRecs', () => {
  describe('filters the correct scheme', () => {
    it('should return the correct scheme item title', () => {
      const expectedString = 'SHOP NEW ARRIVALS';
      const schemeItemTitle = getCertonaTitle(mockedSchemes, 'scheme1');
      expect(expectedString).toEqual(schemeItemTitle);
    });

    it('should return the correct scheme item title when not first scheme', () => {
      const expectedString = 'WE HAVE STUFF';
      const schemeItemTitle = getCertonaTitle(mockedSchemes, 'scheme2');
      expect(expectedString).toEqual(schemeItemTitle);
    });
  });
});
