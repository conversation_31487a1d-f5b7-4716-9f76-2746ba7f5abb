// @ts-nocheck
import React from 'react';
import { render, act } from 'test-utils';
import { mapRecommendations, mapLoadingPlaceholders } from '../helpers/map-recommendations';
import { mockProps } from '../__fixtures__/recommendedProduct-mockProps';
import RecommendedProduct from '../RecommendedProduct';
import { RecsPlaceholder } from '../RecsPlaceholder';

const recommendationsUS = [
  {
    ID: '495965002',
    ProductName: 'Jeans',
    ImageURL: '/webcontent/0017/612/471/cn17612471.jpg',
    LightWeightImageURL: '/webcontent/0017/612/468/cn17612468.jpg',
    DetailURL: '/browse/product.do?pid=495965002&rrec=true',
    instock: 'False',
    OriginalPrice: '69.95',
    CurrentPrice: '33.99',
    MarketingFlag: '',
    PromotionDisplay: '',
  },
  {
    ID: '491343002',
    ProductName: 'Pants',
    ImageURL: '/webcontent/0017/365/846/cn17365846.jpg',
    LightWeightImageURL: '/webcontent/0017/365/842/cn17365842.jpg',
    DetailURL: '/browse/product.do?pid=491343002&rrec=true',
    instock: 'False',
    OriginalPrice: '59.95',
    CurrentPrice: '25.99',
    MarketingFlag: '',
    PromotionDisplay: '',
  },
];

const recommendationsCA = [
  {
    explanation: 'WE PICKED THESE FOR YOU',
    item: [
      {
        ID: '499048',
        ProductName: 'Shirt',
        ImageURL: '/webcontent/0017/772/637/cn17772637.jpg',
        LightWeightImageURL: '/webcontent/0017/772/635/cn17772635.jpg',
        DetailURL: '/browse/product.do?pid=499048063&rrec=true',
        instock: 'False',
        OriginalPrice: '34.95',
        CurrentPrice: '34.95',
        MarketingFlag: '',
        PromotionDisplay: '',
      },
      {
        ID: '397456',
        ProductName: 'Jacket',
        ImageURL: '/webcontent/0017/752/367/cn17752367.jpg',
        LightWeightImageURL: '/webcontent/0017/752/363/cn17752363.jpg',
        DetailURL: '/browse/product.do?pid=397456103&rrec=true',
        instock: 'False',
        OriginalPrice: '39.95',
        CurrentPrice: '14.99',
        MarketingFlag: '',
        PromotionDisplay: '',
      },
      {
        ID: '397456',
        ProductName: 'Jacket',
        ImageURL: '/webcontent/0017/752/367/cn17752367.jpg',
        LightWeightImageURL: '/webcontent/0017/752/363/cn17752363.jpg',
        DetailURL: '/browse/product.do?pid=397456103&rrec=true',
        instock: 'False',
        OriginalPrice: '39.95',
        CurrentPrice: '14.99',
        MarketingFlag: '',
        PromotionDisplay: '',
      },
    ],
  },
];

const recommendationsCAindexed = {
  explanation: 'WE PICKED THESE FOR YOU',
  item: {
    0: {
      ID: '499048',
      ProductName: 'Shirt',
      ImageURL: '/webcontent/0017/772/637/cn17772637.jpg',
      LightWeightImageURL: '/webcontent/0017/772/635/cn17772635.jpg',
      DetailURL: '/browse/product.do?pid=499048063&rrec=true',
      instock: 'False',
      OriginalPrice: '34.95',
      CurrentPrice: '34.95',
      MarketingFlag: '',
      PromotionDisplay: '',
    },
    1: {
      ID: '397456',
      ProductName: 'Jacket',
      ImageURL: '/webcontent/0017/752/367/cn17752367.jpg',
      LightWeightImageURL: '/webcontent/0017/752/363/cn17752363.jpg',
      DetailURL: '/browse/product.do?pid=397456103&rrec=true',
      instock: 'False',
      OriginalPrice: '39.95',
      CurrentPrice: '14.99',
      MarketingFlag: '',
      PromotionDisplay: '',
    },
    2: {
      ID: '397456',
      ProductName: 'Jacket',
      ImageURL: '/webcontent/0017/752/367/cn17752367.jpg',
      LightWeightImageURL: '/webcontent/0017/752/363/cn17752363.jpg',
      DetailURL: '/browse/product.do?pid=397456103&rrec=true',
      instock: 'False',
      OriginalPrice: '39.95',
      CurrentPrice: '14.99',
      MarketingFlag: '',
      PromotionDisplay: '',
    },
  },
};

const props = {
  brandName: 'gap',
  isLarge: true,
  data: {
    customBrand: 'gap',
    scheme: 'scheme',
    product: {
      CurrentPrice: '5',
      DetailURL: '5',
      ImageURL: '5',
      OriginalPrice: '5',
      ProductName: '5',
    },
    productCardStyles: {
      style: {
        width: 'auto',
        marginBottom: '8px',
        border: '2px solid green',
      },
    },
    gridLayout: {
      style: {},
      productsPerRow: {
        desktop: 4,
        mobile: 2,
      },
    },
    numberOfProduct: 2,
  },
};

describe('<Recommendations/>', () => {
  describe('mapRecommendations function', () => {
    // render every component in the componentArray
    const renderComponentArray = (componentArray: JSX.Element[]) => componentArray.map(component => render(component));

    test('correctly maps recommendations', () => {
      const productArray = mapRecommendations(recommendationsUS, props);
      const res = renderComponentArray(productArray);
      expect(res).toMatchSnapshot();
    });

    test('correctly handles certona data for US', () => {
      const productArray = mapRecommendations(recommendationsUS, props);
      expect(productArray.length).toEqual(recommendationsUS.length);
    });

    test('correctly handles certona data for US', () => {
      const productArray = mapRecommendations(recommendationsUS, props);
      expect(productArray.length).toEqual(recommendationsUS.length);
    });

    test('correctly handles certona data for Canada', () => {
      const productArray = mapRecommendations(recommendationsCA, props);
      expect(productArray.length).toEqual(recommendationsCA[0].item.length);
    });

    test('correctly handles indexed-object certona data for Canada', () => {
      const productArray = mapRecommendations(recommendationsCAindexed, props);
      expect(productArray.length).toEqual(recommendationsCA[0].item.length);
    });
  });

  describe('mapLoadingPlaceholders function', () => {
    test('correctly creates loading placeholders.', () => {
      const numberOfProducts = recommendationsUS.length;
      const productArray = mapLoadingPlaceholders(numberOfProducts, props);
      const expectedLoadingPlaceholders = props.data.numberOfProduct;
      expect(productArray.length).toEqual(expectedLoadingPlaceholders);
    });
  });
});
