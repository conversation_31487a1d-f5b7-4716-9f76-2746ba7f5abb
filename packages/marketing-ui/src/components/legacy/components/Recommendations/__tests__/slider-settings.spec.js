// @ts-nocheck
import settings from '../helpers/slider-settings';

const customarrowMaxWidth = '99px';
const defaultStyles = {
  css: [
    {
      '.slick-next': { maxWidth: '20px', transform: 'rotate(180deg)' },
      '.slick-prev': { maxWidth: '20px' },
      float: 'none',
      margin: '0 auto',
      width: '100%',
    },
  ],
};
const customStyles = {
  arrowMaxWidth: customarrowMaxWidth,
  css: [
    {
      '.slick-next': {
        maxWidth: customarrowMaxWidth,
        transform: 'rotate(180deg)',
      },
      '.slick-prev': { maxWidth: customarrowMaxWidth },
      float: 'none',
      margin: '0 auto',
      width: '100%',
    },
  ],
};

describe('slider settings helper', () => {
  it('should return expected default styles if no options are provided', () => {
    expect(settings()).toEqual(defaultStyles);
  });
  it('should use arrowMaxWidth as maxWidth when provided', () => {
    expect(settings({ arrowMaxWidth: customarrowMaxWidth })).toEqual(customStyles);
  });
});
