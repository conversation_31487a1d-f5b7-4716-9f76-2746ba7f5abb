// @ts-nocheck
'use client';
import React from 'react';
import { styled } from '@ecom-next/core/react-stitch';
import PropTypes from 'prop-types';
import { ChevronIcon } from '@ecom-next/core/legacy/icons';

export const StyledButton = styled.button(() => {});

export const PrevArrow = ({ alt, ariaLabel, className, disabled, src, onClick }) => (
  <StyledButton alt={alt} aria-label={ariaLabel} className={className} disabled={disabled} onClick={onClick}>
    {src ? <img alt={alt} draggable='false' src={src} /> : <ChevronIcon css={{ transform: 'rotate(0.25turn)' }} />}
  </StyledButton>
);

export const NextArrow = ({ alt, ariaLabel, className, disabled, src, onClick }) => (
  <StyledButton alt={alt} aria-label={ariaLabel} className={className} disabled={disabled} onClick={onClick}>
    {src ? <img alt={alt} draggable='false' src={src} /> : <ChevronIcon css={{ transform: 'rotate(0.75turn)' }} />}
  </StyledButton>
);

const arrowPropTypes = {
  /**
   * Alt test for arrow image
   */
  alt: PropTypes.string.isRequired,
  /**
   * Src test for arrow image
   */
  src: PropTypes.string,
};

NextArrow.propTypes = arrowPropTypes;
PrevArrow.propTypes = arrowPropTypes;
