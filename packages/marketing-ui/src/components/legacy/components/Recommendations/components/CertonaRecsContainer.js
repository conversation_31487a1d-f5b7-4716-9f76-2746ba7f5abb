// @ts-nocheck
'use client';
import { forBrands, styled, useTheme } from '@ecom-next/core/react-stitch';
import React, { useContext } from 'react';
import { BreakpointContext, LARGE } from '@ecom-next/core/breakpoint-provider';
import { setMaxWidth } from '../helpers/set-custom-css';

const StyledCertonaRecs = styled.div(({ theme, fullWidth, customCss }) => {
  const { minWidth } = useContext(BreakpointContext);
  const slickOverflow = minWidth(LARGE) ? { overflowX: 'hidden', overflowY: 'auto' } : { overflowX: 'auto', overflowY: 'hidden' };
  const commonStyles = {
    ...customCss,
    maxWidth: setMaxWidth(fullWidth, customCss),
    WebkitOverflowScrolling: 'touch',
    width: '100%',
    position: 'relative',
    margin: '0 auto',
    '.slick-list': {
      ...slickOverflow,
    },
  };

  return forBrands(theme, {
    at: commonStyles,
    br: commonStyles,
    brfs: commonStyles,
    gap: commonStyles,
    gapfs: commonStyles,
    on: commonStyles,
  });
});

const SliderContainer = styled.div(() => ({
  position: 'relative',
  float: 'none',
  margin: '0 auto',
  display: 'inline-block',
  width: '100%',
}));

export const quickAddContainerId = 'mui-certona-recs-container';

export const CertonaRecsContainer = ({ brandName, children, layout, fullWidth, customCss }) => {
  const theme = useTheme();
  const brandStyles = layout === 'carousel' && { width: '100%' };
  const sliderContainerBrandStyles = forBrands(theme, {
    at: brandStyles,
    br: brandStyles,
    brfs: brandStyles,
    gap: {},
    gapfs: {},
    on: brandStyles,
  });
  const sliderContainerStyles = {
    ...sliderContainerBrandStyles,
    ...customCss?.sliderContainer,
  };
  return (
    <StyledCertonaRecs brandName={brandName} customCss={customCss} fullWidth={fullWidth} id={quickAddContainerId}>
      <SliderContainer css={sliderContainerStyles}>{children}</SliderContainer>
    </StyledCertonaRecs>
  );
};
