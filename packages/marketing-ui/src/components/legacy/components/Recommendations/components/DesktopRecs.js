// @ts-nocheck
'use client';
import React, { useState } from 'react';
import { useAppState } from '@ecom-next/sitewide/app-state-provider';
import PropTypes from 'prop-types';
import RecommendedProduct from '../RecommendedProduct';
import getSliderSettings from '../helpers/slider-settings';
import { mapLoadingPlaceholders, mapRecommendations } from '../helpers/map-recommendations';
import optionalSettings from '../helpers/optional-settings';
import createTitle from '../helpers/create-headline';
import { CertonaRecsContainer } from './CertonaRecsContainer';
import { NextArrow, PrevArrow } from './Arrows';
import { genProdPageMap, getTotalNumPages } from '../helpers/genProdPageMap';
import { Paginator } from './Paginator';

const DesktopRecs = props => {
  const [currentSlideIndex, setCurrentSlideIndex] = useState(0);
  const [paginatedCurrentSlideIndex, setPaginatedCurrentSlideIndex] = useState(0);

  const { recommendations, data, title, Layout } = props;
  const { arrowPosition, arrowMaxWidth, arrowVerticalPosition, fullWidth, style, numberOfProduct, paginator } = data;

  const { marketingProduct } = optionalSettings(props);
  const { brandName } = useAppState();
  const isInfinite = data.infinite || props.infinite || false;
  const slickDefaultSlidesToShow = data.resslidesToShowSlick ? data.resslidesToShowSlick : data.defaultslidesToShowSlick;

  let sliderChildren;
  if (!Array.isArray(recommendations)) {
    sliderChildren = [];
  } else {
    sliderChildren = recommendations.length === 1 && Array.isArray(recommendations[0].item) ? recommendations[0].item : recommendations;
  }
  const sliderLength = sliderChildren.length;
  const lastIndex = sliderLength - 1;

  const { pageNumberAriaText, paginatorStyles } = paginator || {};
  const { paginatorPrevArrowStyles, paginatorNextArrowStyles, pageNumberStyles } = paginatorStyles || {};

  const showPaginator = paginator?.showPaginator && data.layout === 'carousel';
  const responsiveSlidesToScroll = showPaginator ? data.resslidesToShowSlick : data.resslidesToScrollSlick;

  const slidesToShow = data.defaultslidesToShowSlick ?? 5;
  const slidesToScroll = showPaginator ? slidesToShow : data.defaultslidesToScrollSlick ?? 5;
  const prodPageMap = genProdPageMap(showPaginator, sliderLength, slidesToShow);
  const totalPages = getTotalNumPages(showPaginator, sliderLength, slidesToShow);

  const paginatorContainerStyles = {
    '@media only screen and (max-width: 1023px)': {
      "& nav[role='navigation']": {
        display: 'none',
      },
    },
    '& .slick-slider .slick-arrow': paginatorNextArrowStyles,
    '& .slick-slider .slick-arrow:first-child': paginatorPrevArrowStyles,
  };

  const slickOptions = {
    arrows: data.arrows ?? true,
    arrowPosition,
    arrowVerticalPosition,
    arrowMaxWidth,
    dots: false,
    infinite: data.infinite ?? false,
    draggable: data.draggable ?? false,
    speed: data.speed ?? 300,
    centerMode: false,
    variableWidth: !!marketingProduct,
    slidesToShow,
    slidesToScroll,
    autoplay: data.autoplay ?? false,
    playAltText: data.playAltText ?? 'play',
    pauseAltText: data.pauseAltText ?? 'pause',
    autoplaySpeed: data.autoplaySpeed ?? 500,
    cssEase: data.cssEase ?? 'ease',
    pauseOnHover: data.pauseOnHover ?? false,
    displayPlayPauseButton: data.displayPlayPauseButton ?? true,
    nextArrowAlt: data.nextArrowAlt ?? 'next',
    prevArrowAlt: data.prevArrowAlt ?? 'previous',
    prevArrow: (
      <PrevArrow
        alt={data.prevArrowAlt}
        ariaLabel={data.prevArrowAlt}
        disabled={isDisabledPrev(isInfinite, sliderLength, currentSlideIndex)}
        onClick
        src={data.prevArrowSlick}
      />
    ),
    nextArrow: (
      <NextArrow
        alt={data.nextArrowAlt}
        ariaLabel={data.nextArrowAlt}
        disabled={isDisabledNext(isInfinite, currentSlideIndex, lastIndex, slickDefaultSlidesToShow)}
        onClick
        src={data.nextArrowSlick}
      />
    ),
    beforeChange: (current, next) => {
      setPaginatedCurrentSlideIndex(next);
    },
    responsive: [
      {
        breakpoint: 1350,
        settings: {
          slidesToShow: data.resslidesToShowSlick,
          slidesToScroll: responsiveSlidesToScroll,
        },
      },
    ],
  };
  const showLoading = !recommendations || recommendations.length === 0;
  const settings = getSliderSettings(slickOptions, fullWidth);
  return (
    <CertonaRecsContainer
      brandName={brandName}
      customCss={{
        ...style,
        ...(showPaginator && paginatorContainerStyles),
      }}
      data-testid='certona-recs-container'
      fullWidth={fullWidth}
      layout={data.layout}
    >
      {data.displayTitle ? createTitle(data, title, true) : null}
      <Layout
        layout={props.data.layout}
        style={showPaginator ? { position: 'unset' } : {}}
        {...settings}
        {...props}
        afterChange={index => setCurrentSlideIndex(index)}
        isLarge
        marketingProduct={marketingProduct}
      >
        {!showLoading && marketingProduct && props.data.layout === 'carousel' ? (
          <RecommendedProduct key={marketingProduct.ID} cnt={marketingProduct.ID} product={marketingProduct} {...props} />
        ) : null}
        {!showLoading && props.data.layout === 'carousel' ? mapRecommendations(recommendations, props) : null}

        {showLoading && props.data.layout === 'carousel' && mapLoadingPlaceholders(numberOfProduct, props)}
      </Layout>
      {showPaginator && (
        <Paginator
          pageNumberAriaText={pageNumberAriaText}
          pageNumberStyles={pageNumberStyles}
          paginatedCurrentSlideIndex={paginatedCurrentSlideIndex}
          prodPageMap={prodPageMap}
          totalPages={totalPages}
        />
      )}
    </CertonaRecsContainer>
  );
};
export const isDisabledPrev = (isInfinite, sliderLength, currentSlideIndex) => !isInfinite && (sliderLength < 2 || currentSlideIndex === 0);

export const isDisabledNext = (isInfinite, currentSlideIndex, lastIndex, slickDefaultSlidesToShow) => {
  const distance = slickDefaultSlidesToShow - 1;
  return !isInfinite && currentSlideIndex >= lastIndex - distance;
};

export default DesktopRecs;

DesktopRecs.propTypes = {
  // eslint-disable-next-line react/require-default-props
  data: PropTypes.shape({
    /**
     * the max width of the carousel arrow with pixel units
     */
    arrowMaxWidth: PropTypes.string,
    /**
     * the distance between the carousel and its arrows
     */
    arrowPosition: PropTypes.string,
    arrowVerticalPosition: PropTypes.string,
    /**
     * number of slides to scroll on click
     */
    defaultslidesToScrollSlick: PropTypes.number,
    /**
     * number of slides to show on initial render
     */
    defaultslidesToShowSlick: PropTypes.number,
    /**
     * Allows carousel to utilize full width (bleed)
     */
    fullWidth: PropTypes.bool,
    /**
     * layout type can be 'grid' or 'carousel'
     */
    layout: PropTypes.string.isRequired,
    /**
     * alt text for the next arrow
     */
    nextArrowAlt: PropTypes.string.isRequired,
    /**
     * custom arrow component for next arrow
     */
    nextArrowSlick: PropTypes.string.isRequired,
    /**
     * alt text for the previous arrow
     */
    prevArrowAlt: PropTypes.string.isRequired,
    /**
     * custom arrow component for previous arrow
     */
    prevArrowSlick: PropTypes.string.isRequired,
    /**
     * Responsive: number of slides to scroll in responsive mode.
     * please check the hardcoded device size above
     */
    resslidesToScrollSlick: PropTypes.number.isRequired,
    /**
     * Responsive: number of slides to show on initial render
     * please check the hardcoded device size above
     */
    resslidesToShowSlick: PropTypes.number.isRequired,
    /**
     * Top level object to manipulate baseline styles for Carousel
     */
    style: PropTypes.object,
  }).isRequired,
  /**
   * should be a component: GridLayout or MarketingCarousel */
  Layout: PropTypes.oneOfType([PropTypes.string, PropTypes.func]).isRequired,
  /**
   * this is an array of product data objects that
   */
  recommendations: PropTypes.arrayOf(PropTypes.object).isRequired,
  /**
   * text used as a headline for this component
   */
  title: PropTypes.string,
};

DesktopRecs.defaultProps = {
  title: '',
  data: {
    defaultslidesToShowSlick: 5,
    defaultslidesToScrollSlick: 5,
  },
};
