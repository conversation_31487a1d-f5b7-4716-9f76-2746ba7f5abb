// @ts-nocheck
'use client';
import React, { useState } from 'react';
import PropTypes from 'prop-types';
import { merge } from 'lodash';
import { useAppState } from '@ecom-next/sitewide/app-state-provider';
import RecommendedProduct from '../RecommendedProduct';
import getSliderSettings from '../helpers/slider-settings';
import { mapRecommendations, mapLoadingPlaceholders } from '../helpers/map-recommendations';
import optionalSettings from '../helpers/optional-settings';
import createTitle from '../helpers/create-headline';
import { CertonaRecsContainer } from './CertonaRecsContainer';
import { isDisabledPrev, isDisabledNext } from './DesktopRecs';
import { PrevArrow, NextArrow } from './Arrows';

const marketingDefaultProps = {
  data: {
    dots: false,
    arrows: false,
    swipeToSlide: false,
    speed: 300,
    centerMode: false,
    defaultslidesToShowSlick: 2.5,
    defaultslidesToScrollSlick: 2,
    responsive: [
      {
        breakpoint: 768,
        settings: {
          swipeToSlide: true,
        },
      },
    ],
  },
};

const MobileRecs = props => {
  const [currentSlideIndex, setCurrentSlideIndex] = useState(0);

  const {
    data: { useMobileConfig, numberOfProduct },
    prevArrowAlt,
    nextArrowAlt,
  } = props;
  const actualProps = (() => {
    if (useMobileConfig) {
      // if useMobileConfig true, override similar objects in marketingDefaultProps with props object
      // basically, this uses props rather than the defaults above
      return merge({}, marketingDefaultProps, props);
    }

    // if useMobileConfig false, override similar objects in props with marketingDefaultPropsobject
    return merge({}, props, marketingDefaultProps, {
      prevArrowAlt,
      nextArrowAlt,
    });
  })();
  const { recommendations, data, title, Layout } = actualProps;

  const isInfinite = data.infinite || props.infinite || false;
  const slickDefaultSlidesToShow = data.defaultslidesToScrollSlick ? data.defaultslidesToScrollSlick : 1;
  const sliderChildren = Array.isArray(recommendations) ? recommendations : [];
  const sliderLength = sliderChildren.length;
  const lastIndex = sliderLength - 1;

  const slickOptions = {
    arrows: data.arrows,
    centerMode: data.centerMode,
    dots: data.dots,
    infinite: data.infinite,
    nextArrow: (
      <NextArrow
        alt={data.nextArrowAlt}
        ariaLabel={data.nextArrowAlt}
        disabled={isDisabledNext(isInfinite, currentSlideIndex, lastIndex, slickDefaultSlidesToShow)}
        onClick
        src={data.nextArrowSlick}
      />
    ),
    prevArrow: (
      <PrevArrow
        alt={data.prevArrowAlt}
        ariaLabel={data.prevArrowAlt}
        disabled={isDisabledPrev(isInfinite, sliderLength, currentSlideIndex)}
        onClick
        src={data.prevArrowSlick}
      />
    ),
    prevArrowAlt: data.prevArrowAlt,
    nextArrowAlt: data.nextArrowAlt,
    responsive: data.responsive,
    slidesToScroll: data.defaultslidesToScrollSlick,
    slidesToShow: data.defaultslidesToShowSlick,
    autoplay: data.autoplay ?? false,
    playAltText: data.playAltText ?? 'play',
    pauseAltText: data.pauseAltText ?? 'pause',
    autoplaySpeed: data.autoplaySpeed ?? 500,
    displayPlayPauseButton: data.displayPlayPauseButton ?? true,
    pauseOnHover: data.pauseOnHover ?? false,
    cssEase: data.cssEase ?? 'ease',
    speed: data.speed,
    swipeToSlide: data.swipeToSlide,
  };
  const { brandName } = useAppState();
  const { marketingProduct } = optionalSettings(props);
  const settings = getSliderSettings(slickOptions);
  const showLoading = !recommendations || recommendations.length === 0;
  return (
    <CertonaRecsContainer brandName={brandName} data-testid='certona-recs-container' layout={data.layout}>
      {data.displayTitle ? createTitle(data, title, false) : null}
      <Layout
        layout={props.data.layout}
        {...settings}
        {...props}
        afterChange={index => setCurrentSlideIndex(index)}
        isLarge
        marketingProduct={marketingProduct}
      >
        {!showLoading && marketingProduct && props.data.layout === 'carousel' ? (
          <RecommendedProduct key={marketingProduct.ID} cnt={marketingProduct.ID} product={marketingProduct} {...props} />
        ) : null}
        {!showLoading && props.data.layout === 'carousel' ? mapRecommendations(recommendations, props) : null}

        {showLoading && props.data.layout === 'carousel' && mapLoadingPlaceholders(numberOfProduct, props)}
      </Layout>
    </CertonaRecsContainer>
  );
};

export default MobileRecs;

MobileRecs.propTypes = {
  data: PropTypes.shape({
    /**
     * Bool to render arrows
     */
    arrows: PropTypes.bool,
    /**
     * SlickOption: Enables centered view with partial prev/next slides
     */
    centerMode: PropTypes.bool,
    /**
     * SlickOption: number of slides per scroll
     */
    defaultslidesToScrollSlick: PropTypes.number,
    /**
     * SlickOption: number of slides to show in viewport
     */
    defaultslidesToShowSlick: PropTypes.number,
    /**
     * SlickOption: bool value if dots needed
     */
    dots: PropTypes.bool,
    /**
     * SlickOption: bool value if carousel loop
     */
    infinite: PropTypes.bool,
    /**
     * Layout options: GridLayout or carousel (MarketingCarousel)
     */
    layout: PropTypes.string.isRequired,
    /**
     * SlickOption: img src path
     */
    nextArrowSlick: PropTypes.string,
    /**
     * SlickOption: img src path
     */
    prevArrowSlick: PropTypes.string,
    /**
     * SlickOption: speed in milliseconds during autoplay
     */
    speed: PropTypes.number,
    /**
     * SlickOption: Allow users to drag or swipe directly to a slide irrespective of slidesToScroll
     */
    swipeToSlide: PropTypes.bool,
    /**
     * bool specify in props to override mobile default
     */
    useMobileConfig: PropTypes.bool,
  }).isRequired,
  Layout: PropTypes.oneOfType([PropTypes.string, PropTypes.func]).isRequired,
  /**
   * An array of product data from the product search API
   */
  recommendations: PropTypes.arrayOf(PropTypes.object).isRequired,
  /**
   * Text for the headline
   */
  title: PropTypes.string,
};

MobileRecs.defaultProps = {
  title: '',
  useMobileConfig: false,
};
