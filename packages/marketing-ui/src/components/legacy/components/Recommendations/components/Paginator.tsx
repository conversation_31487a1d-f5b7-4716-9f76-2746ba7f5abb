// @ts-nocheck
'use client';
import React from 'react';
import { styled } from '@ecom-next/core/react-stitch';
import { PaginatorProps } from '../types';

const PageNumberContainer = styled.nav<PaginatorProps['pageNumberStyles']>(({ pageNumberStyles }) => pageNumberStyles);

export const Paginator = ({
  prodPageMap,
  paginatedCurrentSlideIndex,
  totalPages,
  pageNumberStyles,
  pageNumberAriaText = 'page number',
}: PaginatorProps): JSX.Element => {
  const currentPage = prodPageMap[paginatedCurrentSlideIndex];
  return (
    <PageNumberContainer aria-label={`${pageNumberAriaText} ${currentPage}/${totalPages}`} pageNumberStyles={pageNumberStyles} role='navigation'>
      {currentPage && `${currentPage}/${totalPages}`}
    </PageNumberContainer>
  );
};
