// @ts-nocheck
'use client';
import React from 'react';
import { styled } from '@ecom-next/core/react-stitch';

const Headline = styled.h2(({ theme, css }) => ({
  ...theme.brandFont,
  ...css,
}));

const createTitle = (data, title, isLarge) => {
  const style = isLarge ? data.certonaTitle.style.desktop : data.certonaTitle.style.mobile;
  const displayTitle = data.certonaTitle.title ? data.certonaTitle.title : title;
  return <Headline css={style}>{displayTitle}</Headline>;
};

export default createTitle;
