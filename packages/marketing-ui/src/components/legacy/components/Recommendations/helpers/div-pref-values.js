// @ts-nocheck
'use client';
/**
 * personalizationAttribute - brand specific attribute name used in Personalization Provider component
 * tid - tracking id
 * cid - category id to query for associated division (i.e. women, men, etc.)
 */
export const DIV_PREF_VALUES = {
  on: {
    personalizationAttribute: 'ONDIVPREF',
    categoryIds: {
      WOMEN: {
        tid: 'HP_NA_W',
        cid: '10018',
      },
      BABY_BOY: {
        tid: 'HP_NA_BB',
        cid: '37508',
      },
      BABY_GIRL: {
        tid: 'HP_NA_BG',
        cid: '37505',
      },
      BOYS: {
        tid: 'HP_NA_B',
        cid: '5918',
      },
      GIRLS: {
        tid: 'HP_NA_G',
        cid: '6036',
      },
      MATERNITY: {
        tid: 'HP_NA_Mat',
        cid: '8454',
      },
      MEN: {
        tid: 'HP_NA_M',
        cid: '11174',
      },
      MEN_BIG: {
        tid: 'HP_NA_Mbig',
        cid: '85343',
      },
      MEN_TALL: {
        tid: 'HP_NA_Mtall',
        cid: '1091244',
      },
      NEWBORN: {
        tid: 'HP_NA_NB',
        cid: '37505',
      },
      TODDLER_BOY: {
        tid: 'HP_NA_TB',
        cid: '6157',
      },
      TODDLER_GIRL: {
        tid: 'HP_NA_TG',
        cid: '6825',
      },
      WOMEN_PETITE: {
        tid: 'HP_NA_Wpetite',
        cid: '41714',
      },
      WOMEN_PLUS: {
        tid: 'HP_NA_WP',
        cid: '5597',
      },
      WOMEN_TALL: {
        tid: 'HP_NA_Wtall',
        cid: '41728',
      },
    },
  },
};
