// @ts-nocheck
'use client';
import { GenProdPageMapSchemeItem } from '../types';

export const getTotalNumPages = (showPaginator: boolean, nTotalProducts: number, nSlidesToShow: number): number => {
  if (!showPaginator || !nTotalProducts || !nSlidesToShow) return 0;
  if (nTotalProducts <= nSlidesToShow) return 1;
  if (nSlidesToShow === 1) return nTotalProducts;

  const isDivisable = nTotalProducts % nSlidesToShow === 0;
  const nPages = Math.floor(nTotalProducts / nSlidesToShow);

  return isDivisable ? nPages : nPages + 1;
};

export const genProdPageMap = (showPaginator: boolean, nTotalProducts: number, nSlidesToShow: number): GenProdPageMapSchemeItem<number> => {
  const nPages = getTotalNumPages(showPaginator, nTotalProducts, nSlidesToShow);

  if (nPages === 0) return {};
  if (nPages === 1) return { 0: 1 };

  let pageIter = 1;
  let activeProdIter = 0;
  const prodPageMap: GenProdPageMapSchemeItem<number> = {};

  while (pageIter <= nPages) {
    prodPageMap[activeProdIter] = pageIter;
    activeProdIter += nSlidesToShow;
    pageIter += 1;
  }
  return prodPageMap;
};
