// @ts-nocheck
'use client';
/**
 * @param {object} data - from Personalization Provider component
 * @param {string} attributeNameKey - brand specific key where division preference is stored in data
 * @returns attributeNameKey when it exists otherwise returns false by default
 */

const getDivPrefAttribute = (data, attributeNameKey) => {
  let evergreenAttributes = [];
  let divPrefName = false;

  if (data?.featureSelections?.Evergreens) {
    if (data.featureSelections.Evergreens instanceof Array) {
      evergreenAttributes = data.featureSelections.Evergreens;
    } else if (!(data.featureSelections.Evergreens instanceof Array)) {
      evergreenAttributes.push(data.featureSelections.Evergreens);
    }
    for (let i = 0; i < evergreenAttributes.length; i += 1) {
      if (evergreenAttributes[i].AttributeName === attributeNameKey) {
        divPrefName = evergreenAttributes[i].AttributeValue.split('|')[0];
        break;
      }
    }
  }

  return divPrefName;
};

export default getDivPrefAttribute;
