// @ts-nocheck
'use client';
import React from 'react';
import RecommendedProduct from '../RecommendedProduct';
import { RecsPlaceholder } from '../RecsPlaceholder';

export const mapRecommendations = (recommendations, props) => {
  const createProduct = (product, i) => <RecommendedProduct key={product.ID} cnt={i} product={product} {...props} />;

  let productsArray = recommendations;
  if (recommendations.item) {
    productsArray = Object.values(recommendations.item);
  } else if (recommendations[0]?.item) {
    productsArray = recommendations[0].item;
  }
  return productsArray.map((product, i) => createProduct(product, i));
};

export const mapLoadingPlaceholders = (numProducts, props) => {
  const numPlaceholders = numProducts && numProducts > 0 ? numProducts : 5;
  const placeholderArray = [];
  // eslint-disable-next-line no-plusplus
  for (let i = 0; i < numPlaceholders; i++) {
    const placeholder = <RecsPlaceholder key={`${i}-placeholder`} {...props} />;
    placeholderArray.push(placeholder);
  }
  return placeholderArray;
};
