// @ts-nocheck
'use client';
export default options => {
  const getMarketingProduct = props => {
    let product = null;
    const marketingImage = props.data.marketingOptionalImage;

    if (marketingImage) {
      product = {
        CurrentPrice: marketingImage.CurrentPrice || '0.0',
        OriginalPrice: marketingImage.OriginalPrice || '0.0',
        ProductName: marketingImage.productName || '',
        ID: marketingImage.id || 'marketingImage',
        DetailURL: marketingImage.detailURL || '',
        ImageURL: marketingImage.src || '/',
        MinStyleCurrentPrice: marketingImage.minstylecurrentprice || '0.0',
        MaxStyleCurrentPrice: marketingImage.maxstylecurrentprice || '0.0',
        MinStyleRegularPrice: marketingImage.minstyleregularprice || '0.0',
        MaxStyleRegularPrice: marketingImage.maxstyleregularprice || '0.0',
        CurrentPriceRange: marketingImage.CurrentPriceRange || '',
        DiscountRange: marketingImage.DiscountRange || '',
      };
    }
    return product;
  };

  return {
    marketingProduct: getMarketingProduct(options),
  };
};
