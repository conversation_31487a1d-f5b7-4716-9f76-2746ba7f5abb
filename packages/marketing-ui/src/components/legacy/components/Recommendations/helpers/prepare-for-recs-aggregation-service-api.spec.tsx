// @ts-nocheck
import { Brands } from '@ecom-next/core/legacy/utility';
import { aggregationProductListMock, aggregationProductListNoVLIMock, ccListMock } from '../__fixtures__/aggregation-mock';
import prepareDataForRecsAggregationServiceAPI from './prepare-for-recs-aggregation-service-api';

describe('invoke prepareDataForRecsAggregationServiceAPI', () => {
  test('should return old products mapping if brand is Athleta and FF is disabled', () => {
    const result = prepareDataForRecsAggregationServiceAPI({
      ccList: ccListMock,
      cid: 'asd',
      products: aggregationProductListMock,
      brandName: Brands.Athleta,
      enabledFlags: {},
    });

    expect(result).toMatchSnapshot();
  });

  test('should utilize ccId instead of styleId when constructing a pdp url - with cid', () => {
    const result = prepareDataForRecsAggregationServiceAPI({
      ccList: ccListMock,
      cid: 'asd',
      pcid: '702639022',
      products: aggregationProductListMock,
      brandName: Brands.Athleta,
      enabledFlags: {
        'product-recommendations-use-ccid': true,
      },
    });

    expect(result).toMatchSnapshot();
    expect(result[0].DetailURL).toBe('/browse/product.do?cid=asd&pcid=702639022&vid=1&pid=756089032');
  });

  test('should utilize styleId instead of ccId when constructing a pdp url', () => {
    const result = prepareDataForRecsAggregationServiceAPI({
      ccList: ccListMock,
      cid: 'asd',
      pcid: '702639022',
      products: aggregationProductListMock,
      brandName: Brands.Athleta,
      enabledFlags: {
        'product-recommendations-use-ccid': false,
      },
    });

    expect(result).toMatchSnapshot();
    expect(result[0].DetailURL).toBe('/browse/product.do?cid=asd&pcid=702639022&vid=1&pid=756089');
  });

  test('should utilize ccId instead of styleId when constructing a pdp url - no cid', () => {
    const result = prepareDataForRecsAggregationServiceAPI({
      ccList: ccListMock,
      pcid: '702639022',
      products: aggregationProductListMock,
      brandName: Brands.Athleta,
      enabledFlags: {
        'product-recommendations-use-ccid': true,
      },
    });

    expect(result).toMatchSnapshot();
    expect(result[0].DetailURL).toBe('/browse/product.do?pcid=702639022&vid=1&pid=756089032');
  });

  const nonAtBrands = Object.values(Brands).filter(brand => brand !== Brands.Athleta);
  nonAtBrands.forEach(brand => {
    test(`should return old products mapping for ${brand}`, () => {
      const result = prepareDataForRecsAggregationServiceAPI({
        ccList: ccListMock,
        cid: 'asd',
        products: aggregationProductListMock,
        brandName: brand,
        enabledFlags: { 'at-recommendations-remap': true },
      });

      expect(result).toMatchSnapshot();
    });
  });

  test('should return the remapped products when the brand is Athleta and the FF is enabled', () => {
    const result = prepareDataForRecsAggregationServiceAPI({
      ccList: ccListMock,
      cid: 'asd',
      products: aggregationProductListMock,
      brandName: Brands.Athleta,
      enabledFlags: { 'at-recommendations-remap': true },
    });

    expect(result).toMatchSnapshot();
    result.forEach(res => {
      expect(res.ImageURL.includes('vli')).toBe(true);
      expect(res.LightWeightImageURL.includes('vli')).toBe(true);
    });
  });

  test('should return the remapped products with P01 image type when the brand is Athleta & FF is enabled, but `VLI` is not available', () => {
    const result = prepareDataForRecsAggregationServiceAPI({
      ccList: ccListMock,
      cid: 'asd',
      products: aggregationProductListNoVLIMock,
      brandName: Brands.Athleta,
      enabledFlags: { 'at-recommendations-remap': true },
    });

    expect(result).toMatchSnapshot();
    result.forEach(res => {
      expect(res.ImageURL.includes('p01')).toBe(true);
      expect(res.LightWeightImageURL.includes('p01')).toBe(true);
    });
  });
});
