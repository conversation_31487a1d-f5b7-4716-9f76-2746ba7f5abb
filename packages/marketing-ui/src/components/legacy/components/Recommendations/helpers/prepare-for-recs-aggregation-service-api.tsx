// @ts-nocheck
'use client';
import { Brands } from '@ecom-next/core/react-stitch';
import { AggregationServiceAPIProps, AggregationServiceProductProps } from '../types';

export type ProductAdapterProps = {
  [key: string]: string;
};

export type ProductAdapterList = {
  [key: string]: AggregationServiceProductProps;
};

export const productAdapter = (products: AggregationServiceProductProps[]): ProductAdapterList => {
  const productList = {} as ProductAdapterList;
  products.forEach(product => {
    productList[product.styleId] = product;
  });
  return productList;
};
// pcid: Parent Category ID (this is a root id)
// cid: Category ID (sub category ID)
const prepareDataForRecsAggregationServiceAPI = ({ ccList, products, pcid, cid, enabledFlags, brandName }: AggregationServiceAPIProps) => {
  const productList = productAdapter(products);
  return ccList.map(({ ccId, styleId }) => {
    const isAtRecRemap = !!enabledFlags?.['at-recommendations-remap'] && brandName === Brands.Athleta;

    const selectedProduct = productList[styleId];
    const selectedStyle = selectedProduct.styleColors.find(style => style.ccId === ccId);

    const marketingFlags = isAtRecRemap ? selectedStyle?.ccLevelMarketingFlags?.[0]?.content : selectedProduct?.styleMarketingFlags?.[0];

    const lowResolutionType = 'VI_ONESITE';
    const primaryHighResolutionType = 'VLI';
    const secondaryHighResolutionType = 'P01';

    const primaryImageType = isAtRecRemap ? primaryHighResolutionType : lowResolutionType;

    const selectedImage = isAtRecRemap
      ? selectedStyle?.images.find(image => image.type === primaryImageType) || selectedStyle?.images.find(image => image.type === secondaryHighResolutionType)
      : selectedStyle?.images.find(image => image.type === primaryImageType) || selectedStyle?.images.find(image => image.type === lowResolutionType);

    const shouldUseCCID = enabledFlags?.['product-recommendations-use-ccid'] || false;
    
    return {
      CurrentPrice: selectedStyle?.effectivePrice,
      DetailURL: cid
        ? `/browse/product.do?cid=${cid}&pcid=${pcid}&vid=1&pid=${shouldUseCCID ? selectedStyle.ccId : selectedProduct.styleId}`
        : `/browse/product.do?pcid=${pcid}&vid=1&pid=${shouldUseCCID ? selectedStyle.ccId : selectedProduct.styleId}`,
      ImageURL: `https://www4.assets-gap.com/${selectedImage?.path}`,
      LightWeightImageURL: `https://www4.assets-gap.com/${selectedImage?.path}`,
      MarketingFlag: marketingFlags ?? '',
      OriginalPrice: selectedStyle?.regularPrice,
      ProductName: selectedProduct.styleName,
      PromotionDisplay: '',
      InStock: selectedStyle?.inventoryStatus,
    };
  });
};
export default prepareDataForRecsAggregationServiceAPI;
