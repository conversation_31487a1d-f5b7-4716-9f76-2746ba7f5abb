// @ts-nocheck
'use client';
// pcid: Parent Category ID (this is a root id)
// cid: Category ID (sub category ID)
const prepareDataForRecs = (products, numProducts, pcid, cid) =>
  products.slice(0, numProducts).map(product => ({
    CurrentPrice: product.price.currentMaxPrice,
    DetailURL: cid
      ? `/browse/product.do?cid=${cid}&pcid=${pcid}&vid=1&pid=${product.businessCatalogItemId}`
      : `/browse/product.do?pcid=${pcid}&vid=1&pid=${product.businessCatalogItemId}`,
    ID: product.businessCatalogItemId,
    ImageURL: product.categoryLargeImage.path,
    LightWeightImageURL: product.categoryLargeImage.path,
    MarketingFlag: product.marketingFlag ? product.marketingFlag.marketingFlagName : '',
    OriginalPrice: product.price.regularMaxPrice,
    ProductName: product.name,
    PromotionDisplay: '',
    InStock: product.isInStock,
  }));

export default prepareDataForRecs;
