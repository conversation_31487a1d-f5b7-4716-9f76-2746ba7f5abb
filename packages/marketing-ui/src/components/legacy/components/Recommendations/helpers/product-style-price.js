// @ts-nocheck
'use client';
import { merge } from 'lodash';

const productStylePrice = (productTextStyles, textToBeStyled, strikeThrough = false) => {
  const strikeThroughStyles = strikeThrough ? { textDecoration: 'line-through' } : {};

  if (productTextStyles?.[textToBeStyled]?.style) {
    return merge({}, strikeThroughStyles, productTextStyles[textToBeStyled].style);
  }
  return merge({}, strikeThroughStyles, {});
};

export default productStylePrice;
