// @ts-nocheck
'use client';
import React, { useContext } from 'react';
import { styled, useTheme, getFontWeight, forBrands } from '@ecom-next/core/react-stitch';
import { BreakpointContext, LARGE, XLARGE } from '@ecom-next/core/breakpoint-provider';
import productStylePrice from './product-style-price';

export const RenderTextParagraphAndBRFont = styled.p(() => {
  const theme = useTheme();
  const { brand } = theme;

  const brSpecificFont = forBrands(theme, {
    br: () => theme.brandFontAlt,
    brfs: () => theme.brandFontAlt,
  });

  const brSpecificFontWeight = forBrands(theme, {
    br: () => getFontWeight('regular'),
    brfs: () => getFontWeight('regular'),
  });

  const brBananaSerifFontClassTertiary = !!(brand === 'br' || brand === 'brfs');

  if (brBananaSerifFontClassTertiary) {
    return {
      ...brSpecificFont,
      ...brSpecificFontWeight,
    };
  }
  return {};
});

export const RenderMarketingTextDiv = styled.div(() => {
  const theme = useTheme();

  return {
    marginTop: '0.5rem',
    color: theme.color.gray60,
    overflow: 'hidden',
    textOverflow: 'ellipsis',
    lineHeight: 1.2,
    whiteSpace: 'normal',
    ...theme.brandFont,
    ...getFontWeight('regular'),
  };
});

export const RenderMarketingPara = styled.p(() => {});

export const renderMarketingFlagText = props => {
  const { data, product } = props;
  const { productTextStyles = {} } = data;
  if (product.MarketingFlag) {
    return (
      <RenderMarketingTextDiv css={{ marginTop: '0' }}>
        <RenderMarketingPara
          css={productStylePrice('productMarketingFlag')}
          dangerouslySetInnerHTML={{
            __html: product.MarketingFlag,
          }}
          // recieves data from a Gap API, need to leave dangerouslySetInnerHTML becuase we get HTML elements
          // as part of the response
          style={productStylePrice(productTextStyles, 'productMarketingFlag')}
        />
      </RenderMarketingTextDiv>
    );
  }
  return null;
};

export const getPercentage = (currentPrice, originalPrice) => {
  // have to convert to numbers since they come in from API as strings...

  const numericalOriginalPrice = parseFloat(originalPrice);
  const numericalCurrentPrice = parseFloat(currentPrice);

  if (!(isNaN(numericalOriginalPrice) || isNaN(numericalCurrentPrice))) {
    const percentOff = ((numericalOriginalPrice - numericalCurrentPrice) * 100) / numericalOriginalPrice;
    const roundedPercentOff = Math.round(percentOff);

    return roundedPercentOff;
  }

  return '';
};

export const RenderedPercentageSpan = styled.span(() => ({}));

export const renderedPercentage = props => {
  const { data, product } = props;
  const { productTextStyles = {}, priceOffText } = data;
  const percentage = getPercentage(product.CurrentPrice, product.OriginalPrice);

  if (data.showPercentage && percentage > 0) {
    return (
      <RenderedPercentageSpan style={productStylePrice(productTextStyles, 'productPercentage')}>
        &nbsp;{percentage}% {priceOffText}
      </RenderedPercentageSpan>
    );
  }
  return null;
};

export const getPriceRange = (currentPrice, originalPrice) => {
  // have to convert to numbers since they come in from API as strings...

  const numericalOriginalPrice = parseFloat(originalPrice);
  const numericalCurrentPrice = parseFloat(currentPrice);

  if (!(isNaN(numericalOriginalPrice) || isNaN(numericalCurrentPrice))) {
    const percentOff = ((numericalOriginalPrice - numericalCurrentPrice) * 100) / numericalOriginalPrice;
    const roundedPercentOff = Math.round(percentOff);

    return roundedPercentOff;
  }

  return '';
};

export const renderedRangePriceWithStyles = props => {
  const { data, product } = props;
  const { productTextStyles = {} } = data;
  return (
    <RenderedPercentageSpan style={productStylePrice(productTextStyles, 'productPercentage', true)}>{product.MaxStyleRegularPrice}</RenderedPercentageSpan>
  );
};

export const isOnSale = (currentPrice, originalPrice) => {
  // have to convert to numbers since they come in from API as strings...

  const numericalOriginalPrice = parseFloat(originalPrice);
  const numericalCurrentPrice = parseFloat(currentPrice);

  if (!(isNaN(numericalOriginalPrice) || isNaN(numericalCurrentPrice))) {
    return numericalCurrentPrice < numericalOriginalPrice;
  }

  return false;
};

export const RenderedPriceWrapperDiv = styled.div(() => {
  const theme = useTheme();

  return {
    marginTop: '0.5rem',
    color: theme.color.g2,
    overflow: 'hidden',
    textOverflow: 'ellipsis',
    lineHeight: 1.2,
    whiteSpace: 'normal',
    ...theme.brandFont,
    ...getFontWeight('regular'),
  };
});

export const RenderMarketingCleanDiv = styled.div();

export const RenderMarketingFlagDiv = styled.div(() => ({
  marginTop: 0,
}));

export const RenderedPriceDiv = styled.div(() => {
  const theme = useTheme();

  return {
    marginTop: '0.5rem',
    color: theme.color.gray60,
    overflow: 'hidden',
    textOverflow: 'ellipsis',
    lineHeight: 1.2,
    whiteSpace: 'normal',
    ...theme.brandFont,
    ...getFontWeight('regular'),
  };
});

/* eslint-enable react/no-danger */
export const renderPricing = (props, formatCurrency) => {
  const { product, data } = props;
  const { priceNowText, priceWasText, strikeThroughOriginalPriceFlag, productTextStyles = {} } = data;
  const { CurrentPrice, OriginalPrice, displayRangeMarkdown } = product;
  const hasSale = isOnSale(CurrentPrice, OriginalPrice);

  const formatForDisplay = price => {
    if (price && price.length > 0) {
      return formatCurrency(price);
    }
    return '';
  };

  if (CurrentPrice.length > 0 && OriginalPrice.length > 0 && hasSale && !displayRangeMarkdown) {
    const ariaLabelOriginalPrice = OriginalPrice ? `${priceWasText} ${OriginalPrice}` : '';

    return (
      <RenderedPriceDiv data-testid='price-block'>
        <RenderMarketingCleanDiv
          aria-label={ariaLabelOriginalPrice}
          style={productStylePrice(productTextStyles, 'productPrice', strikeThroughOriginalPriceFlag)}
        >
          {formatForDisplay(OriginalPrice || '')}
        </RenderMarketingCleanDiv>
        {renderedPercentage(props)}
        {CurrentPrice && (
          <RenderMarketingCleanDiv data-testid='price-block-now' style={productStylePrice(productTextStyles, 'productSalePrice')}>
            {priceNowText} {formatForDisplay(CurrentPrice || '')}
          </RenderMarketingCleanDiv>
        )}
      </RenderedPriceDiv>
    );
  }
  if (OriginalPrice.length > 0) {
    return (
      <RenderedPriceDiv data-testid='price-block'>
        <RenderMarketingCleanDiv style={productStylePrice(productTextStyles, 'productPrice')}>{formatForDisplay(OriginalPrice || '')}</RenderMarketingCleanDiv>
      </RenderedPriceDiv>
    );
  }
  return null;
};

export const renderMarkdownRangePricing = (props, formatCurrency) => {
  const { product, data } = props;
  const { productTextStyles = {} } = data;
  const { MaxStyleRegularPrice, CurrentPriceRange, DiscountRange } = product;

  const formatForDisplay = price => {
    if (price && price.length > 0) {
      return formatCurrency(price);
    }
    return '';
  };

  return (
    <RenderedPriceDiv data-testid='price-block-range'>
      {MaxStyleRegularPrice && (
        <RenderedPercentageSpan style={productStylePrice(productTextStyles, 'productPercentage', true)}>
          {formatForDisplay(MaxStyleRegularPrice)}
        </RenderedPercentageSpan>
      )}
      {DiscountRange && (
        <RenderedPercentageSpan style={productStylePrice(productTextStyles, 'productSalePrice')}>
          {MaxStyleRegularPrice && ' '}
          {DiscountRange}
        </RenderedPercentageSpan>
      )}
      {CurrentPriceRange && (
        <RenderMarketingCleanDiv data-testid='price-block-now' style={productStylePrice(productTextStyles, 'productSalePrice')}>
          {CurrentPriceRange || ''}
        </RenderMarketingCleanDiv>
      )}
    </RenderedPriceDiv>
  );
};

export const RenderProductCardInnerDiv = styled.div(() => ({
  padding: '0 0.5rem',
}));

export const RenderProductCardDiv = styled.div(() => {
  const theme = useTheme();
  const { brand } = theme;
  const { minWidth } = useContext(BreakpointContext);
  const isGapBrand = brand === 'gap' || brand === 'gapfs';
  const gapLargeProductCard = minWidth(LARGE) && isGapBrand && { maxWidth: '192px' };
  const gapXLargeProductCard = minWidth(XLARGE) && isGapBrand && { maxWidth: '256px' };

  return {
    ...gapLargeProductCard,
    ...gapXLargeProductCard,
  };
});
