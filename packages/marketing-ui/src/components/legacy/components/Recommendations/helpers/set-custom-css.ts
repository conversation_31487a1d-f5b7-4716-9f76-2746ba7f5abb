// @ts-nocheck
'use client';
import { CSSObject } from '@ecom-next/core/react-stitch';

/**
 * @description Extends react-stitch component styles for marketing-ui components
 * @param fullWidth default: `false`; resets the targeted container to `none`;
 * @param customCss optional style object
 * @returns string value; default: `1920px`
 */
export const setMaxWidth = (fullWidth = false, customCss?: CSSObject): string => {
  if (fullWidth) return 'none';
  if (!fullWidth && customCss?.maxWidth) return `${customCss.maxWidth}`;
  return '1920px';
};
