// @ts-nocheck
'use client';
export default options => {
  const slickArrowStyles = {
    maxWidth: options?.arrowMaxWidth ?? '20px',
  };

  const baseStyling = {
    float: 'none',
    width: '100%',
    margin: '0 auto',
    '.slick-prev': { ...slickArrowStyles },
    '.slick-next': {
      ...slickArrowStyles,
      transform: 'rotate(180deg)',
    },
  };

  return {
    ...options,
    css: [baseStyling],
  };
};
