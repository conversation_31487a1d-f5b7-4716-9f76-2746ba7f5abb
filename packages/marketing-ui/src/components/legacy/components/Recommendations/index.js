// @ts-nocheck
'use client';
import React, { useContext } from 'react';
import PropTypes from 'prop-types';
import { lazy } from 'react';
import { useAppState } from '@ecom-next/sitewide/app-state-provider';
import { BreakpointContext, LARGE } from '@ecom-next/core/breakpoint-provider';
import { LocalizationProvider, normalizeLocale } from '@ecom-next/sitewide/localization-provider';
import CertonaRecs from './CertonaRecs';
import ProdCategory from './ProdCategory';

const DesktopRecs = lazy(() => import('./components/DesktopRecs'));
const MobileRecs = lazy(() => import('./components/MobileRecs'));
const Recommendations = props => {
  const { isLarge } = props;

  return isLarge ? <DesktopRecs {...props} /> : <MobileRecs {...props} />;
};

const GridLayout = lazy(() => import('./GridLayout'));
const MarketingCarousel = lazy(() => import('../../helper/MarketingCarousel'));
const Layout = props => {
  const { layout } = props;
  if (layout && layout === 'grid') {
    return <GridLayout {...props} />;
  }
  return <MarketingCarousel {...props} />;
};

const withLocalTranslation = Component => {
  const WithLocalTranslation = props => {
    const { locale } = useAppState();
    const { ratings } = props.data;
    const { ariaLabel, ariaLabelNoReview, reviewCountSingularLabel, reviewCountPluralLabel } = ratings || {};

    const localTranslations = {
      'en-US': {
        translation: {
          ariaLabel: ariaLabel || 'Image of 5 stars, {{starRating}} are filled, {{reviewCount}} Reviews',
          ariaLabelNoReview: ariaLabelNoReview || 'Image of 5 stars, {{starRating}} are filled',
          reviewCountLabel: reviewCountSingularLabel,
          reviewCountLabel_plural: reviewCountPluralLabel,
        },
      },
      'en-CA': {
        translation: {
          ariaLabel,
          ariaLabelNoReview,
          reviewCountLabel: reviewCountSingularLabel,
          reviewCountLabel_plural: reviewCountPluralLabel,
        },
      },
      'fr-CA': {
        translation: {
          ariaLabel,
          ariaLabelNoReview,
          reviewCountLabel: reviewCountSingularLabel,
          reviewCountLabel_plural: reviewCountPluralLabel,
        },
      },
    };

    return (
      <LocalizationProvider locale={locale} translations={localTranslations[normalizeLocale(locale)].translation} supportNesting>
        <Component {...props} />
      </LocalizationProvider>
    );
  };
  return WithLocalTranslation;
};

const RecommendationWrapper = props => {
  const { minWidth } = useContext(BreakpointContext);
  const dataSources = {
    certona: CertonaRecs,
    productCategory: ProdCategory,
  };

  const DataRecs = withLocalTranslation(dataSources[props.data.source || 'certona']);
  const { paginator } = props.data;
  return (
    <DataRecs
      style={paginator?.showPaginator && { position: 'unset' }}
      {...props}
      isLarge={minWidth(LARGE)}
      Layout={Layout}
      Recommendations={Recommendations}
    />
  );
};

export default RecommendationWrapper;

RecommendationWrapper.propTypes = {
  data: PropTypes.shape({
    source: PropTypes.string.isRequired,
  }).isRequired,
};
