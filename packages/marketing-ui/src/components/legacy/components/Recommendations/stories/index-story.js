// @ts-nocheck
'use client';
import React from 'react';
import { CertonaContext } from '@ecom-next/core/legacy/certona-provider';
import { LocalizationProvider, normalizeLocale } from '@ecom-next/sitewide/localization-provider';
import { Provider as FeatureFlagsProvider } from '@ecom-next/core/legacy/feature-flags';
import { BrandInfoProvider } from '@ecom-next/sitewide/brand-info-provider';
import { Brands } from '@ecom-next/core/react-stitch';
import { checkbox, makeControls, UnsupportedByBrand } from '../../../stories/story-helpers';
import {
  API_KEY,
  certonaContextValue,
  certonaLoadingContextValue,
  powerReviewsResponse,
  productCategoryResponse,
  aggregationServiceResponse,
  atAggregationServiceResponse,
  productStyleResponse,
  URL_WITH_CID,
  AGGREGATION_SERVICE_API_URL_WITH_CID,
  AT_AGGREGATION_SERVICE_API_URL_WITH_CID,
} from '../__fixtures__/response';
import {
  certonaStoryData,
  defaultStoryData,
  paginatorDataBottomLeft,
  paginatorDataBottomRight,
  paginatorDataTopLeft,
  paginatorDataTopRight,
  withAutoplayData,
  withImageStylesStoryData,
  withMarkdownPriceRangeData,
  withRatingsData,
  withAggregationServiceAPIStoryData,
  withAtAggregationServiceAPIStoryData,
  DesktopDraggableSlidesData,
} from '../__fixtures__/story';
import README from '../README.mdx';
import Recommendations from '..';
import { useIsBrand } from '../../../hooks/useBrand';

const withProviders = (storyFn, context) => (
  <div style={{ margin: '1rem 3rem' }}>
    <BrandInfoProvider
      {...{
        brand: 'gap',
        brandCode: 1,
        abbrBrand: 'gap',
        abbrNameForTealium: 'gap',
        market: 'us',
      }}
    >
      <FeatureFlagsProvider value={{ enabledFeatures: {} }}>
        <LocalizationProvider locale='en-US' translations={{}}>
          {storyFn(context)}
        </LocalizationProvider>
      </FeatureFlagsProvider>
    </BrandInfoProvider>
  </div>
);

export default {
  title: 'Common/JSON Components (Marketing)/Recommendations',
  decorators: [withProviders],
  parameters: {
    fetchMock: {
      mocks: [
        {
          matcher: {
            url: /api.gap.com\/ux\/web\/product-style-web-experience/,
          },
          headers: {},
          response: () => productStyleResponse,
        },
        {
          matcher: {
            url: /readservices-b2c.powerreviews.com/,
          },
          headers: {},
          response: () => powerReviewsResponse,
        },
        {
          matcher: {
            url: URL_WITH_CID,
          },
          headers: {
            apiKey: API_KEY,
          },
          response: () => productCategoryResponse,
        },
      ],
    },
    knobs: { disabled: true },
    docs: {
      page: README,
    },
    eyes: { include: false },
  },
  tags: ['exclude'],
};

const getRecommendationControls = makeControls({
  showLoading: checkbox('Show Loading Placeholders', true),
});

const RecommendationsTemplate = args => <Recommendations data={args.data} />;

const CertonaTemplate = args => (
  <CertonaContext.Provider value={args.certonaContextValue}>
    <Recommendations data={args.data} />
  </CertonaContext.Provider>
);

const LoadingPlaceholderTemplate = args => (
  <CertonaContext.Provider value={args.showLoading ? certonaLoadingContextValue : certonaContextValue}>
    <Recommendations data={args.data} />
  </CertonaContext.Provider>
);

export const Default = RecommendationsTemplate.bind({});
Default.args = {
  data: defaultStoryData,
};

export const WithDesktopDraggableSlides = RecommendationsTemplate.bind({});
WithDesktopDraggableSlides.args = {
  data: DesktopDraggableSlidesData,
};

export const WithPaginationTopRight = RecommendationsTemplate.bind({});
WithPaginationTopRight.args = {
  data: paginatorDataTopRight,
};

export const WithPaginationTopLeft = RecommendationsTemplate.bind({});
WithPaginationTopLeft.args = {
  data: paginatorDataTopLeft,
};

export const WithPaginationBottomLeft = RecommendationsTemplate.bind({});
WithPaginationBottomLeft.args = {
  data: paginatorDataBottomRight,
};

export const WithPaginationBottomRight = RecommendationsTemplate.bind({});
WithPaginationBottomRight.args = {
  data: paginatorDataBottomLeft,
};

export const WithAggregationServiceAPICall = RecommendationsTemplate.bind({});

WithAggregationServiceAPICall.args = {
  data: withAggregationServiceAPIStoryData,
};

WithAggregationServiceAPICall.parameters = {
  fetchMock: {
    mocks: [
      {
        matcher: {
          url: AGGREGATION_SERVICE_API_URL_WITH_CID,
        },
        response: () => aggregationServiceResponse,
      },
    ],
  },
};

const ATRecommendationsTemplate = args => {
  return useIsBrand(Brands.Athleta) ? (
    <Recommendations data={args.data} />
  ) : (
    <UnsupportedByBrand customMessage="Story only for AT. For other brands, check 'With Aggregation Service API Call' story" />
  );
};

export const WithATAggregationServiceAPICall = ATRecommendationsTemplate.bind({});

WithATAggregationServiceAPICall.args = {
  data: withAtAggregationServiceAPIStoryData,
};
WithATAggregationServiceAPICall.parameters = {
  fetchMock: {
    mocks: [
      {
        matcher: {
          url: AT_AGGREGATION_SERVICE_API_URL_WITH_CID,
        },
        response: () => atAggregationServiceResponse,
      },
    ],
  },
};

export const CertonaWithMarkdownPriceRangeData = CertonaTemplate.bind({});
CertonaWithMarkdownPriceRangeData.args = {
  data: withMarkdownPriceRangeData,
  certonaContextValue,
};

export const Certona = CertonaTemplate.bind({});
Certona.args = {
  data: certonaStoryData,
  certonaContextValue,
};

export const CertonaWithNumberOfProducts = CertonaTemplate.bind({});
CertonaWithNumberOfProducts.args = {
  data: {
    ...certonaStoryData,
    numberOfProduct: 6,
    gridLayout: {
      ...certonaStoryData.gridLayout,
      productsPerRow: {
        ...certonaStoryData.gridLayout.productsPerRow,
        desktop: 3,
        mobile: 2,
      },
    },
  },
  certonaContextValue,
};

export const WithImageStyles = RecommendationsTemplate.bind({});
WithImageStyles.args = {
  data: withImageStylesStoryData,
};

export const WithAutoplay = RecommendationsTemplate.bind({});
WithAutoplay.args = {
  data: withAutoplayData,
};

WithAutoplay.parameters = {
  eyes: { include: false },
};
WithAutoplay.tags = ['exclude'];

export const WithReviewRatings = CertonaTemplate.bind({});
WithReviewRatings.args = {
  data: withRatingsData,
  certonaContextValue,
};

export const CarouselWithQuickAdd = CertonaTemplate.bind({});
CarouselWithQuickAdd.args = {
  data: { ...withRatingsData, withQuickAddEnabled: true, layout: 'carousel' },
  certonaContextValue,
};

export const GridWithQuickAdd = CertonaTemplate.bind({});
GridWithQuickAdd.args = {
  data: { ...withRatingsData, withQuickAddEnabled: true },
  certonaContextValue,
};

export const WithLoadingPlaceholder = LoadingPlaceholderTemplate.bind({});

WithLoadingPlaceholder.args = {
  data: {
    ...certonaStoryData,
    numberOfProduct: 5,
    placeholderStyles: {
      desktop: {
        flex: 1,
      },
      mobile: {
        flex: 1,
      },
    },
  },
};
WithLoadingPlaceholder.argTypes = {
  ...getRecommendationControls('showLoading'),
};
