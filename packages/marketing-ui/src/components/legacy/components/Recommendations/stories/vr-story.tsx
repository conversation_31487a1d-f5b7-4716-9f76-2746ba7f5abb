// @ts-nocheck
'use client';
import React from 'react';
import { CertonaContext, CertonaProvider } from '@ecom-next/core/legacy/certona-provider';
import { LocalizationProvider, normalizeLocale } from '@ecom-next/sitewide/localization-provider';
import { Provider as FeatureFlagsProvider } from '@ecom-next/core/legacy/feature-flags';
import { BrandInfoProvider } from '@ecom-next/sitewide/brand-info-provider';
import { API_KEY, certonaContextValue, powerReviewsResponse, productCategoryResponse, productStyleResponse, URL_WITH_CID } from '../__fixtures__/response';
import {
  certonaStoryData,
  defaultStoryData,
  paginatorDataBottomLeft,
  paginatorDataBottomRight,
  paginatorDataTopLeft,
  paginatorDataTopRight,
  withAutoplayData,
  withImageStylesStoryData,
  withMarkdownPriceRangeData,
  withRatingsData,
  DesktopDraggableSlidesData,
} from '../__fixtures__/story';
import README from '../README.mdx';
import Recommendations from '..';

const withProviders = (storyFn, context) => (
  <div style={{ margin: '1rem 3rem' }}>
    <BrandInfoProvider
      {...{
        brand: 'gap',
        brandCode: 1,
        abbrBrand: 'gap',
        abbrNameForTealium: 'gap',
        market: 'us',
      }}
    >
      <FeatureFlagsProvider value={{ enabledFeatures: {} }}>
        <LocalizationProvider locale='en-US' translations={{}}>
          {storyFn(context)}
        </LocalizationProvider>
      </FeatureFlagsProvider>
    </BrandInfoProvider>
  </div>
);

export default {
  title: 'Common/JSON Components (Marketing)/Recommendations',
  decorators: [withProviders],
  parameters: {
    fetchMock: {
      mocks: [
        {
          matcher: {
            url: /api.gap.com\/ux\/web\/product-style-web-experience/,
          },
          headers: {},
          response: () => productStyleResponse,
        },
        {
          matcher: {
            url: /readservices-b2c.powerreviews.com/,
          },
          headers: {},
          response: () => powerReviewsResponse,
        },
        {
          matcher: {
            url: URL_WITH_CID,
          },
          headers: {
            apiKey: API_KEY,
          },
          response: () => productCategoryResponse,
        },
      ],
    },
    knobs: { disabled: true },
    docs: {
      page: README,
    },
    eyes: { include: false },
  },
  tags: ['exclude', 'flaky-test'],
};

const RecommendationsTemplate = args => <Recommendations data={args.data} />;

const certonaWithNumberOfProductsData = {
  ...certonaStoryData,
  numberOfProduct: 6,
  gridLayout: {
    ...certonaStoryData.gridLayout,
    productsPerRow: {
      ...certonaStoryData.gridLayout.productsPerRow,
      desktop: 3,
      mobile: 2,
    },
  },
};

const WithLoadingPlaceholderData = {
  ...certonaStoryData,
  numberOfProduct: 5,
  placeholderStyles: {
    desktop: {
      flex: 1,
    },
    mobile: {
      flex: 1,
    },
  },
};

export const VisualRegressionForRecommendations = () => (
  <div
    style={{
      fontSize: '20px',
    }}
  >
    <div style={{ fontSize: 25, fontWeight: 'bold' }}>Visual Regression- Recommendations</div>
    <div style={{ paddingTop: 20 }}>Default Recommendations</div>
    <RecommendationsTemplate data={defaultStoryData} />

    <div style={{ paddingTop: 30 }}>Recommendations with desktop draggable slides</div>
    <RecommendationsTemplate data={DesktopDraggableSlidesData} />
    <div style={{ paddingTop: 30 }}>Recommendations with pagination to right</div>
    <RecommendationsTemplate data={paginatorDataTopRight} />
    <div style={{ paddingTop: 30 }}>Recommendations with pagination to top left</div>
    <RecommendationsTemplate data={paginatorDataTopLeft} />
    <div style={{ paddingTop: 30 }}>Recommendations with pagination to bottom left</div>
    <RecommendationsTemplate data={paginatorDataBottomLeft} />
    <div style={{ paddingTop: 30 }}>Recommendations with pagination to bottom right</div>
    <RecommendationsTemplate data={paginatorDataBottomRight} />
    {/* <div style={{paddingTop: 30}}>Recommendations with aggregationService API call</div>
    <RecommendationsTemplate data={withAggregationServiceAPIStoryData} parameters={withAggregationServiceAPICallParams}/> */}
    <div style={{ paddingTop: 30 }}>Recommendations with certona</div>
    <CertonaContext.Provider value={certonaContextValue}>
      <Recommendations data={certonaStoryData} />
    </CertonaContext.Provider>
    <div style={{ paddingTop: 30 }}>Recommendations with certona markdown price range data</div>
    <CertonaContext.Provider value={certonaContextValue}>
      <Recommendations data={withMarkdownPriceRangeData} />
    </CertonaContext.Provider>
    <div style={{ paddingTop: 30 }}>Recommendations with certona number of produts</div>
    <CertonaContext.Provider value={certonaContextValue}>
      <Recommendations data={certonaWithNumberOfProductsData} />
    </CertonaContext.Provider>
    <div style={{ paddingTop: 30 }}>Recommendations with image styles</div>
    <RecommendationsTemplate data={withImageStylesStoryData} />
    <div style={{ paddingTop: 30 }}>Recommendations with autoplay</div>
    <RecommendationsTemplate data={withAutoplayData} parameters={{ eyes: { include: false } }} tags={['exclude']} />
    <div style={{ paddingTop: 30 }}>Recommendations with review rating</div>
    <CertonaContext.Provider value={certonaContextValue}>
      <Recommendations data={withRatingsData} />
    </CertonaContext.Provider>
    <div style={{ paddingTop: 30 }}>Recommendations with quick add</div>
    <CertonaContext.Provider value={certonaContextValue}>
      <Recommendations
        data={{
          ...withRatingsData,
          withQuickAddEnabled: true,
          layout: 'carousel',
        }}
      />
    </CertonaContext.Provider>
    <div style={{ paddingTop: 30 }}>Recommendations with loading placeholder</div>
    <CertonaContext.Provider value={certonaContextValue}>
      <Recommendations data={WithLoadingPlaceholderData} />
    </CertonaContext.Provider>
    <div style={{ paddingTop: 30 }}>Recommendations with no data</div>
    {/* Using the CertonaProvider simulates a failed certona request since the certona scripts
    aren't loaded and so the certona request never succeeds  */}
    <CertonaProvider errorLogger={() => {}}>
      <Recommendations data={WithLoadingPlaceholderData} />
    </CertonaProvider>
  </div>
);
