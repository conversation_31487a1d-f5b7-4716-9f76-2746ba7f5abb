// @ts-nocheck
'use client';
import { Brands, CSSObject } from '@ecom-next/core/react-stitch';
import { EnabledFeaturesType } from '@ecom-next/core/legacy/feature-flags';

export interface GetCertonaDataScheme {
  scheme: string;
  explanation: string;
  display: string;
  items: GetCertonaDataSchemeItem[];
}

export interface GetCertonaDataSchemes {
  schemes: GetCertonaDataScheme[];
}

export interface GetCertonaDataSchemeItem {
  [key: string]: string;
}

export type GetCertonaRecsProps = {
  schemes: GetCertonaDataSchemes[] | any[] | undefined;
  scheme: string;
  numProducts?: number | string;
};

export interface GenProdPageMapSchemeItem<T> {
  [key: number]: T;
}

export type PaginatorProps = {
  prodPageMap: GenProdPageMapSchemeItem<number>;
  paginatedCurrentSlideIndex: number;
  totalPages: number;
  pageNumberStyles: CSSObject;
  pageNumberAriaText: string;
};

export type AggregationServiceProductProps = {
  styleId: string;
  styleName: string;
  reviewScore: number;
  reviewCount: number;
  styleMarketingFlags: string[];
  webProductType: string;
  excludedFromPromotion: string;
  showSellerName: string;
  freeShipping: string;

  styleColors: {
    ccName: string;
    ccShortDescription: string;
    priceType: string;
    percentageOff: string;
    inventoryCount: number;
    backOrderInventoryCount: number;
    backOrderInventoryStatus: string;
    defaultSizeVariantId: string;
    madeToOrder: string;
    lowestPriceCC: string;
    eligibleReturnLocationType: string;
    ccId: string;
    effectivePrice: string;
    ccMarketingFlags: string[];
    ccLevelMarketingFlags: { content: string; position: string }[];
    regularPrice: string;
    inventoryStatus: string;
    images: {
      type: string;
      path: string;
    }[];
  }[];
};

export type AggregationServiceCCListItem = {
  ccId: string;
  styleId: string;
};

export type AggregationServiceAPIProps = {
  ccList: Array<AggregationServiceCCListItem>;
  products: Array<AggregationServiceProductProps>;
  enabledFlags: EnabledFeaturesType;
  brandName: Brands;
  pcid?: string;
  cid: string;
};
