# RedpointPlaceholder

The Redpoint Placeholder component allows developers to add placeholder content to show while waiting for Redpoint data.

## JSON Configuration

```json
{
  "name": "string",
  "instanceName": "string",
  "type": "string",
  "redpointExperimentRunning": "boolean",
  "data": {
    "defaultHeight": {
      "large": "300px", // string
      "small": "300px",
      "isVisible": {
        "large": true,
        "small": false
      }
    },
    "placeholderSettings": {
      "useGreyLoadingEffect": false, //boolean,
      "desktop": {}, // CSS object,
      "mobile": {} // CSS object",
    }
  }
}
```

### Example Redpoint JSON Payload

```json
{
  "name": "div",
  "instanceName": "my-first-test",
  "type": "builtin",
  "redpointExperimentRunning": true,
  "data": {
    "components": ["Here is a div tag. Use the \"knobs\" tab to adjust the JSON. Default content from MCM"],
    "defaultHeight": {
      "large": "300px", // string
      "small": "300px"
    },
    "isVisible": {
      "large": true,
      "small": false
    },
    "placeholderSettings": {
      "useGreyLoadingEffect": false, //boolean,
      "desktop": {}, // CSS object,
      "mobile": {} // CSS object",
    }
  }
}
```

## Recommendations

Set the default height in the data object for the content that will be injected into the site by Redpoint. If a default height is not set, it will default to 300px and may cause extra whitespace to show, or a jumpy experience for the user as the content loads.

### Implementations without Customization

```js
{
    name: "RedpointPlaceholder",
    type: "sitewide",
    instanceName: "my-redpoint-experiment",
    redpointExperimentRunning: true,
}
```

#### Notes

- The `redpointExperimentRunning` flag can be explictily set to true or false. If set to false the component will not render at all.
