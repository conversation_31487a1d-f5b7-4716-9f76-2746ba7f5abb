// @ts-nocheck
'use client';
import React from 'react';
import { ComponentStory } from '@storybook/react';
import { object } from '@storybook/addon-knobs';
import { Notification as NotificationBase } from '@ecom-next/core/legacy/notification';
import README from './README.mdx';
import RedpointPlaceholder, { RedpointPlaceholderProps } from '.';

export default {
  component: RedpointPlaceholder,
  title: 'Common/JSON Components (Marketing)/RedpointPlaceholder',
  parameters: {
    docs: {
      page: README,
    },
    eyes: { include: false },
  },
  tags: ['exclude'],
};

const data: RedpointPlaceholderProps = {
  type: 'sitewide',
  name: 'RedpointPlaceholder',
  instanceName: 'redpoint-experiment',
  redpointExperimentRunning: true,
  data: {
    defaultHeight: {
      large: '0',
      small: '0',
    },
  },
};

const Notification: typeof NotificationBase = props => <NotificationBase {...props} />;

const RedpointTemplate: ComponentStory<typeof RedpointPlaceholder> = (): JSX.Element => (
  <React.Fragment>
    <Notification kind='information'>
      Redpoint placeholder appears in this canvas as an empty div. We will continue to work on a visual demo that will hook into an actual Redpoint experiment.
      For now, review the README.mdx file in the notes.
    </Notification>
    <RedpointPlaceholder {...object('data', data)} />
  </React.Fragment>
);

export const RedpointDefault = RedpointTemplate.bind({});
RedpointDefault.args = data;
