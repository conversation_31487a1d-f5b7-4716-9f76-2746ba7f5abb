// @ts-nocheck
'use client';
import React from 'react';
import { RedpointPlaceholderProps } from './types';

const RedpointPlaceholder = ({ redpointExperimentRunning }: RedpointPlaceholderProps): JSX.Element | null => {
  if (redpointExperimentRunning) {
    return <div data-testid='redpoint-placeholder' />;
  }
  return null;
};

export type { RedpointPlaceholderProps } from './types';

export default RedpointPlaceholder;
