// @ts-nocheck
import React from 'react';
import { LARGE, SMALL } from '@ecom-next/core/breakpoint-provider';
import { render, screen, RenderOptions, act } from 'test-utils';
import { SignInCta, SignInCtaProps } from '.';

const getSignInCta = (): HTMLElement => screen.getByRole('link', { name: /click to sign in!/i });
const defaultProps = {
  text: 'Click to Sign In!',
};
const renderSignInCta = (props: SignInCtaProps = defaultProps, options: RenderOptions = {}): void => {
  render(<SignInCta {...props} />, options);
};
describe('<SignInCta />', () => {
  const realLocation = window.location;

  beforeAll(() => {
    delete (window as { location?: unknown }).location;
  });
  describe('server side', () => {
    it('should not set target url for window.location not defined', () => {
      renderSignInCta();
      expect(getSignInCta()).toHaveAttribute('href', '/my-account/sign-in?targetURL=http://localhost/');
    });
  });

  describe('client side', () => {
    beforeEach(() => {
      window.location.assign('https://www.gap.com/');
    });

    test('sign in cta', () => {
      renderSignInCta();
      expect(getSignInCta()).toMatchSnapshot();
    });

    it('render element', () => {
      renderSignInCta();
      expect(getSignInCta()).toBeInTheDocument();
      expect(getSignInCta()).toHaveAttribute('href', '/my-account/sign-in?targetURL=https://www.gap.com/');
      expect(getSignInCta()).toHaveStyleRules({
        'font-size': '1em',
        'background-color': 'transparent',
      });
    });

    it('should accept a custom path', () => {
      renderSignInCta({
        ...defaultProps,
        path: '/default-path',
      });
      expect(getSignInCta()).toHaveAttribute('href', '/default-path?targetURL=https://www.gap.com/');
    });

    it('should accept a custom target url', () => {
      renderSignInCta({ ...defaultProps, targetURL: 'target-url' });
      expect(getSignInCta()).toHaveAttribute('href', '/my-account/sign-in?targetURL=target-url');
    });

    it('should accept a custom target url with ? and & escaping them with \\', () => {
      renderSignInCta({
        ...defaultProps,
        targetURL: 'browse/product.do?pid=*********&rrec=true&mlink=5001,1,home_gaphome1_rr_2&clink=1',
      });
      expect(getSignInCta()).toHaveAttribute(
        'href',
        '/my-account/sign-in?targetURL=browse/product.do\\?pid=*********\\&rrec=true\\&mlink=5001,1,home_gaphome1_rr_2\\&clink=1'
      );
    });

    it('should add createAccount when it is true', () => {
      renderSignInCta({ ...defaultProps, createAccount: true });
      expect(getSignInCta()).toHaveAttribute('href', '/my-account/sign-in?targetURL=https://www.gap.com/&createAccount=true');
    });

    it('should not add createAccount when it is false', () => {
      renderSignInCta({ ...defaultProps, createAccount: false });

      expect(getSignInCta()).toHaveAttribute('href', '/my-account/sign-in?targetURL=https://www.gap.com/');
    });

    it('should override styles', () => {
      renderSignInCta({
        ...defaultProps,
        style: {
          fontSize: '14px',
          color: 'blue',
        },
      });

      expect(getSignInCta()).toHaveStyleRules({
        'font-size': '14px',
        color: 'blue',
        'background-color': 'transparent',
      });
    });

    it('should have default styles for desktop', () => {
      renderSignInCta(defaultProps, { breakpoint: LARGE });
      expect(getSignInCta()).toHaveStyleRules({
        'background-color': 'transparent',
        border: '0',
        color: '#FFFFFF',
        display: 'inline-block',
        font: '400 11px',
        'font-size': '1em',
        'margin-bottom': '2px',
        padding: '0.5em',
        'text-decoration': 'underline',
        'text-transform': 'none',
      });
    });

    it('should have default styles for mobile', () => {
      renderSignInCta(defaultProps, { breakpoint: SMALL });
      expect(getSignInCta()).toHaveStyleRules({
        'background-color': 'transparent',
        border: '0',
        color: '#999',
        cursor: 'pointer',
        'font-size': '12px',
        padding: '0.5em',
        'text-decoration': 'underline',
        'text-transform': 'none',
      });
    });
  });

  afterAll(() => {
    window.location = realLocation;
  });
});
