# TextHeadline -- Marketing

## Purpose

A simple component for adding a single line of text. Useful for headers and as a subcomponent.
This component now uses `react-stitch` for styling. See the React Stitch storybook for details.

## How to use

Can be configured through JSON as a subcomponent or standalone component.

### Line breaks

To control line breaks, set a style with `"whiteSpace": "pre-line"` and use `\n` in the text to indicate where the breaks should be. If you only want the lines to break on a particular breakpoint, only add the "whiteSpace" code to that breakpoint.

## Accepted properties

- _style_: container style section. _(Optional)_
  - _mobile_: mobile styles (below 768px)
  - _desktop_: desktop specific styles (above 768px)
- _text_: text to be displayed. _(Required)_
- _defaultHeight_: sets the height of the container with a string value in px. The content will align to the top of the container. _(Optional)_
- _className_: applies Stitch classes to container _(Optional)_
  - _mobile_: mobile classes (below 768px)
  - _desktop_: desktop specific classes (above 768px)

## Example

```json
{
  "name": "TextHeadline",
  "type": "sitewide",
  "data": {
    "text": "Just-in \nPicks \nfor You",
    "defaultHeight": "34px",
    "style": {
      "mobile": {
        "whiteSpace": "pre-line"
      },
      "desktop": {
        "whiteSpace": "pre-line"
      }
    },
    "className": {
      "mobile": "sds_heading-a",
      "desktop": "sds_heading-a"
    }
  }
}
```
