// @ts-nocheck
'use client';
import React from 'react';
import { data } from './__fixtures__';
import README from './README.mdx';
import TextHeadline from '.';

export default {
  title: 'Common/JSON Components (Marketing)/Text Headline',
  component: TextHeadline,
  parameters: {
    docs: {
      page: README,
    },
    eyes: { include: false },
  },
};

const TextHeadlineStory = args => (
  <div style={{ margin: '1vw' }}>
    <TextHeadline {...args} />
  </div>
);

export const Default = TextHeadlineStory.bind({});
Default.args = { data };
