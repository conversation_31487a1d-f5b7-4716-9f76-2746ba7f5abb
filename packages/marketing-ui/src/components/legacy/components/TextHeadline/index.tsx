// @ts-nocheck
'use client';
import React from 'react';
import { BreakpointContext, LARGE } from '@ecom-next/core/breakpoint-provider';
import { TextHeadlineProps } from './types';

const TextHeadline = (props: TextHeadlineProps): JSX.Element | null => {
  const { data } = props;
  const getStyle = (isLarge: boolean) => {
    const style = isLarge ? data.style?.desktop : data.style?.mobile;
    return style;
  };

  const getClassName = (isLarge: boolean) => {
    const className = data.className || {};
    const breakpoint = isLarge ? className.desktop : className.mobile;
    return breakpoint || '';
  };

  return data ? (
    <BreakpointContext.Consumer>
      {({ greaterOrEqualTo }) => (
        <div className={getClassName(greaterOrEqualTo(LARGE))} css={getStyle(greaterOrEqualTo(LARGE))}>
          {data.text}
        </div>
      )}
    </BreakpointContext.Consumer>
  ) : null;
};

export default TextHeadline;
