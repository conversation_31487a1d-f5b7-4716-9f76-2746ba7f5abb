// @ts-nocheck
'use client';
/* Deprecated file */
import React from 'react';
import PropTypes from 'prop-types';
import LinkWithModal from '../LinkWithModal';

const WrapperLinkWithTracking = props => {
  const { href, trackingId, children } = props;
  return (
    <LinkWithModal tid={trackingId} to={href} {...props}>
      {children}
    </LinkWithModal>
  );
};

WrapperLinkWithTracking.defaultProps = {
  href: '',
  trackingId: '',
  children: {},
};

WrapperLinkWithTracking.propTypes = {
  children: PropTypes.node,
  href: PropTypes.string,
  trackingId: PropTypes.string,
};

export default WrapperLinkWithTracking;
