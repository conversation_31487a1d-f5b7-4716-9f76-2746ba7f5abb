// @ts-nocheck
const ctaLinks = [
  {
    href: '/browse/category.do?cid=84117',
    trackingId: 'HP_0911_Women',
    text: 'Women',
  },
  {
    href: '/browse/category.do?cid=84118',
    trackingId: 'HP_0912_Men',
    text: 'Men',
  },
];

const buttonDropdownLinks = [
  {
    heading: { text: 'Women' },
    submenu: [
      { href: 'girls', text: 'Girls' },
      { href: 'toddler girl', text: 'Toddler girl' },
      { href: 'baby girl', text: 'Baby girl' },
    ],
  },
  {
    heading: { text: 'Men' },
    submenu: [
      { href: 'boys', text: 'Boys' },
      { href: 'toddler boy', text: 'Toddler boy' },
      { href: 'baby boy', text: 'Baby boy' },
    ],
  },
];

const textOverlayLinks = {
  lazy: true,
  headline: {
    content: 'New\nright\nnow',
    style: {
      desktop: {
        color: '#FFFFFF',
        fontSize: '4.5vw',
        fontWeight: '300',
        whiteSpace: 'pre-line',
        marginBottom: '1rem',
      },
      mobile: {
        fontSize: '2rem',
        letterSpacing: '1px',
      },
    },
  },
  body: {
    content: 'Fast approaching fall days warrant\n wardrobe updates. On our list?\n Cool-weather classics with collegiate\n vibes and versatile appeal.',
    style: {
      desktop: {
        color: '#FFFFFF',
        fontSize: '1rem',
        whiteSpace: 'pre-line',
      },
      mobile: {
        fontSize: '4vw',
        paddingBottom: '1.25rem',
      },
    },
  },
  links: {
    content: ctaLinks,
    buttonClasses: 'sds_btn--underline',
    style: {
      desktop: {},
      mobile: {},
    },
  },
  textContainerStyle: {
    desktop: {
      top: '5%',
      left: '27%',
      bottom: 'auto',
      right: 'auto',
    },
  },
  containerStyle: {
    color: '#FFFFFF',
    maxWidth: '1350px',
  },
  image: {
    smallImg: 'static/media/background-mobile.b7c76761.jpg',
    xlargeImg: 'static/media/background.be7c0d50.jpg',
    alt: 'new right now',
  },
};

const textOverlayButtonDropdowns = {
  lazy: true,
  headline: {
    content: 'New\nright\nnow',
    style: {
      desktop: {
        color: '#FFFFFF',
        fontSize: '4.5vw',
        fontWeight: '300',
        whiteSpace: 'pre-line',
        marginBottom: '1rem',
      },
      mobile: {
        fontSize: '2rem',
        letterSpacing: '1px',
      },
    },
  },
  body: {
    content: 'Fast approaching fall days warrant\n wardrobe updates. On our list?\n Cool-weather classics with collegiate\n vibes and versatile appeal.',
    style: {
      desktop: {
        color: '#FFFFFF',
        fontSize: '1rem',
        whiteSpace: 'pre-line',
      },
      mobile: {
        fontSize: '4vw',
        paddingBottom: '1.25rem',
      },
    },
  },
  links: {
    type: 'dropdown',
    content: buttonDropdownLinks,
    buttonClasses: 'sds_btn--underline',
    style: {
      desktop: {},
      mobile: {},
    },
  },
  textContainerStyle: {
    desktop: {
      top: '5%',
      left: '27%',
      bottom: 'auto',
      right: 'auto',
    },
  },
  containerStyle: {
    color: '#FFFFFF',
    maxWidth: '1350px',
  },
  image: {
    smallImg: 'static/media/background-mobile.b7c76761.jpg',
    xlargeImg: 'static/media/background.be7c0d50.jpg',
    alt: 'new right now',
  },
};

export { textOverlayLinks, textOverlayButtonDropdowns };
