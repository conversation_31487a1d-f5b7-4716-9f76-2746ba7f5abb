// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`TextOverlay should match snapshot for desktop in "at" 1`] = `
.emotion-0 {
  position: relative;
  width: 100%;
  margin: 0 auto 4rem;
  text-align: left;
  color: #FFFFFF;
  max-width: 1350px;
}

.emotion-0 * {
  box-sizing: border-box;
}

.emotion-2 {
  width: 100%;
}

@media (min-width: 768px) {
  .emotion-3 {
    margin: 0;
    padding: 0 0.5rem;
    position: absolute;
    top: 0;
    white-space: nowrap;
    width: auto;
  }
}

@media (max-width: 768px) {
  .emotion-3 {
    position: absolute;
  }
}

.emotion-5 {
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  font-weight: 600;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 4rem;
  letter-spacing: 1.5px;
  line-height: 1;
  margin-bottom: 2rem;
  text-transform: uppercase;
  color: #FFFFFF;
  font-size: 4.5vw;
  font-weight: 300;
  white-space: pre-line;
  margin-bottom: 1rem;
}

.emotion-6 {
  font-family: var(--font-phantom-sans),Helvetica,Arial,Robot<PERSON>,sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 0.875rem;
  line-height: 1.43;
  margin-bottom: 1.25rem;
  color: #FFFFFF;
  font-size: 1rem;
  white-space: pre-line;
}

.emotion-7 {
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
}

.emotion-8 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-color: #FFFFFF;
  border-radius: 0;
  border-style: solid;
  border-width: 0;
  box-sizing: border-box;
  cursor: pointer;
  display: inline-block;
  font-size: 10px;
  font-weight: 700;
  line-height: normal;
  margin: 0;
  padding: 0 12px;
  text-align: center;
  text-transform: uppercase;
  vertical-align: middle;
  white-space: nowrap;
}

.emotion-8:first-of-type {
  padding-left: 0;
}

.emotion-8:not(:first-of-type) {
  border-width: 0 0 0 1px;
}

<div
  class="emotion-0"
  data-testid="text-overlay-container"
>
  <a
    class="emotion-1"
    href="/browse/category.do?cid=11111"
    target="_self"
  >
    <div
      data-testid="background-image"
      role="button"
      tabindex="-1"
    >
      <img
        alt="new right now"
        class=" emotion-2"
        src="static/media/background.be7c0d50.jpg"
      />
    </div>
  </a>
  <div
    class=" emotion-3"
  >
    <div
      class="emotion-1"
    >
      <div
        class="emotion-5"
      >
        New
right
now
      </div>
    </div>
    <div
      class="emotion-6"
    >
      Fast approaching fall days warrant
 wardrobe updates. On our list?
 Cool-weather classics with collegiate
 vibes and versatile appeal.
    </div>
    <div
      class="emotion-7"
    >
      <a
        class="emotion-8"
        href="/browse/category.do?cid=11111"
        target="_self"
      >
        Link text one
      </a>
      <a
        class="emotion-8"
        href="/browse/category.do?cid=22222"
        target="_self"
      >
        Link text two
      </a>
    </div>
  </div>
</div>
`;

exports[`TextOverlay should match snapshot for desktop in "at" 2`] = `
.emotion-0 {
  position: relative;
  width: 100%;
  margin: 0 auto 4rem;
  text-align: left;
  color: #FFFFFF;
  max-width: 1350px;
}

.emotion-0 * {
  box-sizing: border-box;
}

.emotion-2 {
  width: 100%;
}

@media (min-width: 768px) {
  .emotion-3 {
    margin: 0;
    padding: 0 0.5rem;
    position: absolute;
    top: 0;
    white-space: nowrap;
    width: auto;
  }
}

@media (max-width: 768px) {
  .emotion-3 {
    position: absolute;
  }
}

.emotion-5 {
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  font-weight: 600;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 4rem;
  letter-spacing: 1.5px;
  line-height: 1;
  margin-bottom: 2rem;
  text-transform: uppercase;
  font-size: 2rem;
  letter-spacing: 1px;
}

.emotion-6 {
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 0.875rem;
  line-height: 1.43;
  margin-bottom: 1.25rem;
  font-size: 4vw;
  padding-bottom: 1.25rem;
}

.emotion-7 {
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
}

.emotion-8 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-color: #FFFFFF;
  border-radius: 0;
  border-style: solid;
  border-width: 0;
  box-sizing: border-box;
  cursor: pointer;
  display: inline-block;
  font-size: 10px;
  font-weight: 700;
  line-height: normal;
  margin: 0;
  padding: 0 12px;
  text-align: center;
  text-transform: uppercase;
  vertical-align: middle;
  white-space: nowrap;
}

.emotion-8:first-of-type {
  padding-left: 0;
}

.emotion-8:not(:first-of-type) {
  border-width: 0 0 0 1px;
}

<div
  class="emotion-0"
  data-testid="text-overlay-container"
>
  <a
    class="emotion-1"
    href="/browse/category.do?cid=11111"
    target="_self"
  >
    <div
      data-testid="background-image"
      role="button"
      tabindex="-1"
    >
      <img
        alt="new right now"
        class=" emotion-2"
        src="static/media/background-mobile.b7c76761.jpg"
      />
    </div>
  </a>
  <div
    class=" emotion-3"
  >
    <div
      class="emotion-1"
    >
      <div
        class="emotion-5"
      >
        New
right
now
      </div>
    </div>
    <div
      class="emotion-6"
    >
      Fast approaching fall days warrant
 wardrobe updates. On our list?
 Cool-weather classics with collegiate
 vibes and versatile appeal.
    </div>
    <div
      class="emotion-7"
    >
      <a
        class="emotion-8"
        href="/browse/category.do?cid=11111"
        target="_self"
      >
        Link text one
      </a>
      <a
        class="emotion-8"
        href="/browse/category.do?cid=22222"
        target="_self"
      >
        Link text two
      </a>
    </div>
  </div>
</div>
`;

exports[`TextOverlay should match snapshot for desktop in "at" 3`] = `
.emotion-0 {
  position: relative;
  width: 100%;
  margin: 0 auto 4rem;
  text-align: left;
  color: #FFFFFF;
  max-width: 1350px;
}

.emotion-0 * {
  box-sizing: border-box;
}

.emotion-2 {
  width: 100%;
}

@media (min-width: 768px) {
  .emotion-3 {
    margin: 0;
    padding: 0 0.5rem;
    position: absolute;
    top: 0;
    white-space: nowrap;
    width: auto;
  }
}

@media (max-width: 768px) {
  .emotion-3 {
    position: absolute;
  }
}

.emotion-5 {
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  font-weight: 600;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 4rem;
  letter-spacing: 1.5px;
  line-height: 1;
  margin-bottom: 2rem;
  text-transform: uppercase;
  color: #FFFFFF;
  font-size: 4.5vw;
  font-weight: 300;
  white-space: pre-line;
  margin-bottom: 1rem;
}

.emotion-6 {
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 0.875rem;
  line-height: 1.43;
  margin-bottom: 1.25rem;
  color: #FFFFFF;
  font-size: 1rem;
  white-space: pre-line;
}

.emotion-7 {
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
}

.emotion-8 {
  font-size: 0.9735rem;
  width: 100%;
}

.emotion-8:not(:first-child) {
  margin-left: none;
}

.emotion-9 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: none;
  transition: none;
  box-sizing: border-box;
  border: 1.5px solid transparent;
  border-radius: 0;
  font-size: 16px;
  font-weight: 500;
  letter-spacing: 0px;
  min-height: auto;
  max-height: 60px;
  line-height: 1.25;
  padding: 22px 40px;
  width: 100%;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #000000;
  background: transparent;
  border-color: #000000;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  width: 100%;
  text-align: center;
  box-sizing: border-box;
  color: #FFFFFF;
  background-color: #000000;
  height: 60px;
  font-size: 16px;
}

.emotion-9:focus {
  outline: none;
}

.emotion-9>span {
  padding: 1px 0;
}

.emotion-9 svg path {
  fill: #FFFFFF!important;
}

.emotion-9:hover {
  -webkit-text-decoration: underline;
  text-decoration: underline;
}

.emotion-9>span {
  padding: 0;
}

.emotion-11 {
  display: inline-block;
  height: 10px;
  width: 10px;
  min-height: 10px;
  min-width: 10px;
  margin-left: 0.65rem;
  padding-top: 2.4px;
}

.emotion-11 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-11 svg path {
  fill: inherit;
}

.emotion-11 svg rect {
  fill: inherit;
}

.emotion-12 {
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
  background: #FFFFFF;
  box-sizing: border-box;
  list-style-type: none;
  min-width: 100%;
  overflow: hidden;
  position: relative;
  z-index: 390;
  border-width: 0;
  border-style: solid;
  text-align: left;
  letter-spacing: 0;
  pointer-events: auto;
  padding: 0;
  max-height: 0;
  -webkit-transition: max-height .5s ease-out,visibility 0s .5s;
  transition: max-height .5s ease-out,visibility 0s .5s;
  visibility: hidden;
}

.emotion-13 {
  box-sizing: border-box;
  width: 100%;
  border-bottom: 1px solid #000000;
  border-left: 1px solid #333333;
  border-right: 1px solid #333333;
  height: 60px;
}

.emotion-13:last-child {
  border-bottom: 1px solid #333333;
}

.emotion-14 {
  cursor: pointer;
  display: block;
  padding: 1.375rem 0;
  -webkit-text-decoration: none;
  text-decoration: none;
  -webkit-transition: color 0.3s ease;
  transition: color 0.3s ease;
  white-space: nowrap;
  font-size: 16px;
  text-align: center;
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-14:hover,
.emotion-14:focus {
  -webkit-text-decoration: underline;
  text-decoration: underline;
}

<div
  class="emotion-0"
  data-testid="text-overlay-container"
>
  <a
    class="emotion-1"
    href="/browse/category.do?cid=11111"
    target="_self"
  >
    <div
      data-testid="background-image"
      role="button"
      tabindex="-1"
    >
      <img
        alt="new right now"
        class=" emotion-2"
        src="static/media/background.be7c0d50.jpg"
      />
    </div>
  </a>
  <div
    class=" emotion-3"
  >
    <div
      class="emotion-1"
    >
      <div
        class="emotion-5"
      >
        New
right
now
      </div>
    </div>
    <div
      class="emotion-6"
    >
      Fast approaching fall days warrant
 wardrobe updates. On our list?
 Cool-weather classics with collegiate
 vibes and versatile appeal.
    </div>
    <div
      class="emotion-7"
    >
      <div
        class="emotion-8"
        data-testid="button-dropdown-container"
      >
        <button
          aria-expanded="false"
          class="emotion-9"
          color="primary"
        >
          <span
            class="emotion-1"
          >
            Shop Halloween styles
          </span>
          <span
            aria-hidden="true"
            class="emotion-11"
          >
            <svg
              viewBox="0 0 12.87 13"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M5.43 0h2v13h-2z"
              />
              <path
                d="M12.87 5.5v2H0v-2z"
              />
            </svg>
          </span>
        </button>
        <ul
          aria-hidden="true"
          class="emotion-12"
        >
          <li
            class="emotion-13"
          >
            <a
              breakpoint="desktop"
              class="emotion-14"
              href="girls"
              target="_self"
            >
              Girls
            </a>
          </li>
          <li
            class="emotion-13"
          >
            <a
              breakpoint="desktop"
              class="emotion-14"
              href="boys"
              target="_self"
            >
              Boys
            </a>
          </li>
          <li
            class="emotion-13"
          >
            <a
              breakpoint="desktop"
              class="emotion-14"
              href="toddler girl"
              target="_self"
            >
              Toddler girl
            </a>
          </li>
          <li
            class="emotion-13"
          >
            <a
              breakpoint="desktop"
              class="emotion-14"
              href="toddler boy"
              target="_self"
            >
              Toddler boy
            </a>
          </li>
          <li
            class="emotion-13"
          >
            <a
              breakpoint="desktop"
              class="emotion-14"
              href="baby boy"
              target="_self"
            >
              Baby boy
            </a>
          </li>
        </ul>
      </div>
      <div
        class="emotion-8"
        data-testid="button-dropdown-container"
      >
        <button
          aria-expanded="false"
          class="emotion-9"
          color="primary"
        >
          <span
            class="emotion-1"
          />
          <span
            aria-hidden="true"
            class="emotion-11"
          >
            <svg
              viewBox="0 0 12.87 13"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M5.43 0h2v13h-2z"
              />
              <path
                d="M12.87 5.5v2H0v-2z"
              />
            </svg>
          </span>
        </button>
        <ul
          aria-hidden="true"
          class="emotion-12"
        />
      </div>
    </div>
  </div>
</div>
`;

exports[`TextOverlay should match snapshot for desktop in "at" 4`] = `
.emotion-0 {
  position: relative;
  width: 100%;
  margin: 0 auto 4rem;
  text-align: left;
  color: #FFFFFF;
  max-width: 1350px;
}

.emotion-0 * {
  box-sizing: border-box;
}

.emotion-2 {
  width: 100%;
}

@media (min-width: 768px) {
  .emotion-3 {
    margin: 0;
    padding: 0 0.5rem;
    position: absolute;
    top: 0;
    white-space: nowrap;
    width: auto;
  }
}

@media (max-width: 768px) {
  .emotion-3 {
    position: absolute;
  }
}

.emotion-5 {
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  font-weight: 600;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 4rem;
  letter-spacing: 1.5px;
  line-height: 1;
  margin-bottom: 2rem;
  text-transform: uppercase;
  font-size: 2rem;
  letter-spacing: 1px;
}

.emotion-6 {
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 0.875rem;
  line-height: 1.43;
  margin-bottom: 1.25rem;
  font-size: 4vw;
  padding-bottom: 1.25rem;
}

.emotion-7 {
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
}

.emotion-8 {
  font-size: 0.9735rem;
  width: 100%;
}

.emotion-9 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: none;
  transition: none;
  box-sizing: border-box;
  border: 1.5px solid transparent;
  border-radius: 0;
  font-size: 16px;
  font-weight: 500;
  letter-spacing: 0px;
  min-height: auto;
  max-height: 60px;
  line-height: 1.25;
  padding: 22px 40px;
  width: 100%;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #000000;
  background: transparent;
  border-color: #000000;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  width: 100%;
  text-align: center;
  box-sizing: border-box;
  color: #FFFFFF;
  background-color: #000000;
  height: 50px;
  font-size: 14px;
}

.emotion-9:focus {
  outline: none;
}

.emotion-9>span {
  padding: 1px 0;
}

.emotion-9 svg path {
  fill: #FFFFFF!important;
}

.emotion-9:hover {
  -webkit-text-decoration: underline;
  text-decoration: underline;
}

.emotion-9>span {
  padding: 0;
}

.emotion-11 {
  display: inline-block;
  height: 10px;
  width: 10px;
  min-height: 10px;
  min-width: 10px;
  margin-left: 0.65rem;
  padding-top: 2.4px;
}

.emotion-11 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-11 svg path {
  fill: inherit;
}

.emotion-11 svg rect {
  fill: inherit;
}

.emotion-12 {
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
  background: #FFFFFF;
  box-sizing: border-box;
  list-style-type: none;
  min-width: 100%;
  overflow: hidden;
  position: relative;
  z-index: 390;
  border-width: 0;
  border-style: solid;
  text-align: left;
  letter-spacing: 0;
  pointer-events: auto;
  padding: 0;
  max-height: 0;
  -webkit-transition: max-height .5s ease-out,visibility 0s .5s;
  transition: max-height .5s ease-out,visibility 0s .5s;
  visibility: hidden;
}

.emotion-13 {
  box-sizing: border-box;
  width: auto;
  border-bottom: 1px solid #000000;
  border-left: 1px solid #333333;
  border-right: 1px solid #333333;
  height: 50px;
}

.emotion-13:last-child {
  border-bottom: 1px solid #333333;
}

.emotion-14 {
  cursor: pointer;
  display: block;
  padding: 1.125rem 0;
  -webkit-text-decoration: none;
  text-decoration: none;
  -webkit-transition: color 0.3s ease;
  transition: color 0.3s ease;
  white-space: nowrap;
  font-size: 14px;
  text-align: center;
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-14:hover,
.emotion-14:focus {
  -webkit-text-decoration: underline;
  text-decoration: underline;
}

<div
  class="emotion-0"
  data-testid="text-overlay-container"
>
  <a
    class="emotion-1"
    href="/browse/category.do?cid=11111"
    target="_self"
  >
    <div
      data-testid="background-image"
      role="button"
      tabindex="-1"
    >
      <img
        alt="new right now"
        class=" emotion-2"
        src="static/media/background-mobile.b7c76761.jpg"
      />
    </div>
  </a>
  <div
    class=" emotion-3"
  >
    <div
      class="emotion-1"
    >
      <div
        class="emotion-5"
      >
        New
right
now
      </div>
    </div>
    <div
      class="emotion-6"
    >
      Fast approaching fall days warrant
 wardrobe updates. On our list?
 Cool-weather classics with collegiate
 vibes and versatile appeal.
    </div>
    <div
      class="emotion-7"
    >
      <div
        class="emotion-8"
        data-testid="button-dropdown-container"
      >
        <button
          aria-expanded="false"
          class="emotion-9"
          color="primary"
        >
          <span
            class="emotion-1"
          >
            Shop Halloween styles
          </span>
          <span
            aria-hidden="true"
            class="emotion-11"
          >
            <svg
              viewBox="0 0 12.87 13"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M5.43 0h2v13h-2z"
              />
              <path
                d="M12.87 5.5v2H0v-2z"
              />
            </svg>
          </span>
        </button>
        <ul
          aria-hidden="true"
          class="emotion-12"
        >
          <li
            class="emotion-13"
          >
            <a
              breakpoint="mobile"
              class="emotion-14"
              href="girls"
              target="_self"
            >
              Girls
            </a>
          </li>
          <li
            class="emotion-13"
          >
            <a
              breakpoint="mobile"
              class="emotion-14"
              href="boys"
              target="_self"
            >
              Boys
            </a>
          </li>
          <li
            class="emotion-13"
          >
            <a
              breakpoint="mobile"
              class="emotion-14"
              href="toddler girl"
              target="_self"
            >
              Toddler girl
            </a>
          </li>
          <li
            class="emotion-13"
          >
            <a
              breakpoint="mobile"
              class="emotion-14"
              href="toddler boy"
              target="_self"
            >
              Toddler boy
            </a>
          </li>
          <li
            class="emotion-13"
          >
            <a
              breakpoint="mobile"
              class="emotion-14"
              href="baby boy"
              target="_self"
            >
              Baby boy
            </a>
          </li>
        </ul>
      </div>
      <div
        class="emotion-8"
        data-testid="button-dropdown-container"
      >
        <button
          aria-expanded="false"
          class="emotion-9"
          color="primary"
        >
          <span
            class="emotion-1"
          />
          <span
            aria-hidden="true"
            class="emotion-11"
          >
            <svg
              viewBox="0 0 12.87 13"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M5.43 0h2v13h-2z"
              />
              <path
                d="M12.87 5.5v2H0v-2z"
              />
            </svg>
          </span>
        </button>
        <ul
          aria-hidden="true"
          class="emotion-12"
        />
      </div>
    </div>
  </div>
</div>
`;

exports[`TextOverlay should match snapshot for desktop in "br" 1`] = `
.emotion-0 {
  position: relative;
  width: 100%;
  margin: 0 auto 4rem;
  text-align: left;
  color: #FFFFFF;
  max-width: 1350px;
}

.emotion-0 * {
  box-sizing: border-box;
}

.emotion-1 {
  margin-right: 0.75rem;
}

.emotion-1:last-child {
  margin-right: 0;
}

@media (max-width: 768px) {
  .emotion-1 {
    border: 1px solid #ccc;
    font-size: 2.8vw;
    letter-spacing: 3px;
    line-height: 1;
    padding: 8px;
    text-transform: uppercase;
    width: 100%;
  }
}

@media (min-width: 1024px) {
  .emotion-1 {
    font-size: 0.75rem;
  }
}

.emotion-2 {
  position: relative;
  z-index: -50;
}

.emotion-3 {
  width: 100%;
}

.emotion-4 {
  bottom: 0;
  left: 0;
  padding: 0 0.5rem 1rem 0.5rem;
  position: absolute;
  width: 100%;
}

@media (min-width: 768px) {
  .emotion-4 {
    display: none;
  }
}

.emotion-5 {
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  font-weight: 600;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 4rem;
  letter-spacing: 1.5px;
  line-height: 1;
  margin-bottom: 2rem;
  text-transform: uppercase;
  font-size: 2rem;
  letter-spacing: 1px;
}

@media (min-width: 768px) {
  .emotion-6 {
    margin: 0;
    padding: 0 0.5rem;
    position: absolute;
    top: 0;
    white-space: nowrap;
    width: auto;
  }
}

@media (max-width: 768px) {
  .emotion-7 {
    display: none;
  }
}

.emotion-8 {
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  font-weight: 600;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 4rem;
  letter-spacing: 1.5px;
  line-height: 1;
  margin-bottom: 2rem;
  text-transform: uppercase;
  color: #FFFFFF;
  font-size: 4.5vw;
  font-weight: 300;
  white-space: pre-line;
  margin-bottom: 1rem;
}

.emotion-9 {
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 0.875rem;
  line-height: 1.43;
  margin-bottom: 1.25rem;
  color: #FFFFFF;
  font-size: 1rem;
  white-space: pre-line;
}

.emotion-10 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

@media (min-width: 768px) {
  .emotion-10 {
    display: -webkit-inline-box;
    display: -webkit-inline-flex;
    display: -ms-inline-flexbox;
    display: inline-flex;
  }
}

.emotion-11 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-color: #FFFFFF;
  border-radius: 0;
  border-style: solid;
  border-width: 0;
  box-sizing: border-box;
  cursor: pointer;
  display: inline-block;
  font-size: 10px;
  font-weight: 700;
  line-height: normal;
  margin: 0;
  padding: 0 12px;
  text-align: center;
  text-transform: uppercase;
  vertical-align: middle;
  white-space: nowrap;
}

.emotion-11:first-of-type {
  padding-left: 0;
}

.emotion-11:not(:first-of-type) {
  border-width: 0 0 0 1px;
}

<div
  class="emotion-0"
  data-testid="text-overlay-container"
>
  <a
    class="emotion-1"
    href="/browse/category.do?cid=11111"
    target="_self"
  >
    <div
      class="emotion-2"
    >
      <div
        data-testid="background-image"
        role="button"
        tabindex="-1"
      >
        <img
          alt="new right now"
          class=" emotion-3"
          src="static/media/background.be7c0d50.jpg"
        />
      </div>
      <div
        class="emotion-4"
      >
        <div
          class="emotion-5"
        >
          New
right
now
        </div>
      </div>
    </div>
  </a>
  <div
    class=" emotion-6"
  >
    <div
      class="emotion-7"
    >
      <div
        class="emotion-8"
      >
        New
right
now
      </div>
    </div>
    <div
      class="emotion-9"
    >
      Fast approaching fall days warrant
 wardrobe updates. On our list?
 Cool-weather classics with collegiate
 vibes and versatile appeal.
    </div>
    <div
      class="emotion-10"
    >
      <a
        class="emotion-11"
        href="/browse/category.do?cid=11111"
        target="_self"
      >
        Link text one
      </a>
      <a
        class="emotion-11"
        href="/browse/category.do?cid=22222"
        target="_self"
      >
        Link text two
      </a>
    </div>
  </div>
</div>
`;

exports[`TextOverlay should match snapshot for desktop in "br" 2`] = `
.emotion-0 {
  position: relative;
  width: 100%;
  margin: 0 auto 4rem;
  text-align: left;
  color: #FFFFFF;
  max-width: 1350px;
}

.emotion-0 * {
  box-sizing: border-box;
}

.emotion-1 {
  margin-right: 0.75rem;
}

.emotion-1:last-child {
  margin-right: 0;
}

@media (max-width: 768px) {
  .emotion-1 {
    border: 1px solid #ccc;
    font-size: 2.8vw;
    letter-spacing: 3px;
    line-height: 1;
    padding: 8px;
    text-transform: uppercase;
    width: 100%;
  }
}

@media (min-width: 1024px) {
  .emotion-1 {
    font-size: 0.75rem;
  }
}

.emotion-2 {
  position: relative;
  z-index: -50;
}

.emotion-3 {
  width: 100%;
}

.emotion-4 {
  bottom: 0;
  left: 0;
  padding: 0 0.5rem 1rem 0.5rem;
  position: absolute;
  width: 100%;
}

@media (min-width: 768px) {
  .emotion-4 {
    display: none;
  }
}

.emotion-5 {
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  font-weight: 600;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 4rem;
  letter-spacing: 1.5px;
  line-height: 1;
  margin-bottom: 2rem;
  text-transform: uppercase;
  font-size: 2rem;
  letter-spacing: 1px;
}

@media (min-width: 768px) {
  .emotion-6 {
    margin: 0;
    padding: 0 0.5rem;
    position: absolute;
    top: 0;
    white-space: nowrap;
    width: auto;
  }
}

@media (max-width: 768px) {
  .emotion-7 {
    display: none;
  }
}

.emotion-8 {
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  font-weight: 600;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 4rem;
  letter-spacing: 1.5px;
  line-height: 1;
  margin-bottom: 2rem;
  text-transform: uppercase;
  font-size: 2rem;
  letter-spacing: 1px;
}

.emotion-9 {
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 0.875rem;
  line-height: 1.43;
  margin-bottom: 1.25rem;
  font-size: 4vw;
  padding-bottom: 1.25rem;
}

.emotion-10 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

@media (min-width: 768px) {
  .emotion-10 {
    display: -webkit-inline-box;
    display: -webkit-inline-flex;
    display: -ms-inline-flexbox;
    display: inline-flex;
  }
}

.emotion-11 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-color: #FFFFFF;
  border-radius: 0;
  border-style: solid;
  border-width: 0;
  box-sizing: border-box;
  cursor: pointer;
  display: inline-block;
  font-size: 10px;
  font-weight: 700;
  line-height: normal;
  margin: 0;
  padding: 0 12px;
  text-align: center;
  text-transform: uppercase;
  vertical-align: middle;
  white-space: nowrap;
}

.emotion-11:first-of-type {
  padding-left: 0;
}

.emotion-11:not(:first-of-type) {
  border-width: 0 0 0 1px;
}

<div
  class="emotion-0"
  data-testid="text-overlay-container"
>
  <a
    class="emotion-1"
    href="/browse/category.do?cid=11111"
    target="_self"
  >
    <div
      class="emotion-2"
    >
      <div
        data-testid="background-image"
        role="button"
        tabindex="-1"
      >
        <img
          alt="new right now"
          class=" emotion-3"
          src="static/media/background-mobile.b7c76761.jpg"
        />
      </div>
      <div
        class="emotion-4"
      >
        <div
          class="emotion-5"
        >
          New
right
now
        </div>
      </div>
    </div>
  </a>
  <div
    class=" emotion-6"
  >
    <div
      class="emotion-7"
    >
      <div
        class="emotion-8"
      >
        New
right
now
      </div>
    </div>
    <div
      class="emotion-9"
    >
      Fast approaching fall days warrant
 wardrobe updates. On our list?
 Cool-weather classics with collegiate
 vibes and versatile appeal.
    </div>
    <div
      class="emotion-10"
    >
      <a
        class="emotion-11"
        href="/browse/category.do?cid=11111"
        target="_self"
      >
        Link text one
      </a>
      <a
        class="emotion-11"
        href="/browse/category.do?cid=22222"
        target="_self"
      >
        Link text two
      </a>
    </div>
  </div>
</div>
`;

exports[`TextOverlay should match snapshot for desktop in "br" 3`] = `
.emotion-0 {
  position: relative;
  width: 100%;
  margin: 0 auto 4rem;
  text-align: left;
  color: #FFFFFF;
  max-width: 1350px;
}

.emotion-0 * {
  box-sizing: border-box;
}

.emotion-1 {
  margin-right: 0.75rem;
}

.emotion-1:last-child {
  margin-right: 0;
}

@media (max-width: 768px) {
  .emotion-1 {
    border: 1px solid #ccc;
    font-size: 2.8vw;
    letter-spacing: 3px;
    line-height: 1;
    padding: 8px;
    text-transform: uppercase;
    width: 100%;
  }
}

@media (min-width: 1024px) {
  .emotion-1 {
    font-size: 0.75rem;
  }
}

.emotion-2 {
  position: relative;
  z-index: -50;
}

.emotion-3 {
  width: 100%;
}

.emotion-4 {
  bottom: 0;
  left: 0;
  padding: 0 0.5rem 1rem 0.5rem;
  position: absolute;
  width: 100%;
}

@media (min-width: 768px) {
  .emotion-4 {
    display: none;
  }
}

.emotion-5 {
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  font-weight: 600;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 4rem;
  letter-spacing: 1.5px;
  line-height: 1;
  margin-bottom: 2rem;
  text-transform: uppercase;
  font-size: 2rem;
  letter-spacing: 1px;
}

@media (min-width: 768px) {
  .emotion-6 {
    margin: 0;
    padding: 0 0.5rem;
    position: absolute;
    top: 0;
    white-space: nowrap;
    width: auto;
  }
}

@media (max-width: 768px) {
  .emotion-7 {
    display: none;
  }
}

.emotion-8 {
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  font-weight: 600;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 4rem;
  letter-spacing: 1.5px;
  line-height: 1;
  margin-bottom: 2rem;
  text-transform: uppercase;
  color: #FFFFFF;
  font-size: 4.5vw;
  font-weight: 300;
  white-space: pre-line;
  margin-bottom: 1rem;
}

.emotion-9 {
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 0.875rem;
  line-height: 1.43;
  margin-bottom: 1.25rem;
  color: #FFFFFF;
  font-size: 1rem;
  white-space: pre-line;
}

.emotion-10 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

@media (min-width: 768px) {
  .emotion-10 {
    display: -webkit-inline-box;
    display: -webkit-inline-flex;
    display: -ms-inline-flexbox;
    display: inline-flex;
  }
}

.emotion-11 {
  font-size: 0.9735rem;
  width: 100%;
}

.emotion-11:not(:first-child) {
  margin-left: none;
}

.emotion-12 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 0;
  text-align: center;
  display: block;
  width: 100%;
  padding: 0.5em 0.8em;
  font-size: 1rem;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 600;
  color: #000000;
  background-color: transparent;
  border-color: #000000;
  display: block;
  width: 100%;
  text-align: center;
  box-sizing: border-box;
  color: #FFFFFF;
  line-height: 1;
  margin: 0;
  border-width: 0;
  letter-spacing: 0;
  font-size: 1rem;
  text-transform: uppercase;
  padding: 0.75rem 1rem;
  background-color: #000000;
}

.emotion-12:focus {
  outline: none;
}

.emotion-12 svg path {
  fill: #FFFFFF!important;
}

.emotion-14 {
  display: inline-block;
  width: 10px;
  margin-left: 0.65rem;
  vertical-align: initial;
  font-size: 1.3rem;
}

.emotion-15 {
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
  background: #FFFFFF;
  box-sizing: border-box;
  list-style-type: none;
  min-width: 100%;
  overflow: hidden;
  position: relative;
  z-index: 390;
  border-width: 0;
  border-style: solid;
  text-align: center;
  letter-spacing: 0;
  pointer-events: auto;
  padding: 0;
  max-height: 0;
  -webkit-transition: max-height .5s ease-out,visibility 0s .5s;
  transition: max-height .5s ease-out,visibility 0s .5s;
  visibility: hidden;
}

.emotion-16 {
  box-sizing: border-box;
  width: 100%;
  border-bottom: 1px solid #000000;
}

.emotion-16:last-child {
  border: none;
}

.emotion-17 {
  cursor: pointer;
  display: block;
  padding: 1rem;
  -webkit-text-decoration: none;
  text-decoration: none;
  -webkit-transition: none;
  transition: none;
  white-space: nowrap;
  font-size: inherit;
  text-align: center;
  color: #000000;
  text-transform: uppercase;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  font-weight: 600;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-17:hover,
.emotion-17:focus {
  background: #000000;
  color: #FFFFFF;
}

<div
  class="emotion-0"
  data-testid="text-overlay-container"
>
  <a
    class="emotion-1"
    href="/browse/category.do?cid=11111"
    target="_self"
  >
    <div
      class="emotion-2"
    >
      <div
        data-testid="background-image"
        role="button"
        tabindex="-1"
      >
        <img
          alt="new right now"
          class=" emotion-3"
          src="static/media/background.be7c0d50.jpg"
        />
      </div>
      <div
        class="emotion-4"
      >
        <div
          class="emotion-5"
        >
          New
right
now
        </div>
      </div>
    </div>
  </a>
  <div
    class=" emotion-6"
  >
    <div
      class="emotion-7"
    >
      <div
        class="emotion-8"
      >
        New
right
now
      </div>
    </div>
    <div
      class="emotion-9"
    >
      Fast approaching fall days warrant
 wardrobe updates. On our list?
 Cool-weather classics with collegiate
 vibes and versatile appeal.
    </div>
    <div
      class="emotion-10"
    >
      <div
        class="emotion-11"
        data-testid="button-dropdown-container"
      >
        <button
          aria-expanded="false"
          class="emotion-12"
          color="primary"
        >
          <span
            class="emotion-13"
          >
            Shop Halloween styles
          </span>
          <span
            aria-hidden="true"
            class="emotion-14"
          >
            +
          </span>
        </button>
        <ul
          aria-hidden="true"
          class="emotion-15"
        >
          <li
            class="emotion-16"
          >
            <a
              breakpoint="desktop"
              class="emotion-17"
              href="girls"
              target="_self"
            >
              Girls
            </a>
          </li>
          <li
            class="emotion-16"
          >
            <a
              breakpoint="desktop"
              class="emotion-17"
              href="boys"
              target="_self"
            >
              Boys
            </a>
          </li>
          <li
            class="emotion-16"
          >
            <a
              breakpoint="desktop"
              class="emotion-17"
              href="toddler girl"
              target="_self"
            >
              Toddler girl
            </a>
          </li>
          <li
            class="emotion-16"
          >
            <a
              breakpoint="desktop"
              class="emotion-17"
              href="toddler boy"
              target="_self"
            >
              Toddler boy
            </a>
          </li>
          <li
            class="emotion-16"
          >
            <a
              breakpoint="desktop"
              class="emotion-17"
              href="baby boy"
              target="_self"
            >
              Baby boy
            </a>
          </li>
        </ul>
      </div>
      <div
        class="emotion-11"
        data-testid="button-dropdown-container"
      >
        <button
          aria-expanded="false"
          class="emotion-12"
          color="primary"
        >
          <span
            class="emotion-13"
          />
          <span
            aria-hidden="true"
            class="emotion-14"
          >
            +
          </span>
        </button>
        <ul
          aria-hidden="true"
          class="emotion-15"
        />
      </div>
    </div>
  </div>
</div>
`;

exports[`TextOverlay should match snapshot for desktop in "br" 4`] = `
.emotion-0 {
  position: relative;
  width: 100%;
  margin: 0 auto 4rem;
  text-align: left;
  color: #FFFFFF;
  max-width: 1350px;
}

.emotion-0 * {
  box-sizing: border-box;
}

.emotion-1 {
  margin-right: 0.75rem;
}

.emotion-1:last-child {
  margin-right: 0;
}

@media (max-width: 768px) {
  .emotion-1 {
    border: 1px solid #ccc;
    font-size: 2.8vw;
    letter-spacing: 3px;
    line-height: 1;
    padding: 8px;
    text-transform: uppercase;
    width: 100%;
  }
}

@media (min-width: 1024px) {
  .emotion-1 {
    font-size: 0.75rem;
  }
}

.emotion-2 {
  position: relative;
  z-index: -50;
}

.emotion-3 {
  width: 100%;
}

.emotion-4 {
  bottom: 0;
  left: 0;
  padding: 0 0.5rem 1rem 0.5rem;
  position: absolute;
  width: 100%;
}

@media (min-width: 768px) {
  .emotion-4 {
    display: none;
  }
}

.emotion-5 {
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  font-weight: 600;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 4rem;
  letter-spacing: 1.5px;
  line-height: 1;
  margin-bottom: 2rem;
  text-transform: uppercase;
  font-size: 2rem;
  letter-spacing: 1px;
}

@media (min-width: 768px) {
  .emotion-6 {
    margin: 0;
    padding: 0 0.5rem;
    position: absolute;
    top: 0;
    white-space: nowrap;
    width: auto;
  }
}

@media (max-width: 768px) {
  .emotion-7 {
    display: none;
  }
}

.emotion-8 {
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  font-weight: 600;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 4rem;
  letter-spacing: 1.5px;
  line-height: 1;
  margin-bottom: 2rem;
  text-transform: uppercase;
  font-size: 2rem;
  letter-spacing: 1px;
}

.emotion-9 {
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 0.875rem;
  line-height: 1.43;
  margin-bottom: 1.25rem;
  font-size: 4vw;
  padding-bottom: 1.25rem;
}

.emotion-10 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

@media (min-width: 768px) {
  .emotion-10 {
    display: -webkit-inline-box;
    display: -webkit-inline-flex;
    display: -ms-inline-flexbox;
    display: inline-flex;
  }
}

.emotion-11 {
  font-size: 0.9735rem;
  width: 100%;
}

.emotion-12 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 0;
  text-align: center;
  display: block;
  width: 100%;
  padding: 0.5em 0.8em;
  font-size: 1rem;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 600;
  color: #000000;
  background-color: transparent;
  border-color: #000000;
  display: block;
  width: 100%;
  text-align: center;
  box-sizing: border-box;
  color: #FFFFFF;
  line-height: 1;
  margin: 0;
  border-width: 0;
  letter-spacing: 0;
  font-size: 1rem;
  text-transform: uppercase;
  padding: 1rem 0;
  background-color: #000000;
}

.emotion-12:focus {
  outline: none;
}

.emotion-12 svg path {
  fill: #FFFFFF!important;
}

.emotion-14 {
  display: inline-block;
  width: 10px;
  margin-left: 0.65rem;
  vertical-align: initial;
  font-size: 1.3rem;
}

.emotion-15 {
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
  background: #FFFFFF;
  box-sizing: border-box;
  list-style-type: none;
  min-width: 100%;
  overflow: hidden;
  position: relative;
  z-index: 390;
  border-width: 0;
  border-style: solid;
  text-align: center;
  letter-spacing: 0;
  pointer-events: auto;
  padding: inherit;
  max-height: 0;
  -webkit-transition: max-height .5s ease-out,visibility 0s .5s;
  transition: max-height .5s ease-out,visibility 0s .5s;
  visibility: hidden;
}

.emotion-16 {
  box-sizing: border-box;
  width: auto;
  border-bottom: 1px solid #000000;
}

.emotion-16:last-child {
  border: none;
}

.emotion-17 {
  cursor: pointer;
  display: block;
  padding: 1rem;
  -webkit-text-decoration: none;
  text-decoration: none;
  -webkit-transition: none;
  transition: none;
  white-space: nowrap;
  font-size: inherit;
  text-align: center;
  color: #000000;
  text-transform: uppercase;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  font-weight: 600;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-17:hover,
.emotion-17:focus {
  background: #000000;
  color: #FFFFFF;
}

<div
  class="emotion-0"
  data-testid="text-overlay-container"
>
  <a
    class="emotion-1"
    href="/browse/category.do?cid=11111"
    target="_self"
  >
    <div
      class="emotion-2"
    >
      <div
        data-testid="background-image"
        role="button"
        tabindex="-1"
      >
        <img
          alt="new right now"
          class=" emotion-3"
          src="static/media/background-mobile.b7c76761.jpg"
        />
      </div>
      <div
        class="emotion-4"
      >
        <div
          class="emotion-5"
        >
          New
right
now
        </div>
      </div>
    </div>
  </a>
  <div
    class=" emotion-6"
  >
    <div
      class="emotion-7"
    >
      <div
        class="emotion-8"
      >
        New
right
now
      </div>
    </div>
    <div
      class="emotion-9"
    >
      Fast approaching fall days warrant
 wardrobe updates. On our list?
 Cool-weather classics with collegiate
 vibes and versatile appeal.
    </div>
    <div
      class="emotion-10"
    >
      <div
        class="emotion-11"
        data-testid="button-dropdown-container"
      >
        <button
          aria-expanded="false"
          class="emotion-12"
          color="primary"
        >
          <span
            class="emotion-13"
          >
            Shop Halloween styles
          </span>
          <span
            aria-hidden="true"
            class="emotion-14"
          >
            +
          </span>
        </button>
        <ul
          aria-hidden="true"
          class="emotion-15"
        >
          <li
            class="emotion-16"
          >
            <a
              breakpoint="mobile"
              class="emotion-17"
              href="girls"
              target="_self"
            >
              Girls
            </a>
          </li>
          <li
            class="emotion-16"
          >
            <a
              breakpoint="mobile"
              class="emotion-17"
              href="boys"
              target="_self"
            >
              Boys
            </a>
          </li>
          <li
            class="emotion-16"
          >
            <a
              breakpoint="mobile"
              class="emotion-17"
              href="toddler girl"
              target="_self"
            >
              Toddler girl
            </a>
          </li>
          <li
            class="emotion-16"
          >
            <a
              breakpoint="mobile"
              class="emotion-17"
              href="toddler boy"
              target="_self"
            >
              Toddler boy
            </a>
          </li>
          <li
            class="emotion-16"
          >
            <a
              breakpoint="mobile"
              class="emotion-17"
              href="baby boy"
              target="_self"
            >
              Baby boy
            </a>
          </li>
        </ul>
      </div>
      <div
        class="emotion-11"
        data-testid="button-dropdown-container"
      >
        <button
          aria-expanded="false"
          class="emotion-12"
          color="primary"
        >
          <span
            class="emotion-13"
          />
          <span
            aria-hidden="true"
            class="emotion-14"
          >
            +
          </span>
        </button>
        <ul
          aria-hidden="true"
          class="emotion-15"
        />
      </div>
    </div>
  </div>
</div>
`;

exports[`TextOverlay should match snapshot for desktop in "gap" 1`] = `
.emotion-0 {
  position: relative;
  width: 100%;
  margin: 0 auto 4rem;
  text-align: left;
  color: #FFFFFF;
  max-width: 1350px;
}

.emotion-0 * {
  box-sizing: border-box;
}

.emotion-1 {
  margin-right: 0.75rem;
}

.emotion-1:last-child {
  margin-right: 0;
}

@media (max-width: 768px) {
  .emotion-1 {
    background: #FFFFFF;
    border-bottom: 1px solid #ccc;
    border-left: 0;
    border-right: 0;
    color: #122344;
    font-weight: normal;
    margin: 0;
    padding: 1rem 0;
    text-align: left;
    white-space: normal;
    width: 100%;
  }

  .emotion-1:first-of-type {
    border-top: 1px solid #ccc;
  }
}

.emotion-2 {
  width: 100%;
}

@media (min-width: 768px) {
  .emotion-3 {
    margin: 0;
    padding: 0 0.5rem;
    position: absolute;
    top: 0;
    white-space: nowrap;
    width: auto;
  }
}

.emotion-5 {
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  font-weight: 700;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 4rem;
  letter-spacing: 1.5px;
  line-height: 1;
  margin-bottom: 2rem;
  text-transform: uppercase;
  color: #FFFFFF;
  font-size: 4.5vw;
  font-weight: 300;
  white-space: pre-line;
  margin-bottom: 1rem;
}

.emotion-6 {
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 0.875rem;
  line-height: 1.43;
  margin-bottom: 1.25rem;
  color: #FFFFFF;
  font-size: 1rem;
  white-space: pre-line;
}

.emotion-7 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

@media (min-width: 768px) {
  .emotion-7 {
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
  }
}

.emotion-8 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-color: #FFFFFF;
  border-radius: 0;
  border-style: solid;
  border-width: 0;
  box-sizing: border-box;
  cursor: pointer;
  display: inline-block;
  font-size: 10px;
  font-weight: 700;
  line-height: normal;
  margin: 0;
  padding: 0 12px;
  text-align: center;
  text-transform: uppercase;
  vertical-align: middle;
  white-space: nowrap;
}

.emotion-8:first-of-type {
  padding-left: 0;
}

.emotion-8:not(:first-of-type) {
  border-width: 0 0 0 1px;
}

<div
  class="emotion-0"
  data-testid="text-overlay-container"
>
  <a
    class="emotion-1"
    href="/browse/category.do?cid=11111"
    target="_self"
  >
    <div
      data-testid="background-image"
      role="button"
      tabindex="-1"
    >
      <img
        alt="new right now"
        class=" emotion-2"
        src="static/media/background.be7c0d50.jpg"
      />
    </div>
  </a>
  <div
    class=" emotion-3"
  >
    <div
      class="emotion-4"
    >
      <div
        class="emotion-5"
      >
        New
right
now
      </div>
    </div>
    <div
      class="emotion-6"
    >
      Fast approaching fall days warrant
 wardrobe updates. On our list?
 Cool-weather classics with collegiate
 vibes and versatile appeal.
    </div>
    <div
      class="emotion-7"
    >
      <a
        class="emotion-8"
        href="/browse/category.do?cid=11111"
        target="_self"
      >
        Link text one
      </a>
      <a
        class="emotion-8"
        href="/browse/category.do?cid=22222"
        target="_self"
      >
        Link text two
      </a>
    </div>
  </div>
</div>
`;

exports[`TextOverlay should match snapshot for desktop in "gap" 2`] = `
.emotion-0 {
  position: relative;
  width: 100%;
  margin: 0 auto 4rem;
  text-align: left;
  color: #FFFFFF;
  max-width: 1350px;
}

.emotion-0 * {
  box-sizing: border-box;
}

.emotion-1 {
  margin-right: 0.75rem;
}

.emotion-1:last-child {
  margin-right: 0;
}

@media (max-width: 768px) {
  .emotion-1 {
    background: #FFFFFF;
    border-bottom: 1px solid #ccc;
    border-left: 0;
    border-right: 0;
    color: #122344;
    font-weight: normal;
    margin: 0;
    padding: 1rem 0;
    text-align: left;
    white-space: normal;
    width: 100%;
  }

  .emotion-1:first-of-type {
    border-top: 1px solid #ccc;
  }
}

.emotion-2 {
  width: 100%;
}

@media (min-width: 768px) {
  .emotion-3 {
    margin: 0;
    padding: 0 0.5rem;
    position: absolute;
    top: 0;
    white-space: nowrap;
    width: auto;
  }
}

.emotion-5 {
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  font-weight: 700;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 4rem;
  letter-spacing: 1.5px;
  line-height: 1;
  margin-bottom: 2rem;
  text-transform: uppercase;
  font-size: 2rem;
  letter-spacing: 1px;
}

.emotion-6 {
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 0.875rem;
  line-height: 1.43;
  margin-bottom: 1.25rem;
  font-size: 4vw;
  padding-bottom: 1.25rem;
}

.emotion-7 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

@media (min-width: 768px) {
  .emotion-7 {
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
  }
}

.emotion-8 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-color: #FFFFFF;
  border-radius: 0;
  border-style: solid;
  border-width: 0;
  box-sizing: border-box;
  cursor: pointer;
  display: inline-block;
  font-size: 10px;
  font-weight: 700;
  line-height: normal;
  margin: 0;
  padding: 0 12px;
  text-align: center;
  text-transform: uppercase;
  vertical-align: middle;
  white-space: nowrap;
}

.emotion-8:first-of-type {
  padding-left: 0;
}

.emotion-8:not(:first-of-type) {
  border-width: 0 0 0 1px;
}

<div
  class="emotion-0"
  data-testid="text-overlay-container"
>
  <a
    class="emotion-1"
    href="/browse/category.do?cid=11111"
    target="_self"
  >
    <div
      data-testid="background-image"
      role="button"
      tabindex="-1"
    >
      <img
        alt="new right now"
        class=" emotion-2"
        src="static/media/background-mobile.b7c76761.jpg"
      />
    </div>
  </a>
  <div
    class=" emotion-3"
  >
    <div
      class="emotion-4"
    >
      <div
        class="emotion-5"
      >
        New
right
now
      </div>
    </div>
    <div
      class="emotion-6"
    >
      Fast approaching fall days warrant
 wardrobe updates. On our list?
 Cool-weather classics with collegiate
 vibes and versatile appeal.
    </div>
    <div
      class="emotion-7"
    >
      <a
        class="emotion-8"
        href="/browse/category.do?cid=11111"
        target="_self"
      >
        Link text one
      </a>
      <a
        class="emotion-8"
        href="/browse/category.do?cid=22222"
        target="_self"
      >
        Link text two
      </a>
    </div>
  </div>
</div>
`;

exports[`TextOverlay should match snapshot for desktop in "gap" 3`] = `
.emotion-0 {
  position: relative;
  width: 100%;
  margin: 0 auto 4rem;
  text-align: left;
  color: #FFFFFF;
  max-width: 1350px;
}

.emotion-0 * {
  box-sizing: border-box;
}

.emotion-1 {
  margin-right: 0.75rem;
}

.emotion-1:last-child {
  margin-right: 0;
}

@media (max-width: 768px) {
  .emotion-1 {
    background: #FFFFFF;
    border-bottom: 1px solid #ccc;
    border-left: 0;
    border-right: 0;
    color: #122344;
    font-weight: normal;
    margin: 0;
    padding: 1rem 0;
    text-align: left;
    white-space: normal;
    width: 100%;
  }

  .emotion-1:first-of-type {
    border-top: 1px solid #ccc;
  }
}

.emotion-2 {
  width: 100%;
}

@media (min-width: 768px) {
  .emotion-3 {
    margin: 0;
    padding: 0 0.5rem;
    position: absolute;
    top: 0;
    white-space: nowrap;
    width: auto;
  }
}

.emotion-5 {
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  font-weight: 700;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 4rem;
  letter-spacing: 1.5px;
  line-height: 1;
  margin-bottom: 2rem;
  text-transform: uppercase;
  color: #FFFFFF;
  font-size: 4.5vw;
  font-weight: 300;
  white-space: pre-line;
  margin-bottom: 1rem;
}

.emotion-6 {
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 0.875rem;
  line-height: 1.43;
  margin-bottom: 1.25rem;
  color: #FFFFFF;
  font-size: 1rem;
  white-space: pre-line;
}

.emotion-7 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

@media (min-width: 768px) {
  .emotion-7 {
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
  }
}

.emotion-8 {
  font-size: 0.9735rem;
  width: 100%;
}

.emotion-8:not(:first-child) {
  margin-left: none;
}

.emotion-9 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  display: inline-block;
  text-transform: none;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 1px solid transparent;
  border-radius: 0;
  max-width: 300px;
  font-size: 12px;
  font-weight: 500;
  letter-spacing: 2%;
  min-height: unset;
  max-height: auto;
  line-height: 12px;
  padding: 12px;
  width: 100%;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #000000;
  background: transparent;
  border-color: #000000;
  display: block;
  width: -webkit-fit-content!important;
  width: -moz-fit-content!important;
  width: fit-content!important;
  text-align: center;
  box-sizing: border-box;
  color: #2B2B2B;
  line-height: 1;
  margin: 0;
  text-transform: uppercase;
  background-color: #FFFFFF;
  border-color: #CCCCCC;
  border-width: 1px 0;
  font-weight: 700;
  letter-spacing: 1px;
  padding: 7px 1.5rem;
  min-height: 0;
  font-size: 12px;
}

.emotion-9:focus {
  outline: none;
}

.emotion-9>span {
  padding: 1px 0;
}

.emotion-9>span {
  padding: 0;
}

.emotion-11 {
  display: inline-block;
  width: 10px;
  margin-left: 0.65rem;
  vertical-align: initial;
  font-size: 1.3rem;
}

.emotion-12 {
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
  background: #FFFFFF;
  box-sizing: border-box;
  list-style-type: none;
  min-width: 100%;
  overflow: hidden;
  position: relative;
  z-index: 390;
  border-width: 0;
  border-style: solid;
  text-align: left;
  letter-spacing: 0;
  pointer-events: auto;
  padding: 0px;
  max-height: 0;
  -webkit-transition: max-height .5s ease-out,visibility 0s .5s;
  transition: max-height .5s ease-out,visibility 0s .5s;
  visibility: hidden;
}

.emotion-13 {
  box-sizing: border-box;
  width: 100%;
  border-bottom: none;
  letter-spacing: 1px;
  margin-left: 0;
  text-transform: none;
  padding: 12px;
}

.emotion-13:last-child {
  border: none;
}

.emotion-14 {
  cursor: pointer;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  padding: 0px;
  -webkit-text-decoration: none;
  text-decoration: none;
  -webkit-transition: color 0.3s ease;
  transition: color 0.3s ease;
  white-space: nowrap;
  font-size: 18px;
  text-align: center;
  color: inherit;
  -webkit-box-pack: start;
  -ms-flex-pack: start;
  -webkit-justify-content: start;
  justify-content: start;
  background-color: inherit;
  font-weight: 400;
  letter-spacing: 1px;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

<div
  class="emotion-0"
  data-testid="text-overlay-container"
>
  <a
    class="emotion-1"
    href="/browse/category.do?cid=11111"
    target="_self"
  >
    <div
      data-testid="background-image"
      role="button"
      tabindex="-1"
    >
      <img
        alt="new right now"
        class=" emotion-2"
        src="static/media/background.be7c0d50.jpg"
      />
    </div>
  </a>
  <div
    class=" emotion-3"
  >
    <div
      class="emotion-4"
    >
      <div
        class="emotion-5"
      >
        New
right
now
      </div>
    </div>
    <div
      class="emotion-6"
    >
      Fast approaching fall days warrant
 wardrobe updates. On our list?
 Cool-weather classics with collegiate
 vibes and versatile appeal.
    </div>
    <div
      class="emotion-7"
    >
      <div
        class="emotion-8"
        data-testid="button-dropdown-container"
      >
        <button
          aria-expanded="false"
          class="emotion-9"
          color="primary"
        >
          <span
            class="emotion-4"
          >
            Shop Halloween styles
          </span>
          <span
            aria-hidden="true"
            class="emotion-11"
          >
            +
          </span>
        </button>
        <ul
          aria-hidden="true"
          class="emotion-12"
        >
          <li
            class="emotion-13"
          >
            <a
              breakpoint="desktop"
              class="emotion-14"
              href="girls"
              target="_self"
            >
              Girls
            </a>
          </li>
          <li
            class="emotion-13"
          >
            <a
              breakpoint="desktop"
              class="emotion-14"
              href="boys"
              target="_self"
            >
              Boys
            </a>
          </li>
          <li
            class="emotion-13"
          >
            <a
              breakpoint="desktop"
              class="emotion-14"
              href="toddler girl"
              target="_self"
            >
              Toddler girl
            </a>
          </li>
          <li
            class="emotion-13"
          >
            <a
              breakpoint="desktop"
              class="emotion-14"
              href="toddler boy"
              target="_self"
            >
              Toddler boy
            </a>
          </li>
          <li
            class="emotion-13"
          >
            <a
              breakpoint="desktop"
              class="emotion-14"
              href="baby boy"
              target="_self"
            >
              Baby boy
            </a>
          </li>
        </ul>
      </div>
      <div
        class="emotion-8"
        data-testid="button-dropdown-container"
      >
        <button
          aria-expanded="false"
          class="emotion-9"
          color="primary"
        >
          <span
            class="emotion-4"
          />
          <span
            aria-hidden="true"
            class="emotion-11"
          >
            +
          </span>
        </button>
        <ul
          aria-hidden="true"
          class="emotion-12"
        />
      </div>
    </div>
  </div>
</div>
`;

exports[`TextOverlay should match snapshot for desktop in "gap" 4`] = `
.emotion-0 {
  position: relative;
  width: 100%;
  margin: 0 auto 4rem;
  text-align: left;
  color: #FFFFFF;
  max-width: 1350px;
}

.emotion-0 * {
  box-sizing: border-box;
}

.emotion-1 {
  margin-right: 0.75rem;
}

.emotion-1:last-child {
  margin-right: 0;
}

@media (max-width: 768px) {
  .emotion-1 {
    background: #FFFFFF;
    border-bottom: 1px solid #ccc;
    border-left: 0;
    border-right: 0;
    color: #122344;
    font-weight: normal;
    margin: 0;
    padding: 1rem 0;
    text-align: left;
    white-space: normal;
    width: 100%;
  }

  .emotion-1:first-of-type {
    border-top: 1px solid #ccc;
  }
}

.emotion-2 {
  width: 100%;
}

@media (min-width: 768px) {
  .emotion-3 {
    margin: 0;
    padding: 0 0.5rem;
    position: absolute;
    top: 0;
    white-space: nowrap;
    width: auto;
  }
}

.emotion-5 {
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  font-weight: 700;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 4rem;
  letter-spacing: 1.5px;
  line-height: 1;
  margin-bottom: 2rem;
  text-transform: uppercase;
  font-size: 2rem;
  letter-spacing: 1px;
}

.emotion-6 {
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 0.875rem;
  line-height: 1.43;
  margin-bottom: 1.25rem;
  font-size: 4vw;
  padding-bottom: 1.25rem;
}

.emotion-7 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

@media (min-width: 768px) {
  .emotion-7 {
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
  }
}

.emotion-8 {
  font-size: 0.9735rem;
  width: 100%;
}

.emotion-9 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  display: inline-block;
  text-transform: none;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 1px solid transparent;
  border-radius: 0;
  font-size: 12px;
  font-weight: 500;
  letter-spacing: 2%;
  min-height: unset;
  max-height: auto;
  line-height: 1.125;
  padding: 12px;
  width: 100%;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #000000;
  background: transparent;
  border-color: #000000;
  display: block;
  width: -webkit-fit-content!important;
  width: -moz-fit-content!important;
  width: fit-content!important;
  text-align: center;
  box-sizing: border-box;
  color: #2B2B2B;
  line-height: 1;
  margin: 0;
  text-transform: uppercase;
  background-color: #FFFFFF;
  border-color: #CCCCCC;
  border-width: 1px 0;
  font-weight: 700;
  letter-spacing: 1px;
  padding: 7px 1.5rem;
  min-height: 0;
  font-size: inherit;
}

.emotion-9:focus {
  outline: none;
}

.emotion-9>span {
  padding: 1px 0;
}

.emotion-9>span {
  padding: 0;
}

.emotion-11 {
  display: inline-block;
  width: 10px;
  margin-left: 0.65rem;
  vertical-align: initial;
  font-size: 1.3rem;
}

.emotion-12 {
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
  background: #FFFFFF;
  box-sizing: border-box;
  list-style-type: none;
  min-width: 100%;
  overflow: hidden;
  position: relative;
  z-index: 390;
  border-width: 0;
  border-style: solid;
  text-align: left;
  letter-spacing: 0;
  pointer-events: auto;
  padding: 0px;
  max-height: 0;
  -webkit-transition: max-height .5s ease-out,visibility 0s .5s;
  transition: max-height .5s ease-out,visibility 0s .5s;
  visibility: hidden;
}

.emotion-13 {
  box-sizing: border-box;
  width: auto;
  border-bottom: none;
  letter-spacing: 1px;
  margin-left: 0;
  text-transform: none;
  padding: 12px;
}

.emotion-13:last-child {
  border: none;
}

.emotion-14 {
  cursor: pointer;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  padding: 0px;
  -webkit-text-decoration: none;
  text-decoration: none;
  -webkit-transition: color 0.3s ease;
  transition: color 0.3s ease;
  white-space: nowrap;
  font-size: 18px;
  text-align: center;
  color: inherit;
  -webkit-box-pack: start;
  -ms-flex-pack: start;
  -webkit-justify-content: start;
  justify-content: start;
  background-color: inherit;
  font-weight: 400;
  letter-spacing: 1px;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

<div
  class="emotion-0"
  data-testid="text-overlay-container"
>
  <a
    class="emotion-1"
    href="/browse/category.do?cid=11111"
    target="_self"
  >
    <div
      data-testid="background-image"
      role="button"
      tabindex="-1"
    >
      <img
        alt="new right now"
        class=" emotion-2"
        src="static/media/background-mobile.b7c76761.jpg"
      />
    </div>
  </a>
  <div
    class=" emotion-3"
  >
    <div
      class="emotion-4"
    >
      <div
        class="emotion-5"
      >
        New
right
now
      </div>
    </div>
    <div
      class="emotion-6"
    >
      Fast approaching fall days warrant
 wardrobe updates. On our list?
 Cool-weather classics with collegiate
 vibes and versatile appeal.
    </div>
    <div
      class="emotion-7"
    >
      <div
        class="emotion-8"
        data-testid="button-dropdown-container"
      >
        <button
          aria-expanded="false"
          class="emotion-9"
          color="primary"
        >
          <span
            class="emotion-4"
          >
            Shop Halloween styles
          </span>
          <span
            aria-hidden="true"
            class="emotion-11"
          >
            +
          </span>
        </button>
        <ul
          aria-hidden="true"
          class="emotion-12"
        >
          <li
            class="emotion-13"
          >
            <a
              breakpoint="mobile"
              class="emotion-14"
              href="girls"
              target="_self"
            >
              Girls
            </a>
          </li>
          <li
            class="emotion-13"
          >
            <a
              breakpoint="mobile"
              class="emotion-14"
              href="boys"
              target="_self"
            >
              Boys
            </a>
          </li>
          <li
            class="emotion-13"
          >
            <a
              breakpoint="mobile"
              class="emotion-14"
              href="toddler girl"
              target="_self"
            >
              Toddler girl
            </a>
          </li>
          <li
            class="emotion-13"
          >
            <a
              breakpoint="mobile"
              class="emotion-14"
              href="toddler boy"
              target="_self"
            >
              Toddler boy
            </a>
          </li>
          <li
            class="emotion-13"
          >
            <a
              breakpoint="mobile"
              class="emotion-14"
              href="baby boy"
              target="_self"
            >
              Baby boy
            </a>
          </li>
        </ul>
      </div>
      <div
        class="emotion-8"
        data-testid="button-dropdown-container"
      >
        <button
          aria-expanded="false"
          class="emotion-9"
          color="primary"
        >
          <span
            class="emotion-4"
          />
          <span
            aria-hidden="true"
            class="emotion-11"
          >
            +
          </span>
        </button>
        <ul
          aria-hidden="true"
          class="emotion-12"
        />
      </div>
    </div>
  </div>
</div>
`;

exports[`TextOverlay should match snapshot for desktop in "on" 1`] = `
.emotion-0 {
  position: relative;
  width: 100%;
  margin: 0 auto 4rem;
  text-align: left;
  color: #FFFFFF;
  max-width: 1350px;
}

.emotion-0 * {
  box-sizing: border-box;
}

.emotion-1 {
  margin-right: 0.75rem;
}

.emotion-1:last-child {
  margin-right: 0;
}

@media (max-width: 768px) {
  .emotion-1 {
    background: #FFFFFF;
    border-bottom: 1px solid #ccc;
    border-left: 0;
    border-right: 0;
    color: #122344;
    font-weight: normal;
    margin: 0;
    padding: 1rem 0;
    text-align: left;
    white-space: normal;
    width: 100%;
  }

  .emotion-1:first-of-type {
    border-top: 1px solid #ccc;
  }
}

.emotion-2 {
  width: 100%;
}

@media (min-width: 768px) {
  .emotion-3 {
    margin: 0;
    padding: 0 0.5rem;
    position: absolute;
    top: 0;
    white-space: nowrap;
    width: auto;
  }
}

.emotion-5 {
  font-family: var(--font-family-font1),sans-serif;
  font-weight: 700;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 4rem;
  letter-spacing: 1.5px;
  line-height: 1;
  margin-bottom: 2rem;
  text-transform: uppercase;
  color: #FFFFFF;
  font-size: 4.5vw;
  font-weight: 300;
  white-space: pre-line;
  margin-bottom: 1rem;
}

.emotion-6 {
  font-family: var(--font-family-font1),sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 0.875rem;
  line-height: 1.43;
  margin-bottom: 1.25rem;
  color: #FFFFFF;
  font-size: 1rem;
  white-space: pre-line;
}

.emotion-7 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

@media (min-width: 768px) {
  .emotion-7 {
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
  }
}

.emotion-8 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-color: #FFFFFF;
  border-radius: 0;
  border-style: solid;
  border-width: 0;
  box-sizing: border-box;
  cursor: pointer;
  display: inline-block;
  font-size: 10px;
  font-weight: 700;
  line-height: normal;
  margin: 0;
  padding: 0 12px;
  text-align: center;
  text-transform: uppercase;
  vertical-align: middle;
  white-space: nowrap;
}

.emotion-8:first-of-type {
  padding-left: 0;
}

.emotion-8:not(:first-of-type) {
  border-width: 0 0 0 1px;
}

<div
  class="emotion-0"
  data-testid="text-overlay-container"
>
  <a
    class="emotion-1"
    href="/browse/category.do?cid=11111"
    target="_self"
  >
    <div
      data-testid="background-image"
      role="button"
      tabindex="-1"
    >
      <img
        alt="new right now"
        class=" emotion-2"
        src="static/media/background.be7c0d50.jpg"
      />
    </div>
  </a>
  <div
    class=" emotion-3"
  >
    <div
      class="emotion-4"
    >
      <div
        class="emotion-5"
      >
        New
right
now
      </div>
    </div>
    <div
      class="emotion-6"
    >
      Fast approaching fall days warrant
 wardrobe updates. On our list?
 Cool-weather classics with collegiate
 vibes and versatile appeal.
    </div>
    <div
      class="emotion-7"
    >
      <a
        class="emotion-8"
        href="/browse/category.do?cid=11111"
        target="_self"
      >
        Link text one
      </a>
      <a
        class="emotion-8"
        href="/browse/category.do?cid=22222"
        target="_self"
      >
        Link text two
      </a>
    </div>
  </div>
</div>
`;

exports[`TextOverlay should match snapshot for desktop in "on" 2`] = `
.emotion-0 {
  position: relative;
  width: 100%;
  margin: 0 auto 4rem;
  text-align: left;
  color: #FFFFFF;
  max-width: 1350px;
}

.emotion-0 * {
  box-sizing: border-box;
}

.emotion-1 {
  margin-right: 0.75rem;
}

.emotion-1:last-child {
  margin-right: 0;
}

@media (max-width: 768px) {
  .emotion-1 {
    background: #FFFFFF;
    border-bottom: 1px solid #ccc;
    border-left: 0;
    border-right: 0;
    color: #122344;
    font-weight: normal;
    margin: 0;
    padding: 1rem 0;
    text-align: left;
    white-space: normal;
    width: 100%;
  }

  .emotion-1:first-of-type {
    border-top: 1px solid #ccc;
  }
}

.emotion-2 {
  width: 100%;
}

@media (min-width: 768px) {
  .emotion-3 {
    margin: 0;
    padding: 0 0.5rem;
    position: absolute;
    top: 0;
    white-space: nowrap;
    width: auto;
  }
}

.emotion-5 {
  font-family: var(--font-family-font1),sans-serif;
  font-weight: 700;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 4rem;
  letter-spacing: 1.5px;
  line-height: 1;
  margin-bottom: 2rem;
  text-transform: uppercase;
  font-size: 2rem;
  letter-spacing: 1px;
}

.emotion-6 {
  font-family: var(--font-family-font1),sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 0.875rem;
  line-height: 1.43;
  margin-bottom: 1.25rem;
  font-size: 4vw;
  padding-bottom: 1.25rem;
}

.emotion-7 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

@media (min-width: 768px) {
  .emotion-7 {
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
  }
}

.emotion-8 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-color: #FFFFFF;
  border-radius: 0;
  border-style: solid;
  border-width: 0;
  box-sizing: border-box;
  cursor: pointer;
  display: inline-block;
  font-size: 10px;
  font-weight: 700;
  line-height: normal;
  margin: 0;
  padding: 0 12px;
  text-align: center;
  text-transform: uppercase;
  vertical-align: middle;
  white-space: nowrap;
}

.emotion-8:first-of-type {
  padding-left: 0;
}

.emotion-8:not(:first-of-type) {
  border-width: 0 0 0 1px;
}

<div
  class="emotion-0"
  data-testid="text-overlay-container"
>
  <a
    class="emotion-1"
    href="/browse/category.do?cid=11111"
    target="_self"
  >
    <div
      data-testid="background-image"
      role="button"
      tabindex="-1"
    >
      <img
        alt="new right now"
        class=" emotion-2"
        src="static/media/background-mobile.b7c76761.jpg"
      />
    </div>
  </a>
  <div
    class=" emotion-3"
  >
    <div
      class="emotion-4"
    >
      <div
        class="emotion-5"
      >
        New
right
now
      </div>
    </div>
    <div
      class="emotion-6"
    >
      Fast approaching fall days warrant
 wardrobe updates. On our list?
 Cool-weather classics with collegiate
 vibes and versatile appeal.
    </div>
    <div
      class="emotion-7"
    >
      <a
        class="emotion-8"
        href="/browse/category.do?cid=11111"
        target="_self"
      >
        Link text one
      </a>
      <a
        class="emotion-8"
        href="/browse/category.do?cid=22222"
        target="_self"
      >
        Link text two
      </a>
    </div>
  </div>
</div>
`;

exports[`TextOverlay should match snapshot for desktop in "on" 3`] = `
.emotion-0 {
  position: relative;
  width: 100%;
  margin: 0 auto 4rem;
  text-align: left;
  color: #FFFFFF;
  max-width: 1350px;
}

.emotion-0 * {
  box-sizing: border-box;
}

.emotion-1 {
  margin-right: 0.75rem;
}

.emotion-1:last-child {
  margin-right: 0;
}

@media (max-width: 768px) {
  .emotion-1 {
    background: #FFFFFF;
    border-bottom: 1px solid #ccc;
    border-left: 0;
    border-right: 0;
    color: #122344;
    font-weight: normal;
    margin: 0;
    padding: 1rem 0;
    text-align: left;
    white-space: normal;
    width: 100%;
  }

  .emotion-1:first-of-type {
    border-top: 1px solid #ccc;
  }
}

.emotion-2 {
  width: 100%;
}

@media (min-width: 768px) {
  .emotion-3 {
    margin: 0;
    padding: 0 0.5rem;
    position: absolute;
    top: 0;
    white-space: nowrap;
    width: auto;
  }
}

.emotion-5 {
  font-family: var(--font-family-font1),sans-serif;
  font-weight: 700;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 4rem;
  letter-spacing: 1.5px;
  line-height: 1;
  margin-bottom: 2rem;
  text-transform: uppercase;
  color: #FFFFFF;
  font-size: 4.5vw;
  font-weight: 300;
  white-space: pre-line;
  margin-bottom: 1rem;
}

.emotion-6 {
  font-family: var(--font-family-font1),sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 0.875rem;
  line-height: 1.43;
  margin-bottom: 1.25rem;
  color: #FFFFFF;
  font-size: 1rem;
  white-space: pre-line;
}

.emotion-7 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

@media (min-width: 768px) {
  .emotion-7 {
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
  }
}

.emotion-8 {
  font-size: 0.9735rem;
  width: 100%;
}

.emotion-8:not(:first-child) {
  margin-left: none;
}

.emotion-9 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 700;
  letter-spacing: 1.6800000000000002px;
  min-height: 48px;
  max-height: auto;
  line-height: 1.1428571428571428;
  padding: 14px 22px;
  width: 100%;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #003764;
  background: transparent;
  border-color: #003764;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  width: 100%;
  text-align: center;
  box-sizing: border-box;
  color: #003764;
  line-height: 1;
  letter-spacing: 0;
  font-size: 1rem;
  padding: 0 0.8em;
  margin: 0;
  text-transform: uppercase;
  background-color: #FFFFFF;
  border-width: 2px;
  border-color: #003764;
}

.emotion-9:focus {
  outline: none;
}

.emotion-9>span {
  padding: 1px 0;
}

.emotion-11 {
  display: inline-block;
  height: 10px;
  width: 10px;
  min-height: 10px;
  min-width: 10px;
  margin-left: 0.65rem;
  padding-top: 0;
}

.emotion-11 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-11 svg path {
  fill: #003764;
}

.emotion-11 svg rect {
  fill: #003764;
}

.emotion-12 {
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
  background: #FFFFFF;
  box-sizing: border-box;
  list-style-type: none;
  min-width: 100%;
  overflow: hidden;
  position: relative;
  z-index: 390;
  border-width: 0;
  border-style: solid;
  text-align: left;
  letter-spacing: 0;
  pointer-events: auto;
  padding: 0 1rem;
  max-height: 0;
  -webkit-transition: max-height .5s ease-out,visibility 0s .5s;
  transition: max-height .5s ease-out,visibility 0s .5s;
  visibility: hidden;
}

.emotion-13 {
  box-sizing: border-box;
  width: 100%;
  border-bottom: 1px solid #000000;
}

.emotion-13:last-child {
  border: none;
}

.emotion-14 {
  cursor: pointer;
  display: block;
  padding: 0.5rem 0;
  -webkit-text-decoration: none;
  text-decoration: none;
  -webkit-transition: color 0.3s ease;
  transition: color 0.3s ease;
  white-space: nowrap;
  font-size: inherit;
  text-align: center;
  color: #000000;
  font-family: var(--font-family-font1),sans-serif;
  font-weight: 700;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

<div
  class="emotion-0"
  data-testid="text-overlay-container"
>
  <a
    class="emotion-1"
    href="/browse/category.do?cid=11111"
    target="_self"
  >
    <div
      data-testid="background-image"
      role="button"
      tabindex="-1"
    >
      <img
        alt="new right now"
        class=" emotion-2"
        src="static/media/background.be7c0d50.jpg"
      />
    </div>
  </a>
  <div
    class=" emotion-3"
  >
    <div
      class="emotion-4"
    >
      <div
        class="emotion-5"
      >
        New
right
now
      </div>
    </div>
    <div
      class="emotion-6"
    >
      Fast approaching fall days warrant
 wardrobe updates. On our list?
 Cool-weather classics with collegiate
 vibes and versatile appeal.
    </div>
    <div
      class="emotion-7"
    >
      <div
        class="emotion-8"
        data-testid="button-dropdown-container"
      >
        <button
          aria-expanded="false"
          class="emotion-9"
          color="primary"
        >
          <span
            class="emotion-4"
          >
            Shop Halloween styles
          </span>
          <span
            aria-hidden="true"
            class="emotion-11"
          >
            <svg
              viewBox="0 0 10.5 10.5"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M9.75 4.125H6.375V.75a.75.75 0 00-.75-.75h-.75a.75.75 0 00-.75.75v3.375H.75a.75.75 0 00-.75.75v.75a.75.75 0 00.75.75h3.375V9.75a.75.75 0 00.75.75h.75a.75.75 0 00.75-.75V6.375H9.75a.75.75 0 00.75-.75v-.75a.75.75 0 00-.75-.75z"
                fill="#003764"
              />
            </svg>
          </span>
        </button>
        <ul
          aria-hidden="true"
          class="emotion-12"
        >
          <li
            class="emotion-13"
          >
            <a
              breakpoint="desktop"
              class="emotion-14"
              href="girls"
              target="_self"
            >
              Girls
            </a>
          </li>
          <li
            class="emotion-13"
          >
            <a
              breakpoint="desktop"
              class="emotion-14"
              href="boys"
              target="_self"
            >
              Boys
            </a>
          </li>
          <li
            class="emotion-13"
          >
            <a
              breakpoint="desktop"
              class="emotion-14"
              href="toddler girl"
              target="_self"
            >
              Toddler girl
            </a>
          </li>
          <li
            class="emotion-13"
          >
            <a
              breakpoint="desktop"
              class="emotion-14"
              href="toddler boy"
              target="_self"
            >
              Toddler boy
            </a>
          </li>
          <li
            class="emotion-13"
          >
            <a
              breakpoint="desktop"
              class="emotion-14"
              href="baby boy"
              target="_self"
            >
              Baby boy
            </a>
          </li>
        </ul>
      </div>
      <div
        class="emotion-8"
        data-testid="button-dropdown-container"
      >
        <button
          aria-expanded="false"
          class="emotion-9"
          color="primary"
        >
          <span
            class="emotion-4"
          />
          <span
            aria-hidden="true"
            class="emotion-11"
          >
            <svg
              viewBox="0 0 10.5 10.5"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M9.75 4.125H6.375V.75a.75.75 0 00-.75-.75h-.75a.75.75 0 00-.75.75v3.375H.75a.75.75 0 00-.75.75v.75a.75.75 0 00.75.75h3.375V9.75a.75.75 0 00.75.75h.75a.75.75 0 00.75-.75V6.375H9.75a.75.75 0 00.75-.75v-.75a.75.75 0 00-.75-.75z"
                fill="#003764"
              />
            </svg>
          </span>
        </button>
        <ul
          aria-hidden="true"
          class="emotion-12"
        />
      </div>
    </div>
  </div>
</div>
`;

exports[`TextOverlay should match snapshot for desktop in "on" 4`] = `
.emotion-0 {
  position: relative;
  width: 100%;
  margin: 0 auto 4rem;
  text-align: left;
  color: #FFFFFF;
  max-width: 1350px;
}

.emotion-0 * {
  box-sizing: border-box;
}

.emotion-1 {
  margin-right: 0.75rem;
}

.emotion-1:last-child {
  margin-right: 0;
}

@media (max-width: 768px) {
  .emotion-1 {
    background: #FFFFFF;
    border-bottom: 1px solid #ccc;
    border-left: 0;
    border-right: 0;
    color: #122344;
    font-weight: normal;
    margin: 0;
    padding: 1rem 0;
    text-align: left;
    white-space: normal;
    width: 100%;
  }

  .emotion-1:first-of-type {
    border-top: 1px solid #ccc;
  }
}

.emotion-2 {
  width: 100%;
}

@media (min-width: 768px) {
  .emotion-3 {
    margin: 0;
    padding: 0 0.5rem;
    position: absolute;
    top: 0;
    white-space: nowrap;
    width: auto;
  }
}

.emotion-5 {
  font-family: var(--font-family-font1),sans-serif;
  font-weight: 700;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 4rem;
  letter-spacing: 1.5px;
  line-height: 1;
  margin-bottom: 2rem;
  text-transform: uppercase;
  font-size: 2rem;
  letter-spacing: 1px;
}

.emotion-6 {
  font-family: var(--font-family-font1),sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 0.875rem;
  line-height: 1.43;
  margin-bottom: 1.25rem;
  font-size: 4vw;
  padding-bottom: 1.25rem;
}

.emotion-7 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

@media (min-width: 768px) {
  .emotion-7 {
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
  }
}

.emotion-8 {
  font-size: 0.9735rem;
  width: 100%;
}

.emotion-9 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 700;
  letter-spacing: 1.6800000000000002px;
  min-height: 44px;
  max-height: auto;
  line-height: 1.1428571428571428;
  padding: 12px 22px;
  width: 100%;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #003764;
  background: transparent;
  border-color: #003764;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  width: 100%;
  text-align: center;
  box-sizing: border-box;
  color: #003764;
  line-height: 1;
  letter-spacing: 0;
  font-size: 1rem;
  padding: 0 0.8em;
  margin: 0;
  text-transform: uppercase;
  background-color: #FFFFFF;
  border-width: 2px;
  border-color: #003764;
}

.emotion-9:focus {
  outline: none;
}

.emotion-9>span {
  padding: 1px 0;
}

.emotion-11 {
  display: inline-block;
  height: 10px;
  width: 10px;
  min-height: 10px;
  min-width: 10px;
  margin-left: 0.65rem;
  padding-top: 0;
}

.emotion-11 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-11 svg path {
  fill: #003764;
}

.emotion-11 svg rect {
  fill: #003764;
}

.emotion-12 {
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
  background: #FFFFFF;
  box-sizing: border-box;
  list-style-type: none;
  min-width: 100%;
  overflow: hidden;
  position: relative;
  z-index: 390;
  border-width: 0;
  border-style: solid;
  text-align: left;
  letter-spacing: 0;
  pointer-events: auto;
  padding: inherit;
  max-height: 0;
  -webkit-transition: max-height .5s ease-out,visibility 0s .5s;
  transition: max-height .5s ease-out,visibility 0s .5s;
  visibility: hidden;
}

.emotion-13 {
  box-sizing: border-box;
  width: auto;
  border-bottom: 1px solid #000000;
}

.emotion-13:last-child {
  border: none;
}

.emotion-14 {
  cursor: pointer;
  display: block;
  padding: 0.75rem 0;
  -webkit-text-decoration: none;
  text-decoration: none;
  -webkit-transition: color 0.3s ease;
  transition: color 0.3s ease;
  white-space: nowrap;
  font-size: inherit;
  text-align: center;
  color: #000000;
  font-family: var(--font-family-font1),sans-serif;
  font-weight: 700;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

<div
  class="emotion-0"
  data-testid="text-overlay-container"
>
  <a
    class="emotion-1"
    href="/browse/category.do?cid=11111"
    target="_self"
  >
    <div
      data-testid="background-image"
      role="button"
      tabindex="-1"
    >
      <img
        alt="new right now"
        class=" emotion-2"
        src="static/media/background-mobile.b7c76761.jpg"
      />
    </div>
  </a>
  <div
    class=" emotion-3"
  >
    <div
      class="emotion-4"
    >
      <div
        class="emotion-5"
      >
        New
right
now
      </div>
    </div>
    <div
      class="emotion-6"
    >
      Fast approaching fall days warrant
 wardrobe updates. On our list?
 Cool-weather classics with collegiate
 vibes and versatile appeal.
    </div>
    <div
      class="emotion-7"
    >
      <div
        class="emotion-8"
        data-testid="button-dropdown-container"
      >
        <button
          aria-expanded="false"
          class="emotion-9"
          color="primary"
        >
          <span
            class="emotion-4"
          >
            Shop Halloween styles
          </span>
          <span
            aria-hidden="true"
            class="emotion-11"
          >
            <svg
              viewBox="0 0 10.5 10.5"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M9.75 4.125H6.375V.75a.75.75 0 00-.75-.75h-.75a.75.75 0 00-.75.75v3.375H.75a.75.75 0 00-.75.75v.75a.75.75 0 00.75.75h3.375V9.75a.75.75 0 00.75.75h.75a.75.75 0 00.75-.75V6.375H9.75a.75.75 0 00.75-.75v-.75a.75.75 0 00-.75-.75z"
                fill="#003764"
              />
            </svg>
          </span>
        </button>
        <ul
          aria-hidden="true"
          class="emotion-12"
        >
          <li
            class="emotion-13"
          >
            <a
              breakpoint="mobile"
              class="emotion-14"
              href="girls"
              target="_self"
            >
              Girls
            </a>
          </li>
          <li
            class="emotion-13"
          >
            <a
              breakpoint="mobile"
              class="emotion-14"
              href="boys"
              target="_self"
            >
              Boys
            </a>
          </li>
          <li
            class="emotion-13"
          >
            <a
              breakpoint="mobile"
              class="emotion-14"
              href="toddler girl"
              target="_self"
            >
              Toddler girl
            </a>
          </li>
          <li
            class="emotion-13"
          >
            <a
              breakpoint="mobile"
              class="emotion-14"
              href="toddler boy"
              target="_self"
            >
              Toddler boy
            </a>
          </li>
          <li
            class="emotion-13"
          >
            <a
              breakpoint="mobile"
              class="emotion-14"
              href="baby boy"
              target="_self"
            >
              Baby boy
            </a>
          </li>
        </ul>
      </div>
      <div
        class="emotion-8"
        data-testid="button-dropdown-container"
      >
        <button
          aria-expanded="false"
          class="emotion-9"
          color="primary"
        >
          <span
            class="emotion-4"
          />
          <span
            aria-hidden="true"
            class="emotion-11"
          >
            <svg
              viewBox="0 0 10.5 10.5"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M9.75 4.125H6.375V.75a.75.75 0 00-.75-.75h-.75a.75.75 0 00-.75.75v3.375H.75a.75.75 0 00-.75.75v.75a.75.75 0 00.75.75h3.375V9.75a.75.75 0 00.75.75h.75a.75.75 0 00.75-.75V6.375H9.75a.75.75 0 00.75-.75v-.75a.75.75 0 00-.75-.75z"
                fill="#003764"
              />
            </svg>
          </span>
        </button>
        <ul
          aria-hidden="true"
          class="emotion-12"
        />
      </div>
    </div>
  </div>
</div>
`;
