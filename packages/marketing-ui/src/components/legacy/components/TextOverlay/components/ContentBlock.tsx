// @ts-nocheck
'use client';
import React from 'react';
import classnames from 'classnames';
import { CSSObject, Interpolation, Theme, forBrands, styled } from '@ecom-next/core/react-stitch';
import { Body, Eyebrow, Headline } from '../types';
import { DeprecatedStyleProps } from '../../../types';
import { EyebrowContainer } from './EyebrowContainer';
import { BodyContainer } from './BodyContainer';
import { HeadlineContainer } from './HeadlineContainer';
import { CtaContainer } from './CtaContainer';

export type ContentBlockProps = {
  body?: Body;
  breakpoint: keyof DeprecatedStyleProps;
  customClasses?: string;
  eyebrow?: Eyebrow;
  headline?: Headline;
  linksContainerStyle?: Interpolation<Theme>;
  renderedLinks: React.ReactNode;
};
const contentBlockStyleByBrandFn = (theme: Theme): CSSObject =>
  forBrands(theme, {
    at: () => ({
      '@media (max-width: 768px)': {
        position: 'absolute',
      },
    }),
  }) as CSSObject;

const ContentBlockContainer = styled.div<{ theme?: Theme }>(({ theme }) => ({
  '@media (min-width: 768px)': {
    margin: '0',
    padding: '0 0.5rem',
    position: 'absolute',
    top: '0',
    whiteSpace: 'nowrap',
    width: 'auto',
  },
  ...contentBlockStyleByBrandFn(theme),
}));

const DesktopHeadlines = styled.div(
  ({ theme }) =>
    forBrands(theme, {
      br: () => ({
        '@media (max-width: 768px)': {
          display: 'none',
        },
      }),
    }) as CSSObject
);

const ContentBlock = ({
  body = {},
  breakpoint,
  customClasses,
  eyebrow = {},
  headline = {},
  linksContainerStyle = {},
  renderedLinks,
}: ContentBlockProps): JSX.Element => (
  <ContentBlockContainer className={classnames(customClasses)}>
    <DesktopHeadlines>
      {'content' in eyebrow && <EyebrowContainer css={eyebrow.style?.[breakpoint]}>{eyebrow.content}</EyebrowContainer>}
      {'content' in headline && <HeadlineContainer css={headline.style?.[breakpoint]}>{headline.content}</HeadlineContainer>}
    </DesktopHeadlines>
    {'content' in body && <BodyContainer css={body.style?.[breakpoint]}>{body.content}</BodyContainer>}
    <CtaContainer css={linksContainerStyle}>{renderedLinks}</CtaContainer>
  </ContentBlockContainer>
);

export default ContentBlock;
