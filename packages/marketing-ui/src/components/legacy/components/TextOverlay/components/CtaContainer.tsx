// @ts-nocheck
'use client';
import { CSSObject, Interpolation, forBrands, styled, Theme } from '@ecom-next/core/react-stitch';

const basicCtaContainerStyle = {
  display: 'flex',
  '@media (min-width: 768px)': {
    display: 'flex',
  },
};

export const CtaContainer = styled.div<{ css?: Interpolation<Theme> }>(
  ({ theme }) =>
    forBrands(theme, {
      default: () => ({
        display: 'flex',
        '@media (min-width: 768px)': {
          display: 'inline-flex',
        },
      }),
      at: () => ({
        justifyContent: 'center',
      }),
      gap: () => basicCtaContainerStyle,
      on: () => basicCtaContainerStyle,
    }) as CSSObject
);
