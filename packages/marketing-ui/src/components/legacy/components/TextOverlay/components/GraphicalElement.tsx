// @ts-nocheck
'use client';
import React from 'react';
import { CSSObject, forBrands, styled } from '@ecom-next/core/react-stitch';
import { useAppState } from '@ecom-next/sitewide/app-state-provider';
import { Image } from '../types';

export type GraphicalElementProps = {
  graphicalElement?: Image;
  isDesktop: boolean;
  customClasses?: string;
  customStyles?: CSSObject;
};

const basicStyleGraphicalStyle: CSSObject = {
  position: 'absolute',
};

const GraphicalElementContainer = styled.div(
  ({ theme }) =>
    forBrands(theme, {
      on: () => basicStyleGraphicalStyle,
      gap: () => basicStyleGraphicalStyle,
    }) as CSSObject
);

const GraphicalElement = ({ graphicalElement = {}, isDesktop, customClasses, customStyles }: GraphicalElementProps): JSX.Element => {
  const { criticalResources } = useAppState();
  const { alt = '', largeImg = '', smallImg = '' } = graphicalElement;

  criticalResources.push(isDesktop ? largeImg : smallImg);

  return (
    <GraphicalElementContainer className={customClasses} css={customStyles} data-testid='graphical-element'>
      <picture>
        <source media='(max-width:767px)' srcSet={smallImg} />
        <img alt={alt} src={largeImg} />
      </picture>
    </GraphicalElementContainer>
  );
};

export default GraphicalElement;
