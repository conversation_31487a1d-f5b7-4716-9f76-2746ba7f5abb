// @ts-nocheck
'use client';
import React from 'react';
import { textOverlayLinks, textOverlayButtonDropdowns } from './__fixtures__';
import TextOverlay from '.';

export default {
  title: 'Common/JSON Components (Marketing)/Text Overlay',
  component: TextOverlay,
  parameters: {
    eyes: { include: false },
  },
};

const TextOverlayStory = args => (
  <div style={{ margin: '1vw' }}>
    <TextOverlay {...args} />
  </div>
);

export const Default = TextOverlayStory.bind({});
Default.args = textOverlayLinks;

export const WithButtonDropdown = TextOverlayStory.bind({});
WithButtonDropdown.args = textOverlayButtonDropdowns;
