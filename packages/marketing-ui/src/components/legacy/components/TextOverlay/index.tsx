// @ts-nocheck
'use client';
import React, { useContext } from 'react';
import { BreakpointContext, LARGE } from '@ecom-next/core/breakpoint-provider';
import { styled } from '@ecom-next/core/react-stitch';
import { useAppState } from '@ecom-next/sitewide/app-state-provider';
import { Brands } from '@ecom-next/core/legacy/utility';
import { TextOverlayProps } from './types';
import { renderDefaultLinks, renderDropdownLinks } from './helpers';
import { TextOverlayContainer } from './TextOverlayContainer';
import { wcdTracking } from '../../helper/wcdTracking';
import BackgroundImage from './components/BackgroundImage';
import ContentBlock from './components/ContentBlock';
import GraphicalElement from './components/GraphicalElement';
import { EyebrowContainer } from './components/EyebrowContainer';
import { HeadlineContainer } from './components/HeadlineContainer';
import { LinkContainer } from './components/LinkContainer';
import { mapDataToProps } from '../../helper';

const MobileHeadlineContainer = styled.div({
  bottom: '0',
  left: '0',
  padding: '0 0.5rem 1rem 0.5rem',
  position: 'absolute',
  width: '100%',
  '@media (min-width: 768px)': {
    display: 'none',
  },
});

const MobileContainer = styled.div({
  position: 'relative',
  zIndex: -50,
});

export const TextOverlay = ({
  body,
  containerStyle = {},
  eyebrow = {},
  graphicalElement = {},
  headline = {},
  image,
  links = {},
  textContainerStyle,
}: TextOverlayProps): JSX.Element | null => {
  const { brandName } = useAppState();
  const { minWidth } = useContext(BreakpointContext);
  const isDesktop = minWidth(LARGE);
  const breakpoint = isDesktop ? 'desktop' : 'mobile';

  const { href, target, trackingId } = links?.content?.[0] || {};
  const renderedLinks = links?.type === 'dropdown' ? renderDropdownLinks(links) : renderDefaultLinks(breakpoint, links);

  const { containerStyle: customLinksContainerStyle = {} } = links;

  return (
    <TextOverlayContainer css={containerStyle} data-testid='text-overlay-container'>
      <LinkContainer onClick={() => wcdTracking(trackingId)} target={target} to={href}>
        {brandName === Brands.BananaRepublic ? (
          <MobileContainer>
            <BackgroundImage {...image} />
            <MobileHeadlineContainer>
              {'content' in eyebrow && <EyebrowContainer css={eyebrow.style?.mobile}>{eyebrow.content}</EyebrowContainer>}
              {'content' in headline && <HeadlineContainer css={headline.style?.mobile}>{headline.content}</HeadlineContainer>}
            </MobileHeadlineContainer>
          </MobileContainer>
        ) : (
          <BackgroundImage {...image} />
        )}
      </LinkContainer>
      <ContentBlock
        body={body}
        breakpoint={breakpoint}
        css={textContainerStyle?.[breakpoint]}
        headline={headline}
        linksContainerStyle={customLinksContainerStyle}
        renderedLinks={renderedLinks}
      />
      {('smallImg' in graphicalElement || 'largeImg' in graphicalElement) && (
        <GraphicalElement customStyles={graphicalElement.style?.[breakpoint]} graphicalElement={graphicalElement} isDesktop={isDesktop} />
      )}
    </TextOverlayContainer>
  );
};

export default mapDataToProps(TextOverlay);
