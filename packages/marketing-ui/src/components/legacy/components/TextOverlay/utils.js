// @ts-nocheck
'use client';
import React, { Fragment } from 'react';
import LiveTextCTAButton from './LiveTextCTAButton';
import { safeWindow } from '../../helper/safeWindow';

export const getStyleForBreakpoint = (element, isLarge) => (isLarge ? element.style.desktop : element.style.mobile);

export const getBackgroundLink = ({ links }) => links?.content?.[0];

export const wcdTracking = trackingId => {
  const sWindow = safeWindow();
  sWindow?.wcdLib?.contentItemLinkWithFacet(sWindow, '', trackingId);
};

export const renderDefaultLinks = (links, isLarge) => (
  <Fragment>
    {links.content.map(link => (
      <LiveTextCTAButton key={`${link.href}-${link.trackingId}`} customClasses={links.buttonClasses} style={getStyleForBreakpoint(links, isLarge)} {...link} />
    ))}
  </Fragment>
);
