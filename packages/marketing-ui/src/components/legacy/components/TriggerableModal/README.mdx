# TriggerableModal

TriggerableModal is a component that contains one or more clickable triggers that will open a partial-page modal within the limits of its own area.

## Triggers

- The trigger(s) within TriggerableModal can contain any content accepted by the LayoutComponent. See the [documentation on the LayoutComponent](https://github.gapinc.com/wcd/marketing-ui/wiki/Layout-Component) for more information.

- Any children of the trigger become part of the same button area that opens a modal on click. For this reason, there are no "regions" in the trigger; any buttons or links that are content of the trigger will not have their own functionality. Any click event will be absorbed by the trigger, whose only function is to open the modal.

- ModalTrigger was created to be the trigger for this component. Details on the ModalTrigger subcomponent can be found [here](https://github.gapinc.com/ecomfrontend/core-ui/tree/main/packages/marketing-ui/components/ModalTrigger/README.md).

## Partial-page Modal

- The partial-page modal opened in TriggerableModal will only display within TriggerableModal's area.

- The only default contents of this component's modal is a close button 'x'.

- The modal for this component has no header area.

- Beyond these exclusions, the modal will accept any data that the [Modal](/?path=/story/common-modal--open) component accepts. For more details, see the Storybook documentation for "[Modal](/?path=/story/common-modal--open)".

## Component Height

- TriggerableModal _must_ specify a **`height`** property. Be sure to set a height that will fit asset content of the triggers and modal, as oversized content's overflow will be hidden.

  Ex:

```
  "desktopAndMobile": {
    "shouldDisplay": true,
    "data": {
      "className": "layoutChildClass",
      "style": {
        "height": "440px"
      },
      "components": [
        {
          "name": "ModalTrigger",
    ...
```

## Accessibility

### buttonAriaLabel

- `buttonAriaLabel` can be set individually for each `Modal Trigger`.

```
{
  "name": "ModalTrigger",
  "type": "sitewide",
  "data": {
    "buttonAriaLabel": "Click to open a modal to learn more about rockstar jeans",
    "layoutData": {
    ...
```

- This is for accessibility purposes and should describe in detail the action that clicking the trigger will perform.

- If `buttonAriaLabel` is set, this is the **only** information that a screen reader will receive about the trigger. Content within the `layoutData` of the trigger will not be read!

- If left unset, the `aria-label` will be left blank and a screen reader will read the content of the layoutData underneath. It is **important to note** that the only click event associated with any of the `layoutData` content within the trigger will cause the modal to pop up, so all trigger `layoutData` content should be explicit about what to expect on click.

- ✅ Some examples of explicit descriptions: _"Click to open a modal to learn more about Rockstar super-skinny jeans"_, _"Open Rockstar jeans modal"_

- 🚫 Some examples of what to avoid: _"Click here"_, _"Rockstar Skinny Jeans"_, _"Shop now"_

### modalCloseButtonAriaLabel

- The text to be read when the "close" button of each `Modal Trigger` is active on screenreaders.

```
{
  "name": "ModalTrigger",
  "type": "sitewide",
  "data": {
    "modalCloseButtonAriaLabel": "Close",
    ...
  }
  ...
}
```

## Example Use Cases

The first example of the TriggerableModal component's use is in the Old Navy Women's Denim banner menu experience, which you can read about [in the wiki](<https://github.gapinc.com/wcd/marketing-ui/wiki/Old-Navy-Women's-Denim-Banner-Experience-(2019)>).

## Example JSON

For an example JSON, visit the storybook entry for this component and check out the 'Controls' tab.
