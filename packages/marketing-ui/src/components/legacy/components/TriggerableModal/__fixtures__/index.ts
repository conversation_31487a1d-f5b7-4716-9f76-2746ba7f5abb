// @ts-nocheck
import { TriggerableModalProps } from '../types';

export const storyData: TriggerableModalProps = {
  data: {
    modalCloseButtonAriaLabel: 'Close',
    layoutData: {
      desktopAndMobile: {
        shouldDisplay: true,
        data: {
          className: 'layoutChildClass',
          style: {
            height: '440px',
            display: 'flex',
            alignItems: 'center',
            float: 'left',
          },
          components: [
            {
              name: 'ModalTrigger',
              type: 'sitewide',
              data: {
                buttonAriaLabel: 'Click to open a modal to learn more about rockstar jeans',
                layoutData: {
                  desktopAndMobile: {
                    shouldDisplay: true,
                    data: {
                      style: { display: 'block' },
                      components: [
                        {
                          name: 'Image',
                          type: 'sitewide',
                          data: {
                            smallImg: 'https://oldnavy.gap.com/Asset_Archive/ONWeb/content/0017/208/082/assets/190304_034A_W_DenimVI_US_04RockJegging.jpg',
                            largeImg: 'https://oldnavy.gap.com/Asset_Archive/ONWeb/content/0017/208/082/assets/190304_034A_W_DenimVI_US_04RockJegging.jpg',
                            altText: 'a lady in some clothes',
                            style: {
                              desktop: {},
                              mobile: {},
                            },
                          },
                        },
                        {
                          type: 'sitewide',
                          name: 'ComposableButton',
                          data: {
                            color: 'primary',
                            fullWidth: false,
                            size: 'small',
                            variant: 'solid',
                            buttonText: 'Click me first',
                          },
                        },
                      ],
                    },
                  },
                },
                modalLayoutData: {
                  desktopAndMobile: {
                    shouldDisplay: true,
                    data: {
                      components: [
                        {
                          name: 'Image',
                          type: 'sitewide',
                          data: {
                            smallImg: 'https://bananarepublic.gap.com/Asset_Archive/BRWeb/content/0016/881/583/assets/SM/BRSP190226_SITE_US_HP_02_IMG_SM.jpg',
                            largeImg: 'https://bananarepublic.gap.com/Asset_Archive/BRWeb/content/0016/881/583/assets/SM/BRSP190226_SITE_US_HP_02_IMG_SM.jpg',
                            altText: 'this is an image of a lady in some clothes from gap, there is no link within',
                            style: {
                              desktop: {},
                              mobile: {},
                            },
                          },
                        },
                      ],
                    },
                  },
                },
              },
            },
            {
              name: 'ModalTrigger',
              type: 'sitewide',
              data: {
                buttonAriaLabel: 'Click to open a modal to learn more about pop icon skinny jeans',
                layoutData: {
                  desktopAndMobile: {
                    shouldDisplay: true,
                    data: {
                      style: { display: 'block' },
                      components: [
                        {
                          name: 'Image',
                          type: 'sitewide',
                          data: {
                            smallImg: 'https://oldnavy.gap.com/Asset_Archive/ONWeb/content/0016/791/573/assets/190304_034A_W_DenimVI_US_02RockSSkinny.jpg',
                            largeImg: 'https://oldnavy.gap.com/Asset_Archive/ONWeb/content/0016/791/573/assets/190304_034A_W_DenimVI_US_02RockSSkinny.jpg',
                            altText: 'image alt text',
                            style: {},
                          },
                        },
                        {
                          type: 'sitewide',
                          name: 'ComposableButton',
                          data: {
                            color: 'primary',
                            fullWidth: false,
                            size: 'small',
                            variant: 'solid',
                            buttonText: 'Click me second',
                          },
                        },
                      ],
                    },
                  },
                },
                modalLayoutData: {
                  desktopAndMobile: {
                    shouldDisplay: true,
                    data: {
                      style: { display: 'block' },
                      components: [
                        {
                          name: 'Image',
                          type: 'sitewide',
                          data: {
                            smallImg: 'https://github.gapinc.com/pages/fhortop/proto1/Modal1.jpg',
                            largeImg: 'https://github.gapinc.com/pages/fhortop/proto1/Modal1.jpg',
                            altText: 'This is an image with a clickable area within it',
                            style: {},
                            imageMap: {
                              identifier: 'map_0',
                              areaAttributes: [
                                {
                                  shape: 'rect',
                                  coords: '0, 0, 50, 50',
                                  alt: 'this is the link within the image',
                                  mlink: 'PD_4',
                                  plink: 'new',
                                  href: 'https://oldnavy.gap.com/browse/info.do?cid=56526',
                                  target: '_blank',
                                },
                              ],
                            },
                          },
                        },
                      ],
                    },
                  },
                },
              },
            },
          ],
        },
      },
    },
  },
};
