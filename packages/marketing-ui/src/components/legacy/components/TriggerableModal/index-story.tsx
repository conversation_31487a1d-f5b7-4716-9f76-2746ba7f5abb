// @ts-nocheck
'use client';
import { ComponentMeta, ComponentStory } from '@storybook/react';
import React from 'react';
import TriggerableModal from '.';
import JsonDynamicMarketing from '../../../json-marketing';
import CmsDynamicMarketing from '../../../legacy-mui-entry';
import { MarketingProvider } from '../../../marketing-provider';
import README from './README.mdx';
import { storyData } from './__fixtures__';

export default {
  title: 'Common/JSON Components (Marketing)/TriggerableModal',
  component: TriggerableModal,
  parameters: {
    docs: {
      page: README,
    },
    eyes: { include: false },
  },
  tags: ['exclude'],
} as ComponentMeta<typeof TriggerableModal>;

const marketingData = {
  contentData: {
    contentItems: [],
  },
};

const TriggerableModalStory: ComponentStory<typeof TriggerableModal> = args => (
  <div css={{ border: '1px red solid', marginTop: '20px' }}>
    <MarketingProvider value={marketingData} jsonMarketingComponent={JsonDynamicMarketing} cmsMarketingComponent={CmsDynamicMarketing}>
      <TriggerableModal {...args} />
    </MarketingProvider>
  </div>
);

export const Default = TriggerableModalStory.bind({});
Default.storyName = 'Default example';
Default.args = { ...storyData };
