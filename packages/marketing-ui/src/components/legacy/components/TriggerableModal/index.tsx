// @ts-nocheck
'use client';
import React, { createContext, useState } from 'react';
import { Modal } from '@ecom-next/core/legacy/modal';
import { CSSObject } from '@ecom-next/core/react-stitch';
import LayoutComponent from '../LayoutComponent';
import { ModalDataProps, TriggerableModalContextProps, TriggerableModalProps } from './types';

export const TriggerableModalContext = createContext<TriggerableModalContextProps>({
  openModal: () => {},
  openModalWithIframe: () => {},
});

const useModalState = (initialIsOpen?: boolean) => {
  const [isOpen, setIsOpen] = useState<boolean>(!!initialIsOpen);
  const [modalLayoutData, setModalLayoutData] = useState<ModalDataProps['layoutData'] | null>(null);
  const [modalUrl, setModalUrl] = useState<string | null>(null);

  const openModal = (newModalLayoutData: ModalDataProps['layoutData']) => {
    setIsOpen(true);
    setModalLayoutData(newModalLayoutData);
  };

  const openModalWithIframe = (newModalUrl: string) => {
    setIsOpen(true);
    setModalUrl(newModalUrl);
  };

  const closeModal = () => {
    setIsOpen(false);
  };

  return [isOpen, modalUrl, modalLayoutData, openModal, closeModal, openModalWithIframe];
};

const TriggerableModal = ({ data, children }: TriggerableModalProps) => {
  const [isOpen, modalUrl, modalLayoutData, openModal, closeModal, openModalWithIframe] = useModalState(data?.isOpen);

  const {
    modalCloseButtonAriaLabel,
    noHeader = true,
    isPartialPage = true,
    containerStyle = {},
    title,
    modalSize,
    modalStyles,
    modalClasses,
    layoutData,
  } = data;
  const inlineStyle: CSSObject = {
    position: 'relative',
    overflow: 'hidden',
  };

  return (
    <TriggerableModalContext.Provider value={{ openModal, openModalWithIframe }}>
      <div css={{ ...inlineStyle, ...containerStyle }}>
        <Modal
          closeButtonAriaLabel={modalCloseButtonAriaLabel}
          isOpen={isOpen}
          isPartialPage={isPartialPage}
          modalSize={modalSize}
          noHeader={noHeader}
          onClose={closeModal}
          title={title}
        >
          <div className={modalClasses} css={modalStyles}>
            {!modalUrl ? (
              <LayoutComponent
                // @ts-ignore
                className='modalContent'
                data={modalLayoutData}
              />
            ) : (
              <iframe css={{ padding: '0 1em', boxSizing: 'border-box' }} src={modalUrl} title={title} />
            )}
          </div>
        </Modal>
        {children || (
          // @ts-ignore
          <LayoutComponent className='triggerContent' data={layoutData} />
        )}
      </div>
    </TriggerableModalContext.Provider>
  );
};

export default TriggerableModal;
