// @ts-nocheck
'use client';
import { CSSObject } from '@ecom-next/core/react-stitch';
import React from 'react';

export interface ModalDataProps {
  containerStyle?: CSSObject;
  isOpen?: boolean;
  isPartialPage?: boolean;
  // @ts-ignore
  layoutData?: any;
  modalClasses?: string;
  modalCloseButtonAriaLabel: string;
  modalSize?: 'mini' | 'standard' | 'max';
  modalStyles?: CSSObject;
  noHeader?: boolean;
  title?: string;
}

export interface TriggerableModalProps {
  children?: React.ReactNode;
  data: ModalDataProps;
}

export type TriggerableModalContextProps = {
  // @ts-ignore
  openModal: (newModalLayoutData: any) => void;
  openModalWithIframe: (newModalUrl: string) => void;
};
