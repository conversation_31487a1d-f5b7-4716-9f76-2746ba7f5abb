# Typography

Typography is a component that defines styles and scaling behavior for text. Styles and scaling behavior have been defined for each brand by UX.

## Default Behavior

- By default, scaling is not enabled, and each typography style has 1 desktop size and 1 mobile size.

## Technical Notes

### Scaling

- The default _100% scale point_ for `Typography` is `1280px` for desktop and `375px` for mobile (except for `BR`).
  - This means that when scaling is enabled, the scaling typography is the same as the static size at `1280px` window width, but smaller below `1280px` and larger above `1280px`.
    You can see this in action <a href="https://core-ui-main.apps.cfplatform.dev.azeus.gaptech.com/?path=/story/mui_common-json-components-marketing-typography-scaling--style-guide" target="_blank">in this storybook example</a> by turning “enable font scaling” on and off, and changing the window size.
  - For `BR`, the _100% scale point_ for desktop is `1920px` and is set in the config directory.
- There is an optional parameter, `desktopScalingPoint`, that lets the user input a new _100% scale point_ for desktop. If provided, the scaling Typography will be the same size as the static size Typography at the `desktopScalingPoint` instead of 1280. **Be aware that this will change the behavior of a scaling Typography instance, so use with caution.**

#### Scaling Typography Example with default 100% scaling point

```
<Typography
  variant="body2"
  scalableText = {
      enable: true,
      parentMaxWidth={1280}
      minSizePx={11}
    };
>
  Test
</Typography>
```

#### Scaling Typography Example with custom desktopScalingPoint

```
<Typography
  variant="headlineAlt1"
  scalableText = {
      enable: true,
      desktopScalingPoint: 1440,
    };
>
  Test
</Typography>
```

## Gap Redesign 2024

- The Gap Redesign 2024 typography includes new tokens for the variants (`F2`, `F1`, `F0`, `FN1`, `Promo2`)
- Its styles are defined in the `gap-redesign-2024` config file. This object contains the typography styles for the Gap Redesign 2024 brand.

```
<Typography
  variant="F2"
>
  Test
</Typography>
```

## BR / BRFS Typography

- The Headline and Promo typography styles in the BR CMS design system are flipped from how they appear in `@core-ui/react-stitch`. In the BR CMS style guide the Headlines [1-7] and Promo [1] are the font Banana Serif. In `react-stitch` those variants appear as the sans serif font, Lynstone.
- It is expected that `HeadlineAlt4` and `Subhead1` variants use a different font-family from the other variants.
- It is expected that `HeadlineAlt4` is larger than `HeadlineAlt1`
- As mentioned above, the _100% scale point_ for `BR` is `1920px` for desktop. For mobile, it uses the same as other brands.
