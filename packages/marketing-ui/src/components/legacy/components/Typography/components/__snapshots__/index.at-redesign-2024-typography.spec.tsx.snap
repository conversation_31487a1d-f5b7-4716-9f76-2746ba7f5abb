// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`at-redesign-2024-typography - Typography Variants 'Helpers' Snapshots Body for small breakpoint renders customStyles state correctly Athleta 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: black;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 16px;
  line-height: 1.625;
  letter-spacing: 0.8px;
  font-weight: 500;
}

<div
    style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`at-redesign-2024-typography - Typography Variants 'Helpers' Snapshots Body for small breakpoint renders customStyles state correctly Athleta in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: black;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 16px;
  line-height: 1.625;
  letter-spacing: 0.8px;
  font-weight: 500;
}

<div
    style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`at-redesign-2024-typography - Typography Variants 'Helpers' Snapshots Body for small breakpoint renders default state correctly Athleta 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 16px;
  line-height: 1.625;
  letter-spacing: 0.8px;
  font-weight: 500;
}

<div
    style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`at-redesign-2024-typography - Typography Variants 'Helpers' Snapshots Body for small breakpoint renders default state correctly Athleta in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 16px;
  line-height: 1.625;
  letter-spacing: 0.8px;
  font-weight: 500;
}

<div
    style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`at-redesign-2024-typography - Typography Variants 'Helpers' Snapshots Body for small breakpoint renders scalable text state correctly Athleta 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, 4.266666666666667vw);
  line-height: 1.625;
  letter-spacing: 0.21333333333333335vw;
  font-weight: 500;
}

<div
    style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`at-redesign-2024-typography - Typography Variants 'Helpers' Snapshots Body for small breakpoint renders scalable text state correctly Athleta in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, 4.266666666666667vw);
  line-height: 1.625;
  letter-spacing: 0.21333333333333335vw;
  font-weight: 500;
}

<div
    style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`at-redesign-2024-typography - Typography Variants 'Helpers' Snapshots Body for small breakpoint renders scalable text with a max width state correctly Athleta 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(4.266666666666667vw, 46.93333333333333px));
  line-height: 1.625;
  letter-spacing: min(0.21333333333333335vw, 2.3466666666666667px);
  font-weight: 500;
}

<div
    style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`at-redesign-2024-typography - Typography Variants 'Helpers' Snapshots Body for small breakpoint renders scalable text with a max width state correctly Athleta in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(4.266666666666667vw, 46.93333333333333px));
  line-height: 1.625;
  letter-spacing: min(0.21333333333333335vw, 2.3466666666666667px);
  font-weight: 500;
}

<div
    style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`at-redesign-2024-typography - Typography Variants 'Helpers' Snapshots Body for small breakpoint renders variant state correctly Athleta 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 14px;
  line-height: 1.4285714285714286;
  letter-spacing: 0px;
  font-weight: 500;
}

<div
    style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`at-redesign-2024-typography - Typography Variants 'Helpers' Snapshots Body for small breakpoint renders variant state correctly Athleta in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 14px;
  line-height: 1.4285714285714286;
  letter-spacing: 0px;
  font-weight: 500;
}

<div
    style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`at-redesign-2024-typography - Typography Variants 'Helpers' Snapshots Body for x-large breakpoint renders customStyles state correctly Athleta 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: black;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 18px;
  line-height: 1.5555555555555556;
  letter-spacing: 0.9px;
  font-weight: 500;
}

<div
    style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`at-redesign-2024-typography - Typography Variants 'Helpers' Snapshots Body for x-large breakpoint renders customStyles state correctly Athleta in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: black;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 18px;
  line-height: 1.5555555555555556;
  letter-spacing: 0.9px;
  font-weight: 500;
}

<div
    style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`at-redesign-2024-typography - Typography Variants 'Helpers' Snapshots Body for x-large breakpoint renders default state correctly Athleta 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 18px;
  line-height: 1.5555555555555556;
  letter-spacing: 0.9px;
  font-weight: 500;
}

<div
    style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`at-redesign-2024-typography - Typography Variants 'Helpers' Snapshots Body for x-large breakpoint renders default state correctly Athleta in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 18px;
  line-height: 1.5555555555555556;
  letter-spacing: 0.9px;
  font-weight: 500;
}

<div
    style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`at-redesign-2024-typography - Typography Variants 'Helpers' Snapshots Body for x-large breakpoint renders scalable text state correctly Athleta 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, 1.40625vw);
  line-height: 1.5555555555555556;
  letter-spacing: 0.0703125vw;
  font-weight: 500;
}

<div
    style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`at-redesign-2024-typography - Typography Variants 'Helpers' Snapshots Body for x-large breakpoint renders scalable text state correctly Athleta in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, 1.40625vw);
  line-height: 1.5555555555555556;
  letter-spacing: 0.0703125vw;
  font-weight: 500;
}

<div
    style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`at-redesign-2024-typography - Typography Variants 'Helpers' Snapshots Body for x-large breakpoint renders scalable text with a max width state correctly Athleta 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(1.40625vw, 15.46875px));
  line-height: 1.5555555555555556;
  letter-spacing: min(0.0703125vw, 0.7734375px);
  font-weight: 500;
}

<div
    style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`at-redesign-2024-typography - Typography Variants 'Helpers' Snapshots Body for x-large breakpoint renders scalable text with a max width state correctly Athleta in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(1.40625vw, 15.46875px));
  line-height: 1.5555555555555556;
  letter-spacing: min(0.0703125vw, 0.7734375px);
  font-weight: 500;
}

<div
    style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`at-redesign-2024-typography - Typography Variants 'Helpers' Snapshots Body for x-large breakpoint renders variant state correctly Athleta 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 16px;
  line-height: 1.25;
  letter-spacing: 0px;
  font-weight: 500;
}

<div
    style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`at-redesign-2024-typography - Typography Variants 'Helpers' Snapshots Body for x-large breakpoint renders variant state correctly Athleta in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 16px;
  line-height: 1.25;
  letter-spacing: 0px;
  font-weight: 500;
}

<div
    style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`at-redesign-2024-typography - Typography Variants 'Helpers' Snapshots Eyebrow for small breakpoint renders customStyles state correctly Athleta 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: black;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 2;
  letter-spacing: 1.2px;
  font-weight: 700;
  text-transform: uppercase;
}

<div
    style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`at-redesign-2024-typography - Typography Variants 'Helpers' Snapshots Eyebrow for small breakpoint renders customStyles state correctly Athleta in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: black;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 2;
  letter-spacing: 1.2px;
  font-weight: 700;
  text-transform: uppercase;
}

<div
    style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`at-redesign-2024-typography - Typography Variants 'Helpers' Snapshots Eyebrow for small breakpoint renders default state correctly Athleta 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 2;
  letter-spacing: 1.2px;
  font-weight: 700;
  text-transform: uppercase;
}

<div
    style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`at-redesign-2024-typography - Typography Variants 'Helpers' Snapshots Eyebrow for small breakpoint renders default state correctly Athleta in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 2;
  letter-spacing: 1.2px;
  font-weight: 700;
  text-transform: uppercase;
}

<div
    style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`at-redesign-2024-typography - Typography Variants 'Helpers' Snapshots Eyebrow for small breakpoint renders scalable text state correctly Athleta 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, 3.2vw);
  line-height: 2;
  letter-spacing: 0.31999999999999995vw;
  font-weight: 700;
  text-transform: uppercase;
}

<div
    style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`at-redesign-2024-typography - Typography Variants 'Helpers' Snapshots Eyebrow for small breakpoint renders scalable text state correctly Athleta in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, 3.2vw);
  line-height: 2;
  letter-spacing: 0.31999999999999995vw;
  font-weight: 700;
  text-transform: uppercase;
}

<div
    style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`at-redesign-2024-typography - Typography Variants 'Helpers' Snapshots Eyebrow for small breakpoint renders scalable text with a max width state correctly Athleta 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(3.2vw, 35.199999999999996px));
  line-height: 2;
  letter-spacing: min(0.31999999999999995vw, 3.5199999999999996px);
  font-weight: 700;
  text-transform: uppercase;
}

<div
    style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`at-redesign-2024-typography - Typography Variants 'Helpers' Snapshots Eyebrow for small breakpoint renders scalable text with a max width state correctly Athleta in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(3.2vw, 35.199999999999996px));
  line-height: 2;
  letter-spacing: min(0.31999999999999995vw, 3.5199999999999996px);
  font-weight: 700;
  text-transform: uppercase;
}

<div
    style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`at-redesign-2024-typography - Typography Variants 'Helpers' Snapshots Eyebrow for small breakpoint renders variant state correctly Athleta 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 11px;
  line-height: 2;
  letter-spacing: 1.1px;
  font-weight: 700;
  text-transform: uppercase;
}

<div
    style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`at-redesign-2024-typography - Typography Variants 'Helpers' Snapshots Eyebrow for small breakpoint renders variant state correctly Athleta in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 11px;
  line-height: 2;
  letter-spacing: 1.1px;
  font-weight: 700;
  text-transform: uppercase;
}

<div
    style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`at-redesign-2024-typography - Typography Variants 'Helpers' Snapshots Eyebrow for x-large breakpoint renders customStyles state correctly Athleta 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: black;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 17px;
  line-height: 1.588235294117647;
  letter-spacing: 1.7px;
  font-weight: 700;
  text-transform: uppercase;
}

<div
    style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`at-redesign-2024-typography - Typography Variants 'Helpers' Snapshots Eyebrow for x-large breakpoint renders customStyles state correctly Athleta in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: black;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 17px;
  line-height: 1.588235294117647;
  letter-spacing: 1.7px;
  font-weight: 700;
  text-transform: uppercase;
}

<div
    style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`at-redesign-2024-typography - Typography Variants 'Helpers' Snapshots Eyebrow for x-large breakpoint renders default state correctly Athleta 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 17px;
  line-height: 1.588235294117647;
  letter-spacing: 1.7px;
  font-weight: 700;
  text-transform: uppercase;
}

<div
    style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`at-redesign-2024-typography - Typography Variants 'Helpers' Snapshots Eyebrow for x-large breakpoint renders default state correctly Athleta in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 17px;
  line-height: 1.588235294117647;
  letter-spacing: 1.7px;
  font-weight: 700;
  text-transform: uppercase;
}

<div
    style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`at-redesign-2024-typography - Typography Variants 'Helpers' Snapshots Eyebrow for x-large breakpoint renders scalable text state correctly Athleta 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, 1.328125vw);
  line-height: 1.588235294117647;
  letter-spacing: 0.1328125vw;
  font-weight: 700;
  text-transform: uppercase;
}

<div
    style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`at-redesign-2024-typography - Typography Variants 'Helpers' Snapshots Eyebrow for x-large breakpoint renders scalable text state correctly Athleta in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, 1.328125vw);
  line-height: 1.588235294117647;
  letter-spacing: 0.1328125vw;
  font-weight: 700;
  text-transform: uppercase;
}

<div
    style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`at-redesign-2024-typography - Typography Variants 'Helpers' Snapshots Eyebrow for x-large breakpoint renders scalable text with a max width state correctly Athleta 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(1.328125vw, 14.609375px));
  line-height: 1.588235294117647;
  letter-spacing: min(0.1328125vw, 1.4609375px);
  font-weight: 700;
  text-transform: uppercase;
}

<div
    style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`at-redesign-2024-typography - Typography Variants 'Helpers' Snapshots Eyebrow for x-large breakpoint renders scalable text with a max width state correctly Athleta in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(1.328125vw, 14.609375px));
  line-height: 1.588235294117647;
  letter-spacing: min(0.1328125vw, 1.4609375px);
  font-weight: 700;
  text-transform: uppercase;
}

<div
    style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`at-redesign-2024-typography - Typography Variants 'Helpers' Snapshots Eyebrow for x-large breakpoint renders variant state correctly Athleta 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 16px;
  line-height: 1.75;
  letter-spacing: 1.6px;
  font-weight: 700;
  text-transform: uppercase;
}

<div
    style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`at-redesign-2024-typography - Typography Variants 'Helpers' Snapshots Eyebrow for x-large breakpoint renders variant state correctly Athleta in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 16px;
  line-height: 1.75;
  letter-spacing: 1.6px;
  font-weight: 700;
  text-transform: uppercase;
}

<div
    style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`at-redesign-2024-typography - Typography Variants 'Helpers' Snapshots Headline Alt 4 for small breakpoint renders variant state correctly Athleta 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-soehne),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 24px;
  line-height: 1.4166666666666667;
  letter-spacing: -0.3px;
  font-weight: 400;
}

<div
    style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      />
    </div>
  </div>
</DocumentFragment>
`;

exports[`at-redesign-2024-typography - Typography Variants 'Helpers' Snapshots Headline Alt 4 for small breakpoint renders variant state correctly Athleta in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-soehne),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 24px;
  line-height: 1.4166666666666667;
  letter-spacing: -0.3px;
  font-weight: 400;
}

<div
    style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      />
    </div>
  </div>
</DocumentFragment>
`;

exports[`at-redesign-2024-typography - Typography Variants 'Helpers' Snapshots Headline Alt 4 for x-large breakpoint renders variant state correctly Athleta 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-soehne),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 34px;
  line-height: 1;
  letter-spacing: -0.4px;
  font-weight: 400;
}

<div
    style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      />
    </div>
  </div>
</DocumentFragment>
`;

exports[`at-redesign-2024-typography - Typography Variants 'Helpers' Snapshots Headline Alt 4 for x-large breakpoint renders variant state correctly Athleta in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-soehne),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 34px;
  line-height: 1;
  letter-spacing: -0.4px;
  font-weight: 400;
}

<div
    style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      />
    </div>
  </div>
</DocumentFragment>
`;

exports[`at-redesign-2024-typography - Typography Variants 'Helpers' Snapshots Headline Alt 5 for small breakpoint renders variant state correctly Athleta 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-soehne),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 14px;
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

<div
    style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      />
    </div>
  </div>
</DocumentFragment>
`;

exports[`at-redesign-2024-typography - Typography Variants 'Helpers' Snapshots Headline Alt 5 for small breakpoint renders variant state correctly Athleta in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-soehne),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 14px;
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

<div
    style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      />
    </div>
  </div>
</DocumentFragment>
`;

exports[`at-redesign-2024-typography - Typography Variants 'Helpers' Snapshots Headline Alt 5 for x-large breakpoint renders variant state correctly Athleta 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-soehne),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 16px;
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

<div
    style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      />
    </div>
  </div>
</DocumentFragment>
`;

exports[`at-redesign-2024-typography - Typography Variants 'Helpers' Snapshots Headline Alt 5 for x-large breakpoint renders variant state correctly Athleta in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-soehne),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 16px;
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

<div
    style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      />
    </div>
  </div>
</DocumentFragment>
`;

exports[`at-redesign-2024-typography - Typography Variants 'Helpers' Snapshots Headline Alt 6 for small breakpoint renders variant state correctly Athleta 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-soehne),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 14px;
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

<div
    style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      />
    </div>
  </div>
</DocumentFragment>
`;

exports[`at-redesign-2024-typography - Typography Variants 'Helpers' Snapshots Headline Alt 6 for small breakpoint renders variant state correctly Athleta in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-soehne),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 14px;
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

<div
    style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      />
    </div>
  </div>
</DocumentFragment>
`;

exports[`at-redesign-2024-typography - Typography Variants 'Helpers' Snapshots Headline Alt 6 for x-large breakpoint renders variant state correctly Athleta 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-soehne),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 16px;
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

<div
    style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      />
    </div>
  </div>
</DocumentFragment>
`;

exports[`at-redesign-2024-typography - Typography Variants 'Helpers' Snapshots Headline Alt 6 for x-large breakpoint renders variant state correctly Athleta in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-soehne),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 16px;
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

<div
    style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      />
    </div>
  </div>
</DocumentFragment>
`;

exports[`at-redesign-2024-typography - Typography Variants 'Helpers' Snapshots Headline Alt 7 for small breakpoint renders variant state correctly Athleta 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-soehne),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 14px;
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

<div
    style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      />
    </div>
  </div>
</DocumentFragment>
`;

exports[`at-redesign-2024-typography - Typography Variants 'Helpers' Snapshots Headline Alt 7 for small breakpoint renders variant state correctly Athleta in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-soehne),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 14px;
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

<div
    style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      />
    </div>
  </div>
</DocumentFragment>
`;

exports[`at-redesign-2024-typography - Typography Variants 'Helpers' Snapshots Headline Alt 7 for x-large breakpoint renders variant state correctly Athleta 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-soehne),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 16px;
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

<div
    style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      />
    </div>
  </div>
</DocumentFragment>
`;

exports[`at-redesign-2024-typography - Typography Variants 'Helpers' Snapshots Headline Alt 7 for x-large breakpoint renders variant state correctly Athleta in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-soehne),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 16px;
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

<div
    style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      />
    </div>
  </div>
</DocumentFragment>
`;

exports[`at-redesign-2024-typography - Typography Variants 'Helpers' Snapshots Headline Alt for small breakpoint renders customStyles state correctly Athleta 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: black;
  font-family: var(--font-soehne),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 64px;
  line-height: 1;
  letter-spacing: -0.3px;
  font-weight: 400;
}

<div
    style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`at-redesign-2024-typography - Typography Variants 'Helpers' Snapshots Headline Alt for small breakpoint renders customStyles state correctly Athleta in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: black;
  font-family: var(--font-soehne),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 64px;
  line-height: 1;
  letter-spacing: -0.3px;
  font-weight: 400;
}

<div
    style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`at-redesign-2024-typography - Typography Variants 'Helpers' Snapshots Headline Alt for small breakpoint renders default state correctly Athleta 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-soehne),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 64px;
  line-height: 1;
  letter-spacing: -0.3px;
  font-weight: 400;
}

<div
    style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`at-redesign-2024-typography - Typography Variants 'Helpers' Snapshots Headline Alt for small breakpoint renders default state correctly Athleta in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-soehne),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 64px;
  line-height: 1;
  letter-spacing: -0.3px;
  font-weight: 400;
}

<div
    style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`at-redesign-2024-typography - Typography Variants 'Helpers' Snapshots Headline Alt for small breakpoint renders scalable text state correctly Athleta 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-soehne),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(13px, 17.066666666666666vw);
  line-height: 1;
  letter-spacing: -0.07999999999999999vw;
  font-weight: 400;
}

<div
    style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`at-redesign-2024-typography - Typography Variants 'Helpers' Snapshots Headline Alt for small breakpoint renders scalable text state correctly Athleta in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-soehne),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(13px, 17.066666666666666vw);
  line-height: 1;
  letter-spacing: -0.07999999999999999vw;
  font-weight: 400;
}

<div
    style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`at-redesign-2024-typography - Typography Variants 'Helpers' Snapshots Headline Alt for small breakpoint renders scalable text with a max width state correctly Athleta 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-soehne),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(13px, min(17.066666666666666vw, 187.73333333333332px));
  line-height: 1;
  letter-spacing: min(-0.07999999999999999vw, -0.8799999999999999px);
  font-weight: 400;
}

<div
    style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`at-redesign-2024-typography - Typography Variants 'Helpers' Snapshots Headline Alt for small breakpoint renders scalable text with a max width state correctly Athleta in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-soehne),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(13px, min(17.066666666666666vw, 187.73333333333332px));
  line-height: 1;
  letter-spacing: min(-0.07999999999999999vw, -0.8799999999999999px);
  font-weight: 400;
}

<div
    style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`at-redesign-2024-typography - Typography Variants 'Helpers' Snapshots Headline Alt for small breakpoint renders variant state correctly Athleta 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-soehne),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 54px;
  line-height: 1;
  letter-spacing: -0.3px;
  font-weight: 400;
}

<div
    style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`at-redesign-2024-typography - Typography Variants 'Helpers' Snapshots Headline Alt for small breakpoint renders variant state correctly Athleta in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-soehne),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 54px;
  line-height: 1;
  letter-spacing: -0.3px;
  font-weight: 400;
}

<div
    style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`at-redesign-2024-typography - Typography Variants 'Helpers' Snapshots Headline Alt for x-large breakpoint renders customStyles state correctly Athleta 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: black;
  font-family: var(--font-soehne),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 124px;
  line-height: 1;
  letter-spacing: -0.4px;
  font-weight: 400;
}

<div
    style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`at-redesign-2024-typography - Typography Variants 'Helpers' Snapshots Headline Alt for x-large breakpoint renders customStyles state correctly Athleta in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: black;
  font-family: var(--font-soehne),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 124px;
  line-height: 1;
  letter-spacing: -0.4px;
  font-weight: 400;
}

<div
    style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`at-redesign-2024-typography - Typography Variants 'Helpers' Snapshots Headline Alt for x-large breakpoint renders default state correctly Athleta 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-soehne),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 124px;
  line-height: 1;
  letter-spacing: -0.4px;
  font-weight: 400;
}

<div
    style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`at-redesign-2024-typography - Typography Variants 'Helpers' Snapshots Headline Alt for x-large breakpoint renders default state correctly Athleta in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-soehne),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 124px;
  line-height: 1;
  letter-spacing: -0.4px;
  font-weight: 400;
}

<div
    style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`at-redesign-2024-typography - Typography Variants 'Helpers' Snapshots Headline Alt for x-large breakpoint renders scalable text state correctly Athleta 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-soehne),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, 9.6875vw);
  line-height: 1;
  letter-spacing: -0.03125vw;
  font-weight: 400;
}

<div
    style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`at-redesign-2024-typography - Typography Variants 'Helpers' Snapshots Headline Alt for x-large breakpoint renders scalable text state correctly Athleta in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-soehne),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, 9.6875vw);
  line-height: 1;
  letter-spacing: -0.03125vw;
  font-weight: 400;
}

<div
    style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`at-redesign-2024-typography - Typography Variants 'Helpers' Snapshots Headline Alt for x-large breakpoint renders scalable text with a max width state correctly Athleta 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-soehne),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(9.6875vw, 106.5625px));
  line-height: 1;
  letter-spacing: min(-0.03125vw, -0.34375px);
  font-weight: 400;
}

<div
    style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`at-redesign-2024-typography - Typography Variants 'Helpers' Snapshots Headline Alt for x-large breakpoint renders scalable text with a max width state correctly Athleta in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-soehne),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(9.6875vw, 106.5625px));
  line-height: 1;
  letter-spacing: min(-0.03125vw, -0.34375px);
  font-weight: 400;
}

<div
    style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`at-redesign-2024-typography - Typography Variants 'Helpers' Snapshots Headline Alt for x-large breakpoint renders variant state correctly Athleta 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-soehne),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 84px;
  line-height: 1;
  letter-spacing: -0.4px;
  font-weight: 400;
}

<div
    style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`at-redesign-2024-typography - Typography Variants 'Helpers' Snapshots Headline Alt for x-large breakpoint renders variant state correctly Athleta in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-soehne),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 84px;
  line-height: 1;
  letter-spacing: -0.4px;
  font-weight: 400;
}

<div
    style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`at-redesign-2024-typography - Typography Variants 'Helpers' Snapshots Headline for small breakpoint renders customStyles state correctly Athleta 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: black;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 36px;
  line-height: 1;
  letter-spacing: 1.37px;
  text-transform: uppercase;
  font-weight: 500;
}

<div
    style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`at-redesign-2024-typography - Typography Variants 'Helpers' Snapshots Headline for small breakpoint renders customStyles state correctly Athleta in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: black;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 36px;
  line-height: 1;
  letter-spacing: 1.37px;
  text-transform: uppercase;
  font-weight: 500;
}

<div
    style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`at-redesign-2024-typography - Typography Variants 'Helpers' Snapshots Headline for small breakpoint renders default state correctly Athleta 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 36px;
  line-height: 1;
  letter-spacing: 1.37px;
  text-transform: uppercase;
  font-weight: 500;
}

<div
    style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`at-redesign-2024-typography - Typography Variants 'Helpers' Snapshots Headline for small breakpoint renders default state correctly Athleta in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 36px;
  line-height: 1;
  letter-spacing: 1.37px;
  text-transform: uppercase;
  font-weight: 500;
}

<div
    style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`at-redesign-2024-typography - Typography Variants 'Helpers' Snapshots Headline for small breakpoint renders scalable text state correctly Athleta 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(13px, 9.6vw);
  line-height: 1;
  letter-spacing: 0.36533333333333334vw;
  text-transform: uppercase;
  font-weight: 500;
}

<div
    style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`at-redesign-2024-typography - Typography Variants 'Helpers' Snapshots Headline for small breakpoint renders scalable text state correctly Athleta in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(13px, 9.6vw);
  line-height: 1;
  letter-spacing: 0.36533333333333334vw;
  text-transform: uppercase;
  font-weight: 500;
}

<div
    style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`at-redesign-2024-typography - Typography Variants 'Helpers' Snapshots Headline for small breakpoint renders scalable text with a max width state correctly Athleta 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(13px, min(9.6vw, 105.6px));
  line-height: 1;
  letter-spacing: min(0.36533333333333334vw, 4.018666666666666px);
  text-transform: uppercase;
  font-weight: 500;
}

<div
    style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`at-redesign-2024-typography - Typography Variants 'Helpers' Snapshots Headline for small breakpoint renders scalable text with a max width state correctly Athleta in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(13px, min(9.6vw, 105.6px));
  line-height: 1;
  letter-spacing: min(0.36533333333333334vw, 4.018666666666666px);
  text-transform: uppercase;
  font-weight: 500;
}

<div
    style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`at-redesign-2024-typography - Typography Variants 'Helpers' Snapshots Headline for small breakpoint renders variant state correctly Athleta 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 34px;
  line-height: 1;
  letter-spacing: 1.7px;
  text-transform: uppercase;
  font-weight: 500;
}

<div
    style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`at-redesign-2024-typography - Typography Variants 'Helpers' Snapshots Headline for small breakpoint renders variant state correctly Athleta in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 34px;
  line-height: 1;
  letter-spacing: 1.7px;
  text-transform: uppercase;
  font-weight: 500;
}

<div
    style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`at-redesign-2024-typography - Typography Variants 'Helpers' Snapshots Headline for x-large breakpoint renders customStyles state correctly Athleta 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: black;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 60px;
  line-height: 1;
  letter-spacing: 2.4px;
  text-transform: uppercase;
  font-weight: 500;
}

<div
    style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`at-redesign-2024-typography - Typography Variants 'Helpers' Snapshots Headline for x-large breakpoint renders customStyles state correctly Athleta in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: black;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 60px;
  line-height: 1;
  letter-spacing: 2.4px;
  text-transform: uppercase;
  font-weight: 500;
}

<div
    style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`at-redesign-2024-typography - Typography Variants 'Helpers' Snapshots Headline for x-large breakpoint renders default state correctly Athleta 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 60px;
  line-height: 1;
  letter-spacing: 2.4px;
  text-transform: uppercase;
  font-weight: 500;
}

<div
    style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`at-redesign-2024-typography - Typography Variants 'Helpers' Snapshots Headline for x-large breakpoint renders default state correctly Athleta in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 60px;
  line-height: 1;
  letter-spacing: 2.4px;
  text-transform: uppercase;
  font-weight: 500;
}

<div
    style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`at-redesign-2024-typography - Typography Variants 'Helpers' Snapshots Headline for x-large breakpoint renders scalable text state correctly Athleta 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, 4.6875vw);
  line-height: 1;
  letter-spacing: 0.1875vw;
  text-transform: uppercase;
  font-weight: 500;
}

<div
    style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`at-redesign-2024-typography - Typography Variants 'Helpers' Snapshots Headline for x-large breakpoint renders scalable text state correctly Athleta in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, 4.6875vw);
  line-height: 1;
  letter-spacing: 0.1875vw;
  text-transform: uppercase;
  font-weight: 500;
}

<div
    style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`at-redesign-2024-typography - Typography Variants 'Helpers' Snapshots Headline for x-large breakpoint renders scalable text with a max width state correctly Athleta 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(4.6875vw, 51.5625px));
  line-height: 1;
  letter-spacing: min(0.1875vw, 2.0625px);
  text-transform: uppercase;
  font-weight: 500;
}

<div
    style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`at-redesign-2024-typography - Typography Variants 'Helpers' Snapshots Headline for x-large breakpoint renders scalable text with a max width state correctly Athleta in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(4.6875vw, 51.5625px));
  line-height: 1;
  letter-spacing: min(0.1875vw, 2.0625px);
  text-transform: uppercase;
  font-weight: 500;
}

<div
    style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`at-redesign-2024-typography - Typography Variants 'Helpers' Snapshots Headline for x-large breakpoint renders variant state correctly Athleta 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 56px;
  line-height: 1;
  letter-spacing: 2.8px;
  text-transform: uppercase;
  font-weight: 500;
}

<div
    style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`at-redesign-2024-typography - Typography Variants 'Helpers' Snapshots Headline for x-large breakpoint renders variant state correctly Athleta in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 56px;
  line-height: 1;
  letter-spacing: 2.8px;
  text-transform: uppercase;
  font-weight: 500;
}

<div
    style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`at-redesign-2024-typography - Typography Variants 'Helpers' Snapshots Promo1 for small breakpoint renders customStyles state correctly Athleta 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: black;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 60px;
  line-height: 1;
  letter-spacing: 0px;
  font-weight: 600;
  text-transform: uppercase;
}

<div
    style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`at-redesign-2024-typography - Typography Variants 'Helpers' Snapshots Promo1 for small breakpoint renders customStyles state correctly Athleta in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: black;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 60px;
  line-height: 1;
  letter-spacing: 0px;
  font-weight: 600;
  text-transform: uppercase;
}

<div
    style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`at-redesign-2024-typography - Typography Variants 'Helpers' Snapshots Promo1 for small breakpoint renders default state correctly Athleta 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 60px;
  line-height: 1;
  letter-spacing: 0px;
  font-weight: 600;
  text-transform: uppercase;
}

<div
    style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`at-redesign-2024-typography - Typography Variants 'Helpers' Snapshots Promo1 for small breakpoint renders default state correctly Athleta in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 60px;
  line-height: 1;
  letter-spacing: 0px;
  font-weight: 600;
  text-transform: uppercase;
}

<div
    style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`at-redesign-2024-typography - Typography Variants 'Helpers' Snapshots Promo1 for small breakpoint renders scalable text state correctly Athleta 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(13px, 16vw);
  line-height: 1;
  letter-spacing: 0vw;
  font-weight: 600;
  text-transform: uppercase;
}

<div
    style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`at-redesign-2024-typography - Typography Variants 'Helpers' Snapshots Promo1 for small breakpoint renders scalable text state correctly Athleta in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(13px, 16vw);
  line-height: 1;
  letter-spacing: 0vw;
  font-weight: 600;
  text-transform: uppercase;
}

<div
    style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`at-redesign-2024-typography - Typography Variants 'Helpers' Snapshots Promo1 for small breakpoint renders scalable text with a max width state correctly Athleta 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(13px, min(16vw, 176px));
  line-height: 1;
  letter-spacing: min(0vw, 0px);
  font-weight: 600;
  text-transform: uppercase;
}

<div
    style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`at-redesign-2024-typography - Typography Variants 'Helpers' Snapshots Promo1 for small breakpoint renders scalable text with a max width state correctly Athleta in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(13px, min(16vw, 176px));
  line-height: 1;
  letter-spacing: min(0vw, 0px);
  font-weight: 600;
  text-transform: uppercase;
}

<div
    style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`at-redesign-2024-typography - Typography Variants 'Helpers' Snapshots Promo1 for small breakpoint renders variant state correctly Athleta 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 60px;
  line-height: 1;
  letter-spacing: 0px;
  font-weight: 600;
  text-transform: uppercase;
}

<div
    style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`at-redesign-2024-typography - Typography Variants 'Helpers' Snapshots Promo1 for small breakpoint renders variant state correctly Athleta in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 60px;
  line-height: 1;
  letter-spacing: 0px;
  font-weight: 600;
  text-transform: uppercase;
}

<div
    style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`at-redesign-2024-typography - Typography Variants 'Helpers' Snapshots Promo1 for x-large breakpoint renders customStyles state correctly Athleta 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: black;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 100px;
  line-height: 1;
  letter-spacing: 0px;
  font-weight: 600;
  text-transform: uppercase;
}

<div
    style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`at-redesign-2024-typography - Typography Variants 'Helpers' Snapshots Promo1 for x-large breakpoint renders customStyles state correctly Athleta in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: black;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 100px;
  line-height: 1;
  letter-spacing: 0px;
  font-weight: 600;
  text-transform: uppercase;
}

<div
    style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`at-redesign-2024-typography - Typography Variants 'Helpers' Snapshots Promo1 for x-large breakpoint renders default state correctly Athleta 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 100px;
  line-height: 1;
  letter-spacing: 0px;
  font-weight: 600;
  text-transform: uppercase;
}

<div
    style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`at-redesign-2024-typography - Typography Variants 'Helpers' Snapshots Promo1 for x-large breakpoint renders default state correctly Athleta in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 100px;
  line-height: 1;
  letter-spacing: 0px;
  font-weight: 600;
  text-transform: uppercase;
}

<div
    style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`at-redesign-2024-typography - Typography Variants 'Helpers' Snapshots Promo1 for x-large breakpoint renders scalable text state correctly Athleta 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, 7.8125vw);
  line-height: 1;
  letter-spacing: 0vw;
  font-weight: 600;
  text-transform: uppercase;
}

<div
    style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`at-redesign-2024-typography - Typography Variants 'Helpers' Snapshots Promo1 for x-large breakpoint renders scalable text state correctly Athleta in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, 7.8125vw);
  line-height: 1;
  letter-spacing: 0vw;
  font-weight: 600;
  text-transform: uppercase;
}

<div
    style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`at-redesign-2024-typography - Typography Variants 'Helpers' Snapshots Promo1 for x-large breakpoint renders scalable text with a max width state correctly Athleta 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(7.8125vw, 85.9375px));
  line-height: 1;
  letter-spacing: min(0vw, 0px);
  font-weight: 600;
  text-transform: uppercase;
}

<div
    style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`at-redesign-2024-typography - Typography Variants 'Helpers' Snapshots Promo1 for x-large breakpoint renders scalable text with a max width state correctly Athleta in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(7.8125vw, 85.9375px));
  line-height: 1;
  letter-spacing: min(0vw, 0px);
  font-weight: 600;
  text-transform: uppercase;
}

<div
    style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`at-redesign-2024-typography - Typography Variants 'Helpers' Snapshots Promo1 for x-large breakpoint renders variant state correctly Athleta 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 100px;
  line-height: 1;
  letter-spacing: 0px;
  font-weight: 600;
  text-transform: uppercase;
}

<div
    style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`at-redesign-2024-typography - Typography Variants 'Helpers' Snapshots Promo1 for x-large breakpoint renders variant state correctly Athleta in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 100px;
  line-height: 1;
  letter-spacing: 0px;
  font-weight: 600;
  text-transform: uppercase;
}

<div
    style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`at-redesign-2024-typography - Typography Variants 'Helpers' Snapshots Subhead for small breakpoint renders customStyles state correctly Athleta 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: black;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 16px;
  line-height: 1.75;
  letter-spacing: 0.32px;
  font-weight: 400;
  text-transform: uppercase;
}

<div
    style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`at-redesign-2024-typography - Typography Variants 'Helpers' Snapshots Subhead for small breakpoint renders customStyles state correctly Athleta in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: black;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 16px;
  line-height: 1.75;
  letter-spacing: 0.32px;
  font-weight: 400;
  text-transform: uppercase;
}

<div
    style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`at-redesign-2024-typography - Typography Variants 'Helpers' Snapshots Subhead for small breakpoint renders default state correctly Athleta 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 16px;
  line-height: 1.75;
  letter-spacing: 0.32px;
  font-weight: 400;
  text-transform: uppercase;
}

<div
    style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`at-redesign-2024-typography - Typography Variants 'Helpers' Snapshots Subhead for small breakpoint renders default state correctly Athleta in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 16px;
  line-height: 1.75;
  letter-spacing: 0.32px;
  font-weight: 400;
  text-transform: uppercase;
}

<div
    style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`at-redesign-2024-typography - Typography Variants 'Helpers' Snapshots Subhead for small breakpoint renders scalable text state correctly Athleta 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(13px, 4.266666666666667vw);
  line-height: 1.75;
  letter-spacing: 0.08533333333333333vw;
  font-weight: 400;
  text-transform: uppercase;
}

<div
    style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`at-redesign-2024-typography - Typography Variants 'Helpers' Snapshots Subhead for small breakpoint renders scalable text state correctly Athleta in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(13px, 4.266666666666667vw);
  line-height: 1.75;
  letter-spacing: 0.08533333333333333vw;
  font-weight: 400;
  text-transform: uppercase;
}

<div
    style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`at-redesign-2024-typography - Typography Variants 'Helpers' Snapshots Subhead for small breakpoint renders scalable text with a max width state correctly Athleta 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(13px, min(4.266666666666667vw, 46.93333333333333px));
  line-height: 1.75;
  letter-spacing: min(0.08533333333333333vw, 0.9386666666666666px);
  font-weight: 400;
  text-transform: uppercase;
}

<div
    style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`at-redesign-2024-typography - Typography Variants 'Helpers' Snapshots Subhead for small breakpoint renders scalable text with a max width state correctly Athleta in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(13px, min(4.266666666666667vw, 46.93333333333333px));
  line-height: 1.75;
  letter-spacing: min(0.08533333333333333vw, 0.9386666666666666px);
  font-weight: 400;
  text-transform: uppercase;
}

<div
    style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`at-redesign-2024-typography - Typography Variants 'Helpers' Snapshots Subhead for small breakpoint renders variant state correctly Athleta 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 14px;
  line-height: 1.5714285714285714;
  letter-spacing: 0.7px;
  font-weight: 500;
  text-transform: uppercase;
}

<div
    style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`at-redesign-2024-typography - Typography Variants 'Helpers' Snapshots Subhead for small breakpoint renders variant state correctly Athleta in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 14px;
  line-height: 1.5714285714285714;
  letter-spacing: 0.7px;
  font-weight: 500;
  text-transform: uppercase;
}

<div
    style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`at-redesign-2024-typography - Typography Variants 'Helpers' Snapshots Subhead for x-large breakpoint renders customStyles state correctly Athleta 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: black;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 24px;
  line-height: 1.5;
  letter-spacing: 1.2px;
  font-weight: 400;
  text-transform: uppercase;
}

<div
    style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`at-redesign-2024-typography - Typography Variants 'Helpers' Snapshots Subhead for x-large breakpoint renders customStyles state correctly Athleta in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: black;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 24px;
  line-height: 1.5;
  letter-spacing: 1.2px;
  font-weight: 400;
  text-transform: uppercase;
}

<div
    style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`at-redesign-2024-typography - Typography Variants 'Helpers' Snapshots Subhead for x-large breakpoint renders default state correctly Athleta 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 24px;
  line-height: 1.5;
  letter-spacing: 1.2px;
  font-weight: 400;
  text-transform: uppercase;
}

<div
    style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`at-redesign-2024-typography - Typography Variants 'Helpers' Snapshots Subhead for x-large breakpoint renders default state correctly Athleta in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 24px;
  line-height: 1.5;
  letter-spacing: 1.2px;
  font-weight: 400;
  text-transform: uppercase;
}

<div
    style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`at-redesign-2024-typography - Typography Variants 'Helpers' Snapshots Subhead for x-large breakpoint renders scalable text state correctly Athleta 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, 1.875vw);
  line-height: 1.5;
  letter-spacing: 0.09375vw;
  font-weight: 400;
  text-transform: uppercase;
}

<div
    style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`at-redesign-2024-typography - Typography Variants 'Helpers' Snapshots Subhead for x-large breakpoint renders scalable text state correctly Athleta in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, 1.875vw);
  line-height: 1.5;
  letter-spacing: 0.09375vw;
  font-weight: 400;
  text-transform: uppercase;
}

<div
    style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`at-redesign-2024-typography - Typography Variants 'Helpers' Snapshots Subhead for x-large breakpoint renders scalable text with a max width state correctly Athleta 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(1.875vw, 20.625px));
  line-height: 1.5;
  letter-spacing: min(0.09375vw, 1.03125px);
  font-weight: 400;
  text-transform: uppercase;
}

<div
    style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`at-redesign-2024-typography - Typography Variants 'Helpers' Snapshots Subhead for x-large breakpoint renders scalable text with a max width state correctly Athleta in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(1.875vw, 20.625px));
  line-height: 1.5;
  letter-spacing: min(0.09375vw, 1.03125px);
  font-weight: 400;
  text-transform: uppercase;
}

<div
    style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`at-redesign-2024-typography - Typography Variants 'Helpers' Snapshots Subhead for x-large breakpoint renders variant state correctly Athleta 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 18px;
  line-height: 1.5555555555555556;
  letter-spacing: 0.36px;
  font-weight: 500;
  text-transform: uppercase;
}

<div
    style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`at-redesign-2024-typography - Typography Variants 'Helpers' Snapshots Subhead for x-large breakpoint renders variant state correctly Athleta in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 18px;
  line-height: 1.5555555555555556;
  letter-spacing: 0.36px;
  font-weight: 500;
  text-transform: uppercase;
}

<div
    style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`at-redesign-2024-typography - Typography Variants 'Helpers' Snapshots desktopBreakpoint for large breakpoint renders set to 'x-large' should show mobile styles in views below XLARGE state correctly Athleta 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, 4.6875vw);
  line-height: 1;
  letter-spacing: 0.1875vw;
  text-transform: uppercase;
  font-weight: 500;
}

<div
    style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`at-redesign-2024-typography - Typography Variants 'Helpers' Snapshots desktopBreakpoint for large breakpoint renders set to 'x-large' should show mobile styles in views below XLARGE state correctly Athleta in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, 4.6875vw);
  line-height: 1;
  letter-spacing: 0.1875vw;
  text-transform: uppercase;
  font-weight: 500;
}

<div
    style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`at-redesign-2024-typography - Typography Variants 'Helpers' Snapshots desktopBreakpoint for x-large breakpoint renders set to 'x-large' should show mobile styles in views below XLARGE state correctly Athleta 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, 4.6875vw);
  line-height: 1;
  letter-spacing: 0.1875vw;
  text-transform: uppercase;
  font-weight: 500;
}

<div
    style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`at-redesign-2024-typography - Typography Variants 'Helpers' Snapshots desktopBreakpoint for x-large breakpoint renders set to 'x-large' should show mobile styles in views below XLARGE state correctly Athleta in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, 4.6875vw);
  line-height: 1;
  letter-spacing: 0.1875vw;
  text-transform: uppercase;
  font-weight: 500;
}

<div
    style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;
