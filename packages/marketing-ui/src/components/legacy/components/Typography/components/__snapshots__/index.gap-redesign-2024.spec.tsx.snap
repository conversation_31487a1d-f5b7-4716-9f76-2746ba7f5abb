// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gap Snapshots Body for small breakpoint renders customStyles state correctly Gap 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: black;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 18px;
  line-height: 1.2777777777777777;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gap Snapshots Body for small breakpoint renders customStyles state correctly Gap in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: black;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 18px;
  line-height: 1.2777777777777777;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gap Snapshots Body for small breakpoint renders customStyles state correctly GapFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: black;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 18px;
  line-height: 1.2777777777777777;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gap Snapshots Body for small breakpoint renders customStyles state correctly GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: black;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 18px;
  line-height: 1.2777777777777777;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gap Snapshots Body for small breakpoint renders default state correctly Gap 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 18px;
  line-height: 1.2777777777777777;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gap Snapshots Body for small breakpoint renders default state correctly Gap in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 18px;
  line-height: 1.2777777777777777;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gap Snapshots Body for small breakpoint renders default state correctly GapFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 18px;
  line-height: 1.2777777777777777;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gap Snapshots Body for small breakpoint renders default state correctly GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 18px;
  line-height: 1.2777777777777777;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gap Snapshots Body for small breakpoint renders scalable text state correctly Gap 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, 4.8vw);
  line-height: 1.2777777777777777;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gap Snapshots Body for small breakpoint renders scalable text state correctly Gap in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, 4.8vw);
  line-height: 1.2777777777777777;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gap Snapshots Body for small breakpoint renders scalable text state correctly GapFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, 4.8vw);
  line-height: 1.2777777777777777;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gap Snapshots Body for small breakpoint renders scalable text state correctly GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, 4.8vw);
  line-height: 1.2777777777777777;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gap Snapshots Body for small breakpoint renders scalable text with a max width state correctly Gap 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(4.8vw, 52.8px));
  line-height: 1.2777777777777777;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gap Snapshots Body for small breakpoint renders scalable text with a max width state correctly Gap in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(4.8vw, 52.8px));
  line-height: 1.2777777777777777;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gap Snapshots Body for small breakpoint renders scalable text with a max width state correctly GapFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(4.8vw, 52.8px));
  line-height: 1.2777777777777777;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gap Snapshots Body for small breakpoint renders scalable text with a max width state correctly GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(4.8vw, 52.8px));
  line-height: 1.2777777777777777;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gap Snapshots Body for small breakpoint renders variant state correctly Gap 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 16px;
  line-height: 1.25;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gap Snapshots Body for small breakpoint renders variant state correctly Gap in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 16px;
  line-height: 1.25;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gap Snapshots Body for small breakpoint renders variant state correctly GapFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 16px;
  line-height: 1.25;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gap Snapshots Body for small breakpoint renders variant state correctly GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 16px;
  line-height: 1.25;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gap Snapshots Body for x-large breakpoint renders customStyles state correctly Gap 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: black;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 20px;
  line-height: 1.3;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gap Snapshots Body for x-large breakpoint renders customStyles state correctly Gap in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: black;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 20px;
  line-height: 1.3;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gap Snapshots Body for x-large breakpoint renders customStyles state correctly GapFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: black;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 20px;
  line-height: 1.3;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gap Snapshots Body for x-large breakpoint renders customStyles state correctly GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: black;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 20px;
  line-height: 1.3;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gap Snapshots Body for x-large breakpoint renders default state correctly Gap 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 20px;
  line-height: 1.3;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gap Snapshots Body for x-large breakpoint renders default state correctly Gap in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 20px;
  line-height: 1.3;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gap Snapshots Body for x-large breakpoint renders default state correctly GapFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 20px;
  line-height: 1.3;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gap Snapshots Body for x-large breakpoint renders default state correctly GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 20px;
  line-height: 1.3;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gap Snapshots Body for x-large breakpoint renders scalable text state correctly Gap 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, 1.5625vw);
  line-height: 1.3;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gap Snapshots Body for x-large breakpoint renders scalable text state correctly Gap in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, 1.5625vw);
  line-height: 1.3;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gap Snapshots Body for x-large breakpoint renders scalable text state correctly GapFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, 1.5625vw);
  line-height: 1.3;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gap Snapshots Body for x-large breakpoint renders scalable text state correctly GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, 1.5625vw);
  line-height: 1.3;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gap Snapshots Body for x-large breakpoint renders scalable text with a max width state correctly Gap 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(1.5625vw, 17.1875px));
  line-height: 1.3;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gap Snapshots Body for x-large breakpoint renders scalable text with a max width state correctly Gap in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(1.5625vw, 17.1875px));
  line-height: 1.3;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gap Snapshots Body for x-large breakpoint renders scalable text with a max width state correctly GapFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(1.5625vw, 17.1875px));
  line-height: 1.3;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gap Snapshots Body for x-large breakpoint renders scalable text with a max width state correctly GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(1.5625vw, 17.1875px));
  line-height: 1.3;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gap Snapshots Body for x-large breakpoint renders variant state correctly Gap 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 18px;
  line-height: 1.2777777777777777;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gap Snapshots Body for x-large breakpoint renders variant state correctly Gap in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 18px;
  line-height: 1.2777777777777777;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gap Snapshots Body for x-large breakpoint renders variant state correctly GapFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 18px;
  line-height: 1.2777777777777777;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gap Snapshots Body for x-large breakpoint renders variant state correctly GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 18px;
  line-height: 1.2777777777777777;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gap Snapshots Eyebrow for small breakpoint renders customStyles state correctly Gap 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: black;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 30px;
  line-height: 1;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gap Snapshots Eyebrow for small breakpoint renders customStyles state correctly Gap in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: black;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 30px;
  line-height: 1;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gap Snapshots Eyebrow for small breakpoint renders customStyles state correctly GapFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: black;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 30px;
  line-height: 1;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gap Snapshots Eyebrow for small breakpoint renders customStyles state correctly GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: black;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 30px;
  line-height: 1;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gap Snapshots Eyebrow for small breakpoint renders default state correctly Gap 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 30px;
  line-height: 1;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gap Snapshots Eyebrow for small breakpoint renders default state correctly Gap in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 30px;
  line-height: 1;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gap Snapshots Eyebrow for small breakpoint renders default state correctly GapFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 30px;
  line-height: 1;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gap Snapshots Eyebrow for small breakpoint renders default state correctly GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 30px;
  line-height: 1;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gap Snapshots Eyebrow for small breakpoint renders scalable text state correctly Gap 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, 8vw);
  line-height: 1;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gap Snapshots Eyebrow for small breakpoint renders scalable text state correctly Gap in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, 8vw);
  line-height: 1;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gap Snapshots Eyebrow for small breakpoint renders scalable text state correctly GapFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, 8vw);
  line-height: 1;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gap Snapshots Eyebrow for small breakpoint renders scalable text state correctly GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, 8vw);
  line-height: 1;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gap Snapshots Eyebrow for small breakpoint renders scalable text with a max width state correctly Gap 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(8vw, 88px));
  line-height: 1;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gap Snapshots Eyebrow for small breakpoint renders scalable text with a max width state correctly Gap in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(8vw, 88px));
  line-height: 1;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gap Snapshots Eyebrow for small breakpoint renders scalable text with a max width state correctly GapFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(8vw, 88px));
  line-height: 1;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gap Snapshots Eyebrow for small breakpoint renders scalable text with a max width state correctly GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(8vw, 88px));
  line-height: 1;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gap Snapshots Eyebrow for small breakpoint renders variant state correctly Gap 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 26px;
  line-height: 1;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gap Snapshots Eyebrow for small breakpoint renders variant state correctly Gap in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 26px;
  line-height: 1;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gap Snapshots Eyebrow for small breakpoint renders variant state correctly GapFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 26px;
  line-height: 1;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gap Snapshots Eyebrow for small breakpoint renders variant state correctly GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 26px;
  line-height: 1;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gap Snapshots Eyebrow for x-large breakpoint renders customStyles state correctly Gap 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: black;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 50px;
  line-height: 0.92;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gap Snapshots Eyebrow for x-large breakpoint renders customStyles state correctly Gap in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: black;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 50px;
  line-height: 0.92;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gap Snapshots Eyebrow for x-large breakpoint renders customStyles state correctly GapFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: black;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 50px;
  line-height: 0.92;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gap Snapshots Eyebrow for x-large breakpoint renders customStyles state correctly GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: black;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 50px;
  line-height: 0.92;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gap Snapshots Eyebrow for x-large breakpoint renders default state correctly Gap 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 50px;
  line-height: 0.92;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gap Snapshots Eyebrow for x-large breakpoint renders default state correctly Gap in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 50px;
  line-height: 0.92;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gap Snapshots Eyebrow for x-large breakpoint renders default state correctly GapFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 50px;
  line-height: 0.92;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gap Snapshots Eyebrow for x-large breakpoint renders default state correctly GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 50px;
  line-height: 0.92;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gap Snapshots Eyebrow for x-large breakpoint renders scalable text state correctly Gap 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(35px, 3.90625vw);
  line-height: 0.92;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gap Snapshots Eyebrow for x-large breakpoint renders scalable text state correctly Gap in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(35px, 3.90625vw);
  line-height: 0.92;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gap Snapshots Eyebrow for x-large breakpoint renders scalable text state correctly GapFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(35px, 3.90625vw);
  line-height: 0.92;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gap Snapshots Eyebrow for x-large breakpoint renders scalable text state correctly GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(35px, 3.90625vw);
  line-height: 0.92;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gap Snapshots Eyebrow for x-large breakpoint renders scalable text with a max width state correctly Gap 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(35px, min(3.90625vw, 42.96875px));
  line-height: 0.92;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gap Snapshots Eyebrow for x-large breakpoint renders scalable text with a max width state correctly Gap in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(35px, min(3.90625vw, 42.96875px));
  line-height: 0.92;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gap Snapshots Eyebrow for x-large breakpoint renders scalable text with a max width state correctly GapFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(35px, min(3.90625vw, 42.96875px));
  line-height: 0.92;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gap Snapshots Eyebrow for x-large breakpoint renders scalable text with a max width state correctly GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(35px, min(3.90625vw, 42.96875px));
  line-height: 0.92;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gap Snapshots Eyebrow for x-large breakpoint renders variant state correctly Gap 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 40px;
  line-height: 1;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gap Snapshots Eyebrow for x-large breakpoint renders variant state correctly Gap in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 40px;
  line-height: 1;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gap Snapshots Eyebrow for x-large breakpoint renders variant state correctly GapFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 40px;
  line-height: 1;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gap Snapshots Eyebrow for x-large breakpoint renders variant state correctly GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 40px;
  line-height: 1;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gap Snapshots Headline for small breakpoint renders customStyles state correctly Gap 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: black;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 60px;
  line-height: 1;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gap Snapshots Headline for small breakpoint renders customStyles state correctly Gap in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: black;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 60px;
  line-height: 1;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gap Snapshots Headline for small breakpoint renders customStyles state correctly GapFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: black;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 60px;
  line-height: 1;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gap Snapshots Headline for small breakpoint renders customStyles state correctly GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: black;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 60px;
  line-height: 1;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gap Snapshots Headline for small breakpoint renders default state correctly Gap 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 60px;
  line-height: 1;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gap Snapshots Headline for small breakpoint renders default state correctly Gap in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 60px;
  line-height: 1;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gap Snapshots Headline for small breakpoint renders default state correctly GapFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 60px;
  line-height: 1;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gap Snapshots Headline for small breakpoint renders default state correctly GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 60px;
  line-height: 1;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gap Snapshots Headline for small breakpoint renders scalable text state correctly Gap 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, 16vw);
  line-height: 1;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gap Snapshots Headline for small breakpoint renders scalable text state correctly Gap in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, 16vw);
  line-height: 1;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gap Snapshots Headline for small breakpoint renders scalable text state correctly GapFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, 16vw);
  line-height: 1;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gap Snapshots Headline for small breakpoint renders scalable text state correctly GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, 16vw);
  line-height: 1;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gap Snapshots Headline for small breakpoint renders scalable text with a max width state correctly Gap 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, min(16vw, 176px));
  line-height: 1;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gap Snapshots Headline for small breakpoint renders scalable text with a max width state correctly Gap in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, min(16vw, 176px));
  line-height: 1;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gap Snapshots Headline for small breakpoint renders scalable text with a max width state correctly GapFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, min(16vw, 176px));
  line-height: 1;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gap Snapshots Headline for small breakpoint renders scalable text with a max width state correctly GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, min(16vw, 176px));
  line-height: 1;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gap Snapshots Headline for small breakpoint renders variant state correctly Gap 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 55px;
  line-height: 1;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gap Snapshots Headline for small breakpoint renders variant state correctly Gap in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 55px;
  line-height: 1;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gap Snapshots Headline for small breakpoint renders variant state correctly GapFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 55px;
  line-height: 1;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gap Snapshots Headline for small breakpoint renders variant state correctly GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 55px;
  line-height: 1;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gap Snapshots Headline for x-large breakpoint renders customStyles state correctly Gap 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: black;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 120px;
  line-height: 0.8333333333333334;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gap Snapshots Headline for x-large breakpoint renders customStyles state correctly Gap in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: black;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 120px;
  line-height: 0.8333333333333334;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gap Snapshots Headline for x-large breakpoint renders customStyles state correctly GapFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: black;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 120px;
  line-height: 0.8333333333333334;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gap Snapshots Headline for x-large breakpoint renders customStyles state correctly GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: black;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 120px;
  line-height: 0.8333333333333334;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gap Snapshots Headline for x-large breakpoint renders default state correctly Gap 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 120px;
  line-height: 0.8333333333333334;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gap Snapshots Headline for x-large breakpoint renders default state correctly Gap in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 120px;
  line-height: 0.8333333333333334;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gap Snapshots Headline for x-large breakpoint renders default state correctly GapFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 120px;
  line-height: 0.8333333333333334;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gap Snapshots Headline for x-large breakpoint renders default state correctly GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 120px;
  line-height: 0.8333333333333334;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gap Snapshots Headline for x-large breakpoint renders scalable text state correctly Gap 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, 9.375vw);
  line-height: 0.8333333333333334;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gap Snapshots Headline for x-large breakpoint renders scalable text state correctly Gap in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, 9.375vw);
  line-height: 0.8333333333333334;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gap Snapshots Headline for x-large breakpoint renders scalable text state correctly GapFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, 9.375vw);
  line-height: 0.8333333333333334;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gap Snapshots Headline for x-large breakpoint renders scalable text state correctly GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, 9.375vw);
  line-height: 0.8333333333333334;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gap Snapshots Headline for x-large breakpoint renders scalable text with a max width state correctly Gap 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(9.375vw, 103.125px));
  line-height: 0.8333333333333334;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gap Snapshots Headline for x-large breakpoint renders scalable text with a max width state correctly Gap in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(9.375vw, 103.125px));
  line-height: 0.8333333333333334;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gap Snapshots Headline for x-large breakpoint renders scalable text with a max width state correctly GapFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(9.375vw, 103.125px));
  line-height: 0.8333333333333334;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gap Snapshots Headline for x-large breakpoint renders scalable text with a max width state correctly GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(9.375vw, 103.125px));
  line-height: 0.8333333333333334;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gap Snapshots Headline for x-large breakpoint renders variant state correctly Gap 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 110px;
  line-height: 0.8636363636363636;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gap Snapshots Headline for x-large breakpoint renders variant state correctly Gap in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 110px;
  line-height: 0.8636363636363636;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gap Snapshots Headline for x-large breakpoint renders variant state correctly GapFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 110px;
  line-height: 0.8636363636363636;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gap Snapshots Headline for x-large breakpoint renders variant state correctly GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 110px;
  line-height: 0.8636363636363636;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gap Snapshots New Variants for small breakpoint renders default state correctly Gap 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 11px;
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gap Snapshots New Variants for small breakpoint renders default state correctly Gap in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 11px;
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gap Snapshots New Variants for small breakpoint renders default state correctly GapFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 11px;
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gap Snapshots New Variants for small breakpoint renders default state correctly GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 11px;
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gap Snapshots New Variants for small breakpoint renders variant F0 state correctly Gap 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 11px;
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gap Snapshots New Variants for small breakpoint renders variant F0 state correctly Gap in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 11px;
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gap Snapshots New Variants for small breakpoint renders variant F0 state correctly GapFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 11px;
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gap Snapshots New Variants for small breakpoint renders variant F0 state correctly GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 11px;
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gap Snapshots New Variants for small breakpoint renders variant F1 state correctly Gap 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 11px;
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gap Snapshots New Variants for small breakpoint renders variant F1 state correctly Gap in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 11px;
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gap Snapshots New Variants for small breakpoint renders variant F1 state correctly GapFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 11px;
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gap Snapshots New Variants for small breakpoint renders variant F1 state correctly GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 11px;
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gap Snapshots New Variants for small breakpoint renders variant F2 state correctly Gap 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 11px;
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gap Snapshots New Variants for small breakpoint renders variant F2 state correctly Gap in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 11px;
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gap Snapshots New Variants for small breakpoint renders variant F2 state correctly GapFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 11px;
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gap Snapshots New Variants for small breakpoint renders variant F2 state correctly GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 11px;
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gap Snapshots New Variants for small breakpoint renders variant FN1 state correctly Gap 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 11px;
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gap Snapshots New Variants for small breakpoint renders variant FN1 state correctly Gap in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 11px;
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gap Snapshots New Variants for small breakpoint renders variant FN1 state correctly GapFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 11px;
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gap Snapshots New Variants for small breakpoint renders variant FN1 state correctly GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 11px;
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gap Snapshots New Variants for x-large breakpoint renders default state correctly Gap 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gap Snapshots New Variants for x-large breakpoint renders default state correctly Gap in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gap Snapshots New Variants for x-large breakpoint renders default state correctly GapFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gap Snapshots New Variants for x-large breakpoint renders default state correctly GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gap Snapshots New Variants for x-large breakpoint renders variant F0 state correctly Gap 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gap Snapshots New Variants for x-large breakpoint renders variant F0 state correctly Gap in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gap Snapshots New Variants for x-large breakpoint renders variant F0 state correctly GapFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gap Snapshots New Variants for x-large breakpoint renders variant F0 state correctly GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gap Snapshots New Variants for x-large breakpoint renders variant F1 state correctly Gap 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gap Snapshots New Variants for x-large breakpoint renders variant F1 state correctly Gap in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gap Snapshots New Variants for x-large breakpoint renders variant F1 state correctly GapFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gap Snapshots New Variants for x-large breakpoint renders variant F1 state correctly GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gap Snapshots New Variants for x-large breakpoint renders variant F2 state correctly Gap 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gap Snapshots New Variants for x-large breakpoint renders variant F2 state correctly Gap in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gap Snapshots New Variants for x-large breakpoint renders variant F2 state correctly GapFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gap Snapshots New Variants for x-large breakpoint renders variant F2 state correctly GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gap Snapshots New Variants for x-large breakpoint renders variant FN1 state correctly Gap 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gap Snapshots New Variants for x-large breakpoint renders variant FN1 state correctly Gap in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gap Snapshots New Variants for x-large breakpoint renders variant FN1 state correctly GapFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gap Snapshots New Variants for x-large breakpoint renders variant FN1 state correctly GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gap Snapshots Promo for small breakpoint renders customStyles state correctly Gap 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: black;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 60px;
  line-height: 1;
  letter-spacing: 0px;
  font-weight: 600;
  text-transform: uppercase;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gap Snapshots Promo for small breakpoint renders customStyles state correctly Gap in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: black;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 60px;
  line-height: 1;
  letter-spacing: 0px;
  font-weight: 600;
  text-transform: uppercase;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gap Snapshots Promo for small breakpoint renders customStyles state correctly GapFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: black;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 60px;
  line-height: 1;
  letter-spacing: 0px;
  font-weight: 600;
  text-transform: uppercase;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gap Snapshots Promo for small breakpoint renders customStyles state correctly GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: black;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 60px;
  line-height: 1;
  letter-spacing: 0px;
  font-weight: 600;
  text-transform: uppercase;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gap Snapshots Promo for small breakpoint renders default state correctly Gap 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 60px;
  line-height: 1;
  letter-spacing: 0px;
  font-weight: 600;
  text-transform: uppercase;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gap Snapshots Promo for small breakpoint renders default state correctly Gap in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 60px;
  line-height: 1;
  letter-spacing: 0px;
  font-weight: 600;
  text-transform: uppercase;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gap Snapshots Promo for small breakpoint renders default state correctly GapFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 60px;
  line-height: 1;
  letter-spacing: 0px;
  font-weight: 600;
  text-transform: uppercase;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gap Snapshots Promo for small breakpoint renders default state correctly GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 60px;
  line-height: 1;
  letter-spacing: 0px;
  font-weight: 600;
  text-transform: uppercase;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gap Snapshots Promo for small breakpoint renders scalable text state correctly Gap 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(13px, 16vw);
  line-height: 1;
  letter-spacing: 0vw;
  font-weight: 600;
  text-transform: uppercase;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gap Snapshots Promo for small breakpoint renders scalable text state correctly Gap in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(13px, 16vw);
  line-height: 1;
  letter-spacing: 0vw;
  font-weight: 600;
  text-transform: uppercase;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gap Snapshots Promo for small breakpoint renders scalable text state correctly GapFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(13px, 16vw);
  line-height: 1;
  letter-spacing: 0vw;
  font-weight: 600;
  text-transform: uppercase;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gap Snapshots Promo for small breakpoint renders scalable text state correctly GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(13px, 16vw);
  line-height: 1;
  letter-spacing: 0vw;
  font-weight: 600;
  text-transform: uppercase;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gap Snapshots Promo for small breakpoint renders scalable text with a max width state correctly Gap 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(13px, min(16vw, 176px));
  line-height: 1;
  letter-spacing: min(0vw, 0px);
  font-weight: 600;
  text-transform: uppercase;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gap Snapshots Promo for small breakpoint renders scalable text with a max width state correctly Gap in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(13px, min(16vw, 176px));
  line-height: 1;
  letter-spacing: min(0vw, 0px);
  font-weight: 600;
  text-transform: uppercase;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gap Snapshots Promo for small breakpoint renders scalable text with a max width state correctly GapFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(13px, min(16vw, 176px));
  line-height: 1;
  letter-spacing: min(0vw, 0px);
  font-weight: 600;
  text-transform: uppercase;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gap Snapshots Promo for small breakpoint renders scalable text with a max width state correctly GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(13px, min(16vw, 176px));
  line-height: 1;
  letter-spacing: min(0vw, 0px);
  font-weight: 600;
  text-transform: uppercase;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gap Snapshots Promo for small breakpoint renders variant state correctly Gap 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 40px;
  line-height: 1;
  letter-spacing: 0px;
  font-weight: 600;
  text-transform: uppercase;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gap Snapshots Promo for small breakpoint renders variant state correctly Gap in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 40px;
  line-height: 1;
  letter-spacing: 0px;
  font-weight: 600;
  text-transform: uppercase;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gap Snapshots Promo for small breakpoint renders variant state correctly GapFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 40px;
  line-height: 1;
  letter-spacing: 0px;
  font-weight: 600;
  text-transform: uppercase;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gap Snapshots Promo for small breakpoint renders variant state correctly GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 40px;
  line-height: 1;
  letter-spacing: 0px;
  font-weight: 600;
  text-transform: uppercase;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gap Snapshots Promo for x-large breakpoint renders customStyles state correctly Gap 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: black;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 100px;
  line-height: 1;
  letter-spacing: 0px;
  font-weight: 600;
  text-transform: uppercase;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gap Snapshots Promo for x-large breakpoint renders customStyles state correctly Gap in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: black;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 100px;
  line-height: 1;
  letter-spacing: 0px;
  font-weight: 600;
  text-transform: uppercase;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gap Snapshots Promo for x-large breakpoint renders customStyles state correctly GapFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: black;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 100px;
  line-height: 1;
  letter-spacing: 0px;
  font-weight: 600;
  text-transform: uppercase;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gap Snapshots Promo for x-large breakpoint renders customStyles state correctly GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: black;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 100px;
  line-height: 1;
  letter-spacing: 0px;
  font-weight: 600;
  text-transform: uppercase;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gap Snapshots Promo for x-large breakpoint renders default state correctly Gap 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 100px;
  line-height: 1;
  letter-spacing: 0px;
  font-weight: 600;
  text-transform: uppercase;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gap Snapshots Promo for x-large breakpoint renders default state correctly Gap in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 100px;
  line-height: 1;
  letter-spacing: 0px;
  font-weight: 600;
  text-transform: uppercase;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gap Snapshots Promo for x-large breakpoint renders default state correctly GapFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 100px;
  line-height: 1;
  letter-spacing: 0px;
  font-weight: 600;
  text-transform: uppercase;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gap Snapshots Promo for x-large breakpoint renders default state correctly GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 100px;
  line-height: 1;
  letter-spacing: 0px;
  font-weight: 600;
  text-transform: uppercase;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gap Snapshots Promo for x-large breakpoint renders scalable text state correctly Gap 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, 7.8125vw);
  line-height: 1;
  letter-spacing: 0vw;
  font-weight: 600;
  text-transform: uppercase;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gap Snapshots Promo for x-large breakpoint renders scalable text state correctly Gap in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, 7.8125vw);
  line-height: 1;
  letter-spacing: 0vw;
  font-weight: 600;
  text-transform: uppercase;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gap Snapshots Promo for x-large breakpoint renders scalable text state correctly GapFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, 7.8125vw);
  line-height: 1;
  letter-spacing: 0vw;
  font-weight: 600;
  text-transform: uppercase;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gap Snapshots Promo for x-large breakpoint renders scalable text state correctly GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, 7.8125vw);
  line-height: 1;
  letter-spacing: 0vw;
  font-weight: 600;
  text-transform: uppercase;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gap Snapshots Promo for x-large breakpoint renders scalable text with a max width state correctly Gap 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(7.8125vw, 85.9375px));
  line-height: 1;
  letter-spacing: min(0vw, 0px);
  font-weight: 600;
  text-transform: uppercase;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gap Snapshots Promo for x-large breakpoint renders scalable text with a max width state correctly Gap in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(7.8125vw, 85.9375px));
  line-height: 1;
  letter-spacing: min(0vw, 0px);
  font-weight: 600;
  text-transform: uppercase;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gap Snapshots Promo for x-large breakpoint renders scalable text with a max width state correctly GapFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(7.8125vw, 85.9375px));
  line-height: 1;
  letter-spacing: min(0vw, 0px);
  font-weight: 600;
  text-transform: uppercase;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gap Snapshots Promo for x-large breakpoint renders scalable text with a max width state correctly GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(7.8125vw, 85.9375px));
  line-height: 1;
  letter-spacing: min(0vw, 0px);
  font-weight: 600;
  text-transform: uppercase;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gap Snapshots Promo for x-large breakpoint renders variant state correctly Gap 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 80px;
  line-height: 1;
  letter-spacing: 0px;
  font-weight: 600;
  text-transform: uppercase;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gap Snapshots Promo for x-large breakpoint renders variant state correctly Gap in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 80px;
  line-height: 1;
  letter-spacing: 0px;
  font-weight: 600;
  text-transform: uppercase;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gap Snapshots Promo for x-large breakpoint renders variant state correctly GapFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 80px;
  line-height: 1;
  letter-spacing: 0px;
  font-weight: 600;
  text-transform: uppercase;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gap Snapshots Promo for x-large breakpoint renders variant state correctly GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 80px;
  line-height: 1;
  letter-spacing: 0px;
  font-weight: 600;
  text-transform: uppercase;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gap Snapshots Subhead for small breakpoint renders customStyles state correctly Gap 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: black;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 22px;
  line-height: 1.0909090909090908;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gap Snapshots Subhead for small breakpoint renders customStyles state correctly Gap in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: black;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 22px;
  line-height: 1.0909090909090908;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gap Snapshots Subhead for small breakpoint renders customStyles state correctly GapFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: black;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 22px;
  line-height: 1.0909090909090908;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gap Snapshots Subhead for small breakpoint renders customStyles state correctly GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: black;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 22px;
  line-height: 1.0909090909090908;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gap Snapshots Subhead for small breakpoint renders default state correctly Gap 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 22px;
  line-height: 1.0909090909090908;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gap Snapshots Subhead for small breakpoint renders default state correctly Gap in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 22px;
  line-height: 1.0909090909090908;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gap Snapshots Subhead for small breakpoint renders default state correctly GapFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 22px;
  line-height: 1.0909090909090908;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gap Snapshots Subhead for small breakpoint renders default state correctly GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 22px;
  line-height: 1.0909090909090908;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gap Snapshots Subhead for small breakpoint renders scalable text state correctly Gap 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, 5.866666666666666vw);
  line-height: 1.0909090909090908;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gap Snapshots Subhead for small breakpoint renders scalable text state correctly Gap in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, 5.866666666666666vw);
  line-height: 1.0909090909090908;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gap Snapshots Subhead for small breakpoint renders scalable text state correctly GapFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, 5.866666666666666vw);
  line-height: 1.0909090909090908;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gap Snapshots Subhead for small breakpoint renders scalable text state correctly GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, 5.866666666666666vw);
  line-height: 1.0909090909090908;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gap Snapshots Subhead for small breakpoint renders scalable text with a max width state correctly Gap 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, min(5.866666666666666vw, 64.53333333333333px));
  line-height: 1.0909090909090908;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gap Snapshots Subhead for small breakpoint renders scalable text with a max width state correctly Gap in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, min(5.866666666666666vw, 64.53333333333333px));
  line-height: 1.0909090909090908;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gap Snapshots Subhead for small breakpoint renders scalable text with a max width state correctly GapFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, min(5.866666666666666vw, 64.53333333333333px));
  line-height: 1.0909090909090908;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gap Snapshots Subhead for small breakpoint renders scalable text with a max width state correctly GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, min(5.866666666666666vw, 64.53333333333333px));
  line-height: 1.0909090909090908;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gap Snapshots Subhead for small breakpoint renders variant state correctly Gap 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 20px;
  line-height: 1.1;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gap Snapshots Subhead for small breakpoint renders variant state correctly Gap in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 20px;
  line-height: 1.1;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gap Snapshots Subhead for small breakpoint renders variant state correctly GapFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 20px;
  line-height: 1.1;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gap Snapshots Subhead for small breakpoint renders variant state correctly GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 20px;
  line-height: 1.1;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gap Snapshots Subhead for x-large breakpoint renders customStyles state correctly Gap 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: black;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 30px;
  line-height: 1;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gap Snapshots Subhead for x-large breakpoint renders customStyles state correctly Gap in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: black;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 30px;
  line-height: 1;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gap Snapshots Subhead for x-large breakpoint renders customStyles state correctly GapFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: black;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 30px;
  line-height: 1;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gap Snapshots Subhead for x-large breakpoint renders customStyles state correctly GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: black;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 30px;
  line-height: 1;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gap Snapshots Subhead for x-large breakpoint renders default state correctly Gap 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 30px;
  line-height: 1;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gap Snapshots Subhead for x-large breakpoint renders default state correctly Gap in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 30px;
  line-height: 1;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gap Snapshots Subhead for x-large breakpoint renders default state correctly GapFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 30px;
  line-height: 1;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gap Snapshots Subhead for x-large breakpoint renders default state correctly GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 30px;
  line-height: 1;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gap Snapshots Subhead for x-large breakpoint renders scalable text state correctly Gap 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, 2.34375vw);
  line-height: 1;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gap Snapshots Subhead for x-large breakpoint renders scalable text state correctly Gap in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, 2.34375vw);
  line-height: 1;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gap Snapshots Subhead for x-large breakpoint renders scalable text state correctly GapFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, 2.34375vw);
  line-height: 1;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gap Snapshots Subhead for x-large breakpoint renders scalable text state correctly GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, 2.34375vw);
  line-height: 1;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gap Snapshots Subhead for x-large breakpoint renders scalable text with a max width state correctly Gap 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(2.34375vw, 25.78125px));
  line-height: 1;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gap Snapshots Subhead for x-large breakpoint renders scalable text with a max width state correctly Gap in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(2.34375vw, 25.78125px));
  line-height: 1;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gap Snapshots Subhead for x-large breakpoint renders scalable text with a max width state correctly GapFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(2.34375vw, 25.78125px));
  line-height: 1;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gap Snapshots Subhead for x-large breakpoint renders scalable text with a max width state correctly GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(2.34375vw, 25.78125px));
  line-height: 1;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gap Snapshots Subhead for x-large breakpoint renders variant state correctly Gap 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 25px;
  line-height: 1;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gap Snapshots Subhead for x-large breakpoint renders variant state correctly Gap in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 25px;
  line-height: 1;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gap Snapshots Subhead for x-large breakpoint renders variant state correctly GapFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 25px;
  line-height: 1;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gap Snapshots Subhead for x-large breakpoint renders variant state correctly GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 25px;
  line-height: 1;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gap Snapshots desktopBreakpoint for large breakpoint renders set to 'x-large' should show mobile styles in views below XLARGE state correctly Gap 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, 9.375vw);
  line-height: 0.8333333333333334;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gap Snapshots desktopBreakpoint for large breakpoint renders set to 'x-large' should show mobile styles in views below XLARGE state correctly Gap in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, 9.375vw);
  line-height: 0.8333333333333334;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gap Snapshots desktopBreakpoint for large breakpoint renders set to 'x-large' should show mobile styles in views below XLARGE state correctly GapFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, 9.375vw);
  line-height: 0.8333333333333334;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gap Snapshots desktopBreakpoint for large breakpoint renders set to 'x-large' should show mobile styles in views below XLARGE state correctly GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, 9.375vw);
  line-height: 0.8333333333333334;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gap Snapshots desktopBreakpoint for x-large breakpoint renders set to 'x-large' should show mobile styles in views below XLARGE state correctly Gap 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, 9.375vw);
  line-height: 0.8333333333333334;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gap Snapshots desktopBreakpoint for x-large breakpoint renders set to 'x-large' should show mobile styles in views below XLARGE state correctly Gap in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, 9.375vw);
  line-height: 0.8333333333333334;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gap Snapshots desktopBreakpoint for x-large breakpoint renders set to 'x-large' should show mobile styles in views below XLARGE state correctly GapFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, 9.375vw);
  line-height: 0.8333333333333334;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gap Snapshots desktopBreakpoint for x-large breakpoint renders set to 'x-large' should show mobile styles in views below XLARGE state correctly GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, 9.375vw);
  line-height: 0.8333333333333334;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gapfs Snapshots Body for small breakpoint renders customStyles state correctly Gap 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: black;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 18px;
  line-height: 1.2777777777777777;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gapfs Snapshots Body for small breakpoint renders customStyles state correctly Gap in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: black;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 18px;
  line-height: 1.2777777777777777;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gapfs Snapshots Body for small breakpoint renders customStyles state correctly GapFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: black;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 18px;
  line-height: 1.2777777777777777;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gapfs Snapshots Body for small breakpoint renders customStyles state correctly GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: black;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 18px;
  line-height: 1.2777777777777777;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gapfs Snapshots Body for small breakpoint renders default state correctly Gap 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 18px;
  line-height: 1.2777777777777777;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gapfs Snapshots Body for small breakpoint renders default state correctly Gap in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 18px;
  line-height: 1.2777777777777777;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gapfs Snapshots Body for small breakpoint renders default state correctly GapFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 18px;
  line-height: 1.2777777777777777;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gapfs Snapshots Body for small breakpoint renders default state correctly GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 18px;
  line-height: 1.2777777777777777;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gapfs Snapshots Body for small breakpoint renders scalable text state correctly Gap 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, 4.8vw);
  line-height: 1.2777777777777777;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gapfs Snapshots Body for small breakpoint renders scalable text state correctly Gap in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, 4.8vw);
  line-height: 1.2777777777777777;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gapfs Snapshots Body for small breakpoint renders scalable text state correctly GapFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, 4.8vw);
  line-height: 1.2777777777777777;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gapfs Snapshots Body for small breakpoint renders scalable text state correctly GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, 4.8vw);
  line-height: 1.2777777777777777;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gapfs Snapshots Body for small breakpoint renders scalable text with a max width state correctly Gap 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(4.8vw, 52.8px));
  line-height: 1.2777777777777777;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gapfs Snapshots Body for small breakpoint renders scalable text with a max width state correctly Gap in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(4.8vw, 52.8px));
  line-height: 1.2777777777777777;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gapfs Snapshots Body for small breakpoint renders scalable text with a max width state correctly GapFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(4.8vw, 52.8px));
  line-height: 1.2777777777777777;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gapfs Snapshots Body for small breakpoint renders scalable text with a max width state correctly GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(4.8vw, 52.8px));
  line-height: 1.2777777777777777;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gapfs Snapshots Body for small breakpoint renders variant state correctly Gap 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 16px;
  line-height: 1.25;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gapfs Snapshots Body for small breakpoint renders variant state correctly Gap in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 16px;
  line-height: 1.25;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gapfs Snapshots Body for small breakpoint renders variant state correctly GapFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 16px;
  line-height: 1.25;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gapfs Snapshots Body for small breakpoint renders variant state correctly GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 16px;
  line-height: 1.25;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gapfs Snapshots Body for x-large breakpoint renders customStyles state correctly Gap 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: black;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 20px;
  line-height: 1.3;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gapfs Snapshots Body for x-large breakpoint renders customStyles state correctly Gap in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: black;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 20px;
  line-height: 1.3;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gapfs Snapshots Body for x-large breakpoint renders customStyles state correctly GapFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: black;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 20px;
  line-height: 1.3;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gapfs Snapshots Body for x-large breakpoint renders customStyles state correctly GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: black;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 20px;
  line-height: 1.3;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gapfs Snapshots Body for x-large breakpoint renders default state correctly Gap 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 20px;
  line-height: 1.3;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gapfs Snapshots Body for x-large breakpoint renders default state correctly Gap in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 20px;
  line-height: 1.3;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gapfs Snapshots Body for x-large breakpoint renders default state correctly GapFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 20px;
  line-height: 1.3;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gapfs Snapshots Body for x-large breakpoint renders default state correctly GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 20px;
  line-height: 1.3;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gapfs Snapshots Body for x-large breakpoint renders scalable text state correctly Gap 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, 1.5625vw);
  line-height: 1.3;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gapfs Snapshots Body for x-large breakpoint renders scalable text state correctly Gap in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, 1.5625vw);
  line-height: 1.3;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gapfs Snapshots Body for x-large breakpoint renders scalable text state correctly GapFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, 1.5625vw);
  line-height: 1.3;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gapfs Snapshots Body for x-large breakpoint renders scalable text state correctly GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, 1.5625vw);
  line-height: 1.3;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gapfs Snapshots Body for x-large breakpoint renders scalable text with a max width state correctly Gap 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(1.5625vw, 17.1875px));
  line-height: 1.3;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gapfs Snapshots Body for x-large breakpoint renders scalable text with a max width state correctly Gap in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(1.5625vw, 17.1875px));
  line-height: 1.3;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gapfs Snapshots Body for x-large breakpoint renders scalable text with a max width state correctly GapFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(1.5625vw, 17.1875px));
  line-height: 1.3;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gapfs Snapshots Body for x-large breakpoint renders scalable text with a max width state correctly GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(1.5625vw, 17.1875px));
  line-height: 1.3;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gapfs Snapshots Body for x-large breakpoint renders variant state correctly Gap 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 18px;
  line-height: 1.2777777777777777;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gapfs Snapshots Body for x-large breakpoint renders variant state correctly Gap in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 18px;
  line-height: 1.2777777777777777;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gapfs Snapshots Body for x-large breakpoint renders variant state correctly GapFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 18px;
  line-height: 1.2777777777777777;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gapfs Snapshots Body for x-large breakpoint renders variant state correctly GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 18px;
  line-height: 1.2777777777777777;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gapfs Snapshots Eyebrow for small breakpoint renders customStyles state correctly Gap 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: black;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 30px;
  line-height: 1;
  letter-spacing: 0;
  font-weight: 500;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gapfs Snapshots Eyebrow for small breakpoint renders customStyles state correctly Gap in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: black;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 30px;
  line-height: 1;
  letter-spacing: 0;
  font-weight: 500;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gapfs Snapshots Eyebrow for small breakpoint renders customStyles state correctly GapFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: black;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 30px;
  line-height: 1;
  letter-spacing: 0;
  font-weight: 500;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gapfs Snapshots Eyebrow for small breakpoint renders customStyles state correctly GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: black;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 30px;
  line-height: 1;
  letter-spacing: 0;
  font-weight: 500;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gapfs Snapshots Eyebrow for small breakpoint renders default state correctly Gap 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 30px;
  line-height: 1;
  letter-spacing: 0;
  font-weight: 500;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gapfs Snapshots Eyebrow for small breakpoint renders default state correctly Gap in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 30px;
  line-height: 1;
  letter-spacing: 0;
  font-weight: 500;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gapfs Snapshots Eyebrow for small breakpoint renders default state correctly GapFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 30px;
  line-height: 1;
  letter-spacing: 0;
  font-weight: 500;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gapfs Snapshots Eyebrow for small breakpoint renders default state correctly GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 30px;
  line-height: 1;
  letter-spacing: 0;
  font-weight: 500;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gapfs Snapshots Eyebrow for small breakpoint renders scalable text state correctly Gap 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, 8vw);
  line-height: 1;
  letter-spacing: 0;
  font-weight: 500;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gapfs Snapshots Eyebrow for small breakpoint renders scalable text state correctly Gap in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, 8vw);
  line-height: 1;
  letter-spacing: 0;
  font-weight: 500;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gapfs Snapshots Eyebrow for small breakpoint renders scalable text state correctly GapFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, 8vw);
  line-height: 1;
  letter-spacing: 0;
  font-weight: 500;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gapfs Snapshots Eyebrow for small breakpoint renders scalable text state correctly GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, 8vw);
  line-height: 1;
  letter-spacing: 0;
  font-weight: 500;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gapfs Snapshots Eyebrow for small breakpoint renders scalable text with a max width state correctly Gap 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(8vw, 88px));
  line-height: 1;
  letter-spacing: 0;
  font-weight: 500;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gapfs Snapshots Eyebrow for small breakpoint renders scalable text with a max width state correctly Gap in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(8vw, 88px));
  line-height: 1;
  letter-spacing: 0;
  font-weight: 500;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gapfs Snapshots Eyebrow for small breakpoint renders scalable text with a max width state correctly GapFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(8vw, 88px));
  line-height: 1;
  letter-spacing: 0;
  font-weight: 500;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gapfs Snapshots Eyebrow for small breakpoint renders scalable text with a max width state correctly GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(8vw, 88px));
  line-height: 1;
  letter-spacing: 0;
  font-weight: 500;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gapfs Snapshots Eyebrow for small breakpoint renders variant state correctly Gap 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 26px;
  line-height: 1;
  letter-spacing: 0;
  font-weight: 500;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gapfs Snapshots Eyebrow for small breakpoint renders variant state correctly Gap in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 26px;
  line-height: 1;
  letter-spacing: 0;
  font-weight: 500;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gapfs Snapshots Eyebrow for small breakpoint renders variant state correctly GapFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 26px;
  line-height: 1;
  letter-spacing: 0;
  font-weight: 500;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gapfs Snapshots Eyebrow for small breakpoint renders variant state correctly GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 26px;
  line-height: 1;
  letter-spacing: 0;
  font-weight: 500;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gapfs Snapshots Eyebrow for x-large breakpoint renders customStyles state correctly Gap 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: black;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 50px;
  line-height: 0.92;
  letter-spacing: 0;
  font-weight: 500;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gapfs Snapshots Eyebrow for x-large breakpoint renders customStyles state correctly Gap in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: black;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 50px;
  line-height: 0.92;
  letter-spacing: 0;
  font-weight: 500;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gapfs Snapshots Eyebrow for x-large breakpoint renders customStyles state correctly GapFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: black;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 50px;
  line-height: 0.92;
  letter-spacing: 0;
  font-weight: 500;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gapfs Snapshots Eyebrow for x-large breakpoint renders customStyles state correctly GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: black;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 50px;
  line-height: 0.92;
  letter-spacing: 0;
  font-weight: 500;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gapfs Snapshots Eyebrow for x-large breakpoint renders default state correctly Gap 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 50px;
  line-height: 0.92;
  letter-spacing: 0;
  font-weight: 500;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gapfs Snapshots Eyebrow for x-large breakpoint renders default state correctly Gap in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 50px;
  line-height: 0.92;
  letter-spacing: 0;
  font-weight: 500;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gapfs Snapshots Eyebrow for x-large breakpoint renders default state correctly GapFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 50px;
  line-height: 0.92;
  letter-spacing: 0;
  font-weight: 500;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gapfs Snapshots Eyebrow for x-large breakpoint renders default state correctly GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 50px;
  line-height: 0.92;
  letter-spacing: 0;
  font-weight: 500;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gapfs Snapshots Eyebrow for x-large breakpoint renders scalable text state correctly Gap 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(35px, 3.90625vw);
  line-height: 0.92;
  letter-spacing: 0;
  font-weight: 500;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gapfs Snapshots Eyebrow for x-large breakpoint renders scalable text state correctly Gap in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(35px, 3.90625vw);
  line-height: 0.92;
  letter-spacing: 0;
  font-weight: 500;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gapfs Snapshots Eyebrow for x-large breakpoint renders scalable text state correctly GapFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(35px, 3.90625vw);
  line-height: 0.92;
  letter-spacing: 0;
  font-weight: 500;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gapfs Snapshots Eyebrow for x-large breakpoint renders scalable text state correctly GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(35px, 3.90625vw);
  line-height: 0.92;
  letter-spacing: 0;
  font-weight: 500;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gapfs Snapshots Eyebrow for x-large breakpoint renders scalable text with a max width state correctly Gap 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(35px, min(3.90625vw, 42.96875px));
  line-height: 0.92;
  letter-spacing: 0;
  font-weight: 500;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gapfs Snapshots Eyebrow for x-large breakpoint renders scalable text with a max width state correctly Gap in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(35px, min(3.90625vw, 42.96875px));
  line-height: 0.92;
  letter-spacing: 0;
  font-weight: 500;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gapfs Snapshots Eyebrow for x-large breakpoint renders scalable text with a max width state correctly GapFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(35px, min(3.90625vw, 42.96875px));
  line-height: 0.92;
  letter-spacing: 0;
  font-weight: 500;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gapfs Snapshots Eyebrow for x-large breakpoint renders scalable text with a max width state correctly GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(35px, min(3.90625vw, 42.96875px));
  line-height: 0.92;
  letter-spacing: 0;
  font-weight: 500;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gapfs Snapshots Eyebrow for x-large breakpoint renders variant state correctly Gap 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 40px;
  line-height: 1;
  letter-spacing: 0;
  font-weight: 500;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gapfs Snapshots Eyebrow for x-large breakpoint renders variant state correctly Gap in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 40px;
  line-height: 1;
  letter-spacing: 0;
  font-weight: 500;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gapfs Snapshots Eyebrow for x-large breakpoint renders variant state correctly GapFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 40px;
  line-height: 1;
  letter-spacing: 0;
  font-weight: 500;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gapfs Snapshots Eyebrow for x-large breakpoint renders variant state correctly GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 40px;
  line-height: 1;
  letter-spacing: 0;
  font-weight: 500;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gapfs Snapshots Headline for small breakpoint renders customStyles state correctly Gap 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: black;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 60px;
  line-height: 1;
  letter-spacing: 0;
  font-weight: 500;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gapfs Snapshots Headline for small breakpoint renders customStyles state correctly Gap in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: black;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 60px;
  line-height: 1;
  letter-spacing: 0;
  font-weight: 500;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gapfs Snapshots Headline for small breakpoint renders customStyles state correctly GapFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: black;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 60px;
  line-height: 1;
  letter-spacing: 0;
  font-weight: 500;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gapfs Snapshots Headline for small breakpoint renders customStyles state correctly GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: black;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 60px;
  line-height: 1;
  letter-spacing: 0;
  font-weight: 500;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gapfs Snapshots Headline for small breakpoint renders default state correctly Gap 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 60px;
  line-height: 1;
  letter-spacing: 0;
  font-weight: 500;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gapfs Snapshots Headline for small breakpoint renders default state correctly Gap in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 60px;
  line-height: 1;
  letter-spacing: 0;
  font-weight: 500;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gapfs Snapshots Headline for small breakpoint renders default state correctly GapFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 60px;
  line-height: 1;
  letter-spacing: 0;
  font-weight: 500;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gapfs Snapshots Headline for small breakpoint renders default state correctly GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 60px;
  line-height: 1;
  letter-spacing: 0;
  font-weight: 500;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gapfs Snapshots Headline for small breakpoint renders scalable text state correctly Gap 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, 16vw);
  line-height: 1;
  letter-spacing: 0;
  font-weight: 500;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gapfs Snapshots Headline for small breakpoint renders scalable text state correctly Gap in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, 16vw);
  line-height: 1;
  letter-spacing: 0;
  font-weight: 500;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gapfs Snapshots Headline for small breakpoint renders scalable text state correctly GapFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, 16vw);
  line-height: 1;
  letter-spacing: 0;
  font-weight: 500;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gapfs Snapshots Headline for small breakpoint renders scalable text state correctly GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, 16vw);
  line-height: 1;
  letter-spacing: 0;
  font-weight: 500;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gapfs Snapshots Headline for small breakpoint renders scalable text with a max width state correctly Gap 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, min(16vw, 176px));
  line-height: 1;
  letter-spacing: 0;
  font-weight: 500;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gapfs Snapshots Headline for small breakpoint renders scalable text with a max width state correctly Gap in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, min(16vw, 176px));
  line-height: 1;
  letter-spacing: 0;
  font-weight: 500;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gapfs Snapshots Headline for small breakpoint renders scalable text with a max width state correctly GapFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, min(16vw, 176px));
  line-height: 1;
  letter-spacing: 0;
  font-weight: 500;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gapfs Snapshots Headline for small breakpoint renders scalable text with a max width state correctly GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, min(16vw, 176px));
  line-height: 1;
  letter-spacing: 0;
  font-weight: 500;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gapfs Snapshots Headline for small breakpoint renders variant state correctly Gap 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 55px;
  line-height: 1;
  letter-spacing: 0;
  font-weight: 500;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gapfs Snapshots Headline for small breakpoint renders variant state correctly Gap in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 55px;
  line-height: 1;
  letter-spacing: 0;
  font-weight: 500;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gapfs Snapshots Headline for small breakpoint renders variant state correctly GapFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 55px;
  line-height: 1;
  letter-spacing: 0;
  font-weight: 500;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gapfs Snapshots Headline for small breakpoint renders variant state correctly GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 55px;
  line-height: 1;
  letter-spacing: 0;
  font-weight: 500;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gapfs Snapshots Headline for x-large breakpoint renders customStyles state correctly Gap 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: black;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 120px;
  line-height: 0.8333333333333334;
  letter-spacing: 0;
  font-weight: 500;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gapfs Snapshots Headline for x-large breakpoint renders customStyles state correctly Gap in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: black;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 120px;
  line-height: 0.8333333333333334;
  letter-spacing: 0;
  font-weight: 500;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gapfs Snapshots Headline for x-large breakpoint renders customStyles state correctly GapFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: black;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 120px;
  line-height: 0.8333333333333334;
  letter-spacing: 0;
  font-weight: 500;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gapfs Snapshots Headline for x-large breakpoint renders customStyles state correctly GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: black;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 120px;
  line-height: 0.8333333333333334;
  letter-spacing: 0;
  font-weight: 500;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gapfs Snapshots Headline for x-large breakpoint renders default state correctly Gap 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 120px;
  line-height: 0.8333333333333334;
  letter-spacing: 0;
  font-weight: 500;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gapfs Snapshots Headline for x-large breakpoint renders default state correctly Gap in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 120px;
  line-height: 0.8333333333333334;
  letter-spacing: 0;
  font-weight: 500;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gapfs Snapshots Headline for x-large breakpoint renders default state correctly GapFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 120px;
  line-height: 0.8333333333333334;
  letter-spacing: 0;
  font-weight: 500;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gapfs Snapshots Headline for x-large breakpoint renders default state correctly GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 120px;
  line-height: 0.8333333333333334;
  letter-spacing: 0;
  font-weight: 500;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gapfs Snapshots Headline for x-large breakpoint renders scalable text state correctly Gap 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, 9.375vw);
  line-height: 0.8333333333333334;
  letter-spacing: 0;
  font-weight: 500;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gapfs Snapshots Headline for x-large breakpoint renders scalable text state correctly Gap in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, 9.375vw);
  line-height: 0.8333333333333334;
  letter-spacing: 0;
  font-weight: 500;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gapfs Snapshots Headline for x-large breakpoint renders scalable text state correctly GapFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, 9.375vw);
  line-height: 0.8333333333333334;
  letter-spacing: 0;
  font-weight: 500;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gapfs Snapshots Headline for x-large breakpoint renders scalable text state correctly GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, 9.375vw);
  line-height: 0.8333333333333334;
  letter-spacing: 0;
  font-weight: 500;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gapfs Snapshots Headline for x-large breakpoint renders scalable text with a max width state correctly Gap 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(9.375vw, 103.125px));
  line-height: 0.8333333333333334;
  letter-spacing: 0;
  font-weight: 500;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gapfs Snapshots Headline for x-large breakpoint renders scalable text with a max width state correctly Gap in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(9.375vw, 103.125px));
  line-height: 0.8333333333333334;
  letter-spacing: 0;
  font-weight: 500;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gapfs Snapshots Headline for x-large breakpoint renders scalable text with a max width state correctly GapFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(9.375vw, 103.125px));
  line-height: 0.8333333333333334;
  letter-spacing: 0;
  font-weight: 500;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gapfs Snapshots Headline for x-large breakpoint renders scalable text with a max width state correctly GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(9.375vw, 103.125px));
  line-height: 0.8333333333333334;
  letter-spacing: 0;
  font-weight: 500;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gapfs Snapshots Headline for x-large breakpoint renders variant state correctly Gap 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 110px;
  line-height: 0.8636363636363636;
  letter-spacing: 0;
  font-weight: 500;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gapfs Snapshots Headline for x-large breakpoint renders variant state correctly Gap in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 110px;
  line-height: 0.8636363636363636;
  letter-spacing: 0;
  font-weight: 500;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gapfs Snapshots Headline for x-large breakpoint renders variant state correctly GapFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 110px;
  line-height: 0.8636363636363636;
  letter-spacing: 0;
  font-weight: 500;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gapfs Snapshots Headline for x-large breakpoint renders variant state correctly GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 110px;
  line-height: 0.8636363636363636;
  letter-spacing: 0;
  font-weight: 500;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gapfs Snapshots New Variants for small breakpoint renders default state correctly Gap 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 11px;
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gapfs Snapshots New Variants for small breakpoint renders default state correctly Gap in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 11px;
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gapfs Snapshots New Variants for small breakpoint renders default state correctly GapFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 11px;
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gapfs Snapshots New Variants for small breakpoint renders default state correctly GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 11px;
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gapfs Snapshots New Variants for small breakpoint renders variant F0 state correctly Gap 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 11px;
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gapfs Snapshots New Variants for small breakpoint renders variant F0 state correctly Gap in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 11px;
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gapfs Snapshots New Variants for small breakpoint renders variant F0 state correctly GapFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 11px;
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gapfs Snapshots New Variants for small breakpoint renders variant F0 state correctly GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 11px;
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gapfs Snapshots New Variants for small breakpoint renders variant F1 state correctly Gap 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 11px;
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gapfs Snapshots New Variants for small breakpoint renders variant F1 state correctly Gap in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 11px;
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gapfs Snapshots New Variants for small breakpoint renders variant F1 state correctly GapFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 11px;
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gapfs Snapshots New Variants for small breakpoint renders variant F1 state correctly GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 11px;
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gapfs Snapshots New Variants for small breakpoint renders variant F2 state correctly Gap 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 11px;
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gapfs Snapshots New Variants for small breakpoint renders variant F2 state correctly Gap in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 11px;
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gapfs Snapshots New Variants for small breakpoint renders variant F2 state correctly GapFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 11px;
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gapfs Snapshots New Variants for small breakpoint renders variant F2 state correctly GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 11px;
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gapfs Snapshots New Variants for small breakpoint renders variant FN1 state correctly Gap 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 11px;
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gapfs Snapshots New Variants for small breakpoint renders variant FN1 state correctly Gap in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 11px;
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gapfs Snapshots New Variants for small breakpoint renders variant FN1 state correctly GapFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 11px;
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gapfs Snapshots New Variants for small breakpoint renders variant FN1 state correctly GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 11px;
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gapfs Snapshots New Variants for x-large breakpoint renders default state correctly Gap 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gapfs Snapshots New Variants for x-large breakpoint renders default state correctly Gap in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gapfs Snapshots New Variants for x-large breakpoint renders default state correctly GapFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gapfs Snapshots New Variants for x-large breakpoint renders default state correctly GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gapfs Snapshots New Variants for x-large breakpoint renders variant F0 state correctly Gap 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gapfs Snapshots New Variants for x-large breakpoint renders variant F0 state correctly Gap in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gapfs Snapshots New Variants for x-large breakpoint renders variant F0 state correctly GapFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gapfs Snapshots New Variants for x-large breakpoint renders variant F0 state correctly GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gapfs Snapshots New Variants for x-large breakpoint renders variant F1 state correctly Gap 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gapfs Snapshots New Variants for x-large breakpoint renders variant F1 state correctly Gap in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gapfs Snapshots New Variants for x-large breakpoint renders variant F1 state correctly GapFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gapfs Snapshots New Variants for x-large breakpoint renders variant F1 state correctly GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gapfs Snapshots New Variants for x-large breakpoint renders variant F2 state correctly Gap 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gapfs Snapshots New Variants for x-large breakpoint renders variant F2 state correctly Gap in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gapfs Snapshots New Variants for x-large breakpoint renders variant F2 state correctly GapFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gapfs Snapshots New Variants for x-large breakpoint renders variant F2 state correctly GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gapfs Snapshots New Variants for x-large breakpoint renders variant FN1 state correctly Gap 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gapfs Snapshots New Variants for x-large breakpoint renders variant FN1 state correctly Gap in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gapfs Snapshots New Variants for x-large breakpoint renders variant FN1 state correctly GapFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gapfs Snapshots New Variants for x-large breakpoint renders variant FN1 state correctly GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gapfs Snapshots Promo for small breakpoint renders customStyles state correctly Gap 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: black;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 60px;
  line-height: 1;
  letter-spacing: 0px;
  font-weight: 600;
  text-transform: uppercase;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gapfs Snapshots Promo for small breakpoint renders customStyles state correctly Gap in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: black;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 60px;
  line-height: 1;
  letter-spacing: 0px;
  font-weight: 600;
  text-transform: uppercase;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gapfs Snapshots Promo for small breakpoint renders customStyles state correctly GapFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: black;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 60px;
  line-height: 1;
  letter-spacing: 0px;
  font-weight: 600;
  text-transform: uppercase;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gapfs Snapshots Promo for small breakpoint renders customStyles state correctly GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: black;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 60px;
  line-height: 1;
  letter-spacing: 0px;
  font-weight: 600;
  text-transform: uppercase;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gapfs Snapshots Promo for small breakpoint renders default state correctly Gap 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 60px;
  line-height: 1;
  letter-spacing: 0px;
  font-weight: 600;
  text-transform: uppercase;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gapfs Snapshots Promo for small breakpoint renders default state correctly Gap in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 60px;
  line-height: 1;
  letter-spacing: 0px;
  font-weight: 600;
  text-transform: uppercase;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gapfs Snapshots Promo for small breakpoint renders default state correctly GapFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 60px;
  line-height: 1;
  letter-spacing: 0px;
  font-weight: 600;
  text-transform: uppercase;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gapfs Snapshots Promo for small breakpoint renders default state correctly GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 60px;
  line-height: 1;
  letter-spacing: 0px;
  font-weight: 600;
  text-transform: uppercase;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gapfs Snapshots Promo for small breakpoint renders scalable text state correctly Gap 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(13px, 16vw);
  line-height: 1;
  letter-spacing: 0vw;
  font-weight: 600;
  text-transform: uppercase;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gapfs Snapshots Promo for small breakpoint renders scalable text state correctly Gap in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(13px, 16vw);
  line-height: 1;
  letter-spacing: 0vw;
  font-weight: 600;
  text-transform: uppercase;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gapfs Snapshots Promo for small breakpoint renders scalable text state correctly GapFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(13px, 16vw);
  line-height: 1;
  letter-spacing: 0vw;
  font-weight: 600;
  text-transform: uppercase;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gapfs Snapshots Promo for small breakpoint renders scalable text state correctly GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(13px, 16vw);
  line-height: 1;
  letter-spacing: 0vw;
  font-weight: 600;
  text-transform: uppercase;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gapfs Snapshots Promo for small breakpoint renders scalable text with a max width state correctly Gap 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(13px, min(16vw, 176px));
  line-height: 1;
  letter-spacing: min(0vw, 0px);
  font-weight: 600;
  text-transform: uppercase;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gapfs Snapshots Promo for small breakpoint renders scalable text with a max width state correctly Gap in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(13px, min(16vw, 176px));
  line-height: 1;
  letter-spacing: min(0vw, 0px);
  font-weight: 600;
  text-transform: uppercase;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gapfs Snapshots Promo for small breakpoint renders scalable text with a max width state correctly GapFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(13px, min(16vw, 176px));
  line-height: 1;
  letter-spacing: min(0vw, 0px);
  font-weight: 600;
  text-transform: uppercase;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gapfs Snapshots Promo for small breakpoint renders scalable text with a max width state correctly GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(13px, min(16vw, 176px));
  line-height: 1;
  letter-spacing: min(0vw, 0px);
  font-weight: 600;
  text-transform: uppercase;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gapfs Snapshots Promo for small breakpoint renders variant state correctly Gap 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 40px;
  line-height: 1;
  letter-spacing: 0px;
  font-weight: 600;
  text-transform: uppercase;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gapfs Snapshots Promo for small breakpoint renders variant state correctly Gap in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 40px;
  line-height: 1;
  letter-spacing: 0px;
  font-weight: 600;
  text-transform: uppercase;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gapfs Snapshots Promo for small breakpoint renders variant state correctly GapFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 40px;
  line-height: 1;
  letter-spacing: 0px;
  font-weight: 600;
  text-transform: uppercase;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gapfs Snapshots Promo for small breakpoint renders variant state correctly GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 40px;
  line-height: 1;
  letter-spacing: 0px;
  font-weight: 600;
  text-transform: uppercase;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gapfs Snapshots Promo for x-large breakpoint renders customStyles state correctly Gap 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: black;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 100px;
  line-height: 1;
  letter-spacing: 0px;
  font-weight: 600;
  text-transform: uppercase;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gapfs Snapshots Promo for x-large breakpoint renders customStyles state correctly Gap in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: black;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 100px;
  line-height: 1;
  letter-spacing: 0px;
  font-weight: 600;
  text-transform: uppercase;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gapfs Snapshots Promo for x-large breakpoint renders customStyles state correctly GapFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: black;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 100px;
  line-height: 1;
  letter-spacing: 0px;
  font-weight: 600;
  text-transform: uppercase;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gapfs Snapshots Promo for x-large breakpoint renders customStyles state correctly GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: black;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 100px;
  line-height: 1;
  letter-spacing: 0px;
  font-weight: 600;
  text-transform: uppercase;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gapfs Snapshots Promo for x-large breakpoint renders default state correctly Gap 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 100px;
  line-height: 1;
  letter-spacing: 0px;
  font-weight: 600;
  text-transform: uppercase;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gapfs Snapshots Promo for x-large breakpoint renders default state correctly Gap in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 100px;
  line-height: 1;
  letter-spacing: 0px;
  font-weight: 600;
  text-transform: uppercase;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gapfs Snapshots Promo for x-large breakpoint renders default state correctly GapFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 100px;
  line-height: 1;
  letter-spacing: 0px;
  font-weight: 600;
  text-transform: uppercase;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gapfs Snapshots Promo for x-large breakpoint renders default state correctly GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 100px;
  line-height: 1;
  letter-spacing: 0px;
  font-weight: 600;
  text-transform: uppercase;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gapfs Snapshots Promo for x-large breakpoint renders scalable text state correctly Gap 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, 7.8125vw);
  line-height: 1;
  letter-spacing: 0vw;
  font-weight: 600;
  text-transform: uppercase;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gapfs Snapshots Promo for x-large breakpoint renders scalable text state correctly Gap in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, 7.8125vw);
  line-height: 1;
  letter-spacing: 0vw;
  font-weight: 600;
  text-transform: uppercase;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gapfs Snapshots Promo for x-large breakpoint renders scalable text state correctly GapFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, 7.8125vw);
  line-height: 1;
  letter-spacing: 0vw;
  font-weight: 600;
  text-transform: uppercase;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gapfs Snapshots Promo for x-large breakpoint renders scalable text state correctly GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, 7.8125vw);
  line-height: 1;
  letter-spacing: 0vw;
  font-weight: 600;
  text-transform: uppercase;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gapfs Snapshots Promo for x-large breakpoint renders scalable text with a max width state correctly Gap 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(7.8125vw, 85.9375px));
  line-height: 1;
  letter-spacing: min(0vw, 0px);
  font-weight: 600;
  text-transform: uppercase;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gapfs Snapshots Promo for x-large breakpoint renders scalable text with a max width state correctly Gap in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(7.8125vw, 85.9375px));
  line-height: 1;
  letter-spacing: min(0vw, 0px);
  font-weight: 600;
  text-transform: uppercase;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gapfs Snapshots Promo for x-large breakpoint renders scalable text with a max width state correctly GapFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(7.8125vw, 85.9375px));
  line-height: 1;
  letter-spacing: min(0vw, 0px);
  font-weight: 600;
  text-transform: uppercase;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gapfs Snapshots Promo for x-large breakpoint renders scalable text with a max width state correctly GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(7.8125vw, 85.9375px));
  line-height: 1;
  letter-spacing: min(0vw, 0px);
  font-weight: 600;
  text-transform: uppercase;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gapfs Snapshots Promo for x-large breakpoint renders variant state correctly Gap 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 80px;
  line-height: 1;
  letter-spacing: 0px;
  font-weight: 600;
  text-transform: uppercase;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gapfs Snapshots Promo for x-large breakpoint renders variant state correctly Gap in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 80px;
  line-height: 1;
  letter-spacing: 0px;
  font-weight: 600;
  text-transform: uppercase;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gapfs Snapshots Promo for x-large breakpoint renders variant state correctly GapFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 80px;
  line-height: 1;
  letter-spacing: 0px;
  font-weight: 600;
  text-transform: uppercase;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gapfs Snapshots Promo for x-large breakpoint renders variant state correctly GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 80px;
  line-height: 1;
  letter-spacing: 0px;
  font-weight: 600;
  text-transform: uppercase;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gapfs Snapshots Subhead for small breakpoint renders customStyles state correctly Gap 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: black;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 22px;
  line-height: 1.0909090909090908;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gapfs Snapshots Subhead for small breakpoint renders customStyles state correctly Gap in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: black;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 22px;
  line-height: 1.0909090909090908;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gapfs Snapshots Subhead for small breakpoint renders customStyles state correctly GapFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: black;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 22px;
  line-height: 1.0909090909090908;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gapfs Snapshots Subhead for small breakpoint renders customStyles state correctly GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: black;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 22px;
  line-height: 1.0909090909090908;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gapfs Snapshots Subhead for small breakpoint renders default state correctly Gap 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 22px;
  line-height: 1.0909090909090908;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gapfs Snapshots Subhead for small breakpoint renders default state correctly Gap in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 22px;
  line-height: 1.0909090909090908;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gapfs Snapshots Subhead for small breakpoint renders default state correctly GapFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 22px;
  line-height: 1.0909090909090908;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gapfs Snapshots Subhead for small breakpoint renders default state correctly GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 22px;
  line-height: 1.0909090909090908;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gapfs Snapshots Subhead for small breakpoint renders scalable text state correctly Gap 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, 5.866666666666666vw);
  line-height: 1.0909090909090908;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gapfs Snapshots Subhead for small breakpoint renders scalable text state correctly Gap in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, 5.866666666666666vw);
  line-height: 1.0909090909090908;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gapfs Snapshots Subhead for small breakpoint renders scalable text state correctly GapFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, 5.866666666666666vw);
  line-height: 1.0909090909090908;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gapfs Snapshots Subhead for small breakpoint renders scalable text state correctly GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, 5.866666666666666vw);
  line-height: 1.0909090909090908;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gapfs Snapshots Subhead for small breakpoint renders scalable text with a max width state correctly Gap 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, min(5.866666666666666vw, 64.53333333333333px));
  line-height: 1.0909090909090908;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gapfs Snapshots Subhead for small breakpoint renders scalable text with a max width state correctly Gap in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, min(5.866666666666666vw, 64.53333333333333px));
  line-height: 1.0909090909090908;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gapfs Snapshots Subhead for small breakpoint renders scalable text with a max width state correctly GapFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, min(5.866666666666666vw, 64.53333333333333px));
  line-height: 1.0909090909090908;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gapfs Snapshots Subhead for small breakpoint renders scalable text with a max width state correctly GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, min(5.866666666666666vw, 64.53333333333333px));
  line-height: 1.0909090909090908;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gapfs Snapshots Subhead for small breakpoint renders variant state correctly Gap 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 20px;
  line-height: 1.1;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gapfs Snapshots Subhead for small breakpoint renders variant state correctly Gap in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 20px;
  line-height: 1.1;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gapfs Snapshots Subhead for small breakpoint renders variant state correctly GapFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 20px;
  line-height: 1.1;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gapfs Snapshots Subhead for small breakpoint renders variant state correctly GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 20px;
  line-height: 1.1;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gapfs Snapshots Subhead for x-large breakpoint renders customStyles state correctly Gap 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: black;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 30px;
  line-height: 1;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gapfs Snapshots Subhead for x-large breakpoint renders customStyles state correctly Gap in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: black;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 30px;
  line-height: 1;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gapfs Snapshots Subhead for x-large breakpoint renders customStyles state correctly GapFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: black;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 30px;
  line-height: 1;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gapfs Snapshots Subhead for x-large breakpoint renders customStyles state correctly GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: black;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 30px;
  line-height: 1;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gapfs Snapshots Subhead for x-large breakpoint renders default state correctly Gap 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 30px;
  line-height: 1;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gapfs Snapshots Subhead for x-large breakpoint renders default state correctly Gap in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 30px;
  line-height: 1;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gapfs Snapshots Subhead for x-large breakpoint renders default state correctly GapFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 30px;
  line-height: 1;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gapfs Snapshots Subhead for x-large breakpoint renders default state correctly GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 30px;
  line-height: 1;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gapfs Snapshots Subhead for x-large breakpoint renders scalable text state correctly Gap 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, 2.34375vw);
  line-height: 1;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gapfs Snapshots Subhead for x-large breakpoint renders scalable text state correctly Gap in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, 2.34375vw);
  line-height: 1;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gapfs Snapshots Subhead for x-large breakpoint renders scalable text state correctly GapFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, 2.34375vw);
  line-height: 1;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gapfs Snapshots Subhead for x-large breakpoint renders scalable text state correctly GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, 2.34375vw);
  line-height: 1;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gapfs Snapshots Subhead for x-large breakpoint renders scalable text with a max width state correctly Gap 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(2.34375vw, 25.78125px));
  line-height: 1;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gapfs Snapshots Subhead for x-large breakpoint renders scalable text with a max width state correctly Gap in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(2.34375vw, 25.78125px));
  line-height: 1;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gapfs Snapshots Subhead for x-large breakpoint renders scalable text with a max width state correctly GapFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(2.34375vw, 25.78125px));
  line-height: 1;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gapfs Snapshots Subhead for x-large breakpoint renders scalable text with a max width state correctly GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(2.34375vw, 25.78125px));
  line-height: 1;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gapfs Snapshots Subhead for x-large breakpoint renders variant state correctly Gap 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 25px;
  line-height: 1;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gapfs Snapshots Subhead for x-large breakpoint renders variant state correctly Gap in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 25px;
  line-height: 1;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gapfs Snapshots Subhead for x-large breakpoint renders variant state correctly GapFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 25px;
  line-height: 1;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gapfs Snapshots Subhead for x-large breakpoint renders variant state correctly GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 25px;
  line-height: 1;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gapfs Snapshots desktopBreakpoint for large breakpoint renders set to 'x-large' should show mobile styles in views below XLARGE state correctly Gap 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, 9.375vw);
  line-height: 0.8333333333333334;
  letter-spacing: 0;
  font-weight: 500;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gapfs Snapshots desktopBreakpoint for large breakpoint renders set to 'x-large' should show mobile styles in views below XLARGE state correctly Gap in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, 9.375vw);
  line-height: 0.8333333333333334;
  letter-spacing: 0;
  font-weight: 500;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gapfs Snapshots desktopBreakpoint for large breakpoint renders set to 'x-large' should show mobile styles in views below XLARGE state correctly GapFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, 9.375vw);
  line-height: 0.8333333333333334;
  letter-spacing: 0;
  font-weight: 500;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gapfs Snapshots desktopBreakpoint for large breakpoint renders set to 'x-large' should show mobile styles in views below XLARGE state correctly GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, 9.375vw);
  line-height: 0.8333333333333334;
  letter-spacing: 0;
  font-weight: 500;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gapfs Snapshots desktopBreakpoint for x-large breakpoint renders set to 'x-large' should show mobile styles in views below XLARGE state correctly Gap 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, 9.375vw);
  line-height: 0.8333333333333334;
  letter-spacing: 0;
  font-weight: 500;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gapfs Snapshots desktopBreakpoint for x-large breakpoint renders set to 'x-large' should show mobile styles in views below XLARGE state correctly Gap in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, 9.375vw);
  line-height: 0.8333333333333334;
  letter-spacing: 0;
  font-weight: 500;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gapfs Snapshots desktopBreakpoint for x-large breakpoint renders set to 'x-large' should show mobile styles in views below XLARGE state correctly GapFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, 9.375vw);
  line-height: 0.8333333333333334;
  letter-spacing: 0;
  font-weight: 500;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`gap-redesign-2024 - Typography Variants 'Helpers' - gapfs Snapshots desktopBreakpoint for x-large breakpoint renders set to 'x-large' should show mobile styles in views below XLARGE state correctly GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, 9.375vw);
  line-height: 0.8333333333333334;
  letter-spacing: 0;
  font-weight: 500;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <span
        class="emotion-0"
      >
        Hello World!
      </span>
    </div>
  </div>
</DocumentFragment>
`;
